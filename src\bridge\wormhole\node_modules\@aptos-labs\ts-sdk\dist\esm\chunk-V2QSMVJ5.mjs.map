{"version": 3, "sources": ["../../src/api/utils.ts"], "sourcesContent": ["import { waitForIndexer } from \"../internal/transaction\";\nimport { ProcessorType } from \"../utils\";\nimport { AptosConfig } from \"./aptosConfig\";\nimport { AnyNumber } from \"../types\";\n\n/**\n * Waits for the indexer to reach a specified ledger version, allowing for synchronization with the blockchain.\n * This function is useful for ensuring that your application is working with the most up-to-date data before proceeding.\n *\n * @param args - The parameters for waiting on the indexer.\n * @param args.config - The configuration object for Aptos.\n * @param [args.minimumLedgerVersion] - The minimum ledger version to wait for. If not specified, the function will not wait.\n * @param args.processorType - The type of processor to wait for.\n *\n * @example\n * ```typescript\n * import { Aptos, AptosConfig, Network, ProcessorType } from \"@aptos-labs/ts-sdk\";\n *\n * const config = new AptosConfig({ network: Network.TESTNET });\n * const aptos = new Aptos(config);\n *\n * async function runExample() {\n *   // Wait for the indexer to reach a specific ledger version\n *   await aptos.waitForIndexerOnVersion({\n *     config: config,\n *     minimumLedgerVersion: 1000n, // replace with a real ledger version\n *     processorType: ProcessorType.DEFAULT,\n *   });\n *\n *   console.log(\"Indexer is synced to the specified ledger version.\");\n * }\n * runExample().catch(console.error);\n * ```\n * @group Implementation\n */\nexport async function waitForIndexerOnVersion(args: {\n  config: AptosConfig;\n  minimumLedgerVersion?: AnyNumber;\n  processorType: ProcessorType;\n}) {\n  if (args.minimumLedgerVersion !== undefined) {\n    await waitForIndexer({\n      aptosConfig: args.config,\n      minimumLedgerVersion: args.minimumLedgerVersion,\n      processorType: args.processorType,\n    });\n  }\n}\n"], "mappings": "yCAmCA,eAAsBA,EAAwBC,EAI3C,CACGA,EAAK,uBAAyB,QAChC,MAAMC,EAAe,CACnB,YAAaD,EAAK,OAClB,qBAAsBA,EAAK,qBAC3B,cAAeA,EAAK,aACtB,CAAC,CAEL", "names": ["waitForIndexerOnVersion", "args", "waitForIndexer"]}