"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcReferralApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../transformers/index.js");
const index_js_2 = require("../types/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcReferralApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_2.IndexerModule.Referral;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new indexer_proto_ts_1.InjectiveReferralRpc.InjectiveReferralRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchReferrerDetails(address) {
        const request = indexer_proto_ts_1.InjectiveReferralRpc.GetReferrerDetailsRequest.create();
        request.referrerAddress = address;
        try {
            const response = await this.retry(() => this.client.GetReferrerDetails(request, this.metadata));
            return index_js_1.IndexerGrpcReferralTransformer.referrerDetailsResponseToReferrerDetails(address, response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveReferralRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
    async fetchInviteeDetails(address) {
        const request = indexer_proto_ts_1.InjectiveReferralRpc.GetInviteeDetailsRequest.create();
        request.inviteeAddress = address;
        try {
            const response = await this.retry(() => this.client.GetInviteeDetails(request, this.metadata));
            return index_js_1.IndexerGrpcReferralTransformer.inviteeDetailsResponseToInviteeDetails(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveReferralRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
    async fetchReferrerByCode(code) {
        const request = indexer_proto_ts_1.InjectiveReferralRpc.GetReferrerByCodeRequest.create();
        request.referralCode = code;
        try {
            const response = await this.retry(() => this.client.GetReferrerByCode(request, this.metadata));
            return index_js_1.IndexerGrpcReferralTransformer.referrerByCodeResponseToReferrerByCode(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveReferralRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcReferralApi = IndexerGrpcReferralApi;
