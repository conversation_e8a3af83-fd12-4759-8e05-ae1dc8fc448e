"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryTradingStrategyContractUserStrategies = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryTradingStrategyContractUserStrategies extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = (0, index_js_1.toBase64)({
            user_strategy: {
                user: this.params.user,
            },
        });
        return payload;
    }
}
exports.QueryTradingStrategyContractUserStrategies = QueryTradingStrategyContractUserStrategies;
