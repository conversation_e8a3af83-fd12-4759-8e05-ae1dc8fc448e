"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgSwapExactOutput extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgSwapExactOutput(params);
    }
    toData() {
        const { params } = this;
        return {
            target_output_quantity: params.targetOutputQuantity,
            target_denom: params.targetDenom,
            ...(params.feeRecipient && {
                fee_recipient: params.feeRecipient,
            }),
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('swap_exact_output', this.toData());
    }
}
exports.default = ExecArgSwapExactOutput;
