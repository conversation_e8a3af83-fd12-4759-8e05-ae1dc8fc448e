var d=(e=>(e.JSON="application/json",e.BCS="application/x-bcs",e.BCS_SIGNED_TRANSACTION="application/x.aptos.signed_transaction+bcs",e.BCS_VIEW_FUNCTION="application/x.aptos.view_function+bcs",e))(d||{}),u=(t=>(t[t.Bool=0]="Bool",t[t.U8=1]="U8",t[t.U64=2]="U64",t[t.U128=3]="U128",t[t.Address=4]="Address",t[t.Signer=5]="Signer",t[t.Vector=6]="Vector",t[t.Struct=7]="Struct",t[t.U16=8]="U16",t[t.U32=9]="U32",t[t.U256=10]="U256",t[t.Reference=254]="Reference",t[t.Generic=255]="Generic",t))(u||{}),y=(r=>(r[r.U8=0]="U8",r[r.U64=1]="U64",r[r.U128=2]="U128",r[r.Address=3]="Address",r[r.U8Vector=4]="U8Vector",r[r.Bool=5]="Bool",r[r.U16=6]="U16",r[r.U32=7]="U32",r[r.U256=8]="U256",r[r.Serialized=9]="Serialized",r))(y||{}),c=(o=>(o[o.Script=0]="Script",o[o.EntryFunction=2]="EntryFunction",o[o.Multisig=3]="Multisig",o))(c||{}),l=(i=>(i[i.MultiAgentTransaction=0]="MultiAgentTransaction",i[i.FeePayerTransaction=1]="FeePayerTransaction",i))(l||{}),_=(n=>(n[n.Ed25519=0]="Ed25519",n[n.MultiEd25519=1]="MultiEd25519",n[n.MultiAgent=2]="MultiAgent",n[n.FeePayer=3]="FeePayer",n[n.SingleSender=4]="SingleSender",n))(_||{}),x=(p=>(p[p.Ed25519=0]="Ed25519",p[p.MultiEd25519=1]="MultiEd25519",p[p.SingleKey=2]="SingleKey",p[p.MultiKey=3]="MultiKey",p[p.NoAccountAuthenticator=4]="NoAccountAuthenticator",p[p.Abstraction=5]="Abstraction",p))(x||{}),h=(i=>(i.Ed25519="ed25519",i.Secp256k1="secp256k1",i))(h||{}),v=(e=>(e[e.Ed25519=0]="Ed25519",e[e.Secp256k1=1]="Secp256k1",e[e.Keyless=3]="Keyless",e[e.FederatedKeyless=4]="FederatedKeyless",e))(v||{}),R=(o=>(o[o.Ed25519=0]="Ed25519",o[o.Secp256k1=1]="Secp256k1",o[o.Keyless=3]="Keyless",o))(R||{}),m=(a=>(a[a.Ed25519=0]="Ed25519",a))(m||{}),M=(a=>(a[a.Ed25519=0]="Ed25519",a))(M||{}),b=(a=>(a[a.ZkProof=0]="ZkProof",a))(b||{}),k=(a=>(a[a.Groth16=0]="Groth16",a))(k||{}),S=(g=>(g.Pending="pending_transaction",g.User="user_transaction",g.Genesis="genesis_transaction",g.BlockMetadata="block_metadata_transaction",g.StateCheckpoint="state_checkpoint_transaction",g.Validator="validator_transaction",g.BlockEpilogue="block_epilogue_transaction",g))(S||{});function F(s){return s.type==="pending_transaction"}function I(s){return s.type==="user_transaction"}function B(s){return s.type==="genesis_transaction"}function G(s){return s.type==="block_metadata_transaction"}function O(s){return s.type==="state_checkpoint_transaction"}function P(s){return s.type==="validator_transaction"}function A(s){return s.type==="block_epilogue_transaction"}function N(s){return"signature"in s&&s.signature==="ed25519_signature"}function D(s){return"signature"in s&&s.signature==="secp256k1_ecdsa_signature"}function q(s){return s.type==="multi_agent_signature"}function H(s){return s.type==="fee_payer_signature"}function j(s){return s.type==="multi_ed25519_signature"}var f=(o=>(o.PRIVATE="private",o.PUBLIC="public",o.FRIEND="friend",o))(f||{}),C=(e=>(e.STORE="store",e.DROP="drop",e.KEY="key",e.COPY="copy",e))(C||{}),T=(i=>(i.VALIDATOR="validator",i.FULL_NODE="full_node",i))(T||{}),E=(e=>(e[e.Ed25519=0]="Ed25519",e[e.MultiEd25519=1]="MultiEd25519",e[e.SingleKey=2]="SingleKey",e[e.MultiKey=3]="MultiKey",e))(E||{}),U=(i=>(i[i.Ed25519=0]="Ed25519",i[i.Secp256k1Ecdsa=2]="Secp256k1Ecdsa",i))(U||{}),W=(n=>(n[n.DeriveAuid=251]="DeriveAuid",n[n.DeriveObjectAddressFromObject=252]="DeriveObjectAddressFromObject",n[n.DeriveObjectAddressFromGuid=253]="DeriveObjectAddressFromGuid",n[n.DeriveObjectAddressFromSeed=254]="DeriveObjectAddressFromSeed",n[n.DeriveResourceAccountAddress=255]="DeriveResourceAccountAddress",n))(W||{});export{d as a,u as b,y as c,c as d,l as e,_ as f,x as g,h,v as i,R as j,m as k,M as l,b as m,k as n,S as o,F as p,I as q,B as r,G as s,O as t,P as u,A as v,N as w,D as x,q as y,H as z,j as A,f as B,C,T as D,E,U as F,W as G};
//# sourceMappingURL=chunk-ODAAZLPK.mjs.map