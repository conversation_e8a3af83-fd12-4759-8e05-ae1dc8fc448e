import _m0 from "protobufjs/minimal.js";
import { Any } from "../../../../google/protobuf/any";
export declare const protobufPackage = "ibc.lightclients.solomachine.v3";
/**
 * ClientState defines a solo machine client that tracks the current consensus
 * state and if the client is frozen.
 */
export interface ClientState {
    /** latest sequence of the client state */
    sequence: string;
    /** frozen sequence of the solo machine */
    isFrozen: boolean;
    consensusState: ConsensusState | undefined;
}
/**
 * ConsensusState defines a solo machine consensus state. The sequence of a
 * consensus state is contained in the "height" key used in storing the
 * consensus state.
 */
export interface ConsensusState {
    /** public key of the solo machine */
    publicKey: Any | undefined;
    /**
     * diversifier allows the same public key to be re-used across different solo
     * machine clients (potentially on different chains) without being considered
     * misbehaviour.
     */
    diversifier: string;
    timestamp: string;
}
/** Header defines a solo machine consensus header */
export interface Header {
    timestamp: string;
    signature: Uint8Array;
    newPublicKey: Any | undefined;
    newDiversifier: string;
}
/**
 * Misbehaviour defines misbehaviour for a solo machine which consists
 * of a sequence and two signatures over different messages at that sequence.
 */
export interface Misbehaviour {
    sequence: string;
    signatureOne: SignatureAndData | undefined;
    signatureTwo: SignatureAndData | undefined;
}
/**
 * SignatureAndData contains a signature and the data signed over to create that
 * signature.
 */
export interface SignatureAndData {
    signature: Uint8Array;
    path: Uint8Array;
    data: Uint8Array;
    timestamp: string;
}
/**
 * TimestampedSignatureData contains the signature data and the timestamp of the
 * signature.
 */
export interface TimestampedSignatureData {
    signatureData: Uint8Array;
    timestamp: string;
}
/** SignBytes defines the signed bytes used for signature verification. */
export interface SignBytes {
    /** the sequence number */
    sequence: string;
    /** the proof timestamp */
    timestamp: string;
    /** the public key diversifier */
    diversifier: string;
    /** the standardised path bytes */
    path: Uint8Array;
    /** the marshaled data bytes */
    data: Uint8Array;
}
/** HeaderData returns the SignBytes data for update verification. */
export interface HeaderData {
    /** header public key */
    newPubKey: Any | undefined;
    /** header diversifier */
    newDiversifier: string;
}
export declare const ClientState: {
    encode(message: ClientState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ClientState;
    fromJSON(object: any): ClientState;
    toJSON(message: ClientState): unknown;
    create(base?: DeepPartial<ClientState>): ClientState;
    fromPartial(object: DeepPartial<ClientState>): ClientState;
};
export declare const ConsensusState: {
    encode(message: ConsensusState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConsensusState;
    fromJSON(object: any): ConsensusState;
    toJSON(message: ConsensusState): unknown;
    create(base?: DeepPartial<ConsensusState>): ConsensusState;
    fromPartial(object: DeepPartial<ConsensusState>): ConsensusState;
};
export declare const Header: {
    encode(message: Header, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Header;
    fromJSON(object: any): Header;
    toJSON(message: Header): unknown;
    create(base?: DeepPartial<Header>): Header;
    fromPartial(object: DeepPartial<Header>): Header;
};
export declare const Misbehaviour: {
    encode(message: Misbehaviour, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Misbehaviour;
    fromJSON(object: any): Misbehaviour;
    toJSON(message: Misbehaviour): unknown;
    create(base?: DeepPartial<Misbehaviour>): Misbehaviour;
    fromPartial(object: DeepPartial<Misbehaviour>): Misbehaviour;
};
export declare const SignatureAndData: {
    encode(message: SignatureAndData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignatureAndData;
    fromJSON(object: any): SignatureAndData;
    toJSON(message: SignatureAndData): unknown;
    create(base?: DeepPartial<SignatureAndData>): SignatureAndData;
    fromPartial(object: DeepPartial<SignatureAndData>): SignatureAndData;
};
export declare const TimestampedSignatureData: {
    encode(message: TimestampedSignatureData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TimestampedSignatureData;
    fromJSON(object: any): TimestampedSignatureData;
    toJSON(message: TimestampedSignatureData): unknown;
    create(base?: DeepPartial<TimestampedSignatureData>): TimestampedSignatureData;
    fromPartial(object: DeepPartial<TimestampedSignatureData>): TimestampedSignatureData;
};
export declare const SignBytes: {
    encode(message: SignBytes, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignBytes;
    fromJSON(object: any): SignBytes;
    toJSON(message: SignBytes): unknown;
    create(base?: DeepPartial<SignBytes>): SignBytes;
    fromPartial(object: DeepPartial<SignBytes>): SignBytes;
};
export declare const HeaderData: {
    encode(message: HeaderData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HeaderData;
    fromJSON(object: any): HeaderData;
    toJSON(message: HeaderData): unknown;
    create(base?: DeepPartial<HeaderData>): HeaderData;
    fromPartial(object: DeepPartial<HeaderData>): HeaderData;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
