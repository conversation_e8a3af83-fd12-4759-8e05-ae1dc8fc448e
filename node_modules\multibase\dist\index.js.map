{"version": 3, "sources": ["/Users/<USER>/code/pl/js-multibase/webpack/universalModuleDefinition", "/Users/<USER>/code/pl/js-multibase/webpack/bootstrap", "/Users/<USER>/code/pl/js-multibase/node_modules/node-libs-browser/node_modules/buffer/index.js", "/Users/<USER>/code/pl/js-multibase/src/index.js", "/Users/<USER>/code/pl/js-multibase/node_modules/webpack/buildin/global.js", "/Users/<USER>/code/pl/js-multibase/node_modules/base64-js/index.js", "/Users/<USER>/code/pl/js-multibase/node_modules/ieee754/index.js", "/Users/<USER>/code/pl/js-multibase/node_modules/isarray/index.js", "/Users/<USER>/code/pl/js-multibase/src/constants.js", "/Users/<USER>/code/pl/js-multibase/src/base.js", "/Users/<USER>/code/pl/js-multibase/node_modules/base-x/src/index.js", "/Users/<USER>/code/pl/js-multibase/node_modules/safe-buffer/index.js", "/Users/<USER>/code/pl/js-multibase/src/base16.js", "/Users/<USER>/code/pl/js-multibase/src/base32.js", "/Users/<USER>/code/pl/js-multibase/src/base64.js"], "names": ["<PERSON><PERSON><PERSON>", "require", "constants", "exports", "module", "multibase", "encode", "decode", "isEncoded", "names", "Object", "freeze", "keys", "codes", "errNotSupported", "Error", "nameOrCode", "buf", "base", "getBase", "codeBuf", "from", "code", "name", "validEncode", "concat", "bufOrString", "<PERSON><PERSON><PERSON><PERSON>", "toString", "substring", "length", "prototype", "call", "err", "isImplemented", "Base", "baseX", "base16", "base32", "base64", "reduce", "prev", "tupple", "constructor", "implementation", "alphabet", "engine", "string<PERSON><PERSON><PERSON><PERSON><PERSON>", "input", "char", "indexOf", "replace", "RegExp", "bits", "value", "index", "output", "Uint8Array", "i", "buffer", "byteLength", "view", "padding", "url", "pad"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACa;;AAEb,aAAa,mBAAO,CAAC,CAAW;;AAEhC,cAAc,mBAAO,CAAC,CAAS;;AAE/B,cAAc,mBAAO,CAAC,CAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA,mBAAmB;;AAEnB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,iBAAiB;AAC9B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B;;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,2DAA2D;;AAE3D;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gDAAgD,EAAE;AAClD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;;;AAGA;AACA;AACA,qCAAqC;;AAErC;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA,2BAA2B;;AAE3B;AACA;AACA;AACA,GAAG;;;AAGH;;AAEA;AACA,uBAAuB;AACvB,GAAG;AACH,4BAA4B;AAC5B,GAAG;;;AAGH;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH,qBAAqB;;AAErB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA,wBAAwB,QAAQ;AAChC;;AAEA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,GAAG;AACH;AACA;AACA,eAAe;AACf,GAAG;AACH;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,CAAC;AACD;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA,yDAAyD;AACzD,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,kBAAkB;AACnC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,0CAA0C,iBAAiB;;AAE3D;AACA,yDAAyD;;AAEzD;AACA;AACA;;AAEA;AACA,+DAA+D;;AAE/D;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;;;AAGH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA,EAAE;AACF;;;AAGA;;AAEA;AACA;AACA,uDAAuD;;AAEvD,gCAAgC;;AAEhC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B,qCAAqC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA,OAAO;;;AAGP;AACA;AACA;AACA;AACA,OAAO;;;AAGP;AACA,KAAK;AACL;AACA;AACA;;AAEA,yBAAyB;;AAEzB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB;AACrB,C;;;;;;;;;;;;;;;AC5zDA;;;;AAIA;;AAEA,MAAM;AAAEA;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AACA,MAAMC,SAAS,GAAGD,mBAAO,CAAC,CAAD,CAAzB;;AAEAE,OAAO,GAAGC,MAAM,CAACD,OAAP,GAAiBE,SAA3B;AACAF,OAAO,CAACG,MAAR,GAAiBA,MAAjB;AACAH,OAAO,CAACI,MAAR,GAAiBA,MAAjB;AACAJ,OAAO,CAACK,SAAR,GAAoBA,SAApB;AACAL,OAAO,CAACM,KAAR,GAAgBC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACE,IAAP,CAAYV,SAAS,CAACO,KAAtB,CAAd,CAAhB;AACAN,OAAO,CAACU,KAAR,GAAgBH,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACE,IAAP,CAAYV,SAAS,CAACW,KAAtB,CAAd,CAAhB;AAEA,MAAMC,eAAe,GAAG,IAAIC,KAAJ,CAAU,sBAAV,CAAxB;AAEA;;;;;;;;;AAQA,SAASV,SAAT,CAAoBW,UAApB,EAAgCC,GAAhC,EAAqC;AACnC,MAAI,CAACA,GAAL,EAAU;AACR,UAAM,IAAIF,KAAJ,CAAU,4BAAV,CAAN;AACD;;AACD,QAAMG,IAAI,GAAGC,OAAO,CAACH,UAAD,CAApB;AACA,QAAMI,OAAO,GAAGpB,MAAM,CAACqB,IAAP,CAAYH,IAAI,CAACI,IAAjB,CAAhB;AAEA,QAAMC,IAAI,GAAGL,IAAI,CAACK,IAAlB;AACAC,aAAW,CAACD,IAAD,EAAON,GAAP,CAAX;AACA,SAAOjB,MAAM,CAACyB,MAAP,CAAc,CAACL,OAAD,EAAUH,GAAV,CAAd,CAAP;AACD;AAED;;;;;;;;;;AAQA,SAASX,MAAT,CAAiBU,UAAjB,EAA6BC,GAA7B,EAAkC;AAChC,QAAMC,IAAI,GAAGC,OAAO,CAACH,UAAD,CAApB;AACA,QAAMO,IAAI,GAAGL,IAAI,CAACK,IAAlB;AAEA,SAAOlB,SAAS,CAACkB,IAAD,EAAOvB,MAAM,CAACqB,IAAP,CAAYH,IAAI,CAACZ,MAAL,CAAYW,GAAZ,CAAZ,CAAP,CAAhB;AACD;AAED;;;;;;;;;;;AASA,SAASV,MAAT,CAAiBmB,WAAjB,EAA8B;AAC5B,MAAI1B,MAAM,CAAC2B,QAAP,CAAgBD,WAAhB,CAAJ,EAAkC;AAChCA,eAAW,GAAGA,WAAW,CAACE,QAAZ,EAAd;AACD;;AAED,QAAMN,IAAI,GAAGI,WAAW,CAACG,SAAZ,CAAsB,CAAtB,EAAyB,CAAzB,CAAb;AACAH,aAAW,GAAGA,WAAW,CAACG,SAAZ,CAAsB,CAAtB,EAAyBH,WAAW,CAACI,MAArC,CAAd;;AAEA,MAAI,OAAOJ,WAAP,KAAuB,QAA3B,EAAqC;AACnCA,eAAW,GAAG1B,MAAM,CAACqB,IAAP,CAAYK,WAAZ,CAAd;AACD;;AAED,QAAMR,IAAI,GAAGC,OAAO,CAACG,IAAD,CAApB;AACA,SAAOtB,MAAM,CAACqB,IAAP,CAAYH,IAAI,CAACX,MAAL,CAAYmB,WAAW,CAACE,QAAZ,EAAZ,CAAZ,CAAP;AACD;AAED;;;;;;;;;AAOA,SAASpB,SAAT,CAAoBkB,WAApB,EAAiC;AAC/B,MAAI1B,MAAM,CAAC2B,QAAP,CAAgBD,WAAhB,CAAJ,EAAkC;AAChCA,eAAW,GAAGA,WAAW,CAACE,QAAZ,EAAd;AACD,GAH8B,CAK/B;;;AACA,MAAIlB,MAAM,CAACqB,SAAP,CAAiBH,QAAjB,CAA0BI,IAA1B,CAA+BN,WAA/B,MAAgD,iBAApD,EAAuE;AACrE,WAAO,KAAP;AACD;;AAED,QAAMJ,IAAI,GAAGI,WAAW,CAACG,SAAZ,CAAsB,CAAtB,EAAyB,CAAzB,CAAb;;AACA,MAAI;AACF,UAAMX,IAAI,GAAGC,OAAO,CAACG,IAAD,CAApB;AACA,WAAOJ,IAAI,CAACK,IAAZ;AACD,GAHD,CAGE,OAAOU,GAAP,EAAY;AACZ,WAAO,KAAP;AACD;AACF;AAED;;;;;;;;AAMA,SAAST,WAAT,CAAsBD,IAAtB,EAA4BN,GAA5B,EAAiC;AAC/B,QAAMC,IAAI,GAAGC,OAAO,CAACI,IAAD,CAApB;AACAL,MAAI,CAACX,MAAL,CAAYU,GAAG,CAACW,QAAJ,EAAZ;AACD;;AAED,SAAST,OAAT,CAAkBH,UAAlB,EAA8B;AAC5B,MAAIE,IAAJ;;AAEA,MAAIhB,SAAS,CAACO,KAAV,CAAgBO,UAAhB,CAAJ,EAAiC;AAC/BE,QAAI,GAAGhB,SAAS,CAACO,KAAV,CAAgBO,UAAhB,CAAP;AACD,GAFD,MAEO,IAAId,SAAS,CAACW,KAAV,CAAgBG,UAAhB,CAAJ,EAAiC;AACtCE,QAAI,GAAGhB,SAAS,CAACW,KAAV,CAAgBG,UAAhB,CAAP;AACD,GAFM,MAEA;AACL,UAAMF,eAAN;AACD;;AAED,MAAI,CAACI,IAAI,CAACgB,aAAL,EAAL,EAA2B;AACzB,UAAM,IAAInB,KAAJ,CAAU,UAAUC,UAAV,GAAuB,yBAAjC,CAAN;AACD;;AAED,SAAOE,IAAP;AACD,C;;;;;;;ACnIY;;AAEb,MAAM;;AAEN;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,4CAA4C;;;AAG5C,mB;;;;;;;ACnBa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC,SAAS;AAC3C;AACA;AACA,CAAC;AACD;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;;AAElB;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA,6BAA6B;AAC7B;;AAEA,0CAA0C,UAAU;AACpD;AACA,GAAG;;;AAGH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA,C;;;;;;;ACtHa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;;AAEA,QAAQ,UAAU;;AAElB;AACA,E;;;;;;;ACzFa;;AAEb,iBAAiB;;AAEjB;AACA;AACA,E;;;;;;;ACNA;;AAEA,MAAMiB,IAAI,GAAGlC,mBAAO,CAAC,CAAD,CAApB;;AACA,MAAMmC,KAAK,GAAGnC,mBAAO,CAAC,CAAD,CAArB;;AACA,MAAMoC,MAAM,GAAGpC,mBAAO,CAAC,EAAD,CAAtB;;AACA,MAAMqC,MAAM,GAAGrC,mBAAO,CAAC,EAAD,CAAtB;;AACA,MAAMsC,MAAM,GAAGtC,mBAAO,CAAC,EAAD,CAAtB,C,CAEA;;;AACA,MAAMC,SAAS,GAAG,CAChB,CAAC,OAAD,EAAU,GAAV,EAAe,EAAf,EAAmB,GAAnB,CADgB,EAEhB,CAAC,OAAD,EAAU,GAAV,EAAekC,KAAf,EAAsB,IAAtB,CAFgB,EAGhB,CAAC,OAAD,EAAU,GAAV,EAAeA,KAAf,EAAsB,UAAtB,CAHgB,EAIhB,CAAC,QAAD,EAAW,GAAX,EAAgBA,KAAhB,EAAuB,YAAvB,CAJgB,EAKhB,CAAC,QAAD,EAAW,GAAX,EAAgBC,MAAhB,EAAwB,kBAAxB,CALgB,EAMhB,CAAC,QAAD,EAAW,GAAX,EAAgBC,MAAhB,EAAwB,kCAAxB,CANgB,EAOhB,CAAC,WAAD,EAAc,GAAd,EAAmBA,MAAnB,EAA2B,mCAA3B,CAPgB,EAQhB,CAAC,WAAD,EAAc,GAAd,EAAmBA,MAAnB,EAA2B,kCAA3B,CARgB,EAShB,CAAC,cAAD,EAAiB,GAAjB,EAAsBA,MAAtB,EAA8B,mCAA9B,CATgB,EAUhB,CAAC,SAAD,EAAY,GAAZ,EAAiBA,MAAjB,EAAyB,kCAAzB,CAVgB,EAWhB,CAAC,cAAD,EAAiB,GAAjB,EAAsBF,KAAtB,EAA6B,4DAA7B,CAXgB,EAYhB,CAAC,WAAD,EAAc,GAAd,EAAmBA,KAAnB,EAA0B,4DAA1B,CAZgB,EAahB,CAAC,QAAD,EAAW,GAAX,EAAgBG,MAAhB,EAAwB,kEAAxB,CAbgB,EAchB,CAAC,WAAD,EAAc,GAAd,EAAmBA,MAAnB,EAA2B,mEAA3B,CAdgB,EAehB,CAAC,WAAD,EAAc,GAAd,EAAmBA,MAAnB,EAA2B,kEAA3B,CAfgB,EAgBhB,CAAC,cAAD,EAAiB,GAAjB,EAAsBA,MAAtB,EAA8B,mEAA9B,CAhBgB,CAAlB;AAmBA,MAAM9B,KAAK,GAAGP,SAAS,CAACsC,MAAV,CAAiB,CAACC,IAAD,EAAOC,MAAP,KAAkB;AAC/CD,MAAI,CAACC,MAAM,CAAC,CAAD,CAAP,CAAJ,GAAkB,IAAIP,IAAJ,CAASO,MAAM,CAAC,CAAD,CAAf,EAAoBA,MAAM,CAAC,CAAD,CAA1B,EAA+BA,MAAM,CAAC,CAAD,CAArC,EAA0CA,MAAM,CAAC,CAAD,CAAhD,CAAlB;AACA,SAAOD,IAAP;AACD,CAHa,EAGX,EAHW,CAAd;AAKA,MAAM5B,KAAK,GAAGX,SAAS,CAACsC,MAAV,CAAiB,CAACC,IAAD,EAAOC,MAAP,KAAkB;AAC/CD,MAAI,CAACC,MAAM,CAAC,CAAD,CAAP,CAAJ,GAAkBjC,KAAK,CAACiC,MAAM,CAAC,CAAD,CAAP,CAAvB;AACA,SAAOD,IAAP;AACD,CAHa,EAGX,EAHW,CAAd;AAKArC,MAAM,CAACD,OAAP,GAAiB;AACfM,OAAK,EAAEA,KADQ;AAEfI,OAAK,EAAEA;AAFQ,CAAjB,C;;;;;;;ACtCA;;AAEA,MAAMsB,IAAN,CAAW;AACTQ,aAAW,CAAEpB,IAAF,EAAQD,IAAR,EAAcsB,cAAd,EAA8BC,QAA9B,EAAwC;AACjD,SAAKtB,IAAL,GAAYA,IAAZ;AACA,SAAKD,IAAL,GAAYA,IAAZ;AACA,SAAKuB,QAAL,GAAgBA,QAAhB;;AACA,QAAID,cAAc,IAAIC,QAAtB,EAAgC;AAC9B,WAAKC,MAAL,GAAcF,cAAc,CAACC,QAAD,CAA5B;AACD;AACF;;AAEDvC,QAAM,CAAEyC,cAAF,EAAkB;AACtB,WAAO,KAAKD,MAAL,CAAYxC,MAAZ,CAAmByC,cAAnB,CAAP;AACD;;AAEDxC,QAAM,CAAEwC,cAAF,EAAkB;AACtB,WAAO,KAAKD,MAAL,CAAYvC,MAAZ,CAAmBwC,cAAnB,CAAP;AACD;;AAEDb,eAAa,GAAI;AACf,WAAO,KAAKY,MAAZ;AACD;;AApBQ;;AAuBX1C,MAAM,CAACD,OAAP,GAAiBgC,IAAjB,C;;;;;;;ACzBa;AACb;AACA;AACA;AACA;AACA;;AAEA,cAAc,mBAAO,CAAC,EAAa;;AAEnC;AACA;AACA;AACA;;AAEA;;AAEA,iBAAiB,qBAAqB;AACtC;AACA;;AAEA,iBAAiB,qBAAqB;AACtC;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,8CAA8C;;AAE9C,+CAA+C;;AAE/C;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL;AACA,mCAAmC;;AAEnC;AACA,iCAAiC;;AAEjC;;AAEA,8BAA8B,2CAA2C;AACzE;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA,KAAK;;;AAGL;;AAEA,UAAU,YAAY;AACtB;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,gBAAgB;;AAEhB;AACA;AACA,KAAK;;;AAGL;AACA;;AAEA;AACA;AACA;AACA,KAAK;;;AAGL,wDAAwD;;AAExD,oCAAoC;;AAEpC;AACA;AACA,mDAAmD;;AAEnD;AACA;AACA;;AAEA;;AAEA,8BAA8B,2CAA2C;AACzE;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,KAAK;;;AAGL;AACA;AACA,KAAK;;;AAGL;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,sB;;;;;;;ACnMa;;AAEb;AACA,aAAa,mBAAO,CAAC,CAAQ;;AAE7B,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uDAAuD;;AAEvD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,E;;;;;;;ACvEA;;AACA,MAAM;AAAEnC;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AAEAG,MAAM,CAACD,OAAP,GAAiB,SAASkC,MAAT,CAAiBQ,QAAjB,EAA2B;AAC1C,SAAO;AACLvC,UAAM,CAAE0C,KAAF,EAAS;AACb,UAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC7B,eAAOhD,MAAM,CAACqB,IAAP,CAAY2B,KAAZ,EAAmBpB,QAAnB,CAA4B,KAA5B,CAAP;AACD;;AACD,aAAOoB,KAAK,CAACpB,QAAN,CAAe,KAAf,CAAP;AACD,KANI;;AAOLrB,UAAM,CAAEyC,KAAF,EAAS;AACb,WAAK,MAAMC,IAAX,IAAmBD,KAAnB,EAA0B;AACxB,YAAIH,QAAQ,CAACK,OAAT,CAAiBD,IAAjB,IAAyB,CAA7B,EAAgC;AAC9B,gBAAM,IAAIlC,KAAJ,CAAU,0BAAV,CAAN;AACD;AACF;;AACD,aAAOf,MAAM,CAACqB,IAAP,CAAY2B,KAAZ,EAAmB,KAAnB,CAAP;AACD;;AAdI,GAAP;AAgBD,CAjBD,C;;;;;;;ACHA;;AAEA,SAASzC,MAAT,CAAiByC,KAAjB,EAAwBH,QAAxB,EAAkC;AAChCG,OAAK,GAAGA,KAAK,CAACG,OAAN,CAAc,IAAIC,MAAJ,CAAW,GAAX,EAAgB,GAAhB,CAAd,EAAoC,EAApC,CAAR;AACA,QAAMtB,MAAM,GAAGkB,KAAK,CAAClB,MAArB;AAEA,MAAIuB,IAAI,GAAG,CAAX;AACA,MAAIC,KAAK,GAAG,CAAZ;AAEA,MAAIC,KAAK,GAAG,CAAZ;AACA,QAAMC,MAAM,GAAG,IAAIC,UAAJ,CAAgB3B,MAAM,GAAG,CAAT,GAAa,CAAd,GAAmB,CAAlC,CAAf;;AAEA,OAAK,IAAI4B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5B,MAApB,EAA4B4B,CAAC,EAA7B,EAAiC;AAC/BJ,SAAK,GAAIA,KAAK,IAAI,CAAV,GAAeT,QAAQ,CAACK,OAAT,CAAiBF,KAAK,CAACU,CAAD,CAAtB,CAAvB;AACAL,QAAI,IAAI,CAAR;;AAEA,QAAIA,IAAI,IAAI,CAAZ,EAAe;AACbG,YAAM,CAACD,KAAK,EAAN,CAAN,GAAmBD,KAAK,KAAMD,IAAI,GAAG,CAAnB,GAAyB,GAA3C;AACAA,UAAI,IAAI,CAAR;AACD;AACF;;AAED,SAAOG,MAAM,CAACG,MAAd;AACD;;AAED,SAASrD,MAAT,CAAiBqD,MAAjB,EAAyBd,QAAzB,EAAmC;AACjC,QAAMf,MAAM,GAAG6B,MAAM,CAACC,UAAtB;AACA,QAAMC,IAAI,GAAG,IAAIJ,UAAJ,CAAeE,MAAf,CAAb;AACA,QAAMG,OAAO,GAAGjB,QAAQ,CAACK,OAAT,CAAiB,GAAjB,MAA0BL,QAAQ,CAACf,MAAT,GAAkB,CAA5D;;AAEA,MAAIgC,OAAJ,EAAa;AACXjB,YAAQ,GAAGA,QAAQ,CAAChB,SAAT,CAAmB,CAAnB,EAAsBgB,QAAQ,CAACf,MAAT,GAAkB,CAAxC,CAAX;AACD;;AAED,MAAIuB,IAAI,GAAG,CAAX;AACA,MAAIC,KAAK,GAAG,CAAZ;AACA,MAAIE,MAAM,GAAG,EAAb;;AAEA,OAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG5B,MAApB,EAA4B4B,CAAC,EAA7B,EAAiC;AAC/BJ,SAAK,GAAIA,KAAK,IAAI,CAAV,GAAeO,IAAI,CAACH,CAAD,CAA3B;AACAL,QAAI,IAAI,CAAR;;AAEA,WAAOA,IAAI,IAAI,CAAf,EAAkB;AAChBG,YAAM,IAAIX,QAAQ,CAAES,KAAK,KAAMD,IAAI,GAAG,CAAnB,GAAyB,EAA1B,CAAlB;AACAA,UAAI,IAAI,CAAR;AACD;AACF;;AAED,MAAIA,IAAI,GAAG,CAAX,EAAc;AACZG,UAAM,IAAIX,QAAQ,CAAES,KAAK,IAAK,IAAID,IAAf,GAAwB,EAAzB,CAAlB;AACD;;AAED,MAAIS,OAAJ,EAAa;AACX,WAAQN,MAAM,CAAC1B,MAAP,GAAgB,CAAjB,KAAwB,CAA/B,EAAkC;AAChC0B,YAAM,IAAI,GAAV;AACD;AACF;;AAED,SAAOA,MAAP;AACD;;AAEDpD,MAAM,CAACD,OAAP,GAAiB,SAASmC,MAAT,CAAiBO,QAAjB,EAA2B;AAC1C,SAAO;AACLvC,UAAM,CAAE0C,KAAF,EAAS;AACb,UAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC7B,eAAO1C,MAAM,CAACmD,UAAU,CAACpC,IAAX,CAAgB2B,KAAhB,CAAD,EAAyBH,QAAzB,CAAb;AACD;;AAED,aAAOvC,MAAM,CAAC0C,KAAD,EAAQH,QAAR,CAAb;AACD,KAPI;;AAQLtC,UAAM,CAAEyC,KAAF,EAAS;AACb,WAAK,MAAMC,IAAX,IAAmBD,KAAnB,EAA0B;AACxB,YAAIH,QAAQ,CAACK,OAAT,CAAiBD,IAAjB,IAAyB,CAA7B,EAAgC;AAC9B,gBAAM,IAAIlC,KAAJ,CAAU,0BAAV,CAAN;AACD;AACF;;AAED,aAAOR,MAAM,CAACyC,KAAD,EAAQH,QAAR,CAAb;AACD;;AAhBI,GAAP;AAkBD,CAnBD,C;;;;;;;AC7DA;;AACA,MAAM;AAAE7C;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AAEAG,MAAM,CAACD,OAAP,GAAiB,SAASoC,MAAT,CAAiBM,QAAjB,EAA2B;AAC1C;AACA;AACA;AACA;AACA;AACA,QAAMiB,OAAO,GAAGjB,QAAQ,CAACK,OAAT,CAAiB,GAAjB,IAAwB,CAAC,CAAzC;AACA,QAAMa,GAAG,GAAGlB,QAAQ,CAACK,OAAT,CAAiB,GAAjB,IAAwB,CAAC,CAAzB,IAA8BL,QAAQ,CAACK,OAAT,CAAiB,GAAjB,IAAwB,CAAC,CAAnE;AAEA,SAAO;AACL5C,UAAM,CAAE0C,KAAF,EAAS;AACb,UAAIQ,MAAM,GAAG,EAAb;;AAEA,UAAI,OAAOR,KAAP,KAAiB,QAArB,EAA+B;AAC7BQ,cAAM,GAAGxD,MAAM,CAACqB,IAAP,CAAY2B,KAAZ,EAAmBpB,QAAnB,CAA4B,QAA5B,CAAT;AACD,OAFD,MAEO;AACL4B,cAAM,GAAGR,KAAK,CAACpB,QAAN,CAAe,QAAf,CAAT;AACD;;AAED,UAAImC,GAAJ,EAAS;AACPP,cAAM,GAAGA,MAAM,CAACL,OAAP,CAAe,KAAf,EAAsB,GAAtB,EAA2BA,OAA3B,CAAmC,KAAnC,EAA0C,GAA1C,CAAT;AACD;;AAED,YAAMa,GAAG,GAAGR,MAAM,CAACN,OAAP,CAAe,GAAf,CAAZ;;AACA,UAAIc,GAAG,GAAG,CAAN,IAAW,CAACF,OAAhB,EAAyB;AACvBN,cAAM,GAAGA,MAAM,CAAC3B,SAAP,CAAiB,CAAjB,EAAoBmC,GAApB,CAAT;AACD;;AAED,aAAOR,MAAP;AACD,KApBI;;AAqBLjD,UAAM,CAAEyC,KAAF,EAAS;AACb,WAAK,MAAMC,IAAX,IAAmBD,KAAnB,EAA0B;AACxB,YAAIH,QAAQ,CAACK,OAAT,CAAiBD,IAAjB,IAAyB,CAA7B,EAAgC;AAC9B,gBAAM,IAAIlC,KAAJ,CAAU,0BAAV,CAAN;AACD;AACF;;AAED,aAAOf,MAAM,CAACqB,IAAP,CAAY2B,KAAZ,EAAmB,QAAnB,CAAP;AACD;;AA7BI,GAAP;AA+BD,CAxCD,C", "file": "index.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Multibase\"] = factory();\n\telse\n\t\troot[\"Multibase\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 1);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "/**\n * Implementation of the [multibase](https://github.com/multiformats/multibase) specification.\n * @module Multibase\n */\n'use strict'\n\nconst { Buffer } = require('buffer')\nconst constants = require('./constants')\n\nexports = module.exports = multibase\nexports.encode = encode\nexports.decode = decode\nexports.isEncoded = isEncoded\nexports.names = Object.freeze(Object.keys(constants.names))\nexports.codes = Object.freeze(Object.keys(constants.codes))\n\nconst errNotSupported = new Error('Unsupported encoding')\n\n/**\n * Create a new buffer with the multibase varint+code.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be prefixed with multibase.\n * @memberof Multibase\n * @returns {Buffer}\n */\nfunction multibase (nameOrCode, buf) {\n  if (!buf) {\n    throw new Error('requires an encoded buffer')\n  }\n  const base = getBase(nameOrCode)\n  const codeBuf = Buffer.from(base.code)\n\n  const name = base.name\n  validEncode(name, buf)\n  return Buffer.concat([codeBuf, buf])\n}\n\n/**\n * Encode data with the specified base and add the multibase prefix.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be encoded.\n * @returns {Buffer}\n * @memberof Multibase\n */\nfunction encode (nameOrCode, buf) {\n  const base = getBase(nameOrCode)\n  const name = base.name\n\n  return multibase(name, Buffer.from(base.encode(buf)))\n}\n\n/**\n * Takes a buffer or string encoded with multibase header, decodes it and\n * returns the decoded buffer\n *\n * @param {Buffer|string} bufOrString\n * @returns {Buffer}\n * @memberof Multibase\n *\n */\nfunction decode (bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString()\n  }\n\n  const code = bufOrString.substring(0, 1)\n  bufOrString = bufOrString.substring(1, bufOrString.length)\n\n  if (typeof bufOrString === 'string') {\n    bufOrString = Buffer.from(bufOrString)\n  }\n\n  const base = getBase(code)\n  return Buffer.from(base.decode(bufOrString.toString()))\n}\n\n/**\n * Is the given data multibase encoded?\n *\n * @param {Buffer|string} bufOrString\n * @returns {boolean}\n * @memberof Multibase\n */\nfunction isEncoded (bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString()\n  }\n\n  // Ensure bufOrString is a string\n  if (Object.prototype.toString.call(bufOrString) !== '[object String]') {\n    return false\n  }\n\n  const code = bufOrString.substring(0, 1)\n  try {\n    const base = getBase(code)\n    return base.name\n  } catch (err) {\n    return false\n  }\n}\n\n/**\n * @param {string} name\n * @param {Buffer} buf\n * @private\n * @returns {undefined}\n */\nfunction validEncode (name, buf) {\n  const base = getBase(name)\n  base.decode(buf.toString())\n}\n\nfunction getBase (nameOrCode) {\n  let base\n\n  if (constants.names[nameOrCode]) {\n    base = constants.names[nameOrCode]\n  } else if (constants.codes[nameOrCode]) {\n    base = constants.codes[nameOrCode]\n  } else {\n    throw errNotSupported\n  }\n\n  if (!base.isImplemented()) {\n    throw new Error('Base ' + nameOrCode + ' is not implemented yet')\n  }\n\n  return base\n}\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "'use strict'\n\nconst Base = require('./base.js')\nconst baseX = require('base-x')\nconst base16 = require('./base16')\nconst base32 = require('./base32')\nconst base64 = require('./base64')\n\n// name, code, implementation, alphabet\nconst constants = [\n  ['base1', '1', '', '1'],\n  ['base2', '0', baseX, '01'],\n  ['base8', '7', baseX, '01234567'],\n  ['base10', '9', baseX, '0123456789'],\n  ['base16', 'f', base16, '0123456789abcdef'],\n  ['base32', 'b', base32, 'abcdefghijklmnopqrstuvwxyz234567'],\n  ['base32pad', 'c', base32, 'abcdefghijklmnopqrstuvwxyz234567='],\n  ['base32hex', 'v', base32, '0123456789abcdefghijklmnopqrstuv'],\n  ['base32hexpad', 't', base32, '0123456789abcdefghijklmnopqrstuv='],\n  ['base32z', 'h', base32, 'ybndrfg8ejkmcpqxot1uwisza345h769'],\n  ['base58flickr', 'Z', baseX, '**********************************************************'],\n  ['base58btc', 'z', baseX, '**********************************************************'],\n  ['base64', 'm', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'],\n  ['base64pad', 'M', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='],\n  ['base64url', 'u', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'],\n  ['base64urlpad', 'U', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=']\n]\n\nconst names = constants.reduce((prev, tupple) => {\n  prev[tupple[0]] = new Base(tupple[0], tupple[1], tupple[2], tupple[3])\n  return prev\n}, {})\n\nconst codes = constants.reduce((prev, tupple) => {\n  prev[tupple[1]] = names[tupple[0]]\n  return prev\n}, {})\n\nmodule.exports = {\n  names: names,\n  codes: codes\n}\n", "'use strict'\n\nclass Base {\n  constructor (name, code, implementation, alphabet) {\n    this.name = name\n    this.code = code\n    this.alphabet = alphabet\n    if (implementation && alphabet) {\n      this.engine = implementation(alphabet)\n    }\n  }\n\n  encode (stringOrBuffer) {\n    return this.engine.encode(stringOrBuffer)\n  }\n\n  decode (stringOrBuffer) {\n    return this.engine.decode(stringOrBuffer)\n  }\n\n  isImplemented () {\n    return this.engine\n  }\n}\n\nmodule.exports = Base\n", "'use strict'; // base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\n\nvar _Buffer = require('safe-buffer').Buffer;\n\nfunction base(ALPHABET) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n\n  var BASE_MAP = new Uint8Array(256);\n\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n\n    BASE_MAP[xc] = i;\n  }\n\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256); // log(BASE) / log(256), rounded up\n\n  var iFACTOR = Math.log(256) / Math.log(BASE); // log(256) / log(BASE), rounded up\n\n  function encode(source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) {\n      source = _Buffer.from(source);\n    }\n\n    if (!_Buffer.isBuffer(source)) {\n      throw new TypeError('Expected Buffer');\n    }\n\n    if (source.length === 0) {\n      return '';\n    } // Skip & count leading zeroes.\n\n\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    } // Allocate enough space in big-endian base58 representation.\n\n\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size); // Process the bytes.\n\n    while (pbegin !== pend) {\n      var carry = source[pbegin]; // Apply \"b58 = b58 * 256 + ch\".\n\n      var i = 0;\n\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      pbegin++;\n    } // Skip leading zeroes in base58 result.\n\n\n    var it2 = size - length;\n\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    } // Translate the result into a string.\n\n\n    var str = LEADER.repeat(zeroes);\n\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n\n    return str;\n  }\n\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n\n    if (source.length === 0) {\n      return _Buffer.alloc(0);\n    }\n\n    var psz = 0; // Skip leading spaces.\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip and count leading '1's.\n\n\n    var zeroes = 0;\n    var length = 0;\n\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    } // Allocate enough space in big-endian base256 representation.\n\n\n    var size = (source.length - psz) * FACTOR + 1 >>> 0; // log(58) / log(256), rounded up.\n\n    var b256 = new Uint8Array(size); // Process the characters.\n\n    while (source[psz]) {\n      // Decode character\n      var carry = BASE_MAP[source.charCodeAt(psz)]; // Invalid character\n\n      if (carry === 255) {\n        return;\n      }\n\n      var i = 0;\n\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      psz++;\n    } // Skip trailing spaces.\n\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip leading zeroes in b256.\n\n\n    var it4 = size - length;\n\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4));\n\n    vch.fill(0x00, 0, zeroes);\n    var j = zeroes;\n\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n\n    return vch;\n  }\n\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n\n    if (buffer) {\n      return buffer;\n    }\n\n    throw new Error('Non-base' + BASE + ' character');\n  }\n\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\n\nmodule.exports = base;", "\"use strict\";\n\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\n\nvar Buffer = buffer.Buffer; // alternative to using Object.keys for old browsers\n\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\n\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\n\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype); // Copy static methods from Buffer\n\ncopyProps(Buffer, SafeBuffer);\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n\n  return Buffer(arg, encodingOrOffset, length);\n};\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  var buf = Buffer(size);\n\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n\n  return buf;\n};\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return Buffer(size);\n};\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return buffer.SlowBuffer(size);\n};", "'use strict'\nconst { <PERSON><PERSON><PERSON> } = require('buffer')\n\nmodule.exports = function base16 (alphabet) {\n  return {\n    encode (input) {\n      if (typeof input === 'string') {\n        return Buffer.from(input).toString('hex')\n      }\n      return input.toString('hex')\n    },\n    decode (input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base16 character')\n        }\n      }\n      return Buffer.from(input, 'hex')\n    }\n  }\n}\n", "'use strict'\n\nfunction decode (input, alphabet) {\n  input = input.replace(new RegExp('=', 'g'), '')\n  const length = input.length\n\n  let bits = 0\n  let value = 0\n\n  let index = 0\n  const output = new Uint8Array((length * 5 / 8) | 0)\n\n  for (let i = 0; i < length; i++) {\n    value = (value << 5) | alphabet.indexOf(input[i])\n    bits += 5\n\n    if (bits >= 8) {\n      output[index++] = (value >>> (bits - 8)) & 255\n      bits -= 8\n    }\n  }\n\n  return output.buffer\n}\n\nfunction encode (buffer, alphabet) {\n  const length = buffer.byteLength\n  const view = new Uint8Array(buffer)\n  const padding = alphabet.indexOf('=') === alphabet.length - 1\n\n  if (padding) {\n    alphabet = alphabet.substring(0, alphabet.length - 1)\n  }\n\n  let bits = 0\n  let value = 0\n  let output = ''\n\n  for (let i = 0; i < length; i++) {\n    value = (value << 8) | view[i]\n    bits += 8\n\n    while (bits >= 5) {\n      output += alphabet[(value >>> (bits - 5)) & 31]\n      bits -= 5\n    }\n  }\n\n  if (bits > 0) {\n    output += alphabet[(value << (5 - bits)) & 31]\n  }\n\n  if (padding) {\n    while ((output.length % 8) !== 0) {\n      output += '='\n    }\n  }\n\n  return output\n}\n\nmodule.exports = function base32 (alphabet) {\n  return {\n    encode (input) {\n      if (typeof input === 'string') {\n        return encode(Uint8Array.from(input), alphabet)\n      }\n\n      return encode(input, alphabet)\n    },\n    decode (input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base32 character')\n        }\n      }\n\n      return decode(input, alphabet)\n    }\n  }\n}\n", "'use strict'\nconst { <PERSON><PERSON><PERSON> } = require('buffer')\n\nmodule.exports = function base64 (alphabet) {\n  // The alphabet is only used to know:\n  //   1. If padding is enabled (must contain '=')\n  //   2. If the output must be url-safe (must contain '-' and '_')\n  //   3. If the input of the output function is valid\n  // The alphabets from RFC 4648 are always used.\n  const padding = alphabet.indexOf('=') > -1\n  const url = alphabet.indexOf('-') > -1 && alphabet.indexOf('_') > -1\n\n  return {\n    encode (input) {\n      let output = ''\n\n      if (typeof input === 'string') {\n        output = Buffer.from(input).toString('base64')\n      } else {\n        output = input.toString('base64')\n      }\n\n      if (url) {\n        output = output.replace(/\\+/g, '-').replace(/\\//g, '_')\n      }\n\n      const pad = output.indexOf('=')\n      if (pad > 0 && !padding) {\n        output = output.substring(0, pad)\n      }\n\n      return output\n    },\n    decode (input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base64 character')\n        }\n      }\n\n      return Buffer.from(input, 'base64')\n    }\n  }\n}\n"], "sourceRoot": ""}