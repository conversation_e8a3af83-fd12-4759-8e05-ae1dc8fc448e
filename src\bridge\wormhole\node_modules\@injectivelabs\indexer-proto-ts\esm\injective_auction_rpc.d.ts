import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
export declare const protobufPackage = "injective_auction_rpc";
export interface AuctionEndpointRequest {
    /** The auction round number. -1 for latest. */
    round: string;
}
export interface AuctionEndpointResponse {
    /** The auction */
    auction: Auction | undefined;
    /** Bids of the auction */
    bids: Bid[];
}
export interface Auction {
    /** Account address of the auction winner */
    winner: string;
    /** Coins in the basket */
    basket: Coin[];
    winningBidAmount: string;
    round: string;
    /** Auction end timestamp in UNIX millis. */
    endTimestamp: string;
    /** UpdatedAt timestamp in UNIX millis. */
    updatedAt: string;
}
export interface Coin {
    /** Denom of the coin */
    denom: string;
    amount: string;
    usdValue: string;
}
export interface Bid {
    /** Account address of the bidder */
    bidder: string;
    amount: string;
    /** Bid timestamp in UNIX millis. */
    timestamp: string;
}
export interface AuctionsRequest {
}
export interface AuctionsResponse {
    /** The historical auctions */
    auctions: Auction[];
}
export interface StreamBidsRequest {
}
export interface StreamBidsResponse {
    /** Account address of the bidder */
    bidder: string;
    bidAmount: string;
    round: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export interface InjBurntEndpointRequest {
}
export interface InjBurntEndpointResponse {
    totalInjBurnt: string;
}
export declare const AuctionEndpointRequest: {
    encode(message: AuctionEndpointRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuctionEndpointRequest;
    fromJSON(object: any): AuctionEndpointRequest;
    toJSON(message: AuctionEndpointRequest): unknown;
    create(base?: DeepPartial<AuctionEndpointRequest>): AuctionEndpointRequest;
    fromPartial(object: DeepPartial<AuctionEndpointRequest>): AuctionEndpointRequest;
};
export declare const AuctionEndpointResponse: {
    encode(message: AuctionEndpointResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuctionEndpointResponse;
    fromJSON(object: any): AuctionEndpointResponse;
    toJSON(message: AuctionEndpointResponse): unknown;
    create(base?: DeepPartial<AuctionEndpointResponse>): AuctionEndpointResponse;
    fromPartial(object: DeepPartial<AuctionEndpointResponse>): AuctionEndpointResponse;
};
export declare const Auction: {
    encode(message: Auction, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Auction;
    fromJSON(object: any): Auction;
    toJSON(message: Auction): unknown;
    create(base?: DeepPartial<Auction>): Auction;
    fromPartial(object: DeepPartial<Auction>): Auction;
};
export declare const Coin: {
    encode(message: Coin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Coin;
    fromJSON(object: any): Coin;
    toJSON(message: Coin): unknown;
    create(base?: DeepPartial<Coin>): Coin;
    fromPartial(object: DeepPartial<Coin>): Coin;
};
export declare const Bid: {
    encode(message: Bid, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Bid;
    fromJSON(object: any): Bid;
    toJSON(message: Bid): unknown;
    create(base?: DeepPartial<Bid>): Bid;
    fromPartial(object: DeepPartial<Bid>): Bid;
};
export declare const AuctionsRequest: {
    encode(_: AuctionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuctionsRequest;
    fromJSON(_: any): AuctionsRequest;
    toJSON(_: AuctionsRequest): unknown;
    create(base?: DeepPartial<AuctionsRequest>): AuctionsRequest;
    fromPartial(_: DeepPartial<AuctionsRequest>): AuctionsRequest;
};
export declare const AuctionsResponse: {
    encode(message: AuctionsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuctionsResponse;
    fromJSON(object: any): AuctionsResponse;
    toJSON(message: AuctionsResponse): unknown;
    create(base?: DeepPartial<AuctionsResponse>): AuctionsResponse;
    fromPartial(object: DeepPartial<AuctionsResponse>): AuctionsResponse;
};
export declare const StreamBidsRequest: {
    encode(_: StreamBidsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamBidsRequest;
    fromJSON(_: any): StreamBidsRequest;
    toJSON(_: StreamBidsRequest): unknown;
    create(base?: DeepPartial<StreamBidsRequest>): StreamBidsRequest;
    fromPartial(_: DeepPartial<StreamBidsRequest>): StreamBidsRequest;
};
export declare const StreamBidsResponse: {
    encode(message: StreamBidsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamBidsResponse;
    fromJSON(object: any): StreamBidsResponse;
    toJSON(message: StreamBidsResponse): unknown;
    create(base?: DeepPartial<StreamBidsResponse>): StreamBidsResponse;
    fromPartial(object: DeepPartial<StreamBidsResponse>): StreamBidsResponse;
};
export declare const InjBurntEndpointRequest: {
    encode(_: InjBurntEndpointRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InjBurntEndpointRequest;
    fromJSON(_: any): InjBurntEndpointRequest;
    toJSON(_: InjBurntEndpointRequest): unknown;
    create(base?: DeepPartial<InjBurntEndpointRequest>): InjBurntEndpointRequest;
    fromPartial(_: DeepPartial<InjBurntEndpointRequest>): InjBurntEndpointRequest;
};
export declare const InjBurntEndpointResponse: {
    encode(message: InjBurntEndpointResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InjBurntEndpointResponse;
    fromJSON(object: any): InjBurntEndpointResponse;
    toJSON(message: InjBurntEndpointResponse): unknown;
    create(base?: DeepPartial<InjBurntEndpointResponse>): InjBurntEndpointResponse;
    fromPartial(object: DeepPartial<InjBurntEndpointResponse>): InjBurntEndpointResponse;
};
/** InjectiveAuctionRPC defines gRPC API of the Auction API. */
export interface InjectiveAuctionRPC {
    /** Provide historical auction info for a given auction */
    AuctionEndpoint(request: DeepPartial<AuctionEndpointRequest>, metadata?: grpc.Metadata): Promise<AuctionEndpointResponse>;
    /** Provide the historical auctions info */
    Auctions(request: DeepPartial<AuctionsRequest>, metadata?: grpc.Metadata): Promise<AuctionsResponse>;
    /** StreamBids streams new bids of an auction. */
    StreamBids(request: DeepPartial<StreamBidsRequest>, metadata?: grpc.Metadata): Observable<StreamBidsResponse>;
    /** InjBurntEndpoint returns the total amount of INJ burnt in auctions. */
    InjBurntEndpoint(request: DeepPartial<InjBurntEndpointRequest>, metadata?: grpc.Metadata): Promise<InjBurntEndpointResponse>;
}
export declare class InjectiveAuctionRPCClientImpl implements InjectiveAuctionRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    AuctionEndpoint(request: DeepPartial<AuctionEndpointRequest>, metadata?: grpc.Metadata): Promise<AuctionEndpointResponse>;
    Auctions(request: DeepPartial<AuctionsRequest>, metadata?: grpc.Metadata): Promise<AuctionsResponse>;
    StreamBids(request: DeepPartial<StreamBidsRequest>, metadata?: grpc.Metadata): Observable<StreamBidsResponse>;
    InjBurntEndpoint(request: DeepPartial<InjBurntEndpointRequest>, metadata?: grpc.Metadata): Promise<InjBurntEndpointResponse>;
}
export declare const InjectiveAuctionRPCDesc: {
    serviceName: string;
};
export declare const InjectiveAuctionRPCAuctionEndpointDesc: UnaryMethodDefinitionish;
export declare const InjectiveAuctionRPCAuctionsDesc: UnaryMethodDefinitionish;
export declare const InjectiveAuctionRPCStreamBidsDesc: UnaryMethodDefinitionish;
export declare const InjectiveAuctionRPCInjBurntEndpointDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
