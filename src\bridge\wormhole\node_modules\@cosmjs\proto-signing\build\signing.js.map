{"version": 3, "file": "signing.js", "sourceRoot": "", "sources": ["../src/signing.ts"], "names": [], "mappings": ";;;AAAA,yDAAyD;AACzD,yCAAuC;AAEvC,4EAA0E;AAC1E,0DAAkF;AAGlF;;;;GAIG;AACH,SAAS,eAAe,CACtB,OAAoF,EACpF,QAAkB;IAElB,OAAO,OAAO,CAAC,GAAG,CAChB,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAc,EAAE,CAAC,CAAC;QACrC,SAAS,EAAE,MAAM;QACjB,QAAQ,EAAE;YACR,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;SAC3B;QACD,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;KAC3B,CAAC,CACH,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAC/B,OAAoF,EACpF,SAA0B,EAC1B,QAAgB,EAChB,UAA8B,EAC9B,QAA4B,EAC5B,QAAQ,GAAG,kBAAQ,CAAC,gBAAgB;IAEpC,qGAAqG;IACrG,IAAA,cAAM,EACJ,UAAU,KAAK,SAAS,IAAI,OAAO,UAAU,KAAK,QAAQ,EAC1D,wCAAwC,CACzC,CAAC;IACF,IAAA,cAAM,EAAC,QAAQ,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,sCAAsC,CAAC,CAAC;IAEvG,MAAM,QAAQ,GAAG,aAAQ,CAAC,WAAW,CAAC;QACpC,WAAW,EAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;QAC/C,GAAG,EAAE;YACH,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC;YACtB,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;YAC1B,OAAO,EAAE,UAAU;YACnB,KAAK,EAAE,QAAQ;SAChB;KACF,CAAC,CAAC;IACH,OAAO,aAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;AAC5C,CAAC;AAzBD,8CAyBC;AAED,SAAgB,WAAW,CACzB,SAAqB,EACrB,aAAyB,EACzB,OAAe,EACf,aAAqB;IAErB,OAAO;QACL,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,aAAa;QAC5B,OAAO,EAAE,OAAO;QAChB,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC;KACrC,CAAC;AACJ,CAAC;AAZD,kCAYC;AAED,SAAgB,aAAa,CAAC,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAW;IACzF,MAAM,OAAO,GAAG,YAAO,CAAC,WAAW,CAAC;QAClC,aAAa,EAAE,aAAa;QAC5B,aAAa,EAAE,aAAa;QAC5B,SAAS,EAAE,SAAS;QACpB,OAAO,EAAE,OAAO;KACjB,CAAC,CAAC;IACH,OAAO,YAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;AAC1C,CAAC;AARD,sCAQC"}