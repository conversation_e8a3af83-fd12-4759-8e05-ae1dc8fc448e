"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcDistributionApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Chain Grpc API
 */
class ChainGrpcDistributionApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_1.ChainModule.Distribution;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.Params(request, this.metadata));
            return index_js_2.ChainGrpcDistributionTransformer.moduleParamsResponseToModuleParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosDistributionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Params',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Params',
                contextModule: this.module,
            });
        }
    }
    async fetchDelegatorRewardsForValidator({ delegatorAddress, validatorAddress, }) {
        const request = core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryDelegationRewardsRequest.create();
        request.validatorAddress = validatorAddress;
        request.delegatorAddress = delegatorAddress;
        try {
            const response = await this.retry(() => this.client.DelegationRewards(request, this.metadata));
            return index_js_2.ChainGrpcDistributionTransformer.delegationRewardResponseToReward(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosDistributionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DelegationRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DelegationRewards',
                contextModule: this.module,
            });
        }
    }
    async fetchDelegatorRewardsForValidatorNoThrow({ delegatorAddress, validatorAddress, }) {
        const request = core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryDelegationRewardsRequest.create();
        request.validatorAddress = validatorAddress;
        request.delegatorAddress = delegatorAddress;
        try {
            const response = await this.retry(() => this.client.DelegationRewards(request, this.metadata));
            return index_js_2.ChainGrpcDistributionTransformer.delegationRewardResponseToReward(response);
        }
        catch (e) {
            if (e.message.includes('does not exist') ||
                e.message.includes('no delegation for (address, validator) tuple')) {
                return [];
            }
            if (e instanceof core_proto_ts_1.CosmosDistributionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DelegationRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DelegationRewards',
                contextModule: this.module,
            });
        }
    }
    async fetchDelegatorRewards(injectiveAddress) {
        const request = core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryDelegationTotalRewardsRequest.create();
        request.delegatorAddress = injectiveAddress;
        try {
            const response = await this.retry(() => this.client.DelegationTotalRewards(request, this.metadata));
            return index_js_2.ChainGrpcDistributionTransformer.totalDelegationRewardResponseToTotalReward(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosDistributionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DelegationTotalRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DelegationTotalRewards',
                contextModule: this.module,
            });
        }
    }
    async fetchDelegatorRewardsNoThrow(injectiveAddress) {
        const request = core_proto_ts_1.CosmosDistributionV1Beta1Query.QueryDelegationTotalRewardsRequest.create();
        request.delegatorAddress = injectiveAddress;
        try {
            const response = await this.retry(() => this.client.DelegationTotalRewards(request, this.metadata));
            return index_js_2.ChainGrpcDistributionTransformer.totalDelegationRewardResponseToTotalReward(response);
        }
        catch (e) {
            if (e.message.includes('does not exist')) {
                return [];
            }
            if (e instanceof core_proto_ts_1.CosmosDistributionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DelegationTotalRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DelegationTotalRewards',
                contextModule: this.module,
            });
        }
    }
}
exports.ChainGrpcDistributionApi = ChainGrpcDistributionApi;
