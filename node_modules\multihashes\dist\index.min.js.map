{"version": 3, "sources": ["file:webpack/universalModuleDefinition", "file:webpack/bootstrap", "file:/Users/<USER>/code/pl/js-multihash/node_modules/node-libs-browser/node_modules/buffer/index.js", "file:/Users/<USER>/code/pl/js-multihash/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/webpack/buildin/global.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/base64-js/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/ieee754/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/isarray/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/constants.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/base-x/src/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/safe-buffer/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base16.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base32.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/multibase/src/base64.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/index.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/encode.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/decode.js", "file:/Users/<USER>/code/pl/js-multihash/node_modules/varint/length.js", "file:/Users/<USER>/code/pl/js-multihash/src/constants.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "base64", "ieee754", "isArray", "kMaxLength", "<PERSON><PERSON><PERSON>", "TYPED_ARRAY_SUPPORT", "createBuffer", "that", "length", "RangeError", "Uint8Array", "__proto__", "arg", "encodingOrOffset", "this", "Error", "allocUnsafe", "from", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "byteOffset", "byteLength", "undefined", "fromArrayLike", "fromArrayBuffer", "string", "encoding", "isEncoding", "actual", "write", "slice", "fromString", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "checked", "copy", "buffer", "val", "type", "data", "fromObject", "assertSize", "size", "toString", "<PERSON><PERSON><PERSON><PERSON>", "loweredCase", "utf8ToBytes", "base64ToBytes", "toLowerCase", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "b", "bidirectionalIndexOf", "dir", "isNaN", "arrayIndexOf", "indexOf", "lastIndexOf", "arr", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "String", "read", "buf", "readUInt16BE", "foundIndex", "found", "j", "hexWrite", "offset", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "str", "byteArray", "push", "charCodeAt", "asciiToBytes", "latin1Write", "base64Write", "ucs2Write", "units", "hi", "lo", "utf16leToBytes", "fromByteArray", "Math", "min", "res", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "firstByte", "codePoint", "bytesPerSequence", "codePoints", "fromCharCode", "apply", "decodeCodePointsArray", "<PERSON><PERSON><PERSON><PERSON>", "alloc", "INSPECT_MAX_BYTES", "global", "foo", "subarray", "e", "typedArraySupport", "poolSize", "_augment", "species", "configurable", "fill", "allocUnsafeSlow", "_isBuffer", "compare", "a", "x", "y", "concat", "list", "pos", "swap16", "swap32", "swap64", "arguments", "equals", "inspect", "max", "match", "join", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "includes", "isFinite", "toJSON", "Array", "_arr", "ret", "out", "toHex", "bytes", "checkOffset", "ext", "checkInt", "objectWriteUInt16", "littleEndian", "objectWriteUInt32", "checkIEEE754", "writeFloat", "noAssert", "writeDouble", "newBuf", "sliceLen", "readUIntLE", "mul", "readUIntBE", "readUInt8", "readUInt16LE", "readUInt32LE", "readUInt32BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "writeUIntLE", "writeUIntBE", "writeUInt8", "floor", "writeUInt16LE", "writeUInt16BE", "writeUInt32LE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "writeFloatLE", "writeFloatBE", "writeDoubleLE", "writeDoubleBE", "targetStart", "set", "code", "INVALID_BASE64_RE", "Infinity", "leadSurrogate", "toByteArray", "trim", "replace", "stringtrim", "base64clean", "src", "dst", "require", "multibase", "varint", "cs", "validate", "multihash", "decode", "names", "codes", "defaultLengths", "toHexString", "hash", "fromHexString", "toB58String", "encode", "fromB58String", "encoded", "isValidCode", "digest", "hashfn", "coerceCode", "isAppCode", "prefix", "g", "Function", "b64", "lens", "getLens", "validLen", "placeHoldersLen", "tmp", "Arr", "_byteLength", "curByte", "revLookup", "uint8", "extraBytes", "parts", "len2", "encodeChunk", "lookup", "num", "output", "isLE", "mLen", "nBytes", "eLen", "eMax", "eBias", "nBits", "NaN", "rt", "abs", "log", "LN2", "constants", "nameOrCode", "base", "getBase", "codeBuf", "validEncode", "isImplemented", "bufOrString", "substring", "isEncoded", "err", "freeze", "keys", "Base", "baseX", "base16", "base32", "reduce", "prev", "tupple", "implementation", "alphabet", "engine", "string<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>er", "ALPHABET", "BASE_MAP", "char<PERSON>t", "xc", "BASE", "LEADER", "FACTOR", "iFACTOR", "decodeUnsafe", "source", "psz", "zeroes", "b256", "carry", "it3", "it4", "vch", "pbegin", "pend", "b58", "it1", "it2", "repeat", "copyProps", "SafeBuffer", "input", "char", "view", "padding", "bits", "RegExp", "index", "url", "pad", "<PERSON><PERSON><PERSON><PERSON>", "oldOffset", "INT", "shift", "counter", "N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "0"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,IARxB,CASGK,QAAQ,WACX,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,gCClFrD,YAUA,IAAIC,EAAS,EAAQ,GAEjBC,EAAU,EAAQ,GAElBC,EAAU,EAAQ,GAsDtB,SAASC,IACP,OAAOC,EAAOC,oBAAsB,WAAa,WAGnD,SAASC,EAAaC,EAAMC,GAC1B,GAAIL,IAAeK,EACjB,MAAM,IAAIC,WAAW,8BAgBvB,OAbIL,EAAOC,qBAETE,EAAO,IAAIG,WAAWF,IACjBG,UAAYP,EAAOR,WAGX,OAATW,IACFA,EAAO,IAAIH,EAAOI,IAGpBD,EAAKC,OAASA,GAGTD,EAaT,SAASH,EAAOQ,EAAKC,EAAkBL,GACrC,KAAKJ,EAAOC,qBAAyBS,gBAAgBV,GACnD,OAAO,IAAIA,EAAOQ,EAAKC,EAAkBL,GAI3C,GAAmB,iBAARI,EAAkB,CAC3B,GAAgC,iBAArBC,EACT,MAAM,IAAIE,MAAM,qEAGlB,OAAOC,EAAYF,KAAMF,GAG3B,OAAOK,EAAKH,KAAMF,EAAKC,EAAkBL,GAW3C,SAASS,EAAKV,EAAMtB,EAAO4B,EAAkBL,GAC3C,GAAqB,iBAAVvB,EACT,MAAM,IAAIiC,UAAU,yCAGtB,MAA2B,oBAAhBC,aAA+BlC,aAAiBkC,YAsI7D,SAAyBZ,EAAMa,EAAOC,EAAYb,GAGhD,GAFAY,EAAME,WAEFD,EAAa,GAAKD,EAAME,WAAaD,EACvC,MAAM,IAAIZ,WAAW,6BAGvB,GAAIW,EAAME,WAAaD,GAAcb,GAAU,GAC7C,MAAM,IAAIC,WAAW,6BAIrBW,OADiBG,IAAfF,QAAuCE,IAAXf,EACtB,IAAIE,WAAWU,QACHG,IAAXf,EACD,IAAIE,WAAWU,EAAOC,GAEtB,IAAIX,WAAWU,EAAOC,EAAYb,GAGxCJ,EAAOC,qBAETE,EAAOa,GACFT,UAAYP,EAAOR,UAGxBW,EAAOiB,EAAcjB,EAAMa,GAG7B,OAAOb,EAjKEkB,CAAgBlB,EAAMtB,EAAO4B,EAAkBL,GAGnC,iBAAVvB,EAgGb,SAAoBsB,EAAMmB,EAAQC,GACR,iBAAbA,GAAsC,KAAbA,IAClCA,EAAW,QAGb,IAAKvB,EAAOwB,WAAWD,GACrB,MAAM,IAAIT,UAAU,8CAGtB,IAAIV,EAAwC,EAA/Bc,EAAWI,EAAQC,GAE5BE,GADJtB,EAAOD,EAAaC,EAAMC,IACRsB,MAAMJ,EAAQC,GAE5BE,IAAWrB,IAIbD,EAAOA,EAAKwB,MAAM,EAAGF,IAGvB,OAAOtB,EAnHEyB,CAAWzB,EAAMtB,EAAO4B,GAgKnC,SAAoBN,EAAM0B,GACxB,GAAI7B,EAAO8B,SAASD,GAAM,CACxB,IAAIE,EAA4B,EAAtBC,EAAQH,EAAIzB,QAGtB,OAAoB,KAFpBD,EAAOD,EAAaC,EAAM4B,IAEjB3B,QAITyB,EAAII,KAAK9B,EAAM,EAAG,EAAG4B,GAHZ5B,EAOX,GAAI0B,EAAK,CACP,GAA2B,oBAAhBd,aAA+Bc,EAAIK,kBAAkBnB,aAAe,WAAYc,EACzF,MAA0B,iBAAfA,EAAIzB,SAigDN+B,EAjgDmCN,EAAIzB,SAkgDrC+B,EAjgDFjC,EAAaC,EAAM,GAGrBiB,EAAcjB,EAAM0B,GAG7B,GAAiB,WAAbA,EAAIO,MAAqBtC,EAAQ+B,EAAIQ,MACvC,OAAOjB,EAAcjB,EAAM0B,EAAIQ,MAy/CrC,IAAeF,EAr/Cb,MAAM,IAAIrB,UAAU,sFAxLbwB,CAAWnC,EAAMtB,GA6B1B,SAAS0D,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,oCACf,GAAI0B,EAAO,EAChB,MAAM,IAAInC,WAAW,wCA8BzB,SAASO,EAAYT,EAAMqC,GAIzB,GAHAD,EAAWC,GACXrC,EAAOD,EAAaC,EAAMqC,EAAO,EAAI,EAAoB,EAAhBR,EAAQQ,KAE5CxC,EAAOC,oBACV,IAAK,IAAIrC,EAAI,EAAGA,EAAI4E,IAAQ5E,EAC1BuC,EAAKvC,GAAK,EAId,OAAOuC,EA0CT,SAASiB,EAAcjB,EAAMa,GAC3B,IAAIZ,EAASY,EAAMZ,OAAS,EAAI,EAA4B,EAAxB4B,EAAQhB,EAAMZ,QAClDD,EAAOD,EAAaC,EAAMC,GAE1B,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,EAAQxC,GAAK,EAC/BuC,EAAKvC,GAAgB,IAAXoD,EAAMpD,GAGlB,OAAOuC,EAgET,SAAS6B,EAAQ5B,GAGf,GAAIA,GAAUL,IACZ,MAAM,IAAIM,WAAW,0DAAiEN,IAAa0C,SAAS,IAAM,UAGpH,OAAgB,EAATrC,EA8FT,SAASc,EAAWI,EAAQC,GAC1B,GAAIvB,EAAO8B,SAASR,GAClB,OAAOA,EAAOlB,OAGhB,GAA2B,oBAAhBW,aAA6D,mBAAvBA,YAAY2B,SAA0B3B,YAAY2B,OAAOpB,IAAWA,aAAkBP,aACrI,OAAOO,EAAOJ,WAGM,iBAAXI,IACTA,EAAS,GAAKA,GAGhB,IAAIS,EAAMT,EAAOlB,OACjB,GAAY,IAAR2B,EAAW,OAAO,EAItB,IAFA,IAAIY,GAAc,IAGhB,OAAQpB,GACN,IAAK,QACL,IAAK,SACL,IAAK,SACH,OAAOQ,EAET,IAAK,OACL,IAAK,QACL,UAAKZ,EACH,OAAOyB,EAAYtB,GAAQlB,OAE7B,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAa,EAAN2B,EAET,IAAK,MACH,OAAOA,IAAQ,EAEjB,IAAK,SACH,OAAOc,EAAcvB,GAAQlB,OAE/B,QACE,GAAIuC,EAAa,OAAOC,EAAYtB,GAAQlB,OAE5CmB,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,GAOtB,SAASI,EAAaxB,EAAUyB,EAAOC,GACrC,IAAIN,GAAc,EAalB,SANcxB,IAAV6B,GAAuBA,EAAQ,KACjCA,EAAQ,GAKNA,EAAQtC,KAAKN,OACf,MAAO,GAOT,SAJYe,IAAR8B,GAAqBA,EAAMvC,KAAKN,UAClC6C,EAAMvC,KAAKN,QAGT6C,GAAO,EACT,MAAO,GAOT,IAHAA,KAAS,KACTD,KAAW,GAGT,MAAO,GAKT,IAFKzB,IAAUA,EAAW,UAGxB,OAAQA,GACN,IAAK,MACH,OAAO2B,EAASxC,KAAMsC,EAAOC,GAE/B,IAAK,OACL,IAAK,QACH,OAAOE,EAAUzC,KAAMsC,EAAOC,GAEhC,IAAK,QACH,OAAOG,EAAW1C,KAAMsC,EAAOC,GAEjC,IAAK,SACL,IAAK,SACH,OAAOI,EAAY3C,KAAMsC,EAAOC,GAElC,IAAK,SACH,OAAOK,EAAY5C,KAAMsC,EAAOC,GAElC,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOM,EAAa7C,KAAMsC,EAAOC,GAEnC,QACE,GAAIN,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAYA,EAAW,IAAIuB,cAC3BH,GAAc,GAStB,SAASa,EAAKC,EAAGpE,EAAGrB,GAClB,IAAIJ,EAAI6F,EAAEpE,GACVoE,EAAEpE,GAAKoE,EAAEzF,GACTyF,EAAEzF,GAAKJ,EAgJT,SAAS8F,EAAqBxB,EAAQC,EAAKlB,EAAYM,EAAUoC,GAE/D,GAAsB,IAAlBzB,EAAO9B,OAAc,OAAQ,EAqBjC,GAnB0B,iBAAfa,GACTM,EAAWN,EACXA,EAAa,GACJA,EAAa,WACtBA,EAAa,WACJA,GAAc,aACvBA,GAAc,YAGhBA,GAAcA,EAEV2C,MAAM3C,KAERA,EAAa0C,EAAM,EAAIzB,EAAO9B,OAAS,GAIrCa,EAAa,IAAGA,EAAaiB,EAAO9B,OAASa,GAE7CA,GAAciB,EAAO9B,OAAQ,CAC/B,GAAIuD,EAAK,OAAQ,EAAO1C,EAAaiB,EAAO9B,OAAS,OAChD,GAAIa,EAAa,EAAG,CACzB,IAAI0C,EAAyB,OAAQ,EAA5B1C,EAAa,EASxB,GALmB,iBAARkB,IACTA,EAAMnC,EAAOa,KAAKsB,EAAKZ,IAIrBvB,EAAO8B,SAASK,GAElB,OAAmB,IAAfA,EAAI/B,QACE,EAGHyD,EAAa3B,EAAQC,EAAKlB,EAAYM,EAAUoC,GAClD,GAAmB,iBAARxB,EAGhB,OAFAA,GAAY,IAERnC,EAAOC,qBAA+D,mBAAjCK,WAAWd,UAAUsE,QACxDH,EACKrD,WAAWd,UAAUsE,QAAQ/F,KAAKmE,EAAQC,EAAKlB,GAE/CX,WAAWd,UAAUuE,YAAYhG,KAAKmE,EAAQC,EAAKlB,GAIvD4C,EAAa3B,EAAQ,CAACC,GAAMlB,EAAYM,EAAUoC,GAG3D,MAAM,IAAI7C,UAAU,wCAGtB,SAAS+C,EAAaG,EAAK7B,EAAKlB,EAAYM,EAAUoC,GACpD,IA2BI/F,EA3BAqG,EAAY,EACZC,EAAYF,EAAI5D,OAChB+D,EAAYhC,EAAI/B,OAEpB,QAAiBe,IAAbI,IAGe,UAFjBA,EAAW6C,OAAO7C,GAAUuB,gBAEY,UAAbvB,GAAqC,YAAbA,GAAuC,aAAbA,GAAyB,CACpG,GAAIyC,EAAI5D,OAAS,GAAK+B,EAAI/B,OAAS,EACjC,OAAQ,EAGV6D,EAAY,EACZC,GAAa,EACbC,GAAa,EACblD,GAAc,EAIlB,SAASoD,EAAKC,EAAK1G,GACjB,OAAkB,IAAdqG,EACKK,EAAI1G,GAEJ0G,EAAIC,aAAa3G,EAAIqG,GAMhC,GAAIN,EAAK,CACP,IAAIa,GAAc,EAElB,IAAK5G,EAAIqD,EAAYrD,EAAIsG,EAAWtG,IAClC,GAAIyG,EAAKL,EAAKpG,KAAOyG,EAAKlC,GAAqB,IAAhBqC,EAAoB,EAAI5G,EAAI4G,IAEzD,IADoB,IAAhBA,IAAmBA,EAAa5G,GAChCA,EAAI4G,EAAa,IAAML,EAAW,OAAOK,EAAaP,OAEtC,IAAhBO,IAAmB5G,GAAKA,EAAI4G,GAChCA,GAAc,OAMlB,IAFIvD,EAAakD,EAAYD,IAAWjD,EAAaiD,EAAYC,GAE5DvG,EAAIqD,EAAYrD,GAAK,EAAGA,IAAK,CAGhC,IAFA,IAAI6G,GAAQ,EAEHC,EAAI,EAAGA,EAAIP,EAAWO,IAC7B,GAAIL,EAAKL,EAAKpG,EAAI8G,KAAOL,EAAKlC,EAAKuC,GAAI,CACrCD,GAAQ,EACR,MAIJ,GAAIA,EAAO,OAAO7G,EAItB,OAAQ,EAeV,SAAS+G,EAASL,EAAKhD,EAAQsD,EAAQxE,GACrCwE,EAASC,OAAOD,IAAW,EAC3B,IAAIE,EAAYR,EAAIlE,OAASwE,EAExBxE,GAGHA,EAASyE,OAAOzE,IAEH0E,IACX1E,EAAS0E,GALX1E,EAAS0E,EAUX,IAAIC,EAASzD,EAAOlB,OACpB,GAAI2E,EAAS,GAAM,EAAG,MAAM,IAAIjE,UAAU,sBAEtCV,EAAS2E,EAAS,IACpB3E,EAAS2E,EAAS,GAGpB,IAAK,IAAInH,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAC/B,IAAIoH,EAASC,SAAS3D,EAAO4D,OAAW,EAAJtH,EAAO,GAAI,IAC/C,GAAIgG,MAAMoB,GAAS,OAAOpH,EAC1B0G,EAAIM,EAAShH,GAAKoH,EAGpB,OAAOpH,EAGT,SAASuH,EAAUb,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EAAWxC,EAAYtB,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GAG3E,SAASiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GACvC,OAAOgF,EA26BT,SAAsBE,GAGpB,IAFA,IAAIC,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,SAAUxC,EAEhC2H,EAAUC,KAAyB,IAApBF,EAAIG,WAAW7H,IAGhC,OAAO2H,EAn7BWG,CAAapE,GAASgD,EAAKM,EAAQxE,GAGvD,SAASuF,EAAYrB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GAGzC,SAASwF,EAAYtB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOgF,EAAWvC,EAAcvB,GAASgD,EAAKM,EAAQxE,GAGxD,SAASyF,EAAUvB,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EA06BT,SAAwBE,EAAKQ,GAI3B,IAHA,IAAI7H,EAAG8H,EAAIC,EACPT,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,WACjB0F,GAAS,GAAK,KADalI,EAEhCK,EAAIqH,EAAIG,WAAW7H,GACnBmI,EAAK9H,GAAK,EACV+H,EAAK/H,EAAI,IACTsH,EAAUC,KAAKQ,GACfT,EAAUC,KAAKO,GAGjB,OAAOR,EAv7BWU,CAAe3E,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GA+E9E,SAASkD,EAAYgB,EAAKtB,EAAOC,GAC/B,OAAc,IAAVD,GAAeC,IAAQqB,EAAIlE,OACtBR,EAAOsG,cAAc5B,GAErB1E,EAAOsG,cAAc5B,EAAI3C,MAAMqB,EAAOC,IAIjD,SAASE,EAAUmB,EAAKtB,EAAOC,GAC7BA,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAI3B,IAHA,IAAIoD,EAAM,GACNzI,EAAIoF,EAEDpF,EAAIqF,GAAK,CACd,IAKMqD,EAAYC,EAAWC,EAAYC,EALrCC,EAAYpC,EAAI1G,GAChB+I,EAAY,KACZC,EAAmBF,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAI,EAE5F,GAAI9I,EAAIgJ,GAAoB3D,EAG1B,OAAQ2D,GACN,KAAK,EACCF,EAAY,MACdC,EAAYD,GAGd,MAEF,KAAK,EAGyB,MAAV,KAFlBJ,EAAahC,EAAI1G,EAAI,OAGnB6I,GAA6B,GAAZC,IAAqB,EAAmB,GAAbJ,GAExB,MAClBK,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GAEQ,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,KACnCE,GAA6B,GAAZC,IAAoB,IAAoB,GAAbJ,IAAsB,EAAkB,GAAZC,GAEpD,OAAUE,EAAgB,OAAUA,EAAgB,SACtEE,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GACpB4I,EAAalC,EAAI1G,EAAI,GAEO,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,IAAsD,MAAV,IAAbC,KAClEC,GAA6B,GAAZC,IAAoB,IAAqB,GAAbJ,IAAsB,IAAmB,GAAZC,IAAqB,EAAmB,GAAbC,GAEjF,OAAUC,EAAgB,UAC5CE,EAAYF,GAOJ,OAAdE,GAGFA,EAAY,MACZC,EAAmB,GACVD,EAAY,QAErBA,GAAa,MACbN,EAAIb,KAAKmB,IAAc,GAAK,KAAQ,OACpCA,EAAY,MAAqB,KAAZA,GAGvBN,EAAIb,KAAKmB,GACT/I,GAAKgJ,EAGP,OAQF,SAA+BC,GAC7B,IAAI9E,EAAM8E,EAAWzG,OAErB,GAAI2B,GALqB,KAMvB,OAAOqC,OAAO0C,aAAaC,MAAM3C,OAAQyC,GAI3C,IAAIR,EAAM,GACNzI,EAAI,EAER,KAAOA,EAAImE,GACTsE,GAAOjC,OAAO0C,aAAaC,MAAM3C,OAAQyC,EAAWlF,MAAM/D,EAAGA,GAdtC,OAiBzB,OAAOyI,EAvBAW,CAAsBX,GA1gC/BjJ,EAAQ4C,OAASA,EACjB5C,EAAQ6J,WAiUR,SAAoB7G,IACbA,GAAUA,IAEbA,EAAS,GAGX,OAAOJ,EAAOkH,OAAO9G,IAtUvBhD,EAAQ+J,kBAAoB,GA0B5BnH,EAAOC,yBAAqDkB,IAA/BiG,EAAOnH,oBAAoCmH,EAAOnH,oBAO/E,WACE,IACE,IAAI+D,EAAM,IAAI1D,WAAW,GAOzB,OANA0D,EAAIzD,UAAY,CACdA,UAAWD,WAAWd,UACtB6H,IAAK,WACH,OAAO,KAGU,KAAdrD,EAAIqD,OACa,mBAAjBrD,EAAIsD,UACuB,IAAlCtD,EAAIsD,SAAS,EAAG,GAAGpG,WACnB,MAAOqG,GACP,OAAO,GApB0FC,GAKrGpK,EAAQ2C,WAAaA,IAuErBC,EAAOyH,SAAW,KAGlBzH,EAAO0H,SAAW,SAAU1D,GAE1B,OADAA,EAAIzD,UAAYP,EAAOR,UAChBwE,GA4BThE,EAAOa,KAAO,SAAUhC,EAAO4B,EAAkBL,GAC/C,OAAOS,EAAK,KAAMhC,EAAO4B,EAAkBL,IAGzCJ,EAAOC,sBACTD,EAAOR,UAAUe,UAAYD,WAAWd,UACxCQ,EAAOO,UAAYD,WAEG,oBAAX3B,QAA0BA,OAAOgJ,SAAW3H,EAAOrB,OAAOgJ,WAAa3H,GAEhF1B,OAAOC,eAAeyB,EAAQrB,OAAOgJ,QAAS,CAC5C9I,MAAO,KACP+I,cAAc,KAmCpB5H,EAAOkH,MAAQ,SAAU1E,EAAMqF,EAAMtG,GACnC,OAvBF,SAAepB,EAAMqC,EAAMqF,EAAMtG,GAG/B,OAFAgB,EAAWC,GAEPA,GAAQ,EACHtC,EAAaC,EAAMqC,QAGfrB,IAAT0G,EAIyB,iBAAbtG,EAAwBrB,EAAaC,EAAMqC,GAAMqF,KAAKA,EAAMtG,GAAYrB,EAAaC,EAAMqC,GAAMqF,KAAKA,GAG/G3H,EAAaC,EAAMqC,GASnB0E,CAAM,KAAM1E,EAAMqF,EAAMtG,IAoBjCvB,EAAOY,YAAc,SAAU4B,GAC7B,OAAO5B,EAAY,KAAM4B,IAO3BxC,EAAO8H,gBAAkB,SAAUtF,GACjC,OAAO5B,EAAY,KAAM4B,IAqH3BxC,EAAO8B,SAAW,SAAkB2B,GAClC,QAAe,MAALA,IAAaA,EAAEsE,YAG3B/H,EAAOgI,QAAU,SAAiBC,EAAGxE,GACnC,IAAKzD,EAAO8B,SAASmG,KAAOjI,EAAO8B,SAAS2B,GAC1C,MAAM,IAAI3C,UAAU,6BAGtB,GAAImH,IAAMxE,EAAG,OAAO,EAIpB,IAHA,IAAIyE,EAAID,EAAE7H,OACN+H,EAAI1E,EAAErD,OAEDxC,EAAI,EAAGmE,EAAMoE,KAAKC,IAAI8B,EAAGC,GAAIvK,EAAImE,IAAOnE,EAC/C,GAAIqK,EAAErK,KAAO6F,EAAE7F,GAAI,CACjBsK,EAAID,EAAErK,GACNuK,EAAI1E,EAAE7F,GACN,MAIJ,OAAIsK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAGTlI,EAAOwB,WAAa,SAAoBD,GACtC,OAAQ6C,OAAO7C,GAAUuB,eACvB,IAAK,MACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,SACL,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAO,EAET,QACE,OAAO,IAIb9C,EAAOoI,OAAS,SAAgBC,EAAMjI,GACpC,IAAKN,EAAQuI,GACX,MAAM,IAAIvH,UAAU,+CAGtB,GAAoB,IAAhBuH,EAAKjI,OACP,OAAOJ,EAAOkH,MAAM,GAGtB,IAAItJ,EAEJ,QAAeuD,IAAXf,EAGF,IAFAA,EAAS,EAEJxC,EAAI,EAAGA,EAAIyK,EAAKjI,SAAUxC,EAC7BwC,GAAUiI,EAAKzK,GAAGwC,OAItB,IAAI8B,EAASlC,EAAOY,YAAYR,GAC5BkI,EAAM,EAEV,IAAK1K,EAAI,EAAGA,EAAIyK,EAAKjI,SAAUxC,EAAG,CAChC,IAAI0G,EAAM+D,EAAKzK,GAEf,IAAKoC,EAAO8B,SAASwC,GACnB,MAAM,IAAIxD,UAAU,+CAGtBwD,EAAIrC,KAAKC,EAAQoG,GACjBA,GAAOhE,EAAIlE,OAGb,OAAO8B,GAsDTlC,EAAOkB,WAAaA,EAyEpBlB,EAAOR,UAAUuI,WAAY,EAQ7B/H,EAAOR,UAAU+I,OAAS,WACxB,IAAIxG,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAGpB,OAAO8C,MAGTV,EAAOR,UAAUgJ,OAAS,WACxB,IAAIzG,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUiJ,OAAS,WACxB,IAAI1G,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUiD,SAAW,WAC1B,IAAIrC,EAAuB,EAAdM,KAAKN,OAClB,OAAe,IAAXA,EAAqB,GACA,IAArBsI,UAAUtI,OAAqB+C,EAAUzC,KAAM,EAAGN,GAC/C2C,EAAagE,MAAMrG,KAAMgI,YAGlC1I,EAAOR,UAAUmJ,OAAS,SAAgBlF,GACxC,IAAKzD,EAAO8B,SAAS2B,GAAI,MAAM,IAAI3C,UAAU,6BAC7C,OAAIJ,OAAS+C,GACsB,IAA5BzD,EAAOgI,QAAQtH,KAAM+C,IAG9BzD,EAAOR,UAAUoJ,QAAU,WACzB,IAAItD,EAAM,GACNuD,EAAMzL,EAAQ+J,kBAOlB,OALIzG,KAAKN,OAAS,IAChBkF,EAAM5E,KAAK+B,SAAS,MAAO,EAAGoG,GAAKC,MAAM,SAASC,KAAK,KACnDrI,KAAKN,OAASyI,IAAKvD,GAAO,UAGzB,WAAaA,EAAM,KAG5BtF,EAAOR,UAAUwI,QAAU,SAAiBgB,EAAQhG,EAAOC,EAAKgG,EAAWC,GACzE,IAAKlJ,EAAO8B,SAASkH,GACnB,MAAM,IAAIlI,UAAU,6BAmBtB,QAhBcK,IAAV6B,IACFA,EAAQ,QAGE7B,IAAR8B,IACFA,EAAM+F,EAASA,EAAO5I,OAAS,QAGfe,IAAd8H,IACFA,EAAY,QAGE9H,IAAZ+H,IACFA,EAAUxI,KAAKN,QAGb4C,EAAQ,GAAKC,EAAM+F,EAAO5I,QAAU6I,EAAY,GAAKC,EAAUxI,KAAKN,OACtE,MAAM,IAAIC,WAAW,sBAGvB,GAAI4I,GAAaC,GAAWlG,GAASC,EACnC,OAAO,EAGT,GAAIgG,GAAaC,EACf,OAAQ,EAGV,GAAIlG,GAASC,EACX,OAAO,EAOT,GAAIvC,OAASsI,EAAQ,OAAO,EAO5B,IANA,IAAId,GAFJgB,KAAa,IADbD,KAAe,GAIXd,GALJlF,KAAS,IADTD,KAAW,GAOPjB,EAAMoE,KAAKC,IAAI8B,EAAGC,GAClBgB,EAAWzI,KAAKiB,MAAMsH,EAAWC,GACjCE,EAAaJ,EAAOrH,MAAMqB,EAAOC,GAE5BrF,EAAI,EAAGA,EAAImE,IAAOnE,EACzB,GAAIuL,EAASvL,KAAOwL,EAAWxL,GAAI,CACjCsK,EAAIiB,EAASvL,GACbuK,EAAIiB,EAAWxL,GACf,MAIJ,OAAIsK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAqITlI,EAAOR,UAAU6J,SAAW,SAAkBlH,EAAKlB,EAAYM,GAC7D,OAAoD,IAA7Cb,KAAKoD,QAAQ3B,EAAKlB,EAAYM,IAGvCvB,EAAOR,UAAUsE,QAAU,SAAiB3B,EAAKlB,EAAYM,GAC3D,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAG/DvB,EAAOR,UAAUuE,YAAc,SAAqB5B,EAAKlB,EAAYM,GACnE,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAsD/DvB,EAAOR,UAAUkC,MAAQ,SAAeJ,EAAQsD,EAAQxE,EAAQmB,GAE9D,QAAeJ,IAAXyD,EACFrD,EAAW,OACXnB,EAASM,KAAKN,OACdwE,EAAS,OACJ,QAAezD,IAAXf,GAA0C,iBAAXwE,EACxCrD,EAAWqD,EACXxE,EAASM,KAAKN,OACdwE,EAAS,MACJ,KAAI0E,SAAS1E,GAYlB,MAAM,IAAIjE,MAAM,2EAXhBiE,GAAkB,EAEd0E,SAASlJ,IACXA,GAAkB,OACDe,IAAbI,IAAwBA,EAAW,UAEvCA,EAAWnB,EACXA,OAASe,GAOb,IAAI2D,EAAYpE,KAAKN,OAASwE,EAG9B,SAFezD,IAAXf,GAAwBA,EAAS0E,KAAW1E,EAAS0E,GAErDxD,EAAOlB,OAAS,IAAMA,EAAS,GAAKwE,EAAS,IAAMA,EAASlE,KAAKN,OACnE,MAAM,IAAIC,WAAW,0CAGlBkB,IAAUA,EAAW,QAG1B,IAFA,IAAIoB,GAAc,IAGhB,OAAQpB,GACN,IAAK,MACH,OAAOoD,EAASjE,KAAMY,EAAQsD,EAAQxE,GAExC,IAAK,OACL,IAAK,QACH,OAAO+E,EAAUzE,KAAMY,EAAQsD,EAAQxE,GAEzC,IAAK,QACH,OAAOiF,EAAW3E,KAAMY,EAAQsD,EAAQxE,GAE1C,IAAK,SACL,IAAK,SACH,OAAOuF,EAAYjF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,SAEH,OAAOwF,EAAYlF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOyF,EAAUnF,KAAMY,EAAQsD,EAAQxE,GAEzC,QACE,GAAIuC,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,IAKtB3C,EAAOR,UAAU+J,OAAS,WACxB,MAAO,CACLnH,KAAM,SACNC,KAAMmH,MAAMhK,UAAUmC,MAAM5D,KAAK2C,KAAK+I,MAAQ/I,KAAM,KAsHxD,SAAS0C,EAAWkB,EAAKtB,EAAOC,GAC9B,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B8L,GAAOtF,OAAO0C,aAAsB,IAATxC,EAAI1G,IAGjC,OAAO8L,EAGT,SAASrG,EAAYiB,EAAKtB,EAAOC,GAC/B,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B8L,GAAOtF,OAAO0C,aAAaxC,EAAI1G,IAGjC,OAAO8L,EAGT,SAASxG,EAASoB,EAAKtB,EAAOC,GAC5B,IAAIlB,EAAMuC,EAAIlE,SACT4C,GAASA,EAAQ,KAAGA,EAAQ,KAC5BC,GAAOA,EAAM,GAAKA,EAAMlB,KAAKkB,EAAMlB,GAGxC,IAFA,IAAI4H,EAAM,GAED/L,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B+L,GAAOC,EAAMtF,EAAI1G,IAGnB,OAAO+L,EAGT,SAASpG,EAAae,EAAKtB,EAAOC,GAIhC,IAHA,IAAI4G,EAAQvF,EAAI3C,MAAMqB,EAAOC,GACzBoD,EAAM,GAEDzI,EAAI,EAAGA,EAAIiM,EAAMzJ,OAAQxC,GAAK,EACrCyI,GAAOjC,OAAO0C,aAAa+C,EAAMjM,GAAoB,IAAfiM,EAAMjM,EAAI,IAGlD,OAAOyI,EA4CT,SAASyD,EAAYlF,EAAQmF,EAAK3J,GAChC,GAAIwE,EAAS,GAAM,GAAKA,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBACzD,GAAIuE,EAASmF,EAAM3J,EAAQ,MAAM,IAAIC,WAAW,yCA+IlD,SAAS2J,EAAS1F,EAAKzF,EAAO+F,EAAQmF,EAAKlB,EAAKzC,GAC9C,IAAKpG,EAAO8B,SAASwC,GAAM,MAAM,IAAIxD,UAAU,+CAC/C,GAAIjC,EAAQgK,GAAOhK,EAAQuH,EAAK,MAAM,IAAI/F,WAAW,qCACrD,GAAIuE,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBAsDtD,SAAS4J,EAAkB3F,EAAKzF,EAAO+F,EAAQsF,GACzCrL,EAAQ,IAAGA,EAAQ,MAASA,EAAQ,GAExC,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,IAAMiB,EAAQ,KAAQ,GAAKqL,EAAetM,EAAI,EAAIA,MAAqC,GAA5BsM,EAAetM,EAAI,EAAIA,GAkCnG,SAASuM,EAAkB7F,EAAKzF,EAAO+F,EAAQsF,GACzCrL,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAE5C,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,GAAKiB,IAAuC,GAA5BqL,EAAetM,EAAI,EAAIA,GAAS,IAmKjE,SAASwM,EAAa9F,EAAKzF,EAAO+F,EAAQmF,EAAKlB,EAAKzC,GAClD,GAAIxB,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBACpD,GAAIuE,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBAGvC,SAASgK,EAAW/F,EAAKzF,EAAO+F,EAAQsF,EAAcI,GAMpD,OALKA,GACHF,EAAa9F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EAWlB,SAAS2F,EAAYjG,EAAKzF,EAAO+F,EAAQsF,EAAcI,GAMrD,OALKA,GACHF,EAAa9F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EA5dlB5E,EAAOR,UAAUmC,MAAQ,SAAeqB,EAAOC,GAC7C,IAmBIuH,EAnBAzI,EAAMrB,KAAKN,OAqBf,IApBA4C,IAAUA,GAGE,GACVA,GAASjB,GACG,IAAGiB,EAAQ,GACdA,EAAQjB,IACjBiB,EAAQjB,IANVkB,OAAc9B,IAAR8B,EAAoBlB,IAAQkB,GASxB,GACRA,GAAOlB,GACG,IAAGkB,EAAM,GACVA,EAAMlB,IACfkB,EAAMlB,GAGJkB,EAAMD,IAAOC,EAAMD,GAGnBhD,EAAOC,qBACTuK,EAAS9J,KAAK4G,SAAStE,EAAOC,IACvB1C,UAAYP,EAAOR,cACrB,CACL,IAAIiL,EAAWxH,EAAMD,EACrBwH,EAAS,IAAIxK,EAAOyK,OAAUtJ,GAE9B,IAAK,IAAIvD,EAAI,EAAGA,EAAI6M,IAAY7M,EAC9B4M,EAAO5M,GAAK8C,KAAK9C,EAAIoF,GAIzB,OAAOwH,GAYTxK,EAAOR,UAAUkL,WAAa,SAAoB9F,EAAQ1D,EAAYoJ,GACpE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACN/M,EAAI,IAECA,EAAIsD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAAShH,GAAK+M,EAG5B,OAAOxI,GAGTnC,EAAOR,UAAUoL,WAAa,SAAoBhG,EAAQ1D,EAAYoJ,GACpE1F,GAAkB,EAClB1D,GAA0B,EAErBoJ,GACHR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAMvC,IAHA,IAAI+B,EAAMzB,KAAKkE,IAAW1D,GACtByJ,EAAM,EAEHzJ,EAAa,IAAMyJ,GAAO,MAC/BxI,GAAOzB,KAAKkE,IAAW1D,GAAcyJ,EAGvC,OAAOxI,GAGTnC,EAAOR,UAAUqL,UAAY,SAAmBjG,EAAQ0F,GAEtD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAGd5E,EAAOR,UAAUsL,aAAe,SAAsBlG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,GAG5C5E,EAAOR,UAAU+E,aAAe,SAAsBK,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,EAAIlE,KAAKkE,EAAS,IAG3C5E,EAAOR,UAAUuL,aAAe,SAAsBnG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,SACnCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,IAAyB,SAAnBlE,KAAKkE,EAAS,IAGzF5E,EAAOR,UAAUwL,aAAe,SAAsBpG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACrB,SAAfM,KAAKkE,IAAuBlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,KAGpG5E,EAAOR,UAAUyL,UAAY,SAAmBrG,EAAQ1D,EAAYoJ,GAClE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACN/M,EAAI,IAECA,EAAIsD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAAShH,GAAK+M,EAK5B,OADIxI,IADJwI,GAAO,OACSxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAChCiB,GAGTnC,EAAOR,UAAU2L,UAAY,SAAmBvG,EAAQ1D,EAAYoJ,GAClE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAIxC,EAAIsD,EACJyJ,EAAM,EACNxI,EAAMzB,KAAKkE,IAAWhH,GAEnBA,EAAI,IAAM+M,GAAO,MACtBxI,GAAOzB,KAAKkE,IAAWhH,GAAK+M,EAK9B,OADIxI,IADJwI,GAAO,OACSxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAChCiB,GAGTnC,EAAOR,UAAU4L,SAAW,SAAkBxG,EAAQ0F,GAEpD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACtB,IAAfM,KAAKkE,IACyB,GAA5B,IAAOlE,KAAKkE,GAAU,GADKlE,KAAKkE,IAI1C5E,EAAOR,UAAU6L,YAAc,SAAqBzG,EAAQ0F,GACrDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAU8L,YAAc,SAAqB1G,EAAQ0F,GACrDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,EAAS,GAAKlE,KAAKkE,IAAW,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAU+L,YAAc,SAAqB3G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,IAG7F5E,EAAOR,UAAUgM,YAAc,SAAqB5G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,GAAKlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAG7F5E,EAAOR,UAAUiM,YAAc,SAAqB7G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUkM,YAAc,SAAqB9G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAG/C5E,EAAOR,UAAUmM,aAAe,SAAsB/G,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUoM,aAAe,SAAsBhH,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAS/C5E,EAAOR,UAAUqM,YAAc,SAAqBhN,EAAO+F,EAAQ1D,EAAYoJ,IAC7EzL,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBoJ,IAEHN,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAIyJ,EAAM,EACN/M,EAAI,EAGR,IAFA8C,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAeyJ,GAAO,MACjCjK,KAAKkE,EAAShH,GAAKiB,EAAQ8L,EAAM,IAGnC,OAAO/F,EAAS1D,GAGlBlB,EAAOR,UAAUsM,YAAc,SAAqBjN,EAAO+F,EAAQ1D,EAAYoJ,IAC7EzL,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBoJ,IAEHN,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAItD,EAAIsD,EAAa,EACjByJ,EAAM,EAGV,IAFAjK,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAM+M,GAAO,MACzBjK,KAAKkE,EAAShH,GAAKiB,EAAQ8L,EAAM,IAGnC,OAAO/F,EAAS1D,GAGlBlB,EAAOR,UAAUuM,WAAa,SAAoBlN,EAAO+F,EAAQ0F,GAM/D,OALAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,IAAM,GACjD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK6F,MAAMnN,IACpD6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAWlB5E,EAAOR,UAAUyM,cAAgB,SAAuBpN,EAAO+F,EAAQ0F,GAYrE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU0M,cAAgB,SAAuBrN,EAAO+F,EAAQ0F,GAYrE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAWlB5E,EAAOR,UAAU2M,cAAgB,SAAuBtN,EAAO+F,EAAQ0F,GAcrE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,GAAkB,IAAR/F,GAEfsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU4M,cAAgB,SAAuBvN,EAAO+F,EAAQ0F,GAcrE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU6M,WAAa,SAAoBxN,EAAO+F,EAAQ1D,EAAYoJ,GAI3E,GAHAzL,GAASA,EACT+F,GAAkB,GAEb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GACzC8I,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI1O,EAAI,EACJ+M,EAAM,EACN4B,EAAM,EAGV,IAFA7L,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAeyJ,GAAO,MAC7B9L,EAAQ,GAAa,IAAR0N,GAAsC,IAAzB7L,KAAKkE,EAAShH,EAAI,KAC9C2O,EAAM,GAGR7L,KAAKkE,EAAShH,IAAMiB,EAAQ8L,GAAO,GAAK4B,EAAM,IAGhD,OAAO3H,EAAS1D,GAGlBlB,EAAOR,UAAUgN,WAAa,SAAoB3N,EAAO+F,EAAQ1D,EAAYoJ,GAI3E,GAHAzL,GAASA,EACT+F,GAAkB,GAEb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GACzC8I,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI1O,EAAIsD,EAAa,EACjByJ,EAAM,EACN4B,EAAM,EAGV,IAFA7L,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAM+M,GAAO,MACrB9L,EAAQ,GAAa,IAAR0N,GAAsC,IAAzB7L,KAAKkE,EAAShH,EAAI,KAC9C2O,EAAM,GAGR7L,KAAKkE,EAAShH,IAAMiB,EAAQ8L,GAAO,GAAK4B,EAAM,IAGhD,OAAO3H,EAAS1D,GAGlBlB,EAAOR,UAAUiN,UAAY,SAAmB5N,EAAO+F,EAAQ0F,GAO7D,OANAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,KAAO,KAClD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK6F,MAAMnN,IAChDA,EAAQ,IAAGA,EAAQ,IAAOA,EAAQ,GACtC6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAGlB5E,EAAOR,UAAUkN,aAAe,SAAsB7N,EAAO+F,EAAQ0F,GAYnE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUmN,aAAe,SAAsB9N,EAAO+F,EAAQ0F,GAYnE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUoN,aAAe,SAAsB/N,EAAO+F,EAAQ0F,GAcnE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YAEzD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,IAE7BsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUqN,aAAe,SAAsBhO,EAAO+F,EAAQ0F,GAenE,OAdAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YACzD/F,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAExCmB,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAiBlB5E,EAAOR,UAAUsN,aAAe,SAAsBjO,EAAO+F,EAAQ0F,GACnE,OAAOD,EAAW3J,KAAM7B,EAAO+F,GAAQ,EAAM0F,IAG/CtK,EAAOR,UAAUuN,aAAe,SAAsBlO,EAAO+F,EAAQ0F,GACnE,OAAOD,EAAW3J,KAAM7B,EAAO+F,GAAQ,EAAO0F,IAYhDtK,EAAOR,UAAUwN,cAAgB,SAAuBnO,EAAO+F,EAAQ0F,GACrE,OAAOC,EAAY7J,KAAM7B,EAAO+F,GAAQ,EAAM0F,IAGhDtK,EAAOR,UAAUyN,cAAgB,SAAuBpO,EAAO+F,EAAQ0F,GACrE,OAAOC,EAAY7J,KAAM7B,EAAO+F,GAAQ,EAAO0F,IAIjDtK,EAAOR,UAAUyC,KAAO,SAAc+G,EAAQkE,EAAalK,EAAOC,GAOhE,GANKD,IAAOA,EAAQ,GACfC,GAAe,IAARA,IAAWA,EAAMvC,KAAKN,QAC9B8M,GAAelE,EAAO5I,SAAQ8M,EAAclE,EAAO5I,QAClD8M,IAAaA,EAAc,GAC5BjK,EAAM,GAAKA,EAAMD,IAAOC,EAAMD,GAE9BC,IAAQD,EAAO,OAAO,EAC1B,GAAsB,IAAlBgG,EAAO5I,QAAgC,IAAhBM,KAAKN,OAAc,OAAO,EAErD,GAAI8M,EAAc,EAChB,MAAM,IAAI7M,WAAW,6BAGvB,GAAI2C,EAAQ,GAAKA,GAAStC,KAAKN,OAAQ,MAAM,IAAIC,WAAW,6BAC5D,GAAI4C,EAAM,EAAG,MAAM,IAAI5C,WAAW,2BAE9B4C,EAAMvC,KAAKN,SAAQ6C,EAAMvC,KAAKN,QAE9B4I,EAAO5I,OAAS8M,EAAcjK,EAAMD,IACtCC,EAAM+F,EAAO5I,OAAS8M,EAAclK,GAGtC,IACIpF,EADAmE,EAAMkB,EAAMD,EAGhB,GAAItC,OAASsI,GAAUhG,EAAQkK,GAAeA,EAAcjK,EAE1D,IAAKrF,EAAImE,EAAM,EAAGnE,GAAK,IAAKA,EAC1BoL,EAAOpL,EAAIsP,GAAexM,KAAK9C,EAAIoF,QAEhC,GAAIjB,EAAM,MAAS/B,EAAOC,oBAE/B,IAAKrC,EAAI,EAAGA,EAAImE,IAAOnE,EACrBoL,EAAOpL,EAAIsP,GAAexM,KAAK9C,EAAIoF,QAGrC1C,WAAWd,UAAU2N,IAAIpP,KAAKiL,EAAQtI,KAAK4G,SAAStE,EAAOA,EAAQjB,GAAMmL,GAG3E,OAAOnL,GAOT/B,EAAOR,UAAUqI,KAAO,SAAc1F,EAAKa,EAAOC,EAAK1B,GAErD,GAAmB,iBAARY,EAAkB,CAU3B,GATqB,iBAAVa,GACTzB,EAAWyB,EACXA,EAAQ,EACRC,EAAMvC,KAAKN,QACa,iBAAR6C,IAChB1B,EAAW0B,EACXA,EAAMvC,KAAKN,QAGM,IAAf+B,EAAI/B,OAAc,CACpB,IAAIgN,EAAOjL,EAAIsD,WAAW,GAEtB2H,EAAO,MACTjL,EAAMiL,GAIV,QAAiBjM,IAAbI,GAA8C,iBAAbA,EACnC,MAAM,IAAIT,UAAU,6BAGtB,GAAwB,iBAAbS,IAA0BvB,EAAOwB,WAAWD,GACrD,MAAM,IAAIT,UAAU,qBAAuBS,OAErB,iBAARY,IAChBA,GAAY,KAId,GAAIa,EAAQ,GAAKtC,KAAKN,OAAS4C,GAAStC,KAAKN,OAAS6C,EACpD,MAAM,IAAI5C,WAAW,sBAGvB,GAAI4C,GAAOD,EACT,OAAOtC,KAMT,IAAI9C,EAEJ,GALAoF,KAAkB,EAClBC,OAAc9B,IAAR8B,EAAoBvC,KAAKN,OAAS6C,IAAQ,EAC3Cd,IAAKA,EAAM,GAGG,iBAARA,EACT,IAAKvE,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EACzB8C,KAAK9C,GAAKuE,MAEP,CACL,IAAI0H,EAAQ7J,EAAO8B,SAASK,GAAOA,EAAMS,EAAY,IAAI5C,EAAOmC,EAAKZ,GAAUkB,YAC3EV,EAAM8H,EAAMzJ,OAEhB,IAAKxC,EAAI,EAAGA,EAAIqF,EAAMD,IAASpF,EAC7B8C,KAAK9C,EAAIoF,GAAS6G,EAAMjM,EAAImE,GAIhC,OAAOrB,MAKT,IAAI2M,EAAoB,qBAoBxB,SAASzD,EAAMvK,GACb,OAAIA,EAAI,GAAW,IAAMA,EAAEoD,SAAS,IAC7BpD,EAAEoD,SAAS,IAGpB,SAASG,EAAYtB,EAAQwE,GAE3B,IAAIa,EADJb,EAAQA,GAASwH,IAMjB,IAJA,IAAIlN,EAASkB,EAAOlB,OAChBmN,EAAgB,KAChB1D,EAAQ,GAEHjM,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAG/B,IAFA+I,EAAYrF,EAAOmE,WAAW7H,IAEd,OAAU+I,EAAY,MAAQ,CAE5C,IAAK4G,EAAe,CAElB,GAAI5G,EAAY,MAAQ,EAEjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SACK,GAAI5H,EAAI,IAAMwC,EAAQ,EAEtB0F,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SAIF+H,EAAgB5G,EAChB,SAIF,GAAIA,EAAY,MAAQ,EACjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C+H,EAAgB5G,EAChB,SAIFA,EAAkE,OAArD4G,EAAgB,OAAU,GAAK5G,EAAY,YAC/C4G,IAEJzH,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAKhD,GAFA+H,EAAgB,KAEZ5G,EAAY,IAAM,CACpB,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,QACN,GAAIA,EAAY,KAAO,CAC5B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,EAAM,IAAkB,GAAZA,EAAmB,UAClD,GAAIA,EAAY,MAAS,CAC9B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,GAAM,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,SAClF,MAAIA,EAAY,SAIrB,MAAM,IAAIhG,MAAM,sBAHhB,IAAKmF,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,GAAO,IAAMA,GAAa,GAAM,GAAO,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,MAM5H,OAAOkD,EA8BT,SAAShH,EAAcyC,GACrB,OAAO1F,EAAO4N,YApHhB,SAAqBlI,GAInB,IAFAA,EAWF,SAAoBA,GAClB,OAAIA,EAAImI,KAAanI,EAAImI,OAClBnI,EAAIoI,QAAQ,aAAc,IAb3BC,CAAWrI,GAAKoI,QAAQL,EAAmB,KAEzCjN,OAAS,EAAG,MAAO,GAE3B,KAAOkF,EAAIlF,OAAS,GAAM,GACxBkF,GAAY,IAGd,OAAOA,EA0GmBsI,CAAYtI,IAGxC,SAASF,EAAWyI,EAAKC,EAAKlJ,EAAQxE,GACpC,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,KACdxC,EAAIgH,GAAUkJ,EAAI1N,QAAUxC,GAAKiQ,EAAIzN,UADbxC,EAE5BkQ,EAAIlQ,EAAIgH,GAAUiJ,EAAIjQ,GAGxB,OAAOA,K,8EChzDT,MAAM,OAAEoC,GAAW+N,EAAQ,GACrBC,EAAYD,EAAQ,GACpBE,EAASF,EAAQ,IACjBG,EAAKH,EAAQ,IAqMnB,SAASI,EAAUC,GACjBhR,EAAQiR,OAAOD,GApMjBhR,EAAQkR,MAAQJ,EAAGI,MACnBlR,EAAQmR,MAAQL,EAAGK,MACnBnR,EAAQoR,eAAiBN,EAAGM,eAQ5BpR,EAAQqR,YAAc,SAAsBC,GAC1C,IAAK1O,EAAO8B,SAAS4M,GACnB,MAAM,IAAI/N,MAAM,2BAGlB,OAAO+N,EAAKjM,SAAS,QASvBrF,EAAQuR,cAAgB,SAAwBD,GAC9C,OAAO1O,EAAOa,KAAK6N,EAAM,QAS3BtR,EAAQwR,YAAc,SAAsBF,GAC1C,IAAK1O,EAAO8B,SAAS4M,GACnB,MAAM,IAAI/N,MAAM,2BAGlB,OAAOqN,EAAUa,OAAO,YAAaH,GAAMjM,WAAWd,MAAM,IAS9DvE,EAAQ0R,cAAgB,SAAwBJ,GAC9C,IAAIK,EAAUL,EAKd,OAJI1O,EAAO8B,SAAS4M,KAClBK,EAAUL,EAAKjM,YAGVuL,EAAUK,OAAO,IAAMU,IAShC3R,EAAQiR,OAAS,SAAiB/J,GAChC,IAAMtE,EAAO8B,SAASwC,GACpB,MAAM,IAAI3D,MAAM,8BAGlB,GAAI2D,EAAIlE,OAAS,EACf,MAAM,IAAIO,MAAM,2CAGlB,MAAMyM,EAAOa,EAAOI,OAAO/J,GAC3B,IAAKlH,EAAQ4R,YAAY5B,GACvB,MAAM,IAAIzM,MAAJ,6CAAgDyM,EAAK3K,SAAS,MAEtE6B,EAAMA,EAAI3C,MAAMsM,EAAOI,OAAOxE,OAE9B,MAAM9H,EAAMkM,EAAOI,OAAO/J,GAC1B,GAAIvC,EAAM,EACR,MAAM,IAAIpB,MAAJ,oCAAuCoB,IAI/C,IAFAuC,EAAMA,EAAI3C,MAAMsM,EAAOI,OAAOxE,QAEtBzJ,SAAW2B,EACjB,MAAM,IAAIpB,MAAJ,2CAA8C2D,EAAI7B,SAAS,SAGnE,MAAO,CACL2K,KAAMA,EACNjP,KAAM+P,EAAGK,MAAMnB,GACfhN,OAAQ2B,EACRkN,OAAQ3K,IAcZlH,EAAQyR,OAAS,SAAiBI,EAAQ7B,EAAMhN,GAC9C,IAAK6O,QAAmB9N,IAATiM,EACb,MAAM,IAAIzM,MAAM,6DAIlB,MAAMuO,EAAS9R,EAAQ+R,WAAW/B,GAElC,IAAMpN,EAAO8B,SAASmN,GACpB,MAAM,IAAItO,MAAM,6BAOlB,GAJc,MAAVP,IACFA,EAAS6O,EAAO7O,QAGdA,GAAU6O,EAAO7O,SAAWA,EAC9B,MAAM,IAAIO,MAAM,sDAGlB,OAAOX,EAAOoI,OAAO,CACnBpI,EAAOa,KAAKoN,EAAOY,OAAOK,IAC1BlP,EAAOa,KAAKoN,EAAOY,OAAOzO,IAC1B6O,KAUJ7R,EAAQ+R,WAAa,SAAqBhR,GACxC,IAAIiP,EAAOjP,EAEX,GAAoB,iBAATA,EAAmB,CAC5B,QAAuBgD,IAAnB+M,EAAGI,MAAMnQ,GACX,MAAM,IAAIwC,MAAJ,4CAA+CxC,IAEvDiP,EAAOc,EAAGI,MAAMnQ,GAGlB,GAAoB,iBAATiP,EACT,MAAM,IAAIzM,MAAJ,sDAAyDyM,IAGjE,QAAuBjM,IAAnB+M,EAAGK,MAAMnB,KAAwBhQ,EAAQgS,UAAUhC,GACrD,MAAM,IAAIzM,MAAJ,sCAAyCyM,IAGjD,OAAOA,GASThQ,EAAQgS,UAAY,SAAkBhC,GACpC,OAAOA,EAAO,GAAKA,EAAO,IAS5BhQ,EAAQ4R,YAAc,SAAoB5B,GACxC,QAAIhQ,EAAQgS,UAAUhC,MAIlBc,EAAGK,MAAMnB,IAiBfhQ,EAAQ+Q,SAAWA,EASnB/Q,EAAQiS,OAAS,SAAiBjB,GAGhC,OAFAD,EAASC,GAEFA,EAAUzM,MAAM,EAAG,K,6BC5N5B,IAAI2N,EAEJA,EAAI,WACF,OAAO5O,KADL,GAIJ,IAEE4O,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACT,MAAOhI,GAEe,iBAAX/J,SAAqB8R,EAAI9R,QAMtCH,EAAOD,QAAUkS,G,6BCjBjBlS,EAAQ8D,WAkCR,SAAoBsO,GAClB,IAAIC,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAC3B,OAAsC,GAA9BE,EAAWC,GAAuB,EAAIA,GArChDxS,EAAQoQ,YA4CR,SAAqBgC,GACnB,IAAIK,EAQAjS,EAPA6R,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GACvBzL,EAAM,IAAI8L,EAThB,SAAqBN,EAAKG,EAAUC,GAClC,OAAsC,GAA9BD,EAAWC,GAAuB,EAAIA,EAQ5BG,CAAYP,EAAKG,EAAUC,IACzCI,EAAU,EAEVjO,EAAM6N,EAAkB,EAAID,EAAW,EAAIA,EAG/C,IAAK/R,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EACxBiS,EAAMI,EAAUT,EAAI/J,WAAW7H,KAAO,GAAKqS,EAAUT,EAAI/J,WAAW7H,EAAI,KAAO,GAAKqS,EAAUT,EAAI/J,WAAW7H,EAAI,KAAO,EAAIqS,EAAUT,EAAI/J,WAAW7H,EAAI,IACzJoG,EAAIgM,KAAaH,GAAO,GAAK,IAC7B7L,EAAIgM,KAAaH,GAAO,EAAI,IAC5B7L,EAAIgM,KAAmB,IAANH,EAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAI/J,WAAW7H,KAAO,EAAIqS,EAAUT,EAAI/J,WAAW7H,EAAI,KAAO,EAC9EoG,EAAIgM,KAAmB,IAANH,GAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAI/J,WAAW7H,KAAO,GAAKqS,EAAUT,EAAI/J,WAAW7H,EAAI,KAAO,EAAIqS,EAAUT,EAAI/J,WAAW7H,EAAI,KAAO,EACvHoG,EAAIgM,KAAaH,GAAO,EAAI,IAC5B7L,EAAIgM,KAAmB,IAANH,GAGnB,OAAO7L,GAxET5G,EAAQ8I,cA2FR,SAAuBgK,GASrB,IARA,IAAIL,EACA9N,EAAMmO,EAAM9P,OACZ+P,EAAapO,EAAM,EAEnBqO,EAAQ,GAIHxS,EAAI,EAAGyS,EAAOtO,EAAMoO,EAAYvS,EAAIyS,EAAMzS,GAH9B,MAInBwS,EAAM5K,KAAK8K,EAAYJ,EAAOtS,EAAGA,EAJd,MAImCyS,EAAOA,EAAOzS,EAJjD,QAQF,IAAfuS,GACFN,EAAMK,EAAMnO,EAAM,GAClBqO,EAAM5K,KAAK+K,EAAOV,GAAO,GAAKU,EAAOV,GAAO,EAAI,IAAQ,OAChC,IAAfM,IACTN,GAAOK,EAAMnO,EAAM,IAAM,GAAKmO,EAAMnO,EAAM,GAC1CqO,EAAM5K,KAAK+K,EAAOV,GAAO,IAAMU,EAAOV,GAAO,EAAI,IAAQU,EAAOV,GAAO,EAAI,IAAQ,MAGrF,OAAOO,EAAMrH,KAAK,KA3GpB,IALA,IAAIwH,EAAS,GACTN,EAAY,GACZH,EAA4B,oBAAfxP,WAA6BA,WAAakJ,MACvD4D,EAAO,mEAEFxP,EAAI,EAAGmE,EAAMqL,EAAKhN,OAAQxC,EAAImE,IAAOnE,EAC5C2S,EAAO3S,GAAKwP,EAAKxP,GACjBqS,EAAU7C,EAAK3H,WAAW7H,IAAMA,EAQlC,SAAS8R,EAAQF,GACf,IAAIzN,EAAMyN,EAAIpP,OAEd,GAAI2B,EAAM,EAAI,EACZ,MAAM,IAAIpB,MAAM,kDAKlB,IAAIgP,EAAWH,EAAI1L,QAAQ,KAG3B,OAFkB,IAAd6L,IAAiBA,EAAW5N,GAEzB,CAAC4N,EADcA,IAAa5N,EAAM,EAAI,EAAI4N,EAAW,GAoD9D,SAASW,EAAYJ,EAAOlN,EAAOC,GAIjC,IAHA,IAAI4M,EALmBW,EAMnBC,EAAS,GAEJ7S,EAAIoF,EAAOpF,EAAIqF,EAAKrF,GAAK,EAChCiS,GAAOK,EAAMtS,IAAM,GAAK,WAAasS,EAAMtS,EAAI,IAAM,EAAI,QAA0B,IAAfsS,EAAMtS,EAAI,IAC9E6S,EAAOjL,KATF+K,GADgBC,EAUOX,IATT,GAAK,IAAQU,EAAOC,GAAO,GAAK,IAAQD,EAAOC,GAAO,EAAI,IAAQD,EAAa,GAANC,IAY9F,OAAOC,EAAO1H,KAAK,IA3ErBkH,EAAU,IAAIxK,WAAW,IAAM,GAC/BwK,EAAU,IAAIxK,WAAW,IAAM,I,6BChB/BrI,EAAQiH,KAAO,SAAUnC,EAAQ0C,EAAQ8L,EAAMC,EAAMC,GACnD,IAAIrJ,EAAGvJ,EACH6S,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBE,GAAS,EACTpT,EAAI8S,EAAOE,EAAS,EAAI,EACxB1S,EAAIwS,GAAQ,EAAI,EAChB/Q,EAAIuC,EAAO0C,EAAShH,GAMxB,IALAA,GAAKM,EACLqJ,EAAI5H,GAAK,IAAMqR,GAAS,EACxBrR,KAAOqR,EACPA,GAASH,EAEFG,EAAQ,EAAGzJ,EAAQ,IAAJA,EAAUrF,EAAO0C,EAAShH,GAAIA,GAAKM,EAAG8S,GAAS,GAMrE,IAJAhT,EAAIuJ,GAAK,IAAMyJ,GAAS,EACxBzJ,KAAOyJ,EACPA,GAASL,EAEFK,EAAQ,EAAGhT,EAAQ,IAAJA,EAAUkE,EAAO0C,EAAShH,GAAIA,GAAKM,EAAG8S,GAAS,GAErE,GAAU,IAANzJ,EACFA,EAAI,EAAIwJ,MACH,IAAIxJ,IAAMuJ,EACf,OAAO9S,EAAIiT,IAAqB3D,KAAd3N,GAAK,EAAI,GAE3B3B,GAAQmI,KAAK+E,IAAI,EAAGyF,GACpBpJ,GAAQwJ,EAGV,OAAQpR,GAAK,EAAI,GAAK3B,EAAImI,KAAK+E,IAAI,EAAG3D,EAAIoJ,IAG5CvT,EAAQsE,MAAQ,SAAUQ,EAAQrD,EAAO+F,EAAQ8L,EAAMC,EAAMC,GAC3D,IAAIrJ,EAAGvJ,EAAGC,EACN4S,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBI,EAAc,KAATP,EAAcxK,KAAK+E,IAAI,GAAI,IAAM/E,KAAK+E,IAAI,GAAI,IAAM,EACzDtN,EAAI8S,EAAO,EAAIE,EAAS,EACxB1S,EAAIwS,EAAO,GAAK,EAChB/Q,EAAId,EAAQ,GAAe,IAAVA,GAAe,EAAIA,EAAQ,EAAI,EAAI,EAqCxD,IApCAA,EAAQsH,KAAKgL,IAAItS,GAEb+E,MAAM/E,IAAUA,IAAUyO,KAC5BtP,EAAI4F,MAAM/E,GAAS,EAAI,EACvB0I,EAAIuJ,IAEJvJ,EAAIpB,KAAK6F,MAAM7F,KAAKiL,IAAIvS,GAASsH,KAAKkL,KAElCxS,GAASZ,EAAIkI,KAAK+E,IAAI,GAAI3D,IAAM,IAClCA,IACAtJ,GAAK,IAILY,GADE0I,EAAIwJ,GAAS,EACNG,EAAKjT,EAELiT,EAAK/K,KAAK+E,IAAI,EAAG,EAAI6F,IAGpB9S,GAAK,IACfsJ,IACAtJ,GAAK,GAGHsJ,EAAIwJ,GAASD,GACf9S,EAAI,EACJuJ,EAAIuJ,GACKvJ,EAAIwJ,GAAS,GACtB/S,GAAKa,EAAQZ,EAAI,GAAKkI,KAAK+E,IAAI,EAAGyF,GAClCpJ,GAAQwJ,IAER/S,EAAIa,EAAQsH,KAAK+E,IAAI,EAAG6F,EAAQ,GAAK5K,KAAK+E,IAAI,EAAGyF,GACjDpJ,EAAI,IAIDoJ,GAAQ,EAAGzO,EAAO0C,EAAShH,GAAS,IAAJI,EAAUJ,GAAKM,EAAGF,GAAK,IAAK2S,GAAQ,GAK3E,IAHApJ,EAAIA,GAAKoJ,EAAO3S,EAChB6S,GAAQF,EAEDE,EAAO,EAAG3O,EAAO0C,EAAShH,GAAS,IAAJ2J,EAAU3J,GAAKM,EAAGqJ,GAAK,IAAKsJ,GAAQ,GAE1E3O,EAAO0C,EAAShH,EAAIM,IAAU,IAAJyB,I,6BCtF5B,IAAI8C,EAAW,GAAGA,SAElBpF,EAAOD,QAAUoM,MAAM1J,SAAW,SAAUkE,GAC1C,MAA6B,kBAAtBvB,EAAS1E,KAAKiG,K,6BCCvB,MAAM,OACJhE,GACE,EAAQ,GAENsR,EAAY,EAAQ,GAiB1B,SAAStD,EAAUuD,EAAYjN,GAC7B,IAAKA,EACH,MAAM,IAAI3D,MAAM,8BAGlB,MAAM6Q,EAAOC,EAAQF,GACfG,EAAU1R,EAAOa,KAAK2Q,EAAKpE,MAGjC,OA+EF,SAAqBjP,EAAMmG,GACZmN,EAAQtT,GAChBkQ,OAAO/J,EAAI7B,YAlFhBkP,CADaH,EAAKrT,KACAmG,GACXtE,EAAOoI,OAAO,CAACsJ,EAASpN,IAoFjC,SAASmN,EAAQF,GACf,IAAIC,EAEJ,GAAIF,EAAUhD,MAAMiD,GAClBC,EAAOF,EAAUhD,MAAMiD,OAClB,KAAID,EAAU/C,MAAMgD,GAGzB,MAAM,IAAI5Q,MAAM,wBAFhB6Q,EAAOF,EAAU/C,MAAMgD,GAKzB,IAAKC,EAAKI,gBACR,MAAM,IAAIjR,MAAM,QAAU4Q,EAAa,2BAGzC,OAAOC,GA3HTpU,EAAUC,EAAOD,QAAU4Q,GACnBa,OAmCR,SAAgB0C,EAAYjN,GAC1B,MAAMkN,EAAOC,EAAQF,GAErB,OAAOvD,EADMwD,EAAKrT,KACK6B,EAAOa,KAAK2Q,EAAK3C,OAAOvK,MArCjDlH,EAAQiR,OAkDR,SAAgBwD,GACV7R,EAAO8B,SAAS+P,KAClBA,EAAcA,EAAYpP,YAG5B,MAAM2K,EAAOyE,EAAYC,UAAU,EAAG,GAGX,iBAF3BD,EAAcA,EAAYC,UAAU,EAAGD,EAAYzR,WAGjDyR,EAAc7R,EAAOa,KAAKgR,IAG5B,MAAML,EAAOC,EAAQrE,GACrB,OAAOpN,EAAOa,KAAK2Q,EAAKnD,OAAOwD,EAAYpP,cA9D7CrF,EAAQ2U,UAyER,SAAmBF,GACb7R,EAAO8B,SAAS+P,KAClBA,EAAcA,EAAYpP,YAI5B,GAAoD,oBAAhDnE,OAAOkB,UAAUiD,SAAS1E,KAAK8T,GACjC,OAAO,EAGT,MAAMzE,EAAOyE,EAAYC,UAAU,EAAG,GAEtC,IAEE,OADaL,EAAQrE,GACTjP,KACZ,MAAO6T,GACP,OAAO,IAxFX5U,EAAQkR,MAAQhQ,OAAO2T,OAAO3T,OAAO4T,KAAKZ,EAAUhD,QACpDlR,EAAQmR,MAAQjQ,OAAO2T,OAAO3T,OAAO4T,KAAKZ,EAAU/C,S,6BCfpD,MAAM4D,EAAO,EAAQ,GAEfC,EAAQ,EAAQ,IAEhBC,EAAS,EAAQ,IAEjBC,EAAS,EAAQ,IAEjB1S,EAAS,EAAQ,IAGjB0R,EAAY,CAAC,CAAC,QAAS,IAAK,GAAI,KAAM,CAAC,QAAS,IAAKc,EAAO,MAAO,CAAC,QAAS,IAAKA,EAAO,YAAa,CAAC,SAAU,IAAKA,EAAO,cAAe,CAAC,SAAU,IAAKC,EAAQ,oBAAqB,CAAC,SAAU,IAAKC,EAAQ,oCAAqC,CAAC,YAAa,IAAKA,EAAQ,qCAAsC,CAAC,YAAa,IAAKA,EAAQ,oCAAqC,CAAC,eAAgB,IAAKA,EAAQ,qCAAsC,CAAC,UAAW,IAAKA,EAAQ,oCAAqC,CAAC,eAAgB,IAAKF,EAAO,8DAA+D,CAAC,YAAa,IAAKA,EAAO,8DAA+D,CAAC,SAAU,IAAKxS,EAAQ,oEAAqE,CAAC,YAAa,IAAKA,EAAQ,qEAAsE,CAAC,YAAa,IAAKA,EAAQ,oEAAqE,CAAC,eAAgB,IAAKA,EAAQ,sEAC1+B0O,EAAQgD,EAAUiB,OAAO,CAACC,EAAMC,KACpCD,EAAKC,EAAO,IAAM,IAAIN,EAAKM,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAC5DD,GACN,IACGjE,EAAQ+C,EAAUiB,OAAO,CAACC,EAAMC,KACpCD,EAAKC,EAAO,IAAMnE,EAAMmE,EAAO,IACxBD,GACN,IACHnV,EAAOD,QAAU,CACfkR,MAAOA,EACPC,MAAOA,I,6BCGTlR,EAAOD,QAzBP,MACE,YAAYe,EAAMiP,EAAMsF,EAAgBC,GACtCjS,KAAKvC,KAAOA,EACZuC,KAAK0M,KAAOA,EACZ1M,KAAKiS,SAAWA,EAEZD,GAAkBC,IACpBjS,KAAKkS,OAASF,EAAeC,IAIjC,OAAOE,GACL,OAAOnS,KAAKkS,OAAO/D,OAAOgE,GAG5B,OAAOA,GACL,OAAOnS,KAAKkS,OAAOvE,OAAOwE,GAG5B,gBACE,OAAOnS,KAAKkS,U,6BCfhB,IAAIE,EAAU,EAAQ,IAAe9S,OA4LrC3C,EAAOD,QA1LP,SAAc2V,GACZ,GAAIA,EAAS3S,QAAU,IACrB,MAAM,IAAIU,UAAU,qBAKtB,IAFA,IAAIkS,EAAW,IAAI1S,WAAW,KAErBoE,EAAI,EAAGA,EAAIsO,EAAS5S,OAAQsE,IACnCsO,EAAStO,GAAK,IAGhB,IAAK,IAAI9G,EAAI,EAAGA,EAAImV,EAAS3S,OAAQxC,IAAK,CACxC,IAAIsK,EAAI6K,EAASE,OAAOrV,GACpBsV,EAAKhL,EAAEzC,WAAW,GAEtB,GAAqB,MAAjBuN,EAASE,GACX,MAAM,IAAIpS,UAAUoH,EAAI,iBAG1B8K,EAASE,GAAMtV,EAGjB,IAAIuV,EAAOJ,EAAS3S,OAChBgT,EAASL,EAASE,OAAO,GACzBI,EAASlN,KAAKiL,IAAI+B,GAAQhN,KAAKiL,IAAI,KAEnCkC,EAAUnN,KAAKiL,IAAI,KAAOjL,KAAKiL,IAAI+B,GAkEvC,SAASI,EAAaC,GACpB,GAAsB,iBAAXA,EACT,MAAM,IAAI1S,UAAU,mBAGtB,GAAsB,IAAlB0S,EAAOpT,OACT,OAAO0S,EAAQ5L,MAAM,GAGvB,IAAIuM,EAAM,EAEV,GAAoB,MAAhBD,EAAOC,GAAX,CAQA,IAHA,IAAIC,EAAS,EACTtT,EAAS,EAENoT,EAAOC,KAASL,GACrBM,IACAD,IAQF,IAJA,IAAIjR,GAAQgR,EAAOpT,OAASqT,GAAOJ,EAAS,IAAM,EAE9CM,EAAO,IAAIrT,WAAWkC,GAEnBgR,EAAOC,IAAM,CAElB,IAAIG,EAAQZ,EAASQ,EAAO/N,WAAWgO,IAEvC,GAAc,MAAVG,EACF,OAKF,IAFA,IAAIhW,EAAI,EAECiW,EAAMrR,EAAO,GAAc,IAAVoR,GAAehW,EAAIwC,KAAoB,IAATyT,EAAYA,IAAOjW,IACzEgW,GAAST,EAAOQ,EAAKE,KAAS,EAC9BF,EAAKE,GAAOD,EAAQ,MAAQ,EAC5BA,EAAQA,EAAQ,MAAQ,EAG1B,GAAc,IAAVA,EACF,MAAM,IAAIjT,MAAM,kBAGlBP,EAASxC,EACT6V,IAIF,GAAoB,MAAhBD,EAAOC,GAAX,CAOA,IAFA,IAAIK,EAAMtR,EAAOpC,EAEV0T,IAAQtR,GAAsB,IAAdmR,EAAKG,IAC1BA,IAGF,IAAIC,EAAMjB,EAAQlS,YAAY8S,GAAUlR,EAAOsR,IAE/CC,EAAIlM,KAAK,EAAM,EAAG6L,GAGlB,IAFA,IAAIhP,EAAIgP,EAEDI,IAAQtR,GACbuR,EAAIrP,KAAOiP,EAAKG,KAGlB,OAAOC,IAaT,MAAO,CACLlF,OAxJF,SAAgB2E,GAKd,IAJIhK,MAAM1J,QAAQ0T,IAAWA,aAAkBlT,cAC7CkT,EAASV,EAAQjS,KAAK2S,KAGnBV,EAAQhR,SAAS0R,GACpB,MAAM,IAAI1S,UAAU,mBAGtB,GAAsB,IAAlB0S,EAAOpT,OACT,MAAO,GAST,IALA,IAAIsT,EAAS,EACTtT,EAAS,EACT4T,EAAS,EACTC,EAAOT,EAAOpT,OAEX4T,IAAWC,GAA2B,IAAnBT,EAAOQ,IAC/BA,IACAN,IAOF,IAHA,IAAIlR,GAAQyR,EAAOD,GAAUV,EAAU,IAAM,EACzCY,EAAM,IAAI5T,WAAWkC,GAElBwR,IAAWC,GAAM,CAKtB,IAJA,IAAIL,EAAQJ,EAAOQ,GAEfpW,EAAI,EAECuW,EAAM3R,EAAO,GAAc,IAAVoR,GAAehW,EAAIwC,KAAoB,IAAT+T,EAAYA,IAAOvW,IACzEgW,GAAS,IAAMM,EAAIC,KAAS,EAC5BD,EAAIC,GAAOP,EAAQT,IAAS,EAC5BS,EAAQA,EAAQT,IAAS,EAG3B,GAAc,IAAVS,EACF,MAAM,IAAIjT,MAAM,kBAGlBP,EAASxC,EACToW,IAMF,IAFA,IAAII,EAAM5R,EAAOpC,EAEVgU,IAAQ5R,GAAqB,IAAb0R,EAAIE,IACzBA,IAMF,IAFA,IAAI9O,EAAM8N,EAAOiB,OAAOX,GAEjBU,EAAM5R,IAAQ4R,EACnB9O,GAAOyN,EAASE,OAAOiB,EAAIE,IAG7B,OAAO9O,GA4FPiO,aAAcA,EACdlF,OAbF,SAAgB/M,GACd,IAAIY,EAASqR,EAAajS,GAE1B,GAAIY,EACF,OAAOA,EAGT,MAAM,IAAIvB,MAAM,WAAawS,EAAO,kB,6BCpLxC,IAAIjR,EAAS,EAAQ,GAEjBlC,EAASkC,EAAOlC,OAEpB,SAASsU,EAAUzG,EAAKC,GACtB,IAAK,IAAI3O,KAAO0O,EACdC,EAAI3O,GAAO0O,EAAI1O,GAYnB,SAASoV,EAAW/T,EAAKC,EAAkBL,GACzC,OAAOJ,EAAOQ,EAAKC,EAAkBL,GATnCJ,EAAOa,MAAQb,EAAOkH,OAASlH,EAAOY,aAAeZ,EAAO8H,gBAC9DzK,EAAOD,QAAU8E,GAGjBoS,EAAUpS,EAAQ9E,GAClBA,EAAQ4C,OAASuU,GAOnBA,EAAW/U,UAAYlB,OAAOY,OAAOc,EAAOR,WAE5C8U,EAAUtU,EAAQuU,GAElBA,EAAW1T,KAAO,SAAUL,EAAKC,EAAkBL,GACjD,GAAmB,iBAARI,EACT,MAAM,IAAIM,UAAU,iCAGtB,OAAOd,EAAOQ,EAAKC,EAAkBL,IAGvCmU,EAAWrN,MAAQ,SAAU1E,EAAMqF,EAAMtG,GACvC,GAAoB,iBAATiB,EACT,MAAM,IAAI1B,UAAU,6BAGtB,IAAIwD,EAAMtE,EAAOwC,GAYjB,YAVarB,IAAT0G,EACsB,iBAAbtG,EACT+C,EAAIuD,KAAKA,EAAMtG,GAEf+C,EAAIuD,KAAKA,GAGXvD,EAAIuD,KAAK,GAGJvD,GAGTiQ,EAAW3T,YAAc,SAAU4B,GACjC,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,6BAGtB,OAAOd,EAAOwC,IAGhB+R,EAAWzM,gBAAkB,SAAUtF,GACrC,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,6BAGtB,OAAOoB,EAAO+E,WAAWzE,K,6BCtE3B,MAAM,OACJxC,GACE,EAAQ,GAEZ3C,EAAOD,QAAU,SAAgBuV,GAC/B,MAAO,CACL9D,OAAO2F,GACgB,iBAAVA,EACFxU,EAAOa,KAAK2T,GAAO/R,SAAS,OAG9B+R,EAAM/R,SAAS,OAGxB,OAAO+R,GACL,IAAK,MAAMC,KAAQD,EACjB,GAAI7B,EAAS7O,QAAQ2Q,GAAQ,EAC3B,MAAM,IAAI9T,MAAM,4BAIpB,OAAOX,EAAOa,KAAK2T,EAAO,W,6BCAhC,SAAS3F,EAAO3M,EAAQyQ,GACtB,MAAMvS,EAAS8B,EAAOhB,WAChBwT,EAAO,IAAIpU,WAAW4B,GACtByS,EAAUhC,EAAS7O,QAAQ,OAAS6O,EAASvS,OAAS,EAExDuU,IACFhC,EAAWA,EAASb,UAAU,EAAGa,EAASvS,OAAS,IAGrD,IAAIwU,EAAO,EACP/V,EAAQ,EACR4R,EAAS,GAEb,IAAK,IAAI7S,EAAI,EAAGA,EAAIwC,EAAQxC,IAI1B,IAHAiB,EAAQA,GAAS,EAAI6V,EAAK9W,GAC1BgX,GAAQ,EAEDA,GAAQ,GACbnE,GAAUkC,EAAS9T,IAAU+V,EAAO,EAAI,IACxCA,GAAQ,EAQZ,GAJIA,EAAO,IACTnE,GAAUkC,EAAS9T,GAAS,EAAI+V,EAAO,KAGrCD,EACF,KAAOlE,EAAOrQ,OAAS,GAAM,GAC3BqQ,GAAU,IAId,OAAOA,EAGTpT,EAAOD,QAAU,SAAgBuV,GAC/B,MAAO,CACL9D,OAAO2F,GAEI3F,EADY,iBAAV2F,EACKlU,WAAWO,KAAK2T,GAGlBA,EAH0B7B,GAM1C,OAAO6B,GACL,IAAK,MAAMC,KAAQD,EACjB,GAAI7B,EAAS7O,QAAQ2Q,GAAQ,EAC3B,MAAM,IAAI9T,MAAM,4BAIpB,OA1EN,SAAgB6T,EAAO7B,GAErB,MAAMvS,GADNoU,EAAQA,EAAM9G,QAAQ,IAAImH,OAAO,IAAK,KAAM,KACvBzU,OACrB,IAAIwU,EAAO,EACP/V,EAAQ,EACRiW,EAAQ,EACZ,MAAMrE,EAAS,IAAInQ,WAAoB,EAATF,EAAa,EAAI,GAE/C,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,EAAQxC,IAC1BiB,EAAQA,GAAS,EAAI8T,EAAS7O,QAAQ0Q,EAAM5W,IAC5CgX,GAAQ,EAEJA,GAAQ,IACVnE,EAAOqE,KAAWjW,IAAU+V,EAAO,EAAI,IACvCA,GAAQ,GAIZ,OAAOnE,EAAOvO,OAwDHmM,CAAOmG,EAAO7B,O,6BC1E3B,MAAM,OACJ3S,GACE,EAAQ,GAEZ3C,EAAOD,QAAU,SAAgBuV,GAM/B,MAAMgC,EAAUhC,EAAS7O,QAAQ,MAAQ,EACnCiR,EAAMpC,EAAS7O,QAAQ,MAAQ,GAAK6O,EAAS7O,QAAQ,MAAQ,EACnE,MAAO,CACL,OAAO0Q,GACL,IAAI/D,EAAS,GAGXA,EADmB,iBAAV+D,EACAxU,EAAOa,KAAK2T,GAAO/R,SAAS,UAE5B+R,EAAM/R,SAAS,UAGtBsS,IACFtE,EAASA,EAAO/C,QAAQ,MAAO,KAAKA,QAAQ,MAAO,MAGrD,MAAMsH,EAAMvE,EAAO3M,QAAQ,KAM3B,OAJIkR,EAAM,IAAML,IACdlE,EAASA,EAAOqB,UAAU,EAAGkD,IAGxBvE,GAGT,OAAO+D,GACL,IAAK,MAAMC,KAAQD,EACjB,GAAI7B,EAAS7O,QAAQ2Q,GAAQ,EAC3B,MAAM,IAAI9T,MAAM,4BAIpB,OAAOX,EAAOa,KAAK2T,EAAO,c,6BC1ChCnX,EAAOD,QAAU,CACfyR,OAAQ,EAAQ,IAChBR,OAAQ,EAAQ,IAChB4G,eAAgB,EAAQ,M,6BCH1B5X,EAAOD,QAMP,SAASyR,EAAO2B,EAAK7G,EAAK/E,GACxB+E,EAAMA,GAAO,GAEb,IAAIuL,EADJtQ,EAASA,GAAU,EAGnB,KAAO4L,GAAO2E,GACZxL,EAAI/E,KAAkB,IAAN4L,EAXV,IAYNA,GAAO,IAGT,MAbW,IAaJA,GACL7G,EAAI/E,KAAkB,IAAN4L,EAhBV,IAiBNA,KAAS,EAKX,OAFA7G,EAAI/E,GAAgB,EAAN4L,EACd3B,EAAOhF,MAAQjF,EAASsQ,EAAY,EAC7BvL,GAtBT,IAGIwL,EAAMhP,KAAK+E,IAAI,EAAG,K,6BCJtB7N,EAAOD,QAIP,SAASiH,EAAKC,EAAKM,GACjB,IAIInB,EAJA4C,EAAM,EAEN+O,EAAQ,EACRC,EAFAzQ,EAASA,GAAU,EAInB/G,EAAIyG,EAAIlE,OAEZ,EAAG,CACD,GAAIiV,GAAWxX,EAEb,MADAwG,EAAKwF,MAAQ,EACP,IAAIxJ,WAAW,2BAGvBoD,EAAIa,EAAI+Q,KACRhP,GAAO+O,EAAQ,IAjBR,IAiBc3R,IAAa2R,GAjB3B,IAiBoC3R,GAAY0C,KAAK+E,IAAI,EAAGkK,GACnEA,GAAS,QACF3R,GApBD,KAuBR,OADAY,EAAKwF,MAAQwL,EAAUzQ,EAChByB,I,6BCxBT,IAAIiP,EAAKnP,KAAK+E,IAAI,EAAG,GACjBqK,EAAKpP,KAAK+E,IAAI,EAAG,IACjBsK,EAAKrP,KAAK+E,IAAI,EAAG,IACjBuK,EAAKtP,KAAK+E,IAAI,EAAG,IACjBwK,EAAKvP,KAAK+E,IAAI,EAAG,IACjByK,EAAKxP,KAAK+E,IAAI,EAAG,IACjB0K,EAAKzP,KAAK+E,IAAI,EAAG,IACjB2K,EAAK1P,KAAK+E,IAAI,EAAG,IACjB4K,EAAK3P,KAAK+E,IAAI,EAAG,IAErB7N,EAAOD,QAAU,SAAUyB,GACzB,OAAOA,EAAQyW,EAAK,EAAIzW,EAAQ0W,EAAK,EAAI1W,EAAQ2W,EAAK,EAAI3W,EAAQ4W,EAAK,EAAI5W,EAAQ6W,EAAK,EAAI7W,EAAQ8W,EAAK,EAAI9W,EAAQ+W,EAAK,EAAI/W,EAAQgX,EAAK,EAAIhX,EAAQiX,EAAK,EAAI,K,6BCTlK1Y,EAAQkR,MAAQhQ,OAAO2T,OAAO,CAC5B,SAAc,EACd,KAAc,GACd,WAAc,GACd,WAAc,GACd,eAAgB,GAChB,WAAc,GACd,WAAc,GACd,WAAc,GACd,WAAc,GACd,YAAc,GACd,YAAc,GACd,aAAc,GACd,aAAc,GACd,aAAc,GACd,aAAc,GACd,cAAe,GACf,aAAe,GACf,IAAe,IACf,IAAe,IACf,YAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,YAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,aAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,KACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,iBAAkB,MAClB,iBAAkB,MAClB,iBAAkB,MAClB,iBAAkB,QAGpB7U,EAAQmR,MAAQjQ,OAAO2T,OAAO,CAC5B8D,EAAK,WAGL,GAAM,OACN,GAAM,WACN,GAAM,WACN,GAAM,eACN,GAAM,WACN,GAAM,WACN,GAAM,WACN,GAAM,WACN,GAAM,YACN,GAAM,YACN,GAAM,aACN,GAAM,aACN,GAAM,aACN,GAAM,aAEN,GAAM,cACN,GAAM,aAEN,IAAM,MACN,IAAM,MAGN,MAAQ,YACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,YACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cAGR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,KAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,iBACR,MAAQ,iBACR,MAAQ,iBACR,MAAQ,mBAGV3Y,EAAQoR,eAAiBlQ,OAAO2T,OAAO,CACrC,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GAEN,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,KAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ", "file": "index.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Multihashes\"] = factory();\n\telse\n\t\troot[\"Multihashes\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 1);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "/**\n * Multihash implementation in JavaScript.\n *\n * @module multihash\n */\n'use strict'\n\nconst { <PERSON>uff<PERSON> } = require('buffer')\nconst multibase = require('multibase')\nconst varint = require('varint')\nconst cs = require('./constants')\n\nexports.names = cs.names\nexports.codes = cs.codes\nexports.defaultLengths = cs.defaultLengths\n\n/**\n * Convert the given multihash to a hex encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\nexports.toHexString = function toHexString (hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer')\n  }\n\n  return hash.toString('hex')\n}\n\n/**\n * Convert the given hex encoded string to a multihash.\n *\n * @param {string} hash\n * @returns {Buffer}\n */\nexports.fromHexString = function fromHexString (hash) {\n  return Buffer.from(hash, 'hex')\n}\n\n/**\n * Convert the given multihash to a base58 encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\nexports.toB58String = function toB58String (hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer')\n  }\n\n  return multibase.encode('base58btc', hash).toString().slice(1)\n}\n\n/**\n * Convert the given base58 encoded string to a multihash.\n *\n * @param {string|Buffer} hash\n * @returns {Buffer}\n */\nexports.fromB58String = function fromB58String (hash) {\n  let encoded = hash\n  if (Buffer.isBuffer(hash)) {\n    encoded = hash.toString()\n  }\n\n  return multibase.decode('z' + encoded)\n}\n\n/**\n * Decode a hash from the given multihash.\n *\n * @param {Buffer} buf\n * @returns {{code: number, name: string, length: number, digest: Buffer}} result\n */\nexports.decode = function decode (buf) {\n  if (!(Buffer.isBuffer(buf))) {\n    throw new Error('multihash must be a Buffer')\n  }\n\n  if (buf.length < 2) {\n    throw new Error('multihash too short. must be > 2 bytes.')\n  }\n\n  const code = varint.decode(buf)\n  if (!exports.isValidCode(code)) {\n    throw new Error(`multihash unknown function code: 0x${code.toString(16)}`)\n  }\n  buf = buf.slice(varint.decode.bytes)\n\n  const len = varint.decode(buf)\n  if (len < 0) {\n    throw new Error(`multihash invalid length: ${len}`)\n  }\n  buf = buf.slice(varint.decode.bytes)\n\n  if (buf.length !== len) {\n    throw new Error(`multihash length inconsistent: 0x${buf.toString('hex')}`)\n  }\n\n  return {\n    code: code,\n    name: cs.codes[code],\n    length: len,\n    digest: buf\n  }\n}\n\n/**\n *  Encode a hash digest along with the specified function code.\n *\n * > **Note:** the length is derived from the length of the digest itself.\n *\n * @param {Buffer} digest\n * @param {string|number} code\n * @param {number} [length]\n * @returns {Buffer}\n */\nexports.encode = function encode (digest, code, length) {\n  if (!digest || code === undefined) {\n    throw new Error('multihash encode requires at least two args: digest, code')\n  }\n\n  // ensure it's a hashfunction code.\n  const hashfn = exports.coerceCode(code)\n\n  if (!(Buffer.isBuffer(digest))) {\n    throw new Error('digest should be a Buffer')\n  }\n\n  if (length == null) {\n    length = digest.length\n  }\n\n  if (length && digest.length !== length) {\n    throw new Error('digest length should be equal to specified length.')\n  }\n\n  return Buffer.concat([\n    Buffer.from(varint.encode(hashfn)),\n    Buffer.from(varint.encode(length)),\n    digest\n  ])\n}\n\n/**\n * Converts a hash function name into the matching code.\n * If passed a number it will return the number if it's a valid code.\n * @param {string|number} name\n * @returns {number}\n */\nexports.coerceCode = function coerceCode (name) {\n  let code = name\n\n  if (typeof name === 'string') {\n    if (cs.names[name] === undefined) {\n      throw new Error(`Unrecognized hash function named: ${name}`)\n    }\n    code = cs.names[name]\n  }\n\n  if (typeof code !== 'number') {\n    throw new Error(`Hash function code should be a number. Got: ${code}`)\n  }\n\n  if (cs.codes[code] === undefined && !exports.isAppCode(code)) {\n    throw new Error(`Unrecognized function code: ${code}`)\n  }\n\n  return code\n}\n\n/**\n * Checks wether a code is part of the app range\n *\n * @param {number} code\n * @returns {boolean}\n */\nexports.isAppCode = function appCode (code) {\n  return code > 0 && code < 0x10\n}\n\n/**\n * Checks whether a multihash code is valid.\n *\n * @param {number} code\n * @returns {boolean}\n */\nexports.isValidCode = function validCode (code) {\n  if (exports.isAppCode(code)) {\n    return true\n  }\n\n  if (cs.codes[code]) {\n    return true\n  }\n\n  return false\n}\n\n/**\n * Check if the given buffer is a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\nfunction validate (multihash) {\n  exports.decode(multihash) // throws if bad.\n}\nexports.validate = validate\n\n/**\n * Returns a prefix from a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\nexports.prefix = function prefix (multihash) {\n  validate(multihash)\n\n  return multihash.slice(0, 2)\n}\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "/**\n * Implementation of the [multibase](https://github.com/multiformats/multibase) specification.\n * @module Multibase\n */\n'use strict';\n\nconst {\n  Buffer\n} = require('buffer');\n\nconst constants = require('./constants');\n\nexports = module.exports = multibase;\nexports.encode = encode;\nexports.decode = decode;\nexports.isEncoded = isEncoded;\nexports.names = Object.freeze(Object.keys(constants.names));\nexports.codes = Object.freeze(Object.keys(constants.codes));\n/**\n * Create a new buffer with the multibase varint+code.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be prefixed with multibase.\n * @memberof Multibase\n * @returns {Buffer}\n */\n\nfunction multibase(nameOrCode, buf) {\n  if (!buf) {\n    throw new Error('requires an encoded buffer');\n  }\n\n  const base = getBase(nameOrCode);\n  const codeBuf = Buffer.from(base.code);\n  const name = base.name;\n  validEncode(name, buf);\n  return Buffer.concat([codeBuf, buf]);\n}\n/**\n * Encode data with the specified base and add the multibase prefix.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be encoded.\n * @returns {Buffer}\n * @memberof Multibase\n */\n\n\nfunction encode(nameOrCode, buf) {\n  const base = getBase(nameOrCode);\n  const name = base.name;\n  return multibase(name, Buffer.from(base.encode(buf)));\n}\n/**\n * Takes a buffer or string encoded with multibase header, decodes it and\n * returns the decoded buffer\n *\n * @param {Buffer|string} bufOrString\n * @returns {Buffer}\n * @memberof Multibase\n *\n */\n\n\nfunction decode(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  }\n\n  const code = bufOrString.substring(0, 1);\n  bufOrString = bufOrString.substring(1, bufOrString.length);\n\n  if (typeof bufOrString === 'string') {\n    bufOrString = Buffer.from(bufOrString);\n  }\n\n  const base = getBase(code);\n  return Buffer.from(base.decode(bufOrString.toString()));\n}\n/**\n * Is the given data multibase encoded?\n *\n * @param {Buffer|string} bufOrString\n * @returns {boolean}\n * @memberof Multibase\n */\n\n\nfunction isEncoded(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  } // Ensure bufOrString is a string\n\n\n  if (Object.prototype.toString.call(bufOrString) !== '[object String]') {\n    return false;\n  }\n\n  const code = bufOrString.substring(0, 1);\n\n  try {\n    const base = getBase(code);\n    return base.name;\n  } catch (err) {\n    return false;\n  }\n}\n/**\n * @param {string} name\n * @param {Buffer} buf\n * @private\n * @returns {undefined}\n */\n\n\nfunction validEncode(name, buf) {\n  const base = getBase(name);\n  base.decode(buf.toString());\n}\n\nfunction getBase(nameOrCode) {\n  let base;\n\n  if (constants.names[nameOrCode]) {\n    base = constants.names[nameOrCode];\n  } else if (constants.codes[nameOrCode]) {\n    base = constants.codes[nameOrCode];\n  } else {\n    throw new Error('Unsupported encoding');\n  }\n\n  if (!base.isImplemented()) {\n    throw new Error('Base ' + nameOrCode + ' is not implemented yet');\n  }\n\n  return base;\n}", "'use strict';\n\nconst Base = require('./base.js');\n\nconst baseX = require('base-x');\n\nconst base16 = require('./base16');\n\nconst base32 = require('./base32');\n\nconst base64 = require('./base64'); // name, code, implementation, alphabet\n\n\nconst constants = [['base1', '1', '', '1'], ['base2', '0', baseX, '01'], ['base8', '7', baseX, '01234567'], ['base10', '9', baseX, '0123456789'], ['base16', 'f', base16, '0123456789abcdef'], ['base32', 'b', base32, 'abcdefghijklmnopqrstuvwxyz234567'], ['base32pad', 'c', base32, 'abcdefghijklmnopqrstuvwxyz234567='], ['base32hex', 'v', base32, '0123456789abcdefghijklmnopqrstuv'], ['base32hexpad', 't', base32, '0123456789abcdefghijklmnopqrstuv='], ['base32z', 'h', base32, 'ybndrfg8ejkmcpqxot1uwisza345h769'], ['base58flickr', 'Z', baseX, '**********************************************************'], ['base58btc', 'z', baseX, '**********************************************************'], ['base64', 'm', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'], ['base64pad', 'M', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='], ['base64url', 'u', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'], ['base64urlpad', 'U', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=']];\nconst names = constants.reduce((prev, tupple) => {\n  prev[tupple[0]] = new Base(tupple[0], tupple[1], tupple[2], tupple[3]);\n  return prev;\n}, {});\nconst codes = constants.reduce((prev, tupple) => {\n  prev[tupple[1]] = names[tupple[0]];\n  return prev;\n}, {});\nmodule.exports = {\n  names: names,\n  codes: codes\n};", "'use strict';\n\nclass Base {\n  constructor(name, code, implementation, alphabet) {\n    this.name = name;\n    this.code = code;\n    this.alphabet = alphabet;\n\n    if (implementation && alphabet) {\n      this.engine = implementation(alphabet);\n    }\n  }\n\n  encode(stringOrBuffer) {\n    return this.engine.encode(stringOrBuffer);\n  }\n\n  decode(stringOrBuffer) {\n    return this.engine.decode(stringOrBuffer);\n  }\n\n  isImplemented() {\n    return this.engine;\n  }\n\n}\n\nmodule.exports = Base;", "'use strict'; // base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\n// @ts-ignore\n\nvar _Buffer = require('safe-buffer').Buffer;\n\nfunction base(ALPHABET) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n\n  var BASE_MAP = new Uint8Array(256);\n\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n\n    BASE_MAP[xc] = i;\n  }\n\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256); // log(BASE) / log(256), rounded up\n\n  var iFACTOR = Math.log(256) / Math.log(BASE); // log(256) / log(BASE), rounded up\n\n  function encode(source) {\n    if (Array.isArray(source) || source instanceof Uint8Array) {\n      source = _Buffer.from(source);\n    }\n\n    if (!_Buffer.isBuffer(source)) {\n      throw new TypeError('Expected Buffer');\n    }\n\n    if (source.length === 0) {\n      return '';\n    } // Skip & count leading zeroes.\n\n\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    } // Allocate enough space in big-endian base58 representation.\n\n\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size); // Process the bytes.\n\n    while (pbegin !== pend) {\n      var carry = source[pbegin]; // Apply \"b58 = b58 * 256 + ch\".\n\n      var i = 0;\n\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      pbegin++;\n    } // Skip leading zeroes in base58 result.\n\n\n    var it2 = size - length;\n\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    } // Translate the result into a string.\n\n\n    var str = LEADER.repeat(zeroes);\n\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n\n    return str;\n  }\n\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n\n    if (source.length === 0) {\n      return _Buffer.alloc(0);\n    }\n\n    var psz = 0; // Skip leading spaces.\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip and count leading '1's.\n\n\n    var zeroes = 0;\n    var length = 0;\n\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    } // Allocate enough space in big-endian base256 representation.\n\n\n    var size = (source.length - psz) * FACTOR + 1 >>> 0; // log(58) / log(256), rounded up.\n\n    var b256 = new Uint8Array(size); // Process the characters.\n\n    while (source[psz]) {\n      // Decode character\n      var carry = BASE_MAP[source.charCodeAt(psz)]; // Invalid character\n\n      if (carry === 255) {\n        return;\n      }\n\n      var i = 0;\n\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n\n      length = i;\n      psz++;\n    } // Skip trailing spaces.\n\n\n    if (source[psz] === ' ') {\n      return;\n    } // Skip leading zeroes in b256.\n\n\n    var it4 = size - length;\n\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n\n    var vch = _Buffer.allocUnsafe(zeroes + (size - it4));\n\n    vch.fill(0x00, 0, zeroes);\n    var j = zeroes;\n\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n\n    return vch;\n  }\n\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n\n    if (buffer) {\n      return buffer;\n    }\n\n    throw new Error('Non-base' + BASE + ' character');\n  }\n\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\n\nmodule.exports = base;", "\"use strict\";\n\n/*! safe-buffer. MIT License. Feross Aboukhadi<PERSON> <https://feross.org/opensource> */\n\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\n\nvar Buffer = buffer.Buffer; // alternative to using Object.keys for old browsers\n\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\n\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\n\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype); // Copy static methods from Buffer\n\ncopyProps(Buffer, SafeBuffer);\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n\n  return Buffer(arg, encodingOrOffset, length);\n};\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  var buf = Buffer(size);\n\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n\n  return buf;\n};\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return Buffer(size);\n};\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return buffer.SlowBuffer(size);\n};", "'use strict';\n\nconst {\n  <PERSON><PERSON><PERSON>\n} = require('buffer');\n\nmodule.exports = function base16(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return Buffer.from(input).toString('hex');\n      }\n\n      return input.toString('hex');\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base16 character');\n        }\n      }\n\n      return Buffer.from(input, 'hex');\n    }\n\n  };\n};", "'use strict';\n\nfunction decode(input, alphabet) {\n  input = input.replace(new RegExp('=', 'g'), '');\n  const length = input.length;\n  let bits = 0;\n  let value = 0;\n  let index = 0;\n  const output = new Uint8Array(length * 5 / 8 | 0);\n\n  for (let i = 0; i < length; i++) {\n    value = value << 5 | alphabet.indexOf(input[i]);\n    bits += 5;\n\n    if (bits >= 8) {\n      output[index++] = value >>> bits - 8 & 255;\n      bits -= 8;\n    }\n  }\n\n  return output.buffer;\n}\n\nfunction encode(buffer, alphabet) {\n  const length = buffer.byteLength;\n  const view = new Uint8Array(buffer);\n  const padding = alphabet.indexOf('=') === alphabet.length - 1;\n\n  if (padding) {\n    alphabet = alphabet.substring(0, alphabet.length - 1);\n  }\n\n  let bits = 0;\n  let value = 0;\n  let output = '';\n\n  for (let i = 0; i < length; i++) {\n    value = value << 8 | view[i];\n    bits += 8;\n\n    while (bits >= 5) {\n      output += alphabet[value >>> bits - 5 & 31];\n      bits -= 5;\n    }\n  }\n\n  if (bits > 0) {\n    output += alphabet[value << 5 - bits & 31];\n  }\n\n  if (padding) {\n    while (output.length % 8 !== 0) {\n      output += '=';\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = function base32(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return encode(Uint8Array.from(input), alphabet);\n      }\n\n      return encode(input, alphabet);\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base32 character');\n        }\n      }\n\n      return decode(input, alphabet);\n    }\n\n  };\n};", "'use strict';\n\nconst {\n  <PERSON><PERSON><PERSON>\n} = require('buffer');\n\nmodule.exports = function base64(alphabet) {\n  // The alphabet is only used to know:\n  //   1. If padding is enabled (must contain '=')\n  //   2. If the output must be url-safe (must contain '-' and '_')\n  //   3. If the input of the output function is valid\n  // The alphabets from RFC 4648 are always used.\n  const padding = alphabet.indexOf('=') > -1;\n  const url = alphabet.indexOf('-') > -1 && alphabet.indexOf('_') > -1;\n  return {\n    encode(input) {\n      let output = '';\n\n      if (typeof input === 'string') {\n        output = Buffer.from(input).toString('base64');\n      } else {\n        output = input.toString('base64');\n      }\n\n      if (url) {\n        output = output.replace(/\\+/g, '-').replace(/\\//g, '_');\n      }\n\n      const pad = output.indexOf('=');\n\n      if (pad > 0 && !padding) {\n        output = output.substring(0, pad);\n      }\n\n      return output;\n    },\n\n    decode(input) {\n      for (const char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base64 character');\n        }\n      }\n\n      return Buffer.from(input, 'base64');\n    }\n\n  };\n};", "\"use strict\";\n\nmodule.exports = {\n  encode: require('./encode.js'),\n  decode: require('./decode.js'),\n  encodingLength: require('./length.js')\n};", "\"use strict\";\n\nmodule.exports = encode;\nvar MSB = 0x80,\n    REST = 0x7F,\n    MSBALL = ~REST,\n    INT = Math.pow(2, 31);\n\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n\n  while (num >= INT) {\n    out[offset++] = num & 0xFF | MSB;\n    num /= 128;\n  }\n\n  while (num & MSBALL) {\n    out[offset++] = num & 0xFF | MSB;\n    num >>>= 7;\n  }\n\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}", "\"use strict\";\n\nmodule.exports = read;\nvar MSB = 0x80,\n    REST = 0x7F;\n\nfunction read(buf, offset) {\n  var res = 0,\n      offset = offset || 0,\n      shift = 0,\n      counter = offset,\n      b,\n      l = buf.length;\n\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST) << shift : (b & REST) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB);\n\n  read.bytes = counter - offset;\n  return res;\n}", "\"use strict\";\n\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\n\nmodule.exports = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};", "/* eslint quote-props: off */\n/* eslint key-spacing: off */\n'use strict'\n\nexports.names = Object.freeze({\n  'identity':   0x0,\n  'sha1':       0x11,\n  'sha2-256':   0x12,\n  'sha2-512':   0x13,\n  'dbl-sha2-256': 0x56,\n  'sha3-224':   0x17,\n  'sha3-256':   0x16,\n  'sha3-384':   0x15,\n  'sha3-512':   0x14,\n  'shake-128':  0x18,\n  'shake-256':  0x19,\n  'keccak-224': 0x1A,\n  'keccak-256': 0x1B,\n  'keccak-384': 0x1C,\n  'keccak-512': 0x1D,\n  'murmur3-128': 0x22,\n  'murmur3-32':  0x23,\n  'md4':         0xd4,\n  'md5':         0xd5,\n  'blake2b-8':   0xb201,\n  'blake2b-16':  0xb202,\n  'blake2b-24':  0xb203,\n  'blake2b-32':  0xb204,\n  'blake2b-40':  0xb205,\n  'blake2b-48':  0xb206,\n  'blake2b-56':  0xb207,\n  'blake2b-64':  0xb208,\n  'blake2b-72':  0xb209,\n  'blake2b-80':  0xb20a,\n  'blake2b-88':  0xb20b,\n  'blake2b-96':  0xb20c,\n  'blake2b-104': 0xb20d,\n  'blake2b-112': 0xb20e,\n  'blake2b-120': 0xb20f,\n  'blake2b-128': 0xb210,\n  'blake2b-136': 0xb211,\n  'blake2b-144': 0xb212,\n  'blake2b-152': 0xb213,\n  'blake2b-160': 0xb214,\n  'blake2b-168': 0xb215,\n  'blake2b-176': 0xb216,\n  'blake2b-184': 0xb217,\n  'blake2b-192': 0xb218,\n  'blake2b-200': 0xb219,\n  'blake2b-208': 0xb21a,\n  'blake2b-216': 0xb21b,\n  'blake2b-224': 0xb21c,\n  'blake2b-232': 0xb21d,\n  'blake2b-240': 0xb21e,\n  'blake2b-248': 0xb21f,\n  'blake2b-256': 0xb220,\n  'blake2b-264': 0xb221,\n  'blake2b-272': 0xb222,\n  'blake2b-280': 0xb223,\n  'blake2b-288': 0xb224,\n  'blake2b-296': 0xb225,\n  'blake2b-304': 0xb226,\n  'blake2b-312': 0xb227,\n  'blake2b-320': 0xb228,\n  'blake2b-328': 0xb229,\n  'blake2b-336': 0xb22a,\n  'blake2b-344': 0xb22b,\n  'blake2b-352': 0xb22c,\n  'blake2b-360': 0xb22d,\n  'blake2b-368': 0xb22e,\n  'blake2b-376': 0xb22f,\n  'blake2b-384': 0xb230,\n  'blake2b-392': 0xb231,\n  'blake2b-400': 0xb232,\n  'blake2b-408': 0xb233,\n  'blake2b-416': 0xb234,\n  'blake2b-424': 0xb235,\n  'blake2b-432': 0xb236,\n  'blake2b-440': 0xb237,\n  'blake2b-448': 0xb238,\n  'blake2b-456': 0xb239,\n  'blake2b-464': 0xb23a,\n  'blake2b-472': 0xb23b,\n  'blake2b-480': 0xb23c,\n  'blake2b-488': 0xb23d,\n  'blake2b-496': 0xb23e,\n  'blake2b-504': 0xb23f,\n  'blake2b-512': 0xb240,\n  'blake2s-8':   0xb241,\n  'blake2s-16':  0xb242,\n  'blake2s-24':  0xb243,\n  'blake2s-32':  0xb244,\n  'blake2s-40':  0xb245,\n  'blake2s-48':  0xb246,\n  'blake2s-56':  0xb247,\n  'blake2s-64':  0xb248,\n  'blake2s-72':  0xb249,\n  'blake2s-80':  0xb24a,\n  'blake2s-88':  0xb24b,\n  'blake2s-96':  0xb24c,\n  'blake2s-104': 0xb24d,\n  'blake2s-112': 0xb24e,\n  'blake2s-120': 0xb24f,\n  'blake2s-128': 0xb250,\n  'blake2s-136': 0xb251,\n  'blake2s-144': 0xb252,\n  'blake2s-152': 0xb253,\n  'blake2s-160': 0xb254,\n  'blake2s-168': 0xb255,\n  'blake2s-176': 0xb256,\n  'blake2s-184': 0xb257,\n  'blake2s-192': 0xb258,\n  'blake2s-200': 0xb259,\n  'blake2s-208': 0xb25a,\n  'blake2s-216': 0xb25b,\n  'blake2s-224': 0xb25c,\n  'blake2s-232': 0xb25d,\n  'blake2s-240': 0xb25e,\n  'blake2s-248': 0xb25f,\n  'blake2s-256': 0xb260,\n  'Skein256-8': 0xb301,\n  'Skein256-16': 0xb302,\n  'Skein256-24': 0xb303,\n  'Skein256-32': 0xb304,\n  'Skein256-40': 0xb305,\n  'Skein256-48': 0xb306,\n  'Skein256-56': 0xb307,\n  'Skein256-64': 0xb308,\n  'Skein256-72': 0xb309,\n  'Skein256-80': 0xb30a,\n  'Skein256-88': 0xb30b,\n  'Skein256-96': 0xb30c,\n  'Skein256-104': 0xb30d,\n  'Skein256-112': 0xb30e,\n  'Skein256-120': 0xb30f,\n  'Skein256-128': 0xb310,\n  'Skein256-136': 0xb311,\n  'Skein256-144': 0xb312,\n  'Skein256-152': 0xb313,\n  'Skein256-160': 0xb314,\n  'Skein256-168': 0xb315,\n  'Skein256-176': 0xb316,\n  'Skein256-184': 0xb317,\n  'Skein256-192': 0xb318,\n  'Skein256-200': 0xb319,\n  'Skein256-208': 0xb31a,\n  'Skein256-216': 0xb31b,\n  'Skein256-224': 0xb31c,\n  'Skein256-232': 0xb31d,\n  'Skein256-240': 0xb31e,\n  'Skein256-248': 0xb31f,\n  'Skein256-256': 0xb320,\n  'Skein512-8': 0xb321,\n  'Skein512-16': 0xb322,\n  'Skein512-24': 0xb323,\n  'Skein512-32': 0xb324,\n  'Skein512-40': 0xb325,\n  'Skein512-48': 0xb326,\n  'Skein512-56': 0xb327,\n  'Skein512-64': 0xb328,\n  'Skein512-72': 0xb329,\n  'Skein512-80': 0xb32a,\n  'Skein512-88': 0xb32b,\n  'Skein512-96': 0xb32c,\n  'Skein512-104': 0xb32d,\n  'Skein512-112': 0xb32e,\n  'Skein512-120': 0xb32f,\n  'Skein512-128': 0xb330,\n  'Skein512-136': 0xb331,\n  'Skein512-144': 0xb332,\n  'Skein512-152': 0xb333,\n  'Skein512-160': 0xb334,\n  'Skein512-168': 0xb335,\n  'Skein512-176': 0xb336,\n  'Skein512-184': 0xb337,\n  'Skein512-192': 0xb338,\n  'Skein512-200': 0xb339,\n  'Skein512-208': 0xb33a,\n  'Skein512-216': 0xb33b,\n  'Skein512-224': 0xb33c,\n  'Skein512-232': 0xb33d,\n  'Skein512-240': 0xb33e,\n  'Skein512-248': 0xb33f,\n  'Skein512-256': 0xb340,\n  'Skein512-264': 0xb341,\n  'Skein512-272': 0xb342,\n  'Skein512-280': 0xb343,\n  'Skein512-288': 0xb344,\n  'Skein512-296': 0xb345,\n  'Skein512-304': 0xb346,\n  'Skein512-312': 0xb347,\n  'Skein512-320': 0xb348,\n  'Skein512-328': 0xb349,\n  'Skein512-336': 0xb34a,\n  'Skein512-344': 0xb34b,\n  'Skein512-352': 0xb34c,\n  'Skein512-360': 0xb34d,\n  'Skein512-368': 0xb34e,\n  'Skein512-376': 0xb34f,\n  'Skein512-384': 0xb350,\n  'Skein512-392': 0xb351,\n  'Skein512-400': 0xb352,\n  'Skein512-408': 0xb353,\n  'Skein512-416': 0xb354,\n  'Skein512-424': 0xb355,\n  'Skein512-432': 0xb356,\n  'Skein512-440': 0xb357,\n  'Skein512-448': 0xb358,\n  'Skein512-456': 0xb359,\n  'Skein512-464': 0xb35a,\n  'Skein512-472': 0xb35b,\n  'Skein512-480': 0xb35c,\n  'Skein512-488': 0xb35d,\n  'Skein512-496': 0xb35e,\n  'Skein512-504': 0xb35f,\n  'Skein512-512': 0xb360,\n  'Skein1024-8': 0xb361,\n  'Skein1024-16': 0xb362,\n  'Skein1024-24': 0xb363,\n  'Skein1024-32': 0xb364,\n  'Skein1024-40': 0xb365,\n  'Skein1024-48': 0xb366,\n  'Skein1024-56': 0xb367,\n  'Skein1024-64': 0xb368,\n  'Skein1024-72': 0xb369,\n  'Skein1024-80': 0xb36a,\n  'Skein1024-88': 0xb36b,\n  'Skein1024-96': 0xb36c,\n  'Skein1024-104': 0xb36d,\n  'Skein1024-112': 0xb36e,\n  'Skein1024-120': 0xb36f,\n  'Skein1024-128': 0xb370,\n  'Skein1024-136': 0xb371,\n  'Skein1024-144': 0xb372,\n  'Skein1024-152': 0xb373,\n  'Skein1024-160': 0xb374,\n  'Skein1024-168': 0xb375,\n  'Skein1024-176': 0xb376,\n  'Skein1024-184': 0xb377,\n  'Skein1024-192': 0xb378,\n  'Skein1024-200': 0xb379,\n  'Skein1024-208': 0xb37a,\n  'Skein1024-216': 0xb37b,\n  'Skein1024-224': 0xb37c,\n  'Skein1024-232': 0xb37d,\n  'Skein1024-240': 0xb37e,\n  'Skein1024-248': 0xb37f,\n  'Skein1024-256': 0xb380,\n  'Skein1024-264': 0xb381,\n  'Skein1024-272': 0xb382,\n  'Skein1024-280': 0xb383,\n  'Skein1024-288': 0xb384,\n  'Skein1024-296': 0xb385,\n  'Skein1024-304': 0xb386,\n  'Skein1024-312': 0xb387,\n  'Skein1024-320': 0xb388,\n  'Skein1024-328': 0xb389,\n  'Skein1024-336': 0xb38a,\n  'Skein1024-344': 0xb38b,\n  'Skein1024-352': 0xb38c,\n  'Skein1024-360': 0xb38d,\n  'Skein1024-368': 0xb38e,\n  'Skein1024-376': 0xb38f,\n  'Skein1024-384': 0xb390,\n  'Skein1024-392': 0xb391,\n  'Skein1024-400': 0xb392,\n  'Skein1024-408': 0xb393,\n  'Skein1024-416': 0xb394,\n  'Skein1024-424': 0xb395,\n  'Skein1024-432': 0xb396,\n  'Skein1024-440': 0xb397,\n  'Skein1024-448': 0xb398,\n  'Skein1024-456': 0xb399,\n  'Skein1024-464': 0xb39a,\n  'Skein1024-472': 0xb39b,\n  'Skein1024-480': 0xb39c,\n  'Skein1024-488': 0xb39d,\n  'Skein1024-496': 0xb39e,\n  'Skein1024-504': 0xb39f,\n  'Skein1024-512': 0xb3a0,\n  'Skein1024-520': 0xb3a1,\n  'Skein1024-528': 0xb3a2,\n  'Skein1024-536': 0xb3a3,\n  'Skein1024-544': 0xb3a4,\n  'Skein1024-552': 0xb3a5,\n  'Skein1024-560': 0xb3a6,\n  'Skein1024-568': 0xb3a7,\n  'Skein1024-576': 0xb3a8,\n  'Skein1024-584': 0xb3a9,\n  'Skein1024-592': 0xb3aa,\n  'Skein1024-600': 0xb3ab,\n  'Skein1024-608': 0xb3ac,\n  'Skein1024-616': 0xb3ad,\n  'Skein1024-624': 0xb3ae,\n  'Skein1024-632': 0xb3af,\n  'Skein1024-640': 0xb3b0,\n  'Skein1024-648': 0xb3b1,\n  'Skein1024-656': 0xb3b2,\n  'Skein1024-664': 0xb3b3,\n  'Skein1024-672': 0xb3b4,\n  'Skein1024-680': 0xb3b5,\n  'Skein1024-688': 0xb3b6,\n  'Skein1024-696': 0xb3b7,\n  'Skein1024-704': 0xb3b8,\n  'Skein1024-712': 0xb3b9,\n  'Skein1024-720': 0xb3ba,\n  'Skein1024-728': 0xb3bb,\n  'Skein1024-736': 0xb3bc,\n  'Skein1024-744': 0xb3bd,\n  'Skein1024-752': 0xb3be,\n  'Skein1024-760': 0xb3bf,\n  'Skein1024-768': 0xb3c0,\n  'Skein1024-776': 0xb3c1,\n  'Skein1024-784': 0xb3c2,\n  'Skein1024-792': 0xb3c3,\n  'Skein1024-800': 0xb3c4,\n  'Skein1024-808': 0xb3c5,\n  'Skein1024-816': 0xb3c6,\n  'Skein1024-824': 0xb3c7,\n  'Skein1024-832': 0xb3c8,\n  'Skein1024-840': 0xb3c9,\n  'Skein1024-848': 0xb3ca,\n  'Skein1024-856': 0xb3cb,\n  'Skein1024-864': 0xb3cc,\n  'Skein1024-872': 0xb3cd,\n  'Skein1024-880': 0xb3ce,\n  'Skein1024-888': 0xb3cf,\n  'Skein1024-896': 0xb3d0,\n  'Skein1024-904': 0xb3d1,\n  'Skein1024-912': 0xb3d2,\n  'Skein1024-920': 0xb3d3,\n  'Skein1024-928': 0xb3d4,\n  'Skein1024-936': 0xb3d5,\n  'Skein1024-944': 0xb3d6,\n  'Skein1024-952': 0xb3d7,\n  'Skein1024-960': 0xb3d8,\n  'Skein1024-968': 0xb3d9,\n  'Skein1024-976': 0xb3da,\n  'Skein1024-984': 0xb3db,\n  'Skein1024-992': 0xb3dc,\n  'Skein1024-1000': 0xb3dd,\n  'Skein1024-1008': 0xb3de,\n  'Skein1024-1016': 0xb3df,\n  'Skein1024-1024': 0xb3e0\n})\n\nexports.codes = Object.freeze({\n  0x0: 'identity',\n\n  // sha family\n  0x11: 'sha1',\n  0x12: 'sha2-256',\n  0x13: 'sha2-512',\n  0x56: 'dbl-sha2-256',\n  0x17: 'sha3-224',\n  0x16: 'sha3-256',\n  0x15: 'sha3-384',\n  0x14: 'sha3-512',\n  0x18: 'shake-128',\n  0x19: 'shake-256',\n  0x1A: 'keccak-224',\n  0x1B: 'keccak-256',\n  0x1C: 'keccak-384',\n  0x1D: 'keccak-512',\n\n  0x22: 'murmur3-128',\n  0x23: 'murmur3-32',\n\n  0xd4: 'md4',\n  0xd5: 'md5',\n\n  // blake2\n  0xb201: 'blake2b-8',\n  0xb202: 'blake2b-16',\n  0xb203: 'blake2b-24',\n  0xb204: 'blake2b-32',\n  0xb205: 'blake2b-40',\n  0xb206: 'blake2b-48',\n  0xb207: 'blake2b-56',\n  0xb208: 'blake2b-64',\n  0xb209: 'blake2b-72',\n  0xb20a: 'blake2b-80',\n  0xb20b: 'blake2b-88',\n  0xb20c: 'blake2b-96',\n  0xb20d: 'blake2b-104',\n  0xb20e: 'blake2b-112',\n  0xb20f: 'blake2b-120',\n  0xb210: 'blake2b-128',\n  0xb211: 'blake2b-136',\n  0xb212: 'blake2b-144',\n  0xb213: 'blake2b-152',\n  0xb214: 'blake2b-160',\n  0xb215: 'blake2b-168',\n  0xb216: 'blake2b-176',\n  0xb217: 'blake2b-184',\n  0xb218: 'blake2b-192',\n  0xb219: 'blake2b-200',\n  0xb21a: 'blake2b-208',\n  0xb21b: 'blake2b-216',\n  0xb21c: 'blake2b-224',\n  0xb21d: 'blake2b-232',\n  0xb21e: 'blake2b-240',\n  0xb21f: 'blake2b-248',\n  0xb220: 'blake2b-256',\n  0xb221: 'blake2b-264',\n  0xb222: 'blake2b-272',\n  0xb223: 'blake2b-280',\n  0xb224: 'blake2b-288',\n  0xb225: 'blake2b-296',\n  0xb226: 'blake2b-304',\n  0xb227: 'blake2b-312',\n  0xb228: 'blake2b-320',\n  0xb229: 'blake2b-328',\n  0xb22a: 'blake2b-336',\n  0xb22b: 'blake2b-344',\n  0xb22c: 'blake2b-352',\n  0xb22d: 'blake2b-360',\n  0xb22e: 'blake2b-368',\n  0xb22f: 'blake2b-376',\n  0xb230: 'blake2b-384',\n  0xb231: 'blake2b-392',\n  0xb232: 'blake2b-400',\n  0xb233: 'blake2b-408',\n  0xb234: 'blake2b-416',\n  0xb235: 'blake2b-424',\n  0xb236: 'blake2b-432',\n  0xb237: 'blake2b-440',\n  0xb238: 'blake2b-448',\n  0xb239: 'blake2b-456',\n  0xb23a: 'blake2b-464',\n  0xb23b: 'blake2b-472',\n  0xb23c: 'blake2b-480',\n  0xb23d: 'blake2b-488',\n  0xb23e: 'blake2b-496',\n  0xb23f: 'blake2b-504',\n  0xb240: 'blake2b-512',\n  0xb241: 'blake2s-8',\n  0xb242: 'blake2s-16',\n  0xb243: 'blake2s-24',\n  0xb244: 'blake2s-32',\n  0xb245: 'blake2s-40',\n  0xb246: 'blake2s-48',\n  0xb247: 'blake2s-56',\n  0xb248: 'blake2s-64',\n  0xb249: 'blake2s-72',\n  0xb24a: 'blake2s-80',\n  0xb24b: 'blake2s-88',\n  0xb24c: 'blake2s-96',\n  0xb24d: 'blake2s-104',\n  0xb24e: 'blake2s-112',\n  0xb24f: 'blake2s-120',\n  0xb250: 'blake2s-128',\n  0xb251: 'blake2s-136',\n  0xb252: 'blake2s-144',\n  0xb253: 'blake2s-152',\n  0xb254: 'blake2s-160',\n  0xb255: 'blake2s-168',\n  0xb256: 'blake2s-176',\n  0xb257: 'blake2s-184',\n  0xb258: 'blake2s-192',\n  0xb259: 'blake2s-200',\n  0xb25a: 'blake2s-208',\n  0xb25b: 'blake2s-216',\n  0xb25c: 'blake2s-224',\n  0xb25d: 'blake2s-232',\n  0xb25e: 'blake2s-240',\n  0xb25f: 'blake2s-248',\n  0xb260: 'blake2s-256',\n\n  // skein\n  0xb301: 'Skein256-8',\n  0xb302: 'Skein256-16',\n  0xb303: 'Skein256-24',\n  0xb304: 'Skein256-32',\n  0xb305: 'Skein256-40',\n  0xb306: 'Skein256-48',\n  0xb307: 'Skein256-56',\n  0xb308: 'Skein256-64',\n  0xb309: 'Skein256-72',\n  0xb30a: 'Skein256-80',\n  0xb30b: 'Skein256-88',\n  0xb30c: 'Skein256-96',\n  0xb30d: 'Skein256-104',\n  0xb30e: 'Skein256-112',\n  0xb30f: 'Skein256-120',\n  0xb310: 'Skein256-128',\n  0xb311: 'Skein256-136',\n  0xb312: 'Skein256-144',\n  0xb313: 'Skein256-152',\n  0xb314: 'Skein256-160',\n  0xb315: 'Skein256-168',\n  0xb316: 'Skein256-176',\n  0xb317: 'Skein256-184',\n  0xb318: 'Skein256-192',\n  0xb319: 'Skein256-200',\n  0xb31a: 'Skein256-208',\n  0xb31b: 'Skein256-216',\n  0xb31c: 'Skein256-224',\n  0xb31d: 'Skein256-232',\n  0xb31e: 'Skein256-240',\n  0xb31f: 'Skein256-248',\n  0xb320: 'Skein256-256',\n  0xb321: 'Skein512-8',\n  0xb322: 'Skein512-16',\n  0xb323: 'Skein512-24',\n  0xb324: 'Skein512-32',\n  0xb325: 'Skein512-40',\n  0xb326: 'Skein512-48',\n  0xb327: 'Skein512-56',\n  0xb328: 'Skein512-64',\n  0xb329: 'Skein512-72',\n  0xb32a: 'Skein512-80',\n  0xb32b: 'Skein512-88',\n  0xb32c: 'Skein512-96',\n  0xb32d: 'Skein512-104',\n  0xb32e: 'Skein512-112',\n  0xb32f: 'Skein512-120',\n  0xb330: 'Skein512-128',\n  0xb331: 'Skein512-136',\n  0xb332: 'Skein512-144',\n  0xb333: 'Skein512-152',\n  0xb334: 'Skein512-160',\n  0xb335: 'Skein512-168',\n  0xb336: 'Skein512-176',\n  0xb337: 'Skein512-184',\n  0xb338: 'Skein512-192',\n  0xb339: 'Skein512-200',\n  0xb33a: 'Skein512-208',\n  0xb33b: 'Skein512-216',\n  0xb33c: 'Skein512-224',\n  0xb33d: 'Skein512-232',\n  0xb33e: 'Skein512-240',\n  0xb33f: 'Skein512-248',\n  0xb340: 'Skein512-256',\n  0xb341: 'Skein512-264',\n  0xb342: 'Skein512-272',\n  0xb343: 'Skein512-280',\n  0xb344: 'Skein512-288',\n  0xb345: 'Skein512-296',\n  0xb346: 'Skein512-304',\n  0xb347: 'Skein512-312',\n  0xb348: 'Skein512-320',\n  0xb349: 'Skein512-328',\n  0xb34a: 'Skein512-336',\n  0xb34b: 'Skein512-344',\n  0xb34c: 'Skein512-352',\n  0xb34d: 'Skein512-360',\n  0xb34e: 'Skein512-368',\n  0xb34f: 'Skein512-376',\n  0xb350: 'Skein512-384',\n  0xb351: 'Skein512-392',\n  0xb352: 'Skein512-400',\n  0xb353: 'Skein512-408',\n  0xb354: 'Skein512-416',\n  0xb355: 'Skein512-424',\n  0xb356: 'Skein512-432',\n  0xb357: 'Skein512-440',\n  0xb358: 'Skein512-448',\n  0xb359: 'Skein512-456',\n  0xb35a: 'Skein512-464',\n  0xb35b: 'Skein512-472',\n  0xb35c: 'Skein512-480',\n  0xb35d: 'Skein512-488',\n  0xb35e: 'Skein512-496',\n  0xb35f: 'Skein512-504',\n  0xb360: 'Skein512-512',\n  0xb361: 'Skein1024-8',\n  0xb362: 'Skein1024-16',\n  0xb363: 'Skein1024-24',\n  0xb364: 'Skein1024-32',\n  0xb365: 'Skein1024-40',\n  0xb366: 'Skein1024-48',\n  0xb367: 'Skein1024-56',\n  0xb368: 'Skein1024-64',\n  0xb369: 'Skein1024-72',\n  0xb36a: 'Skein1024-80',\n  0xb36b: 'Skein1024-88',\n  0xb36c: 'Skein1024-96',\n  0xb36d: 'Skein1024-104',\n  0xb36e: 'Skein1024-112',\n  0xb36f: 'Skein1024-120',\n  0xb370: 'Skein1024-128',\n  0xb371: 'Skein1024-136',\n  0xb372: 'Skein1024-144',\n  0xb373: 'Skein1024-152',\n  0xb374: 'Skein1024-160',\n  0xb375: 'Skein1024-168',\n  0xb376: 'Skein1024-176',\n  0xb377: 'Skein1024-184',\n  0xb378: 'Skein1024-192',\n  0xb379: 'Skein1024-200',\n  0xb37a: 'Skein1024-208',\n  0xb37b: 'Skein1024-216',\n  0xb37c: 'Skein1024-224',\n  0xb37d: 'Skein1024-232',\n  0xb37e: 'Skein1024-240',\n  0xb37f: 'Skein1024-248',\n  0xb380: 'Skein1024-256',\n  0xb381: 'Skein1024-264',\n  0xb382: 'Skein1024-272',\n  0xb383: 'Skein1024-280',\n  0xb384: 'Skein1024-288',\n  0xb385: 'Skein1024-296',\n  0xb386: 'Skein1024-304',\n  0xb387: 'Skein1024-312',\n  0xb388: 'Skein1024-320',\n  0xb389: 'Skein1024-328',\n  0xb38a: 'Skein1024-336',\n  0xb38b: 'Skein1024-344',\n  0xb38c: 'Skein1024-352',\n  0xb38d: 'Skein1024-360',\n  0xb38e: 'Skein1024-368',\n  0xb38f: 'Skein1024-376',\n  0xb390: 'Skein1024-384',\n  0xb391: 'Skein1024-392',\n  0xb392: 'Skein1024-400',\n  0xb393: 'Skein1024-408',\n  0xb394: 'Skein1024-416',\n  0xb395: 'Skein1024-424',\n  0xb396: 'Skein1024-432',\n  0xb397: 'Skein1024-440',\n  0xb398: 'Skein1024-448',\n  0xb399: 'Skein1024-456',\n  0xb39a: 'Skein1024-464',\n  0xb39b: 'Skein1024-472',\n  0xb39c: 'Skein1024-480',\n  0xb39d: 'Skein1024-488',\n  0xb39e: 'Skein1024-496',\n  0xb39f: 'Skein1024-504',\n  0xb3a0: 'Skein1024-512',\n  0xb3a1: 'Skein1024-520',\n  0xb3a2: 'Skein1024-528',\n  0xb3a3: 'Skein1024-536',\n  0xb3a4: 'Skein1024-544',\n  0xb3a5: 'Skein1024-552',\n  0xb3a6: 'Skein1024-560',\n  0xb3a7: 'Skein1024-568',\n  0xb3a8: 'Skein1024-576',\n  0xb3a9: 'Skein1024-584',\n  0xb3aa: 'Skein1024-592',\n  0xb3ab: 'Skein1024-600',\n  0xb3ac: 'Skein1024-608',\n  0xb3ad: 'Skein1024-616',\n  0xb3ae: 'Skein1024-624',\n  0xb3af: 'Skein1024-632',\n  0xb3b0: 'Skein1024-640',\n  0xb3b1: 'Skein1024-648',\n  0xb3b2: 'Skein1024-656',\n  0xb3b3: 'Skein1024-664',\n  0xb3b4: 'Skein1024-672',\n  0xb3b5: 'Skein1024-680',\n  0xb3b6: 'Skein1024-688',\n  0xb3b7: 'Skein1024-696',\n  0xb3b8: 'Skein1024-704',\n  0xb3b9: 'Skein1024-712',\n  0xb3ba: 'Skein1024-720',\n  0xb3bb: 'Skein1024-728',\n  0xb3bc: 'Skein1024-736',\n  0xb3bd: 'Skein1024-744',\n  0xb3be: 'Skein1024-752',\n  0xb3bf: 'Skein1024-760',\n  0xb3c0: 'Skein1024-768',\n  0xb3c1: 'Skein1024-776',\n  0xb3c2: 'Skein1024-784',\n  0xb3c3: 'Skein1024-792',\n  0xb3c4: 'Skein1024-800',\n  0xb3c5: 'Skein1024-808',\n  0xb3c6: 'Skein1024-816',\n  0xb3c7: 'Skein1024-824',\n  0xb3c8: 'Skein1024-832',\n  0xb3c9: 'Skein1024-840',\n  0xb3ca: 'Skein1024-848',\n  0xb3cb: 'Skein1024-856',\n  0xb3cc: 'Skein1024-864',\n  0xb3cd: 'Skein1024-872',\n  0xb3ce: 'Skein1024-880',\n  0xb3cf: 'Skein1024-888',\n  0xb3d0: 'Skein1024-896',\n  0xb3d1: 'Skein1024-904',\n  0xb3d2: 'Skein1024-912',\n  0xb3d3: 'Skein1024-920',\n  0xb3d4: 'Skein1024-928',\n  0xb3d5: 'Skein1024-936',\n  0xb3d6: 'Skein1024-944',\n  0xb3d7: 'Skein1024-952',\n  0xb3d8: 'Skein1024-960',\n  0xb3d9: 'Skein1024-968',\n  0xb3da: 'Skein1024-976',\n  0xb3db: 'Skein1024-984',\n  0xb3dc: 'Skein1024-992',\n  0xb3dd: 'Skein1024-1000',\n  0xb3de: 'Skein1024-1008',\n  0xb3df: 'Skein1024-1016',\n  0xb3e0: 'Skein1024-1024'\n})\n\nexports.defaultLengths = Object.freeze({\n  0x11: 20,\n  0x12: 32,\n  0x13: 64,\n  0x56: 32,\n  0x17: 28,\n  0x16: 32,\n  0x15: 48,\n  0x14: 64,\n  0x18: 32,\n  0x19: 64,\n  0x1A: 28,\n  0x1B: 32,\n  0x1C: 48,\n  0x1D: 64,\n  0x22: 32,\n\n  0xb201: 0x01,\n  0xb202: 0x02,\n  0xb203: 0x03,\n  0xb204: 0x04,\n  0xb205: 0x05,\n  0xb206: 0x06,\n  0xb207: 0x07,\n  0xb208: 0x08,\n  0xb209: 0x09,\n  0xb20a: 0x0a,\n  0xb20b: 0x0b,\n  0xb20c: 0x0c,\n  0xb20d: 0x0d,\n  0xb20e: 0x0e,\n  0xb20f: 0x0f,\n  0xb210: 0x10,\n  0xb211: 0x11,\n  0xb212: 0x12,\n  0xb213: 0x13,\n  0xb214: 0x14,\n  0xb215: 0x15,\n  0xb216: 0x16,\n  0xb217: 0x17,\n  0xb218: 0x18,\n  0xb219: 0x19,\n  0xb21a: 0x1a,\n  0xb21b: 0x1b,\n  0xb21c: 0x1c,\n  0xb21d: 0x1d,\n  0xb21e: 0x1e,\n  0xb21f: 0x1f,\n  0xb220: 0x20,\n  0xb221: 0x21,\n  0xb222: 0x22,\n  0xb223: 0x23,\n  0xb224: 0x24,\n  0xb225: 0x25,\n  0xb226: 0x26,\n  0xb227: 0x27,\n  0xb228: 0x28,\n  0xb229: 0x29,\n  0xb22a: 0x2a,\n  0xb22b: 0x2b,\n  0xb22c: 0x2c,\n  0xb22d: 0x2d,\n  0xb22e: 0x2e,\n  0xb22f: 0x2f,\n  0xb230: 0x30,\n  0xb231: 0x31,\n  0xb232: 0x32,\n  0xb233: 0x33,\n  0xb234: 0x34,\n  0xb235: 0x35,\n  0xb236: 0x36,\n  0xb237: 0x37,\n  0xb238: 0x38,\n  0xb239: 0x39,\n  0xb23a: 0x3a,\n  0xb23b: 0x3b,\n  0xb23c: 0x3c,\n  0xb23d: 0x3d,\n  0xb23e: 0x3e,\n  0xb23f: 0x3f,\n  0xb240: 0x40,\n  0xb241: 0x01,\n  0xb242: 0x02,\n  0xb243: 0x03,\n  0xb244: 0x04,\n  0xb245: 0x05,\n  0xb246: 0x06,\n  0xb247: 0x07,\n  0xb248: 0x08,\n  0xb249: 0x09,\n  0xb24a: 0x0a,\n  0xb24b: 0x0b,\n  0xb24c: 0x0c,\n  0xb24d: 0x0d,\n  0xb24e: 0x0e,\n  0xb24f: 0x0f,\n  0xb250: 0x10,\n  0xb251: 0x11,\n  0xb252: 0x12,\n  0xb253: 0x13,\n  0xb254: 0x14,\n  0xb255: 0x15,\n  0xb256: 0x16,\n  0xb257: 0x17,\n  0xb258: 0x18,\n  0xb259: 0x19,\n  0xb25a: 0x1a,\n  0xb25b: 0x1b,\n  0xb25c: 0x1c,\n  0xb25d: 0x1d,\n  0xb25e: 0x1e,\n  0xb25f: 0x1f,\n  0xb260: 0x20,\n  0xb301: 0x01,\n  0xb302: 0x02,\n  0xb303: 0x03,\n  0xb304: 0x04,\n  0xb305: 0x05,\n  0xb306: 0x06,\n  0xb307: 0x07,\n  0xb308: 0x08,\n  0xb309: 0x09,\n  0xb30a: 0x0a,\n  0xb30b: 0x0b,\n  0xb30c: 0x0c,\n  0xb30d: 0x0d,\n  0xb30e: 0x0e,\n  0xb30f: 0x0f,\n  0xb310: 0x10,\n  0xb311: 0x11,\n  0xb312: 0x12,\n  0xb313: 0x13,\n  0xb314: 0x14,\n  0xb315: 0x15,\n  0xb316: 0x16,\n  0xb317: 0x17,\n  0xb318: 0x18,\n  0xb319: 0x19,\n  0xb31a: 0x1a,\n  0xb31b: 0x1b,\n  0xb31c: 0x1c,\n  0xb31d: 0x1d,\n  0xb31e: 0x1e,\n  0xb31f: 0x1f,\n  0xb320: 0x20,\n  0xb321: 0x01,\n  0xb322: 0x02,\n  0xb323: 0x03,\n  0xb324: 0x04,\n  0xb325: 0x05,\n  0xb326: 0x06,\n  0xb327: 0x07,\n  0xb328: 0x08,\n  0xb329: 0x09,\n  0xb32a: 0x0a,\n  0xb32b: 0x0b,\n  0xb32c: 0x0c,\n  0xb32d: 0x0d,\n  0xb32e: 0x0e,\n  0xb32f: 0x0f,\n  0xb330: 0x10,\n  0xb331: 0x11,\n  0xb332: 0x12,\n  0xb333: 0x13,\n  0xb334: 0x14,\n  0xb335: 0x15,\n  0xb336: 0x16,\n  0xb337: 0x17,\n  0xb338: 0x18,\n  0xb339: 0x19,\n  0xb33a: 0x1a,\n  0xb33b: 0x1b,\n  0xb33c: 0x1c,\n  0xb33d: 0x1d,\n  0xb33e: 0x1e,\n  0xb33f: 0x1f,\n  0xb340: 0x20,\n  0xb341: 0x21,\n  0xb342: 0x22,\n  0xb343: 0x23,\n  0xb344: 0x24,\n  0xb345: 0x25,\n  0xb346: 0x26,\n  0xb347: 0x27,\n  0xb348: 0x28,\n  0xb349: 0x29,\n  0xb34a: 0x2a,\n  0xb34b: 0x2b,\n  0xb34c: 0x2c,\n  0xb34d: 0x2d,\n  0xb34e: 0x2e,\n  0xb34f: 0x2f,\n  0xb350: 0x30,\n  0xb351: 0x31,\n  0xb352: 0x32,\n  0xb353: 0x33,\n  0xb354: 0x34,\n  0xb355: 0x35,\n  0xb356: 0x36,\n  0xb357: 0x37,\n  0xb358: 0x38,\n  0xb359: 0x39,\n  0xb35a: 0x3a,\n  0xb35b: 0x3b,\n  0xb35c: 0x3c,\n  0xb35d: 0x3d,\n  0xb35e: 0x3e,\n  0xb35f: 0x3f,\n  0xb360: 0x40,\n  0xb361: 0x01,\n  0xb362: 0x02,\n  0xb363: 0x03,\n  0xb364: 0x04,\n  0xb365: 0x05,\n  0xb366: 0x06,\n  0xb367: 0x07,\n  0xb368: 0x08,\n  0xb369: 0x09,\n  0xb36a: 0x0a,\n  0xb36b: 0x0b,\n  0xb36c: 0x0c,\n  0xb36d: 0x0d,\n  0xb36e: 0x0e,\n  0xb36f: 0x0f,\n  0xb370: 0x10,\n  0xb371: 0x11,\n  0xb372: 0x12,\n  0xb373: 0x13,\n  0xb374: 0x14,\n  0xb375: 0x15,\n  0xb376: 0x16,\n  0xb377: 0x17,\n  0xb378: 0x18,\n  0xb379: 0x19,\n  0xb37a: 0x1a,\n  0xb37b: 0x1b,\n  0xb37c: 0x1c,\n  0xb37d: 0x1d,\n  0xb37e: 0x1e,\n  0xb37f: 0x1f,\n  0xb380: 0x20,\n  0xb381: 0x21,\n  0xb382: 0x22,\n  0xb383: 0x23,\n  0xb384: 0x24,\n  0xb385: 0x25,\n  0xb386: 0x26,\n  0xb387: 0x27,\n  0xb388: 0x28,\n  0xb389: 0x29,\n  0xb38a: 0x2a,\n  0xb38b: 0x2b,\n  0xb38c: 0x2c,\n  0xb38d: 0x2d,\n  0xb38e: 0x2e,\n  0xb38f: 0x2f,\n  0xb390: 0x30,\n  0xb391: 0x31,\n  0xb392: 0x32,\n  0xb393: 0x33,\n  0xb394: 0x34,\n  0xb395: 0x35,\n  0xb396: 0x36,\n  0xb397: 0x37,\n  0xb398: 0x38,\n  0xb399: 0x39,\n  0xb39a: 0x3a,\n  0xb39b: 0x3b,\n  0xb39c: 0x3c,\n  0xb39d: 0x3d,\n  0xb39e: 0x3e,\n  0xb39f: 0x3f,\n  0xb3a0: 0x40,\n  0xb3a1: 0x41,\n  0xb3a2: 0x42,\n  0xb3a3: 0x43,\n  0xb3a4: 0x44,\n  0xb3a5: 0x45,\n  0xb3a6: 0x46,\n  0xb3a7: 0x47,\n  0xb3a8: 0x48,\n  0xb3a9: 0x49,\n  0xb3aa: 0x4a,\n  0xb3ab: 0x4b,\n  0xb3ac: 0x4c,\n  0xb3ad: 0x4d,\n  0xb3ae: 0x4e,\n  0xb3af: 0x4f,\n  0xb3b0: 0x50,\n  0xb3b1: 0x51,\n  0xb3b2: 0x52,\n  0xb3b3: 0x53,\n  0xb3b4: 0x54,\n  0xb3b5: 0x55,\n  0xb3b6: 0x56,\n  0xb3b7: 0x57,\n  0xb3b8: 0x58,\n  0xb3b9: 0x59,\n  0xb3ba: 0x5a,\n  0xb3bb: 0x5b,\n  0xb3bc: 0x5c,\n  0xb3bd: 0x5d,\n  0xb3be: 0x5e,\n  0xb3bf: 0x5f,\n  0xb3c0: 0x60,\n  0xb3c1: 0x61,\n  0xb3c2: 0x62,\n  0xb3c3: 0x63,\n  0xb3c4: 0x64,\n  0xb3c5: 0x65,\n  0xb3c6: 0x66,\n  0xb3c7: 0x67,\n  0xb3c8: 0x68,\n  0xb3c9: 0x69,\n  0xb3ca: 0x6a,\n  0xb3cb: 0x6b,\n  0xb3cc: 0x6c,\n  0xb3cd: 0x6d,\n  0xb3ce: 0x6e,\n  0xb3cf: 0x6f,\n  0xb3d0: 0x70,\n  0xb3d1: 0x71,\n  0xb3d2: 0x72,\n  0xb3d3: 0x73,\n  0xb3d4: 0x74,\n  0xb3d5: 0x75,\n  0xb3d6: 0x76,\n  0xb3d7: 0x77,\n  0xb3d8: 0x78,\n  0xb3d9: 0x79,\n  0xb3da: 0x7a,\n  0xb3db: 0x7b,\n  0xb3dc: 0x7c,\n  0xb3dd: 0x7d,\n  0xb3de: 0x7e,\n  0xb3df: 0x7f,\n  0xb3e0: 0x80\n})\n"], "sourceRoot": ""}