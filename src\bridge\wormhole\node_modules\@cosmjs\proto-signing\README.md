# @cosmjs/proto-signing

[![npm version](https://img.shields.io/npm/v/@cosmjs/proto-signing.svg)](https://www.npmjs.com/package/@cosmjs/proto-signing)

Utilities for protobuf based signing (for Cosmos SDK 0.40+) as documented in
[ADR-020](https://github.com/cosmos/cosmos-sdk/blob/66c5798cec/docs/architecture/adr-020-protobuf-transaction-encoding.md)
and
[The 3 levels of proto encoding](https://warta.it/blog/cosmos-sdk-protobuf-signing).

## License

This package is part of the cosmjs repository, licensed under the Apache License
2.0 (see [NOTICE](https://github.com/cosmos/cosmjs/blob/main/NOTICE) and
[LICENSE](https://github.com/cosmos/cosmjs/blob/main/LICENSE)).
