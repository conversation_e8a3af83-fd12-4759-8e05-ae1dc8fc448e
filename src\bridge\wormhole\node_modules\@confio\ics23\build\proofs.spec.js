"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const codecimpl_1 = require("./generated/codecimpl");
const proofs_1 = require("./proofs");
const testhelpers_spec_1 = require("./testhelpers.spec");
describe("calculateExistenceRoot", () => {
    it("must have at least one step", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
        };
        expect(() => (0, proofs_1.calculateExistenceRoot)(proof)).toThrow();
    });
    it("executes one leaf step", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("food"),
            value: (0, testhelpers_spec_1.toAscii)("some longer text"),
            leaf: {
                hash: codecimpl_1.ics23.HashOp.SHA256,
                length: codecimpl_1.ics23.LengthOp.VAR_PROTO,
            },
        };
        const expected = (0, testhelpers_spec_1.fromHex)("b68f5d298e915ae1753dd333da1f9cf605411a5f2e12516be6758f365e6db265");
        expect((0, proofs_1.calculateExistenceRoot)(proof)).toEqual(expected);
    });
    it("cannot execute inner first", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("food"),
            value: (0, testhelpers_spec_1.toAscii)("some longer text"),
            path: [
                {
                    hash: codecimpl_1.ics23.HashOp.SHA256,
                    prefix: (0, testhelpers_spec_1.fromHex)("deadbeef00cafe00"),
                },
            ],
        };
        expect(() => (0, proofs_1.calculateExistenceRoot)(proof)).toThrow();
    });
    it("can execute leaf then inner", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("food"),
            value: (0, testhelpers_spec_1.toAscii)("some longer text"),
            leaf: {
                hash: codecimpl_1.ics23.HashOp.SHA256,
                length: codecimpl_1.ics23.LengthOp.VAR_PROTO,
            },
            // output: b68f5d298e915ae1753dd333da1f9cf605411a5f2e12516be6758f365e6db265
            path: [
                {
                    hash: codecimpl_1.ics23.HashOp.SHA256,
                    prefix: (0, testhelpers_spec_1.fromHex)("deadbeef00cafe00"),
                },
                // echo -n deadbeef00cafe00b68f5d298e915ae1753dd333da1f9cf605411a5f2e12516be6758f365e6db265 | xxd -r -p | sha256sum
            ],
        };
        const expected = (0, testhelpers_spec_1.fromHex)("836ea236a6902a665c2a004c920364f24cad52ded20b1e4f22c3179bfe25b2a9");
        expect((0, proofs_1.calculateExistenceRoot)(proof)).toEqual(expected);
    });
});
describe("ensureSpec", () => {
    const validLeaf = proofs_1.iavlSpec.leafSpec;
    const invalidLeaf = {
        prefix: Uint8Array.from([0]),
        hash: codecimpl_1.ics23.HashOp.SHA512,
        prehashValue: codecimpl_1.ics23.HashOp.NO_HASH,
        prehashKey: codecimpl_1.ics23.HashOp.NO_HASH,
        length: codecimpl_1.ics23.LengthOp.VAR_PROTO,
    };
    const validInner = {
        hash: codecimpl_1.ics23.HashOp.SHA256,
        prefix: (0, testhelpers_spec_1.fromHex)("deadbeef00cafe00"),
        // suffix: Uint8Array.from([]),
    };
    const invalidInner = {
        hash: codecimpl_1.ics23.HashOp.SHA256,
        prefix: (0, testhelpers_spec_1.fromHex)("aa"),
    };
    const invalidInnerHash = {
        hash: codecimpl_1.ics23.HashOp.SHA512,
        prefix: (0, testhelpers_spec_1.fromHex)("deadbeef00cafe00"),
    };
    const depthLimitedSpec = {
        leafSpec: proofs_1.iavlSpec.leafSpec,
        innerSpec: proofs_1.iavlSpec.innerSpec,
        minDepth: 2,
        maxDepth: 4,
    };
    it("rejects empty proof", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec)).toThrow();
    });
    it("accepts one valid leaf", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
        };
        // fail if this throws (invalid spec)
        (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec);
    });
    it("rejects invalid leaf", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: invalidLeaf,
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec)).toThrow();
    });
    it("rejects inner without leaf", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            path: [validInner],
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec)).toThrow();
    });
    it("accepts leaf with one inner", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [validInner],
        };
        // fail if this throws (invalid spec)
        (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec);
    });
    it("rejects with invalid inner (prefix)", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [invalidInner, validInner],
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec)).toThrow();
    });
    it("rejects with invalid inner (hash)", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [validInner, invalidInnerHash],
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, proofs_1.iavlSpec)).toThrow();
    });
    it("accepts depth limited with proper number of nodes", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [validInner, validInner, validInner],
        };
        // fail if this throws (invalid spec)
        (0, proofs_1.ensureSpec)(proof, depthLimitedSpec);
    });
    it("rejects depth limited with too few nodes", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [validInner],
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, depthLimitedSpec)).toThrow();
    });
    it("rejects depth limited with too many nodes", () => {
        const proof = {
            key: (0, testhelpers_spec_1.toAscii)("foo"),
            value: (0, testhelpers_spec_1.toAscii)("bar"),
            leaf: validLeaf,
            path: [validInner, validInner, validInner, validInner, validInner],
        };
        expect(() => (0, proofs_1.ensureSpec)(proof, depthLimitedSpec)).toThrow();
    });
});
//# sourceMappingURL=proofs.spec.js.map