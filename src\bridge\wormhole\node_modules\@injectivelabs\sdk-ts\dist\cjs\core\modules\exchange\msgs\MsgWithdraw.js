"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
/**
 * @category Messages
 */
class MsgWithdraw extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgWithdraw(params);
    }
    toProto() {
        const { params } = this;
        const amountCoin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        amountCoin.denom = params.amount.denom;
        amountCoin.amount = params.amount.amount;
        const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgWithdraw.create();
        message.sender = params.injectiveAddress;
        message.subaccountId = params.subaccountId;
        message.amount = amountCoin;
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgWithdraw.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgWithdraw',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'exchange/MsgWithdraw',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgWithdraw',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgWithdraw',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgWithdraw.encode(this.toProto()).finish();
    }
}
exports.default = MsgWithdraw;
