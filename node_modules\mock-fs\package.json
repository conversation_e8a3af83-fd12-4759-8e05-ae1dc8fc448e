{"name": "mock-fs", "description": "A configurable mock file system.  You know, for testing.", "version": "4.14.0", "main": "lib/index.js", "homepage": "https://github.com/tschaub/mock-fs", "author": {"name": "<PERSON>", "url": "http://tschaub.net/"}, "keywords": ["mock", "fs", "test", "fixtures", "file system", "memory"], "repository": {"type": "git", "url": "git://github.com/tschaub/mock-fs.git"}, "bugs": {"url": "https://github.com/tschaub/mock-fs/issues"}, "license": "MIT", "files": ["lib"], "scripts": {"lint": "eslint benchmarks lib test", "pretest": "npm run lint", "test": "mocha --recursive test"}, "eslintConfig": {"extends": "<PERSON><PERSON><PERSON><PERSON>"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^5.16.0", "eslint-config-tschaub": "^13.1.0", "mocha": "^6.1.4", "rimraf": "^2.6.3", "semver": "^6.0.0"}}