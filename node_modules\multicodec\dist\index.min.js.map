{"version": 3, "sources": ["/Users/<USER>/Code/multiformats/js-multicodec/webpack/universalModuleDefinition", "/Users/<USER>/Code/multiformats/js-multicodec/webpack/bootstrap", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/buffer/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/varint/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/util.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/webpack/buildin/global.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/base64-js/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/ieee754/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/isarray/index.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/varint/encode.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/varint/decode.js", "/Users/<USER>/Code/multiformats/js-multicodec/node_modules/varint/length.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/int-table.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/varint-table.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/constants.js", "/Users/<USER>/Code/multiformats/js-multicodec/src/print.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "base64", "ieee754", "isArray", "kMaxLength", "<PERSON><PERSON><PERSON>", "TYPED_ARRAY_SUPPORT", "createBuffer", "that", "length", "RangeError", "Uint8Array", "__proto__", "arg", "encodingOrOffset", "this", "Error", "allocUnsafe", "from", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "byteOffset", "byteLength", "undefined", "fromArrayLike", "fromArrayBuffer", "string", "encoding", "isEncoding", "actual", "write", "slice", "fromString", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "checked", "copy", "buffer", "val", "type", "data", "fromObject", "assertSize", "size", "toString", "<PERSON><PERSON><PERSON><PERSON>", "loweredCase", "utf8ToBytes", "base64ToBytes", "toLowerCase", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "b", "bidirectionalIndexOf", "dir", "isNaN", "arrayIndexOf", "indexOf", "lastIndexOf", "arr", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "String", "read", "buf", "readUInt16BE", "foundIndex", "found", "j", "hexWrite", "offset", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "str", "byteArray", "push", "charCodeAt", "asciiToBytes", "latin1Write", "base64Write", "ucs2Write", "units", "hi", "lo", "utf16leToBytes", "fromByteArray", "Math", "min", "res", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "firstByte", "codePoint", "bytesPerSequence", "codePoints", "MAX_ARGUMENTS_LENGTH", "fromCharCode", "apply", "decodeCodePointsArray", "<PERSON><PERSON><PERSON><PERSON>", "alloc", "INSPECT_MAX_BYTES", "global", "foo", "subarray", "e", "typedArraySupport", "poolSize", "_augment", "species", "configurable", "fill", "allocUnsafeSlow", "_isBuffer", "compare", "a", "x", "y", "concat", "list", "pos", "swap16", "swap32", "swap64", "arguments", "equals", "inspect", "max", "match", "join", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "includes", "isFinite", "toJSON", "Array", "_arr", "ret", "out", "toHex", "bytes", "checkOffset", "ext", "checkInt", "objectWriteUInt16", "littleEndian", "objectWriteUInt32", "checkIEEE754", "writeFloat", "noAssert", "writeDouble", "newBuf", "sliceLen", "readUIntLE", "mul", "readUIntBE", "readUInt8", "readUInt16LE", "readUInt32LE", "readUInt32BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "writeUIntLE", "writeUIntBE", "writeUInt8", "floor", "writeUInt16LE", "writeUInt16BE", "writeUInt32LE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "writeFloatLE", "writeFloatBE", "writeDoubleLE", "writeDoubleBE", "targetStart", "set", "code", "INVALID_BASE64_RE", "Infinity", "leadSurrogate", "toByteArray", "trim", "replace", "stringtrim", "base64clean", "src", "dst", "encode", "decode", "<PERSON><PERSON><PERSON><PERSON>", "varint", "require", "bufferToNumber", "numberToBuffer", "num", "hexString", "varintBufferEncode", "input", "varintBufferDecode", "varintEncode", "intTable", "codecNameToCodeVarint", "util", "addPrefix", "multicodecStrOrCode", "prefix", "rmPrefix", "getCodec", "prefixedData", "codecName", "getName", "codec", "getNumber", "getCode", "getCodeVarint", "getVarint", "constants", "assign", "print", "g", "Function", "b64", "lens", "getLens", "validLen", "placeHoldersLen", "tmp", "Arr", "_byteLength", "curByte", "revLookup", "uint8", "extraBytes", "parts", "len2", "encodeChunk", "lookup", "output", "isLE", "mLen", "nBytes", "eLen", "eMax", "eBias", "nBits", "NaN", "rt", "abs", "log", "LN2", "oldOffset", "INT", "MSB", "MSBALL", "shift", "counter", "REST", "N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "baseTable", "nameTable", "Map", "encodingName", "freeze", "varintTable", "table", "entries", "toUpperCase", "tableByCode"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,IARvB,CASGK,QAAQ,WACX,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,kuQClFrD,YAUA,IAAIC,EAAS,EAAQ,GAEjBC,EAAU,EAAQ,GAElBC,EAAU,EAAQ,GAsDtB,SAASC,IACP,OAAOC,EAAOC,oBAAsB,WAAa,WAGnD,SAASC,EAAaC,EAAMC,GAC1B,GAAIL,IAAeK,EACjB,MAAM,IAAIC,WAAW,8BAgBvB,OAbIL,EAAOC,qBAETE,EAAO,IAAIG,WAAWF,IACjBG,UAAYP,EAAOR,WAGX,OAATW,IACFA,EAAO,IAAIH,EAAOI,IAGpBD,EAAKC,OAASA,GAGTD,EAaT,SAASH,EAAOQ,EAAKC,EAAkBL,GACrC,KAAKJ,EAAOC,qBAAyBS,gBAAgBV,GACnD,OAAO,IAAIA,EAAOQ,EAAKC,EAAkBL,GAI3C,GAAmB,iBAARI,EAAkB,CAC3B,GAAgC,iBAArBC,EACT,MAAM,IAAIE,MAAM,qEAGlB,OAAOC,EAAYF,KAAMF,GAG3B,OAAOK,EAAKH,KAAMF,EAAKC,EAAkBL,GAW3C,SAASS,EAAKV,EAAMtB,EAAO4B,EAAkBL,GAC3C,GAAqB,iBAAVvB,EACT,MAAM,IAAIiC,UAAU,yCAGtB,MAA2B,oBAAhBC,aAA+BlC,aAAiBkC,YAsI7D,SAAyBZ,EAAMa,EAAOC,EAAYb,GAGhD,GAFAY,EAAME,WAEFD,EAAa,GAAKD,EAAME,WAAaD,EACvC,MAAM,IAAIZ,WAAW,6BAGvB,GAAIW,EAAME,WAAaD,GAAcb,GAAU,GAC7C,MAAM,IAAIC,WAAW,6BAIrBW,OADiBG,IAAfF,QAAuCE,IAAXf,EACtB,IAAIE,WAAWU,QACHG,IAAXf,EACD,IAAIE,WAAWU,EAAOC,GAEtB,IAAIX,WAAWU,EAAOC,EAAYb,GAGxCJ,EAAOC,qBAETE,EAAOa,GACFT,UAAYP,EAAOR,UAGxBW,EAAOiB,EAAcjB,EAAMa,GAG7B,OAAOb,EAjKEkB,CAAgBlB,EAAMtB,EAAO4B,EAAkBL,GAGnC,iBAAVvB,EAgGb,SAAoBsB,EAAMmB,EAAQC,GACR,iBAAbA,GAAsC,KAAbA,IAClCA,EAAW,QAGb,IAAKvB,EAAOwB,WAAWD,GACrB,MAAM,IAAIT,UAAU,8CAGtB,IAAIV,EAAwC,EAA/Bc,EAAWI,EAAQC,GAE5BE,GADJtB,EAAOD,EAAaC,EAAMC,IACRsB,MAAMJ,EAAQC,GAE5BE,IAAWrB,IAIbD,EAAOA,EAAKwB,MAAM,EAAGF,IAGvB,OAAOtB,EAnHEyB,CAAWzB,EAAMtB,EAAO4B,GAgKnC,SAAoBN,EAAM0B,GACxB,GAAI7B,EAAO8B,SAASD,GAAM,CACxB,IAAIE,EAA4B,EAAtBC,EAAQH,EAAIzB,QAGtB,OAAoB,KAFpBD,EAAOD,EAAaC,EAAM4B,IAEjB3B,OACAD,GAGT0B,EAAII,KAAK9B,EAAM,EAAG,EAAG4B,GACd5B,GAGT,GAAI0B,EAAK,CACP,GAA2B,oBAAhBd,aAA+Bc,EAAIK,kBAAkBnB,aAAe,WAAYc,EACzF,MAA0B,iBAAfA,EAAIzB,SAigDN+B,EAjgDmCN,EAAIzB,SAkgDrC+B,EAjgDFjC,EAAaC,EAAM,GAGrBiB,EAAcjB,EAAM0B,GAG7B,GAAiB,WAAbA,EAAIO,MAAqBtC,EAAQ+B,EAAIQ,MACvC,OAAOjB,EAAcjB,EAAM0B,EAAIQ,MAy/CrC,IAAeF,EAr/Cb,MAAM,IAAIrB,UAAU,sFAxLbwB,CAAWnC,EAAMtB,GA6B1B,SAAS0D,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,oCACf,GAAI0B,EAAO,EAChB,MAAM,IAAInC,WAAW,wCA8BzB,SAASO,EAAYT,EAAMqC,GAIzB,GAHAD,EAAWC,GACXrC,EAAOD,EAAaC,EAAMqC,EAAO,EAAI,EAAoB,EAAhBR,EAAQQ,KAE5CxC,EAAOC,oBACV,IAAK,IAAIrC,EAAI,EAAGA,EAAI4E,IAAQ5E,EAC1BuC,EAAKvC,GAAK,EAId,OAAOuC,EA0CT,SAASiB,EAAcjB,EAAMa,GAC3B,IAAIZ,EAASY,EAAMZ,OAAS,EAAI,EAA4B,EAAxB4B,EAAQhB,EAAMZ,QAClDD,EAAOD,EAAaC,EAAMC,GAE1B,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,EAAQxC,GAAK,EAC/BuC,EAAKvC,GAAgB,IAAXoD,EAAMpD,GAGlB,OAAOuC,EAgET,SAAS6B,EAAQ5B,GAGf,GAAIA,GAAUL,IACZ,MAAM,IAAIM,WAAW,0DAAiEN,IAAa0C,SAAS,IAAM,UAGpH,OAAgB,EAATrC,EA8FT,SAASc,EAAWI,EAAQC,GAC1B,GAAIvB,EAAO8B,SAASR,GAClB,OAAOA,EAAOlB,OAGhB,GAA2B,oBAAhBW,aAA6D,mBAAvBA,YAAY2B,SAA0B3B,YAAY2B,OAAOpB,IAAWA,aAAkBP,aACrI,OAAOO,EAAOJ,WAGM,iBAAXI,IACTA,EAAS,GAAKA,GAGhB,IAAIS,EAAMT,EAAOlB,OACjB,GAAY,IAAR2B,EAAW,OAAO,EAItB,IAFA,IAAIY,GAAc,IAGhB,OAAQpB,GACN,IAAK,QACL,IAAK,SACL,IAAK,SACH,OAAOQ,EAET,IAAK,OACL,IAAK,QACL,UAAKZ,EACH,OAAOyB,EAAYtB,GAAQlB,OAE7B,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAa,EAAN2B,EAET,IAAK,MACH,OAAOA,IAAQ,EAEjB,IAAK,SACH,OAAOc,EAAcvB,GAAQlB,OAE/B,QACE,GAAIuC,EAAa,OAAOC,EAAYtB,GAAQlB,OAE5CmB,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,GAOtB,SAASI,EAAaxB,EAAUyB,EAAOC,GACrC,IAAIN,GAAc,EAalB,SANcxB,IAAV6B,GAAuBA,EAAQ,KACjCA,EAAQ,GAKNA,EAAQtC,KAAKN,OACf,MAAO,GAOT,SAJYe,IAAR8B,GAAqBA,EAAMvC,KAAKN,UAClC6C,EAAMvC,KAAKN,QAGT6C,GAAO,EACT,MAAO,GAOT,IAHAA,KAAS,KACTD,KAAW,GAGT,MAAO,GAKT,IAFKzB,IAAUA,EAAW,UAGxB,OAAQA,GACN,IAAK,MACH,OAAO2B,EAASxC,KAAMsC,EAAOC,GAE/B,IAAK,OACL,IAAK,QACH,OAAOE,EAAUzC,KAAMsC,EAAOC,GAEhC,IAAK,QACH,OAAOG,EAAW1C,KAAMsC,EAAOC,GAEjC,IAAK,SACL,IAAK,SACH,OAAOI,EAAY3C,KAAMsC,EAAOC,GAElC,IAAK,SACH,OAAOK,EAAY5C,KAAMsC,EAAOC,GAElC,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOM,EAAa7C,KAAMsC,EAAOC,GAEnC,QACE,GAAIN,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAYA,EAAW,IAAIuB,cAC3BH,GAAc,GAStB,SAASa,EAAKC,EAAGpE,EAAGrB,GAClB,IAAIJ,EAAI6F,EAAEpE,GACVoE,EAAEpE,GAAKoE,EAAEzF,GACTyF,EAAEzF,GAAKJ,EAgJT,SAAS8F,EAAqBxB,EAAQC,EAAKlB,EAAYM,EAAUoC,GAE/D,GAAsB,IAAlBzB,EAAO9B,OAAc,OAAQ,EAqBjC,GAnB0B,iBAAfa,GACTM,EAAWN,EACXA,EAAa,GACJA,EAAa,WACtBA,EAAa,WACJA,GAAc,aACvBA,GAAc,YAGhBA,GAAcA,EAEV2C,MAAM3C,KAERA,EAAa0C,EAAM,EAAIzB,EAAO9B,OAAS,GAIrCa,EAAa,IAAGA,EAAaiB,EAAO9B,OAASa,GAE7CA,GAAciB,EAAO9B,OAAQ,CAC/B,GAAIuD,EAAK,OAAQ,EAAO1C,EAAaiB,EAAO9B,OAAS,OAChD,GAAIa,EAAa,EAAG,CACzB,IAAI0C,EAAyB,OAAQ,EAA5B1C,EAAa,EASxB,GALmB,iBAARkB,IACTA,EAAMnC,EAAOa,KAAKsB,EAAKZ,IAIrBvB,EAAO8B,SAASK,GAElB,OAAmB,IAAfA,EAAI/B,QACE,EAGHyD,EAAa3B,EAAQC,EAAKlB,EAAYM,EAAUoC,GAClD,GAAmB,iBAARxB,EAGhB,OAFAA,GAAY,IAERnC,EAAOC,qBAA+D,mBAAjCK,WAAWd,UAAUsE,QACxDH,EACKrD,WAAWd,UAAUsE,QAAQ/F,KAAKmE,EAAQC,EAAKlB,GAE/CX,WAAWd,UAAUuE,YAAYhG,KAAKmE,EAAQC,EAAKlB,GAIvD4C,EAAa3B,EAAQ,CAACC,GAAMlB,EAAYM,EAAUoC,GAG3D,MAAM,IAAI7C,UAAU,wCAGtB,SAAS+C,EAAaG,EAAK7B,EAAKlB,EAAYM,EAAUoC,GACpD,IA2BI/F,EA3BAqG,EAAY,EACZC,EAAYF,EAAI5D,OAChB+D,EAAYhC,EAAI/B,OAEpB,QAAiBe,IAAbI,IAGe,UAFjBA,EAAW6C,OAAO7C,GAAUuB,gBAEY,UAAbvB,GAAqC,YAAbA,GAAuC,aAAbA,GAAyB,CACpG,GAAIyC,EAAI5D,OAAS,GAAK+B,EAAI/B,OAAS,EACjC,OAAQ,EAGV6D,EAAY,EACZC,GAAa,EACbC,GAAa,EACblD,GAAc,EAIlB,SAASoD,EAAKC,EAAK1G,GACjB,OAAkB,IAAdqG,EACKK,EAAI1G,GAEJ0G,EAAIC,aAAa3G,EAAIqG,GAMhC,GAAIN,EAAK,CACP,IAAIa,GAAc,EAElB,IAAK5G,EAAIqD,EAAYrD,EAAIsG,EAAWtG,IAClC,GAAIyG,EAAKL,EAAKpG,KAAOyG,EAAKlC,GAAqB,IAAhBqC,EAAoB,EAAI5G,EAAI4G,IAEzD,IADoB,IAAhBA,IAAmBA,EAAa5G,GAChCA,EAAI4G,EAAa,IAAML,EAAW,OAAOK,EAAaP,OAEtC,IAAhBO,IAAmB5G,GAAKA,EAAI4G,GAChCA,GAAc,OAMlB,IAFIvD,EAAakD,EAAYD,IAAWjD,EAAaiD,EAAYC,GAE5DvG,EAAIqD,EAAYrD,GAAK,EAAGA,IAAK,CAGhC,IAFA,IAAI6G,GAAQ,EAEHC,EAAI,EAAGA,EAAIP,EAAWO,IAC7B,GAAIL,EAAKL,EAAKpG,EAAI8G,KAAOL,EAAKlC,EAAKuC,GAAI,CACrCD,GAAQ,EACR,MAIJ,GAAIA,EAAO,OAAO7G,EAItB,OAAQ,EAeV,SAAS+G,EAASL,EAAKhD,EAAQsD,EAAQxE,GACrCwE,EAASC,OAAOD,IAAW,EAC3B,IAAIE,EAAYR,EAAIlE,OAASwE,EAExBxE,GAGHA,EAASyE,OAAOzE,IAEH0E,IACX1E,EAAS0E,GALX1E,EAAS0E,EAUX,IAAIC,EAASzD,EAAOlB,OACpB,GAAI2E,EAAS,GAAM,EAAG,MAAM,IAAIjE,UAAU,sBAEtCV,EAAS2E,EAAS,IACpB3E,EAAS2E,EAAS,GAGpB,IAAK,IAAInH,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAC/B,IAAIoH,EAASC,SAAS3D,EAAO4D,OAAW,EAAJtH,EAAO,GAAI,IAC/C,GAAIgG,MAAMoB,GAAS,OAAOpH,EAC1B0G,EAAIM,EAAShH,GAAKoH,EAGpB,OAAOpH,EAGT,SAASuH,EAAUb,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EAAWxC,EAAYtB,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GAG3E,SAASiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GACvC,OAAOgF,EA26BT,SAAsBE,GAGpB,IAFA,IAAIC,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,SAAUxC,EAEhC2H,EAAUC,KAAyB,IAApBF,EAAIG,WAAW7H,IAGhC,OAAO2H,EAn7BWG,CAAapE,GAASgD,EAAKM,EAAQxE,GAGvD,SAASuF,EAAYrB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GAGzC,SAASwF,EAAYtB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOgF,EAAWvC,EAAcvB,GAASgD,EAAKM,EAAQxE,GAGxD,SAASyF,EAAUvB,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EA06BT,SAAwBE,EAAKQ,GAI3B,IAHA,IAAI7H,EAAG8H,EAAIC,EACPT,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,WACjB0F,GAAS,GAAK,KADalI,EAEhCK,EAAIqH,EAAIG,WAAW7H,GACnBmI,EAAK9H,GAAK,EACV+H,EAAK/H,EAAI,IACTsH,EAAUC,KAAKQ,GACfT,EAAUC,KAAKO,GAGjB,OAAOR,EAv7BWU,CAAe3E,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GA+E9E,SAASkD,EAAYgB,EAAKtB,EAAOC,GAC/B,OAAc,IAAVD,GAAeC,IAAQqB,EAAIlE,OACtBR,EAAOsG,cAAc5B,GAErB1E,EAAOsG,cAAc5B,EAAI3C,MAAMqB,EAAOC,IAIjD,SAASE,EAAUmB,EAAKtB,EAAOC,GAC7BA,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAI3B,IAHA,IAAIoD,EAAM,GACNzI,EAAIoF,EAEDpF,EAAIqF,GAAK,CACd,IAKMqD,EAAYC,EAAWC,EAAYC,EALrCC,EAAYpC,EAAI1G,GAChB+I,EAAY,KACZC,EAAmBF,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAI,EAE5F,GAAI9I,EAAIgJ,GAAoB3D,EAG1B,OAAQ2D,GACN,KAAK,EACCF,EAAY,MACdC,EAAYD,GAGd,MAEF,KAAK,EAGyB,MAAV,KAFlBJ,EAAahC,EAAI1G,EAAI,OAGnB6I,GAA6B,GAAZC,IAAqB,EAAmB,GAAbJ,GAExB,MAClBK,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GAEQ,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,KACnCE,GAA6B,GAAZC,IAAoB,IAAoB,GAAbJ,IAAsB,EAAkB,GAAZC,GAEpD,OAAUE,EAAgB,OAAUA,EAAgB,SACtEE,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GACpB4I,EAAalC,EAAI1G,EAAI,GAEO,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,IAAsD,MAAV,IAAbC,KAClEC,GAA6B,GAAZC,IAAoB,IAAqB,GAAbJ,IAAsB,IAAmB,GAAZC,IAAqB,EAAmB,GAAbC,GAEjF,OAAUC,EAAgB,UAC5CE,EAAYF,GAOJ,OAAdE,GAGFA,EAAY,MACZC,EAAmB,GACVD,EAAY,QAErBA,GAAa,MACbN,EAAIb,KAAKmB,IAAc,GAAK,KAAQ,OACpCA,EAAY,MAAqB,KAAZA,GAGvBN,EAAIb,KAAKmB,GACT/I,GAAKgJ,EAGP,OAQF,SAA+BC,GAC7B,IAAI9E,EAAM8E,EAAWzG,OAErB,GAAI2B,GAAO+E,EACT,OAAO1C,OAAO2C,aAAaC,MAAM5C,OAAQyC,GAI3C,IAAIR,EAAM,GACNzI,EAAI,EAER,KAAOA,EAAImE,GACTsE,GAAOjC,OAAO2C,aAAaC,MAAM5C,OAAQyC,EAAWlF,MAAM/D,EAAGA,GAAKkJ,IAGpE,OAAOT,EAvBAY,CAAsBZ,GA1gC/BjJ,EAAQ4C,OAASA,EACjB5C,EAAQ8J,WAiUR,SAAoB9G,IACbA,GAAUA,IAEbA,EAAS,GAGX,OAAOJ,EAAOmH,OAAO/G,IAtUvBhD,EAAQgK,kBAAoB,GA0B5BpH,EAAOC,yBAAqDkB,IAA/BkG,EAAOpH,oBAAoCoH,EAAOpH,oBAO/E,WACE,IACE,IAAI+D,EAAM,IAAI1D,WAAW,GAOzB,OANA0D,EAAIzD,UAAY,CACdA,UAAWD,WAAWd,UACtB8H,IAAK,WACH,OAAO,KAGU,KAAdtD,EAAIsD,OACa,mBAAjBtD,EAAIuD,UACuB,IAAlCvD,EAAIuD,SAAS,EAAG,GAAGrG,WACnB,MAAOsG,GACP,OAAO,GApB0FC,GAKrGrK,EAAQ2C,WAAaA,IAuErBC,EAAO0H,SAAW,KAGlB1H,EAAO2H,SAAW,SAAU3D,GAE1B,OADAA,EAAIzD,UAAYP,EAAOR,UAChBwE,GA4BThE,EAAOa,KAAO,SAAUhC,EAAO4B,EAAkBL,GAC/C,OAAOS,EAAK,KAAMhC,EAAO4B,EAAkBL,IAGzCJ,EAAOC,sBACTD,EAAOR,UAAUe,UAAYD,WAAWd,UACxCQ,EAAOO,UAAYD,WAEG,oBAAX3B,QAA0BA,OAAOiJ,SAAW5H,EAAOrB,OAAOiJ,WAAa5H,GAEhF1B,OAAOC,eAAeyB,EAAQrB,OAAOiJ,QAAS,CAC5C/I,MAAO,KACPgJ,cAAc,KAmCpB7H,EAAOmH,MAAQ,SAAU3E,EAAMsF,EAAMvG,GACnC,OAvBF,SAAepB,EAAMqC,EAAMsF,EAAMvG,GAG/B,OAFAgB,EAAWC,GAEPA,GAAQ,EACHtC,EAAaC,EAAMqC,QAGfrB,IAAT2G,EAIyB,iBAAbvG,EAAwBrB,EAAaC,EAAMqC,GAAMsF,KAAKA,EAAMvG,GAAYrB,EAAaC,EAAMqC,GAAMsF,KAAKA,GAG/G5H,EAAaC,EAAMqC,GASnB2E,CAAM,KAAM3E,EAAMsF,EAAMvG,IAoBjCvB,EAAOY,YAAc,SAAU4B,GAC7B,OAAO5B,EAAY,KAAM4B,IAO3BxC,EAAO+H,gBAAkB,SAAUvF,GACjC,OAAO5B,EAAY,KAAM4B,IAqH3BxC,EAAO8B,SAAW,SAAkB2B,GAClC,QAAe,MAALA,IAAaA,EAAEuE,YAG3BhI,EAAOiI,QAAU,SAAiBC,EAAGzE,GACnC,IAAKzD,EAAO8B,SAASoG,KAAOlI,EAAO8B,SAAS2B,GAC1C,MAAM,IAAI3C,UAAU,6BAGtB,GAAIoH,IAAMzE,EAAG,OAAO,EAIpB,IAHA,IAAI0E,EAAID,EAAE9H,OACNgI,EAAI3E,EAAErD,OAEDxC,EAAI,EAAGmE,EAAMoE,KAAKC,IAAI+B,EAAGC,GAAIxK,EAAImE,IAAOnE,EAC/C,GAAIsK,EAAEtK,KAAO6F,EAAE7F,GAAI,CACjBuK,EAAID,EAAEtK,GACNwK,EAAI3E,EAAE7F,GACN,MAIJ,OAAIuK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAGTnI,EAAOwB,WAAa,SAAoBD,GACtC,OAAQ6C,OAAO7C,GAAUuB,eACvB,IAAK,MACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,SACL,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAO,EAET,QACE,OAAO,IAIb9C,EAAOqI,OAAS,SAAgBC,EAAMlI,GACpC,IAAKN,EAAQwI,GACX,MAAM,IAAIxH,UAAU,+CAGtB,GAAoB,IAAhBwH,EAAKlI,OACP,OAAOJ,EAAOmH,MAAM,GAGtB,IAAIvJ,EAEJ,QAAeuD,IAAXf,EAGF,IAFAA,EAAS,EAEJxC,EAAI,EAAGA,EAAI0K,EAAKlI,SAAUxC,EAC7BwC,GAAUkI,EAAK1K,GAAGwC,OAItB,IAAI8B,EAASlC,EAAOY,YAAYR,GAC5BmI,EAAM,EAEV,IAAK3K,EAAI,EAAGA,EAAI0K,EAAKlI,SAAUxC,EAAG,CAChC,IAAI0G,EAAMgE,EAAK1K,GAEf,IAAKoC,EAAO8B,SAASwC,GACnB,MAAM,IAAIxD,UAAU,+CAGtBwD,EAAIrC,KAAKC,EAAQqG,GACjBA,GAAOjE,EAAIlE,OAGb,OAAO8B,GAsDTlC,EAAOkB,WAAaA,EAyEpBlB,EAAOR,UAAUwI,WAAY,EAQ7BhI,EAAOR,UAAUgJ,OAAS,WACxB,IAAIzG,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAGpB,OAAO8C,MAGTV,EAAOR,UAAUiJ,OAAS,WACxB,IAAI1G,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUkJ,OAAS,WACxB,IAAI3G,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUiD,SAAW,WAC1B,IAAIrC,EAAuB,EAAdM,KAAKN,OAClB,OAAe,IAAXA,EAAqB,GACA,IAArBuI,UAAUvI,OAAqB+C,EAAUzC,KAAM,EAAGN,GAC/C2C,EAAaiE,MAAMtG,KAAMiI,YAGlC3I,EAAOR,UAAUoJ,OAAS,SAAgBnF,GACxC,IAAKzD,EAAO8B,SAAS2B,GAAI,MAAM,IAAI3C,UAAU,6BAC7C,OAAIJ,OAAS+C,GACsB,IAA5BzD,EAAOiI,QAAQvH,KAAM+C,IAG9BzD,EAAOR,UAAUqJ,QAAU,WACzB,IAAIvD,EAAM,GACNwD,EAAM1L,EAAQgK,kBAOlB,OALI1G,KAAKN,OAAS,IAChBkF,EAAM5E,KAAK+B,SAAS,MAAO,EAAGqG,GAAKC,MAAM,SAASC,KAAK,KACnDtI,KAAKN,OAAS0I,IAAKxD,GAAO,UAGzB,WAAaA,EAAM,KAG5BtF,EAAOR,UAAUyI,QAAU,SAAiBgB,EAAQjG,EAAOC,EAAKiG,EAAWC,GACzE,IAAKnJ,EAAO8B,SAASmH,GACnB,MAAM,IAAInI,UAAU,6BAmBtB,QAhBcK,IAAV6B,IACFA,EAAQ,QAGE7B,IAAR8B,IACFA,EAAMgG,EAASA,EAAO7I,OAAS,QAGfe,IAAd+H,IACFA,EAAY,QAGE/H,IAAZgI,IACFA,EAAUzI,KAAKN,QAGb4C,EAAQ,GAAKC,EAAMgG,EAAO7I,QAAU8I,EAAY,GAAKC,EAAUzI,KAAKN,OACtE,MAAM,IAAIC,WAAW,sBAGvB,GAAI6I,GAAaC,GAAWnG,GAASC,EACnC,OAAO,EAGT,GAAIiG,GAAaC,EACf,OAAQ,EAGV,GAAInG,GAASC,EACX,OAAO,EAOT,GAAIvC,OAASuI,EAAQ,OAAO,EAO5B,IANA,IAAId,GAFJgB,KAAa,IADbD,KAAe,GAIXd,GALJnF,KAAS,IADTD,KAAW,GAOPjB,EAAMoE,KAAKC,IAAI+B,EAAGC,GAClBgB,EAAW1I,KAAKiB,MAAMuH,EAAWC,GACjCE,EAAaJ,EAAOtH,MAAMqB,EAAOC,GAE5BrF,EAAI,EAAGA,EAAImE,IAAOnE,EACzB,GAAIwL,EAASxL,KAAOyL,EAAWzL,GAAI,CACjCuK,EAAIiB,EAASxL,GACbwK,EAAIiB,EAAWzL,GACf,MAIJ,OAAIuK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAqITnI,EAAOR,UAAU8J,SAAW,SAAkBnH,EAAKlB,EAAYM,GAC7D,OAAoD,IAA7Cb,KAAKoD,QAAQ3B,EAAKlB,EAAYM,IAGvCvB,EAAOR,UAAUsE,QAAU,SAAiB3B,EAAKlB,EAAYM,GAC3D,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAG/DvB,EAAOR,UAAUuE,YAAc,SAAqB5B,EAAKlB,EAAYM,GACnE,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAsD/DvB,EAAOR,UAAUkC,MAAQ,SAAeJ,EAAQsD,EAAQxE,EAAQmB,GAE9D,QAAeJ,IAAXyD,EACFrD,EAAW,OACXnB,EAASM,KAAKN,OACdwE,EAAS,OACJ,QAAezD,IAAXf,GAA0C,iBAAXwE,EACxCrD,EAAWqD,EACXxE,EAASM,KAAKN,OACdwE,EAAS,MACJ,KAAI2E,SAAS3E,GAYlB,MAAM,IAAIjE,MAAM,2EAXhBiE,GAAkB,EAEd2E,SAASnJ,IACXA,GAAkB,OACDe,IAAbI,IAAwBA,EAAW,UAEvCA,EAAWnB,EACXA,OAASe,GAOb,IAAI2D,EAAYpE,KAAKN,OAASwE,EAG9B,SAFezD,IAAXf,GAAwBA,EAAS0E,KAAW1E,EAAS0E,GAErDxD,EAAOlB,OAAS,IAAMA,EAAS,GAAKwE,EAAS,IAAMA,EAASlE,KAAKN,OACnE,MAAM,IAAIC,WAAW,0CAGlBkB,IAAUA,EAAW,QAG1B,IAFA,IAAIoB,GAAc,IAGhB,OAAQpB,GACN,IAAK,MACH,OAAOoD,EAASjE,KAAMY,EAAQsD,EAAQxE,GAExC,IAAK,OACL,IAAK,QACH,OAAO+E,EAAUzE,KAAMY,EAAQsD,EAAQxE,GAEzC,IAAK,QACH,OAAOiF,EAAW3E,KAAMY,EAAQsD,EAAQxE,GAE1C,IAAK,SACL,IAAK,SACH,OAAOuF,EAAYjF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,SAEH,OAAOwF,EAAYlF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOyF,EAAUnF,KAAMY,EAAQsD,EAAQxE,GAEzC,QACE,GAAIuC,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,IAKtB3C,EAAOR,UAAUgK,OAAS,WACxB,MAAO,CACLpH,KAAM,SACNC,KAAMoH,MAAMjK,UAAUmC,MAAM5D,KAAK2C,KAAKgJ,MAAQhJ,KAAM,KAkGxD,IAAIoG,EAAuB,KAoB3B,SAAS1D,EAAWkB,EAAKtB,EAAOC,GAC9B,IAAI0G,EAAM,GACV1G,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B+L,GAAOvF,OAAO2C,aAAsB,IAATzC,EAAI1G,IAGjC,OAAO+L,EAGT,SAAStG,EAAYiB,EAAKtB,EAAOC,GAC/B,IAAI0G,EAAM,GACV1G,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B+L,GAAOvF,OAAO2C,aAAazC,EAAI1G,IAGjC,OAAO+L,EAGT,SAASzG,EAASoB,EAAKtB,EAAOC,GAC5B,IAAIlB,EAAMuC,EAAIlE,SACT4C,GAASA,EAAQ,KAAGA,EAAQ,KAC5BC,GAAOA,EAAM,GAAKA,EAAMlB,KAAKkB,EAAMlB,GAGxC,IAFA,IAAI6H,EAAM,GAEDhM,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7BgM,GAAOC,EAAMvF,EAAI1G,IAGnB,OAAOgM,EAGT,SAASrG,EAAae,EAAKtB,EAAOC,GAIhC,IAHA,IAAI6G,EAAQxF,EAAI3C,MAAMqB,EAAOC,GACzBoD,EAAM,GAEDzI,EAAI,EAAGA,EAAIkM,EAAM1J,OAAQxC,GAAK,EACrCyI,GAAOjC,OAAO2C,aAAa+C,EAAMlM,GAAoB,IAAfkM,EAAMlM,EAAI,IAGlD,OAAOyI,EA4CT,SAAS0D,EAAYnF,EAAQoF,EAAK5J,GAChC,GAAIwE,EAAS,GAAM,GAAKA,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBACzD,GAAIuE,EAASoF,EAAM5J,EAAQ,MAAM,IAAIC,WAAW,yCA+IlD,SAAS4J,EAAS3F,EAAKzF,EAAO+F,EAAQoF,EAAKlB,EAAK1C,GAC9C,IAAKpG,EAAO8B,SAASwC,GAAM,MAAM,IAAIxD,UAAU,+CAC/C,GAAIjC,EAAQiK,GAAOjK,EAAQuH,EAAK,MAAM,IAAI/F,WAAW,qCACrD,GAAIuE,EAASoF,EAAM1F,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBAsDtD,SAAS6J,EAAkB5F,EAAKzF,EAAO+F,EAAQuF,GACzCtL,EAAQ,IAAGA,EAAQ,MAASA,EAAQ,GAExC,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,IAAMiB,EAAQ,KAAQ,GAAKsL,EAAevM,EAAI,EAAIA,MAAqC,GAA5BuM,EAAevM,EAAI,EAAIA,GAkCnG,SAASwM,EAAkB9F,EAAKzF,EAAO+F,EAAQuF,GACzCtL,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAE5C,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,GAAKiB,IAAuC,GAA5BsL,EAAevM,EAAI,EAAIA,GAAS,IAmKjE,SAASyM,EAAa/F,EAAKzF,EAAO+F,EAAQoF,EAAKlB,EAAK1C,GAClD,GAAIxB,EAASoF,EAAM1F,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBACpD,GAAIuE,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBAGvC,SAASiK,EAAWhG,EAAKzF,EAAO+F,EAAQuF,EAAcI,GAMpD,OALKA,GACHF,EAAa/F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQuF,EAAc,GAAI,GAC7CvF,EAAS,EAWlB,SAAS4F,EAAYlG,EAAKzF,EAAO+F,EAAQuF,EAAcI,GAMrD,OALKA,GACHF,EAAa/F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQuF,EAAc,GAAI,GAC7CvF,EAAS,EA5dlB5E,EAAOR,UAAUmC,MAAQ,SAAeqB,EAAOC,GAC7C,IAmBIwH,EAnBA1I,EAAMrB,KAAKN,OAqBf,IApBA4C,IAAUA,GAGE,GACVA,GAASjB,GACG,IAAGiB,EAAQ,GACdA,EAAQjB,IACjBiB,EAAQjB,IANVkB,OAAc9B,IAAR8B,EAAoBlB,IAAQkB,GASxB,GACRA,GAAOlB,GACG,IAAGkB,EAAM,GACVA,EAAMlB,IACfkB,EAAMlB,GAGJkB,EAAMD,IAAOC,EAAMD,GAGnBhD,EAAOC,qBACTwK,EAAS/J,KAAK6G,SAASvE,EAAOC,IACvB1C,UAAYP,EAAOR,cACrB,CACL,IAAIkL,EAAWzH,EAAMD,EACrByH,EAAS,IAAIzK,EAAO0K,OAAUvJ,GAE9B,IAAK,IAAIvD,EAAI,EAAGA,EAAI8M,IAAY9M,EAC9B6M,EAAO7M,GAAK8C,KAAK9C,EAAIoF,GAIzB,OAAOyH,GAYTzK,EAAOR,UAAUmL,WAAa,SAAoB/F,EAAQ1D,EAAYqJ,GACpE3F,GAAkB,EAClB1D,GAA0B,EACrBqJ,GAAUR,EAAYnF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACXgG,EAAM,EACNhN,EAAI,IAECA,EAAIsD,IAAe0J,GAAO,MACjCzI,GAAOzB,KAAKkE,EAAShH,GAAKgN,EAG5B,OAAOzI,GAGTnC,EAAOR,UAAUqL,WAAa,SAAoBjG,EAAQ1D,EAAYqJ,GACpE3F,GAAkB,EAClB1D,GAA0B,EAErBqJ,GACHR,EAAYnF,EAAQ1D,EAAYR,KAAKN,QAMvC,IAHA,IAAI+B,EAAMzB,KAAKkE,IAAW1D,GACtB0J,EAAM,EAEH1J,EAAa,IAAM0J,GAAO,MAC/BzI,GAAOzB,KAAKkE,IAAW1D,GAAc0J,EAGvC,OAAOzI,GAGTnC,EAAOR,UAAUsL,UAAY,SAAmBlG,EAAQ2F,GAEtD,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAGd5E,EAAOR,UAAUuL,aAAe,SAAsBnG,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,GAG5C5E,EAAOR,UAAU+E,aAAe,SAAsBK,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,EAAIlE,KAAKkE,EAAS,IAG3C5E,EAAOR,UAAUwL,aAAe,SAAsBpG,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,SACnCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,IAAyB,SAAnBlE,KAAKkE,EAAS,IAGzF5E,EAAOR,UAAUyL,aAAe,SAAsBrG,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACrB,SAAfM,KAAKkE,IAAuBlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,KAGpG5E,EAAOR,UAAU0L,UAAY,SAAmBtG,EAAQ1D,EAAYqJ,GAClE3F,GAAkB,EAClB1D,GAA0B,EACrBqJ,GAAUR,EAAYnF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACXgG,EAAM,EACNhN,EAAI,IAECA,EAAIsD,IAAe0J,GAAO,MACjCzI,GAAOzB,KAAKkE,EAAShH,GAAKgN,EAK5B,OADIzI,IADJyI,GAAO,OACSzI,GAAOgE,KAAKgF,IAAI,EAAG,EAAIjK,IAChCiB,GAGTnC,EAAOR,UAAU4L,UAAY,SAAmBxG,EAAQ1D,EAAYqJ,GAClE3F,GAAkB,EAClB1D,GAA0B,EACrBqJ,GAAUR,EAAYnF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAIxC,EAAIsD,EACJ0J,EAAM,EACNzI,EAAMzB,KAAKkE,IAAWhH,GAEnBA,EAAI,IAAMgN,GAAO,MACtBzI,GAAOzB,KAAKkE,IAAWhH,GAAKgN,EAK9B,OADIzI,IADJyI,GAAO,OACSzI,GAAOgE,KAAKgF,IAAI,EAAG,EAAIjK,IAChCiB,GAGTnC,EAAOR,UAAU6L,SAAW,SAAkBzG,EAAQ2F,GAEpD,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACtB,IAAfM,KAAKkE,IACyB,GAA5B,IAAOlE,KAAKkE,GAAU,GADKlE,KAAKkE,IAI1C5E,EAAOR,UAAU8L,YAAc,SAAqB1G,EAAQ2F,GACrDA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAU+L,YAAc,SAAqB3G,EAAQ2F,GACrDA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,EAAS,GAAKlE,KAAKkE,IAAW,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAUgM,YAAc,SAAqB5G,EAAQ2F,GAE1D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,IAG7F5E,EAAOR,UAAUiM,YAAc,SAAqB7G,EAAQ2F,GAE1D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,GAAKlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAG7F5E,EAAOR,UAAUkM,YAAc,SAAqB9G,EAAQ2F,GAE1D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUmM,YAAc,SAAqB/G,EAAQ2F,GAE1D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAG/C5E,EAAOR,UAAUoM,aAAe,SAAsBhH,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUqM,aAAe,SAAsBjH,EAAQ2F,GAE5D,OADKA,GAAUR,EAAYnF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAS/C5E,EAAOR,UAAUsM,YAAc,SAAqBjN,EAAO+F,EAAQ1D,EAAYqJ,IAC7E1L,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBqJ,IAEHN,EAASvJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAKgF,IAAI,EAAG,EAAIjK,GAAc,EACO,GAGtD,IAAI0J,EAAM,EACNhN,EAAI,EAGR,IAFA8C,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAe0J,GAAO,MACjClK,KAAKkE,EAAShH,GAAKiB,EAAQ+L,EAAM,IAGnC,OAAOhG,EAAS1D,GAGlBlB,EAAOR,UAAUuM,YAAc,SAAqBlN,EAAO+F,EAAQ1D,EAAYqJ,IAC7E1L,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBqJ,IAEHN,EAASvJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAKgF,IAAI,EAAG,EAAIjK,GAAc,EACO,GAGtD,IAAItD,EAAIsD,EAAa,EACjB0J,EAAM,EAGV,IAFAlK,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAMgN,GAAO,MACzBlK,KAAKkE,EAAShH,GAAKiB,EAAQ+L,EAAM,IAGnC,OAAOhG,EAAS1D,GAGlBlB,EAAOR,UAAUwM,WAAa,SAAoBnN,EAAO+F,EAAQ2F,GAM/D,OALA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,IAAM,GACjD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK8F,MAAMpN,IACpD6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAWlB5E,EAAOR,UAAU0M,cAAgB,SAAuBrN,EAAO+F,EAAQ2F,GAYrE,OAXA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BqL,EAAkBxJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU2M,cAAgB,SAAuBtN,EAAO+F,EAAQ2F,GAYrE,OAXA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBqL,EAAkBxJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAWlB5E,EAAOR,UAAU4M,cAAgB,SAAuBvN,EAAO+F,EAAQ2F,GAcrE,OAbA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,GAAkB,IAAR/F,GAEfuL,EAAkB1J,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU6M,cAAgB,SAAuBxN,EAAO+F,EAAQ2F,GAcrE,OAbA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBuL,EAAkB1J,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU8M,WAAa,SAAoBzN,EAAO+F,EAAQ1D,EAAYqJ,GAI3E,GAHA1L,GAASA,EACT+F,GAAkB,GAEb2F,EAAU,CACb,IAAIgC,EAAQpG,KAAKgF,IAAI,EAAG,EAAIjK,EAAa,GACzC+I,EAASvJ,KAAM7B,EAAO+F,EAAQ1D,EAAYqL,EAAQ,GAAIA,GAGxD,IAAI3O,EAAI,EACJgN,EAAM,EACN4B,EAAM,EAGV,IAFA9L,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAe0J,GAAO,MAC7B/L,EAAQ,GAAa,IAAR2N,GAAsC,IAAzB9L,KAAKkE,EAAShH,EAAI,KAC9C4O,EAAM,GAGR9L,KAAKkE,EAAShH,IAAMiB,EAAQ+L,GAAO,GAAK4B,EAAM,IAGhD,OAAO5H,EAAS1D,GAGlBlB,EAAOR,UAAUiN,WAAa,SAAoB5N,EAAO+F,EAAQ1D,EAAYqJ,GAI3E,GAHA1L,GAASA,EACT+F,GAAkB,GAEb2F,EAAU,CACb,IAAIgC,EAAQpG,KAAKgF,IAAI,EAAG,EAAIjK,EAAa,GACzC+I,EAASvJ,KAAM7B,EAAO+F,EAAQ1D,EAAYqL,EAAQ,GAAIA,GAGxD,IAAI3O,EAAIsD,EAAa,EACjB0J,EAAM,EACN4B,EAAM,EAGV,IAFA9L,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAMgN,GAAO,MACrB/L,EAAQ,GAAa,IAAR2N,GAAsC,IAAzB9L,KAAKkE,EAAShH,EAAI,KAC9C4O,EAAM,GAGR9L,KAAKkE,EAAShH,IAAMiB,EAAQ+L,GAAO,GAAK4B,EAAM,IAGhD,OAAO5H,EAAS1D,GAGlBlB,EAAOR,UAAUkN,UAAY,SAAmB7N,EAAO+F,EAAQ2F,GAO7D,OANA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,KAAO,KAClD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK8F,MAAMpN,IAChDA,EAAQ,IAAGA,EAAQ,IAAOA,EAAQ,GACtC6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAGlB5E,EAAOR,UAAUmN,aAAe,SAAsB9N,EAAO+F,EAAQ2F,GAYnE,OAXA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BqL,EAAkBxJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUoN,aAAe,SAAsB/N,EAAO+F,EAAQ2F,GAYnE,OAXA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBqL,EAAkBxJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUqN,aAAe,SAAsBhO,EAAO+F,EAAQ2F,GAcnE,OAbA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YAEzD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,IAE7BuL,EAAkB1J,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUsN,aAAe,SAAsBjO,EAAO+F,EAAQ2F,GAenE,OAdA1L,GAASA,EACT+F,GAAkB,EACb2F,GAAUN,EAASvJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YACzD/F,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAExCmB,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBuL,EAAkB1J,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAiBlB5E,EAAOR,UAAUuN,aAAe,SAAsBlO,EAAO+F,EAAQ2F,GACnE,OAAOD,EAAW5J,KAAM7B,EAAO+F,GAAQ,EAAM2F,IAG/CvK,EAAOR,UAAUwN,aAAe,SAAsBnO,EAAO+F,EAAQ2F,GACnE,OAAOD,EAAW5J,KAAM7B,EAAO+F,GAAQ,EAAO2F,IAYhDvK,EAAOR,UAAUyN,cAAgB,SAAuBpO,EAAO+F,EAAQ2F,GACrE,OAAOC,EAAY9J,KAAM7B,EAAO+F,GAAQ,EAAM2F,IAGhDvK,EAAOR,UAAU0N,cAAgB,SAAuBrO,EAAO+F,EAAQ2F,GACrE,OAAOC,EAAY9J,KAAM7B,EAAO+F,GAAQ,EAAO2F,IAIjDvK,EAAOR,UAAUyC,KAAO,SAAcgH,EAAQkE,EAAanK,EAAOC,GAOhE,GANKD,IAAOA,EAAQ,GACfC,GAAe,IAARA,IAAWA,EAAMvC,KAAKN,QAC9B+M,GAAelE,EAAO7I,SAAQ+M,EAAclE,EAAO7I,QAClD+M,IAAaA,EAAc,GAC5BlK,EAAM,GAAKA,EAAMD,IAAOC,EAAMD,GAE9BC,IAAQD,EAAO,OAAO,EAC1B,GAAsB,IAAlBiG,EAAO7I,QAAgC,IAAhBM,KAAKN,OAAc,OAAO,EAErD,GAAI+M,EAAc,EAChB,MAAM,IAAI9M,WAAW,6BAGvB,GAAI2C,EAAQ,GAAKA,GAAStC,KAAKN,OAAQ,MAAM,IAAIC,WAAW,6BAC5D,GAAI4C,EAAM,EAAG,MAAM,IAAI5C,WAAW,2BAE9B4C,EAAMvC,KAAKN,SAAQ6C,EAAMvC,KAAKN,QAE9B6I,EAAO7I,OAAS+M,EAAclK,EAAMD,IACtCC,EAAMgG,EAAO7I,OAAS+M,EAAcnK,GAGtC,IACIpF,EADAmE,EAAMkB,EAAMD,EAGhB,GAAItC,OAASuI,GAAUjG,EAAQmK,GAAeA,EAAclK,EAE1D,IAAKrF,EAAImE,EAAM,EAAGnE,GAAK,IAAKA,EAC1BqL,EAAOrL,EAAIuP,GAAezM,KAAK9C,EAAIoF,QAEhC,GAAIjB,EAAM,MAAS/B,EAAOC,oBAE/B,IAAKrC,EAAI,EAAGA,EAAImE,IAAOnE,EACrBqL,EAAOrL,EAAIuP,GAAezM,KAAK9C,EAAIoF,QAGrC1C,WAAWd,UAAU4N,IAAIrP,KAAKkL,EAAQvI,KAAK6G,SAASvE,EAAOA,EAAQjB,GAAMoL,GAG3E,OAAOpL,GAOT/B,EAAOR,UAAUsI,KAAO,SAAc3F,EAAKa,EAAOC,EAAK1B,GAErD,GAAmB,iBAARY,EAAkB,CAU3B,GATqB,iBAAVa,GACTzB,EAAWyB,EACXA,EAAQ,EACRC,EAAMvC,KAAKN,QACa,iBAAR6C,IAChB1B,EAAW0B,EACXA,EAAMvC,KAAKN,QAGM,IAAf+B,EAAI/B,OAAc,CACpB,IAAIiN,EAAOlL,EAAIsD,WAAW,GAEtB4H,EAAO,MACTlL,EAAMkL,GAIV,QAAiBlM,IAAbI,GAA8C,iBAAbA,EACnC,MAAM,IAAIT,UAAU,6BAGtB,GAAwB,iBAAbS,IAA0BvB,EAAOwB,WAAWD,GACrD,MAAM,IAAIT,UAAU,qBAAuBS,OAErB,iBAARY,IAChBA,GAAY,KAId,GAAIa,EAAQ,GAAKtC,KAAKN,OAAS4C,GAAStC,KAAKN,OAAS6C,EACpD,MAAM,IAAI5C,WAAW,sBAGvB,GAAI4C,GAAOD,EACT,OAAOtC,KAMT,IAAI9C,EAEJ,GALAoF,KAAkB,EAClBC,OAAc9B,IAAR8B,EAAoBvC,KAAKN,OAAS6C,IAAQ,EAC3Cd,IAAKA,EAAM,GAGG,iBAARA,EACT,IAAKvE,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EACzB8C,KAAK9C,GAAKuE,MAEP,CACL,IAAI2H,EAAQ9J,EAAO8B,SAASK,GAAOA,EAAMS,EAAY,IAAI5C,EAAOmC,EAAKZ,GAAUkB,YAC3EV,EAAM+H,EAAM1J,OAEhB,IAAKxC,EAAI,EAAGA,EAAIqF,EAAMD,IAASpF,EAC7B8C,KAAK9C,EAAIoF,GAAS8G,EAAMlM,EAAImE,GAIhC,OAAOrB,MAKT,IAAI4M,EAAoB,qBAoBxB,SAASzD,EAAMxK,GACb,OAAIA,EAAI,GAAW,IAAMA,EAAEoD,SAAS,IAC7BpD,EAAEoD,SAAS,IAGpB,SAASG,EAAYtB,EAAQwE,GAE3B,IAAIa,EADJb,EAAQA,GAASyH,IAMjB,IAJA,IAAInN,EAASkB,EAAOlB,OAChBoN,EAAgB,KAChB1D,EAAQ,GAEHlM,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAG/B,IAFA+I,EAAYrF,EAAOmE,WAAW7H,IAEd,OAAU+I,EAAY,MAAQ,CAE5C,IAAK6G,EAAe,CAElB,GAAI7G,EAAY,MAAQ,EAEjBb,GAAS,IAAM,GAAGgE,EAAMtE,KAAK,IAAM,IAAM,KAC9C,SACK,GAAI5H,EAAI,IAAMwC,EAAQ,EAEtB0F,GAAS,IAAM,GAAGgE,EAAMtE,KAAK,IAAM,IAAM,KAC9C,SAIFgI,EAAgB7G,EAChB,SAIF,GAAIA,EAAY,MAAQ,EACjBb,GAAS,IAAM,GAAGgE,EAAMtE,KAAK,IAAM,IAAM,KAC9CgI,EAAgB7G,EAChB,SAIFA,EAAkE,OAArD6G,EAAgB,OAAU,GAAK7G,EAAY,YAC/C6G,IAEJ1H,GAAS,IAAM,GAAGgE,EAAMtE,KAAK,IAAM,IAAM,KAKhD,GAFAgI,EAAgB,KAEZ7G,EAAY,IAAM,CACpB,IAAKb,GAAS,GAAK,EAAG,MACtBgE,EAAMtE,KAAKmB,QACN,GAAIA,EAAY,KAAO,CAC5B,IAAKb,GAAS,GAAK,EAAG,MACtBgE,EAAMtE,KAAKmB,GAAa,EAAM,IAAkB,GAAZA,EAAmB,UAClD,GAAIA,EAAY,MAAS,CAC9B,IAAKb,GAAS,GAAK,EAAG,MACtBgE,EAAMtE,KAAKmB,GAAa,GAAM,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,SAClF,MAAIA,EAAY,SAIrB,MAAM,IAAIhG,MAAM,sBAHhB,IAAKmF,GAAS,GAAK,EAAG,MACtBgE,EAAMtE,KAAKmB,GAAa,GAAO,IAAMA,GAAa,GAAM,GAAO,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,MAM5H,OAAOmD,EA8BT,SAASjH,EAAcyC,GACrB,OAAO1F,EAAO6N,YApHhB,SAAqBnI,GAInB,IAFAA,EAWF,SAAoBA,GAClB,OAAIA,EAAIoI,KAAapI,EAAIoI,OAClBpI,EAAIqI,QAAQ,aAAc,IAb3BC,CAAWtI,GAAKqI,QAAQL,EAAmB,KAEzClN,OAAS,EAAG,MAAO,GAE3B,KAAOkF,EAAIlF,OAAS,GAAM,GACxBkF,GAAY,IAGd,OAAOA,EA0GmBuI,CAAYvI,IAGxC,SAASF,EAAW0I,EAAKC,EAAKnJ,EAAQxE,GACpC,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,KACdxC,EAAIgH,GAAUmJ,EAAI3N,QAAUxC,GAAKkQ,EAAI1N,UADbxC,EAE5BmQ,EAAInQ,EAAIgH,GAAUkJ,EAAIlQ,GAGxB,OAAOA,K,8CCrzDTP,EAAOD,QAAU,CACf4Q,OAAQ,EAAQ,IAChBC,OAAQ,EAAQ,IAChBC,eAAgB,EAAQ,M,8BCL1B,YACA,MAAMC,EAASC,EAAQ,GAUvB,SAASC,EAAgB/J,GACvB,OAAOW,SAASX,EAAI7B,SAAS,OAAQ,IAGvC,SAAS6L,EAAgBC,GACvB,IAAIC,EAAYD,EAAI9L,SAAS,IAI7B,OAHI+L,EAAUpO,OAAS,GAAM,IAC3BoO,EAAY,IAAMA,GAEbxO,EAAOa,KAAK2N,EAAW,OAjBhCnR,EAAOD,QAAU,CACfkR,iBACAD,iBACAI,mBAiBF,SAA6BC,GAC3B,OAAO1O,EAAOa,KAAKsN,EAAOH,OAAOK,EAAeK,MAjBhDC,mBAoBF,SAA6BD,GAC3B,OAAOJ,EAAeH,EAAOF,OAAOS,KApBpCE,aAuBF,SAAuBL,GACrB,OAAOvO,EAAOa,KAAKsN,EAAOH,OAAOO,Q,sFChCnC,YAaA,MAAMJ,EAASC,EAAQ,GACjBS,EAAWT,EAAQ,IACnBU,EAAwBV,EAAQ,IAChCW,EAAOX,EAAQ,IAErBhR,EAAUC,EAAOD,SAST4R,UAAY,CAACC,EAAqB5M,KACxC,IAAI6M,EAEJ,GAAIlP,EAAO8B,SAASmN,GAClBC,EAASH,EAAKN,mBAAmBQ,OAC5B,CACL,IAAIH,EAAsBG,GAGxB,MAAM,IAAItO,MAAM,6BAFhBuO,EAASJ,EAAsBG,GAKnC,OAAOjP,EAAOqI,OAAO,CAAC6G,EAAQ7M,KAShCjF,EAAQ+R,SAAY9M,IAClB8L,EAAOF,OAAO5L,GACPA,EAAKV,MAAMwM,EAAOF,OAAOnE,QAQlC1M,EAAQgS,SAAYC,IAClB,MAAMhC,EAAOc,EAAOF,OAAOoB,GACrBC,EAAYT,EAASpQ,IAAI4O,GAC/B,QAAkBlM,IAAdmO,EACF,MAAM,IAAI3O,MAAJ,eAAkB0M,EAAlB,eAER,OAAOiC,GAQTlS,EAAQmS,QAAWC,GACVX,EAASpQ,IAAI+Q,GAQtBpS,EAAQqS,UAAatR,IACnB,MAAMkP,EAAOyB,EAAsB3Q,GACnC,QAAagD,IAATkM,EACF,MAAM,IAAI1M,MAAM,UAAYxC,EAAO,eAErC,OAAO4Q,EAAKJ,mBAAmBtB,GAAM,IAQvCjQ,EAAQsS,QAAWL,GACVlB,EAAOF,OAAOoB,GAQvBjS,EAAQuS,cAAiBL,IACvB,MAAMjC,EAAOyB,EAAsBQ,GACnC,QAAanO,IAATkM,EACF,MAAM,IAAI1M,MAAM,UAAY2O,EAAY,eAE1C,OAAOjC,GAQTjQ,EAAQwS,UAAavC,GACZc,EAAOH,OAAOX,GAIvB,MAAMwC,EAAYzB,EAAQ,IAC1B9P,OAAOwR,OAAO1S,EAASyS,GAGvBzS,EAAQ2S,MAAQ3B,EAAQ,M,qDC3HxB,IAAI4B,EAEJA,EAAI,WACF,OAAOtP,KADL,GAIJ,IAEEsP,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACT,MAAOzI,GAEe,iBAAXhK,SAAqBwS,EAAIxS,QAMtCH,EAAOD,QAAU4S,G,6BCjBjB5S,EAAQ8D,WAkCR,SAAoBgP,GAClB,IAAIC,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAC3B,OAAsC,GAA9BE,EAAWC,GAAuB,EAAIA,GArChDlT,EAAQqQ,YA4CR,SAAqByC,GACnB,IAAIK,EAQA3S,EAPAuS,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GACvBnM,EAAM,IAAIwM,EAThB,SAAqBN,EAAKG,EAAUC,GAClC,OAAsC,GAA9BD,EAAWC,GAAuB,EAAIA,EAQ5BG,CAAYP,EAAKG,EAAUC,IACzCI,EAAU,EAEV3O,EAAMuO,EAAkB,EAAID,EAAW,EAAIA,EAG/C,IAAKzS,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EACxB2S,EAAMI,EAAUT,EAAIzK,WAAW7H,KAAO,GAAK+S,EAAUT,EAAIzK,WAAW7H,EAAI,KAAO,GAAK+S,EAAUT,EAAIzK,WAAW7H,EAAI,KAAO,EAAI+S,EAAUT,EAAIzK,WAAW7H,EAAI,IACzJoG,EAAI0M,KAAaH,GAAO,GAAK,IAC7BvM,EAAI0M,KAAaH,GAAO,EAAI,IAC5BvM,EAAI0M,KAAmB,IAANH,EAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAIzK,WAAW7H,KAAO,EAAI+S,EAAUT,EAAIzK,WAAW7H,EAAI,KAAO,EAC9EoG,EAAI0M,KAAmB,IAANH,GAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAIzK,WAAW7H,KAAO,GAAK+S,EAAUT,EAAIzK,WAAW7H,EAAI,KAAO,EAAI+S,EAAUT,EAAIzK,WAAW7H,EAAI,KAAO,EACvHoG,EAAI0M,KAAaH,GAAO,EAAI,IAC5BvM,EAAI0M,KAAmB,IAANH,GAGnB,OAAOvM,GAxET5G,EAAQ8I,cA2FR,SAAuB0K,GASrB,IARA,IAAIL,EACAxO,EAAM6O,EAAMxQ,OACZyQ,EAAa9O,EAAM,EAEnB+O,EAAQ,GAIHlT,EAAI,EAAGmT,EAAOhP,EAAM8O,EAAYjT,EAAImT,EAAMnT,GAH9B,MAInBkT,EAAMtL,KAAKwL,EAAYJ,EAAOhT,EAAGA,EAJd,MAImCmT,EAAOA,EAAOnT,EAJjD,QAQF,IAAfiT,GACFN,EAAMK,EAAM7O,EAAM,GAClB+O,EAAMtL,KAAKyL,EAAOV,GAAO,GAAKU,EAAOV,GAAO,EAAI,IAAQ,OAChC,IAAfM,IACTN,GAAOK,EAAM7O,EAAM,IAAM,GAAK6O,EAAM7O,EAAM,GAC1C+O,EAAMtL,KAAKyL,EAAOV,GAAO,IAAMU,EAAOV,GAAO,EAAI,IAAQU,EAAOV,GAAO,EAAI,IAAQ,MAGrF,OAAOO,EAAM9H,KAAK,KA3GpB,IALA,IAAIiI,EAAS,GACTN,EAAY,GACZH,EAA4B,oBAAflQ,WAA6BA,WAAamJ,MACvD4D,EAAO,mEAEFzP,EAAI,EAAGmE,EAAMsL,EAAKjN,OAAQxC,EAAImE,IAAOnE,EAC5CqT,EAAOrT,GAAKyP,EAAKzP,GACjB+S,EAAUtD,EAAK5H,WAAW7H,IAAMA,EAQlC,SAASwS,EAAQF,GACf,IAAInO,EAAMmO,EAAI9P,OAEd,GAAI2B,EAAM,EAAI,EACZ,MAAM,IAAIpB,MAAM,kDAKlB,IAAI0P,EAAWH,EAAIpM,QAAQ,KAG3B,OAFkB,IAAduM,IAAiBA,EAAWtO,GAEzB,CAACsO,EADcA,IAAatO,EAAM,EAAI,EAAIsO,EAAW,GAoD9D,SAASW,EAAYJ,EAAO5N,EAAOC,GAIjC,IAHA,IAAIsN,EALmBhC,EAMnB2C,EAAS,GAEJtT,EAAIoF,EAAOpF,EAAIqF,EAAKrF,GAAK,EAChC2S,GAAOK,EAAMhT,IAAM,GAAK,WAAagT,EAAMhT,EAAI,IAAM,EAAI,QAA0B,IAAfgT,EAAMhT,EAAI,IAC9EsT,EAAO1L,KATFyL,GADgB1C,EAUOgC,IATT,GAAK,IAAQU,EAAO1C,GAAO,GAAK,IAAQ0C,EAAO1C,GAAO,EAAI,IAAQ0C,EAAa,GAAN1C,IAY9F,OAAO2C,EAAOlI,KAAK,IA3ErB2H,EAAU,IAAIlL,WAAW,IAAM,GAC/BkL,EAAU,IAAIlL,WAAW,IAAM,I,6BChB/BrI,EAAQiH,KAAO,SAAUnC,EAAQ0C,EAAQuM,EAAMC,EAAMC,GACnD,IAAI7J,EAAGxJ,EACHsT,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBE,GAAS,EACT7T,EAAIuT,EAAOE,EAAS,EAAI,EACxBnT,EAAIiT,GAAQ,EAAI,EAChBxR,EAAIuC,EAAO0C,EAAShH,GAMxB,IALAA,GAAKM,EACLsJ,EAAI7H,GAAK,IAAM8R,GAAS,EACxB9R,KAAO8R,EACPA,GAASH,EAEFG,EAAQ,EAAGjK,EAAQ,IAAJA,EAAUtF,EAAO0C,EAAShH,GAAIA,GAAKM,EAAGuT,GAAS,GAMrE,IAJAzT,EAAIwJ,GAAK,IAAMiK,GAAS,EACxBjK,KAAOiK,EACPA,GAASL,EAEFK,EAAQ,EAAGzT,EAAQ,IAAJA,EAAUkE,EAAO0C,EAAShH,GAAIA,GAAKM,EAAGuT,GAAS,GAErE,GAAU,IAANjK,EACFA,EAAI,EAAIgK,MACH,IAAIhK,IAAM+J,EACf,OAAOvT,EAAI0T,IAAqBnE,KAAd5N,GAAK,EAAI,GAE3B3B,GAAQmI,KAAKgF,IAAI,EAAGiG,GACpB5J,GAAQgK,EAGV,OAAQ7R,GAAK,EAAI,GAAK3B,EAAImI,KAAKgF,IAAI,EAAG3D,EAAI4J,IAG5ChU,EAAQsE,MAAQ,SAAUQ,EAAQrD,EAAO+F,EAAQuM,EAAMC,EAAMC,GAC3D,IAAI7J,EAAGxJ,EAAGC,EACNqT,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBI,EAAc,KAATP,EAAcjL,KAAKgF,IAAI,GAAI,IAAMhF,KAAKgF,IAAI,GAAI,IAAM,EACzDvN,EAAIuT,EAAO,EAAIE,EAAS,EACxBnT,EAAIiT,EAAO,GAAK,EAChBxR,EAAId,EAAQ,GAAe,IAAVA,GAAe,EAAIA,EAAQ,EAAI,EAAI,EAqCxD,IApCAA,EAAQsH,KAAKyL,IAAI/S,GAEb+E,MAAM/E,IAAUA,IAAU0O,KAC5BvP,EAAI4F,MAAM/E,GAAS,EAAI,EACvB2I,EAAI+J,IAEJ/J,EAAIrB,KAAK8F,MAAM9F,KAAK0L,IAAIhT,GAASsH,KAAK2L,KAElCjT,GAASZ,EAAIkI,KAAKgF,IAAI,GAAI3D,IAAM,IAClCA,IACAvJ,GAAK,IAILY,GADE2I,EAAIgK,GAAS,EACNG,EAAK1T,EAEL0T,EAAKxL,KAAKgF,IAAI,EAAG,EAAIqG,IAGpBvT,GAAK,IACfuJ,IACAvJ,GAAK,GAGHuJ,EAAIgK,GAASD,GACfvT,EAAI,EACJwJ,EAAI+J,GACK/J,EAAIgK,GAAS,GACtBxT,GAAKa,EAAQZ,EAAI,GAAKkI,KAAKgF,IAAI,EAAGiG,GAClC5J,GAAQgK,IAERxT,EAAIa,EAAQsH,KAAKgF,IAAI,EAAGqG,EAAQ,GAAKrL,KAAKgF,IAAI,EAAGiG,GACjD5J,EAAI,IAID4J,GAAQ,EAAGlP,EAAO0C,EAAShH,GAAS,IAAJI,EAAUJ,GAAKM,EAAGF,GAAK,IAAKoT,GAAQ,GAK3E,IAHA5J,EAAIA,GAAK4J,EAAOpT,EAChBsT,GAAQF,EAEDE,EAAO,EAAGpP,EAAO0C,EAAShH,GAAS,IAAJ4J,EAAU5J,GAAKM,EAAGsJ,GAAK,IAAK8J,GAAQ,GAE1EpP,EAAO0C,EAAShH,EAAIM,IAAU,IAAJyB,I,6BCtF5B,IAAI8C,EAAW,GAAGA,SAElBpF,EAAOD,QAAUqM,MAAM3J,SAAW,SAAUkE,GAC1C,MAA6B,kBAAtBvB,EAAS1E,KAAKiG,K,6BCHvB3G,EAAOD,QAMP,SAAS4Q,EAAOO,EAAK3E,EAAKhF,GACxBgF,EAAMA,GAAO,GAEb,IAAImI,EADJnN,EAASA,GAAU,EAGnB,KAAO2J,GAAOyD,GACZpI,EAAIhF,KAAkB,IAAN2J,EAAa0D,EAC7B1D,GAAO,IAGT,KAAOA,EAAM2D,GACXtI,EAAIhF,KAAkB,IAAN2J,EAAa0D,EAC7B1D,KAAS,EAKX,OAFA3E,EAAIhF,GAAgB,EAAN2J,EACdP,EAAOlE,MAAQlF,EAASmN,EAAY,EAC7BnI,GAtBT,IAAIqI,EAAM,IAENC,GAAS,IACTF,EAAM7L,KAAKgF,IAAI,EAAG,K,6BCJtB9N,EAAOD,QAIP,SAASiH,EAAKC,EAAKM,GACjB,IAIInB,EAJA4C,EAAM,EAEN8L,EAAQ,EACRC,EAFAxN,EAASA,GAAU,EAInB/G,EAAIyG,EAAIlE,OAEZ,EAAG,CACD,GAAIgS,GAAWvU,EAEb,MADAwG,EAAKyF,MAAQ,EACP,IAAIzJ,WAAW,2BAGvBoD,EAAIa,EAAI8N,KACR/L,GAAO8L,EAAQ,IAAM1O,EAAI4O,IAASF,GAAS1O,EAAI4O,GAAQlM,KAAKgF,IAAI,EAAGgH,GACnEA,GAAS,QACF1O,GAAKwO,GAGd,OADA5N,EAAKyF,MAAQsI,EAAUxN,EAChByB,GAvBT,IAAI4L,EAAM,IACNI,EAAO,K,6BCFX,IAAIC,EAAKnM,KAAKgF,IAAI,EAAG,GACjBoH,EAAKpM,KAAKgF,IAAI,EAAG,IACjBqH,EAAKrM,KAAKgF,IAAI,EAAG,IACjBsH,EAAKtM,KAAKgF,IAAI,EAAG,IACjBuH,EAAKvM,KAAKgF,IAAI,EAAG,IACjBwH,EAAKxM,KAAKgF,IAAI,EAAG,IACjByH,EAAKzM,KAAKgF,IAAI,EAAG,IACjB0H,EAAK1M,KAAKgF,IAAI,EAAG,IACjB2H,EAAK3M,KAAKgF,IAAI,EAAG,IAErB9N,EAAOD,QAAU,SAAUyB,GACzB,OAAOA,EAAQyT,EAAK,EAAIzT,EAAQ0T,EAAK,EAAI1T,EAAQ2T,EAAK,EAAI3T,EAAQ4T,EAAK,EAAI5T,EAAQ6T,EAAK,EAAI7T,EAAQ8T,EAAK,EAAI9T,EAAQ+T,EAAK,EAAI/T,EAAQgU,EAAK,EAAIhU,EAAQiU,EAAK,EAAI,K,6BCZlK,MAAMC,EAAY3E,EAAQ,GAGpB4E,EAAY,IAAIC,IAEtB,IAAK,MAAMC,KAAgBH,EAAW,CACpC,MAAM1F,EAAO0F,EAAUG,GACvBF,EAAU5F,IAAIC,EAAM6F,GAGtB7V,EAAOD,QAAUkB,OAAO6U,OAAOH,I,6BCT/B,MAAMD,EAAY3E,EAAQ,GACpBQ,EAAeR,EAAQ,GAAUQ,aAGjCwE,EAAc,GAEpB,IAAK,MAAMF,KAAgBH,EAAW,CACpC,MAAM1F,EAAO0F,EAAUG,GACvBE,EAAYF,GAAgBtE,EAAavB,GAG3ChQ,EAAOD,QAAUkB,OAAO6U,OAAOC,I,6BCX/B,MAAMC,EAAQjF,EAAQ,GAGhByB,EAAY,GAElB,IAAK,MAAO1R,EAAMkP,KAAS/O,OAAOgV,QAAQD,GACxCxD,EAAU1R,EAAKoV,cAAc5F,QAAQ,KAAM,MAAQN,EAGrDhQ,EAAOD,QAAUkB,OAAO6U,OAAOtD,I,6BCT/B,MAAMwD,EAAQjF,EAAQ,GAGhBoF,EAAc,GAEpB,IAAK,MAAOrV,EAAMkP,KAAS/O,OAAOgV,QAAQD,QACdlS,IAAtBqS,EAAYnG,KAAqBmG,EAAYnG,GAAQlP,GAG3Dd,EAAOD,QAAUkB,OAAO6U,OAAOK", "file": "index.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Multicodec\"] = factory();\n\telse\n\t\troot[\"Multicodec\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 4);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "\"use strict\";\n\nmodule.exports = {\n  encode: require('./encode.js'),\n  decode: require('./decode.js'),\n  encodingLength: require('./length.js')\n};", "'use strict'\nconst varint = require('varint')\n\nmodule.exports = {\n  numberToBuffer,\n  bufferToNumber,\n  varintBufferEncode,\n  varintBufferDecode,\n  varintEncode\n}\n\nfunction bufferToNumber (buf) {\n  return parseInt(buf.toString('hex'), 16)\n}\n\nfunction numberToBuffer (num) {\n  let hexString = num.toString(16)\n  if (hexString.length % 2 === 1) {\n    hexString = '0' + hexString\n  }\n  return Buffer.from(hexString, 'hex')\n}\n\nfunction varintBufferEncode (input) {\n  return Buffer.from(varint.encode(bufferToNumber(input)))\n}\n\nfunction varintBufferDecode (input) {\n  return numberToBuffer(varint.decode(input))\n}\n\nfunction varintEncode (num) {\n  return Buffer.from(varint.encode(num))\n}\n", "/**\n * Implementation of the multicodec specification.\n *\n * @module multicodec\n * @example\n * const multicodec = require('multicodec')\n *\n * const prefixedProtobuf = multicodec.addPrefix('protobuf', protobufBuffer)\n * // prefixedProtobuf 0x50...\n *\n */\n'use strict'\n\nconst varint = require('varint')\nconst intTable = require('./int-table')\nconst codecNameToCodeVarint = require('./varint-table')\nconst util = require('./util')\n\nexports = module.exports\n\n/**\n * Prefix a buffer with a multicodec-packed.\n *\n * @param {string|number} multicodecStrOrCode\n * @param {Buffer} data\n * @returns {Buffer}\n */\nexports.addPrefix = (multicodecStrOrCode, data) => {\n  let prefix\n\n  if (Buffer.isBuffer(multicodecStrOrCode)) {\n    prefix = util.varintBufferEncode(multicodecStrOrCode)\n  } else {\n    if (codecNameToCodeVarint[multicodecStrOrCode]) {\n      prefix = codecNameToCodeVarint[multicodecStrOrCode]\n    } else {\n      throw new Error('multicodec not recognized')\n    }\n  }\n  return Buffer.concat([prefix, data])\n}\n\n/**\n * Decapsulate the multicodec-packed prefix from the data.\n *\n * @param {Buffer} data\n * @returns {Buffer}\n */\nexports.rmPrefix = (data) => {\n  varint.decode(data)\n  return data.slice(varint.decode.bytes)\n}\n\n/**\n * Get the codec of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {string}\n */\nexports.getCodec = (prefixedData) => {\n  const code = varint.decode(prefixedData)\n  const codecName = intTable.get(code)\n  if (codecName === undefined) {\n    throw new Error(`Code ${code} not found`)\n  }\n  return codecName\n}\n\n/**\n * Get the name of the codec.\n * @param {number} codec\n * @returns {string}\n */\nexports.getName = (codec) => {\n  return intTable.get(codec)\n}\n\n/**\n * Get the code of the codec\n * @param {string} name\n * @returns {number}\n */\nexports.getNumber = (name) => {\n  const code = codecNameToCodeVarint[name]\n  if (code === undefined) {\n    throw new Error('Codec `' + name + '` not found')\n  }\n  return util.varintBufferDecode(code)[0]\n}\n\n/**\n * Get the code of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {number}\n */\nexports.getCode = (prefixedData) => {\n  return varint.decode(prefixedData)\n}\n\n/**\n * Get the code as varint of a codec name.\n * @param {string} codecName\n * @returns {Buffer}\n */\nexports.getCodeVarint = (codecName) => {\n  const code = codecNameToCodeVarint[codecName]\n  if (code === undefined) {\n    throw new Error('Codec `' + codecName + '` not found')\n  }\n  return code\n}\n\n/**\n * Get the varint of a code.\n * @param {Number} code\n * @returns {Array.<number>}\n */\nexports.getVarint = (code) => {\n  return varint.encode(code)\n}\n\n// Make the constants top-level constants\nconst constants = require('./constants')\nObject.assign(exports, constants)\n\n// Human friendly names for printing, e.g. in error messages\nexports.print = require('./print')\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "\"use strict\";\n\nmodule.exports = encode;\nvar MSB = 0x80,\n    REST = 0x7F,\n    MSBALL = ~REST,\n    INT = Math.pow(2, 31);\n\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n\n  while (num >= INT) {\n    out[offset++] = num & 0xFF | MSB;\n    num /= 128;\n  }\n\n  while (num & MSBALL) {\n    out[offset++] = num & 0xFF | MSB;\n    num >>>= 7;\n  }\n\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}", "\"use strict\";\n\nmodule.exports = read;\nvar MSB = 0x80,\n    REST = 0x7F;\n\nfunction read(buf, offset) {\n  var res = 0,\n      offset = offset || 0,\n      shift = 0,\n      counter = offset,\n      b,\n      l = buf.length;\n\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST) << shift : (b & REST) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB);\n\n  read.bytes = counter - offset;\n  return res;\n}", "\"use strict\";\n\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\n\nmodule.exports = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};", "'use strict'\nconst baseTable = require('./base-table.json')\n\n// map for hexString -> codecName\nconst nameTable = new Map()\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName]\n  nameTable.set(code, encodingName)\n}\n\nmodule.exports = Object.freeze(nameTable)\n", "'use strict'\n\nconst baseTable = require('./base-table.json')\nconst varintEncode = require('./util').varintEncode\n\n// map for codecName -> codeVarintBuffer\nconst varintTable = {}\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName]\n  varintTable[encodingName] = varintEncode(code)\n}\n\nmodule.exports = Object.freeze(varintTable)\n", "'use strict'\n\nconst table = require('./base-table.json')\n\n// map for codecConstant -> code\nconst constants = {}\n\nfor (const [name, code] of Object.entries(table)) {\n  constants[name.toUpperCase().replace(/-/g, '_')] = code\n}\n\nmodule.exports = Object.freeze(constants)\n", "'use strict'\n\nconst table = require('./base-table.json')\n\n// map for code -> print friendly name\nconst tableByCode = {}\n\nfor (const [name, code] of Object.entries(table)) {\n  if (tableByCode[code] === undefined) tableByCode[code] = name\n}\n\nmodule.exports = Object.freeze(tableByCode)\n"], "sourceRoot": ""}