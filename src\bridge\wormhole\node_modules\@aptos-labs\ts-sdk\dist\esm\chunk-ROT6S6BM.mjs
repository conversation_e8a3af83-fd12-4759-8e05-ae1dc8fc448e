import{b as r}from"./chunk-STY74NUA.mjs";var a=class a{static formatPrivateKey(i,n){let e=a.AIP80_PREFIXES[n],t=i;return typeof t=="string"&&t.startsWith(e)&&(t=t.split("-")[2]),`${e}${r.fromHexInput(t).toString()}`}static parseHexInput(i,n,e){let t,p=a.AIP80_PREFIXES[n];if(typeof i=="string")if(!e&&!i.startsWith(p))t=r.fromHexInput(i),e!==!1&&console.warn("[Aptos SDK] It is recommended that private keys are AIP-80 compliant (https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md). You can fix the private key by formatting it with `PrivateKey.formatPrivateKey(privateKey: string, type: 'ed25519' | 'secp256k1'): string`.");else if(i.startsWith(p))t=r.fromHexString(i.split("-")[2]);else throw e?new Error("Invalid HexString input while parsing private key. Must AIP-80 compliant string."):new Error("Invalid HexString input while parsing private key.");else t=r.fromHexInput(i);return t}};a.AIP80_PREFIXES={ed25519:"ed25519-priv-",secp256k1:"secp256k1-priv-"};var o=a;export{o as a};
//# sourceMappingURL=chunk-ROT6S6BM.mjs.map