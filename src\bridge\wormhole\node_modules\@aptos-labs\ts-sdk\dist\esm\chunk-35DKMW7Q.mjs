import{spawn as p}from"child_process";import{platform as g}from"os";var u=class{async init(t){let{network:o,profile:e,extraArguments:r,showStdout:a}=t,s=["aptos","init",`--network=${o??"local"}`,`--profile=${e??"default"}`];return r&&s.push(...r),this.runCommand(s,a)}async compile(t){let{packageDirectoryPath:o,namedAddresses:e,extraArguments:r,showStdout:a}=t,s=["aptos","move","compile","--package-dir",o],n=this.parseNamedAddresses(e);return s.push(...this.prepareNamedAddresses(n)),r&&s.push(...r),this.runCommand(s,a)}async test(t){let{packageDirectoryPath:o,namedAddresses:e,extraArguments:r,showStdout:a}=t,s=["aptos","move","test","--package-dir",o],n=this.parseNamedAddresses(e);return s.push(...this.prepareNamedAddresses(n)),r&&s.push(...r),this.runCommand(s,a)}async publish(t){let{packageDirectoryPath:o,namedAddresses:e,profile:r,extraArguments:a,showStdout:s}=t,n=["aptos","move","publish","--package-dir",o,`--profile=${r??"default"}`],d=this.parseNamedAddresses(e);return n.push(...this.prepareNamedAddresses(d)),a&&n.push(...a),this.runCommand(n,s)}async createObjectAndPublishPackage(t){let{packageDirectoryPath:o,addressName:e,namedAddresses:r,profile:a,extraArguments:s,showStdout:n}=t,d=["aptos","move","create-object-and-publish-package","--package-dir",o,"--address-name",e,`--profile=${a??"default"}`],i=this.parseNamedAddresses(r);d.push(...this.prepareNamedAddresses(i)),s&&d.push(...s);let{output:c}=await this.runCommand(d,n);return{objectAddress:this.extractAddressFromOutput(c)}}async upgradeObjectPackage(t){let{packageDirectoryPath:o,objectAddress:e,namedAddresses:r,profile:a,extraArguments:s,showStdout:n}=t,d=["aptos","move","upgrade-object-package","--package-dir",o,"--object-address",e,`--profile=${a??"default"}`],i=this.parseNamedAddresses(r);return d.push(...this.prepareNamedAddresses(i)),s&&d.push(...s),this.runCommand(d,n)}async buildPublishPayload(t){let{outputFile:o,packageDirectoryPath:e,namedAddresses:r,extraArguments:a,showStdout:s}=t,n=["aptos","move","build-publish-payload","--json-output-file",o,"--package-dir",e],d=this.parseNamedAddresses(r);return n.push(...this.prepareNamedAddresses(d)),a&&n.push(...a),this.runCommand(n,s)}async runScript(t){let{compiledScriptPath:o,profile:e,extraArguments:r,showStdout:a}=t,s=["aptos","move","run-script","--compiled-script-path",o,`--profile=${e??"default"}`];return r&&s.push(...r),this.runCommand(s,a)}async gasProfile(t){let{network:o,transactionId:e,extraArguments:r,showStdout:a}=t,s=["aptos","move","replay","--profile-gas","--network",o,"--txn-id",e];return r&&s.push(...r),this.runCommand(s,a)}async runCommand(t,o=!0){return new Promise((e,r)=>{let a=g(),s,n="",d="";a==="win32"?s=p("npx",t,{shell:!0}):s=p("npx",t),s.stdout.on("data",i=>{d=i.toString(),n+=i.toString()}),o&&(s.stdout.pipe(process.stdout),s.stderr.pipe(process.stderr)),process.stdin.pipe(s.stdin),s.on("close",i=>{if(i===0)try{let c=JSON.parse(d);c.Error?r(new Error(`Error: ${c.Error}`)):c.Result&&e({result:c.Result,output:n})}catch{e({output:n})}else r(new Error(`Child process exited with code ${i}`))})})}prepareNamedAddresses(t){let o=t.size,e=[];if(o===0)return e;e.push("--named-addresses");let r=[];return t.forEach((a,s)=>{let n=`${s}=${a.toString()}`;r.push(n)}),e.push(r.join(",")),e}parseNamedAddresses(t){let o=new Map;return Object.keys(t).forEach(e=>{let r=t[e];o.set(e,r)}),o}extractAddressFromOutput(t){let o=t.match("Code was successfully deployed to object address (0x[0-9a-fA-F]+)");if(o)return o[1];throw new Error("Failed to extract object address from output")}};export{u as a};
//# sourceMappingURL=chunk-35DKMW7Q.mjs.map