import { GoogleProtobufAny } from '@injectivelabs/core-proto-ts';
export declare const createAnyMessage: (msg: {
    type: string;
    value: Uint8Array;
}) => GoogleProtobufAny.Any;
export declare const createAny: (value: any, type: string) => GoogleProtobufAny.Any;
export declare const getInjectiveSignerAddress: (address: string | undefined) => string;
export declare const getEthereumSignerAddress: (address: string | undefined) => string;
