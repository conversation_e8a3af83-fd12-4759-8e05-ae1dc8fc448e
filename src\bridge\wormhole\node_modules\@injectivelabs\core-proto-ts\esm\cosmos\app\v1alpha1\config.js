/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Any } from "../../../google/protobuf/any.js";
export const protobufPackage = "cosmos.app.v1alpha1";
function createBaseConfig() {
    return { modules: [], golangBindings: [] };
}
export const Config = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.modules) {
            ModuleConfig.encode(v, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.golangBindings) {
            GolangBinding.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.modules.push(ModuleConfig.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.golangBindings.push(GolangBinding.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            modules: Array.isArray(object?.modules) ? object.modules.map((e) => ModuleConfig.fromJSON(e)) : [],
            golangBindings: Array.isArray(object?.golangBindings)
                ? object.golangBindings.map((e) => GolangBinding.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.modules) {
            obj.modules = message.modules.map((e) => e ? ModuleConfig.toJSON(e) : undefined);
        }
        else {
            obj.modules = [];
        }
        if (message.golangBindings) {
            obj.golangBindings = message.golangBindings.map((e) => e ? GolangBinding.toJSON(e) : undefined);
        }
        else {
            obj.golangBindings = [];
        }
        return obj;
    },
    create(base) {
        return Config.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseConfig();
        message.modules = object.modules?.map((e) => ModuleConfig.fromPartial(e)) || [];
        message.golangBindings = object.golangBindings?.map((e) => GolangBinding.fromPartial(e)) || [];
        return message;
    },
};
function createBaseModuleConfig() {
    return { name: "", config: undefined, golangBindings: [] };
}
export const ModuleConfig = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.config !== undefined) {
            Any.encode(message.config, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.golangBindings) {
            GolangBinding.encode(v, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModuleConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.config = Any.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.golangBindings.push(GolangBinding.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            config: isSet(object.config) ? Any.fromJSON(object.config) : undefined,
            golangBindings: Array.isArray(object?.golangBindings)
                ? object.golangBindings.map((e) => GolangBinding.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.config !== undefined && (obj.config = message.config ? Any.toJSON(message.config) : undefined);
        if (message.golangBindings) {
            obj.golangBindings = message.golangBindings.map((e) => e ? GolangBinding.toJSON(e) : undefined);
        }
        else {
            obj.golangBindings = [];
        }
        return obj;
    },
    create(base) {
        return ModuleConfig.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModuleConfig();
        message.name = object.name ?? "";
        message.config = (object.config !== undefined && object.config !== null)
            ? Any.fromPartial(object.config)
            : undefined;
        message.golangBindings = object.golangBindings?.map((e) => GolangBinding.fromPartial(e)) || [];
        return message;
    },
};
function createBaseGolangBinding() {
    return { interfaceType: "", implementation: "" };
}
export const GolangBinding = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.interfaceType !== "") {
            writer.uint32(10).string(message.interfaceType);
        }
        if (message.implementation !== "") {
            writer.uint32(18).string(message.implementation);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGolangBinding();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.interfaceType = reader.string();
                    break;
                case 2:
                    message.implementation = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            interfaceType: isSet(object.interfaceType) ? String(object.interfaceType) : "",
            implementation: isSet(object.implementation) ? String(object.implementation) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.interfaceType !== undefined && (obj.interfaceType = message.interfaceType);
        message.implementation !== undefined && (obj.implementation = message.implementation);
        return obj;
    },
    create(base) {
        return GolangBinding.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGolangBinding();
        message.interfaceType = object.interfaceType ?? "";
        message.implementation = object.implementation ?? "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
