import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { FeedConfig, Params, Report } from "./ocr";
export declare const protobufPackage = "injective.ocr.v1beta1";
export interface MsgCreateFeed {
    sender: string;
    config: FeedConfig | undefined;
}
export interface MsgCreateFeedResponse {
}
export interface MsgUpdateFeed {
    sender: string;
    /** feed_id is an unique ID for the target of this config */
    feedId: string;
    /** signers ith element is address ith oracle uses to sign a report */
    signers: string[];
    /**
     * transmitters ith element is address ith oracle uses to transmit a report
     * via the transmit method
     */
    transmitters: string[];
    /** Fixed LINK reward for each observer */
    linkPerObservation: string;
    /** Fixed LINK reward for transmitter */
    linkPerTransmission: string;
    /** Native denom for LINK coin in the bank keeper */
    linkDenom: string;
    /** feed administrator */
    feedAdmin: string;
    /** feed billing administrator */
    billingAdmin: string;
}
export interface MsgUpdateFeedResponse {
}
export interface MsgTransmit {
    /** Address of the transmitter */
    transmitter: string;
    configDigest: Uint8Array;
    feedId: string;
    epoch: string;
    round: string;
    extraHash: Uint8Array;
    report: Report | undefined;
    signatures: Uint8Array[];
}
export interface MsgTransmitResponse {
}
export interface MsgFundFeedRewardPool {
    sender: string;
    feedId: string;
    amount: Coin | undefined;
}
export interface MsgFundFeedRewardPoolResponse {
}
export interface MsgWithdrawFeedRewardPool {
    sender: string;
    feedId: string;
    amount: Coin | undefined;
}
export interface MsgWithdrawFeedRewardPoolResponse {
}
export interface MsgSetPayees {
    sender: string;
    feedId: string;
    /** addresses oracles use to transmit the reports */
    transmitters: string[];
    /** addresses of payees corresponding to list of transmitters */
    payees: string[];
}
export interface MsgSetPayeesResponse {
}
export interface MsgTransferPayeeship {
    /** transmitter address of oracle whose payee is changing */
    sender: string;
    transmitter: string;
    feedId: string;
    /** new payee address */
    proposed: string;
}
export interface MsgTransferPayeeshipResponse {
}
export interface MsgAcceptPayeeship {
    /** new payee address */
    payee: string;
    /** transmitter address of oracle whose payee is changing */
    transmitter: string;
    feedId: string;
}
export interface MsgAcceptPayeeshipResponse {
}
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the ocr parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
export declare const MsgCreateFeed: {
    encode(message: MsgCreateFeed, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateFeed;
    fromJSON(object: any): MsgCreateFeed;
    toJSON(message: MsgCreateFeed): unknown;
    create(base?: DeepPartial<MsgCreateFeed>): MsgCreateFeed;
    fromPartial(object: DeepPartial<MsgCreateFeed>): MsgCreateFeed;
};
export declare const MsgCreateFeedResponse: {
    encode(_: MsgCreateFeedResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateFeedResponse;
    fromJSON(_: any): MsgCreateFeedResponse;
    toJSON(_: MsgCreateFeedResponse): unknown;
    create(base?: DeepPartial<MsgCreateFeedResponse>): MsgCreateFeedResponse;
    fromPartial(_: DeepPartial<MsgCreateFeedResponse>): MsgCreateFeedResponse;
};
export declare const MsgUpdateFeed: {
    encode(message: MsgUpdateFeed, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateFeed;
    fromJSON(object: any): MsgUpdateFeed;
    toJSON(message: MsgUpdateFeed): unknown;
    create(base?: DeepPartial<MsgUpdateFeed>): MsgUpdateFeed;
    fromPartial(object: DeepPartial<MsgUpdateFeed>): MsgUpdateFeed;
};
export declare const MsgUpdateFeedResponse: {
    encode(_: MsgUpdateFeedResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateFeedResponse;
    fromJSON(_: any): MsgUpdateFeedResponse;
    toJSON(_: MsgUpdateFeedResponse): unknown;
    create(base?: DeepPartial<MsgUpdateFeedResponse>): MsgUpdateFeedResponse;
    fromPartial(_: DeepPartial<MsgUpdateFeedResponse>): MsgUpdateFeedResponse;
};
export declare const MsgTransmit: {
    encode(message: MsgTransmit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTransmit;
    fromJSON(object: any): MsgTransmit;
    toJSON(message: MsgTransmit): unknown;
    create(base?: DeepPartial<MsgTransmit>): MsgTransmit;
    fromPartial(object: DeepPartial<MsgTransmit>): MsgTransmit;
};
export declare const MsgTransmitResponse: {
    encode(_: MsgTransmitResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTransmitResponse;
    fromJSON(_: any): MsgTransmitResponse;
    toJSON(_: MsgTransmitResponse): unknown;
    create(base?: DeepPartial<MsgTransmitResponse>): MsgTransmitResponse;
    fromPartial(_: DeepPartial<MsgTransmitResponse>): MsgTransmitResponse;
};
export declare const MsgFundFeedRewardPool: {
    encode(message: MsgFundFeedRewardPool, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgFundFeedRewardPool;
    fromJSON(object: any): MsgFundFeedRewardPool;
    toJSON(message: MsgFundFeedRewardPool): unknown;
    create(base?: DeepPartial<MsgFundFeedRewardPool>): MsgFundFeedRewardPool;
    fromPartial(object: DeepPartial<MsgFundFeedRewardPool>): MsgFundFeedRewardPool;
};
export declare const MsgFundFeedRewardPoolResponse: {
    encode(_: MsgFundFeedRewardPoolResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgFundFeedRewardPoolResponse;
    fromJSON(_: any): MsgFundFeedRewardPoolResponse;
    toJSON(_: MsgFundFeedRewardPoolResponse): unknown;
    create(base?: DeepPartial<MsgFundFeedRewardPoolResponse>): MsgFundFeedRewardPoolResponse;
    fromPartial(_: DeepPartial<MsgFundFeedRewardPoolResponse>): MsgFundFeedRewardPoolResponse;
};
export declare const MsgWithdrawFeedRewardPool: {
    encode(message: MsgWithdrawFeedRewardPool, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdrawFeedRewardPool;
    fromJSON(object: any): MsgWithdrawFeedRewardPool;
    toJSON(message: MsgWithdrawFeedRewardPool): unknown;
    create(base?: DeepPartial<MsgWithdrawFeedRewardPool>): MsgWithdrawFeedRewardPool;
    fromPartial(object: DeepPartial<MsgWithdrawFeedRewardPool>): MsgWithdrawFeedRewardPool;
};
export declare const MsgWithdrawFeedRewardPoolResponse: {
    encode(_: MsgWithdrawFeedRewardPoolResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdrawFeedRewardPoolResponse;
    fromJSON(_: any): MsgWithdrawFeedRewardPoolResponse;
    toJSON(_: MsgWithdrawFeedRewardPoolResponse): unknown;
    create(base?: DeepPartial<MsgWithdrawFeedRewardPoolResponse>): MsgWithdrawFeedRewardPoolResponse;
    fromPartial(_: DeepPartial<MsgWithdrawFeedRewardPoolResponse>): MsgWithdrawFeedRewardPoolResponse;
};
export declare const MsgSetPayees: {
    encode(message: MsgSetPayees, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSetPayees;
    fromJSON(object: any): MsgSetPayees;
    toJSON(message: MsgSetPayees): unknown;
    create(base?: DeepPartial<MsgSetPayees>): MsgSetPayees;
    fromPartial(object: DeepPartial<MsgSetPayees>): MsgSetPayees;
};
export declare const MsgSetPayeesResponse: {
    encode(_: MsgSetPayeesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSetPayeesResponse;
    fromJSON(_: any): MsgSetPayeesResponse;
    toJSON(_: MsgSetPayeesResponse): unknown;
    create(base?: DeepPartial<MsgSetPayeesResponse>): MsgSetPayeesResponse;
    fromPartial(_: DeepPartial<MsgSetPayeesResponse>): MsgSetPayeesResponse;
};
export declare const MsgTransferPayeeship: {
    encode(message: MsgTransferPayeeship, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTransferPayeeship;
    fromJSON(object: any): MsgTransferPayeeship;
    toJSON(message: MsgTransferPayeeship): unknown;
    create(base?: DeepPartial<MsgTransferPayeeship>): MsgTransferPayeeship;
    fromPartial(object: DeepPartial<MsgTransferPayeeship>): MsgTransferPayeeship;
};
export declare const MsgTransferPayeeshipResponse: {
    encode(_: MsgTransferPayeeshipResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgTransferPayeeshipResponse;
    fromJSON(_: any): MsgTransferPayeeshipResponse;
    toJSON(_: MsgTransferPayeeshipResponse): unknown;
    create(base?: DeepPartial<MsgTransferPayeeshipResponse>): MsgTransferPayeeshipResponse;
    fromPartial(_: DeepPartial<MsgTransferPayeeshipResponse>): MsgTransferPayeeshipResponse;
};
export declare const MsgAcceptPayeeship: {
    encode(message: MsgAcceptPayeeship, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAcceptPayeeship;
    fromJSON(object: any): MsgAcceptPayeeship;
    toJSON(message: MsgAcceptPayeeship): unknown;
    create(base?: DeepPartial<MsgAcceptPayeeship>): MsgAcceptPayeeship;
    fromPartial(object: DeepPartial<MsgAcceptPayeeship>): MsgAcceptPayeeship;
};
export declare const MsgAcceptPayeeshipResponse: {
    encode(_: MsgAcceptPayeeshipResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAcceptPayeeshipResponse;
    fromJSON(_: any): MsgAcceptPayeeshipResponse;
    toJSON(_: MsgAcceptPayeeshipResponse): unknown;
    create(base?: DeepPartial<MsgAcceptPayeeshipResponse>): MsgAcceptPayeeshipResponse;
    fromPartial(_: DeepPartial<MsgAcceptPayeeshipResponse>): MsgAcceptPayeeshipResponse;
};
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
/** Msg defines the OCR Msg service. */
export interface Msg {
    /** CreateFeed defines a method for creating feed by module admin */
    CreateFeed(request: DeepPartial<MsgCreateFeed>, metadata?: grpc.Metadata): Promise<MsgCreateFeedResponse>;
    /**
     * CreateFeed defines a method for creating feed by feed admin or feed billing
     * admin
     */
    UpdateFeed(request: DeepPartial<MsgUpdateFeed>, metadata?: grpc.Metadata): Promise<MsgUpdateFeedResponse>;
    /** Transmit defines a method for transmitting the feed info by transmitter */
    Transmit(request: DeepPartial<MsgTransmit>, metadata?: grpc.Metadata): Promise<MsgTransmitResponse>;
    /** FundFeedRewardPool defines a method to put funds into feed reward pool */
    FundFeedRewardPool(request: DeepPartial<MsgFundFeedRewardPool>, metadata?: grpc.Metadata): Promise<MsgFundFeedRewardPoolResponse>;
    /**
     * WithdrawFeedRewardPool defines a method to witdhraw feed reward by feed
     * admin or billing admin
     */
    WithdrawFeedRewardPool(request: DeepPartial<MsgWithdrawFeedRewardPool>, metadata?: grpc.Metadata): Promise<MsgWithdrawFeedRewardPoolResponse>;
    /** SetPayees defines a method to set payees for transmitters (batch action) */
    SetPayees(request: DeepPartial<MsgSetPayees>, metadata?: grpc.Metadata): Promise<MsgSetPayeesResponse>;
    /**
     * TransferPayeeship defines a method for a payee to transfer reward receive
     * ownership
     */
    TransferPayeeship(request: DeepPartial<MsgTransferPayeeship>, metadata?: grpc.Metadata): Promise<MsgTransferPayeeshipResponse>;
    /**
     * AcceptPayeeship defines a method for a new payee to accept reward receive
     * ownership
     */
    AcceptPayeeship(request: DeepPartial<MsgAcceptPayeeship>, metadata?: grpc.Metadata): Promise<MsgAcceptPayeeshipResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    CreateFeed(request: DeepPartial<MsgCreateFeed>, metadata?: grpc.Metadata): Promise<MsgCreateFeedResponse>;
    UpdateFeed(request: DeepPartial<MsgUpdateFeed>, metadata?: grpc.Metadata): Promise<MsgUpdateFeedResponse>;
    Transmit(request: DeepPartial<MsgTransmit>, metadata?: grpc.Metadata): Promise<MsgTransmitResponse>;
    FundFeedRewardPool(request: DeepPartial<MsgFundFeedRewardPool>, metadata?: grpc.Metadata): Promise<MsgFundFeedRewardPoolResponse>;
    WithdrawFeedRewardPool(request: DeepPartial<MsgWithdrawFeedRewardPool>, metadata?: grpc.Metadata): Promise<MsgWithdrawFeedRewardPoolResponse>;
    SetPayees(request: DeepPartial<MsgSetPayees>, metadata?: grpc.Metadata): Promise<MsgSetPayeesResponse>;
    TransferPayeeship(request: DeepPartial<MsgTransferPayeeship>, metadata?: grpc.Metadata): Promise<MsgTransferPayeeshipResponse>;
    AcceptPayeeship(request: DeepPartial<MsgAcceptPayeeship>, metadata?: grpc.Metadata): Promise<MsgAcceptPayeeshipResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgCreateFeedDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateFeedDesc: UnaryMethodDefinitionish;
export declare const MsgTransmitDesc: UnaryMethodDefinitionish;
export declare const MsgFundFeedRewardPoolDesc: UnaryMethodDefinitionish;
export declare const MsgWithdrawFeedRewardPoolDesc: UnaryMethodDefinitionish;
export declare const MsgSetPayeesDesc: UnaryMethodDefinitionish;
export declare const MsgTransferPayeeshipDesc: UnaryMethodDefinitionish;
export declare const MsgAcceptPayeeshipDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
