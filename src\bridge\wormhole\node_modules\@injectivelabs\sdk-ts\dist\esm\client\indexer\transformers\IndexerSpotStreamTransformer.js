import { IndexerGrpcSpotTransformer } from './IndexerGrpcSpotTransformer.js';
/**
 * @category Indexer Stream Transformer
 */
export class IndexerSpotStreamTransformer {
    static tradesStreamCallback = (response) => {
        const trade = response.trade;
        return {
            trade: trade
                ? IndexerGrpcSpotTransformer.grpcTradeToTrade(trade)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static ordersStreamCallback = (response) => {
        const order = response.order;
        return {
            order: order
                ? IndexerGrpcSpotTransformer.grpcOrderToOrder(order)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static orderHistoryStreamCallback = (response) => {
        const order = response.order;
        return {
            order: order
                ? IndexerGrpcSpotTransformer.grpcOrderHistoryToOrderHistory(order)
                : undefined,
            operation: response.operationType,
            timestamp: response.timestamp,
        };
    };
    static orderbookV2StreamCallback = (response) => {
        const orderbook = response.orderbook;
        return {
            orderbook: orderbook
                ? IndexerGrpcSpotTransformer.grpcOrderbookV2ToOrderbookV2({
                    sequence: parseInt(orderbook.sequence, 10),
                    buys: orderbook.buys,
                    sells: orderbook.sells,
                })
                : undefined,
            operation: response.operationType,
            marketId: response.marketId,
            timestamp: response.timestamp,
        };
    };
    static orderbookUpdateStreamCallback = (response) => {
        const orderbook = response.orderbookLevelUpdates;
        return {
            orderbook: orderbook
                ? IndexerGrpcSpotTransformer.grpcOrderbookV2ToOrderbookV2({
                    sequence: parseInt(orderbook.sequence, 10),
                    buys: orderbook.buys,
                    sells: orderbook.sells,
                })
                : undefined,
            operation: response.operationType,
            marketId: response.marketId,
            timestamp: response.timestamp,
        };
    };
}
