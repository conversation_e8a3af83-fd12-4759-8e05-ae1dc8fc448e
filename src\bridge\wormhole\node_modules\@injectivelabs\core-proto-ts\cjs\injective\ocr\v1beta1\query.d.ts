import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { GenesisState } from "./genesis";
import { EpochAndRound, FeedConfig, FeedConfigInfo, Params, Transmission } from "./ocr";
export declare const protobufPackage = "injective.ocr.v1beta1";
export interface QueryParamsRequest {
}
export interface QueryParamsResponse {
    params: Params | undefined;
}
export interface QueryFeedConfigRequest {
    feedId: string;
}
export interface QueryFeedConfigResponse {
    feedConfigInfo: FeedConfigInfo | undefined;
    feedConfig: FeedConfig | undefined;
}
export interface QueryFeedConfigInfoRequest {
    feedId: string;
}
export interface QueryFeedConfigInfoResponse {
    feedConfigInfo: FeedConfigInfo | undefined;
    epochAndRound: EpochAndRound | undefined;
}
export interface QueryLatestRoundRequest {
    feedId: string;
}
export interface QueryLatestRoundResponse {
    latestRoundId: string;
    data: Transmission | undefined;
}
export interface QueryLatestTransmissionDetailsRequest {
    feedId: string;
}
export interface QueryLatestTransmissionDetailsResponse {
    configDigest: Uint8Array;
    epochAndRound: EpochAndRound | undefined;
    data: Transmission | undefined;
}
export interface QueryOwedAmountRequest {
    transmitter: string;
}
export interface QueryOwedAmountResponse {
    amount: Coin | undefined;
}
export interface QueryModuleStateRequest {
}
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
export declare const QueryParamsRequest: {
    encode(_: QueryParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsRequest;
    fromJSON(_: any): QueryParamsRequest;
    toJSON(_: QueryParamsRequest): unknown;
    create(base?: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
    fromPartial(_: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
};
export declare const QueryParamsResponse: {
    encode(message: QueryParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsResponse;
    fromJSON(object: any): QueryParamsResponse;
    toJSON(message: QueryParamsResponse): unknown;
    create(base?: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
    fromPartial(object: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
};
export declare const QueryFeedConfigRequest: {
    encode(message: QueryFeedConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryFeedConfigRequest;
    fromJSON(object: any): QueryFeedConfigRequest;
    toJSON(message: QueryFeedConfigRequest): unknown;
    create(base?: DeepPartial<QueryFeedConfigRequest>): QueryFeedConfigRequest;
    fromPartial(object: DeepPartial<QueryFeedConfigRequest>): QueryFeedConfigRequest;
};
export declare const QueryFeedConfigResponse: {
    encode(message: QueryFeedConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryFeedConfigResponse;
    fromJSON(object: any): QueryFeedConfigResponse;
    toJSON(message: QueryFeedConfigResponse): unknown;
    create(base?: DeepPartial<QueryFeedConfigResponse>): QueryFeedConfigResponse;
    fromPartial(object: DeepPartial<QueryFeedConfigResponse>): QueryFeedConfigResponse;
};
export declare const QueryFeedConfigInfoRequest: {
    encode(message: QueryFeedConfigInfoRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryFeedConfigInfoRequest;
    fromJSON(object: any): QueryFeedConfigInfoRequest;
    toJSON(message: QueryFeedConfigInfoRequest): unknown;
    create(base?: DeepPartial<QueryFeedConfigInfoRequest>): QueryFeedConfigInfoRequest;
    fromPartial(object: DeepPartial<QueryFeedConfigInfoRequest>): QueryFeedConfigInfoRequest;
};
export declare const QueryFeedConfigInfoResponse: {
    encode(message: QueryFeedConfigInfoResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryFeedConfigInfoResponse;
    fromJSON(object: any): QueryFeedConfigInfoResponse;
    toJSON(message: QueryFeedConfigInfoResponse): unknown;
    create(base?: DeepPartial<QueryFeedConfigInfoResponse>): QueryFeedConfigInfoResponse;
    fromPartial(object: DeepPartial<QueryFeedConfigInfoResponse>): QueryFeedConfigInfoResponse;
};
export declare const QueryLatestRoundRequest: {
    encode(message: QueryLatestRoundRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLatestRoundRequest;
    fromJSON(object: any): QueryLatestRoundRequest;
    toJSON(message: QueryLatestRoundRequest): unknown;
    create(base?: DeepPartial<QueryLatestRoundRequest>): QueryLatestRoundRequest;
    fromPartial(object: DeepPartial<QueryLatestRoundRequest>): QueryLatestRoundRequest;
};
export declare const QueryLatestRoundResponse: {
    encode(message: QueryLatestRoundResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLatestRoundResponse;
    fromJSON(object: any): QueryLatestRoundResponse;
    toJSON(message: QueryLatestRoundResponse): unknown;
    create(base?: DeepPartial<QueryLatestRoundResponse>): QueryLatestRoundResponse;
    fromPartial(object: DeepPartial<QueryLatestRoundResponse>): QueryLatestRoundResponse;
};
export declare const QueryLatestTransmissionDetailsRequest: {
    encode(message: QueryLatestTransmissionDetailsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLatestTransmissionDetailsRequest;
    fromJSON(object: any): QueryLatestTransmissionDetailsRequest;
    toJSON(message: QueryLatestTransmissionDetailsRequest): unknown;
    create(base?: DeepPartial<QueryLatestTransmissionDetailsRequest>): QueryLatestTransmissionDetailsRequest;
    fromPartial(object: DeepPartial<QueryLatestTransmissionDetailsRequest>): QueryLatestTransmissionDetailsRequest;
};
export declare const QueryLatestTransmissionDetailsResponse: {
    encode(message: QueryLatestTransmissionDetailsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryLatestTransmissionDetailsResponse;
    fromJSON(object: any): QueryLatestTransmissionDetailsResponse;
    toJSON(message: QueryLatestTransmissionDetailsResponse): unknown;
    create(base?: DeepPartial<QueryLatestTransmissionDetailsResponse>): QueryLatestTransmissionDetailsResponse;
    fromPartial(object: DeepPartial<QueryLatestTransmissionDetailsResponse>): QueryLatestTransmissionDetailsResponse;
};
export declare const QueryOwedAmountRequest: {
    encode(message: QueryOwedAmountRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOwedAmountRequest;
    fromJSON(object: any): QueryOwedAmountRequest;
    toJSON(message: QueryOwedAmountRequest): unknown;
    create(base?: DeepPartial<QueryOwedAmountRequest>): QueryOwedAmountRequest;
    fromPartial(object: DeepPartial<QueryOwedAmountRequest>): QueryOwedAmountRequest;
};
export declare const QueryOwedAmountResponse: {
    encode(message: QueryOwedAmountResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOwedAmountResponse;
    fromJSON(object: any): QueryOwedAmountResponse;
    toJSON(message: QueryOwedAmountResponse): unknown;
    create(base?: DeepPartial<QueryOwedAmountResponse>): QueryOwedAmountResponse;
    fromPartial(object: DeepPartial<QueryOwedAmountResponse>): QueryOwedAmountResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
/** Query defines the gRPC querier service for OCR module. */
export interface Query {
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    /** Retrieves the OCR FeedConfig for a given FeedId */
    FeedConfig(request: DeepPartial<QueryFeedConfigRequest>, metadata?: grpc.Metadata): Promise<QueryFeedConfigResponse>;
    /** Retrieves the OCR FeedConfigInfo for a given FeedId */
    FeedConfigInfo(request: DeepPartial<QueryFeedConfigInfoRequest>, metadata?: grpc.Metadata): Promise<QueryFeedConfigInfoResponse>;
    /** Retrieves latest round ID and data, including median answer for that round */
    LatestRound(request: DeepPartial<QueryLatestRoundRequest>, metadata?: grpc.Metadata): Promise<QueryLatestRoundResponse>;
    /**
     * LatestTransmissionDetails returns details about the latest trasmission
     * recorded on chain for the given feed ID.
     */
    LatestTransmissionDetails(request: DeepPartial<QueryLatestTransmissionDetailsRequest>, metadata?: grpc.Metadata): Promise<QueryLatestTransmissionDetailsResponse>;
    /** Retrieves transmitter's owed amount */
    OwedAmount(request: DeepPartial<QueryOwedAmountRequest>, metadata?: grpc.Metadata): Promise<QueryOwedAmountResponse>;
    /** Retrieves the entire OCR module's state */
    OcrModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    FeedConfig(request: DeepPartial<QueryFeedConfigRequest>, metadata?: grpc.Metadata): Promise<QueryFeedConfigResponse>;
    FeedConfigInfo(request: DeepPartial<QueryFeedConfigInfoRequest>, metadata?: grpc.Metadata): Promise<QueryFeedConfigInfoResponse>;
    LatestRound(request: DeepPartial<QueryLatestRoundRequest>, metadata?: grpc.Metadata): Promise<QueryLatestRoundResponse>;
    LatestTransmissionDetails(request: DeepPartial<QueryLatestTransmissionDetailsRequest>, metadata?: grpc.Metadata): Promise<QueryLatestTransmissionDetailsResponse>;
    OwedAmount(request: DeepPartial<QueryOwedAmountRequest>, metadata?: grpc.Metadata): Promise<QueryOwedAmountResponse>;
    OcrModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryParamsDesc: UnaryMethodDefinitionish;
export declare const QueryFeedConfigDesc: UnaryMethodDefinitionish;
export declare const QueryFeedConfigInfoDesc: UnaryMethodDefinitionish;
export declare const QueryLatestRoundDesc: UnaryMethodDefinitionish;
export declare const QueryLatestTransmissionDetailsDesc: UnaryMethodDefinitionish;
export declare const QueryOwedAmountDesc: UnaryMethodDefinitionish;
export declare const QueryOcrModuleStateDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
