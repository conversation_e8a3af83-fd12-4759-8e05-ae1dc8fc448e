import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_chart_rpc";
export interface SpotMarketHistoryRequest {
    /** Specify unique ticker to search */
    symbol: string;
    /** As an alternative is possible to provide a marketId */
    marketId: string;
    /**
     * Symbol resolution. Possible resolutions are daily (D or 1D, 2D ... ), weekly
     * (1W, 2W ...), monthly (1M, 2M...) and an intra-day resolution – minutes(1, 2
     * ...).
     */
    resolution: string;
    /** Unix timestamp (UTC) of the leftmost required bar, including from */
    from: number;
    /**
     * Unix timestamp (UTC) of the rightmost required bar, including to. It can be
     * in the future. In this case, the rightmost required bar is the latest
     * available bar.
     */
    to: number;
    /**
     * Number of bars (higher priority than from) starting with to. If countback is
     * set, from should be ignored.
     */
    countback: number;
}
export interface SpotMarketHistoryResponse {
    /**
     * Bar time, Unix timestamp (UTC). Daily bars should only have the date part,
     * time should be 0.
     */
    t: number[];
    /** Open price. */
    o: number[];
    /** High price. */
    h: number[];
    /** Low price. */
    l: number[];
    /** Close price. */
    c: number[];
    /** Volume. */
    v: number[];
    /** Status of the request. */
    s: string;
}
export interface DerivativeMarketHistoryRequest {
    /** Specify unique ticker to search. */
    symbol: string;
    /** As an alternative is possible to provide a marketId */
    marketId: string;
    /**
     * Symbol resolution. Possible resolutions are daily (D or 1D, 2D ... ), weekly
     * (1W, 2W ...), monthly (1M, 2M...) and an intra-day resolution – minutes(1, 2
     * ...).
     */
    resolution: string;
    /** Unix timestamp (UTC) of the leftmost required bar, including from */
    from: number;
    /**
     * Unix timestamp (UTC) of the rightmost required bar, including to. It can be
     * in the future. In this case, the rightmost required bar is the latest
     * available bar.
     */
    to: number;
    /**
     * Number of bars (higher priority than from) starting with to. If countback is
     * set, from should be ignored.
     */
    countback: number;
}
export interface DerivativeMarketHistoryResponse {
    /**
     * Bar time, Unix timestamp (UTC). Daily bars should only have the date part,
     * time should be 0.
     */
    t: number[];
    /** Open price. */
    o: number[];
    /** High price. */
    h: number[];
    /** Low price. */
    l: number[];
    /** Close price. */
    c: number[];
    /** Volume. */
    v: number[];
    /** Status of the request. */
    s: string;
}
export interface SpotMarketSummaryRequest {
    /** Market ID of the spot market */
    marketId: string;
    /** Specify the resolution */
    resolution: string;
}
export interface SpotMarketSummaryResponse {
    /** Market ID of the derivativeMarket market */
    marketId: string;
    /** Open price. */
    open: number;
    /** High price. */
    high: number;
    /** Low price. */
    low: number;
    /** Volume. */
    volume: number;
    /** Current price based on latest fill event. */
    price: number;
    /** Change percent from opening price. */
    change: number;
}
export interface AllSpotMarketSummaryRequest {
    /** Specify the resolution */
    resolution: string;
}
export interface AllSpotMarketSummaryResponse {
    field: MarketSummaryResp[];
}
export interface MarketSummaryResp {
    /** Market ID of the derivativeMarket market */
    marketId: string;
    /** Open price. */
    open: number;
    /** High price. */
    high: number;
    /** Low price. */
    low: number;
    /** Volume. */
    volume: number;
    /** Current price based on latest fill event. */
    price: number;
    /** Change percent from opening price. */
    change: number;
}
export interface DerivativeMarketSummaryRequest {
    /** Market ID of the derivative market */
    marketId: string;
    /** Request the summary of index price feed */
    indexPrice: boolean;
    /** Specify the resolution */
    resolution: string;
}
export interface DerivativeMarketSummaryResponse {
    /** Market ID of the derivativeMarket market */
    marketId: string;
    /** Open price. */
    open: number;
    /** High price. */
    high: number;
    /** Low price. */
    low: number;
    /** Volume. */
    volume: number;
    /** Current price based on latest fill event. */
    price: number;
    /** Change percent from opening price. */
    change: number;
}
export interface AllDerivativeMarketSummaryRequest {
    /** Specify the resolution */
    resolution: string;
}
export interface AllDerivativeMarketSummaryResponse {
    field: MarketSummaryResp[];
}
export declare const SpotMarketHistoryRequest: {
    encode(message: SpotMarketHistoryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketHistoryRequest;
    fromJSON(object: any): SpotMarketHistoryRequest;
    toJSON(message: SpotMarketHistoryRequest): unknown;
    create(base?: DeepPartial<SpotMarketHistoryRequest>): SpotMarketHistoryRequest;
    fromPartial(object: DeepPartial<SpotMarketHistoryRequest>): SpotMarketHistoryRequest;
};
export declare const SpotMarketHistoryResponse: {
    encode(message: SpotMarketHistoryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketHistoryResponse;
    fromJSON(object: any): SpotMarketHistoryResponse;
    toJSON(message: SpotMarketHistoryResponse): unknown;
    create(base?: DeepPartial<SpotMarketHistoryResponse>): SpotMarketHistoryResponse;
    fromPartial(object: DeepPartial<SpotMarketHistoryResponse>): SpotMarketHistoryResponse;
};
export declare const DerivativeMarketHistoryRequest: {
    encode(message: DerivativeMarketHistoryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketHistoryRequest;
    fromJSON(object: any): DerivativeMarketHistoryRequest;
    toJSON(message: DerivativeMarketHistoryRequest): unknown;
    create(base?: DeepPartial<DerivativeMarketHistoryRequest>): DerivativeMarketHistoryRequest;
    fromPartial(object: DeepPartial<DerivativeMarketHistoryRequest>): DerivativeMarketHistoryRequest;
};
export declare const DerivativeMarketHistoryResponse: {
    encode(message: DerivativeMarketHistoryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketHistoryResponse;
    fromJSON(object: any): DerivativeMarketHistoryResponse;
    toJSON(message: DerivativeMarketHistoryResponse): unknown;
    create(base?: DeepPartial<DerivativeMarketHistoryResponse>): DerivativeMarketHistoryResponse;
    fromPartial(object: DeepPartial<DerivativeMarketHistoryResponse>): DerivativeMarketHistoryResponse;
};
export declare const SpotMarketSummaryRequest: {
    encode(message: SpotMarketSummaryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketSummaryRequest;
    fromJSON(object: any): SpotMarketSummaryRequest;
    toJSON(message: SpotMarketSummaryRequest): unknown;
    create(base?: DeepPartial<SpotMarketSummaryRequest>): SpotMarketSummaryRequest;
    fromPartial(object: DeepPartial<SpotMarketSummaryRequest>): SpotMarketSummaryRequest;
};
export declare const SpotMarketSummaryResponse: {
    encode(message: SpotMarketSummaryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketSummaryResponse;
    fromJSON(object: any): SpotMarketSummaryResponse;
    toJSON(message: SpotMarketSummaryResponse): unknown;
    create(base?: DeepPartial<SpotMarketSummaryResponse>): SpotMarketSummaryResponse;
    fromPartial(object: DeepPartial<SpotMarketSummaryResponse>): SpotMarketSummaryResponse;
};
export declare const AllSpotMarketSummaryRequest: {
    encode(message: AllSpotMarketSummaryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AllSpotMarketSummaryRequest;
    fromJSON(object: any): AllSpotMarketSummaryRequest;
    toJSON(message: AllSpotMarketSummaryRequest): unknown;
    create(base?: DeepPartial<AllSpotMarketSummaryRequest>): AllSpotMarketSummaryRequest;
    fromPartial(object: DeepPartial<AllSpotMarketSummaryRequest>): AllSpotMarketSummaryRequest;
};
export declare const AllSpotMarketSummaryResponse: {
    encode(message: AllSpotMarketSummaryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AllSpotMarketSummaryResponse;
    fromJSON(object: any): AllSpotMarketSummaryResponse;
    toJSON(message: AllSpotMarketSummaryResponse): unknown;
    create(base?: DeepPartial<AllSpotMarketSummaryResponse>): AllSpotMarketSummaryResponse;
    fromPartial(object: DeepPartial<AllSpotMarketSummaryResponse>): AllSpotMarketSummaryResponse;
};
export declare const MarketSummaryResp: {
    encode(message: MarketSummaryResp, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MarketSummaryResp;
    fromJSON(object: any): MarketSummaryResp;
    toJSON(message: MarketSummaryResp): unknown;
    create(base?: DeepPartial<MarketSummaryResp>): MarketSummaryResp;
    fromPartial(object: DeepPartial<MarketSummaryResp>): MarketSummaryResp;
};
export declare const DerivativeMarketSummaryRequest: {
    encode(message: DerivativeMarketSummaryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketSummaryRequest;
    fromJSON(object: any): DerivativeMarketSummaryRequest;
    toJSON(message: DerivativeMarketSummaryRequest): unknown;
    create(base?: DeepPartial<DerivativeMarketSummaryRequest>): DerivativeMarketSummaryRequest;
    fromPartial(object: DeepPartial<DerivativeMarketSummaryRequest>): DerivativeMarketSummaryRequest;
};
export declare const DerivativeMarketSummaryResponse: {
    encode(message: DerivativeMarketSummaryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketSummaryResponse;
    fromJSON(object: any): DerivativeMarketSummaryResponse;
    toJSON(message: DerivativeMarketSummaryResponse): unknown;
    create(base?: DeepPartial<DerivativeMarketSummaryResponse>): DerivativeMarketSummaryResponse;
    fromPartial(object: DeepPartial<DerivativeMarketSummaryResponse>): DerivativeMarketSummaryResponse;
};
export declare const AllDerivativeMarketSummaryRequest: {
    encode(message: AllDerivativeMarketSummaryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AllDerivativeMarketSummaryRequest;
    fromJSON(object: any): AllDerivativeMarketSummaryRequest;
    toJSON(message: AllDerivativeMarketSummaryRequest): unknown;
    create(base?: DeepPartial<AllDerivativeMarketSummaryRequest>): AllDerivativeMarketSummaryRequest;
    fromPartial(object: DeepPartial<AllDerivativeMarketSummaryRequest>): AllDerivativeMarketSummaryRequest;
};
export declare const AllDerivativeMarketSummaryResponse: {
    encode(message: AllDerivativeMarketSummaryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AllDerivativeMarketSummaryResponse;
    fromJSON(object: any): AllDerivativeMarketSummaryResponse;
    toJSON(message: AllDerivativeMarketSummaryResponse): unknown;
    create(base?: DeepPartial<AllDerivativeMarketSummaryResponse>): AllDerivativeMarketSummaryResponse;
    fromPartial(object: DeepPartial<AllDerivativeMarketSummaryResponse>): AllDerivativeMarketSummaryResponse;
};
/** InjectiveChartRPC implements historical chart data retrieval. */
export interface InjectiveChartRPC {
    /** Request for history bars of spotMarket for TradingView. */
    SpotMarketHistory(request: DeepPartial<SpotMarketHistoryRequest>, metadata?: grpc.Metadata): Promise<SpotMarketHistoryResponse>;
    /** Request for history bars of derivativeMarket for TradingView. */
    DerivativeMarketHistory(request: DeepPartial<DerivativeMarketHistoryRequest>, metadata?: grpc.Metadata): Promise<DerivativeMarketHistoryResponse>;
    /** Gets spot market summary for the latest interval (hour, day, month) */
    SpotMarketSummary(request: DeepPartial<SpotMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<SpotMarketSummaryResponse>;
    /**
     * Gets batch summary for all active markets, for the latest interval (hour,
     * day, month)
     */
    AllSpotMarketSummary(request: DeepPartial<AllSpotMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<AllSpotMarketSummaryResponse>;
    /** Gets derivative market summary for the latest interval (hour, day, month) */
    DerivativeMarketSummary(request: DeepPartial<DerivativeMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<DerivativeMarketSummaryResponse>;
    /**
     * Gets batch summary for all active markets, for the latest interval (hour,
     * day, month)
     */
    AllDerivativeMarketSummary(request: DeepPartial<AllDerivativeMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<AllDerivativeMarketSummaryResponse>;
}
export declare class InjectiveChartRPCClientImpl implements InjectiveChartRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    SpotMarketHistory(request: DeepPartial<SpotMarketHistoryRequest>, metadata?: grpc.Metadata): Promise<SpotMarketHistoryResponse>;
    DerivativeMarketHistory(request: DeepPartial<DerivativeMarketHistoryRequest>, metadata?: grpc.Metadata): Promise<DerivativeMarketHistoryResponse>;
    SpotMarketSummary(request: DeepPartial<SpotMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<SpotMarketSummaryResponse>;
    AllSpotMarketSummary(request: DeepPartial<AllSpotMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<AllSpotMarketSummaryResponse>;
    DerivativeMarketSummary(request: DeepPartial<DerivativeMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<DerivativeMarketSummaryResponse>;
    AllDerivativeMarketSummary(request: DeepPartial<AllDerivativeMarketSummaryRequest>, metadata?: grpc.Metadata): Promise<AllDerivativeMarketSummaryResponse>;
}
export declare const InjectiveChartRPCDesc: {
    serviceName: string;
};
export declare const InjectiveChartRPCSpotMarketHistoryDesc: UnaryMethodDefinitionish;
export declare const InjectiveChartRPCDerivativeMarketHistoryDesc: UnaryMethodDefinitionish;
export declare const InjectiveChartRPCSpotMarketSummaryDesc: UnaryMethodDefinitionish;
export declare const InjectiveChartRPCAllSpotMarketSummaryDesc: UnaryMethodDefinitionish;
export declare const InjectiveChartRPCDerivativeMarketSummaryDesc: UnaryMethodDefinitionish;
export declare const InjectiveChartRPCAllDerivativeMarketSummaryDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
