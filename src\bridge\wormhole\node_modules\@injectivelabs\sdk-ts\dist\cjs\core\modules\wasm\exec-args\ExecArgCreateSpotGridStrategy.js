"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
const types_js_1 = require("../types.js");
/**
 * @category Contract Exec Arguments
 */
/** @deprecated */
class ExecArgCreateSpotGridStrategy extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgCreateSpotGridStrategy(params);
    }
    toData() {
        const { params } = this;
        const trailingBounds = params.trailingArithmetic
            ? {
                upper_trailing_bound: params.trailingArithmetic.upperTrailing,
                lower_trailing_bound: params.trailingArithmetic.lowerTrailing,
            }
            : undefined;
        const strategyType = params.trailingArithmetic && trailingBounds
            ? params.trailingArithmetic.lpMode
                ? { trailing_arithmetic_l_p: trailingBounds }
                : { trailing_arithmetic: trailingBounds }
            : params.strategyType ?? types_js_1.StrategyType.Arithmetic;
        return {
            subaccount_id: params.subaccountId,
            levels: params.levels,
            bounds: [params.lowerBound, params.upperBound],
            slippage: params.slippage,
            exit_type: params.exitType,
            stop_loss: params.stopLoss
                ? {
                    exit_type: params.stopLoss.exitType,
                    exit_price: params.stopLoss.exitPrice,
                }
                : undefined,
            take_profit: params.takeProfit
                ? {
                    exit_type: params.takeProfit.exitType,
                    exit_price: params.takeProfit.exitPrice,
                }
                : undefined,
            strategy_type: strategyType,
            ...(params.feeRecipient && {
                fee_recipient: params.feeRecipient,
            }),
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('create_strategy', this.toData());
    }
}
exports.default = ExecArgCreateSpotGridStrategy;
