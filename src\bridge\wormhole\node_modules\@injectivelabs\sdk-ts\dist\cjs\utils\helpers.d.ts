import { CosmosBaseV1Beta1Coin } from '@injectivelabs/core-proto-ts';
import { Coin } from '@injectivelabs/ts-types';
export declare const isServerSide: () => boolean;
export declare const isReactNative: () => boolean;
export declare const isNode: () => boolean;
export declare const isBrowser: () => boolean;
export declare const objectToJson: (object: Record<string, any>, params?: {
    replacer?: any;
    indentation?: number;
} | undefined) => string;
export declare const protoObjectToJson: (object: any, params?: {
    replacer?: any;
    indentation?: number;
} | undefined) => string;
export declare const grpcCoinToUiCoin: (coin: CosmosBaseV1Beta1Coin.Coin) => Coin;
export declare const uint8ArrayToString: (string: string | Uint8Array | null | undefined) => string;
export declare const sortObjectByKeysWithReduce: <T>(obj: T) => T;
export declare const sortObjectByKeys: <T>(obj: T) => T;
export declare const getErrorMessage: (error: any, endpoint: string) => string;
/**
 * Converts value to it's number representation
 */
export declare const hexToNumber: (value: string) => number;
export declare function isJsonString<T>(str: T): boolean;
