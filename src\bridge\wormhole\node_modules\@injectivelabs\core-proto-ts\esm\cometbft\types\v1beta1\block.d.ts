import _m0 from "protobufjs/minimal.js";
import { EvidenceList } from "./evidence";
import { Commit, Data, Header } from "./types";
export declare const protobufPackage = "cometbft.types.v1beta1";
/** Block defines the structure of a block in the CometBFT blockchain. */
export interface Block {
    header: Header | undefined;
    data: Data | undefined;
    evidence: EvidenceList | undefined;
    lastCommit: Commit | undefined;
}
export declare const Block: {
    encode(message: Block, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Block;
    fromJSON(object: any): Block;
    toJSON(message: Block): unknown;
    create(base?: DeepPartial<Block>): Block;
    fromPartial(object: DeepPartial<Block>): Block;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
