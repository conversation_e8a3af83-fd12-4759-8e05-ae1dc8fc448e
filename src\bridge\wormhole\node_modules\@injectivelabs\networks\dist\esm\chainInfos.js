import { ChainId, EthereumChainId } from '@injectivelabs/ts-types';
const INJ_DENOM = 'inj';
export const mainnetChainInfo = {
    feeDenom: INJ_DENOM,
    chainId: ChainId.Mainnet,
    ethereumChainId: EthereumChainId.Mainnet,
    env: 'mainnet',
};
export const testnetChainInfo = {
    feeDenom: INJ_DENOM,
    chainId: ChainId.Testnet,
    ethereumChainId: EthereumChainId.Sepolia,
    env: 'testnet',
};
export const devnetChainInfo = {
    feeDenom: INJ_DENOM,
    chainId: ChainId.Devnet,
    ethereumChainId: EthereumChainId.Sepolia,
    env: 'devnet',
};
export const localChainInfo = {
    feeDenom: INJ_DENOM,
    chainId: ChainId.Mainnet,
    ethereumChainId: EthereumChainId.Mainnet,
    env: 'local',
};
