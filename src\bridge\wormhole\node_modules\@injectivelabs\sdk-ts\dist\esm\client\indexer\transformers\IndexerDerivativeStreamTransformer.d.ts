import { StreamOperation } from '../../../types/index.js';
import { InjectiveDerivativeExchangeRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Stream Transformer
 */
export declare class IndexerDerivativeStreamTransformer {
    static tradesStreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamTradesResponse) => {
        trade: import("../index.js").DerivativeTrade | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static positionStreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamPositionsResponse) => {
        position: import("../index.js").Position | undefined;
        timestamp: string;
    };
    static ordersStreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamOrdersResponse) => {
        order: import("../index.js").DerivativeLimitOrder | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static orderHistoryStreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamOrdersHistoryResponse) => {
        order: import("../index.js").DerivativeOrderHistory | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static orderbookV2StreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamOrderbookV2Response) => {
        orderbook: import("../index.js").OrderbookWithSequence | undefined;
        operation: StreamOperation;
        marketId: string;
        timestamp: string;
    };
    static orderbookUpdateStreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamOrderbookUpdateResponse) => {
        orderbook: import("../index.js").OrderbookWithSequence | undefined;
        operation: StreamOperation;
        marketId: string;
        timestamp: string;
    };
    static positionV2StreamCallback: (response: InjectiveDerivativeExchangeRpc.StreamPositionsV2Response) => {
        position: import("../index.js").PositionV2 | undefined;
        timestamp: string;
    };
}
