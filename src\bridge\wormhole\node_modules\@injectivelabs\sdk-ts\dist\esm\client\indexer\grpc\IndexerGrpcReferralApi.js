import { UnspecifiedErrorCode, GrpcUnaryRequestException, } from '@injectivelabs/exceptions';
import { InjectiveReferralRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { IndexerGrpcReferralTransformer } from '../transformers/index.js';
import { IndexerModule } from '../types/index.js';
/**
 * @category Indexer Grpc API
 */
export class IndexerGrpcReferralApi extends BaseGrpcConsumer {
    module = IndexerModule.Referral;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new InjectiveReferralRpc.InjectiveReferralRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchReferrerDetails(address) {
        const request = InjectiveReferralRpc.GetReferrerDetailsRequest.create();
        request.referrerAddress = address;
        try {
            const response = await this.retry(() => this.client.GetReferrerDetails(request, this.metadata));
            return IndexerGrpcReferralTransformer.referrerDetailsResponseToReferrerDetails(address, response);
        }
        catch (e) {
            if (e instanceof InjectiveReferralRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
    async fetchInviteeDetails(address) {
        const request = InjectiveReferralRpc.GetInviteeDetailsRequest.create();
        request.inviteeAddress = address;
        try {
            const response = await this.retry(() => this.client.GetInviteeDetails(request, this.metadata));
            return IndexerGrpcReferralTransformer.inviteeDetailsResponseToInviteeDetails(response);
        }
        catch (e) {
            if (e instanceof InjectiveReferralRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
    async fetchReferrerByCode(code) {
        const request = InjectiveReferralRpc.GetReferrerByCodeRequest.create();
        request.referralCode = code;
        try {
            const response = await this.retry(() => this.client.GetReferrerByCode(request, this.metadata));
            return IndexerGrpcReferralTransformer.referrerByCodeResponseToReferrerByCode(response);
        }
        catch (e) {
            if (e instanceof InjectiveReferralRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: e.code,
                    context: 'Referral',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Referral',
                contextModule: this.module,
            });
        }
    }
}
