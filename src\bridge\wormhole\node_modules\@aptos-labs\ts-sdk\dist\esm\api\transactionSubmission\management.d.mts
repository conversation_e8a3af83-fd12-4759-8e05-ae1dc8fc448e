import EventEmitter from 'eventemitter3';
import { AptosConfig } from '../aptosConfig.mjs';
import { InputGenerateTransactionPayloadData, InputGenerateTransactionOptions } from '../../transactions/types.mjs';
import { A as Account } from '../../Ed25519Account-D9XrCLfE.mjs';
import { TransactionWorkerEvents, TransactionWorker } from '../../transactions/management/transactionWorker.mjs';
import '../../types/types.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../utils/const.mjs';
import '../../bcs/serializable/moveStructs.mjs';
import '../../bcs/serializable/movePrimitives.mjs';
import '../../bcs/deserializer.mjs';
import '../../bcs/serializer.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import '../../bcs/serializable/fixedBytes.mjs';
import '../../core/accountAddress.mjs';
import '../../publicKey-CJOcUwJK.mjs';
import '../../core/crypto/signature.mjs';
import '../../transactions/authenticator/account.mjs';
import '../../core/crypto/ed25519.mjs';
import '../../core/crypto/privateKey.mjs';
import '../../core/crypto/multiEd25519.mjs';
import '../../core/crypto/multiKey.mjs';
import '../../core/crypto/singleKey.mjs';
import '../../core/crypto/secp256k1.mjs';
import '../../transactions/instances/rawTransaction.mjs';
import '../../transactions/instances/chainId.mjs';
import '../../transactions/instances/transactionPayload.mjs';
import '../../transactions/instances/identifier.mjs';
import '../../transactions/instances/moduleId.mjs';
import '../../transactions/typeTag/index.mjs';
import '../../transactions/instances/simpleTransaction.mjs';
import '../../transactions/instances/multiAgentTransaction.mjs';
import '../../transactions/management/accountSequenceNumber.mjs';
import '../../transactions/management/asyncQueue.mjs';

declare class TransactionManagement extends EventEmitter<TransactionWorkerEvents> {
    account: Account;
    transactionWorker: TransactionWorker;
    readonly config: AptosConfig;
    /**
     * Initializes a new instance of the Aptos client with the provided configuration settings.
     * This allows you to interact with the Aptos blockchain using the specified network and options.
     *
     * @param config - The configuration settings for the Aptos client.
     * @param config.network - The network to connect to (e.g., TESTNET, MAINNET).
     * @param config.nodeUrl - The URL of the Aptos node to connect to.
     * @param config.account - Optional account settings for authentication.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * async function runExample() {
     *     // Create a configuration for the Aptos client
     *     const config = new AptosConfig({
     *         network: Network.TESTNET, // specify the network to use
     *         nodeUrl: "https://testnet.aptos.dev" // replace with your node URL
     *     });
     *
     *     // Initialize the Aptos client with the configuration
     *     const aptos = new Aptos(config);
     *
     *     console.log("Aptos client initialized successfully.");
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    constructor(config: AptosConfig);
    /**
     * Initializes the transaction worker using the provided sender account and begins listening for events.
     * This function is essential for setting up the transaction processing environment.
     *
     * @param args - The arguments for starting the transaction worker.
     * @param args.sender - The sender account to sign and submit the transaction.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network, Account } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *     const sender = Account.generate(); // Generate a new account for sending transactions
     *
     *     // Start the transaction worker with the sender account
     *     aptos.start({ sender });
     *
     *     console.log("Transaction worker started with sender:", sender.accountAddress);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    private start;
    /**
     * Pushes transaction data to the transaction worker for processing.
     *
     * @param args.data An array of transaction payloads to be processed.
     * @param args.options Optional. Transaction generation configurations (excluding accountSequenceNumber).
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Prepare transaction payloads
     *   const payloads = [
     *      {}, // Build your first transaction payload
     *      {}, // Build your second transaction payload
     *   ];
     *
     *   // Push transaction data to the worker
     *   aptos.push({
     *     data: payloads,
     *     {}, // Specify options as needed
     *   });
     *
     *   console.log("Transaction data pushed successfully.");
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    private push;
    /**
     * Starts listening to transaction worker events, allowing the application to respond to transaction status changes.
     * This function enables the application to handle events such as transaction sent, execution success, or failure.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Register to listen for transaction events
     *   aptos.registerToEvents();
     *
     *   // You can send a transaction here to see the events in action
     *   const sender = Account.generate(); // replace with a real account
     *   const destination = Account.generate(); // replace with a real account
     *
     *   const transaction = await aptos.transaction.build.simple({
     *     sender: sender.accountAddress,
     *     data: {
     *       function: "0x1::aptos_account::transfer",
     *       functionArguments: [destination.accountAddress, 100],
     *     },
     *   });
     *
     *   await aptos.transaction.send(transaction);
     *
     *   console.log("Transaction sent and events registered.");
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    private registerToEvents;
    /**
     * Send batch transactions for a single account.
     *
     * This function uses a transaction worker that receives payloads to be processed
     * and submitted to chain.
     * Note that this process is best for submitting multiple transactions that
     * don't rely on each other, i.e. batch funds, batch token mints, etc.
     *
     * If any worker failure, the functions throws an error.
     *
     * @param args.sender The sender account to sign and submit the transaction
     * @param args.data An array of transaction payloads
     * @param args.options optional. Transaction generation configurations (excluding accountSequenceNumber)
     *
     * @return void. Throws if any error
     * @group Implementation
     */
    forSingleAccount(args: {
        sender: Account;
        data: InputGenerateTransactionPayloadData[];
        options?: Omit<InputGenerateTransactionOptions, "accountSequenceNumber">;
    }): void;
}

export { TransactionManagement };
