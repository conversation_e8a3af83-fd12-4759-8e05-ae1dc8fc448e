{"version": 3, "file": "eip2930Transaction.js", "sourceRoot": "", "sources": ["../src/eip2930Transaction.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAUyB;AACzB,yDAAwD;AAExD,uDAAmD;AACnD,iCAAoC;AAYpC,MAAM,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAM,uBAAuB,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;AAElG;;;;;GAKG;AACH,MAAa,4BAA6B,SAAQ,iCAA6C;IA4F7F;;;;;;OAMG;IACH,YAAmB,MAA+B,EAAE,OAAkB,EAAE;QACtE,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,IAAI,CAAC,CAAA;QA5FpD;;;;;WAKG;QACO,qBAAgB,GAAG,QAAQ,CAAA;QAuFnC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;QAEhD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAEtE,kCAAkC;QAClC,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,kBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,QAAQ,GAAG,IAAA,qBAAc,EAAC,IAAA,eAAQ,EAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE3E,IAAI,CAAC,+BAA+B,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,kBAAW,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAA;YAC3E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IA3HD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAA+B,EAAE,OAAkB,EAAE;QAC5E,OAAO,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACrE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,sFAAsF,gBAAgB,eAAe,UAAU;iBAC5H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,QAAQ,CAAC,KAAK,CAAC,EAAE,CACrB,CAAA;SACF;QAED,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,SAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAE5E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,4BAA4B,CAAC,eAAe,CAAC,MAAa,EAAE,IAAI,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAoC,EAAE,OAAkB,EAAE;QACtF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEzF,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,IAAA,8BAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,MAAM,eAAe,GAAe,EAAE,CAAA;QAEtC,OAAO,IAAI,4BAA4B,CACrC;YACE,OAAO,EAAE,IAAA,qBAAc,EAAC,OAAO,CAAC;YAChC,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,eAAe;YACzC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,qBAAc,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAClD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAmDD;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,CAAA;QAC7B,IAAI,IAAI,MAAM,CAAC,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAE3E,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;IACnD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,6BAAsB,EAAC,IAAI,CAAC,OAAO,CAAC;YACpC,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACxE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC;YACnB,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAgB,CAAC,CAAC,CAAC;SACvD,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACnC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;YAC5B,uBAAuB;YACvB,MAAM,CAAC,IAAI,CAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAgB,CAAC,CAAC,CAAC;SACvD,CAAC,CAAA;QACF,IAAI,WAAW,EAAE;YACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,OAAO,CAAC,CAAC,CAAA;SACvC;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;aAC3D;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAClD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QAExB,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,IAAI;YACF,OAAO,IAAA,gBAAS,EACd,OAAO,EACP,CAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACjE,IAAA,6BAAsB,EAAC,CAAE,CAAC,EAC1B,IAAA,6BAAsB,EAAC,CAAE,CAAC,CAC3B,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/C,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,4BAA4B,CAAC,UAAU,CAC5C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;YACjB,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;YACpB,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;SACrB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAErE,OAAO;YACL,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1D,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,mFAAmF;QACnF,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,oBAAoB,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,EAAE,CAAA;QACxF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;CACF;AAvWD,oEAuWC"}