{"version": 3, "sources": ["../../src/core/crypto/secp256k1.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { sha3_256 } from \"@noble/hashes/sha3\";\nimport { secp256k1 } from \"@noble/curves/secp256k1\";\nimport { HDKey } from \"@scure/bip32\";\nimport { Serializable, Deserializer, Serializer } from \"../../bcs\";\nimport { Hex } from \"../hex\";\nimport { HexInput, PrivateKeyVariants } from \"../../types\";\nimport { isValidBIP44Path, mnemonicToSeed } from \"./hdKey\";\nimport { PrivateKey } from \"./privateKey\";\nimport { PublicKey } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { convertSigningMessage } from \"./utils\";\nimport { AptosConfig } from \"../../api\";\n\n/**\n * Represents a Secp256k1 ECDSA public key.\n *\n * @extends PublicKey\n * @property LENGTH - The length of the Secp256k1 public key in bytes.\n * @group Implementation\n * @category Serialization\n */\nexport class Secp256k1Pub<PERSON><PERSON><PERSON> extends PublicKey {\n  // Secp256k1 ecdsa public keys contain a prefix indicating compression and two 32-byte coordinates.\n  static readonly LENGTH: number = 65;\n\n  // If it's compressed, it is only 33 bytes\n  static readonly COMPRESSED_LENGTH: number = 33;\n\n  // Hex value of the public key\n  private readonly key: Hex;\n\n  /**\n   * Create a new PublicKey instance from a HexInput, which can be a string or Uint8Array.\n   * This constructor validates the length of the provided signature data.\n   *\n   * @param hexInput - A HexInput (string or Uint8Array) representing the signature data.\n   * @throws Error if the length of the signature data is not equal to Secp256k1Signature.LENGTH.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const hex = Hex.fromHexInput(hexInput);\n    const { length } = hex.toUint8Array();\n    if (length === Secp256k1PublicKey.LENGTH) {\n      this.key = hex;\n    } else if (length === Secp256k1PublicKey.COMPRESSED_LENGTH) {\n      const point = secp256k1.ProjectivePoint.fromHex(hex.toUint8Array());\n      this.key = Hex.fromHexInput(point.toRawBytes(false));\n    } else {\n      throw new Error(\n        `PublicKey length should be ${Secp256k1PublicKey.LENGTH} or ${Secp256k1PublicKey.COMPRESSED_LENGTH}, received ${length}`,\n      );\n    }\n  }\n\n  // region PublicKey\n  /**\n   * Verifies a Secp256k1 signature against the public key.\n   *\n   * This function checks the validity of a signature for a given message, ensuring that the signature is canonical as a malleability check.\n   *\n   * @param args - The arguments for verifying the signature.\n   * @param args.message - The message that was signed.\n   * @param args.signature - The signature to verify against the public key.\n   * @group Implementation\n   * @category Serialization\n   */\n  verifySignature(args: { message: HexInput; signature: Secp256k1Signature }): boolean {\n    const { message, signature } = args;\n    const messageToVerify = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToVerify).toUint8Array();\n    const messageSha3Bytes = sha3_256(messageBytes);\n    const signatureBytes = signature.toUint8Array();\n    return secp256k1.verify(signatureBytes, messageSha3Bytes, this.key.toUint8Array(), { lowS: true });\n  }\n\n  /**\n   * Note: Secp256k1Signatures can be verified syncronously.\n   *\n   * Verifies the provided signature against the given message.\n   * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.\n   *\n   * @param args - The arguments for signature verification.\n   * @param args.aptosConfig - The configuration object for connecting to the Aptos network\n   * @param args.message - The message that was signed.\n   * @param args.signature - The signature to verify, which must be an instance of Secp256k1Signature.\n   * @returns A boolean indicating whether the signature is valid for the given message.\n   * @group Implementation\n   * @category Serialization\n   */\n  async verifySignatureAsync(args: {\n    aptosConfig: AptosConfig;\n    message: HexInput;\n    signature: Secp256k1Signature;\n  }): Promise<boolean> {\n    return this.verifySignature(args);\n  }\n\n  /**\n   * Get the data as a Uint8Array representation.\n   *\n   * @returns Uint8Array representation of the data.\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  /**\n   * Serializes the data into a byte array using the provided serializer.\n   * This function is essential for converting data into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to convert the data.\n   * @group Implementation\n   * @category Serialization\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.key.toUint8Array());\n  }\n\n  /**\n   * Deserializes a Secp256k1Signature from the provided deserializer.\n   * This function allows you to reconstruct a Secp256k1Signature object from its serialized byte representation.\n   *\n   * @param deserializer - The deserializer instance used to read the serialized data.\n   * @group Implementation\n   * @category Serialization\n   */\n  // eslint-disable-next-line class-methods-use-this\n  deserialize(deserializer: Deserializer) {\n    const hex = deserializer.deserializeBytes();\n    return new Secp256k1Signature(hex);\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1PublicKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Secp256k1PublicKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * Determine if the provided public key is an instance of Secp256k1PublicKey.\n   *\n   * @deprecated use `instanceof Secp256k1PublicKey` instead\n   * @param publicKey - The public key to check.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isPublicKey(publicKey: PublicKey): publicKey is Secp256k1PublicKey {\n    return publicKey instanceof Secp256k1PublicKey;\n  }\n\n  /**\n   * Determines if the provided public key is a valid instance of a Secp256k1 public key.\n   * This function checks for the presence of a \"key\" property and validates the length of the key data.\n   *\n   * @param publicKey - The public key to validate.\n   * @returns A boolean indicating whether the public key is a valid Secp256k1 public key.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isInstance(publicKey: PublicKey): publicKey is Secp256k1PublicKey {\n    return \"key\" in publicKey && (publicKey.key as any)?.data?.length === Secp256k1PublicKey.LENGTH;\n  }\n}\n\n/**\n * Represents a Secp256k1 ECDSA private key, providing functionality to create, sign messages,\n * derive public keys, and serialize/deserialize the key.\n * @group Implementation\n * @category Serialization\n */\nexport class Secp256k1PrivateKey extends Serializable implements PrivateKey {\n  /**\n   * Length of Secp256k1 ecdsa private key\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The private key bytes\n   * @private\n   * @group Implementation\n   * @category Serialization\n   */\n  private readonly key: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new PrivateKey instance from a Uint8Array or String.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   * @param strict If true, private key must AIP-80 compliant.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(hexInput: HexInput, strict?: boolean) {\n    super();\n\n    const privateKeyHex = PrivateKey.parseHexInput(hexInput, PrivateKeyVariants.Secp256k1, strict);\n    if (privateKeyHex.toUint8Array().length !== Secp256k1PrivateKey.LENGTH) {\n      throw new Error(`PrivateKey length should be ${Secp256k1PrivateKey.LENGTH}`);\n    }\n\n    this.key = privateKeyHex;\n  }\n\n  /**\n   * Generate a new random private key.\n   *\n   * @returns Secp256k1PrivateKey - A newly generated Secp256k1 private key.\n   * @group Implementation\n   * @category Serialization\n   */\n  static generate(): Secp256k1PrivateKey {\n    const hexInput = secp256k1.utils.randomPrivateKey();\n    return new Secp256k1PrivateKey(hexInput, false);\n  }\n\n  /**\n   * Derives a private key from a mnemonic seed phrase using a specified BIP44 path.\n   *\n   * @param path - The BIP44 path to derive the key from.\n   * @param mnemonics - The mnemonic seed phrase used for key generation.\n   *\n   * @returns The generated private key.\n   *\n   * @throws Error if the provided path is not a valid BIP44 path.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromDerivationPath(path: string, mnemonics: string): Secp256k1PrivateKey {\n    if (!isValidBIP44Path(path)) {\n      throw new Error(`Invalid derivation path ${path}`);\n    }\n    return Secp256k1PrivateKey.fromDerivationPathInner(path, mnemonicToSeed(mnemonics));\n  }\n\n  /**\n   * Derives a private key from a specified BIP44 path using a given seed.\n   * This function is essential for generating keys that follow the hierarchical deterministic (HD) wallet structure.\n   *\n   * @param path - The BIP44 path used for key derivation.\n   * @param seed - The seed phrase created by the mnemonics, represented as a Uint8Array.\n   * @returns The generated private key as an instance of Secp256k1PrivateKey.\n   * @throws Error if the derived private key is invalid.\n   * @group Implementation\n   * @category Serialization\n   */\n  private static fromDerivationPathInner(path: string, seed: Uint8Array): Secp256k1PrivateKey {\n    const { privateKey } = HDKey.fromMasterSeed(seed).derive(path);\n    // library returns privateKey as Uint8Array | null\n    if (privateKey === null) {\n      throw new Error(\"Invalid key\");\n    }\n\n    return new Secp256k1PrivateKey(privateKey, false);\n  }\n\n  // endregion\n\n  // region PrivateKey\n\n  /**\n   * Sign the given message with the private key.\n   * This function generates a cryptographic signature for the provided message, ensuring the signature is canonical and non-malleable.\n   *\n   * @param message - A message in HexInput format to be signed.\n   * @returns Signature - The generated signature for the provided message.\n   * @group Implementation\n   * @category Serialization\n   */\n  sign(message: HexInput): Secp256k1Signature {\n    const messageToSign = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToSign);\n    const messageHashBytes = sha3_256(messageBytes.toUint8Array());\n    const signature = secp256k1.sign(messageHashBytes, this.key.toUint8Array(), { lowS: true });\n    return new Secp256k1Signature(signature.toCompactRawBytes());\n  }\n\n  /**\n   * Derive the Secp256k1PublicKey from this private key.\n   *\n   * @returns Secp256k1PublicKey The derived public key.\n   * @group Implementation\n   * @category Serialization\n   */\n  publicKey(): Secp256k1PublicKey {\n    const bytes = secp256k1.getPublicKey(this.key.toUint8Array(), false);\n    return new Secp256k1PublicKey(bytes);\n  }\n\n  /**\n   * Get the private key in bytes (Uint8Array).\n   *\n   * @returns\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  /**\n   * Get the private key as a string representation.\n   *\n   * @returns string representation of the private key\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): string {\n    return this.toAIP80String();\n  }\n\n  /**\n   * Get the private key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the private key.\n   */\n  toHexString(): string {\n    return this.key.toString();\n  }\n\n  /**\n   * Get the private key as a AIP-80 compliant hex string.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @returns AIP-80 compliant string representation of the private key.\n   */\n  toAIP80String(): string {\n    return PrivateKey.formatPrivateKey(this.key.toString(), PrivateKeyVariants.Secp256k1);\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1PrivateKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Secp256k1PrivateKey(bytes, false);\n  }\n\n  // endregion\n\n  /**\n   * Determines if the provided private key is an instance of Secp256k1PrivateKey.\n   *\n   * @param privateKey - The private key to be checked.\n   *\n   * @deprecated use `instanceof Secp256k1PrivateKey` instead\n   * @group Implementation\n   * @category Serialization\n   */\n  static isPrivateKey(privateKey: PrivateKey): privateKey is Secp256k1PrivateKey {\n    return privateKey instanceof Secp256k1PrivateKey;\n  }\n}\n\n/**\n * Represents a signature of a message signed using a Secp256k1 ECDSA private key.\n *\n * @group Implementation\n * @category Serialization\n */\nexport class Secp256k1Signature extends Signature {\n  /**\n   * Secp256k1 ecdsa signatures are 256-bit.\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH = 64;\n\n  /**\n   * The signature bytes\n   * @private\n   * @group Implementation\n   * @category Serialization\n   */\n  private readonly data: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new Signature instance from a Uint8Array or String.\n   *\n   * @param hexInput A HexInput (string or Uint8Array)\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(hexInput: HexInput) {\n    super();\n    const data = Hex.fromHexInput(hexInput);\n    if (data.toUint8Array().length !== Secp256k1Signature.LENGTH) {\n      throw new Error(\n        `Signature length should be ${Secp256k1Signature.LENGTH}, received ${data.toUint8Array().length}`,\n      );\n    }\n    this.data = data;\n  }\n\n  // endregion\n\n  // region Signature\n\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.data.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Secp256k1Signature {\n    const hex = deserializer.deserializeBytes();\n    return new Secp256k1Signature(hex);\n  }\n\n  // endregion\n}\n"], "mappings": "sSAGA,OAAS,YAAAA,MAAgB,qBACzB,OAAS,aAAAC,MAAiB,0BAC1B,OAAS,SAAAC,MAAa,eAmBf,IAAMC,EAAN,MAAMA,UAA2BC,CAAU,CAmBhD,YAAYC,EAAoB,CAC9B,MAAM,EAEN,IAAMC,EAAMC,EAAI,aAAaF,CAAQ,EAC/B,CAAE,OAAAG,CAAO,EAAIF,EAAI,aAAa,EACpC,GAAIE,IAAWL,EAAmB,OAChC,KAAK,IAAMG,UACFE,IAAWL,EAAmB,kBAAmB,CAC1D,IAAMM,EAAQC,EAAU,gBAAgB,QAAQJ,EAAI,aAAa,CAAC,EAClE,KAAK,IAAMC,EAAI,aAAaE,EAAM,WAAW,EAAK,CAAC,CACrD,KACE,OAAM,IAAI,MACR,8BAA8BN,EAAmB,MAAM,OAAOA,EAAmB,iBAAiB,cAAcK,CAAM,EACxH,CAEJ,CAcA,gBAAgBG,EAAqE,CACnF,GAAM,CAAE,QAAAC,EAAS,UAAAC,CAAU,EAAIF,EACzBG,EAAkBC,EAAsBH,CAAO,EAC/CI,EAAeT,EAAI,aAAaO,CAAe,EAAE,aAAa,EAC9DG,EAAmBC,EAASF,CAAY,EACxCG,EAAiBN,EAAU,aAAa,EAC9C,OAAOH,EAAU,OAAOS,EAAgBF,EAAkB,KAAK,IAAI,aAAa,EAAG,CAAE,KAAM,EAAK,CAAC,CACnG,CAgBA,MAAM,qBAAqBN,EAIN,CACnB,OAAO,KAAK,gBAAgBA,CAAI,CAClC,CASA,cAA2B,CACzB,OAAO,KAAK,IAAI,aAAa,CAC/B,CAcA,UAAUS,EAA8B,CACtCA,EAAW,eAAe,KAAK,IAAI,aAAa,CAAC,CACnD,CAWA,YAAYC,EAA4B,CACtC,IAAMf,EAAMe,EAAa,iBAAiB,EAC1C,OAAO,IAAIC,EAAmBhB,CAAG,CACnC,CAEA,OAAO,YAAYe,EAAgD,CACjE,IAAME,EAAQF,EAAa,iBAAiB,EAC5C,OAAO,IAAIlB,EAAmBoB,CAAK,CACrC,CAYA,OAAO,YAAYC,EAAuD,CACxE,OAAOA,aAAqBrB,CAC9B,CAWA,OAAO,WAAWqB,EAAuD,CACvE,MAAO,QAASA,GAAcA,EAAU,KAAa,MAAM,SAAWrB,EAAmB,MAC3F,CACF,EAvJaA,EAEK,OAAiB,GAFtBA,EAKK,kBAA4B,GALvC,IAAMsB,EAANtB,EA+JMuB,EAAN,MAAMA,UAA4BC,CAAmC,CA4B1E,YAAYtB,EAAoBuB,EAAkB,CAChD,MAAM,EAEN,IAAMC,EAAgBC,EAAW,cAAczB,cAAwCuB,CAAM,EAC7F,GAAIC,EAAc,aAAa,EAAE,SAAWH,EAAoB,OAC9D,MAAM,IAAI,MAAM,+BAA+BA,EAAoB,MAAM,EAAE,EAG7E,KAAK,IAAMG,CACb,CASA,OAAO,UAAgC,CACrC,IAAMxB,EAAWK,EAAU,MAAM,iBAAiB,EAClD,OAAO,IAAIgB,EAAoBrB,EAAU,EAAK,CAChD,CAcA,OAAO,mBAAmB0B,EAAcC,EAAwC,CAC9E,GAAI,CAACC,EAAiBF,CAAI,EACxB,MAAM,IAAI,MAAM,2BAA2BA,CAAI,EAAE,EAEnD,OAAOL,EAAoB,wBAAwBK,EAAMG,EAAeF,CAAS,CAAC,CACpF,CAaA,OAAe,wBAAwBD,EAAcI,EAAuC,CAC1F,GAAM,CAAE,WAAAC,CAAW,EAAIC,EAAM,eAAeF,CAAI,EAAE,OAAOJ,CAAI,EAE7D,GAAIK,IAAe,KACjB,MAAM,IAAI,MAAM,aAAa,EAG/B,OAAO,IAAIV,EAAoBU,EAAY,EAAK,CAClD,CAeA,KAAKxB,EAAuC,CAC1C,IAAM0B,EAAgBvB,EAAsBH,CAAO,EAC7CI,EAAeT,EAAI,aAAa+B,CAAa,EAC7CC,EAAmBrB,EAASF,EAAa,aAAa,CAAC,EACvDH,EAAYH,EAAU,KAAK6B,EAAkB,KAAK,IAAI,aAAa,EAAG,CAAE,KAAM,EAAK,CAAC,EAC1F,OAAO,IAAIjB,EAAmBT,EAAU,kBAAkB,CAAC,CAC7D,CASA,WAAgC,CAC9B,IAAMU,EAAQb,EAAU,aAAa,KAAK,IAAI,aAAa,EAAG,EAAK,EACnE,OAAO,IAAIe,EAAmBF,CAAK,CACrC,CASA,cAA2B,CACzB,OAAO,KAAK,IAAI,aAAa,CAC/B,CASA,UAAmB,CACjB,OAAO,KAAK,cAAc,CAC5B,CAOA,aAAsB,CACpB,OAAO,KAAK,IAAI,SAAS,CAC3B,CASA,eAAwB,CACtB,OAAOO,EAAW,iBAAiB,KAAK,IAAI,SAAS,aAA+B,CACtF,CAMA,UAAUV,EAA8B,CACtCA,EAAW,eAAe,KAAK,aAAa,CAAC,CAC/C,CAEA,OAAO,YAAYC,EAAiD,CAClE,IAAME,EAAQF,EAAa,iBAAiB,EAC5C,OAAO,IAAIK,EAAoBH,EAAO,EAAK,CAC7C,CAaA,OAAO,aAAaa,EAA2D,CAC7E,OAAOA,aAAsBV,CAC/B,CACF,EAjMaA,EAMK,OAAiB,GAN5B,IAAMc,EAANd,EAyMMe,EAAN,MAAMA,UAA2BC,CAAU,CAyBhD,YAAYrC,EAAoB,CAC9B,MAAM,EACN,IAAMsC,EAAOpC,EAAI,aAAaF,CAAQ,EACtC,GAAIsC,EAAK,aAAa,EAAE,SAAWF,EAAmB,OACpD,MAAM,IAAI,MACR,8BAA8BA,EAAmB,MAAM,cAAcE,EAAK,aAAa,EAAE,MAAM,EACjG,EAEF,KAAK,KAAOA,CACd,CAMA,cAA2B,CACzB,OAAO,KAAK,KAAK,aAAa,CAChC,CAMA,UAAUvB,EAA8B,CACtCA,EAAW,eAAe,KAAK,KAAK,aAAa,CAAC,CACpD,CAEA,OAAO,YAAYC,EAAgD,CACjE,IAAMf,EAAMe,EAAa,iBAAiB,EAC1C,OAAO,IAAIoB,EAAmBnC,CAAG,CACnC,CAGF,EA1DamC,EAMK,OAAS,GANpB,IAAMnB,EAANmB", "names": ["sha3_256", "secp256k1", "HDKey", "_Secp256k1PublicKey", "PublicKey", "hexInput", "hex", "Hex", "length", "point", "secp256k1", "args", "message", "signature", "messageToVerify", "convertSigningMessage", "messageBytes", "messageSha3Bytes", "sha3_256", "signatureBytes", "serializer", "deserializer", "Secp256k1Signature", "bytes", "public<PERSON>ey", "Secp256k1PublicKey", "_Secp256k1PrivateKey", "Serializable", "strict", "privateKeyHex", "Private<PERSON><PERSON>", "path", "mnemonics", "isValidBIP44Path", "mnemonicToSeed", "seed", "privateKey", "HDKey", "messageToSign", "messageHashBytes", "Secp256k1PrivateKey", "_Secp256k1Signature", "Signature", "data"]}