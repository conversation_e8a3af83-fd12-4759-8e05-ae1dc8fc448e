{"version": 3, "sources": ["/Users/<USER>/aptos-ts-sdk-2/dist/common/cli/index.js", "../../../src/cli/localNode.ts", "../../../src/cli/move.ts"], "names": ["LocalNode", "args", "resolve", "reject", "kill", "err", "cliCommand", "cli<PERSON><PERSON>s", "currentPlatform", "platform", "spawnConfig", "spawn", "data", "str", "operational", "start", "last", "sleep", "Move", "network", "profile", "extraArguments", "showStdout"], "mappings": "AAAA,w0BAAyC,8CCEa,yFACrC,wBACQ,IAWZA,CAAAA,CAAN,KAAgB,CASrB,WAAA,CAAYC,CAAAA,CAAiC,CAR7C,IAAA,CAAS,qBAAA,CAAwB,EAAA,CAEjC,IAAA,CAAS,kBAAA,CAAqB,wBAAA,CAE9B,IAAA,CAAA,UAAA,CAAsB,CAAA,CAAA,CAEtB,IAAA,CAAA,OAAA,CAAiD,IAAA,CAG/C,IAAA,CAAK,UAAA,kCAAaA,CAAAA,2BAAM,YAAA,SAAc,CAAA,GACxC,CAUA,MAAM,IAAA,CAAA,CAAsB,CAC1B,MAAM,IAAI,OAAA,CAAQ,CAACC,CAAAA,CAASC,CAAAA,CAAAA,EAAW,iBAChC,IAAA,qBAAK,OAAA,6BAAS,KAAA,EAYnBC,gCAAAA,IAAK,CAAK,OAAA,CAAQ,GAAA,CAAMC,CAAAA,EAAQ,CAC1BA,CAAAA,CACFF,CAAAA,CAAOE,CAAG,CAAA,CAEVH,CAAAA,CAAQ,CAAA,CAAI,CAEhB,CAAC,CACH,CAAC,CACH,CAUA,MAAM,GAAA,CAAA,CAAqB,CACR,MAAM,IAAA,CAAK,kBAAA,CAAmB,CAAA,EAAA,CAI/C,IAAA,CAAK,KAAA,CAAM,CAAA,CACX,MAAM,IAAA,CAAK,oBAAA,CAAqB,CAAA,CAClC,CAWA,KAAA,CAAA,CAAc,CACZ,IAAMI,CAAAA,CAAa,KAAA,CACbC,CAAAA,CAAU,CAAC,OAAA,CAAS,MAAA,CAAQ,cAAA,CAAgB,iBAAA,CAAmB,cAAA,CAAgB,oBAAoB,CAAA,CAEnGC,CAAAA,CAAkBC,0BAAAA,CAAS,CAC3BC,CAAAA,CAAc,CAClB,GAAA,CAAK,CAAE,GAAG,OAAA,CAAQ,GAAA,CAAK,sBAAA,CAAwB,GAAI,CAAA,CACnD,GAAIF,CAAAA,GAAoB,OAAA,EAAW,CAAE,KAAA,CAAO,CAAA,CAAK,CACnD,CAAA,CAEA,IAAA,CAAK,OAAA,CAAUG,kCAAAA,CAAML,CAAYC,CAAAA,CAASG,CAAW,CAAA,iBAErD,IAAA,qBAAK,OAAA,qBAAQ,MAAA,6BAAQ,EAAA,mBAAG,MAAA,CAASE,CAAAA,EAAc,CAC7C,IAAMC,CAAAA,CAAMD,CAAAA,CAAK,QAAA,CAAS,CAAA,CAEtB,IAAA,CAAK,UAAA,EACP,OAAA,CAAQ,GAAA,CAAIC,CAAG,CAEnB,CAAC,GACH,CAUA,MAAM,oBAAA,CAAA,CAAyC,CAC7C,IAAIC,CAAAA,CAAc,MAAM,IAAA,CAAK,kBAAA,CAAmB,CAAA,CAC1CC,CAAAA,CAAQ,IAAA,CAAK,GAAA,CAAI,CAAA,CAAI,GAAA,CACvBC,CAAAA,CAAOD,CAAAA,CAEX,GAAA,CAAA,CAAO,CAACD,CAAAA,EAAeC,CAAAA,CAAQ,IAAA,CAAK,qBAAA,CAAwBC,CAAAA,CAAAA,CAE1D,MAAMC,iCAAAA,GAAU,CAAA,CAEhBH,CAAAA,CAAc,MAAM,IAAA,CAAK,kBAAA,CAAmB,CAAA,CAC5CE,CAAAA,CAAO,IAAA,CAAK,GAAA,CAAI,CAAA,CAAI,GAAA,CAKtB,EAAA,CAAI,CAACF,CAAAA,CACH,MAAM,IAAI,KAAA,CAAM,yBAAyB,CAAA,CAG3C,MAAO,CAAA,CACT,CASA,MAAM,kBAAA,CAAA,CAAuC,CAC3C,GAAI,CAGF,MAAA,CADa,MAAM,KAAA,CAAM,IAAA,CAAK,kBAAkB,CAAA,CAAA,CACvC,MAAA,GAAW,GAItB,CAAA,UAAmB,CACjB,MAAO,CAAA,CACT,CACF,CACF,CAAA,CC5JA,IAaaI,CAAAA,CAAN,KAAW,CAehB,MAAM,IAAA,CAAKjB,CAAAA,CAKqB,CAC9B,GAAM,CAAE,OAAA,CAAAkB,CAAAA,CAAS,OAAA,CAAAC,CAAAA,CAAS,cAAA,CAAAC,CAAAA,CAAgB,UAAA,CAAAC,CAAW,CAAA,CAAIrB,CAAAA,CACnDM,CAAAA,CAAU,CAAC,OAAA,CAAS,MAAA,CAAQ,CAAA,UAAA,mBAAaY,CAAAA,SAAW,SAAO,CAAA,CAAA", "file": "/Users/<USER>/aptos-ts-sdk-2/dist/common/cli/index.js", "sourcesContent": [null, "/* eslint-disable no-console */\n\nimport { ChildProcessWithoutNullStreams, spawn } from \"child_process\";\nimport kill from \"tree-kill\";\nimport { platform } from \"os\";\n\nimport { sleep } from \"../utils/helpers\";\n\n/**\n * Represents a local node for running a localnet environment.\n * This class provides methods to start, stop, and check the status of the localnet process.\n * It manages the lifecycle of the node process and ensures that it is operational before executing tests.\n * @group Implementation\n * @category CLI\n */\nexport class LocalNode {\n  readonly MAXIMUM_WAIT_TIME_SEC = 75;\n\n  readonly READINESS_ENDPOINT = \"http://127.0.0.1:8070/\";\n\n  showStdout: boolean = true;\n\n  process: ChildProcessWithoutNullStreams | null = null;\n\n  constructor(args?: { showStdout?: boolean }) {\n    this.showStdout = args?.showStdout ?? true;\n  }\n\n  /**\n   * Kills the current process and all its descendant processes.\n   *\n   * @returns {Promise<void>} A promise that resolves to true if the process was successfully killed.\n   * @throws {Error} If there is an error while attempting to kill the process.\n   * @group Implementation\n   * @category CLI\n   */\n  async stop(): Promise<void> {\n    await new Promise((resolve, reject) => {\n      if (!this.process?.pid) return;\n\n      /**\n       * Terminates the process associated with the given process ID.\n       *\n       * @param pid - The process ID of the process to be terminated.\n       * @param callback - A function that is called after the termination attempt is complete.\n       * @param callback.err - An error object if the termination failed; otherwise, null.\n       * @param callback.resolve - A boolean indicating whether the termination was successful.\n       * @group Implementation\n       * @category CLI\n       */\n      kill(this.process.pid, (err) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(true);\n        }\n      });\n    });\n  }\n\n  /**\n   * Runs a localnet and waits for the process to be up.\n   * If the local node process is already running, it returns without starting the process.\n   *\n   * @returns {Promise<void>} A promise that resolves when the process is up.\n   * @group Implementation\n   * @category CLI\n   */\n  async run(): Promise<void> {\n    const nodeIsUp = await this.checkIfProcessIsUp();\n    if (nodeIsUp) {\n      return;\n    }\n    this.start();\n    await this.waitUntilProcessIsUp();\n  }\n\n  /**\n   * Starts the localnet by running the Aptos node with the specified command-line arguments.\n   *\n   * @returns {void}\n   *\n   * @throws {Error} If there is an issue starting the localnet.\n   * @group Implementation\n   * @category CLI\n   */\n  start(): void {\n    const cliCommand = \"npx\";\n    const cliArgs = [\"aptos\", \"node\", \"run-localnet\", \"--force-restart\", \"--assume-yes\", \"--with-indexer-api\"];\n\n    const currentPlatform = platform();\n    const spawnConfig = {\n      env: { ...process.env, ENABLE_KEYLESS_DEFAULT: \"1\" },\n      ...(currentPlatform === \"win32\" && { shell: true }),\n    };\n\n    this.process = spawn(cliCommand, cliArgs, spawnConfig);\n\n    this.process.stdout?.on(\"data\", (data: any) => {\n      const str = data.toString();\n      // Print local node output log\n      if (this.showStdout) {\n        console.log(str);\n      }\n    });\n  }\n\n  /**\n   * Waits for the localnet process to be operational within a specified maximum wait time.\n   * This function continuously checks if the process is up and will throw an error if it fails to start.\n   *\n   * @returns Promise<boolean> - Resolves to true if the process is up, otherwise throws an error.\n   * @group Implementation\n   * @category CLI\n   */\n  async waitUntilProcessIsUp(): Promise<boolean> {\n    let operational = await this.checkIfProcessIsUp();\n    const start = Date.now() / 1000;\n    let last = start;\n\n    while (!operational && start + this.MAXIMUM_WAIT_TIME_SEC > last) {\n      // eslint-disable-next-line no-await-in-loop\n      await sleep(1000);\n      // eslint-disable-next-line no-await-in-loop\n      operational = await this.checkIfProcessIsUp();\n      last = Date.now() / 1000;\n    }\n\n    // If we are here it means something blocks the process to start.\n    // Might worth checking if another process is running on port 8080\n    if (!operational) {\n      throw new Error(\"Process failed to start\");\n    }\n\n    return true;\n  }\n\n  /**\n   * Checks if the localnet is up by querying the readiness endpoint.\n   *\n   * @returns Promise<boolean> - A promise that resolves to true if the localnet is up, otherwise false.\n   * @group Implementation\n   * @category CLI\n   */\n  async checkIfProcessIsUp(): Promise<boolean> {\n    try {\n      // Query readiness endpoint\n      const data = await fetch(this.READINESS_ENDPOINT);\n      if (data.status === 200) {\n        return true;\n      }\n      return false;\n    } catch (err: any) {\n      return false;\n    }\n  }\n}\n", "import { spawn } from \"child_process\";\nimport { platform } from \"os\";\n\nimport { AccountAddress } from \"../core\";\nimport { Network } from \"../utils\";\n\n/**\n * Class representing a Move package management utility for the Aptos blockchain.\n * This class provides methods to initialize directories, compile packages, run tests, publish modules, create objects, upgrade\n * packages, build transaction payloads, and run scripts.\n * @group Implementation\n * @category CLI\n */\nexport class Move {\n  /**\n   * Initialize the current directory for Aptos by configuring the necessary settings.\n   * Configuration will be pushed into .aptos/config.yaml.\n   *\n   * @param args - The arguments for initialization.\n   * @param args.network - Optional Network type argument to use for default settings; defaults to local.\n   * @param args.profile - Optional Profile to use from the config file; defaults to 'default'. This will override associated\n   * settings such as the REST URL, the Faucet URL, and the private key arguments.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async init(args: {\n    network?: Network;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { network, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"init\", `--network=${network ?? \"local\"}`, `--profile=${profile ?? \"default\"}`];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Compile a Move package located at the specified directory path.\n   * This function helps in preparing the Move package for deployment or further processing.\n   *\n   * @param args - The arguments for compiling the package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async compile(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"compile\", \"--package-dir\", packageDirectoryPath];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Run Move unit tests for a specified package.\n   *\n   * @param args - The arguments for running the tests.\n   * @param args.packageDirectoryPath - The path to a Move package (the folder containing a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns The stdout output from running the tests.\n   * @group Implementation\n   * @category CLI\n   */\n  async test(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"test\", \"--package-dir\", packageDirectoryPath];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Publishes the modules to the publisher account on the Aptos blockchain.\n   *\n   * @param args - The arguments for publishing the modules.\n   * @param args.packageDirectoryPath - The path to a move package (the folder with a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async publish(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"publish\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Create a new object and publish the Move package to it on the Aptos blockchain.\n   *\n   * @param args - The arguments for creating the object and publishing the package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.addressName - Address name for the Move package.\n   * @param args.namedAddresses - Named addresses for the Move binary.\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns The object address.\n   *\n   * A complete example in CLI:\n   * aptos move create-object-and-publish-package \\\n   * --package-dir path_to_directory_that_has_move.toml \\\n   * --address-name launchpad_addr \\\n   * --named-addresses \"launchpad_addr=0x123,initial_creator_addr=0x456\" \\\n   * --profile my_profile \\\n   * --assume-yes\n   * @group Implementation\n   * @category CLI\n   */\n  async createObjectAndPublishPackage(args: {\n    packageDirectoryPath: string;\n    addressName: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ objectAddress: string }> {\n    const { packageDirectoryPath, addressName, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"create-object-and-publish-package\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      \"--address-name\",\n      addressName,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    const { output } = await this.runCommand(cliArgs, showStdout);\n    return { objectAddress: this.extractAddressFromOutput(output) };\n  }\n\n  /**\n   * Upgrade a Move package previously published to an object on the Aptos blockchain.\n   * The caller must be the object owner to execute this function.\n   *\n   * @param args - The arguments for upgrading the object package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.objectAddress - Address of the object that the Move package published to. Ex. 0x1000\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async upgradeObjectPackage(args: {\n    packageDirectoryPath: string;\n    objectAddress: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, objectAddress, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"upgrade-object-package\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      \"--object-address\",\n      objectAddress,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Build a publication transaction payload and store it in a JSON output file.\n   *\n   * @param args - The arguments for building the publishing payload.\n   * @param args.packageDirectoryPath - Path to a move package (the folder with a Move.toml file).\n   * @param args.outputFile - Output file to write the publication transaction to.\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]   *\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async buildPublishPayload(args: {\n    packageDirectoryPath: string;\n    outputFile: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { outputFile, packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"build-publish-payload\",\n      \"--json-output-file\",\n      outputFile,\n      \"--package-dir\",\n      packageDirectoryPath,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Runs a Move script using the provided compiled script path and optional parameters. Ensure that the script is compiled\n   * before executing this function.\n   *\n   * @param args - The arguments for running the script.\n   * @param args.compiledScriptPath - Path to a compiled Move script bytecode file.\n   * Ex. \"build/my_package/bytecode_scripts/my_move_script.mv\"\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   *\n   * @returns The standard output from running the script.\n   * @group Implementation\n   * @category CLI\n   */\n  async runScript(args: {\n    compiledScriptPath: string;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { compiledScriptPath, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"run-script\",\n      \"--compiled-script-path\",\n      compiledScriptPath,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  async gasProfile(args: {\n    network: string;\n    transactionId: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string; result?: any }> {\n    const { network, transactionId, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"replay\", \"--profile-gas\", \"--network\", network, \"--txn-id\", transactionId];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Run a command with the specified arguments and return the output.\n   *\n   * @param args - An array of strings representing the command-line arguments to be passed to the command.\n   * @param showStdout - Show the standard output generated by the command.\n   * @returns The standard output generated by the command.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private async runCommand(args: Array<string>, showStdout: boolean = true): Promise<{ result?: any; output: string }> {\n    return new Promise((resolve, reject) => {\n      const currentPlatform = platform();\n      let childProcess;\n      let stdout = \"\";\n      // CLI final stdout is the Result/Error JSON string output\n      // so we need to keep track of the last stdout\n      let lastStdout = \"\";\n\n      // Check if current OS is windows\n      if (currentPlatform === \"win32\") {\n        childProcess = spawn(\"npx\", args, { shell: true });\n      } else {\n        childProcess = spawn(\"npx\", args);\n      }\n\n      childProcess.stdout.on(\"data\", (data) => {\n        lastStdout = data.toString();\n        stdout += data.toString();\n      });\n\n      if (showStdout) {\n        childProcess.stdout.pipe(process.stdout);\n        childProcess.stderr.pipe(process.stderr);\n      }\n      process.stdin.pipe(childProcess.stdin);\n\n      childProcess.on(\"close\", (code) => {\n        if (code === 0) {\n          try {\n            // parse the last stdout as it might be the result\n            const parsed = JSON.parse(lastStdout);\n            if (parsed.Error) {\n              reject(new Error(`Error: ${parsed.Error}`)); // Reject if the \"Error\" key exists\n            } else if (parsed.Result) {\n              resolve({ result: parsed.Result, output: stdout }); // Resolve if the \"Result\" key exists\n            }\n          } catch (error: any) {\n            // resolve the stdout as it might be just a stdout\n            resolve({ output: stdout });\n          }\n        } else {\n          reject(new Error(`Child process exited with code ${code}`)); // Reject with an error if the child process exits with an error code\n        }\n      });\n    });\n  }\n\n  /**\n   * Convert named addresses from a Map into an array separated by a comma.\n   *\n   * @param namedAddresses - A Map where the key is a string representing the name and the value is an AccountAddress.\n   * Ex. {'alice' => '0x123', 'bob' => '0x456'}\n   * @returns An array of named addresses formatted as strings separated by a comma. Ex. \"alice=0x123,bob=0x456\"\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private prepareNamedAddresses(namedAddresses: Map<string, AccountAddress>): Array<string> {\n    const totalNames = namedAddresses.size;\n    const newArgs: Array<string> = [];\n\n    if (totalNames === 0) {\n      return newArgs;\n    }\n\n    newArgs.push(\"--named-addresses\");\n\n    const names: Array<string> = [];\n    namedAddresses.forEach((value, key) => {\n      const toAppend = `${key}=${value.toString()}`;\n      names.push(toAppend);\n    });\n    newArgs.push(names.join(\",\"));\n    return newArgs;\n  }\n\n  /**\n   * Parse named addresses from a Record type into a Map.\n   *\n   * This function transforms a collection of named addresses into a more accessible format by mapping each name to its\n   * corresponding address.\n   *\n   * @param namedAddresses - A record containing named addresses where the key is the name and the value is the AccountAddress.\n   * @returns A Map where each key is a name and each value is the corresponding address.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private parseNamedAddresses(namedAddresses: Record<string, AccountAddress>): Map<string, AccountAddress> {\n    const addressesMap = new Map();\n\n    Object.keys(namedAddresses).forEach((key) => {\n      const address = namedAddresses[key];\n      addressesMap.set(key, address);\n    });\n\n    return addressesMap;\n  }\n\n  /**\n   * Extracts the object address from the provided output string.\n   *\n   * @param output - The output string containing the object address.\n   * @returns The extracted object address.\n   * @throws Error if the object address cannot be extracted from the output.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private extractAddressFromOutput(output: string): string {\n    const match = output.match(\"Code was successfully deployed to object address (0x[0-9a-fA-F]+)\");\n    if (match) {\n      return match[1];\n    }\n    throw new Error(\"Failed to extract object address from output\");\n  }\n}\n"]}