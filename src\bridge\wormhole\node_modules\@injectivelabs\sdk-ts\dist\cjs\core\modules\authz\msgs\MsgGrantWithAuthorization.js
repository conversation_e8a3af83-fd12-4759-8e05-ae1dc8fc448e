"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Messages
 */
class MsgGrantWithAuthorization extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgGrantWithAuthorization(params);
    }
    toProto() {
        const { params } = this;
        const timestamp = this.getTimestamp();
        const grant = core_proto_ts_1.CosmosAuthzV1Beta1Authz.Grant.create();
        grant.authorization = params.authorization.toAny();
        grant.expiration = new Date(Number(timestamp.seconds) * 1000);
        const message = core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.create();
        message.granter = params.granter;
        message.grantee = params.grantee;
        message.grant = grant;
        return core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.fromJSON(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const proto = this.toProto();
        const timestamp = this.getTimestamp();
        const message = proto;
        const messageWithAuthorizationType = (0, snakecase_keys_1.default)({
            ...message,
            grant: {
                authorization: params.authorization.toAmino(),
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        });
        return {
            type: 'cosmos-sdk/MsgGrant',
            value: messageWithAuthorizationType,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.authz.v1beta1.MsgGrant',
            message: proto,
        };
    }
    toWeb3Gw() {
        const { params } = this;
        const amino = this.toAmino();
        const timestamp = this.getTimestamp();
        const messageWithAuthorizationType = {
            granter: amino.value.granter,
            grantee: amino.value.grantee,
            grant: {
                authorization: params.authorization.toWeb3(),
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        };
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...messageWithAuthorizationType,
        };
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712_v1 is not supported for MsgGrantWithAuthorization. Please use EIP712_v2'));
    }
    getTimestamp() {
        const { params } = this;
        if (params.expiration) {
            const timestamp = core_proto_ts_1.GoogleProtobufTimestamp.Timestamp.create();
            timestamp.seconds = params.expiration.toString();
            return timestamp;
        }
        const defaultExpiryYears = params.expiryInSeconds ? 0 : 5;
        const dateNow = new Date();
        const expiration = new Date(dateNow.getFullYear() + (params.expiryInYears || defaultExpiryYears), dateNow.getMonth(), dateNow.getDate());
        const timestamp = core_proto_ts_1.GoogleProtobufTimestamp.Timestamp.create();
        const timestampInSeconds = (expiration.getTime() / 1000 +
            (params.expiryInSeconds || 0)).toString();
        timestamp.seconds = timestampInSeconds;
        return timestamp;
    }
    toBinary() {
        return core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.encode(this.toProto()).finish();
    }
}
exports.default = MsgGrantWithAuthorization;
