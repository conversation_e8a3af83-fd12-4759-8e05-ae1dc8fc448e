"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var attestation_1 = require("./attestation.js");
var batch_1 = require("./batch.js");
var msgs_1 = require("./msgs.js");
var params_1 = require("./params.js");
var types_1 = require("./types.js");
exports.protobufPackage = "injective.peggy.v1";
function createBaseGenesisState() {
    return {
        params: undefined,
        lastObservedNonce: "0",
        valsets: [],
        valsetConfirms: [],
        batches: [],
        batchConfirms: [],
        attestations: [],
        orchestratorAddresses: [],
        erc20ToDenoms: [],
        unbatchedTransfers: [],
        lastObservedEthereumHeight: "0",
        lastOutgoingBatchId: "0",
        lastOutgoingPoolId: "0",
        lastObservedValset: undefined,
        ethereumBlacklist: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h, e_9, _j;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            params_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        if (message.lastObservedNonce !== "0") {
            writer.uint32(16).uint64(message.lastObservedNonce);
        }
        try {
            for (var _k = __values(message.valsets), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                types_1.Valset.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_a = _k.return)) _a.call(_k);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _m = __values(message.valsetConfirms), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                msgs_1.MsgValsetConfirm.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_b = _m.return)) _b.call(_m);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _p = __values(message.batches), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                batch_1.OutgoingTxBatch.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_c = _p.return)) _c.call(_p);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _r = __values(message.batchConfirms), _s = _r.next(); !_s.done; _s = _r.next()) {
                var v = _s.value;
                msgs_1.MsgConfirmBatch.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_s && !_s.done && (_d = _r.return)) _d.call(_r);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _t = __values(message.attestations), _u = _t.next(); !_u.done; _u = _t.next()) {
                var v = _u.value;
                attestation_1.Attestation.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_u && !_u.done && (_e = _t.return)) _e.call(_t);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _v = __values(message.orchestratorAddresses), _w = _v.next(); !_w.done; _w = _v.next()) {
                var v = _w.value;
                msgs_1.MsgSetOrchestratorAddresses.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_w && !_w.done && (_f = _v.return)) _f.call(_v);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _x = __values(message.erc20ToDenoms), _y = _x.next(); !_y.done; _y = _x.next()) {
                var v = _y.value;
                types_1.ERC20ToDenom.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_y && !_y.done && (_g = _x.return)) _g.call(_x);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _z = __values(message.unbatchedTransfers), _0 = _z.next(); !_0.done; _0 = _z.next()) {
                var v = _0.value;
                batch_1.OutgoingTransferTx.encode(v, writer.uint32(82).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_0 && !_0.done && (_h = _z.return)) _h.call(_z);
            }
            finally { if (e_8) throw e_8.error; }
        }
        if (message.lastObservedEthereumHeight !== "0") {
            writer.uint32(88).uint64(message.lastObservedEthereumHeight);
        }
        if (message.lastOutgoingBatchId !== "0") {
            writer.uint32(96).uint64(message.lastOutgoingBatchId);
        }
        if (message.lastOutgoingPoolId !== "0") {
            writer.uint32(104).uint64(message.lastOutgoingPoolId);
        }
        if (message.lastObservedValset !== undefined) {
            types_1.Valset.encode(message.lastObservedValset, writer.uint32(114).fork()).ldelim();
        }
        try {
            for (var _1 = __values(message.ethereumBlacklist), _2 = _1.next(); !_2.done; _2 = _1.next()) {
                var v = _2.value;
                writer.uint32(122).string(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_2 && !_2.done && (_j = _1.return)) _j.call(_1);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = params_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.lastObservedNonce = longToString(reader.uint64());
                    break;
                case 3:
                    message.valsets.push(types_1.Valset.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.valsetConfirms.push(msgs_1.MsgValsetConfirm.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.batches.push(batch_1.OutgoingTxBatch.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.batchConfirms.push(msgs_1.MsgConfirmBatch.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.attestations.push(attestation_1.Attestation.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.orchestratorAddresses.push(msgs_1.MsgSetOrchestratorAddresses.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.erc20ToDenoms.push(types_1.ERC20ToDenom.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.unbatchedTransfers.push(batch_1.OutgoingTransferTx.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.lastObservedEthereumHeight = longToString(reader.uint64());
                    break;
                case 12:
                    message.lastOutgoingBatchId = longToString(reader.uint64());
                    break;
                case 13:
                    message.lastOutgoingPoolId = longToString(reader.uint64());
                    break;
                case 14:
                    message.lastObservedValset = types_1.Valset.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.ethereumBlacklist.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? params_1.Params.fromJSON(object.params) : undefined,
            lastObservedNonce: isSet(object.lastObservedNonce) ? String(object.lastObservedNonce) : "0",
            valsets: Array.isArray(object === null || object === void 0 ? void 0 : object.valsets) ? object.valsets.map(function (e) { return types_1.Valset.fromJSON(e); }) : [],
            valsetConfirms: Array.isArray(object === null || object === void 0 ? void 0 : object.valsetConfirms)
                ? object.valsetConfirms.map(function (e) { return msgs_1.MsgValsetConfirm.fromJSON(e); })
                : [],
            batches: Array.isArray(object === null || object === void 0 ? void 0 : object.batches) ? object.batches.map(function (e) { return batch_1.OutgoingTxBatch.fromJSON(e); }) : [],
            batchConfirms: Array.isArray(object === null || object === void 0 ? void 0 : object.batchConfirms)
                ? object.batchConfirms.map(function (e) { return msgs_1.MsgConfirmBatch.fromJSON(e); })
                : [],
            attestations: Array.isArray(object === null || object === void 0 ? void 0 : object.attestations)
                ? object.attestations.map(function (e) { return attestation_1.Attestation.fromJSON(e); })
                : [],
            orchestratorAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.orchestratorAddresses)
                ? object.orchestratorAddresses.map(function (e) { return msgs_1.MsgSetOrchestratorAddresses.fromJSON(e); })
                : [],
            erc20ToDenoms: Array.isArray(object === null || object === void 0 ? void 0 : object.erc20ToDenoms)
                ? object.erc20ToDenoms.map(function (e) { return types_1.ERC20ToDenom.fromJSON(e); })
                : [],
            unbatchedTransfers: Array.isArray(object === null || object === void 0 ? void 0 : object.unbatchedTransfers)
                ? object.unbatchedTransfers.map(function (e) { return batch_1.OutgoingTransferTx.fromJSON(e); })
                : [],
            lastObservedEthereumHeight: isSet(object.lastObservedEthereumHeight)
                ? String(object.lastObservedEthereumHeight)
                : "0",
            lastOutgoingBatchId: isSet(object.lastOutgoingBatchId) ? String(object.lastOutgoingBatchId) : "0",
            lastOutgoingPoolId: isSet(object.lastOutgoingPoolId) ? String(object.lastOutgoingPoolId) : "0",
            lastObservedValset: isSet(object.lastObservedValset) ? types_1.Valset.fromJSON(object.lastObservedValset) : undefined,
            ethereumBlacklist: Array.isArray(object === null || object === void 0 ? void 0 : object.ethereumBlacklist)
                ? object.ethereumBlacklist.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? params_1.Params.toJSON(message.params) : undefined);
        message.lastObservedNonce !== undefined && (obj.lastObservedNonce = message.lastObservedNonce);
        if (message.valsets) {
            obj.valsets = message.valsets.map(function (e) { return e ? types_1.Valset.toJSON(e) : undefined; });
        }
        else {
            obj.valsets = [];
        }
        if (message.valsetConfirms) {
            obj.valsetConfirms = message.valsetConfirms.map(function (e) { return e ? msgs_1.MsgValsetConfirm.toJSON(e) : undefined; });
        }
        else {
            obj.valsetConfirms = [];
        }
        if (message.batches) {
            obj.batches = message.batches.map(function (e) { return e ? batch_1.OutgoingTxBatch.toJSON(e) : undefined; });
        }
        else {
            obj.batches = [];
        }
        if (message.batchConfirms) {
            obj.batchConfirms = message.batchConfirms.map(function (e) { return e ? msgs_1.MsgConfirmBatch.toJSON(e) : undefined; });
        }
        else {
            obj.batchConfirms = [];
        }
        if (message.attestations) {
            obj.attestations = message.attestations.map(function (e) { return e ? attestation_1.Attestation.toJSON(e) : undefined; });
        }
        else {
            obj.attestations = [];
        }
        if (message.orchestratorAddresses) {
            obj.orchestratorAddresses = message.orchestratorAddresses.map(function (e) {
                return e ? msgs_1.MsgSetOrchestratorAddresses.toJSON(e) : undefined;
            });
        }
        else {
            obj.orchestratorAddresses = [];
        }
        if (message.erc20ToDenoms) {
            obj.erc20ToDenoms = message.erc20ToDenoms.map(function (e) { return e ? types_1.ERC20ToDenom.toJSON(e) : undefined; });
        }
        else {
            obj.erc20ToDenoms = [];
        }
        if (message.unbatchedTransfers) {
            obj.unbatchedTransfers = message.unbatchedTransfers.map(function (e) { return e ? batch_1.OutgoingTransferTx.toJSON(e) : undefined; });
        }
        else {
            obj.unbatchedTransfers = [];
        }
        message.lastObservedEthereumHeight !== undefined &&
            (obj.lastObservedEthereumHeight = message.lastObservedEthereumHeight);
        message.lastOutgoingBatchId !== undefined && (obj.lastOutgoingBatchId = message.lastOutgoingBatchId);
        message.lastOutgoingPoolId !== undefined && (obj.lastOutgoingPoolId = message.lastOutgoingPoolId);
        message.lastObservedValset !== undefined &&
            (obj.lastObservedValset = message.lastObservedValset ? types_1.Valset.toJSON(message.lastObservedValset) : undefined);
        if (message.ethereumBlacklist) {
            obj.ethereumBlacklist = message.ethereumBlacklist.map(function (e) { return e; });
        }
        else {
            obj.ethereumBlacklist = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? params_1.Params.fromPartial(object.params)
            : undefined;
        message.lastObservedNonce = (_a = object.lastObservedNonce) !== null && _a !== void 0 ? _a : "0";
        message.valsets = ((_b = object.valsets) === null || _b === void 0 ? void 0 : _b.map(function (e) { return types_1.Valset.fromPartial(e); })) || [];
        message.valsetConfirms = ((_c = object.valsetConfirms) === null || _c === void 0 ? void 0 : _c.map(function (e) { return msgs_1.MsgValsetConfirm.fromPartial(e); })) || [];
        message.batches = ((_d = object.batches) === null || _d === void 0 ? void 0 : _d.map(function (e) { return batch_1.OutgoingTxBatch.fromPartial(e); })) || [];
        message.batchConfirms = ((_e = object.batchConfirms) === null || _e === void 0 ? void 0 : _e.map(function (e) { return msgs_1.MsgConfirmBatch.fromPartial(e); })) || [];
        message.attestations = ((_f = object.attestations) === null || _f === void 0 ? void 0 : _f.map(function (e) { return attestation_1.Attestation.fromPartial(e); })) || [];
        message.orchestratorAddresses =
            ((_g = object.orchestratorAddresses) === null || _g === void 0 ? void 0 : _g.map(function (e) { return msgs_1.MsgSetOrchestratorAddresses.fromPartial(e); })) || [];
        message.erc20ToDenoms = ((_h = object.erc20ToDenoms) === null || _h === void 0 ? void 0 : _h.map(function (e) { return types_1.ERC20ToDenom.fromPartial(e); })) || [];
        message.unbatchedTransfers = ((_j = object.unbatchedTransfers) === null || _j === void 0 ? void 0 : _j.map(function (e) { return batch_1.OutgoingTransferTx.fromPartial(e); })) || [];
        message.lastObservedEthereumHeight = (_k = object.lastObservedEthereumHeight) !== null && _k !== void 0 ? _k : "0";
        message.lastOutgoingBatchId = (_l = object.lastOutgoingBatchId) !== null && _l !== void 0 ? _l : "0";
        message.lastOutgoingPoolId = (_m = object.lastOutgoingPoolId) !== null && _m !== void 0 ? _m : "0";
        message.lastObservedValset = (object.lastObservedValset !== undefined && object.lastObservedValset !== null)
            ? types_1.Valset.fromPartial(object.lastObservedValset)
            : undefined;
        message.ethereumBlacklist = ((_o = object.ethereumBlacklist) === null || _o === void 0 ? void 0 : _o.map(function (e) { return e; })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
