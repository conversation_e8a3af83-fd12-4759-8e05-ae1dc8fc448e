{"version": 3, "file": "socketwrapper.js", "sourceRoot": "", "sources": ["../src/socketwrapper.ts"], "names": [], "mappings": ";;;;;;AAAA,kEAAsC;AAEtC,SAAS,mBAAmB;IAC1B,OAAO,CACL,OAAO,OAAO,KAAK,WAAW;QAC9B,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW;QACvC,OAAO,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,WAAW,CAC7C,CAAC;AACJ,CAAC;AAqBD;;;;;;GAMG;AACH,MAAa,aAAa;IAexB,YACE,GAAW,EACX,cAA0D,EAC1D,YAAsD,EACtD,WAAwB,EACxB,YAAuD,EACvD,OAAO,GAAG,KAAM;QAdV,WAAM,GAAG,KAAK,CAAC;QAgBrB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC/C,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,MAAM,MAAM,GAAG,IAAI,uBAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEvC,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aAC1B;QACH,CAAC,CAAC;QACF,MAAM,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,EAAE;YAClC,IAAI,CAAC,cAAc,CAAC;gBAClB,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,IAAI,EAAE,YAAY,CAAC,IAAc;aAClC,CAAC,CAAC;QACL,CAAC,CAAC;QACF,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YACpB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,oEAAoE;YACpE,IAAI,CAAC,iBAAkB,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,WAAW,EAAE,CAAC;aACpB;QACH,CAAC,CAAC;QACF,MAAM,CAAC,OAAO,GAAG,CAAC,UAAU,EAAE,EAAE;YAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aAC/B;QACH,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC/B,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;YAExB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC;YACjD,oEAAoE;YACpE,IAAI,CAAC,iBAAkB,CAAC,sCAAsC,OAAO,KAAK,CAAC,CAAC;QAC9E,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC5E;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAC9B,KAAK,uBAAS,CAAC,IAAI;gBACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,uBAAS,CAAC,MAAM;gBACnB,qBAAqB;gBACrB,MAAM;YACR,KAAK,uBAAS,CAAC,UAAU;gBACvB,4BAA4B;gBAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;gBAChC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;gBACxB,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,IAAI,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;iBACpD;gBACD,MAAM;YACR,KAAK,uBAAS,CAAC,OAAO;gBACpB,kCAAkC;gBAClC,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;SACpE;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,IAAY;QAC5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;aAC5E;YAED,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;aACvE;YAED,uEAAuE;YACvE,kGAAkG;YAClG,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,uBAAS,CAAC,IAAI,EAAE;gBAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YAED,IAAI,mBAAmB,EAAE,EAAE;gBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;aAClE;iBAAM;gBACL,2DAA2D;gBAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,OAAO,EAAE,CAAC;aACX;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACK,YAAY;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;SACH;QAED,6EAA6E;QAC7E,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;CACF;AAtKD,sCAsKC"}