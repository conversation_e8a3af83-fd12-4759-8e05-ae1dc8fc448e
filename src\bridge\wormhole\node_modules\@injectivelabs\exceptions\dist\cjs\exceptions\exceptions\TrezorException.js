"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrezorException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
class TrezorException extends base_js_1.ConcreteException {
    static errorClass = 'TrezorException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        this.setName(TrezorException.errorClass);
    }
}
exports.TrezorException = TrezorException;
