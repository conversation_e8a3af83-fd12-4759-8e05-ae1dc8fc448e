import { ChainErrorModule } from '@injectivelabs/exceptions';
export * from './gov.js';
export * from './auth.js';
export * from './bank.js';
export * from './mint.js';
export * from './wasm.js';
export * from './authZ.js';
export * from './peggy.js';
export * from './oracle.js';
export * from './auction.js';
export * from './staking.js';
export * from './exchange.js';
export * from './auth-rest.js';
export * from './bank-rest.js';
export * from './insurance.js';
export * from './distribution.js';
export * from './tokenfactory.js';
export * from './tendermint-rest.js';
export * from './permissions.js';
export interface RestApiResponse<T> {
    data: T;
}
export declare const ChainModule: {
    Auction: ChainErrorModule.Auction;
    Auth: ChainErrorModule.Auth;
    Authz: ChainErrorModule.Authz;
    Bank: ChainErrorModule.Bank;
    Distribution: ChainErrorModule.Distribution;
    Exchange: ChainErrorModule.Exchange;
    Gov: ChainErrorModule.Gov;
    Ibc: ChainErrorModule.Ibc;
    InsuranceFund: ChainErrorModule.InsuranceFund;
    Mint: ChainErrorModule.Mint;
    Oracle: ChainErrorModule.Oracle;
    Peggy: ChainErrorModule.Peggy;
    Staking: ChainErrorModule.Staking;
    Wasm: ChainErrorModule.Wasm;
    WasmX: ChainErrorModule.WasmX;
    Tendermint: ChainErrorModule.Tendermint;
    Permissions: ChainErrorModule.Permissions;
};
