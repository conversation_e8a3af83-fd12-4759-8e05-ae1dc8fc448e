"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var insurance_1 = require("./insurance.js");
exports.protobufPackage = "injective.insurance.v1beta1";
function createBaseGenesisState() {
    return {
        params: undefined,
        insuranceFunds: [],
        redemptionSchedule: [],
        nextShareDenomId: "0",
        nextRedemptionScheduleId: "0",
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            insurance_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _c = __values(message.insuranceFunds), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                insurance_1.InsuranceFund.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.redemptionSchedule), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                insurance_1.RedemptionSchedule.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.nextShareDenomId !== "0") {
            writer.uint32(32).uint64(message.nextShareDenomId);
        }
        if (message.nextRedemptionScheduleId !== "0") {
            writer.uint32(40).uint64(message.nextRedemptionScheduleId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = insurance_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.insuranceFunds.push(insurance_1.InsuranceFund.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.redemptionSchedule.push(insurance_1.RedemptionSchedule.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.nextShareDenomId = longToString(reader.uint64());
                    break;
                case 5:
                    message.nextRedemptionScheduleId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? insurance_1.Params.fromJSON(object.params) : undefined,
            insuranceFunds: Array.isArray(object === null || object === void 0 ? void 0 : object.insuranceFunds)
                ? object.insuranceFunds.map(function (e) { return insurance_1.InsuranceFund.fromJSON(e); })
                : [],
            redemptionSchedule: Array.isArray(object === null || object === void 0 ? void 0 : object.redemptionSchedule)
                ? object.redemptionSchedule.map(function (e) { return insurance_1.RedemptionSchedule.fromJSON(e); })
                : [],
            nextShareDenomId: isSet(object.nextShareDenomId) ? String(object.nextShareDenomId) : "0",
            nextRedemptionScheduleId: isSet(object.nextRedemptionScheduleId) ? String(object.nextRedemptionScheduleId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? insurance_1.Params.toJSON(message.params) : undefined);
        if (message.insuranceFunds) {
            obj.insuranceFunds = message.insuranceFunds.map(function (e) { return e ? insurance_1.InsuranceFund.toJSON(e) : undefined; });
        }
        else {
            obj.insuranceFunds = [];
        }
        if (message.redemptionSchedule) {
            obj.redemptionSchedule = message.redemptionSchedule.map(function (e) { return e ? insurance_1.RedemptionSchedule.toJSON(e) : undefined; });
        }
        else {
            obj.redemptionSchedule = [];
        }
        message.nextShareDenomId !== undefined && (obj.nextShareDenomId = message.nextShareDenomId);
        message.nextRedemptionScheduleId !== undefined && (obj.nextRedemptionScheduleId = message.nextRedemptionScheduleId);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? insurance_1.Params.fromPartial(object.params)
            : undefined;
        message.insuranceFunds = ((_a = object.insuranceFunds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return insurance_1.InsuranceFund.fromPartial(e); })) || [];
        message.redemptionSchedule = ((_b = object.redemptionSchedule) === null || _b === void 0 ? void 0 : _b.map(function (e) { return insurance_1.RedemptionSchedule.fromPartial(e); })) || [];
        message.nextShareDenomId = (_c = object.nextShareDenomId) !== null && _c !== void 0 ? _c : "0";
        message.nextRedemptionScheduleId = (_d = object.nextRedemptionScheduleId) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
