import { Contract, WasmC<PERSON>, Transaction, BankTransfer, ExplorerTransaction, ContractTransaction, ExplorerBlockWithTxs, ExplorerValidatorUptime, ExplorerCW20BalanceWithToken } from '../types/explorer.js';
import { Block, ExplorerValidator, ContractTransactionWithMessages } from '../types/explorer.js';
import { ContractExplorerApiResponse, WasmCodeExplorerApiResponse, BlockFromExplorerApiResponse, CW20BalanceExplorerApiResponse, TransactionFromExplorerApiResponse, BankTransferFromExplorerApiResponse, ContractTransactionExplorerApiResponse, ValidatorUptimeFromExplorerApiResponse } from '../types/explorer-rest.js';
/**
 * @category Indexer Rest Transformer
 */
export declare class IndexerRestExplorerTransformer {
    static blockToBlock(block: BlockFromExplorerApiResponse): Block;
    static blocksToBlocks(blocks: BlockFromExplorerApiResponse[]): Block[];
    static transactionToTransaction(transaction: TransactionFromExplorerApiResponse): ExplorerTransaction;
    static transactionsToTransactions(transactions: TransactionFromExplorerApiResponse[]): ExplorerTransaction[];
    static blockWithTxToBlockWithTx(block: BlockFromExplorerApiResponse): ExplorerBlockWithTxs;
    static blocksWithTxsToBlocksWithTxs(blocks: BlockFromExplorerApiResponse[]): ExplorerBlockWithTxs[];
    static baseTransactionToTransaction(transaction: Transaction): ExplorerTransaction;
    static validatorExplorerToValidator(validators: any[]): Partial<ExplorerValidator>[];
    static validatorUptimeToExplorerValidatorUptime(validatorUptimeList: ValidatorUptimeFromExplorerApiResponse[]): ExplorerValidatorUptime[];
    static contractToExplorerContract(contract: ContractExplorerApiResponse): Contract;
    static contractTransactionToExplorerContractTransaction(transaction: ContractTransactionExplorerApiResponse): ContractTransaction;
    static contractTransactionToExplorerContractTransactionWithMessages(transaction: ContractTransactionExplorerApiResponse): ContractTransactionWithMessages;
    static wasmCodeToExplorerWasmCode(wasmCode: WasmCodeExplorerApiResponse): WasmCode;
    static CW20BalanceToExplorerCW20Balance(balance: CW20BalanceExplorerApiResponse): ExplorerCW20BalanceWithToken;
    static bankTransferToBankTransfer(transfer: BankTransferFromExplorerApiResponse): BankTransfer;
    static bankTransfersToBankTransfers(transfers: BankTransferFromExplorerApiResponse[]): BankTransfer[];
}
