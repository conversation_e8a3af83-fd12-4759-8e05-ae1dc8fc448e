"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.ABCIFinalizeBlockDesc = exports.ABCIVerifyVoteExtensionDesc = exports.ABCIExtendVoteDesc = exports.ABCIProcessProposalDesc = exports.ABCIPrepareProposalDesc = exports.ABCIApplySnapshotChunkDesc = exports.ABCILoadSnapshotChunkDesc = exports.ABCIOfferSnapshotDesc = exports.ABCIListSnapshotsDesc = exports.ABCIInitChainDesc = exports.ABCICommitDesc = exports.ABCIQueryDesc = exports.ABCICheckTxDesc = exports.ABCIInfoDesc = exports.ABCIFlushDesc = exports.ABCIEchoDesc = exports.ABCIDesc = exports.ABCIClientImpl = exports.TxResult = exports.ExecTxResult = exports.ExtendedCommitInfo = exports.CommitInfo = exports.ExtendedVoteInfo = exports.VoteInfo = exports.ResponseFinalizeBlock = exports.ResponseVerifyVoteExtension = exports.ResponseExtendVote = exports.ResponseCommit = exports.ResponseCheckTx = exports.ResponseInitChain = exports.Response = exports.RequestFinalizeBlock = exports.RequestVerifyVoteExtension = exports.RequestExtendVote = exports.RequestProcessProposal = exports.RequestPrepareProposal = exports.RequestInitChain = exports.Request = exports.ResponseVerifyVoteExtension_VerifyStatus = exports.protobufPackage = void 0;
exports.responseVerifyVoteExtension_VerifyStatusFromJSON = responseVerifyVoteExtension_VerifyStatusFromJSON;
exports.responseVerifyVoteExtension_VerifyStatusToJSON = responseVerifyVoteExtension_VerifyStatusToJSON;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var timestamp_1 = require("../../../google/protobuf/timestamp.js");
var params_1 = require("../../types/v1/params.js");
var validator_1 = require("../../types/v1beta1/validator.js");
var types_1 = require("../v1beta1/types.js");
var types_2 = require("../v1beta2/types.js");
exports.protobufPackage = "cometbft.abci.v1beta3";
/** Verification status. */
var ResponseVerifyVoteExtension_VerifyStatus;
(function (ResponseVerifyVoteExtension_VerifyStatus) {
    /** UNKNOWN - Unknown */
    ResponseVerifyVoteExtension_VerifyStatus[ResponseVerifyVoteExtension_VerifyStatus["UNKNOWN"] = 0] = "UNKNOWN";
    /** ACCEPT - Accepted */
    ResponseVerifyVoteExtension_VerifyStatus[ResponseVerifyVoteExtension_VerifyStatus["ACCEPT"] = 1] = "ACCEPT";
    /**
     * REJECT - Rejecting the vote extension will reject the entire precommit by the sender.
     * Incorrectly implementing this thus has liveness implications as it may affect
     * CometBFT's ability to receive 2/3+ valid votes to finalize the block.
     * Honest nodes should never be rejected.
     */
    ResponseVerifyVoteExtension_VerifyStatus[ResponseVerifyVoteExtension_VerifyStatus["REJECT"] = 2] = "REJECT";
    ResponseVerifyVoteExtension_VerifyStatus[ResponseVerifyVoteExtension_VerifyStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ResponseVerifyVoteExtension_VerifyStatus || (exports.ResponseVerifyVoteExtension_VerifyStatus = ResponseVerifyVoteExtension_VerifyStatus = {}));
function responseVerifyVoteExtension_VerifyStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "UNKNOWN":
            return ResponseVerifyVoteExtension_VerifyStatus.UNKNOWN;
        case 1:
        case "ACCEPT":
            return ResponseVerifyVoteExtension_VerifyStatus.ACCEPT;
        case 2:
        case "REJECT":
            return ResponseVerifyVoteExtension_VerifyStatus.REJECT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ResponseVerifyVoteExtension_VerifyStatus.UNRECOGNIZED;
    }
}
function responseVerifyVoteExtension_VerifyStatusToJSON(object) {
    switch (object) {
        case ResponseVerifyVoteExtension_VerifyStatus.UNKNOWN:
            return "UNKNOWN";
        case ResponseVerifyVoteExtension_VerifyStatus.ACCEPT:
            return "ACCEPT";
        case ResponseVerifyVoteExtension_VerifyStatus.REJECT:
            return "REJECT";
        case ResponseVerifyVoteExtension_VerifyStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseRequest() {
    return {
        echo: undefined,
        flush: undefined,
        info: undefined,
        initChain: undefined,
        query: undefined,
        checkTx: undefined,
        commit: undefined,
        listSnapshots: undefined,
        offerSnapshot: undefined,
        loadSnapshotChunk: undefined,
        applySnapshotChunk: undefined,
        prepareProposal: undefined,
        processProposal: undefined,
        extendVote: undefined,
        verifyVoteExtension: undefined,
        finalizeBlock: undefined,
    };
}
exports.Request = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.echo !== undefined) {
            types_1.RequestEcho.encode(message.echo, writer.uint32(10).fork()).ldelim();
        }
        if (message.flush !== undefined) {
            types_1.RequestFlush.encode(message.flush, writer.uint32(18).fork()).ldelim();
        }
        if (message.info !== undefined) {
            types_2.RequestInfo.encode(message.info, writer.uint32(26).fork()).ldelim();
        }
        if (message.initChain !== undefined) {
            exports.RequestInitChain.encode(message.initChain, writer.uint32(42).fork()).ldelim();
        }
        if (message.query !== undefined) {
            types_1.RequestQuery.encode(message.query, writer.uint32(50).fork()).ldelim();
        }
        if (message.checkTx !== undefined) {
            types_1.RequestCheckTx.encode(message.checkTx, writer.uint32(66).fork()).ldelim();
        }
        if (message.commit !== undefined) {
            types_1.RequestCommit.encode(message.commit, writer.uint32(90).fork()).ldelim();
        }
        if (message.listSnapshots !== undefined) {
            types_1.RequestListSnapshots.encode(message.listSnapshots, writer.uint32(98).fork()).ldelim();
        }
        if (message.offerSnapshot !== undefined) {
            types_1.RequestOfferSnapshot.encode(message.offerSnapshot, writer.uint32(106).fork()).ldelim();
        }
        if (message.loadSnapshotChunk !== undefined) {
            types_1.RequestLoadSnapshotChunk.encode(message.loadSnapshotChunk, writer.uint32(114).fork()).ldelim();
        }
        if (message.applySnapshotChunk !== undefined) {
            types_1.RequestApplySnapshotChunk.encode(message.applySnapshotChunk, writer.uint32(122).fork()).ldelim();
        }
        if (message.prepareProposal !== undefined) {
            exports.RequestPrepareProposal.encode(message.prepareProposal, writer.uint32(130).fork()).ldelim();
        }
        if (message.processProposal !== undefined) {
            exports.RequestProcessProposal.encode(message.processProposal, writer.uint32(138).fork()).ldelim();
        }
        if (message.extendVote !== undefined) {
            exports.RequestExtendVote.encode(message.extendVote, writer.uint32(146).fork()).ldelim();
        }
        if (message.verifyVoteExtension !== undefined) {
            exports.RequestVerifyVoteExtension.encode(message.verifyVoteExtension, writer.uint32(154).fork()).ldelim();
        }
        if (message.finalizeBlock !== undefined) {
            exports.RequestFinalizeBlock.encode(message.finalizeBlock, writer.uint32(162).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.echo = types_1.RequestEcho.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.flush = types_1.RequestFlush.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.info = types_2.RequestInfo.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.initChain = exports.RequestInitChain.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.query = types_1.RequestQuery.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.checkTx = types_1.RequestCheckTx.decode(reader, reader.uint32());
                    break;
                case 11:
                    message.commit = types_1.RequestCommit.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.listSnapshots = types_1.RequestListSnapshots.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.offerSnapshot = types_1.RequestOfferSnapshot.decode(reader, reader.uint32());
                    break;
                case 14:
                    message.loadSnapshotChunk = types_1.RequestLoadSnapshotChunk.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.applySnapshotChunk = types_1.RequestApplySnapshotChunk.decode(reader, reader.uint32());
                    break;
                case 16:
                    message.prepareProposal = exports.RequestPrepareProposal.decode(reader, reader.uint32());
                    break;
                case 17:
                    message.processProposal = exports.RequestProcessProposal.decode(reader, reader.uint32());
                    break;
                case 18:
                    message.extendVote = exports.RequestExtendVote.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.verifyVoteExtension = exports.RequestVerifyVoteExtension.decode(reader, reader.uint32());
                    break;
                case 20:
                    message.finalizeBlock = exports.RequestFinalizeBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            echo: isSet(object.echo) ? types_1.RequestEcho.fromJSON(object.echo) : undefined,
            flush: isSet(object.flush) ? types_1.RequestFlush.fromJSON(object.flush) : undefined,
            info: isSet(object.info) ? types_2.RequestInfo.fromJSON(object.info) : undefined,
            initChain: isSet(object.initChain) ? exports.RequestInitChain.fromJSON(object.initChain) : undefined,
            query: isSet(object.query) ? types_1.RequestQuery.fromJSON(object.query) : undefined,
            checkTx: isSet(object.checkTx) ? types_1.RequestCheckTx.fromJSON(object.checkTx) : undefined,
            commit: isSet(object.commit) ? types_1.RequestCommit.fromJSON(object.commit) : undefined,
            listSnapshots: isSet(object.listSnapshots) ? types_1.RequestListSnapshots.fromJSON(object.listSnapshots) : undefined,
            offerSnapshot: isSet(object.offerSnapshot) ? types_1.RequestOfferSnapshot.fromJSON(object.offerSnapshot) : undefined,
            loadSnapshotChunk: isSet(object.loadSnapshotChunk)
                ? types_1.RequestLoadSnapshotChunk.fromJSON(object.loadSnapshotChunk)
                : undefined,
            applySnapshotChunk: isSet(object.applySnapshotChunk)
                ? types_1.RequestApplySnapshotChunk.fromJSON(object.applySnapshotChunk)
                : undefined,
            prepareProposal: isSet(object.prepareProposal)
                ? exports.RequestPrepareProposal.fromJSON(object.prepareProposal)
                : undefined,
            processProposal: isSet(object.processProposal)
                ? exports.RequestProcessProposal.fromJSON(object.processProposal)
                : undefined,
            extendVote: isSet(object.extendVote) ? exports.RequestExtendVote.fromJSON(object.extendVote) : undefined,
            verifyVoteExtension: isSet(object.verifyVoteExtension)
                ? exports.RequestVerifyVoteExtension.fromJSON(object.verifyVoteExtension)
                : undefined,
            finalizeBlock: isSet(object.finalizeBlock) ? exports.RequestFinalizeBlock.fromJSON(object.finalizeBlock) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.echo !== undefined && (obj.echo = message.echo ? types_1.RequestEcho.toJSON(message.echo) : undefined);
        message.flush !== undefined && (obj.flush = message.flush ? types_1.RequestFlush.toJSON(message.flush) : undefined);
        message.info !== undefined && (obj.info = message.info ? types_2.RequestInfo.toJSON(message.info) : undefined);
        message.initChain !== undefined &&
            (obj.initChain = message.initChain ? exports.RequestInitChain.toJSON(message.initChain) : undefined);
        message.query !== undefined && (obj.query = message.query ? types_1.RequestQuery.toJSON(message.query) : undefined);
        message.checkTx !== undefined &&
            (obj.checkTx = message.checkTx ? types_1.RequestCheckTx.toJSON(message.checkTx) : undefined);
        message.commit !== undefined && (obj.commit = message.commit ? types_1.RequestCommit.toJSON(message.commit) : undefined);
        message.listSnapshots !== undefined &&
            (obj.listSnapshots = message.listSnapshots ? types_1.RequestListSnapshots.toJSON(message.listSnapshots) : undefined);
        message.offerSnapshot !== undefined &&
            (obj.offerSnapshot = message.offerSnapshot ? types_1.RequestOfferSnapshot.toJSON(message.offerSnapshot) : undefined);
        message.loadSnapshotChunk !== undefined && (obj.loadSnapshotChunk = message.loadSnapshotChunk
            ? types_1.RequestLoadSnapshotChunk.toJSON(message.loadSnapshotChunk)
            : undefined);
        message.applySnapshotChunk !== undefined && (obj.applySnapshotChunk = message.applySnapshotChunk
            ? types_1.RequestApplySnapshotChunk.toJSON(message.applySnapshotChunk)
            : undefined);
        message.prepareProposal !== undefined && (obj.prepareProposal = message.prepareProposal
            ? exports.RequestPrepareProposal.toJSON(message.prepareProposal)
            : undefined);
        message.processProposal !== undefined && (obj.processProposal = message.processProposal
            ? exports.RequestProcessProposal.toJSON(message.processProposal)
            : undefined);
        message.extendVote !== undefined &&
            (obj.extendVote = message.extendVote ? exports.RequestExtendVote.toJSON(message.extendVote) : undefined);
        message.verifyVoteExtension !== undefined && (obj.verifyVoteExtension = message.verifyVoteExtension
            ? exports.RequestVerifyVoteExtension.toJSON(message.verifyVoteExtension)
            : undefined);
        message.finalizeBlock !== undefined &&
            (obj.finalizeBlock = message.finalizeBlock ? exports.RequestFinalizeBlock.toJSON(message.finalizeBlock) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Request.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseRequest();
        message.echo = (object.echo !== undefined && object.echo !== null)
            ? types_1.RequestEcho.fromPartial(object.echo)
            : undefined;
        message.flush = (object.flush !== undefined && object.flush !== null)
            ? types_1.RequestFlush.fromPartial(object.flush)
            : undefined;
        message.info = (object.info !== undefined && object.info !== null)
            ? types_2.RequestInfo.fromPartial(object.info)
            : undefined;
        message.initChain = (object.initChain !== undefined && object.initChain !== null)
            ? exports.RequestInitChain.fromPartial(object.initChain)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? types_1.RequestQuery.fromPartial(object.query)
            : undefined;
        message.checkTx = (object.checkTx !== undefined && object.checkTx !== null)
            ? types_1.RequestCheckTx.fromPartial(object.checkTx)
            : undefined;
        message.commit = (object.commit !== undefined && object.commit !== null)
            ? types_1.RequestCommit.fromPartial(object.commit)
            : undefined;
        message.listSnapshots = (object.listSnapshots !== undefined && object.listSnapshots !== null)
            ? types_1.RequestListSnapshots.fromPartial(object.listSnapshots)
            : undefined;
        message.offerSnapshot = (object.offerSnapshot !== undefined && object.offerSnapshot !== null)
            ? types_1.RequestOfferSnapshot.fromPartial(object.offerSnapshot)
            : undefined;
        message.loadSnapshotChunk = (object.loadSnapshotChunk !== undefined && object.loadSnapshotChunk !== null)
            ? types_1.RequestLoadSnapshotChunk.fromPartial(object.loadSnapshotChunk)
            : undefined;
        message.applySnapshotChunk = (object.applySnapshotChunk !== undefined && object.applySnapshotChunk !== null)
            ? types_1.RequestApplySnapshotChunk.fromPartial(object.applySnapshotChunk)
            : undefined;
        message.prepareProposal = (object.prepareProposal !== undefined && object.prepareProposal !== null)
            ? exports.RequestPrepareProposal.fromPartial(object.prepareProposal)
            : undefined;
        message.processProposal = (object.processProposal !== undefined && object.processProposal !== null)
            ? exports.RequestProcessProposal.fromPartial(object.processProposal)
            : undefined;
        message.extendVote = (object.extendVote !== undefined && object.extendVote !== null)
            ? exports.RequestExtendVote.fromPartial(object.extendVote)
            : undefined;
        message.verifyVoteExtension = (object.verifyVoteExtension !== undefined && object.verifyVoteExtension !== null)
            ? exports.RequestVerifyVoteExtension.fromPartial(object.verifyVoteExtension)
            : undefined;
        message.finalizeBlock = (object.finalizeBlock !== undefined && object.finalizeBlock !== null)
            ? exports.RequestFinalizeBlock.fromPartial(object.finalizeBlock)
            : undefined;
        return message;
    },
};
function createBaseRequestInitChain() {
    return {
        time: undefined,
        chainId: "",
        consensusParams: undefined,
        validators: [],
        appStateBytes: new Uint8Array(),
        initialHeight: "0",
    };
}
exports.RequestInitChain = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(10).fork()).ldelim();
        }
        if (message.chainId !== "") {
            writer.uint32(18).string(message.chainId);
        }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.validators), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.ValidatorUpdate.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.appStateBytes.length !== 0) {
            writer.uint32(42).bytes(message.appStateBytes);
        }
        if (message.initialHeight !== "0") {
            writer.uint32(48).int64(message.initialHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestInitChain();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.chainId = reader.string();
                    break;
                case 3:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.validators.push(types_1.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.appStateBytes = reader.bytes();
                    break;
                case 6:
                    message.initialHeight = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            chainId: isSet(object.chainId) ? String(object.chainId) : "",
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            validators: Array.isArray(object === null || object === void 0 ? void 0 : object.validators)
                ? object.validators.map(function (e) { return types_1.ValidatorUpdate.fromJSON(e); })
                : [],
            appStateBytes: isSet(object.appStateBytes) ? bytesFromBase64(object.appStateBytes) : new Uint8Array(),
            initialHeight: isSet(object.initialHeight) ? String(object.initialHeight) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.chainId !== undefined && (obj.chainId = message.chainId);
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        if (message.validators) {
            obj.validators = message.validators.map(function (e) { return e ? types_1.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validators = [];
        }
        message.appStateBytes !== undefined &&
            (obj.appStateBytes = base64FromBytes(message.appStateBytes !== undefined ? message.appStateBytes : new Uint8Array()));
        message.initialHeight !== undefined && (obj.initialHeight = message.initialHeight);
        return obj;
    },
    create: function (base) {
        return exports.RequestInitChain.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseRequestInitChain();
        message.time = (_a = object.time) !== null && _a !== void 0 ? _a : undefined;
        message.chainId = (_b = object.chainId) !== null && _b !== void 0 ? _b : "";
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.validators = ((_c = object.validators) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_1.ValidatorUpdate.fromPartial(e); })) || [];
        message.appStateBytes = (_d = object.appStateBytes) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.initialHeight = (_e = object.initialHeight) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseRequestPrepareProposal() {
    return {
        maxTxBytes: "0",
        txs: [],
        localLastCommit: undefined,
        misbehavior: [],
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.RequestPrepareProposal = {
    encode: function (message, writer) {
        var e_2, _a, e_3, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.maxTxBytes !== "0") {
            writer.uint32(8).int64(message.maxTxBytes);
        }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).bytes(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.localLastCommit !== undefined) {
            exports.ExtendedCommitInfo.encode(message.localLastCommit, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_2.Misbehavior.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestPrepareProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.maxTxBytes = longToString(reader.int64());
                    break;
                case 2:
                    message.txs.push(reader.bytes());
                    break;
                case 3:
                    message.localLastCommit = exports.ExtendedCommitInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.misbehavior.push(types_2.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            maxTxBytes: isSet(object.maxTxBytes) ? String(object.maxTxBytes) : "0",
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            localLastCommit: isSet(object.localLastCommit) ? exports.ExtendedCommitInfo.fromJSON(object.localLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return types_2.Misbehavior.fromJSON(e); })
                : [],
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.maxTxBytes !== undefined && (obj.maxTxBytes = message.maxTxBytes);
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.localLastCommit !== undefined &&
            (obj.localLastCommit = message.localLastCommit ? exports.ExtendedCommitInfo.toJSON(message.localLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? types_2.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.RequestPrepareProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseRequestPrepareProposal();
        message.maxTxBytes = (_a = object.maxTxBytes) !== null && _a !== void 0 ? _a : "0";
        message.txs = ((_b = object.txs) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.localLastCommit = (object.localLastCommit !== undefined && object.localLastCommit !== null)
            ? exports.ExtendedCommitInfo.fromPartial(object.localLastCommit)
            : undefined;
        message.misbehavior = ((_c = object.misbehavior) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_2.Misbehavior.fromPartial(e); })) || [];
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseRequestProcessProposal() {
    return {
        txs: [],
        proposedLastCommit: undefined,
        misbehavior: [],
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.RequestProcessProposal = {
    encode: function (message, writer) {
        var e_4, _a, e_5, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (message.proposedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.proposedLastCommit, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_2.Misbehavior.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestProcessProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                case 2:
                    message.proposedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.misbehavior.push(types_2.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.hash = reader.bytes();
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            proposedLastCommit: isSet(object.proposedLastCommit) ? exports.CommitInfo.fromJSON(object.proposedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return types_2.Misbehavior.fromJSON(e); })
                : [],
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.proposedLastCommit !== undefined &&
            (obj.proposedLastCommit = message.proposedLastCommit ? exports.CommitInfo.toJSON(message.proposedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? types_2.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.RequestProcessProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseRequestProcessProposal();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.proposedLastCommit = (object.proposedLastCommit !== undefined && object.proposedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.proposedLastCommit)
            : undefined;
        message.misbehavior = ((_b = object.misbehavior) === null || _b === void 0 ? void 0 : _b.map(function (e) { return types_2.Misbehavior.fromPartial(e); })) || [];
        message.hash = (_c = object.hash) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseRequestExtendVote() {
    return {
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        txs: [],
        proposedLastCommit: undefined,
        misbehavior: [],
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.RequestExtendVote = {
    encode: function (message, writer) {
        var e_6, _a, e_7, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.hash.length !== 0) {
            writer.uint32(10).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(16).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(34).bytes(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_6) throw e_6.error; }
        }
        if (message.proposedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.proposedLastCommit, writer.uint32(42).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_2.Misbehavior.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_7) throw e_7.error; }
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestExtendVote();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hash = reader.bytes();
                    break;
                case 2:
                    message.height = longToString(reader.int64());
                    break;
                case 3:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.txs.push(reader.bytes());
                    break;
                case 5:
                    message.proposedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.misbehavior.push(types_2.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            proposedLastCommit: isSet(object.proposedLastCommit) ? exports.CommitInfo.fromJSON(object.proposedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return types_2.Misbehavior.fromJSON(e); })
                : [],
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.proposedLastCommit !== undefined &&
            (obj.proposedLastCommit = message.proposedLastCommit ? exports.CommitInfo.toJSON(message.proposedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? types_2.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.RequestExtendVote.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseRequestExtendVote();
        message.hash = (_a = object.hash) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.height = (_b = object.height) !== null && _b !== void 0 ? _b : "0";
        message.time = (_c = object.time) !== null && _c !== void 0 ? _c : undefined;
        message.txs = ((_d = object.txs) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.proposedLastCommit = (object.proposedLastCommit !== undefined && object.proposedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.proposedLastCommit)
            : undefined;
        message.misbehavior = ((_e = object.misbehavior) === null || _e === void 0 ? void 0 : _e.map(function (e) { return types_2.Misbehavior.fromPartial(e); })) || [];
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseRequestVerifyVoteExtension() {
    return { hash: new Uint8Array(), validatorAddress: new Uint8Array(), height: "0", voteExtension: new Uint8Array() };
}
exports.RequestVerifyVoteExtension = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.hash.length !== 0) {
            writer.uint32(10).bytes(message.hash);
        }
        if (message.validatorAddress.length !== 0) {
            writer.uint32(18).bytes(message.validatorAddress);
        }
        if (message.height !== "0") {
            writer.uint32(24).int64(message.height);
        }
        if (message.voteExtension.length !== 0) {
            writer.uint32(34).bytes(message.voteExtension);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestVerifyVoteExtension();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hash = reader.bytes();
                    break;
                case 2:
                    message.validatorAddress = reader.bytes();
                    break;
                case 3:
                    message.height = longToString(reader.int64());
                    break;
                case 4:
                    message.voteExtension = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            validatorAddress: isSet(object.validatorAddress) ? bytesFromBase64(object.validatorAddress) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.validatorAddress !== undefined &&
            (obj.validatorAddress = base64FromBytes(message.validatorAddress !== undefined ? message.validatorAddress : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.RequestVerifyVoteExtension.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseRequestVerifyVoteExtension();
        message.hash = (_a = object.hash) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.validatorAddress = (_b = object.validatorAddress) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.height = (_c = object.height) !== null && _c !== void 0 ? _c : "0";
        message.voteExtension = (_d = object.voteExtension) !== null && _d !== void 0 ? _d : new Uint8Array();
        return message;
    },
};
function createBaseRequestFinalizeBlock() {
    return {
        txs: [],
        decidedLastCommit: undefined,
        misbehavior: [],
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.RequestFinalizeBlock = {
    encode: function (message, writer) {
        var e_8, _a, e_9, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_8) throw e_8.error; }
        }
        if (message.decidedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.decidedLastCommit, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_2.Misbehavior.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_9) throw e_9.error; }
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequestFinalizeBlock();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                case 2:
                    message.decidedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.misbehavior.push(types_2.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.hash = reader.bytes();
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            decidedLastCommit: isSet(object.decidedLastCommit) ? exports.CommitInfo.fromJSON(object.decidedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return types_2.Misbehavior.fromJSON(e); })
                : [],
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.decidedLastCommit !== undefined &&
            (obj.decidedLastCommit = message.decidedLastCommit ? exports.CommitInfo.toJSON(message.decidedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? types_2.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.RequestFinalizeBlock.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseRequestFinalizeBlock();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.decidedLastCommit = (object.decidedLastCommit !== undefined && object.decidedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.decidedLastCommit)
            : undefined;
        message.misbehavior = ((_b = object.misbehavior) === null || _b === void 0 ? void 0 : _b.map(function (e) { return types_2.Misbehavior.fromPartial(e); })) || [];
        message.hash = (_c = object.hash) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseResponse() {
    return {
        exception: undefined,
        echo: undefined,
        flush: undefined,
        info: undefined,
        initChain: undefined,
        query: undefined,
        checkTx: undefined,
        commit: undefined,
        listSnapshots: undefined,
        offerSnapshot: undefined,
        loadSnapshotChunk: undefined,
        applySnapshotChunk: undefined,
        prepareProposal: undefined,
        processProposal: undefined,
        extendVote: undefined,
        verifyVoteExtension: undefined,
        finalizeBlock: undefined,
    };
}
exports.Response = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.exception !== undefined) {
            types_1.ResponseException.encode(message.exception, writer.uint32(10).fork()).ldelim();
        }
        if (message.echo !== undefined) {
            types_1.ResponseEcho.encode(message.echo, writer.uint32(18).fork()).ldelim();
        }
        if (message.flush !== undefined) {
            types_1.ResponseFlush.encode(message.flush, writer.uint32(26).fork()).ldelim();
        }
        if (message.info !== undefined) {
            types_1.ResponseInfo.encode(message.info, writer.uint32(34).fork()).ldelim();
        }
        if (message.initChain !== undefined) {
            exports.ResponseInitChain.encode(message.initChain, writer.uint32(50).fork()).ldelim();
        }
        if (message.query !== undefined) {
            types_1.ResponseQuery.encode(message.query, writer.uint32(58).fork()).ldelim();
        }
        if (message.checkTx !== undefined) {
            exports.ResponseCheckTx.encode(message.checkTx, writer.uint32(74).fork()).ldelim();
        }
        if (message.commit !== undefined) {
            exports.ResponseCommit.encode(message.commit, writer.uint32(98).fork()).ldelim();
        }
        if (message.listSnapshots !== undefined) {
            types_1.ResponseListSnapshots.encode(message.listSnapshots, writer.uint32(106).fork()).ldelim();
        }
        if (message.offerSnapshot !== undefined) {
            types_1.ResponseOfferSnapshot.encode(message.offerSnapshot, writer.uint32(114).fork()).ldelim();
        }
        if (message.loadSnapshotChunk !== undefined) {
            types_1.ResponseLoadSnapshotChunk.encode(message.loadSnapshotChunk, writer.uint32(122).fork()).ldelim();
        }
        if (message.applySnapshotChunk !== undefined) {
            types_1.ResponseApplySnapshotChunk.encode(message.applySnapshotChunk, writer.uint32(130).fork()).ldelim();
        }
        if (message.prepareProposal !== undefined) {
            types_2.ResponsePrepareProposal.encode(message.prepareProposal, writer.uint32(138).fork()).ldelim();
        }
        if (message.processProposal !== undefined) {
            types_2.ResponseProcessProposal.encode(message.processProposal, writer.uint32(146).fork()).ldelim();
        }
        if (message.extendVote !== undefined) {
            exports.ResponseExtendVote.encode(message.extendVote, writer.uint32(154).fork()).ldelim();
        }
        if (message.verifyVoteExtension !== undefined) {
            exports.ResponseVerifyVoteExtension.encode(message.verifyVoteExtension, writer.uint32(162).fork()).ldelim();
        }
        if (message.finalizeBlock !== undefined) {
            exports.ResponseFinalizeBlock.encode(message.finalizeBlock, writer.uint32(170).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.exception = types_1.ResponseException.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.echo = types_1.ResponseEcho.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.flush = types_1.ResponseFlush.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.info = types_1.ResponseInfo.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.initChain = exports.ResponseInitChain.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.query = types_1.ResponseQuery.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.checkTx = exports.ResponseCheckTx.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.commit = exports.ResponseCommit.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.listSnapshots = types_1.ResponseListSnapshots.decode(reader, reader.uint32());
                    break;
                case 14:
                    message.offerSnapshot = types_1.ResponseOfferSnapshot.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.loadSnapshotChunk = types_1.ResponseLoadSnapshotChunk.decode(reader, reader.uint32());
                    break;
                case 16:
                    message.applySnapshotChunk = types_1.ResponseApplySnapshotChunk.decode(reader, reader.uint32());
                    break;
                case 17:
                    message.prepareProposal = types_2.ResponsePrepareProposal.decode(reader, reader.uint32());
                    break;
                case 18:
                    message.processProposal = types_2.ResponseProcessProposal.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.extendVote = exports.ResponseExtendVote.decode(reader, reader.uint32());
                    break;
                case 20:
                    message.verifyVoteExtension = exports.ResponseVerifyVoteExtension.decode(reader, reader.uint32());
                    break;
                case 21:
                    message.finalizeBlock = exports.ResponseFinalizeBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            exception: isSet(object.exception) ? types_1.ResponseException.fromJSON(object.exception) : undefined,
            echo: isSet(object.echo) ? types_1.ResponseEcho.fromJSON(object.echo) : undefined,
            flush: isSet(object.flush) ? types_1.ResponseFlush.fromJSON(object.flush) : undefined,
            info: isSet(object.info) ? types_1.ResponseInfo.fromJSON(object.info) : undefined,
            initChain: isSet(object.initChain) ? exports.ResponseInitChain.fromJSON(object.initChain) : undefined,
            query: isSet(object.query) ? types_1.ResponseQuery.fromJSON(object.query) : undefined,
            checkTx: isSet(object.checkTx) ? exports.ResponseCheckTx.fromJSON(object.checkTx) : undefined,
            commit: isSet(object.commit) ? exports.ResponseCommit.fromJSON(object.commit) : undefined,
            listSnapshots: isSet(object.listSnapshots) ? types_1.ResponseListSnapshots.fromJSON(object.listSnapshots) : undefined,
            offerSnapshot: isSet(object.offerSnapshot) ? types_1.ResponseOfferSnapshot.fromJSON(object.offerSnapshot) : undefined,
            loadSnapshotChunk: isSet(object.loadSnapshotChunk)
                ? types_1.ResponseLoadSnapshotChunk.fromJSON(object.loadSnapshotChunk)
                : undefined,
            applySnapshotChunk: isSet(object.applySnapshotChunk)
                ? types_1.ResponseApplySnapshotChunk.fromJSON(object.applySnapshotChunk)
                : undefined,
            prepareProposal: isSet(object.prepareProposal)
                ? types_2.ResponsePrepareProposal.fromJSON(object.prepareProposal)
                : undefined,
            processProposal: isSet(object.processProposal)
                ? types_2.ResponseProcessProposal.fromJSON(object.processProposal)
                : undefined,
            extendVote: isSet(object.extendVote) ? exports.ResponseExtendVote.fromJSON(object.extendVote) : undefined,
            verifyVoteExtension: isSet(object.verifyVoteExtension)
                ? exports.ResponseVerifyVoteExtension.fromJSON(object.verifyVoteExtension)
                : undefined,
            finalizeBlock: isSet(object.finalizeBlock) ? exports.ResponseFinalizeBlock.fromJSON(object.finalizeBlock) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.exception !== undefined &&
            (obj.exception = message.exception ? types_1.ResponseException.toJSON(message.exception) : undefined);
        message.echo !== undefined && (obj.echo = message.echo ? types_1.ResponseEcho.toJSON(message.echo) : undefined);
        message.flush !== undefined && (obj.flush = message.flush ? types_1.ResponseFlush.toJSON(message.flush) : undefined);
        message.info !== undefined && (obj.info = message.info ? types_1.ResponseInfo.toJSON(message.info) : undefined);
        message.initChain !== undefined &&
            (obj.initChain = message.initChain ? exports.ResponseInitChain.toJSON(message.initChain) : undefined);
        message.query !== undefined && (obj.query = message.query ? types_1.ResponseQuery.toJSON(message.query) : undefined);
        message.checkTx !== undefined &&
            (obj.checkTx = message.checkTx ? exports.ResponseCheckTx.toJSON(message.checkTx) : undefined);
        message.commit !== undefined && (obj.commit = message.commit ? exports.ResponseCommit.toJSON(message.commit) : undefined);
        message.listSnapshots !== undefined &&
            (obj.listSnapshots = message.listSnapshots ? types_1.ResponseListSnapshots.toJSON(message.listSnapshots) : undefined);
        message.offerSnapshot !== undefined &&
            (obj.offerSnapshot = message.offerSnapshot ? types_1.ResponseOfferSnapshot.toJSON(message.offerSnapshot) : undefined);
        message.loadSnapshotChunk !== undefined && (obj.loadSnapshotChunk = message.loadSnapshotChunk
            ? types_1.ResponseLoadSnapshotChunk.toJSON(message.loadSnapshotChunk)
            : undefined);
        message.applySnapshotChunk !== undefined && (obj.applySnapshotChunk = message.applySnapshotChunk
            ? types_1.ResponseApplySnapshotChunk.toJSON(message.applySnapshotChunk)
            : undefined);
        message.prepareProposal !== undefined && (obj.prepareProposal = message.prepareProposal
            ? types_2.ResponsePrepareProposal.toJSON(message.prepareProposal)
            : undefined);
        message.processProposal !== undefined && (obj.processProposal = message.processProposal
            ? types_2.ResponseProcessProposal.toJSON(message.processProposal)
            : undefined);
        message.extendVote !== undefined &&
            (obj.extendVote = message.extendVote ? exports.ResponseExtendVote.toJSON(message.extendVote) : undefined);
        message.verifyVoteExtension !== undefined && (obj.verifyVoteExtension = message.verifyVoteExtension
            ? exports.ResponseVerifyVoteExtension.toJSON(message.verifyVoteExtension)
            : undefined);
        message.finalizeBlock !== undefined &&
            (obj.finalizeBlock = message.finalizeBlock ? exports.ResponseFinalizeBlock.toJSON(message.finalizeBlock) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Response.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseResponse();
        message.exception = (object.exception !== undefined && object.exception !== null)
            ? types_1.ResponseException.fromPartial(object.exception)
            : undefined;
        message.echo = (object.echo !== undefined && object.echo !== null)
            ? types_1.ResponseEcho.fromPartial(object.echo)
            : undefined;
        message.flush = (object.flush !== undefined && object.flush !== null)
            ? types_1.ResponseFlush.fromPartial(object.flush)
            : undefined;
        message.info = (object.info !== undefined && object.info !== null)
            ? types_1.ResponseInfo.fromPartial(object.info)
            : undefined;
        message.initChain = (object.initChain !== undefined && object.initChain !== null)
            ? exports.ResponseInitChain.fromPartial(object.initChain)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? types_1.ResponseQuery.fromPartial(object.query)
            : undefined;
        message.checkTx = (object.checkTx !== undefined && object.checkTx !== null)
            ? exports.ResponseCheckTx.fromPartial(object.checkTx)
            : undefined;
        message.commit = (object.commit !== undefined && object.commit !== null)
            ? exports.ResponseCommit.fromPartial(object.commit)
            : undefined;
        message.listSnapshots = (object.listSnapshots !== undefined && object.listSnapshots !== null)
            ? types_1.ResponseListSnapshots.fromPartial(object.listSnapshots)
            : undefined;
        message.offerSnapshot = (object.offerSnapshot !== undefined && object.offerSnapshot !== null)
            ? types_1.ResponseOfferSnapshot.fromPartial(object.offerSnapshot)
            : undefined;
        message.loadSnapshotChunk = (object.loadSnapshotChunk !== undefined && object.loadSnapshotChunk !== null)
            ? types_1.ResponseLoadSnapshotChunk.fromPartial(object.loadSnapshotChunk)
            : undefined;
        message.applySnapshotChunk = (object.applySnapshotChunk !== undefined && object.applySnapshotChunk !== null)
            ? types_1.ResponseApplySnapshotChunk.fromPartial(object.applySnapshotChunk)
            : undefined;
        message.prepareProposal = (object.prepareProposal !== undefined && object.prepareProposal !== null)
            ? types_2.ResponsePrepareProposal.fromPartial(object.prepareProposal)
            : undefined;
        message.processProposal = (object.processProposal !== undefined && object.processProposal !== null)
            ? types_2.ResponseProcessProposal.fromPartial(object.processProposal)
            : undefined;
        message.extendVote = (object.extendVote !== undefined && object.extendVote !== null)
            ? exports.ResponseExtendVote.fromPartial(object.extendVote)
            : undefined;
        message.verifyVoteExtension = (object.verifyVoteExtension !== undefined && object.verifyVoteExtension !== null)
            ? exports.ResponseVerifyVoteExtension.fromPartial(object.verifyVoteExtension)
            : undefined;
        message.finalizeBlock = (object.finalizeBlock !== undefined && object.finalizeBlock !== null)
            ? exports.ResponseFinalizeBlock.fromPartial(object.finalizeBlock)
            : undefined;
        return message;
    },
};
function createBaseResponseInitChain() {
    return { consensusParams: undefined, validators: [], appHash: new Uint8Array() };
}
exports.ResponseInitChain = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.validators), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.ValidatorUpdate.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        if (message.appHash.length !== 0) {
            writer.uint32(26).bytes(message.appHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseInitChain();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.validators.push(types_1.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            validators: Array.isArray(object === null || object === void 0 ? void 0 : object.validators)
                ? object.validators.map(function (e) { return types_1.ValidatorUpdate.fromJSON(e); })
                : [],
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        if (message.validators) {
            obj.validators = message.validators.map(function (e) { return e ? types_1.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validators = [];
        }
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ResponseInitChain.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseResponseInitChain();
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.validators = ((_a = object.validators) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_1.ValidatorUpdate.fromPartial(e); })) || [];
        message.appHash = (_b = object.appHash) !== null && _b !== void 0 ? _b : new Uint8Array();
        return message;
    },
};
function createBaseResponseCheckTx() {
    return {
        code: 0,
        data: new Uint8Array(),
        log: "",
        info: "",
        gasWanted: "0",
        gasUsed: "0",
        events: [],
        codespace: "",
    };
}
exports.ResponseCheckTx = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.code !== 0) {
            writer.uint32(8).uint32(message.code);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        if (message.log !== "") {
            writer.uint32(26).string(message.log);
        }
        if (message.info !== "") {
            writer.uint32(34).string(message.info);
        }
        if (message.gasWanted !== "0") {
            writer.uint32(40).int64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(48).int64(message.gasUsed);
        }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_2.Event.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        if (message.codespace !== "") {
            writer.uint32(66).string(message.codespace);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseCheckTx();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.uint32();
                    break;
                case 2:
                    message.data = reader.bytes();
                    break;
                case 3:
                    message.log = reader.string();
                    break;
                case 4:
                    message.info = reader.string();
                    break;
                case 5:
                    message.gasWanted = longToString(reader.int64());
                    break;
                case 6:
                    message.gasUsed = longToString(reader.int64());
                    break;
                case 7:
                    message.events.push(types_2.Event.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.codespace = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            log: isSet(object.log) ? String(object.log) : "",
            info: isSet(object.info) ? String(object.info) : "",
            gasWanted: isSet(object.gas_wanted) ? String(object.gas_wanted) : "0",
            gasUsed: isSet(object.gas_used) ? String(object.gas_used) : "0",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_2.Event.fromJSON(e); }) : [],
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.log !== undefined && (obj.log = message.log);
        message.info !== undefined && (obj.info = message.info);
        message.gasWanted !== undefined && (obj.gas_wanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gas_used = message.gasUsed);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_2.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        message.codespace !== undefined && (obj.codespace = message.codespace);
        return obj;
    },
    create: function (base) {
        return exports.ResponseCheckTx.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseResponseCheckTx();
        message.code = (_a = object.code) !== null && _a !== void 0 ? _a : 0;
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.log = (_c = object.log) !== null && _c !== void 0 ? _c : "";
        message.info = (_d = object.info) !== null && _d !== void 0 ? _d : "";
        message.gasWanted = (_e = object.gasWanted) !== null && _e !== void 0 ? _e : "0";
        message.gasUsed = (_f = object.gasUsed) !== null && _f !== void 0 ? _f : "0";
        message.events = ((_g = object.events) === null || _g === void 0 ? void 0 : _g.map(function (e) { return types_2.Event.fromPartial(e); })) || [];
        message.codespace = (_h = object.codespace) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseResponseCommit() {
    return { retainHeight: "0" };
}
exports.ResponseCommit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.retainHeight !== "0") {
            writer.uint32(24).int64(message.retainHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseCommit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 3:
                    message.retainHeight = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { retainHeight: isSet(object.retainHeight) ? String(object.retainHeight) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.retainHeight !== undefined && (obj.retainHeight = message.retainHeight);
        return obj;
    },
    create: function (base) {
        return exports.ResponseCommit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseResponseCommit();
        message.retainHeight = (_a = object.retainHeight) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseResponseExtendVote() {
    return { voteExtension: new Uint8Array() };
}
exports.ResponseExtendVote = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.voteExtension.length !== 0) {
            writer.uint32(10).bytes(message.voteExtension);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseExtendVote();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.voteExtension = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array() };
    },
    toJSON: function (message) {
        var obj = {};
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ResponseExtendVote.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseResponseExtendVote();
        message.voteExtension = (_a = object.voteExtension) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseResponseVerifyVoteExtension() {
    return { status: 0 };
}
exports.ResponseVerifyVoteExtension = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseVerifyVoteExtension();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { status: isSet(object.status) ? responseVerifyVoteExtension_VerifyStatusFromJSON(object.status) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = responseVerifyVoteExtension_VerifyStatusToJSON(message.status));
        return obj;
    },
    create: function (base) {
        return exports.ResponseVerifyVoteExtension.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseResponseVerifyVoteExtension();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseResponseFinalizeBlock() {
    return {
        events: [],
        txResults: [],
        validatorUpdates: [],
        consensusParamUpdates: undefined,
        appHash: new Uint8Array(),
    };
}
exports.ResponseFinalizeBlock = {
    encode: function (message, writer) {
        var e_12, _a, e_13, _b, e_14, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _d = __values(message.events), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                types_2.Event.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_12) throw e_12.error; }
        }
        try {
            for (var _f = __values(message.txResults), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exports.ExecTxResult.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_13) throw e_13.error; }
        }
        try {
            for (var _h = __values(message.validatorUpdates), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                types_1.ValidatorUpdate.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_14) throw e_14.error; }
        }
        if (message.consensusParamUpdates !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParamUpdates, writer.uint32(34).fork()).ldelim();
        }
        if (message.appHash.length !== 0) {
            writer.uint32(42).bytes(message.appHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseFinalizeBlock();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.events.push(types_2.Event.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.txResults.push(exports.ExecTxResult.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.validatorUpdates.push(types_1.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.consensusParamUpdates = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_2.Event.fromJSON(e); }) : [],
            txResults: Array.isArray(object === null || object === void 0 ? void 0 : object.txResults) ? object.txResults.map(function (e) { return exports.ExecTxResult.fromJSON(e); }) : [],
            validatorUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.validatorUpdates)
                ? object.validatorUpdates.map(function (e) { return types_1.ValidatorUpdate.fromJSON(e); })
                : [],
            consensusParamUpdates: isSet(object.consensusParamUpdates)
                ? params_1.ConsensusParams.fromJSON(object.consensusParamUpdates)
                : undefined,
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_2.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        if (message.txResults) {
            obj.txResults = message.txResults.map(function (e) { return e ? exports.ExecTxResult.toJSON(e) : undefined; });
        }
        else {
            obj.txResults = [];
        }
        if (message.validatorUpdates) {
            obj.validatorUpdates = message.validatorUpdates.map(function (e) { return e ? types_1.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validatorUpdates = [];
        }
        message.consensusParamUpdates !== undefined && (obj.consensusParamUpdates = message.consensusParamUpdates
            ? params_1.ConsensusParams.toJSON(message.consensusParamUpdates)
            : undefined);
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ResponseFinalizeBlock.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseResponseFinalizeBlock();
        message.events = ((_a = object.events) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_2.Event.fromPartial(e); })) || [];
        message.txResults = ((_b = object.txResults) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.ExecTxResult.fromPartial(e); })) || [];
        message.validatorUpdates = ((_c = object.validatorUpdates) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_1.ValidatorUpdate.fromPartial(e); })) || [];
        message.consensusParamUpdates =
            (object.consensusParamUpdates !== undefined && object.consensusParamUpdates !== null)
                ? params_1.ConsensusParams.fromPartial(object.consensusParamUpdates)
                : undefined;
        message.appHash = (_d = object.appHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        return message;
    },
};
function createBaseVoteInfo() {
    return { validator: undefined, blockIdFlag: 0 };
}
exports.VoteInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validator !== undefined) {
            types_1.Validator.encode(message.validator, writer.uint32(10).fork()).ldelim();
        }
        if (message.blockIdFlag !== 0) {
            writer.uint32(24).int32(message.blockIdFlag);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseVoteInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validator = types_1.Validator.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.blockIdFlag = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validator: isSet(object.validator) ? types_1.Validator.fromJSON(object.validator) : undefined,
            blockIdFlag: isSet(object.blockIdFlag) ? (0, validator_1.blockIDFlagFromJSON)(object.blockIdFlag) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validator !== undefined &&
            (obj.validator = message.validator ? types_1.Validator.toJSON(message.validator) : undefined);
        message.blockIdFlag !== undefined && (obj.blockIdFlag = (0, validator_1.blockIDFlagToJSON)(message.blockIdFlag));
        return obj;
    },
    create: function (base) {
        return exports.VoteInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseVoteInfo();
        message.validator = (object.validator !== undefined && object.validator !== null)
            ? types_1.Validator.fromPartial(object.validator)
            : undefined;
        message.blockIdFlag = (_a = object.blockIdFlag) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseExtendedVoteInfo() {
    return {
        validator: undefined,
        voteExtension: new Uint8Array(),
        extensionSignature: new Uint8Array(),
        blockIdFlag: 0,
    };
}
exports.ExtendedVoteInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validator !== undefined) {
            types_1.Validator.encode(message.validator, writer.uint32(10).fork()).ldelim();
        }
        if (message.voteExtension.length !== 0) {
            writer.uint32(26).bytes(message.voteExtension);
        }
        if (message.extensionSignature.length !== 0) {
            writer.uint32(34).bytes(message.extensionSignature);
        }
        if (message.blockIdFlag !== 0) {
            writer.uint32(40).int32(message.blockIdFlag);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendedVoteInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validator = types_1.Validator.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.voteExtension = reader.bytes();
                    break;
                case 4:
                    message.extensionSignature = reader.bytes();
                    break;
                case 5:
                    message.blockIdFlag = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validator: isSet(object.validator) ? types_1.Validator.fromJSON(object.validator) : undefined,
            voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array(),
            extensionSignature: isSet(object.extensionSignature)
                ? bytesFromBase64(object.extensionSignature)
                : new Uint8Array(),
            blockIdFlag: isSet(object.blockIdFlag) ? (0, validator_1.blockIDFlagFromJSON)(object.blockIdFlag) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validator !== undefined &&
            (obj.validator = message.validator ? types_1.Validator.toJSON(message.validator) : undefined);
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        message.extensionSignature !== undefined &&
            (obj.extensionSignature = base64FromBytes(message.extensionSignature !== undefined ? message.extensionSignature : new Uint8Array()));
        message.blockIdFlag !== undefined && (obj.blockIdFlag = (0, validator_1.blockIDFlagToJSON)(message.blockIdFlag));
        return obj;
    },
    create: function (base) {
        return exports.ExtendedVoteInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseExtendedVoteInfo();
        message.validator = (object.validator !== undefined && object.validator !== null)
            ? types_1.Validator.fromPartial(object.validator)
            : undefined;
        message.voteExtension = (_a = object.voteExtension) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.extensionSignature = (_b = object.extensionSignature) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.blockIdFlag = (_c = object.blockIdFlag) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseCommitInfo() {
    return { round: 0, votes: [] };
}
exports.CommitInfo = {
    encode: function (message, writer) {
        var e_15, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.round !== 0) {
            writer.uint32(8).int32(message.round);
        }
        try {
            for (var _b = __values(message.votes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.VoteInfo.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_15) throw e_15.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCommitInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = reader.int32();
                    break;
                case 2:
                    message.votes.push(exports.VoteInfo.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            round: isSet(object.round) ? Number(object.round) : 0,
            votes: Array.isArray(object === null || object === void 0 ? void 0 : object.votes) ? object.votes.map(function (e) { return exports.VoteInfo.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.round !== undefined && (obj.round = Math.round(message.round));
        if (message.votes) {
            obj.votes = message.votes.map(function (e) { return e ? exports.VoteInfo.toJSON(e) : undefined; });
        }
        else {
            obj.votes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CommitInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCommitInfo();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : 0;
        message.votes = ((_b = object.votes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.VoteInfo.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseExtendedCommitInfo() {
    return { round: 0, votes: [] };
}
exports.ExtendedCommitInfo = {
    encode: function (message, writer) {
        var e_16, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.round !== 0) {
            writer.uint32(8).int32(message.round);
        }
        try {
            for (var _b = __values(message.votes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ExtendedVoteInfo.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendedCommitInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = reader.int32();
                    break;
                case 2:
                    message.votes.push(exports.ExtendedVoteInfo.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            round: isSet(object.round) ? Number(object.round) : 0,
            votes: Array.isArray(object === null || object === void 0 ? void 0 : object.votes) ? object.votes.map(function (e) { return exports.ExtendedVoteInfo.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.round !== undefined && (obj.round = Math.round(message.round));
        if (message.votes) {
            obj.votes = message.votes.map(function (e) { return e ? exports.ExtendedVoteInfo.toJSON(e) : undefined; });
        }
        else {
            obj.votes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ExtendedCommitInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseExtendedCommitInfo();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : 0;
        message.votes = ((_b = object.votes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.ExtendedVoteInfo.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseExecTxResult() {
    return {
        code: 0,
        data: new Uint8Array(),
        log: "",
        info: "",
        gasWanted: "0",
        gasUsed: "0",
        events: [],
        codespace: "",
    };
}
exports.ExecTxResult = {
    encode: function (message, writer) {
        var e_17, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.code !== 0) {
            writer.uint32(8).uint32(message.code);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        if (message.log !== "") {
            writer.uint32(26).string(message.log);
        }
        if (message.info !== "") {
            writer.uint32(34).string(message.info);
        }
        if (message.gasWanted !== "0") {
            writer.uint32(40).int64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(48).int64(message.gasUsed);
        }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_2.Event.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_17) throw e_17.error; }
        }
        if (message.codespace !== "") {
            writer.uint32(66).string(message.codespace);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExecTxResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.uint32();
                    break;
                case 2:
                    message.data = reader.bytes();
                    break;
                case 3:
                    message.log = reader.string();
                    break;
                case 4:
                    message.info = reader.string();
                    break;
                case 5:
                    message.gasWanted = longToString(reader.int64());
                    break;
                case 6:
                    message.gasUsed = longToString(reader.int64());
                    break;
                case 7:
                    message.events.push(types_2.Event.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.codespace = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            log: isSet(object.log) ? String(object.log) : "",
            info: isSet(object.info) ? String(object.info) : "",
            gasWanted: isSet(object.gas_wanted) ? String(object.gas_wanted) : "0",
            gasUsed: isSet(object.gas_used) ? String(object.gas_used) : "0",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_2.Event.fromJSON(e); }) : [],
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.log !== undefined && (obj.log = message.log);
        message.info !== undefined && (obj.info = message.info);
        message.gasWanted !== undefined && (obj.gas_wanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gas_used = message.gasUsed);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_2.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        message.codespace !== undefined && (obj.codespace = message.codespace);
        return obj;
    },
    create: function (base) {
        return exports.ExecTxResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseExecTxResult();
        message.code = (_a = object.code) !== null && _a !== void 0 ? _a : 0;
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.log = (_c = object.log) !== null && _c !== void 0 ? _c : "";
        message.info = (_d = object.info) !== null && _d !== void 0 ? _d : "";
        message.gasWanted = (_e = object.gasWanted) !== null && _e !== void 0 ? _e : "0";
        message.gasUsed = (_f = object.gasUsed) !== null && _f !== void 0 ? _f : "0";
        message.events = ((_g = object.events) === null || _g === void 0 ? void 0 : _g.map(function (e) { return types_2.Event.fromPartial(e); })) || [];
        message.codespace = (_h = object.codespace) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseTxResult() {
    return { height: "0", index: 0, tx: new Uint8Array(), result: undefined };
}
exports.TxResult = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        if (message.index !== 0) {
            writer.uint32(16).uint32(message.index);
        }
        if (message.tx.length !== 0) {
            writer.uint32(26).bytes(message.tx);
        }
        if (message.result !== undefined) {
            exports.ExecTxResult.encode(message.result, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTxResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                case 2:
                    message.index = reader.uint32();
                    break;
                case 3:
                    message.tx = reader.bytes();
                    break;
                case 4:
                    message.result = exports.ExecTxResult.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            index: isSet(object.index) ? Number(object.index) : 0,
            tx: isSet(object.tx) ? bytesFromBase64(object.tx) : new Uint8Array(),
            result: isSet(object.result) ? exports.ExecTxResult.fromJSON(object.result) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.index !== undefined && (obj.index = Math.round(message.index));
        message.tx !== undefined && (obj.tx = base64FromBytes(message.tx !== undefined ? message.tx : new Uint8Array()));
        message.result !== undefined && (obj.result = message.result ? exports.ExecTxResult.toJSON(message.result) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.TxResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTxResult();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.index = (_b = object.index) !== null && _b !== void 0 ? _b : 0;
        message.tx = (_c = object.tx) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.result = (object.result !== undefined && object.result !== null)
            ? exports.ExecTxResult.fromPartial(object.result)
            : undefined;
        return message;
    },
};
var ABCIClientImpl = /** @class */ (function () {
    function ABCIClientImpl(rpc) {
        this.rpc = rpc;
        this.Echo = this.Echo.bind(this);
        this.Flush = this.Flush.bind(this);
        this.Info = this.Info.bind(this);
        this.CheckTx = this.CheckTx.bind(this);
        this.Query = this.Query.bind(this);
        this.Commit = this.Commit.bind(this);
        this.InitChain = this.InitChain.bind(this);
        this.ListSnapshots = this.ListSnapshots.bind(this);
        this.OfferSnapshot = this.OfferSnapshot.bind(this);
        this.LoadSnapshotChunk = this.LoadSnapshotChunk.bind(this);
        this.ApplySnapshotChunk = this.ApplySnapshotChunk.bind(this);
        this.PrepareProposal = this.PrepareProposal.bind(this);
        this.ProcessProposal = this.ProcessProposal.bind(this);
        this.ExtendVote = this.ExtendVote.bind(this);
        this.VerifyVoteExtension = this.VerifyVoteExtension.bind(this);
        this.FinalizeBlock = this.FinalizeBlock.bind(this);
    }
    ABCIClientImpl.prototype.Echo = function (request, metadata) {
        return this.rpc.unary(exports.ABCIEchoDesc, types_1.RequestEcho.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.Flush = function (request, metadata) {
        return this.rpc.unary(exports.ABCIFlushDesc, types_1.RequestFlush.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.Info = function (request, metadata) {
        return this.rpc.unary(exports.ABCIInfoDesc, types_2.RequestInfo.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.CheckTx = function (request, metadata) {
        return this.rpc.unary(exports.ABCICheckTxDesc, types_1.RequestCheckTx.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.Query = function (request, metadata) {
        return this.rpc.unary(exports.ABCIQueryDesc, types_1.RequestQuery.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.Commit = function (request, metadata) {
        return this.rpc.unary(exports.ABCICommitDesc, types_1.RequestCommit.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.InitChain = function (request, metadata) {
        return this.rpc.unary(exports.ABCIInitChainDesc, exports.RequestInitChain.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.ListSnapshots = function (request, metadata) {
        return this.rpc.unary(exports.ABCIListSnapshotsDesc, types_1.RequestListSnapshots.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.OfferSnapshot = function (request, metadata) {
        return this.rpc.unary(exports.ABCIOfferSnapshotDesc, types_1.RequestOfferSnapshot.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.LoadSnapshotChunk = function (request, metadata) {
        return this.rpc.unary(exports.ABCILoadSnapshotChunkDesc, types_1.RequestLoadSnapshotChunk.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.ApplySnapshotChunk = function (request, metadata) {
        return this.rpc.unary(exports.ABCIApplySnapshotChunkDesc, types_1.RequestApplySnapshotChunk.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.PrepareProposal = function (request, metadata) {
        return this.rpc.unary(exports.ABCIPrepareProposalDesc, exports.RequestPrepareProposal.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.ProcessProposal = function (request, metadata) {
        return this.rpc.unary(exports.ABCIProcessProposalDesc, exports.RequestProcessProposal.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.ExtendVote = function (request, metadata) {
        return this.rpc.unary(exports.ABCIExtendVoteDesc, exports.RequestExtendVote.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.VerifyVoteExtension = function (request, metadata) {
        return this.rpc.unary(exports.ABCIVerifyVoteExtensionDesc, exports.RequestVerifyVoteExtension.fromPartial(request), metadata);
    };
    ABCIClientImpl.prototype.FinalizeBlock = function (request, metadata) {
        return this.rpc.unary(exports.ABCIFinalizeBlockDesc, exports.RequestFinalizeBlock.fromPartial(request), metadata);
    };
    return ABCIClientImpl;
}());
exports.ABCIClientImpl = ABCIClientImpl;
exports.ABCIDesc = { serviceName: "cometbft.abci.v1beta3.ABCI" };
exports.ABCIEchoDesc = {
    methodName: "Echo",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestEcho.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseEcho.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIFlushDesc = {
    methodName: "Flush",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestFlush.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseFlush.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIInfoDesc = {
    methodName: "Info",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_2.RequestInfo.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseInfo.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCICheckTxDesc = {
    methodName: "CheckTx",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestCheckTx.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseCheckTx.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIQueryDesc = {
    methodName: "Query",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestQuery.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseQuery.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCICommitDesc = {
    methodName: "Commit",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestCommit.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseCommit.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIInitChainDesc = {
    methodName: "InitChain",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestInitChain.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseInitChain.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIListSnapshotsDesc = {
    methodName: "ListSnapshots",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestListSnapshots.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseListSnapshots.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIOfferSnapshotDesc = {
    methodName: "OfferSnapshot",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestOfferSnapshot.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseOfferSnapshot.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCILoadSnapshotChunkDesc = {
    methodName: "LoadSnapshotChunk",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestLoadSnapshotChunk.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseLoadSnapshotChunk.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIApplySnapshotChunkDesc = {
    methodName: "ApplySnapshotChunk",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return types_1.RequestApplySnapshotChunk.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_1.ResponseApplySnapshotChunk.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIPrepareProposalDesc = {
    methodName: "PrepareProposal",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestPrepareProposal.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_2.ResponsePrepareProposal.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIProcessProposalDesc = {
    methodName: "ProcessProposal",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestProcessProposal.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = types_2.ResponseProcessProposal.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIExtendVoteDesc = {
    methodName: "ExtendVote",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestExtendVote.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseExtendVote.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIVerifyVoteExtensionDesc = {
    methodName: "VerifyVoteExtension",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestVerifyVoteExtension.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseVerifyVoteExtension.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.ABCIFinalizeBlockDesc = {
    methodName: "FinalizeBlock",
    service: exports.ABCIDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.RequestFinalizeBlock.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.ResponseFinalizeBlock.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function toTimestamp(date) {
    var seconds = Math.trunc(date.getTime() / 1000).toString();
    var nanos = (date.getTime() % 1000) * 1000000;
    return { seconds: seconds, nanos: nanos };
}
function fromTimestamp(t) {
    var millis = Number(t.seconds) * 1000;
    millis += t.nanos / 1000000;
    return new Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
