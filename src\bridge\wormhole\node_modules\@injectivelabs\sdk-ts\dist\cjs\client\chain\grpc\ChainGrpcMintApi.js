"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcMintApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const index_js_1 = require("../../../utils/index.js");
const utils_1 = require("@injectivelabs/utils");
const ChainGrpcMintTransformer_js_1 = require("./../transformers/ChainGrpcMintTransformer.js");
const index_js_2 = require("../types/index.js");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
/**
 * @category Chain Grpc API
 */
class ChainGrpcMintApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_2.ChainModule.Mint;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.CosmosMintV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.CosmosMintV1Beta1Query.QueryParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.Params(request, this.metadata));
            return ChainGrpcMintTransformer_js_1.ChainGrpcMintTransformer.moduleParamsResponseToModuleParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosMintV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Params',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Params',
                contextModule: this.module,
            });
        }
    }
    async fetchInflation() {
        const request = core_proto_ts_1.CosmosMintV1Beta1Query.QueryInflationRequest.create();
        try {
            const response = await this.retry(() => this.client.Inflation(request, this.metadata));
            return {
                inflation: (0, index_js_1.cosmosSdkDecToBigNumber)(new utils_1.BigNumberInBase((0, index_js_1.uint8ArrayToString)(response.inflation))).toFixed(),
            };
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosMintV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Inflation',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Inflation',
                contextModule: this.module,
            });
        }
    }
    async fetchAnnualProvisions() {
        const request = core_proto_ts_1.CosmosMintV1Beta1Query.QueryAnnualProvisionsRequest.create();
        try {
            const response = await this.retry(() => this.client.AnnualProvisions(request, this.metadata));
            return {
                annualProvisions: (0, index_js_1.cosmosSdkDecToBigNumber)(new utils_1.BigNumberInBase((0, index_js_1.uint8ArrayToString)(response.annualProvisions))).toFixed(),
            };
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosMintV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'AnnualProvisions',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'AnnualProvisions',
                contextModule: this.module,
            });
        }
    }
}
exports.ChainGrpcMintApi = ChainGrpcMintApi;
