import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { GenesisState } from "./genesis";
import { Params, RegisteredContract } from "./wasmx";
export declare const protobufPackage = "injective.wasmx.v1";
/**
 * QueryWasmxParamsRequest is the request type for the Query/WasmxParams RPC
 * method.
 */
export interface QueryWasmxParamsRequest {
}
/**
 * QueryWasmxParamsRequest is the response type for the Query/WasmxParams RPC
 * method.
 */
export interface QueryWasmxParamsResponse {
    params: Params | undefined;
}
/**
 * QueryModuleStateRequest is the request type for the Query/WasmxModuleState
 * RPC method.
 */
export interface QueryModuleStateRequest {
}
/**
 * QueryModuleStateResponse is the response type for the Query/WasmxModuleState
 * RPC method.
 */
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
/** Contract registration info */
export interface QueryContractRegistrationInfoRequest {
    contractAddress: string;
}
export interface QueryContractRegistrationInfoResponse {
    contract: RegisteredContract | undefined;
}
export declare const QueryWasmxParamsRequest: {
    encode(_: QueryWasmxParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryWasmxParamsRequest;
    fromJSON(_: any): QueryWasmxParamsRequest;
    toJSON(_: QueryWasmxParamsRequest): unknown;
    create(base?: DeepPartial<QueryWasmxParamsRequest>): QueryWasmxParamsRequest;
    fromPartial(_: DeepPartial<QueryWasmxParamsRequest>): QueryWasmxParamsRequest;
};
export declare const QueryWasmxParamsResponse: {
    encode(message: QueryWasmxParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryWasmxParamsResponse;
    fromJSON(object: any): QueryWasmxParamsResponse;
    toJSON(message: QueryWasmxParamsResponse): unknown;
    create(base?: DeepPartial<QueryWasmxParamsResponse>): QueryWasmxParamsResponse;
    fromPartial(object: DeepPartial<QueryWasmxParamsResponse>): QueryWasmxParamsResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
export declare const QueryContractRegistrationInfoRequest: {
    encode(message: QueryContractRegistrationInfoRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryContractRegistrationInfoRequest;
    fromJSON(object: any): QueryContractRegistrationInfoRequest;
    toJSON(message: QueryContractRegistrationInfoRequest): unknown;
    create(base?: DeepPartial<QueryContractRegistrationInfoRequest>): QueryContractRegistrationInfoRequest;
    fromPartial(object: DeepPartial<QueryContractRegistrationInfoRequest>): QueryContractRegistrationInfoRequest;
};
export declare const QueryContractRegistrationInfoResponse: {
    encode(message: QueryContractRegistrationInfoResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryContractRegistrationInfoResponse;
    fromJSON(object: any): QueryContractRegistrationInfoResponse;
    toJSON(message: QueryContractRegistrationInfoResponse): unknown;
    create(base?: DeepPartial<QueryContractRegistrationInfoResponse>): QueryContractRegistrationInfoResponse;
    fromPartial(object: DeepPartial<QueryContractRegistrationInfoResponse>): QueryContractRegistrationInfoResponse;
};
/** Query defines the gRPC querier service. */
export interface Query {
    /** Retrieves wasmx params */
    WasmxParams(request: DeepPartial<QueryWasmxParamsRequest>, metadata?: grpc.Metadata): Promise<QueryWasmxParamsResponse>;
    /** Retrieves contract registration info */
    ContractRegistrationInfo(request: DeepPartial<QueryContractRegistrationInfoRequest>, metadata?: grpc.Metadata): Promise<QueryContractRegistrationInfoResponse>;
    /** Retrieves the entire wasmx module's state */
    WasmxModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    WasmxParams(request: DeepPartial<QueryWasmxParamsRequest>, metadata?: grpc.Metadata): Promise<QueryWasmxParamsResponse>;
    ContractRegistrationInfo(request: DeepPartial<QueryContractRegistrationInfoRequest>, metadata?: grpc.Metadata): Promise<QueryContractRegistrationInfoResponse>;
    WasmxModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryWasmxParamsDesc: UnaryMethodDefinitionish;
export declare const QueryContractRegistrationInfoDesc: UnaryMethodDefinitionish;
export declare const QueryWasmxModuleStateDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
