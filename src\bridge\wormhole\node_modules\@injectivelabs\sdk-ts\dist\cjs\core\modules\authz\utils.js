"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGenericAuthorizationFromMessageType = exports.msgsOrMsgExecMsgs = void 0;
const MsgExec_js_1 = __importDefault(require("./msgs/MsgExec.js"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const msgsOrMsgExecMsgs = (msgs, grantee) => {
    const actualMsgs = Array.isArray(msgs) ? msgs : [msgs];
    if (!grantee) {
        return actualMsgs;
    }
    return actualMsgs.map((msg) => MsgExec_js_1.default.fromJSON({ grantee, msgs: msg }));
};
exports.msgsOrMsgExecMsgs = msgsOrMsgExecMsgs;
const getGenericAuthorizationFromMessageType = (messageTypeUrl) => {
    const genericAuthorizationType = '/cosmos.authz.v1beta1.GenericAuthorization';
    const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.create();
    genericAuthorization.msg = messageTypeUrl.startsWith('/')
        ? messageTypeUrl
        : `/${messageTypeUrl}`;
    const authorization = core_proto_ts_1.GoogleProtobufAny.Any.create();
    authorization.typeUrl = genericAuthorizationType;
    authorization.value = Buffer.from(core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.encode(genericAuthorization).finish());
    return authorization;
};
exports.getGenericAuthorizationFromMessageType = getGenericAuthorizationFromMessageType;
