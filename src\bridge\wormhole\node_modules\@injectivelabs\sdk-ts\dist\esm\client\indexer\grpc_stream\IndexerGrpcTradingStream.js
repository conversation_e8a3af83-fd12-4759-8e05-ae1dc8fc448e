import { InjectiveTradingRpc } from '@injectivelabs/indexer-proto-ts';
import { getGrpcIndexerWebImpl } from '../../base/BaseIndexerGrpcWebConsumer.js';
/**
 * @category Indexer Grid Strategy Grpc Stream
 */
export class IndexerGrpcTradingStream {
    client;
    constructor(endpoint) {
        this.client = new InjectiveTradingRpc.InjectiveTradingRPCClientImpl(getGrpcIndexerWebImpl(endpoint));
    }
    streamGridStrategies({ marketId, callback, onEndCallback, accountAddresses, onStatusCallback, }) {
        const request = InjectiveTradingRpc.StreamStrategyRequest.create();
        if ((!accountAddresses || accountAddresses.length === 0) && !marketId) {
            throw new Error('accountAddresses or marketId is required');
        }
        if (typeof callback !== 'function') {
            throw new Error('callback must be a function');
        }
        if (accountAddresses) {
            request.accountAddresses = accountAddresses;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        const subscription = this.client.StreamStrategy(request).subscribe({
            next(response) {
                callback(response);
            },
            error(err) {
                if (onStatusCallback) {
                    onStatusCallback(err);
                }
            },
            complete() {
                if (onEndCallback) {
                    onEndCallback();
                }
            },
        });
        return subscription;
    }
}
