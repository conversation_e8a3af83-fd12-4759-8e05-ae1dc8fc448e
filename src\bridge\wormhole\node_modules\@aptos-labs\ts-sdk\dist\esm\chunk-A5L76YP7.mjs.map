{"version": 3, "sources": ["../../src/transactions/management/asyncQueue.ts"], "sourcesContent": ["/**\n * The AsyncQueue class is an async-aware data structure that provides a queue-like\n * behavior for managing asynchronous tasks or operations.\n * It allows to enqueue items and dequeue them asynchronously.\n * This is not thread-safe, but it is async concurrency safe, and\n * it does not guarantee ordering for those that call into and await on enqueue.\n * @group Implementation\n * @category Transactions\n */\n\ninterface PendingDequeue<T> {\n  resolve: (value: T) => void;\n  reject: (reason?: AsyncQueueCancelledError) => void;\n}\n\nexport class AsyncQueue<T> {\n  readonly queue: T[] = [];\n\n  // The pendingDequeue is used to handle the resolution of promises when items are enqueued and dequeued.\n  private pendingDequeue: PendingDequeue<T>[] = [];\n\n  private cancelled: boolean = false;\n\n  /**\n   * Adds an item to the queue. If there are pending dequeued promises, it resolves the oldest promise with the enqueued item\n   * immediately; otherwise, it adds the item to the queue.\n   *\n   * @param item - The item to be added to the queue.\n   * @group Implementation\n   * @category Transactions\n   */\n  enqueue(item: T): void {\n    this.cancelled = false;\n\n    if (this.pendingDequeue.length > 0) {\n      const promise = this.pendingDequeue.shift();\n\n      promise?.resolve(item);\n\n      return;\n    }\n\n    this.queue.push(item);\n  }\n\n  /**\n   * Dequeues the next item from the queue and returns a promise that resolves to it.\n   * If the queue is empty, it creates a new promise that will be resolved when an item is enqueued.\n   *\n   * @returns Promise<T>\n   * @group Implementation\n   * @category Transactions\n   */\n  async dequeue(): Promise<T> {\n    if (this.queue.length > 0) {\n      return Promise.resolve(this.queue.shift()!);\n    }\n\n    return new Promise<T>((resolve, reject) => {\n      this.pendingDequeue.push({ resolve, reject });\n    });\n  }\n\n  /**\n   * Determine whether the queue is empty.\n   *\n   * @returns boolean - Returns true if the queue has no elements, otherwise false.\n   * @group Implementation\n   * @category Transactions\n   */\n  isEmpty(): boolean {\n    return this.queue.length === 0;\n  }\n\n  /**\n   * Cancels all pending promises in the queue and rejects them with an AsyncQueueCancelledError.\n   * This ensures that any awaiting code can handle the cancellation appropriately.\n   *\n   * @returns {void}\n   * @group Implementation\n   * @category Transactions\n   */\n  cancel(): void {\n    this.cancelled = true;\n\n    this.pendingDequeue.forEach(async ({ reject }) => {\n      reject(new AsyncQueueCancelledError(\"Task cancelled\"));\n    });\n\n    this.pendingDequeue = [];\n\n    this.queue.length = 0;\n  }\n\n  /**\n   * Determine whether the queue has been cancelled.\n   *\n   * @returns boolean - Returns true if the queue is cancelled, otherwise false.\n   * @group Implementation\n   * @category Transactions\n   */\n  isCancelled(): boolean {\n    return this.cancelled;\n  }\n\n  /**\n   * Retrieve the length of the pending dequeue.\n   *\n   * @returns number - The number of items currently in the pending dequeue.\n   * @group Implementation\n   * @category Transactions\n   */\n  pendingDequeueLength(): number {\n    return this.pendingDequeue.length;\n  }\n}\n\n/**\n * Represents an error that occurs when an asynchronous queue operation is cancelled.\n * This error extends the built-in Error class to provide additional context for cancellation events.\n *\n * @extends Error\n * @group Implementation\n * @category Transactions\n */\nexport class AsyncQueueCancelledError extends Error {}\n"], "mappings": "AAeO,IAAMA,EAAN,KAAoB,CAApB,cACL,KAAS,MAAa,CAAC,EAGvB,KAAQ,eAAsC,CAAC,EAE/C,KAAQ,UAAqB,GAU7B,QAAQC,EAAe,CAGrB,GAFA,KAAK,UAAY,GAEb,KAAK,eAAe,OAAS,EAAG,CAClB,KAAK,eAAe,MAAM,GAEjC,QAAQA,CAAI,EAErB,MACF,CAEA,KAAK,MAAM,KAAKA,CAAI,CACtB,CAUA,MAAM,SAAsB,CAC1B,OAAI,KAAK,MAAM,OAAS,EACf,QAAQ,QAAQ,KAAK,MAAM,MAAM,CAAE,EAGrC,IAAI,QAAW,CAACC,EAASC,IAAW,CACzC,KAAK,eAAe,KAAK,CAAE,QAAAD,EAAS,OAAAC,CAAO,CAAC,CAC9C,CAAC,CACH,CASA,SAAmB,CACjB,OAAO,KAAK,MAAM,SAAW,CAC/B,CAUA,QAAe,CACb,KAAK,UAAY,GAEjB,KAAK,eAAe,QAAQ,MAAO,CAAE,OAAAA,CAAO,IAAM,CAChDA,EAAO,IAAIC,EAAyB,gBAAgB,CAAC,CACvD,CAAC,EAED,KAAK,eAAiB,CAAC,EAEvB,KAAK,MAAM,OAAS,CACtB,CASA,aAAuB,CACrB,OAAO,KAAK,SACd,CASA,sBAA+B,CAC7B,OAAO,KAAK,eAAe,MAC7B,CACF,EAUaA,EAAN,cAAuC,KAAM,CAAC", "names": ["AsyncQueue", "item", "resolve", "reject", "AsyncQueueCancelledError"]}