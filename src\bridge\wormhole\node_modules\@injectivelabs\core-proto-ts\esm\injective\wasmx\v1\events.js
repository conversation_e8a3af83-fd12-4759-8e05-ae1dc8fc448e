/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
import { fundingModeFromJSON, fundingModeToJSON } from "./proposal.js";
export const protobufPackage = "injective.wasmx.v1";
function createBaseEventContractExecution() {
    return { contractAddress: "", response: new Uint8Array(), otherError: "", executionError: "" };
}
export const EventContractExecution = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        if (message.response.length !== 0) {
            writer.uint32(18).bytes(message.response);
        }
        if (message.otherError !== "") {
            writer.uint32(26).string(message.otherError);
        }
        if (message.executionError !== "") {
            writer.uint32(34).string(message.executionError);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventContractExecution();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                case 2:
                    message.response = reader.bytes();
                    break;
                case 3:
                    message.otherError = reader.string();
                    break;
                case 4:
                    message.executionError = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            response: isSet(object.response) ? bytesFromBase64(object.response) : new Uint8Array(),
            otherError: isSet(object.otherError) ? String(object.otherError) : "",
            executionError: isSet(object.executionError) ? String(object.executionError) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.response !== undefined &&
            (obj.response = base64FromBytes(message.response !== undefined ? message.response : new Uint8Array()));
        message.otherError !== undefined && (obj.otherError = message.otherError);
        message.executionError !== undefined && (obj.executionError = message.executionError);
        return obj;
    },
    create(base) {
        return EventContractExecution.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventContractExecution();
        message.contractAddress = object.contractAddress ?? "";
        message.response = object.response ?? new Uint8Array();
        message.otherError = object.otherError ?? "";
        message.executionError = object.executionError ?? "";
        return message;
    },
};
function createBaseEventContractRegistered() {
    return {
        contractAddress: "",
        gasPrice: "0",
        shouldPinContract: false,
        isMigrationAllowed: false,
        codeId: "0",
        adminAddress: "",
        granterAddress: "",
        fundingMode: 0,
    };
}
export const EventContractRegistered = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        if (message.gasPrice !== "0") {
            writer.uint32(24).uint64(message.gasPrice);
        }
        if (message.shouldPinContract === true) {
            writer.uint32(32).bool(message.shouldPinContract);
        }
        if (message.isMigrationAllowed === true) {
            writer.uint32(40).bool(message.isMigrationAllowed);
        }
        if (message.codeId !== "0") {
            writer.uint32(48).uint64(message.codeId);
        }
        if (message.adminAddress !== "") {
            writer.uint32(58).string(message.adminAddress);
        }
        if (message.granterAddress !== "") {
            writer.uint32(66).string(message.granterAddress);
        }
        if (message.fundingMode !== 0) {
            writer.uint32(72).int32(message.fundingMode);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventContractRegistered();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                case 3:
                    message.gasPrice = longToString(reader.uint64());
                    break;
                case 4:
                    message.shouldPinContract = reader.bool();
                    break;
                case 5:
                    message.isMigrationAllowed = reader.bool();
                    break;
                case 6:
                    message.codeId = longToString(reader.uint64());
                    break;
                case 7:
                    message.adminAddress = reader.string();
                    break;
                case 8:
                    message.granterAddress = reader.string();
                    break;
                case 9:
                    message.fundingMode = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            gasPrice: isSet(object.gasPrice) ? String(object.gasPrice) : "0",
            shouldPinContract: isSet(object.shouldPinContract) ? Boolean(object.shouldPinContract) : false,
            isMigrationAllowed: isSet(object.isMigrationAllowed) ? Boolean(object.isMigrationAllowed) : false,
            codeId: isSet(object.codeId) ? String(object.codeId) : "0",
            adminAddress: isSet(object.adminAddress) ? String(object.adminAddress) : "",
            granterAddress: isSet(object.granterAddress) ? String(object.granterAddress) : "",
            fundingMode: isSet(object.fundingMode) ? fundingModeFromJSON(object.fundingMode) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.gasPrice !== undefined && (obj.gasPrice = message.gasPrice);
        message.shouldPinContract !== undefined && (obj.shouldPinContract = message.shouldPinContract);
        message.isMigrationAllowed !== undefined && (obj.isMigrationAllowed = message.isMigrationAllowed);
        message.codeId !== undefined && (obj.codeId = message.codeId);
        message.adminAddress !== undefined && (obj.adminAddress = message.adminAddress);
        message.granterAddress !== undefined && (obj.granterAddress = message.granterAddress);
        message.fundingMode !== undefined && (obj.fundingMode = fundingModeToJSON(message.fundingMode));
        return obj;
    },
    create(base) {
        return EventContractRegistered.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventContractRegistered();
        message.contractAddress = object.contractAddress ?? "";
        message.gasPrice = object.gasPrice ?? "0";
        message.shouldPinContract = object.shouldPinContract ?? false;
        message.isMigrationAllowed = object.isMigrationAllowed ?? false;
        message.codeId = object.codeId ?? "0";
        message.adminAddress = object.adminAddress ?? "";
        message.granterAddress = object.granterAddress ?? "";
        message.fundingMode = object.fundingMode ?? 0;
        return message;
    },
};
function createBaseEventContractDeregistered() {
    return { contractAddress: "" };
}
export const EventContractDeregistered = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventContractDeregistered();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create(base) {
        return EventContractDeregistered.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventContractDeregistered();
        message.contractAddress = object.contractAddress ?? "";
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
