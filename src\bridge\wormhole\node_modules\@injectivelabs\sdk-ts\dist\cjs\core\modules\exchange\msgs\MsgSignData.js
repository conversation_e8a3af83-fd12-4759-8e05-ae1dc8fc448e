"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const index_js_1 = require("../../../../utils/index.js");
/**
 * @category Messages
 */
class MsgSignData extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgSignData(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgSignData.create();
        message.Signer = Buffer.from((0, index_js_1.getEthereumAddress)(params.sender), 'hex');
        message.Data = Buffer.from((0, index_js_1.toUtf8)(params.data), 'utf-8');
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgSignData.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgSignData',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'sign/MsgSignData',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgSignData',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgSignData',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgSignData.encode(this.toProto()).finish();
    }
}
exports.default = MsgSignData;
