import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
import { CosmosDistributionV1Beta1Tx } from '@injectivelabs/core-proto-ts';
/**
 * @category Messages
 */
export default class MsgWithdrawDelegatorReward extends MsgBase {
    static fromJSON(params) {
        return new MsgWithdrawDelegatorReward(params);
    }
    toProto() {
        const { params } = this;
        const message = CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.create();
        message.delegatorAddress = params.delegatorAddress;
        message.validatorAddress = params.validatorAddress;
        return CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...snakecaseKeys(proto),
        };
        return {
            type: 'cosmos-sdk/MsgWithdrawDelegationReward',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            message: proto,
        };
    }
    toBinary() {
        return CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.encode(this.toProto()).finish();
    }
}
