import { ApolloClient, InMemoryCache, } from '@apollo/client';
import { HttpRequestException } from '@injectivelabs/exceptions';
import { USER_DEPOSITS, USER_BRIDGE_DEPOSITS } from './queries.js';
/**
 * @hidden
 */
export class ApolloConsumer {
    apolloClient;
    constructor(graphQlEndpoint) {
        this.apolloClient = new ApolloClient({
            uri: graphQlEndpoint,
            cache: new InMemoryCache(),
            defaultOptions: {
                query: {
                    fetchPolicy: 'no-cache',
                    errorPolicy: 'all',
                },
            },
        });
    }
    async fetchUserDeposits(address) {
        const response = (await this.apolloClient.query({
            query: USER_DEPOSITS,
            variables: {
                destination: `0x${'0'.repeat(24)}${address
                    .toLowerCase()
                    .replace('0x', '')}`,
            },
        }));
        if (response.errors && response.errors.length > 0) {
            throw new HttpRequestException(new Error(response.errors[0].message));
        }
        return response.data.deposits;
    }
    async fetchUserBridgeDeposits(address, timestamp) {
        const response = (await this.apolloClient.query({
            query: USER_BRIDGE_DEPOSITS,
            variables: {
                timestamp,
                sender: address,
            },
        }));
        if (response.errors && response.errors.length > 0) {
            throw new HttpRequestException(new Error(response.errors[0].message));
        }
        return response.data.deposits;
    }
}
