#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DEX交易本地模拟器 - 基于Uniswap V2/ArthSwap的恒定乘积做市商(CPMM)模型
"""

import os
import sys
from decimal import Decimal
from typing import Tuple, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.logger import logger

class LocalSwapSimulator:
    """本地交易模拟器"""
    
    # ArthSwap的手续费率为0.3%
    FEE_NUMERATOR = Decimal('997')
    FEE_DENOMINATOR = Decimal('1000')
    
    def __init__(self, token0: str, token1: str, pool_address: str = None, pool_name: str = ""):
        """
        初始化模拟器
        
        Args:
            token0: 第一个代币符号
            token1: 第二个代币符号
            pool_address: 池子合约地址，如果不指定则使用默认池子
            pool_name: 池子名称，用于日志显示
        """
        self.token0 = token0
        self.token1 = token1
        self.pool_address = pool_address
        self.pool_name = pool_name or "默认池子"
        self.reserve0: Optional[Decimal] = None
        self.reserve1: Optional[Decimal] = None
        self.last_update_timestamp: float = 0
    
    def _simulate_swap(self, amount_in: Decimal, reserve_in: Decimal, reserve_out: Decimal) -> Decimal:
        """
        使用恒定乘积公式模拟交易，完全匹配Router合约的计算方式
        
        Args:
            amount_in: 输入代币数量
            reserve_in: 输入代币池子储备量
            reserve_out: 输出代币池子储备量
            
        Returns:
            Decimal: 输出代币数量
        """
        if amount_in <= 0:
            raise ValueError('INSUFFICIENT_INPUT_AMOUNT')
        if reserve_in <= 0 or reserve_out <= 0:
            raise ValueError('INSUFFICIENT_LIQUIDITY')
            
        # 严格按照Router合约的计算方式：
        # amountOut = (amountIn * 997 * reserveOut) / (reserveIn * 1000 + amountIn * 997)
        numerator = amount_in * self.FEE_NUMERATOR * reserve_out
        denominator = reserve_in * self.FEE_DENOMINATOR + amount_in * self.FEE_NUMERATOR
        
        return numerator / denominator
    
    def update_reserves(self) -> bool:
        """
        从链上更新池子储备量
        
        Returns:
            bool: 更新是否成功
        """
        try:
            client = ArthSwapClient()
            # 获取池子储备量
            if self.pool_address:
                reserve0, reserve1 = client.get_pair_reserves_by_address(self.pool_address)
            else:
                reserve0, reserve1 = client.get_pair_reserves(self.token0, self.token1)
            
            # 获取代币精度
            decimals0 = client.get_token_decimals(self.token0)
            decimals1 = client.get_token_decimals(self.token1)
            
            # 转换为实际数量（考虑精度）
            self.reserve0 = Decimal(str(reserve0)) / Decimal(str(10 ** decimals0))
            self.reserve1 = Decimal(str(reserve1)) / Decimal(str(10 ** decimals1))
            
            logger.info(f"已更新{self.pool_name} ({self.token0}-{self.token1})池子储备量:")
            logger.info(f"  {self.token0}: {float(self.reserve0):.2f}")
            logger.info(f"  {self.token1}: {float(self.reserve1):.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新{self.pool_name}池子储备量失败: {e}")
            return False
    
    def simulate_swap_0_to_1(self, amount_in: Decimal) -> Optional[Decimal]:
        """
        模拟用token0换token1
        
        Args:
            amount_in: 输入token0数量
            
        Returns:
            Optional[Decimal]: 输出token1数量，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None:
            logger.error("池子储备量未初始化")
            return None
        
        try:
            return self._simulate_swap(amount_in, self.reserve0, self.reserve1)
        except Exception as e:
            logger.error(f"模拟交易失败: {e}")
            return None
    
    def simulate_swap_1_to_0(self, amount_in: Decimal) -> Optional[Decimal]:
        """
        模拟用token1换token0
        
        Args:
            amount_in: 输入token1数量
            
        Returns:
            Optional[Decimal]: 输出token0数量，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None:
            logger.error("池子储备量未初始化")
            return None
        
        try:
            return self._simulate_swap(amount_in, self.reserve1, self.reserve0)
        except Exception as e:
            logger.error(f"模拟交易失败: {e}")
            return None
    
    def get_reserves(self) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """
        获取当前池子储备量
        
        Returns:
            Tuple[Optional[Decimal], Optional[Decimal]]: (token0储备量, token1储备量)
        """
        return self.reserve0, self.reserve1
    
    def get_price_0_per_1(self) -> Optional[Decimal]:
        """
        获取token0相对于token1的价格
        
        Returns:
            Optional[Decimal]: token0/token1价格，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None or self.reserve1 == 0:
            return None
        return self.reserve0 / self.reserve1
    
    def get_price_1_per_0(self) -> Optional[Decimal]:
        """
        获取token1相对于token0的价格
        
        Returns:
            Optional[Decimal]: token1/token0价格，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None or self.reserve0 == 0:
            return None
        return self.reserve1 / self.reserve0

    def get_router_style_price(self, is_0_to_1: bool = True, amount: Decimal = Decimal('1.0')) -> Optional[Decimal]:
        """
        模拟Router合约的价格计算方式
        
        Args:
            is_0_to_1: True表示用token0换token1，False表示用token1换token0
            amount: 用于计算价格的输入金额，默认为1
            
        Returns:
            Optional[Decimal]: 考虑手续费和滑点后的价格，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None:
            logger.error("池子储备量未初始化")
            return None
            
        try:
            if is_0_to_1:
                # 模拟用token0换token1
                amount_out = self._simulate_swap(amount, self.reserve0, self.reserve1)
                return amount_out / amount if amount != 0 else None
            else:
                # 模拟用token1换token0
                amount_out = self._simulate_swap(amount, self.reserve1, self.reserve0)
                return amount_out / amount if amount != 0 else None
        except Exception as e:
            logger.error(f"计算Router风格价格失败: {e}")
            return None

    def get_spot_price(self, is_0_to_1: bool = True) -> Optional[Decimal]:
        """
        获取即时价格（不考虑滑点，但考虑手续费）
        
        Args:
            is_0_to_1: True表示token0对token1的价格，False表示token1对token0的价格
            
        Returns:
            Optional[Decimal]: 即时价格，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None:
            return None
            
        try:
            if is_0_to_1:
                # token0/token1价格
                return (self.reserve1 / self.reserve0) * (self.FEE_NUMERATOR / self.FEE_DENOMINATOR)
            else:
                # token1/token0价格
                return (self.reserve0 / self.reserve1) * (self.FEE_NUMERATOR / self.FEE_DENOMINATOR)
        except Exception as e:
            logger.error(f"计算即时价格失败: {e}")
            return None

    def get_exact_price(self, amount_in: Decimal, is_0_to_1: bool = True) -> Optional[Decimal]:
        """
        获取精确的交易价格（完全匹配Router合约计算）
        
        Args:
            amount_in: 输入金额
            is_0_to_1: True表示用token0换token1，False表示用token1换token0
            
        Returns:
            Optional[Decimal]: 交易价格，失败返回None
        """
        if self.reserve0 is None or self.reserve1 is None:
            return None
            
        try:
            if is_0_to_1:
                amount_out = self._simulate_swap(amount_in, self.reserve0, self.reserve1)
            else:
                amount_out = self._simulate_swap(amount_in, self.reserve1, self.reserve0)
            return amount_out / amount_in
        except Exception as e:
            logger.error(f"计算精确价格失败: {e}")
            return None

async def test_multiple_pools():
    """测试多个池子的模拟器功能"""
    # 定义要监控的池子
    pools = [
        {
            "name": "ArthSwap Pool 1",
            "address": "0x池子1地址",
            "tokens": ("USDT", "WASTR")
        },
        {
            "name": "ArthSwap Pool 2",
            "address": "0x池子2地址",
            "tokens": ("USDT", "WASTR")
        }
    ]
    
    # 创建多个池子的模拟器
    simulators = []
    for pool in pools:
        simulator = LocalSwapSimulator(
            token0=pool["tokens"][0],
            token1=pool["tokens"][1],
            pool_address=pool["address"],
            pool_name=pool["name"]
        )
        simulators.append(simulator)
        
        # 更新储备量
        if not simulator.update_reserves():
            continue
        
        # 获取价格
        price = simulator.get_price_1_per_0()
        if price:
            print(f"\n{pool['name']}当前价格:")
            print(f"{pool['tokens'][1]}/{pool['tokens'][0]}: {float(price):.6f}")
    
    # 测试套利机会
    if len(simulators) >= 2:
        sim1, sim2 = simulators[0], simulators[1]
        price1 = sim1.get_price_1_per_0()
        price2 = sim2.get_price_1_per_0()
        
        if price1 and price2:
            price_diff = abs(float(price1) - float(price2))
            price_diff_percent = price_diff / float(price1) * 100
            print(f"\n池子间价差: {price_diff_percent:.2f}%")

if __name__ == "__main__":
    import asyncio
    
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 运行多池测试
    asyncio.run(test_multiple_pools()) 