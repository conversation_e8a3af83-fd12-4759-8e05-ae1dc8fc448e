# web3-bzz

[![NPM Package][npm-image]][npm-url]

This is a sub-package of [web3.js][repo].

This is the swarm package.

Please read the [documentation][docs] for more.

## Installation

You can install the package either using [NPM](https://www.npmjs.com/package/web3-bzz) or using [Yarn](https://yarnpkg.com/package/web3-bzz)

### Using NPM

```bash
npm install web3-bzz
```

### Using Yarn

```bash
yarn add web3-bzz
```

## Usage

```js
const Web3Bzz = require('web3-bzz');

const bzz = new Web3Bzz('http://swarm-gateways.net');
```

## Types

All the TypeScript typings are placed in the `types` folder.

[docs]: http://web3js.readthedocs.io/en/1.0/
[repo]: https://github.com/ethereum/web3.js
[npm-image]: https://img.shields.io/npm/v/web3-bzz.svg
[npm-url]: https://npmjs.org/package/web3-bzz
