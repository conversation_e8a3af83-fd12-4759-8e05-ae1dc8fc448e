{"version": 3, "file": "interfaces.d.ts", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAA;AAE3E,MAAM,WAAW,WAAW;IAC1B,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B;;;;;OAKG;IACH,OAAO,EAAE;QACP,CAAC,GAAG,EAAE,MAAM,GAAG;YACb,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;YAClB,KAAK,EAAE,MAAM,CAAA;SACd,CAAA;KACF,CAAA;IACD;;;OAGG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAA;CACvB;AAED,oBAAY,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,aAAa,GAAG,UAAU,CAAC,CAAC,CAAA;AAEpG,oBAAY,YAAY,GAAG;IACzB,GAAG,EAAE,iBAAiB,CAAA;IACtB,KAAK,EAAE,iBAAiB,EAAE,CAAA;IAC1B,KAAK,EAAE,iBAAiB,CAAA;CACzB,CAAA;AAED,oBAAY,KAAK,GAAG;IAClB,OAAO,EAAE,iBAAiB,CAAA;IAC1B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,QAAQ,EAAE,iBAAiB,CAAA;IAC3B,KAAK,EAAE,iBAAiB,CAAA;IACxB,WAAW,EAAE,iBAAiB,CAAA;IAC9B,YAAY,EAAE,iBAAiB,EAAE,CAAA;IACjC,YAAY,EAAE,YAAY,EAAE,CAAA;CAC7B,CAAA;AAMD,oBAAY,cAAc,GAAG;IAC3B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,WAAW,EAAE,iBAAiB,EAAE,CAAA;CACjC,CAAA;AAKD,oBAAY,mBAAmB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,CAAA;AAC5D,oBAAY,eAAe,GAAG,mBAAmB,EAAE,CAAA;AACnD,oBAAY,UAAU,GAAG,cAAc,EAAE,CAAA;AAEzC;;GAEG;AACH,oBAAY,qBAAqB,GAAG;IAClC,OAAO,EAAE,iBAAiB,CAAA;IAC1B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,KAAK,EAAE,iBAAiB,EAAE,CAAA;IAC1B,OAAO,EAAE,iBAAiB,CAAA;IAC1B,CAAC,EAAE,iBAAiB,CAAA;IACpB,CAAC,EAAE,iBAAiB,CAAA;CACrB,CAAA;AAGD,oBAAY,0BAA0B,GAAG;IACvC,UAAU;IACV,UAAU;IACV,UAAU,EAAE;IACZ,UAAU;IACV,UAAU;IACV,UAAU;CACX,CAAA;AACD,oBAAY,sBAAsB,GAAG,0BAA0B,EAAE,CAAA;AACjE,oBAAY,iBAAiB,GAAG,qBAAqB,EAAE,CAAA;AAEvD;;;;GAIG;AACH,oBAAY,gBAAgB,GAAG;IAC7B,QAAQ,EAAE,OAAO,CAAA;IACjB,SAAS,EAAE,OAAO,CAAA;IAClB,SAAS,EAAE,OAAO,CAAA;IAClB,UAAU,EAAE,OAAO,CAAA;IACnB,SAAS,EAAE,OAAO,CAAA;CACnB,CAAA;AAED;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACrC,4BAA4B,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAAA;IACtD,yBAAyB,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAAA;IACnD,2BAA2B,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,GAAG,MAAM,CAAA;IACrE,gCAAgC,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAAA;IAC1D,qCAAqC,CAAC,OAAO,EAAE,OAAO,GAAG,MAAM,CAAA;IAC/D,0BAA0B,CAAC,MAAM,EAAE,OAAO,GAAG,MAAM,CAAA;IACnD,0BAA0B,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,EAAE;QAAE,UAAU,CAAC,EAAE,OAAO,CAAA;KAAE,GAAG,MAAM,CAAA;IAC7F,sCAAsC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;IAChG,uCAAuC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,CAAA;IACjG,gCAAgC,CAC9B,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,QAAQ,EAAE,MAAM,GAAG,UAAU,GAC5B,MAAM,CAAA;IACT,+BAA+B,CAC7B,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,QAAQ,EAAE,MAAM,GAAG,UAAU,GAC5B,MAAM,CAAA;IACT,wBAAwB,CACtB,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,QAAQ,EAAE,MAAM,GAAG,UAAU,EAC7B,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE,GACjC,MAAM,CAAA;IACT,YAAY,CACV,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,QAAQ,EAAE,MAAM,GAAG,UAAU,EAC7B,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,CAAA;KAAE,GACjC,gBAAgB,CAAA;IACnB,WAAW,IAAI,sBAAsB,CAAA;IACrC,KAAK,CAAC,aAAa,EAAE,sBAAsB,GAAG,IAAI,CAAA;CACnD;AAMD,MAAM,WAAW,qBAAqB;IACpC,UAAU,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,CAAA;IAC1D,UAAU,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9D,aAAa,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9C,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAClF,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACnE,eAAe,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;IACtD,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;IACvD,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;IAC1E,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACvF,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACrD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACvB,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IACvB,YAAY,IAAI,OAAO,CAAC,UAAU,CAAC,CAAA;IACnC,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACxE,QAAQ,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;IACvE,YAAY,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;IAChD,WAAW,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,qBAAqB,CAAA;IAC7D,aAAa,CAAC,CAAC,OAAO,EAAE,UAAU,GAAG,UAAU,CAAA;IAO/C,wBAAwB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;CACvF;AAED,MAAM,WAAW,wBAAyB,SAAQ,qBAAqB;IACrE,oBAAoB,EAAE;QACpB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAC3D,KAAK,IAAI,IAAI,CAAA;KACd,CAAA;IAED,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,WAAW,CAAC,CAAA;IACnD,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;IAC1F,wBAAwB,CAAC,SAAS,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACvD,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;IAEvE,WAAW,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,wBAAwB,CAAA;CACjE"}