import { OrderSide, OrderState } from '@injectivelabs/ts-types';
import { InjectiveSpotExchangeRpc } from '@injectivelabs/indexer-proto-ts';
import { TradeExecutionSide, TradeDirection, TradeExecutionType } from '../../../types/exchange.js';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcSpotApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveSpotExchangeRpc.InjectiveSpotExchangeRPCClientImpl;
    constructor(endpoint: string);
    fetchMarkets(params?: {
        baseDenom?: string;
        marketStatus?: string;
        quoteDenom?: string;
        marketStatuses?: string[];
    }): Promise<import("../types/spot.js").SpotMarket[]>;
    fetchMarket(marketId: string): Promise<import("../types/spot.js").SpotMarket>;
    /** @deprecated - use fetchOrderbookV2 */
    fetchOrderbook(_marketId: string): Promise<void>;
    fetchOrders(params?: {
        marketId?: string;
        marketIds?: string[];
        subaccountId?: string;
        orderSide?: OrderSide;
        isConditional?: boolean;
        pagination?: PaginationOption;
        cid?: string;
        tradeId?: string;
    }): Promise<{
        orders: import("../types/spot.js").SpotLimitOrder[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchOrderHistory(params?: {
        cid?: string;
        state?: OrderState;
        tradeId?: string;
        marketId?: string;
        marketIds?: string[];
        direction?: TradeDirection;
        orderTypes?: OrderSide[];
        pagination?: PaginationOption;
        isConditional?: boolean;
        executionTypes?: TradeExecutionType[];
        subaccountId?: string;
    }): Promise<{
        orderHistory: import("../types/spot.js").SpotOrderHistory[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchTrades(params?: {
        endTime?: number;
        tradeId?: string;
        marketId?: string;
        startTime?: number;
        marketIds?: string[];
        subaccountId?: string;
        accountAddress?: string;
        direction?: TradeDirection;
        pagination?: PaginationOption;
        executionSide?: TradeExecutionSide;
        executionTypes?: TradeExecutionType[];
        cid?: string;
    }): Promise<{
        trades: import("../types/spot.js").SpotTrade[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchSubaccountOrdersList(params?: {
        subaccountId?: string;
        marketId?: string;
        pagination?: PaginationOption;
    }): Promise<{
        orders: import("../types/spot.js").SpotLimitOrder[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchSubaccountTradesList(params?: {
        subaccountId?: string;
        marketId?: string;
        direction?: TradeDirection;
        executionType?: TradeExecutionType;
        pagination?: PaginationOption;
    }): Promise<import("../types/spot.js").SpotTrade[]>;
    /** @deprecated - use fetchOrderbooksV2 */
    fetchOrderbooks(_marketIds: string[]): Promise<void>;
    fetchOrderbooksV2(marketIds: string[]): Promise<{
        marketId: string;
        orderbook: import("../types/exchange.js").OrderbookWithSequence;
    }[]>;
    fetchOrderbookV2(marketId: string): Promise<import("../types/exchange.js").OrderbookWithSequence>;
    fetchAtomicSwapHistory(params: {
        address: string;
        contractAddress: string;
        pagination?: PaginationOption;
    }): Promise<{
        swapHistory: import("../types/spot.js").AtomicSwap[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
}
