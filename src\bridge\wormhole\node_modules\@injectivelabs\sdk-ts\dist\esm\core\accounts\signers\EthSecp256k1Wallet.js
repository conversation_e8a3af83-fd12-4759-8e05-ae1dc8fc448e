import { serializeSignDoc } from '@cosmjs/amino';
import { PrivateKey } from '../PrivateKey.js';
import { PublicKey } from '../PublicKey.js';
export class EthSecp256k1Wallet {
    /**
     * Creates a EthSecp256k1Wallet from the given private key
     *
     * @param privKey The private key.
     * @param prefix The bech32 address prefix (human readable part). Defaults to "inj".
     */
    static async fromKey(privKey, prefix = 'inj') {
        const publicKey = PrivateKey.fromHex(Buffer.from(privKey).toString('hex'))
            .toPublicKey()
            .toPubKeyBytes();
        return new EthSecp256k1Wallet(privKey, publicKey, prefix);
    }
    privateKey;
    publicKey;
    prefix;
    constructor(privKey, pubKey, prefix) {
        this.privateKey = PrivateKey.fromHex(Buffer.from(privKey).toString('hex'));
        this.publicKey = PublicKey.fromBytes(pubKey);
        this.prefix = prefix;
    }
    get address() {
        return this.publicKey.toAddress().toBech32(this.prefix);
    }
    async getAccounts() {
        return [
            {
                algo: 'eth_secp256k1',
                address: this.address,
                pubkey: this.publicKey.toPubKeyBytes(),
            },
        ];
    }
    async signAmino(signerAddress, signDoc) {
        if (signerAddress !== this.address) {
            throw new Error(`Address ${signerAddress} not found in wallet`);
        }
        const messageBytes = serializeSignDoc(signDoc);
        const signature = await this.privateKey.sign(Buffer.from(messageBytes));
        return {
            signed: signDoc,
            signature: {
                pub_key: {
                    type: 'tendermint/PubKeyEthSecp256k1',
                    value: this.publicKey.toBase64(),
                },
                signature: Buffer.from(signature).toString('base64'),
            },
        };
    }
}
