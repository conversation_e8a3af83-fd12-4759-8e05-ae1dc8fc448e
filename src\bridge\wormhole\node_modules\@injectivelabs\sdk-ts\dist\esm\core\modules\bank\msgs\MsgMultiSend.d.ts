import { MsgBase } from '../../MsgBase.js';
import { CosmosBankV1Beta1Tx, CosmosBankV1Beta1Bank, CosmosBaseV1Beta1Coin } from '@injectivelabs/core-proto-ts';
export declare namespace MsgMultiSend {
    interface Params {
        inputs: {
            address: string;
            coins: CosmosBaseV1Beta1Coin.Coin[];
        }[];
        outputs: {
            address: string;
            coins: CosmosBaseV1Beta1Coin.Coin[];
        }[];
    }
    type Proto = CosmosBankV1Beta1Tx.MsgMultiSend;
}
/**
 * @category Messages
 */
export default class MsgMultiSend extends MsgBase<MsgMultiSend.Proto, MsgMultiSend.Params> {
    static fromJSON(params: MsgMultiSend.Params): MsgMultiSend;
    toProto(): CosmosBankV1Beta1Tx.MsgMultiSend;
    toData(): {
        inputs: CosmosBankV1Beta1Bank.Input[];
        outputs: CosmosBankV1Beta1Bank.Output[];
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            inputs: CosmosBankV1Beta1Bank.Input[];
            outputs: CosmosBankV1Beta1Bank.Output[];
        };
    };
    toWeb3Gw(): {
        inputs: CosmosBankV1Beta1Bank.Input[];
        outputs: CosmosBankV1Beta1Bank.Output[];
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosBankV1Beta1Tx.MsgMultiSend;
    };
    toBinary(): Uint8Array;
}
