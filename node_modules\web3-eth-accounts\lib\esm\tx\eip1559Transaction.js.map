{"version": 3, "file": "eip1559Transaction.js", "sourceRoot": "", "sources": ["../../../src/tx/eip1559Transaction.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AAC5D,OAAO,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EACN,iBAAiB,EACjB,iBAAiB,EACjB,iBAAiB,EACjB,gBAAgB,GAChB,MAAM,YAAY,CAAC;AACpB,OAAO,EACN,WAAW,EACX,YAAY,EACZ,SAAS,EACT,kBAAkB,EAClB,0BAA0B,GAC1B,MAAM,oBAAoB,CAAC;AAW5B,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,2BAA2B,GAAG,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAE/F;;;;;GAKG;AACH,gDAAgD;AAChD,MAAM,OAAO,2BAA4B,SAAQ,eAA4C;IAiB5F;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAA8B,EAAE,OAAkB,EAAE;QAC5E,OAAO,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QAC1E,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CACd,sFAAsF,gBAAgB,eAAe,UAAU,CAC9H,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CACzB,EAAE,CACH,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAC/D,CAAC;QACD,iEAAiE;QACjE,OAAO,2BAA2B,CAAC,eAAe,CACjD,MAAqC,EACrC,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAmC,EAAE,OAAkB,EAAE;QACtF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CACd,uGAAuG,CACvG,CAAC;QACH,CAAC;QAED,MAAM,CACL,OAAO,EACP,KAAK,EACL,oBAAoB,EACpB,YAAY,EACZ,QAAQ,EACR,EAAE,EACF,KAAK,EACL,IAAI,EACJ,UAAU,EACV,CAAC,EACD,CAAC,EACD,CAAC,EACD,GAAG,MAAM,CAAC;QAEX,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACvC,uBAAuB,CAAC;YACvB,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,KAAK;YACL,CAAC;YACD,CAAC;YACD,CAAC;SACD,CAAC,CAAC;QAEH,OAAO,IAAI,2BAA2B,CACrC;YACC,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC;YACpC,KAAK;YACL,oBAAoB;YACpB,YAAY;YACZ,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE;YAC5B,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,uDAAuD;YAC/G,CAAC;YACD,CAAC;SACD,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,YAAmB,MAA8B,EAAE,OAAkB,EAAE;;QACtE,KAAK,iCAAM,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC,CAAC;QAnHpD;;;;;WAKG;QACO,qBAAgB,GAAG,QAAQ,CAAC;QA8GrC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,MAAM,CAAC;QAE3E,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAE7E,kCAAkC;QAClC,MAAM,cAAc,GAAG,iBAAiB,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACpD,iCAAiC;QACjC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAElC,IAAI,CAAC,YAAY,GAAG,kBAAkB,CACrC,YAAY,CAAC,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CACvD,CAAC;QACF,IAAI,CAAC,oBAAoB,GAAG,kBAAkB,CAC7C,YAAY,CAAC,oBAAoB,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CACvE,CAAC;QAEF,IAAI,CAAC,+BAA+B,CAAC;YACpC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;SAC/C,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,WAAW,EAAE,CAAC;YACrD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACzB,6DAA6D,CAC7D,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACnD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACzB,iGAAiG,CACjG,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAC;QACpC,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;IACF,CAAC;IAED;;OAEG;IACI,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YAClF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAEhE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACpB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aAChC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5C,MAAM,kBAAkB,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;QAC3D,MAAM,QAAQ,GAAG,kBAAkB,GAAG,OAAO,CAAC;QAC9C,OAAO,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,GAAG;QACT,OAAO;YACN,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC;YACxC,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,0BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC;YACrD,0BAA0B,CAAC,IAAI,CAAC,YAAY,CAAC;YAC7C,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;SAC/E,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,SAAS;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,OAAO,gBAAgB,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,MAAM,OAAO,GAAG,gBAAgB,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,WAAW,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,2BAA2B;QACjC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC;YACJ,OAAO,SAAS,CACf,OAAO,EACP,CAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACjE,0BAA0B,CAAC,CAAE,CAAC,EAC9B,0BAA0B,CAAC,CAAE,CAAC,CAC9B,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACF,CAAC;IAEM,iBAAiB,CAAC,CAAS,EAAE,CAAa,EAAE,CAAa;QAC/D,MAAM,IAAI,mCAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAC;QAExD,OAAO,2BAA2B,CAAC,UAAU,CAC5C;YACC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;YAC/C,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,sGAAsG;YACzH,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;SACxB,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM;QACZ,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE1D,OAAO;YACN,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,oBAAoB,EAAE,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC5D,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;YAC5C,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACd,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7C,QAAQ,IAAI,iBAAiB,IAAI,CAAC,YAAY,yBAAyB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACnG,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC9B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;IACtC,CAAC;CACD"}