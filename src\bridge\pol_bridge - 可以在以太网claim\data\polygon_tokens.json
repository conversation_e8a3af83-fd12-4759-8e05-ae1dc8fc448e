{"******************************************": {"symbol": "WPOL", "name": "Wrapped POL", "address": "******************************************"}, "******************************************": {"symbol": "SPONGE", "name": "Sponge", "address": "******************************************"}, "******************************************": {"symbol": "USDT", "name": "(PoS) Tether USD", "address": "******************************************"}, "******************************************": {"symbol": "TRUMP", "name": "<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "BRIL", "name": "Brilliantcrypto Token", "address": "******************************************"}, "******************************************": {"symbol": "EMT", "name": "Earthmeta", "address": "******************************************"}, "******************************************": {"symbol": "GMT", "name": "GreenMetaverseToken", "address": "******************************************"}, "******************************************": {"symbol": "TBP", "name": "TurboPepe", "address": "******************************************"}, "******************************************": {"symbol": "BET", "name": "BET", "address": "******************************************"}, "******************************************": {"symbol": "USDC", "name": "USD Coin (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "USDC", "name": "USD Coin", "address": "******************************************"}, "******************************************": {"symbol": "PolyDoge", "name": "PolyDoge", "address": "******************************************"}, "0x85f21ae1272e35ec84f4131e0d9075a774354715": {"symbol": "BRC", "name": "BlackRockcoin", "address": "0x85f21ae1272e35ec84f4131e0d9075a774354715"}, "0x66f364f908c662772f5b7ecd58488f372c584833": {"symbol": "MKF", "name": "MAKER FLIP", "address": "0x66f364f908c662772f5b7ecd58488f372c584833"}, "0x7a3574094848e6a0d58f128dacb26a1f14b08296": {"symbol": "<PERSON>t Coin", "name": "MUTC", "address": "0x7a3574094848e6a0d58f128dacb26a1f14b08296"}, "0x61299774020da444af134c82fa83e3810b309991": {"symbol": "RNDR", "name": "Render Token", "address": "0x61299774020da444af134c82fa83e3810b309991"}, "0x3f94618ad346f34f43e27f0cf46decbb0d396b1b": {"symbol": "FKR", "name": "Flicker", "address": "0x3f94618ad346f34f43e27f0cf46decbb0d396b1b"}, "0xe06bd4f5aac8d0aa337d13ec88db6defc6eaeefe": {"symbol": "IXT", "name": "PlanetIX", "address": "0xe06bd4f5aac8d0aa337d13ec88db6defc6eaeefe"}, "0x13646e0e2d768d31b75d1a1e375e3e17f18567f2": {"symbol": "NWS", "name": "Nodewaves", "address": "0x13646e0e2d768d31b75d1a1e375e3e17f18567f2"}, "0xee997788f625809332baabb3110bcf1ba7400824": {"symbol": "FELY", "name": "Fe<PERSON>syum", "address": "0xee997788f625809332baabb3110bcf1ba7400824"}, "0x204820b6e6feae805e376d2c6837446186e57981": {"symbol": "ROND", "name": "ROND Coin", "address": "0x204820b6e6feae805e376d2c6837446186e57981"}, "0xcc44674022a792794d219847362bb95c661937a9": {"symbol": "ROU", "name": "ROUTINE COIN", "address": "0xcc44674022a792794d219847362bb95c661937a9"}, "0xd0258a3fd00f38aa8090dfee343f10a9d4d30d3f": {"symbol": "VOXEL", "name": "VOXEL Token", "address": "0xd0258a3fd00f38aa8090dfee343f10a9d4d30d3f"}, "******************************************": {"symbol": "SFL", "name": "Sunflower Land", "address": "******************************************"}, "******************************************": {"symbol": "NITRO", "name": "<PERSON><PERSON> (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "LINK", "name": "ChainLink Token", "address": "******************************************"}, "******************************************": {"symbol": "WETH", "name": "Wrapped Ether", "address": "******************************************"}, "******************************************": {"symbol": "GCHe", "name": "GC Helium", "address": "******************************************"}, "******************************************": {"symbol": "MLC", "name": "MyLovelyCoin", "address": "******************************************"}, "******************************************": {"symbol": "MEE", "name": "MEE Governance Token", "address": "******************************************"}, "******************************************": {"symbol": "MOON", "name": "Moonflow", "address": "******************************************"}, "******************************************": {"symbol": "SUT", "name": "Super Useless Token", "address": "******************************************"}, "******************************************": {"symbol": "DAI", "name": "(PoS) Dai Stablecoin", "address": "******************************************"}, "******************************************": {"symbol": "FARM", "name": "CryptoFarmers", "address": "******************************************"}, "******************************************": {"symbol": "SMT", "name": "SMARTMALL TOKEN", "address": "******************************************"}, "******************************************": {"symbol": "MV", "name": "Met<PERSON>se (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "AAVE", "name": "Aave (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "DCN", "name": "Decentra Protocol Token", "address": "******************************************"}, "******************************************": {"symbol": "CRV", "name": "CRV (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "NAKA", "name": "Nakamoto.Games", "address": "******************************************"}, "******************************************": {"symbol": "GHST", "name": "Aavegotchi GHST Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "mXEN", "name": "XEN Crypto", "address": "******************************************"}, "******************************************": {"symbol": "XSGD", "name": "XSGD", "address": "******************************************"}, "******************************************": {"symbol": "BRLA", "name": "BRLA Token", "address": "******************************************"}, "******************************************": {"symbol": "GEOD", "name": "Geodnet Token", "address": "******************************************"}, "******************************************": {"symbol": "STG", "name": "StargateToken", "address": "******************************************"}, "******************************************": {"symbol": "NCT", "name": "Toucan Protocol: Nature Carbon Tonne", "address": "******************************************"}, "******************************************": {"symbol": "crvUSDBTCETH", "name": "Curve USD-BTC-ETH", "address": "******************************************"}, "******************************************": {"symbol": "xSPACE", "name": "xSpace Token", "address": "******************************************"}, "******************************************": {"symbol": "WBTC", "name": "(PoS) Wrapped BTC", "address": "******************************************"}, "******************************************": {"symbol": "DOGA", "name": "DOGAMI", "address": "******************************************"}, "******************************************": {"symbol": "VOLLAR", "name": "VDS", "address": "******************************************"}, "******************************************": {"symbol": "TEL", "name": "Telcoin (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "FIRE", "name": "Matr1x Fire Token", "address": "******************************************"}, "******************************************": {"symbol": "axlUSDC", "name": "Axelar Wrapped USDC", "address": "******************************************"}, "******************************************": {"symbol": "SAND", "name": "SAND", "address": "******************************************"}, "******************************************": {"symbol": "POLO", "name": "Pololinks", "address": "******************************************"}, "0x18ec0a6e18e5bc3784fdd3a3634b31245ab704f6": {"symbol": "EURe", "name": "Monerium EUR emoney", "address": "0x18ec0a6e18e5bc3784fdd3a3634b31245ab704f6"}, "0x53a7196eff4049d1049574ebb0f4a10376913a3d": {"symbol": "POLLY", "name": "POLLY", "address": "0x53a7196eff4049d1049574ebb0f4a10376913a3d"}, "0xb5c064f955d8e7f38fe0460c556a72987494ee17": {"symbol": "QUICK", "name": "QuickSwap", "address": "0xb5c064f955d8e7f38fe0460c556a72987494ee17"}, "0x1ba17c639bdaecd8dc4aac37df062d17ee43a1b8": {"symbol": "WIXS", "name": "Wrapped Ixs Token", "address": "0x1ba17c639bdaecd8dc4aac37df062d17ee43a1b8"}, "0x8505b9d2254a7ae468c0e9dd10ccea3a837aef5c": {"symbol": "COMP", "name": "(PoS) Compound", "address": "0x8505b9d2254a7ae468c0e9dd10ccea3a837aef5c"}, "0xe4feab21b42919c5c960ed2b4bdffc521e26881f": {"symbol": "MUT", "name": "MUT", "address": "0xe4feab21b42919c5c960ed2b4bdffc521e26881f"}, "0x610e6d7ea9c21782dafb7d89f792e2981880fc46": {"symbol": "REVIVE", "name": "Revive Token", "address": "0x610e6d7ea9c21782dafb7d89f792e2981880fc46"}, "0xb33eaad8d922b1083446dc23f610c2567fb5180f": {"symbol": "UNI", "name": "Uniswap (PoS)", "address": "0xb33eaad8d922b1083446dc23f610c2567fb5180f"}, "0xd93f7e271cb87c23aaa73edc008a79646d1f9912": {"symbol": "SOL", "name": "Wrapped SOL (Wormhole)", "address": "0xd93f7e271cb87c23aaa73edc008a79646d1f9912"}, "0xe9c21de62c5c5d0ceacce2762bf655afdceb7ab3": {"symbol": "AKRE", "name": "<PERSON><PERSON>", "address": "0xe9c21de62c5c5d0ceacce2762bf655afdceb7ab3"}, "0xd2e57e7019a8faea8b3e4a3738ee5b269975008a": {"symbol": "THAT", "name": "THAT", "address": "0xd2e57e7019a8faea8b3e4a3738ee5b269975008a"}, "******************************************": {"symbol": "CROWD", "name": "CrowdToken", "address": "******************************************"}, "******************************************": {"symbol": "WCHI", "name": "Wrapped CHI (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "LDO", "name": "Lido DAO Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "wstETH", "name": "Wrapped liquid staked Ether 2.0 (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "LRT", "name": "LandRocker", "address": "******************************************"}, "******************************************": {"symbol": "CTF", "name": "Crypto Trading Fund", "address": "******************************************"}, "******************************************": {"symbol": "QUICK", "name": "Quickswap", "address": "******************************************"}, "******************************************": {"symbol": "SPHERE", "name": "Sphere Finance", "address": "******************************************"}, "******************************************": {"symbol": "PAXG", "name": "Paxos Gold (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "USDC", "name": "USD Coin (Wormhole)", "address": "******************************************"}, "******************************************": {"symbol": "GGT", "name": "GO GAME TOKEN", "address": "******************************************"}, "******************************************": {"symbol": "UVM", "name": "Universe Meta", "address": "******************************************"}, "0x8e677ca17065ed74675bc27bcabadb7eef10a292": {"symbol": "RAIN", "name": "Rain Coin", "address": "0x8e677ca17065ed74675bc27bcabadb7eef10a292"}, "0xc708d6f2153933daa50b2d0758955be0a93a8fec": {"symbol": "fxVERSE", "name": "Verse (FXERC20)", "address": "0xc708d6f2153933daa50b2d0758955be0a93a8fec"}, "0xf2ae0038696774d65e67892c9d301c5f2cbbda58": {"symbol": "CXO", "name": "CargoX Token (PoS)", "address": "0xf2ae0038696774d65e67892c9d301c5f2cbbda58"}, "0xf2b028ed5977f136982fdfa429814cf19f09693f": {"symbol": "UNCN", "name": "Unseen", "address": "0xf2b028ed5977f136982fdfa429814cf19f09693f"}, "0x552f4d98f338fbbd3175ddf38ce1260f403bbba2": {"symbol": "MINX", "name": "Modern Innovation Network Token", "address": "0x552f4d98f338fbbd3175ddf38ce1260f403bbba2"}, "******************************************": {"symbol": "SUT", "name": "SUPER TRUST", "address": "******************************************"}, "******************************************": {"symbol": "DIMO", "name": "<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "GNS", "name": "Gains Network", "address": "******************************************"}, "******************************************": {"symbol": "WETH", "name": "Wrapped Ether (Wormhole)", "address": "******************************************"}, "******************************************": {"symbol": "AF", "name": "<PERSON><PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "FBX", "name": "FireBotToken", "address": "******************************************"}, "******************************************": {"symbol": "ORBS", "name": "Orbs (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "KLIMA", "name": "Klima DAO", "address": "******************************************"}, "******************************************": {"symbol": "BAL", "name": "Balancer (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "BLOK", "name": "BLOK", "address": "******************************************"}, "******************************************": {"symbol": "CPR", "name": "CIPHER", "address": "******************************************"}, "0x3a58a54c066fdc0f2d55fc9c89f0415c92ebf3c4": {"symbol": "stMATIC", "name": "Staked MATIC (PoS)", "address": "0x3a58a54c066fdc0f2d55fc9c89f0415c92ebf3c4"}, "0x8a16d4bf8a0a716017e8d2262c4ac32927797a2f": {"symbol": "VCNT", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address": "0x8a16d4bf8a0a716017e8d2262c4ac32927797a2f"}, "0x57666a2f80be60a9b648dd1293d3ac29ef56bfde": {"symbol": "LTSC", "name": "Letscoin", "address": "0x57666a2f80be60a9b648dd1293d3ac29ef56bfde"}, "0xa1428174f516f527fafdd146b883bb4428682737": {"symbol": "SUPER", "name": "SuperFarm (PoS)", "address": "0xa1428174f516f527fafdd146b883bb4428682737"}, "0x5fe2b58c013d7601147dcdd68c143a77499f5531": {"symbol": "GRT", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "0x5fe2b58c013d7601147dcdd68c143a77499f5531"}, "0x2760e46d9bb43dafcbecaad1f64b93207f9f0ed7": {"symbol": "MVX", "name": "Metavault Trade", "address": "0x2760e46d9bb43dafcbecaad1f64b93207f9f0ed7"}, "0x4ed141110f6eeeaba9a1df36d8c26f684d2475dc": {"symbol": "BRZ", "name": "BRZ Token", "address": "0x4ed141110f6eeeaba9a1df36d8c26f684d2475dc"}, "0xe238ecb42c424e877652ad82d8a939183a04c35f": {"symbol": "WIFI", "name": "WiFi Map", "address": "0xe238ecb42c424e877652ad82d8a939183a04c35f"}, "0x784665471bb8b945b57a76a9200b109ee214e789": {"symbol": "KC", "name": "Krasnalcoin", "address": "0x784665471bb8b945b57a76a9200b109ee214e789"}, "0xbac3368b5110f3a3dda8b5a0f7b66edb37c47afe": {"symbol": "AIPEPE", "name": "AI Pepe King", "address": "0xbac3368b5110f3a3dda8b5a0f7b66edb37c47afe"}, "0xe1b3eb06806601828976e491914e3de18b5d6b28": {"symbol": "ZERC", "name": "zkRace", "address": "0xe1b3eb06806601828976e491914e3de18b5d6b28"}, "0x45c32fa6df82ead1e2ef74d17b76547eddfaff89": {"symbol": "FRAX", "name": "Frax", "address": "0x45c32fa6df82ead1e2ef74d17b76547eddfaff89"}, "0x692ac1e363ae34b6b489148152b12e2785a3d8d6": {"symbol": "TRADE", "name": "Polytrade (PoS)", "address": "0x692ac1e363ae34b6b489148152b12e2785a3d8d6"}, "0xe0339c80ffde91f3e20494df88d4206d86024cdf": {"symbol": "ELON", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "0xe0339c80ffde91f3e20494df88d4206d86024cdf"}, "0x5f2f8818002dc64753daedf4a6cb2ccb757cd220": {"symbol": "WSDM", "name": "<PERSON><PERSON>", "address": "0x5f2f8818002dc64753daedf4a6cb2ccb757cd220"}, "0xfe712251173a2cd5f5be2b46bb528328ea3565e1": {"symbol": "MVI", "name": "Metaverse Index (PoS)", "address": "0xfe712251173a2cd5f5be2b46bb528328ea3565e1"}, "0xb7b31a6bc18e48888545ce79e83e06003be70930": {"symbol": "APE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PoS)", "address": "0xb7b31a6bc18e48888545ce79e83e06003be70930"}, "0x6985884c4392d348587b19cb9eaaf157f13271cd": {"symbol": "ZRO", "name": "LayerZero", "address": "0x6985884c4392d348587b19cb9eaaf157f13271cd"}, "0xa3f751662e282e83ec3cbc387d225ca56dd63d3a": {"symbol": "APEPE", "name": "Ape and Pepe", "address": "0xa3f751662e282e83ec3cbc387d225ca56dd63d3a"}, "0x2c89bbc92bd86f8075d1decc58c7f4e0107f286b": {"symbol": "AVAX", "name": "<PERSON><PERSON>", "address": "0x2c89bbc92bd86f8075d1decc58c7f4e0107f286b"}, "0x3c59798620e5fec0ae6df1a19c6454094572ab92": {"symbol": "MNW", "name": "Morpheus.Network (PoS)", "address": "0x3c59798620e5fec0ae6df1a19c6454094572ab92"}, "0xbfc70507384047aa74c29cdc8c5cb88d0f7213ac": {"symbol": "ALI", "name": "Artificial Liquid Intelligence Token", "address": "0xbfc70507384047aa74c29cdc8c5cb88d0f7213ac"}, "0x014799e52d82ab50287ae391c0f261821085945f": {"symbol": "GWF", "name": "Global Wealth Fund Token", "address": "0x014799e52d82ab50287ae391c0f261821085945f"}, "0xa1c57f48f0deb89f569dfbe6e2b7f46d33606fd4": {"symbol": "MANA", "name": "(PoS) Decentraland MANA", "address": "0xa1c57f48f0deb89f569dfbe6e2b7f46d33606fd4"}, "0x88548808840220390dd9e19eefafa442e8e75041": {"symbol": "<PERSON><PERSON>", "name": "VDDS", "address": "0x88548808840220390dd9e19eefafa442e8e75041"}, "0xe0b52e49357fd4daf2c15e02058dce6bc0057db4": {"symbol": "agEUR", "name": "agEUR", "address": "0xe0b52e49357fd4daf2c15e02058dce6bc0057db4"}, "0x8bc3ec2e7973e64be582a90b08cadd13457160fe": {"symbol": "SDG", "name": "ShadowGold", "address": "0x8bc3ec2e7973e64be582a90b08cadd13457160fe"}, "0x3d2bd0e15829aa5c362a4144fdf4a1112fa29b5c": {"symbol": "BONSAI", "name": "Bonsai <PERSON>", "address": "0x3d2bd0e15829aa5c362a4144fdf4a1112fa29b5c"}, "0x0b3f868e0be5597d5db7feb59e1cadbb0fdda50a": {"symbol": "SUSHI", "name": "SushiToken (PoS)", "address": "0x0b3f868e0be5597d5db7feb59e1cadbb0fdda50a"}, "0x34d4ab47bee066f361fa52d792e69ac7bd05ee23": {"symbol": "AURUM", "name": "RaiderAurum", "address": "0x34d4ab47bee066f361fa52d792e69ac7bd05ee23"}, "0x1379e8886a944d2d9d440b3d88df536aea08d9f3": {"symbol": "MYST", "name": "Mysterium (PoS)", "address": "0x1379e8886a944d2d9d440b3d88df536aea08d9f3"}, "0xfa68fb4628dff1028cfec22b4162fccd0d45efb6": {"symbol": "MaticX", "name": "Liquid Staking Matic (PoS)", "address": "0xfa68fb4628dff1028cfec22b4162fccd0d45efb6"}, "0x324165db0a8d41f2eebb38d68e75edd5f8f48963": {"symbol": "PTRN", "name": "PTRN smart contract v1.0", "address": "0x324165db0a8d41f2eebb38d68e75edd5f8f48963"}, "0x9ff62d1fc52a907b6dcba8077c2ddca6e6a9d3e1": {"symbol": "FORT", "name": "Forta", "address": "0x9ff62d1fc52a907b6dcba8077c2ddca6e6a9d3e1"}, "0x2f800db0fdb5223b3c3f354886d907a671414a7f": {"symbol": "BCT", "name": "Toucan Protocol: Base Carbon Tonne", "address": "0x2f800db0fdb5223b3c3f354886d907a671414a7f"}, "0x46777c76dbbe40fabb2aab99e33ce20058e76c59": {"symbol": "L3", "name": "Layer3", "address": "0x46777c76dbbe40fabb2aab99e33ce20058e76c59"}, "0x8287ef2a998ae10dec6203a6dc93d9f2bbdeebe0": {"symbol": "$OMNI", "name": "OMNISPHERE TOKEN", "address": "0x8287ef2a998ae10dec6203a6dc93d9f2bbdeebe0"}, "0x1b815d120b3ef02039ee11dc2d33de7aa4a8c603": {"symbol": "WOO", "name": "Wootrade Network (PoS)", "address": "0x1b815d120b3ef02039ee11dc2d33de7aa4a8c603"}, "0x2ab445c24c96db13383bb34678adae50c43b4baa": {"symbol": "CLAY", "name": "<PERSON>", "address": "0x2ab445c24c96db13383bb34678adae50c43b4baa"}, "0x463fae8f3c63af7c40e50df3ba28469bf9942f69": {"symbol": "fxSABAI", "name": "Sabai Ecover<PERSON> (FXERC20)", "address": "0x463fae8f3c63af7c40e50df3ba28469bf9942f69"}, "******************************************": {"symbol": "@G", "name": "G@POL", "address": "******************************************"}, "******************************************": {"symbol": "SG", "name": "SocialGood (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "CONE", "name": "BitCone", "address": "******************************************"}, "******************************************": {"symbol": "USDT", "name": "Tether USD (Wormhole)", "address": "******************************************"}, "******************************************": {"symbol": "EBC", "name": "EbcXCoin", "address": "******************************************"}, "******************************************": {"symbol": "MSQ", "name": "MSQUARE", "address": "******************************************"}, "******************************************": {"symbol": "DATA", "name": "Streamr", "address": "******************************************"}, "******************************************": {"symbol": "ETH2x-FLI-P", "name": "ETH 2x Flexible Leverage Index", "address": "******************************************"}, "******************************************": {"symbol": "WGhost", "name": "Wrapped Ghost", "address": "******************************************"}, "******************************************": {"symbol": "ECET", "name": "ECET Token", "address": "******************************************"}, "******************************************": {"symbol": "HEX", "name": "HEX (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "MESH", "name": "Meshswap Protocol", "address": "******************************************"}, "******************************************": {"symbol": "tBTC", "name": "Polygon tBTC v2", "address": "******************************************"}, "******************************************": {"symbol": "PYR", "name": "PYR Token", "address": "******************************************"}, "******************************************": {"symbol": "UPO", "name": "UpOnly", "address": "******************************************"}, "******************************************": {"symbol": "QDSN", "name": "QADSAN", "address": "******************************************"}, "******************************************": {"symbol": "CENTUS", "name": "Stable Cent", "address": "******************************************"}, "******************************************": {"symbol": "DMT", "name": "Dragon Master Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "KAS", "name": "<PERSON><PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "MAKERX", "name": "MAKERX", "address": "******************************************"}, "0x6ea4babf46afc7895ee20594b86fdcf74526c3ec": {"symbol": "OC", "name": "OxChange", "address": "0x6ea4babf46afc7895ee20594b86fdcf74526c3ec"}, "0xeeeeeb57642040be42185f49c52f7e9b38f8eeee": {"symbol": "ELK", "name": "Elk", "address": "0xeeeeeb57642040be42185f49c52f7e9b38f8eeee"}, "0xa3fa99a148fa48d14ed51d610c367c61876997f1": {"symbol": "miMATIC", "name": "miMATIC", "address": "0xa3fa99a148fa48d14ed51d610c367c61876997f1"}, "0x5f32abeebd3c2fac1e7459a27e1ae9f1c16cccca": {"symbol": "FAR", "name": "FARCANA", "address": "0x5f32abeebd3c2fac1e7459a27e1ae9f1c16cccca"}, "0x60ea918fc64360269da4efbda11d8fc6514617c6": {"symbol": "SUKU", "name": "SUKU (PoS)", "address": "0x60ea918fc64360269da4efbda11d8fc6514617c6"}, "0x4f604735c1cf31399c6e711d5962b2b3e0225ad3": {"symbol": "USDGLO", "name": "Glo Dollar", "address": "0x4f604735c1cf31399c6e711d5962b2b3e0225ad3"}, "0xdd75542611d57c4b6e68168b14c3591c539022ed": {"symbol": "pZCX", "name": "ZEN Exchange Token", "address": "0xdd75542611d57c4b6e68168b14c3591c539022ed"}, "0x6c0ab120dbd11ba701aff6748568311668f63fe0": {"symbol": "APW", "name": "AP<PERSON>ine <PERSON> (PoS)", "address": "0x6c0ab120dbd11ba701aff6748568311668f63fe0"}, "******************************************": {"symbol": "ZED", "name": "ZED RUN", "address": "******************************************"}, "******************************************": {"symbol": "CIOTX", "name": "Crosschain IOTX", "address": "******************************************"}, "******************************************": {"symbol": "DOLZ", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "AEG", "name": "AETHER GAMES", "address": "******************************************"}, "******************************************": {"symbol": "RNT", "name": "Reental Utility Token", "address": "******************************************"}, "******************************************": {"symbol": "DTEC", "name": "DTEC", "address": "******************************************"}, "******************************************": {"symbol": "JPYC", "name": "JPY Coin", "address": "******************************************"}, "******************************************": {"symbol": "CARR", "name": "Carnomaly", "address": "******************************************"}, "******************************************": {"symbol": "BANANA", "name": "Banana", "address": "******************************************"}, "******************************************": {"symbol": "FTW", "name": "Black Agnus", "address": "******************************************"}, "******************************************": {"symbol": "fxVRSW", "name": "Virtuswap <PERSON> (FXERC20)", "address": "******************************************"}, "0x87fcfbd3eae94524d5ef0c42d01f3dfc96142451": {"symbol": "CPLE", "name": "Carpool Life Economy", "address": "0x87fcfbd3eae94524d5ef0c42d01f3dfc96142451"}, "0x3b56a704c01d650147ade2b8cee594066b3f9421": {"symbol": "FYN", "name": "<PERSON><PERSON><PERSON>", "address": "0x3b56a704c01d650147ade2b8cee594066b3f9421"}, "0x8159462d255c1d24915cb51ec361f700174cd994": {"symbol": "B-stMATIC-Stable", "name": "Balancer stMATIC Stable Pool", "address": "0x8159462d255c1d24915cb51ec361f700174cd994"}, "0xd98e5bdbb77c1bfeac4dd6d2f4d598c9a6d0ab33": {"symbol": "99PC", "name": "99 Cent Plus Coin", "address": "0xd98e5bdbb77c1bfeac4dd6d2f4d598c9a6d0ab33"}, "0x7f792db54b0e580cdc755178443f0430cf799aca": {"symbol": "VOLT", "name": "Volt Inu", "address": "0x7f792db54b0e580cdc755178443f0430cf799aca"}, "0xecdcb5b88f8e3c15f95c720c51c71c9e2080525d": {"symbol": "WBNB", "name": "Wrapped BNB (Wormhole)", "address": "0xecdcb5b88f8e3c15f95c720c51c71c9e2080525d"}, "0x200c234721b5e549c3693ccc93cf191f90dc2af9": {"symbol": "METAL", "name": "METAL", "address": "0x200c234721b5e549c3693ccc93cf191f90dc2af9"}, "0x7c730d513646fcc7dcb74dbfe7e9decb0f455a1c": {"symbol": "MLC", "name": "myLegacy Coin", "address": "0x7c730d513646fcc7dcb74dbfe7e9decb0f455a1c"}, "0x62a872d9977db171d9e213a5dc2b782e72ca0033": {"symbol": "NEUY", "name": "NEUY (PoS)", "address": "0x62a872d9977db171d9e213a5dc2b782e72ca0033"}, "0xe9bc9ad74cca887aff32ba09a121b1256fc9f052": {"symbol": "PIG", "name": "Pigcoin", "address": "0xe9bc9ad74cca887aff32ba09a121b1256fc9f052"}, "0x7dff46370e9ea5f0bad3c4e29711ad50062ea7a4": {"symbol": "SOL", "name": "SOL", "address": "0x7dff46370e9ea5f0bad3c4e29711ad50062ea7a4"}, "0x78445485a8d5b3be765e3027bc336e3c272a23c9": {"symbol": "UBU", "name": "Ubuntu", "address": "0x78445485a8d5b3be765e3027bc336e3c272a23c9"}, "0xcbf4ab00b6aa19b4d5d29c7c3508b393a1c01fe3": {"symbol": "MegaDoge", "name": "MegaDoge.Org", "address": "0xcbf4ab00b6aa19b4d5d29c7c3508b393a1c01fe3"}, "0x613a489785c95afeb3b404cc41565ccff107b6e0": {"symbol": "RADIO", "name": "RadioShack Token", "address": "0x613a489785c95afeb3b404cc41565ccff107b6e0"}, "0x1adcef5c780d8895ac77e6ee9239b4b3ecb76da2": {"symbol": "TOTEM", "name": "TOTEM", "address": "0x1adcef5c780d8895ac77e6ee9239b4b3ecb76da2"}, "0x4455ef8b4b4a007a93daa12de63a47eeac700d9d": {"symbol": "KNIGHT", "name": "<PERSON>", "address": "0x4455ef8b4b4a007a93daa12de63a47eeac700d9d"}, "0x85955046df4668e1dd369d2de9f3aeb98dd2a369": {"symbol": "DPI", "name": "DefiPulse Index (PoS)", "address": "0x85955046df4668e1dd369d2de9f3aeb98dd2a369"}, "0x9876528a8ee19b58edb0ed26a86939834acf0688": {"symbol": "FLTK", "name": "Floin Token", "address": "0x9876528a8ee19b58edb0ed26a86939834acf0688"}, "0xad9f61563b104281b14322fec8b42eb67711bf68": {"symbol": "SNG", "name": "Synergy Land Token", "address": "0xad9f61563b104281b14322fec8b42eb67711bf68"}, "0xe111178a87a3bff0c8d18decba5798827539ae99": {"symbol": "EURS", "name": "STASIS EURS Token (PoS)", "address": "0xe111178a87a3bff0c8d18decba5798827539ae99"}, "0xa2c638b78783e9afe26a16ec8b11de54eb169360": {"symbol": "PIN", "name": "Pay it Now", "address": "0xa2c638b78783e9afe26a16ec8b11de54eb169360"}, "0x50b728d8d964fd00c2d0aad81718b71311fef68a": {"symbol": "SNX", "name": "Synthetix Network Token (PoS)", "address": "0x50b728d8d964fd00c2d0aad81718b71311fef68a"}, "0x0308a3a9c433256ad7ef24dbef9c49c8cb01300a": {"symbol": "GPO", "name": "GoldPesa Option", "address": "0x0308a3a9c433256ad7ef24dbef9c49c8cb01300a"}, "0x3a3df212b7aa91aa0402b9035b098891d276572b": {"symbol": "FISH", "name": "Fish", "address": "0x3a3df212b7aa91aa0402b9035b098891d276572b"}, "0xe77abb1e75d2913b2076dd16049992ffeaca5235": {"symbol": "<PERSON><PERSON>", "name": "Decentrawood", "address": "0xe77abb1e75d2913b2076dd16049992ffeaca5235"}, "0x0bd49815ea8e2682220bcb41524c0dd10ba71d41": {"symbol": "PYM", "name": "<PERSON><PERSON>", "address": "0x0bd49815ea8e2682220bcb41524c0dd10ba71d41"}, "0xc168e40227e4ebd8c1cae80f7a55a4f0e6d66c97": {"symbol": "DFYN", "name": "DFYN Token (PoS)", "address": "0xc168e40227e4ebd8c1cae80f7a55a4f0e6d66c97"}, "0x5c067c80c00ecd2345b05e83a3e758ef799c40b5": {"symbol": "BRL1", "name": "BRL1", "address": "0x5c067c80c00ecd2345b05e83a3e758ef799c40b5"}, "0xb0b195aefa3650a6908f15cdac7d92f8a5791b0b": {"symbol": "BOB", "name": "BOB", "address": "0xb0b195aefa3650a6908f15cdac7d92f8a5791b0b"}, "0x111111517e4929d3dcbdfa7cce55d30d4b6bc4d6": {"symbol": "ICHI", "name": "ICHI", "address": "0x111111517e4929d3dcbdfa7cce55d30d4b6bc4d6"}, "0xab670fdfb0060bdc6508b84a309ff41b56ccaf3f": {"symbol": "USDW", "name": "USDW", "address": "0xab670fdfb0060bdc6508b84a309ff41b56ccaf3f"}, "0xcd7361ac3307d1c5a46b63086a90742ff44c63b3": {"symbol": "RAIDER", "name": "RaiderToken", "address": "0xcd7361ac3307d1c5a46b63086a90742ff44c63b3"}, "0x8d60fb5886497851aac8c5195006ecf07647ba0d": {"symbol": "W3GG", "name": "W3GG Token", "address": "0x8d60fb5886497851aac8c5195006ecf07647ba0d"}, "0x90a9e2772d6b53c92ccbeaba6c31a02c22eac111": {"symbol": "ZHT", "name": "ZHT Token", "address": "0x90a9e2772d6b53c92ccbeaba6c31a02c22eac111"}, "0x0000000000000000000000000000000000000000": {"symbol": "MATIC", "name": "Polygon", "address": "0x0000000000000000000000000000000000000000"}, "0xd8767e36196e164b7a37d0e2c84c54d5e302368a": {"symbol": "ISN", "name": "Insignea", "address": "0xd8767e36196e164b7a37d0e2c84c54d5e302368a"}, "0xb58458c52b6511dc723d7d6f3be8c36d7383b4a8": {"symbol": "FANX", "name": "FrontFanz", "address": "0xb58458c52b6511dc723d7d6f3be8c36d7383b4a8"}, "0x3b7e1ce09afe2bb3a23919afb65a38e627cfbe97": {"symbol": "DST", "name": "Dragon Soul Token", "address": "0x3b7e1ce09afe2bb3a23919afb65a38e627cfbe97"}, "0x70c006878a5a50ed185ac4c87d837633923de296": {"symbol": "REVV", "name": "REVV", "address": "0x70c006878a5a50ed185ac4c87d837633923de296"}, "0xe0bceef36f3a6efdd5eebfacd591423f8549b9d5": {"symbol": "FACTR", "name": "Defactor (PoS)", "address": "0xe0bceef36f3a6efdd5eebfacd591423f8549b9d5"}, "0x3fb83a9a2c4408909c058b0bfe5b4823f54fafe2": {"symbol": "BCUT", "name": "bitsCrunch Token(PoS)", "address": "0x3fb83a9a2c4408909c058b0bfe5b4823f54fafe2"}, "0x6b2f1c1fcfa168a09007e8ae959f4de11bcfd722": {"symbol": "PNT", "name": "Ponos ant", "address": "0x6b2f1c1fcfa168a09007e8ae959f4de11bcfd722"}, "0xee546f831533a913848b72f36a9d5e437f63dbb9": {"symbol": "CCDAO", "name": "Cross-Chain DAO", "address": "0xee546f831533a913848b72f36a9d5e437f63dbb9"}, "0x576cf361711cd940cd9c397bb98c4c896cbd38de": {"symbol": "USDC", "name": "USD Coin (Wormhole)", "address": "0x576cf361711cd940cd9c397bb98c4c896cbd38de"}, "0x9b034262e0095210ab9ddec60199741a8a1fbfe7": {"symbol": "THREE", "name": "ThreeCoin", "address": "0x9b034262e0095210ab9ddec60199741a8a1fbfe7"}, "0x255707b70bf90aa112006e1b07b9aea6de021424": {"symbol": "TETU", "name": "TETU Reward Token", "address": "0x255707b70bf90aa112006e1b07b9aea6de021424"}, "0x11cd72f7a4b699c67f225ca8abb20bc9f8db90c7": {"symbol": "OSAK", "name": "Osaka Protocol", "address": "0x11cd72f7a4b699c67f225ca8abb20bc9f8db90c7"}, "0x282d8efce846a88b159800bd4130ad77443fa1a1": {"symbol": "mOCEAN", "name": "Ocean Token (PoS)", "address": "0x282d8efce846a88b159800bd4130ad77443fa1a1"}, "0x43c73b90e0c2a355784dcf0da12f477729b31e77": {"symbol": "SOIL", "name": "Soil", "address": "0x43c73b90e0c2a355784dcf0da12f477729b31e77"}, "0x3801c3b3b5c98f88a9c9005966aa96aa440b9afc": {"symbol": "GLTR", "name": "GAX Liquidity Token Reward", "address": "0x3801c3b3b5c98f88a9c9005966aa96aa440b9afc"}, "0x91f3b9366801c1fca6184c3bd99d5ab0c43a9033": {"symbol": "ICNX", "name": "IconX World", "address": "0x91f3b9366801c1fca6184c3bd99d5ab0c43a9033"}, "0xaa3717090cddc9b227e49d0d84a28ac0a996e6ff": {"symbol": "ASK", "name": "Permission Token", "address": "0xaa3717090cddc9b227e49d0d84a28ac0a996e6ff"}, "0x388d819724dd6d71760a38f00dc01d310d879771": {"symbol": "JM", "name": "JustMoney", "address": "0x388d819724dd6d71760a38f00dc01d310d879771"}, "******************************************": {"symbol": "JpLink", "name": "JpLinkCoin", "address": "******************************************"}, "******************************************": {"symbol": "PROS", "name": "Prospective", "address": "******************************************"}, "******************************************": {"symbol": "EFIT", "name": "FitnessEver", "address": "******************************************"}, "******************************************": {"symbol": "fxcbETH", "name": "Coinbase Wrapped Staked ETH (FXERC20)", "address": "******************************************"}, "******************************************": {"symbol": "MOCA", "name": "Museum of Crypto Art", "address": "******************************************"}, "******************************************": {"symbol": "FAN", "name": "FAN", "address": "******************************************"}, "******************************************": {"symbol": "UST", "name": "UST (Wormhole)", "address": "******************************************"}, "******************************************": {"symbol": "USDC/USDT/DAI", "name": "Balancer USDC/USDT/DAI", "address": "******************************************"}, "******************************************": {"symbol": "RIA", "name": "Calvaria: Duels of Eternity", "address": "******************************************"}, "******************************************": {"symbol": "dQUICK", "name": "Dragon QUICK", "address": "******************************************"}, "******************************************": {"symbol": "crvUSD", "name": "Curve.Fi USD Stablecoin(PoS)", "address": "******************************************"}, "0x8c92e38eca8210f4fcbf17f0951b198dd7668292": {"symbol": "DHT", "name": "dHedge DAO Token (PoS)", "address": "0x8c92e38eca8210f4fcbf17f0951b198dd7668292"}, "0xc03e6ad83de7c58c9166ff08d66b960d78e64105": {"symbol": "LAND", "name": "Landshare Token", "address": "0xc03e6ad83de7c58c9166ff08d66b960d78e64105"}, "0xfca466f2fa8e667a517c9c6cfa99cf985be5d9b1": {"symbol": "SPEPE", "name": "Saiyan PEPE", "address": "0xfca466f2fa8e667a517c9c6cfa99cf985be5d9b1"}, "0x6f7c932e7684666c9fd1d44527765433e01ff61d": {"symbol": "MKR", "name": "MAKER (PoS)", "address": "0x6f7c932e7684666c9fd1d44527765433e01ff61d"}, "0x598e49f01befeb1753737934a5b11fea9119c796": {"symbol": "ADS", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "0x598e49f01befeb1753737934a5b11fea9119c796"}, "0xdcb72ae4d5dc6ae274461d57e65db8d50d0a33ad": {"symbol": "RADAR", "name": "DappRadar", "address": "0xdcb72ae4d5dc6ae274461d57e65db8d50d0a33ad"}, "0x16eccfdbb4ee1a85a33f3a9b21175cd7ae753db4": {"symbol": "ROUTE (PoS)", "name": "Route", "address": "0x16eccfdbb4ee1a85a33f3a9b21175cd7ae753db4"}, "0xf50d05a1402d0adafa880d36050736f9f6ee7dee": {"symbol": "INST", "name": "Instadapp (PoS)", "address": "0xf50d05a1402d0adafa880d36050736f9f6ee7dee"}, "0xc2ff25dd99e467d2589b2c26edd270f220f14e47": {"symbol": "dEURO", "name": "DecentralizedEURO(PoS)", "address": "0xc2ff25dd99e467d2589b2c26edd270f220f14e47"}, "0x9b3b0703d392321ad24338ff1f846650437a43c9": {"symbol": "BOSON", "name": "<PERSON><PERSON> (PoS)", "address": "0x9b3b0703d392321ad24338ff1f846650437a43c9"}, "0xf9aeac9f339ae152b83d49b06ccb8ad07852a9e2": {"symbol": "GGL", "name": "GGL Company №1", "address": "0xf9aeac9f339ae152b83d49b06ccb8ad07852a9e2"}, "0x351251ae8781a5c47b3dffcc6c0f73a2199ce53a": {"symbol": "BTNM", "name": "Bitnium", "address": "0x351251ae8781a5c47b3dffcc6c0f73a2199ce53a"}, "0x7075cab6bcca06613e2d071bd918d1a0241379e2": {"symbol": "GFARM2", "name": "Gains V2", "address": "0x7075cab6bcca06613e2d071bd918d1a0241379e2"}, "0x1c954e8fe737f99f68fa1ccda3e51ebdb291948c": {"symbol": "KNC", "name": "Kyber Network Crystal v2 (PoS)", "address": "0x1c954e8fe737f99f68fa1ccda3e51ebdb291948c"}, "0x0c9c7712c83b3c70e7c5e11100d33d9401bdf9dd": {"symbol": "WOMBAT", "name": "Wombat", "address": "0x0c9c7712c83b3c70e7c5e11100d33d9401bdf9dd"}, "0xaa4fbc6809a8e1924520fc85282ac4c76a7671d7": {"symbol": "SOULS", "name": "Unfettered Ecosystem", "address": "0xaa4fbc6809a8e1924520fc85282ac4c76a7671d7"}, "0xab5f7a0e20b0d056aed4aa4528c78da45be7308b": {"symbol": "GBYTE", "name": "Imported GBYTE", "address": "0xab5f7a0e20b0d056aed4aa4528c78da45be7308b"}, "0x6396252377f54ad33cff9131708da075b21d9b88": {"symbol": "NFTBS", "name": "NFTBOOKS", "address": "0x6396252377f54ad33cff9131708da075b21d9b88"}, "0x70d59baa5ab360b2723dd561415bdbcd4435e1c4": {"symbol": "SPRING", "name": "Spring Token (PoS)", "address": "0x70d59baa5ab360b2723dd561415bdbcd4435e1c4"}, "0x00000000efe302beaa2b3e6e1b18d08d69a9012a": {"symbol": "AUSD", "name": "AUSD", "address": "0x00000000efe302beaa2b3e6e1b18d08d69a9012a"}, "******************************************": {"symbol": "CGG", "name": "ChainGuardians Governance Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "KOM", "name": "Kommunitas", "address": "******************************************"}, "******************************************": {"symbol": "USD+", "name": "USD+", "address": "******************************************"}, "******************************************": {"symbol": "ETHi", "name": "ETHical Finance", "address": "******************************************"}, "******************************************": {"symbol": "POLSTRIX", "name": "Polstrix <PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "COMBO", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "b01", "name": "b0rder1ess", "address": "******************************************"}, "******************************************": {"symbol": "TITAN", "name": "IRON Titanium Token", "address": "******************************************"}, "******************************************": {"symbol": "DFX", "name": "DFX Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "NYA", "name": "<PERSON><PERSON>", "address": "******************************************"}, "0xee7666aacaefaa6efeef62ea40176d3eb21953b9": {"symbol": "MCHC", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "address": "0xee7666aacaefaa6efeef62ea40176d3eb21953b9"}, "0x5dd1cbf142f4b896d18af835c1734363b0d50fb0": {"symbol": "NKT", "name": "<PERSON><PERSON>", "address": "0x5dd1cbf142f4b896d18af835c1734363b0d50fb0"}, "0x7bebd226154e865954a87650faefa8f485d36081": {"symbol": "ZIG", "name": "<PERSON>ig<PERSON><PERSON><PERSON>", "address": "0x7bebd226154e865954a87650faefa8f485d36081"}, "0xe26cda27c13f4f87cffc2f437c5900b27ebb5bbb": {"symbol": "RBLS", "name": "<PERSON>", "address": "0xe26cda27c13f4f87cffc2f437c5900b27ebb5bbb"}, "0xe5b49820e5a1063f6f4ddf851327b5e8b2301048": {"symbol": "Bonk", "name": "Bonk", "address": "0xe5b49820e5a1063f6f4ddf851327b5e8b2301048"}, "0xd0e9c8f5fae381459cf07ec506c1d2896e8b5df6": {"symbol": "IOEN", "name": "Internet of Energy Network", "address": "0xd0e9c8f5fae381459cf07ec506c1d2896e8b5df6"}, "0xe8d17b127ba8b9899a160d9a07b69bca8e08bfc6": {"symbol": "NSDX", "name": "NASDEX Token", "address": "0xe8d17b127ba8b9899a160d9a07b69bca8e08bfc6"}, "0x3c12f8829041bf99deaa2965014e01b750f87905": {"symbol": "TTAJ", "name": "TTAJ", "address": "0x3c12f8829041bf99deaa2965014e01b750f87905"}, "0x2b9e7ccdf0f4e5b24757c1e1a80e311e34cb10c7": {"symbol": "MASK", "name": "Mask Network (PoS)", "address": "0x2b9e7ccdf0f4e5b24757c1e1a80e311e34cb10c7"}, "0xd3144ff5f388d36c0a445686c08540296d8b209b": {"symbol": "WWD", "name": "Wolf Works DAO", "address": "0xd3144ff5f388d36c0a445686c08540296d8b209b"}, "0x14e5386f47466a463f85d151653e1736c0c50fc3": {"symbol": "RUM", "name": "RUM Pirates of The Arrland Token", "address": "0x14e5386f47466a463f85d151653e1736c0c50fc3"}, "0x874e178a2f3f3f9d34db862453cd756e7eab0381": {"symbol": "GFI", "name": "Gravity Finance", "address": "0x874e178a2f3f3f9d34db862453cd756e7eab0381"}, "0x5f0197ba06860dac7e31258bdf749f92b6a636d4": {"symbol": "1FLR", "name": "<PERSON><PERSON><PERSON>", "address": "0x5f0197ba06860dac7e31258bdf749f92b6a636d4"}, "0x59b5654a17ac44f3068b3882f298881433bb07ef": {"symbol": "CHP", "name": "CoinPoker Chips (PoS)", "address": "0x59b5654a17ac44f3068b3882f298881433bb07ef"}, "0x40379a439d4f6795b6fc9aa5687db461677a2dba": {"symbol": "USDR", "name": "Real USD", "address": "0x40379a439d4f6795b6fc9aa5687db461677a2dba"}, "0x1d3c629ca5c1d0ab3bdf74600e81b4145615df8e": {"symbol": "PRNT", "name": "Preprints.io Token", "address": "0x1d3c629ca5c1d0ab3bdf74600e81b4145615df8e"}, "0xb755506531786c8ac63b756bab1ac387bacb0c04": {"symbol": "ZARP", "name": "ZARP Stablecoin", "address": "0xb755506531786c8ac63b756bab1ac387bacb0c04"}, "0xbb4e25c1f0fbcb60a0d1245683241265f1f64f61": {"symbol": "CPAY", "name": "CIPHEREPAY", "address": "0xbb4e25c1f0fbcb60a0d1245683241265f1f64f61"}, "0x6ccb663dad597058da28b58974e8bdb81323dd09": {"symbol": "BLOC", "name": "BlockCentral Token", "address": "0x6ccb663dad597058da28b58974e8bdb81323dd09"}, "0xe4e0d3f2fe9fa8a18c8df296650fc1540a564dd6": {"symbol": "AGF", "name": "AGRIFI", "address": "0xe4e0d3f2fe9fa8a18c8df296650fc1540a564dd6"}, "0x9f95e17b2668afe01f8fbd157068b0a4405cc08d": {"symbol": "BULL", "name": "Bullieverse", "address": "0x9f95e17b2668afe01f8fbd157068b0a4405cc08d"}, "0x9c78ee466d6cb57a4d01fd887d2b5dfb2d46288f": {"symbol": "MUST", "name": "Must", "address": "0x9c78ee466d6cb57a4d01fd887d2b5dfb2d46288f"}, "0x8f9e8e833a69aa467e42c46cca640da84dd4585f": {"symbol": "CHAMP", "name": "NFT Champions", "address": "0x8f9e8e833a69aa467e42c46cca640da84dd4585f"}, "0x1236ea13c7339287cd00ab196aaa8217006b04dc": {"symbol": "oEPL", "name": "Orbit Bridge Polygon EpicLeague", "address": "0x1236ea13c7339287cd00ab196aaa8217006b04dc"}, "0x61bf130d973d59c69d3227f1668d534d83119860": {"symbol": "TRKX", "name": "Trakx Token", "address": "0x61bf130d973d59c69d3227f1668d534d83119860"}, "0x8c3441e7b9aa8a30a542dde048dd067de2802e9b": {"symbol": "WINK", "name": "WINK", "address": "0x8c3441e7b9aa8a30a542dde048dd067de2802e9b"}, "0x0ba8a6ce46d369d779299dedade864318097b703": {"symbol": "JUSD", "name": "JUSD Stable Token", "address": "0x0ba8a6ce46d369d779299dedade864318097b703"}, "0x65a05db8322701724c197af82c9cae41195b0aa8": {"symbol": "FOX", "name": "FOX (PoS)", "address": "0x65a05db8322701724c197af82c9cae41195b0aa8"}, "0x41b3966b4ff7b427969ddf5da3627d6aeae9a48e": {"symbol": "NEXO", "name": "Nexo (PoS)", "address": "0x41b3966b4ff7b427969ddf5da3627d6aeae9a48e"}, "0xba3cb8329d442e6f9eb70fafe1e214251df3d275": {"symbol": "SWASH", "name": "Swash Token", "address": "0xba3cb8329d442e6f9eb70fafe1e214251df3d275"}, "0x127e47aba094a9a87d084a3a93732909ff031419": {"symbol": "GNUS", "name": "Genius Token & NFT Collections", "address": "0x127e47aba094a9a87d084a3a93732909ff031419"}, "0x5ceebb0947d58fabde2fc026ffe4b33ccfe1ba8b": {"symbol": "4INT", "name": "4INT", "address": "0x5ceebb0947d58fabde2fc026ffe4b33ccfe1ba8b"}, "0xc8bb8eda94931ca2f20ef43ea7dbd58e68400400": {"symbol": "VNXAU", "name": "VNX Gold", "address": "0xc8bb8eda94931ca2f20ef43ea7dbd58e68400400"}, "0x692597b009d13c4049a947cab2239b7d6517875f": {"symbol": "UST", "name": "Wrapped UST Token (PoS)", "address": "0x692597b009d13c4049a947cab2239b7d6517875f"}, "0x554cd6bdd03214b10aafa3e0d4d42de0c5d2937b": {"symbol": "IDRT", "name": "<PERSON><PERSON><PERSON>", "address": "0x554cd6bdd03214b10aafa3e0d4d42de0c5d2937b"}, "0x6899face15c14348e1759371049ab64a3a06bfa6": {"symbol": "SDEX", "name": "SmarDex Token", "address": "0x6899face15c14348e1759371049ab64a3a06bfa6"}, "0x67eb41a14c0fe5cd701fc9d5a3d6597a72f641a6": {"symbol": "GIDDY", "name": "<PERSON><PERSON><PERSON>", "address": "0x67eb41a14c0fe5cd701fc9d5a3d6597a72f641a6"}, "0xdc4f4ed9872571d5ec8986a502a0d88f3a175f1e": {"symbol": "$DEZ", "name": "DEZ", "address": "0xdc4f4ed9872571d5ec8986a502a0d88f3a175f1e"}, "0xd85d1e945766fea5eda9103f918bd915fbca63e6": {"symbol": "CEL", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "0xd85d1e945766fea5eda9103f918bd915fbca63e6"}, "0xb6c3c00d730acca326db40e418353f04f7444e2b": {"symbol": "fcc", "name": "first choice coin", "address": "0xb6c3c00d730acca326db40e418353f04f7444e2b"}, "0x840195888db4d6a99ed9f73fcd3b225bb3cb1a79": {"symbol": "SX", "name": "SportX (PoS)", "address": "0x840195888db4d6a99ed9f73fcd3b225bb3cb1a79"}, "0xca154cf88f6ffbc23e16b5d08a9bf4851fb97199": {"symbol": "RISY", "name": "Risy DAO", "address": "0xca154cf88f6ffbc23e16b5d08a9bf4851fb97199"}, "0xc84902f9f90ffbe4757cd9b770af2873103cfba4": {"symbol": "STR", "name": "<PERSON><PERSON>", "address": "0xc84902f9f90ffbe4757cd9b770af2873103cfba4"}, "0xfdcc3dd6671eab0709a4c0f3f53de9a333d80798": {"symbol": "SBC", "name": "Stable Coin", "address": "0xfdcc3dd6671eab0709a4c0f3f53de9a333d80798"}, "0x4e3decbb3645551b8a19f0ea1678079fcb33fb4c": {"symbol": "jEUR", "name": "Jarvis Synthetic Euro", "address": "0x4e3decbb3645551b8a19f0ea1678079fcb33fb4c"}, "0x1d734a02ef1e1f5886e66b0673b71af5b53ffa94": {"symbol": "SD", "name": "<PERSON><PERSON> (PoS)", "address": "0x1d734a02ef1e1f5886e66b0673b71af5b53ffa94"}, "0x8343091f2499fd4b6174a46d067a920a3b851ff9": {"symbol": "jJPY", "name": "Jarvis Synthetic Japanese Yen", "address": "0x8343091f2499fd4b6174a46d067a920a3b851ff9"}, "0x23001ae6cd2f0644c5846e156565998334fc2be3": {"symbol": "ABYS", "name": "Abyss Fragment", "address": "0x23001ae6cd2f0644c5846e156565998334fc2be3"}, "0x580a84c73811e1839f75d86d75d88cca0c241ff4": {"symbol": "QI", "name": "<PERSON>", "address": "0x580a84c73811e1839f75d86d75d88cca0c241ff4"}, "0x0e9b89007eee9c958c0eda24ef70723c2c93dd58": {"symbol": "aMATICc", "name": "Ankr MATIC Reward Bearing Certif", "address": "0x0e9b89007eee9c958c0eda24ef70723c2c93dd58"}, "0x76e63a3e7ba1e2e61d3da86a87479f983de89a7e": {"symbol": "OMEN", "name": "Augury Finance", "address": "0x76e63a3e7ba1e2e61d3da86a87479f983de89a7e"}, "0x51c065005b11e4bb1d9ea7831d45f65e0fbb7351": {"symbol": "TT", "name": "TT", "address": "0x51c065005b11e4bb1d9ea7831d45f65e0fbb7351"}, "0x110b25d2b21ee73eb401f3ae7833f7072912a0bf": {"symbol": "LIF3", "name": "LIF3", "address": "0x110b25d2b21ee73eb401f3ae7833f7072912a0bf"}, "0x1e37b3855ca1ef46106baa162bfaf8a1e7666a5d": {"symbol": "TSLT", "name": "<PERSON><PERSON>", "address": "0x1e37b3855ca1ef46106baa162bfaf8a1e7666a5d"}, "0xf1ccf7f6aa6e5cf141de54351e8e30a618945530": {"symbol": "DOMME", "name": "<PERSON><PERSON>", "address": "0xf1ccf7f6aa6e5cf141de54351e8e30a618945530"}, "0x33b6d77c607ea499ab5db7e2201c5a516a78a5db": {"symbol": "AIMX", "name": "<PERSON><PERSON><PERSON>", "address": "0x33b6d77c607ea499ab5db7e2201c5a516a78a5db"}, "******************************************": {"symbol": "SPX", "name": "S&P 500", "address": "******************************************"}, "******************************************": {"symbol": "HOM", "name": "Homeety", "address": "******************************************"}, "******************************************": {"symbol": "WFLOP", "name": "Wrapped Flopcoin", "address": "******************************************"}, "******************************************": {"symbol": "ECLD", "name": "Ethernity CLOUD", "address": "******************************************"}, "******************************************": {"symbol": "NEX", "name": "Nash Exchange Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "LSC", "name": "Lifestyle Coin", "address": "******************************************"}, "******************************************": {"symbol": "RMV", "name": "RealityMetaverse(PoS)", "address": "******************************************"}, "******************************************": {"symbol": "POT", "name": "PotCoin.com POT", "address": "******************************************"}, "******************************************": {"symbol": "MART", "name": "ArtMeta", "address": "******************************************"}, "******************************************": {"symbol": "WINTER", "name": "Winter Token (PoS)", "address": "******************************************"}, "0x709a4b6217584188ddb93c82f5d716d969acce1c": {"symbol": "HANU", "name": "<PERSON><PERSON> (PoS)", "address": "0x709a4b6217584188ddb93c82f5d716d969acce1c"}, "0xdbb5da27ffcfebea8799a5832d4607714fc6aba8": {"symbol": "DGEN", "name": "DEGEN (PoS)", "address": "0xdbb5da27ffcfebea8799a5832d4607714fc6aba8"}, "******************************************": {"symbol": "BNL", "name": "BNLCoin", "address": "******************************************"}, "******************************************": {"symbol": "NEB", "name": "Nebulium", "address": "******************************************"}, "******************************************": {"symbol": "PAR", "name": "PAR Stablecoin", "address": "******************************************"}, "******************************************": {"symbol": "EURT", "name": "Euro Tether (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "MMF", "name": "Mad <PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "FLAME", "name": "FireStarter", "address": "******************************************"}, "******************************************": {"symbol": "SMT", "name": "Swarm Markets", "address": "******************************************"}, "******************************************": {"symbol": "GIC", "name": "GIC", "address": "******************************************"}, "******************************************": {"symbol": "JIO", "name": "JIO COIN", "address": "******************************************"}, "******************************************": {"symbol": "$ZKP", "name": "$ZKP Token", "address": "******************************************"}, "******************************************": {"symbol": "PLR", "name": "PILLAR (PoS)", "address": "******************************************"}, "0xc3ec80343d2bae2f8e680fdadde7c17e71e114ea": {"symbol": "OM", "name": "MANTRA DAO (PoS)", "address": "0xc3ec80343d2bae2f8e680fdadde7c17e71e114ea"}, "0x7abe9edf5c544a04da83e9110cf46dbc4759170c": {"symbol": "WPAY", "name": "WPAY", "address": "0x7abe9edf5c544a04da83e9110cf46dbc4759170c"}, "0x5d066d022ede10efa2717ed3d79f22f949f8c175": {"symbol": "CASH", "name": "CASH", "address": "0x5d066d022ede10efa2717ed3d79f22f949f8c175"}, "0x2ad2934d5bfb7912304754479dd1f096d5c807da": {"symbol": "AGC", "name": "Argocoin", "address": "0x2ad2934d5bfb7912304754479dd1f096d5c807da"}, "0x444444444444c1a66f394025ac839a535246fcc8": {"symbol": "GENI", "name": "<PERSON><PERSON>", "address": "0x444444444444c1a66f394025ac839a535246fcc8"}, "0x081ec4c0e30159c8259bad8f4887f83010a681dc": {"symbol": "DE", "name": "DeNet File Token", "address": "0x081ec4c0e30159c8259bad8f4887f83010a681dc"}, "0xd9a1070f832cc02ff9d839500eef6e8c64983726": {"symbol": "BAT", "name": "BATIC", "address": "0xd9a1070f832cc02ff9d839500eef6e8c64983726"}, "******************************************": {"symbol": "ALPHA", "name": "Aavegotchi ALPHA", "address": "******************************************"}, "******************************************": {"symbol": "BRAI", "name": "BRAI", "address": "******************************************"}, "******************************************": {"symbol": "BCMC", "name": "Blockchain Monster Coin", "address": "******************************************"}, "******************************************": {"symbol": "CRMS", "name": "Cryptomus", "address": "******************************************"}, "******************************************": {"symbol": "PLOT", "name": "PlotX", "address": "******************************************"}, "******************************************": {"symbol": "SWAP", "name": "TrustSwap Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "BLES", "name": "Blind Boxes Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "DUBI", "name": "Decentralized Universal Basic Income", "address": "******************************************"}, "******************************************": {"symbol": "INVS", "name": "Invistech", "address": "******************************************"}, "******************************************": {"symbol": "VEXT", "name": "Veloce", "address": "******************************************"}, "0xaeb3dd897ade187b9f9e4c491bc7a81f69f7093e": {"symbol": "EARNM", "name": "EARNM", "address": "0xaeb3dd897ade187b9f9e4c491bc7a81f69f7093e"}, "0x072c6b52cd916a7847facc49f7887b2c3511b4ab": {"symbol": "WAGMPEPE", "name": "We all gonna make PEPE", "address": "0x072c6b52cd916a7847facc49f7887b2c3511b4ab"}, "0x428360b02c1269bc1c79fbc399ad31d58c1e8fda": {"symbol": "DEFIT", "name": "Digital Fitness", "address": "0x428360b02c1269bc1c79fbc399ad31d58c1e8fda"}, "0x9c2c5fd7b07e95ee044ddeba0e97a665f142394f": {"symbol": "1INCH", "name": "1Inch (PoS)", "address": "0x9c2c5fd7b07e95ee044ddeba0e97a665f142394f"}, "0x109ca744ae9203441586dae1a332af815856d8b6": {"symbol": "9x", "name": "www.999x.fun", "address": "0x109ca744ae9203441586dae1a332af815856d8b6"}, "0x3c69d114664d48357d820dbdd121a8071eac99bf": {"symbol": "GALAXIS", "name": "GALAXIS Token(PoS)", "address": "0x3c69d114664d48357d820dbdd121a8071eac99bf"}, "0xfba4d30e964e40775c95b58acf6b5a621b929c0a": {"symbol": "AUTUMN", "name": "Autumn Token (PoS)", "address": "0xfba4d30e964e40775c95b58acf6b5a621b929c0a"}, "0xd566c529b33ecf15170f600d4b1ab12468c8efc6": {"symbol": "UERII", "name": "UERII (PoS)", "address": "0xd566c529b33ecf15170f600d4b1ab12468c8efc6"}, "0xaa9654becca45b5bdfa5ac646c939c62b527d394": {"symbol": "DINO", "name": "DinoS<PERSON>p (PoS)", "address": "0xaa9654becca45b5bdfa5ac646c939c62b527d394"}, "0x6f8a06447ff6fcf75d803135a7de15ce88c1d4ec": {"symbol": "SHIB", "name": "SHIBA INU (PoS)", "address": "0x6f8a06447ff6fcf75d803135a7de15ce88c1d4ec"}, "0xfe049f59963545bf5469f968e04c9646d6e2c2c5": {"symbol": "BC", "name": "Blood Crystal (PoS)", "address": "0xfe049f59963545bf5469f968e04c9646d6e2c2c5"}, "0x41084fdc569099d9e527ccf126e12d9c7c688ec3": {"symbol": "IQT", "name": "IQ Protocol Token", "address": "0x41084fdc569099d9e527ccf126e12d9c7c688ec3"}, "0x202655af326de310491cb54f120e02ee0da92b55": {"symbol": "CRETA", "name": "CRETA TOKEN", "address": "0x202655af326de310491cb54f120e02ee0da92b55"}, "0x71544ebf5d68d8d397059f79408162f155367d74": {"symbol": "HOTOK", "name": "HOTOK", "address": "0x71544ebf5d68d8d397059f79408162f155367d74"}, "0xd13cfd3133239a3c73a9e535a5c4dadee36b395c": {"symbol": "VAI", "name": "VAIOT Token", "address": "0xd13cfd3133239a3c73a9e535a5c4dadee36b395c"}, "******************************************": {"symbol": "MIMO", "name": "MIMO Parallel Governance Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "DFX", "name": "DFX Token (L2)", "address": "******************************************"}, "******************************************": {"symbol": "MEED", "name": "<PERSON><PERSON> Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "GoC", "name": "GoCrypto", "address": "******************************************"}, "******************************************": {"symbol": "FOMO", "name": "Aavegotchi FOMO", "address": "******************************************"}, "******************************************": {"symbol": "EMON", "name": "EthermonToken", "address": "******************************************"}, "******************************************": {"symbol": "UBT", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "$ACTIVE", "name": "Active Token", "address": "******************************************"}, "******************************************": {"symbol": "DDD", "name": "Durgan Dynasty Doubloon", "address": "******************************************"}, "******************************************": {"symbol": "wBAN", "name": "Wrapped <PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "PMOON", "name": "Polmoon", "address": "******************************************"}, "******************************************": {"symbol": "QUICK", "name": "Quick Drop", "address": "******************************************"}, "0xb631937b9e75a66291e7570e8ed3db10eb43a888": {"symbol": "BUCKET", "name": "BUCKET", "address": "0xb631937b9e75a66291e7570e8ed3db10eb43a888"}, "0x22cf06d60f7491adb55c5e1ed28eb79ac567cc24": {"symbol": "CL", "name": "Change Life Token", "address": "0x22cf06d60f7491adb55c5e1ed28eb79ac567cc24"}, "0x15cb250a1c76e169bea293a7b87fce743f12dab8": {"symbol": "DEFY", "name": "Defy", "address": "0x15cb250a1c76e169bea293a7b87fce743f12dab8"}, "0x64f6f111e9fdb753877f17f399b759de97379170": {"symbol": "EGP", "name": "<PERSON>ven <PERSON>", "address": "0x64f6f111e9fdb753877f17f399b759de97379170"}, "******************************************": {"symbol": "GET", "name": "GET Protocol (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "POLO", "name": "Pololinks", "address": "******************************************"}, "******************************************": {"symbol": "EP", "name": "Elemental Particles", "address": "******************************************"}, "******************************************": {"symbol": "AMKT", "name": "Alongside Crypto Market Index (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "ARTH", "name": "ARTH Valuecoin", "address": "******************************************"}, "******************************************": {"symbol": "KAVI", "name": "KAVI", "address": "******************************************"}, "******************************************": {"symbol": "LFT", "name": "Lifti", "address": "******************************************"}, "******************************************": {"symbol": "RETRO", "name": "RETRO", "address": "******************************************"}, "******************************************": {"symbol": "GONE", "name": "GONE", "address": "******************************************"}, "******************************************": {"symbol": "WETH", "name": "Wrapped Ether", "address": "******************************************"}, "******************************************": {"symbol": "SPORK", "name": "The SporkDAO Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "FCAR", "name": "FairCarats", "address": "******************************************"}, "******************************************": {"symbol": "KASTA", "name": "KastaToken", "address": "******************************************"}, "******************************************": {"symbol": "FID", "name": "<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "BXC", "name": "Bloxies Coin", "address": "******************************************"}, "******************************************": {"symbol": "GFC", "name": "GCOIN", "address": "******************************************"}, "******************************************": {"symbol": "MOONED", "name": "<PERSON><PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "POLTRA", "name": "Polygon's Trading BOT", "address": "******************************************"}, "******************************************": {"symbol": "B-stETH-BPT", "name": "Balancer wstETH StablePool", "address": "******************************************"}, "******************************************": {"symbol": "FAI", "name": "Fortune AI Agent", "address": "******************************************"}, "******************************************": {"symbol": "CRO", "name": "CRO (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "DYDX", "name": "dYdX (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "GAIA", "name": "GAIA Everworld", "address": "******************************************"}, "******************************************": {"symbol": "KEK", "name": "Aavegotchi KEK", "address": "******************************************"}, "******************************************": {"symbol": "XIDR", "name": "XIDR", "address": "******************************************"}, "******************************************": {"symbol": "VDA", "name": "Verida", "address": "******************************************"}, "0x189586b5f6317538ae50c20a976597da38984a24": {"symbol": "PORTX", "name": "ChainPort Token [via ChainPort.io]", "address": "0x189586b5f6317538ae50c20a976597da38984a24"}, "0x972999c58bbce63a2e398d4ed3bde414b8349eb3": {"symbol": "PRPS", "name": "Purpose", "address": "0x972999c58bbce63a2e398d4ed3bde414b8349eb3"}, "0xdd875635231e68e846ce190b1396ac0295d9e577": {"symbol": "uWatt", "name": "Uner<PERSON>", "address": "0xdd875635231e68e846ce190b1396ac0295d9e577"}, "0x4dce4cee89151a8cd4df7363ededfada89314159": {"symbol": "POC", "name": "POC Network", "address": "0x4dce4cee89151a8cd4df7363ededfada89314159"}, "0xc5102fe9359fd9a28f877a67e36b0f050d81a3cc": {"symbol": "HOP", "name": "Hop", "address": "0xc5102fe9359fd9a28f877a67e36b0f050d81a3cc"}, "0x15b7c0c907e4c6b9adaaaabc300c08991d6cea05": {"symbol": "GEL", "name": "Gelato Network Token", "address": "0x15b7c0c907e4c6b9adaaaabc300c08991d6cea05"}, "0x612d833c0c7a54cdfbe9a4328b6d658020563ec0": {"symbol": "fxPINE", "name": "Pine Token (FXERC20)", "address": "0x612d833c0c7a54cdfbe9a4328b6d658020563ec0"}, "0x6ae7dfc73e0dde2aa99ac063dcf7e8a63265108c": {"symbol": "JPYC", "name": "JPY Coin (PoS)", "address": "0x6ae7dfc73e0dde2aa99ac063dcf7e8a63265108c"}, "0x4d1137c03262bcd286f26782033b60af792cd59d": {"symbol": "CLPC", "name": "CLP Coin", "address": "0x4d1137c03262bcd286f26782033b60af792cd59d"}, "0x59d9356e565ab3a36dd77763fc0d87feaf85508c": {"symbol": "USDM", "name": "Mountain Protocol USD", "address": "0x59d9356e565ab3a36dd77763fc0d87feaf85508c"}, "0xd7778e1c556130391c6ed3801cdeca0540ae899a": {"symbol": "CES", "name": "TOKENHARDFOREK", "address": "0xd7778e1c556130391c6ed3801cdeca0540ae899a"}, "0xdb7a2607b71134d0b09c27ca2d77b495e4dbeedb": {"symbol": "GRANTS", "name": "<PERSON>", "address": "0xdb7a2607b71134d0b09c27ca2d77b495e4dbeedb"}, "0x6c0cb62d5b8b21d5097313d89670a260266a701a": {"symbol": "IDO", "name": "IDO", "address": "0x6c0cb62d5b8b21d5097313d89670a260266a701a"}, "0x2bc07124d8dac638e290f401046ad584546bc47b": {"symbol": "TOWER", "name": "TOWER", "address": "0x2bc07124d8dac638e290f401046ad584546bc47b"}, "0x3553f861dec0257bada9f8ed268bf0d74e45e89c": {"symbol": "USDT", "name": "USDT (Wormhole)", "address": "0x3553f861dec0257bada9f8ed268bf0d74e45e89c"}, "0xb53ec4ace420a62cfb75afdeba600d284777cd65": {"symbol": "SPACE", "name": "Space Token", "address": "0xb53ec4ace420a62cfb75afdeba600d284777cd65"}, "0x5309e43a4773736eba0c904cb850d09a4f98eef1": {"symbol": "DBC", "name": "DBC", "address": "0x5309e43a4773736eba0c904cb850d09a4f98eef1"}, "0x23e8b6a3f6891254988b84da3738d2bfe5e703b9": {"symbol": "WELT", "name": "FABWELT", "address": "0x23e8b6a3f6891254988b84da3738d2bfe5e703b9"}, "******************************************": {"symbol": "BONK", "name": "Bonk Protocol", "address": "******************************************"}, "******************************************": {"symbol": "Skibo", "name": "scooterluiboost", "address": "******************************************"}, "******************************************": {"symbol": "OPN", "name": "Open Ecosystem Token(PoS)", "address": "******************************************"}, "******************************************": {"symbol": "DCI", "name": "Dynamic Crypto Index", "address": "******************************************"}, "******************************************": {"symbol": "RUSK", "name": "RUSK", "address": "******************************************"}, "******************************************": {"symbol": "FUD", "name": "Aavegotchi FUD", "address": "******************************************"}, "******************************************": {"symbol": "WEXpoly", "name": "WaultSwap Polygon", "address": "******************************************"}, "******************************************": {"symbol": "SURE", "name": "inSure (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "BOMB", "name": "Bombcrypto Coin", "address": "******************************************"}, "******************************************": {"symbol": "CBBAG", "name": "Cabbage", "address": "******************************************"}, "******************************************": {"symbol": "CHAIN", "name": "Chain Games", "address": "******************************************"}, "******************************************": {"symbol": "RVLT", "name": "Revolt 2 Earn", "address": "******************************************"}, "******************************************": {"symbol": "PGX", "name": "<PERSON>eg<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "SNL", "name": "<PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "0xPoly", "name": "<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "20WETH-80BAL", "name": "20WETH-80BAL", "address": "******************************************"}, "******************************************": {"symbol": "tetuBAL-BALWETH", "name": "tetuBal-BPT-80BAL-20WETH Stable Pool", "address": "******************************************"}, "******************************************": {"symbol": "tetuBAL", "name": "TETU_ST_BAL", "address": "******************************************"}, "******************************************": {"symbol": "MASQ", "name": "MASQ (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "PSP", "name": "ParaSwap (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "SPD", "name": "SWISSSPD", "address": "******************************************"}, "******************************************": {"symbol": "PKR", "name": "POLKER", "address": "******************************************"}, "******************************************": {"symbol": "ATK", "name": "Attack", "address": "******************************************"}, "******************************************": {"symbol": "OVR", "name": "OVR (PoS)", "address": "******************************************"}, "0xdd28ec6b06983d01d37dbd9ab581d8d884d95264": {"symbol": "SUMMER", "name": "Summer Token (PoS)", "address": "0xdd28ec6b06983d01d37dbd9ab581d8d884d95264"}, "0xd8ca34fd379d9ca3c6ee3b3905678320f5b45195": {"symbol": "gOHM", "name": "Governance OHM", "address": "0xd8ca34fd379d9ca3c6ee3b3905678320f5b45195"}, "0xfc9dc82a8ba6c64cdff8bf90b9be32d60f291d47": {"symbol": "TOYSTORY", "name": "Toy Story", "address": "0xfc9dc82a8ba6c64cdff8bf90b9be32d60f291d47"}, "0x9c9e5fd8bbc25984b178fdce6117defa39d2db39": {"symbol": "BUSD", "name": "BUSD Token", "address": "0x9c9e5fd8bbc25984b178fdce6117defa39d2db39"}, "0xf1c1a3c2481a3a8a3f173a9ab5ade275292a6fa3": {"symbol": "VEE", "name": "BLOCKv Token (PoS)", "address": "0xf1c1a3c2481a3a8a3f173a9ab5ade275292a6fa3"}, "0x765af38a6e8fdcb1efef8a3dd2213efd3090b00f": {"symbol": "VDT", "name": "Vendetta", "address": "0x765af38a6e8fdcb1efef8a3dd2213efd3090b00f"}, "0x6e4e624106cb12e168e6533f8ec7c82263358940": {"symbol": "AXL", "name": "<PERSON><PERSON>", "address": "0x6e4e624106cb12e168e6533f8ec7c82263358940"}, "0xb7042c40de76cfc607ac05e68f9c28a778f0c8a6": {"symbol": "WISTA", "name": "Wistaverse", "address": "0xb7042c40de76cfc607ac05e68f9c28a778f0c8a6"}, "0x2e1ad108ff1d8c782fcbbb89aad783ac49586756": {"symbol": "TUSD", "name": "TrueUSD (PoS)", "address": "0x2e1ad108ff1d8c782fcbbb89aad783ac49586756"}, "0x8a0e8b4b0903929f47c3ea30973940d4a9702067": {"symbol": "INSUR", "name": "InsurAce (PoS)", "address": "0x8a0e8b4b0903929f47c3ea30973940d4a9702067"}, "0xc7b20be77c499e0a7e671e2802a5c70e1fa90d52": {"symbol": "NIK", "name": "NIK Company №3", "address": "0xc7b20be77c499e0a7e671e2802a5c70e1fa90d52"}, "0x7246e2e2b6fb34ee4f56ab797bf2576b62e954bf": {"symbol": "DSC", "name": "DeepSeekChain", "address": "0x7246e2e2b6fb34ee4f56ab797bf2576b62e954bf"}, "0x186d65ced0693382713437e34ef8723fd6aa9a1e": {"symbol": "una.WEMIX", "name": "una WEMIX", "address": "0x186d65ced0693382713437e34ef8723fd6aa9a1e"}, "0x5bef2617ecca9a39924c09017c5f1e25efbb3ba8": {"symbol": "oUSDC", "name": "Orbit Bridge Polygon USD Coin", "address": "0x5bef2617ecca9a39924c09017c5f1e25efbb3ba8"}, "0xf4c83080e80ae530d6f8180572cbbf1ac9d5d435": {"symbol": "BLANK", "name": "GoBlank <PERSON> (PoS)", "address": "0xf4c83080e80ae530d6f8180572cbbf1ac9d5d435"}, "0xa1068f2c8ae2597f1b6865fd26412f409328d329": {"symbol": "OFF", "name": "OFF", "address": "0xa1068f2c8ae2597f1b6865fd26412f409328d329"}, "0x5d301750cc9719f00872e33ee81f9c37aba242f4": {"symbol": "RVLT", "name": "Revolt 2 Earn", "address": "0x5d301750cc9719f00872e33ee81f9c37aba242f4"}, "0xe1c110e1b1b4a1ded0caf3e42bfbdbb7b5d7ce1c": {"symbol": "ELK", "name": "Elk", "address": "0xe1c110e1b1b4a1ded0caf3e42bfbdbb7b5d7ce1c"}, "0x45a1e48daf3f6a55efbbd067380f4601300baa7a": {"symbol": "EDX", "name": "EADX Token", "address": "0x45a1e48daf3f6a55efbbd067380f4601300baa7a"}, "0x65c9e3289e5949134759119dbc9f862e8d6f2fbe": {"symbol": "MPH", "name": "<PERSON><PERSON><PERSON>", "address": "0x65c9e3289e5949134759119dbc9f862e8d6f2fbe"}, "0xce7edd2405fc6b0a0dc0c2ddc5bc3562f119be9c": {"symbol": "NERZO", "name": "NERZO TOKEN", "address": "0xce7edd2405fc6b0a0dc0c2ddc5bc3562f119be9c"}, "0xa2988c7ad74e8e7f42d002eb05fdb81a53f990a4": {"symbol": "QUICOIN", "name": "En un lugar de la mancha, de cuyo nombre no quiero acordarme...", "address": "0xa2988c7ad74e8e7f42d002eb05fdb81a53f990a4"}, "0xb8d5f5f236c24e09c7f55eec313818742ac4cf79": {"symbol": "VSX", "name": "Versus-X", "address": "0xb8d5f5f236c24e09c7f55eec313818742ac4cf79"}, "0xd6c4173c24ecf337b25f43c37bc359801e10dc93": {"symbol": "TES", "name": "TES High-yield and risk company №10", "address": "0xd6c4173c24ecf337b25f43c37bc359801e10dc93"}, "0xfef5d947472e72efbb2e388c730b7428406f2f95": {"symbol": "OLAS", "name": "Autonolas(PoS)", "address": "0xfef5d947472e72efbb2e388c730b7428406f2f95"}, "0x5c4b7ccbf908e64f32e12c6650ec0c96d717f03f": {"symbol": "BNB", "name": "Binance Token", "address": "0x5c4b7ccbf908e64f32e12c6650ec0c96d717f03f"}, "0x9c1c23e60b72bc88a043bf64afdb16a02540ae8f": {"symbol": "Ring", "name": "Darwinia (PoS)", "address": "0x9c1c23e60b72bc88a043bf64afdb16a02540ae8f"}, "0xef6ab48ef8dfe984fab0d5c4cd6aff2e54dfda14": {"symbol": "CRISP-M", "name": "CRISP Scored Mangroves", "address": "0xef6ab48ef8dfe984fab0d5c4cd6aff2e54dfda14"}, "0x0a02d33031917d836bd7af02f9f7f6c74d67805f": {"symbol": "PKLAY", "name": "Orbit Bridge Polygon Klay", "address": "0x0a02d33031917d836bd7af02f9f7f6c74d67805f"}, "0x550f908e06d1da4ffee6b1fb63730f88ecc4d230": {"symbol": "TRYAN", "name": "<PERSON><PERSON>", "address": "0x550f908e06d1da4ffee6b1fb63730f88ecc4d230"}, "0xd14d1e501b2b52d6134db1ad0857aa91f9bfe2dd": {"symbol": "SWAVE", "name": "Shuts Wave", "address": "0xd14d1e501b2b52d6134db1ad0857aa91f9bfe2dd"}, "0x34c1b299a74588d6abdc1b85a53345a48428a521": {"symbol": "EZ", "name": "EASY V2", "address": "0x34c1b299a74588d6abdc1b85a53345a48428a521"}, "0xedd6ca8a4202d4a36611e2fff109648c4863ae19": {"symbol": "MAHA", "name": "MahaDAO (PoS)", "address": "0xedd6ca8a4202d4a36611e2fff109648c4863ae19"}, "0x3f364853f01d32d581fc9734110b21c77aeea024": {"symbol": "oMATIC", "name": "Orbit Bridge Polygon Matic Token", "address": "0x3f364853f01d32d581fc9734110b21c77aeea024"}, "0xc3323b6e71925b25943fb7369ee6769837e9c676": {"symbol": "PORIGON", "name": "PORIGON", "address": "0xc3323b6e71925b25943fb7369ee6769837e9c676"}, "******************************************": {"symbol": "DogeAid", "name": "DogeAid", "address": "******************************************"}, "******************************************": {"symbol": "TEA-D", "name": "TEA-D", "address": "******************************************"}, "******************************************": {"symbol": "PKT", "name": "Pirate Kings Treasure ", "address": "******************************************"}, "******************************************": {"symbol": "wstETH-WETH-BPT", "name": "Balancer wstETH-WETH Stable Pool", "address": "******************************************"}, "******************************************": {"symbol": "GOON", "name": "GOON", "address": "******************************************"}, "******************************************": {"symbol": "OPUL", "name": "OpulousToken", "address": "******************************************"}, "******************************************": {"symbol": "SHARP", "name": "SHARP", "address": "******************************************"}, "******************************************": {"symbol": "pXi", "name": "<PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "RTK", "name": "RetaStake", "address": "******************************************"}, "******************************************": {"symbol": "ATA", "name": "Automata (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "LGNS", "name": "Long<PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "BTCpx", "name": "BTC Proxy", "address": "******************************************"}, "******************************************": {"symbol": "VIS", "name": "VIS Company №6", "address": "******************************************"}, "******************************************": {"symbol": "DEPS", "name": "Decentralized Euro Protocol Share(PoS)", "address": "******************************************"}, "******************************************": {"symbol": "AMBER", "name": "<PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "FAST", "name": "Edge Video AI", "address": "******************************************"}, "******************************************": {"symbol": "DDM", "name": "DDM Deutsche Mark", "address": "******************************************"}, "******************************************": {"symbol": "maticX-WMATIC-BPT", "name": "Balancer maticX-WMATIC Stable Pool", "address": "******************************************"}, "0xd04b728c12bbb41a5bee5d8b96a74221152401b4": {"symbol": "APP", "name": "APP High-yield and risk company №9", "address": "0xd04b728c12bbb41a5bee5d8b96a74221152401b4"}, "0x6d295da283c4c0658eafe608e4145e8a86777b88": {"symbol": "SOLX", "name": "SolarX", "address": "0x6d295da283c4c0658eafe608e4145e8a86777b88"}, "0xa0ead927e6c31646cf1d4cc721705c415e515bd4": {"symbol": "TRIM", "name": "TRIMBEX", "address": "0xa0ead927e6c31646cf1d4cc721705c415e515bd4"}, "0xf501dd45a1198c2e1b5aef5314a68b9006d842e0": {"symbol": "MTA", "name": "Meta (PoS)", "address": "0xf501dd45a1198c2e1b5aef5314a68b9006d842e0"}, "0x1e796debcb656bd71f2f2311443a52ec8c98229f": {"symbol": "FROTH", "name": "FROTH", "address": "0x1e796debcb656bd71f2f2311443a52ec8c98229f"}, "0x0b220b82f3ea3b7f6d9a1d8ab58930c064a2b5bf": {"symbol": "GLM", "name": "Golem Network Token (PoS)", "address": "0x0b220b82f3ea3b7f6d9a1d8ab58930c064a2b5bf"}, "0x99b59cabce596ba779db2e7e5c66b6f47f99b599": {"symbol": "LQDTY", "name": "Liquidity", "address": "0x99b59cabce596ba779db2e7e5c66b6f47f99b599"}, "0xef205f99bd869e74b16050199d54327a0ecd343d": {"symbol": "FOLO", "name": "Follow (PoS)", "address": "0xef205f99bd869e74b16050199d54327a0ecd343d"}, "0xa7a01e16efc51010c1c8e900c6b1c5a828a7ab8a": {"symbol": "GENKI", "name": "GENKI", "address": "0xa7a01e16efc51010c1c8e900c6b1c5a828a7ab8a"}, "0x1ef5bb23e0b91c2e8480a4a2b71feb4607cb32f1": {"symbol": "SGT", "name": "SGT (PoS)", "address": "0x1ef5bb23e0b91c2e8480a4a2b71feb4607cb32f1"}, "0x7e26081d41fd9a18a32b66268c8cc5c621eb1cf2": {"symbol": "BUSO", "name": "Butterfly Solution", "address": "0x7e26081d41fd9a18a32b66268c8cc5c621eb1cf2"}, "0x4b96dbf8f42c8c296573933a6616dcafb80ca461": {"symbol": "oTON", "name": "Orbit Bridge Polygon Toncoin", "address": "0x4b96dbf8f42c8c296573933a6616dcafb80ca461"}, "0x1e289178612f5b6d32f692e312dcf783c74b2162": {"symbol": "ISP", "name": "Ispolink Token (PoS)", "address": "0x1e289178612f5b6d32f692e312dcf783c74b2162"}, "0xe94fe7bcef8be8ccff138c25031b76c0956e4c3d": {"symbol": "MCW", "name": "MCW Token", "address": "0xe94fe7bcef8be8ccff138c25031b76c0956e4c3d"}, "0x1509706a6c66ca549ff0cb464de88231ddbe213b": {"symbol": "AURA", "name": "<PERSON>ra", "address": "0x1509706a6c66ca549ff0cb464de88231ddbe213b"}, "0xbec158cd8df7e48322485816eab3a984f69458d8": {"symbol": "B2Z", "name": "B2Z Exchange", "address": "0xbec158cd8df7e48322485816eab3a984f69458d8"}, "0xe302672798d12e7f68c783db2c2d5e6b48ccf3ce": {"symbol": "IGS", "name": "Igypt Gold Sovereign ", "address": "0xe302672798d12e7f68c783db2c2d5e6b48ccf3ce"}, "0xd63e1604974b67f9de8a2d1c6ac7d8523e4b0a39": {"symbol": "HIME", "name": "<PERSON><PERSON>", "address": "0xd63e1604974b67f9de8a2d1c6ac7d8523e4b0a39"}, "0x12016b4e07866c962e29b7597ecd66b3b89a3a58": {"symbol": "TREND", "name": "TRENDY DEFI", "address": "0x12016b4e07866c962e29b7597ecd66b3b89a3a58"}, "0x4837b18a6d7af6159c8665505b90a2ed393255e0": {"symbol": "LYP", "name": "Lympid <PERSON>", "address": "0x4837b18a6d7af6159c8665505b90a2ed393255e0"}, "0x8ce10fbfeda4bbce18494d2375115f9d9fdd6417": {"symbol": "ORBD", "name": "OrbitEdge", "address": "0x8ce10fbfeda4bbce18494d2375115f9d9fdd6417"}, "0xccf37622e6b72352e7b410481dd4913563038b7c": {"symbol": "OGC", "name": "Orklin Gold Coin", "address": "0xccf37622e6b72352e7b410481dd4913563038b7c"}, "0x8f2b1536aa674c918725b8d0deddda46bddc2260": {"symbol": "DITEX", "name": "Digital Tech", "address": "0x8f2b1536aa674c918725b8d0deddda46bddc2260"}, "0xa2a5aa62a2b13f95d6af954df8c5ec04a1dda7e1": {"symbol": "GKCM", "name": "Global Knowledge Centre Medical", "address": "0xa2a5aa62a2b13f95d6af954df8c5ec04a1dda7e1"}, "0xf5bb76ffd8e7ffea8a655a722cbdb0f6982a70bb": {"symbol": "TMATO", "name": "Tomato", "address": "0xf5bb76ffd8e7ffea8a655a722cbdb0f6982a70bb"}, "0xb8751eaef59b5825b40fd4043a4a8dbb184cb96b": {"symbol": "POLYD", "name": "PolyD", "address": "0xb8751eaef59b5825b40fd4043a4a8dbb184cb96b"}, "0xd2507e7b5794179380673870d88b22f94da6abe0": {"symbol": "XYO", "name": "XY Oracle (PoS)", "address": "0xd2507e7b5794179380673870d88b22f94da6abe0"}, "0xd90b9773f9922a34f763087665b3a4649e710326": {"symbol": "ARBI", "name": "ArbiBot", "address": "0xd90b9773f9922a34f763087665b3a4649e710326"}, "0x75c0a194cd8b4f01d5ed58be5b7c5b61a9c69d0a": {"symbol": "DHG", "name": "Dragon Hoard Gold", "address": "0x75c0a194cd8b4f01d5ed58be5b7c5b61a9c69d0a"}, "0x84ebc138f4ab844a3050a6059763d269dc9951c6": {"symbol": "FCC", "name": "Fishcake Coin", "address": "0x84ebc138f4ab844a3050a6059763d269dc9951c6"}, "0x774b9dd3977a7556bf16cc22b74b2991e4511e13": {"symbol": "SCAI", "name": "SecureChain AI", "address": "0x774b9dd3977a7556bf16cc22b74b2991e4511e13"}, "******************************************": {"symbol": "FTKX", "name": "FTKX", "address": "******************************************"}, "******************************************": {"symbol": "LGP", "name": "Luminosity Gold Piece ", "address": "******************************************"}, "******************************************": {"symbol": "CCC", "name": "Carbon Counting Coin", "address": "******************************************"}, "******************************************": {"symbol": "PETH", "name": "Orbit Bridge Polygon Ethereum", "address": "******************************************"}, "******************************************": {"symbol": "pDMTR", "name": "<PERSON><PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "EARTH", "name": "Earth", "address": "******************************************"}, "******************************************": {"symbol": "FLEATO", "name": "<PERSON><PERSON><PERSON> (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "GC", "name": "<PERSON>lapa<PERSON><PERSON>n", "address": "******************************************"}, "******************************************": {"symbol": "KSN", "name": "KISSAN", "address": "******************************************"}, "******************************************": {"symbol": "XBR", "name": "X BULL RUN", "address": "******************************************"}, "0x746351ab4b9d4f802b7b770f33184d0a6b17363d": {"symbol": "oMOOI", "name": "Orbit Bridge Polygon MOOI", "address": "0x746351ab4b9d4f802b7b770f33184d0a6b17363d"}, "0x9a94965d690298c0086aba54f0d30daf4ca806a1": {"symbol": "LLK", "name": "Liberoom Like", "address": "0x9a94965d690298c0086aba54f0d30daf4ca806a1"}, "0x40ccd55b789fdee8d434915dc2aa6bd938506a92": {"symbol": "RAGE", "name": "RAGEMATIC (PoS)", "address": "0x40ccd55b789fdee8d434915dc2aa6bd938506a92"}, "******************************************": {"symbol": "oORC", "name": "Orbit Bridge Polygon Orbit Chain", "address": "******************************************"}, "******************************************": {"symbol": "VERI", "name": "Veritaseum (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "nftfan", "name": "NFT Fans", "address": "******************************************"}, "******************************************": {"symbol": "TELEBTC", "name": "teleBTC", "address": "******************************************"}, "******************************************": {"symbol": "PDOGE", "name": "PolyDoge", "address": "******************************************"}, "******************************************": {"symbol": "BTS", "name": "Bitcast", "address": "******************************************"}, "******************************************": {"symbol": "fJLT-F24", "name": "Forwards Voluntary REC Front-Half 2024", "address": "******************************************"}, "******************************************": {"symbol": "POLS", "name": "Bridged prc-20 pols", "address": "******************************************"}, "******************************************": {"symbol": "00", "name": "00 Token", "address": "******************************************"}, "******************************************": {"symbol": "AGIX", "name": "SingularityNET Token (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "oUSDT", "name": "Orbit Bridge Polygon Tether USD", "address": "******************************************"}, "******************************************": {"symbol": "MCR", "name": "MCR Company №4", "address": "******************************************"}, "******************************************": {"symbol": "TRAXX", "name": "TRAXX", "address": "******************************************"}, "******************************************": {"symbol": "ENS", "name": "Ethereum Name Service (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "GLQ", "name": "GraphLinq (PoS)", "address": "******************************************"}, "******************************************": {"symbol": "MKT", "name": "MorroKota", "address": "******************************************"}, "******************************************": {"symbol": "BAG", "name": "Bonk BAG", "address": "******************************************"}, "******************************************": {"symbol": "NXT", "name": "NeXToken", "address": "******************************************"}, "******************************************": {"symbol": "PMT", "name": "<PERSON><PERSON>", "address": "******************************************"}, "******************************************": {"symbol": "TruMATIC-WMATIC", "name": "Balancer TruMATIC-WMATIC Stable Pool", "address": "******************************************"}, "0xf33687811f3ad0cd6b48dd4b39f9f977bd7165a2": {"symbol": "TruMATIC", "name": "TruStake MATIC Vault Shares (PoS)", "address": "0xf33687811f3ad0cd6b48dd4b39f9f977bd7165a2"}, "0x72bd80445b0db58ebe3e8db056529d4c5faf6f2f": {"symbol": "NEAR", "name": "NEAR", "address": "0x72bd80445b0db58ebe3e8db056529d4c5faf6f2f"}, "0xc2b18ff2b90de64ab15c469a62e7f5ac1fa3437e": {"symbol": "PPS", "name": "PPS Company №5", "address": "0xc2b18ff2b90de64ab15c469a62e7f5ac1fa3437e"}, "0x04b48c9707fe5091ee772d92941f745bc0ad2b8f": {"symbol": "DADS", "name": "POLYGON DADS", "address": "0x04b48c9707fe5091ee772d92941f745bc0ad2b8f"}, "0x0fe44f4a31ba04291788b2b0f2e9872f36fe2f94": {"symbol": "KRB", "name": "<PERSON><PERSON><PERSON>", "address": "0x0fe44f4a31ba04291788b2b0f2e9872f36fe2f94"}, "0x0c0391a4abef50fc9386f69f2266d4d2fbeb96c7": {"symbol": "WLOKA", "name": "Wrapped LOKA", "address": "0x0c0391a4abef50fc9386f69f2266d4d2fbeb96c7"}, "0x8ece0a50a025a7e13398212a5bed2ded11959949": {"symbol": "oDAI", "name": "Orbit Bridge Polygon Dai", "address": "0x8ece0a50a025a7e13398212a5bed2ded11959949"}, "0x46f69a07c9d604904ced6d98aab82ac2d441b96f": {"symbol": "TJM", "name": "TTAJ MEME", "address": "0x46f69a07c9d604904ced6d98aab82ac2d441b96f"}, "0x5988bf243adf1b42a2ec2e9452d144a90b1fd9a9": {"symbol": "DB", "name": "DAObiContract1", "address": "0x5988bf243adf1b42a2ec2e9452d144a90b1fd9a9"}, "0x8f94400cdef837f388100cfe56d7ebb8a3a3fb44": {"symbol": "OKB", "name": "OKB (PoS)", "address": "0x8f94400cdef837f388100cfe56d7ebb8a3a3fb44"}, "0x34c8b806538bc6071ed628f2a408581160aca67b": {"symbol": "CIFI", "name": "CIFI@polygon", "address": "0x34c8b806538bc6071ed628f2a408581160aca67b"}, "0x146642d83879257ac9ed35074b1c3714b7e8f452": {"symbol": "AU24T", "name": "AU24T", "address": "0x146642d83879257ac9ed35074b1c3714b7e8f452"}, "0x7583feddbcefa813dc18259940f76a02710a8905": {"symbol": "FET", "name": "<PERSON>tch (PoS)", "address": "0x7583feddbcefa813dc18259940f76a02710a8905"}, "0xe1b3d3f9302fb84c8d5b9e13cd8160fee67ea239": {"symbol": "PCT", "name": "Pinecone Token", "address": "0xe1b3d3f9302fb84c8d5b9e13cd8160fee67ea239"}, "0x2710ed3f5d44268dcb89f549050718aa237c8a47": {"symbol": "MEMO", "name": "MetaMEMO (PoS)", "address": "0x2710ed3f5d44268dcb89f549050718aa237c8a47"}, "0x0946c90058ce01d734b9e770ffcfd0c029f83709": {"symbol": "UTOP", "name": "Utopos", "address": "0x0946c90058ce01d734b9e770ffcfd0c029f83709"}, "0x03787e502cc8e5dbdc0d4b44f888802719dc5165": {"symbol": "ASTGO", "name": "Astrinea Gold", "address": "0x03787e502cc8e5dbdc0d4b44f888802719dc5165"}, "0x5a648b6ec4d2630ad75d9d8fcb452b2554a33e48": {"symbol": "BRCLI", "name": "<PERSON><PERSON><PERSON><PERSON>", "address": "0x5a648b6ec4d2630ad75d9d8fcb452b2554a33e48"}, "0x900f717ea076e1e7a484ad9dd2db81ceec60ebf1": {"symbol": "ANGLE", "name": "ANGLE", "address": "0x900f717ea076e1e7a484ad9dd2db81ceec60ebf1"}, "0x1a16afdf84b3de52a43b49d4a9666c84f53cdbd2": {"symbol": "FANS", "name": "Fanscope FANS", "address": "0x1a16afdf84b3de52a43b49d4a9666c84f53cdbd2"}, "0x12a987ffcf6f7c9de727ad0ca79ad84940caf6cb": {"symbol": "Solx", "name": "Solaxy", "address": "0x12a987ffcf6f7c9de727ad0ca79ad84940caf6cb"}, "0x7f6568e690d856f147c17aa28c5cb99bb2f67b8c": {"symbol": "PTATO", "name": "Potato", "address": "0x7f6568e690d856f147c17aa28c5cb99bb2f67b8c"}, "0x928277e774f34272717eadfafc3fd802dafbd0f5": {"symbol": "PRANA", "name": "Prana_v2", "address": "0x928277e774f34272717eadfafc3fd802dafbd0f5"}, "0x45e119045f46a8e02b5b89f417649fbeb042545d": {"symbol": "OBVX", "name": "OblivionX", "address": "0x45e119045f46a8e02b5b89f417649fbeb042545d"}, "0xf6a38ce301700e28e0d0a3b021bae6d4fa1b5d1b": {"symbol": "BSJ", "name": "BASENJI (PoS)", "address": "0xf6a38ce301700e28e0d0a3b021bae6d4fa1b5d1b"}, "0x2c605cfdc199ae80bf70318de855769702e52967": {"symbol": "VTPN", "name": "VTPN", "address": "0x2c605cfdc199ae80bf70318de855769702e52967"}, "0xc9c1c1c20b3658f8787cc2fd702267791f224ce1": {"symbol": "FTM", "name": "<PERSON><PERSON> (PoS)", "address": "0xc9c1c1c20b3658f8787cc2fd702267791f224ce1"}, "0x7bb11e7f8b10e9e571e5d8eace04735fdfb2358a": {"symbol": "WAVAX", "name": "Wrapped AVAX (Wormhole)", "address": "0x7bb11e7f8b10e9e571e5d8eace04735fdfb2358a"}, "0x5744a08b7c9536204b224e3a65fd780430818b1c": {"symbol": "💰", "name": "USDS", "address": "0x5744a08b7c9536204b224e3a65fd780430818b1c"}, "0x12050c705152931cfee3dd56c52fb09dea816c23": {"symbol": "COPM", "name": "COP Minteo", "address": "0x12050c705152931cfee3dd56c52fb09dea816c23"}, "0x6e031a95e251e0015eb99a330dc1d9933995cc33": {"symbol": "DOG", "name": "DOG•GO•TO•THE•MOON", "address": "0x6e031a95e251e0015eb99a330dc1d9933995cc33"}, "0x0c7304fbaf2a320a1c50c46fe03752722f729946": {"symbol": "SLP", "name": "Smooth Love Potion (PoS)", "address": "0x0c7304fbaf2a320a1c50c46fe03752722f729946"}, "0x70e05a930bab8c3bf24b2b182af3f857d19377ef": {"symbol": "CROS", "name": "<PERSON><PERSON>(PoS)", "address": "0x70e05a930bab8c3bf24b2b182af3f857d19377ef"}, "0x60dfdd509021557334c0b09e7a90f5fdad9257ab": {"symbol": "SND", "name": "SaNiDa", "address": "0x60dfdd509021557334c0b09e7a90f5fdad9257ab"}, "0xbc0bea8e634ec838a2a45f8a43e7e16cd2a8ba99": {"symbol": "GRG", "name": "<PERSON><PERSON> (PoS)", "address": "0xbc0bea8e634ec838a2a45f8a43e7e16cd2a8ba99"}, "0x05fa81ae340098c8c7fcd310469195f9f8410858": {"symbol": "STEM", "name": "STEM", "address": "0x05fa81ae340098c8c7fcd310469195f9f8410858"}, "0xd97d48e26c94324a22ea03dfd46e7b84ebccfb7f": {"symbol": "KVX", "name": "Kravix", "address": "0xd97d48e26c94324a22ea03dfd46e7b84ebccfb7f"}, "0xd2d813ec52a8d3229288571fb38abdfb1d57a8e6": {"symbol": "VRN", "name": "Vireon", "address": "0xd2d813ec52a8d3229288571fb38abdfb1d57a8e6"}, "0x762d3d096b9a74f4d3adf2b0824456ef8fce5daa": {"symbol": "HC", "name": "<PERSON><PERSON><PERSON>", "address": "0x762d3d096b9a74f4d3adf2b0824456ef8fce5daa"}}