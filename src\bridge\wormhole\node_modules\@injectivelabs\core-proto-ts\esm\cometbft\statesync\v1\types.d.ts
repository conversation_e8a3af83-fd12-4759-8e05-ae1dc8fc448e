import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.statesync.v1";
/** Message is the top-level message type for the statesync service. */
export interface Message {
    snapshotsRequest?: SnapshotsRequest | undefined;
    snapshotsResponse?: SnapshotsResponse | undefined;
    chunkRequest?: ChunkRequest | undefined;
    chunkResponse?: ChunkResponse | undefined;
}
/** SnapshotsRequest is sent to request a snapshot. */
export interface SnapshotsRequest {
}
/** SnapshotsResponse contains the snapshot metadata. */
export interface SnapshotsResponse {
    height: string;
    format: number;
    chunks: number;
    hash: Uint8Array;
    metadata: Uint8Array;
}
/** ChunkRequest is sent to request a chunk. */
export interface ChunkRequest {
    height: string;
    format: number;
    index: number;
}
/** ChunkResponse contains a chunk of the snapshot. */
export interface ChunkResponse {
    height: string;
    format: number;
    index: number;
    chunk: Uint8Array;
    missing: boolean;
}
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
export declare const SnapshotsRequest: {
    encode(_: SnapshotsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SnapshotsRequest;
    fromJSON(_: any): SnapshotsRequest;
    toJSON(_: SnapshotsRequest): unknown;
    create(base?: DeepPartial<SnapshotsRequest>): SnapshotsRequest;
    fromPartial(_: DeepPartial<SnapshotsRequest>): SnapshotsRequest;
};
export declare const SnapshotsResponse: {
    encode(message: SnapshotsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SnapshotsResponse;
    fromJSON(object: any): SnapshotsResponse;
    toJSON(message: SnapshotsResponse): unknown;
    create(base?: DeepPartial<SnapshotsResponse>): SnapshotsResponse;
    fromPartial(object: DeepPartial<SnapshotsResponse>): SnapshotsResponse;
};
export declare const ChunkRequest: {
    encode(message: ChunkRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ChunkRequest;
    fromJSON(object: any): ChunkRequest;
    toJSON(message: ChunkRequest): unknown;
    create(base?: DeepPartial<ChunkRequest>): ChunkRequest;
    fromPartial(object: DeepPartial<ChunkRequest>): ChunkRequest;
};
export declare const ChunkResponse: {
    encode(message: ChunkResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ChunkResponse;
    fromJSON(object: any): ChunkResponse;
    toJSON(message: ChunkResponse): unknown;
    create(base?: DeepPartial<ChunkResponse>): ChunkResponse;
    fromPartial(object: DeepPartial<ChunkResponse>): ChunkResponse;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
