"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcReferralTransformer = void 0;
const utils_1 = require("@injectivelabs/utils");
class IndexerGrpcReferralTransformer {
    static referrerDetailsResponseToReferrerDetails(address, response) {
        return {
            referrerAddress: address,
            invitees: response.invitees,
            referrerCode: response.referrerCode,
            totalCommission: new utils_1.BigNumberInBase(response.totalCommission),
            totalTradingVolume: new utils_1.BigNumberInBase(response.totalTradingVolume),
        };
    }
    static inviteeDetailsResponseToInviteeDetails(response) {
        return response;
    }
    static referrerByCodeResponseToReferrerByCode(response) {
        return response?.referrerAddress || '';
    }
}
exports.IndexerGrpcReferralTransformer = IndexerGrpcReferralTransformer;
