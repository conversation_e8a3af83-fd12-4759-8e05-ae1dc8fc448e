import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "packetforward.v1";
/** GenesisState defines the packetforward genesis state */
export interface GenesisState {
    /**
     * key - information about forwarded packet: src_channel
     * (parsedReceiver.Channel), src_port (parsedReceiver.Port), sequence value -
     * information about original packet for refunding if necessary: retries,
     * srcPacketSender, srcPacket.DestinationChannel, srcPacket.DestinationPort
     */
    inFlightPackets: {
        [key: string]: InFlightPacket;
    };
}
export interface GenesisState_InFlightPacketsEntry {
    key: string;
    value: InFlightPacket | undefined;
}
/**
 * InFlightPacket contains information about original packet for
 * writing the acknowledgement and refunding if necessary.
 */
export interface InFlightPacket {
    originalSenderAddress: string;
    refundChannelId: string;
    refundPortId: string;
    packetSrcChannelId: string;
    packetSrcPortId: string;
    packetTimeoutTimestamp: string;
    packetTimeoutHeight: string;
    packetData: Uint8Array;
    refundSequence: string;
    retriesRemaining: number;
    timeout: string;
    nonrefundable: boolean;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
export declare const GenesisState_InFlightPacketsEntry: {
    encode(message: GenesisState_InFlightPacketsEntry, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState_InFlightPacketsEntry;
    fromJSON(object: any): GenesisState_InFlightPacketsEntry;
    toJSON(message: GenesisState_InFlightPacketsEntry): unknown;
    create(base?: DeepPartial<GenesisState_InFlightPacketsEntry>): GenesisState_InFlightPacketsEntry;
    fromPartial(object: DeepPartial<GenesisState_InFlightPacketsEntry>): GenesisState_InFlightPacketsEntry;
};
export declare const InFlightPacket: {
    encode(message: InFlightPacket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InFlightPacket;
    fromJSON(object: any): InFlightPacket;
    toJSON(message: InFlightPacket): unknown;
    create(base?: DeepPartial<InFlightPacket>): InFlightPacket;
    fromPartial(object: DeepPartial<InFlightPacket>): InFlightPacket;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
