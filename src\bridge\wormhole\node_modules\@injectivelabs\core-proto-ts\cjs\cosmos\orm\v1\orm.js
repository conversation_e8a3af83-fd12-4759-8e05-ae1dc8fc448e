"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingletonDescriptor = exports.SecondaryIndexDescriptor = exports.PrimaryKeyDescriptor = exports.TableDescriptor = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.orm.v1";
function createBaseTableDescriptor() {
    return { primaryKey: undefined, index: [], id: 0 };
}
exports.TableDescriptor = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.primaryKey !== undefined) {
            exports.PrimaryKeyDescriptor.encode(message.primaryKey, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.index), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SecondaryIndexDescriptor.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.id !== 0) {
            writer.uint32(24).uint32(message.id);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTableDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.primaryKey = exports.PrimaryKeyDescriptor.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.index.push(exports.SecondaryIndexDescriptor.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.id = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            primaryKey: isSet(object.primaryKey) ? exports.PrimaryKeyDescriptor.fromJSON(object.primaryKey) : undefined,
            index: Array.isArray(object === null || object === void 0 ? void 0 : object.index) ? object.index.map(function (e) { return exports.SecondaryIndexDescriptor.fromJSON(e); }) : [],
            id: isSet(object.id) ? Number(object.id) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.primaryKey !== undefined &&
            (obj.primaryKey = message.primaryKey ? exports.PrimaryKeyDescriptor.toJSON(message.primaryKey) : undefined);
        if (message.index) {
            obj.index = message.index.map(function (e) { return e ? exports.SecondaryIndexDescriptor.toJSON(e) : undefined; });
        }
        else {
            obj.index = [];
        }
        message.id !== undefined && (obj.id = Math.round(message.id));
        return obj;
    },
    create: function (base) {
        return exports.TableDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTableDescriptor();
        message.primaryKey = (object.primaryKey !== undefined && object.primaryKey !== null)
            ? exports.PrimaryKeyDescriptor.fromPartial(object.primaryKey)
            : undefined;
        message.index = ((_a = object.index) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.SecondaryIndexDescriptor.fromPartial(e); })) || [];
        message.id = (_b = object.id) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBasePrimaryKeyDescriptor() {
    return { fields: "", autoIncrement: false };
}
exports.PrimaryKeyDescriptor = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.fields !== "") {
            writer.uint32(10).string(message.fields);
        }
        if (message.autoIncrement === true) {
            writer.uint32(16).bool(message.autoIncrement);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePrimaryKeyDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fields = reader.string();
                    break;
                case 2:
                    message.autoIncrement = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            fields: isSet(object.fields) ? String(object.fields) : "",
            autoIncrement: isSet(object.autoIncrement) ? Boolean(object.autoIncrement) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.fields !== undefined && (obj.fields = message.fields);
        message.autoIncrement !== undefined && (obj.autoIncrement = message.autoIncrement);
        return obj;
    },
    create: function (base) {
        return exports.PrimaryKeyDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePrimaryKeyDescriptor();
        message.fields = (_a = object.fields) !== null && _a !== void 0 ? _a : "";
        message.autoIncrement = (_b = object.autoIncrement) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseSecondaryIndexDescriptor() {
    return { fields: "", id: 0, unique: false };
}
exports.SecondaryIndexDescriptor = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.fields !== "") {
            writer.uint32(10).string(message.fields);
        }
        if (message.id !== 0) {
            writer.uint32(16).uint32(message.id);
        }
        if (message.unique === true) {
            writer.uint32(24).bool(message.unique);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSecondaryIndexDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fields = reader.string();
                    break;
                case 2:
                    message.id = reader.uint32();
                    break;
                case 3:
                    message.unique = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            fields: isSet(object.fields) ? String(object.fields) : "",
            id: isSet(object.id) ? Number(object.id) : 0,
            unique: isSet(object.unique) ? Boolean(object.unique) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.fields !== undefined && (obj.fields = message.fields);
        message.id !== undefined && (obj.id = Math.round(message.id));
        message.unique !== undefined && (obj.unique = message.unique);
        return obj;
    },
    create: function (base) {
        return exports.SecondaryIndexDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSecondaryIndexDescriptor();
        message.fields = (_a = object.fields) !== null && _a !== void 0 ? _a : "";
        message.id = (_b = object.id) !== null && _b !== void 0 ? _b : 0;
        message.unique = (_c = object.unique) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseSingletonDescriptor() {
    return { id: 0 };
}
exports.SingletonDescriptor = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.id !== 0) {
            writer.uint32(8).uint32(message.id);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSingletonDescriptor();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { id: isSet(object.id) ? Number(object.id) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.id !== undefined && (obj.id = Math.round(message.id));
        return obj;
    },
    create: function (base) {
        return exports.SingletonDescriptor.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSingletonDescriptor();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
