"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
const types_js_1 = require("../types.js");
/**
 * @category Contract Exec Arguments
 */
/** @deprecated */
class ExecArgCreatePerpGridStrategy extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgCreatePerpGridStrategy(params);
    }
    toData() {
        const { params } = this;
        return {
            subaccount_id: params.subaccountId,
            bounds: [params.lowerBound, params.upperBound],
            levels: params.levels,
            slippage: params.slippage,
            strategy_type: {
                perpetual: {
                    margin_ratio: params.marginRatio,
                },
            },
            stop_loss: params.stopLoss
                ? {
                    exit_type: types_js_1.ExitType.Default,
                    exit_price: params.stopLoss,
                }
                : undefined,
            take_profit: params.takeProfit
                ? {
                    exit_type: types_js_1.ExitType.Default,
                    exit_price: params.takeProfit,
                }
                : undefined,
            ...(params.feeRecipient && {
                fee_recipient: params.feeRecipient,
            }),
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('create_strategy', this.toData());
    }
}
exports.default = ExecArgCreatePerpGridStrategy;
