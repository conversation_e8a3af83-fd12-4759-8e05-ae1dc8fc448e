"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Module = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.staking.module.v1";
function createBaseModule() {
    return { hooksOrder: [], authority: "", bech32PrefixValidator: "", bech32PrefixConsensus: "" };
}
exports.Module = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.hooksOrder), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        if (message.bech32PrefixValidator !== "") {
            writer.uint32(26).string(message.bech32PrefixValidator);
        }
        if (message.bech32PrefixConsensus !== "") {
            writer.uint32(34).string(message.bech32PrefixConsensus);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hooksOrder.push(reader.string());
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                case 3:
                    message.bech32PrefixValidator = reader.string();
                    break;
                case 4:
                    message.bech32PrefixConsensus = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            hooksOrder: Array.isArray(object === null || object === void 0 ? void 0 : object.hooksOrder) ? object.hooksOrder.map(function (e) { return String(e); }) : [],
            authority: isSet(object.authority) ? String(object.authority) : "",
            bech32PrefixValidator: isSet(object.bech32PrefixValidator) ? String(object.bech32PrefixValidator) : "",
            bech32PrefixConsensus: isSet(object.bech32PrefixConsensus) ? String(object.bech32PrefixConsensus) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.hooksOrder) {
            obj.hooksOrder = message.hooksOrder.map(function (e) { return e; });
        }
        else {
            obj.hooksOrder = [];
        }
        message.authority !== undefined && (obj.authority = message.authority);
        message.bech32PrefixValidator !== undefined && (obj.bech32PrefixValidator = message.bech32PrefixValidator);
        message.bech32PrefixConsensus !== undefined && (obj.bech32PrefixConsensus = message.bech32PrefixConsensus);
        return obj;
    },
    create: function (base) {
        return exports.Module.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseModule();
        message.hooksOrder = ((_a = object.hooksOrder) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.authority = (_b = object.authority) !== null && _b !== void 0 ? _b : "";
        message.bech32PrefixValidator = (_c = object.bech32PrefixValidator) !== null && _c !== void 0 ? _c : "";
        message.bech32PrefixConsensus = (_d = object.bech32PrefixConsensus) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
