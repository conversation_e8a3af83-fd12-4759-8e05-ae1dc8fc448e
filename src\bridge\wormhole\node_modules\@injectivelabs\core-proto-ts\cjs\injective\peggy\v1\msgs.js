"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.MsgRevokeEthereumBlacklistDesc = exports.MsgBlacklistEthereumAddressesDesc = exports.MsgUpdateParamsDesc = exports.MsgSubmitBadSignatureEvidenceDesc = exports.MsgCancelSendToEthDesc = exports.MsgSetOrchestratorAddressesDesc = exports.MsgERC20DeployedClaimDesc = exports.MsgValsetUpdateClaimDesc = exports.MsgWithdrawClaimDesc = exports.MsgDepositClaimDesc = exports.MsgConfirmBatchDesc = exports.MsgRequestBatchDesc = exports.MsgSendToEthDesc = exports.MsgValsetConfirmDesc = exports.MsgDesc = exports.MsgClientImpl = exports.MsgRevokeEthereumBlacklistResponse = exports.MsgRevokeEthereumBlacklist = exports.MsgBlacklistEthereumAddressesResponse = exports.MsgBlacklistEthereumAddresses = exports.MsgUpdateParamsResponse = exports.MsgUpdateParams = exports.MsgValsetUpdatedClaimResponse = exports.MsgValsetUpdatedClaim = exports.MsgSubmitBadSignatureEvidenceResponse = exports.MsgSubmitBadSignatureEvidence = exports.MsgCancelSendToEthResponse = exports.MsgCancelSendToEth = exports.MsgERC20DeployedClaimResponse = exports.MsgERC20DeployedClaim = exports.MsgWithdrawClaimResponse = exports.MsgWithdrawClaim = exports.MsgDepositClaimResponse = exports.MsgDepositClaim = exports.MsgConfirmBatchResponse = exports.MsgConfirmBatch = exports.MsgRequestBatchResponse = exports.MsgRequestBatch = exports.MsgSendToEthResponse = exports.MsgSendToEth = exports.MsgValsetConfirmResponse = exports.MsgValsetConfirm = exports.MsgSetOrchestratorAddressesResponse = exports.MsgSetOrchestratorAddresses = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var any_1 = require("../../../google/protobuf/any.js");
var params_1 = require("./params.js");
var types_1 = require("./types.js");
exports.protobufPackage = "injective.peggy.v1";
function createBaseMsgSetOrchestratorAddresses() {
    return { sender: "", orchestrator: "", ethAddress: "" };
}
exports.MsgSetOrchestratorAddresses = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.orchestrator !== "") {
            writer.uint32(18).string(message.orchestrator);
        }
        if (message.ethAddress !== "") {
            writer.uint32(26).string(message.ethAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSetOrchestratorAddresses();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.orchestrator = reader.string();
                    break;
                case 3:
                    message.ethAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        return obj;
    },
    create: function (base) {
        return exports.MsgSetOrchestratorAddresses.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMsgSetOrchestratorAddresses();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.orchestrator = (_b = object.orchestrator) !== null && _b !== void 0 ? _b : "";
        message.ethAddress = (_c = object.ethAddress) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseMsgSetOrchestratorAddressesResponse() {
    return {};
}
exports.MsgSetOrchestratorAddressesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSetOrchestratorAddressesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgSetOrchestratorAddressesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgSetOrchestratorAddressesResponse();
        return message;
    },
};
function createBaseMsgValsetConfirm() {
    return { nonce: "0", orchestrator: "", ethAddress: "", signature: "" };
}
exports.MsgValsetConfirm = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        if (message.orchestrator !== "") {
            writer.uint32(18).string(message.orchestrator);
        }
        if (message.ethAddress !== "") {
            writer.uint32(26).string(message.ethAddress);
        }
        if (message.signature !== "") {
            writer.uint32(34).string(message.signature);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgValsetConfirm();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.orchestrator = reader.string();
                    break;
                case 3:
                    message.ethAddress = reader.string();
                    break;
                case 4:
                    message.signature = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
            signature: isSet(object.signature) ? String(object.signature) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        message.signature !== undefined && (obj.signature = message.signature);
        return obj;
    },
    create: function (base) {
        return exports.MsgValsetConfirm.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMsgValsetConfirm();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.orchestrator = (_b = object.orchestrator) !== null && _b !== void 0 ? _b : "";
        message.ethAddress = (_c = object.ethAddress) !== null && _c !== void 0 ? _c : "";
        message.signature = (_d = object.signature) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseMsgValsetConfirmResponse() {
    return {};
}
exports.MsgValsetConfirmResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgValsetConfirmResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgValsetConfirmResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgValsetConfirmResponse();
        return message;
    },
};
function createBaseMsgSendToEth() {
    return { sender: "", ethDest: "", amount: undefined, bridgeFee: undefined };
}
exports.MsgSendToEth = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.ethDest !== "") {
            writer.uint32(18).string(message.ethDest);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        if (message.bridgeFee !== undefined) {
            coin_1.Coin.encode(message.bridgeFee, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSendToEth();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.ethDest = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.bridgeFee = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            ethDest: isSet(object.ethDest) ? String(object.ethDest) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
            bridgeFee: isSet(object.bridgeFee) ? coin_1.Coin.fromJSON(object.bridgeFee) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.ethDest !== undefined && (obj.ethDest = message.ethDest);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        message.bridgeFee !== undefined && (obj.bridgeFee = message.bridgeFee ? coin_1.Coin.toJSON(message.bridgeFee) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgSendToEth.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgSendToEth();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.ethDest = (_b = object.ethDest) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        message.bridgeFee = (object.bridgeFee !== undefined && object.bridgeFee !== null)
            ? coin_1.Coin.fromPartial(object.bridgeFee)
            : undefined;
        return message;
    },
};
function createBaseMsgSendToEthResponse() {
    return {};
}
exports.MsgSendToEthResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSendToEthResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgSendToEthResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgSendToEthResponse();
        return message;
    },
};
function createBaseMsgRequestBatch() {
    return { orchestrator: "", denom: "" };
}
exports.MsgRequestBatch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orchestrator !== "") {
            writer.uint32(10).string(message.orchestrator);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRequestBatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orchestrator = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.MsgRequestBatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgRequestBatch();
        message.orchestrator = (_a = object.orchestrator) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgRequestBatchResponse() {
    return {};
}
exports.MsgRequestBatchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRequestBatchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRequestBatchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRequestBatchResponse();
        return message;
    },
};
function createBaseMsgConfirmBatch() {
    return { nonce: "0", tokenContract: "", ethSigner: "", orchestrator: "", signature: "" };
}
exports.MsgConfirmBatch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        if (message.tokenContract !== "") {
            writer.uint32(18).string(message.tokenContract);
        }
        if (message.ethSigner !== "") {
            writer.uint32(26).string(message.ethSigner);
        }
        if (message.orchestrator !== "") {
            writer.uint32(34).string(message.orchestrator);
        }
        if (message.signature !== "") {
            writer.uint32(42).string(message.signature);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgConfirmBatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.tokenContract = reader.string();
                    break;
                case 3:
                    message.ethSigner = reader.string();
                    break;
                case 4:
                    message.orchestrator = reader.string();
                    break;
                case 5:
                    message.signature = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            ethSigner: isSet(object.ethSigner) ? String(object.ethSigner) : "",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
            signature: isSet(object.signature) ? String(object.signature) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.ethSigner !== undefined && (obj.ethSigner = message.ethSigner);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        message.signature !== undefined && (obj.signature = message.signature);
        return obj;
    },
    create: function (base) {
        return exports.MsgConfirmBatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgConfirmBatch();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.tokenContract = (_b = object.tokenContract) !== null && _b !== void 0 ? _b : "";
        message.ethSigner = (_c = object.ethSigner) !== null && _c !== void 0 ? _c : "";
        message.orchestrator = (_d = object.orchestrator) !== null && _d !== void 0 ? _d : "";
        message.signature = (_e = object.signature) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgConfirmBatchResponse() {
    return {};
}
exports.MsgConfirmBatchResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgConfirmBatchResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgConfirmBatchResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgConfirmBatchResponse();
        return message;
    },
};
function createBaseMsgDepositClaim() {
    return {
        eventNonce: "0",
        blockHeight: "0",
        tokenContract: "",
        amount: "",
        ethereumSender: "",
        cosmosReceiver: "",
        orchestrator: "",
        data: "",
    };
}
exports.MsgDepositClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.blockHeight !== "0") {
            writer.uint32(16).uint64(message.blockHeight);
        }
        if (message.tokenContract !== "") {
            writer.uint32(26).string(message.tokenContract);
        }
        if (message.amount !== "") {
            writer.uint32(34).string(message.amount);
        }
        if (message.ethereumSender !== "") {
            writer.uint32(42).string(message.ethereumSender);
        }
        if (message.cosmosReceiver !== "") {
            writer.uint32(50).string(message.cosmosReceiver);
        }
        if (message.orchestrator !== "") {
            writer.uint32(58).string(message.orchestrator);
        }
        if (message.data !== "") {
            writer.uint32(66).string(message.data);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDepositClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.tokenContract = reader.string();
                    break;
                case 4:
                    message.amount = reader.string();
                    break;
                case 5:
                    message.ethereumSender = reader.string();
                    break;
                case 6:
                    message.cosmosReceiver = reader.string();
                    break;
                case 7:
                    message.orchestrator = reader.string();
                    break;
                case 8:
                    message.data = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            ethereumSender: isSet(object.ethereumSender) ? String(object.ethereumSender) : "",
            cosmosReceiver: isSet(object.cosmosReceiver) ? String(object.cosmosReceiver) : "",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
            data: isSet(object.data) ? String(object.data) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.amount !== undefined && (obj.amount = message.amount);
        message.ethereumSender !== undefined && (obj.ethereumSender = message.ethereumSender);
        message.cosmosReceiver !== undefined && (obj.cosmosReceiver = message.cosmosReceiver);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        message.data !== undefined && (obj.data = message.data);
        return obj;
    },
    create: function (base) {
        return exports.MsgDepositClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseMsgDepositClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.blockHeight = (_b = object.blockHeight) !== null && _b !== void 0 ? _b : "0";
        message.tokenContract = (_c = object.tokenContract) !== null && _c !== void 0 ? _c : "";
        message.amount = (_d = object.amount) !== null && _d !== void 0 ? _d : "";
        message.ethereumSender = (_e = object.ethereumSender) !== null && _e !== void 0 ? _e : "";
        message.cosmosReceiver = (_f = object.cosmosReceiver) !== null && _f !== void 0 ? _f : "";
        message.orchestrator = (_g = object.orchestrator) !== null && _g !== void 0 ? _g : "";
        message.data = (_h = object.data) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseMsgDepositClaimResponse() {
    return {};
}
exports.MsgDepositClaimResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgDepositClaimResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgDepositClaimResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgDepositClaimResponse();
        return message;
    },
};
function createBaseMsgWithdrawClaim() {
    return { eventNonce: "0", blockHeight: "0", batchNonce: "0", tokenContract: "", orchestrator: "" };
}
exports.MsgWithdrawClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.blockHeight !== "0") {
            writer.uint32(16).uint64(message.blockHeight);
        }
        if (message.batchNonce !== "0") {
            writer.uint32(24).uint64(message.batchNonce);
        }
        if (message.tokenContract !== "") {
            writer.uint32(34).string(message.tokenContract);
        }
        if (message.orchestrator !== "") {
            writer.uint32(42).string(message.orchestrator);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgWithdrawClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.batchNonce = longToString(reader.uint64());
                    break;
                case 4:
                    message.tokenContract = reader.string();
                    break;
                case 5:
                    message.orchestrator = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            batchNonce: isSet(object.batchNonce) ? String(object.batchNonce) : "0",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.batchNonce !== undefined && (obj.batchNonce = message.batchNonce);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        return obj;
    },
    create: function (base) {
        return exports.MsgWithdrawClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseMsgWithdrawClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.blockHeight = (_b = object.blockHeight) !== null && _b !== void 0 ? _b : "0";
        message.batchNonce = (_c = object.batchNonce) !== null && _c !== void 0 ? _c : "0";
        message.tokenContract = (_d = object.tokenContract) !== null && _d !== void 0 ? _d : "";
        message.orchestrator = (_e = object.orchestrator) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMsgWithdrawClaimResponse() {
    return {};
}
exports.MsgWithdrawClaimResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgWithdrawClaimResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgWithdrawClaimResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgWithdrawClaimResponse();
        return message;
    },
};
function createBaseMsgERC20DeployedClaim() {
    return {
        eventNonce: "0",
        blockHeight: "0",
        cosmosDenom: "",
        tokenContract: "",
        name: "",
        symbol: "",
        decimals: "0",
        orchestrator: "",
    };
}
exports.MsgERC20DeployedClaim = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.blockHeight !== "0") {
            writer.uint32(16).uint64(message.blockHeight);
        }
        if (message.cosmosDenom !== "") {
            writer.uint32(26).string(message.cosmosDenom);
        }
        if (message.tokenContract !== "") {
            writer.uint32(34).string(message.tokenContract);
        }
        if (message.name !== "") {
            writer.uint32(42).string(message.name);
        }
        if (message.symbol !== "") {
            writer.uint32(50).string(message.symbol);
        }
        if (message.decimals !== "0") {
            writer.uint32(56).uint64(message.decimals);
        }
        if (message.orchestrator !== "") {
            writer.uint32(66).string(message.orchestrator);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgERC20DeployedClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 3:
                    message.cosmosDenom = reader.string();
                    break;
                case 4:
                    message.tokenContract = reader.string();
                    break;
                case 5:
                    message.name = reader.string();
                    break;
                case 6:
                    message.symbol = reader.string();
                    break;
                case 7:
                    message.decimals = longToString(reader.uint64());
                    break;
                case 8:
                    message.orchestrator = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            cosmosDenom: isSet(object.cosmosDenom) ? String(object.cosmosDenom) : "",
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            name: isSet(object.name) ? String(object.name) : "",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            decimals: isSet(object.decimals) ? String(object.decimals) : "0",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.cosmosDenom !== undefined && (obj.cosmosDenom = message.cosmosDenom);
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.name !== undefined && (obj.name = message.name);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.decimals !== undefined && (obj.decimals = message.decimals);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        return obj;
    },
    create: function (base) {
        return exports.MsgERC20DeployedClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseMsgERC20DeployedClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.blockHeight = (_b = object.blockHeight) !== null && _b !== void 0 ? _b : "0";
        message.cosmosDenom = (_c = object.cosmosDenom) !== null && _c !== void 0 ? _c : "";
        message.tokenContract = (_d = object.tokenContract) !== null && _d !== void 0 ? _d : "";
        message.name = (_e = object.name) !== null && _e !== void 0 ? _e : "";
        message.symbol = (_f = object.symbol) !== null && _f !== void 0 ? _f : "";
        message.decimals = (_g = object.decimals) !== null && _g !== void 0 ? _g : "0";
        message.orchestrator = (_h = object.orchestrator) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseMsgERC20DeployedClaimResponse() {
    return {};
}
exports.MsgERC20DeployedClaimResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgERC20DeployedClaimResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgERC20DeployedClaimResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgERC20DeployedClaimResponse();
        return message;
    },
};
function createBaseMsgCancelSendToEth() {
    return { transactionId: "0", sender: "" };
}
exports.MsgCancelSendToEth = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.transactionId !== "0") {
            writer.uint32(8).uint64(message.transactionId);
        }
        if (message.sender !== "") {
            writer.uint32(18).string(message.sender);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelSendToEth();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.transactionId = longToString(reader.uint64());
                    break;
                case 2:
                    message.sender = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            transactionId: isSet(object.transactionId) ? String(object.transactionId) : "0",
            sender: isSet(object.sender) ? String(object.sender) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.transactionId !== undefined && (obj.transactionId = message.transactionId);
        message.sender !== undefined && (obj.sender = message.sender);
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelSendToEth.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgCancelSendToEth();
        message.transactionId = (_a = object.transactionId) !== null && _a !== void 0 ? _a : "0";
        message.sender = (_b = object.sender) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgCancelSendToEthResponse() {
    return {};
}
exports.MsgCancelSendToEthResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCancelSendToEthResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgCancelSendToEthResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgCancelSendToEthResponse();
        return message;
    },
};
function createBaseMsgSubmitBadSignatureEvidence() {
    return { subject: undefined, signature: "", sender: "" };
}
exports.MsgSubmitBadSignatureEvidence = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subject !== undefined) {
            any_1.Any.encode(message.subject, writer.uint32(10).fork()).ldelim();
        }
        if (message.signature !== "") {
            writer.uint32(18).string(message.signature);
        }
        if (message.sender !== "") {
            writer.uint32(26).string(message.sender);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSubmitBadSignatureEvidence();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subject = any_1.Any.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.signature = reader.string();
                    break;
                case 3:
                    message.sender = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subject: isSet(object.subject) ? any_1.Any.fromJSON(object.subject) : undefined,
            signature: isSet(object.signature) ? String(object.signature) : "",
            sender: isSet(object.sender) ? String(object.sender) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subject !== undefined && (obj.subject = message.subject ? any_1.Any.toJSON(message.subject) : undefined);
        message.signature !== undefined && (obj.signature = message.signature);
        message.sender !== undefined && (obj.sender = message.sender);
        return obj;
    },
    create: function (base) {
        return exports.MsgSubmitBadSignatureEvidence.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgSubmitBadSignatureEvidence();
        message.subject = (object.subject !== undefined && object.subject !== null)
            ? any_1.Any.fromPartial(object.subject)
            : undefined;
        message.signature = (_a = object.signature) !== null && _a !== void 0 ? _a : "";
        message.sender = (_b = object.sender) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgSubmitBadSignatureEvidenceResponse() {
    return {};
}
exports.MsgSubmitBadSignatureEvidenceResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgSubmitBadSignatureEvidenceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgSubmitBadSignatureEvidenceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgSubmitBadSignatureEvidenceResponse();
        return message;
    },
};
function createBaseMsgValsetUpdatedClaim() {
    return {
        eventNonce: "0",
        valsetNonce: "0",
        blockHeight: "0",
        members: [],
        rewardAmount: "",
        rewardToken: "",
        orchestrator: "",
    };
}
exports.MsgValsetUpdatedClaim = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.eventNonce !== "0") {
            writer.uint32(8).uint64(message.eventNonce);
        }
        if (message.valsetNonce !== "0") {
            writer.uint32(16).uint64(message.valsetNonce);
        }
        if (message.blockHeight !== "0") {
            writer.uint32(24).uint64(message.blockHeight);
        }
        try {
            for (var _b = __values(message.members), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.BridgeValidator.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.rewardAmount !== "") {
            writer.uint32(42).string(message.rewardAmount);
        }
        if (message.rewardToken !== "") {
            writer.uint32(50).string(message.rewardToken);
        }
        if (message.orchestrator !== "") {
            writer.uint32(58).string(message.orchestrator);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgValsetUpdatedClaim();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.eventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.valsetNonce = longToString(reader.uint64());
                    break;
                case 3:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 4:
                    message.members.push(types_1.BridgeValidator.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.rewardAmount = reader.string();
                    break;
                case 6:
                    message.rewardToken = reader.string();
                    break;
                case 7:
                    message.orchestrator = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            eventNonce: isSet(object.eventNonce) ? String(object.eventNonce) : "0",
            valsetNonce: isSet(object.valsetNonce) ? String(object.valsetNonce) : "0",
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            members: Array.isArray(object === null || object === void 0 ? void 0 : object.members) ? object.members.map(function (e) { return types_1.BridgeValidator.fromJSON(e); }) : [],
            rewardAmount: isSet(object.rewardAmount) ? String(object.rewardAmount) : "",
            rewardToken: isSet(object.rewardToken) ? String(object.rewardToken) : "",
            orchestrator: isSet(object.orchestrator) ? String(object.orchestrator) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.eventNonce !== undefined && (obj.eventNonce = message.eventNonce);
        message.valsetNonce !== undefined && (obj.valsetNonce = message.valsetNonce);
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        if (message.members) {
            obj.members = message.members.map(function (e) { return e ? types_1.BridgeValidator.toJSON(e) : undefined; });
        }
        else {
            obj.members = [];
        }
        message.rewardAmount !== undefined && (obj.rewardAmount = message.rewardAmount);
        message.rewardToken !== undefined && (obj.rewardToken = message.rewardToken);
        message.orchestrator !== undefined && (obj.orchestrator = message.orchestrator);
        return obj;
    },
    create: function (base) {
        return exports.MsgValsetUpdatedClaim.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseMsgValsetUpdatedClaim();
        message.eventNonce = (_a = object.eventNonce) !== null && _a !== void 0 ? _a : "0";
        message.valsetNonce = (_b = object.valsetNonce) !== null && _b !== void 0 ? _b : "0";
        message.blockHeight = (_c = object.blockHeight) !== null && _c !== void 0 ? _c : "0";
        message.members = ((_d = object.members) === null || _d === void 0 ? void 0 : _d.map(function (e) { return types_1.BridgeValidator.fromPartial(e); })) || [];
        message.rewardAmount = (_e = object.rewardAmount) !== null && _e !== void 0 ? _e : "";
        message.rewardToken = (_f = object.rewardToken) !== null && _f !== void 0 ? _f : "";
        message.orchestrator = (_g = object.orchestrator) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseMsgValsetUpdatedClaimResponse() {
    return {};
}
exports.MsgValsetUpdatedClaimResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgValsetUpdatedClaimResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgValsetUpdatedClaimResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgValsetUpdatedClaimResponse();
        return message;
    },
};
function createBaseMsgUpdateParams() {
    return { authority: "", params: undefined };
}
exports.MsgUpdateParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.authority !== "") {
            writer.uint32(10).string(message.authority);
        }
        if (message.params !== undefined) {
            params_1.Params.encode(message.params, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.authority = reader.string();
                    break;
                case 2:
                    message.params = params_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            authority: isSet(object.authority) ? String(object.authority) : "",
            params: isSet(object.params) ? params_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.authority !== undefined && (obj.authority = message.authority);
        message.params !== undefined && (obj.params = message.params ? params_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgUpdateParams();
        message.authority = (_a = object.authority) !== null && _a !== void 0 ? _a : "";
        message.params = (object.params !== undefined && object.params !== null)
            ? params_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseMsgUpdateParamsResponse() {
    return {};
}
exports.MsgUpdateParamsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateParamsResponse();
        return message;
    },
};
function createBaseMsgBlacklistEthereumAddresses() {
    return { signer: "", blacklistAddresses: [] };
}
exports.MsgBlacklistEthereumAddresses = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.signer !== "") {
            writer.uint32(10).string(message.signer);
        }
        try {
            for (var _b = __values(message.blacklistAddresses), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBlacklistEthereumAddresses();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.signer = reader.string();
                    break;
                case 2:
                    message.blacklistAddresses.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            signer: isSet(object.signer) ? String(object.signer) : "",
            blacklistAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.blacklistAddresses)
                ? object.blacklistAddresses.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.signer !== undefined && (obj.signer = message.signer);
        if (message.blacklistAddresses) {
            obj.blacklistAddresses = message.blacklistAddresses.map(function (e) { return e; });
        }
        else {
            obj.blacklistAddresses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgBlacklistEthereumAddresses.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgBlacklistEthereumAddresses();
        message.signer = (_a = object.signer) !== null && _a !== void 0 ? _a : "";
        message.blacklistAddresses = ((_b = object.blacklistAddresses) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgBlacklistEthereumAddressesResponse() {
    return {};
}
exports.MsgBlacklistEthereumAddressesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgBlacklistEthereumAddressesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgBlacklistEthereumAddressesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgBlacklistEthereumAddressesResponse();
        return message;
    },
};
function createBaseMsgRevokeEthereumBlacklist() {
    return { signer: "", blacklistAddresses: [] };
}
exports.MsgRevokeEthereumBlacklist = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.signer !== "") {
            writer.uint32(10).string(message.signer);
        }
        try {
            for (var _b = __values(message.blacklistAddresses), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRevokeEthereumBlacklist();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.signer = reader.string();
                    break;
                case 2:
                    message.blacklistAddresses.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            signer: isSet(object.signer) ? String(object.signer) : "",
            blacklistAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.blacklistAddresses)
                ? object.blacklistAddresses.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.signer !== undefined && (obj.signer = message.signer);
        if (message.blacklistAddresses) {
            obj.blacklistAddresses = message.blacklistAddresses.map(function (e) { return e; });
        }
        else {
            obj.blacklistAddresses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgRevokeEthereumBlacklist.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgRevokeEthereumBlacklist();
        message.signer = (_a = object.signer) !== null && _a !== void 0 ? _a : "";
        message.blacklistAddresses = ((_b = object.blacklistAddresses) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseMsgRevokeEthereumBlacklistResponse() {
    return {};
}
exports.MsgRevokeEthereumBlacklistResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgRevokeEthereumBlacklistResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgRevokeEthereumBlacklistResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgRevokeEthereumBlacklistResponse();
        return message;
    },
};
var MsgClientImpl = /** @class */ (function () {
    function MsgClientImpl(rpc) {
        this.rpc = rpc;
        this.ValsetConfirm = this.ValsetConfirm.bind(this);
        this.SendToEth = this.SendToEth.bind(this);
        this.RequestBatch = this.RequestBatch.bind(this);
        this.ConfirmBatch = this.ConfirmBatch.bind(this);
        this.DepositClaim = this.DepositClaim.bind(this);
        this.WithdrawClaim = this.WithdrawClaim.bind(this);
        this.ValsetUpdateClaim = this.ValsetUpdateClaim.bind(this);
        this.ERC20DeployedClaim = this.ERC20DeployedClaim.bind(this);
        this.SetOrchestratorAddresses = this.SetOrchestratorAddresses.bind(this);
        this.CancelSendToEth = this.CancelSendToEth.bind(this);
        this.SubmitBadSignatureEvidence = this.SubmitBadSignatureEvidence.bind(this);
        this.UpdateParams = this.UpdateParams.bind(this);
        this.BlacklistEthereumAddresses = this.BlacklistEthereumAddresses.bind(this);
        this.RevokeEthereumBlacklist = this.RevokeEthereumBlacklist.bind(this);
    }
    MsgClientImpl.prototype.ValsetConfirm = function (request, metadata) {
        return this.rpc.unary(exports.MsgValsetConfirmDesc, exports.MsgValsetConfirm.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.SendToEth = function (request, metadata) {
        return this.rpc.unary(exports.MsgSendToEthDesc, exports.MsgSendToEth.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RequestBatch = function (request, metadata) {
        return this.rpc.unary(exports.MsgRequestBatchDesc, exports.MsgRequestBatch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ConfirmBatch = function (request, metadata) {
        return this.rpc.unary(exports.MsgConfirmBatchDesc, exports.MsgConfirmBatch.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.DepositClaim = function (request, metadata) {
        return this.rpc.unary(exports.MsgDepositClaimDesc, exports.MsgDepositClaim.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.WithdrawClaim = function (request, metadata) {
        return this.rpc.unary(exports.MsgWithdrawClaimDesc, exports.MsgWithdrawClaim.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ValsetUpdateClaim = function (request, metadata) {
        return this.rpc.unary(exports.MsgValsetUpdateClaimDesc, exports.MsgValsetUpdatedClaim.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ERC20DeployedClaim = function (request, metadata) {
        return this.rpc.unary(exports.MsgERC20DeployedClaimDesc, exports.MsgERC20DeployedClaim.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.SetOrchestratorAddresses = function (request, metadata) {
        return this.rpc.unary(exports.MsgSetOrchestratorAddressesDesc, exports.MsgSetOrchestratorAddresses.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CancelSendToEth = function (request, metadata) {
        return this.rpc.unary(exports.MsgCancelSendToEthDesc, exports.MsgCancelSendToEth.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.SubmitBadSignatureEvidence = function (request, metadata) {
        return this.rpc.unary(exports.MsgSubmitBadSignatureEvidenceDesc, exports.MsgSubmitBadSignatureEvidence.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateParams = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateParamsDesc, exports.MsgUpdateParams.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.BlacklistEthereumAddresses = function (request, metadata) {
        return this.rpc.unary(exports.MsgBlacklistEthereumAddressesDesc, exports.MsgBlacklistEthereumAddresses.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.RevokeEthereumBlacklist = function (request, metadata) {
        return this.rpc.unary(exports.MsgRevokeEthereumBlacklistDesc, exports.MsgRevokeEthereumBlacklist.fromPartial(request), metadata);
    };
    return MsgClientImpl;
}());
exports.MsgClientImpl = MsgClientImpl;
exports.MsgDesc = { serviceName: "injective.peggy.v1.Msg" };
exports.MsgValsetConfirmDesc = {
    methodName: "ValsetConfirm",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgValsetConfirm.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgValsetConfirmResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgSendToEthDesc = {
    methodName: "SendToEth",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgSendToEth.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgSendToEthResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRequestBatchDesc = {
    methodName: "RequestBatch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRequestBatch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRequestBatchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgConfirmBatchDesc = {
    methodName: "ConfirmBatch",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgConfirmBatch.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgConfirmBatchResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgDepositClaimDesc = {
    methodName: "DepositClaim",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgDepositClaim.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgDepositClaimResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgWithdrawClaimDesc = {
    methodName: "WithdrawClaim",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgWithdrawClaim.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgWithdrawClaimResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgValsetUpdateClaimDesc = {
    methodName: "ValsetUpdateClaim",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgValsetUpdatedClaim.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgValsetUpdatedClaimResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgERC20DeployedClaimDesc = {
    methodName: "ERC20DeployedClaim",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgERC20DeployedClaim.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgERC20DeployedClaimResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgSetOrchestratorAddressesDesc = {
    methodName: "SetOrchestratorAddresses",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgSetOrchestratorAddresses.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgSetOrchestratorAddressesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCancelSendToEthDesc = {
    methodName: "CancelSendToEth",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCancelSendToEth.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCancelSendToEthResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgSubmitBadSignatureEvidenceDesc = {
    methodName: "SubmitBadSignatureEvidence",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgSubmitBadSignatureEvidence.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgSubmitBadSignatureEvidenceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateParamsDesc = {
    methodName: "UpdateParams",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateParams.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgBlacklistEthereumAddressesDesc = {
    methodName: "BlacklistEthereumAddresses",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgBlacklistEthereumAddresses.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgBlacklistEthereumAddressesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgRevokeEthereumBlacklistDesc = {
    methodName: "RevokeEthereumBlacklist",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgRevokeEthereumBlacklist.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgRevokeEthereumBlacklistResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
