"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InjectiveDirectEthSecp256k1Wallet = exports.InjectiveEthSecp256k1Wallet = void 0;
var EthSecp256k1Wallet_js_1 = require("./EthSecp256k1Wallet.js");
Object.defineProperty(exports, "InjectiveEthSecp256k1Wallet", { enumerable: true, get: function () { return EthSecp256k1Wallet_js_1.EthSecp256k1Wallet; } });
var DirectEthSecp256k1Wallet_js_1 = require("./DirectEthSecp256k1Wallet.js");
Object.defineProperty(exports, "InjectiveDirectEthSecp256k1Wallet", { enumerable: true, get: function () { return DirectEthSecp256k1Wallet_js_1.DirectEthSecp256k1Wallet; } });
