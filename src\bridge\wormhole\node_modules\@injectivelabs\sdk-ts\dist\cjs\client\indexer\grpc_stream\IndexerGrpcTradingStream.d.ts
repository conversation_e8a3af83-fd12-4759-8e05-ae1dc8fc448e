import { Subscription } from 'rxjs';
import { InjectiveTradingRpc } from '@injectivelabs/indexer-proto-ts';
import { StreamStatusResponse } from '../types/index.js';
/**
 * @category Indexer Grid Strategy Grpc Stream
 */
export declare class IndexerGrpcTradingStream {
    protected client: InjectiveTradingRpc.InjectiveTradingRPCClientImpl;
    constructor(endpoint: string);
    streamGridStrategies({ marketId, callback, onEndCallback, accountAddresses, onStatusCallback, }: {
        marketId?: string;
        accountAddresses?: string[];
        onEndCallback?: (status?: StreamStatusResponse) => void;
        onStatusCallback?: (status: StreamStatusResponse) => void;
        callback: (response: InjectiveTradingRpc.StreamStrategyResponse) => void;
    }): Subscription;
}
