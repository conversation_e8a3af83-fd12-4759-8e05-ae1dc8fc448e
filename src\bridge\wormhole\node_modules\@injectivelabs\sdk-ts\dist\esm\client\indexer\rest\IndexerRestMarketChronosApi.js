import { HttpRequestException, UnspecifiedErrorCode, } from '@injectivelabs/exceptions';
import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { IndexerModule } from '../types/index.js';
export class IndexerRestMarketChronosApi extends BaseRestConsumer {
    async fetchMarketsHistory({ marketIds, resolution, countback, }) {
        const path = `history`;
        const queryMarketIds = marketIds.map((marketId) => ({
            marketIDs: marketId,
        }));
        const params = [
            ...queryMarketIds,
            { resolution: String(resolution) },
            { countback: String(countback) },
        ];
        const stringifiedParams = params
            .map((param) => new URLSearchParams(param))
            .join('&');
        const pathWithParams = `${path}?${stringifiedParams}`;
        try {
            const { data } = await this.retry(() => this.get(pathWithParams));
            return data;
        }
        catch (e) {
            if (e instanceof HttpRequestException) {
                throw e;
            }
            throw new HttpRequestException(new Error(e), {
                code: UnspecifiedErrorCode,
                context: `${this.endpoint}/${pathWithParams}`,
                contextModule: IndexerModule.ChronosMarkets,
            });
        }
    }
}
