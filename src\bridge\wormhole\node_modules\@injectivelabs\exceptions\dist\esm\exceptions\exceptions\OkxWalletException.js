import { ConcreteException } from '../base.js';
import { ErrorType } from '../types/index.js';
import { mapMetamaskMessage } from '../utils/maps.js';
const removeOkxWalletFromErrorString = (message) => message
    .replaceAll('OkxWallet', '')
    .replaceAll('Okx', '')
    .replaceAll('OkxWallet:', '');
export class OkxWalletException extends ConcreteException {
    static errorClass = 'OkxWalletException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage(mapMetamaskMessage(removeOkxWalletFromErrorString(message)));
        this.setName(OkxWalletException.errorClass);
    }
}
