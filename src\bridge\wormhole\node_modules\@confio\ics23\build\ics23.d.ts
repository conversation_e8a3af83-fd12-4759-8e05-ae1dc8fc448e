import { ics23 } from "./generated/codecimpl";
import { CommitmentRoot } from "./proofs";
/**
 * verifyMembership ensures proof is (contains) a valid existence proof for the given
 */
export declare function verifyMembership(proof: ics23.ICommitmentProof, spec: ics23.IProofSpec, root: CommitmentRoot, key: Uint8Array, value: Uint8Array): boolean;
/**
 * verifyNonMembership ensures proof is (contains) a valid non-existence proof for the given key
 */
export declare function verifyNonMembership(proof: ics23.ICommitmentProof, spec: ics23.IProofSpec, root: CommitmentRoot, key: Uint8Array): boolean;
/**
 * batchVerifyMembership ensures proof is (contains) a valid existence proof for the given
 */
export declare function batchVerifyMembership(proof: ics23.ICommitmentProof, spec: ics23.IProofSpec, root: CommitmentRoot, items: Map<Uint8Array, Uint8Array>): boolean;
/**
 * batchVerifyNonMembership ensures proof is (contains) a valid existence proof for the given
 */
export declare function batchVerifyNonMembership(proof: ics23.ICommitmentProof, spec: ics23.IProofSpec, root: CommitmentRoot, keys: readonly Uint8Array[]): boolean;
