import _m0 from "protobufjs/minimal.js";
import { Duration } from "../../../google/protobuf/duration";
export declare const protobufPackage = "cometbft.types.v1";
/**
 * ConsensusParams contains consensus critical parameters that determine the
 * validity of blocks.
 */
export interface ConsensusParams {
    block: BlockParams | undefined;
    evidence: EvidenceParams | undefined;
    validator: ValidatorParams | undefined;
    version: VersionParams | undefined;
    /**
     * Use FeatureParams.vote_extensions_enable_height instead
     *
     * @deprecated
     */
    abci: ABCIParams | undefined;
    synchrony: SynchronyParams | undefined;
    feature: FeatureParams | undefined;
}
/** BlockParams define limits on the block size and gas. */
export interface BlockParams {
    /**
     * Maximum size of a block, in bytes.
     *
     * Must be greater or equal to -1 and cannot be greater than the hard-coded
     * maximum block size, which is 100MB.
     *
     * If set to -1, the limit is the hard-coded maximum block size.
     */
    maxBytes: string;
    /**
     * Maximum gas wanted by transactions included in a block.
     *
     * Must be greater or equal to -1. If set to -1, no limit is enforced.
     */
    maxGas: string;
}
/** EvidenceParams determine the validity of evidences of Byzantine behavior. */
export interface EvidenceParams {
    /**
     * Maximum age of evidence, in blocks.
     *
     * The recommended formula for calculating it is max_age_duration / {average
     * block time}.
     */
    maxAgeNumBlocks: string;
    /**
     * Maximum age of evidence, in time.
     *
     * The recommended value of is should correspond to the application's
     * "unbonding period" or other similar mechanism for handling
     * Nothing-At-Stake attacks.
     * See: https://github.com/ethereum/wiki/wiki/Proof-of-Stake-FAQ#what-is-the-nothing-at-stake-problem-and-how-can-it-be-fixed.
     */
    maxAgeDuration: Duration | undefined;
    /**
     * Maximum size in bytes of evidence allowed to be included in a block.
     *
     * It should fall comfortably under the maximum size of a block.
     */
    maxBytes: string;
}
/**
 * ValidatorParams restrict the public key types validators can use.
 *
 * NOTE: uses ABCI public keys naming, not Amino names.
 */
export interface ValidatorParams {
    pubKeyTypes: string[];
}
/** VersionParams contain the version of specific components of CometBFT. */
export interface VersionParams {
    /**
     * The ABCI application version.
     *
     * It was named app_version in CometBFT 0.34.
     */
    app: string;
}
/**
 * HashedParams is a subset of ConsensusParams.
 *
 * It is hashed into the Header.ConsensusHash.
 */
export interface HashedParams {
    blockMaxBytes: string;
    blockMaxGas: string;
}
/**
 * SynchronyParams determine the validity of block timestamps.
 *
 * These parameters are part of the Proposer-Based Timestamps (PBTS) algorithm.
 * For more information on the relationship of the synchrony parameters to
 * block timestamps validity, refer to the PBTS specification:
 * https://github.com/tendermint/spec/blob/master/spec/consensus/proposer-based-timestamp/README.md
 */
export interface SynchronyParams {
    /**
     * Bound for how skewed a proposer's clock may be from any validator on the
     * network while still producing valid proposals.
     */
    precision: Duration | undefined;
    /**
     * Bound for how long a proposal message may take to reach all validators on
     * a network and still be considered valid.
     */
    messageDelay: Duration | undefined;
}
/** FeatureParams configure the height from which features of CometBFT are enabled. */
export interface FeatureParams {
    /**
     * Height during which vote extensions will be enabled.
     *
     * A value of 0 means vote extensions are disabled. A value > 0 denotes
     * the height at which vote extensions will be (or have been) enabled.
     *
     * During the specified height, and for all subsequent heights, precommit
     * messages that do not contain valid extension data will be considered
     * invalid. Prior to this height, or when this height is set to 0, vote
     * extensions will not be used or accepted by validators on the network.
     *
     * Once enabled, vote extensions will be created by the application in
     * ExtendVote, validated by the application in VerifyVoteExtension, and
     * used by the application in PrepareProposal, when proposing the next block.
     *
     * Cannot be set to heights lower or equal to the current blockchain height.
     */
    voteExtensionsEnableHeight: string | undefined;
    /**
     * Height at which Proposer-Based Timestamps (PBTS) will be enabled.
     *
     * A value of 0 means PBTS is disabled. A value > 0 denotes the height at
     * which PBTS will be (or has been) enabled.
     *
     * From the specified height, and for all subsequent heights, the PBTS
     * algorithm will be used to produce and validate block timestamps. Prior to
     * this height, or when this height is set to 0, the legacy BFT Time
     * algorithm is used to produce and validate timestamps.
     *
     * Cannot be set to heights lower or equal to the current blockchain height.
     */
    pbtsEnableHeight: string | undefined;
}
/**
 * ABCIParams is deprecated and its contents moved to FeatureParams
 *
 * @deprecated
 */
export interface ABCIParams {
    /**
     * vote_extensions_enable_height has been deprecated.
     * Instead, use FeatureParams.vote_extensions_enable_height.
     */
    voteExtensionsEnableHeight: string;
}
export declare const ConsensusParams: {
    encode(message: ConsensusParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConsensusParams;
    fromJSON(object: any): ConsensusParams;
    toJSON(message: ConsensusParams): unknown;
    create(base?: DeepPartial<ConsensusParams>): ConsensusParams;
    fromPartial(object: DeepPartial<ConsensusParams>): ConsensusParams;
};
export declare const BlockParams: {
    encode(message: BlockParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BlockParams;
    fromJSON(object: any): BlockParams;
    toJSON(message: BlockParams): unknown;
    create(base?: DeepPartial<BlockParams>): BlockParams;
    fromPartial(object: DeepPartial<BlockParams>): BlockParams;
};
export declare const EvidenceParams: {
    encode(message: EvidenceParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EvidenceParams;
    fromJSON(object: any): EvidenceParams;
    toJSON(message: EvidenceParams): unknown;
    create(base?: DeepPartial<EvidenceParams>): EvidenceParams;
    fromPartial(object: DeepPartial<EvidenceParams>): EvidenceParams;
};
export declare const ValidatorParams: {
    encode(message: ValidatorParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ValidatorParams;
    fromJSON(object: any): ValidatorParams;
    toJSON(message: ValidatorParams): unknown;
    create(base?: DeepPartial<ValidatorParams>): ValidatorParams;
    fromPartial(object: DeepPartial<ValidatorParams>): ValidatorParams;
};
export declare const VersionParams: {
    encode(message: VersionParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VersionParams;
    fromJSON(object: any): VersionParams;
    toJSON(message: VersionParams): unknown;
    create(base?: DeepPartial<VersionParams>): VersionParams;
    fromPartial(object: DeepPartial<VersionParams>): VersionParams;
};
export declare const HashedParams: {
    encode(message: HashedParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HashedParams;
    fromJSON(object: any): HashedParams;
    toJSON(message: HashedParams): unknown;
    create(base?: DeepPartial<HashedParams>): HashedParams;
    fromPartial(object: DeepPartial<HashedParams>): HashedParams;
};
export declare const SynchronyParams: {
    encode(message: SynchronyParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SynchronyParams;
    fromJSON(object: any): SynchronyParams;
    toJSON(message: SynchronyParams): unknown;
    create(base?: DeepPartial<SynchronyParams>): SynchronyParams;
    fromPartial(object: DeepPartial<SynchronyParams>): SynchronyParams;
};
export declare const FeatureParams: {
    encode(message: FeatureParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeatureParams;
    fromJSON(object: any): FeatureParams;
    toJSON(message: FeatureParams): unknown;
    create(base?: DeepPartial<FeatureParams>): FeatureParams;
    fromPartial(object: DeepPartial<FeatureParams>): FeatureParams;
};
export declare const ABCIParams: {
    encode(message: ABCIParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ABCIParams;
    fromJSON(object: any): ABCIParams;
    toJSON(message: ABCIParams): unknown;
    create(base?: DeepPartial<ABCIParams>): ABCIParams;
    fromPartial(object: DeepPartial<ABCIParams>): ABCIParams;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
