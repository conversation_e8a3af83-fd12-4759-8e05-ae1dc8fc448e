import { InjectivePermissionsV1Beta1Tx, InjectivePermissionsV1Beta1Permissions } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { PermissionRole, PermissionActorRoles, PermissionRoleManager, PermissionPolicyStatus, PermissionPolicyManagerCapability } from './../../../../client/chain/types/permissions.js';
export declare namespace MsgCreateNamespace {
    interface Params {
        sender: string;
        namespace: {
            denom: string;
            contractHook: string;
            rolePermissions: PermissionRole[];
            actorRoles: PermissionActorRoles[];
            roleManagers: PermissionRoleManager[];
            policyStatuses: PermissionPolicyStatus[];
            policyManagerCapabilities: PermissionPolicyManagerCapability[];
        };
    }
    type Proto = InjectivePermissionsV1Beta1Tx.MsgCreateNamespace;
}
/**
 * @category Messages
 */
export default class MsgCreateNamespace extends MsgBase<MsgCreateNamespace.Params, MsgCreateNamespace.Proto> {
    static fromJSON(params: MsgCreateNamespace.Params): MsgCreateNamespace;
    toProto(): InjectivePermissionsV1Beta1Tx.MsgCreateNamespace;
    toData(): {
        sender: string;
        namespace: InjectivePermissionsV1Beta1Permissions.Namespace | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            namespace: {
                policyStatuses: InjectivePermissionsV1Beta1Permissions.PolicyStatus[];
                policyManagerCapabilities: InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability[];
                denom?: string | undefined;
                contractHook?: string | undefined;
                rolePermissions?: InjectivePermissionsV1Beta1Permissions.Role[] | undefined;
                actorRoles?: InjectivePermissionsV1Beta1Permissions.ActorRoles[] | undefined;
                roleManagers?: InjectivePermissionsV1Beta1Permissions.RoleManager[] | undefined;
            };
            sender: string;
        };
    };
    toWeb3Gw(): {
        namespace: {
            policyStatuses: InjectivePermissionsV1Beta1Permissions.PolicyStatus[];
            policyManagerCapabilities: InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability[];
            denom?: string | undefined;
            contractHook?: string | undefined;
            rolePermissions?: InjectivePermissionsV1Beta1Permissions.Role[] | undefined;
            actorRoles?: InjectivePermissionsV1Beta1Permissions.ActorRoles[] | undefined;
            roleManagers?: InjectivePermissionsV1Beta1Permissions.RoleManager[] | undefined;
        };
        sender: string;
        '@type': string;
    };
    toEip712(): never;
    toEip712V2(): {
        namespace: {
            denom: any;
            contract_hook: any;
            role_permissions: any;
            actor_roles: any;
            role_managers: any;
            policy_statuses: any;
            policy_manager_capabilities: any;
        };
        sender: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectivePermissionsV1Beta1Tx.MsgCreateNamespace;
    };
    toBinary(): Uint8Array;
}
