#!/usr/bin/env node
import { program } from "commander";
import { parseCommandOptions } from "./utils/parseCommandOptions.js";
import { runCLI } from "./tasks/run.js";
program
    .name("aptos")
    .helpOption(false)
    .option("-i, --install", "install the latest version of the CLI")
    .option("-u, --update", "update the CLI to the latest version")
    .allowUnknownOption();
program.parse(process.argv);
const main = async () => {
    const options = {
        install: program.opts().install,
        update: program.opts().update,
    };
    const unknownOptions = program.args;
    if (process.argv.includes("--help")) {
        return runCLI(unknownOptions);
    }
    await parseCommandOptions(options, unknownOptions);
};
main().catch(console.error);
//# sourceMappingURL=aptos.js.map