/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.staking.module.v1";
function createBaseModule() {
    return { hooksOrder: [], authority: "", bech32PrefixValidator: "", bech32PrefixConsensus: "" };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.hooksOrder) {
            writer.uint32(10).string(v);
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        if (message.bech32PrefixValidator !== "") {
            writer.uint32(26).string(message.bech32PrefixValidator);
        }
        if (message.bech32PrefixConsensus !== "") {
            writer.uint32(34).string(message.bech32PrefixConsensus);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hooksOrder.push(reader.string());
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                case 3:
                    message.bech32PrefixValidator = reader.string();
                    break;
                case 4:
                    message.bech32PrefixConsensus = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            hooksOrder: Array.isArray(object?.hooksOrder) ? object.hooksOrder.map((e) => String(e)) : [],
            authority: isSet(object.authority) ? String(object.authority) : "",
            bech32PrefixValidator: isSet(object.bech32PrefixValidator) ? String(object.bech32PrefixValidator) : "",
            bech32PrefixConsensus: isSet(object.bech32PrefixConsensus) ? String(object.bech32PrefixConsensus) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.hooksOrder) {
            obj.hooksOrder = message.hooksOrder.map((e) => e);
        }
        else {
            obj.hooksOrder = [];
        }
        message.authority !== undefined && (obj.authority = message.authority);
        message.bech32PrefixValidator !== undefined && (obj.bech32PrefixValidator = message.bech32PrefixValidator);
        message.bech32PrefixConsensus !== undefined && (obj.bech32PrefixConsensus = message.bech32PrefixConsensus);
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.hooksOrder = object.hooksOrder?.map((e) => e) || [];
        message.authority = object.authority ?? "";
        message.bech32PrefixValidator = object.bech32PrefixValidator ?? "";
        message.bech32PrefixConsensus = object.bech32PrefixConsensus ?? "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
