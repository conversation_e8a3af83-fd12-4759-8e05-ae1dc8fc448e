{"name": "@aptos-labs/ts-sdk", "description": "Aptos TypeScript SDK", "packageManager": "pnpm@8.15.1", "license": "Apache-2.0", "engines": {"node": ">=20.0.0"}, "bugs": {"url": "https://github.com/aptos-labs/aptos-ts-sdk/issues/new/choose"}, "homepage": "https://aptos-labs.github.io/aptos-ts-sdk/", "main": "dist/common/index.js", "module": "dist/esm/index.mjs", "exports": {".": {"source": "./src/index.ts", "require": {"types": "./dist/common/index.d.ts", "default": "./dist/common/index.js"}, "import": {"types": "./dist/esm/index.d.mts", "default": "./dist/esm/index.mjs"}}, "./dist/common/cli/index.js": "./dist/common/cli/index.js", "./dist/esm/cli/index.mjs": "./dist/esm/cli/index.mjs", "./package.json": "./package.json"}, "files": ["dist", "src"], "scripts": {"build:clean": "rm -rf dist", "build": "pnpm build:clean && tsup", "prepublishOnly": "pnpm run build", "_fmt": "prettier 'src/**/*.ts' 'tests/**/*.ts' 'examples/**/*.js' 'examples/**/*.ts' 'confidential-assets/**/*.ts' 'eslint.config.cjs'", "fmt": "pnpm _fmt --write", "lint": "eslint '**/*.{cts,mts,ts}'", "test": "pnpm jest", "unit-test": "pnpm jest tests/unit", "e2e-test": "pnpm jest tests/e2e", "indexer-codegen": "graphql-codegen --config ./src/types/codegen.yaml && pnpm fmt", "doc": "scripts/generateDocs.sh", "check-version": "scripts/checkVersion.sh", "update-version": "scripts/updateVersion.sh && pnpm doc", "spec": "pnpm build && pnpm _spec", "_spec": "cucumber-js -p default"}, "dependencies": {"@aptos-labs/aptos-cli": "^1.0.2", "@aptos-labs/aptos-client": "^2.0.0", "@noble/curves": "^1.6.0", "@noble/hashes": "^1.5.0", "@scure/bip32": "^1.4.0", "@scure/bip39": "^1.3.0", "eventemitter3": "^5.0.1", "form-data": "^4.0.0", "js-base64": "^3.7.7", "jwt-decode": "^4.0.0", "poseidon-lite": "^0.2.0"}, "devDependencies": {"@babel/traverse": "^7.26.10", "@cucumber/cucumber": "^11.2.0", "@eslint/eslintrc": "^3.3.1", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/import-types-preset": "^3.0.0", "@graphql-codegen/typescript": "^4.1.5", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@graphql-codegen/typescript-operations": "^4.5.1", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.17.25", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "dotenv": "^16.4.7", "eslint": "^9.23.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "graphql": "^16.10.0", "globals": "^16.0.0", "graphql-request": "^7.1.2", "jest": "^29.7.0", "jsonwebtoken": "^9.0.2", "prettier": "^3.5.3", "tree-kill": "^1.2.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsup": "^8.4.0", "typedoc": "^0.28.1", "typedoc-plugin-missing-exports": "^4.0.0", "typescript": "^5.8.2"}, "version": "2.0.1"}