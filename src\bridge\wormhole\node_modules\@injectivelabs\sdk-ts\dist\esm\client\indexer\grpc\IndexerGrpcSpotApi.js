import { GeneralException, UnspecifiedErrorCode, grpcErrorCodeToErrorCode, GrpcUnaryRequestException, } from '@injectivelabs/exceptions';
import { InjectiveSpotExchangeRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { IndexerModule } from '../types/index.js';
import { IndexerGrpcSpotTransformer } from '../transformers/index.js';
/**
 * @category Indexer Grpc API
 */
export class IndexerGrpcSpotApi extends BaseGrpcConsumer {
    module = IndexerModule.Spot;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client =
            new InjectiveSpotExchangeRpc.InjectiveSpotExchangeRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchMarkets(params) {
        const { baseDenom, marketStatus, quoteDenom, marketStatuses } = params || {};
        const request = InjectiveSpotExchangeRpc.MarketsRequest.create();
        if (baseDenom) {
            request.baseDenom = baseDenom;
        }
        if (marketStatus) {
            request.marketStatus = marketStatus;
        }
        if (marketStatuses) {
            request.marketStatuses = marketStatuses;
        }
        if (quoteDenom) {
            request.quoteDenom = quoteDenom;
        }
        try {
            const response = await this.retry(() => this.client.Markets(request, this.metadata));
            return IndexerGrpcSpotTransformer.marketsResponseToMarkets(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Markets',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Markets',
                contextModule: this.module,
            });
        }
    }
    async fetchMarket(marketId) {
        const request = InjectiveSpotExchangeRpc.MarketRequest.create();
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.Market(request, this.metadata));
            return IndexerGrpcSpotTransformer.marketResponseToMarket(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Market',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Market',
                contextModule: this.module,
            });
        }
    }
    /** @deprecated - use fetchOrderbookV2 */
    async fetchOrderbook(_marketId) {
        throw new GeneralException(new Error('deprecated - use fetchOrderbookV2'));
    }
    async fetchOrders(params) {
        const { cid, tradeId, marketId, marketIds, orderSide, pagination, subaccountId, } = params || {};
        const request = InjectiveSpotExchangeRpc.OrdersRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (orderSide) {
            request.orderSide = orderSide;
        }
        if (cid) {
            request.cid = cid;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        // if (isConditional !== undefined) {
        //   request.isConditional = isConditional
        // }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.Orders(request, this.metadata));
            return IndexerGrpcSpotTransformer.ordersResponseToOrders(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Orders',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Orders',
                contextModule: this.module,
            });
        }
    }
    async fetchOrderHistory(params) {
        const { cid, state, tradeId, marketId, direction, marketIds, orderTypes, pagination, subaccountId, executionTypes, } = params || {};
        const request = InjectiveSpotExchangeRpc.OrdersHistoryRequest.create();
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (orderTypes) {
            request.orderTypes = orderTypes;
        }
        if (executionTypes) {
            request.executionTypes = executionTypes;
        }
        if (direction) {
            request.direction = direction;
        }
        if (state) {
            request.state = state;
        }
        if (cid) {
            request.cid = cid;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        /*
        if (isConditional !== undefined) {
          request.isConditional =isConditional
        }
        */
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.OrdersHistory(request, this.metadata));
            return IndexerGrpcSpotTransformer.orderHistoryResponseToOrderHistory(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'OrdersHistory',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'OrdersHistory',
                contextModule: this.module,
            });
        }
    }
    async fetchTrades(params) {
        const { endTime, tradeId, marketId, startTime, direction, marketIds, pagination, subaccountId, executionSide, executionTypes, accountAddress, cid, } = params || {};
        const request = InjectiveSpotExchangeRpc.TradesRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (accountAddress) {
            request.accountAddress = accountAddress;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        if (executionTypes) {
            request.executionTypes = executionTypes;
        }
        if (executionSide) {
            request.executionSide = executionSide;
        }
        if (direction) {
            request.direction = direction;
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (cid) {
            request.cid = cid;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.Trades(request, this.metadata));
            return IndexerGrpcSpotTransformer.tradesResponseToTrades(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Trades',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Trades',
                contextModule: this.module,
            });
        }
    }
    async fetchSubaccountOrdersList(params) {
        const { subaccountId, marketId, pagination } = params || {};
        const request = InjectiveSpotExchangeRpc.SubaccountOrdersListRequest.create();
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.SubaccountOrdersList(request, this.metadata));
            return IndexerGrpcSpotTransformer.ordersResponseToOrders(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'SubaccountOrdersList',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'SubaccountOrdersList',
                contextModule: this.module,
            });
        }
    }
    async fetchSubaccountTradesList(params) {
        const { subaccountId, marketId, direction, executionType, pagination } = params || {};
        const request = InjectiveSpotExchangeRpc.SubaccountTradesListRequest.create();
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (direction) {
            request.direction = direction;
        }
        if (executionType) {
            request.executionType = executionType;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.SubaccountTradesList(request, this.metadata));
            return IndexerGrpcSpotTransformer.subaccountTradesListResponseToTradesList(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'SubaccountTradesList',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'SubaccountTradesList',
                contextModule: this.module,
            });
        }
    }
    /** @deprecated - use fetchOrderbooksV2 */
    async fetchOrderbooks(_marketIds) {
        throw new GeneralException(new Error('deprecated - use fetchOrderbooksV2'));
    }
    async fetchOrderbooksV2(marketIds) {
        const request = InjectiveSpotExchangeRpc.OrderbooksV2Request.create();
        if (marketIds.length > 0) {
            request.marketIds = marketIds;
        }
        try {
            const response = await this.retry(() => this.client.OrderbooksV2(request, this.metadata));
            return IndexerGrpcSpotTransformer.orderbooksV2ResponseToOrderbooksV2(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'OrderbooksV2',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'OrderbooksV2',
                contextModule: this.module,
            });
        }
    }
    async fetchOrderbookV2(marketId) {
        const request = InjectiveSpotExchangeRpc.OrderbookV2Request.create();
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.OrderbookV2(request, this.metadata));
            return IndexerGrpcSpotTransformer.orderbookV2ResponseToOrderbookV2(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'OrderbookV2',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: '',
                contextModule: this.module,
            });
        }
    }
    async fetchAtomicSwapHistory(params) {
        const { address, contractAddress, pagination } = params || {};
        const request = InjectiveSpotExchangeRpc.AtomicSwapHistoryRequest.create();
        request.address = address;
        request.contractAddress = contractAddress;
        if (pagination) {
            if (pagination.fromNumber !== undefined) {
                request.fromNumber = pagination.fromNumber;
            }
            if (pagination.toNumber !== undefined) {
                request.toNumber = pagination.toNumber;
            }
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip;
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.AtomicSwapHistory(request, this.metadata));
            return IndexerGrpcSpotTransformer.grpcAtomicSwapHistoryListToAtomicSwapHistoryList(response);
        }
        catch (e) {
            if (e instanceof InjectiveSpotExchangeRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'AtomicSwapHistory',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'AtomicSwapHistory',
                contextModule: this.module,
            });
        }
    }
}
