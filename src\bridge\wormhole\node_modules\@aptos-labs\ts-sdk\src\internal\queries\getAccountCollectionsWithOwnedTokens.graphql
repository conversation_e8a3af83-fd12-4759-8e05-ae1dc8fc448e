query getAccountCollectionsWithOwnedTokens(
  $where_condition: current_collection_ownership_v2_view_bool_exp!
  $offset: Int
  $limit: Int
  $order_by: [current_collection_ownership_v2_view_order_by!]
) {
  current_collection_ownership_v2_view(where: $where_condition, offset: $offset, limit: $limit, order_by: $order_by) {
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      mutable_description
      max_supply
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
    collection_id
    collection_name
    collection_uri
    creator_address
    distinct_tokens
    last_transaction_version
    owner_address
    single_token_uri
  }
}
