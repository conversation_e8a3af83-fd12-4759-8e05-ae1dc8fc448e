import { BigNumberInBase } from '@injectivelabs/utils';
export class IndexerGrpcReferralTransformer {
    static referrerDetailsResponseToReferrerDetails(address, response) {
        return {
            referrerAddress: address,
            invitees: response.invitees,
            referrerCode: response.referrerCode,
            totalCommission: new BigNumberInBase(response.totalCommission),
            totalTradingVolume: new BigNumberInBase(response.totalTradingVolume),
        };
    }
    static inviteeDetailsResponseToInviteeDetails(response) {
        return response;
    }
    static referrerByCodeResponseToReferrerByCode(response) {
        return response?.referrerAddress || '';
    }
}
