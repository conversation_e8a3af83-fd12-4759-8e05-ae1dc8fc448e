#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
套利执行脚本 - 执行CEX和DEX之间的套利交易
"""

import os
import sys
import time
import asyncio
from typing import Dict, Any, Tuple
from decimal import Decimal

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.cex.gate.client import GateClient
from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.logger import logger
from scripts.dex.local_swap_simulator import LocalSwapSimulator

# 最小利润阈值（USDT）
MIN_PROFIT_THRESHOLD = 0.01

# Gas倍数设置
DEX_GAS_MULTIPLIER = 2.0

class ArbitrageExecutor:
    """套利执行器"""
    
    def __init__(self):
        """初始化套利执行器"""
        self.gate_client = GateClient()
        self.dex_client = ArthSwapClient()
        self.simulator = LocalSwapSimulator("USDT", "WASTR")
    
    async def execute_cex_buy_dex_sell(self, amount_usdt: float, astr_amount: float) -> Dict[str, Any]:
        """
        执行CEX买入DEX卖出套利（并发执行）
        
        Args:
            amount_usdt: 要使用的USDT数量
            astr_amount: 要交易的ASTR数量
            
        Returns:
            交易结果
        """
        try:
            # 1. 确认CEX实际可买入数量
            actual_amount, cex_price = await self._confirm_cex_buy_amount(
                "ASTR/USDT", amount_usdt, astr_amount
            )
            
            if actual_amount == 0 or cex_price == 0:
                return {
                    'status': 'cancelled',
                    'reason': '无法确认CEX可买入数量'
                }
            
            if abs(actual_amount - astr_amount) / astr_amount > 0.01:  # 1%偏差
                logger.warning(f"CEX实际可买入数量 {actual_amount} 与预期数量 {astr_amount} 差异较大")
            
            # 2. 使用实际数量预览DEX卖出
            usdt_received, gas_cost = await self._preview_dex_sell(actual_amount)
            if usdt_received == 0:
                return {
                    'status': 'cancelled',
                    'reason': 'DEX预览交易失败'
                }
            
            # 3. 重新计算预期利润
            cex_cost = actual_amount * cex_price
            expected_profit = usdt_received - cex_cost - gas_cost
            
            if expected_profit <= MIN_PROFIT_THRESHOLD:
                return {
                    'status': 'cancelled',
                    'reason': f'实际利润 {expected_profit:.6f} USDT 低于最小阈值',
                    'details': {
                        'cex_cost': cex_cost,
                        'dex_received': usdt_received,
                        'gas_cost': gas_cost
                    }
                }
            
            logger.info(f"交易预览 - CEX买入: {actual_amount:.4f} ASTR @ {cex_price:.6f}")
            logger.info(f"交易预览 - DEX卖出: 预计获得 {usdt_received:.4f} USDT")
            logger.info(f"交易预览 - 预期利润: {expected_profit:.6f} USDT")
            
            # 4. 并发执行交易
            logger.info(f"开始并发执行CEX买入和DEX卖出套利，数量: {actual_amount} ASTR")
            
            cex_task = asyncio.create_task(
                self._place_cex_buy_order("ASTR/USDT", actual_amount, cex_price)
            )
            dex_task = asyncio.create_task(
                self._execute_dex_sell(actual_amount)
            )
            
            # 等待两个任务完成
            done, pending = await asyncio.wait(
                [cex_task, dex_task],
                return_when=asyncio.ALL_COMPLETED
            )
            
            # 获取结果
            cex_order = await cex_task
            dex_result = await dex_task
            
            # 检查交易结果
            cex_success = cex_order and cex_order.get('status') == 'closed'
            dex_success = dex_result and dex_result.get('status') == 'success'
            
            # 根据不同情况处理
            if cex_success and dex_success:
                # 两边都成功，计算实际利润
                actual_astr_amount = float(cex_order['filled'])
                actual_profit = dex_result['amount_received'] - (actual_astr_amount * cex_price)
                
                return {
                    'status': 'success',
                    'cex_order': cex_order,
                    'dex_result': dex_result,
                    'profit': actual_profit,
                    'expected_profit': expected_profit,
                    'astr_amount': actual_astr_amount
                }
            
            # 处理部分成功的情况
            if cex_success and not dex_success:
                logger.error("CEX买入成功但DEX卖出失败，尝试在CEX卖出")
                rollback_result = await self._rollback_cex_sell(float(cex_order['filled']))
                return {
                    'status': 'failed',
                    'reason': 'DEX卖出失败',
                    'cex_buy_order': cex_order,
                    'dex_result': dex_result,
                    'rollback_result': rollback_result
                }
            
            if not cex_success and dex_success:
                logger.error("DEX卖出成功但CEX买入失败，尝试在DEX买回")
                rollback_result = await self._rollback_dex_buy(actual_amount)
                return {
                    'status': 'failed',
                    'reason': 'CEX买入失败',
                    'cex_order': cex_order,
                    'dex_result': dex_result,
                    'rollback_result': rollback_result
                }
            
            # 两边都失败
            return {
                'status': 'failed',
                'reason': '双边交易都失败',
                'cex_order': cex_order,
                'dex_result': dex_result
            }
            
        except Exception as e:
            logger.error(f"执行套利时发生错误: {e}")
            return {
                'status': 'error',
                'reason': str(e)
            }
    
    async def execute_dex_buy_cex_sell(self, amount_usdt: float) -> Dict[str, Any]:
        """
        执行DEX买入CEX卖出套利（并发执行）
        
        Args:
            amount_usdt: 要使用的USDT数量
            
        Returns:
            交易结果
        """
        try:
            # 1. 确认DEX实际可买入数量
            actual_amount, gas_cost = await self._confirm_dex_buy_amount(amount_usdt)
            if actual_amount == 0:
                return {
                    'status': 'cancelled',
                    'reason': '无法确认DEX可买入数量'
                }
            
            # 2. 获取CEX最新卖价
            order_book = self.gate_client.get_order_book("ASTR/USDT", limit=1)
            if not order_book or not order_book['bids']:
                return {
                    'status': 'cancelled',
                    'reason': '无法获取CEX卖价'
                }
            
            cex_price = float(order_book['bids'][0][0])
            
            # 3. 重新计算预期利润
            expected_revenue = actual_amount * cex_price
            expected_profit = expected_revenue - amount_usdt - gas_cost
            
            if expected_profit <= MIN_PROFIT_THRESHOLD:
                return {
                    'status': 'cancelled',
                    'reason': f'实际利润 {expected_profit:.6f} USDT 低于最小阈值',
                    'details': {
                        'dex_cost': amount_usdt,
                        'cex_revenue': expected_revenue,
                        'gas_cost': gas_cost
                    }
                }
            
            logger.info(f"交易预览 - DEX买入: {actual_amount:.4f} WASTR")
            logger.info(f"交易预览 - CEX卖出: 预计获得 {expected_revenue:.4f} USDT @ {cex_price:.6f}")
            logger.info(f"交易预览 - 预期利润: {expected_profit:.6f} USDT")
            
            # 4. 并发执行交易
            logger.info(f"开始并发执行DEX买入和CEX卖出套利，DEX买入金额: {amount_usdt} USDT")
            
            dex_task = asyncio.create_task(
                self._execute_dex_buy(amount_usdt)
            )
            cex_task = asyncio.create_task(
                self._place_cex_sell_order(
                    "ASTR/USDT",
                    actual_amount,
                    cex_price
                )
            )
            
            # 等待两个任务完成
            done, pending = await asyncio.wait(
                [dex_task, cex_task],
                return_when=asyncio.ALL_COMPLETED
            )
            
            # 获取结果
            dex_result = await dex_task
            cex_order = await cex_task
            
            # 检查交易结果
            dex_success = dex_result and dex_result.get('status') == 'success'
            cex_success = cex_order and cex_order.get('status') == 'closed'
            
            # 根据不同情况处理
            if dex_success and cex_success:
                # 两边都成功，计算实际利润
                actual_profit = float(cex_order['cost']) - amount_usdt
                
                return {
                    'status': 'success',
                    'dex_result': dex_result,
                    'cex_order': cex_order,
                    'profit': actual_profit,
                    'expected_profit': expected_profit,
                    'astr_amount': float(cex_order['filled'])
                }
            
            # 处理部分成功的情况
            if dex_success and not cex_success:
                logger.error("DEX买入成功但CEX卖出失败，尝试在DEX卖回USDT")
                rollback_result = await self._rollback_dex_sell(dex_result['amount_received'])
                return {
                    'status': 'failed',
                    'reason': 'CEX卖出失败',
                    'dex_result': dex_result,
                    'cex_order': cex_order,
                    'rollback_result': rollback_result
                }
            
            if not dex_success and cex_success:
                logger.error("CEX卖出成功但DEX买入失败，尝试在CEX买回")
                rollback_result = await self._rollback_cex_buy(float(cex_order['filled']))
                return {
                    'status': 'failed',
                    'reason': 'DEX买入失败',
                    'dex_result': dex_result,
                    'cex_order': cex_order,
                    'rollback_result': rollback_result
                }
            
            # 两边都失败
            return {
                'status': 'failed',
                'reason': '双边交易都失败',
                'dex_result': dex_result,
                'cex_order': cex_order
            }
            
        except Exception as e:
            logger.error(f"执行套利时发生错误: {e}")
            return {
                'status': 'error',
                'reason': str(e)
            }
    
    async def _confirm_cex_buy_amount(self, symbol: str, usdt_amount: float, expected_amount: float) -> Tuple[float, float]:
        """
        确认CEX实际可买入数量
        
        Args:
            symbol: 交易对
            usdt_amount: 计划使用的USDT数量
            expected_amount: 预期买入的ASTR数量
            
        Returns:
            (实际可买入数量, 实际价格)
        """
        try:
            # 获取最新订单簿
            order_book = self.gate_client.get_order_book(symbol, limit=20)
            if not order_book or not order_book['asks']:
                raise ValueError("无法获取CEX订单簿数据")
            
            total_astr = 0
            total_cost = 0
            remaining_usdt = usdt_amount
            actual_price = 0
            
            for ask in order_book['asks']:
                price = float(ask[0])
                available = float(ask[1])
                
                if total_astr >= expected_amount:
                    break
                
                cost = price * available
                if cost <= remaining_usdt:
                    total_astr += available
                    total_cost += cost
                    remaining_usdt -= cost
                else:
                    partial_amount = remaining_usdt / price
                    total_astr += partial_amount
                    total_cost += remaining_usdt
                    remaining_usdt = 0
                
                actual_price = price  # 使用最后一个成交价格
            
            return min(total_astr, expected_amount), actual_price
            
        except Exception as e:
            logger.error(f"确认CEX可买入数量时出错: {e}")
            return 0, 0
    
    async def _preview_dex_sell(self, amount_wastr: float) -> Tuple[float, float]:
        """
        预览DEX卖出交易
        
        Args:
            amount_wastr: 要卖出的WASTR数量
            
        Returns:
            (预计获得的USDT数量, gas成本)
        """
        try:
            # 更新池子状态
            self.simulator.update_reserves()
            
            # 模拟交易
            usdt_received = self.simulator.simulate_swap_1_to_0(Decimal(str(amount_wastr)))
            if usdt_received is None:
                raise ValueError("DEX模拟交易失败")
            
            # 计算gas成本（USDT）
            gas_cost_astr = 1.6 * DEX_GAS_MULTIPLIER
            gas_cost_usdt = gas_cost_astr * float(self.gate_client.get_ticker("ASTR/USDT")['last'])
            
            return float(usdt_received), gas_cost_usdt
            
        except Exception as e:
            logger.error(f"预览DEX卖出交易时出错: {e}")
            return 0, 0
    
    async def _confirm_dex_buy_amount(self, usdt_amount: float) -> Tuple[float, float]:
        """
        确认DEX实际可买入数量
        
        Args:
            usdt_amount: 要使用的USDT数量
            
        Returns:
            (预计获得的WASTR数量, gas成本)
        """
        try:
            # 更新池子状态
            self.simulator.update_reserves()
            
            # 模拟交易
            wastr_received = self.simulator.simulate_swap_0_to_1(Decimal(str(usdt_amount)))
            if wastr_received is None:
                raise ValueError("DEX模拟交易失败")
            
            # 计算gas成本（USDT）
            gas_cost_astr = 1.6 * DEX_GAS_MULTIPLIER
            gas_cost_usdt = gas_cost_astr * float(self.gate_client.get_ticker("ASTR/USDT")['last'])
            
            return float(wastr_received), gas_cost_usdt
            
        except Exception as e:
            logger.error(f"确认DEX可买入数量时出错: {e}")
            return 0, 0
    
    async def _place_cex_buy_order(self, symbol: str, amount: float, price: float) -> Dict[str, Any]:
        """在CEX下限价买单"""
        try:
            order = self.gate_client.create_limit_buy_order(symbol, amount, price)
            return await self._wait_for_cex_order(order['id'], symbol)
        except Exception as e:
            logger.error(f"CEX下买单失败: {e}")
            return None
    
    async def _place_cex_sell_order(self, symbol: str, amount: float, price: float) -> Dict[str, Any]:
        """在CEX下限价卖单"""
        try:
            order = self.gate_client.create_limit_sell_order(symbol, amount, price)
            return await self._wait_for_cex_order(order['id'], symbol)
        except Exception as e:
            logger.error(f"CEX下卖单失败: {e}")
            return None
    
    async def _wait_for_cex_order(self, order_id: str, symbol: str) -> Dict[str, Any]:
        """等待CEX订单完成"""
        try:
            return self.gate_client.wait_for_order_status(order_id, symbol, 'closed', timeout=30)
        except Exception as e:
            logger.error(f"等待订单完成失败: {e}")
            return None
    
    async def _execute_dex_buy(self, amount_usdt: float) -> Dict[str, Any]:
        """在DEX执行买入"""
        try:
            # 设置gas倍数
            self.dex_client.wallet.set_gas_price_multiplier(DEX_GAS_MULTIPLIER)
            
            # 执行交易
            result = self.dex_client.swap_tokens(
                token_in="USDT",
                token_out="WASTR",
                amount_in=amount_usdt,
                slippage=0.5,
                wait_for_confirmation=True
            )
            
            if result['status'] == 'success':
                # 获取实际收到的WASTR数量
                wastr_balance = self.dex_client.wallet.get_token_balance("WASTR")
                result['amount_received'] = wastr_balance
            
            return result
            
        except Exception as e:
            logger.error(f"DEX买入失败: {e}")
            return {'status': 'failed', 'reason': str(e)}
    
    async def _execute_dex_sell(self, amount_wastr: float) -> Dict[str, Any]:
        """在DEX执行卖出"""
        try:
            # 设置gas倍数
            self.dex_client.wallet.set_gas_price_multiplier(DEX_GAS_MULTIPLIER)
            
            # 执行交易
            result = self.dex_client.swap_tokens(
                token_in="WASTR",
                token_out="USDT",
                amount_in=amount_wastr,
                slippage=0.5,
                wait_for_confirmation=True
            )
            
            if result['status'] == 'success':
                # 获取实际收到的USDT数量
                usdt_balance = self.dex_client.wallet.get_token_balance("USDT")
                result['amount_received'] = usdt_balance
            
            return result
            
        except Exception as e:
            logger.error(f"DEX卖出失败: {e}")
            return {'status': 'failed', 'reason': str(e)}
    
    async def _rollback_cex_sell(self, amount: float) -> Dict[str, Any]:
        """CEX回滚：卖出ASTR"""
        try:
            order_book = self.gate_client.get_order_book("ASTR/USDT", limit=1)
            price = float(order_book['bids'][0][0])  # 使用最高买价
            
            return await self._place_cex_sell_order("ASTR/USDT", amount, price)
        except Exception as e:
            logger.error(f"CEX回滚失败: {e}")
            return None
    
    async def _rollback_dex_sell(self, amount: float) -> Dict[str, Any]:
        """DEX回滚：卖出WASTR换回USDT"""
        return await self._execute_dex_sell(amount)
    
    async def _rollback_dex_buy(self, amount_wastr: float) -> Dict[str, Any]:
        """DEX回滚：买入WASTR"""
        try:
            # 设置gas倍数
            self.dex_client.wallet.set_gas_price_multiplier(DEX_GAS_MULTIPLIER)
            
            # 执行交易
            result = self.dex_client.swap_tokens(
                token_in="USDT",
                token_out="WASTR",
                amount_in=amount_wastr,
                slippage=0.5,
                wait_for_confirmation=True
            )
            
            if result['status'] == 'success':
                # 获取实际收到的WASTR数量
                wastr_balance = self.dex_client.wallet.get_token_balance("WASTR")
                result['amount_received'] = wastr_balance
            
            return result
            
        except Exception as e:
            logger.error(f"DEX回滚买入失败: {e}")
            return {'status': 'failed', 'reason': str(e)}
    
    async def _rollback_cex_buy(self, amount: float) -> Dict[str, Any]:
        """CEX回滚：买入ASTR"""
        try:
            order_book = self.gate_client.get_order_book("ASTR/USDT", limit=1)
            price = float(order_book['asks'][0][0])  # 使用最低卖价
            
            return await self._place_cex_buy_order("ASTR/USDT", amount, price)
        except Exception as e:
            logger.error(f"CEX回滚买入失败: {e}")
            return None

async def get_dex_transaction_result(tx_hash):
    """获取DEX交易的实际结果"""
    try:
        client = ArthSwapClient()
        # 获取交易收据
        receipt = await client.wallet.get_transaction_receipt(tx_hash)
        if not receipt:
            return None
            
        # 解析交易日志获取实际交换金额
        actual_amount = client.router.decode_swap_event(receipt)
        gas_used = float(receipt['gasUsed']) * float(receipt['effectiveGasPrice']) / 1e18
        
        return {
            "status": "success" if receipt['status'] else "failed",
            "tx_hash": tx_hash,
            "amount_received": actual_amount,
            "gas_cost": gas_used
        }
    except Exception as e:
        logger.error(f"获取DEX交易结果时出错: {e}")
        return None

async def execute_arbitrage(direction: str, amount_usdt: float, astr_amount: float = None) -> Dict[str, Any]:
    """
    执行套利交易
    
    Args:
        direction: 交易方向，'cex_buy_dex_sell' 或 'dex_buy_cex_sell'
        amount_usdt: 要使用的USDT数量
        astr_amount: 要交易的ASTR数量（仅CEX买入DEX卖出时需要）
    
    Returns:
        交易结果
    """
    executor = ArbitrageExecutor()
    
    if direction == 'cex_buy_dex_sell':
        if not astr_amount:
            raise ValueError("CEX买入DEX卖出方向需要指定astr_amount参数")
        return await executor.execute_cex_buy_dex_sell(amount_usdt, astr_amount)
    elif direction == 'dex_buy_cex_sell':
        return await executor.execute_dex_buy_cex_sell(amount_usdt)
    else:
        raise ValueError(f"无效的交易方向: {direction}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='执行CEX-DEX套利交易')
    parser.add_argument('direction', choices=['cex_buy_dex_sell', 'dex_buy_cex_sell'], 
                      help='交易方向')
    parser.add_argument('amount_usdt', type=float, help='要使用的USDT数量')
    parser.add_argument('--astr-amount', type=float, help='要交易的ASTR数量（仅CEX买入DEX卖出时需要）')
    
    args = parser.parse_args()
    
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 执行套利
    result = asyncio.run(execute_arbitrage(
        args.direction,
        args.amount_usdt,
        args.astr_amount
    ))
    
    # 打印结果
    print("\n===== 套利执行结果 =====")
    print(f"交易方向: {args.direction}")
    print(f"状态: {result['status']}")
    
    if result['status'] == 'success':
        print(f"利润: {result['profit']:.6f} USDT")
        print(f"交易数量: {result['astr_amount']:.6f} ASTR")
    else:
        print(f"原因: {result.get('reason', '未知')}")
    
    return 0 if result['status'] == 'success' else 1

if __name__ == "__main__":
    sys.exit(main()) 