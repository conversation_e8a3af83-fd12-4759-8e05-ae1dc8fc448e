{"version": 3, "sources": ["../../src/bcs/serializable/entryFunctionBytes.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../serializer\";\nimport { Deserializer } from \"../deserializer\";\nimport { FixedBytes } from \"./fixedBytes\";\nimport { EntryFunctionArgument } from \"../../transactions/instances/transactionArgument\";\nimport { HexInput } from \"../../types\";\n\n/**\n * This class exists solely to represent a sequence of fixed bytes as a serialized entry function, because\n * serializing an entry function appends a prefix that's *only* used for entry function arguments.\n *\n * NOTE: Using this class for serialized script functions will lead to erroneous and unexpected behavior.\n *\n * If you wish to convert this class back to a TransactionArgument, you must know the type\n * of the argument beforehand, and use the appropriate class to deserialize the bytes within\n * an instance of this class.\n * @group Implementation\n * @category BCS\n */\nexport class EntryFunctionBytes extends Serializable implements EntryFunctionArgument {\n  public readonly value: FixedBytes;\n\n  /**\n   * Creates an instance of the class with a specified hexadecimal input value.\n   *\n   * @param value - The hexadecimal input to be converted into FixedBytes.\n   * @group Implementation\n   * @category BCS\n   */\n  private constructor(value: HexInput) {\n    super();\n    this.value = new FixedBytes(value);\n  }\n\n  // Note that to see the Move, BCS-serialized representation of the underlying fixed byte vector,\n  // we must not serialize the length prefix.\n  //\n  // In other words, this class is only used to represent a sequence of bytes that are already\n  // BCS-serialized as a type. To represent those bytes accurately, the BCS-serialized form is the same exact\n  // representation.\n\n  /**\n   * Serializes the value using the provided serializer.\n   * This function is essential for accurately representing a sequence of bytes that are already BCS-serialized as a type.\n   *\n   * Note that to see the Move, BCS-serialized representation of the underlying fixed byte vector,\n   * we must not serialize the length prefix.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serialize(this.value);\n  }\n\n  // When we serialize these bytes as an entry function argument, we need to\n  // serialize the length prefix. This essentially converts the underlying fixed byte vector to a type-agnostic\n  // byte vector to an `any` type.\n  // NOTE: This, and the lack of a `serializeForScriptFunction`, is the only meaningful difference between this\n  // class and FixedBytes.\n\n  /**\n   * Serializes the current instance for use as an entry function argument by converting the underlying fixed byte vector to a\n   * type-agnostic byte vector.\n   * This process includes serializing the length prefix of the byte vector.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForEntryFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(this.value.value.length);\n    serializer.serialize(this);\n  }\n\n  /**\n   * The only way to create an instance of this class is to use this static method.\n   * This function should only be used when deserializing a sequence of EntryFunctionPayload arguments.\n   * @param deserializer - The deserializer instance with the buffered bytes.\n   * @param length - The length of the bytes to deserialize.\n   * @returns An instance of this class, which will now only be usable as an EntryFunctionArgument.\n   * @group Implementation\n   * @category BCS\n   */\n  static deserialize(deserializer: Deserializer, length: number): EntryFunctionBytes {\n    const fixedBytes = FixedBytes.deserialize(deserializer, length);\n    return new EntryFunctionBytes(fixedBytes.value);\n  }\n}\n"], "mappings": "kFAqBO,IAAMA,EAAN,MAAMC,UAA2BC,CAA8C,CAU5E,YAAYC,EAAiB,CACnC,MAAM,EACN,KAAK,MAAQ,IAAIC,EAAWD,CAAK,CACnC,CAoBA,UAAUE,EAA8B,CACtCA,EAAW,UAAU,KAAK,KAAK,CACjC,CAiBA,0BAA0BA,EAA8B,CACtDA,EAAW,sBAAsB,KAAK,MAAM,MAAM,MAAM,EACxDA,EAAW,UAAU,IAAI,CAC3B,CAWA,OAAO,YAAYC,EAA4BC,EAAoC,CACjF,IAAMC,EAAaJ,EAAW,YAAYE,EAAcC,CAAM,EAC9D,OAAO,IAAIN,EAAmBO,EAAW,KAAK,CAChD,CACF", "names": ["EntryFunctionBytes", "_EntryFunctionBytes", "Serializable", "value", "FixedBytes", "serializer", "deserializer", "length", "fixedBytes"]}