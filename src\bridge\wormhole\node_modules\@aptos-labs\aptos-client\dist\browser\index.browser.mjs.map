{"version": 3, "sources": ["../../src/index.browser.ts"], "sourcesContent": ["import { AptosClientRequest, AptosClientResponse } from \"./types\";\n\n/**\n * Used for JSON responses\n *\n * @param options\n */\nexport default async function aptosClient<Res>(\n  options: AptosClientRequest,\n): Promise<AptosClientResponse<Res>> {\n  return jsonRequest<Res>(options);\n}\n\nexport async function jsonRequest<Res>(\n  options: AptosClientRequest,\n): Promise<AptosClientResponse<Res>> {\n  const { requestUrl, requestConfig } = buildRequest(options);\n\n  const res = await fetch(requestUrl, requestConfig);\n  const data = await res.json();\n\n  return {\n    status: res.status,\n    statusText: res.statusText,\n    data,\n    headers: res.headers,\n    config: requestConfig,\n  };\n}\n\n/**\n * Used for binary responses, such as BCS outputs\n *\n * @experimental\n * @param options\n */\nexport async function bcsRequest(\n  options: AptosClientRequest,\n): Promise<AptosClientResponse<ArrayBuffer>> {\n  const { requestUrl, requestConfig } = buildRequest(options);\n\n  const res = await fetch(requestUrl, requestConfig);\n  const data = await res.arrayBuffer();\n\n  return {\n    status: res.status,\n    statusText: res.statusText,\n    data,\n    headers: res.headers,\n    config: requestConfig,\n  };\n}\n\nfunction buildRequest(options: AptosClientRequest) {\n  const headers = new Headers();\n  Object.entries(options?.headers ?? {}).forEach(([key, value]) => {\n    headers.append(key, String(value));\n  });\n\n  const body =\n    options.body instanceof Uint8Array\n      ? options.body\n      : JSON.stringify(options.body);\n\n  const withCredentialsOption = options.overrides?.WITH_CREDENTIALS;\n  let credentials: RequestCredentials;\n  if (withCredentialsOption === false) {\n    credentials = \"omit\";\n  } else if (withCredentialsOption === true) {\n    credentials = \"include\";\n  } else {\n    credentials = withCredentialsOption ?? \"include\";\n  }\n\n  const requestConfig: RequestInit = {\n    method: options.method,\n    headers,\n    body,\n    credentials,\n  };\n\n  const params = new URLSearchParams();\n  Object.entries(options.params ?? {}).forEach(([key, value]) => {\n    if (value !== undefined) {\n      params.append(key, String(value));\n    }\n  });\n\n  const requestUrl =\n    options.url + (params.size > 0 ? `?${params.toString()}` : \"\");\n\n  return { requestUrl, requestConfig };\n}\n"], "mappings": ";AAOA,eAAO,YACL,SACmC;AACnC,SAAO,YAAiB,OAAO;AACjC;AAEA,eAAsB,YACpB,SACmC;AACnC,QAAM,EAAE,YAAY,cAAc,IAAI,aAAa,OAAO;AAE1D,QAAM,MAAM,MAAM,MAAM,YAAY,aAAa;AACjD,QAAM,OAAO,MAAM,IAAI,KAAK;AAE5B,SAAO;AAAA,IACL,QAAQ,IAAI;AAAA,IACZ,YAAY,IAAI;AAAA,IAChB;AAAA,IACA,SAAS,IAAI;AAAA,IACb,QAAQ;AAAA,EACV;AACF;AAQA,eAAsB,WACpB,SAC2C;AAC3C,QAAM,EAAE,YAAY,cAAc,IAAI,aAAa,OAAO;AAE1D,QAAM,MAAM,MAAM,MAAM,YAAY,aAAa;AACjD,QAAM,OAAO,MAAM,IAAI,YAAY;AAEnC,SAAO;AAAA,IACL,QAAQ,IAAI;AAAA,IACZ,YAAY,IAAI;AAAA,IAChB;AAAA,IACA,SAAS,IAAI;AAAA,IACb,QAAQ;AAAA,EACV;AACF;AAEA,SAAS,aAAa,SAA6B;AArDnD;AAsDE,QAAM,UAAU,IAAI,QAAQ;AAC5B,SAAO,SAAQ,wCAAS,YAAT,YAAoB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/D,YAAQ,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,EACnC,CAAC;AAED,QAAM,OACJ,QAAQ,gBAAgB,aACpB,QAAQ,OACR,KAAK,UAAU,QAAQ,IAAI;AAEjC,QAAM,yBAAwB,aAAQ,cAAR,mBAAmB;AACjD,MAAI;AACJ,MAAI,0BAA0B,OAAO;AACnC,kBAAc;AAAA,EAChB,WAAW,0BAA0B,MAAM;AACzC,kBAAc;AAAA,EAChB,OAAO;AACL,kBAAc,wDAAyB;AAAA,EACzC;AAEA,QAAM,gBAA6B;AAAA,IACjC,QAAQ,QAAQ;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,SAAS,IAAI,gBAAgB;AACnC,SAAO,SAAQ,aAAQ,WAAR,YAAkB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7D,QAAI,UAAU,QAAW;AACvB,aAAO,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAClC;AAAA,EACF,CAAC;AAED,QAAM,aACJ,QAAQ,OAAO,OAAO,OAAO,IAAI,IAAI,OAAO,SAAS,CAAC,KAAK;AAE7D,SAAO,EAAE,YAAY,cAAc;AACrC;", "names": []}