import { Account, AuthExtension, BankExtension, Block, Coin, DeliverTxResponse, IndexedTx, QueryClient, SearchTxQuery, SequenceResponse, TxExtension } from "@cosmjs/stargate";
import { CometClient, HttpEndpoint } from "@cosmjs/tendermint-rpc";
import { JsonObject, WasmExtension } from "./modules";
export interface Code {
    readonly id: number;
    /** Bech32 account address */
    readonly creator: string;
    /** Hex-encoded sha256 hash of the code stored here */
    readonly checksum: string;
}
export interface CodeDetails extends Code {
    /** The original Wasm bytes */
    readonly data: Uint8Array;
}
export interface Contract {
    readonly address: string;
    readonly codeId: number;
    /** Bech32 account address */
    readonly creator: string;
    /** Bech32-encoded admin address */
    readonly admin: string | undefined;
    readonly label: string;
    /**
     * The IBC port ID assigned to this contract by wasmd.
     *
     * This is set for all IBC contracts (https://github.com/CosmWasm/wasmd/blob/v0.16.0/x/wasm/keeper/keeper.go#L299-L306).
     */
    readonly ibcPortId: string | undefined;
}
export interface ContractCodeHistoryEntry {
    /** The source of this history entry */
    readonly operation: "Genesis" | "Init" | "Migrate";
    readonly codeId: number;
    readonly msg: JsonObject;
}
/** Use for testing only */
export interface PrivateCosmWasmClient {
    readonly cometClient: CometClient | undefined;
    readonly queryClient: (QueryClient & AuthExtension & BankExtension & TxExtension & WasmExtension) | undefined;
}
export declare class CosmWasmClient {
    private readonly cometClient;
    private readonly queryClient;
    private readonly codesCache;
    private chainId;
    /**
     * Creates an instance by connecting to the given CometBFT RPC endpoint.
     *
     * This uses auto-detection to decide between a CometBFT 0.38, Tendermint 0.37 and 0.34 client.
     * To set the Comet client explicitly, use `create`.
     */
    static connect(endpoint: string | HttpEndpoint): Promise<CosmWasmClient>;
    /**
     * Creates an instance from a manually created Comet client.
     * Use this to use `Comet38Client` or `Tendermint37Client` instead of `Tendermint34Client`.
     */
    static create(cometClient: CometClient): Promise<CosmWasmClient>;
    protected constructor(cometClient: CometClient | undefined);
    protected getCometClient(): CometClient | undefined;
    protected forceGetCometClient(): CometClient;
    protected getQueryClient(): (QueryClient & AuthExtension & BankExtension & TxExtension & WasmExtension) | undefined;
    protected forceGetQueryClient(): QueryClient & AuthExtension & BankExtension & TxExtension & WasmExtension;
    getChainId(): Promise<string>;
    getHeight(): Promise<number>;
    getAccount(searchAddress: string): Promise<Account | null>;
    getSequence(address: string): Promise<SequenceResponse>;
    getBlock(height?: number): Promise<Block>;
    getBalance(address: string, searchDenom: string): Promise<Coin>;
    getTx(id: string): Promise<IndexedTx | null>;
    searchTx(query: SearchTxQuery): Promise<IndexedTx[]>;
    disconnect(): void;
    /**
     * Broadcasts a signed transaction to the network and monitors its inclusion in a block.
     *
     * If broadcasting is rejected by the node for some reason (e.g. because of a CheckTx failure),
     * an error is thrown.
     *
     * If the transaction is not included in a block before the provided timeout, this errors with a `TimeoutError`.
     *
     * If the transaction is included in a block, a `DeliverTxResponse` is returned. The caller then
     * usually needs to check for execution success or failure.
     */
    broadcastTx(tx: Uint8Array, timeoutMs?: number, pollIntervalMs?: number): Promise<DeliverTxResponse>;
    /**
     * Broadcasts a signed transaction to the network without monitoring it.
     *
     * If broadcasting is rejected by the node for some reason (e.g. because of a CheckTx failure),
     * an error is thrown.
     *
     * If the transaction is broadcasted, a `string` containing the hash of the transaction is returned. The caller then
     * usually needs to check if the transaction was included in a block and was successful.
     *
     * @returns Returns the hash of the transaction
     */
    broadcastTxSync(tx: Uint8Array): Promise<string>;
    /**
     * getCodes() returns all codes and is just looping through all pagination pages.
     *
     * This is potentially inefficient and advanced apps should consider creating
     * their own query client to handle pagination together with the app's screens.
     */
    getCodes(): Promise<readonly Code[]>;
    getCodeDetails(codeId: number): Promise<CodeDetails>;
    /**
     * getContracts() returns all contract instances for one code and is just looping through all pagination pages.
     *
     * This is potentially inefficient and advanced apps should consider creating
     * their own query client to handle pagination together with the app's screens.
     */
    getContracts(codeId: number): Promise<readonly string[]>;
    /**
     * Returns a list of contract addresses created by the given creator.
     * This just loops through all pagination pages.
     */
    getContractsByCreator(creator: string): Promise<string[]>;
    /**
     * Throws an error if no contract was found at the address
     */
    getContract(address: string): Promise<Contract>;
    /**
     * Throws an error if no contract was found at the address
     */
    getContractCodeHistory(address: string): Promise<readonly ContractCodeHistoryEntry[]>;
    /**
     * Returns the data at the key if present (raw contract dependent storage data)
     * or null if no data at this key.
     *
     * Promise is rejected when contract does not exist.
     */
    queryContractRaw(address: string, key: Uint8Array): Promise<Uint8Array | null>;
    /**
     * Makes a smart query on the contract, returns the parsed JSON document.
     *
     * Promise is rejected when contract does not exist.
     * Promise is rejected for invalid query format.
     * Promise is rejected for invalid response format.
     */
    queryContractSmart(address: string, queryMsg: JsonObject): Promise<JsonObject>;
    private txsQuery;
}
