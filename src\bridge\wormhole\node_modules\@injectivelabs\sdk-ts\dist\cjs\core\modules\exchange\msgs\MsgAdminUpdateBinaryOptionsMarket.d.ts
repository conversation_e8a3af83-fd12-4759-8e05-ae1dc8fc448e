import { InjectiveExchangeV1Beta1Tx, InjectiveExchangeV1Beta1Exchange } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { GrpcMarketStatus } from '../../../../client/chain/types/index.js';
export declare namespace MsgAdminUpdateBinaryOptionsMarket {
    interface Params {
        sender: string;
        marketId: string;
        settlementPrice: string;
        expirationTimestamp: string;
        settlementTimestamp: string;
        status: GrpcMarketStatus;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket;
}
/**
 * @category Messages
 */
export default class MsgAdminUpdateBinaryOptionsMarket extends MsgBase<MsgAdminUpdateBinaryOptionsMarket.Params, MsgAdminUpdateBinaryOptionsMarket.Proto> {
    static fromJSON(params: MsgAdminUpdateBinaryOptionsMarket.Params): MsgAdminUpdateBinaryOptionsMarket;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket;
    toData(): {
        sender: string;
        marketId: string;
        settlementPrice: string;
        expirationTimestamp: string;
        settlementTimestamp: string;
        status: InjectiveExchangeV1Beta1Exchange.MarketStatus;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            market_id: string;
            settlement_price: string;
            expiration_timestamp: string;
            settlement_timestamp: string;
            status: InjectiveExchangeV1Beta1Exchange.MarketStatus;
        };
    };
    toWeb3Gw(): {
        sender: string;
        market_id: string;
        settlement_price: string;
        expiration_timestamp: string;
        settlement_timestamp: string;
        status: InjectiveExchangeV1Beta1Exchange.MarketStatus;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            settlement_price: string;
            sender: string;
            market_id: string;
            expiration_timestamp: string;
            settlement_timestamp: string;
            status: InjectiveExchangeV1Beta1Exchange.MarketStatus;
        };
    };
    toEip712V2(): {
        settlement_price: string;
        status: string;
        sender: string;
        market_id: string;
        expiration_timestamp: string;
        settlement_timestamp: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgAdminUpdateBinaryOptionsMarket;
    };
    toBinary(): Uint8Array;
}
