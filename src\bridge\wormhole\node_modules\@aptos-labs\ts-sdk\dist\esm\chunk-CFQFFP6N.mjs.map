{"version": 3, "sources": ["../../src/core/crypto/ephemeral.ts"], "sourcesContent": ["import { Serializer, Deserializer } from \"../../bcs\";\nimport { EphemeralPublicKeyVariant, EphemeralSignatureVariant, HexInput } from \"../../types\";\nimport { PublicKey } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { Ed25519PublicKey, Ed25519Signature } from \"./ed25519\";\nimport { Hex } from \"../hex\";\nimport { AptosConfig } from \"../../api\";\n\n/**\n * Represents ephemeral public keys for Aptos Keyless accounts.\n *\n * These keys are used only temporarily within Keyless accounts and are not utilized as public keys for account identification.\n * @group Implementation\n * @category Serialization\n */\nexport class EphemeralPublicKey extends PublicKey {\n  /**\n   * The public key itself\n   * @group Implementation\n   * @category Serialization\n   */\n  public readonly publicKey: PublicKey;\n\n  /**\n   * An enum indicating the scheme of the ephemeral public key\n   * @group Implementation\n   * @category Serialization\n   */\n  public readonly variant: EphemeralPublicKeyVariant;\n\n  /**\n   * Creates an instance of EphemeralPublic<PERSON>ey using the provided public key.\n   * This constructor ensures that only supported signature types are accepted.\n   *\n   * @param publicKey - The public key to be used for the ephemeral public key.\n   * @throws Error if the signature type is unsupported.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(publicKey: PublicKey) {\n    super();\n    const publicKeyType = publicKey.constructor.name;\n    switch (publicKeyType) {\n      case Ed25519PublicKey.name:\n        this.publicKey = publicKey;\n        this.variant = EphemeralPublicKeyVariant.Ed25519;\n        break;\n      default:\n        throw new Error(`Unsupported key for EphemeralPublicKey - ${publicKeyType}`);\n    }\n  }\n\n  /**\n   * Verifies a signed message using the ephemeral public key.\n   *\n   * @param args - The arguments for the verification.\n   * @param args.message - The message that was signed.\n   * @param args.signature - The signature that was signed by the private key of the ephemeral public key.\n   * @returns true if the signature is valid, otherwise false.\n   * @group Implementation\n   * @category Serialization\n   */\n  verifySignature(args: { message: HexInput; signature: EphemeralSignature }): boolean {\n    const { message, signature } = args;\n    return this.publicKey.verifySignature({ message, signature: signature.signature });\n  }\n\n  async verifySignatureAsync(args: {\n    aptosConfig: AptosConfig;\n    message: HexInput;\n    signature: EphemeralSignature;\n  }): Promise<boolean> {\n    return this.verifySignature(args);\n  }\n\n  /**\n   * Serializes the current instance, specifically handling the Ed25519 signature type.\n   * This function ensures that the signature is properly serialized using the provided serializer.\n   *\n   * @param serializer - The serializer instance used to serialize the signature.\n   * @throws Error if the signature type is unknown.\n   * @group Implementation\n   * @category Serialization\n   */\n  serialize(serializer: Serializer): void {\n    if (this.publicKey instanceof Ed25519PublicKey) {\n      serializer.serializeU32AsUleb128(EphemeralPublicKeyVariant.Ed25519);\n      this.publicKey.serialize(serializer);\n    } else {\n      throw new Error(\"Unknown public key type\");\n    }\n  }\n\n  /**\n   * Deserializes an EphemeralSignature from the provided deserializer.\n   * This function allows you to retrieve an EphemeralSignature based on the deserialized data.\n   *\n   * @param deserializer - The deserializer instance used to read the serialized data.\n   * @group Implementation\n   * @category Serialization\n   */\n  static deserialize(deserializer: Deserializer): EphemeralPublicKey {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case EphemeralPublicKeyVariant.Ed25519:\n        return new EphemeralPublicKey(Ed25519PublicKey.deserialize(deserializer));\n      default:\n        throw new Error(`Unknown variant index for EphemeralPublicKey: ${index}`);\n    }\n  }\n\n  /**\n   * Determines if the provided public key is an instance of `EphemeralPublicKey`.\n   *\n   * @param publicKey - The public key to check.\n   * @returns A boolean indicating whether the public key is an ephemeral type.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isPublicKey(publicKey: PublicKey): publicKey is EphemeralPublicKey {\n    return publicKey instanceof EphemeralPublicKey;\n  }\n}\n\n/**\n * Represents ephemeral signatures used in Aptos Keyless accounts.\n *\n * These signatures are utilized within the KeylessSignature framework.\n * @group Implementation\n * @category Serialization\n */\nexport class EphemeralSignature extends Signature {\n  /**\n   * The signature signed by the private key of an EphemeralKeyPair\n   * @group Implementation\n   * @category Serialization\n   */\n  public readonly signature: Signature;\n\n  constructor(signature: Signature) {\n    super();\n    const signatureType = signature.constructor.name;\n    switch (signatureType) {\n      case Ed25519Signature.name:\n        this.signature = signature;\n        break;\n      default:\n        throw new Error(`Unsupported signature for EphemeralSignature - ${signatureType}`);\n    }\n  }\n\n  /**\n   * Deserializes an ephemeral signature from a hexadecimal input.\n   * This function allows you to convert a hexadecimal representation of an ephemeral signature into its deserialized form for\n   * further processing.\n   *\n   * @param hexInput - The hexadecimal input representing the ephemeral signature.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromHex(hexInput: HexInput): EphemeralSignature {\n    const data = Hex.fromHexInput(hexInput);\n    const deserializer = new Deserializer(data.toUint8Array());\n    return EphemeralSignature.deserialize(deserializer);\n  }\n\n  serialize(serializer: Serializer): void {\n    if (this.signature instanceof Ed25519Signature) {\n      serializer.serializeU32AsUleb128(EphemeralSignatureVariant.Ed25519);\n      this.signature.serialize(serializer);\n    } else {\n      throw new Error(\"Unknown signature type\");\n    }\n  }\n\n  static deserialize(deserializer: Deserializer): EphemeralSignature {\n    const index = deserializer.deserializeUleb128AsU32();\n    switch (index) {\n      case EphemeralSignatureVariant.Ed25519:\n        return new EphemeralSignature(Ed25519Signature.deserialize(deserializer));\n      default:\n        throw new Error(`Unknown variant index for EphemeralSignature: ${index}`);\n    }\n  }\n}\n"], "mappings": "oNAeO,IAAMA,EAAN,MAAMC,UAA2BC,CAAU,CAwBhD,YAAYC,EAAsB,CAChC,MAAM,EACN,IAAMC,EAAgBD,EAAU,YAAY,KAC5C,OAAQC,EAAe,CACrB,KAAKC,EAAiB,KACpB,KAAK,UAAYF,EACjB,KAAK,QAAU,EACf,MACF,QACE,MAAM,IAAI,MAAM,4CAA4CC,CAAa,EAAE,CAC/E,CACF,CAYA,gBAAgBE,EAAqE,CACnF,GAAM,CAAE,QAAAC,EAAS,UAAAC,CAAU,EAAIF,EAC/B,OAAO,KAAK,UAAU,gBAAgB,CAAE,QAAAC,EAAS,UAAWC,EAAU,SAAU,CAAC,CACnF,CAEA,MAAM,qBAAqBF,EAIN,CACnB,OAAO,KAAK,gBAAgBA,CAAI,CAClC,CAWA,UAAUG,EAA8B,CACtC,GAAI,KAAK,qBAAqBJ,EAC5BI,EAAW,uBAAuD,EAClE,KAAK,UAAU,UAAUA,CAAU,MAEnC,OAAM,IAAI,MAAM,yBAAyB,CAE7C,CAUA,OAAO,YAAYC,EAAgD,CACjE,IAAMC,EAAQD,EAAa,wBAAwB,EACnD,OAAQC,EAAO,CACb,OACE,OAAO,IAAIV,EAAmBI,EAAiB,YAAYK,CAAY,CAAC,EAC1E,QACE,MAAM,IAAI,MAAM,iDAAiDC,CAAK,EAAE,CAC5E,CACF,CAUA,OAAO,YAAYR,EAAuD,CACxE,OAAOA,aAAqBF,CAC9B,CACF,EASaW,EAAN,MAAMC,UAA2BC,CAAU,CAQhD,YAAYN,EAAsB,CAChC,MAAM,EACN,IAAMO,EAAgBP,EAAU,YAAY,KAC5C,OAAQO,EAAe,CACrB,KAAKC,EAAiB,KACpB,KAAK,UAAYR,EACjB,MACF,QACE,MAAM,IAAI,MAAM,kDAAkDO,CAAa,EAAE,CACrF,CACF,CAWA,OAAO,QAAQE,EAAwC,CACrD,IAAMC,EAAOC,EAAI,aAAaF,CAAQ,EAChCP,EAAe,IAAIU,EAAaF,EAAK,aAAa,CAAC,EACzD,OAAOL,EAAmB,YAAYH,CAAY,CACpD,CAEA,UAAUD,EAA8B,CACtC,GAAI,KAAK,qBAAqBO,EAC5BP,EAAW,uBAAuD,EAClE,KAAK,UAAU,UAAUA,CAAU,MAEnC,OAAM,IAAI,MAAM,wBAAwB,CAE5C,CAEA,OAAO,YAAYC,EAAgD,CACjE,IAAMC,EAAQD,EAAa,wBAAwB,EACnD,OAAQC,EAAO,CACb,OACE,OAAO,IAAIE,EAAmBG,EAAiB,YAAYN,CAAY,CAAC,EAC1E,QACE,MAAM,IAAI,MAAM,iDAAiDC,CAAK,EAAE,CAC5E,CACF,CACF", "names": ["EphemeralPublicKey", "_EphemeralPublicKey", "PublicKey", "public<PERSON>ey", "publicKeyType", "Ed25519PublicKey", "args", "message", "signature", "serializer", "deserializer", "index", "EphemeralSignature", "_EphemeralSignature", "Signature", "signatureType", "Ed25519Signature", "hexInput", "data", "Hex", "Deserializer"]}