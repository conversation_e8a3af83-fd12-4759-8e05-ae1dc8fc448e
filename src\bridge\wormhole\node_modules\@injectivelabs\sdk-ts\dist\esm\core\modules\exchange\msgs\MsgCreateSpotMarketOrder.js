import { MsgBase } from '../../MsgBase.js';
import { amountToCosmosSdkDecAmount, numberToCosmosSdkDecString } from '../../../../utils/numbers.js';
import snakecaseKeys from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Exchange, InjectiveExchangeV1Beta1Tx, } from '@injectivelabs/core-proto-ts';
const createMarketOrder = (params) => {
    const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    orderInfo.subaccountId = params.subaccountId;
    orderInfo.feeRecipient = params.feeRecipient;
    orderInfo.price = params.price;
    orderInfo.quantity = params.quantity;
    if (params.cid) {
        orderInfo.cid = params.cid;
    }
    const spotOrder = InjectiveExchangeV1Beta1Exchange.SpotOrder.create();
    spotOrder.marketId = params.marketId;
    spotOrder.orderInfo = orderInfo;
    spotOrder.orderType = params.orderType;
    spotOrder.triggerPrice = params.triggerPrice || '0';
    const message = InjectiveExchangeV1Beta1Tx.MsgCreateSpotMarketOrder.create();
    message.sender = params.injectiveAddress;
    message.order = spotOrder;
    return InjectiveExchangeV1Beta1Tx.MsgCreateSpotMarketOrder.fromPartial(message);
};
/**
 * @category Messages
 */
export default class MsgCreateSpotMarketOrder extends MsgBase {
    static fromJSON(params) {
        return new MsgCreateSpotMarketOrder(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            price: amountToCosmosSdkDecAmount(initialParams.price).toFixed(),
            triggerPrice: amountToCosmosSdkDecAmount(initialParams.triggerPrice || 0).toFixed(),
            quantity: amountToCosmosSdkDecAmount(initialParams.quantity).toFixed(),
        };
        return createMarketOrder(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateSpotMarketOrder',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const order = createMarketOrder(params);
        const message = {
            ...snakecaseKeys(order),
        };
        return {
            type: 'exchange/MsgCreateSpotMarketOrder',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgCreateSpotMarketOrder',
            ...value,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const order = web3gw.order;
        const messageAdjusted = {
            ...web3gw,
            order: {
                ...order,
                order_info: {
                    ...order.order_info,
                    price: numberToCosmosSdkDecString(params.price),
                    quantity: numberToCosmosSdkDecString(params.quantity),
                },
                trigger_price: numberToCosmosSdkDecString(params.triggerPrice || '0'),
                order_type: InjectiveExchangeV1Beta1Exchange.orderTypeToJSON(params.orderType),
            },
        };
        return messageAdjusted;
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const messageAdjusted = {
            ...value,
            order: {
                ...value.order,
                order_info: {
                    ...value.order?.order_info,
                    price: amountToCosmosSdkDecAmount(params.price).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(params.quantity).toFixed(),
                },
                trigger_price: amountToCosmosSdkDecAmount(params.triggerPrice || '0').toFixed(),
            },
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgCreateSpotMarketOrder',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgCreateSpotMarketOrder.encode(this.toProto()).finish();
    }
}
