{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;AACA,mDAAsE;AACtE,iCAAoF;AAEpF,SAAgB,oBAAoB,CAAC,MAAc,EAAE,MAAc;IACjE,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE;QAClD,MAAM,IAAI,KAAK,CACb,oEAA6D,MAAM,+BAAqB,MAAM,CAAC,KAAK,CAClG,IAAI,EACJ,iBAAiB,CAClB,CAAE,CACJ,CAAA;KACF;AACH,CAAC;AATD,oDASC;AAED;IAAA;IAiGA,CAAC;IAhGe,6BAAiB,GAA/B,UAAgC,UAAyC;QACvE,IAAI,cAAc,CAAA;QAClB,IAAI,gBAAgB,CAAA;QACpB,IAAI,UAAU,IAAI,IAAA,oBAAY,EAAC,UAAU,CAAC,EAAE;YAC1C,cAAc,GAAG,UAAU,CAAA;YAC3B,IAAM,aAAa,GAAqB,EAAE,CAAA;YAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,IAAI,GAAmB,UAAU,CAAC,CAAC,CAAC,CAAA;gBAC1C,IAAM,aAAa,GAAG,IAAA,0BAAQ,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC5C,IAAM,YAAY,GAAa,EAAE,CAAA;gBACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC5D,YAAY,CAAC,IAAI,CAAC,IAAA,0BAAQ,EAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;iBACrD;gBACD,aAAa,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC,CAAA;aAClD;YACD,gBAAgB,GAAG,aAAa,CAAA;SACjC;aAAM;YACL,gBAAgB,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAA;YACnC,iBAAiB;YACjB,IAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;gBAChC,IAAM,OAAO,GAAG,IAAA,6BAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACpC,IAAM,WAAW,GAAa,EAAE,CAAA;gBAChC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;oBAChD,WAAW,CAAC,IAAI,CAAC,IAAA,6BAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAC7C;gBACD,IAAM,QAAQ,GAAmB;oBAC/B,OAAO,SAAA;oBACP,WAAW,aAAA;iBACZ,CAAA;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACpB;YACD,cAAc,GAAG,IAAI,CAAA;SACtB;QAED,OAAO;YACL,cAAc,gBAAA;YACd,UAAU,EAAE,gBAAgB;SAC7B,CAAA;IACH,CAAC;IAEa,4BAAgB,GAA9B,UAA+B,UAA4B;QACzD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAChD,IAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;YACtC,IAAM,OAAO,GAAW,cAAc,CAAC,CAAC,CAAC,CAAA;YACzC,IAAM,YAAY,GAAa,cAAc,CAAC,CAAC,CAAC,CAAA;YAChD,IAAU,cAAe,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC1C,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAA;aACF;YACD,IAAI,OAAO,CAAC,MAAM,IAAI,EAAE,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;YACD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBAC1E,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,MAAM,IAAI,EAAE,EAAE;oBAC1C,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAA;iBACxF;aACF;SACF;IACH,CAAC;IAEa,6BAAiB,GAA/B,UAAgC,UAA4B;QAC1D,IAAM,cAAc,GAAG,EAAE,CAAA;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAM,IAAI,GAAQ,UAAU,CAAC,KAAK,CAAC,CAAA;YACnC,IAAM,QAAQ,GAAQ;gBACpB,OAAO,EAAE,IAAI,GAAG,IAAA,+BAAa,EAAS,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAClE,WAAW,EAAE,EAAE;aAChB,CAAA;YACD,IAAM,YAAY,GAAa,IAAI,CAAC,CAAC,CAAC,CAAA;YACtC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACrD,IAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACtC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,IAAA,+BAAa,EAAC,WAAW,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;aACjF;YACD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC9B;QACD,OAAO,cAAc,CAAA;IACvB,CAAC;IAEa,6BAAiB,GAA/B,UAAgC,UAA4B,EAAE,MAAc;QAC1E,IAAM,wBAAwB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAA;QACtF,IAAM,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;QAEhF,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;YAC9B,IAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC5B,KAAK,IAAI,YAAY,CAAC,MAAM,CAAA;SAC7B;QAED,IAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAA;QACnC,OAAO,SAAS,GAAG,qBAAqB,GAAG,KAAK,GAAG,wBAAwB,CAAA;IAC7E,CAAC;IACH,kBAAC;AAAD,CAAC,AAjGD,IAiGC;AAjGY,kCAAW"}