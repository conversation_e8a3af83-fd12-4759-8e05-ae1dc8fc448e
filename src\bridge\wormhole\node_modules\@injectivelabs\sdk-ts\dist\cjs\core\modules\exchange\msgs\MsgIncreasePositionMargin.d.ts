import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgIncreasePositionMargin {
    interface Params {
        marketId: string;
        injectiveAddress: string;
        srcSubaccountId: string;
        dstSubaccountId: string;
        amount: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin;
}
/**
 * @category Messages
 */
export default class MsgIncreasePositionMargin extends MsgBase<MsgIncreasePositionMargin.Params, MsgIncreasePositionMargin.Proto> {
    static fromJSON(params: MsgIncreasePositionMargin.Params): MsgIncreasePositionMargin;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin;
    toData(): {
        sender: string;
        sourceSubaccountId: string;
        destinationSubaccountId: string;
        marketId: string;
        amount: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            source_subaccount_id: string;
            destination_subaccount_id: string;
            market_id: string;
            amount: string;
        };
    };
    toWeb3Gw(): {
        sender: string;
        source_subaccount_id: string;
        destination_subaccount_id: string;
        market_id: string;
        amount: string;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            amount: string;
            sender: string;
            source_subaccount_id: string;
            destination_subaccount_id: string;
            market_id: string;
        };
    };
    toEip712V2(): {
        amount: string;
        sender: string;
        source_subaccount_id: string;
        destination_subaccount_id: string;
        market_id: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin;
    };
    toBinary(): Uint8Array;
}
