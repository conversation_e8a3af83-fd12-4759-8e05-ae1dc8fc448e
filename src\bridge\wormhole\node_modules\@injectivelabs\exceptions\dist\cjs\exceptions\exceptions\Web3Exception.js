"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Web3Exception = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
class Web3Exception extends base_js_1.ConcreteException {
    static errorClass = 'Web3Exception';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.Web3;
    }
    parse() {
        this.setName(Web3Exception.errorClass);
    }
}
exports.Web3Exception = Web3Exception;
