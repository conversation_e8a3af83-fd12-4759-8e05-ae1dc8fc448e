/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.gov.module.v1";
function createBaseModule() {
    return { maxMetadataLen: "0", authority: "" };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.maxMetadataLen !== "0") {
            writer.uint32(8).uint64(message.maxMetadataLen);
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.maxMetadataLen = longToString(reader.uint64());
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            maxMetadataLen: isSet(object.maxMetadataLen) ? String(object.maxMetadataLen) : "0",
            authority: isSet(object.authority) ? String(object.authority) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.maxMetadataLen !== undefined && (obj.maxMetadataLen = message.maxMetadataLen);
        message.authority !== undefined && (obj.authority = message.authority);
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.maxMetadataLen = object.maxMetadataLen ?? "0";
        message.authority = object.authority ?? "";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
