"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecPrivilegedArgBase_js_1 = require("../ExecPrivilegedArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecPrivilegedArgOffChainVaultRedeem extends ExecPrivilegedArgBase_js_1.ExecPrivilegedArgBase {
    static fromJSON(params) {
        return new ExecPrivilegedArgOffChainVaultRedeem(params);
    }
    toData() {
        const { params } = this;
        return {
            Redeem: { args: params.args },
        };
    }
    toExecData() {
        const { params } = this;
        return (0, ExecPrivilegedArgBase_js_1.dataToExecData)(this.toData(), {
            origin: params.origin,
            name: 'VaultRedeem',
        });
    }
}
exports.default = ExecPrivilegedArgOffChainVaultRedeem;
