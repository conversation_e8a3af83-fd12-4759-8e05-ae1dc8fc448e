"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const grpc_js_1 = require("../../utils/grpc.js");
const GrpcWebImpl_js_1 = require("./GrpcWebImpl.js");
/**
 * @hidden
 */
class BaseGrpcWebConsumer extends GrpcWebImpl_js_1.GrpcWebImpl {
    module = '';
    constructor(endpoint) {
        super(endpoint, {
            transport: (0, grpc_js_1.getGrpcTransport)(),
            setCookieMetadata: true
        });
    }
    static getGrpcWebImpl = (endpoint) => new BaseGrpcWebConsumer(endpoint);
}
exports.default = BaseGrpcWebConsumer;
