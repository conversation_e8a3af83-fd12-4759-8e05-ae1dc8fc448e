import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "ibc.lightclients.wasm.v1";
/** MsgStoreCode defines the request type for the StoreCode rpc. */
export interface MsgStoreCode {
    /** signer address */
    signer: string;
    /** wasm byte code of light client contract. It can be raw or gzip compressed */
    wasmByteCode: Uint8Array;
}
/** MsgStoreCodeResponse defines the response type for the StoreCode rpc */
export interface MsgStoreCodeResponse {
    /** checksum is the sha256 hash of the stored code */
    checksum: Uint8Array;
}
/** MsgRemoveChecksum defines the request type for the MsgRemoveChecksum rpc. */
export interface MsgRemoveChecksum {
    /** signer address */
    signer: string;
    /** checksum is the sha256 hash to be removed from the store */
    checksum: Uint8Array;
}
/** MsgStoreChecksumResponse defines the response type for the StoreCode rpc */
export interface MsgRemoveChecksumResponse {
}
/** MsgMigrateContract defines the request type for the MigrateContract rpc. */
export interface MsgMigrateContract {
    /** signer address */
    signer: string;
    /** the client id of the contract */
    clientId: string;
    /** checksum is the sha256 hash of the new wasm byte code for the contract */
    checksum: Uint8Array;
    /** the json encoded message to be passed to the contract on migration */
    msg: Uint8Array;
}
/** MsgMigrateContractResponse defines the response type for the MigrateContract rpc */
export interface MsgMigrateContractResponse {
}
export declare const MsgStoreCode: {
    encode(message: MsgStoreCode, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgStoreCode;
    fromJSON(object: any): MsgStoreCode;
    toJSON(message: MsgStoreCode): unknown;
    create(base?: DeepPartial<MsgStoreCode>): MsgStoreCode;
    fromPartial(object: DeepPartial<MsgStoreCode>): MsgStoreCode;
};
export declare const MsgStoreCodeResponse: {
    encode(message: MsgStoreCodeResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgStoreCodeResponse;
    fromJSON(object: any): MsgStoreCodeResponse;
    toJSON(message: MsgStoreCodeResponse): unknown;
    create(base?: DeepPartial<MsgStoreCodeResponse>): MsgStoreCodeResponse;
    fromPartial(object: DeepPartial<MsgStoreCodeResponse>): MsgStoreCodeResponse;
};
export declare const MsgRemoveChecksum: {
    encode(message: MsgRemoveChecksum, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRemoveChecksum;
    fromJSON(object: any): MsgRemoveChecksum;
    toJSON(message: MsgRemoveChecksum): unknown;
    create(base?: DeepPartial<MsgRemoveChecksum>): MsgRemoveChecksum;
    fromPartial(object: DeepPartial<MsgRemoveChecksum>): MsgRemoveChecksum;
};
export declare const MsgRemoveChecksumResponse: {
    encode(_: MsgRemoveChecksumResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRemoveChecksumResponse;
    fromJSON(_: any): MsgRemoveChecksumResponse;
    toJSON(_: MsgRemoveChecksumResponse): unknown;
    create(base?: DeepPartial<MsgRemoveChecksumResponse>): MsgRemoveChecksumResponse;
    fromPartial(_: DeepPartial<MsgRemoveChecksumResponse>): MsgRemoveChecksumResponse;
};
export declare const MsgMigrateContract: {
    encode(message: MsgMigrateContract, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgMigrateContract;
    fromJSON(object: any): MsgMigrateContract;
    toJSON(message: MsgMigrateContract): unknown;
    create(base?: DeepPartial<MsgMigrateContract>): MsgMigrateContract;
    fromPartial(object: DeepPartial<MsgMigrateContract>): MsgMigrateContract;
};
export declare const MsgMigrateContractResponse: {
    encode(_: MsgMigrateContractResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgMigrateContractResponse;
    fromJSON(_: any): MsgMigrateContractResponse;
    toJSON(_: MsgMigrateContractResponse): unknown;
    create(base?: DeepPartial<MsgMigrateContractResponse>): MsgMigrateContractResponse;
    fromPartial(_: DeepPartial<MsgMigrateContractResponse>): MsgMigrateContractResponse;
};
/** Msg defines the ibc/08-wasm Msg service. */
export interface Msg {
    /** StoreCode defines a rpc handler method for MsgStoreCode. */
    StoreCode(request: DeepPartial<MsgStoreCode>, metadata?: grpc.Metadata): Promise<MsgStoreCodeResponse>;
    /** RemoveChecksum defines a rpc handler method for MsgRemoveChecksum. */
    RemoveChecksum(request: DeepPartial<MsgRemoveChecksum>, metadata?: grpc.Metadata): Promise<MsgRemoveChecksumResponse>;
    /** MigrateContract defines a rpc handler method for MsgMigrateContract. */
    MigrateContract(request: DeepPartial<MsgMigrateContract>, metadata?: grpc.Metadata): Promise<MsgMigrateContractResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    StoreCode(request: DeepPartial<MsgStoreCode>, metadata?: grpc.Metadata): Promise<MsgStoreCodeResponse>;
    RemoveChecksum(request: DeepPartial<MsgRemoveChecksum>, metadata?: grpc.Metadata): Promise<MsgRemoveChecksumResponse>;
    MigrateContract(request: DeepPartial<MsgMigrateContract>, metadata?: grpc.Metadata): Promise<MsgMigrateContractResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgStoreCodeDesc: UnaryMethodDefinitionish;
export declare const MsgRemoveChecksumDesc: UnaryMethodDefinitionish;
export declare const MsgMigrateContractDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
