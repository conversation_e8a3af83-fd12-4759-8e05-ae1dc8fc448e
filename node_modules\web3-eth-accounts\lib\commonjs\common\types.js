"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeOutput = void 0;
/**
 * Type output options
 */
var TypeOutput;
(function (TypeOutput) {
    TypeOutput[TypeOutput["Number"] = 0] = "Number";
    TypeOutput[TypeOutput["BigInt"] = 1] = "BigInt";
    TypeOutput[TypeOutput["Uint8Array"] = 2] = "Uint8Array";
    TypeOutput[TypeOutput["PrefixedHexString"] = 3] = "PrefixedHexString";
})(TypeOutput || (exports.TypeOutput = TypeOutput = {}));
//# sourceMappingURL=types.js.map