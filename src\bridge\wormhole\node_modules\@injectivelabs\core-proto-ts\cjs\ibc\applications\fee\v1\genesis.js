"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ForwardRelayerAddress = exports.RegisteredCounterpartyPayee = exports.RegisteredPayee = exports.FeeEnabledChannel = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var channel_1 = require("../../../core/channel/v1/channel.js");
var fee_1 = require("./fee.js");
exports.protobufPackage = "ibc.applications.fee.v1";
function createBaseGenesisState() {
    return {
        identifiedFees: [],
        feeEnabledChannels: [],
        registeredPayees: [],
        registeredCounterpartyPayees: [],
        forwardRelayers: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _f = __values(message.identifiedFees), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                fee_1.IdentifiedPacketFees.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_a = _f.return)) _a.call(_f);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _h = __values(message.feeEnabledChannels), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                exports.FeeEnabledChannel.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_b = _h.return)) _b.call(_h);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _k = __values(message.registeredPayees), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                exports.RegisteredPayee.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_c = _k.return)) _c.call(_k);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _m = __values(message.registeredCounterpartyPayees), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                exports.RegisteredCounterpartyPayee.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_d = _m.return)) _d.call(_m);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _p = __values(message.forwardRelayers), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                exports.ForwardRelayerAddress.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_e = _p.return)) _e.call(_p);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.identifiedFees.push(fee_1.IdentifiedPacketFees.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.feeEnabledChannels.push(exports.FeeEnabledChannel.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.registeredPayees.push(exports.RegisteredPayee.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.registeredCounterpartyPayees.push(exports.RegisteredCounterpartyPayee.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.forwardRelayers.push(exports.ForwardRelayerAddress.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            identifiedFees: Array.isArray(object === null || object === void 0 ? void 0 : object.identifiedFees)
                ? object.identifiedFees.map(function (e) { return fee_1.IdentifiedPacketFees.fromJSON(e); })
                : [],
            feeEnabledChannels: Array.isArray(object === null || object === void 0 ? void 0 : object.feeEnabledChannels)
                ? object.feeEnabledChannels.map(function (e) { return exports.FeeEnabledChannel.fromJSON(e); })
                : [],
            registeredPayees: Array.isArray(object === null || object === void 0 ? void 0 : object.registeredPayees)
                ? object.registeredPayees.map(function (e) { return exports.RegisteredPayee.fromJSON(e); })
                : [],
            registeredCounterpartyPayees: Array.isArray(object === null || object === void 0 ? void 0 : object.registeredCounterpartyPayees)
                ? object.registeredCounterpartyPayees.map(function (e) { return exports.RegisteredCounterpartyPayee.fromJSON(e); })
                : [],
            forwardRelayers: Array.isArray(object === null || object === void 0 ? void 0 : object.forwardRelayers)
                ? object.forwardRelayers.map(function (e) { return exports.ForwardRelayerAddress.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.identifiedFees) {
            obj.identifiedFees = message.identifiedFees.map(function (e) { return e ? fee_1.IdentifiedPacketFees.toJSON(e) : undefined; });
        }
        else {
            obj.identifiedFees = [];
        }
        if (message.feeEnabledChannels) {
            obj.feeEnabledChannels = message.feeEnabledChannels.map(function (e) { return e ? exports.FeeEnabledChannel.toJSON(e) : undefined; });
        }
        else {
            obj.feeEnabledChannels = [];
        }
        if (message.registeredPayees) {
            obj.registeredPayees = message.registeredPayees.map(function (e) { return e ? exports.RegisteredPayee.toJSON(e) : undefined; });
        }
        else {
            obj.registeredPayees = [];
        }
        if (message.registeredCounterpartyPayees) {
            obj.registeredCounterpartyPayees = message.registeredCounterpartyPayees.map(function (e) {
                return e ? exports.RegisteredCounterpartyPayee.toJSON(e) : undefined;
            });
        }
        else {
            obj.registeredCounterpartyPayees = [];
        }
        if (message.forwardRelayers) {
            obj.forwardRelayers = message.forwardRelayers.map(function (e) { return e ? exports.ForwardRelayerAddress.toJSON(e) : undefined; });
        }
        else {
            obj.forwardRelayers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseGenesisState();
        message.identifiedFees = ((_a = object.identifiedFees) === null || _a === void 0 ? void 0 : _a.map(function (e) { return fee_1.IdentifiedPacketFees.fromPartial(e); })) || [];
        message.feeEnabledChannels = ((_b = object.feeEnabledChannels) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.FeeEnabledChannel.fromPartial(e); })) || [];
        message.registeredPayees = ((_c = object.registeredPayees) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.RegisteredPayee.fromPartial(e); })) || [];
        message.registeredCounterpartyPayees =
            ((_d = object.registeredCounterpartyPayees) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.RegisteredCounterpartyPayee.fromPartial(e); })) || [];
        message.forwardRelayers = ((_e = object.forwardRelayers) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.ForwardRelayerAddress.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseFeeEnabledChannel() {
    return { portId: "", channelId: "" };
}
exports.FeeEnabledChannel = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.portId !== "") {
            writer.uint32(10).string(message.portId);
        }
        if (message.channelId !== "") {
            writer.uint32(18).string(message.channelId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeEnabledChannel();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.portId = reader.string();
                    break;
                case 2:
                    message.channelId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            portId: isSet(object.portId) ? String(object.portId) : "",
            channelId: isSet(object.channelId) ? String(object.channelId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.portId !== undefined && (obj.portId = message.portId);
        message.channelId !== undefined && (obj.channelId = message.channelId);
        return obj;
    },
    create: function (base) {
        return exports.FeeEnabledChannel.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeeEnabledChannel();
        message.portId = (_a = object.portId) !== null && _a !== void 0 ? _a : "";
        message.channelId = (_b = object.channelId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseRegisteredPayee() {
    return { channelId: "", relayer: "", payee: "" };
}
exports.RegisteredPayee = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.channelId !== "") {
            writer.uint32(10).string(message.channelId);
        }
        if (message.relayer !== "") {
            writer.uint32(18).string(message.relayer);
        }
        if (message.payee !== "") {
            writer.uint32(26).string(message.payee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRegisteredPayee();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.channelId = reader.string();
                    break;
                case 2:
                    message.relayer = reader.string();
                    break;
                case 3:
                    message.payee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            channelId: isSet(object.channelId) ? String(object.channelId) : "",
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            payee: isSet(object.payee) ? String(object.payee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.channelId !== undefined && (obj.channelId = message.channelId);
        message.relayer !== undefined && (obj.relayer = message.relayer);
        message.payee !== undefined && (obj.payee = message.payee);
        return obj;
    },
    create: function (base) {
        return exports.RegisteredPayee.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseRegisteredPayee();
        message.channelId = (_a = object.channelId) !== null && _a !== void 0 ? _a : "";
        message.relayer = (_b = object.relayer) !== null && _b !== void 0 ? _b : "";
        message.payee = (_c = object.payee) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseRegisteredCounterpartyPayee() {
    return { channelId: "", relayer: "", counterpartyPayee: "" };
}
exports.RegisteredCounterpartyPayee = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.channelId !== "") {
            writer.uint32(10).string(message.channelId);
        }
        if (message.relayer !== "") {
            writer.uint32(18).string(message.relayer);
        }
        if (message.counterpartyPayee !== "") {
            writer.uint32(26).string(message.counterpartyPayee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRegisteredCounterpartyPayee();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.channelId = reader.string();
                    break;
                case 2:
                    message.relayer = reader.string();
                    break;
                case 3:
                    message.counterpartyPayee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            channelId: isSet(object.channelId) ? String(object.channelId) : "",
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            counterpartyPayee: isSet(object.counterpartyPayee) ? String(object.counterpartyPayee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.channelId !== undefined && (obj.channelId = message.channelId);
        message.relayer !== undefined && (obj.relayer = message.relayer);
        message.counterpartyPayee !== undefined && (obj.counterpartyPayee = message.counterpartyPayee);
        return obj;
    },
    create: function (base) {
        return exports.RegisteredCounterpartyPayee.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseRegisteredCounterpartyPayee();
        message.channelId = (_a = object.channelId) !== null && _a !== void 0 ? _a : "";
        message.relayer = (_b = object.relayer) !== null && _b !== void 0 ? _b : "";
        message.counterpartyPayee = (_c = object.counterpartyPayee) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseForwardRelayerAddress() {
    return { address: "", packetId: undefined };
}
exports.ForwardRelayerAddress = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.packetId !== undefined) {
            channel_1.PacketId.encode(message.packetId, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseForwardRelayerAddress();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.packetId = channel_1.PacketId.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            packetId: isSet(object.packetId) ? channel_1.PacketId.fromJSON(object.packetId) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.packetId !== undefined && (obj.packetId = message.packetId ? channel_1.PacketId.toJSON(message.packetId) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ForwardRelayerAddress.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseForwardRelayerAddress();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.packetId = (object.packetId !== undefined && object.packetId !== null)
            ? channel_1.PacketId.fromPartial(object.packetId)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
