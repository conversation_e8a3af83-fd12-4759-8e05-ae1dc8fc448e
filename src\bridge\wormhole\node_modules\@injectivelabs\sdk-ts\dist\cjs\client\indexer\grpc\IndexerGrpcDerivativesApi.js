"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcDerivativesApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcDerivativesApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_1.IndexerModule.Derivatives;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client =
            new indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.InjectiveDerivativeExchangeRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchMarkets(params) {
        const { marketStatus, quoteDenom, marketStatuses } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.MarketsRequest.create();
        if (marketStatus) {
            request.marketStatus = marketStatus;
        }
        if (marketStatuses) {
            request.marketStatuses = marketStatuses;
        }
        if (quoteDenom) {
            request.quoteDenom = quoteDenom;
        }
        try {
            const response = await this.retry(() => this.client.Markets(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.marketsResponseToMarkets(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Markets',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Markets',
                contextModule: this.module,
            });
        }
    }
    async fetchMarket(marketId) {
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.MarketRequest.create();
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.Market(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.marketResponseToMarket(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Market',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Market',
                contextModule: this.module,
            });
        }
    }
    async fetchBinaryOptionsMarkets(params) {
        const { marketStatus, quoteDenom, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.BinaryOptionsMarketsRequest.create();
        if (marketStatus) {
            request.marketStatus = marketStatus;
        }
        if (quoteDenom) {
            request.quoteDenom = quoteDenom;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.BinaryOptionsMarkets(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.binaryOptionsMarketResponseWithPaginationToBinaryOptionsMarket(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'BinaryOptionsMarkets',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'BinaryOptionsMarkets',
                contextModule: this.module,
            });
        }
    }
    async fetchBinaryOptionsMarket(marketId) {
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.BinaryOptionsMarketRequest.create();
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.BinaryOptionsMarket(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.binaryOptionsMarketResponseToBinaryOptionsMarket(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'BinaryOptionsMarket',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'BinaryOptionsMarket',
                contextModule: this.module,
            });
        }
    }
    /** @deprecated - use fetchOrderbookV2 */
    async fetchOrderbook(_marketId) {
        throw new exceptions_1.GeneralException(new Error('deprecated - use fetchOrderbookV2'));
    }
    async fetchOrders(params) {
        const { cid, marketId, marketIds, orderSide, pagination, isConditional, subaccountId, tradeId, } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.OrdersRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (orderSide) {
            request.orderSide = orderSide;
        }
        if (isConditional !== undefined) {
            request.isConditional = isConditional ? 'true' : 'false';
        }
        if (cid) {
            request.cid = cid;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.Orders(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.ordersResponseToOrders(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Orders',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Orders',
                contextModule: this.module,
            });
        }
    }
    async fetchOrderHistory(params) {
        const { cid, state, tradeId, marketId, marketIds, direction, pagination, orderTypes, subaccountId, isConditional, executionTypes, } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.OrdersHistoryRequest.create();
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (orderTypes) {
            request.orderTypes = orderTypes;
        }
        if (executionTypes) {
            request.executionTypes = executionTypes;
        }
        if (direction) {
            request.direction = direction;
        }
        if (isConditional !== undefined) {
            request.isConditional = isConditional ? 'true' : 'false';
        }
        if (state) {
            request.state = state;
        }
        if (cid) {
            request.cid = cid;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.OrdersHistory(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.orderHistoryResponseToOrderHistory(response, isConditional);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'OrdersHistory',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'OrdersHistory',
                contextModule: this.module,
            });
        }
    }
    async fetchPositions(params) {
        const { marketId, marketIds, subaccountId, direction, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.PositionsRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (direction) {
            request.direction = direction;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.Positions(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.positionsResponseToPositions(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Positions',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Positions',
                contextModule: this.module,
            });
        }
    }
    async fetchPositionsV2(params) {
        const { marketId, marketIds, subaccountId, direction, pagination, address, } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.PositionsV2Request.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (address) {
            request.accountAddress = address;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (direction) {
            request.direction = direction;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.PositionsV2(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.positionsV2ResponseToPositionsV2(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Positions',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Positions',
                contextModule: this.module,
            });
        }
    }
    async fetchTrades(params) {
        const { endTime, tradeId, marketId, startTime, direction, marketIds, pagination, subaccountId, executionSide, executionTypes, accountAddress, cid, } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.TradesRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (tradeId) {
            request.tradeId = tradeId;
        }
        if (accountAddress) {
            request.accountAddress = accountAddress;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (executionTypes) {
            request.executionTypes = executionTypes;
        }
        if (executionSide) {
            request.executionSide = executionSide;
        }
        if (direction) {
            request.direction = direction;
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (cid) {
            request.cid = cid;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
            if (pagination.startTime !== undefined) {
                request.startTime = pagination.startTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.Trades(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.tradesResponseToTrades(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Trades',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Trades',
                contextModule: this.module,
            });
        }
    }
    async fetchFundingPayments(params) {
        const { marketId, marketIds, subaccountId, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.FundingPaymentsRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
            if (pagination.endTime !== undefined) {
                request.endTime = pagination.endTime.toString();
            }
        }
        try {
            const response = await this.retry(() => this.client.FundingPayments(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.fundingPaymentsResponseToFundingPayments(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'FundingPayments',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'FundingPayments',
                contextModule: this.module,
            });
        }
    }
    async fetchFundingRates(params) {
        const { marketId, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.FundingRatesRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.FundingRates(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.fundingRatesResponseToFundingRates(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'FundingRates',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'FundingRates',
                contextModule: this.module,
            });
        }
    }
    async fetchSubaccountOrdersList(params) {
        const { marketId, subaccountId, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.SubaccountOrdersListRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.SubaccountOrdersList(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.ordersResponseToOrders(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'SubaccountOrdersList',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'SubaccountOrdersList',
                contextModule: this.module,
            });
        }
    }
    async fetchSubaccountTradesList(params) {
        const { marketId, subaccountId, direction, executionType, pagination } = params || {};
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.SubaccountTradesListRequest.create();
        if (marketId) {
            request.marketId = marketId;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (direction) {
            request.direction = direction;
        }
        if (executionType) {
            request.executionType = executionType;
        }
        if (pagination) {
            if (pagination.skip !== undefined) {
                request.skip = pagination.skip.toString();
            }
            if (pagination.limit !== undefined) {
                request.limit = pagination.limit;
            }
        }
        try {
            const response = await this.retry(() => this.client.SubaccountTradesList(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.subaccountTradesListResponseToSubaccountTradesList(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'SubaccountTradesList',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'SubaccountTradesList',
                contextModule: this.module,
            });
        }
    }
    /** @deprecated - use fetchOrderbooksV2 */
    async fetchOrderbooks(_marketIds) {
        throw new exceptions_1.GeneralException(new Error('deprecated - use fetchOrderbooksV2'));
    }
    async fetchOrderbooksV2(marketIds) {
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.OrderbooksV2Request.create();
        if (marketIds.length > 0) {
            request.marketIds = marketIds;
        }
        try {
            const response = await this.retry(() => this.client.OrderbooksV2(request, this.metadata));
            return index_js_2.IndexerGrpcDerivativeTransformer.orderbooksV2ResponseToOrderbooksV2(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'OrderbooksV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'OrderbooksV2',
                contextModule: this.module,
            });
        }
    }
    async fetchOrderbookV2(marketId) {
        const request = indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.OrderbookV2Request.create();
        request.marketId = marketId;
        try {
            const response = await this.retry(() => this.client.OrderbookV2(request));
            return index_js_2.IndexerGrpcDerivativeTransformer.orderbookV2ResponseToOrderbookV2(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveDerivativeExchangeRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'OrderbookV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'OrderbooksV2',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcDerivativesApi = IndexerGrpcDerivativesApi;
