import { InjectiveReferralRpc } from '@injectivelabs/indexer-proto-ts';
import { ReferralDetails } from '../types/index.js';
export declare class IndexerGrpcReferralTransformer {
    static referrerDetailsResponseToReferrerDetails(address: string, response: InjectiveReferralRpc.GetReferrerDetailsResponse): ReferralDetails;
    static inviteeDetailsResponseToInviteeDetails(response: InjectiveReferralRpc.GetInviteeDetailsResponse): InjectiveReferralRpc.GetInviteeDetailsResponse;
    static referrerByCodeResponseToReferrerByCode(response: InjectiveReferralRpc.GetReferrerByCodeResponse): string;
}
