"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.State = exports.ABCIResponsesInfo = exports.ConsensusParamsInfo = exports.ResponseEndBlock = exports.ResponseBeginBlock = exports.LegacyABCIResponses = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var timestamp_1 = require("../../../google/protobuf/timestamp.js");
var types_1 = require("../../abci/v1beta1/types.js");
var types_2 = require("../../abci/v1beta2/types.js");
var types_3 = require("../../abci/v1beta3/types.js");
var params_1 = require("../../types/v1/params.js");
var types_4 = require("../../types/v1beta1/types.js");
var validator_1 = require("../../types/v1beta1/validator.js");
var types_5 = require("../v1beta1/types.js");
exports.protobufPackage = "cometbft.state.v1beta3";
function createBaseLegacyABCIResponses() {
    return { deliverTxs: [], endBlock: undefined, beginBlock: undefined };
}
exports.LegacyABCIResponses = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.deliverTxs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_3.ExecTxResult.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.endBlock !== undefined) {
            exports.ResponseEndBlock.encode(message.endBlock, writer.uint32(18).fork()).ldelim();
        }
        if (message.beginBlock !== undefined) {
            exports.ResponseBeginBlock.encode(message.beginBlock, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLegacyABCIResponses();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.deliverTxs.push(types_3.ExecTxResult.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.endBlock = exports.ResponseEndBlock.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.beginBlock = exports.ResponseBeginBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            deliverTxs: Array.isArray(object === null || object === void 0 ? void 0 : object.deliverTxs) ? object.deliverTxs.map(function (e) { return types_3.ExecTxResult.fromJSON(e); }) : [],
            endBlock: isSet(object.endBlock) ? exports.ResponseEndBlock.fromJSON(object.endBlock) : undefined,
            beginBlock: isSet(object.beginBlock) ? exports.ResponseBeginBlock.fromJSON(object.beginBlock) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.deliverTxs) {
            obj.deliverTxs = message.deliverTxs.map(function (e) { return e ? types_3.ExecTxResult.toJSON(e) : undefined; });
        }
        else {
            obj.deliverTxs = [];
        }
        message.endBlock !== undefined &&
            (obj.endBlock = message.endBlock ? exports.ResponseEndBlock.toJSON(message.endBlock) : undefined);
        message.beginBlock !== undefined &&
            (obj.beginBlock = message.beginBlock ? exports.ResponseBeginBlock.toJSON(message.beginBlock) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.LegacyABCIResponses.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseLegacyABCIResponses();
        message.deliverTxs = ((_a = object.deliverTxs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_3.ExecTxResult.fromPartial(e); })) || [];
        message.endBlock = (object.endBlock !== undefined && object.endBlock !== null)
            ? exports.ResponseEndBlock.fromPartial(object.endBlock)
            : undefined;
        message.beginBlock = (object.beginBlock !== undefined && object.beginBlock !== null)
            ? exports.ResponseBeginBlock.fromPartial(object.beginBlock)
            : undefined;
        return message;
    },
};
function createBaseResponseBeginBlock() {
    return { events: [] };
}
exports.ResponseBeginBlock = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_2.Event.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseBeginBlock();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.events.push(types_2.Event.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_2.Event.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_2.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ResponseBeginBlock.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseResponseBeginBlock();
        message.events = ((_a = object.events) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_2.Event.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseResponseEndBlock() {
    return { validatorUpdates: [], consensusParamUpdates: undefined, events: [] };
}
exports.ResponseEndBlock = {
    encode: function (message, writer) {
        var e_3, _a, e_4, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.validatorUpdates), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                types_1.ValidatorUpdate.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.consensusParamUpdates !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParamUpdates, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.events), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                types_2.Event.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponseEndBlock();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validatorUpdates.push(types_1.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.consensusParamUpdates = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.events.push(types_2.Event.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validatorUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.validatorUpdates)
                ? object.validatorUpdates.map(function (e) { return types_1.ValidatorUpdate.fromJSON(e); })
                : [],
            consensusParamUpdates: isSet(object.consensusParamUpdates)
                ? params_1.ConsensusParams.fromJSON(object.consensusParamUpdates)
                : undefined,
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return types_2.Event.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.validatorUpdates) {
            obj.validatorUpdates = message.validatorUpdates.map(function (e) { return e ? types_1.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validatorUpdates = [];
        }
        message.consensusParamUpdates !== undefined && (obj.consensusParamUpdates = message.consensusParamUpdates
            ? params_1.ConsensusParams.toJSON(message.consensusParamUpdates)
            : undefined);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? types_2.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ResponseEndBlock.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseResponseEndBlock();
        message.validatorUpdates = ((_a = object.validatorUpdates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_1.ValidatorUpdate.fromPartial(e); })) || [];
        message.consensusParamUpdates =
            (object.consensusParamUpdates !== undefined && object.consensusParamUpdates !== null)
                ? params_1.ConsensusParams.fromPartial(object.consensusParamUpdates)
                : undefined;
        message.events = ((_b = object.events) === null || _b === void 0 ? void 0 : _b.map(function (e) { return types_2.Event.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseConsensusParamsInfo() {
    return { consensusParams: undefined, lastHeightChanged: "0" };
}
exports.ConsensusParamsInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(10).fork()).ldelim();
        }
        if (message.lastHeightChanged !== "0") {
            writer.uint32(16).int64(message.lastHeightChanged);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseConsensusParamsInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.lastHeightChanged = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            lastHeightChanged: isSet(object.lastHeightChanged) ? String(object.lastHeightChanged) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        message.lastHeightChanged !== undefined && (obj.lastHeightChanged = message.lastHeightChanged);
        return obj;
    },
    create: function (base) {
        return exports.ConsensusParamsInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseConsensusParamsInfo();
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.lastHeightChanged = (_a = object.lastHeightChanged) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseABCIResponsesInfo() {
    return { legacyAbciResponses: undefined, height: "0", responseFinalizeBlock: undefined };
}
exports.ABCIResponsesInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.legacyAbciResponses !== undefined) {
            exports.LegacyABCIResponses.encode(message.legacyAbciResponses, writer.uint32(10).fork()).ldelim();
        }
        if (message.height !== "0") {
            writer.uint32(16).int64(message.height);
        }
        if (message.responseFinalizeBlock !== undefined) {
            types_3.ResponseFinalizeBlock.encode(message.responseFinalizeBlock, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseABCIResponsesInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.legacyAbciResponses = exports.LegacyABCIResponses.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.height = longToString(reader.int64());
                    break;
                case 3:
                    message.responseFinalizeBlock = types_3.ResponseFinalizeBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            legacyAbciResponses: isSet(object.legacyAbciResponses)
                ? exports.LegacyABCIResponses.fromJSON(object.legacyAbciResponses)
                : undefined,
            height: isSet(object.height) ? String(object.height) : "0",
            responseFinalizeBlock: isSet(object.responseFinalizeBlock)
                ? types_3.ResponseFinalizeBlock.fromJSON(object.responseFinalizeBlock)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.legacyAbciResponses !== undefined && (obj.legacyAbciResponses = message.legacyAbciResponses
            ? exports.LegacyABCIResponses.toJSON(message.legacyAbciResponses)
            : undefined);
        message.height !== undefined && (obj.height = message.height);
        message.responseFinalizeBlock !== undefined && (obj.responseFinalizeBlock = message.responseFinalizeBlock
            ? types_3.ResponseFinalizeBlock.toJSON(message.responseFinalizeBlock)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ABCIResponsesInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseABCIResponsesInfo();
        message.legacyAbciResponses = (object.legacyAbciResponses !== undefined && object.legacyAbciResponses !== null)
            ? exports.LegacyABCIResponses.fromPartial(object.legacyAbciResponses)
            : undefined;
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.responseFinalizeBlock =
            (object.responseFinalizeBlock !== undefined && object.responseFinalizeBlock !== null)
                ? types_3.ResponseFinalizeBlock.fromPartial(object.responseFinalizeBlock)
                : undefined;
        return message;
    },
};
function createBaseState() {
    return {
        version: undefined,
        chainId: "",
        initialHeight: "0",
        lastBlockHeight: "0",
        lastBlockId: undefined,
        lastBlockTime: undefined,
        nextValidators: undefined,
        validators: undefined,
        lastValidators: undefined,
        lastHeightValidatorsChanged: "0",
        consensusParams: undefined,
        lastHeightConsensusParamsChanged: "0",
        lastResultsHash: new Uint8Array(),
        appHash: new Uint8Array(),
    };
}
exports.State = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.version !== undefined) {
            types_5.Version.encode(message.version, writer.uint32(10).fork()).ldelim();
        }
        if (message.chainId !== "") {
            writer.uint32(18).string(message.chainId);
        }
        if (message.initialHeight !== "0") {
            writer.uint32(112).int64(message.initialHeight);
        }
        if (message.lastBlockHeight !== "0") {
            writer.uint32(24).int64(message.lastBlockHeight);
        }
        if (message.lastBlockId !== undefined) {
            types_4.BlockID.encode(message.lastBlockId, writer.uint32(34).fork()).ldelim();
        }
        if (message.lastBlockTime !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.lastBlockTime), writer.uint32(42).fork()).ldelim();
        }
        if (message.nextValidators !== undefined) {
            validator_1.ValidatorSet.encode(message.nextValidators, writer.uint32(50).fork()).ldelim();
        }
        if (message.validators !== undefined) {
            validator_1.ValidatorSet.encode(message.validators, writer.uint32(58).fork()).ldelim();
        }
        if (message.lastValidators !== undefined) {
            validator_1.ValidatorSet.encode(message.lastValidators, writer.uint32(66).fork()).ldelim();
        }
        if (message.lastHeightValidatorsChanged !== "0") {
            writer.uint32(72).int64(message.lastHeightValidatorsChanged);
        }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(82).fork()).ldelim();
        }
        if (message.lastHeightConsensusParamsChanged !== "0") {
            writer.uint32(88).int64(message.lastHeightConsensusParamsChanged);
        }
        if (message.lastResultsHash.length !== 0) {
            writer.uint32(98).bytes(message.lastResultsHash);
        }
        if (message.appHash.length !== 0) {
            writer.uint32(106).bytes(message.appHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.version = types_5.Version.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.chainId = reader.string();
                    break;
                case 14:
                    message.initialHeight = longToString(reader.int64());
                    break;
                case 3:
                    message.lastBlockHeight = longToString(reader.int64());
                    break;
                case 4:
                    message.lastBlockId = types_4.BlockID.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.lastBlockTime = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.nextValidators = validator_1.ValidatorSet.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.validators = validator_1.ValidatorSet.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.lastValidators = validator_1.ValidatorSet.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.lastHeightValidatorsChanged = longToString(reader.int64());
                    break;
                case 10:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 11:
                    message.lastHeightConsensusParamsChanged = longToString(reader.int64());
                    break;
                case 12:
                    message.lastResultsHash = reader.bytes();
                    break;
                case 13:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            version: isSet(object.version) ? types_5.Version.fromJSON(object.version) : undefined,
            chainId: isSet(object.chainId) ? String(object.chainId) : "",
            initialHeight: isSet(object.initialHeight) ? String(object.initialHeight) : "0",
            lastBlockHeight: isSet(object.lastBlockHeight) ? String(object.lastBlockHeight) : "0",
            lastBlockId: isSet(object.lastBlockId) ? types_4.BlockID.fromJSON(object.lastBlockId) : undefined,
            lastBlockTime: isSet(object.lastBlockTime) ? fromJsonTimestamp(object.lastBlockTime) : undefined,
            nextValidators: isSet(object.nextValidators) ? validator_1.ValidatorSet.fromJSON(object.nextValidators) : undefined,
            validators: isSet(object.validators) ? validator_1.ValidatorSet.fromJSON(object.validators) : undefined,
            lastValidators: isSet(object.lastValidators) ? validator_1.ValidatorSet.fromJSON(object.lastValidators) : undefined,
            lastHeightValidatorsChanged: isSet(object.lastHeightValidatorsChanged)
                ? String(object.lastHeightValidatorsChanged)
                : "0",
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            lastHeightConsensusParamsChanged: isSet(object.lastHeightConsensusParamsChanged)
                ? String(object.lastHeightConsensusParamsChanged)
                : "0",
            lastResultsHash: isSet(object.lastResultsHash) ? bytesFromBase64(object.lastResultsHash) : new Uint8Array(),
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.version !== undefined && (obj.version = message.version ? types_5.Version.toJSON(message.version) : undefined);
        message.chainId !== undefined && (obj.chainId = message.chainId);
        message.initialHeight !== undefined && (obj.initialHeight = message.initialHeight);
        message.lastBlockHeight !== undefined && (obj.lastBlockHeight = message.lastBlockHeight);
        message.lastBlockId !== undefined &&
            (obj.lastBlockId = message.lastBlockId ? types_4.BlockID.toJSON(message.lastBlockId) : undefined);
        message.lastBlockTime !== undefined && (obj.lastBlockTime = message.lastBlockTime.toISOString());
        message.nextValidators !== undefined &&
            (obj.nextValidators = message.nextValidators ? validator_1.ValidatorSet.toJSON(message.nextValidators) : undefined);
        message.validators !== undefined &&
            (obj.validators = message.validators ? validator_1.ValidatorSet.toJSON(message.validators) : undefined);
        message.lastValidators !== undefined &&
            (obj.lastValidators = message.lastValidators ? validator_1.ValidatorSet.toJSON(message.lastValidators) : undefined);
        message.lastHeightValidatorsChanged !== undefined &&
            (obj.lastHeightValidatorsChanged = message.lastHeightValidatorsChanged);
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        message.lastHeightConsensusParamsChanged !== undefined &&
            (obj.lastHeightConsensusParamsChanged = message.lastHeightConsensusParamsChanged);
        message.lastResultsHash !== undefined &&
            (obj.lastResultsHash = base64FromBytes(message.lastResultsHash !== undefined ? message.lastResultsHash : new Uint8Array()));
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.State.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseState();
        message.version = (object.version !== undefined && object.version !== null)
            ? types_5.Version.fromPartial(object.version)
            : undefined;
        message.chainId = (_a = object.chainId) !== null && _a !== void 0 ? _a : "";
        message.initialHeight = (_b = object.initialHeight) !== null && _b !== void 0 ? _b : "0";
        message.lastBlockHeight = (_c = object.lastBlockHeight) !== null && _c !== void 0 ? _c : "0";
        message.lastBlockId = (object.lastBlockId !== undefined && object.lastBlockId !== null)
            ? types_4.BlockID.fromPartial(object.lastBlockId)
            : undefined;
        message.lastBlockTime = (_d = object.lastBlockTime) !== null && _d !== void 0 ? _d : undefined;
        message.nextValidators = (object.nextValidators !== undefined && object.nextValidators !== null)
            ? validator_1.ValidatorSet.fromPartial(object.nextValidators)
            : undefined;
        message.validators = (object.validators !== undefined && object.validators !== null)
            ? validator_1.ValidatorSet.fromPartial(object.validators)
            : undefined;
        message.lastValidators = (object.lastValidators !== undefined && object.lastValidators !== null)
            ? validator_1.ValidatorSet.fromPartial(object.lastValidators)
            : undefined;
        message.lastHeightValidatorsChanged = (_e = object.lastHeightValidatorsChanged) !== null && _e !== void 0 ? _e : "0";
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.lastHeightConsensusParamsChanged = (_f = object.lastHeightConsensusParamsChanged) !== null && _f !== void 0 ? _f : "0";
        message.lastResultsHash = (_g = object.lastResultsHash) !== null && _g !== void 0 ? _g : new Uint8Array();
        message.appHash = (_h = object.appHash) !== null && _h !== void 0 ? _h : new Uint8Array();
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function toTimestamp(date) {
    var seconds = Math.trunc(date.getTime() / 1000).toString();
    var nanos = (date.getTime() % 1000) * 1000000;
    return { seconds: seconds, nanos: nanos };
}
function fromTimestamp(t) {
    var millis = Number(t.seconds) * 1000;
    millis += t.nanos / 1000000;
    return new Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
