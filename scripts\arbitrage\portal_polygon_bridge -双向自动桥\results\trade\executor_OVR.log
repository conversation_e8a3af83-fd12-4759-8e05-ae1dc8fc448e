2025-05-22 12:40:42,969 - INFO - ================================================================================
2025-05-22 12:40:42,969 - INFO - 开始执行 OVR 买入交易 - 时间: 2025-05-22 12:40:42
2025-05-22 12:40:42,969 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-22 12:40:42,969 - INFO - 代币地址: ******************************************
2025-05-22 12:40:42,969 - INFO - 收到OVR买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-22 12:40:42,969 - INFO - OVR: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 12:40:42,969 - INFO - OVR: 准备使用KyberSwap在ethereum上执行300.0USDT买入OVR交易
2025-05-22 12:40:42,969 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 12:40:42,978 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 12:40:42,978 - INFO - OVR: 准备调用swap_tokens函数，参数：
2025-05-22 12:40:42,978 - INFO -   chain: ethereum
2025-05-22 12:40:42,978 - INFO -   token_in: USDT
2025-05-22 12:40:42,979 - INFO -   token_out: ******************************************
2025-05-22 12:40:42,979 - INFO -   amount: 300.0
2025-05-22 12:40:42,979 - INFO -   slippage: 0.5%
2025-05-22 12:40:42,979 - INFO -   real: True
2025-05-22 12:40:44,525 - INFO - OVR: swap_tokens返回值类型: <class 'dict'>
2025-05-22 12:40:44,525 - INFO - OVR: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 12:40:44,525 - ERROR - OVR: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT
2025-05-22 12:40:44,526 - INFO - 读取到 166 条现有交易记录
2025-05-22 12:40:44,526 - INFO - 添加新交易记录: OVR (OVR_300.0_2025-05-22 12:40:42)
2025-05-22 12:40:44,528 - INFO - 成功保存 167 条交易记录
2025-05-22 12:40:44,529 - INFO - OVR: 买入交易处理完成，耗时: 1.56秒
2025-05-22 12:40:44,529 - INFO - ================================================================================
2025-05-22 13:14:22,577 - INFO - ================================================================================
2025-05-22 13:14:22,577 - INFO - 开始执行 OVR 买入交易 - 时间: 2025-05-22 13:14:22
2025-05-22 13:14:22,577 - INFO - 链: ethereum, 投入金额: 266.67 USDT
2025-05-22 13:14:22,577 - INFO - 代币地址: ******************************************
2025-05-22 13:14:22,577 - INFO - 收到OVR买入请求 - 链:ethereum, 投入:266.67USDT
2025-05-22 13:14:22,577 - INFO - OVR: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 13:14:22,577 - INFO - OVR: 准备使用KyberSwap在ethereum上执行266.67USDT买入OVR交易
2025-05-22 13:14:22,577 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 13:14:22,587 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 13:14:22,587 - INFO - OVR: 准备调用swap_tokens函数，参数：
2025-05-22 13:14:22,587 - INFO -   chain: ethereum
2025-05-22 13:14:22,587 - INFO -   token_in: USDT
2025-05-22 13:14:22,587 - INFO -   token_out: ******************************************
2025-05-22 13:14:22,587 - INFO -   amount: 266.67
2025-05-22 13:14:22,587 - INFO -   slippage: 0.5%
2025-05-22 13:14:22,587 - INFO -   real: True
2025-05-22 13:14:24,790 - INFO - OVR: swap_tokens返回值类型: <class 'dict'>
2025-05-22 13:14:24,790 - INFO - OVR: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 266.67 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 266.67, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '266670000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 13:14:24,790 - ERROR - OVR: 交易失败 - 代币余额不足。请求: 266.67 USDT，可用: 0.21782 USDT
2025-05-22 13:14:24,791 - INFO - 读取到 168 条现有交易记录
2025-05-22 13:14:24,791 - INFO - 添加新交易记录: OVR (OVR_266.67_2025-05-22 13:14:22)
2025-05-22 13:14:24,794 - INFO - 成功保存 169 条交易记录
2025-05-22 13:14:24,794 - INFO - OVR: 买入交易处理完成，耗时: 2.22秒
2025-05-22 13:14:24,794 - INFO - ================================================================================
2025-05-22 13:47:38,469 - INFO - ================================================================================
2025-05-22 13:47:38,469 - INFO - 开始执行 OVR 买入交易 - 时间: 2025-05-22 13:47:38
2025-05-22 13:47:38,469 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-22 13:47:38,469 - INFO - 代币地址: ******************************************
2025-05-22 13:47:38,469 - INFO - 收到OVR买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-22 13:47:38,469 - INFO - OVR: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 13:47:38,469 - INFO - OVR: 准备使用KyberSwap在ethereum上执行300.0USDT买入OVR交易
2025-05-22 13:47:38,469 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 13:47:38,469 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 13:47:38,469 - INFO - OVR: 准备调用swap_tokens函数，参数：
2025-05-22 13:47:38,469 - INFO -   chain: ethereum
2025-05-22 13:47:38,469 - INFO -   token_in: USDT
2025-05-22 13:47:38,469 - INFO -   token_out: ******************************************
2025-05-22 13:47:38,469 - INFO -   amount: 300.0
2025-05-22 13:47:38,469 - INFO -   slippage: 0.5%
2025-05-22 13:47:38,469 - INFO -   real: True
2025-05-22 13:47:40,623 - INFO - OVR: swap_tokens返回值类型: <class 'dict'>
2025-05-22 13:47:40,623 - INFO - OVR: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 13:47:40,623 - ERROR - OVR: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT
2025-05-22 13:47:40,625 - INFO - 读取到 172 条现有交易记录
2025-05-22 13:47:40,625 - INFO - 添加新交易记录: OVR (OVR_300.0_2025-05-22 13:47:38)
2025-05-22 13:47:40,628 - INFO - 成功保存 173 条交易记录
2025-05-22 13:47:40,628 - INFO - OVR: 买入交易处理完成，耗时: 2.16秒
2025-05-22 13:47:40,628 - INFO - ================================================================================
