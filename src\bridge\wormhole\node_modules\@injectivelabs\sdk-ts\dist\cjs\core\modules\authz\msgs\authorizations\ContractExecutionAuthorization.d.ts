import { Coin } from '@injectivelabs/ts-types';
import { GoogleProtobufAny, CosmwasmWasmV1Authz } from '@injectivelabs/core-proto-ts';
import { BaseAuthorization } from './Base.js';
export declare namespace ContractExecutionAuthorization {
    interface Params {
        contract: string;
        limit?: {
            maxCalls?: number;
            amounts?: Coin[];
        };
        filter?: {
            acceptedMessagesKeys: string[];
        };
    }
    type Any = GoogleProtobufAny.Any;
    type Proto = CosmwasmWasmV1Authz.ContractExecutionAuthorization;
    type Amino = Object;
}
/**
 * @category Contract Exec Arguments
 */
export default class ContractExecutionAuthorization extends BaseAuthorization<ContractExecutionAuthorization.Params, ContractExecutionAuthorization.Proto, ContractExecutionAuthorization.Amino> {
    static fromJSON(params: ContractExecutionAuthorization.Params): ContractExecutionAuthorization;
    toAny(): GoogleProtobufAny.Any;
    toProto(): ContractExecutionAuthorization.Proto;
    toAmino(): ContractExecutionAuthorization.Amino;
    toWeb3(): ContractExecutionAuthorization.Amino;
}
