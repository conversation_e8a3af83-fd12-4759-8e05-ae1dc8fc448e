import{hmac as c}from"@noble/hashes/hmac";import{sha512 as p}from"@noble/hashes/sha512";import*as i from"@scure/bip39";var d=/^m\/44'\/637'\/[0-9]+'\/[0-9]+'\/[0-9]+'?$/,m=/^m\/44'\/637'\/[0-9]+'\/[0-9]+\/[0-9]+$/,y=(t=>(t.ED25519="ed25519 seed",t))(y||{}),u=2147483648;function D(e){return m.test(e)}function E(e){return d.test(e)}var A=(e,t)=>{let r=c.create(p,e).update(t).digest();return{key:r.slice(0,32),chainCode:r.slice(32)}},f=({key:e,chainCode:t},r)=>{let n=new ArrayBuffer(4);new DataView(n).setUint32(0,r);let o=new Uint8Array(n),s=new Uint8Array([0]),a=new Uint8Array([...s,...e,...o]);return A(t,a)},x=e=>e.replace(/'/g,""),U=e=>e.split("/").slice(1).map(x),h=e=>{let t=e.trim().split(/\s+/).map(r=>r.toLowerCase()).join(" ");return i.mnemonicToSeedSync(t)};export{d as a,m as b,y as c,u as d,D as e,E as f,A as g,f as h,U as i,h as j};
//# sourceMappingURL=chunk-C3Q23D22.mjs.map