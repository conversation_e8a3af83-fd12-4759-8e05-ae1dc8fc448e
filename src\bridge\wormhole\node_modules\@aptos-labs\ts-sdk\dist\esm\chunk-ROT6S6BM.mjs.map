{"version": 3, "sources": ["../../src/core/crypto/privateKey.ts"], "sourcesContent": ["/* eslint-disable max-len */\n\nimport { HexInput, PrivateKeyVariants } from \"../../types\";\nimport { Hex } from \"../hex\";\nimport { PublicKey } from \"./publicKey\";\nimport { Signature } from \"./signature\";\n\n/**\n * Represents a private key used for signing messages and deriving the associated public key.\n * @group Implementation\n * @category Serialization\n */\nexport interface PrivateKey {\n  /**\n   * Sign the given message with the private key to create a signature.\n   * @param message - The message to be signed, provided in HexInput format.\n   * @returns A Signature object representing the signed message.\n   * @group Implementation\n   * @category Serialization\n   */\n  sign(message: HexInput): Signature;\n\n  /**\n   * Derive the public key associated with the private key.\n   * @group Implementation\n   * @category Serialization\n   */\n  publicKey(): PublicKey;\n\n  /**\n   * Get the private key in bytes (Uint8Array).\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array;\n}\n\nexport class PrivateKey {\n  /**\n   * The AIP-80 compliant prefixes for each private key type. Append this to a private key's hex representation\n   * to get an AIP-80 compliant string.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   */\n  public static readonly AIP80_PREFIXES = {\n    [PrivateKeyVariants.Ed25519]: \"ed25519-priv-\",\n    [PrivateKeyVariants.Secp256k1]: \"secp256k1-priv-\",\n  };\n\n  /**\n   * Format a HexInput to an AIP-80 compliant string.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @param privateKey - The HexString or Uint8Array format of the private key.\n   * @param privateKeyType - The private key type\n   */\n  public static formatPrivateKey(privateKey: HexInput, type: PrivateKeyVariants): string {\n    const aip80Prefix = PrivateKey.AIP80_PREFIXES[type];\n\n    // Remove the prefix if it exists\n    let formattedPrivateKey = privateKey;\n    if (typeof formattedPrivateKey === \"string\" && formattedPrivateKey.startsWith(aip80Prefix)) {\n      // eslint-disable-next-line prefer-destructuring\n      formattedPrivateKey = formattedPrivateKey.split(\"-\")[2];\n    }\n\n    return `${aip80Prefix}${Hex.fromHexInput(formattedPrivateKey).toString()}`;\n  }\n\n  /**\n   * Parse a HexInput that may be a HexString, Uint8Array, or a AIP-80 compliant string to a Hex instance.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @param value - A HexString, Uint8Array, or a AIP-80 compliant string.\n   * @param privateKeyType - The private key type\n   * @param strict - If true, the value MUST be compliant with AIP-80.\n   */\n  public static parseHexInput(value: HexInput, type: PrivateKeyVariants, strict?: boolean): Hex {\n    let data: Hex;\n\n    const aip80Prefix = PrivateKey.AIP80_PREFIXES[type];\n    if (typeof value === \"string\") {\n      if (!strict && !value.startsWith(aip80Prefix)) {\n        // HexString input\n        data = Hex.fromHexInput(value);\n        // If the strictness is false, the user has opted into non-AIP-80 compliant private keys.\n        if (strict !== false) {\n          // eslint-disable-next-line no-console\n          console.warn(\n            \"[Aptos SDK] It is recommended that private keys are AIP-80 compliant (https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md). You can fix the private key by formatting it with `PrivateKey.formatPrivateKey(privateKey: string, type: 'ed25519' | 'secp256k1'): string`.\",\n          );\n        }\n      } else if (value.startsWith(aip80Prefix)) {\n        // AIP-80 Compliant String input\n        data = Hex.fromHexString(value.split(\"-\")[2]);\n      } else {\n        if (strict) {\n          // The value does not start with the AIP-80 prefix, and strict is true.\n          throw new Error(\"Invalid HexString input while parsing private key. Must AIP-80 compliant string.\");\n        }\n\n        // This condition should never be reached.\n        throw new Error(\"Invalid HexString input while parsing private key.\");\n      }\n    } else {\n      // The value is an Uint8Array\n      data = Hex.fromHexInput(value);\n    }\n\n    return data;\n  }\n}\n"], "mappings": "yCAqCO,IAAMA,EAAN,MAAMA,CAAW,CAoBtB,OAAc,iBAAiBC,EAAsBC,EAAkC,CACrF,IAAMC,EAAcH,EAAW,eAAeE,CAAI,EAG9CE,EAAsBH,EAC1B,OAAI,OAAOG,GAAwB,UAAYA,EAAoB,WAAWD,CAAW,IAEvFC,EAAsBA,EAAoB,MAAM,GAAG,EAAE,CAAC,GAGjD,GAAGD,CAAW,GAAGE,EAAI,aAAaD,CAAmB,EAAE,SAAS,CAAC,EAC1E,CAWA,OAAc,cAAcE,EAAiBJ,EAA0BK,EAAuB,CAC5F,IAAIC,EAEEL,EAAcH,EAAW,eAAeE,CAAI,EAClD,GAAI,OAAOI,GAAU,SACnB,GAAI,CAACC,GAAU,CAACD,EAAM,WAAWH,CAAW,EAE1CK,EAAOH,EAAI,aAAaC,CAAK,EAEzBC,IAAW,IAEb,QAAQ,KACN,uRACF,UAEOD,EAAM,WAAWH,CAAW,EAErCK,EAAOH,EAAI,cAAcC,EAAM,MAAM,GAAG,EAAE,CAAC,CAAC,MAE5C,OAAIC,EAEI,IAAI,MAAM,kFAAkF,EAI9F,IAAI,MAAM,oDAAoD,OAItEC,EAAOH,EAAI,aAAaC,CAAK,EAG/B,OAAOE,CACT,CACF,EA5EaR,EAOY,eAAiB,CACrC,QAA6B,gBAC7B,UAA+B,iBAClC,EAVK,IAAMS,EAANT", "names": ["_<PERSON><PERSON><PERSON>", "privateKey", "type", "aip80Prefix", "formattedPrivateKey", "Hex", "value", "strict", "data", "Private<PERSON><PERSON>"]}