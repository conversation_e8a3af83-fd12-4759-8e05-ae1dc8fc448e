{"version": 3, "file": "stargateclient.js", "sourceRoot": "", "sources": ["../src/stargateclient.ts"], "names": [], "mappings": ";;;AAAA,yDAAyD;AACzD,yCAAyC;AACzC,+CAAyC;AACzC,uCAAsC;AACtC,2DAA2G;AAC3G,yCAA8C;AAC9C,qEAAgF;AAKhF,yCAAoE;AACpE,qCAAsD;AACtD,uCASmB;AACnB,+CAA4C;AAC5C,qCAA+D;AAE/D,MAAa,YAAa,SAAQ,KAAK;IAGrC,YAAmB,OAAe,EAAE,IAAY;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AAPD,oCAOC;AA2GD,SAAgB,kBAAkB,CAAC,MAAyB;IAC1D,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AACvB,CAAC;AAFD,gDAEC;AAED,SAAgB,kBAAkB,CAAC,MAAyB;IAC1D,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAFD,gDAEC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,MAAyB;IAChE,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE;QAC9B,MAAM,IAAI,KAAK,CACb,8BAA8B,MAAM,CAAC,eAAe,cAAc,MAAM,CAAC,MAAM,WAAW,MAAM,CAAC,IAAI,cAAc,MAAM,CAAC,MAAM,EAAE,CACnI,CAAC;KACH;AACH,CAAC;AAND,4DAMC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,MAAyB;IAChE,IAAI,kBAAkB,CAAC,MAAM,CAAC,EAAE;QAC9B,MAAM,IAAI,KAAK,CACb,eAAe,MAAM,CAAC,eAAe,2BAA2B,MAAM,CAAC,MAAM,WAAW,MAAM,CAAC,IAAI,cAAc,MAAM,CAAC,MAAM,EAAE,CACjI,CAAC;KACH;AACH,CAAC;AAND,4DAMC;AAED;;;;GAIG;AACH,MAAa,gBAAiB,SAAQ,KAAK;IAKzC,YAAmB,IAAY,EAAE,SAAiB,EAAE,GAAuB;QACzE,KAAK,CAAC,6CAA6C,IAAI,gBAAgB,SAAS,WAAW,GAAG,EAAE,CAAC,CAAC;QAClG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;CACF;AAXD,4CAWC;AAWD,MAAa,cAAc;IAQzB;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,QAA+B,EAC/B,UAAiC,EAAE;QAEnC,MAAM,WAAW,GAAG,MAAM,IAAA,6BAAY,EAAC,QAAQ,CAAC,CAAC;QACjD,OAAO,cAAc,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAK,CAAC,MAAM,CACxB,WAAwB,EACxB,UAAiC,EAAE;QAEnC,OAAO,IAAI,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,YAAsB,WAAoC,EAAE,OAA8B;QACxF,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,yBAAW,CAAC,cAAc,CAC3C,WAAW,EACX,4BAAkB,EAClB,4BAAkB,EAClB,+BAAqB,EACrB,0BAAgB,CACjB,CAAC;SACH;QACD,MAAM,EAAE,aAAa,GAAG,yBAAc,EAAE,GAAG,OAAO,CAAC;QACnD,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAES,cAAc;QACtB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAES,mBAAmB;QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;SACrG;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAES,cAAc;QAGtB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAES,mBAAmB;QAK3B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,kFAAkF,CAAC,CAAC;SACrG;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;SACxB;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,SAAS;QACpB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CAAC;QACzD,OAAO,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,aAAqB;QAC3C,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC7E,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;SACrD;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE;gBACxD,OAAO,IAAI,CAAC;aACb;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,OAAe;QACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CACb,YAAY,OAAO,oFAAoF,CACxG,CAAC;SACH;QACD,OAAO;YACL,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,MAAe;QACnC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAChE,OAAO;YACL,EAAE,EAAE,IAAA,gBAAK,EAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;YAC9C,MAAM,EAAE;gBACN,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI,aAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;oBACjE,GAAG,EAAE,IAAI,aAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;iBAC9D;gBACD,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;gBACpC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO;gBACtC,IAAI,EAAE,IAAA,yCAAwB,EAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;aAC3D;YACD,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG;SACxB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,WAAmB;QAC1D,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,cAAc,CAAC,OAAe;QACzC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9D,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC3C,MAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,IAAI,UAAU,GAA2B,SAAS,CAAC;QACnD,GAAG;YACD,MAAM,EAAE,mBAAmB,EAAE,UAAU,EAAE,GACvC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAErF,MAAM,iBAAiB,GAAG,mBAAmB,IAAI,EAAE,CAAC;YACpD,cAAc,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;YAC1C,UAAU,GAAG,UAAU,EAAE,OAAO,CAAC;SAClC,QAAQ,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAE9D,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CACrC,CAAC,aAA0B,EAAE,YAAgC,EAAQ,EAAE;YACrE,gJAAgJ;YAChJ,IAAA,cAAM,EAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC7B,OAAO,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC,IAAA,gBAAQ,EAAC,aAAa,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC;QACvG,CAAC,EACD,IAAI,CACL,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,gBAAwB,EAAE,gBAAwB;QAC3E,IAAI,eAAiC,CAAC;QACtC,IAAI;YACF,eAAe,GAAG,CAChB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CACxF,CAAC,kBAAkB,EAAE,OAAO,CAAC;SAC/B;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAC1C,8CAA8C;aAC/C;iBAAM;gBACL,MAAM,CAAC,CAAC;aACT;SACF;QACD,OAAO,eAAe,IAAI,IAAI,CAAC;IACjC,CAAC;IAEM,KAAK,CAAC,KAAK,CAAC,EAAU;QAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QACvD,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAC5B,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,KAAoB;QACxC,IAAI,QAAgB,CAAC;QACrB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,QAAQ,GAAG,KAAK,CAAC;SAClB;aAAM,IAAI,IAAA,6BAAoB,EAAC,KAAK,CAAC,EAAE;YACtC,QAAQ,GAAG,KAAK;iBACb,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBACT,mFAAmF;gBACnF,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ;oBAAE,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC;;oBAC3D,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;YACpC,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAC;SAClB;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAC;SACzG;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAEM,UAAU;QACf,IAAI,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;IACtD,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,WAAW,CACtB,EAAc,EACd,SAAS,GAAG,KAAM,EAClB,cAAc,GAAG,IAAK;QAEtB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;YACpC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC,EAAE,SAAS,CAAC,CAAC;QAEd,MAAM,SAAS,GAAG,KAAK,EAAE,IAAY,EAA8B,EAAE;YACnE,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,YAAY,CACpB,uBAAuB,IAAI,yGACzB,SAAS,GAAG,IACd,WAAW,EACX,IAAI,CACL,CAAC;aACH;YACD,MAAM,IAAA,aAAK,EAAC,cAAc,CAAC,CAAC;YAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO,MAAM;gBACX,CAAC,CAAC;oBACE,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,eAAe,EAAE,IAAI;oBACrB,YAAY,EAAE,MAAM,CAAC,YAAY;oBACjC,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;iBAC5B;gBACH,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC,CAAC;QAEF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAErD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CACrC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,CAC3B,CAAC,KAAK,EAAE,EAAE;YACR,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CACF,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,eAAe,CAAC,EAAc;QACzC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE7E,IAAI,WAAW,CAAC,IAAI,EAAE;YACpB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,IAAI,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CACrF,CAAC;SACH;QAED,MAAM,aAAa,GAAG,IAAA,gBAAK,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;QAE5D,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,KAAa;QAClC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/E,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAa,EAAE;YACvC,MAAM,SAAS,GAAG,gBAAS,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,UAAU,EAAE,CAAC,CAAC;YACvE,OAAO;gBACL,MAAM,EAAE,EAAE,CAAC,MAAM;gBACjB,OAAO,EAAE,EAAE,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAA,gBAAK,EAAC,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;gBAClC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI;gBACpB,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,4BAAmB,CAAC;gBACjD,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE;gBAC3B,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO;gBAC1B,SAAS,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS;aAC/B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAjUD,wCAiUC"}