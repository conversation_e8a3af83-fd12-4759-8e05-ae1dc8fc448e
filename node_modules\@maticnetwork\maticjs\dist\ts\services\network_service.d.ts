import { BaseBigNumber } from "..";
import { HttpRequest } from "../utils";
export declare class NetworkService {
    httpRequest: HttpRequest;
    constructor(baseUrl: string);
    private createUrlForPos;
    private createUrlForZkEvm;
    getBlockIncluded(version: string, blockNumber: number): Promise<{
        start: string;
        end: string;
        headerBlockNumber: BaseBigNumber;
    }>;
    getExitProof(version: string, burnTxHash: string, eventSignature: string): Promise<any>;
    getProof(version: string, start: any, end: any, blockNumber: any): Promise<any>;
    getMerkleProofForZkEvm(version: string, networkID: number, depositCount: number): Promise<any>;
    getBridgeTransactionDetails(version: string, networkID: number, depositCount: number): Promise<any>;
}
