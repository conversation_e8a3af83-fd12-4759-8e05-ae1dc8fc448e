{"version": 3, "file": "thread-chunk3.mjs", "sources": ["../../src/commands/check/thread.ts"], "sourcesContent": ["import ts from 'typescript';\nimport * as path from 'node:path';\n\nimport type { GraphQLSPConfig } from '@gql.tada/internal';\nimport { loadRef } from '@gql.tada/internal';\nimport { getGraphQLDiagnostics } from '@0no-co/graphqlsp/api';\n\nimport { programFactory } from '../../ts';\nimport { expose } from '../../threads';\n\nimport type { Severity, DiagnosticMessage, DiagnosticSignal } from './types';\n\nexport interface DiagnosticsParams {\n  rootPath: string;\n  configPath: string;\n  pluginConfig: GraphQLSPConfig;\n}\n\nasync function* _runDiagnostics(\n  params: DiagnosticsParams\n): AsyncIterableIterator<DiagnosticSignal> {\n  const projectPath = path.dirname(params.configPath);\n  const factory = programFactory(params);\n\n  const externalFiles = factory.createExternalFiles();\n  if (externalFiles.length) {\n    yield { kind: 'EXTERNAL_WARNING' };\n    await factory.addVirtualFiles(externalFiles);\n  }\n\n  const schemaRef = await loadRef(params.pluginConfig).load({ rootPath: projectPath });\n\n  const container = factory.build();\n  const pluginInfo = container.buildPluginInfo(params.pluginConfig);\n  const sourceFiles = container.getSourceFiles();\n\n  yield {\n    kind: 'FILE_COUNT',\n    fileCount: sourceFiles.length,\n  };\n\n  for (const sourceFile of sourceFiles) {\n    const isVueOrSvelte =\n      sourceFile.fileName.endsWith('.vue.ts') || sourceFile.fileName.endsWith('.svelte.ts');\n    let filePath = sourceFile.fileName;\n    pluginInfo.config = {\n      ...pluginInfo.config,\n      shouldCheckForColocatedFragments: isVueOrSvelte\n        ? false\n        : (pluginInfo.config.shouldCheckForColocatedFragments ?? false),\n      trackFieldUsage: isVueOrSvelte ? false : (pluginInfo.config.trackFieldUsage ?? false),\n    };\n    const diagnostics = getGraphQLDiagnostics(filePath, schemaRef, pluginInfo);\n    const messages: DiagnosticMessage[] = [];\n\n    if (diagnostics && diagnostics.length) {\n      for (const diagnostic of diagnostics) {\n        if (\n          !('messageText' in diagnostic) ||\n          typeof diagnostic.messageText !== 'string' ||\n          !diagnostic.file\n        ) {\n          continue;\n        }\n        let severity: Severity = 'info';\n        if (diagnostic.category === ts.DiagnosticCategory.Error) {\n          severity = 'error';\n        } else if (diagnostic.category === ts.DiagnosticCategory.Warning) {\n          severity = 'warn';\n        }\n        const span = { start: diagnostic.start || 1, length: diagnostic.length || 1 };\n        const position = container.getSourcePosition(sourceFile, span);\n        filePath = position.fileName;\n        messages.push({\n          severity,\n          message: diagnostic.messageText,\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n          endLine: position.endLine,\n          endColumn: position.endColumn,\n        });\n      }\n    }\n\n    yield {\n      kind: 'FILE_DIAGNOSTICS',\n      filePath,\n      messages,\n    };\n  }\n}\n\nexport const runDiagnostics = expose(_runDiagnostics);\n"], "names": ["runDiagnostics", "expose", "async", "_runDiagnostics", "params", "projectPath", "path", "dirname", "config<PERSON><PERSON>", "factory", "programFactory", "externalFiles", "createExternalFiles", "length", "kind", "addVirtualFiles", "schemaRef", "loadRef", "pluginConfig", "load", "rootPath", "container", "build", "pluginInfo", "buildPluginInfo", "sourceFiles", "getSourceFiles", "fileCount", "sourceFile", "isVueOrSvelte", "fileName", "endsWith", "filePath", "config", "shouldCheckForColocatedFragments", "trackFieldUsage", "diagnostics", "getGraphQLDiagnostics", "messages", "diagnostic", "messageText", "file", "severity", "category", "ts", "DiagnosticCategory", "Error", "Warning", "position", "getSourcePosition", "start", "push", "message", "line", "col", "endLine", "endColumn"], "mappings": ";;;;;;;;;;;;IA6FaA,IAAiBC,GA3E9BC,gBAAgBC,gBACdC;EAEA,IAAMC,IAAcC,EAAKC,QAAQH,EAAOI;EACxC,IAAMC,IAAUC,EAAeN;EAE/B,IAAMO,IAAgBF,EAAQG;EAC9B,IAAID,EAAcE,QAAQ;UAClB;MAAEC,MAAM;;UACRL,EAAQM,gBAAgBJ;AAChC;EAEA,IAAMK,UAAkBC,EAAQb,EAAOc,cAAcC,KAAK;IAAEC,UAAUf;;EAEtE,IAAMgB,IAAYZ,EAAQa;EAC1B,IAAMC,IAAaF,EAAUG,gBAAgBpB,EAAOc;EACpD,IAAMO,IAAcJ,EAAUK;QAExB;IACJZ,MAAM;IACNa,WAAWF,EAAYZ;;EAGzB,KAAK,IAAMe,KAAcH,GAAa;IACpC,IAAMI,IACJD,EAAWE,SAASC,SAAS,cAAcH,EAAWE,SAASC,SAAS;IAC1E,IAAIC,IAAWJ,EAAWE;IAC1BP,EAAWU,SAAS;SACfV,EAAWU;MACdC,kCAAkCL,KAC9B,IACCN,EAAWU,OAAOC,qCAAoC;MAC3DC,iBAAiBN,KAAgB,IAASN,EAAWU,OAAOE,oBAAmB;;IAEjF,IAAMC,IAAcC,EAAsBL,GAAUhB,GAAWO;IAC/D,IAAMe,IAAgC;IAEtC,IAAIF,KAAeA,EAAYvB;MAC7B,KAAK,IAAM0B,KAAcH,GAAa;QACpC,MACI,iBAAiBG,MACe,mBAA3BA,EAAWC,gBACjBD,EAAWE;UAEZ;;QAEF,IAAIC,IAAqB;QACzB,IAAIH,EAAWI,aAAaC,EAAGC,mBAAmBC;UAChDJ,IAAW;eACN,IAAIH,EAAWI,aAAaC,EAAGC,mBAAmBE;UACvDL,IAAW;;QAGb,IAAMM,IAAW3B,EAAU4B,kBAAkBrB,GADhC;UAAEsB,OAAOX,EAAWW,SAAS;UAAGrC,QAAQ0B,EAAW1B,UAAU;;QAE1EmB,IAAWgB,EAASlB;QACpBQ,EAASa,KAAK;UACZT;UACAU,SAASb,EAAWC;UACpBC,MAAMO,EAASlB;UACfuB,MAAML,EAASK;UACfC,KAAKN,EAASM;UACdC,SAASP,EAASO;UAClBC,WAAWR,EAASQ;;AAExB;;UAGI;MACJ1C,MAAM;MACNkB;MACAM;;AAEJ;AACF;;"}