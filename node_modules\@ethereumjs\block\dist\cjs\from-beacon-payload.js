"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.executionPayloadFromBeaconPayload = void 0;
const util_1 = require("@ethereumjs/util");
function parseExecutionWitnessFromSnakeJson({ state_diff, verkle_proof, }) {
    return {
        stateDiff: state_diff.map(({ stem, suffix_diffs }) => ({
            stem,
            suffixDiffs: suffix_diffs.map(({ current_value, new_value, suffix }) => ({
                currentValue: current_value,
                newValue: new_value,
                suffix,
            })),
        })),
        verkleProof: {
            commitmentsByPath: verkle_proof.commitments_by_path,
            d: verkle_proof.d,
            depthExtensionPresent: verkle_proof.depth_extension_present,
            ipaProof: {
                cl: verkle_proof.ipa_proof.cl,
                cr: verkle_proof.ipa_proof.cr,
                finalEvaluation: verkle_proof.ipa_proof.final_evaluation,
            },
            otherStems: verkle_proof.other_stems,
        },
    };
}
/**
 * Converts a beacon block execution payload JSON object {@link BeaconPayloadJson} to the {@link ExecutionPayload} data needed to construct a {@link Block}.
 * The JSON data can be retrieved from a consensus layer (CL) client on this Beacon API `/eth/v2/beacon/blocks/[block number]`
 */
function executionPayloadFromBeaconPayload(payload) {
    const executionPayload = {
        parentHash: payload.parent_hash,
        feeRecipient: payload.fee_recipient,
        stateRoot: payload.state_root,
        receiptsRoot: payload.receipts_root,
        logsBloom: payload.logs_bloom,
        prevRandao: payload.prev_randao,
        blockNumber: (0, util_1.bigIntToHex)(BigInt(payload.block_number)),
        gasLimit: (0, util_1.bigIntToHex)(BigInt(payload.gas_limit)),
        gasUsed: (0, util_1.bigIntToHex)(BigInt(payload.gas_used)),
        timestamp: (0, util_1.bigIntToHex)(BigInt(payload.timestamp)),
        extraData: payload.extra_data,
        baseFeePerGas: (0, util_1.bigIntToHex)(BigInt(payload.base_fee_per_gas)),
        blockHash: payload.block_hash,
        transactions: payload.transactions,
    };
    if (payload.withdrawals !== undefined && payload.withdrawals !== null) {
        executionPayload.withdrawals = payload.withdrawals.map((wd) => ({
            index: (0, util_1.bigIntToHex)(BigInt(wd.index)),
            validatorIndex: (0, util_1.bigIntToHex)(BigInt(wd.validator_index)),
            address: wd.address,
            amount: (0, util_1.bigIntToHex)(BigInt(wd.amount)),
        }));
    }
    if (payload.blob_gas_used !== undefined && payload.blob_gas_used !== null) {
        executionPayload.blobGasUsed = (0, util_1.bigIntToHex)(BigInt(payload.blob_gas_used));
    }
    if (payload.excess_blob_gas !== undefined && payload.excess_blob_gas !== null) {
        executionPayload.excessBlobGas = (0, util_1.bigIntToHex)(BigInt(payload.excess_blob_gas));
    }
    if (payload.parent_beacon_block_root !== undefined && payload.parent_beacon_block_root !== null) {
        executionPayload.parentBeaconBlockRoot = payload.parent_beacon_block_root;
    }
    // requests
    if (payload.deposit_requests !== undefined && payload.deposit_requests !== null) {
        executionPayload.depositRequests = payload.deposit_requests.map((breq) => ({
            pubkey: breq.pubkey,
            withdrawalCredentials: breq.withdrawal_credentials,
            amount: breq.amount,
            signature: breq.signature,
            index: breq.index,
        }));
    }
    if (payload.withdrawal_requests !== undefined && payload.withdrawal_requests !== null) {
        executionPayload.withdrawalRequests = payload.withdrawal_requests.map((breq) => ({
            sourceAddress: breq.source_address,
            validatorPubkey: breq.validator_pubkey,
            amount: breq.amount,
        }));
    }
    if (payload.consolidation_requests !== undefined && payload.consolidation_requests !== null) {
        executionPayload.consolidationRequests = payload.consolidation_requests.map((breq) => ({
            sourceAddress: breq.source_address,
            sourcePubkey: breq.source_pubkey,
            targetPubkey: breq.target_pubkey,
        }));
    }
    if (payload.execution_witness !== undefined && payload.execution_witness !== null) {
        // the casing structure in payload could be camel case or snake depending upon the CL
        executionPayload.executionWitness =
            payload.execution_witness.verkleProof !== undefined
                ? payload.execution_witness
                : parseExecutionWitnessFromSnakeJson(payload.execution_witness);
    }
    return executionPayload;
}
exports.executionPayloadFromBeaconPayload = executionPayloadFromBeaconPayload;
//# sourceMappingURL=from-beacon-payload.js.map