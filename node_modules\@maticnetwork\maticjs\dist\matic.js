/*! For license information please see matic.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("matic",[],e):"object"==typeof exports?exports.matic=e():t.matic=e()}(self,(()=>(()=>{var t={526:(t,e)=>{"use strict";e.byteLength=function(t){var e=a(t),r=e[0],n=e[1];return 3*(r+n)/4-n},e.toByteArray=function(t){var e,r,o=a(t),s=o[0],h=o[1],c=new i(function(t,e,r){return 3*(e+r)/4-r}(0,s,h)),u=0,l=h>0?s-4:s;for(r=0;r<l;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],c[u++]=e>>16&255,c[u++]=e>>8&255,c[u++]=255&e;return 2===h&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,c[u++]=255&e),1===h&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,c[u++]=e>>8&255,c[u++]=255&e),c},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=16383,a=0,c=n-i;a<c;a+=s)o.push(h(t,a,a+s>c?c:a+s));return 1===i?(e=t[n-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0;s<64;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function a(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function h(t,e,n){for(var i,o,s=[],a=e;a<n;a+=3)i=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),s.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return s.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},404:function(t,e,r){!function(t,e){"use strict";function n(t,e){if(!t)throw new Error(e||"Assertion failed")}function i(t,e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,e,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==e&&"be"!==e||(r=e,e=10),this._init(t||0,e||10,r||"be"))}var s;"object"==typeof t?t.exports=o:e.BN=o,o.BN=o,o.wordSize=26;try{s="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(790).Buffer}catch(t){}function a(t,e){var r=t.charCodeAt(e);return r>=48&&r<=57?r-48:r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:void n(!1,"Invalid character in "+t)}function h(t,e,r){var n=a(t,r);return r-1>=e&&(n|=a(t,r-1)<<4),n}function c(t,e,r,i){for(var o=0,s=0,a=Math.min(t.length,r),h=e;h<a;h++){var c=t.charCodeAt(h)-48;o*=i,s=c>=49?c-49+10:c>=17?c-17+10:c,n(c>=0&&s<i,"Invalid character"),o+=s}return o}function u(t,e){t.words=e.words,t.length=e.length,t.negative=e.negative,t.red=e.red}if(o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,e){return t.cmp(e)>0?t:e},o.min=function(t,e){return t.cmp(e)<0?t:e},o.prototype._init=function(t,e,r){if("number"==typeof t)return this._initNumber(t,e,r);if("object"==typeof t)return this._initArray(t,e,r);"hex"===e&&(e=16),n(e===(0|e)&&e>=2&&e<=36);var i=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(i++,this.negative=1),i<t.length&&(16===e?this._parseHex(t,i,r):(this._parseBase(t,e,i),"le"===r&&this._initArray(this.toArray(),e,r)))},o.prototype._initNumber=function(t,e,r){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(n(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),e,r)},o.prototype._initArray=function(t,e,r){if(n("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var i=0;i<this.length;i++)this.words[i]=0;var o,s,a=0;if("be"===r)for(i=t.length-1,o=0;i>=0;i-=3)s=t[i]|t[i-1]<<8|t[i-2]<<16,this.words[o]|=s<<a&67108863,this.words[o+1]=s>>>26-a&67108863,(a+=24)>=26&&(a-=26,o++);else if("le"===r)for(i=0,o=0;i<t.length;i+=3)s=t[i]|t[i+1]<<8|t[i+2]<<16,this.words[o]|=s<<a&67108863,this.words[o+1]=s>>>26-a&67108863,(a+=24)>=26&&(a-=26,o++);return this._strip()},o.prototype._parseHex=function(t,e,r){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var i,o=0,s=0;if("be"===r)for(n=t.length-1;n>=e;n-=2)i=h(t,e,n)<<o,this.words[s]|=67108863&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;else for(n=(t.length-e)%2==0?e+1:e;n<t.length;n+=2)i=h(t,e,n)<<o,this.words[s]|=67108863&i,o>=18?(o-=18,s+=1,this.words[s]|=i>>>26):o+=8;this._strip()},o.prototype._parseBase=function(t,e,r){this.words=[0],this.length=1;for(var n=0,i=1;i<=67108863;i*=e)n++;n--,i=i/e|0;for(var o=t.length-r,s=o%n,a=Math.min(o,o-s)+r,h=0,u=r;u<a;u+=n)h=c(t,u,u+n,e),this.imuln(i),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h);if(0!==s){var l=1;for(h=c(t,u,t.length,e),u=0;u<s;u++)l*=e;this.imuln(l),this.words[0]+h<67108864?this.words[0]+=h:this._iaddn(h)}this._strip()},o.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype._move=function(t){u(t,this)},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype._strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},"undefined"!=typeof Symbol&&"function"==typeof Symbol.for)try{o.prototype[Symbol.for("nodejs.util.inspect.custom")]=l}catch(t){o.prototype.inspect=l}else o.prototype.inspect=l;function l(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var f=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],p=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function m(t,e,r){r.negative=e.negative^t.negative;var n=t.length+e.length|0;r.length=n,n=n-1|0;var i=0|t.words[0],o=0|e.words[0],s=i*o,a=67108863&s,h=s/67108864|0;r.words[0]=a;for(var c=1;c<n;c++){for(var u=h>>>26,l=67108863&h,f=Math.min(c,e.length-1),d=Math.max(0,c-t.length+1);d<=f;d++){var p=c-d|0;u+=(s=(i=0|t.words[p])*(o=0|e.words[d])+l)/67108864|0,l=67108863&s}r.words[c]=0|l,h=0|u}return 0!==h?r.words[c]=0|h:r.length--,r._strip()}o.prototype.toString=function(t,e){var r;if(e=0|e||1,16===(t=t||10)||"hex"===t){r="";for(var i=0,o=0,s=0;s<this.length;s++){var a=this.words[s],h=(16777215&(a<<i|o)).toString(16);o=a>>>24-i&16777215,(i+=2)>=26&&(i-=26,s--),r=0!==o||s!==this.length-1?f[6-h.length]+h+r:h+r}for(0!==o&&(r=o.toString(16)+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var c=d[t],u=p[t];r="";var l=this.clone();for(l.negative=0;!l.isZero();){var m=l.modrn(u).toString(t);r=(l=l.idivn(u)).isZero()?m+r:f[c-m.length]+m+r}for(this.isZero()&&(r="0"+r);r.length%e!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}n(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16,2)},s&&(o.prototype.toBuffer=function(t,e){return this.toArrayLike(s,t,e)}),o.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},o.prototype.toArrayLike=function(t,e,r){this._strip();var i=this.byteLength(),o=r||Math.max(1,i);n(i<=o,"byte array longer than desired length"),n(o>0,"Requested array length <= 0");var s=function(t,e){return t.allocUnsafe?t.allocUnsafe(e):new t(e)}(t,o);return this["_toArrayLike"+("le"===e?"LE":"BE")](s,i),s},o.prototype._toArrayLikeLE=function(t,e){for(var r=0,n=0,i=0,o=0;i<this.length;i++){var s=this.words[i]<<o|n;t[r++]=255&s,r<t.length&&(t[r++]=s>>8&255),r<t.length&&(t[r++]=s>>16&255),6===o?(r<t.length&&(t[r++]=s>>24&255),n=0,o=0):(n=s>>>24,o+=2)}if(r<t.length)for(t[r++]=n;r<t.length;)t[r++]=0},o.prototype._toArrayLikeBE=function(t,e){for(var r=t.length-1,n=0,i=0,o=0;i<this.length;i++){var s=this.words[i]<<o|n;t[r--]=255&s,r>=0&&(t[r--]=s>>8&255),r>=0&&(t[r--]=s>>16&255),6===o?(r>=0&&(t[r--]=s>>24&255),n=0,o=0):(n=s>>>24,o+=2)}if(r>=0)for(t[r--]=n;r>=0;)t[r--]=0},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var e=t,r=0;return e>=4096&&(r+=13,e>>>=13),e>=64&&(r+=7,e>>>=7),e>=8&&(r+=4,e>>>=4),e>=2&&(r+=2,e>>>=2),r+e},o.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,r=0;return 8191&e||(r+=13,e>>>=13),127&e||(r+=7,e>>>=7),15&e||(r+=4,e>>>=4),3&e||(r+=2,e>>>=2),1&e||r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var r=this._zeroBits(this.words[e]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this._strip()},o.prototype.ior=function(t){return n(!(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var r=0;r<e.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=e.length,this._strip()},o.prototype.iand=function(t){return n(!(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var e,r;this.length>t.length?(e=this,r=t):(e=t,r=this);for(var n=0;n<r.length;n++)this.words[n]=e.words[n]^r.words[n];if(this!==e)for(;n<e.length;n++)this.words[n]=e.words[n];return this.length=e.length,this._strip()},o.prototype.ixor=function(t){return n(!(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){n("number"==typeof t&&t>=0);var e=0|Math.ceil(t/26),r=t%26;this._expand(e),r>0&&e--;for(var i=0;i<e;i++)this.words[i]=67108863&~this.words[i];return r>0&&(this.words[i]=~this.words[i]&67108863>>26-r),this._strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,e){n("number"==typeof t&&t>=0);var r=t/26|0,i=t%26;return this._expand(r+1),this.words[r]=e?this.words[r]|1<<i:this.words[r]&~(1<<i),this._strip()},o.prototype.iadd=function(t){var e,r,n;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(r=this,n=t):(r=t,n=this);for(var i=0,o=0;o<n.length;o++)e=(0|r.words[o])+(0|n.words[o])+i,this.words[o]=67108863&e,i=e>>>26;for(;0!==i&&o<r.length;o++)e=(0|r.words[o])+i,this.words[o]=67108863&e,i=e>>>26;if(this.length=r.length,0!==i)this.words[this.length]=i,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,n,i=this.cmp(t);if(0===i)return this.negative=0,this.length=1,this.words[0]=0,this;i>0?(r=this,n=t):(r=t,n=this);for(var o=0,s=0;s<n.length;s++)o=(e=(0|r.words[s])-(0|n.words[s])+o)>>26,this.words[s]=67108863&e;for(;0!==o&&s<r.length;s++)o=(e=(0|r.words[s])+o)>>26,this.words[s]=67108863&e;if(0===o&&s<r.length&&r!==this)for(;s<r.length;s++)this.words[s]=r.words[s];return this.length=Math.max(this.length,s),r!==this&&(this.negative=1),this._strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var g=function(t,e,r){var n,i,o,s=t.words,a=e.words,h=r.words,c=0,u=0|s[0],l=8191&u,f=u>>>13,d=0|s[1],p=8191&d,m=d>>>13,g=0|s[2],y=8191&g,b=g>>>13,w=0|s[3],v=8191&w,E=w>>>13,k=0|s[4],x=8191&k,_=k>>>13,P=0|s[5],B=8191&P,A=P>>>13,S=0|s[6],I=8191&S,C=S>>>13,M=0|s[7],R=8191&M,T=M>>>13,O=0|s[8],L=8191&O,F=O>>>13,N=0|s[9],D=8191&N,H=N>>>13,U=0|a[0],G=8191&U,z=U>>>13,j=0|a[1],q=8191&j,$=j>>>13,W=0|a[2],V=8191&W,K=W>>>13,Z=0|a[3],Y=8191&Z,J=Z>>>13,X=0|a[4],Q=8191&X,tt=X>>>13,et=0|a[5],rt=8191&et,nt=et>>>13,it=0|a[6],ot=8191&it,st=it>>>13,at=0|a[7],ht=8191&at,ct=at>>>13,ut=0|a[8],lt=8191&ut,ft=ut>>>13,dt=0|a[9],pt=8191&dt,mt=dt>>>13;r.negative=t.negative^e.negative,r.length=19;var gt=(c+(n=Math.imul(l,G))|0)+((8191&(i=(i=Math.imul(l,z))+Math.imul(f,G)|0))<<13)|0;c=((o=Math.imul(f,z))+(i>>>13)|0)+(gt>>>26)|0,gt&=67108863,n=Math.imul(p,G),i=(i=Math.imul(p,z))+Math.imul(m,G)|0,o=Math.imul(m,z);var yt=(c+(n=n+Math.imul(l,q)|0)|0)+((8191&(i=(i=i+Math.imul(l,$)|0)+Math.imul(f,q)|0))<<13)|0;c=((o=o+Math.imul(f,$)|0)+(i>>>13)|0)+(yt>>>26)|0,yt&=67108863,n=Math.imul(y,G),i=(i=Math.imul(y,z))+Math.imul(b,G)|0,o=Math.imul(b,z),n=n+Math.imul(p,q)|0,i=(i=i+Math.imul(p,$)|0)+Math.imul(m,q)|0,o=o+Math.imul(m,$)|0;var bt=(c+(n=n+Math.imul(l,V)|0)|0)+((8191&(i=(i=i+Math.imul(l,K)|0)+Math.imul(f,V)|0))<<13)|0;c=((o=o+Math.imul(f,K)|0)+(i>>>13)|0)+(bt>>>26)|0,bt&=67108863,n=Math.imul(v,G),i=(i=Math.imul(v,z))+Math.imul(E,G)|0,o=Math.imul(E,z),n=n+Math.imul(y,q)|0,i=(i=i+Math.imul(y,$)|0)+Math.imul(b,q)|0,o=o+Math.imul(b,$)|0,n=n+Math.imul(p,V)|0,i=(i=i+Math.imul(p,K)|0)+Math.imul(m,V)|0,o=o+Math.imul(m,K)|0;var wt=(c+(n=n+Math.imul(l,Y)|0)|0)+((8191&(i=(i=i+Math.imul(l,J)|0)+Math.imul(f,Y)|0))<<13)|0;c=((o=o+Math.imul(f,J)|0)+(i>>>13)|0)+(wt>>>26)|0,wt&=67108863,n=Math.imul(x,G),i=(i=Math.imul(x,z))+Math.imul(_,G)|0,o=Math.imul(_,z),n=n+Math.imul(v,q)|0,i=(i=i+Math.imul(v,$)|0)+Math.imul(E,q)|0,o=o+Math.imul(E,$)|0,n=n+Math.imul(y,V)|0,i=(i=i+Math.imul(y,K)|0)+Math.imul(b,V)|0,o=o+Math.imul(b,K)|0,n=n+Math.imul(p,Y)|0,i=(i=i+Math.imul(p,J)|0)+Math.imul(m,Y)|0,o=o+Math.imul(m,J)|0;var vt=(c+(n=n+Math.imul(l,Q)|0)|0)+((8191&(i=(i=i+Math.imul(l,tt)|0)+Math.imul(f,Q)|0))<<13)|0;c=((o=o+Math.imul(f,tt)|0)+(i>>>13)|0)+(vt>>>26)|0,vt&=67108863,n=Math.imul(B,G),i=(i=Math.imul(B,z))+Math.imul(A,G)|0,o=Math.imul(A,z),n=n+Math.imul(x,q)|0,i=(i=i+Math.imul(x,$)|0)+Math.imul(_,q)|0,o=o+Math.imul(_,$)|0,n=n+Math.imul(v,V)|0,i=(i=i+Math.imul(v,K)|0)+Math.imul(E,V)|0,o=o+Math.imul(E,K)|0,n=n+Math.imul(y,Y)|0,i=(i=i+Math.imul(y,J)|0)+Math.imul(b,Y)|0,o=o+Math.imul(b,J)|0,n=n+Math.imul(p,Q)|0,i=(i=i+Math.imul(p,tt)|0)+Math.imul(m,Q)|0,o=o+Math.imul(m,tt)|0;var Et=(c+(n=n+Math.imul(l,rt)|0)|0)+((8191&(i=(i=i+Math.imul(l,nt)|0)+Math.imul(f,rt)|0))<<13)|0;c=((o=o+Math.imul(f,nt)|0)+(i>>>13)|0)+(Et>>>26)|0,Et&=67108863,n=Math.imul(I,G),i=(i=Math.imul(I,z))+Math.imul(C,G)|0,o=Math.imul(C,z),n=n+Math.imul(B,q)|0,i=(i=i+Math.imul(B,$)|0)+Math.imul(A,q)|0,o=o+Math.imul(A,$)|0,n=n+Math.imul(x,V)|0,i=(i=i+Math.imul(x,K)|0)+Math.imul(_,V)|0,o=o+Math.imul(_,K)|0,n=n+Math.imul(v,Y)|0,i=(i=i+Math.imul(v,J)|0)+Math.imul(E,Y)|0,o=o+Math.imul(E,J)|0,n=n+Math.imul(y,Q)|0,i=(i=i+Math.imul(y,tt)|0)+Math.imul(b,Q)|0,o=o+Math.imul(b,tt)|0,n=n+Math.imul(p,rt)|0,i=(i=i+Math.imul(p,nt)|0)+Math.imul(m,rt)|0,o=o+Math.imul(m,nt)|0;var kt=(c+(n=n+Math.imul(l,ot)|0)|0)+((8191&(i=(i=i+Math.imul(l,st)|0)+Math.imul(f,ot)|0))<<13)|0;c=((o=o+Math.imul(f,st)|0)+(i>>>13)|0)+(kt>>>26)|0,kt&=67108863,n=Math.imul(R,G),i=(i=Math.imul(R,z))+Math.imul(T,G)|0,o=Math.imul(T,z),n=n+Math.imul(I,q)|0,i=(i=i+Math.imul(I,$)|0)+Math.imul(C,q)|0,o=o+Math.imul(C,$)|0,n=n+Math.imul(B,V)|0,i=(i=i+Math.imul(B,K)|0)+Math.imul(A,V)|0,o=o+Math.imul(A,K)|0,n=n+Math.imul(x,Y)|0,i=(i=i+Math.imul(x,J)|0)+Math.imul(_,Y)|0,o=o+Math.imul(_,J)|0,n=n+Math.imul(v,Q)|0,i=(i=i+Math.imul(v,tt)|0)+Math.imul(E,Q)|0,o=o+Math.imul(E,tt)|0,n=n+Math.imul(y,rt)|0,i=(i=i+Math.imul(y,nt)|0)+Math.imul(b,rt)|0,o=o+Math.imul(b,nt)|0,n=n+Math.imul(p,ot)|0,i=(i=i+Math.imul(p,st)|0)+Math.imul(m,ot)|0,o=o+Math.imul(m,st)|0;var xt=(c+(n=n+Math.imul(l,ht)|0)|0)+((8191&(i=(i=i+Math.imul(l,ct)|0)+Math.imul(f,ht)|0))<<13)|0;c=((o=o+Math.imul(f,ct)|0)+(i>>>13)|0)+(xt>>>26)|0,xt&=67108863,n=Math.imul(L,G),i=(i=Math.imul(L,z))+Math.imul(F,G)|0,o=Math.imul(F,z),n=n+Math.imul(R,q)|0,i=(i=i+Math.imul(R,$)|0)+Math.imul(T,q)|0,o=o+Math.imul(T,$)|0,n=n+Math.imul(I,V)|0,i=(i=i+Math.imul(I,K)|0)+Math.imul(C,V)|0,o=o+Math.imul(C,K)|0,n=n+Math.imul(B,Y)|0,i=(i=i+Math.imul(B,J)|0)+Math.imul(A,Y)|0,o=o+Math.imul(A,J)|0,n=n+Math.imul(x,Q)|0,i=(i=i+Math.imul(x,tt)|0)+Math.imul(_,Q)|0,o=o+Math.imul(_,tt)|0,n=n+Math.imul(v,rt)|0,i=(i=i+Math.imul(v,nt)|0)+Math.imul(E,rt)|0,o=o+Math.imul(E,nt)|0,n=n+Math.imul(y,ot)|0,i=(i=i+Math.imul(y,st)|0)+Math.imul(b,ot)|0,o=o+Math.imul(b,st)|0,n=n+Math.imul(p,ht)|0,i=(i=i+Math.imul(p,ct)|0)+Math.imul(m,ht)|0,o=o+Math.imul(m,ct)|0;var _t=(c+(n=n+Math.imul(l,lt)|0)|0)+((8191&(i=(i=i+Math.imul(l,ft)|0)+Math.imul(f,lt)|0))<<13)|0;c=((o=o+Math.imul(f,ft)|0)+(i>>>13)|0)+(_t>>>26)|0,_t&=67108863,n=Math.imul(D,G),i=(i=Math.imul(D,z))+Math.imul(H,G)|0,o=Math.imul(H,z),n=n+Math.imul(L,q)|0,i=(i=i+Math.imul(L,$)|0)+Math.imul(F,q)|0,o=o+Math.imul(F,$)|0,n=n+Math.imul(R,V)|0,i=(i=i+Math.imul(R,K)|0)+Math.imul(T,V)|0,o=o+Math.imul(T,K)|0,n=n+Math.imul(I,Y)|0,i=(i=i+Math.imul(I,J)|0)+Math.imul(C,Y)|0,o=o+Math.imul(C,J)|0,n=n+Math.imul(B,Q)|0,i=(i=i+Math.imul(B,tt)|0)+Math.imul(A,Q)|0,o=o+Math.imul(A,tt)|0,n=n+Math.imul(x,rt)|0,i=(i=i+Math.imul(x,nt)|0)+Math.imul(_,rt)|0,o=o+Math.imul(_,nt)|0,n=n+Math.imul(v,ot)|0,i=(i=i+Math.imul(v,st)|0)+Math.imul(E,ot)|0,o=o+Math.imul(E,st)|0,n=n+Math.imul(y,ht)|0,i=(i=i+Math.imul(y,ct)|0)+Math.imul(b,ht)|0,o=o+Math.imul(b,ct)|0,n=n+Math.imul(p,lt)|0,i=(i=i+Math.imul(p,ft)|0)+Math.imul(m,lt)|0,o=o+Math.imul(m,ft)|0;var Pt=(c+(n=n+Math.imul(l,pt)|0)|0)+((8191&(i=(i=i+Math.imul(l,mt)|0)+Math.imul(f,pt)|0))<<13)|0;c=((o=o+Math.imul(f,mt)|0)+(i>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,n=Math.imul(D,q),i=(i=Math.imul(D,$))+Math.imul(H,q)|0,o=Math.imul(H,$),n=n+Math.imul(L,V)|0,i=(i=i+Math.imul(L,K)|0)+Math.imul(F,V)|0,o=o+Math.imul(F,K)|0,n=n+Math.imul(R,Y)|0,i=(i=i+Math.imul(R,J)|0)+Math.imul(T,Y)|0,o=o+Math.imul(T,J)|0,n=n+Math.imul(I,Q)|0,i=(i=i+Math.imul(I,tt)|0)+Math.imul(C,Q)|0,o=o+Math.imul(C,tt)|0,n=n+Math.imul(B,rt)|0,i=(i=i+Math.imul(B,nt)|0)+Math.imul(A,rt)|0,o=o+Math.imul(A,nt)|0,n=n+Math.imul(x,ot)|0,i=(i=i+Math.imul(x,st)|0)+Math.imul(_,ot)|0,o=o+Math.imul(_,st)|0,n=n+Math.imul(v,ht)|0,i=(i=i+Math.imul(v,ct)|0)+Math.imul(E,ht)|0,o=o+Math.imul(E,ct)|0,n=n+Math.imul(y,lt)|0,i=(i=i+Math.imul(y,ft)|0)+Math.imul(b,lt)|0,o=o+Math.imul(b,ft)|0;var Bt=(c+(n=n+Math.imul(p,pt)|0)|0)+((8191&(i=(i=i+Math.imul(p,mt)|0)+Math.imul(m,pt)|0))<<13)|0;c=((o=o+Math.imul(m,mt)|0)+(i>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,n=Math.imul(D,V),i=(i=Math.imul(D,K))+Math.imul(H,V)|0,o=Math.imul(H,K),n=n+Math.imul(L,Y)|0,i=(i=i+Math.imul(L,J)|0)+Math.imul(F,Y)|0,o=o+Math.imul(F,J)|0,n=n+Math.imul(R,Q)|0,i=(i=i+Math.imul(R,tt)|0)+Math.imul(T,Q)|0,o=o+Math.imul(T,tt)|0,n=n+Math.imul(I,rt)|0,i=(i=i+Math.imul(I,nt)|0)+Math.imul(C,rt)|0,o=o+Math.imul(C,nt)|0,n=n+Math.imul(B,ot)|0,i=(i=i+Math.imul(B,st)|0)+Math.imul(A,ot)|0,o=o+Math.imul(A,st)|0,n=n+Math.imul(x,ht)|0,i=(i=i+Math.imul(x,ct)|0)+Math.imul(_,ht)|0,o=o+Math.imul(_,ct)|0,n=n+Math.imul(v,lt)|0,i=(i=i+Math.imul(v,ft)|0)+Math.imul(E,lt)|0,o=o+Math.imul(E,ft)|0;var At=(c+(n=n+Math.imul(y,pt)|0)|0)+((8191&(i=(i=i+Math.imul(y,mt)|0)+Math.imul(b,pt)|0))<<13)|0;c=((o=o+Math.imul(b,mt)|0)+(i>>>13)|0)+(At>>>26)|0,At&=67108863,n=Math.imul(D,Y),i=(i=Math.imul(D,J))+Math.imul(H,Y)|0,o=Math.imul(H,J),n=n+Math.imul(L,Q)|0,i=(i=i+Math.imul(L,tt)|0)+Math.imul(F,Q)|0,o=o+Math.imul(F,tt)|0,n=n+Math.imul(R,rt)|0,i=(i=i+Math.imul(R,nt)|0)+Math.imul(T,rt)|0,o=o+Math.imul(T,nt)|0,n=n+Math.imul(I,ot)|0,i=(i=i+Math.imul(I,st)|0)+Math.imul(C,ot)|0,o=o+Math.imul(C,st)|0,n=n+Math.imul(B,ht)|0,i=(i=i+Math.imul(B,ct)|0)+Math.imul(A,ht)|0,o=o+Math.imul(A,ct)|0,n=n+Math.imul(x,lt)|0,i=(i=i+Math.imul(x,ft)|0)+Math.imul(_,lt)|0,o=o+Math.imul(_,ft)|0;var St=(c+(n=n+Math.imul(v,pt)|0)|0)+((8191&(i=(i=i+Math.imul(v,mt)|0)+Math.imul(E,pt)|0))<<13)|0;c=((o=o+Math.imul(E,mt)|0)+(i>>>13)|0)+(St>>>26)|0,St&=67108863,n=Math.imul(D,Q),i=(i=Math.imul(D,tt))+Math.imul(H,Q)|0,o=Math.imul(H,tt),n=n+Math.imul(L,rt)|0,i=(i=i+Math.imul(L,nt)|0)+Math.imul(F,rt)|0,o=o+Math.imul(F,nt)|0,n=n+Math.imul(R,ot)|0,i=(i=i+Math.imul(R,st)|0)+Math.imul(T,ot)|0,o=o+Math.imul(T,st)|0,n=n+Math.imul(I,ht)|0,i=(i=i+Math.imul(I,ct)|0)+Math.imul(C,ht)|0,o=o+Math.imul(C,ct)|0,n=n+Math.imul(B,lt)|0,i=(i=i+Math.imul(B,ft)|0)+Math.imul(A,lt)|0,o=o+Math.imul(A,ft)|0;var It=(c+(n=n+Math.imul(x,pt)|0)|0)+((8191&(i=(i=i+Math.imul(x,mt)|0)+Math.imul(_,pt)|0))<<13)|0;c=((o=o+Math.imul(_,mt)|0)+(i>>>13)|0)+(It>>>26)|0,It&=67108863,n=Math.imul(D,rt),i=(i=Math.imul(D,nt))+Math.imul(H,rt)|0,o=Math.imul(H,nt),n=n+Math.imul(L,ot)|0,i=(i=i+Math.imul(L,st)|0)+Math.imul(F,ot)|0,o=o+Math.imul(F,st)|0,n=n+Math.imul(R,ht)|0,i=(i=i+Math.imul(R,ct)|0)+Math.imul(T,ht)|0,o=o+Math.imul(T,ct)|0,n=n+Math.imul(I,lt)|0,i=(i=i+Math.imul(I,ft)|0)+Math.imul(C,lt)|0,o=o+Math.imul(C,ft)|0;var Ct=(c+(n=n+Math.imul(B,pt)|0)|0)+((8191&(i=(i=i+Math.imul(B,mt)|0)+Math.imul(A,pt)|0))<<13)|0;c=((o=o+Math.imul(A,mt)|0)+(i>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,n=Math.imul(D,ot),i=(i=Math.imul(D,st))+Math.imul(H,ot)|0,o=Math.imul(H,st),n=n+Math.imul(L,ht)|0,i=(i=i+Math.imul(L,ct)|0)+Math.imul(F,ht)|0,o=o+Math.imul(F,ct)|0,n=n+Math.imul(R,lt)|0,i=(i=i+Math.imul(R,ft)|0)+Math.imul(T,lt)|0,o=o+Math.imul(T,ft)|0;var Mt=(c+(n=n+Math.imul(I,pt)|0)|0)+((8191&(i=(i=i+Math.imul(I,mt)|0)+Math.imul(C,pt)|0))<<13)|0;c=((o=o+Math.imul(C,mt)|0)+(i>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,n=Math.imul(D,ht),i=(i=Math.imul(D,ct))+Math.imul(H,ht)|0,o=Math.imul(H,ct),n=n+Math.imul(L,lt)|0,i=(i=i+Math.imul(L,ft)|0)+Math.imul(F,lt)|0,o=o+Math.imul(F,ft)|0;var Rt=(c+(n=n+Math.imul(R,pt)|0)|0)+((8191&(i=(i=i+Math.imul(R,mt)|0)+Math.imul(T,pt)|0))<<13)|0;c=((o=o+Math.imul(T,mt)|0)+(i>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,n=Math.imul(D,lt),i=(i=Math.imul(D,ft))+Math.imul(H,lt)|0,o=Math.imul(H,ft);var Tt=(c+(n=n+Math.imul(L,pt)|0)|0)+((8191&(i=(i=i+Math.imul(L,mt)|0)+Math.imul(F,pt)|0))<<13)|0;c=((o=o+Math.imul(F,mt)|0)+(i>>>13)|0)+(Tt>>>26)|0,Tt&=67108863;var Ot=(c+(n=Math.imul(D,pt))|0)+((8191&(i=(i=Math.imul(D,mt))+Math.imul(H,pt)|0))<<13)|0;return c=((o=Math.imul(H,mt))+(i>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,h[0]=gt,h[1]=yt,h[2]=bt,h[3]=wt,h[4]=vt,h[5]=Et,h[6]=kt,h[7]=xt,h[8]=_t,h[9]=Pt,h[10]=Bt,h[11]=At,h[12]=St,h[13]=It,h[14]=Ct,h[15]=Mt,h[16]=Rt,h[17]=Tt,h[18]=Ot,0!==c&&(h[19]=c,r.length++),r};function y(t,e,r){r.negative=e.negative^t.negative,r.length=t.length+e.length;for(var n=0,i=0,o=0;o<r.length-1;o++){var s=i;i=0;for(var a=67108863&n,h=Math.min(o,e.length-1),c=Math.max(0,o-t.length+1);c<=h;c++){var u=o-c,l=(0|t.words[u])*(0|e.words[c]),f=67108863&l;a=67108863&(f=f+a|0),i+=(s=(s=s+(l/67108864|0)|0)+(f>>>26)|0)>>>26,s&=67108863}r.words[o]=a,n=s,s=i}return 0!==n?r.words[o]=n:r.length--,r._strip()}function b(t,e,r){return y(t,e,r)}function w(t,e){this.x=t,this.y=e}Math.imul||(g=m),o.prototype.mulTo=function(t,e){var r=this.length+t.length;return 10===this.length&&10===t.length?g(this,t,e):r<63?m(this,t,e):r<1024?y(this,t,e):b(this,t,e)},w.prototype.makeRBT=function(t){for(var e=new Array(t),r=o.prototype._countBits(t)-1,n=0;n<t;n++)e[n]=this.revBin(n,r,t);return e},w.prototype.revBin=function(t,e,r){if(0===t||t===r-1)return t;for(var n=0,i=0;i<e;i++)n|=(1&t)<<e-i-1,t>>=1;return n},w.prototype.permute=function(t,e,r,n,i,o){for(var s=0;s<o;s++)n[s]=e[t[s]],i[s]=r[t[s]]},w.prototype.transform=function(t,e,r,n,i,o){this.permute(o,t,e,r,n,i);for(var s=1;s<i;s<<=1)for(var a=s<<1,h=Math.cos(2*Math.PI/a),c=Math.sin(2*Math.PI/a),u=0;u<i;u+=a)for(var l=h,f=c,d=0;d<s;d++){var p=r[u+d],m=n[u+d],g=r[u+d+s],y=n[u+d+s],b=l*g-f*y;y=l*y+f*g,g=b,r[u+d]=p+g,n[u+d]=m+y,r[u+d+s]=p-g,n[u+d+s]=m-y,d!==a&&(b=h*l-c*f,f=h*f+c*l,l=b)}},w.prototype.guessLen13b=function(t,e){var r=1|Math.max(e,t),n=1&r,i=0;for(r=r/2|0;r;r>>>=1)i++;return 1<<i+1+n},w.prototype.conjugate=function(t,e,r){if(!(r<=1))for(var n=0;n<r/2;n++){var i=t[n];t[n]=t[r-n-1],t[r-n-1]=i,i=e[n],e[n]=-e[r-n-1],e[r-n-1]=-i}},w.prototype.normalize13b=function(t,e){for(var r=0,n=0;n<e/2;n++){var i=8192*Math.round(t[2*n+1]/e)+Math.round(t[2*n]/e)+r;t[n]=67108863&i,r=i<67108864?0:i/67108864|0}return t},w.prototype.convert13b=function(t,e,r,i){for(var o=0,s=0;s<e;s++)o+=0|t[s],r[2*s]=8191&o,o>>>=13,r[2*s+1]=8191&o,o>>>=13;for(s=2*e;s<i;++s)r[s]=0;n(0===o),n(!(-8192&o))},w.prototype.stub=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=0;return e},w.prototype.mulp=function(t,e,r){var n=2*this.guessLen13b(t.length,e.length),i=this.makeRBT(n),o=this.stub(n),s=new Array(n),a=new Array(n),h=new Array(n),c=new Array(n),u=new Array(n),l=new Array(n),f=r.words;f.length=n,this.convert13b(t.words,t.length,s,n),this.convert13b(e.words,e.length,c,n),this.transform(s,o,a,h,n,i),this.transform(c,o,u,l,n,i);for(var d=0;d<n;d++){var p=a[d]*u[d]-h[d]*l[d];h[d]=a[d]*l[d]+h[d]*u[d],a[d]=p}return this.conjugate(a,h,n),this.transform(a,h,f,o,n,i),this.conjugate(f,o,n),this.normalize13b(f,n),r.negative=t.negative^e.negative,r.length=t.length+e.length,r._strip()},o.prototype.mul=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},o.prototype.mulf=function(t){var e=new o(null);return e.words=new Array(this.length+t.length),b(this,t,e)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){var e=t<0;e&&(t=-t),n("number"==typeof t),n(t<67108864);for(var r=0,i=0;i<this.length;i++){var o=(0|this.words[i])*t,s=(67108863&o)+(67108863&r);r>>=26,r+=o/67108864|0,r+=s>>>26,this.words[i]=67108863&s}return 0!==r&&(this.words[i]=r,this.length++),e?this.ineg():this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),r=0;r<e.length;r++){var n=r/26|0,i=r%26;e[r]=t.words[n]>>>i&1}return e}(t);if(0===e.length)return new o(1);for(var r=this,n=0;n<e.length&&0===e[n];n++,r=r.sqr());if(++n<e.length)for(var i=r.sqr();n<e.length;n++,i=i.sqr())0!==e[n]&&(r=r.mul(i));return r},o.prototype.iushln=function(t){n("number"==typeof t&&t>=0);var e,r=t%26,i=(t-r)/26,o=67108863>>>26-r<<26-r;if(0!==r){var s=0;for(e=0;e<this.length;e++){var a=this.words[e]&o,h=(0|this.words[e])-a<<r;this.words[e]=h|s,s=a>>>26-r}s&&(this.words[e]=s,this.length++)}if(0!==i){for(e=this.length-1;e>=0;e--)this.words[e+i]=this.words[e];for(e=0;e<i;e++)this.words[e]=0;this.length+=i}return this._strip()},o.prototype.ishln=function(t){return n(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,e,r){var i;n("number"==typeof t&&t>=0),i=e?(e-e%26)/26:0;var o=t%26,s=Math.min((t-o)/26,this.length),a=67108863^67108863>>>o<<o,h=r;if(i-=s,i=Math.max(0,i),h){for(var c=0;c<s;c++)h.words[c]=this.words[c];h.length=s}if(0===s);else if(this.length>s)for(this.length-=s,c=0;c<this.length;c++)this.words[c]=this.words[c+s];else this.words[0]=0,this.length=1;var u=0;for(c=this.length-1;c>=0&&(0!==u||c>=i);c--){var l=0|this.words[c];this.words[c]=u<<26-o|l>>>o,u=l&a}return h&&0!==u&&(h.words[h.length++]=u),0===this.length&&(this.words[0]=0,this.length=1),this._strip()},o.prototype.ishrn=function(t,e,r){return n(0===this.negative),this.iushrn(t,e,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26,i=1<<e;return!(this.length<=r||!(this.words[r]&i))},o.prototype.imaskn=function(t){n("number"==typeof t&&t>=0);var e=t%26,r=(t-e)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==e&&r++,this.length=Math.min(r,this.length),0!==e){var i=67108863^67108863>>>e<<e;this.words[this.length-1]&=i}return this._strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return n("number"==typeof t),n(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<=t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},o.prototype.isubn=function(t){if(n("number"==typeof t),n(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this._strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,e,r){var i,o,s=t.length+r;this._expand(s);var a=0;for(i=0;i<t.length;i++){o=(0|this.words[i+r])+a;var h=(0|t.words[i])*e;a=((o-=67108863&h)>>26)-(h/67108864|0),this.words[i+r]=67108863&o}for(;i<this.length-r;i++)a=(o=(0|this.words[i+r])+a)>>26,this.words[i+r]=67108863&o;if(0===a)return this._strip();for(n(-1===a),a=0,i=0;i<this.length;i++)a=(o=-(0|this.words[i])+a)>>26,this.words[i]=67108863&o;return this.negative=1,this._strip()},o.prototype._wordDiv=function(t,e){var r=(this.length,t.length),n=this.clone(),i=t,s=0|i.words[i.length-1];0!=(r=26-this._countBits(s))&&(i=i.ushln(r),n.iushln(r),s=0|i.words[i.length-1]);var a,h=n.length-i.length;if("mod"!==e){(a=new o(null)).length=h+1,a.words=new Array(a.length);for(var c=0;c<a.length;c++)a.words[c]=0}var u=n.clone()._ishlnsubmul(i,1,h);0===u.negative&&(n=u,a&&(a.words[h]=1));for(var l=h-1;l>=0;l--){var f=67108864*(0|n.words[i.length+l])+(0|n.words[i.length+l-1]);for(f=Math.min(f/s|0,67108863),n._ishlnsubmul(i,f,l);0!==n.negative;)f--,n.negative=0,n._ishlnsubmul(i,1,l),n.isZero()||(n.negative^=1);a&&(a.words[l]=f)}return a&&a._strip(),n._strip(),"div"!==e&&0!==r&&n.iushrn(r),{div:a||null,mod:n}},o.prototype.divmod=function(t,e,r){return n(!t.isZero()),this.isZero()?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(a=this.neg().divmod(t,e),"mod"!==e&&(i=a.div.neg()),"div"!==e&&(s=a.mod.neg(),r&&0!==s.negative&&s.iadd(t)),{div:i,mod:s}):0===this.negative&&0!==t.negative?(a=this.divmod(t.neg(),e),"mod"!==e&&(i=a.div.neg()),{div:i,mod:a.mod}):this.negative&t.negative?(a=this.neg().divmod(t.neg(),e),"div"!==e&&(s=a.mod.neg(),r&&0!==s.negative&&s.isub(t)),{div:a.div,mod:s}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new o(this.modrn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modrn(t.words[0]))}:this._wordDiv(t,e);var i,s,a},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var r=0!==e.div.negative?e.mod.isub(t):e.mod,n=t.ushrn(1),i=t.andln(1),o=r.cmp(n);return o<0||1===i&&0===o?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},o.prototype.modrn=function(t){var e=t<0;e&&(t=-t),n(t<=67108863);for(var r=(1<<26)%t,i=0,o=this.length-1;o>=0;o--)i=(r*i+(0|this.words[o]))%t;return e?-i:i},o.prototype.modn=function(t){return this.modrn(t)},o.prototype.idivn=function(t){var e=t<0;e&&(t=-t),n(t<=67108863);for(var r=0,i=this.length-1;i>=0;i--){var o=(0|this.words[i])+67108864*r;this.words[i]=o/t|0,r=o%t}return this._strip(),e?this.ineg():this},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i=new o(1),s=new o(0),a=new o(0),h=new o(1),c=0;e.isEven()&&r.isEven();)e.iushrn(1),r.iushrn(1),++c;for(var u=r.clone(),l=e.clone();!e.isZero();){for(var f=0,d=1;!(e.words[0]&d)&&f<26;++f,d<<=1);if(f>0)for(e.iushrn(f);f-- >0;)(i.isOdd()||s.isOdd())&&(i.iadd(u),s.isub(l)),i.iushrn(1),s.iushrn(1);for(var p=0,m=1;!(r.words[0]&m)&&p<26;++p,m<<=1);if(p>0)for(r.iushrn(p);p-- >0;)(a.isOdd()||h.isOdd())&&(a.iadd(u),h.isub(l)),a.iushrn(1),h.iushrn(1);e.cmp(r)>=0?(e.isub(r),i.isub(a),s.isub(h)):(r.isub(e),a.isub(i),h.isub(s))}return{a,b:h,gcd:r.iushln(c)}},o.prototype._invmp=function(t){n(0===t.negative),n(!t.isZero());var e=this,r=t.clone();e=0!==e.negative?e.umod(t):e.clone();for(var i,s=new o(1),a=new o(0),h=r.clone();e.cmpn(1)>0&&r.cmpn(1)>0;){for(var c=0,u=1;!(e.words[0]&u)&&c<26;++c,u<<=1);if(c>0)for(e.iushrn(c);c-- >0;)s.isOdd()&&s.iadd(h),s.iushrn(1);for(var l=0,f=1;!(r.words[0]&f)&&l<26;++l,f<<=1);if(l>0)for(r.iushrn(l);l-- >0;)a.isOdd()&&a.iadd(h),a.iushrn(1);e.cmp(r)>=0?(e.isub(r),s.isub(a)):(r.isub(e),a.isub(s))}return(i=0===e.cmpn(1)?s:a).cmpn(0)<0&&i.iadd(t),i},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),r=t.clone();e.negative=0,r.negative=0;for(var n=0;e.isEven()&&r.isEven();n++)e.iushrn(1),r.iushrn(1);for(;;){for(;e.isEven();)e.iushrn(1);for(;r.isEven();)r.iushrn(1);var i=e.cmp(r);if(i<0){var o=e;e=r,r=o}else if(0===i||0===r.cmpn(1))break;e.isub(r)}return r.iushln(n)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return!(1&this.words[0])},o.prototype.isOdd=function(){return!(1&~this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){n("number"==typeof t);var e=t%26,r=(t-e)/26,i=1<<e;if(this.length<=r)return this._expand(r+1),this.words[r]|=i,this;for(var o=i,s=r;0!==o&&s<this.length;s++){var a=0|this.words[s];o=(a+=o)>>>26,a&=67108863,this.words[s]=a}return 0!==o&&(this.words[s]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var e,r=t<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this._strip(),this.length>1)e=1;else{r&&(t=-t),n(t<=67108863,"Number is too big");var i=0|this.words[0];e=i===t?0:i<t?-1:1}return 0!==this.negative?0|-e:e},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,r=this.length-1;r>=0;r--){var n=0|this.words[r],i=0|t.words[r];if(n!==i){n<i?e=-1:n>i&&(e=1);break}}return e},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new B(t)},o.prototype.toRed=function(t){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return n(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return n(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function E(t,e){this.name=t,this.p=new o(e,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function k(){E.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function x(){E.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function _(){E.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function P(){E.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function B(t){if("string"==typeof t){var e=o._prime(t);this.m=e.p,this.prime=e}else n(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function A(t){B.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}E.prototype._tmp=function(){var t=new o(null);return t.words=new Array(Math.ceil(this.n/13)),t},E.prototype.ireduce=function(t){var e,r=t;do{this.split(r,this.tmp),e=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(e>this.n);var n=e<this.n?-1:r.ucmp(this.p);return 0===n?(r.words[0]=0,r.length=1):n>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},E.prototype.split=function(t,e){t.iushrn(this.n,0,e)},E.prototype.imulK=function(t){return t.imul(this.k)},i(k,E),k.prototype.split=function(t,e){for(var r=4194303,n=Math.min(t.length,9),i=0;i<n;i++)e.words[i]=t.words[i];if(e.length=n,t.length<=9)return t.words[0]=0,void(t.length=1);var o=t.words[9];for(e.words[e.length++]=o&r,i=10;i<t.length;i++){var s=0|t.words[i];t.words[i-10]=(s&r)<<4|o>>>22,o=s}o>>>=22,t.words[i-10]=o,0===o&&t.length>10?t.length-=10:t.length-=9},k.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,r=0;r<t.length;r++){var n=0|t.words[r];e+=977*n,t.words[r]=67108863&e,e=64*n+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},i(x,E),i(_,E),i(P,E),P.prototype.imulK=function(t){for(var e=0,r=0;r<t.length;r++){var n=19*(0|t.words[r])+e,i=67108863&n;n>>>=26,t.words[r]=i,e=n}return 0!==e&&(t.words[t.length++]=e),t},o._prime=function(t){if(v[t])return v[t];var e;if("k256"===t)e=new k;else if("p224"===t)e=new x;else if("p192"===t)e=new _;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new P}return v[t]=e,e},B.prototype._verify1=function(t){n(0===t.negative,"red works only with positives"),n(t.red,"red works only with red numbers")},B.prototype._verify2=function(t,e){n(!(t.negative|e.negative),"red works only with positives"),n(t.red&&t.red===e.red,"red works only with red numbers")},B.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):(u(t,t.umod(this.m)._forceRed(this)),t)},B.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},B.prototype.add=function(t,e){this._verify2(t,e);var r=t.add(e);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},B.prototype.iadd=function(t,e){this._verify2(t,e);var r=t.iadd(e);return r.cmp(this.m)>=0&&r.isub(this.m),r},B.prototype.sub=function(t,e){this._verify2(t,e);var r=t.sub(e);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},B.prototype.isub=function(t,e){this._verify2(t,e);var r=t.isub(e);return r.cmpn(0)<0&&r.iadd(this.m),r},B.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},B.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},B.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},B.prototype.isqr=function(t){return this.imul(t,t.clone())},B.prototype.sqr=function(t){return this.mul(t,t)},B.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(n(e%2==1),3===e){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var i=this.m.subn(1),s=0;!i.isZero()&&0===i.andln(1);)s++,i.iushrn(1);n(!i.isZero());var a=new o(1).toRed(this),h=a.redNeg(),c=this.m.subn(1).iushrn(1),u=this.m.bitLength();for(u=new o(2*u*u).toRed(this);0!==this.pow(u,c).cmp(h);)u.redIAdd(h);for(var l=this.pow(u,i),f=this.pow(t,i.addn(1).iushrn(1)),d=this.pow(t,i),p=s;0!==d.cmp(a);){for(var m=d,g=0;0!==m.cmp(a);g++)m=m.redSqr();n(g<p);var y=this.pow(l,new o(1).iushln(p-g-1));f=f.redMul(y),l=y.redSqr(),d=d.redMul(l),p=g}return f},B.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},B.prototype.pow=function(t,e){if(e.isZero())return new o(1).toRed(this);if(0===e.cmpn(1))return t.clone();var r=new Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var n=2;n<r.length;n++)r[n]=this.mul(r[n-1],t);var i=r[0],s=0,a=0,h=e.bitLength()%26;for(0===h&&(h=26),n=e.length-1;n>=0;n--){for(var c=e.words[n],u=h-1;u>=0;u--){var l=c>>u&1;i!==r[0]&&(i=this.sqr(i)),0!==l||0!==s?(s<<=1,s|=l,(4==++a||0===n&&0===u)&&(i=this.mul(i,r[s]),a=0,s=0)):a=0}h=26}return i},B.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},B.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},o.mont=function(t){return new A(t)},i(A,B),A.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},A.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},A.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),o=i;return i.cmp(this.m)>=0?o=i.isub(this.m):i.cmpn(0)<0&&(o=i.iadd(this.m)),o._forceRed(this)},A.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new o(0)._forceRed(this);var r=t.mul(e),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),i=r.isub(n).iushrn(this.shift),s=i;return i.cmp(this.m)>=0?s=i.isub(this.m):i.cmpn(0)<0&&(s=i.iadd(this.m)),s._forceRed(this)},A.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t=r.nmd(t),this)},287:(t,e,r)=>{"use strict";const n=r(526),i=r(251),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;e.Buffer=h,e.SlowBuffer=function(t){return+t!=t&&(t=0),h.alloc(+t)},e.INSPECT_MAX_BYTES=50;const s=2147483647;function a(t){if(t>s)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,h.prototype),e}function h(t,e,r){if("number"==typeof t){if("string"==typeof e)throw new TypeError('The "string" argument must be of type string. Received type number');return l(t)}return c(t,e,r)}function c(t,e,r){if("string"==typeof t)return function(t,e){if("string"==typeof e&&""!==e||(e="utf8"),!h.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const r=0|m(t,e);let n=a(r);const i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return function(t){if(Z(t,Uint8Array)){const e=new Uint8Array(t);return d(e.buffer,e.byteOffset,e.byteLength)}return f(t)}(t);if(null==t)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(Z(t,ArrayBuffer)||t&&Z(t.buffer,ArrayBuffer))return d(t,e,r);if("undefined"!=typeof SharedArrayBuffer&&(Z(t,SharedArrayBuffer)||t&&Z(t.buffer,SharedArrayBuffer)))return d(t,e,r);if("number"==typeof t)throw new TypeError('The "value" argument must not be of type number. Received type number');const n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return h.from(n,e,r);const i=function(t){if(h.isBuffer(t)){const e=0|p(t.length),r=a(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||Y(t.length)?a(0):f(t):"Buffer"===t.type&&Array.isArray(t.data)?f(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return h.from(t[Symbol.toPrimitive]("string"),e,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return u(t),a(t<0?0:0|p(t))}function f(t){const e=t.length<0?0:0|p(t.length),r=a(e);for(let n=0;n<e;n+=1)r[n]=255&t[n];return r}function d(t,e,r){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw new RangeError('"length" is outside of buffer bounds');let n;return n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),Object.setPrototypeOf(n,h.prototype),n}function p(t){if(t>=s)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s.toString(16)+" bytes");return 0|t}function m(t,e){if(h.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||Z(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return W(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(t).length;default:if(i)return n?-1:W(t).length;e=(""+e).toLowerCase(),i=!0}}function g(t,e,r){let n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return C(this,e,r);case"utf8":case"utf-8":return B(this,e,r);case"ascii":return S(this,e,r);case"latin1":case"binary":return I(this,e,r);case"base64":return P(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function y(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function b(t,e,r,n,i){if(0===t.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),Y(r=+r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof e&&(e=h.from(e,n)),h.isBuffer(e))return 0===e.length?-1:w(t,e,r,n,i);if("number"==typeof e)return e&=255,"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):w(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function w(t,e,r,n,i){let o,s=1,a=t.length,h=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;s=2,a/=2,h/=2,r/=2}function c(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){let n=-1;for(o=r;o<a;o++)if(c(t,o)===c(e,-1===n?0:o-n)){if(-1===n&&(n=o),o-n+1===h)return n*s}else-1!==n&&(o-=o-n),n=-1}else for(r+h>a&&(r=a-h),o=r;o>=0;o--){let r=!0;for(let n=0;n<h;n++)if(c(t,o+n)!==c(e,n)){r=!1;break}if(r)return o}return-1}function v(t,e,r,n){r=Number(r)||0;const i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;const o=e.length;let s;for(n>o/2&&(n=o/2),s=0;s<n;++s){const n=parseInt(e.substr(2*s,2),16);if(Y(n))return s;t[r+s]=n}return s}function E(t,e,r,n){return K(W(e,t.length-r),t,r,n)}function k(t,e,r,n){return K(function(t){const e=[];for(let r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function x(t,e,r,n){return K(V(e),t,r,n)}function _(t,e,r,n){return K(function(t,e){let r,n,i;const o=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(e,t.length-r),t,r,n)}function P(t,e,r){return 0===e&&r===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,r))}function B(t,e,r){r=Math.min(t.length,r);const n=[];let i=e;for(;i<r;){const e=t[i];let o=null,s=e>239?4:e>223?3:e>191?2:1;if(i+s<=r){let r,n,a,h;switch(s){case 1:e<128&&(o=e);break;case 2:r=t[i+1],128==(192&r)&&(h=(31&e)<<6|63&r,h>127&&(o=h));break;case 3:r=t[i+1],n=t[i+2],128==(192&r)&&128==(192&n)&&(h=(15&e)<<12|(63&r)<<6|63&n,h>2047&&(h<55296||h>57343)&&(o=h));break;case 4:r=t[i+1],n=t[i+2],a=t[i+3],128==(192&r)&&128==(192&n)&&128==(192&a)&&(h=(15&e)<<18|(63&r)<<12|(63&n)<<6|63&a,h>65535&&h<1114112&&(o=h))}}null===o?(o=65533,s=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|1023&o),n.push(o),i+=s}return function(t){const e=t.length;if(e<=A)return String.fromCharCode.apply(String,t);let r="",n=0;for(;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=A));return r}(n)}e.kMaxLength=s,h.TYPED_ARRAY_SUPPORT=function(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),h.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(h.prototype,"parent",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.buffer}}),Object.defineProperty(h.prototype,"offset",{enumerable:!0,get:function(){if(h.isBuffer(this))return this.byteOffset}}),h.poolSize=8192,h.from=function(t,e,r){return c(t,e,r)},Object.setPrototypeOf(h.prototype,Uint8Array.prototype),Object.setPrototypeOf(h,Uint8Array),h.alloc=function(t,e,r){return function(t,e,r){return u(t),t<=0?a(t):void 0!==e?"string"==typeof r?a(t).fill(e,r):a(t).fill(e):a(t)}(t,e,r)},h.allocUnsafe=function(t){return l(t)},h.allocUnsafeSlow=function(t){return l(t)},h.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==h.prototype},h.compare=function(t,e){if(Z(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),Z(e,Uint8Array)&&(e=h.from(e,e.offset,e.byteLength)),!h.isBuffer(t)||!h.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let r=t.length,n=e.length;for(let i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},h.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},h.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return h.alloc(0);let r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;const n=h.allocUnsafe(e);let i=0;for(r=0;r<t.length;++r){let e=t[r];if(Z(e,Uint8Array))i+e.length>n.length?(h.isBuffer(e)||(e=h.from(e)),e.copy(n,i)):Uint8Array.prototype.set.call(n,e,i);else{if(!h.isBuffer(e))throw new TypeError('"list" argument must be an Array of Buffers');e.copy(n,i)}i+=e.length}return n},h.byteLength=m,h.prototype._isBuffer=!0,h.prototype.swap16=function(){const t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)y(this,e,e+1);return this},h.prototype.swap32=function(){const t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},h.prototype.swap64=function(){const t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},h.prototype.toString=function(){const t=this.length;return 0===t?"":0===arguments.length?B(this,0,t):g.apply(this,arguments)},h.prototype.toLocaleString=h.prototype.toString,h.prototype.equals=function(t){if(!h.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===h.compare(this,t)},h.prototype.inspect=function(){let t="";const r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(h.prototype[o]=h.prototype.inspect),h.prototype.compare=function(t,e,r,n,i){if(Z(t,Uint8Array)&&(t=h.from(t,t.offset,t.byteLength)),!h.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return-1;if(e>=r)return 1;if(this===t)return 0;let o=(i>>>=0)-(n>>>=0),s=(r>>>=0)-(e>>>=0);const a=Math.min(o,s),c=this.slice(n,i),u=t.slice(e,r);for(let t=0;t<a;++t)if(c[t]!==u[t]){o=c[t],s=u[t];break}return o<s?-1:s<o?1:0},h.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},h.prototype.indexOf=function(t,e,r){return b(this,t,e,r,!0)},h.prototype.lastIndexOf=function(t,e,r){return b(this,t,e,r,!1)},h.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}const i=this.length-e;if((void 0===r||r>i)&&(r=i),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let o=!1;for(;;)switch(n){case"hex":return v(this,t,e,r);case"utf8":case"utf-8":return E(this,t,e,r);case"ascii":case"latin1":case"binary":return k(this,t,e,r);case"base64":return x(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},h.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const A=4096;function S(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}function I(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function C(t,e,r){const n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);let i="";for(let n=e;n<r;++n)i+=J[t[n]];return i}function M(t,e,r){const n=t.slice(e,r);let i="";for(let t=0;t<n.length-1;t+=2)i+=String.fromCharCode(n[t]+256*n[t+1]);return i}function R(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function T(t,e,r,n,i,o){if(!h.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}function O(t,e,r,n,i){z(e,n,i,t,r,7);let o=Number(e&BigInt(4294967295));t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,s>>=8,t[r++]=s,r}function L(t,e,r,n,i){z(e,n,i,t,r,7);let o=Number(e&BigInt(4294967295));t[r+7]=o,o>>=8,t[r+6]=o,o>>=8,t[r+5]=o,o>>=8,t[r+4]=o;let s=Number(e>>BigInt(32)&BigInt(4294967295));return t[r+3]=s,s>>=8,t[r+2]=s,s>>=8,t[r+1]=s,s>>=8,t[r]=s,r+8}function F(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function N(t,e,r,n,o){return e=+e,r>>>=0,o||F(t,0,r,4),i.write(t,e,r,n,23,4),r+4}function D(t,e,r,n,o){return e=+e,r>>>=0,o||F(t,0,r,8),i.write(t,e,r,n,52,8),r+8}h.prototype.slice=function(t,e){const r=this.length;(t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);const n=this.subarray(t,e);return Object.setPrototypeOf(n,h.prototype),n},h.prototype.readUintLE=h.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return n},h.prototype.readUintBE=h.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t+--e],i=1;for(;e>0&&(i*=256);)n+=this[t+--e]*i;return n},h.prototype.readUint8=h.prototype.readUInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),this[t]},h.prototype.readUint16LE=h.prototype.readUInt16LE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]|this[t+1]<<8},h.prototype.readUint16BE=h.prototype.readUInt16BE=function(t,e){return t>>>=0,e||R(t,2,this.length),this[t]<<8|this[t+1]},h.prototype.readUint32LE=h.prototype.readUInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},h.prototype.readUint32BE=h.prototype.readUInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},h.prototype.readBigUInt64LE=X((function(t){j(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=e+256*this[++t]+65536*this[++t]+this[++t]*2**24,i=this[++t]+256*this[++t]+65536*this[++t]+r*2**24;return BigInt(n)+(BigInt(i)<<BigInt(32))})),h.prototype.readBigUInt64BE=X((function(t){j(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=e*2**24+65536*this[++t]+256*this[++t]+this[++t],i=this[++t]*2**24+65536*this[++t]+256*this[++t]+r;return(BigInt(n)<<BigInt(32))+BigInt(i)})),h.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*e)),n},h.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||R(t,e,this.length);let n=e,i=1,o=this[t+--n];for(;n>0&&(i*=256);)o+=this[t+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*e)),o},h.prototype.readInt8=function(t,e){return t>>>=0,e||R(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},h.prototype.readInt16LE=function(t,e){t>>>=0,e||R(t,2,this.length);const r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt16BE=function(t,e){t>>>=0,e||R(t,2,this.length);const r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},h.prototype.readInt32LE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},h.prototype.readInt32BE=function(t,e){return t>>>=0,e||R(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},h.prototype.readBigInt64LE=X((function(t){j(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=this[t+4]+256*this[t+5]+65536*this[t+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+this[++t]*2**24)})),h.prototype.readBigInt64BE=X((function(t){j(t>>>=0,"offset");const e=this[t],r=this[t+7];void 0!==e&&void 0!==r||q(t,this.length-8);const n=(e<<24)+65536*this[++t]+256*this[++t]+this[++t];return(BigInt(n)<<BigInt(32))+BigInt(this[++t]*2**24+65536*this[++t]+256*this[++t]+r)})),h.prototype.readFloatLE=function(t,e){return t>>>=0,e||R(t,4,this.length),i.read(this,t,!0,23,4)},h.prototype.readFloatBE=function(t,e){return t>>>=0,e||R(t,4,this.length),i.read(this,t,!1,23,4)},h.prototype.readDoubleLE=function(t,e){return t>>>=0,e||R(t,8,this.length),i.read(this,t,!0,52,8)},h.prototype.readDoubleBE=function(t,e){return t>>>=0,e||R(t,8,this.length),i.read(this,t,!1,52,8)},h.prototype.writeUintLE=h.prototype.writeUIntLE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||T(this,t,e,r,Math.pow(2,8*r)-1,0);let i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},h.prototype.writeUintBE=h.prototype.writeUIntBE=function(t,e,r,n){t=+t,e>>>=0,r>>>=0,n||T(this,t,e,r,Math.pow(2,8*r)-1,0);let i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},h.prototype.writeUint8=h.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,255,0),this[e]=255&t,e+1},h.prototype.writeUint16LE=h.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},h.prototype.writeUint16BE=h.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},h.prototype.writeUint32LE=h.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},h.prototype.writeUint32BE=h.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},h.prototype.writeBigUInt64LE=X((function(t,e=0){return O(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),h.prototype.writeBigUInt64BE=X((function(t,e=0){return L(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))})),h.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);T(this,t,e,r,n-1,-n)}let i=0,o=1,s=0;for(this[e]=255&t;++i<r&&(o*=256);)t<0&&0===s&&0!==this[e+i-1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+r},h.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){const n=Math.pow(2,8*r-1);T(this,t,e,r,n-1,-n)}let i=r-1,o=1,s=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===s&&0!==this[e+i+1]&&(s=1),this[e+i]=(t/o|0)-s&255;return e+r},h.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},h.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},h.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},h.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,2147483647,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},h.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||T(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},h.prototype.writeBigInt64LE=X((function(t,e=0){return O(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),h.prototype.writeBigInt64BE=X((function(t,e=0){return L(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),h.prototype.writeFloatLE=function(t,e,r){return N(this,t,e,!0,r)},h.prototype.writeFloatBE=function(t,e,r){return N(this,t,e,!1,r)},h.prototype.writeDoubleLE=function(t,e,r){return D(this,t,e,!0,r)},h.prototype.writeDoubleBE=function(t,e,r){return D(this,t,e,!1,r)},h.prototype.copy=function(t,e,r,n){if(!h.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);const i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},h.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!h.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===t.length){const e=t.charCodeAt(0);("utf8"===n&&e<128||"latin1"===n)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;let i;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{const o=h.isBuffer(t)?t:h.from(t,n),s=o.length;if(0===s)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%s]}return this};const H={};function U(t,e,r){H[t]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function G(t){let e="",r=t.length;const n="-"===t[0]?1:0;for(;r>=n+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function z(t,e,r,n,i,o){if(t>r||t<e){const n="bigint"==typeof e?"n":"";let i;throw i=o>3?0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(o+1)}${n}`:`>= -(2${n} ** ${8*(o+1)-1}${n}) and < 2 ** ${8*(o+1)-1}${n}`:`>= ${e}${n} and <= ${r}${n}`,new H.ERR_OUT_OF_RANGE("value",i,t)}!function(t,e,r){j(e,"offset"),void 0!==t[e]&&void 0!==t[e+r]||q(e,t.length-(r+1))}(n,i,o)}function j(t,e){if("number"!=typeof t)throw new H.ERR_INVALID_ARG_TYPE(e,"number",t)}function q(t,e,r){if(Math.floor(t)!==t)throw j(t,r),new H.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new H.ERR_BUFFER_OUT_OF_BOUNDS;throw new H.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${e}`,t)}U("ERR_BUFFER_OUT_OF_BOUNDS",(function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),U("ERR_INVALID_ARG_TYPE",(function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`}),TypeError),U("ERR_OUT_OF_RANGE",(function(t,e,r){let n=`The value of "${t}" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>2**32?i=G(String(r)):"bigint"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=G(i)),i+="n"),n+=` It must be ${e}. Received ${i}`,n}),RangeError);const $=/[^+/0-9A-Za-z-_]/g;function W(t,e){let r;e=e||1/0;const n=t.length;let i=null;const o=[];for(let s=0;s<n;++s){if(r=t.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function V(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace($,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function K(t,e,r,n){let i;for(i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function Z(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}function Y(t){return t!=t}const J=function(){const t="0123456789abcdef",e=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let i=0;i<16;++i)e[n+i]=t[r]+t[i]}return e}();function X(t){return"undefined"==typeof BigInt?Q:t}function Q(){throw new Error("BigInt not supported")}},833:(t,e,r)=>{e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,(t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))})),e.splice(i,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(t){}},e.load=function(){let t;try{t=e.storage.getItem("debug")}catch(t){}return!t&&"undefined"!=typeof process&&"env"in process&&(t=process.env.DEBUG),t},e.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},e.storage=function(){try{return localStorage}catch(t){}}(),e.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=r(736)(e);const{formatters:n}=t.exports;n.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}},736:(t,e,r)=>{t.exports=function(t){function e(t){let r,i,o,s=null;function a(...t){if(!a.enabled)return;const n=a,i=Number(new Date),o=i-(r||i);n.diff=o,n.prev=r,n.curr=i,r=i,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let s=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,((r,i)=>{if("%%"===r)return"%";s++;const o=e.formatters[i];if("function"==typeof o){const e=t[s];r=o.call(n,e),t.splice(s,1),s--}return r})),e.formatArgs.call(n,t),(n.log||e.log).apply(n,t)}return a.namespace=t,a.useColors=e.useColors(),a.color=e.selectColor(t),a.extend=n,a.destroy=e.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==e.namespaces&&(i=e.namespaces,o=e.enabled(t)),o),set:t=>{s=t}}),"function"==typeof e.init&&e.init(a),a}function n(t,r){const n=e(this.namespace+(void 0===r?":":r)+t);return n.log=this.log,n}function i(t){return t.toString().substring(2,t.toString().length-2).replace(/\.\*\?$/,"*")}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){const t=[...e.names.map(i),...e.skips.map(i).map((t=>"-"+t))].join(",");return e.enable(""),t},e.enable=function(t){let r;e.save(t),e.namespaces=t,e.names=[],e.skips=[];const n=("string"==typeof t?t:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&("-"===(t=n[r].replace(/\*/g,".*?"))[0]?e.skips.push(new RegExp("^"+t.slice(1)+"$")):e.names.push(new RegExp("^"+t+"$")))},e.enabled=function(t){if("*"===t[t.length-1])return!0;let r,n;for(r=0,n=e.skips.length;r<n;r++)if(e.skips[r].test(t))return!1;for(r=0,n=e.names.length;r<n;r++)if(e.names[r].test(t))return!0;return!1},e.humanize=r(585),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach((r=>{e[r]=t[r]})),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e),r|=0;return e.colors[Math.abs(r)%e.colors.length]},e.enable(e.load()),e}},7:t=>{"use strict";var e,r="object"==typeof Reflect?Reflect:null,n=r&&"function"==typeof r.apply?r.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};e=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var i=Number.isNaN||function(t){return t!=t};function o(){o.init.call(this)}t.exports=o,t.exports.once=function(t,e){return new Promise((function(r,n){function i(r){t.removeListener(e,o),n(r)}function o(){"function"==typeof t.removeListener&&t.removeListener("error",i),r([].slice.call(arguments))}m(t,e,o,{once:!0}),"error"!==e&&function(t,e){"function"==typeof t.on&&m(t,"error",e,{once:!0})}(t,i)}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var s=10;function a(t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function h(t){return void 0===t._maxListeners?o.defaultMaxListeners:t._maxListeners}function c(t,e,r,n){var i,o,s,c;if(a(r),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),s=o[e]),void 0===s)s=o[e]=r,++t._eventsCount;else if("function"==typeof s?s=o[e]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=h(t))>0&&s.length>i&&!s.warned){s.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=e,u.count=s.length,c=u,console&&console.warn&&console.warn(c)}return t}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=u.bind(n);return i.listener=r,n.wrapFn=i,i}function f(t,e,r){var n=t._events;if(void 0===n)return[];var i=n[e];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}(i):p(i,i.length)}function d(t){var e=this._events;if(void 0!==e){var r=e[t];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function p(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}function m(t,e,r,n){if("function"==typeof t.on)n.once?t.once(e,r):t.on(e,r);else{if("function"!=typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){n.once&&t.removeEventListener(e,i),r(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t||t<0||i(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");s=t}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||i(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},o.prototype.getMaxListeners=function(){return h(this)},o.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i="error"===t,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var s;if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var h=o[t];if(void 0===h)return!1;if("function"==typeof h)n(h,this,e);else{var c=h.length,u=p(h,c);for(r=0;r<c;++r)n(u[r],this,e)}return!0},o.prototype.addListener=function(t,e){return c(this,t,e,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(t,e){return c(this,t,e,!0)},o.prototype.once=function(t,e){return a(e),this.on(t,l(this,t,e)),this},o.prototype.prependOnceListener=function(t,e){return a(e),this.prependListener(t,l(this,t,e)),this},o.prototype.removeListener=function(t,e){var r,n,i,o,s;if(a(e),void 0===(n=this._events))return this;if(void 0===(r=n[t]))return this;if(r===e||r.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,r.listener||e));else if("function"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(r,i),1===r.length&&(n[t]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",t,s||e)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(t){var e,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[t]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=r[t]))this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},o.prototype.listeners=function(t){return f(this,t,!0)},o.prototype.rawListeners=function(t){return f(this,t,!1)},o.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):d.call(t,e)},o.prototype.listenerCount=d,o.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}},251:(t,e)=>{e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,h=(1<<a)-1,c=h>>1,u=-7,l=r?i-1:0,f=r?-1:1,d=t[e+l];for(l+=f,o=d&(1<<-u)-1,d>>=-u,u+=a;u>0;o=256*o+t[e+l],l+=f,u-=8);for(s=o&(1<<-u)-1,o>>=-u,u+=n;u>0;s=256*s+t[e+l],l+=f,u-=8);if(0===o)o=1-c;else{if(o===h)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),o-=c}return(d?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,h,c=8*o-i-1,u=(1<<c)-1,l=u>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=u):(s=Math.floor(Math.log(e)/Math.LN2),e*(h=Math.pow(2,-s))<1&&(s--,h*=2),(e+=s+l>=1?f/h:f*Math.pow(2,1-l))*h>=2&&(s++,h/=2),s+l>=u?(a=0,s=u):s+l>=1?(a=(e*h-1)*Math.pow(2,i),s+=l):(a=e*Math.pow(2,l-1)*Math.pow(2,i),s=0));i>=8;t[r+d]=255&a,d+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;t[r+d]=255&s,d+=p,s/=256,c-=8);t[r+d-p]|=128*m}},698:t=>{"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},585:t=>{var e=1e3,r=60*e,n=60*r,i=24*n,o=7*i;function s(t,e,r,n){var i=e>=1.5*r;return Math.round(t/r)+" "+n+(i?"s":"")}t.exports=function(t,a){a=a||{};var h,c,u=typeof t;if("string"===u&&t.length>0)return function(t){if(!((t=String(t)).length>100)){var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(s){var a=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*o;case"days":case"day":case"d":return a*i;case"hours":case"hour":case"hrs":case"hr":case"h":return a*n;case"minutes":case"minute":case"mins":case"min":case"m":return a*r;case"seconds":case"second":case"secs":case"sec":case"s":return a*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(t);if("number"===u&&isFinite(t))return a.long?(h=t,(c=Math.abs(h))>=i?s(h,c,i,"day"):c>=n?s(h,c,n,"hour"):c>=r?s(h,c,r,"minute"):c>=e?s(h,c,e,"second"):h+" ms"):function(t){var o=Math.abs(t);return o>=i?Math.round(t/i)+"d":o>=n?Math.round(t/n)+"h":o>=r?Math.round(t/r)+"m":o>=e?Math.round(t/e)+"s":t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},92:(t,e,r)=>{"use strict";var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}();t.exports=e=n.fetch,n.fetch&&(e.default=n.fetch.bind(n)),e.Headers=n.Headers,e.Request=n.Request,e.Response=n.Response},48:t=>{"use strict";var e={};function r(t,r,n){n||(n=Error);var i=function(t){var e,n;function i(e,n,i){return t.call(this,function(t,e,n){return"string"==typeof r?r:r(t,e,n)}(e,n,i))||this}return n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n,i}(n);i.prototype.name=n.name,i.prototype.code=t,e[t]=i}function n(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}r("ERR_INVALID_OPT_VALUE",(function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'}),TypeError),r("ERR_INVALID_ARG_TYPE",(function(t,e,r){var i,o,s,a,h;if("string"==typeof e&&(o="not ",e.substr(0,4)===o)?(i="must not be",e=e.replace(/^not /,"")):i="must be",function(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-9,r)===e}(t," argument"))s="The ".concat(t," ").concat(i," ").concat(n(e,"type"));else{var c=("number"!=typeof h&&(h=0),h+1>(a=t).length||-1===a.indexOf(".",h)?"argument":"property");s='The "'.concat(t,'" ').concat(c," ").concat(i," ").concat(n(e,"type"))}return s+". Received type ".concat(typeof r)}),TypeError),r("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),r("ERR_METHOD_NOT_IMPLEMENTED",(function(t){return"The "+t+" method is not implemented"})),r("ERR_STREAM_PREMATURE_CLOSE","Premature close"),r("ERR_STREAM_DESTROYED",(function(t){return"Cannot call "+t+" after a stream was destroyed"})),r("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),r("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),r("ERR_STREAM_WRITE_AFTER_END","write after end"),r("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),r("ERR_UNKNOWN_ENCODING",(function(t){return"Unknown encoding: "+t}),TypeError),r("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.F=e},382:(t,e,r)=>{"use strict";var n=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=c;var i=r(412),o=r(708);r(698)(c,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var h=s[a];c.prototype[h]||(c.prototype[h]=o.prototype[h])}function c(t){if(!(this instanceof c))return new c(t);i.call(this,t),o.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",u)))}function u(){this._writableState.ended||process.nextTick(l,this)}function l(t){t.end()}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(c.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(c.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})},600:(t,e,r)=>{"use strict";t.exports=i;var n=r(610);function i(t){if(!(this instanceof i))return new i(t);n.call(this,t)}r(698)(i,n),i.prototype._transform=function(t,e,r){r(null,t)}},412:(t,e,r)=>{"use strict";var n;t.exports=_,_.ReadableState=x,r(7).EventEmitter;var i,o=function(t,e){return t.listeners(e).length},s=r(345),a=r(287).Buffer,h=(void 0!==r.g?r.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},c=r(838);i=c&&c.debuglog?c.debuglog("stream"):function(){};var u,l,f,d=r(726),p=r(896),m=r(291).getHighWaterMark,g=r(48).F,y=g.ERR_INVALID_ARG_TYPE,b=g.ERR_STREAM_PUSH_AFTER_EOF,w=g.ERR_METHOD_NOT_IMPLEMENTED,v=g.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r(698)(_,s);var E=p.errorOrDestroy,k=["error","close","destroy","pause","resume"];function x(t,e,i){n=n||r(382),t=t||{},"boolean"!=typeof i&&(i=e instanceof n),this.objectMode=!!t.objectMode,i&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=m(this,t,"readableHighWaterMark",i),this.buffer=new d,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(u||(u=r(141).I),this.decoder=new u(t.encoding),this.encoding=t.encoding)}function _(t){if(n=n||r(382),!(this instanceof _))return new _(t);var e=this instanceof n;this._readableState=new x(t,this,e),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function P(t,e,r,n,o){i("readableAddChunk",e);var s,c=t._readableState;if(null===e)c.reading=!1,function(t,e){if(i("onEofChunk"),!e.ended){if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?I(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,C(t)))}}(t,c);else if(o||(s=function(t,e){var r,n;return n=e,a.isBuffer(n)||n instanceof h||"string"==typeof e||void 0===e||t.objectMode||(r=new y("chunk",["string","Buffer","Uint8Array"],e)),r}(c,e)),s)E(t,s);else if(c.objectMode||e&&e.length>0)if("string"==typeof e||c.objectMode||Object.getPrototypeOf(e)===a.prototype||(e=function(t){return a.from(t)}(e)),n)c.endEmitted?E(t,new v):B(t,c,e,!0);else if(c.ended)E(t,new b);else{if(c.destroyed)return!1;c.reading=!1,c.decoder&&!r?(e=c.decoder.write(e),c.objectMode||0!==e.length?B(t,c,e,!1):M(t,c)):B(t,c,e,!1)}else n||(c.reading=!1,M(t,c));return!c.ended&&(c.length<c.highWaterMark||0===c.length)}function B(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&I(t)),M(t,e)}Object.defineProperty(_.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),_.prototype.destroy=p.destroy,_.prototype._undestroy=p.undestroy,_.prototype._destroy=function(t,e){e(t)},_.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof t&&((e=e||n.defaultEncoding)!==n.encoding&&(t=a.from(t,e),e=""),r=!0),P(this,t,e,!1,r)},_.prototype.unshift=function(t){return P(this,t,null,!0,!1)},_.prototype.isPaused=function(){return!1===this._readableState.flowing},_.prototype.setEncoding=function(t){u||(u=r(141).I);var e=new u(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;for(var n=this._readableState.buffer.head,i="";null!==n;)i+=e.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var A=1073741824;function S(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!=t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=function(t){return t>=A?t=A:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function I(t){var e=t._readableState;i("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(i("emitReadable",e.flowing),e.emittedReadable=!0,process.nextTick(C,t))}function C(t){var e=t._readableState;i("emitReadable_",e.destroyed,e.length,e.ended),e.destroyed||!e.length&&!e.ended||(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,F(t)}function M(t,e){e.readingMore||(e.readingMore=!0,process.nextTick(R,t,e))}function R(t,e){for(;!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length);){var r=e.length;if(i("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}function T(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function O(t){i("readable nexttick read 0"),t.read(0)}function L(t,e){i("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),F(t),e.flowing&&!e.reading&&t.read(0)}function F(t){var e=t._readableState;for(i("flow",e.flowing);e.flowing&&null!==t.read(););}function N(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r);var r}function D(t){var e=t._readableState;i("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,process.nextTick(H,e,t))}function H(t,e){if(i("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var r=e._writableState;(!r||r.autoDestroy&&r.finished)&&e.destroy()}}function U(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}_.prototype.read=function(t){i("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return i("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?D(this):I(this),null;if(0===(t=S(t,e))&&e.ended)return 0===e.length&&D(this),null;var n,o=e.needReadable;return i("need readable",o),(0===e.length||e.length-t<e.highWaterMark)&&i("length less than watermark",o=!0),e.ended||e.reading?i("reading or ended",o=!1):o&&(i("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=S(r,e))),null===(n=t>0?N(t,e):null)?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&D(this)),null!==n&&this.emit("data",n),n},_.prototype._read=function(t){E(this,new w("_read()"))},_.prototype.pipe=function(t,e){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=t;break;case 1:n.pipes=[n.pipes,t];break;default:n.pipes.push(t)}n.pipesCount+=1,i("pipe count=%d opts=%j",n.pipesCount,e);var s=e&&!1===e.end||t===process.stdout||t===process.stderr?p:a;function a(){i("onend"),t.end()}n.endEmitted?process.nextTick(s):r.once("end",s),t.on("unpipe",(function e(o,s){i("onunpipe"),o===r&&s&&!1===s.hasUnpiped&&(s.hasUnpiped=!0,i("cleanup"),t.removeListener("close",f),t.removeListener("finish",d),t.removeListener("drain",h),t.removeListener("error",l),t.removeListener("unpipe",e),r.removeListener("end",a),r.removeListener("end",p),r.removeListener("data",u),c=!0,!n.awaitDrain||t._writableState&&!t._writableState.needDrain||h())}));var h=function(t){return function(){var e=t._readableState;i("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&o(t,"data")&&(e.flowing=!0,F(t))}}(r);t.on("drain",h);var c=!1;function u(e){i("ondata");var o=t.write(e);i("dest.write",o),!1===o&&((1===n.pipesCount&&n.pipes===t||n.pipesCount>1&&-1!==U(n.pipes,t))&&!c&&(i("false write response, pause",n.awaitDrain),n.awaitDrain++),r.pause())}function l(e){i("onerror",e),p(),t.removeListener("error",l),0===o(t,"error")&&E(t,e)}function f(){t.removeListener("finish",d),p()}function d(){i("onfinish"),t.removeListener("close",f),p()}function p(){i("unpipe"),r.unpipe(t)}return r.on("data",u),function(t,e,r){if("function"==typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?Array.isArray(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}(t,"error",l),t.once("close",f),t.once("finish",d),t.emit("pipe",r),n.flowing||(i("pipe resume"),r.resume()),t},_.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=U(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},_.prototype.on=function(t,e){var r=s.prototype.on.call(this,t,e),n=this._readableState;return"data"===t?(n.readableListening=this.listenerCount("readable")>0,!1!==n.flowing&&this.resume()):"readable"===t&&(n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.flowing=!1,n.emittedReadable=!1,i("on readable",n.length,n.reading),n.length?I(this):n.reading||process.nextTick(O,this))),r},_.prototype.addListener=_.prototype.on,_.prototype.removeListener=function(t,e){var r=s.prototype.removeListener.call(this,t,e);return"readable"===t&&process.nextTick(T,this),r},_.prototype.removeAllListeners=function(t){var e=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||process.nextTick(T,this),e},_.prototype.resume=function(){var t=this._readableState;return t.flowing||(i("resume"),t.flowing=!t.readableListening,function(t,e){e.resumeScheduled||(e.resumeScheduled=!0,process.nextTick(L,t,e))}(this,t)),t.paused=!1,this},_.prototype.pause=function(){return i("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(i("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},_.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var o in t.on("end",(function(){if(i("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(o){i("wrapped data"),r.decoder&&(o=r.decoder.write(o)),r.objectMode&&null==o||(r.objectMode||o&&o.length)&&(e.push(o)||(n=!0,t.pause()))})),t)void 0===this[o]&&"function"==typeof t[o]&&(this[o]=function(e){return function(){return t[e].apply(t,arguments)}}(o));for(var s=0;s<k.length;s++)t.on(k[s],this.emit.bind(this,k[s]));return this._read=function(e){i("wrapped _read",e),n&&(n=!1,t.resume())},this},"function"==typeof Symbol&&(_.prototype[Symbol.asyncIterator]=function(){return void 0===l&&(l=r(955)),l(this)}),Object.defineProperty(_.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(_.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(_.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),_._fromList=N,Object.defineProperty(_.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(_.from=function(t,e){return void 0===f&&(f=r(157)),f(_,t,e)})},610:(t,e,r)=>{"use strict";t.exports=u;var n=r(48).F,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,h=r(382);function c(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function u(t){if(!(this instanceof u))return new u(t);h.call(this,t),this._transformState={afterTransform:c.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",l)}function l(){var t=this;"function"!=typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush((function(e,r){f(t,e,r)}))}function f(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new a;if(t._transformState.transforming)throw new s;return t.push(null)}r(698)(u,h),u.prototype.push=function(t,e){return this._transformState.needTransform=!1,h.prototype.push.call(this,t,e)},u.prototype._transform=function(t,e,r){r(new i("_transform()"))},u.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},u.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},u.prototype._destroy=function(t,e){h.prototype._destroy.call(this,t,(function(t){e(t)}))}},708:(t,e,r)=>{"use strict";function n(t){var e=this;this.next=null,this.entry=null,this.finish=function(){!function(t,e){var r=t.entry;for(t.entry=null;r;){var n=r.callback;e.pendingcb--,n(undefined),r=r.next}e.corkedRequestsFree.next=t}(e,t)}}var i;t.exports=_,_.WritableState=x;var o,s={deprecate:r(643)},a=r(345),h=r(287).Buffer,c=(void 0!==r.g?r.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},u=r(896),l=r(291).getHighWaterMark,f=r(48).F,d=f.ERR_INVALID_ARG_TYPE,p=f.ERR_METHOD_NOT_IMPLEMENTED,m=f.ERR_MULTIPLE_CALLBACK,g=f.ERR_STREAM_CANNOT_PIPE,y=f.ERR_STREAM_DESTROYED,b=f.ERR_STREAM_NULL_VALUES,w=f.ERR_STREAM_WRITE_AFTER_END,v=f.ERR_UNKNOWN_ENCODING,E=u.errorOrDestroy;function k(){}function x(t,e,o){i=i||r(382),t=t||{},"boolean"!=typeof o&&(o=e instanceof i),this.objectMode=!!t.objectMode,o&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=l(this,t,"writableHighWaterMark",o),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var s=!1===t.decodeStrings;this.decodeStrings=!s,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,e){var r=t._writableState,n=r.sync,i=r.writecb;if("function"!=typeof i)throw new m;if(function(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}(r),e)!function(t,e,r,n,i){--e.pendingcb,r?(process.nextTick(i,n),process.nextTick(C,t,e),t._writableState.errorEmitted=!0,E(t,n)):(i(n),t._writableState.errorEmitted=!0,E(t,n),C(t,e))}(t,r,n,e,i);else{var o=S(r)||t.destroyed;o||r.corked||r.bufferProcessing||!r.bufferedRequest||A(t,r),n?process.nextTick(B,t,r,o,i):B(t,r,o,i)}}(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new n(this)}function _(t){var e=this instanceof(i=i||r(382));if(!e&&!o.call(_,this))return new _(t);this._writableState=new x(t,this,e),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),a.call(this)}function P(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new y("write")):r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function B(t,e,r,n){r||function(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}(t,e),e.pendingcb--,n(),C(t,e)}function A(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var i=e.bufferedRequestCount,o=new Array(i),s=e.corkedRequestsFree;s.entry=r;for(var a=0,h=!0;r;)o[a]=r,r.isBuf||(h=!1),r=r.next,a+=1;o.allBuffers=h,P(t,e,!0,e.length,o,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new n(e),e.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,u=r.encoding,l=r.callback;if(P(t,e,!1,e.objectMode?1:c.length,c,u,l),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function S(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function I(t,e){t._final((function(r){e.pendingcb--,r&&E(t,r),e.prefinished=!0,t.emit("prefinish"),C(t,e)}))}function C(t,e){var r=S(e);if(r&&(function(t,e){e.prefinished||e.finalCalled||("function"!=typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.pendingcb++,e.finalCalled=!0,process.nextTick(I,t,e)))}(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var n=t._readableState;(!n||n.autoDestroy&&n.endEmitted)&&t.destroy()}return r}r(698)(_,a),x.prototype.getBuffer=function(){for(var t=this.bufferedRequest,e=[];t;)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(x.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(o=Function.prototype[Symbol.hasInstance],Object.defineProperty(_,Symbol.hasInstance,{value:function(t){return!!o.call(this,t)||this===_&&t&&t._writableState instanceof x}})):o=function(t){return t instanceof this},_.prototype.pipe=function(){E(this,new g)},_.prototype.write=function(t,e,r){var n,i=this._writableState,o=!1,s=!i.objectMode&&(n=t,h.isBuffer(n)||n instanceof c);return s&&!h.isBuffer(t)&&(t=function(t){return h.from(t)}(t)),"function"==typeof e&&(r=e,e=null),s?e="buffer":e||(e=i.defaultEncoding),"function"!=typeof r&&(r=k),i.ending?function(t,e){var r=new w;E(t,r),process.nextTick(e,r)}(this,r):(s||function(t,e,r,n){var i;return null===r?i=new b:"string"==typeof r||e.objectMode||(i=new d("chunk",["string","Buffer"],r)),!i||(E(t,i),process.nextTick(n,i),!1)}(this,i,t,r))&&(i.pendingcb++,o=function(t,e,r,n,i,o){if(!r){var s=function(t,e,r){return t.objectMode||!1===t.decodeStrings||"string"!=typeof e||(e=h.from(e,r)),e}(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var c=e.length<e.highWaterMark;if(c||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else P(t,e,!1,a,n,i,o);return c}(this,i,s,t,e,r)),o},_.prototype.cork=function(){this._writableState.corked++},_.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||A(this,t))},_.prototype.setDefaultEncoding=function(t){if("string"==typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new v(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(_.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(_.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),_.prototype._write=function(t,e,r){r(new p("_write()"))},_.prototype._writev=null,_.prototype.end=function(t,e,r){var n=this._writableState;return"function"==typeof t?(r=t,t=null,e=null):"function"==typeof e&&(r=e,e=null),null!=t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||function(t,e,r){e.ending=!0,C(t,e),r&&(e.finished?process.nextTick(r):t.once("finish",r)),e.ended=!0,t.writable=!1}(this,n,r),this},Object.defineProperty(_.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(_.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),_.prototype.destroy=u.destroy,_.prototype._undestroy=u.undestroy,_.prototype._destroy=function(t,e){e(t)}},955:(t,e,r)=>{"use strict";var n;function i(t,e,r){return(e=function(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var o=r(238),s=Symbol("lastResolve"),a=Symbol("lastReject"),h=Symbol("error"),c=Symbol("ended"),u=Symbol("lastPromise"),l=Symbol("handlePromise"),f=Symbol("stream");function d(t,e){return{value:t,done:e}}function p(t){var e=t[s];if(null!==e){var r=t[f].read();null!==r&&(t[u]=null,t[s]=null,t[a]=null,e(d(r,!1)))}}function m(t){process.nextTick(p,t)}var g=Object.getPrototypeOf((function(){})),y=Object.setPrototypeOf((i(n={get stream(){return this[f]},next:function(){var t=this,e=this[h];if(null!==e)return Promise.reject(e);if(this[c])return Promise.resolve(d(void 0,!0));if(this[f].destroyed)return new Promise((function(e,r){process.nextTick((function(){t[h]?r(t[h]):e(d(void 0,!0))}))}));var r,n=this[u];if(n)r=new Promise(function(t,e){return function(r,n){t.then((function(){e[c]?r(d(void 0,!0)):e[l](r,n)}),n)}}(n,this));else{var i=this[f].read();if(null!==i)return Promise.resolve(d(i,!1));r=new Promise(this[l])}return this[u]=r,r}},Symbol.asyncIterator,(function(){return this})),i(n,"return",(function(){var t=this;return new Promise((function(e,r){t[f].destroy(null,(function(t){t?r(t):e(d(void 0,!0))}))}))})),n),g);t.exports=function(t){var e,r=Object.create(y,(i(e={},f,{value:t,writable:!0}),i(e,s,{value:null,writable:!0}),i(e,a,{value:null,writable:!0}),i(e,h,{value:null,writable:!0}),i(e,c,{value:t._readableState.endEmitted,writable:!0}),i(e,l,{value:function(t,e){var n=r[f].read();n?(r[u]=null,r[s]=null,r[a]=null,t(d(n,!1))):(r[s]=t,r[a]=e)},writable:!0}),e));return r[u]=null,o(t,(function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=r[a];return null!==e&&(r[u]=null,r[s]=null,r[a]=null,e(t)),void(r[h]=t)}var n=r[s];null!==n&&(r[u]=null,r[s]=null,r[a]=null,n(d(void 0,!0))),r[c]=!0})),t.on("readable",m.bind(null,r)),r}},726:(t,e,r)=>{"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e,r){return(e=a(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,a(n.key),n)}}function a(t){var e=function(t){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}var h=r(287).Buffer,c=r(340).inspect,u=c&&c.custom||"inspect";t.exports=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.head=null,this.tail=null,this.length=0}var e,r;return e=t,(r=[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";for(var e=this.head,r=""+e.data;e=e.next;)r+=t+e.data;return r}},{key:"concat",value:function(t){if(0===this.length)return h.alloc(0);for(var e,r,n,i=h.allocUnsafe(t>>>0),o=this.head,s=0;o;)e=o.data,r=i,n=s,h.prototype.copy.call(e,r,n),s+=o.data.length,o=o.next;return i}},{key:"consume",value:function(t,e){var r;return t<this.head.data.length?(r=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):r=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,r=1,n=e.data;for(t-=n.length;e=e.next;){var i=e.data,o=t>i.length?i.length:t;if(o===i.length?n+=i:n+=i.slice(0,t),0==(t-=o)){o===i.length?(++r,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(t){var e=h.allocUnsafe(t),r=this.head,n=1;for(r.data.copy(e),t-=r.data.length;r=r.next;){var i=r.data,o=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,o),0==(t-=o)){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,e}},{key:u,value:function(t,e){return c(this,i(i({},e),{},{depth:0,customInspect:!1}))}}])&&s(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}()},896:t=>{"use strict";function e(t,e){n(t,e),r(t)}function r(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function n(t,e){t.emit("error",e)}t.exports={destroy:function(t,i){var o=this,s=this._readableState&&this._readableState.destroyed,a=this._writableState&&this._writableState.destroyed;return s||a?(i?i(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,process.nextTick(n,this,t)):process.nextTick(n,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!i&&t?o._writableState?o._writableState.errorEmitted?process.nextTick(r,o):(o._writableState.errorEmitted=!0,process.nextTick(e,o,t)):process.nextTick(e,o,t):i?(process.nextTick(r,o),i(t)):process.nextTick(r,o)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(t,e){var r=t._readableState,n=t._writableState;r&&r.autoDestroy||n&&n.autoDestroy?t.destroy(e):t.emit("error",e)}}},238:(t,e,r)=>{"use strict";var n=r(48).F.ERR_STREAM_PREMATURE_CLOSE;function i(){}t.exports=function t(e,r,o){if("function"==typeof r)return t(e,null,r);r||(r={}),o=function(t){var e=!1;return function(){if(!e){e=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];t.apply(this,n)}}}(o||i);var s=r.readable||!1!==r.readable&&e.readable,a=r.writable||!1!==r.writable&&e.writable,h=function(){e.writable||u()},c=e._writableState&&e._writableState.finished,u=function(){a=!1,c=!0,s||o.call(e)},l=e._readableState&&e._readableState.endEmitted,f=function(){s=!1,l=!0,a||o.call(e)},d=function(t){o.call(e,t)},p=function(){var t;return s&&!l?(e._readableState&&e._readableState.ended||(t=new n),o.call(e,t)):a&&!c?(e._writableState&&e._writableState.ended||(t=new n),o.call(e,t)):void 0},m=function(){e.req.on("finish",u)};return function(t){return t.setHeader&&"function"==typeof t.abort}(e)?(e.on("complete",u),e.on("abort",p),e.req?m():e.on("request",m)):a&&!e._writableState&&(e.on("end",h),e.on("close",h)),e.on("end",f),e.on("finish",u),!1!==r.error&&e.on("error",d),e.on("close",p),function(){e.removeListener("complete",u),e.removeListener("abort",p),e.removeListener("request",m),e.req&&e.req.removeListener("finish",u),e.removeListener("end",h),e.removeListener("close",h),e.removeListener("finish",u),e.removeListener("end",f),e.removeListener("error",d),e.removeListener("close",p)}}},157:t=>{t.exports=function(){throw new Error("Readable.from is not available in the browser")}},758:(t,e,r)=>{"use strict";var n,i=r(48).F,o=i.ERR_MISSING_ARGS,s=i.ERR_STREAM_DESTROYED;function a(t){if(t)throw t}function h(t){t()}function c(t,e){return t.pipe(e)}t.exports=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var u,l=function(t){return t.length?"function"!=typeof t[t.length-1]?a:t.pop():a}(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new o("streams");var f=e.map((function(t,i){var o=i<e.length-1;return function(t,e,i,o){o=function(t){var e=!1;return function(){e||(e=!0,t.apply(void 0,arguments))}}(o);var a=!1;t.on("close",(function(){a=!0})),void 0===n&&(n=r(238)),n(t,{readable:e,writable:i},(function(t){if(t)return o(t);a=!0,o()}));var h=!1;return function(e){if(!a&&!h)return h=!0,function(t){return t.setHeader&&"function"==typeof t.abort}(t)?t.abort():"function"==typeof t.destroy?t.destroy():void o(e||new s("pipe"))}}(t,o,i>0,(function(t){u||(u=t),t&&f.forEach(h),o||(f.forEach(h),l(u))}))}));return e.reduce(c)}},291:(t,e,r)=>{"use strict";var n=r(48).F.ERR_INVALID_OPT_VALUE;t.exports={getHighWaterMark:function(t,e,r,i){var o=function(t,e,r){return null!=t.highWaterMark?t.highWaterMark:e?t[r]:null}(e,i,r);if(null!=o){if(!isFinite(o)||Math.floor(o)!==o||o<0)throw new n(i?r:"highWaterMark",o);return Math.floor(o)}return t.objectMode?16:16384}}},345:(t,e,r)=>{t.exports=r(7).EventEmitter},399:(t,e,r)=>{(e=t.exports=r(412)).Stream=e,e.Readable=e,e.Writable=r(708),e.Duplex=r(382),e.Transform=r(610),e.PassThrough=r(600),e.finished=r(238),e.pipeline=r(758)},858:(t,e)=>{"use strict";function r(t,e,r){if(r>t.length)throw new Error("invalid RLP (safeSlice): end slice of Uint8Array out-of-bounds");return t.slice(e,r)}function n(t){if(0===t[0])throw new Error("invalid RLP: extra zeros");return a(function(t){let e="";for(let r=0;r<t.length;r++)e+=s[t[r]];return e}(t))}function i(t,e){if(t<56)return Uint8Array.from([t+e]);const r=u(t),n=u(e+55+r.length/2);return Uint8Array.from(h(n+r))}function o(t){let e,i,s,a,h;const c=[],u=t[0];if(u<=127)return{data:t.slice(0,1),remainder:t.slice(1)};if(u<=183){if(e=u-127,s=128===u?Uint8Array.from([]):r(t,1,e),2===e&&s[0]<128)throw new Error("invalid RLP encoding: invalid prefix, single byte < 0x80 are not prefixed");return{data:s,remainder:t.slice(e)}}if(u<=191){if(i=u-182,t.length-1<i)throw new Error("invalid RLP: not enough bytes for string length");if(e=n(r(t,1,i)),e<=55)throw new Error("invalid RLP: expected string length to be greater than 55");return s=r(t,i,e+i),{data:s,remainder:t.slice(e+i)}}if(u<=247){for(e=u-191,a=r(t,1,e);a.length;)h=o(a),c.push(h.data),a=h.remainder;return{data:c,remainder:t.slice(e)}}{if(i=u-246,e=n(r(t,1,i)),e<56)throw new Error("invalid RLP: encoded list too short");const s=i+e;if(s>t.length)throw new Error("invalid RLP: total length is larger than the data");for(a=r(t,i,s);a.length;)h=o(a),c.push(h.data),a=h.remainder;return{data:c,remainder:t.slice(s)}}}const s=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function a(t){const e=Number.parseInt(t,16);if(Number.isNaN(e))throw new Error("Invalid byte sequence");return e}function h(t){if("string"!=typeof t)throw new TypeError("hexToBytes: expected string, got "+typeof t);if(t.length%2)throw new Error("hexToBytes: received invalid unpadded hex");const e=new Uint8Array(t.length/2);for(let r=0;r<e.length;r++){const n=2*r;e[r]=a(t.slice(n,n+2))}return e}function c(...t){if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}function u(t){if(t<0)throw new Error("Invalid integer as argument, must be unsigned!");const e=t.toString(16);return e.length%2?`0${e}`:e}function l(t){return t.length>=2&&"0"===t[0]&&"x"===t[1]}function f(t){if(t instanceof Uint8Array)return t;if("string"==typeof t)return l(t)?h((r="string"!=typeof(n=t)?n:l(n)?n.slice(2):n).length%2?`0${r}`:r):(e=t,(new TextEncoder).encode(e));var e,r,n;if("number"==typeof t||"bigint"==typeof t)return t?h(u(t)):Uint8Array.from([]);if(null==t)return Uint8Array.from([]);throw new Error("toBytes: received unsupported type "+typeof t)}const d={encode:function t(e){if(Array.isArray(e)){const r=[];for(let n=0;n<e.length;n++)r.push(t(e[n]));const n=c(...r);return c(i(n.length,192),n)}const r=f(e);return 1===r.length&&r[0]<128?r:c(i(r.length,128),r)},decode:function(t,e=!1){if(!t||0===t.length)return Uint8Array.from([]);const r=o(f(t));if(e)return r;if(0!==r.remainder.length)throw new Error("invalid RLP: remainder must be zero");return r.data}};e.Ay=d},861:(t,e,r)=>{var n=r(287),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,e),e.Buffer=s),o(i,s),s.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},141:(t,e,r)=>{"use strict";var n=r(3).Buffer,i=n.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(n.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=h,this.end=c,e=4;break;case"utf8":this.fillLast=a,e=4;break;case"base64":this.text=u,this.end=l,e=3;break;default:return this.write=f,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(e)}function s(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function a(t){var e=this.lastTotal-this.lastNeed,r=function(t,e){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==r?r:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function h(t,e){if((t.length-e)%2==0){var r=t.toString("utf16le",e);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function c(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function u(t,e){var r=(t.length-e)%3;return 0===r?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function l(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function f(t){return t.toString(this.encoding)}function d(t){return t&&t.length?this.write(t):""}e.I=o,o.prototype.write=function(t){if(0===t.length)return"";var e,r;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""},o.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},o.prototype.text=function(t,e){var r=function(t,e,r){var n=e.length-1;if(n<r)return 0;var i=s(e[n]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--n<r||-2===i?0:(i=s(e[n]))>=0?(i>0&&(t.lastNeed=i-2),i):--n<r||-2===i?0:(i=s(e[n]))>=0?(i>0&&(2===i?i=0:t.lastNeed=i-3),i):0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var n=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)},o.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},3:(t,e,r)=>{var n=r(287),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,e),e.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(t,e,r){if("number"==typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},s.alloc=function(t,e,r){if("number"!=typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"==typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},s.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},643:(t,e,r)=>{function n(t){try{if(!r.g.localStorage)return!1}catch(t){return!1}var e=r.g.localStorage[t];return null!=e&&"true"===String(e).toLowerCase()}t.exports=function(t,e){if(n("noDeprecation"))return t;var r=!1;return function(){if(!r){if(n("throwDeprecation"))throw new Error(e);n("traceDeprecation")?console.trace(e):console.warn(e),r=!0}return t.apply(this,arguments)}}},790:()=>{},340:()=>{},838:()=>{}},e={};function r(n){var i=e[n];if(void 0!==i)return i.exports;var o=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var n={};return(()=>{"use strict";r.r(n),r.d(n,{ABIManager:()=>Vi,ADDRESS_ZERO:()=>Mi,BN:()=>Vr(),BaseBigNumber:()=>c,BaseContract:()=>h,BaseContractMethod:()=>s,BaseToken:()=>Ni,BaseWeb3Client:()=>a,BridgeClient:()=>$i,BridgeUtil:()=>_o,BufferUtil:()=>Kr,Converter:()=>f,DAI_PERMIT_TYPEHASH:()=>Ri,EIP_2612_DOMAIN_TYPEHASH:()=>Oi,EIP_2612_PERMIT_TYPEHASH:()=>Ti,ERROR_TYPE:()=>o,EventBus:()=>m,ExitUtil:()=>no,GasSwapper:()=>lo,HttpRequest:()=>Pi,Keccak:()=>zr,Log_Event_Signature:()=>i,Logger:()=>y,MAX_AMOUNT:()=>Ci,NetworkService:()=>Ui,POSClient:()=>po,Permit:()=>Ei,ProofUtil:()=>xi,RootChain:()=>oo,RootChainManager:()=>ro,UNISWAP_DOMAIN_TYPEHASH:()=>Li,Web3SideChainClient:()=>Ai,ZkEVMWrapper:()=>Bo,ZkEvmBridge:()=>xo,ZkEvmBridgeClient:()=>Zi,ZkEvmClient:()=>So,_GLOBAL_INDEX_MAINNET_FLAG:()=>Fi,default:()=>Io,eventBusPromise:()=>p,mapPromise:()=>v,merge:()=>b,promiseAny:()=>Ii,promiseResolve:()=>Si,resolve:()=>qi,service:()=>Gi,setProofApi:()=>zi,setZkEvmProofApi:()=>ji,throwNotImplemented:()=>Ki,use:()=>d,utils:()=>Yi});var t={};r.r(t),r.d(t,{DO:()=>J,OG:()=>ut,My:()=>Q,Ph:()=>it,lX:()=>ot,Id:()=>ct,fg:()=>dt,qj:()=>ht,aT:()=>nt,aY:()=>Y,lq:()=>st,z:()=>at,Q5:()=>mt});var e,i,o,s=function(t){this.logger=t},a=function(){function t(t){this.logger=t}return t.prototype.getRootHash=function(t,e){return this.sendRPCRequest({jsonrpc:"2.0",method:"eth_getRootHash",params:[Number(t),Number(e)],id:(new Date).getTime()}).then((function(t){return String(t.result)}))},t.prototype.getAccountsUsingRPC_=function(){return this.sendRPCRequest({jsonrpc:"2.0",method:"eth_accounts",params:[],id:(new Date).getTime()}).then((function(t){return t.result}))},t}(),h=function(t,e){this.address=t,this.logger=e},c=function(){function t(){}return t.isBN=function(t){return Ki()},t}(),u=(e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},e(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}),l=function(t){function e(e){return t.call(this)||this}return u(e,t),e.prototype.toString=function(t){return Ki()},e.prototype.toNumber=function(){return Ki()},e.prototype.add=function(t){return Ki()},e.prototype.sub=function(t){return Ki()},e.prototype.mul=function(t){return Ki()},e.prototype.div=function(t){return Ki()},e.prototype.lte=function(t){return Ki()},e.prototype.lt=function(t){return Ki()},e.prototype.gte=function(t){return Ki()},e.prototype.gt=function(t){return Ki()},e.prototype.eq=function(t){return Ki()},e}(c),f=function(){function t(){}return t.toHex=function(t){var e=typeof t;if("number"===e)t=new Yi.BN(t);else if("string"===e){if("0x"===t.slice(0,2))return t;t=new Yi.BN(t)}if(Yi.BN.isBN(t))return"0x"+t.toString(16);throw new Error("Invalid value ".concat(t,", value is not a number."))},t.toBN=function(t){return"string"==typeof t&&"0x"===t.slice(0,2)&&(t=parseInt(t,16)),Yi.BN.isBN(t)||(t=new Yi.BN(t)),t},t}(),d=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n="function"==typeof t?new t:t;return n.setup.apply(n,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([mo],e,!1))},p=function(t){var e=new Promise(t),r=new m;return e.on=r.on.bind(r),e.emit=r.emit.bind(r),e},m=function(){function t(t){this._events={},this._ctx=t}return t.prototype.on=function(t,e){return null==this._events[t]&&(this._events[t]=[]),this._events[t].push(e),this},t.prototype.off=function(t,e){if(this._events[t])if(e){var r=this._events[t].indexOf(e);this._events[t].splice(r,1)}else this._events[t]=[]},t.prototype.emit=function(t){for(var e=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=this._events[t]||[];return Promise.all(i.map((function(t){var n=t.call.apply(t,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([e._ctx],r,!1));return n&&n.then?n:Promise.resolve(n)})))},t.prototype.destroy=function(){this._events=null,this._ctx=null},t}();!function(t){t.Erc20Transfer="0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",t.Erc721Transfer="0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",t.Erc1155Transfer="0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62",t.Erc721BatchTransfer="0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df",t.Erc1155BatchTransfer="0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb",t.Erc721TransferWithMetadata="0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14"}(i||(i={})),function(t){t.AllowedOnRoot="allowed_on_root",t.AllowedOnChild="allowed_on_child",t.Unknown="unknown",t.ProofAPINotSet="proof_api_not_set",t.TransactionOptionNotObject="transation_object_not_object",t.BurnTxNotCheckPointed="burn_tx_not_checkpointed",t.EIP1559NotSupported="eip-1559_not_supported",t.NullSpenderAddress="null_spender_address",t.AllowedOnNonNativeTokens="allowed_on_non_native_token",t.AllowedOnMainnet="allowed_on_mainnet",t.BridgeAdapterNotFound="bridge_adapter_address_not_passed"}(o||(o={}));var g=function(){function t(t,e){this.type=t,this.message=this.getMsg_(e)}return t.prototype.throw=function(){throw this.get()},t.prototype.get=function(){return{message:this.message,type:this.type}},t.prototype.getMsg_=function(t){var e;switch(this.type){case o.AllowedOnChild:e="The action ".concat(t," is allowed only on child token.");break;case o.AllowedOnRoot:e="The action ".concat(t," is allowed only on root token.");break;case o.AllowedOnMainnet:e="The action is allowed only on mainnet chains.";break;case o.ProofAPINotSet:e='Proof api is not set, please set it using "setProofApi"';break;case o.BurnTxNotCheckPointed:e="Burn transaction has not been checkpointed as yet";break;case o.EIP1559NotSupported:e="".concat(t?"Root":"Child"," chain doesn't support eip-1559");break;case o.NullSpenderAddress:e="Please provide spender address.";break;default:this.type||(this.type=o.Unknown),e=this.message}return e},t}(),y=function(){function t(){}return t.prototype.enableLog=function(t){this.isEnabled=!!t},t.prototype.log=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.isEnabled&&console.log.apply(console,t)},t.prototype.error=function(t,e){return new g(t,e)},t}(),b=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Object.assign.apply(Object,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([{}],t,!1))},w=function(t,e){var r=t.map((function(t,r){return e(t,r)}));return Promise.all(r)};function v(t,e,r){void 0===r&&(r={});var n=t.length,i=r.concurrency||n,o=[],s=function(){var r=t.splice(0,i);return w(r,e).then((function(t){return o=o.concat(t),n>o.length?s():Si(o)}))};return s()}function E(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`positive integer expected, not ${t}`)}function k(t,...e){if(!((r=t)instanceof Uint8Array||null!=r&&"object"==typeof r&&"Uint8Array"===r.constructor.name))throw new Error("Uint8Array expected");var r;if(e.length>0&&!e.includes(t.length))throw new Error(`Uint8Array expected of length ${e}, not of length=${t.length}`)}function x(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");E(t.outputLen),E(t.blockLen)}function _(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function P(t,e){k(t);const r=e.outputLen;if(t.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}const B={number:E,bool:function(t){if("boolean"!=typeof t)throw new Error(`boolean expected, not ${t}`)},bytes:k,hash:x,exists:_,output:P},A="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,S=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),I=(t,e)=>t<<32-e|t>>>e,C=68===new Uint8Array(new Uint32Array([287454020]).buffer)[0];function M(t){for(let r=0;r<t.length;r++)t[r]=(e=t[r])<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255;var e}const R=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function T(t){k(t);let e="";for(let r=0;r<t.length;r++)e+=R[t[r]];return e}function O(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}function L(t){return"string"==typeof t&&(t=O(t)),k(t),t}class F{clone(){return this._cloneInto()}}function N(t){const e=e=>t().update(L(e)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function D(t=32){if(A&&"function"==typeof A.getRandomValues)return A.getRandomValues(new Uint8Array(t));throw new Error("crypto.getRandomValues must be defined")}const H=(t,e,r)=>t&e^t&r^e&r;class U extends F{constructor(t,e,r,n){super(),this.blockLen=t,this.outputLen=e,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=S(this.buffer)}update(t){_(this);const{view:e,buffer:r,blockLen:n}=this,i=(t=L(t)).length;for(let o=0;o<i;){const s=Math.min(n-this.pos,i-o);if(s!==n)r.set(t.subarray(o,o+s),this.pos),this.pos+=s,o+=s,this.pos===n&&(this.process(e,0),this.pos=0);else{const e=S(t);for(;n<=i-o;o+=n)this.process(e,o)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){_(this),P(t,this),this.finished=!0;const{buffer:e,view:r,blockLen:n,isLE:i}=this;let{pos:o}=this;e[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>n-o&&(this.process(r,0),o=0);for(let t=o;t<n;t++)e[t]=0;!function(t,e,r,n){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,r,n);const i=BigInt(32),o=BigInt(4294967295),s=Number(r>>i&o),a=Number(r&o),h=n?4:0,c=n?0:4;t.setUint32(e+h,s,n),t.setUint32(e+c,a,n)}(r,n-8,BigInt(8*this.length),i),this.process(r,0);const s=S(t),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const h=a/4,c=this.get();if(h>c.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<h;t++)s.setUint32(4*t,c[t],i)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const r=t.slice(0,e);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:r,length:n,finished:i,destroyed:o,pos:s}=this;return t.length=n,t.pos=s,t.finished=i,t.destroyed=o,n%e&&t.buffer.set(r),t}}const G=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),z=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),j=new Uint32Array(64);class q extends U{constructor(){super(64,32,8,!1),this.A=0|z[0],this.B=0|z[1],this.C=0|z[2],this.D=0|z[3],this.E=0|z[4],this.F=0|z[5],this.G=0|z[6],this.H=0|z[7]}get(){const{A:t,B:e,C:r,D:n,E:i,F:o,G:s,H:a}=this;return[t,e,r,n,i,o,s,a]}set(t,e,r,n,i,o,s,a){this.A=0|t,this.B=0|e,this.C=0|r,this.D=0|n,this.E=0|i,this.F=0|o,this.G=0|s,this.H=0|a}process(t,e){for(let r=0;r<16;r++,e+=4)j[r]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=j[t-15],r=j[t-2],n=I(e,7)^I(e,18)^e>>>3,i=I(r,17)^I(r,19)^r>>>10;j[t]=i+j[t-7]+n+j[t-16]|0}let{A:r,B:n,C:i,D:o,E:s,F:a,G:h,H:c}=this;for(let t=0;t<64;t++){const e=c+(I(s,6)^I(s,11)^I(s,25))+((u=s)&a^~u&h)+G[t]+j[t]|0,l=(I(r,2)^I(r,13)^I(r,22))+H(r,n,i)|0;c=h,h=a,a=s,s=o+e|0,o=i,i=n,n=r,r=e+l|0}var u;r=r+this.A|0,n=n+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,a=a+this.F|0,h=h+this.G|0,c=c+this.H|0,this.set(r,n,i,o,s,a,h,c)}roundClean(){j.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const $=N((()=>new q));class W extends F{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,x(t);const r=L(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const n=this.blockLen,i=new Uint8Array(n);i.set(r.length>n?t.create().update(r).digest():r);for(let t=0;t<i.length;t++)i[t]^=54;this.iHash.update(i),this.oHash=t.create();for(let t=0;t<i.length;t++)i[t]^=106;this.oHash.update(i),i.fill(0)}update(t){return _(this),this.iHash.update(t),this}digestInto(t){_(this),k(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:r,finished:n,destroyed:i,blockLen:o,outputLen:s}=this;return t.finished=n,t.destroyed=i,t.blockLen=o,t.outputLen=s,t.oHash=e._cloneInto(t.oHash),t.iHash=r._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const V=(t,e,r)=>new W(t,e).update(r).digest();V.create=(t,e)=>new W(t,e);const K=BigInt(1),Z=BigInt(2);function Y(t){return t instanceof Uint8Array||null!=t&&"object"==typeof t&&"Uint8Array"===t.constructor.name}function J(t){if(!Y(t))throw new Error("Uint8Array expected")}const X=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function Q(t){J(t);let e="";for(let r=0;r<t.length;r++)e+=X[t[r]];return e}function tt(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);return BigInt(""===t?"0":`0x${t}`)}const et={_0:48,_9:57,_A:65,_F:70,_a:97,_f:102};function rt(t){return t>=et._0&&t<=et._9?t-et._0:t>=et._A&&t<=et._F?t-(et._A-10):t>=et._a&&t<=et._f?t-(et._a-10):void 0}function nt(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let e=0,i=0;e<r;e++,i+=2){const r=rt(t.charCodeAt(i)),o=rt(t.charCodeAt(i+1));if(void 0===r||void 0===o){const e=t[i]+t[i+1];throw new Error('hex string expected, got non-hex character "'+e+'" at index '+i)}n[e]=16*r+o}return n}function it(t){return tt(Q(t))}function ot(t){return J(t),tt(Q(Uint8Array.from(t).reverse()))}function st(t,e){return nt(t.toString(16).padStart(2*e,"0"))}function at(t,e){return st(t,e).reverse()}function ht(t,e,r){let n;if("string"==typeof e)try{n=nt(e)}catch(r){throw new Error(`${t} must be valid hex string, got "${e}". Cause: ${r}`)}else{if(!Y(e))throw new Error(`${t} must be hex string or Uint8Array`);n=Uint8Array.from(e)}const i=n.length;if("number"==typeof r&&i!==r)throw new Error(`${t} expected ${r} bytes, got ${i}`);return n}function ct(...t){let e=0;for(let r=0;r<t.length;r++){const n=t[r];J(n),e+=n.length}const r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}const ut=t=>(Z<<BigInt(t-1))-K,lt=t=>new Uint8Array(t),ft=t=>Uint8Array.from(t);function dt(t,e,r){if("number"!=typeof t||t<2)throw new Error("hashLen must be a number");if("number"!=typeof e||e<2)throw new Error("qByteLen must be a number");if("function"!=typeof r)throw new Error("hmacFn must be a function");let n=lt(t),i=lt(t),o=0;const s=()=>{n.fill(1),i.fill(0),o=0},a=(...t)=>r(i,n,...t),h=(t=lt())=>{i=a(ft([0]),t),n=a(),0!==t.length&&(i=a(ft([1]),t),n=a())},c=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let t=0;const r=[];for(;t<e;){n=a();const e=n.slice();r.push(e),t+=n.length}return ct(...r)};return(t,e)=>{let r;for(s(),h(t);!(r=e(c()));)h();return s(),r}}const pt={bigint:t=>"bigint"==typeof t,function:t=>"function"==typeof t,boolean:t=>"boolean"==typeof t,string:t=>"string"==typeof t,stringOrUint8Array:t=>"string"==typeof t||Y(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>"function"==typeof t&&Number.isSafeInteger(t.outputLen)};function mt(t,e,r={}){const n=(e,r,n)=>{const i=pt[r];if("function"!=typeof i)throw new Error(`Invalid validator "${r}", expected function`);const o=t[e];if(!(n&&void 0===o||i(o,t)))throw new Error(`Invalid param ${String(e)}=${o} (${typeof o}), expected ${r}`)};for(const[t,r]of Object.entries(e))n(t,r,!1);for(const[t,e]of Object.entries(r))n(t,e,!0);return t}const gt=BigInt(0),yt=BigInt(1),bt=BigInt(2),wt=BigInt(3),vt=BigInt(4),Et=BigInt(5),kt=BigInt(8);function xt(t,e){const r=t%e;return r>=gt?r:e+r}function _t(t,e,r){if(r<=gt||e<gt)throw new Error("Expected power/modulo > 0");if(r===yt)return gt;let n=yt;for(;e>gt;)e&yt&&(n=n*t%r),t=t*t%r,e>>=yt;return n}function Pt(t,e,r){let n=t;for(;e-- >gt;)n*=n,n%=r;return n}function Bt(t,e){if(t===gt||e<=gt)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let r=xt(t,e),n=e,i=gt,o=yt,s=yt,a=gt;for(;r!==gt;){const t=n/r,e=n%r,h=i-s*t,c=o-a*t;n=r,r=e,i=s,o=a,s=h,a=c}if(n!==yt)throw new Error("invert: does not exist");return xt(i,e)}BigInt(9),BigInt(16);const At=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function St(t,e){const r=void 0!==e?e:t.toString(2).length;return{nBitLength:r,nByteLength:Math.ceil(r/8)}}function It(t){if("bigint"!=typeof t)throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function Ct(t){const e=It(t);return e+Math.ceil(e/2)}const Mt=BigInt(0),Rt=BigInt(1);function Tt(t){return mt(t.Fp,At.reduce(((t,e)=>(t[e]="function",t)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"})),mt(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...St(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const{Ph:Ot,aT:Lt}=t,Ft={Err:class extends Error{constructor(t=""){super(t)}},_parseInt(t){const{Err:e}=Ft;if(t.length<2||2!==t[0])throw new e("Invalid signature integer tag");const r=t[1],n=t.subarray(2,r+2);if(!r||n.length!==r)throw new e("Invalid signature integer: wrong length");if(128&n[0])throw new e("Invalid signature integer: negative");if(0===n[0]&&!(128&n[1]))throw new e("Invalid signature integer: unnecessary leading zero");return{d:Ot(n),l:t.subarray(r+2)}},toSig(t){const{Err:e}=Ft,r="string"==typeof t?Lt(t):t;J(r);let n=r.length;if(n<2||48!=r[0])throw new e("Invalid signature tag");if(r[1]!==n-2)throw new e("Invalid signature: incorrect length");const{d:i,l:o}=Ft._parseInt(r.subarray(2)),{d:s,l:a}=Ft._parseInt(o);if(a.length)throw new e("Invalid signature: left bytes after parsing");return{r:i,s}},hexFromSig(t){const e=t=>8&Number.parseInt(t[0],16)?"00"+t:t,r=t=>{const e=t.toString(16);return 1&e.length?`0${e}`:e},n=e(r(t.s)),i=e(r(t.r)),o=n.length/2,s=i.length/2,a=r(o),h=r(s);return`30${r(s+o+4)}02${h}${i}02${a}${n}`}},Nt=BigInt(0),Dt=BigInt(1),Ht=(BigInt(2),BigInt(3));function Ut(t){const e=function(t){const e=Tt(t);return mt(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}(t),{Fp:r,n}=e,i=r.BYTES+1,o=2*r.BYTES+1;function s(t){return xt(t,n)}function a(t){return Bt(t,n)}const{ProjectivePoint:h,normPrivateKeyToScalar:c,weierstrassEquation:u,isWithinCurveOrder:l}=function(t){const e=function(t){const e=Tt(t);mt(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof r||"bigint"!=typeof r.beta||"function"!=typeof r.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}(t),{Fp:r}=e,n=e.toBytes||((t,e,n)=>{const i=e.toAffine();return ct(Uint8Array.from([4]),r.toBytes(i.x),r.toBytes(i.y))}),i=e.fromBytes||(t=>{const e=t.subarray(1);return{x:r.fromBytes(e.subarray(0,r.BYTES)),y:r.fromBytes(e.subarray(r.BYTES,2*r.BYTES))}});function o(t){const{a:n,b:i}=e,o=r.sqr(t),s=r.mul(o,t);return r.add(r.add(s,r.mul(t,n)),i)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function s(t){return"bigint"==typeof t&&Nt<t&&t<e.n}function a(t){if(!s(t))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function h(t){const{allowedPrivateKeyLengths:r,nByteLength:n,wrapPrivateKey:i,n:o}=e;if(r&&"bigint"!=typeof t){if(Y(t)&&(t=Q(t)),"string"!=typeof t||!r.includes(t.length))throw new Error("Invalid key");t=t.padStart(2*n,"0")}let s;try{s="bigint"==typeof t?t:it(ht("private key",t,n))}catch(e){throw new Error(`private key must be ${n} bytes, hex or bigint, not ${typeof t}`)}return i&&(s=xt(s,o)),a(s),s}const c=new Map;function u(t){if(!(t instanceof l))throw new Error("ProjectivePoint expected")}class l{constructor(t,e,n){if(this.px=t,this.py=e,this.pz=n,null==t||!r.isValid(t))throw new Error("x required");if(null==e||!r.isValid(e))throw new Error("y required");if(null==n||!r.isValid(n))throw new Error("z required")}static fromAffine(t){const{x:e,y:n}=t||{};if(!t||!r.isValid(e)||!r.isValid(n))throw new Error("invalid affine point");if(t instanceof l)throw new Error("projective point not allowed");const i=t=>r.eql(t,r.ZERO);return i(e)&&i(n)?l.ZERO:new l(e,n,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){const e=r.invertBatch(t.map((t=>t.pz)));return t.map(((t,r)=>t.toAffine(e[r]))).map(l.fromAffine)}static fromHex(t){const e=l.fromAffine(i(ht("pointHex",t)));return e.assertValidity(),e}static fromPrivateKey(t){return l.BASE.multiply(h(t))}_setWindowSize(t){this._WINDOW_SIZE=t,c.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint&&!r.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:t,y:n}=this.toAffine();if(!r.isValid(t)||!r.isValid(n))throw new Error("bad point: x or y not FE");const i=r.sqr(n),s=o(t);if(!r.eql(i,s))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:t}=this.toAffine();if(r.isOdd)return!r.isOdd(t);throw new Error("Field doesn't support isOdd")}equals(t){u(t);const{px:e,py:n,pz:i}=this,{px:o,py:s,pz:a}=t,h=r.eql(r.mul(e,a),r.mul(o,i)),c=r.eql(r.mul(n,a),r.mul(s,i));return h&&c}negate(){return new l(this.px,r.neg(this.py),this.pz)}double(){const{a:t,b:n}=e,i=r.mul(n,Ht),{px:o,py:s,pz:a}=this;let h=r.ZERO,c=r.ZERO,u=r.ZERO,f=r.mul(o,o),d=r.mul(s,s),p=r.mul(a,a),m=r.mul(o,s);return m=r.add(m,m),u=r.mul(o,a),u=r.add(u,u),h=r.mul(t,u),c=r.mul(i,p),c=r.add(h,c),h=r.sub(d,c),c=r.add(d,c),c=r.mul(h,c),h=r.mul(m,h),u=r.mul(i,u),p=r.mul(t,p),m=r.sub(f,p),m=r.mul(t,m),m=r.add(m,u),u=r.add(f,f),f=r.add(u,f),f=r.add(f,p),f=r.mul(f,m),c=r.add(c,f),p=r.mul(s,a),p=r.add(p,p),f=r.mul(p,m),h=r.sub(h,f),u=r.mul(p,d),u=r.add(u,u),u=r.add(u,u),new l(h,c,u)}add(t){u(t);const{px:n,py:i,pz:o}=this,{px:s,py:a,pz:h}=t;let c=r.ZERO,f=r.ZERO,d=r.ZERO;const p=e.a,m=r.mul(e.b,Ht);let g=r.mul(n,s),y=r.mul(i,a),b=r.mul(o,h),w=r.add(n,i),v=r.add(s,a);w=r.mul(w,v),v=r.add(g,y),w=r.sub(w,v),v=r.add(n,o);let E=r.add(s,h);return v=r.mul(v,E),E=r.add(g,b),v=r.sub(v,E),E=r.add(i,o),c=r.add(a,h),E=r.mul(E,c),c=r.add(y,b),E=r.sub(E,c),d=r.mul(p,v),c=r.mul(m,b),d=r.add(c,d),c=r.sub(y,d),d=r.add(y,d),f=r.mul(c,d),y=r.add(g,g),y=r.add(y,g),b=r.mul(p,b),v=r.mul(m,v),y=r.add(y,b),b=r.sub(g,b),b=r.mul(p,b),v=r.add(v,b),g=r.mul(y,v),f=r.add(f,g),g=r.mul(E,v),c=r.mul(w,c),c=r.sub(c,g),g=r.mul(w,y),d=r.mul(E,d),d=r.add(d,g),new l(c,f,d)}subtract(t){return this.add(t.negate())}is0(){return this.equals(l.ZERO)}wNAF(t){return d.wNAFCached(this,c,t,(t=>{const e=r.invertBatch(t.map((t=>t.pz)));return t.map(((t,r)=>t.toAffine(e[r]))).map(l.fromAffine)}))}multiplyUnsafe(t){const n=l.ZERO;if(t===Nt)return n;if(a(t),t===Dt)return this;const{endo:i}=e;if(!i)return d.unsafeLadder(this,t);let{k1neg:o,k1:s,k2neg:h,k2:c}=i.splitScalar(t),u=n,f=n,p=this;for(;s>Nt||c>Nt;)s&Dt&&(u=u.add(p)),c&Dt&&(f=f.add(p)),p=p.double(),s>>=Dt,c>>=Dt;return o&&(u=u.negate()),h&&(f=f.negate()),f=new l(r.mul(f.px,i.beta),f.py,f.pz),u.add(f)}multiply(t){a(t);let n,i,o=t;const{endo:s}=e;if(s){const{k1neg:t,k1:e,k2neg:a,k2:h}=s.splitScalar(o);let{p:c,f:u}=this.wNAF(e),{p:f,f:p}=this.wNAF(h);c=d.constTimeNegate(t,c),f=d.constTimeNegate(a,f),f=new l(r.mul(f.px,s.beta),f.py,f.pz),n=c.add(f),i=u.add(p)}else{const{p:t,f:e}=this.wNAF(o);n=t,i=e}return l.normalizeZ([n,i])[0]}multiplyAndAddUnsafe(t,e,r){const n=l.BASE,i=(t,e)=>e!==Nt&&e!==Dt&&t.equals(n)?t.multiply(e):t.multiplyUnsafe(e),o=i(this,e).add(i(t,r));return o.is0()?void 0:o}toAffine(t){const{px:e,py:n,pz:i}=this,o=this.is0();null==t&&(t=o?r.ONE:r.inv(i));const s=r.mul(e,t),a=r.mul(n,t),h=r.mul(i,t);if(o)return{x:r.ZERO,y:r.ZERO};if(!r.eql(h,r.ONE))throw new Error("invZ was invalid");return{x:s,y:a}}isTorsionFree(){const{h:t,isTorsionFree:r}=e;if(t===Dt)return!0;if(r)return r(l,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:t,clearCofactor:r}=e;return t===Dt?this:r?r(l,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return this.assertValidity(),n(l,this,t)}toHex(t=!0){return Q(this.toRawBytes(t))}}l.BASE=new l(e.Gx,e.Gy,r.ONE),l.ZERO=new l(r.ZERO,r.ONE,r.ZERO);const f=e.nBitLength,d=function(t,e){const r=(t,e)=>{const r=e.negate();return t?r:e},n=t=>({windows:Math.ceil(e/t)+1,windowSize:2**(t-1)});return{constTimeNegate:r,unsafeLadder(e,r){let n=t.ZERO,i=e;for(;r>Mt;)r&Rt&&(n=n.add(i)),i=i.double(),r>>=Rt;return n},precomputeWindow(t,e){const{windows:r,windowSize:i}=n(e),o=[];let s=t,a=s;for(let t=0;t<r;t++){a=s,o.push(a);for(let t=1;t<i;t++)a=a.add(s),o.push(a);s=a.double()}return o},wNAF(e,i,o){const{windows:s,windowSize:a}=n(e);let h=t.ZERO,c=t.BASE;const u=BigInt(2**e-1),l=2**e,f=BigInt(e);for(let t=0;t<s;t++){const e=t*a;let n=Number(o&u);o>>=f,n>a&&(n-=l,o+=Rt);const s=e,d=e+Math.abs(n)-1,p=t%2!=0,m=n<0;0===n?c=c.add(r(p,i[s])):h=h.add(r(m,i[d]))}return{p:h,f:c}},wNAFCached(t,e,r,n){const i=t._WINDOW_SIZE||1;let o=e.get(t);return o||(o=this.precomputeWindow(t,i),1!==i&&e.set(t,n(o))),this.wNAF(i,o,r)}}}(l,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:l,normPrivateKeyToScalar:h,weierstrassEquation:o,isWithinCurveOrder:s}}({...e,toBytes(t,e,n){const i=e.toAffine(),o=r.toBytes(i.x),s=ct;return n?s(Uint8Array.from([e.hasEvenY()?2:3]),o):s(Uint8Array.from([4]),o,r.toBytes(i.y))},fromBytes(t){const e=t.length,n=t[0],s=t.subarray(1);if(e!==i||2!==n&&3!==n){if(e===o&&4===n)return{x:r.fromBytes(s.subarray(0,r.BYTES)),y:r.fromBytes(s.subarray(r.BYTES,2*r.BYTES))};throw new Error(`Point of length ${e} was invalid. Expected ${i} compressed bytes or ${o} uncompressed bytes`)}{const t=it(s);if(!(Nt<(a=t)&&a<r.ORDER))throw new Error("Point is not on curve");const e=u(t);let i;try{i=r.sqrt(e)}catch(t){const e=t instanceof Error?": "+t.message:"";throw new Error("Point is not on curve"+e)}return!(1&~n)!=((i&Dt)===Dt)&&(i=r.neg(i)),{x:t,y:i}}var a}}),f=t=>Q(st(t,e.nByteLength));function d(t){return t>n>>Dt}const p=(t,e,r)=>it(t.slice(e,r));class m{constructor(t,e,r){this.r=t,this.s=e,this.recovery=r,this.assertValidity()}static fromCompact(t){const r=e.nByteLength;return t=ht("compactSignature",t,2*r),new m(p(t,0,r),p(t,r,2*r))}static fromDER(t){const{r:e,s:r}=Ft.toSig(ht("DER",t));return new m(e,r)}assertValidity(){if(!l(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!l(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(t){return new m(this.r,this.s,t)}recoverPublicKey(t){const{r:n,s:i,recovery:o}=this,c=w(ht("msgHash",t));if(null==o||![0,1,2,3].includes(o))throw new Error("recovery id invalid");const u=2===o||3===o?n+e.n:n;if(u>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const l=1&o?"03":"02",d=h.fromHex(l+f(u)),p=a(u),m=s(-c*p),g=s(i*p),y=h.BASE.multiplyAndAddUnsafe(d,m,g);if(!y)throw new Error("point at infinify");return y.assertValidity(),y}hasHighS(){return d(this.s)}normalizeS(){return this.hasHighS()?new m(this.r,s(-this.s),this.recovery):this}toDERRawBytes(){return nt(this.toDERHex())}toDERHex(){return Ft.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return nt(this.toCompactHex())}toCompactHex(){return f(this.r)+f(this.s)}}const g={isValidPrivateKey(t){try{return c(t),!0}catch(t){return!1}},normPrivateKeyToScalar:c,randomPrivateKey:()=>{const t=Ct(e.n);return function(t,e,r=!1){const n=t.length,i=It(e),o=Ct(e);if(n<16||n<o||n>1024)throw new Error(`expected ${o}-1024 bytes of input, got ${n}`);const s=xt(r?it(t):ot(t),e-yt)+yt;return r?at(s,i):st(s,i)}(e.randomBytes(t),e.n)},precompute:(t=8,e=h.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)};function y(t){const e=Y(t),r="string"==typeof t,n=(e||r)&&t.length;return e?n===i||n===o:r?n===2*i||n===2*o:t instanceof h}const b=e.bits2int||function(t){const r=it(t),n=8*t.length-e.nBitLength;return n>0?r>>BigInt(n):r},w=e.bits2int_modN||function(t){return s(b(t))},v=ut(e.nBitLength);function E(t){if("bigint"!=typeof t)throw new Error("bigint expected");if(!(Nt<=t&&t<v))throw new Error(`bigint expected < 2^${e.nBitLength}`);return st(t,e.nByteLength)}const k={lowS:e.lowS,prehash:!1},x={lowS:e.lowS,prehash:!1};return h.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return h.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,r=!0){if(y(t))throw new Error("first arg must be private key");if(!y(e))throw new Error("second arg must be public key");return h.fromHex(e).multiply(c(t)).toRawBytes(r)},sign:function(t,n,i=k){const{seed:o,k2sig:u}=function(t,n,i=k){if(["recovered","canonical"].some((t=>t in i)))throw new Error("sign() legacy options not supported");const{hash:o,randomBytes:u}=e;let{lowS:f,prehash:p,extraEntropy:g}=i;null==f&&(f=!0),t=ht("msgHash",t),p&&(t=ht("prehashed msgHash",o(t)));const y=w(t),v=c(n),x=[E(v),E(y)];if(null!=g&&!1!==g){const t=!0===g?u(r.BYTES):g;x.push(ht("extraEntropy",t))}const _=ct(...x),P=y;return{seed:_,k2sig:function(t){const e=b(t);if(!l(e))return;const r=a(e),n=h.BASE.multiply(e).toAffine(),i=s(n.x);if(i===Nt)return;const o=s(r*s(P+i*v));if(o===Nt)return;let c=(n.x===i?0:2)|Number(n.y&Dt),u=o;return f&&d(o)&&(u=function(t){return d(t)?s(-t):t}(o),c^=1),new m(i,u,c)}}}(t,n,i),f=e;return dt(f.hash.outputLen,f.nByteLength,f.hmac)(o,u)},verify:function(t,r,n,i=x){const o=t;if(r=ht("msgHash",r),n=ht("publicKey",n),"strict"in i)throw new Error("options.strict was renamed to lowS");const{lowS:c,prehash:u}=i;let l,f;try{if("string"==typeof o||Y(o))try{l=m.fromDER(o)}catch(t){if(!(t instanceof Ft.Err))throw t;l=m.fromCompact(o)}else{if("object"!=typeof o||"bigint"!=typeof o.r||"bigint"!=typeof o.s)throw new Error("PARSE");{const{r:t,s:e}=o;l=new m(t,e)}}f=h.fromHex(n)}catch(t){if("PARSE"===t.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(c&&l.hasHighS())return!1;u&&(r=e.hash(r));const{r:d,s:p}=l,g=w(r),y=a(p),b=s(g*y),v=s(d*y),E=h.BASE.multiplyAndAddUnsafe(f,b,v)?.toAffine();return!!E&&s(E.x)===d},ProjectivePoint:h,Signature:m,utils:g}}function Gt(t){return{hash:t,hmac:(e,...r)=>V(t,e,function(...t){let e=0;for(let r=0;r<t.length;r++){const n=t[r];k(n),e+=n.length}const r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}(...r)),randomBytes:D}}BigInt(4);const zt=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),jt=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),qt=BigInt(1),$t=BigInt(2),Wt=(t,e)=>(t+e/$t)/e;const Vt=function(t,e,r=!1,n={}){if(t<=gt)throw new Error(`Expected Field ORDER > 0, got ${t}`);const{nBitLength:i,nByteLength:o}=St(t,e);if(o>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=function(t){if(t%vt===wt){const e=(t+yt)/vt;return function(t,r){const n=t.pow(r,e);if(!t.eql(t.sqr(n),r))throw new Error("Cannot find square root");return n}}if(t%kt===Et){const e=(t-Et)/kt;return function(t,r){const n=t.mul(r,bt),i=t.pow(n,e),o=t.mul(r,i),s=t.mul(t.mul(o,bt),i),a=t.mul(o,t.sub(s,t.ONE));if(!t.eql(t.sqr(a),r))throw new Error("Cannot find square root");return a}}return function(t){const e=(t-yt)/bt;let r,n,i;for(r=t-yt,n=0;r%bt===gt;r/=bt,n++);for(i=bt;i<t&&_t(i,e,t)!==t-yt;i++);if(1===n){const e=(t+yt)/vt;return function(t,r){const n=t.pow(r,e);if(!t.eql(t.sqr(n),r))throw new Error("Cannot find square root");return n}}const o=(r+yt)/bt;return function(t,s){if(t.pow(s,e)===t.neg(t.ONE))throw new Error("Cannot find square root");let a=n,h=t.pow(t.mul(t.ONE,i),r),c=t.pow(s,o),u=t.pow(s,r);for(;!t.eql(u,t.ONE);){if(t.eql(u,t.ZERO))return t.ZERO;let e=1;for(let r=t.sqr(u);e<a&&!t.eql(r,t.ONE);e++)r=t.sqr(r);const r=t.pow(h,yt<<BigInt(a-e-1));h=t.sqr(r),c=t.mul(c,r),u=t.mul(u,h),a=e}return c}}(t)}(t),a=Object.freeze({ORDER:t,BITS:i,BYTES:o,MASK:ut(i),ZERO:gt,ONE:yt,create:e=>xt(e,t),isValid:e=>{if("bigint"!=typeof e)throw new Error("Invalid field element: expected bigint, got "+typeof e);return gt<=e&&e<t},is0:t=>t===gt,isOdd:t=>(t&yt)===yt,neg:e=>xt(-e,t),eql:(t,e)=>t===e,sqr:e=>xt(e*e,t),add:(e,r)=>xt(e+r,t),sub:(e,r)=>xt(e-r,t),mul:(e,r)=>xt(e*r,t),pow:(t,e)=>function(t,e,r){if(r<gt)throw new Error("Expected power > 0");if(r===gt)return t.ONE;if(r===yt)return e;let n=t.ONE,i=e;for(;r>gt;)r&yt&&(n=t.mul(n,i)),i=t.sqr(i),r>>=yt;return n}(a,t,e),div:(e,r)=>xt(e*Bt(r,t),t),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:e=>Bt(e,t),sqrt:n.sqrt||(t=>s(a,t)),invertBatch:t=>function(t,e){const r=new Array(e.length),n=e.reduce(((e,n,i)=>t.is0(n)?e:(r[i]=e,t.mul(e,n))),t.ONE),i=t.inv(n);return e.reduceRight(((e,n,i)=>t.is0(n)?e:(r[i]=t.mul(e,r[i]),t.mul(e,n))),i),r}(a,t),cmov:(t,e,r)=>r?e:t,toBytes:t=>r?at(t,o):st(t,o),fromBytes:t=>{if(t.length!==o)throw new Error(`Fp.fromBytes: expected ${o}, got ${t.length}`);return r?ot(t):it(t)}});return Object.freeze(a)}(zt,void 0,void 0,{sqrt:function(t){const e=zt,r=BigInt(3),n=BigInt(6),i=BigInt(11),o=BigInt(22),s=BigInt(23),a=BigInt(44),h=BigInt(88),c=t*t*t%e,u=c*c*t%e,l=Pt(u,r,e)*u%e,f=Pt(l,r,e)*u%e,d=Pt(f,$t,e)*c%e,p=Pt(d,i,e)*d%e,m=Pt(p,o,e)*p%e,g=Pt(m,a,e)*m%e,y=Pt(g,h,e)*g%e,b=Pt(y,a,e)*m%e,w=Pt(b,r,e)*u%e,v=Pt(w,s,e)*p%e,E=Pt(v,n,e)*c%e,k=Pt(E,$t,e);if(!Vt.eql(Vt.sqr(k),t))throw new Error("Cannot find square root");return k}}),Kt=function(t,e){const r=e=>Ut({...t,...Gt(e)});return Object.freeze({...r(e),create:r})}({a:BigInt(0),b:BigInt(7),Fp:Vt,n:jt,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=jt,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-qt*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),o=r,s=BigInt("0x100000000000000000000000000000000"),a=Wt(o*t,e),h=Wt(-n*t,e);let c=xt(t-a*r-h*i,e),u=xt(-a*n-h*o,e);const l=c>s,f=u>s;if(l&&(c=e-c),f&&(u=e-u),c>s||u>s)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:l,k1:c,k2neg:f,k2:u}}}},$);function Zt(t){if(!(t instanceof Uint8Array))throw new TypeError("bytesToUtf8 expected Uint8Array, got "+typeof t);return(new TextDecoder).decode(t)}function Yt(t,e){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}function Jt(t){return e=>(B.bytes(e),t(e))}function Xt(t){if("string"!=typeof t)throw new Error("[isHexPrefixed] input must be type 'string', received type "+typeof t);return"0"===t[0]&&"x"===t[1]}BigInt(0),Kt.ProjectivePoint,B.bool,B.bytes,(()=>{const t="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,e="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);e&&!t&&e("crypto")})();function Qt(t){let e=t;if("string"!=typeof e)throw new Error("[padToEven] value must be type 'string', received "+typeof e);return e.length%2&&(e=`0${e}`),e}function te(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/)||void 0!==e&&e>0&&t.length!==2+2*e)}const ee=function(t){if(!(t instanceof Uint8Array))throw new Error(`This method only supports Uint8Array but input was: ${t}`)},re=BigInt(0),ne=T,ie={},oe={};for(let t=0;t<16;t++){const e=t,r=16*t,n=t.toString(16).toLowerCase();oe[n]=e,oe[n.toUpperCase()]=e,ie[n]=r,ie[n.toUpperCase()]=r}function se(t){const e=t.length,r=new Uint8Array(e/2);for(let n=0;n<e;n+=2)r[n/2]=ie[t[n]]+oe[t[n+1]];return r}const ae=t=>{if("0x"===t.slice(0,2))throw new Error("hex string is prefixed with 0x, should be unprefixed");return se(Qt(t))},he=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0"))),ce=t=>{let e="0x";if(void 0===t||0===t.length)return e;for(const r of t)e+=he[r];return e},ue=[];for(let t=0;t<=65535;t++)ue[t]=BigInt(t);const le=(t,e=!1)=>{e&&t.reverse();const r=ce(t);return"0x"===r?re:4===r.length?ue[t[0]]:6===r.length?ue[256*t[0]+t[1]]:BigInt(r)},fe=t=>{if("string"!=typeof t)throw new Error(`hex argument type ${typeof t} must be of type string`);if(!/^0x[0-9a-fA-F]*$/.test(t))throw new Error(`Input must be a 0x-prefixed hexadecimal string, got ${t}`);return(t=t.slice(2)).length%2!=0&&(t=Qt(t)),se(t)},de=t=>{if(!Number.isSafeInteger(t)||t<0)throw new Error(`Received an invalid integer type: ${t}`);return`0x${t.toString(16)}`},pe=t=>{const e=de(t);return fe(e)},me=(t,e=!1)=>{const r=we("0x"+Qt(t.toString(16)));return e?r.reverse():r},ge=t=>new Uint8Array(t),ye=(t,e)=>(ee(t),((t,e,r)=>r?t.length<e?new Uint8Array([...t,...ge(e-t.length)]):t.subarray(0,e):t.length<e?new Uint8Array([...ge(e-t.length),...t]):t.subarray(-e))(t,e,!1)),be=t=>(ee(t),(t=>{let e=t[0];for(;t.length>0&&"0"===e.toString();)e=(t=t.slice(1))[0];return t})(t)),we=t=>{if(null==t)return new Uint8Array;if(Array.isArray(t)||t instanceof Uint8Array)return Uint8Array.from(t);if("string"==typeof t){if(!te(t))throw new Error(`Cannot convert string to Uint8Array. toBytes only supports 0x-prefixed hex strings and this string was given: ${t}`);return fe(t)}if("number"==typeof t)return pe(t);if("bigint"==typeof t){if(t<re)throw new Error(`Cannot convert negative bigint to Uint8Array. Given: ${t}`);let e=t.toString(16);return e.length%2&&(e="0"+e),ae(e)}if(void 0!==t.toBytes)return t.toBytes();throw new Error("invalid type")},ve=t=>{for(const[e,r]of Object.entries(t))if(void 0!==r&&r.length>0&&0===r[0])throw new Error(`${e} cannot have leading zeroes, received: ${ce(r)}`)},Ee=t=>"0x"+t.toString(16),ke=t=>be(me(t)),xe=(...t)=>{if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r},_e=BigInt("0xffffffffffffffff"),Pe=BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),Be=(BigInt("115792089237316195423570985008687907853269984665640564039457584007913129639935"),Kt.CURVE.n,Kt.CURVE.n/BigInt(2)),Ae=(BigInt("0x10000000000000000000000000000000000000000000000000000000000000000"),fe("0xc5d2460186f7233c927e7db2dcc703c0e500b653ca82273b7bfad8045d85a470"),fe("0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347")),Se=fe("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),Ie=Uint8Array.from([128]),Ce=(BigInt(-1),BigInt(0)),Me=BigInt(1),Re=BigInt(2),Te=(BigInt(3),BigInt(7)),Oe=BigInt(8),Le=BigInt(27);function Fe(t,e,r){if(r>t.length)throw new Error("invalid RLP (safeSlice): end slice of Uint8Array out-of-bounds");return t.slice(e,r)}function Ne(t){if(0===t[0])throw new Error("invalid RLP: extra zeros");return Ge(function(t){let e="";for(let r=0;r<t.length;r++)e+=Ue[t[r]];return e}(t))}function De(t,e){if(t<56)return Uint8Array.from([t+e]);const r=qe(t),n=qe(e+55+r.length/2);return Uint8Array.from(ze(n+r))}function He(t){let e,r,n,i,o;const s=[],a=t[0];if(a<=127)return{data:t.slice(0,1),remainder:t.subarray(1)};if(a<=183){if(e=a-127,n=128===a?Uint8Array.from([]):Fe(t,1,e),2===e&&n[0]<128)throw new Error("invalid RLP encoding: invalid prefix, single byte < 0x80 are not prefixed");return{data:n,remainder:t.subarray(e)}}if(a<=191){if(r=a-182,t.length-1<r)throw new Error("invalid RLP: not enough bytes for string length");if(e=Ne(Fe(t,1,r)),e<=55)throw new Error("invalid RLP: expected string length to be greater than 55");return n=Fe(t,r,e+r),{data:n,remainder:t.subarray(e+r)}}if(a<=247){for(e=a-191,i=Fe(t,1,e);i.length;)o=He(i),s.push(o.data),i=o.remainder;return{data:s,remainder:t.subarray(e)}}{if(r=a-246,e=Ne(Fe(t,1,r)),e<56)throw new Error("invalid RLP: encoded list too short");const n=r+e;if(n>t.length)throw new Error("invalid RLP: total length is larger than the data");for(i=Fe(t,r,n);i.length;)o=He(i),s.push(o.data),i=o.remainder;return{data:s,remainder:t.subarray(n)}}}BigInt(28),BigInt(31),BigInt(32),BigInt(64),BigInt(128),BigInt(255),BigInt(256),BigInt(96),BigInt(100),BigInt(160),BigInt(224),BigInt(7922816251426434e13),BigInt(1461501637330903e33),BigInt(2695994666715064e52),BigInt(1e9);const Ue=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function Ge(t){const e=Number.parseInt(t,16);if(Number.isNaN(e))throw new Error("Invalid byte sequence");return e}function ze(t){if("string"!=typeof t)throw new TypeError("hexToBytes: expected string, got "+typeof t);if(t.length%2)throw new Error("hexToBytes: received invalid unpadded hex");const e=new Uint8Array(t.length/2);for(let r=0;r<e.length;r++){const n=2*r;e[r]=Ge(t.slice(n,n+2))}return e}function je(...t){if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),r=new Uint8Array(e);for(let e=0,n=0;e<t.length;e++){const i=t[e];r.set(i,n),n+=i.length}return r}function qe(t){if(t<0)throw new Error("Invalid integer as argument, must be unsigned!");const e=t.toString(16);return e.length%2?`0${e}`:e}function $e(t){return t.length>=2&&"0"===t[0]&&"x"===t[1]}function We(t){if(t instanceof Uint8Array)return t;if("string"==typeof t)return $e(t)?ze((r="string"!=typeof(n=t)?n:$e(n)?n.slice(2):n).length%2?`0${r}`:r):(e=t,(new TextEncoder).encode(e));var e,r,n;if("number"==typeof t||"bigint"==typeof t)return t?ze(qe(t)):Uint8Array.from([]);if(null==t)return Uint8Array.from([]);throw new Error("toBytes: received unsupported type "+typeof t)}const Ve=function t(e){if(Array.isArray(e)){const r=[];let n=0;for(let i=0;i<e.length;i++){const o=t(e[i]);r.push(o),n+=o.length}return je(De(n,192),...r)}const r=We(e);return 1===r.length&&r[0]<128?r:je(De(r.length,128),r)},Ke=function(t,e=!1){if(null==t||0===t.length)return Uint8Array.from([]);const r=He(We(t));if(e)return{data:r.data,remainder:r.remainder.slice()};if(0!==r.remainder.length)throw new Error("invalid RLP: remainder must be zero");return r.data},Ze=BigInt(2**32-1),Ye=BigInt(32);function Je(t,e=!1){return e?{h:Number(t&Ze),l:Number(t>>Ye&Ze)}:{h:0|Number(t>>Ye&Ze),l:0|Number(t&Ze)}}function Xe(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:o,l:s}=Je(t[i],e);[r[i],n[i]]=[o,s]}return[r,n]}const Qe=[],tr=[],er=[],rr=BigInt(0),nr=BigInt(1),ir=BigInt(2),or=BigInt(7),sr=BigInt(256),ar=BigInt(113);for(let t=0,e=nr,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],Qe.push(2*(5*n+r)),tr.push((t+1)*(t+2)/2%64);let i=rr;for(let t=0;t<7;t++)e=(e<<nr^(e>>or)*ar)%sr,e&ir&&(i^=nr<<(nr<<BigInt(t))-nr);er.push(i)}const[hr,cr]=Xe(er,!0),ur=(t,e,r)=>r>32?((t,e,r)=>e<<r-32|t>>>64-r)(t,e,r):((t,e,r)=>t<<r|e>>>32-r)(t,e,r),lr=(t,e,r)=>r>32?((t,e,r)=>t<<r-32|e>>>64-r)(t,e,r):((t,e,r)=>e<<r|t>>>32-r)(t,e,r);class fr extends F{constructor(t,e,r,n=!1,i=24){if(super(),this.blockLen=t,this.suffix=e,this.outputLen=r,this.enableXOF=n,this.rounds=i,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,E(r),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");var o;this.state=new Uint8Array(200),this.state32=(o=this.state,new Uint32Array(o.buffer,o.byteOffset,Math.floor(o.byteLength/4)))}keccak(){C||M(this.state32),function(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let e=0;e<10;e++)r[e]=t[e]^t[e+10]^t[e+20]^t[e+30]^t[e+40];for(let e=0;e<10;e+=2){const n=(e+8)%10,i=(e+2)%10,o=r[i],s=r[i+1],a=ur(o,s,1)^r[n],h=lr(o,s,1)^r[n+1];for(let r=0;r<50;r+=10)t[e+r]^=a,t[e+r+1]^=h}let e=t[2],i=t[3];for(let r=0;r<24;r++){const n=tr[r],o=ur(e,i,n),s=lr(e,i,n),a=Qe[r];e=t[a],i=t[a+1],t[a]=o,t[a+1]=s}for(let e=0;e<50;e+=10){for(let n=0;n<10;n++)r[n]=t[e+n];for(let n=0;n<10;n++)t[e+n]^=~r[(n+2)%10]&r[(n+4)%10]}t[0]^=hr[n],t[1]^=cr[n]}r.fill(0)}(this.state32,this.rounds),C||M(this.state32),this.posOut=0,this.pos=0}update(t){_(this);const{blockLen:e,state:r}=this,n=(t=L(t)).length;for(let i=0;i<n;){const o=Math.min(e-this.pos,n-i);for(let e=0;e<o;e++)r[this.pos++]^=t[i++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:e,pos:r,blockLen:n}=this;t[r]^=e,128&e&&r===n-1&&this.keccak(),t[n-1]^=128,this.keccak()}writeInto(t){_(this,!1),k(t),this.finish();const e=this.state,{blockLen:r}=this;for(let n=0,i=t.length;n<i;){this.posOut>=r&&this.keccak();const o=Math.min(r-this.posOut,i-n);t.set(e.subarray(this.posOut,this.posOut+o),n),this.posOut+=o,n+=o}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return E(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(P(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){const{blockLen:e,suffix:r,outputLen:n,rounds:i,enableXOF:o}=this;return t||(t=new fr(e,r,n,o,i)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=i,t.suffix=r,t.outputLen=n,t.enableXOF=o,t.destroyed=this.destroyed,t}}const dr=(t,e,r)=>N((()=>new fr(e,t,r))),pr=dr(1,144,28),mr=dr(1,136,32),gr=dr(1,104,48),yr=dr(1,72,64),br=Jt(pr),wr=(()=>{const t=Jt(mr);return t.create=mr.create,t})(),vr=Jt(gr),Er=Jt(yr),kr=function(t,e=!1){if(ee(t),e&&64!==t.length&&(t=Kt.ProjectivePoint.fromHex(t).toRawBytes(!1).slice(1)),64!==t.length)throw new Error("Expected pubKey to be of length 64");return wr(t).subarray(-20)},xr=kr;new Uint8Array(0);class _r{constructor(t){if(20!==t.length)throw new Error("Invalid address length");this.bytes=t}static zero(){return new _r(ge(20))}static fromString(t){if(!function(t){try{!function(t){if("string"!=typeof t)throw new Error(`This method only supports strings but input was: ${t}`)}(t)}catch(t){return!1}return/^0x[0-9a-fA-F]{40}$/.test(t)}(t))throw new Error("Invalid address");return new _r(we(t))}static fromPublicKey(t){if(!(t instanceof Uint8Array))throw new Error("Public key should be Uint8Array");const e=kr(t);return new _r(e)}static fromPrivateKey(t){if(!(t instanceof Uint8Array))throw new Error("Private key should be Uint8Array");const e=function(t){return xr(function(t){return ee(t),Kt.ProjectivePoint.fromPrivateKey(t).toRawBytes(!1).slice(1)}(t))}(t);return new _r(e)}static generate(t,e){if("bigint"!=typeof e)throw new Error("Expected nonce to be a bigint");return new _r(function(t,e){return ee(t),ee(e),le(e)===Ce?wr(Ve([t,Uint8Array.from([])])).subarray(-20):wr(Ve([t,e])).subarray(-20)}(t.bytes,me(e)))}static generate2(t,e,r){if(!(e instanceof Uint8Array))throw new Error("Expected salt to be a Uint8Array");if(!(r instanceof Uint8Array))throw new Error("Expected initCode to be a Uint8Array");return new _r(function(t,e,r){if(ee(t),ee(e),ee(r),20!==t.length)throw new Error("Expected from to be of length 20");if(32!==e.length)throw new Error("Expected salt to be of length 32");return wr(xe(fe("0xff"),t,e,wr(r))).subarray(-20)}(t.bytes,e,r))}equals(t){return Yt(this.bytes,t.bytes)}isZero(){return this.equals(_r.zero())}isPrecompileOrSystemAddress(){const t=le(this.bytes),e=Ce,r=BigInt("0xffff");return t>=e&&t<=r}toString(){return ce(this.bytes)}toBytes(){return new Uint8Array(this.bytes)}}var Pr,Br,Ar,Sr;function Ir(t,e){if(null===t)return null;if(void 0===t)return;if("string"==typeof t&&!te(t))throw new Error(`A string must be provided with a 0x-prefix, given: ${t}`);if("number"==typeof t&&!Number.isSafeInteger(t))throw new Error("The provided number is greater than MAX_SAFE_INTEGER (please use an alternative input type)");const r=we(t);switch(e){case Ar.Uint8Array:return r;case Ar.BigInt:return le(r);case Ar.Number:{const t=le(r);if(t>BigInt(Number.MAX_SAFE_INTEGER))throw new Error("The provided number is greater than MAX_SAFE_INTEGER (please use an alternative output type)");return Number(t)}case Ar.PrefixedHexString:return ce(r);default:throw new Error("unknown outputType")}}!function(t){t.String="string",t.Bytes="view",t.Number="number"}(Pr||(Pr={})),function(t){t.String="string",t.Bytes="view",t.JSON="json"}(Br||(Br={})),(Sr=Ar||(Ar={}))[Sr.Number=0]="Number",Sr[Sr.BigInt=1]="BigInt",Sr[Sr.Uint8Array=2]="Uint8Array",Sr[Sr.PrefixedHexString=3]="PrefixedHexString";class Cr{constructor(t,e,r,n){this.index=t,this.validatorIndex=e,this.address=r,this.amount=n}static fromWithdrawalData(t){const{index:e,validatorIndex:r,address:n,amount:i}=t,o=Ir(e,Ar.BigInt),s=Ir(r,Ar.BigInt),a=n instanceof _r?n:new _r(we(n)),h=Ir(i,Ar.BigInt);return new Cr(o,s,a,h)}static fromValuesArray(t){if(4!==t.length)throw Error(`Invalid withdrawalArray length expected=4 actual=${t.length}`);const[e,r,n,i]=t;return Cr.fromWithdrawalData({index:e,validatorIndex:r,address:n,amount:i})}static toBytesArray(t){const{index:e,validatorIndex:r,address:n,amount:i}=t;return[Ir(e,Ar.BigInt)===Ce?new Uint8Array:Ir(e,Ar.Uint8Array),Ir(r,Ar.BigInt)===Ce?new Uint8Array:Ir(r,Ar.Uint8Array),n instanceof _r?n.bytes:Ir(n,Ar.Uint8Array),Ir(i,Ar.BigInt)===Ce?new Uint8Array:Ir(i,Ar.Uint8Array)]}raw(){return Cr.toBytesArray(this)}toValue(){return{index:this.index,validatorIndex:this.validatorIndex,address:this.address.bytes,amount:this.amount}}toJSON(){return{index:Ee(this.index),validatorIndex:Ee(this.validatorIndex),address:ce(this.address.bytes),amount:Ee(this.amount)}}}function Mr(t,e,r){const n=Kt.sign(t,e),i=n.toCompactRawBytes();return{r:i.slice(0,32),s:i.slice(32,64),v:void 0===r?BigInt(n.recovery+27):BigInt(n.recovery+35)+BigInt(r)*Re}}const Rr=function(t,e,r,n,i){const o=xe(ye(r,32),ye(n,32)),s=function(t,e){return t===Ce||t===Me?t:void 0===e?t-Le:t-(e*Re+BigInt(35))}(e,i);if(!function(t){return t===Ce||t===Me}(s))throw new Error("Invalid signature v value");return Kt.Signature.fromCompact(o).addRecoveryBit(Number(s)).recoverPublicKey(t).toRawBytes(!1).slice(1)};var Tr=r(7);const Or=Jt($),Lr=131072;function Fr(t){const e=new Uint8Array(131072);for(let r=0;r<4096;r++){const n=new Uint8Array(32);n.set(t.subarray(31*r,31*(r+1)),0),e.set(n,32*r)}return e}const Nr=(t,e)=>{const r=new Uint8Array(32);return r.set([e],0),r.set(Or(t).subarray(1),1),r};class Dr{constructor(){this.permits=1,this.promiseResolverQueue=[]}async acquire(){return this.permits>0?(this.permits-=1,Promise.resolve(!0)):new Promise((t=>this.promiseResolverQueue.push(t)))}release(){if(this.permits+=1,this.permits>1&&this.promiseResolverQueue.length>0)console.warn("Lock.permits should never be > 0 when there is someone waiting.");else if(1===this.permits&&this.promiseResolverQueue.length>0){this.permits-=1;const t=this.promiseResolverQueue.shift();t&&t(!0)}}}class Hr{constructor(t){this._database=t??new Map}async get(t){const e=t instanceof Uint8Array?ne(t):t.toString();return this._database.get(e)}async put(t,e){const r=t instanceof Uint8Array?ne(t):t.toString();this._database.set(r,e)}async del(t){const e=t instanceof Uint8Array?ne(t):t.toString();this._database.delete(e)}async batch(t){for(const e of t)"del"===e.type&&await this.del(e.key),"put"===e.type&&await this.put(e.key,e.value)}shallowCopy(){return new Hr(this._database)}open(){return Promise.resolve()}}const Ur=async(t,e)=>{const r=JSON.stringify({method:e.method,params:e.params,jsonrpc:"2.0",id:1}),n=await fetch(t,{headers:{"content-type":"application/json"},method:"POST",body:r});if(!n.ok)throw new Error(`JSONRpcError: ${JSON.stringify({method:e.method,status:n.status,message:await n.text().catch((()=>"Could not parse error message likely because of a network error"))},null,2)}`);return(await n.json()).result},Gr=t=>{if("string"==typeof t)return t;if("object"==typeof t&&void 0!==t._getConnection)return t._getConnection().url;throw new Error("Must provide valid provider URL or Web3Provider")};var zr=function(){function t(){}return t.assertIsBuffer=function(t){if(!Buffer.isBuffer(t)){var e="This method only supports Buffer but input was: ".concat(t);throw new Error(e)}},t.keccak=function(e,r){switch(void 0===r&&(r=256),t.assertIsBuffer(e),r){case 224:return Buffer.from(br(e));case 256:return Buffer.from(wr(e));case 384:return Buffer.from(vr(e));case 512:return Buffer.from(Er(e));default:throw new Error("Invald algorithm: keccak".concat(r))}},t.keccak256=function(e){return t.keccak(e)},t}(),jr=r(861),qr=zr.keccak256,$r=function(){function t(t){if(void 0===t&&(t=[]),t.length<1)throw new Error("Atleast 1 leaf needed");var e=Math.ceil(Math.log(t.length)/Math.log(2));if(e>20)throw new Error("Depth must be 20 or less");this.leaves=t.concat(Array.from(Array(Math.pow(2,e)-t.length),(function(){return ge(32)}))),this.layers=[this.leaves],this.createHashes(this.leaves)}return t.prototype.createHashes=function(t){if(1===t.length)return!1;for(var e=[],r=0;r<t.length;r+=2){var n=t[r],i=t[r+1],o=jr.Buffer.concat([n,i]);e.push(qr(o))}t.length%2==1&&e.push(t[t.length-1]),this.layers.push(e),this.createHashes(e)},t.prototype.getLeaves=function(){return this.leaves},t.prototype.getLayers=function(){return this.layers},t.prototype.getRoot=function(){return this.layers[this.layers.length-1][0]},t.prototype.getProof=function(t){for(var e=-1,r=0;r<this.leaves.length;r++)0===jr.Buffer.compare(t,this.leaves[r])&&(e=r);var n=[];if(e<=this.getLeaves().length){var i=void 0;for(r=0;r<this.layers.length-1;r++)i=e%2==0?e+1:e-1,e=Math.floor(e/2),n.push(this.layers[r][i])}return n},t.prototype.verify=function(t,e,r,n){if(!Array.isArray(n)||!t||!r)return!1;for(var i=t,o=0;o<n.length;o++){var s=n[o];i=qr(e%2==0?jr.Buffer.concat([i,s]):jr.Buffer.concat([s,i])),e=Math.floor(e/2)}return 0===jr.Buffer.compare(i,r)},t}(),Wr=r(404),Vr=r.n(Wr),Kr=function(){function t(){}return t.padToEven=function(t){var e=t;if("string"!=typeof e)throw new Error("[padToEven] value must be type 'string', received ".concat(typeof e));return e.length%2&&(e="0".concat(e)),e},t.isHexPrefixed=function(t){if("string"!=typeof t)throw new Error("[isHexPrefixed] input must be type 'string', received type ".concat(typeof t));return"0"===t[0]&&"x"===t[1]},t.isHexString=function(t,e){return!("string"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/)||e&&t.length!==2+2*e)},t.intToHex=function(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("Received an invalid integer type: ".concat(t));return"0x".concat(t.toString(16))},t.stripHexPrefix=function(e){if("string"!=typeof e)throw new Error("[stripHexPrefix] input must be type 'string', received ".concat(typeof e));return t.isHexPrefixed(e)?e.slice(2):e},t.intToBuffer=function(e){var r=t.intToHex(e);return Buffer.from(t.padToEven(r.slice(2)),"hex")},t.toBuffer=function(e){if(null==e)return Buffer.allocUnsafe(0);if(Buffer.isBuffer(e))return Buffer.from(e);if(Array.isArray(e)||e instanceof Uint8Array)return Buffer.from(e);if("string"==typeof e){if(!t.isHexString(e))throw new Error("Cannot convert string to buffer. toBuffer only supports 0x-prefixed hex strings and this string was given: ".concat(e));return Buffer.from(t.padToEven(t.stripHexPrefix(e)),"hex")}if("number"==typeof e)return t.intToBuffer(e);if(Vr().isBN(e)){if(e.isNeg())throw new Error("Cannot convert negative BN to buffer. Given: ".concat(e));return e.toArrayLike(Buffer)}if(e.toArray)return Buffer.from(e.toArray());if(e.toBuffer)return Buffer.from(e.toBuffer());throw new Error("invalid type")},t.bufferToHex=function(e){return"0x"+(e=t.toBuffer(e)).toString("hex")},t}(),Zr=r(858);const Yr="object"==typeof performance&&performance&&"function"==typeof performance.now?performance:Date,Jr=new Set,Xr="object"==typeof process&&process?process:{},Qr=(t,e,r,n)=>{"function"==typeof Xr.emitWarning?Xr.emitWarning(t,e,r,n):console.error(`[${r}] ${e}: ${t}`)};let tn=globalThis.AbortController,en=globalThis.AbortSignal;if(void 0===tn){en=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(t,e){this._onabort.push(e)}},tn=class{constructor(){e()}signal=new en;abort(t){if(!this.signal.aborted){this.signal.reason=t,this.signal.aborted=!0;for(const e of this.signal._onabort)e(t);this.signal.onabort?.(t)}}};let t="1"!==Xr.env?.LRU_CACHE_IGNORE_AC_WARNING;const e=()=>{t&&(t=!1,Qr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}Symbol("type");const rn=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),nn=t=>rn(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?on:null:null;class on extends Array{constructor(t){super(t),this.fill(0)}}class sn{heap;length;static#t=!1;static create(t){const e=nn(t);if(!e)return[];sn.#t=!0;const r=new sn(t,e);return sn.#t=!1,r}constructor(t,e){if(!sn.#t)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}}class an{#e;#r;#n;#i;#o;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#s;#a;#h;#c;#u;#l;#f;#d;#p;#m;#g;#y;#b;#w;#v;#E;#k;static unsafeExposeInternals(t){return{starts:t.#b,ttls:t.#w,sizes:t.#y,keyMap:t.#h,keyList:t.#c,valList:t.#u,next:t.#l,prev:t.#f,get head(){return t.#d},get tail(){return t.#p},free:t.#m,isBackgroundFetch:e=>t.#x(e),backgroundFetch:(e,r,n,i)=>t.#_(e,r,n,i),moveToTail:e=>t.#P(e),indexes:e=>t.#B(e),rindexes:e=>t.#A(e),isStale:e=>t.#S(e)}}get max(){return this.#e}get maxSize(){return this.#r}get calculatedSize(){return this.#a}get size(){return this.#s}get fetchMethod(){return this.#o}get dispose(){return this.#n}get disposeAfter(){return this.#i}constructor(t){const{max:e=0,ttl:r,ttlResolution:n=1,ttlAutopurge:i,updateAgeOnGet:o,updateAgeOnHas:s,allowStale:a,dispose:h,disposeAfter:c,noDisposeOnSet:u,noUpdateTTL:l,maxSize:f=0,maxEntrySize:d=0,sizeCalculation:p,fetchMethod:m,noDeleteOnFetchRejection:g,noDeleteOnStaleGet:y,allowStaleOnFetchRejection:b,allowStaleOnFetchAbort:w,ignoreFetchAbort:v}=t;if(0!==e&&!rn(e))throw new TypeError("max option must be a nonnegative integer");const E=e?nn(e):Array;if(!E)throw new Error("invalid max value: "+e);if(this.#e=e,this.#r=f,this.maxEntrySize=d||this.#r,this.sizeCalculation=p,this.sizeCalculation){if(!this.#r&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if("function"!=typeof this.sizeCalculation)throw new TypeError("sizeCalculation set to non-function")}if(void 0!==m&&"function"!=typeof m)throw new TypeError("fetchMethod must be a function if specified");if(this.#o=m,this.#E=!!m,this.#h=new Map,this.#c=new Array(e).fill(void 0),this.#u=new Array(e).fill(void 0),this.#l=new E(e),this.#f=new E(e),this.#d=0,this.#p=0,this.#m=sn.create(e),this.#s=0,this.#a=0,"function"==typeof h&&(this.#n=h),"function"==typeof c?(this.#i=c,this.#g=[]):(this.#i=void 0,this.#g=void 0),this.#v=!!this.#n,this.#k=!!this.#i,this.noDisposeOnSet=!!u,this.noUpdateTTL=!!l,this.noDeleteOnFetchRejection=!!g,this.allowStaleOnFetchRejection=!!b,this.allowStaleOnFetchAbort=!!w,this.ignoreFetchAbort=!!v,0!==this.maxEntrySize){if(0!==this.#r&&!rn(this.#r))throw new TypeError("maxSize must be a positive integer if specified");if(!rn(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#I()}if(this.allowStale=!!a,this.noDeleteOnStaleGet=!!y,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!s,this.ttlResolution=rn(n)||0===n?n:1,this.ttlAutopurge=!!i,this.ttl=r||0,this.ttl){if(!rn(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#C()}if(0===this.#e&&0===this.ttl&&0===this.#r)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#e&&!this.#r){const t="LRU_CACHE_UNBOUNDED";(t=>!Jr.has(t))(t)&&(Jr.add(t),Qr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",t,an))}}getRemainingTTL(t){return this.#h.has(t)?1/0:0}#C(){const t=new on(this.#e),e=new on(this.#e);this.#w=t,this.#b=e,this.#M=(r,n,i=Yr.now())=>{if(e[r]=0!==n?i:0,t[r]=n,0!==n&&this.ttlAutopurge){const t=setTimeout((()=>{this.#S(r)&&this.delete(this.#c[r])}),n+1);t.unref&&t.unref()}},this.#R=r=>{e[r]=0!==t[r]?Yr.now():0},this.#T=(i,o)=>{if(t[o]){const s=t[o],a=e[o];if(!s||!a)return;i.ttl=s,i.start=a,i.now=r||n();const h=i.now-a;i.remainingTTL=s-h}};let r=0;const n=()=>{const t=Yr.now();if(this.ttlResolution>0){r=t;const e=setTimeout((()=>r=0),this.ttlResolution);e.unref&&e.unref()}return t};this.getRemainingTTL=i=>{const o=this.#h.get(i);if(void 0===o)return 0;const s=t[o],a=e[o];return s&&a?s-((r||n())-a):1/0},this.#S=i=>{const o=e[i],s=t[i];return!!s&&!!o&&(r||n())-o>s}}#R=()=>{};#T=()=>{};#M=()=>{};#S=()=>!1;#I(){const t=new on(this.#e);this.#a=0,this.#y=t,this.#O=e=>{this.#a-=t[e],t[e]=0},this.#L=(t,e,r,n)=>{if(this.#x(e))return 0;if(!rn(r)){if(!n)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if("function"!=typeof n)throw new TypeError("sizeCalculation must be a function");if(r=n(e,t),!rn(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return r},this.#F=(e,r,n)=>{if(t[e]=r,this.#r){const r=this.#r-t[e];for(;this.#a>r;)this.#N(!0)}this.#a+=t[e],n&&(n.entrySize=r,n.totalCalculatedSize=this.#a)}}#O=t=>{};#F=(t,e,r)=>{};#L=(t,e,r,n)=>{if(r||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#B({allowStale:t=this.allowStale}={}){if(this.#s)for(let e=this.#p;this.#D(e)&&(!t&&this.#S(e)||(yield e),e!==this.#d);)e=this.#f[e]}*#A({allowStale:t=this.allowStale}={}){if(this.#s)for(let e=this.#d;this.#D(e)&&(!t&&this.#S(e)||(yield e),e!==this.#p);)e=this.#l[e]}#D(t){return void 0!==t&&this.#h.get(this.#c[t])===t}*entries(){for(const t of this.#B())void 0===this.#u[t]||void 0===this.#c[t]||this.#x(this.#u[t])||(yield[this.#c[t],this.#u[t]])}*rentries(){for(const t of this.#A())void 0===this.#u[t]||void 0===this.#c[t]||this.#x(this.#u[t])||(yield[this.#c[t],this.#u[t]])}*keys(){for(const t of this.#B()){const e=this.#c[t];void 0===e||this.#x(this.#u[t])||(yield e)}}*rkeys(){for(const t of this.#A()){const e=this.#c[t];void 0===e||this.#x(this.#u[t])||(yield e)}}*values(){for(const t of this.#B())void 0===this.#u[t]||this.#x(this.#u[t])||(yield this.#u[t])}*rvalues(){for(const t of this.#A())void 0===this.#u[t]||this.#x(this.#u[t])||(yield this.#u[t])}[Symbol.iterator](){return this.entries()}find(t,e={}){for(const r of this.#B()){const n=this.#u[r],i=this.#x(n)?n.__staleWhileFetching:n;if(void 0!==i&&t(i,this.#c[r],this))return this.get(this.#c[r],e)}}forEach(t,e=this){for(const r of this.#B()){const n=this.#u[r],i=this.#x(n)?n.__staleWhileFetching:n;void 0!==i&&t.call(e,i,this.#c[r],this)}}rforEach(t,e=this){for(const r of this.#A()){const n=this.#u[r],i=this.#x(n)?n.__staleWhileFetching:n;void 0!==i&&t.call(e,i,this.#c[r],this)}}purgeStale(){let t=!1;for(const e of this.#A({allowStale:!0}))this.#S(e)&&(this.delete(this.#c[e]),t=!0);return t}info(t){const e=this.#h.get(t);if(void 0===e)return;const r=this.#u[e],n=this.#x(r)?r.__staleWhileFetching:r;if(void 0===n)return;const i={value:n};if(this.#w&&this.#b){const t=this.#w[e],r=this.#b[e];if(t&&r){const e=t-(Yr.now()-r);i.ttl=e,i.start=Date.now()}}return this.#y&&(i.size=this.#y[e]),i}dump(){const t=[];for(const e of this.#B({allowStale:!0})){const r=this.#c[e],n=this.#u[e],i=this.#x(n)?n.__staleWhileFetching:n;if(void 0===i||void 0===r)continue;const o={value:i};if(this.#w&&this.#b){o.ttl=this.#w[e];const t=Yr.now()-this.#b[e];o.start=Math.floor(Date.now()-t)}this.#y&&(o.size=this.#y[e]),t.unshift([r,o])}return t}load(t){this.clear();for(const[e,r]of t){if(r.start){const t=Date.now()-r.start;r.start=Yr.now()-t}this.set(e,r.value,r)}}set(t,e,r={}){if(void 0===e)return this.delete(t),this;const{ttl:n=this.ttl,start:i,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:s=this.sizeCalculation,status:a}=r;let{noUpdateTTL:h=this.noUpdateTTL}=r;const c=this.#L(t,e,r.size||0,s);if(this.maxEntrySize&&c>this.maxEntrySize)return a&&(a.set="miss",a.maxEntrySizeExceeded=!0),this.delete(t),this;let u=0===this.#s?void 0:this.#h.get(t);if(void 0===u)u=0===this.#s?this.#p:0!==this.#m.length?this.#m.pop():this.#s===this.#e?this.#N(!1):this.#s,this.#c[u]=t,this.#u[u]=e,this.#h.set(t,u),this.#l[this.#p]=u,this.#f[u]=this.#p,this.#p=u,this.#s++,this.#F(u,c,a),a&&(a.set="add"),h=!1;else{this.#P(u);const r=this.#u[u];if(e!==r){if(this.#E&&this.#x(r)){r.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:e}=r;void 0===e||o||(this.#v&&this.#n?.(e,t,"set"),this.#k&&this.#g?.push([e,t,"set"]))}else o||(this.#v&&this.#n?.(r,t,"set"),this.#k&&this.#g?.push([r,t,"set"]));if(this.#O(u),this.#F(u,c,a),this.#u[u]=e,a){a.set="replace";const t=r&&this.#x(r)?r.__staleWhileFetching:r;void 0!==t&&(a.oldValue=t)}}else a&&(a.set="update")}if(0===n||this.#w||this.#C(),this.#w&&(h||this.#M(u,n,i),a&&this.#T(a,u)),!o&&this.#k&&this.#g){const t=this.#g;let e;for(;e=t?.shift();)this.#i?.(...e)}return this}pop(){try{for(;this.#s;){const t=this.#u[this.#d];if(this.#N(!0),this.#x(t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(void 0!==t)return t}}finally{if(this.#k&&this.#g){const t=this.#g;let e;for(;e=t?.shift();)this.#i?.(...e)}}}#N(t){const e=this.#d,r=this.#c[e],n=this.#u[e];return this.#E&&this.#x(n)?n.__abortController.abort(new Error("evicted")):(this.#v||this.#k)&&(this.#v&&this.#n?.(n,r,"evict"),this.#k&&this.#g?.push([n,r,"evict"])),this.#O(e),t&&(this.#c[e]=void 0,this.#u[e]=void 0,this.#m.push(e)),1===this.#s?(this.#d=this.#p=0,this.#m.length=0):this.#d=this.#l[e],this.#h.delete(r),this.#s--,e}has(t,e={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:n}=e,i=this.#h.get(t);if(void 0!==i){const t=this.#u[i];if(this.#x(t)&&void 0===t.__staleWhileFetching)return!1;if(!this.#S(i))return r&&this.#R(i),n&&(n.has="hit",this.#T(n,i)),!0;n&&(n.has="stale",this.#T(n,i))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:r=this.allowStale}=e,n=this.#h.get(t);if(void 0===n||!r&&this.#S(n))return;const i=this.#u[n];return this.#x(i)?i.__staleWhileFetching:i}#_(t,e,r,n){const i=void 0===e?void 0:this.#u[e];if(this.#x(i))return i;const o=new tn,{signal:s}=r;s?.addEventListener("abort",(()=>o.abort(s.reason)),{signal:o.signal});const a={signal:o.signal,options:r,context:n},h=(n,i=!1)=>{const{aborted:s}=o.signal,h=r.ignoreFetchAbort&&void 0!==n;if(r.status&&(s&&!i?(r.status.fetchAborted=!0,r.status.fetchError=o.signal.reason,h&&(r.status.fetchAbortIgnored=!0)):r.status.fetchResolved=!0),s&&!h&&!i)return c(o.signal.reason);const l=u;return this.#u[e]===u&&(void 0===n?l.__staleWhileFetching?this.#u[e]=l.__staleWhileFetching:this.delete(t):(r.status&&(r.status.fetchUpdated=!0),this.set(t,n,a.options))),n},c=n=>{const{aborted:i}=o.signal,s=i&&r.allowStaleOnFetchAbort,a=s||r.allowStaleOnFetchRejection,h=a||r.noDeleteOnFetchRejection,c=u;if(this.#u[e]===u&&(h&&void 0!==c.__staleWhileFetching?s||(this.#u[e]=c.__staleWhileFetching):this.delete(t)),a)return r.status&&void 0!==c.__staleWhileFetching&&(r.status.returnedStale=!0),c.__staleWhileFetching;if(c.__returned===c)throw n};r.status&&(r.status.fetchDispatched=!0);const u=new Promise(((e,n)=>{const s=this.#o?.(t,i,a);s&&s instanceof Promise&&s.then((t=>e(void 0===t?void 0:t)),n),o.signal.addEventListener("abort",(()=>{r.ignoreFetchAbort&&!r.allowStaleOnFetchAbort||(e(void 0),r.allowStaleOnFetchAbort&&(e=t=>h(t,!0)))}))})).then(h,(t=>(r.status&&(r.status.fetchRejected=!0,r.status.fetchError=t),c(t)))),l=Object.assign(u,{__abortController:o,__staleWhileFetching:i,__returned:void 0});return void 0===e?(this.set(t,l,{...a.options,status:void 0}),e=this.#h.get(t)):this.#u[e]=l,l}#x(t){if(!this.#E)return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof tn}async fetch(t,e={}){const{allowStale:r=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:s=this.noDisposeOnSet,size:a=0,sizeCalculation:h=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:u=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:l=this.allowStaleOnFetchRejection,ignoreFetchAbort:f=this.ignoreFetchAbort,allowStaleOnFetchAbort:d=this.allowStaleOnFetchAbort,context:p,forceRefresh:m=!1,status:g,signal:y}=e;if(!this.#E)return g&&(g.fetch="get"),this.get(t,{allowStale:r,updateAgeOnGet:n,noDeleteOnStaleGet:i,status:g});const b={allowStale:r,updateAgeOnGet:n,noDeleteOnStaleGet:i,ttl:o,noDisposeOnSet:s,size:a,sizeCalculation:h,noUpdateTTL:c,noDeleteOnFetchRejection:u,allowStaleOnFetchRejection:l,allowStaleOnFetchAbort:d,ignoreFetchAbort:f,status:g,signal:y};let w=this.#h.get(t);if(void 0===w){g&&(g.fetch="miss");const e=this.#_(t,w,b,p);return e.__returned=e}{const e=this.#u[w];if(this.#x(e)){const t=r&&void 0!==e.__staleWhileFetching;return g&&(g.fetch="inflight",t&&(g.returnedStale=!0)),t?e.__staleWhileFetching:e.__returned=e}const i=this.#S(w);if(!m&&!i)return g&&(g.fetch="hit"),this.#P(w),n&&this.#R(w),g&&this.#T(g,w),e;const o=this.#_(t,w,b,p),s=void 0!==o.__staleWhileFetching&&r;return g&&(g.fetch=i?"stale":"refresh",s&&i&&(g.returnedStale=!0)),s?o.__staleWhileFetching:o.__returned=o}}get(t,e={}){const{allowStale:r=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:i=this.noDeleteOnStaleGet,status:o}=e,s=this.#h.get(t);if(void 0!==s){const e=this.#u[s],a=this.#x(e);return o&&this.#T(o,s),this.#S(s)?(o&&(o.get="stale"),a?(o&&r&&void 0!==e.__staleWhileFetching&&(o.returnedStale=!0),r?e.__staleWhileFetching:void 0):(i||this.delete(t),o&&r&&(o.returnedStale=!0),r?e:void 0)):(o&&(o.get="hit"),a?e.__staleWhileFetching:(this.#P(s),n&&this.#R(s),e))}o&&(o.get="miss")}#H(t,e){this.#f[e]=t,this.#l[t]=e}#P(t){t!==this.#p&&(t===this.#d?this.#d=this.#l[t]:this.#H(this.#f[t],this.#l[t]),this.#H(this.#p,t),this.#p=t)}delete(t){let e=!1;if(0!==this.#s){const r=this.#h.get(t);if(void 0!==r)if(e=!0,1===this.#s)this.clear();else{this.#O(r);const e=this.#u[r];if(this.#x(e)?e.__abortController.abort(new Error("deleted")):(this.#v||this.#k)&&(this.#v&&this.#n?.(e,t,"delete"),this.#k&&this.#g?.push([e,t,"delete"])),this.#h.delete(t),this.#c[r]=void 0,this.#u[r]=void 0,r===this.#p)this.#p=this.#f[r];else if(r===this.#d)this.#d=this.#l[r];else{const t=this.#f[r];this.#l[t]=this.#l[r];const e=this.#l[r];this.#f[e]=this.#f[r]}this.#s--,this.#m.push(r)}}if(this.#k&&this.#g?.length){const t=this.#g;let e;for(;e=t?.shift();)this.#i?.(...e)}return e}clear(){for(const t of this.#A({allowStale:!0})){const e=this.#u[t];if(this.#x(e))e.__abortController.abort(new Error("deleted"));else{const r=this.#c[t];this.#v&&this.#n?.(e,r,"delete"),this.#k&&this.#g?.push([e,r,"delete"])}}if(this.#h.clear(),this.#u.fill(void 0),this.#c.fill(void 0),this.#w&&this.#b&&(this.#w.fill(0),this.#b.fill(0)),this.#y&&this.#y.fill(0),this.#d=0,this.#p=0,this.#m.length=0,this.#a=0,this.#s=0,this.#k&&this.#g){const t=this.#g;let e;for(;e=t?.shift();)this.#i?.(...e)}}}class hn{constructor(t){this._stats={cache:{reads:0,hits:0,writes:0},db:{reads:0,hits:0,writes:0}},this.db=t.db,this.cacheSize=t.cacheSize??0,this.valueEncoding=t.valueEncoding??Br.String,this.checkpoints=[],this.cacheSize>0&&(this._cache=new an({max:this.cacheSize,updateAgeOnGet:!0}))}setCheckpoints(t){this.checkpoints=[];for(let e=0;e<t.length;e++)this.checkpoints.push({root:t[e].root,keyValueMap:new Map(t[e].keyValueMap)})}hasCheckpoints(){return this.checkpoints.length>0}checkpoint(t){this.checkpoints.push({keyValueMap:new Map,root:t})}async commit(){const{keyValueMap:t}=this.checkpoints.pop();if(this.hasCheckpoints()){const e=this.checkpoints[this.checkpoints.length-1].keyValueMap;for(const[r,n]of t.entries())e.set(r,n)}else{const e=[];for(const[r,n]of t.entries())void 0===n?e.push({type:"del",key:ae(r)}):e.push({type:"put",key:ae(r),value:n});await this.batch(e)}}async revert(){const{root:t}=this.checkpoints.pop();return t}async get(t){const e=ne(t);if(void 0!==this._cache){const t=this._cache.get(e);if(this._stats.cache.reads+=1,void 0!==t)return this._stats.cache.hits+=1,t}for(let t=this.checkpoints.length-1;t>=0;t--)if(this.checkpoints[t].keyValueMap.has(e))return this.checkpoints[t].keyValueMap.get(e);const r=await this.db.get(e,{keyEncoding:Pr.String,valueEncoding:this.valueEncoding});this._stats.db.reads+=1,void 0!==r&&(this._stats.db.hits+=1);const n=void 0!==r?r instanceof Uint8Array?r:ae(r):void 0;return this._cache?.set(e,n),this.hasCheckpoints()&&this.checkpoints[this.checkpoints.length-1].keyValueMap.set(e,n),n}async put(t,e){const r=ne(t);if(this.hasCheckpoints())this.checkpoints[this.checkpoints.length-1].keyValueMap.set(r,e);else{const t=this.valueEncoding===Br.Bytes?e:ne(e);await this.db.put(r,t,{keyEncoding:Pr.String,valueEncoding:this.valueEncoding}),this._stats.db.writes+=1,void 0!==this._cache&&(this._cache.set(r,e),this._stats.cache.writes+=1)}}async del(t){const e=ne(t);this.hasCheckpoints()?this.checkpoints[this.checkpoints.length-1].keyValueMap.set(e,void 0):(await this.db.del(e,{keyEncoding:Pr.String}),this._stats.db.writes+=1,void 0!==this._cache&&(this._cache.set(e,void 0),this._stats.cache.writes+=1))}async batch(t){if(this.hasCheckpoints())for(const e of t)"put"===e.type?await this.put(e.key,e.value):"del"===e.type&&await this.del(e.key);else{const e=t.map((t=>{const e={key:ne(t.key),value:"put"===t.type?t.value:void 0,type:t.type,opts:{...t.opts,valueEncoding:this.valueEncoding}};return"put"===t.type&&this.valueEncoding===Br.String&&(e.value=ne(e.value)),e}));await this.db.batch(e)}}stats(t=!0){const e={...this._stats,size:this._cache?.size??0};return t&&(this._stats={cache:{reads:0,hits:0,writes:0},db:{reads:0,hits:0,writes:0}}),e}shallowCopy(){return new hn({db:this.db,cacheSize:this.cacheSize,valueEncoding:this.valueEncoding})}open(){return Promise.resolve()}}class cn{constructor(){this._branches=new Array(16).fill(null),this._value=null}static fromArray(t){const e=new cn;return e._branches=t.slice(0,16),e._value=t[16],e}value(t){return null!=t&&(this._value=t),this._value&&this._value.length>0?this._value:null}setBranch(t,e){this._branches[t]=e}raw(){return[...this._branches,this._value]}serialize(){return Ve(this.raw())}getBranch(t){const e=this._branches[t];return null!==e&&e.length>0?e:null}getChildren(){const t=[];for(let e=0;e<16;e++){const r=this._branches[e];null!==r&&r.length>0&&t.push([e,r])}return t}}function un(t,e){return t.length%2?t.unshift(1):(t.unshift(0),t.unshift(0)),e&&(t[0]+=2),t}function ln(t){const e=we(t),r=[];for(let t=0;t<e.length;t++){let n=2*t;r[n]=e[t]>>4,++n,r[n]=e[t]%16}return r}function fn(t){const e=new Uint8Array(t.length/2);for(let r=0;r<e.length;r++){let n=2*r;e[r]=(t[n]<<4)+t[++n]}return e}function dn(t,e){const r=Math.min(t.length,e.length);let n=0;for(let i=0;i<r;i++){if(t[i]<e[i]){n=-1;break}if(t[i]>e[i]){n=1;break}}return 0===n&&(t.length<e.length?n=-1:t.length>e.length&&(n=1)),n}function pn(t,e){let r=0;for(;t[r]===e[r]&&t.length>r;)r++;return r}class mn{constructor(t,e,r){this._nibbles=t,this._value=e,this._terminator=r}static decodeKey(t){return(e=t)[0]%2?e.slice(1):e.slice(2);var e}key(t){return void 0!==t&&(this._nibbles=t),this._nibbles.slice(0)}keyLength(){return this._nibbles.length}value(t){return void 0!==t&&(this._value=t),this._value}encodedKey(){return un(this._nibbles.slice(0),this._terminator)}raw(){return[fn(this.encodedKey()),this._value]}serialize(){return Ve(this.raw())}}class gn extends mn{constructor(t,e){super(t,e,!1)}static encodeKey(t){return un(t,!1)}}class yn extends mn{constructor(t,e){super(t,e,!0)}static encodeKey(t){return un(t,!0)}}function bn(t){if(17===t.length)return cn.fromArray(t);if(2===t.length){const e=ln(t[0]);return e[0]>1?new yn(yn.decodeKey(e),t[1]):new gn(gn.decodeKey(e),t[1])}throw new Error("Invalid node")}function wn(t){return Array.isArray(t)&&!(t instanceof Uint8Array)}var vn=r(833);const En=O("__root__");async function*kn(t,e=[],r=async(t,e)=>{},n=async(t,e)=>!0,i=new Set){if(!Yt(t,this.EMPTY_TRIE_ROOT))try{const o=await this.lookupNode(t);if(void 0===o||i.has(T(this.hash(o.serialize()))))return;if(i.add(T(this.hash(o.serialize()))),await r(o,e),await n(o,e)&&(yield{node:o,currentKey:e}),o instanceof cn)for(const[t,s]of o._branches.entries()){const o=[...e,t],a=s instanceof Uint8Array?s:this.hash(Ve(s));yield*kn.bind(this)(a,o,r,n,i)}else if(o instanceof gn){const t=o.value(),s=[...e,...o._nibbles];yield*kn.bind(this)(t,s,r,n,i)}}catch(t){return}}var xn=r(399);class _n extends xn.Readable{constructor(t){super({objectMode:!0}),this.trie=t,this._started=!1}async _read(){if(!this._started){this._started=!0;try{await(async(t,e)=>{await t.walkTrie(t.root(),(async(t,r,n,i)=>{let o=n;r instanceof yn?(o=n.concat(r.key()),e(0,r,o,i)):r instanceof cn&&r.value()?e(0,r,o,i):null!==r&&i.allChildren(r,n)}))})(this.trie,(async(t,e,r,n)=>{null!==e&&(this.push({key:fn(r),value:e.value()}),n.allChildren(e,r))}))}catch(t){if("Missing node in DB"!==t.message)throw t}this.push(null)}}}class Pn{constructor(t){this.maxPoolSize=t,this.currentPoolSize=0,this.queue=[]}executeOrQueue(t,e){this.currentPoolSize<this.maxPoolSize?(this.currentPoolSize++,e((()=>{if(this.currentPoolSize--,this.queue.length>0){this.queue.sort(((t,e)=>e.priority-t.priority));const t=this.queue.shift();this.executeOrQueue(t.priority,t.fn)}}))):this.queue.push({priority:t,fn:e})}finished(){return 0===this.currentPoolSize}}class Bn{constructor(t,e,r){this.onNode=t,this.taskExecutor=new Pn(r),this.trie=e,this.resolve=()=>{},this.reject=()=>{}}static async newWalk(t,e,r,n){const i=new Bn(t,e,n??500);await i.startWalk(r)}async startWalk(t){return new Promise((async(e,r)=>{let n;this.resolve=e,this.reject=r;try{n=await this.trie.lookupNode(t)}catch(t){return this.reject(t)}this.processNode(t,n,[])}))}allChildren(t,e=[]){if(t instanceof yn)return;let r;if(t instanceof gn?r=[[t.key(),t.value()]]:t instanceof cn&&(r=t.getChildren().map((t=>[[t[0]],t[1]]))),r)for(const t of r){const r=t[0],n=t[1],i=e.concat(r),o=i.length;this.pushNodeToQueue(n,i,o)}}pushNodeToQueue(t,e=[],r){this.taskExecutor.executeOrQueue(r??e.length,(async r=>{let n;try{n=await this.trie.lookupNode(t)}catch(t){return this.reject(t)}r(),this.processNode(t,n,e)}))}onlyBranchIndex(t,e=[],r,n){if(!(t instanceof cn))throw new Error("Expected branch node");const i=t.getBranch(r);if(!i)throw new Error("Could not get branch of childIndex");const o=e.slice();o.push(r);const s=n??o.length;this.pushNodeToQueue(i,o,s)}processNode(t,e,r=[]){this.onNode(t,e,r,this),this.taskExecutor.finished()&&this.resolve()}}class An{constructor(t){let e;if(this._opts={useKeyHashing:!1,useKeyHashingFunction:wr,keyPrefix:void 0,useRootPersistence:!1,useNodePruning:!1,cacheSize:0,valueEncoding:Br.String},this._lock=new Dr,this._debug=vn("trie"),this.walkTrieIterable=kn.bind(this),void 0!==t){if(void 0!==t?.valueEncoding&&void 0===t.db)throw new Error("`valueEncoding` can only be set if a `db` is provided");this._opts={...this._opts,...t},this._opts.useKeyHashingFunction=t.common?.customCrypto.keccak256??t.useKeyHashingFunction??wr,e=void 0!==t.db?t.valueEncoding??Br.String:Br.Bytes}else e=Br.Bytes;this.DEBUG="undefined"==typeof window&&(process?.env?.DEBUG?.includes("ethjs")??!1),this.debug=this.DEBUG?(t,e=[])=>{let r=this._debug;for(const t of e)r=r.extend(t);r(t)}:(...t)=>{},this.database(t?.db??new Hr,e),this.EMPTY_TRIE_ROOT=this.hash(Ie),this._hashLen=this.EMPTY_TRIE_ROOT.length,this._root=this.EMPTY_TRIE_ROOT,t?.root&&this.root(t.root),this.DEBUG&&this.debug(`Trie created:\n    || Root: ${ce(this.root())}\n    || Secure: ${this._opts.useKeyHashing}\n    || Persistent: ${this._opts.useRootPersistence}\n    || Pruning: ${this._opts.useNodePruning}\n    || CacheSize: ${this._opts.cacheSize}\n    || ----------------`)}static async createFromProof(t,e,r=!1){const n=new An(e),i=await n.updateFromProof(t,r);return n.root(i),await n.persistRoot(),n}static async verifyProof(t,e,r){try{const n=await An.createFromProof(e,r);return await n.get(t,!0)}catch(t){throw new Error("Invalid proof provided")}}static verifyRangeProof(t,e,r,n,i,o,s){return Mn(t,e&&ln(e),r&&ln(r),n.map((t=>t)).map(ln),i,o,s?.useKeyHashingFunction??wr)}static async fromProof(t,e){const r=await An.create(e);if(e?.root&&!Yt(e.root,r.hash(t[0])))throw new Error("Invalid proof provided");const n=await r.updateFromProof(t);return r.root(n),await r.persistRoot(),r}verifyRangeProof(t,e,r,n,i,o){return Mn(t,e&&ln(this.appliedKey(e)),r&&ln(this.appliedKey(r)),n.map((t=>this.appliedKey(t))).map(ln),i,o,this._opts.useKeyHashingFunction)}async createProof(t){this.DEBUG&&this.debug(`Creating Proof for Key: ${ce(t)}`,["CREATE_PROOF"]);const{stack:e}=await this.findPath(this.appliedKey(t)),r=e.map((t=>t.serialize()));return this.DEBUG&&this.debug(`Proof created with (${e.length}) nodes`,["CREATE_PROOF"]),r}async updateFromProof(t,e=!1){this.DEBUG&&this.debug(`Saving (${t.length}) proof nodes in DB`,["FROM_PROOF"]);const r=t.map((t=>{let e=Uint8Array.from(this.hash(t));return e=this._opts.keyPrefix?xe(this._opts.keyPrefix,e):e,{type:"put",key:e,value:t}}));if(e&&void 0!==r[0]&&null!==r[0]&&!Yt(this.root(),r[0].key))throw new Error("The provided proof does not have the expected trie root");if(await this._db.batch(r),void 0!==r[0])return r[0].key}async verifyProof(t,e,r){this.DEBUG&&this.debug(`Verifying Proof:\n|| Key: ${ce(e)}\n|| Root: ${ce(t)}\n|| Proof: (${r.length}) nodes\n    `,["VERIFY_PROOF"]);const n=new An({root:t,useKeyHashingFunction:this._opts.useKeyHashingFunction,common:this._opts.common});try{await n.updateFromProof(r,!0)}catch(t){throw new Error("Invalid proof nodes given")}try{this.DEBUG&&this.debug(`Verifying proof by retrieving key: ${ce(e)} from proof trie`,["VERIFY_PROOF"]);const t=await n.get(this.appliedKey(e),!0);return this.DEBUG&&this.debug("PROOF VERIFIED",["VERIFY_PROOF"]),t}catch(t){throw"Missing node in DB"===t.message?new Error("Invalid proof provided"):t}}async fromProof(t){if(await this.updateFromProof(t,!1),Yt(this.root(),this.EMPTY_TRIE_ROOT)&&void 0!==t[0]){let e=Uint8Array.from(this.hash(t[0]));e=this._opts.keyPrefix?xe(this._opts.keyPrefix,e):e,this.root(e),await this.persistRoot()}}static async create(t){const e=t?.common?.customCrypto.keccak256??t?.useKeyHashingFunction??wr;let r=En;const n=t?.valueEncoding===Br.Bytes?Br.Bytes:Br.String;if(!0===t?.useKeyHashing&&(r=e.call(void 0,En)),void 0!==t?.keyPrefix&&(r=xe(t.keyPrefix,r)),void 0!==t?.db&&!0===t?.useRootPersistence)if(void 0===t?.root){const e=await(t?.db.get(ne(r),{keyEncoding:Pr.String,valueEncoding:n}));t.root="string"==typeof e?ae(e):e}else await(t?.db.put(ne(r),n===Br.Bytes?t.root:ne(t.root),{keyEncoding:Pr.String,valueEncoding:n}));return new An(t)}database(t,e){if(void 0!==t){if(t instanceof hn)throw new Error("Cannot pass in an instance of CheckpointDB");this._db=new hn({db:t,cacheSize:this._opts.cacheSize,valueEncoding:e})}return this._db}root(t){if(void 0!==t){if(null===t&&(t=this.EMPTY_TRIE_ROOT),this.DEBUG&&this.debug(`Setting root to ${ce(t)}`),t.length!==this._hashLen)throw new Error(`Invalid root length. Roots are ${this._hashLen} bytes, got ${t.length} bytes`);this._root=t}return this._root}async checkRoot(t){try{return null!==await this.lookupNode(t)}catch(e){if("Missing node in DB"===e.message)return Yt(t,this.EMPTY_TRIE_ROOT);throw e}}async get(t,e=!1){this.DEBUG&&this.debug(`Key: ${ce(t)}`,["GET"]);const{node:r,remaining:n}=await this.findPath(this.appliedKey(t),e);let i=null;return r&&0===n.length&&(i=r.value()),this.DEBUG&&this.debug(`Value: ${null===i?"null":ce(i)}`,["GET"]),i}async put(t,e,r=!1){if(this.DEBUG&&this.debug(`Key: ${ce(t)}`,["PUT"]),this.DEBUG&&this.debug(`Value: ${null===e?"null":ce(t)}`,["PUT"]),this._opts.useRootPersistence&&!0===Yt(t,En))throw new Error(`Attempted to set '${Zt(En)}' key but it is not allowed.`);if(null===e||0===e.length)return this.del(t);await this._lock.acquire();const n=r?t:this.appliedKey(t);if(!0===Yt(this.root(),this.EMPTY_TRIE_ROOT))await this._createInitialNode(n,e);else{const{remaining:r,stack:i}=await this.findPath(n);let o=[];if(this._opts.useNodePruning){const r=await this.get(t);null!==r&&!1!==Yt(r,e)||(o=i.map((t=>this.hash(t.serialize()))).map((t=>({type:"del",key:this._opts.keyPrefix?xe(this._opts.keyPrefix,t):t,opts:{keyEncoding:Pr.Bytes}}))))}await this._updateNode(n,e,r,i),this._opts.useNodePruning&&await this._db.batch(o)}await this.persistRoot(),this._lock.release()}async del(t,e=!1){this.DEBUG&&this.debug(`Key: ${ce(t)}`,["DEL"]),await this._lock.acquire();const r=e?t:this.appliedKey(t),{node:n,stack:i}=await this.findPath(r);let o=[];this._opts.useNodePruning&&null!==n&&(o=i.map((t=>this.hash(t.serialize()))).map((t=>({type:"del",key:this._opts.keyPrefix?xe(this._opts.keyPrefix,t):t,opts:{keyEncoding:Pr.Bytes}})))),n&&await this._deleteNode(r,i),this._opts.useNodePruning&&await this._db.batch(o),await this.persistRoot(),this._lock.release()}async findPath(t,e=!1,r={stack:[]}){const n=ln(t),i=n.length,o=Array.from({length:i});let s=0;for(let t=0;t<r.stack.length-1;t++)o[t]=r.stack[t],s+=o[t]instanceof cn?1:o[t].keyLength();this.DEBUG&&this.debug(`Target (${n.length}): [${n}]`,["FIND_PATH"]);let a=null;const h=async(t,e,r,h)=>{if(o[s]=e,e instanceof cn)if(s===i)a={node:e,remaining:[],stack:o};else{const t=n[s];this.DEBUG&&this.debug(`Looking for node on branch index: [${t}]`,["FIND_PATH","BranchNode"]);const i=e.getBranch(t);this.DEBUG&&this.debug(null===i?"NULL":i instanceof Uint8Array?`NodeHash: ${ce(i)}`:`Raw_Node: ${i.toString()}`,["FIND_PATH","BranchNode",t.toString()]),i?(s++,h.onlyBranchIndex(e,r,t)):a={node:null,remaining:n.slice(s),stack:o}}else if(e instanceof yn){const t=s;if(i-s>e.key().length)return void(a={node:null,remaining:n.slice(t),stack:o});for(const r of e.key()){if(r!==n[s])return void(a={node:null,remaining:n.slice(t),stack:o});s++}a={node:e,remaining:[],stack:o}}else if(e instanceof gn){this.DEBUG&&this.debug(`Comparing node key to expected\n|| Node_Key: [${e.key()}]\n|| Expected: [${n.slice(s,s+e.key().length)}]\n|| Matching: [${n.slice(s,s+e.key().length).toString()===e.key().toString()}]\n            `,["FIND_PATH","ExtensionNode"]);const t=s;for(const r of e.key()){if(this.DEBUG&&this.debug(`NextNode: ${e.value()}`,["FIND_PATH","ExtensionNode"]),r!==n[s])return void(a={node:null,remaining:n.slice(t),stack:o});s++}h.allChildren(e,r)}},c=r.stack[r.stack.length-1],u=void 0!==c?this.hash(c?.serialize()):this.root();try{this.DEBUG&&this.debug(`Walking trie from ${void 0===c?"ROOT":"NODE"}: ${ce(u)}`,["FIND_PATH"]),await this.walkTrie(u,h)}catch(t){if("Missing node in DB"!==t.message||e)throw t}return null===a&&(a={node:null,remaining:[],stack:o}),this.DEBUG&&this.debug(null!==a.node?`Target Node FOUND for ${ln(t)}`:"Target Node NOT FOUND",["FIND_PATH"]),a.stack=a.stack.filter((t=>void 0!==t)),this.DEBUG&&this.debug(`Result:\n        || Node: ${null===a.node?"null":a.node.constructor.name}\n        || Remaining: [${a.remaining}]\n|| Stack: ${a.stack.map((t=>t.constructor.name)).join(", ")}`,["FIND_PATH"]),a}async walkTrie(t,e){await Bn.newWalk(e,this,t)}async walkAllNodes(t){for await(const{node:e,currentKey:r}of this.walkTrieIterable(this.root()))await t(e,r)}async walkAllValueNodes(t){for await(const{node:e,currentKey:r}of this.walkTrieIterable(this.root(),[],void 0,(async t=>t instanceof yn||t instanceof cn&&null!==t.value())))await t(e,r)}async _createInitialNode(t,e){const r=new yn(ln(t),e).serialize();this.root(this.hash(r));let n=this.root();n=this._opts.keyPrefix?xe(this._opts.keyPrefix,n):n,await this._db.put(n,r),await this.persistRoot()}async lookupNode(t){if(wn(t)){const e=bn(t);return this.DEBUG&&this.debug(`${e.constructor.name}`,["LOOKUP_NODE","RAW_NODE"]),e}this.DEBUG&&this.debug(`${ce(t)}`,["LOOKUP_NODE","BY_HASH"]);const e=this._opts.keyPrefix?xe(this._opts.keyPrefix,t):t,r=await this._db.get(e)??null;if(null===r)throw new Error("Missing node in DB");const n=function(t){const e=Ke(Uint8Array.from(t));if(!wn(e))throw new Error("Invalid node");return bn(e)}(r);return this.DEBUG&&this.debug(`${n.constructor.name} found in DB`,["LOOKUP_NODE","BY_HASH"]),n}async _updateNode(t,e,r,n){const i=[],o=n.pop();if(!o)throw new Error("Stack underflow");const s=ln(t);let a=!1;if(o instanceof yn){let t=0;for(let e=0;e<n.length;e++){const r=n[e];r instanceof cn?t++:t+=r.key().length}pn(o.key(),s.slice(t))===o.key().length&&0===r.length&&(a=!0)}if(a)o.value(e),n.push(o);else if(o instanceof cn)if(n.push(o),0!==r.length){r.shift();const t=new yn(r,e);n.push(t)}else o.value(e);else{const t=o.key(),s=pn(t,r),a=new cn;if(0!==s){const i=o.key().slice(0,s),a=new gn(i,e);n.push(a),t.splice(0,s),r.splice(0,s)}if(n.push(a),0!==t.length){const e=t.shift();if(0!==t.length||o instanceof yn){o.key(t);const r=this._formatNode(o,!1,i);a.setBranch(e,r)}else this._formatNode(o,!1,i,!0),a.setBranch(e,o.value())}else a.value(o.value());if(0!==r.length){r.shift();const t=new yn(r,e);n.push(t)}else a.value(e)}await this.saveStack(s,n,i)}async _deleteNode(t,e){let r=e.pop();if(void 0===r)throw new Error("missing last node");let n=e.pop();const i=[];let o=ln(t);if(!n)return void this.root(this.EMPTY_TRIE_ROOT);if(r instanceof cn)r.value(null);else{if(!(n instanceof cn))throw new Error("Expected branch node");const t=r.key();o.splice(o.length-t.length),this._formatNode(r,!1,i,!0),n.setBranch(o.pop(),null),r=n,n=e.pop()}const s=r.getChildren();if(1===s.length){const t=s[0][1],r=s[0][0];this._opts.useNodePruning&&i.push({type:"del",key:t}),o=((t,e,r,n,i)=>{if(null==n||n instanceof cn){if(null!=n&&i.push(n),r instanceof cn){const r=new gn([e],null);i.push(r),t.push(e)}else{const n=r.key();n.unshift(e),r.key(n.slice(0)),t=t.concat(n)}i.push(r)}else{let o=n.key();if(r instanceof cn)o.push(e),t.push(e),n.key(o),i.push(n);else{const n=r.key();n.unshift(e),t=t.concat(n),o=o.concat(n),r.key(o)}i.push(r)}return t})(o,r,await this.lookupNode(t),n,e),await this.saveStack(o,e,i)}else n&&e.push(n),e.push(r),await this.saveStack(o,e,i)}async saveStack(t,e,r){let n;for(;e.length;){const i=e.pop();if(i instanceof yn)t.splice(t.length-i.key().length);else if(i instanceof gn)t.splice(t.length-i.key().length),n&&i.value(n);else if(i instanceof cn&&n){const e=t.pop();i.setBranch(e,n)}n=this._formatNode(i,0===e.length,r)}n&&this.root(n),await this._db.batch(r),await this.persistRoot()}_formatNode(t,e,r,n=!1){const i=t.serialize();if(i.length>=32||e){const t=this.hash(i),e=this._opts.keyPrefix?xe(this._opts.keyPrefix,t):t;return n?this._opts.useNodePruning&&r.push({type:"del",key:e}):r.push({type:"put",key:e,value:i}),t}return t.raw()}async batch(t,e){for(const r of t)if("put"===r.type){if(null===r.value||void 0===r.value)throw new Error("Invalid batch db operation");await this.put(r.key,r.value,e)}else"del"===r.type&&await this.del(r.key,e);await this.persistRoot()}async verifyPrunedIntegrity(){const t=[ne(this.root()),ne(this.appliedKey(En))];for(const e of this._db.db._database.keys()){if(t.includes(e))continue;let r=!1;try{await this.walkTrie(this.root(),(async function(t,n,i,o){if(!r){if(n instanceof cn){for(const t of n._branches)if(null!==t&&ne(t)===e)return void(r=!0);o.allChildren(n,i)}if(n instanceof gn){if(ne(n.value())===e)return void(r=!0);o.allChildren(n,i)}}}))}catch{return!1}if(!r)return!1}return!0}createReadStream(){return new _n(this)}shallowCopy(t=!0,e){const r=new An({...this._opts,db:this._db.db.shallowCopy(),root:this.root(),cacheSize:0,...e??{}});return t&&this.hasCheckpoints()&&r._db.setCheckpoints(this._db.checkpoints),r}async persistRoot(){if(this._opts.useRootPersistence){this.DEBUG&&this.debug(`Persisting root: \n|| RootHash: ${ce(this.root())}\n|| RootKey: ${ce(this.appliedKey(En))}`,["PERSIST_ROOT"]);let t=this.appliedKey(En);t=this._opts.keyPrefix?xe(this._opts.keyPrefix,t):t,await this._db.put(t,this.root())}}async _findDbNodes(t){await this.walkTrie(this.root(),(async(e,r,n,i)=>{wn(e)?null!==r&&i.allChildren(r,n):t(e,r,n,i)}))}appliedKey(t){return this._opts.useKeyHashing?this.hash(t):t}hash(t){return Uint8Array.from(this._opts.useKeyHashingFunction.call(void 0,t))}hasCheckpoints(){return this._db.hasCheckpoints()}checkpoint(){this.DEBUG&&this.debug(`${ce(this.root())}`,["CHECKPOINT"]),this._db.checkpoint(this.root())}async commit(){if(!this.hasCheckpoints())throw new Error("trying to commit when not checkpointed");this.DEBUG&&this.debug(`${ce(this.root())}`,["COMMIT"]),await this._lock.acquire(),await this._db.commit(),await this.persistRoot(),this._lock.release()}async revert(){if(!this.hasCheckpoints())throw new Error("trying to revert when not checkpointed");this.DEBUG&&this.debug(`${ce(this.root())}`,["REVERT","BEFORE"]),await this._lock.acquire(),this.root(await this._db.revert()),await this.persistRoot(),this._lock.release(),this.DEBUG&&this.debug(`${ce(this.root())}`,["REVERT","AFTER"])}flushCheckpoints(){this.DEBUG&&this.debug(`Deleting ${this._db.checkpoints.length} checkpoints.`,["FLUSH_CHECKPOINTS"]),this._db.checkpoints=[]}}async function Sn(t,e,r,n,i,o,s){if(r instanceof cn){if(o)for(let t=0;t<n[i];t++)r.setBranch(t,null);else for(let t=n[i]+1;t<16;t++)r.setBranch(t,null);s.push(r);const e=r.getBranch(n[i]),a=e&&await t.lookupNode(e);return Sn(t,r,a,n,i+1,o,s)}if(r instanceof gn||r instanceof yn){if(n.length-i<r.keyLength()||0!==dn(r._nibbles,n.slice(i,i+r.keyLength())))return o?dn(r._nibbles,n.slice(i))<0&&e.setBranch(n[i-1],null):dn(r._nibbles,n.slice(i))>0&&e.setBranch(n[i-1],null),i-1;if(r instanceof yn)return e.setBranch(n[i-1],null),i-1;{const a=await t.lookupNode(r.value());return a instanceof yn?(e.setBranch(n[i-1],null),i-1):(s.push(r),Sn(t,r,a,n,i+r.keyLength(),o,s))}}if(null===r)return i-1;throw new Error("invalid node")}async function In(t,e,r,n){const i=await An.fromProof(r,{root:t,useKeyHashingFunction:n});try{const t=await i.get(e,!0);return{trie:i,value:t}}catch(t){throw"Missing node in DB"===t.message?new Error("Invalid proof provided"):t}}async function Cn(t,e){let r=0,n=await t.lookupNode(t.root());for(;null!==n;)if(n instanceof cn){for(let t=e[r]+1;t<16;t++)if(null!==n.getBranch(t))return!0;const i=n.getBranch(e[r]);n=i&&await t.lookupNode(i),r+=1}else{if(!(n instanceof gn)){if(n instanceof yn)return!1;throw new Error("invalid node")}if(e.length-r<n.keyLength()||0!==dn(n._nibbles,e.slice(r,r+n.keyLength())))return dn(n._nibbles,e.slice(r))>0;r+=n.keyLength(),n=await t.lookupNode(n._value)}return!1}async function Mn(t,e,r,n,i,o,s){if(n.length!==i.length)throw new Error("invalid keys length or values length");for(let t=0;t<n.length-1;t++)if(dn(n[t],n[t+1])>=0)throw new Error("invalid keys order");for(const t of i)if(0===t.length)throw new Error("invalid values");if(null===o&&null===e&&null===r){const e=new An({useKeyHashingFunction:s});for(let t=0;t<n.length;t++)await e.put(fn(n[t]),i[t]);if(!Yt(t,e.root()))throw new Error("invalid all elements proof: root mismatch");return!1}if(null!==o&&null!==e&&null===r&&0===n.length){const{trie:r,value:n}=await In(t,fn(e),o,s);if(null!==n||await Cn(r,e))throw new Error("invalid zero element proof: value mismatch");return!1}if(null===o||null===e||null===r)throw new Error("invalid all elements proof: proof, firstKey, lastKey must be null at the same time");if(1===n.length&&0===dn(e,r)){const{trie:r,value:a}=await In(t,fn(e),o,s);if(0!==dn(e,n[0]))throw new Error("invalid one element proof: firstKey should be equal to keys[0]");if(null===a||!Yt(a,i[0]))throw new Error("invalid one element proof: value mismatch");return Cn(r,e)}if(dn(e,r)>=0)throw new Error("invalid two edge elements proof: firstKey should be less than lastKey");if(e.length!==r.length)throw new Error("invalid two edge elements proof: the length of firstKey should be equal to the length of lastKey");const a=await An.fromProof(o,{useKeyHashingFunction:s,root:t}),h=await async function(t,e,r){let n,i,o=0,s=null,a=await t.lookupNode(t.root());const h=[];for(;;)if(a instanceof gn||a instanceof yn){if(h.push(a),n=e.length-o<a.keyLength()?dn(e.slice(o),a._nibbles):dn(e.slice(o,o+a.keyLength()),a._nibbles),i=r.length-o<a.keyLength()?dn(r.slice(o),a._nibbles):dn(r.slice(o,o+a.keyLength()),a._nibbles),0!==n||0!==i)break;if(a instanceof yn)throw new Error("invalid node");s=a,o+=a.keyLength(),a=await t.lookupNode(a.value())}else{if(!(a instanceof cn))throw new Error("invalid node");{h.push(a);const n=a.getBranch(e[o]),i=a.getBranch(r[o]);if(null===n||null===i)break;if(n instanceof Uint8Array){if(!(i instanceof Uint8Array))break;if(!Yt(n,i))break}else{if(i instanceof Uint8Array)break;if(n.length!==i.length)break;let t=!1;for(let e=0;e<n.length;e++)if(!Yt(n[e],i[e])){t=!0;break}if(t)break}s=a,a=await t.lookupNode(n),o+=1}}const c=(e,r)=>t.saveStack(e,r,[]);if(a instanceof gn||a instanceof yn){const u=async t=>null===s||(h.pop(),s.setBranch(t[o-1],null),await c(t.slice(0,o-1),h),!1);if(-1===n&&-1===i)throw new Error("invalid range");if(1===n&&1===i)throw new Error("invalid range");if(0!==n&&0!==i)return u(e);if(0!==i){if(a instanceof yn)return u(e);const r=await t.lookupNode(a._value);if(r instanceof yn)return u(e);const n=await Sn(t,a,r,e.slice(o),a.keyLength(),!1,h);return await c(e.slice(0,o+n),h),!1}if(0!==n){if(a instanceof yn)return u(r);const e=await t.lookupNode(a._value);if(e instanceof yn)return u(r);const n=await Sn(t,a,e,r.slice(o),a.keyLength(),!0,h);return await c(r.slice(0,o+n),h),!1}return!1}if(a instanceof cn){for(let t=e[o]+1;t<r[o];t++)a.setBranch(t,null);{const r=[...h],n=a.getBranch(e[o]),i=n&&await t.lookupNode(n),s=await Sn(t,a,i,e.slice(o),1,!1,r);await c(e.slice(0,o+s),r)}{const e=[...h],n=a.getBranch(r[o]),i=n&&await t.lookupNode(n),s=await Sn(t,a,i,r.slice(o),1,!0,e);await c(r.slice(0,o+s),e)}return!1}throw new Error("invalid node")}(a,e,r);h&&a.root(a.EMPTY_TRIE_ROOT);for(let t=0;t<n.length;t++)await a.put(fn(n[t]),i[t]);if(!Yt(a.root(),t))throw new Error("invalid two edge elements proof: root mismatch");return Cn(a,n[n.length-1])}const Rn={mainnet:{name:"mainnet",chainId:1,networkId:1,defaultHardfork:"shanghai",consensus:{type:"pow",algorithm:"ethash",ethash:{}},comment:"The Ethereum main chain",url:"https://ethstats.net/",genesis:{gasLimit:5e3,difficulty:17179869184,nonce:"0x0000000000000042",extraData:"0x11bbe8db4e347b4e8c937c1c8370e4b5ed33adb3db69cbdb7a38e1e50b1b82fa"},hardforks:[{name:"chainstart",block:0,forkHash:"0xfc64ec04"},{name:"homestead",block:115e4,forkHash:"0x97c2c34c"},{name:"dao",block:192e4,forkHash:"0x91d1f948"},{name:"tangerineWhistle",block:2463e3,forkHash:"0x7a64da13"},{name:"spuriousDragon",block:2675e3,forkHash:"0x3edd5b10"},{name:"byzantium",block:437e4,forkHash:"0xa00bc324"},{name:"constantinople",block:728e4,forkHash:"0x668db0af"},{name:"petersburg",block:728e4,forkHash:"0x668db0af"},{name:"istanbul",block:9069e3,forkHash:"0x879d6e30"},{name:"muirGlacier",block:92e5,forkHash:"0xe029e991"},{name:"berlin",block:12244e3,forkHash:"0x0eb440f6"},{name:"london",block:12965e3,forkHash:"0xb715077d"},{name:"arrowGlacier",block:13773e3,forkHash:"0x20c327fc"},{name:"grayGlacier",block:1505e4,forkHash:"0xf0afd0e3"},{name:"paris",ttd:"58750000000000000000000",block:15537394,forkHash:"0xf0afd0e3"},{name:"mergeForkIdTransition",block:null,forkHash:null},{name:"shanghai",block:null,timestamp:"1681338455",forkHash:"0xdce96c2d"},{name:"cancun",block:null,timestamp:"1710338135",forkHash:"0x9f3d2254"}],bootstrapNodes:[{ip:"*************",port:30303,id:"d860a01f9722d78051619d1e2351aba3f43f943f6f00718d1b9baa4101932a1f5011f16bb2b1bb35db20d6fe28fa0bf09636d26a87d31de9ec6203eeedb1f666",location:"ap-southeast-1-001",comment:"bootnode-aws-ap-southeast-1-001"},{ip:"***********",port:30303,id:"22a8232c3abc76a16ae9d6c3b164f98775fe226f0917b0ca871128a74a8e9630b458460865bab457221f1d448dd9791d24c4e5d88786180ac185df813a68d4de",location:"us-east-1-001",comment:"bootnode-aws-us-east-1-001"},{ip:"*************",port:30303,id:"2b252ab6a1d0f971d9722cb839a42cb81db019ba44c08754628ab4a823487071b5695317c8ccd085219c3a03af063495b2f1da8d18218da2d6a82981b45e6ffc",location:"eu-west-1-001",comment:"bootnode-hetzner-hel"},{ip:"*************",port:30303,id:"4aeb4ab6c14b23e2c4cfdce879c04b0748a20d8e9b59e25ded2a08143e265c6c25936e74cbc8e641e3312ca288673d91f2f93f8e277de3cfa444ecdaaf982052",location:"eu-central-1-001",comment:"bootnode-hetzner-fsn"}],dnsNetworks:["enrtree://<EMAIL>"]},goerli:{name:"goerli",chainId:5,networkId:5,defaultHardfork:"shanghai",consensus:{type:"poa",algorithm:"clique",clique:{period:15,epoch:3e4}},comment:"Cross-client PoA test network",url:"https://github.com/goerli/testnet",genesis:{timestamp:"0x5c51a607",gasLimit:10485760,difficulty:1,nonce:"0x0000000000000000",extraData:"0x22466c6578692069732061207468696e6722202d204166726900000000000000e0a2bd4258d2768837baa26a28fe71dc079f84c70000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"},hardforks:[{name:"chainstart",block:0,forkHash:"0xa3f5ab08"},{name:"homestead",block:0,forkHash:"0xa3f5ab08"},{name:"tangerineWhistle",block:0,forkHash:"0xa3f5ab08"},{name:"spuriousDragon",block:0,forkHash:"0xa3f5ab08"},{name:"byzantium",block:0,forkHash:"0xa3f5ab08"},{name:"constantinople",block:0,forkHash:"0xa3f5ab08"},{name:"petersburg",block:0,forkHash:"0xa3f5ab08"},{name:"istanbul",block:1561651,forkHash:"0xc25efa5c"},{name:"berlin",block:4460644,forkHash:"0x757a1c47"},{name:"london",block:5062605,forkHash:"0xb8c6299d"},{name:"paris",ttd:"10790000",block:7382819,forkHash:"0xb8c6299d"},{name:"mergeForkIdTransition",block:null,forkHash:null},{name:"shanghai",block:null,timestamp:"1678832736",forkHash:"0xf9843abf"},{name:"cancun",block:null,timestamp:"1705473120",forkHash:"0x70cc14e2"}],bootstrapNodes:[{ip:"************",port:30303,id:"011f758e6552d105183b1761c5e2dea0111bc20fd5f6422bc7f91e0fabbec9a6595caf6239b37feb773dddd3f87240d99d859431891e4a642cf2a0a9e6cbb98a",location:"",comment:"Upstream bootnode 1"},{ip:"************",port:30303,id:"176b9417f511d05b6b2cf3e34b756cf0a7096b3094572a8f6ef4cdcb9d1f9d00683bf0f83347eebdf3b81c3521c2332086d9592802230bf528eaf606a1d9677b",location:"",comment:"Upstream bootnode 2"},{ip:"*************",port:30313,id:"46add44b9f13965f7b9875ac6b85f016f341012d84f975377573800a863526f4da19ae2c620ec73d11591fa9510e992ecc03ad0751f53cc02f7c7ed6d55c7291",location:"",comment:"Upstream bootnode 3"},{ip:"*************",port:30313,id:"b5948a2d3e9d486c4d75bf32713221c2bd6cf86463302339299bd227dc2e276cd5a1c7ca4f43a0e9122fe9af884efed563bd2a1fd28661f3b5f5ad7bf1de5949",location:"",comment:"Upstream bootnode 4"},{ip:"***********",port:30303,id:"a61215641fb8714a373c80edbfa0ea8878243193f57c96eeb44d0bc019ef295abd4e044fd619bfc4c59731a73fb79afe84e9ab6da0c743ceb479cbb6d263fa91",location:"",comment:"Ethereum Foundation bootnode"},{ip:"*************",port:30303,id:"a869b02cec167211fb4815a82941db2e7ed2936fd90e78619c53eb17753fcf0207463e3419c264e2a1dd8786de0df7e68cf99571ab8aeb7c4e51367ef186b1dd",location:"",comment:"Goerli Initiative bootnode"},{ip:"*************",port:30303,id:"807b37ee4816ecf407e9112224494b74dd5933625f655962d892f2f0f02d7fbbb3e2a94cf87a96609526f30c998fd71e93e2f53015c558ffc8b03eceaf30ee33",location:"",comment:"Goerli Initiative bootnode"},{ip:"*************",port:40303,id:"a59e33ccd2b3e52d578f1fbd70c6f9babda2650f0760d6ff3b37742fdcdfdb3defba5d56d315b40c46b70198c7621e63ffa3f987389c7118634b0fefbbdfa7fd",location:"",comment:"Goerli Initiative bootnode"}],dnsNetworks:["enrtree://<EMAIL>"]},sepolia:{name:"sepolia",chainId:11155111,networkId:11155111,defaultHardfork:"shanghai",consensus:{type:"pow",algorithm:"ethash",ethash:{}},comment:"PoW test network to replace Ropsten",url:"https://github.com/ethereum/go-ethereum/pull/23730",genesis:{timestamp:"0x6159af19",gasLimit:3e7,difficulty:131072,nonce:"0x0000000000000000",extraData:"0x5365706f6c69612c20417468656e732c204174746963612c2047726565636521"},hardforks:[{name:"chainstart",block:0,forkHash:"0xfe3366e7"},{name:"homestead",block:0,forkHash:"0xfe3366e7"},{name:"tangerineWhistle",block:0,forkHash:"0xfe3366e7"},{name:"spuriousDragon",block:0,forkHash:"0xfe3366e7"},{name:"byzantium",block:0,forkHash:"0xfe3366e7"},{name:"constantinople",block:0,forkHash:"0xfe3366e7"},{name:"petersburg",block:0,forkHash:"0xfe3366e7"},{name:"istanbul",block:0,forkHash:"0xfe3366e7"},{name:"muirGlacier",block:0,forkHash:"0xfe3366e7"},{name:"berlin",block:0,forkHash:"0xfe3366e7"},{name:"london",block:0,forkHash:"0xfe3366e7"},{name:"paris",ttd:"17000000000000000",block:1450409,forkHash:"0xfe3366e7"},{name:"mergeForkIdTransition",block:1735371,forkHash:"0xb96cbd13"},{name:"shanghai",block:null,timestamp:"1677557088",forkHash:"0xf7f9bc08"},{name:"cancun",block:null,timestamp:"1706655072",forkHash:"0x88cf81d9"}],bootstrapNodes:[{ip:"*************",port:30303,id:"9246d00bc8fd1742e5ad2428b80fc4dc45d786283e05ef6edbd9002cbc335d40998444732fbe921cb88e1d2c73d1b1de53bae6a2237996e9bfe14f871baf7066",location:"",comment:"geth"},{ip:"*************",port:30303,id:"ec66ddcf1a974950bd4c782789a7e04f8aa7110a72569b6e65fcd51e937e74eed303b1ea734e4d19cfaec9fbff9b6ee65bf31dcb50ba79acce9dd63a6aca61c7",location:"",comment:"besu"},{ip:"**************",port:30303,id:"ce970ad2e9daa9e14593de84a8b49da3d54ccfdf83cbc4fe519cb8b36b5918ed4eab087dedd4a62479b8d50756b492d5f762367c8d20329a7854ec01547568a6",location:"",comment:"EF"},{ip:"************",port:30303,id:"075503b13ed736244896efcde2a992ec0b451357d46cb7a8132c0384721742597fc8f0d91bbb40bb52e7d6e66728d36a1fda09176294e4a30cfac55dcce26bc6",location:"",comment:"lodestar"}],dnsNetworks:["enrtree://<EMAIL>"]},holesky:{name:"holesky",chainId:17e3,networkId:17e3,defaultHardfork:"paris",consensus:{type:"pos",algorithm:"casper"},comment:"PoS test network to replace Goerli",url:"https://github.com/eth-clients/holesky/",genesis:{baseFeePerGas:"0x3B9ACA00",difficulty:"0x01",extraData:"0x",gasLimit:"0x17D7840",nonce:"0x0000000000001234",timestamp:"0x65156994"},hardforks:[{name:"chainstart",block:0,forkHash:"0xc61a6098"},{name:"homestead",block:0,forkHash:"0xc61a6098"},{name:"tangerineWhistle",block:0,forkHash:"0xc61a6098"},{name:"spuriousDragon",block:0,forkHash:"0xc61a6098"},{name:"byzantium",block:0,forkHash:"0xc61a6098"},{name:"constantinople",block:0,forkHash:"0xc61a6098"},{name:"petersburg",block:0,forkHash:"0xc61a6098"},{name:"istanbul",block:0,forkHash:"0xc61a6098"},{name:"muirGlacier",block:0,forkHash:"0xc61a6098"},{name:"berlin",block:0,forkHash:"0xc61a6098"},{name:"london",block:0,forkHash:"0xc61a6098"},{name:"paris",ttd:"0",block:0,forkHash:"0xc61a6098"},{name:"mergeForkIdTransition",block:0,forkHash:"0xc61a6098"},{name:"shanghai",block:null,timestamp:"1696000704",forkHash:"0xfd4f016b"},{name:"cancun",block:null,timestamp:"1707305664",forkHash:"0x9b192ad0"}],bootstrapNodes:[{ip:"**************",port:30303,id:"ac906289e4b7f12df423d654c5a962b6ebe5b3a74cc9e06292a85221f9a64a6f1cfdd6b714ed6dacef51578f92b34c60ee91e9ede9c7f8fadc4d347326d95e2b",location:"",comment:"bootnode 1"},{ip:"***************",port:30303,id:"a3435a0155a3e837c02f5e7f5662a2f1fbc25b48e4dc232016e1c51b544cb5b4510ef633ea3278c0e970fa8ad8141e2d4d0f9f95456c537ff05fdf9b31c15072",location:"",comment:"bootnode 2"}],dnsNetworks:["enrtree://<EMAIL>"]},kaustinen:{name:"kaustinen",chainId:69420,networkId:69420,defaultHardfork:"prague",consensus:{type:"pos",algorithm:"casper"},comment:"Verkle kaustinen testnet 2 (likely temporary, do not hard-wire into production code)",url:"https://github.com/eth-clients/kaustinen/",genesis:{difficulty:"0x01",extraData:"0x",gasLimit:"0x17D7840",nonce:"0x0000000000001234",timestamp:"0x65608a64"},hardforks:[{name:"chainstart",block:0},{name:"homestead",block:0},{name:"tangerineWhistle",block:0},{name:"spuriousDragon",block:0},{name:"byzantium",block:0},{name:"constantinople",block:0},{name:"petersburg",block:0},{name:"istanbul",block:0},{name:"berlin",block:0},{name:"london",block:0},{name:"paris",ttd:"0",block:0},{name:"mergeForkIdTransition",block:0},{name:"shanghai",block:null,timestamp:"0"},{name:"prague",block:null,timestamp:"1700825700"}],bootstrapNodes:[],dnsNetworks:[]}};let Tn=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];"undefined"!=typeof Int32Array&&(Tn=new Int32Array(Tn));var On,Ln,Fn,Nn,Dn,Hn;!function(t){t[t.Mainnet=1]="Mainnet",t[t.Goerli=5]="Goerli",t[t.Sepolia=11155111]="Sepolia",t[t.Holesky=17e3]="Holesky",t[t.Kaustinen=69420]="Kaustinen"}(On||(On={})),On.Mainnet,fe("0xd7f8974fb5ac78d9ac099b9ad5018bedc2ce0a72dad1827a1709da30580f0544"),On.Goerli,fe("0x5d6cded585e73c4e322c30c2f782a336316f17dd85a4863b9d838d2d4b8b3008"),On.Sepolia,fe("0x5eb6e371a698b8d68f665192350ffcecbbbf322916f4b51bd79bb6887da3f494"),On.Holesky,fe("0x69d8c9d72f6fa4ad42d4702b433707212f90db395eb54dc20bc85de253788783"),On.Kaustinen,fe("0x5e8519756841faf0b2c28951c451b61a4b407b70a5ce5b57992f4bec973173ff"),function(t){t.Chainstart="chainstart",t.Homestead="homestead",t.Dao="dao",t.TangerineWhistle="tangerineWhistle",t.SpuriousDragon="spuriousDragon",t.Byzantium="byzantium",t.Constantinople="constantinople",t.Petersburg="petersburg",t.Istanbul="istanbul",t.MuirGlacier="muirGlacier",t.Berlin="berlin",t.London="london",t.ArrowGlacier="arrowGlacier",t.GrayGlacier="grayGlacier",t.MergeForkIdTransition="mergeForkIdTransition",t.Paris="paris",t.Shanghai="shanghai",t.Cancun="cancun",t.Prague="prague"}(Ln||(Ln={})),function(t){t.ProofOfStake="pos",t.ProofOfWork="pow",t.ProofOfAuthority="poa"}(Fn||(Fn={})),function(t){t.Ethash="ethash",t.Clique="clique",t.Casper="casper"}(Nn||(Nn={})),function(t){t.PolygonMainnet="polygon-mainnet",t.PolygonMumbai="polygon-mumbai",t.ArbitrumOne="arbitrum-one",t.xDaiChain="x-dai-chain",t.OptimisticKovan="optimistic-kovan",t.OptimisticEthereum="optimistic-ethereum"}(Dn||(Dn={})),function(t){t.Stagnant="stagnant",t.Draft="draft",t.Review="review",t.Final="final"}(Hn||(Hn={}));const Un={1153:{comment:"Transient storage opcodes",url:"https://eips.ethereum.org/EIPS/eip-1153",status:Hn.Review,minimumHardfork:Ln.Chainstart,requiredEIPs:[],gasPrices:{tstore:{v:100,d:"Base fee of the TSTORE opcode"},tload:{v:100,d:"Base fee of the TLOAD opcode"}}},1559:{comment:"Fee market change for ETH 1.0 chain",url:"https://eips.ethereum.org/EIPS/eip-1559",status:Hn.Final,minimumHardfork:Ln.Berlin,requiredEIPs:[2930],gasConfig:{baseFeeMaxChangeDenominator:{v:8,d:"Maximum base fee change denominator"},elasticityMultiplier:{v:2,d:"Maximum block gas target elasticity"},initialBaseFee:{v:1e9,d:"Initial base fee on first EIP1559 block"}}},2315:{comment:"Simple subroutines for the EVM",url:"https://eips.ethereum.org/EIPS/eip-2315",status:Hn.Draft,minimumHardfork:Ln.Istanbul,requiredEIPs:[],gasPrices:{beginsub:{v:2,d:"Base fee of the BEGINSUB opcode"},returnsub:{v:5,d:"Base fee of the RETURNSUB opcode"},jumpsub:{v:10,d:"Base fee of the JUMPSUB opcode"}}},2565:{comment:"ModExp gas cost",url:"https://eips.ethereum.org/EIPS/eip-2565",status:Hn.Final,minimumHardfork:Ln.Byzantium,requiredEIPs:[],gasPrices:{modexpGquaddivisor:{v:3,d:"Gquaddivisor from modexp precompile for gas calculation"}}},2718:{comment:"Typed Transaction Envelope",url:"https://eips.ethereum.org/EIPS/eip-2718",status:Hn.Final,minimumHardfork:Ln.Chainstart,requiredEIPs:[]},2929:{comment:"Gas cost increases for state access opcodes",url:"https://eips.ethereum.org/EIPS/eip-2929",status:Hn.Final,minimumHardfork:Ln.Chainstart,requiredEIPs:[],gasPrices:{coldsload:{v:2100,d:"Gas cost of the first read of storage from a given location (per transaction)"},coldaccountaccess:{v:2600,d:"Gas cost of the first read of a given address (per transaction)"},warmstorageread:{v:100,d:"Gas cost of reading storage locations which have already loaded 'cold'"},sstoreCleanGasEIP2200:{v:2900,d:"Once per SSTORE operation from clean non-zero to something else"},sstoreNoopGasEIP2200:{v:100,d:"Once per SSTORE operation if the value doesn't change"},sstoreDirtyGasEIP2200:{v:100,d:"Once per SSTORE operation if a dirty value is changed"},sstoreInitRefundEIP2200:{v:19900,d:"Once per SSTORE operation for resetting to the original zero value"},sstoreCleanRefundEIP2200:{v:4900,d:"Once per SSTORE operation for resetting to the original non-zero value"},call:{v:0,d:"Base fee of the CALL opcode"},callcode:{v:0,d:"Base fee of the CALLCODE opcode"},delegatecall:{v:0,d:"Base fee of the DELEGATECALL opcode"},staticcall:{v:0,d:"Base fee of the STATICCALL opcode"},balance:{v:0,d:"Base fee of the BALANCE opcode"},extcodesize:{v:0,d:"Base fee of the EXTCODESIZE opcode"},extcodecopy:{v:0,d:"Base fee of the EXTCODECOPY opcode"},extcodehash:{v:0,d:"Base fee of the EXTCODEHASH opcode"},sload:{v:0,d:"Base fee of the SLOAD opcode"},sstore:{v:0,d:"Base fee of the SSTORE opcode"}}},2930:{comment:"Optional access lists",url:"https://eips.ethereum.org/EIPS/eip-2930",status:Hn.Final,minimumHardfork:Ln.Istanbul,requiredEIPs:[2718,2929],gasPrices:{accessListStorageKeyCost:{v:1900,d:"Gas cost per storage key in an Access List transaction"},accessListAddressCost:{v:2400,d:"Gas cost per storage key in an Access List transaction"}}},2935:{comment:"Save historical block hashes in state (Verkle related usage, UNSTABLE)",url:"https://github.com/gballet/EIPs/pull/3/commits/2e9ac09a142b0d9fb4db0b8d4609f92e5d9990c5",status:Hn.Draft,minimumHardfork:Ln.Chainstart,requiredEIPs:[],vm:{historyStorageAddress:{v:BigInt("******************************************"),d:"The address where the historical blockhashes are stored"},historyServeWindow:{v:BigInt(256),d:"The amount of blocks to be served by the historical blockhash contract"}}},3074:{comment:"AUTH and AUTHCALL opcodes",url:"https://eips.ethereum.org/EIPS/eip-3074",status:Hn.Review,minimumHardfork:Ln.London,requiredEIPs:[],gasPrices:{auth:{v:3100,d:"Gas cost of the AUTH opcode"},authcall:{v:0,d:"Gas cost of the AUTHCALL opcode"},authcallValueTransfer:{v:6700,d:"Paid for CALL when the value transfer is non-zero"}}},3198:{comment:"BASEFEE opcode",url:"https://eips.ethereum.org/EIPS/eip-3198",status:Hn.Final,minimumHardfork:Ln.London,requiredEIPs:[],gasPrices:{basefee:{v:2,d:"Gas cost of the BASEFEE opcode"}}},3529:{comment:"Reduction in refunds",url:"https://eips.ethereum.org/EIPS/eip-3529",status:Hn.Final,minimumHardfork:Ln.Berlin,requiredEIPs:[2929],gasConfig:{maxRefundQuotient:{v:5,d:"Maximum refund quotient; max tx refund is min(tx.gasUsed/maxRefundQuotient, tx.gasRefund)"}},gasPrices:{selfdestructRefund:{v:0,d:"Refunded following a selfdestruct operation"},sstoreClearRefundEIP2200:{v:4800,d:"Once per SSTORE operation for clearing an originally existing storage slot"}}},3540:{comment:"EVM Object Format (EOF) v1",url:"https://eips.ethereum.org/EIPS/eip-3540",status:Hn.Review,minimumHardfork:Ln.London,requiredEIPs:[3541]},3541:{comment:"Reject new contracts starting with the 0xEF byte",url:"https://eips.ethereum.org/EIPS/eip-3541",status:Hn.Final,minimumHardfork:Ln.Berlin,requiredEIPs:[]},3554:{comment:"Difficulty Bomb Delay to December 1st 2021",url:"https://eips.ethereum.org/EIPS/eip-3554",status:Hn.Final,minimumHardfork:Ln.MuirGlacier,requiredEIPs:[],pow:{difficultyBombDelay:{v:95e5,d:"the amount of blocks to delay the difficulty bomb with"}}},3607:{comment:"Reject transactions from senders with deployed code",url:"https://eips.ethereum.org/EIPS/eip-3607",status:Hn.Final,minimumHardfork:Ln.Chainstart,requiredEIPs:[]},3651:{comment:"Warm COINBASE",url:"https://eips.ethereum.org/EIPS/eip-3651",status:Hn.Review,minimumHardfork:Ln.London,requiredEIPs:[2929]},3670:{comment:"EOF - Code Validation",url:"https://eips.ethereum.org/EIPS/eip-3670",status:"Review",minimumHardfork:Ln.London,requiredEIPs:[3540],gasConfig:{},gasPrices:{},vm:{},pow:{}},3675:{comment:"Upgrade consensus to Proof-of-Stake",url:"https://eips.ethereum.org/EIPS/eip-3675",status:Hn.Final,minimumHardfork:Ln.London,requiredEIPs:[]},3855:{comment:"PUSH0 instruction",url:"https://eips.ethereum.org/EIPS/eip-3855",status:Hn.Review,minimumHardfork:Ln.Chainstart,requiredEIPs:[],gasPrices:{push0:{v:2,d:"Base fee of the PUSH0 opcode"}}},3860:{comment:"Limit and meter initcode",url:"https://eips.ethereum.org/EIPS/eip-3860",status:Hn.Review,minimumHardfork:Ln.SpuriousDragon,requiredEIPs:[],gasPrices:{initCodeWordCost:{v:2,d:"Gas to pay for each word (32 bytes) of initcode when creating a contract"}},vm:{maxInitCodeSize:{v:49152,d:"Maximum length of initialization code when creating a contract"}}},4345:{comment:"Difficulty Bomb Delay to June 2022",url:"https://eips.ethereum.org/EIPS/eip-4345",status:Hn.Final,minimumHardfork:Ln.London,requiredEIPs:[],pow:{difficultyBombDelay:{v:107e5,d:"the amount of blocks to delay the difficulty bomb with"}}},4399:{comment:"Supplant DIFFICULTY opcode with PREVRANDAO",url:"https://eips.ethereum.org/EIPS/eip-4399",status:Hn.Review,minimumHardfork:Ln.London,requiredEIPs:[],gasPrices:{prevrandao:{v:2,d:"Base fee of the PREVRANDAO opcode (previously DIFFICULTY)"}}},4788:{comment:"Beacon block root in the EVM",url:"https://eips.ethereum.org/EIPS/eip-4788",status:Hn.Draft,minimumHardfork:Ln.Cancun,requiredEIPs:[],gasPrices:{},vm:{historicalRootsLength:{v:8191,d:"The modulo parameter of the beaconroot ring buffer in the beaconroot statefull precompile"}}},4844:{comment:"Shard Blob Transactions",url:"https://eips.ethereum.org/EIPS/eip-4844",status:Hn.Draft,minimumHardfork:Ln.Paris,requiredEIPs:[1559,2718,2930,4895],gasConfig:{blobGasPerBlob:{v:131072,d:"The base fee for blob gas per blob"},targetBlobGasPerBlock:{v:393216,d:"The target blob gas consumed per block"},maxblobGasPerBlock:{v:786432,d:"The max blob gas allowable per block"},blobGasPriceUpdateFraction:{v:3338477,d:"The denominator used in the exponential when calculating a blob gas price"}},gasPrices:{simpleGasPerBlob:{v:12e3,d:"The basic gas fee for each blob"},minBlobGasPrice:{v:1,d:"The minimum fee per blob gas"},kzgPointEvaluationGasPrecompilePrice:{v:5e4,d:"The fee associated with the point evaluation precompile"},blobhash:{v:3,d:"Base fee of the BLOBHASH opcode"}},sharding:{blobCommitmentVersionKzg:{v:1,d:"The number indicated a versioned hash is a KZG commitment"},fieldElementsPerBlob:{v:4096,d:"The number of field elements allowed per blob"}}},4895:{comment:"Beacon chain push withdrawals as operations",url:"https://eips.ethereum.org/EIPS/eip-4895",status:Hn.Review,minimumHardfork:Ln.Paris,requiredEIPs:[]},5133:{comment:"Delaying Difficulty Bomb to mid-September 2022",url:"https://eips.ethereum.org/EIPS/eip-5133",status:Hn.Draft,minimumHardfork:Ln.GrayGlacier,requiredEIPs:[],pow:{difficultyBombDelay:{v:114e5,d:"the amount of blocks to delay the difficulty bomb with"}}},5656:{comment:"MCOPY - Memory copying instruction",url:"https://eips.ethereum.org/EIPS/eip-5656",status:Hn.Draft,minimumHardfork:Ln.Shanghai,requiredEIPs:[],gasPrices:{mcopy:{v:3,d:"Base fee of the MCOPY opcode"}}},6780:{comment:"SELFDESTRUCT only in same transaction",url:"https://eips.ethereum.org/EIPS/eip-6780",status:Hn.Draft,minimumHardfork:Ln.London,requiredEIPs:[]},6800:{comment:"Ethereum state using a unified verkle tree (experimental)",url:"https://github.com/ethereum/EIPs/pull/6800",status:Hn.Draft,minimumHardfork:Ln.London,requiredEIPs:[]},7516:{comment:"BLOBBASEFEE opcode",url:"https://eips.ethereum.org/EIPS/eip-7516",status:Hn.Draft,minimumHardfork:Ln.Paris,requiredEIPs:[4844],gasPrices:{blobbasefee:{v:2,d:"Gas cost of the BLOBBASEFEE opcode"}}}};var Gn;!function(t){t.Draft="draft",t.Review="review",t.Final="final"}(Gn||(Gn={}));const zn={chainstart:{name:"chainstart",comment:"Start of the Ethereum main chain",url:"",status:Gn.Final,gasConfig:{minGasLimit:{v:5e3,d:"Minimum the gas limit may ever be"},gasLimitBoundDivisor:{v:1024,d:"The bound divisor of the gas limit, used in update calculations"},maxRefundQuotient:{v:2,d:"Maximum refund quotient; max tx refund is min(tx.gasUsed/maxRefundQuotient, tx.gasRefund)"}},gasPrices:{base:{v:2,d:"Gas base cost, used e.g. for ChainID opcode (Istanbul)"},exp:{v:10,d:"Base fee of the EXP opcode"},expByte:{v:10,d:"Times ceil(log256(exponent)) for the EXP instruction"},keccak256:{v:30,d:"Base fee of the SHA3 opcode"},keccak256Word:{v:6,d:"Once per word of the SHA3 operation's data"},sload:{v:50,d:"Base fee of the SLOAD opcode"},sstoreSet:{v:2e4,d:"Once per SSTORE operation if the zeroness changes from zero"},sstoreReset:{v:5e3,d:"Once per SSTORE operation if the zeroness does not change from zero"},sstoreRefund:{v:15e3,d:"Once per SSTORE operation if the zeroness changes to zero"},jumpdest:{v:1,d:"Base fee of the JUMPDEST opcode"},log:{v:375,d:"Base fee of the LOG opcode"},logData:{v:8,d:"Per byte in a LOG* operation's data"},logTopic:{v:375,d:"Multiplied by the * of the LOG*, per LOG transaction. e.g. LOG0 incurs 0 * c_txLogTopicGas, LOG4 incurs 4 * c_txLogTopicGas"},create:{v:32e3,d:"Base fee of the CREATE opcode"},call:{v:40,d:"Base fee of the CALL opcode"},callStipend:{v:2300,d:"Free gas given at beginning of call"},callValueTransfer:{v:9e3,d:"Paid for CALL when the value transfor is non-zero"},callNewAccount:{v:25e3,d:"Paid for CALL when the destination address didn't exist prior"},selfdestructRefund:{v:24e3,d:"Refunded following a selfdestruct operation"},memory:{v:3,d:"Times the address of the (highest referenced byte in memory + 1). NOTE: referencing happens on read, write and in instructions such as RETURN and CALL"},quadCoeffDiv:{v:512,d:"Divisor for the quadratic particle of the memory cost equation"},createData:{v:200,d:""},tx:{v:21e3,d:"Per transaction. NOTE: Not payable on data of calls between transactions"},txCreation:{v:32e3,d:"The cost of creating a contract via tx"},txDataZero:{v:4,d:"Per byte of data attached to a transaction that equals zero. NOTE: Not payable on data of calls between transactions"},txDataNonZero:{v:68,d:"Per byte of data attached to a transaction that is not equal to zero. NOTE: Not payable on data of calls between transactions"},copy:{v:3,d:"Multiplied by the number of 32-byte words that are copied (round up) for any *COPY operation and added"},ecRecover:{v:3e3,d:""},sha256:{v:60,d:""},sha256Word:{v:12,d:""},ripemd160:{v:600,d:""},ripemd160Word:{v:120,d:""},identity:{v:15,d:""},identityWord:{v:3,d:""},stop:{v:0,d:"Base fee of the STOP opcode"},add:{v:3,d:"Base fee of the ADD opcode"},mul:{v:5,d:"Base fee of the MUL opcode"},sub:{v:3,d:"Base fee of the SUB opcode"},div:{v:5,d:"Base fee of the DIV opcode"},sdiv:{v:5,d:"Base fee of the SDIV opcode"},mod:{v:5,d:"Base fee of the MOD opcode"},smod:{v:5,d:"Base fee of the SMOD opcode"},addmod:{v:8,d:"Base fee of the ADDMOD opcode"},mulmod:{v:8,d:"Base fee of the MULMOD opcode"},signextend:{v:5,d:"Base fee of the SIGNEXTEND opcode"},lt:{v:3,d:"Base fee of the LT opcode"},gt:{v:3,d:"Base fee of the GT opcode"},slt:{v:3,d:"Base fee of the SLT opcode"},sgt:{v:3,d:"Base fee of the SGT opcode"},eq:{v:3,d:"Base fee of the EQ opcode"},iszero:{v:3,d:"Base fee of the ISZERO opcode"},and:{v:3,d:"Base fee of the AND opcode"},or:{v:3,d:"Base fee of the OR opcode"},xor:{v:3,d:"Base fee of the XOR opcode"},not:{v:3,d:"Base fee of the NOT opcode"},byte:{v:3,d:"Base fee of the BYTE opcode"},address:{v:2,d:"Base fee of the ADDRESS opcode"},balance:{v:20,d:"Base fee of the BALANCE opcode"},origin:{v:2,d:"Base fee of the ORIGIN opcode"},caller:{v:2,d:"Base fee of the CALLER opcode"},callvalue:{v:2,d:"Base fee of the CALLVALUE opcode"},calldataload:{v:3,d:"Base fee of the CALLDATALOAD opcode"},calldatasize:{v:2,d:"Base fee of the CALLDATASIZE opcode"},calldatacopy:{v:3,d:"Base fee of the CALLDATACOPY opcode"},codesize:{v:2,d:"Base fee of the CODESIZE opcode"},codecopy:{v:3,d:"Base fee of the CODECOPY opcode"},gasprice:{v:2,d:"Base fee of the GASPRICE opcode"},extcodesize:{v:20,d:"Base fee of the EXTCODESIZE opcode"},extcodecopy:{v:20,d:"Base fee of the EXTCODECOPY opcode"},blockhash:{v:20,d:"Base fee of the BLOCKHASH opcode"},coinbase:{v:2,d:"Base fee of the COINBASE opcode"},timestamp:{v:2,d:"Base fee of the TIMESTAMP opcode"},number:{v:2,d:"Base fee of the NUMBER opcode"},difficulty:{v:2,d:"Base fee of the DIFFICULTY opcode"},gaslimit:{v:2,d:"Base fee of the GASLIMIT opcode"},pop:{v:2,d:"Base fee of the POP opcode"},mload:{v:3,d:"Base fee of the MLOAD opcode"},mstore:{v:3,d:"Base fee of the MSTORE opcode"},mstore8:{v:3,d:"Base fee of the MSTORE8 opcode"},sstore:{v:0,d:"Base fee of the SSTORE opcode"},jump:{v:8,d:"Base fee of the JUMP opcode"},jumpi:{v:10,d:"Base fee of the JUMPI opcode"},pc:{v:2,d:"Base fee of the PC opcode"},msize:{v:2,d:"Base fee of the MSIZE opcode"},gas:{v:2,d:"Base fee of the GAS opcode"},push:{v:3,d:"Base fee of the PUSH opcode"},dup:{v:3,d:"Base fee of the DUP opcode"},swap:{v:3,d:"Base fee of the SWAP opcode"},callcode:{v:40,d:"Base fee of the CALLCODE opcode"},return:{v:0,d:"Base fee of the RETURN opcode"},invalid:{v:0,d:"Base fee of the INVALID opcode"},selfdestruct:{v:0,d:"Base fee of the SELFDESTRUCT opcode"}},vm:{stackLimit:{v:1024,d:"Maximum size of VM stack allowed"},callCreateDepth:{v:1024,d:"Maximum depth of call/create stack"},maxExtraDataSize:{v:32,d:"Maximum size extra data may be after Genesis"}},pow:{minimumDifficulty:{v:131072,d:"The minimum that the difficulty may ever be"},difficultyBoundDivisor:{v:2048,d:"The bound divisor of the difficulty, used in the update calculations"},durationLimit:{v:13,d:"The decision boundary on the blocktime duration used to determine whether difficulty should go up or not"},epochDuration:{v:3e4,d:"Duration between proof-of-work epochs"},timebombPeriod:{v:1e5,d:"Exponential difficulty timebomb period"},minerReward:{v:BigInt("5000000000000000000"),d:"the amount a miner get rewarded for mining a block"},difficultyBombDelay:{v:0,d:"the amount of blocks to delay the difficulty bomb with"}}},homestead:{name:"homestead",comment:"Homestead hardfork with protocol and network changes",url:"https://eips.ethereum.org/EIPS/eip-606",status:Gn.Final,gasPrices:{delegatecall:{v:40,d:"Base fee of the DELEGATECALL opcode"}}},dao:{name:"dao",comment:"DAO rescue hardfork",url:"https://eips.ethereum.org/EIPS/eip-779",status:Gn.Final},tangerineWhistle:{name:"tangerineWhistle",comment:"Hardfork with gas cost changes for IO-heavy operations",url:"https://eips.ethereum.org/EIPS/eip-608",status:Gn.Final,gasPrices:{sload:{v:200,d:"Once per SLOAD operation"},call:{v:700,d:"Once per CALL operation & message call transaction"},extcodesize:{v:700,d:"Base fee of the EXTCODESIZE opcode"},extcodecopy:{v:700,d:"Base fee of the EXTCODECOPY opcode"},balance:{v:400,d:"Base fee of the BALANCE opcode"},delegatecall:{v:700,d:"Base fee of the DELEGATECALL opcode"},callcode:{v:700,d:"Base fee of the CALLCODE opcode"},selfdestruct:{v:5e3,d:"Base fee of the SELFDESTRUCT opcode"}}},spuriousDragon:{name:"spuriousDragon",comment:"HF with EIPs for simple replay attack protection, EXP cost increase, state trie clearing, contract code size limit",url:"https://eips.ethereum.org/EIPS/eip-607",status:Gn.Final,gasPrices:{expByte:{v:50,d:"Times ceil(log256(exponent)) for the EXP instruction"}},vm:{maxCodeSize:{v:24576,d:"Maximum length of contract code"}}},byzantium:{name:"byzantium",comment:"Hardfork with new precompiles, instructions and other protocol changes",url:"https://eips.ethereum.org/EIPS/eip-609",status:Gn.Final,gasPrices:{modexpGquaddivisor:{v:20,d:"Gquaddivisor from modexp precompile for gas calculation"},ecAdd:{v:500,d:"Gas costs for curve addition precompile"},ecMul:{v:4e4,d:"Gas costs for curve multiplication precompile"},ecPairing:{v:1e5,d:"Base gas costs for curve pairing precompile"},ecPairingWord:{v:8e4,d:"Gas costs regarding curve pairing precompile input length"},revert:{v:0,d:"Base fee of the REVERT opcode"},staticcall:{v:700,d:"Base fee of the STATICCALL opcode"},returndatasize:{v:2,d:"Base fee of the RETURNDATASIZE opcode"},returndatacopy:{v:3,d:"Base fee of the RETURNDATACOPY opcode"}},pow:{minerReward:{v:BigInt("3000000000000000000"),d:"the amount a miner get rewarded for mining a block"},difficultyBombDelay:{v:3e6,d:"the amount of blocks to delay the difficulty bomb with"}}},constantinople:{name:"constantinople",comment:"Postponed hardfork including EIP-1283 (SSTORE gas metering changes)",url:"https://eips.ethereum.org/EIPS/eip-1013",status:Gn.Final,gasPrices:{netSstoreNoopGas:{v:200,d:"Once per SSTORE operation if the value doesn't change"},netSstoreInitGas:{v:2e4,d:"Once per SSTORE operation from clean zero"},netSstoreCleanGas:{v:5e3,d:"Once per SSTORE operation from clean non-zero"},netSstoreDirtyGas:{v:200,d:"Once per SSTORE operation from dirty"},netSstoreClearRefund:{v:15e3,d:"Once per SSTORE operation for clearing an originally existing storage slot"},netSstoreResetRefund:{v:4800,d:"Once per SSTORE operation for resetting to the original non-zero value"},netSstoreResetClearRefund:{v:19800,d:"Once per SSTORE operation for resetting to the original zero value"},shl:{v:3,d:"Base fee of the SHL opcode"},shr:{v:3,d:"Base fee of the SHR opcode"},sar:{v:3,d:"Base fee of the SAR opcode"},extcodehash:{v:400,d:"Base fee of the EXTCODEHASH opcode"},create2:{v:32e3,d:"Base fee of the CREATE2 opcode"}},pow:{minerReward:{v:BigInt("2000000000000000000"),d:"The amount a miner gets rewarded for mining a block"},difficultyBombDelay:{v:5e6,d:"the amount of blocks to delay the difficulty bomb with"}}},petersburg:{name:"petersburg",comment:"Aka constantinopleFix, removes EIP-1283, activate together with or after constantinople",url:"https://eips.ethereum.org/EIPS/eip-1716",status:Gn.Final,gasPrices:{netSstoreNoopGas:{v:null,d:"Removed along EIP-1283"},netSstoreInitGas:{v:null,d:"Removed along EIP-1283"},netSstoreCleanGas:{v:null,d:"Removed along EIP-1283"},netSstoreDirtyGas:{v:null,d:"Removed along EIP-1283"},netSstoreClearRefund:{v:null,d:"Removed along EIP-1283"},netSstoreResetRefund:{v:null,d:"Removed along EIP-1283"},netSstoreResetClearRefund:{v:null,d:"Removed along EIP-1283"}}},istanbul:{name:"istanbul",comment:"HF targeted for December 2019 following the Constantinople/Petersburg HF",url:"https://eips.ethereum.org/EIPS/eip-1679",status:Gn.Final,gasConfig:{},gasPrices:{blake2Round:{v:1,d:"Gas cost per round for the Blake2 F precompile"},ecAdd:{v:150,d:"Gas costs for curve addition precompile"},ecMul:{v:6e3,d:"Gas costs for curve multiplication precompile"},ecPairing:{v:45e3,d:"Base gas costs for curve pairing precompile"},ecPairingWord:{v:34e3,d:"Gas costs regarding curve pairing precompile input length"},txDataNonZero:{v:16,d:"Per byte of data attached to a transaction that is not equal to zero. NOTE: Not payable on data of calls between transactions"},sstoreSentryGasEIP2200:{v:2300,d:"Minimum gas required to be present for an SSTORE call, not consumed"},sstoreNoopGasEIP2200:{v:800,d:"Once per SSTORE operation if the value doesn't change"},sstoreDirtyGasEIP2200:{v:800,d:"Once per SSTORE operation if a dirty value is changed"},sstoreInitGasEIP2200:{v:2e4,d:"Once per SSTORE operation from clean zero to non-zero"},sstoreInitRefundEIP2200:{v:19200,d:"Once per SSTORE operation for resetting to the original zero value"},sstoreCleanGasEIP2200:{v:5e3,d:"Once per SSTORE operation from clean non-zero to something else"},sstoreCleanRefundEIP2200:{v:4200,d:"Once per SSTORE operation for resetting to the original non-zero value"},sstoreClearRefundEIP2200:{v:15e3,d:"Once per SSTORE operation for clearing an originally existing storage slot"},balance:{v:700,d:"Base fee of the BALANCE opcode"},extcodehash:{v:700,d:"Base fee of the EXTCODEHASH opcode"},chainid:{v:2,d:"Base fee of the CHAINID opcode"},selfbalance:{v:5,d:"Base fee of the SELFBALANCE opcode"},sload:{v:800,d:"Base fee of the SLOAD opcode"}}},muirGlacier:{name:"muirGlacier",comment:"HF to delay the difficulty bomb",url:"https://eips.ethereum.org/EIPS/eip-2384",status:Gn.Final,pow:{difficultyBombDelay:{v:9e6,d:"the amount of blocks to delay the difficulty bomb with"}}},berlin:{name:"berlin",comment:"HF targeted for July 2020 following the Muir Glacier HF",url:"https://eips.ethereum.org/EIPS/eip-2070",status:Gn.Final,eips:[2565,2929,2718,2930]},london:{name:"london",comment:"HF targeted for July 2021 following the Berlin fork",url:"https://github.com/ethereum/eth1.0-specs/blob/master/network-upgrades/mainnet-upgrades/london.md",status:Gn.Final,eips:[1559,3198,3529,3541]},arrowGlacier:{name:"arrowGlacier",comment:"HF to delay the difficulty bomb",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/arrow-glacier.md",status:Gn.Final,eips:[4345]},grayGlacier:{name:"grayGlacier",comment:"Delaying the difficulty bomb to Mid September 2022",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/gray-glacier.md",status:Gn.Final,eips:[5133]},paris:{name:"paris",comment:"Hardfork to upgrade the consensus mechanism to Proof-of-Stake",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/merge.md",status:Gn.Final,consensus:{type:"pos",algorithm:"casper",casper:{}},eips:[3675,4399]},mergeForkIdTransition:{name:"mergeForkIdTransition",comment:"Pre-merge hardfork to fork off non-upgraded clients",url:"https://eips.ethereum.org/EIPS/eip-3675",status:Gn.Final,eips:[]},shanghai:{name:"shanghai",comment:"Next feature hardfork after the merge hardfork having withdrawals, warm coinbase, push0, limit/meter initcode",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/shanghai.md",status:Gn.Final,eips:[3651,3855,3860,4895]},cancun:{name:"cancun",comment:"Next feature hardfork after shanghai, includes proto-danksharding EIP 4844 blobs (still WIP hence not for production use), transient storage opcodes, parent beacon block root availability in EVM, selfdestruct only in same transaction, and blob base fee opcode",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/cancun.md",status:Gn.Final,eips:[1153,4844,4788,5656,6780,7516]},prague:{name:"prague",comment:"Next feature hardfork after cancun, internally used for verkle testing/implementation (incomplete/experimental)",url:"https://github.com/ethereum/execution-specs/blob/master/network-upgrades/mainnet-upgrades/prague.md",status:Gn.Draft,eips:[6800]}};function jn(t,e=!0){const{name:r,config:n,difficulty:i,mixHash:o,gasLimit:s,coinbase:a,baseFeePerGas:h,excessBlobGas:c}=t;let{extraData:u,timestamp:l,nonce:f}=t;const d=Number(l),{chainId:p}=n;if(""===u&&(u="0x"),Xt(l)||(l=de(parseInt(l))),18!==f.length&&(f=function(t){return t&&"0x0"!==t?Xt(t)?"0x"+(t=>{if("string"!=typeof t)throw new Error("[stripHexPrefix] input must be type 'string', received "+typeof t);return Xt(t)?t.slice(2):t})(t).padStart(16,"0"):"0x"+t.padStart(16,"0"):"0x0000000000000000"}(f)),n.eip155Block!==n.eip158Block)throw new Error("EIP155 block number must equal EIP 158 block number since both are part of SpuriousDragon hardfork and the client only supports activating the full hardfork");const m={name:r,chainId:p,networkId:p,genesis:{timestamp:l,gasLimit:s,difficulty:i,nonce:f,extraData:u,mixHash:o,coinbase:a,baseFeePerGas:h,excessBlobGas:c},hardfork:void 0,hardforks:[],bootstrapNodes:[],consensus:void 0!==n.clique?{type:"poa",algorithm:"clique",clique:{period:n.clique.period??n.clique.blockperiodseconds,epoch:n.clique.epoch??n.clique.epochlength}}:{type:"pow",algorithm:"ethash",ethash:{}}},g={[Ln.Homestead]:{name:"homesteadBlock"},[Ln.Dao]:{name:"daoForkBlock"},[Ln.TangerineWhistle]:{name:"eip150Block"},[Ln.SpuriousDragon]:{name:"eip155Block"},[Ln.Byzantium]:{name:"byzantiumBlock"},[Ln.Constantinople]:{name:"constantinopleBlock"},[Ln.Petersburg]:{name:"petersburgBlock"},[Ln.Istanbul]:{name:"istanbulBlock"},[Ln.MuirGlacier]:{name:"muirGlacierBlock"},[Ln.Berlin]:{name:"berlinBlock"},[Ln.London]:{name:"londonBlock"},[Ln.MergeForkIdTransition]:{name:"mergeForkBlock",postMerge:e},[Ln.Shanghai]:{name:"shanghaiTime",postMerge:!0,isTimestamp:!0},[Ln.Cancun]:{name:"cancunTime",postMerge:!0,isTimestamp:!0},[Ln.Prague]:{name:"pragueTime",postMerge:!0,isTimestamp:!0}},y=Object.keys(g).reduce(((t,e)=>(t[g[e].name]=e,t)),{}),b=Object.keys(n).filter((t=>void 0!==y[t]&&void 0!==n[t]&&null!==n[t]));m.hardforks=b.map((t=>({name:y[t],block:!0===g[y[t]].isTimestamp||"number"!=typeof n[t]?null:n[t],timestamp:!0===g[y[t]].isTimestamp&&"number"==typeof n[t]?n[t]:void 0}))).filter((t=>null!==t.block||void 0!==t.timestamp)),m.hardforks.sort((function(t,e){return(t.block??1/0)-(e.block??1/0)})),m.hardforks.sort((function(t,e){return(t.timestamp??0)-(e.timestamp??0)}));for(const t of m.hardforks)t.timestamp===d&&(t.timestamp=0);if(void 0!==n.terminalTotalDifficulty){const t={name:Ln.Paris,ttd:n.terminalTotalDifficulty,block:null},e=m.hardforks.findIndex((t=>!0===g[t.name]?.postMerge));-1!==e?m.hardforks.splice(e,0,t):m.hardforks.push(t)}const w=m.hardforks.length>0?m.hardforks.slice(-1)[0]:void 0;return m.hardfork=w?.name,m.hardforks.unshift({name:Ln.Chainstart,block:0}),m}class qn{constructor(t){this._eips=[],this._paramsCache={},this._activatedEIPsCache=[],this.events=new Tr.EventEmitter,this._customChains=t.customChains??[],this._chainParams=this.setChain(t.chain),this.DEFAULT_HARDFORK=this._chainParams.defaultHardfork??Ln.Shanghai,this.HARDFORK_CHANGES=this.hardforks().map((t=>[t.name,zn[t.name]??(this._chainParams.customHardforks&&this._chainParams.customHardforks[t.name])])),this._hardfork=this.DEFAULT_HARDFORK,void 0!==t.hardfork&&this.setHardfork(t.hardfork),t.eips&&this.setEIPs(t.eips),this.customCrypto=t.customCrypto??{},0===Object.keys(this._paramsCache).length&&(this._buildParamsCache(),this._buildActivatedEIPsCache())}static custom(t,e={}){const r=e.baseChain??"mainnet",n={...qn._getChainParams(r)};if(n.name="custom-chain","string"!=typeof t)return new qn({chain:{...n,...t},...e});if(t===Dn.PolygonMainnet)return qn.custom({name:Dn.PolygonMainnet,chainId:137,networkId:137},e);if(t===Dn.PolygonMumbai)return qn.custom({name:Dn.PolygonMumbai,chainId:80001,networkId:80001},e);if(t===Dn.ArbitrumOne)return qn.custom({name:Dn.ArbitrumOne,chainId:42161,networkId:42161},e);if(t===Dn.xDaiChain)return qn.custom({name:Dn.xDaiChain,chainId:100,networkId:100},e);if(t===Dn.OptimisticKovan)return qn.custom({name:Dn.OptimisticKovan,chainId:69,networkId:69},{hardfork:Ln.Berlin,...e});if(t===Dn.OptimisticEthereum)return qn.custom({name:Dn.OptimisticEthereum,chainId:10,networkId:10},{hardfork:Ln.Berlin,...e});throw new Error(`Custom chain ${t} not supported`)}static fromGethGenesis(t,{chain:e,eips:r,genesisHash:n,hardfork:i,mergeForkIdPostMerge:o,customCrypto:s}){const a=function(t,e,r){try{const n=["config","difficulty","gasLimit","nonce","alloc"];if(n.some((e=>!(e in t)))){const e=n.filter((e=>!(e in t)));throw new Error(`Invalid format, expected geth genesis field "${e}" missing`)}return void 0!==e&&(t.name=e),jn(t,r)}catch(t){throw new Error(`Error parsing parameters file: ${t.message}`)}}(t,e,o),h=new qn({chain:a.name??"custom",customChains:[a],eips:r,hardfork:i??a.hardfork,customCrypto:s});return void 0!==n&&h.setForkHashes(n),h}static isSupportedChainId(t){const e=this.getInitializedChains();return Boolean(e.names[t.toString()])}static _getChainParams(t,e){const r=this.getInitializedChains(e);if("number"==typeof t||"bigint"==typeof t){if(t=t.toString(),r.names[t])return r[r.names[t]];throw new Error(`Chain with ID ${t} not supported`)}if(void 0!==r[t])return r[t];throw new Error(`Chain with name ${t} not supported`)}setChain(t){if("number"==typeof t||"bigint"==typeof t||"string"==typeof t)this._chainParams=qn._getChainParams(t,this._customChains);else{if("object"!=typeof t)throw new Error("Wrong input format");{if(this._customChains.length>0)throw new Error("Chain must be a string, number, or bigint when initialized with customChains passed in");const e=["networkId","genesis","hardforks","bootstrapNodes"];for(const r of e)if(!(r in t))throw new Error(`Missing required chain parameter: ${r}`);this._chainParams=t}}for(const t of this.hardforks())if(void 0===t.block)throw new Error("Hardfork cannot have undefined block number");return this._chainParams}setHardfork(t){let e=!1;for(const r of this.HARDFORK_CHANGES)r[0]===t&&(this._hardfork!==t&&(this._hardfork=t,this._buildParamsCache(),this._buildActivatedEIPsCache(),this.events.emit("hardforkChanged",t)),e=!0);if(!e)throw new Error(`Hardfork with name ${t} not supported`)}getHardforkBy(t){const e=Ir(t.blockNumber,Ar.BigInt),r=Ir(t.td,Ar.BigInt),n=Ir(t.timestamp,Ar.BigInt),i=this.hardforks().filter((t=>null!==t.block||null!==t.ttd&&void 0!==t.ttd||void 0!==t.timestamp)),o=i.findIndex((t=>null!==t.ttd&&void 0!==t.ttd));if(i.slice(o+1).findIndex((t=>null!==t.ttd&&void 0!==t.ttd))>=0)throw Error("More than one merge hardforks found with ttd specified");let s=i.findIndex((t=>void 0!==e&&null!==t.block&&BigInt(t.block)>e||void 0!==n&&void 0!==t.timestamp&&BigInt(t.timestamp)>n));if(-1===s)s=i.length;else if(0===s)throw Error("Must have at least one hardfork at block 0");if(void 0===n&&(s-=i.slice(0,s).reverse().findIndex((t=>null!==t.block||void 0!==t.ttd))),s-=1,null===i[s].block&&void 0===i[s].timestamp)(null==r||BigInt(i[s].ttd)>r)&&(s-=1);else if(o>=0&&null!=r){if(s>=o&&BigInt(i[o].ttd)>r)throw Error("Maximum HF determined by total difficulty is lower than the block number HF");if(s<o&&BigInt(i[o].ttd)<r)throw Error("HF determined by block number is lower than the minimum total difficulty HF")}const a=s;for(;s<i.length-1&&i[s].block===i[s+1].block&&i[s].timestamp===i[s+1].timestamp;s++);if(void 0!==n){if(i.slice(0,a).reduce(((t,e)=>Math.max(Number(e.timestamp??"0"),t)),0)>n)throw Error("Maximum HF determined by timestamp is lower than the block number/ttd HF");if(i.slice(s+1).reduce(((t,e)=>Math.min(Number(e.timestamp??n),t)),Number(n))<n)throw Error("Maximum HF determined by block number/ttd is lower than timestamp HF")}return i[s].name}setHardforkBy(t){const e=this.getHardforkBy(t);return this.setHardfork(e),e}_getHardfork(t){const e=this.hardforks();for(const r of e)if(r.name===t)return r;return null}setEIPs(t=[]){for(const e of t){if(!(e in Un))throw new Error(`${e} not supported`);const t=this.gteHardfork(Un[e].minimumHardfork);if(!t)throw new Error(`${e} cannot be activated on hardfork ${this.hardfork()}, minimumHardfork: ${t}`)}this._eips=t,this._buildParamsCache(),this._buildActivatedEIPsCache();for(const e of t)if(void 0!==Un[e].requiredEIPs)for(const r of Un[e].requiredEIPs)if(!t.includes(r)&&!this.isActivatedEIP(r))throw new Error(`${e} requires EIP ${r}, but is not included in the EIP list`)}_mergeWithParamsCache(t){this._paramsCache.gasConfig={...this._paramsCache.gasConfig,...t.gasConfig},this._paramsCache.gasPrices={...this._paramsCache.gasPrices,...t.gasPrices},this._paramsCache.pow={...this._paramsCache.pow,...t.pow},this._paramsCache.sharding={...this._paramsCache.sharding,...t.sharding},this._paramsCache.vm={...this._paramsCache.vm,...t.vm}}_buildParamsCache(){this._paramsCache={};const t=this.hardfork();for(const e of this.HARDFORK_CHANGES){if("eips"in e[1]){const t=e[1].eips;for(const e of t){if(!(e in Un))throw new Error(`${e} not supported`);this._mergeWithParamsCache(Un[e])}}else this._mergeWithParamsCache(e[1]);if(e[0]===t)break}for(const t of this._eips){if(!(t in Un))throw new Error(`${t} not supported`);this._mergeWithParamsCache(Un[t])}}_buildActivatedEIPsCache(){this._activatedEIPsCache=[];for(const t of this.HARDFORK_CHANGES){const e=t[1];this.gteHardfork(e.name)&&"eips"in e&&(this._activatedEIPsCache=this._activatedEIPsCache.concat(e.eips))}this._activatedEIPsCache=this._activatedEIPsCache.concat(this._eips)}param(t,e){let r=null;return void 0!==this._paramsCache[t]&&void 0!==this._paramsCache[t][e]&&(r=this._paramsCache[t][e].v),BigInt(r??0)}paramByHardfork(t,e,r){let n=null;for(const i of this.HARDFORK_CHANGES){if("eips"in i[1]){const r=i[1].eips;for(const i of r){const r=this.paramByEIP(t,e,i);n="bigint"==typeof r?r:n}}else void 0!==i[1][t]&&void 0!==i[1][t][e]&&(n=i[1][t][e].v);if(i[0]===r)break}return BigInt(n??0)}paramByEIP(t,e,r){if(!(r in Un))throw new Error(`${r} not supported`);const n=Un[r];if(!(t in n))return;if(void 0===n[t][e])return;const i=n[t][e].v;return BigInt(i)}paramByBlock(t,e,r,n,i){const o=this.getHardforkBy({blockNumber:r,td:n,timestamp:i});return this.paramByHardfork(t,e,o)}isActivatedEIP(t){return!!this._activatedEIPsCache.includes(t)}hardforkIsActiveOnBlock(t,e){e=Ir(e,Ar.BigInt),t=t??this._hardfork;const r=this.hardforkBlock(t);return"bigint"==typeof r&&r!==Ce&&e>=r}activeOnBlock(t){return this.hardforkIsActiveOnBlock(null,t)}hardforkGteHardfork(t,e){t=t??this._hardfork;const r=this.hardforks();let n=-1,i=-1,o=0;for(const s of r)s.name===t&&(n=o),s.name===e&&(i=o),o+=1;return n>=i&&-1!==i}gteHardfork(t){return this.hardforkGteHardfork(null,t)}hardforkBlock(t){t=t??this._hardfork;const e=this._getHardfork(t)?.block;return null==e?null:BigInt(e)}hardforkTimestamp(t){t=t??this._hardfork;const e=this._getHardfork(t)?.timestamp;return null==e?null:BigInt(e)}eipBlock(t){for(const e of this.HARDFORK_CHANGES){const r=e[1];if("eips"in r&&r.eips.includes(t))return this.hardforkBlock(e[0])}return null}eipTimestamp(t){for(const e of this.HARDFORK_CHANGES){const r=e[1];if("eips"in r&&r.eips.includes(t))return this.hardforkTimestamp(e[0])}return null}hardforkTTD(t){t=t??this._hardfork;const e=this._getHardfork(t)?.ttd;return null==e?null:BigInt(e)}nextHardforkBlockOrTimestamp(t){t=t??this._hardfork;const e=this.hardforks();let r=e.findIndex((e=>e.name===t));if(t===Ln.Paris&&(r-=1),r<0)return null;let n=e[r].timestamp??e[r].block;n=null!=n?Number(n):null;const i=e.slice(r+1).find((t=>{let e=t.timestamp??t.block;return e=null!=e?Number(e):null,t.name!==Ln.Paris&&null!=e&&e!==n}));if(void 0===i)return null;const o=i.timestamp??i.block;return null==o?null:BigInt(o)}_calcForkHash(t,e){let r=new Uint8Array(0),n=0;for(const e of this.hardforks()){const{block:i,timestamp:o,name:s}=e;let a=o??i;if(a=null!==a?Number(a):null,"number"==typeof a&&0!==a&&a!==n&&s!==Ln.Paris){const t=fe("0x"+a.toString(16).padStart(16,"0"));r=xe(r,t),n=a}if(e.name===t)break}const i=xe(e,r);return ce(pe(((t,e)=>{let r=0===e?0:~e;for(let e=0;e<t.length;e++)r=Tn[255&(r^t[e])]^r>>>8;return~r})(i,o)>>>0>>>0));var o}forkHash(t,e){t=t??this._hardfork;const r=this._getHardfork(t);if(null===r||null===r?.block&&void 0===r?.timestamp&&void 0===r?.ttd)throw new Error("No fork hash calculation possible for future hardfork");if(null!=r?.forkHash)return r.forkHash;if(!e)throw new Error("genesisHash required for forkHash calculation");return this._calcForkHash(t,e)}hardforkForForkHash(t){const e=this.hardforks().filter((e=>e.forkHash===t));return e.length>=1?e[e.length-1]:null}setForkHashes(t){for(const e of this.hardforks()){const r=e.timestamp??e.block;null!==e.forkHash&&void 0!==e.forkHash||null==r&&void 0===e.ttd||(e.forkHash=this.forkHash(e.name,t))}}genesis(){return this._chainParams.genesis}hardforks(){const t=this._chainParams.hardforks;return void 0!==this._chainParams.customHardforks&&this._chainParams.customHardforks,t}bootstrapNodes(){return this._chainParams.bootstrapNodes}dnsNetworks(){return this._chainParams.dnsNetworks}hardfork(){return this._hardfork}chainId(){return BigInt(this._chainParams.chainId)}chainName(){return this._chainParams.name}networkId(){return BigInt(this._chainParams.networkId)}eips(){return this._eips}consensusType(){const t=this.hardfork();let e;for(const r of this.HARDFORK_CHANGES)if("consensus"in r[1]&&(e=r[1].consensus.type),r[0]===t)break;return e??this._chainParams.consensus.type}consensusAlgorithm(){const t=this.hardfork();let e;for(const r of this.HARDFORK_CHANGES)if("consensus"in r[1]&&(e=r[1].consensus.algorithm),r[0]===t)break;return e??this._chainParams.consensus.algorithm}consensusConfig(){const t=this.hardfork();let e;for(const r of this.HARDFORK_CHANGES){if("consensus"in r[1]){const t=r[1],n=t.consensus.algorithm;e=t.consensus[n]}if(r[0]===t)break}return e??this._chainParams.consensus[this.consensusAlgorithm()]??{}}copy(){const t=Object.assign(Object.create(Object.getPrototypeOf(this)),this);return t.events=new Tr.EventEmitter,t}static getInitializedChains(t){const e={};for(const[t,r]of Object.entries(On))e[r]=t.toLowerCase();const r={...Rn};if(t)for(const n of t){const{name:t}=n;e[n.chainId.toString()]=t,r[t]=n}return r.names=e,r}}var $n,Wn;!function(t){t[t.EIP155ReplayProtection=155]="EIP155ReplayProtection",t[t.EIP1559FeeMarket=1559]="EIP1559FeeMarket",t[t.EIP2718TypedTransaction=2718]="EIP2718TypedTransaction",t[t.EIP2930AccessLists=2930]="EIP2930AccessLists"}($n||($n={})),function(t){t[t.Legacy=0]="Legacy",t[t.AccessListEIP2930=1]="AccessListEIP2930",t[t.FeeMarketEIP1559=2]="FeeMarketEIP1559",t[t.BlobEIP4844=3]="BlobEIP4844"}(Wn||(Wn={}));class Vn{static getAccessListData(t){let e,r;if(function(t){if(0===t.length)return!0;const e=t[0];return!!Array.isArray(e)}(t)){r=t??[];const n=[];for(let t=0;t<r.length;t++){const e=r[t],i=ce(e[0]),o=[];for(let t=0;t<e[1].length;t++)o.push(ce(e[1][t]));const s={address:i,storageKeys:o};n.push(s)}e=n}else{e=t;const n=[];for(let e=0;e<t.length;e++){const r=t[e],i=fe(r.address),o=[];for(let t=0;t<r.storageKeys.length;t++)o.push(fe(r.storageKeys[t]));n.push([i,o])}r=n}return{AccessListJSON:e,accessList:r}}static verifyAccessList(t){for(let e=0;e<t.length;e++){const r=t[e],n=r[0],i=r[1];if(void 0!==r[2])throw new Error("Access list item cannot have 3 elements. It can only have an address, and an array of storage slots.");if(20!==n.length)throw new Error("Invalid EIP-2930 transaction: address length should be 20 bytes");for(let t=0;t<i.length;t++)if(32!==i[t].length)throw new Error("Invalid EIP-2930 transaction: storage slot length should be 32 bytes")}}static getAccessListJSON(t){const e=[];for(let r=0;r<t.length;r++){const n=t[r],i={address:ce(ye(n[0],20)),storageKeys:[]},o=n[1];for(let t=0;t<o.length;t++){const e=o[t];i.storageKeys.push(ce(ye(e,32)))}e.push(i)}return e}static getDataFeeEIP2930(t,e){const r=e.param("gasPrices","accessListStorageKeyCost"),n=e.param("gasPrices","accessListAddressCost");let i=0;for(let e=0;e<t.length;e++)i+=t[e][1].length;return t.length*Number(n)+i*Number(r)}}function Kn(t){return fe("0x"+t.toString(16).padStart(2,"0"))}class Zn{constructor(t,e){this.cache={hash:void 0,dataFee:void 0,senderPubKey:void 0},this.activeCapabilities=[],this.DEFAULT_CHAIN=On.Mainnet;const{nonce:r,gasLimit:n,to:i,value:o,data:s,v:a,r:h,s:c,type:u}=t;this._type=Number(le(we(u))),this.txOptions=e;const l=we(""===i?"0x":i),f=we(""===a?"0x":a),d=we(""===h?"0x":h),p=we(""===c?"0x":c);this.nonce=le(we(""===r?"0x":r)),this.gasLimit=le(we(""===n?"0x":n)),this.to=l.length>0?new _r(l):void 0,this.value=le(we(""===o?"0x":o)),this.data=we(""===s?"0x":s),this.v=f.length>0?le(f):void 0,this.r=d.length>0?le(d):void 0,this.s=p.length>0?le(p):void 0,this._validateCannotExceedMaxInteger({value:this.value,r:this.r,s:this.s}),this._validateCannotExceedMaxInteger({gasLimit:this.gasLimit},64),this._validateCannotExceedMaxInteger({nonce:this.nonce},64,!0);const m=void 0===this.to||null===this.to,g=e.allowUnlimitedInitCodeSize??!1,y=e.common??this._getCommon();m&&y.isActivatedEIP(3860)&&!1===g&&function(t,e){const r=t.param("vm","maxInitCodeSize");if(r&&BigInt(e)>r)throw new Error(`the initcode size of this transaction is too large: it is ${e} while the max is ${t.param("vm","maxInitCodeSize")}`)}(y,this.data.length)}get type(){return this._type}supports(t){return this.activeCapabilities.includes(t)}getValidationErrors(){const t=[];return this.isSigned()&&!this.verifySignature()&&t.push("Invalid Signature"),this.getBaseFee()>this.gasLimit&&t.push(`gasLimit is too low. given ${this.gasLimit}, need at least ${this.getBaseFee()}`),t}isValid(){return 0===this.getValidationErrors().length}getBaseFee(){const t=this.common.param("gasPrices","tx");let e=this.getDataFee();if(t&&(e+=t),this.common.gteHardfork("homestead")&&this.toCreationAddress()){const t=this.common.param("gasPrices","txCreation");t&&(e+=t)}return e}getDataFee(){const t=this.common.param("gasPrices","txDataZero"),e=this.common.param("gasPrices","txDataNonZero");let r=Ce;for(let n=0;n<this.data.length;n++)0===this.data[n]?r+=t:r+=e;if((void 0===this.to||null===this.to)&&this.common.isActivatedEIP(3860)){const t=BigInt(Math.ceil(this.data.length/32));r+=this.common.param("gasPrices","initCodeWordCost")*t}return r}toCreationAddress(){return void 0===this.to||0===this.to.bytes.length}isSigned(){const{v:t,r:e,s:r}=this;return void 0!==t&&void 0!==e&&void 0!==r}verifySignature(){try{const t=this.getSenderPublicKey();return 0!==be(t).length}catch(t){return!1}}getSenderAddress(){return new _r(xr(this.getSenderPublicKey()))}sign(t){if(32!==t.length){const t=this._errorMsg("Private key must be 32 bytes in length.");throw new Error(t)}let e=!1;this.type===Wn.Legacy&&this.common.gteHardfork("spuriousDragon")&&!this.supports($n.EIP155ReplayProtection)&&(this.activeCapabilities.push($n.EIP155ReplayProtection),e=!0);const r=this.getHashedMessageToSign(),n=this.common.customCrypto?.ecsign??Mr,{v:i,r:o,s}=n(r,t),a=this.addSignature(i,o,s,!0);if(e){const t=this.activeCapabilities.indexOf($n.EIP155ReplayProtection);t>-1&&this.activeCapabilities.splice(t,1)}return a}toJSON(){return{type:Ee(BigInt(this.type)),nonce:Ee(this.nonce),gasLimit:Ee(this.gasLimit),to:void 0!==this.to?this.to.toString():void 0,value:Ee(this.value),data:ce(this.data),v:void 0!==this.v?Ee(this.v):void 0,r:void 0!==this.r?Ee(this.r):void 0,s:void 0!==this.s?Ee(this.s):void 0}}_getCommon(t,e){if(void 0!==e){const r=le(we(e));if(t){if(t.chainId()!==r){const e=this._errorMsg(`The chain ID does not match the chain ID of Common. Got: ${r}, expected: ${t.chainId}`);throw new Error(e)}return t.copy()}return qn.isSupportedChainId(r)?new qn({chain:r}):qn.custom({name:"custom-chain",networkId:r,chainId:r},{baseChain:this.DEFAULT_CHAIN})}return t?.copy()??new qn({chain:this.DEFAULT_CHAIN})}_validateCannotExceedMaxInteger(t,e=256,r=!1){for(const[n,i]of Object.entries(t))switch(e){case 64:if(r){if(void 0!==i&&i>=_e){const t=this._errorMsg(`${n} cannot equal or exceed MAX_UINT64 (2^64-1), given ${i}`);throw new Error(t)}}else if(void 0!==i&&i>_e){const t=this._errorMsg(`${n} cannot exceed MAX_UINT64 (2^64-1), given ${i}`);throw new Error(t)}break;case 256:if(r){if(void 0!==i&&i>=Pe){const t=this._errorMsg(`${n} cannot equal or exceed MAX_INTEGER (2^256-1), given ${i}`);throw new Error(t)}}else if(void 0!==i&&i>Pe){const t=this._errorMsg(`${n} cannot exceed MAX_INTEGER (2^256-1), given ${i}`);throw new Error(t)}break;default:{const t=this._errorMsg("unimplemented bits value");throw new Error(t)}}}static _validateNotArray(t){const e=["nonce","gasPrice","gasLimit","to","value","data","v","r","s","type","baseFee","maxFeePerGas","chainId"];for(const[r,n]of Object.entries(t))if(e.includes(r)&&Array.isArray(n))throw new Error(`${r} cannot be an array`)}_getSharedErrorPostfix(){let t="";try{t=this.isSigned()?ce(this.hash()):"not available (unsigned)"}catch(e){t="error"}let e="";try{e=this.isSigned().toString()}catch(e){t="error"}let r="";try{r=this.common.hardfork()}catch(t){r="error"}let n=`tx type=${this.type} hash=${t} nonce=${this.nonce} value=${this.value} `;return n+=`signed=${e} hf=${r}`,n}}function Yn(t,e){const r=t.maxPriorityFeePerGas,n=t.maxFeePerGas-e,i=(r<n?r:n)+e;return t.gasLimit*i+t.value}function Jn(t,e){if(void 0===e||e>t.maxFeePerGas)throw new Error("Tx cannot pay baseFee");const r=t.maxFeePerGas-e;return t.maxPriorityFeePerGas<r?t.maxPriorityFeePerGas:r}function Xn(t,e){return`${e} (${t.errorStr()})`}function Qn(t,e){if(t.cache.dataFee&&t.cache.dataFee.hardfork===t.common.hardfork())return t.cache.dataFee.value;const r=Zn.prototype.getDataFee.bind(t)()+(e??0n);return Object.isFrozen(t)&&(t.cache.dataFee={value:r,hardfork:t.common.hardfork()}),r}function ti(t){if(!t.isSigned()){const e=Xn(t,"Cannot call hash method if transaction is not signed");throw new Error(e)}const e=t.common.customCrypto.keccak256??wr;return Object.isFrozen(t)?(t.cache.hash||(t.cache.hash=e(t.serialize())),t.cache.hash):e(t.serialize())}function ei(t){const{s:e}=t;if(t.common.gteHardfork("homestead")&&void 0!==e&&e>Be){const e=Xn(t,"Invalid Signature: s-values greater than secp256k1n/2 are considered invalid");throw new Error(e)}}function ri(t){if(void 0!==t.cache.senderPubKey)return t.cache.senderPubKey;const e=t.getMessageToVerifySignature(),{v:r,r:n,s:i}=t;ei(t);try{const o=(t.common.customCrypto.ecrecover??Rr)(e,r,ke(n),ke(i),t.supports($n.EIP155ReplayProtection)?t.common.chainId():void 0);return Object.isFrozen(t)&&(t.cache.senderPubKey=o),o}catch(e){const r=Xn(t,"Invalid Signature");throw new Error(r)}}function ni(t,e){if(void 0!==e&&e>t)throw new Error("Tx cannot pay baseFee");return void 0===e?t:t-e}function ii(t){return(t.common.customCrypto.keccak256??wr)(t.getMessageToSign())}function oi(t,e){return xe(Kn(t.type),Ve(e??t.raw()))}function si(t){const{v:e}=t;if(void 0!==e&&e!==Ce&&e!==Me){const e=Xn(t,"The y-parity of the transaction should either be 0 or 1");throw new Error(e)}}function ai(t){return Qn(t,BigInt(Vn.getDataFeeEIP2930(t.accessList,t.common)))}class hi extends Zn{constructor(t,e={}){super({...t,type:Wn.FeeMarketEIP1559},e);const{chainId:r,accessList:n,maxFeePerGas:i,maxPriorityFeePerGas:o}=t;if(this.common=this._getCommon(e.common,r),this.chainId=this.common.chainId(),!1===this.common.isActivatedEIP(1559))throw new Error("EIP-1559 not enabled on Common");this.activeCapabilities=this.activeCapabilities.concat([1559,2718,2930]);const s=Vn.getAccessListData(n??[]);if(this.accessList=s.accessList,this.AccessListJSON=s.AccessListJSON,Vn.verifyAccessList(this.accessList),this.maxFeePerGas=le(we(""===i?"0x":i)),this.maxPriorityFeePerGas=le(we(""===o?"0x":o)),this._validateCannotExceedMaxInteger({maxFeePerGas:this.maxFeePerGas,maxPriorityFeePerGas:this.maxPriorityFeePerGas}),Zn._validateNotArray(t),this.gasLimit*this.maxFeePerGas>Pe){const t=this._errorMsg("gasLimit * maxFeePerGas cannot exceed MAX_INTEGER (2^256-1)");throw new Error(t)}if(this.maxFeePerGas<this.maxPriorityFeePerGas){const t=this._errorMsg("maxFeePerGas cannot be less than maxPriorityFeePerGas (The total must be the larger of the two)");throw new Error(t)}si(this),ei(this),(e?.freeze??1)&&Object.freeze(this)}static fromTxData(t,e={}){return new hi(t,e)}static fromSerializedTx(t,e={}){if(!1===Yt(t.subarray(0,1),Kn(Wn.FeeMarketEIP1559)))throw new Error(`Invalid serialized tx input: not an EIP-1559 transaction (wrong tx type, expected: ${Wn.FeeMarketEIP1559}, received: ${ce(t.subarray(0,1))}`);const r=Ke(t.subarray(1));if(!Array.isArray(r))throw new Error("Invalid serialized tx input: must be array");return hi.fromValuesArray(r,e)}static fromValuesArray(t,e={}){if(9!==t.length&&12!==t.length)throw new Error("Invalid EIP-1559 transaction. Only expecting 9 values (for unsigned tx) or 12 values (for signed tx).");const[r,n,i,o,s,a,h,c,u,l,f,d]=t;return this._validateNotArray({chainId:r,v:l}),ve({nonce:n,maxPriorityFeePerGas:i,maxFeePerGas:o,gasLimit:s,value:h,v:l,r:f,s:d}),new hi({chainId:le(r),nonce:n,maxPriorityFeePerGas:i,maxFeePerGas:o,gasLimit:s,to:a,value:h,data:c,accessList:u??[],v:void 0!==l?le(l):void 0,r:f,s:d},e)}getDataFee(){return ai(this)}getEffectivePriorityFee(t){return Jn(this,t)}getUpfrontCost(t=Ce){return Yn(this,t)}raw(){return[ke(this.chainId),ke(this.nonce),ke(this.maxPriorityFeePerGas),ke(this.maxFeePerGas),ke(this.gasLimit),void 0!==this.to?this.to.bytes:new Uint8Array(0),ke(this.value),this.data,this.accessList,void 0!==this.v?ke(this.v):new Uint8Array(0),void 0!==this.r?ke(this.r):new Uint8Array(0),void 0!==this.s?ke(this.s):new Uint8Array(0)]}serialize(){return oi(this)}getMessageToSign(){return oi(this,this.raw().slice(0,9))}getHashedMessageToSign(){return ii(this)}hash(){return ti(this)}getMessageToVerifySignature(){return this.getHashedMessageToSign()}getSenderPublicKey(){return ri(this)}addSignature(t,e,r,n=!1){e=we(e),r=we(r);const i={...this.txOptions,common:this.common};return hi.fromTxData({chainId:this.chainId,nonce:this.nonce,maxPriorityFeePerGas:this.maxPriorityFeePerGas,maxFeePerGas:this.maxFeePerGas,gasLimit:this.gasLimit,to:this.to,value:this.value,data:this.data,accessList:this.accessList,v:n?t-Le:t,r:le(e),s:le(r)},i)}toJSON(){const t=Vn.getAccessListJSON(this.accessList);return{...super.toJSON(),chainId:Ee(this.chainId),maxPriorityFeePerGas:Ee(this.maxPriorityFeePerGas),maxFeePerGas:Ee(this.maxFeePerGas),accessList:t}}errorStr(){let t=this._getSharedErrorPostfix();return t+=` maxFeePerGas=${this.maxFeePerGas} maxPriorityFeePerGas=${this.maxPriorityFeePerGas}`,t}_errorMsg(t){return Xn(this,t)}}class ci extends Zn{constructor(t,e={}){super({...t,type:Wn.AccessListEIP2930},e);const{chainId:r,accessList:n,gasPrice:i}=t;if(this.common=this._getCommon(e.common,r),this.chainId=this.common.chainId(),!this.common.isActivatedEIP(2930))throw new Error("EIP-2930 not enabled on Common");this.activeCapabilities=this.activeCapabilities.concat([2718,2930]);const o=Vn.getAccessListData(n??[]);if(this.accessList=o.accessList,this.AccessListJSON=o.AccessListJSON,Vn.verifyAccessList(this.accessList),this.gasPrice=le(we(""===i?"0x":i)),this._validateCannotExceedMaxInteger({gasPrice:this.gasPrice}),Zn._validateNotArray(t),this.gasPrice*this.gasLimit>Pe){const t=this._errorMsg("gasLimit * gasPrice cannot exceed MAX_INTEGER");throw new Error(t)}si(this),ei(this),(e?.freeze??1)&&Object.freeze(this)}static fromTxData(t,e={}){return new ci(t,e)}static fromSerializedTx(t,e={}){if(!1===Yt(t.subarray(0,1),Kn(Wn.AccessListEIP2930)))throw new Error(`Invalid serialized tx input: not an EIP-2930 transaction (wrong tx type, expected: ${Wn.AccessListEIP2930}, received: ${ce(t.subarray(0,1))}`);const r=Ke(Uint8Array.from(t.subarray(1)));if(!Array.isArray(r))throw new Error("Invalid serialized tx input: must be array");return ci.fromValuesArray(r,e)}static fromValuesArray(t,e={}){if(8!==t.length&&11!==t.length)throw new Error("Invalid EIP-2930 transaction. Only expecting 8 values (for unsigned tx) or 11 values (for signed tx).");const[r,n,i,o,s,a,h,c,u,l,f]=t;return this._validateNotArray({chainId:r,v:u}),ve({nonce:n,gasPrice:i,gasLimit:o,value:a,v:u,r:l,s:f}),new ci({chainId:le(r),nonce:n,gasPrice:i,gasLimit:o,to:s,value:a,data:h,accessList:c??[],v:void 0!==u?le(u):void 0,r:l,s:f},e)}getEffectivePriorityFee(t){return ni(this.gasPrice,t)}getDataFee(){return ai(this)}getUpfrontCost(){return this.gasLimit*this.gasPrice+this.value}raw(){return[ke(this.chainId),ke(this.nonce),ke(this.gasPrice),ke(this.gasLimit),void 0!==this.to?this.to.bytes:new Uint8Array(0),ke(this.value),this.data,this.accessList,void 0!==this.v?ke(this.v):new Uint8Array(0),void 0!==this.r?ke(this.r):new Uint8Array(0),void 0!==this.s?ke(this.s):new Uint8Array(0)]}serialize(){return oi(this)}getMessageToSign(){return oi(this,this.raw().slice(0,8))}getHashedMessageToSign(){return ii(this)}hash(){return ti(this)}getMessageToVerifySignature(){return this.getHashedMessageToSign()}getSenderPublicKey(){return ri(this)}addSignature(t,e,r,n=!1){e=we(e),r=we(r);const i={...this.txOptions,common:this.common};return ci.fromTxData({chainId:this.chainId,nonce:this.nonce,gasPrice:this.gasPrice,gasLimit:this.gasLimit,to:this.to,value:this.value,data:this.data,accessList:this.accessList,v:n?t-Le:t,r:le(e),s:le(r)},i)}toJSON(){const t=Vn.getAccessListJSON(this.accessList);return{...super.toJSON(),chainId:Ee(this.chainId),gasPrice:Ee(this.gasPrice),accessList:t}}errorStr(){let t=this._getSharedErrorPostfix();return t+=` gasPrice=${this.gasPrice} accessListCount=${this.accessList?.length??0}`,t}_errorMsg(t){return Xn(this,t)}}class ui extends Zn{constructor(t,e={}){super({...t,type:Wn.BlobEIP4844},e);const{chainId:r,accessList:n,maxFeePerGas:i,maxPriorityFeePerGas:o,maxFeePerBlobGas:s}=t;if(this.common=this._getCommon(e.common,r),this.chainId=this.common.chainId(),!1===this.common.isActivatedEIP(1559))throw new Error("EIP-1559 not enabled on Common");if(!1===this.common.isActivatedEIP(4844))throw new Error("EIP-4844 not enabled on Common");this.activeCapabilities=this.activeCapabilities.concat([1559,2718,2930]);const a=Vn.getAccessListData(n??[]);if(this.accessList=a.accessList,this.AccessListJSON=a.AccessListJSON,Vn.verifyAccessList(this.accessList),this.maxFeePerGas=le(we(""===i?"0x":i)),this.maxPriorityFeePerGas=le(we(""===o?"0x":o)),this._validateCannotExceedMaxInteger({maxFeePerGas:this.maxFeePerGas,maxPriorityFeePerGas:this.maxPriorityFeePerGas}),Zn._validateNotArray(t),this.gasLimit*this.maxFeePerGas>Pe){const t=this._errorMsg("gasLimit * maxFeePerGas cannot exceed MAX_INTEGER (2^256-1)");throw new Error(t)}if(this.maxFeePerGas<this.maxPriorityFeePerGas){const t=this._errorMsg("maxFeePerGas cannot be less than maxPriorityFeePerGas (The total must be the larger of the two)");throw new Error(t)}this.maxFeePerBlobGas=le(we(""===(s??"")?"0x":s)),this.blobVersionedHashes=(t.blobVersionedHashes??[]).map((t=>we(t))),si(this),ei(this);for(const t of this.blobVersionedHashes){if(32!==t.length){const t=this._errorMsg("versioned hash is invalid length");throw new Error(t)}if(BigInt(t[0])!==this.common.param("sharding","blobCommitmentVersionKzg")){const t=this._errorMsg("versioned hash does not start with KZG commitment version");throw new Error(t)}}if(this.blobVersionedHashes.length>6){const t=this._errorMsg("tx can contain at most 6 blobs");throw new Error(t)}if(0===this.blobVersionedHashes.length){const t=this._errorMsg("tx should contain at least one blob");throw new Error(t)}if(void 0===this.to){const t=this._errorMsg('tx should have a "to" field and cannot be used to create contracts');throw new Error(t)}this.blobs=t.blobs?.map((t=>we(t))),this.kzgCommitments=t.kzgCommitments?.map((t=>we(t))),this.kzgProofs=t.kzgProofs?.map((t=>we(t))),(e?.freeze??1)&&Object.freeze(this)}static fromTxData(t,e){if(void 0===e?.common?.customCrypto?.kzg)throw new Error("A common object with customCrypto.kzg initialized required to instantiate a 4844 blob tx");const r=e.common.customCrypto.kzg;if(void 0!==t.blobsData){if(void 0!==t.blobs)throw new Error("cannot have both raw blobs data and encoded blobs in constructor");if(void 0!==t.kzgCommitments)throw new Error("cannot have both raw blobs data and KZG commitments in constructor");if(void 0!==t.blobVersionedHashes)throw new Error("cannot have both raw blobs data and versioned hashes in constructor");if(void 0!==t.kzgProofs)throw new Error("cannot have both raw blobs data and KZG proofs in constructor");t.blobs=(t=>{const e=O(t),r=e.byteLength;if(0===r)throw Error("invalid blob data");if(r>262143)throw Error("blob data is too large");const n=Math.ceil(r/Lr),i=function(t,e){const r=new Uint8Array(e*Lr).fill(0);return r.set(t),r[t.byteLength]=128,r}(e,n),o=[];for(let t=0;t<n;t++){const e=Fr(i.subarray(t*Lr,(t+1)*Lr));o.push(e)}return o})(t.blobsData.reduce(((t,e)=>t+e))),t.kzgCommitments=((t,e)=>{const r=[];for(const n of e)r.push(t.blobToKzgCommitment(n));return r})(r,t.blobs),t.blobVersionedHashes=(t=>{const e=[];for(const r of t)e.push(Nr(r,1));return e})(t.kzgCommitments),t.kzgProofs=((t,e,r)=>e.map(((e,n)=>t.computeBlobKzgProof(e,r[n]))))(r,t.blobs,t.kzgCommitments)}return new ui(t,e)}getEffectivePriorityFee(t){return Jn(this,t)}static minimalFromNetworkWrapper(t,e){if(void 0===e?.common?.customCrypto?.kzg)throw new Error("A common object with customCrypto.kzg initialized required to instantiate a 4844 blob tx");return ui.fromTxData({...t,blobs:void 0,kzgCommitments:void 0,kzgProofs:void 0},e)}static fromSerializedTx(t,e={}){if(void 0===e.common?.customCrypto?.kzg)throw new Error("A common object with customCrypto.kzg initialized required to instantiate a 4844 blob tx");if(!1===Yt(t.subarray(0,1),Kn(Wn.BlobEIP4844)))throw new Error(`Invalid serialized tx input: not an EIP-4844 transaction (wrong tx type, expected: ${Wn.BlobEIP4844}, received: ${ce(t.subarray(0,1))}`);const r=Ke(t.subarray(1));if(!Array.isArray(r))throw new Error("Invalid serialized tx input: must be array");return ui.fromValuesArray(r,e)}static fromValuesArray(t,e={}){if(void 0===e.common?.customCrypto?.kzg)throw new Error("A common object with customCrypto.kzg initialized required to instantiate a 4844 blob tx");if(11!==t.length&&14!==t.length)throw new Error("Invalid EIP-4844 transaction. Only expecting 11 values (for unsigned tx) or 14 values (for signed tx).");const[r,n,i,o,s,a,h,c,u,l,f,d,p,m]=t;return this._validateNotArray({chainId:r,v:d}),ve({nonce:n,maxPriorityFeePerGas:i,maxFeePerGas:o,gasLimit:s,value:h,maxFeePerBlobGas:l,v:d,r:p,s:m}),new ui({chainId:le(r),nonce:n,maxPriorityFeePerGas:i,maxFeePerGas:o,gasLimit:s,to:a,value:h,data:c,accessList:u??[],maxFeePerBlobGas:l,blobVersionedHashes:f,v:void 0!==d?le(d):void 0,r:p,s:m},e)}static fromSerializedBlobTxNetworkWrapper(t,e){if(!e||!e.common)throw new Error("common instance required to validate versioned hashes");if(void 0===e.common?.customCrypto?.kzg)throw new Error("A common object with customCrypto.kzg initialized required to instantiate a 4844 blob tx");if(!1===Yt(t.subarray(0,1),Kn(Wn.BlobEIP4844)))throw new Error(`Invalid serialized tx input: not an EIP-4844 transaction (wrong tx type, expected: ${Wn.BlobEIP4844}, received: ${ce(t.subarray(0,1))}`);const r=Ke(t.subarray(1));if(4!==r.length)throw Error("Expected 4 values in the deserialized network transaction");const[n,i,o,s]=r,a=ui.fromValuesArray(n,{...e,freeze:!1});if(void 0===a.to)throw Error("BlobEIP4844Transaction can not be send without a valid `to`");const h=Number(e.common.param("sharding","blobCommitmentVersionKzg"));return((t,e,r,n,i,o)=>{if(t.length!==e.length||e.length!==r.length)throw new Error("Number of blobVersionedHashes, blobs, and commitments not all equal");if(0===t.length)throw new Error("Invalid transaction with empty blobs");let s;try{s=o.verifyBlobKzgProofBatch(e,r,n)}catch(t){throw new Error(`KZG verification of blobs fail with error=${t}`)}if(!s)throw new Error("KZG proof cannot be verified from blobs/commitments");for(let e=0;e<t.length;e++)if(!Yt(Nr(r[e],i),t[e]))throw new Error(`commitment for blob at index ${e} does not match versionedHash`)})(a.blobVersionedHashes,i,o,s,h,e.common.customCrypto.kzg),a.blobs=i,a.kzgCommitments=o,a.kzgProofs=s,(e?.freeze??!0)&&Object.freeze(a),a}getDataFee(){return ai(this)}getUpfrontCost(t=Ce){return Yn(this,t)}raw(){return[ke(this.chainId),ke(this.nonce),ke(this.maxPriorityFeePerGas),ke(this.maxFeePerGas),ke(this.gasLimit),void 0!==this.to?this.to.bytes:new Uint8Array(0),ke(this.value),this.data,this.accessList,ke(this.maxFeePerBlobGas),this.blobVersionedHashes,void 0!==this.v?ke(this.v):new Uint8Array(0),void 0!==this.r?ke(this.r):new Uint8Array(0),void 0!==this.s?ke(this.s):new Uint8Array(0)]}serialize(){return oi(this)}serializeNetworkWrapper(){if(void 0===this.blobs||void 0===this.kzgCommitments||void 0===this.kzgProofs)throw new Error("cannot serialize network wrapper without blobs, KZG commitments and KZG proofs provided");return oi(this,[this.raw(),this.blobs,this.kzgCommitments,this.kzgProofs])}getMessageToSign(){return oi(this,this.raw().slice(0,11))}getHashedMessageToSign(){return ii(this)}hash(){return ti(this)}getMessageToVerifySignature(){return this.getHashedMessageToSign()}getSenderPublicKey(){return ri(this)}toJSON(){const t=Vn.getAccessListJSON(this.accessList);return{...super.toJSON(),chainId:Ee(this.chainId),maxPriorityFeePerGas:Ee(this.maxPriorityFeePerGas),maxFeePerGas:Ee(this.maxFeePerGas),accessList:t,maxFeePerBlobGas:Ee(this.maxFeePerBlobGas),blobVersionedHashes:this.blobVersionedHashes.map((t=>ce(t)))}}addSignature(t,e,r,n=!1){e=we(e),r=we(r);const i={...this.txOptions,common:this.common};return ui.fromTxData({chainId:this.chainId,nonce:this.nonce,maxPriorityFeePerGas:this.maxPriorityFeePerGas,maxFeePerGas:this.maxFeePerGas,gasLimit:this.gasLimit,to:this.to,value:this.value,data:this.data,accessList:this.accessList,v:n?t-Le:t,r:le(e),s:le(r),maxFeePerBlobGas:this.maxFeePerBlobGas,blobVersionedHashes:this.blobVersionedHashes,blobs:this.blobs,kzgCommitments:this.kzgCommitments,kzgProofs:this.kzgProofs},i)}errorStr(){let t=this._getSharedErrorPostfix();return t+=` maxFeePerGas=${this.maxFeePerGas} maxPriorityFeePerGas=${this.maxPriorityFeePerGas}`,t}_errorMsg(t){return Xn(this,t)}numBlobs(){return this.blobVersionedHashes.length}}function li(t,e){const r=Number(t),n=2*Number(e);return r===n+35||r===n+36}class fi extends Zn{constructor(t,e={}){if(super({...t,type:Wn.Legacy},e),this.common=this._validateTxV(this.v,e.common),this.keccakFunction=this.common.customCrypto.keccak256??wr,this.gasPrice=le(we(""===t.gasPrice?"0x":t.gasPrice)),this.gasPrice*this.gasLimit>Pe){const t=this._errorMsg("gas limit * gasPrice cannot exceed MAX_INTEGER (2^256-1)");throw new Error(t)}this._validateCannotExceedMaxInteger({gasPrice:this.gasPrice}),Zn._validateNotArray(t),this.common.gteHardfork("spuriousDragon")&&(this.isSigned()?li(this.v,this.common.chainId())&&this.activeCapabilities.push($n.EIP155ReplayProtection):this.activeCapabilities.push($n.EIP155ReplayProtection)),(e?.freeze??1)&&Object.freeze(this)}static fromTxData(t,e={}){return new fi(t,e)}static fromSerializedTx(t,e={}){const r=Ke(t);if(!Array.isArray(r))throw new Error("Invalid serialized tx input. Must be array");return this.fromValuesArray(r,e)}static fromValuesArray(t,e={}){if(6!==t.length&&9!==t.length)throw new Error("Invalid transaction. Only expecting 6 values (for unsigned tx) or 9 values (for signed tx).");const[r,n,i,o,s,a,h,c,u]=t;return ve({nonce:r,gasPrice:n,gasLimit:i,value:s,v:h,r:c,s:u}),new fi({nonce:r,gasPrice:n,gasLimit:i,to:o,value:s,data:a,v:h,r:c,s:u},e)}getEffectivePriorityFee(t){return ni(this.gasPrice,t)}raw(){return[ke(this.nonce),ke(this.gasPrice),ke(this.gasLimit),void 0!==this.to?this.to.bytes:new Uint8Array(0),ke(this.value),this.data,void 0!==this.v?ke(this.v):new Uint8Array(0),void 0!==this.r?ke(this.r):new Uint8Array(0),void 0!==this.s?ke(this.s):new Uint8Array(0)]}serialize(){return Ve(this.raw())}getMessageToSign(){const t=[ke(this.nonce),ke(this.gasPrice),ke(this.gasLimit),void 0!==this.to?this.to.bytes:new Uint8Array(0),ke(this.value),this.data];return this.supports($n.EIP155ReplayProtection)&&(t.push(ke(this.common.chainId())),t.push(be(we(0))),t.push(be(we(0)))),t}getHashedMessageToSign(){const t=this.getMessageToSign();return this.keccakFunction(Ve(t))}getDataFee(){return Qn(this)}getUpfrontCost(){return this.gasLimit*this.gasPrice+this.value}hash(){return ti(this)}getMessageToVerifySignature(){if(!this.isSigned()){const t=this._errorMsg("This transaction is not signed");throw new Error(t)}return this.getHashedMessageToSign()}getSenderPublicKey(){return ri(this)}addSignature(t,e,r,n=!1){e=we(e),r=we(r),n&&this.supports($n.EIP155ReplayProtection)&&(t+=this.common.chainId()*Re+Oe);const i={...this.txOptions,common:this.common};return fi.fromTxData({nonce:this.nonce,gasPrice:this.gasPrice,gasLimit:this.gasLimit,to:this.to,value:this.value,data:this.data,v:t,r:le(e),s:le(r)},i)}toJSON(){return{...super.toJSON(),gasPrice:Ee(this.gasPrice)}}_validateTxV(t,e){let r;const n=void 0!==t?Number(t):void 0;if(void 0!==n&&n<37&&27!==n&&28!==n)throw new Error(`Legacy txs need either v = 27/28 or v >= 37 (EIP-155 replay protection), got v = ${n}`);if(void 0!==n&&0!==n&&(!e||e.gteHardfork("spuriousDragon"))&&27!==n&&28!==n)if(e){if(!li(BigInt(n),e.chainId()))throw new Error(`Incompatible EIP155-based V ${n} and chain id ${e.chainId()}. See the Common parameter of the Transaction constructor to set the chain id.`)}else{let t;t=(n-35)%2==0?35:36,r=BigInt(n-t)/Re}return this._getCommon(e,r)}errorStr(){let t=this._getSharedErrorPostfix();return t+=` gasPrice=${this.gasPrice}`,t}_errorMsg(t){return Xn(this,t)}}class di{constructor(){}static fromTxData(t,e={}){if("type"in t&&void 0!==t.type){if(function(t){return Number(le(we(t.type)))===Wn.Legacy}(t))return fi.fromTxData(t,e);if(function(t){return Number(le(we(t.type)))===Wn.AccessListEIP2930}(t))return ci.fromTxData(t,e);if(function(t){return Number(le(we(t.type)))===Wn.FeeMarketEIP1559}(t))return hi.fromTxData(t,e);if(function(t){return Number(le(we(t.type)))===Wn.BlobEIP4844}(t))return ui.fromTxData(t,e);throw new Error(`Tx instantiation with type ${t?.type} not supported`)}return fi.fromTxData(t,e)}static fromSerializedData(t,e={}){if(!(t[0]<=127))return fi.fromSerializedTx(t,e);switch(t[0]){case Wn.AccessListEIP2930:return ci.fromSerializedTx(t,e);case Wn.FeeMarketEIP1559:return hi.fromSerializedTx(t,e);case Wn.BlobEIP4844:return ui.fromSerializedTx(t,e);default:throw new Error(`TypedTransaction with ID ${t[0]} unknown`)}}static fromBlockBodyData(t,e={}){if(t instanceof Uint8Array)return this.fromSerializedData(t,e);if(Array.isArray(t))return fi.fromValuesArray(t,e);throw new Error("Cannot decode transaction: unknown type input")}static async fromJsonRpcProvider(t,e,r){const n=Gr(t),i=await Ur(n,{method:"eth_getTransactionByHash",params:[e]});if(null===i)throw new Error("No data returned from provider");return di.fromRPC(i,r)}static async fromRPC(t,e={}){return di.fromTxData((t=>{const e=Object.assign({},t);return e.gasLimit=Ir(e.gasLimit??e.gas,Ar.BigInt),e.data=void 0===e.data?e.input:e.data,e.gasPrice=void 0!==e.gasPrice?BigInt(e.gasPrice):void 0,e.value=void 0!==e.value?BigInt(e.value):void 0,e.to=null!==e.to&&void 0!==e.to?ye(we(e.to),20):null,e.v="0x0"===e.v?"0x":e.v,e.r="0x0"===e.r?"0x":e.r,e.s="0x0"===e.s?"0x":e.s,"0x"===e.v&&"0x"===e.r&&"0x"===e.s||(e.v=Ir(e.v,Ar.BigInt)),e})(t),e)}}const pi=function(t){if(void 0!==t){if(!te(t)){if(!new RegExp(/^\d+$/).test(t))throw new Error(`Cannot convert string to hex string. numberToHex only supports 0x-prefixed hex or integer strings but the given string was: ${t}`);return"0x"+parseInt(t,10).toString(16)}return t}},mi=BigInt("0xffffffffffffff");class gi{constructor(t,e={}){this.cache={hash:void 0},e.common?this.common=e.common.copy():this.common=new qn({chain:On.Mainnet}),this.keccakFunction=this.common.customCrypto.keccak256??wr;const r=e.skipConsensusFormatValidation??!1,n={parentHash:ge(32),uncleHash:Ae,coinbase:_r.zero(),stateRoot:ge(32),transactionsTrie:Se,receiptTrie:Se,logsBloom:ge(256),difficulty:Ce,number:Ce,gasLimit:mi,gasUsed:Ce,timestamp:Ce,extraData:new Uint8Array(0),mixHash:ge(32),nonce:ge(8)},i=Ir(t.parentHash,Ar.Uint8Array)??n.parentHash,o=Ir(t.uncleHash,Ar.Uint8Array)??n.uncleHash,s=new _r(Ir(t.coinbase??n.coinbase,Ar.Uint8Array)),a=Ir(t.stateRoot,Ar.Uint8Array)??n.stateRoot,h=Ir(t.transactionsTrie,Ar.Uint8Array)??n.transactionsTrie,c=Ir(t.receiptTrie,Ar.Uint8Array)??n.receiptTrie,u=Ir(t.logsBloom,Ar.Uint8Array)??n.logsBloom,l=Ir(t.difficulty,Ar.BigInt)??n.difficulty,f=Ir(t.number,Ar.BigInt)??n.number,d=Ir(t.gasLimit,Ar.BigInt)??n.gasLimit,p=Ir(t.gasUsed,Ar.BigInt)??n.gasUsed,m=Ir(t.timestamp,Ar.BigInt)??n.timestamp,g=Ir(t.extraData,Ar.Uint8Array)??n.extraData,y=Ir(t.mixHash,Ar.Uint8Array)??n.mixHash,b=Ir(t.nonce,Ar.Uint8Array)??n.nonce,w=e.setHardfork??!1;!0===w?this.common.setHardforkBy({blockNumber:f,timestamp:m}):"boolean"!=typeof w&&this.common.setHardforkBy({blockNumber:f,td:w,timestamp:m});const v={baseFeePerGas:this.common.isActivatedEIP(1559)?f===this.common.hardforkBlock(Ln.London)?this.common.param("gasConfig","initialBaseFee"):Te:void 0,withdrawalsRoot:this.common.isActivatedEIP(4895)?Se:void 0,blobGasUsed:this.common.isActivatedEIP(4844)?Ce:void 0,excessBlobGas:this.common.isActivatedEIP(4844)?Ce:void 0,parentBeaconBlockRoot:this.common.isActivatedEIP(4788)?ge(32):void 0},E=Ir(t.baseFeePerGas,Ar.BigInt)??v.baseFeePerGas,k=Ir(t.withdrawalsRoot,Ar.Uint8Array)??v.withdrawalsRoot,x=Ir(t.blobGasUsed,Ar.BigInt)??v.blobGasUsed,_=Ir(t.excessBlobGas,Ar.BigInt)??v.excessBlobGas,P=Ir(t.parentBeaconBlockRoot,Ar.Uint8Array)??v.parentBeaconBlockRoot;if(!this.common.isActivatedEIP(1559)&&void 0!==E)throw new Error("A base fee for a block can only be set with EIP1559 being activated");if(!this.common.isActivatedEIP(4895)&&void 0!==k)throw new Error("A withdrawalsRoot for a header can only be provided with EIP4895 being activated");if(!this.common.isActivatedEIP(4844)){if(void 0!==x)throw new Error("blob gas used can only be provided with EIP4844 activated");if(void 0!==_)throw new Error("excess blob gas can only be provided with EIP4844 activated")}if(!this.common.isActivatedEIP(4788)&&void 0!==P)throw new Error("A parentBeaconBlockRoot for a header can only be provided with EIP4788 being activated");if(this.parentHash=i,this.uncleHash=o,this.coinbase=s,this.stateRoot=a,this.transactionsTrie=h,this.receiptTrie=c,this.logsBloom=u,this.difficulty=l,this.number=f,this.gasLimit=d,this.gasUsed=p,this.timestamp=m,this.extraData=g,this.mixHash=y,this.nonce=b,this.baseFeePerGas=E,this.withdrawalsRoot=k,this.blobGasUsed=x,this.excessBlobGas=_,this.parentBeaconBlockRoot=P,this._genericFormatValidation(),this._validateDAOExtraData(),e.calcDifficultyFromHeader&&this.common.consensusAlgorithm()===Nn.Ethash&&(this.difficulty=this.ethashCanonicalDifficulty(e.calcDifficultyFromHeader)),e.cliqueSigner){const t=97;if(this.extraData.length<t){const e=t-this.extraData.length;this.extraData=xe(this.extraData,new Uint8Array(e))}this.extraData=this.cliqueSealBlock(e.cliqueSigner)}!1===r&&this._consensusFormatValidation(),(e?.freeze??1)&&Object.freeze(this)}get prevRandao(){if(!1===this.common.isActivatedEIP(4399)){const t=this._errorMsg("The prevRandao parameter can only be accessed when EIP-4399 is activated");throw new Error(t)}return this.mixHash}static fromHeaderData(t={},e={}){return new gi(t,e)}static fromRLPSerializedHeader(t,e={}){const r=Ke(t);if(!Array.isArray(r))throw new Error("Invalid serialized header input. Must be array");return gi.fromValuesArray(r,e)}static fromValuesArray(t,e={}){const r=function(t){const[e,r,n,i,o,s,a,h,c,u,l,f,d,p,m,g,y,b,w,v]=t;if(t.length>20)throw new Error(`invalid header. More values than expected were received. Max: 20, got: ${t.length}`);if(t.length<15)throw new Error(`invalid header. Less values than expected were received. Min: 15, got: ${t.length}`);return{parentHash:e,uncleHash:r,coinbase:n,stateRoot:i,transactionsTrie:o,receiptTrie:s,logsBloom:a,difficulty:h,number:c,gasLimit:u,gasUsed:l,timestamp:f,extraData:d,mixHash:p,nonce:m,baseFeePerGas:g,withdrawalsRoot:y,blobGasUsed:b,excessBlobGas:w,parentBeaconBlockRoot:v}}(t),{number:n,baseFeePerGas:i,excessBlobGas:o,blobGasUsed:s,parentBeaconBlockRoot:a}=r,h=gi.fromHeaderData(r,e);if(h.common.isActivatedEIP(1559)&&void 0===i){const t=me(h.common.eipBlock(1559));if(t&&Yt(t,n))throw new Error("invalid header. baseFeePerGas should be provided")}if(h.common.isActivatedEIP(4844)){if(void 0===o)throw new Error("invalid header. excessBlobGas should be provided");if(void 0===s)throw new Error("invalid header. blobGasUsed should be provided")}if(h.common.isActivatedEIP(4788)&&void 0===a)throw new Error("invalid header. parentBeaconBlockRoot should be provided");return h}_genericFormatValidation(){const{parentHash:t,stateRoot:e,transactionsTrie:r,receiptTrie:n,mixHash:i,nonce:o}=this;if(32!==t.length){const e=this._errorMsg(`parentHash must be 32 bytes, received ${t.length} bytes`);throw new Error(e)}if(32!==e.length){const t=this._errorMsg(`stateRoot must be 32 bytes, received ${e.length} bytes`);throw new Error(t)}if(32!==r.length){const t=this._errorMsg(`transactionsTrie must be 32 bytes, received ${r.length} bytes`);throw new Error(t)}if(32!==n.length){const t=this._errorMsg(`receiptTrie must be 32 bytes, received ${n.length} bytes`);throw new Error(t)}if(32!==i.length){const t=this._errorMsg(`mixHash must be 32 bytes, received ${i.length} bytes`);throw new Error(t)}if(8!==o.length){const t=this._errorMsg(`nonce must be 8 bytes, received ${o.length} bytes`);throw new Error(t)}if(this.gasUsed>this.gasLimit){const t=this._errorMsg(`Invalid block: too much gas used. Used: ${this.gasUsed}, gas limit: ${this.gasLimit}`);throw new Error(t)}if(!0===this.common.isActivatedEIP(1559)){if("bigint"!=typeof this.baseFeePerGas){const t=this._errorMsg("EIP1559 block has no base fee field");throw new Error(t)}const t=this.common.hardforkBlock(Ln.London);if("bigint"==typeof t&&t!==Ce&&this.number===t){const t=this.common.param("gasConfig","initialBaseFee");if(this.baseFeePerGas!==t){const t=this._errorMsg("Initial EIP1559 block does not have initial base fee");throw new Error(t)}}}if(!0===this.common.isActivatedEIP(4895)){if(void 0===this.withdrawalsRoot){const t=this._errorMsg("EIP4895 block has no withdrawalsRoot field");throw new Error(t)}if(32!==this.withdrawalsRoot?.length){const t=this._errorMsg(`withdrawalsRoot must be 32 bytes, received ${this.withdrawalsRoot.length} bytes`);throw new Error(t)}}if(!0===this.common.isActivatedEIP(4788)){if(void 0===this.parentBeaconBlockRoot){const t=this._errorMsg("EIP4788 block has no parentBeaconBlockRoot field");throw new Error(t)}if(32!==this.parentBeaconBlockRoot?.length){const t=this._errorMsg(`parentBeaconBlockRoot must be 32 bytes, received ${this.parentBeaconBlockRoot.length} bytes`);throw new Error(t)}}}_consensusFormatValidation(){const{nonce:t,uncleHash:e,difficulty:r,extraData:n,number:i}=this;if(this.common.consensusAlgorithm()===Nn.Ethash&&i>Ce&&this.extraData.length>this.common.param("vm","maxExtraDataSize")){const t=this._errorMsg("invalid amount of extra data");throw new Error(t)}if(this.common.consensusAlgorithm()===Nn.Clique){const t=97;if(this.cliqueIsEpochTransition()){const e=this.extraData.length-t;if(e%20!=0){const t=this._errorMsg(`invalid signer list length in extraData, received signer length of ${e} (not divisible by 20)`);throw new Error(t)}if(!this.coinbase.isZero()){const t=this._errorMsg(`coinbase must be filled with zeros on epoch transition blocks, received ${this.coinbase}`);throw new Error(t)}}else if(this.extraData.length!==t){const e=this._errorMsg(`extraData must be ${t} bytes on non-epoch transition blocks, received ${this.extraData.length} bytes`);throw new Error(e)}if(!Yt(this.mixHash,new Uint8Array(32))){const t=this._errorMsg(`mixHash must be filled with zeros, received ${this.mixHash}`);throw new Error(t)}}if(this.common.consensusType()===Fn.ProofOfStake){let o=!1,s="";if(Yt(e,Ae)||(s+=`, uncleHash: ${ce(e)} (expected: ${ce(Ae)})`,o=!0),i!==Ce&&(r!==Ce&&(s+=`, difficulty: ${r} (expected: 0)`,o=!0),n.length>32&&(s+=`, extraData: ${ce(n)} (cannot exceed 32 bytes length, received ${n.length} bytes)`,o=!0),Yt(t,ge(8))||(s+=`, nonce: ${ce(t)} (expected: ${ce(ge(8))})`,o=!0)),o){const t=this._errorMsg(`Invalid PoS block: ${s}`);throw new Error(t)}}}validateGasLimit(t){let e=t.gasLimit;const r=this.common.hardforkBlock(Ln.London);"bigint"==typeof r&&r!==Ce&&this.number===r&&(e*=this.common.param("gasConfig","elasticityMultiplier"));const n=this.gasLimit,i=e/this.common.param("gasConfig","gasLimitBoundDivisor"),o=e+i,s=e-i;if(n>=o){const t=this._errorMsg(`gas limit increased too much. Gas limit: ${n}, max gas limit: ${o}`);throw new Error(t)}if(n<=s){const t=this._errorMsg(`gas limit decreased too much. Gas limit: ${n}, min gas limit: ${s}`);throw new Error(t)}if(n<this.common.param("gasConfig","minGasLimit")){const t=this._errorMsg(`gas limit decreased below minimum gas limit. Gas limit: ${n}, minimum gas limit: ${this.common.param("gasConfig","minGasLimit")}`);throw new Error(t)}}calcNextBaseFee(){if(!1===this.common.isActivatedEIP(1559)){const t=this._errorMsg("calcNextBaseFee() can only be called with EIP1559 being activated");throw new Error(t)}let t;const e=this.common.param("gasConfig","elasticityMultiplier"),r=this.gasLimit/e;if(r===this.gasUsed)t=this.baseFeePerGas;else if(this.gasUsed>r){const e=this.gasUsed-r,n=this.common.param("gasConfig","baseFeeMaxChangeDenominator"),i=this.baseFeePerGas*e/r/n;t=(i>Me?i:Me)+this.baseFeePerGas}else{const e=r-this.gasUsed,n=this.common.param("gasConfig","baseFeeMaxChangeDenominator"),i=this.baseFeePerGas*e/r/n;t=this.baseFeePerGas-i>Ce?this.baseFeePerGas-i:Ce}return t}getBlobGasPrice(){if(void 0===this.excessBlobGas)throw new Error("header must have excessBlobGas field populated");return this._getBlobGasPrice(this.excessBlobGas)}_getBlobGasPrice(t){return((t,e,r)=>{let n=Me,i=Ce,o=t*r;for(;o>Ce;)i+=o,o=o*e/(r*n),n++;return i/r})(this.common.param("gasPrices","minBlobGasPrice"),t,this.common.param("gasConfig","blobGasPriceUpdateFraction"))}calcDataFee(t){return this.common.param("gasConfig","blobGasPerBlob")*BigInt(t)*this.getBlobGasPrice()}calcNextExcessBlobGas(){const t=(this.excessBlobGas??Ce)+(this.blobGasUsed??Ce),e=this.common.param("gasConfig","targetBlobGasPerBlock");return t<=e?Ce:t-e}calcNextBlobGasPrice(){return this._getBlobGasPrice(this.calcNextExcessBlobGas())}raw(){const t=[this.parentHash,this.uncleHash,this.coinbase.bytes,this.stateRoot,this.transactionsTrie,this.receiptTrie,this.logsBloom,ke(this.difficulty),ke(this.number),ke(this.gasLimit),ke(this.gasUsed),ke(this.timestamp??Ce),this.extraData,this.mixHash,this.nonce];return!0===this.common.isActivatedEIP(1559)&&t.push(ke(this.baseFeePerGas)),!0===this.common.isActivatedEIP(4895)&&t.push(this.withdrawalsRoot),this.common.isActivatedEIP(6800),!0===this.common.isActivatedEIP(4844)&&(t.push(ke(this.blobGasUsed)),t.push(ke(this.excessBlobGas))),!0===this.common.isActivatedEIP(4788)&&t.push(this.parentBeaconBlockRoot),t}hash(){return Object.isFrozen(this)?(this.cache.hash||(this.cache.hash=this.keccakFunction(Ve(this.raw()))),this.cache.hash):this.keccakFunction(Ve(this.raw()))}isGenesis(){return this.number===Ce}_requireClique(t){if(this.common.consensusAlgorithm()!==Nn.Clique){const e=this._errorMsg(`BlockHeader.${t}() call only supported for clique PoA networks`);throw new Error(e)}}ethashCanonicalDifficulty(t){if(this.common.consensusType()!==Fn.ProofOfWork){const t=this._errorMsg("difficulty calculation is only supported on PoW chains");throw new Error(t)}if(this.common.consensusAlgorithm()!==Nn.Ethash){const t=this._errorMsg("difficulty calculation currently only supports the ethash algorithm");throw new Error(t)}const e=this.timestamp,{timestamp:r,difficulty:n}=t,i=this.common.param("pow","minimumDifficulty"),o=n/this.common.param("pow","difficultyBoundDivisor");let s,a=this.number;if(!0===this.common.gteHardfork(Ln.Byzantium)){const i=Yt(t.uncleHash,Ae)?1:2;let a=BigInt(i)-(e-r)/BigInt(9);const h=BigInt(-99);h>a&&(a=h),s=n+o*a}if(!0===this.common.gteHardfork(Ln.Byzantium))a-=this.common.param("pow","difficultyBombDelay"),a<Ce&&(a=Ce);else if(!0===this.common.gteHardfork(Ln.Homestead)){let t=Me-(e-r)/BigInt(10);const i=BigInt(-99);i>t&&(t=i),s=n+o*t}else s=r+this.common.param("pow","durationLimit")>e?o+n:n-o;const h=a/BigInt(1e5)-Re;return h>=0&&(s+=Re**h),s<i&&(s=i),s}cliqueSigHash(){this._requireClique("cliqueSigHash");const t=this.raw();return t[12]=this.extraData.subarray(0,this.extraData.length-65),this.keccakFunction(Ve(t))}cliqueIsEpochTransition(){this._requireClique("cliqueIsEpochTransition");const t=BigInt(this.common.consensusConfig().epoch);return this.number%t===Ce}cliqueExtraVanity(){return this._requireClique("cliqueExtraVanity"),this.extraData.subarray(0,32)}cliqueExtraSeal(){return this._requireClique("cliqueExtraSeal"),this.extraData.subarray(-65)}cliqueSealBlock(t){this._requireClique("cliqueSealBlock");const e=(this.common.customCrypto?.ecsign??Mr)(this.cliqueSigHash(),t),r=xe(e.r,e.s,me(e.v-Le)),n=this.extraData.subarray(0,this.extraData.length-65);return xe(n,r)}cliqueEpochTransitionSigners(){if(this._requireClique("cliqueEpochTransitionSigners"),!this.cliqueIsEpochTransition()){const t=this._errorMsg("Signers are only included in epoch transition blocks (clique)");throw new Error(t)}const t=this.extraData.length-65,e=this.extraData.subarray(32,t),r=[];for(let t=0;t<=e.length-20;t+=20)r.push(e.subarray(t,t+20));return r.map((t=>new _r(t)))}cliqueVerifySignature(t){this._requireClique("cliqueVerifySignature");const e=this.cliqueSigner();return!!t.find((t=>t.equals(e)))}cliqueSigner(){this._requireClique("cliqueSigner");const t=this.cliqueExtraSeal();if(0===t.length||Yt(t,new Uint8Array(65)))return _r.zero();const e=t.subarray(0,32),r=t.subarray(32,64),n=le(t.subarray(64,65))+Le,i=Rr(this.cliqueSigHash(),n,e,r);return _r.fromPublicKey(i)}serialize(){return Ve(this.raw())}toJSON(){const t=this.withdrawalsRoot?{withdrawalsRoot:ce(this.withdrawalsRoot)}:{},e={parentHash:ce(this.parentHash),uncleHash:ce(this.uncleHash),coinbase:this.coinbase.toString(),stateRoot:ce(this.stateRoot),transactionsTrie:ce(this.transactionsTrie),...t,receiptTrie:ce(this.receiptTrie),logsBloom:ce(this.logsBloom),difficulty:Ee(this.difficulty),number:Ee(this.number),gasLimit:Ee(this.gasLimit),gasUsed:Ee(this.gasUsed),timestamp:Ee(this.timestamp),extraData:ce(this.extraData),mixHash:ce(this.mixHash),nonce:ce(this.nonce)};return!0===this.common.isActivatedEIP(1559)&&(e.baseFeePerGas=Ee(this.baseFeePerGas)),!0===this.common.isActivatedEIP(4844)&&(e.blobGasUsed=Ee(this.blobGasUsed),e.excessBlobGas=Ee(this.excessBlobGas)),!0===this.common.isActivatedEIP(4788)&&(e.parentBeaconBlockRoot=ce(this.parentBeaconBlockRoot)),e}_validateDAOExtraData(){if(!1===this.common.hardforkIsActiveOnBlock(Ln.Dao,this.number))return;const t=this.common.hardforkBlock(Ln.Dao);if(null===t||this.number<t)return;const e=fe("0x64616f2d686172642d666f726b"),r=BigInt(9);if(this.number-t<=r&&!Yt(this.extraData,e)){const t=this._errorMsg(`extraData should be 'dao-hard-fork', got ${Zt(this.extraData)} (hex: ${ce(this.extraData)})`);throw new Error(t)}}errorStr(){let t="";try{t=ce(this.hash())}catch(e){t="error"}let e="";try{e=this.common.hardfork()}catch(t){e="error"}let r=`block header number=${this.number} hash=${t} `;return r+=`hf=${e} baseFeePerGas=${this.baseFeePerGas??"none"}`,r}_errorMsg(t){return`${t} (${this.errorStr()})`}}function yi(t,e){const{parentHash:r,sha3Uncles:n,miner:i,stateRoot:o,transactionsRoot:s,receiptsRoot:a,logsBloom:h,difficulty:c,number:u,gasLimit:l,gasUsed:f,timestamp:d,extraData:p,mixHash:m,nonce:g,baseFeePerGas:y,withdrawalsRoot:b,blobGasUsed:w,excessBlobGas:v,parentBeaconBlockRoot:E}=t;return gi.fromHeaderData({parentHash:r,uncleHash:n,coinbase:i,stateRoot:o,transactionsTrie:s,receiptTrie:a,logsBloom:h,difficulty:pi(c),number:u,gasLimit:l,gasUsed:f,timestamp:d,extraData:p,mixHash:m,nonce:g,baseFeePerGas:y,withdrawalsRoot:b,blobGasUsed:w,excessBlobGas:v,parentBeaconBlockRoot:E},e)}function bi(t){const e=Object.assign({},t);return e.gasLimit=Ir(e.gasLimit??e.gas,Ar.BigInt),e.data=void 0===e.data?e.input:e.data,e.gasPrice=void 0!==e.gasPrice?BigInt(e.gasPrice):void 0,e.value=void 0!==e.value?BigInt(e.value):void 0,e.to=null!==e.to&&void 0!==e.to?ye(we(e.to),20):null,e.v=Ir(e.v,Ar.BigInt),e}function wi(t,e=[],r){const n=yi(t,r),i=[],o={common:n.common};for(const e of t.transactions??[]){const t=bi(e),r=di.fromTxData(t,o);i.push(r)}const s=e.map((t=>yi(t,r)));return vi.fromBlockData({header:n,transactions:i,uncleHeaders:s,withdrawals:t.withdrawals},r)}class vi{constructor(t,e=[],r=[],n,i={},o){if(this.transactions=[],this.uncleHeaders=[],this.cache={},this.header=t??gi.fromHeaderData({},i),this.common=this.header.common,this.keccakFunction=this.common.customCrypto.keccak256??wr,this.transactions=e,this.withdrawals=n??(this.common.isActivatedEIP(4895)?[]:void 0),this.executionWitness=o,this.common.isActivatedEIP(6800)&&void 0===this.executionWitness&&(this.executionWitness={stateDiff:[],verkleProof:{commitmentsByPath:[],d:"0x",depthExtensionPresent:"0x",ipaProof:{cl:[],cr:[],finalEvaluation:"0x"},otherStems:[]}}),this.uncleHeaders=r,r.length>0){if(this.validateUncles(),this.common.consensusType()===Fn.ProofOfAuthority){const t=this._errorMsg("Block initialization with uncleHeaders on a PoA network is not allowed");throw new Error(t)}if(this.common.consensusType()===Fn.ProofOfStake){const t=this._errorMsg("Block initialization with uncleHeaders on a PoS network is not allowed");throw new Error(t)}}if(!this.common.isActivatedEIP(4895)&&void 0!==n)throw new Error("Cannot have a withdrawals field if EIP 4895 is not active");if(!this.common.isActivatedEIP(6800)&&null!=o)throw new Error("Cannot have executionWitness field if EIP 6800 is not active ");(i?.freeze??1)&&Object.freeze(this)}static async genWithdrawalsTrieRoot(t,e){const r=e??new An;for(const[e,n]of t.entries())await r.put(Ve(e),Ve(n.raw()));return r.root()}static async genTransactionsTrieRoot(t,e){const r=e??new An;for(const[e,n]of t.entries())await r.put(Ve(e),n.serialize());return r.root()}static fromBlockData(t={},e){const{header:r,transactions:n,uncleHeaders:i,withdrawals:o,executionWitness:s}=t,a=gi.fromHeaderData(r,e),h=[];for(const t of n??[]){const r=di.fromTxData(t,{...e,common:a.common});h.push(r)}const c=[],u={...e,common:a.common,calcDifficultyFromHeader:void 0};void 0!==e?.setHardfork&&(u.setHardfork=!0);for(const t of i??[]){const e=gi.fromHeaderData(t,u);c.push(e)}const l=o?.map(Cr.fromWithdrawalData);return new vi(a,h,c,l,e,s)}static fromRLPSerializedBlock(t,e){const r=Ke(Uint8Array.from(t));if(!Array.isArray(r))throw new Error("Invalid serialized block input. Must be array");return vi.fromValuesArray(r,e)}static fromValuesArray(t,e){if(t.length>5)throw new Error(`invalid block. More values=${t.length} than expected were received (at most 5)`);const[r,n,i,o,s]=t,a=gi.fromValuesArray(r,e);if(a.common.isActivatedEIP(4895)&&(void 0===o||!Array.isArray(o)))throw new Error("Invalid serialized block input: EIP-4895 is active, and no withdrawals were provided as array");const h=[];for(const t of n??[])h.push(di.fromBlockBodyData(t,{...e,common:a.common}));const c=[],u={...e,common:a.common,calcDifficultyFromHeader:void 0};void 0!==e?.setHardfork&&(u.setHardfork=!0);for(const t of i??[])c.push(gi.fromValuesArray(t,u));const l=o?.map((([t,e,r,n])=>({index:t,validatorIndex:e,address:r,amount:n})))?.map(Cr.fromWithdrawalData);let f;return f=a.common.isActivatedEIP(6800)&&void 0!==s?JSON.parse(Zt(Ke(s))):null,new vi(a,h,c,l,e,f)}static fromRPC(t,e,r){return wi(t,e,r)}static async fromExecutionPayload(t,e){const{blockNumber:r,receiptsRoot:n,prevRandao:i,feeRecipient:o,transactions:s,withdrawals:a,executionWitness:h}=t,c=[];for(const[t,r]of s.entries())try{const t=di.fromSerializedData(fe(r),{common:e?.common});c.push(t)}catch(e){throw`Invalid tx at index ${t}: ${e}`}const u=await vi.genTransactionsTrieRoot(c,new An({common:e?.common})),l=a?.map((t=>Cr.fromWithdrawalData(t))),f=l?await vi.genWithdrawalsTrieRoot(l,new An({common:e?.common})):void 0,d={...t,number:r,receiptTrie:n,transactionsTrie:u,withdrawalsRoot:f,mixHash:i,coinbase:o},p=vi.fromBlockData({header:d,transactions:c,withdrawals:l,executionWitness:h},e);if(p.common.isActivatedEIP(6800)&&null==h)throw Error("Missing executionWitness for EIP-6800 activated executionPayload");if(!Yt(p.hash(),fe(t.blockHash))){const e=`Invalid blockHash, expected: ${t.blockHash}, received: ${ce(p.hash())}`;throw Error(e)}return p}static async fromBeaconPayloadJson(t,e){const r=function(t){const e={parentHash:t.parent_hash,feeRecipient:t.fee_recipient,stateRoot:t.state_root,receiptsRoot:t.receipts_root,logsBloom:t.logs_bloom,prevRandao:t.prev_randao,blockNumber:Ee(BigInt(t.block_number)),gasLimit:Ee(BigInt(t.gas_limit)),gasUsed:Ee(BigInt(t.gas_used)),timestamp:Ee(BigInt(t.timestamp)),extraData:t.extra_data,baseFeePerGas:Ee(BigInt(t.base_fee_per_gas)),blockHash:t.block_hash,transactions:t.transactions};return void 0!==t.withdrawals&&null!==t.withdrawals&&(e.withdrawals=t.withdrawals.map((t=>({index:Ee(BigInt(t.index)),validatorIndex:Ee(BigInt(t.validator_index)),address:t.address,amount:Ee(BigInt(t.amount))})))),void 0!==t.blob_gas_used&&null!==t.blob_gas_used&&(e.blobGasUsed=Ee(BigInt(t.blob_gas_used))),void 0!==t.excess_blob_gas&&null!==t.excess_blob_gas&&(e.excessBlobGas=Ee(BigInt(t.excess_blob_gas))),void 0!==t.parent_beacon_block_root&&null!==t.parent_beacon_block_root&&(e.parentBeaconBlockRoot=t.parent_beacon_block_root),void 0!==t.execution_witness&&null!==t.execution_witness&&(e.executionWitness=t.execution_witness),e}(t);return vi.fromExecutionPayload(r,e)}raw(){const t=[this.header.raw(),this.transactions.map((t=>t.supports($n.EIP2718TypedTransaction)?t.serialize():t.raw())),this.uncleHeaders.map((t=>t.raw()))],e=this.withdrawals?.map((t=>t.raw()));if(e&&t.push(e),void 0!==this.executionWitness&&null!==this.executionWitness){const e=Ve(JSON.stringify(this.executionWitness));t.push(e)}return t}hash(){return this.header.hash()}isGenesis(){return this.header.isGenesis()}serialize(){return Ve(this.raw())}async genTxTrie(){return vi.genTransactionsTrieRoot(this.transactions,new An({common:this.common}))}async transactionsTrieIsValid(){let t;return 0===this.transactions.length?(t=Yt(this.header.transactionsTrie,Se),t):(void 0===this.cache.txTrieRoot&&(this.cache.txTrieRoot=await this.genTxTrie()),t=Yt(this.cache.txTrieRoot,this.header.transactionsTrie),t)}getTransactionsValidationErrors(){const t=[];let e=Ce;const r=this.common.param("gasConfig","maxblobGasPerBlock"),n=this.common.param("gasConfig","blobGasPerBlob");for(let[i,o]of this.transactions.entries()){const s=o.getValidationErrors();!0===this.common.isActivatedEIP(1559)&&(o.supports($n.EIP1559FeeMarket)?o.maxFeePerGas<this.header.baseFeePerGas&&s.push("tx unable to pay base fee (EIP-1559 tx)"):o.gasPrice<this.header.baseFeePerGas&&s.push("tx unable to pay base fee (non EIP-1559 tx)")),!0===this.common.isActivatedEIP(4844)&&o instanceof ui&&(e+=BigInt(o.numBlobs())*n,e>r&&s.push(`tx causes total blob gas of ${e} to exceed maximum blob gas per block of ${r}`)),s.length>0&&t.push(`errors at tx ${i}: ${s.join(", ")}`)}return!0===this.common.isActivatedEIP(4844)&&e!==this.header.blobGasUsed&&t.push(`invalid blobGasUsed expected=${this.header.blobGasUsed} actual=${e}`),t}transactionsAreValid(){return 0===this.getTransactionsValidationErrors().length}async validateData(t=!1,e=!0){if(e){const t=this.getTransactionsValidationErrors();if(t.length>0){const e=this._errorMsg(`invalid transactions: ${t.join(" ")}`);throw new Error(e)}}if(!t){if(e)for(const[t,e]of this.transactions.entries())if(!e.isSigned()){const e=this._errorMsg(`invalid transactions: transaction at index ${t} is unsigned`);throw new Error(e)}if(!await this.transactionsTrieIsValid()){const t=this._errorMsg("invalid transaction trie");throw new Error(t)}if(!this.uncleHashIsValid()){const t=this._errorMsg("invalid uncle hash");throw new Error(t)}if(this.common.isActivatedEIP(4895)&&!await this.withdrawalsTrieIsValid()){const t=this._errorMsg("invalid withdrawals trie");throw new Error(t)}if(this.common.isActivatedEIP(6800)){if(void 0===this.executionWitness)throw new Error("Invalid block: missing executionWitness");if(null===this.executionWitness)throw new Error("Invalid block: ethereumjs stateless client needs executionWitness")}}}validateBlobTransactions(t){if(this.common.isActivatedEIP(4844)){const e=this.common.param("gasConfig","maxblobGasPerBlock"),r=this.common.param("gasConfig","blobGasPerBlob");let n=Ce;const i=t.calcNextExcessBlobGas();if(this.header.excessBlobGas!==i)throw new Error(`block excessBlobGas mismatch: have ${this.header.excessBlobGas}, want ${i}`);let o;for(const t of this.transactions)if(t instanceof ui){if(o=o??this.header.getBlobGasPrice(),t.maxFeePerBlobGas<o)throw new Error(`blob transaction maxFeePerBlobGas ${t.maxFeePerBlobGas} < than block blob gas price ${o} - ${this.errorStr()}`);if(n+=BigInt(t.blobVersionedHashes.length)*r,n>e)throw new Error(`tx causes total blob gas of ${n} to exceed maximum blob gas per block of ${e}`)}if(this.header.blobGasUsed!==n)throw new Error(`block blobGasUsed mismatch: have ${this.header.blobGasUsed}, want ${n}`)}}uncleHashIsValid(){const t=this.uncleHeaders.map((t=>t.raw())),e=Ve(t);return Yt(this.keccakFunction(e),this.header.uncleHash)}async withdrawalsTrieIsValid(){if(!this.common.isActivatedEIP(4895))throw new Error("EIP 4895 is not activated");return Yt(await vi.genWithdrawalsTrieRoot(this.withdrawals,new An({common:this.common})),this.header.withdrawalsRoot)}validateUncles(){if(this.isGenesis())return;if(this.uncleHeaders.length>2){const t=this._errorMsg("too many uncle headers");throw new Error(t)}const t=this.uncleHeaders.map((t=>ce(t.hash())));if(new Set(t).size!==t.length){const t=this._errorMsg("duplicate uncles");throw new Error(t)}}ethashCanonicalDifficulty(t){return this.header.ethashCanonicalDifficulty(t.header)}validateGasLimit(t){return this.header.validateGasLimit(t.header)}toJSON(){const t=this.withdrawals?{withdrawals:this.withdrawals.map((t=>t.toJSON()))}:{};return{header:this.header.toJSON(),transactions:this.transactions.map((t=>t.toJSON())),uncleHeaders:this.uncleHeaders.map((t=>t.toJSON())),...t}}errorStr(){let t="";try{t=ce(this.hash())}catch(e){t="error"}let e="";try{e=this.common.hardfork()}catch(t){e="error"}let r=`block number=${this.header.number} hash=${t} `;return r+=`hf=${e} baseFeePerGas=${this.header.baseFeePerGas??"none"} `,r+=`txs=${this.transactions.length} uncles=${this.uncleHeaders.length}`,r}_errorMsg(t){return`${t} (${this.errorStr()})`}}vi.fromJsonRpcProvider=async(t,e,r)=>{let n;const i=Gr(t);if("string"==typeof e&&66===e.length)n=await Ur(i,{method:"eth_getBlockByHash",params:[e,!0]});else if("bigint"==typeof e)n=await Ur(i,{method:"eth_getBlockByNumber",params:[Ee(e),!0]});else{if(!Xt(e)&&"latest"!==e&&"earliest"!==e&&"pending"!==e&&"finalized"!==e&&"safe"!==e)throw new Error(`expected blockTag to be block hash, bigint, hex prefixed string, or earliest/latest/pending; got ${e}`);n=await Ur(i,{method:"eth_getBlockByNumber",params:[e,!0]})}if(null===n)throw new Error("No block data returned from provider");const o=[];if(n.uncles.length>0)for(let t=0;t<n.uncles.length;t++){const e=await Ur(i,{method:"eth_getUncleByBlockHashAndIndex",params:[n.hash,de(t)]});o.push(e)}return wi(n,o,r)};var Ei,ki=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(h){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,h])}}},xi=function(){function t(){}return t.getFastMerkleProof=function(t,e,r,n){return i=this,o=void 0,a=function(){var i,o,s,a,h,c,u,l,f;return ki(this,(function(d){switch(d.label){case 0:i=Math.ceil(Math.log2(n-r+1)),o=[],a=e-(s=r),h=0,c=n-s,u=function(e){var r,n,u,f,d,p,m,g,y,b,w;return ki(this,(function(v){switch(v.label){case 0:return r=Math.pow(2,i-e),a>(n=h+r/2-1)?(u=n+1,[4,l.queryRootHash(t,s+h,s+n)]):[3,2];case 1:return w=v.sent(),o.push(w),h=u,[3,6];case 2:return f=Math.min(c,n),d=i-(e+1),c<=n?(w=l.recursiveZeroHash(d,t),o.push(w),[3,5]):[3,3];case 3:return p=Math.ceil(Math.log2(c-n)),m=d-p,[4,l.queryRootHash(t,s+n+1,s+c)];case 4:g=v.sent(),y=l.recursiveZeroHash(p,t),(b=Array.from({length:Math.pow(2,m)},(function(){return Kr.toBuffer(y)})))[0]=g,w=new $r(b).getRoot(),o.push(w),v.label=5;case 5:c=f,v.label=6;case 6:return[2]}}))},l=this,f=0,d.label=1;case 1:return f<i?[5,u(f)]:[3,4];case 2:d.sent(),d.label=3;case 3:return f+=1,[3,1];case 4:return[2,o.reverse()]}}))},new((s=void 0)||(s=Promise))((function(t,e){function r(t){try{h(a.next(t))}catch(t){e(t)}}function n(t){try{h(a.throw(t))}catch(t){e(t)}}function h(e){var i;e.done?t(e.value):(i=e.value,i instanceof s?i:new s((function(t){t(i)}))).then(r,n)}h((a=a.apply(i,o||[])).next())}));var i,o,s,a},t.buildBlockProof=function(e,r,n,i){return t.getFastMerkleProof(e,i,r,n).then((function(t){return Kr.bufferToHex(Buffer.concat(t.map((function(t){return Kr.toBuffer(t)}))))}))},t.queryRootHash=function(t,e,r){return t.getRootHash(e,r).then((function(t){return Kr.toBuffer("0x".concat(t))})).catch((function(t){return null}))},t.recursiveZeroHash=function(t,e){if(0===t)return"0x0000000000000000000000000000000000000000000000000000000000000000";var r=this.recursiveZeroHash(t-1,e);return zr.keccak256(Kr.toBuffer(e.encodeParameters([r,r],["bytes32","bytes32"])))},t.getReceiptProof=function(e,r,n,i,o){void 0===i&&(i=1/0);var s,a=Kr.bufferToHex(t.getStateSyncTxHash(r)),h=new An;if(o)s=Si(o);else{var c=[];r.transactions.forEach((function(t){t.transactionHash!==a&&c.push(n.getTransactionReceipt(t.transactionHash))})),s=v(c,(function(t){return t}),{concurrency:i})}return s.then((function(e){return Promise.all(e.map((function(e){var r=Zr.Ay.encode(e.transactionIndex),n=t.getReceiptBytes(e);return h.put(r,n)})))})).then((function(t){return h.findPath(Zr.Ay.encode(e.transactionIndex),!0)})).then((function(n){if(n.remaining.length>0)throw new Error("Node does not contain the key");return{blockHash:Kr.toBuffer(e.blockHash),parentNodes:n.stack.map((function(t){return t.raw()})),root:t.getRawHeader(r).receiptTrie,path:Zr.Ay.encode(e.transactionIndex),value:t.isTypedReceipt(e)?n.node.value:Zr.Ay.decode(n.node.value.toString())}}))},t.isTypedReceipt=function(t){var e=f.toHex(t.type);return null!=t.status&&"0x0"!==e&&"0x"!==e},t.getStateSyncTxHash=function(t){return zr.keccak256(Buffer.concat([Buffer.from("matic-bor-receipt-","utf-8"),ye(Kr.toBuffer(t.number),8),Kr.toBuffer(t.hash)]))},t.getReceiptBytes=function(e){var r=Zr.Ay.encode([Kr.toBuffer(void 0!==e.status&&null!=e.status?e.status?"0x1":"0x":e.root),Kr.toBuffer(e.cumulativeGasUsed),Kr.toBuffer(e.logsBloom),e.logs.map((function(t){return[Kr.toBuffer(t.address),t.topics.map(Kr.toBuffer),Kr.toBuffer(t.data)]}))]);return t.isTypedReceipt(e)&&(r=Buffer.concat([Kr.toBuffer(e.type),r])),r},t.getRawHeader=function(t){t.difficulty=f.toHex(t.difficulty);var e=new qn({chain:On.Mainnet,hardfork:Ln.London});return gi.fromHeaderData(t,{common:e,skipConsensusFormatValidation:!0})},t}(),_i="node"===process.env.BUILD_ENV?r(92).default:window.fetch,Pi=function(){function t(t){void 0===t&&(t={}),this.baseUrl="",(t="string"==typeof t?{baseUrl:t}:t).baseUrl&&(this.baseUrl=t.baseUrl)}return t.prototype.get=function(t,e){return void 0===t&&(t=""),void 0===e&&(e={}),t=this.baseUrl+t+Object.keys(e).map((function(t){return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(e[t]))})).join("&"),_i(t,{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}}).then((function(t){return t.json()}))},t.prototype.post=function(t,e){return void 0===t&&(t=""),t=this.baseUrl+t,_i(t,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:e?JSON.stringify(e):null}).then((function(t){return t.json()}))},t}(),Bi={1:"Main",5:"Main",11155111:"Main",137:"Matic",80001:"Matic",80002:"Matic",1442:"zkEVM",2442:"zkEVM",1101:"zkEVM"},Ai=function(){function t(){this.logger=new y}return t.prototype.init=function(t){(t=t||{}).parent.defaultConfig=t.parent.defaultConfig||{},t.child.defaultConfig=t.child.defaultConfig||{},this.config=t;var e=Yi.Web3Client;if(!e)throw new Error("Web3Client is not set");Yi.UnstoppableDomains&&(this.resolution=Yi.UnstoppableDomains),this.parent=new e(t.parent.provider,this.logger),this.child=new e(t.child.provider,this.logger),this.logger.enableLog(t.log);var r=t.network,n=t.version,i=this.abiManager=new Vi(r,n);return this.logger.log("init called",i),i.init().catch((function(t){throw new Error("network ".concat(r," - ").concat(n," is not supported"))}))},t.prototype.getABI=function(t,e){return this.abiManager.getABI(t,e)},t.prototype.getConfig=function(t){return this.abiManager.getConfig(t)},Object.defineProperty(t.prototype,"mainPlasmaContracts",{get:function(){return this.getConfig("Main.Contracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mainPOSContracts",{get:function(){return this.getConfig("Main.POSContracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mainZkEvmContracts",{get:function(){return this.getConfig("Main.Contracts")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zkEvmContracts",{get:function(){return this.getConfig("zkEVM.Contracts")},enumerable:!1,configurable:!0}),t.prototype.isEIP1559Supported=function(t){return this.getConfig("".concat(Bi[t],".SupportsEIP1559"))},t}(),Si=function(t){return Promise.resolve(t)},Ii=function(t){var e=new Array(t.length),r=0;return new Promise((function(n,i){t.forEach((function(o){Promise.resolve(o).then(n).catch((function(n){e[r]=n,(r+=1)===t.length&&i(e)}))}))}))},Ci="0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",Mi="0x0000000000000000000000000000000000000000",Ri="0xea2aa0a1be11a07ed86d755c93467f4f82362b452371d1ba94d1715123511acb",Ti="0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9",Oi="0x8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f",Li="0x8cad95687ba82c2ce50e74f7b754645e5117c3a5bec8151c0726d5857980a866",Fi=BigInt(Math.pow(2,64));!function(t){t.DAI="DAI",t.EIP_2612="EIP_2612",t.UNISWAP="UNISWAP"}(Ei||(Ei={}));var Ni=function(){function t(t,e){this.contractParam=t,this.client=e}return Object.defineProperty(t.prototype,"contractAddress",{get:function(){return this.contractParam.address},enumerable:!1,configurable:!0}),t.prototype.getContract=function(){var t=this;if(this.contract_)return Si(this.contract_);var e=this.contractParam;return this.client.getABI(e.name,e.bridgeType).then((function(r){return t.contract_=t.getContract_({abi:r,isParent:e.isParent,tokenAddress:e.address}),t.contract_}))},t.prototype.getChainId=function(){var t=this;return this.chainId_?Si(this.chainId_):this.getClient(this.contractParam.isParent).getChainId().then((function(e){return t.chainId_=e,t.chainId_}))},t.prototype.processWrite=function(t,e){var r=this;return void 0===e&&(e={}),this.validateTxOption_(e),this.client.logger.log("process write"),this.createTransactionConfig({txConfig:e,isWrite:!0,method:t,isParent:this.contractParam.isParent}).then((function(n){return r.client.logger.log("process write config"),e.returnTransaction?b(n,{data:t.encodeABI(),to:t.address}):t.write(n)}))},t.prototype.sendTransaction=function(t){void 0===t&&(t={}),this.validateTxOption_(t);var e=this.contractParam.isParent,r=this.getClient(e);return r.logger.log("process write"),this.createTransactionConfig({txConfig:t,isWrite:!0,method:null,isParent:this.contractParam.isParent}).then((function(e){return r.logger.log("process write config"),t.returnTransaction?e:r.write(e)}))},t.prototype.readTransaction=function(t){void 0===t&&(t={}),this.validateTxOption_(t);var e=this.contractParam.isParent,r=this.getClient(e);return r.logger.log("process read"),this.createTransactionConfig({txConfig:t,isWrite:!0,method:null,isParent:this.contractParam.isParent}).then((function(e){return r.logger.log("write tx config created"),t.returnTransaction?e:r.read(e)}))},t.prototype.validateTxOption_=function(t){("object"!=typeof t||Array.isArray(t))&&new g(o.TransactionOptionNotObject).throw()},t.prototype.processRead=function(t,e){var r=this;return void 0===e&&(e={}),this.validateTxOption_(e),this.client.logger.log("process read"),this.createTransactionConfig({txConfig:e,isWrite:!1,method:t,isParent:this.contractParam.isParent}).then((function(n){return r.client.logger.log("read tx config created"),e.returnTransaction?b(n,{data:t.encodeABI(),to:r.contract_.address}):t.read(n)}))},t.prototype.getClient=function(t){return t?this.client.parent:this.client.child},t.prototype.getContract_=function(t){var e=t.isParent,r=t.tokenAddress,n=t.abi;return this.getClient(e).getContract(r,n)},Object.defineProperty(t.prototype,"parentDefaultConfig",{get:function(){return this.client.config.parent.defaultConfig},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childDefaultConfig",{get:function(){return this.client.config.child.defaultConfig},enumerable:!1,configurable:!0}),t.prototype.createTransactionConfig=function(t){var e=this,r=t.txConfig,n=t.method,i=t.isParent,s=t.isWrite,a=i?this.parentDefaultConfig:this.childDefaultConfig;r=b(a,r||{});var h=i?this.client.parent:this.client.child;return h.logger.log("txConfig",r,"onRoot",i,"isWrite",s),s?this.getChainId().then((function(t){var s,a,c,u,l,f=r.maxFeePerGas,d=r.maxPriorityFeePerGas,p=e.client.isEIP1559Supported(t),m=f||d;return r.chainId=r.chainId||t,!p&&m&&h.logger.error(o.EIP1559NotSupported,i).throw(),Promise.all([r.gasLimit?r.gasLimit:(s={from:r.from,value:r.value,to:r.to},a=e,c=void 0,u=void 0,l=function(){var t,e;return function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(h){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,h])}}}(this,(function(r){switch(r.label){case 0:return n?[4,n.estimateGas(s)]:[3,2];case 1:return e=r.sent(),[3,4];case 2:return[4,h.estimateGas(s)];case 3:e=r.sent(),r.label=4;case 4:return t=e,[2,new Yi.BN(Math.trunc(1.15*Number(t))).toString()]}}))},new(u||(u=Promise))((function(t,e){function r(t){try{i(l.next(t))}catch(t){e(t)}}function n(t){try{i(l.throw(t))}catch(t){e(t)}}function i(e){var i;e.done?t(e.value):(i=e.value,i instanceof u?i:new u((function(t){t(i)}))).then(r,n)}i((l=l.apply(a,c||[])).next())}))),r.nonce?r.nonce:h.getTransactionCount(r.from,"pending")]).then((function(t){var e=t[0],n=t[1];return h.logger.log("options filled"),r.gasLimit=Number(e),r.nonce=n,r}))})):Si(r)},t.prototype.transferERC20=function(t,e,r){var n=this;return this.getContract().then((function(i){var o=i.method("transfer",t,f.toHex(e));return n.processWrite(o,r)}))},t.prototype.transferERC721=function(t,e,r,n){var i=this;return this.getContract().then((function(o){var s=o.method("transferFrom",t,e,r);return i.processWrite(s,n)}))},t.prototype.checkForNonNative=function(t){this.contractParam.address===Mi&&this.client.logger.error(o.AllowedOnNonNativeTokens,t).throw()},t.prototype.checkForRoot=function(t){this.contractParam.isParent||this.client.logger.error(o.AllowedOnRoot,t).throw()},t.prototype.checkForChild=function(t){this.contractParam.isParent&&this.client.logger.error(o.AllowedOnChild,t).throw()},t.prototype.checkAdapterPresent=function(t){this.contractParam.bridgeAdapterAddress||this.client.logger.error(o.BridgeAdapterNotFound,t).throw()},t.prototype.transferERC1155=function(t,e){var r=this;return this.getContract().then((function(n){var i=n.method("safeTransferFrom",t.from,t.to,f.toHex(t.tokenId),f.toHex(t.amount),t.data||"0x");return r.processWrite(i,e)}))},t}(),Di=function(){function t(t){this.httpRequest=new Pi(t)}return t.prototype.getABI=function(t,e,r,n){var i="".concat(t,"/").concat(e,"/artifacts/").concat(r,"/").concat(n,".json");return this.httpRequest.get(i).then((function(t){return t.abi}))},t.prototype.getAddress=function(t,e){var r="".concat(t,"/").concat(e,"/index.json");return this.httpRequest.get(r)},t}(),Hi={abiStoreUrl:"https://static.polygon.technology/network/",zkEvmBridgeService:"https://proof-generator.polygon.technology/"},Ui=function(){function t(t){this.httpRequest=new Pi(t)}return t.prototype.createUrlForPos=function(t,e){return"".concat("v1"===t?"matic":t).concat(e)},t.prototype.createUrlForZkEvm=function(t,e){return"".concat(t,"/").concat(e)},t.prototype.getBlockIncluded=function(t,e){var r=this.createUrlForPos(t,"/block-included/".concat(e));return this.httpRequest.get(r).then((function(t){var e=t.headerBlockNumber,r="0x"===e.slice(0,2)?parseInt(e,16):e;return t.headerBlockNumber=new Yi.BN(r),t}))},t.prototype.getExitProof=function(t,e,r){var n=this.createUrlForPos(t,"/exit-payload/".concat(e,"?eventSignature=").concat(r));return this.httpRequest.get(n).then((function(t){return t.result}))},t.prototype.getProof=function(t,e,r,n){var i=this.createUrlForPos(t,"/fast-merkle-proof?start=".concat(e,"&end=").concat(r,"&number=").concat(n));return this.httpRequest.get(i).then((function(t){return t.proof}))},t.prototype.getMerkleProofForZkEvm=function(t,e,r){var n=this.createUrlForZkEvm(t,"merkle-proof?net_id=".concat(e,"&deposit_cnt=").concat(r));return this.httpRequest.get(n).then((function(t){return t.proof}))},t.prototype.getBridgeTransactionDetails=function(t,e,r){var n=this.createUrlForZkEvm(t,"bridge?net_id=".concat(e,"&deposit_cnt=").concat(r));return this.httpRequest.get(n).then((function(t){return t.deposit}))},t}(),Gi=new function(){};Gi.abi=new Di(Hi.abiStoreUrl);var zi=function(t){"/"!==t[t.length-1]&&(t+="/"),t+="api/v1/",Gi.network=new Ui(t)},ji=function(t){"/"!==t[t.length-1]&&(t+="/"),t+="api/zkevm/",Gi.zkEvmNetwork=new Ui(t)};function qi(t,e){return(Array.isArray(e)?e:e.split(".")).reduce((function(t,e){return t&&t[e]}),t)}var $i=function(){function t(){this.client=new Ai}return t.prototype.isCheckPointed=function(t){return this.exitUtil.isCheckPointed(t)},t.prototype.isDeposited=function(t){var e=this.client,r=new Ni({address:e.abiManager.getConfig("Matic.GenesisContracts.StateReceiver"),isParent:!1,name:"StateReceiver",bridgeType:"genesis"},e);return r.getContract().then((function(n){return Promise.all([e.parent.getTransactionReceipt(t),r.processRead(n.method("lastStateId"))])})).then((function(t){var r=t[0],n=t[1],i=r.logs.find((function(t){return"0x103fed9db65eac19c4d870f49ab7520fe03b99f1838e5996caf47e9e43308392"===t.topics[0]}));if(!i)throw new Error("StateSynced event not found");var o=e.child.decodeParameters(i.topics[1],["uint256"])[0],s=Yi.BN.isBN(o)?o:new Yi.BN(o);return new Yi.BN(n).gte(s)}))},t}(),Wi={},Vi=function(){function t(t,e){this.networkName=t,this.version=e}return t.prototype.init=function(){var t=this;return Gi.abi.getAddress(this.networkName,this.version).then((function(e){var r;Wi[t.networkName]=((r={})[t.version]={address:e,abi:{}},r)}))},t.prototype.getConfig=function(t){return qi(Wi[this.networkName][this.version].address,t)},t.prototype.getABI=function(t,e){var r,n=this;if(void 0===e&&(e="plasma"),Wi[this.networkName]&&Wi[this.networkName][this.version]&&Wi[this.networkName][this.version].abi&&(r=Wi[this.networkName][this.version].abi[e]),r){var i=r[t];if(i)return Si(i)}return Gi.abi.getABI(this.networkName,this.version,e,t).then((function(r){return n.setABI(t,e,r),r}))},t.prototype.setABI=function(t,e,r){var n=Wi[this.networkName][this.version].abi;n[e]||(n[e]={}),n[e][t]=r},t}(),Ki=function(){throw new Error("not implemented")},Zi=function(){function t(){this.client=new Ai}return t.prototype.isDepositClaimable=function(t){var e=this;return Promise.all([this.rootChainBridge.networkID(),this.bridgeUtil.getBridgeLogData(t,!0)]).then((function(t){return Gi.zkEvmNetwork.getBridgeTransactionDetails(e.client.config.version,t[0],t[1].depositCount)})).then((function(t){return t.ready_for_claim}))},t.prototype.isWithdrawExitable=function(t){var e=this;return Promise.all([this.childChainBridge.networkID(),this.bridgeUtil.getBridgeLogData(t,!1)]).then((function(t){return Gi.zkEvmNetwork.getBridgeTransactionDetails(e.client.config.version,t[0],t[1].depositCount)})).then((function(t){return t.ready_for_claim}))},t.prototype.isDeposited=function(t){var e=this;return this.bridgeUtil.getBridgeLogData(t,!0).then((function(t){return e.childChainBridge.isClaimed(t.depositCount,0)}))},t.prototype.isExited=function(t){var e=this;return this.bridgeUtil.getBridgeLogData(t,!1).then((function(t){return e.rootChainBridge.isClaimed(t.depositCount,1)}))},t}(),Yi={converter:f,Web3Client:a,BN:l,UnstoppableDomains:Object},Ji=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Xi=function(t){function e(e,r,n){var i=t.call(this,e,r)||this;return i.getPOSContracts=n,i}return Ji(e,t),Object.defineProperty(e.prototype,"rootChainManager",{get:function(){return this.getPOSContracts().rootChainManager},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"gasSwapper",{get:function(){return this.getPOSContracts().gasSwapper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"exitUtil",{get:function(){return this.getPOSContracts().exitUtil},enumerable:!1,configurable:!0}),e.prototype.getPredicateAddress=function(){var t=this;return this.predicateAddress?Si(this.predicateAddress):this.rootChainManager.method("tokenToType",this.contractParam.address).then((function(t){return t.read()})).then((function(e){if(!e)throw new Error("Invalid Token Type");return t.rootChainManager.method("typeToPredicate",e)})).then((function(t){return t.read()})).then((function(e){return t.predicateAddress=e,e}))},e.prototype.isWithdrawn=function(t,e){var r=this;if(!t)throw new Error("txHash not provided");return this.exitUtil.getExitHash(t,0,e).then((function(t){return r.rootChainManager.isExitProcessed(t)}))},e.prototype.isWithdrawnOnIndex=function(t,e,r){var n=this;if(!t)throw new Error("txHash not provided");return this.exitUtil.getExitHash(t,e,r).then((function(t){return n.rootChainManager.isExitProcessed(t)}))},e.prototype.withdrawExitPOS=function(t,e,r,n){var i=this;return this.exitUtil.buildPayloadForExit(t,e,r).then((function(t){return i.rootChainManager.exit(t,n)}))},e}(Ni),Qi=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),to=function(t){function e(e,r,n,i){return t.call(this,{isParent:r,address:e,name:"ChildERC20",bridgeType:"pos"},n,i)||this}return Qi(e,t),e.prototype.getBalance=function(t,e){var r=this;return this.getContract().then((function(n){var i=n.method("balanceOf",t);return r.processRead(i,e)}))},e.prototype.getAllowance=function(t,e){var r=this;void 0===e&&(e={});var n=e.spenderAddress,i=n?Si(n):this.getPredicateAddress();return Promise.all([i,this.getContract()]).then((function(n){var i=n[0],o=n[1].method("allowance",t,i);return r.processRead(o,e)}))},e.prototype.approve=function(t,e){var r=this;void 0===e&&(e={});var n=e.spenderAddress;n||this.contractParam.isParent||this.client.logger.error(o.NullSpenderAddress).throw();var i=n?Si(n):this.getPredicateAddress();return Promise.all([i,this.getContract()]).then((function(n){var i=n[0],o=n[1].method("approve",i,f.toHex(t));return r.processWrite(o,e)}))},e.prototype.approveMax=function(t){return void 0===t&&(t={}),this.approve(Ci,t)},e.prototype.deposit=function(t,e,r){this.checkForRoot("deposit");var n=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return this.rootChainManager.deposit(e,this.contractParam.address,n,r)},e.prototype.depositWithGas=function(t,e,r,n,i){var s=this;return this.checkForRoot("deposit"),this.getChainId().then((function(a){1!==a&&s.client.logger.error(o.AllowedOnMainnet).throw();var h=s.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return i.value=f.toHex(r),s.gasSwapper.depositWithGas(s.contractParam.address,h,e,n,i)}))},e.prototype.depositEther_=function(t,e,r){var n=this;return void 0===r&&(r={}),this.checkForRoot("depositEther"),r.value=f.toHex(t),this.rootChainManager.method("depositEtherFor",e).then((function(t){return n.processWrite(t,r)}))},e.prototype.depositEtherWithGas_=function(t,e,r,n,i){var s=this;return void 0===i&&(i={}),this.checkForRoot("depositEtherWithGas"),this.getChainId().then((function(a){1!==a&&s.client.logger.error(o.AllowedOnMainnet).throw();var h=s.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return i.value=f.toHex(f.toBN(t).add(f.toBN(r))),s.gasSwapper.depositWithGas("******************************************",h,e,n,i)}))},e.prototype.withdrawStart=function(t,e){var r=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(n){var i=n.method("withdraw",f.toHex(t));return r.processWrite(i,e)}))},e.prototype.withdrawExit_=function(t,e,r){var n=this;void 0===r&&(r={});var o=r.burnEventSignature?r.burnEventSignature:i.Erc20Transfer;return this.exitUtil.buildPayloadForExit(t,o,e).then((function(t){return n.rootChainManager.exit(t,r)}))},e.prototype.withdrawExit=function(t,e){return this.checkForRoot("withdrawExit"),this.withdrawExit_(t,!1,e)},e.prototype.withdrawExitFaster=function(t,e){return this.checkForRoot("withdrawExitFaster"),this.withdrawExit_(t,!0,e)},e.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,i.Erc20Transfer)},e.prototype.transfer=function(t,e,r){return this.transferERC20(e,t,r)},e}(Xi),eo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ro=function(t){function e(e,r){return t.call(this,{address:r,name:"RootChainManager",bridgeType:"pos",isParent:!0},e)||this}return eo(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.deposit=function(t,e,r,n){var i=this;return this.method("depositFor",t,e,r).then((function(t){return i.processWrite(t,n)}))},e.prototype.exit=function(t,e){var r=this;return this.method("exit",t).then((function(t){return r.processWrite(t,e)}))},e.prototype.isExitProcessed=function(t){var e=this;return this.method("processedExits",t).then((function(t){return e.processRead(t)}))},e}(Ni),no=function(){function t(t,e){this.maticClient_=t.child,this.rootChain=e;var r=t.config;this.config=r,this.requestConcurrency=r.requestConcurrency}return t.prototype.getLogIndex_=function(t,e){var r=-1;switch(t){case"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef":case"0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14":r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[2].toLowerCase()}));break;case"0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62":case"0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb":r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[3].toLowerCase()}));break;default:r=e.logs.findIndex((function(e){return e.topics[0].toLowerCase()===t.toLowerCase()}))}if(r<0)throw new Error("Log not found in receipt");return r},t.prototype.getAllLogIndices_=function(t,e){var r=[];switch(t){case"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef":case"0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14":r=e.logs.reduce((function(e,n,i){return n.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===n.topics[2].toLowerCase()&&r.push(i),r}),[]);break;case"0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62":case"0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb":r=e.logs.reduce((function(e,n,i){return n.topics[0].toLowerCase()===t.toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===n.topics[3].toLowerCase()&&r.push(i),r}),[]);break;case"0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df":r=e.logs.reduce((function(t,e,n){return"0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"===e.topics[0].toLowerCase()&&"0x0000000000000000000000000000000000000000000000000000000000000000"===e.topics[2].toLowerCase()&&r.push(n),r}),[]);break;default:r=e.logs.reduce((function(e,n,i){return n.topics[0].toLowerCase()===t.toLowerCase()&&r.push(i),r}),[])}if(0===r.length)throw new Error("Log not found in receipt");return r},t.prototype.getChainBlockInfo=function(t){return Promise.all([this.rootChain.getLastChildBlock(),this.maticClient_.getTransaction(t)]).then((function(t){return{lastChildBlock:t[0],txBlockNumber:t[1].blockNumber}}))},t.prototype.isCheckPointed_=function(t){return new Yi.BN(t.lastChildBlock).gte(new Yi.BN(t.txBlockNumber))},t.prototype.isCheckPointed=function(t){var e=this;return this.getChainBlockInfo(t).then((function(t){return e.isCheckPointed_(t)}))},t.prototype.getRootBlockInfo=function(t){var e,r=this;return this.rootChain.findRootBlockFromChild(t).then((function(t){return e=t,r.rootChain.method("headerBlocks",f.toHex(t))})).then((function(t){return t.read()})).then((function(t){return{headerBlockNumber:e,end:t.end.toString(),start:t.start.toString()}}))},t.prototype.getRootBlockInfoFromAPI=function(t){var e=this;return this.maticClient_.logger.log("block info from API 1"),Gi.network.getBlockIncluded(this.config.version,t).then((function(t){if(e.maticClient_.logger.log("block info from API 2",t),!(t&&t.start&&t.end&&t.headerBlockNumber))throw Error("Network API Error");return t})).catch((function(r){return e.maticClient_.logger.log("block info from API",r),e.getRootBlockInfo(t)}))},t.prototype.getBlockProof=function(t,e){return xi.buildBlockProof(this.maticClient_,parseInt(e.start,10),parseInt(e.end,10),parseInt(t+"",10))},t.prototype.getBlockProofFromAPI=function(t,e){var r=this;return Gi.network.getProof(this.config.version,e.start,e.end,t).then((function(t){if(!t)throw Error("Network API Error");return r.maticClient_.logger.log("block proof from API 1"),t})).catch((function(n){return r.getBlockProof(t,e)}))},t.prototype.getExitProofFromAPI=function(t,e){var r=this;return Gi.network.getExitProof(this.config.version,t,e).then((function(t){if(!t)throw Error("Network API Error");return r.maticClient_.logger.log("exit proof from API 1"),t})).catch((function(n){return r.buildPayloadForExit(t,e,!1)}))},t.prototype.buildPayloadForExit=function(t,e,r,n){var i,s,a,h,c,u=this;if(void 0===n&&(n=0),r&&!Gi.network&&new g(o.ProofAPINotSet).throw(),n<0)throw new Error("Index must not be a negative integer");return r?this.getExitProofFromAPI(t,e):this.getChainBlockInfo(t).then((function(e){if(!u.isCheckPointed_(e))throw new Error("Burn transaction has not been checkpointed as yet");return i=e.txBlockNumber,Promise.all([u.maticClient_.getTransactionReceipt(t),u.maticClient_.getBlockWithTransaction(i)])})).then((function(t){return a=t[0],h=t[1],u.getRootBlockInfo(i)})).then((function(t){return s=t,u.getBlockProof(i,s)})).then((function(t){return c=t,xi.getReceiptProof(a,h,u.maticClient_,u.requestConcurrency)})).then((function(t){if(n>0){var r=u.getAllLogIndices_(e,a);if(n>=r.length)throw new Error("Index is greater than the number of tokens in this transaction");return u.encodePayload_(s.headerBlockNumber.toNumber(),c,i,h.timestamp,Buffer.from(h.transactionsRoot.slice(2),"hex"),Buffer.from(h.receiptsRoot.slice(2),"hex"),xi.getReceiptBytes(a),t.parentNodes,t.path,r[n])}var o=u.getLogIndex_(e,a);return u.encodePayload_(s.headerBlockNumber.toNumber(),c,i,h.timestamp,Buffer.from(h.transactionsRoot.slice(2),"hex"),Buffer.from(h.receiptsRoot.slice(2),"hex"),xi.getReceiptBytes(a),t.parentNodes,t.path,o)}))},t.prototype.buildMultiplePayloadsForExit=function(t,e,r){var n,i,s,a,h,c=this;return r&&!Gi.network&&new g(o.ProofAPINotSet).throw(),this.getChainBlockInfo(t).then((function(e){if(!r&&!c.isCheckPointed_(e))throw new Error("Burn transaction has not been checkpointed as yet");return n=e.txBlockNumber,Promise.all([c.maticClient_.getTransactionReceipt(t),c.maticClient_.getBlockWithTransaction(n)])})).then((function(t){return s=t[0],a=t[1],r?c.getRootBlockInfoFromAPI(n):c.getRootBlockInfo(n)})).then((function(t){return i=t,r?c.getBlockProofFromAPI(n,i):c.getBlockProof(n,i)})).then((function(t){return h=t,xi.getReceiptProof(s,a,c.maticClient_,c.requestConcurrency)})).then((function(t){for(var r=[],o=0,u=c.getAllLogIndices_(e,s);o<u.length;o++){var l=u[o];r.push(c.encodePayload_(i.headerBlockNumber.toNumber(),h,n,a.timestamp,Buffer.from(a.transactionsRoot.slice(2),"hex"),Buffer.from(a.receiptsRoot.slice(2),"hex"),xi.getReceiptBytes(s),t.parentNodes,t.path,l))}return r}))},t.prototype.encodePayload_=function(t,e,r,n,i,o,s,a,h,c){return Kr.bufferToHex(Zr.Ay.encode([t,e,r,n,Kr.bufferToHex(i),Kr.bufferToHex(o),Kr.bufferToHex(s),Kr.bufferToHex(Zr.Ay.encode(a)),Kr.bufferToHex(Buffer.concat([Buffer.from("00","hex"),h])),c]))},t.prototype.getExitHash=function(t,e,r){var n,i,s,a=this;return Promise.all([this.rootChain.getLastChildBlock(),this.maticClient_.getTransactionReceipt(t)]).then((function(t){return n=t[0],i=t[1],a.maticClient_.getBlockWithTransaction(i.blockNumber)})).then((function(t){return s=t,a.isCheckPointed_({lastChildBlock:n,txBlockNumber:i.blockNumber})||a.maticClient_.logger.error(o.BurnTxNotCheckPointed).throw(),xi.getReceiptProof(i,s,a.maticClient_,a.requestConcurrency)})).then((function(t){var n,o=[];return t.path.forEach((function(t){o.push(Buffer.from("0"+(t/16).toString(16),"hex")),o.push(Buffer.from("0"+(t%16).toString(16),"hex"))})),e>0&&(n=a.getAllLogIndices_(r,i)[e]),n=a.getLogIndex_(r,i),a.maticClient_.etheriumSha3(i.blockNumber,Kr.bufferToHex(Buffer.concat(o)),n)}))},t}(),io=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oo=function(t){function e(e,r){return t.call(this,{address:r,name:"RootChain",isParent:!0},e)||this}return io(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.getLastChildBlock=function(){var t=this;return this.method("getLastChildBlock").then((function(e){return e.read({},t.client.config.rootChainDefaultBlock||"safe")}))},e.prototype.findRootBlockFromChild=function(t){return e=this,r=void 0,i=function(){var e,r,n,i,o,s,a,h,c,u,l;return function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(h){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,n=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,h])}}}(this,(function(f){switch(f.label){case 0:return e=new Yi.BN(1),r=new Yi.BN(2),n=new Yi.BN(1e4),t=new Yi.BN(t),i=e,[4,this.method("currentHeaderBlock")];case 1:return[4,f.sent().read()];case 2:o=f.sent(),s=new Yi.BN(o).div(n),f.label=3;case 3:return i.lte(s)?i.eq(s)?(a=i,[3,6]):(h=i.add(s).div(r),[4,this.method("headerBlocks",h.mul(n).toString())]):[3,6];case 4:return[4,f.sent().read()];case 5:return c=f.sent(),u=new Yi.BN(c.start),l=new Yi.BN(c.end),u.lte(t)&&t.lte(l)?(a=h,[3,6]):(u.gt(t)?s=h.sub(e):l.lt(t)&&(i=h.add(e)),[3,3]);case 6:return[2,a.mul(n)]}}))},new((n=void 0)||(n=Promise))((function(t,o){function s(t){try{h(i.next(t))}catch(t){o(t)}}function a(t){try{h(i.throw(t))}catch(t){o(t)}}function h(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(t){t(r)}))).then(s,a)}h((i=i.apply(e,r||[])).next())}));var e,r,n,i},e}(Ni),so=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ao=function(t){function e(e,r,n,i){return t.call(this,{isParent:r,address:e,name:"ChildERC721",bridgeType:"pos"},n,i)||this}return so(e,t),e.prototype.validateMany_=function(t){if(t.length>20)throw new Error("can not process more than 20 tokens");return t.map((function(t){return f.toHex(t)}))},e.prototype.getTokensCount=function(t,e){var r=this;return this.getContract().then((function(n){var i=n.method("balanceOf",t);return r.processRead(i,e)})).then((function(t){return Number(t)}))},e.prototype.getTokenIdAtIndexForUser=function(t,e,r){var n=this;return this.getContract().then((function(i){var o=i.method("tokenOfOwnerByIndex",e,t);return n.processRead(o,r)}))},e.prototype.getAllTokens=function(t,e){var r=this;return void 0===e&&(e=1/0),this.getTokensCount(t).then((function(n){(n=Number(n))>e&&(n=e);for(var i=[],o=0;o<n;o++)i.push(r.getTokenIdAtIndexForUser(o,t));return Promise.all(i)}))},e.prototype.isApproved=function(t,e){var r=this;return this.checkForRoot("isApproved"),this.getContract().then((function(n){var i=n.method("getApproved",t);return Promise.all([r.processRead(i,e),r.getPredicateAddress()]).then((function(t){return t[0]===t[1]}))}))},e.prototype.isApprovedAll=function(t,e){var r=this;return this.checkForRoot("isApprovedAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var i=n[0],o=n[1],s=i.method("isApprovedForAll",t,o);return r.processRead(s,e)}))},e.prototype.approve=function(t,e){var r=this;return this.checkForRoot("approve"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var i=n[0],o=n[1],s=i.method("approve",o,f.toHex(t));return r.processWrite(s,e)}))},e.prototype.approveAll=function(t){var e=this;return this.checkForRoot("approveAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(r){var n=r[0],i=r[1],o=n.method("setApprovalForAll",i,!0);return e.processWrite(o,t)}))},e.prototype.deposit=function(t,e,r){this.checkForRoot("deposit");var n=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return this.rootChainManager.deposit(e,this.contractParam.address,n,r)},e.prototype.depositMany=function(t,e,r){this.checkForRoot("depositMany");var n=this.validateMany_(t),i=this.client.parent.encodeParameters([n],["uint256[]"]);return this.rootChainManager.deposit(e,this.contractParam.address,i,r)},e.prototype.withdrawStart=function(t,e){var r=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(n){var i=n.method("withdraw",f.toHex(t));return r.processWrite(i,e)}))},e.prototype.withdrawStartWithMetaData=function(t,e){var r=this;return this.checkForChild("withdrawStartWithMetaData"),this.getContract().then((function(n){var i=n.method("withdrawWithMetadata",f.toHex(t));return r.processWrite(i,e)}))},e.prototype.withdrawStartMany=function(t,e){var r=this;this.checkForChild("withdrawStartMany");var n=this.validateMany_(t);return this.getContract().then((function(t){var i=t.method("withdrawBatch",n);return r.processWrite(i,e)}))},e.prototype.withdrawExit=function(t,e){var r=this;return this.checkForRoot("withdrawExit"),this.exitUtil.buildPayloadForExit(t,i.Erc721Transfer,!1).then((function(t){return r.rootChainManager.exit(t,e)}))},e.prototype.withdrawExitOnIndex=function(t,e,r){var n=this;return this.checkForRoot("withdrawExit"),this.exitUtil.buildPayloadForExit(t,i.Erc721Transfer,!1,e).then((function(t){return n.rootChainManager.exit(t,r)}))},e.prototype.withdrawExitFaster=function(t,e){var r=this;return this.checkForRoot("withdrawExitFaster"),this.exitUtil.buildPayloadForExit(t,i.Erc721Transfer,!0).then((function(t){return r.rootChainManager.exit(t,e)}))},e.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,i.Erc721Transfer)},e.prototype.isWithdrawExitedMany=function(t){return this.isWithdrawn(t,i.Erc721BatchTransfer)},e.prototype.isWithdrawExitedOnIndex=function(t,e){return this.isWithdrawnOnIndex(t,e,i.Erc721Transfer)},e.prototype.transfer=function(t,e,r,n){return this.transferERC721(e,r,t,n)},e}(Xi),ho=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),co=function(t){function e(e,r,n,i){return t.call(this,{isParent:r,address:e,name:"ChildERC1155",bridgeType:"pos"},n,i)||this}return ho(e,t),Object.defineProperty(e.prototype,"addressConfig",{get:function(){return this.client.config.erc1155||{}},enumerable:!1,configurable:!0}),e.prototype.getAddress_=function(t){var e=this.addressConfig;return e[t]?Si(e[t]):this.client.getConfig(t)},e.prototype.getBalance=function(t,e,r){var n=this;return this.getContract().then((function(i){var o=i.method("balanceOf",t,f.toHex(e));return n.processRead(o,r)}))},e.prototype.isApprovedAll=function(t,e){var r=this;return this.checkForRoot("isApprovedAll"),Promise.all([this.getContract(),this.getPredicateAddress()]).then((function(n){var i=n[0],o=n[1],s=i.method("isApprovedForAll",t,o);return r.processRead(s,e)}))},e.prototype.approveAll_=function(t,e){var r=this;return this.checkForRoot("approve"),Promise.all([this.getContract(),t]).then((function(t){var n=t[0],i=t[1],o=n.method("setApprovalForAll",i,!0);return r.processWrite(o,e)}))},e.prototype.approveAll=function(t){return this.checkForRoot("approve"),this.approveAll_(this.getPredicateAddress(),t)},e.prototype.approveAllForMintable=function(t){return this.checkForRoot("approveForMintable"),this.approveAll_(this.getAddress_("Main.POSContracts.MintableERC1155PredicateProxy"),t)},e.prototype.deposit=function(t,e){return this.checkForRoot("deposit"),this.depositMany({amounts:[t.amount],tokenIds:[t.tokenId],userAddress:t.userAddress,data:t.data},e)},e.prototype.depositMany=function(t,e){this.checkForRoot("depositMany");var r=t.tokenIds,n=t.amounts,i=t.data,o=t.userAddress,s=f.toHex(0),a=this.client.parent.encodeParameters([r.map((function(t){return f.toHex(t)})),n.map((function(t){return f.toHex(t)})),i||s],["uint256[]","uint256[]","bytes"]);return this.rootChainManager.deposit(o,this.contractParam.address,a,e)},e.prototype.withdrawStart=function(t,e,r){var n=this;return this.checkForChild("withdrawStart"),this.getContract().then((function(i){var o=i.method("withdrawSingle",f.toHex(t),f.toHex(e));return n.processWrite(o,r)}))},e.prototype.withdrawStartMany=function(t,e,r){var n=this;this.checkForChild("withdrawStartMany");var i=t.map((function(t){return f.toHex(t)})),o=e.map((function(t){return f.toHex(t)}));return this.getContract().then((function(t){var e=t.method("withdrawBatch",i,o);return n.processWrite(e,r)}))},e.prototype.withdrawExit=function(t,e){return this.checkForRoot("withdrawExit"),this.withdrawExitPOS(t,i.Erc1155Transfer,!1,e)},e.prototype.withdrawExitFaster=function(t,e){return this.checkForRoot("withdrawExitFaster"),this.withdrawExitPOS(t,i.Erc1155Transfer,!0,e)},e.prototype.withdrawExitMany=function(t,e){return this.checkForRoot("withdrawExitMany"),this.withdrawExitPOS(t,i.Erc1155BatchTransfer,!1,e)},e.prototype.withdrawExitFasterMany=function(t,e){return this.checkForRoot("withdrawExitFasterMany"),this.withdrawExitPOS(t,i.Erc1155BatchTransfer,!0,e)},e.prototype.isWithdrawExited=function(t){return this.isWithdrawn(t,i.Erc1155Transfer)},e.prototype.isWithdrawExitedMany=function(t){return this.isWithdrawn(t,i.Erc1155BatchTransfer)},e.prototype.transfer=function(t,e){return this.transferERC1155(t,e)},e}(Xi),uo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),lo=function(t){function e(e,r){return t.call(this,{address:r,name:"GasSwapper",bridgeType:"pos",isParent:!0},e)||this}return uo(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.depositWithGas=function(t,e,r,n,i){var o=this;return this.method("swapAndBridge",t,e,r,n).then((function(t){return o.processWrite(t,i)}))},e}(Ni),fo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),po=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return fo(e,t),e.prototype.init=function(t){var e=this,r=this.client;return r.init(t).then((function(n){var i=r.mainPOSContracts;r.config=t=Object.assign({rootChainManager:i.RootChainManagerProxy,rootChain:r.mainPlasmaContracts.RootChainProxy,gasSwapper:i.GasSwapper},t),e.rootChainManager=new ro(e.client,t.rootChainManager);var o=new oo(e.client,t.rootChain);return e.exitUtil=new no(e.client,o),e.gasSwapper=new lo(e.client,t.gasSwapper),e}))},e.prototype.erc20=function(t,e){return new to(t,e,this.client,this.getContracts_.bind(this))},e.prototype.erc721=function(t,e){return new ao(t,e,this.client,this.getContracts_.bind(this))},e.prototype.erc1155=function(t,e){return new co(t,e,this.client,this.getContracts_.bind(this))},e.prototype.depositEther=function(t,e,r){return new to("",!0,this.client,this.getContracts_.bind(this)).depositEther_(t,e,r)},e.prototype.depositEtherWithGas=function(t,e,r,n,i){return new to("",!0,this.client,this.getContracts_.bind(this)).depositEtherWithGas_(t,e,r,n,i)},e.prototype.getContracts_=function(){return{exitUtil:this.exitUtil,rootChainManager:this.rootChainManager,gasSwapper:this.gasSwapper}},e}($i),mo={utils:Yi,use:d,POSClient:po},go=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),yo=function(t){function e(e,r,n){var i=t.call(this,e,r)||this;return i.getZkEvmContracts=n,i}return go(e,t),Object.defineProperty(e.prototype,"parentBridge",{get:function(){return this.getZkEvmContracts().parentBridge},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"zkEVMWrapper",{get:function(){return this.getZkEvmContracts().zkEVMWrapper},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"childBridge",{get:function(){return this.getZkEvmContracts().childBridge},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bridgeUtil",{get:function(){return this.getZkEvmContracts().bridgeUtil},enumerable:!1,configurable:!0}),e}(Ni),bo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),wo=function(t){function e(e,r,n){return t.call(this,{address:r,name:"ZkEVMBridgeAdapter",bridgeType:"zkevm",isParent:n},e)||this}return bo(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.bridgeToken=function(t,e,r,n){var i=this;return this.method("bridgeToken",t,f.toHex(e),r).then((function(t){return i.processWrite(t,n)}))},e}(Ni),vo=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Eo=function(t){function e(e,r,n,i,o){var s=t.call(this,{isParent:r,address:e,bridgeAdapterAddress:n,name:"ERC20",bridgeType:"zkevm"},i,o)||this;return n&&(s.bridgeAdapter=new wo(s.client,n,r)),s}return vo(e,t),e.prototype.getBridgeAddress=function(){return(this.contractParam.isParent?this.parentBridge:this.childBridge).contractAddress},e.prototype.isEtherToken=function(){return this.contractParam.address===Mi},e.prototype.getBalance=function(t,e){var r=this;return this.isEtherToken()?(this.contractParam.isParent?this.client.parent:this.client.child).getBalance(t):this.getContract().then((function(n){var i=n.method("balanceOf",t);return r.processRead(i,e)}))},e.prototype.isApprovalNeeded=function(){return!this.isEtherToken()&&(this.contractParam.isParent?this.parentBridge:this.childBridge).getOriginTokenInfo(this.contractParam.address).then((function(t){return t[1]===Mi}))},e.prototype.getAllowance=function(t,e){var r=this;void 0===e&&(e={}),this.checkForNonNative("getAllowance");var n=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getContract().then((function(i){var o=i.method("allowance",t,n);return r.processRead(o,e)}))},e.prototype.approve=function(t,e){var r=this;void 0===e&&(e={}),this.checkForNonNative("approve");var n=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getContract().then((function(i){var o=i.method("approve",n,f.toHex(t));return r.processWrite(o,e)}))},e.prototype.approveMax=function(t){return void 0===t&&(t={}),this.checkForNonNative("approveMax"),this.approve(Ci,t)},e.prototype.deposit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForRoot("deposit");var i=r.permitData||"0x",o=r.forceUpdateGlobalExitRoot||!0,s=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return this.isEtherToken()&&(r.value=f.toHex(t)),this.childBridge.networkID().then((function(t){return n.parentBridge.bridgeAsset(t,e,s,n.contractParam.address,o,i,r)}))},e.prototype.depositWithGas=function(t,e,r,n){void 0===n&&(n={}),this.checkForRoot("deposit");var i=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return n.value=f.toHex(r),n.v&&n.r&&n.s?this.zkEVMWrapper.depositPermitWithGas(this.contractParam.address,i,e,Math.floor((Date.now()+36e5)/1e3).toString(),n.v,n.r,n.s,n):this.zkEVMWrapper.depositWithGas(this.contractParam.address,i,e,n)},e.prototype.depositPermitWithGas=function(t,e,r,n){var i=this;void 0===n&&(n={}),this.checkForRoot("deposit"),this.checkForNonNative("getPermitData");var o=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return n.value=f.toHex(r),this.getPermitSignatureParams_(t,this.zkEVMWrapper.contractAddress).then((function(t){return i.zkEVMWrapper.depositPermitWithGas(i.contractParam.address,o,e,Math.floor((Date.now()+36e5)/1e3).toString(),t.v,t.r,t.s,n)}))},e.prototype.depositWithPermit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForRoot("deposit"),this.checkForNonNative("depositWithPermit");var i=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]),o=r.forceUpdateGlobalExitRoot||!0;return this.getPermitData(i,r).then((function(t){return n.childBridge.networkID().then((function(s){return n.parentBridge.bridgeAsset(s,e,i,n.contractParam.address,o,t,r)}))}))},e.prototype.depositCustomERC20=function(t,e,r){return void 0===r&&(r=!0),this.checkForRoot("depositCustomERC20"),this.checkAdapterPresent("depositCustomERC20"),this.checkForNonNative("depositCustomERC20"),this.bridgeAdapter.bridgeToken(e,t,r)},e.prototype.customERC20DepositClaim=function(t,e){var r=this;return this.checkForChild("customERC20DepositClaim"),this.parentBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!0,e)})).then((function(t){return r.childBridge.claimMessage(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.depositClaim=function(t,e){var r=this;return this.checkForChild("depositClaim"),this.parentBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!0,e)})).then((function(t){return r.childBridge.claimAsset(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.withdraw=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForChild("withdraw");var i=r.permitData||"0x",o=r.forceUpdateGlobalExitRoot||!0,s=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]);return this.isEtherToken()&&(r.value=f.toHex(t)),this.parentBridge.networkID().then((function(t){return n.childBridge.bridgeAsset(t,e,s,n.contractParam.address,o,i,r)}))},e.prototype.withdrawCustomERC20=function(t,e,r){return void 0===r&&(r=!0),this.checkForChild("withdrawCustomERC20"),this.checkAdapterPresent("depositCustomERC20"),this.checkForNonNative("withdrawCustomERC20"),this.bridgeAdapter.bridgeToken(e,t,r)},e.prototype.customERC20WithdrawExit=function(t,e){var r=this;return this.checkForRoot("customERC20WithdrawExit"),this.childBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!1,e)})).then((function(t){return r.parentBridge.claimMessage(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.withdrawWithPermit=function(t,e,r){var n=this;void 0===r&&(r={}),this.checkForChild("withdraw");var i=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]),o=r.forceUpdateGlobalExitRoot||!0;return this.getPermitData(i,r).then((function(t){return n.parentBridge.networkID().then((function(s){return n.childBridge.bridgeAsset(s,e,i,n.contractParam.address,o,t,r)}))}))},e.prototype.withdrawExit=function(t,e){var r=this;return this.checkForRoot("withdrawExit"),this.childBridge.networkID().then((function(e){return r.bridgeUtil.buildPayloadForClaim(t,!1,e)})).then((function(t){return r.parentBridge.claimAsset(t.smtProof,t.smtProofRollup,t.globalIndex,t.mainnetExitRoot,t.rollupExitRoot,t.originNetwork,t.originTokenAddress,t.destinationNetwork,t.destinationAddress,t.amount,t.metadata,e)}))},e.prototype.transfer=function(t,e,r){return void 0===r&&(r={}),this.contractParam.address===Mi?(r.to=e,r.value=f.toHex(t),this.sendTransaction(r)):this.transferERC20(e,t,r)},e.prototype.getPermit=function(){var t,e=this;return this.getContract().then((function(r){var n=(t=r).method("PERMIT_TYPEHASH");return e.processRead(n)})).then((function(r){switch(r){case Ri:return Ei.DAI;case Ti:var n=t.method("DOMAIN_TYPEHASH"),i=t.method("EIP712DOMAIN_HASH");return Ii([e.processRead(n),e.processRead(i)]).then((function(t){switch(t){case Oi:return Ei.EIP_2612;case Li:return Ei.UNISWAP;default:return Promise.reject(new Error("Unsupported domain typehash: ".concat(t)))}}));default:return Promise.reject(new Error("Unsupported permit typehash: ".concat(r)))}}))},e.prototype.getTypedData_=function(t,e,r,n,i,o,s){var a={types:{EIP712Domain:[{name:"name",type:"string"},{name:"version",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"}],Permit:[]},primaryType:"Permit",domain:{name:n,version:"1",chainId:r,verifyingContract:this.contractParam.address},message:{}};switch(t){case Ei.DAI:a.types.Permit=[{name:"holder",type:"address"},{name:"spender",type:"address"},{name:"nonce",type:"uint256"},{name:"expiry",type:"uint256"},{name:"allowed",type:"bool"}],a.message={holder:e,spender:o,nonce:i,expiry:Math.floor((Date.now()+36e5)/1e3),allowed:!0};case Ei.EIP_2612:case Ei.UNISWAP:t===Ei.UNISWAP&&(a.types.EIP712Domain=[{name:"name",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"}],delete a.domain.version),a.types.Permit=[{name:"owner",type:"address"},{name:"spender",type:"address"},{name:"value",type:"uint256"},{name:"nonce",type:"uint256"},{name:"deadline",type:"uint256"}],a.message={owner:e,spender:o,value:s,nonce:i,deadline:Math.floor((Date.now()+36e5)/1e3)}}return a},e.prototype.getSignatureParameters_=function(t,e){if(!te(e))throw new Error('Given value "'.concat(e,'" is not a valid hex string.'));"0x"!==e.slice(0,2)&&(e="0x".concat(e));var r=e.slice(0,66),n="0x".concat(e.slice(66,130)),i=t.hexToNumber("0x".concat(e.slice(130,132)));return[27,28].includes(i)||(i+=27),{r,s:n,v:i}},e.prototype.encodePermitFunctionData_=function(t,e,r,n,i,o,s){var a,h=r.r,c=r.s,u=r.v;switch(e){case Ei.DAI:a=t.method("permit",i,n,o,Math.floor((Date.now()+36e5)/1e3),!0,u,h,c);break;case Ei.EIP_2612:case Ei.UNISWAP:a=t.method("permit",i,n,s,Math.floor((Date.now()+36e5)/1e3),u,h,c)}return a.encodeABI()},e.prototype.getPermitSignatureParams_=function(t,e){var r,n,i,o,s,a=this,h=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]),c=this.contractParam.isParent?this.client.parent:this.client.child;return Promise.all(["WEB3"===c.name?c.getAccountsUsingRPC_():c.getAccounts(),this.getContract(),c.getChainId(),this.getPermit()]).then((function(t){r=t[0][0],o=t[1],n=t[2],i=t[3];var e=o.method("name"),s=o.method("nonces",r);return Promise.all([a.processRead(e),a.processRead(s)])})).then((function(t){var o=t[0];return s=t[1],a.getTypedData_(i,r,n,o,s,e,h)})).then((function(t){return c.signTypedData(r,t)})).then((function(t){return a.getSignatureParameters_(c,t)}))},e.prototype.getPermitData_=function(t,e){var r,n,i,o,s,a=this,h=this.client.parent.encodeParameters([f.toHex(t)],["uint256"]),c=this.contractParam.isParent?this.client.parent:this.client.child;return Promise.all(["WEB3"===c.name?c.getAccountsUsingRPC_():c.getAccounts(),this.getContract(),c.getChainId(),this.getPermit()]).then((function(t){r=t[0][0],o=t[1],n=t[2],i=t[3];var e=o.method("name"),s=o.method("nonces",r);return Promise.all([a.processRead(e),a.processRead(s)])})).then((function(t){var o=t[0];return s=t[1],a.getTypedData_(i,r,n,o,s,e,h)})).then((function(t){return c.signTypedData(r,t)})).then((function(t){var n=a.getSignatureParameters_(c,t);return a.encodePermitFunctionData_(o,i,n,e,r,s,h)}))},e.prototype.getPermitData=function(t,e){void 0===e&&(e={}),this.checkForNonNative("getPermitData");var r=e.spenderAddress?e.spenderAddress:this.getBridgeAddress();return this.getPermitData_(t,r)},e}(yo),ko=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),xo=function(t){function e(e,r,n){return t.call(this,{address:r,name:"PolygonZkEVMBridge",bridgeType:"zkevm",isParent:n},e)||this}return ko(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.bridgeAsset=function(t,e,r,n,i,o,s){var a=this;return void 0===o&&(o="0x"),this.method("bridgeAsset",t,e,f.toHex(r),n,i,o).then((function(t){return a.processWrite(t,s)}))},e.prototype.claimAsset=function(t,e,r,n,i,o,s,a,h,c,u,l){var f=this;return this.method("claimAsset",t,e,r,n,i,o,s,a,h,c,u).then((function(t){return f.processWrite(t,l)}))},e.prototype.bridgeMessage=function(t,e,r,n,i){var o=this;return void 0===n&&(n="0x"),this.method("bridgeMessage",t,e,r,n).then((function(t){return o.processWrite(t,i)}))},e.prototype.claimMessage=function(t,e,r,n,i,o,s,a,h,c,u,l){var f=this;return this.method("claimMessage",t,e,r,n,i,o,s,a,h,c,u).then((function(t){return f.processWrite(t,l)}))},e.prototype.getMappedTokenInfo=function(t,e){var r=this;return this.method("getTokenWrappedAddress",t,e).then((function(t){return r.processRead(t)}))},e.prototype.isClaimed=function(t,e){var r=this;return this.method("isClaimed",t,e).then((function(t){return r.processRead(t)}))},e.prototype.precalculatedMappedTokenInfo=function(t,e){var r=this;return this.method("precalculatedWrapperAddress",t,e).then((function(t){return r.processRead(t)}))},e.prototype.getOriginTokenInfo=function(t){var e=this;return this.method("wrappedTokenToTokenInfo",t).then((function(t){return e.processRead(t)}))},e.prototype.networkID=function(){var t=this;return this.networkID_?Si(this.networkID_):this.method("networkID").then((function(e){return t.processRead(e).then((function(e){return t.networkID_=e,e}))}))},e}(Ni),_o=function(){function t(t){this.BRIDGE_TOPIC="0x501781209a1f8899323b96b4ef08b168df93e0a90c673d1e4cce39366cb62f9b",this.client_=t}return t.prototype.decodedBridgeData_=function(t,e){var r=e?this.client_.parent:this.client_.child;return this.client_.getABI("PolygonZkEVMBridge","zkevm").then((function(e){var n=e.filter((function(t){return"BridgeEvent"===t.name}));if(!n.length)throw new Error("Data not decoded");var i=r.decodeParameters(t,n[0].inputs);return{leafType:i[0],originNetwork:i[1],originTokenAddress:i[2],destinationNetwork:i[3],destinationAddress:i[4],amount:i[5],metadata:i[6]||"0x",depositCount:i[7]}}))},t.prototype.getBridgeLogData_=function(t,e){var r=this;return(e?this.client_.parent:this.client_.child).getTransactionReceipt(t).then((function(t){var n=t.logs.filter((function(t){return t.topics[0].toLowerCase()===r.BRIDGE_TOPIC}));if(!n.length)throw new Error("Log not found in receipt");var i=n[0].data;return r.decodedBridgeData_(i,e)}))},t.prototype.getProof_=function(t,e){return Gi.zkEvmNetwork.getMerkleProofForZkEvm(this.client_.config.version,t,e).then((function(t){return t})).catch((function(t){throw new Error("Error in creating proof")}))},t.prototype.getBridgeLogData=function(t,e){return this.getBridgeLogData_(t,e)},t.prototype.computeGlobalIndex=function(t,e,r){return BigInt(r)===BigInt(0)?BigInt(t)+Fi:BigInt(t)+BigInt(e)*BigInt(Math.pow(2,32))},t.prototype.buildPayloadForClaim=function(t,e,r){var n=this;return this.getBridgeLogData_(t,e).then((function(t){var e=t.originNetwork,i=t.originTokenAddress,o=t.destinationNetwork,s=t.destinationAddress,a=t.amount,h=t.metadata,c=t.depositCount;return n.getProof_(r,c).then((function(t){var u={};return u.smtProof=t.merkle_proof,u.smtProofRollup=t.rollup_merkle_proof,u.globalIndex=n.computeGlobalIndex(c,o,r).toString(),u.mainnetExitRoot=t.main_exit_root,u.rollupExitRoot=t.rollup_exit_root,u.originNetwork=e,u.originTokenAddress=i,u.destinationNetwork=o,u.destinationAddress=s,u.amount=a,u.metadata=h,u}))}))},t}(),Po=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Bo=function(t){function e(e,r){return t.call(this,{address:r,name:"ZkEVMWrapper",bridgeType:"zkevm",isParent:!0},e)||this}return Po(e,t),e.prototype.method=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return this.getContract().then((function(r){return r.method.apply(r,function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}([t],e,!1))}))},e.prototype.depositWithGas=function(t,e,r,n){var i=this;return this.method("deposit",t,e,r).then((function(t){return i.processWrite(t,n)}))},e.prototype.depositPermitWithGas=function(t,e,r,n,i,o,s,a){var h=this;return this.method("deposit",t,e,r,n,i,o,s).then((function(t){return h.processWrite(t,a)}))},e}(Ni),Ao=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),So=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Ao(e,t),e.prototype.init=function(t){var e=this,r=this.client;return r.init(t).then((function(n){var i=r.mainZkEvmContracts,o=r.zkEvmContracts;return r.config=t=Object.assign({parentBridge:i.PolygonZkEVMBridgeProxy,childBridge:o.PolygonZkEVMBridge,zkEVMWrapper:i.ZkEVMWrapper},t),e.rootChainBridge=new xo(e.client,t.parentBridge,!0),e.childChainBridge=new xo(e.client,t.childBridge,!1),e.zkEVMWrapper=new Bo(e.client,t.zkEVMWrapper),e.bridgeUtil=new _o(e.client),Gi.zkEvmNetwork||("/"!==Hi.zkEvmBridgeService[Hi.zkEvmBridgeService.length-1]&&(Hi.zkEvmBridgeService+="/"),Hi.zkEvmBridgeService+="api/zkevm/",Gi.zkEvmNetwork=new Ui(Hi.zkEvmBridgeService)),e}))},e.prototype.erc20=function(t,e,r){return new Eo(t,e,r,this.client,this.getContracts_.bind(this))},e.prototype.getContracts_=function(){return{parentBridge:this.rootChainBridge,childBridge:this.childChainBridge,bridgeUtil:this.bridgeUtil,zkEVMWrapper:this.zkEVMWrapper}},e}(Zi);const Io=mo})(),n})()));
//# sourceMappingURL=matic.js.map