{"version": 3, "sources": ["../../src/types/generated/queries.ts"], "sourcesContent": ["import * as Types from \"./operations\";\n\nimport { GraphQLClient, RequestOptions } from \"graphql-request\";\ntype GraphQLClientRequestHeaders = RequestOptions[\"requestHeaders\"];\nexport const TokenActivitiesFieldsFragmentDoc = `\n    fragment TokenActivitiesFields on token_activities_v2 {\n  after_value\n  before_value\n  entry_function_id_str\n  event_account_address\n  event_index\n  from_address\n  is_fungible_v2\n  property_version_v1\n  to_address\n  token_amount\n  token_data_id\n  token_standard\n  transaction_timestamp\n  transaction_version\n  type\n}\n    `;\nexport const AnsTokenFragmentFragmentDoc = `\n    fragment AnsTokenFragment on current_aptos_names {\n  domain\n  expiration_timestamp\n  registered_address\n  subdomain\n  token_standard\n  is_primary\n  owner_address\n  subdomain_expiration_policy\n  domain_expiration_timestamp\n}\n    `;\nexport const CurrentTokenOwnershipFieldsFragmentDoc = `\n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    `;\nexport const GetAccountCoinsCount = `\n    query getAccountCoinsCount($address: String) {\n  current_fungible_asset_balances_aggregate(\n    where: {owner_address: {_eq: $address}}\n  ) {\n    aggregate {\n      count\n    }\n  }\n}\n    `;\nexport const GetAccountCoinsData = `\n    query getAccountCoinsData($where_condition: current_fungible_asset_balances_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_fungible_asset_balances_order_by!]) {\n  current_fungible_asset_balances(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    amount\n    asset_type\n    is_frozen\n    is_primary\n    last_transaction_timestamp\n    last_transaction_version\n    owner_address\n    storage_id\n    token_standard\n    metadata {\n      token_standard\n      symbol\n      supply_aggregator_table_key_v1\n      supply_aggregator_table_handle_v1\n      project_uri\n      name\n      last_transaction_version\n      last_transaction_timestamp\n      icon_uri\n      decimals\n      creator_address\n      asset_type\n    }\n  }\n}\n    `;\nexport const GetAccountCollectionsWithOwnedTokens = `\n    query getAccountCollectionsWithOwnedTokens($where_condition: current_collection_ownership_v2_view_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_collection_ownership_v2_view_order_by!]) {\n  current_collection_ownership_v2_view(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      mutable_description\n      max_supply\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n    collection_id\n    collection_name\n    collection_uri\n    creator_address\n    distinct_tokens\n    last_transaction_version\n    owner_address\n    single_token_uri\n  }\n}\n    `;\nexport const GetAccountOwnedTokens = `\n    query getAccountOwnedTokens($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    ${CurrentTokenOwnershipFieldsFragmentDoc}`;\nexport const GetAccountOwnedTokensByTokenData = `\n    query getAccountOwnedTokensByTokenData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    ${CurrentTokenOwnershipFieldsFragmentDoc}`;\nexport const GetAccountOwnedTokensFromCollection = `\n    query getAccountOwnedTokensFromCollection($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    ${CurrentTokenOwnershipFieldsFragmentDoc}`;\nexport const GetAccountTokensCount = `\n    query getAccountTokensCount($where_condition: current_token_ownerships_v2_bool_exp, $offset: Int, $limit: Int) {\n  current_token_ownerships_v2_aggregate(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    aggregate {\n      count\n    }\n  }\n}\n    `;\nexport const GetAccountTransactionsCount = `\n    query getAccountTransactionsCount($address: String) {\n  account_transactions_aggregate(where: {account_address: {_eq: $address}}) {\n    aggregate {\n      count\n    }\n  }\n}\n    `;\nexport const GetChainTopUserTransactions = `\n    query getChainTopUserTransactions($limit: Int) {\n  user_transactions(limit: $limit, order_by: {version: desc}) {\n    version\n  }\n}\n    `;\nexport const GetCollectionData = `\n    query getCollectionData($where_condition: current_collections_v2_bool_exp!) {\n  current_collections_v2(where: $where_condition) {\n    uri\n    total_minted_v2\n    token_standard\n    table_handle_v1\n    mutable_uri\n    mutable_description\n    max_supply\n    collection_id\n    collection_name\n    creator_address\n    current_supply\n    description\n    last_transaction_timestamp\n    last_transaction_version\n    cdn_asset_uris {\n      cdn_image_uri\n      asset_uri\n      animation_optimizer_retry_count\n      cdn_animation_uri\n      cdn_json_uri\n      image_optimizer_retry_count\n      json_parser_retry_count\n      raw_animation_uri\n      raw_image_uri\n    }\n  }\n}\n    `;\nexport const GetCurrentFungibleAssetBalances = `\n    query getCurrentFungibleAssetBalances($where_condition: current_fungible_asset_balances_bool_exp, $offset: Int, $limit: Int) {\n  current_fungible_asset_balances(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    amount\n    asset_type\n    is_frozen\n    is_primary\n    last_transaction_timestamp\n    last_transaction_version\n    owner_address\n    storage_id\n    token_standard\n  }\n}\n    `;\nexport const GetDelegatedStakingActivities = `\n    query getDelegatedStakingActivities($delegatorAddress: String, $poolAddress: String) {\n  delegated_staking_activities(\n    where: {delegator_address: {_eq: $delegatorAddress}, pool_address: {_eq: $poolAddress}}\n  ) {\n    amount\n    delegator_address\n    event_index\n    event_type\n    pool_address\n    transaction_version\n  }\n}\n    `;\nexport const GetEvents = `\n    query getEvents($where_condition: events_bool_exp, $offset: Int, $limit: Int, $order_by: [events_order_by!]) {\n  events(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    account_address\n    creation_number\n    data\n    event_index\n    sequence_number\n    transaction_block_height\n    transaction_version\n    type\n    indexed_type\n  }\n}\n    `;\nexport const GetFungibleAssetActivities = `\n    query getFungibleAssetActivities($where_condition: fungible_asset_activities_bool_exp, $offset: Int, $limit: Int) {\n  fungible_asset_activities(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    amount\n    asset_type\n    block_height\n    entry_function_id_str\n    event_index\n    gas_fee_payer_address\n    is_frozen\n    is_gas_fee\n    is_transaction_success\n    owner_address\n    storage_id\n    storage_refund_amount\n    token_standard\n    transaction_timestamp\n    transaction_version\n    type\n  }\n}\n    `;\nexport const GetFungibleAssetMetadata = `\n    query getFungibleAssetMetadata($where_condition: fungible_asset_metadata_bool_exp, $offset: Int, $limit: Int) {\n  fungible_asset_metadata(where: $where_condition, offset: $offset, limit: $limit) {\n    icon_uri\n    project_uri\n    supply_aggregator_table_handle_v1\n    supply_aggregator_table_key_v1\n    creator_address\n    asset_type\n    decimals\n    last_transaction_timestamp\n    last_transaction_version\n    name\n    symbol\n    token_standard\n    supply_v2\n    maximum_v2\n  }\n}\n    `;\nexport const GetNames = `\n    query getNames($offset: Int, $limit: Int, $where_condition: current_aptos_names_bool_exp, $order_by: [current_aptos_names_order_by!]) {\n  current_aptos_names(\n    limit: $limit\n    where: $where_condition\n    order_by: $order_by\n    offset: $offset\n  ) {\n    ...AnsTokenFragment\n  }\n}\n    ${AnsTokenFragmentFragmentDoc}`;\nexport const GetNumberOfDelegators = `\n    query getNumberOfDelegators($where_condition: num_active_delegator_per_pool_bool_exp, $order_by: [num_active_delegator_per_pool_order_by!]) {\n  num_active_delegator_per_pool(where: $where_condition, order_by: $order_by) {\n    num_active_delegator\n    pool_address\n  }\n}\n    `;\nexport const GetObjectData = `\n    query getObjectData($where_condition: current_objects_bool_exp, $offset: Int, $limit: Int, $order_by: [current_objects_order_by!]) {\n  current_objects(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    allow_ungated_transfer\n    state_key_hash\n    owner_address\n    object_address\n    last_transaction_version\n    last_guid_creation_num\n    is_deleted\n  }\n}\n    `;\nexport const GetProcessorStatus = `\n    query getProcessorStatus($where_condition: processor_status_bool_exp) {\n  processor_status(where: $where_condition) {\n    last_success_version\n    processor\n    last_updated\n  }\n}\n    `;\nexport const GetTableItemsData = `\n    query getTableItemsData($where_condition: table_items_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_items_order_by!]) {\n  table_items(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    decoded_key\n    decoded_value\n    key\n    table_handle\n    transaction_version\n    write_set_change_index\n  }\n}\n    `;\nexport const GetTableItemsMetadata = `\n    query getTableItemsMetadata($where_condition: table_metadatas_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_metadatas_order_by!]) {\n  table_metadatas(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    handle\n    key_type\n    value_type\n  }\n}\n    `;\nexport const GetTokenActivity = `\n    query getTokenActivity($where_condition: token_activities_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [token_activities_v2_order_by!]) {\n  token_activities_v2(\n    where: $where_condition\n    order_by: $order_by\n    offset: $offset\n    limit: $limit\n  ) {\n    ...TokenActivitiesFields\n  }\n}\n    ${TokenActivitiesFieldsFragmentDoc}`;\nexport const GetCurrentTokenOwnership = `\n    query getCurrentTokenOwnership($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    ${CurrentTokenOwnershipFieldsFragmentDoc}`;\nexport const GetTokenData = `\n    query getTokenData($where_condition: current_token_datas_v2_bool_exp, $offset: Int, $limit: Int, $order_by: [current_token_datas_v2_order_by!]) {\n  current_token_datas_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    `;\n\nexport type SdkFunctionWrapper = <T>(\n  action: (requestHeaders?: Record<string, string>) => Promise<T>,\n  operationName: string,\n  operationType?: string,\n  variables?: any,\n) => Promise<T>;\n\nconst defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType, _variables) => action();\n\nexport function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {\n  return {\n    getAccountCoinsCount(\n      variables?: Types.GetAccountCoinsCountQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountCoinsCountQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountCoinsCountQuery>(GetAccountCoinsCount, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountCoinsCount\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountCoinsData(\n      variables: Types.GetAccountCoinsDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountCoinsDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountCoinsDataQuery>(GetAccountCoinsData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountCoinsData\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountCollectionsWithOwnedTokens(\n      variables: Types.GetAccountCollectionsWithOwnedTokensQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountCollectionsWithOwnedTokensQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountCollectionsWithOwnedTokensQuery>(\n            GetAccountCollectionsWithOwnedTokens,\n            variables,\n            { ...requestHeaders, ...wrappedRequestHeaders },\n          ),\n        \"getAccountCollectionsWithOwnedTokens\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountOwnedTokens(\n      variables: Types.GetAccountOwnedTokensQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountOwnedTokensQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountOwnedTokensQuery>(GetAccountOwnedTokens, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountOwnedTokens\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountOwnedTokensByTokenData(\n      variables: Types.GetAccountOwnedTokensByTokenDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountOwnedTokensByTokenDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountOwnedTokensByTokenDataQuery>(GetAccountOwnedTokensByTokenData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountOwnedTokensByTokenData\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountOwnedTokensFromCollection(\n      variables: Types.GetAccountOwnedTokensFromCollectionQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountOwnedTokensFromCollectionQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountOwnedTokensFromCollectionQuery>(\n            GetAccountOwnedTokensFromCollection,\n            variables,\n            { ...requestHeaders, ...wrappedRequestHeaders },\n          ),\n        \"getAccountOwnedTokensFromCollection\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountTokensCount(\n      variables?: Types.GetAccountTokensCountQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountTokensCountQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountTokensCountQuery>(GetAccountTokensCount, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountTokensCount\",\n        \"query\",\n        variables,\n      );\n    },\n    getAccountTransactionsCount(\n      variables?: Types.GetAccountTransactionsCountQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetAccountTransactionsCountQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetAccountTransactionsCountQuery>(GetAccountTransactionsCount, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getAccountTransactionsCount\",\n        \"query\",\n        variables,\n      );\n    },\n    getChainTopUserTransactions(\n      variables?: Types.GetChainTopUserTransactionsQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetChainTopUserTransactionsQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetChainTopUserTransactionsQuery>(GetChainTopUserTransactions, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getChainTopUserTransactions\",\n        \"query\",\n        variables,\n      );\n    },\n    getCollectionData(\n      variables: Types.GetCollectionDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetCollectionDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetCollectionDataQuery>(GetCollectionData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getCollectionData\",\n        \"query\",\n        variables,\n      );\n    },\n    getCurrentFungibleAssetBalances(\n      variables?: Types.GetCurrentFungibleAssetBalancesQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetCurrentFungibleAssetBalancesQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetCurrentFungibleAssetBalancesQuery>(GetCurrentFungibleAssetBalances, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getCurrentFungibleAssetBalances\",\n        \"query\",\n        variables,\n      );\n    },\n    getDelegatedStakingActivities(\n      variables?: Types.GetDelegatedStakingActivitiesQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetDelegatedStakingActivitiesQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetDelegatedStakingActivitiesQuery>(GetDelegatedStakingActivities, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getDelegatedStakingActivities\",\n        \"query\",\n        variables,\n      );\n    },\n    getEvents(\n      variables?: Types.GetEventsQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetEventsQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetEventsQuery>(GetEvents, variables, { ...requestHeaders, ...wrappedRequestHeaders }),\n        \"getEvents\",\n        \"query\",\n        variables,\n      );\n    },\n    getFungibleAssetActivities(\n      variables?: Types.GetFungibleAssetActivitiesQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetFungibleAssetActivitiesQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetFungibleAssetActivitiesQuery>(GetFungibleAssetActivities, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getFungibleAssetActivities\",\n        \"query\",\n        variables,\n      );\n    },\n    getFungibleAssetMetadata(\n      variables?: Types.GetFungibleAssetMetadataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetFungibleAssetMetadataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetFungibleAssetMetadataQuery>(GetFungibleAssetMetadata, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getFungibleAssetMetadata\",\n        \"query\",\n        variables,\n      );\n    },\n    getNames(\n      variables?: Types.GetNamesQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetNamesQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetNamesQuery>(GetNames, variables, { ...requestHeaders, ...wrappedRequestHeaders }),\n        \"getNames\",\n        \"query\",\n        variables,\n      );\n    },\n    getNumberOfDelegators(\n      variables?: Types.GetNumberOfDelegatorsQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetNumberOfDelegatorsQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetNumberOfDelegatorsQuery>(GetNumberOfDelegators, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getNumberOfDelegators\",\n        \"query\",\n        variables,\n      );\n    },\n    getObjectData(\n      variables?: Types.GetObjectDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetObjectDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetObjectDataQuery>(GetObjectData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getObjectData\",\n        \"query\",\n        variables,\n      );\n    },\n    getProcessorStatus(\n      variables?: Types.GetProcessorStatusQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetProcessorStatusQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetProcessorStatusQuery>(GetProcessorStatus, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getProcessorStatus\",\n        \"query\",\n        variables,\n      );\n    },\n    getTableItemsData(\n      variables: Types.GetTableItemsDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetTableItemsDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetTableItemsDataQuery>(GetTableItemsData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getTableItemsData\",\n        \"query\",\n        variables,\n      );\n    },\n    getTableItemsMetadata(\n      variables: Types.GetTableItemsMetadataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetTableItemsMetadataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetTableItemsMetadataQuery>(GetTableItemsMetadata, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getTableItemsMetadata\",\n        \"query\",\n        variables,\n      );\n    },\n    getTokenActivity(\n      variables: Types.GetTokenActivityQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetTokenActivityQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetTokenActivityQuery>(GetTokenActivity, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getTokenActivity\",\n        \"query\",\n        variables,\n      );\n    },\n    getCurrentTokenOwnership(\n      variables: Types.GetCurrentTokenOwnershipQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetCurrentTokenOwnershipQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetCurrentTokenOwnershipQuery>(GetCurrentTokenOwnership, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getCurrentTokenOwnership\",\n        \"query\",\n        variables,\n      );\n    },\n    getTokenData(\n      variables?: Types.GetTokenDataQueryVariables,\n      requestHeaders?: GraphQLClientRequestHeaders,\n    ): Promise<Types.GetTokenDataQuery> {\n      return withWrapper(\n        (wrappedRequestHeaders) =>\n          client.request<Types.GetTokenDataQuery>(GetTokenData, variables, {\n            ...requestHeaders,\n            ...wrappedRequestHeaders,\n          }),\n        \"getTokenData\",\n        \"query\",\n        variables,\n      );\n    },\n  };\n}\nexport type Sdk = ReturnType<typeof getSdk>;\n"], "mappings": "AAIO,IAAMA,EAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBnCC,EAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAa9BC,EAAyC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgDzCC,EAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWvBC,EAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkCtBC,EAAuC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmCvCC,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW/BJ,CAAsC,GAC/BK,EAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW1CL,CAAsC,GAC/BM,EAAsC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW7CN,CAAsC,GAC/BO,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaxBC,EAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS9BC,EAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO9BC,EAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA+BpBC,EAAkC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBlCC,EAAgC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAchCC,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBZC,EAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0B7BC,EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoB3BC,EAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWlBjB,CAA2B,GACpBkB,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQxBC,EAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBhBC,EAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASrBC,EAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBpBC,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcxBC,EAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAW1BxB,CAAgC,GACzByB,EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWlCvB,CAAsC,GAC/BwB,EAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiDtBC,EAAqC,CAACC,EAAQC,EAAgBC,EAAgBC,IAAeH,EAAO,EAEnG,SAASI,EAAOC,EAAuBC,EAAkCP,EAAgB,CAC9F,MAAO,CACL,qBACEQ,EACAC,EAC0C,CAC1C,OAAOF,EACJG,GACCJ,EAAO,QAAyC9B,EAAsBgC,EAAW,CAC/E,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,uBACA,QACAF,CACF,CACF,EACA,oBACEA,EACAC,EACyC,CACzC,OAAOF,EACJG,GACCJ,EAAO,QAAwC7B,EAAqB+B,EAAW,CAC7E,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,sBACA,QACAF,CACF,CACF,EACA,qCACEA,EACAC,EAC0D,CAC1D,OAAOF,EACJG,GACCJ,EAAO,QACL5B,EACA8B,EACA,CAAE,GAAGC,EAAgB,GAAGC,CAAsB,CAChD,EACF,uCACA,QACAF,CACF,CACF,EACA,sBACEA,EACAC,EAC2C,CAC3C,OAAOF,EACJG,GACCJ,EAAO,QAA0C3B,EAAuB6B,EAAW,CACjF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,wBACA,QACAF,CACF,CACF,EACA,iCACEA,EACAC,EACsD,CACtD,OAAOF,EACJG,GACCJ,EAAO,QAAqD1B,EAAkC4B,EAAW,CACvG,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,mCACA,QACAF,CACF,CACF,EACA,oCACEA,EACAC,EACyD,CACzD,OAAOF,EACJG,GACCJ,EAAO,QACLzB,EACA2B,EACA,CAAE,GAAGC,EAAgB,GAAGC,CAAsB,CAChD,EACF,sCACA,QACAF,CACF,CACF,EACA,sBACEA,EACAC,EAC2C,CAC3C,OAAOF,EACJG,GACCJ,EAAO,QAA0CxB,EAAuB0B,EAAW,CACjF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,wBACA,QACAF,CACF,CACF,EACA,4BACEA,EACAC,EACiD,CACjD,OAAOF,EACJG,GACCJ,EAAO,QAAgDvB,EAA6ByB,EAAW,CAC7F,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,8BACA,QACAF,CACF,CACF,EACA,4BACEA,EACAC,EACiD,CACjD,OAAOF,EACJG,GACCJ,EAAO,QAAgDtB,EAA6BwB,EAAW,CAC7F,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,8BACA,QACAF,CACF,CACF,EACA,kBACEA,EACAC,EACuC,CACvC,OAAOF,EACJG,GACCJ,EAAO,QAAsCrB,EAAmBuB,EAAW,CACzE,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,oBACA,QACAF,CACF,CACF,EACA,gCACEA,EACAC,EACqD,CACrD,OAAOF,EACJG,GACCJ,EAAO,QAAoDpB,EAAiCsB,EAAW,CACrG,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,kCACA,QACAF,CACF,CACF,EACA,8BACEA,EACAC,EACmD,CACnD,OAAOF,EACJG,GACCJ,EAAO,QAAkDnB,EAA+BqB,EAAW,CACjG,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,gCACA,QACAF,CACF,CACF,EACA,UACEA,EACAC,EAC+B,CAC/B,OAAOF,EACJG,GACCJ,EAAO,QAA8BlB,EAAWoB,EAAW,CAAE,GAAGC,EAAgB,GAAGC,CAAsB,CAAC,EAC5G,YACA,QACAF,CACF,CACF,EACA,2BACEA,EACAC,EACgD,CAChD,OAAOF,EACJG,GACCJ,EAAO,QAA+CjB,EAA4BmB,EAAW,CAC3F,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,6BACA,QACAF,CACF,CACF,EACA,yBACEA,EACAC,EAC8C,CAC9C,OAAOF,EACJG,GACCJ,EAAO,QAA6ChB,EAA0BkB,EAAW,CACvF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,2BACA,QACAF,CACF,CACF,EACA,SACEA,EACAC,EAC8B,CAC9B,OAAOF,EACJG,GACCJ,EAAO,QAA6Bf,EAAUiB,EAAW,CAAE,GAAGC,EAAgB,GAAGC,CAAsB,CAAC,EAC1G,WACA,QACAF,CACF,CACF,EACA,sBACEA,EACAC,EAC2C,CAC3C,OAAOF,EACJG,GACCJ,EAAO,QAA0Cd,EAAuBgB,EAAW,CACjF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,wBACA,QACAF,CACF,CACF,EACA,cACEA,EACAC,EACmC,CACnC,OAAOF,EACJG,GACCJ,EAAO,QAAkCb,EAAee,EAAW,CACjE,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,gBACA,QACAF,CACF,CACF,EACA,mBACEA,EACAC,EACwC,CACxC,OAAOF,EACJG,GACCJ,EAAO,QAAuCZ,EAAoBc,EAAW,CAC3E,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,qBACA,QACAF,CACF,CACF,EACA,kBACEA,EACAC,EACuC,CACvC,OAAOF,EACJG,GACCJ,EAAO,QAAsCX,EAAmBa,EAAW,CACzE,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,oBACA,QACAF,CACF,CACF,EACA,sBACEA,EACAC,EAC2C,CAC3C,OAAOF,EACJG,GACCJ,EAAO,QAA0CV,EAAuBY,EAAW,CACjF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,wBACA,QACAF,CACF,CACF,EACA,iBACEA,EACAC,EACsC,CACtC,OAAOF,EACJG,GACCJ,EAAO,QAAqCT,EAAkBW,EAAW,CACvE,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,mBACA,QACAF,CACF,CACF,EACA,yBACEA,EACAC,EAC8C,CAC9C,OAAOF,EACJG,GACCJ,EAAO,QAA6CR,EAA0BU,EAAW,CACvF,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,2BACA,QACAF,CACF,CACF,EACA,aACEA,EACAC,EACkC,CAClC,OAAOF,EACJG,GACCJ,EAAO,QAAiCP,EAAcS,EAAW,CAC/D,GAAGC,EACH,GAAGC,CACL,CAAC,EACH,eACA,QACAF,CACF,CACF,CACF,CACF", "names": ["TokenActivitiesFieldsFragmentDoc", "AnsTokenFragmentFragmentDoc", "CurrentTokenOwnershipFieldsFragmentDoc", "GetAccountCoinsCount", "GetAccountCoinsData", "GetAccountCollectionsWithOwnedTokens", "GetAccountOwnedTokens", "GetAccountOwnedTokensByTokenData", "GetAccountOwnedTokensFromCollection", "GetAccountTokensCount", "GetAccountTransactionsCount", "GetChainTopUserTransactions", "GetCollectionData", "GetCurrentFungibleAssetBalances", "GetDelegatedStakingActivities", "GetEvents", "GetFungibleAssetActivities", "GetFungibleAssetMetadata", "GetNames", "GetNumberOfDelegators", "GetObjectData", "GetProcessorStatus", "GetTableItemsData", "GetTableItemsMetadata", "GetTokenActivity", "GetCurrentTokenOwnership", "GetTokenData", "defaultWrapper", "action", "_operationName", "_operationType", "_variables", "getSdk", "client", "with<PERSON><PERSON><PERSON>", "variables", "requestHeaders", "wrappedRequestHeaders"]}