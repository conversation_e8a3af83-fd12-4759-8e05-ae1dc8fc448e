"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const MsgBase_js_1 = require("../../MsgBase.js");
/**
 * @category Messages
 */
class MsgDeposit extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgDeposit(params);
    }
    toProto() {
        const { params } = this;
        const deposit = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        deposit.denom = params.amount.denom;
        deposit.amount = params.amount.amount;
        const message = core_proto_ts_1.CosmosGovV1Tx.MsgDeposit.create();
        message.proposalId = params.proposalId.toString();
        message.depositor = params.depositor;
        message.amount = [deposit];
        return core_proto_ts_1.CosmosGovV1Tx.MsgDeposit.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.gov.v1beta1.MsgDeposit',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgDeposit',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.gov.v1beta1.MsgDeposit',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.gov.v1beta1.MsgDeposit',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosGovV1Tx.MsgDeposit.encode(this.toProto()).finish();
    }
}
exports.default = MsgDeposit;
