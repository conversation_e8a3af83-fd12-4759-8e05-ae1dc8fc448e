{"version": 3, "file": "methods.js", "sourceRoot": "", "sources": ["../../../../src/program/namespace/methods.ts"], "names": [], "mappings": ";;;AAWA,kEAIiC;AACjC,4CAAyD;AAoBzD,MAAa,qBAAqB;IACzB,MAAM,CAAC,KAAK,CACjB,QAAkB,EAClB,SAAoB,EACpB,KAA2B,EAC3B,IAAwB,EACxB,IAAwB,EACxB,KAAiB,EACjB,UAA2B,EAC3B,MAA+B,EAC/B,gBAAuC,EACvC,QAAsB,EACtB,cAA2C;QAE3C,OAAO,CAAC,GAAG,IAAI,EAAE,EAAE,CACjB,IAAI,cAAc,CAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,UAAU,EACV,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,gBAAgB,EAChB,QAAQ,EACR,cAAc,CACf,CAAC;IACN,CAAC;CACF;AA9BD,sDA8BC;AAaD,SAAgB,iBAAiB,CAC/B,cAA8C;IAE9C,OAAO,CACL,OAAO,cAAc,KAAK,QAAQ;QAClC,cAAc,KAAK,IAAI;QACvB,CAAC,CAAC,KAAK,IAAI,cAAc,CAAC,CAAC,uBAAuB;KACnD,CAAC;AACJ,CAAC;AARD,8CAQC;AAED,SAAgB,sBAAsB,CACpC,eAAmC,EACnC,WAAoB;IAEpB,MAAM,QAAQ,GAAoB,EAAE,CAAC;IACrC,KAAK,MAAM,WAAW,IAAI,eAAe,EAAE;QACzC,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,IAAI,WAAW;gBACb,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;YACJ,SAAS;SACV;QACD,QAAQ,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC,OAAO,CAAC;YAChD,CAAC,CAAC,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC;YACvC,CAAC,CAAC,IAAA,4BAAgB,EAAC,OAAO,CAAC,CAAC;KAC/B;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAnBD,wDAmBC;AAED,MAAa,cAAc;IAUzB,YACE,KAAiB,EACT,KAAyB,EACzB,KAAyB,EACzB,MAAkB,EAClB,WAA4B,EAC5B,OAAgC,EACxC,SAAmB,EACX,UAAqB,EAC7B,MAA4B,EAC5B,iBAAwC,EACxC,SAAuB,EACvB,eAA4C;QAVpC,UAAK,GAAL,KAAK,CAAoB;QACzB,UAAK,GAAL,KAAK,CAAoB;QACzB,WAAM,GAAN,MAAM,CAAY;QAClB,gBAAW,GAAX,WAAW,CAAiB;QAC5B,YAAO,GAAP,OAAO,CAAyB;QAEhC,eAAU,GAAV,UAAU,CAAW;QAjBd,cAAS,GAAoB,EAAE,CAAC;QACzC,uBAAkB,GAAuB,EAAE,CAAC;QAC5C,aAAQ,GAAkB,EAAE,CAAC;QAC7B,qBAAgB,GAAkC,EAAE,CAAC;QACrD,sBAAiB,GAAkC,EAAE,CAAC;QAEtD,yBAAoB,GAAY,IAAI,CAAC;QAiB3C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,uCAAgB,CAC3C,KAAK,EACL,IAAI,CAAC,SAAS,EACd,SAAS,EACT,UAAU,EACV,MAAM,EACN,iBAAiB,EACjB,SAAS,EACT,eAAe,CAChB,CAAC;IACJ,CAAC;IAEM,IAAI,CAAC,KAAiB;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,OAAO;QAGlB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,SAEX,CAAC;IACJ,CAAC;IAEM,QAAQ,CACb,QAAgD;QAEhD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,cAAc,CACnB,QAAyC;QAEzC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,OAAO,CAAC,OAAsB;QACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,iBAAiB,CACtB,QAA4B;QAE5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,eAAe,CACpB,GAAkC;QAElC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,gBAAgB,CACrB,GAAkC;QAElC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,GAAG,CAAC,OAAwB;QACvC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QAED,aAAa;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAChC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAwB;QAI9C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACrC,OAAO;YACL,OAAO;YACP,SAAS,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;SACnC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,OAAwB;QACxC,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,aAAa;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,QAAQ,CACnB,OAAwB;QAExB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QAED,aAAa;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QAED,aAAa;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,OAAO;QAKlB,OAAO;YACL,WAAW,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;YACrC,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;YAC7B,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ;SAC7B,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;SACxC;QAED,aAAa;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;YAC/B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;SACzC,CAAC,CAAC;IACL,CAAC;CACF;AA7MD,wCA6MC"}