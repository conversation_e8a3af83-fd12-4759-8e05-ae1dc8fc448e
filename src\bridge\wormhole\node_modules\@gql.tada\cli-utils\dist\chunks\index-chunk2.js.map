{"version": 3, "file": "index-chunk2.js", "sources": ["../../src/threads/index.ts"], "sourcesContent": ["import type { WorkerOptions } from 'node:worker_threads';\nimport { Worker, isMainThread, parentPort, SHARE_ENV } from 'node:worker_threads';\n\nconst port = parentPort!;\nif (!isMainThread && !port) {\n  throw new ReferenceError('Failed to receive parent message port');\n}\n\nconst enum MainMessageCodes {\n  Start = 'START',\n  Close = 'CLOSE',\n  Pull = 'PULL',\n}\n\ninterface MainMessage {\n  id?: number;\n  kind: MainMessageCodes;\n  data?: any;\n}\n\nconst enum ThreadMessageCodes {\n  Next = 'NEXT',\n  Throw = 'THROW',\n  Return = 'RETURN',\n}\n\ninterface ThreadMessage {\n  id?: number;\n  kind: ThreadMessageCodes;\n  data?: any;\n  extra?: Record<string, unknown>;\n}\n\nconst workerOpts: WorkerOptions = {\n  env: SHARE_ENV,\n  stderr: false,\n  stdout: false,\n  stdin: false,\n};\n\nconst getMessageData = (message: ThreadMessage) => {\n  const data = message.data;\n  if (message.kind === ThreadMessageCodes.Throw) {\n    return typeof data === 'object' && data && message.extra != null\n      ? Object.assign(data, message.extra)\n      : data;\n  } else {\n    return data;\n  }\n};\n\nconst asyncIteratorSymbol = (): typeof Symbol.asyncIterator =>\n  (typeof Symbol === 'function' && Symbol.asyncIterator) || ('@@asyncIterator' as any);\n\n/** Capture the stack above the caller */\nfunction captureStack(): NodeJS.CallSite[] {\n  const _error: any = new Error();\n  const _prepareStackTrace = Error.prepareStackTrace;\n  try {\n    let stack: NodeJS.CallSite[] | undefined;\n    Error.prepareStackTrace = (_error, _stack) => (stack = _stack);\n    Error.captureStackTrace(_error);\n    if (!_error.stack) throw _error;\n    return (stack && stack.slice(2)) || [];\n  } finally {\n    Error.prepareStackTrace = _prepareStackTrace;\n  }\n}\n\nexport interface Generator<Args extends readonly any[], Next> {\n  // TODO: Update to support for AsyncGenerator interface\n  (...args: Args): AsyncIterableIterator<Next>;\n}\n\nfunction main<Args extends readonly any[], Next>(url: string | URL): Generator<Args, Next> {\n  let worker: Worker;\n  let ids = 0;\n  return (...args: Args) => {\n    if (!worker) {\n      worker = new Worker(url, workerOpts);\n      worker.unref();\n    }\n\n    const id = ++ids | 0;\n    const buffer: ThreadMessage[] = [];\n\n    let started = false;\n    let ended = false;\n    let pulled = false;\n    let resolve: ((value: IteratorResult<Next>) => void) | void;\n    let reject: ((error: any) => void) | void;\n\n    function cleanup() {\n      ended = true;\n      resolve = undefined;\n      reject = undefined;\n      worker.removeListener('message', receiveMessage);\n      worker.removeListener('error', receiveError);\n    }\n\n    function sendMessage(kind: MainMessageCodes) {\n      worker.postMessage({ id, kind });\n    }\n\n    function receiveError(error: any) {\n      cleanup();\n      buffer.length = 1;\n      buffer[0] = {\n        id,\n        kind: ThreadMessageCodes.Throw,\n        data: error,\n      };\n    }\n\n    function receiveMessage(data: unknown) {\n      const message: ThreadMessage | null =\n        data && typeof data === 'object' && 'kind' in data ? (data as ThreadMessage) : null;\n      if (!message) {\n        return;\n      } else if (reject && message.kind === ThreadMessageCodes.Throw) {\n        reject(getMessageData(message));\n        cleanup();\n      } else if (resolve && message.kind === ThreadMessageCodes.Return) {\n        resolve({ done: true, value: getMessageData(message) });\n        cleanup();\n      } else if (resolve && message.kind === ThreadMessageCodes.Next) {\n        pulled = false;\n        resolve({ done: false, value: getMessageData(message) });\n      } else if (\n        message.kind === ThreadMessageCodes.Throw ||\n        message.kind === ThreadMessageCodes.Return\n      ) {\n        buffer.push(message);\n        cleanup();\n      } else if (message.kind === ThreadMessageCodes.Next) {\n        buffer.push(message);\n        pulled = false;\n      }\n    }\n\n    return {\n      async next() {\n        if (!started) {\n          started = true;\n          worker.addListener('message', receiveMessage);\n          worker.addListener('error', receiveError);\n          worker.postMessage({\n            id,\n            kind: MainMessageCodes.Start,\n            data: args,\n          });\n        }\n        if (ended && !buffer.length) {\n          return { done: true } as IteratorReturnResult<any>;\n        } else if (!ended && !pulled && buffer.length <= 1) {\n          pulled = true;\n          sendMessage(MainMessageCodes.Pull);\n        }\n        const message = buffer.shift();\n        if (message && message.kind === ThreadMessageCodes.Throw) {\n          cleanup();\n          throw getMessageData(message);\n        } else if (message && message.kind === ThreadMessageCodes.Return) {\n          cleanup();\n          return { value: getMessageData(message), done: true };\n        } else if (message && message.kind === ThreadMessageCodes.Next) {\n          return { value: getMessageData(message), done: false };\n        } else {\n          return new Promise((_resolve, _reject) => {\n            resolve = (value) => {\n              resolve = undefined;\n              reject = undefined;\n              _resolve(value);\n            };\n            reject = (error) => {\n              resolve = undefined;\n              reject = undefined;\n              _reject(error);\n            };\n          });\n        }\n      },\n      async return() {\n        if (!ended) {\n          cleanup();\n          sendMessage(MainMessageCodes.Close);\n        }\n        return { done: true } as IteratorReturnResult<any>;\n      },\n      [asyncIteratorSymbol()]() {\n        return this;\n      },\n    };\n  };\n}\n\nfunction thread<Args extends readonly any[], Next>(\n  message: MainMessage,\n  generator: Generator<Args, Next>\n): void {\n  if (message.kind !== MainMessageCodes.Start) return;\n  const id = message.id;\n  const iterator = generator(...(message.data as any));\n\n  let ended = false;\n  let pulled = false;\n  let looping = false;\n\n  function cleanup() {\n    ended = true;\n    port.removeListener('message', receiveMessage);\n  }\n\n  async function sendMessage(kind: ThreadMessageCodes, data?: any) {\n    try {\n      const message: ThreadMessage = { id, kind, data };\n      // NOTE: Copy error annotations to separate property\n      if (kind === ThreadMessageCodes.Throw && typeof data === 'object' && data != null)\n        message.extra = { ...data };\n      port.postMessage(message);\n    } catch (error) {\n      cleanup();\n      if (iterator.throw) {\n        let result = await iterator.throw();\n        if (result.done === false && iterator.return) {\n          result = await iterator.return();\n          sendMessage(ThreadMessageCodes.Return, result.value);\n        } else {\n          sendMessage(ThreadMessageCodes.Return, result.value);\n        }\n      } else {\n        sendMessage(ThreadMessageCodes.Return);\n      }\n    }\n  }\n\n  async function receiveMessage(data: unknown) {\n    const message: MainMessage | null =\n      data && typeof data === 'object' && 'kind' in data ? (data as MainMessage) : null;\n    let next: IteratorResult<Next>;\n    if (!message) {\n      return;\n    } else if (message.kind === MainMessageCodes.Close) {\n      cleanup();\n      if (iterator.return) iterator.return();\n    } else if (message.kind === MainMessageCodes.Pull && looping) {\n      pulled = true;\n    } else if (message.kind === MainMessageCodes.Pull) {\n      for (pulled = looping = true; pulled && !ended; ) {\n        try {\n          if ((next = await iterator.next()).done) {\n            cleanup();\n            if (iterator.return) next = await iterator.return();\n            sendMessage(ThreadMessageCodes.Return, next.value);\n          } else {\n            pulled = false;\n            sendMessage(ThreadMessageCodes.Next, next.value);\n          }\n        } catch (error) {\n          cleanup();\n          sendMessage(ThreadMessageCodes.Throw, error);\n        }\n      }\n      looping = false;\n    }\n  }\n\n  port.addListener('message', receiveMessage);\n}\n\nexport function expose<Args extends readonly any[], Return>(\n  generator: Generator<Args, Return>\n): Generator<Args, Return> {\n  if (isMainThread) {\n    const call = captureStack()[0];\n    const file = call && call.getFileName();\n    if (!file) throw new ReferenceError('Captured stack trace is empty');\n    const url = file.startsWith('file://') ? new URL(file) : file;\n    return main(url);\n  } else {\n    port.addListener('message', (data) => {\n      const message: MainMessage | null =\n        data && typeof data === 'object' && 'kind' in data ? (data as MainMessage) : null;\n      if (message) thread(message, generator);\n    });\n    return generator;\n  }\n}\n"], "names": ["port", "parentPort", "isMainThread", "ReferenceError", "MainMessageCodes", "ThreadMessageCodes", "workerOpts", "env", "SHARE_ENV", "stderr", "stdout", "stdin", "getMessageData", "message", "data", "kind", "<PERSON>hrow", "extra", "Object", "assign", "asyncIteratorSymbol", "Symbol", "asyncIterator", "expose", "generator", "call", "captureStack", "_error", "Error", "_prepareStackTrace", "prepareStackTrace", "stack", "_stack", "captureStackTrace", "slice", "file", "getFileName", "main", "url", "worker", "ids", "args", "Worker", "unref", "id", "buffer", "started", "ended", "pulled", "resolve", "reject", "cleanup", "undefined", "removeListener", "receiveMessage", "receiveError", "sendMessage", "postMessage", "error", "length", "Return", "done", "value", "Next", "push", "next", "addListener", "Start", "<PERSON><PERSON>", "shift", "Promise", "_resolve", "_reject", "return", "Close", "this", "startsWith", "URL", "thread", "iterator", "looping", "async", "throw", "result"], "mappings": ";;AAGA,IAAMA,IAAOC,EAAAA;;AACb,KAAKC,EAAAA,iBAAiBF;EACpB,MAAM,IAAIG,eAAe;;;AAC1B,IAEUC,aAAAA;EAAAA,EAAgB,QAAA;EAAhBA,EAAgB,QAAA;EAAhBA,EAAgB,OAAA;EAAA,OAAhBA;AAAgB,EAAhBA,KAAgB,CAAA;;AAAA,IAYhBC,aAAAA;EAAAA,EAAkB,OAAA;EAAlBA,EAAkB,QAAA;EAAlBA,EAAkB,SAAA;EAAA,OAAlBA;AAAkB,EAAlBA,KAAkB,CAAA;;AAa7B,IAAMC,IAA4B;EAChCC,KAAKC,EAASA;EACdC,SAAQ;EACRC,SAAQ;EACRC,QAAO;;;AAGT,IAAMC,iBAAkBC;EACtB,IAAMC,IAAOD,EAAQC;EACrB,IAAID,EAAQE,SAASV,EAAmBW;IACtC,OAAuB,mBAATF,KAAqBA,KAAyB,QAAjBD,EAAQI,QAC/CC,OAAOC,OAAOL,GAAMD,EAAQI,SAC5BH;;IAEJ,OAAOA;;AACT;;AAGF,IAAMM,sBAAsBA,MACP,qBAAXC,UAAyBA,OAAOC,iBAAmB;;iBA0NtD,SAASC,OACdC;EAEA,IAAItB,gBAAc;IAChB,IAAMuB,IA3NV,SAASC;MACP,IAAMC,IAAc,IAAIC;MACxB,IAAMC,IAAqBD,MAAME;MACjC;QACE,IAAIC;QACJH,MAAME,oBAAoB,CAACH,GAAQK,MAAYD,IAAQC;QACvDJ,MAAMK,kBAAkBN;QACxB,KAAKA,EAAOI;UAAO,MAAMJ;;QACzB,OAAQI,KAASA,EAAMG,MAAM,MAAO;AACtC,QAAU;QACRN,MAAME,oBAAoBD;AAC5B;AACF,KA+MiBH,GAAe;IAC5B,IAAMS,IAAOV,KAAQA,EAAKW;IAC1B,KAAKD;MAAM,MAAM,IAAIhC,eAAe;;IAEpC,OA5MJ,SAASkC,KAAwCC;MAC/C,IAAIC;MACJ,IAAIC,IAAM;MACV,OAAO,IAAIC;QACT,KAAKF;WACHA,IAAS,IAAIG,EAAAA,OAAOJ,GAAKhC,IAClBqC;;QAGT,IAAMC,IAAa,MAANJ;QACb,IAAMK,IAA0B;QAEhC,IAAIC,KAAU;QACd,IAAIC,KAAQ;QACZ,IAAIC,KAAS;QACb,IAAIC;QACJ,IAAIC;QAEJ,SAASC;UACPJ,KAAQ;UACRE,SAAUG;UACVF,SAASE;UACTb,EAAOc,eAAe,WAAWC;UACjCf,EAAOc,eAAe,SAASE;AACjC;QAEA,SAASC,YAAYzC;UACnBwB,EAAOkB,YAAY;YAAEb;YAAI7B;;AAC3B;QAEA,SAASwC,aAAaG;UACpBP;UACAN,EAAOc,SAAS;UAChBd,EAAO,KAAK;YACVD;YACA7B,MAAMV,EAAmBW;YACzBF,MAAM4C;;AAEV;QAEA,SAASJ,eAAexC;UACtB,IAAMD,IACJC,KAAwB,mBAATA,KAAqB,UAAUA,IAAQA,IAAyB;UACjF,KAAKD;YACH;iBACK,IAAIqC,KAAUrC,EAAQE,SAASV,EAAmBW,OAAO;YAC9DkC,EAAOtC,eAAeC;YACtBsC;AACD,iBAAM,IAAIF,KAAWpC,EAAQE,SAASV,EAAmBuD,QAAQ;YAChEX,EAAQ;cAAEY,OAAM;cAAMC,OAAOlD,eAAeC;;YAC5CsC;AACD,iBAAM,IAAIF,KAAWpC,EAAQE,SAASV,EAAmB0D,MAAM;YAC9Df,KAAS;YACTC,EAAQ;cAAEY,OAAM;cAAOC,OAAOlD,eAAeC;;AAC/C,iBAAO,IACLA,EAAQE,SAASV,EAAmBW,SACpCH,EAAQE,SAASV,EAAmBuD,QACpC;YACAf,EAAOmB,KAAKnD;YACZsC;AACD,iBAAM,IAAItC,EAAQE,SAASV,EAAmB0D,MAAM;YACnDlB,EAAOmB,KAAKnD;YACZmC,KAAS;AACX;AACF;QAEA,OAAO;UACL,UAAMiB;YACJ,KAAKnB,GAAS;cACZA,KAAU;cACVP,EAAO2B,YAAY,WAAWZ;cAC9Bf,EAAO2B,YAAY,SAASX;cAC5BhB,EAAOkB,YAAY;gBACjBb;gBACA7B,MAAMX,EAAiB+D;gBACvBrD,MAAM2B;;AAEV;YACA,IAAIM,MAAUF,EAAOc;cACnB,OAAO;gBAAEE,OAAM;;mBACV,KAAKd,MAAUC,KAAUH,EAAOc,UAAU,GAAG;cAClDX,KAAS;cACTQ,YAAYpD,EAAiBgE;AAC/B;YACA,IAAMvD,IAAUgC,EAAOwB;YACvB,IAAIxD,KAAWA,EAAQE,SAASV,EAAmBW,OAAO;cACxDmC;cACA,MAAMvC,eAAeC;AACtB,mBAAM,IAAIA,KAAWA,EAAQE,SAASV,EAAmBuD,QAAQ;cAChET;cACA,OAAO;gBAAEW,OAAOlD,eAAeC;gBAAUgD,OAAM;;AAChD,mBAAM,IAAIhD,KAAWA,EAAQE,SAASV,EAAmB0D;cACxD,OAAO;gBAAED,OAAOlD,eAAeC;gBAAUgD,OAAM;;;cAE/C,OAAO,IAAIS,SAAQ,CAACC,GAAUC;gBAC5BvB,IAAWa;kBACTb,SAAUG;kBACVF,SAASE;kBACTmB,EAAST;AAAM;gBAEjBZ,IAAUQ;kBACRT,SAAUG;kBACVF,SAASE;kBACToB,EAAQd;AAAM;AACf;;AAGN;UACD,YAAMe;YACJ,KAAK1B,GAAO;cACVI;cACAK,YAAYpD,EAAiBsE;AAC/B;YACA,OAAO;cAAEb,OAAM;;AAChB;UACD,CAACzC;YACC,OAAOuD;AACT;;AACD;AAEL,KAoFWtC,CADKF,EAAKyC,WAAW,aAAa,IAAIC,IAAI1C,KAAQA;AAE3D,SAAO;IACLnC,EAAKkE,YAAY,YAAYpD;MAC3B,IAAMD,IACJC,KAAwB,mBAATA,KAAqB,UAAUA,IAAQA,IAAuB;MAC/E,IAAID;SAvFV,SAASiE,OACPjE,GACAW;UAEA,IAAIX,EAAQE,SAASX,EAAiB+D;YAAO;;UAC7C,IAAMvB,IAAK/B,EAAQ+B;UACnB,IAAMmC,IAAWvD,KAAcX,EAAQC;UAEvC,IAAIiC,KAAQ;UACZ,IAAIC,KAAS;UACb,IAAIgC,KAAU;UAEd,SAAS7B;YACPJ,KAAQ;YACR/C,EAAKqD,eAAe,WAAWC;AACjC;UAEA2B,eAAezB,YAAYzC,GAA0BD;YACnD;cACE,IAAMD,IAAyB;gBAAE+B;gBAAI7B;gBAAMD;;cAE3C,IAAIC,MAASV,EAAmBW,SAAyB,mBAATF,KAA6B,QAARA;gBACnED,EAAQI,QAAQ;qBAAKH;;;cACvBd,EAAKyD,YAAY5C;AAClB,cAAC,OAAO6C;cACPP;cACA,IAAI4B,EAASG,OAAO;gBAClB,IAAIC,UAAeJ,EAASG;gBAC5B,KAAoB,MAAhBC,EAAOtB,QAAkBkB,EAASN,QAAQ;kBAC5CU,UAAeJ,EAASN;kBACxBjB,YAAYnD,EAAmBuD,QAAQuB,EAAOrB;AAChD;kBACEN,YAAYnD,EAAmBuD,QAAQuB,EAAOrB;;AAElD;gBACEN,YAAYnD,EAAmBuD;;AAEnC;AACF;UAEAqB,eAAe3B,eAAexC;YAC5B,IAAMD,IACJC,KAAwB,mBAATA,KAAqB,UAAUA,IAAQA,IAAuB;YAC/E,IAAImD;YACJ,KAAKpD;cACH;mBACK,IAAIA,EAAQE,SAASX,EAAiBsE,OAAO;cAClDvB;cACA,IAAI4B,EAASN;gBAAQM,EAASN;;AAC/B,mBAAM,IAAI5D,EAAQE,SAASX,EAAiBgE,QAAQY;cACnDhC,KAAS;mBACJ,IAAInC,EAAQE,SAASX,EAAiBgE,MAAM;cACjD,KAAKpB,IAASgC,KAAU,GAAMhC,MAAWD;gBACvC;kBACE,KAAKkB,UAAac,EAASd,QAAQJ,MAAM;oBACvCV;oBACA,IAAI4B,EAASN;sBAAQR,UAAac,EAASN;;oBAC3CjB,YAAYnD,EAAmBuD,QAAQK,EAAKH;AAC9C,yBAAO;oBACLd,KAAS;oBACTQ,YAAYnD,EAAmB0D,MAAME,EAAKH;AAC5C;AACD,kBAAC,OAAOJ;kBACPP;kBACAK,YAAYnD,EAAmBW,OAAO0C;AACxC;;cAEFsB,KAAU;AACZ;AACF;UAEAhF,EAAKkE,YAAY,WAAWZ;AAC9B,SAemBwB,CAAOjE,GAASW;;AAAU;IAEzC,OAAOA;AACT;AACF"}