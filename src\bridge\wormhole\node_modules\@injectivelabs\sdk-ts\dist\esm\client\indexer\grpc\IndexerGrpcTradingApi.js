import { UnspecifiedErrorCode, grpcErrorCodeToErrorCode, GrpcUnaryRequestException, } from '@injectivelabs/exceptions';
import { InjectiveTradingRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { IndexerModule } from '../types/index.js';
/**
 * @category Indexer Grpc API
 */
export class IndexerGrpcTradingApi extends BaseGrpcConsumer {
    module = IndexerModule.Trading;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new InjectiveTradingRpc.InjectiveTradingRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchTradingStats() {
        const request = InjectiveTradingRpc.GetTradingStatsRequest.create();
        try {
            const response = await this.retry(() => this.client.GetTradingStats(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof InjectiveTradingRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'TradingStats',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'TradingStats',
                contextModule: this.module,
            });
        }
    }
    async fetchGridStrategies({ skip, state, limit, withTvl, endTime, marketId, startTime, marketType, strategyType, subaccountId, accountAddress, withPerformance, pendingExecution, lastExecutedTime, isTrailingStrategy, }) {
        const request = InjectiveTradingRpc.ListTradingStrategiesRequest.create();
        if (accountAddress) {
            request.accountAddress = accountAddress;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (strategyType) {
            request.strategyType = strategyType;
        }
        if (marketType) {
            request.marketType = marketType;
        }
        if (state) {
            request.state = state;
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (withTvl) {
            request.withTvl = withTvl;
        }
        if (withPerformance) {
            request.withPerformance = withPerformance;
        }
        if (isTrailingStrategy) {
            request.isTrailingStrategy = isTrailingStrategy;
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (pendingExecution) {
            request.pendingExecution = pendingExecution;
        }
        if (lastExecutedTime) {
            request.lastExecutedTime = lastExecutedTime.toString();
        }
        try {
            const response = await this.retry(() => this.client.ListTradingStrategies(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof InjectiveTradingRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'GridStrategies',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'GridStrategies',
                contextModule: this.module,
            });
        }
    }
}
