import { grpc } from "@injectivelabs/grpc-web";
import { GetBlockResultsRequest, GetBlockResultsResponse } from "./block_results";
export declare const protobufPackage = "cometbft.services.block_results.v2";
/** BlockResultService provides the block results of a given or latestheight. */
export interface BlockResultsService {
    /** GetBlockResults returns the BlockResults of the requested height. */
    GetBlockResults(request: DeepPartial<GetBlockResultsRequest>, metadata?: grpc.Metadata): Promise<GetBlockResultsResponse>;
}
export declare class BlockResultsServiceClientImpl implements BlockResultsService {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetBlockResults(request: DeepPartial<GetBlockResultsRequest>, metadata?: grpc.Metadata): Promise<GetBlockResultsResponse>;
}
export declare const BlockResultsServiceDesc: {
    serviceName: string;
};
export declare const BlockResultsServiceGetBlockResultsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
