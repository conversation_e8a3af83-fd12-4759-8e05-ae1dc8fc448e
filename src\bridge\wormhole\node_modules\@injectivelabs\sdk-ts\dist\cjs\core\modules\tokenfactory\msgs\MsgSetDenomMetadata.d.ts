import { MsgBase } from '../../MsgBase.js';
import { CosmosBankV1Beta1Bank, InjectiveTokenFactoryV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgSetDenomMetadata {
    interface Params {
        sender: string;
        metadata: CosmosBankV1Beta1Bank.Metadata;
        adminBurnDisabled?: boolean;
    }
    type Proto = InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata;
}
/**
 * @category Messages
 */
export default class MsgSetDenomMetadata extends MsgBase<MsgSetDenomMetadata.Params, MsgSetDenomMetadata.Proto> {
    static fromJSON(params: MsgSetDenomMetadata.Params): MsgSetDenomMetadata;
    toProto(): InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata;
    toData(): {
        sender: string;
        metadata: CosmosBankV1Beta1Bank.Metadata | undefined;
        adminBurnDisabled: InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata_AdminBurnDisabled | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            metadata: CosmosBankV1Beta1Bank.Metadata | undefined;
            admin_burn_disabled: InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata_AdminBurnDisabled | undefined;
        };
    };
    toWeb3Gw(): {
        sender: string;
        metadata: CosmosBankV1Beta1Bank.Metadata | undefined;
        admin_burn_disabled: InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata_AdminBurnDisabled | undefined;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata;
    };
    toBinary(): Uint8Array;
}
