import { EncodeObject, OfflineSigner, Registry } from "@cosmjs/proto-signing";
import { AminoTypes, Attribute, Coin, DeliverTxResponse, Event, GasPrice, logs, SignerData, StdFee } from "@cosmjs/stargate";
import { CometClient, HttpEndpoint } from "@cosmjs/tendermint-rpc";
import { TxRaw } from "cosmjs-types/cosmos/tx/v1beta1/tx";
import { AccessConfig } from "cosmjs-types/cosmwasm/wasm/v1/types";
import { CosmWasmClient } from "./cosmwasmclient";
import { JsonObject } from "./modules";
export interface UploadResult {
    /** A hex encoded sha256 checksum of the original Wasm code (that is stored on chain) */
    readonly checksum: string;
    /** Size of the original wasm code in bytes */
    readonly originalSize: number;
    /** Size of the compressed wasm code in bytes */
    readonly compressedSize: number;
    /** The ID of the code asigned by the chain */
    readonly codeId: number;
    /** @deprecated Not filled in Cosmos SDK >= 0.50. Use events instead. */
    readonly logs: readonly logs.Log[];
    /** Block height in which the transaction is included */
    readonly height: number;
    /** Transaction hash (might be used as transaction ID). Guaranteed to be non-empty upper-case hex */
    readonly transactionHash: string;
    readonly events: readonly Event[];
    readonly gasWanted: bigint;
    readonly gasUsed: bigint;
}
/**
 * The options of .instantiate() and .instantiate2() call.
 * All properties are optional.
 */
export interface InstantiateOptions {
    readonly memo?: string;
    /**
     * The funds that are transferred from the sender to the newly created contract.
     * The funds are transferred as part of the message execution after the contract address is
     * created and before the instantiation message is executed by the contract.
     *
     * Only native tokens are supported.
     */
    readonly funds?: readonly Coin[];
    /**
     * A bech32 encoded address of an admin account.
     * Caution: an admin has the privilege to upgrade a contract. If this is not desired, do not set this value.
     */
    readonly admin?: string;
}
export interface InstantiateResult {
    /** The address of the newly instantiated contract */
    readonly contractAddress: string;
    /** @deprecated Not filled in Cosmos SDK >= 0.50. Use events instead. */
    readonly logs: readonly logs.Log[];
    /** Block height in which the transaction is included */
    readonly height: number;
    /** Transaction hash (might be used as transaction ID). Guaranteed to be non-empty upper-case hex */
    readonly transactionHash: string;
    readonly events: readonly Event[];
    readonly gasWanted: bigint;
    readonly gasUsed: bigint;
}
/**
 * Result type of updateAdmin and clearAdmin
 */
export interface ChangeAdminResult {
    /** @deprecated Not filled in Cosmos SDK >= 0.50. Use events instead. */
    readonly logs: readonly logs.Log[];
    /** Block height in which the transaction is included */
    readonly height: number;
    /** Transaction hash (might be used as transaction ID). Guaranteed to be non-empty upper-case hex */
    readonly transactionHash: string;
    readonly events: readonly Event[];
    readonly gasWanted: bigint;
    readonly gasUsed: bigint;
}
export interface MigrateResult {
    /** @deprecated Not filled in Cosmos SDK >= 0.50. Use events instead. */
    readonly logs: readonly logs.Log[];
    /** Block height in which the transaction is included */
    readonly height: number;
    /** Transaction hash (might be used as transaction ID). Guaranteed to be non-empty upper-case hex */
    readonly transactionHash: string;
    readonly events: readonly Event[];
    readonly gasWanted: bigint;
    readonly gasUsed: bigint;
}
export interface ExecuteInstruction {
    contractAddress: string;
    msg: JsonObject;
    funds?: readonly Coin[];
}
export interface ExecuteResult {
    /** @deprecated Not filled in Cosmos SDK >= 0.50. Use events instead. */
    readonly logs: readonly logs.Log[];
    /** Block height in which the transaction is included */
    readonly height: number;
    /** Transaction hash (might be used as transaction ID). Guaranteed to be non-empty upper-case hex */
    readonly transactionHash: string;
    readonly events: readonly Event[];
    readonly gasWanted: bigint;
    readonly gasUsed: bigint;
}
/**
 * Searches in events for an event of the given event type which contains an
 * attribute for with the given key.
 *
 * Throws if the attribute was not found.
 */
export declare function findAttribute(events: readonly Event[], eventType: string, attrKey: string): Attribute;
export interface SigningCosmWasmClientOptions {
    readonly registry?: Registry;
    readonly aminoTypes?: AminoTypes;
    readonly broadcastTimeoutMs?: number;
    readonly broadcastPollIntervalMs?: number;
    readonly gasPrice?: GasPrice;
}
export declare class SigningCosmWasmClient extends CosmWasmClient {
    readonly registry: Registry;
    readonly broadcastTimeoutMs: number | undefined;
    readonly broadcastPollIntervalMs: number | undefined;
    private readonly signer;
    private readonly aminoTypes;
    private readonly gasPrice;
    private readonly defaultGasMultiplier;
    /**
     * Creates an instance by connecting to the given CometBFT RPC endpoint.
     *
     * This uses auto-detection to decide between a CometBFT 0.38, Tendermint 0.37 and 0.34 client.
     * To set the Comet client explicitly, use `createWithSigner`.
     */
    static connectWithSigner(endpoint: string | HttpEndpoint, signer: OfflineSigner, options?: SigningCosmWasmClientOptions): Promise<SigningCosmWasmClient>;
    /**
     * Creates an instance from a manually created Comet client.
     * Use this to use `Comet38Client` or `Tendermint37Client` instead of `Tendermint34Client`.
     */
    static createWithSigner(cometClient: CometClient, signer: OfflineSigner, options?: SigningCosmWasmClientOptions): Promise<SigningCosmWasmClient>;
    /**
     * Creates a client in offline mode.
     *
     * This should only be used in niche cases where you know exactly what you're doing,
     * e.g. when building an offline signing application.
     *
     * When you try to use online functionality with such a signer, an
     * exception will be raised.
     */
    static offline(signer: OfflineSigner, options?: SigningCosmWasmClientOptions): Promise<SigningCosmWasmClient>;
    protected constructor(cometClient: CometClient | undefined, signer: OfflineSigner, options: SigningCosmWasmClientOptions);
    simulate(signerAddress: string, messages: readonly EncodeObject[], memo: string | undefined): Promise<number>;
    /** Uploads code and returns a receipt, including the code ID */
    upload(senderAddress: string, wasmCode: Uint8Array, fee: StdFee | "auto" | number, memo?: string, instantiatePermission?: AccessConfig): Promise<UploadResult>;
    instantiate(senderAddress: string, codeId: number, msg: JsonObject, label: string, fee: StdFee | "auto" | number, options?: InstantiateOptions): Promise<InstantiateResult>;
    instantiate2(senderAddress: string, codeId: number, salt: Uint8Array, msg: JsonObject, label: string, fee: StdFee | "auto" | number, options?: InstantiateOptions): Promise<InstantiateResult>;
    updateAdmin(senderAddress: string, contractAddress: string, newAdmin: string, fee: StdFee | "auto" | number, memo?: string): Promise<ChangeAdminResult>;
    clearAdmin(senderAddress: string, contractAddress: string, fee: StdFee | "auto" | number, memo?: string): Promise<ChangeAdminResult>;
    migrate(senderAddress: string, contractAddress: string, codeId: number, migrateMsg: JsonObject, fee: StdFee | "auto" | number, memo?: string): Promise<MigrateResult>;
    execute(senderAddress: string, contractAddress: string, msg: JsonObject, fee: StdFee | "auto" | number, memo?: string, funds?: readonly Coin[]): Promise<ExecuteResult>;
    /**
     * Like `execute` but allows executing multiple messages in one transaction.
     */
    executeMultiple(senderAddress: string, instructions: readonly ExecuteInstruction[], fee: StdFee | "auto" | number, memo?: string): Promise<ExecuteResult>;
    sendTokens(senderAddress: string, recipientAddress: string, amount: readonly Coin[], fee: StdFee | "auto" | number, memo?: string): Promise<DeliverTxResponse>;
    delegateTokens(delegatorAddress: string, validatorAddress: string, amount: Coin, fee: StdFee | "auto" | number, memo?: string): Promise<DeliverTxResponse>;
    undelegateTokens(delegatorAddress: string, validatorAddress: string, amount: Coin, fee: StdFee | "auto" | number, memo?: string): Promise<DeliverTxResponse>;
    withdrawRewards(delegatorAddress: string, validatorAddress: string, fee: StdFee | "auto" | number, memo?: string): Promise<DeliverTxResponse>;
    /**
     * Creates a transaction with the given messages, fee, memo and timeout height. Then signs and broadcasts the transaction.
     *
     * @param signerAddress The address that will sign transactions using this instance. The signer must be able to sign with this address.
     * @param messages
     * @param fee
     * @param memo
     * @param timeoutHeight (optional) timeout height to prevent the tx from being committed past a certain height
     */
    signAndBroadcast(signerAddress: string, messages: readonly EncodeObject[], fee: StdFee | "auto" | number, memo?: string, timeoutHeight?: bigint): Promise<DeliverTxResponse>;
    /**
     * Creates a transaction with the given messages, fee, memo and timeout height. Then signs and broadcasts the transaction.
     *
     * This method is useful if you want to send a transaction in broadcast,
     * without waiting for it to be placed inside a block, because for example
     * I would like to receive the hash to later track the transaction with another tool.
     *
     * @param signerAddress The address that will sign transactions using this instance. The signer must be able to sign with this address.
     * @param messages
     * @param fee
     * @param memo
     * @param timeoutHeight (optional) timeout height to prevent the tx from being committed past a certain height
     *
     * @returns Returns the hash of the transaction
     */
    signAndBroadcastSync(signerAddress: string, messages: readonly EncodeObject[], fee: StdFee | "auto" | number, memo?: string, timeoutHeight?: bigint): Promise<string>;
    sign(signerAddress: string, messages: readonly EncodeObject[], fee: StdFee, memo: string, explicitSignerData?: SignerData, timeoutHeight?: bigint): Promise<TxRaw>;
    private signAmino;
    private signDirect;
}
