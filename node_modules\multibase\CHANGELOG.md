<a name="0.6.1"></a>
## [0.6.1](https://github.com/multiformats/js-multibase/compare/v0.6.0...v0.6.1) (2020-03-16)


### Bug Fixes

* fix base32pad, cleanup ([bd02451](https://github.com/multiformats/js-multibase/commit/bd02451))



<a name="0.6.0"></a>
# [0.6.0](https://github.com/multiformats/js-multibase/compare/v0.5.0...v0.6.0) (2018-11-22)


### Bug Fixes

* base64url encodes multiple occurances of + and / ([#39](https://github.com/multiformats/js-multibase/issues/39)) ([84f79ba](https://github.com/multiformats/js-multibase/commit/84f79ba)), closes [#35](https://github.com/multiformats/js-multibase/issues/35)
* decode example typo ([4d0fc17](https://github.com/multiformats/js-multibase/commit/4d0fc17))
* do not throw when non buffer/string passed to isEncoded ([1ae01a0](https://github.com/multiformats/js-multibase/commit/1ae01a0))



<a name="0.5.0"></a>
# [0.5.0](https://github.com/multiformats/js-multibase/compare/v0.4.0...v0.5.0) (2018-08-30)


### Features

* export list of multibase names and codes ([#31](https://github.com/multiformats/js-multibase/issues/31)) ([c9e221c](https://github.com/multiformats/js-multibase/commit/c9e221c))



<a name="0.4.0"></a>
# [0.4.0](https://github.com/multiformats/js-multibase/compare/v0.3.4...v0.4.0) (2018-01-01)


### Bug Fixes

* base32 and base64 ([#23](https://github.com/multiformats/js-multibase/issues/23)) ([a9b5150](https://github.com/multiformats/js-multibase/commit/a9b5150))



<a name="0.3.4"></a>
## [0.3.4](https://github.com/multiformats/js-multibase/compare/v0.3.3...v0.3.4) (2017-03-16)



<a name="0.3.3"></a>
## [0.3.3](https://github.com/multiformats/js-multibase/compare/v0.3.2...v0.3.3) (2017-03-15)


### Bug Fixes

* **base16:** hex strings are always of even length ([#16](https://github.com/multiformats/js-multibase/issues/16)) ([b02e0d3](https://github.com/multiformats/js-multibase/commit/b02e0d3))



