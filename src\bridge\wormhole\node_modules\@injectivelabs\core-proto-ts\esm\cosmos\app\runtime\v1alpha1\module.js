/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.app.runtime.v1alpha1";
function createBaseModule() {
    return {
        appName: "",
        beginBlockers: [],
        endBlockers: [],
        initGenesis: [],
        exportGenesis: [],
        overrideStoreKeys: [],
        orderMigrations: [],
        precommiters: [],
        prepareCheckStaters: [],
    };
}
export const Module = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.appName !== "") {
            writer.uint32(10).string(message.appName);
        }
        for (const v of message.beginBlockers) {
            writer.uint32(18).string(v);
        }
        for (const v of message.endBlockers) {
            writer.uint32(26).string(v);
        }
        for (const v of message.initGenesis) {
            writer.uint32(34).string(v);
        }
        for (const v of message.exportGenesis) {
            writer.uint32(42).string(v);
        }
        for (const v of message.overrideStoreKeys) {
            StoreKeyConfig.encode(v, writer.uint32(50).fork()).ldelim();
        }
        for (const v of message.orderMigrations) {
            writer.uint32(58).string(v);
        }
        for (const v of message.precommiters) {
            writer.uint32(66).string(v);
        }
        for (const v of message.prepareCheckStaters) {
            writer.uint32(74).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModule();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.appName = reader.string();
                    break;
                case 2:
                    message.beginBlockers.push(reader.string());
                    break;
                case 3:
                    message.endBlockers.push(reader.string());
                    break;
                case 4:
                    message.initGenesis.push(reader.string());
                    break;
                case 5:
                    message.exportGenesis.push(reader.string());
                    break;
                case 6:
                    message.overrideStoreKeys.push(StoreKeyConfig.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.orderMigrations.push(reader.string());
                    break;
                case 8:
                    message.precommiters.push(reader.string());
                    break;
                case 9:
                    message.prepareCheckStaters.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            appName: isSet(object.appName) ? String(object.appName) : "",
            beginBlockers: Array.isArray(object?.beginBlockers) ? object.beginBlockers.map((e) => String(e)) : [],
            endBlockers: Array.isArray(object?.endBlockers) ? object.endBlockers.map((e) => String(e)) : [],
            initGenesis: Array.isArray(object?.initGenesis) ? object.initGenesis.map((e) => String(e)) : [],
            exportGenesis: Array.isArray(object?.exportGenesis) ? object.exportGenesis.map((e) => String(e)) : [],
            overrideStoreKeys: Array.isArray(object?.overrideStoreKeys)
                ? object.overrideStoreKeys.map((e) => StoreKeyConfig.fromJSON(e))
                : [],
            orderMigrations: Array.isArray(object?.orderMigrations) ? object.orderMigrations.map((e) => String(e)) : [],
            precommiters: Array.isArray(object?.precommiters) ? object.precommiters.map((e) => String(e)) : [],
            prepareCheckStaters: Array.isArray(object?.prepareCheckStaters)
                ? object.prepareCheckStaters.map((e) => String(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.appName !== undefined && (obj.appName = message.appName);
        if (message.beginBlockers) {
            obj.beginBlockers = message.beginBlockers.map((e) => e);
        }
        else {
            obj.beginBlockers = [];
        }
        if (message.endBlockers) {
            obj.endBlockers = message.endBlockers.map((e) => e);
        }
        else {
            obj.endBlockers = [];
        }
        if (message.initGenesis) {
            obj.initGenesis = message.initGenesis.map((e) => e);
        }
        else {
            obj.initGenesis = [];
        }
        if (message.exportGenesis) {
            obj.exportGenesis = message.exportGenesis.map((e) => e);
        }
        else {
            obj.exportGenesis = [];
        }
        if (message.overrideStoreKeys) {
            obj.overrideStoreKeys = message.overrideStoreKeys.map((e) => e ? StoreKeyConfig.toJSON(e) : undefined);
        }
        else {
            obj.overrideStoreKeys = [];
        }
        if (message.orderMigrations) {
            obj.orderMigrations = message.orderMigrations.map((e) => e);
        }
        else {
            obj.orderMigrations = [];
        }
        if (message.precommiters) {
            obj.precommiters = message.precommiters.map((e) => e);
        }
        else {
            obj.precommiters = [];
        }
        if (message.prepareCheckStaters) {
            obj.prepareCheckStaters = message.prepareCheckStaters.map((e) => e);
        }
        else {
            obj.prepareCheckStaters = [];
        }
        return obj;
    },
    create(base) {
        return Module.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModule();
        message.appName = object.appName ?? "";
        message.beginBlockers = object.beginBlockers?.map((e) => e) || [];
        message.endBlockers = object.endBlockers?.map((e) => e) || [];
        message.initGenesis = object.initGenesis?.map((e) => e) || [];
        message.exportGenesis = object.exportGenesis?.map((e) => e) || [];
        message.overrideStoreKeys = object.overrideStoreKeys?.map((e) => StoreKeyConfig.fromPartial(e)) || [];
        message.orderMigrations = object.orderMigrations?.map((e) => e) || [];
        message.precommiters = object.precommiters?.map((e) => e) || [];
        message.prepareCheckStaters = object.prepareCheckStaters?.map((e) => e) || [];
        return message;
    },
};
function createBaseStoreKeyConfig() {
    return { moduleName: "", kvStoreKey: "" };
}
export const StoreKeyConfig = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.moduleName !== "") {
            writer.uint32(10).string(message.moduleName);
        }
        if (message.kvStoreKey !== "") {
            writer.uint32(18).string(message.kvStoreKey);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStoreKeyConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.moduleName = reader.string();
                    break;
                case 2:
                    message.kvStoreKey = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            moduleName: isSet(object.moduleName) ? String(object.moduleName) : "",
            kvStoreKey: isSet(object.kvStoreKey) ? String(object.kvStoreKey) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.moduleName !== undefined && (obj.moduleName = message.moduleName);
        message.kvStoreKey !== undefined && (obj.kvStoreKey = message.kvStoreKey);
        return obj;
    },
    create(base) {
        return StoreKeyConfig.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseStoreKeyConfig();
        message.moduleName = object.moduleName ?? "";
        message.kvStoreKey = object.kvStoreKey ?? "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
