import { StreamOperation } from '../../../types/index.js';
import { InjectiveSpotExchangeRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Stream Transformer
 */
export declare class IndexerSpotStreamTransformer {
    static tradesStreamCallback: (response: InjectiveSpotExchangeRpc.StreamTradesResponse) => {
        trade: import("../index.js").SpotTrade | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static ordersStreamCallback: (response: InjectiveSpotExchangeRpc.StreamOrdersResponse) => {
        order: import("../index.js").SpotLimitOrder | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static orderHistoryStreamCallback: (response: InjectiveSpotExchangeRpc.StreamOrdersHistoryResponse) => {
        order: import("../index.js").SpotOrderHistory | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
    static orderbookV2StreamCallback: (response: InjectiveSpotExchangeRpc.StreamOrderbookV2Response) => {
        orderbook: import("../index.js").OrderbookWithSequence | undefined;
        operation: StreamOperation;
        marketId: string;
        timestamp: string;
    };
    static orderbookUpdateStreamCallback: (response: InjectiveSpotExchangeRpc.StreamOrderbookUpdateResponse) => {
        orderbook: import("../index.js").OrderbookWithSequence | undefined;
        operation: StreamOperation;
        marketId: string;
        timestamp: string;
    };
}
