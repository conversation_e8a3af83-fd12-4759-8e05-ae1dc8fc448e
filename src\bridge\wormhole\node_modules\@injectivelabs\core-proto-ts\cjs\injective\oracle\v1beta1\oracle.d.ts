import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.oracle.v1beta1";
export declare enum OracleType {
    Unspecified = 0,
    Band = 1,
    PriceFeed = 2,
    Coinbase = 3,
    Chainlink = 4,
    Razor = 5,
    Dia = 6,
    API3 = 7,
    Uma = 8,
    Pyth = 9,
    BandIBC = 10,
    Provider = 11,
    Stork = 12,
    UNRECOGNIZED = -1
}
export declare function oracleTypeFromJSON(object: any): OracleType;
export declare function oracleTypeToJSON(object: OracleType): string;
export interface Params {
    pythContract: string;
}
export interface OracleInfo {
    symbol: string;
    oracleType: OracleType;
}
export interface ChainlinkPriceState {
    feedId: string;
    answer: string;
    timestamp: string;
    priceState: PriceState | undefined;
}
export interface BandPriceState {
    symbol: string;
    rate: string;
    resolveTime: string;
    requestID: string;
    priceState: PriceState | undefined;
}
export interface PriceFeedState {
    base: string;
    quote: string;
    priceState: PriceState | undefined;
    relayers: string[];
}
export interface ProviderInfo {
    provider: string;
    relayers: string[];
}
export interface ProviderState {
    providerInfo: ProviderInfo | undefined;
    providerPriceStates: ProviderPriceState[];
}
export interface ProviderPriceState {
    symbol: string;
    state: PriceState | undefined;
}
export interface PriceFeedInfo {
    base: string;
    quote: string;
}
export interface PriceFeedPrice {
    price: string;
}
export interface CoinbasePriceState {
    /** kind should always be "prices" */
    kind: string;
    /** timestamp of the when the price was signed by coinbase */
    timestamp: string;
    /** the symbol of the price, e.g. BTC */
    key: string;
    /** the value of the price scaled by 1e6 */
    value: string;
    /** the price state */
    priceState: PriceState | undefined;
}
export interface StorkPriceState {
    /** timestamp of the when the price was signed by stork */
    timestamp: string;
    /** the symbol of the price, e.g. BTC */
    symbol: string;
    /** the value of the price scaled by 1e18 */
    value: string;
    /** the price state */
    priceState: PriceState | undefined;
}
export interface PriceState {
    price: string;
    cumulativePrice: string;
    timestamp: string;
}
export interface PythPriceState {
    priceId: string;
    emaPrice: string;
    emaConf: string;
    conf: string;
    publishTime: string;
    priceState: PriceState | undefined;
}
export interface BandOracleRequest {
    /** Unique Identifier for band ibc oracle request */
    requestId: string;
    /**
     * OracleScriptID is the unique identifier of the oracle script to be
     * executed.
     */
    oracleScriptId: string;
    /** Symbols is the list of symbols to prepare in the calldata */
    symbols: string[];
    /**
     * AskCount is the number of validators that are requested to respond to this
     * oracle request. Higher value means more security, at a higher gas cost.
     */
    askCount: string;
    /**
     * MinCount is the minimum number of validators necessary for the request to
     * proceed to the execution phase. Higher value means more security, at the
     * cost of liveness.
     */
    minCount: string;
    /**
     * FeeLimit is the maximum tokens that will be paid to all data source
     * providers.
     */
    feeLimit: Coin[];
    /** PrepareGas is amount of gas to pay to prepare raw requests */
    prepareGas: string;
    /** ExecuteGas is amount of gas to reserve for executing */
    executeGas: string;
    /**
     * MinSourceCount is the minimum number of data sources that must be used by
     * each validator
     */
    minSourceCount: string;
}
export interface BandIBCParams {
    /** true if Band IBC should be enabled */
    bandIbcEnabled: boolean;
    /** block request interval to send Band IBC prices */
    ibcRequestInterval: string;
    /** band IBC source channel */
    ibcSourceChannel: string;
    /** band IBC version */
    ibcVersion: string;
    /** band IBC portID */
    ibcPortId: string;
    /** legacy oracle scheme ids */
    legacyOracleIds: string[];
}
export interface SymbolPriceTimestamp {
    oracle: OracleType;
    symbolId: string;
    timestamp: string;
}
export interface LastPriceTimestamps {
    lastPriceTimestamps: SymbolPriceTimestamp[];
}
export interface PriceRecords {
    oracle: OracleType;
    symbolId: string;
    latestPriceRecords: PriceRecord[];
}
export interface PriceRecord {
    timestamp: string;
    price: string;
}
/**
 * MetadataStatistics refers to the metadata summary statistics of the
 * historical sample considered
 */
export interface MetadataStatistics {
    /**
     * GroupCount refers to the number of groups used. Equals RecordsSampleSize if
     * no grouping is used
     */
    groupCount: number;
    /** RecordsSampleSize refers to the total number of records used. */
    recordsSampleSize: number;
    /**
     * Mean refers to the arithmetic mean
     * For trades, the mean is the VWAP computed over the grouped trade records ∑
     * (price * quantity) / ∑ quantity For oracle prices, the mean is computed
     * over the price records ∑ (price) / prices_count
     */
    mean: string;
    /**
     * TWAP refers to the time-weighted average price which equals ∑ (price_i *
     * ∆t_i) / ∑ ∆t_i where ∆t_i = t_i - t_{i-1}
     */
    twap: string;
    /** FirstTimestamp is the timestamp of the oldest record considered */
    firstTimestamp: string;
    /** LastTimestamp is the timestamp of the youngest record considered */
    lastTimestamp: string;
    /** MinPrice refers to the smallest individual raw price considered */
    minPrice: string;
    /** MaxPrice refers to the largest individual raw price considered */
    maxPrice: string;
    /** MedianPrice refers to the median individual raw price considered */
    medianPrice: string;
}
export interface PriceAttestation {
    priceId: string;
    /** MaxPrice refers to the largest individual raw price considered */
    price: string;
    conf: string;
    expo: number;
    emaPrice: string;
    emaConf: string;
    emaExpo: number;
    publishTime: string;
}
export interface AssetPair {
    assetId: string;
    signedPrices: SignedPriceOfAssetPair[];
}
export interface SignedPriceOfAssetPair {
    publisherKey: string;
    timestamp: string;
    price: string;
    signature: Uint8Array;
}
export declare const Params: {
    encode(message: Params, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Params;
    fromJSON(object: any): Params;
    toJSON(message: Params): unknown;
    create(base?: DeepPartial<Params>): Params;
    fromPartial(object: DeepPartial<Params>): Params;
};
export declare const OracleInfo: {
    encode(message: OracleInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleInfo;
    fromJSON(object: any): OracleInfo;
    toJSON(message: OracleInfo): unknown;
    create(base?: DeepPartial<OracleInfo>): OracleInfo;
    fromPartial(object: DeepPartial<OracleInfo>): OracleInfo;
};
export declare const ChainlinkPriceState: {
    encode(message: ChainlinkPriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ChainlinkPriceState;
    fromJSON(object: any): ChainlinkPriceState;
    toJSON(message: ChainlinkPriceState): unknown;
    create(base?: DeepPartial<ChainlinkPriceState>): ChainlinkPriceState;
    fromPartial(object: DeepPartial<ChainlinkPriceState>): ChainlinkPriceState;
};
export declare const BandPriceState: {
    encode(message: BandPriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BandPriceState;
    fromJSON(object: any): BandPriceState;
    toJSON(message: BandPriceState): unknown;
    create(base?: DeepPartial<BandPriceState>): BandPriceState;
    fromPartial(object: DeepPartial<BandPriceState>): BandPriceState;
};
export declare const PriceFeedState: {
    encode(message: PriceFeedState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceFeedState;
    fromJSON(object: any): PriceFeedState;
    toJSON(message: PriceFeedState): unknown;
    create(base?: DeepPartial<PriceFeedState>): PriceFeedState;
    fromPartial(object: DeepPartial<PriceFeedState>): PriceFeedState;
};
export declare const ProviderInfo: {
    encode(message: ProviderInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProviderInfo;
    fromJSON(object: any): ProviderInfo;
    toJSON(message: ProviderInfo): unknown;
    create(base?: DeepPartial<ProviderInfo>): ProviderInfo;
    fromPartial(object: DeepPartial<ProviderInfo>): ProviderInfo;
};
export declare const ProviderState: {
    encode(message: ProviderState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProviderState;
    fromJSON(object: any): ProviderState;
    toJSON(message: ProviderState): unknown;
    create(base?: DeepPartial<ProviderState>): ProviderState;
    fromPartial(object: DeepPartial<ProviderState>): ProviderState;
};
export declare const ProviderPriceState: {
    encode(message: ProviderPriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProviderPriceState;
    fromJSON(object: any): ProviderPriceState;
    toJSON(message: ProviderPriceState): unknown;
    create(base?: DeepPartial<ProviderPriceState>): ProviderPriceState;
    fromPartial(object: DeepPartial<ProviderPriceState>): ProviderPriceState;
};
export declare const PriceFeedInfo: {
    encode(message: PriceFeedInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceFeedInfo;
    fromJSON(object: any): PriceFeedInfo;
    toJSON(message: PriceFeedInfo): unknown;
    create(base?: DeepPartial<PriceFeedInfo>): PriceFeedInfo;
    fromPartial(object: DeepPartial<PriceFeedInfo>): PriceFeedInfo;
};
export declare const PriceFeedPrice: {
    encode(message: PriceFeedPrice, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceFeedPrice;
    fromJSON(object: any): PriceFeedPrice;
    toJSON(message: PriceFeedPrice): unknown;
    create(base?: DeepPartial<PriceFeedPrice>): PriceFeedPrice;
    fromPartial(object: DeepPartial<PriceFeedPrice>): PriceFeedPrice;
};
export declare const CoinbasePriceState: {
    encode(message: CoinbasePriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CoinbasePriceState;
    fromJSON(object: any): CoinbasePriceState;
    toJSON(message: CoinbasePriceState): unknown;
    create(base?: DeepPartial<CoinbasePriceState>): CoinbasePriceState;
    fromPartial(object: DeepPartial<CoinbasePriceState>): CoinbasePriceState;
};
export declare const StorkPriceState: {
    encode(message: StorkPriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StorkPriceState;
    fromJSON(object: any): StorkPriceState;
    toJSON(message: StorkPriceState): unknown;
    create(base?: DeepPartial<StorkPriceState>): StorkPriceState;
    fromPartial(object: DeepPartial<StorkPriceState>): StorkPriceState;
};
export declare const PriceState: {
    encode(message: PriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceState;
    fromJSON(object: any): PriceState;
    toJSON(message: PriceState): unknown;
    create(base?: DeepPartial<PriceState>): PriceState;
    fromPartial(object: DeepPartial<PriceState>): PriceState;
};
export declare const PythPriceState: {
    encode(message: PythPriceState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PythPriceState;
    fromJSON(object: any): PythPriceState;
    toJSON(message: PythPriceState): unknown;
    create(base?: DeepPartial<PythPriceState>): PythPriceState;
    fromPartial(object: DeepPartial<PythPriceState>): PythPriceState;
};
export declare const BandOracleRequest: {
    encode(message: BandOracleRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BandOracleRequest;
    fromJSON(object: any): BandOracleRequest;
    toJSON(message: BandOracleRequest): unknown;
    create(base?: DeepPartial<BandOracleRequest>): BandOracleRequest;
    fromPartial(object: DeepPartial<BandOracleRequest>): BandOracleRequest;
};
export declare const BandIBCParams: {
    encode(message: BandIBCParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BandIBCParams;
    fromJSON(object: any): BandIBCParams;
    toJSON(message: BandIBCParams): unknown;
    create(base?: DeepPartial<BandIBCParams>): BandIBCParams;
    fromPartial(object: DeepPartial<BandIBCParams>): BandIBCParams;
};
export declare const SymbolPriceTimestamp: {
    encode(message: SymbolPriceTimestamp, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SymbolPriceTimestamp;
    fromJSON(object: any): SymbolPriceTimestamp;
    toJSON(message: SymbolPriceTimestamp): unknown;
    create(base?: DeepPartial<SymbolPriceTimestamp>): SymbolPriceTimestamp;
    fromPartial(object: DeepPartial<SymbolPriceTimestamp>): SymbolPriceTimestamp;
};
export declare const LastPriceTimestamps: {
    encode(message: LastPriceTimestamps, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LastPriceTimestamps;
    fromJSON(object: any): LastPriceTimestamps;
    toJSON(message: LastPriceTimestamps): unknown;
    create(base?: DeepPartial<LastPriceTimestamps>): LastPriceTimestamps;
    fromPartial(object: DeepPartial<LastPriceTimestamps>): LastPriceTimestamps;
};
export declare const PriceRecords: {
    encode(message: PriceRecords, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceRecords;
    fromJSON(object: any): PriceRecords;
    toJSON(message: PriceRecords): unknown;
    create(base?: DeepPartial<PriceRecords>): PriceRecords;
    fromPartial(object: DeepPartial<PriceRecords>): PriceRecords;
};
export declare const PriceRecord: {
    encode(message: PriceRecord, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceRecord;
    fromJSON(object: any): PriceRecord;
    toJSON(message: PriceRecord): unknown;
    create(base?: DeepPartial<PriceRecord>): PriceRecord;
    fromPartial(object: DeepPartial<PriceRecord>): PriceRecord;
};
export declare const MetadataStatistics: {
    encode(message: MetadataStatistics, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MetadataStatistics;
    fromJSON(object: any): MetadataStatistics;
    toJSON(message: MetadataStatistics): unknown;
    create(base?: DeepPartial<MetadataStatistics>): MetadataStatistics;
    fromPartial(object: DeepPartial<MetadataStatistics>): MetadataStatistics;
};
export declare const PriceAttestation: {
    encode(message: PriceAttestation, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceAttestation;
    fromJSON(object: any): PriceAttestation;
    toJSON(message: PriceAttestation): unknown;
    create(base?: DeepPartial<PriceAttestation>): PriceAttestation;
    fromPartial(object: DeepPartial<PriceAttestation>): PriceAttestation;
};
export declare const AssetPair: {
    encode(message: AssetPair, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AssetPair;
    fromJSON(object: any): AssetPair;
    toJSON(message: AssetPair): unknown;
    create(base?: DeepPartial<AssetPair>): AssetPair;
    fromPartial(object: DeepPartial<AssetPair>): AssetPair;
};
export declare const SignedPriceOfAssetPair: {
    encode(message: SignedPriceOfAssetPair, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignedPriceOfAssetPair;
    fromJSON(object: any): SignedPriceOfAssetPair;
    toJSON(message: SignedPriceOfAssetPair): unknown;
    create(base?: DeepPartial<SignedPriceOfAssetPair>): SignedPriceOfAssetPair;
    fromPartial(object: DeepPartial<SignedPriceOfAssetPair>): SignedPriceOfAssetPair;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
