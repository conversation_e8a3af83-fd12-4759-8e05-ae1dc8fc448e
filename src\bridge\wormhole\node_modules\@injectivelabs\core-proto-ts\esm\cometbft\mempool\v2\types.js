/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cometbft.mempool.v2";
function createBaseTxs() {
    return { txs: [] };
}
export const Txs = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.txs) {
            writer.uint32(10).bytes(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTxs();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { txs: Array.isArray(object?.txs) ? object.txs.map((e) => bytesFromBase64(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.txs) {
            obj.txs = message.txs.map((e) => base64FromBytes(e !== undefined ? e : new Uint8Array()));
        }
        else {
            obj.txs = [];
        }
        return obj;
    },
    create(base) {
        return Txs.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTxs();
        message.txs = object.txs?.map((e) => e) || [];
        return message;
    },
};
function createBaseHaveTx() {
    return { txKey: new Uint8Array() };
}
export const HaveTx = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.txKey.length !== 0) {
            writer.uint32(10).bytes(message.txKey);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHaveTx();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txKey = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { txKey: isSet(object.txKey) ? bytesFromBase64(object.txKey) : new Uint8Array() };
    },
    toJSON(message) {
        const obj = {};
        message.txKey !== undefined &&
            (obj.txKey = base64FromBytes(message.txKey !== undefined ? message.txKey : new Uint8Array()));
        return obj;
    },
    create(base) {
        return HaveTx.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseHaveTx();
        message.txKey = object.txKey ?? new Uint8Array();
        return message;
    },
};
function createBaseResetRoute() {
    return {};
}
export const ResetRoute = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseResetRoute();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return ResetRoute.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseResetRoute();
        return message;
    },
};
function createBaseMessage() {
    return { txs: undefined, haveTx: undefined, resetRoute: undefined };
}
export const Message = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.txs !== undefined) {
            Txs.encode(message.txs, writer.uint32(10).fork()).ldelim();
        }
        if (message.haveTx !== undefined) {
            HaveTx.encode(message.haveTx, writer.uint32(18).fork()).ldelim();
        }
        if (message.resetRoute !== undefined) {
            ResetRoute.encode(message.resetRoute, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs = Txs.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.haveTx = HaveTx.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.resetRoute = ResetRoute.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            txs: isSet(object.txs) ? Txs.fromJSON(object.txs) : undefined,
            haveTx: isSet(object.haveTx) ? HaveTx.fromJSON(object.haveTx) : undefined,
            resetRoute: isSet(object.resetRoute) ? ResetRoute.fromJSON(object.resetRoute) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.txs !== undefined && (obj.txs = message.txs ? Txs.toJSON(message.txs) : undefined);
        message.haveTx !== undefined && (obj.haveTx = message.haveTx ? HaveTx.toJSON(message.haveTx) : undefined);
        message.resetRoute !== undefined &&
            (obj.resetRoute = message.resetRoute ? ResetRoute.toJSON(message.resetRoute) : undefined);
        return obj;
    },
    create(base) {
        return Message.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMessage();
        message.txs = (object.txs !== undefined && object.txs !== null) ? Txs.fromPartial(object.txs) : undefined;
        message.haveTx = (object.haveTx !== undefined && object.haveTx !== null)
            ? HaveTx.fromPartial(object.haveTx)
            : undefined;
        message.resetRoute = (object.resetRoute !== undefined && object.resetRoute !== null)
            ? ResetRoute.fromPartial(object.resetRoute)
            : undefined;
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
