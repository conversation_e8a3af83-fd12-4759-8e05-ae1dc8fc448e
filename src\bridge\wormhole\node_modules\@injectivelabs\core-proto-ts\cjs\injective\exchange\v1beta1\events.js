"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventOrderCancelFail = exports.EventInvalidGrant = exports.EventGrantActivation = exports.EventGrantAuthorizations = exports.Orderbook = exports.OrderbookUpdate = exports.EventOrderbookUpdate = exports.EventAtomicMarketOrderFeeMultipliersUpdated = exports.EventOrderFail = exports.EventConditionalDerivativeOrderTrigger = exports.EventCancelConditionalDerivativeOrder = exports.EventNewConditionalDerivativeOrder = exports.EventTradingRewardDistribution = exports.EventTradingRewardCampaignUpdate = exports.EventFeeDiscountSchedule = exports.EventCancelDerivativeOrder = exports.DerivativeMarketOrderCancel = exports.EventBatchDepositUpdate = exports.EventSubaccountBalanceTransfer = exports.EventSubaccountWithdraw = exports.EventSubaccountDeposit = exports.EventPerpetualMarketFundingUpdate = exports.EventExpiryFuturesMarketUpdate = exports.EventPerpetualMarketUpdate = exports.EventSpotMarketUpdate = exports.EventCancelSpotOrder = exports.EventNewDerivativeOrders = exports.EventNewSpotOrders = exports.EventBinaryOptionsMarketUpdate = exports.EventAllPositionsHaircut = exports.EventMarketBeyondBankruptcy = exports.EventNotSettledMarketBalance = exports.EventSettledMarketBalance = exports.EventDerivativeMarketPaused = exports.EventBatchDerivativePosition = exports.EventLostFundsFromLiquidation = exports.EventBatchDerivativeExecution = exports.EventBatchSpotExecution = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var exchange_1 = require("./exchange.js");
exports.protobufPackage = "injective.exchange.v1beta1";
function createBaseEventBatchSpotExecution() {
    return { marketId: "", isBuy: false, executionType: 0, trades: [] };
}
exports.EventBatchSpotExecution = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.executionType !== 0) {
            writer.uint32(24).int32(message.executionType);
        }
        try {
            for (var _b = __values(message.trades), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.TradeLog.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBatchSpotExecution();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.executionType = reader.int32();
                    break;
                case 4:
                    message.trades.push(exchange_1.TradeLog.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            executionType: isSet(object.executionType) ? (0, exchange_1.executionTypeFromJSON)(object.executionType) : 0,
            trades: Array.isArray(object === null || object === void 0 ? void 0 : object.trades) ? object.trades.map(function (e) { return exchange_1.TradeLog.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.executionType !== undefined && (obj.executionType = (0, exchange_1.executionTypeToJSON)(message.executionType));
        if (message.trades) {
            obj.trades = message.trades.map(function (e) { return e ? exchange_1.TradeLog.toJSON(e) : undefined; });
        }
        else {
            obj.trades = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventBatchSpotExecution.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventBatchSpotExecution();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        message.executionType = (_c = object.executionType) !== null && _c !== void 0 ? _c : 0;
        message.trades = ((_d = object.trades) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exchange_1.TradeLog.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventBatchDerivativeExecution() {
    return { marketId: "", isBuy: false, isLiquidation: false, cumulativeFunding: "", executionType: 0, trades: [] };
}
exports.EventBatchDerivativeExecution = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.isLiquidation === true) {
            writer.uint32(24).bool(message.isLiquidation);
        }
        if (message.cumulativeFunding !== "") {
            writer.uint32(34).string(message.cumulativeFunding);
        }
        if (message.executionType !== 0) {
            writer.uint32(40).int32(message.executionType);
        }
        try {
            for (var _b = __values(message.trades), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DerivativeTradeLog.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBatchDerivativeExecution();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.isLiquidation = reader.bool();
                    break;
                case 4:
                    message.cumulativeFunding = reader.string();
                    break;
                case 5:
                    message.executionType = reader.int32();
                    break;
                case 6:
                    message.trades.push(exchange_1.DerivativeTradeLog.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            isLiquidation: isSet(object.isLiquidation) ? Boolean(object.isLiquidation) : false,
            cumulativeFunding: isSet(object.cumulativeFunding) ? String(object.cumulativeFunding) : "",
            executionType: isSet(object.executionType) ? (0, exchange_1.executionTypeFromJSON)(object.executionType) : 0,
            trades: Array.isArray(object === null || object === void 0 ? void 0 : object.trades) ? object.trades.map(function (e) { return exchange_1.DerivativeTradeLog.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.isLiquidation !== undefined && (obj.isLiquidation = message.isLiquidation);
        message.cumulativeFunding !== undefined && (obj.cumulativeFunding = message.cumulativeFunding);
        message.executionType !== undefined && (obj.executionType = (0, exchange_1.executionTypeToJSON)(message.executionType));
        if (message.trades) {
            obj.trades = message.trades.map(function (e) { return e ? exchange_1.DerivativeTradeLog.toJSON(e) : undefined; });
        }
        else {
            obj.trades = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventBatchDerivativeExecution.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseEventBatchDerivativeExecution();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        message.isLiquidation = (_c = object.isLiquidation) !== null && _c !== void 0 ? _c : false;
        message.cumulativeFunding = (_d = object.cumulativeFunding) !== null && _d !== void 0 ? _d : "";
        message.executionType = (_e = object.executionType) !== null && _e !== void 0 ? _e : 0;
        message.trades = ((_f = object.trades) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exchange_1.DerivativeTradeLog.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventLostFundsFromLiquidation() {
    return {
        marketId: "",
        subaccountId: new Uint8Array(),
        lostFundsFromAvailableDuringPayout: "",
        lostFundsFromOrderCancels: "",
    };
}
exports.EventLostFundsFromLiquidation = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId.length !== 0) {
            writer.uint32(18).bytes(message.subaccountId);
        }
        if (message.lostFundsFromAvailableDuringPayout !== "") {
            writer.uint32(26).string(message.lostFundsFromAvailableDuringPayout);
        }
        if (message.lostFundsFromOrderCancels !== "") {
            writer.uint32(34).string(message.lostFundsFromOrderCancels);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventLostFundsFromLiquidation();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.bytes();
                    break;
                case 3:
                    message.lostFundsFromAvailableDuringPayout = reader.string();
                    break;
                case 4:
                    message.lostFundsFromOrderCancels = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            lostFundsFromAvailableDuringPayout: isSet(object.lostFundsFromAvailableDuringPayout)
                ? String(object.lostFundsFromAvailableDuringPayout)
                : "",
            lostFundsFromOrderCancels: isSet(object.lostFundsFromOrderCancels)
                ? String(object.lostFundsFromOrderCancels)
                : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.lostFundsFromAvailableDuringPayout !== undefined &&
            (obj.lostFundsFromAvailableDuringPayout = message.lostFundsFromAvailableDuringPayout);
        message.lostFundsFromOrderCancels !== undefined &&
            (obj.lostFundsFromOrderCancels = message.lostFundsFromOrderCancels);
        return obj;
    },
    create: function (base) {
        return exports.EventLostFundsFromLiquidation.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventLostFundsFromLiquidation();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.lostFundsFromAvailableDuringPayout = (_c = object.lostFundsFromAvailableDuringPayout) !== null && _c !== void 0 ? _c : "";
        message.lostFundsFromOrderCancels = (_d = object.lostFundsFromOrderCancels) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseEventBatchDerivativePosition() {
    return { marketId: "", positions: [] };
}
exports.EventBatchDerivativePosition = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _b = __values(message.positions), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.SubaccountPosition.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBatchDerivativePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.positions.push(exchange_1.SubaccountPosition.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            positions: Array.isArray(object === null || object === void 0 ? void 0 : object.positions)
                ? object.positions.map(function (e) { return exchange_1.SubaccountPosition.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.positions) {
            obj.positions = message.positions.map(function (e) { return e ? exchange_1.SubaccountPosition.toJSON(e) : undefined; });
        }
        else {
            obj.positions = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventBatchDerivativePosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventBatchDerivativePosition();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.positions = ((_b = object.positions) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.SubaccountPosition.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventDerivativeMarketPaused() {
    return { marketId: "", settlePrice: "", totalMissingFunds: "", missingFundsRate: "" };
}
exports.EventDerivativeMarketPaused = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.settlePrice !== "") {
            writer.uint32(18).string(message.settlePrice);
        }
        if (message.totalMissingFunds !== "") {
            writer.uint32(26).string(message.totalMissingFunds);
        }
        if (message.missingFundsRate !== "") {
            writer.uint32(34).string(message.missingFundsRate);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventDerivativeMarketPaused();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.settlePrice = reader.string();
                    break;
                case 3:
                    message.totalMissingFunds = reader.string();
                    break;
                case 4:
                    message.missingFundsRate = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlePrice: isSet(object.settlePrice) ? String(object.settlePrice) : "",
            totalMissingFunds: isSet(object.totalMissingFunds) ? String(object.totalMissingFunds) : "",
            missingFundsRate: isSet(object.missingFundsRate) ? String(object.missingFundsRate) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlePrice !== undefined && (obj.settlePrice = message.settlePrice);
        message.totalMissingFunds !== undefined && (obj.totalMissingFunds = message.totalMissingFunds);
        message.missingFundsRate !== undefined && (obj.missingFundsRate = message.missingFundsRate);
        return obj;
    },
    create: function (base) {
        return exports.EventDerivativeMarketPaused.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventDerivativeMarketPaused();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.settlePrice = (_b = object.settlePrice) !== null && _b !== void 0 ? _b : "";
        message.totalMissingFunds = (_c = object.totalMissingFunds) !== null && _c !== void 0 ? _c : "";
        message.missingFundsRate = (_d = object.missingFundsRate) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseEventSettledMarketBalance() {
    return { marketId: "", amount: "" };
}
exports.EventSettledMarketBalance = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSettledMarketBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.EventSettledMarketBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventSettledMarketBalance();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventNotSettledMarketBalance() {
    return { marketId: "", amount: "" };
}
exports.EventNotSettledMarketBalance = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNotSettledMarketBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.EventNotSettledMarketBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventNotSettledMarketBalance();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventMarketBeyondBankruptcy() {
    return { marketId: "", settlePrice: "", missingMarketFunds: "" };
}
exports.EventMarketBeyondBankruptcy = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.settlePrice !== "") {
            writer.uint32(18).string(message.settlePrice);
        }
        if (message.missingMarketFunds !== "") {
            writer.uint32(26).string(message.missingMarketFunds);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventMarketBeyondBankruptcy();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.settlePrice = reader.string();
                    break;
                case 3:
                    message.missingMarketFunds = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlePrice: isSet(object.settlePrice) ? String(object.settlePrice) : "",
            missingMarketFunds: isSet(object.missingMarketFunds) ? String(object.missingMarketFunds) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlePrice !== undefined && (obj.settlePrice = message.settlePrice);
        message.missingMarketFunds !== undefined && (obj.missingMarketFunds = message.missingMarketFunds);
        return obj;
    },
    create: function (base) {
        return exports.EventMarketBeyondBankruptcy.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventMarketBeyondBankruptcy();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.settlePrice = (_b = object.settlePrice) !== null && _b !== void 0 ? _b : "";
        message.missingMarketFunds = (_c = object.missingMarketFunds) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventAllPositionsHaircut() {
    return { marketId: "", settlePrice: "", missingFundsRate: "" };
}
exports.EventAllPositionsHaircut = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.settlePrice !== "") {
            writer.uint32(18).string(message.settlePrice);
        }
        if (message.missingFundsRate !== "") {
            writer.uint32(26).string(message.missingFundsRate);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAllPositionsHaircut();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.settlePrice = reader.string();
                    break;
                case 3:
                    message.missingFundsRate = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlePrice: isSet(object.settlePrice) ? String(object.settlePrice) : "",
            missingFundsRate: isSet(object.missingFundsRate) ? String(object.missingFundsRate) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlePrice !== undefined && (obj.settlePrice = message.settlePrice);
        message.missingFundsRate !== undefined && (obj.missingFundsRate = message.missingFundsRate);
        return obj;
    },
    create: function (base) {
        return exports.EventAllPositionsHaircut.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventAllPositionsHaircut();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.settlePrice = (_b = object.settlePrice) !== null && _b !== void 0 ? _b : "";
        message.missingFundsRate = (_c = object.missingFundsRate) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventBinaryOptionsMarketUpdate() {
    return { market: undefined };
}
exports.EventBinaryOptionsMarketUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.BinaryOptionsMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBinaryOptionsMarketUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.BinaryOptionsMarket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { market: isSet(object.market) ? exchange_1.BinaryOptionsMarket.fromJSON(object.market) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined &&
            (obj.market = message.market ? exchange_1.BinaryOptionsMarket.toJSON(message.market) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventBinaryOptionsMarketUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventBinaryOptionsMarketUpdate();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.BinaryOptionsMarket.fromPartial(object.market)
            : undefined;
        return message;
    },
};
function createBaseEventNewSpotOrders() {
    return { marketId: "", buyOrders: [], sellOrders: [] };
}
exports.EventNewSpotOrders = {
    encode: function (message, writer) {
        var e_4, _a, e_5, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _c = __values(message.buyOrders), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.SpotLimitOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _e = __values(message.sellOrders), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.SpotLimitOrder.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNewSpotOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.buyOrders.push(exchange_1.SpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.sellOrders.push(exchange_1.SpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            buyOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.buyOrders) ? object.buyOrders.map(function (e) { return exchange_1.SpotLimitOrder.fromJSON(e); }) : [],
            sellOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.sellOrders)
                ? object.sellOrders.map(function (e) { return exchange_1.SpotLimitOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.buyOrders) {
            obj.buyOrders = message.buyOrders.map(function (e) { return e ? exchange_1.SpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.buyOrders = [];
        }
        if (message.sellOrders) {
            obj.sellOrders = message.sellOrders.map(function (e) { return e ? exchange_1.SpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.sellOrders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventNewSpotOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventNewSpotOrders();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.buyOrders = ((_b = object.buyOrders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.SpotLimitOrder.fromPartial(e); })) || [];
        message.sellOrders = ((_c = object.sellOrders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.SpotLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventNewDerivativeOrders() {
    return { marketId: "", buyOrders: [], sellOrders: [] };
}
exports.EventNewDerivativeOrders = {
    encode: function (message, writer) {
        var e_6, _a, e_7, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _c = __values(message.buyOrders), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.DerivativeLimitOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _e = __values(message.sellOrders), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.DerivativeLimitOrder.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNewDerivativeOrders();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.buyOrders.push(exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.sellOrders.push(exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            buyOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.buyOrders)
                ? object.buyOrders.map(function (e) { return exchange_1.DerivativeLimitOrder.fromJSON(e); })
                : [],
            sellOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.sellOrders)
                ? object.sellOrders.map(function (e) { return exchange_1.DerivativeLimitOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.buyOrders) {
            obj.buyOrders = message.buyOrders.map(function (e) { return e ? exchange_1.DerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.buyOrders = [];
        }
        if (message.sellOrders) {
            obj.sellOrders = message.sellOrders.map(function (e) { return e ? exchange_1.DerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.sellOrders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventNewDerivativeOrders.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventNewDerivativeOrders();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.buyOrders = ((_b = object.buyOrders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.DerivativeLimitOrder.fromPartial(e); })) || [];
        message.sellOrders = ((_c = object.sellOrders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.DerivativeLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventCancelSpotOrder() {
    return { marketId: "", order: undefined };
}
exports.EventCancelSpotOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            exchange_1.SpotLimitOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventCancelSpotOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.SpotLimitOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? exchange_1.SpotLimitOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? exchange_1.SpotLimitOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventCancelSpotOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventCancelSpotOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.SpotLimitOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseEventSpotMarketUpdate() {
    return { market: undefined };
}
exports.EventSpotMarketUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.SpotMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSpotMarketUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.SpotMarket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { market: isSet(object.market) ? exchange_1.SpotMarket.fromJSON(object.market) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.SpotMarket.toJSON(message.market) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventSpotMarketUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventSpotMarketUpdate();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.SpotMarket.fromPartial(object.market)
            : undefined;
        return message;
    },
};
function createBaseEventPerpetualMarketUpdate() {
    return { market: undefined, perpetualMarketInfo: undefined, funding: undefined };
}
exports.EventPerpetualMarketUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.DerivativeMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        if (message.perpetualMarketInfo !== undefined) {
            exchange_1.PerpetualMarketInfo.encode(message.perpetualMarketInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.funding !== undefined) {
            exchange_1.PerpetualMarketFunding.encode(message.funding, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventPerpetualMarketUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.DerivativeMarket.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.perpetualMarketInfo = exchange_1.PerpetualMarketInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.funding = exchange_1.PerpetualMarketFunding.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            market: isSet(object.market) ? exchange_1.DerivativeMarket.fromJSON(object.market) : undefined,
            perpetualMarketInfo: isSet(object.perpetualMarketInfo)
                ? exchange_1.PerpetualMarketInfo.fromJSON(object.perpetualMarketInfo)
                : undefined,
            funding: isSet(object.funding) ? exchange_1.PerpetualMarketFunding.fromJSON(object.funding) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.DerivativeMarket.toJSON(message.market) : undefined);
        message.perpetualMarketInfo !== undefined && (obj.perpetualMarketInfo = message.perpetualMarketInfo
            ? exchange_1.PerpetualMarketInfo.toJSON(message.perpetualMarketInfo)
            : undefined);
        message.funding !== undefined &&
            (obj.funding = message.funding ? exchange_1.PerpetualMarketFunding.toJSON(message.funding) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventPerpetualMarketUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventPerpetualMarketUpdate();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.DerivativeMarket.fromPartial(object.market)
            : undefined;
        message.perpetualMarketInfo = (object.perpetualMarketInfo !== undefined && object.perpetualMarketInfo !== null)
            ? exchange_1.PerpetualMarketInfo.fromPartial(object.perpetualMarketInfo)
            : undefined;
        message.funding = (object.funding !== undefined && object.funding !== null)
            ? exchange_1.PerpetualMarketFunding.fromPartial(object.funding)
            : undefined;
        return message;
    },
};
function createBaseEventExpiryFuturesMarketUpdate() {
    return { market: undefined, expiryFuturesMarketInfo: undefined };
}
exports.EventExpiryFuturesMarketUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.DerivativeMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        if (message.expiryFuturesMarketInfo !== undefined) {
            exchange_1.ExpiryFuturesMarketInfo.encode(message.expiryFuturesMarketInfo, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventExpiryFuturesMarketUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.DerivativeMarket.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.expiryFuturesMarketInfo = exchange_1.ExpiryFuturesMarketInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            market: isSet(object.market) ? exchange_1.DerivativeMarket.fromJSON(object.market) : undefined,
            expiryFuturesMarketInfo: isSet(object.expiryFuturesMarketInfo)
                ? exchange_1.ExpiryFuturesMarketInfo.fromJSON(object.expiryFuturesMarketInfo)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.DerivativeMarket.toJSON(message.market) : undefined);
        message.expiryFuturesMarketInfo !== undefined && (obj.expiryFuturesMarketInfo = message.expiryFuturesMarketInfo
            ? exchange_1.ExpiryFuturesMarketInfo.toJSON(message.expiryFuturesMarketInfo)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventExpiryFuturesMarketUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventExpiryFuturesMarketUpdate();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.DerivativeMarket.fromPartial(object.market)
            : undefined;
        message.expiryFuturesMarketInfo =
            (object.expiryFuturesMarketInfo !== undefined && object.expiryFuturesMarketInfo !== null)
                ? exchange_1.ExpiryFuturesMarketInfo.fromPartial(object.expiryFuturesMarketInfo)
                : undefined;
        return message;
    },
};
function createBaseEventPerpetualMarketFundingUpdate() {
    return { marketId: "", funding: undefined, isHourlyFunding: false, fundingRate: "", markPrice: "" };
}
exports.EventPerpetualMarketFundingUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.funding !== undefined) {
            exchange_1.PerpetualMarketFunding.encode(message.funding, writer.uint32(18).fork()).ldelim();
        }
        if (message.isHourlyFunding === true) {
            writer.uint32(24).bool(message.isHourlyFunding);
        }
        if (message.fundingRate !== "") {
            writer.uint32(34).string(message.fundingRate);
        }
        if (message.markPrice !== "") {
            writer.uint32(42).string(message.markPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventPerpetualMarketFundingUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.funding = exchange_1.PerpetualMarketFunding.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.isHourlyFunding = reader.bool();
                    break;
                case 4:
                    message.fundingRate = reader.string();
                    break;
                case 5:
                    message.markPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            funding: isSet(object.funding) ? exchange_1.PerpetualMarketFunding.fromJSON(object.funding) : undefined,
            isHourlyFunding: isSet(object.isHourlyFunding) ? Boolean(object.isHourlyFunding) : false,
            fundingRate: isSet(object.fundingRate) ? String(object.fundingRate) : "",
            markPrice: isSet(object.markPrice) ? String(object.markPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.funding !== undefined &&
            (obj.funding = message.funding ? exchange_1.PerpetualMarketFunding.toJSON(message.funding) : undefined);
        message.isHourlyFunding !== undefined && (obj.isHourlyFunding = message.isHourlyFunding);
        message.fundingRate !== undefined && (obj.fundingRate = message.fundingRate);
        message.markPrice !== undefined && (obj.markPrice = message.markPrice);
        return obj;
    },
    create: function (base) {
        return exports.EventPerpetualMarketFundingUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventPerpetualMarketFundingUpdate();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.funding = (object.funding !== undefined && object.funding !== null)
            ? exchange_1.PerpetualMarketFunding.fromPartial(object.funding)
            : undefined;
        message.isHourlyFunding = (_b = object.isHourlyFunding) !== null && _b !== void 0 ? _b : false;
        message.fundingRate = (_c = object.fundingRate) !== null && _c !== void 0 ? _c : "";
        message.markPrice = (_d = object.markPrice) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseEventSubaccountDeposit() {
    return { srcAddress: "", subaccountId: new Uint8Array(), amount: undefined };
}
exports.EventSubaccountDeposit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.srcAddress !== "") {
            writer.uint32(10).string(message.srcAddress);
        }
        if (message.subaccountId.length !== 0) {
            writer.uint32(18).bytes(message.subaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSubaccountDeposit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.srcAddress = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.bytes();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            srcAddress: isSet(object.srcAddress) ? String(object.srcAddress) : "",
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.srcAddress !== undefined && (obj.srcAddress = message.srcAddress);
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventSubaccountDeposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventSubaccountDeposit();
        message.srcAddress = (_a = object.srcAddress) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseEventSubaccountWithdraw() {
    return { subaccountId: new Uint8Array(), dstAddress: "", amount: undefined };
}
exports.EventSubaccountWithdraw = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId.length !== 0) {
            writer.uint32(10).bytes(message.subaccountId);
        }
        if (message.dstAddress !== "") {
            writer.uint32(18).string(message.dstAddress);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSubaccountWithdraw();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.bytes();
                    break;
                case 2:
                    message.dstAddress = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            dstAddress: isSet(object.dstAddress) ? String(object.dstAddress) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.dstAddress !== undefined && (obj.dstAddress = message.dstAddress);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventSubaccountWithdraw.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventSubaccountWithdraw();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.dstAddress = (_b = object.dstAddress) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseEventSubaccountBalanceTransfer() {
    return { srcSubaccountId: "", dstSubaccountId: "", amount: undefined };
}
exports.EventSubaccountBalanceTransfer = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.srcSubaccountId !== "") {
            writer.uint32(10).string(message.srcSubaccountId);
        }
        if (message.dstSubaccountId !== "") {
            writer.uint32(18).string(message.dstSubaccountId);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSubaccountBalanceTransfer();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.srcSubaccountId = reader.string();
                    break;
                case 2:
                    message.dstSubaccountId = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            srcSubaccountId: isSet(object.srcSubaccountId) ? String(object.srcSubaccountId) : "",
            dstSubaccountId: isSet(object.dstSubaccountId) ? String(object.dstSubaccountId) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.srcSubaccountId !== undefined && (obj.srcSubaccountId = message.srcSubaccountId);
        message.dstSubaccountId !== undefined && (obj.dstSubaccountId = message.dstSubaccountId);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventSubaccountBalanceTransfer.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventSubaccountBalanceTransfer();
        message.srcSubaccountId = (_a = object.srcSubaccountId) !== null && _a !== void 0 ? _a : "";
        message.dstSubaccountId = (_b = object.dstSubaccountId) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseEventBatchDepositUpdate() {
    return { depositUpdates: [] };
}
exports.EventBatchDepositUpdate = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.depositUpdates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DepositUpdate.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBatchDepositUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.depositUpdates.push(exchange_1.DepositUpdate.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            depositUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.depositUpdates)
                ? object.depositUpdates.map(function (e) { return exchange_1.DepositUpdate.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.depositUpdates) {
            obj.depositUpdates = message.depositUpdates.map(function (e) { return e ? exchange_1.DepositUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.depositUpdates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventBatchDepositUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventBatchDepositUpdate();
        message.depositUpdates = ((_a = object.depositUpdates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.DepositUpdate.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseDerivativeMarketOrderCancel() {
    return { marketOrder: undefined, cancelQuantity: "" };
}
exports.DerivativeMarketOrderCancel = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketOrder !== undefined) {
            exchange_1.DerivativeMarketOrder.encode(message.marketOrder, writer.uint32(10).fork()).ldelim();
        }
        if (message.cancelQuantity !== "") {
            writer.uint32(18).string(message.cancelQuantity);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarketOrderCancel();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketOrder = exchange_1.DerivativeMarketOrder.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.cancelQuantity = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketOrder: isSet(object.marketOrder) ? exchange_1.DerivativeMarketOrder.fromJSON(object.marketOrder) : undefined,
            cancelQuantity: isSet(object.cancelQuantity) ? String(object.cancelQuantity) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketOrder !== undefined &&
            (obj.marketOrder = message.marketOrder ? exchange_1.DerivativeMarketOrder.toJSON(message.marketOrder) : undefined);
        message.cancelQuantity !== undefined && (obj.cancelQuantity = message.cancelQuantity);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarketOrderCancel.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseDerivativeMarketOrderCancel();
        message.marketOrder = (object.marketOrder !== undefined && object.marketOrder !== null)
            ? exchange_1.DerivativeMarketOrder.fromPartial(object.marketOrder)
            : undefined;
        message.cancelQuantity = (_a = object.cancelQuantity) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseEventCancelDerivativeOrder() {
    return { marketId: "", isLimitCancel: false, limitOrder: undefined, marketOrderCancel: undefined };
}
exports.EventCancelDerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isLimitCancel === true) {
            writer.uint32(16).bool(message.isLimitCancel);
        }
        if (message.limitOrder !== undefined) {
            exchange_1.DerivativeLimitOrder.encode(message.limitOrder, writer.uint32(26).fork()).ldelim();
        }
        if (message.marketOrderCancel !== undefined) {
            exports.DerivativeMarketOrderCancel.encode(message.marketOrderCancel, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventCancelDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isLimitCancel = reader.bool();
                    break;
                case 3:
                    message.limitOrder = exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.marketOrderCancel = exports.DerivativeMarketOrderCancel.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isLimitCancel: isSet(object.isLimitCancel) ? Boolean(object.isLimitCancel) : false,
            limitOrder: isSet(object.limitOrder) ? exchange_1.DerivativeLimitOrder.fromJSON(object.limitOrder) : undefined,
            marketOrderCancel: isSet(object.marketOrderCancel)
                ? exports.DerivativeMarketOrderCancel.fromJSON(object.marketOrderCancel)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isLimitCancel !== undefined && (obj.isLimitCancel = message.isLimitCancel);
        message.limitOrder !== undefined &&
            (obj.limitOrder = message.limitOrder ? exchange_1.DerivativeLimitOrder.toJSON(message.limitOrder) : undefined);
        message.marketOrderCancel !== undefined && (obj.marketOrderCancel = message.marketOrderCancel
            ? exports.DerivativeMarketOrderCancel.toJSON(message.marketOrderCancel)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventCancelDerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventCancelDerivativeOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isLimitCancel = (_b = object.isLimitCancel) !== null && _b !== void 0 ? _b : false;
        message.limitOrder = (object.limitOrder !== undefined && object.limitOrder !== null)
            ? exchange_1.DerivativeLimitOrder.fromPartial(object.limitOrder)
            : undefined;
        message.marketOrderCancel = (object.marketOrderCancel !== undefined && object.marketOrderCancel !== null)
            ? exports.DerivativeMarketOrderCancel.fromPartial(object.marketOrderCancel)
            : undefined;
        return message;
    },
};
function createBaseEventFeeDiscountSchedule() {
    return { schedule: undefined };
}
exports.EventFeeDiscountSchedule = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.schedule !== undefined) {
            exchange_1.FeeDiscountSchedule.encode(message.schedule, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventFeeDiscountSchedule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.schedule = exchange_1.FeeDiscountSchedule.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { schedule: isSet(object.schedule) ? exchange_1.FeeDiscountSchedule.fromJSON(object.schedule) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? exchange_1.FeeDiscountSchedule.toJSON(message.schedule) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventFeeDiscountSchedule.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventFeeDiscountSchedule();
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? exchange_1.FeeDiscountSchedule.fromPartial(object.schedule)
            : undefined;
        return message;
    },
};
function createBaseEventTradingRewardCampaignUpdate() {
    return { campaignInfo: undefined, campaignRewardPools: [] };
}
exports.EventTradingRewardCampaignUpdate = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.campaignInfo !== undefined) {
            exchange_1.TradingRewardCampaignInfo.encode(message.campaignInfo, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.campaignRewardPools), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventTradingRewardCampaignUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignInfo = exchange_1.TradingRewardCampaignInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.campaignRewardPools.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            campaignInfo: isSet(object.campaignInfo) ? exchange_1.TradingRewardCampaignInfo.fromJSON(object.campaignInfo) : undefined,
            campaignRewardPools: Array.isArray(object === null || object === void 0 ? void 0 : object.campaignRewardPools)
                ? object.campaignRewardPools.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.campaignInfo !== undefined &&
            (obj.campaignInfo = message.campaignInfo ? exchange_1.TradingRewardCampaignInfo.toJSON(message.campaignInfo) : undefined);
        if (message.campaignRewardPools) {
            obj.campaignRewardPools = message.campaignRewardPools.map(function (e) { return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined; });
        }
        else {
            obj.campaignRewardPools = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventTradingRewardCampaignUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventTradingRewardCampaignUpdate();
        message.campaignInfo = (object.campaignInfo !== undefined && object.campaignInfo !== null)
            ? exchange_1.TradingRewardCampaignInfo.fromPartial(object.campaignInfo)
            : undefined;
        message.campaignRewardPools = ((_a = object.campaignRewardPools) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventTradingRewardDistribution() {
    return { accountRewards: [] };
}
exports.EventTradingRewardDistribution = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.accountRewards), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.AccountRewards.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventTradingRewardDistribution();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountRewards.push(exchange_1.AccountRewards.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accountRewards: Array.isArray(object === null || object === void 0 ? void 0 : object.accountRewards)
                ? object.accountRewards.map(function (e) { return exchange_1.AccountRewards.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accountRewards) {
            obj.accountRewards = message.accountRewards.map(function (e) { return e ? exchange_1.AccountRewards.toJSON(e) : undefined; });
        }
        else {
            obj.accountRewards = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventTradingRewardDistribution.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventTradingRewardDistribution();
        message.accountRewards = ((_a = object.accountRewards) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.AccountRewards.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventNewConditionalDerivativeOrder() {
    return { marketId: "", order: undefined, hash: new Uint8Array(), isMarket: false };
}
exports.EventNewConditionalDerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            exchange_1.DerivativeOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        if (message.hash.length !== 0) {
            writer.uint32(26).bytes(message.hash);
        }
        if (message.isMarket === true) {
            writer.uint32(32).bool(message.isMarket);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNewConditionalDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = exchange_1.DerivativeOrder.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.hash = reader.bytes();
                    break;
                case 4:
                    message.isMarket = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? exchange_1.DerivativeOrder.fromJSON(object.order) : undefined,
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            isMarket: isSet(object.isMarket) ? Boolean(object.isMarket) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? exchange_1.DerivativeOrder.toJSON(message.order) : undefined);
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.isMarket !== undefined && (obj.isMarket = message.isMarket);
        return obj;
    },
    create: function (base) {
        return exports.EventNewConditionalDerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventNewConditionalDerivativeOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.order = (object.order !== undefined && object.order !== null)
            ? exchange_1.DerivativeOrder.fromPartial(object.order)
            : undefined;
        message.hash = (_b = object.hash) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.isMarket = (_c = object.isMarket) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseEventCancelConditionalDerivativeOrder() {
    return { marketId: "", isLimitCancel: false, limitOrder: undefined, marketOrder: undefined };
}
exports.EventCancelConditionalDerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isLimitCancel === true) {
            writer.uint32(16).bool(message.isLimitCancel);
        }
        if (message.limitOrder !== undefined) {
            exchange_1.DerivativeLimitOrder.encode(message.limitOrder, writer.uint32(26).fork()).ldelim();
        }
        if (message.marketOrder !== undefined) {
            exchange_1.DerivativeMarketOrder.encode(message.marketOrder, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventCancelConditionalDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isLimitCancel = reader.bool();
                    break;
                case 3:
                    message.limitOrder = exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.marketOrder = exchange_1.DerivativeMarketOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isLimitCancel: isSet(object.isLimitCancel) ? Boolean(object.isLimitCancel) : false,
            limitOrder: isSet(object.limitOrder) ? exchange_1.DerivativeLimitOrder.fromJSON(object.limitOrder) : undefined,
            marketOrder: isSet(object.marketOrder) ? exchange_1.DerivativeMarketOrder.fromJSON(object.marketOrder) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isLimitCancel !== undefined && (obj.isLimitCancel = message.isLimitCancel);
        message.limitOrder !== undefined &&
            (obj.limitOrder = message.limitOrder ? exchange_1.DerivativeLimitOrder.toJSON(message.limitOrder) : undefined);
        message.marketOrder !== undefined &&
            (obj.marketOrder = message.marketOrder ? exchange_1.DerivativeMarketOrder.toJSON(message.marketOrder) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventCancelConditionalDerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventCancelConditionalDerivativeOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isLimitCancel = (_b = object.isLimitCancel) !== null && _b !== void 0 ? _b : false;
        message.limitOrder = (object.limitOrder !== undefined && object.limitOrder !== null)
            ? exchange_1.DerivativeLimitOrder.fromPartial(object.limitOrder)
            : undefined;
        message.marketOrder = (object.marketOrder !== undefined && object.marketOrder !== null)
            ? exchange_1.DerivativeMarketOrder.fromPartial(object.marketOrder)
            : undefined;
        return message;
    },
};
function createBaseEventConditionalDerivativeOrderTrigger() {
    return {
        marketId: new Uint8Array(),
        isLimitTrigger: false,
        triggeredOrderHash: new Uint8Array(),
        placedOrderHash: new Uint8Array(),
        triggeredOrderCid: "",
    };
}
exports.EventConditionalDerivativeOrderTrigger = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId.length !== 0) {
            writer.uint32(10).bytes(message.marketId);
        }
        if (message.isLimitTrigger === true) {
            writer.uint32(16).bool(message.isLimitTrigger);
        }
        if (message.triggeredOrderHash.length !== 0) {
            writer.uint32(26).bytes(message.triggeredOrderHash);
        }
        if (message.placedOrderHash.length !== 0) {
            writer.uint32(34).bytes(message.placedOrderHash);
        }
        if (message.triggeredOrderCid !== "") {
            writer.uint32(42).string(message.triggeredOrderCid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventConditionalDerivativeOrderTrigger();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.bytes();
                    break;
                case 2:
                    message.isLimitTrigger = reader.bool();
                    break;
                case 3:
                    message.triggeredOrderHash = reader.bytes();
                    break;
                case 4:
                    message.placedOrderHash = reader.bytes();
                    break;
                case 5:
                    message.triggeredOrderCid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? bytesFromBase64(object.marketId) : new Uint8Array(),
            isLimitTrigger: isSet(object.isLimitTrigger) ? Boolean(object.isLimitTrigger) : false,
            triggeredOrderHash: isSet(object.triggeredOrderHash)
                ? bytesFromBase64(object.triggeredOrderHash)
                : new Uint8Array(),
            placedOrderHash: isSet(object.placedOrderHash) ? bytesFromBase64(object.placedOrderHash) : new Uint8Array(),
            triggeredOrderCid: isSet(object.triggeredOrderCid) ? String(object.triggeredOrderCid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined &&
            (obj.marketId = base64FromBytes(message.marketId !== undefined ? message.marketId : new Uint8Array()));
        message.isLimitTrigger !== undefined && (obj.isLimitTrigger = message.isLimitTrigger);
        message.triggeredOrderHash !== undefined &&
            (obj.triggeredOrderHash = base64FromBytes(message.triggeredOrderHash !== undefined ? message.triggeredOrderHash : new Uint8Array()));
        message.placedOrderHash !== undefined &&
            (obj.placedOrderHash = base64FromBytes(message.placedOrderHash !== undefined ? message.placedOrderHash : new Uint8Array()));
        message.triggeredOrderCid !== undefined && (obj.triggeredOrderCid = message.triggeredOrderCid);
        return obj;
    },
    create: function (base) {
        return exports.EventConditionalDerivativeOrderTrigger.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventConditionalDerivativeOrderTrigger();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.isLimitTrigger = (_b = object.isLimitTrigger) !== null && _b !== void 0 ? _b : false;
        message.triggeredOrderHash = (_c = object.triggeredOrderHash) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.placedOrderHash = (_d = object.placedOrderHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.triggeredOrderCid = (_e = object.triggeredOrderCid) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseEventOrderFail() {
    return { account: new Uint8Array(), hashes: [], flags: [], cids: [] };
}
exports.EventOrderFail = {
    encode: function (message, writer) {
        var e_11, _a, e_12, _b, e_13, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account.length !== 0) {
            writer.uint32(10).bytes(message.account);
        }
        try {
            for (var _d = __values(message.hashes), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(18).bytes(v);
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_11) throw e_11.error; }
        }
        writer.uint32(26).fork();
        try {
            for (var _f = __values(message.flags), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(v);
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_12) throw e_12.error; }
        }
        writer.ldelim();
        try {
            for (var _h = __values(message.cids), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOrderFail();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.bytes();
                    break;
                case 2:
                    message.hashes.push(reader.bytes());
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.flags.push(reader.uint32());
                        }
                    }
                    else {
                        message.flags.push(reader.uint32());
                    }
                    break;
                case 4:
                    message.cids.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? bytesFromBase64(object.account) : new Uint8Array(),
            hashes: Array.isArray(object === null || object === void 0 ? void 0 : object.hashes) ? object.hashes.map(function (e) { return bytesFromBase64(e); }) : [],
            flags: Array.isArray(object === null || object === void 0 ? void 0 : object.flags) ? object.flags.map(function (e) { return Number(e); }) : [],
            cids: Array.isArray(object === null || object === void 0 ? void 0 : object.cids) ? object.cids.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined &&
            (obj.account = base64FromBytes(message.account !== undefined ? message.account : new Uint8Array()));
        if (message.hashes) {
            obj.hashes = message.hashes.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.hashes = [];
        }
        if (message.flags) {
            obj.flags = message.flags.map(function (e) { return Math.round(e); });
        }
        else {
            obj.flags = [];
        }
        if (message.cids) {
            obj.cids = message.cids.map(function (e) { return e; });
        }
        else {
            obj.cids = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventOrderFail.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventOrderFail();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.hashes = ((_b = object.hashes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.flags = ((_c = object.flags) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.cids = ((_d = object.cids) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseEventAtomicMarketOrderFeeMultipliersUpdated() {
    return { marketFeeMultipliers: [] };
}
exports.EventAtomicMarketOrderFeeMultipliersUpdated = {
    encode: function (message, writer) {
        var e_14, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.marketFeeMultipliers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.MarketFeeMultiplier.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAtomicMarketOrderFeeMultipliersUpdated();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketFeeMultipliers.push(exchange_1.MarketFeeMultiplier.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketFeeMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.marketFeeMultipliers)
                ? object.marketFeeMultipliers.map(function (e) { return exchange_1.MarketFeeMultiplier.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.marketFeeMultipliers) {
            obj.marketFeeMultipliers = message.marketFeeMultipliers.map(function (e) { return e ? exchange_1.MarketFeeMultiplier.toJSON(e) : undefined; });
        }
        else {
            obj.marketFeeMultipliers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventAtomicMarketOrderFeeMultipliersUpdated.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventAtomicMarketOrderFeeMultipliersUpdated();
        message.marketFeeMultipliers = ((_a = object.marketFeeMultipliers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.MarketFeeMultiplier.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventOrderbookUpdate() {
    return { spotUpdates: [], derivativeUpdates: [] };
}
exports.EventOrderbookUpdate = {
    encode: function (message, writer) {
        var e_15, _a, e_16, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.spotUpdates), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.OrderbookUpdate.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_15) throw e_15.error; }
        }
        try {
            for (var _e = __values(message.derivativeUpdates), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.OrderbookUpdate.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOrderbookUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.spotUpdates.push(exports.OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.derivativeUpdates.push(exports.OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            spotUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.spotUpdates)
                ? object.spotUpdates.map(function (e) { return exports.OrderbookUpdate.fromJSON(e); })
                : [],
            derivativeUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeUpdates)
                ? object.derivativeUpdates.map(function (e) { return exports.OrderbookUpdate.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.spotUpdates) {
            obj.spotUpdates = message.spotUpdates.map(function (e) { return e ? exports.OrderbookUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.spotUpdates = [];
        }
        if (message.derivativeUpdates) {
            obj.derivativeUpdates = message.derivativeUpdates.map(function (e) { return e ? exports.OrderbookUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeUpdates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventOrderbookUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventOrderbookUpdate();
        message.spotUpdates = ((_a = object.spotUpdates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.OrderbookUpdate.fromPartial(e); })) || [];
        message.derivativeUpdates = ((_b = object.derivativeUpdates) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.OrderbookUpdate.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOrderbookUpdate() {
    return { seq: "0", orderbook: undefined };
}
exports.OrderbookUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.seq !== "0") {
            writer.uint32(8).uint64(message.seq);
        }
        if (message.orderbook !== undefined) {
            exports.Orderbook.encode(message.orderbook, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbookUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.seq = longToString(reader.uint64());
                    break;
                case 2:
                    message.orderbook = exports.Orderbook.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            seq: isSet(object.seq) ? String(object.seq) : "0",
            orderbook: isSet(object.orderbook) ? exports.Orderbook.fromJSON(object.orderbook) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.seq !== undefined && (obj.seq = message.seq);
        message.orderbook !== undefined &&
            (obj.orderbook = message.orderbook ? exports.Orderbook.toJSON(message.orderbook) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.OrderbookUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOrderbookUpdate();
        message.seq = (_a = object.seq) !== null && _a !== void 0 ? _a : "0";
        message.orderbook = (object.orderbook !== undefined && object.orderbook !== null)
            ? exports.Orderbook.fromPartial(object.orderbook)
            : undefined;
        return message;
    },
};
function createBaseOrderbook() {
    return { marketId: new Uint8Array(), buyLevels: [], sellLevels: [] };
}
exports.Orderbook = {
    encode: function (message, writer) {
        var e_17, _a, e_18, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId.length !== 0) {
            writer.uint32(10).bytes(message.marketId);
        }
        try {
            for (var _c = __values(message.buyLevels), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.Level.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_17) throw e_17.error; }
        }
        try {
            for (var _e = __values(message.sellLevels), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.Level.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_18) throw e_18.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.bytes();
                    break;
                case 2:
                    message.buyLevels.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.sellLevels.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? bytesFromBase64(object.marketId) : new Uint8Array(),
            buyLevels: Array.isArray(object === null || object === void 0 ? void 0 : object.buyLevels) ? object.buyLevels.map(function (e) { return exchange_1.Level.fromJSON(e); }) : [],
            sellLevels: Array.isArray(object === null || object === void 0 ? void 0 : object.sellLevels) ? object.sellLevels.map(function (e) { return exchange_1.Level.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined &&
            (obj.marketId = base64FromBytes(message.marketId !== undefined ? message.marketId : new Uint8Array()));
        if (message.buyLevels) {
            obj.buyLevels = message.buyLevels.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.buyLevels = [];
        }
        if (message.sellLevels) {
            obj.sellLevels = message.sellLevels.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.sellLevels = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Orderbook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseOrderbook();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.buyLevels = ((_b = object.buyLevels) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        message.sellLevels = ((_c = object.sellLevels) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventGrantAuthorizations() {
    return { granter: "", grants: [] };
}
exports.EventGrantAuthorizations = {
    encode: function (message, writer) {
        var e_19, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        try {
            for (var _b = __values(message.grants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.GrantAuthorization.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_19) throw e_19.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventGrantAuthorizations();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                case 2:
                    message.grants.push(exchange_1.GrantAuthorization.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            granter: isSet(object.granter) ? String(object.granter) : "",
            grants: Array.isArray(object === null || object === void 0 ? void 0 : object.grants) ? object.grants.map(function (e) { return exchange_1.GrantAuthorization.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        if (message.grants) {
            obj.grants = message.grants.map(function (e) { return e ? exchange_1.GrantAuthorization.toJSON(e) : undefined; });
        }
        else {
            obj.grants = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventGrantAuthorizations.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventGrantAuthorizations();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        message.grants = ((_b = object.grants) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.GrantAuthorization.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventGrantActivation() {
    return { grantee: "", granter: "", amount: "" };
}
exports.EventGrantActivation = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grantee !== "") {
            writer.uint32(10).string(message.grantee);
        }
        if (message.granter !== "") {
            writer.uint32(18).string(message.granter);
        }
        if (message.amount !== "") {
            writer.uint32(26).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventGrantActivation();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grantee = reader.string();
                    break;
                case 2:
                    message.granter = reader.string();
                    break;
                case 3:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            grantee: isSet(object.grantee) ? String(object.grantee) : "",
            granter: isSet(object.granter) ? String(object.granter) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.grantee !== undefined && (obj.grantee = message.grantee);
        message.granter !== undefined && (obj.granter = message.granter);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.EventGrantActivation.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventGrantActivation();
        message.grantee = (_a = object.grantee) !== null && _a !== void 0 ? _a : "";
        message.granter = (_b = object.granter) !== null && _b !== void 0 ? _b : "";
        message.amount = (_c = object.amount) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseEventInvalidGrant() {
    return { grantee: "", granter: "" };
}
exports.EventInvalidGrant = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grantee !== "") {
            writer.uint32(10).string(message.grantee);
        }
        if (message.granter !== "") {
            writer.uint32(18).string(message.granter);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventInvalidGrant();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grantee = reader.string();
                    break;
                case 2:
                    message.granter = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            grantee: isSet(object.grantee) ? String(object.grantee) : "",
            granter: isSet(object.granter) ? String(object.granter) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.grantee !== undefined && (obj.grantee = message.grantee);
        message.granter !== undefined && (obj.granter = message.granter);
        return obj;
    },
    create: function (base) {
        return exports.EventInvalidGrant.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventInvalidGrant();
        message.grantee = (_a = object.grantee) !== null && _a !== void 0 ? _a : "";
        message.granter = (_b = object.granter) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEventOrderCancelFail() {
    return { marketId: "", subaccountId: "", orderHash: "", cid: "", description: "" };
}
exports.EventOrderCancelFail = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.orderHash !== "") {
            writer.uint32(26).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(34).string(message.cid);
        }
        if (message.description !== "") {
            writer.uint32(42).string(message.description);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOrderCancelFail();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.orderHash = reader.string();
                    break;
                case 4:
                    message.cid = reader.string();
                    break;
                case 5:
                    message.description = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            description: isSet(object.description) ? String(object.description) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        message.description !== undefined && (obj.description = message.description);
        return obj;
    },
    create: function (base) {
        return exports.EventOrderCancelFail.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseEventOrderCancelFail();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.orderHash = (_c = object.orderHash) !== null && _c !== void 0 ? _c : "";
        message.cid = (_d = object.cid) !== null && _d !== void 0 ? _d : "";
        message.description = (_e = object.description) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
