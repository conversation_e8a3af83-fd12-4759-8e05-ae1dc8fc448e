import _m0 from "protobufjs/minimal.js";
import { Height } from "../../../core/client/v1/client";
export declare const protobufPackage = "ibc.lightclients.wasm.v1";
/** Wasm light client's Client state */
export interface ClientState {
    /**
     * bytes encoding the client state of the underlying light client
     * implemented as a Wasm contract.
     */
    data: Uint8Array;
    checksum: Uint8Array;
    latestHeight: Height | undefined;
}
/** Wasm light client's ConsensusState */
export interface ConsensusState {
    /**
     * bytes encoding the consensus state of the underlying light client
     * implemented as a Wasm contract.
     */
    data: Uint8Array;
}
/** Wasm light client message (either header(s) or misbehaviour) */
export interface ClientMessage {
    data: Uint8Array;
}
/**
 * Checksums defines a list of all checksums that are stored
 *
 * Deprecated: This message is deprecated in favor of storing the checksums
 * using a Collections.KeySet.
 *
 * @deprecated
 */
export interface Checksums {
    checksums: Uint8Array[];
}
export declare const ClientState: {
    encode(message: <PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ClientState;
    fromJSON(object: any): ClientState;
    toJSON(message: ClientState): unknown;
    create(base?: DeepPartial<ClientState>): ClientState;
    fromPartial(object: DeepPartial<ClientState>): ClientState;
};
export declare const ConsensusState: {
    encode(message: ConsensusState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConsensusState;
    fromJSON(object: any): ConsensusState;
    toJSON(message: ConsensusState): unknown;
    create(base?: DeepPartial<ConsensusState>): ConsensusState;
    fromPartial(object: DeepPartial<ConsensusState>): ConsensusState;
};
export declare const ClientMessage: {
    encode(message: ClientMessage, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ClientMessage;
    fromJSON(object: any): ClientMessage;
    toJSON(message: ClientMessage): unknown;
    create(base?: DeepPartial<ClientMessage>): ClientMessage;
    fromPartial(object: DeepPartial<ClientMessage>): ClientMessage;
};
export declare const Checksums: {
    encode(message: Checksums, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Checksums;
    fromJSON(object: any): Checksums;
    toJSON(message: Checksums): unknown;
    create(base?: DeepPartial<Checksums>): Checksums;
    fromPartial(object: DeepPartial<Checksums>): Checksums;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
