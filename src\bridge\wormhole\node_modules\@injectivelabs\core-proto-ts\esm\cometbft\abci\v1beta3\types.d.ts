import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { ConsensusParams } from "../../types/v1/params";
import { BlockIDFlag } from "../../types/v1beta1/validator";
import { RequestApplySnapshotChunk, RequestCheckTx, RequestCommit, RequestEcho, RequestFlush, RequestListSnapshots, RequestLoadSnapshotChunk, RequestOfferSnapshot, RequestQuery, ResponseApplySnapshotChunk, ResponseEcho, ResponseException, ResponseFlush, ResponseInfo, ResponseListSnapshots, ResponseLoadSnapshotChunk, ResponseOfferSnapshot, ResponseQuery, Validator, ValidatorUpdate } from "../v1beta1/types";
import { Event, Misbehavior, RequestInfo, ResponsePrepareProposal, ResponseProcessProposal } from "../v1beta2/types";
export declare const protobufPackage = "cometbft.abci.v1beta3";
/** Request represents a request to the ABCI application. */
export interface Request {
    echo?: RequestEcho | undefined;
    flush?: RequestFlush | undefined;
    info?: RequestInfo | undefined;
    initChain?: RequestInitChain | undefined;
    query?: RequestQuery | undefined;
    checkTx?: RequestCheckTx | undefined;
    commit?: RequestCommit | undefined;
    listSnapshots?: RequestListSnapshots | undefined;
    offerSnapshot?: RequestOfferSnapshot | undefined;
    loadSnapshotChunk?: RequestLoadSnapshotChunk | undefined;
    applySnapshotChunk?: RequestApplySnapshotChunk | undefined;
    prepareProposal?: RequestPrepareProposal | undefined;
    processProposal?: RequestProcessProposal | undefined;
    extendVote?: RequestExtendVote | undefined;
    verifyVoteExtension?: RequestVerifyVoteExtension | undefined;
    finalizeBlock?: RequestFinalizeBlock | undefined;
}
/** RequestInitChain is a request to initialize the blockchain. */
export interface RequestInitChain {
    time: Date | undefined;
    chainId: string;
    consensusParams: ConsensusParams | undefined;
    validators: ValidatorUpdate[];
    appStateBytes: Uint8Array;
    initialHeight: string;
}
/**
 * RequestPrepareProposal is a request for the ABCI application to prepare a new
 * block proposal.
 */
export interface RequestPrepareProposal {
    /** the modified transactions cannot exceed this size. */
    maxTxBytes: string;
    /**
     * txs is an array of transactions that will be included in a block,
     * sent to the app for possible modifications.
     */
    txs: Uint8Array[];
    localLastCommit: ExtendedCommitInfo | undefined;
    misbehavior: Misbehavior[];
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the validator proposing the block. */
    proposerAddress: Uint8Array;
}
/** RequestProcessProposal is a request for the ABCI application to process proposal. */
export interface RequestProcessProposal {
    txs: Uint8Array[];
    proposedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    /** hash is the merkle root hash of the fields of the proposed block. */
    hash: Uint8Array;
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
}
/** Extends a vote with application-injected data */
export interface RequestExtendVote {
    /** the hash of the block that this vote may be referring to */
    hash: Uint8Array;
    /** the height of the extended vote */
    height: string;
    /** info of the block that this vote may be referring to */
    time: Date | undefined;
    txs: Uint8Array[];
    proposedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
}
/** Verify the vote extension */
export interface RequestVerifyVoteExtension {
    /** the hash of the block that this received vote corresponds to */
    hash: Uint8Array;
    /** the validator that signed the vote extension */
    validatorAddress: Uint8Array;
    height: string;
    voteExtension: Uint8Array;
}
/** RequestFinalizeBlock is a request to finalize the block. */
export interface RequestFinalizeBlock {
    txs: Uint8Array[];
    decidedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    /** hash is the merkle root hash of the fields of the decided block. */
    hash: Uint8Array;
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** proposer_address is the address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
}
/** Response represents a response from the ABCI application. */
export interface Response {
    exception?: ResponseException | undefined;
    echo?: ResponseEcho | undefined;
    flush?: ResponseFlush | undefined;
    info?: ResponseInfo | undefined;
    initChain?: ResponseInitChain | undefined;
    query?: ResponseQuery | undefined;
    checkTx?: ResponseCheckTx | undefined;
    commit?: ResponseCommit | undefined;
    listSnapshots?: ResponseListSnapshots | undefined;
    offerSnapshot?: ResponseOfferSnapshot | undefined;
    loadSnapshotChunk?: ResponseLoadSnapshotChunk | undefined;
    applySnapshotChunk?: ResponseApplySnapshotChunk | undefined;
    prepareProposal?: ResponsePrepareProposal | undefined;
    processProposal?: ResponseProcessProposal | undefined;
    extendVote?: ResponseExtendVote | undefined;
    verifyVoteExtension?: ResponseVerifyVoteExtension | undefined;
    finalizeBlock?: ResponseFinalizeBlock | undefined;
}
/**
 * ResponseInitChain contains the ABCI application's hash and updates to the
 * validator set and/or the consensus params, if any.
 */
export interface ResponseInitChain {
    consensusParams: ConsensusParams | undefined;
    validators: ValidatorUpdate[];
    appHash: Uint8Array;
}
/**
 * ResponseCheckTx shows if the transaction was deemed valid by the ABCI
 * application.
 */
export interface ResponseCheckTx {
    code: number;
    data: Uint8Array;
    /** nondeterministic */
    log: string;
    /** nondeterministic */
    info: string;
    gasWanted: string;
    gasUsed: string;
    events: Event[];
    codespace: string;
}
/** ResponseCommit indicates how much blocks should CometBFT retain. */
export interface ResponseCommit {
    retainHeight: string;
}
/** ResponseExtendVote is the result of extending a vote with application-injected data. */
export interface ResponseExtendVote {
    voteExtension: Uint8Array;
}
/** ResponseVerifyVoteExtension is the result of verifying a vote extension. */
export interface ResponseVerifyVoteExtension {
    status: ResponseVerifyVoteExtension_VerifyStatus;
}
/** Verification status. */
export declare enum ResponseVerifyVoteExtension_VerifyStatus {
    /** UNKNOWN - Unknown */
    UNKNOWN = 0,
    /** ACCEPT - Accepted */
    ACCEPT = 1,
    /**
     * REJECT - Rejecting the vote extension will reject the entire precommit by the sender.
     * Incorrectly implementing this thus has liveness implications as it may affect
     * CometBFT's ability to receive 2/3+ valid votes to finalize the block.
     * Honest nodes should never be rejected.
     */
    REJECT = 2,
    UNRECOGNIZED = -1
}
export declare function responseVerifyVoteExtension_VerifyStatusFromJSON(object: any): ResponseVerifyVoteExtension_VerifyStatus;
export declare function responseVerifyVoteExtension_VerifyStatusToJSON(object: ResponseVerifyVoteExtension_VerifyStatus): string;
/** FinalizeBlockResponse contains the result of executing the block. */
export interface ResponseFinalizeBlock {
    /** set of block events emitted as part of executing the block */
    events: Event[];
    /**
     * the result of executing each transaction including the events
     * the particular transaction emitted. This should match the order
     * of the transactions delivered in the block itself
     */
    txResults: ExecTxResult[];
    /** a list of updates to the validator set. These will reflect the validator set at current height + 2. */
    validatorUpdates: ValidatorUpdate[];
    /** updates to the consensus params, if any. */
    consensusParamUpdates: ConsensusParams | undefined;
    /**
     * app_hash is the hash of the applications' state which is used to confirm
     * that execution of the transactions was deterministic.
     * It is up to the application to decide which algorithm to use.
     */
    appHash: Uint8Array;
}
/** VoteInfo contains the information about the vote. */
export interface VoteInfo {
    validator: Validator | undefined;
    blockIdFlag: BlockIDFlag;
}
/** ExtendedVoteInfo extends VoteInfo with the vote extensions (non-deterministic). */
export interface ExtendedVoteInfo {
    /** The validator that sent the vote. */
    validator: Validator | undefined;
    /** Non-deterministic extension provided by the sending validator's application. */
    voteExtension: Uint8Array;
    /** Vote extension signature created by CometBFT */
    extensionSignature: Uint8Array;
    /** block_id_flag indicates whether the validator voted for a block, nil, or did not vote at all */
    blockIdFlag: BlockIDFlag;
}
/** CommitInfo contains votes for the particular round. */
export interface CommitInfo {
    round: number;
    votes: VoteInfo[];
}
/**
 * ExtendedCommitInfo is similar to CommitInfo except that it is only used in
 * the PrepareProposal request such that Tendermint can provide vote extensions
 * to the application.
 */
export interface ExtendedCommitInfo {
    /** The round at which the block proposer decided in the previous height. */
    round: number;
    /**
     * List of validators' addresses in the last validator set with their voting
     * information, including vote extensions.
     */
    votes: ExtendedVoteInfo[];
}
/**
 * ExecTxResult contains results of executing one individual transaction.
 *
 * * Its structure is equivalent to #ResponseDeliverTx which will be deprecated/deleted
 */
export interface ExecTxResult {
    code: number;
    data: Uint8Array;
    /** nondeterministic */
    log: string;
    /** nondeterministic */
    info: string;
    gasWanted: string;
    gasUsed: string;
    /** nondeterministic */
    events: Event[];
    codespace: string;
}
/**
 * TxResult contains results of executing the transaction.
 *
 * One usage is indexing transaction results.
 */
export interface TxResult {
    height: string;
    index: number;
    tx: Uint8Array;
    result: ExecTxResult | undefined;
}
export declare const Request: {
    encode(message: Request, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Request;
    fromJSON(object: any): Request;
    toJSON(message: Request): unknown;
    create(base?: DeepPartial<Request>): Request;
    fromPartial(object: DeepPartial<Request>): Request;
};
export declare const RequestInitChain: {
    encode(message: RequestInitChain, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestInitChain;
    fromJSON(object: any): RequestInitChain;
    toJSON(message: RequestInitChain): unknown;
    create(base?: DeepPartial<RequestInitChain>): RequestInitChain;
    fromPartial(object: DeepPartial<RequestInitChain>): RequestInitChain;
};
export declare const RequestPrepareProposal: {
    encode(message: RequestPrepareProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestPrepareProposal;
    fromJSON(object: any): RequestPrepareProposal;
    toJSON(message: RequestPrepareProposal): unknown;
    create(base?: DeepPartial<RequestPrepareProposal>): RequestPrepareProposal;
    fromPartial(object: DeepPartial<RequestPrepareProposal>): RequestPrepareProposal;
};
export declare const RequestProcessProposal: {
    encode(message: RequestProcessProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestProcessProposal;
    fromJSON(object: any): RequestProcessProposal;
    toJSON(message: RequestProcessProposal): unknown;
    create(base?: DeepPartial<RequestProcessProposal>): RequestProcessProposal;
    fromPartial(object: DeepPartial<RequestProcessProposal>): RequestProcessProposal;
};
export declare const RequestExtendVote: {
    encode(message: RequestExtendVote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestExtendVote;
    fromJSON(object: any): RequestExtendVote;
    toJSON(message: RequestExtendVote): unknown;
    create(base?: DeepPartial<RequestExtendVote>): RequestExtendVote;
    fromPartial(object: DeepPartial<RequestExtendVote>): RequestExtendVote;
};
export declare const RequestVerifyVoteExtension: {
    encode(message: RequestVerifyVoteExtension, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestVerifyVoteExtension;
    fromJSON(object: any): RequestVerifyVoteExtension;
    toJSON(message: RequestVerifyVoteExtension): unknown;
    create(base?: DeepPartial<RequestVerifyVoteExtension>): RequestVerifyVoteExtension;
    fromPartial(object: DeepPartial<RequestVerifyVoteExtension>): RequestVerifyVoteExtension;
};
export declare const RequestFinalizeBlock: {
    encode(message: RequestFinalizeBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestFinalizeBlock;
    fromJSON(object: any): RequestFinalizeBlock;
    toJSON(message: RequestFinalizeBlock): unknown;
    create(base?: DeepPartial<RequestFinalizeBlock>): RequestFinalizeBlock;
    fromPartial(object: DeepPartial<RequestFinalizeBlock>): RequestFinalizeBlock;
};
export declare const Response: {
    encode(message: Response, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Response;
    fromJSON(object: any): Response;
    toJSON(message: Response): unknown;
    create(base?: DeepPartial<Response>): Response;
    fromPartial(object: DeepPartial<Response>): Response;
};
export declare const ResponseInitChain: {
    encode(message: ResponseInitChain, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseInitChain;
    fromJSON(object: any): ResponseInitChain;
    toJSON(message: ResponseInitChain): unknown;
    create(base?: DeepPartial<ResponseInitChain>): ResponseInitChain;
    fromPartial(object: DeepPartial<ResponseInitChain>): ResponseInitChain;
};
export declare const ResponseCheckTx: {
    encode(message: ResponseCheckTx, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseCheckTx;
    fromJSON(object: any): ResponseCheckTx;
    toJSON(message: ResponseCheckTx): unknown;
    create(base?: DeepPartial<ResponseCheckTx>): ResponseCheckTx;
    fromPartial(object: DeepPartial<ResponseCheckTx>): ResponseCheckTx;
};
export declare const ResponseCommit: {
    encode(message: ResponseCommit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseCommit;
    fromJSON(object: any): ResponseCommit;
    toJSON(message: ResponseCommit): unknown;
    create(base?: DeepPartial<ResponseCommit>): ResponseCommit;
    fromPartial(object: DeepPartial<ResponseCommit>): ResponseCommit;
};
export declare const ResponseExtendVote: {
    encode(message: ResponseExtendVote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseExtendVote;
    fromJSON(object: any): ResponseExtendVote;
    toJSON(message: ResponseExtendVote): unknown;
    create(base?: DeepPartial<ResponseExtendVote>): ResponseExtendVote;
    fromPartial(object: DeepPartial<ResponseExtendVote>): ResponseExtendVote;
};
export declare const ResponseVerifyVoteExtension: {
    encode(message: ResponseVerifyVoteExtension, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseVerifyVoteExtension;
    fromJSON(object: any): ResponseVerifyVoteExtension;
    toJSON(message: ResponseVerifyVoteExtension): unknown;
    create(base?: DeepPartial<ResponseVerifyVoteExtension>): ResponseVerifyVoteExtension;
    fromPartial(object: DeepPartial<ResponseVerifyVoteExtension>): ResponseVerifyVoteExtension;
};
export declare const ResponseFinalizeBlock: {
    encode(message: ResponseFinalizeBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseFinalizeBlock;
    fromJSON(object: any): ResponseFinalizeBlock;
    toJSON(message: ResponseFinalizeBlock): unknown;
    create(base?: DeepPartial<ResponseFinalizeBlock>): ResponseFinalizeBlock;
    fromPartial(object: DeepPartial<ResponseFinalizeBlock>): ResponseFinalizeBlock;
};
export declare const VoteInfo: {
    encode(message: VoteInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VoteInfo;
    fromJSON(object: any): VoteInfo;
    toJSON(message: VoteInfo): unknown;
    create(base?: DeepPartial<VoteInfo>): VoteInfo;
    fromPartial(object: DeepPartial<VoteInfo>): VoteInfo;
};
export declare const ExtendedVoteInfo: {
    encode(message: ExtendedVoteInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendedVoteInfo;
    fromJSON(object: any): ExtendedVoteInfo;
    toJSON(message: ExtendedVoteInfo): unknown;
    create(base?: DeepPartial<ExtendedVoteInfo>): ExtendedVoteInfo;
    fromPartial(object: DeepPartial<ExtendedVoteInfo>): ExtendedVoteInfo;
};
export declare const CommitInfo: {
    encode(message: CommitInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CommitInfo;
    fromJSON(object: any): CommitInfo;
    toJSON(message: CommitInfo): unknown;
    create(base?: DeepPartial<CommitInfo>): CommitInfo;
    fromPartial(object: DeepPartial<CommitInfo>): CommitInfo;
};
export declare const ExtendedCommitInfo: {
    encode(message: ExtendedCommitInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendedCommitInfo;
    fromJSON(object: any): ExtendedCommitInfo;
    toJSON(message: ExtendedCommitInfo): unknown;
    create(base?: DeepPartial<ExtendedCommitInfo>): ExtendedCommitInfo;
    fromPartial(object: DeepPartial<ExtendedCommitInfo>): ExtendedCommitInfo;
};
export declare const ExecTxResult: {
    encode(message: ExecTxResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExecTxResult;
    fromJSON(object: any): ExecTxResult;
    toJSON(message: ExecTxResult): unknown;
    create(base?: DeepPartial<ExecTxResult>): ExecTxResult;
    fromPartial(object: DeepPartial<ExecTxResult>): ExecTxResult;
};
export declare const TxResult: {
    encode(message: TxResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TxResult;
    fromJSON(object: any): TxResult;
    toJSON(message: TxResult): unknown;
    create(base?: DeepPartial<TxResult>): TxResult;
    fromPartial(object: DeepPartial<TxResult>): TxResult;
};
/** ABCIService is a service for an ABCI application. */
export interface ABCI {
    /** Echo returns back the same message it is sent. */
    Echo(request: DeepPartial<RequestEcho>, metadata?: grpc.Metadata): Promise<ResponseEcho>;
    /** Flush flushes the write buffer. */
    Flush(request: DeepPartial<RequestFlush>, metadata?: grpc.Metadata): Promise<ResponseFlush>;
    /** Info returns information about the application state. */
    Info(request: DeepPartial<RequestInfo>, metadata?: grpc.Metadata): Promise<ResponseInfo>;
    /** CheckTx validates a transaction. */
    CheckTx(request: DeepPartial<RequestCheckTx>, metadata?: grpc.Metadata): Promise<ResponseCheckTx>;
    /** Query queries the application state. */
    Query(request: DeepPartial<RequestQuery>, metadata?: grpc.Metadata): Promise<ResponseQuery>;
    /** Commit commits a block of transactions. */
    Commit(request: DeepPartial<RequestCommit>, metadata?: grpc.Metadata): Promise<ResponseCommit>;
    /** InitChain initializes the blockchain. */
    InitChain(request: DeepPartial<RequestInitChain>, metadata?: grpc.Metadata): Promise<ResponseInitChain>;
    /** ListSnapshots lists all the available snapshots. */
    ListSnapshots(request: DeepPartial<RequestListSnapshots>, metadata?: grpc.Metadata): Promise<ResponseListSnapshots>;
    /** OfferSnapshot sends a snapshot offer. */
    OfferSnapshot(request: DeepPartial<RequestOfferSnapshot>, metadata?: grpc.Metadata): Promise<ResponseOfferSnapshot>;
    /** LoadSnapshotChunk returns a chunk of snapshot. */
    LoadSnapshotChunk(request: DeepPartial<RequestLoadSnapshotChunk>, metadata?: grpc.Metadata): Promise<ResponseLoadSnapshotChunk>;
    /** ApplySnapshotChunk applies a chunk of snapshot. */
    ApplySnapshotChunk(request: DeepPartial<RequestApplySnapshotChunk>, metadata?: grpc.Metadata): Promise<ResponseApplySnapshotChunk>;
    /** PrepareProposal returns a proposal for the next block. */
    PrepareProposal(request: DeepPartial<RequestPrepareProposal>, metadata?: grpc.Metadata): Promise<ResponsePrepareProposal>;
    /** ProcessProposal validates a proposal. */
    ProcessProposal(request: DeepPartial<RequestProcessProposal>, metadata?: grpc.Metadata): Promise<ResponseProcessProposal>;
    /** ExtendVote extends a vote with application-injected data (vote extensions). */
    ExtendVote(request: DeepPartial<RequestExtendVote>, metadata?: grpc.Metadata): Promise<ResponseExtendVote>;
    /** VerifyVoteExtension verifies a vote extension. */
    VerifyVoteExtension(request: DeepPartial<RequestVerifyVoteExtension>, metadata?: grpc.Metadata): Promise<ResponseVerifyVoteExtension>;
    /** FinalizeBlock finalizes a block. */
    FinalizeBlock(request: DeepPartial<RequestFinalizeBlock>, metadata?: grpc.Metadata): Promise<ResponseFinalizeBlock>;
}
export declare class ABCIClientImpl implements ABCI {
    private readonly rpc;
    constructor(rpc: Rpc);
    Echo(request: DeepPartial<RequestEcho>, metadata?: grpc.Metadata): Promise<ResponseEcho>;
    Flush(request: DeepPartial<RequestFlush>, metadata?: grpc.Metadata): Promise<ResponseFlush>;
    Info(request: DeepPartial<RequestInfo>, metadata?: grpc.Metadata): Promise<ResponseInfo>;
    CheckTx(request: DeepPartial<RequestCheckTx>, metadata?: grpc.Metadata): Promise<ResponseCheckTx>;
    Query(request: DeepPartial<RequestQuery>, metadata?: grpc.Metadata): Promise<ResponseQuery>;
    Commit(request: DeepPartial<RequestCommit>, metadata?: grpc.Metadata): Promise<ResponseCommit>;
    InitChain(request: DeepPartial<RequestInitChain>, metadata?: grpc.Metadata): Promise<ResponseInitChain>;
    ListSnapshots(request: DeepPartial<RequestListSnapshots>, metadata?: grpc.Metadata): Promise<ResponseListSnapshots>;
    OfferSnapshot(request: DeepPartial<RequestOfferSnapshot>, metadata?: grpc.Metadata): Promise<ResponseOfferSnapshot>;
    LoadSnapshotChunk(request: DeepPartial<RequestLoadSnapshotChunk>, metadata?: grpc.Metadata): Promise<ResponseLoadSnapshotChunk>;
    ApplySnapshotChunk(request: DeepPartial<RequestApplySnapshotChunk>, metadata?: grpc.Metadata): Promise<ResponseApplySnapshotChunk>;
    PrepareProposal(request: DeepPartial<RequestPrepareProposal>, metadata?: grpc.Metadata): Promise<ResponsePrepareProposal>;
    ProcessProposal(request: DeepPartial<RequestProcessProposal>, metadata?: grpc.Metadata): Promise<ResponseProcessProposal>;
    ExtendVote(request: DeepPartial<RequestExtendVote>, metadata?: grpc.Metadata): Promise<ResponseExtendVote>;
    VerifyVoteExtension(request: DeepPartial<RequestVerifyVoteExtension>, metadata?: grpc.Metadata): Promise<ResponseVerifyVoteExtension>;
    FinalizeBlock(request: DeepPartial<RequestFinalizeBlock>, metadata?: grpc.Metadata): Promise<ResponseFinalizeBlock>;
}
export declare const ABCIDesc: {
    serviceName: string;
};
export declare const ABCIEchoDesc: UnaryMethodDefinitionish;
export declare const ABCIFlushDesc: UnaryMethodDefinitionish;
export declare const ABCIInfoDesc: UnaryMethodDefinitionish;
export declare const ABCICheckTxDesc: UnaryMethodDefinitionish;
export declare const ABCIQueryDesc: UnaryMethodDefinitionish;
export declare const ABCICommitDesc: UnaryMethodDefinitionish;
export declare const ABCIInitChainDesc: UnaryMethodDefinitionish;
export declare const ABCIListSnapshotsDesc: UnaryMethodDefinitionish;
export declare const ABCIOfferSnapshotDesc: UnaryMethodDefinitionish;
export declare const ABCILoadSnapshotChunkDesc: UnaryMethodDefinitionish;
export declare const ABCIApplySnapshotChunkDesc: UnaryMethodDefinitionish;
export declare const ABCIPrepareProposalDesc: UnaryMethodDefinitionish;
export declare const ABCIProcessProposalDesc: UnaryMethodDefinitionish;
export declare const ABCIExtendVoteDesc: UnaryMethodDefinitionish;
export declare const ABCIVerifyVoteExtensionDesc: UnaryMethodDefinitionish;
export declare const ABCIFinalizeBlockDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
