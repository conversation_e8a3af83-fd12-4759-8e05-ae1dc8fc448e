import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { AccountRewards, BinaryOptionsMarket, CampaignRewardPool, DepositUpdate, DerivativeLimitOrder, DerivativeMarket, DerivativeMarketOrder, DerivativeOrder, DerivativeTradeLog, ExecutionType, ExpiryFuturesMarketInfo, FeeDiscountSchedule, GrantAuthorization, Level, MarketFeeMultiplier, PerpetualMarketFunding, PerpetualMarketInfo, SpotLimitOrder, SpotMarket, SubaccountPosition, TradeLog, TradingRewardCampaignInfo } from "./exchange";
export declare const protobufPackage = "injective.exchange.v1beta1";
export interface EventBatchSpotExecution {
    marketId: string;
    isBuy: boolean;
    executionType: ExecutionType;
    trades: TradeLog[];
}
export interface EventBatchDerivativeExecution {
    marketId: string;
    isBuy: boolean;
    isLiquidation: boolean;
    /** nil for time expiry futures */
    cumulativeFunding: string;
    executionType: ExecutionType;
    trades: DerivativeTradeLog[];
}
export interface EventLostFundsFromLiquidation {
    marketId: string;
    subaccountId: Uint8Array;
    lostFundsFromAvailableDuringPayout: string;
    lostFundsFromOrderCancels: string;
}
export interface EventBatchDerivativePosition {
    marketId: string;
    positions: SubaccountPosition[];
}
export interface EventDerivativeMarketPaused {
    marketId: string;
    settlePrice: string;
    totalMissingFunds: string;
    missingFundsRate: string;
}
export interface EventSettledMarketBalance {
    marketId: string;
    amount: string;
}
export interface EventNotSettledMarketBalance {
    marketId: string;
    amount: string;
}
export interface EventMarketBeyondBankruptcy {
    marketId: string;
    settlePrice: string;
    missingMarketFunds: string;
}
export interface EventAllPositionsHaircut {
    marketId: string;
    settlePrice: string;
    missingFundsRate: string;
}
export interface EventBinaryOptionsMarketUpdate {
    market: BinaryOptionsMarket | undefined;
}
export interface EventNewSpotOrders {
    marketId: string;
    buyOrders: SpotLimitOrder[];
    sellOrders: SpotLimitOrder[];
}
export interface EventNewDerivativeOrders {
    marketId: string;
    buyOrders: DerivativeLimitOrder[];
    sellOrders: DerivativeLimitOrder[];
}
export interface EventCancelSpotOrder {
    marketId: string;
    order: SpotLimitOrder | undefined;
}
export interface EventSpotMarketUpdate {
    market: SpotMarket | undefined;
}
export interface EventPerpetualMarketUpdate {
    market: DerivativeMarket | undefined;
    perpetualMarketInfo: PerpetualMarketInfo | undefined;
    funding: PerpetualMarketFunding | undefined;
}
export interface EventExpiryFuturesMarketUpdate {
    market: DerivativeMarket | undefined;
    expiryFuturesMarketInfo: ExpiryFuturesMarketInfo | undefined;
}
export interface EventPerpetualMarketFundingUpdate {
    marketId: string;
    funding: PerpetualMarketFunding | undefined;
    isHourlyFunding: boolean;
    fundingRate: string;
    markPrice: string;
}
export interface EventSubaccountDeposit {
    srcAddress: string;
    subaccountId: Uint8Array;
    amount: Coin | undefined;
}
export interface EventSubaccountWithdraw {
    subaccountId: Uint8Array;
    dstAddress: string;
    amount: Coin | undefined;
}
export interface EventSubaccountBalanceTransfer {
    srcSubaccountId: string;
    dstSubaccountId: string;
    amount: Coin | undefined;
}
export interface EventBatchDepositUpdate {
    depositUpdates: DepositUpdate[];
}
export interface DerivativeMarketOrderCancel {
    marketOrder: DerivativeMarketOrder | undefined;
    cancelQuantity: string;
}
export interface EventCancelDerivativeOrder {
    marketId: string;
    isLimitCancel: boolean;
    limitOrder: DerivativeLimitOrder | undefined;
    marketOrderCancel: DerivativeMarketOrderCancel | undefined;
}
export interface EventFeeDiscountSchedule {
    schedule: FeeDiscountSchedule | undefined;
}
export interface EventTradingRewardCampaignUpdate {
    campaignInfo: TradingRewardCampaignInfo | undefined;
    campaignRewardPools: CampaignRewardPool[];
}
export interface EventTradingRewardDistribution {
    accountRewards: AccountRewards[];
}
export interface EventNewConditionalDerivativeOrder {
    marketId: string;
    order: DerivativeOrder | undefined;
    hash: Uint8Array;
    isMarket: boolean;
}
export interface EventCancelConditionalDerivativeOrder {
    marketId: string;
    isLimitCancel: boolean;
    limitOrder: DerivativeLimitOrder | undefined;
    marketOrder: DerivativeMarketOrder | undefined;
}
export interface EventConditionalDerivativeOrderTrigger {
    marketId: Uint8Array;
    isLimitTrigger: boolean;
    triggeredOrderHash: Uint8Array;
    placedOrderHash: Uint8Array;
    triggeredOrderCid: string;
}
export interface EventOrderFail {
    account: Uint8Array;
    hashes: Uint8Array[];
    flags: number[];
    cids: string[];
}
export interface EventAtomicMarketOrderFeeMultipliersUpdated {
    marketFeeMultipliers: MarketFeeMultiplier[];
}
export interface EventOrderbookUpdate {
    spotUpdates: OrderbookUpdate[];
    derivativeUpdates: OrderbookUpdate[];
}
export interface OrderbookUpdate {
    seq: string;
    orderbook: Orderbook | undefined;
}
export interface Orderbook {
    marketId: Uint8Array;
    buyLevels: Level[];
    sellLevels: Level[];
}
export interface EventGrantAuthorizations {
    granter: string;
    grants: GrantAuthorization[];
}
export interface EventGrantActivation {
    grantee: string;
    granter: string;
    amount: string;
}
export interface EventInvalidGrant {
    grantee: string;
    granter: string;
}
export interface EventOrderCancelFail {
    marketId: string;
    subaccountId: string;
    orderHash: string;
    cid: string;
    description: string;
}
export declare const EventBatchSpotExecution: {
    encode(message: EventBatchSpotExecution, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBatchSpotExecution;
    fromJSON(object: any): EventBatchSpotExecution;
    toJSON(message: EventBatchSpotExecution): unknown;
    create(base?: DeepPartial<EventBatchSpotExecution>): EventBatchSpotExecution;
    fromPartial(object: DeepPartial<EventBatchSpotExecution>): EventBatchSpotExecution;
};
export declare const EventBatchDerivativeExecution: {
    encode(message: EventBatchDerivativeExecution, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBatchDerivativeExecution;
    fromJSON(object: any): EventBatchDerivativeExecution;
    toJSON(message: EventBatchDerivativeExecution): unknown;
    create(base?: DeepPartial<EventBatchDerivativeExecution>): EventBatchDerivativeExecution;
    fromPartial(object: DeepPartial<EventBatchDerivativeExecution>): EventBatchDerivativeExecution;
};
export declare const EventLostFundsFromLiquidation: {
    encode(message: EventLostFundsFromLiquidation, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventLostFundsFromLiquidation;
    fromJSON(object: any): EventLostFundsFromLiquidation;
    toJSON(message: EventLostFundsFromLiquidation): unknown;
    create(base?: DeepPartial<EventLostFundsFromLiquidation>): EventLostFundsFromLiquidation;
    fromPartial(object: DeepPartial<EventLostFundsFromLiquidation>): EventLostFundsFromLiquidation;
};
export declare const EventBatchDerivativePosition: {
    encode(message: EventBatchDerivativePosition, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBatchDerivativePosition;
    fromJSON(object: any): EventBatchDerivativePosition;
    toJSON(message: EventBatchDerivativePosition): unknown;
    create(base?: DeepPartial<EventBatchDerivativePosition>): EventBatchDerivativePosition;
    fromPartial(object: DeepPartial<EventBatchDerivativePosition>): EventBatchDerivativePosition;
};
export declare const EventDerivativeMarketPaused: {
    encode(message: EventDerivativeMarketPaused, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventDerivativeMarketPaused;
    fromJSON(object: any): EventDerivativeMarketPaused;
    toJSON(message: EventDerivativeMarketPaused): unknown;
    create(base?: DeepPartial<EventDerivativeMarketPaused>): EventDerivativeMarketPaused;
    fromPartial(object: DeepPartial<EventDerivativeMarketPaused>): EventDerivativeMarketPaused;
};
export declare const EventSettledMarketBalance: {
    encode(message: EventSettledMarketBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSettledMarketBalance;
    fromJSON(object: any): EventSettledMarketBalance;
    toJSON(message: EventSettledMarketBalance): unknown;
    create(base?: DeepPartial<EventSettledMarketBalance>): EventSettledMarketBalance;
    fromPartial(object: DeepPartial<EventSettledMarketBalance>): EventSettledMarketBalance;
};
export declare const EventNotSettledMarketBalance: {
    encode(message: EventNotSettledMarketBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNotSettledMarketBalance;
    fromJSON(object: any): EventNotSettledMarketBalance;
    toJSON(message: EventNotSettledMarketBalance): unknown;
    create(base?: DeepPartial<EventNotSettledMarketBalance>): EventNotSettledMarketBalance;
    fromPartial(object: DeepPartial<EventNotSettledMarketBalance>): EventNotSettledMarketBalance;
};
export declare const EventMarketBeyondBankruptcy: {
    encode(message: EventMarketBeyondBankruptcy, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventMarketBeyondBankruptcy;
    fromJSON(object: any): EventMarketBeyondBankruptcy;
    toJSON(message: EventMarketBeyondBankruptcy): unknown;
    create(base?: DeepPartial<EventMarketBeyondBankruptcy>): EventMarketBeyondBankruptcy;
    fromPartial(object: DeepPartial<EventMarketBeyondBankruptcy>): EventMarketBeyondBankruptcy;
};
export declare const EventAllPositionsHaircut: {
    encode(message: EventAllPositionsHaircut, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAllPositionsHaircut;
    fromJSON(object: any): EventAllPositionsHaircut;
    toJSON(message: EventAllPositionsHaircut): unknown;
    create(base?: DeepPartial<EventAllPositionsHaircut>): EventAllPositionsHaircut;
    fromPartial(object: DeepPartial<EventAllPositionsHaircut>): EventAllPositionsHaircut;
};
export declare const EventBinaryOptionsMarketUpdate: {
    encode(message: EventBinaryOptionsMarketUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBinaryOptionsMarketUpdate;
    fromJSON(object: any): EventBinaryOptionsMarketUpdate;
    toJSON(message: EventBinaryOptionsMarketUpdate): unknown;
    create(base?: DeepPartial<EventBinaryOptionsMarketUpdate>): EventBinaryOptionsMarketUpdate;
    fromPartial(object: DeepPartial<EventBinaryOptionsMarketUpdate>): EventBinaryOptionsMarketUpdate;
};
export declare const EventNewSpotOrders: {
    encode(message: EventNewSpotOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNewSpotOrders;
    fromJSON(object: any): EventNewSpotOrders;
    toJSON(message: EventNewSpotOrders): unknown;
    create(base?: DeepPartial<EventNewSpotOrders>): EventNewSpotOrders;
    fromPartial(object: DeepPartial<EventNewSpotOrders>): EventNewSpotOrders;
};
export declare const EventNewDerivativeOrders: {
    encode(message: EventNewDerivativeOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNewDerivativeOrders;
    fromJSON(object: any): EventNewDerivativeOrders;
    toJSON(message: EventNewDerivativeOrders): unknown;
    create(base?: DeepPartial<EventNewDerivativeOrders>): EventNewDerivativeOrders;
    fromPartial(object: DeepPartial<EventNewDerivativeOrders>): EventNewDerivativeOrders;
};
export declare const EventCancelSpotOrder: {
    encode(message: EventCancelSpotOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventCancelSpotOrder;
    fromJSON(object: any): EventCancelSpotOrder;
    toJSON(message: EventCancelSpotOrder): unknown;
    create(base?: DeepPartial<EventCancelSpotOrder>): EventCancelSpotOrder;
    fromPartial(object: DeepPartial<EventCancelSpotOrder>): EventCancelSpotOrder;
};
export declare const EventSpotMarketUpdate: {
    encode(message: EventSpotMarketUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSpotMarketUpdate;
    fromJSON(object: any): EventSpotMarketUpdate;
    toJSON(message: EventSpotMarketUpdate): unknown;
    create(base?: DeepPartial<EventSpotMarketUpdate>): EventSpotMarketUpdate;
    fromPartial(object: DeepPartial<EventSpotMarketUpdate>): EventSpotMarketUpdate;
};
export declare const EventPerpetualMarketUpdate: {
    encode(message: EventPerpetualMarketUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventPerpetualMarketUpdate;
    fromJSON(object: any): EventPerpetualMarketUpdate;
    toJSON(message: EventPerpetualMarketUpdate): unknown;
    create(base?: DeepPartial<EventPerpetualMarketUpdate>): EventPerpetualMarketUpdate;
    fromPartial(object: DeepPartial<EventPerpetualMarketUpdate>): EventPerpetualMarketUpdate;
};
export declare const EventExpiryFuturesMarketUpdate: {
    encode(message: EventExpiryFuturesMarketUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventExpiryFuturesMarketUpdate;
    fromJSON(object: any): EventExpiryFuturesMarketUpdate;
    toJSON(message: EventExpiryFuturesMarketUpdate): unknown;
    create(base?: DeepPartial<EventExpiryFuturesMarketUpdate>): EventExpiryFuturesMarketUpdate;
    fromPartial(object: DeepPartial<EventExpiryFuturesMarketUpdate>): EventExpiryFuturesMarketUpdate;
};
export declare const EventPerpetualMarketFundingUpdate: {
    encode(message: EventPerpetualMarketFundingUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventPerpetualMarketFundingUpdate;
    fromJSON(object: any): EventPerpetualMarketFundingUpdate;
    toJSON(message: EventPerpetualMarketFundingUpdate): unknown;
    create(base?: DeepPartial<EventPerpetualMarketFundingUpdate>): EventPerpetualMarketFundingUpdate;
    fromPartial(object: DeepPartial<EventPerpetualMarketFundingUpdate>): EventPerpetualMarketFundingUpdate;
};
export declare const EventSubaccountDeposit: {
    encode(message: EventSubaccountDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSubaccountDeposit;
    fromJSON(object: any): EventSubaccountDeposit;
    toJSON(message: EventSubaccountDeposit): unknown;
    create(base?: DeepPartial<EventSubaccountDeposit>): EventSubaccountDeposit;
    fromPartial(object: DeepPartial<EventSubaccountDeposit>): EventSubaccountDeposit;
};
export declare const EventSubaccountWithdraw: {
    encode(message: EventSubaccountWithdraw, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSubaccountWithdraw;
    fromJSON(object: any): EventSubaccountWithdraw;
    toJSON(message: EventSubaccountWithdraw): unknown;
    create(base?: DeepPartial<EventSubaccountWithdraw>): EventSubaccountWithdraw;
    fromPartial(object: DeepPartial<EventSubaccountWithdraw>): EventSubaccountWithdraw;
};
export declare const EventSubaccountBalanceTransfer: {
    encode(message: EventSubaccountBalanceTransfer, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSubaccountBalanceTransfer;
    fromJSON(object: any): EventSubaccountBalanceTransfer;
    toJSON(message: EventSubaccountBalanceTransfer): unknown;
    create(base?: DeepPartial<EventSubaccountBalanceTransfer>): EventSubaccountBalanceTransfer;
    fromPartial(object: DeepPartial<EventSubaccountBalanceTransfer>): EventSubaccountBalanceTransfer;
};
export declare const EventBatchDepositUpdate: {
    encode(message: EventBatchDepositUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBatchDepositUpdate;
    fromJSON(object: any): EventBatchDepositUpdate;
    toJSON(message: EventBatchDepositUpdate): unknown;
    create(base?: DeepPartial<EventBatchDepositUpdate>): EventBatchDepositUpdate;
    fromPartial(object: DeepPartial<EventBatchDepositUpdate>): EventBatchDepositUpdate;
};
export declare const DerivativeMarketOrderCancel: {
    encode(message: DerivativeMarketOrderCancel, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketOrderCancel;
    fromJSON(object: any): DerivativeMarketOrderCancel;
    toJSON(message: DerivativeMarketOrderCancel): unknown;
    create(base?: DeepPartial<DerivativeMarketOrderCancel>): DerivativeMarketOrderCancel;
    fromPartial(object: DeepPartial<DerivativeMarketOrderCancel>): DerivativeMarketOrderCancel;
};
export declare const EventCancelDerivativeOrder: {
    encode(message: EventCancelDerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventCancelDerivativeOrder;
    fromJSON(object: any): EventCancelDerivativeOrder;
    toJSON(message: EventCancelDerivativeOrder): unknown;
    create(base?: DeepPartial<EventCancelDerivativeOrder>): EventCancelDerivativeOrder;
    fromPartial(object: DeepPartial<EventCancelDerivativeOrder>): EventCancelDerivativeOrder;
};
export declare const EventFeeDiscountSchedule: {
    encode(message: EventFeeDiscountSchedule, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventFeeDiscountSchedule;
    fromJSON(object: any): EventFeeDiscountSchedule;
    toJSON(message: EventFeeDiscountSchedule): unknown;
    create(base?: DeepPartial<EventFeeDiscountSchedule>): EventFeeDiscountSchedule;
    fromPartial(object: DeepPartial<EventFeeDiscountSchedule>): EventFeeDiscountSchedule;
};
export declare const EventTradingRewardCampaignUpdate: {
    encode(message: EventTradingRewardCampaignUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventTradingRewardCampaignUpdate;
    fromJSON(object: any): EventTradingRewardCampaignUpdate;
    toJSON(message: EventTradingRewardCampaignUpdate): unknown;
    create(base?: DeepPartial<EventTradingRewardCampaignUpdate>): EventTradingRewardCampaignUpdate;
    fromPartial(object: DeepPartial<EventTradingRewardCampaignUpdate>): EventTradingRewardCampaignUpdate;
};
export declare const EventTradingRewardDistribution: {
    encode(message: EventTradingRewardDistribution, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventTradingRewardDistribution;
    fromJSON(object: any): EventTradingRewardDistribution;
    toJSON(message: EventTradingRewardDistribution): unknown;
    create(base?: DeepPartial<EventTradingRewardDistribution>): EventTradingRewardDistribution;
    fromPartial(object: DeepPartial<EventTradingRewardDistribution>): EventTradingRewardDistribution;
};
export declare const EventNewConditionalDerivativeOrder: {
    encode(message: EventNewConditionalDerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventNewConditionalDerivativeOrder;
    fromJSON(object: any): EventNewConditionalDerivativeOrder;
    toJSON(message: EventNewConditionalDerivativeOrder): unknown;
    create(base?: DeepPartial<EventNewConditionalDerivativeOrder>): EventNewConditionalDerivativeOrder;
    fromPartial(object: DeepPartial<EventNewConditionalDerivativeOrder>): EventNewConditionalDerivativeOrder;
};
export declare const EventCancelConditionalDerivativeOrder: {
    encode(message: EventCancelConditionalDerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventCancelConditionalDerivativeOrder;
    fromJSON(object: any): EventCancelConditionalDerivativeOrder;
    toJSON(message: EventCancelConditionalDerivativeOrder): unknown;
    create(base?: DeepPartial<EventCancelConditionalDerivativeOrder>): EventCancelConditionalDerivativeOrder;
    fromPartial(object: DeepPartial<EventCancelConditionalDerivativeOrder>): EventCancelConditionalDerivativeOrder;
};
export declare const EventConditionalDerivativeOrderTrigger: {
    encode(message: EventConditionalDerivativeOrderTrigger, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventConditionalDerivativeOrderTrigger;
    fromJSON(object: any): EventConditionalDerivativeOrderTrigger;
    toJSON(message: EventConditionalDerivativeOrderTrigger): unknown;
    create(base?: DeepPartial<EventConditionalDerivativeOrderTrigger>): EventConditionalDerivativeOrderTrigger;
    fromPartial(object: DeepPartial<EventConditionalDerivativeOrderTrigger>): EventConditionalDerivativeOrderTrigger;
};
export declare const EventOrderFail: {
    encode(message: EventOrderFail, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOrderFail;
    fromJSON(object: any): EventOrderFail;
    toJSON(message: EventOrderFail): unknown;
    create(base?: DeepPartial<EventOrderFail>): EventOrderFail;
    fromPartial(object: DeepPartial<EventOrderFail>): EventOrderFail;
};
export declare const EventAtomicMarketOrderFeeMultipliersUpdated: {
    encode(message: EventAtomicMarketOrderFeeMultipliersUpdated, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAtomicMarketOrderFeeMultipliersUpdated;
    fromJSON(object: any): EventAtomicMarketOrderFeeMultipliersUpdated;
    toJSON(message: EventAtomicMarketOrderFeeMultipliersUpdated): unknown;
    create(base?: DeepPartial<EventAtomicMarketOrderFeeMultipliersUpdated>): EventAtomicMarketOrderFeeMultipliersUpdated;
    fromPartial(object: DeepPartial<EventAtomicMarketOrderFeeMultipliersUpdated>): EventAtomicMarketOrderFeeMultipliersUpdated;
};
export declare const EventOrderbookUpdate: {
    encode(message: EventOrderbookUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOrderbookUpdate;
    fromJSON(object: any): EventOrderbookUpdate;
    toJSON(message: EventOrderbookUpdate): unknown;
    create(base?: DeepPartial<EventOrderbookUpdate>): EventOrderbookUpdate;
    fromPartial(object: DeepPartial<EventOrderbookUpdate>): EventOrderbookUpdate;
};
export declare const OrderbookUpdate: {
    encode(message: OrderbookUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderbookUpdate;
    fromJSON(object: any): OrderbookUpdate;
    toJSON(message: OrderbookUpdate): unknown;
    create(base?: DeepPartial<OrderbookUpdate>): OrderbookUpdate;
    fromPartial(object: DeepPartial<OrderbookUpdate>): OrderbookUpdate;
};
export declare const Orderbook: {
    encode(message: Orderbook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Orderbook;
    fromJSON(object: any): Orderbook;
    toJSON(message: Orderbook): unknown;
    create(base?: DeepPartial<Orderbook>): Orderbook;
    fromPartial(object: DeepPartial<Orderbook>): Orderbook;
};
export declare const EventGrantAuthorizations: {
    encode(message: EventGrantAuthorizations, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventGrantAuthorizations;
    fromJSON(object: any): EventGrantAuthorizations;
    toJSON(message: EventGrantAuthorizations): unknown;
    create(base?: DeepPartial<EventGrantAuthorizations>): EventGrantAuthorizations;
    fromPartial(object: DeepPartial<EventGrantAuthorizations>): EventGrantAuthorizations;
};
export declare const EventGrantActivation: {
    encode(message: EventGrantActivation, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventGrantActivation;
    fromJSON(object: any): EventGrantActivation;
    toJSON(message: EventGrantActivation): unknown;
    create(base?: DeepPartial<EventGrantActivation>): EventGrantActivation;
    fromPartial(object: DeepPartial<EventGrantActivation>): EventGrantActivation;
};
export declare const EventInvalidGrant: {
    encode(message: EventInvalidGrant, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventInvalidGrant;
    fromJSON(object: any): EventInvalidGrant;
    toJSON(message: EventInvalidGrant): unknown;
    create(base?: DeepPartial<EventInvalidGrant>): EventInvalidGrant;
    fromPartial(object: DeepPartial<EventInvalidGrant>): EventInvalidGrant;
};
export declare const EventOrderCancelFail: {
    encode(message: EventOrderCancelFail, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOrderCancelFail;
    fromJSON(object: any): EventOrderCancelFail;
    toJSON(message: EventOrderCancelFail): unknown;
    create(base?: DeepPartial<EventOrderCancelFail>): EventOrderCancelFail;
    fromPartial(object: DeepPartial<EventOrderCancelFail>): EventOrderCancelFail;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
