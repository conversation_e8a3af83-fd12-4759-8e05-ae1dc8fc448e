"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.HealthGetStatusDesc = exports.HealthDesc = exports.HealthClientImpl = exports.HealthStatus = exports.GetStatusResponse = exports.GetStatusRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "api.v1";
function createBaseGetStatusRequest() {
    return {};
}
exports.GetStatusRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetStatusRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.GetStatusRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBaseGetStatusRequest();
        return message;
    },
};
function createBaseGetStatusResponse() {
    return { s: "", errmsg: "", data: undefined, status: "" };
}
exports.GetStatusResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.s !== "") {
            writer.uint32(10).string(message.s);
        }
        if (message.errmsg !== "") {
            writer.uint32(18).string(message.errmsg);
        }
        if (message.data !== undefined) {
            exports.HealthStatus.encode(message.data, writer.uint32(26).fork()).ldelim();
        }
        if (message.status !== "") {
            writer.uint32(34).string(message.status);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetStatusResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.s = reader.string();
                    break;
                case 2:
                    message.errmsg = reader.string();
                    break;
                case 3:
                    message.data = exports.HealthStatus.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.status = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            s: isSet(object.s) ? String(object.s) : "",
            errmsg: isSet(object.errmsg) ? String(object.errmsg) : "",
            data: isSet(object.data) ? exports.HealthStatus.fromJSON(object.data) : undefined,
            status: isSet(object.status) ? String(object.status) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.s !== undefined && (obj.s = message.s);
        message.errmsg !== undefined && (obj.errmsg = message.errmsg);
        message.data !== undefined && (obj.data = message.data ? exports.HealthStatus.toJSON(message.data) : undefined);
        message.status !== undefined && (obj.status = message.status);
        return obj;
    },
    create(base) {
        return exports.GetStatusResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseGetStatusResponse();
        message.s = (_a = object.s) !== null && _a !== void 0 ? _a : "";
        message.errmsg = (_b = object.errmsg) !== null && _b !== void 0 ? _b : "";
        message.data = (object.data !== undefined && object.data !== null)
            ? exports.HealthStatus.fromPartial(object.data)
            : undefined;
        message.status = (_c = object.status) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseHealthStatus() {
    return {
        localHeight: 0,
        localTimestamp: 0,
        horacleHeight: 0,
        horacleTimestamp: 0,
        migrationLastVersion: 0,
        epHeight: 0,
        epTimestamp: 0,
    };
}
exports.HealthStatus = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.localHeight !== 0) {
            writer.uint32(8).sint32(message.localHeight);
        }
        if (message.localTimestamp !== 0) {
            writer.uint32(16).sint32(message.localTimestamp);
        }
        if (message.horacleHeight !== 0) {
            writer.uint32(24).sint32(message.horacleHeight);
        }
        if (message.horacleTimestamp !== 0) {
            writer.uint32(32).sint32(message.horacleTimestamp);
        }
        if (message.migrationLastVersion !== 0) {
            writer.uint32(40).sint32(message.migrationLastVersion);
        }
        if (message.epHeight !== 0) {
            writer.uint32(48).sint32(message.epHeight);
        }
        if (message.epTimestamp !== 0) {
            writer.uint32(56).sint32(message.epTimestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHealthStatus();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.localHeight = reader.sint32();
                    break;
                case 2:
                    message.localTimestamp = reader.sint32();
                    break;
                case 3:
                    message.horacleHeight = reader.sint32();
                    break;
                case 4:
                    message.horacleTimestamp = reader.sint32();
                    break;
                case 5:
                    message.migrationLastVersion = reader.sint32();
                    break;
                case 6:
                    message.epHeight = reader.sint32();
                    break;
                case 7:
                    message.epTimestamp = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            localHeight: isSet(object.localHeight) ? Number(object.localHeight) : 0,
            localTimestamp: isSet(object.localTimestamp) ? Number(object.localTimestamp) : 0,
            horacleHeight: isSet(object.horacleHeight) ? Number(object.horacleHeight) : 0,
            horacleTimestamp: isSet(object.horacleTimestamp) ? Number(object.horacleTimestamp) : 0,
            migrationLastVersion: isSet(object.migrationLastVersion) ? Number(object.migrationLastVersion) : 0,
            epHeight: isSet(object.epHeight) ? Number(object.epHeight) : 0,
            epTimestamp: isSet(object.epTimestamp) ? Number(object.epTimestamp) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.localHeight !== undefined && (obj.localHeight = Math.round(message.localHeight));
        message.localTimestamp !== undefined && (obj.localTimestamp = Math.round(message.localTimestamp));
        message.horacleHeight !== undefined && (obj.horacleHeight = Math.round(message.horacleHeight));
        message.horacleTimestamp !== undefined && (obj.horacleTimestamp = Math.round(message.horacleTimestamp));
        message.migrationLastVersion !== undefined && (obj.migrationLastVersion = Math.round(message.migrationLastVersion));
        message.epHeight !== undefined && (obj.epHeight = Math.round(message.epHeight));
        message.epTimestamp !== undefined && (obj.epTimestamp = Math.round(message.epTimestamp));
        return obj;
    },
    create(base) {
        return exports.HealthStatus.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBaseHealthStatus();
        message.localHeight = (_a = object.localHeight) !== null && _a !== void 0 ? _a : 0;
        message.localTimestamp = (_b = object.localTimestamp) !== null && _b !== void 0 ? _b : 0;
        message.horacleHeight = (_c = object.horacleHeight) !== null && _c !== void 0 ? _c : 0;
        message.horacleTimestamp = (_d = object.horacleTimestamp) !== null && _d !== void 0 ? _d : 0;
        message.migrationLastVersion = (_e = object.migrationLastVersion) !== null && _e !== void 0 ? _e : 0;
        message.epHeight = (_f = object.epHeight) !== null && _f !== void 0 ? _f : 0;
        message.epTimestamp = (_g = object.epTimestamp) !== null && _g !== void 0 ? _g : 0;
        return message;
    },
};
class HealthClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.GetStatus = this.GetStatus.bind(this);
    }
    GetStatus(request, metadata) {
        return this.rpc.unary(exports.HealthGetStatusDesc, exports.GetStatusRequest.fromPartial(request), metadata);
    }
}
exports.HealthClientImpl = HealthClientImpl;
exports.HealthDesc = { serviceName: "api.v1.Health" };
exports.HealthGetStatusDesc = {
    methodName: "GetStatus",
    service: exports.HealthDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.GetStatusRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.GetStatusResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
