import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cosmos.authz.module.v1";
/** <PERSON>du<PERSON> is the config object of the authz module. */
export interface Module {
}
export declare const Module: {
    encode(_: <PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Module;
    fromJSON(_: any): Module;
    toJSON(_: Module): unknown;
    create(base?: DeepPartial<Module>): Module;
    fromPartial(_: DeepPartial<Module>): Module;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
