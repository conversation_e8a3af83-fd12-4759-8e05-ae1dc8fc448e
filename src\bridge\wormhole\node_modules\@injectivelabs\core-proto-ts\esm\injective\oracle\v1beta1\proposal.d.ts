import _m0 from "protobufjs/minimal.js";
import { BandIBCParams, BandOracleRequest } from "./oracle";
export declare const protobufPackage = "injective.oracle.v1beta1";
export interface GrantBandOraclePrivilegeProposal {
    title: string;
    description: string;
    relayers: string[];
}
export interface RevokeBandOraclePrivilegeProposal {
    title: string;
    description: string;
    relayers: string[];
}
export interface GrantPriceFeederPrivilegeProposal {
    title: string;
    description: string;
    base: string;
    quote: string;
    relayers: string[];
}
export interface GrantProviderPrivilegeProposal {
    title: string;
    description: string;
    provider: string;
    relayers: string[];
}
export interface RevokeProviderPrivilegeProposal {
    title: string;
    description: string;
    provider: string;
    relayers: string[];
}
export interface RevokePriceFeederPrivilegeProposal {
    title: string;
    description: string;
    base: string;
    quote: string;
    relayers: string[];
}
export interface AuthorizeBandOracleRequestProposal {
    title: string;
    description: string;
    request: BandOracleRequest | undefined;
}
export interface UpdateBandOracleRequestProposal {
    title: string;
    description: string;
    deleteRequestIds: string[];
    updateOracleRequest: BandOracleRequest | undefined;
}
export interface EnableBandIBCProposal {
    title: string;
    description: string;
    bandIbcParams: BandIBCParams | undefined;
}
export interface GrantStorkPublisherPrivilegeProposal {
    title: string;
    description: string;
    storkPublishers: string[];
}
export interface RevokeStorkPublisherPrivilegeProposal {
    title: string;
    description: string;
    storkPublishers: string[];
}
export declare const GrantBandOraclePrivilegeProposal: {
    encode(message: GrantBandOraclePrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GrantBandOraclePrivilegeProposal;
    fromJSON(object: any): GrantBandOraclePrivilegeProposal;
    toJSON(message: GrantBandOraclePrivilegeProposal): unknown;
    create(base?: DeepPartial<GrantBandOraclePrivilegeProposal>): GrantBandOraclePrivilegeProposal;
    fromPartial(object: DeepPartial<GrantBandOraclePrivilegeProposal>): GrantBandOraclePrivilegeProposal;
};
export declare const RevokeBandOraclePrivilegeProposal: {
    encode(message: RevokeBandOraclePrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RevokeBandOraclePrivilegeProposal;
    fromJSON(object: any): RevokeBandOraclePrivilegeProposal;
    toJSON(message: RevokeBandOraclePrivilegeProposal): unknown;
    create(base?: DeepPartial<RevokeBandOraclePrivilegeProposal>): RevokeBandOraclePrivilegeProposal;
    fromPartial(object: DeepPartial<RevokeBandOraclePrivilegeProposal>): RevokeBandOraclePrivilegeProposal;
};
export declare const GrantPriceFeederPrivilegeProposal: {
    encode(message: GrantPriceFeederPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GrantPriceFeederPrivilegeProposal;
    fromJSON(object: any): GrantPriceFeederPrivilegeProposal;
    toJSON(message: GrantPriceFeederPrivilegeProposal): unknown;
    create(base?: DeepPartial<GrantPriceFeederPrivilegeProposal>): GrantPriceFeederPrivilegeProposal;
    fromPartial(object: DeepPartial<GrantPriceFeederPrivilegeProposal>): GrantPriceFeederPrivilegeProposal;
};
export declare const GrantProviderPrivilegeProposal: {
    encode(message: GrantProviderPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GrantProviderPrivilegeProposal;
    fromJSON(object: any): GrantProviderPrivilegeProposal;
    toJSON(message: GrantProviderPrivilegeProposal): unknown;
    create(base?: DeepPartial<GrantProviderPrivilegeProposal>): GrantProviderPrivilegeProposal;
    fromPartial(object: DeepPartial<GrantProviderPrivilegeProposal>): GrantProviderPrivilegeProposal;
};
export declare const RevokeProviderPrivilegeProposal: {
    encode(message: RevokeProviderPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RevokeProviderPrivilegeProposal;
    fromJSON(object: any): RevokeProviderPrivilegeProposal;
    toJSON(message: RevokeProviderPrivilegeProposal): unknown;
    create(base?: DeepPartial<RevokeProviderPrivilegeProposal>): RevokeProviderPrivilegeProposal;
    fromPartial(object: DeepPartial<RevokeProviderPrivilegeProposal>): RevokeProviderPrivilegeProposal;
};
export declare const RevokePriceFeederPrivilegeProposal: {
    encode(message: RevokePriceFeederPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RevokePriceFeederPrivilegeProposal;
    fromJSON(object: any): RevokePriceFeederPrivilegeProposal;
    toJSON(message: RevokePriceFeederPrivilegeProposal): unknown;
    create(base?: DeepPartial<RevokePriceFeederPrivilegeProposal>): RevokePriceFeederPrivilegeProposal;
    fromPartial(object: DeepPartial<RevokePriceFeederPrivilegeProposal>): RevokePriceFeederPrivilegeProposal;
};
export declare const AuthorizeBandOracleRequestProposal: {
    encode(message: AuthorizeBandOracleRequestProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuthorizeBandOracleRequestProposal;
    fromJSON(object: any): AuthorizeBandOracleRequestProposal;
    toJSON(message: AuthorizeBandOracleRequestProposal): unknown;
    create(base?: DeepPartial<AuthorizeBandOracleRequestProposal>): AuthorizeBandOracleRequestProposal;
    fromPartial(object: DeepPartial<AuthorizeBandOracleRequestProposal>): AuthorizeBandOracleRequestProposal;
};
export declare const UpdateBandOracleRequestProposal: {
    encode(message: UpdateBandOracleRequestProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): UpdateBandOracleRequestProposal;
    fromJSON(object: any): UpdateBandOracleRequestProposal;
    toJSON(message: UpdateBandOracleRequestProposal): unknown;
    create(base?: DeepPartial<UpdateBandOracleRequestProposal>): UpdateBandOracleRequestProposal;
    fromPartial(object: DeepPartial<UpdateBandOracleRequestProposal>): UpdateBandOracleRequestProposal;
};
export declare const EnableBandIBCProposal: {
    encode(message: EnableBandIBCProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EnableBandIBCProposal;
    fromJSON(object: any): EnableBandIBCProposal;
    toJSON(message: EnableBandIBCProposal): unknown;
    create(base?: DeepPartial<EnableBandIBCProposal>): EnableBandIBCProposal;
    fromPartial(object: DeepPartial<EnableBandIBCProposal>): EnableBandIBCProposal;
};
export declare const GrantStorkPublisherPrivilegeProposal: {
    encode(message: GrantStorkPublisherPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GrantStorkPublisherPrivilegeProposal;
    fromJSON(object: any): GrantStorkPublisherPrivilegeProposal;
    toJSON(message: GrantStorkPublisherPrivilegeProposal): unknown;
    create(base?: DeepPartial<GrantStorkPublisherPrivilegeProposal>): GrantStorkPublisherPrivilegeProposal;
    fromPartial(object: DeepPartial<GrantStorkPublisherPrivilegeProposal>): GrantStorkPublisherPrivilegeProposal;
};
export declare const RevokeStorkPublisherPrivilegeProposal: {
    encode(message: RevokeStorkPublisherPrivilegeProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RevokeStorkPublisherPrivilegeProposal;
    fromJSON(object: any): RevokeStorkPublisherPrivilegeProposal;
    toJSON(message: RevokeStorkPublisherPrivilegeProposal): unknown;
    create(base?: DeepPartial<RevokeStorkPublisherPrivilegeProposal>): RevokeStorkPublisherPrivilegeProposal;
    fromPartial(object: DeepPartial<RevokeStorkPublisherPrivilegeProposal>): RevokeStorkPublisherPrivilegeProposal;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
