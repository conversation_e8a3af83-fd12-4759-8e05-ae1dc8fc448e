#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取以太坊gas价格的多种方法
支持多种不同的API和方法来获取当前gas价格
强制使用SOCKS5代理
"""

import os
import sys
import json
import yaml
import asyncio
import aiohttp
import logging
import requests
import argparse
import random
from web3 import Web3
from typing import Dict, Any, Optional
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from web3.providers import HTTPProvider
from web3.middleware import geth_poa_middleware

# Windows平台异步支持
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ETHGasTracker")

def load_config() -> Dict:
    """加载配置文件"""
    try:
        config_path = os.path.join("config", "config.yaml")
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return {}

def get_proxy_config() -> Dict[str, str]:
    """
    获取代理配置，强制使用SOCKS5代理
    
    Returns:
        Dict[str, str]: 代理配置字典
    """
    config = load_config()
    proxy_list = config.get("proxy", {}).get("proxy_list", [])
    if not proxy_list:
        raise ValueError("配置文件中未找到代理配置")
    
    # 随机选择一个代理
    proxy_url = random.choice(proxy_list)
    return {
        "http": proxy_url,
        "https": proxy_url
    }

class ProxyHTTPProvider(HTTPProvider):
    """支持代理的Web3 HTTP Provider"""
    
    def __init__(self, endpoint_uri: str):
        super().__init__(endpoint_uri)
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        # 强制使用SOCKS5代理
        proxy_config = get_proxy_config()
        session.proxies = proxy_config
        
        self._request_kwargs = {
            'timeout': 10,
            'proxies': proxy_config
        }
        self.session = session

    def make_request(self, method: str, params: list) -> Dict:
        """重写make_request方法来使用session"""
        try:
            if not hasattr(self, 'session'):
                return super().make_request(method, params)
                
            kwargs = self._request_kwargs.copy()
            headers = self.get_request_headers()
            data = self.encode_rpc_request(method, params)
            response = self.session.post(
                self.endpoint_uri,
                data=data,
                headers=headers,
                **kwargs
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise requests.exceptions.RequestException(
                    f"HTTP {response.status_code}: {response.text}"
                )
        except Exception as e:
            logger.error(f"代理请求失败: {str(e)}")
            raise

async def get_gas_web3() -> Optional[float]:
    """
    方法1: 使用Web3直接从以太坊节点获取gas价格
    """
    try:
        config = load_config()
        rpc_url = config.get("dex", {}).get("ethereum", {}).get("rpc_url")
        if not rpc_url:
            logger.error("未找到以太坊RPC URL")
            return None
            
        provider = ProxyHTTPProvider(rpc_url)
        w3 = Web3(provider)
        
        # 添加POA中间件以支持更多网络
        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        if not w3.is_connected():
            logger.error("无法连接到以太坊节点")
            return None
            
        gas_price = w3.eth.gas_price
        return float(w3.from_wei(gas_price, 'gwei'))
    except Exception as e:
        logger.error(f"使用Web3获取gas价格时出错: {str(e)}")
        return None

async def get_gas_web3_backup() -> Optional[float]:
    """
    方法2: 使用备用RPC节点获取gas价格
    """
    try:
        config = load_config()
        backup_urls = config.get("dex", {}).get("ethereum", {}).get("backup_rpc_urls", [])
        
        for url in backup_urls:
            try:
                provider = ProxyHTTPProvider(url)
                w3 = Web3(provider)
                # 添加POA中间件以支持更多网络
                w3.middleware_onion.inject(geth_poa_middleware, layer=0)
                
                if w3.is_connected():
                    gas_price = w3.eth.gas_price
                    return float(w3.from_wei(gas_price, 'gwei'))
            except:
                continue
                
        logger.error("所有备用RPC节点都无法连接")
        return None
    except Exception as e:
        logger.error(f"使用备用RPC获取gas价格时出错: {str(e)}")
        return None

async def get_gas_etherscan() -> Optional[float]:
    """
    方法3: 使用Etherscan API获取gas价格
    """
    try:
        config = load_config()
        api_key = config.get("api_keys", {}).get("etherscan", {}).get("key")
        if not api_key:
            logger.error("未找到Etherscan API密钥")
            return None
            
        url = f"https://api.etherscan.io/api?module=gastracker&action=gasoracle&apikey={api_key}"
        
        # 获取代理配置
        proxy_config = get_proxy_config()
        
        async with aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(ssl=False),
            timeout=aiohttp.ClientTimeout(total=10)
        ) as session:
            async with session.get(url, proxy=proxy_config["http"]) as response:
                if response.status == 200:
                    data = await response.json()
                    if data["status"] == "1" and "result" in data:
                        return float(data["result"]["SafeGasPrice"])
                    else:
                        logger.error(f"Etherscan API返回错误: {data.get('message')}")
                        return None
                else:
                    logger.error(f"Etherscan API请求失败: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"从Etherscan获取gas价格时出错: {str(e)}")
        return None

async def get_gas_all() -> Dict[str, Optional[float]]:
    """
    获取所有方法的gas价格
    """
    tasks = [
        get_gas_web3(),
        get_gas_web3_backup(),
        get_gas_etherscan()
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return {
        "Web3": results[0] if not isinstance(results[0], Exception) else None,
        "Web3 Backup": results[1] if not isinstance(results[1], Exception) else None,
        "Etherscan": results[2] if not isinstance(results[2], Exception) else None
    }

def print_results(results: Dict[str, Optional[float]]) -> None:
    """打印结果"""
    print("\n" + "=" * 50)
    print(f"以太坊Gas价格 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 过滤掉异常值（与平均值相差太大的值）
    valid_prices = [price for price in results.values() if price is not None]
    if valid_prices:
        avg = sum(valid_prices) / len(valid_prices)
        # 过滤掉与平均值相差超过30%的值
        filtered_prices = [p for p in valid_prices if abs(p - avg) / avg <= 0.3]
        avg_price = sum(filtered_prices) / len(filtered_prices) if filtered_prices else None
    else:
        avg_price = None
    
    for source, price in results.items():
        if price is not None:
            print(f"{source:15}: {price:.2f} Gwei")
        else:
            print(f"{source:15}: 获取失败")
            
    if avg_price is not None:
        print("-" * 50)
        print(f"{'平均价格':15}: {avg_price:.2f} Gwei")
    print("=" * 50 + "\n")

async def main(method: str = "all") -> None:
    """
    主函数
    
    Args:
        method: 获取gas价格的方法
    """
    method = method.lower()
    
    if method == "all":
        results = await get_gas_all()
        print_results(results)
    else:
        result = None
        method_map = {
            "web3": get_gas_web3,
            "web3_backup": get_gas_web3_backup,
            "etherscan": get_gas_etherscan
        }
        
        if method in method_map:
            result = await method_map[method]()
        else:
            print(f"不支持的方法: {method}")
            return
            
        if result is not None:
            print(f"\n{method.capitalize()} Gas价格: {result:.2f} Gwei\n")
        else:
            print(f"\n从{method.capitalize()}获取Gas价格失败\n")

async def get_current_gas_price() -> Optional[float]:
    """
    获取当前gas价格的简单方法，优先使用Etherscan，失败后依次尝试其他方法
    
    Returns:
        float | None: gas价格（单位：Gwei），如果所有方法都失败则返回None
    """
    # 1. 首先尝试Etherscan
    try:
        price = await get_gas_etherscan()
        if price is not None:
            return price
    except:
        pass
        
    # 2. 尝试Web3
    try:
        price = await get_gas_web3()
        if price is not None:
            return price
    except:
        pass
        
    # 3. 最后尝试Web3 Backup
    try:
        price = await get_gas_web3_backup()
        if price is not None:
            return price
    except:
        pass
        
    return None

# 同步版本的方法，方便其他脚本调用
def get_gas_price() -> Optional[float]:
    """
    获取当前gas价格的同步方法
    
    Returns:
        float | None: gas价格（单位：Gwei），如果所有方法都失败则返回None
    
    Example:
        >>> from scripts.utils.get_gas import get_gas_price
        >>> gas_price = get_gas_price()
        >>> if gas_price is not None:
        >>>     print(f"当前gas价格: {gas_price:.2f} Gwei")
        >>> else:
        >>>     print("获取gas价格失败")
    """
    return asyncio.run(get_current_gas_price())

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="获取以太坊Gas价格的工具")
    parser.add_argument(
        "--method",
        type=str,
        choices=["web3", "web3_backup", "etherscan", "all"],
        default="all",
        help="选择获取gas价格的方法"
    )
    
    args = parser.parse_args()
    
    # 运行异步主函数
    asyncio.run(main(args.method)) 