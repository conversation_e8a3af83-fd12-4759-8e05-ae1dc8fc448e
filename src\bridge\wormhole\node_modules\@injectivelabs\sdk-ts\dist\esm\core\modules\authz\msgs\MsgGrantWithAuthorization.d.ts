import { MsgBase } from '../../MsgBase.js';
import { CosmosAuthzV1Beta1Tx, CosmosAuthzV1Beta1Authz } from '@injectivelabs/core-proto-ts';
import { BaseAuthorization } from './authorizations/Base.js';
export declare namespace MsgGrantWithAuthorization {
    interface Params {
        authorization: BaseAuthorization<unknown, unknown, unknown>;
        grantee: string;
        granter: string;
        expiration?: number;
        expiryInYears?: number;
        expiryInSeconds?: number;
    }
    type Proto = CosmosAuthzV1Beta1Tx.MsgGrant;
    type Object = Omit<CosmosAuthzV1Beta1Tx.MsgGrant, 'msgs'> & {
        msgs: any;
    };
}
/**
 * @category Messages
 */
export default class MsgGrantWithAuthorization extends MsgBase<MsgGrantWithAuthorization.Params, MsgGrantWithAuthorization.Proto> {
    static fromJSON(params: MsgGrantWithAuthorization.Params): MsgGrantWithAuthorization;
    toProto(): CosmosAuthzV1Beta1Tx.MsgGrant;
    toData(): {
        granter: string;
        grantee: string;
        grant: CosmosAuthzV1Beta1Authz.Grant | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: MsgGrantWithAuthorization.Object;
    };
    toDirectSign(): {
        type: string;
        message: CosmosAuthzV1Beta1Tx.MsgGrant;
    };
    toWeb3Gw(): {
        granter: string;
        grantee: string;
        grant: {
            authorization: unknown;
            expiration: string;
        };
        '@type': string;
    };
    toEip712(): never;
    private getTimestamp;
    toBinary(): Uint8Array;
}
