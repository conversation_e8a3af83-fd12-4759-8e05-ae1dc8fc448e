"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StargateClient = void 0;
const stargate_1 = require("@cosmjs/stargate");
const AccountParser_js_1 = require("../accounts/AccountParser.js");
class StargateClient extends stargate_1.StargateClient {
    async getAccount(searchAddress) {
        try {
            const chainId = await this.getChainId();
            const isInjective = chainId.startsWith('injective');
            const account = await this.forceGetQueryClient().auth.account(searchAddress);
            if (!account) {
                return null;
            }
            if (isInjective) {
                return (0, AccountParser_js_1.accountParser)(account);
            }
            return (0, stargate_1.accountFromAny)(account);
        }
        catch (error) {
            if (/rpc error: code = NotFound/i.test(error.toString())) {
                return null;
            }
            throw error;
        }
    }
}
exports.StargateClient = StargateClient;
