"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventContractDeregistered = exports.EventContractRegistered = exports.EventContractExecution = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var proposal_1 = require("./proposal.js");
exports.protobufPackage = "injective.wasmx.v1";
function createBaseEventContractExecution() {
    return { contractAddress: "", response: new Uint8Array(), otherError: "", executionError: "" };
}
exports.EventContractExecution = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        if (message.response.length !== 0) {
            writer.uint32(18).bytes(message.response);
        }
        if (message.otherError !== "") {
            writer.uint32(26).string(message.otherError);
        }
        if (message.executionError !== "") {
            writer.uint32(34).string(message.executionError);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventContractExecution();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                case 2:
                    message.response = reader.bytes();
                    break;
                case 3:
                    message.otherError = reader.string();
                    break;
                case 4:
                    message.executionError = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            response: isSet(object.response) ? bytesFromBase64(object.response) : new Uint8Array(),
            otherError: isSet(object.otherError) ? String(object.otherError) : "",
            executionError: isSet(object.executionError) ? String(object.executionError) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.response !== undefined &&
            (obj.response = base64FromBytes(message.response !== undefined ? message.response : new Uint8Array()));
        message.otherError !== undefined && (obj.otherError = message.otherError);
        message.executionError !== undefined && (obj.executionError = message.executionError);
        return obj;
    },
    create: function (base) {
        return exports.EventContractExecution.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEventContractExecution();
        message.contractAddress = (_a = object.contractAddress) !== null && _a !== void 0 ? _a : "";
        message.response = (_b = object.response) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.otherError = (_c = object.otherError) !== null && _c !== void 0 ? _c : "";
        message.executionError = (_d = object.executionError) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseEventContractRegistered() {
    return {
        contractAddress: "",
        gasPrice: "0",
        shouldPinContract: false,
        isMigrationAllowed: false,
        codeId: "0",
        adminAddress: "",
        granterAddress: "",
        fundingMode: 0,
    };
}
exports.EventContractRegistered = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        if (message.gasPrice !== "0") {
            writer.uint32(24).uint64(message.gasPrice);
        }
        if (message.shouldPinContract === true) {
            writer.uint32(32).bool(message.shouldPinContract);
        }
        if (message.isMigrationAllowed === true) {
            writer.uint32(40).bool(message.isMigrationAllowed);
        }
        if (message.codeId !== "0") {
            writer.uint32(48).uint64(message.codeId);
        }
        if (message.adminAddress !== "") {
            writer.uint32(58).string(message.adminAddress);
        }
        if (message.granterAddress !== "") {
            writer.uint32(66).string(message.granterAddress);
        }
        if (message.fundingMode !== 0) {
            writer.uint32(72).int32(message.fundingMode);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventContractRegistered();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                case 3:
                    message.gasPrice = longToString(reader.uint64());
                    break;
                case 4:
                    message.shouldPinContract = reader.bool();
                    break;
                case 5:
                    message.isMigrationAllowed = reader.bool();
                    break;
                case 6:
                    message.codeId = longToString(reader.uint64());
                    break;
                case 7:
                    message.adminAddress = reader.string();
                    break;
                case 8:
                    message.granterAddress = reader.string();
                    break;
                case 9:
                    message.fundingMode = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            gasPrice: isSet(object.gasPrice) ? String(object.gasPrice) : "0",
            shouldPinContract: isSet(object.shouldPinContract) ? Boolean(object.shouldPinContract) : false,
            isMigrationAllowed: isSet(object.isMigrationAllowed) ? Boolean(object.isMigrationAllowed) : false,
            codeId: isSet(object.codeId) ? String(object.codeId) : "0",
            adminAddress: isSet(object.adminAddress) ? String(object.adminAddress) : "",
            granterAddress: isSet(object.granterAddress) ? String(object.granterAddress) : "",
            fundingMode: isSet(object.fundingMode) ? (0, proposal_1.fundingModeFromJSON)(object.fundingMode) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.gasPrice !== undefined && (obj.gasPrice = message.gasPrice);
        message.shouldPinContract !== undefined && (obj.shouldPinContract = message.shouldPinContract);
        message.isMigrationAllowed !== undefined && (obj.isMigrationAllowed = message.isMigrationAllowed);
        message.codeId !== undefined && (obj.codeId = message.codeId);
        message.adminAddress !== undefined && (obj.adminAddress = message.adminAddress);
        message.granterAddress !== undefined && (obj.granterAddress = message.granterAddress);
        message.fundingMode !== undefined && (obj.fundingMode = (0, proposal_1.fundingModeToJSON)(message.fundingMode));
        return obj;
    },
    create: function (base) {
        return exports.EventContractRegistered.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseEventContractRegistered();
        message.contractAddress = (_a = object.contractAddress) !== null && _a !== void 0 ? _a : "";
        message.gasPrice = (_b = object.gasPrice) !== null && _b !== void 0 ? _b : "0";
        message.shouldPinContract = (_c = object.shouldPinContract) !== null && _c !== void 0 ? _c : false;
        message.isMigrationAllowed = (_d = object.isMigrationAllowed) !== null && _d !== void 0 ? _d : false;
        message.codeId = (_e = object.codeId) !== null && _e !== void 0 ? _e : "0";
        message.adminAddress = (_f = object.adminAddress) !== null && _f !== void 0 ? _f : "";
        message.granterAddress = (_g = object.granterAddress) !== null && _g !== void 0 ? _g : "";
        message.fundingMode = (_h = object.fundingMode) !== null && _h !== void 0 ? _h : 0;
        return message;
    },
};
function createBaseEventContractDeregistered() {
    return { contractAddress: "" };
}
exports.EventContractDeregistered = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventContractDeregistered();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create: function (base) {
        return exports.EventContractDeregistered.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventContractDeregistered();
        message.contractAddress = (_a = object.contractAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
