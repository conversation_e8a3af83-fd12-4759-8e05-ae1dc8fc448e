import { A as Account, j as SingleKeySignerOrLegacyEd25519Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { HexInput, SigningScheme } from '../types/types.mjs';
import { AccountAddress, AccountAddressInput } from '../core/accountAddress.mjs';
import { P as PublicKey } from '../publicKey-CJOcUwJK.mjs';
import { AccountAuthenticatorMultiKey } from '../transactions/authenticator/account.mjs';
import { AnyRawTransaction } from '../transactions/types.mjs';
import { KeylessSigner } from './AbstractKeylessAccount.mjs';
import { AptosConfig } from '../api/aptosConfig.mjs';
import { MultiKeySignature, MultiKey } from '../core/crypto/multiKey.mjs';
import '../core/crypto/signature.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../core/crypto/ed25519.mjs';
import '../bcs/deserializer.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import './EphemeralKeyPair.mjs';
import '../core/crypto/ephemeral.mjs';
import '../federatedKeyless-DAYXjY2Y.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';
import '../utils/const.mjs';

/**
 * Arguments required to verify a multi-key signature against a given message.
 *
 * @param message - The original message that was signed.
 * @param signature - The multi-key signature to be verified.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface VerifyMultiKeySignatureArgs {
    message: HexInput;
    signature: MultiKeySignature;
}
/**
 * Signer implementation for the MultiKey authentication scheme.
 *
 * This account utilizes an M of N signing scheme, where M and N are specified in the {@link MultiKey}.
 * It signs messages using an array of M accounts, each corresponding to a public key in the {@link MultiKey}.
 *
 * Note: Generating a signer instance does not create the account on-chain.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare class MultiKeyAccount implements Account, KeylessSigner {
    /**
     * Public key associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly publicKey: MultiKey;
    /**
     * Account address associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly accountAddress: AccountAddress;
    /**
     * Signing scheme used to sign transactions
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signingScheme: SigningScheme;
    /**
     * The signers used to sign messages.  These signers should correspond to public keys in the
     * MultiKeyAccount's public key.  The number of signers should be equal to this.publicKey.signaturesRequired.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signers: Account[];
    /**
     * An array of indices where for signer[i], signerIndicies[i] is the index of the corresponding public key in
     * publicKey.publicKeys.  Used to derive the right public key to use for verification.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signerIndicies: number[];
    readonly signaturesBitmap: Uint8Array;
    /**
     * Constructs a MultiKeyAccount instance, which requires multiple signatures for transactions.
     *
     * @param args - The arguments for creating a MultiKeyAccount.
     * @param args.multiKey - The multikey of the account consisting of N public keys and a number M representing the required signatures.
     * @param args.signers - An array of M signers that will be used to sign the transaction.
     * @param args.address - An optional account address input. If not provided, the derived address from the public key will be used.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    constructor(args: {
        multiKey: MultiKey;
        signers: SingleKeySignerOrLegacyEd25519Account[];
        address?: AccountAddressInput;
    });
    /**
     * Static constructor to create a MultiKeyAccount using the provided public keys and signers.
     *
     * @param args - The arguments for creating a MultiKeyAccount.
     * @param args.publicKeys - The N public keys of the MultiKeyAccount.
     * @param args.signaturesRequired - The number of signatures required to authorize a transaction.
     * @param args.signers - An array of M signers that will be used to sign the transaction.
     * @returns MultiKeyAccount - The newly created MultiKeyAccount.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static fromPublicKeysAndSigners(args: {
        address?: AccountAddressInput;
        publicKeys: PublicKey[];
        signaturesRequired: number;
        signers: SingleKeySignerOrLegacyEd25519Account[];
    }): MultiKeyAccount;
    /**
     * Determines if the provided account is a multi-key account.
     *
     * @param account - The account to check.
     * @returns A boolean indicating whether the account is a multi-key account.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    static isMultiKeySigner(account: Account): account is MultiKeyAccount;
    /**
     * Sign a message using the account's signers and return an AccountAuthenticator containing the signature along with the
     * account's public key.
     * @param message - The signing message, represented as binary input in hexadecimal format.
     * @returns An instance of AccountAuthenticatorMultiKey that includes the signature and the public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signWithAuthenticator(message: HexInput): AccountAuthenticatorMultiKey;
    /**
     * Sign a transaction using the account's signers, returning an AccountAuthenticator that contains the signature and the
     * account's public key.
     * @param transaction - The raw transaction to be signed.
     * @returns An AccountAuthenticatorMultiKey containing the signature of the transaction along with the account's public key.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorMultiKey;
    /**
     * Waits for any proofs on KeylessAccount signers to be fetched. This ensures that signing with the KeylessAccount does not
     * fail due to missing proofs.
     * @return {Promise<void>} A promise that resolves when all proofs have been fetched.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    waitForProofFetch(): Promise<void>;
    /**
     * Validates that the Keyless Account can be used to sign transactions.
     * @return
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    checkKeylessAccountValidity(aptosConfig: AptosConfig): Promise<void>;
    /**
     * Sign the given message using the MultiKeyAccount's signers
     * @param data - The data to be signed in HexInput format.
     * @returns MultiKeySignature
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    sign(data: HexInput): MultiKeySignature;
    /**
     * Sign the given transaction using the MultiKeyAccount's signers.
     * This function aggregates signatures from all signers associated with the MultiKeyAccount.
     *
     * @param transaction - The transaction to be signed.
     * @returns MultiKeySignature - An object containing the aggregated signatures and a bitmap of the signatures.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransaction(transaction: AnyRawTransaction): MultiKeySignature;
    /**
     * Verify the given message and signature with the public keys.
     *
     * This function checks if the provided signatures are valid for the given message using the corresponding public keys.
     * Note: If you are using KeylessAccounts, you must use `verifySignatureAsync` instead.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The raw message data in HexInput format.
     * @param args.signature - The signed message MultiKeySignature containing multiple signatures.
     * @returns A boolean indicating whether the signatures are valid for the message.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignature(args: {
        message: HexInput;
        signature: MultiKeySignature;
    }): boolean;
    /**
     * Verify the given message and signature with the public keys.
     *
     * This function checks if the provided signatures are valid for the given message using the corresponding public keys.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The raw message data in HexInput format.
     * @param args.signature - The signed message MultiKeySignature containing multiple signatures.
     * @param args.options.throwErrorWithReason - Whether to throw an error with the reason for the verification failure.
     * @returns A boolean indicating whether the signatures are valid for the message.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: MultiKeySignature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
}

export { MultiKeyAccount, type VerifyMultiKeySignatureArgs };
