"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerRestSpotChronosApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const BaseRestConsumer_js_1 = __importDefault(require("../../base/BaseRestConsumer.js"));
const index_js_1 = require("../types/index.js");
/**
 * @category Indexer Chronos API
 */
class IndexerRestSpotChronosApi extends BaseRestConsumer_js_1.default {
    async fetchMarketSummary(marketId) {
        const path = `market_summary`;
        try {
            const { data } = await this.retry(() => this.get(path, {
                marketId,
                resolution: '24h',
            }));
            return data;
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${path}?marketId=${marketId}`,
                contextModule: index_js_1.IndexerModule.ChronosSpot,
            });
        }
    }
    async fetchMarketsSummary() {
        const path = `market_summary_all`;
        try {
            const { data } = await this.retry(() => this.get(path, {
                resolution: '24h',
            }));
            return data;
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${path}`,
                contextModule: index_js_1.IndexerModule.ChronosSpot,
            });
        }
    }
}
exports.IndexerRestSpotChronosApi = IndexerRestSpotChronosApi;
