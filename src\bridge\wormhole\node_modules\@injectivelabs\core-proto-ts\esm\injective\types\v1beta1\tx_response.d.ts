import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective.types.v1beta1";
/**
 * base header ak message type, we can cast the bytes into corresponding message
 * response type
 */
export interface TxResponseGenericMessage {
    header: string;
    data: Uint8Array;
}
/** improvised message to unpack length prefixed messages in tx response data */
export interface TxResponseData {
    messages: TxResponseGenericMessage[];
}
export declare const TxResponseGenericMessage: {
    encode(message: TxResponseGenericMessage, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TxResponseGenericMessage;
    fromJSON(object: any): TxResponseGenericMessage;
    toJSON(message: TxResponseGenericMessage): unknown;
    create(base?: DeepPartial<TxResponseGenericMessage>): TxResponseGenericMessage;
    fromPartial(object: DeepPartial<TxResponseGenericMessage>): TxResponseGenericMessage;
};
export declare const TxResponseData: {
    encode(message: TxResponseData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TxResponseData;
    fromJSON(object: any): TxResponseData;
    toJSON(message: TxResponseData): unknown;
    create(base?: DeepPartial<TxResponseData>): TxResponseData;
    fromPartial(object: DeepPartial<TxResponseData>): TxResponseData;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
