import { ITransactionOption } from "../interfaces";
import { Web3SideChainClient } from "../utils";
import { ZkEvmToken } from "./zkevm_token";
import { TYPE_AMOUNT } from "../types";
import { IAllowanceTransactionOption, IApproveTransactionOption, IBridgeTransactionOption, IZkEvmClientConfig, IZkEvmContracts } from "../interfaces";
export declare class ERC20 extends ZkEvmToken {
    private bridgeAdapter;
    constructor(tokenAddress: string, isParent: boolean, bridgeAdapterAddress: any, client: Web3SideChainClient<IZkEvmClientConfig>, getContracts: () => IZkEvmContracts);
    /**
     * get bridge for that token
     *
     * @returns
     * @memberof ERC20
     */
    getBridgeAddress(): string;
    isEtherToken(): boolean;
    /**
     * get token balance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    getBalance(userAddress: string, option?: ITransactionOption): Promise<string>;
    /**
     * is Approval needed to bridge tokens to other chains
     *
     * @returns
     * @memberof ERC20
     */
    isApprovalNeeded(): false | Promise<boolean>;
    /**
     * get allowance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    getAllowance(userAddress: string, option?: IAllowanceTransactionOption): Promise<string>;
    /**
     * Approve given amount of tokens for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {IApproveTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    approve(amount: TYPE_AMOUNT, option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Approve max amount of tokens for user
     *
     * @param {IApproveTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    approveMax(option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    deposit(amount: TYPE_AMOUNT, userAddress: string, option?: IBridgeTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user along with ETH for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option?: IBridgeTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user along with ETH for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    depositPermitWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option?: IBridgeTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Deposit given amount of token for user with permit call
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    depositWithPermit(amount: TYPE_AMOUNT, userAddress: string, option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Bridge asset to child chain using Custom ERC20 bridge Adapter
     * @param amount
     * @param userAddress
     * @param forceUpdateGlobalExitRoot
     * @returns
     * @memberof ERC20
     */
    depositCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot?: boolean): Promise<import("..").ITransactionWriteResult>;
    /**
     * Claim asset on child chain bridged using custom bridge adapter on root chain
     * @param transactionHash
     * @param option
     * @returns
     * @memberof ERC20
     */
    customERC20DepositClaim(transactionHash: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Complete deposit after GlobalExitRootManager is synced from Parent to root
     *
     * @param {string} transactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    depositClaim(transactionHash: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * initiate withdraw by burning provided amount
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdraw(amount: TYPE_AMOUNT, userAddress: string, option?: IBridgeTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Bridge asset to root chain using Custom ERC20 bridge Adapter
     * @param amount
     * @param userAddress
     * @param forceUpdateGlobalExitRoot
     * @returns
     * @memberof ERC20
     */
    withdrawCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot?: boolean): Promise<import("..").ITransactionWriteResult>;
    /**
     * Claim asset on root chain bridged using custom bridge adapter on child chain
     * @param burnTransactionHash
     * @param option
     * @returns
     * @memberof ERC20
     */
    customERC20WithdrawExit(burnTransactionHash: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * initiate withdraw by transferring amount with PermitData for native tokens
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdrawWithPermit(amount: TYPE_AMOUNT, userAddress: string, option?: IApproveTransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * Complete deposit after GlobalExitRootManager is synced from Parent to root
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    withdrawExit(burnTransactionHash: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * transfer amount to another user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    transfer(amount: TYPE_AMOUNT, to: string, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * get permitType of the token
     *
     * @returns
     * @memberof ERC20
     */
    private getPermit;
    /**
     * get typedData for signing
     * @param {string} permitType
     * @param {string} account
     * @param {number} chainId
     * @param {string} name
     * @param {string} nonce
     * @param {string} spenderAddress
     * @param {string} amount
     *
     * @returns
     * @memberof ERC20
     */
    private getTypedData_;
    /**
     * get {r, s, v} from signature
     * @param {BaseWeb3Client} client
     * @param {string} signature
     *
     * @returns
     * @memberof ERC20
     */
    private getSignatureParameters_;
    /**
     * encode permit function data
     * @param {BaseContract} contract
     * @param {string} permitType
     * @param {any} signatureParams
     * @param {string} spenderAddress
     * @param {string} account
     * @param {string} nonce
     * @param {string} amount
     *
     * @returns
     * @memberof ERC20
     */
    private encodePermitFunctionData_;
    private getPermitSignatureParams_;
    /**
     * Get permit data for given spender for given amount
     * @param {TYPE_AMOUNT} amount
     * @param {string} spenderAddress
     *
     * @returns
     * @memberof ERC20
     */
    private getPermitData_;
    /**
     * Get permit data for given amount
     * @param {TYPE_AMOUNT} amount
     * @param {IApproveTransactionOption} option
     *
     * @returns
     * @memberof ERC20
     */
    getPermitData(amount: TYPE_AMOUNT, option?: IApproveTransactionOption): Promise<any>;
}
