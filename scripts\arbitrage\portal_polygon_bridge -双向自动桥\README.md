# Polygon-以太坊跨链套利查找器

该套利查找器用于在Polygon和以太坊网络之间寻找潜在的套利机会。它基于zero_tx_tokens.json中的代币数据，通过KyberSwap进行路由查询，计算不同交易额度下的潜在利润。

## 功能特点

- 根据zero_tx_tokens.json数据自动筛选具有ETH和Polygon双链地址的代币
- 自动计算并优化测试金额
- 自动识别低价链和高价链
- 测试多种金额以发现最佳套利机会
- 支持连续扫描，结果实时保存
- 生成详细的JSON和CSV报告

## 安装依赖

确保你已经安装了必要的Python包：

```bash
pip install pandas web3 requests asyncio aiohttp
```

## 使用方法

### 快速启动

使用已经准备好的运行脚本：
```bash
套利汇总脚本命令
python -m scripts.arbitrage.portal_polygon_bridge.main
```
```bash
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev
python -m scripts.arbitrage.portal_polygon_bridge.run_arbitrage_finder --threads 3  --token-limit 3

python -m scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder.py --threads 8 --proxy-timeout 20 --direct-first --api-retries 6 --price-check-retries 4

python -m scripts.arbitrage.portal_polygon_bridge.run_arbitrage_finder --threads 8 --proxy-timeout 20 --api-retries 6 --price-check-retries 4

cd scripts/arbitrage/portal_polygon_bridge
python bridge_arb_finder.py --threads 5

二次分析
python scripts/arbitrage/portal_polygon_bridge/bridge_arb_finder_secondary.py

python -m scripts.arbitrage.portal_polygon_bridge.bridge_arb_executor  --live --auto 
```

### 高级用法

如果需要更精细地控制套利查找器，可以直接使用bridge_arb_finder.py：

```bash
cd C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev
python -m scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder
```

## 原理说明

该套利查找器的工作原理如下：

1. 加载zero_tx_tokens.json中的代币数据
2. 对每个同时有以太坊和Polygon地址的代币：
   - 检查两个链上1个代币兑换为USDT的价格
   - 确定哪个链的价格更低
   - 根据代币的交易历史确定测试金额
   - 在低价链上用USDT购买代币
   - 在高价链上卖出这些代币获取USDT
   - 计算利润和利润百分比
3. 将结果保存为JSON和CSV格式

## 测试金额确定规则

测试金额是根据代币历史交易量自动确定的：

- 当交易次数≥3时：使用最小交易量、平均交易量和最大交易量（均受限于8-300 USDT范围）
- 当交易次数=2时：使用最小交易量和最大交易量（均受限于8-300 USDT范围）
- 当交易次数=1时：使用该交易量（受限于8-300 USDT范围）

## 结果说明

查找器将在以下位置保存结果：

```
scripts/arbitrage/portal_polygon_bridge/results/
```

生成的文件包括：

- arbitrage_results_[timestamp].json：所有检查结果
- arbitrage_opportunities_[timestamp].json：发现的套利机会
- arbitrage_opportunities_[timestamp].csv：套利机会的电子表格版本

## 注意事项

- 该查找器仅用于研究目的，不构成投资建议
- 所有的交易都没有考虑网络手续费和滑点
- 实际执行套利交易需要考虑交易速度、跨链桥费用等因素
- 某些代币在两条链上可能有不同的合约实现，可能存在风险

## 故障排除

如果遇到API错误或超时，可能是由于以下原因：

- KyberSwap API请求限制
- 网络连接问题
- 某些代币流动性不足

尝试：
- 增加API请求之间的延迟
- 重新运行脚本
- 减小测试金额范围 