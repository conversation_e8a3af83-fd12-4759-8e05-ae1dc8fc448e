import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective.peggy.v1";
/** IDSet represents a set of IDs */
export interface IDSet {
    ids: string[];
}
export interface BatchFees {
    token: string;
    totalFees: string;
}
export declare const IDSet: {
    encode(message: IDS<PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): IDSet;
    fromJSO<PERSON>(object: any): IDSet;
    toJSON(message: IDSet): unknown;
    create(base?: DeepPartial<IDSet>): IDSet;
    fromPartial(object: DeepPartial<IDSet>): IDSet;
};
export declare const BatchFees: {
    encode(message: Batch<PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchFees;
    fromJSON(object: any): BatchFees;
    toJSO<PERSON>(message: BatchFees): unknown;
    create(base?: DeepPartial<BatchFees>): BatchFees;
    fromPartial(object: DeepPartial<BatchFees>): BatchFees;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
