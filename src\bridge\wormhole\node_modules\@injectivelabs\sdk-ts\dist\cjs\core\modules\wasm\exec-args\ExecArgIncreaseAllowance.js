"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgIncreaseAllowance extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgIncreaseAllowance(params);
    }
    toData() {
        const { params } = this;
        return {
            amount: params.amount,
            spender: params.spender,
            expires: params.expires,
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('increase_allowance', this.toData());
    }
}
exports.default = ExecArgIncreaseAllowance;
