"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryRoute = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryRoute extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        return (0, index_js_1.toBase64)({
            get_route: {
                source_denom: this.params.sourceDenom,
                target_denom: this.params.targetDenom,
            },
        });
    }
}
exports.QueryRoute = QueryRoute;
