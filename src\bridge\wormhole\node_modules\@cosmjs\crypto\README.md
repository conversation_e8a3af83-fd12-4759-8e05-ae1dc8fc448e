# @cosmjs/crypto

[![npm version](https://img.shields.io/npm/v/@cosmjs/crypto.svg)](https://www.npmjs.com/package/@cosmjs/crypto)

This package contains low-level cryptographic functionality used in other
@cosmjs libraries. Little of it is implemented here, but mainly it is a curation
of external libraries along with correctness tests. We add type safety, some
more checks, and a simple API to these libraries. This can also be freely
imported outside of CosmJS based applications.

## License

This package is part of the cosmjs repository, licensed under the Apache License
2.0 (see [NOTICE](https://github.com/cosmos/cosmjs/blob/main/NOTICE) and
[LICENSE](https://github.com/cosmos/cosmjs/blob/main/LICENSE)).
