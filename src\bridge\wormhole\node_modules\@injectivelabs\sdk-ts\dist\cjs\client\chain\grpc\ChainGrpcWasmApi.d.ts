import { CosmwasmWasmV1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcWasmApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: CosmwasmWasmV1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchContractAccountsBalance({ contractAddress, pagination, }: {
        contractAddress: string;
        pagination?: PaginationOption;
    }): Promise<import("../types/wasm.js").ContractAccountsBalanceWithPagination>;
    fetchContractState({ contractAddress, pagination, }: {
        contractAddress: string;
        pagination?: PaginationOption;
    }): Promise<import("../types/wasm.js").ContractStateWithPagination>;
    fetchContractInfo(contractAddress: string): Promise<import("../types/wasm.js").ContractInfo | undefined>;
    fetchContractHistory(contractAddress: string): Promise<{
        entriesList: import("../types/wasm.js").ContractCodeHistoryEntry[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchSmartContractState(contractAddress: string, query?: string | Record<string, any>): Promise<CosmwasmWasmV1Query.QuerySmartContractStateResponse>;
    fetchRawContractState(contractAddress: string, query?: string): Promise<CosmwasmWasmV1Query.QueryRawContractStateResponse>;
    fetchContractCodes(pagination?: PaginationOption): Promise<{
        codeInfosList: import("../types/wasm.js").CodeInfoResponse[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchContractCode(codeId: number): Promise<{
        codeInfo: import("../types/wasm.js").CodeInfoResponse;
        data: Uint8Array<ArrayBufferLike>;
    }>;
    fetchContractCodeContracts(codeId: number, pagination?: PaginationOption): Promise<{
        contractsList: string[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
}
