#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import argparse
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime
import yaml

# API端点
API_ENDPOINTS = {
    "polygonscan": "https://api.polygonscan.com/api",
    "etherscan": "https://api.etherscan.io/api"
}

# 链ID与名称映射
CHAIN_NAMES = {
    1: "ethereum",
    137: "polygon"
}

CHAIN_IDS = {
    "ethereum": 1,
    "polygon": 137
}

def load_config():
    """
    从config.yaml加载配置
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config', 'config.yaml')
        
        if not os.path.exists(config_path):
            print(f"警告: 找不到配置文件: {config_path}")
            return {}
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

def get_token_info(token_address: str, chain: str, api_key: str = None) -> Dict[str, Any]:
    """
    获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        api_key: API密钥(可选)
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 首先尝试从本地配置文件中获取代币信息
    token_info = get_token_from_local_config(token_address, chain)
    if token_info:
        return token_info
    
    # Polygonscan不支持tokeninfo接口，对于polygon链直接返回默认值
    if chain == "polygon":
        print(f"无法获取Polygon代币信息，使用默认值")
        return {
            "name": "Unknown Token",
            "symbol": "UNKNOWN",
            "decimals": 18
        }
    
    # 对于以太坊，使用API获取
    api_endpoint = API_ENDPOINTS.get(f"{chain}scan")
    if not api_endpoint:
        print(f"不支持的链: {chain}")
        return {
            "name": "Unknown Token",
            "symbol": "UNKNOWN",
            "decimals": 18
        }
    
    # 构建API请求
    params = {
        "module": "token",
        "action": "tokeninfo",
        "contractaddress": token_address,
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        response = requests.get(api_endpoint, params=params)
        data = response.json()
        
        if data["status"] == "1":
            token_data = data["result"][0] if isinstance(data["result"], list) else data["result"]
            return {
                "name": token_data.get("name", "Unknown"),
                "symbol": token_data.get("symbol", "UNKNOWN"),
                "decimals": int(token_data.get("decimals", 18))
            }
        else:
            print(f"获取代币信息失败: {data.get('message', 'Unknown error')}")
            return {
                "name": "Unknown Token",
                "symbol": "UNKNOWN",
                "decimals": 18
            }
    
    except Exception as e:
        print(f"获取代币信息出错: {str(e)}")
        return {
            "name": "Unknown Token",
            "symbol": "UNKNOWN",
            "decimals": 18
        }

def get_token_from_local_config(token_address: str, chain: str) -> Dict[str, Any]:
    """
    从本地配置文件中获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 尝试从tokens.json加载代币信息
    tokens_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tokens.json")
    
    if not os.path.exists(tokens_file):
        return {}
    
    try:
        with open(tokens_file, 'r', encoding='utf-8') as f:
            tokens_data = json.load(f)
        
        # 获取当前链的chain_id
        chain_id = "1" if chain == "ethereum" else "137" if chain == "polygon" else None
        
        if not chain_id:
            return {}
        
        # 遍历所有代币
        for symbol, token_info in tokens_data.items():
            if chain_id in token_info:
                if token_info[chain_id].get("address", "").lower() == token_address.lower():
                    return {
                        "name": token_info[chain_id].get("name", symbol),
                        "symbol": symbol,
                        "decimals": token_info[chain_id].get("decimals", 18)
                    }
        
        return {}
    
    except Exception as e:
        print(f"从本地配置读取代币信息失败: {str(e)}")
        return {}

def get_token_transactions(token_address: str, chain: str, minutes: int = 10, api_key: str = None) -> List[Dict[str, Any]]:
    """
    获取代币最近的交易
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        minutes: 过去几分钟的交易
        api_key: API密钥(可选)
        
    Returns:
        List[Dict[str, Any]]: 交易列表
    """
    api_endpoint = API_ENDPOINTS.get(f"{chain}scan")
    if not api_endpoint:
        print(f"不支持的链: {chain}")
        return []
    
    # 计算时间范围
    now = int(time.time())
    start_time = now - (minutes * 60)
    
    # 构建API请求
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "startblock": 0,
        "endblock": *********,
        "sort": "desc",
        "page": 1,
        "offset": 200  # 限制结果数量
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        print(f"正在请求API: {api_endpoint}")
        response = requests.get(api_endpoint, params=params)
        
        if response.status_code != 200:
            print(f"API请求失败，状态码: {response.status_code}")
            return []
            
        data = response.json()
        print(f"API响应状态: {data.get('status')}, 消息: {data.get('message', 'No message')}")
        
        transactions = []
        
        if data["status"] == "1":
            # 获取代币信息
            token_info = get_token_info(token_address, chain, api_key)
            
            decimals = token_info["decimals"]
            
            # 加载已知的DEX合约信息
            dex_contracts = get_dex_contracts(chain)
            
            # 筛选最近n分钟的交易
            for tx in data["result"]:
                tx_timestamp = int(tx["timeStamp"])
                
                if tx_timestamp >= start_time:
                    # 计算真实代币数量
                    value = int(tx["value"]) / (10 ** decimals)
                    
                    # 判断交易类型
                    tx_type = "Transfer"  # 默认为普通转账
                    dex_name = None
                    
                    # 检查是否为DEX交易
                    from_address = tx["from"].lower()
                    to_address = tx["to"].lower()
                    
                    for dex in dex_contracts:
                        # 检查地址是否匹配DEX合约
                        if (from_address in dex["addresses"] or 
                            to_address in dex["addresses"]):
                            tx_type = "DEX"
                            dex_name = dex["name"]
                            break
                    
                    transactions.append({
                        "hash": tx["hash"],
                        "from": tx["from"],
                        "to": tx["to"],
                        "value": value,
                        "token_symbol": token_info["symbol"],
                        "timestamp": tx_timestamp,
                        "datetime": datetime.fromtimestamp(tx_timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                        "gas_used": int(tx["gasUsed"]),
                        "block_number": int(tx["blockNumber"]),
                        "type": tx_type,
                        "dex": dex_name
                    })
                else:
                    # 已经超过时间范围
                    break
        else:
            print(f"API请求错误: {data.get('message', 'Unknown error')}")
            
            # 如果是API速率限制错误
            if "rate limit" in data.get('message', '').lower() or "ratelimit" in data.get('message', '').lower():
                print("触发API速率限制，请稍后再试或使用API密钥提高限制")
        
        return transactions
    
    except Exception as e:
        print(f"获取代币交易失败: {str(e)}")
        return []

def get_dex_contracts(chain: str) -> List[Dict[str, Any]]:
    """
    获取链上已知的DEX合约地址
    
    Args:
        chain: 链名称
        
    Returns:
        List[Dict[str, Any]]: DEX合约信息
    """
    if chain == "polygon":
        return [
            {
                "name": "QuickSwap",
                "addresses": [
                    "0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff",  # QuickSwap Router
                    "0xf5b509bb0909a69b1c207e495f687a596c168e12"   # QuickSwap V3 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "0x1b02da8cb0d097eb8d57a175b88c7d8b47997506"  # SushiSwap Router
                ]
            },
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************"   # Uniswap V3 Router 2
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "******************************************",  # AAVE Lending Pool
                    "******************************************"   # AAVE Pool
                ]
            }
        ]
    elif chain == "ethereum":
        return [
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************",  # Uniswap V3 Router 2
                    "******************************************"   # Uniswap V2 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "******************************************"  # SushiSwap Router
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "Curve",
                "addresses": [
                    "******************************************",  # Curve.fi Pool
                    "0xbEbc44782C7dB0a1A60Cb6fe97d0b483032FF1C7"   # Curve 3pool
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9",  # AAVE Lending Pool
                    "0x87870bca3f3fd6335c3f4ce8392d69350b4fa4e2"   # AAVE V3 Pool
                ]
            }
        ]
    else:
        return []

def format_transactions(transactions: List[Dict[str, Any]], verbose: bool = False) -> str:
    """
    格式化交易信息以便显示
    
    Args:
        transactions: 交易列表
        verbose: 是否显示详细信息
        
    Returns:
        str: 格式化后的输出
    """
    if not transactions:
        return "未找到任何交易"
    
    output = []
    output.append(f"找到 {len(transactions)} 笔交易")
    
    # 分类计数
    transfer_count = sum(1 for tx in transactions if tx.get("type") == "Transfer")
    dex_count = sum(1 for tx in transactions if tx.get("type") == "DEX")
    
    output.append(f"普通转账: {transfer_count} 笔, DEX交易: {dex_count} 笔")
    
    # 按类型分组
    dex_transactions = [tx for tx in transactions if tx.get("type") == "DEX"]
    transfer_transactions = [tx for tx in transactions if tx.get("type") == "Transfer"]
    
    # 先显示DEX交易
    if dex_transactions:
        output.append("\nDEX交易:")
        for i, tx in enumerate(dex_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  DEX: {tx.get('dex', 'Unknown')}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    # 然后显示普通转账
    if transfer_transactions:
        output.append("\n普通转账:")
        for i, tx in enumerate(transfer_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    return "\n".join(output)

def get_api_keys() -> Dict[str, str]:
    """
    从配置或环境变量中获取API密钥
    
    Returns:
        Dict[str, str]: API密钥字典
    """
    api_keys = {}
    
    # 尝试从配置中获取
    config = load_config()
    
    if config and "api_keys" in config:
        api_keys = config.get("api_keys", {})
    
    # 尝试从环境变量获取
    for chain in ["polygon", "ethereum"]:
        env_var = f"{chain.upper()}_SCAN_API_KEY"
        
        if os.environ.get(env_var):
            api_keys[f"{chain}scan"] = os.environ.get(env_var)
    
    return api_keys

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="监控特定代币的最近交易")
    parser.add_argument("token_address", help="代币合约地址")
    parser.add_argument("--minutes", type=int, default=10, help="查询过去几分钟的交易 (默认: 10)")
    parser.add_argument("--chains", nargs="+", default=["polygon", "ethereum"], help="要监控的链 (默认: polygon ethereum)")
    parser.add_argument("--api-keys-file", help="包含API密钥的文件路径")
    parser.add_argument("--verbose", action="store_true", help="显示详细信息")
    
    args = parser.parse_args()
    
    # 加载API密钥
    api_keys = {
        "polygonscan": "GPHHB2FE5KTCCR27A44E89D695N7WUBXCF",
        "etherscan": "**********************************"
    }
    
    # 如果提供了API密钥文件，则从文件加载
    if args.api_keys_file and os.path.exists(args.api_keys_file):
        try:
            with open(args.api_keys_file, 'r') as f:
                file_api_keys = json.load(f)
                api_keys.update(file_api_keys)
        except Exception as e:
            print(f"读取API密钥文件失败: {str(e)}")
    
    # 监控特定代币在多个链上的交易
    monitor_token_transactions(
        args.token_address,
        args.chains,
        args.minutes,
        api_keys,
        args.verbose
    )

def monitor_token_transactions(token_address: str, chains: List[str], minutes: int = 10, 
                              api_keys: Dict[str, str] = None, verbose: bool = False):
    """
    监控特定代币在多个链上的交易
    
    Args:
        token_address: 代币合约地址
        chains: 要监控的链列表
        minutes: 过去几分钟的交易
        api_keys: API密钥字典，格式为 {"polygonscan": "YOUR_KEY", "etherscan": "YOUR_KEY"}
        verbose: 是否显示详细信息
    """
    if not api_keys:
        api_keys = {}
    
    for chain in chains:
        print(f"\n===== 正在查询 {chain.upper()} 链上最近 {minutes} 分钟的交易 =====")
        
        # 获取API密钥(如果有)
        api_key = api_keys.get(f"{chain}scan")
        
        # 获取代币交易
        transactions = get_token_transactions(token_address, chain, minutes, api_key)
        
        # 格式化并显示结果
        result = format_transactions(transactions, verbose)
        print(result)

if __name__ == "__main__":
    main() 