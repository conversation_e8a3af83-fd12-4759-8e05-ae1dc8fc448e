"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgWithdrawDelegatorReward extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgWithdrawDelegatorReward(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.create();
        message.delegatorAddress = params.delegatorAddress;
        message.validatorAddress = params.validatorAddress;
        return core_proto_ts_1.CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgWithdrawDelegationReward',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosDistributionV1Beta1Tx.MsgWithdrawDelegatorReward.encode(this.toProto()).finish();
    }
}
exports.default = MsgWithdrawDelegatorReward;
