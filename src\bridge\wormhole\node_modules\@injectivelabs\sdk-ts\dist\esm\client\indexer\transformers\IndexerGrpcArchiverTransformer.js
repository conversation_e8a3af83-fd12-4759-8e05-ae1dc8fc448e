/**
 * @category Indexer Grpc Transformer
 */
export class IndexerGrpcArchiverTransformer {
    static grpcHistoricalBalanceToHistoricalBalance(historicalBalance) {
        return {
            t: historicalBalance.t,
            v: historicalBalance.v,
        };
    }
    static grpcHistoricalRPNLToHistoricalRPNL(historicalRPNL) {
        return {
            t: historicalRPNL.t,
            v: historicalRPNL.v,
        };
    }
    static grpcHistoricalVolumesToHistoricalVolumes(historicalVolumes) {
        return {
            t: historicalVolumes.t,
            v: historicalVolumes.v,
        };
    }
    static grpcLeaderboardRowToLeaderboardRow(leaderboardRow) {
        return {
            account: leaderboardRow.account,
            pnl: leaderboardRow.pnl,
            volume: leaderboardRow.volume,
            rank: leaderboardRow.rank,
        };
    }
    static grpcHistoricalBalanceResponseToHistoricalBalances(response) {
        if (!response.historicalBalance) {
            return { t: [], v: [] };
        }
        return IndexerGrpcArchiverTransformer.grpcHistoricalBalanceToHistoricalBalance(response.historicalBalance);
    }
    static grpcHistoricalRPNLResponseToHistoricalRPNL(response) {
        if (!response.historicalRpnl) {
            return { t: [], v: [] };
        }
        return IndexerGrpcArchiverTransformer.grpcHistoricalRPNLToHistoricalRPNL(response.historicalRpnl);
    }
    static grpcHistoricalVolumesResponseToHistoricalVolumes(response) {
        if (!response.historicalVolumes) {
            return { t: [], v: [] };
        }
        return IndexerGrpcArchiverTransformer.grpcHistoricalVolumesToHistoricalVolumes(response.historicalVolumes);
    }
    static grpcPnlLeaderboardResponseToPnlLeaderboard(response) {
        return {
            firstDate: response.firstDate,
            lastDate: response.lastDate,
            leaders: response.leaders.map(IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow),
            accountRow: response.accountRow
                ? IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow(response.accountRow)
                : undefined,
        };
    }
    static grpcVolLeaderboardResponseToVolLeaderboard(response) {
        return {
            firstDate: response.firstDate,
            lastDate: response.lastDate,
            leaders: response.leaders.map(IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow),
            accountRow: response.accountRow
                ? IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow(response.accountRow)
                : undefined,
        };
    }
    static grpcPnlLeaderboardFixedResolutionResponseToPnlLeaderboard(response) {
        return {
            firstDate: response.firstDate,
            lastDate: response.lastDate,
            leaders: response.leaders.map(IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow),
            accountRow: response.accountRow
                ? IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow(response.accountRow)
                : undefined,
        };
    }
    static grpcVolLeaderboardFixedResolutionResponseToVolLeaderboard(response) {
        return {
            firstDate: response.firstDate,
            lastDate: response.lastDate,
            leaders: response.leaders.map(IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow),
            accountRow: response.accountRow
                ? IndexerGrpcArchiverTransformer.grpcLeaderboardRowToLeaderboardRow(response.accountRow)
                : undefined,
        };
    }
    static grpcDenomHoldersResponseToDenomHolders(response) {
        return {
            holders: response.holders.map((holder) => ({
                accountAddress: holder.accountAddress,
                balance: holder.balance,
            })),
            next: response.next,
            total: response.total,
        };
    }
}
