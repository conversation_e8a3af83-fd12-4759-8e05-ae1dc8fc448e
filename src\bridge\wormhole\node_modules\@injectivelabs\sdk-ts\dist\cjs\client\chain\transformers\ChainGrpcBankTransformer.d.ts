import { Coin } from '@injectivelabs/ts-types';
import { Pagination } from '../../../types/index.js';
import { BankModuleParams, Metadata, TotalSupply } from '../types/index.js';
import { CosmosBankV1Beta1Bank, CosmosBankV1Beta1Query } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcBankTransformer {
    static metadataToMetadata(metadata: CosmosBankV1Beta1Bank.Metadata): Metadata;
    static moduleParamsResponseToModuleParams(response: CosmosBankV1Beta1Query.QueryParamsResponse): BankModuleParams;
    static denomOwnersResponseToDenomOwners(response: CosmosBankV1Beta1Query.QueryDenomOwnersResponse): {
        denomOwners: {
            address: string;
            balance: Coin | undefined;
        }[];
        pagination: Pagination;
    };
    static totalSupplyResponseToTotalSupply(response: CosmosBankV1Beta1Query.QueryTotalSupplyResponse): {
        supply: TotalSupply;
        pagination: Pagination;
    };
    static denomsMetadataResponseToDenomsMetadata(response: CosmosBankV1Beta1Query.QueryDenomsMetadataResponse): {
        metadatas: Metadata[];
        pagination: Pagination;
    };
    static balanceResponseToBalance(response: CosmosBankV1Beta1Query.QueryBalanceResponse): Coin;
    static balancesResponseToBalances(response: CosmosBankV1Beta1Query.QueryAllBalancesResponse): {
        balances: Coin[];
        pagination: Pagination;
    };
}
