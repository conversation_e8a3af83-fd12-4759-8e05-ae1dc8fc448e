{"version": 3, "file": "tasks.js", "sourceRoot": "", "sources": ["../../../src/util/tasks.ts"], "names": [], "mappings": ";;;AAKA,MAAa,uBAAuB;IAQlC;;;;;OAKG;IACH,YAAY,WAAmB;QAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,eAAe,GAAG,CAAC,CAAA;QACxB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;IACjB,CAAC;IAED;;;;;;;;OAQG;IACH,cAAc,CAAC,QAAgB,EAAE,EAAY;QAC3C,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,EAAE;YAC3C,IAAI,CAAC,eAAe,EAAE,CAAA;YACtB,EAAE,CAAC,GAAG,EAAE;gBACN,IAAI,CAAC,eAAe,EAAE,CAAA;gBACtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAA;oBAClD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;oBAC/B,IAAI,CAAC,cAAc,CAAC,IAAK,CAAC,QAAQ,EAAE,IAAK,CAAC,EAAE,CAAC,CAAA;iBAC9C;YACH,CAAC,CAAC,CAAA;SACH;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAA;SAClC;IACH,CAAC;IAED;;;;OAIG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,eAAe,KAAK,CAAC,CAAA;IACnC,CAAC;CACF;AArDD,0DAqDC"}