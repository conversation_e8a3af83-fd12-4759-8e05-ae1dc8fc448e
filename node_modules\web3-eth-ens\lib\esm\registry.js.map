{"version": 3, "file": "registry.js", "sourceRoot": "", "sources": ["../../src/registry.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAGF,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAEtC,MAAM,OAAO,QAAQ;IAIpB,YAAmB,OAA0B,EAAE,qBAA+B;QAC7E,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAC3B,cAAc,EACd,qBAAqB,aAArB,qBAAqB,cAArB,qBAAqB,GAAI,iBAAiB,CAAC,IAAI,EAC/C,OAAO,CACP,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAEY,QAAQ,CAAC,IAAY;;YACjC,IAAI;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAElE,OAAO,MAAM,CAAC;aACd;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,MAAM,CAAC,IAAY;;YAC/B,IAAI;gBACH,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aACxD;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,YAAY,CAAC,IAAY;;YACrC,IAAI;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAE1E,OAAO,OAAO,CAAC;aACf;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAEY,WAAW,CAAC,IAAY;;YACpC,IAAI;gBACH,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO;qBAC1B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;qBACxB,IAAI,EAAE;qBACN,IAAI,CAAC,OAAO,CAAC,EAAE;oBACf,wCAAwC;oBACxC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;wBAChC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,iBAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBACxE,0EAA0E;wBAC1E,OAAO,QAAQ,CAAC;qBAChB;oBACD,MAAM,IAAI,KAAK,EAAE,CAAC;gBACnB,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;gBACf,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC,sGAAsG;aACzH;QACF,CAAC;KAAA;IAED,IAAW,MAAM;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC7B,CAAC;CACD"}