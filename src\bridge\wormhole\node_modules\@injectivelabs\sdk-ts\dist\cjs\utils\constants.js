"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_STD_FEE_BY_DENOM = exports.DEFAULT_STD_FEE = exports.DEFAULT_DERIVATION_PATH = exports.BECH32_ADDR_CONS_PREFIX = exports.BECH32_ADDR_VAL_PREFIX = exports.BECH32_ADDR_ACC_PREFIX = exports.BECH32_PUBKEY_CONS_PREFIX = exports.BECH32_PUBKEY_VAL_PREFIX = exports.BECH32_PUBKEY_ACC_PREFIX = void 0;
const utils_1 = require("@injectivelabs/utils");
Object.defineProperty(exports, "DEFAULT_STD_FEE", { enumerable: true, get: function () { return utils_1.DEFAULT_STD_FEE; } });
Object.defineProperty(exports, "DEFAULT_STD_FEE_BY_DENOM", { enumerable: true, get: function () { return utils_1.DEFAULT_STD_FEE_BY_DENOM; } });
exports.BECH32_PUBKEY_ACC_PREFIX = 'injpub';
exports.BECH32_PUBKEY_VAL_PREFIX = 'injvaloperpub';
exports.BECH32_PUBKEY_CONS_PREFIX = 'injvalconspub';
exports.BECH32_ADDR_ACC_PREFIX = 'inj';
exports.BECH32_ADDR_VAL_PREFIX = 'injvaloper';
exports.BECH32_ADDR_CONS_PREFIX = 'injvalcons';
exports.DEFAULT_DERIVATION_PATH = "m/44'/60'/0'/0/0";
