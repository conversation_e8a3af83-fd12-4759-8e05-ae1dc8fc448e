import { getGrpcTransport } from '../../utils/grpc.js';
import { GrpcWebImpl } from './IndexerGrpcWebImpl.js';
/**
 * @hidden
 */
export default class BaseIndexerGrpcWebConsumer extends GrpcWebImpl {
    module = '';
    constructor(endpoint) {
        super(endpoint, { transport: getGrpcTransport() });
    }
}
export const getGrpcIndexerWebImpl = (endpoint) => new BaseIndexerGrpcWebConsumer(endpoint);
