type AptosClientResponse<Res> = {
    status: number;
    statusText: string;
    data: Res;
    config?: any;
    request?: any;
    response?: any;
    headers?: any;
};
type AptosClientRequest = {
    url: string;
    method: "GET" | "POST";
    body?: any;
    params?: any;
    headers?: any;
    overrides?: any;
};

/**
 * Used for JSON responses
 * @param requestOptions
 */
declare function aptosClient<Res>(requestOptions: AptosClientRequest): Promise<AptosClientResponse<Res>>;
declare function jsonRequest<Res>(requestOptions: AptosClientRequest): Promise<AptosClientResponse<Res>>;
/**
 * Used for binary responses, such as BCS outputs
 *
 * @experimental
 * @param requestOptions
 */
declare function bcsRequest(requestOptions: AptosClientRequest): Promise<AptosClientResponse<Buffer>>;

export { bcsRequest, aptosClient as default, jsonRequest };
