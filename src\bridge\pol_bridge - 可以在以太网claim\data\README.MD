一，获取马蹄链所有不活跃币种

fetch_polygon_tokens.py

data
polygon_tokens.json

二，eth链支持币种
root_eth_pol.json

三，获取pol链映射地址

python check_token.py  --root-eth-pol --query-contract
data
tokens.json

四，合并不活跃的且映射的地址

python merge_token_info.py
data
mapped_tokens.json

五，移除活跃的映射地址

python get_token_volumes.py
data
filtered_tokens.json

六，移除2day内没套利机会的币种  
python filter_token_zero_tx.py --minutes 2888
data
zero_tx_tokens.json

尴尬，直接使用第六步就可以了。其它步骤全部省略

一，七天内有套利机会的币种
python filter_poly_tokens_zero_tx.py --stats-only --days 7 --max-tx 2500


all token

tokens.json

两天内有套利的币种

多少tx内有 多个套利机会的币种

还需要写一个脚本来路由无法交易的币种


一：筛选所有能够路由的币种
python src/bridge/pol_bridge/data/test_tokens_routing.py --workers 4 --amount 0.1
使用数据 tokens.json
保存到 routable_tokens_only.json

二：去掉太活跃币种
删除vol大于28w的币种

python src/bridge/pol_bridge/data/get_token_volumes.py
使用数据 routable_tokens_only.json
保存到  low_volume_routable_tokens.json

三：七天内有套利机会的币种
python src/bridge/pol_bridge/data/filter_poly_tokens_zero_tx.py --threads 4
使用数据 low_volume_routable_tokens.json
保存到 zero_tx_tokens_filtered.json  zero_tx_stats_detailed.json


