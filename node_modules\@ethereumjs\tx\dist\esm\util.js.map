{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,UAAU,EACV,UAAU,EACV,aAAa,EACb,uBAAuB,GACxB,MAAM,kBAAkB,CAAA;AAEzB,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAA;AAY9D,MAAM,UAAU,oBAAoB,CAAC,MAAc,EAAE,MAAc;IACjE,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;IAC7D,IAAI,eAAe,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE;QACvD,MAAM,IAAI,KAAK,CACb,6DAA6D,MAAM,qBAAqB,MAAM,CAAC,KAAK,CAClG,IAAI,EACJ,iBAAiB,CAClB,EAAE,CACJ,CAAA;KACF;AACH,CAAC;AAED,MAAM,OAAO,WAAW;IACf,MAAM,CAAC,iBAAiB,CAAC,UAAwC;QACtE,IAAI,cAAc,CAAA;QAClB,IAAI,gBAAgB,CAAA;QACpB,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;YAC5B,cAAc,GAAG,UAAU,CAAA;YAC3B,MAAM,aAAa,GAAoB,EAAE,CAAA;YAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,IAAI,GAAmB,UAAU,CAAC,CAAC,CAAC,CAAA;gBAC1C,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7C,MAAM,YAAY,GAAiB,EAAE,CAAA;gBACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oBAC5D,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;iBACvD;gBACD,aAAa,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;aACjD;YACD,gBAAgB,GAAG,aAAa,CAAA;SACjC;aAAM;YACL,gBAAgB,GAAG,UAAU,IAAI,EAAE,CAAA;YACnC,iBAAiB;YACjB,MAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAA;gBAChC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,WAAW,GAAwB,EAAE,CAAA;gBAC3C,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;oBAChD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBAC5C;gBACD,MAAM,QAAQ,GAAmB;oBAC/B,OAAO;oBACP,WAAW;iBACZ,CAAA;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACpB;YACD,cAAc,GAAG,IAAI,CAAA;SACtB;QAED,OAAO;YACL,cAAc;YACd,UAAU,EAAE,gBAAgB;SAC7B,CAAA;IACH,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,UAA2B;QACxD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YAChD,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;YACtC,MAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;YACjC,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAA;YACtC,IAAU,cAAe,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;gBAC1C,MAAM,IAAI,KAAK,CACb,sGAAsG,CACvG,CAAA;aACF;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;YACD,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBAC1E,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;oBAC3C,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAA;iBACxF;aACF;SACF;IACH,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA2B;QACzD,MAAM,cAAc,GAAG,EAAE,CAAA;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAQ,UAAU,CAAC,KAAK,CAAC,CAAA;YACnC,MAAM,QAAQ,GAAQ;gBACpB,OAAO,EAAE,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC/C,WAAW,EAAE,EAAE;aAChB,CAAA;YACD,MAAM,YAAY,GAAiB,IAAI,CAAC,CAAC,CAAC,CAAA;YAC1C,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACrD,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACtC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;aACtE;YACD,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;SAC9B;QACD,OAAO,cAAc,CAAA;IACvB,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,UAA2B,EAAE,MAAc;QACzE,MAAM,wBAAwB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,0BAA0B,CAAC,CAAA;QACtF,MAAM,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAA;QAEhF,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC5B,KAAK,IAAI,YAAY,CAAC,MAAM,CAAA;SAC7B;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAA;QACnC,OAAO,SAAS,GAAG,MAAM,CAAC,qBAAqB,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAA;IAC7F,CAAC;CACF;AAED,MAAM,OAAO,kBAAkB;IACtB,MAAM,CAAC,wBAAwB,CACpC,iBAA6D;QAE7D,IAAI,qBAAqB,CAAA;QACzB,IAAI,uBAAuB,CAAA;QAC3B,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;YAC1C,qBAAqB,GAAG,iBAAiB,CAAA;YACzC,MAAM,oBAAoB,GAA2B,EAAE,CAAA;YACvD,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;YACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjD,MAAM,IAAI,GAA0B,iBAAiB,CAAC,CAAC,CAAC,CAAA;gBACxD,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;oBAC3B,+CAA+C;oBAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;wBAC3B,MAAM,IAAI,KAAK,CAAC,wCAAwC,GAAG,iBAAiB,CAAC,CAAA;qBAC9E;iBACF;gBACD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACxC,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC7C,MAAM,SAAS,GAAG,EAAE,CAAA;gBACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC1C,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;iBAC1C;gBACD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACxC,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAC5B,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAE5B,oBAAoB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aAC7E;YACD,uBAAuB,GAAG,oBAAoB,CAAA;SAC/C;aAAM;YACL,uBAAuB,GAAG,iBAAiB,IAAI,EAAE,CAAA;YACjD,iBAAiB;YACjB,MAAM,IAAI,GAAsB,EAAE,CAAA;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACvD,MAAM,IAAI,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAA;gBACvC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;gBACtB,MAAM,SAAS,GAAwB,EAAE,CAAA;gBACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;iBACtC;gBACD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACnC,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC7B,MAAM,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC7B,MAAM,QAAQ,GAA0B;oBACtC,OAAO;oBACP,OAAO;oBACP,KAAK,EAAE,SAAS;oBAChB,OAAO;oBACP,CAAC;oBACD,CAAC;iBACF,CAAA;gBACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;aACpB;YACD,qBAAqB,GAAG,IAAI,CAAA;SAC7B;QAED,OAAO;YACL,qBAAqB;YACrB,iBAAiB,EAAE,uBAAuB;SAC3C,CAAA;IACH,CAAC;IAEM,MAAM,CAAC,uBAAuB,CAAC,iBAAyC;QAC7E,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;YACvD,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAA;YACpD,MAAM,OAAO,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;YAC1C,MAAM,OAAO,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;YAClC,MAAM,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAA;YAClC,uBAAuB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;YAC1C,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAA;aACnF;YACD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAA;aAC7F;iBAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjC,uBAAuB,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;aACjD;SACF;IACH,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,aAAqC,EAAE,MAAc;QACnF,MAAM,eAAe,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAA;QACpE,OAAO,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,CAAA;IACvD,CAAC;CACF;AAED,MAAM,UAAU,WAAW,CAAC,MAAuB;IACjD,OAAO,UAAU,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;AAChE,CAAC"}