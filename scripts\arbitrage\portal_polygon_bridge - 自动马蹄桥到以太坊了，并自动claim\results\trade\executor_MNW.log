2025-05-19 09:58:44,350 - INFO - ================================================================================
2025-05-19 09:58:44,350 - INFO - 开始执行 MNW 买入交易 - 时间: 2025-05-19 09:58:44
2025-05-19 09:58:44,350 - INFO - 链: polygon, 投入金额: 230.86 USDT
2025-05-19 09:58:44,350 - INFO - 代币地址: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 09:58:44,350 - INFO - 收到MNW买入请求 - 链:polygon, 投入:230.86USDT
2025-05-19 09:58:44,350 - INFO - MNW: 将在polygon链上执行买入，代币地址: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 09:58:44,350 - INFO - MNW: 准备使用KyberSwap在polygon上执行230.86USDT买入MNW交易
2025-05-19 09:58:44,350 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 09:58:44,359 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 09:58:44,359 - INFO - MNW: 准备调用swap_tokens函数，参数：
2025-05-19 09:58:44,359 - INFO -   chain: polygon
2025-05-19 09:58:44,359 - INFO -   token_in: USDT
2025-05-19 09:58:44,359 - INFO -   token_out: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 09:58:44,359 - INFO -   amount: 230.86
2025-05-19 09:58:44,359 - INFO -   slippage: 0.5%
2025-05-19 09:58:44,359 - INFO -   real: True
2025-05-19 09:58:45,432 - INFO - MNW: swap_tokens返回值类型: <class 'dict'>
2025-05-19 09:58:45,433 - INFO - MNW: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 230.86 USDT，可用: 193.405006 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'amount': 230.86, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'amount_in_wei': '230860000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 09:58:45,433 - ERROR - MNW: 交易失败 - 代币余额不足。请求: 230.86 USDT，可用: 193.405006 USDT
2025-05-19 09:58:45,433 - INFO - 读取到 127 条现有交易记录
2025-05-19 09:58:45,434 - INFO - 添加新交易记录: MNW (MNW_230.86_2025-05-19 09:58:44)
2025-05-19 09:58:45,436 - INFO - 成功保存 128 条交易记录
2025-05-19 09:58:45,436 - INFO - MNW: 买入交易处理完成，耗时: 1.09秒
2025-05-19 09:58:45,436 - INFO - ================================================================================
2025-05-19 12:17:38,745 - INFO - ================================================================================
2025-05-19 12:17:38,745 - INFO - 开始执行 MNW 买入交易 - 时间: 2025-05-19 12:17:38
2025-05-19 12:17:38,745 - INFO - 链: polygon, 投入金额: 211.62 USDT
2025-05-19 12:17:38,745 - INFO - 代币地址: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 12:17:38,745 - INFO - 收到MNW买入请求 - 链:polygon, 投入:211.62USDT
2025-05-19 12:17:38,745 - INFO - MNW: 将在polygon链上执行买入，代币地址: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 12:17:38,745 - INFO - MNW: 准备使用KyberSwap在polygon上执行211.62USDT买入MNW交易
2025-05-19 12:17:38,745 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:17:38,745 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:17:38,745 - INFO - MNW: 准备调用swap_tokens函数，参数：
2025-05-19 12:17:38,745 - INFO -   chain: polygon
2025-05-19 12:17:38,745 - INFO -   token_in: USDT
2025-05-19 12:17:38,745 - INFO -   token_out: 0x3c59798620e5fec0ae6df1a19c6454094572ab92
2025-05-19 12:17:38,745 - INFO -   amount: 211.62
2025-05-19 12:17:38,745 - INFO -   slippage: 0.5%
2025-05-19 12:17:38,745 - INFO -   real: True
2025-05-19 12:17:55,504 - INFO - MNW: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:17:55,505 - INFO - MNW: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 2219.0356469368858 0x3c59798620e5fec0ae6df1a19c6454094572ab92 到地址 0xF9f2934331c051a37Bb6e9C1397205C0E458f434', 'error': None, 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'amount': 211.62, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'amount_in_wei': '211620000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '2219035646936885669971', 'amount_out': 2219.0356469368858, 'route_summary': {'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amountIn': '211620000', 'amountInUsd': '198.13390480135334', 'tokenOut': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'amountOut': '2219035646936885669971', 'amountOutUsd': '201.72103098886907', 'gas': '1130000', 'gasPrice': '41185643049', 'gasUsd': '0.010156659103295817', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x0e3eb2c75bd7dd0e12249d96b1321d9570764d77', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'swapAmount': '148134000', 'amountOut': '148163868', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '1457652066949847389969617340386294118487833376467'}, 'extra': {'GlobalState': {'community_fee_token0': 150, 'community_fee_token1': 150, 'feeOtz': 10, 'feeZto': 10, 'price': '79219842100078313354635000410', 'tick': -3, 'timepoint_index': 28473, 'unlocked': False}, 'Liquidity': '94385262536467', 'ri': '81824fc4-83ce-4328-9095-17c295ed2a5f'}}, {'pool': '0xa4d8c89f0c20efbe54cba9e7e7a7e509056228d9', 'tokenIn': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'tokenOut': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'swapAmount': '148163868', 'amountOut': '61716454000278582', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 500, 'priceLimit': '4295558253'}, 'extra': {'nSqrtRx96': '1617341539968269806001242684929030'}}, {'pool': '0x426a2c62bf87d377d7d2efa13da2556109dfd098', 'tokenIn': '0x7ceb23fd6bc0add59e62ac25578270cff1b9f619', 'tokenOut': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'swapAmount': '61716454000278582', 'amountOut': '1552861100053956828728', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 10000, 'priceLimit': '2441239278316307050158786415'}, 'extra': {'nSqrtRx96': '498438328950360345796524383'}}], [{'pool': '0x7b925e617aefd7fb3a93abe3a701135d7a1ba710', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'swapAmount': '63486000', 'amountOut': '63503663', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '1457652066949847389969617340386294118487833376467'}, 'extra': {'Liquidity': '69602110976837', 'GlobalState': {'price': '79216782821922122423733917025', 'tick': -3, 'feeZto': 10, 'feeOtz': 10, 'timepoint_index': 10844, 'community_fee_token0': 150, 'community_fee_token1': 150, 'unlocked': False}}}, {'pool': '0xa374094527e1673a86de625aa59517c5de346d32', 'tokenIn': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'tokenOut': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'swapAmount': '63503663', 'amountOut': '272740344969239802075', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 500, 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '38221553597994495071863'}}, {'pool': '0x1abc944412f8c8cfafb3fe7764fa954739aab044', 'tokenIn': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'tokenOut': '0x3c59798620e5fec0ae6df1a19c6454094572ab92', 'swapAmount': '272740344969239802075', 'amountOut': '666174546882928841243', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '4306310045'}, 'extra': {'Liquidity': '54648052520611637031701', 'GlobalState': {'price': '123462001053940458023686698003', 'tick': 8872, 'feeZto': 1972, 'feeOtz': 1972, 'timepoint_index': 15320, 'community_fee_token0': 150, 'community_fee_token1': 150, 'unlocked': False}}}]], 'routeID': '81824fc4-83ce-4328-9095-17c295ed2a5f', 'checksum': '14439943944486679733', 'timestamp': 1747628264}, 'tx_hash': '0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e', 'receipt': AttributeDict({'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'blockNumber': 71694348, 'contractAddress': None, 'cumulativeGasUsed': 8989882, 'effectiveGasPrice': 55985543506, 'from': '0xF9f2934331c051a37Bb6e9C1397205C0E458f434', 'gasUsed': 1215481, 'logs': [AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c9d10a0'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 89, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006131b5fae19ea4f9d964eac0408e4408b66337b5')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffff45ef1c77'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 90, 'removed': False}), AttributeDict({'address': '0x0e3Eb2C75Bd7dD0e12249d96b1321d9570764D77', 'topics': [HexBytes('0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000000a'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 91, 'removed': False}), AttributeDict({'address': '0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000008d4d443'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 92, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000008d45870'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 93, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77'), HexBytes('0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000000000de'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 94, 'removed': False}), AttributeDict({'address': '0x0e3Eb2C75Bd7dD0e12249d96b1321d9570764D77', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffff72b2bbd0000000000000000000000000000000000000000000000000000000008d458700000000000000000000000000000000000000000fff8b6721f6bb74bede1e0f1000000000000000000000000000000000000000000000000000055d7c80ba713fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 95, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d770000000000000000000000000000000000000000000000000000000008d4d4430000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c3359'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 96, 'removed': False}), AttributeDict({'address': '0x7ceB23fD6bC0adD59E62ac25578270cFf1b9f619', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000a4d8c89f0c20efbe54cba9e7e7a7e509056228d9'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000db4860081045df'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 97, 'removed': False}), AttributeDict({'address': '0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000a4d8c89f0c20efbe54cba9e7e7a7e509056228d9')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000008d4d443'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 98, 'removed': False}), AttributeDict({'address': '0xA4D8c89f0c20efbe54cBa9e7e7a7E509056228D9', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000008d4d443ffffffffffffffffffffffffffffffffffffffffffffffffff24b79ff7efba210000000000000000000000000000000000004fbeb6441d54504851e9bbe3b17900000000000000000000000000000000000000000000000000ab764e83d3b31a000000000000000000000000000000000000000000000000000000000003075a'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 99, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000a4d8c89f0c20efbe54cba9e7e7a7e509056228d900000000000000000000000000000000000000000000000000db4860081045df0000000000000000000000007ceb23fd6bc0add59e62ac25578270cff1b9f619'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 100, 'removed': False}), AttributeDict({'address': '0x3c59798620e5fEC0Ae6dF1A19c6454094572Ab92', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000426a2c62bf87d377d7d2efa13da2556109dfd098'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000005430319e5ef4a75340'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 101, 'removed': False}), AttributeDict({'address': '0x7ceB23fD6bC0adD59E62ac25578270cFf1b9f619', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000426a2c62bf87d377d7d2efa13da2556109dfd098')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000db4860081045df'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 102, 'removed': False}), AttributeDict({'address': '0x426a2C62bF87D377D7D2EFA13Da2556109DFd098', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffabcfce61a10b58acc000000000000000000000000000000000000000000000000000db4860081045df0000000000000000000000000000000000000000019c4c7bbc89f3adcb2639a6000000000000000000000000000000000000000000000059b9e10986775e6632fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe73fe'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 103, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000426a2c62bf87d377d7d2efa13da2556109dfd09800000000000000000000000000000000000000000000005430319e5ef4a753400000000000000000000000003c59798620e5fec0ae6df1a19c6454094572ab92'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 104, 'removed': False}), AttributeDict({'address': '0x7B925e617aefd7FB3a93Abe3a701135D7a1Ba710', 'topics': [HexBytes('0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000000a'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 105, 'removed': False}), AttributeDict({'address': '0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000007b925e617aefd7fb3a93abe3a701135d7a1ba710'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000003c8f8c3'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 106, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000007b925e617aefd7fb3a93abe3a701135d7a1ba710')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000003c8b830'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 107, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000007b925e617aefd7fb3a93abe3a701135d7a1ba710'), HexBytes('0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000005f'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 108, 'removed': False}), AttributeDict({'address': '0x7B925e617aefd7FB3a93Abe3a701135D7a1Ba710', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffc37073d0000000000000000000000000000000000000000000000000000000003c8b8300000000000000000000000000000000000000000fff72bd63ded32e833be0ab800000000000000000000000000000000000000000000000000003f4d8119b345fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 109, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000007b925e617aefd7fb3a93abe3a701135d7a1ba7100000000000000000000000000000000000000000000000000000000003c8f8c30000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa84174'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 110, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000eca447c9917ab31c9'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 111, 'removed': False}), AttributeDict({'address': '0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000003c8f8c3'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 112, 'removed': False}), AttributeDict({'address': '0xA374094527e1673A86dE625aa59517c5dE346d32', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xfffffffffffffffffffffffffffffffffffffffffffffff135bb8366e854ce370000000000000000000000000000000000000000000000000000000003c8f8c3000000000000000000000000000000000000000000000817a0b979a74bccb8b000000000000000000000000000000000000000000000000025cf7ea3d3bb34affffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8fa5'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 113, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d3200000000000000000000000000000000000000000000000eca447c9917ab31c90000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 114, 'removed': False}), AttributeDict({'address': '0x1ABc944412f8C8CFAfb3fE7764FA954739aAB044', 'topics': [HexBytes('0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000000008ac'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 115, 'removed': False}), AttributeDict({'address': '0x3c59798620e5fEC0Ae6dF1A19c6454094572Ab92', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000001abc944412f8c8cfafb3fe7764fa954739aab044'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000241da6308cc67f8402'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 116, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000001abc944412f8c8cfafb3fe7764fa954739aab044')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000eca447c9917ab31c9'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 117, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000001abc944412f8c8cfafb3fe7764fa954739aab044'), HexBytes('0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000142c5905d4452e2'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 118, 'removed': False}), AttributeDict({'address': '0x1ABc944412f8C8CFAfb3fE7764FA954739aAB044', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000eca447c9917ab31c9ffffffffffffffffffffffffffffffffffffffffffffffdbe259cf7339807bfe00000000000000000000000000000000000000018eed4fe459707ce1d1fdc40f000000000000000000000000000000000000000000000b927a13b5b47ae2f31500000000000000000000000000000000000000000000000000000000000022a8'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 119, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000001abc944412f8c8cfafb3fe7764fa954739aab0440000000000000000000000000000000000000000000000241da6308cc67f84020000000000000000000000003c59798620e5fec0ae6df1a19c6454094572ab92'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 120, 'removed': False}), AttributeDict({'address': '0x3c59798620e5fEC0Ae6dF1A19c6454094572Ab92', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000004f82e73edb06d29ff62c91ec8f5ff06571bdeb29')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000028982fc88f426f0'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 121, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0x4bc8151c051441255339d01fbaeb38cf109cbfd75e9a5c62fb8f1dfb37fe6fd6'), HexBytes('0x0000000000000000000000004f82e73edb06d29ff62c91ec8f5ff06571bdeb29'), HexBytes('0x0000000000000000000000003c59798620e5fec0ae6df1a19c6454094572ab92'), HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000028982fc88f426f00000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 122, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xbf402572f7d269fcae3a56e497d9fc9459e32213d9286c383ad57fa2b532fa8f')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 123, 'removed': False}), AttributeDict({'address': '0x3c59798620e5fEC0Ae6dF1A19c6454094572Ab92', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000784b4e4bef3232b052'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 124, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000c2132d05d31c914a87c6611c10748aeb04b58e8f0000000000000000000000003c59798620e5fec0ae6df1a19c6454094572ab92000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000c9d10a00000000000000000000000000000000000000000000000784b4e4bef3232b052'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 125, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec60000000000000000000000000000000000000000000000784b4e4bef3232b0520000000000000000000000003c59798620e5fec0ae6df1a19c6454094572ab92'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 126, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002977b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223139382e3332333539373834383837373534222c22416d6f756e744f7574555344223a223230312e3732313033303938383836393037222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2232323139303335363436393336383835363639393731222c2254696d657374616d70223a313734373632383237312c22526f7574654944223a2262373732386535362d383239622d346463662d386630622d6536353066313432326332623a65333164323466652d343935382d346665362d396234302d633631633666316135393731222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a22413477366c41366257775a73454a7635766c754a6473647277717a766a6a686161507055523675426c6f74635842506a556f786b674a59524637346d7471457246646675654e6f5332475735612f326c774150374b7361717470326c5875695470796c6544596954647468763971656468424d642b3934664256413066392f3137326c574b654f79654f4165705851704d5632414f3346445165655153315a302f6535617067545a4271754979392f6a58664b4c346761454b542f6a31426d6b4b79524939566d5642336337587561442f78647a6557334b6e445332737a7438747172486578516e6f545a4f32367a78327039464c306f2f4b3536436b4e544458386d4a41524b49394d574c4d6c4b42726935612f764e704f4b4f664d784f462b6855715a474d485736322f4665314d38496b4b6f79684555764a457877466b496f6b7a4d644e563231415144426b764a5a78536b773d3d227d7d000000000000000000'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 127, 'removed': False}), AttributeDict({'address': '0x0000000000000000000000000000000000001010', 'topics': [HexBytes('0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63'), HexBytes('0x0000000000000000000000000000000000000000000000000000000000001010'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x00000000000000000000000002f70172f7f490653665c9bfac0666147c8af1f5')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000b25ae426a38b6f000000000000000000000000000000000000000000000000071da885d05208ae000000000000000000000000000000000000000000000212a8484f4cb0bb1f19000000000000000000000000000000000000000000000000066b4da1a9ae7d3f000000000000000000000000000000000000000000000212a8faaa30d75eaa88'), 'blockNumber': 71694348, 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'blockHash': HexBytes('0xd16ff2d81e6c92f7eece6e6666e81c9d2e763d170f607ee55983224a8a45cb2d'), 'logIndex': 128, 'removed': False})], 'logsBloom': HexBytes('0x00002000010000000000000000000000000000000001400008404010200020000800001020000010080000000000000104848120000020000000000000200004000800001003401800000028000000800000002000040000000111140020000010000000020000000000400000000800000004000000000180020010000a000200014000020800000c000004800000a0000102004400080000000000002000002200000000000000000010000008008048004020000000080040000000000040002001020000000006010a00002010004042000004000080401100000000200000100080000820000004080000000100a0008000110000000000000001100c00'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e'), 'transactionIndex': 24, 'type': 0})}
2025-05-19 12:17:55,506 - INFO - MNW: 交易消息: 交换完成，接收了 2219.0356469368858 0x3c59798620e5fec0ae6df1a19c6454094572ab92 到地址 0xF9f2934331c051a37Bb6e9C1397205C0E458f434
2025-05-19 12:17:55,506 - INFO - MNW: 从消息中提取到代币数量: 2219.0356469368858
2025-05-19 12:17:55,506 - INFO - MNW: 交易成功 - 哈希: 0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e
2025-05-19 12:17:55,507 - INFO - MNW: 最终确认的购买数量: 2219.0356469368858
2025-05-19 12:17:55,507 - INFO - 读取到 140 条现有交易记录
2025-05-19 12:17:55,508 - INFO - 添加新交易记录: MNW (MNW_211.62_2025-05-19 12:17:38)
2025-05-19 12:17:55,510 - INFO - 成功保存 141 条交易记录
2025-05-19 12:17:55,510 - INFO - MNW: 交易成功，从区块链获取实际买入数量...
2025-05-19 12:17:55,517 - INFO - MNW: 使用tx_token_change_tracker获取交易 0x13afb4a0b098fb999aff9f1a050c2f2522b3b7d397ee5c1a4c28808c8f4f218e 的代币数量...
2025-05-19 12:17:56,108 - INFO - MNW: 从区块链获取到实际买入数量: 2219.0356469368858 MNW
2025-05-19 12:17:58,380 - INFO - MNW: 开始从Polygon桥接到以太坊...
2025-05-19 12:41:55,692 - INFO - 已在新线程 Trade-RAI 中启动RAI交易
2025-05-19 12:41:55,693 - INFO - ================================================================================
2025-05-19 12:41:55,693 - INFO - 开始执行 RAI 买入交易 - 时间: 2025-05-19 12:41:55
2025-05-19 12:41:55,693 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-19 12:41:55,693 - INFO - 代币地址: ******************************************
2025-05-19 12:41:55,693 - INFO - 收到RAI买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-19 12:41:55,693 - INFO - RAI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:41:55,694 - INFO - RAI: 准备使用KyberSwap在ethereum上执行300.0USDT买入RAI交易
2025-05-19 12:41:55,694 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:41:55,694 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:41:55,694 - INFO - RAI: 准备调用swap_tokens函数，参数：
2025-05-19 12:41:55,694 - INFO -   chain: ethereum
2025-05-19 12:41:55,694 - INFO -   token_in: USDT
2025-05-19 12:41:55,694 - INFO -   token_out: ******************************************
2025-05-19 12:41:55,694 - INFO -   amount: 300.0
2025-05-19 12:41:55,694 - INFO -   slippage: 0.5%
2025-05-19 12:41:55,694 - INFO -   real: True
2025-05-19 12:41:58,285 - INFO - RAI: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:41:58,285 - INFO - RAI: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:41:58,285 - ERROR - RAI: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT
2025-05-19 12:41:58,286 - INFO - 读取到 141 条现有交易记录
2025-05-19 12:41:58,286 - INFO - 添加新交易记录: RAI (RAI_300.0_2025-05-19 12:41:55)
2025-05-19 12:41:58,288 - INFO - 成功保存 142 条交易记录
2025-05-19 12:41:58,289 - INFO - RAI: 买入交易处理完成，耗时: 2.60秒
2025-05-19 12:41:58,289 - INFO - ================================================================================
2025-05-19 12:50:52,169 - INFO - 已在新线程 Trade-PLOT 中启动PLOT交易
2025-05-19 12:50:52,170 - INFO - ================================================================================
2025-05-19 12:50:52,170 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 12:50:52
2025-05-19 12:50:52,170 - INFO - 链: ethereum, 投入金额: 144.81 USDT
2025-05-19 12:50:52,171 - INFO - 代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:144.81USDT
2025-05-19 12:50:52,171 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行144.81USDT买入PLOT交易
2025-05-19 12:50:52,171 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:50:52,171 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:50:52,171 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 12:50:52,171 - INFO -   chain: ethereum
2025-05-19 12:50:52,171 - INFO -   token_in: USDT
2025-05-19 12:50:52,171 - INFO -   token_out: ******************************************
2025-05-19 12:50:52,171 - INFO -   amount: 144.81
2025-05-19 12:50:52,171 - INFO -   slippage: 0.5%
2025-05-19 12:50:52,171 - INFO -   real: True
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 144.81, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '144810000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:50:54,684 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT
2025-05-19 12:50:54,685 - INFO - 读取到 142 条现有交易记录
2025-05-19 12:50:54,685 - INFO - 添加新交易记录: PLOT (PLOT_144.81_2025-05-19 12:50:52)
2025-05-19 12:50:54,688 - INFO - 成功保存 143 条交易记录
2025-05-19 12:50:54,688 - INFO - PLOT: 买入交易处理完成，耗时: 2.52秒
2025-05-19 12:50:54,688 - INFO - ================================================================================
2025-05-19 12:52:36,190 - INFO - 已在新线程 Trade-DODO 中启动DODO交易
2025-05-19 12:52:36,192 - INFO - ================================================================================
2025-05-19 12:52:36,192 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 12:52:36
2025-05-19 12:52:36,192 - INFO - 链: ethereum, 投入金额: 105.5 USDT
2025-05-19 12:52:36,192 - INFO - 代币地址: ******************************************
2025-05-19 12:52:36,192 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:105.5USDT
2025-05-19 12:52:36,192 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:52:36,192 - INFO - DODO: 准备使用KyberSwap在ethereum上执行105.5USDT买入DODO交易
2025-05-19 12:52:36,192 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:52:36,192 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:52:36,193 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 12:52:36,193 - INFO -   chain: ethereum
2025-05-19 12:52:36,193 - INFO -   token_in: USDT
2025-05-19 12:52:36,193 - INFO -   token_out: ******************************************
2025-05-19 12:52:36,193 - INFO -   amount: 105.5
2025-05-19 12:52:36,193 - INFO -   slippage: 0.5%
2025-05-19 12:52:36,193 - INFO -   real: True
2025-05-19 12:52:38,160 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:52:38,160 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 105.5 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 105.5, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '105500000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:52:38,161 - ERROR - DODO: 交易失败 - 代币余额不足。请求: 105.5 USDT，可用: 67.81312 USDT
2025-05-19 12:52:38,163 - INFO - 读取到 143 条现有交易记录
2025-05-19 12:52:38,163 - INFO - 添加新交易记录: DODO (DODO_105.5_2025-05-19 12:52:36)
2025-05-19 12:52:38,165 - INFO - 成功保存 144 条交易记录
2025-05-19 12:52:38,165 - INFO - DODO: 买入交易处理完成，耗时: 1.97秒
2025-05-19 12:52:38,165 - INFO - ================================================================================
2025-05-19 13:23:30,586 - INFO - 已在新线程 Trade-PLOT 中启动PLOT交易
2025-05-19 13:23:30,587 - INFO - ================================================================================
2025-05-19 13:23:30,588 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 13:23:30
2025-05-19 13:23:30,588 - INFO - 链: ethereum, 投入金额: 189.15 USDT
2025-05-19 13:23:30,588 - INFO - 代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:189.15USDT
2025-05-19 13:23:30,588 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行189.15USDT买入PLOT交易
2025-05-19 13:23:30,588 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 13:23:30,588 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 13:23:30,588 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 13:23:30,588 - INFO -   chain: ethereum
2025-05-19 13:23:30,588 - INFO -   token_in: USDT
2025-05-19 13:23:30,588 - INFO -   token_out: ******************************************
2025-05-19 13:23:30,588 - INFO -   amount: 189.15
2025-05-19 13:23:30,589 - INFO -   slippage: 0.5%
2025-05-19 13:23:30,589 - INFO -   real: True
2025-05-19 13:23:33,140 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 13:23:33,144 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 189.15, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '189150000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 13:23:33,145 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT
2025-05-19 13:23:33,146 - INFO - 读取到 144 条现有交易记录
2025-05-19 13:23:33,146 - INFO - 添加新交易记录: PLOT (PLOT_189.15_2025-05-19 13:23:30)
2025-05-19 13:23:33,151 - INFO - 成功保存 145 条交易记录
2025-05-19 13:23:33,151 - INFO - PLOT: 买入交易处理完成，耗时: 2.56秒
2025-05-19 13:23:33,151 - INFO - ================================================================================
2025-05-19 13:23:51,112 - INFO - MNW: 等待桥接完成...
2025-05-19 13:23:51,112 - INFO - MNW: Burn交易哈希: 0x40d6685f6435b5e5681875ca7c8cc5d39d30cbfeceea28b6b3f991fc810d50ad
2025-05-19 13:23:51,112 - WARNING - MNW: 未获取到Claim交易哈希
2025-05-19 13:23:51,112 - INFO - MNW: 桥接操作完成
2025-05-19 13:23:51,112 - INFO - MNW: 桥接操作完成，结果: {'success': False, 'burn_tx': '0x40d6685f6435b5e5681875ca7c8cc5d39d30cbfeceea28b6b3f991fc810d50ad', 'claim_tx': None, 'message': '未知状态', 'claim_status': 'confirmed'}
2025-05-19 13:23:51,113 - INFO - MNW: 买入交易处理完成，耗时: 3972.37秒
2025-05-19 13:23:51,113 - INFO - ================================================================================
