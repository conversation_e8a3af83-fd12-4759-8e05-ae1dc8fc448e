import{b as c}from"./chunk-STY74NUA.mjs";import{a as U,b,c as f,d as h,e as l,f as y}from"./chunk-56CNRT2K.mjs";import{a as s}from"./chunk-KDMSOCZY.mjs";var B=class{bcsToBytes(){let e=new n;return this.serialize(e),e.toUint8Array()}bcsToHex(){let e=this.bcsToBytes();return c.fromHexInput(e)}toStringWithoutPrefix(){return this.bcsToHex().toStringWithoutPrefix()}toString(){return`0x${this.toStringWithoutPrefix()}`}},n=class{constructor(e=64){if(e<=0)throw new Error("Length needs to be greater than 0");this.buffer=new ArrayBuffer(e),this.offset=0}ensureBufferWillHandleSize(e){for(;this.buffer.byteLength<this.offset+e;){let t=new ArrayBuffer(this.buffer.byteLength*2);new Uint8Array(t).set(new Uint8Array(this.buffer)),this.buffer=t}}appendToBuffer(e){this.ensureBufferWillHandleSize(e.length),new Uint8Array(this.buffer,this.offset).set(e),this.offset+=e.length}serializeWithFunction(e,t,i){this.ensureBufferWillHandleSize(t);let a=new DataView(this.buffer,this.offset);e.apply(a,[0,i,!0]),this.offset+=t}serializeStr(e){let t=new TextEncoder;this.serializeBytes(t.encode(e))}serializeBytes(e){this.serializeU32AsUleb128(e.length),this.appendToBuffer(e)}serializeFixedBytes(e){this.appendToBuffer(e)}serializeBool(e){p(e);let t=e?1:0;this.appendToBuffer(new Uint8Array([t]))}serializeU8(e){this.appendToBuffer(new Uint8Array([e]))}serializeU16(e){this.serializeWithFunction(DataView.prototype.setUint16,2,e)}serializeU32(e){this.serializeWithFunction(DataView.prototype.setUint32,4,e)}serializeU64(e){let t=BigInt(e)&BigInt(f),i=BigInt(e)>>BigInt(32);this.serializeU32(Number(t)),this.serializeU32(Number(i))}serializeU128(e){let t=BigInt(e)&h,i=BigInt(e)>>BigInt(64);this.serializeU64(t),this.serializeU64(i)}serializeU256(e){let t=BigInt(e)&l,i=BigInt(e)>>BigInt(128);this.serializeU128(t),this.serializeU128(i)}serializeU32AsUleb128(e){let t=e,i=[];for(;t>>>7;)i.push(t&127|128),t>>>=7;i.push(t),this.appendToBuffer(new Uint8Array(i))}toUint8Array(){return new Uint8Array(this.buffer).slice(0,this.offset)}serialize(e){e.serialize(this)}serializeVector(e){this.serializeU32AsUleb128(e.length),e.forEach(t=>{t.serialize(this)})}serializeOption(e,t){let i=e!==void 0;this.serializeBool(i),i&&(typeof e=="string"?this.serializeStr(e):e instanceof Uint8Array?t!==void 0?this.serializeFixedBytes(e):this.serializeBytes(e):e.serialize(this))}serializeOptionStr(e){e===void 0?this.serializeU32AsUleb128(0):(this.serializeU32AsUleb128(1),this.serializeStr(e))}};s([o(0,U)],n.prototype,"serializeU8",1),s([o(0,b)],n.prototype,"serializeU16",1),s([o(0,f)],n.prototype,"serializeU32",1),s([o(BigInt(0),h)],n.prototype,"serializeU64",1),s([o(BigInt(0),l)],n.prototype,"serializeU128",1),s([o(BigInt(0),y)],n.prototype,"serializeU256",1),s([o(0,f)],n.prototype,"serializeU32AsUleb128",1);function p(r){if(typeof r!="boolean")throw new Error(`${r} is not a boolean value`)}var A=(r,e,t)=>`${r} is out of range: [${e}, ${t}]`;function z(r,e,t){let i=BigInt(r);if(i>BigInt(t)||i<BigInt(e))throw new Error(A(r,e,t))}function o(r,e){return(t,i,a)=>{let g=a.value;return a.value=function(u){return z(u,r,e),g.apply(this,[u])},a}}export{B as a,n as b,p as c,A as d,z as e};
//# sourceMappingURL=chunk-EBMEXURY.mjs.map