import{a as d}from"./chunk-YOZBVVKL.mjs";import{b as s}from"./chunk-CZYH3G7E.mjs";import{b as r}from"./chunk-RGKRCZ36.mjs";async function g(o){let{aptosConfig:e,accountAddress:t}=o,{data:n}=await s({aptosConfig:e,originMethod:"getInfo",path:`accounts/${r.from(t).toString()}`});return n}async function f(o){return o.options?.ledgerVersion!==void 0?c(o):d(async()=>c(o),`module-${o.accountAddress}-${o.moduleName}`,1e3*60*5)()}async function c(o){let{aptosConfig:e,accountAddress:t,moduleName:n,options:i}=o,{data:u}=await s({aptosConfig:e,originMethod:"getModule",path:`accounts/${r.from(t).toString()}/module/${n}`,params:{ledger_version:i?.ledgerVersion}});return u}export{g as a,f as b};
//# sourceMappingURL=chunk-57J5YBMT.mjs.map