"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var genesis_1 = require("../../channel/v1/genesis.js");
var genesis_2 = require("../../client/v1/genesis.js");
var genesis_3 = require("../../connection/v1/genesis.js");
exports.protobufPackage = "ibc.core.types.v1";
function createBaseGenesisState() {
    return { clientGenesis: undefined, connectionGenesis: undefined, channelGenesis: undefined };
}
exports.GenesisState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.clientGenesis !== undefined) {
            genesis_2.GenesisState.encode(message.clientGenesis, writer.uint32(10).fork()).ldelim();
        }
        if (message.connectionGenesis !== undefined) {
            genesis_3.GenesisState.encode(message.connectionGenesis, writer.uint32(18).fork()).ldelim();
        }
        if (message.channelGenesis !== undefined) {
            genesis_1.GenesisState.encode(message.channelGenesis, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.clientGenesis = genesis_2.GenesisState.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.connectionGenesis = genesis_3.GenesisState.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.channelGenesis = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            clientGenesis: isSet(object.clientGenesis) ? genesis_2.GenesisState.fromJSON(object.clientGenesis) : undefined,
            connectionGenesis: isSet(object.connectionGenesis) ? genesis_3.GenesisState.fromJSON(object.connectionGenesis) : undefined,
            channelGenesis: isSet(object.channelGenesis) ? genesis_1.GenesisState.fromJSON(object.channelGenesis) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.clientGenesis !== undefined &&
            (obj.clientGenesis = message.clientGenesis ? genesis_2.GenesisState.toJSON(message.clientGenesis) : undefined);
        message.connectionGenesis !== undefined &&
            (obj.connectionGenesis = message.connectionGenesis ? genesis_3.GenesisState.toJSON(message.connectionGenesis) : undefined);
        message.channelGenesis !== undefined &&
            (obj.channelGenesis = message.channelGenesis ? genesis_1.GenesisState.toJSON(message.channelGenesis) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseGenesisState();
        message.clientGenesis = (object.clientGenesis !== undefined && object.clientGenesis !== null)
            ? genesis_2.GenesisState.fromPartial(object.clientGenesis)
            : undefined;
        message.connectionGenesis = (object.connectionGenesis !== undefined && object.connectionGenesis !== null)
            ? genesis_3.GenesisState.fromPartial(object.connectionGenesis)
            : undefined;
        message.channelGenesis = (object.channelGenesis !== undefined && object.channelGenesis !== null)
            ? genesis_1.GenesisState.fromPartial(object.channelGenesis)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
