"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryProviderPriceStateDesc = exports.QueryStorkPublishersDesc = exports.QueryStorkPriceStatesDesc = exports.QueryPythPriceStatesDesc = exports.QueryCoinbasePriceStatesDesc = exports.QueryPriceFeedPriceStatesDesc = exports.QueryBandIBCPriceStatesDesc = exports.QueryBandPriceStatesDesc = exports.QueryBandRelayersDesc = exports.QueryParamsDesc = exports.QueryDesc = exports.QueryClientImpl = exports.QueryOraclePriceResponse = exports.PricePairState = exports.QueryOraclePriceRequest = exports.ScalingOptions = exports.QueryOracleProviderPricesResponse = exports.QueryOracleProviderPricesRequest = exports.QueryOracleProvidersInfoResponse = exports.QueryOracleProvidersInfoRequest = exports.QueryOracleVolatilityResponse = exports.QueryOracleVolatilityRequest = exports.OracleHistoryOptions = exports.QueryHistoricalPriceRecordsResponse = exports.QueryHistoricalPriceRecordsRequest = exports.QueryModuleStateResponse = exports.QueryModuleStateRequest = exports.QueryProviderPriceStateResponse = exports.QueryProviderPriceStateRequest = exports.QueryStorkPublishersResponse = exports.QueryStorkPublishersRequest = exports.QueryStorkPriceStatesResponse = exports.QueryStorkPriceStatesRequest = exports.QueryPythPriceStatesResponse = exports.QueryPythPriceStatesRequest = exports.QueryCoinbasePriceStatesResponse = exports.QueryCoinbasePriceStatesRequest = exports.QueryPriceFeedPriceStatesResponse = exports.QueryPriceFeedPriceStatesRequest = exports.QueryBandIBCPriceStatesResponse = exports.QueryBandIBCPriceStatesRequest = exports.QueryBandPriceStatesResponse = exports.QueryBandPriceStatesRequest = exports.QueryBandRelayersResponse = exports.QueryBandRelayersRequest = exports.QueryParamsResponse = exports.QueryParamsRequest = exports.QueryPythPriceResponse = exports.QueryPythPriceRequest = exports.protobufPackage = void 0;
exports.GrpcWebError = exports.GrpcWebImpl = exports.QueryPythPriceDesc = exports.QueryOraclePriceDesc = exports.QueryOracleProviderPricesDesc = exports.QueryOracleProvidersInfoDesc = exports.QueryOracleVolatilityDesc = exports.QueryHistoricalPriceRecordsDesc = exports.QueryOracleModuleStateDesc = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var genesis_1 = require("./genesis.js");
var oracle_1 = require("./oracle.js");
exports.protobufPackage = "injective.oracle.v1beta1";
function createBaseQueryPythPriceRequest() {
    return { priceId: "" };
}
exports.QueryPythPriceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.priceId !== "") {
            writer.uint32(10).string(message.priceId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPythPriceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { priceId: isSet(object.priceId) ? String(object.priceId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.priceId !== undefined && (obj.priceId = message.priceId);
        return obj;
    },
    create: function (base) {
        return exports.QueryPythPriceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPythPriceRequest();
        message.priceId = (_a = object.priceId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryPythPriceResponse() {
    return { priceState: undefined };
}
exports.QueryPythPriceResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.priceState !== undefined) {
            oracle_1.PythPriceState.encode(message.priceState, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPythPriceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceState = oracle_1.PythPriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { priceState: isSet(object.priceState) ? oracle_1.PythPriceState.fromJSON(object.priceState) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? oracle_1.PythPriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryPythPriceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryPythPriceResponse();
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? oracle_1.PythPriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBaseQueryParamsRequest() {
    return {};
}
exports.QueryParamsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryParamsRequest();
        return message;
    },
};
function createBaseQueryParamsResponse() {
    return { params: undefined };
}
exports.QueryParamsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            oracle_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = oracle_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { params: isSet(object.params) ? oracle_1.Params.fromJSON(object.params) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? oracle_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? oracle_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQueryBandRelayersRequest() {
    return {};
}
exports.QueryBandRelayersRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandRelayersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryBandRelayersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryBandRelayersRequest();
        return message;
    },
};
function createBaseQueryBandRelayersResponse() {
    return { relayers: [] };
}
exports.QueryBandRelayersResponse = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.relayers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandRelayersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.relayers.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { relayers: Array.isArray(object === null || object === void 0 ? void 0 : object.relayers) ? object.relayers.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.relayers) {
            obj.relayers = message.relayers.map(function (e) { return e; });
        }
        else {
            obj.relayers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBandRelayersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBandRelayersResponse();
        message.relayers = ((_a = object.relayers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryBandPriceStatesRequest() {
    return {};
}
exports.QueryBandPriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandPriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryBandPriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryBandPriceStatesRequest();
        return message;
    },
};
function createBaseQueryBandPriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryBandPriceStatesResponse = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.BandPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandPriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.BandPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.BandPriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.BandPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBandPriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBandPriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.BandPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryBandIBCPriceStatesRequest() {
    return {};
}
exports.QueryBandIBCPriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandIBCPriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryBandIBCPriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryBandIBCPriceStatesRequest();
        return message;
    },
};
function createBaseQueryBandIBCPriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryBandIBCPriceStatesResponse = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.BandPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBandIBCPriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.BandPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.BandPriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.BandPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBandIBCPriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBandIBCPriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.BandPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryPriceFeedPriceStatesRequest() {
    return {};
}
exports.QueryPriceFeedPriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPriceFeedPriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryPriceFeedPriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryPriceFeedPriceStatesRequest();
        return message;
    },
};
function createBaseQueryPriceFeedPriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryPriceFeedPriceStatesResponse = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PriceFeedState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPriceFeedPriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.PriceFeedState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.PriceFeedState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.PriceFeedState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryPriceFeedPriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPriceFeedPriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.PriceFeedState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryCoinbasePriceStatesRequest() {
    return {};
}
exports.QueryCoinbasePriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryCoinbasePriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryCoinbasePriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryCoinbasePriceStatesRequest();
        return message;
    },
};
function createBaseQueryCoinbasePriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryCoinbasePriceStatesResponse = {
    encode: function (message, writer) {
        var e_5, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.CoinbasePriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryCoinbasePriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.CoinbasePriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.CoinbasePriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.CoinbasePriceState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryCoinbasePriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryCoinbasePriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.CoinbasePriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryPythPriceStatesRequest() {
    return {};
}
exports.QueryPythPriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPythPriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryPythPriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryPythPriceStatesRequest();
        return message;
    },
};
function createBaseQueryPythPriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryPythPriceStatesResponse = {
    encode: function (message, writer) {
        var e_6, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PythPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPythPriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.PythPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.PythPriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.PythPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryPythPriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPythPriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.PythPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryStorkPriceStatesRequest() {
    return {};
}
exports.QueryStorkPriceStatesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryStorkPriceStatesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryStorkPriceStatesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryStorkPriceStatesRequest();
        return message;
    },
};
function createBaseQueryStorkPriceStatesResponse() {
    return { priceStates: [] };
}
exports.QueryStorkPriceStatesResponse = {
    encode: function (message, writer) {
        var e_7, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceStates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.StorkPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryStorkPriceStatesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceStates.push(oracle_1.StorkPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceStates: Array.isArray(object === null || object === void 0 ? void 0 : object.priceStates)
                ? object.priceStates.map(function (e) { return oracle_1.StorkPriceState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceStates) {
            obj.priceStates = message.priceStates.map(function (e) { return e ? oracle_1.StorkPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.priceStates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryStorkPriceStatesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryStorkPriceStatesResponse();
        message.priceStates = ((_a = object.priceStates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.StorkPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryStorkPublishersRequest() {
    return {};
}
exports.QueryStorkPublishersRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryStorkPublishersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryStorkPublishersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryStorkPublishersRequest();
        return message;
    },
};
function createBaseQueryStorkPublishersResponse() {
    return { publishers: [] };
}
exports.QueryStorkPublishersResponse = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.publishers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryStorkPublishersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.publishers.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { publishers: Array.isArray(object === null || object === void 0 ? void 0 : object.publishers) ? object.publishers.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.publishers) {
            obj.publishers = message.publishers.map(function (e) { return e; });
        }
        else {
            obj.publishers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryStorkPublishersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryStorkPublishersResponse();
        message.publishers = ((_a = object.publishers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryProviderPriceStateRequest() {
    return { provider: "", symbol: "" };
}
exports.QueryProviderPriceStateRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.provider !== "") {
            writer.uint32(10).string(message.provider);
        }
        if (message.symbol !== "") {
            writer.uint32(18).string(message.symbol);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryProviderPriceStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.provider = reader.string();
                    break;
                case 2:
                    message.symbol = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            provider: isSet(object.provider) ? String(object.provider) : "",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.provider !== undefined && (obj.provider = message.provider);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        return obj;
    },
    create: function (base) {
        return exports.QueryProviderPriceStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryProviderPriceStateRequest();
        message.provider = (_a = object.provider) !== null && _a !== void 0 ? _a : "";
        message.symbol = (_b = object.symbol) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryProviderPriceStateResponse() {
    return { priceState: undefined };
}
exports.QueryProviderPriceStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.priceState !== undefined) {
            oracle_1.PriceState.encode(message.priceState, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryProviderPriceStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceState = oracle_1.PriceState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { priceState: isSet(object.priceState) ? oracle_1.PriceState.fromJSON(object.priceState) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.priceState !== undefined &&
            (obj.priceState = message.priceState ? oracle_1.PriceState.toJSON(message.priceState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryProviderPriceStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryProviderPriceStateResponse();
        message.priceState = (object.priceState !== undefined && object.priceState !== null)
            ? oracle_1.PriceState.fromPartial(object.priceState)
            : undefined;
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
exports.QueryModuleStateRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
exports.QueryModuleStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            genesis_1.GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? genesis_1.GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? genesis_1.GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? genesis_1.GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseQueryHistoricalPriceRecordsRequest() {
    return { oracle: 0, symbolId: "" };
}
exports.QueryHistoricalPriceRecordsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.oracle !== 0) {
            writer.uint32(8).int32(message.oracle);
        }
        if (message.symbolId !== "") {
            writer.uint32(18).string(message.symbolId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryHistoricalPriceRecordsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.oracle = reader.int32();
                    break;
                case 2:
                    message.symbolId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            oracle: isSet(object.oracle) ? (0, oracle_1.oracleTypeFromJSON)(object.oracle) : 0,
            symbolId: isSet(object.symbolId) ? String(object.symbolId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.oracle !== undefined && (obj.oracle = (0, oracle_1.oracleTypeToJSON)(message.oracle));
        message.symbolId !== undefined && (obj.symbolId = message.symbolId);
        return obj;
    },
    create: function (base) {
        return exports.QueryHistoricalPriceRecordsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryHistoricalPriceRecordsRequest();
        message.oracle = (_a = object.oracle) !== null && _a !== void 0 ? _a : 0;
        message.symbolId = (_b = object.symbolId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryHistoricalPriceRecordsResponse() {
    return { priceRecords: [] };
}
exports.QueryHistoricalPriceRecordsResponse = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.priceRecords), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PriceRecords.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryHistoricalPriceRecordsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.priceRecords.push(oracle_1.PriceRecords.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            priceRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.priceRecords)
                ? object.priceRecords.map(function (e) { return oracle_1.PriceRecords.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.priceRecords) {
            obj.priceRecords = message.priceRecords.map(function (e) { return e ? oracle_1.PriceRecords.toJSON(e) : undefined; });
        }
        else {
            obj.priceRecords = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryHistoricalPriceRecordsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryHistoricalPriceRecordsResponse();
        message.priceRecords = ((_a = object.priceRecords) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.PriceRecords.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOracleHistoryOptions() {
    return { maxAge: "0", includeRawHistory: false, includeMetadata: false };
}
exports.OracleHistoryOptions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.maxAge !== "0") {
            writer.uint32(8).uint64(message.maxAge);
        }
        if (message.includeRawHistory === true) {
            writer.uint32(16).bool(message.includeRawHistory);
        }
        if (message.includeMetadata === true) {
            writer.uint32(24).bool(message.includeMetadata);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOracleHistoryOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.maxAge = longToString(reader.uint64());
                    break;
                case 2:
                    message.includeRawHistory = reader.bool();
                    break;
                case 3:
                    message.includeMetadata = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            maxAge: isSet(object.maxAge) ? String(object.maxAge) : "0",
            includeRawHistory: isSet(object.includeRawHistory) ? Boolean(object.includeRawHistory) : false,
            includeMetadata: isSet(object.includeMetadata) ? Boolean(object.includeMetadata) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.maxAge !== undefined && (obj.maxAge = message.maxAge);
        message.includeRawHistory !== undefined && (obj.includeRawHistory = message.includeRawHistory);
        message.includeMetadata !== undefined && (obj.includeMetadata = message.includeMetadata);
        return obj;
    },
    create: function (base) {
        return exports.OracleHistoryOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseOracleHistoryOptions();
        message.maxAge = (_a = object.maxAge) !== null && _a !== void 0 ? _a : "0";
        message.includeRawHistory = (_b = object.includeRawHistory) !== null && _b !== void 0 ? _b : false;
        message.includeMetadata = (_c = object.includeMetadata) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseQueryOracleVolatilityRequest() {
    return { baseInfo: undefined, quoteInfo: undefined, oracleHistoryOptions: undefined };
}
exports.QueryOracleVolatilityRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.baseInfo !== undefined) {
            oracle_1.OracleInfo.encode(message.baseInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.quoteInfo !== undefined) {
            oracle_1.OracleInfo.encode(message.quoteInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.oracleHistoryOptions !== undefined) {
            exports.OracleHistoryOptions.encode(message.oracleHistoryOptions, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleVolatilityRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.baseInfo = oracle_1.OracleInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.quoteInfo = oracle_1.OracleInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.oracleHistoryOptions = exports.OracleHistoryOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            baseInfo: isSet(object.baseInfo) ? oracle_1.OracleInfo.fromJSON(object.baseInfo) : undefined,
            quoteInfo: isSet(object.quoteInfo) ? oracle_1.OracleInfo.fromJSON(object.quoteInfo) : undefined,
            oracleHistoryOptions: isSet(object.oracleHistoryOptions)
                ? exports.OracleHistoryOptions.fromJSON(object.oracleHistoryOptions)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.baseInfo !== undefined &&
            (obj.baseInfo = message.baseInfo ? oracle_1.OracleInfo.toJSON(message.baseInfo) : undefined);
        message.quoteInfo !== undefined &&
            (obj.quoteInfo = message.quoteInfo ? oracle_1.OracleInfo.toJSON(message.quoteInfo) : undefined);
        message.oracleHistoryOptions !== undefined && (obj.oracleHistoryOptions = message.oracleHistoryOptions
            ? exports.OracleHistoryOptions.toJSON(message.oracleHistoryOptions)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleVolatilityRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryOracleVolatilityRequest();
        message.baseInfo = (object.baseInfo !== undefined && object.baseInfo !== null)
            ? oracle_1.OracleInfo.fromPartial(object.baseInfo)
            : undefined;
        message.quoteInfo = (object.quoteInfo !== undefined && object.quoteInfo !== null)
            ? oracle_1.OracleInfo.fromPartial(object.quoteInfo)
            : undefined;
        message.oracleHistoryOptions = (object.oracleHistoryOptions !== undefined && object.oracleHistoryOptions !== null)
            ? exports.OracleHistoryOptions.fromPartial(object.oracleHistoryOptions)
            : undefined;
        return message;
    },
};
function createBaseQueryOracleVolatilityResponse() {
    return { volatility: "", historyMetadata: undefined, rawHistory: [] };
}
exports.QueryOracleVolatilityResponse = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.volatility !== "") {
            writer.uint32(10).string(message.volatility);
        }
        if (message.historyMetadata !== undefined) {
            oracle_1.MetadataStatistics.encode(message.historyMetadata, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.rawHistory), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PriceRecord.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleVolatilityResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.volatility = reader.string();
                    break;
                case 2:
                    message.historyMetadata = oracle_1.MetadataStatistics.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.rawHistory.push(oracle_1.PriceRecord.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            volatility: isSet(object.volatility) ? String(object.volatility) : "",
            historyMetadata: isSet(object.historyMetadata) ? oracle_1.MetadataStatistics.fromJSON(object.historyMetadata) : undefined,
            rawHistory: Array.isArray(object === null || object === void 0 ? void 0 : object.rawHistory) ? object.rawHistory.map(function (e) { return oracle_1.PriceRecord.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.volatility !== undefined && (obj.volatility = message.volatility);
        message.historyMetadata !== undefined &&
            (obj.historyMetadata = message.historyMetadata ? oracle_1.MetadataStatistics.toJSON(message.historyMetadata) : undefined);
        if (message.rawHistory) {
            obj.rawHistory = message.rawHistory.map(function (e) { return e ? oracle_1.PriceRecord.toJSON(e) : undefined; });
        }
        else {
            obj.rawHistory = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleVolatilityResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryOracleVolatilityResponse();
        message.volatility = (_a = object.volatility) !== null && _a !== void 0 ? _a : "";
        message.historyMetadata = (object.historyMetadata !== undefined && object.historyMetadata !== null)
            ? oracle_1.MetadataStatistics.fromPartial(object.historyMetadata)
            : undefined;
        message.rawHistory = ((_b = object.rawHistory) === null || _b === void 0 ? void 0 : _b.map(function (e) { return oracle_1.PriceRecord.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryOracleProvidersInfoRequest() {
    return {};
}
exports.QueryOracleProvidersInfoRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleProvidersInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleProvidersInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryOracleProvidersInfoRequest();
        return message;
    },
};
function createBaseQueryOracleProvidersInfoResponse() {
    return { providers: [] };
}
exports.QueryOracleProvidersInfoResponse = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.providers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.ProviderInfo.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleProvidersInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.providers.push(oracle_1.ProviderInfo.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            providers: Array.isArray(object === null || object === void 0 ? void 0 : object.providers) ? object.providers.map(function (e) { return oracle_1.ProviderInfo.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.providers) {
            obj.providers = message.providers.map(function (e) { return e ? oracle_1.ProviderInfo.toJSON(e) : undefined; });
        }
        else {
            obj.providers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleProvidersInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOracleProvidersInfoResponse();
        message.providers = ((_a = object.providers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.ProviderInfo.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryOracleProviderPricesRequest() {
    return { provider: "" };
}
exports.QueryOracleProviderPricesRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.provider !== "") {
            writer.uint32(10).string(message.provider);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleProviderPricesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.provider = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { provider: isSet(object.provider) ? String(object.provider) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.provider !== undefined && (obj.provider = message.provider);
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleProviderPricesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOracleProviderPricesRequest();
        message.provider = (_a = object.provider) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryOracleProviderPricesResponse() {
    return { providerState: [] };
}
exports.QueryOracleProviderPricesResponse = {
    encode: function (message, writer) {
        var e_12, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.providerState), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.ProviderState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOracleProviderPricesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.providerState.push(oracle_1.ProviderState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            providerState: Array.isArray(object === null || object === void 0 ? void 0 : object.providerState)
                ? object.providerState.map(function (e) { return oracle_1.ProviderState.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.providerState) {
            obj.providerState = message.providerState.map(function (e) { return e ? oracle_1.ProviderState.toJSON(e) : undefined; });
        }
        else {
            obj.providerState = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryOracleProviderPricesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOracleProviderPricesResponse();
        message.providerState = ((_a = object.providerState) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.ProviderState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseScalingOptions() {
    return { baseDecimals: 0, quoteDecimals: 0 };
}
exports.ScalingOptions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.baseDecimals !== 0) {
            writer.uint32(8).uint32(message.baseDecimals);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(16).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseScalingOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.baseDecimals = reader.uint32();
                    break;
                case 2:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            baseDecimals: isSet(object.baseDecimals) ? Number(object.baseDecimals) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.baseDecimals !== undefined && (obj.baseDecimals = Math.round(message.baseDecimals));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.ScalingOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseScalingOptions();
        message.baseDecimals = (_a = object.baseDecimals) !== null && _a !== void 0 ? _a : 0;
        message.quoteDecimals = (_b = object.quoteDecimals) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseQueryOraclePriceRequest() {
    return { oracleType: 0, base: "", quote: "", scalingOptions: undefined };
}
exports.QueryOraclePriceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.oracleType !== 0) {
            writer.uint32(8).int32(message.oracleType);
        }
        if (message.base !== "") {
            writer.uint32(18).string(message.base);
        }
        if (message.quote !== "") {
            writer.uint32(26).string(message.quote);
        }
        if (message.scalingOptions !== undefined) {
            exports.ScalingOptions.encode(message.scalingOptions, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOraclePriceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.oracleType = reader.int32();
                    break;
                case 2:
                    message.base = reader.string();
                    break;
                case 3:
                    message.quote = reader.string();
                    break;
                case 4:
                    message.scalingOptions = exports.ScalingOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            base: isSet(object.base) ? String(object.base) : "",
            quote: isSet(object.quote) ? String(object.quote) : "",
            scalingOptions: isSet(object.scalingOptions) ? exports.ScalingOptions.fromJSON(object.scalingOptions) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.base !== undefined && (obj.base = message.base);
        message.quote !== undefined && (obj.quote = message.quote);
        message.scalingOptions !== undefined &&
            (obj.scalingOptions = message.scalingOptions ? exports.ScalingOptions.toJSON(message.scalingOptions) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryOraclePriceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryOraclePriceRequest();
        message.oracleType = (_a = object.oracleType) !== null && _a !== void 0 ? _a : 0;
        message.base = (_b = object.base) !== null && _b !== void 0 ? _b : "";
        message.quote = (_c = object.quote) !== null && _c !== void 0 ? _c : "";
        message.scalingOptions = (object.scalingOptions !== undefined && object.scalingOptions !== null)
            ? exports.ScalingOptions.fromPartial(object.scalingOptions)
            : undefined;
        return message;
    },
};
function createBasePricePairState() {
    return {
        pairPrice: "",
        basePrice: "",
        quotePrice: "",
        baseCumulativePrice: "",
        quoteCumulativePrice: "",
        baseTimestamp: "0",
        quoteTimestamp: "0",
    };
}
exports.PricePairState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.pairPrice !== "") {
            writer.uint32(10).string(message.pairPrice);
        }
        if (message.basePrice !== "") {
            writer.uint32(18).string(message.basePrice);
        }
        if (message.quotePrice !== "") {
            writer.uint32(26).string(message.quotePrice);
        }
        if (message.baseCumulativePrice !== "") {
            writer.uint32(34).string(message.baseCumulativePrice);
        }
        if (message.quoteCumulativePrice !== "") {
            writer.uint32(42).string(message.quoteCumulativePrice);
        }
        if (message.baseTimestamp !== "0") {
            writer.uint32(48).int64(message.baseTimestamp);
        }
        if (message.quoteTimestamp !== "0") {
            writer.uint32(56).int64(message.quoteTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePricePairState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pairPrice = reader.string();
                    break;
                case 2:
                    message.basePrice = reader.string();
                    break;
                case 3:
                    message.quotePrice = reader.string();
                    break;
                case 4:
                    message.baseCumulativePrice = reader.string();
                    break;
                case 5:
                    message.quoteCumulativePrice = reader.string();
                    break;
                case 6:
                    message.baseTimestamp = longToString(reader.int64());
                    break;
                case 7:
                    message.quoteTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            pairPrice: isSet(object.pairPrice) ? String(object.pairPrice) : "",
            basePrice: isSet(object.basePrice) ? String(object.basePrice) : "",
            quotePrice: isSet(object.quotePrice) ? String(object.quotePrice) : "",
            baseCumulativePrice: isSet(object.baseCumulativePrice) ? String(object.baseCumulativePrice) : "",
            quoteCumulativePrice: isSet(object.quoteCumulativePrice) ? String(object.quoteCumulativePrice) : "",
            baseTimestamp: isSet(object.baseTimestamp) ? String(object.baseTimestamp) : "0",
            quoteTimestamp: isSet(object.quoteTimestamp) ? String(object.quoteTimestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.pairPrice !== undefined && (obj.pairPrice = message.pairPrice);
        message.basePrice !== undefined && (obj.basePrice = message.basePrice);
        message.quotePrice !== undefined && (obj.quotePrice = message.quotePrice);
        message.baseCumulativePrice !== undefined && (obj.baseCumulativePrice = message.baseCumulativePrice);
        message.quoteCumulativePrice !== undefined && (obj.quoteCumulativePrice = message.quoteCumulativePrice);
        message.baseTimestamp !== undefined && (obj.baseTimestamp = message.baseTimestamp);
        message.quoteTimestamp !== undefined && (obj.quoteTimestamp = message.quoteTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.PricePairState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBasePricePairState();
        message.pairPrice = (_a = object.pairPrice) !== null && _a !== void 0 ? _a : "";
        message.basePrice = (_b = object.basePrice) !== null && _b !== void 0 ? _b : "";
        message.quotePrice = (_c = object.quotePrice) !== null && _c !== void 0 ? _c : "";
        message.baseCumulativePrice = (_d = object.baseCumulativePrice) !== null && _d !== void 0 ? _d : "";
        message.quoteCumulativePrice = (_e = object.quoteCumulativePrice) !== null && _e !== void 0 ? _e : "";
        message.baseTimestamp = (_f = object.baseTimestamp) !== null && _f !== void 0 ? _f : "0";
        message.quoteTimestamp = (_g = object.quoteTimestamp) !== null && _g !== void 0 ? _g : "0";
        return message;
    },
};
function createBaseQueryOraclePriceResponse() {
    return { pricePairState: undefined };
}
exports.QueryOraclePriceResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.pricePairState !== undefined) {
            exports.PricePairState.encode(message.pricePairState, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOraclePriceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pricePairState = exports.PricePairState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            pricePairState: isSet(object.pricePairState) ? exports.PricePairState.fromJSON(object.pricePairState) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.pricePairState !== undefined &&
            (obj.pricePairState = message.pricePairState ? exports.PricePairState.toJSON(message.pricePairState) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryOraclePriceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryOraclePriceResponse();
        message.pricePairState = (object.pricePairState !== undefined && object.pricePairState !== null)
            ? exports.PricePairState.fromPartial(object.pricePairState)
            : undefined;
        return message;
    },
};
var QueryClientImpl = /** @class */ (function () {
    function QueryClientImpl(rpc) {
        this.rpc = rpc;
        this.Params = this.Params.bind(this);
        this.BandRelayers = this.BandRelayers.bind(this);
        this.BandPriceStates = this.BandPriceStates.bind(this);
        this.BandIBCPriceStates = this.BandIBCPriceStates.bind(this);
        this.PriceFeedPriceStates = this.PriceFeedPriceStates.bind(this);
        this.CoinbasePriceStates = this.CoinbasePriceStates.bind(this);
        this.PythPriceStates = this.PythPriceStates.bind(this);
        this.StorkPriceStates = this.StorkPriceStates.bind(this);
        this.StorkPublishers = this.StorkPublishers.bind(this);
        this.ProviderPriceState = this.ProviderPriceState.bind(this);
        this.OracleModuleState = this.OracleModuleState.bind(this);
        this.HistoricalPriceRecords = this.HistoricalPriceRecords.bind(this);
        this.OracleVolatility = this.OracleVolatility.bind(this);
        this.OracleProvidersInfo = this.OracleProvidersInfo.bind(this);
        this.OracleProviderPrices = this.OracleProviderPrices.bind(this);
        this.OraclePrice = this.OraclePrice.bind(this);
        this.PythPrice = this.PythPrice.bind(this);
    }
    QueryClientImpl.prototype.Params = function (request, metadata) {
        return this.rpc.unary(exports.QueryParamsDesc, exports.QueryParamsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BandRelayers = function (request, metadata) {
        return this.rpc.unary(exports.QueryBandRelayersDesc, exports.QueryBandRelayersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BandPriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryBandPriceStatesDesc, exports.QueryBandPriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BandIBCPriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryBandIBCPriceStatesDesc, exports.QueryBandIBCPriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PriceFeedPriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryPriceFeedPriceStatesDesc, exports.QueryPriceFeedPriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.CoinbasePriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryCoinbasePriceStatesDesc, exports.QueryCoinbasePriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PythPriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryPythPriceStatesDesc, exports.QueryPythPriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.StorkPriceStates = function (request, metadata) {
        return this.rpc.unary(exports.QueryStorkPriceStatesDesc, exports.QueryStorkPriceStatesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.StorkPublishers = function (request, metadata) {
        return this.rpc.unary(exports.QueryStorkPublishersDesc, exports.QueryStorkPublishersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ProviderPriceState = function (request, metadata) {
        return this.rpc.unary(exports.QueryProviderPriceStateDesc, exports.QueryProviderPriceStateRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OracleModuleState = function (request, metadata) {
        return this.rpc.unary(exports.QueryOracleModuleStateDesc, exports.QueryModuleStateRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.HistoricalPriceRecords = function (request, metadata) {
        return this.rpc.unary(exports.QueryHistoricalPriceRecordsDesc, exports.QueryHistoricalPriceRecordsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OracleVolatility = function (request, metadata) {
        return this.rpc.unary(exports.QueryOracleVolatilityDesc, exports.QueryOracleVolatilityRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OracleProvidersInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryOracleProvidersInfoDesc, exports.QueryOracleProvidersInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OracleProviderPrices = function (request, metadata) {
        return this.rpc.unary(exports.QueryOracleProviderPricesDesc, exports.QueryOracleProviderPricesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OraclePrice = function (request, metadata) {
        return this.rpc.unary(exports.QueryOraclePriceDesc, exports.QueryOraclePriceRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PythPrice = function (request, metadata) {
        return this.rpc.unary(exports.QueryPythPriceDesc, exports.QueryPythPriceRequest.fromPartial(request), metadata);
    };
    return QueryClientImpl;
}());
exports.QueryClientImpl = QueryClientImpl;
exports.QueryDesc = { serviceName: "injective.oracle.v1beta1.Query" };
exports.QueryParamsDesc = {
    methodName: "Params",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBandRelayersDesc = {
    methodName: "BandRelayers",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBandRelayersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBandRelayersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBandPriceStatesDesc = {
    methodName: "BandPriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBandPriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBandPriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBandIBCPriceStatesDesc = {
    methodName: "BandIBCPriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBandIBCPriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBandIBCPriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPriceFeedPriceStatesDesc = {
    methodName: "PriceFeedPriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPriceFeedPriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPriceFeedPriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryCoinbasePriceStatesDesc = {
    methodName: "CoinbasePriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryCoinbasePriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryCoinbasePriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPythPriceStatesDesc = {
    methodName: "PythPriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPythPriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPythPriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryStorkPriceStatesDesc = {
    methodName: "StorkPriceStates",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryStorkPriceStatesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryStorkPriceStatesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryStorkPublishersDesc = {
    methodName: "StorkPublishers",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryStorkPublishersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryStorkPublishersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryProviderPriceStateDesc = {
    methodName: "ProviderPriceState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryProviderPriceStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryProviderPriceStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOracleModuleStateDesc = {
    methodName: "OracleModuleState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryModuleStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryHistoricalPriceRecordsDesc = {
    methodName: "HistoricalPriceRecords",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryHistoricalPriceRecordsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryHistoricalPriceRecordsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOracleVolatilityDesc = {
    methodName: "OracleVolatility",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOracleVolatilityRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOracleVolatilityResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOracleProvidersInfoDesc = {
    methodName: "OracleProvidersInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOracleProvidersInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOracleProvidersInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOracleProviderPricesDesc = {
    methodName: "OracleProviderPrices",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOracleProviderPricesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOracleProviderPricesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOraclePriceDesc = {
    methodName: "OraclePrice",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOraclePriceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOraclePriceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPythPriceDesc = {
    methodName: "PythPrice",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPythPriceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPythPriceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
