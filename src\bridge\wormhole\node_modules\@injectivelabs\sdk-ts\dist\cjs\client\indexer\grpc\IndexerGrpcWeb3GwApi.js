"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcWeb3GwApi = void 0;
const utils_1 = require("@injectivelabs/utils");
const index_js_1 = require("../types/index.js");
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const IndexerGrpcTransactionApi_js_1 = require("./IndexerGrpcTransactionApi.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcWeb3GwApi extends IndexerGrpcTransactionApi_js_1.IndexerGrpcTransactionApi {
    module = index_js_1.IndexerModule.Web3Gw;
    constructor(endpoint) {
        super(endpoint);
    }
    async prepareEip712Request({ address, chainId, message, memo, sequence, accountNumber, estimateGas = false, gasLimit = utils_1.DEFAULT_GAS_LIMIT, feeDenom = utils_1.DEFAULT_BRIDGE_FEE_DENOM, feePrice = utils_1.DEFAULT_BRIDGE_FEE_PRICE, timeoutHeight, eip712Version = 'v1', }) {
        const txFeeAmount = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        txFeeAmount.denom = feeDenom;
        txFeeAmount.amount = feePrice;
        const cosmosTxFee = indexer_proto_ts_1.InjectiveExchangeRpc.CosmosTxFee.create();
        cosmosTxFee.price = [txFeeAmount];
        if (!estimateGas) {
            cosmosTxFee.gas = gasLimit.toString();
        }
        const prepareTxRequest = indexer_proto_ts_1.InjectiveExchangeRpc.PrepareEip712Request.create();
        prepareTxRequest.chainId = chainId.toString();
        prepareTxRequest.signerAddress = address;
        prepareTxRequest.fee = cosmosTxFee;
        const arrayOfMessages = Array.isArray(message) ? message : [message];
        const messagesList = arrayOfMessages.map((message) => Buffer.from(JSON.stringify(message), 'utf8'));
        prepareTxRequest.msgs = messagesList;
        if (timeoutHeight !== undefined) {
            prepareTxRequest.timeoutHeight = timeoutHeight.toString();
        }
        if (memo) {
            prepareTxRequest.memo = typeof memo === 'number' ? memo.toString() : memo;
        }
        if (eip712Version) {
            prepareTxRequest.eip712Wrapper = eip712Version;
        }
        if (accountNumber) {
            prepareTxRequest.accountNumber = accountNumber.toString();
        }
        if (sequence) {
            prepareTxRequest.sequence = sequence.toString();
        }
        try {
            const response = await this.client.PrepareEip712(prepareTxRequest, this.metadata);
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExchangeRpc.GrpcWebError) {
                throw new exceptions_1.TransactionException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PrepareEip712',
                    contextModule: 'Web3Gateway',
                    type: e.type,
                });
            }
            throw new exceptions_1.TransactionException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Web3Gateway.PrepareEip712',
                type: exceptions_1.ErrorType.Web3Gateway,
            });
        }
    }
}
exports.IndexerGrpcWeb3GwApi = IndexerGrpcWeb3GwApi;
