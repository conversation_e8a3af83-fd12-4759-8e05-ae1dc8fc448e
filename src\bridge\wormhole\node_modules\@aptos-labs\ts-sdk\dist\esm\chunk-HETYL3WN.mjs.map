{"version": 3, "sources": ["../../src/client/core.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { VERSION } from \"../version\";\nimport { AnyNumber, AptosRequest, AptosResponse, Client, ClientRequest, ClientResponse, MimeType } from \"../types\";\nimport { AptosApiType } from \"../utils\";\nimport { AptosApiError } from \"../errors\";\n\n/**\n * Sends a request using the specified options and returns the response.\n *\n * @param options - The options for the request.\n * @param options.url - The URL to send the request to.\n * @param options.method - The HTTP method to use for the request.\n * @param options.body - The body of the request.\n * @param options.contentType - The content type of the request.\n * @param options.params - The query parameters to include in the request.\n * @param options.overrides - Additional overrides for the request.\n * @param options.overrides.HEADERS - Custom headers to include in the request.\n * @param options.overrides.AUTH_TOKEN - The authorization token for the request.\n * @param options.overrides.API_KEY - The API key for the request.\n * @param options.originMethod - The origin method for the request.\n * @param client - The client used to make the request.\n *\n * @returns The response from the request.\n * @group Implementation\n * @category Client\n */\nexport async function request<Req, Res>(options: ClientRequest<Req>, client: Client): Promise<ClientResponse<Res>> {\n  const { url, method, body, contentType, params, overrides, originMethod } = options;\n  const headers: Record<string, string | AnyNumber | boolean | undefined> = {\n    ...overrides?.HEADERS,\n    \"x-aptos-client\": `aptos-typescript-sdk/${VERSION}`,\n    \"content-type\": contentType ?? MimeType.JSON,\n    \"x-aptos-typescript-sdk-origin-method\": originMethod,\n  };\n\n  if (overrides?.AUTH_TOKEN) {\n    headers.Authorization = `Bearer ${overrides?.AUTH_TOKEN}`;\n  }\n  if (overrides?.API_KEY) {\n    headers.Authorization = `Bearer ${overrides?.API_KEY}`;\n  }\n\n  /*\n   * make a call using the @aptos-labs/aptos-client package\n   * {@link https://www.npmjs.com/package/@aptos-labs/aptos-client}\n   */\n  return client.provider<Req, Res>({\n    url,\n    method,\n    body,\n    params,\n    headers,\n    overrides,\n  });\n}\n\n/**\n * The main function to use when making an API request, returning the response or throwing an AptosApiError on failure.\n *\n * @param aptosRequestOpts - Options for the Aptos request, including the URL and path.\n * @param aptosConfig - The configuration information for the SDK client instance.\n * @param apiType - The type of API being accessed, which determines how the response is handled.\n * @returns The response from the API request or throws an AptosApiError if the request fails.\n * @group Implementation\n * @category Client\n */\nexport async function aptosRequest<Req extends {}, Res extends {}>(\n  aptosRequestOpts: AptosRequest,\n  aptosConfig: AptosConfig,\n  apiType: AptosApiType,\n): Promise<AptosResponse<Req, Res>> {\n  const { url, path } = aptosRequestOpts;\n  const fullUrl = path ? `${url}/${path}` : url;\n  const clientResponse = await request<Req, Res>({ ...aptosRequestOpts, url: fullUrl }, aptosConfig.client);\n\n  const aptosResponse: AptosResponse<Req, Res> = {\n    status: clientResponse.status,\n    statusText: clientResponse.statusText ?? \"No status text provided\",\n    data: clientResponse.data,\n    headers: clientResponse.headers,\n    config: clientResponse.config,\n    request: clientResponse.request,\n    url: fullUrl,\n  };\n\n  // Handle case for `Unauthorized` error (i.e. API_KEY error)\n  if (aptosResponse.status === 401) {\n    throw new AptosApiError({ apiType, aptosRequest: aptosRequestOpts, aptosResponse });\n  }\n\n  // to support both fullnode and indexer responses,\n  // check if it is an indexer query, and adjust response.data\n  if (apiType === AptosApiType.INDEXER) {\n    const indexerResponse = aptosResponse.data as any;\n    // Handle Indexer general errors\n    if (indexerResponse.errors) {\n      throw new AptosApiError({\n        apiType,\n        aptosRequest: aptosRequestOpts,\n        aptosResponse,\n      });\n    }\n    aptosResponse.data = indexerResponse.data as Res;\n  } else if (apiType === AptosApiType.PEPPER || apiType === AptosApiType.PROVER) {\n    if (aptosResponse.status >= 400) {\n      throw new AptosApiError({ apiType, aptosRequest: aptosRequestOpts, aptosResponse });\n    }\n  }\n\n  if (aptosResponse.status >= 200 && aptosResponse.status < 300) {\n    return aptosResponse;\n  }\n\n  // We have to explicitly check for all request types, because if the error is a non-indexer error, but\n  // comes from an indexer request (e.g. 404), we'll need to mention it appropriately\n  throw new AptosApiError({ apiType, aptosRequest: aptosRequestOpts, aptosResponse });\n}\n"], "mappings": "kFA6BA,eAAsBA,EAAkBC,EAA6BC,EAA8C,CACjH,GAAM,CAAE,IAAAC,EAAK,OAAAC,EAAQ,KAAAC,EAAM,YAAAC,EAAa,OAAAC,EAAQ,UAAAC,EAAW,aAAAC,CAAa,EAAIR,EACtES,EAAoE,CACxE,GAAGF,GAAW,QACd,iBAAkB,wBAAwBG,CAAO,GACjD,eAAgBL,GAAe,mBAC/B,uCAAwCG,CAC1C,EAEA,OAAID,GAAW,aACbE,EAAQ,cAAgB,UAAUF,GAAW,UAAU,IAErDA,GAAW,UACbE,EAAQ,cAAgB,UAAUF,GAAW,OAAO,IAO/CN,EAAO,SAAmB,CAC/B,IAAAC,EACA,OAAAC,EACA,KAAAC,EACA,OAAAE,EACA,QAAAG,EACA,UAAAF,CACF,CAAC,CACH,CAYA,eAAsBI,EACpBC,EACAC,EACAC,EACkC,CAClC,GAAM,CAAE,IAAAZ,EAAK,KAAAa,CAAK,EAAIH,EAChBI,EAAUD,EAAO,GAAGb,CAAG,IAAIa,CAAI,GAAKb,EACpCe,EAAiB,MAAMlB,EAAkB,CAAE,GAAGa,EAAkB,IAAKI,CAAQ,EAAGH,EAAY,MAAM,EAElGK,EAAyC,CAC7C,OAAQD,EAAe,OACvB,WAAYA,EAAe,YAAc,0BACzC,KAAMA,EAAe,KACrB,QAASA,EAAe,QACxB,OAAQA,EAAe,OACvB,QAASA,EAAe,QACxB,IAAKD,CACP,EAGA,GAAIE,EAAc,SAAW,IAC3B,MAAM,IAAIC,EAAc,CAAE,QAAAL,EAAS,aAAcF,EAAkB,cAAAM,CAAc,CAAC,EAKpF,GAAIJ,IAAY,UAAsB,CACpC,IAAMM,EAAkBF,EAAc,KAEtC,GAAIE,EAAgB,OAClB,MAAM,IAAID,EAAc,CACtB,QAAAL,EACA,aAAcF,EACd,cAAAM,CACF,CAAC,EAEHA,EAAc,KAAOE,EAAgB,IACvC,UAAWN,IAAY,UAAuBA,IAAY,WACpDI,EAAc,QAAU,IAC1B,MAAM,IAAIC,EAAc,CAAE,QAAAL,EAAS,aAAcF,EAAkB,cAAAM,CAAc,CAAC,EAItF,GAAIA,EAAc,QAAU,KAAOA,EAAc,OAAS,IACxD,OAAOA,EAKT,MAAM,IAAIC,EAAc,CAAE,QAAAL,EAAS,aAAcF,EAAkB,cAAAM,CAAc,CAAC,CACpF", "names": ["request", "options", "client", "url", "method", "body", "contentType", "params", "overrides", "originMethod", "headers", "VERSION", "aptosRequest", "aptosRequestOpts", "aptosConfig", "apiType", "path", "fullUrl", "clientResponse", "aptosResponse", "AptosApiError", "indexerResponse"]}