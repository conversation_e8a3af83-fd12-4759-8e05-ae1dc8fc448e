import { EncodeObject, GeneratedType } from "@cosmjs/proto-signing";
import { Msg<PERSON><PERSON>r<PERSON>dmin, MsgExecuteContract, MsgInstantiateContract, MsgInstantiateContract2, MsgMigrateContract, MsgStoreCode, MsgUpdateAdmin } from "cosmjs-types/cosmwasm/wasm/v1/tx";
export declare const wasmTypes: ReadonlyArray<[string, GeneratedType]>;
export interface MsgStoreCodeEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgStoreCode";
    readonly value: Partial<MsgStoreCode>;
}
export declare function isMsgStoreCodeEncodeObject(object: EncodeObject): object is MsgStoreCodeEncodeObject;
export interface MsgInstantiateContractEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgInstantiateContract";
    readonly value: Partial<MsgInstantiateContract>;
}
export declare function isMsgInstantiateContractEncodeObject(object: EncodeObject): object is MsgInstantiateContractEncodeObject;
export interface MsgInstantiateContract2EncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgInstantiateContract2";
    readonly value: Partial<MsgInstantiateContract2>;
}
export declare function isMsgInstantiateContract2EncodeObject(object: EncodeObject): object is MsgInstantiateContract2EncodeObject;
export interface MsgUpdateAdminEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgUpdateAdmin";
    readonly value: Partial<MsgUpdateAdmin>;
}
export declare function isMsgUpdateAdminEncodeObject(object: EncodeObject): object is MsgUpdateAdminEncodeObject;
export interface MsgClearAdminEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgClearAdmin";
    readonly value: Partial<MsgClearAdmin>;
}
export declare function isMsgClearAdminEncodeObject(object: EncodeObject): object is MsgClearAdminEncodeObject;
export interface MsgMigrateContractEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgMigrateContract";
    readonly value: Partial<MsgMigrateContract>;
}
export declare function isMsgMigrateEncodeObject(object: EncodeObject): object is MsgMigrateContractEncodeObject;
export interface MsgExecuteContractEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmwasm.wasm.v1.MsgExecuteContract";
    readonly value: Partial<MsgExecuteContract>;
}
export declare function isMsgExecuteEncodeObject(object: EncodeObject): object is MsgExecuteContractEncodeObject;
