#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询Gate.io账户余额的脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

import argparse
from tabulate import tabulate

try:
    from src.cex.gate.client import GateClient
    from src.utils.logger import logger
except ImportError:
    import logging
    # 如果无法导入logger，创建一个基本的logger
    logger = logging.getLogger("gate_balance")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.warning("无法导入必要的模块，请确保已设置正确的PYTHONPATH")
    sys.exit(1)

# 设置默认输出目录
DEFAULT_OUTPUT_DIR = os.path.join(project_root, 'data', 'cex', 'gate_info')

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="查询Gate.io账户余额")
    parser.add_argument('currencies', nargs='*', help='要查询的币种代码列表，例如BTC ETH USDT，默认查询所有币种')
    parser.add_argument('-m', '--min', type=float, default=0.0, help="最小余额阈值，默认为0")
    parser.add_argument('-f', '--format', choices=['table', 'simple', 'plain'], default='table', 
                        help='输出格式: table=带格式表格，simple=简单表格，plain=纯文本')
    parser.add_argument('-s', '--save', action='store_true', help='保存余额数据到文件')
    parser.add_argument('-o', '--output-dir', default=DEFAULT_OUTPUT_DIR, help='输出目录路径')
    
    args = parser.parse_args()
    
    try:
        # 创建Gate客户端
        client = GateClient()
        
        # 如果指定了币种，则查询特定币种，否则查询所有币种
        currencies = args.currencies if args.currencies else None
        balances = []
        
        if currencies:
            # 查询特定币种
            for currency in currencies:
                try:
                    balance = client.get_balance(currency.upper())
                    for symbol, amount in balance.items():
                        if amount >= args.min:
                            balances.append({
                                'symbol': symbol,
                                'amount': amount
                            })
                except Exception as e:
                    logger.error(f"获取{currency}余额时出错: {e}")
        else:
            # 查询所有币种
            all_balances = client.get_balance()
            for symbol, amount in all_balances.items():
                if amount >= args.min:
                    balances.append({
                        'symbol': symbol,
                        'amount': amount
                    })
        
        # 按余额降序排序
        balances.sort(key=lambda x: x['amount'], reverse=True)
        
        # 显示结果
        if not balances:
            logger.info("没有满足条件的余额")
            return 0
        
        # 准备表格数据
        table_data = []
        for b in balances:
            table_data.append([
                b['symbol'],
                f"{b['amount']:.8f}"
            ])
        
        # 表格头
        headers = ['币种', '余额']
        
        # 根据指定格式输出
        logger.info("账户余额:")
        if args.format == 'table':
            logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='pretty'))
        elif args.format == 'simple':
            logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='simple'))
        elif args.format == 'plain':
            for row in table_data:
                logger.info(f"{row[0]}: {row[1]}")
        
        # 保存余额数据到文件
        if args.save:
            save_balances_to_file(balances, args.output_dir)
    
    except Exception as e:
        logger.error(f"查询余额时出错: {e}")
        return 1
    
    return 0

def save_balances_to_file(balances, output_dir=DEFAULT_OUTPUT_DIR):
    """保存余额数据到文件"""
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 构建输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(output_dir, f"gate_balances_{timestamp}.json")
        
        # 准备数据
        data = {
            'timestamp': int(datetime.now().timestamp()),
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'balances': balances
        }
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"余额数据已保存到: {output_file}")
        return True
    except Exception as e:
        logger.error(f"保存余额数据时出错: {e}")
        return False

if __name__ == "__main__":
    sys.exit(main()) 