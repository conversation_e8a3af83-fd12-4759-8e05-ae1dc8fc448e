import { MsgBase } from '../../MsgBase.js';
import { CosmosBaseV1Beta1Coin, InjectiveTokenFactoryV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { TypedDataField } from '../../../tx/index.js';
export declare namespace MsgBurn {
    interface Params {
        sender: string;
        burnFromAddress?: string;
        amount: {
            amount: string;
            denom: string;
        };
    }
    type Proto = InjectiveTokenFactoryV1Beta1Tx.MsgBurn;
}
/**
 * @category Messages
 */
export default class MsgBurn extends MsgBase<MsgBurn.Params, MsgBurn.Proto> {
    static fromJSON(params: MsgBurn.Params): MsgBurn;
    toProto(): InjectiveTokenFactoryV1Beta1Tx.MsgBurn;
    toData(): {
        sender: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        burnFromAddress: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            burnFromAddress: string;
            sender: string;
            amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        };
    };
    toWeb3Gw(): {
        burnFromAddress: string;
        sender: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toEip712Types(): Map<string, TypedDataField[]>;
    toDirectSign(): {
        type: string;
        message: InjectiveTokenFactoryV1Beta1Tx.MsgBurn;
    };
    toBinary(): Uint8Array;
}
