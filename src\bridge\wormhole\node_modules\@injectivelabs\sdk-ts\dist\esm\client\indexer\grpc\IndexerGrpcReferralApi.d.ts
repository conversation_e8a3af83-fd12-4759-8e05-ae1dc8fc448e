import { InjectiveReferralRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcReferralApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveReferralRpc.InjectiveReferralRPCClientImpl;
    constructor(endpoint: string);
    fetchReferrerDetails(address: string): Promise<import("../types/referral.js").ReferralDetails>;
    fetchInviteeDetails(address: string): Promise<InjectiveReferralRpc.GetInviteeDetailsResponse>;
    fetchReferrerByCode(code: string): Promise<string>;
}
