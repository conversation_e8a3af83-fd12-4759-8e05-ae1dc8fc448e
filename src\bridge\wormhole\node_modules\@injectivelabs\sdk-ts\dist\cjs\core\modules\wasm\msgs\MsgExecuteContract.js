"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const exceptions_1 = require("@injectivelabs/exceptions");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const utf8_js_1 = require("../../../../utils/utf8.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgExecuteContract extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgExecuteContract(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmwasmWasmV1Tx.MsgExecuteContract.create();
        const msg = this.getMsgObject();
        message.sender = params.sender;
        message.contract = params.contractAddress;
        message.msg = (0, utf8_js_1.fromUtf8)(JSON.stringify(msg));
        if (params.funds) {
            const fundsToArray = Array.isArray(params.funds)
                ? params.funds
                : [params.funds];
            const funds = fundsToArray.map((coin) => {
                const funds = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
                funds.denom = coin.denom;
                funds.amount = coin.amount;
                return funds;
            });
            message.funds = funds;
        }
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgExecuteContract.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmwasm.wasm.v1.MsgExecuteContract',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
            msg: this.getMsgObject(),
        };
        return {
            type: 'wasm/MsgExecuteContract',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmwasm.wasm.v1.MsgExecuteContract',
            ...value,
        };
    }
    // public toEip712(): never {
    //   throw new GeneralException(
    //     new Error(
    //       'EIP712_v1 is not supported for MsgExecuteContract. Please use EIP712_v2',
    //     ),
    //   )
    // }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmwasm.wasm.v1.MsgExecuteContract',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgExecuteContract.encode(this.toProto()).finish();
    }
    getMsgObject() {
        const { params } = this;
        if ((params.exec || params.msg) && params.execArgs) {
            throw new exceptions_1.GeneralException(new Error('Please provide only one exec|msg argument'));
        }
        if (params.execArgs) {
            return params.execArgs.toExecData();
        }
        if (params.exec) {
            return {
                [params.exec.action]: params.exec.msg,
            };
        }
        if (params.msg) {
            return params.msg;
        }
        throw new exceptions_1.GeneralException(new Error('Please provide at least one exec argument'));
    }
}
exports.default = MsgExecuteContract;
