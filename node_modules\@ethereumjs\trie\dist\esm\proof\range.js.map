{"version": 3, "file": "range.js", "sourceRoot": "", "sources": ["../../../src/proof/range.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAE9C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AACtE,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AACjC,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAA;AAInE,iHAAiH;AAEjH;;;;;;;;;;GAUG;AACH,KAAK,UAAU,KAAK,CAClB,IAAU,EACV,MAAgB,EAChB,KAAsB,EACtB,GAAY,EACZ,GAAW,EACX,UAAmB,EACnB,KAAiB;IAEjB,IAAI,KAAK,YAAY,UAAU,EAAE;QAC/B;;;WAGG;QACH,IAAI,UAAU,EAAE;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBACjC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;aACzB;SACF;aAAM;YACL,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBACtC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;aACzB;SACF;QAED,gCAAgC;QAChC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAEjB,4BAA4B;QAC5B,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;QACtC,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA;QACpD,OAAO,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;KACnE;SAAM,IAAI,KAAK,YAAY,aAAa,IAAI,KAAK,YAAY,QAAQ,EAAE;QACtE;;;;WAIG;QACH,IACE,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE;YACpC,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAC7E;YACA,IAAI,UAAU,EAAE;gBACd,IAAI,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;oBACtD,CAAC;oBAAC,MAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;iBACtD;aACF;iBAAM;gBACL,IAAI,cAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE;oBACtD,CAAC;oBAAC,MAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;iBACtD;aACF;YACD,OAAO,GAAG,GAAG,CAAC,CAAA;SACf;QAED,IAAI,KAAK,YAAY,QAAQ,EAAE;YAC7B,2DAA2D;YAC3D,CAAC;YAAC,MAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACrD,OAAO,GAAG,GAAG,CAAC,CAAA;SACf;aAAM;YACL,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;YACnD,IAAI,MAAM,YAAY,QAAQ,EAAE;gBAC9B,iEAAiE;gBACjE,CAAC;gBAAC,MAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;gBACrD,OAAO,GAAG,GAAG,CAAC,CAAA;aACf;YAED,gCAAgC;YAChC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAEjB,4BAA4B;YAC5B,OAAO,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;SACnF;KACF;SAAM,IAAI,KAAK,KAAK,IAAI,EAAE;QACzB,OAAO,GAAG,GAAG,CAAC,CAAA;KACf;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;KAChC;AACH,CAAC;AAED;;;;;;GAMG;AACH,KAAK,UAAU,aAAa,CAAC,IAAU,EAAE,IAAa,EAAE,KAAc;IACpE,eAAe;IACf,IAAI,GAAG,GAAG,CAAC,CAAA;IACX,cAAc;IACd,IAAI,MAAM,GAAoB,IAAI,CAAA;IAClC,eAAe;IACf,IAAI,IAAI,GAAoB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IAC9D,IAAI,aAAsB,CAAA;IAC1B,IAAI,cAAuB,CAAA;IAC3B,6BAA6B;IAC7B,MAAM,KAAK,GAAe,EAAE,CAAA;IAE5B,+CAA+C;IAE/C,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,YAAY,aAAa,IAAI,IAAI,YAAY,QAAQ,EAAE;YAC7D,gCAAgC;YAChC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEhB,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE;gBACxC,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;aAC/D;iBAAM;gBACL,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;aACvF;YAED,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE;gBACzC,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;aACjE;iBAAM;gBACL,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;aACzF;YAED,+FAA+F;YAC/F,IAAI,aAAa,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,EAAE;gBAC/C,MAAK;aACN;YAED,IAAI,IAAI,YAAY,QAAQ,EAAE;gBAC5B,sBAAsB;gBACtB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;aAChC;YAED,4BAA4B;YAC5B,MAAM,GAAG,IAAI,CAAA;YACb,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAA;YACvB,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;SAC3C;aAAM,IAAI,IAAI,YAAY,UAAU,EAAE;YACrC,gCAAgC;YAChC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEhB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;YAE5C,sDAAsD;YACtD,IAAI,QAAQ,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;gBAC3C,MAAK;aACN;YAED,qDAAqD;YACrD,IAAI,CAAC,CAAC,QAAQ,YAAY,UAAU,CAAC,EAAE;gBACrC,IAAI,SAAS,YAAY,UAAU,EAAE;oBACnC,MAAK;iBACN;gBAED,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;oBACxC,MAAK;iBACN;gBAED,IAAI,KAAK,GAAG,KAAK,CAAA;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;wBAC3C,KAAK,GAAG,IAAI,CAAA;wBACZ,MAAK;qBACN;iBACF;gBACD,IAAI,KAAK,EAAE;oBACT,MAAK;iBACN;aACF;iBAAM;gBACL,IAAI,CAAC,CAAC,SAAS,YAAY,UAAU,CAAC,EAAE;oBACtC,MAAK;iBACN;gBAED,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;oBACrC,MAAK;iBACN;aACF;YAED,4BAA4B;YAC5B,MAAM,GAAG,IAAI,CAAA;YACb,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;YACtC,GAAG,IAAI,CAAC,CAAA;SACT;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;IAED,+EAA+E;IAE/E,MAAM,SAAS,GAAG,CAAC,GAAY,EAAE,KAAiB,EAAE,EAAE;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IACvC,CAAC,CAAA;IAED,IAAI,IAAI,YAAY,aAAa,IAAI,IAAI,YAAY,QAAQ,EAAE;QAC7D;;;;;;;WAOG;QACH,MAAM,gCAAgC,GAAG,KAAK,EAAE,GAAY,EAAE,EAAE;YAC9D,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,OAAO,IAAI,CAAA;aACZ;YAED,KAAK,CAAC,GAAG,EAAE,CACV;YAAC,MAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;YACrD,MAAM,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;YAC7C,OAAO,KAAK,CAAA;QACd,CAAC,CAAA;QAED,IAAI,aAAa,KAAK,CAAC,CAAC,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;SACjC;QAED,IAAI,aAAa,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAA;SACjC;QAED,IAAI,aAAa,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC,EAAE;YAC/C,wBAAwB;YACxB,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAA;SAC9C;QAED,kBAAkB;QAClB,IAAI,cAAc,KAAK,CAAC,EAAE;YACxB,IAAI,IAAI,YAAY,QAAQ,EAAE;gBAC5B,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAA;aAC9C;YAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,KAAK,YAAY,QAAQ,EAAE;gBAC7B,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAA;aAC9C;YAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;YAC9F,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,CAAA;YAEnD,OAAO,KAAK,CAAA;SACb;QAED,mBAAmB;QACnB,IAAI,aAAa,KAAK,CAAC,EAAE;YACvB,IAAI,IAAI,YAAY,QAAQ,EAAE;gBAC5B,OAAO,gCAAgC,CAAC,KAAK,CAAC,CAAA;aAC/C;YAED,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,KAAK,YAAY,QAAQ,EAAE;gBAC7B,OAAO,gCAAgC,CAAC,KAAK,CAAC,CAAA;aAC/C;YAED,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;YAC9F,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,CAAA;YAEpD,OAAO,KAAK,CAAA;SACb;QAED,OAAO,KAAK,CAAA;KACb;SAAM,IAAI,IAAI,YAAY,UAAU,EAAE;QACrC,4CAA4C;QAC5C,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;SACxB;QAED;YACE;;;;eAIG;YACH,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YACtC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA;YACnD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAA;YAChF,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;SACrD;QAED;YACE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,CAAA;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;YACvC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA;YACnD,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;YAChF,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;SACtD;QAED,OAAO,KAAK,CAAA;KACb;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;KAChC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,KAAK,UAAU,WAAW,CACxB,QAAoB,EACpB,GAAe,EACf,KAAmB,EACnB,qBAAuC;IAEvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QAC5C,IAAI,EAAE,QAAQ;QACd,qBAAqB;KACtB,CAAC,CAAA;IACF,IAAI;QACF,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QAC5C,OAAO;YACL,IAAI,EAAE,SAAS;YACf,KAAK;SACN,CAAA;KACF;IAAC,OAAO,GAAQ,EAAE;QACjB,IAAI,GAAG,CAAC,OAAO,KAAK,oBAAoB,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;SAC1C;aAAM;YACL,MAAM,GAAG,CAAA;SACV;KACF;AACH,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,eAAe,CAAC,IAAU,EAAE,GAAY;IACrD,IAAI,GAAG,GAAG,CAAC,CAAA;IACX,IAAI,IAAI,GAAoB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IAC9D,OAAO,IAAI,KAAK,IAAI,EAAE;QACpB,IAAI,IAAI,YAAY,UAAU,EAAE;YAC9B,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBAC9B,OAAO,IAAI,CAAA;iBACZ;aACF;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;YACrC,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA;YAC5C,GAAG,IAAI,CAAC,CAAA;SACT;aAAM,IAAI,IAAI,YAAY,aAAa,EAAE;YACxC,IACE,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE;gBACnC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,EAC3E;gBACA,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;aACzD;YAED,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAA;YACvB,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SAC1C;aAAM,IAAI,IAAI,YAAY,QAAQ,EAAE;YACnC,OAAO,KAAK,CAAA;SACb;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAA;SAChC;KACF;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,QAAoB,EACpB,QAAwB,EACxB,OAAuB,EACvB,IAAe,EACf,MAAoB,EACpB,KAA0B,EAC1B,qBAAuC;IAEvC,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;KACxD;IAED,kCAAkC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;YAC7C,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;SACtC;KACF;IACD,mCAAmC;IACnC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;SAClC;KACF;IAED,qBAAqB;IACrB,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;QAC3D,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,qBAAqB,EAAE,CAAC,CAAA;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;SACnD;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;SAC7D;QACD,OAAO,KAAK,CAAA;KACb;IAED,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;QAC3D,qBAAqB;QACrB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,WAAW,CACvC,QAAQ,EACR,cAAc,CAAC,QAAQ,CAAC,EACxB,KAAK,EACL,qBAAqB,CACtB,CAAA;YAED,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE;gBAC7D,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;aAC9D;YAED,OAAO,KAAK,CAAA;SACb;KACF;IAED,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;QAC3D,MAAM,IAAI,KAAK,CACb,oFAAoF,CACrF,CAAA;KACF;IAED,oBAAoB;IACpB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,WAAW,CACvC,QAAQ,EACR,cAAc,CAAC,QAAQ,CAAC,EACxB,KAAK,EACL,qBAAqB,CACtB,CAAA;QAED,IAAI,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAA;SAClF;QACD,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YACpD,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;SAC7D;QAED,OAAO,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;KACvC;IAED,0BAA0B;IAC1B,IAAI,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;QAC1C,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAA;KACzF;IACD,IAAI,QAAQ,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;QACtC,MAAM,IAAI,KAAK,CACb,kGAAkG,CACnG,CAAA;KACF;IAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QACvC,qBAAqB;QACrB,IAAI,EAAE,QAAQ;KACf,CAAC,CAAA;IAEF,2CAA2C;IAC3C,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC1D,IAAI,KAAK,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;KAChC;IAED,+BAA+B;IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;KACnD;IAED,mBAAmB;IACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAA;KAClE;IAED,OAAO,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAA;AACrD,CAAC"}