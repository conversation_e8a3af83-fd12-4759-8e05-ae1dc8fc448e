export declare const generateArbitrarySignDoc: (message: string, signer: string) => {
    signDoc: {
        account_number: string;
        chain_id: string;
        fee: {
            amount: never[];
            gas: string;
        };
        memo: string;
        msgs: {
            type: string;
            value: {
                data: string;
                signer: string;
            };
        }[];
        sequence: string;
    };
    signDocBuff: Buffer<ArrayBuffer>;
    stringifiedSignDoc: string;
};
