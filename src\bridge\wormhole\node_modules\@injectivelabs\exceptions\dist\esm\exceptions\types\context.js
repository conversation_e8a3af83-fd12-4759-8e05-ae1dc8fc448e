export var HttpRequestMethod;
(function (HttpRequestMethod) {
    HttpRequestMethod["Get"] = "GET";
    HttpRequestMethod["Post"] = "POST";
    HttpRequestMethod["Options"] = "OPTIONS";
})(HttpRequestMethod || (HttpRequestMethod = {}));
export var ErrorType;
(function (ErrorType) {
    ErrorType["Unspecified"] = "unspecified";
    ErrorType["ChainError"] = "chain-error";
    ErrorType["ExecutionError"] = "execution-error";
    ErrorType["NotFoundError"] = "not-found-error";
    ErrorType["ValidationError"] = "validation-error";
    ErrorType["WalletError"] = "wallet-error";
    ErrorType["WalletNotInstalledError"] = "wallet-not-installed-error";
    ErrorType["GrpcUnaryRequest"] = "grpc-unary-request";
    ErrorType["HttpRequest"] = "http-request";
    ErrorType["Web3"] = "web3";
    ErrorType["Web3Gateway"] = "web3-gateway";
})(ErrorType || (ErrorType = {}));
