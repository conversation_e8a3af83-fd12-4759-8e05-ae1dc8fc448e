# IP轮换器 PowerShell脚本
# 使用方法: .\ip_rotator.ps1 [command] [options]

param(
    [Parameter(Position=0)]
    [string]$Command = "",
    
    [Parameter(Position=1)]
    [string]$ProxyName = "",
    
    [string]$Config = "config\ip.yaml"
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

$PythonScript = "src\utils\ip\ip_rotator.py"

# 检查文件是否存在
if (-not (Test-Path $PythonScript)) {
    Write-Host "错误: 找不到Python脚本 $PythonScript" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $Config)) {
    Write-Host "错误: 找不到配置文件 $Config" -ForegroundColor Red
    exit 1
}

# 显示帮助信息
function Show-Help {
    Write-Host ""
    Write-Host "IP轮换器 - 自动切换代理IP" -ForegroundColor Green
    Write-Host ""
    Write-Host "使用方法:"
    Write-Host "  .\ip_rotator.ps1 [command] [options]"
    Write-Host ""
    Write-Host "可用命令:" -ForegroundColor Yellow
    Write-Host "  start          启动IP自动轮换（每分钟切换一次）"
    Write-Host "  stop           停止IP轮换"
    Write-Host "  status         显示当前状态"
    Write-Host "  test           测试所有代理连接"
    Write-Host "  switch [name]  手动切换代理（可指定代理名称）"
    Write-Host "  list           列出所有可用代理"
    Write-Host "  help           显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Cyan
    Write-Host "  .\ip_rotator.ps1 start                启动自动轮换"
    Write-Host "  .\ip_rotator.ps1 status               查看状态"
    Write-Host "  .\ip_rotator.ps1 switch 香港          切换到香港节点"
    Write-Host "  .\ip_rotator.ps1 test                 测试所有代理"
    Write-Host ""
}

# 执行Python脚本
function Invoke-PythonScript {
    param(
        [string]$Arguments
    )
    
    try {
        $process = Start-Process -FilePath "python" -ArgumentList "$PythonScript $Arguments" -Wait -PassThru -NoNewWindow
        return $process.ExitCode
    }
    catch {
        Write-Host "执行Python脚本失败: $_" -ForegroundColor Red
        return 1
    }
}

# 处理命令
switch ($Command.ToLower()) {
    "start" {
        Write-Host "启动IP轮换器..." -ForegroundColor Green
        $exitCode = Invoke-PythonScript "start --config `"$Config`""
        if ($exitCode -eq 0) {
            Write-Host "IP轮换器启动成功" -ForegroundColor Green
        } else {
            Write-Host "IP轮换器启动失败" -ForegroundColor Red
        }
    }
    
    "stop" {
        Write-Host "停止IP轮换器..." -ForegroundColor Yellow
        $exitCode = Invoke-PythonScript "stop --config `"$Config`""
        if ($exitCode -eq 0) {
            Write-Host "IP轮换器已停止" -ForegroundColor Green
        } else {
            Write-Host "停止IP轮换器失败" -ForegroundColor Red
        }
    }
    
    "status" {
        Write-Host "查询IP轮换器状态..." -ForegroundColor Cyan
        Invoke-PythonScript "status --config `"$Config`""
    }
    
    "test" {
        Write-Host "测试代理连接..." -ForegroundColor Cyan
        Invoke-PythonScript "test --config `"$Config`""
    }
    
    "switch" {
        if ($ProxyName) {
            Write-Host "切换到代理: $ProxyName" -ForegroundColor Green
            Invoke-PythonScript "switch --config `"$Config`" --name `"$ProxyName`""
        } else {
            Write-Host "随机切换代理..." -ForegroundColor Green
            Invoke-PythonScript "switch --config `"$Config`""
        }
    }
    
    "list" {
        Write-Host "列出所有代理..." -ForegroundColor Cyan
        Invoke-PythonScript "list --config `"$Config`""
    }
    
    "help" {
        Show-Help
    }
    
    "" {
        Show-Help
    }
    
    default {
        Write-Host "未知命令: $Command" -ForegroundColor Red
        Show-Help
    }
}
