{"name": "web3-core-requestmanager", "version": "1.10.4", "description": "Web3 module to handle requests to external providers.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-core-requestmanager", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "main": "lib/index.js", "scripts": {"compile": "tsc -b tsconfig.json"}, "dependencies": {"util": "^0.12.5", "web3-core-helpers": "1.10.4", "web3-providers-http": "1.10.4", "web3-providers-ipc": "1.10.4", "web3-providers-ws": "1.10.4"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}