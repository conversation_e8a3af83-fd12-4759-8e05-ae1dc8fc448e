import { BaseWasmQuery } from '../../BaseWasmQuery.js';
import { toBase64 } from '../../../../utils/index.js';
export class QueryGetAllLendingRates extends BaseWasmQuery {
    toPayload() {
        const payload = {
            get_all_lending_rates: {
                ...(this.params.limit !== undefined ? { limit: this.params.limit } : {}),
                ...(this.params.startAfter ? { start_after: this.params.startAfter } : {}),
            },
        };
        return toBase64(payload);
    }
}
