# Polygon-Ethereum 桥接工具

这个工具允许用户在 Polygon PoS 链和以太坊主网之间双向桥接代币。它基于官方的 Polygon PoS 桥接机制，该机制是 Polygon 网络认可的安全桥接方式。

## 快速开始

### 从 Polygon 到以太坊的桥接

```bash
# 直接进行 claim 操作
python -m src.bridge.pol_bridge.bridge_tokens claim --tx-hash 0x08e201d3978159dccbd6d806025cb97dfddc087fb65fc0082a9c7ec10f33b2ae

# 从 Polygon 转移到以太坊
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-address ****************************************** --amount 2.407
```

### 从以太坊到 Polygon 的桥接

```bash
# 从以太坊转移到 Polygon
python -m src.bridge.pol_bridge.bridge_tokens to-polygon --token-address ****************************************** --amount 0.01 --direct
```

注意事项：
1. claim 操作会自动检查交易是否已被包含在检查点中
2. 如果交易尚未被包含在检查点中，会每30秒重试一次
3. 最大重试时间为48小时

## 主要功能

- 在以太坊和Polygon之间双向桥接ERC20代币
- 使用最新官方合约地址和API
- 自动处理代币授权流程
- 提供实时的交易状态查询
- 支持多种常见代币

## 使用说明

详细的使用说明请查看 `bridge_tokens_usage.md` 文件。

## 重要注意事项

- 从以太坊到Polygon的桥接通常在15-20分钟内完成
- 从Polygon到以太坊的桥接需要等待Polygon检查点确认，通常需要3-6小时
- 在进行桥接操作前，请确保您有足够的代币余额和原生代币(ETH/MATIC)支付燃气费
- 首次使用建议先尝试小额转账以验证功能正常

## 技术说明

该工具使用官方Polygon PoS桥的合约进行操作：

- Polygon上的代币使用内置的`withdraw()`方法进行销毁
- 以太坊上使用`RootChainManager`合约的`depositFor()`方法将代币存入Polygon

## 相关链接

- [Polygon Portal](https://portal.polygon.technology/bridge)
- [Polygon 桥接文档](https://docs.polygon.technology/tools/)
- [Polygon Token Mapper](https://mapper.polygon.technology/)

## 安装

首先安装所需依赖：

```bash
pip install -r src/bridge/pol_bridge/requirements.txt
```

## 使用方法

该工具提供以下命令：

### 查询代币跨链映射地址

```bash
# 查询Polygon链上代币在以太坊的映射地址
python -m src.bridge.pol_bridge.check_token ****************************************** --direct

# 查询以太坊链上代币在Polygon的映射地址
python -m src.bridge.pol_bridge.check_token ****************************************** --chain ethereum --direct

python src/bridge/pol_bridge/check_token.py --root-eth-pol --query-contract

# 查询并保存映射信息到本地
python -m src.bridge.pol_bridge.check_token ****************************************** --save

# 显示详细信息
python -m src.bridge.pol_bridge.check_token ****************************************** --verbose
```

```bash
#以太坊claim命令
python -m src.bridge.pol_bridge.bridge_tokens claim --burn-tx-hash 0x57579dd67d34d2eef46ed258b77893603e33907061031645b848762a3f1e9ff6
```
```bash
#polygon to eth
python -m src.bridge.pol_bridge.bridge_tokens to-ethereum --token-address ****************************************** --amount 2.407 --auto-claim  

验证通过5月10日
#eth to polygon
python -m src.bridge.pol_bridge.bridge_tokens to-polygon --token-address ****************************************** --amount 0.01 --direct  --auto-claim
 
 5月日通过
```
### 列出可桥接的代币

```bash
python -m src.bridge.pol_bridge list-tokens \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY
```

### 查询代币余额

```bash
python -m src.bridge.pol_bridge balance \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --chain polygon \
  --token-symbol USDT
```

或者使用代币地址：

```bash
python -m src.bridge.pol_bridge balance \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --chain polygon \
  --token-address ******************************************
```

### 从 Polygon 提取代币到以太坊（第一步）

```bash
python -m src.bridge.pol_bridge withdraw \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --token-symbol USDT \
  --amount 10
```

### 从以太坊存入代币到Polygon

```bash
python -m src.bridge.pol_bridge deposit \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --token-symbol USDT \
  --amount 10
```

### 在以太坊上认领代币（第二步）

注意：此步骤必须在第一步的交易被包含在检查点后执行（通常需要几个小时）。

```bash
python -m src.bridge.pol_bridge claim \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --burn-tx-hash 0x123...
```

### 一步完成桥接（自动执行相关步骤）

从Polygon到以太坊：

```bash
python -m src.bridge.pol_bridge bridge \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --token-symbol USDT \
  --amount 10 \
  --direction to-ethereum
```

从以太坊到Polygon：

```bash
python -m src.bridge.pol_bridge bridge \
  --eth-rpc https://ethereum-rpc-url \
  --polygon-rpc https://polygon-rpc-url \
  --private-key-env ETH_PRIVATE_KEY \
  --token-symbol USDT \
  --amount 10 \
  --direction to-polygon
```

注意：
- 如果是从Polygon到以太坊，且检查点尚未包含您的交易，桥接命令会处理第一步，然后提供执行第二步的说明。
- 如果是从以太坊到Polygon，通常需要等待约20分钟才能在Polygon上看到代币。

## 桥接过程参数

- `--eth-rpc`: 以太坊 RPC URL
- `--polygon-rpc`: Polygon RPC URL
- `--private-key`: 以太坊私钥（不推荐，仅用于测试）
- `--private-key-env`: 包含私钥的环境变量名
- `--gas-price`: 燃气价格（Gwei），默认 50
- `--gas-limit`: 燃气上限，默认 500000
- `--max-fee-per-gas`: EIP-1559 最大费用（Gwei）
- `--max-priority-fee-per-gas`: EIP-1559 最大优先费用（Gwei）

## 桥接过程说明

### 从 Polygon 到以太坊的桥接

这是一个两步式过程：

1. **提取（Withdraw）**: 在 Polygon 上销毁代币，生成桥接请求
2. **认领（Claim）**: 在以太坊上使用证明认领等量的代币

两个步骤之间必须等待 Polygon 的检查点包含您的交易，这通常需要几个小时。您可以在 https://mapper.polygon.technology/ 查看交易的检查点状态。

### 从以太坊到Polygon的桥接

这是一个单步过程：

1. **存入（Deposit）**: 在以太坊上存入代币，代币会自动在Polygon上生成

这个过程通常需要等待20分钟左右，代币就会自动出现在Polygon网络上的同一地址中。

## 代码示例

以下是在 Python 代码中使用此功能的示例：

```python
import asyncio
import os
from web3 import Web3
from eth_account import Account
from src.bridge.pol_bridge import PolygonBridgeClient, PolygonBridgeTokens

async def bridge_tokens_to_ethereum():
    # 设置账户
    private_key = os.environ.get("ETH_PRIVATE_KEY")
    account = Account.from_key(private_key)
    
    # 创建客户端
    client = PolygonBridgeClient(
        ethereum_rpc_url="https://ethereum-rpc-url",
        polygon_rpc_url="https://polygon-rpc-url",
        account=account,
        gas_price_gwei=50
    )
    
    # 获取USDT代币信息
    token_info = PolygonBridgeTokens.get_token_by_symbol("USDT", PolygonBridgeTokens.POLYGON_CHAIN_ID)
    
    # 桥接10 USDT到以太坊
    amount = 10 * (10 ** token_info["decimals"])
    result = await client.bridge_token_from_polygon_to_ethereum(token_info["address"], amount)
    
    print(result)

async def bridge_tokens_to_polygon():
    # 设置账户
    private_key = os.environ.get("ETH_PRIVATE_KEY")
    account = Account.from_key(private_key)
    
    # 创建客户端
    client = PolygonBridgeClient(
        ethereum_rpc_url="https://ethereum-rpc-url",
        polygon_rpc_url="https://polygon-rpc-url",
        account=account,
        gas_price_gwei=50
    )
    
    # 获取USDT代币信息
    token_info = PolygonBridgeTokens.get_token_by_symbol("USDT", PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
    
    # 桥接10 USDT到Polygon
    amount = 10 * (10 ** token_info["decimals"])
    result = await client.bridge_token_from_ethereum_to_polygon(token_info["address"], amount)
    
    print(result)

if __name__ == "__main__":
    # 选择要执行的桥接方向
    asyncio.run(bridge_tokens_to_ethereum())
    # 或者
    # asyncio.run(bridge_tokens_to_polygon())
```

## 注意事项

- 请确保您的账户有足够的代币进行桥接，以及足够的原生代币（MATIC 和 ETH）支付燃气费
- 从Polygon到以太坊的桥接过程可能需要几个小时才能完成，请耐心等待
- 从以太坊到Polygon的桥接通常只需要约20分钟
- 建议先小额测试，然后再桥接大量代币
- 始终保存交易哈希，以便需要时查询交易状态

## check_token 工具参数

- `address`：要查询的代币地址（必填）
- `--chain`：指定源链（'polygon' 或 'ethereum'，默认为 'polygon'）
- `--direct`：直接使用综合查询方法
- `--query-contract`：仅使用合约查询
- `--skip-contract`：跳过合约查询
- `--skip-local`：跳过本地文件查询
- `--save`：保存查询结果到本地文件
- `--no-save`：不保存查询结果到本地文件
- `--timeout`：查询超时时间（秒），默认 30 秒
- `--retry`：重试次数，默认 3 次
- `--verbose`：显示详细信息

## 桥接过程参数

- `--eth-rpc`: 以太坊 RPC URL
- `--polygon-rpc`: Polygon RPC URL
- `--private-key`: 以太坊私钥（不推荐，仅用于测试）
- `--private-key-env`: 包含私钥的环境变量名
- `--gas-price`: 燃气价格（Gwei），默认 50
- `--gas-limit`: 燃气上限，默认 500000
- `--max-fee-per-gas`: EIP-1559 最大费用（Gwei）
- `--max-priority-fee-per-gas`: EIP-1559 最大优先费用（Gwei）

## 桥接过程说明

从 Polygon 到以太坊的桥接是一个两步式过程：

1. **提取（Withdraw）**: 在 Polygon 上销毁代币，生成桥接请求
2. **认领（Claim）**: 在以太坊上使用证明认领等量的代币

两个步骤之间必须等待 Polygon 的检查点包含您的交易，这通常需要几个小时。您可以在 https://mapper.polygon.technology/ 查看交易的检查点状态。

## 代码示例

以下是在 Python 代码中使用此功能的示例：

```python
import asyncio
import os
from web3 import Web3
from eth_account import Account
from src.bridge.pol_bridge import PolygonBridgeClient, PolygonBridgeTokens

async def bridge_tokens():
    # 设置账户
    private_key = os.environ.get("ETH_PRIVATE_KEY")
    account = Account.from_key(private_key)
    
    # 创建客户端
    client = PolygonBridgeClient(
        ethereum_rpc_url="https://ethereum-rpc-url",
        polygon_rpc_url="https://polygon-rpc-url",
        account=account,
        gas_price_gwei=50
    )
    
    # 获取USDT代币信息
    token_info = PolygonBridgeTokens.get_token_by_symbol("USDT", PolygonBridgeTokens.POLYGON_CHAIN_ID)
    
    # 桥接10 USDT
    amount = 10 * (10 ** token_info["decimals"])
    result = await client.bridge_token_from_polygon_to_ethereum(token_info["address"], amount)
    
    print(result)

if __name__ == "__main__":
    asyncio.run(bridge_tokens())
```

## 查询代币映射关系示例

以下是在 Python 代码中查询代币映射关系的示例：

```python
from src.bridge.pol_bridge.check_token import query_token_mapping

# 查询 Polygon 上的代币在以太坊的映射关系
result = query_token_mapping("******************************************", "polygon")

if result["success"]:
    print(f"映射成功！以太坊地址: {result['ethereum_address']}")
    print(f"代币名称: {result['name']}")
    print(f"代币符号: {result['symbol']}")
else:
    print(f"映射失败: {result.get('message', '未知错误')}")
```

## 注意事项

- 请确保您的账户有足够的代币进行桥接，以及足够的原生代币（MATIC 和 ETH）支付燃气费
- 桥接过程可能需要几个小时才能完成，请耐心等待
- 建议先小额测试，然后再桥接大量代币
- 始终保存第一步（withdraw）的交易哈希，因为您稍后需要它来完成第二步（claim）

## 代币交易监控功能

该工具用于监控以太坊和Polygon网络上特定代币的最近交易，能够检测大额转账和DEX交易。

### 使用方法

基本用法:
```bash
python -m src.bridge.pol_bridge.monitor_token_tx 0x代币地址 --minutes 10
```

完整参数:
```bash
python -m src.bridge.pol_bridge.monitor_token_tx 0x代币地址 [--minutes 分钟数] [--chains polygon ethereum] [--api-keys-file 密钥文件] [--verbose]
```

参数说明:
- `token_address`: 要监控的代币合约地址
- `--minutes`: 查询过去几分钟的交易(默认: 10)
- `--chains`: 要监控的区块链(默认: polygon ethereum)
- `--api-keys-file`: 包含API密钥的JSON文件路径(可选，格式: `{"polygonscan": "YOUR_KEY", "etherscan": "YOUR_KEY"}`)
- `--verbose`: 显示详细交易信息(包括区块号和Gas消耗)

### 功能特点

1. 监控多链上的代币交易
2. 自动识别DEX交易
3. 支持主流DEX(Uniswap, SushiSwap, QuickSwap等)
4. 显示交易金额、时间和类型

### 使用示例

监控VEE代币在Polygon上最近5分钟的交易:
```bash
python -m src.bridge.pol_bridge.monitor_token_tx ****************************************** --minutes 200 --chains polygon --verbose
```

监控USDT在以太坊上最近30分钟的交易，显示详细信息:
```bash
python -m src.bridge.pol_bridge.monitor_token_tx ****************************************** --minutes 30 --chains ethereum --verbose
```

## 简化的双向桥接工具

我们还提供了一个简化版的双向桥接工具，它使用统一的命令行接口，使桥接操作更加简单：

```bash
# 列出可桥接的代币
python -m src.bridge.pol_bridge.bridge_tokens list

# Windows PowerShell环境下使用（所有参数在一行内）：
# 从Polygon到以太坊桥接代币（使用代币符号）





# 查询VEE代币在Polygon上的余额
python -m src.bridge.pol_bridge.bridge_tokens balance --chain polygon --token-address ******************************************

# Linux/Mac终端下使用（可使用换行符）:
# 从Polygon到以太坊桥接代币
#   --token-symbol USDT \
#   --amount 10
```

脚本将自动从项目配置文件 `config/config.yaml` 中读取网络 RPC URL 和私钥信息，无需手动提供。同时，该脚本已针对 Windows 系统进行优化，确保在 Windows 环境下正常运行和显示中文。

更多详细用法请参考 [bridge_tokens_usage.md](./bridge_tokens_usage.md) 