{"version": 3, "sources": ["../../src/transactions/instances/identifier.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\n\n/**\n * Represents an Identifier that can be serialized and deserialized.\n * This class is used to denote the module \"name\" in \"ModuleId\" and\n * the \"function name\" in \"EntryFunction\".\n *\n * @extends Serializable\n * @group Implementation\n * @category Transactions\n */\nexport class Identifier extends Serializable {\n  public identifier: string;\n\n  /**\n   * Creates an instance of the class with a specified identifier.\n   *\n   * @param identifier - The unique identifier for the instance.\n   * @group Implementation\n   * @category Transactions\n   */\n  constructor(identifier: string) {\n    super();\n    this.identifier = identifier;\n  }\n\n  /**\n   * Serializes the identifier of the current instance using the provided serializer.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category Transactions\n   */\n  public serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.identifier);\n  }\n\n  /**\n   * Deserializes an identifier from the provided deserializer.\n   * This function is useful for reconstructing an Identifier object from a serialized format.\n   *\n   * @param deserializer - The deserializer instance used to read the serialized data.\n   * @group Implementation\n   * @category Transactions\n   */\n  static deserialize(deserializer: Deserializer): Identifier {\n    const identifier = deserializer.deserializeStr();\n    return new Identifier(identifier);\n  }\n}\n"], "mappings": "yCAeO,IAAMA,EAAN,MAAMC,UAAmBC,CAAa,CAU3C,YAAYC,EAAoB,CAC9B,MAAM,EACN,KAAK,WAAaA,CACpB,CASO,UAAUC,EAA8B,CAC7CA,EAAW,aAAa,KAAK,UAAU,CACzC,CAUA,OAAO,YAAYC,EAAwC,CACzD,IAAMF,EAAaE,EAAa,eAAe,EAC/C,OAAO,IAAIJ,EAAWE,CAAU,CAClC,CACF", "names": ["Identifier", "_Identifier", "Serializable", "identifier", "serializer", "deserializer"]}