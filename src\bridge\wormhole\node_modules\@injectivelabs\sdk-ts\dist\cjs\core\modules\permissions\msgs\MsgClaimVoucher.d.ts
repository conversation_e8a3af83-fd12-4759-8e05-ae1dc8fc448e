import { MsgBase } from '../../MsgBase.js';
import { InjectivePermissionsV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgClaimVoucher {
    interface Params {
        sender: string;
        denom: string;
    }
    type Proto = InjectivePermissionsV1Beta1Tx.MsgClaimVoucher;
}
/**
 * @category Messages
 */
export default class MsgClaimVoucher extends MsgBase<MsgClaimVoucher.Params, MsgClaimVoucher.Proto> {
    static fromJSON(params: MsgClaimVoucher.Params): MsgClaimVoucher;
    toProto(): InjectivePermissionsV1Beta1Tx.MsgClaimVoucher;
    toData(): {
        sender: string;
        denom: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            denom: string;
        };
    };
    toWeb3Gw(): {
        sender: string;
        denom: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectivePermissionsV1Beta1Tx.MsgClaimVoucher;
    };
    toBinary(): Uint8Array;
}
