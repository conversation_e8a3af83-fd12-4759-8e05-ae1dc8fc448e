"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEthereumSignerAddress = exports.getInjectiveSignerAddress = exports.createAny = exports.createAnyMessage = void 0;
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const index_js_1 = require("../../../utils/index.js");
const createAnyMessage = (msg) => {
    const message = core_proto_ts_1.GoogleProtobufAny.Any.create();
    message.typeUrl = `${msg.type.startsWith('/') ? '' : '/'}${msg.type}`;
    message.value = msg.value;
    return message;
};
exports.createAnyMessage = createAnyMessage;
const createAny = (value, type) => {
    const message = core_proto_ts_1.GoogleProtobufAny.Any.create();
    message.typeUrl = type;
    message.value = value;
    return message;
};
exports.createAny = createAny;
const getInjectiveSignerAddress = (address) => {
    if (!address) {
        return '';
    }
    if (address.startsWith('inj')) {
        return address;
    }
    if (address.startsWith('0x')) {
        return (0, index_js_1.getInjectiveAddress)(address);
    }
    return address;
};
exports.getInjectiveSignerAddress = getInjectiveSignerAddress;
const getEthereumSignerAddress = (address) => {
    if (!address) {
        return '';
    }
    if (address.startsWith('0x')) {
        return address;
    }
    if (address.startsWith('inj')) {
        return (0, index_js_1.getEthereumAddress)(address);
    }
    return `0x${address}`;
};
exports.getEthereumSignerAddress = getEthereumSignerAddress;
