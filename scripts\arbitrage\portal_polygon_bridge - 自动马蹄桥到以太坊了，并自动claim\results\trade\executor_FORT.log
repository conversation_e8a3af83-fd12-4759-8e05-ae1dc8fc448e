2025-05-19 10:21:25,452 - INFO - ================================================================================
2025-05-19 10:21:25,452 - INFO - 开始执行 FORT 买入交易 - 时间: 2025-05-19 10:21:25
2025-05-19 10:21:25,452 - INFO - 链: ethereum, 投入金额: 200.0 USDT
2025-05-19 10:21:25,452 - INFO - 代币地址: ******************************************
2025-05-19 10:21:25,452 - INFO - 收到FORT买入请求 - 链:ethereum, 投入:200.0USDT
2025-05-19 10:21:25,452 - INFO - FORT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:21:25,452 - INFO - FORT: 准备使用KyberSwap在ethereum上执行200.0USDT买入FORT交易
2025-05-19 10:21:25,452 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:21:25,452 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:21:25,452 - INFO - FORT: 准备调用swap_tokens函数，参数：
2025-05-19 10:21:25,452 - INFO -   chain: ethereum
2025-05-19 10:21:25,452 - INFO -   token_in: USDT
2025-05-19 10:21:25,452 - INFO -   token_out: ******************************************
2025-05-19 10:21:25,452 - INFO -   amount: 200.0
2025-05-19 10:21:25,452 - INFO -   slippage: 0.5%
2025-05-19 10:21:25,453 - INFO -   real: True
2025-05-19 10:21:27,771 - INFO - FORT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:21:27,771 - INFO - FORT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 200.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 200.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '200000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:21:27,772 - ERROR - FORT: 交易失败 - 代币余额不足。请求: 200.0 USDT，可用: 67.81312 USDT
2025-05-19 10:21:27,777 - INFO - 读取到 130 条现有交易记录
2025-05-19 10:21:27,777 - INFO - 添加新交易记录: FORT (FORT_200.0_2025-05-19 10:21:25)
2025-05-19 10:21:27,790 - INFO - 成功保存 131 条交易记录
2025-05-19 10:21:27,813 - INFO - FORT: 买入交易处理完成，耗时: 2.36秒
2025-05-19 10:21:27,813 - INFO - ================================================================================
2025-05-19 11:05:57,889 - INFO - ================================================================================
2025-05-19 11:05:57,889 - INFO - 开始执行 FORT 买入交易 - 时间: 2025-05-19 11:05:57
2025-05-19 11:05:57,889 - INFO - 链: ethereum, 投入金额: 183.33 USDT
2025-05-19 11:05:57,889 - INFO - 代币地址: ******************************************
2025-05-19 11:05:57,889 - INFO - 收到FORT买入请求 - 链:ethereum, 投入:183.33USDT
2025-05-19 11:05:57,889 - INFO - FORT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:05:57,889 - INFO - FORT: 准备使用KyberSwap在ethereum上执行183.33USDT买入FORT交易
2025-05-19 11:05:57,889 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:05:57,889 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:05:57,889 - INFO - FORT: 准备调用swap_tokens函数，参数：
2025-05-19 11:05:57,889 - INFO -   chain: ethereum
2025-05-19 11:05:57,889 - INFO -   token_in: USDT
2025-05-19 11:05:57,889 - INFO -   token_out: ******************************************
2025-05-19 11:05:57,889 - INFO -   amount: 183.33
2025-05-19 11:05:57,889 - INFO -   slippage: 0.5%
2025-05-19 11:05:57,889 - INFO -   real: True
2025-05-19 11:05:59,354 - INFO - FORT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:05:59,355 - INFO - FORT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 183.33 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 183.33, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '183330000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:05:59,358 - ERROR - FORT: 交易失败 - 代币余额不足。请求: 183.33 USDT，可用: 67.81312 USDT
2025-05-19 11:05:59,359 - INFO - 读取到 134 条现有交易记录
2025-05-19 11:05:59,359 - INFO - 添加新交易记录: FORT (FORT_183.33_2025-05-19 11:05:57)
2025-05-19 11:05:59,361 - INFO - 成功保存 135 条交易记录
2025-05-19 11:05:59,361 - INFO - FORT: 买入交易处理完成，耗时: 1.47秒
2025-05-19 11:05:59,361 - INFO - ================================================================================
