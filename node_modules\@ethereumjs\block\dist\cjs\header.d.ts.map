{"version": 3, "file": "header.d.ts", "sourceRoot": "", "sources": ["../../src/header.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,MAAM,EAA+C,MAAM,oBAAoB,CAAA;AAE/F,OAAO,EACL,OAAO,EAsBR,MAAM,kBAAkB,CAAA;AAMzB,OAAO,KAAK,EAAE,gBAAgB,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,YAAY,CAAA;AAIxF,UAAU,WAAW;IACnB,IAAI,EAAE,UAAU,GAAG,SAAS,CAAA;CAC7B;AAID;;GAEG;AACH,qBAAa,WAAW;IACtB,SAAgB,UAAU,EAAE,UAAU,CAAA;IACtC,SAAgB,SAAS,EAAE,UAAU,CAAA;IACrC,SAAgB,QAAQ,EAAE,OAAO,CAAA;IACjC,SAAgB,SAAS,EAAE,UAAU,CAAA;IACrC,SAAgB,gBAAgB,EAAE,UAAU,CAAA;IAC5C,SAAgB,WAAW,EAAE,UAAU,CAAA;IACvC,SAAgB,SAAS,EAAE,UAAU,CAAA;IACrC,SAAgB,UAAU,EAAE,MAAM,CAAA;IAClC,SAAgB,MAAM,EAAE,MAAM,CAAA;IAC9B,SAAgB,QAAQ,EAAE,MAAM,CAAA;IAChC,SAAgB,OAAO,EAAE,MAAM,CAAA;IAC/B,SAAgB,SAAS,EAAE,MAAM,CAAA;IACjC,SAAgB,SAAS,EAAE,UAAU,CAAA;IACrC,SAAgB,OAAO,EAAE,UAAU,CAAA;IACnC,SAAgB,KAAK,EAAE,UAAU,CAAA;IACjC,SAAgB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtC,SAAgB,eAAe,CAAC,EAAE,UAAU,CAAA;IAC5C,SAAgB,WAAW,CAAC,EAAE,MAAM,CAAA;IACpC,SAAgB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtC,SAAgB,qBAAqB,CAAC,EAAE,UAAU,CAAA;IAClD,SAAgB,YAAY,CAAC,EAAE,UAAU,CAAA;IAEzC,SAAgB,MAAM,EAAE,MAAM,CAAA;IAE9B,SAAS,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,UAAU,KAAK,UAAU,CAAA;IAEzD,SAAS,CAAC,KAAK,EAAE,WAAW,CAE3B;IAED;;OAEG;IACH,IAAI,UAAU,eAQb;IAED;;;;;OAKG;WACW,cAAc,CAAC,UAAU,GAAE,UAAe,EAAE,IAAI,GAAE,YAAiB;IAIjF;;;;;OAKG;WACW,uBAAuB,CAAC,oBAAoB,EAAE,UAAU,EAAE,IAAI,GAAE,YAAiB;IAQ/F;;;;;OAKG;WACW,eAAe,CAAC,MAAM,EAAE,gBAAgB,EAAE,IAAI,GAAE,YAAiB;IAoC/E;;;;;;OAMG;gBACS,UAAU,EAAE,UAAU,EAAE,IAAI,GAAE,YAAiB;IAiL3D;;OAEG;IACH,SAAS,CAAC,wBAAwB;IAiGlC;;;OAGG;IACH,SAAS,CAAC,0BAA0B;IAmFpC;;;;;OAKG;IACH,gBAAgB,CAAC,iBAAiB,EAAE,WAAW;IA4C/C;;OAEG;IACI,eAAe,IAAI,MAAM;IAwChC;;;OAGG;IACH,eAAe,IAAI,MAAM;IAOzB;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAQxB;;;;;OAKG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM;IAQrC;;OAEG;IACI,qBAAqB,IAAI,MAAM;IAYtC;;;OAGG;IACI,oBAAoB,IAAI,MAAM;IAIrC;;OAEG;IACH,GAAG,IAAI,gBAAgB;IAgDvB;;OAEG;IACH,IAAI,IAAI,UAAU;IAUlB;;OAEG;IACH,SAAS,IAAI,OAAO;IAIpB,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM;IASrC;;;;OAIG;IACH,yBAAyB,CAAC,iBAAiB,EAAE,WAAW,GAAG,MAAM;IAoEjE;;OAEG;IACH,aAAa;IAOb;;;OAGG;IACH,uBAAuB,IAAI,OAAO;IAQlC;;;OAGG;IACH,iBAAiB,IAAI,UAAU;IAK/B;;;OAGG;IACH,eAAe,IAAI,UAAU;IAK7B;;;;OAIG;IACH,OAAO,CAAC,eAAe;IAevB;;;;;;;OAOG;IACH,4BAA4B,IAAI,OAAO,EAAE;IAmBzC;;;;;OAKG;IACH,qBAAqB,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,OAAO;IASrD;;OAEG;IACH,YAAY,IAAI,OAAO;IAcvB;;OAEG;IACH,SAAS,IAAI,UAAU;IAIvB;;OAEG;IACH,MAAM,IAAI,UAAU;IAsCpB;;;OAGG;IACH,SAAS,CAAC,qBAAqB;IAqB/B;;OAEG;IACI,QAAQ;IAkBf;;;;;OAKG;IACH,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM;CAGhC"}