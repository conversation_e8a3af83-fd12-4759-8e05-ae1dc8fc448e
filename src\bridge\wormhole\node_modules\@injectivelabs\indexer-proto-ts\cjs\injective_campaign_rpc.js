"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectiveCampaignRPCGetGuildMemberDesc = exports.InjectiveCampaignRPCListGuildMembersDesc = exports.InjectiveCampaignRPCListGuildsDesc = exports.InjectiveCampaignRPCCampaignsV2Desc = exports.InjectiveCampaignRPCCampaignsDesc = exports.InjectiveCampaignRPCRankingDesc = exports.InjectiveCampaignRPCDesc = exports.InjectiveCampaignRPCClientImpl = exports.GetGuildMemberResponse = exports.GetGuildMemberRequest = exports.GuildMember = exports.ListGuildMembersResponse = exports.ListGuildMembersRequest = exports.CampaignSummary = exports.Guild = exports.ListGuildsResponse = exports.ListGuildsRequest = exports.CampaignV2 = exports.CampaignsV2Response = exports.CampaignsV2Request = exports.CampaignsResponse = exports.CampaignsRequest = exports.Paging = exports.CampaignUser = exports.Coin = exports.Campaign = exports.RankingResponse = exports.RankingRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const long_1 = __importDefault(require("long"));
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective_campaign_rpc";
function createBaseRankingRequest() {
    return { campaignId: "", marketId: "", accountAddress: "", limit: 0, skip: "0", contractAddress: "" };
}
exports.RankingRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignId !== "") {
            writer.uint32(10).string(message.campaignId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.limit !== 0) {
            writer.uint32(32).sint32(message.limit);
        }
        if (message.skip !== "0") {
            writer.uint32(40).uint64(message.skip);
        }
        if (message.contractAddress !== "") {
            writer.uint32(50).string(message.contractAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRankingRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.limit = reader.sint32();
                    break;
                case 5:
                    message.skip = longToString(reader.uint64());
                    break;
                case 6:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignId: isSet(object.campaignId) ? String(object.campaignId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            skip: isSet(object.skip) ? String(object.skip) : "0",
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignId !== undefined && (obj.campaignId = message.campaignId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.skip !== undefined && (obj.skip = message.skip);
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create(base) {
        return exports.RankingRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseRankingRequest();
        message.campaignId = (_a = object.campaignId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.limit = (_d = object.limit) !== null && _d !== void 0 ? _d : 0;
        message.skip = (_e = object.skip) !== null && _e !== void 0 ? _e : "0";
        message.contractAddress = (_f = object.contractAddress) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseRankingResponse() {
    return { campaign: undefined, users: [], paging: undefined };
}
exports.RankingResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaign !== undefined) {
            exports.Campaign.encode(message.campaign, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.users) {
            exports.CampaignUser.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.paging !== undefined) {
            exports.Paging.encode(message.paging, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRankingResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaign = exports.Campaign.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.users.push(exports.CampaignUser.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.paging = exports.Paging.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaign: isSet(object.campaign) ? exports.Campaign.fromJSON(object.campaign) : undefined,
            users: Array.isArray(object === null || object === void 0 ? void 0 : object.users) ? object.users.map((e) => exports.CampaignUser.fromJSON(e)) : [],
            paging: isSet(object.paging) ? exports.Paging.fromJSON(object.paging) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaign !== undefined && (obj.campaign = message.campaign ? exports.Campaign.toJSON(message.campaign) : undefined);
        if (message.users) {
            obj.users = message.users.map((e) => e ? exports.CampaignUser.toJSON(e) : undefined);
        }
        else {
            obj.users = [];
        }
        message.paging !== undefined && (obj.paging = message.paging ? exports.Paging.toJSON(message.paging) : undefined);
        return obj;
    },
    create(base) {
        return exports.RankingResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseRankingResponse();
        message.campaign = (object.campaign !== undefined && object.campaign !== null)
            ? exports.Campaign.fromPartial(object.campaign)
            : undefined;
        message.users = ((_a = object.users) === null || _a === void 0 ? void 0 : _a.map((e) => exports.CampaignUser.fromPartial(e))) || [];
        message.paging = (object.paging !== undefined && object.paging !== null)
            ? exports.Paging.fromPartial(object.paging)
            : undefined;
        return message;
    },
};
function createBaseCampaign() {
    return {
        campaignId: "",
        marketId: "",
        totalScore: "",
        lastUpdated: "0",
        startDate: "0",
        endDate: "0",
        isClaimable: false,
        roundId: 0,
        managerContract: "",
        rewards: [],
        userScore: "",
        userClaimed: false,
        subaccountIdSuffix: "",
        rewardContract: "",
        version: "",
        type: "",
    };
}
exports.Campaign = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignId !== "") {
            writer.uint32(10).string(message.campaignId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.totalScore !== "") {
            writer.uint32(34).string(message.totalScore);
        }
        if (message.lastUpdated !== "0") {
            writer.uint32(40).sint64(message.lastUpdated);
        }
        if (message.startDate !== "0") {
            writer.uint32(48).sint64(message.startDate);
        }
        if (message.endDate !== "0") {
            writer.uint32(56).sint64(message.endDate);
        }
        if (message.isClaimable === true) {
            writer.uint32(64).bool(message.isClaimable);
        }
        if (message.roundId !== 0) {
            writer.uint32(72).sint32(message.roundId);
        }
        if (message.managerContract !== "") {
            writer.uint32(82).string(message.managerContract);
        }
        for (const v of message.rewards) {
            exports.Coin.encode(v, writer.uint32(90).fork()).ldelim();
        }
        if (message.userScore !== "") {
            writer.uint32(98).string(message.userScore);
        }
        if (message.userClaimed === true) {
            writer.uint32(104).bool(message.userClaimed);
        }
        if (message.subaccountIdSuffix !== "") {
            writer.uint32(114).string(message.subaccountIdSuffix);
        }
        if (message.rewardContract !== "") {
            writer.uint32(122).string(message.rewardContract);
        }
        if (message.version !== "") {
            writer.uint32(130).string(message.version);
        }
        if (message.type !== "") {
            writer.uint32(138).string(message.type);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaign();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.totalScore = reader.string();
                    break;
                case 5:
                    message.lastUpdated = longToString(reader.sint64());
                    break;
                case 6:
                    message.startDate = longToString(reader.sint64());
                    break;
                case 7:
                    message.endDate = longToString(reader.sint64());
                    break;
                case 8:
                    message.isClaimable = reader.bool();
                    break;
                case 9:
                    message.roundId = reader.sint32();
                    break;
                case 10:
                    message.managerContract = reader.string();
                    break;
                case 11:
                    message.rewards.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.userScore = reader.string();
                    break;
                case 13:
                    message.userClaimed = reader.bool();
                    break;
                case 14:
                    message.subaccountIdSuffix = reader.string();
                    break;
                case 15:
                    message.rewardContract = reader.string();
                    break;
                case 16:
                    message.version = reader.string();
                    break;
                case 17:
                    message.type = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignId: isSet(object.campaignId) ? String(object.campaignId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            totalScore: isSet(object.totalScore) ? String(object.totalScore) : "",
            lastUpdated: isSet(object.lastUpdated) ? String(object.lastUpdated) : "0",
            startDate: isSet(object.startDate) ? String(object.startDate) : "0",
            endDate: isSet(object.endDate) ? String(object.endDate) : "0",
            isClaimable: isSet(object.isClaimable) ? Boolean(object.isClaimable) : false,
            roundId: isSet(object.roundId) ? Number(object.roundId) : 0,
            managerContract: isSet(object.managerContract) ? String(object.managerContract) : "",
            rewards: Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map((e) => exports.Coin.fromJSON(e)) : [],
            userScore: isSet(object.userScore) ? String(object.userScore) : "",
            userClaimed: isSet(object.userClaimed) ? Boolean(object.userClaimed) : false,
            subaccountIdSuffix: isSet(object.subaccountIdSuffix) ? String(object.subaccountIdSuffix) : "",
            rewardContract: isSet(object.rewardContract) ? String(object.rewardContract) : "",
            version: isSet(object.version) ? String(object.version) : "",
            type: isSet(object.type) ? String(object.type) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignId !== undefined && (obj.campaignId = message.campaignId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.totalScore !== undefined && (obj.totalScore = message.totalScore);
        message.lastUpdated !== undefined && (obj.lastUpdated = message.lastUpdated);
        message.startDate !== undefined && (obj.startDate = message.startDate);
        message.endDate !== undefined && (obj.endDate = message.endDate);
        message.isClaimable !== undefined && (obj.isClaimable = message.isClaimable);
        message.roundId !== undefined && (obj.roundId = Math.round(message.roundId));
        message.managerContract !== undefined && (obj.managerContract = message.managerContract);
        if (message.rewards) {
            obj.rewards = message.rewards.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.rewards = [];
        }
        message.userScore !== undefined && (obj.userScore = message.userScore);
        message.userClaimed !== undefined && (obj.userClaimed = message.userClaimed);
        message.subaccountIdSuffix !== undefined && (obj.subaccountIdSuffix = message.subaccountIdSuffix);
        message.rewardContract !== undefined && (obj.rewardContract = message.rewardContract);
        message.version !== undefined && (obj.version = message.version);
        message.type !== undefined && (obj.type = message.type);
        return obj;
    },
    create(base) {
        return exports.Campaign.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;
        const message = createBaseCampaign();
        message.campaignId = (_a = object.campaignId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.totalScore = (_c = object.totalScore) !== null && _c !== void 0 ? _c : "";
        message.lastUpdated = (_d = object.lastUpdated) !== null && _d !== void 0 ? _d : "0";
        message.startDate = (_e = object.startDate) !== null && _e !== void 0 ? _e : "0";
        message.endDate = (_f = object.endDate) !== null && _f !== void 0 ? _f : "0";
        message.isClaimable = (_g = object.isClaimable) !== null && _g !== void 0 ? _g : false;
        message.roundId = (_h = object.roundId) !== null && _h !== void 0 ? _h : 0;
        message.managerContract = (_j = object.managerContract) !== null && _j !== void 0 ? _j : "";
        message.rewards = ((_k = object.rewards) === null || _k === void 0 ? void 0 : _k.map((e) => exports.Coin.fromPartial(e))) || [];
        message.userScore = (_l = object.userScore) !== null && _l !== void 0 ? _l : "";
        message.userClaimed = (_m = object.userClaimed) !== null && _m !== void 0 ? _m : false;
        message.subaccountIdSuffix = (_o = object.subaccountIdSuffix) !== null && _o !== void 0 ? _o : "";
        message.rewardContract = (_p = object.rewardContract) !== null && _p !== void 0 ? _p : "";
        message.version = (_q = object.version) !== null && _q !== void 0 ? _q : "";
        message.type = (_r = object.type) !== null && _r !== void 0 ? _r : "";
        return message;
    },
};
function createBaseCoin() {
    return { denom: "", amount: "", usdValue: "" };
}
exports.Coin = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        if (message.usdValue !== "") {
            writer.uint32(26).string(message.usdValue);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCoin();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                case 3:
                    message.usdValue = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            usdValue: isSet(object.usdValue) ? String(object.usdValue) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.amount !== undefined && (obj.amount = message.amount);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        return obj;
    },
    create(base) {
        return exports.Coin.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseCoin();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        message.usdValue = (_c = object.usdValue) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseCampaignUser() {
    return {
        campaignId: "",
        marketId: "",
        accountAddress: "",
        score: "",
        contractUpdated: false,
        blockHeight: "0",
        blockTime: "0",
        purchasedAmount: "",
        galxeUpdated: false,
        rewardClaimed: false,
    };
}
exports.CampaignUser = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignId !== "") {
            writer.uint32(10).string(message.campaignId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.score !== "") {
            writer.uint32(34).string(message.score);
        }
        if (message.contractUpdated === true) {
            writer.uint32(40).bool(message.contractUpdated);
        }
        if (message.blockHeight !== "0") {
            writer.uint32(48).sint64(message.blockHeight);
        }
        if (message.blockTime !== "0") {
            writer.uint32(56).sint64(message.blockTime);
        }
        if (message.purchasedAmount !== "") {
            writer.uint32(66).string(message.purchasedAmount);
        }
        if (message.galxeUpdated === true) {
            writer.uint32(72).bool(message.galxeUpdated);
        }
        if (message.rewardClaimed === true) {
            writer.uint32(80).bool(message.rewardClaimed);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignUser();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.score = reader.string();
                    break;
                case 5:
                    message.contractUpdated = reader.bool();
                    break;
                case 6:
                    message.blockHeight = longToString(reader.sint64());
                    break;
                case 7:
                    message.blockTime = longToString(reader.sint64());
                    break;
                case 8:
                    message.purchasedAmount = reader.string();
                    break;
                case 9:
                    message.galxeUpdated = reader.bool();
                    break;
                case 10:
                    message.rewardClaimed = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignId: isSet(object.campaignId) ? String(object.campaignId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            score: isSet(object.score) ? String(object.score) : "",
            contractUpdated: isSet(object.contractUpdated) ? Boolean(object.contractUpdated) : false,
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            blockTime: isSet(object.blockTime) ? String(object.blockTime) : "0",
            purchasedAmount: isSet(object.purchasedAmount) ? String(object.purchasedAmount) : "",
            galxeUpdated: isSet(object.galxeUpdated) ? Boolean(object.galxeUpdated) : false,
            rewardClaimed: isSet(object.rewardClaimed) ? Boolean(object.rewardClaimed) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignId !== undefined && (obj.campaignId = message.campaignId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.score !== undefined && (obj.score = message.score);
        message.contractUpdated !== undefined && (obj.contractUpdated = message.contractUpdated);
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.blockTime !== undefined && (obj.blockTime = message.blockTime);
        message.purchasedAmount !== undefined && (obj.purchasedAmount = message.purchasedAmount);
        message.galxeUpdated !== undefined && (obj.galxeUpdated = message.galxeUpdated);
        message.rewardClaimed !== undefined && (obj.rewardClaimed = message.rewardClaimed);
        return obj;
    },
    create(base) {
        return exports.CampaignUser.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const message = createBaseCampaignUser();
        message.campaignId = (_a = object.campaignId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.score = (_d = object.score) !== null && _d !== void 0 ? _d : "";
        message.contractUpdated = (_e = object.contractUpdated) !== null && _e !== void 0 ? _e : false;
        message.blockHeight = (_f = object.blockHeight) !== null && _f !== void 0 ? _f : "0";
        message.blockTime = (_g = object.blockTime) !== null && _g !== void 0 ? _g : "0";
        message.purchasedAmount = (_h = object.purchasedAmount) !== null && _h !== void 0 ? _h : "";
        message.galxeUpdated = (_j = object.galxeUpdated) !== null && _j !== void 0 ? _j : false;
        message.rewardClaimed = (_k = object.rewardClaimed) !== null && _k !== void 0 ? _k : false;
        return message;
    },
};
function createBasePaging() {
    return { total: "0", from: 0, to: 0, countBySubaccount: "0", next: [] };
}
exports.Paging = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.total !== "0") {
            writer.uint32(8).sint64(message.total);
        }
        if (message.from !== 0) {
            writer.uint32(16).sint32(message.from);
        }
        if (message.to !== 0) {
            writer.uint32(24).sint32(message.to);
        }
        if (message.countBySubaccount !== "0") {
            writer.uint32(32).sint64(message.countBySubaccount);
        }
        for (const v of message.next) {
            writer.uint32(42).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePaging();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.total = longToString(reader.sint64());
                    break;
                case 2:
                    message.from = reader.sint32();
                    break;
                case 3:
                    message.to = reader.sint32();
                    break;
                case 4:
                    message.countBySubaccount = longToString(reader.sint64());
                    break;
                case 5:
                    message.next.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            total: isSet(object.total) ? String(object.total) : "0",
            from: isSet(object.from) ? Number(object.from) : 0,
            to: isSet(object.to) ? Number(object.to) : 0,
            countBySubaccount: isSet(object.countBySubaccount) ? String(object.countBySubaccount) : "0",
            next: Array.isArray(object === null || object === void 0 ? void 0 : object.next) ? object.next.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.total !== undefined && (obj.total = message.total);
        message.from !== undefined && (obj.from = Math.round(message.from));
        message.to !== undefined && (obj.to = Math.round(message.to));
        message.countBySubaccount !== undefined && (obj.countBySubaccount = message.countBySubaccount);
        if (message.next) {
            obj.next = message.next.map((e) => e);
        }
        else {
            obj.next = [];
        }
        return obj;
    },
    create(base) {
        return exports.Paging.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBasePaging();
        message.total = (_a = object.total) !== null && _a !== void 0 ? _a : "0";
        message.from = (_b = object.from) !== null && _b !== void 0 ? _b : 0;
        message.to = (_c = object.to) !== null && _c !== void 0 ? _c : 0;
        message.countBySubaccount = (_d = object.countBySubaccount) !== null && _d !== void 0 ? _d : "0";
        message.next = ((_e = object.next) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        return message;
    },
};
function createBaseCampaignsRequest() {
    return { roundId: "0", accountAddress: "", toRoundId: 0, contractAddress: "", type: "" };
}
exports.CampaignsRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.roundId !== "0") {
            writer.uint32(8).sint64(message.roundId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(18).string(message.accountAddress);
        }
        if (message.toRoundId !== 0) {
            writer.uint32(24).sint32(message.toRoundId);
        }
        if (message.contractAddress !== "") {
            writer.uint32(34).string(message.contractAddress);
        }
        if (message.type !== "") {
            writer.uint32(42).string(message.type);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.roundId = longToString(reader.sint64());
                    break;
                case 2:
                    message.accountAddress = reader.string();
                    break;
                case 3:
                    message.toRoundId = reader.sint32();
                    break;
                case 4:
                    message.contractAddress = reader.string();
                    break;
                case 5:
                    message.type = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            roundId: isSet(object.roundId) ? String(object.roundId) : "0",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            toRoundId: isSet(object.toRoundId) ? Number(object.toRoundId) : 0,
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            type: isSet(object.type) ? String(object.type) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.roundId !== undefined && (obj.roundId = message.roundId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.toRoundId !== undefined && (obj.toRoundId = Math.round(message.toRoundId));
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.type !== undefined && (obj.type = message.type);
        return obj;
    },
    create(base) {
        return exports.CampaignsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseCampaignsRequest();
        message.roundId = (_a = object.roundId) !== null && _a !== void 0 ? _a : "0";
        message.accountAddress = (_b = object.accountAddress) !== null && _b !== void 0 ? _b : "";
        message.toRoundId = (_c = object.toRoundId) !== null && _c !== void 0 ? _c : 0;
        message.contractAddress = (_d = object.contractAddress) !== null && _d !== void 0 ? _d : "";
        message.type = (_e = object.type) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseCampaignsResponse() {
    return { campaigns: [], accumulatedRewards: [], rewardCount: 0 };
}
exports.CampaignsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.campaigns) {
            exports.Campaign.encode(v, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.accumulatedRewards) {
            exports.Coin.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.rewardCount !== 0) {
            writer.uint32(24).sint32(message.rewardCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaigns.push(exports.Campaign.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.accumulatedRewards.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.rewardCount = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaigns: Array.isArray(object === null || object === void 0 ? void 0 : object.campaigns) ? object.campaigns.map((e) => exports.Campaign.fromJSON(e)) : [],
            accumulatedRewards: Array.isArray(object === null || object === void 0 ? void 0 : object.accumulatedRewards)
                ? object.accumulatedRewards.map((e) => exports.Coin.fromJSON(e))
                : [],
            rewardCount: isSet(object.rewardCount) ? Number(object.rewardCount) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.campaigns) {
            obj.campaigns = message.campaigns.map((e) => e ? exports.Campaign.toJSON(e) : undefined);
        }
        else {
            obj.campaigns = [];
        }
        if (message.accumulatedRewards) {
            obj.accumulatedRewards = message.accumulatedRewards.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.accumulatedRewards = [];
        }
        message.rewardCount !== undefined && (obj.rewardCount = Math.round(message.rewardCount));
        return obj;
    },
    create(base) {
        return exports.CampaignsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseCampaignsResponse();
        message.campaigns = ((_a = object.campaigns) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Campaign.fromPartial(e))) || [];
        message.accumulatedRewards = ((_b = object.accumulatedRewards) === null || _b === void 0 ? void 0 : _b.map((e) => exports.Coin.fromPartial(e))) || [];
        message.rewardCount = (_c = object.rewardCount) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseCampaignsV2Request() {
    return {
        type: "",
        active: false,
        limit: 0,
        cursor: "",
        fromStartDate: "0",
        toStartDate: "0",
        fromEndDate: "0",
        toEndDate: "0",
        status: "",
    };
}
exports.CampaignsV2Request = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        if (message.active === true) {
            writer.uint32(16).bool(message.active);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        if (message.cursor !== "") {
            writer.uint32(34).string(message.cursor);
        }
        if (message.fromStartDate !== "0") {
            writer.uint32(40).sint64(message.fromStartDate);
        }
        if (message.toStartDate !== "0") {
            writer.uint32(48).sint64(message.toStartDate);
        }
        if (message.fromEndDate !== "0") {
            writer.uint32(56).sint64(message.fromEndDate);
        }
        if (message.toEndDate !== "0") {
            writer.uint32(64).sint64(message.toEndDate);
        }
        if (message.status !== "") {
            writer.uint32(74).string(message.status);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignsV2Request();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.string();
                    break;
                case 2:
                    message.active = reader.bool();
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                case 4:
                    message.cursor = reader.string();
                    break;
                case 5:
                    message.fromStartDate = longToString(reader.sint64());
                    break;
                case 6:
                    message.toStartDate = longToString(reader.sint64());
                    break;
                case 7:
                    message.fromEndDate = longToString(reader.sint64());
                    break;
                case 8:
                    message.toEndDate = longToString(reader.sint64());
                    break;
                case 9:
                    message.status = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? String(object.type) : "",
            active: isSet(object.active) ? Boolean(object.active) : false,
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            cursor: isSet(object.cursor) ? String(object.cursor) : "",
            fromStartDate: isSet(object.fromStartDate) ? String(object.fromStartDate) : "0",
            toStartDate: isSet(object.toStartDate) ? String(object.toStartDate) : "0",
            fromEndDate: isSet(object.fromEndDate) ? String(object.fromEndDate) : "0",
            toEndDate: isSet(object.toEndDate) ? String(object.toEndDate) : "0",
            status: isSet(object.status) ? String(object.status) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = message.type);
        message.active !== undefined && (obj.active = message.active);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.cursor !== undefined && (obj.cursor = message.cursor);
        message.fromStartDate !== undefined && (obj.fromStartDate = message.fromStartDate);
        message.toStartDate !== undefined && (obj.toStartDate = message.toStartDate);
        message.fromEndDate !== undefined && (obj.fromEndDate = message.fromEndDate);
        message.toEndDate !== undefined && (obj.toEndDate = message.toEndDate);
        message.status !== undefined && (obj.status = message.status);
        return obj;
    },
    create(base) {
        return exports.CampaignsV2Request.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBaseCampaignsV2Request();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : "";
        message.active = (_b = object.active) !== null && _b !== void 0 ? _b : false;
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        message.cursor = (_d = object.cursor) !== null && _d !== void 0 ? _d : "";
        message.fromStartDate = (_e = object.fromStartDate) !== null && _e !== void 0 ? _e : "0";
        message.toStartDate = (_f = object.toStartDate) !== null && _f !== void 0 ? _f : "0";
        message.fromEndDate = (_g = object.fromEndDate) !== null && _g !== void 0 ? _g : "0";
        message.toEndDate = (_h = object.toEndDate) !== null && _h !== void 0 ? _h : "0";
        message.status = (_j = object.status) !== null && _j !== void 0 ? _j : "";
        return message;
    },
};
function createBaseCampaignsV2Response() {
    return { campaigns: [], cursor: "" };
}
exports.CampaignsV2Response = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.campaigns) {
            exports.CampaignV2.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.cursor !== "") {
            writer.uint32(18).string(message.cursor);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignsV2Response();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaigns.push(exports.CampaignV2.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.cursor = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaigns: Array.isArray(object === null || object === void 0 ? void 0 : object.campaigns) ? object.campaigns.map((e) => exports.CampaignV2.fromJSON(e)) : [],
            cursor: isSet(object.cursor) ? String(object.cursor) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.campaigns) {
            obj.campaigns = message.campaigns.map((e) => e ? exports.CampaignV2.toJSON(e) : undefined);
        }
        else {
            obj.campaigns = [];
        }
        message.cursor !== undefined && (obj.cursor = message.cursor);
        return obj;
    },
    create(base) {
        return exports.CampaignsV2Response.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseCampaignsV2Response();
        message.campaigns = ((_a = object.campaigns) === null || _a === void 0 ? void 0 : _a.map((e) => exports.CampaignV2.fromPartial(e))) || [];
        message.cursor = (_b = object.cursor) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseCampaignV2() {
    return {
        campaignId: "",
        marketId: "",
        totalScore: "",
        createdAt: "0",
        updatedAt: "0",
        startDate: "0",
        endDate: "0",
        isClaimable: false,
        roundId: 0,
        managerContract: "",
        rewards: [],
        subaccountIdSuffix: "",
        rewardContract: "",
        type: "",
        version: "",
        name: "",
        description: "",
    };
}
exports.CampaignV2 = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignId !== "") {
            writer.uint32(10).string(message.campaignId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.totalScore !== "") {
            writer.uint32(34).string(message.totalScore);
        }
        if (message.createdAt !== "0") {
            writer.uint32(40).sint64(message.createdAt);
        }
        if (message.updatedAt !== "0") {
            writer.uint32(48).sint64(message.updatedAt);
        }
        if (message.startDate !== "0") {
            writer.uint32(56).sint64(message.startDate);
        }
        if (message.endDate !== "0") {
            writer.uint32(64).sint64(message.endDate);
        }
        if (message.isClaimable === true) {
            writer.uint32(72).bool(message.isClaimable);
        }
        if (message.roundId !== 0) {
            writer.uint32(80).sint32(message.roundId);
        }
        if (message.managerContract !== "") {
            writer.uint32(90).string(message.managerContract);
        }
        for (const v of message.rewards) {
            exports.Coin.encode(v, writer.uint32(98).fork()).ldelim();
        }
        if (message.subaccountIdSuffix !== "") {
            writer.uint32(106).string(message.subaccountIdSuffix);
        }
        if (message.rewardContract !== "") {
            writer.uint32(114).string(message.rewardContract);
        }
        if (message.type !== "") {
            writer.uint32(122).string(message.type);
        }
        if (message.version !== "") {
            writer.uint32(130).string(message.version);
        }
        if (message.name !== "") {
            writer.uint32(138).string(message.name);
        }
        if (message.description !== "") {
            writer.uint32(146).string(message.description);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignV2();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.totalScore = reader.string();
                    break;
                case 5:
                    message.createdAt = longToString(reader.sint64());
                    break;
                case 6:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                case 7:
                    message.startDate = longToString(reader.sint64());
                    break;
                case 8:
                    message.endDate = longToString(reader.sint64());
                    break;
                case 9:
                    message.isClaimable = reader.bool();
                    break;
                case 10:
                    message.roundId = reader.sint32();
                    break;
                case 11:
                    message.managerContract = reader.string();
                    break;
                case 12:
                    message.rewards.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 13:
                    message.subaccountIdSuffix = reader.string();
                    break;
                case 14:
                    message.rewardContract = reader.string();
                    break;
                case 15:
                    message.type = reader.string();
                    break;
                case 16:
                    message.version = reader.string();
                    break;
                case 17:
                    message.name = reader.string();
                    break;
                case 18:
                    message.description = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignId: isSet(object.campaignId) ? String(object.campaignId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            totalScore: isSet(object.totalScore) ? String(object.totalScore) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "0",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
            startDate: isSet(object.startDate) ? String(object.startDate) : "0",
            endDate: isSet(object.endDate) ? String(object.endDate) : "0",
            isClaimable: isSet(object.isClaimable) ? Boolean(object.isClaimable) : false,
            roundId: isSet(object.roundId) ? Number(object.roundId) : 0,
            managerContract: isSet(object.managerContract) ? String(object.managerContract) : "",
            rewards: Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map((e) => exports.Coin.fromJSON(e)) : [],
            subaccountIdSuffix: isSet(object.subaccountIdSuffix) ? String(object.subaccountIdSuffix) : "",
            rewardContract: isSet(object.rewardContract) ? String(object.rewardContract) : "",
            type: isSet(object.type) ? String(object.type) : "",
            version: isSet(object.version) ? String(object.version) : "",
            name: isSet(object.name) ? String(object.name) : "",
            description: isSet(object.description) ? String(object.description) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignId !== undefined && (obj.campaignId = message.campaignId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.totalScore !== undefined && (obj.totalScore = message.totalScore);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        message.startDate !== undefined && (obj.startDate = message.startDate);
        message.endDate !== undefined && (obj.endDate = message.endDate);
        message.isClaimable !== undefined && (obj.isClaimable = message.isClaimable);
        message.roundId !== undefined && (obj.roundId = Math.round(message.roundId));
        message.managerContract !== undefined && (obj.managerContract = message.managerContract);
        if (message.rewards) {
            obj.rewards = message.rewards.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.rewards = [];
        }
        message.subaccountIdSuffix !== undefined && (obj.subaccountIdSuffix = message.subaccountIdSuffix);
        message.rewardContract !== undefined && (obj.rewardContract = message.rewardContract);
        message.type !== undefined && (obj.type = message.type);
        message.version !== undefined && (obj.version = message.version);
        message.name !== undefined && (obj.name = message.name);
        message.description !== undefined && (obj.description = message.description);
        return obj;
    },
    create(base) {
        return exports.CampaignV2.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
        const message = createBaseCampaignV2();
        message.campaignId = (_a = object.campaignId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.totalScore = (_c = object.totalScore) !== null && _c !== void 0 ? _c : "";
        message.createdAt = (_d = object.createdAt) !== null && _d !== void 0 ? _d : "0";
        message.updatedAt = (_e = object.updatedAt) !== null && _e !== void 0 ? _e : "0";
        message.startDate = (_f = object.startDate) !== null && _f !== void 0 ? _f : "0";
        message.endDate = (_g = object.endDate) !== null && _g !== void 0 ? _g : "0";
        message.isClaimable = (_h = object.isClaimable) !== null && _h !== void 0 ? _h : false;
        message.roundId = (_j = object.roundId) !== null && _j !== void 0 ? _j : 0;
        message.managerContract = (_k = object.managerContract) !== null && _k !== void 0 ? _k : "";
        message.rewards = ((_l = object.rewards) === null || _l === void 0 ? void 0 : _l.map((e) => exports.Coin.fromPartial(e))) || [];
        message.subaccountIdSuffix = (_m = object.subaccountIdSuffix) !== null && _m !== void 0 ? _m : "";
        message.rewardContract = (_o = object.rewardContract) !== null && _o !== void 0 ? _o : "";
        message.type = (_p = object.type) !== null && _p !== void 0 ? _p : "";
        message.version = (_q = object.version) !== null && _q !== void 0 ? _q : "";
        message.name = (_r = object.name) !== null && _r !== void 0 ? _r : "";
        message.description = (_s = object.description) !== null && _s !== void 0 ? _s : "";
        return message;
    },
};
function createBaseListGuildsRequest() {
    return { campaignContract: "", limit: 0, skip: 0, sortBy: "" };
}
exports.ListGuildsRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignContract !== "") {
            writer.uint32(10).string(message.campaignContract);
        }
        if (message.limit !== 0) {
            writer.uint32(16).sint32(message.limit);
        }
        if (message.skip !== 0) {
            writer.uint32(24).sint32(message.skip);
        }
        if (message.sortBy !== "") {
            writer.uint32(34).string(message.sortBy);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListGuildsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignContract = reader.string();
                    break;
                case 2:
                    message.limit = reader.sint32();
                    break;
                case 3:
                    message.skip = reader.sint32();
                    break;
                case 4:
                    message.sortBy = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            skip: isSet(object.skip) ? Number(object.skip) : 0,
            sortBy: isSet(object.sortBy) ? String(object.sortBy) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.skip !== undefined && (obj.skip = Math.round(message.skip));
        message.sortBy !== undefined && (obj.sortBy = message.sortBy);
        return obj;
    },
    create(base) {
        return exports.ListGuildsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseListGuildsRequest();
        message.campaignContract = (_a = object.campaignContract) !== null && _a !== void 0 ? _a : "";
        message.limit = (_b = object.limit) !== null && _b !== void 0 ? _b : 0;
        message.skip = (_c = object.skip) !== null && _c !== void 0 ? _c : 0;
        message.sortBy = (_d = object.sortBy) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseListGuildsResponse() {
    return { guilds: [], paging: undefined, updatedAt: "0", campaignSummary: undefined };
}
exports.ListGuildsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.guilds) {
            exports.Guild.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.paging !== undefined) {
            exports.Paging.encode(message.paging, writer.uint32(18).fork()).ldelim();
        }
        if (message.updatedAt !== "0") {
            writer.uint32(24).sint64(message.updatedAt);
        }
        if (message.campaignSummary !== undefined) {
            exports.CampaignSummary.encode(message.campaignSummary, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListGuildsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.guilds.push(exports.Guild.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.paging = exports.Paging.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                case 4:
                    message.campaignSummary = exports.CampaignSummary.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            guilds: Array.isArray(object === null || object === void 0 ? void 0 : object.guilds) ? object.guilds.map((e) => exports.Guild.fromJSON(e)) : [],
            paging: isSet(object.paging) ? exports.Paging.fromJSON(object.paging) : undefined,
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
            campaignSummary: isSet(object.campaignSummary) ? exports.CampaignSummary.fromJSON(object.campaignSummary) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.guilds) {
            obj.guilds = message.guilds.map((e) => e ? exports.Guild.toJSON(e) : undefined);
        }
        else {
            obj.guilds = [];
        }
        message.paging !== undefined && (obj.paging = message.paging ? exports.Paging.toJSON(message.paging) : undefined);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        message.campaignSummary !== undefined &&
            (obj.campaignSummary = message.campaignSummary ? exports.CampaignSummary.toJSON(message.campaignSummary) : undefined);
        return obj;
    },
    create(base) {
        return exports.ListGuildsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseListGuildsResponse();
        message.guilds = ((_a = object.guilds) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Guild.fromPartial(e))) || [];
        message.paging = (object.paging !== undefined && object.paging !== null)
            ? exports.Paging.fromPartial(object.paging)
            : undefined;
        message.updatedAt = (_b = object.updatedAt) !== null && _b !== void 0 ? _b : "0";
        message.campaignSummary = (object.campaignSummary !== undefined && object.campaignSummary !== null)
            ? exports.CampaignSummary.fromPartial(object.campaignSummary)
            : undefined;
        return message;
    },
};
function createBaseGuild() {
    return {
        campaignContract: "",
        guildId: "",
        masterAddress: "",
        createdAt: "0",
        tvlScore: "",
        volumeScore: "",
        rankByVolume: 0,
        rankByTvl: 0,
        logo: "",
        totalTvl: "",
        updatedAt: "0",
        name: "",
        isActive: false,
        masterBalance: "",
        description: "",
    };
}
exports.Guild = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignContract !== "") {
            writer.uint32(10).string(message.campaignContract);
        }
        if (message.guildId !== "") {
            writer.uint32(18).string(message.guildId);
        }
        if (message.masterAddress !== "") {
            writer.uint32(26).string(message.masterAddress);
        }
        if (message.createdAt !== "0") {
            writer.uint32(32).sint64(message.createdAt);
        }
        if (message.tvlScore !== "") {
            writer.uint32(42).string(message.tvlScore);
        }
        if (message.volumeScore !== "") {
            writer.uint32(50).string(message.volumeScore);
        }
        if (message.rankByVolume !== 0) {
            writer.uint32(56).sint32(message.rankByVolume);
        }
        if (message.rankByTvl !== 0) {
            writer.uint32(64).sint32(message.rankByTvl);
        }
        if (message.logo !== "") {
            writer.uint32(74).string(message.logo);
        }
        if (message.totalTvl !== "") {
            writer.uint32(82).string(message.totalTvl);
        }
        if (message.updatedAt !== "0") {
            writer.uint32(88).sint64(message.updatedAt);
        }
        if (message.name !== "") {
            writer.uint32(114).string(message.name);
        }
        if (message.isActive === true) {
            writer.uint32(104).bool(message.isActive);
        }
        if (message.masterBalance !== "") {
            writer.uint32(122).string(message.masterBalance);
        }
        if (message.description !== "") {
            writer.uint32(130).string(message.description);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGuild();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignContract = reader.string();
                    break;
                case 2:
                    message.guildId = reader.string();
                    break;
                case 3:
                    message.masterAddress = reader.string();
                    break;
                case 4:
                    message.createdAt = longToString(reader.sint64());
                    break;
                case 5:
                    message.tvlScore = reader.string();
                    break;
                case 6:
                    message.volumeScore = reader.string();
                    break;
                case 7:
                    message.rankByVolume = reader.sint32();
                    break;
                case 8:
                    message.rankByTvl = reader.sint32();
                    break;
                case 9:
                    message.logo = reader.string();
                    break;
                case 10:
                    message.totalTvl = reader.string();
                    break;
                case 11:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                case 14:
                    message.name = reader.string();
                    break;
                case 13:
                    message.isActive = reader.bool();
                    break;
                case 15:
                    message.masterBalance = reader.string();
                    break;
                case 16:
                    message.description = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            guildId: isSet(object.guildId) ? String(object.guildId) : "",
            masterAddress: isSet(object.masterAddress) ? String(object.masterAddress) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "0",
            tvlScore: isSet(object.tvlScore) ? String(object.tvlScore) : "",
            volumeScore: isSet(object.volumeScore) ? String(object.volumeScore) : "",
            rankByVolume: isSet(object.rankByVolume) ? Number(object.rankByVolume) : 0,
            rankByTvl: isSet(object.rankByTvl) ? Number(object.rankByTvl) : 0,
            logo: isSet(object.logo) ? String(object.logo) : "",
            totalTvl: isSet(object.totalTvl) ? String(object.totalTvl) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
            name: isSet(object.name) ? String(object.name) : "",
            isActive: isSet(object.isActive) ? Boolean(object.isActive) : false,
            masterBalance: isSet(object.masterBalance) ? String(object.masterBalance) : "",
            description: isSet(object.description) ? String(object.description) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.guildId !== undefined && (obj.guildId = message.guildId);
        message.masterAddress !== undefined && (obj.masterAddress = message.masterAddress);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.tvlScore !== undefined && (obj.tvlScore = message.tvlScore);
        message.volumeScore !== undefined && (obj.volumeScore = message.volumeScore);
        message.rankByVolume !== undefined && (obj.rankByVolume = Math.round(message.rankByVolume));
        message.rankByTvl !== undefined && (obj.rankByTvl = Math.round(message.rankByTvl));
        message.logo !== undefined && (obj.logo = message.logo);
        message.totalTvl !== undefined && (obj.totalTvl = message.totalTvl);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        message.name !== undefined && (obj.name = message.name);
        message.isActive !== undefined && (obj.isActive = message.isActive);
        message.masterBalance !== undefined && (obj.masterBalance = message.masterBalance);
        message.description !== undefined && (obj.description = message.description);
        return obj;
    },
    create(base) {
        return exports.Guild.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        const message = createBaseGuild();
        message.campaignContract = (_a = object.campaignContract) !== null && _a !== void 0 ? _a : "";
        message.guildId = (_b = object.guildId) !== null && _b !== void 0 ? _b : "";
        message.masterAddress = (_c = object.masterAddress) !== null && _c !== void 0 ? _c : "";
        message.createdAt = (_d = object.createdAt) !== null && _d !== void 0 ? _d : "0";
        message.tvlScore = (_e = object.tvlScore) !== null && _e !== void 0 ? _e : "";
        message.volumeScore = (_f = object.volumeScore) !== null && _f !== void 0 ? _f : "";
        message.rankByVolume = (_g = object.rankByVolume) !== null && _g !== void 0 ? _g : 0;
        message.rankByTvl = (_h = object.rankByTvl) !== null && _h !== void 0 ? _h : 0;
        message.logo = (_j = object.logo) !== null && _j !== void 0 ? _j : "";
        message.totalTvl = (_k = object.totalTvl) !== null && _k !== void 0 ? _k : "";
        message.updatedAt = (_l = object.updatedAt) !== null && _l !== void 0 ? _l : "0";
        message.name = (_m = object.name) !== null && _m !== void 0 ? _m : "";
        message.isActive = (_o = object.isActive) !== null && _o !== void 0 ? _o : false;
        message.masterBalance = (_p = object.masterBalance) !== null && _p !== void 0 ? _p : "";
        message.description = (_q = object.description) !== null && _q !== void 0 ? _q : "";
        return message;
    },
};
function createBaseCampaignSummary() {
    return {
        campaignId: "",
        campaignContract: "",
        totalGuildsCount: 0,
        totalTvl: "",
        totalAverageTvl: "",
        totalVolume: "",
        updatedAt: "0",
        totalMembersCount: 0,
        startTime: "0",
        endTime: "0",
    };
}
exports.CampaignSummary = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignId !== "") {
            writer.uint32(10).string(message.campaignId);
        }
        if (message.campaignContract !== "") {
            writer.uint32(18).string(message.campaignContract);
        }
        if (message.totalGuildsCount !== 0) {
            writer.uint32(24).sint32(message.totalGuildsCount);
        }
        if (message.totalTvl !== "") {
            writer.uint32(34).string(message.totalTvl);
        }
        if (message.totalAverageTvl !== "") {
            writer.uint32(42).string(message.totalAverageTvl);
        }
        if (message.totalVolume !== "") {
            writer.uint32(50).string(message.totalVolume);
        }
        if (message.updatedAt !== "0") {
            writer.uint32(56).sint64(message.updatedAt);
        }
        if (message.totalMembersCount !== 0) {
            writer.uint32(64).sint32(message.totalMembersCount);
        }
        if (message.startTime !== "0") {
            writer.uint32(72).sint64(message.startTime);
        }
        if (message.endTime !== "0") {
            writer.uint32(80).sint64(message.endTime);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCampaignSummary();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignId = reader.string();
                    break;
                case 2:
                    message.campaignContract = reader.string();
                    break;
                case 3:
                    message.totalGuildsCount = reader.sint32();
                    break;
                case 4:
                    message.totalTvl = reader.string();
                    break;
                case 5:
                    message.totalAverageTvl = reader.string();
                    break;
                case 6:
                    message.totalVolume = reader.string();
                    break;
                case 7:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                case 8:
                    message.totalMembersCount = reader.sint32();
                    break;
                case 9:
                    message.startTime = longToString(reader.sint64());
                    break;
                case 10:
                    message.endTime = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignId: isSet(object.campaignId) ? String(object.campaignId) : "",
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            totalGuildsCount: isSet(object.totalGuildsCount) ? Number(object.totalGuildsCount) : 0,
            totalTvl: isSet(object.totalTvl) ? String(object.totalTvl) : "",
            totalAverageTvl: isSet(object.totalAverageTvl) ? String(object.totalAverageTvl) : "",
            totalVolume: isSet(object.totalVolume) ? String(object.totalVolume) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
            totalMembersCount: isSet(object.totalMembersCount) ? Number(object.totalMembersCount) : 0,
            startTime: isSet(object.startTime) ? String(object.startTime) : "0",
            endTime: isSet(object.endTime) ? String(object.endTime) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignId !== undefined && (obj.campaignId = message.campaignId);
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.totalGuildsCount !== undefined && (obj.totalGuildsCount = Math.round(message.totalGuildsCount));
        message.totalTvl !== undefined && (obj.totalTvl = message.totalTvl);
        message.totalAverageTvl !== undefined && (obj.totalAverageTvl = message.totalAverageTvl);
        message.totalVolume !== undefined && (obj.totalVolume = message.totalVolume);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        message.totalMembersCount !== undefined && (obj.totalMembersCount = Math.round(message.totalMembersCount));
        message.startTime !== undefined && (obj.startTime = message.startTime);
        message.endTime !== undefined && (obj.endTime = message.endTime);
        return obj;
    },
    create(base) {
        return exports.CampaignSummary.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        const message = createBaseCampaignSummary();
        message.campaignId = (_a = object.campaignId) !== null && _a !== void 0 ? _a : "";
        message.campaignContract = (_b = object.campaignContract) !== null && _b !== void 0 ? _b : "";
        message.totalGuildsCount = (_c = object.totalGuildsCount) !== null && _c !== void 0 ? _c : 0;
        message.totalTvl = (_d = object.totalTvl) !== null && _d !== void 0 ? _d : "";
        message.totalAverageTvl = (_e = object.totalAverageTvl) !== null && _e !== void 0 ? _e : "";
        message.totalVolume = (_f = object.totalVolume) !== null && _f !== void 0 ? _f : "";
        message.updatedAt = (_g = object.updatedAt) !== null && _g !== void 0 ? _g : "0";
        message.totalMembersCount = (_h = object.totalMembersCount) !== null && _h !== void 0 ? _h : 0;
        message.startTime = (_j = object.startTime) !== null && _j !== void 0 ? _j : "0";
        message.endTime = (_k = object.endTime) !== null && _k !== void 0 ? _k : "0";
        return message;
    },
};
function createBaseListGuildMembersRequest() {
    return { campaignContract: "", guildId: "", limit: 0, skip: 0, includeGuildInfo: false, sortBy: "" };
}
exports.ListGuildMembersRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignContract !== "") {
            writer.uint32(10).string(message.campaignContract);
        }
        if (message.guildId !== "") {
            writer.uint32(18).string(message.guildId);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        if (message.skip !== 0) {
            writer.uint32(32).sint32(message.skip);
        }
        if (message.includeGuildInfo === true) {
            writer.uint32(40).bool(message.includeGuildInfo);
        }
        if (message.sortBy !== "") {
            writer.uint32(50).string(message.sortBy);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListGuildMembersRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignContract = reader.string();
                    break;
                case 2:
                    message.guildId = reader.string();
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                case 4:
                    message.skip = reader.sint32();
                    break;
                case 5:
                    message.includeGuildInfo = reader.bool();
                    break;
                case 6:
                    message.sortBy = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            guildId: isSet(object.guildId) ? String(object.guildId) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
            skip: isSet(object.skip) ? Number(object.skip) : 0,
            includeGuildInfo: isSet(object.includeGuildInfo) ? Boolean(object.includeGuildInfo) : false,
            sortBy: isSet(object.sortBy) ? String(object.sortBy) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.guildId !== undefined && (obj.guildId = message.guildId);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        message.skip !== undefined && (obj.skip = Math.round(message.skip));
        message.includeGuildInfo !== undefined && (obj.includeGuildInfo = message.includeGuildInfo);
        message.sortBy !== undefined && (obj.sortBy = message.sortBy);
        return obj;
    },
    create(base) {
        return exports.ListGuildMembersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseListGuildMembersRequest();
        message.campaignContract = (_a = object.campaignContract) !== null && _a !== void 0 ? _a : "";
        message.guildId = (_b = object.guildId) !== null && _b !== void 0 ? _b : "";
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        message.skip = (_d = object.skip) !== null && _d !== void 0 ? _d : 0;
        message.includeGuildInfo = (_e = object.includeGuildInfo) !== null && _e !== void 0 ? _e : false;
        message.sortBy = (_f = object.sortBy) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseListGuildMembersResponse() {
    return { members: [], paging: undefined, guildInfo: undefined };
}
exports.ListGuildMembersResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.members) {
            exports.GuildMember.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.paging !== undefined) {
            exports.Paging.encode(message.paging, writer.uint32(18).fork()).ldelim();
        }
        if (message.guildInfo !== undefined) {
            exports.Guild.encode(message.guildInfo, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListGuildMembersResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.members.push(exports.GuildMember.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.paging = exports.Paging.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.guildInfo = exports.Guild.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            members: Array.isArray(object === null || object === void 0 ? void 0 : object.members) ? object.members.map((e) => exports.GuildMember.fromJSON(e)) : [],
            paging: isSet(object.paging) ? exports.Paging.fromJSON(object.paging) : undefined,
            guildInfo: isSet(object.guildInfo) ? exports.Guild.fromJSON(object.guildInfo) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.members) {
            obj.members = message.members.map((e) => e ? exports.GuildMember.toJSON(e) : undefined);
        }
        else {
            obj.members = [];
        }
        message.paging !== undefined && (obj.paging = message.paging ? exports.Paging.toJSON(message.paging) : undefined);
        message.guildInfo !== undefined &&
            (obj.guildInfo = message.guildInfo ? exports.Guild.toJSON(message.guildInfo) : undefined);
        return obj;
    },
    create(base) {
        return exports.ListGuildMembersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseListGuildMembersResponse();
        message.members = ((_a = object.members) === null || _a === void 0 ? void 0 : _a.map((e) => exports.GuildMember.fromPartial(e))) || [];
        message.paging = (object.paging !== undefined && object.paging !== null)
            ? exports.Paging.fromPartial(object.paging)
            : undefined;
        message.guildInfo = (object.guildInfo !== undefined && object.guildInfo !== null)
            ? exports.Guild.fromPartial(object.guildInfo)
            : undefined;
        return message;
    },
};
function createBaseGuildMember() {
    return {
        campaignContract: "",
        guildId: "",
        address: "",
        joinedAt: "0",
        tvlScore: "",
        volumeScore: "",
        totalTvl: "",
        volumeScorePercentage: 0,
        tvlScorePercentage: 0,
        tvlReward: [],
        volumeReward: [],
    };
}
exports.GuildMember = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignContract !== "") {
            writer.uint32(10).string(message.campaignContract);
        }
        if (message.guildId !== "") {
            writer.uint32(18).string(message.guildId);
        }
        if (message.address !== "") {
            writer.uint32(26).string(message.address);
        }
        if (message.joinedAt !== "0") {
            writer.uint32(32).sint64(message.joinedAt);
        }
        if (message.tvlScore !== "") {
            writer.uint32(42).string(message.tvlScore);
        }
        if (message.volumeScore !== "") {
            writer.uint32(50).string(message.volumeScore);
        }
        if (message.totalTvl !== "") {
            writer.uint32(58).string(message.totalTvl);
        }
        if (message.volumeScorePercentage !== 0) {
            writer.uint32(65).double(message.volumeScorePercentage);
        }
        if (message.tvlScorePercentage !== 0) {
            writer.uint32(73).double(message.tvlScorePercentage);
        }
        for (const v of message.tvlReward) {
            exports.Coin.encode(v, writer.uint32(82).fork()).ldelim();
        }
        for (const v of message.volumeReward) {
            exports.Coin.encode(v, writer.uint32(90).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGuildMember();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignContract = reader.string();
                    break;
                case 2:
                    message.guildId = reader.string();
                    break;
                case 3:
                    message.address = reader.string();
                    break;
                case 4:
                    message.joinedAt = longToString(reader.sint64());
                    break;
                case 5:
                    message.tvlScore = reader.string();
                    break;
                case 6:
                    message.volumeScore = reader.string();
                    break;
                case 7:
                    message.totalTvl = reader.string();
                    break;
                case 8:
                    message.volumeScorePercentage = reader.double();
                    break;
                case 9:
                    message.tvlScorePercentage = reader.double();
                    break;
                case 10:
                    message.tvlReward.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.volumeReward.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            guildId: isSet(object.guildId) ? String(object.guildId) : "",
            address: isSet(object.address) ? String(object.address) : "",
            joinedAt: isSet(object.joinedAt) ? String(object.joinedAt) : "0",
            tvlScore: isSet(object.tvlScore) ? String(object.tvlScore) : "",
            volumeScore: isSet(object.volumeScore) ? String(object.volumeScore) : "",
            totalTvl: isSet(object.totalTvl) ? String(object.totalTvl) : "",
            volumeScorePercentage: isSet(object.volumeScorePercentage) ? Number(object.volumeScorePercentage) : 0,
            tvlScorePercentage: isSet(object.tvlScorePercentage) ? Number(object.tvlScorePercentage) : 0,
            tvlReward: Array.isArray(object === null || object === void 0 ? void 0 : object.tvlReward) ? object.tvlReward.map((e) => exports.Coin.fromJSON(e)) : [],
            volumeReward: Array.isArray(object === null || object === void 0 ? void 0 : object.volumeReward) ? object.volumeReward.map((e) => exports.Coin.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.guildId !== undefined && (obj.guildId = message.guildId);
        message.address !== undefined && (obj.address = message.address);
        message.joinedAt !== undefined && (obj.joinedAt = message.joinedAt);
        message.tvlScore !== undefined && (obj.tvlScore = message.tvlScore);
        message.volumeScore !== undefined && (obj.volumeScore = message.volumeScore);
        message.totalTvl !== undefined && (obj.totalTvl = message.totalTvl);
        message.volumeScorePercentage !== undefined && (obj.volumeScorePercentage = message.volumeScorePercentage);
        message.tvlScorePercentage !== undefined && (obj.tvlScorePercentage = message.tvlScorePercentage);
        if (message.tvlReward) {
            obj.tvlReward = message.tvlReward.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.tvlReward = [];
        }
        if (message.volumeReward) {
            obj.volumeReward = message.volumeReward.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.volumeReward = [];
        }
        return obj;
    },
    create(base) {
        return exports.GuildMember.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        const message = createBaseGuildMember();
        message.campaignContract = (_a = object.campaignContract) !== null && _a !== void 0 ? _a : "";
        message.guildId = (_b = object.guildId) !== null && _b !== void 0 ? _b : "";
        message.address = (_c = object.address) !== null && _c !== void 0 ? _c : "";
        message.joinedAt = (_d = object.joinedAt) !== null && _d !== void 0 ? _d : "0";
        message.tvlScore = (_e = object.tvlScore) !== null && _e !== void 0 ? _e : "";
        message.volumeScore = (_f = object.volumeScore) !== null && _f !== void 0 ? _f : "";
        message.totalTvl = (_g = object.totalTvl) !== null && _g !== void 0 ? _g : "";
        message.volumeScorePercentage = (_h = object.volumeScorePercentage) !== null && _h !== void 0 ? _h : 0;
        message.tvlScorePercentage = (_j = object.tvlScorePercentage) !== null && _j !== void 0 ? _j : 0;
        message.tvlReward = ((_k = object.tvlReward) === null || _k === void 0 ? void 0 : _k.map((e) => exports.Coin.fromPartial(e))) || [];
        message.volumeReward = ((_l = object.volumeReward) === null || _l === void 0 ? void 0 : _l.map((e) => exports.Coin.fromPartial(e))) || [];
        return message;
    },
};
function createBaseGetGuildMemberRequest() {
    return { campaignContract: "", address: "" };
}
exports.GetGuildMemberRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.campaignContract !== "") {
            writer.uint32(10).string(message.campaignContract);
        }
        if (message.address !== "") {
            writer.uint32(18).string(message.address);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetGuildMemberRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignContract = reader.string();
                    break;
                case 2:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            campaignContract: isSet(object.campaignContract) ? String(object.campaignContract) : "",
            address: isSet(object.address) ? String(object.address) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.campaignContract !== undefined && (obj.campaignContract = message.campaignContract);
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create(base) {
        return exports.GetGuildMemberRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseGetGuildMemberRequest();
        message.campaignContract = (_a = object.campaignContract) !== null && _a !== void 0 ? _a : "";
        message.address = (_b = object.address) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseGetGuildMemberResponse() {
    return { info: undefined };
}
exports.GetGuildMemberResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.info !== undefined) {
            exports.GuildMember.encode(message.info, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetGuildMemberResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.info = exports.GuildMember.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { info: isSet(object.info) ? exports.GuildMember.fromJSON(object.info) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.info !== undefined && (obj.info = message.info ? exports.GuildMember.toJSON(message.info) : undefined);
        return obj;
    },
    create(base) {
        return exports.GetGuildMemberResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseGetGuildMemberResponse();
        message.info = (object.info !== undefined && object.info !== null)
            ? exports.GuildMember.fromPartial(object.info)
            : undefined;
        return message;
    },
};
class InjectiveCampaignRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.Ranking = this.Ranking.bind(this);
        this.Campaigns = this.Campaigns.bind(this);
        this.CampaignsV2 = this.CampaignsV2.bind(this);
        this.ListGuilds = this.ListGuilds.bind(this);
        this.ListGuildMembers = this.ListGuildMembers.bind(this);
        this.GetGuildMember = this.GetGuildMember.bind(this);
    }
    Ranking(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCRankingDesc, exports.RankingRequest.fromPartial(request), metadata);
    }
    Campaigns(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCCampaignsDesc, exports.CampaignsRequest.fromPartial(request), metadata);
    }
    CampaignsV2(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCCampaignsV2Desc, exports.CampaignsV2Request.fromPartial(request), metadata);
    }
    ListGuilds(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCListGuildsDesc, exports.ListGuildsRequest.fromPartial(request), metadata);
    }
    ListGuildMembers(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCListGuildMembersDesc, exports.ListGuildMembersRequest.fromPartial(request), metadata);
    }
    GetGuildMember(request, metadata) {
        return this.rpc.unary(exports.InjectiveCampaignRPCGetGuildMemberDesc, exports.GetGuildMemberRequest.fromPartial(request), metadata);
    }
}
exports.InjectiveCampaignRPCClientImpl = InjectiveCampaignRPCClientImpl;
exports.InjectiveCampaignRPCDesc = { serviceName: "injective_campaign_rpc.InjectiveCampaignRPC" };
exports.InjectiveCampaignRPCRankingDesc = {
    methodName: "Ranking",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.RankingRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.RankingResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveCampaignRPCCampaignsDesc = {
    methodName: "Campaigns",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.CampaignsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.CampaignsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveCampaignRPCCampaignsV2Desc = {
    methodName: "CampaignsV2",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.CampaignsV2Request.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.CampaignsV2Response.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveCampaignRPCListGuildsDesc = {
    methodName: "ListGuilds",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.ListGuildsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.ListGuildsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveCampaignRPCListGuildMembersDesc = {
    methodName: "ListGuildMembers",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.ListGuildMembersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.ListGuildMembersResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveCampaignRPCGetGuildMemberDesc = {
    methodName: "GetGuildMember",
    service: exports.InjectiveCampaignRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.GetGuildMemberRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.GetGuildMemberResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
