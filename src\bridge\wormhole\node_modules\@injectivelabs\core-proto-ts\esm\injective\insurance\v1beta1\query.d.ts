import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { GenesisState } from "./genesis";
import { InsuranceFund, Params } from "./insurance";
export declare const protobufPackage = "injective.insurance.v1beta1";
/**
 * QueryInsuranceParamsRequest is the request type for the Query/InsuranceParams
 * RPC method.
 */
export interface QueryInsuranceParamsRequest {
}
/**
 * QueryInsuranceParamsRequest is the response type for the
 * Query/InsuranceParams RPC method.
 */
export interface QueryInsuranceParamsResponse {
    params: Params | undefined;
}
/**
 * QueryInsuranceFundRequest is the request type for the Query/InsuranceFunds
 * RPC method.
 */
export interface QueryInsuranceFundRequest {
    /** Market ID for the market */
    marketId: string;
}
/**
 * QueryInsuranceFundResponse is the response type for the Query/InsuranceFund
 * RPC method.
 */
export interface QueryInsuranceFundResponse {
    fund: InsuranceFund | undefined;
}
/**
 * QueryInsuranceFundsRequest is the request type for the Query/InsuranceFunds
 * RPC method.
 */
export interface QueryInsuranceFundsRequest {
}
/**
 * QueryInsuranceFundsResponse is the response type for the Query/InsuranceFunds
 * RPC method.
 */
export interface QueryInsuranceFundsResponse {
    funds: InsuranceFund[];
}
/**
 * QueryEstimatedRedemptionsRequest is the request type for the
 * Query/EstimatedRedemptions RPC method.
 */
export interface QueryEstimatedRedemptionsRequest {
    marketId: string;
    address: string;
}
/**
 * QueryEstimatedRedemptionsResponse is the response type for the
 * Query/EstimatedRedemptions RPC method.
 */
export interface QueryEstimatedRedemptionsResponse {
    amount: Coin[];
}
/**
 * QueryPendingRedemptionsRequest is the request type for the
 * Query/PendingRedemptions RPC method.
 */
export interface QueryPendingRedemptionsRequest {
    marketId: string;
    address: string;
}
/**
 * QueryPendingRedemptionsResponse is the response type for the
 * Query/PendingRedemptions RPC method.
 */
export interface QueryPendingRedemptionsResponse {
    amount: Coin[];
}
/**
 * QueryModuleStateRequest is the request type for the
 * Query/InsuranceModuleState RPC method.
 */
export interface QueryModuleStateRequest {
}
/**
 * QueryModuleStateResponse is the response type for the
 * Query/InsuranceModuleState RPC method.
 */
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
export declare const QueryInsuranceParamsRequest: {
    encode(_: QueryInsuranceParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceParamsRequest;
    fromJSON(_: any): QueryInsuranceParamsRequest;
    toJSON(_: QueryInsuranceParamsRequest): unknown;
    create(base?: DeepPartial<QueryInsuranceParamsRequest>): QueryInsuranceParamsRequest;
    fromPartial(_: DeepPartial<QueryInsuranceParamsRequest>): QueryInsuranceParamsRequest;
};
export declare const QueryInsuranceParamsResponse: {
    encode(message: QueryInsuranceParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceParamsResponse;
    fromJSON(object: any): QueryInsuranceParamsResponse;
    toJSON(message: QueryInsuranceParamsResponse): unknown;
    create(base?: DeepPartial<QueryInsuranceParamsResponse>): QueryInsuranceParamsResponse;
    fromPartial(object: DeepPartial<QueryInsuranceParamsResponse>): QueryInsuranceParamsResponse;
};
export declare const QueryInsuranceFundRequest: {
    encode(message: QueryInsuranceFundRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceFundRequest;
    fromJSON(object: any): QueryInsuranceFundRequest;
    toJSON(message: QueryInsuranceFundRequest): unknown;
    create(base?: DeepPartial<QueryInsuranceFundRequest>): QueryInsuranceFundRequest;
    fromPartial(object: DeepPartial<QueryInsuranceFundRequest>): QueryInsuranceFundRequest;
};
export declare const QueryInsuranceFundResponse: {
    encode(message: QueryInsuranceFundResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceFundResponse;
    fromJSON(object: any): QueryInsuranceFundResponse;
    toJSON(message: QueryInsuranceFundResponse): unknown;
    create(base?: DeepPartial<QueryInsuranceFundResponse>): QueryInsuranceFundResponse;
    fromPartial(object: DeepPartial<QueryInsuranceFundResponse>): QueryInsuranceFundResponse;
};
export declare const QueryInsuranceFundsRequest: {
    encode(_: QueryInsuranceFundsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceFundsRequest;
    fromJSON(_: any): QueryInsuranceFundsRequest;
    toJSON(_: QueryInsuranceFundsRequest): unknown;
    create(base?: DeepPartial<QueryInsuranceFundsRequest>): QueryInsuranceFundsRequest;
    fromPartial(_: DeepPartial<QueryInsuranceFundsRequest>): QueryInsuranceFundsRequest;
};
export declare const QueryInsuranceFundsResponse: {
    encode(message: QueryInsuranceFundsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryInsuranceFundsResponse;
    fromJSON(object: any): QueryInsuranceFundsResponse;
    toJSON(message: QueryInsuranceFundsResponse): unknown;
    create(base?: DeepPartial<QueryInsuranceFundsResponse>): QueryInsuranceFundsResponse;
    fromPartial(object: DeepPartial<QueryInsuranceFundsResponse>): QueryInsuranceFundsResponse;
};
export declare const QueryEstimatedRedemptionsRequest: {
    encode(message: QueryEstimatedRedemptionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryEstimatedRedemptionsRequest;
    fromJSON(object: any): QueryEstimatedRedemptionsRequest;
    toJSON(message: QueryEstimatedRedemptionsRequest): unknown;
    create(base?: DeepPartial<QueryEstimatedRedemptionsRequest>): QueryEstimatedRedemptionsRequest;
    fromPartial(object: DeepPartial<QueryEstimatedRedemptionsRequest>): QueryEstimatedRedemptionsRequest;
};
export declare const QueryEstimatedRedemptionsResponse: {
    encode(message: QueryEstimatedRedemptionsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryEstimatedRedemptionsResponse;
    fromJSON(object: any): QueryEstimatedRedemptionsResponse;
    toJSON(message: QueryEstimatedRedemptionsResponse): unknown;
    create(base?: DeepPartial<QueryEstimatedRedemptionsResponse>): QueryEstimatedRedemptionsResponse;
    fromPartial(object: DeepPartial<QueryEstimatedRedemptionsResponse>): QueryEstimatedRedemptionsResponse;
};
export declare const QueryPendingRedemptionsRequest: {
    encode(message: QueryPendingRedemptionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPendingRedemptionsRequest;
    fromJSON(object: any): QueryPendingRedemptionsRequest;
    toJSON(message: QueryPendingRedemptionsRequest): unknown;
    create(base?: DeepPartial<QueryPendingRedemptionsRequest>): QueryPendingRedemptionsRequest;
    fromPartial(object: DeepPartial<QueryPendingRedemptionsRequest>): QueryPendingRedemptionsRequest;
};
export declare const QueryPendingRedemptionsResponse: {
    encode(message: QueryPendingRedemptionsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPendingRedemptionsResponse;
    fromJSON(object: any): QueryPendingRedemptionsResponse;
    toJSON(message: QueryPendingRedemptionsResponse): unknown;
    create(base?: DeepPartial<QueryPendingRedemptionsResponse>): QueryPendingRedemptionsResponse;
    fromPartial(object: DeepPartial<QueryPendingRedemptionsResponse>): QueryPendingRedemptionsResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
/** Query defines the gRPC querier service. */
export interface Query {
    /** Retrieves insurance params */
    InsuranceParams(request: DeepPartial<QueryInsuranceParamsRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceParamsResponse>;
    /** Retrieves individual insurance fund information from market id */
    InsuranceFund(request: DeepPartial<QueryInsuranceFundRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceFundResponse>;
    /** Retrieves all insurance funds */
    InsuranceFunds(request: DeepPartial<QueryInsuranceFundsRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceFundsResponse>;
    /**
     * Retrives the value of insurance fund share token at current price (not
     * pending redemption)
     */
    EstimatedRedemptions(request: DeepPartial<QueryEstimatedRedemptionsRequest>, metadata?: grpc.Metadata): Promise<QueryEstimatedRedemptionsResponse>;
    /** Retrieves pending redemptions' share token at current price */
    PendingRedemptions(request: DeepPartial<QueryPendingRedemptionsRequest>, metadata?: grpc.Metadata): Promise<QueryPendingRedemptionsResponse>;
    /** Retrieves the entire insurance module's state */
    InsuranceModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    InsuranceParams(request: DeepPartial<QueryInsuranceParamsRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceParamsResponse>;
    InsuranceFund(request: DeepPartial<QueryInsuranceFundRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceFundResponse>;
    InsuranceFunds(request: DeepPartial<QueryInsuranceFundsRequest>, metadata?: grpc.Metadata): Promise<QueryInsuranceFundsResponse>;
    EstimatedRedemptions(request: DeepPartial<QueryEstimatedRedemptionsRequest>, metadata?: grpc.Metadata): Promise<QueryEstimatedRedemptionsResponse>;
    PendingRedemptions(request: DeepPartial<QueryPendingRedemptionsRequest>, metadata?: grpc.Metadata): Promise<QueryPendingRedemptionsResponse>;
    InsuranceModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryInsuranceParamsDesc: UnaryMethodDefinitionish;
export declare const QueryInsuranceFundDesc: UnaryMethodDefinitionish;
export declare const QueryInsuranceFundsDesc: UnaryMethodDefinitionish;
export declare const QueryEstimatedRedemptionsDesc: UnaryMethodDefinitionish;
export declare const QueryPendingRedemptionsDesc: UnaryMethodDefinitionish;
export declare const QueryInsuranceModuleStateDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
