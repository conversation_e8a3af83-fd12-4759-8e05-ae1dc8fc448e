{"version": 3, "file": "rpc_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/rpc_errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAKF,8DAAsD;AACtD,sDAa2B;AAC3B,mEAA2F;AAE3F,MAAa,QAAS,SAAQ,kCAAa;IAK1C,YAAmB,QAAkC,EAAE,OAAgB;QACtE,KAAK,CACJ,OAAO,aAAP,OAAO,cAAP,OAAO,GACN,sDAA8B,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CACjF,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAChC,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC;IACpC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IAC5F,CAAC;CACD;AAnBD,4BAmBC;AAED,MAAa,uBAAwB,SAAQ,kCAAa;IAIzD,YAAmB,IAAY,EAAE,IAAc;;QAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,sCAAsC;YACtC,KAAK,EAAE,CAAC;QACT,CAAC;aAAM,IAAI,MAAA,wCAAgB,CAAC,IAAI,CAAC,0CAAE,OAAO,EAAE,CAAC;YAC5C,KAAK,CAAC,wCAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACP,gHAAgH;YAChH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,wCAAgB,CAAC,CAAC,IAAI,CACzD,UAAU,CAAC,EAAE,CACZ,OAAO,UAAU,KAAK,QAAQ;gBAC9B,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC9C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAC/C,CAAC;YACF,KAAK,CACJ,MAAA,MAAA,wCAAgB,CAAC,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,EAAE,CAAC,0CAAE,OAAO,mCAC/C,sDAA8B,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,mCAAI,IAAI,CAAC,CAC3E,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,CAAC;CACD;AA1BD,0DA0BC;AAED,MAAa,UAAW,SAAQ,QAAQ;IAEvC,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,qCAAoB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF1D,SAAI,GAAG,qCAAoB,CAAC;IAGnC,CAAC;CACD;AALD,gCAKC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAEhD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,wCAAuB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF7D,SAAI,GAAG,wCAAuB,CAAC;IAGtC,CAAC;CACD;AALD,kDAKC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAEhD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,uCAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,uCAAsB,CAAC;IAGrC,CAAC;CACD;AALD,kDAKC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,uCAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,uCAAsB,CAAC;IAGrC,CAAC;CACD;AALD,gDAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IAE1C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,uCAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,uCAAsB,CAAC;IAGrC,CAAC;CACD;AALD,sCAKC;AAED,MAAa,iBAAkB,SAAQ,QAAQ;IAE9C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,sCAAqB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF3D,SAAI,GAAG,sCAAqB,CAAC;IAGpC,CAAC;CACD;AALD,8CAKC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,2CAA0B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFhE,SAAI,GAAG,2CAA0B,CAAC;IAGzC,CAAC;CACD;AALD,gDAKC;AAED,MAAa,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,6CAA4B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFlE,SAAI,GAAG,6CAA4B,CAAC;IAG3C,CAAC;CACD;AALD,4DAKC;AAED,MAAa,sBAAuB,SAAQ,QAAQ;IAEnD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,yCAAwB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF9D,SAAI,GAAG,yCAAwB,CAAC;IAGvC,CAAC;CACD;AALD,wDAKC;AAED,MAAa,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,sCAAqB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF3D,SAAI,GAAG,sCAAqB,CAAC;IAGpC,CAAC;CACD;AALD,4DAKC;AAED,MAAa,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,6CAA4B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFlE,SAAI,GAAG,6CAA4B,CAAC;IAG3C,CAAC;CACD;AALD,4DAKC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,wCAAgB,CAAC,uCAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,uCAAsB,CAAC;IAGrC,CAAC;CACD;AALD,gDAKC;AAEY,QAAA,YAAY,GAAG,IAAI,GAAG,EAAsC,CAAC;AAC1E,oBAAY,CAAC,GAAG,CAAC,qCAAoB,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AAC9D,oBAAY,CAAC,GAAG,CAAC,wCAAuB,EAAE;IACzC,KAAK,EAAE,mBAAmB;CAC1B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,uCAAsB,EAAE;IACxC,KAAK,EAAE,mBAAmB;CAC1B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,uCAAsB,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;AACxE,oBAAY,CAAC,GAAG,CAAC,uCAAsB,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AACnE,oBAAY,CAAC,GAAG,CAAC,sCAAqB,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACtE,oBAAY,CAAC,GAAG,CAAC,2CAA0B,EAAE;IAC5C,KAAK,EAAE,kBAAkB;CACzB,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,6CAA4B,EAAE;IAC9C,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,6CAA4B,EAAE;IAC9C,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,yCAAwB,EAAE;IAC1C,KAAK,EAAE,sBAAsB;CAC7B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,sCAAqB,EAAE;IACvC,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,oBAAY,CAAC,GAAG,CAAC,uCAAsB,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC"}