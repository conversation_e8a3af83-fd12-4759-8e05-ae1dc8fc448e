{"version": 3, "file": "trie.d.ts", "sourceRoot": "", "sources": ["../../src/trie.ts"], "names": [], "mappings": "AAIA,OAAO,EAEL,IAAI,EAGJ,aAAa,EAOd,MAAM,kBAAkB,CAAA;AAIzB,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAA;AAa5C,OAAO,EAAE,cAAc,IAAI,UAAU,EAAE,MAAM,sBAAsB,CAAA;AAGnE,OAAO,KAAK,EACV,YAAY,EACZ,iBAAiB,EACjB,OAAO,EACP,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,oBAAoB,EACpB,mBAAmB,EACpB,MAAM,YAAY,CAAA;AACnB,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,KAAK,EAAE,SAAS,EAAE,EAAE,EAAY,MAAM,kBAAkB,CAAA;AAC/D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAA;AAErC;;GAEG;AACH,qBAAa,IAAI;IACf,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,oBAAoB,CAQ7C;IAED,iCAAiC;IACjC,eAAe,EAAE,UAAU,CAAA;IAE3B,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAG,YAAY,CAAA;IAC5B,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAA;IAC1B,SAAS,CAAC,KAAK,OAAa;IAC5B,SAAS,CAAC,KAAK,EAAE,UAAU,CAAA;IAE3B,oBAAoB;IACpB,SAAS,CAAC,KAAK,EAAE,OAAO,CAAA;IACxB,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAgB;IAC1C,SAAS,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,IAAI,CAAA;IAEvC;;;;;OAKG;gBACS,IAAI,CAAC,EAAE,QAAQ;IAmD3B;;;;;;;OAOG;WACU,eAAe,CAC1B,KAAK,EAAE,KAAK,EACZ,QAAQ,CAAC,EAAE,QAAQ,EACnB,gBAAgB,GAAE,OAAe;IASnC;;;;;;;;;OASG;WACU,WAAW,CACtB,GAAG,EAAE,UAAU,EACf,KAAK,EAAE,KAAK,EACZ,IAAI,CAAC,EAAE,QAAQ,GACd,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAU7B;;;;;;;;;;;;;OAaG;IACH,MAAM,CAAC,gBAAgB,CACrB,QAAQ,EAAE,UAAU,EACpB,QAAQ,EAAE,UAAU,GAAG,IAAI,EAC3B,OAAO,EAAE,UAAU,GAAG,IAAI,EAC1B,IAAI,EAAE,UAAU,EAAE,EAClB,MAAM,EAAE,UAAU,EAAE,EACpB,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,EAC1B,IAAI,CAAC,EAAE,QAAQ,GACd,OAAO,CAAC,OAAO,CAAC;IAYnB;;;;;OAKG;WACU,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAWpE;;;;;;;;;;;;OAYG;IACH,gBAAgB,CACd,QAAQ,EAAE,UAAU,EACpB,QAAQ,EAAE,UAAU,GAAG,IAAI,EAC3B,OAAO,EAAE,UAAU,GAAG,IAAI,EAC1B,IAAI,EAAE,UAAU,EAAE,EAClB,MAAM,EAAE,UAAU,EAAE,EACpB,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,GACzB,OAAO,CAAC,OAAO,CAAC;IAYnB;;;;;OAKG;IACG,WAAW,CAAC,GAAG,EAAE,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC;IAUlD;;;;;;;OAOG;IACG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,gBAAgB,GAAE,OAAe;IA0BrE;;;;;;;;OAQG;IACG,WAAW,CACf,QAAQ,EAAE,UAAU,EACpB,GAAG,EAAE,UAAU,EACf,KAAK,EAAE,KAAK,GACX,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAoC7B;;;;;;OAMG;IACG,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;WAa/B,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ;IAyCnC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,aAAa;IAY5E;;OAEG;IACH,IAAI,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,IAAI,GAAG,UAAU;IAiB3C;;OAEG;IACG,SAAS,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC;IAanD;;;;;OAKG;IACG,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,cAAc,UAAQ,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAW9E;;;;;;OAMG;IACG,GAAG,CACP,GAAG,EAAE,UAAU,EACf,KAAK,EAAE,UAAU,GAAG,IAAI,EACxB,gBAAgB,GAAE,OAAe,GAChC,OAAO,CAAC,IAAI,CAAC;IAyDhB;;;;;OAKG;IACG,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,gBAAgB,GAAE,OAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAqC5E;;;;;OAKG;IACG,QAAQ,CACZ,GAAG,EAAE,UAAU,EACf,cAAc,UAAQ,EACtB,WAAW,GAAE;QACX,KAAK,EAAE,QAAQ,EAAE,CAAA;KAGlB,GACA,OAAO,CAAC,IAAI,CAAC;IAyHhB;;;;;OAKG;IACG,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAI3E,gBAAgB;;;OAAuB;IAEvC;;;;OAIG;IACG,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAMnD;;;;OAIG;IACG,iBAAiB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAaxD;;;OAGG;cACa,kBAAkB,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC;IAWrF;;OAEG;IACG,UAAU,CAAC,IAAI,EAAE,UAAU,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC;IAoBpE;;;;;;;OAOG;cACa,WAAW,CACzB,CAAC,EAAE,UAAU,EACb,KAAK,EAAE,UAAU,EACjB,YAAY,EAAE,OAAO,EACrB,KAAK,EAAE,QAAQ,EAAE,GAChB,OAAO,CAAC,IAAI,CAAC;IA8FhB;;;OAGG;cACa,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAkI5E;;;;;;OAMG;IACG,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IA8BrF;;;;;;;;OAQG;IACH,WAAW,CACT,IAAI,EAAE,QAAQ,EACd,QAAQ,EAAE,OAAO,EACjB,OAAO,EAAE,SAAS,EAAE,EACpB,MAAM,GAAE,OAAe,GACtB,UAAU,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE;IA4BvC;;;;;;;;;;;;;OAaG;IACG,KAAK,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE,gBAAgB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAkBlE,qBAAqB,IAAI,OAAO,CAAC,OAAO,CAAC;IAuD/C;;;OAGG;IACH,gBAAgB,IAAI,UAAU;IAI9B;;;;;;;;;;;;OAYG;IACH,WAAW,CAAC,kBAAkB,UAAO,EAAE,IAAI,CAAC,EAAE,mBAAmB,GAAG,IAAI;IAcxE;;OAEG;IACG,WAAW;IAejB;;;;;OAKG;cACa,YAAY,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAavE;;;;OAIG;IACH,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU;IAOpC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAI3C;;OAEG;IACH,cAAc;IAId;;;OAGG;IACH,UAAU;IAKV;;;;OAIG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAW7B;;;;OAIG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAa7B;;OAEG;IACH,gBAAgB;CAKjB"}