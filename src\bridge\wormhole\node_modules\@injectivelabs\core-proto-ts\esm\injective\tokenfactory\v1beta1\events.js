/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Metadata } from "../../../cosmos/bank/v1beta1/bank.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin.js";
export const protobufPackage = "injective.tokenfactory.v1beta1";
function createBaseEventCreateDenom() {
    return { account: "", denom: "" };
}
export const EventCreateDenom = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventCreateDenom();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return EventCreateDenom.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventCreateDenom();
        message.account = object.account ?? "";
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseEventMint() {
    return { minter: "", amount: undefined, receiver: "" };
}
export const EventMint = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.minter !== "") {
            writer.uint32(10).string(message.minter);
        }
        if (message.amount !== undefined) {
            Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        if (message.receiver !== "") {
            writer.uint32(26).string(message.receiver);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventMint();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.minter = reader.string();
                    break;
                case 2:
                    message.amount = Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.receiver = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            minter: isSet(object.minter) ? String(object.minter) : "",
            amount: isSet(object.amount) ? Coin.fromJSON(object.amount) : undefined,
            receiver: isSet(object.receiver) ? String(object.receiver) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.minter !== undefined && (obj.minter = message.minter);
        message.amount !== undefined && (obj.amount = message.amount ? Coin.toJSON(message.amount) : undefined);
        message.receiver !== undefined && (obj.receiver = message.receiver);
        return obj;
    },
    create(base) {
        return EventMint.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventMint();
        message.minter = object.minter ?? "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? Coin.fromPartial(object.amount)
            : undefined;
        message.receiver = object.receiver ?? "";
        return message;
    },
};
function createBaseEventBurn() {
    return { burner: "", amount: undefined, burnFrom: "" };
}
export const EventBurn = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.burner !== "") {
            writer.uint32(10).string(message.burner);
        }
        if (message.amount !== undefined) {
            Coin.encode(message.amount, writer.uint32(18).fork()).ldelim();
        }
        if (message.burnFrom !== "") {
            writer.uint32(26).string(message.burnFrom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventBurn();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.burner = reader.string();
                    break;
                case 2:
                    message.amount = Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.burnFrom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            burner: isSet(object.burner) ? String(object.burner) : "",
            amount: isSet(object.amount) ? Coin.fromJSON(object.amount) : undefined,
            burnFrom: isSet(object.burnFrom) ? String(object.burnFrom) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.burner !== undefined && (obj.burner = message.burner);
        message.amount !== undefined && (obj.amount = message.amount ? Coin.toJSON(message.amount) : undefined);
        message.burnFrom !== undefined && (obj.burnFrom = message.burnFrom);
        return obj;
    },
    create(base) {
        return EventBurn.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventBurn();
        message.burner = object.burner ?? "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? Coin.fromPartial(object.amount)
            : undefined;
        message.burnFrom = object.burnFrom ?? "";
        return message;
    },
};
function createBaseEventChangeAdmin() {
    return { denom: "", newAdminAddress: "" };
}
export const EventChangeAdmin = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.newAdminAddress !== "") {
            writer.uint32(18).string(message.newAdminAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventChangeAdmin();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.newAdminAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            newAdminAddress: isSet(object.newAdminAddress) ? String(object.newAdminAddress) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.newAdminAddress !== undefined && (obj.newAdminAddress = message.newAdminAddress);
        return obj;
    },
    create(base) {
        return EventChangeAdmin.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventChangeAdmin();
        message.denom = object.denom ?? "";
        message.newAdminAddress = object.newAdminAddress ?? "";
        return message;
    },
};
function createBaseEventSetDenomMetadata() {
    return { denom: "", metadata: undefined };
}
export const EventSetDenomMetadata = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.metadata !== undefined) {
            Metadata.encode(message.metadata, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventSetDenomMetadata();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.metadata = Metadata.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            metadata: isSet(object.metadata) ? Metadata.fromJSON(object.metadata) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.metadata !== undefined && (obj.metadata = message.metadata ? Metadata.toJSON(message.metadata) : undefined);
        return obj;
    },
    create(base) {
        return EventSetDenomMetadata.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventSetDenomMetadata();
        message.denom = object.denom ?? "";
        message.metadata = (object.metadata !== undefined && object.metadata !== null)
            ? Metadata.fromPartial(object.metadata)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
