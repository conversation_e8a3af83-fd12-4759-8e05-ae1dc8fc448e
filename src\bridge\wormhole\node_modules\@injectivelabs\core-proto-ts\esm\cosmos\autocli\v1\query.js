/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
import { ModuleOptions } from "./options.js";
export const protobufPackage = "cosmos.autocli.v1";
function createBaseAppOptionsRequest() {
    return {};
}
export const AppOptionsRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAppOptionsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return AppOptionsRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseAppOptionsRequest();
        return message;
    },
};
function createBaseAppOptionsResponse() {
    return { moduleOptions: {} };
}
export const AppOptionsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        Object.entries(message.moduleOptions).forEach(([key, value]) => {
            AppOptionsResponse_ModuleOptionsEntry.encode({ key: key, value }, writer.uint32(10).fork()).ldelim();
        });
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAppOptionsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    const entry1 = AppOptionsResponse_ModuleOptionsEntry.decode(reader, reader.uint32());
                    if (entry1.value !== undefined) {
                        message.moduleOptions[entry1.key] = entry1.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            moduleOptions: isObject(object.moduleOptions)
                ? Object.entries(object.moduleOptions).reduce((acc, [key, value]) => {
                    acc[key] = ModuleOptions.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON(message) {
        const obj = {};
        obj.moduleOptions = {};
        if (message.moduleOptions) {
            Object.entries(message.moduleOptions).forEach(([k, v]) => {
                obj.moduleOptions[k] = ModuleOptions.toJSON(v);
            });
        }
        return obj;
    },
    create(base) {
        return AppOptionsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAppOptionsResponse();
        message.moduleOptions = Object.entries(object.moduleOptions ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = ModuleOptions.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseAppOptionsResponse_ModuleOptionsEntry() {
    return { key: "", value: undefined };
}
export const AppOptionsResponse_ModuleOptionsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            ModuleOptions.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAppOptionsResponse_ModuleOptionsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = ModuleOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? ModuleOptions.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? ModuleOptions.toJSON(message.value) : undefined);
        return obj;
    },
    create(base) {
        return AppOptionsResponse_ModuleOptionsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAppOptionsResponse_ModuleOptionsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? ModuleOptions.fromPartial(object.value)
            : undefined;
        return message;
    },
};
export class QueryClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.AppOptions = this.AppOptions.bind(this);
    }
    AppOptions(request, metadata) {
        return this.rpc.unary(QueryAppOptionsDesc, AppOptionsRequest.fromPartial(request), metadata);
    }
}
export const QueryDesc = { serviceName: "cosmos.autocli.v1.Query" };
export const QueryAppOptionsDesc = {
    methodName: "AppOptions",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return AppOptionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = AppOptionsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
