{"name": "eth-lib", "version": "0.1.29", "description": "Lightweight Ethereum libraries", "main": "lib/index.js", "scripts": {"babel": "babel src --out-dir=lib", "test": "mocha test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/maiavictor/eth-lib.git"}, "keywords": ["ethereum"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/maiavictor/eth-lib/issues"}, "homepage": "https://github.com/maiavictor/eth-lib#readme", "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-es2015": "^6.24.1", "ethjs-account": "^0.1.1", "ethjs-signer": "^0.1.1", "forall": "^0.1.1", "rlp": "^2.0.0"}, "dependencies": {"bn.js": "^4.11.6", "elliptic": "^6.4.0", "nano-json-stream-parser": "^0.1.2", "servify": "^0.1.12", "ws": "^3.0.0", "xhr-request-promise": "^0.1.2"}}