"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MsgDecoder = void 0;
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const index_js_1 = require("../../modules/exchange/index.js");
const index_js_2 = require("../../../utils/index.js");
class MsgDecoder {
    static decode(message) {
        const type = message.typeUrl;
        switch (true) {
            case type.includes('MsgIncreasePositionMargin'): {
                const msg = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgIncreasePositionMargin.decode(message.value);
                return index_js_1.MsgIncreasePositionMargin.fromJSON({
                    marketId: msg.marketId,
                    injectiveAddress: msg.sender,
                    srcSubaccountId: msg.sourceSubaccountId,
                    dstSubaccountId: msg.destinationSubaccountId,
                    amount: msg.amount,
                });
            }
            case type.includes('MsgSignData'): {
                const msg = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgSignData.decode(message.value);
                return index_js_1.MsgSignData.fromJSON({
                    data: Buffer.from(msg.Data).toString('utf-8'),
                    sender: (0, index_js_2.getInjectiveAddress)(Buffer.from(msg.Signer).toString('hex')),
                });
            }
            default:
                throw new Error('Unknown message type');
        }
    }
}
exports.MsgDecoder = MsgDecoder;
