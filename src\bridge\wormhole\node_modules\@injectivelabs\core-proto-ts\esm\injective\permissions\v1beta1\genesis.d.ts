import _m0 from "protobufjs/minimal.js";
import { Params } from "./params";
import { AddressVoucher, Namespace } from "./permissions";
export declare const protobufPackage = "injective.permissions.v1beta1";
/** GenesisState defines the permissions module's genesis state. */
export interface GenesisState {
    /** params defines the parameters of the module. */
    params: Params | undefined;
    namespaces: Namespace[];
    vouchers: AddressVoucher[];
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
