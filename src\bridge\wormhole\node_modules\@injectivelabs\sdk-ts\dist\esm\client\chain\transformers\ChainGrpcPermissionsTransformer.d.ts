import { PermissionRole, PermissionNamespace, PermissionActorRoles, PermissionRoleManager, PermissionPolicyStatus, PermissionsModuleParams, PermissionAddressVoucher, PermissionPolicyManagerCapability, GrpcPermissionRole, GrpcPermissionNamespace, GrpcPermissionActorRoles, GrpcPermissionRoleManager, GrpcPermissionPolicyStatus, GrpcPermissionAddressVoucher, GrpcPermissionPolicyStatusManagerCapability } from '../types/permissions.js';
import { InjectivePermissionsV1Beta1Query } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcPermissionsTransformer {
    static grpcRoleToRole(grpcRole: GrpcPermissionRole): PermissionRole;
    static grpcActorRolesToActorRoles(grpcActorRoles: GrpcPermissionActorRoles): PermissionActorRoles;
    static grpcRoleManagerToRoleManager(grpcRoleManager: GrpcPermissionRoleManager): PermissionRoleManager;
    static grpcPolicyStatusToPolicyStatus(grpcPolicyStatus: GrpcPermissionPolicyStatus): PermissionPolicyStatus;
    static grpcPolicyManagerCapabilityToPolicyManagerCapability(grpcPolicyManagerCapability: GrpcPermissionPolicyStatusManagerCapability): PermissionPolicyManagerCapability;
    static grpcAddressVoucherToAddressVoucher(grpcAddressVoucher: GrpcPermissionAddressVoucher): PermissionAddressVoucher;
    static grpcNamespaceToNamespace(grpcNamespace: GrpcPermissionNamespace): PermissionNamespace;
    static moduleParamsResponseToModuleParams(response: InjectivePermissionsV1Beta1Query.QueryParamsResponse): PermissionsModuleParams;
    static nameSpaceDenomsResponseToNameSpaceDenoms(response: InjectivePermissionsV1Beta1Query.QueryNamespaceDenomsResponse): string[];
    static namespaceResponseToNamespaces(response: InjectivePermissionsV1Beta1Query.QueryNamespaceResponse): PermissionNamespace | undefined;
    static namespacesResponseToNamespaces(response: InjectivePermissionsV1Beta1Query.QueryNamespacesResponse): void[];
    static actorsByRoleResponseToActorsByRole(response: InjectivePermissionsV1Beta1Query.QueryActorsByRoleResponse): {
        roles: string;
    }[];
    static rolesByActorResponseToRolesByActor(response: InjectivePermissionsV1Beta1Query.QueryRolesByActorResponse): {
        roles: string;
    }[];
    static roleManagerResponseToRoleManager(response: InjectivePermissionsV1Beta1Query.QueryRoleManagerResponse): PermissionRoleManager | undefined;
    static roleManagersResponseToRoleManagers(response: InjectivePermissionsV1Beta1Query.QueryRoleManagersResponse): PermissionRoleManager[];
    static policyStatusesResponseToPolicyStatuses(response: InjectivePermissionsV1Beta1Query.QueryPolicyStatusesResponse): PermissionPolicyStatus[];
    static policyManagerCapabilitiesResponseToPolicyManagerCapabilities(response: InjectivePermissionsV1Beta1Query.QueryPolicyManagerCapabilitiesResponse): PermissionPolicyManagerCapability[];
    static voucherResponseToVoucher(response: InjectivePermissionsV1Beta1Query.QueryVoucherResponse): import("@injectivelabs/ts-types").Coin | undefined;
    static vouchersResponseToVouchers(response: InjectivePermissionsV1Beta1Query.QueryVouchersResponse): PermissionAddressVoucher[];
    static moduleStateResponseToModuleState(response: InjectivePermissionsV1Beta1Query.QueryModuleStateResponse): {
        params: PermissionsModuleParams;
        namespaces: PermissionNamespace[];
        vouchers: PermissionAddressVoucher[];
    } | undefined;
}
