import { InjectiveCampaignRpc } from '@injectivelabs/indexer-proto-ts';
import { Guild, Campaign, CampaignV2, GuildMember, CampaignUser, GuildCampaignSummary } from '../types/campaign.js';
export declare class IndexerCampaignTransformer {
    static GrpcCampaignUserToCampaignUser(campaignUser: InjectiveCampaignRpc.CampaignUser): CampaignUser;
    static GrpcCampaignToCampaign(campaign: InjectiveCampaignRpc.Campaign): Campaign;
    static GrpcGuildToGuild(guild: InjectiveCampaignRpc.Guild): Guild;
    static GrpcGuildMemberToGuildMember(member: InjectiveCampaignRpc.GuildMember): GuildMember;
    static GrpcGuildCampaignSummaryToGuildCampaignSummary(campaignSummary: InjectiveCampaignRpc.CampaignSummary): GuildCampaignSummary;
    static CampaignResponseToCampaign(response: InjectiveCampaignRpc.RankingResponse): {
        campaign: Campaign | undefined;
        users: CampaignUser[];
        paging: import("../../../index.js").ExchangePagination;
    };
    static RoundsResponseToRounds(response: InjectiveCampaignRpc.CampaignsResponse): {
        campaigns: Campaign[];
        accumulatedRewards: InjectiveCampaignRpc.Coin[];
        rewardCount: number;
    };
    static GuildsResponseToGuilds(response: InjectiveCampaignRpc.ListGuildsResponse): {
        guilds: Guild[];
        paging: import("../../../index.js").ExchangePagination;
        updatedAt: number;
        summary: GuildCampaignSummary | undefined;
    };
    static GuildMemberResponseToGuildMember(response: InjectiveCampaignRpc.GetGuildMemberResponse): {
        info: GuildMember | undefined;
    };
    static GuildMembersResponseToGuildMembers(response: InjectiveCampaignRpc.ListGuildMembersResponse): {
        members: GuildMember[];
        paging: import("../../../index.js").ExchangePagination;
        guildInfo: Guild | undefined;
    };
    static GrpcCampaignV2ToCampaignV2(campaign: InjectiveCampaignRpc.CampaignV2): CampaignV2;
    static CampaignsV2ResponseToCampaigns(response: InjectiveCampaignRpc.CampaignsV2Response): {
        campaigns: CampaignV2[];
        cursor: string;
    };
}
