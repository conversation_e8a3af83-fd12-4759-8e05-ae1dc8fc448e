var i=class{constructor(){this.queue=[];this.pendingDequeue=[];this.cancelled=!1}enqueue(e){if(this.cancelled=!1,this.pendingDequeue.length>0){this.pendingDequeue.shift()?.resolve(e);return}this.queue.push(e)}async dequeue(){return this.queue.length>0?Promise.resolve(this.queue.shift()):new Promise((e,u)=>{this.pendingDequeue.push({resolve:e,reject:u})})}isEmpty(){return this.queue.length===0}cancel(){this.cancelled=!0,this.pendingDequeue.forEach(async({reject:e})=>{e(new n("Task cancelled"))}),this.pendingDequeue=[],this.queue.length=0}isCancelled(){return this.cancelled}pendingDequeueLength(){return this.pendingDequeue.length}},n=class extends Error{};export{i as a,n as b};
//# sourceMappingURL=chunk-A5L76YP7.mjs.map