{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,2BAA6B;AAC7B,6BAA+B;AAC/B,yBAA2B;AAC3B,oDAA+C;AAE/C,SAAgB,iBAAiB,CAC/B,YAAmC;IAEnC,OAAO,UAAC,IAA2B;QACjC,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;AACJ,CAAC;AAND,8CAMC;AAED;IAIE,kBACE,gBAAuC,EAC9B,YAAmC;QAAnC,iBAAY,GAAZ,YAAY,CAAuB;QAE5C,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC;IAClC,CAAC;IAED,8BAAW,GAAX,UAAY,QAAoB;QAC9B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa;YAC5C,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAC7C;YAEA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;SAC/D;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,6BAAU,GAAV,cAAc,CAAC;IAEf,mCAAgB,GAAhB,UAAiB,QAA8B;QAA/C,iBAcC;QAbC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC5E,IAAM,OAAO,GAAG,yBAAyB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,eAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,UAAW,CAAC,CAAC;QAEzE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;YACxB,KAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC1D,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAe,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE;YACjB,KAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAClD,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wBAAK,GAAL,UAAM,QAAuB;QAA7B,iBA6BC;QA5BC,IAAM,OAAO,GAA8B,EAAE,CAAC;QAC9C,QAAQ,CAAC,OAAO,CAAC,UAAC,GAAG,EAAE,MAAM;YAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAE9C,IAAM,WAAW,GAAG;YAClB,IAAI,EAAE,SAAS,CAAC,QAAQ;YACxB,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC3D,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,MAAM;SACf,CAAC;QACF,IAAI,SAAS,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACnC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,uBACrB,WAAW,GAAK,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,YAAY,GACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;SACH;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CACzB,WAAW,EACX,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;SACH;QACD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,GAAG;YAC3B,KAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YACzD,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,yBAAM,GAAN;QACE,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IACH,eAAC;AAAD,CAAC,AA5ED,IA4EC;AAED,SAAS,yBAAyB,CAAC,OAElC;IACC,IAAM,eAAe,GAAyC,EAAE,CAAC;IAEjE,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE;QACvB,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC/B,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,eAAe,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC9B;SACF;KACF;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,aAAa,CAAC,GAAW;IAChC,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KAClB;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,QAAQ,CAAC,EAAc;IAC9B,IAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;KAChB;IACD,OAAO,GAAG,CAAC;AACb,CAAC"}