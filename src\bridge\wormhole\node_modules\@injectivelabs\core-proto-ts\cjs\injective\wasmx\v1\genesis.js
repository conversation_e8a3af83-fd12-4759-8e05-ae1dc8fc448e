"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.RegisteredContractWithAddress = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var wasmx_1 = require("./wasmx.js");
exports.protobufPackage = "injective.wasmx.v1";
function createBaseRegisteredContractWithAddress() {
    return { address: "", registeredContract: undefined };
}
exports.RegisteredContractWithAddress = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.registeredContract !== undefined) {
            wasmx_1.RegisteredContract.encode(message.registeredContract, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRegisteredContractWithAddress();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.registeredContract = wasmx_1.RegisteredContract.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            registeredContract: isSet(object.registeredContract)
                ? wasmx_1.RegisteredContract.fromJSON(object.registeredContract)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.registeredContract !== undefined && (obj.registeredContract = message.registeredContract
            ? wasmx_1.RegisteredContract.toJSON(message.registeredContract)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.RegisteredContractWithAddress.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseRegisteredContractWithAddress();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.registeredContract = (object.registeredContract !== undefined && object.registeredContract !== null)
            ? wasmx_1.RegisteredContract.fromPartial(object.registeredContract)
            : undefined;
        return message;
    },
};
function createBaseGenesisState() {
    return { params: undefined, registeredContracts: [] };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            wasmx_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.registeredContracts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.RegisteredContractWithAddress.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = wasmx_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.registeredContracts.push(exports.RegisteredContractWithAddress.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? wasmx_1.Params.fromJSON(object.params) : undefined,
            registeredContracts: Array.isArray(object === null || object === void 0 ? void 0 : object.registeredContracts)
                ? object.registeredContracts.map(function (e) { return exports.RegisteredContractWithAddress.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? wasmx_1.Params.toJSON(message.params) : undefined);
        if (message.registeredContracts) {
            obj.registeredContracts = message.registeredContracts.map(function (e) {
                return e ? exports.RegisteredContractWithAddress.toJSON(e) : undefined;
            });
        }
        else {
            obj.registeredContracts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? wasmx_1.Params.fromPartial(object.params)
            : undefined;
        message.registeredContracts =
            ((_a = object.registeredContracts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.RegisteredContractWithAddress.fromPartial(e); })) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
