"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERC20ToDenom = exports.LastClaimEvent = exports.LastObservedEthereumBlockHeight = exports.Valset = exports.BridgeValidator = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "injective.peggy.v1";
function createBaseBridgeValidator() {
    return { power: "0", ethereumAddress: "" };
}
exports.BridgeValidator = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.power !== "0") {
            writer.uint32(8).uint64(message.power);
        }
        if (message.ethereumAddress !== "") {
            writer.uint32(18).string(message.ethereumAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBridgeValidator();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.power = longToString(reader.uint64());
                    break;
                case 2:
                    message.ethereumAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            power: isSet(object.power) ? String(object.power) : "0",
            ethereumAddress: isSet(object.ethereumAddress) ? String(object.ethereumAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.power !== undefined && (obj.power = message.power);
        message.ethereumAddress !== undefined && (obj.ethereumAddress = message.ethereumAddress);
        return obj;
    },
    create: function (base) {
        return exports.BridgeValidator.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBridgeValidator();
        message.power = (_a = object.power) !== null && _a !== void 0 ? _a : "0";
        message.ethereumAddress = (_b = object.ethereumAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseValset() {
    return { nonce: "0", members: [], height: "0", rewardAmount: "", rewardToken: "" };
}
exports.Valset = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        try {
            for (var _b = __values(message.members), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.BridgeValidator.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.height !== "0") {
            writer.uint32(24).uint64(message.height);
        }
        if (message.rewardAmount !== "") {
            writer.uint32(34).string(message.rewardAmount);
        }
        if (message.rewardToken !== "") {
            writer.uint32(42).string(message.rewardToken);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseValset();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.members.push(exports.BridgeValidator.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.height = longToString(reader.uint64());
                    break;
                case 4:
                    message.rewardAmount = reader.string();
                    break;
                case 5:
                    message.rewardToken = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            members: Array.isArray(object === null || object === void 0 ? void 0 : object.members) ? object.members.map(function (e) { return exports.BridgeValidator.fromJSON(e); }) : [],
            height: isSet(object.height) ? String(object.height) : "0",
            rewardAmount: isSet(object.rewardAmount) ? String(object.rewardAmount) : "",
            rewardToken: isSet(object.rewardToken) ? String(object.rewardToken) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        if (message.members) {
            obj.members = message.members.map(function (e) { return e ? exports.BridgeValidator.toJSON(e) : undefined; });
        }
        else {
            obj.members = [];
        }
        message.height !== undefined && (obj.height = message.height);
        message.rewardAmount !== undefined && (obj.rewardAmount = message.rewardAmount);
        message.rewardToken !== undefined && (obj.rewardToken = message.rewardToken);
        return obj;
    },
    create: function (base) {
        return exports.Valset.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseValset();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.members = ((_b = object.members) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.BridgeValidator.fromPartial(e); })) || [];
        message.height = (_c = object.height) !== null && _c !== void 0 ? _c : "0";
        message.rewardAmount = (_d = object.rewardAmount) !== null && _d !== void 0 ? _d : "";
        message.rewardToken = (_e = object.rewardToken) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseLastObservedEthereumBlockHeight() {
    return { cosmosBlockHeight: "0", ethereumBlockHeight: "0" };
}
exports.LastObservedEthereumBlockHeight = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.cosmosBlockHeight !== "0") {
            writer.uint32(8).uint64(message.cosmosBlockHeight);
        }
        if (message.ethereumBlockHeight !== "0") {
            writer.uint32(16).uint64(message.ethereumBlockHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLastObservedEthereumBlockHeight();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.cosmosBlockHeight = longToString(reader.uint64());
                    break;
                case 2:
                    message.ethereumBlockHeight = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            cosmosBlockHeight: isSet(object.cosmosBlockHeight) ? String(object.cosmosBlockHeight) : "0",
            ethereumBlockHeight: isSet(object.ethereumBlockHeight) ? String(object.ethereumBlockHeight) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.cosmosBlockHeight !== undefined && (obj.cosmosBlockHeight = message.cosmosBlockHeight);
        message.ethereumBlockHeight !== undefined && (obj.ethereumBlockHeight = message.ethereumBlockHeight);
        return obj;
    },
    create: function (base) {
        return exports.LastObservedEthereumBlockHeight.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseLastObservedEthereumBlockHeight();
        message.cosmosBlockHeight = (_a = object.cosmosBlockHeight) !== null && _a !== void 0 ? _a : "0";
        message.ethereumBlockHeight = (_b = object.ethereumBlockHeight) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseLastClaimEvent() {
    return { ethereumEventNonce: "0", ethereumEventHeight: "0" };
}
exports.LastClaimEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ethereumEventNonce !== "0") {
            writer.uint32(8).uint64(message.ethereumEventNonce);
        }
        if (message.ethereumEventHeight !== "0") {
            writer.uint32(16).uint64(message.ethereumEventHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLastClaimEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ethereumEventNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.ethereumEventHeight = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ethereumEventNonce: isSet(object.ethereumEventNonce) ? String(object.ethereumEventNonce) : "0",
            ethereumEventHeight: isSet(object.ethereumEventHeight) ? String(object.ethereumEventHeight) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ethereumEventNonce !== undefined && (obj.ethereumEventNonce = message.ethereumEventNonce);
        message.ethereumEventHeight !== undefined && (obj.ethereumEventHeight = message.ethereumEventHeight);
        return obj;
    },
    create: function (base) {
        return exports.LastClaimEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseLastClaimEvent();
        message.ethereumEventNonce = (_a = object.ethereumEventNonce) !== null && _a !== void 0 ? _a : "0";
        message.ethereumEventHeight = (_b = object.ethereumEventHeight) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseERC20ToDenom() {
    return { erc20: "", denom: "" };
}
exports.ERC20ToDenom = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.erc20 !== "") {
            writer.uint32(10).string(message.erc20);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseERC20ToDenom();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.erc20 = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            erc20: isSet(object.erc20) ? String(object.erc20) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.erc20 !== undefined && (obj.erc20 = message.erc20);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.ERC20ToDenom.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseERC20ToDenom();
        message.erc20 = (_a = object.erc20) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
