import { GetAccountOwnedTokensQueryResponse, GetAccountOwnedTokensFromCollectionResponse, GetAccountCollectionsWithOwnedTokenResponse, GetAccountCoinsDataResponse, GetObjectDataQueryResponse } from '../types/indexer.mjs';
import { AccountData, LedgerVersionArg, MoveModuleBytecode, CursorPaginationArgs, PaginationArgs, TransactionResponse, MoveResource, MoveStructId, AnyNumber, TokenStandardArg, OrderByArg, WhereArg } from '../types/types.mjs';
import { AccountAddressInput, AccountAddress } from '../core/accountAddress.mjs';
import { AptosConfig } from './aptosConfig.mjs';
import { PrivateKey } from '../core/crypto/privateKey.mjs';
import { CurrentFungibleAssetBalancesBoolExp } from '../types/generated/types.mjs';
import { A as Account$1 } from '../Ed25519Account-D9XrCLfE.mjs';
import { AccountAbstraction } from './account/abstraction.mjs';
import '../types/generated/operations.mjs';
import '../utils/apiEndpoints.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../utils/const.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

/**
 * A class to query all `Account` related queries on Aptos.
 * @group Account
 */
declare class Account {
    readonly config: AptosConfig;
    abstraction: AccountAbstraction;
    /**
     * Creates an instance of the Aptos client with the provided configuration.
     *
     * @param config - The configuration settings for the Aptos client.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * async function runExample() {
     *     // Initialize the Aptos client with testnet configuration
     *     const config = new AptosConfig({ network: Network.TESTNET }); // specify your own network if needed
     *     const aptos = new Aptos(config);
     *
     *     console.log("Aptos client initialized:", aptos);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    constructor(config: AptosConfig);
    /**
     * Queries the current state for an Aptos account given its account address.
     *
     * @param args - The arguments for retrieving account information.
     * @param args.accountAddress - The Aptos account address to query.
     * @returns The account data.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *     // Retrieve account information for a specific address
     *     const accountInfo = await aptos.getAccountInfo({ accountAddress: "0x1" }); // replace with a real account address
     *     console.log(accountInfo);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountInfo(args: {
        accountAddress: AccountAddressInput;
    }): Promise<AccountData>;
    /**
     * Queries for all modules in an account given an account address.
     * This function may call the API multiple times to auto paginate through results.
     *
     * @param args.accountAddress - The Aptos account address to query modules for.
     * @param args.options.limit - The maximum number of results to return.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it retrieves the latest version.
     *
     * @returns - The account modules associated with the specified address.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetching account modules for a specific account
     *   const accountModules = await aptos.getAccountModules({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       limit: 10, // limiting to 10 modules
     *     },
     *   });
     *
     *   console.log(accountModules);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountModules(args: {
        accountAddress: AccountAddressInput;
        options?: {
            limit?: number;
        } & LedgerVersionArg;
    }): Promise<MoveModuleBytecode[]>;
    /**
     * Queries for a page of modules in an account given an account address.
     *
     * @param args.accountAddress - The Aptos account address to query modules for.
     * @param args.options.cursor - The cursor to start returning results from.  Note, this is obfuscated and is not an index.
     * @param args.options.limit - The maximum number of results to return.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it retrieves the latest version.
     *
     * @returns - The account modules associated with the specified address. Along with a cursor for future pagination. If the cursor is undefined, it means there are no more modules to fetch.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetching account modules for a specific account
     *   const {modules, cursor} = await aptos.getAccountModulesPage({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       cursor: undefined, // starting from the first module
     *       limit: 10, // limiting to 10 modules
     *     },
     *   });
     *
     *   console.log(modules);
     *   console.log(`More to fetch: ${cursor !== undefined}`);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountModulesPage(args: {
        accountAddress: AccountAddressInput;
        options?: CursorPaginationArgs & LedgerVersionArg;
    }): Promise<{
        modules: MoveModuleBytecode[];
        cursor: string | undefined;
    }>;
    /**
     * Queries for a specific account module given an account address and module name.
     *
     * @param args.accountAddress - The Aptos account address.
     * @param args.moduleName - The name of the module.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it will get the latest version.
     *
     * @returns The account module associated with the specified account address and module name.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the account module for a specific account address and module name
     *   const module = await aptos.getAccountModule({
     *     accountAddress: "0x1", // replace with a real account address
     *     moduleName: "MyModule" // specify the module name
     *   });
     *
     *   console.log(module);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountModule(args: {
        accountAddress: AccountAddressInput;
        moduleName: string;
        options?: LedgerVersionArg;
    }): Promise<MoveModuleBytecode>;
    /**
     * Queries account transactions given an account address.
     * This function may call the API multiple times to auto paginate and retrieve all account transactions.
     *
     * @param args.accountAddress - The Aptos account address to query transactions for.
     * @param args.options - Optional pagination arguments.
     * @param args.options.offset - The number of transactions to start returning results from.
     * @param args.options.limit - The maximum number of results to return.
     *
     * @returns The account transactions.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetch transactions for a specific account
     *   const transactions = await aptos.getAccountTransactions({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       offset: 0, // starting from the first transaction
     *       limit: 10, // limiting to 10 transactions
     *     },
     *   });
     *
     *   console.log(transactions);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountTransactions(args: {
        accountAddress: AccountAddressInput;
        options?: PaginationArgs;
    }): Promise<TransactionResponse[]>;
    /**
     * Queries all account resources given an account address.
     * This function may call the API multiple times to auto paginate through results.
     *
     * @param args.accountAddress - The Aptos account address to query resources for.
     * @param args.options.limit - The maximum number of results to return.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it will get the latest version.
     * @returns Account resources.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetching account resources for a specific account address
     *   const resources = await aptos.getAccountResources({ accountAddress: "0x1" }); // replace with a real account address
     *   console.log(resources);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountResources(args: {
        accountAddress: AccountAddressInput;
        options?: PaginationArgs & LedgerVersionArg;
    }): Promise<MoveResource[]>;
    /**
     * Queries a page of account resources given an account address.
     *
     * @param args.accountAddress - The Aptos account address to query resources for.
     * @param args.options.cursor - The cursor to start returning results from.  Note, this is obfuscated and is not an index.
     * @param args.options.limit - The maximum number of results to return.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it will get the latest version.
     * @returns Account resources.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetching account resources for a specific account address
     *   const resources = await aptos.getAccountResourcesPage({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       cursor: undefined, // starting from the first resource
     *       limit: 10, // limiting to 10 resources
     *     },
     *   });
     *   console.log(resources);
     *   console.log(`More to fetch: ${resources.cursor !== undefined}`);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountResourcesPage(args: {
        accountAddress: AccountAddressInput;
        options?: CursorPaginationArgs & LedgerVersionArg;
    }): Promise<{
        resources: MoveResource[];
        cursor: string | undefined;
    }>;
    /**
     * Queries a specific account resource given an account address and resource type.
     *
     * @template T - The typed output of the resource.
     * @param args.accountAddress - The Aptos account address to query.
     * @param args.resourceType - The string representation of an on-chain Move struct type, e.g., "0x1::aptos_coin::AptosCoin".
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it will get the latest version.
     * @returns The account resource of the specified type.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the account resource for a specific account address and resource type
     *   const resource = await aptos.getAccountResource({
     *     accountAddress: "0x1", // replace with a real account address
     *     resourceType: "0x1::aptos_coin::AptosCoin"
     *   });
     *
     *   console.log(resource);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountResource<T extends {} = any>(args: {
        accountAddress: AccountAddressInput;
        resourceType: MoveStructId;
        options?: LedgerVersionArg;
    }): Promise<T>;
    /**
     * Looks up the account address for a given authentication key, handling both rotated and non-rotated keys.
     *
     * @param args.authenticationKey - The authentication key for which to look up the account address.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to before querying.
     * @param args.options.ledgerVersion - The ledger version to query; if not provided, it will get the latest version.
     * @returns Promise<AccountAddress> - The account address associated with the authentication key.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Look up the original account address for a given authentication key
     *   const accountAddress = await aptos.lookupOriginalAccountAddress({
     *     authenticationKey: "0x1", // replace with a real authentication key
     *   });
     *
     *   console.log("Original Account Address:", accountAddress);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    lookupOriginalAccountAddress(args: {
        authenticationKey: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: LedgerVersionArg;
    }): Promise<AccountAddress>;
    /**
     * Queries the current count of tokens owned by a specified account.
     *
     * @param args - The parameters for the query.
     * @param args.accountAddress - The account address to query the token count for.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to before querying.
     * @returns The current count of tokens owned by the account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the count of tokens owned by the account
     *   const tokensCount = await aptos.getAccountTokensCount({ accountAddress: "0x1" }); // replace with a real account address
     *   console.log(`Tokens Count: ${tokensCount}`);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountTokensCount(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
    }): Promise<number>;
    /**
     * Queries the tokens currently owned by a specified account, including NFTs and fungible tokens.
     * If desired, you can filter the results by a specific token standard.
     *
     * @param args.accountAddress The account address for which to retrieve owned tokens.
     * @param args.minimumLedgerVersion Optional ledger version to sync up to before querying.
     * @param args.options.tokenStandard Optional filter for the NFT standard to query for.
     * @param args.options.offset Optional number to start returning results from.
     * @param args.options.limit Optional number of results to return.
     * @param args.options.orderBy Optional order to sort the tokens by.
     * @returns An array of tokens with their respective data.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the tokens owned by a specific account
     *   const accountOwnedTokens = await aptos.getAccountOwnedTokens({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       limit: 10, // specify how many tokens to return
     *       orderBy: "created_at", // specify the order of the results
     *     },
     *   });
     *
     *   console.log(accountOwnedTokens);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountOwnedTokens(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensQueryResponse[0]>;
    }): Promise<GetAccountOwnedTokensQueryResponse>;
    /**
     * Queries all current tokens of a specific collection that an account owns by the collection address.
     * This query returns all tokens (v1 and v2 standards) an account owns, including NFTs, fungible, soulbound, etc.
     * If you want to get only the token from a specific standard, you can pass an optional tokenStandard parameter.
     *
     * @param args.accountAddress - The account address we want to get the tokens for.
     * @param args.collectionAddress - The address of the collection being queried.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to, before querying.
     * @param args.options.tokenStandard - The NFT standard to query for.
     * @param args.options.offset - The number token to start returning results from.
     * @param args.options.limit - The number of results to return.
     * @param args.options.orderBy - The order to sort the tokens by.
     * @returns Tokens array with the token data.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get tokens owned by a specific account in a specific collection
     *   const accountOwnedTokens = await aptos.getAccountOwnedTokensFromCollectionAddress({
     *     accountAddress: "0x1", // replace with a real account address
     *     collectionAddress: "0x2", // replace with a real collection address
     *   });
     *
     *   console.log(accountOwnedTokens);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountOwnedTokensFromCollectionAddress(args: {
        accountAddress: AccountAddressInput;
        collectionAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensFromCollectionResponse[0]>;
    }): Promise<GetAccountOwnedTokensFromCollectionResponse>;
    /**
     * Queries for all collections that an account currently has tokens for, including NFTs, fungible tokens, and soulbound tokens.
     * If you want to filter by a specific token standard, you can pass an optional tokenStandard parameter.
     *
     * @param args.accountAddress - The account address we want to get the collections for.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to before querying.
     * @param args.options.tokenStandard - The NFT standard to query for.
     * @param args.options.offset - The number of the collection to start returning results from.
     * @param args.options.limit - The number of results to return.
     * @param args.options.orderBy - The order to sort the tokens by.
     * @returns Collections array with the collections data.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get account collections with owned tokens for a specific account
     *   const accountCollectionsWithOwnedTokens = await aptos.getAccountCollectionsWithOwnedTokens({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       tokenStandard: "NFT", // specify the token standard if needed
     *       limit: 10, // specify the number of results to return
     *     },
     *   });
     *
     *   console.log(accountCollectionsWithOwnedTokens);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountCollectionsWithOwnedTokens(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountCollectionsWithOwnedTokenResponse[0]>;
    }): Promise<GetAccountCollectionsWithOwnedTokenResponse>;
    /**
     * Queries the current count of transactions submitted by an account.
     *
     * @param args - The parameters for the query.
     * @param args.accountAddress - The account address we want to get the total count for.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to before querying.
     * @returns Current count of transactions made by an account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the count of transactions for a specific account
     *   const accountTransactionsCount = await aptos.getAccountTransactionsCount({
     *     accountAddress: "0x1", // replace with a real account address
     *     minimumLedgerVersion: 1, // specify your own minimum ledger version if needed
     *   });
     *
     *   console.log(accountTransactionsCount);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountTransactionsCount(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
    }): Promise<number>;
    /**
     * Retrieves the coins data for a specified account.
     *
     * @param args.accountAddress - The account address for which to retrieve the coin's data.
     * @param args.minimumLedgerVersion - Optional ledger version to sync up to before querying.
     * @param args.options.offset - Optional. The number of coins to start returning results from.
     * @param args.options.limit - Optional. The number of results to return.
     * @param args.options.orderBy - Optional. The order to sort the coins by.
     * @param args.options.where - Optional. Filter the results by specific criteria.
     * @returns An array containing the coins data for the specified account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Fetching coins data for a specific account
     *   const accountCoinsData = await aptos.getAccountCoinsData({
     *     accountAddress: "0x1", // replace with a real account address
     *     options: {
     *       limit: 10, // specify the number of results to return
     *       orderBy: { asset_type: "asc" }, // specify the order of results
     *     },
     *   });
     *
     *   console.log(accountCoinsData);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountCoinsData(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: PaginationArgs & OrderByArg<GetAccountCoinsDataResponse[0]> & WhereArg<CurrentFungibleAssetBalancesBoolExp>;
    }): Promise<GetAccountCoinsDataResponse>;
    /**
     * Retrieves the current count of an account's coins aggregated across all types.
     *
     * @param args The parameters for the account coins count query.
     * @param args.accountAddress The account address we want to get the total count for.
     * @param args.minimumLedgerVersion Optional ledger version to sync up to before querying.
     * @returns The current count of the aggregated coins for the specified account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Getting the account coins count for a specific account
     *   const accountCoinsCount = await aptos.getAccountCoinsCount({ accountAddress: "0x1" }); // replace with a real account address
     *   console.log("Account Coins Count:", accountCoinsCount);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountCoinsCount(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
    }): Promise<number>;
    /**
     * Retrieves the current amount of APT for a specified account. If the account does not exist, it will return 0.
     *
     * @param args The arguments for the account query.
     * @param args.accountAddress The account address for which to retrieve the APT amount.
     * @param args.minimumLedgerVersion Optional ledger version to sync up to before querying.
     * @returns The current amount of APT for the specified account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the APT amount for a specific account
     *   const accountAPTAmount = await aptos.getAccountAPTAmount({ accountAddress: "0x1" }); // replace with a real account address
     *   console.log("Account APT Amount:", accountAPTAmount);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountAPTAmount(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
    }): Promise<number>;
    /**
     * Queries the current amount of a specified coin held by an account.
     *
     * @param args The parameters for querying the account's coin amount.
     * @param args.accountAddress The account address to query for the coin amount.
     * @param args.coinType The coin type to query. Note: If not provided, it may be automatically populated if `faMetadataAddress`
     * is specified.
     * @param args.faMetadataAddress The fungible asset metadata address to query. Note: If not provided, it may be automatically
     * populated if `coinType` is specified.
     * @param args.minimumLedgerVersion Not used anymore, here for backward compatibility
     * see https://github.com/aptos-labs/aptos-ts-sdk/pull/519, will be removed in the near future.
     * Optional ledger version to sync up to before querying.
     * @returns The current amount of the specified coin held by the account.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Query the account's coin amount for a specific coin type
     *   const accountCoinAmount = await aptos.getAccountCoinAmount({
     *     accountAddress: "0x1", // replace with a real account address
     *     coinType: "0x1::aptos_coin::AptosCoin" // specify the coin type
     *   });
     *
     *   console.log(`Account coin amount: ${accountCoinAmount}`);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountCoinAmount(args: {
        accountAddress: AccountAddressInput;
        coinType?: MoveStructId;
        faMetadataAddress?: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
    }): Promise<number>;
    /**
     * Queries an account's owned objects.
     *
     * @param args.accountAddress The account address we want to get the objects for.
     * @param args.minimumLedgerVersion Optional ledger version to sync up to before querying.
     * @param args.options.offset The starting position to start returning results from.
     * @param args.options.limit The number of results to return.
     * @param args.options.orderBy The order to sort the objects by.
     * @returns Objects array with the object data.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   // Get the objects owned by the specified account
     *   const accountOwnedObjects = await aptos.getAccountOwnedObjects({
     *     accountAddress: "0x1", // replace with a real account address
     *     minimumLedgerVersion: 1, // optional, specify if needed
     *     options: {
     *       offset: 0, // optional, specify if needed
     *       limit: 10, // optional, specify if needed
     *       orderBy: "created_at", // optional, specify if needed
     *     },
     *   });
     *
     *   console.log(accountOwnedObjects);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     */
    getAccountOwnedObjects(args: {
        accountAddress: AccountAddressInput;
        minimumLedgerVersion?: AnyNumber;
        options?: PaginationArgs & OrderByArg<GetObjectDataQueryResponse[0]>;
    }): Promise<GetObjectDataQueryResponse>;
    /**
     * Derives an account by providing a private key. This function resolves the provided private key type and derives the public
     * key from it.
     *
     * If the privateKey is a Secp256k1 type, it derives the account using the derived public key and auth key using the SingleKey
     * scheme locally.
     * If the privateKey is an ED25519 type, it looks up the authentication key on chain to determine whether it is a Legacy ED25519
     * key or a Unified ED25519 key, and then derives the account based on that.
     *
     * @param args - The arguments for deriving the account.
     * @param args.privateKey - An account private key.
     * @returns The derived Account type.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network, Ed25519PrivateKey } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *     // Deriving an account from a private key
     *     const account = await aptos.deriveAccountFromPrivateKey({
     *         privateKey: new Ed25519PrivateKey("0x123") // replace with a real private key
     *     });
     *
     *     console.log(account);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Account
     * @deprecated Note that more inspection is needed by the user to determine which account exists on-chain
     */
    deriveAccountFromPrivateKey(args: {
        privateKey: PrivateKey;
    }): Promise<Account$1>;
}

export { Account };
