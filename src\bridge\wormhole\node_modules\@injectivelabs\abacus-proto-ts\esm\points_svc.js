/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import Long from "long";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "points_svc";
function createBasePointsLatestForAccountRequest() {
    return { accountAddress: "" };
}
export const PointsLatestForAccountRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLatestForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create(base) {
        return PointsLatestForAccountRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsLatestForAccountRequest();
        message.accountAddress = object.accountAddress ?? "";
        return message;
    },
};
function createBasePointsLatestForAccountResponse() {
    return { rank: "0", totalPoints: "0", totalPointsPrecise: 0, league: "", updatedAt: "" };
}
export const PointsLatestForAccountResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.rank !== "0") {
            writer.uint32(8).uint64(message.rank);
        }
        if (message.totalPoints !== "0") {
            writer.uint32(16).uint64(message.totalPoints);
        }
        if (message.totalPointsPrecise !== 0) {
            writer.uint32(25).double(message.totalPointsPrecise);
        }
        if (message.league !== "") {
            writer.uint32(34).string(message.league);
        }
        if (message.updatedAt !== "") {
            writer.uint32(42).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLatestForAccountResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rank = longToString(reader.uint64());
                    break;
                case 2:
                    message.totalPoints = longToString(reader.uint64());
                    break;
                case 3:
                    message.totalPointsPrecise = reader.double();
                    break;
                case 4:
                    message.league = reader.string();
                    break;
                case 5:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rank: isSet(object.rank) ? String(object.rank) : "0",
            totalPoints: isSet(object.totalPoints) ? String(object.totalPoints) : "0",
            totalPointsPrecise: isSet(object.totalPointsPrecise) ? Number(object.totalPointsPrecise) : 0,
            league: isSet(object.league) ? String(object.league) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.rank !== undefined && (obj.rank = message.rank);
        message.totalPoints !== undefined && (obj.totalPoints = message.totalPoints);
        message.totalPointsPrecise !== undefined && (obj.totalPointsPrecise = message.totalPointsPrecise);
        message.league !== undefined && (obj.league = message.league);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsLatestForAccountResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsLatestForAccountResponse();
        message.rank = object.rank ?? "0";
        message.totalPoints = object.totalPoints ?? "0";
        message.totalPointsPrecise = object.totalPointsPrecise ?? 0;
        message.league = object.league ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsStatsDailyForAccountRequest() {
    return { accountAddress: "", daysLimit: undefined };
}
export const PointsStatsDailyForAccountRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.daysLimit !== undefined) {
            writer.uint32(16).uint64(message.daysLimit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsStatsDailyForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.daysLimit = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            daysLimit: isSet(object.daysLimit) ? String(object.daysLimit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.daysLimit !== undefined && (obj.daysLimit = message.daysLimit);
        return obj;
    },
    create(base) {
        return PointsStatsDailyForAccountRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsStatsDailyForAccountRequest();
        message.accountAddress = object.accountAddress ?? "";
        message.daysLimit = object.daysLimit ?? undefined;
        return message;
    },
};
function createBaseHistoricalPointsStatsRowCollection() {
    return { rows: [] };
}
export const HistoricalPointsStatsRowCollection = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.rows) {
            HistoricalPointsStatsRow.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalPointsStatsRowCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rows.push(HistoricalPointsStatsRow.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rows: Array.isArray(object?.rows) ? object.rows.map((e) => HistoricalPointsStatsRow.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.rows) {
            obj.rows = message.rows.map((e) => e ? HistoricalPointsStatsRow.toJSON(e) : undefined);
        }
        else {
            obj.rows = [];
        }
        return obj;
    },
    create(base) {
        return HistoricalPointsStatsRowCollection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseHistoricalPointsStatsRowCollection();
        message.rows = object.rows?.map((e) => HistoricalPointsStatsRow.fromPartial(e)) || [];
        return message;
    },
};
function createBaseHistoricalPointsStatsRow() {
    return { week: "", day: undefined, points: "0", pointsPrecise: 0, volume: 0 };
}
export const HistoricalPointsStatsRow = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.week !== "") {
            writer.uint32(10).string(message.week);
        }
        if (message.day !== undefined) {
            writer.uint32(18).string(message.day);
        }
        if (message.points !== "0") {
            writer.uint32(24).uint64(message.points);
        }
        if (message.pointsPrecise !== 0) {
            writer.uint32(33).double(message.pointsPrecise);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalPointsStatsRow();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.week = reader.string();
                    break;
                case 2:
                    message.day = reader.string();
                    break;
                case 3:
                    message.points = longToString(reader.uint64());
                    break;
                case 4:
                    message.pointsPrecise = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            week: isSet(object.week) ? String(object.week) : "",
            day: isSet(object.day) ? String(object.day) : undefined,
            points: isSet(object.points) ? String(object.points) : "0",
            pointsPrecise: isSet(object.pointsPrecise) ? Number(object.pointsPrecise) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.week !== undefined && (obj.week = message.week);
        message.day !== undefined && (obj.day = message.day);
        message.points !== undefined && (obj.points = message.points);
        message.pointsPrecise !== undefined && (obj.pointsPrecise = message.pointsPrecise);
        message.volume !== undefined && (obj.volume = message.volume);
        return obj;
    },
    create(base) {
        return HistoricalPointsStatsRow.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseHistoricalPointsStatsRow();
        message.week = object.week ?? "";
        message.day = object.day ?? undefined;
        message.points = object.points ?? "0";
        message.pointsPrecise = object.pointsPrecise ?? 0;
        message.volume = object.volume ?? 0;
        return message;
    },
};
function createBasePointsStatsWeeklyForAccountRequest() {
    return { accountAddress: "", weeksLimit: undefined };
}
export const PointsStatsWeeklyForAccountRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.weeksLimit !== undefined) {
            writer.uint32(16).uint64(message.weeksLimit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsStatsWeeklyForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.weeksLimit = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            weeksLimit: isSet(object.weeksLimit) ? String(object.weeksLimit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.weeksLimit !== undefined && (obj.weeksLimit = message.weeksLimit);
        return obj;
    },
    create(base) {
        return PointsStatsWeeklyForAccountRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsStatsWeeklyForAccountRequest();
        message.accountAddress = object.accountAddress ?? "";
        message.weeksLimit = object.weeksLimit ?? undefined;
        return message;
    },
};
function createBasePointsLeaderboardRequest() {
    return {};
}
export const PointsLeaderboardRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLeaderboardRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsLeaderboardRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsLeaderboardRequest();
        return message;
    },
};
function createBaseLeaderboardPointsRowCollection() {
    return { rows: [] };
}
export const LeaderboardPointsRowCollection = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.rows) {
            LeaderboardPointsRow.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLeaderboardPointsRowCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rows.push(LeaderboardPointsRow.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { rows: Array.isArray(object?.rows) ? object.rows.map((e) => LeaderboardPointsRow.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.rows) {
            obj.rows = message.rows.map((e) => e ? LeaderboardPointsRow.toJSON(e) : undefined);
        }
        else {
            obj.rows = [];
        }
        return obj;
    },
    create(base) {
        return LeaderboardPointsRowCollection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseLeaderboardPointsRowCollection();
        message.rows = object.rows?.map((e) => LeaderboardPointsRow.fromPartial(e)) || [];
        return message;
    },
};
function createBaseLeaderboardPointsRow() {
    return { rank: "0", totalRank: "0", accountAddress: "", points: 0, league: "" };
}
export const LeaderboardPointsRow = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.rank !== "0") {
            writer.uint32(8).uint64(message.rank);
        }
        if (message.totalRank !== "0") {
            writer.uint32(16).uint64(message.totalRank);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.points !== 0) {
            writer.uint32(33).double(message.points);
        }
        if (message.league !== "") {
            writer.uint32(50).string(message.league);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLeaderboardPointsRow();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rank = longToString(reader.uint64());
                    break;
                case 2:
                    message.totalRank = longToString(reader.uint64());
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.points = reader.double();
                    break;
                case 6:
                    message.league = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rank: isSet(object.rank) ? String(object.rank) : "0",
            totalRank: isSet(object.totalRank) ? String(object.totalRank) : "0",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            points: isSet(object.points) ? Number(object.points) : 0,
            league: isSet(object.league) ? String(object.league) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.rank !== undefined && (obj.rank = message.rank);
        message.totalRank !== undefined && (obj.totalRank = message.totalRank);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.points !== undefined && (obj.points = message.points);
        message.league !== undefined && (obj.league = message.league);
        return obj;
    },
    create(base) {
        return LeaderboardPointsRow.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseLeaderboardPointsRow();
        message.rank = object.rank ?? "0";
        message.totalRank = object.totalRank ?? "0";
        message.accountAddress = object.accountAddress ?? "";
        message.points = object.points ?? 0;
        message.league = object.league ?? "";
        return message;
    },
};
function createBasePointsSimulateAllocationRequest() {
    return {
        account: "",
        marketId: "",
        tradeDirection: "",
        executionSide: "",
        usdValue: 0,
        flags: [],
        marketType: "",
        timestamp: "0",
        priorDailyVolume: undefined,
    };
}
export const PointsSimulateAllocationRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.tradeDirection !== "") {
            writer.uint32(26).string(message.tradeDirection);
        }
        if (message.executionSide !== "") {
            writer.uint32(34).string(message.executionSide);
        }
        if (message.usdValue !== 0) {
            writer.uint32(41).double(message.usdValue);
        }
        for (const v of message.flags) {
            writer.uint32(50).string(v);
        }
        if (message.marketType !== "") {
            writer.uint32(58).string(message.marketType);
        }
        if (message.timestamp !== "0") {
            writer.uint32(64).uint64(message.timestamp);
        }
        if (message.priorDailyVolume !== undefined) {
            writer.uint32(73).double(message.priorDailyVolume);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSimulateAllocationRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.tradeDirection = reader.string();
                    break;
                case 4:
                    message.executionSide = reader.string();
                    break;
                case 5:
                    message.usdValue = reader.double();
                    break;
                case 6:
                    message.flags.push(reader.string());
                    break;
                case 7:
                    message.marketType = reader.string();
                    break;
                case 8:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 9:
                    message.priorDailyVolume = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            tradeDirection: isSet(object.tradeDirection) ? String(object.tradeDirection) : "",
            executionSide: isSet(object.executionSide) ? String(object.executionSide) : "",
            usdValue: isSet(object.usdValue) ? Number(object.usdValue) : 0,
            flags: Array.isArray(object?.flags) ? object.flags.map((e) => String(e)) : [],
            marketType: isSet(object.marketType) ? String(object.marketType) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            priorDailyVolume: isSet(object.priorDailyVolume) ? Number(object.priorDailyVolume) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.tradeDirection !== undefined && (obj.tradeDirection = message.tradeDirection);
        message.executionSide !== undefined && (obj.executionSide = message.executionSide);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        if (message.flags) {
            obj.flags = message.flags.map((e) => e);
        }
        else {
            obj.flags = [];
        }
        message.marketType !== undefined && (obj.marketType = message.marketType);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.priorDailyVolume !== undefined && (obj.priorDailyVolume = message.priorDailyVolume);
        return obj;
    },
    create(base) {
        return PointsSimulateAllocationRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSimulateAllocationRequest();
        message.account = object.account ?? "";
        message.marketId = object.marketId ?? "";
        message.tradeDirection = object.tradeDirection ?? "";
        message.executionSide = object.executionSide ?? "";
        message.usdValue = object.usdValue ?? 0;
        message.flags = object.flags?.map((e) => e) || [];
        message.marketType = object.marketType ?? "";
        message.timestamp = object.timestamp ?? "0";
        message.priorDailyVolume = object.priorDailyVolume ?? undefined;
        return message;
    },
};
function createBasePointsSimulateAllocationResponse() {
    return {
        pointsEmitted: 0,
        pointsBreakdown: "",
        priorDailyVolume: 0,
        effectiveVolumeTiers: [],
        effectiveMultipliers: [],
    };
}
export const PointsSimulateAllocationResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.pointsEmitted !== 0) {
            writer.uint32(9).double(message.pointsEmitted);
        }
        if (message.pointsBreakdown !== "") {
            writer.uint32(18).string(message.pointsBreakdown);
        }
        if (message.priorDailyVolume !== 0) {
            writer.uint32(25).double(message.priorDailyVolume);
        }
        for (const v of message.effectiveVolumeTiers) {
            ModelEffectiveVolumeTier.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (const v of message.effectiveMultipliers) {
            ModelPointsMultiplier.encode(v, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSimulateAllocationResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pointsEmitted = reader.double();
                    break;
                case 2:
                    message.pointsBreakdown = reader.string();
                    break;
                case 3:
                    message.priorDailyVolume = reader.double();
                    break;
                case 4:
                    message.effectiveVolumeTiers.push(ModelEffectiveVolumeTier.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.effectiveMultipliers.push(ModelPointsMultiplier.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            pointsEmitted: isSet(object.pointsEmitted) ? Number(object.pointsEmitted) : 0,
            pointsBreakdown: isSet(object.pointsBreakdown) ? String(object.pointsBreakdown) : "",
            priorDailyVolume: isSet(object.priorDailyVolume) ? Number(object.priorDailyVolume) : 0,
            effectiveVolumeTiers: Array.isArray(object?.effectiveVolumeTiers)
                ? object.effectiveVolumeTiers.map((e) => ModelEffectiveVolumeTier.fromJSON(e))
                : [],
            effectiveMultipliers: Array.isArray(object?.effectiveMultipliers)
                ? object.effectiveMultipliers.map((e) => ModelPointsMultiplier.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.pointsEmitted !== undefined && (obj.pointsEmitted = message.pointsEmitted);
        message.pointsBreakdown !== undefined && (obj.pointsBreakdown = message.pointsBreakdown);
        message.priorDailyVolume !== undefined && (obj.priorDailyVolume = message.priorDailyVolume);
        if (message.effectiveVolumeTiers) {
            obj.effectiveVolumeTiers = message.effectiveVolumeTiers.map((e) => e ? ModelEffectiveVolumeTier.toJSON(e) : undefined);
        }
        else {
            obj.effectiveVolumeTiers = [];
        }
        if (message.effectiveMultipliers) {
            obj.effectiveMultipliers = message.effectiveMultipliers.map((e) => e ? ModelPointsMultiplier.toJSON(e) : undefined);
        }
        else {
            obj.effectiveMultipliers = [];
        }
        return obj;
    },
    create(base) {
        return PointsSimulateAllocationResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSimulateAllocationResponse();
        message.pointsEmitted = object.pointsEmitted ?? 0;
        message.pointsBreakdown = object.pointsBreakdown ?? "";
        message.priorDailyVolume = object.priorDailyVolume ?? 0;
        message.effectiveVolumeTiers = object.effectiveVolumeTiers?.map((e) => ModelEffectiveVolumeTier.fromPartial(e)) ||
            [];
        message.effectiveMultipliers = object.effectiveMultipliers?.map((e) => ModelPointsMultiplier.fromPartial(e)) || [];
        return message;
    },
};
function createBaseModelEffectiveVolumeTier() {
    return { tierNumber: 0, upperThreshold: 0, takerRate: 0, makerRate: 0, effectiveVolume: 0, effectiveRate: 0 };
}
export const ModelEffectiveVolumeTier = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.tierNumber !== 0) {
            writer.uint32(8).uint32(message.tierNumber);
        }
        if (message.upperThreshold !== 0) {
            writer.uint32(17).double(message.upperThreshold);
        }
        if (message.takerRate !== 0) {
            writer.uint32(25).double(message.takerRate);
        }
        if (message.makerRate !== 0) {
            writer.uint32(33).double(message.makerRate);
        }
        if (message.effectiveVolume !== 0) {
            writer.uint32(41).double(message.effectiveVolume);
        }
        if (message.effectiveRate !== 0) {
            writer.uint32(49).double(message.effectiveRate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelEffectiveVolumeTier();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tierNumber = reader.uint32();
                    break;
                case 2:
                    message.upperThreshold = reader.double();
                    break;
                case 3:
                    message.takerRate = reader.double();
                    break;
                case 4:
                    message.makerRate = reader.double();
                    break;
                case 5:
                    message.effectiveVolume = reader.double();
                    break;
                case 6:
                    message.effectiveRate = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            tierNumber: isSet(object.tierNumber) ? Number(object.tierNumber) : 0,
            upperThreshold: isSet(object.upperThreshold) ? Number(object.upperThreshold) : 0,
            takerRate: isSet(object.takerRate) ? Number(object.takerRate) : 0,
            makerRate: isSet(object.makerRate) ? Number(object.makerRate) : 0,
            effectiveVolume: isSet(object.effectiveVolume) ? Number(object.effectiveVolume) : 0,
            effectiveRate: isSet(object.effectiveRate) ? Number(object.effectiveRate) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.tierNumber !== undefined && (obj.tierNumber = Math.round(message.tierNumber));
        message.upperThreshold !== undefined && (obj.upperThreshold = message.upperThreshold);
        message.takerRate !== undefined && (obj.takerRate = message.takerRate);
        message.makerRate !== undefined && (obj.makerRate = message.makerRate);
        message.effectiveVolume !== undefined && (obj.effectiveVolume = message.effectiveVolume);
        message.effectiveRate !== undefined && (obj.effectiveRate = message.effectiveRate);
        return obj;
    },
    create(base) {
        return ModelEffectiveVolumeTier.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModelEffectiveVolumeTier();
        message.tierNumber = object.tierNumber ?? 0;
        message.upperThreshold = object.upperThreshold ?? 0;
        message.takerRate = object.takerRate ?? 0;
        message.makerRate = object.makerRate ?? 0;
        message.effectiveVolume = object.effectiveVolume ?? 0;
        message.effectiveRate = object.effectiveRate ?? 0;
        return message;
    },
};
function createBaseModelPointsMultiplier() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
    };
}
export const ModelPointsMultiplier = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(58).string(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(66).string(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelPointsMultiplier();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = reader.string();
                    break;
                case 8:
                    message.effectiveDateEnd = reader.string();
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object?.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object?.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object?.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object?.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        return obj;
    },
    create(base) {
        return ModelPointsMultiplier.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModelPointsMultiplier();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.multiplier = object.multiplier ?? 0;
        message.affectedUsers = object.affectedUsers?.map((e) => e) || [];
        message.affectedMarkets = object.affectedMarkets?.map((e) => e) || [];
        message.allMarketsExcept = object.allMarketsExcept?.map((e) => e) || [];
        message.effectiveDateStart = object.effectiveDateStart ?? undefined;
        message.effectiveDateEnd = object.effectiveDateEnd ?? undefined;
        message.effectiveFlags = object.effectiveFlags?.map((e) => e) || [];
        return message;
    },
};
function createBasePointsGetLeagueConfigRequest() {
    return {};
}
export const PointsGetLeagueConfigRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetLeagueConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsGetLeagueConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsGetLeagueConfigRequest();
        return message;
    },
};
function createBasePointsGetLeagueConfigResponse() {
    return {
        whiteThreshold: 0,
        orangeThreshold: 0,
        blueThreshold: 0,
        purpleThreshold: 0,
        blackThreshold: 0,
        updatedBy: "",
        updatedAt: "",
    };
}
export const PointsGetLeagueConfigResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        if (message.updatedBy !== "") {
            writer.uint32(50).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(58).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetLeagueConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                case 6:
                    message.updatedBy = reader.string();
                    break;
                case 7:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsGetLeagueConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsGetLeagueConfigResponse();
        message.whiteThreshold = object.whiteThreshold ?? 0;
        message.orangeThreshold = object.orangeThreshold ?? 0;
        message.blueThreshold = object.blueThreshold ?? 0;
        message.purpleThreshold = object.purpleThreshold ?? 0;
        message.blackThreshold = object.blackThreshold ?? 0;
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsSetLeagueConfigRequest() {
    return { whiteThreshold: 0, orangeThreshold: 0, blueThreshold: 0, purpleThreshold: 0, blackThreshold: 0 };
}
export const PointsSetLeagueConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetLeagueConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        return obj;
    },
    create(base) {
        return PointsSetLeagueConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSetLeagueConfigRequest();
        message.whiteThreshold = object.whiteThreshold ?? 0;
        message.orangeThreshold = object.orangeThreshold ?? 0;
        message.blueThreshold = object.blueThreshold ?? 0;
        message.purpleThreshold = object.purpleThreshold ?? 0;
        message.blackThreshold = object.blackThreshold ?? 0;
        return message;
    },
};
function createBasePointsSetLeagueConfigResponse() {
    return {
        whiteThreshold: 0,
        orangeThreshold: 0,
        blueThreshold: 0,
        purpleThreshold: 0,
        blackThreshold: 0,
        updatedBy: "",
        updatedAt: "",
    };
}
export const PointsSetLeagueConfigResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        if (message.updatedBy !== "") {
            writer.uint32(50).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(58).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetLeagueConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                case 6:
                    message.updatedBy = reader.string();
                    break;
                case 7:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsSetLeagueConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSetLeagueConfigResponse();
        message.whiteThreshold = object.whiteThreshold ?? 0;
        message.orangeThreshold = object.orangeThreshold ?? 0;
        message.blueThreshold = object.blueThreshold ?? 0;
        message.purpleThreshold = object.purpleThreshold ?? 0;
        message.blackThreshold = object.blackThreshold ?? 0;
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsGetEmissionConfigRequest() {
    return {};
}
export const PointsGetEmissionConfigRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetEmissionConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsGetEmissionConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsGetEmissionConfigRequest();
        return message;
    },
};
function createBasePointsGetEmissionConfigResponse() {
    return { unitVolume: 0, volumeTiers: [], updatedBy: "", updatedAt: "" };
}
export const PointsGetEmissionConfigResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.updatedBy !== "") {
            writer.uint32(26).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(34).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetEmissionConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.updatedBy = reader.string();
                    break;
                case 4:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object?.volumeTiers)
                ? object.volumeTiers.map((e) => ModelVolumeTierConfig.fromJSON(e))
                : [],
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsGetEmissionConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsGetEmissionConfigResponse();
        message.unitVolume = object.unitVolume ?? 0;
        message.volumeTiers = object.volumeTiers?.map((e) => ModelVolumeTierConfig.fromPartial(e)) || [];
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBaseModelVolumeTierConfig() {
    return { upperThreshold: 0, takerRate: 0, makerRate: 0 };
}
export const ModelVolumeTierConfig = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.upperThreshold !== 0) {
            writer.uint32(9).double(message.upperThreshold);
        }
        if (message.takerRate !== 0) {
            writer.uint32(17).double(message.takerRate);
        }
        if (message.makerRate !== 0) {
            writer.uint32(25).double(message.makerRate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelVolumeTierConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.upperThreshold = reader.double();
                    break;
                case 2:
                    message.takerRate = reader.double();
                    break;
                case 3:
                    message.makerRate = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            upperThreshold: isSet(object.upperThreshold) ? Number(object.upperThreshold) : 0,
            takerRate: isSet(object.takerRate) ? Number(object.takerRate) : 0,
            makerRate: isSet(object.makerRate) ? Number(object.makerRate) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.upperThreshold !== undefined && (obj.upperThreshold = message.upperThreshold);
        message.takerRate !== undefined && (obj.takerRate = message.takerRate);
        message.makerRate !== undefined && (obj.makerRate = message.makerRate);
        return obj;
    },
    create(base) {
        return ModelVolumeTierConfig.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModelVolumeTierConfig();
        message.upperThreshold = object.upperThreshold ?? 0;
        message.takerRate = object.takerRate ?? 0;
        message.makerRate = object.makerRate ?? 0;
        return message;
    },
};
function createBasePointsSetEmissionConfigRequest() {
    return { unitVolume: 0, volumeTiers: [] };
}
export const PointsSetEmissionConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetEmissionConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object?.volumeTiers)
                ? object.volumeTiers.map((e) => ModelVolumeTierConfig.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        return obj;
    },
    create(base) {
        return PointsSetEmissionConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSetEmissionConfigRequest();
        message.unitVolume = object.unitVolume ?? 0;
        message.volumeTiers = object.volumeTiers?.map((e) => ModelVolumeTierConfig.fromPartial(e)) || [];
        return message;
    },
};
function createBasePointsSetEmissionConfigResponse() {
    return { unitVolume: 0, volumeTiers: [], updatedBy: "", updatedAt: "" };
}
export const PointsSetEmissionConfigResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.updatedBy !== "") {
            writer.uint32(26).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(34).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetEmissionConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.updatedBy = reader.string();
                    break;
                case 4:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object?.volumeTiers)
                ? object.volumeTiers.map((e) => ModelVolumeTierConfig.fromJSON(e))
                : [],
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsSetEmissionConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsSetEmissionConfigResponse();
        message.unitVolume = object.unitVolume ?? 0;
        message.volumeTiers = object.volumeTiers?.map((e) => ModelVolumeTierConfig.fromPartial(e)) || [];
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsListBanConfigsRequest() {
    return {};
}
export const PointsListBanConfigsRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListBanConfigsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsListBanConfigsRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsListBanConfigsRequest();
        return message;
    },
};
function createBaseBanConfigCollection() {
    return { bans: [] };
}
export const BanConfigCollection = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.bans) {
            BanConfig.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBanConfigCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bans.push(BanConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { bans: Array.isArray(object?.bans) ? object.bans.map((e) => BanConfig.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.bans) {
            obj.bans = message.bans.map((e) => e ? BanConfig.toJSON(e) : undefined);
        }
        else {
            obj.bans = [];
        }
        return obj;
    },
    create(base) {
        return BanConfigCollection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseBanConfigCollection();
        message.bans = object.bans?.map((e) => BanConfig.fromPartial(e)) || [];
        return message;
    },
};
function createBaseBanConfig() {
    return { id: "", label: "", accountAddress: "", ethAddress: "", createdBy: "", createdAt: "" };
}
export const BanConfig = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.ethAddress !== "") {
            writer.uint32(34).string(message.ethAddress);
        }
        if (message.createdBy !== "") {
            writer.uint32(42).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(50).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBanConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.ethAddress = reader.string();
                    break;
                case 5:
                    message.createdBy = reader.string();
                    break;
                case 6:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return BanConfig.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseBanConfig();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.accountAddress = object.accountAddress ?? "";
        message.ethAddress = object.ethAddress ?? "";
        message.createdBy = object.createdBy ?? "";
        message.createdAt = object.createdAt ?? "";
        return message;
    },
};
function createBasePointsCreateBanConfigRequest() {
    return { accountAddresses: [], ethAddresses: [], label: "", effectiveFrom: "0" };
}
export const PointsCreateBanConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.accountAddresses) {
            writer.uint32(10).string(v);
        }
        for (const v of message.ethAddresses) {
            writer.uint32(18).string(v);
        }
        if (message.label !== "") {
            writer.uint32(26).string(message.label);
        }
        if (message.effectiveFrom !== "0") {
            writer.uint32(32).uint64(message.effectiveFrom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateBanConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddresses.push(reader.string());
                    break;
                case 2:
                    message.ethAddresses.push(reader.string());
                    break;
                case 3:
                    message.label = reader.string();
                    break;
                case 4:
                    message.effectiveFrom = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddresses: Array.isArray(object?.accountAddresses)
                ? object.accountAddresses.map((e) => String(e))
                : [],
            ethAddresses: Array.isArray(object?.ethAddresses) ? object.ethAddresses.map((e) => String(e)) : [],
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.accountAddresses) {
            obj.accountAddresses = message.accountAddresses.map((e) => e);
        }
        else {
            obj.accountAddresses = [];
        }
        if (message.ethAddresses) {
            obj.ethAddresses = message.ethAddresses.map((e) => e);
        }
        else {
            obj.ethAddresses = [];
        }
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        return obj;
    },
    create(base) {
        return PointsCreateBanConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsCreateBanConfigRequest();
        message.accountAddresses = object.accountAddresses?.map((e) => e) || [];
        message.ethAddresses = object.ethAddresses?.map((e) => e) || [];
        message.label = object.label ?? "";
        message.effectiveFrom = object.effectiveFrom ?? "0";
        return message;
    },
};
function createBasePointsDeleteBanConfigRequest() {
    return { id: "" };
}
export const PointsDeleteBanConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteBanConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return PointsDeleteBanConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsDeleteBanConfigRequest();
        message.id = object.id ?? "";
        return message;
    },
};
function createBasePointsDeleteBanConfigResponse() {
    return {};
}
export const PointsDeleteBanConfigResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteBanConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsDeleteBanConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteBanConfigResponse();
        return message;
    },
};
function createBasePointsListMultiplierConfigsRequest() {
    return {};
}
export const PointsListMultiplierConfigsRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListMultiplierConfigsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsListMultiplierConfigsRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsListMultiplierConfigsRequest();
        return message;
    },
};
function createBasePointsMultiplierConfigCollection() {
    return { multipliers: [] };
}
export const PointsMultiplierConfigCollection = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.multipliers) {
            PointsMultiplierConfig.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsMultiplierConfigCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.multipliers.push(PointsMultiplierConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            multipliers: Array.isArray(object?.multipliers)
                ? object.multipliers.map((e) => PointsMultiplierConfig.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.multipliers) {
            obj.multipliers = message.multipliers.map((e) => e ? PointsMultiplierConfig.toJSON(e) : undefined);
        }
        else {
            obj.multipliers = [];
        }
        return obj;
    },
    create(base) {
        return PointsMultiplierConfigCollection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsMultiplierConfigCollection();
        message.multipliers = object.multipliers?.map((e) => PointsMultiplierConfig.fromPartial(e)) || [];
        return message;
    },
};
function createBasePointsMultiplierConfig() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
        enforceRecalculate: false,
        createdBy: "",
        createdAt: "",
        updatedBy: "",
        updatedAt: "",
    };
}
export const PointsMultiplierConfig = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(56).uint64(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(64).uint64(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(80).bool(message.enforceRecalculate);
        }
        if (message.createdBy !== "") {
            writer.uint32(90).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(98).string(message.createdAt);
        }
        if (message.updatedBy !== "") {
            writer.uint32(106).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(114).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsMultiplierConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = longToString(reader.uint64());
                    break;
                case 8:
                    message.effectiveDateEnd = longToString(reader.uint64());
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                case 10:
                    message.enforceRecalculate = reader.bool();
                    break;
                case 11:
                    message.createdBy = reader.string();
                    break;
                case 12:
                    message.createdAt = reader.string();
                    break;
                case 13:
                    message.updatedBy = reader.string();
                    break;
                case 14:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object?.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object?.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object?.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object?.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsMultiplierConfig.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsMultiplierConfig();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.multiplier = object.multiplier ?? 0;
        message.affectedUsers = object.affectedUsers?.map((e) => e) || [];
        message.affectedMarkets = object.affectedMarkets?.map((e) => e) || [];
        message.allMarketsExcept = object.allMarketsExcept?.map((e) => e) || [];
        message.effectiveDateStart = object.effectiveDateStart ?? undefined;
        message.effectiveDateEnd = object.effectiveDateEnd ?? undefined;
        message.effectiveFlags = object.effectiveFlags?.map((e) => e) || [];
        message.enforceRecalculate = object.enforceRecalculate ?? false;
        message.createdBy = object.createdBy ?? "";
        message.createdAt = object.createdAt ?? "";
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsCreateMultiplierConfigRequest() {
    return {
        marketIds: [],
        allMarketsExcept: [],
        accountAddresses: [],
        flags: [],
        multiplier: 0,
        label: "",
        effectiveFrom: undefined,
        effectiveUntil: undefined,
        enforceRecalculate: false,
    };
}
export const PointsCreateMultiplierConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.marketIds) {
            writer.uint32(10).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(18).string(v);
        }
        for (const v of message.accountAddresses) {
            writer.uint32(26).string(v);
        }
        for (const v of message.flags) {
            writer.uint32(34).string(v);
        }
        if (message.multiplier !== 0) {
            writer.uint32(41).double(message.multiplier);
        }
        if (message.label !== "") {
            writer.uint32(50).string(message.label);
        }
        if (message.effectiveFrom !== undefined) {
            writer.uint32(56).sint64(message.effectiveFrom);
        }
        if (message.effectiveUntil !== undefined) {
            writer.uint32(64).sint64(message.effectiveUntil);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(72).bool(message.enforceRecalculate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateMultiplierConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketIds.push(reader.string());
                    break;
                case 2:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 3:
                    message.accountAddresses.push(reader.string());
                    break;
                case 4:
                    message.flags.push(reader.string());
                    break;
                case 5:
                    message.multiplier = reader.double();
                    break;
                case 6:
                    message.label = reader.string();
                    break;
                case 7:
                    message.effectiveFrom = longToString(reader.sint64());
                    break;
                case 8:
                    message.effectiveUntil = longToString(reader.sint64());
                    break;
                case 9:
                    message.enforceRecalculate = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketIds: Array.isArray(object?.marketIds) ? object.marketIds.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object?.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            accountAddresses: Array.isArray(object?.accountAddresses)
                ? object.accountAddresses.map((e) => String(e))
                : [],
            flags: Array.isArray(object?.flags) ? object.flags.map((e) => String(e)) : [],
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : undefined,
            effectiveUntil: isSet(object.effectiveUntil) ? String(object.effectiveUntil) : undefined,
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        if (message.accountAddresses) {
            obj.accountAddresses = message.accountAddresses.map((e) => e);
        }
        else {
            obj.accountAddresses = [];
        }
        if (message.flags) {
            obj.flags = message.flags.map((e) => e);
        }
        else {
            obj.flags = [];
        }
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.effectiveUntil !== undefined && (obj.effectiveUntil = message.effectiveUntil);
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        return obj;
    },
    create(base) {
        return PointsCreateMultiplierConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsCreateMultiplierConfigRequest();
        message.marketIds = object.marketIds?.map((e) => e) || [];
        message.allMarketsExcept = object.allMarketsExcept?.map((e) => e) || [];
        message.accountAddresses = object.accountAddresses?.map((e) => e) || [];
        message.flags = object.flags?.map((e) => e) || [];
        message.multiplier = object.multiplier ?? 0;
        message.label = object.label ?? "";
        message.effectiveFrom = object.effectiveFrom ?? undefined;
        message.effectiveUntil = object.effectiveUntil ?? undefined;
        message.enforceRecalculate = object.enforceRecalculate ?? false;
        return message;
    },
};
function createBasePointsCreateMultiplierConfigResponse() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
        enforceRecalculate: false,
        createdBy: "",
        createdAt: "",
        updatedBy: "",
        updatedAt: "",
    };
}
export const PointsCreateMultiplierConfigResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(56).uint64(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(64).uint64(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(80).bool(message.enforceRecalculate);
        }
        if (message.createdBy !== "") {
            writer.uint32(90).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(98).string(message.createdAt);
        }
        if (message.updatedBy !== "") {
            writer.uint32(106).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(114).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateMultiplierConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = longToString(reader.uint64());
                    break;
                case 8:
                    message.effectiveDateEnd = longToString(reader.uint64());
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                case 10:
                    message.enforceRecalculate = reader.bool();
                    break;
                case 11:
                    message.createdBy = reader.string();
                    break;
                case 12:
                    message.createdAt = reader.string();
                    break;
                case 13:
                    message.updatedBy = reader.string();
                    break;
                case 14:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object?.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object?.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object?.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object?.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return PointsCreateMultiplierConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsCreateMultiplierConfigResponse();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.multiplier = object.multiplier ?? 0;
        message.affectedUsers = object.affectedUsers?.map((e) => e) || [];
        message.affectedMarkets = object.affectedMarkets?.map((e) => e) || [];
        message.allMarketsExcept = object.allMarketsExcept?.map((e) => e) || [];
        message.effectiveDateStart = object.effectiveDateStart ?? undefined;
        message.effectiveDateEnd = object.effectiveDateEnd ?? undefined;
        message.effectiveFlags = object.effectiveFlags?.map((e) => e) || [];
        message.enforceRecalculate = object.enforceRecalculate ?? false;
        message.createdBy = object.createdBy ?? "";
        message.createdAt = object.createdAt ?? "";
        message.updatedBy = object.updatedBy ?? "";
        message.updatedAt = object.updatedAt ?? "";
        return message;
    },
};
function createBasePointsDeleteMultiplierConfigRequest() {
    return { id: "" };
}
export const PointsDeleteMultiplierConfigRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteMultiplierConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return PointsDeleteMultiplierConfigRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsDeleteMultiplierConfigRequest();
        message.id = object.id ?? "";
        return message;
    },
};
function createBasePointsDeleteMultiplierConfigResponse() {
    return {};
}
export const PointsDeleteMultiplierConfigResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteMultiplierConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsDeleteMultiplierConfigResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteMultiplierConfigResponse();
        return message;
    },
};
function createBasePointsListAdminCorrectionsRequest() {
    return { correctionType: undefined, accountAddress: undefined };
}
export const PointsListAdminCorrectionsRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.correctionType !== undefined) {
            writer.uint32(10).string(message.correctionType);
        }
        if (message.accountAddress !== undefined) {
            writer.uint32(18).string(message.accountAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListAdminCorrectionsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.correctionType = reader.string();
                    break;
                case 2:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : undefined,
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create(base) {
        return PointsListAdminCorrectionsRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsListAdminCorrectionsRequest();
        message.correctionType = object.correctionType ?? undefined;
        message.accountAddress = object.accountAddress ?? undefined;
        return message;
    },
};
function createBaseAdminPointsCorrectionCollection() {
    return { corrections: [] };
}
export const AdminPointsCorrectionCollection = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.corrections) {
            AdminPointsCorrection.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAdminPointsCorrectionCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.corrections.push(AdminPointsCorrection.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            corrections: Array.isArray(object?.corrections)
                ? object.corrections.map((e) => AdminPointsCorrection.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.corrections) {
            obj.corrections = message.corrections.map((e) => e ? AdminPointsCorrection.toJSON(e) : undefined);
        }
        else {
            obj.corrections = [];
        }
        return obj;
    },
    create(base) {
        return AdminPointsCorrectionCollection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAdminPointsCorrectionCollection();
        message.corrections = object.corrections?.map((e) => AdminPointsCorrection.fromPartial(e)) || [];
        return message;
    },
};
function createBaseAdminPointsCorrection() {
    return {
        id: "",
        label: "",
        accountAddress: "",
        correctionType: "",
        value: 0,
        effectiveFrom: "",
        createdBy: "",
        createdAt: "",
    };
}
export const AdminPointsCorrection = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(34).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(41).double(message.value);
        }
        if (message.effectiveFrom !== "") {
            writer.uint32(50).string(message.effectiveFrom);
        }
        if (message.createdBy !== "") {
            writer.uint32(58).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(66).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAdminPointsCorrection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.correctionType = reader.string();
                    break;
                case 5:
                    message.value = reader.double();
                    break;
                case 6:
                    message.effectiveFrom = reader.string();
                    break;
                case 7:
                    message.createdBy = reader.string();
                    break;
                case 8:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return AdminPointsCorrection.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAdminPointsCorrection();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.accountAddress = object.accountAddress ?? "";
        message.correctionType = object.correctionType ?? "";
        message.value = object.value ?? 0;
        message.effectiveFrom = object.effectiveFrom ?? "";
        message.createdBy = object.createdBy ?? "";
        message.createdAt = object.createdAt ?? "";
        return message;
    },
};
function createBasePointsCreateAdminCorrectionRequest() {
    return { accountAddress: "", correctionType: "", value: 0, label: "", effectiveFrom: "0" };
}
export const PointsCreateAdminCorrectionRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(18).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(25).double(message.value);
        }
        if (message.label !== "") {
            writer.uint32(34).string(message.label);
        }
        if (message.effectiveFrom !== "0") {
            writer.uint32(40).sint64(message.effectiveFrom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateAdminCorrectionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.correctionType = reader.string();
                    break;
                case 3:
                    message.value = reader.double();
                    break;
                case 4:
                    message.label = reader.string();
                    break;
                case 5:
                    message.effectiveFrom = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        return obj;
    },
    create(base) {
        return PointsCreateAdminCorrectionRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsCreateAdminCorrectionRequest();
        message.accountAddress = object.accountAddress ?? "";
        message.correctionType = object.correctionType ?? "";
        message.value = object.value ?? 0;
        message.label = object.label ?? "";
        message.effectiveFrom = object.effectiveFrom ?? "0";
        return message;
    },
};
function createBasePointsCreateAdminCorrectionResponse() {
    return {
        id: "",
        label: "",
        accountAddress: "",
        correctionType: "",
        value: 0,
        effectiveFrom: "",
        createdBy: "",
        createdAt: "",
    };
}
export const PointsCreateAdminCorrectionResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(34).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(41).double(message.value);
        }
        if (message.effectiveFrom !== "") {
            writer.uint32(50).string(message.effectiveFrom);
        }
        if (message.createdBy !== "") {
            writer.uint32(58).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(66).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateAdminCorrectionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.correctionType = reader.string();
                    break;
                case 5:
                    message.value = reader.double();
                    break;
                case 6:
                    message.effectiveFrom = reader.string();
                    break;
                case 7:
                    message.createdBy = reader.string();
                    break;
                case 8:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return PointsCreateAdminCorrectionResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsCreateAdminCorrectionResponse();
        message.id = object.id ?? "";
        message.label = object.label ?? "";
        message.accountAddress = object.accountAddress ?? "";
        message.correctionType = object.correctionType ?? "";
        message.value = object.value ?? 0;
        message.effectiveFrom = object.effectiveFrom ?? "";
        message.createdBy = object.createdBy ?? "";
        message.createdAt = object.createdAt ?? "";
        return message;
    },
};
function createBasePointsDeleteAdminCorrectionRequest() {
    return { id: "" };
}
export const PointsDeleteAdminCorrectionRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteAdminCorrectionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return PointsDeleteAdminCorrectionRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsDeleteAdminCorrectionRequest();
        message.id = object.id ?? "";
        return message;
    },
};
function createBasePointsDeleteAdminCorrectionResponse() {
    return {};
}
export const PointsDeleteAdminCorrectionResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteAdminCorrectionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PointsDeleteAdminCorrectionResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteAdminCorrectionResponse();
        return message;
    },
};
function createBasePointsReprocessTradesAfterDateRequest() {
    return { afterDate: "0" };
}
export const PointsReprocessTradesAfterDateRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.afterDate !== "0") {
            writer.uint32(8).sint64(message.afterDate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsReprocessTradesAfterDateRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.afterDate = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { afterDate: isSet(object.afterDate) ? String(object.afterDate) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.afterDate !== undefined && (obj.afterDate = message.afterDate);
        return obj;
    },
    create(base) {
        return PointsReprocessTradesAfterDateRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsReprocessTradesAfterDateRequest();
        message.afterDate = object.afterDate ?? "0";
        return message;
    },
};
function createBasePointsReprocessTradesAfterDateResponse() {
    return { affectedCount: "0" };
}
export const PointsReprocessTradesAfterDateResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.affectedCount !== "0") {
            writer.uint32(8).sint64(message.affectedCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsReprocessTradesAfterDateResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.affectedCount = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { affectedCount: isSet(object.affectedCount) ? String(object.affectedCount) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.affectedCount !== undefined && (obj.affectedCount = message.affectedCount);
        return obj;
    },
    create(base) {
        return PointsReprocessTradesAfterDateResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePointsReprocessTradesAfterDateResponse();
        message.affectedCount = object.affectedCount ?? "0";
        return message;
    },
};
export class PointsSvcClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.PointsLatestForAccount = this.PointsLatestForAccount.bind(this);
        this.PointsStatsDailyForAccount = this.PointsStatsDailyForAccount.bind(this);
        this.PointsStatsWeeklyForAccount = this.PointsStatsWeeklyForAccount.bind(this);
        this.PointsLeaderboard = this.PointsLeaderboard.bind(this);
        this.PointsSimulateAllocation = this.PointsSimulateAllocation.bind(this);
        this.PointsGetLeagueConfig = this.PointsGetLeagueConfig.bind(this);
        this.PointsSetLeagueConfig = this.PointsSetLeagueConfig.bind(this);
        this.PointsGetEmissionConfig = this.PointsGetEmissionConfig.bind(this);
        this.PointsSetEmissionConfig = this.PointsSetEmissionConfig.bind(this);
        this.PointsListBanConfigs = this.PointsListBanConfigs.bind(this);
        this.PointsCreateBanConfig = this.PointsCreateBanConfig.bind(this);
        this.PointsDeleteBanConfig = this.PointsDeleteBanConfig.bind(this);
        this.PointsListMultiplierConfigs = this.PointsListMultiplierConfigs.bind(this);
        this.PointsCreateMultiplierConfig = this.PointsCreateMultiplierConfig.bind(this);
        this.PointsDeleteMultiplierConfig = this.PointsDeleteMultiplierConfig.bind(this);
        this.PointsListAdminCorrections = this.PointsListAdminCorrections.bind(this);
        this.PointsCreateAdminCorrection = this.PointsCreateAdminCorrection.bind(this);
        this.PointsDeleteAdminCorrection = this.PointsDeleteAdminCorrection.bind(this);
        this.PointsReprocessTradesAfterDate = this.PointsReprocessTradesAfterDate.bind(this);
    }
    PointsLatestForAccount(request, metadata) {
        return this.rpc.unary(PointsSvcPointsLatestForAccountDesc, PointsLatestForAccountRequest.fromPartial(request), metadata);
    }
    PointsStatsDailyForAccount(request, metadata) {
        return this.rpc.unary(PointsSvcPointsStatsDailyForAccountDesc, PointsStatsDailyForAccountRequest.fromPartial(request), metadata);
    }
    PointsStatsWeeklyForAccount(request, metadata) {
        return this.rpc.unary(PointsSvcPointsStatsWeeklyForAccountDesc, PointsStatsWeeklyForAccountRequest.fromPartial(request), metadata);
    }
    PointsLeaderboard(request, metadata) {
        return this.rpc.unary(PointsSvcPointsLeaderboardDesc, PointsLeaderboardRequest.fromPartial(request), metadata);
    }
    PointsSimulateAllocation(request, metadata) {
        return this.rpc.unary(PointsSvcPointsSimulateAllocationDesc, PointsSimulateAllocationRequest.fromPartial(request), metadata);
    }
    PointsGetLeagueConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsGetLeagueConfigDesc, PointsGetLeagueConfigRequest.fromPartial(request), metadata);
    }
    PointsSetLeagueConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsSetLeagueConfigDesc, PointsSetLeagueConfigRequest.fromPartial(request), metadata);
    }
    PointsGetEmissionConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsGetEmissionConfigDesc, PointsGetEmissionConfigRequest.fromPartial(request), metadata);
    }
    PointsSetEmissionConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsSetEmissionConfigDesc, PointsSetEmissionConfigRequest.fromPartial(request), metadata);
    }
    PointsListBanConfigs(request, metadata) {
        return this.rpc.unary(PointsSvcPointsListBanConfigsDesc, PointsListBanConfigsRequest.fromPartial(request), metadata);
    }
    PointsCreateBanConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsCreateBanConfigDesc, PointsCreateBanConfigRequest.fromPartial(request), metadata);
    }
    PointsDeleteBanConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsDeleteBanConfigDesc, PointsDeleteBanConfigRequest.fromPartial(request), metadata);
    }
    PointsListMultiplierConfigs(request, metadata) {
        return this.rpc.unary(PointsSvcPointsListMultiplierConfigsDesc, PointsListMultiplierConfigsRequest.fromPartial(request), metadata);
    }
    PointsCreateMultiplierConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsCreateMultiplierConfigDesc, PointsCreateMultiplierConfigRequest.fromPartial(request), metadata);
    }
    PointsDeleteMultiplierConfig(request, metadata) {
        return this.rpc.unary(PointsSvcPointsDeleteMultiplierConfigDesc, PointsDeleteMultiplierConfigRequest.fromPartial(request), metadata);
    }
    PointsListAdminCorrections(request, metadata) {
        return this.rpc.unary(PointsSvcPointsListAdminCorrectionsDesc, PointsListAdminCorrectionsRequest.fromPartial(request), metadata);
    }
    PointsCreateAdminCorrection(request, metadata) {
        return this.rpc.unary(PointsSvcPointsCreateAdminCorrectionDesc, PointsCreateAdminCorrectionRequest.fromPartial(request), metadata);
    }
    PointsDeleteAdminCorrection(request, metadata) {
        return this.rpc.unary(PointsSvcPointsDeleteAdminCorrectionDesc, PointsDeleteAdminCorrectionRequest.fromPartial(request), metadata);
    }
    PointsReprocessTradesAfterDate(request, metadata) {
        return this.rpc.unary(PointsSvcPointsReprocessTradesAfterDateDesc, PointsReprocessTradesAfterDateRequest.fromPartial(request), metadata);
    }
}
export const PointsSvcDesc = { serviceName: "points_svc.PointsSvc" };
export const PointsSvcPointsLatestForAccountDesc = {
    methodName: "PointsLatestForAccount",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsLatestForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsLatestForAccountResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsStatsDailyForAccountDesc = {
    methodName: "PointsStatsDailyForAccount",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsStatsDailyForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = HistoricalPointsStatsRowCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsStatsWeeklyForAccountDesc = {
    methodName: "PointsStatsWeeklyForAccount",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsStatsWeeklyForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = HistoricalPointsStatsRowCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsLeaderboardDesc = {
    methodName: "PointsLeaderboard",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsLeaderboardRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = LeaderboardPointsRowCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsSimulateAllocationDesc = {
    methodName: "PointsSimulateAllocation",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsSimulateAllocationRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsSimulateAllocationResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsGetLeagueConfigDesc = {
    methodName: "PointsGetLeagueConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsGetLeagueConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsGetLeagueConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsSetLeagueConfigDesc = {
    methodName: "PointsSetLeagueConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsSetLeagueConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsSetLeagueConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsGetEmissionConfigDesc = {
    methodName: "PointsGetEmissionConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsGetEmissionConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsGetEmissionConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsSetEmissionConfigDesc = {
    methodName: "PointsSetEmissionConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsSetEmissionConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsSetEmissionConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsListBanConfigsDesc = {
    methodName: "PointsListBanConfigs",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsListBanConfigsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = BanConfigCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsCreateBanConfigDesc = {
    methodName: "PointsCreateBanConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsCreateBanConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = BanConfigCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsDeleteBanConfigDesc = {
    methodName: "PointsDeleteBanConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsDeleteBanConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsDeleteBanConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsListMultiplierConfigsDesc = {
    methodName: "PointsListMultiplierConfigs",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsListMultiplierConfigsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsMultiplierConfigCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsCreateMultiplierConfigDesc = {
    methodName: "PointsCreateMultiplierConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsCreateMultiplierConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsCreateMultiplierConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsDeleteMultiplierConfigDesc = {
    methodName: "PointsDeleteMultiplierConfig",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsDeleteMultiplierConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsDeleteMultiplierConfigResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsListAdminCorrectionsDesc = {
    methodName: "PointsListAdminCorrections",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsListAdminCorrectionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = AdminPointsCorrectionCollection.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsCreateAdminCorrectionDesc = {
    methodName: "PointsCreateAdminCorrection",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsCreateAdminCorrectionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsCreateAdminCorrectionResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsDeleteAdminCorrectionDesc = {
    methodName: "PointsDeleteAdminCorrection",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsDeleteAdminCorrectionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsDeleteAdminCorrectionResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PointsSvcPointsReprocessTradesAfterDateDesc = {
    methodName: "PointsReprocessTradesAfterDate",
    service: PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PointsReprocessTradesAfterDateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PointsReprocessTradesAfterDateResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
