import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.services.pruning.v1";
/** SetBlockRetainHeightRequest sets the retain height for blocks. */
export interface SetBlockRetainHeightRequest {
    height: string;
}
/** SetBlockRetainHeightResponse is empty. */
export interface SetBlockRetainHeightResponse {
}
/** GetBlockRetainHeightRequest is a request for the retain height. */
export interface GetBlockRetainHeightRequest {
}
/** GetBlockRetainHeightResponse returns the retain height for blocks. */
export interface GetBlockRetainHeightResponse {
    /** The retain height set by the application. */
    appRetainHeight: string;
    /**
     * The retain height set via the pruning service (e.g. by the data
     * companion) specifically for blocks.
     */
    pruningServiceRetainHeight: string;
}
/** SetBlockResultsRetainHeightRequest sets the retain height for block results. */
export interface SetBlockResultsRetainHeightRequest {
    height: string;
}
/** SetBlockResultsRetainHeightResponse is empty. */
export interface SetBlockResultsRetainHeightResponse {
}
/** GetBlockResultsRetainHeightRequest is a request for the retain height. */
export interface GetBlockResultsRetainHeightRequest {
}
/** GetBlockResultsRetainHeightResponse returns the retain height for block results. */
export interface GetBlockResultsRetainHeightResponse {
    /**
     * The retain height set by the pruning service (e.g. by the data
     * companion) specifically for block results.
     */
    pruningServiceRetainHeight: string;
}
/** SetTxIndexerRetainHeightRequest sets the retain height for the tx indexer. */
export interface SetTxIndexerRetainHeightRequest {
    height: string;
}
/** SetTxIndexerRetainHeightResponse is empty. */
export interface SetTxIndexerRetainHeightResponse {
}
/** GetTxIndexerRetainHeightRequest is a request for the retain height. */
export interface GetTxIndexerRetainHeightRequest {
}
/** GetTxIndexerRetainHeightResponse returns the retain height for the tx indexer. */
export interface GetTxIndexerRetainHeightResponse {
    height: string;
}
/** SetBlockIndexerRetainHeightRequest sets the retain height for the block indexer. */
export interface SetBlockIndexerRetainHeightRequest {
    height: string;
}
/** SetBlockIndexerRetainHeightResponse is empty. */
export interface SetBlockIndexerRetainHeightResponse {
}
/** GetBlockIndexerRetainHeightRequest is a request for the retain height. */
export interface GetBlockIndexerRetainHeightRequest {
}
/** GetBlockIndexerRetainHeightResponse returns the retain height for the block indexer. */
export interface GetBlockIndexerRetainHeightResponse {
    height: string;
}
export declare const SetBlockRetainHeightRequest: {
    encode(message: SetBlockRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockRetainHeightRequest;
    fromJSON(object: any): SetBlockRetainHeightRequest;
    toJSON(message: SetBlockRetainHeightRequest): unknown;
    create(base?: DeepPartial<SetBlockRetainHeightRequest>): SetBlockRetainHeightRequest;
    fromPartial(object: DeepPartial<SetBlockRetainHeightRequest>): SetBlockRetainHeightRequest;
};
export declare const SetBlockRetainHeightResponse: {
    encode(_: SetBlockRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockRetainHeightResponse;
    fromJSON(_: any): SetBlockRetainHeightResponse;
    toJSON(_: SetBlockRetainHeightResponse): unknown;
    create(base?: DeepPartial<SetBlockRetainHeightResponse>): SetBlockRetainHeightResponse;
    fromPartial(_: DeepPartial<SetBlockRetainHeightResponse>): SetBlockRetainHeightResponse;
};
export declare const GetBlockRetainHeightRequest: {
    encode(_: GetBlockRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockRetainHeightRequest;
    fromJSON(_: any): GetBlockRetainHeightRequest;
    toJSON(_: GetBlockRetainHeightRequest): unknown;
    create(base?: DeepPartial<GetBlockRetainHeightRequest>): GetBlockRetainHeightRequest;
    fromPartial(_: DeepPartial<GetBlockRetainHeightRequest>): GetBlockRetainHeightRequest;
};
export declare const GetBlockRetainHeightResponse: {
    encode(message: GetBlockRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockRetainHeightResponse;
    fromJSON(object: any): GetBlockRetainHeightResponse;
    toJSON(message: GetBlockRetainHeightResponse): unknown;
    create(base?: DeepPartial<GetBlockRetainHeightResponse>): GetBlockRetainHeightResponse;
    fromPartial(object: DeepPartial<GetBlockRetainHeightResponse>): GetBlockRetainHeightResponse;
};
export declare const SetBlockResultsRetainHeightRequest: {
    encode(message: SetBlockResultsRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockResultsRetainHeightRequest;
    fromJSON(object: any): SetBlockResultsRetainHeightRequest;
    toJSON(message: SetBlockResultsRetainHeightRequest): unknown;
    create(base?: DeepPartial<SetBlockResultsRetainHeightRequest>): SetBlockResultsRetainHeightRequest;
    fromPartial(object: DeepPartial<SetBlockResultsRetainHeightRequest>): SetBlockResultsRetainHeightRequest;
};
export declare const SetBlockResultsRetainHeightResponse: {
    encode(_: SetBlockResultsRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockResultsRetainHeightResponse;
    fromJSON(_: any): SetBlockResultsRetainHeightResponse;
    toJSON(_: SetBlockResultsRetainHeightResponse): unknown;
    create(base?: DeepPartial<SetBlockResultsRetainHeightResponse>): SetBlockResultsRetainHeightResponse;
    fromPartial(_: DeepPartial<SetBlockResultsRetainHeightResponse>): SetBlockResultsRetainHeightResponse;
};
export declare const GetBlockResultsRetainHeightRequest: {
    encode(_: GetBlockResultsRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockResultsRetainHeightRequest;
    fromJSON(_: any): GetBlockResultsRetainHeightRequest;
    toJSON(_: GetBlockResultsRetainHeightRequest): unknown;
    create(base?: DeepPartial<GetBlockResultsRetainHeightRequest>): GetBlockResultsRetainHeightRequest;
    fromPartial(_: DeepPartial<GetBlockResultsRetainHeightRequest>): GetBlockResultsRetainHeightRequest;
};
export declare const GetBlockResultsRetainHeightResponse: {
    encode(message: GetBlockResultsRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockResultsRetainHeightResponse;
    fromJSON(object: any): GetBlockResultsRetainHeightResponse;
    toJSON(message: GetBlockResultsRetainHeightResponse): unknown;
    create(base?: DeepPartial<GetBlockResultsRetainHeightResponse>): GetBlockResultsRetainHeightResponse;
    fromPartial(object: DeepPartial<GetBlockResultsRetainHeightResponse>): GetBlockResultsRetainHeightResponse;
};
export declare const SetTxIndexerRetainHeightRequest: {
    encode(message: SetTxIndexerRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetTxIndexerRetainHeightRequest;
    fromJSON(object: any): SetTxIndexerRetainHeightRequest;
    toJSON(message: SetTxIndexerRetainHeightRequest): unknown;
    create(base?: DeepPartial<SetTxIndexerRetainHeightRequest>): SetTxIndexerRetainHeightRequest;
    fromPartial(object: DeepPartial<SetTxIndexerRetainHeightRequest>): SetTxIndexerRetainHeightRequest;
};
export declare const SetTxIndexerRetainHeightResponse: {
    encode(_: SetTxIndexerRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetTxIndexerRetainHeightResponse;
    fromJSON(_: any): SetTxIndexerRetainHeightResponse;
    toJSON(_: SetTxIndexerRetainHeightResponse): unknown;
    create(base?: DeepPartial<SetTxIndexerRetainHeightResponse>): SetTxIndexerRetainHeightResponse;
    fromPartial(_: DeepPartial<SetTxIndexerRetainHeightResponse>): SetTxIndexerRetainHeightResponse;
};
export declare const GetTxIndexerRetainHeightRequest: {
    encode(_: GetTxIndexerRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTxIndexerRetainHeightRequest;
    fromJSON(_: any): GetTxIndexerRetainHeightRequest;
    toJSON(_: GetTxIndexerRetainHeightRequest): unknown;
    create(base?: DeepPartial<GetTxIndexerRetainHeightRequest>): GetTxIndexerRetainHeightRequest;
    fromPartial(_: DeepPartial<GetTxIndexerRetainHeightRequest>): GetTxIndexerRetainHeightRequest;
};
export declare const GetTxIndexerRetainHeightResponse: {
    encode(message: GetTxIndexerRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTxIndexerRetainHeightResponse;
    fromJSON(object: any): GetTxIndexerRetainHeightResponse;
    toJSON(message: GetTxIndexerRetainHeightResponse): unknown;
    create(base?: DeepPartial<GetTxIndexerRetainHeightResponse>): GetTxIndexerRetainHeightResponse;
    fromPartial(object: DeepPartial<GetTxIndexerRetainHeightResponse>): GetTxIndexerRetainHeightResponse;
};
export declare const SetBlockIndexerRetainHeightRequest: {
    encode(message: SetBlockIndexerRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockIndexerRetainHeightRequest;
    fromJSON(object: any): SetBlockIndexerRetainHeightRequest;
    toJSON(message: SetBlockIndexerRetainHeightRequest): unknown;
    create(base?: DeepPartial<SetBlockIndexerRetainHeightRequest>): SetBlockIndexerRetainHeightRequest;
    fromPartial(object: DeepPartial<SetBlockIndexerRetainHeightRequest>): SetBlockIndexerRetainHeightRequest;
};
export declare const SetBlockIndexerRetainHeightResponse: {
    encode(_: SetBlockIndexerRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBlockIndexerRetainHeightResponse;
    fromJSON(_: any): SetBlockIndexerRetainHeightResponse;
    toJSON(_: SetBlockIndexerRetainHeightResponse): unknown;
    create(base?: DeepPartial<SetBlockIndexerRetainHeightResponse>): SetBlockIndexerRetainHeightResponse;
    fromPartial(_: DeepPartial<SetBlockIndexerRetainHeightResponse>): SetBlockIndexerRetainHeightResponse;
};
export declare const GetBlockIndexerRetainHeightRequest: {
    encode(_: GetBlockIndexerRetainHeightRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockIndexerRetainHeightRequest;
    fromJSON(_: any): GetBlockIndexerRetainHeightRequest;
    toJSON(_: GetBlockIndexerRetainHeightRequest): unknown;
    create(base?: DeepPartial<GetBlockIndexerRetainHeightRequest>): GetBlockIndexerRetainHeightRequest;
    fromPartial(_: DeepPartial<GetBlockIndexerRetainHeightRequest>): GetBlockIndexerRetainHeightRequest;
};
export declare const GetBlockIndexerRetainHeightResponse: {
    encode(message: GetBlockIndexerRetainHeightResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockIndexerRetainHeightResponse;
    fromJSON(object: any): GetBlockIndexerRetainHeightResponse;
    toJSON(message: GetBlockIndexerRetainHeightResponse): unknown;
    create(base?: DeepPartial<GetBlockIndexerRetainHeightResponse>): GetBlockIndexerRetainHeightResponse;
    fromPartial(object: DeepPartial<GetBlockIndexerRetainHeightResponse>): GetBlockIndexerRetainHeightResponse;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
