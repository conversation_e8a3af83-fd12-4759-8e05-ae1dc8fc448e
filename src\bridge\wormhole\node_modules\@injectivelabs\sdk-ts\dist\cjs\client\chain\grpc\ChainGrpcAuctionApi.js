"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcAuctionApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Chain Grpc API
 */
class ChainGrpcAuctionApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_1.ChainModule.Auction;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.InjectiveAuctionV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.InjectiveAuctionV1Beta1Query.QueryAuctionParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.AuctionParams(request, this.metadata));
            return index_js_2.ChainGrpcAuctionTransformer.moduleParamsResponseToModuleParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveAuctionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'AuctionParams',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'AuctionParams',
                contextModule: this.module,
            });
        }
    }
    async fetchCurrentBasket() {
        const request = core_proto_ts_1.InjectiveAuctionV1Beta1Query.QueryCurrentAuctionBasketRequest.create();
        try {
            const response = await this.retry(() => this.client.CurrentAuctionBasket(request, this.metadata));
            return index_js_2.ChainGrpcAuctionTransformer.currentBasketResponseToCurrentBasket(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveAuctionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'CurrentAuctionBasket',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'CurrentAuctionBasket',
                contextModule: this.module,
            });
        }
    }
    async fetchModuleState() {
        const request = core_proto_ts_1.InjectiveAuctionV1Beta1Query.QueryModuleStateRequest.create();
        try {
            const response = await this.retry(() => this.client.AuctionModuleState(request, this.metadata));
            return index_js_2.ChainGrpcAuctionTransformer.auctionModuleStateResponseToAuctionModuleState(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveAuctionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'AuctionModuleState',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'AuctionModuleState',
                contextModule: this.module,
            });
        }
    }
    async fetchLastAuctionResult() {
        const request = core_proto_ts_1.InjectiveAuctionV1Beta1Query.QueryLastAuctionResultRequest.create();
        try {
            const response = await this.retry(() => this.client.LastAuctionResult(request, this.metadata));
            return index_js_2.ChainGrpcAuctionTransformer.LastAuctionResultResponseToLastAuctionResult(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveAuctionV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'CurrentAuctionBasket',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'LastAuctionResult',
                contextModule: this.module,
            });
        }
    }
}
exports.ChainGrpcAuctionApi = ChainGrpcAuctionApi;
