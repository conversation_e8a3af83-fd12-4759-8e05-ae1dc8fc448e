/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { BaseAccount } from "../../../../cosmos/auth/v1beta1/auth.js";
export const protobufPackage = "ibc.applications.interchain_accounts.v1";
function createBaseInterchainAccount() {
    return { baseAccount: undefined, accountOwner: "" };
}
export const InterchainAccount = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.baseAccount !== undefined) {
            BaseAccount.encode(message.baseAccount, writer.uint32(10).fork()).ldelim();
        }
        if (message.accountOwner !== "") {
            writer.uint32(18).string(message.accountOwner);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInterchainAccount();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.baseAccount = BaseAccount.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.accountOwner = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            baseAccount: isSet(object.baseAccount) ? BaseAccount.fromJSON(object.baseAccount) : undefined,
            accountOwner: isSet(object.accountOwner) ? String(object.accountOwner) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.baseAccount !== undefined &&
            (obj.baseAccount = message.baseAccount ? BaseAccount.toJSON(message.baseAccount) : undefined);
        message.accountOwner !== undefined && (obj.accountOwner = message.accountOwner);
        return obj;
    },
    create(base) {
        return InterchainAccount.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseInterchainAccount();
        message.baseAccount = (object.baseAccount !== undefined && object.baseAccount !== null)
            ? BaseAccount.fromPartial(object.baseAccount)
            : undefined;
        message.accountOwner = object.accountOwner ?? "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
