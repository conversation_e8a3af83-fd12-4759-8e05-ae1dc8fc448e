import { MsgTransfer as BaseMsgTransferCosmjs } from 'cosmjs-types/ibc/applications/transfer/v1/tx.js';
export declare namespace MsgTransferCosmjs {
    interface Params {
        amount: {
            denom: string;
            amount: string;
        };
        sender: string;
        port: string;
        receiver: string;
        channelId: string;
        timeout?: number;
        height?: {
            revisionHeight: number;
            revisionNumber: number;
        };
    }
    type Proto = BaseMsgTransferCosmjs;
}
/**
 * @category Messages
 *
 * @deprecated use MsgTransfer with SIGN_DIRECT and a Cosmos wallet
 */
export default class MsgTransferCosmjs {
    params: MsgTransferCosmjs.Params;
    constructor(params: MsgTransferCosmjs.Params);
    static fromJSON(params: MsgTransferCosmjs.Params): MsgTransferCosmjs;
    toProto(): BaseMsgTransferCosmjs;
    toData(): void;
    toAmino(): {
        type: string;
        value: {
            sourcePort: string;
            sourceChannel: string;
            token: import("cosmjs-types/cosmos/base/v1beta1/coin").Coin;
            sender: string;
            receiver: string;
            timeoutHeight: import("cosmjs-types/ibc/core/client/v1/client").Height;
            timeoutTimestamp: bigint;
            memo: string;
        };
    };
    toWeb3Gw(): {
        sourcePort: string;
        sourceChannel: string;
        token: import("cosmjs-types/cosmos/base/v1beta1/coin").Coin;
        sender: string;
        receiver: string;
        timeoutHeight: import("cosmjs-types/ibc/core/client/v1/client").Height;
        timeoutTimestamp: bigint;
        memo: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: BaseMsgTransferCosmjs;
    };
    toBinary(): Uint8Array;
}
