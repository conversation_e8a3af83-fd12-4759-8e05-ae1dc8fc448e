import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { InsuranceFund, RedemptionSchedule } from "./insurance";
export declare const protobufPackage = "injective.insurance.v1beta1";
export interface EventInsuranceFundUpdate {
    fund: InsuranceFund | undefined;
}
export interface EventRequestRedemption {
    schedule: RedemptionSchedule | undefined;
}
export interface EventWithdrawRedemption {
    /** redemption schedule triggered withdraw */
    schedule: RedemptionSchedule | undefined;
    /** redeem coin amount in base_currency */
    redeemCoin: Coin | undefined;
}
export interface EventUnderwrite {
    /** address of the underwriter */
    underwriter: string;
    /** marketId of insurance fund for the redemption */
    marketId: string;
    /** deposit coin amount */
    deposit: Coin | undefined;
    /** share coin amount */
    shares: Coin | undefined;
}
export interface EventInsuranceWithdraw {
    marketId: string;
    marketTicker: string;
    withdrawal: Coin | undefined;
}
export declare const EventInsuranceFundUpdate: {
    encode(message: EventInsuranceFundUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventInsuranceFundUpdate;
    fromJSON(object: any): EventInsuranceFundUpdate;
    toJSON(message: EventInsuranceFundUpdate): unknown;
    create(base?: DeepPartial<EventInsuranceFundUpdate>): EventInsuranceFundUpdate;
    fromPartial(object: DeepPartial<EventInsuranceFundUpdate>): EventInsuranceFundUpdate;
};
export declare const EventRequestRedemption: {
    encode(message: EventRequestRedemption, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventRequestRedemption;
    fromJSON(object: any): EventRequestRedemption;
    toJSON(message: EventRequestRedemption): unknown;
    create(base?: DeepPartial<EventRequestRedemption>): EventRequestRedemption;
    fromPartial(object: DeepPartial<EventRequestRedemption>): EventRequestRedemption;
};
export declare const EventWithdrawRedemption: {
    encode(message: EventWithdrawRedemption, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventWithdrawRedemption;
    fromJSON(object: any): EventWithdrawRedemption;
    toJSON(message: EventWithdrawRedemption): unknown;
    create(base?: DeepPartial<EventWithdrawRedemption>): EventWithdrawRedemption;
    fromPartial(object: DeepPartial<EventWithdrawRedemption>): EventWithdrawRedemption;
};
export declare const EventUnderwrite: {
    encode(message: EventUnderwrite, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventUnderwrite;
    fromJSON(object: any): EventUnderwrite;
    toJSON(message: EventUnderwrite): unknown;
    create(base?: DeepPartial<EventUnderwrite>): EventUnderwrite;
    fromPartial(object: DeepPartial<EventUnderwrite>): EventUnderwrite;
};
export declare const EventInsuranceWithdraw: {
    encode(message: EventInsuranceWithdraw, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventInsuranceWithdraw;
    fromJSON(object: any): EventInsuranceWithdraw;
    toJSON(message: EventInsuranceWithdraw): unknown;
    create(base?: DeepPartial<EventInsuranceWithdraw>): EventInsuranceWithdraw;
    fromPartial(object: DeepPartial<EventInsuranceWithdraw>): EventInsuranceWithdraw;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
