import { ExecPrivilegedArgs } from '../exec-args.js';
import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgPrivilegedExecuteContract {
    interface Params {
        sender: string;
        funds: string;
        contractAddress: string;
        data: ExecPrivilegedArgs;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract;
}
/**
 * @category Messages
 */
export default class MsgPrivilegedExecuteContract extends MsgBase<MsgPrivilegedExecuteContract.Params, MsgPrivilegedExecuteContract.Proto> {
    static fromJSON(params: MsgPrivilegedExecuteContract.Params): MsgPrivilegedExecuteContract;
    toProto(): MsgPrivilegedExecuteContract.Proto;
    toData(): {
        sender: string;
        funds: string;
        contractAddress: string;
        data: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract>;
    };
    toWeb3Gw(): {
        sender: string;
        funds: string;
        contract_address: string;
        data: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgPrivilegedExecuteContract;
    };
    toBinary(): Uint8Array;
}
