import { OrderSide, OrderState } from '@injectivelabs/ts-types';
import { InjectiveDerivativeExchangeRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { TradeDirection, TradeExecutionSide, TradeExecutionType } from '../../../types/exchange.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcDerivativesApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveDerivativeExchangeRpc.InjectiveDerivativeExchangeRPCClientImpl;
    constructor(endpoint: string);
    fetchMarkets(params?: {
        quoteDenom?: string;
        marketStatus?: string;
        marketStatuses?: string[];
    }): Promise<import("../types/derivatives.js").DerivativeMarket[]>;
    fetchMarket(marketId: string): Promise<import("../types/derivatives.js").DerivativeMarket>;
    fetchBinaryOptionsMarkets(params?: {
        marketStatus?: string;
        quoteDenom?: string;
        pagination?: PaginationOption;
    }): Promise<{
        markets: import("../types/derivatives.js").BinaryOptionsMarket[];
        pagination: {
            to: number;
            from: number;
            total: number;
            countBySubaccount: number;
            next: string[];
        };
    }>;
    fetchBinaryOptionsMarket(marketId: string): Promise<import("../types/derivatives.js").BinaryOptionsMarket>;
    /** @deprecated - use fetchOrderbookV2 */
    fetchOrderbook(_marketId: string): Promise<void>;
    fetchOrders(params?: {
        cid?: string;
        tradeId?: string;
        marketId?: string;
        marketIds?: string[];
        orderSide?: OrderSide;
        pagination?: PaginationOption;
        subaccountId?: string;
        isConditional?: boolean;
    }): Promise<{
        orders: import("../types/derivatives.js").DerivativeLimitOrder[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchOrderHistory(params?: {
        cid?: string;
        state?: OrderState;
        tradeId?: string;
        marketId?: string;
        marketIds?: string[];
        orderTypes?: OrderSide[];
        direction?: TradeDirection;
        pagination?: PaginationOption;
        subaccountId?: string;
        isConditional?: boolean;
        executionTypes?: TradeExecutionType[];
    }): Promise<{
        orderHistory: import("../types/derivatives.js").DerivativeOrderHistory[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchPositions(params?: {
        marketId?: string;
        marketIds?: string[];
        subaccountId?: string;
        direction?: TradeDirection;
        pagination?: PaginationOption;
    }): Promise<{
        positions: import("../types/derivatives.js").Position[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchPositionsV2(params?: {
        address?: string;
        marketId?: string;
        marketIds?: string[];
        subaccountId?: string;
        direction?: TradeDirection;
        pagination?: PaginationOption;
    }): Promise<{
        positions: import("../types/derivatives.js").PositionV2[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchTrades(params?: {
        endTime?: number;
        tradeId?: string;
        marketId?: string;
        startTime?: number;
        marketIds?: string[];
        subaccountId?: string;
        accountAddress?: string;
        direction?: TradeDirection;
        pagination?: PaginationOption;
        executionSide?: TradeExecutionSide;
        executionTypes?: TradeExecutionType[];
        cid?: string;
    }): Promise<{
        trades: import("../types/derivatives.js").DerivativeTrade[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchFundingPayments(params?: {
        marketId?: string;
        marketIds?: string[];
        subaccountId?: string;
        pagination?: PaginationOption;
    }): Promise<{
        fundingPayments: import("../types/derivatives.js").FundingPayment[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchFundingRates(params?: {
        marketId?: string;
        pagination?: PaginationOption;
    }): Promise<{
        fundingRates: import("../types/derivatives.js").FundingRate[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchSubaccountOrdersList(params?: {
        marketId?: string;
        subaccountId?: string;
        pagination?: PaginationOption;
    }): Promise<{
        orders: import("../types/derivatives.js").DerivativeLimitOrder[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchSubaccountTradesList(params: {
        marketId?: string;
        subaccountId?: string;
        direction?: TradeDirection;
        executionType?: TradeExecutionType;
        pagination?: PaginationOption;
    }): Promise<import("../types/derivatives.js").DerivativeTrade[]>;
    /** @deprecated - use fetchOrderbooksV2 */
    fetchOrderbooks(_marketIds: string[]): Promise<void>;
    fetchOrderbooksV2(marketIds: string[]): Promise<{
        marketId: string;
        orderbook: import("../types/exchange.js").OrderbookWithSequence;
    }[]>;
    fetchOrderbookV2(marketId: string): Promise<import("../types/exchange.js").OrderbookWithSequence>;
}
