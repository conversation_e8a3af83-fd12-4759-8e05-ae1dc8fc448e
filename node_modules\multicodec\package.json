{"name": "multicodec", "version": "0.5.7", "description": "JavaScript implementation of the multicodec specification", "leadMaintainer": "<PERSON><PERSON> <<EMAIL>>", "main": "src/index.js", "scripts": {"lint": "aegir lint", "test": "aegir test", "test:node": "aegir test --target node", "test:browser": "aegir test --target browser", "build": "aegir build", "docs": "aegir docs", "release": "aegir release", "release-minor": "aegir release --type minor", "release-major": "aegir release --type major", "coverage": "aegir coverage", "coverage-publish": "aegir coverage --provider coveralls"}, "pre-push": ["lint", "test"], "repository": {"type": "git", "url": "git+https://github.com/multiformats/js-multicodec.git"}, "keywords": ["IPFS", "multiformats", "multicodec", "binary", "packed", "the", "data!"], "license": "MIT", "bugs": {"url": "https://github.com/multiformats/js-multicodec/issues"}, "homepage": "https://github.com/multiformats/js-multicodec#readme", "dependencies": {"varint": "^5.0.0"}, "devDependencies": {"aegir": "^20.5.0", "bent": "^7.0.4", "chai": "^4.2.0", "dirty-chai": "^2.0.1", "pre-push": "~0.1.1"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Mische <<EMAIL>>", "achingbrain <<EMAIL>>", "kumavis <<EMAIL>>", "kumavis <<EMAIL>>", "wanderer <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "ᴠɪᴄᴛᴏʀ ʙᴊᴇʟᴋʜᴏʟᴍ <victorb<PERSON><PERSON><PERSON>@gmail.com>"]}