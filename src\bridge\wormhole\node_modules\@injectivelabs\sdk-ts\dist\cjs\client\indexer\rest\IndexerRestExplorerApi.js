"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerRestExplorerApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const BaseRestConsumer_js_1 = __importDefault(require("../../base/BaseRestConsumer.js"));
const index_js_1 = require("../transformers/index.js");
const index_js_2 = require("../types/index.js");
const explorerEndpointSuffix = 'api/explorer/v1';
/**
 * @category Indexer Rest API
 */
class IndexerRestExplorerApi extends BaseRestConsumer_js_1.default {
    constructor(endpoint) {
        super(endpoint.includes(explorerEndpointSuffix)
            ? endpoint
            : `${endpoint}/${explorerEndpointSuffix}`);
    }
    async fetchBlock(blockHashHeight) {
        const endpoint = `blocks/${blockHashHeight}`;
        try {
            const response = await this.retry(() => this.get(`blocks/${blockHashHeight}`));
            return index_js_1.IndexerRestExplorerTransformer.blockWithTxToBlockWithTx(response.data.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchBlocks(params) {
        const endpoint = 'blocks';
        try {
            const { before, limit, from, to } = params || { limit: 12 };
            const response = await this.retry(() => this.get(endpoint, {
                before,
                limit,
                from,
                to,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                blocks: index_js_1.IndexerRestExplorerTransformer.blocksToBlocks(data),
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchBlocksWithTx(params) {
        const endpoint = 'blocks';
        try {
            const { before, limit, from, to } = params || { limit: 12 };
            const response = await this.retry(() => this.get(endpoint, {
                before,
                limit,
                from,
                to,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                blocks: data
                    ? index_js_1.IndexerRestExplorerTransformer.blocksWithTxsToBlocksWithTxs(data)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchTransactions(params) {
        const endpoint = 'txs';
        try {
            const { type, skip, after, limit, before, status, endTime, toNumber, startTime, fromNumber, } = params || {
                limit: 12,
            };
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                after,
                before,
                status,
                end_time: endTime,
                to_number: toNumber,
                start_time: startTime,
                from_number: fromNumber,
                type: type ? type.join(',') : undefined,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                transactions: data
                    ? index_js_1.IndexerRestExplorerTransformer.transactionsToTransactions(data)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchAccountTransactions({ account, params, }) {
        const endpoint = `accountTxs/${account}`;
        try {
            const { type, skip, limit, after, before, status, endTime, toNumber, startTime, fromNumber, withClaimId, } = params || {
                limit: 12,
            };
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                after,
                before,
                status,
                end_time: endTime,
                to_number: toNumber,
                start_time: startTime,
                from_number: fromNumber,
                type: type ? type.join(',') : undefined,
                with_claim_id: withClaimId,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                transactions: data
                    ? index_js_1.IndexerRestExplorerTransformer.transactionsToTransactions(data)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchTransaction(hash) {
        const endpoint = `txs/${hash}`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            return index_js_1.IndexerRestExplorerTransformer.transactionToTransaction(response.data.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchValidators() {
        const endpoint = 'validators';
        try {
            const response = await this.retry(() => this.get(endpoint));
            if (!response.data || !response.data.data) {
                return [];
            }
            return index_js_1.IndexerRestExplorerTransformer.validatorExplorerToValidator(response.data.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchValidatorUptime(validatorConsensusAddress) {
        const endpoint = `validator_uptime/${validatorConsensusAddress}`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            if (!response.data || !response.data.data) {
                return [];
            }
            return index_js_1.IndexerRestExplorerTransformer.validatorUptimeToExplorerValidatorUptime(response.data.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchContract(contractAddress) {
        const endpoint = `/wasm/contracts/${contractAddress}`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            return index_js_1.IndexerRestExplorerTransformer.contractToExplorerContract(response.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchContracts(params) {
        const endpoint = `/wasm/contracts`;
        try {
            const { assetsOnly, fromNumber, limit, skip, label, codeId } = params || {
                limit: 12,
            };
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                label,
                code_id: codeId?.toString(),
                assets_only: assetsOnly,
                from_number: fromNumber,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                contracts: data
                    ? data.map(index_js_1.IndexerRestExplorerTransformer.contractToExplorerContract)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchContractTransactions({ contractAddress, params, }) {
        const endpoint = `/contractTxs/${contractAddress}`;
        try {
            const { fromNumber, limit, skip, toNumber } = params || { limit: 12 };
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                to_number: toNumber,
                from_number: fromNumber,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                transactions: data
                    ? data.map(index_js_1.IndexerRestExplorerTransformer.contractTransactionToExplorerContractTransaction)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchContractTransactionsWithMessages({ contractAddress, params, }) {
        const endpoint = `/contractTxs/${contractAddress}`;
        try {
            const { fromNumber, limit, skip, toNumber } = params || { limit: 12 };
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                to_number: toNumber,
                from_number: fromNumber,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                transactions: data
                    ? data.map(index_js_1.IndexerRestExplorerTransformer.contractTransactionToExplorerContractTransactionWithMessages)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchWasmCode(codeId) {
        const endpoint = `/wasm/codes/${codeId}`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            return index_js_1.IndexerRestExplorerTransformer.wasmCodeToExplorerWasmCode(response.data);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchWasmCodes(params) {
        const endpoint = `/wasm/codes`;
        try {
            const { fromNumber, limit, toNumber } = params || { limit: 12 };
            const response = await this.retry(() => this.get(endpoint, {
                limit,
                from_number: fromNumber,
                to_number: toNumber,
            }));
            const { paging, data } = response.data;
            return {
                paging,
                wasmCodes: data
                    ? data.map(index_js_1.IndexerRestExplorerTransformer.wasmCodeToExplorerWasmCode)
                    : [],
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchCW20Balances(address) {
        const endpoint = `/wasm/${address}/cw20-balance`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            if (response.data.length === 0) {
                return [];
            }
            return response.data.map(index_js_1.IndexerRestExplorerTransformer.CW20BalanceToExplorerCW20Balance);
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchCW20BalancesNoThrow(address) {
        const endpoint = `/wasm/${address}/cw20-balance`;
        try {
            const response = await this.retry(() => this.get(endpoint));
            if (response.data.length === 0) {
                return [];
            }
            return response.data;
        }
        catch (e) {
            const error = e;
            if (error.message.includes(404) || error.message.includes(500)) {
                return [];
            }
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
    async fetchBankTransfers(params) {
        const endpoint = `/bank/transfers`;
        const { endTime, limit, skip, startTime, address, recipients, senders } = params || { limit: 10 };
        try {
            const response = await this.retry(() => this.get(endpoint, {
                skip,
                limit,
                senders,
                address,
                recipients,
                end_time: endTime,
                start_time: startTime,
                is_community_pool_related: params.isCommunitySpendPool,
            }));
            const { data, paging } = response.data;
            return {
                paging,
                data: index_js_1.IndexerRestExplorerTransformer.bankTransfersToBankTransfers(data || []),
            };
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_2.IndexerModule.Explorer,
            });
        }
    }
}
exports.IndexerRestExplorerApi = IndexerRestExplorerApi;
