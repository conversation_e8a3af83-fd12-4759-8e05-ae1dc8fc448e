{"version": 3, "sources": ["../../src/core/crypto/ed25519.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { ed25519 } from \"@noble/curves/ed25519\";\nimport { Deserializer } from \"../../bcs/deserializer\";\nimport { Serializable, Serializer } from \"../../bcs/serializer\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Hex } from \"../hex\";\nimport { HexInput, SigningScheme as AuthenticationKeyScheme, PrivateKeyVariants } from \"../../types\";\nimport { CK<PERSON>riv, deriveKey, HARDENED_OFFSET, isValidHardenedPath, mnemonicToSeed, splitPath } from \"./hdKey\";\nimport { PrivateKey } from \"./privateKey\";\nimport { AccountPublicKey, PublicKey, VerifySignatureArgs, VerifySignatureAsyncArgs } from \"./publicKey\";\nimport { Signature } from \"./signature\";\nimport { convertSigningMessage } from \"./utils\";\n\n/**\n * L is the value that greater than or equal to will produce a non-canonical signature, and must be rejected\n * @group Implementation\n * @category Serialization\n */\nconst L: number[] = [\n  0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0x00, 0x00, 0x00,\n  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10,\n];\n\n/**\n * Checks if an ED25519 signature is non-canonical.\n * This function helps determine the validity of a signature by verifying its canonical form.\n *\n * @param signature - The signature to be checked for canonicality.\n * @returns A boolean indicating whether the signature is non-canonical.\n *\n * Comes from Aptos Core\n * https://github.com/aptos-labs/aptos-core/blob/main/crates/aptos-crypto/src/ed25519/ed25519_sigs.rs#L47-L85\n * @group Implementation\n * @category Serialization\n */\nexport function isCanonicalEd25519Signature(signature: Signature): boolean {\n  const s = signature.toUint8Array().slice(32);\n  for (let i = L.length - 1; i >= 0; i -= 1) {\n    if (s[i] < L[i]) {\n      return true;\n    }\n    if (s[i] > L[i]) {\n      return false;\n    }\n  }\n  // As this stage S == L which implies a non-canonical S.\n  return false;\n}\n\n/**\n * Represents the public key of an Ed25519 key pair.\n *\n * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports\n * `Legacy` and `Unified` authentication keys.\n *\n * Ed25519 scheme is represented in the SDK as `Legacy authentication key` and also\n * as `AnyPublicKey` that represents any `Unified authentication key`.\n * @group Implementation\n * @category Serialization\n */\nexport class Ed25519PublicKey extends AccountPublicKey {\n  /**\n   * Length of an Ed25519 public key\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * Bytes of the public key\n   * @private\n   * @group Implementation\n   * @category Serialization\n   */\n  private readonly key: Hex;\n\n  /**\n   * Creates an instance of the Ed25519Signature class from a hex input.\n   * This constructor validates the length of the signature to ensure it meets the required specifications.\n   *\n   * @param hexInput - The hex input representing the Ed25519 signature.\n   * @throws Error if the signature length is not equal to Ed25519Signature.LENGTH.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(hexInput: HexInput) {\n    super();\n\n    const hex = Hex.fromHexInput(hexInput);\n    if (hex.toUint8Array().length !== Ed25519PublicKey.LENGTH) {\n      throw new Error(`PublicKey length should be ${Ed25519PublicKey.LENGTH}`);\n    }\n    this.key = hex;\n  }\n\n  // region AccountPublicKey\n\n  /**\n   * Verifies a signed message using a public key.\n   *\n   * @param args - The arguments for verification.\n   * @param args.message - A signed message as a Hex string or Uint8Array.\n   * @param args.signature - The signature of the message.\n   * @group Implementation\n   * @category Serialization\n   */\n  verifySignature(args: VerifySignatureArgs): boolean {\n    const { message, signature } = args;\n    // Verify malleability\n    if (!isCanonicalEd25519Signature(signature)) {\n      return false;\n    }\n\n    const messageToVerify = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToVerify).toUint8Array();\n    const signatureBytes = signature.toUint8Array();\n    const publicKeyBytes = this.key.toUint8Array();\n    return ed25519.verify(signatureBytes, messageBytes, publicKeyBytes);\n  }\n\n  /**\n   * Note: Ed25519Signatures can be verified syncronously.\n   *\n   * Verifies the provided signature against the given message.\n   * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.\n   *\n   * @param args - The arguments for signature verification.\n   * @param args.aptosConfig - The configuration object for connecting to the Aptos network\n   * @param args.message - The message that was signed.\n   * @param args.signature - The signature to verify, which must be an instance of Secp256k1Signature.\n   * @returns A boolean indicating whether the signature is valid for the given message.\n   * @group Implementation\n   * @category Serialization\n   */\n  async verifySignatureAsync(args: VerifySignatureAsyncArgs): Promise<boolean> {\n    return this.verifySignature(args);\n  }\n\n  /**\n   * Generates an authentication key from the public key using the Ed25519 scheme.\n   * This function is essential for creating a secure authentication key that can be used for further cryptographic operations.\n   *\n   * @returns {AuthenticationKey} The generated authentication key.\n   * @group Implementation\n   * @category Serialization\n   */\n  authKey(): AuthenticationKey {\n    return AuthenticationKey.fromSchemeAndBytes({\n      scheme: AuthenticationKeyScheme.Ed25519,\n      input: this.toUint8Array(),\n    });\n  }\n\n  /**\n   * Convert the internal data representation to a Uint8Array.\n   *\n   * @returns Uint8Array representation of the data.\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.key.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  /**\n   * Serializes the data into a byte array using the provided serializer.\n   * This allows for the conversion of data into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category Serialization\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.key.toUint8Array());\n  }\n\n  /**\n   * Deserialize bytes into an Ed25519Signature object.\n   * This function is used to convert serialized byte data into a usable Ed25519Signature instance.\n   *\n   * @param deserializer - The deserializer instance used to read the byte data.\n   * @group Implementation\n   * @category Serialization\n   */\n  static deserialize(deserializer: Deserializer): Ed25519PublicKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519PublicKey(bytes);\n  }\n\n  // endregion\n\n  /**\n   * Determine if the provided public key is an instance of Ed25519PublicKey.\n   *\n   * @param publicKey - The public key to check.\n   * @returns True if the public key is an instance of Ed25519PublicKey, otherwise false.\n   * @deprecated use `instanceof Ed25519PublicKey` instead.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isPublicKey(publicKey: AccountPublicKey): publicKey is Ed25519PublicKey {\n    return publicKey instanceof Ed25519PublicKey;\n  }\n\n  /**\n   * Determines if the provided public key is a valid Ed25519 public key.\n   * This function checks for the presence of the \"key\" property and verifies that its data length matches the expected length\n   * for Ed25519 public keys.\n   *\n   * @param publicKey - The public key to validate.\n   * @returns A boolean indicating whether the public key is a valid Ed25519 public key.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isInstance(publicKey: PublicKey): publicKey is Ed25519PublicKey {\n    return \"key\" in publicKey && (publicKey.key as any)?.data?.length === Ed25519PublicKey.LENGTH;\n  }\n}\n\n/**\n * Represents the private key of an Ed25519 key pair.\n * @group Implementation\n * @category Serialization\n */\nexport class Ed25519PrivateKey extends Serializable implements PrivateKey {\n  /**\n   * Length of an Ed25519 private key\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH: number = 32;\n\n  /**\n   * The Ed25519 key seed to use for BIP-32 compatibility\n   * See more {@link https://github.com/satoshilabs/slips/blob/master/slip-0010.md}\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly SLIP_0010_SEED = \"ed25519 seed\";\n\n  /**\n   * The Ed25519 signing key\n   * @private\n   * @group Implementation\n   * @category Serialization\n   */\n  private readonly signingKey: Hex;\n\n  // region Constructors\n\n  /**\n   * Create a new PrivateKey instance from a Uint8Array or String.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @param hexInput HexInput (string or Uint8Array)\n   * @param strict If true, private key must AIP-80 compliant.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(hexInput: HexInput, strict?: boolean) {\n    super();\n\n    const privateKeyHex = PrivateKey.parseHexInput(hexInput, PrivateKeyVariants.Ed25519, strict);\n    if (privateKeyHex.toUint8Array().length !== Ed25519PrivateKey.LENGTH) {\n      throw new Error(`PrivateKey length should be ${Ed25519PrivateKey.LENGTH}`);\n    }\n\n    // Create keyPair from Private key in Uint8Array format\n    this.signingKey = privateKeyHex;\n  }\n\n  /**\n   * Generate a new random private key.\n   *\n   * @returns Ed25519PrivateKey A newly generated Ed25519 private key.\n   * @group Implementation\n   * @category Serialization\n   */\n  static generate(): Ed25519PrivateKey {\n    const keyPair = ed25519.utils.randomPrivateKey();\n    return new Ed25519PrivateKey(keyPair, false);\n  }\n\n  /**\n   * Derives a private key from a mnemonic seed phrase using a specified BIP44 path.\n   * To derive multiple keys from the same phrase, change the path\n   *\n   * IMPORTANT: Ed25519 supports hardened derivation only, as it lacks a key homomorphism, making non-hardened derivation impossible.\n   *\n   * @param path - The BIP44 path used for key derivation.\n   * @param mnemonics - The mnemonic seed phrase from which the key will be derived.\n   * @throws Error if the provided path is not a valid hardened path.\n   * @group Implementation\n   * @category Serialization\n   */\n  static fromDerivationPath(path: string, mnemonics: string): Ed25519PrivateKey {\n    if (!isValidHardenedPath(path)) {\n      throw new Error(`Invalid derivation path ${path}`);\n    }\n    return Ed25519PrivateKey.fromDerivationPathInner(path, mnemonicToSeed(mnemonics));\n  }\n\n  /**\n   * Derives a child private key from a given BIP44 path and seed.\n   * A private inner function so we can separate from the main fromDerivationPath() method\n   * to add tests to verify we create the keys correctly.\n   *\n   * @param path - The BIP44 path used for key derivation.\n   * @param seed - The seed phrase created by the mnemonics, represented as a Uint8Array.\n   * @param offset - The offset used for key derivation, defaults to HARDENED_OFFSET.\n   * @returns An instance of Ed25519PrivateKey derived from the specified path and seed.\n   * @group Implementation\n   * @category Serialization\n   */\n  private static fromDerivationPathInner(path: string, seed: Uint8Array, offset = HARDENED_OFFSET): Ed25519PrivateKey {\n    const { key, chainCode } = deriveKey(Ed25519PrivateKey.SLIP_0010_SEED, seed);\n\n    const segments = splitPath(path).map((el) => parseInt(el, 10));\n\n    // Derive the child key based on the path\n    const { key: privateKey } = segments.reduce((parentKeys, segment) => CKDPriv(parentKeys, segment + offset), {\n      key,\n      chainCode,\n    });\n    return new Ed25519PrivateKey(privateKey, false);\n  }\n\n  // endregion\n\n  // region PrivateKey\n\n  /**\n   * Derive the Ed25519PublicKey for this private key.\n   *\n   * @returns Ed25519PublicKey - The derived public key corresponding to the private key.\n   * @group Implementation\n   * @category Serialization\n   */\n  publicKey(): Ed25519PublicKey {\n    const bytes = ed25519.getPublicKey(this.signingKey.toUint8Array());\n    return new Ed25519PublicKey(bytes);\n  }\n\n  /**\n   * Sign the given message with the private key.\n   * This function generates a digital signature for the specified message, ensuring its authenticity and integrity.\n   *\n   * @param message - A message as a string or Uint8Array in HexInput format.\n   * @returns A digital signature for the provided message.\n   * @group Implementation\n   * @category Serialization\n   */\n  sign(message: HexInput): Ed25519Signature {\n    const messageToSign = convertSigningMessage(message);\n    const messageBytes = Hex.fromHexInput(messageToSign).toUint8Array();\n    const signatureBytes = ed25519.sign(messageBytes, this.signingKey.toUint8Array());\n    return new Ed25519Signature(signatureBytes);\n  }\n\n  /**\n   * Get the private key in bytes (Uint8Array).\n   *\n   * @returns Uint8Array representation of the private key\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.signingKey.toUint8Array();\n  }\n\n  /**\n   * Get the private key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the private key.\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): string {\n    return this.toAIP80String();\n  }\n\n  /**\n   * Get the private key as a hex string with the 0x prefix.\n   *\n   * @returns string representation of the private key.\n   */\n  toHexString(): string {\n    return this.signingKey.toString();\n  }\n\n  /**\n   * Get the private key as a AIP-80 compliant hex string.\n   *\n   * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)\n   *\n   * @returns AIP-80 compliant string representation of the private key.\n   */\n  toAIP80String(): string {\n    return PrivateKey.formatPrivateKey(this.signingKey.toString(), PrivateKeyVariants.Ed25519);\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Ed25519PrivateKey {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519PrivateKey(bytes, false);\n  }\n\n  // endregion\n\n  /**\n   * Determines if the provided private key is an instance of Ed25519PrivateKey.\n   *\n   * @param privateKey - The private key to check.\n   * @returns A boolean indicating whether the private key is an Ed25519PrivateKey.\n   *\n   * @deprecated Use `instanceof Ed25519PrivateKey` instead.\n   * @group Implementation\n   * @category Serialization\n   */\n  static isPrivateKey(privateKey: PrivateKey): privateKey is Ed25519PrivateKey {\n    return privateKey instanceof Ed25519PrivateKey;\n  }\n}\n\n/**\n * Represents a signature of a message signed using an Ed25519 private key.\n * @group Implementation\n * @category Serialization\n */\nexport class Ed25519Signature extends Signature {\n  /**\n   * Length of an Ed25519 signature, which is 64 bytes.\n   * @group Implementation\n   * @category Serialization\n   */\n  static readonly LENGTH = 64;\n\n  /**\n   * The signature bytes\n   * @private\n   * @group Implementation\n   * @category Serialization\n   */\n  private readonly data: Hex;\n\n  // region Constructors\n\n  constructor(hexInput: HexInput) {\n    super();\n    const data = Hex.fromHexInput(hexInput);\n    if (data.toUint8Array().length !== Ed25519Signature.LENGTH) {\n      throw new Error(`Signature length should be ${Ed25519Signature.LENGTH}`);\n    }\n    this.data = data;\n  }\n\n  // endregion\n\n  // region Signature\n\n  toUint8Array(): Uint8Array {\n    return this.data.toUint8Array();\n  }\n\n  // endregion\n\n  // region Serializable\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.data.toUint8Array());\n  }\n\n  static deserialize(deserializer: Deserializer): Ed25519Signature {\n    const bytes = deserializer.deserializeBytes();\n    return new Ed25519Signature(bytes);\n  }\n\n  // endregion\n}\n"], "mappings": "2WAGA,OAAS,WAAAA,MAAe,wBAiBxB,IAAMC,EAAc,CAClB,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,EAAM,EAAM,EAC5G,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAC1E,EAcO,SAASC,EAA4BC,EAA+B,CACzE,IAAMC,EAAID,EAAU,aAAa,EAAE,MAAM,EAAE,EAC3C,QAASE,EAAIJ,EAAE,OAAS,EAAGI,GAAK,EAAGA,GAAK,EAAG,CACzC,GAAID,EAAEC,CAAC,EAAIJ,EAAEI,CAAC,EACZ,MAAO,GAET,GAAID,EAAEC,CAAC,EAAIJ,EAAEI,CAAC,EACZ,MAAO,EAEX,CAEA,MAAO,EACT,CAaO,IAAMC,EAAN,MAAMA,UAAyBC,CAAiB,CAyBrD,YAAYC,EAAoB,CAC9B,MAAM,EAEN,IAAMC,EAAMC,EAAI,aAAaF,CAAQ,EACrC,GAAIC,EAAI,aAAa,EAAE,SAAWH,EAAiB,OACjD,MAAM,IAAI,MAAM,8BAA8BA,EAAiB,MAAM,EAAE,EAEzE,KAAK,IAAMG,CACb,CAaA,gBAAgBE,EAAoC,CAClD,GAAM,CAAE,QAAAC,EAAS,UAAAT,CAAU,EAAIQ,EAE/B,GAAI,CAACT,EAA4BC,CAAS,EACxC,MAAO,GAGT,IAAMU,EAAkBC,EAAsBF,CAAO,EAC/CG,EAAeL,EAAI,aAAaG,CAAe,EAAE,aAAa,EAC9DG,EAAiBb,EAAU,aAAa,EACxCc,EAAiB,KAAK,IAAI,aAAa,EAC7C,OAAOC,EAAQ,OAAOF,EAAgBD,EAAcE,CAAc,CACpE,CAgBA,MAAM,qBAAqBN,EAAkD,CAC3E,OAAO,KAAK,gBAAgBA,CAAI,CAClC,CAUA,SAA6B,CAC3B,OAAOQ,EAAkB,mBAAmB,CAC1C,SACA,MAAO,KAAK,aAAa,CAC3B,CAAC,CACH,CASA,cAA2B,CACzB,OAAO,KAAK,IAAI,aAAa,CAC/B,CAcA,UAAUC,EAA8B,CACtCA,EAAW,eAAe,KAAK,IAAI,aAAa,CAAC,CACnD,CAUA,OAAO,YAAYC,EAA8C,CAC/D,IAAMC,EAAQD,EAAa,iBAAiB,EAC5C,OAAO,IAAIf,EAAiBgB,CAAK,CACnC,CAaA,OAAO,YAAYC,EAA4D,CAC7E,OAAOA,aAAqBjB,CAC9B,CAYA,OAAO,WAAWiB,EAAqD,CACrE,MAAO,QAASA,GAAcA,EAAU,KAAa,MAAM,SAAWjB,EAAiB,MACzF,CACF,EAjKaA,EAMK,OAAiB,GAN5B,IAAMkB,EAANlB,EAwKMmB,EAAN,MAAMA,UAA0BC,CAAmC,CAoCxE,YAAYlB,EAAoBmB,EAAkB,CAChD,MAAM,EAEN,IAAMC,EAAgBC,EAAW,cAAcrB,YAAsCmB,CAAM,EAC3F,GAAIC,EAAc,aAAa,EAAE,SAAWH,EAAkB,OAC5D,MAAM,IAAI,MAAM,+BAA+BA,EAAkB,MAAM,EAAE,EAI3E,KAAK,WAAaG,CACpB,CASA,OAAO,UAA8B,CACnC,IAAME,EAAUZ,EAAQ,MAAM,iBAAiB,EAC/C,OAAO,IAAIO,EAAkBK,EAAS,EAAK,CAC7C,CAcA,OAAO,mBAAmBC,EAAcC,EAAsC,CAC5E,GAAI,CAACC,EAAoBF,CAAI,EAC3B,MAAM,IAAI,MAAM,2BAA2BA,CAAI,EAAE,EAEnD,OAAON,EAAkB,wBAAwBM,EAAMG,EAAeF,CAAS,CAAC,CAClF,CAcA,OAAe,wBAAwBD,EAAcI,EAAkBC,EAASC,EAAoC,CAClH,GAAM,CAAE,IAAAC,EAAK,UAAAC,CAAU,EAAIC,EAAUf,EAAkB,eAAgBU,CAAI,EAErEM,EAAWC,EAAUX,CAAI,EAAE,IAAKY,GAAO,SAASA,EAAI,EAAE,CAAC,EAGvD,CAAE,IAAKC,CAAW,EAAIH,EAAS,OAAO,CAACI,EAAYC,IAAYC,EAAQF,EAAYC,EAAUV,CAAM,EAAG,CAC1G,IAAAE,EACA,UAAAC,CACF,CAAC,EACD,OAAO,IAAId,EAAkBmB,EAAY,EAAK,CAChD,CAaA,WAA8B,CAC5B,IAAMtB,EAAQJ,EAAQ,aAAa,KAAK,WAAW,aAAa,CAAC,EACjE,OAAO,IAAIM,EAAiBF,CAAK,CACnC,CAWA,KAAKV,EAAqC,CACxC,IAAMoC,EAAgBlC,EAAsBF,CAAO,EAC7CG,EAAeL,EAAI,aAAasC,CAAa,EAAE,aAAa,EAC5DhC,EAAiBE,EAAQ,KAAKH,EAAc,KAAK,WAAW,aAAa,CAAC,EAChF,OAAO,IAAIkC,EAAiBjC,CAAc,CAC5C,CASA,cAA2B,CACzB,OAAO,KAAK,WAAW,aAAa,CACtC,CASA,UAAmB,CACjB,OAAO,KAAK,cAAc,CAC5B,CAOA,aAAsB,CACpB,OAAO,KAAK,WAAW,SAAS,CAClC,CASA,eAAwB,CACtB,OAAOa,EAAW,iBAAiB,KAAK,WAAW,SAAS,WAA6B,CAC3F,CAMA,UAAUT,EAA8B,CACtCA,EAAW,eAAe,KAAK,aAAa,CAAC,CAC/C,CAEA,OAAO,YAAYC,EAA+C,CAChE,IAAMC,EAAQD,EAAa,iBAAiB,EAC5C,OAAO,IAAII,EAAkBH,EAAO,EAAK,CAC3C,CAcA,OAAO,aAAasB,EAAyD,CAC3E,OAAOA,aAAsBnB,CAC/B,CACF,EA9MaA,EAMK,OAAiB,GANtBA,EAcK,eAAiB,eAd5B,IAAMyB,EAANzB,EAqNM0B,EAAN,MAAMA,UAAyBC,CAAU,CAkB9C,YAAY5C,EAAoB,CAC9B,MAAM,EACN,IAAM6C,EAAO3C,EAAI,aAAaF,CAAQ,EACtC,GAAI6C,EAAK,aAAa,EAAE,SAAWF,EAAiB,OAClD,MAAM,IAAI,MAAM,8BAA8BA,EAAiB,MAAM,EAAE,EAEzE,KAAK,KAAOE,CACd,CAMA,cAA2B,CACzB,OAAO,KAAK,KAAK,aAAa,CAChC,CAMA,UAAUjC,EAA8B,CACtCA,EAAW,eAAe,KAAK,KAAK,aAAa,CAAC,CACpD,CAEA,OAAO,YAAYC,EAA8C,CAC/D,IAAMC,EAAQD,EAAa,iBAAiB,EAC5C,OAAO,IAAI8B,EAAiB7B,CAAK,CACnC,CAGF,EAjDa6B,EAMK,OAAS,GANpB,IAAMF,EAANE", "names": ["ed25519", "L", "isCanonicalEd25519Signature", "signature", "s", "i", "_Ed25519PublicKey", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>", "hexInput", "hex", "Hex", "args", "message", "messageToVerify", "convertSigningMessage", "messageBytes", "signatureBytes", "publicKeyBytes", "ed25519", "AuthenticationKey", "serializer", "deserializer", "bytes", "public<PERSON>ey", "Ed25519PublicKey", "_Ed25519PrivateKey", "Serializable", "strict", "privateKeyHex", "Private<PERSON><PERSON>", "keyPair", "path", "mnemonics", "isValidHardenedPath", "mnemonicToSeed", "seed", "offset", "HARDENED_OFFSET", "key", "chainCode", "<PERSON><PERSON><PERSON>", "segments", "splitPath", "el", "privateKey", "parentKeys", "segment", "CKDPriv", "messageToSign", "Ed25519Signature", "Ed25519PrivateKey", "_Ed25519Signature", "Signature", "data"]}