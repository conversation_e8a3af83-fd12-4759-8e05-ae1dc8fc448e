"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenPrice = void 0;
const utils_1 = require("@injectivelabs/utils");
const networks_1 = require("@injectivelabs/networks");
const ASSET_PRICE_SERVICE_URL = 'https://k8s.mainnet.asset.injective.network/asset-price/v1';
const TESTNET_ASSET_PRICE_SERVICE_URL = 'https://k8s.testnet.asset.injective.network/asset-price/v1';
const DEVNET_ASSET_PRICE_SERVICE_URL = 'https://devnet.asset.injective.dev/asset-price/v1';
const getAssetMicroserviceEndpoint = (network = networks_1.Network.Mainnet) => {
    if ((0, networks_1.isTestnet)(network)) {
        return TESTNET_ASSET_PRICE_SERVICE_URL;
    }
    if ((0, networks_1.isDevnet)(network)) {
        return DEVNET_ASSET_PRICE_SERVICE_URL;
    }
    return ASSET_PRICE_SERVICE_URL;
};
class TokenPrice {
    client;
    constructor(network) {
        this.client = new utils_1.HttpRestClient(getAssetMicroserviceEndpoint(network));
    }
    async fetchUsdTokensPriceMap() {
        const response = await this.client.retry(() => this.client.get(`denoms?withPrice=true`));
        const tokenPriceMap = Object.values(response.data).reduce((prices, tokenWithPrice) => {
            const id = tokenWithPrice.coingecko_id || tokenWithPrice.denom;
            if (prices[id]) {
                return prices;
            }
            prices[id] = tokenWithPrice.price.price;
            return prices;
        }, {});
        return tokenPriceMap;
    }
}
exports.TokenPrice = TokenPrice;
