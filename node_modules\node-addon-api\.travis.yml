language: c++
compiler:
  - clang
  - gcc
# For Linux, use an Ubuntu 14 image
dist: trusty
os:
  - linux
  - osx
env:
  global:
    # https://github.com/jasongin/nvs/blob/master/doc/CI.md
    - NVS_VERSION=1.4.2
  matrix:
    - NODEJS_VERSION=node/6
    - NODEJS_VERSION=node/8
    - NODEJS_VERSION=node/10
    - NODEJS_VERSION=node/12
    - NODEJS_VERSION=node/13
    - NODEJS_VERSION=nightly
matrix:
  fast_finish: true
  allow_failures:
    - env: NODEJS_VERSION=nightly
    - env: NODEJS_VERSION=node/6
sudo: false
cache:
  directories:
    - node_modules
    - $HOME/.npm
addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - g++-4.9
before_install:
  # coveralls
  - pip2 install --user cpp-coveralls
  # compilers
  - if [ "$CXX" = "g++" -a "$TRAVIS_OS_NAME" = "linux" ]; then export CXX="g++-4.9" CC="gcc-4.9" AR="gcc-ar-4.9" RANLIB="gcc-ranlib-4.9" NM="gcc-nm-4.9" ; fi
  - if [ "$CXX" = "clang++" ]; then export NPMOPT=--clang=1 ; fi
  - export CFLAGS="$CFLAGS -O3 --coverage" LDFLAGS="$LDFLAGS --coverage"
  - echo "CFLAGS=\"$CFLAGS\" LDFLAGS=\"$LDFLAGS\""
  # nvs
  - git clone --branch v$NVS_VERSION --depth 1 https://github.com/jasongin/nvs ~/.nvs
  - . ~/.nvs/nvs.sh
  - nvs --version
  # node.js
  - nvs add $NODEJS_VERSION
  - nvs use $NODEJS_VERSION
  - node --version
  - npm --version
install:
  - npm install $NPMOPT
script:
  # Travis CI sets NVM_NODEJS_ORG_MIRROR, but it makes node-gyp fail to download headers for nightly builds.
  - unset NVM_NODEJS_ORG_MIRROR

  - npm test
after_success:
  - cpp-coveralls --gcov-options '\-lp' --build-root test/build --exclude test
