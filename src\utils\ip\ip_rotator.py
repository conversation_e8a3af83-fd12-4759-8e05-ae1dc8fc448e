#!/usr/bin/env python3
"""
IP轮换器命令行工具
"""

import sys
import time
import signal
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.utils.ip.ip_manager import IPManager

class IPRotator:
    """IP轮换器"""
    
    def __init__(self):
        self.ip_manager = None
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/ip_rotator.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def start(self, config_path: str = "config/ip.yaml"):
        """
        启动IP轮换
        
        Args:
            config_path: 配置文件路径
        """
        try:
            self.logger.info("正在启动IP轮换器...")
            
            # 创建IP管理器
            self.ip_manager = IPManager(config_path)
            
            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 启动轮换
            self.ip_manager.start_rotation()
            
            self.logger.info("IP轮换器已启动，按 Ctrl+C 停止")
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
                
        except Exception as e:
            self.logger.error(f"启动IP轮换器失败: {e}")
            sys.exit(1)
    
    def stop(self):
        """停止IP轮换"""
        if self.ip_manager:
            self.logger.info("正在停止IP轮换器...")
            self.ip_manager.stop_rotation()
            self.logger.info("IP轮换器已停止")
    
    def status(self, config_path: str = "config/ip.yaml"):
        """
        显示状态
        
        Args:
            config_path: 配置文件路径
        """
        try:
            ip_manager = IPManager(config_path)
            status = ip_manager.get_status()
            
            print("=== IP轮换器状态 ===")
            print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
            print(f"当前代理: {status['current_proxy'] or '无'}")
            print(f"当前IP: {status['current_ip'] or '未知'}")
            print(f"上次轮换时间: {status['last_rotation_time'] or '无'}")
            print(f"代理节点总数: {status['total_proxies']}")
            print(f"轮换间隔: {status['rotation_interval']} 秒")
            
        except Exception as e:
            self.logger.error(f"获取状态失败: {e}")
            sys.exit(1)
    
    def test(self, config_path: str = "config/ip.yaml"):
        """
        测试代理连接
        
        Args:
            config_path: 配置文件路径
        """
        try:
            self.logger.info("正在测试代理连接...")
            
            ip_manager = IPManager(config_path)
            
            print("=== 代理连接测试 ===")
            success_count = 0
            total_count = len(ip_manager.proxy_list)
            
            for i, proxy in enumerate(ip_manager.proxy_list, 1):
                print(f"[{i}/{total_count}] 测试 {proxy['name']}...", end=' ')
                
                if ip_manager._test_proxy_connection(proxy):
                    print("✓ 成功")
                    success_count += 1
                else:
                    print("✗ 失败")
            
            print(f"\n测试完成: {success_count}/{total_count} 个代理可用")
            
        except Exception as e:
            self.logger.error(f"测试代理失败: {e}")
            sys.exit(1)
    
    def switch(self, proxy_name: str = None, config_path: str = "config/ip.yaml"):
        """
        手动切换代理
        
        Args:
            proxy_name: 代理名称，如果为None则随机选择
            config_path: 配置文件路径
        """
        try:
            ip_manager = IPManager(config_path)
            
            if proxy_name:
                # 查找指定的代理
                target_proxy = None
                for proxy in ip_manager.proxy_list:
                    if proxy_name in proxy['name']:
                        target_proxy = proxy
                        break
                
                if not target_proxy:
                    print(f"未找到包含 '{proxy_name}' 的代理")
                    return
                    
                print(f"正在切换到代理: {target_proxy['name']}")
                if ip_manager.switch_proxy(target_proxy):
                    print("切换成功")
                else:
                    print("切换失败")
            else:
                print("正在随机切换代理...")
                if ip_manager.switch_proxy():
                    print(f"切换成功，当前代理: {ip_manager.current_proxy['name']}")
                else:
                    print("切换失败")
                    
        except Exception as e:
            self.logger.error(f"切换代理失败: {e}")
            sys.exit(1)
    
    def list_proxies(self, config_path: str = "config/ip.yaml"):
        """
        列出所有代理
        
        Args:
            config_path: 配置文件路径
        """
        try:
            ip_manager = IPManager(config_path)
            
            print("=== 可用代理列表 ===")
            for i, proxy in enumerate(ip_manager.proxy_list, 1):
                print(f"{i:2d}. {proxy['name']}")
                print(f"    服务器: {proxy['server']}:{proxy['port']}")
                print(f"    类型: {proxy['type']}")
                print()
                
        except Exception as e:
            self.logger.error(f"列出代理失败: {e}")
            sys.exit(1)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止...")
        self.stop()
        sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='IP轮换器')
    parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 启动命令
    start_parser = subparsers.add_parser('start', help='启动IP轮换')
    start_parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    # 停止命令
    stop_parser = subparsers.add_parser('stop', help='停止IP轮换')

    # 状态命令
    status_parser = subparsers.add_parser('status', help='显示状态')
    status_parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    # 测试命令
    test_parser = subparsers.add_parser('test', help='测试代理连接')
    test_parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    # 切换命令
    switch_parser = subparsers.add_parser('switch', help='手动切换代理')
    switch_parser.add_argument('--name', '-n', help='代理名称（部分匹配）')
    switch_parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    # 列表命令
    list_parser = subparsers.add_parser('list', help='列出所有代理')
    list_parser.add_argument('--config', '-c', default='config/ip.yaml', help='配置文件路径')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 获取配置文件路径
    config_path = getattr(args, 'config', 'config/ip.yaml')

    rotator = IPRotator()

    if args.command == 'start':
        rotator.start(config_path)
    elif args.command == 'stop':
        rotator.stop()
    elif args.command == 'status':
        rotator.status(config_path)
    elif args.command == 'test':
        rotator.test(config_path)
    elif args.command == 'switch':
        rotator.switch(getattr(args, 'name', None), config_path)
    elif args.command == 'list':
        rotator.list_proxies(config_path)

if __name__ == '__main__':
    main()
