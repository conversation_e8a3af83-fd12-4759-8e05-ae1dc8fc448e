import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.auction.v1beta1";
export interface Params {
    /** auction_period_duration defines the auction period duration */
    auctionPeriod: string;
    /** min_next_bid_increment_rate defines the minimum increment rate for new bids */
    minNextBidIncrementRate: string;
    /**
     * inj_basket_max_cap defines the maximum cap for INJ contained in an auction
     * basket
     */
    injBasketMaxCap: string;
}
export interface Bid {
    bidder: string;
    amount: Coin | undefined;
}
export interface LastAuctionResult {
    /** winner describes the address of the winner */
    winner: string;
    /** amount describes the amount the winner get from the auction */
    amount: Coin | undefined;
    /** round defines the round number of auction */
    round: string;
}
export interface EventBid {
    /** bidder describes the address of bidder */
    bidder: string;
    /** amount describes the amount the bidder put on the auction */
    amount: Coin | undefined;
    /** round defines the round number of auction */
    round: string;
}
export interface EventAuctionResult {
    /** winner describes the address of the winner */
    winner: string;
    /** amount describes the amount the winner get from the auction */
    amount: Coin | undefined;
    /** round defines the round number of auction */
    round: string;
}
export interface EventAuctionStart {
    /** round defines the round number of auction */
    round: string;
    /** ending_timestamp describes auction end time */
    endingTimestamp: string;
    /**
     * new_basket describes auction module balance at the time of new auction
     * start
     */
    newBasket: Coin[];
}
export declare const Params: {
    encode(message: Params, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Params;
    fromJSON(object: any): Params;
    toJSON(message: Params): unknown;
    create(base?: DeepPartial<Params>): Params;
    fromPartial(object: DeepPartial<Params>): Params;
};
export declare const Bid: {
    encode(message: Bid, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Bid;
    fromJSON(object: any): Bid;
    toJSON(message: Bid): unknown;
    create(base?: DeepPartial<Bid>): Bid;
    fromPartial(object: DeepPartial<Bid>): Bid;
};
export declare const LastAuctionResult: {
    encode(message: LastAuctionResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LastAuctionResult;
    fromJSON(object: any): LastAuctionResult;
    toJSON(message: LastAuctionResult): unknown;
    create(base?: DeepPartial<LastAuctionResult>): LastAuctionResult;
    fromPartial(object: DeepPartial<LastAuctionResult>): LastAuctionResult;
};
export declare const EventBid: {
    encode(message: EventBid, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBid;
    fromJSON(object: any): EventBid;
    toJSON(message: EventBid): unknown;
    create(base?: DeepPartial<EventBid>): EventBid;
    fromPartial(object: DeepPartial<EventBid>): EventBid;
};
export declare const EventAuctionResult: {
    encode(message: EventAuctionResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAuctionResult;
    fromJSON(object: any): EventAuctionResult;
    toJSON(message: EventAuctionResult): unknown;
    create(base?: DeepPartial<EventAuctionResult>): EventAuctionResult;
    fromPartial(object: DeepPartial<EventAuctionResult>): EventAuctionResult;
};
export declare const EventAuctionStart: {
    encode(message: EventAuctionStart, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAuctionStart;
    fromJSON(object: any): EventAuctionStart;
    toJSON(message: EventAuctionStart): unknown;
    create(base?: DeepPartial<EventAuctionStart>): EventAuctionStart;
    fromPartial(object: DeepPartial<EventAuctionStart>): EventAuctionStart;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
