import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { BalancesResponse, DenomBalance, DenomOwnersResponse } from './../types/bank-rest.js';
/**
 * @category Chain Rest API
 */
export declare class ChainRestBankApi extends BaseRestConsumer {
    /**
     * Get address's balance
     *
     * @param address address of account to look up
     */
    fetchBalances(address: string, params?: Record<string, any>): Promise<BalancesResponse>;
    /**
     * Get address's balances
     *
     * @param address address of account to look up
     */
    fetchBalance(address: string, denom: string, params?: Record<string, any>): Promise<DenomBalance>;
    fetchDenomOwners(denom: string, params?: Record<string, any>): Promise<DenomOwnersResponse['denom_owners']>;
}
