"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }var N=Object.defineProperty;var k=Object.getOwnPropertyDescriptor;var h=(r,e,t,n)=>{for(var s=n>1?void 0:n?k(e,t):e,o=r.length-1,c;o>=0;o--)(c=r[o])&&(s=(n?c(e,t,s):c(s))||s);return n&&s&&N(e,t,s),s};var d=class extends Error{constructor(e,t){super(e),this.invalidReason=t}};var _utils = require('@noble/hashes/utils');var H=(n=>(n.TOO_SHORT="too_short",n.INVALID_LENGTH="invalid_length",n.INVALID_HEX_CHARS="invalid_hex_chars",n))(H||{}),_= exports.j =class r{constructor(e){this.data=e}toUint8Array(){return this.data}toStringWithoutPrefix(){return _utils.bytesToHex.call(void 0, this.data)}toString(){return`0x${this.toStringWithoutPrefix()}`}static fromHexString(e){let t=e;if(t.startsWith("0x")&&(t=t.slice(2)),t.length===0)throw new d("Hex string is too short, must be at least 1 char long, excluding the optional leading 0x.","too_short");if(t.length%2!==0)throw new d("Hex string must be an even number of hex characters.","invalid_length");try{return new r(_utils.hexToBytes.call(void 0, t))}catch(n){throw new d(`Hex string contains invalid hex characters: ${_optionalChain([n, 'optionalAccess', _2 => _2.message])}`,"invalid_hex_chars")}}static fromHexInput(e){return e instanceof Uint8Array?new r(e):r.fromHexString(e)}static hexInputToUint8Array(e){return e instanceof Uint8Array?e:r.fromHexString(e).toUint8Array()}static hexInputToString(e){return r.fromHexInput(e).toString()}static hexInputToStringWithoutPrefix(e){return r.fromHexInput(e).toStringWithoutPrefix()}static isValid(e){try{return r.fromHexString(e),{valid:!0}}catch(t){return{valid:!1,invalidReason:_optionalChain([t, 'optionalAccess', _3 => _3.invalidReason]),invalidReasonMessage:_optionalChain([t, 'optionalAccess', _4 => _4.message])}}}equals(e){return this.data.length!==e.data.length?!1:this.data.every((t,n)=>t===e.data[n])}},ye= exports.k =r=>new TextDecoder().decode(_.fromHexInput(r).toUint8Array());var T=255,U= exports.c =65535,x= exports.d =4294967295,b= exports.e =18446744073709551615n,A= exports.f =340282366920938463463374607431768211455n,v= exports.g =115792089237316195423570985008687907853269984665640564039457584007913129639935n;var m=class{bcsToBytes(){let e=new l;return this.serialize(e),e.toUint8Array()}bcsToHex(){let e=this.bcsToBytes();return _.fromHexInput(e)}toStringWithoutPrefix(){return this.bcsToHex().toStringWithoutPrefix()}toString(){return`0x${this.toStringWithoutPrefix()}`}},l= exports.m =class{constructor(e=64){if(e<=0)throw new Error("Length needs to be greater than 0");this.buffer=new ArrayBuffer(e),this.offset=0}ensureBufferWillHandleSize(e){for(;this.buffer.byteLength<this.offset+e;){let t=new ArrayBuffer(this.buffer.byteLength*2);new Uint8Array(t).set(new Uint8Array(this.buffer)),this.buffer=t}}appendToBuffer(e){this.ensureBufferWillHandleSize(e.length),new Uint8Array(this.buffer,this.offset).set(e),this.offset+=e.length}serializeWithFunction(e,t,n){this.ensureBufferWillHandleSize(t);let s=new DataView(this.buffer,this.offset);e.apply(s,[0,n,!0]),this.offset+=t}serializeStr(e){let t=new TextEncoder;this.serializeBytes(t.encode(e))}serializeBytes(e){this.serializeU32AsUleb128(e.length),this.appendToBuffer(e)}serializeFixedBytes(e){this.appendToBuffer(e)}serializeBool(e){O(e);let t=e?1:0;this.appendToBuffer(new Uint8Array([t]))}serializeU8(e){this.appendToBuffer(new Uint8Array([e]))}serializeU16(e){this.serializeWithFunction(DataView.prototype.setUint16,2,e)}serializeU32(e){this.serializeWithFunction(DataView.prototype.setUint32,4,e)}serializeU64(e){let t=BigInt(e)&BigInt(x),n=BigInt(e)>>BigInt(32);this.serializeU32(Number(t)),this.serializeU32(Number(n))}serializeU128(e){let t=BigInt(e)&b,n=BigInt(e)>>BigInt(64);this.serializeU64(t),this.serializeU64(n)}serializeU256(e){let t=BigInt(e)&A,n=BigInt(e)>>BigInt(128);this.serializeU128(t),this.serializeU128(n)}serializeU32AsUleb128(e){let t=e,n=[];for(;t>>>7;)n.push(t&127|128),t>>>=7;n.push(t),this.appendToBuffer(new Uint8Array(n))}toUint8Array(){return new Uint8Array(this.buffer).slice(0,this.offset)}serialize(e){e.serialize(this)}serializeVector(e){this.serializeU32AsUleb128(e.length),e.forEach(t=>{t.serialize(this)})}serializeOption(e,t){let n=e!==void 0;this.serializeBool(n),n&&(typeof e=="string"?this.serializeStr(e):e instanceof Uint8Array?t!==void 0?this.serializeFixedBytes(e):this.serializeBytes(e):e.serialize(this))}serializeOptionStr(e){e===void 0?this.serializeU32AsUleb128(0):(this.serializeU32AsUleb128(1),this.serializeStr(e))}};h([f(0,T)],l.prototype,"serializeU8",1),h([f(0,U)],l.prototype,"serializeU16",1),h([f(0,x)],l.prototype,"serializeU32",1),h([f(BigInt(0),b)],l.prototype,"serializeU64",1),h([f(BigInt(0),A)],l.prototype,"serializeU128",1),h([f(BigInt(0),v)],l.prototype,"serializeU256",1),h([f(0,x)],l.prototype,"serializeU32AsUleb128",1);function O(r){if(typeof r!="boolean")throw new Error(`${r} is not a boolean value`)}var D=(r,e,t)=>`${r} is out of range: [${e}, ${t}]`;function W(r,e,t){let n=BigInt(r);if(n>BigInt(t)||n<BigInt(e))throw new Error(D(r,e,t))}function f(r,e){return(t,n,s)=>{let o=s.value;return s.value=function(g){return W(g,r,e),o.apply(this,[g])},s}}var F=(s=>(s.JSON="application/json",s.BCS="application/x-bcs",s.BCS_SIGNED_TRANSACTION="application/x.aptos.signed_transaction+bcs",s.BCS_VIEW_FUNCTION="application/x.aptos.view_function+bcs",s))(F||{}),z= exports.r =(a=>(a[a.Bool=0]="Bool",a[a.U8=1]="U8",a[a.U64=2]="U64",a[a.U128=3]="U128",a[a.Address=4]="Address",a[a.Signer=5]="Signer",a[a.Vector=6]="Vector",a[a.Struct=7]="Struct",a[a.U16=8]="U16",a[a.U32=9]="U32",a[a.U256=10]="U256",a[a.Reference=254]="Reference",a[a.Generic=255]="Generic",a))(z||{}),I= exports.s =(p=>(p[p.U8=0]="U8",p[p.U64=1]="U64",p[p.U128=2]="U128",p[p.Address=3]="Address",p[p.U8Vector=4]="U8Vector",p[p.Bool=5]="Bool",p[p.U16=6]="U16",p[p.U32=7]="U32",p[p.U256=8]="U256",p[p.Serialized=9]="Serialized",p))(I||{}),P= exports.t =(n=>(n[n.Script=0]="Script",n[n.EntryFunction=2]="EntryFunction",n[n.Multisig=3]="Multisig",n))(P||{}),G= exports.u =(t=>(t[t.MultiAgentTransaction=0]="MultiAgentTransaction",t[t.FeePayerTransaction=1]="FeePayerTransaction",t))(G||{}),L= exports.v =(o=>(o[o.Ed25519=0]="Ed25519",o[o.MultiEd25519=1]="MultiEd25519",o[o.MultiAgent=2]="MultiAgent",o[o.FeePayer=3]="FeePayer",o[o.SingleSender=4]="SingleSender",o))(L||{}),$= exports.w =(c=>(c[c.Ed25519=0]="Ed25519",c[c.MultiEd25519=1]="MultiEd25519",c[c.SingleKey=2]="SingleKey",c[c.MultiKey=3]="MultiKey",c[c.NoAccountAuthenticator=4]="NoAccountAuthenticator",c[c.Abstraction=5]="Abstraction",c))($||{}),q= exports.x =(t=>(t.Ed25519="ed25519",t.Secp256k1="secp256k1",t))(q||{}),X= exports.y =(s=>(s[s.Ed25519=0]="Ed25519",s[s.Secp256k1=1]="Secp256k1",s[s.Keyless=3]="Keyless",s[s.FederatedKeyless=4]="FederatedKeyless",s))(X||{}),Y= exports.z =(n=>(n[n.Ed25519=0]="Ed25519",n[n.Secp256k1=1]="Secp256k1",n[n.Keyless=3]="Keyless",n))(Y||{}),j= exports.A =(e=>(e[e.Ed25519=0]="Ed25519",e))(j||{}),Z= exports.B =(e=>(e[e.Ed25519=0]="Ed25519",e))(Z||{}),Q= exports.C =(e=>(e[e.ZkProof=0]="ZkProof",e))(Q||{}),K= exports.D =(e=>(e[e.Groth16=0]="Groth16",e))(K||{}),J= exports.E =(g=>(g.Pending="pending_transaction",g.User="user_transaction",g.Genesis="genesis_transaction",g.BlockMetadata="block_metadata_transaction",g.StateCheckpoint="state_checkpoint_transaction",g.Validator="validator_transaction",g.BlockEpilogue="block_epilogue_transaction",g))(J||{});function Ae(r){return r.type==="pending_transaction"}function Se(r){return r.type==="user_transaction"}function Re(r){return r.type==="genesis_transaction"}function Te(r){return r.type==="block_metadata_transaction"}function Ue(r){return r.type==="state_checkpoint_transaction"}function ve(r){return r.type==="validator_transaction"}function Ie(r){return r.type==="block_epilogue_transaction"}function Me(r){return"signature"in r&&r.signature==="ed25519_signature"}function Ee(r){return"signature"in r&&r.signature==="secp256k1_ecdsa_signature"}function Be(r){return r.type==="multi_agent_signature"}function Ne(r){return r.type==="fee_payer_signature"}function ke(r){return r.type==="multi_ed25519_signature"}var V=(n=>(n.PRIVATE="private",n.PUBLIC="public",n.FRIEND="friend",n))(V||{}),ee= exports.S =(s=>(s.STORE="store",s.DROP="drop",s.KEY="key",s.COPY="copy",s))(ee||{}),te= exports.T =(t=>(t.VALIDATOR="validator",t.FULL_NODE="full_node",t))(te||{}),re= exports.U =(s=>(s[s.Ed25519=0]="Ed25519",s[s.MultiEd25519=1]="MultiEd25519",s[s.SingleKey=2]="SingleKey",s[s.MultiKey=3]="MultiKey",s))(re||{}),ne= exports.V =(t=>(t[t.Ed25519=0]="Ed25519",t[t.Secp256k1Ecdsa=2]="Secp256k1Ecdsa",t))(ne||{}),M= exports.W =(o=>(o[o.DeriveAuid=251]="DeriveAuid",o[o.DeriveObjectAddressFromObject=252]="DeriveObjectAddressFromObject",o[o.DeriveObjectAddressFromGuid=253]="DeriveObjectAddressFromGuid",o[o.DeriveObjectAddressFromSeed=254]="DeriveObjectAddressFromSeed",o[o.DeriveResourceAccountAddress=255]="DeriveResourceAccountAddress",o))(M||{});var oe=(y=>(y.INCORRECT_NUMBER_OF_BYTES="incorrect_number_of_bytes",y.INVALID_HEX_CHARS="invalid_hex_chars",y.TOO_SHORT="too_short",y.TOO_LONG="too_long",y.LEADING_ZERO_X_REQUIRED="leading_zero_x_required",y.LONG_FORM_REQUIRED_UNLESS_SPECIAL="long_form_required_unless_special",y.INVALID_PADDING_ZEROES="INVALID_PADDING_ZEROES",y.INVALID_PADDING_STRICTNESS="INVALID_PADDING_STRICTNESS",y))(oe||{}),i=class i extends m{constructor(e){if(super(),e.length!==i.LENGTH)throw new d("AccountAddress data should be exactly 32 bytes long","incorrect_number_of_bytes");this.data=e}isSpecial(){return this.data.slice(0,this.data.length-1).every(e=>e===0)&&this.data[this.data.length-1]<16}toString(){return`0x${this.toStringWithoutPrefix()}`}toStringWithoutPrefix(){let e=_utils.bytesToHex.call(void 0, this.data);return this.isSpecial()&&(e=e[e.length-1]),e}toStringLong(){return`0x${this.toStringLongWithoutPrefix()}`}toStringLongWithoutPrefix(){return _utils.bytesToHex.call(void 0, this.data)}toStringShort(){return`0x${this.toStringShortWithoutPrefix()}`}toStringShortWithoutPrefix(){let e=_utils.bytesToHex.call(void 0, this.data).replace(/^0+/,"");return e===""?"0":e}toUint8Array(){return this.data}serialize(e){e.serializeFixedBytes(this.data)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(3),e.serialize(this)}static deserialize(e){let t=e.deserializeFixedBytes(i.LENGTH);return new i(t)}static fromStringStrict(e){if(!e.startsWith("0x"))throw new d("Hex string must start with a leading 0x.","leading_zero_x_required");let t=i.fromString(e);if(e.length!==i.LONG_STRING_LENGTH+2)if(t.isSpecial()){if(e.length!==3)throw new d(`The given hex string ${e} is a special address not in LONG form, it must be 0x0 to 0xf without padding zeroes.`,"INVALID_PADDING_ZEROES")}else throw new d(`The given hex string ${e} is not a special address, it must be represented as 0x + 64 chars.`,"long_form_required_unless_special");return t}static fromString(e,{maxMissingChars:t=4}={}){let n=e;if(e.startsWith("0x")&&(n=e.slice(2)),n.length===0)throw new d("Hex string is too short, must be 1 to 64 chars long, excluding the leading 0x.","too_short");if(n.length>64)throw new d("Hex string is too long, must be 1 to 64 chars long, excluding the leading 0x.","too_long");if(t>63||t<0)throw new d(`maxMissingChars must be between or equal to 0 and 63. Received ${t}`,"INVALID_PADDING_STRICTNESS");let s;try{s=_utils.hexToBytes.call(void 0, n.padStart(64,"0"))}catch(c){throw new d(`Hex characters are invalid: ${_optionalChain([c, 'optionalAccess', _5 => _5.message])}`,"invalid_hex_chars")}let o=new i(s);if(n.length<64-t&&!o.isSpecial())throw new d(`Hex string is too short, must be ${64-t} to 64 chars long, excluding the leading 0x. You may need to fix 
the addresss by padding it with 0s before passing it to \`fromString\` (e.g. <addressString>.padStart(64, '0')). 
Received ${e}`,"too_short");return o}static from(e,{maxMissingChars:t=4}={}){return typeof e=="string"?i.fromString(e,{maxMissingChars:t}):e instanceof Uint8Array?new i(e):e}static fromStrict(e){return typeof e=="string"?i.fromStringStrict(e):e instanceof Uint8Array?new i(e):e}static isValid(e){try{return e.strict?i.fromStrict(e.input):i.from(e.input),{valid:!0}}catch(t){return{valid:!1,invalidReason:_optionalChain([t, 'optionalAccess', _6 => _6.invalidReason]),invalidReasonMessage:_optionalChain([t, 'optionalAccess', _7 => _7.message])}}}equals(e){return this.data.length!==e.data.length?!1:this.data.every((t,n)=>t===e.data[n])}};i.LENGTH=32,i.LONG_STRING_LENGTH=64,i.ZERO=i.from("0x0"),i.ONE=i.from("0x1"),i.TWO=i.from("0x2"),i.THREE=i.from("0x3"),i.FOUR=i.from("0x4"),i.A=i.from("0xA");var u=i;var _sha3 = require('@noble/hashes/sha3');var R=(r,e)=>{let t=r.bcsToBytes(),n=typeof e=="string"?Buffer.from(e,"utf8"):e,s=new Uint8Array([...t,...n,254]);return new u(_sha3.sha3_256.call(void 0, s))},je= exports._ =(r,e)=>{let t=r.bcsToBytes(),n=typeof e=="string"?Buffer.from(e,"utf8"):e,s=new Uint8Array([...t,...n,255]);return new u(_sha3.sha3_256.call(void 0, s))},Ze= exports.$ =(r,e,t)=>{let n=`${e}::${t}`;return R(r,n)};var _jsbase64 = require('js-base64');async function et(r){return new Promise(e=>{setTimeout(e,r)})}function tt(r){return r instanceof Error?r.message:String(r)}var rt=()=>Math.floor(Date.now()/1e3);function nt(r){let e=new Date(r*1e3);return e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),Math.floor(e.getTime()/1e3)}function st(r){let e=r.replace(/-/g,"+").replace(/_/g,"/"),t=e+"==".substring(0,(3-e.length%3)%3);return _jsbase64.decode.call(void 0, t)}function ot(r){let e=r.replace(/-/g,"+").replace(/_/g,"/");for(;e.length%4!==0;)e+="=";return new Uint8Array(Buffer.from(e,"base64"))}var it=(r,e)=>r*10**e,at= exports.ha =(r,e)=>r/10**e,B=r=>{let e="";for(let t=2;t<r.length;t+=2)e+=String.fromCharCode(parseInt(r.substring(t,t+2),16));return e},pt= exports.ia =r=>{let{account_address:e,module_name:t,struct_name:n}=r,s=B(t),o=B(n);return`${e}::${s}::${o}`},ct= exports.ja =r=>typeof r=="object"&&!Array.isArray(r)&&r!==null&&"account_address"in r&&"module_name"in r&&"struct_name"in r&&typeof r.account_address=="string"&&typeof r.module_name=="string"&&typeof r.struct_name=="string";function dt(r){let e=r.split("::");if(e.length!==3)throw new Error(`Invalid function ${r}`);let t=e[0],n=e[1],s=e[2];return{moduleAddress:t,moduleName:n,functionName:s}}function ut(r){let e=r.split("::");return e.length===3&&u.isValid({input:e[0]}).valid}function gt(r,e=6,t=5){return`${r.slice(0,e)}...${r.slice(-t)}`}var ae="0x1::aptos_coin::AptosCoin",lt=u.A.toStringLong();function pe(r){let e=/0x[0-9a-fA-F]+/g;return r.replace(e,t=>u.from(t,{maxMissingChars:63}).toStringShort())}function yt(r){let e=pe(r);return e===ae?u.A:R(u.A,e)}exports.a = h; exports.b = T; exports.c = U; exports.d = x; exports.e = b; exports.f = A; exports.g = v; exports.h = d; exports.i = H; exports.j = _; exports.k = ye; exports.l = m; exports.m = l; exports.n = O; exports.o = D; exports.p = W; exports.q = F; exports.r = z; exports.s = I; exports.t = P; exports.u = G; exports.v = L; exports.w = $; exports.x = q; exports.y = X; exports.z = Y; exports.A = j; exports.B = Z; exports.C = Q; exports.D = K; exports.E = J; exports.F = Ae; exports.G = Se; exports.H = Re; exports.I = Te; exports.J = Ue; exports.K = ve; exports.L = Ie; exports.M = Me; exports.N = Ee; exports.O = Be; exports.P = Ne; exports.Q = ke; exports.R = V; exports.S = ee; exports.T = te; exports.U = re; exports.V = ne; exports.W = M; exports.X = oe; exports.Y = u; exports.Z = R; exports._ = je; exports.$ = Ze; exports.aa = et; exports.ba = tt; exports.ca = rt; exports.da = nt; exports.ea = st; exports.fa = ot; exports.ga = it; exports.ha = at; exports.ia = pt; exports.ja = ct; exports.ka = dt; exports.la = ut; exports.ma = gt; exports.na = yt;
//# sourceMappingURL=chunk-ZMDE3DNL.js.map