{"version": 3, "file": "matic.node.min.js", "mappings": "2BACA,IAAIA,EAAS,EAAQ,KACjBC,EAASD,EAAOC,OAGpB,SAASC,EAAWC,EAAKC,GACvB,IAAK,IAAIC,KAAOF,EACdC,EAAIC,GAAOF,EAAIE,EAEnB,CASA,SAASC,EAAYC,EAAKC,EAAkBC,GAC1C,OAAOR,EAAOM,EAAKC,EAAkBC,EACvC,CAVIR,EAAOS,MAAQT,EAAOU,OAASV,EAAOW,aAAeX,EAAOY,gBAC9DC,EAAOC,QAAUf,GAGjBE,EAAUF,EAAQe,GAClBA,EAAQd,OAASK,GAQnBJ,EAAUD,EAAQK,GAElBA,EAAWI,KAAO,SAAUH,EAAKC,EAAkBC,GACjD,GAAmB,iBAARF,EACT,MAAM,IAAIS,UAAU,iCAEtB,OAAOf,EAAOM,EAAKC,EAAkBC,EACvC,EAEAH,EAAWK,MAAQ,SAAUM,EAAMC,EAAMC,GACvC,GAAoB,iBAATF,EACT,MAAM,IAAID,UAAU,6BAEtB,IAAII,EAAMnB,EAAOgB,GAUjB,YATaI,IAATH,EACsB,iBAAbC,EACTC,EAAIF,KAAKA,EAAMC,GAEfC,EAAIF,KAAKA,GAGXE,EAAIF,KAAK,GAEJE,CACT,EAEAd,EAAWM,YAAc,SAAUK,GACjC,GAAoB,iBAATA,EACT,MAAM,IAAID,UAAU,6BAEtB,OAAOf,EAAOgB,EAChB,EAEAX,EAAWO,gBAAkB,SAAUI,GACrC,GAAoB,iBAATA,EACT,MAAM,IAAID,UAAU,6BAEtB,OAAOhB,EAAOsB,WAAWL,EAC3B,C,uBC7DAH,EAAOC,QAAUQ,QAAQ,S,uBCAzBT,EAAOC,QAAUQ,QAAQ,a,GCCrBC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBL,IAAjBM,EACH,OAAOA,EAAaZ,QAGrB,IAAID,EAASU,EAAyBE,GAAY,CAGjDX,QAAS,CAAC,GAOX,OAHAa,EAAoBF,GAAUZ,EAAQA,EAAOC,QAASU,GAG/CX,EAAOC,OACf,CCrBAU,EAAoBI,EAAKf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,WAC7B,IAAOjB,EAAiB,QACxB,IAAM,EAEP,OADAW,EAAoBO,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdL,EAAoBO,EAAI,CAACjB,EAASmB,KACjC,IAAI,IAAI7B,KAAO6B,EACXT,EAAoBU,EAAED,EAAY7B,KAASoB,EAAoBU,EAAEpB,EAASV,IAC5E+B,OAAOC,eAAetB,EAASV,EAAK,CAAEiC,YAAY,EAAMC,IAAKL,EAAW7B,IAE1E,ECNDoB,EAAoBU,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFhB,EAAoBoB,EAAK9B,IACH,oBAAX+B,QAA0BA,OAAOC,aAC1CX,OAAOC,eAAetB,EAAS+B,OAAOC,YAAa,CAAEC,MAAO,WAE7DZ,OAAOC,eAAetB,EAAS,aAAc,CAAEiC,OAAO,GAAO,E,sgCCF9D,I,ECHYC,ECAAC,EFGZ,EACI,SAAmBC,GAAA,KAAAA,OAAAA,CAEnB,EGFJ,aAGI,WAAmBA,GAAA,KAAAA,OAAAA,CAEnB,CAoDJ,OA5BI,YAAAC,YAAA,SAAaC,EAAoBC,GAC7B,OAAOC,KAAKC,eAAe,CACvBC,QAAS,MACTC,OAAQ,kBACRC,OAAQ,CAACC,OAAOP,GAAaO,OAAON,IACpCO,IAAI,IAAIC,MAAOC,YAChBC,MAAK,SAAAC,GACJ,OAAOC,OAAOD,EAAQE,OAC1B,GACJ,EAEA,YAAAC,qBAAA,WACI,OAAOb,KAAKC,eAAe,CACvBC,QAAS,MACTC,OAAQ,eACRC,OAAQ,GACRE,IAAI,IAAIC,MAAOC,YAChBC,MAAK,SAAAC,GACJ,OAAOA,EAAQE,MACnB,GACJ,EAQJ,EAzDA,GCDA,EAEI,SAAmBE,EAAwBlB,GAAxB,KAAAkB,QAAAA,EAAwB,KAAAlB,OAAAA,CAE3C,ECLJ,0BAiBA,QAhBW,EAAAmB,KAAP,SAAYtB,GACR,OAAOuB,IACX,EAcJ,EAjBA,G,mcCCA,cAEI,WAAYvB,G,OACR,cAAO,IACX,CAgDJ,OApDoC,OAMhC,YAAAwB,SAAA,SAASC,GACL,OAAOF,IACX,EAEA,YAAAG,SAAA,WACI,OAAOH,IACX,EAEA,YAAAI,IAAA,SAAI3B,GACA,OAAOuB,IACX,EAEA,YAAAK,IAAA,SAAI5B,GACA,OAAOuB,IACX,EAEA,YAAAM,IAAA,SAAI7B,GACA,OAAOuB,IACX,EAEA,YAAAO,IAAA,SAAI9B,GACA,OAAOuB,IACX,EAEA,YAAAQ,IAAA,SAAI/B,GACA,OAAOuB,IACX,EAEA,YAAAS,GAAA,SAAGhC,GACC,OAAOuB,IAEX,EAEA,YAAAU,IAAA,SAAIjC,GACA,OAAOuB,IAEX,EAEA,YAAAW,GAAA,SAAGlC,GACC,OAAOuB,IAEX,EAEA,YAAAY,GAAA,SAAGnC,GACC,OAAOuB,IACX,EACJ,EApDA,CAAoCa,GCApC,0BA+BA,QA9BW,EAAAC,MAAP,SAAaC,GACT,IAAMC,SAAkBD,EACxB,GAAiB,WAAbC,EACAD,EAAS,IAAIE,GAAMC,GAAGH,QACnB,GAAiB,WAAbC,EAAuB,CAC9B,GAAuC,OAAlCD,EAAkBI,MAAM,EAAG,GAC5B,OAAOJ,EAEXA,EAAS,IAAIE,GAAMC,GAAGH,E,CAE1B,GAAIE,GAAMC,GAAGnB,KAAKgB,GACd,MAAO,KAAOA,EAAOd,SAAS,IAG9B,MAAM,IAAImB,MAAM,wBAAiBL,EAAM,4BAE/C,EAEO,EAAAM,KAAP,SAAYN,GAUR,MARiB,iBADOA,GAEmB,OAAlCA,EAAkBI,MAAM,EAAG,KAC5BJ,EAASO,SAASP,EAAkB,KAGvCE,GAAMC,GAAGnB,KAAKgB,KACfA,EAAS,IAAIE,GAAMC,GAAGH,IAEnBA,CACX,EACJ,EA/BA,GCAaQ,EAAM,SAACC,G,IAAQ,wDACxB,IAAMC,EAA4C,mBAAXD,EAAwB,IAAIA,EAAWA,EAC9E,OAAOC,EAAeC,MAAK,MAApBD,E,+LAAc,EAAOE,IAAkBjC,GAAO,GACzD,ECAakC,EAAkB,SAAaC,GACxC,IAAMC,EAA+B,IAAIC,QAAQF,GAC3CG,EAAW,IAAIC,EAGrB,OAFAH,EAAQI,GAAKF,EAASE,GAAGC,KAAKH,GAC9BF,EAAQM,KAAOJ,EAASI,KAAKD,KAAKH,GAC3BF,CACX,EAEA,aAEI,WAAYO,GAMJ,KAAAC,QAEJ,CAAC,EAPDtD,KAAKuD,KAAOF,CAChB,CA0CJ,OAlCI,YAAAH,GAAA,SAAGM,EAAeC,GAKd,OAJ2B,MAAvBzD,KAAKsD,QAAQE,KACbxD,KAAKsD,QAAQE,GAAS,IAE1BxD,KAAKsD,QAAQE,GAAOE,KAAKD,GAClBzD,IACX,EAEA,YAAA2D,IAAA,SAAIH,EAAeC,GACf,GAAIzD,KAAKsD,QAAQE,GACb,GAAIC,EAAI,CACJ,IAAMG,EAAQ5D,KAAKsD,QAAQE,GAAOK,QAAQJ,GAC1CzD,KAAKsD,QAAQE,GAAOM,OAAOF,EAAO,E,MAGlC5D,KAAKsD,QAAQE,GAAS,EAGlC,EAEA,YAAAJ,KAAA,SAAKI,G,IAAL,WAAoB,oDAChB,IAAMO,EAAS/D,KAAKsD,QAAQE,IAAU,GACtC,OAAOT,QAAQiB,IACXD,EAAOE,KAAI,SAAAR,GACP,IAAM7C,EAAS6C,EAAGpE,KAAI,MAAPoE,E,+LAAE,EAAM,EAAKF,MAASW,GAAI,IACzC,OAAOtD,GAAUA,EAAOH,KAAOG,EAASmC,QAAQoB,QAAQvD,EAC5D,IAER,EAEA,YAAAwD,QAAA,WACIpE,KAAKsD,QAAU,KACftD,KAAKuD,KAAO,IAChB,EACJ,EA9CA,IRdA,SAAY7D,GAGR,qFACA,sFACA,uFACA,2FACA,4FACA,iGACH,CATD,CAAYA,IAAAA,EAAmB,KCA/B,SAAYC,GACR,kCACA,oCACA,oBACA,qCACA,4DACA,mDACA,+CACA,4CACA,yDACA,wCACA,2DACH,CAZD,CAAYA,IAAAA,EAAU,KQGtB,iBAII,WAAY0E,EAAkBC,GAC1BtE,KAAKqE,KAAOA,EACZrE,KAAKuE,QAAUvE,KAAKwE,QAAQF,EAChC,CA8CJ,OA5CI,YAAAG,MAAA,WACI,MAAMzE,KAAKhB,KACf,EAEA,YAAAA,IAAA,WACI,MAAO,CACHuF,QAASvE,KAAKuE,QACdF,KAAMrE,KAAKqE,KAEnB,EAEQ,YAAAG,QAAR,SAAgBF,GACZ,IAAII,EACJ,OAAQ1E,KAAKqE,MACT,KAAK1E,EAAWgF,eACZD,EAAS,qBAAcJ,EAAI,oCAC3B,MACJ,KAAK3E,EAAWiF,cACZF,EAAS,qBAAcJ,EAAI,mCAC3B,MACJ,KAAK3E,EAAWkF,iBACZH,EAAS,gDACT,MACJ,KAAK/E,EAAWmF,eACZJ,EAAS,0DACT,MACJ,KAAK/E,EAAWoF,sBACZL,EAAS,oDACT,MACJ,KAAK/E,EAAWqF,oBACZN,EAAS,UAAGJ,EAAO,OAAS,QAAO,mCACnC,MACJ,KAAK3E,EAAWsF,mBACZP,EAAS,kCACT,MACJ,QACS1E,KAAKqE,OACNrE,KAAKqE,KAAO1E,EAAWuF,SAE3BR,EAAS1E,KAAKuE,QAGtB,OAAOG,CACX,EACJ,EArDA,GCAA,0BAiBA,QAbI,YAAAS,UAAA,SAAU1F,GACNO,KAAKoF,YAAY3F,CACrB,EAEA,YAAA4F,IAAA,W,IAAI,sDACIrF,KAAKoF,WACLE,QAAQD,IAAG,MAAXC,QAAef,EAEvB,EAEA,YAAAgB,MAAA,SAAMlB,EAAkBC,GACpB,OAAO,IAAIkB,EAAYnB,EAAMC,EACjC,EACJ,EAjBA,GCHamB,EAAQ,W,IAAC,sDAClB,OAAO5G,OAAO6G,OAAM,MAAb7G,O,+LAAM,EAAQ,CAAC,GAAMI,GAAG,GACnC,ECCM0G,EAAc,SAACC,EAA+BC,GAClD,IAAMC,EAAOF,EAAS3B,KAAI,SAAC8B,EAAKnC,GAC9B,OAAOiC,EAAUE,EAAKnC,EACxB,IACA,OAAOb,QAAQiB,IAAI8B,EACrB,EAEO,SAASE,EAAWC,EAAeJ,EAAqBK,QAAA,IAAAA,IAAAA,EAA4B,CAAC,GAC1F,IAAMC,EAAeF,EAAO/I,OACtBkJ,EAAcF,EAAOE,aAAeD,EAEtCvF,EAAS,GACPyF,EAAsC,WAC1C,IAAMT,EAAWK,EAAOnC,OAAO,EAAGsC,GAClC,OAAOT,EAAYC,EAAUC,GAAWpF,MAAK,SAAA6F,GAG3C,OAFA1F,EAASA,EAAO2F,OAAOD,GAEhBH,EAAevF,EAAO1D,OAC3BmJ,IAAoBG,GAAe5F,EACvC,GACF,EAEA,OAAOyF,GACT,CC1BA,MAAM,EAA+BrI,QAAQ,oBCA7C,SAASyI,EAAOnI,GACZ,IAAK+B,OAAOqG,cAAcpI,IAAMA,EAAI,EAChC,MAAM,IAAI8D,MAAM,kCAAkC9D,IAC1D,CAUA,SAASqI,EAAMC,KAAMC,GACjB,MALoBnI,EAKPkI,aAJQE,YACX,MAALpI,GAA0B,iBAANA,GAAyC,eAAvBA,EAAEqI,YAAYC,MAIrD,MAAM,IAAI5E,MAAM,uBANjB,IAAiB1D,EAOpB,GAAImI,EAAQ3J,OAAS,IAAM2J,EAAQI,SAASL,EAAE1J,QAC1C,MAAM,IAAIkF,MAAM,iCAAiCyE,oBAA0BD,EAAE1J,SACrF,CAOA,SAASgK,EAAOC,EAAUC,GAAgB,GACtC,GAAID,EAASE,UACT,MAAM,IAAIjF,MAAM,oCACpB,GAAIgF,GAAiBD,EAASG,SAC1B,MAAM,IAAIlF,MAAM,wCACxB,CACA,SAASmF,EAAOC,EAAKL,GACjBR,EAAMa,GACN,MAAMC,EAAMN,EAASO,UACrB,GAAIF,EAAItK,OAASuK,EACb,MAAM,IAAIrF,MAAM,yDAAyDqF,IAEjF,CAEA,MACA,EADe,CAAEhB,SAAQkB,KAnCzB,SAAcf,GACV,GAAiB,kBAANA,EACP,MAAM,IAAIxE,MAAM,yBAAyBwE,IACjD,EAgC+BD,QAAOiB,KApBtC,SAAcC,GACV,GAAiB,mBAANA,GAAwC,mBAAbA,EAAEC,OACpC,MAAM,IAAI1F,MAAM,mDACpBqE,EAAOoB,EAAEH,WACTjB,EAAOoB,EAAEE,SACb,EAe4Cb,SAAQK,UCvC9CS,EAA6BC,OAAO,GAAK,GAAK,GAC9CC,EAAuBD,OAAO,IAEpC,SAASE,EAAQ7J,EAAG8J,GAAK,GACrB,OAAIA,EACO,CAAEP,EAAGxH,OAAO/B,EAAI0J,GAAaK,EAAGhI,OAAQ/B,GAAK4J,EAAQF,IACzD,CAAEH,EAAsC,EAAnCxH,OAAQ/B,GAAK4J,EAAQF,GAAiBK,EAA4B,EAAzBhI,OAAO/B,EAAI0J,GACpE,CACA,SAASM,EAAMC,EAAKH,GAAK,GACrB,IAAII,EAAK,IAAIC,YAAYF,EAAIrL,QACzBwL,EAAK,IAAID,YAAYF,EAAIrL,QAC7B,IAAK,IAAIyL,EAAI,EAAGA,EAAIJ,EAAIrL,OAAQyL,IAAK,CACjC,MAAM,EAAEd,EAAC,EAAEQ,GAAMF,EAAQI,EAAII,GAAIP,IAChCI,EAAGG,GAAID,EAAGC,IAAM,CAACd,EAAGQ,EACzB,CACA,MAAO,CAACG,EAAIE,EAChB,CACA,MCOaE,EAAmE,KAA5D,IAAI9B,WAAW,IAAI2B,YAAY,CAAC,YAAahM,QAAQ,GASlE,SAASoM,EAAWC,GACvB,IAAK,IAAIH,EAAI,EAAGA,EAAIG,EAAI5L,OAAQyL,IAC5BG,EAAIH,IATaI,EASCD,EAAIH,KATc,GAAM,WAC5CI,GAAQ,EAAK,SACbA,IAAS,EAAK,MACdA,IAAS,GAAM,IAHG,IAACA,CAWzB,CA8EO,SAASC,EAAQC,GAIpB,MAHoB,iBAATA,IACPA,EAZD,SAAqBC,GACxB,GAAmB,iBAARA,EACP,MAAM,IAAI9G,MAAM,2CAA2C8G,GAC/D,OAAO,IAAIpC,YAAW,IAAIqC,aAAcC,OAAOF,GACnD,CAQeG,CAAYJ,IACvB,EAAOA,GACAA,CACX,CAoBO,MAAMK,EAET,KAAAC,GACI,OAAOvJ,KAAKwJ,YAChB,EC1IJ,MAAMC,EAAU,GACVC,EAAY,GACZC,EAAa,GACbC,EAAsB3B,OAAO,GAC7B4B,EAAsB5B,OAAO,GAC7B6B,EAAsB7B,OAAO,GAC7B8B,EAAsB9B,OAAO,GAC7B+B,EAAwB/B,OAAO,KAC/BgC,EAAyBhC,OAAO,KACtC,IAAK,IAAIiC,EAAQ,EAAGC,EAAIN,EAAKO,EAAI,EAAGC,EAAI,EAAGH,EAAQ,GAAIA,IAAS,EAE3DE,EAAGC,GAAK,CAACA,GAAI,EAAID,EAAI,EAAIC,GAAK,GAC/BZ,EAAQ/F,KAAK,GAAK,EAAI2G,EAAID,IAE1BV,EAAUhG,MAAQwG,EAAQ,IAAMA,EAAQ,GAAM,EAAK,IAEnD,IAAII,EAAIV,EACR,IAAK,IAAIW,EAAI,EAAGA,EAAI,EAAGA,IACnBJ,GAAMA,GAAKN,GAASM,GAAKJ,GAAOE,GAAWD,EACvCG,EAAIL,IACJQ,GAAKT,IAASA,GAAuB5B,OAAOsC,IAAMV,GAE1DF,EAAWjG,KAAK4G,EACpB,CACA,MAAOE,EAAaC,GAA+BnC,EAAMqB,GAAY,GAE/De,EAAQ,CAAC7C,EAAGQ,EAAGsC,IAAOA,EAAI,GFEjB,EAAC9C,EAAGQ,EAAGsC,IAAOtC,GAAMsC,EAAI,GAAQ9C,IAAO,GAAK8C,EEFtBC,CAAO/C,EAAGQ,EAAGsC,GFDnC,EAAC9C,EAAGQ,EAAGsC,IAAO9C,GAAK8C,EAAMtC,IAAO,GAAKsC,EECGE,CAAOhD,EAAGQ,EAAGsC,GAC9DG,EAAQ,CAACjD,EAAGQ,EAAGsC,IAAOA,EAAI,GFEjB,EAAC9C,EAAGQ,EAAGsC,IAAO9C,GAAM8C,EAAI,GAAQtC,IAAO,GAAKsC,EEFtBI,CAAOlD,EAAGQ,EAAGsC,GFDnC,EAAC9C,EAAGQ,EAAGsC,IAAOtC,GAAKsC,EAAM9C,IAAO,GAAK8C,EECGK,CAAOnD,EAAGQ,EAAGsC,GA+C7D,MAAMM,UAAe3B,EAExB,WAAAvC,CAAYgB,EAAUmD,EAAQxD,EAAWyD,GAAY,EAAOC,EAAS,IAcjE,GAbAC,QACArL,KAAK+H,SAAWA,EAChB/H,KAAKkL,OAASA,EACdlL,KAAK0H,UAAYA,EACjB1H,KAAKmL,UAAYA,EACjBnL,KAAKoL,OAASA,EACdpL,KAAKsL,IAAM,EACXtL,KAAKuL,OAAS,EACdvL,KAAKsH,UAAW,EAChBtH,KAAKqH,WAAY,EAEjBZ,EAAOiB,GAEH,GAAK1H,KAAK+H,UAAY/H,KAAK+H,UAAY,IACvC,MAAM,IAAI3F,MAAM,4CDhFT,IAAC0G,ECiFZ9I,KAAKwL,MAAQ,IAAI1E,WAAW,KAC5B9G,KAAKyL,SDlFO3C,ECkFO9I,KAAKwL,MDlFJ,IAAI/C,YAAYK,EAAIrM,OAAQqM,EAAI4C,WAAYC,KAAKC,MAAM9C,EAAI+C,WAAa,ICmFhG,CACA,MAAAC,GACSlD,GACDC,EAAW7I,KAAKyL,SApErB,SAAiBd,EAAGS,EAAS,IAChC,MAAMW,EAAI,IAAItD,YAAY,IAE1B,IAAK,IAAIyB,EAAQ,GAAKkB,EAAQlB,EAAQ,GAAIA,IAAS,CAE/C,IAAK,IAAIE,EAAI,EAAGA,EAAI,GAAIA,IACpB2B,EAAE3B,GAAKO,EAAEP,GAAKO,EAAEP,EAAI,IAAMO,EAAEP,EAAI,IAAMO,EAAEP,EAAI,IAAMO,EAAEP,EAAI,IAC5D,IAAK,IAAIA,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAAG,CAC5B,MAAM4B,GAAQ5B,EAAI,GAAK,GACjB6B,GAAQ7B,EAAI,GAAK,GACjB8B,EAAKH,EAAEE,GACPE,EAAKJ,EAAEE,EAAO,GACdG,EAAK1B,EAAMwB,EAAIC,EAAI,GAAKJ,EAAEC,GAC1BK,EAAKvB,EAAMoB,EAAIC,EAAI,GAAKJ,EAAEC,EAAO,GACvC,IAAK,IAAI3B,EAAI,EAAGA,EAAI,GAAIA,GAAK,GACzBM,EAAEP,EAAIC,IAAM+B,EACZzB,EAAEP,EAAIC,EAAI,IAAMgC,CAExB,CAEA,IAAIC,EAAO3B,EAAE,GACT4B,EAAO5B,EAAE,GACb,IAAK,IAAIL,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MAAMkC,EAAQ9C,EAAUY,GAClB8B,EAAK1B,EAAM4B,EAAMC,EAAMC,GACvBH,EAAKvB,EAAMwB,EAAMC,EAAMC,GACvBC,EAAKhD,EAAQa,GACnBgC,EAAO3B,EAAE8B,GACTF,EAAO5B,EAAE8B,EAAK,GACd9B,EAAE8B,GAAML,EACRzB,EAAE8B,EAAK,GAAKJ,CAChB,CAEA,IAAK,IAAIhC,EAAI,EAAGA,EAAI,GAAIA,GAAK,GAAI,CAC7B,IAAK,IAAID,EAAI,EAAGA,EAAI,GAAIA,IACpB2B,EAAE3B,GAAKO,EAAEN,EAAID,GACjB,IAAK,IAAIA,EAAI,EAAGA,EAAI,GAAIA,IACpBO,EAAEN,EAAID,KAAO2B,GAAG3B,EAAI,GAAK,IAAM2B,GAAG3B,EAAI,GAAK,GACnD,CAEAO,EAAE,IAAMH,EAAYN,GACpBS,EAAE,IAAMF,EAAYP,EACxB,CACA6B,EAAEpO,KAAK,EACX,CAyBQ+O,CAAQ1M,KAAKyL,QAASzL,KAAKoL,QACtBxC,GACDC,EAAW7I,KAAKyL,SACpBzL,KAAKuL,OAAS,EACdvL,KAAKsL,IAAM,CACf,CACA,MAAAqB,CAAO1D,GACH/B,EAAOlH,MACP,MAAM,SAAE+H,EAAQ,MAAEyD,GAAUxL,KAEtB4M,GADN3D,EAAOD,EAAQC,IACE/L,OACjB,IAAK,IAAIoO,EAAM,EAAGA,EAAMsB,GAAM,CAC1B,MAAMC,EAAOlB,KAAKlE,IAAIM,EAAW/H,KAAKsL,IAAKsB,EAAMtB,GACjD,IAAK,IAAI3C,EAAI,EAAGA,EAAIkE,EAAMlE,IACtB6C,EAAMxL,KAAKsL,QAAUrC,EAAKqC,KAC1BtL,KAAKsL,MAAQvD,GACb/H,KAAK8L,QACb,CACA,OAAO9L,IACX,CACA,MAAA8M,GACI,GAAI9M,KAAKsH,SACL,OACJtH,KAAKsH,UAAW,EAChB,MAAM,MAAEkE,EAAK,OAAEN,EAAM,IAAEI,EAAG,SAAEvD,GAAa/H,KAEzCwL,EAAMF,IAAQJ,EACA,IAATA,GAAwBI,IAAQvD,EAAW,GAC5C/H,KAAK8L,SACTN,EAAMzD,EAAW,IAAM,IACvB/H,KAAK8L,QACT,CACA,SAAAiB,CAAUvF,GACNN,EAAOlH,MAAM,GACb2G,EAAMa,GACNxH,KAAK8M,SACL,MAAME,EAAYhN,KAAKwL,OACjB,SAAEzD,GAAa/H,KACrB,IAAK,IAAIsL,EAAM,EAAGsB,EAAMpF,EAAItK,OAAQoO,EAAMsB,GAAM,CACxC5M,KAAKuL,QAAUxD,GACf/H,KAAK8L,SACT,MAAMe,EAAOlB,KAAKlE,IAAIM,EAAW/H,KAAKuL,OAAQqB,EAAMtB,GACpD9D,EAAIyF,IAAID,EAAUE,SAASlN,KAAKuL,OAAQvL,KAAKuL,OAASsB,GAAOvB,GAC7DtL,KAAKuL,QAAUsB,EACfvB,GAAOuB,CACX,CACA,OAAOrF,CACX,CACA,OAAA2F,CAAQ3F,GAEJ,IAAKxH,KAAKmL,UACN,MAAM,IAAI/I,MAAM,yCACpB,OAAOpC,KAAK+M,UAAUvF,EAC1B,CACA,GAAA4F,CAAIzG,GAEA,OADAF,EAAOE,GACA3G,KAAKmN,QAAQ,IAAIrG,WAAWH,GACvC,CACA,UAAA0G,CAAW7F,GAEP,GADAD,EAAOC,EAAKxH,MACRA,KAAKsH,SACL,MAAM,IAAIlF,MAAM,+BAGpB,OAFApC,KAAK+M,UAAUvF,GACfxH,KAAKoE,UACEoD,CACX,CACA,MAAA8F,GACI,OAAOtN,KAAKqN,WAAW,IAAIvG,WAAW9G,KAAK0H,WAC/C,CACA,OAAAtD,GACIpE,KAAKqH,WAAY,EACjBrH,KAAKwL,MAAM7N,KAAK,EACpB,CACA,UAAA6L,CAAW+D,GACP,MAAM,SAAExF,EAAQ,OAAEmD,EAAM,UAAExD,EAAS,OAAE0D,EAAM,UAAED,GAAcnL,KAY3D,OAXAuN,IAAOA,EAAK,IAAItC,EAAOlD,EAAUmD,EAAQxD,EAAWyD,EAAWC,IAC/DmC,EAAG9B,QAAQwB,IAAIjN,KAAKyL,SACpB8B,EAAGjC,IAAMtL,KAAKsL,IACdiC,EAAGhC,OAASvL,KAAKuL,OACjBgC,EAAGjG,SAAWtH,KAAKsH,SACnBiG,EAAGnC,OAASA,EAEZmC,EAAGrC,OAASA,EACZqC,EAAG7F,UAAYA,EACf6F,EAAGpC,UAAYA,EACfoC,EAAGlG,UAAYrH,KAAKqH,UACbkG,CACX,EAEJ,MAAMC,EAAM,CAACtC,EAAQnD,EAAUL,IDxCxB,SAAyB+F,GAC5B,MAAMC,EAASC,GAAQF,IAAWd,OAAO3D,EAAQ2E,IAAML,SACjDM,EAAMH,IAIZ,OAHAC,EAAMhG,UAAYkG,EAAIlG,UACtBgG,EAAM3F,SAAW6F,EAAI7F,SACrB2F,EAAM5F,OAAS,IAAM2F,IACdC,CACX,CCiC6CG,EAAgB,IAAM,IAAI5C,EAAOlD,EAAUmD,EAAQxD,KASnFoG,EAA6BN,EAAI,EAAM,IAAK,IAK5CO,EAA6BP,EAAI,EAAM,IAAK,IAC5CQ,EAA6BR,EAAI,EAAM,IAAK,IAC5CS,EAA6BT,EAAI,EAAM,GAAI,ICnLjD,SAASU,EAAStG,GACrB,OAAQ+F,IACJ,QAAaA,GACN/F,EAAK+F,GAEpB,CAjCmB,OACC,QAkCE,MAClB,MAAMQ,EAAkC,iBAAfC,YAA2B,WAAYA,WAAaA,WAAWC,YAASvQ,EAC3FwQ,EAAgC,oBAAX/Q,QACG,mBAAnBA,OAAOS,SACdT,OAAOS,QAAQmF,KAAK5F,QAEd+Q,IAAgBH,GAAYG,EAAY,SAGrD,EATqB,GAAf,MCnCMC,GAAYL,EAASJ,GACrBU,GAAY,MACrB,MAAMC,EAAIP,EAASH,GAEnB,OADAU,EAAE3G,OAASiG,EAAWjG,OACf2G,CACV,EAJwB,GAKZC,GAAYR,EAASF,GACrBW,GAAYT,EAASD,GCPlC,+BA6CA,QAxCW,EAAAW,eAAiB,SAAUC,GAC9B,IAAKnS,OAAOoS,SAASD,GAAQ,CAC7B,IAAMlB,EAAM,0DAAmDkB,GAC/D,MAAM,IAAIzM,MAAMuL,E,CAEpB,EAOO,EAAA7B,OAAS,SAAUpN,EAAWqQ,GAEjC,YAFiC,IAAAA,IAAAA,EAAA,KACjC9D,EAAO2D,eAAelQ,GACdqQ,GACJ,KAAK,IACD,OAAOrS,OAAOS,KAAKoR,GAAU7P,IAEjC,KAAK,IACD,OAAOhC,OAAOS,KAAK,GAAKuB,IAE5B,KAAK,IACD,OAAOhC,OAAOS,KAAKuR,GAAUhQ,IAEjC,KAAK,IACD,OAAOhC,OAAOS,KAAKwR,GAAUjQ,IAEjC,QACI,MAAM,IAAI0D,MAAM,kCAA2B2M,IAGvD,EAMO,EAAAP,UAAY,SAAU9P,GACzB,OAAOuM,EAAOa,OAAOpN,EACzB,EACJ,C,CA7CA,G,UCAMsQ,GAAO,GAAOR,UAIpB,cAII,WAAYS,GACR,QADQ,IAAAA,IAAAA,EAAA,IACJA,EAAO/R,OAAS,EAChB,MAAM,IAAIkF,MAAM,yBAGpB,IAAM8M,EAAQvD,KAAKwD,KAAKxD,KAAKtG,IAAI4J,EAAO/R,QAAUyO,KAAKtG,IAAI,IAC3D,GAAI6J,EAAQ,GACR,MAAM,IAAI9M,MAAM,4BAGpBpC,KAAKiP,OAASA,EAAO1I,OACjB6I,MAAMjS,KAEFiS,MAAMzD,KAAK0D,IAAI,EAAGH,GAASD,EAAO/R,SAClC,WAAM,WAAAoS,OAAM,GAAN,KAGdtP,KAAKuP,OAAS,CAACvP,KAAKiP,QACpBjP,KAAKwP,aAAaxP,KAAKiP,OAC3B,CAiFJ,OA/EI,YAAAO,aAAA,SAAaC,GACT,GAAqB,IAAjBA,EAAMvS,OACN,OAAO,EAKX,IADA,IAAMwS,EAAY,GACT/G,EAAI,EAAGA,EAAI8G,EAAMvS,OAAQyL,GAAK,EAAG,CACtC,IAAMgH,EAAOF,EAAM9G,GACbiH,EAAQH,EAAM9G,EAAI,GAElBM,EAAO,UAAW1C,OAAO,CAACoJ,EAAMC,IACtCF,EAAUhM,KAAKsL,GAAK/F,G,CAIpBwG,EAAMvS,OAAS,GAAM,GACrBwS,EAAUhM,KAAK+L,EAAMA,EAAMvS,OAAS,IAGxC8C,KAAKuP,OAAO7L,KAAKgM,GACjB1P,KAAKwP,aAAaE,EACtB,EAEA,YAAAG,UAAA,WACI,OAAO7P,KAAKiP,MAChB,EAEA,YAAAa,UAAA,WACI,OAAO9P,KAAKuP,MAChB,EAEA,YAAAQ,QAAA,WACI,OAAO/P,KAAKuP,OAAOvP,KAAKuP,OAAOrS,OAAS,GAAG,EAC/C,EAEA,YAAA8S,SAAA,SAASC,GAEL,IADA,IAAIrM,GAAS,EACJ+E,EAAI,EAAGA,EAAI3I,KAAKiP,OAAO/R,OAAQyL,IACa,IAA7C,UAAWuH,QAAQD,EAAMjQ,KAAKiP,OAAOtG,MACrC/E,EAAQ+E,GAIhB,IAAMwH,EAAQ,GACd,GAAIvM,GAAS5D,KAAK6P,YAAY3S,OAC1B,KAAIkT,OAAY,EAChB,IAASzH,EAAI,EAAGA,EAAI3I,KAAKuP,OAAOrS,OAAS,EAAGyL,IAEpCyH,EADAxM,EAAQ,GAAM,EACCA,EAAQ,EAERA,EAAQ,EAE3BA,EAAQ+H,KAAKC,MAAMhI,EAAQ,GAC3BuM,EAAMzM,KAAK1D,KAAKuP,OAAO5G,GAAGyH,GARd,CAWpB,OAAOD,CACX,EAEA,YAAAE,OAAA,SAAO5Q,EAAOmE,EAAO0M,EAAMH,GACvB,IAAKf,MAAMmB,QAAQJ,KAAW1Q,IAAU6Q,EACpC,OAAO,EAIX,IADA,IAAI1I,EAAOnI,EACFkJ,EAAI,EAAGA,EAAIwH,EAAMjT,OAAQyL,IAAK,CACnC,IAAM6H,EAAOL,EAAMxH,GAEff,EAAOoH,GADPpL,EAAQ,GAAM,EACF,UAAW2C,OAAO,CAACqB,EAAM4I,IAEzB,UAAWjK,OAAO,CAACiK,EAAM5I,KAGzChE,EAAQ+H,KAAKC,MAAMhI,EAAQ,E,CAG/B,OAA0C,IAAnC,UAAWsM,QAAQtI,EAAM0I,EACpC,EACJ,EAxGA,GCNA,MAAM,GAA+BtS,QAAQ,S,eCmB7C,2BA4GA,QApGW,EAAAyS,UAAP,SAAiBhR,GACb,IAAIf,EAAIe,EAER,GAAiB,iBAANf,EACP,MAAM,IAAI0D,MAAM,mEAA4D1D,IAKhF,OAFIA,EAAExB,OAAS,IAAGwB,EAAI,WAAIA,IAEnBA,CACX,EAEO,EAAAgS,cAAP,SAAqBxH,GACjB,GAAmB,iBAARA,EACP,MAAM,IAAI9G,MAAM,4EAAqE8G,IAGzF,MAAkB,MAAXA,EAAI,IAAyB,MAAXA,EAAI,EACjC,EAoBO,EAAAyH,YAAP,SAAmBlR,EAAevC,GAC9B,QAAqB,iBAAVuC,IAAuBA,EAAMmR,MAAM,qBAE1C1T,GAAUuC,EAAMvC,SAAW,EAAI,EAAIA,EAG3C,EAnDO,EAAA2T,SAAW,SAAUlI,GACxB,IAAKtI,OAAOqG,cAAciC,IAAMA,EAAI,EAChC,MAAM,IAAIvG,MAAM,4CAAqCuG,IAEzD,MAAO,YAAKA,EAAE1H,SAAS,IAC3B,EAsBO,EAAA6P,eAAiB,SAAC5H,GACrB,GAAmB,iBAARA,EACP,MAAM,IAAI9G,MAAM,wEAAiE8G,IAGrF,OAAO6H,EAAWL,cAAcxH,GAAOA,EAAI/G,MAAM,GAAK+G,CAC1D,EAOO,EAAA8H,YAAc,SAAUrI,GAC3B,IAAMsI,EAAMF,EAAWF,SAASlI,GAChC,OAAOjM,OAAOS,KAAK4T,EAAWN,UAAUQ,EAAI9O,MAAM,IAAK,MAC3D,EAWO,EAAA+O,SAAW,SAAUC,GACxB,GAAIA,QACA,OAAOzU,OAAOW,YAAY,GAG9B,GAAIX,OAAOoS,SAASqC,GAChB,OAAOzU,OAAOS,KAAKgU,GAGvB,GAAI/B,MAAMmB,QAAQY,IAAMA,aAAarK,WACjC,OAAOpK,OAAOS,KAAKgU,GAGvB,GAAiB,iBAANA,EAAgB,CACvB,IAAKJ,EAAWJ,YAAYQ,GACxB,MAAM,IAAI/O,MACN,qHAA8G+O,IAGtH,OAAOzU,OAAOS,KAAK4T,EAAWN,UAAUM,EAAWD,eAAeK,IAAK,M,CAG3E,GAAiB,iBAANA,EACP,OAAOJ,EAAWC,YAAYG,GAGlC,GAAI,KAAGpQ,KAAKoQ,GAAI,CACZ,GAAIA,EAAEC,QACF,MAAM,IAAIhP,MAAM,uDAAgD+O,IAEpE,OAAOA,EAAEE,YAAY3U,O,CAGzB,GAAIyU,EAAEG,QAEF,OAAO5U,OAAOS,KAAKgU,EAAEG,WAGzB,GAAIH,EAAED,SACF,OAAOxU,OAAOS,KAAKgU,EAAED,YAGzB,MAAM,IAAI9O,MAAM,eACpB,EAMO,EAAAmP,YAAc,SAAU1T,GAE3B,MAAO,MADPA,EAAMkT,EAAWG,SAASrT,IACRoD,SAAS,MAC/B,EACJ,C,CA5GA,GCnBA,MAAM,GAA+BjD,QAAQ,O,eCA7C,MAAM,GAA+BA,QAAQ,oBCAvC,GAA+BA,QAAQ,qBCAvC,GAA+BA,QAAQ,sB,ICOjCwT,G,6jCCSZ,2BAwNA,QAtNiB,EAAAC,mBAAb,SACIC,EACAC,EACA7R,EACAC,G,6GAEM6R,EAAkBjG,KAAKwD,KAAKxD,KAAKkG,KAAK9R,EAAWD,EAAa,IAG9DgS,EAA0B,GAG1BC,EAAcJ,GADdK,EAASlS,GAEXmS,EAAY,EACZC,EAAanS,EAAWiS,E,WAEnB9C,G,oFACCiD,EAAU,WAAMP,EAAkB1C,GAKpC6C,GAFEK,EAAYH,EAAYE,EAAU,EAAI,IAIlCE,EAAeD,EAAY,EAEP,GAAM,EAAKE,cAAcZ,EAAMM,EAASC,EAAWD,EAASI,KAJtF,M,cAIMG,EAAoB,SAC1BT,EAAcpO,KAAK6O,GACnBN,EAAYI,E,oBAMNG,EAAgB7G,KAAKlE,IAAIyK,EAAYE,GAGrCK,EAAiBb,GAAmB1C,EAAQ,GAC9CgD,GAAcE,GAERG,EAAoB,EAAKG,kBAAkBD,EAAgBf,GACjEI,EAAcpO,KAAK6O,G,OAHnB,M,OAgB2B,OAVrBI,EAAgBhH,KAAKwD,KAAKxD,KAAKkG,KAAKK,EAAaE,IAGjDQ,EAAmBH,EAAiBE,EAOf,GAAM,EAAKL,cAAcZ,EAAMM,EAASI,EAAY,EAAGJ,EAASE,I,OAArFW,EAAqB,SAGrB,EAAY,EAAKH,kBAAkBC,EAAejB,IAGlDzC,EAASG,MAAMjS,KAAK,CAAED,OAAQ,WAAK0V,KAAoB,WAAM,OAAA7B,GAAWG,SAAS,EAApB,KAC5D,GAAK2B,EACNN,EAAoB,IAAIO,GAAW7D,GAAQc,UACjD+B,EAAcpO,KAAK6O,G,iBAEvBL,EAAaM,E,uCAjDZtD,EAAQ,E,wBAAGA,EAAQ0C,E,KAAnB1C,IAAkC,M,wCAAEA,GAAS,E,aAqDtD,MAAO,CAAP,EAAO4C,EAAciB,W,qSAGlB,EAAAC,gBAAP,SAAuBC,EAA2BnT,EAAoBC,EAAkB4R,GACpF,OAAOuB,EAAUzB,mBACbwB,EAAWtB,EAAa7R,EAAYC,GACtCU,MAAK,SAAA0P,GACH,OAAOY,GAAWQ,YACd7U,OAAO6J,OACH4J,EAAMlM,KAAI,SAAAkP,GACN,OAAOpC,GAAWG,SAASiC,EAC/B,KAGZ,GACJ,EAEO,EAAAb,cAAP,SAAqBc,EAAwBtT,EAAoBC,GAC7D,OAAOqT,EAAOvT,YAAYC,EAAYC,GAAUU,MAAK,SAAA4S,GACjD,OAAOtC,GAAWG,SAAS,YAAKmC,GACpC,IAAGC,OAAM,SAAAC,GACL,OAAO,IACX,GACJ,EAEO,EAAAb,kBAAP,SAAyBpU,EAAW8U,GAChC,GAAU,IAAN9U,EAAS,MAAO,qEACpB,IAAMkV,EAAUxT,KAAK0S,kBAAkBpU,EAAI,EAAG8U,GAC9C,OAAO,GAAO5E,UACVuC,GAAWG,SAASkC,EAAOK,iBAAiB,CAACD,EAASA,GAAU,CAAC,UAAW,aAEpF,EAEO,EAAAE,gBAAP,SAAuBC,EAA8BC,EAA8BlC,EAAsBmC,EAA+BC,QAA/B,IAAAD,IAAAA,EAAA,KACrG,IAEIE,EAFEC,EAAkBjD,GAAWQ,YAAY2B,EAAUe,mBAAmBL,IACtEM,EAAe,IAAI,QAEzB,GAAKJ,EAsBDC,EAAiBvN,GAAesN,OAtBlB,CACd,IAAM,EAAkB,GACxBF,EAAMO,aAAaC,SAAQ,SAAAC,GACnBA,EAAGC,kBAAoBN,GAI3B,EAAgBtQ,KACZgO,EAAK6C,sBAAsBF,EAAGC,iBAEtC,IACAP,EAAiB/N,EACb,GACA,SAAAD,GACI,OAAOA,CACX,GACA,CACIK,YAAayN,G,CAQzB,OAAOE,EAAetT,MAAK,SAAA+T,GACvB,OAAOzR,QAAQiB,IACXwQ,EAASvQ,KAAI,SAAAwQ,GACT,IAAMC,EAAO,YAAWD,EAAeE,kBACjCC,EAAa1B,EAAU2B,gBAAgBJ,GAC7C,OAAOP,EAAaY,IAAIJ,EAAME,EAClC,IAER,IAAGnU,MAAK,SAAA8S,GACJ,OAAOW,EAAaa,SAAS,YAAWpB,EAAQgB,mBAAmB,EACvE,IAAGlU,MAAK,SAAAG,GACJ,GAAIA,EAAOoU,UAAU9X,OAAS,EAC1B,MAAM,IAAIkF,MAAM,iCAUpB,MAPY,CACR6S,UAAWlE,GAAWG,SAASyC,EAAQsB,WACvCC,YAAatU,EAAOuU,MAAMlR,KAAI,SAAA0G,GAAK,OAAAA,EAAEyK,KAAF,IACnC9E,KAAM4C,EAAUmC,aAAazB,GAAO0B,YACpCZ,KAAM,YAAWf,EAAQgB,kBACzBlV,MAAOyT,EAAUqC,eAAe5B,GAAW/S,EAAO4P,KAAK/Q,MAAQ,YAAWmB,EAAO4P,KAAK/Q,MAAMwB,YAGpG,GACJ,EAEO,EAAAsU,eAAP,SAAsB5B,GAClB,IAAM6B,EAAUC,EAAU3T,MAAM6R,EAAQtP,MACxC,OAAyB,MAAlBsP,EAAQ+B,QAA8B,QAAZF,GAAiC,OAAZA,CAC1D,EASO,EAAAvB,mBAAP,SAA0BL,GACtB,OAAO,GAAOpF,UACV9R,OAAO6J,OAAO,CAEV7J,OAAOS,KAAK,qBAAsB,UAClC,IAAAwY,eAAc5E,GAAWG,SAAS0C,EAAMnN,QAAS,GACjDsK,GAAWG,SAAS0C,EAAMhM,QAGtC,EAEO,EAAAiN,gBAAP,SAAuBlB,GACnB,IAAIiC,EAAc,YAAW,CACzB7E,GAAWG,cACYpT,IAAnB6V,EAAQ+B,QAA0C,MAAlB/B,EAAQ+B,OAAkB/B,EAAQ+B,OAAS,MAAQ,KAAQ/B,EAAQrD,MAEvGS,GAAWG,SAASyC,EAAQkC,mBAC5B9E,GAAWG,SAASyC,EAAQmC,WAE5BnC,EAAQoC,KAAK9R,KAAI,SAAAoE,GAEb,MAAO,CACH0I,GAAWG,SAAS7I,EAAEvH,SACtBuH,EAAE2N,OAAO/R,IAAI8M,GAAWG,UACxBH,GAAWG,SAAS7I,EAAEY,MAE9B,MAKJ,OAHIiK,EAAUqC,eAAe5B,KACzBiC,EAAclZ,OAAO6J,OAAO,CAACwK,GAAWG,SAASyC,EAAQtP,MAAOuR,KAE7DA,CACX,EAEO,EAAAP,aAAP,SAAoBY,GAChBA,EAAOC,WAAaT,EAAU3T,MAAMmU,EAAOC,YAC3C,IAAMC,EAAS,IAAI,GAAAC,OAAO,CACtBC,MAAO,GAAAC,MAAMC,QAASC,SAAU,GAAAC,SAASC,SAM7C,OAJkB,GAAAC,YAAYC,eAAeX,EAAQ,CACjDE,OAAQA,EACRU,+BAA+B,GAGvC,EACJ,EAxNA,GChBMC,GAGa,eAMnB,cAGI,WAAY5Q,QAAA,IAAAA,IAAAA,EAAuC,CAAC,GAFpD,KAAA6Q,QAAU,IAGN7Q,EAA2B,iBAAXA,EAAsB,CAClC6Q,QAAS7Q,GACTA,GAEO6Q,UACP/W,KAAK+W,QAAU7Q,EAAO6Q,QAE9B,CA+BJ,OA7BI,YAAA/X,IAAA,SAAOgY,EAAUC,GAIb,YAJG,IAAAD,IAAAA,EAAA,SAAU,IAAAC,IAAAA,EAAA,IACbD,EAAMhX,KAAK+W,QAAUC,EAAMnY,OAAOqY,KAAKD,GACnChT,KAAI,SAAAnH,GAAO,gBAAGqa,mBAAmBra,GAAI,YAAIqa,mBAAmBF,EAAMna,IAAvD,IAAgEsa,KAAK,KAE7EN,GAAME,EAAK,CACd7W,OAAQ,MACRkX,QAAS,CACL,eAAgB,mBAChB,OAAU,sBAEf5W,MAAK,SAAA6W,GACJ,OAAOA,EAAIC,MACf,GACJ,EAEA,YAAAC,KAAA,SAAKR,EAAUS,GAGX,YAHC,IAAAT,IAAAA,EAAA,IACDA,EAAMhX,KAAK+W,QAAUC,EAEdF,GAAME,EAAK,CACd7W,OAAQ,OACRkX,QAAS,CACL,eAAgB,mBAChB,OAAU,oBAEdI,KAAMA,EAAOC,KAAKC,UAAUF,GAAQ,OACrChX,MAAK,SAAA6W,GACJ,OAAOA,EAAIC,MACf,GACJ,EACJ,EA1CA,GCHMK,GAAsB,CACxB,EAAG,OACH,EAAG,OACH,SAAU,OACV,IAAK,QACL,MAAO,QACP,MAAO,QACP,KAAM,QACN,KAAM,QACN,KAAM,SAGV,2BAQI,KAAAhY,OAAS,IAAIiY,CA+DjB,QA5DI,YAAAC,KAAA,SAAKC,IACDA,EAASA,GAAU,CAAC,GACbC,OAAOC,cAAgBF,EAAOC,OAAOC,eAAiB,CAAC,EAC9DF,EAAOG,MAAMD,cAAgBF,EAAOG,MAAMD,eAAiB,CAAC,EAC5DjY,KAAK+X,OAASA,EAGd,IAAMI,EAAalW,GAAMkW,WAEzB,IAAKA,EACD,MAAM,IAAI/V,MAAM,yBAGhBH,GAAMmW,qBACNpY,KAAKqY,WAAapW,GAAMmW,oBAG5BpY,KAAKgY,OAAS,IAAKG,EAAmBJ,EAAOC,OAAOM,SAAUtY,KAAKJ,QACnEI,KAAKkY,MAAQ,IAAKC,EAAmBJ,EAAOG,MAAMI,SAAUtY,KAAKJ,QAEjEI,KAAKJ,OAAOuF,UAAU4S,EAAO1S,KAE7B,IAAMkT,EAAUR,EAAOQ,QACjBC,EAAUT,EAAOS,QACjBC,EAAazY,KAAKyY,WACpB,IAAIC,GAAWH,EAASC,GAE5B,OADAxY,KAAKJ,OAAOyF,IAAI,cAAeoT,GACxBA,EAAWX,OAAOxE,OAAM,SAAAqF,GAC3B,MAAM,IAAIvW,MAAM,kBAAWmW,EAAO,cAAMC,EAAO,qBACnD,GACJ,EAEA,YAAAI,OAAA,SAAO5R,EAAc3C,GACjB,OAAOrE,KAAKyY,WAAWG,OAAO5R,EAAM3C,EACxC,EAEA,YAAAwU,UAAA,SAAUnE,GACN,OAAO1U,KAAKyY,WAAWI,UAAUnE,EACrC,EAEA,sBAAI,kCAAmB,C,IAAvB,WACI,OAAO1U,KAAK6Y,UAAU,iBAC1B,E,gCAEA,sBAAI,+BAAgB,C,IAApB,WACI,OAAO7Y,KAAK6Y,UAAU,oBAC1B,E,gCAEA,sBAAI,iCAAkB,C,IAAtB,WACI,OAAO7Y,KAAK6Y,UAAU,iBAC1B,E,gCAEA,sBAAI,6BAAc,C,IAAlB,WACI,OAAO7Y,KAAK6Y,UAAU,kBAC1B,E,gCAEA,YAAAC,mBAAA,SAAmBC,GACf,OAAO/Y,KAAK6Y,UAAU,UAAGjB,GAAoBmB,GAAQ,oBACzD,EAEJ,EAvEA,GClBavS,GAAiB,SAAI/G,GAC9B,OAAOsD,QAAQoB,QAAW1E,EAC9B,EAEauZ,GAAa,SAACC,GACvB,IAAMC,EAAgB,IAAI9J,MAAM6J,EAAc/b,QAC1Cic,EAAU,EAGd,OAAO,IAAIpW,SAAQ,SAACoB,EAASiV,GACzBH,EAAc7E,SAAQ,SAACtR,GACnBC,QAAQoB,QAAQrB,GACXrC,KAAK0D,GACLmP,OAAM,SAAC/N,GACJ2T,EAAcC,GAAW5T,GACzB4T,GAAoB,KACJF,EAAc/b,QAE1Bkc,EAAOF,EAEf,GACR,GACJ,GACJ,EJvBaG,GAAa,qEACbC,GAAe,6CACfC,GAAsB,qEACtBC,GAA2B,qEAC3BC,GAA2B,qEAC3BC,GAA0B,qEAC1BC,GAA6B1R,OAAO,WAAK,MACtD,SAAYuJ,GACR,YACA,sBACA,mBACH,CAJD,CAAYA,KAAAA,GAAM,K,IKUlB,cAKI,WACcoI,EACAxG,GADA,KAAAwG,cAAAA,EACA,KAAAxG,OAAAA,CAEd,CA4QJ,OA1QI,sBAAI,8BAAe,C,IAAnB,WACI,OAAOpT,KAAK4Z,cAAc9Y,OAC9B,E,gCAEA,YAAA+Y,YAAA,sBACI,GAAI7Z,KAAK8Z,UACL,OAAOtT,GAA6BxG,KAAK8Z,WAE7C,IAAMF,EAAgB5Z,KAAK4Z,cAC3B,OAAO5Z,KAAKoT,OAAOwF,OACfgB,EAAc5S,KACd4S,EAAcG,YAChBtZ,MAAK,SAAAuZ,GAMH,OALA,EAAKF,UAAY,EAAKG,aAAa,CAC/BD,IAAG,EACHE,SAAUN,EAAcM,SACxBC,aAAcP,EAAc9Y,UAEzB,EAAKgZ,SAChB,GACJ,EAEA,YAAAM,WAAA,sBACI,OAAIpa,KAAKqa,SACE7T,GAAuBxG,KAAKqa,UAExBra,KAAKsa,UAAUta,KAAK4Z,cAAcM,UACnCE,aAAa3Z,MAAK,SAAAsY,GAE5B,OADA,EAAKsB,SAAWtB,EACT,EAAKsB,QAChB,GACJ,EAEU,YAAAE,aAAV,SAAuBpa,EAA4B+F,GAAnD,WAII,YAJ+C,IAAAA,IAAAA,EAAA,IAC/ClG,KAAKwa,kBAAkBtU,GAEvBlG,KAAKoT,OAAOxT,OAAOyF,IAAI,iBAChBrF,KAAKya,wBACR,CACIC,SAAUxU,EACVyU,SAAS,EACTxa,OAAM,EACN+Z,SAAUla,KAAK4Z,cAAcM,WAC9BzZ,MAAK,SAAAsX,GAEJ,OADA,EAAK3E,OAAOxT,OAAOyF,IAAI,wBACnBa,EAAO0U,kBACAnV,EAAMsS,EAAQ,CACjB9O,KAAM9I,EAAO0a,YACbtN,GAAIpN,EAAOW,UAGEX,EAAO2a,MACxB/C,EAGR,GACR,EAEU,YAAAgD,gBAAV,SAA0B7U,QAAA,IAAAA,IAAAA,EAAA,IACtBlG,KAAKwa,kBAAkBtU,GAEvB,IAAMgU,EAAWla,KAAK4Z,cAAcM,SAC9B9G,EAASpT,KAAKsa,UAAUJ,GAG9B,OAFA9G,EAAOxT,OAAOyF,IAAI,iBAEXrF,KAAKya,wBACR,CACIC,SAAUxU,EACVyU,SAAS,EACTxa,OAAQ,KACR+Z,SAAUla,KAAK4Z,cAAcM,WAC9BzZ,MAAK,SAAAsX,GAEJ,OADA3E,EAAOxT,OAAOyF,IAAI,wBACda,EAAO0U,kBACA7C,EAEU3E,EAAO0H,MACxB/C,EAGR,GACR,EAEU,YAAAiD,gBAAV,SAA0B9U,QAAA,IAAAA,IAAAA,EAAA,IACtBlG,KAAKwa,kBAAkBtU,GACvB,IAAMgU,EAAWla,KAAK4Z,cAAcM,SAC9B9G,EAASpT,KAAKsa,UAAUJ,GAE9B,OADA9G,EAAOxT,OAAOyF,IAAI,gBACXrF,KAAKya,wBACR,CACIC,SAAUxU,EACVyU,SAAS,EACTxa,OAAQ,KACR+Z,SAAUla,KAAK4Z,cAAcM,WAC9BzZ,MAAK,SAAAsX,GAEJ,OADA3E,EAAOxT,OAAOyF,IAAI,2BACda,EAAO0U,kBACA7C,EAEJ3E,EAAO6H,KACVlD,EAER,GACR,EAEQ,YAAAyC,kBAAR,SAA0BtU,IACA,iBAAXA,GAAuBkJ,MAAMmB,QAAQrK,KAC5C,IAAIV,EAAY7F,EAAWub,4BAA4BzW,OAE/D,EAEU,YAAA0W,YAAV,SAAyBhb,EAA4B+F,GAArD,WAGI,YAHiD,IAAAA,IAAAA,EAAA,IACjDlG,KAAKwa,kBAAkBtU,GACvBlG,KAAKoT,OAAOxT,OAAOyF,IAAI,gBAChBrF,KAAKya,wBACR,CACIC,SAAUxU,EACVyU,SAAS,EACTxa,OAAM,EACN+Z,SAAUla,KAAK4Z,cAAcM,WAC9BzZ,MAAK,SAAAsX,GAEJ,OADA,EAAK3E,OAAOxT,OAAOyF,IAAI,0BACnBa,EAAO0U,kBACAnV,EAAMsS,EAAQ,CACjB9O,KAAM9I,EAAO0a,YACbtN,GAAI,EAAKuM,UAAUhZ,UAGpBX,EAAO8a,KACVlD,EAER,GACR,EAEU,YAAAuC,UAAV,SAAoBJ,GAChB,OAAOA,EAAWla,KAAKoT,OAAO4E,OAC1BhY,KAAKoT,OAAO8E,KACpB,EAEQ,YAAA+B,aAAR,SAAqB,G,IAAEC,EAAQ,WAAEC,EAAY,eAAEH,EAAG,MAE9C,OADeha,KAAKsa,UAAUJ,GAChBL,YAAYM,EAAcH,EAC5C,EAEA,sBAAc,kCAAmB,C,IAAjC,WAEI,OADkCha,KAAKoT,OAAO2E,OAChCC,OAAOC,aACzB,E,gCAEA,sBAAc,iCAAkB,C,IAAhC,WAEI,OADkCjY,KAAKoT,OAAO2E,OAChCG,MAAMD,aACxB,E,gCAEU,YAAAwC,wBAAV,SAAkC,GAAlC,WAAoCC,EAAQ,WAAEva,EAAM,SAAE+Z,EAAQ,WAAES,EAAO,UAC7D1C,EAAgBiC,EAAWla,KAAKob,oBAAsBpb,KAAKqb,mBACjEX,EAAWjV,EAAMwS,EAAgByC,GAAY,CAAC,GAC9C,IAAMtH,EAAS8G,EAAWla,KAAKoT,OAAO4E,OAClChY,KAAKoT,OAAO8E,MAOhB,OANA9E,EAAOxT,OAAOyF,IAAI,WAAYqV,EAAU,SAAUR,EAAU,UAAWS,GAMnEA,EACO3a,KAAKoa,aAAa3Z,MAAK,SAAA6a,GAClB,IAPWvD,E,QAOXwD,EAAuCb,EAAQ,aAAjCc,EAAyBd,EAAQ,qBAEjD5B,EAAqB,EAAK1F,OAAO0F,mBAAmBwC,GACpDG,EAAoBF,GAAgBC,EAO1C,OANAd,EAAS3B,QAAU2B,EAAS3B,SAAWuC,GAElCxC,GAAsB2C,GACvBrI,EAAOxT,OAAO2F,MAAM5F,EAAWqF,oBAAqBkV,GAAUzV,QAG3D1B,QAAQiB,IAAI,CACb0W,EAAiB,SAIbA,EAASgB,UAtBA3D,EAmBG,CACV5a,KAAMud,EAASvd,KAAMsC,MAAOib,EAASjb,MAAO8N,GAAImN,EAASnN,I,EApBjB,E,OAAA,E,OAAA,E,EAAA,W,ynCACzCpN,EAAS,GAAMA,EAAOwb,YAAY5D,IAAlC,M,cAAS,W,aAAmC,SAAM3E,EAAOuI,YAAY5D,I,OAAzB,W,iBAC3D,OADMnX,EAAS,EACR,CAAP,EAAO,IAAIqB,GAAMC,GAAGyJ,KAAKiQ,MAAuB,KAAjBvb,OAAOO,KAAiBK,Y,gRAqB9CyZ,EAASmB,MAEJnB,EAASmB,MADXzI,EAAO0I,oBAAoBpB,EAASvd,KAAM,aAE/CsD,MAAK,SAAAG,GACG,IAAA8a,EAAmB9a,EAAM,GAAfib,EAASjb,EAAM,GAKhC,OAJAwS,EAAOxT,OAAOyF,IAAI,kBAElBqV,EAASgB,SAAWrb,OAAOqb,GAC3BhB,EAASmB,MAAQA,EACVnB,CACX,GACJ,IAEGlU,GAA0CkU,EACrD,EAEU,YAAAqB,cAAV,SAAwBxO,EAAYxL,EAAqBmE,GAAzD,WACI,OAAOlG,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,WACAoN,EACAkI,EAAU3T,MAAMC,IAEpB,OAAO,EAAKwY,aACRpa,EAAQ+F,EAEhB,GACJ,EAEU,YAAA+V,eAAV,SAAyB9e,EAAcoQ,EAAY2O,EAAiBhW,GAApE,WACI,OAAOlG,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,eACAhD,EACAoQ,EACA2O,GAEJ,OAAO,EAAK3B,aACRpa,EAAQ+F,EAEhB,GACJ,EAEU,YAAAiW,kBAAV,SAA4BC,GACpBpc,KAAK4Z,cAAc9Y,UAAYwY,IAC/BtZ,KAAKoT,OAAOxT,OAAO2F,MAAM5F,EAAW0c,yBAA0BD,GAAY3X,OAElF,EAEU,YAAA6X,aAAV,SAAuBF,GACdpc,KAAK4Z,cAAcM,UACpBla,KAAKoT,OAAOxT,OAAO2F,MAAM5F,EAAWiF,cAAewX,GAAY3X,OAEvE,EAEU,YAAA8X,cAAV,SAAwBH,GAChBpc,KAAK4Z,cAAcM,UACnBla,KAAKoT,OAAOxT,OAAO2F,MAAM5F,EAAWgF,eAAgByX,GAAY3X,OAExE,EAEU,YAAA+X,oBAAV,SAA8BJ,GACrBpc,KAAK4Z,cAAc6C,sBACpBzc,KAAKoT,OAAOxT,OAAO2F,MAAM5F,EAAW+c,sBAAuBN,GAAY3X,OAE/E,EAEU,YAAAkY,gBAAV,SAA0BC,EAAgC1W,GAA1D,WACI,OAAOlG,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,mBACAyc,EAAMzf,KACNyf,EAAMrP,GACNkI,EAAU3T,MAAM8a,EAAMV,SACtBzG,EAAU3T,MAAM8a,EAAM7a,QACtB6a,EAAM3T,MAAQ,MAElB,OAAO,EAAKsR,aACRpa,EAAQ+F,EAEhB,GACJ,EAEJ,EArRA,GCfA,cAGI,WAAY6Q,GACR/W,KAAK6c,YAAc,IAAIC,GAAY/F,EACvC,CAaJ,OAXI,YAAA6B,OAAA,SAAOL,EAAiBC,EAAiBuB,EAAoBgD,GACzD,IAAM/F,EAAM,UAAGuB,EAAO,YAAIC,EAAO,sBAAcuB,EAAU,YAAIgD,EAAY,SACzE,OAAO/c,KAAK6c,YAAY7d,IAAIgY,GAAKvW,MAAK,SAACG,GACnC,OAAOA,EAAOoZ,GAClB,GACJ,EAEA,YAAAgD,WAAA,SAAWzE,EAAiBC,GACxB,IAAMxB,EAAM,UAAGuB,EAAO,YAAIC,EAAO,eACjC,OAAOxY,KAAK6c,YAAY7d,IAAIgY,EAChC,EACJ,EAlBA,GCFa,GAAS,CACpBiG,YAAa,6CACbC,mBAAoB,+CCCtB,cAGI,WAAYnG,GACR/W,KAAK6c,YAAc,IAAIC,GAAY/F,EACvC,CAqDJ,OAnDY,YAAAoG,gBAAR,SAAwB3E,EAAiBxB,GACrC,MAAO,UAAe,OAAZwB,EAAmB,QAAUA,GAAO,OAAGxB,EACrD,EAEQ,YAAAoG,kBAAR,SAA0B5E,EAAiBxB,GACvC,MAAO,UAAGwB,EAAO,YAAIxB,EACzB,EAEA,YAAAqG,iBAAA,SAAiB7E,EAAiB7G,GAC9B,IAAMqF,EAAMhX,KAAKmd,gBAAgB3E,EAAS,0BAAmB7G,IAC7D,OAAO3R,KAAK6c,YAAY7d,IAIrBgY,GAAKvW,MAAK,SAAAG,GACT,IAAM0c,EAAoB1c,EAAO0c,kBAC3BC,EAA6D,OAAlCD,EAAkBnb,MAAM,EAAG,GAAcG,SACtEgb,EAAmB,IACnBA,EAEJ,OADA1c,EAAO0c,kBAAoB,IAAIrb,GAAMC,GAAGqb,GACjC3c,CACX,GACJ,EAEA,YAAA4c,aAAA,SAAahF,EAAiBiF,EAAoBC,GAC9C,IAAM1G,EAAMhX,KAAKmd,gBAAgB3E,EAAS,wBAAiBiF,EAAU,2BAAmBC,IACxF,OAAO1d,KAAK6c,YAAY7d,IAASgY,GAAKvW,MAAK,SAAAG,GACvC,OAAOA,EAAOA,MAClB,GACJ,EAEA,YAAAoP,SAAA,SAASwI,EAAiBmF,EAAOC,EAAKjM,GAClC,IAAMqF,EAAMhX,KAAKmd,gBAAgB3E,EAAS,mCAA4BmF,EAAK,gBAAQC,EAAG,mBAAWjM,IACjG,OAAO3R,KAAK6c,YAAY7d,IAASgY,GAAKvW,MAAK,SAAAG,GACvC,OAAOA,EAAOuP,KAClB,GACJ,EAEA,YAAA0N,uBAAA,SAAuBrF,EAAiBsF,EAAmBC,GACvD,IAAM/G,EAAMhX,KAAKod,kBAAkB5E,EAAS,8BAAuBsF,EAAS,wBAAgBC,IAC5F,OAAO/d,KAAK6c,YAAY7d,IAASgY,GAAKvW,MAAK,SAAAG,GACvC,OAAOA,EAAOuP,KAClB,GACJ,EAEA,YAAA6N,4BAAA,SAA4BxF,EAAiBsF,EAAmBC,GAC5D,IAAM/G,EAAMhX,KAAKod,kBAAkB5E,EAAS,wBAAiBsF,EAAS,wBAAgBC,IACtF,OAAO/d,KAAK6c,YAAY7d,IAASgY,GAAKvW,MAAK,SAAAG,GACvC,OAAOA,EAAOqd,OAClB,GACJ,EACJ,EA1DA,GCSaC,GAAU,IANvB,WAIA,EAGAA,GAAQlE,IAAM,IAAImE,GAAW,GAAOlB,aCX7B,IAAMmB,GAAc,SAACpH,GAEG,MAAvBA,EADcA,EAAI9Z,OACF,KAChB8Z,GAAO,KAEXA,GAAO,UACPkH,GAAQ3F,QAAU,IAAI8F,GAAerH,EACzC,EAEasH,GAAmB,SAACtH,GAEF,MAAvBA,EADcA,EAAI9Z,OACF,KAChB8Z,GAAO,KAEXA,GAAO,aACPkH,GAAQK,aAAe,IAAIF,GAAerH,EAC9C,EClBO,SAAS7S,GAAQlF,EAAKyV,GAEzB,OADmBtF,MAAMmB,QAAQmE,GAAQA,EAAOA,EAAKpM,MAAM,MACzCkW,QAAO,SAACC,EAAMC,GAAS,OAAAD,GAAQA,EAAKC,EAAb,GAAoBzf,EACjE,CCCA,+BAEI,KAAAmU,OAAiC,IAAIuL,EAiDzC,QAtCI,YAAAC,eAAA,SAAeC,GACX,OAAO7e,KAAK8e,SAASF,eACjBC,EAER,EAEA,YAAAE,YAAA,SAAYC,GACR,IAAM5L,EAASpT,KAAKoT,OAEd6L,EAAQ,IAAIC,GAAU,CACxBpe,QAASsS,EAAOqF,WAAWI,UAAU,wCACrCqB,UAAU,EACVlT,KAAM,gBACN+S,WAAY,WACb3G,GAEH,OAAO6L,EAAMpF,cAAcpZ,MAAK,SAAAub,GAC5B,OAAOjZ,QAAQiB,IAAI,CACfoP,EAAO4E,OAAOzD,sBAAsByK,GACpCC,EAAmB,YACfjD,EAAS7b,OAAO,iBAG5B,IAAGM,MAAK,SAAAG,GACG,IAAA+S,EAAwB/S,EAAM,GAArBue,EAAeve,EAAM,GAE/Bwe,EAAYzL,EAAQoC,KAAKsJ,MAAK,SAAAC,GAAK,MADlB,uEACkBA,EAAEtJ,OAAO,EAAT,IACzC,IAAKoJ,EACD,MAAM,IAAIhd,MAAM,+BAEpB,IAAMmd,EAAcnM,EAAO8E,MAAMsH,iBAAiBJ,EAAUpJ,OAAO,GAAI,CAAC,YAAY,GAC9EyJ,EAAgBxd,GAAMC,GAAGnB,KAAKwe,GAAeA,EAAc,IAAItd,GAAMC,GAAGqd,GAC9E,OAAO,IAAItd,GAAMC,GAAGid,GAAazd,IAC7B+d,EAER,GACJ,EAEJ,EAnDA,GCYMC,GAAqB,CAAC,EAE5B,cACI,WAAmBC,EAA4BnH,GAA5B,KAAAmH,YAAAA,EAA4B,KAAAnH,QAAAA,CAE/C,CAyDJ,OAvDI,YAAAV,KAAA,sBACI,OAAOoG,GAAQlE,IAAIgD,WACfhd,KAAK2f,YAAa3f,KAAKwY,SACzB/X,MAAK,SAAAG,G,MACH8e,GAAM,EAAKC,eAAY,MAClB,EAAKnH,SAAU,CACZ1X,QAASF,EACToZ,IAAK,CAAC,G,EAGlB,GACJ,EAEA,YAAAnB,UAAA,SAAUnE,GACN,OAAOvQ,GACHub,GAAM1f,KAAK2f,aAAa3f,KAAKwY,SAAS1X,QACtC4T,EAER,EAEA,YAAAkE,OAAA,SAAOmE,EAAsBhD,GAA7B,IACQ6F,EADR,OAWI,QAXyB,IAAA7F,IAAAA,EAAA,UAIrB2F,GAAM1f,KAAK2f,cAAgBD,GAAM1f,KAAK2f,aAAa3f,KAAKwY,UACxDkH,GAAM1f,KAAK2f,aAAa3f,KAAKwY,SAASwB,MAEtC4F,EAAuBF,GAAM1f,KAAK2f,aAAa3f,KAAKwY,SAASwB,IAAID,IAIjE6F,EAAsB,CACtB,IAAMC,EAAiBD,EAAqB7C,GAC5C,GAAI8C,EACA,OAAOrZ,GAAoBqZ,E,CAGnC,OAAO3B,GAAQlE,IAAIpB,OACf5Y,KAAK2f,YACL3f,KAAKwY,QACLuB,EACAgD,GACFtc,MAAK,SAAAG,GAEH,OADA,EAAKkf,OAAO/C,EAAchD,EAAYnZ,GAC/BA,CACX,GACJ,EAEA,YAAAkf,OAAA,SAAO/C,EAAsBhD,EAAoBC,GAC7C,IAAM+F,EAAWL,GAAM1f,KAAK2f,aAAa3f,KAAKwY,SAASwB,IAClD+F,EAAShG,KACVgG,EAAShG,GAAc,CAAC,GAE5BgG,EAAShG,GAAYgD,GAAgB/C,CACzC,EACJ,EA5DA,GClBahZ,GAAsB,WAC/B,MAAM,IAAIoB,MAAM,kBAEpB,ECEA,2BAEI,KAAAgR,OAAiD,IAAIuL,EA6EzD,QAjEI,YAAAqB,mBAAA,SAAmBnB,GAAnB,WACI,OAAO9b,QAAQiB,IAAI,CAAChE,KAAKigB,gBAAgBnC,YAAa9d,KAAKkgB,WAAWC,iBAClEtB,GAAQ,KACRpe,MAAK,SAAAG,GACL,OAAOsd,GAAQK,aAAaP,4BACxB,EAAK5K,OAAO2E,OAAOS,QACnB5X,EAAO,GACPA,EAAO,GAAGmd,aAElB,IAAGtd,MAAK,SAAA2f,GACJ,OAAOA,EAAQC,eACnB,GACJ,EASA,YAAAC,mBAAA,SAAmBzB,GAAnB,WACI,OAAO9b,QAAQiB,IAAI,CAAChE,KAAKugB,iBAAiBzC,YAAa9d,KAAKkgB,WAAWC,iBACnEtB,GAAQ,KACRpe,MAAK,SAAAG,GACL,OAAOsd,GAAQK,aAAaP,4BACxB,EAAK5K,OAAO2E,OAAOS,QACnB5X,EAAO,GACPA,EAAO,GAAGmd,aAElB,IAAGtd,MAAK,SAAA2f,GACJ,OAAOA,EAAQC,eACnB,GACJ,EASA,YAAAtB,YAAA,SAAYF,GAAZ,WACI,OAAO7e,KAAKkgB,WAAWC,iBACnBtB,GAAQ,GACVpe,MAAK,SAAAG,GACH,OAAO,EAAK2f,iBAAiBC,UAAU5f,EAAOmd,aAAc,EAChE,GACJ,EASA,YAAA0C,SAAA,SAAS5B,GAAT,WACI,OAAO7e,KAAKkgB,WAAWC,iBACnBtB,GAAQ,GACVpe,MAAK,SAAAG,GACH,OAAO,EAAKqf,gBAAgBO,UAAU5f,EAAOmd,aAAc,EAC/D,GACJ,EAEJ,EA/EA,GCoBa9b,GAAQ,CACjB4D,UAAW4P,EACX0C,WAAYuI,EACZxe,GAAIye,EACJvI,mBAAoBvZ,Q,2dCzBxB,eAII,WACI+a,EACAxG,EACUwN,GAHd,MAKI,YAAMhH,EAAexG,IAAO,K,OAFlB,EAAAwN,gBAAAA,E,CAGd,CA4EJ,OAtF8B,QAY1B,sBAAc,+BAAgB,C,IAA9B,WACI,OAAO5gB,KAAK4gB,kBAAkBC,gBAClC,E,gCAEA,sBAAc,yBAAU,C,IAAxB,WACI,OAAO7gB,KAAK4gB,kBAAkBE,UAClC,E,gCAEA,sBAAc,uBAAQ,C,IAAtB,WACI,OAAO9gB,KAAK4gB,kBAAkB9B,QAClC,E,gCAGA,YAAAiC,oBAAA,sBACI,OAAI/gB,KAAKghB,iBACExa,GAAexG,KAAKghB,kBAExBhhB,KAAK6gB,iBAAiB1gB,OACzB,cACAH,KAAK4Z,cAAc9Y,SACrBL,MAAK,SAAAN,GACH,OAAOA,EAAO8a,MAClB,IAAGxa,MAAK,SAAAwgB,GACJ,IAAKA,EACD,MAAM,IAAI7e,MAAM,sBAEpB,OAAO,EAAKye,iBAAiB1gB,OACzB,kBAAmB8gB,EAE3B,IAAGxgB,MAAK,SAAAygB,GACJ,OAAOA,EAAsBjG,MACjC,IAAGxa,MAAK,SAAAugB,GAEJ,OADA,EAAKA,iBAAmBA,EACjBA,CACX,GACJ,EAEU,YAAAG,YAAV,SAAsBtC,EAAgBnB,GAAtC,WACI,IAAKmB,EACD,MAAM,IAAIzc,MAAM,uBAEpB,OAAOpC,KAAK8e,SAASsC,YACjBvC,EAAQ,EAAGnB,GACbjd,MAAK,SAAA4gB,GACH,OAAO,EAAKR,iBAAiBS,gBACzBD,EAER,GACJ,EAEU,YAAAE,mBAAV,SAA6B1C,EAAgBjb,EAAe8Z,GAA5D,WACE,IAAKmB,EACD,MAAM,IAAIzc,MAAM,uBAEpB,OAAOpC,KAAK8e,SAASsC,YACjBvC,EAAQjb,EAAO8Z,GACjBjd,MAAK,SAAA4gB,GACH,OAAO,EAAKR,iBAAiBS,gBACzBD,EAER,GACJ,EAEY,YAAAG,gBAAV,SAA0B/D,EAAoBC,EAAwB+D,EAAiBvb,GAAvF,WACI,OAAOlG,KAAK8e,SAAS4C,oBACjBjE,EACAC,EACA+D,GACFhhB,MAAK,SAAAC,GACH,OAAO,EAAKmgB,iBAAiBc,KACzBjhB,EAASwF,EAEjB,GACJ,EACJ,EAtFA,CAA8BgZ,I,2dCI9B,eAEI,WACI/E,EACAD,EACA9G,EACAwO,G,OAEA,YAAM,CACF1H,SAAQ,EACRpZ,QAASqZ,EACTnT,KAAM,aACN+S,WAAY,OACb3G,EAAQwO,IAAa,IAC5B,CAyPJ,OAvQ2B,QAgBvB,YAAAC,WAAA,SAAWC,EAAqB5b,GAAhC,WACI,OAAOlG,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,YACA2hB,GAEJ,OAAO,EAAK3G,YAAoBhb,EAAQ+F,EAC5C,GACJ,EAUA,YAAA6b,aAAA,SAAaD,EAAqB5b,GAAlC,gBAAkC,IAAAA,IAAAA,EAAA,IAC9B,IAAM8b,EAAiB9b,EAAO8b,eAExBC,EAAmBD,EAAiBxb,GAAewb,GAAkBhiB,KAAK+gB,sBAEhF,OAAOhe,QAAQiB,IAAI,CAACie,EAAkBjiB,KAAK6Z,gBAAgBpZ,MAAK,SAAAG,GACrD,IAAAogB,EAA8BpgB,EAAM,GACrCT,EAD+BS,EAAM,GACnBT,OACpB,YACA2hB,EACAd,GAEJ,OAAO,EAAK7F,YAAoBhb,EAAQ+F,EAC5C,GACJ,EAEA,YAAAgc,QAAA,SAAQngB,EAAqBmE,GAA7B,gBAA6B,IAAAA,IAAAA,EAAA,IACzB,IAAM8b,EAAiB9b,EAAO8b,eAEzBA,GAAmBhiB,KAAK4Z,cAAcM,UACvCla,KAAKoT,OAAOxT,OAAO2F,MAAM5F,EAAWsF,oBAAoBR,QAG5D,IAAMwd,EAAmBD,EAAiBxb,GAAewb,GAAkBhiB,KAAK+gB,sBAEhF,OAAOhe,QAAQiB,IAAI,CAACie,EAAkBjiB,KAAK6Z,gBAAgBpZ,MAAK,SAAAG,GACrD,IAAAogB,EAA8BpgB,EAAM,GACrCT,EAD+BS,EAAM,GACnBT,OACpB,UACA6gB,EACAvL,EAAU3T,MAAMC,IAEpB,OAAO,EAAKwY,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAic,WAAA,SAAWjc,GACP,YADO,IAAAA,IAAAA,EAAA,IACAlG,KAAKkiB,QACR7I,GACEnT,EAEV,EAWA,YAAA+X,QAAA,SAAQlc,EAAqB+f,EAAqB5b,GAC9ClG,KAAKsc,aAAa,WAElB,IAAM8F,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAEL,OAAO/B,KAAK6gB,iBAAiB5C,QACzB6D,EACA9hB,KAAK4Z,cAAc9Y,QACnBshB,EACAlc,EAER,EAWA,YAAAmc,eAAA,SAAetgB,EAAqB+f,EAAqBQ,EAA4BC,EAAsBrc,GAA3G,WAGI,OAFAlG,KAAKsc,aAAa,WAEXtc,KAAKoa,aAAa3Z,MAAK,SAACsY,GACX,IAAZA,GACA,EAAK3F,OAAOxT,OAAO2F,MAAM5F,EAAWkF,kBAAkBJ,QAE1D,IAAM2d,EAAc,EAAKhP,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAKL,OAFAmE,EAAOzG,MAAQgW,EAAU3T,MAAMwgB,GAExB,EAAKxB,WAAWuB,eACnB,EAAKzI,cAAc9Y,QACnBshB,EACAN,EACAS,EACArc,EAER,GAEJ,EAEQ,YAAAsc,cAAR,SAAsBzgB,EAAqB+f,EAAqB5b,GAAhE,WAKI,YAL4D,IAAAA,IAAAA,EAAA,IAC5DlG,KAAKsc,aAAa,gBAGlBpW,EAAOzG,MAAQgW,EAAU3T,MAAMC,GACxB/B,KAAK6gB,iBAAiB1gB,OAAO,kBAAmB2hB,GAAarhB,MAAK,SAAAN,GACrE,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAEQ,YAAAuc,qBAAR,SAA6B1gB,EAAqB+f,EAAqBQ,EAA4BC,EAAsBrc,GAAzH,WAGI,YAHqH,IAAAA,IAAAA,EAAA,IACrHlG,KAAKsc,aAAa,uBAEXtc,KAAKoa,aAAa3Z,MAAK,SAACsY,GACX,IAAZA,GACA,EAAK3F,OAAOxT,OAAO2F,MAAM5F,EAAWkF,kBAAkBJ,QAE1D,IAAM2d,EAAc,EAAKhP,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YASL,OANAmE,EAAOzG,MAAQgW,EAAU3T,MACrB2T,EAAUpT,KAAKN,GAAQX,IACnBqU,EAAUpT,KAAKigB,KAIhB,EAAKxB,WAAWuB,eACnB,6CACAD,EACAN,EACAS,EACArc,EAER,GACJ,EAUA,YAAAwc,cAAA,SAAc3gB,EAAqBmE,GAAnC,WAII,OAHAlG,KAAKuc,cAAc,iBAGZvc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,WACAsV,EAAU3T,MAAMC,IAEpB,OAAO,EAAKwY,aAAapa,EAAQ+F,EACrC,GACJ,EAEQ,YAAAyc,cAAR,SAAsBC,EAA6BnB,EAAiBvb,GAApE,gBAAoE,IAAAA,IAAAA,EAAA,IAChE,IAAMwX,EAAiBxX,EAAO2c,mBAC1B3c,EAAO2c,mBAAqBnjB,EAAoBojB,cAEpD,OAAO9iB,KAAK8e,SAAS4C,oBACjBkB,EACAlF,EACA+D,GACFhhB,MAAK,SAAAC,GACH,OAAO,EAAKmgB,iBAAiBc,KACzBjhB,EAASwF,EAEjB,GACJ,EAUA,YAAA6c,aAAA,SAAaH,EAA6B1c,GAGtC,OAFAlG,KAAKsc,aAAa,gBAEXtc,KAAK2iB,cAAcC,GAAqB,EAAO1c,EAC1D,EAYA,YAAA8c,mBAAA,SAAmBJ,EAA6B1c,GAG5C,OAFAlG,KAAKsc,aAAa,sBAEXtc,KAAK2iB,cAAcC,GAAqB,EAAM1c,EACzD,EASA,YAAA+c,iBAAA,SAAiBxF,GACb,OAAOzd,KAAKmhB,YAAY1D,EAAY/d,EAAoBojB,cAC5D,EAWA,YAAAI,SAAA,SAASnhB,EAAqBwL,EAAYrH,GACtC,OAAOlG,KAAK+b,cAAcxO,EAAIxL,EAAQmE,EAC1C,EAEJ,EAvQA,CAA2Bid,I,2dCL3B,eAEI,WAAYC,EAAgDtiB,G,OACxD,YAAM,CACFA,QAASA,EACTkG,KAAM,mBACN+S,WAAY,MACZG,UAAU,GACXkJ,IAAQ,IACf,CAoCJ,OA7CsC,QAWlC,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACvB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC9C,GACJ,EAEA,YAAA+Z,QAAA,SAAQ6D,EAAqB3H,EAAsBkJ,EAAqBnd,GAAxE,WACI,OAAOlG,KAAKG,OACR,aACA2hB,EACA3H,EACAkJ,GACF5iB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAyb,KAAA,SAAK2B,EAAqBpd,GAA1B,WACI,OAAOlG,KAAKG,OAAO,OAAQmjB,GAAa7iB,MAAK,SAAAN,GACzC,OAAO,EAAKoa,aACRpa,EACA+F,EAER,GACJ,EAEA,YAAAob,gBAAA,SAAgBD,GAAhB,WACI,OAAOrhB,KAAKG,OACR,iBAAkBkhB,GACpB5gB,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAAqBhb,EACrC,GACJ,EAEJ,EA7CA,CAAsC+e,ICctC,cAQI,WAAY9L,EAAgDmQ,GACxDvjB,KAAKwjB,aAAepQ,EAAO8E,MAC3BlY,KAAKujB,UAAYA,EACjB,IAAMxL,EAAS3E,EAAO2E,OACtB/X,KAAK+X,OAASA,EACd/X,KAAK6T,mBAAqBkE,EAAOlE,kBACrC,CA2cJ,OAzcY,YAAA4P,aAAR,SAAqBC,EAAqB/P,GACtC,IAAIgQ,GAAY,EAEhB,OAAQD,GACJ,IAAK,qEACL,IAAK,qEACDC,EAAWhQ,EAAQoC,KAAK6N,WACpB,SAAAve,GACI,OAAAA,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,eACZ,uEAAhCxe,EAAI2Q,OAAO,GAAG6N,aADd,IAGR,MAEJ,IAAK,qEACL,IAAK,qEACDF,EAAWhQ,EAAQoC,KAAK6N,WACpB,SAAAve,GACI,OAAAA,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,eACZ,uEAAhCxe,EAAI2Q,OAAO,GAAG6N,aADd,IAGR,MAEJ,QACIF,EAAWhQ,EAAQoC,KAAK6N,WAAU,SAAAve,GAAO,OAAAA,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,aAA5C,IAEjD,GAAIF,EAAW,EACX,MAAM,IAAIvhB,MAAM,4BAEpB,OAAOuhB,CACX,EAEQ,YAAAG,kBAAR,SAA0BJ,EAAqB/P,GAC3C,IAAIoQ,EAAa,GAEjB,OAAQL,GACJ,IAAK,qEACL,IAAK,qEACDK,EAAapQ,EAAQoC,KAAKyI,QACtB,SAACjL,EAAGlO,EAAKzB,GACT,OAAEyB,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,eACV,uEAAhCxe,EAAI2Q,OAAO,GAAG6N,eACdE,EAAWrgB,KAAKE,GAAQmgB,CAF5B,GAEyC,IAE7C,MAEJ,IAAK,qEACL,IAAK,qEACDA,EAAapQ,EAAQoC,KAAKyI,QACtB,SAACjL,EAAGlO,EAAKzB,GACT,OAAEyB,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,eACV,uEAAhCxe,EAAI2Q,OAAO,GAAG6N,eACdE,EAAWrgB,KAAKE,GAAQmgB,CAF5B,GAEyC,IAE7C,MAEJ,IAAK,qEACDA,EAAapQ,EAAQoC,KAAKyI,QACtB,SAACjL,EAAGlO,EAAKzB,GACT,MAAkC,uEAAhCyB,EAAI2Q,OAAO,GAAG6N,eACoB,uEAAhCxe,EAAI2Q,OAAO,GAAG6N,eACdE,EAAWrgB,KAAKE,GAAQmgB,CAF5B,GAEyC,IAE7C,MAEJ,QACIA,EAAapQ,EAAQoC,KAAKyI,QACtB,SAACjL,EAAGlO,EAAKzB,GACT,OAAEyB,EAAI2Q,OAAO,GAAG6N,gBAAkBH,EAAYG,eAC1CE,EAAWrgB,KAAKE,GAAQmgB,CAD5B,GACyC,IAGrD,GAA0B,IAAtBA,EAAW7mB,OACX,MAAM,IAAIkF,MAAM,4BAEpB,OAAO2hB,CACX,EAEA,YAAAC,kBAAA,SAAkBvG,GACd,OAAO1a,QAAQiB,IAAI,CACfhE,KAAKujB,UAAUU,oBACfjkB,KAAKwjB,aAAaU,eAAezG,KAClChd,MAAK,SAAAG,GACJ,MAAO,CACHujB,eAAgBvjB,EAAO,GACvBwjB,cAAexjB,EAAO,GAAG+Q,YAEjC,GACJ,EAEQ,YAAA0S,gBAAR,SAAwBpb,GAEpB,OAAO,IAAIhH,GAAMC,GAAG+G,EAAKkb,gBAAgBziB,IACrC,IAAIO,GAAMC,GAAG+G,EAAKmb,eAE1B,EAEA,YAAAxF,eAAA,SAAenB,GAAf,WACI,OAAOzd,KAAKgkB,kBACRvG,GACFhd,MAAK,SAAAG,GACH,OAAO,EAAKyjB,gBACRzjB,EAER,GACJ,EAaQ,YAAA0jB,iBAAR,SAAyBF,GAAzB,IAEQG,EAFR,OAGI,OAAOvkB,KAAKujB,UAAUiB,uBAClBJ,GACF3jB,MAAK,SAAAkR,GAEH,OADA4S,EAAkB5S,EACX,EAAK4R,UAAUpjB,OAClB,eACAsV,EAAU3T,MAAM6P,GAExB,IAAGlR,MAAK,SAAAN,GACJ,OAAOA,EAAO8a,MAClB,IAAGxa,MAAK,SAAAgkB,GACJ,MAAO,CAEHnH,kBAAmBiH,EAGnB3G,IAAK6G,EAAc7G,IAAI3c,WAEvB0c,MAAO8G,EAAc9G,MAAM1c,WAEnC,GAEJ,EAEQ,YAAAyjB,wBAAR,SAAgCN,GAAhC,WAEI,OADApkB,KAAKwjB,aAAa5jB,OAAOyF,IAAI,yBACtB6Y,GAAQ3F,QAAQ8E,iBACnBrd,KAAK+X,OAAOS,QACZ4L,GACF3jB,MAAK,SAAAkkB,GAEH,GADA,EAAKnB,aAAa5jB,OAAOyF,IAAI,wBAAyBsf,KACjDA,GAAgBA,EAAYhH,OAAUgH,EAAY/G,KAAQ+G,EAAYrH,mBACvE,MAAMlb,MAAM,qBAEhB,OAAOuiB,CACX,IAAGrR,OAAM,SAAAqF,GAEL,OADA,EAAK6K,aAAa5jB,OAAOyF,IAAI,sBAAuBsT,GAC7C,EAAK2L,iBAAiBF,EACjC,GACJ,EAEQ,YAAAQ,cAAR,SAAsBR,EAAuBK,GACzC,OAAOvR,GAAUF,gBACbhT,KAAKwjB,aACLlhB,SAASmiB,EAAc9G,MAAO,IAC9Brb,SAASmiB,EAAc7G,IAAK,IAC5Btb,SAAS8hB,EAAgB,GAAI,IAErC,EAEQ,YAAAS,qBAAR,SAA6BT,EAAuBK,GAApD,WAEI,OAAOvG,GAAQ3F,QAAQvI,SACnBhQ,KAAK+X,OAAOS,QACZiM,EAAc9G,MACd8G,EAAc7G,IACdwG,GACF3jB,MAAK,SAAAqkB,GACH,IAAKA,EACD,MAAM1iB,MAAM,qBAGhB,OADA,EAAKohB,aAAa5jB,OAAOyF,IAAI,0BACtByf,CACX,IAAGxR,OAAM,SAAAC,GACL,OAAO,EAAKqR,cAAcR,EAAeK,EAC7C,GACJ,EAEQ,YAAAM,oBAAR,SAA4BC,EAAkBtH,GAA9C,WAEI,OAAOQ,GAAQ3F,QAAQiF,aACnBxd,KAAK+X,OAAOS,QAASwM,EAAUtH,GACjCjd,MAAK,SAAAwkB,GACH,IAAKA,EACD,MAAM7iB,MAAM,qBAGhB,OADA,EAAKohB,aAAa5jB,OAAOyF,IAAI,yBACtB4f,CACX,IAAG3R,OAAM,SAAAC,GACL,OAAO,EAAKmO,oBAAoBsD,EAAUtH,GAAgB,EAC9D,GACJ,EAEA,YAAAgE,oBAAA,SAAoBjE,EAAoBiG,EAAqBjC,EAAiB7d,GAA9E,IAUQwgB,EACAK,EACA9Q,EACAC,EACAkR,EAdR,OAMI,QAN0E,IAAAlhB,IAAAA,EAAA,GAEtE6d,IAAWvD,GAAQ3F,SACnB,IAAI/S,EAAY7F,EAAWmF,gBAAgBL,QAG3Cb,EAAQ,EACR,MAAM,IAAIxB,MAAM,wCASpB,OAAIqf,EACOzhB,KAAK+kB,oBAAoBtH,EAAYiG,GAGzC1jB,KAAKgkB,kBACRvG,GACFhd,MAAK,SAAAykB,GACH,IAAK,EAAKb,gBAAgBa,GACtB,MAAM,IAAI9iB,MACN,qDAQR,OAHAgiB,EAAgBc,EAAUd,cAGnBrhB,QAAQiB,IAAI,CACf,EAAKwf,aAAajP,sBAAsBkJ,GACxC,EAAK+F,aAAa2B,wBAAwBf,IAElD,IAAG3jB,MAAK,SAAAG,GAGJ,OAFC+S,EAAkB/S,EAAM,GAAfgT,EAAShT,EAAM,GAElB,EAAK0jB,iBAAiBF,EACjC,IAAG3jB,MAAK,SAAA2kB,GAGJ,OAFAX,EAAgBW,EAET,EAAKR,cAAcR,EAAeK,EAC7C,IAAGhkB,MAAK,SAAA4kB,GAGJ,OAFAP,EAAaO,EAENnS,GAAUQ,gBACbC,EACAC,EACA,EAAK4P,aACL,EAAK3P,mBAEb,IAAGpT,MAAK,SAAC6kB,GAIL,GAAI1hB,EAAQ,EAAG,CACX,IAAMmgB,EAAa,EAAKD,kBACpBJ,EAAa/P,GAGjB,GAAI/P,GAASmgB,EAAW7mB,OACpB,MAAM,IAAIkF,MAAM,kEAGpB,OAAO,EAAKmjB,eACRd,EAAcnH,kBAAkBnc,WAChC2jB,EACAV,EACAxQ,EAAM4R,UACN9oB,OAAOS,KAAKyW,EAAM6R,iBAAiBtjB,MAAM,GAAI,OAC7CzF,OAAOS,KAAKyW,EAAM8R,aAAavjB,MAAM,GAAI,OACzC+Q,GAAU2B,gBAAgBlB,GAC1B2R,EAAapQ,YACboQ,EAAa5Q,KACbqP,EAAWngB,G,CAKnB,IAAM+f,EAAW,EAAKF,aAClBC,EAAa/P,GAGjB,OAAO,EAAK4R,eACRd,EAAcnH,kBAAkBnc,WAChC2jB,EACAV,EACAxQ,EAAM4R,UACN9oB,OAAOS,KAAKyW,EAAM6R,iBAAiBtjB,MAAM,GAAI,OAC7CzF,OAAOS,KAAKyW,EAAM8R,aAAavjB,MAAM,GAAI,OACzC+Q,GAAU2B,gBAAgBlB,GAC1B2R,EAAapQ,YACboQ,EAAa5Q,KACbiP,EAER,GACJ,EAEA,YAAAgC,6BAAA,SAA6BlI,EAAoBiG,EAAqBjC,GAAtE,IAMQ2C,EACAK,EACA9Q,EACAC,EACAkR,EAVR,OAYI,OAVIrD,IAAWvD,GAAQ3F,SACnB,IAAI/S,EAAY7F,EAAWmF,gBAAgBL,QASxCzE,KAAKgkB,kBACRvG,GACFhd,MAAK,SAAAykB,GACH,IAAKzD,IAAW,EAAK4C,gBAAgBa,GACjC,MAAM,IAAI9iB,MACN,qDAQR,OAHAgiB,EAAgBc,EAAUd,cAGnBrhB,QAAQiB,IAAI,CACf,EAAKwf,aAAajP,sBAAsBkJ,GACxC,EAAK+F,aAAa2B,wBAAwBf,IAElD,IAAG3jB,MAAK,SAAAG,GAGJ,OAFC+S,EAAkB/S,EAAM,GAAfgT,EAAShT,EAAM,GAGrB6gB,EAAS,EAAKiD,wBAAwBN,GAClC,EAAKE,iBAAiBF,EAElC,IAAG3jB,MAAK,SAAA2kB,GAGJ,OAFAX,EAAgBW,EAGZ3D,EAAS,EAAKoD,qBAAqBT,EAAeK,GAC9C,EAAKG,cAAcR,EAAeK,EAE9C,IAAGhkB,MAAK,SAAA4kB,GAGJ,OAFAP,EAAaO,EAENnS,GAAUQ,gBACbC,EACAC,EACA,EAAK4P,aACL,EAAK3P,mBAEb,IAAGpT,MAAK,SAAC6kB,GAOL,IANA,IAGMM,EAAqB,GAGJ,MANJ,EAAK9B,kBACpBJ,EAAa/P,GAKM,eAAY,CAA9B,IAAMgQ,EAAQ,KACfiC,EAASliB,KACL,EAAK6hB,eACDd,EAAcnH,kBAAkBnc,WAChC2jB,EACAV,EACAxQ,EAAM4R,UACN9oB,OAAOS,KAAKyW,EAAM6R,iBAAiBtjB,MAAM,GAAI,OAC7CzF,OAAOS,KAAKyW,EAAM8R,aAAavjB,MAAM,GAAI,OACzC+Q,GAAU2B,gBAAgBlB,GAC1B2R,EAAapQ,YACboQ,EAAa5Q,KACbiP,G,CAKZ,OAAOiC,CACX,GACJ,EAEQ,YAAAL,eAAR,SACIM,EACA7S,EACArB,EACA6T,EACAC,EACAC,EACA/R,EACAmS,EACApR,EACAiP,GAEA,OAAO5S,GAAWQ,YACd,YAAW,CACPsU,EACA7S,EACArB,EACA6T,EACAzU,GAAWQ,YAAYkU,GACvB1U,GAAWQ,YAAYmU,GACvB3U,GAAWQ,YAAYoC,GACvB5C,GAAWQ,YAAY,YAAWuU,IAClC/U,GAAWQ,YAAY7U,OAAO6J,OAAO,CAAC7J,OAAOS,KAAK,KAAM,OAAQuX,KAChEiP,IAGZ,EAEA,YAAAvC,YAAA,SAAY3D,EAAY7Z,EAAO8f,GAA/B,IACQS,EACAxQ,EACAC,EAHR,OAKI,OAAO7Q,QAAQiB,IAAI,CACfhE,KAAKujB,UAAUU,oBACfjkB,KAAKwjB,aAAajP,sBAAsBkJ,KACzChd,MAAK,SAAAG,GAGJ,OAFAujB,EAAiBvjB,EAAO,GACxB+S,EAAU/S,EAAO,GACV,EAAK4iB,aAAa2B,wBACrBxR,EAAQhC,YAEhB,IAAGlR,MAAK,SAAAslB,GAKJ,OAJAnS,EAAQmS,EACH,EAAK1B,gBAAgB,CAAEF,eAAgBA,EAAgBC,cAAezQ,EAAQhC,eAC/E,EAAK6R,aAAa5jB,OAAO2F,MAAM5F,EAAWoF,uBAAuBN,QAE9DyO,GAAUQ,gBACbC,EACAC,EACA,EAAK4P,aACL,EAAK3P,mBAEb,IAAGpT,MAAK,SAAC6kB,GACL,IAAI3B,EACEqC,EAAY,GAalB,OAZAV,EAAa5Q,KAAKN,SAAQ,SAAA6R,GACtBD,EAAUtiB,KAAKhH,OAAOS,KAAK,KAAO8oB,EAAO,IAAMhlB,SAAS,IAAK,QAC7D+kB,EAAUtiB,KAAKhH,OAAOS,KAAK,KAAO8oB,EAAO,IAAMhlB,SAAS,IAAK,OACjE,IAEI2C,EAAQ,IAER+f,EADmB,EAAKG,kBAAkBJ,EAAa/P,GACjC/P,IAG1B+f,EAAW,EAAKF,aAAaC,EAAa/P,GAEnC,EAAK6P,aAAa0C,aACrBvS,EAAQhC,YAAaZ,GAAWQ,YAAY7U,OAAO6J,OAAOyf,IAAarC,EAE/E,GACJ,EACJ,EAzdA,G,2dCZA,eAEI,WAAYP,EAAgDtiB,G,OACxD,YAAM,CACFA,QAASA,EACTkG,KAAM,YACNkT,UAAU,GACXkJ,IAAQ,IACf,CA8DJ,OAtE+B,QAU3B,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACvB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC9C,GACJ,EAEA,YAAA+f,kBAAA,sBACI,OAAOjkB,KAAKG,OAAO,qBAAqBM,MAAK,SAAAN,GACzC,OAAOA,EAAO8a,KAAa,CAAC,EAAG,EAAK7H,OAAO2E,OAAOoO,uBAAyB,OAC/E,GACJ,EAEM,YAAA3B,uBAAN,SAA6B4B,G,wqCAUV,OATTC,EAAS,IAAIpkB,GAAMC,GAAG,GACtBokB,EAAS,IAAIrkB,GAAMC,GAAG,GACtBqkB,EAAqB,IAAItkB,GAAMC,GAAG,KAExCkkB,EAAmB,IAAInkB,GAAMC,GAAGkkB,GAE5BzI,EAAQ0I,EAGG,GAAMrmB,KAAKG,OAAO,uB,OACN,SADZ,SACyB8a,Q,OAAlCuL,EAAqB,SACvB5I,EAAM,IAAI3b,GAAMC,GAAGskB,GAAoBjlB,IACvCglB,G,wBAKG5I,EAAMnc,IAAIoc,GACTD,EAAM/b,GAAGgc,IACT6I,EAAM9I,EACN,QAEE+I,EAAM/I,EAAMvc,IAAIwc,GAAKrc,IAAI+kB,GACJ,GAAMtmB,KAAKG,OAClC,eACAumB,EAAIplB,IAAIilB,GAAoBtlB,cARf,M,OAUG,SAJO,SAIkBga,Q,OAK7C,OALM0J,EAAc,SAEdgC,EAAc,IAAI1kB,GAAMC,GAAGyiB,EAAYhH,OACvCiJ,EAAY,IAAI3kB,GAAMC,GAAGyiB,EAAY/G,KAEvC+I,EAAYnlB,IAAI4kB,IAAqBA,EAAiB5kB,IAAIolB,IAE1DH,EAAMC,EACN,QACOC,EAAYhlB,GAAGykB,GAEtBxI,EAAM8I,EAAIrlB,IAAIglB,GACPO,EAAUnlB,GAAG2kB,KAEpBzI,EAAQ+I,EAAItlB,IAAIilB,I,cAGxB,MAAO,CAAP,EAAOI,EAAInlB,IAAIilB,I,qSAGvB,EAtEA,CAA+BrH,I,2dCG/B,eAGI,WACI/E,EACAD,EACA9G,EACAwO,G,OAEA,YAAM,CACF1H,SAAQ,EACRpZ,QAASqZ,EACTnT,KAAM,cACN+S,WAAY,OACb3G,EAAQwO,IAAa,IAC5B,CAqUJ,OApV4B,QAiBhB,YAAAiF,cAAR,SAAsBC,GAClB,GAAIA,EAAS5pB,OAAS,GAClB,MAAM,IAAIkF,MAAM,uCAEpB,OAAO0kB,EAAS7iB,KAAI,SAAAiY,GAChB,OAAOzG,EAAU3T,MAAMoa,EAC3B,GACJ,EAUA,YAAA6K,eAAA,SAAejF,EAAqBkF,GAApC,WACI,OAAOhnB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,YACA2hB,GAEJ,OAAO,EAAK3G,YAAoBhb,EAAQ6mB,EAC5C,IAAGvmB,MAAK,SAAAwmB,GACJ,OAAO5mB,OAAO4mB,EAClB,GACJ,EAWA,YAAAC,yBAAA,SAAyBtjB,EAAeke,EAAqBkF,GAA7D,WACI,OAAOhnB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,sBACA2hB,EACAle,GAGJ,OAAO,EAAKuX,YAAoBhb,EAAQ6mB,EAC5C,GACJ,EAUA,YAAAG,aAAA,SAAarF,EAAqBsF,GAAlC,WACI,YAD8B,IAAAA,IAAAA,EAAA,KACvBpnB,KAAK+mB,eAAejF,GAAarhB,MAAK,SAAAwmB,IACzCA,EAAQ5mB,OAAO4mB,IACHG,IACRH,EAAQG,GAGZ,IADA,IAAMxhB,EAAW,GACR+C,EAAI,EAAGA,EAAIse,EAAOte,IACvB/C,EAASlC,KACL,EAAKwjB,yBAAyBve,EAAGmZ,IAGzC,OAAO/e,QAAQiB,IACX4B,EAER,GACJ,EAEA,YAAAyhB,WAAA,SAAWnL,EAAiBhW,GAA5B,WAGI,OAFAlG,KAAKsc,aAAa,cAEXtc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,cACA+b,GAEJ,OAAOnZ,QAAQiB,IAAI,CACf,EAAKmX,YAAoBhb,EAAQ+F,GACjC,EAAK6a,wBACNtgB,MAAK,SAAAG,GACJ,OAAOA,EAAO,KAAOA,EAAO,EAChC,GACJ,GACJ,EAEA,YAAA0mB,cAAA,SAAcxF,EAAqB5b,GAAnC,WAGI,OAFAlG,KAAKsc,aAAa,iBAEXvZ,QAAQiB,IAAI,CAAChE,KAAK6Z,cAAe7Z,KAAK+gB,wBAAwBtgB,MAAK,SAAAG,GAC/D,IAAAob,EAA8Bpb,EAAM,GAA1BogB,EAAoBpgB,EAAM,GACrCT,EAAS6b,EAAS7b,OACpB,mBACA2hB,EACAd,GAEJ,OAAO,EAAK7F,YAAqBhb,EAAQ+F,EAC7C,GAEJ,EAEA,YAAAgc,QAAA,SAAQhG,EAAsBhW,GAA9B,WAGI,OAFAlG,KAAKsc,aAAa,WAEXvZ,QAAQiB,IAAI,CAAChE,KAAK6Z,cAAe7Z,KAAK+gB,wBAAwBtgB,MAAK,SAAAG,GAC/D,IAAAob,EAA8Bpb,EAAM,GAA1BogB,EAAoBpgB,EAAM,GACrCT,EAAS6b,EAAS7b,OACpB,UACA6gB,EACAvL,EAAU3T,MAAMoa,IAEpB,OAAO,EAAK3B,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAqhB,WAAA,SAAWrhB,GAAX,WAGI,OAFAlG,KAAKsc,aAAa,cAEXvZ,QAAQiB,IAAI,CAAChE,KAAK6Z,cAAe7Z,KAAK+gB,wBAAwBtgB,MAAK,SAAAG,GAC/D,IAAAob,EAA8Bpb,EAAM,GAA1BogB,EAAoBpgB,EAAM,GACrCT,EAAS6b,EAAS7b,OACpB,oBACA6gB,GACA,GAEJ,OAAO,EAAKzG,aAAapa,EAAQ+F,EACrC,GACJ,EAGA,YAAA+X,QAAA,SAAQ/B,EAAsB4F,EAAqB5b,GAC/ClG,KAAKsc,aAAa,WAElB,IAAM8F,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMoa,IACjB,CAAC,YAEL,OAAOlc,KAAK6gB,iBAAiB5C,QACzB6D,EACA9hB,KAAK4Z,cAAc9Y,QACnBshB,EACAlc,EAER,EAEA,YAAAshB,YAAA,SAAYV,EAAyBhF,EAAqB5b,GACtDlG,KAAKsc,aAAa,eAElB,IAAMmL,EAAcznB,KAAK6mB,cAAcC,GAEjC1E,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgU,GACD,CAAC,cAEL,OAAOznB,KAAK6gB,iBAAiB5C,QACzB6D,EACA9hB,KAAK4Z,cAAc9Y,QACnBshB,EACAlc,EAER,EAEA,YAAAwc,cAAA,SAAcxG,EAAsBhW,GAApC,WAII,OAHAlG,KAAKuc,cAAc,iBAGZvc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,WACAsV,EAAU3T,MAAMoa,IAEpB,OAAO,EAAK3B,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAwhB,0BAAA,SAA0BxL,EAAsBhW,GAAhD,WAII,OAHAlG,KAAKuc,cAAc,6BAGZvc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,uBACAsV,EAAU3T,MAAMoa,IAEpB,OAAO,EAAK3B,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAyhB,kBAAA,SAAkBb,EAAyB5gB,GAA3C,WACIlG,KAAKuc,cAAc,qBAEnB,IAAMkL,EAAcznB,KAAK6mB,cAAcC,GAEvC,OAAO9mB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,gBACAsnB,GAEJ,OAAO,EAAKlN,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAA6c,aAAA,SAAaH,EAA6B1c,GAA1C,WAGI,OAFAlG,KAAKsc,aAAa,gBAEXtc,KAAK8e,SAAS4C,oBACjBkB,EACAljB,EAAoBkoB,gBACpB,GACFnnB,MAAK,SAAAC,GACH,OAAO,EAAKmgB,iBAAiBc,KACzBjhB,EAASwF,EAEjB,GACJ,EAEA,YAAA2hB,oBAAA,SAAoBjF,EAA6Bhf,EAAesC,GAAhE,WAGE,OAFAlG,KAAKsc,aAAa,gBAEXtc,KAAK8e,SAAS4C,oBACjBkB,EACAljB,EAAoBkoB,gBACpB,EACAhkB,GACFnD,MAAK,SAAAC,GACH,OAAO,EAAKmgB,iBAAiBc,KACzBjhB,EAASwF,EAEjB,GACF,EAqBA,YAAA8c,mBAAA,SAAmBJ,EAA6B1c,GAAhD,WAGI,OAFAlG,KAAKsc,aAAa,sBAEXtc,KAAK8e,SAAS4C,oBACjBkB,EACAljB,EAAoBkoB,gBACpB,GACFnnB,MAAK,SAAAC,GACH,OAAO,EAAKmgB,iBAAiBc,KACzBjhB,EAASwF,EAEjB,GACJ,EAiBA,YAAA+c,iBAAA,SAAiBpE,GACb,OAAO7e,KAAKmhB,YACRtC,EAAQnf,EAAoBkoB,eAEpC,EAEA,YAAAE,qBAAA,SAAqBjJ,GACjB,OAAO7e,KAAKmhB,YACRtC,EAAQnf,EAAoBqoB,oBAEpC,EAEA,YAAAC,wBAAA,SAAwBnJ,EAAgBjb,GACpC,OAAO5D,KAAKuhB,mBACR1C,EAAQjb,EAAOlE,EAAoBkoB,eAE3C,EAYA,YAAA1E,SAAA,SAAShH,EAAiB/e,EAAcoQ,EAAYrH,GAChD,OAAOlG,KAAKic,eACR9e,EACAoQ,EACA2O,EACAhW,EAER,EAEJ,EApVA,CAA4Bid,I,2dCD5B,eAQI,WACIhJ,EACAD,EACA9G,EACAwO,G,OAEA,YAAM,CACF1H,SAAQ,EACRpZ,QAASqZ,EACTnT,KAAM,eACN+S,WAAY,OACb3G,EAAQwO,IAAa,IAE5B,CA+TJ,OApV6B,QAIzB,sBAAI,4BAAa,C,IAAjB,WACI,OAAO5hB,KAAKoT,OAAO2E,OAAOkQ,SAAW,CAAC,CAC1C,E,gCAiBQ,YAAAC,YAAR,SAAoBzoB,GAChB,IAAM0oB,EAAYnoB,KAAKooB,cACvB,OAAID,EAAU1oB,GACH+G,GAAe2hB,EAAU1oB,IAG7BO,KAAKoT,OAAOyF,UAAUpZ,EACjC,EAWA,YAAAoiB,WAAA,SAAWC,EAAqB5F,EAAsBhW,GAAtD,WACI,OAAOlG,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,YACA2hB,EACArM,EAAU3T,MAAMoa,IAEpB,OAAO,EAAKf,YAAoBhb,EAAQ+F,EAC5C,GACJ,EAUA,YAAAohB,cAAA,SAAcxF,EAAqB5b,GAAnC,WAGI,OAFAlG,KAAKsc,aAAa,iBAEXvZ,QAAQiB,IAAI,CAAChE,KAAK6Z,cAAe7Z,KAAK+gB,wBAAwBtgB,MAAK,SAAAG,GAC/D,IAAAob,EAA8Bpb,EAAM,GAA1BogB,EAAoBpgB,EAAM,GACrCT,EAAS6b,EAAS7b,OACpB,mBACA2hB,EACAd,GAEJ,OAAO,EAAK7F,YAAqBhb,EAAQ+F,EAC7C,GAEJ,EAEQ,YAAAmiB,YAAR,SAAoBC,EAA0CpiB,GAA9D,WAGI,OAFAlG,KAAKsc,aAAa,WAEXvZ,QAAQiB,IAAI,CAAChE,KAAK6Z,cAAeyO,IAA0B7nB,MAAK,SAAAG,GAC5D,IAAAob,EAA8Bpb,EAAM,GAA1BogB,EAAoBpgB,EAAM,GACrCT,EAAS6b,EAAS7b,OACpB,oBACA6gB,GACA,GAEJ,OAAO,EAAKzG,aAAapa,EAAQ+F,EACrC,GACJ,EASA,YAAAqhB,WAAA,SAAWrhB,GAGP,OAFAlG,KAAKsc,aAAa,WAEXtc,KAAKqoB,YACRroB,KAAK+gB,sBAAuB7a,EAEpC,EASA,YAAAqiB,sBAAA,SAAsBriB,GAGlB,OAFAlG,KAAKsc,aAAa,sBAEXtc,KAAKqoB,YACRroB,KAAKkoB,YAFW,mDAEehiB,EAEvC,EAUA,YAAA+X,QAAA,SAAQrB,EAA+B1W,GAEnC,OADAlG,KAAKsc,aAAa,WACXtc,KAAKwnB,YAAY,CACpBgB,QAAS,CAAC5L,EAAM7a,QAChB+kB,SAAU,CAAClK,EAAMV,SACjB4F,YAAalF,EAAMkF,YACnB7Y,KAAM2T,EAAM3T,MACb/C,EACP,EAUA,YAAAshB,YAAA,SAAY5K,EAAoC1W,GAC5ClG,KAAKsc,aAAa,eAEV,IAAAwK,EAAyClK,EAAK,SAApC4L,EAA+B5L,EAAK,QAA3B3T,EAAsB2T,EAAK,KAArBkF,EAAgBlF,EAAK,YAChD6L,EAAWhT,EAAU3T,MAAM,GAC3BsgB,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CACIqT,EAAS7iB,KAAI,SAAAqG,GAAK,OAAAmL,EAAU3T,MAAMwI,EAAhB,IAClBke,EAAQvkB,KAAI,SAAAvF,GAAK,OAAA+W,EAAU3T,MAAMpD,EAAhB,IACjBuK,GAAQwf,GAEZ,CAAC,YAAa,YAAa,UAG/B,OAAOzoB,KAAK6gB,iBAAiB5C,QACzB6D,EACA9hB,KAAK4Z,cAAc9Y,QACnBshB,EACAlc,EAGR,EAWA,YAAAwc,cAAA,SAAcxG,EAAsBna,EAAqBmE,GAAzD,WAGI,OAFAlG,KAAKuc,cAAc,iBAEZvc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,iBACAsV,EAAU3T,MAAMoa,GAChBzG,EAAU3T,MAAMC,IAEpB,OAAO,EAAKwY,aAAapa,EAAQ+F,EACrC,GACJ,EAWA,YAAAyhB,kBAAA,SAAkBb,EAAyB0B,EAAwBtiB,GAAnE,WACIlG,KAAKuc,cAAc,qBAEnB,IAAMkL,EAAcX,EAAS7iB,KAAI,SAAAqG,GAC7B,OAAOmL,EAAU3T,MAAMwI,EAC3B,IACMoe,EAAeF,EAAQvkB,KAAI,SAAAqG,GAC7B,OAAOmL,EAAU3T,MAAMwI,EAC3B,IAEA,OAAOtK,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,gBACAsnB,EACAiB,GAEJ,OAAO,EAAKnO,aAAapa,EAAQ+F,EACrC,GACJ,EAUA,YAAA6c,aAAA,SAAaH,EAA6B1c,GAGtC,OAFAlG,KAAKsc,aAAa,gBAEXtc,KAAKwhB,gBACRoB,EACAljB,EAAoBipB,iBACpB,EACAziB,EAER,EAYA,YAAA8c,mBAAA,SAAmBJ,EAA6B1c,GAG5C,OAFAlG,KAAKsc,aAAa,sBAEXtc,KAAKwhB,gBACRoB,EACAljB,EAAoBipB,iBACpB,EACAziB,EAER,EAUA,YAAA0iB,iBAAA,SAAiBhG,EAA6B1c,GAI1C,OAHAlG,KAAKsc,aAAa,oBAGXtc,KAAKwhB,gBACRoB,EACAljB,EAAoBmpB,sBACpB,EACA3iB,EAER,EAYA,YAAA4iB,uBAAA,SAAuBlG,EAA6B1c,GAIhD,OAHAlG,KAAKsc,aAAa,0BAGXtc,KAAKwhB,gBACRoB,EACAljB,EAAoBmpB,sBACpB,EACA3iB,EAER,EASA,YAAA+c,iBAAA,SAAiBpE,GACb,OAAO7e,KAAKmhB,YACRtC,EAAQnf,EAAoBipB,gBAEpC,EASA,YAAAb,qBAAA,SAAqBjJ,GACjB,OAAO7e,KAAKmhB,YACRtC,EAAQnf,EAAoBmpB,qBAEpC,EAUA,YAAA3F,SAAA,SAAStG,EAAgC1W,GACrC,OAAOlG,KAAK2c,gBACRC,EAAO1W,EAEf,EACJ,EApVA,CAA6Bid,I,2dCJ7B,eAEI,WAAYC,EAAgDtiB,G,OACxD,YAAM,CACFA,QAASA,EACTkG,KAAM,aACN+S,WAAY,MACZG,UAAU,GACXkJ,IAAQ,IACf,CA0BJ,OAnCgC,QAW5B,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACvB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC9C,GACJ,EAEA,YAAAme,eAAA,SACIlI,EACA4O,EACAjH,EACAS,EACArc,GALJ,WAOI,OAAOlG,KAAKG,OACR,gBACAga,EACA4O,EACAjH,EACAS,GACF9hB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAEJ,EAnCA,CAAgCgZ,I,2dCahC,4B,8CAiGA,QAjG+B,QAK3B,YAAApH,KAAA,SAAKC,GAAL,WACU3E,EAASpT,KAAKoT,OAEpB,OAAOA,EAAO0E,KAAKC,GAAQtX,MAAK,SAAA8S,GAC5B,IAAMyV,EAAmB5V,EAAO4V,iBAChC5V,EAAO2E,OAASA,EAASlZ,OAAO6G,OAC5B,CACImb,iBAAkBmI,EAAiBC,sBACnC1F,UAAWnQ,EAAO8V,oBAAoBC,eACtCrI,WAAYkI,EAAiBI,YAEjCrR,GAGJ,EAAK8I,iBAAmB,IAAIwI,GACxB,EAAKjW,OACL2E,EAAO8I,kBAGX,IAAM0C,EAAY,IAAI+F,GAClB,EAAKlW,OACL2E,EAAOwL,WAaX,OAVA,EAAKzE,SAAW,IAAIyK,GAChB,EAAKnW,OACLmQ,GAGJ,EAAKzC,WAAa,IAAIsI,GAClB,EAAKhW,OACL2E,EAAO+I,YAGJ,CACX,GACJ,EAEA,YAAA0I,MAAA,SAAMrP,EAAcD,GAChB,OAAO,IAAIuP,GACPtP,EACAD,EACAla,KAAKoT,OACLpT,KAAK0pB,cAAcvmB,KAAKnD,MAEhC,EAEA,YAAA2pB,OAAA,SAAOxP,EAAcD,GACjB,OAAO,IAAI0P,GACPzP,EACAD,EACAla,KAAKoT,OACLpT,KAAK0pB,cAAcvmB,KAAKnD,MAEhC,EAEA,YAAAioB,QAAA,SAAQ9N,EAAcD,GAClB,OAAO,IAAI2P,GACP1P,EACAD,EACAla,KAAKoT,OACLpT,KAAK0pB,cAAcvmB,KAAKnD,MAEhC,EAEA,YAAA8pB,aAAA,SAAa/nB,EAAqB+f,EAAqB5b,GACnD,OAAO,IAAIujB,GACP,IAAI,EAAMzpB,KAAKoT,OACfpT,KAAK0pB,cAAcvmB,KAAKnD,OACX,cAAE+B,EAAQ+f,EAAa5b,EAC5C,EAEA,YAAA6jB,oBAAA,SACIhoB,EACA+f,EACAQ,EACAC,EACArc,GAEA,OAAO,IAAIujB,GACP,IAAI,EAAMzpB,KAAKoT,OACfpT,KAAK0pB,cAAcvmB,KAAKnD,OACJ,qBAAE+B,EAAQ+f,EAAaQ,EAAeC,EAAcrc,EAChF,EAEQ,YAAAwjB,cAAR,WACI,MAAO,CACH5K,SAAU9e,KAAK8e,SACf+B,iBAAkB7gB,KAAK6gB,iBACvBC,WAAY9gB,KAAK8gB,WAEzB,EACJ,EAjGA,CAA+BkJ,ICblBrnB,GAAgB,CACzBV,MAAOA,GACPM,IAAG,EACH0nB,UAAS,I,2dCFb,eAEI,WACIrQ,EACAxG,EACU8W,GAHd,MAKI,YAAMtQ,EAAexG,IAAO,K,OAFlB,EAAA8W,kBAAAA,E,CAGd,CAkBJ,OA1BgC,QAU5B,sBAAc,2BAAY,C,IAA1B,WACI,OAAOlqB,KAAKkqB,oBAAoBC,YACpC,E,gCAEA,sBAAc,2BAAY,C,IAA1B,WACI,OAAOnqB,KAAKkqB,oBAAoBE,YACpC,E,gCAEA,sBAAc,0BAAW,C,IAAzB,WACI,OAAOpqB,KAAKkqB,oBAAoBG,WACpC,E,gCAEA,sBAAc,yBAAU,C,IAAxB,WACI,OAAOrqB,KAAKkqB,oBAAoBhK,UACpC,E,gCAEJ,EA1BA,CAAgChB,I,2dCIhC,eAEE,WAAYkE,EAAkDtiB,EAAiBoZ,G,OAC7E,YACE,CACEpZ,QAASA,EACTkG,KAAM,qBACN+S,WAAY,QACZG,SAAUA,GAEZkJ,IACD,IACH,CA8BF,OA1CwC,QActC,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACzB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC7B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC5C,GACF,EAYA,YAAAomB,YAAA,SACEC,EACAxoB,EACAyoB,EACAtkB,GAJF,WAME,OAAOlG,KAAKG,OAAO,cAAeoqB,EAAW9U,EAAU3T,MAAMC,GAASyoB,GAA2B/pB,MAC/F,SAAAN,GACE,OAAO,EAAKoa,aAAapa,EAAQ+F,EACnC,GAEJ,EACF,EA1CA,CAAwCgZ,I,2dCExC,eAEI,WACI/E,EACAD,EACAuC,EACArJ,EACAwO,GALJ,MAOI,YAAM,CACF1H,SAAQ,EACRpZ,QAASqZ,EACTsC,qBAAoB,EACpBzV,KAAM,QACN+S,WAAY,SACb3G,EAAQwO,IAAa,K,OACpBnF,IACA,EAAKgO,cAAgB,IAAIC,GACrB,EAAKtX,OACLqJ,EACAvC,I,CAGZ,CAuyBJ,OA9zB2B,QA+BvB,YAAAyQ,iBAAA,WAEI,OADe3qB,KAAK4Z,cAAcM,SAAWla,KAAKmqB,aAAenqB,KAAKqqB,aACxDO,eAClB,EAEA,YAAAC,aAAA,WACI,OAAO7qB,KAAK4Z,cAAc9Y,UAAYwY,EAC1C,EAUA,YAAAuI,WAAA,SAAWC,EAAqB5b,GAAhC,WACI,OAAIlG,KAAK6qB,gBACU7qB,KAAK4Z,cAAcM,SAAWla,KAAKoT,OAAO4E,OAAShY,KAAKoT,OAAO8E,OAChE2J,WAAWC,GAElB9hB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,YACA2hB,GAEJ,OAAO,EAAK3G,YAAoBhb,EAAQ+F,EAC5C,GAGR,EAQA,YAAA4kB,iBAAA,WACI,OAAI9qB,KAAK6qB,iBAIM7qB,KAAK4Z,cAAcM,SAAWla,KAAKmqB,aAAenqB,KAAKqqB,aAExDU,mBAAmB/qB,KAAK4Z,cAAc9Y,SAC/CL,MAAK,SAAAuqB,GACF,OAAOA,EAAU,KAAO1R,EAC5B,GACR,EAUA,YAAAyI,aAAA,SAAaD,EAAqB5b,GAAlC,gBAAkC,IAAAA,IAAAA,EAAA,IAC9BlG,KAAKmc,kBAAkB,gBACvB,IAAM6F,EAAiB9b,EAAO8b,eAAiB9b,EAAO8b,eAAiBhiB,KAAK2qB,mBAE5E,OAAO3qB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,YACA2hB,EACAE,GAEJ,OAAO,EAAK7G,YAAoBhb,EAAQ+F,EAC5C,GACJ,EAUA,YAAAgc,QAAA,SAAQngB,EAAqBmE,GAA7B,gBAA6B,IAAAA,IAAAA,EAAA,IACzBlG,KAAKmc,kBAAkB,WACvB,IAAM6F,EAAiB9b,EAAO8b,eAAiB9b,EAAO8b,eAAiBhiB,KAAK2qB,mBAE5E,OAAO3qB,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,IAAM7b,EAAS6b,EAAS7b,OACpB,UACA6hB,EACAvM,EAAU3T,MAAMC,IAEpB,OAAO,EAAKwY,aAAapa,EAAQ+F,EACrC,GACJ,EASA,YAAAic,WAAA,SAAWjc,GAEP,YAFO,IAAAA,IAAAA,EAAA,IACPlG,KAAKmc,kBAAkB,cAChBnc,KAAKkiB,QACR7I,GACAnT,EAER,EAWA,YAAA+X,QAAA,SAAQlc,EAAqB+f,EAAqB5b,GAAlD,gBAAkD,IAAAA,IAAAA,EAAA,IAC9ClG,KAAKsc,aAAa,WAClB,IAAM2O,EAAa/kB,EAAO+kB,YAAc,KAClCT,EAA4BtkB,EAAOskB,4BAA6B,EAEhEpI,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAOL,OAJI/B,KAAK6qB,iBACL3kB,EAAOzG,MAAQgW,EAAU3T,MAAMC,IAG5B/B,KAAKqqB,YAAYvM,YAAYrd,MAAK,SAAAyqB,GACrC,OAAO,EAAKf,aAAagB,YACrBD,EACApJ,EACAM,EACA,EAAKxI,cAAc9Y,QACnB0pB,EACAS,EACA/kB,EAER,GACJ,EAWA,YAAAmc,eAAA,SAAetgB,EAAqB+f,EAAqBsJ,EAA2BllB,QAAA,IAAAA,IAAAA,EAAA,IAChFlG,KAAKsc,aAAa,WAElB,IAAM8F,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAIL,OADAmE,EAAOzG,MAAQgW,EAAU3T,MAAMspB,GAC3BllB,EAAOiL,GAAKjL,EAAO5G,GAAK4G,EAAOyE,EACxB3K,KAAKoqB,aAAaiB,qBACrBrrB,KAAK4Z,cAAc9Y,QACnBshB,EACAN,EACAnW,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,KAAMrqB,WAC1CiF,EAAOiL,EACPjL,EAAO5G,EACP4G,EAAOyE,EACPzE,GAGDlG,KAAKoqB,aAAa/H,eACrBriB,KAAK4Z,cAAc9Y,QACnBshB,EACAN,EACA5b,EAER,EAWA,YAAAmlB,qBAAA,SAAqBtpB,EAAqB+f,EAAqBsJ,EAA2BllB,GAA1F,gBAA0F,IAAAA,IAAAA,EAAA,IACtFlG,KAAKsc,aAAa,WAClBtc,KAAKmc,kBAAkB,iBAEvB,IAAMiG,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAKL,OAFAmE,EAAOzG,MAAQgW,EAAU3T,MAAMspB,GAExBprB,KAAKurB,0BAA0BxpB,EAAQ/B,KAAKoqB,aAAaQ,iBAAiBnqB,MAC7E,SAAA+qB,GACI,OAAO,EAAKpB,aAAaiB,qBACrB,EAAKzR,cAAc9Y,QACnBshB,EACAN,EACAnW,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,KAAMrqB,WAC1CuqB,EAAgBra,EAChBqa,EAAgBlsB,EAChBksB,EAAgB7gB,EAChBzE,EAER,GAER,EAWA,YAAAulB,kBAAA,SAAkB1pB,EAAqB+f,EAAqB5b,GAA5D,gBAA4D,IAAAA,IAAAA,EAAA,IACxDlG,KAAKsc,aAAa,WAClBtc,KAAKmc,kBAAkB,qBAEvB,IAAMiG,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAGCyoB,EAA4BtkB,EAAOskB,4BAA6B,EAEtE,OAAOxqB,KAAK0rB,cAActJ,EAAalc,GAAQzF,MAAK,SAAAwqB,GAChD,OAAO,EAAKZ,YAAYvM,YAAYrd,MAAK,SAAAyqB,GACrC,OAAO,EAAKf,aAAagB,YACrBD,EACApJ,EACAM,EACA,EAAKxI,cAAc9Y,QACnB0pB,EACAS,EACA/kB,EAER,GACJ,GACJ,EAUA,YAAAylB,mBAAA,SAAmB5pB,EAAqB+f,EAAqB0I,GAMzD,YANyD,IAAAA,IAAAA,GAAA,GAEzDxqB,KAAKsc,aAAa,sBAClBtc,KAAKwc,oBAAoB,sBAEzBxc,KAAKmc,kBAAkB,sBAChBnc,KAAKyqB,cAAcH,YAAYxI,EAAa/f,EAAQyoB,EAC/D,EASA,YAAAoB,wBAAA,SAAwBtX,EAAyBpO,GAAjD,WAEI,OADAlG,KAAKuc,cAAc,2BACZvc,KAAKmqB,aAAarM,YAAYrd,MAAK,SAAAyqB,GACtC,OAAO,EAAKhL,WAAW2L,qBACnBvX,GAAiB,EAAM4W,EAE/B,IAAGzqB,MAAK,SAAAC,GACJ,OAAO,EAAK2pB,YAAYyB,aACpBprB,EAAQqrB,SACRrrB,EAAQsrB,eACRtrB,EAAQurB,YACRvrB,EAAQwrB,gBACRxrB,EAAQyrB,eACRzrB,EAAQ0rB,cACR1rB,EAAQ2rB,mBACR3rB,EAAQ4rB,mBACR5rB,EAAQ6rB,mBACR7rB,EAAQqB,OACRrB,EAAQ8rB,SACRtmB,EAER,GACJ,EAWA,YAAAumB,aAAA,SAAanY,EAAyBpO,GAAtC,WAEI,OADAlG,KAAKuc,cAAc,gBACZvc,KAAKmqB,aAAarM,YAAYrd,MAAK,SAAAyqB,GACtC,OAAO,EAAKhL,WAAW2L,qBACnBvX,GAAiB,EAAM4W,EAE/B,IAAGzqB,MAAK,SAAAC,GACJ,OAAO,EAAK2pB,YAAYqC,WACpBhsB,EAAQqrB,SACRrrB,EAAQsrB,eACRtrB,EAAQurB,YACRvrB,EAAQwrB,gBACRxrB,EAAQyrB,eACRzrB,EAAQ0rB,cACR1rB,EAAQ2rB,mBACR3rB,EAAQ4rB,mBACR5rB,EAAQ6rB,mBACR7rB,EAAQqB,OACRrB,EAAQ8rB,SACRtmB,EAER,GACJ,EAWA,YAAAymB,SAAA,SAAS5qB,EAAqB+f,EAAqB5b,GAAnD,gBAAmD,IAAAA,IAAAA,EAAA,IAC/ClG,KAAKuc,cAAc,YACnB,IAAM0O,EAAa/kB,EAAO+kB,YAAc,KAClCT,EAA4BtkB,EAAOskB,4BAA6B,EAEhEpI,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAOL,OAJI/B,KAAK6qB,iBACL3kB,EAAOzG,MAAQgW,EAAU3T,MAAMC,IAG5B/B,KAAKmqB,aAAarM,YAAYrd,MAAK,SAAAyqB,GACtC,OAAO,EAAKb,YAAYc,YACpBD,EACApJ,EACAM,EACA,EAAKxI,cAAc9Y,QACnB0pB,EACAS,EACA/kB,EAER,GACJ,EAUA,YAAA0mB,oBAAA,SAAoB7qB,EAAqB+f,EAAqB0I,GAM1D,YAN0D,IAAAA,IAAAA,GAAA,GAE1DxqB,KAAKuc,cAAc,uBACnBvc,KAAKwc,oBAAoB,sBAEzBxc,KAAKmc,kBAAkB,uBAChBnc,KAAKyqB,cAAcH,YAAYxI,EAAa/f,EAAQyoB,EAC/D,EASA,YAAAqC,wBAAA,SAAwBjK,EAA6B1c,GAArD,WAEI,OADAlG,KAAKsc,aAAa,2BACXtc,KAAKqqB,YAAYvM,YAAYrd,MAAK,SAAAyqB,GACrC,OAAO,EAAKhL,WAAW2L,qBACnBjJ,GAAqB,EAAOsI,EAEpC,IAAGzqB,MAAK,SAAAC,GACJ,OAAO,EAAKypB,aAAa2B,aACrBprB,EAAQqrB,SACRrrB,EAAQsrB,eACRtrB,EAAQurB,YACRvrB,EAAQwrB,gBACRxrB,EAAQyrB,eACRzrB,EAAQ0rB,cACR1rB,EAAQ2rB,mBACR3rB,EAAQ4rB,mBACR5rB,EAAQ6rB,mBACR7rB,EAAQqB,OACRrB,EAAQ8rB,SACRtmB,EAER,GACJ,EAWA,YAAA4mB,mBAAA,SAAmB/qB,EAAqB+f,EAAqB5b,GAA7D,gBAA6D,IAAAA,IAAAA,EAAA,IACzDlG,KAAKuc,cAAc,YAEnB,IAAM6F,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAGCyoB,EAA4BtkB,EAAOskB,4BAA6B,EAEtE,OAAOxqB,KAAK0rB,cAActJ,EAAalc,GAAQzF,MAAK,SAAAwqB,GAChD,OAAO,EAAKd,aAAarM,YAAYrd,MAAK,SAAAyqB,GACtC,OAAO,EAAKb,YAAYc,YACpBD,EACApJ,EACAM,EACA,EAAKxI,cAAc9Y,QACnB0pB,EACAS,EACA/kB,EAER,GACJ,GACJ,EAUA,YAAA6c,aAAA,SAAaH,EAA6B1c,GAA1C,WAEI,OADAlG,KAAKsc,aAAa,gBACXtc,KAAKqqB,YAAYvM,YAAYrd,MAAK,SAAAyqB,GACrC,OAAO,EAAKhL,WAAW2L,qBACnBjJ,GAAqB,EAAOsI,EAEpC,IAAGzqB,MAAK,SAAAC,GACJ,OAAO,EAAKypB,aAAauC,WACrBhsB,EAAQqrB,SACRrrB,EAAQsrB,eACRtrB,EAAQurB,YACRvrB,EAAQwrB,gBACRxrB,EAAQyrB,eACRzrB,EAAQ0rB,cACR1rB,EAAQ2rB,mBACR3rB,EAAQ4rB,mBACR5rB,EAAQ6rB,mBACR7rB,EAAQqB,OACRrB,EAAQ8rB,SACRtmB,EAER,GACJ,EAWA,YAAAgd,SAAA,SAASnhB,EAAqBwL,EAAYrH,GACtC,YADsC,IAAAA,IAAAA,EAAA,IAClClG,KAAK4Z,cAAc9Y,UAAYwY,IAC/BpT,EAAOqH,GAAKA,EACZrH,EAAOzG,MAAQgW,EAAU3T,MAAMC,GACxB/B,KAAK+a,gBAAgB7U,IAEzBlG,KAAK+b,cAAcxO,EAAIxL,EAAQmE,EAC1C,EAQQ,YAAA6mB,UAAR,eACQ/Q,EADR,OAEI,OAAOhc,KAAK6Z,cAAcpZ,MAAK,SAAAusB,GAE3B,IAAM7sB,GADN6b,EAAWgR,GACa7sB,OACpB,mBAEJ,OAAO,EAAKgb,YAAoBhb,EACpC,IAAGM,MAAK,SAAAwsB,GACJ,OAAQA,GACJ,KAAK1T,GACD,OAAO/H,GAAO0b,IAElB,KAAK1T,GACD,IAAM2T,EAAkBnR,EAAS7b,OAAO,mBAClCitB,EAAoBpR,EAAS7b,OAAO,qBAC1C,OAAO6Y,GAAW,CAAC,EAAKmC,YAAoBgS,GAAkB,EAAKhS,YAAoBiS,KAAqB3sB,MACxG,SAAC4sB,GACG,OAAQA,GACJ,KAAK5T,GACD,OAAOjI,GAAO8b,SAElB,KAAK5T,GACD,OAAOlI,GAAO+b,QAElB,QACI,OAAOxqB,QAAQqW,OAAO,IAAIhX,MAAM,uCAAgCirB,KAG5E,IAGR,QACI,OAAOtqB,QAAQqW,OAAO,IAAIhX,MAAM,uCAAgC6qB,KAG5E,GACJ,EAeQ,YAAAO,cAAR,SAAsBC,EAAoBC,EAAiB3U,EAAiB/R,EAAc6U,EAAemG,EAAwBjgB,GAC7H,IAAM4rB,EAAY,CACdC,MAAO,CACHC,aAAc,CACV,CAAE7mB,KAAM,OAAQ3C,KAAM,UACtB,CAAE2C,KAAM,UAAW3C,KAAM,UACzB,CAAE2C,KAAM,UAAW3C,KAAM,WACzB,CAAE2C,KAAM,oBAAqB3C,KAAM,YAEvCmN,OAAQ,IAEZsc,YAAa,SACbC,OAAQ,CACJ/mB,KAAI,EACJwR,QAAS,IACTO,QAAO,EACPiV,kBAAmBhuB,KAAK4Z,cAAc9Y,SAE1CyD,QAAS,CAAC,GAEd,OAAQkpB,GACJ,KAAKjc,GAAO0b,IACRS,EAAUC,MAAMpc,OAAS,CACrB,CAAExK,KAAM,SAAU3C,KAAM,WACxB,CAAE2C,KAAM,UAAW3C,KAAM,WACzB,CAAE2C,KAAM,QAAS3C,KAAM,WACvB,CAAE2C,KAAM,SAAU3C,KAAM,WACxB,CAAE2C,KAAM,UAAW3C,KAAM,SAE7BspB,EAAUppB,QAAU,CAChB0pB,OAAQP,EACRQ,QAASlM,EACTnG,MAAK,EACLsS,OAAQxiB,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,KAC5C8C,SAAS,GAEjB,KAAK5c,GAAO8b,SACZ,KAAK9b,GAAO+b,QAEJE,IAAejc,GAAO+b,UACtBI,EAAUC,MAAMC,aAAe,CAC3B,CAAE7mB,KAAM,OAAQ3C,KAAM,UACtB,CAAE2C,KAAM,UAAW3C,KAAM,WACzB,CAAE2C,KAAM,oBAAqB3C,KAAM,mBAEhCspB,EAAUI,OAAOvV,SAE5BmV,EAAUC,MAAMpc,OAAS,CACrB,CAAExK,KAAM,QAAS3C,KAAM,WACvB,CAAE2C,KAAM,UAAW3C,KAAM,WACzB,CAAE2C,KAAM,QAAS3C,KAAM,WACvB,CAAE2C,KAAM,QAAS3C,KAAM,WACvB,CAAE2C,KAAM,WAAY3C,KAAM,YAE9BspB,EAAUppB,QAAU,CAChB8pB,MAAOX,EACPQ,QAASlM,EACTviB,MAAOsC,EACP8Z,MAAOA,EACPyS,SAAU3iB,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,MAG1D,OAAOqC,CACX,EAUQ,YAAAY,wBAAR,SAAgCnb,EAAwBob,GACpD,KAAK,IAAA7d,aAAY6d,GACb,MAAM,IAAIpsB,MACN,gBAAgBmE,OAAOioB,EAAW,iCAIZ,OAA1BA,EAAUrsB,MAAM,EAAG,KACnBqsB,EAAY,KAAKjoB,OAAOioB,IAG5B,IAAMlvB,EAAIkvB,EAAUrsB,MAAM,EAAG,IACvBwI,EAAI,KAAKpE,OAAOioB,EAAUrsB,MAAM,GAAI,MACtCgP,EAAIiC,EAAOqb,YAAY,KAAKloB,OAAOioB,EAAUrsB,MAAM,IAAK,OAI5D,MAHK,CAAC,GAAI,IAAI8E,SAASkK,KACnBA,GAAK,IAEF,CACH7R,EACAqL,EAAGA,EACHwG,EAAGA,EAEX,EAeQ,YAAAud,0BAAR,SAAkC1S,EAAwByR,EAAoBjC,EAAsBxJ,EAAwB0L,EAAiB7R,EAAe9Z,GAChJ,IACJ5B,EADIb,EAAYksB,EAAe,EAAxB7gB,EAAS6gB,EAAe,EAArBra,EAAMqa,EAAe,EAEnC,OAAQiC,GACJ,KAAKjc,GAAO0b,IACR/sB,EAAS6b,EAAS7b,OACd,SACAutB,EACA1L,EACAnG,EACAlQ,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,MACpC,EACAna,EACA7R,EACAqL,GAEJ,MAEJ,KAAK6G,GAAO8b,SACZ,KAAK9b,GAAO+b,QACRptB,EAAS6b,EAAS7b,OACd,SACAutB,EACA1L,EACAjgB,EACA4J,KAAKC,OAAOrL,KAAK+qB,MAAQ,MAAW,KACpCna,EACA7R,EACAqL,GAIZ,OAAOxK,EAAO0a,WAClB,EAEQ,YAAA0Q,0BAAR,SAAkCxpB,EAAqBigB,GAAvD,IAOQ0L,EACA3U,EACA0U,EACAzR,EACAH,EAXR,OACUuG,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAGCqR,EAASpT,KAAK4Z,cAAcM,SAAWla,KAAKoT,OAAO4E,OAAShY,KAAKoT,OAAO8E,MAO9E,OAAOnV,QAAQiB,IAAI,CAAiB,SAAhBoP,EAAOpM,KAAkBoM,EAAOvS,uBAAyBuS,EAAOub,cAAe3uB,KAAK6Z,cAAezG,EAAOgH,aAAcpa,KAAK+sB,cAActsB,MAAK,SAAAG,GAChK8sB,EAAU9sB,EAAO,GAAG,GACpBob,EAAWpb,EAAO,GAClBmY,EAAUnY,EAAO,GACjB6sB,EAAa7sB,EAAO,GACpB,IAAMguB,EAAa5S,EAAS7b,OAAO,QAC7B0uB,EAAc7S,EAAS7b,OAAO,SAAUutB,GAC9C,OAAO3qB,QAAQiB,IAAI,CAAC,EAAKmX,YAAoByT,GAAa,EAAKzT,YAAoB0T,IACvF,IAAGpuB,MAAK,SAAAwI,GACJ,IAAMjC,EAAOiC,EAAK,GAElB,OADA4S,EAAQ5S,EAAK,GACN,EAAKukB,cAAcC,EAAYC,EAAS3U,EAAS/R,EAAM6U,EAAOmG,EAAgBI,EACzF,IAAG3hB,MAAK,SAAAktB,GACJ,OAAOva,EAAO0b,cAAcpB,EAASC,EACzC,IAAGltB,MAAK,SAAA+tB,GACJ,OAAO,EAAKD,wBAAwBnb,EAAQob,EAChD,GACJ,EAUQ,YAAAO,eAAR,SAAuBhtB,EAAqBigB,GAA5C,IAQQ0L,EACA3U,EACA0U,EACAzR,EACAH,EAZR,OAEUuG,EAAcpiB,KAAKoT,OAAO4E,OAAOvE,iBACnC,CAACgC,EAAU3T,MAAMC,IACjB,CAAC,YAGCqR,EAASpT,KAAK4Z,cAAcM,SAAWla,KAAKoT,OAAO4E,OAAShY,KAAKoT,OAAO8E,MAO9E,OAAOnV,QAAQiB,IAAI,CAAiB,SAAhBoP,EAAOpM,KAAkBoM,EAAOvS,uBAAyBuS,EAAOub,cAAe3uB,KAAK6Z,cAAezG,EAAOgH,aAAcpa,KAAK+sB,cAActsB,MAAK,SAAAG,GAChK8sB,EAAU9sB,EAAO,GAAG,GACpBob,EAAWpb,EAAO,GAClBmY,EAAUnY,EAAO,GACjB6sB,EAAa7sB,EAAO,GACpB,IAAMguB,EAAa5S,EAAS7b,OAAO,QAC7B0uB,EAAc7S,EAAS7b,OAAO,SAAUutB,GAC9C,OAAO3qB,QAAQiB,IAAI,CAAC,EAAKmX,YAAoByT,GAAa,EAAKzT,YAAoB0T,IACvF,IAAGpuB,MAAK,SAAAwI,GACJ,IAAMjC,EAAOiC,EAAK,GAElB,OADA4S,EAAQ5S,EAAK,GACN,EAAKukB,cAAcC,EAAYC,EAAS3U,EAAS/R,EAAM6U,EAAOmG,EAAgBI,EACzF,IAAG3hB,MAAK,SAAAktB,GACJ,OAAOva,EAAO0b,cAAcpB,EAASC,EACzC,IAAGltB,MAAK,SAAA+tB,GACJ,IAAMQ,EAAsB,EAAKT,wBAAwBnb,EAAQob,GACjE,OAAO,EAAKE,0BACR1S,EAAUyR,EAAYuB,EAAqBhN,EAAgB0L,EAAS7R,EAAOuG,EAEnF,GACJ,EAUA,YAAAsJ,cAAA,SAAc3pB,EAAqBmE,QAAA,IAAAA,IAAAA,EAAA,IAC/BlG,KAAKmc,kBAAkB,iBAEvB,IAAM6F,EAAiB9b,EAAO8b,eAAiB9b,EAAO8b,eAAiBhiB,KAAK2qB,mBAE5E,OAAO3qB,KAAK+uB,eAAehtB,EAAQigB,EACvC,EACJ,EA9zBA,CAA2BiN,I,2dCN3B,eAII,WAAY7L,EAAkDtiB,EAAiBoZ,G,OAC3E,YAAM,CACFpZ,QAASA,EACTkG,KAAM,qBACN+S,WAAY,QACZG,SAAUA,GACXkJ,IAAQ,IACf,CA8QJ,OAzRiC,QAa7B,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACvB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC9C,GACJ,EAeA,YAAAinB,YAAA,SACImB,EACAC,EACAxqB,EACAkd,EACAuL,EACAS,EACA/kB,GAPJ,WASI,YAHA,IAAA+kB,IAAAA,EAAA,MAGOjrB,KAAKG,OACR,cACAmsB,EACAC,EACA9W,EAAU3T,MAAMC,GAChBkd,EACAuL,EACAS,GACFxqB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAoBA,YAAAwmB,WAAA,SACIX,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAxqB,EACAyqB,EACAtmB,GAZJ,WAcI,OAAOlG,KAAKG,OACR,aACA4rB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAxqB,EACAyqB,GACF/rB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aACRpa,EACA+F,EAER,GACJ,EAaA,YAAAgpB,cAAA,SACI5C,EACAC,EACA/B,EACAS,EACA/kB,GALJ,WAOI,YAHA,IAAA+kB,IAAAA,EAAA,MAGOjrB,KAAKG,OACR,gBACAmsB,EACAC,EACA/B,EACAS,GACFxqB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAsBA,YAAA4lB,aAAA,SACIC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAxqB,EACAyqB,EACAtmB,GAZJ,WAaI,OAAOlG,KAAKG,OACR,eACA4rB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAxqB,EACAyqB,GACF/rB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aACRpa,EACA+F,EAER,GACJ,EAUA,YAAAipB,mBAAA,SACI/C,EACAC,GAFJ,WAII,OAAOrsB,KAAKG,OACR,yBAA0BisB,EAAeC,GAC3C5rB,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAAoBhb,EACpC,GACJ,EASA,YAAAqgB,UAAA,SACI5c,EACAwrB,GAFJ,WAII,OAAOpvB,KAAKG,OACR,YAAayD,EAAOwrB,GACtB3uB,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAAoBhb,EACpC,GACJ,EAUA,YAAAkvB,6BAAA,SACIjD,EACAC,GAFJ,WAII,OAAOrsB,KAAKG,OACR,8BAA+BisB,EAAeC,GAChD5rB,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAAoBhb,EACpC,GACJ,EASA,YAAA4qB,mBAAA,SAAmBuE,GAAnB,WACI,OAAOtvB,KAAKG,OACR,0BAA2BmvB,GAC7B7uB,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAA8Bhb,EAC9C,GACJ,EAQA,YAAA2d,UAAA,sBACI,OAAI9d,KAAKuvB,WACE/oB,GAAuBxG,KAAKuvB,YAEhCvvB,KAAKG,OACR,aACFM,MAAK,SAAAN,GACH,OAAO,EAAKgb,YAAoBhb,GAAQM,MAAK,SAACyqB,GAE1C,OADA,EAAKqE,WAAarE,EACXA,CACX,GACJ,GACJ,EAEJ,EAzRA,CAAiChM,ICkCjC,cAII,WAAY9L,GAFJ,KAAAoc,aAAe,qEAGnBxvB,KAAKojB,QAAUhQ,CACnB,CAyFJ,OAvFY,YAAAqc,mBAAR,SAA2BxmB,EAAciR,GACrC,IAAM9G,EAAS8G,EAAWla,KAAKojB,QAAQpL,OAAShY,KAAKojB,QAAQlL,MAC7D,OAAOlY,KAAKojB,QAAQxK,OAAO,qBAAsB,SAASnY,MAAK,SAAAuZ,GAC3D,IAAM4T,EAAQ5T,EAAI0V,QAAO,SAAAlsB,GAAS,MAAe,gBAAfA,EAAMwD,IAAN,IAClC,IAAK4mB,EAAM1wB,OACP,MAAM,IAAIkF,MAAM,oBAEpB,IAAMutB,EAAcvc,EAAOoM,iBAAiBvW,EAAM2kB,EAAM,GAAGgC,QAE3D,MAAO,CACHC,SAF0HF,EAAW,GAGrIvD,cAH0HuD,EAAW,GAIrItD,mBAJ0HsD,EAAW,GAKrIrD,mBAL0HqD,EAAW,GAMrIpD,mBAN0HoD,EAAW,GAOrI5tB,OAP0H4tB,EAAW,GAQrInD,SAR0HmD,EAAW,IAQ/G,KACtB5R,aAT0H4R,EAAW,GAW7I,GACJ,EAEQ,YAAAG,kBAAR,SAA0Bxb,EAAyB4F,GAAnD,WAEI,OADeA,EAAWla,KAAKojB,QAAQpL,OAAShY,KAAKojB,QAAQlL,OAC/C3D,sBAAsBD,GAC/B7T,MAAK,SAAAkT,GACF,IAAMoC,EAAOpC,EAAQoC,KAAK2Z,QAAO,SAAArqB,GAAO,OAAAA,EAAI2Q,OAAO,GAAG6N,gBAAkB,EAAK2L,YAArC,IACxC,IAAKzZ,EAAK7Y,OACN,MAAM,IAAIkF,MAAM,4BAGpB,IAAM6G,EAAO8M,EAAK,GAAG9M,KACrB,OAAO,EAAKwmB,mBAAmBxmB,EAAMiR,EACzC,GACR,EAEQ,YAAA6V,UAAR,SAAkB7E,EAAmBnN,GACjC,OAAOG,GAAQK,aAAaV,uBACxB7d,KAAKojB,QAAQrL,OAAOS,QACpB0S,EACAnN,GACFtd,MAAK,SAAA0P,GACH,OAAOA,CACX,IAAGmD,OAAM,SAAAC,GACL,MAAM,IAAInR,MAAM,0BACpB,GACJ,EAEA,YAAA+d,iBAAA,SAAiB7L,EAAyB4F,GACtC,OAAOla,KAAK8vB,kBAAkBxb,EAAiB4F,EACnD,EAEA,YAAA8V,mBAAA,SAAmBC,EAAoBC,EAAqBC,GACxD,OAAIloB,OAAOkoB,KAAqBloB,OAAO,GAC5BA,OAAOgoB,GAActW,GAErB1R,OAAOgoB,GAAchoB,OAAOioB,GAAejoB,OAAO,WAAK,IAEtE,EAEA,YAAA4jB,qBAAA,SAAqBvX,EAAyB4F,EAAmBgR,GAAjE,WACI,OAAOlrB,KAAK8vB,kBAAkBxb,EAAiB4F,GAAUzZ,MAAK,SAAAwI,GAEtD,IAAAmjB,EAMiBnjB,EAAI,cALrBojB,EAKiBpjB,EAAI,mBAJrBqjB,EAIiBrjB,EAAI,mBAHrBsjB,EAGiBtjB,EAAI,mBAFrBlH,EAEiBkH,EAAI,OADrBujB,EACiBvjB,EAAI,SAArB8U,EAAiB9U,EAAI,aACzB,OAAO,EAAK8mB,UAAU7E,EAAWnN,GAActd,MAAK,SAAA0P,GAChD,IAAMzP,EAAU,CAAC,EAYjB,OAXAA,EAAQqrB,SAAW5b,EAAMigB,aACzB1vB,EAAQsrB,eAAiB7b,EAAMkgB,oBAC/B3vB,EAAQurB,YAAc,EAAK+D,mBAAmBjS,EAAcuO,EAAoBpB,GAAWjqB,WAC3FP,EAAQwrB,gBAAkB/b,EAAMmgB,eAChC5vB,EAAQyrB,eAAiBhc,EAAMogB,iBAC/B7vB,EAAQ0rB,cAAgBA,EACxB1rB,EAAQ2rB,mBAAqBA,EAC7B3rB,EAAQ4rB,mBAAqBA,EAC7B5rB,EAAQ6rB,mBAAqBA,EAC7B7rB,EAAQqB,OAASA,EACjBrB,EAAQ8rB,SAAWA,EACZ9rB,CACX,GACJ,GACJ,EACJ,EA/FA,G,2dCnCA,eAEI,WAAY0iB,EAAkDtiB,G,OAC1D,YAAM,CACFA,QAASA,EACTkG,KAAM,eACN+S,WAAY,QACZG,UAAU,GACXkJ,IAAQ,IACf,CAgDJ,OAzDkC,QAW9B,YAAAjjB,OAAA,SAAOic,G,IAAoB,wDACvB,OAAOpc,KAAK6Z,cAAcpZ,MAAK,SAAAub,GAC3B,OAAOA,EAAS7b,OAAM,MAAf6b,E,+LAAQ,EAAQI,GAAelY,GAAI,GAC9C,GACJ,EAEA,YAAAme,eAAA,SACIlI,EACA4O,EACAjH,EACA5b,GAJJ,WAMI,OAAOlG,KAAKG,OACR,UACAga,EACA4O,EACAjH,GACFrhB,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAEA,YAAAmlB,qBAAA,SACIlR,EACA4O,EACAjH,EACAwM,EACAnd,EACA7R,EACAqL,EACAzE,GARJ,WAUI,OAAOlG,KAAKG,OACR,UACAga,EACA4O,EACAjH,EACAwM,EACAnd,EACA7R,EACAqL,GACFlK,MAAK,SAAAN,GACH,OAAO,EAAKoa,aAAapa,EAAQ+F,EACrC,GACJ,EAEJ,EAzDA,CAAkCgZ,I,2dCUlC,4B,8CAgFA,QAhFiC,QAI7B,YAAApH,KAAA,SAAKC,GAAL,WACU3E,EAASpT,KAAKoT,OAEpB,OAAOA,EAAO0E,KAAKC,GAAQtX,MAAK,SAAA8S,GAC5B,IAAMid,EAAqBpd,EAAOod,mBAC5BC,EAAiBrd,EAAOqd,eAuC9B,OAtCArd,EAAO2E,OAASA,EAASlZ,OAAO6G,OAC5B,CACIykB,aAAcqG,EAAmBE,wBACjCrG,YAAaoG,EAAeE,mBAC5BvG,aAAcoG,EAAmBI,cAErC7Y,GAGJ,EAAKkI,gBAAkB,IAAI4Q,GACvB,EAAKzd,OACL2E,EAAOoS,cACP,GAGJ,EAAK5J,iBAAmB,IAAIsQ,GACxB,EAAKzd,OACL2E,EAAOsS,aACP,GAGJ,EAAKD,aAAe,IAAIwG,GACpB,EAAKxd,OACL2E,EAAOqS,cAGX,EAAKlK,WAAa,IAAI4Q,GAClB,EAAK1d,QAGJ8K,GAAQK,eACqE,MAA1E,GAAUrB,mBAAmB,GAAUA,mBAAmBhgB,OAAS,KACnE,GAAUggB,oBAAsB,KAEpC,GAAUA,oBAAsB,aAChCgB,GAAQK,aAAe,IAAIF,GAAe,GAAUnB,qBAGjD,CACX,GACJ,EAYA,YAAAsM,MAAA,SAAMrP,EAAsBD,EAAoBuC,GAC5C,OAAO,IAAI,GACPtC,EACAD,EACAuC,EACAzc,KAAKoT,OACLpT,KAAK0pB,cAAcvmB,KAAKnD,MAEhC,EAEQ,YAAA0pB,cAAR,WACI,MAAO,CACHS,aAAcnqB,KAAKigB,gBACnBoK,YAAarqB,KAAKugB,iBAClBL,WAAYlgB,KAAKkgB,WACjBkK,aAAcpqB,KAAKoqB,aAE3B,EACJ,EAhFA,CAAiC2G,ICDjC,W", "sources": ["webpack://@maticnetwork/maticjs/./node_modules/safe-buffer/index.js", "webpack://@maticnetwork/maticjs/external commonjs2 \"buffer\"", "webpack://@maticnetwork/maticjs/external commonjs2 \"node-fetch\"", "webpack://@maticnetwork/maticjs/webpack/bootstrap", "webpack://@maticnetwork/maticjs/webpack/runtime/compat get default export", "webpack://@maticnetwork/maticjs/webpack/runtime/define property getters", "webpack://@maticnetwork/maticjs/webpack/runtime/hasOwnProperty shorthand", "webpack://@maticnetwork/maticjs/webpack/runtime/make namespace object", "webpack://@maticnetwork/maticjs/./src/abstracts/contract_method.ts", "webpack://@maticnetwork/maticjs/./src/enums/log_event_signature.ts", "webpack://@maticnetwork/maticjs/./src/enums/error_type.ts", "webpack://@maticnetwork/maticjs/./src/abstracts/base_web3_client.ts", "webpack://@maticnetwork/maticjs/./src/abstracts/base_contract.ts", "webpack://@maticnetwork/maticjs/./src/abstracts/base_big_number.ts", "webpack://@maticnetwork/maticjs/./src/implementation/bn.ts", "webpack://@maticnetwork/maticjs/./src/utils/converter.ts", "webpack://@maticnetwork/maticjs/./src/utils/use.ts", "webpack://@maticnetwork/maticjs/./src/utils/event_bus.ts", "webpack://@maticnetwork/maticjs/./src/utils/error_helper.ts", "webpack://@maticnetwork/maticjs/./src/utils/logger.ts", "webpack://@maticnetwork/maticjs/./src/utils/merge.ts", "webpack://@maticnetwork/maticjs/./src/utils/map_promise.ts", "webpack://@maticnetwork/maticjs/external commonjs2 \"@ethereumjs/util\"", "webpack://@maticnetwork/maticjs/./node_modules/@noble/hashes/esm/_assert.js", "webpack://@maticnetwork/maticjs/./node_modules/@noble/hashes/esm/_u64.js", "webpack://@maticnetwork/maticjs/./node_modules/@noble/hashes/esm/utils.js", "webpack://@maticnetwork/maticjs/./node_modules/@noble/hashes/esm/sha3.js", "webpack://@maticnetwork/maticjs/./node_modules/ethereum-cryptography/esm/utils.js", "webpack://@maticnetwork/maticjs/./node_modules/ethereum-cryptography/esm/keccak.js", "webpack://@maticnetwork/maticjs/./src/utils/keccak.ts", "webpack://@maticnetwork/maticjs/./src/utils/merkle_tree.ts", "webpack://@maticnetwork/maticjs/external commonjs2 \"bn.js\"", "webpack://@maticnetwork/maticjs/./src/utils/buffer-utils.ts", "webpack://@maticnetwork/maticjs/external commonjs2 \"rlp\"", "webpack://@maticnetwork/maticjs/external commonjs2 \"@ethereumjs/trie\"", "webpack://@maticnetwork/maticjs/external commonjs2 \"@ethereumjs/block\"", "webpack://@maticnetwork/maticjs/external commonjs2 \"@ethereumjs/common\"", "webpack://@maticnetwork/maticjs/./src/constant.ts", "webpack://@maticnetwork/maticjs/./src/utils/proof_util.ts", "webpack://@maticnetwork/maticjs/./src/utils/http_request.ts", "webpack://@maticnetwork/maticjs/./src/utils/web3_side_chain_client.ts", "webpack://@maticnetwork/maticjs/./src/utils/promise_resolve.ts", "webpack://@maticnetwork/maticjs/./src/utils/base_token.ts", "webpack://@maticnetwork/maticjs/./src/services/abi_service.ts", "webpack://@maticnetwork/maticjs/./src/config.ts", "webpack://@maticnetwork/maticjs/./src/services/network_service.ts", "webpack://@maticnetwork/maticjs/./src/services/index.ts", "webpack://@maticnetwork/maticjs/./src/utils/set_proof_api_url.ts", "webpack://@maticnetwork/maticjs/./src/utils/resolve.ts", "webpack://@maticnetwork/maticjs/./src/utils/bridge_client.ts", "webpack://@maticnetwork/maticjs/./src/utils/abi_manager.ts", "webpack://@maticnetwork/maticjs/./src/utils/not_implemented.ts", "webpack://@maticnetwork/maticjs/./src/utils/zkevm_bridge_client.ts", "webpack://@maticnetwork/maticjs/./src/utils/index.ts", "webpack://@maticnetwork/maticjs/./src/pos/pos_token.ts", "webpack://@maticnetwork/maticjs/./src/pos/erc20.ts", "webpack://@maticnetwork/maticjs/./src/pos/root_chain_manager.ts", "webpack://@maticnetwork/maticjs/./src/pos/exit_util.ts", "webpack://@maticnetwork/maticjs/./src/pos/root_chain.ts", "webpack://@maticnetwork/maticjs/./src/pos/erc721.ts", "webpack://@maticnetwork/maticjs/./src/pos/erc1155.ts", "webpack://@maticnetwork/maticjs/./src/pos/gas_swapper.ts", "webpack://@maticnetwork/maticjs/./src/pos/index.ts", "webpack://@maticnetwork/maticjs/./src/default.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/zkevm_token.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/zkevm_custom_bridge.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/erc20.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/zkevm_bridge.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/bridge_util.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/zkevm_wrapper.ts", "webpack://@maticnetwork/maticjs/./src/zkevm/index.ts", "webpack://@maticnetwork/maticjs/./src/index.ts"], "sourcesContent": ["/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "module.exports = require(\"buffer\");", "module.exports = require(\"node-fetch\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import { ITransactionRequestConfig, ITransactionWriteResult } from \"../interfaces\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseContractMethod {\n    constructor(public logger: Logger) {\n\n    }\n    abstract get address(): string;\n    abstract read<T>(tx?: ITransactionRequestConfig, defaultBlock?: number | string): Promise<T>;\n    abstract write(tx: ITransactionRequestConfig,): ITransactionWriteResult;\n    abstract estimateGas(tx: ITransactionRequestConfig,): Promise<number>;\n    abstract encodeABI(): any;\n}", "export enum Log_Event_Signature {\n    // PlasmaErc20WithdrawEventSig = '0xebff2602b3f468259e1e99f613fed6691f3a6526effe6ef3e768ba7ae7a36c4f',\n    // PlasmaErc721WithdrawEventSig = '0x9b1bfa7fa9ee420a16e124f794c35ac9f90472acc99140eb2f6447c714cad8eb',\n    Erc20Transfer = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',\n    Erc721Transfer = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',\n    Erc1155Transfer = '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62',\n    Erc721BatchTransfer = '0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df',\n    Erc1155BatchTransfer = '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb',\n    Erc721TransferWithMetadata = '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14',\n}\n", "export enum ERROR_TYPE {\n    AllowedOnRoot = \"allowed_on_root\",\n    AllowedOnChild = \"allowed_on_child\",\n    Unknown = \"unknown\",\n    ProofAPINotSet = \"proof_api_not_set\",\n    TransactionOptionNotObject = \"transation_object_not_object\",\n    BurnTxNotCheckPointed = \"burn_tx_not_checkpointed\",\n    EIP1559NotSupported = \"eip-1559_not_supported\",\n    NullSpenderAddress = \"null_spender_address\",\n    AllowedOnNonNativeTokens = \"allowed_on_non_native_token\",\n    AllowedOnMainnet = \"allowed_on_mainnet\",\n    BridgeAdapterNotFound = \"bridge_adapter_address_not_passed\"\n}\n", "import { BaseContract } from \"../abstracts\";\nimport { ITransactionRequestConfig, ITransactionReceipt, ITransactionData, IBlock, IBlockWithTransaction, IJsonRpcRequestPayload, IJsonRpcResponse, ITransactionWriteResult } from \"../interfaces\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseWeb3Client {\n    abstract name: string;\n\n    constructor(public logger: Logger) {\n\n    }\n\n    abstract getContract(address: string, abi: any): BaseContract;\n\n    abstract read(config: ITransactionRequestConfig): Promise<string>;\n\n    abstract write(config: ITransactionRequestConfig): ITransactionWriteResult;\n    abstract getGasPrice(): Promise<string>;\n    abstract estimateGas(config: ITransactionRequestConfig): Promise<number>;\n    abstract getChainId(): Promise<number>;\n    abstract getTransactionCount(address: string, blockNumber: any): Promise<number>;\n\n    abstract getTransaction(transactionHash: string): Promise<ITransactionData>;\n    abstract getTransactionReceipt(transactionHash: string): Promise<ITransactionReceipt>;\n    // abstract extend(property: string, methods: IMethod[])\n\n    abstract getBlock(blockHashOrBlockNumber): Promise<IBlock>;\n    abstract getBlockWithTransaction(blockHashOrBlockNumber): Promise<IBlockWithTransaction>;\n    abstract hexToNumber(value: any): number;\n    abstract hexToNumberString(value: any): string;\n    abstract getBalance(address: string): Promise<string>;\n    abstract getAccounts(): Promise<string[]>;\n    abstract signTypedData(signer: string, typedData: object): Promise<string>;\n\n    getRootHash?(startBlock: number, endBlock: number) {\n        return this.sendRPCRequest({\n            jsonrpc: '2.0',\n            method: 'eth_getRootHash',\n            params: [Number(startBlock), Number(endBlock)],\n            id: new Date().getTime()\n        }).then(payload => {\n            return String(payload.result);\n        });\n    }\n\n    getAccountsUsingRPC_() {\n        return this.sendRPCRequest({\n            jsonrpc: '2.0',\n            method: 'eth_accounts',\n            params: [],\n            id: new Date().getTime()\n        }).then(payload => {\n            return payload.result;\n        });\n    }\n\n    abstract sendRPCRequest(request: IJsonRpcRequestPayload): Promise<IJsonRpcResponse>;\n\n    abstract encodeParameters(params: any[], types: any[]): string;\n    abstract decodeParameters(hexString: string, types: any[]): any[];\n    abstract etheriumSha3(...value): string;\n\n}\n", "import { BaseContractMethod } from \"../abstracts\";\nimport { Logger } from \"../utils\";\n\nexport abstract class BaseContract {\n\n    constructor(public address: string, public logger:Logger) {\n\n    }\n\n    abstract method(methodName: string, ...args): BaseContractMethod;\n}", "import { throwNotImplemented } from \"..\";\n\nexport abstract class BaseBigNumber {\n    static isBN(value) {\n        return throwNotImplemented<boolean>();\n    }\n\n    abstract toString(): string;\n    abstract toNumber(): number;\n    abstract add(value: BaseBigNumber): BaseBigNumber;\n    abstract sub(value: BaseBigNumber): BaseBigNumber;\n    abstract mul(value: BaseBigNumber): BaseBigNumber;\n    abstract div(value: BaseBigNumber): BaseBigNumber;\n\n    abstract lte(value: BaseBigNumber): boolean;\n    abstract lt(value: BaseBigNumber): boolean;\n    abstract gte(value: BaseBigNumber): boolean;\n    abstract gt(value: BaseBigNumber): boolean;\n    abstract eq(value: BaseBigNumber): boolean;\n}\n", "import { throwNotImplemented } from \"..\";\nimport { BaseBigNumber } from \"../abstracts\";\n\nexport class EmptyBigNumber extends BaseBigNumber {\n\n    constructor(value) {\n        super();\n    }\n\n    toString(base?) {\n        return throwNotImplemented<string>();\n    }\n\n    toNumber() {\n        return throwNotImplemented<number>();\n    }\n\n    add(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    sub(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    mul(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    div(value: BaseBigNumber) {\n        return throwNotImplemented<BaseBigNumber>();\n    }\n\n    lte(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n    }\n\n    lt(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    gte(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    gt(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n\n    }\n\n    eq(value: BaseBigNumber) {\n        return throwNotImplemented<boolean>();\n    }\n}\n", "import { BaseBigNumber } from \"../abstracts\";\nimport { utils } from \"../utils\";\n\nexport class Converter {\n    static toHex(amount: BaseBigNumber | string | number) {\n        const dataType = typeof amount;\n        if (dataType === 'number') {\n            amount = new utils.BN(amount);\n        } else if (dataType === 'string') {\n            if ((amount as string).slice(0, 2) === '0x') {\n                return amount;\n            }\n            amount = new utils.BN(amount);\n        }\n        if (utils.BN.isBN(amount)) {\n            return '0x' + amount.toString(16);\n        }\n        else {\n            throw new Error(`Invalid value ${amount}, value is not a number.`);\n        }\n    }\n\n    static toBN(amount: BaseBigNumber | string | number): BaseBigNumber {\n        const dataType = typeof amount;\n        if (dataType === 'string') {\n            if ((amount as string).slice(0, 2) === '0x') {\n                amount = parseInt(amount as string, 16);\n            }\n        }\n        if (!utils.BN.isBN(amount)) {\n            amount = new utils.BN(amount);\n        }\n        return amount as BaseBigNumber;\n    }\n}\n", "import { IPlugin } from \"../interfaces\";\nimport { defaultExport } from \"../default\";\n\nexport const use = (plugin, ...payload) => {\n    const pluginInstance: IPlugin = typeof plugin === \"function\" ? new plugin() : plugin;\n    return pluginInstance.setup(defaultExport, ...payload);\n};", "export interface IEventBusPromise<T> extends Promise<T> {\n    on(event: string, cb: Function);\n    emit(event: string, ...args);\n    destroy();\n}\n\nexport const eventBusPromise = function <T>(executor: (resolve: (value?: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void) {\n    const promise: IEventBusPromise<T> = new Promise(executor) as any;\n    const eventBus = new EventBus();\n    promise.on = eventBus.on.bind(eventBus);\n    promise.emit = eventBus.emit.bind(eventBus);\n    return promise;\n};\n\nexport class EventBus {\n\n    constructor(ctx?) {\n        this._ctx = ctx;\n    }\n\n    private _ctx;\n\n    private _events: {\n        [key: string]: Function[]\n    } = {};\n\n    on(event: string, cb: Function) {\n        if (this._events[event] == null) {\n            this._events[event] = [];\n        }\n        this._events[event].push(cb);\n        return this;\n    }\n\n    off(event: string, cb: Function) {\n        if (this._events[event]) {\n            if (cb) {\n                const index = this._events[event].indexOf(cb);\n                this._events[event].splice(index, 1);\n            }\n            else {\n                this._events[event] = [];\n            }\n        }\n    }\n\n    emit(event: string, ...args) {\n        const events = this._events[event] || [];\n        return Promise.all(\n            events.map(cb => {\n                const result = cb.call(this._ctx, ...args);\n                return result && result.then ? result : Promise.resolve(result);\n            })\n        );\n    }\n\n    destroy() {\n        this._events = null;\n        this._ctx = null;\n    }\n}", "import { ERROR_TYPE } from \"../enums\";\nimport { IError } from \"../interfaces\";\n\nexport class <PERSON>rrorHelper implements IError {\n    type: ERROR_TYPE;\n    message: string;\n\n    constructor(type: ERROR_TYPE, info?) {\n        this.type = type;\n        this.message = this.getMsg_(info);\n    }\n\n    throw() {\n        throw this.get();\n    }\n\n    get() {\n        return {\n            message: this.message,\n            type: this.type\n        } as IError;\n    }\n\n    private getMsg_(info) {\n        let errMsg: string;\n        switch (this.type) {\n            case ERROR_TYPE.AllowedOnChild:\n                errMsg = `The action ${info} is allowed only on child token.`;\n                break;\n            case ERROR_TYPE.AllowedOnRoot:\n                errMsg = `The action ${info} is allowed only on root token.`;\n                break;\n            case ERROR_TYPE.AllowedOnMainnet:\n                errMsg = `The action is allowed only on mainnet chains.`;\n                break;\n            case ERROR_TYPE.ProofAPINotSet:\n                errMsg = `Proof api is not set, please set it using \"setProofApi\"`;\n                break;\n            case ERROR_TYPE.BurnTxNotCheckPointed:\n                errMsg = `Burn transaction has not been checkpointed as yet`;\n                break;\n            case ERROR_TYPE.EIP1559NotSupported:\n                errMsg = `${info ? 'Root' : 'Child'} chain doesn't support eip-1559`;\n                break;\n            case ERROR_TYPE.NullSpenderAddress:\n                errMsg = `Please provide spender address.`;\n                break;\n            default:\n                if (!this.type) {\n                    this.type = ERROR_TYPE.Unknown;\n                }\n                errMsg = this.message;\n                break;\n        }\n        return errMsg;\n    }\n}", "import { ERROR_TYPE } from \"../enums\";\nimport { <PERSON>rror<PERSON>elper } from \"./error_helper\";\n\nexport class Logger {\n\n    private isEnabled: boolean;\n\n    enableLog(value) {\n        this.isEnabled = value ? true : false;\n    }\n\n    log(...message) {\n        if (this.isEnabled) {\n            console.log(...message);\n        }\n    }\n\n    error(type: ERROR_TYPE, info?) {\n        return new ErrorHelper(type, info);\n    }\n}", "export const merge = (...obj) => {\n    return Object.assign({}, ...obj);\n};", "import { promiseResolve } from '..';\nimport { IMapPromiseOption } from '../interfaces';\n\nconst runPromises = (promises: Array<Promise<any>>, converter: Function) => {\n  const maps = promises.map((val, index) => {\n    return converter(val, index);\n  });\n  return Promise.all(maps);\n};\n\nexport function mapPromise(values: any[], converter: Function, option: IMapPromiseOption = {} as any) {\n  const valuesLength = values.length;\n  const concurrency = option.concurrency || valuesLength;\n\n  let result = [];\n  const limitPromiseRun: () => Promise<any> = () => {\n    const promises = values.splice(0, concurrency);\n    return runPromises(promises, converter).then(promiseResult => {\n      result = result.concat(promiseResult);\n\n      return valuesLength > result.length ?\n        limitPromiseRun() : promiseResolve(result);\n    });\n  };\n\n  return limitPromiseRun();\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"@ethereumjs/util\");", "function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`positive integer expected, not ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`boolean expected, not ${b}`);\n}\n// copied from utils\nexport function isBytes(a) {\n    return (a instanceof Uint8Array ||\n        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));\n}\nfunction bytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Uint8Array expected of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(h.outputLen);\n    number(h.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { bytes as abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a) {\n    return (a instanceof Uint8Array ||\n        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));\n}\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// The rotate left (circular left shift) operation for uint32\nexport const rotl = (word, shift) => (word << shift) | ((word >>> (32 - shift)) >>> 0);\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\n// The byte swap operation for uint32\nexport const byteSwap = (word) => ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff);\n// Conditionally byte swap if on a big-endian platform\nexport const byteSwapIfBE = isLE ? (n) => n : (n) => byteSwap(n);\n// In place byte swap for Uint32Array\nexport function byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n}\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    abytes(bytes);\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 };\nfunction asciiToBase16(char) {\n    if (char >= asciis._0 && char <= asciis._9)\n        return char - asciis._0;\n    if (char >= asciis._A && char <= asciis._F)\n        return char - (asciis._A - 10);\n    if (char >= asciis._a && char <= asciis._f)\n        return char - (asciis._a - 10);\n    return;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { bytes, exists, number, output } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport { Hash, u32, toBytes, wrapConstructor, wrapXOFConstructorWithOpts, isLE, byteSwap32, } from './utils.js';\n// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.\n// It's called a sponge function.\n// Various per round constants calculations\nconst SHA3_PI = [];\nconst SHA3_ROTL = [];\nconst _SHA3_IOTA = [];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n    // Pi\n    [x, y] = [y, (2 * x + 3 * y) % 5];\n    SHA3_PI.push(2 * (5 * y + x));\n    // Rotational\n    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n    // Iota\n    let t = _0n;\n    for (let j = 0; j < 7; j++) {\n        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n        if (R & _2n)\n            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n    }\n    _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h, l, s) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h, l, s) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n// Same as keccakf1600, but allows to skip some rounds\nexport function keccakP(s, rounds = 24) {\n    const B = new Uint32Array(5 * 2);\n    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n    for (let round = 24 - rounds; round < 24; round++) {\n        // Theta θ\n        for (let x = 0; x < 10; x++)\n            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n        for (let x = 0; x < 10; x += 2) {\n            const idx1 = (x + 8) % 10;\n            const idx0 = (x + 2) % 10;\n            const B0 = B[idx0];\n            const B1 = B[idx0 + 1];\n            const Th = rotlH(B0, B1, 1) ^ B[idx1];\n            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n            for (let y = 0; y < 50; y += 10) {\n                s[x + y] ^= Th;\n                s[x + y + 1] ^= Tl;\n            }\n        }\n        // Rho (ρ) and Pi (π)\n        let curH = s[2];\n        let curL = s[3];\n        for (let t = 0; t < 24; t++) {\n            const shift = SHA3_ROTL[t];\n            const Th = rotlH(curH, curL, shift);\n            const Tl = rotlL(curH, curL, shift);\n            const PI = SHA3_PI[t];\n            curH = s[PI];\n            curL = s[PI + 1];\n            s[PI] = Th;\n            s[PI + 1] = Tl;\n        }\n        // Chi (χ)\n        for (let y = 0; y < 50; y += 10) {\n            for (let x = 0; x < 10; x++)\n                B[x] = s[y + x];\n            for (let x = 0; x < 10; x++)\n                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n        }\n        // Iota (ι)\n        s[0] ^= SHA3_IOTA_H[round];\n        s[1] ^= SHA3_IOTA_L[round];\n    }\n    B.fill(0);\n}\nexport class Keccak extends Hash {\n    // NOTE: we accept arguments in bytes instead of bits here.\n    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {\n        super();\n        this.blockLen = blockLen;\n        this.suffix = suffix;\n        this.outputLen = outputLen;\n        this.enableXOF = enableXOF;\n        this.rounds = rounds;\n        this.pos = 0;\n        this.posOut = 0;\n        this.finished = false;\n        this.destroyed = false;\n        // Can be passed from user as dkLen\n        number(outputLen);\n        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n        if (0 >= this.blockLen || this.blockLen >= 200)\n            throw new Error('Sha3 supports only keccak-f1600 function');\n        this.state = new Uint8Array(200);\n        this.state32 = u32(this.state);\n    }\n    keccak() {\n        if (!isLE)\n            byteSwap32(this.state32);\n        keccakP(this.state32, this.rounds);\n        if (!isLE)\n            byteSwap32(this.state32);\n        this.posOut = 0;\n        this.pos = 0;\n    }\n    update(data) {\n        exists(this);\n        const { blockLen, state } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            for (let i = 0; i < take; i++)\n                state[this.pos++] ^= data[pos++];\n            if (this.pos === blockLen)\n                this.keccak();\n        }\n        return this;\n    }\n    finish() {\n        if (this.finished)\n            return;\n        this.finished = true;\n        const { state, suffix, pos, blockLen } = this;\n        // Do the padding\n        state[pos] ^= suffix;\n        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)\n            this.keccak();\n        state[blockLen - 1] ^= 0x80;\n        this.keccak();\n    }\n    writeInto(out) {\n        exists(this, false);\n        bytes(out);\n        this.finish();\n        const bufferOut = this.state;\n        const { blockLen } = this;\n        for (let pos = 0, len = out.length; pos < len;) {\n            if (this.posOut >= blockLen)\n                this.keccak();\n            const take = Math.min(blockLen - this.posOut, len - pos);\n            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n            this.posOut += take;\n            pos += take;\n        }\n        return out;\n    }\n    xofInto(out) {\n        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n        if (!this.enableXOF)\n            throw new Error('XOF is not possible for this instance');\n        return this.writeInto(out);\n    }\n    xof(bytes) {\n        number(bytes);\n        return this.xofInto(new Uint8Array(bytes));\n    }\n    digestInto(out) {\n        output(out, this);\n        if (this.finished)\n            throw new Error('digest() was already called');\n        this.writeInto(out);\n        this.destroy();\n        return out;\n    }\n    digest() {\n        return this.digestInto(new Uint8Array(this.outputLen));\n    }\n    destroy() {\n        this.destroyed = true;\n        this.state.fill(0);\n    }\n    _cloneInto(to) {\n        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));\n        to.state32.set(this.state32);\n        to.pos = this.pos;\n        to.posOut = this.posOut;\n        to.finished = this.finished;\n        to.rounds = rounds;\n        // Suffix can change in cSHAKE\n        to.suffix = suffix;\n        to.outputLen = outputLen;\n        to.enableXOF = enableXOF;\n        to.destroyed = this.destroyed;\n        return to;\n    }\n}\nconst gen = (suffix, blockLen, outputLen) => wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\nexport const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/**\n * SHA3-256 hash function\n * @param message - that would be hashed\n */\nexport const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\nexport const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\nexport const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\nexport const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/**\n * keccak-256 hash function. Different from SHA3-256.\n * @param message - that would be hashed\n */\nexport const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\nexport const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\nexport const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\nconst genShake = (suffix, blockLen, outputLen) => wrapXOFConstructorWithOpts((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));\nexport const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\nexport const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n//# sourceMappingURL=sha3.js.map", "import assert from \"@noble/hashes/_assert\";\nimport { hexToBytes as _hexToBytes } from \"@noble/hashes/utils\";\nconst assertBool = assert.bool;\nconst assertBytes = assert.bytes;\nexport { assertBool, assertBytes };\nexport { bytesToHex, bytesToHex as toHex, concatBytes, createView, utf8ToBytes } from \"@noble/hashes/utils\";\n// buf.toString('utf8') -> bytesToUtf8(buf)\nexport function bytesToUtf8(data) {\n    if (!(data instanceof Uint8Array)) {\n        throw new TypeError(`bytesToUtf8 expected Uint8Array, got ${typeof data}`);\n    }\n    return new TextDecoder().decode(data);\n}\nexport function hexToBytes(data) {\n    const sliced = data.startsWith(\"0x\") ? data.substring(2) : data;\n    return _hexToBytes(sliced);\n}\n// buf.equals(buf2) -> equalsBytes(buf, buf2)\nexport function equalsBytes(a, b) {\n    if (a.length !== b.length) {\n        return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n// Internal utils\nexport function wrapHash(hash) {\n    return (msg) => {\n        assert.bytes(msg);\n        return hash(msg);\n    };\n}\n// TODO(v3): switch away from node crypto, remove this unnecessary variable.\nexport const crypto = (() => {\n    const webCrypto = typeof globalThis === \"object\" && \"crypto\" in globalThis ? globalThis.crypto : undefined;\n    const nodeRequire = typeof module !== \"undefined\" &&\n        typeof module.require === \"function\" &&\n        module.require.bind(module);\n    return {\n        node: nodeRequire && !webCrypto ? nodeRequire(\"crypto\") : undefined,\n        web: webCrypto\n    };\n})();\n", "import { keccak_224, keccak_256, keccak_384, keccak_512 } from \"@noble/hashes/sha3\";\nimport { wrapHash } from \"./utils.js\";\nexport const keccak224 = wrapHash(keccak_224);\nexport const keccak256 = (() => {\n    const k = wrapHash(keccak_256);\n    k.create = keccak_256.create;\n    return k;\n})();\nexport const keccak384 = wrapHash(keccak_384);\nexport const keccak512 = wrapHash(keccak_512);\n", "import { keccak224, keccak384, keccak256 as k256, keccak512 } from 'ethereum-cryptography/keccak';\n\nexport class Keccak {\n    /**\n     * Throws if input is not a buffer\n     * @param {Buffer} input value to check\n     */\n    static assertIsBuffer = function (input: Buffer): void {\n        if (!Buffer.isBuffer(input)) {\n        const msg = `This method only supports <PERSON>uffer but input was: ${input}`;\n        throw new Error(msg);\n        }\n    };\n\n    /**\n     * Creates Keccak hash of a Buffer input\n     * @param a The input data (Buffer)\n     * @param bits (number = 256) The Keccak width\n     */\n    static keccak = function (a: Buffer, bits = 256): Buffer {\n        Keccak.assertIsBuffer(a);\n        switch (bits) {\n            case 224: {\n                return Buffer.from(keccak224(a));\n            }\n            case 256: {\n                return Buffer.from(k256(a));\n            }\n            case 384: {\n                return Buffer.from(keccak384(a));\n            }\n            case 512: {\n                return Buffer.from(keccak512(a));\n            }\n            default: {\n                throw new Error(`Invald algorithm: keccak${bits}`);\n            }\n        }\n    };\n\n    /**\n     * Creates Keccak-256 hash of the input, alias for keccak(a, 256).\n     * @param a The input data (Buffer)\n     */\n    static keccak256 = function (a: Buffer): Buffer {\n        return Keccak.keccak(a);\n    };\n}\n", "import { zeros } from '@ethereumjs/util';\nimport { Keccak } from './keccak';\nconst sha3 = Keccak.keccak256;\n\nimport { Buffer as SafeBuffer } from \"safe-buffer\";\n\nexport class MerkleTree {\n    leaves: any;\n    layers: any;\n\n    constructor(leaves = []) {\n        if (leaves.length < 1) {\n            throw new Error('Atleast 1 leaf needed');\n        }\n\n        const depth = Math.ceil(Math.log(leaves.length) / Math.log(2));\n        if (depth > 20) {\n            throw new Error('Depth must be 20 or less');\n        }\n\n        this.leaves = leaves.concat(\n            Array.from(\n                // tslint:disable-next-line\n                Array(Math.pow(2, depth) - leaves.length),\n                () => zeros(32)\n            )\n        );\n        this.layers = [this.leaves];\n        this.createHashes(this.leaves);\n    }\n\n    createHashes(nodes) {\n        if (nodes.length === 1) {\n            return false;\n        }\n\n\n        const treeLevel = [];\n        for (let i = 0; i < nodes.length; i += 2) {\n            const left = nodes[i];\n            const right = nodes[i + 1];\n\n            const data = SafeBuffer.concat([left, right]);\n            treeLevel.push(sha3(data as unknown as <PERSON>uffer));\n        }\n\n        // is odd number of nodes\n        if (nodes.length % 2 === 1) {\n            treeLevel.push(nodes[nodes.length - 1]);\n        }\n\n        this.layers.push(treeLevel);\n        this.createHashes(treeLevel);\n    }\n\n    getLeaves() {\n        return this.leaves;\n    }\n\n    getLayers() {\n        return this.layers;\n    }\n\n    getRoot() {\n        return this.layers[this.layers.length - 1][0];\n    }\n\n    getProof(leaf) {\n        let index = -1;\n        for (let i = 0; i < this.leaves.length; i++) {\n            if (SafeBuffer.compare(leaf, this.leaves[i]) === 0) {\n                index = i;\n            }\n        }\n\n        const proof = [];\n        if (index <= this.getLeaves().length) {\n            let siblingIndex;\n            for (let i = 0; i < this.layers.length - 1; i++) {\n                if (index % 2 === 0) {\n                    siblingIndex = index + 1;\n                } else {\n                    siblingIndex = index - 1;\n                }\n                index = Math.floor(index / 2);\n                proof.push(this.layers[i][siblingIndex]);\n            }\n        }\n        return proof;\n    }\n\n    verify(value, index, root, proof) {\n        if (!Array.isArray(proof) || !value || !root) {\n            return false;\n        }\n\n        let hash = value;\n        for (let i = 0; i < proof.length; i++) {\n            const node = proof[i];\n            if (index % 2 === 0) {\n                hash = sha3(SafeBuffer.concat([hash, node]) as unknown as Buffer);\n            } else {\n                hash = sha3(SafeBuffer.concat([node, hash]) as unknown as Buffer);\n            }\n\n            index = Math.floor(index / 2);\n        }\n\n        return SafeBuffer.compare(hash, root) === 0;\n    }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"bn.js\");", "import {\n    ITransformableToArray,\n    PrefixedHexString,\n    ITransformableToBuffer,\n    BN\n} from \"./types\";\n\nexport type ToBufferInputTypes =\n    | PrefixedHexString\n    | number\n    | BN\n    | Buffer\n    | Uint8Array\n    | number[]\n    | ITransformableToArray\n    | ITransformableToBuffer\n    | null\n    | undefined;\n\nexport class BufferUtil {\n    static intToHex = function (i: number) {\n        if (!Number.isSafeInteger(i) || i < 0) {\n            throw new Error(`Received an invalid integer type: ${i}`);\n        }\n        return `0x${i.toString(16)}`;\n    };\n\n    static padToEven(value: string): string {\n        let a = value;\n\n        if (typeof a !== 'string') {\n            throw new Error(`[padToEven] value must be type 'string', received ${typeof a}`);\n        }\n\n        if (a.length % 2) a = `0${a}`;\n\n        return a;\n    }\n\n    static isHexPrefixed(str: string): boolean {\n        if (typeof str !== 'string') {\n            throw new Error(`[isHexPrefixed] input must be type 'string', received type ${typeof str}`);\n        }\n\n        return str[0] === '0' && str[1] === 'x';\n    }\n\n    static stripHexPrefix = (str: string): string => {\n        if (typeof str !== 'string') {\n            throw new Error(`[stripHexPrefix] input must be type 'string', received ${typeof str}`);\n        }\n\n        return BufferUtil.isHexPrefixed(str) ? str.slice(2) : str;\n    }\n\n    /**\n     * Converts an `Number` to a `Buffer`\n     * @param {Number} i\n     * @return {Buffer}\n     */\n    static intToBuffer = function (i: number) {\n        const hex = BufferUtil.intToHex(i);\n        return Buffer.from(BufferUtil.padToEven(hex.slice(2)), 'hex');\n    };\n\n    static isHexString(value: string, length?: number): boolean {\n        if (typeof value !== 'string' || !value.match(/^0x[0-9A-Fa-f]*$/)) return false;\n\n        if (length && value.length !== 2 + 2 * length) return false;\n\n        return true;\n    }\n\n\n    static toBuffer = function (v: ToBufferInputTypes): Buffer {\n        if (v === null || v === undefined) {\n            return Buffer.allocUnsafe(0);\n        }\n\n        if (Buffer.isBuffer(v)) {\n            return Buffer.from(v);\n        }\n\n        if (Array.isArray(v) || v instanceof Uint8Array) {\n            return Buffer.from(v as Uint8Array);\n        }\n\n        if (typeof v === 'string') {\n            if (!BufferUtil.isHexString(v)) {\n                throw new Error(\n                    `Cannot convert string to buffer. toBuffer only supports 0x-prefixed hex strings and this string was given: ${v}`\n                );\n            }\n            return Buffer.from(BufferUtil.padToEven(BufferUtil.stripHexPrefix(v)), 'hex');\n        }\n\n        if (typeof v === 'number') {\n            return BufferUtil.intToBuffer(v);\n        }\n\n        if (BN.isBN(v)) {\n            if (v.isNeg()) {\n                throw new Error(`Cannot convert negative BN to buffer. Given: ${v}`);\n            }\n            return v.toArrayLike(Buffer);\n        }\n\n        if (v.toArray) {\n            // converts a BN to a Buffer\n            return Buffer.from(v.toArray());\n        }\n\n        if (v.toBuffer) {\n            return Buffer.from(v.toBuffer());\n        }\n\n        throw new Error('invalid type');\n    };\n\n    /**\n     * Converts a `Buffer` into a `0x`-prefixed hex `String`.\n     * @param buf `Buffer` object to convert\n     */\n    static bufferToHex = function (buf: Buffer): string {\n        buf = BufferUtil.toBuffer(buf);\n        return '0x' + buf.toString('hex');\n    };\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"rlp\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"@ethereumjs/trie\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"@ethereumjs/block\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"@ethereumjs/common\");", "export const MAX_AMOUNT = '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff';\nexport const ADDRESS_ZERO = '******************************************';\nexport const DAI_PERMIT_TYPEHASH = \"0xea2aa0a1be11a07ed86d755c93467f4f82362b452371d1ba94d1715123511acb\";\nexport const EIP_2612_PERMIT_TYPEHASH = \"0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9\";\nexport const EIP_2612_DOMAIN_TYPEHASH = \"0x8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f\";\nexport const UNISWAP_DOMAIN_TYPEHASH = \"0x8cad95687ba82c2ce50e74f7b754645e5117c3a5bec8151c0726d5857980a866\";\nexport const _GLOBAL_INDEX_MAINNET_FLAG = BigInt(2 ** 64);\nexport enum Permit {\n    DAI = \"DAI\",\n    EIP_2612 = \"EIP_2612\",\n    UNISWAP = \"UNISWAP\",\n}\n", "import { BaseWeb3Client } from \"../abstracts\";\nimport { MerkleTree } from \"./merkle_tree\";\nimport { setLengthLeft } from \"@ethereumjs/util\";\nimport { Keccak } from \"./keccak\";\nimport { BufferUtil } from \"./buffer-utils\";\nimport rlp from \"rlp\";\nimport { ITransactionReceipt, IBlock, IBlockWithTransaction } from \"../interfaces\";\nimport { mapPromise } from \"./map_promise\";\nimport { Trie as TRIE } from '@ethereumjs/trie';\nimport { BlockHeader } from '@ethereumjs/block';\nimport { Converter, promiseResolve, utils } from \"..\";\nimport { Common, Chain, Hardfork } from '@ethereumjs/common';\n\n// Implementation adapted from <PERSON>'s `matic-proofs` library used under MIT License\n// https://github.com/TomAFrench/matic-proofs\n\nexport class ProofUtil {\n\n    static async getFastMerkleProof(\n        web3: BaseWeb3Client,\n        blockNumber: number,\n        startBlock: number,\n        endBlock: number\n    ): Promise<string[]> {\n        const merkleTreeDepth = Math.ceil(Math.log2(endBlock - startBlock + 1));\n\n        // We generate the proof root down, whereas we need from leaf up\n        const reversedProof: string[] = [];\n\n        const offset = startBlock;\n        const targetIndex = blockNumber - offset;\n        let leftBound = 0;\n        let rightBound = endBlock - offset;\n        //   console.log(\"Searching for\", targetIndex);\n        for (let depth = 0; depth < merkleTreeDepth; depth += 1) {\n            const nLeaves = 2 ** (merkleTreeDepth - depth);\n\n            // The pivot leaf is the last leaf which is included in the left subtree\n            const pivotLeaf = leftBound + nLeaves / 2 - 1;\n\n            if (targetIndex > pivotLeaf) {\n                // Get the root hash to the merkle subtree to the left\n                const newLeftBound = pivotLeaf + 1;\n                // eslint-disable-next-line no-await-in-loop\n                const subTreeMerkleRoot = await this.queryRootHash(web3, offset + leftBound, offset + pivotLeaf);\n                reversedProof.push(subTreeMerkleRoot);\n                leftBound = newLeftBound;\n            } else {\n                // Things are more complex when querying to the right.\n                // Root hash may come some layers down so we need to build a full tree by padding with zeros\n                // Some trees may be completely empty\n\n                const newRightBound = Math.min(rightBound, pivotLeaf);\n\n                // Expect the merkle tree to have a height one less than the current layer\n                const expectedHeight = merkleTreeDepth - (depth + 1);\n                if (rightBound <= pivotLeaf) {\n                    // Tree is empty so we repeatedly hash zero to correct height\n                    const subTreeMerkleRoot = this.recursiveZeroHash(expectedHeight, web3);\n                    reversedProof.push(subTreeMerkleRoot);\n                } else {\n                    // Height of tree given by RPC node\n                    const subTreeHeight = Math.ceil(Math.log2(rightBound - pivotLeaf));\n\n                    // Find the difference in height between this and the subtree we want\n                    const heightDifference = expectedHeight - subTreeHeight;\n\n                    // For every extra layer we need to fill 2*n leaves filled with the merkle root of a zero-filled Merkle tree\n                    // We need to build a tree which has heightDifference layers\n\n                    // The first leaf will hold the root hash as returned by the RPC\n                    // eslint-disable-next-line no-await-in-loop\n                    const remainingNodesHash = await this.queryRootHash(web3, offset + pivotLeaf + 1, offset + rightBound);\n\n                    // The remaining leaves will hold the merkle root of a zero-filled tree of height subTreeHeight\n                    const leafRoots = this.recursiveZeroHash(subTreeHeight, web3);\n\n                    // Build a merkle tree of correct size for the subtree using these merkle roots\n                    const leaves = Array.from({ length: 2 ** heightDifference }, () => BufferUtil.toBuffer(leafRoots));\n                    leaves[0] = remainingNodesHash;\n                    const subTreeMerkleRoot = new MerkleTree(leaves).getRoot();\n                    reversedProof.push(subTreeMerkleRoot);\n                }\n                rightBound = newRightBound;\n            }\n        }\n\n        return reversedProof.reverse();\n    }\n\n    static buildBlockProof(maticWeb3: BaseWeb3Client, startBlock: number, endBlock: number, blockNumber: number) {\n        return ProofUtil.getFastMerkleProof(\n            maticWeb3, blockNumber, startBlock, endBlock\n        ).then(proof => {\n            return BufferUtil.bufferToHex(\n                Buffer.concat(\n                    proof.map(p => {\n                        return BufferUtil.toBuffer(p);\n                    })\n                )\n            );\n        });\n    }\n\n    static queryRootHash(client: BaseWeb3Client, startBlock: number, endBlock: number) {\n        return client.getRootHash(startBlock, endBlock).then(rootHash => {\n            return BufferUtil.toBuffer(`0x${rootHash}`);\n        }).catch(_ => {\n            return null;\n        });\n    }\n\n    static recursiveZeroHash(n: number, client: BaseWeb3Client) {\n        if (n === 0) return '******************************************000000000000000000000000';\n        const subHash = this.recursiveZeroHash(n - 1, client);\n        return Keccak.keccak256(\n            BufferUtil.toBuffer(client.encodeParameters([subHash, subHash], ['bytes32', 'bytes32'],))\n        );\n    }\n\n    static getReceiptProof(receipt: ITransactionReceipt, block: IBlockWithTransaction, web3: BaseWeb3Client, requestConcurrency = Infinity, receiptsVal?: ITransactionReceipt[]) {\n        const stateSyncTxHash = BufferUtil.bufferToHex(ProofUtil.getStateSyncTxHash(block));\n        const receiptsTrie = new TRIE();\n        let receiptPromise: Promise<ITransactionReceipt[]>;\n        if (!receiptsVal) {\n            const receiptPromises = [];\n            block.transactions.forEach(tx => {\n                if (tx.transactionHash === stateSyncTxHash) {\n                    // ignore if tx hash is bor state-sync tx\n                    return;\n                }\n                receiptPromises.push(\n                    web3.getTransactionReceipt(tx.transactionHash)\n                );\n            });\n            receiptPromise = mapPromise(\n                receiptPromises,\n                val => {\n                    return val;\n                },\n                {\n                    concurrency: requestConcurrency,\n                }\n            );\n        }\n        else {\n            receiptPromise = promiseResolve(receiptsVal);\n        }\n\n        return receiptPromise.then(receipts => {\n            return Promise.all(\n                receipts.map(siblingReceipt => {\n                    const path = rlp.encode(siblingReceipt.transactionIndex);\n                    const rawReceipt = ProofUtil.getReceiptBytes(siblingReceipt);\n                    return receiptsTrie.put(path, rawReceipt);\n                })\n            );\n        }).then(_ => {\n            return receiptsTrie.findPath(rlp.encode(receipt.transactionIndex), true);\n        }).then(result => {\n            if (result.remaining.length > 0) {\n                throw new Error('Node does not contain the key');\n            }\n            // result.node.value\n            const prf = {\n                blockHash: BufferUtil.toBuffer(receipt.blockHash),\n                parentNodes: result.stack.map(s => s.raw()),\n                root: ProofUtil.getRawHeader(block).receiptTrie,\n                path: rlp.encode(receipt.transactionIndex),\n                value: ProofUtil.isTypedReceipt(receipt) ? result.node.value : rlp.decode(result.node.value.toString())\n            };\n            return prf;\n        });\n    }\n\n    static isTypedReceipt(receipt: ITransactionReceipt) {\n        const hexType = Converter.toHex(receipt.type);\n        return receipt.status != null && hexType !== \"0x0\" && hexType !== \"0x\";\n    }\n\n    // getStateSyncTxHash returns block's tx hash for state-sync receipt\n    // Bor blockchain includes extra receipt/tx for state-sync logs,\n    // but it is not included in transactionRoot or receiptRoot.\n    // So, while calculating proof, we have to exclude them.\n    //\n    // This is derived from block's hash and number\n    // state-sync tx hash = keccak256(\"matic-bor-receipt-\" + block.number + block.hash)\n    static getStateSyncTxHash(block): Buffer {\n        return Keccak.keccak256(\n            Buffer.concat([\n                // prefix for bor receipt\n                Buffer.from('matic-bor-receipt-', 'utf-8'),\n                setLengthLeft(BufferUtil.toBuffer(block.number), 8), // 8 bytes of block number (BigEndian)\n                BufferUtil.toBuffer(block.hash), // block hash\n            ])\n        );\n    }\n\n    static getReceiptBytes(receipt: ITransactionReceipt) {\n        let encodedData = rlp.encode([\n            BufferUtil.toBuffer(\n                receipt.status !== undefined && receipt.status != null ? (receipt.status ? '0x1' : '0x') : receipt.root\n            ),\n            BufferUtil.toBuffer(receipt.cumulativeGasUsed),\n            BufferUtil.toBuffer(receipt.logsBloom),\n            // encoded log array\n            receipt.logs.map(l => {\n                // [address, [topics array], data]\n                return [\n                    BufferUtil.toBuffer(l.address), // convert address to buffer\n                    l.topics.map(BufferUtil.toBuffer), // convert topics to buffer\n                    BufferUtil.toBuffer(l.data), // convert data to buffer\n                ];\n            }),\n        ]);\n        if (ProofUtil.isTypedReceipt(receipt)) {\n            encodedData = Buffer.concat([BufferUtil.toBuffer(receipt.type), encodedData]);\n        }\n        return encodedData;\n    }\n\n    static getRawHeader(_block) {\n        _block.difficulty = Converter.toHex(_block.difficulty) as any;\n        const common = new Common({\n            chain: Chain.Mainnet, hardfork: Hardfork.London\n        });\n        const rawHeader = BlockHeader.fromHeaderData(_block, {\n            common: common,\n            skipConsensusFormatValidation: true\n        });\n        return rawHeader;\n    }\n}\n", "const fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response> =\n    (() => {\n        if (process.env.BUILD_ENV === \"node\") {\n            return require('node-fetch').default;\n        }\n        return window.fetch;\n    })();\n\n\nexport class HttpRequest {\n    baseUrl = \"\";\n\n    constructor(option: { baseUrl: string } | string = {} as any) {\n        option = typeof option === \"string\" ? {\n            baseUrl: option\n        } : option;\n\n        if (option.baseUrl) {\n            this.baseUrl = option.baseUrl;\n        }\n    }\n\n    get<T>(url = \"\", query = {}): Promise<T> {\n        url = this.baseUrl + url + Object.keys(query).\n            map(key => `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`).join('&');\n\n        return fetch(url, {\n            method: 'GET',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            }\n        }).then(res => {\n            return res.json();\n        });\n    }\n\n    post(url = \"\", body) {\n        url = this.baseUrl + url;\n\n        return fetch(url, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: body ? JSON.stringify(body) : null\n        }).then(res => {\n            return res.json();\n        });\n    }\n}", "import { IBaseClientConfig } from \"../interfaces\";\nimport { BaseWeb3Client } from \"../abstracts\";\nimport { ABIManager } from \"../utils\";\nimport { Logger } from \"./logger\";\nimport { utils } from \"..\";\n\nconst chainIdToConfigPath = {\n    1: 'Main',\n    5: 'Main',\n    11155111: 'Main',\n    137: 'Matic',\n    80001: 'Matic',\n    80002: 'Matic',\n    1442: 'zkEVM',\n    2442: 'zkEVM',\n    1101: 'zkEVM'\n};\n\nexport class Web3SideChainClient<T_CONFIG> {\n    parent: BaseWeb3Client;\n    child: BaseWeb3Client;\n\n    config: T_CONFIG;\n\n    abiManager: ABIManager;\n\n    logger = new Logger();\n    resolution: {};\n\n    init(config: IBaseClientConfig) {\n        config = config || {} as any;\n        config.parent.defaultConfig = config.parent.defaultConfig || {} as any;\n        config.child.defaultConfig = config.child.defaultConfig || {} as any;\n        this.config = config as any;\n\n        // tslint:disable-next-line\n        const Web3Client = utils.Web3Client;\n\n        if (!Web3Client) {\n            throw new Error(\"Web3Client is not set\");\n        }\n\n        if (utils.UnstoppableDomains) {\n            this.resolution = utils.UnstoppableDomains;\n        }\n\n        this.parent = new (Web3Client as any)(config.parent.provider, this.logger);\n        this.child = new (Web3Client as any)(config.child.provider, this.logger);\n\n        this.logger.enableLog(config.log);\n\n        const network = config.network;\n        const version = config.version;\n        const abiManager = this.abiManager =\n            new ABIManager(network, version);\n        this.logger.log(\"init called\", abiManager);\n        return abiManager.init().catch(err => {\n            throw new Error(`network ${network} - ${version} is not supported`);\n        });\n    }\n\n    getABI(name: string, type?: string) {\n        return this.abiManager.getABI(name, type);\n    }\n\n    getConfig(path: string) {\n        return this.abiManager.getConfig(path);\n    }\n\n    get mainPlasmaContracts() {\n        return this.getConfig(\"Main.Contracts\");\n    }\n\n    get mainPOSContracts() {\n        return this.getConfig(\"Main.POSContracts\");\n    }\n\n    get mainZkEvmContracts() {\n        return this.getConfig(\"Main.Contracts\");\n    }\n\n    get zkEvmContracts() {\n        return this.getConfig(\"zkEVM.Contracts\");\n    }\n\n    isEIP1559Supported(chainId: number): boolean {\n        return this.getConfig(`${chainIdToConfigPath[chainId]}.SupportsEIP1559`);\n    }\n\n}\n", "export const promiseResolve = <T>(value?) => {\n    return Promise.resolve<T>(value);\n};\n\nexport const promiseAny = (promisesArray) => {\n    const promiseErrors = new Array(promisesArray.length);\n    let counter = 0;\n\n    //return a new promise\n    return new Promise((resolve, reject) => {\n        promisesArray.forEach((promise) => {\n            Promise.resolve(promise)\n                .then(resolve) // resolve, when any of the input promise resolves\n                .catch((error) => {\n                    promiseErrors[counter] = error;\n                    counter = counter + 1;\n                    if (counter === promisesArray.length) {\n                        // all promises rejected, reject outer promise\n                        reject(promiseErrors);\n                    }\n                }); // reject, when any of the input promise rejects\n        });\n    });\n};\n", "import { Web3SideChainClient } from \"./web3_side_chain_client\";\nimport { ITransactionRequestConfig, ITransactionOption, IContractInitParam, IBaseClientConfig, ITransactionWriteResult } from \"../interfaces\";\nimport { BaseContractMethod, BaseContract } from \"../abstracts\";\nimport { Converter, merge, utils } from \"../utils\";\nimport { promiseResolve } from \"./promise_resolve\";\nimport { ERROR_TYPE } from \"../enums\";\nimport { POSERC1155TransferParam, TYPE_AMOUNT } from \"../types\";\nimport { <PERSON>rror<PERSON>elper } from \"./error_helper\";\nimport { ADDRESS_ZERO } from '../constant';\n\nexport interface ITransactionConfigParam {\n    txConfig: ITransactionRequestConfig;\n    method?: BaseContractMethod;\n    isWrite?: boolean;\n    isParent?: boolean;\n}\n\nexport class BaseToken<T_CLIENT_CONFIG> {\n\n    private contract_: BaseContract;\n    private chainId_: number;\n\n    constructor(\n        protected contractParam: IContractInitParam,\n        protected client: Web3SideChainClient<T_CLIENT_CONFIG>,\n    ) {\n    }\n\n    get contractAddress() {\n        return this.contractParam.address;\n    }\n\n    getContract(): Promise<BaseContract> {\n        if (this.contract_) {\n            return promiseResolve<BaseContract>(this.contract_ as any);\n        }\n        const contractParam = this.contractParam;\n        return this.client.getABI(\n            contractParam.name,\n            contractParam.bridgeType,\n        ).then(abi => {\n            this.contract_ = this.getContract_({\n                abi,\n                isParent: contractParam.isParent,\n                tokenAddress: contractParam.address\n            });\n            return this.contract_;\n        });\n    }\n\n    getChainId(): Promise<number> {\n        if (this.chainId_) {\n            return promiseResolve<number>(this.chainId_ as any);\n        }\n        const client = this.getClient(this.contractParam.isParent);\n        return client.getChainId().then(chainId => {\n            this.chainId_ = chainId;\n            return this.chainId_;\n        });\n    }\n\n    protected processWrite(method: BaseContractMethod, option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n\n        this.client.logger.log(\"process write\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                this.client.logger.log(\"process write config\");\n                if (option.returnTransaction) {\n                    return merge(config, {\n                        data: method.encodeABI(),\n                        to: method.address\n                    } as ITransactionRequestConfig);\n                }\n                const methodResult = method.write(\n                    config,\n                );\n                return methodResult;\n            });\n    }\n\n    protected sendTransaction(option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n\n        const isParent = this.contractParam.isParent;\n        const client = this.getClient(isParent);\n        client.logger.log(\"process write\");\n\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method: null as any,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                client.logger.log(\"process write config\");\n                if (option.returnTransaction) {\n                    return config as any;\n                }\n                const methodResult = client.write(\n                    config,\n                );\n                return methodResult;\n            });\n    }\n\n    protected readTransaction(option: ITransactionOption = {}): Promise<ITransactionWriteResult> {\n        this.validateTxOption_(option);\n        const isParent = this.contractParam.isParent;\n        const client = this.getClient(isParent);\n        client.logger.log(\"process read\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: true,\n                method: null as any,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                client.logger.log(\"write tx config created\");\n                if (option.returnTransaction) {\n                    return config as any;\n                }\n                return client.read(\n                    config,\n                );\n            });\n    }\n\n    private validateTxOption_(option: ITransactionOption) {\n        if (typeof option !== 'object' || Array.isArray(option)) {\n            new ErrorHelper(ERROR_TYPE.TransactionOptionNotObject).throw();\n        }\n    }\n\n    protected processRead<T>(method: BaseContractMethod, option: ITransactionOption = {}): Promise<T> {\n        this.validateTxOption_(option);\n        this.client.logger.log(\"process read\");\n        return this.createTransactionConfig(\n            {\n                txConfig: option,\n                isWrite: false,\n                method,\n                isParent: this.contractParam.isParent\n            }).then(config => {\n                this.client.logger.log(\"read tx config created\");\n                if (option.returnTransaction) {\n                    return merge(config, {\n                        data: method.encodeABI(),\n                        to: this.contract_.address\n                    } as ITransactionRequestConfig);\n                }\n                return method.read(\n                    config,\n                );\n            });\n    }\n\n    protected getClient(isParent) {\n        return isParent ? this.client.parent :\n            this.client.child;\n    }\n\n    private getContract_({ isParent, tokenAddress, abi }) {\n        const client = this.getClient(isParent);\n        return client.getContract(tokenAddress, abi);\n    }\n\n    protected get parentDefaultConfig() {\n        const config: IBaseClientConfig = this.client.config as any;\n        return config.parent.defaultConfig;\n    }\n\n    protected get childDefaultConfig() {\n        const config: IBaseClientConfig = this.client.config as any;\n        return config.child.defaultConfig;\n    }\n\n    protected createTransactionConfig({ txConfig, method, isParent, isWrite }: ITransactionConfigParam) {\n        const defaultConfig = isParent ? this.parentDefaultConfig : this.childDefaultConfig;\n        txConfig = merge(defaultConfig, (txConfig || {}));\n        const client = isParent ? this.client.parent :\n            this.client.child;\n        client.logger.log(\"txConfig\", txConfig, \"onRoot\", isParent, \"isWrite\", isWrite);\n        const estimateGas = async (config: ITransactionRequestConfig) => {\n            const result = method ? await method.estimateGas(config) : await client.estimateGas(config);\n            return new utils.BN(Math.trunc(Number(result) * 1.15)).toString();\n        };\n        // txConfig.chainId = Converter.toHex(txConfig.chainId) as any;\n        if (isWrite) {\n            return this.getChainId().then(clientChainId => {\n                const { maxFeePerGas, maxPriorityFeePerGas } = txConfig;\n\n                const isEIP1559Supported = this.client.isEIP1559Supported(clientChainId);\n                const isMaxFeeProvided = (maxFeePerGas || maxPriorityFeePerGas);\n                txConfig.chainId = txConfig.chainId || clientChainId;\n\n                if (!isEIP1559Supported && isMaxFeeProvided) {\n                    client.logger.error(ERROR_TYPE.EIP1559NotSupported, isParent).throw();\n                }\n                // const [gasLimit, nonce] = \n                return Promise.all([\n                    !(txConfig.gasLimit)\n                        ? estimateGas({\n                            from: txConfig.from, value: txConfig.value, to: txConfig.to\n                        })\n                        : txConfig.gasLimit,\n                    !txConfig.nonce ?\n                        client.getTransactionCount(txConfig.from, 'pending')\n                        : txConfig.nonce\n                ]).then(result => {\n                    const [gasLimit, nonce] = result;\n                    client.logger.log(\"options filled\");\n\n                    txConfig.gasLimit = Number(gasLimit);\n                    txConfig.nonce = nonce;\n                    return txConfig;\n                });\n            });\n        }\n        return promiseResolve<ITransactionRequestConfig>(txConfig);\n    }\n\n    protected transferERC20(to: string, amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"transfer\",\n                to,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n    protected transferERC721(from: string, to: string, tokenId: string, option: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"transferFrom\",\n                from,\n                to,\n                tokenId\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n    protected checkForNonNative(methodName) {\n        if (this.contractParam.address === ADDRESS_ZERO) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnNonNativeTokens, methodName).throw();\n        }\n    }\n\n    protected checkForRoot(methodName) {\n        if (!this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnRoot, methodName).throw();\n        }\n    }\n\n    protected checkForChild(methodName) {\n        if (this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.AllowedOnChild, methodName).throw();\n        }\n    }\n\n    protected checkAdapterPresent(methodName) {\n        if (!this.contractParam.bridgeAdapterAddress) {\n            this.client.logger.error(ERROR_TYPE.BridgeAdapterNotFound, methodName).throw();\n        }\n    }\n\n    protected transferERC1155(param: POSERC1155TransferParam, option: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"safeTransferFrom\",\n                param.from,\n                param.to,\n                Converter.toHex(param.tokenId),\n                Converter.toHex(param.amount),\n                param.data || '0x'\n            );\n            return this.processWrite(\n                method, option\n            );\n        });\n    }\n\n}\n", "import { HttpRequest } from \"../utils\";\n\nexport class ABIService {\n    httpRequest: HttpRequest;\n\n    constructor(baseUrl: string) {\n        this.httpRequest = new HttpRequest(baseUrl);\n    }\n\n    getABI(network: string, version: string, bridgeType: string, contractName: string) {\n        const url = `${network}/${version}/artifacts/${bridgeType}/${contractName}.json`;\n        return this.httpRequest.get(url).then((result: any) => {\n            return result.abi;\n        });\n    }\n\n    getAddress(network: string, version: string) {\n        const url = `${network}/${version}/index.json`;\n        return this.httpRequest.get(url);\n    }\n}\n", "export const config = {\n  abiStoreUrl: 'https://static.polygon.technology/network/',\n  zkEvmBridgeService: 'https://proof-generator.polygon.technology/',\n}\n", "import { BaseBigNumber, utils } from \"..\";\nimport { HttpRequest } from \"../utils\";\n\nexport class NetworkService {\n    httpRequest: HttpRequest;\n\n    constructor(baseUrl: string) {\n        this.httpRequest = new HttpRequest(baseUrl);\n    }\n\n    private createUrlForPos(version: string, url: string) {\n        return `${version === 'v1' ? 'matic' : version}${url}`;\n    }\n\n    private createUrlForZkEvm(version: string, url: string) {\n        return `${version}/${url}`;\n    }\n\n    getBlockIncluded(version: string, blockNumber: number) {\n        const url = this.createUrlForPos(version, `/block-included/${blockNumber}`);\n        return this.httpRequest.get<{\n            start: string;\n            end: string;\n            headerBlockNumber: BaseBigNumber;\n        }>(url).then(result => {\n            const headerBlockNumber = result.headerBlockNumber as any as string;\n            const decimalHeaderBlockNumber = headerBlockNumber.slice(0, 2) === '0x' ? parseInt(\n                headerBlockNumber, 16\n            ) : headerBlockNumber;\n            result.headerBlockNumber = new utils.BN(decimalHeaderBlockNumber);\n            return result;\n        });\n    }\n\n    getExitProof(version: string, burnTxHash: string, eventSignature: string) {\n        const url = this.createUrlForPos(version, `/exit-payload/${burnTxHash}?eventSignature=${eventSignature}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.result;\n        });\n    }\n\n    getProof(version: string, start, end, blockNumber) {\n        const url = this.createUrlForPos(version, `/fast-merkle-proof?start=${start}&end=${end}&number=${blockNumber}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.proof;\n        });\n    }\n\n    getMerkleProofForZkEvm(version: string, networkID: number, depositCount: number) {\n        const url = this.createUrlForZkEvm(version, `merkle-proof?net_id=${networkID}&deposit_cnt=${depositCount}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.proof;\n        });\n    }\n\n    getBridgeTransactionDetails(version: string, networkID: number, depositCount: number) {\n        const url = this.createUrlForZkEvm(version, `bridge?net_id=${networkID}&deposit_cnt=${depositCount}`);\n        return this.httpRequest.get<any>(url).then(result => {\n            return result.deposit;\n        });\n    }\n}\n", "import { ABIService } from \"./abi_service\";\nimport { config } from \"../config\";\nimport { NetworkService } from \"./network_service\";\n\nexport * from \"./network_service\";\n\nclass Service {\n    network: NetworkService;\n    zkEvmNetwork: NetworkService;\n    abi: ABIService;\n}\n\nexport const service = new Service();\nservice.abi = new ABIService(config.abiStoreUrl);\n\n\n\n", "import { service, NetworkService } from \"../services\";\n\nexport const setProofApi = (url: string) => {\n    const urlLength = url.length;\n    if (url[urlLength - 1] !== '/') {\n        url += '/';\n    }\n    url += 'api/v1/';\n    service.network = new NetworkService(url);\n};\n\nexport const setZkEvmProofApi = (url: string) => {\n    const urlLength = url.length;\n    if (url[urlLength - 1] !== '/') {\n        url += '/';\n    }\n    url += 'api/zkevm/';\n    service.zkEvmNetwork = new NetworkService(url);\n};\n", "export function resolve(obj, path) {\n    const properties = Array.isArray(path) ? path : path.split(\".\");\n    return properties.reduce((prev, curr) => prev && prev[curr], obj);\n}", "import { Web3SideChainClient } from \"../utils\";\nimport { ExitUtil } from \"../pos\";\nimport { BaseToken, utils } from \"..\";\n\nexport class BridgeClient<T> {\n\n    client: Web3SideChainClient<T> = new Web3SideChainClient();\n\n    exitUtil: ExitUtil;\n\n    /**\n     * check whether a txHash is checkPointed \n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof BridgeClient\n     */\n    isCheckPointed(txHash: string) {\n        return this.exitUtil.isCheckPointed(\n            txHash\n        );\n    }\n\n    isDeposited(depositTxHash: string) {\n        const client = this.client;\n\n        const token = new BaseToken({\n            address: client.abiManager.getConfig(\"Matic.GenesisContracts.StateReceiver\"),\n            isParent: false,\n            name: 'StateReceiver',\n            bridgeType: 'genesis'\n        }, client);\n\n        return token.getContract().then(contract => {\n            return Promise.all([\n                client.parent.getTransactionReceipt(depositTxHash),\n                token['processRead']<string>(\n                    contract.method(\"lastStateId\")\n                )\n            ]);\n        }).then(result => {\n            const [receipt, lastStateId] = result;\n            const eventSignature = `0x103fed9db65eac19c4d870f49ab7520fe03b99f1838e5996caf47e9e43308392`;\n            const targetLog = receipt.logs.find(q => q.topics[0] === eventSignature);\n            if (!targetLog) {\n                throw new Error(\"StateSynced event not found\");\n            }\n            const rootStateId = client.child.decodeParameters(targetLog.topics[1], ['uint256'])[0];\n            const rootStateIdBN = utils.BN.isBN(rootStateId) ? rootStateId : new utils.BN(rootStateId);\n            return new utils.BN(lastStateId).gte(\n                rootStateIdBN\n            );\n        });\n    }\n\n}", "import { service } from \"../services\";\nimport { resolve, promiseResolve } from \".\";\n\ntype T_ABI_CACHE = {\n    [networkName: string]: {\n        [version: string]: {\n            address: any,\n            abi: {\n                [bridgeType: string]: {\n                    [contractName: string]: any\n                }\n            }\n        }\n    }\n};\n\nconst cache: T_ABI_CACHE = {};\n\nexport class ABIManager {\n    constructor(public networkName: string, public version: string) {\n\n    }\n\n    init() {\n        return service.abi.getAddress(\n            this.networkName, this.version\n        ).then(result => {\n            cache[this.networkName] = {\n                [this.version]: {\n                    address: result,\n                    abi: {}\n                }\n            };\n        });\n    }\n\n    getConfig(path: string) {\n        return resolve(\n            cache[this.networkName][this.version].address,\n            path\n        );\n    }\n\n    getABI(contractName: string, bridgeType = 'plasma'): Promise<any> {\n        let targetBridgeABICache;\n\n        if (\n            cache[this.networkName] && cache[this.networkName][this.version] &&\n            cache[this.networkName][this.version].abi\n        ) {\n            targetBridgeABICache = cache[this.networkName][this.version].abi[bridgeType];\n        }\n\n\n        if (targetBridgeABICache) {\n            const abiForContract = targetBridgeABICache[contractName];\n            if (abiForContract) {\n                return promiseResolve<any>(abiForContract);\n            }\n        }\n        return service.abi.getABI(\n            this.networkName,\n            this.version,\n            bridgeType,\n            contractName\n        ).then(result => {\n            this.setABI(contractName, bridgeType, result);\n            return result;\n        });\n    }\n\n    setABI(contractName: string, bridgeType: string, abi: any) {\n        const abiStore = cache[this.networkName][this.version].abi;\n        if (!abiStore[bridgeType]) {\n            abiStore[bridgeType] = {};\n        }\n        abiStore[bridgeType][contractName] = abi;\n    }\n}", "export const throwNotImplemented = <T>() => {\n    throw new Error(\"not implemented\");\n    return '' as any as T;\n};", "import { Web3SideChainClient } from \".\";\nimport { BridgeUtil, ZkEvmBridge } from \"../zkevm\";\nimport { service } from \"../services\";\nimport { IBaseClientConfig } from \"..\";\n\nexport class ZkEvmBridgeClient {\n\n    client: Web3SideChainClient<IBaseClientConfig> = new Web3SideChainClient();\n    bridgeUtil: BridgeUtil;\n    rootChainBridge: ZkEvmBridge;\n    childChainBridge: ZkEvmBridge;\n\n    /**\n     * check whether a txHash is synced with child chain\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isDepositClaimable(txHash: string) {\n        return Promise.all([this.rootChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(\n            txHash, true\n        )]).then(result => {\n            return service.zkEvmNetwork.getBridgeTransactionDetails(\n                this.client.config.version,\n                result[0],\n                result[1].depositCount\n            );\n        }).then(details => {\n            return details.ready_for_claim;\n        });\n    }\n\n    /**\n     * check whether proof is submitted on parent chain\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isWithdrawExitable(txHash: string) {\n        return Promise.all([this.childChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(\n            txHash, false\n        )]).then(result => {\n            return service.zkEvmNetwork.getBridgeTransactionDetails(\n                this.client.config.version,\n                result[0],\n                result[1].depositCount\n            );\n        }).then(details => {\n            return details.ready_for_claim;\n        });\n    }\n\n    /**\n     * check whether deposit is completed\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isDeposited(txHash: string) {\n        return this.bridgeUtil.getBridgeLogData(\n            txHash, true\n        ).then(result => {\n            return this.childChainBridge.isClaimed(result.depositCount, 0);\n        });\n    }\n\n    /**\n     * check whether deposit is completed\n     *\n     * @param {string} txHash\n     * @returns\n     * @memberof ZkEvmBridgeClient\n     */\n    isExited(txHash: string) {\n        return this.bridgeUtil.getBridgeLogData(\n            txHash, false\n        ).then(result => {\n            return this.rootChainBridge.isClaimed(result.depositCount, 1);\n        });\n    }\n\n}\n", "import { BaseWeb3Client } from \"../abstracts\";\nimport { EmptyBigNumber } from \"../implementation\";\nimport { Converter } from \"./converter\";\n\nexport * from \"./use\";\nexport * from \"./event_bus\";\nexport * from \"./logger\";\nexport * from \"./merge\";\nexport * from \"./map_promise\";\nexport * from \"./proof_util\";\nexport * from \"./http_request\";\nexport * from \"./converter\";\nexport * from \"./web3_side_chain_client\";\nexport * from \"./base_token\";\nexport * from \"./set_proof_api_url\";\nexport * from \"./resolve\";\nexport * from \"./promise_resolve\";\nexport * from \"./bridge_client\";\nexport * from \"./abi_manager\";\nexport * from \"./not_implemented\";\nexport * from \"./zkevm_bridge_client\";\nexport * from \"./buffer-utils\";\nexport * from \"./keccak\";\nexport * from \"./types\";\n\nexport const utils = {\n    converter: Converter,\n    Web3Client: BaseWeb3Client,\n    BN: EmptyBigNumber,\n    UnstoppableDomains: Object\n};\n", "import { BaseToken, Web3SideChainClient, promiseResolve } from \"../utils\";\nimport { IContractInitParam, IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { IPOSContracts } from \"../interfaces\";\n\nexport class POSToken extends BaseToken<IPOSClientConfig> {\n\n    private predicateAddress: string;\n\n    constructor(\n        contractParam: IContractInitParam,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        protected getPOSContracts: () => IPOSContracts\n    ) {\n        super(contractParam, client);\n    }\n\n    protected get rootChainManager() {\n        return this.getPOSContracts().rootChainManager;\n    }\n\n    protected get gasSwapper() {\n        return this.getPOSContracts().gasSwapper;\n    }\n\n    protected get exitUtil() {\n        return this.getPOSContracts().exitUtil;\n    }\n\n\n    getPredicateAddress(): Promise<string> {\n        if (this.predicateAddress) {\n            return promiseResolve(this.predicateAddress);\n        }\n        return this.rootChainManager.method(\n            \"tokenToType\",\n            this.contractParam.address\n        ).then(method => {\n            return method.read();\n        }).then(tokenType => {\n            if (!tokenType) {\n                throw new Error('Invalid Token Type');\n            }\n            return this.rootChainManager.method(\n                \"typeToPredicate\", tokenType\n            );\n        }).then(typeToPredicateMethod => {\n            return typeToPredicateMethod.read<string>();\n        }).then(predicateAddress => {\n            this.predicateAddress = predicateAddress;\n            return predicateAddress;\n        });\n    }\n\n    protected isWithdrawn(txHash: string, eventSignature: string) {\n        if (!txHash) {\n            throw new Error(`txHash not provided`);\n        }\n        return this.exitUtil.getExitHash(\n            txHash, 0, eventSignature\n        ).then(exitHash => {\n            return this.rootChainManager.isExitProcessed(\n                exitHash\n            );\n        });\n    }\n\n    protected isWithdrawnOnIndex(txHash: string, index: number, eventSignature: string) {\n      if (!txHash) {\n          throw new Error(`txHash not provided`);\n      }\n      return this.exitUtil.getExitHash(\n          txHash, index, eventSignature\n      ).then(exitHash => {\n          return this.rootChainManager.isExitProcessed(\n              exitHash\n          );\n      });\n  }\n\n    protected withdrawExitPOS(burnTxHash: string, eventSignature: string, isFast: boolean, option: ITransactionOption) {\n        return this.exitUtil.buildPayloadForExit(\n            burnTxHash,\n            eventSignature,\n            isFast\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n}\n", "import { ITransactionOption } from \"../interfaces\";\nimport { Converter, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ERROR_TYPE, Log_Event_Signature } from \"../enums\";\nimport { MAX_AMOUNT, promiseResolve } from \"..\";\nimport { IAllowanceTransactionOption, IApproveTransactionOption, IExitTransactionOption, IPOSClientConfig, IPOSContracts } from \"../interfaces\";\n\nexport class ERC20 extends POSToken {\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: 'ChildERC20',\n            bridgeType: 'pos'\n        }, client, getContracts);\n    }\n\n    getBalance(userAddress: string, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * get allowance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getAllowance(userAddress: string, option: IAllowanceTransactionOption = {}) {\n        const spenderAddress = option.spenderAddress;\n\n        const predicatePromise = spenderAddress ? promiseResolve(spenderAddress) : this.getPredicateAddress();\n\n        return Promise.all([predicatePromise, this.getContract()]).then(result => {\n            const [predicateAddress, contract] = result;\n            const method = contract.method(\n                \"allowance\",\n                userAddress,\n                predicateAddress,\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    approve(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        const spenderAddress = option.spenderAddress;\n\n        if (!spenderAddress && !this.contractParam.isParent) {\n            this.client.logger.error(ERROR_TYPE.NullSpenderAddress).throw();\n        }\n\n        const predicatePromise = spenderAddress ? promiseResolve(spenderAddress) : this.getPredicateAddress();\n\n        return Promise.all([predicatePromise, this.getContract()]).then(result => {\n            const [predicateAddress, contract] = result;\n            const method = contract.method(\n                \"approve\",\n                predicateAddress,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    approveMax(option: IApproveTransactionOption = {}) {\n        return this.approve(\n            MAX_AMOUNT\n            , option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    deposit(amount: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETHER for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        return this.getChainId().then((chainId: number) => {\n            if (chainId !== 1) {\n                this.client.logger.error(ERROR_TYPE.AllowedOnMainnet).throw();\n            }\n            const amountInABI = this.client.parent.encodeParameters(\n                [Converter.toHex(amount)],\n                ['uint256'],\n            );\n\n            option.value = Converter.toHex(swapEthAmount);\n\n            return this.gasSwapper.depositWithGas(\n                this.contractParam.address,\n                amountInABI,\n                userAddress,\n                swapCallData,\n                option\n            );\n        });\n\n    }\n\n    private depositEther_(amount: TYPE_AMOUNT, userAddress: string, option: ITransactionOption = {}) {\n        this.checkForRoot(\"depositEther\");\n\n\n        option.value = Converter.toHex(amount);\n        return this.rootChainManager.method(\"depositEtherFor\", userAddress).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    private depositEtherWithGas_(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option: ITransactionOption = {}) {\n        this.checkForRoot(\"depositEtherWithGas\");\n\n        return this.getChainId().then((chainId: number) => {\n            if (chainId !== 1) {\n                this.client.logger.error(ERROR_TYPE.AllowedOnMainnet).throw();\n            }\n            const amountInABI = this.client.parent.encodeParameters(\n                [Converter.toHex(amount)],\n                ['uint256'],\n            );\n\n            option.value = Converter.toHex(\n                Converter.toBN(amount).add(\n                    Converter.toBN(swapEthAmount)\n                )\n            );\n\n            return this.gasSwapper.depositWithGas(\n                \"******************************************\",\n                amountInABI,\n                userAddress,\n                swapCallData,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by burning provided amount\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawStart(amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdraw\",\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    private withdrawExit_(burnTransactionHash: string, isFast: boolean, option: IExitTransactionOption = {}) {\n        const eventSignature = option.burnEventSignature ?\n            option.burnEventSignature : Log_Event_Signature.Erc20Transfer;\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            eventSignature,\n            isFast\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    /**\n     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExit(burnTransactionHash: string, option?: IExitTransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.withdrawExit_(burnTransactionHash, false, option);\n    }\n\n    /**\n     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.\n     *\n     *  Note:- It create the proof in api call for fast exit.\n     * \n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExitFaster(burnTransactionHash: string, option?: IExitTransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.withdrawExit_(burnTransactionHash, true, option);\n    }\n\n    /**\n     * check if exit has been completed for a transaction hash\n     *\n     * @param {string} burnTxHash\n     * @returns\n     * @memberof ERC20\n     */\n    isWithdrawExited(burnTxHash: string) {\n        return this.isWithdrawn(burnTxHash, Log_Event_Signature.Erc20Transfer);\n    }\n\n    /**\n     * transfer amount to another user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    transfer(amount: TYPE_AMOUNT, to: string, option?: ITransactionOption) {\n        return this.transferERC20(to, amount, option);\n    }\n\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class RootChainManager extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'RootChainManager',\n            bridgeType: 'pos',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    deposit(userAddress: string, tokenAddress: string, depositData: string, option?: ITransactionOption) {\n        return this.method(\n            \"depositFor\",\n            userAddress,\n            tokenAddress,\n            depositData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    exit(exitPayload: string, option: ITransactionOption) {\n        return this.method(\"exit\", exitPayload).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    isExitProcessed(exitHash: string) {\n        return this.method(\n            \"processedExits\", exitHash\n        ).then(method => {\n            return this.processRead<boolean>(method);\n        });\n    }\n\n}\n", "import { <PERSON><PERSON><PERSON><PERSON> } from \"./root_chain\";\nimport { Converter, ProofUtil, Web3SideChainClient } from \"../utils\";\nimport { BufferUtil } from \"../utils/buffer-utils\";\nimport rlp from \"rlp\";\nimport { IBlockWithTransaction, ITransactionReceipt } from \"../interfaces\";\nimport { service } from \"../services\";\nimport { BaseBigNumber, BaseWeb3Client } from \"../abstracts\";\nimport { ErrorHelper } from \"../utils/error_helper\";\nimport { ERROR_TYPE, IBaseClientConfig, IRootBlockInfo, utils } from \"..\";\n\ninterface IChainBlockInfo {\n    lastChildBlock: string;\n    txBlockNumber: number;\n}\n\n\n\nexport class ExitUtil {\n    private maticClient_: BaseWeb3Client;\n\n    rootChain: RootChain;\n\n    requestConcurrency: number;\n    config: IBaseClientConfig;\n\n    constructor(client: Web3SideChainClient<IBaseClientConfig>, rootChain: <PERSON><PERSON>hain) {\n        this.maticClient_ = client.child;\n        this.rootChain = rootChain;\n        const config = client.config;\n        this.config = config;\n        this.requestConcurrency = config.requestConcurrency;\n    }\n\n    private getLogIndex_(logEventSig: string, receipt: ITransactionReceipt) {\n        let logIndex = -1;\n\n        switch (logEventSig) {\n            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':\n            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':\n                logIndex = receipt.logs.findIndex(\n                    log =>\n                        log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000'\n                );\n                break;\n\n            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':\n            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':\n                logIndex = receipt.logs.findIndex(\n                    log =>\n                        log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[3].toLowerCase() === '******************************************000000000000000000000000'\n                );\n                break;\n\n            default:\n                logIndex = receipt.logs.findIndex(log => log.topics[0].toLowerCase() === logEventSig.toLowerCase());\n        }\n        if (logIndex < 0) {\n            throw new Error(\"Log not found in receipt\");\n        }\n        return logIndex;\n    }\n\n    private getAllLogIndices_(logEventSig: string, receipt: ITransactionReceipt) {\n        let logIndices = [];\n\n        switch (logEventSig) {\n            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':\n            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':\n            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&\n                        log.topics[3].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            case '0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df':\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef' &&\n                        log.topics[2].toLowerCase() === '******************************************000000000000000000000000') &&\n                        logIndices.push(index), logIndices), []\n                );\n                break;\n\n            default:\n                logIndices = receipt.logs.reduce(\n                    (_, log, index) =>\n                    ((log.topics[0].toLowerCase() === logEventSig.toLowerCase()) &&\n                        logIndices.push(index), logIndices), []\n                );\n        }\n        if (logIndices.length === 0) {\n            throw new Error(\"Log not found in receipt\");\n        }\n        return logIndices;\n    }\n\n    getChainBlockInfo(burnTxHash: string) {\n        return Promise.all([\n            this.rootChain.getLastChildBlock(),\n            this.maticClient_.getTransaction(burnTxHash),\n        ]).then(result => {\n            return {\n                lastChildBlock: result[0],\n                txBlockNumber: result[1].blockNumber\n            } as IChainBlockInfo;\n        });\n    }\n\n    private isCheckPointed_(data: IChainBlockInfo) {\n        // lastchild block is greater equal to transaction block number; \n        return new utils.BN(data.lastChildBlock).gte(\n            new utils.BN(data.txBlockNumber)\n        );\n    }\n\n    isCheckPointed(burnTxHash: string) {\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(result => {\n            return this.isCheckPointed_(\n                result\n            );\n        });\n    }\n\n    /**\n     * returns info about block number existence on parent chain\n     * 1. root block number, \n     * 2. start block number, \n     * 3. end block number \n     *\n     * @private\n     * @param {number} txBlockNumber - transaction block number on child chain\n     * @return {*} \n     * @memberof ExitUtil\n     */\n    private getRootBlockInfo(txBlockNumber: number) {\n        // find in which block child was included in parent\n        let rootBlockNumber: BaseBigNumber;\n        return this.rootChain.findRootBlockFromChild(\n            txBlockNumber\n        ).then(blockNumber => {\n            rootBlockNumber = blockNumber;\n            return this.rootChain.method(\n                \"headerBlocks\",\n                Converter.toHex(blockNumber)\n            );\n        }).then(method => {\n            return method.read<IRootBlockInfo>();\n        }).then(rootBlockInfo => {\n            return {\n                // header block number - root block number in which child block exist \n                headerBlockNumber: rootBlockNumber,\n                // range of block\n                // end - block end number\n                end: rootBlockInfo.end.toString(),\n                // start - block start number\n                start: rootBlockInfo.start.toString(),\n            } as IRootBlockInfo;\n        });\n\n    }\n\n    private getRootBlockInfoFromAPI(txBlockNumber: number) {\n        this.maticClient_.logger.log(\"block info from API 1\");\n        return service.network.getBlockIncluded(\n            this.config.version,\n            txBlockNumber\n        ).then(headerBlock => {\n            this.maticClient_.logger.log(\"block info from API 2\", headerBlock);\n            if (!headerBlock || !headerBlock.start || !headerBlock.end || !headerBlock.headerBlockNumber) {\n                throw Error('Network API Error');\n            }\n            return headerBlock;\n        }).catch(err => {\n            this.maticClient_.logger.log(\"block info from API\", err);\n            return this.getRootBlockInfo(txBlockNumber);\n        });\n    }\n\n    private getBlockProof(txBlockNumber: number, rootBlockInfo: { start, end }) {\n        return ProofUtil.buildBlockProof(\n            this.maticClient_,\n            parseInt(rootBlockInfo.start, 10),\n            parseInt(rootBlockInfo.end, 10),\n            parseInt(txBlockNumber + '', 10)\n        );\n    }\n\n    private getBlockProofFromAPI(txBlockNumber: number, rootBlockInfo: { start, end }) {\n\n        return service.network.getProof(\n            this.config.version,\n            rootBlockInfo.start,\n            rootBlockInfo.end,\n            txBlockNumber\n        ).then(blockProof => {\n            if (!blockProof) {\n                throw Error('Network API Error');\n            }\n            this.maticClient_.logger.log(\"block proof from API 1\");\n            return blockProof;\n        }).catch(_ => {\n            return this.getBlockProof(txBlockNumber, rootBlockInfo);\n        });\n    }\n\n    private getExitProofFromAPI(burnHash: string, eventSignature: string) {\n\n        return service.network.getExitProof(\n            this.config.version, burnHash, eventSignature\n        ).then(exitProof => {\n            if (!exitProof) {\n                throw Error('Network API Error');\n            }\n            this.maticClient_.logger.log(\"exit proof from API 1\");\n            return exitProof;\n        }).catch(_ => {\n            return this.buildPayloadForExit(burnHash, eventSignature, false);\n        });\n    }\n\n    buildPayloadForExit(burnTxHash: string, logEventSig: string, isFast: boolean, index = 0) {\n\n        if (isFast && !service.network) {\n            new ErrorHelper(ERROR_TYPE.ProofAPINotSet).throw();\n        }\n\n        if (index < 0) {\n            throw new Error('Index must not be a negative integer');\n        }\n\n        let txBlockNumber: number,\n            rootBlockInfo: IRootBlockInfo,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction,\n            blockProof;\n\n        if (isFast) {\n            return this.getExitProofFromAPI(burnTxHash, logEventSig);\n        }\n\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(blockInfo => {\n            if (!this.isCheckPointed_(blockInfo)) {\n                throw new Error(\n                    'Burn transaction has not been checkpointed as yet'\n                );\n            }\n\n            // step 1 - Get Block number from transaction hash\n            txBlockNumber = blockInfo.txBlockNumber;\n            // step 2-  get transaction receipt from txhash and \n            // block information from block number\n            return Promise.all([\n                this.maticClient_.getTransactionReceipt(burnTxHash),\n                this.maticClient_.getBlockWithTransaction(txBlockNumber)\n            ]);\n        }).then(result => {\n            [receipt, block] = result;\n            // step  3 - get information about block saved in parent chain \n            return this.getRootBlockInfo(txBlockNumber);\n        }).then(rootBlockInfoResult => {\n            rootBlockInfo = rootBlockInfoResult;\n            // step 4 - build block proof\n            return this.getBlockProof(txBlockNumber, rootBlockInfo);\n        }).then(blockProofResult => {\n            blockProof = blockProofResult;\n            // step 5- create receipt proof\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            // step 6 - encode payload, convert into hex\n\n            // when token index is not 0\n            if (index > 0) {\n                const logIndices = this.getAllLogIndices_(\n                    logEventSig, receipt\n                );\n\n                if (index >= logIndices.length) {\n                    throw new Error('Index is greater than the number of tokens in this transaction');\n                }\n\n                return this.encodePayload_(\n                    rootBlockInfo.headerBlockNumber.toNumber(),\n                    blockProof,\n                    txBlockNumber,\n                    block.timestamp,\n                    Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                    Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                    ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                    receiptProof.parentNodes,\n                    receiptProof.path,\n                    logIndices[index]\n                );\n            }\n\n            // when token index is 0\n            const logIndex = this.getLogIndex_(\n                logEventSig, receipt\n            );\n\n            return this.encodePayload_(\n                rootBlockInfo.headerBlockNumber.toNumber(),\n                blockProof,\n                txBlockNumber,\n                block.timestamp,\n                Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                receiptProof.parentNodes,\n                receiptProof.path,\n                logIndex\n            );\n        });\n    }\n\n    buildMultiplePayloadsForExit(burnTxHash: string, logEventSig: string, isFast: boolean) {\n\n        if (isFast && !service.network) {\n            new ErrorHelper(ERROR_TYPE.ProofAPINotSet).throw();\n        }\n\n        let txBlockNumber: number,\n            rootBlockInfo: IRootBlockInfo,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction,\n            blockProof;\n\n        return this.getChainBlockInfo(\n            burnTxHash\n        ).then(blockInfo => {\n            if (!isFast && !this.isCheckPointed_(blockInfo)) {\n                throw new Error(\n                    'Burn transaction has not been checkpointed as yet'\n                );\n            }\n\n            // step 1 - Get Block number from transaction hash\n            txBlockNumber = blockInfo.txBlockNumber;\n            // step 2-  get transaction receipt from txhash and \n            // block information from block number\n            return Promise.all([\n                this.maticClient_.getTransactionReceipt(burnTxHash),\n                this.maticClient_.getBlockWithTransaction(txBlockNumber)\n            ]);\n        }).then(result => {\n            [receipt, block] = result;\n            // step  3 - get information about block saved in parent chain \n            return (\n                isFast ? this.getRootBlockInfoFromAPI(txBlockNumber) :\n                    this.getRootBlockInfo(txBlockNumber)\n            );\n        }).then(rootBlockInfoResult => {\n            rootBlockInfo = rootBlockInfoResult;\n            // step 4 - build block proof\n            return (\n                isFast ? this.getBlockProofFromAPI(txBlockNumber, rootBlockInfo) :\n                    this.getBlockProof(txBlockNumber, rootBlockInfo)\n            );\n        }).then(blockProofResult => {\n            blockProof = blockProofResult;\n            // step 5- create receipt proof\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            const logIndices = this.getAllLogIndices_(\n                logEventSig, receipt\n            );\n            const payloads: string[] = [];\n\n            // step 6 - encode payloads, convert into hex\n            for (const logIndex of logIndices) {\n                payloads.push(\n                    this.encodePayload_(\n                        rootBlockInfo.headerBlockNumber.toNumber(),\n                        blockProof,\n                        txBlockNumber,\n                        block.timestamp,\n                        Buffer.from(block.transactionsRoot.slice(2), 'hex'),\n                        Buffer.from(block.receiptsRoot.slice(2), 'hex'),\n                        ProofUtil.getReceiptBytes(receipt), // rlp encoded\n                        receiptProof.parentNodes,\n                        receiptProof.path,\n                        logIndex\n                    )\n                );\n            }\n\n            return payloads;\n        });\n    }\n\n    private encodePayload_(\n        headerNumber,\n        buildBlockProof,\n        blockNumber,\n        timestamp,\n        transactionsRoot,\n        receiptsRoot,\n        receipt,\n        receiptParentNodes,\n        path,\n        logIndex\n    ) {\n        return BufferUtil.bufferToHex(\n            rlp.encode([\n                headerNumber,\n                buildBlockProof,\n                blockNumber,\n                timestamp,\n                BufferUtil.bufferToHex(transactionsRoot),\n                BufferUtil.bufferToHex(receiptsRoot),\n                BufferUtil.bufferToHex(receipt),\n                BufferUtil.bufferToHex(rlp.encode(receiptParentNodes) as Buffer),\n                BufferUtil.bufferToHex(Buffer.concat([Buffer.from('00', 'hex'), path])),\n                logIndex,\n            ]) as Buffer\n        );\n    }\n\n    getExitHash(burnTxHash, index, logEventSig) {\n        let lastChildBlock: string,\n            receipt: ITransactionReceipt,\n            block: IBlockWithTransaction;\n\n        return Promise.all([\n            this.rootChain.getLastChildBlock(),\n            this.maticClient_.getTransactionReceipt(burnTxHash)\n        ]).then(result => {\n            lastChildBlock = result[0];\n            receipt = result[1];\n            return this.maticClient_.getBlockWithTransaction(\n                receipt.blockNumber\n            );\n        }).then(blockResult => {\n            block = blockResult;\n            if (!this.isCheckPointed_({ lastChildBlock: lastChildBlock, txBlockNumber: receipt.blockNumber })) {\n                this.maticClient_.logger.error(ERROR_TYPE.BurnTxNotCheckPointed).throw();\n            }\n            return ProofUtil.getReceiptProof(\n                receipt,\n                block,\n                this.maticClient_,\n                this.requestConcurrency\n            );\n        }).then((receiptProof: any) => {\n            let logIndex;\n            const nibbleArr = [];\n            receiptProof.path.forEach(byte => {\n                nibbleArr.push(Buffer.from('0' + (byte / 0x10).toString(16), 'hex'));\n                nibbleArr.push(Buffer.from('0' + (byte % 0x10).toString(16), 'hex'));\n            });\n\n            if (index > 0) {\n                const logIndices = this.getAllLogIndices_(logEventSig, receipt);\n                logIndex = logIndices[index];\n            }\n\n            logIndex = this.getLogIndex_(logEventSig, receipt);\n\n            return this.maticClient_.etheriumSha3(\n                receipt.blockNumber, BufferUtil.bufferToHex(Buffer.concat(nibbleArr)), logIndex\n            );\n        });\n    }\n}\n", "import { BaseToken, utils, Web3SideChainClient } from \"../utils\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { BaseBigNumber } from \"..\";\n\nexport class <PERSON><PERSON>hain extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: '<PERSON><PERSON>hai<PERSON>',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    getLastChildBlock() {\n        return this.method(\"getLastChildBlock\").then(method => {\n            return method.read<string>({}, this.client.config.rootChainDefaultBlock || 'safe');\n        });\n    }\n\n    async findRootBlockFromChild(childBlockNumber: TYPE_AMOUNT): Promise<BaseBigNumber> {\n        const bigOne = new utils.BN(1);\n        const bigtwo = new utils.BN(2);\n        const checkPointInterval = new utils.BN(10000);\n\n        childBlockNumber = new utils.BN(childBlockNumber);\n        // first checkpoint id = start * 10000\n        let start = bigOne;\n\n        // last checkpoint id = end * 10000\n        const method = await this.method(\"currentHeaderBlock\");\n        const currentHeaderBlock = await method.read<string>();\n        let end = new utils.BN(currentHeaderBlock).div(\n            checkPointInterval\n        );\n\n        // binary search on all the checkpoints to find the checkpoint that contains the childBlockNumber\n        let ans;\n        while (start.lte(end)) {\n            if (start.eq(end)) {\n                ans = start;\n                break;\n            }\n            const mid = start.add(end).div(bigtwo);\n            const headerBlocksMethod = await this.method(\n                \"headerBlocks\",\n                mid.mul(checkPointInterval).toString()\n            );\n            const headerBlock = await headerBlocksMethod.read<{ start: number, end: number }>();\n\n            const headerStart = new utils.BN(headerBlock.start);\n            const headerEnd = new utils.BN(headerBlock.end);\n\n            if (headerStart.lte(childBlockNumber) && childBlockNumber.lte(headerEnd)) {\n                // if childBlockNumber is between the upper and lower bounds of the headerBlock, we found our answer\n                ans = mid;\n                break;\n            } else if (headerStart.gt(childBlockNumber)) {\n                // childBlockNumber was checkpointed before this header\n                end = mid.sub(bigOne);\n            } else if (headerEnd.lt(childBlockNumber)) {\n                // childBlockNumber was checkpointed after this header\n                start = mid.add(bigOne);\n            }\n        }\n        return ans.mul(checkPointInterval);\n    }\n\n}\n", "import { IPOSClientConfig, IPOSContracts, ITransactionOption } from \"../interfaces\";\nimport { RootChainManager } from \"./root_chain_manager\";\nimport { Converter, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ExitUtil } from \"./exit_util\";\nimport { Log_Event_Signature } from \"../enums\";\n\nexport class ERC721 extends POSToken {\n\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: 'ChildERC721',\n            bridgeType: 'pos'\n        }, client, getContracts);\n    }\n\n    private validateMany_(tokenIds) {\n        if (tokenIds.length > 20) {\n            throw new Error('can not process more than 20 tokens');\n        }\n        return tokenIds.map(tokenId => {\n            return Converter.toHex(tokenId);\n        });\n    }\n\n    /**\n     * get tokens count for the user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [options]\n     * @returns\n     * @memberof ERC721\n     */\n    getTokensCount(userAddress: string, options?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress\n            );\n            return this.processRead<string>(method, options);\n        }).then(count => {\n            return Number(count);\n        });\n    }\n\n    /**\n     * returns token id on supplied index for user\n     *\n     * @param {number} index\n     * @param {string} userAddress\n     * @param {ITransactionOption} [options]\n     * @returns\n     * @memberof ERC721\n     */\n    getTokenIdAtIndexForUser(index: number, userAddress: string, options?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"tokenOfOwnerByIndex\",\n                userAddress,\n                index\n            );\n\n            return this.processRead<string>(method, options);\n        });\n    }\n\n    /**\n     * get all tokens for user\n     *\n     * @param {string} userAddress\n     * @param {*} [limit=Infinity]\n     * @returns\n     * @memberof ERC721\n     */\n    getAllTokens(userAddress: string, limit = Infinity) {\n        return this.getTokensCount(userAddress).then(count => {\n            count = Number(count);\n            if (count > limit) {\n                count = limit;\n            }\n            const promises = [];\n            for (let i = 0; i < count; i++) {\n                promises.push(\n                    this.getTokenIdAtIndexForUser(i, userAddress)\n                );\n            }\n            return Promise.all(\n                promises\n            );\n        });\n    }\n\n    isApproved(tokenId: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApproved\");\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"getApproved\",\n                tokenId\n            );\n            return Promise.all([\n                this.processRead<string>(method, option),\n                this.getPredicateAddress()\n            ]).then(result => {\n                return result[0] === result[1];\n            });\n        });\n    }\n\n    isApprovedAll(userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApprovedAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"isApprovedForAll\",\n                userAddress,\n                predicateAddress\n            );\n            return this.processRead<boolean>(method, option);\n        });\n\n    }\n\n    approve(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"approve\",\n                predicateAddress,\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    approveAll(option?: ITransactionOption) {\n        this.checkForRoot(\"approveAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"setApprovalForAll\",\n                predicateAddress,\n                true\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n\n    deposit(tokenId: TYPE_AMOUNT, userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(tokenId)],\n            ['uint256'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    depositMany(tokenIds: TYPE_AMOUNT[], userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"depositMany\");\n\n        const tokensInHex = this.validateMany_(tokenIds);\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [tokensInHex],\n            ['uint256[]'],\n        );\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n    }\n\n    withdrawStart(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdraw\",\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawStartWithMetaData(tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartWithMetaData\");\n\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawWithMetadata\",\n                Converter.toHex(tokenId)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawStartMany(tokenIds: TYPE_AMOUNT[], option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartMany\");\n\n        const tokensInHex = this.validateMany_(tokenIds);\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawBatch\",\n                tokensInHex\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            Log_Event_Signature.Erc721Transfer,\n            false\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    withdrawExitOnIndex(burnTransactionHash: string, index: number, option?: ITransactionOption) {\n      this.checkForRoot(\"withdrawExit\");\n\n      return this.exitUtil.buildPayloadForExit(\n          burnTransactionHash,\n          Log_Event_Signature.Erc721Transfer,\n          false,\n          index\n      ).then(payload => {\n          return this.rootChainManager.exit(\n              payload, option\n          );\n      });\n    }\n\n    // async withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption) {\n    //     this.checkForRoot(\"withdrawExitMany\");\n\n    //     return this.exitUtil.buildMultiplePayloadsForExit(\n    //         burnTransactionHash,\n    //         Log_Event_Signature.Erc721BatchTransfer,\n    //         false\n    //     ).then(async payloads => {\n    //         const exitTxs = [];\n    //         if()\n    //         for(const i in payloads) {\n    //           exitTxs.push(this.rootChainManager.exit(\n    //             payloads[i], option\n    //         ));\n    //         }\n    //         return Promise.all(exitTxs);\n    //         });\n    // }\n\n    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.exitUtil.buildPayloadForExit(\n            burnTransactionHash,\n            Log_Event_Signature.Erc721Transfer,\n            true\n        ).then(payload => {\n            return this.rootChainManager.exit(\n                payload, option\n            );\n        });\n    }\n\n    // withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption) {\n    //     this.checkForRoot(\"withdrawExitFasterMany\");\n\n\n    //     return this.exitUtil.buildPayloadForExit(\n    //         burnTransactionHash,\n    //         Log_Event_Signature.Erc721BatchTransfer,\n    //         true\n    //     ).then(payload => {\n    //         return this.rootChainManager.exit(\n    //             payload, option\n    //         );\n    //     });\n    // }\n\n    isWithdrawExited(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc721Transfer\n        );\n    }\n\n    isWithdrawExitedMany(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc721BatchTransfer\n        );\n    }\n\n    isWithdrawExitedOnIndex(txHash: string, index: number) {\n        return this.isWithdrawnOnIndex(\n            txHash, index, Log_Event_Signature.Erc721Transfer\n        );\n    }\n\n    /**\n     * transfer to another user\n     *\n     * @param {string} tokenId\n     * @param {string} from\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC721\n     */\n    transfer(tokenId: string, from: string, to: string, option?: ITransactionOption) {\n        return this.transferERC721(\n            from,\n            to,\n            tokenId,\n            option\n        );\n    }\n\n}\n", "import { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\nimport { Converter, promiseResolve, Web3SideChainClient } from \"../utils\";\nimport { POSToken } from \"./pos_token\";\nimport { Log_Event_Signature } from \"../enums\";\nimport { IPOSContracts, IPOSERC1155Address } from \"../interfaces\";\nimport { POSERC1155DepositBatchParam, POSERC1155DepositParam, POSERC1155TransferParam, TYPE_AMOUNT } from \"..\";\n\nexport class ERC1155 extends POSToken {\n\n    mintablePredicateAddress: string;\n\n    get addressConfig(): IPOSERC1155Address {\n        return this.client.config.erc1155 || {};\n    }\n\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        client: Web3SideChainClient<IPOSClientConfig>,\n        getContracts: () => IPOSContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n            bridgeType: 'pos'\n        }, client, getContracts);\n\n    }\n\n    private getAddress_(value: string) {\n        const addresses = this.addressConfig;\n        if (addresses[value]) {\n            return promiseResolve(addresses[value]);\n        }\n\n        return this.client.getConfig(value);\n    }\n\n    /**\n     * get balance of a user for supplied token\n     *\n     * @param {string} userAddress\n     * @param {TYPE_AMOUNT} tokenId\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    getBalance(userAddress: string, tokenId: TYPE_AMOUNT, option?: ITransactionOption) {\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"balanceOf\",\n                userAddress,\n                Converter.toHex(tokenId)\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * check if a user is approved for all tokens\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isApprovedAll(userAddress: string, option?: ITransactionOption) {\n        this.checkForRoot(\"isApprovedAll\");\n\n        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"isApprovedForAll\",\n                userAddress,\n                predicateAddress\n            );\n            return this.processRead<boolean>(method, option);\n        });\n\n    }\n\n    private approveAll_(predicateAddressPromise: Promise<string>, option: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return Promise.all([this.getContract(), predicateAddressPromise]).then(result => {\n            const [contract, predicateAddress] = result;\n            const method = contract.method(\n                \"setApprovalForAll\",\n                predicateAddress,\n                true\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * approve all tokens \n     *\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    approveAll(option?: ITransactionOption) {\n        this.checkForRoot(\"approve\");\n\n        return this.approveAll_(\n            this.getPredicateAddress(), option\n        );\n    }\n\n    /**\n     * approve all tokens for mintable token\n     *\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    approveAllForMintable(option?: ITransactionOption) {\n        this.checkForRoot(\"approveForMintable\");\n        const addressPath = \"Main.POSContracts.MintableERC1155PredicateProxy\";\n        return this.approveAll_(\n            this.getAddress_(addressPath), option\n        );\n    }\n\n    /**\n     * deposit supplied amount of token for a user \n     *\n     * @param {POSERC1155DepositParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    deposit(param: POSERC1155DepositParam, option?: ITransactionOption) {\n        this.checkForRoot(\"deposit\");\n        return this.depositMany({\n            amounts: [param.amount],\n            tokenIds: [param.tokenId],\n            userAddress: param.userAddress,\n            data: param.data\n        }, option);\n    }\n\n    /**\n     * deposit supplied amount of multiple token for user\n     *\n     * @param {POSERC1155DepositBatchParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    depositMany(param: POSERC1155DepositBatchParam, option?: ITransactionOption) {\n        this.checkForRoot(\"depositMany\");\n\n        const { tokenIds, amounts, data, userAddress } = param;\n        const emptyHex = Converter.toHex(0);\n        const amountInABI = this.client.parent.encodeParameters(\n            [\n                tokenIds.map(t => Converter.toHex(t)),\n                amounts.map(a => Converter.toHex(a)),\n                data || emptyHex\n            ],\n            ['uint256[]', 'uint256[]', 'bytes'],\n        );\n\n        return this.rootChainManager.deposit(\n            userAddress,\n            this.contractParam.address,\n            amountInABI,\n            option\n        );\n\n    }\n\n    /**\n     * start withdraw process by burning the required amount for a token\n     *\n     * @param {string} tokenId\n     * @param {TYPE_AMOUNT} amount\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawStart(tokenId: TYPE_AMOUNT, amount: TYPE_AMOUNT, option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStart\");\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawSingle\",\n                Converter.toHex(tokenId),\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * start the withdraw process by burning the supplied amount of multiple token at a time\n     *\n     * @param {TYPE_AMOUNT[]} tokenIds\n     * @param {TYPE_AMOUNT[]} amounts\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawStartMany(tokenIds: TYPE_AMOUNT[], amounts: TYPE_AMOUNT[], option?: ITransactionOption) {\n        this.checkForChild(\"withdrawStartMany\");\n\n        const tokensInHex = tokenIds.map(t => {\n            return Converter.toHex(t);\n        });\n        const amountsInHex = amounts.map(t => {\n            return Converter.toHex(t);\n        });\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"withdrawBatch\",\n                tokensInHex,\n                amountsInHex\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * exit the withdraw process and get the burned amount on root chain\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155Transfer,\n            false,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process and get the burned amount on root chain\n     * \n     * the process is faster because it uses proof api\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFaster\");\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155Transfer,\n            true,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process for many burned transaction and get the burned amount on root chain\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitMany\");\n\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155BatchTransfer,\n            false,\n            option\n        );\n    }\n\n    /**\n     * exit the withdraw process for many burned transaction and get the burned amount on root chain\n     *\n     * the process is faster because it uses proof api\n     * \n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExitFasterMany\");\n\n\n        return this.withdrawExitPOS(\n            burnTransactionHash,\n            Log_Event_Signature.Erc1155BatchTransfer,\n            true,\n            option\n        );\n    }\n\n    /**\n     * check if exit has been completed for a transaction hash\n     *\n     * @param {string} burnTxHash\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isWithdrawExited(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc1155Transfer\n        );\n    }\n\n    /**\n     * check if batch exit has been completed for a transaction hash\n     *\n     * @param {string} txHash\n     * @return {*} \n     * @memberof ERC1155\n     */\n    isWithdrawExitedMany(txHash: string) {\n        return this.isWithdrawn(\n            txHash, Log_Event_Signature.Erc1155BatchTransfer\n        );\n    }\n\n    /**\n     * transfer the required amount of a token to another user\n     *\n     * @param {POSERC1155TransferParam} param\n     * @param {ITransactionOption} [option]\n     * @return {*} \n     * @memberof ERC1155\n     */\n    transfer(param: POSERC1155TransferParam, option?: ITransactionOption) {\n        return this.transferERC1155(\n            param, option\n        );\n    }\n}", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IPOSClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class GasSwapper extends BaseToken<IPOSClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'GasSwapper',\n            bridgeType: 'pos',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    depositWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        swapCallData: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"swapAndBridge\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n            swapCallData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n}\n", "import { ERC20 } from \"./erc20\";\nimport { Root<PERSON>hainManager } from \"./root_chain_manager\";\nimport { BridgeClient } from \"../utils\";\nimport { IPOSClientConfig, IPOSContracts, ITransactionOption } from \"../interfaces\";\nimport { ExitUtil } from \"./exit_util\";\nimport { RootChain } from \"./root_chain\";\nimport { ERC721 } from \"./erc721\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { ERC1155 } from \"./erc1155\";\nimport { GasSwapper } from \"./gas_swapper\";\n\nexport * from \"./exit_util\";\nexport * from \"./root_chain_manager\";\nexport * from \"./root_chain\";\nexport * from \"./gas_swapper\";\n\nexport class POSClient extends BridgeClient<IPOSClientConfig> {\n\n    rootChainManager: RootChainManager;\n    gasSwapper: GasSwapper;\n\n    init(config: IPOSClientConfig) {\n        const client = this.client;\n\n        return client.init(config).then(_ => {\n            const mainPOSContracts = client.mainPOSContracts;\n            client.config = config = Object.assign(\n                {\n                    rootChainManager: mainPOSContracts.RootChainManagerProxy,\n                    rootChain: client.mainPlasmaContracts.RootChainProxy,\n                    gasSwapper: mainPOSContracts.GasSwapper\n                } as IPOSClientConfig,\n                config\n            );\n\n            this.rootChainManager = new RootChainManager(\n                this.client,\n                config.rootChainManager,\n            );\n\n            const rootChain = new RootChain(\n                this.client,\n                config.rootChain,\n            );\n\n            this.exitUtil = new ExitUtil(\n                this.client,\n                rootChain\n            );\n\n            this.gasSwapper = new GasSwapper(\n                this.client,\n                config.gasSwapper\n            );\n\n            return this;\n        });\n    }\n\n    erc20(tokenAddress, isParent?: boolean) {\n        return new ERC20(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    erc721(tokenAddress, isParent?: boolean) {\n        return new ERC721(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    erc1155(tokenAddress, isParent?: boolean) {\n        return new ERC1155(\n            tokenAddress,\n            isParent,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    depositEther(amount: TYPE_AMOUNT, userAddress: string, option: ITransactionOption) {\n        return new ERC20(\n            '', true, this.client,\n            this.getContracts_.bind(this),\n        )['depositEther_'](amount, userAddress, option);\n    }\n\n    depositEtherWithGas(\n        amount: TYPE_AMOUNT,\n        userAddress: string,\n        swapEthAmount: TYPE_AMOUNT,\n        swapCallData: string,\n        option: ITransactionOption\n    ) {\n        return new ERC20(\n            '', true, this.client,\n            this.getContracts_.bind(this),\n        )['depositEtherWithGas_'](amount, userAddress, swapEthAmount, swapCallData, option);\n    }\n\n    private getContracts_() {\n        return {\n            exitUtil: this.exitUtil,\n            rootChainManager: this.rootChainManager,\n            gasSwapper: this.gasSwapper\n        } as IPOSContracts;\n    }\n}", "import { POSClient } from \"./pos\";\nimport { use, utils } from \"./utils\";\n\nexport const defaultExport = {\n    utils: utils,\n    use,\n    POSClient,\n};", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IContractInitParam, IZkEvmClientConfig } from \"../interfaces\";\nimport { IZkEvmContracts } from \"../interfaces\";\n\nexport class ZkEvmToken extends BaseToken<IZkEvmClientConfig> {\n\n    constructor(\n        contractParam: IContractInitParam,\n        client: Web3SideChainClient<IZkEvmClientConfig>,\n        protected getZkEvmContracts: () => IZkEvmContracts\n    ) {\n        super(contractParam, client);\n    }\n\n    protected get parentBridge() {\n        return this.getZkEvmContracts().parentBridge;\n    }\n\n    protected get zkEVMWrapper() {\n        return this.getZkEvmContracts().zkEVMWrapper;\n    }\n\n    protected get childBridge() {\n        return this.getZkEvmContracts().childBridge;\n    }\n\n    protected get bridgeUtil() {\n        return this.getZkEvmContracts().bridgeUtil;\n    }\n\n}\n", "import { ITransactionOption, IZkEvmClientConfig } from '../interfaces';\nimport { BaseToken, Converter, Web3SideChainClient } from '../utils';\nimport { TYPE_AMOUNT } from '../types';\n\n/**\n * ZkEVMBridgeAdapter used ZkEVMBridge to implement additional custom features\n * like bridging custom ERC20\n */\nexport class ZkEVMBridgeAdapter extends BaseToken<IZkEvmClientConfig> {\n\n  constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string, isParent: boolean) {\n    super(\n      {\n        address: address,\n        name: 'ZkEVMBridgeAdapter',\n        bridgeType: 'zkevm',\n        isParent: isParent, // decides if it's a child chain or a root chain adapter\n      },\n      client_,\n    );\n  }\n\n  method(methodName: string, ...args) {\n    return this.getContract().then(contract => {\n      return contract.method(methodName, ...args);\n    });\n  }\n\n  /**\n   * uses the bridge function present in the adapter contract\n   * @param recipient\n   * @param amount\n   * @param forceUpdateGlobalExitRoot\n   * @param option\n   *\n   * @returns\n   * @memberof ZkEvmCustomBridge\n   */\n  bridgeToken(\n    recipient: string,\n    amount: TYPE_AMOUNT,\n    forceUpdateGlobalExitRoot?: boolean,\n    option?: ITransactionOption,\n  ) {\n    return this.method('bridgeToken', recipient, Converter.toHex(amount), forceUpdateGlobalExitRoot).then(\n      method => {\n        return this.processWrite(method, option);\n      },\n    );\n  }\n}\n", "import { isHexString } from '@ethereumjs/util';\nimport { ITransactionOption } from \"../interfaces\";\nimport { Converter, Web3SideChainClient, promiseAny } from \"../utils\";\nimport { ZkEvmToken } from \"./zkevm_token\";\nimport { TYPE_AMOUNT } from \"../types\";\nimport { BaseContractMethod } from \"../abstracts\";\nimport { MAX_AMOUNT, ADDRESS_ZERO, DAI_PERMIT_TYPEHASH, EIP_2612_PERMIT_TYPEHASH, UNISWAP_DOMAIN_TYPEHASH, EIP_2612_DOMAIN_TYPEHASH, Permit, BaseContract, BaseWeb3Client, ERROR_TYPE } from '..';\nimport { IAllowanceTransactionOption, IApproveTransactionOption, IBridgeTransactionOption, IZkEvmClientConfig, IZkEvmContracts } from \"../interfaces\";\nimport { ZkEVMBridgeAdapter } from './zkevm_custom_bridge';\n\nexport class ERC20 extends ZkEvmToken {\n    private bridgeAdapter: ZkEVMBridgeAdapter;\n    constructor(\n        tokenAddress: string,\n        isParent: boolean,\n        bridgeAdapterAddress,\n        client: Web3SideChainClient<IZkEvmClientConfig>,\n        getContracts: () => IZkEvmContracts\n    ) {\n        super({\n            isParent,\n            address: tokenAddress,\n            bridgeAdapterAddress,\n            name: 'ERC20',\n            bridgeType: 'zkevm'\n        }, client, getContracts);\n        if (bridgeAdapterAddress) {\n            this.bridgeAdapter = new ZkEVMBridgeAdapter(\n                this.client,\n                bridgeAdapterAddress,\n                isParent\n            );\n        }\n    }\n\n    /**\n     * get bridge for that token\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    getBridgeAddress() {\n        const bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;\n        return bridge.contractAddress;\n    }\n\n    isEtherToken() {\n        return this.contractParam.address === ADDRESS_ZERO;\n    }\n\n    /**\n     * get token balance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getBalance(userAddress: string, option?: ITransactionOption) {\n        if (this.isEtherToken()) {\n            const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n            return client.getBalance(userAddress);\n        } else {\n            return this.getContract().then(contract => {\n                const method = contract.method(\n                    \"balanceOf\",\n                    userAddress\n                );\n                return this.processRead<string>(method, option);\n            });\n        }\n\n    }\n\n    /**\n     * is Approval needed to bridge tokens to other chains\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    isApprovalNeeded() {\n        if (this.isEtherToken()) {\n            return false;\n        }\n\n        const bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;\n\n        return bridge.getOriginTokenInfo(this.contractParam.address)\n            .then(tokenInfo => {\n                return tokenInfo[1] === ADDRESS_ZERO;\n            });\n    }\n\n    /**\n     * get allowance of user\n     *\n     * @param {string} userAddress\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    getAllowance(userAddress: string, option: IAllowanceTransactionOption = {}) {\n        this.checkForNonNative(\"getAllowance\");\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"allowance\",\n                userAddress,\n                spenderAddress,\n            );\n            return this.processRead<string>(method, option);\n        });\n    }\n\n    /**\n     * Approve given amount of tokens for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {IApproveTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    approve(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"approve\");\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getContract().then(contract => {\n            const method = contract.method(\n                \"approve\",\n                spenderAddress,\n                Converter.toHex(amount)\n            );\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Approve max amount of tokens for user\n     *\n     * @param {IApproveTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    approveMax(option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"approveMax\");\n        return this.approve(\n            MAX_AMOUNT,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    deposit(amount: TYPE_AMOUNT, userAddress: string, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        const permitData = option.permitData || '0x';\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        if (this.isEtherToken()) {\n            option.value = Converter.toHex(amount);\n        }\n\n        return this.childBridge.networkID().then(networkId => {\n            return this.parentBridge.bridgeAsset(\n                networkId,\n                userAddress,\n                amountInABI,\n                this.contractParam.address,\n                forceUpdateGlobalExitRoot,\n                permitData,\n                option\n            );\n        });\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETH for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        option.value = Converter.toHex(ethGasAmount);\n        if (option.v && option.r && option.s) {\n            return this.zkEVMWrapper.depositPermitWithGas(\n                this.contractParam.address,\n                amountInABI,\n                userAddress,\n                Math.floor((Date.now() + 3600000) / 1000).toString(),\n                option.v,\n                option.r,\n                option.s,\n                option\n            );\n        }\n        return this.zkEVMWrapper.depositWithGas(\n            this.contractParam.address,\n            amountInABI,\n            userAddress,\n            option\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user along with ETH for gas token\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositPermitWithGas(amount: TYPE_AMOUNT, userAddress: string, ethGasAmount: TYPE_AMOUNT, option: IBridgeTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        this.checkForNonNative(\"getPermitData\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        option.value = Converter.toHex(ethGasAmount);\n\n        return this.getPermitSignatureParams_(amount, this.zkEVMWrapper.contractAddress).then(\n            signatureParams => {\n                return this.zkEVMWrapper.depositPermitWithGas(\n                    this.contractParam.address,\n                    amountInABI,\n                    userAddress,\n                    Math.floor((Date.now() + 3600000) / 1000).toString(),\n                    signatureParams.v,\n                    signatureParams.r,\n                    signatureParams.s,\n                    option\n                );\n            }\n        );\n    }\n\n    /**\n     * Deposit given amount of token for user with permit call\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositWithPermit(amount: TYPE_AMOUNT, userAddress: string, option: IApproveTransactionOption = {}) {\n        this.checkForRoot(\"deposit\");\n        this.checkForNonNative(\"depositWithPermit\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        return this.getPermitData(amountInABI, option).then(permitData => {\n            return this.childBridge.networkID().then(networkId => {\n                return this.parentBridge.bridgeAsset(\n                    networkId,\n                    userAddress,\n                    amountInABI,\n                    this.contractParam.address,\n                    forceUpdateGlobalExitRoot,\n                    permitData,\n                    option\n                );\n            });\n        });\n    }\n\n    /**\n     * Bridge asset to child chain using Custom ERC20 bridge Adapter\n     * @param amount\n     * @param userAddress\n     * @param forceUpdateGlobalExitRoot\n     * @returns\n     * @memberof ERC20\n     */\n    depositCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot = true) {\n        // should be allowed to be used only in root chain\n        this.checkForRoot(\"depositCustomERC20\");\n        this.checkAdapterPresent(\"depositCustomERC20\");\n        // should not be allowed to use for native asset\n        this.checkForNonNative(\"depositCustomERC20\");\n        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);\n    }\n\n    /**\n     * Claim asset on child chain bridged using custom bridge adapter on root chain\n     * @param transactionHash\n     * @param option\n     * @returns\n     * @memberof ERC20\n     */\n    customERC20DepositClaim(transactionHash: string, option?: ITransactionOption) {\n        this.checkForChild(\"customERC20DepositClaim\");\n        return this.parentBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                transactionHash, true, networkId\n            );\n        }).then(payload => {\n            return this.childBridge.claimMessage(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n\n    /**\n     * Complete deposit after GlobalExitRootManager is synced from Parent to root\n     *\n     * @param {string} transactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    depositClaim(transactionHash: string, option?: ITransactionOption) {\n        this.checkForChild(\"depositClaim\");\n        return this.parentBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                transactionHash, true, networkId\n            );\n        }).then(payload => {\n            return this.childBridge.claimAsset(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by burning provided amount\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdraw(amount: TYPE_AMOUNT, userAddress: string, option: IBridgeTransactionOption = {}) {\n        this.checkForChild(\"withdraw\");\n        const permitData = option.permitData || '0x';\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        if (this.isEtherToken()) {\n            option.value = Converter.toHex(amount);\n        }\n\n        return this.parentBridge.networkID().then(networkId => {\n            return this.childBridge.bridgeAsset(\n                networkId,\n                userAddress,\n                amountInABI,\n                this.contractParam.address,\n                forceUpdateGlobalExitRoot,\n                permitData,\n                option\n            );\n        });\n    }\n\n    /**\n     * Bridge asset to root chain using Custom ERC20 bridge Adapter\n     * @param amount\n     * @param userAddress\n     * @param forceUpdateGlobalExitRoot\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawCustomERC20(amount: TYPE_AMOUNT, userAddress: string, forceUpdateGlobalExitRoot = true) {\n        // should be allowed to be used only in root chain\n        this.checkForChild(\"withdrawCustomERC20\");\n        this.checkAdapterPresent(\"depositCustomERC20\");\n        // should not be allowed to use for native asset\n        this.checkForNonNative(\"withdrawCustomERC20\");\n        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);\n    }\n\n    /**\n     * Claim asset on root chain bridged using custom bridge adapter on child chain\n     * @param burnTransactionHash\n     * @param option\n     * @returns\n     * @memberof ERC20\n     */\n    customERC20WithdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"customERC20WithdrawExit\");\n        return this.childBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                burnTransactionHash, false, networkId\n            );\n        }).then(payload => {\n            return this.parentBridge.claimMessage(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * initiate withdraw by transferring amount with PermitData for native tokens\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} userAddress\n     * @param {IBridgeTransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawWithPermit(amount: TYPE_AMOUNT, userAddress: string, option: IApproveTransactionOption = {}) {\n        this.checkForChild(\"withdraw\");\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;\n\n        return this.getPermitData(amountInABI, option).then(permitData => {\n            return this.parentBridge.networkID().then(networkId => {\n                return this.childBridge.bridgeAsset(\n                    networkId,\n                    userAddress,\n                    amountInABI,\n                    this.contractParam.address,\n                    forceUpdateGlobalExitRoot,\n                    permitData,\n                    option\n                );\n            });\n        });\n    }\n\n    /**\n     * Complete deposit after GlobalExitRootManager is synced from Parent to root\n     *\n     * @param {string} burnTransactionHash\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    withdrawExit(burnTransactionHash: string, option?: ITransactionOption) {\n        this.checkForRoot(\"withdrawExit\");\n        return this.childBridge.networkID().then(networkId => {\n            return this.bridgeUtil.buildPayloadForClaim(\n                burnTransactionHash, false, networkId\n            );\n        }).then(payload => {\n            return this.parentBridge.claimAsset(\n                payload.smtProof,\n                payload.smtProofRollup,\n                payload.globalIndex,\n                payload.mainnetExitRoot,\n                payload.rollupExitRoot,\n                payload.originNetwork,\n                payload.originTokenAddress,\n                payload.destinationNetwork,\n                payload.destinationAddress,\n                payload.amount,\n                payload.metadata,\n                option\n            );\n        });\n    }\n\n    /**\n     * transfer amount to another user\n     *\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} to\n     * @param {ITransactionOption} [option]\n     * @returns\n     * @memberof ERC20\n     */\n    transfer(amount: TYPE_AMOUNT, to: string, option: ITransactionOption = {}) {\n        if (this.contractParam.address === ADDRESS_ZERO) {\n            option.to = to;\n            option.value = Converter.toHex(amount);\n            return this.sendTransaction(option);\n        }\n        return this.transferERC20(to, amount, option);\n    }\n\n    /**\n     * get permitType of the token\n     *\n     * @returns\n     * @memberof ERC20\n     */\n    private getPermit() {\n        let contract: BaseContract;\n        return this.getContract().then(contractInstance => {\n            contract = contractInstance;\n            const method = contract.method(\n                \"PERMIT_TYPEHASH\",\n            );\n            return this.processRead<string>(method);\n        }).then(permitTypehash => {\n            switch (permitTypehash) {\n                case DAI_PERMIT_TYPEHASH: {\n                    return Permit.DAI;\n                }\n                case EIP_2612_PERMIT_TYPEHASH: {\n                    const DOMAIN_TYPEHASH = contract.method(\"DOMAIN_TYPEHASH\");\n                    const EIP712DOMAIN_HASH = contract.method(\"EIP712DOMAIN_HASH\");\n                    return promiseAny([this.processRead<string>(DOMAIN_TYPEHASH), this.processRead<string>(EIP712DOMAIN_HASH)]).then(\n                        (domainTypehash) => {\n                            switch (domainTypehash) {\n                                case EIP_2612_DOMAIN_TYPEHASH: {\n                                    return Permit.EIP_2612;\n                                }\n                                case UNISWAP_DOMAIN_TYPEHASH: {\n                                    return Permit.UNISWAP;\n                                }\n                                default: {\n                                    return Promise.reject(new Error(`Unsupported domain typehash: ${domainTypehash}`));\n                                }\n                            }\n                        }\n                    );\n                }\n                default: {\n                    return Promise.reject(new Error(`Unsupported permit typehash: ${permitTypehash}`));\n                }\n            }\n        });\n    }\n\n    /**\n     * get typedData for signing\n     * @param {string} permitType\n     * @param {string} account\n     * @param {number} chainId\n     * @param {string} name\n     * @param {string} nonce\n     * @param {string} spenderAddress\n     * @param {string} amount\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getTypedData_(permitType: string, account: string, chainId: number, name: string, nonce: string, spenderAddress: string, amount: string) {\n        const typedData = {\n            types: {\n                EIP712Domain: [\n                    { name: 'name', type: 'string' },\n                    { name: 'version', type: 'string' },\n                    { name: 'chainId', type: 'uint256' },\n                    { name: 'verifyingContract', type: 'address' }\n                ],\n                Permit: []\n            },\n            primaryType: \"Permit\",\n            domain: {\n                name,\n                version: \"1\",\n                chainId,\n                verifyingContract: this.contractParam.address,\n            },\n            message: {}\n        };\n        switch (permitType) {\n            case Permit.DAI:\n                typedData.types.Permit = [\n                    { name: \"holder\", type: \"address\" },\n                    { name: \"spender\", type: \"address\" },\n                    { name: \"nonce\", type: \"uint256\" },\n                    { name: \"expiry\", type: \"uint256\" },\n                    { name: \"allowed\", type: \"bool\" },\n                ];\n                typedData.message = {\n                    holder: account,\n                    spender: spenderAddress,\n                    nonce,\n                    expiry: Math.floor((Date.now() + 3600000) / 1000),\n                    allowed: true,\n                };\n            case Permit.EIP_2612:\n            case Permit.UNISWAP:\n\n                if (permitType === Permit.UNISWAP) {\n                    typedData.types.EIP712Domain = [\n                        { name: 'name', type: 'string' },\n                        { name: 'chainId', type: 'uint256' },\n                        { name: 'verifyingContract', type: 'address' }\n                    ];\n                    delete typedData.domain.version;\n                }\n                typedData.types.Permit = [\n                    { name: 'owner', type: 'address' },\n                    { name: 'spender', type: 'address' },\n                    { name: 'value', type: 'uint256' },\n                    { name: 'nonce', type: 'uint256' },\n                    { name: 'deadline', type: 'uint256' }\n                ];\n                typedData.message = {\n                    owner: account,\n                    spender: spenderAddress,\n                    value: amount,\n                    nonce: nonce,\n                    deadline: Math.floor((Date.now() + 3600000) / 1000),\n                };\n        }\n        return typedData;\n    }\n\n    /**\n     * get {r, s, v} from signature\n     * @param {BaseWeb3Client} client\n     * @param {string} signature\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getSignatureParameters_(client: BaseWeb3Client, signature: string) {\n        if (!isHexString(signature)) {\n            throw new Error(\n                'Given value \"'.concat(signature, '\" is not a valid hex string.'),\n            );\n        }\n\n        if (signature.slice(0, 2) !== '0x') {\n            signature = '0x'.concat(signature);\n        }\n\n        const r = signature.slice(0, 66);\n        const s = '0x'.concat(signature.slice(66, 130));\n        let v = client.hexToNumber('0x'.concat(signature.slice(130, 132)));\n        if (![27, 28].includes(v as any)) {\n            v += 27;\n        }\n        return {\n            r: r,\n            s: s,\n            v: v,\n        };\n    }\n\n    /**\n     * encode permit function data\n     * @param {BaseContract} contract\n     * @param {string} permitType\n     * @param {any} signatureParams\n     * @param {string} spenderAddress\n     * @param {string} account\n     * @param {string} nonce\n     * @param {string} amount\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private encodePermitFunctionData_(contract: BaseContract, permitType: string, signatureParams: any, spenderAddress: string, account: string, nonce: string, amount: string) {\n        const { r, s, v } = signatureParams;\n        let method: BaseContractMethod;\n        switch (permitType) {\n            case Permit.DAI:\n                method = contract.method(\n                    \"permit\",\n                    account,\n                    spenderAddress,\n                    nonce,\n                    Math.floor((Date.now() + 3600000) / 1000),\n                    true,\n                    v,\n                    r,\n                    s,\n                );\n                break;\n\n            case Permit.EIP_2612:\n            case Permit.UNISWAP:\n                method = contract.method(\n                    \"permit\",\n                    account,\n                    spenderAddress,\n                    amount,\n                    Math.floor((Date.now() + 3600000) / 1000),\n                    v,\n                    r,\n                    s,\n                );\n                break;\n        }\n        return method.encodeABI();\n    }\n\n    private getPermitSignatureParams_(amount: TYPE_AMOUNT, spenderAddress: string) {\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n        let account: string;\n        let chainId: number;\n        let permitType: string;\n        let contract: BaseContract;\n        let nonce: string;\n\n        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(result => {\n            account = result[0][0];\n            contract = result[1];\n            chainId = result[2];\n            permitType = result[3];\n            const nameMethod = contract.method(\"name\");\n            const nonceMethod = contract.method(\"nonces\", account);\n            return Promise.all([this.processRead<string>(nameMethod), this.processRead<string>(nonceMethod)]);\n        }).then(data => {\n            const name = data[0];\n            nonce = data[1];\n            return this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);\n        }).then(typedData => {\n            return client.signTypedData(account, typedData);\n        }).then(signature => {\n            return this.getSignatureParameters_(client, signature);\n        });\n    }\n\n    /**\n     * Get permit data for given spender for given amount\n     * @param {TYPE_AMOUNT} amount\n     * @param {string} spenderAddress\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    private getPermitData_(amount: TYPE_AMOUNT, spenderAddress: string) {\n\n        const amountInABI = this.client.parent.encodeParameters(\n            [Converter.toHex(amount)],\n            ['uint256'],\n        );\n\n        const client = this.contractParam.isParent ? this.client.parent : this.client.child;\n        let account: string;\n        let chainId: number;\n        let permitType: string;\n        let contract: BaseContract;\n        let nonce: string;\n\n        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(result => {\n            account = result[0][0];\n            contract = result[1];\n            chainId = result[2];\n            permitType = result[3];\n            const nameMethod = contract.method(\"name\");\n            const nonceMethod = contract.method(\"nonces\", account);\n            return Promise.all([this.processRead<string>(nameMethod), this.processRead<string>(nonceMethod)]);\n        }).then(data => {\n            const name = data[0];\n            nonce = data[1];\n            return this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);\n        }).then(typedData => {\n            return client.signTypedData(account, typedData);\n        }).then(signature => {\n            const signatureParameters = this.getSignatureParameters_(client, signature);\n            return this.encodePermitFunctionData_(\n                contract, permitType, signatureParameters, spenderAddress, account, nonce, amountInABI\n            );\n        });\n    }\n\n    /**\n     * Get permit data for given amount\n     * @param {TYPE_AMOUNT} amount\n     * @param {IApproveTransactionOption} option\n     * \n     * @returns\n     * @memberof ERC20\n     */\n    getPermitData(amount: TYPE_AMOUNT, option: IApproveTransactionOption = {}) {\n        this.checkForNonNative(\"getPermitData\");\n\n        const spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();\n\n        return this.getPermitData_(amount, spenderAddress);\n    }\n}\n", "import { BaseToken, Web3SideChainClient, Converter, promiseResolve } from \"../utils\";\nimport { IZkEvmClientConfig, ITransactionOption } from \"../interfaces\";\nimport { TYPE_AMOUNT } from \"../types\";\n\nexport class ZkEvmBridge extends BaseToken<IZkEvmClientConfig> {\n\n    networkID_: number;\n\n    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string, isParent: boolean) {\n        super({\n            address: address,\n            name: 'PolygonZkEVMBridge',\n            bridgeType: 'zkevm',\n            isParent: isParent\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    /**\n     * bridge function to be called on that network from where token is to be transferred to a different network\n     *\n     * @param {string} token Token address\n     * @param {number} destinationNetwork Network at which tokens will be bridged\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amountamount amount of tokens\n     * @param {string} [permitData] Permit data to avoid approve call\n     * @param {ITransactionOption} [option] \n     * \n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    bridgeAsset(\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        token: string,\n        forceUpdateGlobalExitRoot: boolean,\n        permitData = '0x',\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"bridgeAsset\",\n            destinationNetwork,\n            destinationAddress,\n            Converter.toHex(amount),\n            token,\n            forceUpdateGlobalExitRoot,\n            permitData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Claim function to be called on the destination network\n     *\n     * @param {string[]} smtProof Merkle Proof\n     * @param {string[]} smtProofRollup Roll up Merkle Proof\n     * @param {string} globalIndex Global Index\n     * @param {string} mainnetExitRoot Mainnet Exit Root\n     * @param {string} rollupExitRoot RollUP Exit Root\n     * @param {number} originNetwork Network at which token was initially deployed\n     * @param {string} originTokenAddress Address of token at network where token was initially deployed\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amount amount of tokens\n     * @param {string} [metadata] Metadata of token\n     * @param {ITransactionOption} [option]\n     * \n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    claimAsset(\n        smtProof: string[],\n        smtProofRollup: string[],\n        globalIndex: string,\n        mainnetExitRoot: string,\n        rollupExitRoot: string,\n        originNetwork: number,\n        originTokenAddress: string,\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        metadata: string,\n        option: ITransactionOption\n    ) {\n        return this.method(\n            \"claimAsset\",\n            smtProof,\n            smtProofRollup,\n            globalIndex,\n            mainnetExitRoot,\n            rollupExitRoot,\n            originNetwork,\n            originTokenAddress,\n            destinationNetwork,\n            destinationAddress,\n            amount,\n            metadata\n        ).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    /**\n     * bridge function to be called on that network from where message is to be transferred to a different network\n     * @param {number} destinationNetwork Network at which tokens will be bridged\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {boolean} forceUpdateGlobalExitRoot Indicates if the new global exit root is updated or not\n     * @param {string} [permitData] Permit data to avoid approve call\n     * @param {ITransactionOption} [option]\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    bridgeMessage(\n        destinationNetwork: number,\n        destinationAddress: string,\n        forceUpdateGlobalExitRoot: boolean,\n        permitData = '0x',\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"bridgeMessage\",\n            destinationNetwork,\n            destinationAddress,\n            forceUpdateGlobalExitRoot,\n            permitData\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    /**\n     * Claim Message new function to be called on the destination network\n     * If the receiving address is an EOA, the call will result as a success\n     * Which means that the amount of ether will be transferred correctly, but the message\n     * will not trigger any execution. this will work after Etrog changes\n     * @param {string[]} smtProof Merkle Proof\n     * @param {string[]} smtProofRollup Roll up Merkle Proof\n     * @param {string} globalIndex Global Index\n     * @param {string} mainnetExitRoot Mainnet Exit Root\n     * @param {string} rollupExitRoot RollUP Exit Root\n     * @param {number} originNetwork Network at which token was initially deployed\n     * @param {string} originTokenAddress Address of token at network where token was initially deployed\n     * @param {string} destinationAddress Address to which tokens will be bridged\n     * @param {TYPE_AMOUNT} amount amount of tokens\n     * @param {string} [metadata] Metadata of token\n     * @param {ITransactionOption} [option]\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    claimMessage(\n        smtProof: string[],\n        smtProofRollup: string[],\n        globalIndex: string,\n        mainnetExitRoot: string,\n        rollupExitRoot: string,\n        originNetwork: number,\n        originTokenAddress: string,\n        destinationNetwork: number,\n        destinationAddress: string,\n        amount: TYPE_AMOUNT,\n        metadata: string,\n        option: ITransactionOption) {\n        return this.method(\n            \"claimMessage\",\n            smtProof,\n            smtProofRollup,\n            globalIndex,\n            mainnetExitRoot,\n            rollupExitRoot,\n            originNetwork,\n            originTokenAddress,\n            destinationNetwork,\n            destinationAddress,\n            amount,\n            metadata\n        ).then(method => {\n            return this.processWrite(\n                method,\n                option\n            );\n        });\n    }\n\n    /**\n     * get the address of token which is created by the bridge contract on the non origin chain\n     *\n     * @param {number} originNetwork Network at which the token was initially deployed\n     * @param {string} originTokenAddress Address at the network where token was initially deployed\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    getMappedTokenInfo(\n        originNetwork: number,\n        originTokenAddress: string\n    ) {\n        return this.method(\n            \"getTokenWrappedAddress\", originNetwork, originTokenAddress\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * Tells if claim has already happed or not based on the deposit index\n     *\n     * @param {number} index\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    isClaimed(\n        index: number,\n        sourceBridgeNetwork: number\n    ) {\n        return this.method(\n            \"isClaimed\", index, sourceBridgeNetwork\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * Even if the wrapped contract is not deployed on the destination chain, it will tell us the address which is going to be.\n     *\n     * @param {number} originNetwork Network at which the token was initially deployed\n     * @param {string} originTokenAddress Address at the network where token was initially deployed\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    precalculatedMappedTokenInfo(\n        originNetwork: number,\n        originTokenAddress: string\n    ) {\n        return this.method(\n            \"precalculatedWrapperAddress\", originNetwork, originTokenAddress\n        ).then(method => {\n            return this.processRead<string>(method);\n        });\n    }\n\n    /**\n     * get the address and network of the wrapped token where it was emerged initially\n     *\n     * @param {number} wrappedToken\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    getOriginTokenInfo(wrappedToken: string) {\n        return this.method(\n            \"wrappedTokenToTokenInfo\", wrappedToken\n        ).then(method => {\n            return this.processRead<[number, string]>(method);\n        });\n    }\n\n    /**\n     * get the network ID for chain in which the bridge contract is deployed\n     *\n     * @returns\n     * @memberof ZkEvmBridge\n     */\n    networkID() {\n        if (this.networkID_) {\n            return promiseResolve<number>(this.networkID_ as any);\n        }\n        return this.method(\n            \"networkID\"\n        ).then(method => {\n            return this.processRead<number>(method).then((networkId) => {\n                this.networkID_ = networkId;\n                return networkId;\n            });\n        });\n    }\n\n}\n", "import { Web3SideChainClient } from \"../utils\";\nimport { service } from \"../services\";\nimport { IBaseClientConfig, _GLOBAL_INDEX_MAINNET_FLAG } from \"..\";\nimport { TYPE_AMOUNT } from '../types';\n\ninterface IBridgeEventInfo {\n    originNetwork: number;\n    originTokenAddress: string;\n    destinationNetwork: number;\n    destinationAddress: string;\n    amount: TYPE_AMOUNT;\n    metadata: string;\n    depositCount: number;\n}\n\ninterface IMerkleProof {\n    merkle_proof: string[];\n    rollup_merkle_proof?: string[];\n    exit_root_num: string;\n    l2_exit_root_num: string;\n    main_exit_root: string;\n    rollup_exit_root: string;\n}\n\ninterface IClaimPayload {\n    smtProof: string[];\n    smtProofRollup: string[];\n    globalIndex: string;\n    mainnetExitRoot: string;\n    rollupExitRoot: string;\n    originNetwork: number;\n    originTokenAddress: string;\n    destinationNetwork: number;\n    destinationAddress: string;\n    amount: TYPE_AMOUNT;\n    metadata: string;\n}\n\nexport class BridgeUtil {\n    private client_: Web3SideChainClient<IBaseClientConfig>;\n    private BRIDGE_TOPIC = \"0x501781209a1f8899323b96b4ef08b168df93e0a90c673d1e4cce39366cb62f9b\";\n\n    constructor(client: Web3SideChainClient<IBaseClientConfig>) {\n        this.client_ = client;\n    }\n\n    private decodedBridgeData_(data: string, isParent: boolean) {\n        const client = isParent ? this.client_.parent : this.client_.child;\n        return this.client_.getABI(\"PolygonZkEVMBridge\", \"zkevm\").then(abi => {\n            const types = abi.filter(event => event.name === \"BridgeEvent\");\n            if (!types.length) {\n                throw new Error(\"Data not decoded\");\n            }\n            const decodedData = client.decodeParameters(data, types[0].inputs);\n            const [leafType, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata, depositCount] = decodedData;\n            return {\n                leafType,\n                originNetwork,\n                originTokenAddress,\n                destinationNetwork,\n                destinationAddress,\n                amount,\n                metadata: metadata || '0x',\n                depositCount,\n            } as IBridgeEventInfo;\n        });\n    }\n\n    private getBridgeLogData_(transactionHash: string, isParent: boolean) {\n        const client = isParent ? this.client_.parent : this.client_.child;\n        return client.getTransactionReceipt(transactionHash)\n            .then(receipt => {\n                const logs = receipt.logs.filter(log => log.topics[0].toLowerCase() === this.BRIDGE_TOPIC);\n                if (!logs.length) {\n                    throw new Error(\"Log not found in receipt\");\n                }\n\n                const data = logs[0].data;\n                return this.decodedBridgeData_(data, isParent);\n            });\n    }\n\n    private getProof_(networkId: number, depositCount: number) {\n        return service.zkEvmNetwork.getMerkleProofForZkEvm(\n            this.client_.config.version,\n            networkId,\n            depositCount,\n        ).then(proof => {\n            return proof as IMerkleProof;\n        }).catch(_ => {\n            throw new Error(\"Error in creating proof\");\n        });\n    }\n\n    getBridgeLogData(transactionHash: string, isParent: boolean) {\n        return this.getBridgeLogData_(transactionHash, isParent);\n    }\n\n    computeGlobalIndex(indexLocal: number, indexRollup: number, sourceNetworkId: number) {\n        if (BigInt(sourceNetworkId) === BigInt(0)) {\n            return BigInt(indexLocal) + _GLOBAL_INDEX_MAINNET_FLAG;\n        } else {\n            return BigInt(indexLocal) + BigInt(indexRollup) * BigInt(2 ** 32);\n        }\n    }\n\n    buildPayloadForClaim(transactionHash: string, isParent: boolean, networkId: number) {\n        return this.getBridgeLogData_(transactionHash, isParent).then(data => {\n            const {\n                originNetwork,\n                originTokenAddress,\n                destinationNetwork,\n                destinationAddress,\n                amount,\n                metadata,\n                depositCount } = data;\n            return this.getProof_(networkId, depositCount).then(proof => {\n                const payload = {} as IClaimPayload;\n                payload.smtProof = proof.merkle_proof;\n                payload.smtProofRollup = proof.rollup_merkle_proof;\n                payload.globalIndex = this.computeGlobalIndex(depositCount, destinationNetwork, networkId).toString();\n                payload.mainnetExitRoot = proof.main_exit_root;\n                payload.rollupExitRoot = proof.rollup_exit_root;\n                payload.originNetwork = originNetwork;\n                payload.originTokenAddress = originTokenAddress;\n                payload.destinationNetwork = destinationNetwork;\n                payload.destinationAddress = destinationAddress;\n                payload.amount = amount;\n                payload.metadata = metadata;\n                return payload;\n            });\n        });\n    }\n}\n", "import { BaseToken, Web3SideChainClient } from \"../utils\";\nimport { IZkEvmClientConfig, ITransactionOption } from \"../interfaces\";\n\nexport class ZkEVMWrapper extends BaseToken<IZkEvmClientConfig> {\n\n    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string) {\n        super({\n            address: address,\n            name: 'Zk<PERSON>VMWrapper',\n            bridgeType: 'zkevm',\n            isParent: true\n        }, client_);\n    }\n\n    method(methodName: string, ...args) {\n        return this.getContract().then(contract => {\n            return contract.method(methodName, ...args);\n        });\n    }\n\n    depositWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"deposit\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n    depositPermitWithGas(\n        tokenAddress: string,\n        depositAmount: string,\n        userAddress: string,\n        deadline: string,\n        v: number,\n        r: string,\n        s: string,\n        option?: ITransactionOption\n    ) {\n        return this.method(\n            \"deposit\",\n            tokenAddress,\n            depositAmount,\n            userAddress,\n            deadline,\n            v,\n            r,\n            s\n        ).then(method => {\n            return this.processWrite(method, option);\n        });\n    }\n\n}\n", "import { ERC20 } from \"./erc20\";\nimport { ZkEvmBridge } from \"./zkevm_bridge\";\nimport { BridgeUtil } from \"./bridge_util\";\nimport { ZkEvmBridgeClient } from \"../utils\";\nimport { IZkEvmClientConfig, IZkEvmContracts } from \"../interfaces\";\nimport { config as urlConfig } from \"../config\";\nimport { service, NetworkService } from \"../services\";\nimport { ZkEVMWrapper } from \"./zkevm_wrapper\";\n\nexport * from \"./zkevm_bridge\";\nexport * from \"./bridge_util\";\nexport * from \"./zkevm_wrapper\";\n\nexport class ZkEvmClient extends ZkEvmBridgeClient {\n\n    zkEVMWrapper: ZkEVMWrapper;\n\n    init(config: IZkEvmClientConfig) {\n        const client = this.client;\n\n        return client.init(config).then(_ => {\n            const mainZkEvmContracts = client.mainZkEvmContracts;\n            const zkEvmContracts = client.zkEvmContracts;\n            client.config = config = Object.assign(\n                {\n                    parentBridge: mainZkEvmContracts.PolygonZkEVMBridgeProxy,\n                    childBridge: zkEvmContracts.PolygonZkEVMBridge,\n                    zkEVMWrapper: mainZkEvmContracts.ZkEVMWrapper\n                } as IZkEvmClientConfig,\n                config\n            );\n\n            this.rootChainBridge = new ZkEvmBridge(\n                this.client,\n                config.parentBridge,\n                true\n            );\n\n            this.childChainBridge = new ZkEvmBridge(\n                this.client,\n                config.childBridge,\n                false\n            );\n\n            this.zkEVMWrapper = new ZkEVMWrapper(\n                this.client,\n                config.zkEVMWrapper\n            );\n\n            this.bridgeUtil = new BridgeUtil(\n                this.client\n            );\n\n            if (!service.zkEvmNetwork) {\n                if (urlConfig.zkEvmBridgeService[urlConfig.zkEvmBridgeService.length - 1] !== '/') {\n                    urlConfig.zkEvmBridgeService += '/';\n                }\n                urlConfig.zkEvmBridgeService += 'api/zkevm/';\n                service.zkEvmNetwork = new NetworkService(urlConfig.zkEvmBridgeService);\n            }\n\n            return this;\n        });\n    }\n\n    /**\n     * creates instance of ERC20 token\n     *\n     * @param {string} tokenAddress\n     * @param {boolean} isParent\n     *\n     * @param bridgeAdapterAddress Needed if a custom erc20 token is being bridged\n     * @returns\n     * @memberof ERC20\n     */\n    erc20(tokenAddress: string, isParent?: boolean, bridgeAdapterAddress?: string) {\n        return new ERC20(\n            tokenAddress,\n            isParent,\n            bridgeAdapterAddress,\n            this.client,\n            this.getContracts_.bind(this)\n        );\n    }\n\n    private getContracts_() {\n        return {\n            parentBridge: this.rootChainBridge,\n            childBridge: this.childChainBridge,\n            bridgeUtil: this.bridgeUtil,\n            zkEVMWrapper: this.zkEVMWrapper\n        } as IZkEvmContracts;\n    }\n}\n", "import { defaultExport } from \"./default\";\n\nexport * from \"./utils\";\nexport * from \"./enums\";\nexport * from \"./pos\";\nexport * from \"./interfaces\";\nexport * from \"./types\";\nexport * from \"./constant\";\nexport * from \"./abstracts\";\nexport * from \"./services\";\nexport * from \"./zkevm\";\n\nexport default defaultExport;\n"], "names": ["buffer", "<PERSON><PERSON><PERSON>", "copyProps", "src", "dst", "key", "SafeBuffer", "arg", "encodingOrOffset", "length", "from", "alloc", "allocUnsafe", "allocUnsafeSlow", "module", "exports", "TypeError", "size", "fill", "encoding", "buf", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "require", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "Log_Event_Signature", "ERROR_TYPE", "logger", "getRootHash", "startBlock", "endBlock", "this", "sendRPCRequest", "jsonrpc", "method", "params", "Number", "id", "Date", "getTime", "then", "payload", "String", "result", "getAccountsUsingRPC_", "address", "isBN", "throwNotImplemented", "toString", "base", "toNumber", "add", "sub", "mul", "div", "lte", "lt", "gte", "gt", "eq", "BaseBigNumber", "toHex", "amount", "dataType", "utils", "BN", "slice", "Error", "toBN", "parseInt", "use", "plugin", "pluginInstance", "setup", "defaultExport", "eventBusPromise", "executor", "promise", "Promise", "eventBus", "EventBus", "on", "bind", "emit", "ctx", "_events", "_ctx", "event", "cb", "push", "off", "index", "indexOf", "splice", "events", "all", "map", "args", "resolve", "destroy", "type", "info", "message", "getMsg_", "throw", "errMsg", "Allowed<PERSON>n<PERSON><PERSON><PERSON>", "AllowedOnRoot", "AllowedOnMainnet", "ProofAPINotSet", "BurnTxNotCheckPointed", "EIP1559NotSupported", "NullSpenderAddress", "Unknown", "enableLog", "isEnabled", "log", "console", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "merge", "assign", "runPromises", "promises", "converter", "maps", "val", "mapPromise", "values", "option", "valuesLength", "concurrency", "limitPromiseRun", "promiseResult", "concat", "promiseResolve", "number", "isSafeInteger", "bytes", "b", "lengths", "Uint8Array", "constructor", "name", "includes", "exists", "instance", "checkFinished", "destroyed", "finished", "output", "out", "min", "outputLen", "bool", "hash", "h", "create", "blockLen", "U32_MASK64", "BigInt", "_32n", "fromBig", "le", "l", "split", "lst", "Ah", "Uint32Array", "Al", "i", "isLE", "byteSwap32", "arr", "word", "toBytes", "data", "str", "TextEncoder", "encode", "utf8ToBytes", "Hash", "clone", "_cloneInto", "SHA3_PI", "SHA3_ROTL", "_SHA3_IOTA", "_0n", "_1n", "_2n", "_7n", "_256n", "_0x71n", "round", "R", "x", "y", "t", "j", "SHA3_IOTA_H", "SHA3_IOTA_L", "rotlH", "s", "rotlBH", "rotlSH", "rotlL", "rotlBL", "rotlSL", "Keccak", "suffix", "enableXOF", "rounds", "super", "pos", "posOut", "state", "state32", "byteOffset", "Math", "floor", "byteLength", "keccak", "B", "idx1", "idx0", "B0", "B1", "Th", "Tl", "curH", "curL", "shift", "PI", "keccakP", "update", "len", "take", "finish", "writeInto", "bufferOut", "set", "subarray", "xofInto", "xof", "digestInto", "digest", "to", "gen", "hashCons", "hashC", "msg", "tmp", "wrapConstructor", "keccak_224", "keccak_256", "keccak_384", "keccak_512", "wrapHash", "webCrypto", "globalThis", "crypto", "nodeRequire", "keccak224", "keccak256", "k", "keccak384", "keccak512", "assertIsBuffer", "input", "<PERSON><PERSON><PERSON><PERSON>", "bits", "sha3", "leaves", "depth", "ceil", "Array", "pow", "zeros", "layers", "createHashes", "nodes", "treeLevel", "left", "right", "getLeaves", "getLayers", "getRoot", "getProof", "leaf", "compare", "proof", "siblingIndex", "verify", "root", "isArray", "node", "padToEven", "isHexPrefixed", "isHexString", "match", "intToHex", "stripHexPrefix", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "intToBuffer", "hex", "<PERSON><PERSON><PERSON><PERSON>", "v", "isNeg", "toArrayLike", "toArray", "bufferToHex", "Permit", "getFastMerkleProof", "web3", "blockNumber", "<PERSON>rk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log2", "reversedProof", "targetIndex", "offset", "leftBound", "rightBound", "nLeaves", "pivotLeaf", "newLeftBound", "queryRootHash", "subTreeMerkleRoot", "newRightBound", "expectedHeight", "recursiveZeroHash", "subTreeHeight", "heightDifference", "remainingNodesHash", "MerkleTree", "reverse", "buildBlockProof", "maticWeb3", "ProofUtil", "p", "client", "rootHash", "catch", "_", "subHash", "encodeParameters", "getReceiptProof", "receipt", "block", "requestConcurrency", "receiptsVal", "receiptPromise", "stateSyncTxHash", "getStateSyncTxHash", "receiptsTrie", "transactions", "for<PERSON>ach", "tx", "transactionHash", "getTransactionReceipt", "receipts", "siblingReceipt", "path", "transactionIndex", "rawReceipt", "getReceiptBytes", "put", "<PERSON><PERSON><PERSON>", "remaining", "blockHash", "parentNodes", "stack", "raw", "getRawHeader", "receiptTrie", "isTypedReceipt", "hexType", "Converter", "status", "setLengthLeft", "encodedData", "cumulativeGasUsed", "logsBloom", "logs", "topics", "_block", "difficulty", "common", "Common", "chain", "Chain", "Mainnet", "hardfork", "Hardfork", "London", "BlockHeader", "fromHeaderData", "skipConsensusFormatValidation", "fetch", "baseUrl", "url", "query", "keys", "encodeURIComponent", "join", "headers", "res", "json", "post", "body", "JSON", "stringify", "chainIdToConfigPath", "<PERSON><PERSON>", "init", "config", "parent", "defaultConfig", "child", "Web3Client", "UnstoppableDomains", "resolution", "provider", "network", "version", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ABIManager", "err", "getABI", "getConfig", "isEIP1559Supported", "chainId", "promiseAny", "promisesArray", "promiseErrors", "counter", "reject", "MAX_AMOUNT", "ADDRESS_ZERO", "DAI_PERMIT_TYPEHASH", "EIP_2612_PERMIT_TYPEHASH", "EIP_2612_DOMAIN_TYPEHASH", "UNISWAP_DOMAIN_TYPEHASH", "_GLOBAL_INDEX_MAINNET_FLAG", "contractParam", "getContract", "contract_", "bridgeType", "abi", "getContract_", "isParent", "tokenAddress", "get<PERSON>hainId", "chainId_", "getClient", "processWrite", "validateTxOption_", "createTransactionConfig", "txConfig", "isWrite", "returnTransaction", "encodeABI", "write", "sendTransaction", "readTransaction", "read", "TransactionOptionNotObject", "processRead", "parentDefaultConfig", "childDefaultConfig", "clientChainId", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "isMaxFeeProvided", "gasLimit", "estimateGas", "trunc", "nonce", "getTransactionCount", "transferERC20", "contract", "transferERC721", "tokenId", "checkForNonNative", "methodName", "AllowedOnNonNativeTokens", "checkForRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkAdapterPresent", "bridgeAdapter<PERSON>dd<PERSON>", "BridgeAdapterNotFound", "transferERC1155", "param", "httpRequest", "HttpRequest", "contractName", "get<PERSON><PERSON><PERSON>", "abiStoreUrl", "zkEvmBridgeService", "createUrlForPos", "createUrlForZkEvm", "getBlockIncluded", "headerBlockNumber", "decimalHeaderBlockNumber", "getExitProof", "burnTxHash", "eventSignature", "start", "end", "getMerkleProofForZkEvm", "networkID", "depositCount", "getBridgeTransactionDetails", "deposit", "service", "ABIService", "setProofApi", "NetworkService", "setZkEvmProofApi", "zkEvmNetwork", "reduce", "prev", "curr", "Web3SideChainClient", "<PERSON><PERSON><PERSON><PERSON>Pointed", "txHash", "exitUtil", "isDeposited", "depositTxHash", "token", "BaseToken", "lastStateId", "targetLog", "find", "q", "rootStateId", "decodeParameters", "rootStateIdBN", "cache", "networkName", "targetBridgeABICache", "abiForContract", "setABI", "abiStore", "isDepositClaimable", "rootChainBridge", "bridgeUtil", "getBridgeLogData", "details", "ready_for_claim", "isWithdrawExitable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isClaimed", "isExited", "BaseWeb3Client", "EmptyBigNumber", "getPOSContracts", "root<PERSON><PERSON>n<PERSON><PERSON>", "gasSwapper", "getPredicateAddress", "predicateAddress", "tokenType", "typeToPredicateMethod", "isWithdrawn", "getExitHash", "exitHash", "isExitProcessed", "isWithdrawnOnIndex", "withdrawExitPOS", "isFast", "buildPayloadForExit", "exit", "getContracts", "getBalance", "userAddress", "getAllowance", "spender<PERSON><PERSON><PERSON>", "predicatePromise", "approve", "approveMax", "amountInABI", "depositWithGas", "swapEthAmount", "swapCallData", "depositEther_", "depositEtherWithGas_", "withdrawStart", "withdrawExit_", "burnTransactionHash", "burnEventSignature", "Erc20Transfer", "withdrawExit", "withdrawExitFaster", "isWithdrawExited", "transfer", "POSToken", "client_", "depositData", "exitPayload", "<PERSON><PERSON><PERSON><PERSON>", "maticClient_", "getLogIndex_", "logEventSig", "logIndex", "findIndex", "toLowerCase", "getAllLogIndices_", "logIndices", "getChainBlockInfo", "getLastChildBlock", "getTransaction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "txBlockNumber", "isCheckPointed_", "getRootBlockInfo", "rootBlockNumber", "findRootBlockFromChild", "rootBlockInfo", "getRootBlockInfoFromAPI", "headerBlock", "getBlockProof", "getBlockProofFromAPI", "blockProof", "getExitProofFromAPI", "burnHash", "exitProof", "blockInfo", "getBlockWithTransaction", "rootBlockInfoResult", "blockProofResult", "receiptProof", "encodePayload_", "timestamp", "transactionsRoot", "receiptsRoot", "buildMultiplePayloadsForExit", "payloads", "headerNumber", "receiptParentNodes", "blockResult", "nibbleArr", "byte", "etheriumSha3", "rootChainDefaultBlock", "childBlockNumber", "bigOne", "bigtwo", "checkPointInterval", "currentHeaderBlock", "ans", "mid", "headerStart", "headerEnd", "validateMany_", "tokenIds", "getTokensCount", "options", "count", "getTokenIdAtIndexForUser", "getAllTokens", "limit", "isApproved", "isApprovedAll", "approveAll", "depositMany", "tokensInHex", "withdrawStartWithMetaData", "withdrawStartMany", "Erc721Transfer", "withdrawExitOnIndex", "isWithdrawExitedMany", "Erc721BatchTransfer", "isWithdrawExitedOnIndex", "erc1155", "getAddress_", "addresses", "addressConfig", "approveAll_", "predicateAddressPromise", "approveAllForMintable", "amounts", "emptyHex", "amountsInHex", "Erc1155Transfer", "withdrawExitMany", "Erc1155BatchTransfer", "withdrawExitFasterMany", "depositAmount", "mainPOSContracts", "RootChainManagerProxy", "mainPlasmaContracts", "RootChainProxy", "GasSwapper", "RootChainManager", "Root<PERSON>hain", "ExitUtil", "erc20", "ERC20", "getContracts_", "erc721", "ERC721", "ERC1155", "depositEther", "depositEtherWithGas", "BridgeClient", "POSClient", "getZkEvmContracts", "parentBridge", "zkEVMWrapper", "child<PERSON><PERSON>", "bridgeToken", "recipient", "forceUpdateGlobalExitRoot", "bridgeAdapter", "ZkEVMBridgeAdapter", "getBridgeAddress", "contractAddress", "isEtherToken", "isApprovalNeeded", "getOriginTokenInfo", "tokenInfo", "permitData", "networkId", "bridgeAsset", "ethGasAmount", "depositPermitWithGas", "now", "getPermitSignatureParams_", "signatureParams", "depositWithPermit", "getPermitData", "depositCustomERC20", "customERC20DepositClaim", "buildPayloadForClaim", "claimMessage", "smtProof", "smtProofRollup", "globalIndex", "mainnetExitRoot", "rollupExitRoot", "originNetwork", "originTokenAddress", "destinationNetwork", "destinationAddress", "metadata", "depositClaim", "claimAsset", "withdraw", "withdrawCustomERC20", "customERC20WithdrawExit", "withdrawWithPermit", "get<PERSON><PERSON><PERSON>", "contractInstance", "permit<PERSON><PERSON><PERSON><PERSON>", "DAI", "DOMAIN_TYPEHASH", "EIP712DOMAIN_HASH", "domainTypehash", "EIP_2612", "UNISWAP", "getTypedData_", "permitType", "account", "typedData", "types", "EIP712Domain", "primaryType", "domain", "verifyingContract", "holder", "spender", "expiry", "allowed", "owner", "deadline", "getSignatureParameters_", "signature", "hexToNumber", "encodePermitFunctionData_", "getAccounts", "nameMethod", "nonceMethod", "signTypedData", "getPermitData_", "signatureParameters", "ZkEvmToken", "bridgeMessage", "getMappedTokenInfo", "sourceBridgeNetwork", "precalculatedMappedTokenInfo", "wrappedToken", "networkID_", "BRIDGE_TOPIC", "decodedBridgeData_", "filter", "decodedData", "inputs", "leafType", "getBridgeLogData_", "getProof_", "computeGlobalIndex", "indexLocal", "indexRollup", "sourceNetworkId", "merkle_proof", "rollup_merkle_proof", "main_exit_root", "rollup_exit_root", "mainZkEvmContracts", "zkEvmContracts", "PolygonZkEVMBridgeProxy", "PolygonZkEVMBridge", "ZkEVMWrapper", "ZkEvmBridge", "BridgeUtil", "ZkEvmBridgeClient"], "sourceRoot": ""}