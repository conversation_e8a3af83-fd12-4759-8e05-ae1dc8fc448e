#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ArthSwap交易脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

import argparse
import time
from src.dex.astar.arthswap.client import ArthSwapClient
from config.config import config  # 直接导入config对象


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='ArthSwap DEX交易')
    parser.add_argument('token_in', help='输入代币符号，例如ASTR, WASTR, USDT, USDC')
    parser.add_argument('token_out', help='输出代币符号，例如ASTR, WASTR, USDT, USDC')
    parser.add_argument('amount', type=float, help='输入代币数量')
    parser.add_argument('-s', '--slippage', type=float, default=0.1, help='滑点容忍度，百分比，默认为0.1')
    parser.add_argument('-g', '--gas-multiplier', type=float, default=1.0, help='gas价格倍数，默认1.0')
    parser.add_argument('-p', '--preview', action='store_true', help='仅预览交易，不实际执行')
    parser.add_argument('-w', '--no-wait', action='store_true', help='不等待交易确认')
    parser.add_argument('--auto-confirm', action='store_true', help='自动确认交易')
    parser.add_argument('--expected-profit', type=float, help='预期利润（USDT）')
    
    args = parser.parse_args()
    
    # 检查金额
    if args.amount <= 0:
        print("错误: 金额必须大于0")
        return 1
    
    # 检查gas倍数
    if args.gas_multiplier <= 0:
        print("错误: gas倍数必须大于0")
        return 1
    
    # 不需要初始化配置，直接使用已加载的config对象
    
    try:
        # 创建ArthSwap客户端
        client = ArthSwapClient()
        
        # 设置gas价格倍数
        if args.gas_multiplier != 1.0:
            client.wallet.set_gas_price_multiplier(args.gas_multiplier)
            print(f"已将gas价格设置为默认值的 {args.gas_multiplier} 倍")
        
        # 大写代币符号
        token_in = args.token_in.upper()
        token_out = args.token_out.upper()
        
        # 显示当前余额
        token_in_balance = client.wallet.get_token_balance(token_in)
        token_out_balance = client.wallet.get_token_balance(token_out)
        
        print(f"\n钱包地址: {client.address}")
        print("=" * 80)
        print("当前余额:")
        print(f"{token_in}: {token_in_balance:.6f}")
        print(f"{token_out}: {token_out_balance:.6f}")
        print("-" * 80)
        
        # 检查余额是否足够
        if token_in_balance < args.amount:
            print(f"错误: {token_in}余额不足。当前余额: {token_in_balance}, 需要: {args.amount}")
            return 1
        
        # 获取交易预览
        try:
            preview = client.get_expected_output(token_in, token_out, args.amount)
            
            print("交易预览:")
            print(f"输入: {preview['amount_in']} {preview['token_in']}")
            print(f"输出: {preview['amount_out']:.6f} {preview['token_out']}")
            print(f"价格影响: {preview['price_impact']:.2f}%")
            print(f"汇率: 1 {preview['token_in']} = {preview['rate']:.6f} {preview['token_out']}")
            print(f"滑点容忍度: {args.slippage}%")
            
            # 计算最小输出金额
            min_amount_out = preview['amount_out'] * (1 - args.slippage / 100)
            print(f"最小输出: {min_amount_out:.6f} {preview['token_out']}")
            
            # 计算gas费用（约1.6 ASTR）
            gas_fee_astr = 1.6
            
            # 如果是自动确认模式，计算实际利润
            if args.auto_confirm and args.expected_profit is not None:
                # 获取ASTR当前价格（用于计算gas费用的USDT价值）
                if token_in == 'ASTR' or token_out == 'ASTR':
                    astr_price = preview['rate'] if token_in == 'ASTR' else 1/preview['rate']
                else:
                    # 如果交易对不包含ASTR，需要额外查询ASTR价格
                    astr_preview = client.get_expected_output('ASTR', 'USDT', 1)
                    astr_price = astr_preview['rate']
                
                                # 计算gas费用（USDT）
                gas_fee_usdt = gas_fee_astr * astr_price
                print(f"预估gas费用: {gas_fee_usdt:.6f} USDT")
                
                # 计算实际利润（考虑gas费用）
                actual_profit = args.expected_profit - gas_fee_usdt
                print(f"预期利润: {args.expected_profit:.6f} USDT")
                print(f"实际利润(扣除gas): {actual_profit:.6f} USDT")
                
                # 如果实际利润小于等于0，取消交易
                if actual_profit <= 0:
                    print("实际利润不足，取消交易")
                    return 0
                
                print("利润满足条件，自动确认交易")
                should_proceed = True
            else:
                # 如果只是预览，则退出
                if args.preview:
                    print("\n仅预览模式，未执行实际交易")
                    return 0
                
                # 确认交易
                print("-" * 80)
                confirmation = input("确认执行此交易? [y/N]: ")
                should_proceed = confirmation.lower() == 'y'
            
            if not should_proceed:
                print("交易已取消")
                return 0
            
        except Exception as e:
            print(f"获取交易预览失败: {str(e)}")
            return 1
        
        # 执行交易
        print("\n执行交易...")
        result = client.swap_tokens(
            token_in=token_in,
            token_out=token_out,
            amount_in=args.amount,
            slippage=args.slippage,
            wait_for_confirmation=not args.no_wait
        )
        
        # 显示交易结果
        print("-" * 80)
        print(f"交易状态: {result['status']}")
        print(f"交易哈希: {result['tx_hash']}")
        
        # 如果等待确认，显示余额变化
        if not args.no_wait and result['status'] == 'success':
            # 等待一下，确保余额更新
            time.sleep(2)
            
            # 获取更新后的余额
            new_token_in_balance = client.wallet.get_token_balance(token_in)
            new_token_out_balance = client.wallet.get_token_balance(token_out)
            
            print("\n交易后余额:")
            print(f"{token_in}: {new_token_in_balance:.6f} ({new_token_in_balance - token_in_balance:+.6f})")
            print(f"{token_out}: {new_token_out_balance:.6f} ({new_token_out_balance - token_out_balance:+.6f})")
        
        print("=" * 80)
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return 1
        
    return 0


if __name__ == "__main__":
    sys.exit(main()) 