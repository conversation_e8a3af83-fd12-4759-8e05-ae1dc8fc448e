import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { EpochAndRound, FeedConfig, Params, Transmission } from "./ocr";
export declare const protobufPackage = "injective.ocr.v1beta1";
/** GenesisState defines the OCR module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to OCR. */
    params: Params | undefined;
    /** feed_configs stores all of the supported OCR feeds */
    feedConfigs: FeedConfig[];
    /** latest_epoch_and_rounds stores the latest epoch and round for each feedId */
    latestEpochAndRounds: FeedEpochAndRound[];
    /** feed_transmissions stores the last transmission for each feed */
    feedTransmissions: FeedTransmission[];
    /**
     * latest_aggregator_round_ids stores the latest aggregator round ID for each
     * feedId
     */
    latestAggregatorRoundIds: FeedLatestAggregatorRoundIDs[];
    /** reward_pools stores the reward pools */
    rewardPools: RewardPool[];
    /** feed_observation_counts stores the feed observation counts */
    feedObservationCounts: FeedCounts[];
    /** feed_transmission_counts stores the feed transmission counts */
    feedTransmissionCounts: FeedCounts[];
    /** pending_payeeships stores the pending payeeships */
    pendingPayeeships: PendingPayeeship[];
}
export interface FeedTransmission {
    feedId: string;
    transmission: Transmission | undefined;
}
export interface FeedEpochAndRound {
    feedId: string;
    epochAndRound: EpochAndRound | undefined;
}
export interface FeedLatestAggregatorRoundIDs {
    feedId: string;
    aggregatorRoundId: string;
}
export interface RewardPool {
    feedId: string;
    amount: Coin | undefined;
}
export interface FeedCounts {
    feedId: string;
    counts: Count[];
}
export interface Count {
    address: string;
    count: string;
}
export interface PendingPayeeship {
    feedId: string;
    transmitter: string;
    proposedPayee: string;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
export declare const FeedTransmission: {
    encode(message: FeedTransmission, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedTransmission;
    fromJSON(object: any): FeedTransmission;
    toJSON(message: FeedTransmission): unknown;
    create(base?: DeepPartial<FeedTransmission>): FeedTransmission;
    fromPartial(object: DeepPartial<FeedTransmission>): FeedTransmission;
};
export declare const FeedEpochAndRound: {
    encode(message: FeedEpochAndRound, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedEpochAndRound;
    fromJSON(object: any): FeedEpochAndRound;
    toJSON(message: FeedEpochAndRound): unknown;
    create(base?: DeepPartial<FeedEpochAndRound>): FeedEpochAndRound;
    fromPartial(object: DeepPartial<FeedEpochAndRound>): FeedEpochAndRound;
};
export declare const FeedLatestAggregatorRoundIDs: {
    encode(message: FeedLatestAggregatorRoundIDs, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedLatestAggregatorRoundIDs;
    fromJSON(object: any): FeedLatestAggregatorRoundIDs;
    toJSON(message: FeedLatestAggregatorRoundIDs): unknown;
    create(base?: DeepPartial<FeedLatestAggregatorRoundIDs>): FeedLatestAggregatorRoundIDs;
    fromPartial(object: DeepPartial<FeedLatestAggregatorRoundIDs>): FeedLatestAggregatorRoundIDs;
};
export declare const RewardPool: {
    encode(message: RewardPool, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RewardPool;
    fromJSON(object: any): RewardPool;
    toJSON(message: RewardPool): unknown;
    create(base?: DeepPartial<RewardPool>): RewardPool;
    fromPartial(object: DeepPartial<RewardPool>): RewardPool;
};
export declare const FeedCounts: {
    encode(message: FeedCounts, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeedCounts;
    fromJSON(object: any): FeedCounts;
    toJSON(message: FeedCounts): unknown;
    create(base?: DeepPartial<FeedCounts>): FeedCounts;
    fromPartial(object: DeepPartial<FeedCounts>): FeedCounts;
};
export declare const Count: {
    encode(message: Count, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Count;
    fromJSON(object: any): Count;
    toJSON(message: Count): unknown;
    create(base?: DeepPartial<Count>): Count;
    fromPartial(object: DeepPartial<Count>): Count;
};
export declare const PendingPayeeship: {
    encode(message: PendingPayeeship, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PendingPayeeship;
    fromJSON(object: any): PendingPayeeship;
    toJSON(message: PendingPayeeship): unknown;
    create(base?: DeepPartial<PendingPayeeship>): PendingPayeeship;
    fromPartial(object: DeepPartial<PendingPayeeship>): PendingPayeeship;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
