"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectivePortfolioRPCStreamAccountPortfolioDesc = exports.InjectivePortfolioRPCAccountPortfolioBalancesDesc = exports.InjectivePortfolioRPCAccountPortfolioDesc = exports.InjectivePortfolioRPCTokenHoldersDesc = exports.InjectivePortfolioRPCDesc = exports.InjectivePortfolioRPCClientImpl = exports.StreamAccountPortfolioResponse = exports.StreamAccountPortfolioRequest = exports.PortfolioBalances = exports.AccountPortfolioBalancesResponse = exports.AccountPortfolioBalancesRequest = exports.DerivativePosition = exports.PositionsWithUPNL = exports.SubaccountDeposit = exports.SubaccountBalanceV2 = exports.Coin = exports.Portfolio = exports.AccountPortfolioResponse = exports.AccountPortfolioRequest = exports.Holder = exports.TokenHoldersResponse = exports.TokenHoldersRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const long_1 = __importDefault(require("long"));
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
exports.protobufPackage = "injective_portfolio_rpc";
function createBaseTokenHoldersRequest() {
    return { denom: "", cursor: "", limit: 0 };
}
exports.TokenHoldersRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.cursor !== "") {
            writer.uint32(18).string(message.cursor);
        }
        if (message.limit !== 0) {
            writer.uint32(24).sint32(message.limit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTokenHoldersRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.cursor = reader.string();
                    break;
                case 3:
                    message.limit = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            cursor: isSet(object.cursor) ? String(object.cursor) : "",
            limit: isSet(object.limit) ? Number(object.limit) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.cursor !== undefined && (obj.cursor = message.cursor);
        message.limit !== undefined && (obj.limit = Math.round(message.limit));
        return obj;
    },
    create(base) {
        return exports.TokenHoldersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseTokenHoldersRequest();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.cursor = (_b = object.cursor) !== null && _b !== void 0 ? _b : "";
        message.limit = (_c = object.limit) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseTokenHoldersResponse() {
    return { holders: [], nextCursors: [] };
}
exports.TokenHoldersResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.holders) {
            exports.Holder.encode(v, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.nextCursors) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTokenHoldersResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.holders.push(exports.Holder.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.nextCursors.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            holders: Array.isArray(object === null || object === void 0 ? void 0 : object.holders) ? object.holders.map((e) => exports.Holder.fromJSON(e)) : [],
            nextCursors: Array.isArray(object === null || object === void 0 ? void 0 : object.nextCursors) ? object.nextCursors.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.holders) {
            obj.holders = message.holders.map((e) => e ? exports.Holder.toJSON(e) : undefined);
        }
        else {
            obj.holders = [];
        }
        if (message.nextCursors) {
            obj.nextCursors = message.nextCursors.map((e) => e);
        }
        else {
            obj.nextCursors = [];
        }
        return obj;
    },
    create(base) {
        return exports.TokenHoldersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseTokenHoldersResponse();
        message.holders = ((_a = object.holders) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Holder.fromPartial(e))) || [];
        message.nextCursors = ((_b = object.nextCursors) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        return message;
    },
};
function createBaseHolder() {
    return { accountAddress: "", balance: "" };
}
exports.Holder = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.balance !== "") {
            writer.uint32(18).string(message.balance);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHolder();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.balance = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            balance: isSet(object.balance) ? String(object.balance) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.balance !== undefined && (obj.balance = message.balance);
        return obj;
    },
    create(base) {
        return exports.Holder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseHolder();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.balance = (_b = object.balance) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseAccountPortfolioRequest() {
    return { accountAddress: "" };
}
exports.AccountPortfolioRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAccountPortfolioRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create(base) {
        return exports.AccountPortfolioRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAccountPortfolioRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseAccountPortfolioResponse() {
    return { portfolio: undefined };
}
exports.AccountPortfolioResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.portfolio !== undefined) {
            exports.Portfolio.encode(message.portfolio, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAccountPortfolioResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.portfolio = exports.Portfolio.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { portfolio: isSet(object.portfolio) ? exports.Portfolio.fromJSON(object.portfolio) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.portfolio !== undefined &&
            (obj.portfolio = message.portfolio ? exports.Portfolio.toJSON(message.portfolio) : undefined);
        return obj;
    },
    create(base) {
        return exports.AccountPortfolioResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseAccountPortfolioResponse();
        message.portfolio = (object.portfolio !== undefined && object.portfolio !== null)
            ? exports.Portfolio.fromPartial(object.portfolio)
            : undefined;
        return message;
    },
};
function createBasePortfolio() {
    return { accountAddress: "", bankBalances: [], subaccounts: [], positionsWithUpnl: [] };
}
exports.Portfolio = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        for (const v of message.bankBalances) {
            exports.Coin.encode(v, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.subaccounts) {
            exports.SubaccountBalanceV2.encode(v, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.positionsWithUpnl) {
            exports.PositionsWithUPNL.encode(v, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePortfolio();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.bankBalances.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.subaccounts.push(exports.SubaccountBalanceV2.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.positionsWithUpnl.push(exports.PositionsWithUPNL.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            bankBalances: Array.isArray(object === null || object === void 0 ? void 0 : object.bankBalances) ? object.bankBalances.map((e) => exports.Coin.fromJSON(e)) : [],
            subaccounts: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccounts)
                ? object.subaccounts.map((e) => exports.SubaccountBalanceV2.fromJSON(e))
                : [],
            positionsWithUpnl: Array.isArray(object === null || object === void 0 ? void 0 : object.positionsWithUpnl)
                ? object.positionsWithUpnl.map((e) => exports.PositionsWithUPNL.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        if (message.bankBalances) {
            obj.bankBalances = message.bankBalances.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.bankBalances = [];
        }
        if (message.subaccounts) {
            obj.subaccounts = message.subaccounts.map((e) => e ? exports.SubaccountBalanceV2.toJSON(e) : undefined);
        }
        else {
            obj.subaccounts = [];
        }
        if (message.positionsWithUpnl) {
            obj.positionsWithUpnl = message.positionsWithUpnl.map((e) => e ? exports.PositionsWithUPNL.toJSON(e) : undefined);
        }
        else {
            obj.positionsWithUpnl = [];
        }
        return obj;
    },
    create(base) {
        return exports.Portfolio.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePortfolio();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.bankBalances = ((_b = object.bankBalances) === null || _b === void 0 ? void 0 : _b.map((e) => exports.Coin.fromPartial(e))) || [];
        message.subaccounts = ((_c = object.subaccounts) === null || _c === void 0 ? void 0 : _c.map((e) => exports.SubaccountBalanceV2.fromPartial(e))) || [];
        message.positionsWithUpnl = ((_d = object.positionsWithUpnl) === null || _d === void 0 ? void 0 : _d.map((e) => exports.PositionsWithUPNL.fromPartial(e))) || [];
        return message;
    },
};
function createBaseCoin() {
    return { denom: "", amount: "", usdValue: "" };
}
exports.Coin = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        if (message.usdValue !== "") {
            writer.uint32(26).string(message.usdValue);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCoin();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                case 3:
                    message.usdValue = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            usdValue: isSet(object.usdValue) ? String(object.usdValue) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.amount !== undefined && (obj.amount = message.amount);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        return obj;
    },
    create(base) {
        return exports.Coin.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseCoin();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        message.usdValue = (_c = object.usdValue) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseSubaccountBalanceV2() {
    return { subaccountId: "", denom: "", deposit: undefined };
}
exports.SubaccountBalanceV2 = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.deposit !== undefined) {
            exports.SubaccountDeposit.encode(message.deposit, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubaccountBalanceV2();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.deposit = exports.SubaccountDeposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            deposit: isSet(object.deposit) ? exports.SubaccountDeposit.fromJSON(object.deposit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.denom !== undefined && (obj.denom = message.denom);
        message.deposit !== undefined &&
            (obj.deposit = message.deposit ? exports.SubaccountDeposit.toJSON(message.deposit) : undefined);
        return obj;
    },
    create(base) {
        return exports.SubaccountBalanceV2.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseSubaccountBalanceV2();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? exports.SubaccountDeposit.fromPartial(object.deposit)
            : undefined;
        return message;
    },
};
function createBaseSubaccountDeposit() {
    return { totalBalance: "", availableBalance: "", totalBalanceUsd: "", availableBalanceUsd: "" };
}
exports.SubaccountDeposit = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.totalBalance !== "") {
            writer.uint32(10).string(message.totalBalance);
        }
        if (message.availableBalance !== "") {
            writer.uint32(18).string(message.availableBalance);
        }
        if (message.totalBalanceUsd !== "") {
            writer.uint32(26).string(message.totalBalanceUsd);
        }
        if (message.availableBalanceUsd !== "") {
            writer.uint32(34).string(message.availableBalanceUsd);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubaccountDeposit();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.totalBalance = reader.string();
                    break;
                case 2:
                    message.availableBalance = reader.string();
                    break;
                case 3:
                    message.totalBalanceUsd = reader.string();
                    break;
                case 4:
                    message.availableBalanceUsd = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            totalBalance: isSet(object.totalBalance) ? String(object.totalBalance) : "",
            availableBalance: isSet(object.availableBalance) ? String(object.availableBalance) : "",
            totalBalanceUsd: isSet(object.totalBalanceUsd) ? String(object.totalBalanceUsd) : "",
            availableBalanceUsd: isSet(object.availableBalanceUsd) ? String(object.availableBalanceUsd) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.totalBalance !== undefined && (obj.totalBalance = message.totalBalance);
        message.availableBalance !== undefined && (obj.availableBalance = message.availableBalance);
        message.totalBalanceUsd !== undefined && (obj.totalBalanceUsd = message.totalBalanceUsd);
        message.availableBalanceUsd !== undefined && (obj.availableBalanceUsd = message.availableBalanceUsd);
        return obj;
    },
    create(base) {
        return exports.SubaccountDeposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseSubaccountDeposit();
        message.totalBalance = (_a = object.totalBalance) !== null && _a !== void 0 ? _a : "";
        message.availableBalance = (_b = object.availableBalance) !== null && _b !== void 0 ? _b : "";
        message.totalBalanceUsd = (_c = object.totalBalanceUsd) !== null && _c !== void 0 ? _c : "";
        message.availableBalanceUsd = (_d = object.availableBalanceUsd) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBasePositionsWithUPNL() {
    return { position: undefined, unrealizedPnl: "" };
}
exports.PositionsWithUPNL = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.position !== undefined) {
            exports.DerivativePosition.encode(message.position, writer.uint32(10).fork()).ldelim();
        }
        if (message.unrealizedPnl !== "") {
            writer.uint32(18).string(message.unrealizedPnl);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePositionsWithUPNL();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.position = exports.DerivativePosition.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.unrealizedPnl = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            position: isSet(object.position) ? exports.DerivativePosition.fromJSON(object.position) : undefined,
            unrealizedPnl: isSet(object.unrealizedPnl) ? String(object.unrealizedPnl) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.position !== undefined &&
            (obj.position = message.position ? exports.DerivativePosition.toJSON(message.position) : undefined);
        message.unrealizedPnl !== undefined && (obj.unrealizedPnl = message.unrealizedPnl);
        return obj;
    },
    create(base) {
        return exports.PositionsWithUPNL.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePositionsWithUPNL();
        message.position = (object.position !== undefined && object.position !== null)
            ? exports.DerivativePosition.fromPartial(object.position)
            : undefined;
        message.unrealizedPnl = (_a = object.unrealizedPnl) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseDerivativePosition() {
    return {
        ticker: "",
        marketId: "",
        subaccountId: "",
        direction: "",
        quantity: "",
        entryPrice: "",
        margin: "",
        liquidationPrice: "",
        markPrice: "",
        aggregateReduceOnlyQuantity: "",
        updatedAt: "0",
        createdAt: "0",
    };
}
exports.DerivativePosition = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.ticker !== "") {
            writer.uint32(10).string(message.ticker);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(26).string(message.subaccountId);
        }
        if (message.direction !== "") {
            writer.uint32(34).string(message.direction);
        }
        if (message.quantity !== "") {
            writer.uint32(42).string(message.quantity);
        }
        if (message.entryPrice !== "") {
            writer.uint32(50).string(message.entryPrice);
        }
        if (message.margin !== "") {
            writer.uint32(58).string(message.margin);
        }
        if (message.liquidationPrice !== "") {
            writer.uint32(66).string(message.liquidationPrice);
        }
        if (message.markPrice !== "") {
            writer.uint32(74).string(message.markPrice);
        }
        if (message.aggregateReduceOnlyQuantity !== "") {
            writer.uint32(90).string(message.aggregateReduceOnlyQuantity);
        }
        if (message.updatedAt !== "0") {
            writer.uint32(96).sint64(message.updatedAt);
        }
        if (message.createdAt !== "0") {
            writer.uint32(104).sint64(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativePosition();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ticker = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.subaccountId = reader.string();
                    break;
                case 4:
                    message.direction = reader.string();
                    break;
                case 5:
                    message.quantity = reader.string();
                    break;
                case 6:
                    message.entryPrice = reader.string();
                    break;
                case 7:
                    message.margin = reader.string();
                    break;
                case 8:
                    message.liquidationPrice = reader.string();
                    break;
                case 9:
                    message.markPrice = reader.string();
                    break;
                case 11:
                    message.aggregateReduceOnlyQuantity = reader.string();
                    break;
                case 12:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                case 13:
                    message.createdAt = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            direction: isSet(object.direction) ? String(object.direction) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            entryPrice: isSet(object.entryPrice) ? String(object.entryPrice) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            liquidationPrice: isSet(object.liquidationPrice) ? String(object.liquidationPrice) : "",
            markPrice: isSet(object.markPrice) ? String(object.markPrice) : "",
            aggregateReduceOnlyQuantity: isSet(object.aggregateReduceOnlyQuantity)
                ? String(object.aggregateReduceOnlyQuantity)
                : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.direction !== undefined && (obj.direction = message.direction);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.entryPrice !== undefined && (obj.entryPrice = message.entryPrice);
        message.margin !== undefined && (obj.margin = message.margin);
        message.liquidationPrice !== undefined && (obj.liquidationPrice = message.liquidationPrice);
        message.markPrice !== undefined && (obj.markPrice = message.markPrice);
        message.aggregateReduceOnlyQuantity !== undefined &&
            (obj.aggregateReduceOnlyQuantity = message.aggregateReduceOnlyQuantity);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return exports.DerivativePosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        const message = createBaseDerivativePosition();
        message.ticker = (_a = object.ticker) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.subaccountId = (_c = object.subaccountId) !== null && _c !== void 0 ? _c : "";
        message.direction = (_d = object.direction) !== null && _d !== void 0 ? _d : "";
        message.quantity = (_e = object.quantity) !== null && _e !== void 0 ? _e : "";
        message.entryPrice = (_f = object.entryPrice) !== null && _f !== void 0 ? _f : "";
        message.margin = (_g = object.margin) !== null && _g !== void 0 ? _g : "";
        message.liquidationPrice = (_h = object.liquidationPrice) !== null && _h !== void 0 ? _h : "";
        message.markPrice = (_j = object.markPrice) !== null && _j !== void 0 ? _j : "";
        message.aggregateReduceOnlyQuantity = (_k = object.aggregateReduceOnlyQuantity) !== null && _k !== void 0 ? _k : "";
        message.updatedAt = (_l = object.updatedAt) !== null && _l !== void 0 ? _l : "0";
        message.createdAt = (_m = object.createdAt) !== null && _m !== void 0 ? _m : "0";
        return message;
    },
};
function createBaseAccountPortfolioBalancesRequest() {
    return { accountAddress: "", usd: false };
}
exports.AccountPortfolioBalancesRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.usd === true) {
            writer.uint32(16).bool(message.usd);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAccountPortfolioBalancesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.usd = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            usd: isSet(object.usd) ? Boolean(object.usd) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.usd !== undefined && (obj.usd = message.usd);
        return obj;
    },
    create(base) {
        return exports.AccountPortfolioBalancesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseAccountPortfolioBalancesRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.usd = (_b = object.usd) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseAccountPortfolioBalancesResponse() {
    return { portfolio: undefined };
}
exports.AccountPortfolioBalancesResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.portfolio !== undefined) {
            exports.PortfolioBalances.encode(message.portfolio, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAccountPortfolioBalancesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.portfolio = exports.PortfolioBalances.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { portfolio: isSet(object.portfolio) ? exports.PortfolioBalances.fromJSON(object.portfolio) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.portfolio !== undefined &&
            (obj.portfolio = message.portfolio ? exports.PortfolioBalances.toJSON(message.portfolio) : undefined);
        return obj;
    },
    create(base) {
        return exports.AccountPortfolioBalancesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        const message = createBaseAccountPortfolioBalancesResponse();
        message.portfolio = (object.portfolio !== undefined && object.portfolio !== null)
            ? exports.PortfolioBalances.fromPartial(object.portfolio)
            : undefined;
        return message;
    },
};
function createBasePortfolioBalances() {
    return { accountAddress: "", bankBalances: [], subaccounts: [], totalUsd: "" };
}
exports.PortfolioBalances = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        for (const v of message.bankBalances) {
            exports.Coin.encode(v, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.subaccounts) {
            exports.SubaccountBalanceV2.encode(v, writer.uint32(26).fork()).ldelim();
        }
        if (message.totalUsd !== "") {
            writer.uint32(34).string(message.totalUsd);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePortfolioBalances();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.bankBalances.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.subaccounts.push(exports.SubaccountBalanceV2.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.totalUsd = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            bankBalances: Array.isArray(object === null || object === void 0 ? void 0 : object.bankBalances) ? object.bankBalances.map((e) => exports.Coin.fromJSON(e)) : [],
            subaccounts: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccounts)
                ? object.subaccounts.map((e) => exports.SubaccountBalanceV2.fromJSON(e))
                : [],
            totalUsd: isSet(object.totalUsd) ? String(object.totalUsd) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        if (message.bankBalances) {
            obj.bankBalances = message.bankBalances.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.bankBalances = [];
        }
        if (message.subaccounts) {
            obj.subaccounts = message.subaccounts.map((e) => e ? exports.SubaccountBalanceV2.toJSON(e) : undefined);
        }
        else {
            obj.subaccounts = [];
        }
        message.totalUsd !== undefined && (obj.totalUsd = message.totalUsd);
        return obj;
    },
    create(base) {
        return exports.PortfolioBalances.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePortfolioBalances();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.bankBalances = ((_b = object.bankBalances) === null || _b === void 0 ? void 0 : _b.map((e) => exports.Coin.fromPartial(e))) || [];
        message.subaccounts = ((_c = object.subaccounts) === null || _c === void 0 ? void 0 : _c.map((e) => exports.SubaccountBalanceV2.fromPartial(e))) || [];
        message.totalUsd = (_d = object.totalUsd) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseStreamAccountPortfolioRequest() {
    return { accountAddress: "", subaccountId: "", type: "" };
}
exports.StreamAccountPortfolioRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.type !== "") {
            writer.uint32(26).string(message.type);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamAccountPortfolioRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.type = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            type: isSet(object.type) ? String(object.type) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.type !== undefined && (obj.type = message.type);
        return obj;
    },
    create(base) {
        return exports.StreamAccountPortfolioRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseStreamAccountPortfolioRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.type = (_c = object.type) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseStreamAccountPortfolioResponse() {
    return { type: "", denom: "", amount: "", subaccountId: "", timestamp: "0" };
}
exports.StreamAccountPortfolioResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.amount !== "") {
            writer.uint32(26).string(message.amount);
        }
        if (message.subaccountId !== "") {
            writer.uint32(34).string(message.subaccountId);
        }
        if (message.timestamp !== "0") {
            writer.uint32(40).sint64(message.timestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamAccountPortfolioResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.amount = reader.string();
                    break;
                case 4:
                    message.subaccountId = reader.string();
                    break;
                case 5:
                    message.timestamp = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? String(object.type) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = message.type);
        message.denom !== undefined && (obj.denom = message.denom);
        message.amount !== undefined && (obj.amount = message.amount);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create(base) {
        return exports.StreamAccountPortfolioResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseStreamAccountPortfolioResponse();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.amount = (_c = object.amount) !== null && _c !== void 0 ? _c : "";
        message.subaccountId = (_d = object.subaccountId) !== null && _d !== void 0 ? _d : "";
        message.timestamp = (_e = object.timestamp) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
class InjectivePortfolioRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.TokenHolders = this.TokenHolders.bind(this);
        this.AccountPortfolio = this.AccountPortfolio.bind(this);
        this.AccountPortfolioBalances = this.AccountPortfolioBalances.bind(this);
        this.StreamAccountPortfolio = this.StreamAccountPortfolio.bind(this);
    }
    TokenHolders(request, metadata) {
        return this.rpc.unary(exports.InjectivePortfolioRPCTokenHoldersDesc, exports.TokenHoldersRequest.fromPartial(request), metadata);
    }
    AccountPortfolio(request, metadata) {
        return this.rpc.unary(exports.InjectivePortfolioRPCAccountPortfolioDesc, exports.AccountPortfolioRequest.fromPartial(request), metadata);
    }
    AccountPortfolioBalances(request, metadata) {
        return this.rpc.unary(exports.InjectivePortfolioRPCAccountPortfolioBalancesDesc, exports.AccountPortfolioBalancesRequest.fromPartial(request), metadata);
    }
    StreamAccountPortfolio(request, metadata) {
        return this.rpc.invoke(exports.InjectivePortfolioRPCStreamAccountPortfolioDesc, exports.StreamAccountPortfolioRequest.fromPartial(request), metadata);
    }
}
exports.InjectivePortfolioRPCClientImpl = InjectivePortfolioRPCClientImpl;
exports.InjectivePortfolioRPCDesc = { serviceName: "injective_portfolio_rpc.InjectivePortfolioRPC" };
exports.InjectivePortfolioRPCTokenHoldersDesc = {
    methodName: "TokenHolders",
    service: exports.InjectivePortfolioRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.TokenHoldersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.TokenHoldersResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectivePortfolioRPCAccountPortfolioDesc = {
    methodName: "AccountPortfolio",
    service: exports.InjectivePortfolioRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AccountPortfolioRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AccountPortfolioResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectivePortfolioRPCAccountPortfolioBalancesDesc = {
    methodName: "AccountPortfolioBalances",
    service: exports.InjectivePortfolioRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AccountPortfolioBalancesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AccountPortfolioBalancesResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectivePortfolioRPCStreamAccountPortfolioDesc = {
    methodName: "StreamAccountPortfolio",
    service: exports.InjectivePortfolioRPCDesc,
    requestStream: false,
    responseStream: true,
    requestType: {
        serializeBinary() {
            return exports.StreamAccountPortfolioRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.StreamAccountPortfolioResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
    invoke(methodDesc, _request, metadata) {
        var _a;
        const upStreamCodes = this.options.upStreamRetryCodes || [];
        const DEFAULT_TIMEOUT_TIME = 3000;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new rxjs_1.Observable((observer) => {
            const upStream = (() => {
                const client = grpc_web_1.grpc.invoke(methodDesc, {
                    host: this.host,
                    request,
                    transport: this.options.streamingTransport || this.options.transport,
                    metadata: maybeCombinedMetadata,
                    debug: this.options.debug,
                    onMessage: (next) => observer.next(next),
                    onEnd: (code, message, trailers) => {
                        if (code === 0) {
                            observer.complete();
                        }
                        else if (upStreamCodes.includes(code)) {
                            setTimeout(upStream, DEFAULT_TIMEOUT_TIME);
                        }
                        else {
                            const err = new Error(message);
                            err.code = code;
                            err.metadata = trailers;
                            observer.error(err);
                        }
                    },
                });
                observer.add(() => {
                    if (!observer.closed) {
                        return client.close();
                    }
                });
            });
            upStream();
        }).pipe((0, operators_1.share)());
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
