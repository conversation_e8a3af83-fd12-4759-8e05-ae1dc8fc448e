# 桥接失败智能恢复机制实现总结

## 概述

在 `bridge_arb_executor.py` 中添加了智能恢复机制，当桥接操作失败后，系统会自动检查桥接状态并尝试恢复或重试操作。

## 核心功能

### 1. 主要恢复流程

当桥接操作失败时，系统会启动 `handle_bridge_failure_recovery` 方法，执行以下步骤：

1. **检查半完成的桥接交易**
2. **检查钱包余额**
3. **重试桥接操作**
4. **循环重试直到成功或达到最大重试次数**

### 2. 半完成桥接检查 (`check_half_completed_bridge`)

**功能**：检查是否存在已发起但未完成的桥接交易

**检查逻辑**：
- 根据桥接方向确定检查的链：
  - `ethereum_to_polygon`: 检查以太坊链上的交易
  - `polygon_to_ethereum`: 检查Polygon链上的交易
- 查找来自钱包地址且金额匹配的交易（允许1%容差）
- 检查时间范围：
  - 以太坊：30分钟内
  - Polygon：120分钟内

**返回结果**：
```python
{
    "found": True/False,
    "tx_hash": "交易哈希",
    "chain": "检查的链",
    "amount": "交易金额",
    "bridge_direction": "桥接方向"
}
```

### 3. 继续桥接操作 (`continue_bridge_operation`)

**功能**：继续未完成的桥接操作

**处理逻辑**：

#### A. Polygon到以太坊方向
- 执行claim操作：`python -m src.bridge.pol_bridge.bridge_tokens claim --tx-hash {tx_hash}`
- 使用subprocess调用命令行工具
- 超时时间：300秒

#### B. 以太坊到Polygon方向
- 使用 `monitor_polygon_deposit` 方法监控Polygon到账
- 参数设置：
  - `check_interval`: 10秒
  - `timeout_minutes`: 80分钟
  - `initial_wait_time`: 60秒
  - `expected_amount`: 预期到账金额

### 4. 钱包余额检查 (`check_wallet_balance`)

**功能**：检查钱包中是否有足够的代币进行桥接

**检查逻辑**：
- 根据桥接方向确定检查的链和代币地址
- 使用KyberSwapClient获取代币余额
- 比较余额与所需数量
- 失败时自动重试3次，每次间隔10秒

**返回结果**：
```python
{
    "sufficient": True/False,
    "balance": "实际余额",
    "required": "需要数量",
    "error": "错误信息（如果有）"
}
```

## 恢复策略

### 重试参数
- **最大重试次数**：10次
- **重试间隔**：60秒
- **余额检查重试**：3次，间隔10秒

### 恢复优先级
1. **优先处理半完成的桥接**：如果发现半完成的交易，立即尝试继续
2. **余额检查**：确保有足够的代币进行桥接
3. **重新桥接**：如果余额充足但没有半完成交易，重新执行桥接

### 停止条件
- ✅ 桥接成功完成
- ❌ 钱包余额不足
- ❌ 达到最大重试次数
- ❌ 余额检查连续失败

## 集成方式

### 原有代码修改
```python
# 修改前
if not bridge_result.get("success"):
    error_msg = bridge_result.get("error", "未知错误")
    self.logger.error(f"桥接操作失败: {error_msg}")
    execution_record['bridge_error'] = error_msg

# 修改后
if not bridge_result.get("success"):
    error_msg = bridge_result.get("error", "未知错误")
    self.logger.error(f"桥接操作失败: {error_msg}")
    
    # 启动智能恢复机制
    self.logger.info("启动桥接失败智能恢复机制...")
    recovery_result = await self.handle_bridge_failure_recovery(actual_amount_str)
    
    if recovery_result.get("success"):
        self.logger.info("桥接恢复成功")
        execution_record['bridge_success'] = True
        execution_record['bridge_recovery'] = True
        execution_record.update(recovery_result.get('bridge_record', {}))
    else:
        self.logger.error(f"桥接恢复失败: {recovery_result.get('error', '未知错误')}")
        execution_record['bridge_error'] = error_msg
        execution_record['recovery_error'] = recovery_result.get('error', '未知错误')
```

## 日志输出示例

```
桥接操作失败: 网络超时
启动桥接失败智能恢复机制...
开始桥接失败恢复流程，代币数量: 100.5
使用钱包地址: ******************************************
开始第 1 次恢复尝试...
检查半完成的桥接交易，钱包地址: ******************************************
发现匹配的桥接交易: 0xabc123...
发现半完成的桥接交易，开始继续后续操作...
继续桥接操作，方向: polygon_to_ethereum, 交易哈希: 0xabc123...
执行claim操作...
执行命令: python -m src.bridge.pol_bridge.bridge_tokens claim --tx-hash 0xabc123...
Claim操作成功完成
桥接恢复成功
```

## 错误处理

### 常见错误场景
1. **无法获取钱包地址**：配置文件问题
2. **无法导入监控模块**：文件路径问题
3. **RPC连接失败**：网络问题，自动重试
4. **余额不足**：停止重试，返回错误
5. **claim命令执行失败**：subprocess错误
6. **监控超时**：Polygon到账超时

### 错误恢复
- 网络相关错误：自动重试
- 配置相关错误：立即返回失败
- 余额不足：停止重试
- 超时错误：继续下一次重试

## 优势

1. **自动化恢复**：无需人工干预，自动处理桥接失败
2. **智能检测**：能够识别半完成的桥接交易
3. **多重保障**：余额检查、交易检查、重试机制
4. **详细日志**：完整的恢复过程记录
5. **灵活配置**：可调整重试次数和间隔
6. **向后兼容**：不影响原有的桥接成功流程

## 使用场景

- **网络不稳定**：临时网络问题导致的桥接失败
- **RPC节点问题**：RPC响应超时或错误
- **交易确认延迟**：区块链网络拥堵
- **部分完成的桥接**：第一步成功但第二步失败的情况

通过这个智能恢复机制，大大提高了桥接操作的成功率和系统的稳定性。
