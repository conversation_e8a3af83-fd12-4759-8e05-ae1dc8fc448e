{"version": 3, "sources": ["../../src/api/table.ts"], "sourcesContent": ["import { getTableItem, getTableItemsData, getTableItemsMetadata } from \"../internal/table\";\nimport {\n  TableItemRequest,\n  LedgerVersionArg,\n  AnyNumber,\n  PaginationArgs,\n  WhereArg,\n  OrderByArg,\n  GetTableItemsDataResponse,\n  GetTableItemsMetadataResponse,\n} from \"../types\";\nimport { TableItemsBoolExp, TableMetadatasBoolExp } from \"../types/generated/types\";\nimport { ProcessorType } from \"../utils\";\nimport { AptosConfig } from \"./aptosConfig\";\nimport { waitForIndexerOnVersion } from \"./utils\";\n\n/**\n * A class to query all `Table` Aptos related queries.\n * @group Table\n */\nexport class Table {\n  readonly config: AptosConfig;\n\n  /**\n   * Initializes a new instance of the Aptos client with the specified configuration.\n   * This allows you to interact with the Aptos blockchain using the provided settings.\n   *\n   * @param config - The configuration settings for the Aptos client.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * async function runExample() {\n   *     // Create a new Aptos client with testnet configuration\n   *     const config = new AptosConfig({ network: Network.TESTNET });\n   *     const aptos = new Aptos(config);\n   *\n   *     console.log(\"Aptos client initialized:\", aptos);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Table\n   */\n  constructor(config: AptosConfig) {\n    this.config = config;\n  }\n\n  /**\n   * Queries for a specific item in a table identified by the handle and the key for the item.\n   * This function allows you to retrieve structured data from a table in the Aptos blockchain.\n   *\n   * @param args.handle A pointer to where that table is stored.\n   * @param args.data Object that describes the table item, including key and value types.\n   * @param args.data.key_type The Move type of the table key.\n   * @param args.data.value_type The Move type of the table value.\n   * @param args.data.key The value of the table key.\n   * @param args.options.ledgerVersion The ledger version to query; if not provided, it will get the latest version.\n   *\n   * @returns Table item value rendered in JSON.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   // Retrieve a table item from the Aptos blockchain\n   *   const tableItem = await aptos.getTableItem({\n   *     handle: \"0x1b854694ae746cdbd8d44186ca4929b2b337df21d1c74633be19b2710552fdca\",\n   *     data: {\n   *       key_type: \"address\", // Move type of table key\n   *       value_type: \"u128\", // Move type of table value\n   *       key: \"0x619dc29a0aac8fa146714058e8dd6d2d0f3bdf5f6331907bf91f3acd81e6935\" // Value of table key\n   *     },\n   *   });\n   *\n   *   console.log(tableItem);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Table\n   */\n  async getTableItem<T>(args: { handle: string; data: TableItemRequest; options?: LedgerVersionArg }): Promise<T> {\n    return getTableItem<T>({ aptosConfig: this.config, ...args });\n  }\n\n  /**\n   * Queries for table items data with optional filtering and pagination.\n   * This function allows you to retrieve specific data from a table based on provided criteria.\n   *\n   * @param args - The arguments for querying table items data.\n   * @param args.minimumLedgerVersion - Optional minimum ledger version to wait for before querying.\n   * @param args.options - Optional parameters for pagination and filtering.\n   * @param args.options.where - Conditions to filter the response.\n   * @param args.options.offset - The number of items to skip before starting to collect the result set.\n   * @param args.options.limit - The maximum number of items to return.\n   * @param args.options.orderBy - The criteria to order the results.\n   *\n   * Note: This query calls the indexer server.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   // Retrieve table items data with specific filtering options\n   *   const data = await aptos.getTableItemsData({\n   *     minimumLedgerVersion: 1, // specify your own minimum ledger version if needed\n   *     options: {\n   *       where: {\n   *         table_handle: { _eq: \"0x1b854694ae746cdbd8d44186ca4929b2b337df21d1c74633be19b2710552fdca\" },\n   *         transaction_version: { _eq: \"0\" }\n   *       },\n   *       limit: 10, // specify your own limit if needed\n   *     },\n   *   });\n   *\n   *   console.log(data);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   *\n   * @returns GetTableItemsDataResponse\n   * @group Table\n   */\n  async getTableItemsData(args: {\n    minimumLedgerVersion?: AnyNumber;\n    options?: PaginationArgs & WhereArg<TableItemsBoolExp> & OrderByArg<GetTableItemsDataResponse[0]>;\n  }): Promise<GetTableItemsDataResponse> {\n    await waitForIndexerOnVersion({\n      config: this.config,\n      minimumLedgerVersion: args.minimumLedgerVersion,\n      processorType: ProcessorType.DEFAULT,\n    });\n    return getTableItemsData({ aptosConfig: this.config, ...args });\n  }\n\n  /**\n   * Queries for the metadata of table items, allowing for filtering and pagination.\n   *\n   * @param args - The parameters for the query.\n   * @param args.minimumLedgerVersion - Optional minimum ledger version to wait for before querying.\n   * @param args.options - Optional parameters for pagination and filtering.\n   * @param args.options.where - Conditions to filter the response.\n   * @param args.options.offset - The offset for pagination.\n   * @param args.options.limit - The maximum number of items to return.\n   * @param args.options.orderBy - The order in which to return the items.\n   *\n   * Note that this query calls the indexer server.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   // Fetching table items metadata with a filter condition\n   *   const data = await aptos.getTableItemsMetadata({\n   *     minimumLedgerVersion: 1, // specify your own minimum ledger version if needed\n   *     options: {\n   *       where: { handle: { _eq: \"0x1b854694ae746cdbd8d44186ca4929b2b337df21d1c74633be19b2710552fdca\" } },\n   *       limit: 10, // specify your own limit if needed\n   *     },\n   *   });\n   *\n   *   console.log(data);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   *\n   * @returns GetTableItemsMetadataResponse\n   * @group Table\n   */\n  async getTableItemsMetadata(args: {\n    minimumLedgerVersion?: AnyNumber;\n    options?: PaginationArgs & WhereArg<TableMetadatasBoolExp> & OrderByArg<GetTableItemsMetadataResponse[0]>;\n  }): Promise<GetTableItemsMetadataResponse> {\n    await waitForIndexerOnVersion({\n      config: this.config,\n      minimumLedgerVersion: args.minimumLedgerVersion,\n      processorType: ProcessorType.DEFAULT,\n    });\n    return getTableItemsMetadata({ aptosConfig: this.config, ...args });\n  }\n}\n"], "mappings": "gGAoBO,IAAMA,EAAN,KAAY,CAwBjB,YAAYC,EAAqB,CAC/B,KAAK,OAASA,CAChB,CAuCA,MAAM,aAAgBC,EAA0F,CAC9G,OAAOC,EAAgB,CAAE,YAAa,KAAK,OAAQ,GAAGD,CAAK,CAAC,CAC9D,CA4CA,MAAM,kBAAkBA,EAGe,CACrC,aAAME,EAAwB,CAC5B,OAAQ,KAAK,OACb,qBAAsBF,EAAK,qBAC3B,iCACF,CAAC,EACMG,EAAkB,CAAE,YAAa,KAAK,OAAQ,GAAGH,CAAK,CAAC,CAChE,CAwCA,MAAM,sBAAsBA,EAGe,CACzC,aAAME,EAAwB,CAC5B,OAAQ,KAAK,OACb,qBAAsBF,EAAK,qBAC3B,iCACF,CAAC,EACMI,EAAsB,CAAE,YAAa,KAAK,OAAQ,GAAGJ,CAAK,CAAC,CACpE,CACF", "names": ["Table", "config", "args", "getTableItem", "waitForIndexerOnVersion", "getTableItemsData", "getTableItemsMetadata"]}