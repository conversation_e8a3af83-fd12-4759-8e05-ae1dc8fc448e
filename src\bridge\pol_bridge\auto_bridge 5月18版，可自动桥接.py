from typing import Dict, Optional, Union
from decimal import Decimal
import asyncio
import logging
from .bridge_tokens import PolygonEthereumBridge

class AutoBridge:
    """
    自动桥接工具类
    封装了双向自动桥接功能，包括自动监控和claim
    """
    
    def __init__(
        self,
        ethereum_rpc_url: str,
        polygon_rpc_url: str,
        private_key: str,
        check_interval: int = 60,
        max_check_time: int = 21600,  # 6小时
        initial_wait: int = 1800,      # 30分钟
    ):
        """
        初始化自动桥接工具
        
        Args:
            ethereum_rpc_url: 以太坊RPC地址
            polygon_rpc_url: Polygon RPC地址
            private_key: 私钥
            check_interval: 检查间隔(秒)
            max_check_time: 最大检查时间(秒)
            initial_wait: 初始等待时间(秒)
        """
        self.bridge = PolygonEthereumBridge(
            ethereum_rpc_url=ethereum_rpc_url,
            polygon_rpc_url=polygon_rpc_url,
            private_key=private_key
        )
        self.check_interval = check_interval
        self.max_check_time = max_check_time
        self.initial_wait = initial_wait
        
    async def eth_to_polygon(
        self,
        token_address: str,
        amount: Union[int, float, Decimal],
        receiver: Optional[str] = None,
        wait_time: int = 1200,
        direct: bool = True
    ) -> Dict:
        """
        从以太坊桥接到Polygon，并自动监控到账
        
        Args:
            token_address: 代币地址
            amount: 数量
            receiver: 接收地址(可选)
            wait_time: 等待时间(秒)
            direct: 是否使用直接桥接
            
        Returns:
            Dict: 桥接结果
        """
        try:
            if direct:
                bridge_result = await self.bridge.direct_bridge_to_polygon(
                    token_address=token_address,
                    amount=amount,
                    receiver=receiver
                )
            else:
                bridge_result = await self.bridge.bridge_to_polygon(
                    token_address=token_address,
                    amount=amount,
                    receiver=receiver
                )
            
            if not bridge_result:
                return {
                    "success": False,
                    "message": "桥接交易失败",
                    "bridge_tx": None
                }
                
            # 等待指定时间
            logging.info(f"等待 {wait_time} 秒后开始监控...")
            await asyncio.sleep(wait_time)
            
            # 获取接收地址
            receiver = receiver or self.bridge.ethereum_account.address
            
            # 监控到账
            monitor_result = await self.bridge.monitor_polygon_deposit(
                ethereum_token_address=token_address,
                receiver_address=receiver,
                expected_amount=amount,
                timeout_minutes=80  # 增加超时时间到80分钟
            )
            
            return {
                "success": monitor_result["success"],
                "message": monitor_result.get("message", "未知状态"),
                "bridge_tx": bridge_result,
                "amount": monitor_result.get("amount"),
                "polygon_tx": monitor_result.get("tx_hash")
            }
            
        except Exception as e:
            logging.error(f"桥接过程出错: {str(e)}")
            return {
                "success": False,
                "message": f"错误: {str(e)}",
                "bridge_tx": None
            }
            
    async def polygon_to_eth(
        self,
        token_address: str,
        amount: Union[int, float, Decimal],
        auto_claim: bool = True
    ) -> Dict:
        """
        从Polygon桥接到以太坊，并自动执行claim
        
        Args:
            token_address: 代币地址
            amount: 数量
            auto_claim: 是否自动claim
            
        Returns:
            Dict: 桥接结果
        """
        try:
            result = await self.bridge.polygon_to_ethereum(
                token_address=token_address,
                amount=str(amount),
                wait_for_claim=auto_claim,
                auto_check_interval=self.check_interval,
                max_check_time=self.max_check_time,
                initial_wait_time=self.initial_wait
            )
            
            return {
                "success": True if result.get("claim_tx_hash") else False,
                "burn_tx": result.get("burn_transaction_hash"),
                "claim_tx": result.get("claim_tx_hash"),
                "message": result.get("message", "未知状态"),
                "claim_status": result.get("claim_status")
            }
            
        except Exception as e:
            logging.error(f"桥接过程出错: {str(e)}")
            return {
                "success": False,
                "message": f"错误: {str(e)}",
                "burn_tx": None,
                "claim_tx": None
            }
            
    def get_token_info(self, token_address: str, chain: str = "ethereum") -> Dict:
        """
        获取代币信息
        
        Args:
            token_address: 代币地址
            chain: 链名称 (ethereum 或 polygon)
            
        Returns:
            Dict: 代币信息
        """
        return self.bridge.get_token_info(token_address=token_address, chain=chain)
        
    def get_mapped_address(self, token_address: str, from_chain: str = "ethereum") -> Optional[str]:
        """
        获取代币在另一条链上的映射地址
        
        Args:
            token_address: 代币地址
            from_chain: 源链名称 (ethereum 或 polygon)
            
        Returns:
            Optional[str]: 映射地址
        """
        if from_chain.lower() == "ethereum":
            return self.bridge.get_polygon_mapped_address(token_address)
        else:
            return self.bridge.get_root_token(token_address) 