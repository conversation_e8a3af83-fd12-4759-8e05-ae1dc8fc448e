import{a as k}from"./chunk-YOZBVVKL.mjs";import{e as x,f as B}from"./chunk-GBNAG7KK.mjs";import{b as f,d as A}from"./chunk-CZYH3G7E.mjs";import{e as b}from"./chunk-6WDVDEQZ.mjs";import{d as C}from"./chunk-RJ7F4JDV.mjs";import{a as d}from"./chunk-7DQDJ2SA.mjs";async function H(o){let{aptosConfig:n,options:t}=o;return A({aptosConfig:n,originMethod:"getTransactions",path:"transactions",params:{start:t?.offset,limit:t?.limit}})}async function W(o){let{aptosConfig:n}=o;return k(async()=>{let{data:t}=await f({aptosConfig:n,originMethod:"getGasPriceEstimation",path:"estimate_gas_price"});return t},`gas-price-${n.network}`,1e3*60*5)()}async function O(o){let{aptosConfig:n,ledgerVersion:t}=o,{data:s}=await f({aptosConfig:n,originMethod:"getTransactionByVersion",path:`transactions/by_version/${t}`});return s}async function y(o){let{aptosConfig:n,transactionHash:t}=o,{data:s}=await f({aptosConfig:n,path:`transactions/by_hash/${t}`,originMethod:"getTransactionByHash"});return s}async function q(o){let{aptosConfig:n,transactionHash:t}=o;try{return(await y({aptosConfig:n,transactionHash:t})).type==="pending_transaction"}catch(s){if(s?.status===404)return!0;throw s}}async function R(o){let{aptosConfig:n,transactionHash:t}=o,{data:s}=await f({aptosConfig:n,path:`transactions/wait_by_hash/${t}`,originMethod:"longWaitForTransaction"});return s}async function z(o){let{aptosConfig:n,transactionHash:t,options:s}=o,r=s?.timeoutSecs??20,u=s?.checkSuccess??!0,e=!0,p=0,i,g,l=200,h=1.5;function c(a){if(!(a instanceof b)||(g=a,a.status!==404&&a.status>=400&&a.status<500))throw a}try{i=await y({aptosConfig:n,transactionHash:t}),e=i.type==="pending_transaction"}catch(a){c(a)}if(e){let a=Date.now();try{i=await R({aptosConfig:n,transactionHash:t}),e=i.type==="pending_transaction"}catch(w){c(w)}p=(Date.now()-a)/1e3}for(;e&&!(p>=r);){try{if(i=await y({aptosConfig:n,transactionHash:t}),e=i.type==="pending_transaction",!e)break}catch(a){c(a)}await d(l),p+=l/1e3,l*=h}if(i===void 0)throw g||new m(`Fetching transaction ${t} failed and timed out after ${r} seconds`,i);if(i.type==="pending_transaction")throw new m(`Transaction ${t} timed out in pending state after ${r} seconds`,i);if(!u)return i;if(!i.success)throw new T(`Transaction ${t} failed with an error: ${i.vm_status}`,i);return i}async function U(o){let{aptosConfig:n,processorType:t}=o,s=BigInt(o.minimumLedgerVersion),r=3e3,u=new Date().getTime(),e=BigInt(-1);for(;e<s;){if(new Date().getTime()-u>r)throw new Error("waitForLastSuccessIndexerVersionSync timeout");if(t===void 0?e=await x({aptosConfig:n}):e=(await B({aptosConfig:n,processorType:t})).last_success_version,e>=s)break;await d(200)}}var m=class extends Error{constructor(n,t){super(n),this.lastSubmittedTransaction=t}},T=class extends Error{constructor(n,t){super(n),this.transaction=t}};async function X(o){let{aptosConfig:n,ledgerVersion:t,options:s}=o,{data:r}=await f({aptosConfig:n,originMethod:"getBlockByVersion",path:`blocks/by_version/${t}`,params:{with_transactions:s?.withTransactions}});return P({block:r,...o})}async function j(o){let{aptosConfig:n,blockHeight:t,options:s}=o,{data:r}=await f({aptosConfig:n,originMethod:"getBlockByHeight",path:`blocks/by_height/${t}`,params:{with_transactions:s?.withTransactions}});return P({block:r,...o})}async function P(o){let{aptosConfig:n,block:t,options:s}=o;if(s?.withTransactions){t.transactions=t.transactions??[];let r=t.transactions[t.transactions.length-1],u=BigInt(t.first_version),e=BigInt(t.last_version),p=r?.version,i;if(p===void 0?i=u-1n:i=BigInt(p),i===e)return t;let g=[],l=100n;for(let c=i+1n;c<e;c+=BigInt(100))g.push(H({aptosConfig:n,options:{offset:c,limit:Math.min(Number(l),Number(e-c+1n))}}));let h=await Promise.all(g);for(let c of h)t.transactions.push(...c)}return t}export{H as a,W as b,O as c,y as d,q as e,R as f,z as g,U as h,m as i,T as j,X as k,j as l};
//# sourceMappingURL=chunk-KRBZ54CY.mjs.map