"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcUnaryRequestException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
class GrpcUnaryRequestException extends base_js_1.ConcreteException {
    static errorClass = 'GrpcUnaryRequestException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.GrpcUnaryRequest;
    }
    parse() {
        const { message } = this;
        if (message.toLowerCase().includes('response closed without headers')) {
            this.setMessage('The request has failed. The server has closed the connection without sending any headers.');
            this.setContextCode(index_js_1.GRPC_REQUEST_FAILED);
        }
        this.setName(GrpcUnaryRequestException.errorClass);
    }
}
exports.GrpcUnaryRequestException = GrpcUnaryRequestException;
