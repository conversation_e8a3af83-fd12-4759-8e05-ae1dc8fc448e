import _m0 from "protobufjs/minimal.js";
import { Event, ExecTxResult, ValidatorUpdate } from "../../../abci/v2/types";
import { ConsensusParams } from "../../../types/v2/params";
export declare const protobufPackage = "cometbft.services.block_results.v2";
/** GetBlockResults is a request for the BlockResults of a given height. */
export interface GetBlockResultsRequest {
    height: string;
}
/** GetBlockResultsResponse contains the block results for the given height. */
export interface GetBlockResultsResponse {
    height: string;
    txResults: ExecTxResult[];
    finalizeBlockEvents: Event[];
    validatorUpdates: ValidatorUpdate[];
    consensusParamUpdates: ConsensusParams | undefined;
    appHash: Uint8Array;
}
export declare const GetBlockResultsRequest: {
    encode(message: GetBlockResultsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockResultsRequest;
    fromJSON(object: any): GetBlockResultsRequest;
    toJSON(message: GetBlockResultsRequest): unknown;
    create(base?: DeepPartial<GetBlockResultsRequest>): GetBlockResultsRequest;
    fromPartial(object: DeepPartial<GetBlockResultsRequest>): GetBlockResultsRequest;
};
export declare const GetBlockResultsResponse: {
    encode(message: GetBlockResultsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockResultsResponse;
    fromJSON(object: any): GetBlockResultsResponse;
    toJSON(message: GetBlockResultsResponse): unknown;
    create(base?: DeepPartial<GetBlockResultsResponse>): GetBlockResultsResponse;
    fromPartial(object: DeepPartial<GetBlockResultsResponse>): GetBlockResultsResponse;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
