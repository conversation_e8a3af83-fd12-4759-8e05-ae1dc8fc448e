{"_ethers.alias": {"base64.js": "browser-base64.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/base64": "./lib/browser-base64.js"}, "dependencies": {"@ethersproject/bytes": "^5.8.0"}, "description": "Base64 coder.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/base64", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/base64", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xc359cc40bc66946930c92e7ac84e7344800958d70bbef7803e7f2777fb7a2ce9", "types": "./lib/index.d.ts", "version": "5.8.0"}