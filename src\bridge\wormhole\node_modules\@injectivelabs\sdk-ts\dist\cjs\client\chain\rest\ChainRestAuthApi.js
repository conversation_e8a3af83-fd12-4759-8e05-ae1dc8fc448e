"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainRestAuthApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const BaseRestConsumer_js_1 = __importDefault(require("../../base/BaseRestConsumer.js"));
const index_js_1 = require("../types/index.js");
/**
 * @category Chain Rest API
 */
class ChainRestAuthApi extends BaseRestConsumer_js_1.default {
    /**
     * Looks up the account information for the Injective address.
     *
     * @param address address of account to look up
     */
    async fetchAccount(address, params = {}) {
        const endpoint = `cosmos/auth/v1beta1/accounts/${address}`;
        try {
            const response = await this.retry(() => this.get(endpoint, params));
            return response.data;
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_1.ChainModule.Auth,
            });
        }
    }
    /**
     * Looks up the account information for any cosmos chain address.
     *
     * @param address address of account to look up
     */
    async fetchCosmosAccount(address, params = {}) {
        const endpoint = `cosmos/auth/v1beta1/accounts/${address}`;
        try {
            const isInjectiveAddress = address.startsWith('inj') || address.startsWith('evmos');
            const response = await this.retry(() => this.get(endpoint, params));
            const baseAccount = isInjectiveAddress
                ? response.data.account.base_account
                : response.data.account;
            return baseAccount;
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_1.ChainModule.Auth,
            });
        }
    }
}
exports.ChainRestAuthApi = ChainRestAuthApi;
