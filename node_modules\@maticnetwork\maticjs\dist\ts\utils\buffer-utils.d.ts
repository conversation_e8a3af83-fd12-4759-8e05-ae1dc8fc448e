/// <reference types="node" />
import { ITransformableToArray, PrefixedHexString, ITransformableToBuffer, BN } from "./types";
export type ToBufferInputTypes = PrefixedHexString | number | BN | Buffer | Uint8Array | number[] | ITransformableToArray | ITransformableToBuffer | null | undefined;
export declare class BufferUtil {
    static intToHex: (i: number) => string;
    static padToEven(value: string): string;
    static isHexPrefixed(str: string): boolean;
    static stripHexPrefix: (str: string) => string;
    /**
     * Converts an `Number` to a `Buffer`
     * @param {Number} i
     * @return {Buffer}
     */
    static intToBuffer: (i: number) => Buffer;
    static isHexString(value: string, length?: number): boolean;
    static toBuffer: (v: ToBufferInputTypes) => Buffer;
    /**
     * Converts a `Buffer` into a `0x`-prefixed hex `String`.
     * @param buf `Buffer` object to convert
     */
    static bufferToHex: (buf: Buffer) => string;
}
