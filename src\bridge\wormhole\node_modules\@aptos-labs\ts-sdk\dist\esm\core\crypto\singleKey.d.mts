import { Deserializer } from '../../bcs/deserializer.mjs';
import { Serializer } from '../../bcs/serializer.mjs';
import { AnyPublicKeyVariant, HexInput } from '../../types/types.mjs';
import { b as AccountPublic<PERSON>ey, P as PublicKey, A as AuthenticationKey } from '../../publicKey-CJOcUwJK.mjs';
import { Ed25519PrivateKey } from './ed25519.mjs';
import { Secp256k1PrivateKey } from './secp256k1.mjs';
import { Signature } from './signature.mjs';
import { AptosConfig } from '../../api/aptosConfig.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../hex.mjs';
import '../common.mjs';
import '../accountAddress.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import './privateKey.mjs';
import '../../utils/const.mjs';

type PrivateKeyInput = Ed25519PrivateKey | Secp256k1PrivateKey;
/**
 * Represents any public key supported by Aptos.
 *
 * Since [AIP-55](https://github.com/aptos-foundation/AIPs/pull/263) Aptos supports
 * `Legacy` and `Unified` authentication keys.
 *
 * Any unified authentication key is represented in the SDK as `AnyPublicKey`.
 * @group Implementation
 * @category Serialization
 */
declare class AnyPublicKey extends AccountPublicKey {
    /**
     * Reference to the inner public key
     * @group Implementation
     * @category Serialization
     */
    readonly publicKey: PublicKey;
    /**
     * Index of the underlying enum variant
     * @group Implementation
     * @category Serialization
     */
    readonly variant: AnyPublicKeyVariant;
    /**
     * Creates an instance of the signature class based on the provided signature type.
     * This allows for the handling of different signature variants such as Ed25519, Secp256k1, and Keyless.
     *
     * @param publicKey - The publicKey object which determines the variant to be used.
     * @throws Error if the provided signature type is unsupported.
     * @group Implementation
     * @category Serialization
     */
    constructor(publicKey: PublicKey);
    /**
     * Verifies the provided signature against the given message.
     * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.
     *
     * @param args - The arguments for signature verification.
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify, which must be an instance of AnySignature.
     * @returns A boolean indicating whether the signature is valid for the given message.
     * @group Implementation
     * @category Serialization
     */
    verifySignature(args: {
        message: HexInput;
        signature: AnySignature;
    }): boolean;
    /**
     * Verifies the provided signature against the given message.
     * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.
     *
     * @param args - The arguments for signature verification.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify, which must be an instance of AnySignature.
     * @returns A boolean indicating whether the signature is valid for the given message.
     * @group Implementation
     * @category Serialization
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Generates an authentication key from the current instance's byte representation.
     * This function is essential for creating a unique identifier for authentication purposes.
     *
     * @returns {AuthenticationKey} The generated authentication key.
     * @group Implementation
     * @category Serialization
     */
    authKey(): AuthenticationKey;
    /**
     * Get the signature in bytes (Uint8Array).
     *
     * This function is a warning that it will soon return the underlying signature bytes directly.
     * Use AnySignature.bcsToBytes() instead.
     *
     * @returns Uint8Array representation of the signature.
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Serializes the current object using the provided serializer.
     * This function helps in converting the object into a format suitable for transmission or storage.
     *
     * @param serializer - The serializer instance used to perform the serialization.
     * @group Implementation
     * @category Serialization
     */
    serialize(serializer: Serializer): void;
    /**
     * Deserializes an AnySignature from the provided deserializer.
     * This function helps in reconstructing the AnySignature object from its serialized form, allowing for further processing or validation.
     *
     * @param deserializer - The deserializer instance used to read the serialized data.
     * @group Implementation
     * @category Serialization
     */
    static deserialize(deserializer: Deserializer): AnyPublicKey;
    /**
     * Determines if the provided public key is an instance of AnyPublicKey.
     *
     * @param publicKey - The public key to check.
     * @deprecated Use `instanceof AnyPublicKey` instead.
     * @group Implementation
     * @category Serialization
     */
    static isPublicKey(publicKey: AccountPublicKey): publicKey is AnyPublicKey;
    /**
     * Determines if the current public key is an instance of Ed25519PublicKey.
     *
     * @deprecated use `publicKey instanceof Ed25519PublicKey` instead.
     * @group Implementation
     * @category Serialization
     */
    isEd25519(): boolean;
    /**
     * Checks if the public key is an instance of Secp256k1PublicKey.
     *
     * @deprecated use `publicKey instanceof Secp256k1PublicKey` instead.
     * @group Implementation
     * @category Serialization
     */
    isSecp256k1PublicKey(): boolean;
    /**
     * Determines if the provided publicKey is an instance of a valid PublicKey object.
     *
     * @param publicKey - The publicKey to be checked for validity.
     * @param publicKey.publicKey - The actual publicKey object that needs to be validated.
     * @returns True if the signature is a valid instance; otherwise, false.
     * @group Implementation
     * @category Serialization
     */
    static isInstance(publicKey: PublicKey): publicKey is AnyPublicKey;
}
/**
 * Represents a signature that utilizes the SingleKey authentication scheme.
 * This class is designed to encapsulate various types of signatures, which can
 * only be generated by a `SingleKeySigner` due to the shared authentication mechanism.
 *
 * @extends Signature
 * @group Implementation
 * @category Serialization
 */
declare class AnySignature extends Signature {
    readonly signature: Signature;
    /**
     * Index of the underlying enum variant
     * @group Implementation
     * @category Serialization
     */
    private readonly variant;
    constructor(signature: Signature);
    toUint8Array(): Uint8Array;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): AnySignature;
    static isInstance(signature: Signature): signature is AnySignature;
}

export { AnyPublicKey, AnySignature, type PrivateKeyInput };
