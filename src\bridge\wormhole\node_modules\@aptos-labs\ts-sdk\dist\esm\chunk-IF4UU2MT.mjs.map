{"version": 3, "sources": ["../../src/core/common.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This error is used to explain why parsing failed.\n * @group Implementation\n * @category Serialization\n */\nexport class ParsingError<T> extends Error {\n  /**\n   * This provides a programmatic way to access why parsing failed. Downstream devs\n   * might want to use this to build their own error messages if the default error\n   * messages are not suitable for their use case. This should be an enum.\n   * @group Implementation\n   * @category Serialization\n   */\n  public invalidReason: T;\n\n  /**\n   * Creates an instance of the error with a specified message and invalid reason.\n   *\n   * @param message The error message that describes the issue.\n   * @param invalidReason The reason why the input is considered invalid.\n   * @group Implementation\n   * @category Serialization\n   */\n  constructor(message: string, invalidReason: T) {\n    super(message);\n    this.invalidReason = invalidReason;\n  }\n}\n\n/**\n * Whereas ParsingError is thrown when parsing fails, e.g. in a fromString function,\n * this type is returned from \"defensive\" functions like isValid.\n * @group Implementation\n * @category Serialization\n */\nexport type ParsingResult<T> = {\n  /**\n   * True if valid, false otherwise.\n   * @group Implementation\n   * @category Serialization\n   */\n  valid: boolean;\n\n  /**\n   * If valid is false, this will be a code explaining why parsing failed.\n   * @group Implementation\n   * @category Serialization\n   */\n  invalidReason?: T;\n\n  /**\n   * If valid is false, this will be a string explaining why parsing failed.\n   * @group Implementation\n   * @category Serialization\n   */\n  invalidReasonMessage?: string;\n};\n"], "mappings": "AAQO,IAAMA,EAAN,cAA8B,KAAM,CAkBzC,YAAYC,EAAiBC,EAAkB,CAC7C,MAAMD,CAAO,EACb,KAAK,cAAgBC,CACvB,CACF", "names": ["ParsingE<PERSON>r", "message", "invalidReason"]}