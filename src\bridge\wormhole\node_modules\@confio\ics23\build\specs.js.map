{"version": 3, "file": "specs.js", "sourceRoot": "", "sources": ["../src/specs.ts"], "names": [], "mappings": ";;;AAEA,SAAgB,UAAU,CAAC,IAAmB,EAAE,IAAmB;IACjE,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KACpD;IACD,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,0BAA0B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;KAC9D;IACD,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;KAClE;IACD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;KACzD;IACD,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,CAAC;AAdD,gCAcC;AAED,SAAgB,WAAW,CACzB,KAAqB,EACrB,MAAqC,EACrC,IAAsB;IAEtB,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,sBAAsB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;KACrD;IACD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;IACD,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;IACD,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE;QACrD,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;KACnE;IACD,MAAM,iBAAiB,GAAG,CAAC,IAAI,CAAC,UAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC;IAC1E,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,iBAAiB,EAAE;QACzE,MAAM,IAAI,KAAK,CAAC,oBAAoB,KAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;KAClE;AACH,CAAC;AArBD,kCAqBC;AAED,SAAS,YAAY,CACnB,KAAyB,EACzB,MAA0B;IAE1B,8CAA8C;IAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO;KACR;IACD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,+DAA+D;AAC/D,SAAgB,gBAAgB,CAAC,CAAa,EAAE,CAAa;IAC3D,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,MAAM,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;KACjE;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SACpE;KACF;AACH,CAAC;AATD,4CASC;AAED,SAAgB,UAAU,CAAC,CAAa,EAAE,CAAa;IACrD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;QACzB,OAAO,KAAK,CAAC;KACd;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAVD,gCAUC;AAED,SAAS,SAAS,CAChB,KAAyB,EACzB,MAA0B;IAE1B,8CAA8C;IAC9C,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,KAAK,CAAC;KACd;IACD,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;KACF;IACD,MAAM,IAAI,CAAC;AACb,CAAC;AAED,qDAAqD;AACrD,0BAA0B;AAC1B,SAAgB,iBAAiB,CAAC,KAAiB,EAAE,IAAgB;IACnE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;QAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC;AAJD,8CAIC;AAED,SAAgB,WAAW,CAAC,KAAiB,EAAE,IAAgB;IAC7D,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC;SACd;QACD,2CAA2C;KAC5C;IACD,yDAAyD;IACzD,OAAO,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACpC,CAAC;AAbD,kCAaC"}