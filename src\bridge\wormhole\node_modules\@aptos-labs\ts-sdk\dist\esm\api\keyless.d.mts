import { HexInput } from '../types/types.mjs';
import { AccountAddressInput } from '../core/accountAddress.mjs';
import { AptosConfig } from './aptosConfig.mjs';
import { SimpleTransaction } from '../transactions/instances/simpleTransaction.mjs';
import { InputGenerateTransactionOptions } from '../transactions/types.mjs';
import { l as ZeroKnowledgeSig } from '../federatedKeyless-DAYXjY2Y.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { EphemeralKeyPair } from '../account/EphemeralKeyPair.mjs';
import { KeylessAccount } from '../account/KeylessAccount.mjs';
import { ProofFetchCallback } from '../account/AbstractKeylessAccount.mjs';
import { FederatedKeylessAccount } from '../account/FederatedKeylessAccount.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../utils/const.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import '../core/crypto/ephemeral.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';

/**
 * A class to query all `Keyless` related queries on Aptos.
 *
 * More documentation on how to integrate Keyless Accounts see the below
 * [Aptos Keyless Integration Guide](https://aptos.dev/guides/keyless-accounts/#aptos-keyless-integration-guide).
 * @group Keyless
 */
declare class Keyless {
    readonly config: AptosConfig;
    /**
     * Initializes a new instance of the Aptos class with the provided configuration.
     * This allows you to interact with the Aptos blockchain using the specified network settings.
     *
     * @param config - The configuration settings for connecting to the Aptos network.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * async function runExample() {
     *     // Create a new configuration for the Aptos client
     *     const config = new AptosConfig({ network: Network.TESTNET }); // Specify your desired network
     *
     *     // Initialize the Aptos client with the configuration
     *     const aptos = new Aptos(config);
     *
     *     console.log("Aptos client initialized:", aptos);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Keyless
     */
    constructor(config: AptosConfig);
    /**
     * Fetches the pepper from the Aptos pepper service API.
     *
     * @param args - The arguments for fetching the pepper.
     * @param args.jwt - JWT token.
     * @param args.ephemeralKeyPair - The EphemeralKeyPair used to generate the nonce in the JWT token.
     * @param args.derivationPath - A derivation path used for creating multiple accounts per user via the BIP-44 standard. Defaults
     * to "m/44'/637'/0'/0'/0".
     * @returns The pepper which is a Uint8Array of length 31.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   const ephemeralKeyPair = new EphemeralKeyPair(); // create a new ephemeral key pair
     *   const jwt = "your_jwt_token"; // replace with a real JWT token
     *
     *   // Fetching the pepper using the provided JWT and ephemeral key pair
     *   const pepper = await aptos.getPepper({
     *     jwt,
     *     ephemeralKeyPair,
     *     // derivationPath: "m/44'/637'/0'/0'/0" // specify your own if needed
     *   });
     *
     *   console.log("Fetched pepper:", pepper);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Keyless
     */
    getPepper(args: {
        jwt: string;
        ephemeralKeyPair: EphemeralKeyPair;
        derivationPath?: string;
    }): Promise<Uint8Array>;
    /**
     * Fetches a proof from the Aptos prover service API.
     *
     * @param args - The arguments for fetching the proof.
     * @param args.jwt - JWT token.
     * @param args.ephemeralKeyPair - The EphemeralKeyPair used to generate the nonce in the JWT token.
     * @param args.pepper - The pepper used for the account. If not provided, it will be fetched from the Aptos pepper service.
     * @param args.uidKey - A key in the JWT token to use to set the uidVal in the IdCommitment.
     *
     * @returns The proof which is represented by a ZeroKnowledgeSig.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network, EphemeralKeyPair, getPepper } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   const jwt = "your_jwt_token"; // replace with a real JWT token
     *   const ephemeralKeyPair = new EphemeralKeyPair(); // create a new ephemeral key pair
     *
     *   // Fetch the proof using the getProof function
     *   const proof = await aptos.getProof({
     *     jwt,
     *     ephemeralKeyPair,
     *     pepper: await getPepper({}), // fetch the pepper if not provided
     *     uidKey: "sub", // specify the uid key
     *   });
     *
     *   console.log("Fetched proof:", proof);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Keyless
     */
    getProof(args: {
        jwt: string;
        ephemeralKeyPair: EphemeralKeyPair;
        pepper?: HexInput;
        uidKey?: string;
    }): Promise<ZeroKnowledgeSig>;
    deriveKeylessAccount(args: {
        jwt: string;
        ephemeralKeyPair: EphemeralKeyPair;
        uidKey?: string;
        pepper?: HexInput;
        proofFetchCallback?: ProofFetchCallback;
    }): Promise<KeylessAccount>;
    deriveKeylessAccount(args: {
        jwt: string;
        ephemeralKeyPair: EphemeralKeyPair;
        jwkAddress: AccountAddressInput;
        uidKey?: string;
        pepper?: HexInput;
        proofFetchCallback?: ProofFetchCallback;
    }): Promise<FederatedKeylessAccount>;
    /**
     * This installs a set of FederatedJWKs at an address for a given iss.
     *
     * It will fetch the JSON Web Keyset (JWK) set from the well-known endpoint and update the FederatedJWKs at the sender's address
     * to reflect it.
     *
     * @param args.sender The account that will install the JWKs
     * @param args.iss the iss claim of the federated OIDC provider.
     * @param args.jwksUrl the URL to find the corresponding JWKs. For supported IDP providers this parameter in not necessary.
     *
     * @returns The pending transaction that results from submission.
     * @group Keyless
     */
    updateFederatedKeylessJwkSetTransaction(args: {
        sender: Account;
        iss: string;
        jwksUrl?: string;
        options?: InputGenerateTransactionOptions;
    }): Promise<SimpleTransaction>;
}

export { Keyless };
