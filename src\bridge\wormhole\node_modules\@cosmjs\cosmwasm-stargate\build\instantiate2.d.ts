/**
 * Private function to export test vector data for https://github.com/cosmos/cosmjs/pull/1253.
 * Do not use in production code.
 */
export declare function _instantiate2AddressIntermediate(checksum: Uint8Array, creator: string, salt: Uint8Array, msg: string | null, prefix: string): {
    key: Uint8Array;
    addressData: Uint8Array;
    address: string;
};
/**
 * Predictable address generation for the MsgInstantiateContract2
 * introduced with wasmd 0.29.
 *
 * With `checksum`, `creator` and `salt`, the instantiate 2 address is
 * generated in binary form. The `bech32Prefix` is then used for the bech32 representation.
 * Chains using address formats other than bech32 are not supported by this API.
 */
export declare function instantiate2Address(checksum: Uint8Array, creator: string, salt: Uint8Array, bech32Prefix: string): string;
