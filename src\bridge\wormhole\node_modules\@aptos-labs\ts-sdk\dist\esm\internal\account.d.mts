import { AptosConfig } from '../api/aptosConfig.mjs';
import { GetAccountOwnedTokensQueryResponse, GetAccountOwnedTokensFromCollectionResponse, GetAccountCollectionsWithOwnedTokenResponse, GetAccountCoinsDataResponse, GetObjectDataQueryResponse } from '../types/indexer.mjs';
import { AccountData, LedgerVersionArg, MoveModuleBytecode, CursorPaginationArgs, PaginationArgs, TransactionResponse, MoveResource, MoveStructId, TokenStandardArg, OrderByArg, WhereArg, PendingTransactionResponse } from '../types/types.mjs';
import { AccountAddressInput, AccountAddress } from '../core/accountAddress.mjs';
import { A as AuthenticationKey } from '../publicKey-CJOcUwJK.mjs';
import { CurrentFungibleAssetBalancesBoolExp } from '../types/generated/types.mjs';
import { Ed25519PrivateKey } from '../core/crypto/ed25519.mjs';
import { PrivateKey } from '../core/crypto/privateKey.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/generated/operations.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/crypto/signature.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

/**
 * This file contains the underlying implementations for exposed API surface in
 * the {@link api/account}. By moving the methods out into a separate file,
 * other namespaces and processes can access these methods without depending on the entire
 * account namespace and without having a dependency cycle error.
 * @group Implementation
 */

/**
 * Retrieves account information for a specified account address.
 *
 * @param args - The arguments for retrieving account information.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.accountAddress - The address of the account to retrieve information for.
 * @group Implementation
 */
declare function getInfo(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
}): Promise<AccountData>;
/**
 * Retrieves the modules associated with a specified account address.
 *
 * @param args - The arguments for retrieving modules.
 * @param args.aptosConfig - The configuration for connecting to the Aptos blockchain.
 * @param args.accountAddress - The address of the account whose modules are to be retrieved.
 * @param args.options - Optional parameters for pagination and ledger version.
 * @param args.options.limit - The maximum number of modules to retrieve (default is 1000).
 * @param args.options.offset - The starting point for pagination.  Note, this is obfuscated and is not an index.
 * @param args.options.ledgerVersion - The specific ledger version to query.
 * @group Implementation
 */
declare function getModules(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: {
        limit?: number;
    } & LedgerVersionArg;
}): Promise<MoveModuleBytecode[]>;
/**
 * Retrieves the modules associated with a specified account address.
 *
 * @param args - The arguments for retrieving modules.
 * @param args.aptosConfig - The configuration for connecting to the Aptos blockchain.
 * @param args.accountAddress - The address of the account whose modules are to be retrieved.
 * @param args.options - Optional parameters for pagination and ledger version.
 * @param args.options.cursor - The starting point for pagination.  Note, this is obfuscated and is not an index.
 * @param args.options.limit - The maximum number of modules to retrieve (default is 100).
 * @param args.options.ledgerVersion - The specific ledger version to query.
 * @group Implementation
 */
declare function getModulesPage(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: CursorPaginationArgs & LedgerVersionArg;
}): Promise<{
    modules: MoveModuleBytecode[];
    cursor: string | undefined;
}>;
/**
 * Queries for a move module given an account address and module name.
 * This function can help you retrieve the module's ABI and other relevant information.
 *
 * @param args - The arguments for retrieving the module.
 * @param args.aptosConfig - The configuration for the Aptos client.
 * @param args.accountAddress - The account address in hex-encoded 32 byte format.
 * @param args.moduleName - The name of the module to retrieve.
 * @param args.options - Optional parameters for the request.
 * @param args.options.ledgerVersion - Specifies the ledger version of transactions. By default, the latest version will be used.
 * @returns The move module.
 * @group Implementation
 */
declare function getModule(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    moduleName: string;
    options?: LedgerVersionArg;
}): Promise<MoveModuleBytecode>;
/**
 * Retrieves a list of transactions associated with a specific account address.
 * This function allows you to paginate through the transactions for better performance and usability.
 *
 * @param args - The arguments for retrieving transactions.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.accountAddress - The account address for which to retrieve transactions.
 * @param args.options - Optional pagination parameters.
 * @param args.options.offset - The starting point for pagination.
 * @param args.options.limit - The maximum number of transactions to retrieve.
 * @group Implementation
 */
declare function getTransactions(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: PaginationArgs;
}): Promise<TransactionResponse[]>;
/**
 * Retrieves a list of resources associated with a specific account address.
 *
 * @param args - The arguments for retrieving resources.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.accountAddress - The address of the account to fetch resources for.
 * @param args.options - Optional pagination and ledger version parameters.
 * @param args.options.limit - The maximum number of resources to retrieve (default is 999).
 * @param args.options.ledgerVersion - The specific ledger version to query.
 * @group Implementation
 */
declare function getResources(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: {
        limit?: number;
    } & LedgerVersionArg;
}): Promise<MoveResource[]>;
/**
 * Retrieves a page of resources associated with a specific account address.
 *
 * @param args - The arguments for retrieving resources.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.accountAddress - The address of the account to fetch resources for.
 * @param args.options - Optional pagination and ledger version parameters.
 * @param args.options.cursor - The starting point for pagination.  Note, this is obfuscated and is not an index.
 * @param args.options.limit - The maximum number of resources to retrieve (default is 100).
 * @param args.options.ledgerVersion - The specific ledger version to query.
 * @group Implementation
 */
declare function getResourcesPage(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: CursorPaginationArgs & LedgerVersionArg;
}): Promise<{
    resources: MoveResource[];
    cursor: string | undefined;
}>;
/**
 * Retrieves a specific resource of a given type for the specified account address.
 *
 * @param args - The arguments for retrieving the resource.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.accountAddress - The address of the account from which to retrieve the resource.
 * @param args.resourceType - The type of the resource to retrieve, specified as a MoveStructId.
 * @param args.options - Optional parameters for specifying the ledger version.
 * @group Implementation
 */
declare function getResource<T extends {}>(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    resourceType: MoveStructId;
    options?: LedgerVersionArg;
}): Promise<T>;
/**
 * Retrieves the original account address associated with a given authentication key, which is useful for handling key rotations.
 *
 * @param args - The arguments for the lookup.
 * @param args.aptosConfig - The configuration for the Aptos client.
 * @param args.authenticationKey - The authentication key for which to look up the original address.
 * @param args.options - Optional parameters for specifying the ledger version.
 * @returns The original account address or the provided authentication key address if not found.
 * @throws Throws an error if the lookup fails for reasons other than the address not being found.
 * @group Implementation
 */
declare function lookupOriginalAccountAddress(args: {
    aptosConfig: AptosConfig;
    authenticationKey: AccountAddressInput;
    options?: LedgerVersionArg;
}): Promise<AccountAddress>;
/**
 * Retrieves the count of tokens owned by a specific account address.
 *
 * @param args - The arguments for retrieving the account tokens count.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.accountAddress - The address of the account for which to count the tokens.
 * @returns The count of tokens owned by the specified account.
 * @group Implementation
 */
declare function getAccountTokensCount(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
}): Promise<number>;
/**
 * Retrieves the tokens owned by a specified account address.
 *
 * @param args - The arguments for retrieving the account's tokens.
 * @param args.aptosConfig - The configuration for the Aptos client.
 * @param args.accountAddress - The address of the account whose tokens are being queried.
 * @param args.options - Optional parameters for filtering and pagination.
 * @param args.options.tokenStandard - The specific token standard to filter the results.
 * @param args.options.offset - The number of records to skip before starting to collect the result set.
 * @param args.options.limit - The maximum number of records to return.
 * @param args.options.orderBy - The criteria for ordering the results.
 * @returns A promise that resolves to the current token ownerships of the specified account.
 * @group Implementation
 */
declare function getAccountOwnedTokens(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensQueryResponse[0]>;
}): Promise<GetAccountOwnedTokensQueryResponse>;
/**
 * Retrieves the tokens owned by a specific account from a particular collection address.
 *
 * @param args - The parameters required to fetch the owned tokens.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.accountAddress - The address of the account whose tokens are being queried.
 * @param args.collectionAddress - The address of the collection from which tokens are being retrieved.
 * @param args.options - Optional parameters for filtering and pagination, including token standard, pagination arguments, and
 * order by options.
 * @group Implementation
 */
declare function getAccountOwnedTokensFromCollectionAddress(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    collectionAddress: AccountAddressInput;
    options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountOwnedTokensFromCollectionResponse[0]>;
}): Promise<GetAccountOwnedTokensFromCollectionResponse>;
/**
 * Retrieves the collections owned by a specified account along with the tokens in those collections.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration for the Aptos client.
 * @param args.accountAddress - The address of the account whose collections are being queried.
 * @param args.options - Optional parameters for filtering and pagination.
 * @param args.options.tokenStandard - An optional token standard to filter the collections.
 * @param args.options.offset - An optional offset for pagination.
 * @param args.options.limit - An optional limit for the number of results returned.
 * @param args.options.orderBy - An optional parameter to specify the order of the results.
 * @group Implementation
 */
declare function getAccountCollectionsWithOwnedTokens(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: TokenStandardArg & PaginationArgs & OrderByArg<GetAccountCollectionsWithOwnedTokenResponse[0]>;
}): Promise<GetAccountCollectionsWithOwnedTokenResponse>;
/**
 * Retrieves the count of transactions associated with a specified account.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.accountAddress - The address of the account for which to retrieve the transaction count.
 * @returns The number of transactions associated with the specified account.
 * @group Implementation
 */
declare function getAccountTransactionsCount(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
}): Promise<number>;
/**
 * Retrieves the amount of a specific coin held by an account.
 *
 * @param args - The parameters for the request.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.accountAddress - The address of the account to query.
 * @param args.coinType - Optional; the type of coin to check the amount for.
 * @param args.faMetadataAddress - Optional; the address of the fungible asset metadata.
 * @returns The amount of the specified coin held by the account, or 0 if none is found.
 * @throws Error if neither coinType nor faMetadataAddress is provided.
 * @group Implementation
 */
declare function getAccountCoinAmount(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    coinType?: MoveStructId;
    faMetadataAddress?: AccountAddressInput;
}): Promise<number>;
/**
 * Retrieves the current fungible asset balances for a specified account.
 *
 * @param args - The arguments for retrieving account coins data.
 * @param args.aptosConfig - The configuration for connecting to the Aptos network.
 * @param args.accountAddress - The address of the account for which to retrieve coin data.
 * @param args.options - Optional parameters for pagination and filtering the results.
 * @param args.options.offset - The number of items to skip before starting to collect the result set.
 * @param args.options.limit - The maximum number of items to return.
 * @param args.options.orderBy - The criteria for ordering the results.
 * @param args.options.where - Conditions to filter the results based on the current fungible asset balances.
 * @group Implementation
 */
declare function getAccountCoinsData(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: PaginationArgs & OrderByArg<GetAccountCoinsDataResponse[0]> & WhereArg<CurrentFungibleAssetBalancesBoolExp>;
}): Promise<GetAccountCoinsDataResponse>;
/**
 * Retrieves the count of fungible asset coins held by a specified account.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.accountAddress - The address of the account for which to retrieve the coin count.
 * @throws Error if the count of account coins cannot be retrieved.
 * @group Implementation
 */
declare function getAccountCoinsCount(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
}): Promise<number>;
/**
 * Retrieves the objects owned by a specified account.
 *
 * @param args - The parameters for the request.
 * @param args.aptosConfig - The configuration for the Aptos client.
 * @param args.accountAddress - The address of the account whose owned objects are to be retrieved.
 * @param args.options - Optional parameters for pagination and ordering of the results.
 * @param args.options.offset - The number of items to skip before starting to collect the result set.
 * @param args.options.limit - The maximum number of items to return.
 * @param args.options.orderBy - The criteria to order the results by.
 * @returns A promise that resolves to the current objects owned by the specified account.
 * @group Implementation
 */
declare function getAccountOwnedObjects(args: {
    aptosConfig: AptosConfig;
    accountAddress: AccountAddressInput;
    options?: PaginationArgs & OrderByArg<GetObjectDataQueryResponse[0]>;
}): Promise<GetObjectDataQueryResponse>;
/**
 * Derives an account from the provided private key and Aptos configuration.
 * This function helps in obtaining the account details associated with a given private key,
 * considering both unified and legacy authentication schemes.
 *
 * NOTE: There is a potential issue once the unified single signer scheme is adopted by the community.
 * Because one could create two accounts with the same private key with this new authenticator type,
 * we’ll need to determine the order in which we look up the accounts: first unified scheme and then legacy scheme,
 * or first legacy scheme and then unified scheme.
 *
 * @param args - The arguments for deriving the account.
 * @param args.aptosConfig - The Aptos configuration used for account lookup.
 * @param args.privateKey - The private key used to derive the account.
 * @throws Error if the account cannot be derived from the private key.
 * @group Implementation
 * @deprecated Note that more inspection is needed by the user to determine which account exists on-chain
 */
declare function deriveAccountFromPrivateKey(args: {
    aptosConfig: AptosConfig;
    privateKey: PrivateKey;
}): Promise<Account>;
/**
 * Checks if an account exists by verifying its information against the Aptos blockchain.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration for connecting to the Aptos blockchain.
 * @param args.authKey - The authentication key used to derive the account address.
 * @returns A promise that resolves to a boolean indicating whether the account exists.
 *
 * @throws Throws an Error if there is an issue while looking for account information.
 * @group Implementation
 */
declare function isAccountExist(args: {
    aptosConfig: AptosConfig;
    authKey: AuthenticationKey;
}): Promise<boolean>;
/**
 * Rotates the authentication key for a given account.
 *
 * @param args - The arguments for rotating the authentication key.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.fromAccount - The account from which the authentication key will be rotated.
 * @param args.toAccount - (Optional) The target account to rotate to. Required if not using toNewPrivateKey or toAuthKey.
 * @param args.toNewPrivateKey - (Optional) The new private key to rotate to. Required if not using toAccount or toAuthKey.
 * @param args.toAuthKey - (Optional) The new authentication key to rotate to. Can only be used with dangerouslySkipVerification=true.
 * @param args.dangerouslySkipVerification - (Optional) If true, skips verification steps after rotation. Required when using toAuthKey.
 *
 * @remarks
 * This function supports three modes of rotation:
 * 1. Using a target Account object (toAccount)
 * 2. Using a new private key (toNewPrivateKey)
 * 3. Using a raw authentication key (toAuthKey) - requires dangerouslySkipVerification=true
 *
 * When not using dangerouslySkipVerification, the function performs additional safety checks and account setup.
 *
 * If the new key is a multi key, skipping verification is dangerous because verification will publish the public key onchain and
 * prevent users from being locked out of the account from loss of knowledge of one of the public keys.
 *
 * @returns A promise that resolves to the pending transaction response.
 * @throws Error if the rotation fails or verification fails.
 *
 * @group Implementation
 */
declare function rotateAuthKey(args: {
    aptosConfig: AptosConfig;
    fromAccount: Account;
} & ({
    toAccount: Account;
    dangerouslySkipVerification?: never;
} | {
    toNewPrivateKey: Ed25519PrivateKey;
    dangerouslySkipVerification?: never;
} | {
    toAuthKey: AuthenticationKey;
    dangerouslySkipVerification: true;
})): Promise<PendingTransactionResponse>;

export { deriveAccountFromPrivateKey, getAccountCoinAmount, getAccountCoinsCount, getAccountCoinsData, getAccountCollectionsWithOwnedTokens, getAccountOwnedObjects, getAccountOwnedTokens, getAccountOwnedTokensFromCollectionAddress, getAccountTokensCount, getAccountTransactionsCount, getInfo, getModule, getModules, getModulesPage, getResource, getResources, getResourcesPage, getTransactions, isAccountExist, lookupOriginalAccountAddress, rotateAuthKey };
