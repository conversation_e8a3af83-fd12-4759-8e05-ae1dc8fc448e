var e = require("typescript");

var a = require("@gql.tada/internal");

var r = require("@0no-co/graphqlsp/api");

var i = require("./index-chunk2.js");

var n = require("./index-chunk.js");

var t = i.expose((async function* _runTurbo(i) {
  var t = a.getSchemaNamesFromConfig(i.pluginConfig);
  var l = n.programFactory(i);
  l.addSourceFile({
    fileId: "__gql-tada-override__.d.ts",
    sourceText: s,
    scriptKind: e.ScriptKind.TS
  });
  var u = l.createExternalFiles();
  if (u.length) {
    yield {
      kind: "EXTERNAL_WARNING"
    };
    await l.addVirtualFiles(u);
  }
  var c = l.build();
  var d = c.buildPluginInfo(i.pluginConfig);
  var p = c.getSourceFiles();
  yield {
    kind: "FILE_COUNT",
    fileCount: p.length
  };
  var g = c.program.getTypeChecker();
  for (var m of p) {
    var f = m.fileName;
    var y = [];
    var T = [];
    var v = r.findAllCallExpressions(m, d, !1).nodes;
    for (var h of v) {
      var F = h.node.parent;
      if (!e.isCallExpression(F)) {
        continue;
      }
      var N = c.getSourcePosition(m, F.getStart());
      f = N.fileName;
      if (!t.has(h.schema)) {
        T.push({
          message: h.schema ? `The '${h.schema}' schema is not in the configuration but was referenced by document.` : t.size > 1 ? "The document is not for a known schema. Have you re-generated the output file?" : "Multiple schemas are configured, but the document is not for a specific schema.",
          file: N.fileName,
          line: N.line,
          col: N.col
        });
        continue;
      }
      var S = g.getTypeAtLocation(F);
      var b = g.getTypeAtLocation(h.node);
      if (!S.symbol || "TadaDocumentNode" !== S.symbol.getEscapedName()) {
        T.push({
          message: 'The discovered document is not of type "TadaDocumentNode".\nIf this is unexpected, please file an issue describing your case.',
          file: N.fileName,
          line: N.line,
          col: N.col
        });
        continue;
      }
      var q = "value" in b && "string" == typeof b.value && !(b.flags & e.TypeFlags.StringLiteral) ? JSON.stringify(b.value) : g.typeToString(b, F, o);
      var _ = g.typeToString(S, F, o);
      y.push({
        schemaName: h.schema,
        argumentKey: q,
        documentType: _
      });
    }
    yield {
      kind: "FILE_TURBO",
      filePath: f,
      documents: y,
      warnings: T
    };
  }
}));

var o = e.TypeFormatFlags.NoTruncation | e.TypeFormatFlags.NoTypeReduction | e.TypeFormatFlags.InTypeAlias | e.TypeFormatFlags.UseFullyQualifiedType | e.TypeFormatFlags.GenerateNamesForShadowedTypeParams | e.TypeFormatFlags.UseAliasDefinedOutsideCurrentScope | e.TypeFormatFlags.AllowUniqueESSymbolType | e.TypeFormatFlags.WriteTypeArgumentsOfSignature;

var s = "\nimport * as _gqlTada from 'gql.tada';\ndeclare module 'gql.tada' {\n  interface setupCache {\n    readonly __cacheDisabled: true;\n  }\n}\n".trim();

exports.runTurbo = t;
//# sourceMappingURL=thread-chunk2.js.map
