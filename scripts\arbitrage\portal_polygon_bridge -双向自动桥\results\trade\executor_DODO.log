2025-05-19 09:02:32,065 - INFO - ================================================================================
2025-05-19 09:02:32,065 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 09:02:32
2025-05-19 09:02:32,065 - INFO - 链: ethereum, 投入金额: 60.29 USDT
2025-05-19 09:02:32,065 - INFO - 代币地址: ******************************************
2025-05-19 09:02:32,065 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:60.29USDT
2025-05-19 09:02:32,065 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 09:02:32,066 - INFO - DODO: 准备使用KyberSwap在ethereum上执行60.29USDT买入DODO交易
2025-05-19 09:02:32,066 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 09:02:32,066 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 09:02:32,066 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 09:02:32,066 - INFO -   chain: ethereum
2025-05-19 09:02:32,066 - INFO -   token_in: USDT
2025-05-19 09:02:32,066 - INFO -   token_out: ******************************************
2025-05-19 09:02:32,066 - INFO -   amount: 60.29
2025-05-19 09:02:32,066 - INFO -   slippage: 0.5%
2025-05-19 09:02:32,066 - INFO -   real: True
2025-05-19 09:03:05,387 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-19 09:03:05,387 - INFO - CHAIN: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 6667.692734863269 ****************************************** 到地址 ******************************************', 'error': None, 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 207.55, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '207550000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '6667692734863268919637', 'amount_out': 6667.692734863269, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '207550000', 'amountInUsd': '207.71472941245096', 'tokenOut': '******************************************', 'amountOut': '6667692734863268919637', 'amountOutUsd': '207.59991518464454', 'gas': '260000', 'gasPrice': '591204878', 'gasUsd': '0.3755677176330642', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '207550000', 'amountOut': '84830376265495866', 'exchange': 'uniswap-v4', 'poolType': 'uniswap-v4', 'poolExtra': {'router': '0x66a9893cc07d91d95644aedd05d03f95e1dba8af', 'permit2Addr': '0x000000000022d473030f116ddee9f6b43ac78ba3', 'tokenIn': '******************************************', 'tokenOut': '0x0000000000000000000000000000000000000000', 'fee': 500, 'tickSpacing': 10, 'hookAddress': '0x0000000000000000000000000000000000000000', 'hookData': '', 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '3917976402987065780006745', 'ri': '904f1669-1605-46c3-b05f-6ba9e69331d8'}}, {'pool': '0x33906431e44553411b8668543ffc20aaa24f75f9', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '84830376265495866', 'amountOut': '6667692734863268919637', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22513523}, 'extra': None}]], 'routeID': '904f1669-1605-46c3-b05f-6ba9e69331d8', 'checksum': '16153237873536486003', 'timestamp': 1747616555}, 'tx_hash': '0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5', 'receipt': AttributeDict({'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'blockNumber': 22513614, 'contractAddress': None, 'cumulativeGasUsed': 6414154, 'effectiveGasPrice': 842416638, 'from': '******************************************', 'gasUsed': 352212, 'logs': [AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 150, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 151, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 152, 'removed': False}), AttributeDict({'address': '0x000000000022D473030F116dDEE9F6B43aC78BA3', 'topics': [HexBytes('0xda9fa7c1b00402c17d0161b249b1ab8bbec047c5a52207b9c112deffd817036b'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec7'), HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90')], 'data': HexBytes('0x000000000000000000000000ffffffffffffffffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000ffffffffffff'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 153, 'removed': False}), AttributeDict({'address': '0x000000000004444c5dc75cB358380D2e3dE08A90', 'topics': [HexBytes('0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f'), HexBytes('0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4fffffffffffffffffffffffffffffffffffffffffffffffffffffffff3a109d0000000000000000000000000000000000000000000033da9ed281c176c44ef59000000000000000000000000000000000000000000000000043a9364558d8891fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcf96300000000000000000000000000000000000000000000000000000000000001f4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 154, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 155, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 156, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90000000000000000000000000000000000000000000000000012d60019920f8e40000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 157, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 158, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 159, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x000000000000000000000000000000000000000000000003a5e4a1f2533615c3000000000000000000000000000000000000000000046200d5120a369cc9a32a'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 160, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e40000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 161, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f900000000000000000000000000000000000000000000016972f9b8d1194fe624000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 162, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 163, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec7000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000c5ef63000000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 164, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec600000000000000000000000000000000000000000000016972f9b8d1194fe624000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 165, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002977b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223230372e3731383236363735313738363235222c22416d6f756e744f7574555344223a223230372e3539393931353138343634343534222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2236363637363932373334383633323638393139363337222c2254696d657374616d70223a313734373631363536342c22526f7574654944223a2231353133663938612d643939622d343030302d383636652d6436646539353730633762643a35306435313833632d646339392d346336652d613331642d633034313863666166376333222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a2258515879444156307065494e68767a3871727379614a62596168546d4b4e4d446c373264625a6c302b592f33454b7536444f766c2b36787a43507362354731716d6358336f4d6e2f6775614f6c2b39737368716a73574e2b746c6e304e4a2f786933743046693275514d6f4c655836374872704b6d684d2b39583576674b6d5746716a384c3445797834596a6e30675569725258565a5a6c744c575076445951755a6e6331556a7643597a634d78366557667249624d6a4939705533695672416b787741424c7475346e315055597157715730314247696448386d71375551386d36357750724a2b4e326d5668325a454c4f5a786c5247426f6c6c3031554b2f627931562f57414a446a45654d5a784673664243653053676b32333436684a564d6774766271674847544d7731594b623768724f30644f6c5a485257596568414b514b35326c6a33744d336d675666593269387a6a413d3d227d7d000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 166, 'removed': False})], 'logsBloom': HexBytes('0x002120000100000000000000800000000000000000000000000000002001000000000010200000000010000000000100060000008c0000000000000000280000000000000080000000004028000100200020002000000820000000008020000000000000000000000000000000002000000000200000000000000010000200020022000800000000040000000000000000000001000008080000084000100000020000000000000000000080100800000000400000000000004000080000004000000802100000000000000000201000000000420400001040000000000000000010200000802000004000001000000000408200010000400000000000000400'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'type': 0})}
2025-05-19 09:03:05,389 - INFO - CHAIN: 交易消息: 交换完成，接收了 6667.692734863269 ****************************************** 到地址 ******************************************
2025-05-19 09:03:05,389 - INFO - CHAIN: 从消息中提取到代币数量: 6667.692734863269
2025-05-19 09:03:05,389 - INFO - CHAIN: 交易成功 - 哈希: 0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5
2025-05-19 09:03:05,389 - INFO - CHAIN: 最终确认的购买数量: 6667.692734863269
2025-05-19 09:03:05,390 - INFO - 读取到 124 条现有交易记录
2025-05-19 09:03:05,390 - INFO - 添加新交易记录: CHAIN (CHAIN_207.55_2025-05-19 09:02:29)
2025-05-19 09:03:05,393 - INFO - 成功保存 125 条交易记录
2025-05-19 09:03:05,393 - INFO - CHAIN: 交易成功，从区块链获取实际买入数量...
2025-05-19 09:03:05,401 - INFO - CHAIN: 使用tx_token_change_tracker获取交易 0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5 的代币数量...
2025-05-19 09:03:08,925 - INFO - CHAIN: 从区块链获取到实际买入数量: 6667.************ CHAIN
2025-05-19 09:03:12,481 - INFO - CHAIN: 开始从以太坊桥接到Polygon...
2025-05-19 09:03:25,806 - ERROR - CHAIN: 桥接失败: 错误: 无法获取代币信息，请确保这是一个有效的ERC20代币: 发送交易失败: 'PolygonEthereumBridge' object has no attribute 'account'
2025-05-19 09:03:25,806 - INFO - CHAIN: 等待桥接完成...
2025-05-19 09:03:25,806 - WARNING - CHAIN: 未获取到Polygon到账交易哈希
2025-05-19 09:03:25,807 - INFO - CHAIN: 桥接操作完成
2025-05-19 09:03:25,807 - INFO - CHAIN: 桥接操作完成，结果: {'success': False, 'message': "错误: 无法获取代币信息，请确保这是一个有效的ERC20代币: 发送交易失败: 'PolygonEthereumBridge' object has no attribute 'account'", 'bridge_tx': None}
2025-05-19 09:03:25,807 - INFO - CHAIN: 买入交易处理完成，耗时: 56.23秒
2025-05-19 09:03:25,807 - INFO - ================================================================================
2025-05-19 09:05:51,666 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 09:05:51,667 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '未知', 'message': '交易已发送但确认状态未知，请手动检查', 'error': '未知错误', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 60.29, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '********', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '1138054553177174492129', 'amount_out': 1138.*************, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '********', 'amountInUsd': '60.3378512949972', 'tokenOut': '******************************************', 'amountOut': '1138054553177174492129', 'amountOutUsd': '60.657547386479294', 'gas': '260000', 'gasPrice': '591204878', 'gasUsd': '0.3755677176330642', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '********', 'amountOut': '24642126432708574', 'exchange': 'uniswap-v4', 'poolType': 'uniswap-v4', 'poolExtra': {'router': '0x66a9893cc07d91d95644aedd05d03f95e1dba8af', 'permit2Addr': '0x000000000022d473030f116ddee9f6b43ac78ba3', 'tokenIn': '******************************************', 'tokenOut': '0x0000000000000000000000000000000000000000', 'fee': 500, 'tickSpacing': 10, 'hookAddress': '0x0000000000000000000000000000000000000000', 'hookData': '', 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '3917938133812757861621044', 'ri': '7a48e651-1f72-4be9-ae09-af9f7e9980c6'}}, {'pool': '0x68fa181c720c07b7ff7412220e2431ce90a65a14', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '24642126432708574', 'amountOut': '1138054553177174492129', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22513586}, 'extra': None}]], 'routeID': '7a48e651-1f72-4be9-ae09-af9f7e9980c6', 'checksum': '6446749815851377278', 'timestamp': 1747616560}}
2025-05-19 09:05:51,667 - ERROR - DODO: 交易失败 - 未知错误
2025-05-19 09:05:51,668 - INFO - 读取到 125 条现有交易记录
2025-05-19 09:05:51,668 - INFO - 添加新交易记录: DODO (DODO_60.29_2025-05-19 09:02:32)
2025-05-19 09:05:51,670 - INFO - 成功保存 126 条交易记录
2025-05-19 09:05:51,670 - INFO - DODO: 买入交易处理完成，耗时: 199.61秒
2025-05-19 09:05:51,670 - INFO - ================================================================================
2025-05-19 10:13:51,143 - INFO - ================================================================================
2025-05-19 10:13:51,143 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 10:13:51
2025-05-19 10:13:51,143 - INFO - 链: ethereum, 投入金额: 75.36 USDT
2025-05-19 10:13:51,144 - INFO - 代币地址: ******************************************
2025-05-19 10:13:51,144 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:75.36USDT
2025-05-19 10:13:51,144 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:13:51,144 - INFO - DODO: 准备使用KyberSwap在ethereum上执行75.36USDT买入DODO交易
2025-05-19 10:13:51,144 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:13:51,144 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:13:51,144 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 10:13:51,144 - INFO -   chain: ethereum
2025-05-19 10:13:51,144 - INFO -   token_in: USDT
2025-05-19 10:13:51,144 - INFO -   token_out: ******************************************
2025-05-19 10:13:51,144 - INFO -   amount: 75.36
2025-05-19 10:13:51,144 - INFO -   slippage: 0.5%
2025-05-19 10:13:51,144 - INFO -   real: True
2025-05-19 10:13:52,908 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:13:52,908 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 75.36, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '75360000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:13:52,908 - ERROR - DODO: 交易失败 - 代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT
2025-05-19 10:13:52,909 - INFO - 读取到 129 条现有交易记录
2025-05-19 10:13:52,910 - INFO - 添加新交易记录: DODO (DODO_75.36_2025-05-19 10:13:51)
2025-05-19 10:13:52,913 - INFO - 成功保存 130 条交易记录
2025-05-19 10:13:52,913 - INFO - DODO: 买入交易处理完成，耗时: 1.77秒
2025-05-19 10:13:52,913 - INFO - ================================================================================
2025-05-19 10:54:06,242 - INFO - ================================================================================
2025-05-19 10:54:06,242 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 10:54:06
2025-05-19 10:54:06,242 - INFO - 链: ethereum, 投入金额: 75.36 USDT
2025-05-19 10:54:06,243 - INFO - 代币地址: ******************************************
2025-05-19 10:54:06,243 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:75.36USDT
2025-05-19 10:54:06,243 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:54:06,243 - INFO - DODO: 准备使用KyberSwap在ethereum上执行75.36USDT买入DODO交易
2025-05-19 10:54:06,243 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:54:06,243 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:54:06,244 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 10:54:06,244 - INFO -   chain: ethereum
2025-05-19 10:54:06,244 - INFO -   token_in: USDT
2025-05-19 10:54:06,244 - INFO -   token_out: ******************************************
2025-05-19 10:54:06,244 - INFO -   amount: 75.36
2025-05-19 10:54:06,244 - INFO -   slippage: 0.5%
2025-05-19 10:54:06,244 - INFO -   real: True
2025-05-19 10:54:08,192 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:54:08,192 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 75.36, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '75360000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:54:08,192 - ERROR - DODO: 交易失败 - 代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT
2025-05-19 10:54:08,192 - INFO - 读取到 133 条现有交易记录
2025-05-19 10:54:08,192 - INFO - 添加新交易记录: DODO (DODO_75.36_2025-05-19 10:54:06)
2025-05-19 10:54:08,196 - INFO - 成功保存 134 条交易记录
2025-05-19 10:54:08,196 - INFO - DODO: 买入交易处理完成，耗时: 1.95秒
2025-05-19 10:54:08,196 - INFO - ================================================================================
2025-05-19 11:25:15,049 - INFO - ================================================================================
2025-05-19 11:25:15,049 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 11:25:15
2025-05-19 11:25:15,049 - INFO - 链: ethereum, 投入金额: 75.36 USDT
2025-05-19 11:25:15,049 - INFO - 代币地址: ******************************************
2025-05-19 11:25:15,049 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:75.36USDT
2025-05-19 11:25:15,049 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:25:15,050 - INFO - DODO: 准备使用KyberSwap在ethereum上执行75.36USDT买入DODO交易
2025-05-19 11:25:15,050 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:25:15,050 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:25:15,050 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 11:25:15,050 - INFO -   chain: ethereum
2025-05-19 11:25:15,050 - INFO -   token_in: USDT
2025-05-19 11:25:15,050 - INFO -   token_out: ******************************************
2025-05-19 11:25:15,050 - INFO -   amount: 75.36
2025-05-19 11:25:15,050 - INFO -   slippage: 0.5%
2025-05-19 11:25:15,050 - INFO -   real: True
2025-05-19 11:25:17,483 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:25:17,483 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 75.36, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '75360000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:25:17,483 - ERROR - DODO: 交易失败 - 代币余额不足。请求: 75.36 USDT，可用: 67.81312 USDT
2025-05-19 11:25:17,484 - INFO - 读取到 138 条现有交易记录
2025-05-19 11:25:17,484 - INFO - 添加新交易记录: DODO (DODO_75.36_2025-05-19 11:25:15)
2025-05-19 11:25:17,486 - INFO - 成功保存 139 条交易记录
2025-05-19 11:25:17,486 - INFO - DODO: 买入交易处理完成，耗时: 2.44秒
2025-05-19 11:25:17,486 - INFO - ================================================================================
2025-05-19 12:52:36,192 - INFO - ================================================================================
2025-05-19 12:52:36,192 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 12:52:36
2025-05-19 12:52:36,192 - INFO - 链: ethereum, 投入金额: 105.5 USDT
2025-05-19 12:52:36,192 - INFO - 代币地址: ******************************************
2025-05-19 12:52:36,192 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:105.5USDT
2025-05-19 12:52:36,192 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:52:36,192 - INFO - DODO: 准备使用KyberSwap在ethereum上执行105.5USDT买入DODO交易
2025-05-19 12:52:36,192 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:52:36,192 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:52:36,193 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 12:52:36,193 - INFO -   chain: ethereum
2025-05-19 12:52:36,193 - INFO -   token_in: USDT
2025-05-19 12:52:36,193 - INFO -   token_out: ******************************************
2025-05-19 12:52:36,193 - INFO -   amount: 105.5
2025-05-19 12:52:36,193 - INFO -   slippage: 0.5%
2025-05-19 12:52:36,193 - INFO -   real: True
2025-05-19 12:52:38,160 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:52:38,160 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 105.5 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 105.5, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '105500000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:52:38,161 - ERROR - DODO: 交易失败 - 代币余额不足。请求: 105.5 USDT，可用: 67.81312 USDT
2025-05-19 12:52:38,163 - INFO - 读取到 143 条现有交易记录
2025-05-19 12:52:38,163 - INFO - 添加新交易记录: DODO (DODO_105.5_2025-05-19 12:52:36)
2025-05-19 12:52:38,165 - INFO - 成功保存 144 条交易记录
2025-05-19 12:52:38,165 - INFO - DODO: 买入交易处理完成，耗时: 1.97秒
2025-05-19 12:52:38,165 - INFO - ================================================================================
2025-05-19 14:37:03,025 - INFO - ================================================================================
2025-05-19 14:37:03,025 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 14:37:03
2025-05-19 14:37:03,025 - INFO - 链: ethereum, 投入金额: 105.5 USDT
2025-05-19 14:37:03,025 - INFO - 代币地址: ******************************************
2025-05-19 14:37:03,025 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:105.5USDT
2025-05-19 14:37:03,025 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 14:37:03,026 - INFO - DODO: 准备使用KyberSwap在ethereum上执行105.5USDT买入DODO交易
2025-05-19 14:37:03,026 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 14:37:03,026 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 14:37:03,026 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 14:37:03,026 - INFO -   chain: ethereum
2025-05-19 14:37:03,026 - INFO -   token_in: USDT
2025-05-19 14:37:03,026 - INFO -   token_out: ******************************************
2025-05-19 14:37:03,026 - INFO -   amount: 105.5
2025-05-19 14:37:03,026 - INFO -   slippage: 0.5%
2025-05-19 14:37:03,026 - INFO -   real: True
2025-05-19 14:37:46,800 - INFO - 已在新线程 Trade-PLOT 中启动PLOT交易
2025-05-19 14:37:46,801 - INFO - ================================================================================
2025-05-19 14:37:46,802 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 14:37:46
2025-05-19 14:37:46,802 - INFO - 链: ethereum, 投入金额: 233.49 USDT
2025-05-19 14:37:46,802 - INFO - 代币地址: ******************************************
2025-05-19 14:37:46,802 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:233.49USDT
2025-05-19 14:37:46,802 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 14:37:46,802 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行233.49USDT买入PLOT交易
2025-05-19 14:37:46,802 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 14:37:46,802 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 14:37:46,802 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 14:37:46,802 - INFO -   chain: ethereum
2025-05-19 14:37:46,802 - INFO -   token_in: USDT
2025-05-19 14:37:46,802 - INFO -   token_out: ******************************************
2025-05-19 14:37:46,802 - INFO -   amount: 233.49
2025-05-19 14:37:46,802 - INFO -   slippage: 0.5%
2025-05-19 14:37:46,803 - INFO -   real: True
2025-05-19 14:37:58,890 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 14:37:58,891 - INFO - DODO: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 2082.314490954566 ****************************************** 到地址 ******************************************', 'error': None, 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 105.5, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '105500000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '2082314490954565679763', 'amount_out': 2082.314490954566, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '105500000', 'amountInUsd': '105.6569703345483', 'tokenOut': '******************************************', 'amountOut': '2082314490954565679763', 'amountOutUsd': '106.30974187143198', 'gas': '270000', 'gasPrice': '634499686', 'gasUsd': '0.40702206038646804', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '******************************************', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '105500000', 'amountOut': '44485320852446528', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 100, 'priceLimit': '1461446703485210103287273052203988822378723970341'}, 'extra': {'nSqrtRx96': '3858165122369143791099021', 'ri': '930ca437-3d21-4dc2-a676-191580b77c5f'}}, {'pool': '0x68fa181c720c07b7ff7412220e2431ce90a65a14', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '44485320852446528', 'amountOut': '2082314490954565679763', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22515257}, 'extra': None}]], 'routeID': '930ca437-3d21-4dc2-a676-191580b77c5f', 'checksum': '6654648624114982781', 'timestamp': 1747636630}, 'tx_hash': '0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61', 'receipt': AttributeDict({'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'blockNumber': 22515273, 'contractAddress': None, 'cumulativeGasUsed': 18887547, 'effectiveGasPrice': 906545792, 'from': '******************************************', 'gasUsed': 300053, 'logs': [AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000649cd60'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 585, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000009e273ae6763e41'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 586, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000649cd60'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 587, 'removed': False}), AttributeDict({'address': '0xc7bBeC68d12a0d1830360F8Ec58fA599bA1b0e9b', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffff61d8c51989c1bf000000000000000000000000000000000000000000000000000000000649cd600000000000000000000000000000000000000000000330b6055570cb905d3c41000000000000000000000000000000000000000000000000023678e8200d8fc2fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcf829'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 588, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b000000000000000000000000000000000000000000000000009e273ae6763e41000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 589, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000009e273ae6763e41'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 590, 'removed': False}), AttributeDict({'address': '0x43Dfc4159D86F3A37A5A4B3D4580b888ad7d4DDd', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 591, 'removed': False}), AttributeDict({'address': '0x68Fa181c720C07B7FF7412220E2431ce90A65A14', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x00000000000000000000000000000000000000000000e7ce8da64750210ed07000000000000000000000000000000000000000000000000144a43ae63edd7454'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 592, 'removed': False}), AttributeDict({'address': '0x68Fa181c720C07B7FF7412220E2431ce90A65A14', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009e273ae6763e41000000000000000000000000000000000000000000000070cdb4373336b3fe520000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 593, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14000000000000000000000000000000000000000000000070cdb4373336b3fe5200000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 594, 'removed': False}), AttributeDict({'address': '0x43Dfc4159D86F3A37A5A4B3D4580b888ad7d4DDd', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 595, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec700000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000649cd60000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 596, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6000000000000000000000000000000000000000000000070cdb4373336b3fe5200000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 597, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002967b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223130352e36353639373033333435343833222c22416d6f756e744f7574555344223a223130362e3330393734313837313433313938222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2232303832333134343930393534353635363739373633222c2254696d657374616d70223a313734373633363633372c22526f7574654944223a2234393530643538662d306361662d343263392d386265362d6265613166613935633835363a30396666666562352d383764642d343930362d616135382d303931313435333861343465222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a2263394a54336b38714b6a65304d7a383864387557336b31416275516e51693451306e676a50366e72576e7a6352564a6e63786c4757524f436b49424a7778384747444a48756b557554337a57654b3675575a576b4e6e577867437531724b733430774d4c5355485a7a36724134586b6a694e6c533034753457372b54716571736d57774e696a4c7358434b512f2f5a72787953464b305747566637345a477975693930384e39504855684c4f53625530644536683374494466336a394e6545794b6e5548544e54534a623039434d44504d39584e4d59513346655a446f4532435448465967374c486a536d58586846684c4a616f57476d51364c713565596374627a446832704667442b67315a4d6c53547934575a4f6d74432f4f4a363753432b776a7459656a624a504a4f787974386a586e366b3339392f63716746537a4b707a58716c4479306c3534487a72576a3056655533413d3d227d7d00000000000000000000'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 598, 'removed': False})], 'logsBloom': HexBytes('0x00202000010000000000000080000000000000000000000000000000200000000000001010000000000000000000010006000000080220000080000000000000000000000000000800000028000000200000003000000000000000000020000000000000002000000000000000000000000000000000004000000010000a00020000000000000000060000000000000000000000400008080000004000100000000000000000000000000080000800000000400000000000004000000000000000000002000008000000000000201c00000000000401001000400000000000020000200000002000040000000000000000008000010000000000000000000000'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'type': 0})}
2025-05-19 14:37:58,892 - INFO - DODO: 交易消息: 交换完成，接收了 2082.314490954566 ****************************************** 到地址 ******************************************
2025-05-19 14:37:58,894 - INFO - DODO: 从消息中提取到代币数量: 2082.314490954566
2025-05-19 14:37:58,894 - INFO - DODO: 交易成功 - 哈希: 0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61
2025-05-19 14:37:58,894 - INFO - DODO: 最终确认的购买数量: 2082.314490954566
2025-05-19 14:37:58,895 - INFO - 读取到 146 条现有交易记录
2025-05-19 14:37:58,896 - INFO - 添加新交易记录: DODO (DODO_105.5_2025-05-19 14:37:03)
2025-05-19 14:37:58,898 - INFO - 成功保存 147 条交易记录
2025-05-19 14:37:58,898 - INFO - DODO: 交易成功，从区块链获取实际买入数量...
2025-05-19 14:37:58,904 - INFO - DODO: 使用tx_token_change_tracker获取交易 0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61 的代币数量...
2025-05-19 14:37:59,849 - INFO - DODO: 从区块链获取到实际买入数量: 2080.8578692221536 DODO
2025-05-19 14:38:01,653 - INFO - DODO: 开始从以太坊桥接到Polygon...
2025-05-19 14:38:01,657 - ERROR - DODO: 桥接失败: Bridge未正确初始化，请检查private_key是否正确传入
2025-05-19 14:38:01,658 - INFO - DODO: 等待桥接完成...
2025-05-19 14:38:01,658 - WARNING - DODO: 未获取到Polygon到账交易哈希
2025-05-19 14:38:01,658 - INFO - DODO: 桥接操作完成
2025-05-19 14:38:01,658 - INFO - DODO: 桥接操作完成，结果: {'success': False, 'message': 'Bridge未正确初始化，请检查private_key是否正确传入', 'bridge_tx': None}
2025-05-19 14:38:01,659 - INFO - DODO: 买入交易处理完成，耗时: 58.63秒
2025-05-19 14:38:01,659 - INFO - ================================================================================
2025-05-19 15:10:10,371 - INFO - ================================================================================
2025-05-19 15:10:10,371 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 15:10:10
2025-05-19 15:10:10,371 - INFO - 链: ethereum, 投入金额: 135.65 USDT
2025-05-19 15:10:10,371 - INFO - 代币地址: ******************************************
2025-05-19 15:10:10,371 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:135.65USDT
2025-05-19 15:10:10,371 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 15:10:10,372 - INFO - DODO: 准备使用KyberSwap在ethereum上执行135.65USDT买入DODO交易
2025-05-19 15:10:10,372 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 15:10:10,372 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 15:10:10,372 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 15:10:10,372 - INFO -   chain: ethereum
2025-05-19 15:10:10,372 - INFO -   token_in: USDT
2025-05-19 15:10:10,372 - INFO -   token_out: ******************************************
2025-05-19 15:10:10,372 - INFO -   amount: 135.65
2025-05-19 15:10:10,372 - INFO -   slippage: 0.5%
2025-05-19 15:10:10,372 - INFO -   real: True
2025-05-19 15:10:37,880 - INFO - 已在新线程 Trade-DWEB 中启动DWEB交易
2025-05-19 15:10:37,881 - INFO - ================================================================================
2025-05-19 15:10:37,881 - INFO - 开始执行 DWEB 买入交易 - 时间: 2025-05-19 15:10:37
2025-05-19 15:10:37,881 - INFO - 链: ethereum, 投入金额: 92.69 USDT
2025-05-19 15:10:37,882 - INFO - 代币地址: ******************************************
2025-05-19 15:10:37,882 - INFO - 收到DWEB买入请求 - 链:ethereum, 投入:92.69USDT
2025-05-19 15:10:37,882 - INFO - DWEB: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 15:10:37,882 - INFO - DWEB: 准备使用KyberSwap在ethereum上执行92.69USDT买入DWEB交易
2025-05-19 15:10:37,882 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 15:10:37,882 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 15:10:37,882 - INFO - DWEB: 准备调用swap_tokens函数，参数：
2025-05-19 15:10:37,882 - INFO -   chain: ethereum
2025-05-19 15:10:37,882 - INFO -   token_in: USDT
2025-05-19 15:10:37,882 - INFO -   token_out: ******************************************
2025-05-19 15:10:37,882 - INFO -   amount: 92.69
2025-05-19 15:10:37,882 - INFO -   slippage: 0.5%
2025-05-19 15:10:37,882 - INFO -   real: True
2025-05-19 15:10:49,791 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 15:10:49,792 - INFO - DODO: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '交易已上链但执行失败', 'error': '未知错误', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 135.65, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '135650000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '2694142039906938219796', 'amount_out': 2694.1420399069384, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '135650000', 'amountInUsd': '134.752691529302', 'tokenOut': '******************************************', 'amountOut': '2694142039906938219796', 'amountOutUsd': '136.7235348892546', 'gas': '330000', 'gasPrice': '1406647834', 'gasUsd': '1.1055869659621367', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '******************************************', 'tokenIn': '******************************************', 'tokenOut': '0x2260fac5e5542a773aa44fbcfedf7c193bc2c599', 'swapAmount': '135650000', 'amountOut': '132355', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 500, 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '2535781482979592906095480773396', 'ri': '259e64ed-699b-4404-8006-e5ed04f23857'}}, {'pool': '0x4ab6702b3ed3877e9b1f203f90cbef13d663b0e8', 'tokenIn': '0x2260fac5e5542a773aa44fbcfedf7c193bc2c599', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '132355', 'amountOut': '57026962247116280', 'exchange': 'pancake', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 25, 'feePrecision': 10000, 'blockNumber': 22515372}, 'extra': None}, {'pool': '0x68fa181c720c07b7ff7412220e2431ce90a65a14', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '57026962247116280', 'amountOut': '2694142039906938219796', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22515395}, 'extra': None}]], 'routeID': '259e64ed-699b-4404-8006-e5ed04f23857', 'checksum': '2316874049104655073', 'timestamp': 1747638618}}
2025-05-19 15:10:49,792 - ERROR - DODO: 交易失败 - 未知错误
2025-05-19 15:10:49,793 - INFO - 读取到 150 条现有交易记录
2025-05-19 15:10:49,793 - INFO - 添加新交易记录: DODO (DODO_135.65_2025-05-19 15:10:10)
2025-05-19 15:10:49,796 - INFO - 成功保存 151 条交易记录
2025-05-19 15:10:49,796 - INFO - DODO: 买入交易处理完成，耗时: 39.43秒
2025-05-19 15:10:49,796 - INFO - ================================================================================
