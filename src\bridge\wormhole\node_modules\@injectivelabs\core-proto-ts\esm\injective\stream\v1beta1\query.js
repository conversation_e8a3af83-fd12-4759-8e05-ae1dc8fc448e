/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import Long from "long";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
import { share } from "rxjs/operators";
import { Coin } from "../../../cosmos/base/v1beta1/coin.js";
import { Deposit, DerivativeLimitOrder, Level, PositionDelta, SpotLimitOrder } from "../../exchange/v1beta1/exchange.js";
export const protobufPackage = "injective.stream.v1beta1";
export var OrderUpdateStatus;
(function (OrderUpdateStatus) {
    OrderUpdateStatus[OrderUpdateStatus["Unspecified"] = 0] = "Unspecified";
    OrderUpdateStatus[OrderUpdateStatus["Booked"] = 1] = "Booked";
    OrderUpdateStatus[OrderUpdateStatus["Matched"] = 2] = "Matched";
    OrderUpdateStatus[OrderUpdateStatus["Cancelled"] = 3] = "Cancelled";
    OrderUpdateStatus[OrderUpdateStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OrderUpdateStatus || (OrderUpdateStatus = {}));
export function orderUpdateStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return OrderUpdateStatus.Unspecified;
        case 1:
        case "Booked":
            return OrderUpdateStatus.Booked;
        case 2:
        case "Matched":
            return OrderUpdateStatus.Matched;
        case 3:
        case "Cancelled":
            return OrderUpdateStatus.Cancelled;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OrderUpdateStatus.UNRECOGNIZED;
    }
}
export function orderUpdateStatusToJSON(object) {
    switch (object) {
        case OrderUpdateStatus.Unspecified:
            return "Unspecified";
        case OrderUpdateStatus.Booked:
            return "Booked";
        case OrderUpdateStatus.Matched:
            return "Matched";
        case OrderUpdateStatus.Cancelled:
            return "Cancelled";
        case OrderUpdateStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseStreamRequest() {
    return {
        bankBalancesFilter: undefined,
        subaccountDepositsFilter: undefined,
        spotTradesFilter: undefined,
        derivativeTradesFilter: undefined,
        spotOrdersFilter: undefined,
        derivativeOrdersFilter: undefined,
        spotOrderbooksFilter: undefined,
        derivativeOrderbooksFilter: undefined,
        positionsFilter: undefined,
        oraclePriceFilter: undefined,
    };
}
export const StreamRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.bankBalancesFilter !== undefined) {
            BankBalancesFilter.encode(message.bankBalancesFilter, writer.uint32(10).fork()).ldelim();
        }
        if (message.subaccountDepositsFilter !== undefined) {
            SubaccountDepositsFilter.encode(message.subaccountDepositsFilter, writer.uint32(18).fork()).ldelim();
        }
        if (message.spotTradesFilter !== undefined) {
            TradesFilter.encode(message.spotTradesFilter, writer.uint32(26).fork()).ldelim();
        }
        if (message.derivativeTradesFilter !== undefined) {
            TradesFilter.encode(message.derivativeTradesFilter, writer.uint32(34).fork()).ldelim();
        }
        if (message.spotOrdersFilter !== undefined) {
            OrdersFilter.encode(message.spotOrdersFilter, writer.uint32(42).fork()).ldelim();
        }
        if (message.derivativeOrdersFilter !== undefined) {
            OrdersFilter.encode(message.derivativeOrdersFilter, writer.uint32(50).fork()).ldelim();
        }
        if (message.spotOrderbooksFilter !== undefined) {
            OrderbookFilter.encode(message.spotOrderbooksFilter, writer.uint32(58).fork()).ldelim();
        }
        if (message.derivativeOrderbooksFilter !== undefined) {
            OrderbookFilter.encode(message.derivativeOrderbooksFilter, writer.uint32(66).fork()).ldelim();
        }
        if (message.positionsFilter !== undefined) {
            PositionsFilter.encode(message.positionsFilter, writer.uint32(74).fork()).ldelim();
        }
        if (message.oraclePriceFilter !== undefined) {
            OraclePriceFilter.encode(message.oraclePriceFilter, writer.uint32(82).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bankBalancesFilter = BankBalancesFilter.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.subaccountDepositsFilter = SubaccountDepositsFilter.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.spotTradesFilter = TradesFilter.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.derivativeTradesFilter = TradesFilter.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.spotOrdersFilter = OrdersFilter.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.derivativeOrdersFilter = OrdersFilter.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.spotOrderbooksFilter = OrderbookFilter.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.derivativeOrderbooksFilter = OrderbookFilter.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.positionsFilter = PositionsFilter.decode(reader, reader.uint32());
                    break;
                case 10:
                    message.oraclePriceFilter = OraclePriceFilter.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            bankBalancesFilter: isSet(object.bankBalancesFilter)
                ? BankBalancesFilter.fromJSON(object.bankBalancesFilter)
                : undefined,
            subaccountDepositsFilter: isSet(object.subaccountDepositsFilter)
                ? SubaccountDepositsFilter.fromJSON(object.subaccountDepositsFilter)
                : undefined,
            spotTradesFilter: isSet(object.spotTradesFilter) ? TradesFilter.fromJSON(object.spotTradesFilter) : undefined,
            derivativeTradesFilter: isSet(object.derivativeTradesFilter)
                ? TradesFilter.fromJSON(object.derivativeTradesFilter)
                : undefined,
            spotOrdersFilter: isSet(object.spotOrdersFilter) ? OrdersFilter.fromJSON(object.spotOrdersFilter) : undefined,
            derivativeOrdersFilter: isSet(object.derivativeOrdersFilter)
                ? OrdersFilter.fromJSON(object.derivativeOrdersFilter)
                : undefined,
            spotOrderbooksFilter: isSet(object.spotOrderbooksFilter)
                ? OrderbookFilter.fromJSON(object.spotOrderbooksFilter)
                : undefined,
            derivativeOrderbooksFilter: isSet(object.derivativeOrderbooksFilter)
                ? OrderbookFilter.fromJSON(object.derivativeOrderbooksFilter)
                : undefined,
            positionsFilter: isSet(object.positionsFilter) ? PositionsFilter.fromJSON(object.positionsFilter) : undefined,
            oraclePriceFilter: isSet(object.oraclePriceFilter)
                ? OraclePriceFilter.fromJSON(object.oraclePriceFilter)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.bankBalancesFilter !== undefined && (obj.bankBalancesFilter = message.bankBalancesFilter
            ? BankBalancesFilter.toJSON(message.bankBalancesFilter)
            : undefined);
        message.subaccountDepositsFilter !== undefined && (obj.subaccountDepositsFilter = message.subaccountDepositsFilter
            ? SubaccountDepositsFilter.toJSON(message.subaccountDepositsFilter)
            : undefined);
        message.spotTradesFilter !== undefined &&
            (obj.spotTradesFilter = message.spotTradesFilter ? TradesFilter.toJSON(message.spotTradesFilter) : undefined);
        message.derivativeTradesFilter !== undefined && (obj.derivativeTradesFilter = message.derivativeTradesFilter
            ? TradesFilter.toJSON(message.derivativeTradesFilter)
            : undefined);
        message.spotOrdersFilter !== undefined &&
            (obj.spotOrdersFilter = message.spotOrdersFilter ? OrdersFilter.toJSON(message.spotOrdersFilter) : undefined);
        message.derivativeOrdersFilter !== undefined && (obj.derivativeOrdersFilter = message.derivativeOrdersFilter
            ? OrdersFilter.toJSON(message.derivativeOrdersFilter)
            : undefined);
        message.spotOrderbooksFilter !== undefined && (obj.spotOrderbooksFilter = message.spotOrderbooksFilter
            ? OrderbookFilter.toJSON(message.spotOrderbooksFilter)
            : undefined);
        message.derivativeOrderbooksFilter !== undefined &&
            (obj.derivativeOrderbooksFilter = message.derivativeOrderbooksFilter
                ? OrderbookFilter.toJSON(message.derivativeOrderbooksFilter)
                : undefined);
        message.positionsFilter !== undefined &&
            (obj.positionsFilter = message.positionsFilter ? PositionsFilter.toJSON(message.positionsFilter) : undefined);
        message.oraclePriceFilter !== undefined && (obj.oraclePriceFilter = message.oraclePriceFilter
            ? OraclePriceFilter.toJSON(message.oraclePriceFilter)
            : undefined);
        return obj;
    },
    create(base) {
        return StreamRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseStreamRequest();
        message.bankBalancesFilter = (object.bankBalancesFilter !== undefined && object.bankBalancesFilter !== null)
            ? BankBalancesFilter.fromPartial(object.bankBalancesFilter)
            : undefined;
        message.subaccountDepositsFilter =
            (object.subaccountDepositsFilter !== undefined && object.subaccountDepositsFilter !== null)
                ? SubaccountDepositsFilter.fromPartial(object.subaccountDepositsFilter)
                : undefined;
        message.spotTradesFilter = (object.spotTradesFilter !== undefined && object.spotTradesFilter !== null)
            ? TradesFilter.fromPartial(object.spotTradesFilter)
            : undefined;
        message.derivativeTradesFilter =
            (object.derivativeTradesFilter !== undefined && object.derivativeTradesFilter !== null)
                ? TradesFilter.fromPartial(object.derivativeTradesFilter)
                : undefined;
        message.spotOrdersFilter = (object.spotOrdersFilter !== undefined && object.spotOrdersFilter !== null)
            ? OrdersFilter.fromPartial(object.spotOrdersFilter)
            : undefined;
        message.derivativeOrdersFilter =
            (object.derivativeOrdersFilter !== undefined && object.derivativeOrdersFilter !== null)
                ? OrdersFilter.fromPartial(object.derivativeOrdersFilter)
                : undefined;
        message.spotOrderbooksFilter = (object.spotOrderbooksFilter !== undefined && object.spotOrderbooksFilter !== null)
            ? OrderbookFilter.fromPartial(object.spotOrderbooksFilter)
            : undefined;
        message.derivativeOrderbooksFilter =
            (object.derivativeOrderbooksFilter !== undefined && object.derivativeOrderbooksFilter !== null)
                ? OrderbookFilter.fromPartial(object.derivativeOrderbooksFilter)
                : undefined;
        message.positionsFilter = (object.positionsFilter !== undefined && object.positionsFilter !== null)
            ? PositionsFilter.fromPartial(object.positionsFilter)
            : undefined;
        message.oraclePriceFilter = (object.oraclePriceFilter !== undefined && object.oraclePriceFilter !== null)
            ? OraclePriceFilter.fromPartial(object.oraclePriceFilter)
            : undefined;
        return message;
    },
};
function createBaseStreamResponse() {
    return {
        blockHeight: "0",
        blockTime: "0",
        bankBalances: [],
        subaccountDeposits: [],
        spotTrades: [],
        derivativeTrades: [],
        spotOrders: [],
        derivativeOrders: [],
        spotOrderbookUpdates: [],
        derivativeOrderbookUpdates: [],
        positions: [],
        oraclePrices: [],
    };
}
export const StreamResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.blockHeight !== "0") {
            writer.uint32(8).uint64(message.blockHeight);
        }
        if (message.blockTime !== "0") {
            writer.uint32(16).int64(message.blockTime);
        }
        for (const v of message.bankBalances) {
            BankBalance.encode(v, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.subaccountDeposits) {
            SubaccountDeposits.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (const v of message.spotTrades) {
            SpotTrade.encode(v, writer.uint32(42).fork()).ldelim();
        }
        for (const v of message.derivativeTrades) {
            DerivativeTrade.encode(v, writer.uint32(50).fork()).ldelim();
        }
        for (const v of message.spotOrders) {
            SpotOrderUpdate.encode(v, writer.uint32(58).fork()).ldelim();
        }
        for (const v of message.derivativeOrders) {
            DerivativeOrderUpdate.encode(v, writer.uint32(66).fork()).ldelim();
        }
        for (const v of message.spotOrderbookUpdates) {
            OrderbookUpdate.encode(v, writer.uint32(74).fork()).ldelim();
        }
        for (const v of message.derivativeOrderbookUpdates) {
            OrderbookUpdate.encode(v, writer.uint32(82).fork()).ldelim();
        }
        for (const v of message.positions) {
            Position.encode(v, writer.uint32(90).fork()).ldelim();
        }
        for (const v of message.oraclePrices) {
            OraclePrice.encode(v, writer.uint32(98).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockHeight = longToString(reader.uint64());
                    break;
                case 2:
                    message.blockTime = longToString(reader.int64());
                    break;
                case 3:
                    message.bankBalances.push(BankBalance.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.subaccountDeposits.push(SubaccountDeposits.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.spotTrades.push(SpotTrade.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.derivativeTrades.push(DerivativeTrade.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.spotOrders.push(SpotOrderUpdate.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.derivativeOrders.push(DerivativeOrderUpdate.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.spotOrderbookUpdates.push(OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.derivativeOrderbookUpdates.push(OrderbookUpdate.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.positions.push(Position.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.oraclePrices.push(OraclePrice.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            blockTime: isSet(object.blockTime) ? String(object.blockTime) : "0",
            bankBalances: Array.isArray(object?.bankBalances)
                ? object.bankBalances.map((e) => BankBalance.fromJSON(e))
                : [],
            subaccountDeposits: Array.isArray(object?.subaccountDeposits)
                ? object.subaccountDeposits.map((e) => SubaccountDeposits.fromJSON(e))
                : [],
            spotTrades: Array.isArray(object?.spotTrades) ? object.spotTrades.map((e) => SpotTrade.fromJSON(e)) : [],
            derivativeTrades: Array.isArray(object?.derivativeTrades)
                ? object.derivativeTrades.map((e) => DerivativeTrade.fromJSON(e))
                : [],
            spotOrders: Array.isArray(object?.spotOrders)
                ? object.spotOrders.map((e) => SpotOrderUpdate.fromJSON(e))
                : [],
            derivativeOrders: Array.isArray(object?.derivativeOrders)
                ? object.derivativeOrders.map((e) => DerivativeOrderUpdate.fromJSON(e))
                : [],
            spotOrderbookUpdates: Array.isArray(object?.spotOrderbookUpdates)
                ? object.spotOrderbookUpdates.map((e) => OrderbookUpdate.fromJSON(e))
                : [],
            derivativeOrderbookUpdates: Array.isArray(object?.derivativeOrderbookUpdates)
                ? object.derivativeOrderbookUpdates.map((e) => OrderbookUpdate.fromJSON(e))
                : [],
            positions: Array.isArray(object?.positions) ? object.positions.map((e) => Position.fromJSON(e)) : [],
            oraclePrices: Array.isArray(object?.oraclePrices)
                ? object.oraclePrices.map((e) => OraclePrice.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.blockTime !== undefined && (obj.blockTime = message.blockTime);
        if (message.bankBalances) {
            obj.bankBalances = message.bankBalances.map((e) => e ? BankBalance.toJSON(e) : undefined);
        }
        else {
            obj.bankBalances = [];
        }
        if (message.subaccountDeposits) {
            obj.subaccountDeposits = message.subaccountDeposits.map((e) => e ? SubaccountDeposits.toJSON(e) : undefined);
        }
        else {
            obj.subaccountDeposits = [];
        }
        if (message.spotTrades) {
            obj.spotTrades = message.spotTrades.map((e) => e ? SpotTrade.toJSON(e) : undefined);
        }
        else {
            obj.spotTrades = [];
        }
        if (message.derivativeTrades) {
            obj.derivativeTrades = message.derivativeTrades.map((e) => e ? DerivativeTrade.toJSON(e) : undefined);
        }
        else {
            obj.derivativeTrades = [];
        }
        if (message.spotOrders) {
            obj.spotOrders = message.spotOrders.map((e) => e ? SpotOrderUpdate.toJSON(e) : undefined);
        }
        else {
            obj.spotOrders = [];
        }
        if (message.derivativeOrders) {
            obj.derivativeOrders = message.derivativeOrders.map((e) => e ? DerivativeOrderUpdate.toJSON(e) : undefined);
        }
        else {
            obj.derivativeOrders = [];
        }
        if (message.spotOrderbookUpdates) {
            obj.spotOrderbookUpdates = message.spotOrderbookUpdates.map((e) => e ? OrderbookUpdate.toJSON(e) : undefined);
        }
        else {
            obj.spotOrderbookUpdates = [];
        }
        if (message.derivativeOrderbookUpdates) {
            obj.derivativeOrderbookUpdates = message.derivativeOrderbookUpdates.map((e) => e ? OrderbookUpdate.toJSON(e) : undefined);
        }
        else {
            obj.derivativeOrderbookUpdates = [];
        }
        if (message.positions) {
            obj.positions = message.positions.map((e) => e ? Position.toJSON(e) : undefined);
        }
        else {
            obj.positions = [];
        }
        if (message.oraclePrices) {
            obj.oraclePrices = message.oraclePrices.map((e) => e ? OraclePrice.toJSON(e) : undefined);
        }
        else {
            obj.oraclePrices = [];
        }
        return obj;
    },
    create(base) {
        return StreamResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseStreamResponse();
        message.blockHeight = object.blockHeight ?? "0";
        message.blockTime = object.blockTime ?? "0";
        message.bankBalances = object.bankBalances?.map((e) => BankBalance.fromPartial(e)) || [];
        message.subaccountDeposits = object.subaccountDeposits?.map((e) => SubaccountDeposits.fromPartial(e)) || [];
        message.spotTrades = object.spotTrades?.map((e) => SpotTrade.fromPartial(e)) || [];
        message.derivativeTrades = object.derivativeTrades?.map((e) => DerivativeTrade.fromPartial(e)) || [];
        message.spotOrders = object.spotOrders?.map((e) => SpotOrderUpdate.fromPartial(e)) || [];
        message.derivativeOrders = object.derivativeOrders?.map((e) => DerivativeOrderUpdate.fromPartial(e)) || [];
        message.spotOrderbookUpdates = object.spotOrderbookUpdates?.map((e) => OrderbookUpdate.fromPartial(e)) || [];
        message.derivativeOrderbookUpdates =
            object.derivativeOrderbookUpdates?.map((e) => OrderbookUpdate.fromPartial(e)) || [];
        message.positions = object.positions?.map((e) => Position.fromPartial(e)) || [];
        message.oraclePrices = object.oraclePrices?.map((e) => OraclePrice.fromPartial(e)) || [];
        return message;
    },
};
function createBaseOrderbookUpdate() {
    return { seq: "0", orderbook: undefined };
}
export const OrderbookUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.seq !== "0") {
            writer.uint32(8).uint64(message.seq);
        }
        if (message.orderbook !== undefined) {
            Orderbook.encode(message.orderbook, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOrderbookUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.seq = longToString(reader.uint64());
                    break;
                case 2:
                    message.orderbook = Orderbook.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            seq: isSet(object.seq) ? String(object.seq) : "0",
            orderbook: isSet(object.orderbook) ? Orderbook.fromJSON(object.orderbook) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.seq !== undefined && (obj.seq = message.seq);
        message.orderbook !== undefined &&
            (obj.orderbook = message.orderbook ? Orderbook.toJSON(message.orderbook) : undefined);
        return obj;
    },
    create(base) {
        return OrderbookUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOrderbookUpdate();
        message.seq = object.seq ?? "0";
        message.orderbook = (object.orderbook !== undefined && object.orderbook !== null)
            ? Orderbook.fromPartial(object.orderbook)
            : undefined;
        return message;
    },
};
function createBaseOrderbook() {
    return { marketId: "", buyLevels: [], sellLevels: [] };
}
export const Orderbook = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        for (const v of message.buyLevels) {
            Level.encode(v, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.sellLevels) {
            Level.encode(v, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOrderbook();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.buyLevels.push(Level.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.sellLevels.push(Level.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            buyLevels: Array.isArray(object?.buyLevels) ? object.buyLevels.map((e) => Level.fromJSON(e)) : [],
            sellLevels: Array.isArray(object?.sellLevels) ? object.sellLevels.map((e) => Level.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.buyLevels) {
            obj.buyLevels = message.buyLevels.map((e) => e ? Level.toJSON(e) : undefined);
        }
        else {
            obj.buyLevels = [];
        }
        if (message.sellLevels) {
            obj.sellLevels = message.sellLevels.map((e) => e ? Level.toJSON(e) : undefined);
        }
        else {
            obj.sellLevels = [];
        }
        return obj;
    },
    create(base) {
        return Orderbook.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOrderbook();
        message.marketId = object.marketId ?? "";
        message.buyLevels = object.buyLevels?.map((e) => Level.fromPartial(e)) || [];
        message.sellLevels = object.sellLevels?.map((e) => Level.fromPartial(e)) || [];
        return message;
    },
};
function createBaseBankBalance() {
    return { account: "", balances: [] };
}
export const BankBalance = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        for (const v of message.balances) {
            Coin.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBankBalance();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.balances.push(Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            balances: Array.isArray(object?.balances) ? object.balances.map((e) => Coin.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        if (message.balances) {
            obj.balances = message.balances.map((e) => e ? Coin.toJSON(e) : undefined);
        }
        else {
            obj.balances = [];
        }
        return obj;
    },
    create(base) {
        return BankBalance.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseBankBalance();
        message.account = object.account ?? "";
        message.balances = object.balances?.map((e) => Coin.fromPartial(e)) || [];
        return message;
    },
};
function createBaseSubaccountDeposits() {
    return { subaccountId: "", deposits: [] };
}
export const SubaccountDeposits = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        for (const v of message.deposits) {
            SubaccountDeposit.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubaccountDeposits();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.deposits.push(SubaccountDeposit.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            deposits: Array.isArray(object?.deposits) ? object.deposits.map((e) => SubaccountDeposit.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.deposits) {
            obj.deposits = message.deposits.map((e) => e ? SubaccountDeposit.toJSON(e) : undefined);
        }
        else {
            obj.deposits = [];
        }
        return obj;
    },
    create(base) {
        return SubaccountDeposits.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubaccountDeposits();
        message.subaccountId = object.subaccountId ?? "";
        message.deposits = object.deposits?.map((e) => SubaccountDeposit.fromPartial(e)) || [];
        return message;
    },
};
function createBaseSubaccountDeposit() {
    return { denom: "", deposit: undefined };
}
export const SubaccountDeposit = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.deposit !== undefined) {
            Deposit.encode(message.deposit, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubaccountDeposit();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.deposit = Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            deposit: isSet(object.deposit) ? Deposit.fromJSON(object.deposit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.deposit !== undefined && (obj.deposit = message.deposit ? Deposit.toJSON(message.deposit) : undefined);
        return obj;
    },
    create(base) {
        return SubaccountDeposit.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubaccountDeposit();
        message.denom = object.denom ?? "";
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? Deposit.fromPartial(object.deposit)
            : undefined;
        return message;
    },
};
function createBaseSpotOrderUpdate() {
    return { status: 0, orderHash: "", cid: "", order: undefined };
}
export const SpotOrderUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        if (message.orderHash !== "") {
            writer.uint32(18).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        if (message.order !== undefined) {
            SpotOrder.encode(message.order, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotOrderUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                case 2:
                    message.orderHash = reader.string();
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                case 4:
                    message.order = SpotOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            status: isSet(object.status) ? orderUpdateStatusFromJSON(object.status) : 0,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            order: isSet(object.order) ? SpotOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.status !== undefined && (obj.status = orderUpdateStatusToJSON(message.status));
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        message.order !== undefined && (obj.order = message.order ? SpotOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create(base) {
        return SpotOrderUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotOrderUpdate();
        message.status = object.status ?? 0;
        message.orderHash = object.orderHash ?? "";
        message.cid = object.cid ?? "";
        message.order = (object.order !== undefined && object.order !== null)
            ? SpotOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseSpotOrder() {
    return { marketId: "", order: undefined };
}
export const SpotOrder = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            SpotLimitOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotOrder();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = SpotLimitOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? SpotLimitOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? SpotLimitOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create(base) {
        return SpotOrder.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotOrder();
        message.marketId = object.marketId ?? "";
        message.order = (object.order !== undefined && object.order !== null)
            ? SpotLimitOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseDerivativeOrderUpdate() {
    return { status: 0, orderHash: "", cid: "", order: undefined };
}
export const DerivativeOrderUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        if (message.orderHash !== "") {
            writer.uint32(18).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(26).string(message.cid);
        }
        if (message.order !== undefined) {
            DerivativeOrder.encode(message.order, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeOrderUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                case 2:
                    message.orderHash = reader.string();
                    break;
                case 3:
                    message.cid = reader.string();
                    break;
                case 4:
                    message.order = DerivativeOrder.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            status: isSet(object.status) ? orderUpdateStatusFromJSON(object.status) : 0,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            order: isSet(object.order) ? DerivativeOrder.fromJSON(object.order) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.status !== undefined && (obj.status = orderUpdateStatusToJSON(message.status));
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        message.order !== undefined && (obj.order = message.order ? DerivativeOrder.toJSON(message.order) : undefined);
        return obj;
    },
    create(base) {
        return DerivativeOrderUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeOrderUpdate();
        message.status = object.status ?? 0;
        message.orderHash = object.orderHash ?? "";
        message.cid = object.cid ?? "";
        message.order = (object.order !== undefined && object.order !== null)
            ? DerivativeOrder.fromPartial(object.order)
            : undefined;
        return message;
    },
};
function createBaseDerivativeOrder() {
    return { marketId: "", order: undefined, isMarket: false };
}
export const DerivativeOrder = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.order !== undefined) {
            DerivativeLimitOrder.encode(message.order, writer.uint32(18).fork()).ldelim();
        }
        if (message.isMarket === true) {
            writer.uint32(24).bool(message.isMarket);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeOrder();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.order = DerivativeLimitOrder.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.isMarket = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            order: isSet(object.order) ? DerivativeLimitOrder.fromJSON(object.order) : undefined,
            isMarket: isSet(object.isMarket) ? Boolean(object.isMarket) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.order !== undefined && (obj.order = message.order ? DerivativeLimitOrder.toJSON(message.order) : undefined);
        message.isMarket !== undefined && (obj.isMarket = message.isMarket);
        return obj;
    },
    create(base) {
        return DerivativeOrder.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeOrder();
        message.marketId = object.marketId ?? "";
        message.order = (object.order !== undefined && object.order !== null)
            ? DerivativeLimitOrder.fromPartial(object.order)
            : undefined;
        message.isMarket = object.isMarket ?? false;
        return message;
    },
};
function createBasePosition() {
    return {
        marketId: "",
        subaccountId: "",
        isLong: false,
        quantity: "",
        entryPrice: "",
        margin: "",
        cumulativeFundingEntry: "",
    };
}
export const Position = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.isLong === true) {
            writer.uint32(24).bool(message.isLong);
        }
        if (message.quantity !== "") {
            writer.uint32(34).string(message.quantity);
        }
        if (message.entryPrice !== "") {
            writer.uint32(42).string(message.entryPrice);
        }
        if (message.margin !== "") {
            writer.uint32(50).string(message.margin);
        }
        if (message.cumulativeFundingEntry !== "") {
            writer.uint32(58).string(message.cumulativeFundingEntry);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePosition();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.isLong = reader.bool();
                    break;
                case 4:
                    message.quantity = reader.string();
                    break;
                case 5:
                    message.entryPrice = reader.string();
                    break;
                case 6:
                    message.margin = reader.string();
                    break;
                case 7:
                    message.cumulativeFundingEntry = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            isLong: isSet(object.isLong) ? Boolean(object.isLong) : false,
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            entryPrice: isSet(object.entryPrice) ? String(object.entryPrice) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            cumulativeFundingEntry: isSet(object.cumulativeFundingEntry) ? String(object.cumulativeFundingEntry) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.isLong !== undefined && (obj.isLong = message.isLong);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.entryPrice !== undefined && (obj.entryPrice = message.entryPrice);
        message.margin !== undefined && (obj.margin = message.margin);
        message.cumulativeFundingEntry !== undefined && (obj.cumulativeFundingEntry = message.cumulativeFundingEntry);
        return obj;
    },
    create(base) {
        return Position.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePosition();
        message.marketId = object.marketId ?? "";
        message.subaccountId = object.subaccountId ?? "";
        message.isLong = object.isLong ?? false;
        message.quantity = object.quantity ?? "";
        message.entryPrice = object.entryPrice ?? "";
        message.margin = object.margin ?? "";
        message.cumulativeFundingEntry = object.cumulativeFundingEntry ?? "";
        return message;
    },
};
function createBaseOraclePrice() {
    return { symbol: "", price: "", type: "" };
}
export const OraclePrice = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.type !== "") {
            writer.uint32(26).string(message.type);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOraclePrice();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.type = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            price: isSet(object.price) ? String(object.price) : "",
            type: isSet(object.type) ? String(object.type) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.price !== undefined && (obj.price = message.price);
        message.type !== undefined && (obj.type = message.type);
        return obj;
    },
    create(base) {
        return OraclePrice.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOraclePrice();
        message.symbol = object.symbol ?? "";
        message.price = object.price ?? "";
        message.type = object.type ?? "";
        return message;
    },
};
function createBaseSpotTrade() {
    return {
        marketId: "",
        isBuy: false,
        executionType: "",
        quantity: "",
        price: "",
        subaccountId: "",
        fee: "",
        orderHash: "",
        feeRecipientAddress: "",
        cid: "",
        tradeId: "",
    };
}
export const SpotTrade = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.executionType !== "") {
            writer.uint32(26).string(message.executionType);
        }
        if (message.quantity !== "") {
            writer.uint32(34).string(message.quantity);
        }
        if (message.price !== "") {
            writer.uint32(42).string(message.price);
        }
        if (message.subaccountId !== "") {
            writer.uint32(50).string(message.subaccountId);
        }
        if (message.fee !== "") {
            writer.uint32(58).string(message.fee);
        }
        if (message.orderHash !== "") {
            writer.uint32(66).string(message.orderHash);
        }
        if (message.feeRecipientAddress !== "") {
            writer.uint32(74).string(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(82).string(message.cid);
        }
        if (message.tradeId !== "") {
            writer.uint32(90).string(message.tradeId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotTrade();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.executionType = reader.string();
                    break;
                case 4:
                    message.quantity = reader.string();
                    break;
                case 5:
                    message.price = reader.string();
                    break;
                case 6:
                    message.subaccountId = reader.string();
                    break;
                case 7:
                    message.fee = reader.string();
                    break;
                case 8:
                    message.orderHash = reader.string();
                    break;
                case 9:
                    message.feeRecipientAddress = reader.string();
                    break;
                case 10:
                    message.cid = reader.string();
                    break;
                case 11:
                    message.tradeId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            executionType: isSet(object.executionType) ? String(object.executionType) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            price: isSet(object.price) ? String(object.price) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            feeRecipientAddress: isSet(object.feeRecipientAddress) ? String(object.feeRecipientAddress) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            tradeId: isSet(object.tradeId) ? String(object.tradeId) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.executionType !== undefined && (obj.executionType = message.executionType);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.price !== undefined && (obj.price = message.price);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.feeRecipientAddress !== undefined && (obj.feeRecipientAddress = message.feeRecipientAddress);
        message.cid !== undefined && (obj.cid = message.cid);
        message.tradeId !== undefined && (obj.tradeId = message.tradeId);
        return obj;
    },
    create(base) {
        return SpotTrade.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotTrade();
        message.marketId = object.marketId ?? "";
        message.isBuy = object.isBuy ?? false;
        message.executionType = object.executionType ?? "";
        message.quantity = object.quantity ?? "";
        message.price = object.price ?? "";
        message.subaccountId = object.subaccountId ?? "";
        message.fee = object.fee ?? "";
        message.orderHash = object.orderHash ?? "";
        message.feeRecipientAddress = object.feeRecipientAddress ?? "";
        message.cid = object.cid ?? "";
        message.tradeId = object.tradeId ?? "";
        return message;
    },
};
function createBaseDerivativeTrade() {
    return {
        marketId: "",
        isBuy: false,
        executionType: "",
        subaccountId: "",
        positionDelta: undefined,
        payout: "",
        fee: "",
        orderHash: "",
        feeRecipientAddress: "",
        cid: "",
        tradeId: "",
    };
}
export const DerivativeTrade = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        if (message.executionType !== "") {
            writer.uint32(26).string(message.executionType);
        }
        if (message.subaccountId !== "") {
            writer.uint32(34).string(message.subaccountId);
        }
        if (message.positionDelta !== undefined) {
            PositionDelta.encode(message.positionDelta, writer.uint32(42).fork()).ldelim();
        }
        if (message.payout !== "") {
            writer.uint32(50).string(message.payout);
        }
        if (message.fee !== "") {
            writer.uint32(58).string(message.fee);
        }
        if (message.orderHash !== "") {
            writer.uint32(66).string(message.orderHash);
        }
        if (message.feeRecipientAddress !== "") {
            writer.uint32(74).string(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(82).string(message.cid);
        }
        if (message.tradeId !== "") {
            writer.uint32(90).string(message.tradeId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeTrade();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                case 3:
                    message.executionType = reader.string();
                    break;
                case 4:
                    message.subaccountId = reader.string();
                    break;
                case 5:
                    message.positionDelta = PositionDelta.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.payout = reader.string();
                    break;
                case 7:
                    message.fee = reader.string();
                    break;
                case 8:
                    message.orderHash = reader.string();
                    break;
                case 9:
                    message.feeRecipientAddress = reader.string();
                    break;
                case 10:
                    message.cid = reader.string();
                    break;
                case 11:
                    message.tradeId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            executionType: isSet(object.executionType) ? String(object.executionType) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            positionDelta: isSet(object.positionDelta) ? PositionDelta.fromJSON(object.positionDelta) : undefined,
            payout: isSet(object.payout) ? String(object.payout) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            feeRecipientAddress: isSet(object.feeRecipientAddress) ? String(object.feeRecipientAddress) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
            tradeId: isSet(object.tradeId) ? String(object.tradeId) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.executionType !== undefined && (obj.executionType = message.executionType);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.positionDelta !== undefined &&
            (obj.positionDelta = message.positionDelta ? PositionDelta.toJSON(message.positionDelta) : undefined);
        message.payout !== undefined && (obj.payout = message.payout);
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.feeRecipientAddress !== undefined && (obj.feeRecipientAddress = message.feeRecipientAddress);
        message.cid !== undefined && (obj.cid = message.cid);
        message.tradeId !== undefined && (obj.tradeId = message.tradeId);
        return obj;
    },
    create(base) {
        return DerivativeTrade.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeTrade();
        message.marketId = object.marketId ?? "";
        message.isBuy = object.isBuy ?? false;
        message.executionType = object.executionType ?? "";
        message.subaccountId = object.subaccountId ?? "";
        message.positionDelta = (object.positionDelta !== undefined && object.positionDelta !== null)
            ? PositionDelta.fromPartial(object.positionDelta)
            : undefined;
        message.payout = object.payout ?? "";
        message.fee = object.fee ?? "";
        message.orderHash = object.orderHash ?? "";
        message.feeRecipientAddress = object.feeRecipientAddress ?? "";
        message.cid = object.cid ?? "";
        message.tradeId = object.tradeId ?? "";
        return message;
    },
};
function createBaseTradesFilter() {
    return { subaccountIds: [], marketIds: [] };
}
export const TradesFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.subaccountIds) {
            writer.uint32(10).string(v);
        }
        for (const v of message.marketIds) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTradesFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountIds: Array.isArray(object?.subaccountIds) ? object.subaccountIds.map((e) => String(e)) : [],
            marketIds: Array.isArray(object?.marketIds) ? object.marketIds.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map((e) => e);
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create(base) {
        return TradesFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTradesFilter();
        message.subaccountIds = object.subaccountIds?.map((e) => e) || [];
        message.marketIds = object.marketIds?.map((e) => e) || [];
        return message;
    },
};
function createBasePositionsFilter() {
    return { subaccountIds: [], marketIds: [] };
}
export const PositionsFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.subaccountIds) {
            writer.uint32(10).string(v);
        }
        for (const v of message.marketIds) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePositionsFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountIds: Array.isArray(object?.subaccountIds) ? object.subaccountIds.map((e) => String(e)) : [],
            marketIds: Array.isArray(object?.marketIds) ? object.marketIds.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map((e) => e);
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create(base) {
        return PositionsFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePositionsFilter();
        message.subaccountIds = object.subaccountIds?.map((e) => e) || [];
        message.marketIds = object.marketIds?.map((e) => e) || [];
        return message;
    },
};
function createBaseOrdersFilter() {
    return { subaccountIds: [], marketIds: [] };
}
export const OrdersFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.subaccountIds) {
            writer.uint32(10).string(v);
        }
        for (const v of message.marketIds) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOrdersFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountIds: Array.isArray(object?.subaccountIds) ? object.subaccountIds.map((e) => String(e)) : [],
            marketIds: Array.isArray(object?.marketIds) ? object.marketIds.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map((e) => e);
        }
        else {
            obj.subaccountIds = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create(base) {
        return OrdersFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOrdersFilter();
        message.subaccountIds = object.subaccountIds?.map((e) => e) || [];
        message.marketIds = object.marketIds?.map((e) => e) || [];
        return message;
    },
};
function createBaseOrderbookFilter() {
    return { marketIds: [] };
}
export const OrderbookFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.marketIds) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOrderbookFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { marketIds: Array.isArray(object?.marketIds) ? object.marketIds.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create(base) {
        return OrderbookFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOrderbookFilter();
        message.marketIds = object.marketIds?.map((e) => e) || [];
        return message;
    },
};
function createBaseBankBalancesFilter() {
    return { accounts: [] };
}
export const BankBalancesFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.accounts) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBankBalancesFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accounts.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { accounts: Array.isArray(object?.accounts) ? object.accounts.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.accounts) {
            obj.accounts = message.accounts.map((e) => e);
        }
        else {
            obj.accounts = [];
        }
        return obj;
    },
    create(base) {
        return BankBalancesFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseBankBalancesFilter();
        message.accounts = object.accounts?.map((e) => e) || [];
        return message;
    },
};
function createBaseSubaccountDepositsFilter() {
    return { subaccountIds: [] };
}
export const SubaccountDepositsFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.subaccountIds) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSubaccountDepositsFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            subaccountIds: Array.isArray(object?.subaccountIds) ? object.subaccountIds.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map((e) => e);
        }
        else {
            obj.subaccountIds = [];
        }
        return obj;
    },
    create(base) {
        return SubaccountDepositsFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSubaccountDepositsFilter();
        message.subaccountIds = object.subaccountIds?.map((e) => e) || [];
        return message;
    },
};
function createBaseOraclePriceFilter() {
    return { symbol: [] };
}
export const OraclePriceFilter = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.symbol) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOraclePriceFilter();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { symbol: Array.isArray(object?.symbol) ? object.symbol.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.symbol) {
            obj.symbol = message.symbol.map((e) => e);
        }
        else {
            obj.symbol = [];
        }
        return obj;
    },
    create(base) {
        return OraclePriceFilter.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOraclePriceFilter();
        message.symbol = object.symbol?.map((e) => e) || [];
        return message;
    },
};
export class StreamClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.Stream = this.Stream.bind(this);
    }
    Stream(request, metadata) {
        return this.rpc.invoke(StreamStreamDesc, StreamRequest.fromPartial(request), metadata);
    }
}
export const StreamDesc = { serviceName: "injective.stream.v1beta1.Stream" };
export const StreamStreamDesc = {
    methodName: "Stream",
    service: StreamDesc,
    requestStream: false,
    responseStream: true,
    requestType: {
        serializeBinary() {
            return StreamRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = StreamResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
    invoke(methodDesc, _request, metadata) {
        const upStreamCodes = this.options.upStreamRetryCodes || [];
        const DEFAULT_TIMEOUT_TIME = 3_000;
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Observable((observer) => {
            const upStream = (() => {
                const client = grpc.invoke(methodDesc, {
                    host: this.host,
                    request,
                    transport: this.options.streamingTransport || this.options.transport,
                    metadata: maybeCombinedMetadata,
                    debug: this.options.debug,
                    onMessage: (next) => observer.next(next),
                    onEnd: (code, message, trailers) => {
                        if (code === 0) {
                            observer.complete();
                        }
                        else if (upStreamCodes.includes(code)) {
                            setTimeout(upStream, DEFAULT_TIMEOUT_TIME);
                        }
                        else {
                            const err = new Error(message);
                            err.code = code;
                            err.metadata = trailers;
                            observer.error(err);
                        }
                    },
                });
                observer.add(() => {
                    if (!observer.closed) {
                        return client.close();
                    }
                });
            });
            upStream();
        }).pipe(share());
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
