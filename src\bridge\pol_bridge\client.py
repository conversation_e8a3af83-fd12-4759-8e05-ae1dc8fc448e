"""
此文件已弃用，应删除。

我们已经迁移到使用官方Polygon PoS桥的实现。
该实现位于bridge_tokens.py中。
请使用bridge_tokens.py作为主要的桥接脚本。
"""

# 定义一个空类，以防有代码仍在导入这个模块
class PolygonBridgeClient:
    """
    此类已弃用，请使用bridge_tokens.py中的PolygonEthereumBridge类
    """
    
    def __init__(self, *args, **kwargs):
        print("警告: PolygonBridgeClient已弃用，请使用bridge_tokens.py中的PolygonEthereumBridge类")
        raise ImportError("PolygonBridgeClient已弃用，请使用bridge_tokens.py中的PolygonEthereumBridge类") 