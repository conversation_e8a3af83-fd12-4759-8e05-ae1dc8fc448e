"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const utf8_js_1 = require("../../../../utils/utf8.js");
const MsgBase_js_1 = require("../../MsgBase.js");
const utils_1 = require("@injectivelabs/utils");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Messages
 */
class MsgStoreCode extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgStoreCode(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmwasmWasmV1Tx.MsgStoreCode.create();
        message.sender = params.sender;
        message.wasmByteCode =
            typeof params.wasmBytes === 'string'
                ? (0, utf8_js_1.fromUtf8)(params.wasmBytes)
                : params.wasmBytes;
        if (params.instantiatePermission) {
            const accessConfig = core_proto_ts_1.CosmwasmWasmV1Types.AccessConfig.create();
            accessConfig.permission = params.instantiatePermission.permission;
            accessConfig.addresses = params.instantiatePermission.addresses;
            message.instantiatePermission = accessConfig;
        }
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgStoreCode.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmwasm.wasm.v1.MsgStoreCode',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
            wasm_byte_code: Buffer.from(proto.wasmByteCode).toString('base64'),
            instantiate_permission: proto.instantiatePermission
                ? {
                    permission: (0, utils_1.toPascalCase)(core_proto_ts_1.CosmwasmWasmV1Types.accessTypeToJSON(proto.instantiatePermission.permission).replace('ACCESS_TYPE_', '')),
                    addresses: proto.instantiatePermission.addresses || [],
                }
                : undefined,
        };
        return {
            type: 'wasm/MsgStoreCode',
            value: { ...message },
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmwasm.wasm.v1.MsgStoreCode',
            ...value,
        };
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712_v1 is not supported for MsgStoreCode. Please use EIP712_v2'));
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmwasm.wasm.v1.MsgStoreCode',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmwasmWasmV1Tx.MsgStoreCode.encode(this.toProto()).finish();
    }
}
exports.default = MsgStoreCode;
