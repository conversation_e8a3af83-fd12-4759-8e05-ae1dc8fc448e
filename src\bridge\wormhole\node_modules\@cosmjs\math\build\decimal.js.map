{"version": 3, "file": "decimal.js", "sourceRoot": "", "sources": ["../src/decimal.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAuB;AAIvB,8EAA8E;AAC9E,2CAA2C;AAC3C,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAEhC;;;;GAIG;AACH,MAAa,OAAO;IACX,MAAM,CAAC,aAAa,CAAC,KAAa,EAAE,gBAAwB;QACjE,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QAEjD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,YAAY,EAAE;YAChB,oEAAoE;YACpE,MAAM,IAAI,KAAK,CAAC,iCAAiC,YAAY,CAAC,KAAM,GAAG,CAAC,EAAE,CAAC,CAAC;SAC7E;QAED,IAAI,KAAa,CAAC;QAClB,IAAI,UAAkB,CAAC;QAEvB,IAAI,KAAK,KAAK,EAAE,EAAE;YAChB,KAAK,GAAG,GAAG,CAAC;YACZ,UAAU,GAAG,EAAE,CAAC;SACjB;aAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC,+BAA+B;YAC/B,KAAK,GAAG,KAAK,CAAC;YACd,UAAU,GAAG,EAAE,CAAC;SACjB;aAAM;YACL,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/B,QAAQ,KAAK,CAAC,MAAM,EAAE;gBACpB,KAAK,CAAC,CAAC;gBACP,KAAK,CAAC;oBACJ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;gBACzF,KAAK,CAAC;oBACJ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;wBAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;oBAC1D,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACjB,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBACzC,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;aACpD;SACF;QAED,IAAI,UAAU,CAAC,MAAM,GAAG,gBAAgB,EAAE;YACxC,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,GAAG,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,EAAE,CAAC;QAEvE,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,OAAe,EAAE,gBAAwB;QACjE,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QACjD,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAI,CAAC,gBAAwB;QACzC,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QACjD,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,GAAG,CAAC,gBAAwB;QACxC,OAAO,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;QACjD,OAAO,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,CAAC;IAC3E,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,gBAAwB;QAC5D,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAChG,IAAI,gBAAgB,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QACpF,IAAI,gBAAgB,GAAG,mBAAmB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,qCAAqC,mBAAmB,EAAE,CAAC,CAAC;SAC7E;IACH,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,CAAU,EAAE,CAAU;QAC1C,IAAI,CAAC,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;YAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACjG,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,IAAW,gBAAgB;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAOD,YAAoB,OAAe,EAAE,gBAAwB;QAC3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;YAC9B,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;SACH;QAED,IAAI,CAAC,IAAI,GAAG;YACV,OAAO,EAAE,IAAI,eAAE,CAAC,OAAO,CAAC;YACxB,gBAAgB,EAAE,gBAAgB;SACnC,CAAC;IACJ,CAAC;IAED,iDAAiD;IACzC,KAAK;QACX,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC1D,CAAC;IAED,wFAAwF;IACjF,KAAK;QACV,MAAM,MAAM,GAAG,IAAI,eAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;SACrB;aAAM;YACL,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACjF;IACH,CAAC;IAED,sFAAsF;IAC/E,IAAI;QACT,MAAM,MAAM,GAAG,IAAI,eAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;SACrB;aAAM;YACL,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACzF;IACH,CAAC;IAEM,QAAQ;QACb,MAAM,MAAM,GAAG,IAAI,eAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,UAAU,CAAC,MAAM,EAAE,EAAE;YACvB,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;SACzB;aAAM;YACL,MAAM,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YAC3F,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACpE,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,qBAAqB,EAAE,CAAC;SACvD;IACH,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACzB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACpC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACtE,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,CAAU;QACpB,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;YAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpG,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACrD,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,CAAU;QACrB,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;YAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpG,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5D,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC1E,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,CAA2B;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5D,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAChE,CAAC;IAEM,MAAM,CAAC,CAAU;QACtB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAEM,UAAU,CAAC,CAAU;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAEM,iBAAiB,CAAC,CAAU;QACjC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAEM,aAAa,CAAC,CAAU;QAC7B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAEM,oBAAoB,CAAC,CAAU;QACpC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;CACF;AA3ND,0BA2NC"}