import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
/**
 * When we execute the `send` action on
 * a CW20 contract the  flow is the following:
 * Contract A -> moves tokens to Contract B -> Contract B executed a `receive` method
 * to move the tokens from its ownership to the recipient
 *
 * From the CW20 docs
 * Send{contract, amount, msg} - Moves amount tokens from the env.sender account to the recipient account.
 * contract must be an address of a contract that implements the Receiver interface.
 * The msg will be passed to the recipient contract, along with the amount.
 *
 * @experimental
 */
export declare namespace ExecArgCW20Send {
    interface Params {
        contractAddress: string;
        amount: string;
        /**
         * This object represents the underlying method
         * that we want to execute on the CW20 smart contract
         */
        msg?: object;
    }
    interface Data {
        contract: string;
        amount: string;
        msg: string;
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecArgCW20Send extends ExecArgBase<ExecArgCW20Send.Params, ExecArgCW20Send.Data> {
    static fromJSON(params: ExecArgCW20Send.Params): ExecArgCW20Send;
    toData(): ExecArgCW20Send.Data;
    toExecData(): ExecDataRepresentation<ExecArgCW20Send.Data>;
}
