import { <PERSON>to<PERSON><PERSON> } from '@injectivelabs/mito-proto-ts';
import { MitoIDO, MitoGauge, MitoVault, MitoHolders, MitoChanges, MitoMission, MitoTransfer, MitoTokenInfo, MitoPortfolio, MitoPagination, MitoIDOProgress, MitoStakingPool, MitoLeaderboard, MitoVestingConfig, MitoDenomBalance, MitoSubscription, MitoIDOSubscriber, MitoPriceSnapshot, MitoClaimReference, MitoIDOSubscription, MitoVestingConfigMap, MitoWhitelistAccount, MitoLeaderboardEpoch, MitoSubaccountBalance, MitoMissionLeaderboard, MitoStakeToSubscription, MitoIDOSubscriptionActivity, MitoMissionLeaderboardEntry, MitoIDOClaimedCoins } from '../types/mito.js';
/**
 * @category Indexer Grpc Transformer
 */
export declare class IndexerGrpcMitoTransformer {
    static grpcTokenInfoToTokenInfo(tokenInfo: MitoApi.TokenInfo): MitoTokenInfo;
    static mitoPaginationToPagination(pagination?: MitoApi.Pagination): MitoPagination | undefined;
    static mitoDenomBalanceToDenomBalance(denomBalance: MitoApi.DenomBalance): MitoDenomBalance;
    static changesResponseToChanges(changes?: MitoApi.Changes): MitoChanges | undefined;
    static mitoSubaccountInfoToSubaccountInfo(mitoSubaccountInfo?: MitoApi.SubaccountBalance): MitoSubaccountBalance | undefined;
    static mitoVaultToVault(vault: MitoApi.Vault): MitoVault;
    static mitoPriceSnapshotToPriceSnapshot(snapshot: MitoApi.PriceSnapshot): MitoPriceSnapshot;
    static portfolioResponseToPortfolio(portfolio: MitoApi.PortfolioResponse): MitoPortfolio;
    static leaderboardResponseToLeaderboard(leaderboard: MitoApi.LeaderboardResponse): MitoLeaderboard;
    static mitoTransferHistoryToTransferHistory(transfer: MitoApi.Transfer): MitoTransfer;
    static mitoLeaderboardEpochToLeaderboardEpoch(leaderboardEpoch: MitoApi.LeaderboardEpoch): MitoLeaderboardEpoch;
    static mitoStakingRewardToStakingReward(stakingReward: MitoApi.StakingReward): {
        apr: number;
        vaultName: string;
        vaultAddress: string;
        lockTimestamp: number;
        claimableRewards: import("@injectivelabs/ts-types").Coin[];
        stakedAmount: import("@injectivelabs/ts-types").Coin | undefined;
        lockedAmount: import("@injectivelabs/ts-types").Coin | undefined;
    };
    static mitoGaugeToGauge(gauge: MitoApi.Gauge): MitoGauge;
    static mitoStakingPoolToStakingPool(stakingPool: MitoApi.StakingPool): MitoStakingPool;
    static mitoStakingActivityToStakingActivity(stakingActivity: MitoApi.StakingActivity): {
        action: string;
        txHash: string;
        staker: string;
        vaultAddress: string;
        numberByAccount: number;
        timestamp: number;
        rewardedTokens: import("@injectivelabs/ts-types").Coin[];
        stakeAmount: import("@injectivelabs/ts-types").Coin | undefined;
    };
    static mitoSubscriptionToSubscription(subscription: MitoApi.Subscription): MitoSubscription;
    static mitoLpHolderToLPHolder(holder: MitoApi.Holders): MitoHolders;
    static mitoMissionToMission(mission: MitoApi.Mission): MitoMission;
    static mitoMissionLeaderboardEntryToMissionLeaderboardEntry(entry: MitoApi.MissionLeaderboardEntry): MitoMissionLeaderboardEntry;
    static mitoIDOProgressToIDOProgress(progress: MitoApi.IDOProgress): MitoIDOProgress;
    static mitoStakedToSubscriptionToStakedToSubscription(data: MitoApi.ArrayOfString): MitoStakeToSubscription;
    static mitoIDOToIDO(IDO: MitoApi.IDO): MitoIDO;
    static mitoIDOSubscriberToIDOSubscriber(IDOSubscriber: MitoApi.IDOSubscriber): MitoIDOSubscriber;
    static mitoIDOClaimedCoinsToIDOClaimedCoins(claimedCoins: MitoApi.IDOClaimedCoins): MitoIDOClaimedCoins;
    static mitoIDOSubscriptionToIDOSubscription(subscription: MitoApi.IDOSubscription): MitoIDOSubscription;
    static mitoIDOSubscriptionActivityToIDOSubscriptionActivity(IDOSubscriptionActivity: MitoApi.IDOSubscriptionActivity): MitoIDOSubscriptionActivity;
    static mitoWhitelistAccountToWhitelistAccount(account: MitoApi.WhitelistAccount): MitoWhitelistAccount;
    static mitoClaimReferenceToClaimReference(claimReference: MitoApi.ClaimReference): MitoClaimReference;
    static mitoVestingCOonfigToVestingConfig(config?: MitoApi.VestingConfig): MitoVestingConfig;
    static mitoIDOInitParamsToIDOVestingConfig(initParams?: MitoApi.InitParams): MitoVestingConfigMap | undefined;
    static vaultResponseToVault(response: MitoApi.GetVaultResponse): MitoVault;
    static vaultsResponseToVaults(response: MitoApi.GetVaultsResponse): {
        vaults: MitoVault[];
        pagination?: MitoPagination;
    };
    static lpTokenPriceChartResponseToLPTokenPriceChart(response: MitoApi.LPTokenPriceChartResponse): MitoPriceSnapshot[];
    static vaultsByHolderAddressResponseToVaultsByHolderAddress(response: MitoApi.VaultsByHolderAddressResponse): {
        subscriptions: MitoSubscription[];
        pagination: MitoPagination | undefined;
    };
    static lpHoldersResponseToLPHolders(response: MitoApi.LPHoldersResponse): {
        holders: MitoHolders[];
        pagination: MitoPagination | undefined;
    };
    static transferHistoryResponseToTransfer(response: MitoApi.TransfersHistoryResponse): {
        transfers: MitoTransfer[];
        pagination: MitoPagination | undefined;
    };
    static leaderboardEpochsResponseToLeaderboardEpochs(response: MitoApi.LeaderboardEpochsResponse): {
        epochs: MitoLeaderboardEpoch[];
        pagination: MitoPagination | undefined;
    };
    static stakingPoolsResponseToStakingPools(response: MitoApi.GetStakingPoolsResponse): {
        pools: MitoStakingPool[];
        pagination: MitoPagination | undefined;
    };
    static stakingRewardByAccountResponseToStakingRewardByAccount(response: MitoApi.StakingRewardByAccountResponse): {
        rewards: {
            apr: number;
            vaultName: string;
            vaultAddress: string;
            lockTimestamp: number;
            claimableRewards: import("@injectivelabs/ts-types").Coin[];
            stakedAmount: import("@injectivelabs/ts-types").Coin | undefined;
            lockedAmount: import("@injectivelabs/ts-types").Coin | undefined;
        }[];
        pagination: MitoPagination | undefined;
    };
    static mitoStakingHistoryResponseTpStakingHistory(response: MitoApi.StakingHistoryResponse): {
        activities: {
            action: string;
            txHash: string;
            staker: string;
            vaultAddress: string;
            numberByAccount: number;
            timestamp: number;
            rewardedTokens: import("@injectivelabs/ts-types").Coin[];
            stakeAmount: import("@injectivelabs/ts-types").Coin | undefined;
        }[];
        pagination: MitoPagination | undefined;
    };
    static mitoMissionsResponseMissions(response: MitoApi.MissionsResponse): MitoMission[];
    static mitoMissionLeaderboardResponseToMissionLeaderboard(response: MitoApi.MissionLeaderboardResponse): MitoMissionLeaderboard;
    static mitoListIDOsResponseToIDOs(response: MitoApi.ListIDOsResponse): {
        idos: MitoIDO[];
        pagination: MitoPagination | undefined;
    };
    static mitoIDOResponseToIDO(response: MitoApi.GetIDOResponse): {
        ido: MitoIDO | undefined;
    };
    static mitoIDOSubscribersResponseToIDOSubscribers(response: MitoApi.GetIDOSubscribersResponse): {
        marketId: string;
        quoteDenom: string;
        subscribers: MitoIDOSubscriber[];
        pagination: MitoPagination | undefined;
        tokenInfo: MitoTokenInfo | undefined;
    };
    static mitoIDOSubscriptionResponseToIDOSubscription(response: MitoApi.GetIDOSubscriptionResponse): {
        subscription: MitoIDOSubscription | undefined;
    };
    static mitoIDOActivitiesResponseToIDOActivities(response: MitoApi.GetIDOActivitiesResponse): {
        activities: MitoIDOSubscriptionActivity[];
        pagination: MitoPagination | undefined;
    };
    static mitoWhitelistAccountResponseToWhitelistAccount(response: MitoApi.GetWhitelistResponse): {
        idoAddress: string | undefined;
        accounts: MitoWhitelistAccount[];
        pagination: MitoPagination | undefined;
    };
    static claimReferencesResponseToClaimReferences(response: MitoApi.GetClaimReferencesResponse): {
        claimReferences: MitoClaimReference[];
        pagination?: MitoPagination;
    };
}
