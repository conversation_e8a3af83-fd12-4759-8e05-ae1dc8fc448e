```bash
#获取代币精度
python -m scripts.utils.fetch_token_decimals
#更新缺失精度信息的代币。
python -m scripts.utils.update_missing_decimals

#获取指定网络的pool地址geckoterminal
python -m scripts.utils.extract_all_pools --avax

#获取所有网络的pool地址geckoterminal
python -m scripts.utils.extract_all_pools -a

#获取所有网络的代币地址geckoterminal
python -m scripts.utils.get_pool_tokens

#获取以太坊网络Gwei
python scripts/utils/get_gas.py --method all

```