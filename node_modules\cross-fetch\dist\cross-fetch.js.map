{"version": 3, "file": "cross-fetch.js", "sources": ["../node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nvar g =\n  (typeof globalThis !== 'undefined' && globalThis) ||\n  (typeof self !== 'undefined' && self) ||\n  // eslint-disable-next-line no-undef\n  (typeof global !== 'undefined' && global) ||\n  {}\n\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob:\n    'FileReader' in g &&\n    'Blob' in g &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n      }\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body._noBody) return\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type)\n  var encoding = match ? match[1] : 'utf-8'\n  reader.readAsText(blob, encoding)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed\n    this._bodyInit = body\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n  }\n\n  this.arrayBuffer = function() {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this)\n      if (isConsumed) {\n        return isConsumed\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(\n          this._bodyArrayBuffer.buffer.slice(\n            this._bodyArrayBuffer.byteOffset,\n            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n          )\n        )\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer)\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer)\n    } else {\n      throw new Error('could not read as ArrayBuffer')\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal || (function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }());\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime())\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime()\n      }\n    }\n  }\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders\n    .split('\\r')\n    .map(function(header) {\n      return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n    })\n    .forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        try {\n          headers.append(key, value)\n        } catch (error) {\n          console.warn('Response ' + error.message)\n        }\n      }\n    })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n  }\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 200, statusText: ''})\n  response.ok = false\n  response.status = 0\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = g.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      setTimeout(function() {\n        resolve(new Response(body, options))\n      }, 0)\n    }\n\n    xhr.onerror = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request failed'))\n      }, 0)\n    }\n\n    xhr.ontimeout = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request timed out'))\n      }, 0)\n    }\n\n    xhr.onabort = function() {\n      setTimeout(function() {\n        reject(new DOMException('Aborted', 'AbortError'))\n      }, 0)\n    }\n\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url\n      } catch (e) {\n        return url\n      }\n    }\n\n    xhr.open(request.method, fixUrl(request.url), true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob'\n      } else if (\n        support.arrayBuffer\n      ) {\n        xhr.responseType = 'arraybuffer'\n      }\n    }\n\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n        names.push(normalizeName(name))\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]))\n      })\n      request.headers.forEach(function(value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value)\n        }\n      })\n    } else {\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n    }\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!g.fetch) {\n  g.fetch = fetch\n  g.Headers = Headers\n  g.Request = Request\n  g.Response = Response\n}\n"], "names": ["g", "self", "support", "isDataView", "obj", "viewClasses", "isArrayBuffer<PERSON>iew", "normalizeName", "name", "normalizeValue", "value", "iteratorFor", "items", "iterator", "Headers", "headers", "header", "oldValue", "callback", "thisArg", "consumed", "body", "fileReaderReady", "reader", "resolve", "reject", "readBlobAsArrayBuffer", "blob", "promise", "readBlobAsText", "match", "encoding", "readArrayBufferAsText", "buf", "view", "chars", "i", "bufferClone", "Body", "rejected", "isConsumed", "decode", "methods", "normalizeMethod", "method", "upcased", "Request", "input", "options", "ctrl", "reParamSearch", "reQueryString", "form", "bytes", "split", "parseHeaders", "rawHeaders", "preProcessedHeaders", "line", "parts", "key", "error", "Response", "bodyInit", "response", "redirectStatuses", "url", "status", "exports", "DOMException", "message", "fetch", "init", "request", "xhr", "abortXhr", "fixUrl", "names"], "mappings": "+BACA,IAAIA,EACD,OAAO,WAAe,KAAe,YACrC,OAAOC,EAAS,KAAeA,GAE/B,OAAO,OAAW,KAAe,QAClC,CAAE,EAEAC,EAAU,CACZ,aAAc,oBAAqBF,EACnC,SAAU,WAAYA,GAAK,aAAc,OACzC,KACE,eAAgBA,GAChB,SAAUA,GACT,UAAW,CACV,GAAI,CACF,WAAI,KACG,EACR,MAAW,CACV,MAAO,EACR,CACP,EAAQ,EACN,SAAU,aAAcA,EACxB,YAAa,gBAAiBA,CAChC,EAEA,SAASG,EAAWC,EAAK,CACvB,OAAOA,GAAO,SAAS,UAAU,cAAcA,CAAG,CACpD,CAEA,GAAIF,EAAQ,YACV,IAAIG,EAAc,CAChB,qBACA,sBACA,6BACA,sBACA,uBACA,sBACA,uBACA,wBACA,uBACD,EAEGC,EACF,YAAY,QACZ,SAASF,EAAK,CACZ,OAAOA,GAAOC,EAAY,QAAQ,OAAO,UAAU,SAAS,KAAKD,CAAG,CAAC,EAAI,EAC1E,EAGL,SAASG,EAAcC,EAAM,CAI3B,GAHI,OAAOA,GAAS,WAClBA,EAAO,OAAOA,CAAI,GAEhB,6BAA6B,KAAKA,CAAI,GAAKA,IAAS,GACtD,MAAM,IAAI,UAAU,4CAA8CA,EAAO,GAAG,EAE9E,OAAOA,EAAK,YAAa,CAC3B,CAEA,SAASC,EAAeC,EAAO,CAC7B,OAAI,OAAOA,GAAU,WACnBA,EAAQ,OAAOA,CAAK,GAEfA,CACT,CAGA,SAASC,EAAYC,EAAO,CAC1B,IAAIC,EAAW,CACb,KAAM,UAAW,CACf,IAAIH,EAAQE,EAAM,MAAO,EACzB,MAAO,CAAC,KAAMF,IAAU,OAAW,MAAOA,CAAK,CAChD,CACF,EAED,OAAIR,EAAQ,WACVW,EAAS,OAAO,QAAQ,EAAI,UAAW,CACrC,OAAOA,CACR,GAGIA,CACT,CAEO,SAASC,EAAQC,EAAS,CAC/B,KAAK,IAAM,CAAE,EAETA,aAAmBD,EACrBC,EAAQ,QAAQ,SAASL,EAAOF,EAAM,CACpC,KAAK,OAAOA,EAAME,CAAK,CACxB,EAAE,IAAI,EACE,MAAM,QAAQK,CAAO,EAC9BA,EAAQ,QAAQ,SAASC,EAAQ,CAC/B,GAAIA,EAAO,QAAU,EACnB,MAAM,IAAI,UAAU,sEAAwEA,EAAO,MAAM,EAE3G,KAAK,OAAOA,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,CACjC,EAAE,IAAI,EACED,GACT,OAAO,oBAAoBA,CAAO,EAAE,QAAQ,SAASP,EAAM,CACzD,KAAK,OAAOA,EAAMO,EAAQP,CAAI,CAAC,CAChC,EAAE,IAAI,CAEX,CAEAM,EAAQ,UAAU,OAAS,SAASN,EAAME,EAAO,CAC/CF,EAAOD,EAAcC,CAAI,EACzBE,EAAQD,EAAeC,CAAK,EAC5B,IAAIO,EAAW,KAAK,IAAIT,CAAI,EAC5B,KAAK,IAAIA,CAAI,EAAIS,EAAWA,EAAW,KAAOP,EAAQA,CACxD,EAEAI,EAAQ,UAAU,OAAY,SAASN,EAAM,CAC3C,OAAO,KAAK,IAAID,EAAcC,CAAI,CAAC,CACrC,EAEAM,EAAQ,UAAU,IAAM,SAASN,EAAM,CACrC,OAAAA,EAAOD,EAAcC,CAAI,EAClB,KAAK,IAAIA,CAAI,EAAI,KAAK,IAAIA,CAAI,EAAI,IAC3C,EAEAM,EAAQ,UAAU,IAAM,SAASN,EAAM,CACrC,OAAO,KAAK,IAAI,eAAeD,EAAcC,CAAI,CAAC,CACpD,EAEAM,EAAQ,UAAU,IAAM,SAASN,EAAME,EAAO,CAC5C,KAAK,IAAIH,EAAcC,CAAI,CAAC,EAAIC,EAAeC,CAAK,CACtD,EAEAI,EAAQ,UAAU,QAAU,SAASI,EAAUC,EAAS,CACtD,QAASX,KAAQ,KAAK,IAChB,KAAK,IAAI,eAAeA,CAAI,GAC9BU,EAAS,KAAKC,EAAS,KAAK,IAAIX,CAAI,EAAGA,EAAM,IAAI,CAGvD,EAEAM,EAAQ,UAAU,KAAO,UAAW,CAClC,IAAIF,EAAQ,CAAE,EACd,YAAK,QAAQ,SAASF,EAAOF,EAAM,CACjCI,EAAM,KAAKJ,CAAI,CACnB,CAAG,EACMG,EAAYC,CAAK,CAC1B,EAEAE,EAAQ,UAAU,OAAS,UAAW,CACpC,IAAIF,EAAQ,CAAE,EACd,YAAK,QAAQ,SAASF,EAAO,CAC3BE,EAAM,KAAKF,CAAK,CACpB,CAAG,EACMC,EAAYC,CAAK,CAC1B,EAEAE,EAAQ,UAAU,QAAU,UAAW,CACrC,IAAIF,EAAQ,CAAE,EACd,YAAK,QAAQ,SAASF,EAAOF,EAAM,CACjCI,EAAM,KAAK,CAACJ,EAAME,CAAK,CAAC,CAC5B,CAAG,EACMC,EAAYC,CAAK,CAC1B,EAEIV,EAAQ,WACVY,EAAQ,UAAU,OAAO,QAAQ,EAAIA,EAAQ,UAAU,SAGzD,SAASM,EAASC,EAAM,CACtB,GAAI,CAAAA,EAAK,QACT,IAAIA,EAAK,SACP,OAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC,EAErDA,EAAK,SAAW,GAClB,CAEA,SAASC,EAAgBC,EAAQ,CAC/B,OAAO,IAAI,QAAQ,SAASC,EAASC,EAAQ,CAC3CF,EAAO,OAAS,UAAW,CACzBC,EAAQD,EAAO,MAAM,CACtB,EACDA,EAAO,QAAU,UAAW,CAC1BE,EAAOF,EAAO,KAAK,CACpB,CACL,CAAG,CACH,CAEA,SAASG,EAAsBC,EAAM,CACnC,IAAIJ,EAAS,IAAI,WACbK,EAAUN,EAAgBC,CAAM,EACpC,OAAAA,EAAO,kBAAkBI,CAAI,EACtBC,CACT,CAEA,SAASC,EAAeF,EAAM,CAC5B,IAAIJ,EAAS,IAAI,WACbK,EAAUN,EAAgBC,CAAM,EAChCO,EAAQ,2BAA2B,KAAKH,EAAK,IAAI,EACjDI,EAAWD,EAAQA,EAAM,CAAC,EAAI,QAClC,OAAAP,EAAO,WAAWI,EAAMI,CAAQ,EACzBH,CACT,CAEA,SAASI,EAAsBC,EAAK,CAIlC,QAHIC,EAAO,IAAI,WAAWD,CAAG,EACzBE,EAAQ,IAAI,MAAMD,EAAK,MAAM,EAExBE,EAAI,EAAGA,EAAIF,EAAK,OAAQE,IAC/BD,EAAMC,CAAC,EAAI,OAAO,aAAaF,EAAKE,CAAC,CAAC,EAExC,OAAOD,EAAM,KAAK,EAAE,CACtB,CAEA,SAASE,EAAYJ,EAAK,CACxB,GAAIA,EAAI,MACN,OAAOA,EAAI,MAAM,CAAC,EAElB,IAAIC,EAAO,IAAI,WAAWD,EAAI,UAAU,EACxC,OAAAC,EAAK,IAAI,IAAI,WAAWD,CAAG,CAAC,EACrBC,EAAK,MAEhB,CAEA,SAASI,GAAO,CACd,YAAK,SAAW,GAEhB,KAAK,UAAY,SAASjB,EAAM,CAY9B,KAAK,SAAW,KAAK,SACrB,KAAK,UAAYA,EACZA,EAGM,OAAOA,GAAS,SACzB,KAAK,UAAYA,EACRnB,EAAQ,MAAQ,KAAK,UAAU,cAAcmB,CAAI,EAC1D,KAAK,UAAYA,EACRnB,EAAQ,UAAY,SAAS,UAAU,cAAcmB,CAAI,EAClE,KAAK,cAAgBA,EACZnB,EAAQ,cAAgB,gBAAgB,UAAU,cAAcmB,CAAI,EAC7E,KAAK,UAAYA,EAAK,SAAU,EACvBnB,EAAQ,aAAeA,EAAQ,MAAQC,EAAWkB,CAAI,GAC/D,KAAK,iBAAmBgB,EAAYhB,EAAK,MAAM,EAE/C,KAAK,UAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,GACxCnB,EAAQ,cAAgB,YAAY,UAAU,cAAcmB,CAAI,GAAKf,EAAkBe,CAAI,GACpG,KAAK,iBAAmBgB,EAAYhB,CAAI,EAExC,KAAK,UAAYA,EAAO,OAAO,UAAU,SAAS,KAAKA,CAAI,GAjB3D,KAAK,QAAU,GACf,KAAK,UAAY,IAmBd,KAAK,QAAQ,IAAI,cAAc,IAC9B,OAAOA,GAAS,SAClB,KAAK,QAAQ,IAAI,eAAgB,0BAA0B,EAClD,KAAK,WAAa,KAAK,UAAU,KAC1C,KAAK,QAAQ,IAAI,eAAgB,KAAK,UAAU,IAAI,EAC3CnB,EAAQ,cAAgB,gBAAgB,UAAU,cAAcmB,CAAI,GAC7E,KAAK,QAAQ,IAAI,eAAgB,iDAAiD,EAGvF,EAEGnB,EAAQ,OACV,KAAK,KAAO,UAAW,CACrB,IAAIqC,EAAWnB,EAAS,IAAI,EAC5B,GAAImB,EACF,OAAOA,EAGT,GAAI,KAAK,UACP,OAAO,QAAQ,QAAQ,KAAK,SAAS,EAChC,GAAI,KAAK,iBACd,OAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC,EACnD,GAAI,KAAK,cACd,MAAM,IAAI,MAAM,sCAAsC,EAEtD,OAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC,CAEpD,GAGH,KAAK,YAAc,UAAW,CAC5B,GAAI,KAAK,iBAAkB,CACzB,IAAIC,EAAapB,EAAS,IAAI,EAC9B,OAAIoB,IAEO,YAAY,OAAO,KAAK,gBAAgB,EAC1C,QAAQ,QACb,KAAK,iBAAiB,OAAO,MAC3B,KAAK,iBAAiB,WACtB,KAAK,iBAAiB,WAAa,KAAK,iBAAiB,UAC1D,CACF,EAEM,QAAQ,QAAQ,KAAK,gBAAgB,EAEpD,KAAW,IAAItC,EAAQ,KACjB,OAAO,KAAK,OAAO,KAAKwB,CAAqB,EAE7C,MAAM,IAAI,MAAM,+BAA+B,EAElD,EAED,KAAK,KAAO,UAAW,CACrB,IAAIa,EAAWnB,EAAS,IAAI,EAC5B,GAAImB,EACF,OAAOA,EAGT,GAAI,KAAK,UACP,OAAOV,EAAe,KAAK,SAAS,EAC/B,GAAI,KAAK,iBACd,OAAO,QAAQ,QAAQG,EAAsB,KAAK,gBAAgB,CAAC,EAC9D,GAAI,KAAK,cACd,MAAM,IAAI,MAAM,sCAAsC,EAEtD,OAAO,QAAQ,QAAQ,KAAK,SAAS,CAExC,EAEG9B,EAAQ,WACV,KAAK,SAAW,UAAW,CACzB,OAAO,KAAK,OAAO,KAAKuC,CAAM,CAC/B,GAGH,KAAK,KAAO,UAAW,CACrB,OAAO,KAAK,KAAI,EAAG,KAAK,KAAK,KAAK,CACnC,EAEM,IACT,CAGA,IAAIC,EAAU,CAAC,UAAW,SAAU,MAAO,OAAQ,UAAW,QAAS,OAAQ,MAAO,OAAO,EAE7F,SAASC,EAAgBC,EAAQ,CAC/B,IAAIC,EAAUD,EAAO,YAAa,EAClC,OAAOF,EAAQ,QAAQG,CAAO,EAAI,GAAKA,EAAUD,CACnD,CAEO,SAASE,EAAQC,EAAOC,EAAS,CACtC,GAAI,EAAE,gBAAgBF,GACpB,MAAM,IAAI,UAAU,4FAA4F,EAGlHE,EAAUA,GAAW,CAAE,EACvB,IAAI3B,EAAO2B,EAAQ,KAEnB,GAAID,aAAiBD,EAAS,CAC5B,GAAIC,EAAM,SACR,MAAM,IAAI,UAAU,cAAc,EAEpC,KAAK,IAAMA,EAAM,IACjB,KAAK,YAAcA,EAAM,YACpBC,EAAQ,UACX,KAAK,QAAU,IAAIlC,EAAQiC,EAAM,OAAO,GAE1C,KAAK,OAASA,EAAM,OACpB,KAAK,KAAOA,EAAM,KAClB,KAAK,OAASA,EAAM,OAChB,CAAC1B,GAAQ0B,EAAM,WAAa,OAC9B1B,EAAO0B,EAAM,UACbA,EAAM,SAAW,GAEvB,MACI,KAAK,IAAM,OAAOA,CAAK,EAiBzB,GAdA,KAAK,YAAcC,EAAQ,aAAe,KAAK,aAAe,eAC1DA,EAAQ,SAAW,CAAC,KAAK,WAC3B,KAAK,QAAU,IAAIlC,EAAQkC,EAAQ,OAAO,GAE5C,KAAK,OAASL,EAAgBK,EAAQ,QAAU,KAAK,QAAU,KAAK,EACpE,KAAK,KAAOA,EAAQ,MAAQ,KAAK,MAAQ,KACzC,KAAK,OAASA,EAAQ,QAAU,KAAK,QAAW,UAAY,CAC1D,GAAI,oBAAqBhD,EAAG,CAC1B,IAAIiD,EAAO,IAAI,gBACf,OAAOA,EAAK,MACb,CACF,EAAA,EACD,KAAK,SAAW,MAEX,KAAK,SAAW,OAAS,KAAK,SAAW,SAAW5B,EACvD,MAAM,IAAI,UAAU,2CAA2C,EAIjE,GAFA,KAAK,UAAUA,CAAI,GAEf,KAAK,SAAW,OAAS,KAAK,SAAW,UACvC2B,EAAQ,QAAU,YAAcA,EAAQ,QAAU,YAAY,CAEhE,IAAIE,EAAgB,gBACpB,GAAIA,EAAc,KAAK,KAAK,GAAG,EAE7B,KAAK,IAAM,KAAK,IAAI,QAAQA,EAAe,OAAS,IAAI,OAAO,SAAS,MACnE,CAEL,IAAIC,EAAgB,KACpB,KAAK,MAAQA,EAAc,KAAK,KAAK,GAAG,EAAI,IAAM,KAAO,KAAO,IAAI,KAAI,EAAG,QAAS,CACrF,CACF,CAEL,CAEAL,EAAQ,UAAU,MAAQ,UAAW,CACnC,OAAO,IAAIA,EAAQ,KAAM,CAAC,KAAM,KAAK,SAAS,CAAC,CACjD,EAEA,SAASL,EAAOpB,EAAM,CACpB,IAAI+B,EAAO,IAAI,SACf,OAAA/B,EACG,KAAM,EACN,MAAM,GAAG,EACT,QAAQ,SAASgC,EAAO,CACvB,GAAIA,EAAO,CACT,IAAIC,EAAQD,EAAM,MAAM,GAAG,EACvB7C,EAAO8C,EAAM,MAAO,EAAC,QAAQ,MAAO,GAAG,EACvC5C,EAAQ4C,EAAM,KAAK,GAAG,EAAE,QAAQ,MAAO,GAAG,EAC9CF,EAAK,OAAO,mBAAmB5C,CAAI,EAAG,mBAAmBE,CAAK,CAAC,CAChE,CACP,CAAK,EACI0C,CACT,CAEA,SAASG,EAAaC,EAAY,CAChC,IAAIzC,EAAU,IAAID,EAGd2C,EAAsBD,EAAW,QAAQ,eAAgB,GAAG,EAIhE,OAAAC,EACG,MAAM,IAAI,EACV,IAAI,SAASzC,EAAQ,CACpB,OAAOA,EAAO,QAAQ;AAAA,CAAI,IAAM,EAAIA,EAAO,OAAO,EAAGA,EAAO,MAAM,EAAIA,CAC5E,CAAK,EACA,QAAQ,SAAS0C,EAAM,CACtB,IAAIC,EAAQD,EAAK,MAAM,GAAG,EACtBE,EAAMD,EAAM,MAAK,EAAG,KAAM,EAC9B,GAAIC,EAAK,CACP,IAAIlD,EAAQiD,EAAM,KAAK,GAAG,EAAE,KAAM,EAClC,GAAI,CACF5C,EAAQ,OAAO6C,EAAKlD,CAAK,CAC1B,OAAQmD,EAAO,CACd,QAAQ,KAAK,YAAcA,EAAM,OAAO,CACzC,CACF,CACP,CAAK,EACI9C,CACT,CAEAuB,EAAK,KAAKQ,EAAQ,SAAS,EAEpB,SAASgB,EAASC,EAAUf,EAAS,CAC1C,GAAI,EAAE,gBAAgBc,GACpB,MAAM,IAAI,UAAU,4FAA4F,EAQlH,GANKd,IACHA,EAAU,CAAE,GAGd,KAAK,KAAO,UACZ,KAAK,OAASA,EAAQ,SAAW,OAAY,IAAMA,EAAQ,OACvD,KAAK,OAAS,KAAO,KAAK,OAAS,IACrC,MAAM,IAAI,WAAW,0FAA0F,EAEjH,KAAK,GAAK,KAAK,QAAU,KAAO,KAAK,OAAS,IAC9C,KAAK,WAAaA,EAAQ,aAAe,OAAY,GAAK,GAAKA,EAAQ,WACvE,KAAK,QAAU,IAAIlC,EAAQkC,EAAQ,OAAO,EAC1C,KAAK,IAAMA,EAAQ,KAAO,GAC1B,KAAK,UAAUe,CAAQ,CACzB,CAEAzB,EAAK,KAAKwB,EAAS,SAAS,EAE5BA,EAAS,UAAU,MAAQ,UAAW,CACpC,OAAO,IAAIA,EAAS,KAAK,UAAW,CAClC,OAAQ,KAAK,OACb,WAAY,KAAK,WACjB,QAAS,IAAIhD,EAAQ,KAAK,OAAO,EACjC,IAAK,KAAK,GACd,CAAG,CACH,EAEAgD,EAAS,MAAQ,UAAW,CAC1B,IAAIE,EAAW,IAAIF,EAAS,KAAM,CAAC,OAAQ,IAAK,WAAY,EAAE,CAAC,EAC/D,OAAAE,EAAS,GAAK,GACdA,EAAS,OAAS,EAClBA,EAAS,KAAO,QACTA,CACT,EAEA,IAAIC,EAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EAE/CH,EAAS,SAAW,SAASI,EAAKC,EAAQ,CACxC,GAAIF,EAAiB,QAAQE,CAAM,IAAM,GACvC,MAAM,IAAI,WAAW,qBAAqB,EAG5C,OAAO,IAAIL,EAAS,KAAM,CAAC,OAAQK,EAAQ,QAAS,CAAC,SAAUD,CAAG,CAAC,CAAC,CACtE,EAEuBE,EAAA,aAAGpE,EAAE,aAC5B,GAAI,CACF,IAAIqE,cACN,MAAc,CACZA,eAAe,SAASC,EAAS9D,EAAM,CACrC,KAAK,QAAU8D,EACf,KAAK,KAAO9D,EACZ,IAAIqD,EAAQ,MAAMS,CAAO,EACzB,KAAK,MAAQT,EAAM,KACpB,EACDQ,EAAAA,aAAa,UAAY,OAAO,OAAO,MAAM,SAAS,EACtDA,eAAa,UAAU,YAAcA,EAAY,YACnD,CAEO,SAASE,EAAMxB,EAAOyB,EAAM,CACjC,OAAO,IAAI,QAAQ,SAAShD,EAASC,EAAQ,CAC3C,IAAIgD,EAAU,IAAI3B,EAAQC,EAAOyB,CAAI,EAErC,GAAIC,EAAQ,QAAUA,EAAQ,OAAO,QACnC,OAAOhD,EAAO,IAAI4C,EAAAA,aAAa,UAAW,YAAY,CAAC,EAGzD,IAAIK,EAAM,IAAI,eAEd,SAASC,GAAW,CAClBD,EAAI,MAAO,CACZ,CAEDA,EAAI,OAAS,UAAW,CACtB,IAAI1B,EAAU,CACZ,WAAY0B,EAAI,WAChB,QAASnB,EAAamB,EAAI,sBAAqB,GAAM,EAAE,CACxD,EAGGD,EAAQ,IAAI,QAAQ,SAAS,IAAM,IAAMC,EAAI,OAAS,KAAOA,EAAI,OAAS,KAC5E1B,EAAQ,OAAS,IAEjBA,EAAQ,OAAS0B,EAAI,OAEvB1B,EAAQ,IAAM,gBAAiB0B,EAAMA,EAAI,YAAc1B,EAAQ,QAAQ,IAAI,eAAe,EAC1F,IAAI3B,EAAO,aAAcqD,EAAMA,EAAI,SAAWA,EAAI,aAClD,WAAW,UAAW,CACpBlD,EAAQ,IAAIsC,EAASzC,EAAM2B,CAAO,CAAC,CACpC,EAAE,CAAC,CACL,EAED0B,EAAI,QAAU,UAAW,CACvB,WAAW,UAAW,CACpBjD,EAAO,IAAI,UAAU,wBAAwB,CAAC,CAC/C,EAAE,CAAC,CACL,EAEDiD,EAAI,UAAY,UAAW,CACzB,WAAW,UAAW,CACpBjD,EAAO,IAAI,UAAU,2BAA2B,CAAC,CAClD,EAAE,CAAC,CACL,EAEDiD,EAAI,QAAU,UAAW,CACvB,WAAW,UAAW,CACpBjD,EAAO,IAAI4C,EAAAA,aAAa,UAAW,YAAY,CAAC,CACjD,EAAE,CAAC,CACL,EAED,SAASO,EAAOV,EAAK,CACnB,GAAI,CACF,OAAOA,IAAQ,IAAMlE,EAAE,SAAS,KAAOA,EAAE,SAAS,KAAOkE,CAC1D,MAAW,CACV,OAAOA,CACR,CACF,CAoBD,GAlBAQ,EAAI,KAAKD,EAAQ,OAAQG,EAAOH,EAAQ,GAAG,EAAG,EAAI,EAE9CA,EAAQ,cAAgB,UAC1BC,EAAI,gBAAkB,GACbD,EAAQ,cAAgB,SACjCC,EAAI,gBAAkB,IAGpB,iBAAkBA,IAChBxE,EAAQ,KACVwE,EAAI,aAAe,OAEnBxE,EAAQ,cAERwE,EAAI,aAAe,gBAInBF,GAAQ,OAAOA,EAAK,SAAY,UAAY,EAAEA,EAAK,mBAAmB1D,GAAYd,EAAE,SAAWwE,EAAK,mBAAmBxE,EAAE,SAAW,CACtI,IAAI6E,EAAQ,CAAA,EACZ,OAAO,oBAAoBL,EAAK,OAAO,EAAE,QAAQ,SAAShE,EAAM,CAC9DqE,EAAM,KAAKtE,EAAcC,CAAI,CAAC,EAC9BkE,EAAI,iBAAiBlE,EAAMC,EAAe+D,EAAK,QAAQhE,CAAI,CAAC,CAAC,CACrE,CAAO,EACDiE,EAAQ,QAAQ,QAAQ,SAAS/D,EAAOF,EAAM,CACxCqE,EAAM,QAAQrE,CAAI,IAAM,IAC1BkE,EAAI,iBAAiBlE,EAAME,CAAK,CAE1C,CAAO,CACP,MACM+D,EAAQ,QAAQ,QAAQ,SAAS/D,EAAOF,EAAM,CAC5CkE,EAAI,iBAAiBlE,EAAME,CAAK,CACxC,CAAO,EAGC+D,EAAQ,SACVA,EAAQ,OAAO,iBAAiB,QAASE,CAAQ,EAEjDD,EAAI,mBAAqB,UAAW,CAE9BA,EAAI,aAAe,GACrBD,EAAQ,OAAO,oBAAoB,QAASE,CAAQ,CAEvD,GAGHD,EAAI,KAAK,OAAOD,EAAQ,UAAc,IAAc,KAAOA,EAAQ,SAAS,CAChF,CAAG,CACH,CAEA,OAAAF,EAAM,SAAW,GAEZvE,EAAE,QACLA,EAAE,MAAQuE,EACVvE,EAAE,QAAUc,EACZd,EAAE,QAAU8C,EACZ9C,EAAE,SAAW8D", "x_google_ignoreList": [0]}