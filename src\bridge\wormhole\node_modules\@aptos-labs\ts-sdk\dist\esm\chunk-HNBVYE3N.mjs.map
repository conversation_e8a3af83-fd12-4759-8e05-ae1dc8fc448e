{"version": 3, "sources": ["../../src/core/account/utils/address.ts"], "sourcesContent": ["import { sha3_256 } from \"@noble/hashes/sha3\";\nimport { AccountAddress } from \"../../accountAddress\";\nimport { DeriveScheme } from \"../../../types\";\n\n/**\n * Creates an object address from creator address and seed\n *\n * @param creatorAddress The object creator account address\n * @param seed The seed in either Uint8Array | string type\n *\n * @returns The object account address\n * @group Implementation\n * @category Account (On-Chain Model)\n */\nexport const createObjectAddress = (creatorAddress: AccountAddress, seed: Uint8Array | string): AccountAddress => {\n  const creatorBytes = creatorAddress.bcsToBytes();\n\n  const seedBytes = typeof seed === \"string\" ? Buffer.from(seed, \"utf8\") : seed;\n\n  const bytes = new Uint8Array([...creatorBytes, ...seedBytes, DeriveScheme.DeriveObjectAddressFromSeed]);\n\n  return new AccountAddress(sha3_256(bytes));\n};\n\n/**\n * Creates a resource address from creator address and seed\n *\n * @param creatorAddress The creator account address\n * @param seed The seed in either Uint8Array | string type\n *\n * @returns The resource account address\n * @group Implementation\n * @category Account (On-Chain Model)\n */\nexport const createResourceAddress = (creatorAddress: AccountAddress, seed: Uint8Array | string): AccountAddress => {\n  const creatorBytes = creatorAddress.bcsToBytes();\n\n  const seedBytes = typeof seed === \"string\" ? Buffer.from(seed, \"utf8\") : seed;\n\n  const bytes = new Uint8Array([...creatorBytes, ...seedBytes, DeriveScheme.DeriveResourceAccountAddress]);\n\n  return new AccountAddress(sha3_256(bytes));\n};\n\n/**\n * Creates a token object address from creator address, collection name and token name\n *\n * @param creatorAddress The token creator account address\n * @param collectionName The collection name\n * @param tokenName The token name\n *\n * @returns The token account address\n * @group Implementation\n * @category Account (On-Chain Model)\n */\nexport const createTokenAddress = (\n  creatorAddress: AccountAddress,\n  collectionName: string,\n  tokenName: string,\n): AccountAddress => {\n  const seed = `${collectionName}::${tokenName}`;\n  return createObjectAddress(creatorAddress, seed);\n};\n"], "mappings": "yCAAA,OAAS,YAAAA,MAAgB,qBAclB,IAAMC,EAAsB,CAACC,EAAgCC,IAA8C,CAChH,IAAMC,EAAeF,EAAe,WAAW,EAEzCG,EAAY,OAAOF,GAAS,SAAW,OAAO,KAAKA,EAAM,MAAM,EAAIA,EAEnEG,EAAQ,IAAI,WAAW,CAAC,GAAGF,EAAc,GAAGC,KAAmD,CAAC,EAEtG,OAAO,IAAIE,EAAeC,EAASF,CAAK,CAAC,CAC3C,EAYaG,EAAwB,CAACP,EAAgCC,IAA8C,CAClH,IAAMC,EAAeF,EAAe,WAAW,EAEzCG,EAAY,OAAOF,GAAS,SAAW,OAAO,KAAKA,EAAM,MAAM,EAAIA,EAEnEG,EAAQ,IAAI,WAAW,CAAC,GAAGF,EAAc,GAAGC,KAAoD,CAAC,EAEvG,OAAO,IAAIE,EAAeC,EAASF,CAAK,CAAC,CAC3C,EAaaI,EAAqB,CAChCR,EACAS,EACAC,IACmB,CACnB,IAAMT,EAAO,GAAGQ,CAAc,KAAKC,CAAS,GAC5C,OAAOX,EAAoBC,EAAgBC,CAAI,CACjD", "names": ["sha3_256", "createObjectAddress", "<PERSON><PERSON><PERSON><PERSON>", "seed", "creatorBytes", "seedBytes", "bytes", "Account<PERSON><PERSON><PERSON>", "sha3_256", "createResourceAddress", "createTokenAddress", "collectionName", "tokenName"]}