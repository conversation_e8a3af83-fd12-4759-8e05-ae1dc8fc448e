import _m0 from "protobufjs/minimal.js";
import { FundingMode } from "./proposal";
export declare const protobufPackage = "injective.wasmx.v1";
export interface EventContractExecution {
    contractAddress: string;
    response: Uint8Array;
    otherError: string;
    executionError: string;
}
export interface EventContractRegistered {
    contractAddress: string;
    gasPrice: string;
    shouldPinContract: boolean;
    isMigrationAllowed: boolean;
    codeId: string;
    adminAddress: string;
    granterAddress: string;
    fundingMode: FundingMode;
}
export interface EventContractDeregistered {
    contractAddress: string;
}
export declare const EventContractExecution: {
    encode(message: EventContractExecution, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventContractExecution;
    fromJSON(object: any): EventContractExecution;
    toJSON(message: EventContractExecution): unknown;
    create(base?: DeepPartial<EventContractExecution>): EventContractExecution;
    fromPartial(object: DeepPartial<EventContractExecution>): EventContractExecution;
};
export declare const EventContractRegistered: {
    encode(message: EventContractRegistered, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventContractRegistered;
    fromJSON(object: any): EventContractRegistered;
    toJSON(message: EventContractRegistered): unknown;
    create(base?: DeepPartial<EventContractRegistered>): EventContractRegistered;
    fromPartial(object: DeepPartial<EventContractRegistered>): EventContractRegistered;
};
export declare const EventContractDeregistered: {
    encode(message: EventContractDeregistered, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventContractDeregistered;
    fromJSON(object: any): EventContractDeregistered;
    toJSON(message: EventContractDeregistered): unknown;
    create(base?: DeepPartial<EventContractDeregistered>): EventContractDeregistered;
    fromPartial(object: DeepPartial<EventContractDeregistered>): EventContractDeregistered;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
