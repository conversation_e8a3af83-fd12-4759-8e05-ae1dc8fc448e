#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import logging
import argparse
import requests
import traceback
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import yaml
from decimal import Decimal
from web3 import Web3

# 设置日志
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# API端点
API_ENDPOINTS = {
    "polygonscan": "https://api.polygonscan.com/api",
    "etherscan": "https://api.etherscan.io/api"
}

# 链ID与名称映射
CHAIN_NAMES = {
    1: "ethereum",
    137: "polygon"
}

CHAIN_IDS = {
    "ethereum": 1,
    "polygon": 137
}

def load_config():
    """
    从config.yaml加载配置
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config', 'config.yaml')
        
        if not os.path.exists(config_path):
            logger.warning(f"找不到配置文件: {config_path}")
            return {}
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return {}

def get_token_info(token_address: str, chain: str, api_key: str = None) -> Dict[str, Any]:
    """
    获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        api_key: API密钥(可选)
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 首先尝试从本地配置文件中获取代币信息
    token_info = get_token_from_local_config(token_address, chain)
    if token_info:
        logger.info(f"从本地配置找到{chain}代币信息: {token_info['symbol']}")
        return token_info
    
    # 对于以太坊，使用tokeninfo接口
    if chain == "ethereum":
        api_endpoint = API_ENDPOINTS.get("etherscan")
        if not api_endpoint:
            logger.error(f"不支持的链: {chain}")
            return {
                "name": "Unknown Token",
                "symbol": "UNKNOWN",
                "decimals": 18
            }
        
        # 构建API请求
        params = {
            "module": "token",
            "action": "tokeninfo",
            "contractaddress": token_address,
        }
        
        if api_key:
            params["apikey"] = api_key
        
        try:
            logger.info(f"请求以太坊代币信息: {token_address}")
            response = requests.get(api_endpoint, params=params)
            data = response.json()
            
            if data["status"] == "1":
                token_data = data["result"][0] if isinstance(data["result"], list) else data["result"]
                return {
                    "name": token_data.get("name", "Unknown"),
                    "symbol": token_data.get("symbol", "UNKNOWN"),
                    "decimals": int(token_data.get("decimals", 18))
                }
            else:
                logger.error(f"获取以太坊代币信息失败: {data.get('message', 'Unknown error')}")
                # 尝试使用替代方法获取代币信息
                return get_token_info_alternative(token_address, chain, api_key)
        
        except Exception as e:
            logger.error(f"获取以太坊代币信息出错: {str(e)}")
            # 尝试使用替代方法获取代币信息
            return get_token_info_alternative(token_address, chain, api_key)
    
    # 对于Polygon，使用替代方法获取代币信息
    return get_token_info_alternative(token_address, chain, api_key)

def get_token_info_alternative(token_address: str, chain: str, api_key: str = None) -> Dict[str, Any]:
    """
    使用替代方法获取代币信息 (通过tokentx API)
    
    Args:
        token_address: 代币地址
        chain: 链名称
        api_key: API密钥(可选)
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 根据链名获取正确的API端点
    api_endpoint = None
    if chain == "ethereum":
        api_endpoint = API_ENDPOINTS.get("etherscan")
    elif chain == "polygon":
        api_endpoint = API_ENDPOINTS.get("polygonscan")
    
    if not api_endpoint:
        logger.error(f"不支持的链: {chain}")
        return {
            "name": "Unknown Token",
            "symbol": "UNKNOWN",
            "decimals": 18
        }
    
    # 使用tokentx接口获取代币信息
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "page": 1,
        "offset": 1,
        "sort": "desc"
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        logger.info(f"尝试通过交易记录获取{chain}代币信息: {token_address}")
        logger.info(f"API端点: {api_endpoint}")
        response = requests.get(api_endpoint, params=params, timeout=30)
        data = response.json()
        
        if data["status"] == "1" and data["result"]:
            tx = data["result"][0]
            return {
                "name": tx.get("tokenName", "Unknown Token"),
                "symbol": tx.get("tokenSymbol", "UNKNOWN"),
                "decimals": int(tx.get("tokenDecimal", 18))
            }
    except Exception as e:
        logger.error(f"通过交易记录获取代币信息失败: {str(e)}")
    
    logger.info(f"无法获取{chain}代币信息，使用默认值")
    return {
        "name": "Unknown Token",
        "symbol": "UNKNOWN",
        "decimals": 18
    }

def get_token_from_local_config(token_address: str, chain: str) -> Dict[str, Any]:
    """
    从本地配置文件中获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 尝试从tokens.json加载代币信息
    tokens_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tokens.json")
    
    if not os.path.exists(tokens_file):
        return {}
    
    try:
        with open(tokens_file, 'r', encoding='utf-8') as f:
            tokens_data = json.load(f)
        
        # 获取当前链的chain_id
        chain_id = "1" if chain == "ethereum" else "137" if chain == "polygon" else None
        
        if not chain_id:
            return {}
        
        # 遍历所有代币
        for symbol, token_info in tokens_data.items():
            if chain_id in token_info:
                if token_info[chain_id].get("address", "").lower() == token_address.lower():
                    return {
                        "name": token_info[chain_id].get("name", symbol),
                        "symbol": symbol,
                        "decimals": token_info[chain_id].get("decimals", 18)
                    }
        
        return {}
    
    except Exception as e:
        logger.error(f"从本地配置读取代币信息失败: {str(e)}")
        return {}

def get_token_transactions(token_address: str, chain: str, minutes: int = 10, api_key: str = None) -> List[Dict[str, Any]]:
    """获取代币最近的交易"""
    api_endpoint = API_ENDPOINTS.get(f"{chain}scan")
    if not api_endpoint:
        print(f"不支持的链: {chain}")
        return []
    
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "startblock": 0,
        "endblock": *********,
        "sort": "desc",
        "page": 1,
        "offset": 1000
    }
    
    if api_key:
        params["apikey"] = api_key
        print(f"使用API密钥: {api_key[:5]}...{api_key[-5:]}")
    
    try:
        print(f"正在请求{chain}scan API: {api_endpoint}")
        print(f"请求参数: {params}")
        response = requests.get(api_endpoint, params=params, timeout=30)
        data = response.json()
        print(f"{chain}scan API响应状态: {data.get('status')}, 消息: {data.get('message', 'No message')}")
        
        if data["status"] == "1":
            # 获取代币信息
            token_info = get_token_info(token_address, chain, api_key)
            
            # 计算时间范围
            now = int(time.time())
            start_time = now - (minutes * 60)
            
            transactions = []
            for tx in data["result"]:
                tx_timestamp = int(tx["timeStamp"])
                if tx_timestamp >= start_time:
                    # 保留原始数据，不进行精度转换
                    transactions.append({
                        "hash": tx["hash"],
                        "from": tx["from"],
                        "to": tx["to"],
                        "value": tx["value"],  # 保持原始值
                        "tokenDecimal": tx["tokenDecimal"],
                        "tokenSymbol": tx["tokenSymbol"],
                        "timestamp": tx_timestamp,
                        "blockNumber": tx["blockNumber"],
                        "timeStamp": tx["timeStamp"],
                        "contractAddress": tx["contractAddress"]
                    })
            return transactions
    except Exception as e:
        print(f"获取{chain}代币交易失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
    
    return []

def get_dex_contracts(chain: str) -> List[Dict[str, Any]]:
    """
    获取链上已知的DEX合约地址
    
    Args:
        chain: 链名称
        
    Returns:
        List[Dict[str, Any]]: DEX合约信息
    """
    if chain == "polygon":
        return [
            {
                "name": "QuickSwap",
                "addresses": [
                    "0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff",  # QuickSwap Router
                    "0xf5b509bb0909a69b1c207e495f687a596c168e12"   # QuickSwap V3 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "0x1b02da8cb0d097eb8d57a175b88c7d8b47997506"  # SushiSwap Router
                ]
            },
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************"   # Uniswap V3 Router 2
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "******************************************",  # AAVE Lending Pool
                    "******************************************"   # AAVE Pool
                ]
            }
        ]
    elif chain == "ethereum":
        return [
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************",  # Uniswap V3 Router 2
                    "******************************************"   # Uniswap V2 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "******************************************"  # SushiSwap Router
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "Curve",
                "addresses": [
                    "******************************************",  # Curve.fi Pool
                    "0xbEbc44782C7dB0a1A60Cb6fe97d0b483032FF1C7"   # Curve 3pool
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9",  # AAVE Lending Pool
                    "0x87870bca3f3fd6335c3f4ce8392d69350b4fa4e2"   # AAVE V3 Pool
                ]
            }
        ]
    else:
        return []

def format_transactions(transactions: List[Dict[str, Any]], verbose: bool = False) -> str:
    """
    格式化交易信息以便显示
    
    Args:
        transactions: 交易列表
        verbose: 是否显示详细信息
        
    Returns:
        str: 格式化后的输出
    """
    if not transactions:
        return "未找到任何交易"
    
    output = []
    output.append(f"找到 {len(transactions)} 笔交易")
    
    # 分类计数
    transfer_count = sum(1 for tx in transactions if tx.get("type") == "Transfer")
    dex_count = sum(1 for tx in transactions if tx.get("type") == "DEX")
    
    output.append(f"普通转账: {transfer_count} 笔, DEX交易: {dex_count} 笔")
    
    # 按类型分组
    dex_transactions = [tx for tx in transactions if tx.get("type") == "DEX"]
    transfer_transactions = [tx for tx in transactions if tx.get("type") == "Transfer"]
    
    # 先显示DEX交易
    if dex_transactions:
        output.append("\nDEX交易:")
        for i, tx in enumerate(dex_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  DEX: {tx.get('dex', 'Unknown')}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    # 然后显示普通转账
    if transfer_transactions:
        output.append("\n普通转账:")
        for i, tx in enumerate(transfer_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    return "\n".join(output)

def get_api_keys() -> Dict[str, str]:
    """
    从配置或环境变量中获取API密钥
    
    Returns:
        Dict[str, str]: API密钥字典
    """
    api_keys = {}
    
    # 尝试从配置中获取
    config = load_config()
    logger.info(f"加载配置文件")
    
    # 尝试从config.yaml格式获取API密钥
    if config and "api_keys" in config:
        # 处理新格式，如 etherscan: { key: "API_KEY" }
        if "etherscan" in config["api_keys"]:
            etherscan_config = config["api_keys"]["etherscan"]
            if isinstance(etherscan_config, dict) and "key" in etherscan_config:
                api_keys["etherscan"] = etherscan_config["key"]
                logger.info(f"从配置文件中加载了etherscan API密钥: {api_keys['etherscan'][:5]}...{api_keys['etherscan'][-5:]}")
            elif isinstance(etherscan_config, str):
                api_keys["etherscan"] = etherscan_config
                logger.info(f"从配置文件中加载了etherscan API密钥: {api_keys['etherscan'][:5]}...{api_keys['etherscan'][-5:]}")
        
        # 处理新格式，如 polygonscan: { key: "API_KEY" }
        if "polygonscan" in config["api_keys"]:
            polygonscan_config = config["api_keys"]["polygonscan"]
            if isinstance(polygonscan_config, dict) and "key" in polygonscan_config:
                api_keys["polygonscan"] = polygonscan_config["key"]
                logger.info(f"从配置文件中加载了polygonscan API密钥: {api_keys['polygonscan'][:5]}...{api_keys['polygonscan'][-5:]}")
            elif isinstance(polygonscan_config, str):
                api_keys["polygonscan"] = polygonscan_config
                logger.info(f"从配置文件中加载了polygonscan API密钥: {api_keys['polygonscan'][:5]}...{api_keys['polygonscan'][-5:]}")
        
        # 尝试直接获取每个键
        api_keys_dict = config["api_keys"]
        for key, value in api_keys_dict.items():
            # 跳过已处理的键或非字符串值
            if key in ["etherscan", "polygonscan"] or not isinstance(value, str):
                continue
            api_keys[key] = value
            logger.info(f"从配置文件中加载了{key} API密钥: {value[:5]}...{value[-5:]}")
    
    # 输出读取到的所有API密钥
    if api_keys:
        logger.info(f"已读取到以下API密钥: {list(api_keys.keys())}")
    else:
        logger.info("未从配置文件中读取到任何API密钥")
    
    # 尝试从环境变量获取
    for chain in ["polygon", "ethereum"]:
        scan_name = f"{chain}scan"
        env_var = f"{chain.upper()}_SCAN_API_KEY"
        
        if os.environ.get(env_var) and scan_name not in api_keys:
            api_keys[scan_name] = os.environ.get(env_var)
            logger.info(f"从环境变量加载了{scan_name} API密钥")
    
    return api_keys

def check_api_status(chain: str, api_key: str = None) -> str:
    """
    检查区块链浏览器API的状态
    
    Args:
        chain: 链名称 (ethereum或polygon)
        api_key: API密钥(可选)
        
    Returns:
        str: API状态，可能的值：
            - "ok": API正常工作
            - "rate_limited": API速率受限
            - "error": API请求出错
            - "unknown": 无法确定API状态
    """
    # 根据链名获取正确的API端点
    api_endpoint = None
    if chain == "ethereum":
        api_endpoint = API_ENDPOINTS.get("etherscan")
    elif chain == "polygon":
        api_endpoint = API_ENDPOINTS.get("polygonscan")
    
    if not api_endpoint:
        logger.error(f"不支持的链: {chain}，可用的API端点: {list(API_ENDPOINTS.keys())}")
        return "unknown"
    
    # 构建一个简单的API请求来检查状态
    params = {
        "module": "gastracker",
        "action": "gasoracle"
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        response = requests.get(api_endpoint, params=params, timeout=10)
        
        if response.status_code != 200:
            logger.error(f"API状态检查失败，状态码: {response.status_code}")
            return "error"
            
        data = response.json()
        
        if data["status"] == "1":
            return "ok"
        else:
            error_message = data.get("message", "").lower()
            if "rate limit" in error_message or "ratelimit" in error_message:
                logger.info(f"{chain}scan API速率限制: {error_message}")
                return "rate_limited"
            else:
                logger.error(f"{chain}scan API错误: {error_message}")
                return "error"
    
    except Exception as e:
        logger.error(f"检查{chain}scan API状态出错: {str(e)}")
        return "error"

def get_token_transactions_raw(token_address: str, chain: str, minutes: int = 10, api_key: str = None) -> Dict[str, Any]:
    """
    获取代币交易的原始API响应
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        minutes: 过去几分钟的交易
        api_key: API密钥(可选)
        
    Returns:
        Dict[str, Any]: 原始API响应
    """
    # 根据链名获取正确的API端点
    api_endpoint = None
    if chain == "ethereum":
        api_endpoint = API_ENDPOINTS.get("etherscan")
    elif chain == "polygon":
        api_endpoint = API_ENDPOINTS.get("polygonscan")
    
    if not api_endpoint:
        logger.error(f"不支持的链: {chain}，可用的API端点: {list(API_ENDPOINTS.keys())}")
        return {"status": "0", "message": f"不支持的链: {chain}", "result": []}
    
    # 计算时间范围
    now = int(time.time())
    start_time = now - (minutes * 60)
    
    # 构建API请求
    params = {
        "module": "account",
        "action": "tokentx",
        "contractaddress": token_address,
        "startblock": 0,
        "endblock": *********,
        "sort": "desc",
        "page": 1,
        "offset": 1000  # 限制结果数量
    }
    
    if api_key:
        params["apikey"] = api_key
    
    try:
        logger.info(f"正在请求{chain}scan API原始响应: {api_endpoint}")
        response = requests.get(api_endpoint, params=params, timeout=30)
        
        if response.status_code != 200:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return {
                "status": "0", 
                "message": f"HTTP错误: {response.status_code}", 
                "result": [], 
                "http_status_code": response.status_code
            }
            
        return response.json()
    
    except Exception as e:
        logger.error(f"获取{chain}代币交易原始响应失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "0", 
            "message": f"请求异常: {str(e)}", 
            "result": [],
            "exception": str(e)
        }

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="监控特定代币的最近交易")
    parser.add_argument("token_address", help="代币合约地址")
    parser.add_argument("--minutes", type=int, default=10, help="查询过去几分钟的交易 (默认: 10)")
    parser.add_argument("--chains", nargs="+", default=["polygon", "ethereum"], help="要监控的链 (默认: polygon ethereum)")
    parser.add_argument("--verbose", action="store_true", help="显示详细信息")
    
    args = parser.parse_args()
    
    # 初始化监控器
    monitor = TokenTransactionMonitor()
    
    # 监控交易
    all_transactions = []
    
    for chain in args.chains:
        print(f"\n===== 正在查询 {chain.upper()} 链上最近 {args.minutes} 分钟的交易 =====")
        result = monitor.get_transactions(args.token_address, chain, args.minutes)
        
        if "error" in result:
            print(f"错误: {result['error']}")
            continue
            
        transactions = result["transactions"]
        all_transactions.extend(transactions)
        
        # 显示统计信息
        print(f"找到 {len(transactions)} 笔交易")
        print(f"普通转账: {result['summary']['transfer_count']} 笔, DEX交易: {result['summary']['dex_count']} 笔\n")
        
        # 分类显示交易
        dex_transactions = [tx for tx in transactions if tx.get("type") == "DEX"]
        transfer_transactions = [tx for tx in transactions if tx.get("type") == "Transfer"]
        
        # 显示DEX交易
        if dex_transactions:
            print("DEX交易:")
            for i, tx in enumerate(dex_transactions, 1):
                print(f"\n交易 {i}:")
                print(f"  哈希: {tx['hash']}")
                print(f"  时间: {tx['datetime']}")
                print(f"  DEX: {tx.get('dex', 'Unknown')}")
                print(f"  从: {tx['from']}")
                print(f"  到: {tx['to']}")
                print(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
                if args.verbose:
                    print(f"  区块号: {tx['block_number']}")
                    print(f"  Gas消耗: {tx['gas_used']}")
        
        # 显示普通转账
        if transfer_transactions:
            print("\n普通转账:")
            for i, tx in enumerate(transfer_transactions, 1):
                print(f"\n交易 {i}:")
                print(f"  哈希: {tx['hash']}")
                print(f"  时间: {tx['datetime']}")
                print(f"  从: {tx['from']}")
                print(f"  到: {tx['to']}")
                print(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
                if args.verbose:
                    print(f"  区块号: {tx['block_number']}")
                    print(f"  Gas消耗: {tx['gas_used']}")
    
    # 显示所有链上交易的汇总
    if len(args.chains) > 1 and all_transactions:
        print("\n===== 所有链上交易汇总 =====")
        print(f"总计找到 {len(all_transactions)} 笔交易")
        
        # 按链和类型统计
        chain_stats = {}
        for tx in all_transactions:
            chain = tx.get("chain", "unknown")
            tx_type = tx.get("type", "unknown")
            
            if chain not in chain_stats:
                chain_stats[chain] = {"Transfer": 0, "DEX": 0}
            
            chain_stats[chain][tx_type] += 1
        
        # 显示统计
        for chain, stats in chain_stats.items():
            print(f"{chain.upper()}: 普通转账: {stats['Transfer']} 笔, DEX交易: {stats['DEX']} 笔")
        
        # 显示最新交易
        if all_transactions:
            all_transactions.sort(key=lambda x: x["timestamp"], reverse=True)
            latest_tx = all_transactions[0]
            
            print(f"\n最新交易 ({latest_tx['chain'].upper()}):")
            print(f"  时间: {latest_tx['datetime']}")
            print(f"  类型: {latest_tx['type']}")
            if latest_tx['type'] == "DEX":
                print(f"  DEX: {latest_tx.get('dex', 'Unknown')}")
            print(f"  数量: {latest_tx['value']:,.6f} {latest_tx['token_symbol']}")
            print(f"  哈希: {latest_tx['hash']}")

class TokenTransactionMonitor:
    """代币交易监控器"""
    
    def __init__(self, api_keys: Dict[str, str] = None):
        """
        初始化监控器
        
        Args:
            api_keys: API密钥字典，格式为 {"polygonscan": "KEY", "etherscan": "KEY"}
        """
        self.api_keys = api_keys or get_api_keys()
        self.api_endpoints = API_ENDPOINTS
        
    def get_transactions(self, token_address: str, chain: str, minutes: int = 10,
                        include_raw: bool = False) -> Dict[str, Any]:
        """
        获取代币交易历史
        
        Args:
            token_address: 代币合约地址
            chain: 链名称 (ethereum/polygon)
            minutes: 查询过去几分钟的交易
            include_raw: 是否包含原始API响应
            
        Returns:
            Dict: {
                "transactions": List[交易详情],
                "summary": {
                    "total_count": 总交易数,
                    "transfer_count": 普通转账数,
                    "dex_count": DEX交易数,
                    "volume": 交易量统计
                },
                "raw_response": 原始API响应(如果include_raw=True)
            }
        """
        try:
            # 获取API密钥
            api_key = None
            if chain == "ethereum":
                api_key = self.api_keys.get("etherscan")
            elif chain == "polygon":
                api_key = self.api_keys.get("polygonscan")
            
            # 获取RPC URL
            config = load_config()
            if chain == "polygon":
                rpc_url = config.get("rpc", {}).get("polygon", {}).get("rpc_url", "")
                if not rpc_url:
                    raise ValueError("未配置Polygon RPC节点")
            else:
                rpc_url = config.get("rpc", {}).get("ethereum", {}).get("rpc_url", "")
                if not rpc_url:
                    raise ValueError("未配置以太坊RPC节点")
            
            # 获取交易列表
            transactions = get_token_transactions(token_address, chain, minutes, api_key)
            
            # 计算统计信息
            transfer_count = sum(1 for tx in transactions if tx.get("type") == "Transfer")
            dex_count = sum(1 for tx in transactions if tx.get("type") == "DEX")
            
            # 计算交易量
            volume_stats = self._calculate_volume_stats(transactions)
            
            result = {
                "transactions": transactions,
                "summary": {
                    "total_count": len(transactions),
                    "transfer_count": transfer_count,
                    "dex_count": dex_count,
                    "volume": volume_stats
                }
            }
            
            # 如果需要原始响应
            if include_raw:
                raw_response = get_token_transactions_raw(token_address, chain, minutes, api_key)
                result["raw_response"] = raw_response
            
            return result
            
        except Exception as e:
            logger.error(f"获取交易历史失败: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                "error": str(e),
                "transactions": [],
                "summary": {
                    "total_count": 0,
                    "transfer_count": 0,
                    "dex_count": 0,
                    "volume": {}
                }
            }
    
    def monitor_multiple_chains(self, token_address: str, chains: List[str], 
                              minutes: int = 10) -> Dict[str, Any]:
        """
        监控多个链上的代币交易
        
        Args:
            token_address: 代币合约地址
            chains: 要监控的链列表 ["ethereum", "polygon"]
            minutes: 查询过去几分钟的交易
            
        Returns:
            Dict: {
                "chain_name": {
                    "transactions": [...],
                    "summary": {...}
                }
            }
        """
        results = {}
        for chain in chains:
            results[chain] = self.get_transactions(token_address, chain, minutes)
        return results
    
    def _calculate_volume_stats(self, transactions: List[Dict]) -> Dict[str, Any]:
        """计算交易量统计"""
        if not transactions:
            return {}
            
        volume_stats = {
            "total": 0.0,
            "transfer": 0.0,
            "dex": 0.0,
            "token_symbol": transactions[0].get("token_symbol", "UNKNOWN")
        }
        
        for tx in transactions:
            value = float(tx.get("value", 0))
            volume_stats["total"] += value
            
            if tx.get("type") == "Transfer":
                volume_stats["transfer"] += value
            elif tx.get("type") == "DEX":
                volume_stats["dex"] += value
        
        return volume_stats
    
    def get_dex_interactions(self, token_address: str, chain: str, 
                           minutes: int = 10) -> Dict[str, Any]:
        """
        获取代币与DEX的交互情况
        
        Args:
            token_address: 代币合约地址
            chain: 链名称
            minutes: 查询过去几分钟的交易
            
        Returns:
            Dict: DEX交互统计
        """
        transactions = self.get_transactions(token_address, chain, minutes)
        
        dex_stats = {}
        for tx in transactions["transactions"]:
            if tx.get("type") == "DEX":
                dex_name = tx.get("dex", "Unknown DEX")
                if dex_name not in dex_stats:
                    dex_stats[dex_name] = {
                        "count": 0,
                        "volume": 0.0
                    }
                
                dex_stats[dex_name]["count"] += 1
                dex_stats[dex_name]["volume"] += float(tx.get("value", 0))
        
        return {
            "dex_interactions": dex_stats,
            "total_dex_transactions": transactions["summary"]["dex_count"]
        }
    
    def get_token_info(self, token_address: str, chain: str) -> Dict[str, Any]:
        """
        获取代币信息
        
        Args:
            token_address: 代币合约地址
            chain: 链名称
            
        Returns:
            Dict: 代币信息
        """
        api_key = self.api_keys.get(f"{chain}scan")
        return get_token_info(token_address, chain, api_key)

    def get_token_decimals(self, token_address: str, chain: str) -> int:
        """获取代币精度"""
        if not token_address:
            logging.warning("代币地址为空")
            return 18
        
        logging.info(f"开始获取代币精度，地址: {token_address}")
        
        # 1. 先从本地配置获取
        try:
            # 修正路径构建逻辑
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            config_dir = os.path.join(project_root, 'data', 'utils', 'token', 'gate_tokens_with_decimals.json')
            logging.info(f"尝试从配置文件获取精度: {config_dir}")
            
            if os.path.exists(config_dir):
                with open(config_dir, 'r', encoding='utf-8') as f:
                    tokens_data = json.load(f)
                    
                # 获取对应链的代币列表
                chain_tokens = tokens_data.get(chain.lower(), [])
                
                # 查找代币信息（不区分大小写）
                token_address_lower = token_address.lower()
                for token in chain_tokens:
                    if token.get('contract_address', '').lower() == token_address_lower:
                        decimals = int(token.get('decimals', 18))
                        logging.info(f"从本地配置获取到代币精度: {decimals}")
                        return decimals
                logging.info("在本地配置中未找到该代币")
            else:
                logging.warning(f"配置文件不存在: {config_dir}")
                
        except Exception as e:
            logging.warning(f"从本地配置获取代币精度失败: {str(e)}")
        
        # 2. 从交易数据中获取
        try:
            # 使用Web3获取合约精度
            web3 = Web3(Web3.HTTPProvider(
                "https://polygon-rpc.com" if chain == "polygon" else "https://eth.llamarpc.com"
            ))
            
            contract = web3.eth.contract(
                address=web3.to_checksum_address(token_address),
                abi=[{
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                }]
            )
            
            decimals = contract.functions.decimals().call()
            logging.info(f"从合约获取到代币精度: {decimals}")
            return decimals
            
        except Exception as e:
            logging.warning(f"从合约获取代币精度失败: {str(e)}")
        
        # 3. 使用默认值
        logging.warning(f"无法获取代币精度，使用默认值18")
        return 18

    def check_bridge_deposit(self, transactions: List[Dict], receiver_address: str, expected_amount: Optional[float] = None):
        """检查桥接存款交易"""
        logging.info(f"开始检查桥接存款交易，接收地址: {receiver_address}")
        logging.info(f"预期金额: {expected_amount if expected_amount is not None else '未指定'}")
        logging.info(f"交易数量: {len(transactions)}")
        
        # 记录前几笔交易的基本信息用于调试
        for i, tx in enumerate(transactions[:5]):
            logging.info(f"交易 {i+1} 信息:")
            logging.info(f"  哈希: {tx.get('hash')}")
            logging.info(f"  发送方: {tx.get('from')}")
            logging.info(f"  接收方: {tx.get('to')}")
            logging.info(f"  代币地址: {tx.get('contractAddress')}")
            logging.info(f"  原始金额: {tx.get('value')}")
            
        for tx in transactions:
            try:
                if (tx['from'].lower() == "0x0000000000000000000000000000000000000000" and
                    tx['to'].lower() == receiver_address.lower()):
                    
                    token_address = tx.get('contractAddress')
                    token_value = tx.get('value', '0')
                    
                    # 使用改进后的方法获取精度
                    token_decimals = self.get_token_decimals(token_address, "polygon")
                    
                    # 使用Decimal处理金额
                    amount = Decimal(token_value) / Decimal(str(10 ** token_decimals))
                    
                    logging.info(f"找到匹配的桥接交易:")
                    logging.info(f"交易哈希: {tx.get('hash')}")
                    logging.info(f"发送方: {tx.get('from')}")
                    logging.info(f"接收方: {tx.get('to')}")
                    logging.info(f"代币地址: {token_address}")
                    logging.info(f"原始金额: {token_value}")
                    logging.info(f"代币精度: {token_decimals}")
                    logging.info(f"计算后金额: {amount}")
                    
                    # 如果提供了预期金额，检查是否匹配
                    if expected_amount is not None:
                        expected_decimal = Decimal(str(expected_amount))
                        received_decimal = amount
                        # 允许0.1%的误差
                        tolerance = expected_decimal * Decimal('0.001')
                        difference = abs(expected_decimal - received_decimal)
                        
                        if difference > tolerance:
                            logging.warning(f"金额不匹配: 预期 {expected_decimal}, 实际收到 {received_decimal}, 差额 {difference}")
                            continue
                        else:
                            logging.info(f"金额匹配: 预期 {expected_decimal}, 实际收到 {received_decimal}, 差额在允许范围内")
                    
                    return {
                        "success": True,
                        "amount": float(amount),
                        "tx_hash": tx.get('hash'),
                        "block_number": tx.get('blockNumber'),
                        "timestamp": tx.get('timeStamp'),
                        "from": tx.get('from')
                    }
                    
            except Exception as e:
                logging.error(f"处理交易时出错: {str(e)}")
                logging.error(f"交易数据: {tx}")
                continue
        
        logging.warning(f"未找到匹配的桥接交易")
        return {"success": False, "amount": "0"}

if __name__ == "__main__":
    main() 