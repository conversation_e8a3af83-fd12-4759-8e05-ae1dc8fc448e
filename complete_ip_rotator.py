#!/usr/bin/env python3
"""
完整的IP轮换器
包含订阅更新、代理管理、自动轮换等功能
"""

import sys
import time
import random
import logging
import threading
import subprocess
import tempfile
import yaml
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.utils.ip.subscription_updater import SubscriptionUpdater

class CompleteIPRotator:
    """完整的IP轮换器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化IP轮换器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.updater = SubscriptionUpdater(config_path)
        self.current_proxy = None
        self.is_running = False
        self.rotation_thread = None
        self.start_time = None
        self.switch_count = 0
        self.clash_process = None
        self.clash_config_file = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/complete_ip_rotator.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def update_subscription(self) -> bool:
        """更新订阅"""
        print("🔄 正在更新订阅...")
        success = self.updater.update_subscription()
        
        if success:
            count = self.updater.get_proxy_count()
            last_update = self.updater.get_last_update_time()
            print(f"✅ 订阅更新成功！")
            print(f"   节点数量: {count}")
            print(f"   更新时间: {last_update}")
            
            # 显示地区分布
            self._show_region_distribution()
        else:
            print("❌ 订阅更新失败")
        
        return success
    
    def _show_region_distribution(self):
        """显示地区分布"""
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        regions = {}
        for proxy in proxies:
            name = proxy.get('name', '')
            for region in ['香港', '台湾', '日本', '新加坡', '美国', '韩国', '英国', '德国', '法国']:
                if region in name:
                    regions[region] = regions.get(region, 0) + 1
                    break
        
        if regions:
            print("📊 节点分布:")
            for region, count in regions.items():
                print(f"   {region}: {count}个")
    
    def _generate_clash_config(self, proxy: dict) -> dict:
        """生成Clash配置"""
        config = self._load_config()
        local_proxy = config.get('local_proxy', {})
        dns_config = config.get('dns', {})
        
        clash_config = {
            'mixed-port': local_proxy.get('mixed_port', 7890),
            'allow-lan': local_proxy.get('allow_lan', False),
            'bind-address': local_proxy.get('bind_address', '*'),
            'mode': local_proxy.get('mode', 'global'),
            'log-level': local_proxy.get('log_level', 'info'),
            'ipv6': local_proxy.get('ipv6', False),
            'external-controller': local_proxy.get('external_controller', '127.0.0.1:9090'),
            'dns': dns_config,
            'proxies': [proxy],
            'proxy-groups': [
                {
                    'name': 'PROXY',
                    'type': 'select',
                    'proxies': [proxy['name']]
                }
            ],
            'rules': [
                'MATCH,PROXY'
            ]
        }
        
        return clash_config
    
    def _start_clash(self, proxy: dict) -> bool:
        """启动Clash代理客户端"""
        try:
            # 停止现有的Clash进程
            self._stop_clash()
            
            # 生成Clash配置
            clash_config = self._generate_clash_config(proxy)
            
            # 创建临时配置文件
            self.clash_config_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.yaml', 
                delete=False,
                encoding='utf-8'
            )
            
            yaml.dump(clash_config, self.clash_config_file, 
                     default_flow_style=False, allow_unicode=True)
            self.clash_config_file.close()
            
            # 启动Clash（需要先安装Clash）
            clash_cmd = [
                'clash',
                '-f', self.clash_config_file.name
            ]
            
            self.clash_process = subprocess.Popen(
                clash_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
            )
            
            # 等待启动
            time.sleep(3)
            
            # 检查进程是否正常运行
            if self.clash_process.poll() is None:
                # 设置系统代理
                self._set_system_proxy(True)
                return True
            else:
                self.logger.error("Clash启动失败")
                return False
                
        except FileNotFoundError:
            self.logger.error("未找到Clash程序，请先安装Clash")
            return False
        except Exception as e:
            self.logger.error(f"启动Clash失败: {e}")
            return False
    
    def _stop_clash(self):
        """停止Clash"""
        try:
            if self.clash_process:
                self.clash_process.terminate()
                try:
                    self.clash_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.clash_process.kill()
                    self.clash_process.wait()
                
                self.clash_process = None
            
            # 删除临时配置文件
            if self.clash_config_file and Path(self.clash_config_file.name).exists():
                Path(self.clash_config_file.name).unlink()
                self.clash_config_file = None
                
        except Exception as e:
            self.logger.error(f"停止Clash失败: {e}")
    
    def _set_system_proxy(self, enable: bool = True):
        """设置系统代理"""
        try:
            if enable:
                # 启用系统代理，指向Clash
                cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
                cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f'
                
                subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
                subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
                
                self.logger.info("系统代理已启用: 127.0.0.1:7890")
            else:
                # 禁用系统代理
                cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
                subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
                
                self.logger.info("系统代理已禁用")
                
        except Exception as e:
            self.logger.error(f"设置系统代理失败: {e}")
    
    def switch_proxy(self, proxy_name: str = None, use_clash: bool = True) -> bool:
        """
        切换代理
        
        Args:
            proxy_name: 指定的代理名称（部分匹配）
            use_clash: 是否使用Clash客户端
            
        Returns:
            bool: 切换是否成功
        """
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if not proxies:
            print("❌ 代理列表为空，请先更新订阅")
            return False
        
        # 选择代理
        if proxy_name:
            proxy = None
            for p in proxies:
                if proxy_name.lower() in p.get('name', '').lower():
                    proxy = p
                    break
            if not proxy:
                print(f"❌ 未找到包含 '{proxy_name}' 的代理")
                return False
        else:
            proxy = random.choice(proxies)
        
        # 记录切换信息
        self.current_proxy = proxy
        self.switch_count += 1
        
        print(f"🔄 [{self.switch_count}] 切换到: {proxy['name']}")
        print(f"   服务器: {proxy['server']}:{proxy['port']}")
        print(f"   类型: {proxy['type']}")
        print(f"   时间: {datetime.now().strftime('%H:%M:%S')}")
        
        if use_clash:
            # 使用Clash客户端
            if self._start_clash(proxy):
                print("✅ Clash代理启动成功")
                self.logger.info(f"通过Clash切换到代理: {proxy['name']}")
                return True
            else:
                print("❌ Clash代理启动失败")
                return False
        else:
            # 仅记录，不启动实际代理
            self.logger.info(f"切换到代理: {proxy['name']} (仅记录)")
            return True
    
    def start_rotation(self, interval: int = 60, use_clash: bool = True):
        """启动自动轮换"""
        if self.is_running:
            print("⚠️  IP轮换已在运行中")
            return
        
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if not proxies:
            print("❌ 代理列表为空，请先更新订阅")
            return
        
        self.is_running = True
        self.start_time = datetime.now()
        self.switch_count = 0
        
        print(f"🚀 启动IP自动轮换")
        print(f"   轮换间隔: {interval}秒")
        print(f"   可用节点: {len(proxies)}个")
        print(f"   使用Clash: {'是' if use_clash else '否'}")
        print(f"   开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("   按 Ctrl+C 停止轮换")
        print("=" * 50)
        
        # 首次切换
        self.switch_proxy(use_clash=use_clash)
        
        try:
            while self.is_running:
                time.sleep(interval)
                if self.is_running:
                    self.switch_proxy(use_clash=use_clash)
        except KeyboardInterrupt:
            self.stop_rotation()
    
    def stop_rotation(self):
        """停止自动轮换"""
        if not self.is_running:
            return
            
        self.is_running = False
        end_time = datetime.now()
        duration = end_time - self.start_time if self.start_time else None
        
        # 停止Clash和系统代理
        self._stop_clash()
        self._set_system_proxy(False)
        
        print("\n" + "=" * 50)
        print("🛑 IP轮换已停止")
        if duration:
            print(f"   运行时长: {duration}")
        print(f"   总切换次数: {self.switch_count}")
        print(f"   停止时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.info(f"IP轮换停止，总切换次数: {self.switch_count}")
    
    def list_proxies(self, region: str = None):
        """列出代理"""
        proxies = self.updater.list_proxies_by_region(region)
        
        if region:
            print(f"📋 {region}地区代理列表 ({len(proxies)}个):")
        else:
            print(f"📋 所有代理列表 ({len(proxies)}个):")
        
        print("-" * 80)
        
        for i, proxy in enumerate(proxies, 1):
            status = "🟢 当前" if proxy == self.current_proxy else "⚪"
            print(f"{i:2d}. {status} {proxy['name']}")
            print(f"     服务器: {proxy['server']}:{proxy['port']}")
            print(f"     类型: {proxy['type']}")
            print()
    
    def get_status(self):
        """显示当前状态"""
        print("📊 IP轮换器状态")
        print("-" * 30)
        print(f"运行状态: {'🟢 运行中' if self.is_running else '🔴 已停止'}")
        print(f"当前代理: {self.current_proxy['name'] if self.current_proxy else '无'}")
        if self.current_proxy:
            print(f"代理服务器: {self.current_proxy['server']}:{self.current_proxy['port']}")
        
        count = self.updater.get_proxy_count()
        last_update = self.updater.get_last_update_time()
        print(f"可用节点: {count}个")
        print(f"切换次数: {self.switch_count}")
        print(f"上次更新: {last_update or '未更新'}")
        
        if self.start_time:
            runtime = datetime.now() - self.start_time
            print(f"运行时长: {runtime}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🔧 完整IP轮换器")
        print("=" * 30)
        print("使用方法:")
        print("  python complete_ip_rotator.py update                    - 更新订阅")
        print("  python complete_ip_rotator.py start [间隔] [--no-clash] - 启动自动轮换")
        print("  python complete_ip_rotator.py switch [名称] [--no-clash]- 手动切换代理")
        print("  python complete_ip_rotator.py list [地区]               - 列出代理")
        print("  python complete_ip_rotator.py status                   - 显示状态")
        print()
        print("示例:")
        print("  python complete_ip_rotator.py update                   - 更新订阅")
        print("  python complete_ip_rotator.py start 60                 - 每60秒切换一次")
        print("  python complete_ip_rotator.py switch 香港               - 切换到香港节点")
        print("  python complete_ip_rotator.py start 60 --no-clash      - 不使用Clash")
        return
    
    command = sys.argv[1].lower()
    rotator = CompleteIPRotator()
    
    if command == "update":
        rotator.update_subscription()
    
    elif command == "start":
        interval = 60
        use_clash = True
        
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                if sys.argv[2] != '--no-clash':
                    print("❌ 间隔时间必须是数字")
                    return
        
        if '--no-clash' in sys.argv:
            use_clash = False
        
        rotator.start_rotation(interval, use_clash)
    
    elif command == "switch":
        proxy_name = None
        use_clash = True
        
        for arg in sys.argv[2:]:
            if arg == '--no-clash':
                use_clash = False
            else:
                proxy_name = arg
        
        if rotator.switch_proxy(proxy_name, use_clash):
            print("✅ 代理切换成功")
        else:
            print("❌ 代理切换失败")
    
    elif command == "list":
        region = sys.argv[2] if len(sys.argv) > 2 else None
        rotator.list_proxies(region)
    
    elif command == "status":
        rotator.get_status()
    
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()
