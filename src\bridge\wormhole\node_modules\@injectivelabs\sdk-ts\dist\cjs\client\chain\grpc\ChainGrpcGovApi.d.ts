import { CosmosGovV1Gov, CosmosGovV1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcGovApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: CosmosGovV1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/gov.js").GovModuleStateParams>;
    fetchProposals({ status, pagination, }: {
        status: CosmosGovV1Gov.ProposalStatus;
        pagination?: PaginationOption;
    }): Promise<{
        proposals: import("../types/gov.js").Proposal[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchProposal(proposalId: number): Promise<import("../types/gov.js").Proposal | undefined>;
    fetchProposalDeposits({ proposalId, pagination, }: {
        proposalId: number;
        pagination?: PaginationOption;
    }): Promise<{
        deposits: import("../types/gov.js").ProposalDeposit[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchProposalVotes({ proposalId, pagination, }: {
        proposalId: number;
        pagination?: PaginationOption;
    }): Promise<{
        votes: import("../types/gov.js").Vote[];
        pagination: import("../../../types/pagination.js").Pagination;
    }>;
    fetchProposalTally(proposalId: number): Promise<import("../types/gov.js").TallyResult>;
}
