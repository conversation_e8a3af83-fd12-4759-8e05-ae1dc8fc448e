# 清理黑名单代币数据脚本

## 功能说明

该脚本用于从零交易统计文件中删除黑名单中的代币数据。

- **输入文件**: `results/blacklist.json` - 包含需要删除的代币ETH地址
- **目标文件**: `../../../src/bridge/pol_bridge/data/zero_tx_stats_detailed.json` - 零交易统计数据文件

## 使用方法

### 1. Python脚本直接调用

```bash
# 试运行模式（只显示将要删除的数据，不实际执行）
python scripts/arbitrage/portal_polygon_bridge/clean_blacklisted_tokens.py --dry-run

# 实际执行删除（会自动创建备份）
python scripts/arbitrage/portal_polygon_bridge/clean_blacklisted_tokens.py --backup

# 实际执行删除（不创建备份）
python scripts/arbitrage/portal_polygon_bridge/clean_blacklisted_tokens.py
```

### 2. 批处理文件调用（推荐）

```cmd
# 显示帮助信息
.\scripts\arbitrage\portal_polygon_bridge\clean_blacklisted_tokens.bat help

# 试运行模式
.\scripts\arbitrage\portal_polygon_bridge\clean_blacklisted_tokens.bat dry-run

# 实际执行删除（会自动创建备份）
.\scripts\arbitrage\portal_polygon_bridge\clean_blacklisted_tokens.bat
```

## 文件结构

```
scripts/arbitrage/portal_polygon_bridge/
├── clean_blacklisted_tokens.py      # Python脚本
├── clean_blacklisted_tokens.bat     # Windows批处理文件
├── results/
│   └── blacklist.json              # 黑名单文件
└── README_clean_blacklisted_tokens.md  # 说明文档

src/bridge/pol_bridge/data/
└── zero_tx_stats_detailed.json     # 目标数据文件
```

## 黑名单文件格式

`results/blacklist.json` 文件格式：

```json
{
    "eth_address": [
        "******************************************",
        "******************************************",
        "..."
    ],
    "details": [
        {
            "symbol": "GCR",
            "eth_address": "******************************************",
            "added_time": "2025-07-12 21:17:29",
            "reason": "累计利润低于-10 USDT"
        }
    ]
}
```

## 目标数据文件格式

`zero_tx_stats_detailed.json` 文件格式：

```json
{
    "record_key_1": {
        "name": "Token Name",
        "symbol": "TOKEN",
        "address": "polygon_address",
        "volume_usd": 0,
        "zero_tx_count": 1,
        "eth_address": "0x1234...",  // 用于匹配黑名单的字段
        "transactions": [...]
    }
}
```

## 安全特性

1. **备份功能**: 使用 `--backup` 参数会在删除前自动创建备份文件
2. **试运行模式**: 使用 `--dry-run` 参数可以预览将要删除的数据
3. **详细日志**: 显示删除的详细信息，包括代币名称、地址、交易数量等
4. **错误处理**: 完善的错误处理和文件验证

## 示例输出

```
🚀 开始清理黑名单代币数据...
📁 黑名单文件: .../blacklist.json
📁 零交易统计文件: .../zero_tx_stats_detailed.json
⚠️  运行模式: 实际删除
------------------------------------------------------------
✅ 成功加载黑名单文件
📊 黑名单中包含 7 个地址
✅ 成功加载零交易统计文件
📊 文件中包含 173 个代币记录
✅ 已创建备份文件: .../zero_tx_stats_detailed.json.backup_20250725_221700

🔍 找到 1 个需要删除的记录:
  📍 记录键: 0x6307b25a665efc992ec1c1bc403c38fddd7c661
     ETH地址: ******************************************
     代币: SURE_2 (inSure (PoS))
     零交易数量: 1
     交易量(USD): 0

✅ 已删除 1 个代币的数据
📊 剩余代币数量: 172
✅ 已保存清理后的数据
------------------------------------------------------------
🎉 清理操作完成！
```

## 注意事项

1. 建议在执行实际删除前先使用试运行模式查看将要删除的数据
2. 重要数据删除前请确保已创建备份
3. 脚本会根据 `eth_address` 字段进行匹配，地址比较不区分大小写
4. 删除操作不可逆，请谨慎使用
