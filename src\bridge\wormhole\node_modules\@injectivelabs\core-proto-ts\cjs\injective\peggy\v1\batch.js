"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutgoingTransferTx = exports.OutgoingTxBatch = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var attestation_1 = require("./attestation.js");
exports.protobufPackage = "injective.peggy.v1";
function createBaseOutgoingTxBatch() {
    return { batchNonce: "0", batchTimeout: "0", transactions: [], tokenContract: "", block: "0" };
}
exports.OutgoingTxBatch = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.batchNonce !== "0") {
            writer.uint32(8).uint64(message.batchNonce);
        }
        if (message.batchTimeout !== "0") {
            writer.uint32(16).uint64(message.batchTimeout);
        }
        try {
            for (var _b = __values(message.transactions), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.OutgoingTransferTx.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.tokenContract !== "") {
            writer.uint32(34).string(message.tokenContract);
        }
        if (message.block !== "0") {
            writer.uint32(40).uint64(message.block);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOutgoingTxBatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batchNonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.batchTimeout = longToString(reader.uint64());
                    break;
                case 3:
                    message.transactions.push(exports.OutgoingTransferTx.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.tokenContract = reader.string();
                    break;
                case 5:
                    message.block = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            batchNonce: isSet(object.batchNonce) ? String(object.batchNonce) : "0",
            batchTimeout: isSet(object.batchTimeout) ? String(object.batchTimeout) : "0",
            transactions: Array.isArray(object === null || object === void 0 ? void 0 : object.transactions)
                ? object.transactions.map(function (e) { return exports.OutgoingTransferTx.fromJSON(e); })
                : [],
            tokenContract: isSet(object.tokenContract) ? String(object.tokenContract) : "",
            block: isSet(object.block) ? String(object.block) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.batchNonce !== undefined && (obj.batchNonce = message.batchNonce);
        message.batchTimeout !== undefined && (obj.batchTimeout = message.batchTimeout);
        if (message.transactions) {
            obj.transactions = message.transactions.map(function (e) { return e ? exports.OutgoingTransferTx.toJSON(e) : undefined; });
        }
        else {
            obj.transactions = [];
        }
        message.tokenContract !== undefined && (obj.tokenContract = message.tokenContract);
        message.block !== undefined && (obj.block = message.block);
        return obj;
    },
    create: function (base) {
        return exports.OutgoingTxBatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseOutgoingTxBatch();
        message.batchNonce = (_a = object.batchNonce) !== null && _a !== void 0 ? _a : "0";
        message.batchTimeout = (_b = object.batchTimeout) !== null && _b !== void 0 ? _b : "0";
        message.transactions = ((_c = object.transactions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.OutgoingTransferTx.fromPartial(e); })) || [];
        message.tokenContract = (_d = object.tokenContract) !== null && _d !== void 0 ? _d : "";
        message.block = (_e = object.block) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseOutgoingTransferTx() {
    return { id: "0", sender: "", destAddress: "", erc20Token: undefined, erc20Fee: undefined };
}
exports.OutgoingTransferTx = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.id !== "0") {
            writer.uint32(8).uint64(message.id);
        }
        if (message.sender !== "") {
            writer.uint32(18).string(message.sender);
        }
        if (message.destAddress !== "") {
            writer.uint32(26).string(message.destAddress);
        }
        if (message.erc20Token !== undefined) {
            attestation_1.ERC20Token.encode(message.erc20Token, writer.uint32(34).fork()).ldelim();
        }
        if (message.erc20Fee !== undefined) {
            attestation_1.ERC20Token.encode(message.erc20Fee, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOutgoingTransferTx();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = longToString(reader.uint64());
                    break;
                case 2:
                    message.sender = reader.string();
                    break;
                case 3:
                    message.destAddress = reader.string();
                    break;
                case 4:
                    message.erc20Token = attestation_1.ERC20Token.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.erc20Fee = attestation_1.ERC20Token.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            id: isSet(object.id) ? String(object.id) : "0",
            sender: isSet(object.sender) ? String(object.sender) : "",
            destAddress: isSet(object.destAddress) ? String(object.destAddress) : "",
            erc20Token: isSet(object.erc20Token) ? attestation_1.ERC20Token.fromJSON(object.erc20Token) : undefined,
            erc20Fee: isSet(object.erc20Fee) ? attestation_1.ERC20Token.fromJSON(object.erc20Fee) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.sender !== undefined && (obj.sender = message.sender);
        message.destAddress !== undefined && (obj.destAddress = message.destAddress);
        message.erc20Token !== undefined &&
            (obj.erc20Token = message.erc20Token ? attestation_1.ERC20Token.toJSON(message.erc20Token) : undefined);
        message.erc20Fee !== undefined &&
            (obj.erc20Fee = message.erc20Fee ? attestation_1.ERC20Token.toJSON(message.erc20Fee) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.OutgoingTransferTx.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseOutgoingTransferTx();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "0";
        message.sender = (_b = object.sender) !== null && _b !== void 0 ? _b : "";
        message.destAddress = (_c = object.destAddress) !== null && _c !== void 0 ? _c : "";
        message.erc20Token = (object.erc20Token !== undefined && object.erc20Token !== null)
            ? attestation_1.ERC20Token.fromPartial(object.erc20Token)
            : undefined;
        message.erc20Fee = (object.erc20Fee !== undefined && object.erc20Fee !== null)
            ? attestation_1.ERC20Token.fromPartial(object.erc20Fee)
            : undefined;
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
