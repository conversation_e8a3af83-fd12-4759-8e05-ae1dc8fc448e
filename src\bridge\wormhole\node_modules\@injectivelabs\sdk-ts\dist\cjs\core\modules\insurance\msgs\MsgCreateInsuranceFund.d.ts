import { MsgBase } from '../../MsgBase.js';
import { CosmosBaseV1Beta1Coin, InjectiveInsuranceV1Beta1Tx, InjectiveOracleV1Beta1Oracle } from '@injectivelabs/core-proto-ts';
export declare namespace MsgCreateInsuranceFund {
    interface Params {
        fund: {
            ticker: string;
            quoteDenom: string;
            oracleBase: string;
            oracleQuote: string;
            oracleType: InjectiveOracleV1Beta1Oracle.OracleType;
            expiry?: number;
        };
        deposit: {
            amount: string;
            denom: string;
        };
        injectiveAddress: string;
    }
    type Proto = InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund;
}
/**
 * @category Messages
 */
export default class MsgCreateInsuranceFund extends MsgBase<MsgCreateInsuranceFund.Params, MsgCreateInsuranceFund.Proto> {
    static fromJSON(params: MsgCreateInsuranceFund.Params): MsgCreateInsuranceFund;
    toProto(): InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund;
    toData(): {
        sender: string;
        ticker: string;
        quoteDenom: string;
        oracleBase: string;
        oracleQuote: string;
        oracleType: InjectiveOracleV1Beta1Oracle.OracleType;
        expiry: string;
        initialDeposit: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            ticker: string;
            quote_denom: string;
            oracle_base: string;
            oracle_quote: string;
            oracle_type: InjectiveOracleV1Beta1Oracle.OracleType;
            expiry: string;
            initial_deposit: CosmosBaseV1Beta1Coin.Coin | undefined;
        };
    };
    toWeb3Gw(): {
        sender: string;
        ticker: string;
        quote_denom: string;
        oracle_base: string;
        oracle_quote: string;
        oracle_type: InjectiveOracleV1Beta1Oracle.OracleType;
        expiry: string;
        initial_deposit: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toEip712V2(): {
        oracle_type: string;
        sender: string;
        ticker: string;
        quote_denom: string;
        oracle_base: string;
        oracle_quote: string;
        expiry: string;
        initial_deposit: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund;
    };
    toBinary(): Uint8Array;
}
