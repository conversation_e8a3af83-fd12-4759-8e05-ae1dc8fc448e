{"version": 3, "file": "instruction.js", "sourceRoot": "", "sources": ["../../../../src/program/namespace/instruction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAIyB;AAQzB,6CAA0C;AAC1C,4CAKsB;AACtB,8CAA0D;AAC1D,kEAAoD;AASpD,MAAqB,2BAA2B;IACvC,MAAM,CAAC,KAAK,CACjB,KAAQ,EACR,QAAgC,EAChC,SAAoB;QAEpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAI,mBAAQ,CAAC,6BAA6B,CAAC,CAAC;SACnD;QAED,MAAM,EAAE,GAAG,CACT,GAAG,IAAsC,EACjB,EAAE;YAC1B,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAA,4BAAe,EAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;YACxD,IAAA,4BAAgB,EAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC/C,mBAAmB,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;YAEpC,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEvC,IAAI,GAAG,CAAC,iBAAiB,KAAK,SAAS,EAAE;gBACvC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,iBAAiB,CAAC,CAAC;aACrC;YAED,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBAChC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;aAC9C;YAED,OAAO,IAAI,gCAAsB,CAAC;gBAChC,IAAI;gBACJ,SAAS;gBACT,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAA,yBAAa,EAAC,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC;aAC5D,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,6DAA6D;QAC7D,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,IAAiD,EAAE,EAAE;YACrE,OAAO,2BAA2B,CAAC,aAAa,CAC9C,IAAI,EACJ,KAAK,CAAC,QAAQ,EACd,SAAS,EACT,KAAK,CAAC,IAAI,CACX,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,MAAM,CAAC,aAAa,CACzB,GAAyB,EACzB,QAAmC,EACnC,SAAoB,EACpB,MAAe;QAEf,IAAI,CAAC,GAAG,EAAE;YACR,OAAO,EAAE,CAAC;SACX;QAED,OAAO,QAAQ;aACZ,GAAG,CAAC,CAAC,GAAmB,EAAE,EAAE;YAC3B,mBAAmB;YACnB,MAAM,cAAc,GAClB,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/C,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAa,CAAC;gBAC1C,OAAO,2BAA2B,CAAC,aAAa,CAC9C,OAAO,EACN,GAAmB,CAAC,QAAQ,EAC7B,SAAS,EACT,MAAM,CACP,CAAC,IAAI,EAAE,CAAC;aACV;iBAAM;gBACL,MAAM,OAAO,GAAe,GAAiB,CAAC;gBAC9C,IAAI,MAAiB,CAAC;gBACtB,IAAI;oBACF,MAAM,GAAG,IAAA,4BAAgB,EAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAY,CAAC,CAAC;iBACrD;gBAAC,OAAO,GAAG,EAAE;oBACZ,MAAM,IAAI,KAAK,CACb,iCACE,GAAG,CAAC,IACN,uCACE,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,oBAAoB,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,EAC/D,iCAAiC,CAClC,CAAC;iBACH;gBAED,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChE,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC;gBAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC;gBAC/C,OAAO;oBACL,MAAM;oBACN,UAAU;oBACV,QAAQ;iBACT,CAAC;aACH;QACH,CAAC,CAAC;aACD,IAAI,EAAE,CAAC;IACZ,CAAC;CACF;AAjGD,8CAiGC;AAsED,mEAAmE;AACnE,SAAS,mBAAmB,CAAC,EAAkB,EAAE,GAAG,IAAW;IAC7D,OAAO;AACT,CAAC"}