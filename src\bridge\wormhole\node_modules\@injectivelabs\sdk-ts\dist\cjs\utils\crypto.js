"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignTypedDataVersionV4 = exports.TypedDataUtilsHashStruct = exports.TypedDataUtilsSanitizeData = exports.sanitizeTypedData = exports.publicKeyToAddress = exports.messageHash = exports.domainHash = exports.privateKeyHashToPublicKeyBase64 = exports.privateKeyToPublicKeyBase64 = exports.privateKeyHashToPublicKey = exports.privateKeyToPublicKey = exports.ripemd160 = exports.sha256 = exports.hashToHex = void 0;
exports.uint8ArrayToHex = uint8ArrayToHex;
exports.hexToUnit8Array = hexToUnit8Array;
exports.decompressPubKey = decompressPubKey;
// import CryptoEs from 'crypto-es'
const crypto_js_1 = __importDefault(require("crypto-js"));
const secp256k1_1 = require("@noble/curves/secp256k1");
const keccak256_1 = __importDefault(require("keccak256"));
const eth_sig_util_1 = require("@metamask/eth-sig-util");
const hashToHex = (data) => {
    return crypto_js_1.default.SHA256(crypto_js_1.default.enc.Base64.parse(data))
        .toString()
        .toUpperCase();
};
exports.hashToHex = hashToHex;
const sha256 = (data) => {
    const wordArray = crypto_js_1.default.lib.WordArray.create(data);
    const hash = crypto_js_1.default.SHA256(wordArray);
    return Uint8Array.from(Buffer.from(hash.toString(), 'hex'));
};
exports.sha256 = sha256;
const ripemd160 = (data) => {
    const wordArray = crypto_js_1.default.lib.WordArray.create(data);
    const hash = crypto_js_1.default.RIPEMD160(wordArray);
    return Uint8Array.from(Buffer.from(hash.toString(), 'hex'));
};
exports.ripemd160 = ripemd160;
const privateKeyToPublicKey = (privateKey) => {
    return secp256k1_1.secp256k1.getPublicKey(privateKey, true);
};
exports.privateKeyToPublicKey = privateKeyToPublicKey;
const privateKeyHashToPublicKey = (privateKeyHash) => {
    const privateKey = privateKeyHash.startsWith('0x')
        ? privateKeyHash.slice(2)
        : privateKeyHash;
    return secp256k1_1.secp256k1.getPublicKey(Buffer.from(privateKey, 'hex'), true);
};
exports.privateKeyHashToPublicKey = privateKeyHashToPublicKey;
const privateKeyToPublicKeyBase64 = (privateKey) => {
    return Buffer.from((0, exports.privateKeyToPublicKey)(privateKey)).toString('base64');
};
exports.privateKeyToPublicKeyBase64 = privateKeyToPublicKeyBase64;
const privateKeyHashToPublicKeyBase64 = (privateKeyHash) => {
    return Buffer.from((0, exports.privateKeyHashToPublicKey)(privateKeyHash)).toString('base64');
};
exports.privateKeyHashToPublicKeyBase64 = privateKeyHashToPublicKeyBase64;
const domainHash = (message) => eth_sig_util_1.TypedDataUtils.hashStruct('EIP712Domain', message.domain, message.types, eth_sig_util_1.SignTypedDataVersion.V4);
exports.domainHash = domainHash;
const messageHash = (message) => eth_sig_util_1.TypedDataUtils.hashStruct(message.primaryType, message.message, message.types, eth_sig_util_1.SignTypedDataVersion.V4);
exports.messageHash = messageHash;
function uint8ArrayToHex(arr) {
    return Buffer.from(arr).toString('hex');
}
function hexToUnit8Array(str) {
    return new Uint8Array(Buffer.from(str, 'hex'));
}
function decompressPubKey(startsWith02Or03) {
    const testBuffer = Buffer.from(startsWith02Or03, 'hex');
    if (testBuffer.length === 64)
        startsWith02Or03 = '04' + startsWith02Or03;
    const point = secp256k1_1.secp256k1.ProjectivePoint.fromHex(Buffer.from(testBuffer).toString('hex'));
    const decompressed = point.toHex(false);
    if (!decompressed.startsWith('04'))
        return decompressed;
    return decompressed.slice(2);
}
const publicKeyToAddress = function (pubKey, sanitize = false) {
    if (sanitize && pubKey.length !== 64) {
        pubKey = secp256k1_1.secp256k1.ProjectivePoint.fromHex(pubKey)
            .toRawBytes(false)
            .slice(1);
    }
    if (pubKey.length !== 64) {
        throw new Error('Expected pubKey to be of length 64');
    }
    return (0, keccak256_1.default)(Buffer.from(pubKey)).subarray(-20);
};
exports.publicKeyToAddress = publicKeyToAddress;
const sanitizeTypedData = (data) => {
    switch (Object.prototype.toString.call(data)) {
        case '[object Object]': {
            const entries = Object.keys(data).map((k) => [
                k,
                (0, exports.sanitizeTypedData)(data[k]),
            ]);
            return Object.fromEntries(entries);
        }
        case '[object Array]':
            return data.map((v) => (0, exports.sanitizeTypedData)(v));
        case '[object BigInt]':
            return data.toString();
        default:
            return data;
    }
};
exports.sanitizeTypedData = sanitizeTypedData;
exports.TypedDataUtilsSanitizeData = eth_sig_util_1.TypedDataUtils.sanitizeData;
exports.TypedDataUtilsHashStruct = eth_sig_util_1.TypedDataUtils.hashStruct;
exports.SignTypedDataVersionV4 = eth_sig_util_1.SignTypedDataVersion.V4;
