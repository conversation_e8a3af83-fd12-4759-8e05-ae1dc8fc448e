{"version": 3, "file": "instruction.js", "sourceRoot": "", "sources": ["../../../../src/coder/system/instruction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAuB;AACvB,4DAA8C;AAC9C,0DAAkC;AAIlC,MAAa,sBAAsB;IACjC,gEAAgE;IAChE,YAAY,CAAM,IAAG,CAAC;IAEtB,MAAM,CAAC,MAAc,EAAE,EAAO;QAC5B,QAAQ,IAAA,mBAAS,EAAC,MAAM,CAAC,EAAE;YACzB,KAAK,eAAe,CAAC,CAAC;gBACpB,OAAO,mBAAmB,CAAC,EAAE,CAAC,CAAC;aAChC;YACD,KAAK,QAAQ,CAAC,CAAC;gBACb,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC;aACzB;YACD,KAAK,UAAU,CAAC,CAAC;gBACf,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;YACD,KAAK,uBAAuB,CAAC,CAAC;gBAC5B,OAAO,2BAA2B,CAAC,EAAE,CAAC,CAAC;aACxC;YACD,KAAK,qBAAqB,CAAC,CAAC;gBAC1B,OAAO,yBAAyB,CAAC,EAAE,CAAC,CAAC;aACtC;YACD,KAAK,sBAAsB,CAAC,CAAC;gBAC3B,OAAO,0BAA0B,CAAC,EAAE,CAAC,CAAC;aACvC;YACD,KAAK,wBAAwB,CAAC,CAAC;gBAC7B,OAAO,4BAA4B,CAAC,EAAE,CAAC,CAAC;aACzC;YACD,KAAK,uBAAuB,CAAC,CAAC;gBAC5B,OAAO,2BAA2B,CAAC,EAAE,CAAC,CAAC;aACxC;YACD,KAAK,UAAU,CAAC,CAAC;gBACf,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;aAC3B;YACD,KAAK,kBAAkB,CAAC,CAAC;gBACvB,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;aACnC;YACD,KAAK,gBAAgB,CAAC,CAAC;gBACrB,OAAO,oBAAoB,CAAC,EAAE,CAAC,CAAC;aACjC;YACD,KAAK,kBAAkB,CAAC,CAAC;gBACvB,OAAO,sBAAsB,CAAC,EAAE,CAAC,CAAC;aACnC;YACD,OAAO,CAAC,CAAC;gBACP,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;aACnD;SACF;IACH,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,GAAQ;QACnC,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;CACF;AAnDD,wDAmDC;AAED,MAAM,gBAAiB,SAAQ,YAAY,CAAC,MAAqB;IAgB/D,YAAmB,QAAiB;QAClC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QADH,aAAQ,GAAR,QAAQ,CAAS;QAfpC,WAAM,GAAG,YAAY,CAAC,MAAM,CAO1B;YACE,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC1B,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC;YACjC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;SACxE,EACD,IAAI,CAAC,QAAQ,CACd,CAAC;IAIF,CAAC;IAED,MAAM,CAAC,GAAkB,EAAE,CAAS,EAAE,MAAM,GAAG,CAAC;QAC9C,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;SACzB;QAED,MAAM,IAAI,GAAG;YACX,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;SAChC,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,CAAC,CAAS,EAAE,MAAM,GAAG,CAAC;QAC3B,OAAO,CACL,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI;YACvB,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI;YACvB,IAAI,eAAE,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,CACzE,CAAC;IACJ,CAAC;CACF;AAED,SAAS,gBAAgB,CAAC,QAAgB;IACxC,OAAO,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED,SAAS,SAAS,CAAC,QAAgB;IACjC,OAAO,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,mBAAmB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAO;IAC1D,OAAO,UAAU,CAAC;QAChB,aAAa,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE;KAC5D,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,EAAE,KAAK,EAAO;IAClC,OAAO,UAAU,CAAC;QAChB,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE;KACpC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,QAAQ,EAAO;IACvC,OAAO,UAAU,CAAC;QAChB,QAAQ,EAAE,EAAE,QAAQ,EAAE;KACvB,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAAC,EACnC,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,KAAK,GACD;IACJ,OAAO,UAAU,CACf;QACE,qBAAqB,EAAE;YACrB,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;SACxB;KACF,EACD,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACxC,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CAAC,EAAE,UAAU,EAAO;IACvD,OAAO,UAAU,CAAC;QAChB,sBAAsB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;KAC9D,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yBAAyB,CAAC,EAAE,UAAU,EAAO;IACpD,OAAO,UAAU,CAAC;QAChB,mBAAmB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;KAC3D,CAAC,CAAC;AACL,CAAC;AAED,SAAS,0BAA0B,CAAC,EAAE,QAAQ,EAAO;IACnD,OAAO,UAAU,CAAC;QAChB,oBAAoB,EAAE,EAAE,QAAQ,EAAE;KACnC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,2BAA2B,CAAC,EAAE,UAAU,EAAO;IACtD,OAAO,UAAU,CAAC;QAChB,qBAAqB,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE,EAAE;KAC7D,CAAC,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,EAAE,KAAK,EAAO;IACpC,OAAO,UAAU,CAAC;QAChB,QAAQ,EAAE,EAAE,KAAK,EAAE;KACpB,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAO;IAC/D,OAAO,UAAU,CACf;QACE,gBAAgB,EAAE;YAChB,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;YACJ,KAAK;YACL,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;SACxB;KACF,EACD,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACxC,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAO;IACtD,OAAO,UAAU,CACf;QACE,cAAc,EAAE;YACd,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACrB,IAAI;YACJ,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;SACxB;KACF,EACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACzC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAO;IAC5D,OAAO,UAAU,CACf;QACE,gBAAgB,EAAE;YAChB,QAAQ;YACR,IAAI;YACJ,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;SACxB;KACF,EACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CACzC,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;AACnE,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC;IAClB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;IAC7B,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;IAC1B,SAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,eAAe,CAChB,CAAC;AACF,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC1E,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EACpD,UAAU,CACX,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC;IAClB,SAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;IACxB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;IAC7B,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;IAC1B,SAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,uBAAuB,CACxB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,qBAAqB,CACtB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EACpD,sBAAsB,CACvB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,wBAAwB,CACzB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,EAC9C,uBAAuB,CACxB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EACjD,UAAU,CACX,CAAC;AACF,MAAM,CAAC,UAAU,CACf,CAAC,EACD,YAAY,CAAC,MAAM,CAAC;IAClB,SAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;IACxB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;IAC1B,SAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,kBAAkB,CACnB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,EAAE,EACF,YAAY,CAAC,MAAM,CAAC;IAClB,SAAS,CAAC,MAAM,CAAC;IACjB,gBAAgB,CAAC,MAAM,CAAC;IACxB,SAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,gBAAgB,CACjB,CAAC;AACF,MAAM,CAAC,UAAU,CACf,EAAE,EACF,YAAY,CAAC,MAAM,CAAC;IAClB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;IAC7B,gBAAgB,CAAC,MAAM,CAAC;IACxB,SAAS,CAAC,OAAO,CAAC;CACnB,CAAC,EACF,kBAAkB,CACnB,CAAC;AAEF,SAAS,UAAU,CAAC,WAAgB,EAAE,OAAgB;IACpD,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,kBAAkB,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAE3C,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;KACzB;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CACjC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAC1D,CAAC"}