import { toUtf8 } from '../../../utils/index.js';
export class NeptuneQueryTransformer {
    static contractPricesResponseToPrices(response) {
        const data = JSON.parse(toUtf8(response.data));
        return data.map(([assetInfo, priceInfo]) => ({
            assetInfo,
            price: priceInfo.price,
        }));
    }
    static contractLendingRatesResponseToLendingRates(response) {
        const data = JSON.parse(toUtf8(response.data));
        return data.map(([assetInfo, lendingRate]) => ({
            assetInfo,
            lendingRate,
        }));
    }
}
