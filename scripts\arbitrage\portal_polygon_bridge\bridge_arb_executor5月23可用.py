#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动执行Polygon-以太坊之间的套利交易买入操作
核心功能：执行代币买入交易并追踪交易结果
API接口：execute_buy, execute_trade_in_thread
"""

import os
import sys
import json
import yaml
import logging
import asyncio
import time
import traceback
import requests
import threading
import random
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from web3 import Web3
from src.network.tx_token_change_tracker import get_transaction_token
from src.bridge.pol_bridge.auto_bridge import AutoBridge
from decimal import Decimal, ROUND_DOWN

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# 确保必要的目录存在
EXECUTED_DIR = os.path.join("data", "arbitrage", "portal_polygon_bridge", "executed")
os.makedirs(EXECUTED_DIR, exist_ok=True)

# 设置固定的日志目录
PROJECT_ROOT = r"C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev"
RESULTS_DIR = os.path.join(PROJECT_ROOT, "scripts", "arbitrage", "portal_polygon_bridge", "results")
TRADE_DIR = os.path.join(RESULTS_DIR, "trade")
os.makedirs(RESULTS_DIR, exist_ok=True)
os.makedirs(TRADE_DIR, exist_ok=True)

# 日志文件大小限制 (18MB)
MAX_LOG_SIZE = 18 * 1024 * 1024

# 检查并管理日志文件大小
def manage_log_file(log_file_path):
    """
    检查日志文件大小，如果超过限制则删除一半旧内容
    
    Args:
        log_file_path: 日志文件路径
    """
    if os.path.exists(log_file_path):
        file_size = os.path.getsize(log_file_path)
        if file_size > MAX_LOG_SIZE:
            print(f"日志文件 {log_file_path} 大小超过 {MAX_LOG_SIZE/1024/1024}MB，删除一半旧数据")
            try:
                # 读取现有日志
                with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                # 保留后半部分（较新的日志）
                new_lines = lines[len(lines)//2:]
                
                # 写入新内容
                with open(log_file_path, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                
                print(f"已清理日志文件 {log_file_path}，保留 {len(new_lines)} 行")
            except Exception as e:
                print(f"清理日志文件 {log_file_path} 失败: {str(e)}")

# 设置固定格式的日志文件路径
def get_symbol_log_file(symbol):
    """获取币种特定的固定日志文件路径"""
    # 将执行日志也放在trade目录下
    log_file = os.path.join(TRADE_DIR, f"executor_{symbol}.log")
    # 检查并管理文件大小
    manage_log_file(log_file)
    return log_file

# 设置固定格式的交易日志文件路径
def get_trade_log_file(symbol):
    """获取币种特定的交易日志文件路径"""
    log_file = os.path.join(TRADE_DIR, f"trade_{symbol}.log")
    # 检查并管理文件大小
    manage_log_file(log_file)
    return log_file

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join("logs", "arb_executor.log"), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("ArbitrageExecutor")

def load_config() -> Dict:
    """
    加载配置文件
    
    Returns:
        配置字典
    """
    config_path = os.path.join("config", "config.yaml")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return {}

def save_executed_trade(opportunity: Dict, success: bool, execution_record: Dict = None):
    """
    保存执行过的交易记录
    
    Args:
        opportunity: 套利机会详情
        success: 交易是否成功
        execution_record: 可选的执行记录详情
    """
    try:
        # 检查执行目录是否存在，不存在则创建
        if not os.path.exists(EXECUTED_DIR):
            os.makedirs(EXECUTED_DIR)
        
        # 如果没有提供执行记录，则从机会创建一个
        if execution_record is None:
            # 确保opportunity_hash存在
            opportunity_hash = opportunity.get('hash')
            if not opportunity_hash:
                # 如果哈希不存在，创建一个
                symbol = opportunity.get('symbol', 'Unknown')
                timestamp = opportunity.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                price = opportunity.get('price', '')
                if price:
                    opportunity_hash = f"{symbol}_{price}_{timestamp}"
                else:
                    opportunity_hash = f"{symbol}_{timestamp}"
                # 更新opportunity中的hash
                opportunity['hash'] = opportunity_hash
            
            # 创建记录
            record = {
                'opportunity_hash': opportunity_hash,
                'symbol': opportunity.get('symbol', 'Unknown'),
                'chain': opportunity.get('lower_chain', 'unknown'),
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': success,
                'amount_bought': opportunity.get('amount_bought', 0),
                'tx_hash': opportunity.get('tx_hash', ''),
                'price': opportunity.get('price', 0)
            }
        else:
            # 使用提供的执行记录
            record = execution_record
            # 确保opportunity_hash字段存在
            if 'opportunity_hash' not in record and 'hash' in opportunity:
                record['opportunity_hash'] = opportunity['hash']
        
        # 读取已有记录
        file_path = os.path.join(EXECUTED_DIR, "executed_trades.json")
        existing_records = []
        
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    existing_records = json.load(f)
                if not isinstance(existing_records, list):
                    # 如果不是列表，转换为列表
                    existing_records = [existing_records] if existing_records else []
                    
                logger.info(f"读取到 {len(existing_records)} 条现有交易记录")
            except Exception as e:
                logger.error(f"读取现有交易记录失败: {str(e)}")
                existing_records = []
        
        # 检查是否已存在相同的记录（基于opportunity_hash）
        found_duplicate = False
        for existing_record in existing_records:
            if (existing_record.get('opportunity_hash') == record.get('opportunity_hash')):
                found_duplicate = True
                logger.info(f"跳过已存在的交易记录: {record.get('opportunity_hash')}")
                break
        
        # 只有在没有找到重复的情况下才添加新记录
        if not found_duplicate:
            # 添加新记录到列表
            existing_records.append(record)
            logger.info(f"添加新交易记录: {record.get('symbol')} ({record.get('opportunity_hash')})")
            
            # 保存回文件 - 使用安全的写入方式
            try:
                # 先写入临时文件
                temp_file = file_path + ".tmp"
                with open(temp_file, "w", encoding="utf-8") as f:
                    json.dump(existing_records, f, indent=2, ensure_ascii=False)
                
                # 然后重命名临时文件，替换原文件
                if os.path.exists(file_path):
                    os.replace(temp_file, file_path)
                else:
                    os.rename(temp_file, file_path)
                
                logger.info(f"成功保存 {len(existing_records)} 条交易记录")
            except Exception as e:
                logger.error(f"保存交易记录失败: {str(e)}")
                logger.error(traceback.format_exc())
        else:
            logger.info(f"交易记录 {record.get('opportunity_hash')} 已存在，未添加新记录")
        
    except Exception as e:
        logger.error(f"保存交易记录时出错: {str(e)}")
        logger.error(traceback.format_exc())

async def get_token_amount_from_tx(tx_hash: str, token_address: str, chain: str) -> float:
    """
    从交易哈希中获取实际买入的代币数量
    
    Args:
        tx_hash: 交易哈希
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        
    Returns:
        float: 实际买入的代币数量，如果获取失败则返回0
    """
    try:
        # 加载配置文件获取API密钥
        config = load_config()
        if not config:
            logger.error("无法加载配置文件")
            return 0
            
        # 根据链选择合适的API密钥和URL
        if chain.lower() == "ethereum":
            api_key = config.get("api_keys", {}).get("etherscan", {}).get("key", "")
            api_url = f"https://api.etherscan.io/api"
            block_explorer = "https://etherscan.io/tx/"
        elif chain.lower() == "polygon":
            api_key = config.get("api_keys", {}).get("polygonscan", {}).get("key", "")
            api_url = f"https://api.polygonscan.com/api"
            block_explorer = "https://polygonscan.com/tx/"
        else:
            logger.error(f"不支持的链类型: {chain}")
            return 0
            
        if not api_key:
            logger.warning(f"未找到{chain}的API密钥，无法解析交易")
            return 0
            
        # 获取钱包地址
        private_key = config.get("dex", {}).get(chain.lower(), {}).get("wallet", {}).get("private_key", "")
        if not private_key:
            logger.error(f"无法获取私钥，无法确定接收地址")
            return 0
            
        web3 = Web3()
        account = web3.eth.account.from_key(private_key)
        receiver_address = account.address
        logger.info(f"接收地址: {receiver_address}")
        
        # 设置代理
        proxies = None
        if "proxy" in config and config["proxy"].get("enabled", False):
            proxies = {
                "http": config["proxy"].get("http", ""),
                "https": config["proxy"].get("https", "")
            }
            logger.info(f"使用代理获取交易收据: {proxies}")
        
        logger.info(f"使用区块浏览器API获取交易信息: {block_explorer}{tx_hash}")
        
        # 仅使用API方法获取交易收据和日志（方法2）
        params = {
            "module": "proxy",
            "action": "eth_getTransactionReceipt",
            "txhash": tx_hash,
            "apikey": api_key
        }
        
        if proxies:
            response = requests.get(api_url, params=params, proxies=proxies, timeout=15)
        else:
            response = requests.get(api_url, params=params, timeout=15)
            
        if response and response.status_code == 200:
            data = response.json()
            
            if "result" in data and data["result"]:
                receipt = data["result"]
                logs = receipt.get("logs", [])
                logger.info(f"交易收据中包含 {len(logs)} 条日志")
                
                # ERC20 Transfer事件的签名
                transfer_signature = web3.keccak(text="Transfer(address,address,uint256)").hex()
                
                # 找出所有Token Transfer事件
                token_transfer_events = []
                
                for log in logs:
                    # 检查是否是Transfer事件
                    if len(log.get("topics", [])) >= 3 and log["topics"][0] == transfer_signature:
                        from_address = "0x" + log["topics"][1][-40:]
                        to_address = "0x" + log["topics"][2][-40:]
                        
                        # 检查接收地址是否匹配我们的地址
                        if to_address.lower() == receiver_address.lower():
                            # 检查是否是目标代币地址
                            if log["address"].lower() == token_address.lower():
                                # 解析转账金额
                                amount_wei = int(log["data"], 16)
                                token_contract = log["address"]
                                
                                token_transfer_events.append({
                                    "token_address": token_contract,
                                    "from_address": from_address,
                                    "to_address": to_address,
                                    "amount_wei": amount_wei,
                                    "log_index": int(log.get("logIndex", "0"), 16)
                                })
                
                # 根据logIndex排序，找到最后一个Transfer事件
                if token_transfer_events:
                    token_transfer_events.sort(key=lambda x: x["log_index"])
                    last_transfer = token_transfer_events[-1]
                    logger.info(f"找到 {len(token_transfer_events)} 个Token Transfer事件，使用最后一个事件（logIndex: {last_transfer['log_index']}）")
                    
                    # 获取代币精度
                    decimals = None  # 初始为None，表示尚未获取到精度
                    
                    # 方法1: 尝试从API获取代币精度
                    try:
                        # 使用区块浏览器API获取代币精度
                        if chain.lower() == "ethereum":
                            token_params = {
                                "module": "token",
                                "action": "tokeninfo",
                                "contractaddress": token_address,
                                "apikey": api_key
                            }
                        else:  # polygon
                            token_params = {
                                "module": "token",
                                "action": "tokeninfo",
                                "contractaddress": token_address,
                                "apikey": api_key
                            }
                        
                        # 发起API请求
                        if proxies:
                            token_response = requests.get(api_url, params=token_params, proxies=proxies, timeout=10)
                        else:
                            token_response = requests.get(api_url, params=token_params, timeout=10)
                        
                        if token_response and token_response.status_code == 200:
                            token_data = token_response.json()
                            if token_data.get("status") == "1" and "result" in token_data:
                                result = token_data["result"]
                                if isinstance(result, list) and len(result) > 0:
                                    token_info = result[0]
                                    if "decimals" in token_info:
                                        decimals = int(token_info["decimals"])
                                        logger.info(f"API成功: 从区块浏览器API获取到代币精度: {decimals}")
                                    else:
                                        logger.warning("API返回的代币信息中没有decimals字段")
                                else:
                                    logger.warning(f"API返回的代币信息格式异常: {result}")
                            else:
                                logger.warning(f"API返回状态不正确: {token_data.get('message', 'Unknown Error')}")
                    except Exception as e:
                        logger.warning(f"从API获取代币精度失败: {str(e)}")
                    
                    # 方法2: 如果API获取失败，从文件中获取
                    if decimals is None:
                        try:
                            # 获取文件路径
                            file_path = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
                            
                            # 检查文件是否存在
                            if os.path.exists(file_path):
                                # 读取文件内容
                                with open(file_path, "r", encoding="utf-8") as f:
                                    token_data = json.load(f)
                                
                                # 根据链选择对应的数据
                                chain_key = "ETH" if chain.lower() == "ethereum" else "POLYGON"
                                
                                # 在文件中查找匹配的代币地址
                                if chain_key in token_data:
                                    for token in token_data[chain_key]:
                                        if token.get("contract_address", "").lower() == token_address.lower():
                                            if token.get("decimals") is not None:
                                                decimals = int(token.get("decimals"))
                                                logger.info(f"文件成功: 从本地文件获取到代币 {token.get('symbol')} 的精度: {decimals}")
                                                break
                                else:
                                    logger.warning(f"在文件中找不到链 {chain_key} 的数据")
                            else:
                                logger.warning(f"本地文件 {file_path} 不存在")
                        except Exception as e:
                            logger.warning(f"从本地文件获取代币精度失败: {str(e)}")
                    
                    # 方法3: 如果前两种方法都失败，使用默认值18
                    if decimals is None:
                        decimals = 18
                        logger.warning(f"无法获取代币精度，使用默认值: {decimals}")
                    
                    # 计算实际数量
                    amount = last_transfer["amount_wei"] / (10 ** decimals)
                    logger.info(f"最终计算得到的代币数量: {amount} (使用精度: {decimals})")
                    return amount
                else:
                    logger.warning("未找到与目标代币地址和接收地址匹配的Transfer事件")
            else:
                logger.warning("无法获取交易收据或收据不包含结果")
        else:
            logger.error(f"API请求失败，状态码: {response.status_code if response else 'None'}")
            
        # 如果API方法失败，返回0
        logger.warning(f"API方法失败，无法从交易中获取代币数量，返回0")
        return 0
    except Exception as e:
        logger.error(f"获取交易代币数量时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return 0

async def execute_buy_transaction(opportunity: Dict) -> Tuple[bool, Dict]:
    """
    执行买入交易并自动桥接
    
    Args:
        opportunity: 套利机会详情
    
    Returns:
        Tuple[bool, Dict]: 交易是否成功和执行记录详情
    """
    start_time = time.time()
    
    # 提取必要信息
    symbol = opportunity.get('symbol', 'UNKNOWN')
    chain = opportunity.get('lower_chain', '')
    usdt_input = float(opportunity.get('usdt_input', 0))
    
    # 使用固定格式的日志文件，按币种保存
    exec_log_file = get_symbol_log_file(symbol)
    trade_log_file = get_trade_log_file(symbol)
    
    # 创建专门的文件处理器
    file_handler = logging.FileHandler(exec_log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
    
    # 创建交易日志文件并写入交易信息
    with open(trade_log_file, 'a', encoding='utf-8') as f:
        f.write("\n" + "=" * 80 + "\n")
        f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"币种: {symbol}\n")
        f.write(f"链: {chain}\n")
        f.write(f"以太坊地址: {opportunity.get('eth_address', 'N/A')}\n")
        f.write(f"Polygon地址: {opportunity.get('polygon_address', 'N/A')}\n")
        
        # 输出实际使用的地址
        if chain == "ethereum":
            f.write(f"使用地址: {opportunity.get('eth_address', 'N/A')}\n")
        else:
            f.write(f"使用地址: {opportunity.get('polygon_address', 'N/A')}\n")
            
        f.write(f"USDT投入: {usdt_input}\n")
        f.write(f"交易方向: {'ethereum_to_polygon' if chain == 'ethereum' else 'polygon_to_ethereum'}\n")
        f.write("=" * 80 + "\n\n")
    
    # 记录开始执行的详细信息
    logger.info("=" * 80)
    logger.info(f"开始执行 {symbol} 买入交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"链: {chain}, 投入金额: {usdt_input} USDT")
    if chain == "ethereum":
        logger.info(f"代币地址: {opportunity.get('eth_address', 'N/A')}")
    else:
        logger.info(f"代币地址: {opportunity.get('polygon_address', 'N/A')}")
    
    # 创建执行记录
    execution_record = {
        'opportunity_hash': opportunity.get('hash', f"{symbol}_{datetime.now().strftime('%Y%m%d%H%M%S')}"),
        'symbol': symbol,
        'chain': chain,
        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'usdt_input': usdt_input,
        'success': False,
        'amount_bought': 0,
        'tx_hash': '',
        'price': 0,
        'error': None,
        'log_file': exec_log_file  # 记录日志文件位置
    }
    
    try:
        if not chain:
            logger.error(f"{symbol}: 缺少链信息，无法执行交易")
            execution_record['error'] = "缺少链信息"
            save_executed_trade(opportunity, False, execution_record)
            return False, execution_record
        
        logger.info(f"收到{symbol}买入请求 - 链:{chain}, 投入:{usdt_input}USDT")
        
        # 检查USDT输入金额是否有效
        if usdt_input <= 0:
            logger.error(f"{symbol}: USDT输入金额必须大于0: {usdt_input}")
            execution_record['error'] = f"USDT输入金额无效: {usdt_input}"
            save_executed_trade(opportunity, False, execution_record)
            return False, execution_record
        
        # 获取相应链上的代币地址
        token_address = ""
        if chain == "ethereum":
            token_address = opportunity.get('eth_address', '')
        elif chain == "polygon":
            token_address = opportunity.get('polygon_address', '')
        else:
            logger.error(f"{symbol}: 不支持的链: {chain}")
            execution_record['error'] = f"不支持的链: {chain}"
            save_executed_trade(opportunity, False, execution_record)
            return False, execution_record
        
        if not token_address or not Web3.is_address(token_address):
            logger.error(f"{symbol}: 无效的代币地址: {token_address}")
            execution_record['error'] = f"无效的代币地址: {token_address}"
            save_executed_trade(opportunity, False, execution_record)
            return False, execution_record
        
        logger.info(f"{symbol}: 将在{chain}链上执行买入，代币地址: {token_address}")
        
        # 设置交易参数
        from_token = "USDT"  # 输入代币固定为USDT
        
        # 准备交易
        try:
            # 使用KyberSwap执行交易
            logger.info(f"{symbol}: 准备使用KyberSwap在{chain}上执行{usdt_input}USDT买入{symbol}交易")
            
            # 记录详细的模块导入信息
            try:
                # 检查模块路径
                import importlib.util
                swap_module_spec = importlib.util.find_spec("src.dex.KyberSwap.swap")
                if swap_module_spec:
                    logger.info(f"KyberSwap模块路径: {swap_module_spec.origin}")
                else:
                    logger.warning("无法找到KyberSwap模块路径")
                
                # 引入必要的模块
                from src.dex.KyberSwap.swap import swap_tokens
                
                # 显示模块信息
                import inspect
                swap_params = inspect.signature(swap_tokens).parameters
                logger.info(f"swap_tokens 函数参数: {list(swap_params.keys())}")
            except Exception as e:
                logger.error(f"检查模块信息时出错: {str(e)}")
                logger.error(traceback.format_exc())
            
            # 检查链是否支持
            supported_chains = ["ethereum", "polygon", "bsc", "base"]
            if chain.lower() not in supported_chains:
                logger.error(f"{symbol}: KyberSwap不支持的链: {chain}")
                execution_record['error'] = f"KyberSwap不支持的链: {chain}"
                save_executed_trade(opportunity, False, execution_record)
                return False, execution_record
            
            # 直接调用swap_tokens函数执行交易
            from src.dex.KyberSwap.swap import swap_tokens
            
            # 详细记录交易参数
            chain_name = chain.lower()
            logger.info(f"{symbol}: 准备调用swap_tokens函数，参数：")
            logger.info(f"  chain: {chain_name}")
            logger.info(f"  token_in: {from_token}")
            logger.info(f"  token_out: {token_address}")
            logger.info(f"  amount: {usdt_input}")
            logger.info(f"  slippage: 0.5%")
            logger.info(f"  real: True")
            
            # 执行交易
            result = await swap_tokens(
                chain=chain_name,
                token_in=from_token,
                token_out=token_address,
                amount=usdt_input,
                slippage=0.5,  # 0.5%滑点
                timeout=1200,  # 设置足够长的超时时间 (20分钟)
                gas_multiplier=1.8,
                real=True  # 真实交易
            )
            
            # 检查result是否为None
            if result is None:
                logger.error(f"{symbol}: swap_tokens函数返回了空结果")
                execution_record['error'] = "swap_tokens函数返回空结果"
                save_executed_trade(opportunity, False, execution_record)
                return False, execution_record
            
            # 检查swap_tokens返回结果的格式和内容
            logger.info(f"{symbol}: swap_tokens返回值类型: {type(result)}")
            logger.info(f"{symbol}: swap_tokens返回值内容: {result}")
            
            # 处理不同格式的返回值
            tx_hash = None
            amount_out = 0
            
            if isinstance(result, dict):
                # 新版本返回字典格式结果
                if result.get('success') and 'tx_hash' in result:
                    tx_hash = result['tx_hash']
                    logger.info(f"{symbol}: 交易成功 - 哈希: {tx_hash}")
                else:
                    error_msg = result.get('error', '未知错误')
                    logger.error(f"{symbol}: 交易失败 - {error_msg}")
                    execution_record['error'] = f"交易失败: {error_msg}"
                    save_executed_trade(opportunity, False, execution_record)
                    return False, execution_record
            elif isinstance(result, tuple) and len(result) == 2:
                # 返回元组 (tx_receipt, amount_out)
                tx_receipt, amount_out_tuple = result
                if tx_receipt and 'transactionHash' in tx_receipt:
                    tx_hash = tx_receipt['transactionHash'].hex()
                    logger.info(f"{symbol}: 交易成功 - 哈希: {tx_hash}")
                else:
                    logger.error(f"{symbol}: 交易失败 - 未获取到交易哈希")
                    execution_record['error'] = "交易失败，未获取到交易哈希"
                    save_executed_trade(opportunity, False, execution_record)
                    return False, execution_record
            else:
                logger.error(f"{symbol}: 无法识别的swap_tokens返回值格式: {result}")
                execution_record['error'] = "无法识别的swap_tokens返回值格式"
                save_executed_trade(opportunity, False, execution_record)
                return False, execution_record
                
            if tx_hash:
                # 获取实际买入的代币数量
                actual_amount = 0
                
                try:
                    # 获取钱包地址
                    config = load_config()
                    if not config:
                        logger.error("无法加载配置文件")
                        return False, execution_record
                        
                    private_key = config.get("dex", {}).get("ethereum", {}).get("wallet", {}).get("private_key")
                    if not private_key:
                        logger.error("无法获取以太坊私钥")
                        return False, execution_record
                        
                    web3 = Web3()
                    account = web3.eth.account.from_key(private_key)
                    user_address = account.address
                    
                    # 使用tx_token_change_tracker获取代币数量
                    logger.info(f"{symbol}: 使用tx_token_change_tracker获取交易 {tx_hash} 的代币数量...")
                    token_result = get_transaction_token(
                        tx_hash=tx_hash,
                        chain=chain,
                        user_address=user_address
                    )
                    
                    if "error" not in token_result:
                        # 保持原始字符串格式的数量，避免精度损失
                        actual_amount_str = str(token_result["amount"])
                        actual_amount = float(actual_amount_str)  # 仅用于显示和计算价格
                        logger.info(f"{symbol}: 从tx_token_change_tracker成功获取到代币数量: {actual_amount_str}")
                        
                        # 更新执行记录
                        execution_record.update({
                            'amount_bought': actual_amount_str,  # 使用字符串格式保存
                            'price': float(usdt_input) / float(actual_amount_str) if float(actual_amount_str) > 0 else 0,
                            'token_symbol': token_result.get('symbol', ''),
                            'token_name': token_result.get('name', ''),
                            'token_decimals': token_result.get('decimals', 18)
                        })
                        
                        # 初始化AutoBridge
                        try:
                            auto_bridge = AutoBridge(
                                ethereum_rpc_url=config.get("dex", {}).get("ethereum", {}).get("rpc_url", ""),
                                polygon_rpc_url=config.get("dex", {}).get("polygon", {}).get("rpc_url", ""),
                                private_key=private_key,
                                check_interval=60,
                                max_check_time=21600,  # 6小时
                                initial_wait=1800       # 30分钟
                            )
                            
                            # 获取当前链上的代币地址
                            if not token_address:
                                token_address = opportunity.get('eth_address') if chain.lower() == "ethereum" else opportunity.get('polygon_address')
                            
                            if not token_address:
                                logger.error(f"{symbol}: 无法获取代币地址")
                                execution_record["bridge_success"] = False
                                execution_record["bridge_message"] = "无法获取代币地址"
                                return True, execution_record
                            
                            # 根据当前链确定桥接方向
                            bridge_result = None
                            try:
                                if chain.lower() == "ethereum":
                                    logger.info(f"{symbol}: 开始从以太坊桥接到Polygon...")
                                    bridge_result = await auto_bridge.eth_to_polygon(
                                        token_address=token_address,
                                        amount=actual_amount_str,  # 使用字符串格式的精确数量
                                        wait_time=1200,
                                        direct=True
                                    )
                                    
                                    # 添加详细的错误检查
                                    if not bridge_result.get("success"):
                                        error_msg = bridge_result.get("message", "未知错误")
                                        logger.error(f"{symbol}: 桥接失败: {error_msg}")
                                        if "ethereum_account" in error_msg:
                                            logger.error(f"{symbol}: Bridge初始化错误，请检查private_key是否正确传入")
                                elif chain.lower() == "polygon":
                                    logger.info(f"{symbol}: 开始从Polygon桥接到以太坊...")
                                    bridge_result = await auto_bridge.polygon_to_eth(
                                        token_address=token_address,
                                        amount=actual_amount_str,  # 使用字符串格式的精确数量
                                        auto_claim=True
                                    )
                                
                                if bridge_result:
                                    # 等待桥接完成
                                    logger.info(f"{symbol}: 等待桥接完成...")
                                    
                                    # 记录桥接交易哈希
                                    if chain.lower() == "polygon":
                                        burn_tx = bridge_result.get("burn_tx")
                                        if burn_tx:
                                            logger.info(f"{symbol}: Burn交易哈希: {burn_tx}")
                                            
                                        # 等待claim交易完成
                                        claim_tx = bridge_result.get("claim_tx")
                                        if claim_tx:
                                            logger.info(f"{symbol}: Claim交易哈希: {claim_tx}")
                                        else:
                                            logger.warning(f"{symbol}: 未获取到Claim交易哈希")
                                    else:
                                        bridge_tx = bridge_result.get("bridge_tx")
                                        if bridge_tx:
                                            logger.info(f"{symbol}: 桥接交易哈希: {bridge_tx}")
                                            
                                        # 等待Polygon到账
                                        polygon_tx = bridge_result.get("polygon_tx")
                                        if polygon_tx:
                                            logger.info(f"{symbol}: Polygon到账交易哈希: {polygon_tx}")
                                        else:
                                            logger.warning(f"{symbol}: 未获取到Polygon到账交易哈希")
                                        
                                    logger.info(f"{symbol}: 桥接操作完成")
                                    
                                    # 更新执行记录，添加桥接信息
                                    execution_record.update({
                                        "bridge_success": bridge_result.get("success", False),
                                        "bridge_message": bridge_result.get("message", ""),
                                        "bridge_tx": bridge_result.get("bridge_tx") or bridge_result.get("burn_tx"),
                                        "bridge_amount": actual_amount_str,
                                        "claim_tx": bridge_result.get("claim_tx"),
                                        "polygon_tx": bridge_result.get("polygon_tx")
                                    })
                                    
                                    logger.info(f"{symbol}: 桥接操作完成，结果: {bridge_result}")
                                else:
                                    logger.error(f"{symbol}: 桥接操作失败")
                                    execution_record["bridge_success"] = False
                                    execution_record["bridge_message"] = "桥接操作失败"
                            except Exception as e:
                                logger.error(f"{symbol}: 桥接过程出错: {str(e)}")
                                execution_record["bridge_success"] = False
                                execution_record["bridge_message"] = f"桥接过程出错: {str(e)}"
                        except Exception as e:
                            logger.error(f"{symbol}: 初始化桥接工具时出错: {str(e)}")
                            execution_record["bridge_success"] = False
                            execution_record["bridge_message"] = f"初始化桥接工具时出错: {str(e)}"
                        
                        return True, execution_record
                    else:
                        logger.error(f"{symbol}: 从区块链获取数量失败: {token_result.get('error')}")
                        execution_record['error'] = f"从区块链获取数量失败: {token_result.get('error')}"
                        save_executed_trade(opportunity, False, execution_record)
                        return False, execution_record
                        
                except Exception as e:
                    logger.error(f"{symbol}: 从区块链获取数量时出错: {str(e)}")
                    logger.error(traceback.format_exc())
                    execution_record['error'] = f"从区块链获取数量时出错: {str(e)}"
                    save_executed_trade(opportunity, False, execution_record)
                    return False, execution_record
            else:
                logger.error(f"{symbol}: 交易失败 - 未获取到交易哈希")
                execution_record['error'] = "交易失败，未获取到交易哈希"
                
                # 在交易日志文件中记录失败
                try:
                    with open(trade_log_file, 'a', encoding='utf-8') as f:
                        f.write(f"交易失败 - 未获取到交易哈希\n")
                        f.write(f"失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("=" * 80 + "\n\n")
                except Exception as e:
                    logger.error(f"写入交易失败结果到交易日志文件时出错: {str(e)}")
                
                save_executed_trade(opportunity, False, execution_record)
                return False, execution_record
        
        except Exception as e:
            logger.error(f"{symbol}: 执行交易时出错: {str(e)}")
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"{symbol}: 错误详情:\n{error_trace}")
            
            execution_record['error'] = f"执行交易时出错: {str(e)}"
            
            # 在交易日志文件中记录错误
            try:
                with open(trade_log_file, 'a', encoding='utf-8') as f:
                    f.write(f"交易出错: {str(e)}\n")
                    f.write(f"错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 80 + "\n\n")
            except Exception as write_error:
                logger.error(f"写入交易错误结果到交易日志文件时出错: {str(write_error)}")
            
            save_executed_trade(opportunity, False, execution_record)
            return False, execution_record
        
    except Exception as e:
        logger.error(f"{symbol}: 处理买入交易时出错: {str(e)}")
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"{symbol}: 错误详情:\n{error_trace}")
        
        execution_record['error'] = f"处理买入交易时出错: {str(e)}"
        
        # 在交易日志文件中记录错误
        try:
            with open(trade_log_file, 'a', encoding='utf-8') as f:
                f.write(f"交易出错: {str(e)}\n")
                f.write(f"错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("=" * 80 + "\n\n")
        except Exception as write_error:
            logger.error(f"写入交易错误结果到交易日志文件时出错: {str(write_error)}")
            
        save_executed_trade(opportunity, False, execution_record)
        return False, execution_record
    finally:
        # 记录执行时间
        elapsed_time = time.time() - start_time
        logger.info(f"{symbol}: 买入交易处理完成，耗时: {elapsed_time:.2f}秒")
        logger.info("=" * 80)
        
        # 删除专用的文件处理器
        logger.removeHandler(file_handler)
        file_handler.close()

async def execute_buy(symbol: str, chain: str, token_address: str, amount: float) -> Tuple[bool, Dict]:
    """
    执行代币买入的API函数，方便其他模块直接调用
    
    Args:
        symbol: 代币符号
        chain: 交易链，必须是'ethereum'或'polygon'
        token_address: 代币合约地址
        amount: USDT输入金额
        
    Returns:
        Tuple[bool, Dict]: 交易是否成功和执行记录详情
    """
    try:
        # 验证参数
        if chain.lower() not in ["ethereum", "polygon"]:
            logger.error(f"不支持的链: {chain}，必须是ethereum或polygon")
            return False, {"error": f"不支持的链: {chain}"}
        
        if not token_address or not Web3.is_address(token_address):
            logger.error(f"无效的代币地址: {token_address}")
            return False, {"error": f"无效的代币地址: {token_address}"}
        
        if amount <= 0:
            logger.error(f"无效的金额: {amount}")
            return False, {"error": f"无效的金额: {amount}"}
        
        logger.info(f"准备执行{symbol}买入交易 - 链:{chain}, 代币:{token_address}, 投入:{amount}USDT")
        
        # 构建opportunity对象，必须包含执行交易所需的所有字段
        opportunity = {
            "symbol": symbol,
            "lower_chain": chain.lower(),
            "eth_address": token_address if chain.lower() == "ethereum" else "",
            "polygon_address": token_address if chain.lower() == "polygon" else "",
            "usdt_input": amount,
            "hash": f"{symbol}_{chain}_{amount}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "execution_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 执行buy交易
        logger.info(f"开始执行{symbol}买入交易...")
        success, execution_record = await execute_buy_transaction(opportunity)
        
        if success:
            logger.info(f"{symbol}交易执行成功!")
            return True, execution_record
        else:
            logger.warning(f"{symbol}交易执行失败")
            return False, execution_record
    
    except Exception as e:
        logger.error(f"执行{symbol}买入交易时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False, {"error": str(e)}

def execute_trade_in_thread(opportunity: Dict) -> None:
    """
    在新线程中执行交易，不阻塞主线程
    
    Args:
        opportunity: 包含交易所需全部信息的字典，必须包含以下字段:
                    - symbol: 代币符号
                    - lower_chain: 交易链(ethereum或polygon)
                    - eth_address/polygon_address: 对应链上的代币地址(注意：有可能只有一个有值)
                    - usdt_input: USDT输入金额
                    - log_file: 可选参数，指定日志文件路径
    """
    async def _run_trade():
        try:
            # 提取必要信息
            symbol = opportunity.get('symbol', 'UNKNOWN')
            chain = opportunity.get('lower_chain', '')
            
            # 检查是否提供了日志文件路径，如果有则使用，否则生成默认路径
            log_file = opportunity.get('log_file')
            if not log_file:
                # 设置线程特定的日志文件
                log_file = get_trade_log_file(symbol)
            
            # 确保日志目录存在
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            # 创建线程特定的文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='a')
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
            thread_logger = logging.getLogger(f"Thread-{symbol}")
            thread_logger.setLevel(logging.INFO)
            thread_logger.addHandler(file_handler)
            
            # 记录开始执行的信息
            thread_logger.info("=" * 80)
            thread_logger.info(f"开始在线程中执行{symbol}交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 获取对应链上的代币地址
            token_address = ""
            if chain.lower() == "ethereum":
                token_address = opportunity.get('eth_address', '')
                thread_logger.info(f"链: {chain}, 代币地址: {token_address}")
            elif chain.lower() == "polygon":
                token_address = opportunity.get('polygon_address', '')
                thread_logger.info(f"链: {chain}, 代币地址: {token_address}")
            else:
                thread_logger.error(f"不支持的链: {chain}，必须是ethereum或polygon")
                return
                
            amount = opportunity.get('usdt_input', 0)
            
            if not all([symbol, chain, token_address, amount]):
                thread_logger.error(f"缺少必要参数: symbol={symbol}, chain={chain}, token_address={token_address}, amount={amount}")
                return
                
            thread_logger.info(f"在新线程中开始执行{symbol}交易 - 链: {chain}, 金额: {amount} USDT")
            
            # 执行交易 - 构建opportunity对象
            tx_opportunity = {
                "symbol": symbol,
                "lower_chain": chain.lower(),
                "eth_address": opportunity.get('eth_address', '') if chain.lower() == "ethereum" else "",
                "polygon_address": opportunity.get('polygon_address', '') if chain.lower() == "polygon" else "",
                "usdt_input": amount,
                "hash": opportunity.get('hash', f"{symbol}_{chain}_{amount}_{datetime.now().strftime('%Y%m%d%H%M%S')}"),
                "execution_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                # 这里使用与执行相同的日志文件，确保数据一致性
                "log_file": get_symbol_log_file(symbol)  # 使用执行日志文件，不是交易日志文件
            }
            
            # 直接调用execute_buy_transaction，并正确处理返回值
            thread_logger.info(f"调用execute_buy_transaction函数")
            
            # 正确处理异步函数的返回值
            try:
                success, execution_record = await execute_buy_transaction(tx_opportunity)
                
                if success:
                    # 使用从执行记录中获取的实际数量
                    actual_amount = execution_record.get('amount_bought', 0)
                    thread_logger.info(f"线程中的{symbol}交易执行成功! 获得: {actual_amount} {symbol}")
                else:
                    thread_logger.warning(f"线程中的{symbol}交易执行失败: {execution_record.get('error')}")
            except Exception as e:
                thread_logger.error(f"执行交易时出错: {str(e)}")
                thread_logger.error(traceback.format_exc())
                
            # 关闭日志文件
            thread_logger.info(f"{symbol}交易执行完成 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            thread_logger.info("=" * 80)
            thread_logger.removeHandler(file_handler)
            file_handler.close()
                
        except Exception as e:
            logger.error(f"线程执行交易时出错: {str(e)}")
            logger.error(traceback.format_exc())
    
    def _thread_wrapper():
        # 创建一个新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 执行异步交易函数
            loop.run_until_complete(_run_trade())
        except Exception as e:
            logger.error(f"执行交易线程出错: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # 关闭事件循环
            loop.close()
    
    # 创建并启动新线程
    thread = threading.Thread(
        target=_thread_wrapper, 
        name=f"Trade-{opportunity.get('symbol')}"
    )
    thread.start()
    logger.info(f"已在新线程 {thread.name} 中启动{opportunity.get('symbol')}交易")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="套利执行器 - 执行买入功能")
    parser.add_argument("--symbol", type=str, required=True, help="代币符号")
    parser.add_argument("--chain", type=str, required=True, help="交易链 (ethereum或polygon)")
    parser.add_argument("--amount", type=float, required=True, help="USDT输入金额")
    parser.add_argument("--token-address", type=str, required=True, help="代币合约地址")
    parser.add_argument("--log-file", type=str, help="指定日志文件路径")
    parser.add_argument("--thread", action="store_true", help="是否在新线程中执行交易")
    
    args = parser.parse_args()
    
    # 设置特定的日志文件（如果提供）
    if args.log_file:
        # 确保目录存在
        log_dir = os.path.dirname(args.log_file)
        if log_dir:  # 确保有目录部分
            os.makedirs(log_dir, exist_ok=True)
        
        # 添加文件处理器
        file_handler = logging.FileHandler(args.log_file, encoding='utf-8', mode='a')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        logger.info(f"日志将记录到文件: {args.log_file}")
    else:
        # 默认使用trade目录
        # 使用固定格式的日志文件名
        args.log_file = get_symbol_log_file(args.symbol)
        
        # 添加文件处理器
        file_handler = logging.FileHandler(args.log_file, encoding='utf-8', mode='a')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)
        
        logger.info(f"日志将记录到文件: {args.log_file}")
    
    # 记录脚本启动信息
    logger.info("=" * 80)
    logger.info(f"套利执行器启动 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"参数: 符号={args.symbol}, 链={args.chain}, 金额={args.amount}, 代币地址={args.token_address}")
    
    # 构建opportunity对象
    opportunity = {
        "symbol": args.symbol,
        "lower_chain": args.chain.lower(),
        "eth_address": args.token_address if args.chain.lower() == "ethereum" else "",
        "polygon_address": args.token_address if args.chain.lower() == "polygon" else "",
        "usdt_input": args.amount,
        "hash": f"{args.symbol}_{args.chain}_{args.amount}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "execution_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "log_file": args.log_file  # 保持日志文件一致
    }
    
    logger.info(f"准备执行{args.symbol}交易...")
    
    # 根据选项决定是在线程中执行还是直接执行
    if args.thread:
        # 执行交易在新线程中
        logger.info(f"将在新线程中执行{args.symbol}交易")
        trade_thread = execute_trade_in_thread(opportunity)
        
        try:
            # 等待交易线程完成
            while trade_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，等待交易完成...")
            trade_thread.join()
    else:
        # 直接在主线程中执行交易
        logger.info(f"在主线程中执行{args.symbol}交易")
        try:
            # 创建事件循环
            loop = asyncio.get_event_loop()
            
            # 执行交易
            success, result = loop.run_until_complete(execute_buy_transaction(opportunity))
            
            # 输出结果
            if success:
                logger.info(f"交易执行成功！购买了 {result.get('amount_bought')} {args.symbol}")
                logger.info(f"交易哈希: {result.get('tx_hash')}")
            else:
                logger.error(f"交易执行失败: {result.get('error')}")
                
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"执行交易时出错: {str(e)}")
            logger.error(traceback.format_exc())
        finally:
            # 关闭事件循环
            try:
                if loop and loop.is_running():
                    loop.close()
            except:
                pass 