import { amountToCosmosSdkDecAmount } from '../../../../utils/numbers.js';
import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx, InjectiveExchangeV1Beta1Exchange, } from '@injectivelabs/core-proto-ts';
const createSpotOrder = (args) => {
    const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    const paramsFromArgs = {
        ...args,
        price: args.price,
        triggerPrice: args.triggerPrice || '0',
        quantity: args.quantity,
    };
    orderInfo.subaccountId = args.subaccountId;
    orderInfo.feeRecipient = paramsFromArgs.feeRecipient;
    orderInfo.price = paramsFromArgs.price;
    orderInfo.quantity = paramsFromArgs.quantity;
    if (paramsFromArgs.cid) {
        orderInfo.cid = paramsFromArgs.cid;
    }
    const order = InjectiveExchangeV1Beta1Exchange.SpotOrder.create();
    order.marketId = paramsFromArgs.marketId;
    order.orderType = paramsFromArgs.orderType;
    order.orderInfo = orderInfo;
    if (paramsFromArgs.triggerPrice) {
        order.triggerPrice = paramsFromArgs.triggerPrice;
    }
    return order;
};
const createDerivativeOrder = (args) => {
    const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    const paramsFromArgs = {
        ...args,
        price: args.price,
        triggerPrice: args.triggerPrice || '0',
        quantity: args.quantity,
    };
    orderInfo.subaccountId = args.subaccountId;
    orderInfo.feeRecipient = paramsFromArgs.feeRecipient;
    orderInfo.price = paramsFromArgs.price;
    orderInfo.quantity = paramsFromArgs.quantity;
    if (paramsFromArgs.cid) {
        orderInfo.cid = paramsFromArgs.cid;
    }
    const order = InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
    order.marketId = paramsFromArgs.marketId;
    order.orderType = paramsFromArgs.orderType;
    order.orderInfo = orderInfo;
    order.margin = paramsFromArgs.margin;
    if (paramsFromArgs.triggerPrice) {
        order.triggerPrice = paramsFromArgs.triggerPrice;
    }
    return order;
};
const createBinaryOptionOrder = (args) => {
    const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
    const paramsFromArgs = {
        ...args,
        price: args.price,
        triggerPrice: args.triggerPrice || '0',
        quantity: args.quantity,
    };
    orderInfo.subaccountId = args.subaccountId;
    orderInfo.feeRecipient = paramsFromArgs.feeRecipient;
    orderInfo.price = paramsFromArgs.price;
    orderInfo.quantity = paramsFromArgs.quantity;
    if (paramsFromArgs.cid) {
        orderInfo.cid = paramsFromArgs.cid;
    }
    const order = InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
    order.marketId = paramsFromArgs.marketId;
    order.orderType = paramsFromArgs.orderType;
    order.orderInfo = orderInfo;
    order.margin = paramsFromArgs.margin;
    if (paramsFromArgs.triggerPrice) {
        order.triggerPrice = paramsFromArgs.triggerPrice;
    }
    return order;
};
const createMsgAndCancelOrders = (params) => {
    const message = InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders.create();
    message.sender = params.injectiveAddress;
    if (params.spotMarketIdsToCancelAll &&
        params.spotMarketIdsToCancelAll.length > 0) {
        message.spotMarketIdsToCancelAll = params.spotMarketIdsToCancelAll;
        message.subaccountId = params.subaccountId;
    }
    if (params.derivativeMarketIdsToCancelAll &&
        params.derivativeMarketIdsToCancelAll.length > 0) {
        message.derivativeMarketIdsToCancelAll =
            params.derivativeMarketIdsToCancelAll;
        message.subaccountId = params.subaccountId;
    }
    if (params.binaryOptionsMarketIdsToCancelAll &&
        params.binaryOptionsMarketIdsToCancelAll.length > 0) {
        message.binaryOptionsMarketIdsToCancelAll =
            params.binaryOptionsMarketIdsToCancelAll;
        message.subaccountId = params.subaccountId;
    }
    if (params.spotOrdersToCancel && params.spotOrdersToCancel.length > 0) {
        const orderData = params.spotOrdersToCancel.map(({ marketId, subaccountId, orderHash, cid }) => {
            const orderData = InjectiveExchangeV1Beta1Tx.OrderData.create();
            orderData.marketId = marketId;
            orderData.subaccountId = subaccountId;
            if (orderHash) {
                orderData.orderHash = orderHash;
            }
            if (cid) {
                orderData.cid = cid;
            }
            return orderData;
        });
        message.spotOrdersToCancel = orderData;
    }
    if (params.derivativeOrdersToCancel &&
        params.derivativeOrdersToCancel.length > 0) {
        const orderData = params.derivativeOrdersToCancel.map(({ marketId, subaccountId, orderHash, cid }) => {
            const orderData = InjectiveExchangeV1Beta1Tx.OrderData.create();
            orderData.marketId = marketId;
            orderData.subaccountId = subaccountId;
            if (orderHash) {
                orderData.orderHash = orderHash;
            }
            if (cid) {
                orderData.cid = cid;
            }
            return orderData;
        });
        message.derivativeOrdersToCancel = orderData;
    }
    if (params.binaryOptionsOrdersToCancel &&
        params.binaryOptionsOrdersToCancel.length > 0) {
        const orderData = params.binaryOptionsOrdersToCancel.map(({ marketId, subaccountId, orderHash, cid }) => {
            const orderData = InjectiveExchangeV1Beta1Tx.OrderData.create();
            orderData.marketId = marketId;
            orderData.subaccountId = subaccountId;
            if (orderHash) {
                orderData.orderHash = orderHash;
            }
            if (cid) {
                orderData.cid = cid;
            }
            return orderData;
        });
        message.derivativeOrdersToCancel = orderData;
    }
    return message;
};
/**
 * @category Messages
 */
export default class MsgBatchUpdateOrders extends MsgBase {
    static fromJSON(params) {
        return new MsgBatchUpdateOrders(params);
    }
    toProto() {
        const { params } = this;
        const message = createMsgAndCancelOrders(params);
        if (params.spotOrdersToCreate && params.spotOrdersToCreate.length > 0) {
            const orderData = params.spotOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: amountToCosmosSdkDecAmount(args.price).toFixed(),
                    triggerPrice: amountToCosmosSdkDecAmount(args.triggerPrice || 0).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(args.quantity).toFixed(),
                };
                return createSpotOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.spotOrdersToCreate = orderData;
        }
        if (params.derivativeOrdersToCreate &&
            params.derivativeOrdersToCreate.length > 0) {
            const orderData = params.derivativeOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: amountToCosmosSdkDecAmount(args.price).toFixed(),
                    margin: amountToCosmosSdkDecAmount(args.margin).toFixed(),
                    triggerPrice: amountToCosmosSdkDecAmount(args.triggerPrice || 0).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(args.quantity).toFixed(),
                };
                return createDerivativeOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.derivativeOrdersToCreate = orderData;
        }
        if (params.binaryOptionsOrdersToCancel &&
            params.binaryOptionsOrdersToCancel.length > 0) {
            const orderData = params.binaryOptionsOrdersToCancel.map(({ marketId, subaccountId, orderHash, cid }) => {
                const orderData = InjectiveExchangeV1Beta1Tx.OrderData.create();
                orderData.marketId = marketId;
                orderData.subaccountId = subaccountId;
                if (orderHash) {
                    orderData.orderHash = orderHash;
                }
                if (cid) {
                    orderData.cid = cid;
                }
                return orderData;
            });
            message.derivativeOrdersToCancel = orderData;
        }
        if (params.binaryOptionsMarketIdsToCancelAll &&
            params.binaryOptionsMarketIdsToCancelAll.length > 0) {
            message.subaccountId = params.subaccountId;
            message.binaryOptionsMarketIdsToCancelAll =
                params.binaryOptionsMarketIdsToCancelAll;
        }
        if (params.binaryOptionsOrdersToCreate &&
            params.binaryOptionsOrdersToCreate.length > 0) {
            const orderData = params.binaryOptionsOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: amountToCosmosSdkDecAmount(args.price).toFixed(),
                    margin: amountToCosmosSdkDecAmount(args.margin).toFixed(),
                    triggerPrice: amountToCosmosSdkDecAmount(args.triggerPrice || 0).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(args.quantity).toFixed(),
                };
                return createBinaryOptionOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.derivativeOrdersToCreate = orderData;
        }
        return InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgBatchUpdateOrders',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const message = createMsgAndCancelOrders(params);
        if (params.spotOrdersToCreate && params.spotOrdersToCreate.length > 0) {
            const orderData = params.spotOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: args.price,
                    triggerPrice: args.triggerPrice || '0',
                    quantity: args.quantity,
                };
                return createSpotOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.spotOrdersToCreate = orderData;
        }
        if (params.derivativeOrdersToCreate &&
            params.derivativeOrdersToCreate.length > 0) {
            const orderData = params.derivativeOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: args.price,
                    margin: args.margin,
                    triggerPrice: args.triggerPrice || '0',
                    quantity: args.quantity,
                };
                return createDerivativeOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.derivativeOrdersToCreate = orderData;
        }
        if (params.binaryOptionsOrdersToCreate &&
            params.binaryOptionsOrdersToCreate.length > 0) {
            const orderData = params.binaryOptionsOrdersToCreate.map((args) => {
                const paramsFromArgs = {
                    ...args,
                    price: args.price,
                    margin: args.margin,
                    triggerPrice: args.triggerPrice || '0',
                    quantity: args.quantity,
                };
                return createBinaryOptionOrder({
                    ...args,
                    ...paramsFromArgs,
                    subaccountId: params.subaccountId,
                });
            });
            message.derivativeOrdersToCreate = orderData;
        }
        const msg = {
            ...snakecaseKeys(InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders.fromPartial(message)),
        };
        return {
            type: 'exchange/MsgBatchUpdateOrders',
            value: msg,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgBatchUpdateOrders',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgBatchUpdateOrders',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders.encode(this.toProto()).finish();
    }
}
