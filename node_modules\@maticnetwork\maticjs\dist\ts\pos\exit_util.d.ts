import { <PERSON><PERSON>hai<PERSON> } from "./root_chain";
import { Web3SideChainClient } from "../utils";
import { IBaseClientConfig } from "..";
interface IChainBlockInfo {
    lastChildBlock: string;
    txBlockNumber: number;
}
export declare class ExitUtil {
    private maticClient_;
    rootChain: RootChain;
    requestConcurrency: number;
    config: IBaseClientConfig;
    constructor(client: Web3SideChainClient<IBaseClientConfig>, rootChain: RootChain);
    private getLogIndex_;
    private getAllLogIndices_;
    getChainBlockInfo(burnTxHash: string): Promise<IChainBlockInfo>;
    private isCheckPointed_;
    isCheckPointed(burnTxHash: string): Promise<boolean>;
    /**
     * returns info about block number existence on parent chain
     * 1. root block number,
     * 2. start block number,
     * 3. end block number
     *
     * @private
     * @param {number} txBlockNumber - transaction block number on child chain
     * @return {*}
     * @memberof ExitUtil
     */
    private getRootBlockInfo;
    private getRootBlockInfoFromAPI;
    private getBlockProof;
    private getBlockProofFromAPI;
    private getExitProofFromAPI;
    buildPayloadForExit(burnTxHash: string, logEventSig: string, isFast: boolean, index?: number): any;
    buildMultiplePayloadsForExit(burnTxHash: string, logEventSig: string, isFast: boolean): Promise<string[]>;
    private encodePayload_;
    getExitHash(burnTxHash: any, index: any, logEventSig: any): Promise<string>;
}
export {};
