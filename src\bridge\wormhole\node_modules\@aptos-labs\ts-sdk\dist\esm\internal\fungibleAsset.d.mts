import { AptosConfig } from '../api/aptosConfig.mjs';
import { GetFungibleAssetMetadataResponse, GetFungibleAssetActivitiesResponse, GetCurrentFungibleAssetBalancesResponse } from '../types/indexer.mjs';
import { PaginationArgs, WhereArg, AnyNumber } from '../types/types.mjs';
import { FungibleAssetMetadataBoolExp, FungibleAssetActivitiesBoolExp, CurrentFungibleAssetBalancesBoolExp } from '../types/generated/types.mjs';
import { AccountAddressInput } from '../core/accountAddress.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { InputGenerateTransactionOptions } from '../transactions/types.mjs';
import { SimpleTransaction } from '../transactions/instances/simpleTransaction.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/generated/operations.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

/**
 * This file contains the underlying implementations for exposed API surface in
 * the {@link api/fungible_asset}. By moving the methods out into a separate file,
 * other namespaces and processes can access these methods without depending on the entire
 * fungible_asset namespace and without having a dependency cycle error.
 * @group Implementation
 */

/**
 * Retrieves metadata for fungible assets based on specified criteria.
 * This function allows you to filter and paginate through fungible asset metadata.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration for Aptos.
 * @param [args.options] - Optional parameters for pagination and filtering.
 * @param [args.options.limit] - The maximum number of results to return.
 * @param [args.options.offset] - The number of results to skip before starting to collect the result set.
 * @param [args.options.where] - Conditions to filter the results.
 * @group Implementation
 */
declare function getFungibleAssetMetadata(args: {
    aptosConfig: AptosConfig;
    options?: PaginationArgs & WhereArg<FungibleAssetMetadataBoolExp>;
}): Promise<GetFungibleAssetMetadataResponse>;
/**
 * Retrieves the activities associated with fungible assets.
 * This function allows you to filter and paginate through the activities based on specified conditions.
 *
 * @param args - The arguments for retrieving fungible asset activities.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param [args.options] - Optional parameters for pagination and filtering.
 * @param [args.options.limit] - The maximum number of activities to retrieve.
 * @param [args.options.offset] - The number of activities to skip before starting to collect the result set.
 * @param [args.options.where] - Conditions to filter the activities.
 * @returns A promise that resolves to an array of fungible asset activities.
 * @group Implementation
 */
declare function getFungibleAssetActivities(args: {
    aptosConfig: AptosConfig;
    options?: PaginationArgs & WhereArg<FungibleAssetActivitiesBoolExp>;
}): Promise<GetFungibleAssetActivitiesResponse>;
/**
 * Retrieves the current balances of fungible assets for a specified configuration.
 *
 * @param args - The arguments for retrieving fungible asset balances.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.options - Optional parameters for pagination and filtering.
 * @param args.options.limit - The maximum number of results to return.
 * @param args.options.offset - The number of results to skip before starting to collect the results.
 * @param args.options.where - Conditions to filter the results based on specific criteria.
 * @returns The current balances of fungible assets.
 * @group Implementation
 */
declare function getCurrentFungibleAssetBalances(args: {
    aptosConfig: AptosConfig;
    options?: PaginationArgs & WhereArg<CurrentFungibleAssetBalancesBoolExp>;
}): Promise<GetCurrentFungibleAssetBalancesResponse>;
/**
 * Transfers a specified amount of a fungible asset from the sender to the recipient.
 * This function helps facilitate the transfer of digital assets between accounts on the Aptos blockchain.
 *
 * @param args - The parameters for the transfer operation.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.sender - The account initiating the transfer.
 * @param args.fungibleAssetMetadataAddress - The address of the fungible asset's metadata.
 * @param args.recipient - The address of the account receiving the asset.
 * @param args.amount - The amount of the fungible asset to transfer.
 * @param args.options - Optional settings for generating the transaction.
 * @group Implementation
 */
declare function transferFungibleAsset(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    fungibleAssetMetadataAddress: AccountAddressInput;
    recipient: AccountAddressInput;
    amount: AnyNumber;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;
/**
 * Transfers a specified amount of a fungible asset from any (primary or secondary) fungible store to any (primary or secondary) fungible store.
 * This function helps facilitate the transfer of digital assets between fungible stores on the Aptos blockchain.
 *
 * @param args - The parameters for the transfer operation.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.sender - The account initiating the transfer.
 * @param args.fromStore - The address of the fungible store initiating the transfer.
 * @param args.toStore - The address of the fungible store receiving the asset.
 * @param args.amount - The amount of the fungible asset to transfer. Must be a positive number.
 * @param args.options - Optional settings for generating the transaction.
 * @returns A SimpleTransaction that can be submitted to the blockchain.
 * @throws Error if the transaction generation fails or if the input parameters are invalid.
 * @group Implementation
 */
declare function transferFungibleAssetBetweenStores(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    fromStore: AccountAddressInput;
    toStore: AccountAddressInput;
    amount: AnyNumber;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;

export { getCurrentFungibleAssetBalances, getFungibleAssetActivities, getFungibleAssetMetadata, transferFungibleAsset, transferFungibleAssetBetweenStores };
