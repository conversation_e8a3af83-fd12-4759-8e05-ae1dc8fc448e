/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { ContractGrant } from "../../../cosmwasm/wasm/v1/authz.js";
export const protobufPackage = "injective.wasmx.v1";
function createBaseContractExecutionCompatAuthorization() {
    return { grants: [] };
}
export const ContractExecutionCompatAuthorization = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.grants) {
            ContractGrant.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseContractExecutionCompatAuthorization();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grants.push(ContractGrant.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { grants: Array.isArray(object?.grants) ? object.grants.map((e) => ContractGrant.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.grants) {
            obj.grants = message.grants.map((e) => e ? ContractGrant.toJSON(e) : undefined);
        }
        else {
            obj.grants = [];
        }
        return obj;
    },
    create(base) {
        return ContractExecutionCompatAuthorization.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseContractExecutionCompatAuthorization();
        message.grants = object.grants?.map((e) => ContractGrant.fromPartial(e)) || [];
        return message;
    },
};
