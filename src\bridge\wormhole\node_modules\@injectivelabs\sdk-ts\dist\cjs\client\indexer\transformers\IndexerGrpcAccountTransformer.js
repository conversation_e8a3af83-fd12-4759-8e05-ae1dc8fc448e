"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcAccountTransformer = void 0;
const pagination_js_1 = require("../../..//utils/pagination.js");
/**
 * @category Indexer Grpc Transformer
 *
 */
class IndexerGrpcAccountTransformer {
    /**
     *
     * @deprecated - use IndexerGrpcAccountPortfolioApi.accountPortfolioResponseToAccountPortfolio
     */
    static accountPortfolioResponseToAccountPortfolio(response) {
        const portfolio = response.portfolio;
        const subaccounts = portfolio.subaccounts || [];
        return {
            portfolioValue: portfolio.portfolioValue,
            availableBalance: portfolio.availableBalance,
            lockedBalance: portfolio.lockedBalance,
            unrealizedPnl: portfolio.unrealizedPnl,
            subaccountsList: subaccounts.map(IndexerGrpcAccountTransformer.grpcSubaccountPortfolioToSubaccountPortfolio),
        };
    }
    static grpcSubaccountPortfolioToSubaccountPortfolio(subaccountPortfolio) {
        return {
            subaccountId: subaccountPortfolio.subaccountId,
            availableBalance: subaccountPortfolio.availableBalance,
            lockedBalance: subaccountPortfolio.lockedBalance,
            unrealizedPnl: subaccountPortfolio.unrealizedPnl,
        };
    }
    static grpcAccountPortfolioToAccountPortfolio(portfolio) {
        return {
            portfolioValue: portfolio.portfolioValue,
            availableBalance: portfolio.availableBalance,
            lockedBalance: portfolio.lockedBalance,
            unrealizedPnl: portfolio.unrealizedPnl,
            subaccountsList: portfolio.subaccounts.map(IndexerGrpcAccountTransformer.grpcSubaccountPortfolioToSubaccountPortfolio),
        };
    }
    static grpcAmountToAmount(amount) {
        return {
            amount: amount.amount,
            denom: amount.denom,
        };
    }
    static grpcDepositToDeposit(deposit) {
        return {
            totalBalance: deposit.totalBalance,
            availableBalance: deposit.availableBalance,
        };
    }
    static balancesResponseToBalances(response) {
        return response.balances.map((b) => IndexerGrpcAccountTransformer.grpcBalanceToBalance(b));
    }
    static balanceResponseToBalance(response) {
        return IndexerGrpcAccountTransformer.grpcBalanceToBalance(response.balance);
    }
    static grpcBalanceToBalance(balance) {
        const deposit = balance.deposit;
        return {
            subaccountId: balance.subaccountId,
            accountAddress: balance.accountAddress,
            denom: balance.denom,
            deposit: deposit
                ? IndexerGrpcAccountTransformer.grpcDepositToDeposit(deposit)
                : undefined,
        };
    }
    static grpcBalancesToBalances(balances) {
        return balances.map((balance) => IndexerGrpcAccountTransformer.grpcBalanceToBalance(balance));
    }
    static grpcTransferHistoryEntryToTransferHistoryEntry(transfer) {
        const amount = transfer.amount;
        return {
            transferType: transfer.transferType,
            srcSubaccountId: transfer.srcSubaccountId,
            srcSubaccountAddress: transfer.srcAccountAddress,
            dstSubaccountId: transfer.dstSubaccountId,
            dstSubaccountAddress: transfer.dstAccountAddress,
            executedAt: parseInt(transfer.executedAt, 10),
            amount: amount
                ? IndexerGrpcAccountTransformer.grpcAmountToAmount(amount)
                : undefined,
        };
    }
    static tradingRewardsResponseToTradingRewards(response) {
        const rewards = response.rewards;
        return rewards.map(IndexerGrpcAccountTransformer.grpcTradingRewardToTradingReward);
    }
    static grpcTradingRewardsToTradingRewards(rewards) {
        return rewards.map(IndexerGrpcAccountTransformer.grpcTradingRewardToTradingReward);
    }
    static grpcTradingRewardToTradingReward(reward) {
        return {
            accountAddress: reward.accountAddress,
            rewards: reward.rewards.map((r) => ({
                amount: r.amount,
                denom: r.denom,
            })),
            distributedAt: parseInt(reward.distributedAt, 10),
        };
    }
    static transferHistoryResponseToTransferHistory(response) {
        const transfers = response.transfers;
        const pagination = response.paging;
        return {
            transfers: transfers.map((transfer) => IndexerGrpcAccountTransformer.grpcTransferHistoryEntryToTransferHistoryEntry(transfer)),
            pagination: (0, pagination_js_1.grpcPagingToPaging)(pagination),
        };
    }
    static grpcTransferHistoryToTransferHistory(transfers) {
        return transfers.map((transfer) => IndexerGrpcAccountTransformer.grpcTransferHistoryEntryToTransferHistoryEntry(transfer));
    }
}
exports.IndexerGrpcAccountTransformer = IndexerGrpcAccountTransformer;
