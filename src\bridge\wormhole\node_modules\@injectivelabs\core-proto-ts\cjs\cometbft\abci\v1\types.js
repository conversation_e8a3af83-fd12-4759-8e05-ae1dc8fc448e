"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validator = exports.TxResult = exports.ExecTxResult = exports.EventAttribute = exports.Event = exports.ExtendedCommitInfo = exports.CommitInfo = exports.FinalizeBlockResponse = exports.VerifyVoteExtensionResponse = exports.ExtendVoteResponse = exports.ProcessProposalResponse = exports.PrepareProposalResponse = exports.ApplySnapshotChunkResponse = exports.LoadSnapshotChunkResponse = exports.OfferSnapshotResponse = exports.ListSnapshotsResponse = exports.CommitResponse = exports.CheckTxResponse = exports.QueryResponse = exports.InitChainResponse = exports.InfoResponse_LanePrioritiesEntry = exports.InfoResponse = exports.FlushResponse = exports.EchoResponse = exports.ExceptionResponse = exports.Response = exports.FinalizeBlockRequest = exports.VerifyVoteExtensionRequest = exports.ExtendVoteRequest = exports.ProcessProposalRequest = exports.PrepareProposalRequest = exports.ApplySnapshotChunkRequest = exports.LoadSnapshotChunkRequest = exports.OfferSnapshotRequest = exports.ListSnapshotsRequest = exports.CommitRequest = exports.CheckTxRequest = exports.QueryRequest = exports.InitChainRequest = exports.InfoRequest = exports.FlushRequest = exports.EchoRequest = exports.Request = exports.MisbehaviorType = exports.VerifyVoteExtensionStatus = exports.ProcessProposalStatus = exports.ApplySnapshotChunkResult = exports.OfferSnapshotResult = exports.CheckTxType = exports.protobufPackage = void 0;
exports.Snapshot = exports.Misbehavior = exports.ExtendedVoteInfo = exports.VoteInfo = exports.ValidatorUpdate = void 0;
exports.checkTxTypeFromJSON = checkTxTypeFromJSON;
exports.checkTxTypeToJSON = checkTxTypeToJSON;
exports.offerSnapshotResultFromJSON = offerSnapshotResultFromJSON;
exports.offerSnapshotResultToJSON = offerSnapshotResultToJSON;
exports.applySnapshotChunkResultFromJSON = applySnapshotChunkResultFromJSON;
exports.applySnapshotChunkResultToJSON = applySnapshotChunkResultToJSON;
exports.processProposalStatusFromJSON = processProposalStatusFromJSON;
exports.processProposalStatusToJSON = processProposalStatusToJSON;
exports.verifyVoteExtensionStatusFromJSON = verifyVoteExtensionStatusFromJSON;
exports.verifyVoteExtensionStatusToJSON = verifyVoteExtensionStatusToJSON;
exports.misbehaviorTypeFromJSON = misbehaviorTypeFromJSON;
exports.misbehaviorTypeToJSON = misbehaviorTypeToJSON;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var duration_1 = require("../../../google/protobuf/duration.js");
var timestamp_1 = require("../../../google/protobuf/timestamp.js");
var proof_1 = require("../../crypto/v1/proof.js");
var params_1 = require("../../types/v1/params.js");
var validator_1 = require("../../types/v1/validator.js");
exports.protobufPackage = "cometbft.abci.v1";
/**
 * Type of the transaction check request.
 *
 * This enumeration is incompatible with the CheckTxType definition in
 * cometbft.abci.v1beta1 and therefore shall not be used in encoding with the same
 * field number.
 */
var CheckTxType;
(function (CheckTxType) {
    /** CHECK_TX_TYPE_UNKNOWN - Unknown */
    CheckTxType[CheckTxType["CHECK_TX_TYPE_UNKNOWN"] = 0] = "CHECK_TX_TYPE_UNKNOWN";
    /** CHECK_TX_TYPE_RECHECK - Recheck (2nd, 3rd, etc.) */
    CheckTxType[CheckTxType["CHECK_TX_TYPE_RECHECK"] = 1] = "CHECK_TX_TYPE_RECHECK";
    /** CHECK_TX_TYPE_CHECK - Check (1st time) */
    CheckTxType[CheckTxType["CHECK_TX_TYPE_CHECK"] = 2] = "CHECK_TX_TYPE_CHECK";
    CheckTxType[CheckTxType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(CheckTxType || (exports.CheckTxType = CheckTxType = {}));
function checkTxTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "CHECK_TX_TYPE_UNKNOWN":
            return CheckTxType.CHECK_TX_TYPE_UNKNOWN;
        case 1:
        case "CHECK_TX_TYPE_RECHECK":
            return CheckTxType.CHECK_TX_TYPE_RECHECK;
        case 2:
        case "CHECK_TX_TYPE_CHECK":
            return CheckTxType.CHECK_TX_TYPE_CHECK;
        case -1:
        case "UNRECOGNIZED":
        default:
            return CheckTxType.UNRECOGNIZED;
    }
}
function checkTxTypeToJSON(object) {
    switch (object) {
        case CheckTxType.CHECK_TX_TYPE_UNKNOWN:
            return "CHECK_TX_TYPE_UNKNOWN";
        case CheckTxType.CHECK_TX_TYPE_RECHECK:
            return "CHECK_TX_TYPE_RECHECK";
        case CheckTxType.CHECK_TX_TYPE_CHECK:
            return "CHECK_TX_TYPE_CHECK";
        case CheckTxType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** The result of offering a snapshot. */
var OfferSnapshotResult;
(function (OfferSnapshotResult) {
    /** OFFER_SNAPSHOT_RESULT_UNKNOWN - Unknown result, abort all snapshot restoration */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_UNKNOWN"] = 0] = "OFFER_SNAPSHOT_RESULT_UNKNOWN";
    /** OFFER_SNAPSHOT_RESULT_ACCEPT - Snapshot accepted, apply chunks */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_ACCEPT"] = 1] = "OFFER_SNAPSHOT_RESULT_ACCEPT";
    /** OFFER_SNAPSHOT_RESULT_ABORT - Abort all snapshot restoration */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_ABORT"] = 2] = "OFFER_SNAPSHOT_RESULT_ABORT";
    /** OFFER_SNAPSHOT_RESULT_REJECT - Reject this specific snapshot, try others */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_REJECT"] = 3] = "OFFER_SNAPSHOT_RESULT_REJECT";
    /** OFFER_SNAPSHOT_RESULT_REJECT_FORMAT - Reject all snapshots of this format, try others */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_REJECT_FORMAT"] = 4] = "OFFER_SNAPSHOT_RESULT_REJECT_FORMAT";
    /** OFFER_SNAPSHOT_RESULT_REJECT_SENDER - Reject all snapshots from the sender(s), try others */
    OfferSnapshotResult[OfferSnapshotResult["OFFER_SNAPSHOT_RESULT_REJECT_SENDER"] = 5] = "OFFER_SNAPSHOT_RESULT_REJECT_SENDER";
    OfferSnapshotResult[OfferSnapshotResult["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OfferSnapshotResult || (exports.OfferSnapshotResult = OfferSnapshotResult = {}));
function offerSnapshotResultFromJSON(object) {
    switch (object) {
        case 0:
        case "OFFER_SNAPSHOT_RESULT_UNKNOWN":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_UNKNOWN;
        case 1:
        case "OFFER_SNAPSHOT_RESULT_ACCEPT":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_ACCEPT;
        case 2:
        case "OFFER_SNAPSHOT_RESULT_ABORT":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_ABORT;
        case 3:
        case "OFFER_SNAPSHOT_RESULT_REJECT":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT;
        case 4:
        case "OFFER_SNAPSHOT_RESULT_REJECT_FORMAT":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT_FORMAT;
        case 5:
        case "OFFER_SNAPSHOT_RESULT_REJECT_SENDER":
            return OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT_SENDER;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OfferSnapshotResult.UNRECOGNIZED;
    }
}
function offerSnapshotResultToJSON(object) {
    switch (object) {
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_UNKNOWN:
            return "OFFER_SNAPSHOT_RESULT_UNKNOWN";
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_ACCEPT:
            return "OFFER_SNAPSHOT_RESULT_ACCEPT";
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_ABORT:
            return "OFFER_SNAPSHOT_RESULT_ABORT";
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT:
            return "OFFER_SNAPSHOT_RESULT_REJECT";
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT_FORMAT:
            return "OFFER_SNAPSHOT_RESULT_REJECT_FORMAT";
        case OfferSnapshotResult.OFFER_SNAPSHOT_RESULT_REJECT_SENDER:
            return "OFFER_SNAPSHOT_RESULT_REJECT_SENDER";
        case OfferSnapshotResult.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** The result of applying a snapshot chunk. */
var ApplySnapshotChunkResult;
(function (ApplySnapshotChunkResult) {
    /** APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN - Unknown result, abort all snapshot restoration */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN"] = 0] = "APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN";
    /** APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT - Chunk successfully accepted */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT"] = 1] = "APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT";
    /** APPLY_SNAPSHOT_CHUNK_RESULT_ABORT - Abort all snapshot restoration */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_ABORT"] = 2] = "APPLY_SNAPSHOT_CHUNK_RESULT_ABORT";
    /** APPLY_SNAPSHOT_CHUNK_RESULT_RETRY - Retry chunk (combine with refetch and reject) */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_RETRY"] = 3] = "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY";
    /** APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT - Retry snapshot (combine with refetch and reject) */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT"] = 4] = "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT";
    /** APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT - Reject this snapshot, try others */
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT"] = 5] = "APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT";
    ApplySnapshotChunkResult[ApplySnapshotChunkResult["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ApplySnapshotChunkResult || (exports.ApplySnapshotChunkResult = ApplySnapshotChunkResult = {}));
function applySnapshotChunkResultFromJSON(object) {
    switch (object) {
        case 0:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN;
        case 1:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT;
        case 2:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_ABORT":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_ABORT;
        case 3:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_RETRY;
        case 4:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT;
        case 5:
        case "APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT":
            return ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ApplySnapshotChunkResult.UNRECOGNIZED;
    }
}
function applySnapshotChunkResultToJSON(object) {
    switch (object) {
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN";
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT";
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_ABORT:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_ABORT";
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_RETRY:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY";
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT";
        case ApplySnapshotChunkResult.APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT:
            return "APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT";
        case ApplySnapshotChunkResult.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** ProcessProposalStatus is the status of the proposal processing. */
var ProcessProposalStatus;
(function (ProcessProposalStatus) {
    /** PROCESS_PROPOSAL_STATUS_UNKNOWN - Unknown */
    ProcessProposalStatus[ProcessProposalStatus["PROCESS_PROPOSAL_STATUS_UNKNOWN"] = 0] = "PROCESS_PROPOSAL_STATUS_UNKNOWN";
    /** PROCESS_PROPOSAL_STATUS_ACCEPT - Accepted */
    ProcessProposalStatus[ProcessProposalStatus["PROCESS_PROPOSAL_STATUS_ACCEPT"] = 1] = "PROCESS_PROPOSAL_STATUS_ACCEPT";
    /** PROCESS_PROPOSAL_STATUS_REJECT - Rejected */
    ProcessProposalStatus[ProcessProposalStatus["PROCESS_PROPOSAL_STATUS_REJECT"] = 2] = "PROCESS_PROPOSAL_STATUS_REJECT";
    ProcessProposalStatus[ProcessProposalStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ProcessProposalStatus || (exports.ProcessProposalStatus = ProcessProposalStatus = {}));
function processProposalStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "PROCESS_PROPOSAL_STATUS_UNKNOWN":
            return ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_UNKNOWN;
        case 1:
        case "PROCESS_PROPOSAL_STATUS_ACCEPT":
            return ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_ACCEPT;
        case 2:
        case "PROCESS_PROPOSAL_STATUS_REJECT":
            return ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_REJECT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ProcessProposalStatus.UNRECOGNIZED;
    }
}
function processProposalStatusToJSON(object) {
    switch (object) {
        case ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_UNKNOWN:
            return "PROCESS_PROPOSAL_STATUS_UNKNOWN";
        case ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_ACCEPT:
            return "PROCESS_PROPOSAL_STATUS_ACCEPT";
        case ProcessProposalStatus.PROCESS_PROPOSAL_STATUS_REJECT:
            return "PROCESS_PROPOSAL_STATUS_REJECT";
        case ProcessProposalStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** VerifyVoteExtensionStatus is the status of the vote extension verification. */
var VerifyVoteExtensionStatus;
(function (VerifyVoteExtensionStatus) {
    /** VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN - Unknown */
    VerifyVoteExtensionStatus[VerifyVoteExtensionStatus["VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN"] = 0] = "VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN";
    /** VERIFY_VOTE_EXTENSION_STATUS_ACCEPT - Accepted */
    VerifyVoteExtensionStatus[VerifyVoteExtensionStatus["VERIFY_VOTE_EXTENSION_STATUS_ACCEPT"] = 1] = "VERIFY_VOTE_EXTENSION_STATUS_ACCEPT";
    /**
     * VERIFY_VOTE_EXTENSION_STATUS_REJECT - Rejecting the vote extension will reject the entire precommit by the sender.
     * Incorrectly implementing this thus has liveness implications as it may affect
     * CometBFT's ability to receive 2/3+ valid votes to finalize the block.
     * Honest nodes should never be rejected.
     */
    VerifyVoteExtensionStatus[VerifyVoteExtensionStatus["VERIFY_VOTE_EXTENSION_STATUS_REJECT"] = 2] = "VERIFY_VOTE_EXTENSION_STATUS_REJECT";
    VerifyVoteExtensionStatus[VerifyVoteExtensionStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(VerifyVoteExtensionStatus || (exports.VerifyVoteExtensionStatus = VerifyVoteExtensionStatus = {}));
function verifyVoteExtensionStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN":
            return VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN;
        case 1:
        case "VERIFY_VOTE_EXTENSION_STATUS_ACCEPT":
            return VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_ACCEPT;
        case 2:
        case "VERIFY_VOTE_EXTENSION_STATUS_REJECT":
            return VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_REJECT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return VerifyVoteExtensionStatus.UNRECOGNIZED;
    }
}
function verifyVoteExtensionStatusToJSON(object) {
    switch (object) {
        case VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN:
            return "VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN";
        case VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_ACCEPT:
            return "VERIFY_VOTE_EXTENSION_STATUS_ACCEPT";
        case VerifyVoteExtensionStatus.VERIFY_VOTE_EXTENSION_STATUS_REJECT:
            return "VERIFY_VOTE_EXTENSION_STATUS_REJECT";
        case VerifyVoteExtensionStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** The type of misbehavior committed by a validator. */
var MisbehaviorType;
(function (MisbehaviorType) {
    /** MISBEHAVIOR_TYPE_UNKNOWN - Unknown */
    MisbehaviorType[MisbehaviorType["MISBEHAVIOR_TYPE_UNKNOWN"] = 0] = "MISBEHAVIOR_TYPE_UNKNOWN";
    /** MISBEHAVIOR_TYPE_DUPLICATE_VOTE - Duplicate vote */
    MisbehaviorType[MisbehaviorType["MISBEHAVIOR_TYPE_DUPLICATE_VOTE"] = 1] = "MISBEHAVIOR_TYPE_DUPLICATE_VOTE";
    /** MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK - Light client attack */
    MisbehaviorType[MisbehaviorType["MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK"] = 2] = "MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK";
    MisbehaviorType[MisbehaviorType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(MisbehaviorType || (exports.MisbehaviorType = MisbehaviorType = {}));
function misbehaviorTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "MISBEHAVIOR_TYPE_UNKNOWN":
            return MisbehaviorType.MISBEHAVIOR_TYPE_UNKNOWN;
        case 1:
        case "MISBEHAVIOR_TYPE_DUPLICATE_VOTE":
            return MisbehaviorType.MISBEHAVIOR_TYPE_DUPLICATE_VOTE;
        case 2:
        case "MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK":
            return MisbehaviorType.MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK;
        case -1:
        case "UNRECOGNIZED":
        default:
            return MisbehaviorType.UNRECOGNIZED;
    }
}
function misbehaviorTypeToJSON(object) {
    switch (object) {
        case MisbehaviorType.MISBEHAVIOR_TYPE_UNKNOWN:
            return "MISBEHAVIOR_TYPE_UNKNOWN";
        case MisbehaviorType.MISBEHAVIOR_TYPE_DUPLICATE_VOTE:
            return "MISBEHAVIOR_TYPE_DUPLICATE_VOTE";
        case MisbehaviorType.MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK:
            return "MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK";
        case MisbehaviorType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseRequest() {
    return {
        echo: undefined,
        flush: undefined,
        info: undefined,
        initChain: undefined,
        query: undefined,
        checkTx: undefined,
        commit: undefined,
        listSnapshots: undefined,
        offerSnapshot: undefined,
        loadSnapshotChunk: undefined,
        applySnapshotChunk: undefined,
        prepareProposal: undefined,
        processProposal: undefined,
        extendVote: undefined,
        verifyVoteExtension: undefined,
        finalizeBlock: undefined,
    };
}
exports.Request = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.echo !== undefined) {
            exports.EchoRequest.encode(message.echo, writer.uint32(10).fork()).ldelim();
        }
        if (message.flush !== undefined) {
            exports.FlushRequest.encode(message.flush, writer.uint32(18).fork()).ldelim();
        }
        if (message.info !== undefined) {
            exports.InfoRequest.encode(message.info, writer.uint32(26).fork()).ldelim();
        }
        if (message.initChain !== undefined) {
            exports.InitChainRequest.encode(message.initChain, writer.uint32(42).fork()).ldelim();
        }
        if (message.query !== undefined) {
            exports.QueryRequest.encode(message.query, writer.uint32(50).fork()).ldelim();
        }
        if (message.checkTx !== undefined) {
            exports.CheckTxRequest.encode(message.checkTx, writer.uint32(66).fork()).ldelim();
        }
        if (message.commit !== undefined) {
            exports.CommitRequest.encode(message.commit, writer.uint32(90).fork()).ldelim();
        }
        if (message.listSnapshots !== undefined) {
            exports.ListSnapshotsRequest.encode(message.listSnapshots, writer.uint32(98).fork()).ldelim();
        }
        if (message.offerSnapshot !== undefined) {
            exports.OfferSnapshotRequest.encode(message.offerSnapshot, writer.uint32(106).fork()).ldelim();
        }
        if (message.loadSnapshotChunk !== undefined) {
            exports.LoadSnapshotChunkRequest.encode(message.loadSnapshotChunk, writer.uint32(114).fork()).ldelim();
        }
        if (message.applySnapshotChunk !== undefined) {
            exports.ApplySnapshotChunkRequest.encode(message.applySnapshotChunk, writer.uint32(122).fork()).ldelim();
        }
        if (message.prepareProposal !== undefined) {
            exports.PrepareProposalRequest.encode(message.prepareProposal, writer.uint32(130).fork()).ldelim();
        }
        if (message.processProposal !== undefined) {
            exports.ProcessProposalRequest.encode(message.processProposal, writer.uint32(138).fork()).ldelim();
        }
        if (message.extendVote !== undefined) {
            exports.ExtendVoteRequest.encode(message.extendVote, writer.uint32(146).fork()).ldelim();
        }
        if (message.verifyVoteExtension !== undefined) {
            exports.VerifyVoteExtensionRequest.encode(message.verifyVoteExtension, writer.uint32(154).fork()).ldelim();
        }
        if (message.finalizeBlock !== undefined) {
            exports.FinalizeBlockRequest.encode(message.finalizeBlock, writer.uint32(162).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.echo = exports.EchoRequest.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.flush = exports.FlushRequest.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.info = exports.InfoRequest.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.initChain = exports.InitChainRequest.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.query = exports.QueryRequest.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.checkTx = exports.CheckTxRequest.decode(reader, reader.uint32());
                    break;
                case 11:
                    message.commit = exports.CommitRequest.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.listSnapshots = exports.ListSnapshotsRequest.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.offerSnapshot = exports.OfferSnapshotRequest.decode(reader, reader.uint32());
                    break;
                case 14:
                    message.loadSnapshotChunk = exports.LoadSnapshotChunkRequest.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.applySnapshotChunk = exports.ApplySnapshotChunkRequest.decode(reader, reader.uint32());
                    break;
                case 16:
                    message.prepareProposal = exports.PrepareProposalRequest.decode(reader, reader.uint32());
                    break;
                case 17:
                    message.processProposal = exports.ProcessProposalRequest.decode(reader, reader.uint32());
                    break;
                case 18:
                    message.extendVote = exports.ExtendVoteRequest.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.verifyVoteExtension = exports.VerifyVoteExtensionRequest.decode(reader, reader.uint32());
                    break;
                case 20:
                    message.finalizeBlock = exports.FinalizeBlockRequest.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            echo: isSet(object.echo) ? exports.EchoRequest.fromJSON(object.echo) : undefined,
            flush: isSet(object.flush) ? exports.FlushRequest.fromJSON(object.flush) : undefined,
            info: isSet(object.info) ? exports.InfoRequest.fromJSON(object.info) : undefined,
            initChain: isSet(object.initChain) ? exports.InitChainRequest.fromJSON(object.initChain) : undefined,
            query: isSet(object.query) ? exports.QueryRequest.fromJSON(object.query) : undefined,
            checkTx: isSet(object.checkTx) ? exports.CheckTxRequest.fromJSON(object.checkTx) : undefined,
            commit: isSet(object.commit) ? exports.CommitRequest.fromJSON(object.commit) : undefined,
            listSnapshots: isSet(object.listSnapshots) ? exports.ListSnapshotsRequest.fromJSON(object.listSnapshots) : undefined,
            offerSnapshot: isSet(object.offerSnapshot) ? exports.OfferSnapshotRequest.fromJSON(object.offerSnapshot) : undefined,
            loadSnapshotChunk: isSet(object.loadSnapshotChunk)
                ? exports.LoadSnapshotChunkRequest.fromJSON(object.loadSnapshotChunk)
                : undefined,
            applySnapshotChunk: isSet(object.applySnapshotChunk)
                ? exports.ApplySnapshotChunkRequest.fromJSON(object.applySnapshotChunk)
                : undefined,
            prepareProposal: isSet(object.prepareProposal)
                ? exports.PrepareProposalRequest.fromJSON(object.prepareProposal)
                : undefined,
            processProposal: isSet(object.processProposal)
                ? exports.ProcessProposalRequest.fromJSON(object.processProposal)
                : undefined,
            extendVote: isSet(object.extendVote) ? exports.ExtendVoteRequest.fromJSON(object.extendVote) : undefined,
            verifyVoteExtension: isSet(object.verifyVoteExtension)
                ? exports.VerifyVoteExtensionRequest.fromJSON(object.verifyVoteExtension)
                : undefined,
            finalizeBlock: isSet(object.finalizeBlock) ? exports.FinalizeBlockRequest.fromJSON(object.finalizeBlock) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.echo !== undefined && (obj.echo = message.echo ? exports.EchoRequest.toJSON(message.echo) : undefined);
        message.flush !== undefined && (obj.flush = message.flush ? exports.FlushRequest.toJSON(message.flush) : undefined);
        message.info !== undefined && (obj.info = message.info ? exports.InfoRequest.toJSON(message.info) : undefined);
        message.initChain !== undefined &&
            (obj.initChain = message.initChain ? exports.InitChainRequest.toJSON(message.initChain) : undefined);
        message.query !== undefined && (obj.query = message.query ? exports.QueryRequest.toJSON(message.query) : undefined);
        message.checkTx !== undefined &&
            (obj.checkTx = message.checkTx ? exports.CheckTxRequest.toJSON(message.checkTx) : undefined);
        message.commit !== undefined && (obj.commit = message.commit ? exports.CommitRequest.toJSON(message.commit) : undefined);
        message.listSnapshots !== undefined &&
            (obj.listSnapshots = message.listSnapshots ? exports.ListSnapshotsRequest.toJSON(message.listSnapshots) : undefined);
        message.offerSnapshot !== undefined &&
            (obj.offerSnapshot = message.offerSnapshot ? exports.OfferSnapshotRequest.toJSON(message.offerSnapshot) : undefined);
        message.loadSnapshotChunk !== undefined && (obj.loadSnapshotChunk = message.loadSnapshotChunk
            ? exports.LoadSnapshotChunkRequest.toJSON(message.loadSnapshotChunk)
            : undefined);
        message.applySnapshotChunk !== undefined && (obj.applySnapshotChunk = message.applySnapshotChunk
            ? exports.ApplySnapshotChunkRequest.toJSON(message.applySnapshotChunk)
            : undefined);
        message.prepareProposal !== undefined && (obj.prepareProposal = message.prepareProposal
            ? exports.PrepareProposalRequest.toJSON(message.prepareProposal)
            : undefined);
        message.processProposal !== undefined && (obj.processProposal = message.processProposal
            ? exports.ProcessProposalRequest.toJSON(message.processProposal)
            : undefined);
        message.extendVote !== undefined &&
            (obj.extendVote = message.extendVote ? exports.ExtendVoteRequest.toJSON(message.extendVote) : undefined);
        message.verifyVoteExtension !== undefined && (obj.verifyVoteExtension = message.verifyVoteExtension
            ? exports.VerifyVoteExtensionRequest.toJSON(message.verifyVoteExtension)
            : undefined);
        message.finalizeBlock !== undefined &&
            (obj.finalizeBlock = message.finalizeBlock ? exports.FinalizeBlockRequest.toJSON(message.finalizeBlock) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Request.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseRequest();
        message.echo = (object.echo !== undefined && object.echo !== null)
            ? exports.EchoRequest.fromPartial(object.echo)
            : undefined;
        message.flush = (object.flush !== undefined && object.flush !== null)
            ? exports.FlushRequest.fromPartial(object.flush)
            : undefined;
        message.info = (object.info !== undefined && object.info !== null)
            ? exports.InfoRequest.fromPartial(object.info)
            : undefined;
        message.initChain = (object.initChain !== undefined && object.initChain !== null)
            ? exports.InitChainRequest.fromPartial(object.initChain)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? exports.QueryRequest.fromPartial(object.query)
            : undefined;
        message.checkTx = (object.checkTx !== undefined && object.checkTx !== null)
            ? exports.CheckTxRequest.fromPartial(object.checkTx)
            : undefined;
        message.commit = (object.commit !== undefined && object.commit !== null)
            ? exports.CommitRequest.fromPartial(object.commit)
            : undefined;
        message.listSnapshots = (object.listSnapshots !== undefined && object.listSnapshots !== null)
            ? exports.ListSnapshotsRequest.fromPartial(object.listSnapshots)
            : undefined;
        message.offerSnapshot = (object.offerSnapshot !== undefined && object.offerSnapshot !== null)
            ? exports.OfferSnapshotRequest.fromPartial(object.offerSnapshot)
            : undefined;
        message.loadSnapshotChunk = (object.loadSnapshotChunk !== undefined && object.loadSnapshotChunk !== null)
            ? exports.LoadSnapshotChunkRequest.fromPartial(object.loadSnapshotChunk)
            : undefined;
        message.applySnapshotChunk = (object.applySnapshotChunk !== undefined && object.applySnapshotChunk !== null)
            ? exports.ApplySnapshotChunkRequest.fromPartial(object.applySnapshotChunk)
            : undefined;
        message.prepareProposal = (object.prepareProposal !== undefined && object.prepareProposal !== null)
            ? exports.PrepareProposalRequest.fromPartial(object.prepareProposal)
            : undefined;
        message.processProposal = (object.processProposal !== undefined && object.processProposal !== null)
            ? exports.ProcessProposalRequest.fromPartial(object.processProposal)
            : undefined;
        message.extendVote = (object.extendVote !== undefined && object.extendVote !== null)
            ? exports.ExtendVoteRequest.fromPartial(object.extendVote)
            : undefined;
        message.verifyVoteExtension = (object.verifyVoteExtension !== undefined && object.verifyVoteExtension !== null)
            ? exports.VerifyVoteExtensionRequest.fromPartial(object.verifyVoteExtension)
            : undefined;
        message.finalizeBlock = (object.finalizeBlock !== undefined && object.finalizeBlock !== null)
            ? exports.FinalizeBlockRequest.fromPartial(object.finalizeBlock)
            : undefined;
        return message;
    },
};
function createBaseEchoRequest() {
    return { message: "" };
}
exports.EchoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.message !== "") {
            writer.uint32(10).string(message.message);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEchoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.message = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { message: isSet(object.message) ? String(object.message) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.message !== undefined && (obj.message = message.message);
        return obj;
    },
    create: function (base) {
        return exports.EchoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEchoRequest();
        message.message = (_a = object.message) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseFlushRequest() {
    return {};
}
exports.FlushRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFlushRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.FlushRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseFlushRequest();
        return message;
    },
};
function createBaseInfoRequest() {
    return { version: "", blockVersion: "0", p2pVersion: "0", abciVersion: "" };
}
exports.InfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.version !== "") {
            writer.uint32(10).string(message.version);
        }
        if (message.blockVersion !== "0") {
            writer.uint32(16).uint64(message.blockVersion);
        }
        if (message.p2pVersion !== "0") {
            writer.uint32(24).uint64(message.p2pVersion);
        }
        if (message.abciVersion !== "") {
            writer.uint32(34).string(message.abciVersion);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.version = reader.string();
                    break;
                case 2:
                    message.blockVersion = longToString(reader.uint64());
                    break;
                case 3:
                    message.p2pVersion = longToString(reader.uint64());
                    break;
                case 4:
                    message.abciVersion = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            version: isSet(object.version) ? String(object.version) : "",
            blockVersion: isSet(object.blockVersion) ? String(object.blockVersion) : "0",
            p2pVersion: isSet(object.p2pVersion) ? String(object.p2pVersion) : "0",
            abciVersion: isSet(object.abciVersion) ? String(object.abciVersion) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.version !== undefined && (obj.version = message.version);
        message.blockVersion !== undefined && (obj.blockVersion = message.blockVersion);
        message.p2pVersion !== undefined && (obj.p2pVersion = message.p2pVersion);
        message.abciVersion !== undefined && (obj.abciVersion = message.abciVersion);
        return obj;
    },
    create: function (base) {
        return exports.InfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseInfoRequest();
        message.version = (_a = object.version) !== null && _a !== void 0 ? _a : "";
        message.blockVersion = (_b = object.blockVersion) !== null && _b !== void 0 ? _b : "0";
        message.p2pVersion = (_c = object.p2pVersion) !== null && _c !== void 0 ? _c : "0";
        message.abciVersion = (_d = object.abciVersion) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseInitChainRequest() {
    return {
        time: undefined,
        chainId: "",
        consensusParams: undefined,
        validators: [],
        appStateBytes: new Uint8Array(),
        initialHeight: "0",
    };
}
exports.InitChainRequest = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(10).fork()).ldelim();
        }
        if (message.chainId !== "") {
            writer.uint32(18).string(message.chainId);
        }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.validators), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ValidatorUpdate.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.appStateBytes.length !== 0) {
            writer.uint32(42).bytes(message.appStateBytes);
        }
        if (message.initialHeight !== "0") {
            writer.uint32(48).int64(message.initialHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInitChainRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.chainId = reader.string();
                    break;
                case 3:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.validators.push(exports.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.appStateBytes = reader.bytes();
                    break;
                case 6:
                    message.initialHeight = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            chainId: isSet(object.chainId) ? String(object.chainId) : "",
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            validators: Array.isArray(object === null || object === void 0 ? void 0 : object.validators)
                ? object.validators.map(function (e) { return exports.ValidatorUpdate.fromJSON(e); })
                : [],
            appStateBytes: isSet(object.appStateBytes) ? bytesFromBase64(object.appStateBytes) : new Uint8Array(),
            initialHeight: isSet(object.initialHeight) ? String(object.initialHeight) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.chainId !== undefined && (obj.chainId = message.chainId);
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        if (message.validators) {
            obj.validators = message.validators.map(function (e) { return e ? exports.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validators = [];
        }
        message.appStateBytes !== undefined &&
            (obj.appStateBytes = base64FromBytes(message.appStateBytes !== undefined ? message.appStateBytes : new Uint8Array()));
        message.initialHeight !== undefined && (obj.initialHeight = message.initialHeight);
        return obj;
    },
    create: function (base) {
        return exports.InitChainRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseInitChainRequest();
        message.time = (_a = object.time) !== null && _a !== void 0 ? _a : undefined;
        message.chainId = (_b = object.chainId) !== null && _b !== void 0 ? _b : "";
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.validators = ((_c = object.validators) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.ValidatorUpdate.fromPartial(e); })) || [];
        message.appStateBytes = (_d = object.appStateBytes) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.initialHeight = (_e = object.initialHeight) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseQueryRequest() {
    return { data: new Uint8Array(), path: "", height: "0", prove: false };
}
exports.QueryRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.data.length !== 0) {
            writer.uint32(10).bytes(message.data);
        }
        if (message.path !== "") {
            writer.uint32(18).string(message.path);
        }
        if (message.height !== "0") {
            writer.uint32(24).int64(message.height);
        }
        if (message.prove === true) {
            writer.uint32(32).bool(message.prove);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.data = reader.bytes();
                    break;
                case 2:
                    message.path = reader.string();
                    break;
                case 3:
                    message.height = longToString(reader.int64());
                    break;
                case 4:
                    message.prove = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            path: isSet(object.path) ? String(object.path) : "",
            height: isSet(object.height) ? String(object.height) : "0",
            prove: isSet(object.prove) ? Boolean(object.prove) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.path !== undefined && (obj.path = message.path);
        message.height !== undefined && (obj.height = message.height);
        message.prove !== undefined && (obj.prove = message.prove);
        return obj;
    },
    create: function (base) {
        return exports.QueryRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseQueryRequest();
        message.data = (_a = object.data) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.path = (_b = object.path) !== null && _b !== void 0 ? _b : "";
        message.height = (_c = object.height) !== null && _c !== void 0 ? _c : "0";
        message.prove = (_d = object.prove) !== null && _d !== void 0 ? _d : false;
        return message;
    },
};
function createBaseCheckTxRequest() {
    return { tx: new Uint8Array(), type: 0 };
}
exports.CheckTxRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tx.length !== 0) {
            writer.uint32(10).bytes(message.tx);
        }
        if (message.type !== 0) {
            writer.uint32(24).int32(message.type);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCheckTxRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tx = reader.bytes();
                    break;
                case 3:
                    message.type = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tx: isSet(object.tx) ? bytesFromBase64(object.tx) : new Uint8Array(),
            type: isSet(object.type) ? checkTxTypeFromJSON(object.type) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tx !== undefined && (obj.tx = base64FromBytes(message.tx !== undefined ? message.tx : new Uint8Array()));
        message.type !== undefined && (obj.type = checkTxTypeToJSON(message.type));
        return obj;
    },
    create: function (base) {
        return exports.CheckTxRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCheckTxRequest();
        message.tx = (_a = object.tx) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.type = (_b = object.type) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseCommitRequest() {
    return {};
}
exports.CommitRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCommitRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.CommitRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseCommitRequest();
        return message;
    },
};
function createBaseListSnapshotsRequest() {
    return {};
}
exports.ListSnapshotsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListSnapshotsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.ListSnapshotsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseListSnapshotsRequest();
        return message;
    },
};
function createBaseOfferSnapshotRequest() {
    return { snapshot: undefined, appHash: new Uint8Array() };
}
exports.OfferSnapshotRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.snapshot !== undefined) {
            exports.Snapshot.encode(message.snapshot, writer.uint32(10).fork()).ldelim();
        }
        if (message.appHash.length !== 0) {
            writer.uint32(18).bytes(message.appHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOfferSnapshotRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.snapshot = exports.Snapshot.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            snapshot: isSet(object.snapshot) ? exports.Snapshot.fromJSON(object.snapshot) : undefined,
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.snapshot !== undefined && (obj.snapshot = message.snapshot ? exports.Snapshot.toJSON(message.snapshot) : undefined);
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.OfferSnapshotRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOfferSnapshotRequest();
        message.snapshot = (object.snapshot !== undefined && object.snapshot !== null)
            ? exports.Snapshot.fromPartial(object.snapshot)
            : undefined;
        message.appHash = (_a = object.appHash) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseLoadSnapshotChunkRequest() {
    return { height: "0", format: 0, chunk: 0 };
}
exports.LoadSnapshotChunkRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        if (message.format !== 0) {
            writer.uint32(16).uint32(message.format);
        }
        if (message.chunk !== 0) {
            writer.uint32(24).uint32(message.chunk);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLoadSnapshotChunkRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                case 2:
                    message.format = reader.uint32();
                    break;
                case 3:
                    message.chunk = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            format: isSet(object.format) ? Number(object.format) : 0,
            chunk: isSet(object.chunk) ? Number(object.chunk) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.format !== undefined && (obj.format = Math.round(message.format));
        message.chunk !== undefined && (obj.chunk = Math.round(message.chunk));
        return obj;
    },
    create: function (base) {
        return exports.LoadSnapshotChunkRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseLoadSnapshotChunkRequest();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.format = (_b = object.format) !== null && _b !== void 0 ? _b : 0;
        message.chunk = (_c = object.chunk) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseApplySnapshotChunkRequest() {
    return { index: 0, chunk: new Uint8Array(), sender: "" };
}
exports.ApplySnapshotChunkRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.index !== 0) {
            writer.uint32(8).uint32(message.index);
        }
        if (message.chunk.length !== 0) {
            writer.uint32(18).bytes(message.chunk);
        }
        if (message.sender !== "") {
            writer.uint32(26).string(message.sender);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseApplySnapshotChunkRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.index = reader.uint32();
                    break;
                case 2:
                    message.chunk = reader.bytes();
                    break;
                case 3:
                    message.sender = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            index: isSet(object.index) ? Number(object.index) : 0,
            chunk: isSet(object.chunk) ? bytesFromBase64(object.chunk) : new Uint8Array(),
            sender: isSet(object.sender) ? String(object.sender) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.index !== undefined && (obj.index = Math.round(message.index));
        message.chunk !== undefined &&
            (obj.chunk = base64FromBytes(message.chunk !== undefined ? message.chunk : new Uint8Array()));
        message.sender !== undefined && (obj.sender = message.sender);
        return obj;
    },
    create: function (base) {
        return exports.ApplySnapshotChunkRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseApplySnapshotChunkRequest();
        message.index = (_a = object.index) !== null && _a !== void 0 ? _a : 0;
        message.chunk = (_b = object.chunk) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.sender = (_c = object.sender) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBasePrepareProposalRequest() {
    return {
        maxTxBytes: "0",
        txs: [],
        localLastCommit: undefined,
        misbehavior: [],
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.PrepareProposalRequest = {
    encode: function (message, writer) {
        var e_2, _a, e_3, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.maxTxBytes !== "0") {
            writer.uint32(8).int64(message.maxTxBytes);
        }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).bytes(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.localLastCommit !== undefined) {
            exports.ExtendedCommitInfo.encode(message.localLastCommit, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.Misbehavior.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePrepareProposalRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.maxTxBytes = longToString(reader.int64());
                    break;
                case 2:
                    message.txs.push(reader.bytes());
                    break;
                case 3:
                    message.localLastCommit = exports.ExtendedCommitInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.misbehavior.push(exports.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            maxTxBytes: isSet(object.maxTxBytes) ? String(object.maxTxBytes) : "0",
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            localLastCommit: isSet(object.localLastCommit) ? exports.ExtendedCommitInfo.fromJSON(object.localLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return exports.Misbehavior.fromJSON(e); })
                : [],
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.maxTxBytes !== undefined && (obj.maxTxBytes = message.maxTxBytes);
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.localLastCommit !== undefined &&
            (obj.localLastCommit = message.localLastCommit ? exports.ExtendedCommitInfo.toJSON(message.localLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? exports.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.PrepareProposalRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBasePrepareProposalRequest();
        message.maxTxBytes = (_a = object.maxTxBytes) !== null && _a !== void 0 ? _a : "0";
        message.txs = ((_b = object.txs) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.localLastCommit = (object.localLastCommit !== undefined && object.localLastCommit !== null)
            ? exports.ExtendedCommitInfo.fromPartial(object.localLastCommit)
            : undefined;
        message.misbehavior = ((_c = object.misbehavior) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.Misbehavior.fromPartial(e); })) || [];
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseProcessProposalRequest() {
    return {
        txs: [],
        proposedLastCommit: undefined,
        misbehavior: [],
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.ProcessProposalRequest = {
    encode: function (message, writer) {
        var e_4, _a, e_5, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (message.proposedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.proposedLastCommit, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.Misbehavior.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_5) throw e_5.error; }
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProcessProposalRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                case 2:
                    message.proposedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.misbehavior.push(exports.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.hash = reader.bytes();
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            proposedLastCommit: isSet(object.proposedLastCommit) ? exports.CommitInfo.fromJSON(object.proposedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return exports.Misbehavior.fromJSON(e); })
                : [],
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.proposedLastCommit !== undefined &&
            (obj.proposedLastCommit = message.proposedLastCommit ? exports.CommitInfo.toJSON(message.proposedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? exports.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ProcessProposalRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseProcessProposalRequest();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.proposedLastCommit = (object.proposedLastCommit !== undefined && object.proposedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.proposedLastCommit)
            : undefined;
        message.misbehavior = ((_b = object.misbehavior) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.Misbehavior.fromPartial(e); })) || [];
        message.hash = (_c = object.hash) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseExtendVoteRequest() {
    return {
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        txs: [],
        proposedLastCommit: undefined,
        misbehavior: [],
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
    };
}
exports.ExtendVoteRequest = {
    encode: function (message, writer) {
        var e_6, _a, e_7, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.hash.length !== 0) {
            writer.uint32(10).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(16).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(34).bytes(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_6) throw e_6.error; }
        }
        if (message.proposedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.proposedLastCommit, writer.uint32(42).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.Misbehavior.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_7) throw e_7.error; }
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendVoteRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hash = reader.bytes();
                    break;
                case 2:
                    message.height = longToString(reader.int64());
                    break;
                case 3:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.txs.push(reader.bytes());
                    break;
                case 5:
                    message.proposedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.misbehavior.push(exports.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            proposedLastCommit: isSet(object.proposedLastCommit) ? exports.CommitInfo.fromJSON(object.proposedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return exports.Misbehavior.fromJSON(e); })
                : [],
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.proposedLastCommit !== undefined &&
            (obj.proposedLastCommit = message.proposedLastCommit ? exports.CommitInfo.toJSON(message.proposedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? exports.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ExtendVoteRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseExtendVoteRequest();
        message.hash = (_a = object.hash) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.height = (_b = object.height) !== null && _b !== void 0 ? _b : "0";
        message.time = (_c = object.time) !== null && _c !== void 0 ? _c : undefined;
        message.txs = ((_d = object.txs) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.proposedLastCommit = (object.proposedLastCommit !== undefined && object.proposedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.proposedLastCommit)
            : undefined;
        message.misbehavior = ((_e = object.misbehavior) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.Misbehavior.fromPartial(e); })) || [];
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseVerifyVoteExtensionRequest() {
    return { hash: new Uint8Array(), validatorAddress: new Uint8Array(), height: "0", voteExtension: new Uint8Array() };
}
exports.VerifyVoteExtensionRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.hash.length !== 0) {
            writer.uint32(10).bytes(message.hash);
        }
        if (message.validatorAddress.length !== 0) {
            writer.uint32(18).bytes(message.validatorAddress);
        }
        if (message.height !== "0") {
            writer.uint32(24).int64(message.height);
        }
        if (message.voteExtension.length !== 0) {
            writer.uint32(34).bytes(message.voteExtension);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseVerifyVoteExtensionRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.hash = reader.bytes();
                    break;
                case 2:
                    message.validatorAddress = reader.bytes();
                    break;
                case 3:
                    message.height = longToString(reader.int64());
                    break;
                case 4:
                    message.voteExtension = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            validatorAddress: isSet(object.validatorAddress) ? bytesFromBase64(object.validatorAddress) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.validatorAddress !== undefined &&
            (obj.validatorAddress = base64FromBytes(message.validatorAddress !== undefined ? message.validatorAddress : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.VerifyVoteExtensionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseVerifyVoteExtensionRequest();
        message.hash = (_a = object.hash) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.validatorAddress = (_b = object.validatorAddress) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.height = (_c = object.height) !== null && _c !== void 0 ? _c : "0";
        message.voteExtension = (_d = object.voteExtension) !== null && _d !== void 0 ? _d : new Uint8Array();
        return message;
    },
};
function createBaseFinalizeBlockRequest() {
    return {
        txs: [],
        decidedLastCommit: undefined,
        misbehavior: [],
        hash: new Uint8Array(),
        height: "0",
        time: undefined,
        nextValidatorsHash: new Uint8Array(),
        proposerAddress: new Uint8Array(),
        syncingToHeight: "0",
    };
}
exports.FinalizeBlockRequest = {
    encode: function (message, writer) {
        var e_8, _a, e_9, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.txs), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_8) throw e_8.error; }
        }
        if (message.decidedLastCommit !== undefined) {
            exports.CommitInfo.encode(message.decidedLastCommit, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.misbehavior), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.Misbehavior.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_9) throw e_9.error; }
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.height !== "0") {
            writer.uint32(40).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(50).fork()).ldelim();
        }
        if (message.nextValidatorsHash.length !== 0) {
            writer.uint32(58).bytes(message.nextValidatorsHash);
        }
        if (message.proposerAddress.length !== 0) {
            writer.uint32(66).bytes(message.proposerAddress);
        }
        if (message.syncingToHeight !== "0") {
            writer.uint32(72).int64(message.syncingToHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFinalizeBlockRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                case 2:
                    message.decidedLastCommit = exports.CommitInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.misbehavior.push(exports.Misbehavior.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.hash = reader.bytes();
                    break;
                case 5:
                    message.height = longToString(reader.int64());
                    break;
                case 6:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.nextValidatorsHash = reader.bytes();
                    break;
                case 8:
                    message.proposerAddress = reader.bytes();
                    break;
                case 9:
                    message.syncingToHeight = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [],
            decidedLastCommit: isSet(object.decidedLastCommit) ? exports.CommitInfo.fromJSON(object.decidedLastCommit) : undefined,
            misbehavior: Array.isArray(object === null || object === void 0 ? void 0 : object.misbehavior)
                ? object.misbehavior.map(function (e) { return exports.Misbehavior.fromJSON(e); })
                : [],
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            nextValidatorsHash: isSet(object.nextValidatorsHash)
                ? bytesFromBase64(object.nextValidatorsHash)
                : new Uint8Array(),
            proposerAddress: isSet(object.proposerAddress) ? bytesFromBase64(object.proposerAddress) : new Uint8Array(),
            syncingToHeight: isSet(object.syncingToHeight) ? String(object.syncingToHeight) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        message.decidedLastCommit !== undefined &&
            (obj.decidedLastCommit = message.decidedLastCommit ? exports.CommitInfo.toJSON(message.decidedLastCommit) : undefined);
        if (message.misbehavior) {
            obj.misbehavior = message.misbehavior.map(function (e) { return e ? exports.Misbehavior.toJSON(e) : undefined; });
        }
        else {
            obj.misbehavior = [];
        }
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.nextValidatorsHash !== undefined &&
            (obj.nextValidatorsHash = base64FromBytes(message.nextValidatorsHash !== undefined ? message.nextValidatorsHash : new Uint8Array()));
        message.proposerAddress !== undefined &&
            (obj.proposerAddress = base64FromBytes(message.proposerAddress !== undefined ? message.proposerAddress : new Uint8Array()));
        message.syncingToHeight !== undefined && (obj.syncingToHeight = message.syncingToHeight);
        return obj;
    },
    create: function (base) {
        return exports.FinalizeBlockRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseFinalizeBlockRequest();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.decidedLastCommit = (object.decidedLastCommit !== undefined && object.decidedLastCommit !== null)
            ? exports.CommitInfo.fromPartial(object.decidedLastCommit)
            : undefined;
        message.misbehavior = ((_b = object.misbehavior) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.Misbehavior.fromPartial(e); })) || [];
        message.hash = (_c = object.hash) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.height = (_d = object.height) !== null && _d !== void 0 ? _d : "0";
        message.time = (_e = object.time) !== null && _e !== void 0 ? _e : undefined;
        message.nextValidatorsHash = (_f = object.nextValidatorsHash) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proposerAddress = (_g = object.proposerAddress) !== null && _g !== void 0 ? _g : new Uint8Array();
        message.syncingToHeight = (_h = object.syncingToHeight) !== null && _h !== void 0 ? _h : "0";
        return message;
    },
};
function createBaseResponse() {
    return {
        exception: undefined,
        echo: undefined,
        flush: undefined,
        info: undefined,
        initChain: undefined,
        query: undefined,
        checkTx: undefined,
        commit: undefined,
        listSnapshots: undefined,
        offerSnapshot: undefined,
        loadSnapshotChunk: undefined,
        applySnapshotChunk: undefined,
        prepareProposal: undefined,
        processProposal: undefined,
        extendVote: undefined,
        verifyVoteExtension: undefined,
        finalizeBlock: undefined,
    };
}
exports.Response = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.exception !== undefined) {
            exports.ExceptionResponse.encode(message.exception, writer.uint32(10).fork()).ldelim();
        }
        if (message.echo !== undefined) {
            exports.EchoResponse.encode(message.echo, writer.uint32(18).fork()).ldelim();
        }
        if (message.flush !== undefined) {
            exports.FlushResponse.encode(message.flush, writer.uint32(26).fork()).ldelim();
        }
        if (message.info !== undefined) {
            exports.InfoResponse.encode(message.info, writer.uint32(34).fork()).ldelim();
        }
        if (message.initChain !== undefined) {
            exports.InitChainResponse.encode(message.initChain, writer.uint32(50).fork()).ldelim();
        }
        if (message.query !== undefined) {
            exports.QueryResponse.encode(message.query, writer.uint32(58).fork()).ldelim();
        }
        if (message.checkTx !== undefined) {
            exports.CheckTxResponse.encode(message.checkTx, writer.uint32(74).fork()).ldelim();
        }
        if (message.commit !== undefined) {
            exports.CommitResponse.encode(message.commit, writer.uint32(98).fork()).ldelim();
        }
        if (message.listSnapshots !== undefined) {
            exports.ListSnapshotsResponse.encode(message.listSnapshots, writer.uint32(106).fork()).ldelim();
        }
        if (message.offerSnapshot !== undefined) {
            exports.OfferSnapshotResponse.encode(message.offerSnapshot, writer.uint32(114).fork()).ldelim();
        }
        if (message.loadSnapshotChunk !== undefined) {
            exports.LoadSnapshotChunkResponse.encode(message.loadSnapshotChunk, writer.uint32(122).fork()).ldelim();
        }
        if (message.applySnapshotChunk !== undefined) {
            exports.ApplySnapshotChunkResponse.encode(message.applySnapshotChunk, writer.uint32(130).fork()).ldelim();
        }
        if (message.prepareProposal !== undefined) {
            exports.PrepareProposalResponse.encode(message.prepareProposal, writer.uint32(138).fork()).ldelim();
        }
        if (message.processProposal !== undefined) {
            exports.ProcessProposalResponse.encode(message.processProposal, writer.uint32(146).fork()).ldelim();
        }
        if (message.extendVote !== undefined) {
            exports.ExtendVoteResponse.encode(message.extendVote, writer.uint32(154).fork()).ldelim();
        }
        if (message.verifyVoteExtension !== undefined) {
            exports.VerifyVoteExtensionResponse.encode(message.verifyVoteExtension, writer.uint32(162).fork()).ldelim();
        }
        if (message.finalizeBlock !== undefined) {
            exports.FinalizeBlockResponse.encode(message.finalizeBlock, writer.uint32(170).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.exception = exports.ExceptionResponse.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.echo = exports.EchoResponse.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.flush = exports.FlushResponse.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.info = exports.InfoResponse.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.initChain = exports.InitChainResponse.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.query = exports.QueryResponse.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.checkTx = exports.CheckTxResponse.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.commit = exports.CommitResponse.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.listSnapshots = exports.ListSnapshotsResponse.decode(reader, reader.uint32());
                    break;
                case 14:
                    message.offerSnapshot = exports.OfferSnapshotResponse.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.loadSnapshotChunk = exports.LoadSnapshotChunkResponse.decode(reader, reader.uint32());
                    break;
                case 16:
                    message.applySnapshotChunk = exports.ApplySnapshotChunkResponse.decode(reader, reader.uint32());
                    break;
                case 17:
                    message.prepareProposal = exports.PrepareProposalResponse.decode(reader, reader.uint32());
                    break;
                case 18:
                    message.processProposal = exports.ProcessProposalResponse.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.extendVote = exports.ExtendVoteResponse.decode(reader, reader.uint32());
                    break;
                case 20:
                    message.verifyVoteExtension = exports.VerifyVoteExtensionResponse.decode(reader, reader.uint32());
                    break;
                case 21:
                    message.finalizeBlock = exports.FinalizeBlockResponse.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            exception: isSet(object.exception) ? exports.ExceptionResponse.fromJSON(object.exception) : undefined,
            echo: isSet(object.echo) ? exports.EchoResponse.fromJSON(object.echo) : undefined,
            flush: isSet(object.flush) ? exports.FlushResponse.fromJSON(object.flush) : undefined,
            info: isSet(object.info) ? exports.InfoResponse.fromJSON(object.info) : undefined,
            initChain: isSet(object.initChain) ? exports.InitChainResponse.fromJSON(object.initChain) : undefined,
            query: isSet(object.query) ? exports.QueryResponse.fromJSON(object.query) : undefined,
            checkTx: isSet(object.checkTx) ? exports.CheckTxResponse.fromJSON(object.checkTx) : undefined,
            commit: isSet(object.commit) ? exports.CommitResponse.fromJSON(object.commit) : undefined,
            listSnapshots: isSet(object.listSnapshots) ? exports.ListSnapshotsResponse.fromJSON(object.listSnapshots) : undefined,
            offerSnapshot: isSet(object.offerSnapshot) ? exports.OfferSnapshotResponse.fromJSON(object.offerSnapshot) : undefined,
            loadSnapshotChunk: isSet(object.loadSnapshotChunk)
                ? exports.LoadSnapshotChunkResponse.fromJSON(object.loadSnapshotChunk)
                : undefined,
            applySnapshotChunk: isSet(object.applySnapshotChunk)
                ? exports.ApplySnapshotChunkResponse.fromJSON(object.applySnapshotChunk)
                : undefined,
            prepareProposal: isSet(object.prepareProposal)
                ? exports.PrepareProposalResponse.fromJSON(object.prepareProposal)
                : undefined,
            processProposal: isSet(object.processProposal)
                ? exports.ProcessProposalResponse.fromJSON(object.processProposal)
                : undefined,
            extendVote: isSet(object.extendVote) ? exports.ExtendVoteResponse.fromJSON(object.extendVote) : undefined,
            verifyVoteExtension: isSet(object.verifyVoteExtension)
                ? exports.VerifyVoteExtensionResponse.fromJSON(object.verifyVoteExtension)
                : undefined,
            finalizeBlock: isSet(object.finalizeBlock) ? exports.FinalizeBlockResponse.fromJSON(object.finalizeBlock) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.exception !== undefined &&
            (obj.exception = message.exception ? exports.ExceptionResponse.toJSON(message.exception) : undefined);
        message.echo !== undefined && (obj.echo = message.echo ? exports.EchoResponse.toJSON(message.echo) : undefined);
        message.flush !== undefined && (obj.flush = message.flush ? exports.FlushResponse.toJSON(message.flush) : undefined);
        message.info !== undefined && (obj.info = message.info ? exports.InfoResponse.toJSON(message.info) : undefined);
        message.initChain !== undefined &&
            (obj.initChain = message.initChain ? exports.InitChainResponse.toJSON(message.initChain) : undefined);
        message.query !== undefined && (obj.query = message.query ? exports.QueryResponse.toJSON(message.query) : undefined);
        message.checkTx !== undefined &&
            (obj.checkTx = message.checkTx ? exports.CheckTxResponse.toJSON(message.checkTx) : undefined);
        message.commit !== undefined && (obj.commit = message.commit ? exports.CommitResponse.toJSON(message.commit) : undefined);
        message.listSnapshots !== undefined &&
            (obj.listSnapshots = message.listSnapshots ? exports.ListSnapshotsResponse.toJSON(message.listSnapshots) : undefined);
        message.offerSnapshot !== undefined &&
            (obj.offerSnapshot = message.offerSnapshot ? exports.OfferSnapshotResponse.toJSON(message.offerSnapshot) : undefined);
        message.loadSnapshotChunk !== undefined && (obj.loadSnapshotChunk = message.loadSnapshotChunk
            ? exports.LoadSnapshotChunkResponse.toJSON(message.loadSnapshotChunk)
            : undefined);
        message.applySnapshotChunk !== undefined && (obj.applySnapshotChunk = message.applySnapshotChunk
            ? exports.ApplySnapshotChunkResponse.toJSON(message.applySnapshotChunk)
            : undefined);
        message.prepareProposal !== undefined && (obj.prepareProposal = message.prepareProposal
            ? exports.PrepareProposalResponse.toJSON(message.prepareProposal)
            : undefined);
        message.processProposal !== undefined && (obj.processProposal = message.processProposal
            ? exports.ProcessProposalResponse.toJSON(message.processProposal)
            : undefined);
        message.extendVote !== undefined &&
            (obj.extendVote = message.extendVote ? exports.ExtendVoteResponse.toJSON(message.extendVote) : undefined);
        message.verifyVoteExtension !== undefined && (obj.verifyVoteExtension = message.verifyVoteExtension
            ? exports.VerifyVoteExtensionResponse.toJSON(message.verifyVoteExtension)
            : undefined);
        message.finalizeBlock !== undefined &&
            (obj.finalizeBlock = message.finalizeBlock ? exports.FinalizeBlockResponse.toJSON(message.finalizeBlock) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Response.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseResponse();
        message.exception = (object.exception !== undefined && object.exception !== null)
            ? exports.ExceptionResponse.fromPartial(object.exception)
            : undefined;
        message.echo = (object.echo !== undefined && object.echo !== null)
            ? exports.EchoResponse.fromPartial(object.echo)
            : undefined;
        message.flush = (object.flush !== undefined && object.flush !== null)
            ? exports.FlushResponse.fromPartial(object.flush)
            : undefined;
        message.info = (object.info !== undefined && object.info !== null)
            ? exports.InfoResponse.fromPartial(object.info)
            : undefined;
        message.initChain = (object.initChain !== undefined && object.initChain !== null)
            ? exports.InitChainResponse.fromPartial(object.initChain)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? exports.QueryResponse.fromPartial(object.query)
            : undefined;
        message.checkTx = (object.checkTx !== undefined && object.checkTx !== null)
            ? exports.CheckTxResponse.fromPartial(object.checkTx)
            : undefined;
        message.commit = (object.commit !== undefined && object.commit !== null)
            ? exports.CommitResponse.fromPartial(object.commit)
            : undefined;
        message.listSnapshots = (object.listSnapshots !== undefined && object.listSnapshots !== null)
            ? exports.ListSnapshotsResponse.fromPartial(object.listSnapshots)
            : undefined;
        message.offerSnapshot = (object.offerSnapshot !== undefined && object.offerSnapshot !== null)
            ? exports.OfferSnapshotResponse.fromPartial(object.offerSnapshot)
            : undefined;
        message.loadSnapshotChunk = (object.loadSnapshotChunk !== undefined && object.loadSnapshotChunk !== null)
            ? exports.LoadSnapshotChunkResponse.fromPartial(object.loadSnapshotChunk)
            : undefined;
        message.applySnapshotChunk = (object.applySnapshotChunk !== undefined && object.applySnapshotChunk !== null)
            ? exports.ApplySnapshotChunkResponse.fromPartial(object.applySnapshotChunk)
            : undefined;
        message.prepareProposal = (object.prepareProposal !== undefined && object.prepareProposal !== null)
            ? exports.PrepareProposalResponse.fromPartial(object.prepareProposal)
            : undefined;
        message.processProposal = (object.processProposal !== undefined && object.processProposal !== null)
            ? exports.ProcessProposalResponse.fromPartial(object.processProposal)
            : undefined;
        message.extendVote = (object.extendVote !== undefined && object.extendVote !== null)
            ? exports.ExtendVoteResponse.fromPartial(object.extendVote)
            : undefined;
        message.verifyVoteExtension = (object.verifyVoteExtension !== undefined && object.verifyVoteExtension !== null)
            ? exports.VerifyVoteExtensionResponse.fromPartial(object.verifyVoteExtension)
            : undefined;
        message.finalizeBlock = (object.finalizeBlock !== undefined && object.finalizeBlock !== null)
            ? exports.FinalizeBlockResponse.fromPartial(object.finalizeBlock)
            : undefined;
        return message;
    },
};
function createBaseExceptionResponse() {
    return { error: "" };
}
exports.ExceptionResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.error !== "") {
            writer.uint32(10).string(message.error);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExceptionResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.error = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { error: isSet(object.error) ? String(object.error) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.error !== undefined && (obj.error = message.error);
        return obj;
    },
    create: function (base) {
        return exports.ExceptionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseExceptionResponse();
        message.error = (_a = object.error) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseEchoResponse() {
    return { message: "" };
}
exports.EchoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.message !== "") {
            writer.uint32(10).string(message.message);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEchoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.message = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { message: isSet(object.message) ? String(object.message) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.message !== undefined && (obj.message = message.message);
        return obj;
    },
    create: function (base) {
        return exports.EchoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEchoResponse();
        message.message = (_a = object.message) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseFlushResponse() {
    return {};
}
exports.FlushResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFlushResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.FlushResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseFlushResponse();
        return message;
    },
};
function createBaseInfoResponse() {
    return {
        data: "",
        version: "",
        appVersion: "0",
        lastBlockHeight: "0",
        lastBlockAppHash: new Uint8Array(),
        lanePriorities: {},
        defaultLane: "",
    };
}
exports.InfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.data !== "") {
            writer.uint32(10).string(message.data);
        }
        if (message.version !== "") {
            writer.uint32(18).string(message.version);
        }
        if (message.appVersion !== "0") {
            writer.uint32(24).uint64(message.appVersion);
        }
        if (message.lastBlockHeight !== "0") {
            writer.uint32(32).int64(message.lastBlockHeight);
        }
        if (message.lastBlockAppHash.length !== 0) {
            writer.uint32(42).bytes(message.lastBlockAppHash);
        }
        Object.entries(message.lanePriorities).forEach(function (_a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            exports.InfoResponse_LanePrioritiesEntry.encode({ key: key, value: value }, writer.uint32(50).fork()).ldelim();
        });
        if (message.defaultLane !== "") {
            writer.uint32(58).string(message.defaultLane);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.data = reader.string();
                    break;
                case 2:
                    message.version = reader.string();
                    break;
                case 3:
                    message.appVersion = longToString(reader.uint64());
                    break;
                case 4:
                    message.lastBlockHeight = longToString(reader.int64());
                    break;
                case 5:
                    message.lastBlockAppHash = reader.bytes();
                    break;
                case 6:
                    var entry6 = exports.InfoResponse_LanePrioritiesEntry.decode(reader, reader.uint32());
                    if (entry6.value !== undefined) {
                        message.lanePriorities[entry6.key] = entry6.value;
                    }
                    break;
                case 7:
                    message.defaultLane = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            data: isSet(object.data) ? String(object.data) : "",
            version: isSet(object.version) ? String(object.version) : "",
            appVersion: isSet(object.appVersion) ? String(object.appVersion) : "0",
            lastBlockHeight: isSet(object.lastBlockHeight) ? String(object.lastBlockHeight) : "0",
            lastBlockAppHash: isSet(object.lastBlockAppHash) ? bytesFromBase64(object.lastBlockAppHash) : new Uint8Array(),
            lanePriorities: isObject(object.lanePriorities)
                ? Object.entries(object.lanePriorities).reduce(function (acc, _a) {
                    var _b = __read(_a, 2), key = _b[0], value = _b[1];
                    acc[key] = Number(value);
                    return acc;
                }, {})
                : {},
            defaultLane: isSet(object.defaultLane) ? String(object.defaultLane) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.data !== undefined && (obj.data = message.data);
        message.version !== undefined && (obj.version = message.version);
        message.appVersion !== undefined && (obj.appVersion = message.appVersion);
        message.lastBlockHeight !== undefined && (obj.lastBlockHeight = message.lastBlockHeight);
        message.lastBlockAppHash !== undefined &&
            (obj.lastBlockAppHash = base64FromBytes(message.lastBlockAppHash !== undefined ? message.lastBlockAppHash : new Uint8Array()));
        obj.lanePriorities = {};
        if (message.lanePriorities) {
            Object.entries(message.lanePriorities).forEach(function (_a) {
                var _b = __read(_a, 2), k = _b[0], v = _b[1];
                obj.lanePriorities[k] = Math.round(v);
            });
        }
        message.defaultLane !== undefined && (obj.defaultLane = message.defaultLane);
        return obj;
    },
    create: function (base) {
        return exports.InfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseInfoResponse();
        message.data = (_a = object.data) !== null && _a !== void 0 ? _a : "";
        message.version = (_b = object.version) !== null && _b !== void 0 ? _b : "";
        message.appVersion = (_c = object.appVersion) !== null && _c !== void 0 ? _c : "0";
        message.lastBlockHeight = (_d = object.lastBlockHeight) !== null && _d !== void 0 ? _d : "0";
        message.lastBlockAppHash = (_e = object.lastBlockAppHash) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.lanePriorities = Object.entries((_f = object.lanePriorities) !== null && _f !== void 0 ? _f : {}).reduce(function (acc, _a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            if (value !== undefined) {
                acc[key] = Number(value);
            }
            return acc;
        }, {});
        message.defaultLane = (_g = object.defaultLane) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseInfoResponse_LanePrioritiesEntry() {
    return { key: "", value: 0 };
}
exports.InfoResponse_LanePrioritiesEntry = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== 0) {
            writer.uint32(16).uint32(message.value);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInfoResponse_LanePrioritiesEntry();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { key: isSet(object.key) ? String(object.key) : "", value: isSet(object.value) ? Number(object.value) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = Math.round(message.value));
        return obj;
    },
    create: function (base) {
        return exports.InfoResponse_LanePrioritiesEntry.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseInfoResponse_LanePrioritiesEntry();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (_b = object.value) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseInitChainResponse() {
    return { consensusParams: undefined, validators: [], appHash: new Uint8Array() };
}
exports.InitChainResponse = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.consensusParams !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParams, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.validators), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ValidatorUpdate.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        if (message.appHash.length !== 0) {
            writer.uint32(26).bytes(message.appHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInitChainResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.consensusParams = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.validators.push(exports.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.appHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            consensusParams: isSet(object.consensusParams) ? params_1.ConsensusParams.fromJSON(object.consensusParams) : undefined,
            validators: Array.isArray(object === null || object === void 0 ? void 0 : object.validators)
                ? object.validators.map(function (e) { return exports.ValidatorUpdate.fromJSON(e); })
                : [],
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.consensusParams !== undefined &&
            (obj.consensusParams = message.consensusParams ? params_1.ConsensusParams.toJSON(message.consensusParams) : undefined);
        if (message.validators) {
            obj.validators = message.validators.map(function (e) { return e ? exports.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validators = [];
        }
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.InitChainResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseInitChainResponse();
        message.consensusParams = (object.consensusParams !== undefined && object.consensusParams !== null)
            ? params_1.ConsensusParams.fromPartial(object.consensusParams)
            : undefined;
        message.validators = ((_a = object.validators) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.ValidatorUpdate.fromPartial(e); })) || [];
        message.appHash = (_b = object.appHash) !== null && _b !== void 0 ? _b : new Uint8Array();
        return message;
    },
};
function createBaseQueryResponse() {
    return {
        code: 0,
        log: "",
        info: "",
        index: "0",
        key: new Uint8Array(),
        value: new Uint8Array(),
        proofOps: undefined,
        height: "0",
        codespace: "",
    };
}
exports.QueryResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.code !== 0) {
            writer.uint32(8).uint32(message.code);
        }
        if (message.log !== "") {
            writer.uint32(26).string(message.log);
        }
        if (message.info !== "") {
            writer.uint32(34).string(message.info);
        }
        if (message.index !== "0") {
            writer.uint32(40).int64(message.index);
        }
        if (message.key.length !== 0) {
            writer.uint32(50).bytes(message.key);
        }
        if (message.value.length !== 0) {
            writer.uint32(58).bytes(message.value);
        }
        if (message.proofOps !== undefined) {
            proof_1.ProofOps.encode(message.proofOps, writer.uint32(66).fork()).ldelim();
        }
        if (message.height !== "0") {
            writer.uint32(72).int64(message.height);
        }
        if (message.codespace !== "") {
            writer.uint32(82).string(message.codespace);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.uint32();
                    break;
                case 3:
                    message.log = reader.string();
                    break;
                case 4:
                    message.info = reader.string();
                    break;
                case 5:
                    message.index = longToString(reader.int64());
                    break;
                case 6:
                    message.key = reader.bytes();
                    break;
                case 7:
                    message.value = reader.bytes();
                    break;
                case 8:
                    message.proofOps = proof_1.ProofOps.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.height = longToString(reader.int64());
                    break;
                case 10:
                    message.codespace = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            log: isSet(object.log) ? String(object.log) : "",
            info: isSet(object.info) ? String(object.info) : "",
            index: isSet(object.index) ? String(object.index) : "0",
            key: isSet(object.key) ? bytesFromBase64(object.key) : new Uint8Array(),
            value: isSet(object.value) ? bytesFromBase64(object.value) : new Uint8Array(),
            proofOps: isSet(object.proofOps) ? proof_1.ProofOps.fromJSON(object.proofOps) : undefined,
            height: isSet(object.height) ? String(object.height) : "0",
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.log !== undefined && (obj.log = message.log);
        message.info !== undefined && (obj.info = message.info);
        message.index !== undefined && (obj.index = message.index);
        message.key !== undefined &&
            (obj.key = base64FromBytes(message.key !== undefined ? message.key : new Uint8Array()));
        message.value !== undefined &&
            (obj.value = base64FromBytes(message.value !== undefined ? message.value : new Uint8Array()));
        message.proofOps !== undefined && (obj.proofOps = message.proofOps ? proof_1.ProofOps.toJSON(message.proofOps) : undefined);
        message.height !== undefined && (obj.height = message.height);
        message.codespace !== undefined && (obj.codespace = message.codespace);
        return obj;
    },
    create: function (base) {
        return exports.QueryResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseQueryResponse();
        message.code = (_a = object.code) !== null && _a !== void 0 ? _a : 0;
        message.log = (_b = object.log) !== null && _b !== void 0 ? _b : "";
        message.info = (_c = object.info) !== null && _c !== void 0 ? _c : "";
        message.index = (_d = object.index) !== null && _d !== void 0 ? _d : "0";
        message.key = (_e = object.key) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.value = (_f = object.value) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.proofOps = (object.proofOps !== undefined && object.proofOps !== null)
            ? proof_1.ProofOps.fromPartial(object.proofOps)
            : undefined;
        message.height = (_g = object.height) !== null && _g !== void 0 ? _g : "0";
        message.codespace = (_h = object.codespace) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseCheckTxResponse() {
    return {
        code: 0,
        data: new Uint8Array(),
        log: "",
        info: "",
        gasWanted: "0",
        gasUsed: "0",
        events: [],
        codespace: "",
        laneId: "",
    };
}
exports.CheckTxResponse = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.code !== 0) {
            writer.uint32(8).uint32(message.code);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        if (message.log !== "") {
            writer.uint32(26).string(message.log);
        }
        if (message.info !== "") {
            writer.uint32(34).string(message.info);
        }
        if (message.gasWanted !== "0") {
            writer.uint32(40).int64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(48).int64(message.gasUsed);
        }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Event.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        if (message.codespace !== "") {
            writer.uint32(66).string(message.codespace);
        }
        if (message.laneId !== "") {
            writer.uint32(98).string(message.laneId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCheckTxResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.uint32();
                    break;
                case 2:
                    message.data = reader.bytes();
                    break;
                case 3:
                    message.log = reader.string();
                    break;
                case 4:
                    message.info = reader.string();
                    break;
                case 5:
                    message.gasWanted = longToString(reader.int64());
                    break;
                case 6:
                    message.gasUsed = longToString(reader.int64());
                    break;
                case 7:
                    message.events.push(exports.Event.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.codespace = reader.string();
                    break;
                case 12:
                    message.laneId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            log: isSet(object.log) ? String(object.log) : "",
            info: isSet(object.info) ? String(object.info) : "",
            gasWanted: isSet(object.gas_wanted) ? String(object.gas_wanted) : "0",
            gasUsed: isSet(object.gas_used) ? String(object.gas_used) : "0",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return exports.Event.fromJSON(e); }) : [],
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
            laneId: isSet(object.laneId) ? String(object.laneId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.log !== undefined && (obj.log = message.log);
        message.info !== undefined && (obj.info = message.info);
        message.gasWanted !== undefined && (obj.gas_wanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gas_used = message.gasUsed);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? exports.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        message.codespace !== undefined && (obj.codespace = message.codespace);
        message.laneId !== undefined && (obj.laneId = message.laneId);
        return obj;
    },
    create: function (base) {
        return exports.CheckTxResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseCheckTxResponse();
        message.code = (_a = object.code) !== null && _a !== void 0 ? _a : 0;
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.log = (_c = object.log) !== null && _c !== void 0 ? _c : "";
        message.info = (_d = object.info) !== null && _d !== void 0 ? _d : "";
        message.gasWanted = (_e = object.gasWanted) !== null && _e !== void 0 ? _e : "0";
        message.gasUsed = (_f = object.gasUsed) !== null && _f !== void 0 ? _f : "0";
        message.events = ((_g = object.events) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.Event.fromPartial(e); })) || [];
        message.codespace = (_h = object.codespace) !== null && _h !== void 0 ? _h : "";
        message.laneId = (_j = object.laneId) !== null && _j !== void 0 ? _j : "";
        return message;
    },
};
function createBaseCommitResponse() {
    return { retainHeight: "0" };
}
exports.CommitResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.retainHeight !== "0") {
            writer.uint32(24).int64(message.retainHeight);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCommitResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 3:
                    message.retainHeight = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { retainHeight: isSet(object.retainHeight) ? String(object.retainHeight) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.retainHeight !== undefined && (obj.retainHeight = message.retainHeight);
        return obj;
    },
    create: function (base) {
        return exports.CommitResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseCommitResponse();
        message.retainHeight = (_a = object.retainHeight) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseListSnapshotsResponse() {
    return { snapshots: [] };
}
exports.ListSnapshotsResponse = {
    encode: function (message, writer) {
        var e_12, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.snapshots), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Snapshot.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseListSnapshotsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.snapshots.push(exports.Snapshot.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            snapshots: Array.isArray(object === null || object === void 0 ? void 0 : object.snapshots) ? object.snapshots.map(function (e) { return exports.Snapshot.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.snapshots) {
            obj.snapshots = message.snapshots.map(function (e) { return e ? exports.Snapshot.toJSON(e) : undefined; });
        }
        else {
            obj.snapshots = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ListSnapshotsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseListSnapshotsResponse();
        message.snapshots = ((_a = object.snapshots) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.Snapshot.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOfferSnapshotResponse() {
    return { result: 0 };
}
exports.OfferSnapshotResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.result !== 0) {
            writer.uint32(8).int32(message.result);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOfferSnapshotResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.result = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { result: isSet(object.result) ? offerSnapshotResultFromJSON(object.result) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.result !== undefined && (obj.result = offerSnapshotResultToJSON(message.result));
        return obj;
    },
    create: function (base) {
        return exports.OfferSnapshotResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOfferSnapshotResponse();
        message.result = (_a = object.result) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseLoadSnapshotChunkResponse() {
    return { chunk: new Uint8Array() };
}
exports.LoadSnapshotChunkResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.chunk.length !== 0) {
            writer.uint32(10).bytes(message.chunk);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLoadSnapshotChunkResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.chunk = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { chunk: isSet(object.chunk) ? bytesFromBase64(object.chunk) : new Uint8Array() };
    },
    toJSON: function (message) {
        var obj = {};
        message.chunk !== undefined &&
            (obj.chunk = base64FromBytes(message.chunk !== undefined ? message.chunk : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.LoadSnapshotChunkResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseLoadSnapshotChunkResponse();
        message.chunk = (_a = object.chunk) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseApplySnapshotChunkResponse() {
    return { result: 0, refetchChunks: [], rejectSenders: [] };
}
exports.ApplySnapshotChunkResponse = {
    encode: function (message, writer) {
        var e_13, _a, e_14, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.result !== 0) {
            writer.uint32(8).int32(message.result);
        }
        writer.uint32(18).fork();
        try {
            for (var _c = __values(message.refetchChunks), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(v);
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_13) throw e_13.error; }
        }
        writer.ldelim();
        try {
            for (var _e = __values(message.rejectSenders), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseApplySnapshotChunkResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.result = reader.int32();
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.refetchChunks.push(reader.uint32());
                        }
                    }
                    else {
                        message.refetchChunks.push(reader.uint32());
                    }
                    break;
                case 3:
                    message.rejectSenders.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            result: isSet(object.result) ? applySnapshotChunkResultFromJSON(object.result) : 0,
            refetchChunks: Array.isArray(object === null || object === void 0 ? void 0 : object.refetchChunks) ? object.refetchChunks.map(function (e) { return Number(e); }) : [],
            rejectSenders: Array.isArray(object === null || object === void 0 ? void 0 : object.rejectSenders) ? object.rejectSenders.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.result !== undefined && (obj.result = applySnapshotChunkResultToJSON(message.result));
        if (message.refetchChunks) {
            obj.refetchChunks = message.refetchChunks.map(function (e) { return Math.round(e); });
        }
        else {
            obj.refetchChunks = [];
        }
        if (message.rejectSenders) {
            obj.rejectSenders = message.rejectSenders.map(function (e) { return e; });
        }
        else {
            obj.rejectSenders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ApplySnapshotChunkResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseApplySnapshotChunkResponse();
        message.result = (_a = object.result) !== null && _a !== void 0 ? _a : 0;
        message.refetchChunks = ((_b = object.refetchChunks) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.rejectSenders = ((_c = object.rejectSenders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBasePrepareProposalResponse() {
    return { txs: [] };
}
exports.PrepareProposalResponse = {
    encode: function (message, writer) {
        var e_15, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.txs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_15) throw e_15.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePrepareProposalResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.PrepareProposalResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBasePrepareProposalResponse();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseProcessProposalResponse() {
    return { status: 0 };
}
exports.ProcessProposalResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProcessProposalResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { status: isSet(object.status) ? processProposalStatusFromJSON(object.status) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = processProposalStatusToJSON(message.status));
        return obj;
    },
    create: function (base) {
        return exports.ProcessProposalResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseProcessProposalResponse();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseExtendVoteResponse() {
    return { voteExtension: new Uint8Array() };
}
exports.ExtendVoteResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.voteExtension.length !== 0) {
            writer.uint32(10).bytes(message.voteExtension);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendVoteResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.voteExtension = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array() };
    },
    toJSON: function (message) {
        var obj = {};
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ExtendVoteResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseExtendVoteResponse();
        message.voteExtension = (_a = object.voteExtension) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseVerifyVoteExtensionResponse() {
    return { status: 0 };
}
exports.VerifyVoteExtensionResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== 0) {
            writer.uint32(8).int32(message.status);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseVerifyVoteExtensionResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { status: isSet(object.status) ? verifyVoteExtensionStatusFromJSON(object.status) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = verifyVoteExtensionStatusToJSON(message.status));
        return obj;
    },
    create: function (base) {
        return exports.VerifyVoteExtensionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseVerifyVoteExtensionResponse();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseFinalizeBlockResponse() {
    return {
        events: [],
        txResults: [],
        validatorUpdates: [],
        consensusParamUpdates: undefined,
        appHash: new Uint8Array(),
        nextBlockDelay: undefined,
    };
}
exports.FinalizeBlockResponse = {
    encode: function (message, writer) {
        var e_16, _a, e_17, _b, e_18, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _d = __values(message.events), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                exports.Event.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_16) throw e_16.error; }
        }
        try {
            for (var _f = __values(message.txResults), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exports.ExecTxResult.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_17) throw e_17.error; }
        }
        try {
            for (var _h = __values(message.validatorUpdates), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                exports.ValidatorUpdate.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_18) throw e_18.error; }
        }
        if (message.consensusParamUpdates !== undefined) {
            params_1.ConsensusParams.encode(message.consensusParamUpdates, writer.uint32(34).fork()).ldelim();
        }
        if (message.appHash.length !== 0) {
            writer.uint32(42).bytes(message.appHash);
        }
        if (message.nextBlockDelay !== undefined) {
            duration_1.Duration.encode(message.nextBlockDelay, writer.uint32(50).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFinalizeBlockResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.events.push(exports.Event.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.txResults.push(exports.ExecTxResult.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.validatorUpdates.push(exports.ValidatorUpdate.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.consensusParamUpdates = params_1.ConsensusParams.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.appHash = reader.bytes();
                    break;
                case 6:
                    message.nextBlockDelay = duration_1.Duration.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return exports.Event.fromJSON(e); }) : [],
            txResults: Array.isArray(object === null || object === void 0 ? void 0 : object.txResults) ? object.txResults.map(function (e) { return exports.ExecTxResult.fromJSON(e); }) : [],
            validatorUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.validatorUpdates)
                ? object.validatorUpdates.map(function (e) { return exports.ValidatorUpdate.fromJSON(e); })
                : [],
            consensusParamUpdates: isSet(object.consensusParamUpdates)
                ? params_1.ConsensusParams.fromJSON(object.consensusParamUpdates)
                : undefined,
            appHash: isSet(object.appHash) ? bytesFromBase64(object.appHash) : new Uint8Array(),
            nextBlockDelay: isSet(object.nextBlockDelay) ? duration_1.Duration.fromJSON(object.nextBlockDelay) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? exports.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        if (message.txResults) {
            obj.txResults = message.txResults.map(function (e) { return e ? exports.ExecTxResult.toJSON(e) : undefined; });
        }
        else {
            obj.txResults = [];
        }
        if (message.validatorUpdates) {
            obj.validatorUpdates = message.validatorUpdates.map(function (e) { return e ? exports.ValidatorUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.validatorUpdates = [];
        }
        message.consensusParamUpdates !== undefined && (obj.consensusParamUpdates = message.consensusParamUpdates
            ? params_1.ConsensusParams.toJSON(message.consensusParamUpdates)
            : undefined);
        message.appHash !== undefined &&
            (obj.appHash = base64FromBytes(message.appHash !== undefined ? message.appHash : new Uint8Array()));
        message.nextBlockDelay !== undefined &&
            (obj.nextBlockDelay = message.nextBlockDelay ? duration_1.Duration.toJSON(message.nextBlockDelay) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FinalizeBlockResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseFinalizeBlockResponse();
        message.events = ((_a = object.events) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.Event.fromPartial(e); })) || [];
        message.txResults = ((_b = object.txResults) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.ExecTxResult.fromPartial(e); })) || [];
        message.validatorUpdates = ((_c = object.validatorUpdates) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.ValidatorUpdate.fromPartial(e); })) || [];
        message.consensusParamUpdates =
            (object.consensusParamUpdates !== undefined && object.consensusParamUpdates !== null)
                ? params_1.ConsensusParams.fromPartial(object.consensusParamUpdates)
                : undefined;
        message.appHash = (_d = object.appHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.nextBlockDelay = (object.nextBlockDelay !== undefined && object.nextBlockDelay !== null)
            ? duration_1.Duration.fromPartial(object.nextBlockDelay)
            : undefined;
        return message;
    },
};
function createBaseCommitInfo() {
    return { round: 0, votes: [] };
}
exports.CommitInfo = {
    encode: function (message, writer) {
        var e_19, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.round !== 0) {
            writer.uint32(8).int32(message.round);
        }
        try {
            for (var _b = __values(message.votes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.VoteInfo.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_19) throw e_19.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCommitInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = reader.int32();
                    break;
                case 2:
                    message.votes.push(exports.VoteInfo.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            round: isSet(object.round) ? Number(object.round) : 0,
            votes: Array.isArray(object === null || object === void 0 ? void 0 : object.votes) ? object.votes.map(function (e) { return exports.VoteInfo.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.round !== undefined && (obj.round = Math.round(message.round));
        if (message.votes) {
            obj.votes = message.votes.map(function (e) { return e ? exports.VoteInfo.toJSON(e) : undefined; });
        }
        else {
            obj.votes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CommitInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCommitInfo();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : 0;
        message.votes = ((_b = object.votes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.VoteInfo.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseExtendedCommitInfo() {
    return { round: 0, votes: [] };
}
exports.ExtendedCommitInfo = {
    encode: function (message, writer) {
        var e_20, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.round !== 0) {
            writer.uint32(8).int32(message.round);
        }
        try {
            for (var _b = __values(message.votes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ExtendedVoteInfo.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_20_1) { e_20 = { error: e_20_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_20) throw e_20.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendedCommitInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = reader.int32();
                    break;
                case 2:
                    message.votes.push(exports.ExtendedVoteInfo.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            round: isSet(object.round) ? Number(object.round) : 0,
            votes: Array.isArray(object === null || object === void 0 ? void 0 : object.votes) ? object.votes.map(function (e) { return exports.ExtendedVoteInfo.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.round !== undefined && (obj.round = Math.round(message.round));
        if (message.votes) {
            obj.votes = message.votes.map(function (e) { return e ? exports.ExtendedVoteInfo.toJSON(e) : undefined; });
        }
        else {
            obj.votes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ExtendedCommitInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseExtendedCommitInfo();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : 0;
        message.votes = ((_b = object.votes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.ExtendedVoteInfo.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEvent() {
    return { type: "", attributes: [] };
}
exports.Event = {
    encode: function (message, writer) {
        var e_21, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        try {
            for (var _b = __values(message.attributes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.EventAttribute.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_21_1) { e_21 = { error: e_21_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_21) throw e_21.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.string();
                    break;
                case 2:
                    message.attributes.push(exports.EventAttribute.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            type: isSet(object.type) ? String(object.type) : "",
            attributes: Array.isArray(object === null || object === void 0 ? void 0 : object.attributes)
                ? object.attributes.map(function (e) { return exports.EventAttribute.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.type !== undefined && (obj.type = message.type);
        if (message.attributes) {
            obj.attributes = message.attributes.map(function (e) { return e ? exports.EventAttribute.toJSON(e) : undefined; });
        }
        else {
            obj.attributes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Event.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEvent();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : "";
        message.attributes = ((_b = object.attributes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.EventAttribute.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventAttribute() {
    return { key: "", value: "", index: false };
}
exports.EventAttribute = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== "") {
            writer.uint32(18).string(message.value);
        }
        if (message.index === true) {
            writer.uint32(24).bool(message.index);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAttribute();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = reader.string();
                    break;
                case 3:
                    message.index = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? String(object.value) : "",
            index: isSet(object.index) ? Boolean(object.index) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value);
        message.index !== undefined && (obj.index = message.index);
        return obj;
    },
    create: function (base) {
        return exports.EventAttribute.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventAttribute();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (_b = object.value) !== null && _b !== void 0 ? _b : "";
        message.index = (_c = object.index) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseExecTxResult() {
    return {
        code: 0,
        data: new Uint8Array(),
        log: "",
        info: "",
        gasWanted: "0",
        gasUsed: "0",
        events: [],
        codespace: "",
    };
}
exports.ExecTxResult = {
    encode: function (message, writer) {
        var e_22, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.code !== 0) {
            writer.uint32(8).uint32(message.code);
        }
        if (message.data.length !== 0) {
            writer.uint32(18).bytes(message.data);
        }
        if (message.log !== "") {
            writer.uint32(26).string(message.log);
        }
        if (message.info !== "") {
            writer.uint32(34).string(message.info);
        }
        if (message.gasWanted !== "0") {
            writer.uint32(40).int64(message.gasWanted);
        }
        if (message.gasUsed !== "0") {
            writer.uint32(48).int64(message.gasUsed);
        }
        try {
            for (var _b = __values(message.events), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.Event.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_22_1) { e_22 = { error: e_22_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_22) throw e_22.error; }
        }
        if (message.codespace !== "") {
            writer.uint32(66).string(message.codespace);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExecTxResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.uint32();
                    break;
                case 2:
                    message.data = reader.bytes();
                    break;
                case 3:
                    message.log = reader.string();
                    break;
                case 4:
                    message.info = reader.string();
                    break;
                case 5:
                    message.gasWanted = longToString(reader.int64());
                    break;
                case 6:
                    message.gasUsed = longToString(reader.int64());
                    break;
                case 7:
                    message.events.push(exports.Event.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.codespace = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(),
            log: isSet(object.log) ? String(object.log) : "",
            info: isSet(object.info) ? String(object.info) : "",
            gasWanted: isSet(object.gas_wanted) ? String(object.gas_wanted) : "0",
            gasUsed: isSet(object.gas_used) ? String(object.gas_used) : "0",
            events: Array.isArray(object === null || object === void 0 ? void 0 : object.events) ? object.events.map(function (e) { return exports.Event.fromJSON(e); }) : [],
            codespace: isSet(object.codespace) ? String(object.codespace) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.data !== undefined &&
            (obj.data = base64FromBytes(message.data !== undefined ? message.data : new Uint8Array()));
        message.log !== undefined && (obj.log = message.log);
        message.info !== undefined && (obj.info = message.info);
        message.gasWanted !== undefined && (obj.gas_wanted = message.gasWanted);
        message.gasUsed !== undefined && (obj.gas_used = message.gasUsed);
        if (message.events) {
            obj.events = message.events.map(function (e) { return e ? exports.Event.toJSON(e) : undefined; });
        }
        else {
            obj.events = [];
        }
        message.codespace !== undefined && (obj.codespace = message.codespace);
        return obj;
    },
    create: function (base) {
        return exports.ExecTxResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseExecTxResult();
        message.code = (_a = object.code) !== null && _a !== void 0 ? _a : 0;
        message.data = (_b = object.data) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.log = (_c = object.log) !== null && _c !== void 0 ? _c : "";
        message.info = (_d = object.info) !== null && _d !== void 0 ? _d : "";
        message.gasWanted = (_e = object.gasWanted) !== null && _e !== void 0 ? _e : "0";
        message.gasUsed = (_f = object.gasUsed) !== null && _f !== void 0 ? _f : "0";
        message.events = ((_g = object.events) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.Event.fromPartial(e); })) || [];
        message.codespace = (_h = object.codespace) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseTxResult() {
    return { height: "0", index: 0, tx: new Uint8Array(), result: undefined };
}
exports.TxResult = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        if (message.index !== 0) {
            writer.uint32(16).uint32(message.index);
        }
        if (message.tx.length !== 0) {
            writer.uint32(26).bytes(message.tx);
        }
        if (message.result !== undefined) {
            exports.ExecTxResult.encode(message.result, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTxResult();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                case 2:
                    message.index = reader.uint32();
                    break;
                case 3:
                    message.tx = reader.bytes();
                    break;
                case 4:
                    message.result = exports.ExecTxResult.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            index: isSet(object.index) ? Number(object.index) : 0,
            tx: isSet(object.tx) ? bytesFromBase64(object.tx) : new Uint8Array(),
            result: isSet(object.result) ? exports.ExecTxResult.fromJSON(object.result) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.index !== undefined && (obj.index = Math.round(message.index));
        message.tx !== undefined && (obj.tx = base64FromBytes(message.tx !== undefined ? message.tx : new Uint8Array()));
        message.result !== undefined && (obj.result = message.result ? exports.ExecTxResult.toJSON(message.result) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.TxResult.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTxResult();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.index = (_b = object.index) !== null && _b !== void 0 ? _b : 0;
        message.tx = (_c = object.tx) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.result = (object.result !== undefined && object.result !== null)
            ? exports.ExecTxResult.fromPartial(object.result)
            : undefined;
        return message;
    },
};
function createBaseValidator() {
    return { address: new Uint8Array(), power: "0" };
}
exports.Validator = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address.length !== 0) {
            writer.uint32(10).bytes(message.address);
        }
        if (message.power !== "0") {
            writer.uint32(24).int64(message.power);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseValidator();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.bytes();
                    break;
                case 3:
                    message.power = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? bytesFromBase64(object.address) : new Uint8Array(),
            power: isSet(object.power) ? String(object.power) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined &&
            (obj.address = base64FromBytes(message.address !== undefined ? message.address : new Uint8Array()));
        message.power !== undefined && (obj.power = message.power);
        return obj;
    },
    create: function (base) {
        return exports.Validator.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseValidator();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.power = (_b = object.power) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseValidatorUpdate() {
    return { power: "0", pubKeyBytes: new Uint8Array(), pubKeyType: "" };
}
exports.ValidatorUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.power !== "0") {
            writer.uint32(16).int64(message.power);
        }
        if (message.pubKeyBytes.length !== 0) {
            writer.uint32(26).bytes(message.pubKeyBytes);
        }
        if (message.pubKeyType !== "") {
            writer.uint32(34).string(message.pubKeyType);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseValidatorUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 2:
                    message.power = longToString(reader.int64());
                    break;
                case 3:
                    message.pubKeyBytes = reader.bytes();
                    break;
                case 4:
                    message.pubKeyType = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            power: isSet(object.power) ? String(object.power) : "0",
            pubKeyBytes: isSet(object.pubKeyBytes) ? bytesFromBase64(object.pubKeyBytes) : new Uint8Array(),
            pubKeyType: isSet(object.pubKeyType) ? String(object.pubKeyType) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.power !== undefined && (obj.power = message.power);
        message.pubKeyBytes !== undefined &&
            (obj.pubKeyBytes = base64FromBytes(message.pubKeyBytes !== undefined ? message.pubKeyBytes : new Uint8Array()));
        message.pubKeyType !== undefined && (obj.pubKeyType = message.pubKeyType);
        return obj;
    },
    create: function (base) {
        return exports.ValidatorUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseValidatorUpdate();
        message.power = (_a = object.power) !== null && _a !== void 0 ? _a : "0";
        message.pubKeyBytes = (_b = object.pubKeyBytes) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.pubKeyType = (_c = object.pubKeyType) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseVoteInfo() {
    return { validator: undefined, blockIdFlag: 0 };
}
exports.VoteInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validator !== undefined) {
            exports.Validator.encode(message.validator, writer.uint32(10).fork()).ldelim();
        }
        if (message.blockIdFlag !== 0) {
            writer.uint32(24).int32(message.blockIdFlag);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseVoteInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validator = exports.Validator.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.blockIdFlag = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validator: isSet(object.validator) ? exports.Validator.fromJSON(object.validator) : undefined,
            blockIdFlag: isSet(object.blockIdFlag) ? (0, validator_1.blockIDFlagFromJSON)(object.blockIdFlag) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validator !== undefined &&
            (obj.validator = message.validator ? exports.Validator.toJSON(message.validator) : undefined);
        message.blockIdFlag !== undefined && (obj.blockIdFlag = (0, validator_1.blockIDFlagToJSON)(message.blockIdFlag));
        return obj;
    },
    create: function (base) {
        return exports.VoteInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseVoteInfo();
        message.validator = (object.validator !== undefined && object.validator !== null)
            ? exports.Validator.fromPartial(object.validator)
            : undefined;
        message.blockIdFlag = (_a = object.blockIdFlag) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseExtendedVoteInfo() {
    return {
        validator: undefined,
        voteExtension: new Uint8Array(),
        extensionSignature: new Uint8Array(),
        blockIdFlag: 0,
    };
}
exports.ExtendedVoteInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validator !== undefined) {
            exports.Validator.encode(message.validator, writer.uint32(10).fork()).ldelim();
        }
        if (message.voteExtension.length !== 0) {
            writer.uint32(26).bytes(message.voteExtension);
        }
        if (message.extensionSignature.length !== 0) {
            writer.uint32(34).bytes(message.extensionSignature);
        }
        if (message.blockIdFlag !== 0) {
            writer.uint32(40).int32(message.blockIdFlag);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExtendedVoteInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validator = exports.Validator.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.voteExtension = reader.bytes();
                    break;
                case 4:
                    message.extensionSignature = reader.bytes();
                    break;
                case 5:
                    message.blockIdFlag = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validator: isSet(object.validator) ? exports.Validator.fromJSON(object.validator) : undefined,
            voteExtension: isSet(object.voteExtension) ? bytesFromBase64(object.voteExtension) : new Uint8Array(),
            extensionSignature: isSet(object.extensionSignature)
                ? bytesFromBase64(object.extensionSignature)
                : new Uint8Array(),
            blockIdFlag: isSet(object.blockIdFlag) ? (0, validator_1.blockIDFlagFromJSON)(object.blockIdFlag) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validator !== undefined &&
            (obj.validator = message.validator ? exports.Validator.toJSON(message.validator) : undefined);
        message.voteExtension !== undefined &&
            (obj.voteExtension = base64FromBytes(message.voteExtension !== undefined ? message.voteExtension : new Uint8Array()));
        message.extensionSignature !== undefined &&
            (obj.extensionSignature = base64FromBytes(message.extensionSignature !== undefined ? message.extensionSignature : new Uint8Array()));
        message.blockIdFlag !== undefined && (obj.blockIdFlag = (0, validator_1.blockIDFlagToJSON)(message.blockIdFlag));
        return obj;
    },
    create: function (base) {
        return exports.ExtendedVoteInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseExtendedVoteInfo();
        message.validator = (object.validator !== undefined && object.validator !== null)
            ? exports.Validator.fromPartial(object.validator)
            : undefined;
        message.voteExtension = (_a = object.voteExtension) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.extensionSignature = (_b = object.extensionSignature) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.blockIdFlag = (_c = object.blockIdFlag) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseMisbehavior() {
    return { type: 0, validator: undefined, height: "0", time: undefined, totalVotingPower: "0" };
}
exports.Misbehavior = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.type !== 0) {
            writer.uint32(8).int32(message.type);
        }
        if (message.validator !== undefined) {
            exports.Validator.encode(message.validator, writer.uint32(18).fork()).ldelim();
        }
        if (message.height !== "0") {
            writer.uint32(24).int64(message.height);
        }
        if (message.time !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.time), writer.uint32(34).fork()).ldelim();
        }
        if (message.totalVotingPower !== "0") {
            writer.uint32(40).int64(message.totalVotingPower);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMisbehavior();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.int32();
                    break;
                case 2:
                    message.validator = exports.Validator.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.height = longToString(reader.int64());
                    break;
                case 4:
                    message.time = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.totalVotingPower = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            type: isSet(object.type) ? misbehaviorTypeFromJSON(object.type) : 0,
            validator: isSet(object.validator) ? exports.Validator.fromJSON(object.validator) : undefined,
            height: isSet(object.height) ? String(object.height) : "0",
            time: isSet(object.time) ? fromJsonTimestamp(object.time) : undefined,
            totalVotingPower: isSet(object.totalVotingPower) ? String(object.totalVotingPower) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.type !== undefined && (obj.type = misbehaviorTypeToJSON(message.type));
        message.validator !== undefined &&
            (obj.validator = message.validator ? exports.Validator.toJSON(message.validator) : undefined);
        message.height !== undefined && (obj.height = message.height);
        message.time !== undefined && (obj.time = message.time.toISOString());
        message.totalVotingPower !== undefined && (obj.totalVotingPower = message.totalVotingPower);
        return obj;
    },
    create: function (base) {
        return exports.Misbehavior.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMisbehavior();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : 0;
        message.validator = (object.validator !== undefined && object.validator !== null)
            ? exports.Validator.fromPartial(object.validator)
            : undefined;
        message.height = (_b = object.height) !== null && _b !== void 0 ? _b : "0";
        message.time = (_c = object.time) !== null && _c !== void 0 ? _c : undefined;
        message.totalVotingPower = (_d = object.totalVotingPower) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function createBaseSnapshot() {
    return { height: "0", format: 0, chunks: 0, hash: new Uint8Array(), metadata: new Uint8Array() };
}
exports.Snapshot = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).uint64(message.height);
        }
        if (message.format !== 0) {
            writer.uint32(16).uint32(message.format);
        }
        if (message.chunks !== 0) {
            writer.uint32(24).uint32(message.chunks);
        }
        if (message.hash.length !== 0) {
            writer.uint32(34).bytes(message.hash);
        }
        if (message.metadata.length !== 0) {
            writer.uint32(42).bytes(message.metadata);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSnapshot();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.uint64());
                    break;
                case 2:
                    message.format = reader.uint32();
                    break;
                case 3:
                    message.chunks = reader.uint32();
                    break;
                case 4:
                    message.hash = reader.bytes();
                    break;
                case 5:
                    message.metadata = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            format: isSet(object.format) ? Number(object.format) : 0,
            chunks: isSet(object.chunks) ? Number(object.chunks) : 0,
            hash: isSet(object.hash) ? bytesFromBase64(object.hash) : new Uint8Array(),
            metadata: isSet(object.metadata) ? bytesFromBase64(object.metadata) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.format !== undefined && (obj.format = Math.round(message.format));
        message.chunks !== undefined && (obj.chunks = Math.round(message.chunks));
        message.hash !== undefined &&
            (obj.hash = base64FromBytes(message.hash !== undefined ? message.hash : new Uint8Array()));
        message.metadata !== undefined &&
            (obj.metadata = base64FromBytes(message.metadata !== undefined ? message.metadata : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.Snapshot.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseSnapshot();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.format = (_b = object.format) !== null && _b !== void 0 ? _b : 0;
        message.chunks = (_c = object.chunks) !== null && _c !== void 0 ? _c : 0;
        message.hash = (_d = object.hash) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.metadata = (_e = object.metadata) !== null && _e !== void 0 ? _e : new Uint8Array();
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function toTimestamp(date) {
    var seconds = Math.trunc(date.getTime() / 1000).toString();
    var nanos = (date.getTime() % 1000) * 1000000;
    return { seconds: seconds, nanos: nanos };
}
function fromTimestamp(t) {
    var millis = Number(t.seconds) * 1000;
    millis += t.nanos / 1000000;
    return new Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
