"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryTradingStrategyContractConfig = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryTradingStrategyContractConfig extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = (0, index_js_1.toBase64)({
            config: {},
        });
        return payload;
    }
}
exports.QueryTradingStrategyContractConfig = QueryTradingStrategyContractConfig;
