import { InjectiveAbacusRpc } from '@injectivelabs/abacus-proto-ts';
export declare class AbacusGrpcTransformer {
    static grpcPointsStatsDailyToPointsStatsDaily(response: InjectiveAbacusRpc.HistoricalPointsStatsRowCollection): InjectiveAbacusRpc.HistoricalPointsStatsRow[];
    static grpcPointsStatsWeeklyToPointsStatsWeekly(response: InjectiveAbacusRpc.HistoricalPointsStatsRowCollection): InjectiveAbacusRpc.HistoricalPointsStatsRow[];
    static grpcPointsLatestToPointsLatest(response: InjectiveAbacusRpc.PointsLatestForAccountResponse): InjectiveAbacusRpc.PointsLatestForAccountResponse;
}
