#!/usr/bin/env -S npx tsx

/**
 * 测试网络名称解析功能
 * 验证网络名称到链ID的转换是否正确工作
 */

import { 
  resolveNetworkName, 
  validateNetwork, 
  listSupportedNetworks,
  getNetworkFriendlyName,
  NETWORK_CHAIN_ID_MAP 
} from '../src/config/rpc-integration.ts'

function testNetworkResolution() {
  console.log('🧪 Testing Network Name Resolution')
  console.log('==================================')

  const testCases = [
    // 主网测试
    { input: 'ethereum', expected: 1 },
    { input: 'eth', expected: 1 },
    { input: 'mainnet', expected: 1 },
    { input: 'polygon', expected: 137 },
    { input: 'matic', expected: 137 },
    { input: 'bsc', expected: 56 },
    { input: 'bnb', expected: 56 },
    { input: 'binance', expected: 56 },
    { input: 'avalanche', expected: 43114 },
    { input: 'avax', expected: 43114 },
    { input: 'arbitrum', expected: 42161 },
    { input: 'arb', expected: 42161 },
    { input: 'optimism', expected: 10 },
    { input: 'op', expected: 10 },
    { input: 'base', expected: 8453 },
    
    // 测试网测试
    { input: 'sepolia', expected: 11155111 },
    { input: 'eth-sepolia', expected: 11155111 },
    { input: 'fuji', expected: 43113 },
    { input: 'avax-fuji', expected: 43113 },
    { input: 'avalanche-fuji', expected: 43113 },
    { input: 'polygon-amoy', expected: 80002 },
    { input: 'amoy', expected: 80002 },
    { input: 'bsc-testnet', expected: 97 },
    { input: 'bnb-testnet', expected: 97 },
    
    // 数字输入测试
    { input: '1', expected: 1 },
    { input: '137', expected: 137 },
    { input: '43114', expected: 43114 },
    
    // 大小写测试
    { input: 'ETHEREUM', expected: 1 },
    { input: 'Polygon', expected: 137 },
    { input: 'BSC', expected: 56 },
    
    // 无效输入测试
    { input: 'invalid-network', expected: null },
    { input: 'etherem', expected: null }, // 拼写错误
    { input: '', expected: null },
  ]

  let passed = 0
  let failed = 0

  for (const testCase of testCases) {
    const result = resolveNetworkName(testCase.input)
    const success = result === testCase.expected
    
    if (success) {
      console.log(`✅ "${testCase.input}" -> ${result}`)
      passed++
    } else {
      console.log(`❌ "${testCase.input}" -> ${result} (expected: ${testCase.expected})`)
      failed++
    }
  }

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)
  return failed === 0
}

function testNetworkValidation() {
  console.log('\n🔍 Testing Network Validation')
  console.log('==============================')

  const testCases = [
    { input: 'ethereum', shouldBeValid: true },
    { input: 'polygon', shouldBeValid: true },
    { input: '1', shouldBeValid: true },
    { input: '137', shouldBeValid: true },
    { input: 'invalid', shouldBeValid: false },
    { input: 'etherem', shouldBeValid: false }, // 应该提供建议
  ]

  let passed = 0
  let failed = 0

  for (const testCase of testCases) {
    const validation = validateNetwork(testCase.input)
    const success = validation.valid === testCase.shouldBeValid
    
    if (success) {
      console.log(`✅ "${testCase.input}" validation: ${validation.valid}`)
      if (!validation.valid && validation.suggestions) {
        console.log(`   Suggestions: ${validation.suggestions.join(', ')}`)
      }
      passed++
    } else {
      console.log(`❌ "${testCase.input}" validation: ${validation.valid} (expected: ${testCase.shouldBeValid})`)
      failed++
    }
  }

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)
  return failed === 0
}

function testFriendlyNames() {
  console.log('\n🏷️  Testing Friendly Names')
  console.log('===========================')

  const testChainIds = [1, 137, 56, 43114, 42161, 10, 8453, 11155111, 43113, 80002]
  
  for (const chainId of testChainIds) {
    const friendlyName = getNetworkFriendlyName(chainId)
    console.log(`Chain ${chainId}: ${friendlyName}`)
  }
}

function testNetworkListing() {
  console.log('\n📋 Testing Network Listing')
  console.log('===========================')

  const networks = listSupportedNetworks()
  const chainIds = Object.keys(networks).map(Number).sort((a, b) => a - b)
  
  console.log(`Total networks: ${chainIds.length}`)
  
  for (const chainId of chainIds.slice(0, 10)) { // 只显示前10个
    const names = networks[chainId]
    const primaryName = getNetworkFriendlyName(chainId)
    console.log(`${primaryName} (${chainId}): ${names.length} aliases`)
  }
  
  if (chainIds.length > 10) {
    console.log(`... and ${chainIds.length - 10} more networks`)
  }
}

function testCaseInsensitivity() {
  console.log('\n🔤 Testing Case Insensitivity')
  console.log('==============================')

  const testCases = [
    'ethereum',
    'ETHEREUM', 
    'Ethereum',
    'eThErEuM',
    'polygon',
    'POLYGON',
    'Polygon',
    'bsc',
    'BSC',
    'Bsc'
  ]

  let passed = 0
  let failed = 0

  for (const testCase of testCases) {
    const result = resolveNetworkName(testCase)
    if (result !== null) {
      console.log(`✅ "${testCase}" -> ${result}`)
      passed++
    } else {
      console.log(`❌ "${testCase}" -> null`)
      failed++
    }
  }

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)
  return failed === 0
}

function testEdgeCases() {
  console.log('\n🎯 Testing Edge Cases')
  console.log('======================')

  const edgeCases = [
    { input: ' ethereum ', expected: 1 }, // 空格
    { input: '  1  ', expected: 1 }, // 数字带空格
    { input: '0', expected: null }, // 无效链ID
    { input: '-1', expected: null }, // 负数
    { input: 'abc123', expected: null }, // 混合字符
    { input: '999999999', expected: 999999999 }, // 大数字
  ]

  let passed = 0
  let failed = 0

  for (const testCase of edgeCases) {
    const result = resolveNetworkName(testCase.input)
    const success = result === testCase.expected
    
    if (success) {
      console.log(`✅ "${testCase.input}" -> ${result}`)
      passed++
    } else {
      console.log(`❌ "${testCase.input}" -> ${result} (expected: ${testCase.expected})`)
      failed++
    }
  }

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`)
  return failed === 0
}

async function main() {
  console.log('🚀 Network Name Resolution Test Suite')
  console.log('======================================')
  
  const results = [
    testNetworkResolution(),
    testNetworkValidation(),
    testCaseInsensitivity(),
    testEdgeCases()
  ]
  
  // 显示信息性测试
  testFriendlyNames()
  testNetworkListing()
  
  const allPassed = results.every(result => result)
  
  console.log('\n🎉 Test Summary')
  console.log('===============')
  
  if (allPassed) {
    console.log('✅ All tests passed!')
    console.log('\n💡 Network name resolution is working correctly.')
    console.log('You can now use network names like "ethereum", "polygon", "sepolia" etc.')
  } else {
    console.log('❌ Some tests failed!')
    console.log('Please check the implementation.')
    process.exit(1)
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { main }
