"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
/**
 * @category Messages
 */
class MsgVote extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgVote(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.CosmosGovV1Tx.MsgVote.create();
        message.proposalId = params.proposalId.toString();
        message.voter = params.voter;
        message.option = params.vote;
        message.metadata = params.metadata || '';
        return message;
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.gov.v1.MsgVote',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
            option: proto.option,
        };
        return {
            type: 'cosmos-sdk/v1/MsgVote',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.gov.v1.MsgVote',
            ...value,
        };
    }
    toEip712V2() {
        const web3Gw = this.toWeb3Gw();
        web3Gw.option = core_proto_ts_1.CosmosGovV1Gov.voteOptionToJSON(web3Gw.option);
        return web3Gw;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.gov.v1.MsgVote',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosGovV1Tx.MsgVote.encode(this.toProto()).finish();
    }
}
exports.default = MsgVote;
