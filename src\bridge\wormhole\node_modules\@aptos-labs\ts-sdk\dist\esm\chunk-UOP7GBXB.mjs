import{b as p}from"./chunk-HETYL3WN.mjs";async function o(e){let{type:t,originMethod:s,path:i,body:r,acceptType:R,contentType:f,params:c,aptosConfig:n,overrides:A}=e,d=n.getRequestUrl(t);return p({url:d,method:"POST",originMethod:s,path:i,body:r,contentType:f,acceptType:R,params:c,overrides:A},n,e.type)}async function q(e){let{aptosConfig:t}=e;return o({...e,type:"Fullnode",overrides:{...t.clientConfig,...t.fullnodeConfig,...e.overrides,HEADERS:{...t.clientConfig?.HEADERS,...t.fullnodeConfig?.HEADERS}}})}async function C(e){let{aptosConfig:t}=e;return o({...e,type:"Indexer",overrides:{...t.clientConfig,...t.indexerConfig,...e.overrides,HEADERS:{...t.clientConfig?.HEADERS,...t.indexerConfig?.HEADERS}}})}async function E(e){let{aptosConfig:t}=e,s={...t,clientConfig:{...t.clientConfig}};return delete s?.clientConfig?.API_KEY,o({...e,type:"Faucet",overrides:{...s.clientConfig,...s.faucetConfig,...e.overrides,HEADERS:{...s.clientConfig?.HEADERS,...s.faucetConfig?.HEADERS}}})}async function a(e){return o({...e,type:"Pepper"})}async function m(e){return o({...e,type:"Prover"})}export{o as a,q as b,C as c,E as d,a as e,m as f};
//# sourceMappingURL=chunk-UOP7GBXB.mjs.map