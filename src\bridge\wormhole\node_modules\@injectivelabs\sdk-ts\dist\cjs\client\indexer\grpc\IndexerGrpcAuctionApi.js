"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcAuctionApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcAuctionApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_1.IndexerModule.Account;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new indexer_proto_ts_1.InjectiveAuctionRpc.InjectiveAuctionRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchAuction(round) {
        const request = indexer_proto_ts_1.InjectiveAuctionRpc.AuctionEndpointRequest.create();
        /**
         * If round is provided, set it on the request,
         * otherwise fetch latest round
         **/
        if (round) {
            request.round = round.toString();
        }
        try {
            const response = await this.retry(() => this.client.AuctionEndpoint(request, this.metadata));
            return index_js_2.IndexerGrpcAuctionTransformer.auctionResponseToAuction(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveAuctionRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'AuctionEndpoint',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'AuctionEndpoint',
                contextModule: this.module,
            });
        }
    }
    async fetchAuctions() {
        const request = indexer_proto_ts_1.InjectiveAuctionRpc.AuctionsRequest.create();
        try {
            const response = await this.retry(() => this.client.Auctions(request, this.metadata));
            return index_js_2.IndexerGrpcAuctionTransformer.auctionsResponseToAuctions(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveAuctionRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Auctions',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Auctions',
                contextModule: this.module,
            });
        }
    }
    async fetchInjBurnt() {
        const request = {};
        try {
            const response = await this.retry(() => this.client.InjBurntEndpoint(request, this.metadata));
            return index_js_2.IndexerGrpcAuctionTransformer.injBurntResponseToInjBurnt(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveAuctionRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'InjBurntEndpoint',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'InjBurntEndpoint',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcAuctionApi = IndexerGrpcAuctionApi;
