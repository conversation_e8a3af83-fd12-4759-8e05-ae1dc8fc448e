import { InjectiveExchangeV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcExchangeApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveExchangeV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/exchange.js").ExchangeModuleParams>;
    fetchModuleState(): Promise<import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/genesis.js").GenesisState>;
    fetchFeeDiscountSchedule(): Promise<import("../types/exchange.js").FeeDiscountSchedule>;
    fetchFeeDiscountAccountInfo(injectiveAddress: string): Promise<import("../types/exchange.js").FeeDiscountAccountInfo>;
    fetchTradingRewardsCampaign(): Promise<import("../types/exchange.js").TradeRewardCampaign>;
    fetchTradeRewardPoints(injectiveAddresses: string[]): Promise<string[]>;
    fetchPendingTradeRewardPoints(injectiveAddresses: string[], timestamp?: number): Promise<string[]>;
    fetchPositions(): Promise<import("../types/exchange.js").ChainDerivativePosition[]>;
    fetchSubaccountTradeNonce(subaccountId: string): Promise<InjectiveExchangeV1Beta1Query.QuerySubaccountTradeNonceResponse>;
    fetchIsOptedOutOfRewards(account: string): Promise<import("../types/exchange.js").IsOptedOutOfRewards>;
    fetchActiveStakeGrant(account: string): Promise<{
        grant: import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/exchange.js").ActiveGrant;
        effectiveGrant: import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/exchange.js").EffectiveGrant;
    }>;
    fetchDenomDecimal(denom: string): Promise<string>;
    fetchDenomDecimals(): Promise<import("../types/exchange.js").ChainDenomDecimal[]>;
    fetchDenomMinNotional(denom: string): Promise<string>;
    fetchDenomMinNotionals(): Promise<import("../types/exchange.js").ChainDenomMinNotional[]>;
    fetchDerivativeMarkets(status?: string, marketIds?: string[]): Promise<import("../../index.js").DerivativeMarket[]>;
    fetchSpotMarkets(status?: string, marketIds?: string[]): Promise<import("../../index.js").SpotMarket[]>;
    fetchFullSpotMarkets(status?: string, marketIds?: string[]): Promise<import("../../index.js").SpotMarket[]>;
}
