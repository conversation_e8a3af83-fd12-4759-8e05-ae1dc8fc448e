import { ConcreteException } from '../base.js';
import { ErrorType } from '../types/index.js';
import { mapMetamaskMessage } from '../utils/maps.js';
const removeBitGetFromErrorString = (message) => message
    .replaceAll('BitGet', '')
    .replaceAll('Bitget:', '')
    .replaceAll('Bitkeep:', '');
export class BitGetException extends ConcreteException {
    static errorClass = 'BitGetException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setName(BitGetException.errorClass);
        this.setMessage(mapMetamaskMessage(removeBitGetFromErrorString(message)));
    }
}
