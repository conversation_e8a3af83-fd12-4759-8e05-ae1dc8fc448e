"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainRestWasmApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const BaseRestConsumer_js_1 = __importDefault(require("../../base/BaseRestConsumer.js"));
const index_js_1 = require("../types/index.js");
/**
 * @category Chain Wasm API
 */
class ChainRestWasmApi extends BaseRestConsumer_js_1.default {
    async fetchSmartContractState(contractAddress, query, params = {}) {
        const endpoint = `cosmwasm/wasm/v1/contract/${contractAddress}/smart/${query}`;
        try {
            const response = await this.retry(() => this.get(endpoint, params));
            return response.data;
        }
        catch (e) {
            if (e instanceof exceptions_1.HttpRequestException) {
                throw e;
            }
            throw new exceptions_1.HttpRequestException(new Error(e), {
                code: exceptions_1.UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: index_js_1.ChainModule.Bank,
            });
        }
    }
}
exports.ChainRestWasmApi = ChainRestWasmApi;
