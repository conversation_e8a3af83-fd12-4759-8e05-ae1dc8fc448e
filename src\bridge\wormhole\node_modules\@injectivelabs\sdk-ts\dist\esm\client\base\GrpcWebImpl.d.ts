import { grpcPkg } from './../../utils/grpc.js';
interface UnaryMethodDefinitionR extends grpcPkg.grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinition = UnaryMethodDefinitionR;
export interface Rpc {
    unary<T extends UnaryMethodDefinition>(methodDesc: T, request: any, metadata: grpcPkg.grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebError extends Error {
    code: grpcPkg.grpc.Code;
    metadata: grpcPkg.grpc.Metadata;
    constructor(message: string, code: grpcPkg.grpc.Code, metadata: grpcPkg.grpc.Metadata);
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpcPkg.grpc.TransportFactory;
        debug?: boolean;
        setCookieMetadata?: boolean;
        metadata?: grpcPkg.grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinition>(methodDesc: T, _request: any, metadata: grpcPkg.grpc.Metadata | undefined): Promise<any>;
}
export {};
