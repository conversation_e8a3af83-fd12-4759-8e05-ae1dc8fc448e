import { Deserializer } from '../../bcs/deserializer.mjs';
import { Serializable, Serializer } from '../../bcs/serializer.mjs';
import { HexInput } from '../../types/types.mjs';
import { PrivateKey } from './privateKey.mjs';
import { P as PublicKey } from '../../publicKey-CJOcUwJK.mjs';
import { Signature } from './signature.mjs';
import { AptosConfig } from '../../api/aptosConfig.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../hex.mjs';
import '../common.mjs';
import '../accountAddress.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import '../../utils/const.mjs';

/**
 * Represents a Secp256k1 ECDSA public key.
 *
 * @extends PublicKey
 * @property LENGTH - The length of the Secp256k1 public key in bytes.
 * @group Implementation
 * @category Serialization
 */
declare class Secp256k1PublicKey extends PublicKey {
    static readonly LENGTH: number;
    static readonly COMPRESSED_LENGTH: number;
    private readonly key;
    /**
     * Create a new PublicKey instance from a HexInput, which can be a string or Uint8Array.
     * This constructor validates the length of the provided signature data.
     *
     * @param hexInput - A HexInput (string or Uint8Array) representing the signature data.
     * @throws Error if the length of the signature data is not equal to Secp256k1Signature.LENGTH.
     * @group Implementation
     * @category Serialization
     */
    constructor(hexInput: HexInput);
    /**
     * Verifies a Secp256k1 signature against the public key.
     *
     * This function checks the validity of a signature for a given message, ensuring that the signature is canonical as a malleability check.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify against the public key.
     * @group Implementation
     * @category Serialization
     */
    verifySignature(args: {
        message: HexInput;
        signature: Secp256k1Signature;
    }): boolean;
    /**
     * Note: Secp256k1Signatures can be verified syncronously.
     *
     * Verifies the provided signature against the given message.
     * This function helps ensure the integrity and authenticity of the message by confirming that the signature is valid.
     *
     * @param args - The arguments for signature verification.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify, which must be an instance of Secp256k1Signature.
     * @returns A boolean indicating whether the signature is valid for the given message.
     * @group Implementation
     * @category Serialization
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Secp256k1Signature;
    }): Promise<boolean>;
    /**
     * Get the data as a Uint8Array representation.
     *
     * @returns Uint8Array representation of the data.
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Serializes the data into a byte array using the provided serializer.
     * This function is essential for converting data into a format suitable for transmission or storage.
     *
     * @param serializer - The serializer instance used to convert the data.
     * @group Implementation
     * @category Serialization
     */
    serialize(serializer: Serializer): void;
    /**
     * Deserializes a Secp256k1Signature from the provided deserializer.
     * This function allows you to reconstruct a Secp256k1Signature object from its serialized byte representation.
     *
     * @param deserializer - The deserializer instance used to read the serialized data.
     * @group Implementation
     * @category Serialization
     */
    deserialize(deserializer: Deserializer): Secp256k1Signature;
    static deserialize(deserializer: Deserializer): Secp256k1PublicKey;
    /**
     * Determine if the provided public key is an instance of Secp256k1PublicKey.
     *
     * @deprecated use `instanceof Secp256k1PublicKey` instead
     * @param publicKey - The public key to check.
     * @group Implementation
     * @category Serialization
     */
    static isPublicKey(publicKey: PublicKey): publicKey is Secp256k1PublicKey;
    /**
     * Determines if the provided public key is a valid instance of a Secp256k1 public key.
     * This function checks for the presence of a "key" property and validates the length of the key data.
     *
     * @param publicKey - The public key to validate.
     * @returns A boolean indicating whether the public key is a valid Secp256k1 public key.
     * @group Implementation
     * @category Serialization
     */
    static isInstance(publicKey: PublicKey): publicKey is Secp256k1PublicKey;
}
/**
 * Represents a Secp256k1 ECDSA private key, providing functionality to create, sign messages,
 * derive public keys, and serialize/deserialize the key.
 * @group Implementation
 * @category Serialization
 */
declare class Secp256k1PrivateKey extends Serializable implements PrivateKey {
    /**
     * Length of Secp256k1 ecdsa private key
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH: number;
    /**
     * The private key bytes
     * @private
     * @group Implementation
     * @category Serialization
     */
    private readonly key;
    /**
     * Create a new PrivateKey instance from a Uint8Array or String.
     *
     * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)
     *
     * @param hexInput A HexInput (string or Uint8Array)
     * @param strict If true, private key must AIP-80 compliant.
     * @group Implementation
     * @category Serialization
     */
    constructor(hexInput: HexInput, strict?: boolean);
    /**
     * Generate a new random private key.
     *
     * @returns Secp256k1PrivateKey - A newly generated Secp256k1 private key.
     * @group Implementation
     * @category Serialization
     */
    static generate(): Secp256k1PrivateKey;
    /**
     * Derives a private key from a mnemonic seed phrase using a specified BIP44 path.
     *
     * @param path - The BIP44 path to derive the key from.
     * @param mnemonics - The mnemonic seed phrase used for key generation.
     *
     * @returns The generated private key.
     *
     * @throws Error if the provided path is not a valid BIP44 path.
     * @group Implementation
     * @category Serialization
     */
    static fromDerivationPath(path: string, mnemonics: string): Secp256k1PrivateKey;
    /**
     * Derives a private key from a specified BIP44 path using a given seed.
     * This function is essential for generating keys that follow the hierarchical deterministic (HD) wallet structure.
     *
     * @param path - The BIP44 path used for key derivation.
     * @param seed - The seed phrase created by the mnemonics, represented as a Uint8Array.
     * @returns The generated private key as an instance of Secp256k1PrivateKey.
     * @throws Error if the derived private key is invalid.
     * @group Implementation
     * @category Serialization
     */
    private static fromDerivationPathInner;
    /**
     * Sign the given message with the private key.
     * This function generates a cryptographic signature for the provided message, ensuring the signature is canonical and non-malleable.
     *
     * @param message - A message in HexInput format to be signed.
     * @returns Signature - The generated signature for the provided message.
     * @group Implementation
     * @category Serialization
     */
    sign(message: HexInput): Secp256k1Signature;
    /**
     * Derive the Secp256k1PublicKey from this private key.
     *
     * @returns Secp256k1PublicKey The derived public key.
     * @group Implementation
     * @category Serialization
     */
    publicKey(): Secp256k1PublicKey;
    /**
     * Get the private key in bytes (Uint8Array).
     *
     * @returns
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    /**
     * Get the private key as a string representation.
     *
     * @returns string representation of the private key
     * @group Implementation
     * @category Serialization
     */
    toString(): string;
    /**
     * Get the private key as a hex string with the 0x prefix.
     *
     * @returns string representation of the private key.
     */
    toHexString(): string;
    /**
     * Get the private key as a AIP-80 compliant hex string.
     *
     * [Read about AIP-80](https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md)
     *
     * @returns AIP-80 compliant string representation of the private key.
     */
    toAIP80String(): string;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Secp256k1PrivateKey;
    /**
     * Determines if the provided private key is an instance of Secp256k1PrivateKey.
     *
     * @param privateKey - The private key to be checked.
     *
     * @deprecated use `instanceof Secp256k1PrivateKey` instead
     * @group Implementation
     * @category Serialization
     */
    static isPrivateKey(privateKey: PrivateKey): privateKey is Secp256k1PrivateKey;
}
/**
 * Represents a signature of a message signed using a Secp256k1 ECDSA private key.
 *
 * @group Implementation
 * @category Serialization
 */
declare class Secp256k1Signature extends Signature {
    /**
     * Secp256k1 ecdsa signatures are 256-bit.
     * @group Implementation
     * @category Serialization
     */
    static readonly LENGTH = 64;
    /**
     * The signature bytes
     * @private
     * @group Implementation
     * @category Serialization
     */
    private readonly data;
    /**
     * Create a new Signature instance from a Uint8Array or String.
     *
     * @param hexInput A HexInput (string or Uint8Array)
     * @group Implementation
     * @category Serialization
     */
    constructor(hexInput: HexInput);
    toUint8Array(): Uint8Array;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Secp256k1Signature;
}

export { Secp256k1PrivateKey, Secp256k1PublicKey, Secp256k1Signature };
