import { CreateTransactionArgs, CreateTransactionResult, CreateTransactionWithSignersArgs } from './types/index.js';
import { DirectSignResponse } from '@cosmjs/proto-signing';
import { Msgs } from '../modules/msgs.js';
import { CosmosTxV1Beta1Tx } from '@injectivelabs/core-proto-ts';
/**
 * @typedef {Object} CreateTransactionWithSignersArgs
 * @param {CreateTransactionWithSignersArgs} params
 * @property {Msg | Msg[]} message - the Cosmos messages to wrap them in a transaction
 * @property {string} memo - the memo to attach to the transaction
 * @property {StdFee} fee - the fee to attach to the transaction
 * @property {SignerDetails} signers - the signers of the transaction
 * @property {number} number - the account number to attach to the transaction
 * @property {number} chainId - the chain-id to attach to the transaction
 * @property {string} pubKey - the account pubKey to attach to the transaction (in base64)
 *
 * @typedef {Object} CreateTransactionResult
 * @property {TxRaw} txRaw  - the Tx raw that was created
 * @property {SignDoc} signDoc  - the SignDoc that was created - used for signing of the transaction
 * @property {SignerDetails} signers  - the signers of the transaction
 * @property {Uint8Array} bodyBytes  - the body bytes of the transaction
 * @property {Uint8Array} authInfoBytes  - the auth info bytes of the transaction
 * @property {Uint8Array} signBytes  - the sign bytes of the transaction (SignDoc serialized to binary)
 * @property {Uint8Array} signHashedBytes  - the sign bytes of the transaction (SignDoc serialized to binary) and hashed using keccak256
 * @returns {CreateTransactionResult} result
 */
export declare const createTransactionWithSigners: ({ signers, chainId, message, timeoutHeight, memo, fee, signMode, }: CreateTransactionWithSignersArgs) => CreateTransactionResult;
/**
 * @typedef {Object} CreateTransactionArgs
 * @param {CreateTransactionArgs} params
 * @property {MsgArg | MsgArg[]} message - the Cosmos messages to wrap them in a transaction
 * @property {string} memo - the memo to attach to the transaction
 * @property {StdFee} fee - the fee to attach to the transaction
 * @property {string} sequence - the account sequence to attach to the transaction
 * @property {number} number - the account number to attach to the transaction
 * @property {number} chainId - the chain-id to attach to the transaction
 * @property {string} pubKey - the account pubKey to attach to the transaction (in base64)
 *
 * @typedef {Object} CreateTransactionResult
 * @property {TxRaw} txRaw  // the Tx raw that was created
 * @property {SignDoc} signDoc  // the SignDoc that was created - used for signing of the transaction
 * @property {number} accountNumber  // the account number of the signer of the transaction
 * @property {Uint8Array} bodyBytes  // the body bytes of the transaction
 * @property {Uint8Array} authInfoBytes  // the auth info bytes of the transaction
 * @property {Uint8Array} signBytes  // the sign bytes of the transaction (SignDoc serialized to binary)
 * @property {Uint8Array} signHashedBytes  // the sign bytes of the transaction (SignDoc serialized to binary) and hashed using keccak256
 * @returns {CreateTransactionResult} result
 */
export declare const createTransaction: (args: CreateTransactionArgs) => CreateTransactionResult;
/**
 * Used when we want to pass a Msg class instead of the {type, message}
 * object of the Message (using the toDirectSign() method)
 * @returns
 */
export declare const createTransactionFromMsg: (params: Omit<CreateTransactionArgs, "message"> & {
    message: Msgs | Msgs[];
}) => CreateTransactionResult;
/**
 * Used when we get a DirectSignResponse from
 * Cosmos native wallets like Keplr, Leap, etc after
 * the TxRaw has been signed.
 *
 * The reason why we need to create a new TxRaw and
 * not use the one that we passed to signing is that the users
 * can change the gas fees and that will alter the original
 * TxRaw which will cause signature miss match if we broadcast
 * that transaction on chain
 * @returns TxRaw
 */
export declare const createTxRawFromSigResponse: (response: CosmosTxV1Beta1Tx.TxRaw | DirectSignResponse) => CosmosTxV1Beta1Tx.TxRaw;
/**
 * Used when we don't have account details and block details
 * and we pass the message and the user's address only
 * @returns
 */
export declare const createTransactionForAddressAndMsg: (params: Omit<CreateTransactionArgs, "message" | "sequence" | "pubKey" | "accountNumber"> & {
    message: Msgs | Msgs[];
    address: string;
    pubKey?: string;
    endpoint: string;
}) => Promise<CreateTransactionResult>;
export declare const createTransactionAndCosmosSignDoc: (args: CreateTransactionArgs) => {
    cosmosSignDoc: CosmosTxV1Beta1Tx.SignDoc;
    txRaw: CosmosTxV1Beta1Tx.TxRaw;
    signDoc: CosmosTxV1Beta1Tx.SignDoc;
    bodyBytes: Uint8Array;
    signers: import("./types/tx.js").SignerDetails | import("./types/tx.js").SignerDetails[];
    signer: import("./types/tx.js").SignerDetails;
    authInfoBytes: Uint8Array;
    signBytes: Uint8Array;
    signHashedBytes: Uint8Array;
};
export declare const createTransactionAndCosmosSignDocForAddressAndMsg: (params: Omit<CreateTransactionArgs, "message" | "sequence" | "pubKey" | "accountNumber"> & {
    message: Msgs | Msgs[];
    address: string;
    pubKey?: string;
    endpoint: string;
}) => Promise<{
    cosmosSignDoc: CosmosTxV1Beta1Tx.SignDoc;
    txRaw: CosmosTxV1Beta1Tx.TxRaw;
    signDoc: CosmosTxV1Beta1Tx.SignDoc;
    bodyBytes: Uint8Array;
    signers: import("./types/tx.js").SignerDetails | import("./types/tx.js").SignerDetails[];
    signer: import("./types/tx.js").SignerDetails;
    authInfoBytes: Uint8Array;
    signBytes: Uint8Array;
    signHashedBytes: Uint8Array;
}>;
export declare const getTxRawFromTxRawOrDirectSignResponse: (txRawOrDirectSignResponse: CosmosTxV1Beta1Tx.TxRaw | DirectSignResponse) => CosmosTxV1Beta1Tx.TxRaw;
