{"version": 3, "file": "thread-chunk.js", "sources": ["../../../../node_modules/.pnpm/@0no-co+graphql.web@1.0.7_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs", "../../src/commands/generate-persisted/thread.ts"], "sourcesContent": ["var e = {\n  NAME: \"Name\",\n  DOCUMENT: \"Document\",\n  OPERATION_DEFINITION: \"OperationDefinition\",\n  VARIABLE_DEFINITION: \"VariableDefinition\",\n  SELECTION_SET: \"SelectionSet\",\n  FIELD: \"Field\",\n  ARGUMENT: \"Argument\",\n  FRAGMENT_SPREAD: \"FragmentSpread\",\n  INLINE_FRAGMENT: \"InlineFragment\",\n  FRAGMENT_DEFINITION: \"FragmentDefinition\",\n  VARIABLE: \"Variable\",\n  INT: \"IntValue\",\n  FLOAT: \"FloatValue\",\n  STRING: \"StringValue\",\n  BOOLEAN: \"BooleanValue\",\n  NULL: \"NullValue\",\n  ENUM: \"EnumValue\",\n  LIST: \"ListValue\",\n  OBJECT: \"ObjectValue\",\n  OBJECT_FIELD: \"ObjectField\",\n  DIRECTIVE: \"Directive\",\n  NAMED_TYPE: \"NamedType\",\n  LIST_TYPE: \"ListType\",\n  NON_NULL_TYPE: \"NonNullType\"\n};\n\nvar r = {\n  QUERY: \"query\",\n  MUTATION: \"mutation\",\n  SUBSCRIPTION: \"subscription\"\n};\n\nclass GraphQLError extends Error {\n  constructor(e, r, i, n, a, t, l) {\n    super(e);\n    this.name = \"GraphQLError\";\n    this.message = e;\n    if (a) {\n      this.path = a;\n    }\n    if (r) {\n      this.nodes = Array.isArray(r) ? r : [ r ];\n    }\n    if (i) {\n      this.source = i;\n    }\n    if (n) {\n      this.positions = n;\n    }\n    if (t) {\n      this.originalError = t;\n    }\n    var o = l;\n    if (!o && t) {\n      var u = t.extensions;\n      if (u && \"object\" == typeof u) {\n        o = u;\n      }\n    }\n    this.extensions = o || {};\n  }\n  toJSON() {\n    return {\n      ...this,\n      message: this.message\n    };\n  }\n  toString() {\n    return this.message;\n  }\n  get [Symbol.toStringTag]() {\n    return \"GraphQLError\";\n  }\n}\n\nvar i;\n\nvar n;\n\nfunction error(e) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e}`);\n}\n\nfunction advance(e) {\n  e.lastIndex = n;\n  if (e.test(i)) {\n    return i.slice(n, n = e.lastIndex);\n  }\n}\n\nvar a = / +(?=[^\\s])/y;\n\nfunction blockString(e) {\n  var r = e.split(\"\\n\");\n  var i = \"\";\n  var n = 0;\n  var t = 0;\n  var l = r.length - 1;\n  for (var o = 0; o < r.length; o++) {\n    a.lastIndex = 0;\n    if (a.test(r[o])) {\n      if (o && (!n || a.lastIndex < n)) {\n        n = a.lastIndex;\n      }\n      t = t || o;\n      l = o;\n    }\n  }\n  for (var u = t; u <= l; u++) {\n    if (u !== t) {\n      i += \"\\n\";\n    }\n    i += r[u].slice(n).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return i;\n}\n\nfunction ignored() {\n  for (var e = 0 | i.charCodeAt(n++); 9 === e || 10 === e || 13 === e || 32 === e || 35 === e || 44 === e || 65279 === e; e = 0 | i.charCodeAt(n++)) {\n    if (35 === e) {\n      while (10 !== (e = i.charCodeAt(n++)) && 13 !== e) {}\n    }\n  }\n  n--;\n}\n\nvar t = /[_A-Za-z]\\w*/y;\n\nvar l = new RegExp(\"(?:(null|true|false)|\\\\$(\" + t.source + ')|(-?\\\\d+)((?:\\\\.\\\\d+)?[eE][+-]?\\\\d+|\\\\.\\\\d+)?|(\"\"\"(?:\"\"\"|(?:[\\\\s\\\\S]*?[^\\\\\\\\])\"\"\"))|(\"(?:\"|[^\\\\r\\\\n]*?[^\\\\\\\\]\"))|(' + t.source + \"))\", \"y\");\n\nvar o = function(e) {\n  e[e.Const = 1] = \"Const\";\n  e[e.Var = 2] = \"Var\";\n  e[e.Int = 3] = \"Int\";\n  e[e.Float = 4] = \"Float\";\n  e[e.BlockString = 5] = \"BlockString\";\n  e[e.String = 6] = \"String\";\n  e[e.Enum = 7] = \"Enum\";\n  return e;\n}(o || {});\n\nvar u = /\\\\/g;\n\nfunction value(e) {\n  var r;\n  var a;\n  l.lastIndex = n;\n  if (91 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var d = [];\n    while (93 !== i.charCodeAt(n)) {\n      d.push(value(e));\n    }\n    n++;\n    ignored();\n    return {\n      kind: \"ListValue\",\n      values: d\n    };\n  } else if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var v = [];\n    while (125 !== i.charCodeAt(n)) {\n      if (null == (r = advance(t))) {\n        throw error(\"ObjectField\");\n      }\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"ObjectField\");\n      }\n      ignored();\n      v.push({\n        kind: \"ObjectField\",\n        name: {\n          kind: \"Name\",\n          value: r\n        },\n        value: value(e)\n      });\n    }\n    n++;\n    ignored();\n    return {\n      kind: \"ObjectValue\",\n      fields: v\n    };\n  } else if (null != (a = l.exec(i))) {\n    n = l.lastIndex;\n    ignored();\n    if (null != (r = a[o.Const])) {\n      return \"null\" === r ? {\n        kind: \"NullValue\"\n      } : {\n        kind: \"BooleanValue\",\n        value: \"true\" === r\n      };\n    } else if (null != (r = a[o.Var])) {\n      if (e) {\n        throw error(\"Variable\");\n      } else {\n        return {\n          kind: \"Variable\",\n          name: {\n            kind: \"Name\",\n            value: r\n          }\n        };\n      }\n    } else if (null != (r = a[o.Int])) {\n      var s;\n      if (null != (s = a[o.Float])) {\n        return {\n          kind: \"FloatValue\",\n          value: r + s\n        };\n      } else {\n        return {\n          kind: \"IntValue\",\n          value: r\n        };\n      }\n    } else if (null != (r = a[o.BlockString])) {\n      return {\n        kind: \"StringValue\",\n        value: blockString(r.slice(3, -3)),\n        block: !0\n      };\n    } else if (null != (r = a[o.String])) {\n      return {\n        kind: \"StringValue\",\n        value: u.test(r) ? JSON.parse(r) : r.slice(1, -1),\n        block: !1\n      };\n    } else if (null != (r = a[o.Enum])) {\n      return {\n        kind: \"EnumValue\",\n        value: r\n      };\n    }\n  }\n  throw error(\"Value\");\n}\n\nfunction arguments_(e) {\n  if (40 === i.charCodeAt(n)) {\n    var r = [];\n    n++;\n    ignored();\n    var a;\n    do {\n      if (null == (a = advance(t))) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      r.push({\n        kind: \"Argument\",\n        name: {\n          kind: \"Name\",\n          value: a\n        },\n        value: value(e)\n      });\n    } while (41 !== i.charCodeAt(n));\n    n++;\n    ignored();\n    return r;\n  }\n}\n\nfunction directives(e) {\n  if (64 === i.charCodeAt(n)) {\n    var r = [];\n    var a;\n    do {\n      n++;\n      if (null == (a = advance(t))) {\n        throw error(\"Directive\");\n      }\n      ignored();\n      r.push({\n        kind: \"Directive\",\n        name: {\n          kind: \"Name\",\n          value: a\n        },\n        arguments: arguments_(e)\n      });\n    } while (64 === i.charCodeAt(n));\n    return r;\n  }\n}\n\nfunction type() {\n  var e;\n  var r = 0;\n  while (91 === i.charCodeAt(n)) {\n    r++;\n    n++;\n    ignored();\n  }\n  if (null == (e = advance(t))) {\n    throw error(\"NamedType\");\n  }\n  ignored();\n  var a = {\n    kind: \"NamedType\",\n    name: {\n      kind: \"Name\",\n      value: e\n    }\n  };\n  do {\n    if (33 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      a = {\n        kind: \"NonNullType\",\n        type: a\n      };\n    }\n    if (r) {\n      if (93 !== i.charCodeAt(n++)) {\n        throw error(\"NamedType\");\n      }\n      ignored();\n      a = {\n        kind: \"ListType\",\n        type: a\n      };\n    }\n  } while (r--);\n  return a;\n}\n\nvar d = new RegExp(\"(?:(\\\\.{3})|(\" + t.source + \"))\", \"y\");\n\nvar v = function(e) {\n  e[e.Spread = 1] = \"Spread\";\n  e[e.Name = 2] = \"Name\";\n  return e;\n}(v || {});\n\nfunction selectionSet() {\n  var e = [];\n  var r;\n  var a;\n  do {\n    d.lastIndex = n;\n    if (null != (a = d.exec(i))) {\n      n = d.lastIndex;\n      if (null != a[v.Spread]) {\n        ignored();\n        var l = advance(t);\n        if (null != l && \"on\" !== l) {\n          ignored();\n          e.push({\n            kind: \"FragmentSpread\",\n            name: {\n              kind: \"Name\",\n              value: l\n            },\n            directives: directives(!1)\n          });\n        } else {\n          ignored();\n          if (\"on\" === l) {\n            if (null == (l = advance(t))) {\n              throw error(\"NamedType\");\n            }\n            ignored();\n          }\n          var o = directives(!1);\n          if (123 !== i.charCodeAt(n++)) {\n            throw error(\"InlineFragment\");\n          }\n          ignored();\n          e.push({\n            kind: \"InlineFragment\",\n            typeCondition: l ? {\n              kind: \"NamedType\",\n              name: {\n                kind: \"Name\",\n                value: l\n              }\n            } : void 0,\n            directives: o,\n            selectionSet: selectionSet()\n          });\n        }\n      } else if (null != (r = a[v.Name])) {\n        var u = void 0;\n        ignored();\n        if (58 === i.charCodeAt(n)) {\n          n++;\n          ignored();\n          u = r;\n          if (null == (r = advance(t))) {\n            throw error(\"Field\");\n          }\n          ignored();\n        }\n        var s = arguments_(!1);\n        ignored();\n        var c = directives(!1);\n        var f = void 0;\n        if (123 === i.charCodeAt(n)) {\n          n++;\n          ignored();\n          f = selectionSet();\n        }\n        e.push({\n          kind: \"Field\",\n          alias: u ? {\n            kind: \"Name\",\n            value: u\n          } : void 0,\n          name: {\n            kind: \"Name\",\n            value: r\n          },\n          arguments: s,\n          directives: c,\n          selectionSet: f\n        });\n      }\n    } else {\n      throw error(\"SelectionSet\");\n    }\n  } while (125 !== i.charCodeAt(n));\n  n++;\n  ignored();\n  return {\n    kind: \"SelectionSet\",\n    selections: e\n  };\n}\n\nfunction fragmentDefinition() {\n  var e;\n  var r;\n  if (null == (e = advance(t))) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  if (\"on\" !== advance(t)) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  if (null == (r = advance(t))) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  var a = directives(!1);\n  if (123 !== i.charCodeAt(n++)) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  return {\n    kind: \"FragmentDefinition\",\n    name: {\n      kind: \"Name\",\n      value: e\n    },\n    typeCondition: {\n      kind: \"NamedType\",\n      name: {\n        kind: \"Name\",\n        value: r\n      }\n    },\n    directives: a,\n    selectionSet: selectionSet()\n  };\n}\n\nvar s = /(?:query|mutation|subscription|fragment)/y;\n\nfunction operationDefinition(e) {\n  var r;\n  var a;\n  var l;\n  if (e) {\n    ignored();\n    r = advance(t);\n    a = function variableDefinitions() {\n      ignored();\n      if (40 === i.charCodeAt(n)) {\n        var e = [];\n        n++;\n        ignored();\n        var r;\n        do {\n          if (36 !== i.charCodeAt(n++)) {\n            throw error(\"Variable\");\n          }\n          if (null == (r = advance(t))) {\n            throw error(\"Variable\");\n          }\n          ignored();\n          if (58 !== i.charCodeAt(n++)) {\n            throw error(\"VariableDefinition\");\n          }\n          ignored();\n          var a = type();\n          var l = void 0;\n          if (61 === i.charCodeAt(n)) {\n            n++;\n            ignored();\n            l = value(!0);\n          }\n          ignored();\n          e.push({\n            kind: \"VariableDefinition\",\n            variable: {\n              kind: \"Variable\",\n              name: {\n                kind: \"Name\",\n                value: r\n              }\n            },\n            type: a,\n            defaultValue: l,\n            directives: directives(!0)\n          });\n        } while (41 !== i.charCodeAt(n));\n        n++;\n        ignored();\n        return e;\n      }\n    }();\n    l = directives(!1);\n  }\n  if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    return {\n      kind: \"OperationDefinition\",\n      operation: e || \"query\",\n      name: r ? {\n        kind: \"Name\",\n        value: r\n      } : void 0,\n      variableDefinitions: a,\n      directives: l,\n      selectionSet: selectionSet()\n    };\n  }\n}\n\nfunction parse(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return function document() {\n    var e;\n    var r;\n    ignored();\n    var a = [];\n    do {\n      if (\"fragment\" === (e = advance(s))) {\n        ignored();\n        a.push(fragmentDefinition());\n      } else if (null != (r = operationDefinition(e))) {\n        a.push(r);\n      } else {\n        throw error(\"Document\");\n      }\n    } while (n < i.length);\n    return {\n      kind: \"Document\",\n      definitions: a\n    };\n  }();\n}\n\nfunction parseValue(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  ignored();\n  return value(!1);\n}\n\nfunction parseType(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return type();\n}\n\nvar c = {};\n\nfunction visit(e, r) {\n  var i = [];\n  var n = [];\n  try {\n    var a = function traverse(e, a, t) {\n      var l = !1;\n      var o = r[e.kind] && r[e.kind].enter || r[e.kind] || r.enter;\n      var u = o && o.call(r, e, a, t, n, i);\n      if (!1 === u) {\n        return e;\n      } else if (null === u) {\n        return null;\n      } else if (u === c) {\n        throw c;\n      } else if (u && \"string\" == typeof u.kind) {\n        l = u !== e;\n        e = u;\n      }\n      if (t) {\n        i.push(t);\n      }\n      var d;\n      var v = {\n        ...e\n      };\n      for (var s in e) {\n        n.push(s);\n        var f = e[s];\n        if (Array.isArray(f)) {\n          var m = [];\n          for (var g = 0; g < f.length; g++) {\n            if (null != f[g] && \"string\" == typeof f[g].kind) {\n              i.push(e);\n              n.push(g);\n              d = traverse(f[g], g, f);\n              n.pop();\n              i.pop();\n              if (null == d) {\n                l = !0;\n              } else {\n                l = l || d !== f[g];\n                m.push(d);\n              }\n            }\n          }\n          f = m;\n        } else if (null != f && \"string\" == typeof f.kind) {\n          if (void 0 !== (d = traverse(f, s, e))) {\n            l = l || f !== d;\n            f = d;\n          }\n        }\n        n.pop();\n        if (l) {\n          v[s] = f;\n        }\n      }\n      if (t) {\n        i.pop();\n      }\n      var p = r[e.kind] && r[e.kind].leave || r.leave;\n      var h = p && p.call(r, e, a, t, n, i);\n      if (h === c) {\n        throw c;\n      } else if (void 0 !== h) {\n        return h;\n      } else if (void 0 !== u) {\n        return l ? v : u;\n      } else {\n        return l ? v : e;\n      }\n    }(e);\n    return void 0 !== a && !1 !== a ? a : e;\n  } catch (r) {\n    if (r !== c) {\n      throw r;\n    }\n    return e;\n  }\n}\n\nfunction mapJoin(e, r, i) {\n  var n = \"\";\n  for (var a = 0; a < e.length; a++) {\n    if (a) {\n      n += r;\n    }\n    n += i(e[a]);\n  }\n  return n;\n}\n\nfunction printString(e) {\n  return JSON.stringify(e);\n}\n\nfunction printBlockString(e) {\n  return '\"\"\"\\n' + e.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nvar f = \"\\n\";\n\nvar m = {\n  OperationDefinition(e) {\n    var r = e.operation;\n    if (e.name) {\n      r += \" \" + e.name.value;\n    }\n    if (e.variableDefinitions && e.variableDefinitions.length) {\n      if (!e.name) {\n        r += \" \";\n      }\n      r += \"(\" + mapJoin(e.variableDefinitions, \", \", m.VariableDefinition) + \")\";\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return \"query\" !== r ? r + \" \" + m.SelectionSet(e.selectionSet) : m.SelectionSet(e.selectionSet);\n  },\n  VariableDefinition(e) {\n    var r = m.Variable(e.variable) + \": \" + _print(e.type);\n    if (e.defaultValue) {\n      r += \" = \" + _print(e.defaultValue);\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r;\n  },\n  Field(e) {\n    var r = e.alias ? e.alias.value + \": \" + e.name.value : e.name.value;\n    if (e.arguments && e.arguments.length) {\n      var i = mapJoin(e.arguments, \", \", m.Argument);\n      if (r.length + i.length + 2 > 80) {\n        r += \"(\" + (f += \"  \") + mapJoin(e.arguments, f, m.Argument) + (f = f.slice(0, -2)) + \")\";\n      } else {\n        r += \"(\" + i + \")\";\n      }\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    if (e.selectionSet) {\n      r += \" \" + m.SelectionSet(e.selectionSet);\n    }\n    return r;\n  },\n  StringValue(e) {\n    if (e.block) {\n      return printBlockString(e.value).replace(/\\n/g, f);\n    } else {\n      return printString(e.value);\n    }\n  },\n  BooleanValue: e => \"\" + e.value,\n  NullValue: e => \"null\",\n  IntValue: e => e.value,\n  FloatValue: e => e.value,\n  EnumValue: e => e.value,\n  Name: e => e.value,\n  Variable: e => \"$\" + e.name.value,\n  ListValue: e => \"[\" + mapJoin(e.values, \", \", _print) + \"]\",\n  ObjectValue: e => \"{\" + mapJoin(e.fields, \", \", m.ObjectField) + \"}\",\n  ObjectField: e => e.name.value + \": \" + _print(e.value),\n  Document(e) {\n    if (!e.definitions || !e.definitions.length) {\n      return \"\";\n    }\n    return mapJoin(e.definitions, \"\\n\\n\", _print);\n  },\n  SelectionSet: e => \"{\" + (f += \"  \") + mapJoin(e.selections, f, _print) + (f = f.slice(0, -2)) + \"}\",\n  Argument: e => e.name.value + \": \" + _print(e.value),\n  FragmentSpread(e) {\n    var r = \"...\" + e.name.value;\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r;\n  },\n  InlineFragment(e) {\n    var r = \"...\";\n    if (e.typeCondition) {\n      r += \" on \" + e.typeCondition.name.value;\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r += \" \" + m.SelectionSet(e.selectionSet);\n  },\n  FragmentDefinition(e) {\n    var r = \"fragment \" + e.name.value;\n    r += \" on \" + e.typeCondition.name.value;\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r + \" \" + m.SelectionSet(e.selectionSet);\n  },\n  Directive(e) {\n    var r = \"@\" + e.name.value;\n    if (e.arguments && e.arguments.length) {\n      r += \"(\" + mapJoin(e.arguments, \", \", m.Argument) + \")\";\n    }\n    return r;\n  },\n  NamedType: e => e.name.value,\n  ListType: e => \"[\" + _print(e.type) + \"]\",\n  NonNullType: e => _print(e.type) + \"!\"\n};\n\nvar _print = e => m[e.kind](e);\n\nfunction print(e) {\n  f = \"\\n\";\n  return m[e.kind] ? m[e.kind](e) : \"\";\n}\n\nfunction valueFromASTUntyped(e, r) {\n  switch (e.kind) {\n   case \"NullValue\":\n    return null;\n\n   case \"IntValue\":\n    return parseInt(e.value, 10);\n\n   case \"FloatValue\":\n    return parseFloat(e.value);\n\n   case \"StringValue\":\n   case \"EnumValue\":\n   case \"BooleanValue\":\n    return e.value;\n\n   case \"ListValue\":\n    var i = [];\n    for (var n of e.values) {\n      i.push(valueFromASTUntyped(n, r));\n    }\n    return i;\n\n   case \"ObjectValue\":\n    var a = Object.create(null);\n    for (var t of e.fields) {\n      a[t.name.value] = valueFromASTUntyped(t.value, r);\n    }\n    return a;\n\n   case \"Variable\":\n    return r && r[e.name.value];\n  }\n}\n\nfunction valueFromTypeNode(e, r, i) {\n  if (\"Variable\" === e.kind) {\n    return i ? valueFromTypeNode(i[e.name.value], r, i) : void 0;\n  } else if (\"NonNullType\" === r.kind) {\n    return \"NullValue\" !== e.kind ? valueFromTypeNode(e, r, i) : void 0;\n  } else if (\"NullValue\" === e.kind) {\n    return null;\n  } else if (\"ListType\" === r.kind) {\n    if (\"ListValue\" === e.kind) {\n      var n = [];\n      for (var a of e.values) {\n        var t = valueFromTypeNode(a, r.type, i);\n        if (void 0 === t) {\n          return;\n        } else {\n          n.push(t);\n        }\n      }\n      return n;\n    }\n  } else if (\"NamedType\" === r.kind) {\n    switch (r.name.value) {\n     case \"Int\":\n     case \"Float\":\n     case \"String\":\n     case \"Bool\":\n      return r.name.value + \"Value\" === e.kind ? valueFromASTUntyped(e, i) : void 0;\n\n     default:\n      return valueFromASTUntyped(e, i);\n    }\n  }\n}\n\nexport { c as BREAK, GraphQLError, e as Kind, r as OperationTypeNode, parse, parseType, parseValue, print, printBlockString, printString, valueFromASTUntyped, valueFromTypeNode, visit };\n//# sourceMappingURL=graphql.web.mjs.map\n", "import ts from 'typescript';\nimport { Kind, parse, print } from '@0no-co/graphql.web';\n\nimport type { FragmentDefinitionNode } from '@0no-co/graphql.web';\nimport type { GraphQLSPConfig } from '@gql.tada/internal';\nimport { getSchemaNamesFromConfig } from '@gql.tada/internal';\n\nimport {\n  findAllPersistedCallExpressions,\n  getDocumentReferenceFromTypeQuery,\n  getDocumentReferenceFromDocumentNode,\n  unrollTadaFragments,\n} from '@0no-co/graphqlsp/api';\n\nimport { programFactory } from '../../ts';\nimport { expose } from '../../threads';\n\nimport type { PersistedSignal, PersistedWarning, PersistedDocument } from './types';\n\nexport interface PersistedParams {\n  disableNormalization: boolean;\n  rootPath: string;\n  configPath: string;\n  pluginConfig: GraphQLSPConfig;\n}\n\nasync function* _runPersisted(params: PersistedParams): AsyncIterableIterator<PersistedSignal> {\n  const schemaNames = getSchemaNamesFromConfig(params.pluginConfig);\n  const factory = programFactory(params);\n\n  const externalFiles = factory.createExternalFiles();\n  if (externalFiles.length) {\n    yield { kind: 'EXTERNAL_WARNING' };\n    await factory.addVirtualFiles(externalFiles);\n  }\n\n  const container = factory.build();\n  const pluginInfo = container.buildPluginInfo(params.pluginConfig);\n  const sourceFiles = container.getSourceFiles();\n\n  yield {\n    kind: 'FILE_COUNT',\n    fileCount: sourceFiles.length,\n  };\n\n  for (const sourceFile of sourceFiles) {\n    let filePath = sourceFile.fileName;\n    const documents: PersistedDocument[] = [];\n    const warnings: PersistedWarning[] = [];\n\n    const calls = findAllPersistedCallExpressions(sourceFile, pluginInfo);\n    for (const call of calls) {\n      const position = container.getSourcePosition(sourceFile, call.node.getStart());\n      filePath = position.fileName;\n\n      if (!schemaNames.has(call.schema)) {\n        warnings.push({\n          message: call.schema\n            ? `The '${call.schema}' schema is not in the configuration but was referenced by \"graphql.persisted\".`\n            : schemaNames.size > 1\n              ? 'The document is not for a known schema. Have you re-generated the output file?'\n              : 'Multiple schemas are configured, but the document is not for a specific schema.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      const hashArg = call.node.arguments[0];\n      const docArg = call.node.arguments[1];\n      const typeQuery = call.node.typeArguments && call.node.typeArguments[0];\n      if (!hashArg || !ts.isStringLiteral(hashArg)) {\n        warnings.push({\n          message:\n            '\"graphql.persisted\" must be called with a string literal as the first argument.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      } else if (!docArg && !typeQuery) {\n        warnings.push({\n          message:\n            '\"graphql.persisted\" is missing a document.\\n' +\n            'This may be passed as a generic such as `graphql.persisted<typeof document>` or as the second argument.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      let foundNode: ts.CallExpression | null = null;\n      let referencingNode: ts.Node = call.node;\n      if (docArg && (ts.isCallExpression(docArg) || ts.isIdentifier(docArg))) {\n        const result = getDocumentReferenceFromDocumentNode(\n          docArg,\n          sourceFile.fileName,\n          pluginInfo\n        );\n        foundNode = result.node;\n        referencingNode = docArg;\n      } else if (typeQuery && ts.isTypeQueryNode(typeQuery)) {\n        const result = getDocumentReferenceFromTypeQuery(\n          typeQuery,\n          sourceFile.fileName,\n          pluginInfo\n        );\n        foundNode = result.node;\n        referencingNode = typeQuery;\n      }\n\n      if (!foundNode) {\n        warnings.push({\n          message:\n            `Could not find reference for \"${referencingNode.getText()}\".\\n` +\n            'If this is unexpected, please file an issue describing your case.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      if (\n        !foundNode ||\n        !ts.isCallExpression(foundNode) ||\n        (!ts.isNoSubstitutionTemplateLiteral(foundNode.arguments[0]) &&\n          !ts.isStringLiteral(foundNode.arguments[0]))\n      ) {\n        warnings.push({\n          message:\n            `The referenced document of \"${referencingNode.getText()}\" contains no document string literal.\\n` +\n            'If this is unexpected, please file an issue describing your case.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      const fragmentDefs: FragmentDefinitionNode[] = [];\n      const operation = foundNode.arguments[0].getText().slice(1, -1);\n      if (foundNode.arguments[1] && ts.isArrayLiteralExpression(foundNode.arguments[1])) {\n        unrollTadaFragments(\n          foundNode.arguments[1],\n          fragmentDefs,\n          container.buildPluginInfo(params.pluginConfig)\n        );\n      }\n\n      const seen = new Set<string>();\n      let document: string;\n      if (params.disableNormalization) {\n        document = operation;\n      } else {\n        try {\n          const parsed = parse(operation);\n          const seen = new Set<unknown>();\n          for (const definition of parsed.definitions) {\n            if (definition.kind === Kind.FRAGMENT_DEFINITION && !seen.has(definition)) {\n              stripUnmaskDirectivesFromDefinition(definition);\n            }\n          }\n          document = print(parsed);\n        } catch (_error) {\n          warnings.push({\n            message:\n              `The referenced document of \"${referencingNode.getText()}\" could not be parsed.\\n` +\n              'Run `check` to see specific validation errors.',\n            file: position.fileName,\n            line: position.line,\n            col: position.col,\n          });\n          continue;\n        }\n      }\n\n      // NOTE: Update graphqlsp not to pre-parse fragments, which also swallows errors\n      for (const fragmentDef of fragmentDefs) {\n        stripUnmaskDirectivesFromDefinition(fragmentDef);\n        const printedFragmentDef = print(fragmentDef);\n        if (!seen.has(printedFragmentDef)) {\n          document += '\\n\\n' + print(fragmentDef);\n          seen.add(printedFragmentDef);\n        }\n      }\n\n      documents.push({\n        schemaName: call.schema,\n        hashKey: hashArg.getText().slice(1, -1),\n        document,\n      });\n    }\n\n    yield {\n      kind: 'FILE_PERSISTED',\n      filePath,\n      documents,\n      warnings,\n    };\n  }\n}\n\nexport const runPersisted = expose(_runPersisted);\ntype writable<T> = { -readonly [K in keyof T]: T[K] };\n\nconst stripUnmaskDirectivesFromDefinition = (definition: FragmentDefinitionNode) => {\n  (definition as writable<FragmentDefinitionNode>).directives = definition.directives?.filter(\n    (directive) => directive.name.value !== '_unmask'\n  );\n};\n"], "names": ["e", "runPersisted", "expose", "async", "_runPersisted", "params", "schemaNames", "getSchemaNamesFromConfig", "pluginConfig", "factory", "programFactory", "externalFiles", "createExternalFiles", "length", "kind", "addVirtualFiles", "container", "build", "pluginInfo", "buildPluginInfo", "sourceFiles", "getSourceFiles", "fileCount", "sourceFile", "filePath", "fileName", "documents", "warnings", "calls", "findAllPersistedCallExpressions", "call", "position", "getSourcePosition", "node", "getStart", "has", "schema", "push", "message", "size", "file", "line", "col", "hashArg", "arguments", "doc<PERSON><PERSON>", "typeQuery", "typeArguments", "ts", "isStringLiteral", "foundNode", "referencingNode", "isCallExpression", "isIdentifier", "getDocumentReferenceFromDocumentNode", "isTypeQueryNode", "getDocumentReferenceFromTypeQuery", "getText", "isNoSubstitutionTemplateLiteral", "fragmentDefs", "operation", "slice", "isArrayLiteralExpression", "unrollTadaFragments", "seen", "Set", "document", "disableNormalization", "parsed", "parse", "definition", "definitions", "Kind", "stripUnmaskDirectivesFromDefinition", "print", "_error", "fragmentDef", "printedFragmentDef", "add", "schemaName", "hash<PERSON><PERSON>", "directives", "filter", "directive", "name", "value"], "mappings": ";;;;;;;;;;AAAO,IAAMA,IAUU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICmMVC,IAAeC,EAAMA,QAnLlCC,gBAAgBC,cAAcC;EAC5B,IAAMC,IAAcC,EAAAA,yBAAyBF,EAAOG;EACpD,IAAMC,IAAUC,iBAAeL;EAE/B,IAAMM,IAAgBF,EAAQG;EAC9B,IAAID,EAAcE,QAAQ;UAClB;MAAEC,MAAM;;UACRL,EAAQM,gBAAgBJ;AAChC;EAEA,IAAMK,IAAYP,EAAQQ;EAC1B,IAAMC,IAAaF,EAAUG,gBAAgBd,EAAOG;EACpD,IAAMY,IAAcJ,EAAUK;QAExB;IACJP,MAAM;IACNQ,WAAWF,EAAYP;;EAGzB,KAAK,IAAMU,KAAcH,GAAa;IACpC,IAAII,IAAWD,EAAWE;IAC1B,IAAMC,IAAiC;IACvC,IAAMC,IAA+B;IAErC,IAAMC,IAAQC,EAAAA,gCAAgCN,GAAYL;IAC1D,KAAK,IAAMY,KAAQF,GAAO;MACxB,IAAMG,IAAWf,EAAUgB,kBAAkBT,GAAYO,EAAKG,KAAKC;MACnEV,IAAWO,EAASN;MAEpB,KAAKnB,EAAY6B,IAAIL,EAAKM,SAAS;QACjCT,EAASU,KAAK;UACZC,SAASR,EAAKM,SACV,QAAQN,EAAKM,0FACb9B,EAAYiC,OAAO,IACjB,mFACA;UACNC,MAAMT,EAASN;UACfgB,MAAMV,EAASU;UACfC,KAAKX,EAASW;;QAEhB;AACF;MAEA,IAAMC,IAAUb,EAAKG,KAAKW,UAAU;MACpC,IAAMC,IAASf,EAAKG,KAAKW,UAAU;MACnC,IAAME,IAAYhB,EAAKG,KAAKc,iBAAiBjB,EAAKG,KAAKc,cAAc;MACrE,KAAKJ,MAAYK,EAAGC,gBAAgBN,IAAU;QAC5ChB,EAASU,KAAK;UACZC,SACE;UACFE,MAAMT,EAASN;UACfgB,MAAMV,EAASU;UACfC,KAAKX,EAASW;;QAEhB;AACF,aAAO,KAAKG,MAAWC,GAAW;QAChCnB,EAASU,KAAK;UACZC,SACE;UAEFE,MAAMT,EAASN;UACfgB,MAAMV,EAASU;UACfC,KAAKX,EAASW;;QAEhB;AACF;MAEA,IAAIQ,IAAsC;MAC1C,IAAIC,IAA2BrB,EAAKG;MACpC,IAAIY,MAAWG,EAAGI,iBAAiBP,MAAWG,EAAGK,aAAaR,KAAU;QAMtEK,IALeI,EAAAA,qCACbT,GACAtB,EAAWE,UACXP,GAEiBe;QACnBkB,IAAkBN;AACnB,aAAM,IAAIC,KAAaE,EAAGO,gBAAgBT,IAAY;QAMrDI,IALeM,EAAAA,kCACbV,GACAvB,EAAWE,UACXP,GAEiBe;QACnBkB,IAAkBL;AACpB;MAEA,KAAKI,GAAW;QACdvB,EAASU,KAAK;UACZC,SACE,iCAAiCa,EAAgBM;UAEnDjB,MAAMT,EAASN;UACfgB,MAAMV,EAASU;UACfC,KAAKX,EAASW;;QAEhB;AACF;MAEA,KACGQ,MACAF,EAAGI,iBAAiBF,OACnBF,EAAGU,gCAAgCR,EAAUN,UAAU,QACtDI,EAAGC,gBAAgBC,EAAUN,UAAU,KAC1C;QACAjB,EAASU,KAAK;UACZC,SACE,+BAA+Ba,EAAgBM;UAEjDjB,MAAMT,EAASN;UACfgB,MAAMV,EAASU;UACfC,KAAKX,EAASW;;QAEhB;AACF;MAEA,IAAMiB,IAAyC;MAC/C,IAAMC,IAAYV,EAAUN,UAAU,GAAGa,UAAUI,MAAM,IAAI;MAC7D,IAAIX,EAAUN,UAAU,MAAMI,EAAGc,yBAAyBZ,EAAUN,UAAU;QAC5EmB,EAAAA,oBACEb,EAAUN,UAAU,IACpBe,GACA3C,EAAUG,gBAAgBd,EAAOG;;MAIrC,IAAMwD,IAAO,IAAIC;MACjB,IAAIC,SAAgB;MACpB,IAAI7D,EAAO8D;QACTD,IAAWN;;QAEX;UACE,IAAMQ,IAASC,MAAMT;UACrB,IAAMI,IAAO,IAAIC;UACjB,KAAK,IAAMK,KAAcF,EAAOG;YAC9B,IAAID,EAAWxD,SAAS0D,MAA6BR,EAAK7B,IAAImC;cAC5DG,oCAAoCH;;;UAGxCJ,IAAWQ,MAAMN;AAClB,UAAC,OAAOO;UACPhD,EAASU,KAAK;YACZC,SACE,+BAA+Ba,EAAgBM;YAEjDjB,MAAMT,EAASN;YACfgB,MAAMV,EAASU;YACfC,KAAKX,EAASW;;UAEhB;AACF;;MAIF,KAAK,IAAMkC,KAAejB,GAAc;QACtCc,oCAAoCG;QACpC,IAAMC,IAAqBH,MAAME;QACjC,KAAKZ,EAAK7B,IAAI0C,IAAqB;UACjCX,KAAY,SAASQ,MAAME;UAC3BZ,EAAKc,IAAID;AACX;AACF;MAEAnD,EAAUW,KAAK;QACb0C,YAAYjD,EAAKM;QACjB4C,SAASrC,EAAQc,UAAUI,MAAM,IAAI;QACrCK;;AAEJ;UAEM;MACJpD,MAAM;MACNU;MACAE;MACAC;;AAEJ;AACF;;AAKA,IAAM8C,sCAAuCH;EAC1CA,EAAgDW,aAAaX,EAAWW,YAAYC,QAClFC,KAAuC,cAAzBA,EAAUC,KAAKC;AAC/B;;", "x_google_ignoreList": [0]}