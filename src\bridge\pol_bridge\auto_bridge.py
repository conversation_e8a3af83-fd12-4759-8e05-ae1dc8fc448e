from typing import Dict, Optional, Union, Any
from decimal import Decimal
import asyncio
import logging
import os
import yaml
import random
import time
from .bridge_tokens import PolygonEthereumBridge
import traceback

class AutoBridge:
    """
    自动桥接工具类
    封装了双向自动桥接功能，包括自动监控和claim
    """
    
    def __init__(
        self,
        private_key: Optional[str] = None,
        check_interval: int = 60,
        max_check_time: int = 21600,  # 6小时
        initial_wait: int = 1800,      # 30分钟
    ):
        """
        初始化自动桥接工具
        """
        # 加载RPC配置
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config', 'config.yaml')
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 获取以太坊RPC
        eth_rpc_urls = [config['rpc']['ethereum']['rpc_url']]
        eth_rpc_urls.extend(config['rpc']['ethereum']['backup_rpc_urls'])
        
        # 获取Polygon RPC
        polygon_rpc_urls = [config['rpc']['polygon']['rpc_url']]
        polygon_rpc_urls.extend(config['rpc']['polygon']['backup_rpc_urls'])
        
        # 获取私钥
        if private_key is None:
            private_key = config['wallet']['private_key']
        
        # 添加重试逻辑
        max_retries = 3
        retry_delay = 8  # 5秒
        last_error = None
        
        for retry in range(max_retries):
            # 随机选择RPC节点
            ethereum_rpc = random.choice(eth_rpc_urls)
            polygon_rpc = random.choice(polygon_rpc_urls)
            
            try:
                self.bridge = PolygonEthereumBridge(
                    ethereum_rpc_url=ethereum_rpc,
                    polygon_rpc_url=polygon_rpc,
                    private_key=private_key
                )
                
                # 验证初始化是否成功
                if not hasattr(self.bridge, 'ethereum_account'):
                    raise ValueError("Bridge初始化失败，请检查私钥和RPC配置")
                
                # 验证RPC连接
                try:
                    # 测试以太坊RPC
                    _ = self.bridge.ethereum_web3.eth.block_number
                    # 测试Polygon RPC
                    _ = self.bridge.polygon_web3.eth.block_number
                    break  # 如果都成功了，跳出重试循环
                except Exception as e:
                    raise ValueError(f"RPC连接测试失败: {str(e)}")
                
            except Exception as e:
                last_error = e
                # 从列表中移除失败的RPC
                if ethereum_rpc in eth_rpc_urls:
                    eth_rpc_urls.remove(ethereum_rpc)
                if polygon_rpc in polygon_rpc_urls:
                    polygon_rpc_urls.remove(polygon_rpc)
                
                if not eth_rpc_urls or not polygon_rpc_urls:
                    raise ValueError("所有RPC节点都不可用")
                
                if retry < max_retries - 1:
                    logging.warning(f"Bridge初始化失败 (尝试 {retry + 1}/{max_retries}): {str(e)}")
                    logging.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    raise ValueError(f"Bridge初始化失败，已达到最大重试次数: {str(last_error)}")
        
        self.check_interval = check_interval
        self.max_check_time = max_check_time
        self.initial_wait = initial_wait
        
    async def eth_to_polygon(
        self,
        token_address: str,
        amount: Union[int, float, Decimal],
        direct: bool = True  # 保留参数但不使用，总是等待完成
    ) -> Dict[str, Any]:
        """
        从以太坊桥接到Polygon，自动完成整个过程并等待到账
        失败后会重试3次，重试间隔为指数增长（18-58秒）
        
        Args:
            token_address: 代币地址
            amount: 数量
            direct: 保留参数但不使用，总是等待完成
            
        Returns:
            Dict[str, Any]: 包含交易结果的字典，只在完全成功或完全失败时返回
        """
        max_retries = 3
        base_delay = 18  # 初始延迟18秒
        last_error = None
        
        for retry in range(max_retries):
            try:
                # 执行桥接，获取交易哈希
                bridge_tx = await self.bridge.direct_bridge_to_polygon(
                    token_address=token_address,
                    amount=amount
                )
                
                if not isinstance(bridge_tx, str) or not bridge_tx.startswith("0x"):
                    raise ValueError(str(bridge_tx) if isinstance(bridge_tx, str) else "桥接失败")
                
                # 强制监控到账
                deposit_result = await self.bridge.monitor_polygon_deposit(
                    ethereum_token_address=token_address,
                    receiver_address=self.bridge.get_web3_by_chain_id(1).eth.account.from_key(self.bridge.private_key).address,
                    check_interval=10,
                    timeout_minutes=80,
                    expected_amount=amount,
                    initial_wait_time=900
                )
                
                # 只有两种情况：完全成功或失败
                if deposit_result.get("success"):
                    return {
                        "success": True,
                        "bridge_tx": bridge_tx,
                        "polygon_tx": deposit_result.get("polygon_tx"),
                        "message": "代币已成功到账Polygon"
                    }
                else:
                    return {
                        "success": False,
                        "error": deposit_result.get("error", "桥接失败，请检查交易状态")
                    }
                
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    delay = base_delay * (2 ** retry)  # 18, 36, 58秒
                    print(f"ETH->Polygon 桥接尝试 {retry + 1} 失败: {str(e)}")
                    print(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"桥接过程出错（已重试{max_retries}次）: {str(last_error)}"
                    }

    async def polygon_to_eth(
        self,
        token_address: str,
        amount: Union[int, float, Decimal]
    ) -> Dict[str, Any]:
        """
        从Polygon桥接到以太坊
        
        Args:
            token_address: 代币地址
            amount: 代币数量
            
        Returns:
            Dict: 桥接结果
        """
        max_retries = 3
        base_delay = 18  # 基础延迟时间（秒）
        last_error = None
        
        for retry in range(max_retries):
            try:
                # 直接调用 bridge_tokens.py 中的 polygon_to_ethereum 方法
                result = await self.bridge.polygon_to_ethereum(
                    amount=str(amount),
                    token_address=token_address,
                    wait_for_claim=True  # 等待 claim 完成
                )
                
                # 确保结果是字典类型
                if isinstance(result, dict):
                    if not result.get("success"):
                        error_msg = result.get("error", "未知错误")
                        raise ValueError(error_msg)
                    return result
                else:
                    # 如果是 AttributeDict，转换为普通字典
                    result_dict = dict(result)
                    if not result_dict.get("success"):
                        error_msg = result_dict.get("error", "未知错误")
                        raise ValueError(error_msg)
                    return result_dict
                
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    delay = base_delay * (2 ** retry)  # 18, 36, 72秒
                    print(f"Polygon->ETH 桥接尝试 {retry + 1} 失败: {str(e)}")
                    print(f"等待 {delay} 秒后重试...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    return {
                        "success": False,
                        "error": f"桥接过程出错（已重试{max_retries}次）: {str(last_error)}"
                    }

    def get_token_info(self, token_address: str, chain: str = "ethereum") -> Dict:
        """
        获取代币信息
        
        Args:
            token_address: 代币地址
            chain: 链名称 (ethereum 或 polygon)
            
        Returns:
            Dict: 代币信息
        """
        return self.bridge.get_token_info(token_address=token_address, chain=chain)
        
    def get_mapped_address(self, token_address: str, from_chain: str = "ethereum") -> Optional[str]:
        """
        获取代币在另一条链上的映射地址
        
        Args:
            token_address: 代币地址
            from_chain: 源链名称 (ethereum 或 polygon)
            
        Returns:
            Optional[str]: 映射地址
        """
        if from_chain.lower() == "ethereum":
            return self.bridge.get_polygon_mapped_address(token_address)
        else:
            return self.bridge.get_root_token(token_address) 