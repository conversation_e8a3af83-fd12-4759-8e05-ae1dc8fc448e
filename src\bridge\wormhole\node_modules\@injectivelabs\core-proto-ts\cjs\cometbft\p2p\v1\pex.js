"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = exports.PexAddrs = exports.PexRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var types_1 = require("./types.js");
exports.protobufPackage = "cometbft.p2p.v1";
function createBasePexRequest() {
    return {};
}
exports.PexRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePexRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.PexRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBasePexRequest();
        return message;
    },
};
function createBasePexAddrs() {
    return { addrs: [] };
}
exports.PexAddrs = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.addrs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.NetAddress.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePexAddrs();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.addrs.push(types_1.NetAddress.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { addrs: Array.isArray(object === null || object === void 0 ? void 0 : object.addrs) ? object.addrs.map(function (e) { return types_1.NetAddress.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.addrs) {
            obj.addrs = message.addrs.map(function (e) { return e ? types_1.NetAddress.toJSON(e) : undefined; });
        }
        else {
            obj.addrs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.PexAddrs.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBasePexAddrs();
        message.addrs = ((_a = object.addrs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_1.NetAddress.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMessage() {
    return { pexRequest: undefined, pexAddrs: undefined };
}
exports.Message = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.pexRequest !== undefined) {
            exports.PexRequest.encode(message.pexRequest, writer.uint32(10).fork()).ldelim();
        }
        if (message.pexAddrs !== undefined) {
            exports.PexAddrs.encode(message.pexAddrs, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessage();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pexRequest = exports.PexRequest.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.pexAddrs = exports.PexAddrs.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            pexRequest: isSet(object.pexRequest) ? exports.PexRequest.fromJSON(object.pexRequest) : undefined,
            pexAddrs: isSet(object.pexAddrs) ? exports.PexAddrs.fromJSON(object.pexAddrs) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.pexRequest !== undefined &&
            (obj.pexRequest = message.pexRequest ? exports.PexRequest.toJSON(message.pexRequest) : undefined);
        message.pexAddrs !== undefined && (obj.pexAddrs = message.pexAddrs ? exports.PexAddrs.toJSON(message.pexAddrs) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Message.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseMessage();
        message.pexRequest = (object.pexRequest !== undefined && object.pexRequest !== null)
            ? exports.PexRequest.fromPartial(object.pexRequest)
            : undefined;
        message.pexAddrs = (object.pexAddrs !== undefined && object.pexAddrs !== null)
            ? exports.PexAddrs.fromPartial(object.pexAddrs)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
