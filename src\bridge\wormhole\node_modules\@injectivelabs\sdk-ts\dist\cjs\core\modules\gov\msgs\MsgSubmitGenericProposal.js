"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
/**
 * @category Messages
 */
class MsgSubmitGenericProposal extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgSubmitGenericProposal(params);
    }
    toProto() {
        const { params } = this;
        const depositParams = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        depositParams.denom = params.deposit.denom;
        depositParams.amount = params.deposit.amount;
        const message = core_proto_ts_1.CosmosGovV1Tx.MsgSubmitProposal.create();
        message.messages = params.messages.map((msg) => {
            const contentAny = core_proto_ts_1.GoogleProtobufAny.Any.create();
            contentAny.typeUrl = msg.toDirectSign().type;
            contentAny.value = msg.toBinary();
            return contentAny;
        });
        message.initialDeposit = [depositParams];
        message.proposer = params.proposer;
        message.metadata = params.metadata || '';
        message.title = params.title;
        message.summary = params.summary;
        message.expedited = params.expedited || false;
        return core_proto_ts_1.CosmosGovV1Tx.MsgSubmitProposal.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.gov.v1.MsgSubmitProposal',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const proto = this.toProto();
        const messageWithProposalType = (0, snakecase_keys_1.default)({
            ...proto,
            messages: params.messages.map((msg) => msg.toAmino()),
        });
        return {
            type: 'cosmos-sdk/v1/MsgSubmitProposal',
            value: messageWithProposalType,
        };
    }
    toWeb3Gw() {
        const { params } = this;
        const amino = this.toAmino();
        const messageWithProposalType = {
            ...amino.value,
            messages: params.messages.map((msg) => msg.toWeb3Gw()),
        };
        return {
            '@type': '/cosmos.gov.v1.MsgSubmitProposal',
            ...messageWithProposalType,
        };
    }
    toEip712() {
        const { type, value } = this.toAmino();
        const messageAdjusted = { ...value };
        if (!messageAdjusted.expedited) {
            delete messageAdjusted.expedited;
        }
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.gov.v1.MsgSubmitProposal',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosGovV1Tx.MsgSubmitProposal.encode(this.toProto()).finish();
    }
}
exports.default = MsgSubmitGenericProposal;
