import { grpcPkg } from '../../utils/grpc.js';
import { GrpcWebImpl } from './IndexerGrpcWebImpl.js';
export default class BaseIndexerGrpcConsumer extends GrpcWebImpl {
    protected module: string;
    protected metadata?: grpcPkg.grpc.Metadata;
    constructor(endpoint: string);
    getGrpcWebImpl(endpoint: string): BaseIndexerGrpcConsumer;
    setMetadata(map: Record<string, string>): this;
    clearMetadata(): void;
    protected retry<TResponse>(grpcCall: Function, retries?: number, delay?: number): Promise<TResponse>;
}
