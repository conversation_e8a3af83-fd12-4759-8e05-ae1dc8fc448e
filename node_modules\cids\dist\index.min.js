/*! For license information please see index.min.js.LICENSE */
!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.Cids=n():e.Cids=n()}(window,(function(){return function(e){var n={};function t(i){if(n[i])return n[i].exports;var r=n[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,t),r.l=!0,r.exports}return t.m=e,t.c=n,t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:i})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(t.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)t.d(i,r,function(n){return e[n]}.bind(null,r));return i},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=6)}([function(e,n,t){"use strict";(function(e){var i=t(9),r=t(10),o=t(11);function s(){return k.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function a(e,n){if(s()<n)throw new RangeError("Invalid typed array length");return k.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(n)).__proto__=k.prototype:(null===e&&(e=new k(n)),e.length=n),e}function k(e,n,t){if(!(k.TYPED_ARRAY_SUPPORT||this instanceof k))return new k(e,n,t);if("number"==typeof e){if("string"==typeof n)throw new Error("If encoding is specified then the first argument must be a string");return l(this,e)}return u(this,e,n,t)}function u(e,n,t,i){if("number"==typeof n)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&n instanceof ArrayBuffer?function(e,n,t,i){if(n.byteLength,t<0||n.byteLength<t)throw new RangeError("'offset' is out of bounds");if(n.byteLength<t+(i||0))throw new RangeError("'length' is out of bounds");n=void 0===t&&void 0===i?new Uint8Array(n):void 0===i?new Uint8Array(n,t):new Uint8Array(n,t,i);k.TYPED_ARRAY_SUPPORT?(e=n).__proto__=k.prototype:e=c(e,n);return e}(e,n,t,i):"string"==typeof n?function(e,n,t){"string"==typeof t&&""!==t||(t="utf8");if(!k.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var i=0|b(n,t),r=(e=a(e,i)).write(n,t);r!==i&&(e=e.slice(0,r));return e}(e,n,t):function(e,n){if(k.isBuffer(n)){var t=0|h(n.length);return 0===(e=a(e,t)).length?e:(n.copy(e,0,0,t),e)}if(n){if("undefined"!=typeof ArrayBuffer&&n.buffer instanceof ArrayBuffer||"length"in n)return"number"!=typeof n.length||(i=n.length)!=i?a(e,0):c(e,n);if("Buffer"===n.type&&o(n.data))return c(e,n.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,n)}function f(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function l(e,n){if(f(n),e=a(e,n<0?0:0|h(n)),!k.TYPED_ARRAY_SUPPORT)for(var t=0;t<n;++t)e[t]=0;return e}function c(e,n){var t=n.length<0?0:0|h(n.length);e=a(e,t);for(var i=0;i<t;i+=1)e[i]=255&n[i];return e}function h(e){if(e>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|e}function b(e,n){if(k.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var t=e.length;if(0===t)return 0;for(var i=!1;;)switch(n){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":case void 0:return L(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*t;case"hex":return t>>>1;case"base64":return z(e).length;default:if(i)return L(e).length;n=(""+n).toLowerCase(),i=!0}}function S(e,n,t){var i=!1;if((void 0===n||n<0)&&(n=0),n>this.length)return"";if((void 0===t||t>this.length)&&(t=this.length),t<=0)return"";if((t>>>=0)<=(n>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return P(this,n,t);case"utf8":case"utf-8":return B(this,n,t);case"ascii":return _(this,n,t);case"latin1":case"binary":return R(this,n,t);case"base64":return x(this,n,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,n,t);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function p(e,n,t){var i=e[n];e[n]=e[t],e[t]=i}function d(e,n,t,i,r){if(0===e.length)return-1;if("string"==typeof t?(i=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,isNaN(t)&&(t=r?0:e.length-1),t<0&&(t=e.length+t),t>=e.length){if(r)return-1;t=e.length-1}else if(t<0){if(!r)return-1;t=0}if("string"==typeof n&&(n=k.from(n,i)),k.isBuffer(n))return 0===n.length?-1:g(e,n,t,i,r);if("number"==typeof n)return n&=255,k.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,n,t):Uint8Array.prototype.lastIndexOf.call(e,n,t):g(e,[n],t,i,r);throw new TypeError("val must be string, number or Buffer")}function g(e,n,t,i,r){var o,s=1,a=e.length,k=n.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||n.length<2)return-1;s=2,a/=2,k/=2,t/=2}function u(e,n){return 1===s?e[n]:e.readUInt16BE(n*s)}if(r){var f=-1;for(o=t;o<a;o++)if(u(e,o)===u(n,-1===f?0:o-f)){if(-1===f&&(f=o),o-f+1===k)return f*s}else-1!==f&&(o-=o-f),f=-1}else for(t+k>a&&(t=a-k),o=t;o>=0;o--){for(var l=!0,c=0;c<k;c++)if(u(e,o+c)!==u(n,c)){l=!1;break}if(l)return o}return-1}function w(e,n,t,i){t=Number(t)||0;var r=e.length-t;i?(i=Number(i))>r&&(i=r):i=r;var o=n.length;if(o%2!=0)throw new TypeError("Invalid hex string");i>o/2&&(i=o/2);for(var s=0;s<i;++s){var a=parseInt(n.substr(2*s,2),16);if(isNaN(a))return s;e[t+s]=a}return s}function m(e,n,t,i){return q(L(n,e.length-t),e,t,i)}function y(e,n,t,i){return q(function(e){for(var n=[],t=0;t<e.length;++t)n.push(255&e.charCodeAt(t));return n}(n),e,t,i)}function v(e,n,t,i){return y(e,n,t,i)}function E(e,n,t,i){return q(z(n),e,t,i)}function A(e,n,t,i){return q(function(e,n){for(var t,i,r,o=[],s=0;s<e.length&&!((n-=2)<0);++s)t=e.charCodeAt(s),i=t>>8,r=t%256,o.push(r),o.push(i);return o}(n,e.length-t),e,t,i)}function x(e,n,t){return 0===n&&t===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(n,t))}function B(e,n,t){t=Math.min(e.length,t);for(var i=[],r=n;r<t;){var o,s,a,k,u=e[r],f=null,l=u>239?4:u>223?3:u>191?2:1;if(r+l<=t)switch(l){case 1:u<128&&(f=u);break;case 2:128==(192&(o=e[r+1]))&&(k=(31&u)<<6|63&o)>127&&(f=k);break;case 3:o=e[r+1],s=e[r+2],128==(192&o)&&128==(192&s)&&(k=(15&u)<<12|(63&o)<<6|63&s)>2047&&(k<55296||k>57343)&&(f=k);break;case 4:o=e[r+1],s=e[r+2],a=e[r+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(k=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&k<1114112&&(f=k)}null===f?(f=65533,l=1):f>65535&&(f-=65536,i.push(f>>>10&1023|55296),f=56320|1023&f),i.push(f),r+=l}return function(e){var n=e.length;if(n<=4096)return String.fromCharCode.apply(String,e);var t="",i=0;for(;i<n;)t+=String.fromCharCode.apply(String,e.slice(i,i+=4096));return t}(i)}n.Buffer=k,n.SlowBuffer=function(e){+e!=e&&(e=0);return k.alloc(+e)},n.INSPECT_MAX_BYTES=50,k.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(n){return!1}}(),n.kMaxLength=s(),k.poolSize=8192,k._augment=function(e){return e.__proto__=k.prototype,e},k.from=function(e,n,t){return u(null,e,n,t)},k.TYPED_ARRAY_SUPPORT&&(k.prototype.__proto__=Uint8Array.prototype,k.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&k[Symbol.species]===k&&Object.defineProperty(k,Symbol.species,{value:null,configurable:!0})),k.alloc=function(e,n,t){return function(e,n,t,i){return f(n),n<=0?a(e,n):void 0!==t?"string"==typeof i?a(e,n).fill(t,i):a(e,n).fill(t):a(e,n)}(null,e,n,t)},k.allocUnsafe=function(e){return l(null,e)},k.allocUnsafeSlow=function(e){return l(null,e)},k.isBuffer=function(e){return!(null==e||!e._isBuffer)},k.compare=function(e,n){if(!k.isBuffer(e)||!k.isBuffer(n))throw new TypeError("Arguments must be Buffers");if(e===n)return 0;for(var t=e.length,i=n.length,r=0,o=Math.min(t,i);r<o;++r)if(e[r]!==n[r]){t=e[r],i=n[r];break}return t<i?-1:i<t?1:0},k.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},k.concat=function(e,n){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return k.alloc(0);var t;if(void 0===n)for(n=0,t=0;t<e.length;++t)n+=e[t].length;var i=k.allocUnsafe(n),r=0;for(t=0;t<e.length;++t){var s=e[t];if(!k.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(i,r),r+=s.length}return i},k.byteLength=b,k.prototype._isBuffer=!0,k.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var n=0;n<e;n+=2)p(this,n,n+1);return this},k.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var n=0;n<e;n+=4)p(this,n,n+3),p(this,n+1,n+2);return this},k.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var n=0;n<e;n+=8)p(this,n,n+7),p(this,n+1,n+6),p(this,n+2,n+5),p(this,n+3,n+4);return this},k.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?B(this,0,e):S.apply(this,arguments)},k.prototype.equals=function(e){if(!k.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===k.compare(this,e)},k.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},k.prototype.compare=function(e,n,t,i,r){if(!k.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===n&&(n=0),void 0===t&&(t=e?e.length:0),void 0===i&&(i=0),void 0===r&&(r=this.length),n<0||t>e.length||i<0||r>this.length)throw new RangeError("out of range index");if(i>=r&&n>=t)return 0;if(i>=r)return-1;if(n>=t)return 1;if(this===e)return 0;for(var o=(r>>>=0)-(i>>>=0),s=(t>>>=0)-(n>>>=0),a=Math.min(o,s),u=this.slice(i,r),f=e.slice(n,t),l=0;l<a;++l)if(u[l]!==f[l]){o=u[l],s=f[l];break}return o<s?-1:s<o?1:0},k.prototype.includes=function(e,n,t){return-1!==this.indexOf(e,n,t)},k.prototype.indexOf=function(e,n,t){return d(this,e,n,t,!0)},k.prototype.lastIndexOf=function(e,n,t){return d(this,e,n,t,!1)},k.prototype.write=function(e,n,t,i){if(void 0===n)i="utf8",t=this.length,n=0;else if(void 0===t&&"string"==typeof n)i=n,t=this.length,n=0;else{if(!isFinite(n))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");n|=0,isFinite(t)?(t|=0,void 0===i&&(i="utf8")):(i=t,t=void 0)}var r=this.length-n;if((void 0===t||t>r)&&(t=r),e.length>0&&(t<0||n<0)||n>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var o=!1;;)switch(i){case"hex":return w(this,e,n,t);case"utf8":case"utf-8":return m(this,e,n,t);case"ascii":return y(this,e,n,t);case"latin1":case"binary":return v(this,e,n,t);case"base64":return E(this,e,n,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,n,t);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},k.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function _(e,n,t){var i="";t=Math.min(e.length,t);for(var r=n;r<t;++r)i+=String.fromCharCode(127&e[r]);return i}function R(e,n,t){var i="";t=Math.min(e.length,t);for(var r=n;r<t;++r)i+=String.fromCharCode(e[r]);return i}function P(e,n,t){var i=e.length;(!n||n<0)&&(n=0),(!t||t<0||t>i)&&(t=i);for(var r="",o=n;o<t;++o)r+=N(e[o]);return r}function C(e,n,t){for(var i=e.slice(n,t),r="",o=0;o<i.length;o+=2)r+=String.fromCharCode(i[o]+256*i[o+1]);return r}function T(e,n,t){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+n>t)throw new RangeError("Trying to access beyond buffer length")}function O(e,n,t,i,r,o){if(!k.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(n>r||n<o)throw new RangeError('"value" argument is out of bounds');if(t+i>e.length)throw new RangeError("Index out of range")}function U(e,n,t,i){n<0&&(n=65535+n+1);for(var r=0,o=Math.min(e.length-t,2);r<o;++r)e[t+r]=(n&255<<8*(i?r:1-r))>>>8*(i?r:1-r)}function I(e,n,t,i){n<0&&(n=4294967295+n+1);for(var r=0,o=Math.min(e.length-t,4);r<o;++r)e[t+r]=n>>>8*(i?r:3-r)&255}function j(e,n,t,i,r,o){if(t+i>e.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function D(e,n,t,i,o){return o||j(e,0,t,4),r.write(e,n,t,i,23,4),t+4}function M(e,n,t,i,o){return o||j(e,0,t,8),r.write(e,n,t,i,52,8),t+8}k.prototype.slice=function(e,n){var t,i=this.length;if((e=~~e)<0?(e+=i)<0&&(e=0):e>i&&(e=i),(n=void 0===n?i:~~n)<0?(n+=i)<0&&(n=0):n>i&&(n=i),n<e&&(n=e),k.TYPED_ARRAY_SUPPORT)(t=this.subarray(e,n)).__proto__=k.prototype;else{var r=n-e;t=new k(r,void 0);for(var o=0;o<r;++o)t[o]=this[o+e]}return t},k.prototype.readUIntLE=function(e,n,t){e|=0,n|=0,t||T(e,n,this.length);for(var i=this[e],r=1,o=0;++o<n&&(r*=256);)i+=this[e+o]*r;return i},k.prototype.readUIntBE=function(e,n,t){e|=0,n|=0,t||T(e,n,this.length);for(var i=this[e+--n],r=1;n>0&&(r*=256);)i+=this[e+--n]*r;return i},k.prototype.readUInt8=function(e,n){return n||T(e,1,this.length),this[e]},k.prototype.readUInt16LE=function(e,n){return n||T(e,2,this.length),this[e]|this[e+1]<<8},k.prototype.readUInt16BE=function(e,n){return n||T(e,2,this.length),this[e]<<8|this[e+1]},k.prototype.readUInt32LE=function(e,n){return n||T(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},k.prototype.readUInt32BE=function(e,n){return n||T(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},k.prototype.readIntLE=function(e,n,t){e|=0,n|=0,t||T(e,n,this.length);for(var i=this[e],r=1,o=0;++o<n&&(r*=256);)i+=this[e+o]*r;return i>=(r*=128)&&(i-=Math.pow(2,8*n)),i},k.prototype.readIntBE=function(e,n,t){e|=0,n|=0,t||T(e,n,this.length);for(var i=n,r=1,o=this[e+--i];i>0&&(r*=256);)o+=this[e+--i]*r;return o>=(r*=128)&&(o-=Math.pow(2,8*n)),o},k.prototype.readInt8=function(e,n){return n||T(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},k.prototype.readInt16LE=function(e,n){n||T(e,2,this.length);var t=this[e]|this[e+1]<<8;return 32768&t?4294901760|t:t},k.prototype.readInt16BE=function(e,n){n||T(e,2,this.length);var t=this[e+1]|this[e]<<8;return 32768&t?4294901760|t:t},k.prototype.readInt32LE=function(e,n){return n||T(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},k.prototype.readInt32BE=function(e,n){return n||T(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},k.prototype.readFloatLE=function(e,n){return n||T(e,4,this.length),r.read(this,e,!0,23,4)},k.prototype.readFloatBE=function(e,n){return n||T(e,4,this.length),r.read(this,e,!1,23,4)},k.prototype.readDoubleLE=function(e,n){return n||T(e,8,this.length),r.read(this,e,!0,52,8)},k.prototype.readDoubleBE=function(e,n){return n||T(e,8,this.length),r.read(this,e,!1,52,8)},k.prototype.writeUIntLE=function(e,n,t,i){(e=+e,n|=0,t|=0,i)||O(this,e,n,t,Math.pow(2,8*t)-1,0);var r=1,o=0;for(this[n]=255&e;++o<t&&(r*=256);)this[n+o]=e/r&255;return n+t},k.prototype.writeUIntBE=function(e,n,t,i){(e=+e,n|=0,t|=0,i)||O(this,e,n,t,Math.pow(2,8*t)-1,0);var r=t-1,o=1;for(this[n+r]=255&e;--r>=0&&(o*=256);)this[n+r]=e/o&255;return n+t},k.prototype.writeUInt8=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,1,255,0),k.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[n]=255&e,n+1},k.prototype.writeUInt16LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,65535,0),k.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8):U(this,e,n,!0),n+2},k.prototype.writeUInt16BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,65535,0),k.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=255&e):U(this,e,n,!1),n+2},k.prototype.writeUInt32LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,4294967295,0),k.TYPED_ARRAY_SUPPORT?(this[n+3]=e>>>24,this[n+2]=e>>>16,this[n+1]=e>>>8,this[n]=255&e):I(this,e,n,!0),n+4},k.prototype.writeUInt32BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,4294967295,0),k.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=255&e):I(this,e,n,!1),n+4},k.prototype.writeIntLE=function(e,n,t,i){if(e=+e,n|=0,!i){var r=Math.pow(2,8*t-1);O(this,e,n,t,r-1,-r)}var o=0,s=1,a=0;for(this[n]=255&e;++o<t&&(s*=256);)e<0&&0===a&&0!==this[n+o-1]&&(a=1),this[n+o]=(e/s>>0)-a&255;return n+t},k.prototype.writeIntBE=function(e,n,t,i){if(e=+e,n|=0,!i){var r=Math.pow(2,8*t-1);O(this,e,n,t,r-1,-r)}var o=t-1,s=1,a=0;for(this[n+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[n+o+1]&&(a=1),this[n+o]=(e/s>>0)-a&255;return n+t},k.prototype.writeInt8=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,1,127,-128),k.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[n]=255&e,n+1},k.prototype.writeInt16LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,32767,-32768),k.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8):U(this,e,n,!0),n+2},k.prototype.writeInt16BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,32767,-32768),k.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=255&e):U(this,e,n,!1),n+2},k.prototype.writeInt32LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,2147483647,-2147483648),k.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8,this[n+2]=e>>>16,this[n+3]=e>>>24):I(this,e,n,!0),n+4},k.prototype.writeInt32BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),k.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=255&e):I(this,e,n,!1),n+4},k.prototype.writeFloatLE=function(e,n,t){return D(this,e,n,!0,t)},k.prototype.writeFloatBE=function(e,n,t){return D(this,e,n,!1,t)},k.prototype.writeDoubleLE=function(e,n,t){return M(this,e,n,!0,t)},k.prototype.writeDoubleBE=function(e,n,t){return M(this,e,n,!1,t)},k.prototype.copy=function(e,n,t,i){if(t||(t=0),i||0===i||(i=this.length),n>=e.length&&(n=e.length),n||(n=0),i>0&&i<t&&(i=t),i===t)return 0;if(0===e.length||0===this.length)return 0;if(n<0)throw new RangeError("targetStart out of bounds");if(t<0||t>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-n<i-t&&(i=e.length-n+t);var r,o=i-t;if(this===e&&t<n&&n<i)for(r=o-1;r>=0;--r)e[r+n]=this[r+t];else if(o<1e3||!k.TYPED_ARRAY_SUPPORT)for(r=0;r<o;++r)e[r+n]=this[r+t];else Uint8Array.prototype.set.call(e,this.subarray(t,t+o),n);return o},k.prototype.fill=function(e,n,t,i){if("string"==typeof e){if("string"==typeof n?(i=n,n=0,t=this.length):"string"==typeof t&&(i=t,t=this.length),1===e.length){var r=e.charCodeAt(0);r<256&&(e=r)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!k.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(n<0||this.length<n||this.length<t)throw new RangeError("Out of range index");if(t<=n)return this;var o;if(n>>>=0,t=void 0===t?this.length:t>>>0,e||(e=0),"number"==typeof e)for(o=n;o<t;++o)this[o]=e;else{var s=k.isBuffer(e)?e:L(new k(e,i).toString()),a=s.length;for(o=0;o<t-n;++o)this[o+n]=s[o%a]}return this};var Y=/[^+\/0-9A-Za-z-_]/g;function N(e){return e<16?"0"+e.toString(16):e.toString(16)}function L(e,n){var t;n=n||1/0;for(var i=e.length,r=null,o=[],s=0;s<i;++s){if((t=e.charCodeAt(s))>55295&&t<57344){if(!r){if(t>56319){(n-=3)>-1&&o.push(239,191,189);continue}if(s+1===i){(n-=3)>-1&&o.push(239,191,189);continue}r=t;continue}if(t<56320){(n-=3)>-1&&o.push(239,191,189),r=t;continue}t=65536+(r-55296<<10|t-56320)}else r&&(n-=3)>-1&&o.push(239,191,189);if(r=null,t<128){if((n-=1)<0)break;o.push(t)}else if(t<2048){if((n-=2)<0)break;o.push(t>>6|192,63&t|128)}else if(t<65536){if((n-=3)<0)break;o.push(t>>12|224,t>>6&63|128,63&t|128)}else{if(!(t<1114112))throw new Error("Invalid code point");if((n-=4)<0)break;o.push(t>>18|240,t>>12&63|128,t>>6&63|128,63&t|128)}}return o}function z(e){return i.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(Y,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function q(e,n,t,i){for(var r=0;r<i&&!(r+t>=n.length||r>=e.length);++r)n[r+t]=e[r];return r}}).call(this,t(8))},function(e){e.exports=JSON.parse('{"identity":0,"ip4":4,"tcp":6,"sha1":17,"sha2-256":18,"sha2-512":19,"sha3-512":20,"sha3-384":21,"sha3-256":22,"sha3-224":23,"shake-128":24,"shake-256":25,"keccak-224":26,"keccak-256":27,"keccak-384":28,"keccak-512":29,"dccp":33,"murmur3-128":34,"murmur3-32":35,"ip6":41,"ip6zone":42,"path":47,"multicodec":48,"multihash":49,"multiaddr":50,"multibase":51,"dns":53,"dns4":54,"dns6":55,"dnsaddr":56,"protobuf":80,"cbor":81,"raw":85,"dbl-sha2-256":86,"rlp":96,"bencode":99,"dag-pb":112,"dag-cbor":113,"libp2p-key":114,"git-raw":120,"torrent-info":123,"torrent-file":124,"leofcoin-block":129,"leofcoin-tx":130,"leofcoin-pr":131,"sctp":132,"eth-block":144,"eth-block-list":145,"eth-tx-trie":146,"eth-tx":147,"eth-tx-receipt-trie":148,"eth-tx-receipt":149,"eth-state-trie":150,"eth-account-snapshot":151,"eth-storage-trie":152,"bitcoin-block":176,"bitcoin-tx":177,"zcash-block":192,"zcash-tx":193,"stellar-block":208,"stellar-tx":209,"md4":212,"md5":213,"bmt":214,"decred-block":224,"decred-tx":225,"ipld-ns":226,"ipfs-ns":227,"swarm-ns":228,"ipns-ns":229,"zeronet":230,"ed25519-pub":237,"dash-block":240,"dash-tx":241,"swarm-manifest":250,"swarm-feed":251,"udp":273,"p2p-webrtc-star":275,"p2p-webrtc-direct":276,"p2p-stardust":277,"p2p-circuit":290,"dag-json":297,"udt":301,"utp":302,"unix":400,"p2p":421,"ipfs":421,"https":443,"onion":444,"onion3":445,"garlic64":446,"garlic32":447,"tls":448,"quic":460,"ws":477,"wss":478,"p2p-websocket-star":479,"http":480,"json":512,"messagepack":513,"x11":4352,"blake2b-8":45569,"blake2b-16":45570,"blake2b-24":45571,"blake2b-32":45572,"blake2b-40":45573,"blake2b-48":45574,"blake2b-56":45575,"blake2b-64":45576,"blake2b-72":45577,"blake2b-80":45578,"blake2b-88":45579,"blake2b-96":45580,"blake2b-104":45581,"blake2b-112":45582,"blake2b-120":45583,"blake2b-128":45584,"blake2b-136":45585,"blake2b-144":45586,"blake2b-152":45587,"blake2b-160":45588,"blake2b-168":45589,"blake2b-176":45590,"blake2b-184":45591,"blake2b-192":45592,"blake2b-200":45593,"blake2b-208":45594,"blake2b-216":45595,"blake2b-224":45596,"blake2b-232":45597,"blake2b-240":45598,"blake2b-248":45599,"blake2b-256":45600,"blake2b-264":45601,"blake2b-272":45602,"blake2b-280":45603,"blake2b-288":45604,"blake2b-296":45605,"blake2b-304":45606,"blake2b-312":45607,"blake2b-320":45608,"blake2b-328":45609,"blake2b-336":45610,"blake2b-344":45611,"blake2b-352":45612,"blake2b-360":45613,"blake2b-368":45614,"blake2b-376":45615,"blake2b-384":45616,"blake2b-392":45617,"blake2b-400":45618,"blake2b-408":45619,"blake2b-416":45620,"blake2b-424":45621,"blake2b-432":45622,"blake2b-440":45623,"blake2b-448":45624,"blake2b-456":45625,"blake2b-464":45626,"blake2b-472":45627,"blake2b-480":45628,"blake2b-488":45629,"blake2b-496":45630,"blake2b-504":45631,"blake2b-512":45632,"blake2s-8":45633,"blake2s-16":45634,"blake2s-24":45635,"blake2s-32":45636,"blake2s-40":45637,"blake2s-48":45638,"blake2s-56":45639,"blake2s-64":45640,"blake2s-72":45641,"blake2s-80":45642,"blake2s-88":45643,"blake2s-96":45644,"blake2s-104":45645,"blake2s-112":45646,"blake2s-120":45647,"blake2s-128":45648,"blake2s-136":45649,"blake2s-144":45650,"blake2s-152":45651,"blake2s-160":45652,"blake2s-168":45653,"blake2s-176":45654,"blake2s-184":45655,"blake2s-192":45656,"blake2s-200":45657,"blake2s-208":45658,"blake2s-216":45659,"blake2s-224":45660,"blake2s-232":45661,"blake2s-240":45662,"blake2s-248":45663,"blake2s-256":45664,"skein256-8":45825,"skein256-16":45826,"skein256-24":45827,"skein256-32":45828,"skein256-40":45829,"skein256-48":45830,"skein256-56":45831,"skein256-64":45832,"skein256-72":45833,"skein256-80":45834,"skein256-88":45835,"skein256-96":45836,"skein256-104":45837,"skein256-112":45838,"skein256-120":45839,"skein256-128":45840,"skein256-136":45841,"skein256-144":45842,"skein256-152":45843,"skein256-160":45844,"skein256-168":45845,"skein256-176":45846,"skein256-184":45847,"skein256-192":45848,"skein256-200":45849,"skein256-208":45850,"skein256-216":45851,"skein256-224":45852,"skein256-232":45853,"skein256-240":45854,"skein256-248":45855,"skein256-256":45856,"skein512-8":45857,"skein512-16":45858,"skein512-24":45859,"skein512-32":45860,"skein512-40":45861,"skein512-48":45862,"skein512-56":45863,"skein512-64":45864,"skein512-72":45865,"skein512-80":45866,"skein512-88":45867,"skein512-96":45868,"skein512-104":45869,"skein512-112":45870,"skein512-120":45871,"skein512-128":45872,"skein512-136":45873,"skein512-144":45874,"skein512-152":45875,"skein512-160":45876,"skein512-168":45877,"skein512-176":45878,"skein512-184":45879,"skein512-192":45880,"skein512-200":45881,"skein512-208":45882,"skein512-216":45883,"skein512-224":45884,"skein512-232":45885,"skein512-240":45886,"skein512-248":45887,"skein512-256":45888,"skein512-264":45889,"skein512-272":45890,"skein512-280":45891,"skein512-288":45892,"skein512-296":45893,"skein512-304":45894,"skein512-312":45895,"skein512-320":45896,"skein512-328":45897,"skein512-336":45898,"skein512-344":45899,"skein512-352":45900,"skein512-360":45901,"skein512-368":45902,"skein512-376":45903,"skein512-384":45904,"skein512-392":45905,"skein512-400":45906,"skein512-408":45907,"skein512-416":45908,"skein512-424":45909,"skein512-432":45910,"skein512-440":45911,"skein512-448":45912,"skein512-456":45913,"skein512-464":45914,"skein512-472":45915,"skein512-480":45916,"skein512-488":45917,"skein512-496":45918,"skein512-504":45919,"skein512-512":45920,"skein1024-8":45921,"skein1024-16":45922,"skein1024-24":45923,"skein1024-32":45924,"skein1024-40":45925,"skein1024-48":45926,"skein1024-56":45927,"skein1024-64":45928,"skein1024-72":45929,"skein1024-80":45930,"skein1024-88":45931,"skein1024-96":45932,"skein1024-104":45933,"skein1024-112":45934,"skein1024-120":45935,"skein1024-128":45936,"skein1024-136":45937,"skein1024-144":45938,"skein1024-152":45939,"skein1024-160":45940,"skein1024-168":45941,"skein1024-176":45942,"skein1024-184":45943,"skein1024-192":45944,"skein1024-200":45945,"skein1024-208":45946,"skein1024-216":45947,"skein1024-224":45948,"skein1024-232":45949,"skein1024-240":45950,"skein1024-248":45951,"skein1024-256":45952,"skein1024-264":45953,"skein1024-272":45954,"skein1024-280":45955,"skein1024-288":45956,"skein1024-296":45957,"skein1024-304":45958,"skein1024-312":45959,"skein1024-320":45960,"skein1024-328":45961,"skein1024-336":45962,"skein1024-344":45963,"skein1024-352":45964,"skein1024-360":45965,"skein1024-368":45966,"skein1024-376":45967,"skein1024-384":45968,"skein1024-392":45969,"skein1024-400":45970,"skein1024-408":45971,"skein1024-416":45972,"skein1024-424":45973,"skein1024-432":45974,"skein1024-440":45975,"skein1024-448":45976,"skein1024-456":45977,"skein1024-464":45978,"skein1024-472":45979,"skein1024-480":45980,"skein1024-488":45981,"skein1024-496":45982,"skein1024-504":45983,"skein1024-512":45984,"skein1024-520":45985,"skein1024-528":45986,"skein1024-536":45987,"skein1024-544":45988,"skein1024-552":45989,"skein1024-560":45990,"skein1024-568":45991,"skein1024-576":45992,"skein1024-584":45993,"skein1024-592":45994,"skein1024-600":45995,"skein1024-608":45996,"skein1024-616":45997,"skein1024-624":45998,"skein1024-632":45999,"skein1024-640":46000,"skein1024-648":46001,"skein1024-656":46002,"skein1024-664":46003,"skein1024-672":46004,"skein1024-680":46005,"skein1024-688":46006,"skein1024-696":46007,"skein1024-704":46008,"skein1024-712":46009,"skein1024-720":46010,"skein1024-728":46011,"skein1024-736":46012,"skein1024-744":46013,"skein1024-752":46014,"skein1024-760":46015,"skein1024-768":46016,"skein1024-776":46017,"skein1024-784":46018,"skein1024-792":46019,"skein1024-800":46020,"skein1024-808":46021,"skein1024-816":46022,"skein1024-824":46023,"skein1024-832":46024,"skein1024-840":46025,"skein1024-848":46026,"skein1024-856":46027,"skein1024-864":46028,"skein1024-872":46029,"skein1024-880":46030,"skein1024-888":46031,"skein1024-896":46032,"skein1024-904":46033,"skein1024-912":46034,"skein1024-920":46035,"skein1024-928":46036,"skein1024-936":46037,"skein1024-944":46038,"skein1024-952":46039,"skein1024-960":46040,"skein1024-968":46041,"skein1024-976":46042,"skein1024-984":46043,"skein1024-992":46044,"skein1024-1000":46045,"skein1024-1008":46046,"skein1024-1016":46047,"skein1024-1024":46048,"holochain-adr-v0":8417572,"holochain-adr-v1":8483108,"holochain-key-v0":9728292,"holochain-key-v1":9793828,"holochain-sig-v0":10645796,"holochain-sig-v1":10711332}')},function(e,n,t){"use strict";e.exports={encode:t(15),decode:t(16),encodingLength:t(17)}},function(e,n,t){"use strict";(function(e){const i=t(12),r=t(14);n.names=r.names,n.codes=r.codes,n.defaultLengths=r.defaultLengths;const o=t(2);function s(e){n.decode(e)}n.toHexString=function(n){if(!e.isBuffer(n))throw new Error("must be passed a buffer");return n.toString("hex")},n.fromHexString=function(n){return e.from(n,"hex")},n.toB58String=function(n){if(!e.isBuffer(n))throw new Error("must be passed a buffer");return i.encode(n)},n.fromB58String=function(n){let t=n;return e.isBuffer(n)&&(t=n.toString()),e.from(i.decode(t))},n.decode=function(t){if(!e.isBuffer(t))throw new Error("multihash must be a Buffer");if(t.length<3)throw new Error("multihash too short. must be > 3 bytes.");const i=o.decode(t);if(!n.isValidCode(i))throw new Error("multihash unknown function code: 0x".concat(i.toString(16)));t=t.slice(o.decode.bytes);const s=o.decode(t);if(s<1)throw new Error("multihash invalid length: 0x".concat(s.toString(16)));if((t=t.slice(o.decode.bytes)).length!==s)throw new Error("multihash length inconsistent: 0x".concat(t.toString("hex")));return{code:i,name:r.codes[i],length:s,digest:t}},n.encode=function(t,i,r){if(!t||void 0===i)throw new Error("multihash encode requires at least two args: digest, code");const s=n.coerceCode(i);if(!e.isBuffer(t))throw new Error("digest should be a Buffer");if(null==r&&(r=t.length),r&&t.length!==r)throw new Error("digest length should be equal to specified length.");return e.concat([e.from(o.encode(s)),e.from(o.encode(r)),t])},n.coerceCode=function(e){let t=e;if("string"==typeof e){if(void 0===r.names[e])throw new Error("Unrecognized hash function named: ".concat(e));t=r.names[e]}if("number"!=typeof t)throw new Error("Hash function code should be a number. Got: ".concat(t));if(void 0===r.codes[t]&&!n.isAppCode(t))throw new Error("Unrecognized function code: ".concat(t));return t},n.isAppCode=function(e){return e>0&&e<16},n.isValidCode=function(e){return!!n.isAppCode(e)||!!r.codes[e]},n.validate=s,n.prefix=function(e){return s(e),e.slice(0,2)}}).call(this,t(0).Buffer)},function(e,n,t){"use strict";var i=t(13).Buffer;e.exports=function(e){for(var n={},t=e.length,r=e.charAt(0),o=0;o<e.length;o++){var s=e.charAt(o);if(void 0!==n[s])throw new TypeError(s+" is ambiguous");n[s]=o}function a(e){if("string"!=typeof e)throw new TypeError("Expected String");if(0===e.length)return i.allocUnsafe(0);for(var o=[0],s=0;s<e.length;s++){var a=n[e[s]];if(void 0===a)return;for(var k=0,u=a;k<o.length;++k)u+=o[k]*t,o[k]=255&u,u>>=8;for(;u>0;)o.push(255&u),u>>=8}for(var f=0;e[f]===r&&f<e.length-1;++f)o.push(0);return i.from(o.reverse())}return{encode:function(n){if(0===n.length)return"";for(var i=[0],o=0;o<n.length;++o){for(var s=0,a=n[o];s<i.length;++s)a+=i[s]<<8,i[s]=a%t,a=a/t|0;for(;a>0;)i.push(a%t),a=a/t|0}for(var k="",u=0;0===n[u]&&u<n.length-1;++u)k+=r;for(var f=i.length-1;f>=0;--f)k+=e[i[f]];return k},decodeUnsafe:a,decode:function(e){var n=a(e);if(n)return n;throw new Error("Non-base"+t+" character")}}}},function(e,n,t){"use strict";(function(n){const i=t(2);function r(e){return parseInt(e.toString("hex"),16)}function o(e){let t=e.toString(16);return t.length%2==1&&(t="0"+t),n.from(t,"hex")}e.exports={numberToBuffer:o,bufferToNumber:r,varintBufferEncode:function(e){return n.from(i.encode(r(e)))},varintBufferDecode:function(e){return o(i.decode(e))},varintEncode:function(e){return n.from(i.encode(e))}}}).call(this,t(0).Buffer)},function(e,n,t){e.exports=t(7)},function(e,n,t){"use strict";const{Buffer:i}=t(0),r=t(3),o=t(18),s=t(24),a=t(1),k=t(29);class u{constructor(e,n,t,a){if(f.isCID(e)){const n=e;return this.version=n.version,this.codec=n.codec,this.multihash=i.from(n.multihash),void(this.multibaseName=n.multibaseName||(0===n.version?"base58btc":"base32"))}if("string"==typeof e){const n=o.isEncoded(e);if(n){const t=o.decode(e);this.version=parseInt(t.slice(0,1).toString("hex"),16),this.codec=s.getCodec(t.slice(1)),this.multihash=s.rmPrefix(t.slice(1)),this.multibaseName=n}else this.version=0,this.codec="dag-pb",this.multihash=r.fromB58String(e),this.multibaseName="base58btc";return u.validateCID(this),void Object.defineProperty(this,"string",{value:e})}if(i.isBuffer(e)){const n=e.slice(0,1),t=parseInt(n.toString("hex"),16);if(1===t){const n=e;this.version=t,this.codec=s.getCodec(n.slice(1)),this.multihash=s.rmPrefix(n.slice(1)),this.multibaseName="base32"}else this.version=0,this.codec="dag-pb",this.multihash=e,this.multibaseName="base58btc";u.validateCID(this)}else this.version=e,this.codec=n,this.multihash=t,this.multibaseName=a||(0===e?"base58btc":"base32"),u.validateCID(this)}get buffer(){let e=this._buffer;if(!e){if(0===this.version)e=this.multihash;else{if(1!==this.version)throw new Error("unsupported version");e=i.concat([i.from("01","hex"),s.getCodeVarint(this.codec),this.multihash])}Object.defineProperty(this,"_buffer",{value:e})}return e}get prefix(){return i.concat([i.from("0".concat(this.version),"hex"),s.getCodeVarint(this.codec),r.prefix(this.multihash)])}toV0(){if("dag-pb"!==this.codec)throw new Error("Cannot convert a non dag-pb CID to CIDv0");const{name:e,length:n}=r.decode(this.multihash);if("sha2-256"!==e)throw new Error("Cannot convert non sha2-256 multihash CID to CIDv0");if(32!==n)throw new Error("Cannot convert non 32 byte multihash CID to CIDv0");return new f(0,this.codec,this.multihash)}toV1(){return new f(1,this.codec,this.multihash)}toBaseEncodedString(e=this.multibaseName){if(this.string&&e===this.multibaseName)return this.string;let n=null;if(0===this.version){if("base58btc"!==e)throw new Error("not supported with CIDv0, to support different bases, please migrate the instance do CIDv1, you can do that through cid.toV1()");n=r.toB58String(this.multihash)}else{if(1!==this.version)throw new Error("unsupported version");n=o.encode(e,this.buffer).toString()}return e===this.multibaseName&&Object.defineProperty(this,"string",{value:n}),n}[Symbol.for("nodejs.util.inspect.custom")](){return"CID("+this.toString()+")"}toString(e){return this.toBaseEncodedString(e)}toJSON(){return{codec:this.codec,version:this.version,hash:this.multihash}}equals(e){return this.codec===e.codec&&this.version===e.version&&this.multihash.equals(e.multihash)}static validateCID(e){const n=k.checkCIDComponents(e);if(n)throw new Error(n)}}const f=t(30)(u,{className:"CID",symbolName:"@ipld/js-cid/CID"});f.codecs=a,e.exports=f},function(e,n,t){"use strict";var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(r){"object"==typeof window&&(i=window)}e.exports=i},function(e,n,t){"use strict";n.byteLength=function(e){var n=u(e),t=n[0],i=n[1];return 3*(t+i)/4-i},n.toByteArray=function(e){var n,t,i=u(e),s=i[0],a=i[1],k=new o(function(e,n,t){return 3*(n+t)/4-t}(0,s,a)),f=0,l=a>0?s-4:s;for(t=0;t<l;t+=4)n=r[e.charCodeAt(t)]<<18|r[e.charCodeAt(t+1)]<<12|r[e.charCodeAt(t+2)]<<6|r[e.charCodeAt(t+3)],k[f++]=n>>16&255,k[f++]=n>>8&255,k[f++]=255&n;2===a&&(n=r[e.charCodeAt(t)]<<2|r[e.charCodeAt(t+1)]>>4,k[f++]=255&n);1===a&&(n=r[e.charCodeAt(t)]<<10|r[e.charCodeAt(t+1)]<<4|r[e.charCodeAt(t+2)]>>2,k[f++]=n>>8&255,k[f++]=255&n);return k},n.fromByteArray=function(e){for(var n,t=e.length,r=t%3,o=[],s=0,a=t-r;s<a;s+=16383)o.push(f(e,s,s+16383>a?a:s+16383));1===r?(n=e[t-1],o.push(i[n>>2]+i[n<<4&63]+"==")):2===r&&(n=(e[t-2]<<8)+e[t-1],o.push(i[n>>10]+i[n>>4&63]+i[n<<2&63]+"="));return o.join("")};for(var i=[],r=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,k=s.length;a<k;++a)i[a]=s[a],r[s.charCodeAt(a)]=a;function u(e){var n=e.length;if(n%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=e.indexOf("=");return-1===t&&(t=n),[t,t===n?0:4-t%4]}function f(e,n,t){for(var r,o,s=[],a=n;a<t;a+=3)r=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(i[(o=r)>>18&63]+i[o>>12&63]+i[o>>6&63]+i[63&o]);return s.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},function(e,n,t){"use strict";n.read=function(e,n,t,i,r){var o,s,a=8*r-i-1,k=(1<<a)-1,u=k>>1,f=-7,l=t?r-1:0,c=t?-1:1,h=e[n+l];for(l+=c,o=h&(1<<-f)-1,h>>=-f,f+=a;f>0;o=256*o+e[n+l],l+=c,f-=8);for(s=o&(1<<-f)-1,o>>=-f,f+=i;f>0;s=256*s+e[n+l],l+=c,f-=8);if(0===o)o=1-u;else{if(o===k)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,i),o-=u}return(h?-1:1)*s*Math.pow(2,o-i)},n.write=function(e,n,t,i,r,o){var s,a,k,u=8*o-r-1,f=(1<<u)-1,l=f>>1,c=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,h=i?0:o-1,b=i?1:-1,S=n<0||0===n&&1/n<0?1:0;for(n=Math.abs(n),isNaN(n)||n===1/0?(a=isNaN(n)?1:0,s=f):(s=Math.floor(Math.log(n)/Math.LN2),n*(k=Math.pow(2,-s))<1&&(s--,k*=2),(n+=s+l>=1?c/k:c*Math.pow(2,1-l))*k>=2&&(s++,k/=2),s+l>=f?(a=0,s=f):s+l>=1?(a=(n*k-1)*Math.pow(2,r),s+=l):(a=n*Math.pow(2,l-1)*Math.pow(2,r),s=0));r>=8;e[t+h]=255&a,h+=b,a/=256,r-=8);for(s=s<<r|a,u+=r;u>0;e[t+h]=255&s,h+=b,s/=256,u-=8);e[t+h-b]|=128*S}},function(e,n,t){"use strict";var i={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==i.call(e)}},function(e,n,t){"use strict";var i=t(4);e.exports=i("**********************************************************")},function(e,n,t){"use strict";var i=t(0),r=i.Buffer;function o(e,n){for(var t in e)n[t]=e[t]}function s(e,n,t){return r(e,n,t)}r.from&&r.alloc&&r.allocUnsafe&&r.allocUnsafeSlow?e.exports=i:(o(i,n),n.Buffer=s),s.prototype=Object.create(r.prototype),o(r,s),s.from=function(e,n,t){if("number"==typeof e)throw new TypeError("Argument must not be a number");return r(e,n,t)},s.alloc=function(e,n,t){if("number"!=typeof e)throw new TypeError("Argument must be a number");var i=r(e);return void 0!==n?"string"==typeof t?i.fill(n,t):i.fill(n):i.fill(0),i},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i.SlowBuffer(e)}},function(e,n,t){"use strict";n.names=Object.freeze({identity:0,sha1:17,"sha2-256":18,"sha2-512":19,"dbl-sha2-256":86,"sha3-224":23,"sha3-256":22,"sha3-384":21,"sha3-512":20,"shake-128":24,"shake-256":25,"keccak-224":26,"keccak-256":27,"keccak-384":28,"keccak-512":29,"murmur3-128":34,"murmur3-32":35,"blake2b-8":45569,"blake2b-16":45570,"blake2b-24":45571,"blake2b-32":45572,"blake2b-40":45573,"blake2b-48":45574,"blake2b-56":45575,"blake2b-64":45576,"blake2b-72":45577,"blake2b-80":45578,"blake2b-88":45579,"blake2b-96":45580,"blake2b-104":45581,"blake2b-112":45582,"blake2b-120":45583,"blake2b-128":45584,"blake2b-136":45585,"blake2b-144":45586,"blake2b-152":45587,"blake2b-160":45588,"blake2b-168":45589,"blake2b-176":45590,"blake2b-184":45591,"blake2b-192":45592,"blake2b-200":45593,"blake2b-208":45594,"blake2b-216":45595,"blake2b-224":45596,"blake2b-232":45597,"blake2b-240":45598,"blake2b-248":45599,"blake2b-256":45600,"blake2b-264":45601,"blake2b-272":45602,"blake2b-280":45603,"blake2b-288":45604,"blake2b-296":45605,"blake2b-304":45606,"blake2b-312":45607,"blake2b-320":45608,"blake2b-328":45609,"blake2b-336":45610,"blake2b-344":45611,"blake2b-352":45612,"blake2b-360":45613,"blake2b-368":45614,"blake2b-376":45615,"blake2b-384":45616,"blake2b-392":45617,"blake2b-400":45618,"blake2b-408":45619,"blake2b-416":45620,"blake2b-424":45621,"blake2b-432":45622,"blake2b-440":45623,"blake2b-448":45624,"blake2b-456":45625,"blake2b-464":45626,"blake2b-472":45627,"blake2b-480":45628,"blake2b-488":45629,"blake2b-496":45630,"blake2b-504":45631,"blake2b-512":45632,"blake2s-8":45633,"blake2s-16":45634,"blake2s-24":45635,"blake2s-32":45636,"blake2s-40":45637,"blake2s-48":45638,"blake2s-56":45639,"blake2s-64":45640,"blake2s-72":45641,"blake2s-80":45642,"blake2s-88":45643,"blake2s-96":45644,"blake2s-104":45645,"blake2s-112":45646,"blake2s-120":45647,"blake2s-128":45648,"blake2s-136":45649,"blake2s-144":45650,"blake2s-152":45651,"blake2s-160":45652,"blake2s-168":45653,"blake2s-176":45654,"blake2s-184":45655,"blake2s-192":45656,"blake2s-200":45657,"blake2s-208":45658,"blake2s-216":45659,"blake2s-224":45660,"blake2s-232":45661,"blake2s-240":45662,"blake2s-248":45663,"blake2s-256":45664,"Skein256-8":45825,"Skein256-16":45826,"Skein256-24":45827,"Skein256-32":45828,"Skein256-40":45829,"Skein256-48":45830,"Skein256-56":45831,"Skein256-64":45832,"Skein256-72":45833,"Skein256-80":45834,"Skein256-88":45835,"Skein256-96":45836,"Skein256-104":45837,"Skein256-112":45838,"Skein256-120":45839,"Skein256-128":45840,"Skein256-136":45841,"Skein256-144":45842,"Skein256-152":45843,"Skein256-160":45844,"Skein256-168":45845,"Skein256-176":45846,"Skein256-184":45847,"Skein256-192":45848,"Skein256-200":45849,"Skein256-208":45850,"Skein256-216":45851,"Skein256-224":45852,"Skein256-232":45853,"Skein256-240":45854,"Skein256-248":45855,"Skein256-256":45856,"Skein512-8":45857,"Skein512-16":45858,"Skein512-24":45859,"Skein512-32":45860,"Skein512-40":45861,"Skein512-48":45862,"Skein512-56":45863,"Skein512-64":45864,"Skein512-72":45865,"Skein512-80":45866,"Skein512-88":45867,"Skein512-96":45868,"Skein512-104":45869,"Skein512-112":45870,"Skein512-120":45871,"Skein512-128":45872,"Skein512-136":45873,"Skein512-144":45874,"Skein512-152":45875,"Skein512-160":45876,"Skein512-168":45877,"Skein512-176":45878,"Skein512-184":45879,"Skein512-192":45880,"Skein512-200":45881,"Skein512-208":45882,"Skein512-216":45883,"Skein512-224":45884,"Skein512-232":45885,"Skein512-240":45886,"Skein512-248":45887,"Skein512-256":45888,"Skein512-264":45889,"Skein512-272":45890,"Skein512-280":45891,"Skein512-288":45892,"Skein512-296":45893,"Skein512-304":45894,"Skein512-312":45895,"Skein512-320":45896,"Skein512-328":45897,"Skein512-336":45898,"Skein512-344":45899,"Skein512-352":45900,"Skein512-360":45901,"Skein512-368":45902,"Skein512-376":45903,"Skein512-384":45904,"Skein512-392":45905,"Skein512-400":45906,"Skein512-408":45907,"Skein512-416":45908,"Skein512-424":45909,"Skein512-432":45910,"Skein512-440":45911,"Skein512-448":45912,"Skein512-456":45913,"Skein512-464":45914,"Skein512-472":45915,"Skein512-480":45916,"Skein512-488":45917,"Skein512-496":45918,"Skein512-504":45919,"Skein512-512":45920,"Skein1024-8":45921,"Skein1024-16":45922,"Skein1024-24":45923,"Skein1024-32":45924,"Skein1024-40":45925,"Skein1024-48":45926,"Skein1024-56":45927,"Skein1024-64":45928,"Skein1024-72":45929,"Skein1024-80":45930,"Skein1024-88":45931,"Skein1024-96":45932,"Skein1024-104":45933,"Skein1024-112":45934,"Skein1024-120":45935,"Skein1024-128":45936,"Skein1024-136":45937,"Skein1024-144":45938,"Skein1024-152":45939,"Skein1024-160":45940,"Skein1024-168":45941,"Skein1024-176":45942,"Skein1024-184":45943,"Skein1024-192":45944,"Skein1024-200":45945,"Skein1024-208":45946,"Skein1024-216":45947,"Skein1024-224":45948,"Skein1024-232":45949,"Skein1024-240":45950,"Skein1024-248":45951,"Skein1024-256":45952,"Skein1024-264":45953,"Skein1024-272":45954,"Skein1024-280":45955,"Skein1024-288":45956,"Skein1024-296":45957,"Skein1024-304":45958,"Skein1024-312":45959,"Skein1024-320":45960,"Skein1024-328":45961,"Skein1024-336":45962,"Skein1024-344":45963,"Skein1024-352":45964,"Skein1024-360":45965,"Skein1024-368":45966,"Skein1024-376":45967,"Skein1024-384":45968,"Skein1024-392":45969,"Skein1024-400":45970,"Skein1024-408":45971,"Skein1024-416":45972,"Skein1024-424":45973,"Skein1024-432":45974,"Skein1024-440":45975,"Skein1024-448":45976,"Skein1024-456":45977,"Skein1024-464":45978,"Skein1024-472":45979,"Skein1024-480":45980,"Skein1024-488":45981,"Skein1024-496":45982,"Skein1024-504":45983,"Skein1024-512":45984,"Skein1024-520":45985,"Skein1024-528":45986,"Skein1024-536":45987,"Skein1024-544":45988,"Skein1024-552":45989,"Skein1024-560":45990,"Skein1024-568":45991,"Skein1024-576":45992,"Skein1024-584":45993,"Skein1024-592":45994,"Skein1024-600":45995,"Skein1024-608":45996,"Skein1024-616":45997,"Skein1024-624":45998,"Skein1024-632":45999,"Skein1024-640":46e3,"Skein1024-648":46001,"Skein1024-656":46002,"Skein1024-664":46003,"Skein1024-672":46004,"Skein1024-680":46005,"Skein1024-688":46006,"Skein1024-696":46007,"Skein1024-704":46008,"Skein1024-712":46009,"Skein1024-720":46010,"Skein1024-728":46011,"Skein1024-736":46012,"Skein1024-744":46013,"Skein1024-752":46014,"Skein1024-760":46015,"Skein1024-768":46016,"Skein1024-776":46017,"Skein1024-784":46018,"Skein1024-792":46019,"Skein1024-800":46020,"Skein1024-808":46021,"Skein1024-816":46022,"Skein1024-824":46023,"Skein1024-832":46024,"Skein1024-840":46025,"Skein1024-848":46026,"Skein1024-856":46027,"Skein1024-864":46028,"Skein1024-872":46029,"Skein1024-880":46030,"Skein1024-888":46031,"Skein1024-896":46032,"Skein1024-904":46033,"Skein1024-912":46034,"Skein1024-920":46035,"Skein1024-928":46036,"Skein1024-936":46037,"Skein1024-944":46038,"Skein1024-952":46039,"Skein1024-960":46040,"Skein1024-968":46041,"Skein1024-976":46042,"Skein1024-984":46043,"Skein1024-992":46044,"Skein1024-1000":46045,"Skein1024-1008":46046,"Skein1024-1016":46047,"Skein1024-1024":46048}),n.codes=Object.freeze({0:"identity",17:"sha1",18:"sha2-256",19:"sha2-512",86:"dbl-sha2-256",23:"sha3-224",22:"sha3-256",21:"sha3-384",20:"sha3-512",24:"shake-128",25:"shake-256",26:"keccak-224",27:"keccak-256",28:"keccak-384",29:"keccak-512",34:"murmur3-128",35:"murmur3-32",45569:"blake2b-8",45570:"blake2b-16",45571:"blake2b-24",45572:"blake2b-32",45573:"blake2b-40",45574:"blake2b-48",45575:"blake2b-56",45576:"blake2b-64",45577:"blake2b-72",45578:"blake2b-80",45579:"blake2b-88",45580:"blake2b-96",45581:"blake2b-104",45582:"blake2b-112",45583:"blake2b-120",45584:"blake2b-128",45585:"blake2b-136",45586:"blake2b-144",45587:"blake2b-152",45588:"blake2b-160",45589:"blake2b-168",45590:"blake2b-176",45591:"blake2b-184",45592:"blake2b-192",45593:"blake2b-200",45594:"blake2b-208",45595:"blake2b-216",45596:"blake2b-224",45597:"blake2b-232",45598:"blake2b-240",45599:"blake2b-248",45600:"blake2b-256",45601:"blake2b-264",45602:"blake2b-272",45603:"blake2b-280",45604:"blake2b-288",45605:"blake2b-296",45606:"blake2b-304",45607:"blake2b-312",45608:"blake2b-320",45609:"blake2b-328",45610:"blake2b-336",45611:"blake2b-344",45612:"blake2b-352",45613:"blake2b-360",45614:"blake2b-368",45615:"blake2b-376",45616:"blake2b-384",45617:"blake2b-392",45618:"blake2b-400",45619:"blake2b-408",45620:"blake2b-416",45621:"blake2b-424",45622:"blake2b-432",45623:"blake2b-440",45624:"blake2b-448",45625:"blake2b-456",45626:"blake2b-464",45627:"blake2b-472",45628:"blake2b-480",45629:"blake2b-488",45630:"blake2b-496",45631:"blake2b-504",45632:"blake2b-512",45633:"blake2s-8",45634:"blake2s-16",45635:"blake2s-24",45636:"blake2s-32",45637:"blake2s-40",45638:"blake2s-48",45639:"blake2s-56",45640:"blake2s-64",45641:"blake2s-72",45642:"blake2s-80",45643:"blake2s-88",45644:"blake2s-96",45645:"blake2s-104",45646:"blake2s-112",45647:"blake2s-120",45648:"blake2s-128",45649:"blake2s-136",45650:"blake2s-144",45651:"blake2s-152",45652:"blake2s-160",45653:"blake2s-168",45654:"blake2s-176",45655:"blake2s-184",45656:"blake2s-192",45657:"blake2s-200",45658:"blake2s-208",45659:"blake2s-216",45660:"blake2s-224",45661:"blake2s-232",45662:"blake2s-240",45663:"blake2s-248",45664:"blake2s-256",45825:"Skein256-8",45826:"Skein256-16",45827:"Skein256-24",45828:"Skein256-32",45829:"Skein256-40",45830:"Skein256-48",45831:"Skein256-56",45832:"Skein256-64",45833:"Skein256-72",45834:"Skein256-80",45835:"Skein256-88",45836:"Skein256-96",45837:"Skein256-104",45838:"Skein256-112",45839:"Skein256-120",45840:"Skein256-128",45841:"Skein256-136",45842:"Skein256-144",45843:"Skein256-152",45844:"Skein256-160",45845:"Skein256-168",45846:"Skein256-176",45847:"Skein256-184",45848:"Skein256-192",45849:"Skein256-200",45850:"Skein256-208",45851:"Skein256-216",45852:"Skein256-224",45853:"Skein256-232",45854:"Skein256-240",45855:"Skein256-248",45856:"Skein256-256",45857:"Skein512-8",45858:"Skein512-16",45859:"Skein512-24",45860:"Skein512-32",45861:"Skein512-40",45862:"Skein512-48",45863:"Skein512-56",45864:"Skein512-64",45865:"Skein512-72",45866:"Skein512-80",45867:"Skein512-88",45868:"Skein512-96",45869:"Skein512-104",45870:"Skein512-112",45871:"Skein512-120",45872:"Skein512-128",45873:"Skein512-136",45874:"Skein512-144",45875:"Skein512-152",45876:"Skein512-160",45877:"Skein512-168",45878:"Skein512-176",45879:"Skein512-184",45880:"Skein512-192",45881:"Skein512-200",45882:"Skein512-208",45883:"Skein512-216",45884:"Skein512-224",45885:"Skein512-232",45886:"Skein512-240",45887:"Skein512-248",45888:"Skein512-256",45889:"Skein512-264",45890:"Skein512-272",45891:"Skein512-280",45892:"Skein512-288",45893:"Skein512-296",45894:"Skein512-304",45895:"Skein512-312",45896:"Skein512-320",45897:"Skein512-328",45898:"Skein512-336",45899:"Skein512-344",45900:"Skein512-352",45901:"Skein512-360",45902:"Skein512-368",45903:"Skein512-376",45904:"Skein512-384",45905:"Skein512-392",45906:"Skein512-400",45907:"Skein512-408",45908:"Skein512-416",45909:"Skein512-424",45910:"Skein512-432",45911:"Skein512-440",45912:"Skein512-448",45913:"Skein512-456",45914:"Skein512-464",45915:"Skein512-472",45916:"Skein512-480",45917:"Skein512-488",45918:"Skein512-496",45919:"Skein512-504",45920:"Skein512-512",45921:"Skein1024-8",45922:"Skein1024-16",45923:"Skein1024-24",45924:"Skein1024-32",45925:"Skein1024-40",45926:"Skein1024-48",45927:"Skein1024-56",45928:"Skein1024-64",45929:"Skein1024-72",45930:"Skein1024-80",45931:"Skein1024-88",45932:"Skein1024-96",45933:"Skein1024-104",45934:"Skein1024-112",45935:"Skein1024-120",45936:"Skein1024-128",45937:"Skein1024-136",45938:"Skein1024-144",45939:"Skein1024-152",45940:"Skein1024-160",45941:"Skein1024-168",45942:"Skein1024-176",45943:"Skein1024-184",45944:"Skein1024-192",45945:"Skein1024-200",45946:"Skein1024-208",45947:"Skein1024-216",45948:"Skein1024-224",45949:"Skein1024-232",45950:"Skein1024-240",45951:"Skein1024-248",45952:"Skein1024-256",45953:"Skein1024-264",45954:"Skein1024-272",45955:"Skein1024-280",45956:"Skein1024-288",45957:"Skein1024-296",45958:"Skein1024-304",45959:"Skein1024-312",45960:"Skein1024-320",45961:"Skein1024-328",45962:"Skein1024-336",45963:"Skein1024-344",45964:"Skein1024-352",45965:"Skein1024-360",45966:"Skein1024-368",45967:"Skein1024-376",45968:"Skein1024-384",45969:"Skein1024-392",45970:"Skein1024-400",45971:"Skein1024-408",45972:"Skein1024-416",45973:"Skein1024-424",45974:"Skein1024-432",45975:"Skein1024-440",45976:"Skein1024-448",45977:"Skein1024-456",45978:"Skein1024-464",45979:"Skein1024-472",45980:"Skein1024-480",45981:"Skein1024-488",45982:"Skein1024-496",45983:"Skein1024-504",45984:"Skein1024-512",45985:"Skein1024-520",45986:"Skein1024-528",45987:"Skein1024-536",45988:"Skein1024-544",45989:"Skein1024-552",45990:"Skein1024-560",45991:"Skein1024-568",45992:"Skein1024-576",45993:"Skein1024-584",45994:"Skein1024-592",45995:"Skein1024-600",45996:"Skein1024-608",45997:"Skein1024-616",45998:"Skein1024-624",45999:"Skein1024-632",46e3:"Skein1024-640",46001:"Skein1024-648",46002:"Skein1024-656",46003:"Skein1024-664",46004:"Skein1024-672",46005:"Skein1024-680",46006:"Skein1024-688",46007:"Skein1024-696",46008:"Skein1024-704",46009:"Skein1024-712",46010:"Skein1024-720",46011:"Skein1024-728",46012:"Skein1024-736",46013:"Skein1024-744",46014:"Skein1024-752",46015:"Skein1024-760",46016:"Skein1024-768",46017:"Skein1024-776",46018:"Skein1024-784",46019:"Skein1024-792",46020:"Skein1024-800",46021:"Skein1024-808",46022:"Skein1024-816",46023:"Skein1024-824",46024:"Skein1024-832",46025:"Skein1024-840",46026:"Skein1024-848",46027:"Skein1024-856",46028:"Skein1024-864",46029:"Skein1024-872",46030:"Skein1024-880",46031:"Skein1024-888",46032:"Skein1024-896",46033:"Skein1024-904",46034:"Skein1024-912",46035:"Skein1024-920",46036:"Skein1024-928",46037:"Skein1024-936",46038:"Skein1024-944",46039:"Skein1024-952",46040:"Skein1024-960",46041:"Skein1024-968",46042:"Skein1024-976",46043:"Skein1024-984",46044:"Skein1024-992",46045:"Skein1024-1000",46046:"Skein1024-1008",46047:"Skein1024-1016",46048:"Skein1024-1024"}),n.defaultLengths=Object.freeze({17:20,18:32,19:64,86:32,23:28,22:32,21:48,20:64,24:32,25:64,26:28,27:32,28:48,29:64,34:32,45569:1,45570:2,45571:3,45572:4,45573:5,45574:6,45575:7,45576:8,45577:9,45578:10,45579:11,45580:12,45581:13,45582:14,45583:15,45584:16,45585:17,45586:18,45587:19,45588:20,45589:21,45590:22,45591:23,45592:24,45593:25,45594:26,45595:27,45596:28,45597:29,45598:30,45599:31,45600:32,45601:33,45602:34,45603:35,45604:36,45605:37,45606:38,45607:39,45608:40,45609:41,45610:42,45611:43,45612:44,45613:45,45614:46,45615:47,45616:48,45617:49,45618:50,45619:51,45620:52,45621:53,45622:54,45623:55,45624:56,45625:57,45626:58,45627:59,45628:60,45629:61,45630:62,45631:63,45632:64,45633:1,45634:2,45635:3,45636:4,45637:5,45638:6,45639:7,45640:8,45641:9,45642:10,45643:11,45644:12,45645:13,45646:14,45647:15,45648:16,45649:17,45650:18,45651:19,45652:20,45653:21,45654:22,45655:23,45656:24,45657:25,45658:26,45659:27,45660:28,45661:29,45662:30,45663:31,45664:32,45825:1,45826:2,45827:3,45828:4,45829:5,45830:6,45831:7,45832:8,45833:9,45834:10,45835:11,45836:12,45837:13,45838:14,45839:15,45840:16,45841:17,45842:18,45843:19,45844:20,45845:21,45846:22,45847:23,45848:24,45849:25,45850:26,45851:27,45852:28,45853:29,45854:30,45855:31,45856:32,45857:1,45858:2,45859:3,45860:4,45861:5,45862:6,45863:7,45864:8,45865:9,45866:10,45867:11,45868:12,45869:13,45870:14,45871:15,45872:16,45873:17,45874:18,45875:19,45876:20,45877:21,45878:22,45879:23,45880:24,45881:25,45882:26,45883:27,45884:28,45885:29,45886:30,45887:31,45888:32,45889:33,45890:34,45891:35,45892:36,45893:37,45894:38,45895:39,45896:40,45897:41,45898:42,45899:43,45900:44,45901:45,45902:46,45903:47,45904:48,45905:49,45906:50,45907:51,45908:52,45909:53,45910:54,45911:55,45912:56,45913:57,45914:58,45915:59,45916:60,45917:61,45918:62,45919:63,45920:64,45921:1,45922:2,45923:3,45924:4,45925:5,45926:6,45927:7,45928:8,45929:9,45930:10,45931:11,45932:12,45933:13,45934:14,45935:15,45936:16,45937:17,45938:18,45939:19,45940:20,45941:21,45942:22,45943:23,45944:24,45945:25,45946:26,45947:27,45948:28,45949:29,45950:30,45951:31,45952:32,45953:33,45954:34,45955:35,45956:36,45957:37,45958:38,45959:39,45960:40,45961:41,45962:42,45963:43,45964:44,45965:45,45966:46,45967:47,45968:48,45969:49,45970:50,45971:51,45972:52,45973:53,45974:54,45975:55,45976:56,45977:57,45978:58,45979:59,45980:60,45981:61,45982:62,45983:63,45984:64,45985:65,45986:66,45987:67,45988:68,45989:69,45990:70,45991:71,45992:72,45993:73,45994:74,45995:75,45996:76,45997:77,45998:78,45999:79,46e3:80,46001:81,46002:82,46003:83,46004:84,46005:85,46006:86,46007:87,46008:88,46009:89,46010:90,46011:91,46012:92,46013:93,46014:94,46015:95,46016:96,46017:97,46018:98,46019:99,46020:100,46021:101,46022:102,46023:103,46024:104,46025:105,46026:106,46027:107,46028:108,46029:109,46030:110,46031:111,46032:112,46033:113,46034:114,46035:115,46036:116,46037:117,46038:118,46039:119,46040:120,46041:121,46042:122,46043:123,46044:124,46045:125,46046:126,46047:127,46048:128})},function(e,n,t){"use strict";e.exports=function e(n,t,r){t=t||[];var o=r=r||0;for(;n>=i;)t[r++]=255&n|128,n/=128;for(;-128&n;)t[r++]=255&n|128,n>>>=7;return t[r]=0|n,e.bytes=r-o+1,t};var i=Math.pow(2,31)},function(e,n,t){"use strict";e.exports=function e(n,t){var i,r=0,o=0,s=t=t||0,a=n.length;do{if(s>=a)throw e.bytes=0,new RangeError("Could not decode varint");i=n[s++],r+=o<28?(127&i)<<o:(127&i)*Math.pow(2,o),o+=7}while(i>=128);return e.bytes=s-t,r}},function(e,n,t){"use strict";var i=Math.pow(2,7),r=Math.pow(2,14),o=Math.pow(2,21),s=Math.pow(2,28),a=Math.pow(2,35),k=Math.pow(2,42),u=Math.pow(2,49),f=Math.pow(2,56),l=Math.pow(2,63);e.exports=function(e){return e<i?1:e<r?2:e<o?3:e<s?4:e<a?5:e<k?6:e<u?7:e<f?8:e<l?9:10}},function(e,n,t){"use strict";(function(i){const r=t(19);(n=e.exports=s).encode=function(e,n){const t=a(e);return s(t.name,i.from(t.encode(n)))},n.decode=function(e){i.isBuffer(e)&&(e=e.toString());const n=e.substring(0,1);"string"==typeof(e=e.substring(1,e.length))&&(e=i.from(e));const t=a(n);return i.from(t.decode(e.toString()))},n.isEncoded=function(e){i.isBuffer(e)&&(e=e.toString());if("[object String]"!==Object.prototype.toString.call(e))return!1;const n=e.substring(0,1);try{return a(n).name}catch(t){return!1}},n.names=Object.freeze(Object.keys(r.names)),n.codes=Object.freeze(Object.keys(r.codes));const o=new Error("Unsupported encoding");function s(e,n){if(!n)throw new Error("requires an encoded buffer");const t=a(e),r=i.from(t.code);return function(e,n){a(e).decode(n.toString())}(t.name,n),i.concat([r,n])}function a(e){let n;if(r.names[e])n=r.names[e];else{if(!r.codes[e])throw o;n=r.codes[e]}if(!n.isImplemented())throw new Error("Base "+e+" is not implemented yet");return n}}).call(this,t(0).Buffer)},function(e,n,t){"use strict";const i=t(20),r=t(4),o=t(21),s=t(22),a=t(23),k=[["base1","1","","1"],["base2","0",r,"01"],["base8","7",r,"01234567"],["base10","9",r,"0123456789"],["base16","f",o,"0123456789abcdef"],["base32","b",s,"abcdefghijklmnopqrstuvwxyz234567"],["base32pad","c",s,"abcdefghijklmnopqrstuvwxyz234567="],["base32hex","v",s,"0123456789abcdefghijklmnopqrstuv"],["base32hexpad","t",s,"0123456789abcdefghijklmnopqrstuv="],["base32z","h",s,"ybndrfg8ejkmcpqxot1uwisza345h769"],["base58flickr","Z",r,"**********************************************************"],["base58btc","z",r,"**********************************************************"],["base64","m",a,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"],["base64pad","M",a,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="],["base64url","u",a,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"],["base64urlpad","U",a,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_="]],u=k.reduce((e,n)=>(e[n[0]]=new i(n[0],n[1],n[2],n[3]),e),{}),f=k.reduce((e,n)=>(e[n[1]]=u[n[0]],e),{});e.exports={names:u,codes:f}},function(e,n,t){"use strict";e.exports=class{constructor(e,n,t,i){this.name=e,this.code=n,this.alphabet=i,t&&i&&(this.engine=t(i))}encode(e){return this.engine.encode(e)}decode(e){return this.engine.decode(e)}isImplemented(){return this.engine}}},function(e,n,t){"use strict";(function(n){e.exports=function(e){return{encode:e=>"string"==typeof e?n.from(e).toString("hex"):e.toString("hex"),decode(t){for(let n of t)if(e.indexOf(n)<0)throw new Error("invalid base16 character");return n.from(t,"hex")}}}}).call(this,t(0).Buffer)},function(e,n,t){"use strict";(function(n){function t(e,n){let t=e.byteLength,i=new Uint8Array(e),r=n.indexOf("=")===n.length-1;r&&(n=n.substring(0,n.length-2));let o=0,s=0,a="";for(let k=0;k<t;k++)for(s=s<<8|i[k],o+=8;o>=5;)a+=n[s>>>o-5&31],o-=5;if(o>0&&(a+=n[s<<5-o&31]),r)for(;a.length%8!=0;)a+="=";return a}e.exports=function(e){return{encode:i=>t("string"==typeof i?n.from(i):i,e),decode(n){for(let t of n)if(e.indexOf(t)<0)throw new Error("invalid base32 character");return function(e,n){let t=(e=e.replace(new RegExp("=","g"),"")).length,i=0,r=0,o=0,s=new Uint8Array(5*t/8|0);for(let a=0;a<t;a++)r=r<<5|n.indexOf(e[a]),i+=5,i>=8&&(s[o++]=r>>>i-8&255,i-=8);return s.buffer}(n,e)}}}}).call(this,t(0).Buffer)},function(e,n,t){"use strict";(function(n){e.exports=function(e){const t=e.indexOf("=")>-1,i=e.indexOf("-")>-1&&e.indexOf("_")>-1;return{encode(e){let r="";r="string"==typeof e?n.from(e).toString("base64"):e.toString("base64"),i&&(r=r.replace(/\+/g,"-").replace(/\//g,"_"));const o=r.indexOf("=");return o>0&&!t&&(r=r.substring(0,o)),r},decode(t){for(let n of t)if(e.indexOf(n)<0)throw new Error("invalid base64 character");return n.from(t,"base64")}}}}).call(this,t(0).Buffer)},function(e,n,t){"use strict";(function(i){const r=t(2),o=t(25),s=t(26),a=t(5);(n=e.exports).addPrefix=(e,n)=>{let t;if(i.isBuffer(e))t=a.varintBufferEncode(e);else{if(!s[e])throw new Error("multicodec not recognized");t=s[e]}return i.concat([t,n])},n.rmPrefix=e=>(r.decode(e),e.slice(r.decode.bytes)),n.getCodec=e=>{const n=r.decode(e),t=o.get(n);if(void 0===t)throw new Error("Code ".concat(n," not found"));return t},n.getName=e=>o.get(e),n.getNumber=e=>{const n=s[e];if(void 0===n)throw new Error("Codec `"+e+"` not found");return a.varintBufferDecode(n)[0]},n.getCode=e=>r.decode(e),n.getCodeVarint=e=>{const n=s[e];if(void 0===n)throw new Error("Codec `"+e+"` not found");return n},n.getVarint=e=>r.encode(e);const k=t(27);Object.assign(n,k),n.print=t(28)}).call(this,t(0).Buffer)},function(e,n,t){"use strict";const i=t(1),r=new Map;for(const o in i){const e=i[o];r.set(e,o)}e.exports=Object.freeze(r)},function(e,n,t){"use strict";const i=t(1),r=t(5).varintEncode,o={};for(const s in i){const e=i[s];o[s]=r(e)}e.exports=Object.freeze(o)},function(e,n,t){"use strict";const i=t(1),r={};for(const[o,s]of Object.entries(i))r[o.toUpperCase().replace(/-/g,"_")]=s;e.exports=Object.freeze(r)},function(e,n,t){"use strict";const i=t(1),r={};for(const[o,s]of Object.entries(i))void 0===r[s]&&(r[s]=o);e.exports=Object.freeze(r)},function(e,n,t){"use strict";const i=t(3),{Buffer:r}=t(0);var o={checkCIDComponents:function(e){if(null==e)return"null values are not valid CIDs";if(0!==e.version&&1!==e.version)return"Invalid version, must be a number equal to 1 or 0";if("string"!=typeof e.codec)return"codec must be string";if(0===e.version){if("dag-pb"!==e.codec)return"codec must be 'dag-pb' for CIDv0";if("base58btc"!==e.multibaseName)return"multibaseName must be 'base58btc' for CIDv0"}if(!r.isBuffer(e.multihash))return"multihash must be a Buffer";try{i.validate(e.multihash)}catch(n){let e=n.message;return e||(e="Multihash validation failed"),e}}};e.exports=o},function(e,n,t){"use strict";e.exports=function(e,{className:n,symbolName:t}){const i=Symbol.for(t),r={[n]:class extends e{constructor(...e){super(...e),Object.defineProperty(this,i,{value:!0})}get[Symbol.toStringTag](){return n}}}[n];return r["is".concat(n)]=e=>!(!e||!e[i]),r},e.exports.proto=function(e,{className:n,symbolName:t,withoutNew:i}){const r=Symbol.for(t),o={[n]:function(...n){if(i&&!(this instanceof o))return new o(...n);const t=e.call(this,...n)||this;return t&&!t[r]&&Object.defineProperty(t,r,{value:!0}),t}}[n];return o.prototype=Object.create(e.prototype),o.prototype.constructor=o,Object.defineProperty(o.prototype,Symbol.toStringTag,{get:()=>n}),o["is".concat(n)]=e=>!(!e||!e[r]),o}}])}));
//# sourceMappingURL=index.min.js.map