{"name": "cids", "version": "0.7.5", "description": "CID Implementation in JavaScript", "leadMaintainer": "<PERSON><PERSON> Mische <<EMAIL>>", "main": "src/index.js", "types": "src/index.d.ts", "scripts": {"lint": "aegir lint", "test": "aegir test", "test:node": "aegir test --target node", "test:browser": "aegir test --target browser", "build": "aegir build", "release": "aegir release --docs", "release-minor": "aegir release --type minor --docs", "release-major": "aegir release --type major --docs", "coverage": "aegir coverage", "docs": "aegir docs"}, "pre-push": ["lint", "test"], "repository": {"type": "git", "url": "git://github.com/multiformats/js-cid.git"}, "keywords": ["multihash", "cid", "ipld"], "license": "MIT", "bugs": {"url": "https://github.com/multiformats/js-cid/issues"}, "dependencies": {"buffer": "^5.5.0", "class-is": "^1.1.0", "multibase": "~0.6.0", "multicodec": "^1.0.0", "multihashes": "~0.4.15"}, "devDependencies": {"aegir": "^21.3.0", "chai": "^4.2.0", "dirty-chai": "^2.0.1", "multihashing": "~0.3.3", "multihashing-async": "~0.8.0"}, "engines": {"node": ">=4.0.0", "npm": ">=3.0.0"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Mitar <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Real Harry <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Vagg <<EMAIL>>", "<PERSON> <victorb<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> Mische <<EMAIL>>", "bluelovers <<EMAIL>>", "greenkeeper[bot] <greenkeeper[bot]@users.noreply.github.com>", "kumavis <<EMAIL>>"]}