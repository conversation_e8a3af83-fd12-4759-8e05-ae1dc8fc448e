{"version": 3, "file": "instantiate2.js", "sourceRoot": "", "sources": ["../src/instantiate2.ts"], "names": [], "mappings": ";;;AAAA,2CAAgD;AAChD,+CAAyE;AACzE,uCAAsC;AACtC,yCAAuC;AAEvC;;;GAGG;AACH,SAAS,IAAI,CAAC,IAAY,EAAE,GAAe;IACzC,OAAO,IAAI,eAAM,CAAC,IAAA,eAAM,EAAC,IAAA,kBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,SAAS,QAAQ,CAAC,GAAW;IAC3B,OAAO,aAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC;AACnD,CAAC;AAED;;;GAGG;AACH,gEAAgE;AAChE,SAAgB,gCAAgC,CAC9C,QAAoB,EACpB,OAAe,EACf,IAAgB,EAChB,GAAkB,EAClB,MAAc;IAEd,IAAA,cAAM,EAAC,QAAQ,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,IAAA,qBAAU,EAAC,OAAO,CAAC,CAAC,IAAI,CAAC;IAE7C,MAAM,OAAO,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,iBAAM,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;IAEzE,kBAAkB;IAClB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IAEhG,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC;QACzB,GAAG,IAAA,kBAAO,EAAC,MAAM,CAAC;QAClB,IAAI;QACJ,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC5B,GAAG,QAAQ;QACX,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;QAC/B,GAAG,WAAW;QACd,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACxB,GAAG,IAAI;QACP,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3B,GAAG,OAAO;KACX,CAAC,CAAC;IACH,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACxC,MAAM,OAAO,GAAG,IAAA,mBAAQ,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC9C,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;AACvC,CAAC;AA9BD,4EA8BC;AAED;;;;;;;GAOG;AACH,SAAgB,mBAAmB,CACjC,QAAoB,EACpB,OAAe,EACf,IAAgB,EAChB,YAAoB;IAEpB,wCAAwC;IACxC,iHAAiH;IACjH,MAAM,GAAG,GAAG,IAAI,CAAC;IACjB,OAAO,gCAAgC,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC,OAAO,CAAC;AAC9F,CAAC;AAVD,kDAUC"}