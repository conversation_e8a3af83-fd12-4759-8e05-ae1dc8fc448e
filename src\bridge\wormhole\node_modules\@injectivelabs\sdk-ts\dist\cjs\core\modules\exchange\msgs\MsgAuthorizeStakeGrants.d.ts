import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgAuthorizeStakeGrants {
    interface Params {
        grantee: string;
        injectiveAddress: string;
        amount: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants;
}
/**
 * @category Messages
 */
export default class MsgAuthorizeStakeGrants extends MsgBase<MsgAuthorizeStakeGrants.Params, MsgAuthorizeStakeGrants.Proto> {
    static fromJSON(params: MsgAuthorizeStakeGrants.Params): MsgAuthorizeStakeGrants;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants;
    toData(): {
        sender: string;
        grants: import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/exchange.js").GrantAuthorization[];
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            grants: import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/exchange.js").GrantAuthorization[];
        };
    };
    toWeb3Gw(): {
        sender: string;
        grants: import("@injectivelabs/core-proto-ts/cjs/injective/exchange/v1beta1/exchange.js").GrantAuthorization[];
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants;
    };
    toBinary(): Uint8Array;
}
