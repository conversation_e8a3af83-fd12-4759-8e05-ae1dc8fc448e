"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NeptuneService = void 0;
const networks_1 = require("@injectivelabs/networks");
const helper_js_1 = require("./helper.js");
const index_js_1 = require("../../chain/index.js");
const index_js_2 = require("./queries/index.js");
const transformer_js_1 = require("./transformer.js");
const ExecArgNeptuneDeposit_js_1 = __importDefault(require("../../../core/modules/wasm/exec-args/ExecArgNeptuneDeposit.js"));
const ExecArgNeptuneWithdraw_js_1 = __importDefault(require("../../../core/modules/wasm/exec-args/ExecArgNeptuneWithdraw.js"));
const MsgExecuteContractCompat_js_1 = __importDefault(require("../../../core/modules/wasm/msgs/MsgExecuteContractCompat.js"));
const exceptions_1 = require("@injectivelabs/exceptions");
const index_js_3 = require("./index.js");
const types_js_1 = require("./types.js");
const NEPTUNE_USDT_MARKET_CONTRACT = 'inj1nc7gjkf2mhp34a6gquhurg8qahnw5kxs5u3s4u';
const NEPTUNE_INTEREST_CONTRACT = 'inj1ftech0pdjrjawltgejlmpx57cyhsz6frdx2dhq';
class NeptuneService {
    client;
    priceOracleContract;
    /**
     * Constructs a new NeptuneService instan ce.
     * @param network The network to use (default: Mainnet).
     * @param endpoints Optional custom network endpoints.
     */
    constructor(network = networks_1.Network.MainnetSentry, endpoints) {
        if (!(0, networks_1.isMainnet)(network)) {
            throw new exceptions_1.GeneralException(new Error('Please switch to mainnet network'));
        }
        const networkEndpoints = endpoints || (0, networks_1.getNetworkEndpoints)(network);
        this.client = new index_js_1.ChainGrpcWasmApi(networkEndpoints.grpc);
        this.priceOracleContract = index_js_3.NEPTUNE_PRICE_CONTRACT;
    }
    /**
     * Fetch prices for given assets from the Neptune Price Oracle contract.
     * @param assets Array of AssetInfo objects.
     * @returns Array of Price objects.
     */
    async fetchPrices(assets) {
        const queryGetPricesPayload = new index_js_2.QueryGetPrices({ assets }).toPayload();
        try {
            const response = await this.client.fetchSmartContractState(this.priceOracleContract, queryGetPricesPayload);
            const prices = transformer_js_1.NeptuneQueryTransformer.contractPricesResponseToPrices(response);
            return prices;
        }
        catch (error) {
            console.error('Error fetching prices:', error);
            throw new exceptions_1.GeneralException(new Error('Failed to fetch prices'));
        }
    }
    /**
     * Fetch the redemption ratio based on CW20 and native asset prices.
     * @param cw20Asset AssetInfo for the CW20 token.
     * @param nativeAsset AssetInfo for the native token.
     * @returns Redemption ratio as a number.
     */
    async fetchRedemptionRatio({ cw20Asset, nativeAsset, }) {
        const prices = await this.fetchPrices([cw20Asset, nativeAsset]);
        const [cw20Price] = prices;
        const [nativePrice] = prices.reverse();
        if (!cw20Price || !nativePrice) {
            throw new exceptions_1.GeneralException(new Error('Failed to compute redemption ratio'));
        }
        return Number(cw20Price.price) / Number(nativePrice.price);
    }
    /**
     * Convert CW20 nUSDT to bank nUSDT using the redemption ratio.
     * @param amountCW20 Amount in CW20 nUSDT.
     * @param redemptionRatio Redemption ratio.
     * @returns Amount in bank nUSDT.
     */
    calculateBankAmount(amountCW20, redemptionRatio) {
        return amountCW20 * redemptionRatio;
    }
    /**
     * Convert bank nUSDT to CW20 nUSDT using the redemption ratio.
     * @param amountBank Amount in bank nUSDT.
     * @param redemptionRatio Redemption ratio.
     * @returns Amount in CW20 nUSDT.
     */
    calculateCw20Amount(amountBank, redemptionRatio) {
        return amountBank / redemptionRatio;
    }
    /**
     * Create a deposit message.
     * @param sender Sender's Injective address.
     * @param contractAddress USDT market contract address.
     * @param denom Denomination of the asset.
     * @param amount Amount to deposit as a string.
     * @returns MsgExecuteContractCompat message.
     */
    createDepositMsg({ denom, amount, sender, contractAddress = NEPTUNE_USDT_MARKET_CONTRACT, }) {
        return MsgExecuteContractCompat_js_1.default.fromJSON({
            sender,
            contractAddress,
            execArgs: ExecArgNeptuneDeposit_js_1.default.fromJSON({}),
            funds: {
                denom,
                amount,
            },
        });
    }
    /**
     * Create a withdraw message.
     * @param sender Sender's Injective address.
     * @param contractAddress nUSDT contract address.
     * @param amount Amount to withdraw as a string.
     * @returns MsgExecuteContractCompat message.
     */
    createWithdrawMsg({ amount, sender, cw20ContractAddress = types_js_1.NEPTUNE_USDT_CW20_CONTRACT, marketContractAddress = NEPTUNE_USDT_MARKET_CONTRACT, }) {
        return MsgExecuteContractCompat_js_1.default.fromJSON({
            sender,
            contractAddress: cw20ContractAddress,
            execArgs: ExecArgNeptuneWithdraw_js_1.default.fromJSON({
                amount,
                contract: marketContractAddress,
            }),
        });
    }
    /**
     * Fetch lending rates with optional pagination parameters.
     * @param limit Maximum number of lending rates to fetch.
     * @param startAfter AssetInfo to start after for pagination.
     * @returns Array of [AssetInfo, Decimal256] tuples.
     */
    async getLendingRates({ limit, startAfter, contractAddress = NEPTUNE_INTEREST_CONTRACT, }) {
        const query = new index_js_2.QueryGetAllLendingRates({ limit, startAfter });
        const payload = query.toPayload();
        try {
            const response = await this.client.fetchSmartContractState(contractAddress, payload);
            const lendingRates = transformer_js_1.NeptuneQueryTransformer.contractLendingRatesResponseToLendingRates(response);
            return lendingRates;
        }
        catch (error) {
            throw new exceptions_1.GeneralException(new Error('Failed to fetch lending rates'));
        }
    }
    /**
     * Fetch the lending rate for a specific denom by querying the smart contract with pagination.
     * @param denom The denomination string of the asset to find the lending rate for.
     * @returns Lending rate as a string.
     */
    async getLendingRateByDenom({ denom, contractAddress = NEPTUNE_INTEREST_CONTRACT, }) {
        const limit = 10;
        let startAfter = undefined;
        while (true) {
            const lendingRates = await this.getLendingRates({
                limit,
                startAfter,
                contractAddress,
            });
            if (lendingRates.length === 0) {
                return;
            }
            for (const { assetInfo, lendingRate } of lendingRates) {
                const currentDenom = (0, helper_js_1.getDenom)(assetInfo);
                if (currentDenom === denom) {
                    return lendingRate;
                }
            }
            if (lendingRates.length < limit) {
                return;
            }
            const lastLendingRate = lendingRates[lendingRates.length - 1];
            startAfter = lastLendingRate.assetInfo;
        }
    }
    /**
     * Calculates APY from APR and compounding frequency.
     *
     * @param apr - The annual percentage rate as a decimal (e.g., 0.10 for 10%)
     * @param compoundingFrequency - Number of times interest is compounded per year
     * @returns The annual percentage yield as a decimal
     */
    calculateAPY(apr) {
        return Math.exp(apr) - 1;
    }
}
exports.NeptuneService = NeptuneService;
