/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin.js";
import { GenesisState } from "./genesis.js";
import { Params } from "./params.js";
import { AddressVoucher, Namespace, PolicyManagerCapability, PolicyStatus, RoleManager } from "./permissions.js";
export const protobufPackage = "injective.permissions.v1beta1";
function createBaseQueryParamsRequest() {
    return {};
}
export const QueryParamsRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryParamsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return QueryParamsRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseQueryParamsRequest();
        return message;
    },
};
function createBaseQueryParamsResponse() {
    return { params: undefined };
}
export const QueryParamsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.params !== undefined) {
            Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryParamsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { params: isSet(object.params) ? Params.fromJSON(object.params) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.params !== undefined && (obj.params = message.params ? Params.toJSON(message.params) : undefined);
        return obj;
    },
    create(base) {
        return QueryParamsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQueryNamespaceDenomsRequest() {
    return {};
}
export const QueryNamespaceDenomsRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespaceDenomsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return QueryNamespaceDenomsRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseQueryNamespaceDenomsRequest();
        return message;
    },
};
function createBaseQueryNamespaceDenomsResponse() {
    return { denoms: [] };
}
export const QueryNamespaceDenomsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.denoms) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespaceDenomsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denoms.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denoms: Array.isArray(object?.denoms) ? object.denoms.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.denoms) {
            obj.denoms = message.denoms.map((e) => e);
        }
        else {
            obj.denoms = [];
        }
        return obj;
    },
    create(base) {
        return QueryNamespaceDenomsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryNamespaceDenomsResponse();
        message.denoms = object.denoms?.map((e) => e) || [];
        return message;
    },
};
function createBaseQueryNamespacesRequest() {
    return {};
}
export const QueryNamespacesRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespacesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return QueryNamespacesRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseQueryNamespacesRequest();
        return message;
    },
};
function createBaseQueryNamespacesResponse() {
    return { namespaces: [] };
}
export const QueryNamespacesResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.namespaces) {
            Namespace.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespacesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.namespaces.push(Namespace.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            namespaces: Array.isArray(object?.namespaces) ? object.namespaces.map((e) => Namespace.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.namespaces) {
            obj.namespaces = message.namespaces.map((e) => e ? Namespace.toJSON(e) : undefined);
        }
        else {
            obj.namespaces = [];
        }
        return obj;
    },
    create(base) {
        return QueryNamespacesResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryNamespacesResponse();
        message.namespaces = object.namespaces?.map((e) => Namespace.fromPartial(e)) || [];
        return message;
    },
};
function createBaseQueryNamespaceRequest() {
    return { denom: "" };
}
export const QueryNamespaceRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespaceRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return QueryNamespaceRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryNamespaceRequest();
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseQueryNamespaceResponse() {
    return { namespace: undefined };
}
export const QueryNamespaceResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.namespace !== undefined) {
            Namespace.encode(message.namespace, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryNamespaceResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.namespace = Namespace.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { namespace: isSet(object.namespace) ? Namespace.fromJSON(object.namespace) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.namespace !== undefined &&
            (obj.namespace = message.namespace ? Namespace.toJSON(message.namespace) : undefined);
        return obj;
    },
    create(base) {
        return QueryNamespaceResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryNamespaceResponse();
        message.namespace = (object.namespace !== undefined && object.namespace !== null)
            ? Namespace.fromPartial(object.namespace)
            : undefined;
        return message;
    },
};
function createBaseQueryActorsByRoleRequest() {
    return { denom: "", role: "" };
}
export const QueryActorsByRoleRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.role !== "") {
            writer.uint32(18).string(message.role);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryActorsByRoleRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.role = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            role: isSet(object.role) ? String(object.role) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.role !== undefined && (obj.role = message.role);
        return obj;
    },
    create(base) {
        return QueryActorsByRoleRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryActorsByRoleRequest();
        message.denom = object.denom ?? "";
        message.role = object.role ?? "";
        return message;
    },
};
function createBaseQueryActorsByRoleResponse() {
    return { actors: [] };
}
export const QueryActorsByRoleResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.actors) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryActorsByRoleResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.actors.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { actors: Array.isArray(object?.actors) ? object.actors.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.actors) {
            obj.actors = message.actors.map((e) => e);
        }
        else {
            obj.actors = [];
        }
        return obj;
    },
    create(base) {
        return QueryActorsByRoleResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryActorsByRoleResponse();
        message.actors = object.actors?.map((e) => e) || [];
        return message;
    },
};
function createBaseQueryRolesByActorRequest() {
    return { denom: "", actor: "" };
}
export const QueryRolesByActorRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.actor !== "") {
            writer.uint32(18).string(message.actor);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRolesByActorRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.actor = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            actor: isSet(object.actor) ? String(object.actor) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.actor !== undefined && (obj.actor = message.actor);
        return obj;
    },
    create(base) {
        return QueryRolesByActorRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRolesByActorRequest();
        message.denom = object.denom ?? "";
        message.actor = object.actor ?? "";
        return message;
    },
};
function createBaseQueryRolesByActorResponse() {
    return { roles: [] };
}
export const QueryRolesByActorResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.roles) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRolesByActorResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.roles.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { roles: Array.isArray(object?.roles) ? object.roles.map((e) => String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.roles) {
            obj.roles = message.roles.map((e) => e);
        }
        else {
            obj.roles = [];
        }
        return obj;
    },
    create(base) {
        return QueryRolesByActorResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRolesByActorResponse();
        message.roles = object.roles?.map((e) => e) || [];
        return message;
    },
};
function createBaseQueryRoleManagersRequest() {
    return { denom: "" };
}
export const QueryRoleManagersRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRoleManagersRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return QueryRoleManagersRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRoleManagersRequest();
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseQueryRoleManagersResponse() {
    return { roleManagers: [] };
}
export const QueryRoleManagersResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.roleManagers) {
            RoleManager.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRoleManagersResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.roleManagers.push(RoleManager.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            roleManagers: Array.isArray(object?.roleManagers)
                ? object.roleManagers.map((e) => RoleManager.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.roleManagers) {
            obj.roleManagers = message.roleManagers.map((e) => e ? RoleManager.toJSON(e) : undefined);
        }
        else {
            obj.roleManagers = [];
        }
        return obj;
    },
    create(base) {
        return QueryRoleManagersResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRoleManagersResponse();
        message.roleManagers = object.roleManagers?.map((e) => RoleManager.fromPartial(e)) || [];
        return message;
    },
};
function createBaseQueryRoleManagerRequest() {
    return { denom: "", manager: "" };
}
export const QueryRoleManagerRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.manager !== "") {
            writer.uint32(18).string(message.manager);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRoleManagerRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.manager = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            manager: isSet(object.manager) ? String(object.manager) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.manager !== undefined && (obj.manager = message.manager);
        return obj;
    },
    create(base) {
        return QueryRoleManagerRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRoleManagerRequest();
        message.denom = object.denom ?? "";
        message.manager = object.manager ?? "";
        return message;
    },
};
function createBaseQueryRoleManagerResponse() {
    return { roleManager: undefined };
}
export const QueryRoleManagerResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.roleManager !== undefined) {
            RoleManager.encode(message.roleManager, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryRoleManagerResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.roleManager = RoleManager.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { roleManager: isSet(object.roleManager) ? RoleManager.fromJSON(object.roleManager) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.roleManager !== undefined &&
            (obj.roleManager = message.roleManager ? RoleManager.toJSON(message.roleManager) : undefined);
        return obj;
    },
    create(base) {
        return QueryRoleManagerResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryRoleManagerResponse();
        message.roleManager = (object.roleManager !== undefined && object.roleManager !== null)
            ? RoleManager.fromPartial(object.roleManager)
            : undefined;
        return message;
    },
};
function createBaseQueryPolicyStatusesRequest() {
    return { denom: "" };
}
export const QueryPolicyStatusesRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryPolicyStatusesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return QueryPolicyStatusesRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryPolicyStatusesRequest();
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseQueryPolicyStatusesResponse() {
    return { policyStatuses: [] };
}
export const QueryPolicyStatusesResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.policyStatuses) {
            PolicyStatus.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryPolicyStatusesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.policyStatuses.push(PolicyStatus.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            policyStatuses: Array.isArray(object?.policyStatuses)
                ? object.policyStatuses.map((e) => PolicyStatus.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.policyStatuses) {
            obj.policyStatuses = message.policyStatuses.map((e) => e ? PolicyStatus.toJSON(e) : undefined);
        }
        else {
            obj.policyStatuses = [];
        }
        return obj;
    },
    create(base) {
        return QueryPolicyStatusesResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryPolicyStatusesResponse();
        message.policyStatuses = object.policyStatuses?.map((e) => PolicyStatus.fromPartial(e)) || [];
        return message;
    },
};
function createBaseQueryPolicyManagerCapabilitiesRequest() {
    return { denom: "" };
}
export const QueryPolicyManagerCapabilitiesRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryPolicyManagerCapabilitiesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return QueryPolicyManagerCapabilitiesRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryPolicyManagerCapabilitiesRequest();
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseQueryPolicyManagerCapabilitiesResponse() {
    return { policyManagerCapabilities: [] };
}
export const QueryPolicyManagerCapabilitiesResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.policyManagerCapabilities) {
            PolicyManagerCapability.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryPolicyManagerCapabilitiesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.policyManagerCapabilities.push(PolicyManagerCapability.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            policyManagerCapabilities: Array.isArray(object?.policyManagerCapabilities)
                ? object.policyManagerCapabilities.map((e) => PolicyManagerCapability.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.policyManagerCapabilities) {
            obj.policyManagerCapabilities = message.policyManagerCapabilities.map((e) => e ? PolicyManagerCapability.toJSON(e) : undefined);
        }
        else {
            obj.policyManagerCapabilities = [];
        }
        return obj;
    },
    create(base) {
        return QueryPolicyManagerCapabilitiesResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryPolicyManagerCapabilitiesResponse();
        message.policyManagerCapabilities =
            object.policyManagerCapabilities?.map((e) => PolicyManagerCapability.fromPartial(e)) || [];
        return message;
    },
};
function createBaseQueryVouchersRequest() {
    return { denom: "" };
}
export const QueryVouchersRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryVouchersRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return QueryVouchersRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryVouchersRequest();
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseQueryVouchersResponse() {
    return { vouchers: [] };
}
export const QueryVouchersResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.vouchers) {
            AddressVoucher.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryVouchersResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.vouchers.push(AddressVoucher.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            vouchers: Array.isArray(object?.vouchers) ? object.vouchers.map((e) => AddressVoucher.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.vouchers) {
            obj.vouchers = message.vouchers.map((e) => e ? AddressVoucher.toJSON(e) : undefined);
        }
        else {
            obj.vouchers = [];
        }
        return obj;
    },
    create(base) {
        return QueryVouchersResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryVouchersResponse();
        message.vouchers = object.vouchers?.map((e) => AddressVoucher.fromPartial(e)) || [];
        return message;
    },
};
function createBaseQueryVoucherRequest() {
    return { denom: "", address: "" };
}
export const QueryVoucherRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.address !== "") {
            writer.uint32(18).string(message.address);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryVoucherRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            address: isSet(object.address) ? String(object.address) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create(base) {
        return QueryVoucherRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryVoucherRequest();
        message.denom = object.denom ?? "";
        message.address = object.address ?? "";
        return message;
    },
};
function createBaseQueryVoucherResponse() {
    return { voucher: undefined };
}
export const QueryVoucherResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.voucher !== undefined) {
            Coin.encode(message.voucher, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryVoucherResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.voucher = Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { voucher: isSet(object.voucher) ? Coin.fromJSON(object.voucher) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.voucher !== undefined && (obj.voucher = message.voucher ? Coin.toJSON(message.voucher) : undefined);
        return obj;
    },
    create(base) {
        return QueryVoucherResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryVoucherResponse();
        message.voucher = (object.voucher !== undefined && object.voucher !== null)
            ? Coin.fromPartial(object.voucher)
            : undefined;
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
export const QueryModuleStateRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return QueryModuleStateRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
export const QueryModuleStateResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.state !== undefined) {
            GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { state: isSet(object.state) ? GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.state !== undefined && (obj.state = message.state ? GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create(base) {
        return QueryModuleStateResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
export class QueryClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.Params = this.Params.bind(this);
        this.NamespaceDenoms = this.NamespaceDenoms.bind(this);
        this.Namespaces = this.Namespaces.bind(this);
        this.Namespace = this.Namespace.bind(this);
        this.RolesByActor = this.RolesByActor.bind(this);
        this.ActorsByRole = this.ActorsByRole.bind(this);
        this.RoleManagers = this.RoleManagers.bind(this);
        this.RoleManager = this.RoleManager.bind(this);
        this.PolicyStatuses = this.PolicyStatuses.bind(this);
        this.PolicyManagerCapabilities = this.PolicyManagerCapabilities.bind(this);
        this.Vouchers = this.Vouchers.bind(this);
        this.Voucher = this.Voucher.bind(this);
        this.PermissionsModuleState = this.PermissionsModuleState.bind(this);
    }
    Params(request, metadata) {
        return this.rpc.unary(QueryParamsDesc, QueryParamsRequest.fromPartial(request), metadata);
    }
    NamespaceDenoms(request, metadata) {
        return this.rpc.unary(QueryNamespaceDenomsDesc, QueryNamespaceDenomsRequest.fromPartial(request), metadata);
    }
    Namespaces(request, metadata) {
        return this.rpc.unary(QueryNamespacesDesc, QueryNamespacesRequest.fromPartial(request), metadata);
    }
    Namespace(request, metadata) {
        return this.rpc.unary(QueryNamespaceDesc, QueryNamespaceRequest.fromPartial(request), metadata);
    }
    RolesByActor(request, metadata) {
        return this.rpc.unary(QueryRolesByActorDesc, QueryRolesByActorRequest.fromPartial(request), metadata);
    }
    ActorsByRole(request, metadata) {
        return this.rpc.unary(QueryActorsByRoleDesc, QueryActorsByRoleRequest.fromPartial(request), metadata);
    }
    RoleManagers(request, metadata) {
        return this.rpc.unary(QueryRoleManagersDesc, QueryRoleManagersRequest.fromPartial(request), metadata);
    }
    RoleManager(request, metadata) {
        return this.rpc.unary(QueryRoleManagerDesc, QueryRoleManagerRequest.fromPartial(request), metadata);
    }
    PolicyStatuses(request, metadata) {
        return this.rpc.unary(QueryPolicyStatusesDesc, QueryPolicyStatusesRequest.fromPartial(request), metadata);
    }
    PolicyManagerCapabilities(request, metadata) {
        return this.rpc.unary(QueryPolicyManagerCapabilitiesDesc, QueryPolicyManagerCapabilitiesRequest.fromPartial(request), metadata);
    }
    Vouchers(request, metadata) {
        return this.rpc.unary(QueryVouchersDesc, QueryVouchersRequest.fromPartial(request), metadata);
    }
    Voucher(request, metadata) {
        return this.rpc.unary(QueryVoucherDesc, QueryVoucherRequest.fromPartial(request), metadata);
    }
    PermissionsModuleState(request, metadata) {
        return this.rpc.unary(QueryPermissionsModuleStateDesc, QueryModuleStateRequest.fromPartial(request), metadata);
    }
}
export const QueryDesc = { serviceName: "injective.permissions.v1beta1.Query" };
export const QueryParamsDesc = {
    methodName: "Params",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryParamsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryNamespaceDenomsDesc = {
    methodName: "NamespaceDenoms",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryNamespaceDenomsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryNamespaceDenomsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryNamespacesDesc = {
    methodName: "Namespaces",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryNamespacesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryNamespacesResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryNamespaceDesc = {
    methodName: "Namespace",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryNamespaceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryNamespaceResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryRolesByActorDesc = {
    methodName: "RolesByActor",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryRolesByActorRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryRolesByActorResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryActorsByRoleDesc = {
    methodName: "ActorsByRole",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryActorsByRoleRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryActorsByRoleResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryRoleManagersDesc = {
    methodName: "RoleManagers",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryRoleManagersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryRoleManagersResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryRoleManagerDesc = {
    methodName: "RoleManager",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryRoleManagerRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryRoleManagerResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryPolicyStatusesDesc = {
    methodName: "PolicyStatuses",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryPolicyStatusesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryPolicyStatusesResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryPolicyManagerCapabilitiesDesc = {
    methodName: "PolicyManagerCapabilities",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryPolicyManagerCapabilitiesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryPolicyManagerCapabilitiesResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryVouchersDesc = {
    methodName: "Vouchers",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryVouchersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryVouchersResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryVoucherDesc = {
    methodName: "Voucher",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryVoucherRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryVoucherResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const QueryPermissionsModuleStateDesc = {
    methodName: "PermissionsModuleState",
    service: QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryModuleStateResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
