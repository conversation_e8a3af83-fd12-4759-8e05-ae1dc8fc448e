import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Any } from "../../../../google/protobuf/any";
import { Duration } from "../../../../google/protobuf/duration";
import { PageRequest, PageResponse } from "../../../base/query/v1beta1/pagination";
export declare const protobufPackage = "cosmos.orm.query.v1alpha1";
/** GetRequest is the Query/Get request type. */
export interface GetRequest {
    /** message_name is the fully-qualified message name of the ORM table being queried. */
    messageName: string;
    /**
     * index is the index fields expression used in orm definitions. If it
     * is empty, the table's primary key is assumed. If it is non-empty, it must
     * refer to an unique index.
     */
    index: string;
    /**
     * values are the values of the fields corresponding to the requested index.
     * There must be as many values provided as there are fields in the index and
     * these values must correspond to the index field types.
     */
    values: IndexValue[];
}
/** GetResponse is the Query/Get response type. */
export interface GetResponse {
    /**
     * result is the result of the get query. If no value is found, the gRPC
     * status code NOT_FOUND will be returned.
     */
    result: Any | undefined;
}
/** ListRequest is the Query/List request type. */
export interface ListRequest {
    /** message_name is the fully-qualified message name of the ORM table being queried. */
    messageName: string;
    /**
     * index is the index fields expression used in orm definitions. If it
     * is empty, the table's primary key is assumed.
     */
    index: string;
    /** prefix defines a prefix query. */
    prefix?: ListRequest_Prefix | undefined;
    /** range defines a range query. */
    range?: ListRequest_Range | undefined;
    /** pagination is the pagination request. */
    pagination: PageRequest | undefined;
}
/** Prefix specifies the arguments to a prefix query. */
export interface ListRequest_Prefix {
    /**
     * values specifies the index values for the prefix query.
     * It is valid to special a partial prefix with fewer values than
     * the number of fields in the index.
     */
    values: IndexValue[];
}
/** Range specifies the arguments to a range query. */
export interface ListRequest_Range {
    /**
     * start specifies the starting index values for the range query.
     * It is valid to provide fewer values than the number of fields in the
     * index.
     */
    start: IndexValue[];
    /**
     * end specifies the inclusive ending index values for the range query.
     * It is valid to provide fewer values than the number of fields in the
     * index.
     */
    end: IndexValue[];
}
/** ListResponse is the Query/List response type. */
export interface ListResponse {
    /** results are the results of the query. */
    results: Any[];
    /** pagination is the pagination response. */
    pagination: PageResponse | undefined;
}
/** IndexValue represents the value of a field in an ORM index expression. */
export interface IndexValue {
    /**
     * uint specifies a value for an uint32, fixed32, uint64, or fixed64
     * index field.
     */
    uint?: string | undefined;
    /**
     * int64 specifies a value for an int32, sfixed32, int64, or sfixed64
     * index field.
     */
    int?: string | undefined;
    /** str specifies a value for a string index field. */
    str?: string | undefined;
    /** bytes specifies a value for a bytes index field. */
    bytes?: Uint8Array | undefined;
    /** enum specifies a value for an enum index field. */
    enum?: string | undefined;
    /** bool specifies a value for a bool index field. */
    bool?: boolean | undefined;
    /** timestamp specifies a value for a timestamp index field. */
    timestamp?: Date | undefined;
    /** duration specifies a value for a duration index field. */
    duration?: Duration | undefined;
}
export declare const GetRequest: {
    encode(message: GetRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetRequest;
    fromJSON(object: any): GetRequest;
    toJSON(message: GetRequest): unknown;
    create(base?: DeepPartial<GetRequest>): GetRequest;
    fromPartial(object: DeepPartial<GetRequest>): GetRequest;
};
export declare const GetResponse: {
    encode(message: GetResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetResponse;
    fromJSON(object: any): GetResponse;
    toJSON(message: GetResponse): unknown;
    create(base?: DeepPartial<GetResponse>): GetResponse;
    fromPartial(object: DeepPartial<GetResponse>): GetResponse;
};
export declare const ListRequest: {
    encode(message: ListRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListRequest;
    fromJSON(object: any): ListRequest;
    toJSON(message: ListRequest): unknown;
    create(base?: DeepPartial<ListRequest>): ListRequest;
    fromPartial(object: DeepPartial<ListRequest>): ListRequest;
};
export declare const ListRequest_Prefix: {
    encode(message: ListRequest_Prefix, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListRequest_Prefix;
    fromJSON(object: any): ListRequest_Prefix;
    toJSON(message: ListRequest_Prefix): unknown;
    create(base?: DeepPartial<ListRequest_Prefix>): ListRequest_Prefix;
    fromPartial(object: DeepPartial<ListRequest_Prefix>): ListRequest_Prefix;
};
export declare const ListRequest_Range: {
    encode(message: ListRequest_Range, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListRequest_Range;
    fromJSON(object: any): ListRequest_Range;
    toJSON(message: ListRequest_Range): unknown;
    create(base?: DeepPartial<ListRequest_Range>): ListRequest_Range;
    fromPartial(object: DeepPartial<ListRequest_Range>): ListRequest_Range;
};
export declare const ListResponse: {
    encode(message: ListResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListResponse;
    fromJSON(object: any): ListResponse;
    toJSON(message: ListResponse): unknown;
    create(base?: DeepPartial<ListResponse>): ListResponse;
    fromPartial(object: DeepPartial<ListResponse>): ListResponse;
};
export declare const IndexValue: {
    encode(message: IndexValue, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): IndexValue;
    fromJSON(object: any): IndexValue;
    toJSON(message: IndexValue): unknown;
    create(base?: DeepPartial<IndexValue>): IndexValue;
    fromPartial(object: DeepPartial<IndexValue>): IndexValue;
};
/** Query is a generic gRPC service for querying ORM data. */
export interface Query {
    /** Get queries an ORM table against an unique index. */
    Get(request: DeepPartial<GetRequest>, metadata?: grpc.Metadata): Promise<GetResponse>;
    /** List queries an ORM table against an index. */
    List(request: DeepPartial<ListRequest>, metadata?: grpc.Metadata): Promise<ListResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Get(request: DeepPartial<GetRequest>, metadata?: grpc.Metadata): Promise<GetResponse>;
    List(request: DeepPartial<ListRequest>, metadata?: grpc.Metadata): Promise<ListResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryGetDesc: UnaryMethodDefinitionish;
export declare const QueryListDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
