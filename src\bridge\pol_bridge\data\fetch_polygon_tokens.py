#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import requests
from typing import Dict, List, Optional, Any

# API基础URL
BASE_URL = "https://app.geckoterminal.com/api/p1/polygon_pos/pools"

# 保存文件的路径
OUTPUT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "polygon_tokens.json")

def fetch_page(page_number: int) -> Optional[Dict[str, Any]]:
    """
    获取指定页码的数据
    
    Args:
        page_number: 页码
        
    Returns:
        获取到的数据，如果获取失败则返回None
    """
    params = {
        "include": "dex,dex.network,dex.network.network_metric,tokens",
        "page": page_number,
        "include_network_metrics": "true",
        "include_meta": "1",
        "volume_24h[gte]": "18",
        "volume_24h[lte]": "288888",
        "liquidity[gte]": "588",
        "tx_count_24h[lte]": "588",
        "networks": "polygon_pos"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
    }
    
    for attempt in range(3):  # 重试3次
        try:
            response = requests.get(BASE_URL, params=params, headers=headers, timeout=30)
            response.raise_for_status()  # 检查HTTP错误
            return response.json()
        except requests.RequestException as e:
            print(f"获取第 {page_number} 页数据失败 (尝试 {attempt+1}/3): {e}")
            time.sleep(5)  # 失败后等待5秒再重试
    
    return None

def extract_tokens(data: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    从API响应中提取代币信息
    
    Args:
        data: API响应数据
        
    Returns:
        代币信息列表
    """
    tokens = []
    
    # 检查included字段中是否包含token数据
    if "included" in data:
        for item in data["included"]:
            if item["type"] == "token":
                attr = item["attributes"]
                token_info = {
                    "symbol": attr.get("symbol", ""),
                    "name": attr.get("name", ""),
                    "address": attr.get("address", "").lower()  # 统一转为小写
                }
                tokens.append(token_info)
    
    return tokens

def save_tokens(tokens: List[Dict[str, str]]) -> None:
    """
    保存代币信息到文件
    
    Args:
        tokens: 代币信息列表
    """
    # 读取现有数据（如果有）
    existing_tokens = {}
    if os.path.exists(OUTPUT_FILE):
        try:
            with open(OUTPUT_FILE, "r", encoding="utf-8") as f:
                existing_tokens = json.load(f)
        except json.JSONDecodeError:
            print(f"警告: {OUTPUT_FILE} 文件格式无效，将创建新文件")
    
    # 合并新数据，使用地址作为键以避免重复
    for token in tokens:
        address = token.get("address", "")
        if address and address not in existing_tokens:
            existing_tokens[address] = token
    
    # 保存回文件
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        json.dump(existing_tokens, f, indent=2, ensure_ascii=False)
    
    print(f"已保存 {len(existing_tokens)} 个唯一代币到 {OUTPUT_FILE}")

def main():
    """主函数"""
    print("开始获取Polygon代币数据...")
    
    all_tokens = []
    page_number = 1
    
    while True:
        print(f"正在获取第 {page_number} 页数据...")
        data = fetch_page(page_number)
        
        if not data:
            print(f"无法获取第 {page_number} 页数据，程序结束")
            break
        
        tokens = extract_tokens(data)
        all_tokens.extend(tokens)
        
        print(f"第 {page_number} 页: 新增 {len(tokens)} 个代币，当前共 {len(all_tokens)} 个代币")
        
        # 检查是否有下一页
        if "links" in data and data["links"].get("next"):
            page_number += 1
            print(f"等待 1 秒后获取下一页...")
            time.sleep(1)  # 等待1秒再请求下一页
        else:
            print("已到达最后一页，程序结束")
            break
    
    # 保存所有收集到的代币信息
    save_tokens(all_tokens)
    print("程序执行完毕")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}") 