{"name": "class-is", "version": "1.1.0", "description": "Enhances a JavaScript class by adding an is<Class> property to compare types between realms.", "keywords": ["withis", "with-is", "isclass", "is-class", "symbols", "realms", "instanceof", "instance-of"], "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/moxystudio/js-class-is", "repository": {"type": "git", "url": "**************:moxystudio/js-class-is.git"}, "license": "MIT", "main": "index.js", "scripts": {"lint": "eslint .", "test": "jest --env node --coverage", "prerelease": "npm t && npm run lint", "release": "standard-version", "precommit": "lint-staged", "commitmsg": "commitlint -e $GIT_PARAMS"}, "standard-version": {"scripts": {"posttag": "git push --follow-tags origin master && npm publish"}}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@commitlint/cli": "^6.1.3", "@commitlint/config-conventional": "^6.1.3", "eslint": "^4.19.1", "eslint-config-moxy": "^5.2.1", "husky": "^0.14.3", "jest": "^22.4.3", "lint-staged": "^7.0.0", "standard-version": "^4.3.0"}, "dependencies": {}}