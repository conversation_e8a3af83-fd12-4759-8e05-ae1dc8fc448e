import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { ModuleOptions } from "./options";
export declare const protobufPackage = "cosmos.autocli.v1";
/** AppOptionsRequest is the RemoteInfoService/AppOptions request type. */
export interface AppOptionsRequest {
}
/** AppOptionsResponse is the RemoteInfoService/AppOptions response type. */
export interface AppOptionsResponse {
    /** module_options is a map of module name to autocli module options. */
    moduleOptions: {
        [key: string]: ModuleOptions;
    };
}
export interface AppOptionsResponse_ModuleOptionsEntry {
    key: string;
    value: ModuleOptions | undefined;
}
export declare const AppOptionsRequest: {
    encode(_: AppOptionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AppOptionsRequest;
    fromJSON(_: any): AppOptionsRequest;
    toJSON(_: AppOptionsRequest): unknown;
    create(base?: DeepPartial<AppOptionsRequest>): AppOptionsRequest;
    fromPartial(_: DeepPartial<AppOptionsRequest>): AppOptionsRequest;
};
export declare const AppOptionsResponse: {
    encode(message: AppOptionsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AppOptionsResponse;
    fromJSON(object: any): AppOptionsResponse;
    toJSON(message: AppOptionsResponse): unknown;
    create(base?: DeepPartial<AppOptionsResponse>): AppOptionsResponse;
    fromPartial(object: DeepPartial<AppOptionsResponse>): AppOptionsResponse;
};
export declare const AppOptionsResponse_ModuleOptionsEntry: {
    encode(message: AppOptionsResponse_ModuleOptionsEntry, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AppOptionsResponse_ModuleOptionsEntry;
    fromJSON(object: any): AppOptionsResponse_ModuleOptionsEntry;
    toJSON(message: AppOptionsResponse_ModuleOptionsEntry): unknown;
    create(base?: DeepPartial<AppOptionsResponse_ModuleOptionsEntry>): AppOptionsResponse_ModuleOptionsEntry;
    fromPartial(object: DeepPartial<AppOptionsResponse_ModuleOptionsEntry>): AppOptionsResponse_ModuleOptionsEntry;
};
/**
 * RemoteInfoService provides clients with the information they need
 * to build dynamically CLI clients for remote chains.
 */
export interface Query {
    /** AppOptions returns the autocli options for all of the modules in an app. */
    AppOptions(request: DeepPartial<AppOptionsRequest>, metadata?: grpc.Metadata): Promise<AppOptionsResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    AppOptions(request: DeepPartial<AppOptionsRequest>, metadata?: grpc.Metadata): Promise<AppOptionsResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryAppOptionsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
