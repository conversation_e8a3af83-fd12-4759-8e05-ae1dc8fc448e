"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgChangeAdmin extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgChangeAdmin(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgChangeAdmin.create();
        message.sender = params.sender;
        message.denom = params.denom;
        message.newAdmin = params.newAdmin;
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgChangeAdmin.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgChangeAdmin',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'injective/tokenfactory/change-admin',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgChangeAdmin',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.tokenfactory.v1beta1.MsgChangeAdmin',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgChangeAdmin.encode(this.toProto()).finish();
    }
}
exports.default = MsgChangeAdmin;
