{"version": 3, "file": "block.js", "sourceRoot": "", "sources": ["../../src/block.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAA;AAClD,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AACrC,OAAO,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAA;AACvC,OAAO,EAAE,sBAAsB,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAA;AACvF,OAAO,EACL,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACb,oBAAoB,EACpB,cAAc,EACd,aAAa,EACb,mBAAmB,EACnB,UAAU,EACV,iBAAiB,EACjB,WAAW,EACX,UAAU,EACV,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,WAAW,EACX,UAAU,EACV,QAAQ,EACR,WAAW,GACZ,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAA;AAE3D,OAAO,EAAE,iCAAiC,EAAE,MAAM,0BAA0B,CAAA;AAC5E,OAAO,EAAE,YAAY,EAAE,MAAM,eAAe,CAAA;AAC5C,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAA;AA+BzC;;GAEG;AACH,MAAM,OAAO,KAAK;IAudhB;;;OAGG;IACH,YACE,MAAoB,EACpB,eAAmC,EAAE,EACrC,eAA8B,EAAE,EAChC,WAA0B,EAC1B,OAAqB,EAAE,EACvB,QAAqC,EACrC,gBAAgD;QAhelC,iBAAY,GAAuB,EAAE,CAAA;QACrC,iBAAY,GAAkB,EAAE,CAAA;QAatC,UAAK,GAIX,EAAE,CAAA;QAgdJ,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAC5D,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;QAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,SAAS,CAAA;QAErE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QACrF,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACxC,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;QAC/E,mEAAmE;QACnE,6FAA6F;QAC7F,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YAC3E,IAAI,CAAC,gBAAgB,GAAG;gBACtB,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE;oBACX,iBAAiB,EAAE,EAAE;oBACrB,CAAC,EAAE,IAAI;oBACP,qBAAqB,EAAE,IAAI;oBAC3B,QAAQ,EAAE;wBACR,EAAE,EAAE,EAAE;wBACN,EAAE,EAAE,EAAE;wBACN,eAAe,EAAE,IAAI;qBACtB;oBACD,UAAU,EAAE,EAAE;iBACf;aACF,CAAA;SACF;QAED,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAA;YACrB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,aAAa,CAAC,gBAAgB,EAAE;gBAClE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,wEAAwE,CACzE,CAAA;gBACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;aACrB;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,aAAa,CAAC,YAAY,EAAE;gBAC9D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,wEAAwE,CACzE,CAAA;gBACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;aACrB;SACF;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,WAAW,KAAK,SAAS,EAAE;YAClE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAA;SAC7E;QAED,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YACjC,gBAAgB,KAAK,SAAS;YAC9B,gBAAgB,KAAK,IAAI,EACzB;YACA,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;SACjF;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;SACxE;QAED,2EAA2E;QAC3E,sEAAsE;QACtE,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;oBACzC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;aAChE;SACF;QACD,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAthBD;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAiB,EAAE,SAAgB;QAC5E,MAAM,IAAI,GAAG,SAAS,IAAI,IAAI,IAAI,EAAE,CAAA;QACpC,KAAK,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;YACnC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;SACpD;QACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;IACpB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAuB,EAAE,SAAgB;QACnF,MAAM,IAAI,GAAG,SAAS,IAAI,IAAI,IAAI,EAAE,CAAA;QACpC,KAAK,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,EAAE;YACnC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,EAAE,CAAC,CAAA;SAC9C;QACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;IACpB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,QAAoC,EAAE,SAAgB;QAC5F,2EAA2E;QAC3E,sEAAsE;QACtE,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACxC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;oBACzC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;aAChE;SACF;QACD,MAAM,IAAI,GAAG,SAAS,IAAI,IAAI,IAAI,EAAE,CAAA;QACpC,KAAK,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;YACzC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAA;SAC/C;QACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;IACpB,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,YAAuB,EAAE,EAAE,IAAmB;QACxE,MAAM,EACJ,MAAM,EAAE,UAAU,EAClB,YAAY,EAAE,OAAO,EACrB,YAAY,EAAE,OAAO,EACrB,WAAW,EAAE,eAAe,EAC5B,gBAAgB,EAAE,oBAAoB,EACtC,QAAQ,EAAE,UAAU,GACrB,GAAG,SAAS,CAAA;QAEb,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAE3D,qBAAqB;QACrB,MAAM,YAAY,GAAG,EAAE,CAAA;QACvB,KAAK,MAAM,MAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YAClC,MAAM,EAAE,GAAG,kBAAkB,CAAC,UAAU,CAAC,MAAM,EAAE;gBAC/C,GAAG,IAAI;gBACP,2DAA2D;gBAC3D,MAAM,EAAE,MAAM,CAAC,MAAM;aACT,CAAC,CAAA;YACf,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACtB;QAED,sBAAsB;QACtB,MAAM,YAAY,GAAG,EAAE,CAAA;QACvB,MAAM,SAAS,GAAiB;YAC9B,GAAG,IAAI;YACP,2DAA2D;YAC3D,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,iIAAiI;YACjI,wBAAwB,EAAE,SAAS;SACpC,CAAA;QACD,6EAA6E;QAC7E,IAAI,IAAI,EAAE,WAAW,KAAK,SAAS,EAAE;YACnC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAA;SAC7B;QACD,KAAK,MAAM,MAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YAClC,MAAM,EAAE,GAAG,WAAW,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;YACxD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;SACtB;QAED,MAAM,WAAW,GAAG,eAAe,EAAE,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAA;QACvE,4EAA4E;QAC5E,sBAAsB;QACtB,MAAM,gBAAgB,GAAG,oBAAoB,CAAA;QAE7C,OAAO,IAAI,KAAK,CACd,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,IAAI,EACJ,UAAU,EACV,gBAAgB,CACjB,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,sBAAsB,CAAC,UAAsB,EAAE,IAAmB;QAC9E,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAe,CAAA;QAEpE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAA;SACjE;QAED,OAAO,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAkB,EAAE,IAAmB;QACnE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,MAAM,IAAI,KAAK,CACb,8BAA8B,MAAM,CAAC,MAAM,0CAA0C,CACtF,CAAA;SACF;QAED,kGAAkG;QAClG,4CAA4C;QAC5C,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,GAAG,MAAM,CAAA;QAC5D,MAAM,MAAM,GAAG,WAAW,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAE5D,qFAAqF;QACrF,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YACxD,CAAC,CAAE,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAsB;YAClD,CAAC,CAAC,SAAS,CAAA;QACb,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YACrD,CAAC,CAAE,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAmB;YAC/C,CAAC,CAAC,SAAS,CAAA;QACb,4FAA4F;QAC5F,iFAAiF;QACjF,MAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YAC9D,CAAC,CAAE,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAA2B;YACvD,CAAC,CAAC,IAAI,CAAA;QAER,IACE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YAClC,CAAC,eAAe,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,EAClE;YACA,MAAM,IAAI,KAAK,CACb,+FAA+F,CAChG,CAAA;SACF;QAED,IACE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YAClC,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAC5D;YACA,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAA;SACF;QAED,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,qBAAqB,KAAK,SAAS,EAAE;YAC7E,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAA;SACF;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,EAAE,CAAA;QACvB,KAAK,MAAM,MAAM,IAAI,OAAO,IAAI,EAAE,EAAE;YAClC,YAAY,CAAC,IAAI,CACf,kBAAkB,CAAC,iBAAiB,CAAC,MAAM,EAAE;gBAC3C,GAAG,IAAI;gBACP,2DAA2D;gBAC3D,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CACH,CAAA;SACF;QAED,sBAAsB;QACtB,MAAM,YAAY,GAAG,EAAE,CAAA;QACvB,MAAM,SAAS,GAAiB;YAC9B,GAAG,IAAI;YACP,2DAA2D;YAC3D,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,iIAAiI;YACjI,wBAAwB,EAAE,SAAS;SACpC,CAAA;QACD,6EAA6E;QAC7E,IAAI,IAAI,EAAE,WAAW,KAAK,SAAS,EAAE;YACnC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAA;SAC7B;QACD,KAAK,MAAM,eAAe,IAAI,OAAO,IAAI,EAAE,EAAE;YAC3C,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAA;SAC3E;QAED,MAAM,WAAW,GAAI,eAAqC;YACxD,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACnD,KAAK;YACL,cAAc;YACd,OAAO;YACP,MAAM;SACP,CAAC,CAAC;YACH,EAAE,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAA;QAEtC,IAAI,QAAQ,CAAA;QACZ,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACtC,QAAQ,GAAI,YAA+B,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACxD,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAC9C,CAAA;SACF;QACD,gFAAgF;QAChF,0EAA0E;QAC1E,IAAI,gBAAgB,CAAA;QACpB,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,qBAAqB,KAAK,SAAS,EAAE;gBACvC,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,qBAAqB,CAAe,CAAC,CAAC,CAAA;aAC5F;iBAAM,IAAI,IAAI,EAAE,gBAAgB,KAAK,SAAS,EAAE;gBAC/C,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;aACzC;iBAAM;gBACL,mFAAmF;gBACnF,yFAAyF;gBACzF,gBAAgB,GAAG,IAAI,CAAA;aACxB;SACF;QAED,OAAO,IAAI,KAAK,CACd,MAAM,EACN,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,IAAI,EACJ,QAAQ,EACR,gBAAgB,CACjB,CAAA;IACH,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,OAAO,CAAC,SAAuB,EAAE,MAAc,EAAE,IAAmB;QAChF,OAAO,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC9C,CAAC;IA+DD;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACtC,OAAyB,EACzB,IAAmB;QAEnB,MAAM,EACJ,WAAW,EAAE,MAAM,EACnB,YAAY,EAAE,WAAW,EACzB,UAAU,EAAE,OAAO,EACnB,YAAY,EAAE,QAAQ,EACtB,YAAY,EACZ,WAAW,EAAE,eAAe,EAC5B,eAAe,EACf,kBAAkB,EAClB,qBAAqB,EACrB,gBAAgB,GACjB,GAAG,OAAO,CAAA;QAEX,MAAM,GAAG,GAAG,EAAE,CAAA;QACd,KAAK,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,EAAE;YAC1D,IAAI;gBACF,MAAM,EAAE,GAAG,kBAAkB,CAAC,kBAAkB,CAC9C,UAAU,CAAC,YAAiC,CAAC,EAC7C;oBACE,MAAM,EAAE,IAAI,EAAE,MAAM;iBACrB,CACF,CAAA;gBACD,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACb;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,eAAe,GAAG,uBAAuB,KAAK,KAAK,KAAK,EAAE,CAAA;gBAChE,MAAM,eAAe,CAAA;aACtB;SACF;QAED,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,uBAAuB,CAC1D,GAAG,EACH,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CACnC,CAAA;QACD,MAAM,WAAW,GAAG,eAAe,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAA;QACzF,MAAM,eAAe,GAAG,WAAW;YACjC,CAAC,CAAC,MAAM,KAAK,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YACrF,CAAC,CAAC,SAAS,CAAA;QAEb,MAAM,kBAAkB,GAAG,eAAe,KAAK,SAAS,IAAI,eAAe,KAAK,IAAI,CAAA;QACpF,MAAM,qBAAqB,GAAG,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,KAAK,IAAI,CAAA;QAC7F,MAAM,wBAAwB,GAC5B,qBAAqB,KAAK,SAAS,IAAI,qBAAqB,KAAK,IAAI,CAAA;QAEvE,MAAM,QAAQ,GACZ,kBAAkB,IAAI,qBAAqB,IAAI,wBAAwB;YACrE,CAAC,CAAE,EAAiC;YACpC,CAAC,CAAC,SAAS,CAAA;QAEf,IAAI,eAAe,KAAK,SAAS,IAAI,eAAe,KAAK,IAAI,EAAE;YAC7D,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;gBACnC,QAAS,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;aAC/C;SACF;QACD,IAAI,kBAAkB,KAAK,SAAS,IAAI,kBAAkB,KAAK,IAAI,EAAE;YACnE,KAAK,MAAM,KAAK,IAAI,kBAAkB,EAAE;gBACtC,QAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;aAClD;SACF;QACD,IAAI,qBAAqB,KAAK,SAAS,IAAI,qBAAqB,KAAK,IAAI,EAAE;YACzE,KAAK,MAAM,KAAK,IAAI,qBAAqB,EAAE;gBACzC,QAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;aACrD;SACF;QAED,MAAM,YAAY,GAAG,QAAQ;YAC3B,CAAC,CAAC,MAAM,KAAK,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;YAC/E,CAAC,CAAC,SAAS,CAAA;QAEb,MAAM,MAAM,GAAe;YACzB,GAAG,OAAO;YACV,MAAM;YACN,WAAW;YACX,gBAAgB;YAChB,eAAe;YACf,OAAO;YACP,QAAQ;YACR,YAAY;SACb,CAAA;QAED,4EAA4E;QAC5E,MAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAC/B,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,EACtE,IAAI,CACL,CAAA;QACD,IACE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;YACjC,CAAC,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,IAAI,CAAC,EAC7D;YACA,MAAM,KAAK,CAAC,kEAAkE,CAAC,CAAA;SAChF;QACD,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,OAAO,CAAC,SAA8B,CAAC,CAAC,EAAE;YAClF,MAAM,eAAe,GAAG,gCACtB,OAAO,CAAC,SACV,eAAe,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAA;YACzC,MAAM,KAAK,CAAC,eAAe,CAAC,CAAA;SAC7B;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACvC,OAA0B,EAC1B,IAAmB;QAEnB,MAAM,gBAAgB,GAAG,iCAAiC,CAAC,OAAO,CAAC,CAAA;QACnE,OAAO,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;IAC3D,CAAC;IAyFD;;OAEG;IACH,GAAG;QACD,MAAM,UAAU,GAAe;YAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAC3B,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAC5D;YACjB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;SACxC,CAAA;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;QAC9D,IAAI,cAAc,EAAE;YAClB,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;SAChC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAA;QAChE,IAAI,WAAW,EAAE;YACf,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;SAC7B;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;YACzE,MAAM,qBAAqB,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA;YAC/E,UAAU,CAAC,IAAI,CAAC,qBAA4B,CAAC,CAAA;SAC9C;QACD,OAAO,UAAU,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,IAAI;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACb,OAAO,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;IAC5F,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,uBAAuB;QAC3B,IAAI,MAAM,CAAA;QACV,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAA;YACjE,OAAO,MAAM,CAAA;SACd;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA;SAC/C;QACD,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAA;QACzE,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,aAA0C;QAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;SAC7C;QAED,MAAM,QAAQ,GAAG,aAAa,IAAI,IAAI,CAAC,QAAS,CAAA;QAEhD,IAAI,QAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,YAAa,EAAE,aAAa,CAAC,CAAA;SAC7D;QAED,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,KAAK,SAAS,EAAE;gBACzC,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAS,CAAC,CAAA;aAC1E;YACD,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,CAAA;SACvE;aAAM;YACL,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAA;YAC9D,OAAO,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,CAAA;SAC5D;IACH,CAAC;IACD;;;OAGG;IACH,+BAA+B;QAC7B,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,IAAI,WAAW,GAAG,QAAQ,CAAA;QAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;QACzE,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QAEvE,wCAAwC;QACxC,KAAK,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;YAC/C,MAAM,IAAI,GAAG,EAAE,CAAC,mBAAmB,EAAE,CAAA;YACrC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;oBAC5C,EAAE,GAAG,EAAiC,CAAA;oBACtC,IAAI,EAAE,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,aAAc,EAAE;wBAChD,IAAI,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAA;qBACrD;iBACF;qBAAM;oBACL,EAAE,GAAG,EAAuB,CAAA;oBAC5B,IAAI,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,aAAc,EAAE;wBAC5C,IAAI,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAA;qBACzD;iBACF;aACF;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,EAAE,YAAY,sBAAsB,EAAE;oBACxC,WAAW,IAAI,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,cAAc,CAAA;oBACrD,IAAI,WAAW,GAAG,YAAY,EAAE;wBAC9B,IAAI,CAAC,IAAI,CACP,+BAA+B,WAAW,4CAA4C,YAAY,EAAE,CACrG,CAAA;qBACF;iBACF;aACF;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACnB,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;aACrD;SACF;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAI,WAAW,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBAC3C,MAAM,CAAC,IAAI,CAAC,gCAAgC,IAAI,CAAC,MAAM,CAAC,WAAW,WAAW,WAAW,EAAE,CAAC,CAAA;aAC7F;SACF;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,oBAAoB;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,+BAA+B,EAAE,CAAA;QAErD,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,CAAC,aAAsB,KAAK,EAAE,YAAqB,IAAI;QACvE,IAAI,SAAS,EAAE;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,+BAA+B,EAAE,CAAA;YACvD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,yBAAyB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;gBACzE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;aACrB;SACF;QAED,IAAI,UAAU,EAAE;YACd,OAAM;SACP;QAED,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBACrD,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE;oBAClB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8CAA8C,KAAK,cAAc,CAClE,CAAA;oBACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;iBACrB;aACF;SACF;QAED,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC,EAAE;YAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;YACtD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAA;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC,EAAE;YAC9E,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;YACtD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,+BAA+B;QAC/B,kGAAkG;QAClG,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;aAC3D;YACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;gBAClC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAA;aACrF;SACF;IACH,CAAC;IAED;;;;;OAKG;IACH,wBAAwB,CAAC,YAAyB;QAChD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,oBAAoB,CAAC,CAAA;YACzE,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;YACvE,IAAI,WAAW,GAAG,QAAQ,CAAA;YAE1B,MAAM,qBAAqB,GAAG,YAAY,CAAC,qBAAqB,EAAE,CAAA;YAClE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,KAAK,qBAAqB,EAAE;gBACvD,MAAM,IAAI,KAAK,CACb,sCAAsC,IAAI,CAAC,MAAM,CAAC,aAAa,UAAU,qBAAqB,EAAE,CACjG,CAAA;aACF;YAED,IAAI,YAAY,CAAA;YAEhB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;gBAClC,IAAI,EAAE,YAAY,sBAAsB,EAAE;oBACxC,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAA;oBAC5D,IAAI,EAAE,CAAC,gBAAgB,GAAG,YAAY,EAAE;wBACtC,MAAM,IAAI,KAAK,CACb,qCACE,EAAE,CAAC,gBACL,gCAAgC,YAAY,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,CACpE,CAAA;qBACF;oBAED,WAAW,IAAI,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,cAAc,CAAA;oBAErE,IAAI,WAAW,GAAG,YAAY,EAAE;wBAC9B,MAAM,IAAI,KAAK,CACb,+BAA+B,WAAW,4CAA4C,YAAY,EAAE,CACrG,CAAA;qBACF;iBACF;aACF;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,WAAW,EAAE;gBAC3C,MAAM,IAAI,KAAK,CACb,oCAAoC,IAAI,CAAC,MAAM,CAAC,WAAW,UAAU,WAAW,EAAE,CACnF,CAAA;aACF;SACF;IACH,CAAC;IAED;;;OAGG;IACH,gBAAgB;QACd,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,OAAO,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;SAC/D;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAA;QACtD,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC9B,OAAO,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;IACrE,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,sBAAsB;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAA;SAC7C;QAED,IAAI,MAAM,CAAA;QACV,IAAI,IAAI,CAAC,WAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAClC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,eAAgB,EAAE,aAAa,CAAC,CAAA;YACjE,OAAO,MAAM,CAAA;SACd;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,KAAK,SAAS,EAAE;YAChD,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG,MAAM,KAAK,CAAC,sBAAsB,CACjE,IAAI,CAAC,WAAY,EACjB,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAClC,CAAA;SACF;QACD,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,eAAgB,CAAC,CAAA;QAClF,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;;;;;OAQG;IACH,cAAc;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;YACpB,OAAM;SACP;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;YACpD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,wCAAwC;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QAChF,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,CAAC,EAAE;YACvD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAA;YAC9C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED;;;;OAIG;IACH,yBAAyB,CAAC,WAAkB;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IAClE,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,WAAkB;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW;YACtC,CAAC,CAAC;gBACE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;aACvD;YACH,CAAC,CAAC,EAAE,CAAA;QACN,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YAC5B,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;YACxD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;YACxD,GAAG,eAAe;YAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;SACnE,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACH,kBAAkB;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAA;QAC/B,MAAM,MAAM,GAAG,SAAS,CAAC,MAAO,CAAA;QAChC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;QACpF,MAAM,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAE1F,MAAM,gBAAgB,GAAqB;YACzC,WAAW,EAAE,MAAM,CAAC,MAAO;YAC3B,UAAU,EAAE,MAAM,CAAC,UAAW;YAC9B,YAAY,EAAE,MAAM,CAAC,QAAS;YAC9B,SAAS,EAAE,MAAM,CAAC,SAAU;YAC5B,YAAY,EAAE,MAAM,CAAC,WAAY;YACjC,SAAS,EAAE,MAAM,CAAC,SAAU;YAC5B,QAAQ,EAAE,MAAM,CAAC,QAAS;YAC1B,OAAO,EAAE,MAAM,CAAC,OAAQ;YACxB,SAAS,EAAE,MAAM,CAAC,SAAU;YAC5B,SAAS,EAAE,MAAM,CAAC,SAAU;YAC5B,aAAa,EAAE,MAAM,CAAC,aAAc;YACpC,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAClC,UAAU,EAAE,MAAM,CAAC,OAAQ;YAC3B,YAAY;YACZ,GAAG,cAAc;YACjB,qBAAqB,EAAE,MAAM,CAAC,qBAAqB;YACnD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YAEvC,oFAAoF;YACpF,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YAClE,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;YACrE,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS;SACzE,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACnC,QAAQ,OAAO,CAAC,IAAI,EAAE;oBACpB,KAAK,aAAa,CAAC,OAAO;wBACxB,gBAAgB,CAAC,eAAgB,CAAC,IAAI,CAAE,OAA0B,CAAC,MAAM,EAAE,CAAC,CAAA;wBAC5E,SAAQ;oBAEV,KAAK,aAAa,CAAC,UAAU;wBAC3B,gBAAgB,CAAC,kBAAmB,CAAC,IAAI,CAAE,OAA6B,CAAC,MAAM,EAAE,CAAC,CAAA;wBAClF,SAAQ;oBAEV,KAAK,aAAa,CAAC,aAAa;wBAC9B,gBAAgB,CAAC,qBAAsB,CAAC,IAAI,CAAE,OAAgC,CAAC,MAAM,EAAE,CAAC,CAAA;wBACxF,SAAQ;iBACX;aACF;SACF;aAAM,IACL,gBAAgB,CAAC,eAAe,KAAK,SAAS;YAC9C,gBAAgB,CAAC,kBAAkB,KAAK,SAAS;YACjD,gBAAgB,CAAC,qBAAqB,KAAK,SAAS,EACpD;YACA,MAAM,KAAK,CAAC,iEAAiE,CAAC,CAAA;SAC/E;QAED,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,IAAI;YACF,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;SAC/B;QAAC,OAAO,CAAM,EAAE;YACf,IAAI,GAAG,OAAO,CAAA;SACf;QACD,IAAI,EAAE,GAAG,EAAE,CAAA;QACX,IAAI;YACF,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;SAC5B;QAAC,OAAO,CAAM,EAAE;YACf,EAAE,GAAG,OAAO,CAAA;SACb;QACD,IAAI,QAAQ,GAAG,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,SAAS,IAAI,GAAG,CAAA;QACjE,QAAQ,IAAI,MAAM,EAAE,kBAAkB,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,GAAG,CAAA;QAC5E,QAAQ,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAA;QAChF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;;;AAnuBD;;;;;;GAMG;AACW,yBAAmB,GAAG,KAAK,EACvC,QAAiC,EACjC,QAAyB,EACzB,IAAkB,EAClB,EAAE;IACF,IAAI,SAAS,CAAA;IACb,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAA;IAEzC,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,EAAE,EAAE;QAC1D,SAAS,GAAG,MAAM,iBAAiB,CAAC,WAAW,EAAE;YAC/C,MAAM,EAAE,oBAAoB;YAC5B,MAAM,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;SACzB,CAAC,CAAA;KACH;SAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QACvC,SAAS,GAAG,MAAM,iBAAiB,CAAC,WAAW,EAAE;YAC/C,MAAM,EAAE,sBAAsB;YAC9B,MAAM,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;SACtC,CAAC,CAAA;KACH;SAAM,IACL,WAAW,CAAC,QAAQ,CAAC;QACrB,QAAQ,KAAK,QAAQ;QACrB,QAAQ,KAAK,UAAU;QACvB,QAAQ,KAAK,SAAS;QACtB,QAAQ,KAAK,WAAW;QACxB,QAAQ,KAAK,MAAM,EACnB;QACA,SAAS,GAAG,MAAM,iBAAiB,CAAC,WAAW,EAAE;YAC/C,MAAM,EAAE,sBAAsB;YAC9B,MAAM,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC;SACzB,CAAC,CAAA;KACH;SAAM;QACL,MAAM,IAAI,KAAK,CACb,oGAAoG,QAAQ,EAAE,CAC/G,CAAA;KACF;IAED,IAAI,SAAS,KAAK,IAAI,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;KACxD;IAED,MAAM,YAAY,GAAG,EAAE,CAAA;IACvB,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,MAAM,UAAU,GAAG,MAAM,iBAAiB,CAAC,WAAW,EAAE;gBACtD,MAAM,EAAE,iCAAiC;gBACzC,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;aACtC,CAAC,CAAA;YACF,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;SAC9B;KACF;IAED,OAAO,YAAY,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;AACpD,CAAC,CAAA"}