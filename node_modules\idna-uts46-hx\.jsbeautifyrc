{"js": {"indent_size": 2, "indent_char": " ", "eol": "\n", "indent_level": 0, "indent_with_tabs": false, "preserve_newlines": true, "max_preserve_newlines": 2, "jslint_happy": false, "space_after_anon_function": false, "keep_array_indentation": false, "keep_function_indentation": false, "space_before_conditional": true, "break_chained_methods": false, "eval_code": false, "unescape_strings": false, "wrap_line_length": 0, "wrap_attributes": "auto", "end_with_newline": true, "brace_style": "end-expand", "end_with_comma": false, "space_in_paren": false}, "json": {"indent_size": 2, "indent_char": " ", "eol": "\n", "indent_level": 0, "indent_with_tabs": false, "preserve_newlines": true, "max_preserve_newlines": 2, "jslint_happy": false, "space_after_anon_function": false, "keep_array_indentation": false, "keep_function_indentation": false, "space_before_conditional": true, "break_chained_methods": false, "eval_code": false, "unescape_strings": false, "wrap_line_length": 0, "wrap_attributes": "auto", "end_with_newline": true, "brace_style": "end-expand", "end_with_comma": false, "space_in_paren": false}, "html": {"brace_style": "collapse", "indent_char": " ", "indent_size": 2, "indent_with_tabs": false, "preserve_newlines": true, "max_preserve_newlines": 1, "space_after_anon_function": false, "wrap_line_length": 0, "indent_scripts": "normal", "end_with_newline": true, "indent_inner_html": false, "unformatted": ["a", "sub", "sup", "b", "i", "u", "span"], "wrap_attributes": "auto", "wrap_attributes_indent_size": 2, "extra_liners": ["head", "body", "/html"]}, "css": {"indent_char": " ", "align_assignments": true, "convert_quotes": "none", "end_with_newline": true, "force_indentation": false, "indent_comments": true, "indent_size": 2, "newline_between_rules": false, "no_lead_zero": true, "predefinedConfig": "csscomb", "preserve_newlines": true, "selector_separator_newline": true, "wrap_line_length": 0}, "less": {"indent_char": " ", "align_assignments": true, "convert_quotes": "none", "end_with_newline": true, "force_indentation": false, "indent_comments": true, "indent_size": 2, "newline_between_rules": true, "no_lead_zero": true, "predefinedConfig": "csscomb", "preserve_newlines": true, "selector_separator_newline": true, "wrap_line_length": 0}}