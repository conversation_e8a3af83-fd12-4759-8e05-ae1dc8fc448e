"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryResolverAddress = exports.QueryInjectiveAddress = exports.QueryInjName = void 0;
var QueryInjName_js_1 = require("./QueryInjName.js");
Object.defineProperty(exports, "QueryInjName", { enumerable: true, get: function () { return QueryInjName_js_1.QueryInjName; } });
var QueryInjectiveAddress_js_1 = require("./QueryInjectiveAddress.js");
Object.defineProperty(exports, "QueryInjectiveAddress", { enumerable: true, get: function () { return QueryInjectiveAddress_js_1.QueryInjectiveAddress; } });
var QueryResolverAddress_js_1 = require("./QueryResolverAddress.js");
Object.defineProperty(exports, "QueryResolverAddress", { enumerable: true, get: function () { return QueryResolverAddress_js_1.QueryResolverAddress; } });
