{"version": 3, "file": "account_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/account_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAEF,yCAAyC;AAEzC,OAAO,EACN,sBAAsB,EACtB,uBAAuB,EACvB,qBAAqB,EACrB,mBAAmB,EACnB,uBAAuB,EACvB,2BAA2B,EAC3B,oBAAoB,EACpB,aAAa,EACb,qBAAqB,GACrB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEtD,MAAM,OAAO,qBAAsB,SAAQ,aAAa;IAEvD;QACC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAFjC,SAAI,GAAG,sBAAsB,CAAC;IAGrC,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,aAAa;IAExD;QACC,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAFzD,SAAI,GAAG,uBAAuB,CAAC;IAGtC,CAAC;CACD;AAED,MAAM,OAAO,qBAAsB,SAAQ,aAAa;IAEvD,YAAmB,YAAoB;QACtC,KAAK,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC;QAFrB,SAAI,GAAG,qBAAqB,CAAC;IAGpC,CAAC;CACD;AAED,MAAM,OAAO,eAAgB,SAAQ,aAAa;IAEjD;QACC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAFnC,SAAI,GAAG,mBAAmB,CAAC;IAGlC,CAAC;CACD;AAED,MAAM,OAAO,kBAAmB,SAAQ,aAAa;IAEpD;QACC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAFnD,SAAI,GAAG,uBAAuB,CAAC;IAGtC,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IAEtD;QACC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAFjC,SAAI,GAAG,2BAA2B,CAAC;IAG1C,CAAC;CACD;AAED,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IAEtD;QACC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAF5B,SAAI,GAAG,oBAAoB,CAAC;IAGnC,CAAC;CACD;AAED,MAAM,OAAO,aAAc,SAAQ,aAAa;IAE/C;QACC,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAF1C,SAAI,GAAG,aAAa,CAAC;IAG5B,CAAC;CACD;AAED,MAAM,OAAO,qBAAsB,SAAQ,aAAa;IAEvD;QACC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAFxD,SAAI,GAAG,qBAAqB,CAAC;IAGpC,CAAC;CACD"}