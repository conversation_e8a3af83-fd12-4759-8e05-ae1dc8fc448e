#!/usr/bin/env python3
"""
自动IP轮换器 - 每分钟自动轮换所有IP
不依赖V2Ray，直接设置系统代理
"""

import yaml
import time
import random
import logging
import subprocess
import threading
from datetime import datetime
from pathlib import Path

class AutoIPRotator:
    """自动IP轮换器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化自动IP轮换器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.current_proxy = None
        self.is_running = False
        self.rotation_thread = None
        self.start_time = None
        self.switch_count = 0
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/auto_ip_rotator.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 确保日志目录存在
        Path('logs').mkdir(exist_ok=True)
        
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _set_system_proxy(self, server: str, port: int) -> bool:
        """
        设置系统代理
        
        Args:
            server: 代理服务器
            port: 代理端口
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 启用系统代理
            cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
            cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "{server}:{port}" /f'
            
            subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
            subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
            
            self.logger.info(f"系统代理设置成功: {server}:{port}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置系统代理失败: {e}")
            return False
    
    def _disable_system_proxy(self) -> bool:
        """
        禁用系统代理
        
        Returns:
            bool: 禁用是否成功
        """
        try:
            cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
            subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
            
            self.logger.info("系统代理已禁用")
            return True
            
        except Exception as e:
            self.logger.error(f"禁用系统代理失败: {e}")
            return False
    
    def _show_region_distribution(self, proxies):
        """显示地区分布"""
        regions = {}
        for proxy in proxies:
            name = proxy.get('name', '')
            for region in ['香港', '台湾', '日本', '新加坡', '美国', '韩国', '澳门', '加拿大', '英国', '德国', '法国']:
                if region in name:
                    regions[region] = regions.get(region, 0) + 1
                    break
        
        if regions:
            print("📊 节点分布:")
            for region, count in sorted(regions.items()):
                print(f"   {region}: {count}个")
    
    def switch_proxy(self, proxy_name: str = None) -> bool:
        """
        切换代理
        
        Args:
            proxy_name: 指定的代理名称（部分匹配）
            
        Returns:
            bool: 切换是否成功
        """
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if not proxies:
            print("❌ 代理列表为空")
            return False
        
        # 选择代理
        if proxy_name:
            proxy = None
            for p in proxies:
                if proxy_name.lower() in p.get('name', '').lower():
                    proxy = p
                    break
            if not proxy:
                print(f"❌ 未找到包含 '{proxy_name}' 的代理")
                return False
        else:
            proxy = random.choice(proxies)
        
        # 记录切换信息
        self.current_proxy = proxy
        self.switch_count += 1
        
        print(f"🔄 [{self.switch_count}] 切换到: {proxy['name']}")
        print(f"   服务器: {proxy['server']}:{proxy['port']}")
        print(f"   类型: {proxy['type']}")
        print(f"   时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 设置系统代理（注意：这里设置的是Shadowsocks服务器地址，实际需要本地SS客户端）
        if self._set_system_proxy(proxy['server'], proxy['port']):
            self.logger.info(f"切换到代理: {proxy['name']}")
            return True
        else:
            return False
    
    def start_rotation(self, interval: int = 60):
        """
        启动自动轮换
        
        Args:
            interval: 轮换间隔（秒）
        """
        if self.is_running:
            print("⚠️  IP轮换已在运行中")
            return
        
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if not proxies:
            print("❌ 代理列表为空")
            return
        
        self.is_running = True
        self.start_time = datetime.now()
        self.switch_count = 0
        
        print(f"🚀 启动自动IP轮换")
        print(f"   轮换间隔: {interval}秒")
        print(f"   可用节点: {len(proxies)}个")
        print(f"   开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("   按 Ctrl+C 停止轮换")
        print("=" * 50)
        
        # 显示地区分布
        self._show_region_distribution(proxies)
        print("=" * 50)
        
        # 首次切换
        self.switch_proxy()
        
        try:
            while self.is_running:
                time.sleep(interval)
                if self.is_running:
                    self.switch_proxy()
        except KeyboardInterrupt:
            self.stop_rotation()
    
    def stop_rotation(self):
        """停止自动轮换"""
        if not self.is_running:
            return
            
        self.is_running = False
        end_time = datetime.now()
        duration = end_time - self.start_time if self.start_time else None
        
        # 禁用系统代理
        self._disable_system_proxy()
        
        print("\n" + "=" * 50)
        print("🛑 自动IP轮换已停止")
        if duration:
            print(f"   运行时长: {duration}")
        print(f"   总切换次数: {self.switch_count}")
        print(f"   停止时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.info(f"自动IP轮换停止，总切换次数: {self.switch_count}")
    
    def list_proxies(self, region: str = None):
        """列出代理"""
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        if region:
            proxies = [p for p in proxies if region in p.get('name', '')]
            print(f"📋 {region}地区代理列表 ({len(proxies)}个):")
        else:
            print(f"📋 所有代理列表 ({len(proxies)}个):")
        
        print("-" * 80)
        
        for i, proxy in enumerate(proxies[:20], 1):  # 只显示前20个
            status = "🟢 当前" if proxy == self.current_proxy else "⚪"
            print(f"{i:2d}. {status} {proxy['name']}")
            print(f"     服务器: {proxy['server']}:{proxy['port']}")
            print(f"     类型: {proxy['type']}")
            print()
        
        if len(proxies) > 20:
            print(f"... 还有 {len(proxies) - 20} 个代理")
    
    def get_status(self):
        """显示当前状态"""
        config = self._load_config()
        proxies = config.get('proxies', [])
        
        print("📊 自动IP轮换器状态")
        print("-" * 30)
        print(f"运行状态: {'🟢 运行中' if self.is_running else '🔴 已停止'}")
        print(f"当前代理: {self.current_proxy['name'] if self.current_proxy else '无'}")
        if self.current_proxy:
            print(f"代理服务器: {self.current_proxy['server']}:{self.current_proxy['port']}")
        print(f"可用节点: {len(proxies)}个")
        print(f"切换次数: {self.switch_count}")
        
        if self.start_time:
            runtime = datetime.now() - self.start_time
            print(f"运行时长: {runtime}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("🔧 自动IP轮换器")
        print("=" * 30)
        print("使用方法:")
        print("  python auto_ip_rotator.py start [间隔秒数]  - 启动自动轮换")
        print("  python auto_ip_rotator.py switch [名称]     - 手动切换代理")
        print("  python auto_ip_rotator.py list [地区]       - 列出代理")
        print("  python auto_ip_rotator.py status           - 显示状态")
        print("  python auto_ip_rotator.py stop             - 停止代理")
        print()
        print("示例:")
        print("  python auto_ip_rotator.py start 60         - 每60秒切换一次")
        print("  python auto_ip_rotator.py switch 香港       - 切换到香港节点")
        print("  python auto_ip_rotator.py list 日本         - 列出日本节点")
        print()
        print("注意: 这个版本直接设置系统代理，需要配合本地SS客户端使用")
        return
    
    command = sys.argv[1].lower()
    rotator = AutoIPRotator()
    
    if command == "start":
        interval = 60
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                print("❌ 间隔时间必须是数字")
                return
        rotator.start_rotation(interval)
    
    elif command == "switch":
        proxy_name = sys.argv[2] if len(sys.argv) > 2 else None
        if rotator.switch_proxy(proxy_name):
            print("✅ 代理切换成功")
        else:
            print("❌ 代理切换失败")
    
    elif command == "list":
        region = sys.argv[2] if len(sys.argv) > 2 else None
        rotator.list_proxies(region)
    
    elif command == "status":
        rotator.get_status()
    
    elif command == "stop":
        rotator._disable_system_proxy()
        print("✅ 系统代理已禁用")
    
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()
