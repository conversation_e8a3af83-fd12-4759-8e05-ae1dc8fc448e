import { InjectiveTradingRpc } from '@injectivelabs/indexer-proto-ts';
export type ListTradingStrategiesResponse = InjectiveTradingRpc.ListTradingStrategiesResponse;
export type TradingStrategy = InjectiveTradingRpc.TradingStrategy;
export declare enum MarketType {
    Spot = "spot",
    Derivative = "derivative"
}
export declare enum GridStrategyType {
    Geometric = "geometric",
    Arithmetic = "arithmetic",
    Perpetual = "perpetual"
}
export type GridStrategyStreamResponse = InjectiveTradingRpc.StreamStrategyResponse;
