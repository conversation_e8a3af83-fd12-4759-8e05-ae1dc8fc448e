"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcExplorerApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcExplorerApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_1.IndexerModule.Explorer;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new indexer_proto_ts_1.InjectiveExplorerRpc.InjectiveExplorerRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchTxByHash(hash) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetTxByTxHashRequest.create();
        request.hash = hash;
        try {
            const response = await this.client.GetTxByTxHash(request, this.metadata);
            return index_js_2.IndexerGrpcExplorerTransformer.getTxByTxHashResponseToTx(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetTxByTxHash',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetTxByTxHash',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountTx({ address, limit, type, before, after, startTime, endTime, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetAccountTxsRequest.create();
        request.address = address;
        if (limit) {
            request.limit = limit;
        }
        if (before) {
            request.before = before.toString();
        }
        if (after) {
            request.after = after.toString();
        }
        if (before) {
            request.before = before.toString();
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (type) {
            request.type = type;
        }
        try {
            const response = await this.retry(() => this.client.GetAccountTxs(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getAccountTxsResponseToAccountTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetAccountTxs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetAccountTxs',
                contextModule: this.module,
            });
        }
    }
    async fetchValidator(validatorAddress) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetValidatorRequest.create();
        request.address = validatorAddress;
        try {
            const response = await this.retry(() => this.client.GetValidator(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.validatorResponseToValidator(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetValidator',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetValidator',
                contextModule: this.module,
            });
        }
    }
    async fetchValidatorUptime(validatorAddress) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetValidatorUptimeRequest.create();
        request.address = validatorAddress;
        try {
            const response = await this.retry(() => this.client.GetValidatorUptime(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getValidatorUptimeResponseToValidatorUptime(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetValidatorUptime',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetValidatorUptime',
                contextModule: this.module,
            });
        }
    }
    async fetchPeggyDepositTxs({ sender, receiver, limit, skip, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetPeggyDepositTxsRequest.create();
        if (sender) {
            request.sender = sender;
        }
        if (receiver) {
            request.receiver = receiver;
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        try {
            const response = await this.retry(() => this.client.GetPeggyDepositTxs(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getPeggyDepositTxsResponseToPeggyDepositTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetPeggyDepositTxs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetPeggyDepositTxs',
                contextModule: this.module,
            });
        }
    }
    async fetchPeggyWithdrawalTxs({ sender, receiver, limit, skip, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetPeggyWithdrawalTxsRequest.create();
        if (sender) {
            request.sender = sender;
        }
        if (receiver) {
            request.receiver = receiver;
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        try {
            const response = await this.retry(() => this.client.GetPeggyWithdrawalTxs(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getPeggyWithdrawalTxsResponseToPeggyWithdrawalTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetPeggyWithdrawalTxs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetPeggyWithdrawalTxs',
                contextModule: this.module,
            });
        }
    }
    async fetchBlocks({ before, after, limit, from, to, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetBlocksRequest.create();
        if (before) {
            request.before = before.toString();
        }
        if (after) {
            request.after = after.toString();
        }
        if (from) {
            request.from = from.toString();
        }
        if (to) {
            request.to = to.toString();
        }
        if (limit) {
            request.limit = limit;
        }
        try {
            const response = await this.retry(() => this.client.GetBlocks(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetBlocks',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetBlocks',
                contextModule: this.module,
            });
        }
    }
    async fetchBlock(id) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetBlockRequest.create();
        request.id = id;
        try {
            const response = await this.retry(() => this.client.GetBlock(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetBlock',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetBlock',
                contextModule: this.module,
            });
        }
    }
    async fetchTxs({ before, after, limit, skip, type, chainModule, startTime, endTime, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetTxsRequest.create();
        if (before) {
            request.before = before.toString();
        }
        if (after) {
            request.after = after.toString();
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        if (type) {
            request.type = type;
        }
        if (chainModule) {
            request.module = chainModule;
        }
        if (before) {
            request.before = before.toString();
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        try {
            const response = await this.retry(() => this.client.GetTxs(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetTxs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetTxs',
                contextModule: this.module,
            });
        }
    }
    async fetchIBCTransferTxs({ sender, receiver, srcChannel, srcPort, destChannel, destPort, limit, skip, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetIBCTransferTxsRequest.create();
        if (sender) {
            request.sender = sender;
        }
        if (receiver) {
            request.receiver = receiver;
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        if (srcChannel) {
            request.srcChannel = srcChannel;
        }
        if (srcPort) {
            request.srcPort = srcPort;
        }
        if (destChannel) {
            request.destChannel = destChannel;
        }
        if (destPort) {
            request.destPort = destPort;
        }
        try {
            const response = await this.retry(() => this.client.GetIBCTransferTxs(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getIBCTransferTxsResponseToIBCTransferTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetIBCTransferTxs',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetIBCTransferTxs',
                contextModule: this.module,
            });
        }
    }
    async fetchExplorerStats() {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetStatsRequest.create();
        try {
            const response = await this.retry(() => this.client.GetStats(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getExplorerStatsResponseToExplorerStats(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetExplorerStats',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetExplorerStats',
                contextModule: this.module,
            });
        }
    }
    async fetchTxsV2({ type, token, status, perPage, endTime, startTime, blockNumber, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetTxsV2Request.create();
        if (token) {
            request.token = token;
        }
        if (blockNumber) {
            request.blockNumber = blockNumber.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (perPage) {
            request.perPage = perPage;
        }
        if (status) {
            request.status = status;
        }
        if (type) {
            request.type = type;
        }
        try {
            const response = await this.retry(() => this.client.GetTxsV2(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getTxsV2ResponseToTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetTxsV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetTxsV2',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountTxsV2({ type, token, address, endTime, perPage, startTime, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetAccountTxsV2Request.create();
        request.address = address;
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (perPage) {
            request.perPage = perPage;
        }
        if (token) {
            request.token = token;
        }
        if (type) {
            request.type = type;
        }
        try {
            const response = await this.retry(() => this.client.GetAccountTxsV2(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getAccountTxsV2ResponseToAccountTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetAccountTxsV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetAccountTxsV2',
                contextModule: this.module,
            });
        }
    }
    async fetchBlocksV2({ token, perPage, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetBlocksV2Request.create({});
        if (perPage) {
            request.perPage = perPage;
        }
        if (token) {
            request.token = token;
        }
        try {
            const response = await this.retry(() => this.client.GetBlocksV2(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getBlocksV2ResponseToBlocks(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetBlocksV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetBlocksV2',
                contextModule: this.module,
            });
        }
    }
    async fetchContractTxsV2({ to, from, token, height, status, perPage, contractAddress, }) {
        const request = indexer_proto_ts_1.InjectiveExplorerRpc.GetContractTxsV2Request.create();
        request.address = contractAddress;
        if (from) {
            request.from = from.toString();
        }
        if (to) {
            request.to = to.toString();
        }
        if (perPage) {
            request.perPage = perPage;
        }
        if (token) {
            request.token = token;
        }
        if (height) {
            request.height = height;
        }
        if (status) {
            request.status = status;
        }
        try {
            const response = await this.retry(() => this.client.GetContractTxsV2(request, this.metadata));
            return index_js_2.IndexerGrpcExplorerTransformer.getContractTxsV2ResponseToContractTxs(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveExplorerRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GetContractTxsV2',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GetContractTxsV2',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcExplorerApi = IndexerGrpcExplorerApi;
