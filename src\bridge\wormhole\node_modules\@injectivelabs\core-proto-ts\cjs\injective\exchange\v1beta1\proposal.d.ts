import _m0 from "protobufjs/minimal.js";
import { CommunityPoolSpendProposal } from "../../../cosmos/distribution/v1beta1/distribution";
import { OracleType } from "../../oracle/v1beta1/oracle";
import { CampaignRewardPool, DenomDecimals, DenomMinNotional, FeeDiscountSchedule, MarketFeeMultiplier, MarketStatus, TradingRewardCampaignInfo } from "./exchange";
export declare const protobufPackage = "injective.exchange.v1beta1";
export declare enum ExchangeType {
    EXCHANGE_UNSPECIFIED = 0,
    SPOT = 1,
    DERIVATIVES = 2,
    UNRECOGNIZED = -1
}
export declare function exchangeTypeFromJSON(object: any): ExchangeType;
export declare function exchangeTypeToJSON(object: ExchangeType): string;
export interface SpotMarketParamUpdateProposal {
    title: string;
    description: string;
    marketId: string;
    /** maker_fee_rate defines the trade fee rate for makers on the spot market */
    makerFeeRate: string;
    /** taker_fee_rate defines the trade fee rate for takers on the spot market */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the relayer fee share rate for the spot
     * market
     */
    relayerFeeShareRate: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    status: MarketStatus;
    ticker: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    adminInfo: AdminInfo | undefined;
    /** base token decimals */
    baseDecimals: number;
    /** quote token decimals */
    quoteDecimals: number;
}
export interface ExchangeEnableProposal {
    title: string;
    description: string;
    exchangeType: ExchangeType;
}
export interface BatchExchangeModificationProposal {
    title: string;
    description: string;
    spotMarketParamUpdateProposals: SpotMarketParamUpdateProposal[];
    derivativeMarketParamUpdateProposals: DerivativeMarketParamUpdateProposal[];
    spotMarketLaunchProposals: SpotMarketLaunchProposal[];
    perpetualMarketLaunchProposals: PerpetualMarketLaunchProposal[];
    expiryFuturesMarketLaunchProposals: ExpiryFuturesMarketLaunchProposal[];
    tradingRewardCampaignUpdateProposal: TradingRewardCampaignUpdateProposal | undefined;
    binaryOptionsMarketLaunchProposals: BinaryOptionsMarketLaunchProposal[];
    binaryOptionsParamUpdateProposals: BinaryOptionsMarketParamUpdateProposal[];
    denomDecimalsUpdateProposal: UpdateDenomDecimalsProposal | undefined;
    feeDiscountProposal: FeeDiscountProposal | undefined;
    marketForcedSettlementProposals: MarketForcedSettlementProposal[];
    denomMinNotionalProposal: DenomMinNotionalProposal | undefined;
}
/**
 * SpotMarketLaunchProposal defines a SDK message for proposing a new spot
 * market through governance
 */
export interface SpotMarketLaunchProposal {
    title: string;
    description: string;
    /** Ticker for the spot market. */
    ticker: string;
    /** type of coin to use as the base currency */
    baseDenom: string;
    /** type of coin to use as the quote currency */
    quoteDenom: string;
    /** min_price_tick_size defines the minimum tick size of the order's price */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /** maker_fee_rate defines the fee percentage makers pay when trading */
    makerFeeRate: string;
    /** taker_fee_rate defines the fee percentage takers pay when trading */
    takerFeeRate: string;
    /** min_notional defines the minimum notional for orders in the market */
    minNotional: string;
    adminInfo: AdminInfo | undefined;
    /** base token decimals */
    baseDecimals: number;
    /** quote token decimals */
    quoteDecimals: number;
}
/**
 * PerpetualMarketLaunchProposal defines a SDK message for proposing a new
 * perpetual futures market through governance
 */
export interface PerpetualMarketLaunchProposal {
    title: string;
    description: string;
    /** Ticker for the derivative market. */
    ticker: string;
    /** type of coin to use as the base currency */
    quoteDenom: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Oracle type */
    oracleType: OracleType;
    /**
     * initial_margin_ratio defines the initial margin ratio for the derivative
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio for the
     * derivative market
     */
    maintenanceMarginRatio: string;
    /**
     * maker_fee_rate defines the exchange trade fee for makers for the derivative
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the exchange trade fee for takers for the derivative
     * market
     */
    takerFeeRate: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    adminInfo: AdminInfo | undefined;
}
export interface BinaryOptionsMarketLaunchProposal {
    title: string;
    description: string;
    /** Ticker for the derivative contract. */
    ticker: string;
    /** Oracle symbol */
    oracleSymbol: string;
    /** Oracle Provider */
    oracleProvider: string;
    /** Oracle type */
    oracleType: OracleType;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** expiration timestamp */
    expirationTimestamp: string;
    /** expiration timestamp */
    settlementTimestamp: string;
    /** admin of the market */
    admin: string;
    /** Address of the quote currency denomination for the binary options contract */
    quoteDenom: string;
    /** maker_fee_rate defines the maker fee rate of a binary options market */
    makerFeeRate: string;
    /** taker_fee_rate defines the taker fee rate of a derivative market */
    takerFeeRate: string;
    /**
     * min_price_tick_size defines the minimum tick size that the price and margin
     * required for orders in the market
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the quantity
     * required for orders in the market
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    adminPermissions: number;
}
/**
 * ExpiryFuturesMarketLaunchProposal defines a SDK message for proposing a new
 * expiry futures market through governance
 */
export interface ExpiryFuturesMarketLaunchProposal {
    title: string;
    description: string;
    /** Ticker for the derivative market. */
    ticker: string;
    /** type of coin to use as the quote currency */
    quoteDenom: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Oracle type */
    oracleType: OracleType;
    /** Expiration time of the market */
    expiry: string;
    /**
     * initial_margin_ratio defines the initial margin ratio for the derivative
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio for the
     * derivative market
     */
    maintenanceMarginRatio: string;
    /**
     * maker_fee_rate defines the exchange trade fee for makers for the derivative
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the exchange trade fee for takers for the derivative
     * market
     */
    takerFeeRate: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    adminInfo: AdminInfo | undefined;
}
export interface DerivativeMarketParamUpdateProposal {
    title: string;
    description: string;
    marketId: string;
    /**
     * initial_margin_ratio defines the initial margin ratio for the derivative
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio for the
     * derivative market
     */
    maintenanceMarginRatio: string;
    /**
     * maker_fee_rate defines the exchange trade fee for makers for the derivative
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the exchange trade fee for takers for the derivative
     * market
     */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the relayer fee share rate for the
     * derivative market
     */
    relayerFeeShareRate: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /** hourly_interest_rate defines the hourly interest rate */
    HourlyInterestRate: string;
    /**
     * hourly_funding_rate_cap defines the maximum absolute value of the hourly
     * funding rate
     */
    HourlyFundingRateCap: string;
    status: MarketStatus;
    oracleParams: OracleParams | undefined;
    ticker: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    adminInfo: AdminInfo | undefined;
}
export interface AdminInfo {
    admin: string;
    adminPermissions: number;
}
export interface MarketForcedSettlementProposal {
    title: string;
    description: string;
    marketId: string;
    settlementPrice: string;
}
export interface UpdateDenomDecimalsProposal {
    title: string;
    description: string;
    denomDecimals: DenomDecimals[];
}
export interface BinaryOptionsMarketParamUpdateProposal {
    title: string;
    description: string;
    marketId: string;
    /**
     * maker_fee_rate defines the exchange trade fee for makers for the derivative
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the exchange trade fee for takers for the derivative
     * market
     */
    takerFeeRate: string;
    /**
     * relayer_fee_share_rate defines the relayer fee share rate for the
     * derivative market
     */
    relayerFeeShareRate: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /** expiration timestamp */
    expirationTimestamp: string;
    /** expiration timestamp */
    settlementTimestamp: string;
    /** new price at which market will be settled */
    settlementPrice: string;
    /** admin of the market */
    admin: string;
    status: MarketStatus;
    oracleParams: ProviderOracleParams | undefined;
    ticker: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
}
export interface ProviderOracleParams {
    /** Oracle base currency */
    symbol: string;
    /** Oracle quote currency */
    provider: string;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Oracle type */
    oracleType: OracleType;
}
export interface OracleParams {
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Oracle type */
    oracleType: OracleType;
}
export interface TradingRewardCampaignLaunchProposal {
    title: string;
    description: string;
    campaignInfo: TradingRewardCampaignInfo | undefined;
    campaignRewardPools: CampaignRewardPool[];
}
export interface TradingRewardCampaignUpdateProposal {
    title: string;
    description: string;
    campaignInfo: TradingRewardCampaignInfo | undefined;
    campaignRewardPoolsAdditions: CampaignRewardPool[];
    campaignRewardPoolsUpdates: CampaignRewardPool[];
}
export interface RewardPointUpdate {
    accountAddress: string;
    /** new_points overwrites the current trading reward points for the account */
    newPoints: string;
}
export interface TradingRewardPendingPointsUpdateProposal {
    title: string;
    description: string;
    pendingPoolTimestamp: string;
    rewardPointUpdates: RewardPointUpdate[];
}
export interface FeeDiscountProposal {
    title: string;
    description: string;
    schedule: FeeDiscountSchedule | undefined;
}
export interface BatchCommunityPoolSpendProposal {
    title: string;
    description: string;
    proposals: CommunityPoolSpendProposal[];
}
/**
 * AtomicMarketOrderFeeMultiplierScheduleProposal defines a SDK message for
 * proposing new atomic take fee multipliers for specified markets
 */
export interface AtomicMarketOrderFeeMultiplierScheduleProposal {
    title: string;
    description: string;
    marketFeeMultipliers: MarketFeeMultiplier[];
}
export interface DenomMinNotionalProposal {
    title: string;
    description: string;
    denomMinNotionals: DenomMinNotional[];
}
export declare const SpotMarketParamUpdateProposal: {
    encode(message: SpotMarketParamUpdateProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketParamUpdateProposal;
    fromJSON(object: any): SpotMarketParamUpdateProposal;
    toJSON(message: SpotMarketParamUpdateProposal): unknown;
    create(base?: DeepPartial<SpotMarketParamUpdateProposal>): SpotMarketParamUpdateProposal;
    fromPartial(object: DeepPartial<SpotMarketParamUpdateProposal>): SpotMarketParamUpdateProposal;
};
export declare const ExchangeEnableProposal: {
    encode(message: ExchangeEnableProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExchangeEnableProposal;
    fromJSON(object: any): ExchangeEnableProposal;
    toJSON(message: ExchangeEnableProposal): unknown;
    create(base?: DeepPartial<ExchangeEnableProposal>): ExchangeEnableProposal;
    fromPartial(object: DeepPartial<ExchangeEnableProposal>): ExchangeEnableProposal;
};
export declare const BatchExchangeModificationProposal: {
    encode(message: BatchExchangeModificationProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchExchangeModificationProposal;
    fromJSON(object: any): BatchExchangeModificationProposal;
    toJSON(message: BatchExchangeModificationProposal): unknown;
    create(base?: DeepPartial<BatchExchangeModificationProposal>): BatchExchangeModificationProposal;
    fromPartial(object: DeepPartial<BatchExchangeModificationProposal>): BatchExchangeModificationProposal;
};
export declare const SpotMarketLaunchProposal: {
    encode(message: SpotMarketLaunchProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketLaunchProposal;
    fromJSON(object: any): SpotMarketLaunchProposal;
    toJSON(message: SpotMarketLaunchProposal): unknown;
    create(base?: DeepPartial<SpotMarketLaunchProposal>): SpotMarketLaunchProposal;
    fromPartial(object: DeepPartial<SpotMarketLaunchProposal>): SpotMarketLaunchProposal;
};
export declare const PerpetualMarketLaunchProposal: {
    encode(message: PerpetualMarketLaunchProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PerpetualMarketLaunchProposal;
    fromJSON(object: any): PerpetualMarketLaunchProposal;
    toJSON(message: PerpetualMarketLaunchProposal): unknown;
    create(base?: DeepPartial<PerpetualMarketLaunchProposal>): PerpetualMarketLaunchProposal;
    fromPartial(object: DeepPartial<PerpetualMarketLaunchProposal>): PerpetualMarketLaunchProposal;
};
export declare const BinaryOptionsMarketLaunchProposal: {
    encode(message: BinaryOptionsMarketLaunchProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BinaryOptionsMarketLaunchProposal;
    fromJSON(object: any): BinaryOptionsMarketLaunchProposal;
    toJSON(message: BinaryOptionsMarketLaunchProposal): unknown;
    create(base?: DeepPartial<BinaryOptionsMarketLaunchProposal>): BinaryOptionsMarketLaunchProposal;
    fromPartial(object: DeepPartial<BinaryOptionsMarketLaunchProposal>): BinaryOptionsMarketLaunchProposal;
};
export declare const ExpiryFuturesMarketLaunchProposal: {
    encode(message: ExpiryFuturesMarketLaunchProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExpiryFuturesMarketLaunchProposal;
    fromJSON(object: any): ExpiryFuturesMarketLaunchProposal;
    toJSON(message: ExpiryFuturesMarketLaunchProposal): unknown;
    create(base?: DeepPartial<ExpiryFuturesMarketLaunchProposal>): ExpiryFuturesMarketLaunchProposal;
    fromPartial(object: DeepPartial<ExpiryFuturesMarketLaunchProposal>): ExpiryFuturesMarketLaunchProposal;
};
export declare const DerivativeMarketParamUpdateProposal: {
    encode(message: DerivativeMarketParamUpdateProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketParamUpdateProposal;
    fromJSON(object: any): DerivativeMarketParamUpdateProposal;
    toJSON(message: DerivativeMarketParamUpdateProposal): unknown;
    create(base?: DeepPartial<DerivativeMarketParamUpdateProposal>): DerivativeMarketParamUpdateProposal;
    fromPartial(object: DeepPartial<DerivativeMarketParamUpdateProposal>): DerivativeMarketParamUpdateProposal;
};
export declare const AdminInfo: {
    encode(message: AdminInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AdminInfo;
    fromJSON(object: any): AdminInfo;
    toJSON(message: AdminInfo): unknown;
    create(base?: DeepPartial<AdminInfo>): AdminInfo;
    fromPartial(object: DeepPartial<AdminInfo>): AdminInfo;
};
export declare const MarketForcedSettlementProposal: {
    encode(message: MarketForcedSettlementProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MarketForcedSettlementProposal;
    fromJSON(object: any): MarketForcedSettlementProposal;
    toJSON(message: MarketForcedSettlementProposal): unknown;
    create(base?: DeepPartial<MarketForcedSettlementProposal>): MarketForcedSettlementProposal;
    fromPartial(object: DeepPartial<MarketForcedSettlementProposal>): MarketForcedSettlementProposal;
};
export declare const UpdateDenomDecimalsProposal: {
    encode(message: UpdateDenomDecimalsProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): UpdateDenomDecimalsProposal;
    fromJSON(object: any): UpdateDenomDecimalsProposal;
    toJSON(message: UpdateDenomDecimalsProposal): unknown;
    create(base?: DeepPartial<UpdateDenomDecimalsProposal>): UpdateDenomDecimalsProposal;
    fromPartial(object: DeepPartial<UpdateDenomDecimalsProposal>): UpdateDenomDecimalsProposal;
};
export declare const BinaryOptionsMarketParamUpdateProposal: {
    encode(message: BinaryOptionsMarketParamUpdateProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BinaryOptionsMarketParamUpdateProposal;
    fromJSON(object: any): BinaryOptionsMarketParamUpdateProposal;
    toJSON(message: BinaryOptionsMarketParamUpdateProposal): unknown;
    create(base?: DeepPartial<BinaryOptionsMarketParamUpdateProposal>): BinaryOptionsMarketParamUpdateProposal;
    fromPartial(object: DeepPartial<BinaryOptionsMarketParamUpdateProposal>): BinaryOptionsMarketParamUpdateProposal;
};
export declare const ProviderOracleParams: {
    encode(message: ProviderOracleParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProviderOracleParams;
    fromJSON(object: any): ProviderOracleParams;
    toJSON(message: ProviderOracleParams): unknown;
    create(base?: DeepPartial<ProviderOracleParams>): ProviderOracleParams;
    fromPartial(object: DeepPartial<ProviderOracleParams>): ProviderOracleParams;
};
export declare const OracleParams: {
    encode(message: OracleParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleParams;
    fromJSON(object: any): OracleParams;
    toJSON(message: OracleParams): unknown;
    create(base?: DeepPartial<OracleParams>): OracleParams;
    fromPartial(object: DeepPartial<OracleParams>): OracleParams;
};
export declare const TradingRewardCampaignLaunchProposal: {
    encode(message: TradingRewardCampaignLaunchProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignLaunchProposal;
    fromJSON(object: any): TradingRewardCampaignLaunchProposal;
    toJSON(message: TradingRewardCampaignLaunchProposal): unknown;
    create(base?: DeepPartial<TradingRewardCampaignLaunchProposal>): TradingRewardCampaignLaunchProposal;
    fromPartial(object: DeepPartial<TradingRewardCampaignLaunchProposal>): TradingRewardCampaignLaunchProposal;
};
export declare const TradingRewardCampaignUpdateProposal: {
    encode(message: TradingRewardCampaignUpdateProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignUpdateProposal;
    fromJSON(object: any): TradingRewardCampaignUpdateProposal;
    toJSON(message: TradingRewardCampaignUpdateProposal): unknown;
    create(base?: DeepPartial<TradingRewardCampaignUpdateProposal>): TradingRewardCampaignUpdateProposal;
    fromPartial(object: DeepPartial<TradingRewardCampaignUpdateProposal>): TradingRewardCampaignUpdateProposal;
};
export declare const RewardPointUpdate: {
    encode(message: RewardPointUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RewardPointUpdate;
    fromJSON(object: any): RewardPointUpdate;
    toJSON(message: RewardPointUpdate): unknown;
    create(base?: DeepPartial<RewardPointUpdate>): RewardPointUpdate;
    fromPartial(object: DeepPartial<RewardPointUpdate>): RewardPointUpdate;
};
export declare const TradingRewardPendingPointsUpdateProposal: {
    encode(message: TradingRewardPendingPointsUpdateProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardPendingPointsUpdateProposal;
    fromJSON(object: any): TradingRewardPendingPointsUpdateProposal;
    toJSON(message: TradingRewardPendingPointsUpdateProposal): unknown;
    create(base?: DeepPartial<TradingRewardPendingPointsUpdateProposal>): TradingRewardPendingPointsUpdateProposal;
    fromPartial(object: DeepPartial<TradingRewardPendingPointsUpdateProposal>): TradingRewardPendingPointsUpdateProposal;
};
export declare const FeeDiscountProposal: {
    encode(message: FeeDiscountProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountProposal;
    fromJSON(object: any): FeeDiscountProposal;
    toJSON(message: FeeDiscountProposal): unknown;
    create(base?: DeepPartial<FeeDiscountProposal>): FeeDiscountProposal;
    fromPartial(object: DeepPartial<FeeDiscountProposal>): FeeDiscountProposal;
};
export declare const BatchCommunityPoolSpendProposal: {
    encode(message: BatchCommunityPoolSpendProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchCommunityPoolSpendProposal;
    fromJSON(object: any): BatchCommunityPoolSpendProposal;
    toJSON(message: BatchCommunityPoolSpendProposal): unknown;
    create(base?: DeepPartial<BatchCommunityPoolSpendProposal>): BatchCommunityPoolSpendProposal;
    fromPartial(object: DeepPartial<BatchCommunityPoolSpendProposal>): BatchCommunityPoolSpendProposal;
};
export declare const AtomicMarketOrderFeeMultiplierScheduleProposal: {
    encode(message: AtomicMarketOrderFeeMultiplierScheduleProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AtomicMarketOrderFeeMultiplierScheduleProposal;
    fromJSON(object: any): AtomicMarketOrderFeeMultiplierScheduleProposal;
    toJSON(message: AtomicMarketOrderFeeMultiplierScheduleProposal): unknown;
    create(base?: DeepPartial<AtomicMarketOrderFeeMultiplierScheduleProposal>): AtomicMarketOrderFeeMultiplierScheduleProposal;
    fromPartial(object: DeepPartial<AtomicMarketOrderFeeMultiplierScheduleProposal>): AtomicMarketOrderFeeMultiplierScheduleProposal;
};
export declare const DenomMinNotionalProposal: {
    encode(message: DenomMinNotionalProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DenomMinNotionalProposal;
    fromJSON(object: any): DenomMinNotionalProposal;
    toJSON(message: DenomMinNotionalProposal): unknown;
    create(base?: DeepPartial<DenomMinNotionalProposal>): DenomMinNotionalProposal;
    fromPartial(object: DeepPartial<DenomMinNotionalProposal>): DenomMinNotionalProposal;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
