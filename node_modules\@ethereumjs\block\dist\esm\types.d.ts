import type { <PERSON>Header } from './header.js';
import type { Common } from '@ethereumjs/common';
import type { JsonRpcTx, JsonTx, TransactionType, TxData } from '@ethereumjs/tx';
import type { AddressLike, BigIntLike, BytesLike, CLRequest, CLRequestType, ConsolidationRequestV1, DepositRequestV1, JsonRpcWithdrawal, PrefixedHexString, RequestBytes, VerkleExecutionWitness, WithdrawalBytes, WithdrawalData, WithdrawalRequestV1 } from '@ethereumjs/util';
/**
 * An object to set to which blockchain the blocks and their headers belong. This could be specified
 * using a {@link Common} object, or `chain` and `hardfork`. Defaults to mainnet without specifying a
 * hardfork.
 */
export interface BlockOptions {
    /**
     * A {@link Common} object defining the chain and the hardfork a block/block header belongs to.
     *
     * Object will be internally copied so that tx behavior don't incidentally
     * change on future HF changes.
     *
     * Default: {@link Common} object set to `mainnet` and the HF currently defined as the default
     * hardfork in the {@link Common} class.
     *
     * Current default hardfork: `merge`
     */
    common?: Common;
    /**
     * Set the hardfork either by timestamp (for HFs from Shanghai onwards) or by block number
     * for older Hfs.
     *
     * Additionally it is possible to pass in a specific TD value to support live-Merge-HF
     * transitions. Note that this should only be needed in very rare and specific scenarios.
     *
     * Default: `false` (HF is set to whatever default HF is set by the {@link Common} instance)
     */
    setHardfork?: boolean | BigIntLike;
    /**
     * If a preceding {@link BlockHeader} (usually the parent header) is given the preceding
     * header will be used to calculate the difficulty for this block and the calculated
     * difficulty takes precedence over a provided static `difficulty` value.
     *
     * Note that this option has no effect on networks other than PoW/Ethash networks
     * (respectively also deactivates on the Merge HF switching to PoS/Casper).
     */
    calcDifficultyFromHeader?: BlockHeader;
    /**
     * A block object by default gets frozen along initialization. This gives you
     * strong additional security guarantees on the consistency of the block parameters.
     * It also enables block hash caching when the `hash()` method is called multiple times.
     *
     * If you need to deactivate the block freeze - e.g. because you want to subclass block and
     * add additional properties - it is strongly encouraged that you do the freeze yourself
     * within your code instead.
     *
     * Default: true
     */
    freeze?: boolean;
    /**
     * Provide a clique signer's privateKey to seal this block.
     * Will throw if provided on a non-PoA chain.
     */
    cliqueSigner?: Uint8Array;
    /**
     *  Skip consensus format validation checks on header if set. Defaults to false.
     */
    skipConsensusFormatValidation?: boolean;
    executionWitness?: VerkleExecutionWitness;
}
/**
 * A block header's data.
 */
export interface HeaderData {
    parentHash?: BytesLike | string;
    uncleHash?: BytesLike | string;
    coinbase?: AddressLike | string;
    stateRoot?: BytesLike | string;
    transactionsTrie?: BytesLike | string;
    receiptTrie?: BytesLike | string;
    logsBloom?: BytesLike | string;
    difficulty?: BigIntLike | string;
    number?: BigIntLike | string;
    gasLimit?: BigIntLike | string;
    gasUsed?: BigIntLike | string;
    timestamp?: BigIntLike | string;
    extraData?: BytesLike | string;
    mixHash?: BytesLike | string;
    nonce?: BytesLike | string;
    baseFeePerGas?: BigIntLike | string;
    withdrawalsRoot?: BytesLike | string;
    blobGasUsed?: BigIntLike | string;
    excessBlobGas?: BigIntLike | string;
    parentBeaconBlockRoot?: BytesLike | string;
    requestsRoot?: BytesLike | string;
}
/**
 * A block's data.
 */
export interface BlockData {
    /**
     * Header data for the block
     */
    header?: HeaderData;
    transactions?: Array<TxData[TransactionType]>;
    uncleHeaders?: Array<HeaderData>;
    withdrawals?: Array<WithdrawalData>;
    requests?: Array<CLRequest<CLRequestType>>;
    /**
     * EIP-6800: Verkle Proof Data (experimental)
     */
    executionWitness?: VerkleExecutionWitness | null;
}
export declare type WithdrawalsBytes = WithdrawalBytes[];
export declare type RequestsBytes = RequestBytes[];
export declare type ExecutionWitnessBytes = Uint8Array;
export declare type BlockBytes = [BlockHeaderBytes, TransactionsBytes, UncleHeadersBytes] | [BlockHeaderBytes, TransactionsBytes, UncleHeadersBytes, WithdrawalsBytes] | [BlockHeaderBytes, TransactionsBytes, UncleHeadersBytes, WithdrawalsBytes, RequestsBytes] | [
    BlockHeaderBytes,
    TransactionsBytes,
    UncleHeadersBytes,
    WithdrawalsBytes,
    RequestsBytes,
    ExecutionWitnessBytes
];
/**
 * BlockHeaderBuffer is a Buffer array, except for the Verkle PreState which is an array of prestate arrays.
 */
export declare type BlockHeaderBytes = Uint8Array[];
export declare type BlockBodyBytes = [
    TransactionsBytes,
    UncleHeadersBytes,
    WithdrawalsBytes?,
    RequestBytes?
];
/**
 * TransactionsBytes can be an array of serialized txs for Typed Transactions or an array of Uint8Array Arrays for legacy transactions.
 */
export declare type TransactionsBytes = Uint8Array[][] | Uint8Array[];
export declare type UncleHeadersBytes = Uint8Array[][];
/**
 * An object with the block's data represented as strings.
 */
export interface JsonBlock {
    /**
     * Header data for the block
     */
    header?: JsonHeader;
    transactions?: JsonTx[];
    uncleHeaders?: JsonHeader[];
    withdrawals?: JsonRpcWithdrawal[];
    requests?: PrefixedHexString[] | null;
    executionWitness?: VerkleExecutionWitness | null;
}
/**
 * An object with the block header's data represented as 0x-prefixed hex strings.
 */
export interface JsonHeader {
    parentHash?: PrefixedHexString | string;
    uncleHash?: PrefixedHexString | string;
    coinbase?: PrefixedHexString | string;
    stateRoot?: PrefixedHexString | string;
    transactionsTrie?: PrefixedHexString | string;
    receiptTrie?: PrefixedHexString | string;
    logsBloom?: PrefixedHexString | string;
    difficulty?: PrefixedHexString | string;
    number?: PrefixedHexString | string;
    gasLimit?: PrefixedHexString | string;
    gasUsed?: PrefixedHexString | string;
    timestamp?: PrefixedHexString | string;
    extraData?: PrefixedHexString | string;
    mixHash?: PrefixedHexString | string;
    nonce?: PrefixedHexString | string;
    baseFeePerGas?: PrefixedHexString | string;
    withdrawalsRoot?: PrefixedHexString | string;
    blobGasUsed?: PrefixedHexString | string;
    excessBlobGas?: PrefixedHexString | string;
    parentBeaconBlockRoot?: PrefixedHexString | string;
    requestsRoot?: PrefixedHexString | string;
}
export interface JsonRpcBlock {
    number: PrefixedHexString | string;
    hash: PrefixedHexString | string;
    parentHash: PrefixedHexString | string;
    mixHash?: PrefixedHexString | string;
    nonce: PrefixedHexString | string;
    sha3Uncles: PrefixedHexString | string;
    logsBloom: PrefixedHexString | string;
    transactionsRoot: PrefixedHexString | string;
    stateRoot: PrefixedHexString | string;
    receiptsRoot: PrefixedHexString | string;
    miner: PrefixedHexString | string;
    difficulty: PrefixedHexString | string;
    totalDifficulty: PrefixedHexString | string;
    extraData: PrefixedHexString | string;
    size: PrefixedHexString | string;
    gasLimit: PrefixedHexString | string;
    gasUsed: PrefixedHexString | string;
    timestamp: PrefixedHexString | string;
    transactions: Array<JsonRpcTx | PrefixedHexString | string>;
    uncles: PrefixedHexString[] | string[];
    baseFeePerGas?: PrefixedHexString | string;
    withdrawals?: Array<JsonRpcWithdrawal>;
    withdrawalsRoot?: PrefixedHexString | string;
    blobGasUsed?: PrefixedHexString | string;
    excessBlobGas?: PrefixedHexString | string;
    parentBeaconBlockRoot?: PrefixedHexString | string;
    executionWitness?: VerkleExecutionWitness | null;
    requestsRoot?: PrefixedHexString | string;
    requests?: Array<PrefixedHexString | string>;
}
export declare type WithdrawalV1 = {
    index: PrefixedHexString;
    validatorIndex: PrefixedHexString;
    address: PrefixedHexString;
    amount: PrefixedHexString;
};
export declare type ExecutionPayload = {
    parentHash: PrefixedHexString | string;
    feeRecipient: PrefixedHexString | string;
    stateRoot: PrefixedHexString | string;
    receiptsRoot: PrefixedHexString | string;
    logsBloom: PrefixedHexString | string;
    prevRandao: PrefixedHexString | string;
    blockNumber: PrefixedHexString | string;
    gasLimit: PrefixedHexString | string;
    gasUsed: PrefixedHexString | string;
    timestamp: PrefixedHexString | string;
    extraData: PrefixedHexString | string;
    baseFeePerGas: PrefixedHexString | string;
    blockHash: PrefixedHexString | string;
    transactions: PrefixedHexString[] | string[];
    withdrawals?: WithdrawalV1[];
    blobGasUsed?: PrefixedHexString | string;
    excessBlobGas?: PrefixedHexString | string;
    parentBeaconBlockRoot?: PrefixedHexString | string;
    executionWitness?: VerkleExecutionWitness | null;
    depositRequests?: DepositRequestV1[];
    withdrawalRequests?: WithdrawalRequestV1[];
    consolidationRequests?: ConsolidationRequestV1[];
};
//# sourceMappingURL=types.d.ts.map