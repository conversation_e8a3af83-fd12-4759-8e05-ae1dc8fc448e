import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { PageRequest, PageResponse } from "../../../../cosmos/base/query/v1beta1/pagination";
export declare const protobufPackage = "ibc.lightclients.wasm.v1";
/** QueryChecksumsRequest is the request type for the Query/Checksums RPC method. */
export interface QueryChecksumsRequest {
    /** pagination defines an optional pagination for the request. */
    pagination: PageRequest | undefined;
}
/** QueryChecksumsResponse is the response type for the Query/Checksums RPC method. */
export interface QueryChecksumsResponse {
    /** checksums is a list of the hex encoded checksums of all wasm codes stored. */
    checksums: string[];
    /** pagination defines the pagination in the response. */
    pagination: PageResponse | undefined;
}
/** QueryCodeRequest is the request type for the Query/Code RPC method. */
export interface QueryCodeRequest {
    /** checksum is a hex encoded string of the code stored. */
    checksum: string;
}
/** QueryCodeResponse is the response type for the Query/Code RPC method. */
export interface QueryCodeResponse {
    data: Uint8Array;
}
export declare const QueryChecksumsRequest: {
    encode(message: QueryChecksumsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryChecksumsRequest;
    fromJSON(object: any): QueryChecksumsRequest;
    toJSON(message: QueryChecksumsRequest): unknown;
    create(base?: DeepPartial<QueryChecksumsRequest>): QueryChecksumsRequest;
    fromPartial(object: DeepPartial<QueryChecksumsRequest>): QueryChecksumsRequest;
};
export declare const QueryChecksumsResponse: {
    encode(message: QueryChecksumsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryChecksumsResponse;
    fromJSON(object: any): QueryChecksumsResponse;
    toJSON(message: QueryChecksumsResponse): unknown;
    create(base?: DeepPartial<QueryChecksumsResponse>): QueryChecksumsResponse;
    fromPartial(object: DeepPartial<QueryChecksumsResponse>): QueryChecksumsResponse;
};
export declare const QueryCodeRequest: {
    encode(message: QueryCodeRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCodeRequest;
    fromJSON(object: any): QueryCodeRequest;
    toJSON(message: QueryCodeRequest): unknown;
    create(base?: DeepPartial<QueryCodeRequest>): QueryCodeRequest;
    fromPartial(object: DeepPartial<QueryCodeRequest>): QueryCodeRequest;
};
export declare const QueryCodeResponse: {
    encode(message: QueryCodeResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCodeResponse;
    fromJSON(object: any): QueryCodeResponse;
    toJSON(message: QueryCodeResponse): unknown;
    create(base?: DeepPartial<QueryCodeResponse>): QueryCodeResponse;
    fromPartial(object: DeepPartial<QueryCodeResponse>): QueryCodeResponse;
};
/** Query service for wasm module */
export interface Query {
    /** Get all Wasm checksums */
    Checksums(request: DeepPartial<QueryChecksumsRequest>, metadata?: grpc.Metadata): Promise<QueryChecksumsResponse>;
    /** Get Wasm code for given checksum */
    Code(request: DeepPartial<QueryCodeRequest>, metadata?: grpc.Metadata): Promise<QueryCodeResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Checksums(request: DeepPartial<QueryChecksumsRequest>, metadata?: grpc.Metadata): Promise<QueryChecksumsResponse>;
    Code(request: DeepPartial<QueryCodeRequest>, metadata?: grpc.Metadata): Promise<QueryCodeResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryChecksumsDesc: UnaryMethodDefinitionish;
export declare const QueryCodeDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
