"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcTradingStream = void 0;
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcWebConsumer_js_1 = require("../../base/BaseIndexerGrpcWebConsumer.js");
/**
 * @category Indexer Grid Strategy Grpc Stream
 */
class IndexerGrpcTradingStream {
    client;
    constructor(endpoint) {
        this.client = new indexer_proto_ts_1.InjectiveTradingRpc.InjectiveTradingRPCClientImpl((0, BaseIndexerGrpcWebConsumer_js_1.getGrpcIndexerWebImpl)(endpoint));
    }
    streamGridStrategies({ marketId, callback, onEndCallback, accountAddresses, onStatusCallback, }) {
        const request = indexer_proto_ts_1.InjectiveTradingRpc.StreamStrategyRequest.create();
        if ((!accountAddresses || accountAddresses.length === 0) && !marketId) {
            throw new Error('accountAddresses or marketId is required');
        }
        if (typeof callback !== 'function') {
            throw new Error('callback must be a function');
        }
        if (accountAddresses) {
            request.accountAddresses = accountAddresses;
        }
        if (marketId) {
            request.marketId = marketId;
        }
        const subscription = this.client.StreamStrategy(request).subscribe({
            next(response) {
                callback(response);
            },
            error(err) {
                if (onStatusCallback) {
                    onStatusCallback(err);
                }
            },
            complete() {
                if (onEndCallback) {
                    onEndCallback();
                }
            },
        });
        return subscription;
    }
}
exports.IndexerGrpcTradingStream = IndexerGrpcTradingStream;
