{"name": "@gql.tada/internal", "version": "1.0.8", "public": true, "description": "Internal logic for gql.tada’s CLI tool and GraphQLSP.", "author": "0no.co <<EMAIL>>", "sideEffects": false, "source": "./src/index.ts", "main": "./dist/gql-tada-internal", "module": "./dist/gql-tada-internal.mjs", "types": "./dist/gql-tada-internal.d.ts", "exports": {".": {"types": "./dist/gql-tada-internal.d.ts", "import": "./dist/gql-tada-internal.mjs", "require": "./dist/gql-tada-internal.js", "source": "./src/index.ts"}, "./package.json": "./package.json"}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist/"], "repository": {"type": "git", "url": "https://github.com/0no-co/gql.tada.git", "directory": "packages/internal"}, "bugs": {"url": "https://github.com/0no-co/gql.tada/issues"}, "homepage": "https://gql-tada.0no.co/", "license": "MIT", "devDependencies": {"@types/node": "^20.11.0", "@urql/core": "^5.0.2", "@urql/exchange-retry": "^1.2.1", "graphql": "^16.8.1", "rollup": "^4.9.4", "sade": "^1.8.1", "type-fest": "^4.10.2", "typescript": "^5.5.2"}, "dependencies": {"@0no-co/graphql.web": "^1.0.5"}, "peerDependencies": {"graphql": "^15.5.0 || ^16.0.0 || ^17.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"test": "vitest test", "build": "rollup -c ../../scripts/rollup.config.mjs", "clean": "rimraf dist node_modules/.cache"}}