import { Web3SideChainClient } from ".";
import { BridgeUtil, ZkEvmBridge } from "../zkevm";
import { IBaseClientConfig } from "..";
export declare class ZkEvmBridgeClient {
    client: Web3SideChainClient<IBaseClientConfig>;
    bridgeUtil: BridgeUtil;
    rootChainBridge: ZkEvmBridge;
    childChainBridge: ZkEvmBridge;
    /**
     * check whether a txHash is synced with child chain
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    isDepositClaimable(txHash: string): Promise<any>;
    /**
     * check whether proof is submitted on parent chain
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    isWithdrawExitable(txHash: string): Promise<any>;
    /**
     * check whether deposit is completed
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    isDeposited(txHash: string): Promise<string>;
    /**
     * check whether deposit is completed
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    isExited(txHash: string): Promise<string>;
}
