import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_archiver_rpc";
export interface BalanceRequest {
    /** Account address */
    account: string;
    /** Symbol resolution. Possible resolutions are 1D,1W,1M */
    resolution: string;
}
export interface BalanceResponse {
    historicalBalance: HistoricalBalance | undefined;
}
export interface HistoricalBalance {
    /** Time, Unix timestamp (UTC) */
    t: number[];
    /** Balance value */
    v: number[];
}
export interface RpnlRequest {
    /** Account address */
    account: string;
    /** Symbol resolution. Possible resolutions are 1D,1W,1M */
    resolution: string;
}
export interface RpnlResponse {
    historicalRpnl: HistoricalRPNL | undefined;
}
export interface HistoricalRPNL {
    /** Time, Unix timestamp (UTC) */
    t: number[];
    /** Realized Profit and Loss value */
    v: number[];
}
export interface VolumesRequest {
    /** Account address */
    account: string;
    /** Symbol resolution. Possible resolutions are 1D,1W,1M */
    resolution: string;
}
export interface VolumesResponse {
    historicalVolumes: HistoricalVolumes | undefined;
}
export interface HistoricalVolumes {
    /** Time, Unix timestamp (UTC) */
    t: number[];
    /** Volume value */
    v: number[];
}
export interface PnlLeaderboardRequest {
    /** Start date of the leaderboard period in unix time (ms) */
    startDate: string;
    /** End date of the leaderboard period in unix time (ms) */
    endDate: string;
    /** Number of leaderboard entries to return */
    limit: number;
    /** Account address that's querying the leaderboard */
    account: string;
}
export interface PnlLeaderboardResponse {
    /** First date of snapshots used for the leaderboard period */
    firstDate: string;
    /** Last date of snapshots used for the leaderboard period */
    lastDate: string;
    /** Leaderboard entries */
    leaders: LeaderboardRow[];
    /** Leaderboard entry for the querying account */
    accountRow: LeaderboardRow | undefined;
}
export interface LeaderboardRow {
    /** Account address */
    account: string;
    /** Realized profit and loss (USD) */
    pnl: number;
    /** Trade volume (USD) */
    volume: number;
    /** Rank in leaderboard */
    rank: number;
}
export interface VolLeaderboardRequest {
    /** Start date of the leaderboard period in unix time (ms) */
    startDate: string;
    /** End date of the leaderboard period in unix time (ms) */
    endDate: string;
    /** Number of leaderboard entries to return */
    limit: number;
    /** Account address that's querying the leaderboard */
    account: string;
}
export interface VolLeaderboardResponse {
    /** First date of snapshots used for the leaderboard period */
    firstDate: string;
    /** Last date of snapshots used for the leaderboard period */
    lastDate: string;
    /** Leaderboard entries */
    leaders: LeaderboardRow[];
    /** Leaderboard entry for the querying account */
    accountRow: LeaderboardRow | undefined;
}
export interface PnlLeaderboardFixedResolutionRequest {
    /** Leaderboard resolution. Possible resolutions are 1D,1W,1M,6M,ALL */
    resolution: string;
    /** Number of leaderboard entries to return */
    limit: number;
    /** Account address that's querying the leaderboard */
    account: string;
}
export interface PnlLeaderboardFixedResolutionResponse {
    /** First date of snapshots used for the leaderboard period */
    firstDate: string;
    /** Last date of snapshots used for the leaderboard period */
    lastDate: string;
    /** Leaderboard entries */
    leaders: LeaderboardRow[];
    /** Leaderboard entry for the querying account */
    accountRow: LeaderboardRow | undefined;
}
export interface VolLeaderboardFixedResolutionRequest {
    /** Leaderboard resolution. Possible resolutions are 1D,1W,1M,6M,ALL */
    resolution: string;
    /** Number of leaderboard entries to return */
    limit: number;
    /** Account address that's querying the leaderboard */
    account: string;
}
export interface VolLeaderboardFixedResolutionResponse {
    /** First date of snapshots used for the leaderboard period */
    firstDate: string;
    /** Last date of snapshots used for the leaderboard period */
    lastDate: string;
    /** Leaderboard entries */
    leaders: LeaderboardRow[];
    /** Leaderboard entry for the querying account */
    accountRow: LeaderboardRow | undefined;
}
export interface DenomHoldersRequest {
    /** Denom address */
    denom: string;
    /** Token for pagination */
    token: string;
    limit: number;
}
export interface DenomHoldersResponse {
    holders: Holder[];
    /** Next tokens for pagination */
    next: string[];
    /** Total number of holders */
    total: number;
}
export interface Holder {
    /** Account address for the holder */
    accountAddress: string;
    /** The balance of the holder */
    balance: string;
}
export interface HistoricalTradesRequest {
    /** The starting block height that the trades must be equal or older than */
    fromBlock: string;
    /** The ending block height that the trades must be equal or older than */
    endBlock: string;
    /**
     * The starting timestamp in UNIX milliseconds that the trades must be equal or
     * older than
     */
    fromTime: string;
    /**
     * The ending timestamp in UNIX milliseconds that the trades must be equal or
     * older than
     */
    endTime: string;
    /** The number of trades to return per page */
    perPage: number;
    /** Token for pagination */
    token: string;
    /** Account address */
    account: string;
}
export interface HistoricalTradesResponse {
    trades: HistoricalTrade[];
    /** The last block height available in the service */
    lastHeight: string;
    /** The timestamp of the last block available in the service */
    lastTime: string;
    /** Next token for pagination */
    next: string[];
}
export interface HistoricalTrade {
    /** Account address */
    account: string;
    /** The subaccountId that executed the trade */
    subaccountId: string;
    /** The ID of the market that this trade is in */
    marketId: string;
    /** The direction the trade */
    tradeDirection: string;
    /** Price level at which trade has been executed */
    price: PriceLevel | undefined;
    /** The fee associated with the trade (quote asset denom) */
    fee: string;
    /** Timestamp of trade execution in UNIX millis */
    executedAt: string;
    /** Block height of trade execution */
    executedHeight: string;
    /** Fee recipient address */
    feeRecipient: string;
    /** Trade's execution side, maker/taker */
    executionSide: string;
    /** USD value of the trade at the time of execution */
    usdValue: string;
    /** A list of flag assigned to the trade */
    flags: string[];
    /** Type of market */
    marketType: string;
    /** Unique trade ID */
    tradeId: string;
}
export interface PriceLevel {
    /** Price number of the price level. */
    price: string;
    /** Quantity of the price level. */
    quantity: string;
    /** Price level last updated timestamp in UNIX millis. */
    timestamp: string;
}
export declare const BalanceRequest: {
    encode(message: BalanceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BalanceRequest;
    fromJSON(object: any): BalanceRequest;
    toJSON(message: BalanceRequest): unknown;
    create(base?: DeepPartial<BalanceRequest>): BalanceRequest;
    fromPartial(object: DeepPartial<BalanceRequest>): BalanceRequest;
};
export declare const BalanceResponse: {
    encode(message: BalanceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BalanceResponse;
    fromJSON(object: any): BalanceResponse;
    toJSON(message: BalanceResponse): unknown;
    create(base?: DeepPartial<BalanceResponse>): BalanceResponse;
    fromPartial(object: DeepPartial<BalanceResponse>): BalanceResponse;
};
export declare const HistoricalBalance: {
    encode(message: HistoricalBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalBalance;
    fromJSON(object: any): HistoricalBalance;
    toJSON(message: HistoricalBalance): unknown;
    create(base?: DeepPartial<HistoricalBalance>): HistoricalBalance;
    fromPartial(object: DeepPartial<HistoricalBalance>): HistoricalBalance;
};
export declare const RpnlRequest: {
    encode(message: RpnlRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RpnlRequest;
    fromJSON(object: any): RpnlRequest;
    toJSON(message: RpnlRequest): unknown;
    create(base?: DeepPartial<RpnlRequest>): RpnlRequest;
    fromPartial(object: DeepPartial<RpnlRequest>): RpnlRequest;
};
export declare const RpnlResponse: {
    encode(message: RpnlResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RpnlResponse;
    fromJSON(object: any): RpnlResponse;
    toJSON(message: RpnlResponse): unknown;
    create(base?: DeepPartial<RpnlResponse>): RpnlResponse;
    fromPartial(object: DeepPartial<RpnlResponse>): RpnlResponse;
};
export declare const HistoricalRPNL: {
    encode(message: HistoricalRPNL, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalRPNL;
    fromJSON(object: any): HistoricalRPNL;
    toJSON(message: HistoricalRPNL): unknown;
    create(base?: DeepPartial<HistoricalRPNL>): HistoricalRPNL;
    fromPartial(object: DeepPartial<HistoricalRPNL>): HistoricalRPNL;
};
export declare const VolumesRequest: {
    encode(message: VolumesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolumesRequest;
    fromJSON(object: any): VolumesRequest;
    toJSON(message: VolumesRequest): unknown;
    create(base?: DeepPartial<VolumesRequest>): VolumesRequest;
    fromPartial(object: DeepPartial<VolumesRequest>): VolumesRequest;
};
export declare const VolumesResponse: {
    encode(message: VolumesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolumesResponse;
    fromJSON(object: any): VolumesResponse;
    toJSON(message: VolumesResponse): unknown;
    create(base?: DeepPartial<VolumesResponse>): VolumesResponse;
    fromPartial(object: DeepPartial<VolumesResponse>): VolumesResponse;
};
export declare const HistoricalVolumes: {
    encode(message: HistoricalVolumes, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalVolumes;
    fromJSON(object: any): HistoricalVolumes;
    toJSON(message: HistoricalVolumes): unknown;
    create(base?: DeepPartial<HistoricalVolumes>): HistoricalVolumes;
    fromPartial(object: DeepPartial<HistoricalVolumes>): HistoricalVolumes;
};
export declare const PnlLeaderboardRequest: {
    encode(message: PnlLeaderboardRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PnlLeaderboardRequest;
    fromJSON(object: any): PnlLeaderboardRequest;
    toJSON(message: PnlLeaderboardRequest): unknown;
    create(base?: DeepPartial<PnlLeaderboardRequest>): PnlLeaderboardRequest;
    fromPartial(object: DeepPartial<PnlLeaderboardRequest>): PnlLeaderboardRequest;
};
export declare const PnlLeaderboardResponse: {
    encode(message: PnlLeaderboardResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PnlLeaderboardResponse;
    fromJSON(object: any): PnlLeaderboardResponse;
    toJSON(message: PnlLeaderboardResponse): unknown;
    create(base?: DeepPartial<PnlLeaderboardResponse>): PnlLeaderboardResponse;
    fromPartial(object: DeepPartial<PnlLeaderboardResponse>): PnlLeaderboardResponse;
};
export declare const LeaderboardRow: {
    encode(message: LeaderboardRow, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LeaderboardRow;
    fromJSON(object: any): LeaderboardRow;
    toJSON(message: LeaderboardRow): unknown;
    create(base?: DeepPartial<LeaderboardRow>): LeaderboardRow;
    fromPartial(object: DeepPartial<LeaderboardRow>): LeaderboardRow;
};
export declare const VolLeaderboardRequest: {
    encode(message: VolLeaderboardRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolLeaderboardRequest;
    fromJSON(object: any): VolLeaderboardRequest;
    toJSON(message: VolLeaderboardRequest): unknown;
    create(base?: DeepPartial<VolLeaderboardRequest>): VolLeaderboardRequest;
    fromPartial(object: DeepPartial<VolLeaderboardRequest>): VolLeaderboardRequest;
};
export declare const VolLeaderboardResponse: {
    encode(message: VolLeaderboardResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolLeaderboardResponse;
    fromJSON(object: any): VolLeaderboardResponse;
    toJSON(message: VolLeaderboardResponse): unknown;
    create(base?: DeepPartial<VolLeaderboardResponse>): VolLeaderboardResponse;
    fromPartial(object: DeepPartial<VolLeaderboardResponse>): VolLeaderboardResponse;
};
export declare const PnlLeaderboardFixedResolutionRequest: {
    encode(message: PnlLeaderboardFixedResolutionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PnlLeaderboardFixedResolutionRequest;
    fromJSON(object: any): PnlLeaderboardFixedResolutionRequest;
    toJSON(message: PnlLeaderboardFixedResolutionRequest): unknown;
    create(base?: DeepPartial<PnlLeaderboardFixedResolutionRequest>): PnlLeaderboardFixedResolutionRequest;
    fromPartial(object: DeepPartial<PnlLeaderboardFixedResolutionRequest>): PnlLeaderboardFixedResolutionRequest;
};
export declare const PnlLeaderboardFixedResolutionResponse: {
    encode(message: PnlLeaderboardFixedResolutionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PnlLeaderboardFixedResolutionResponse;
    fromJSON(object: any): PnlLeaderboardFixedResolutionResponse;
    toJSON(message: PnlLeaderboardFixedResolutionResponse): unknown;
    create(base?: DeepPartial<PnlLeaderboardFixedResolutionResponse>): PnlLeaderboardFixedResolutionResponse;
    fromPartial(object: DeepPartial<PnlLeaderboardFixedResolutionResponse>): PnlLeaderboardFixedResolutionResponse;
};
export declare const VolLeaderboardFixedResolutionRequest: {
    encode(message: VolLeaderboardFixedResolutionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolLeaderboardFixedResolutionRequest;
    fromJSON(object: any): VolLeaderboardFixedResolutionRequest;
    toJSON(message: VolLeaderboardFixedResolutionRequest): unknown;
    create(base?: DeepPartial<VolLeaderboardFixedResolutionRequest>): VolLeaderboardFixedResolutionRequest;
    fromPartial(object: DeepPartial<VolLeaderboardFixedResolutionRequest>): VolLeaderboardFixedResolutionRequest;
};
export declare const VolLeaderboardFixedResolutionResponse: {
    encode(message: VolLeaderboardFixedResolutionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VolLeaderboardFixedResolutionResponse;
    fromJSON(object: any): VolLeaderboardFixedResolutionResponse;
    toJSON(message: VolLeaderboardFixedResolutionResponse): unknown;
    create(base?: DeepPartial<VolLeaderboardFixedResolutionResponse>): VolLeaderboardFixedResolutionResponse;
    fromPartial(object: DeepPartial<VolLeaderboardFixedResolutionResponse>): VolLeaderboardFixedResolutionResponse;
};
export declare const DenomHoldersRequest: {
    encode(message: DenomHoldersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DenomHoldersRequest;
    fromJSON(object: any): DenomHoldersRequest;
    toJSON(message: DenomHoldersRequest): unknown;
    create(base?: DeepPartial<DenomHoldersRequest>): DenomHoldersRequest;
    fromPartial(object: DeepPartial<DenomHoldersRequest>): DenomHoldersRequest;
};
export declare const DenomHoldersResponse: {
    encode(message: DenomHoldersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DenomHoldersResponse;
    fromJSON(object: any): DenomHoldersResponse;
    toJSON(message: DenomHoldersResponse): unknown;
    create(base?: DeepPartial<DenomHoldersResponse>): DenomHoldersResponse;
    fromPartial(object: DeepPartial<DenomHoldersResponse>): DenomHoldersResponse;
};
export declare const Holder: {
    encode(message: Holder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Holder;
    fromJSON(object: any): Holder;
    toJSON(message: Holder): unknown;
    create(base?: DeepPartial<Holder>): Holder;
    fromPartial(object: DeepPartial<Holder>): Holder;
};
export declare const HistoricalTradesRequest: {
    encode(message: HistoricalTradesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalTradesRequest;
    fromJSON(object: any): HistoricalTradesRequest;
    toJSON(message: HistoricalTradesRequest): unknown;
    create(base?: DeepPartial<HistoricalTradesRequest>): HistoricalTradesRequest;
    fromPartial(object: DeepPartial<HistoricalTradesRequest>): HistoricalTradesRequest;
};
export declare const HistoricalTradesResponse: {
    encode(message: HistoricalTradesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalTradesResponse;
    fromJSON(object: any): HistoricalTradesResponse;
    toJSON(message: HistoricalTradesResponse): unknown;
    create(base?: DeepPartial<HistoricalTradesResponse>): HistoricalTradesResponse;
    fromPartial(object: DeepPartial<HistoricalTradesResponse>): HistoricalTradesResponse;
};
export declare const HistoricalTrade: {
    encode(message: HistoricalTrade, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalTrade;
    fromJSON(object: any): HistoricalTrade;
    toJSON(message: HistoricalTrade): unknown;
    create(base?: DeepPartial<HistoricalTrade>): HistoricalTrade;
    fromPartial(object: DeepPartial<HistoricalTrade>): HistoricalTrade;
};
export declare const PriceLevel: {
    encode(message: PriceLevel, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PriceLevel;
    fromJSON(object: any): PriceLevel;
    toJSON(message: PriceLevel): unknown;
    create(base?: DeepPartial<PriceLevel>): PriceLevel;
    fromPartial(object: DeepPartial<PriceLevel>): PriceLevel;
};
/** InjectiveArchiverRPC defines gRPC API of Archiver provider. */
export interface InjectiveArchiverRPC {
    /** Provide historical balance data for a given account address. */
    Balance(request: DeepPartial<BalanceRequest>, metadata?: grpc.Metadata): Promise<BalanceResponse>;
    /** Provide historical realized profit and loss data for a given account address. */
    Rpnl(request: DeepPartial<RpnlRequest>, metadata?: grpc.Metadata): Promise<RpnlResponse>;
    /** Provide historical volumes for a given account address. */
    Volumes(request: DeepPartial<VolumesRequest>, metadata?: grpc.Metadata): Promise<VolumesResponse>;
    /** Provide pnl leaderboard data. */
    PnlLeaderboard(request: DeepPartial<PnlLeaderboardRequest>, metadata?: grpc.Metadata): Promise<PnlLeaderboardResponse>;
    /** Provide volume leaderboard data. */
    VolLeaderboard(request: DeepPartial<VolLeaderboardRequest>, metadata?: grpc.Metadata): Promise<VolLeaderboardResponse>;
    /** Provide pnl leaderboard data. */
    PnlLeaderboardFixedResolution(request: DeepPartial<PnlLeaderboardFixedResolutionRequest>, metadata?: grpc.Metadata): Promise<PnlLeaderboardFixedResolutionResponse>;
    /** Provide volume leaderboard data. */
    VolLeaderboardFixedResolution(request: DeepPartial<VolLeaderboardFixedResolutionRequest>, metadata?: grpc.Metadata): Promise<VolLeaderboardFixedResolutionResponse>;
    /** Provide a list of addresses holding a specific denom */
    DenomHolders(request: DeepPartial<DenomHoldersRequest>, metadata?: grpc.Metadata): Promise<DenomHoldersResponse>;
    /** Provide historical trades data. */
    HistoricalTrades(request: DeepPartial<HistoricalTradesRequest>, metadata?: grpc.Metadata): Promise<HistoricalTradesResponse>;
}
export declare class InjectiveArchiverRPCClientImpl implements InjectiveArchiverRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    Balance(request: DeepPartial<BalanceRequest>, metadata?: grpc.Metadata): Promise<BalanceResponse>;
    Rpnl(request: DeepPartial<RpnlRequest>, metadata?: grpc.Metadata): Promise<RpnlResponse>;
    Volumes(request: DeepPartial<VolumesRequest>, metadata?: grpc.Metadata): Promise<VolumesResponse>;
    PnlLeaderboard(request: DeepPartial<PnlLeaderboardRequest>, metadata?: grpc.Metadata): Promise<PnlLeaderboardResponse>;
    VolLeaderboard(request: DeepPartial<VolLeaderboardRequest>, metadata?: grpc.Metadata): Promise<VolLeaderboardResponse>;
    PnlLeaderboardFixedResolution(request: DeepPartial<PnlLeaderboardFixedResolutionRequest>, metadata?: grpc.Metadata): Promise<PnlLeaderboardFixedResolutionResponse>;
    VolLeaderboardFixedResolution(request: DeepPartial<VolLeaderboardFixedResolutionRequest>, metadata?: grpc.Metadata): Promise<VolLeaderboardFixedResolutionResponse>;
    DenomHolders(request: DeepPartial<DenomHoldersRequest>, metadata?: grpc.Metadata): Promise<DenomHoldersResponse>;
    HistoricalTrades(request: DeepPartial<HistoricalTradesRequest>, metadata?: grpc.Metadata): Promise<HistoricalTradesResponse>;
}
export declare const InjectiveArchiverRPCDesc: {
    serviceName: string;
};
export declare const InjectiveArchiverRPCBalanceDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCRpnlDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCVolumesDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCPnlLeaderboardDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCVolLeaderboardDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCPnlLeaderboardFixedResolutionDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCVolLeaderboardFixedResolutionDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCDenomHoldersDesc: UnaryMethodDefinitionish;
export declare const InjectiveArchiverRPCHistoricalTradesDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
