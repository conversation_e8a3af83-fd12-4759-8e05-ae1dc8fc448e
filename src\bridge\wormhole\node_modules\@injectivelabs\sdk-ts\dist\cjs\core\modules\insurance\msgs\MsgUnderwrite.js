"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgUnderwrite extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgUnderwrite(params);
    }
    toProto() {
        const { params } = this;
        const amountCoin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        amountCoin.denom = params.amount.denom;
        amountCoin.amount = params.amount.amount;
        const message = core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgUnderwrite.create();
        message.sender = params.injectiveAddress;
        message.marketId = params.marketId;
        message.deposit = amountCoin;
        return message;
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.insurance.v1beta1.MsgUnderwrite',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'insurance/MsgUnderwrite',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.insurance.v1beta1.MsgUnderwrite',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.insurance.v1beta1.MsgUnderwrite',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgUnderwrite.encode(this.toProto()).finish();
    }
}
exports.default = MsgUnderwrite;
