{"version": 3, "file": "directsecp256k1wallet.js", "sourceRoot": "", "sources": ["../src/directsecp256k1wallet.ts"], "names": [], "mappings": ";;;AAAA,yCAAyF;AACzF,2CAAmD;AACnD,+CAA4C;AAI5C,uCAA0C;AAE1C;;;;GAIG;AACH,MAAa,qBAAqB;IAChC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAmB,EAAE,MAAM,GAAG,QAAQ;QAChE,MAAM,YAAY,GAAG,CAAC,MAAM,kBAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;QACnE,OAAO,IAAI,qBAAqB,CAAC,OAAO,EAAE,kBAAS,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5F,CAAC;IAMD,YAAoB,OAAmB,EAAE,MAAkB,EAAE,MAAc;QACzE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,IAAY,OAAO;QACjB,OAAO,IAAA,mBAAQ,EAAC,IAAI,CAAC,MAAM,EAAE,IAAA,sCAA8B,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5E,CAAC;IAEM,KAAK,CAAC,WAAW;QACtB,OAAO;YACL;gBACE,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB;SACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,OAAgB;QACvD,MAAM,SAAS,GAAG,IAAA,uBAAa,EAAC,OAAO,CAAC,CAAC;QACzC,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,sBAAsB,CAAC,CAAC;SAC3D;QACD,MAAM,aAAa,GAAG,IAAA,eAAM,EAAC,SAAS,CAAC,CAAC;QACxC,MAAM,SAAS,GAAG,MAAM,kBAAS,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/E,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChF,MAAM,YAAY,GAAG,IAAA,gCAAwB,EAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAC3E,OAAO;YACL,MAAM,EAAE,OAAO;YACf,SAAS,EAAE,YAAY;SACxB,CAAC;IACJ,CAAC;CACF;AAlDD,sDAkDC"}