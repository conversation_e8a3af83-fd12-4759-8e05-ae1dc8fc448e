{"version": 3, "sources": ["../../../../src/api/transactionSubmission/sign.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Account } from \"../../account\";\nimport { signTransaction } from \"../../internal/transactionSubmission\";\nimport { AccountAuthenticator, AnyRawTransaction } from \"../../transactions\";\nimport { AptosConfig } from \"../aptosConfig\";\n\n/**\n * A class to handle all `Sign` transaction operations.\n *\n * @param config - The configuration object for Aptos.\n * @group Implementation\n */\nexport class Sign {\n  readonly config: AptosConfig;\n\n  /**\n   * Creates an instance of the Aptos client with the specified configuration.\n   *\n   * @param config - The configuration settings for the Aptos client.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network } from \"@aptos-labs/ts-sdk\";\n   *\n   * async function runExample() {\n   *     // Create a new Aptos client with testnet configuration\n   *     const config = new AptosConfig({ network: Network.TESTNET });\n   *     const aptos = new Aptos(config);\n   *\n   *     console.log(\"Aptos client created with config:\", config);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Implementation\n   */\n  constructor(config: AptosConfig) {\n    this.config = config;\n  }\n\n  /**\n   * Signs a transaction using the provided account signer. This function is essential for ensuring that transactions are properly\n   * authenticated before being sent to the network.\n   *\n   * @param args - The arguments for signing the transaction.\n   * @param args.signer - The account that will sign the transaction.\n   * @param args.transaction - The raw transaction data to be signed.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network, Account } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   const sender = Account.generate(); // Generate a new account for signing\n   *   const transaction = await aptos.transaction.build.simple({\n   *     sender: sender.accountAddress,\n   *     data: {\n   *       function: \"0x1::aptos_account::transfer\",\n   *       functionArguments: [ \"0x1\", 100 ], // replace with a real account address and amount\n   *     },\n   *   });\n   *\n   *   // Sign the transaction\n   *   const signedTransaction = await aptos.transaction.sign({\n   *     signer: sender,\n   *     transaction: transaction,\n   *   });\n   *\n   *   console.log(\"Signed Transaction:\", signedTransaction);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Implementation\n   */\n  // eslint-disable-next-line class-methods-use-this\n  transaction(args: { signer: Account; transaction: AnyRawTransaction }): AccountAuthenticator {\n    return signTransaction({\n      ...args,\n    });\n  }\n\n  /**\n   * Sets the fee payer address for a transaction and signs it with the provided account.\n   * This function is essential for transactions that require a designated fee payer.\n   *\n   * @param args - The arguments for the function.\n   * @param args.signer - The account that will sign the transaction.\n   * @param args.transaction - The transaction object that requires a fee payer address.\n   *\n   * @throws Error if the transaction does not have a feePayerAddress property.\n   *\n   * @example\n   * ```typescript\n   * import { Aptos, AptosConfig, Network, Account } from \"@aptos-labs/ts-sdk\";\n   *\n   * const config = new AptosConfig({ network: Network.TESTNET });\n   * const aptos = new Aptos(config);\n   *\n   * async function runExample() {\n   *   const sender = Account.generate(); // Generate a new account for signing\n   *   const transaction = await aptos.transaction.build.simple({\n   *     sender: sender.accountAddress,\n   *     data: {\n   *       function: \"0x1::aptos_account::transfer\",\n   *       functionArguments: [\"0x1\", 100], // replace with a real recipient address\n   *     },\n   *   });\n   *\n   *   // Set the fee payer for the transaction\n   *   transaction.feePayerAddress = \"0x1\"; // replace with a real fee payer address\n   *\n   *   const signedTransaction = await aptos.transactionAsFeePayer({ signer: sender, transaction });\n   *\n   *   console.log(\"Signed Transaction:\", signedTransaction);\n   * }\n   * runExample().catch(console.error);\n   * ```\n   * @group Implementation\n   */\n  // eslint-disable-next-line class-methods-use-this\n  transactionAsFeePayer(args: { signer: Account; transaction: AnyRawTransaction }): AccountAuthenticator {\n    const { signer, transaction } = args;\n\n    // if transaction doesn't hold a \"feePayerAddress\" prop it means\n    // this is not a fee payer transaction\n    if (!transaction.feePayerAddress) {\n      throw new Error(`Transaction ${transaction} is not a Fee Payer transaction`);\n    }\n\n    // Set the feePayerAddress to the signer account address\n    transaction.feePayerAddress = signer.accountAddress;\n\n    return signTransaction({\n      signer,\n      transaction,\n    });\n  }\n}\n"], "mappings": "y8DAcO,IAAMA,EAAN,KAAW,CAuBhB,YAAYC,EAAqB,CAC/B,KAAK,OAASA,CAChB,CAwCA,YAAYC,EAAiF,CAC3F,OAAOC,EAAgB,CACrB,GAAGD,CACL,CAAC,CACH,CAyCA,sBAAsBA,EAAiF,CACrG,GAAM,CAAE,OAAAE,EAAQ,YAAAC,CAAY,EAAIH,EAIhC,GAAI,CAACG,EAAY,gBACf,MAAM,IAAI,MAAM,eAAeA,CAAW,iCAAiC,EAI7E,OAAAA,EAAY,gBAAkBD,EAAO,eAE9BD,EAAgB,CACrB,OAAAC,EACA,YAAAC,CACF,CAAC,CACH,CACF", "names": ["Sign", "config", "args", "signTransaction", "signer", "transaction"]}