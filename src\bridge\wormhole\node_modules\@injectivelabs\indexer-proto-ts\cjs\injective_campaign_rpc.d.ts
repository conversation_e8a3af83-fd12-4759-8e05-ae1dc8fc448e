import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_campaign_rpc";
export interface RankingRequest {
    /** Campaign ID */
    campaignId: string;
    /** MarketId of the campaign */
    marketId: string;
    /** Account address */
    accountAddress: string;
    limit: number;
    skip: string;
    /** Contract address that manages the round and reward */
    contractAddress: string;
}
export interface RankingResponse {
    /** The campaign information */
    campaign: Campaign | undefined;
    /** The campaign users */
    users: CampaignUser[];
    paging: Paging | undefined;
}
export interface Campaign {
    campaignId: string;
    /** MarketId of the trading strategy */
    marketId: string;
    /** Total campaign score */
    totalScore: string;
    /** Last time the campaign score has been updated. */
    lastUpdated: string;
    /** Campaign start date in UNIX millis. */
    startDate: string;
    /** Campaign end date in UNIX millis. */
    endDate: string;
    /** Whether the campaign rewards can be claimed. */
    isClaimable: boolean;
    /** Campaigns round ID */
    roundId: number;
    /** Contract address that controls this campaign */
    managerContract: string;
    /** Reward tokens of this campaign */
    rewards: Coin[];
    /**
     * Total user score if accountAddress is passed, this is useful to estimate
     * account's reward
     */
    userScore: string;
    /** Return true if user claimed the reward of this campaign */
    userClaimed: boolean;
    /** Suffix of the subaccount that eligible for volume score */
    subaccountIdSuffix: string;
    /** Contract that manage users reward */
    rewardContract: string;
    /**
     * Version of reward contract, UI use this to determine the message that need
     * to be sent
     */
    version: string;
    /** Campaign type */
    type: string;
}
export interface Coin {
    /** Denom of the coin */
    denom: string;
    amount: string;
    usdValue: string;
}
export interface CampaignUser {
    campaignId: string;
    /** MarketId of the trading strategy */
    marketId: string;
    /** Account address */
    accountAddress: string;
    /** Campaign score */
    score: string;
    /** Whether the distribution contract has been updated with the latest score */
    contractUpdated: boolean;
    /** Block height when the score has been updated. */
    blockHeight: string;
    /** Block time timestamp in UNIX millis. */
    blockTime: string;
    /** Amount swapped but only count base denom of the market */
    purchasedAmount: string;
    /**
     * True if this user is updated to be in Galxe Campain list, only eligible
     * address are added
     */
    galxeUpdated: boolean;
    /** True if this user claimed the reward */
    rewardClaimed: boolean;
}
/** Paging defines the structure for required params for handling pagination */
export interface Paging {
    /** total number of txs saved in database */
    total: string;
    /** can be either block height or index num */
    from: number;
    /** can be either block height or index num */
    to: number;
    /** count entries by subaccount, serving some places on helix */
    countBySubaccount: string;
    /** array of tokens to navigate to the next pages */
    next: string[];
}
export interface CampaignsRequest {
    /** Round ID, if not specified, it will return latest roundId */
    roundId: string;
    /** Address of login account, if not specified it will return no user rewards */
    accountAddress: string;
    /**
     * This will return campaign x where x.roundId <= toRoundId. Useful for listing
     * multiple rounds
     */
    toRoundId: number;
    /** Contract address that manages the round and reward */
    contractAddress: string;
    /** Campaign type */
    type: string;
}
export interface CampaignsResponse {
    campaigns: Campaign[];
    accumulatedRewards: Coin[];
    rewardCount: number;
}
export interface CampaignsV2Request {
    /** Campaign type */
    type: string;
    /**
     * Whether the campaign is active.
     * Deprecated: use status instead.
     */
    active: boolean;
    /** Limit number of returned campaigns */
    limit: number;
    /** Cursor for pagination */
    cursor: string;
    /** Filter campaigns by start date greater than this value in milliseconds */
    fromStartDate: string;
    /** Filter campaigns by start date lower than this value in milliseconds */
    toStartDate: string;
    /** Filter campaigns by end date greater than this value in milliseconds */
    fromEndDate: string;
    /** Filter campaigns by end date lower than this value in milliseconds */
    toEndDate: string;
    /** Filter campaigns by status */
    status: string;
}
export interface CampaignsV2Response {
    campaigns: CampaignV2[];
    cursor: string;
}
export interface CampaignV2 {
    campaignId: string;
    /** MarketId of the trading strategy */
    marketId: string;
    /** Total campaign score */
    totalScore: string;
    /** Campaign creation date in UNIX millis. */
    createdAt: string;
    /** Campaign last update date in UNIX millis. */
    updatedAt: string;
    /** Campaign start date in UNIX millis. */
    startDate: string;
    /** Campaign end date in UNIX millis. */
    endDate: string;
    /** Whether the campaign rewards can be claimed. */
    isClaimable: boolean;
    /** Campaigns round ID */
    roundId: number;
    /** Contract address that controls this campaign */
    managerContract: string;
    /** Reward tokens of this campaign */
    rewards: Coin[];
    /** Suffix of the subaccount that eligible for volume score */
    subaccountIdSuffix: string;
    /** Contract that manage users reward */
    rewardContract: string;
    /** Campaign type */
    type: string;
    /**
     * Version of reward contract, UI use this to determine the message that need
     * to be sent
     */
    version: string;
    /** Campaign name */
    name: string;
    /** Campaign description */
    description: string;
}
export interface ListGuildsRequest {
    /** Campaign contract address */
    campaignContract: string;
    /** Limit number of returned guilds */
    limit: number;
    /** Skip some first guilds in the list for next page */
    skip: number;
    /** Sort by some metrics */
    sortBy: string;
}
export interface ListGuildsResponse {
    guilds: Guild[];
    paging: Paging | undefined;
    /** Snapshot updated at time in UNIX milli */
    updatedAt: string;
    /** Summary of the campaign */
    campaignSummary: CampaignSummary | undefined;
}
export interface Guild {
    campaignContract: string;
    /** Guild ID */
    guildId: string;
    /** Guild's master address */
    masterAddress: string;
    /** Guild creation date (in UNIX milliseconds) */
    createdAt: string;
    /** Average TVL score */
    tvlScore: string;
    /** Total volume score */
    volumeScore: string;
    /** guild's rank by volume */
    rankByVolume: number;
    /** guild's rank by TVL */
    rankByTvl: number;
    /**
     * guild's logo, at the moment it supports numberic string (i.e '1', '2' and so
     * on) not a random URL because of front end limitation
     */
    logo: string;
    /** guild's total TVL */
    totalTvl: string;
    /** Snapshot updated at time in UNIX milli */
    updatedAt: string;
    /** Guild name */
    name: string;
    /**
     * Active status of guild, true when master total tvl meets the minimum
     * requirements
     */
    isActive: boolean;
    /** Master balance (in current campaigns denom) */
    masterBalance: string;
    /** Guild description, set by master of the guild */
    description: string;
}
export interface CampaignSummary {
    /** Campaign id */
    campaignId: string;
    /** Guild manager contract address */
    campaignContract: string;
    /** Number of guild in the campaign */
    totalGuildsCount: number;
    /** Total TVL */
    totalTvl: string;
    /** Sum average TVL of all guilds */
    totalAverageTvl: string;
    /** Total volume across all guilds (in market quote denom, often USDT) */
    totalVolume: string;
    /** Snapshot updated at time in UNIX milli */
    updatedAt: string;
    /** Total member joined the campaign (include guild masters) */
    totalMembersCount: number;
    /** Campaign start time */
    startTime: string;
    /** Campaign end time */
    endTime: string;
}
export interface ListGuildMembersRequest {
    /** Campaign contract address */
    campaignContract: string;
    /** ID of guild, inside campaign */
    guildId: string;
    /** Limit number of returned guild members */
    limit: number;
    /** Skip some first guild members in the list for next page */
    skip: number;
    /**
     * whether to include guild summary info, it's better to use this in terms of
     * latency, instead of sending 2 requests we just need once
     */
    includeGuildInfo: boolean;
    /** Sort by some metrics */
    sortBy: string;
}
export interface ListGuildMembersResponse {
    members: GuildMember[];
    paging: Paging | undefined;
    guildInfo: Guild | undefined;
}
export interface GuildMember {
    /** Guild manager contract address */
    campaignContract: string;
    /** Guild ID */
    guildId: string;
    /** Guild member address */
    address: string;
    /** Guild enrollment date (in UNIX milliseconds) */
    joinedAt: string;
    /** Average TVL score */
    tvlScore: string;
    /** Total volume score */
    volumeScore: string;
    /** Total volume score */
    totalTvl: string;
    /** Volume percentage out of guilds total volume */
    volumeScorePercentage: number;
    /** TVL percentage out of guilds total TVL score */
    tvlScorePercentage: number;
    /** Rewards for volume campaign (amount+denom) */
    tvlReward: Coin[];
    /** Rewards for TVL campaign (amount+denom) */
    volumeReward: Coin[];
}
export interface GetGuildMemberRequest {
    /** Campaign contract address */
    campaignContract: string;
    /** User address */
    address: string;
}
export interface GetGuildMemberResponse {
    info: GuildMember | undefined;
}
export declare const RankingRequest: {
    encode(message: RankingRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RankingRequest;
    fromJSON(object: any): RankingRequest;
    toJSON(message: RankingRequest): unknown;
    create(base?: DeepPartial<RankingRequest>): RankingRequest;
    fromPartial(object: DeepPartial<RankingRequest>): RankingRequest;
};
export declare const RankingResponse: {
    encode(message: RankingResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RankingResponse;
    fromJSON(object: any): RankingResponse;
    toJSON(message: RankingResponse): unknown;
    create(base?: DeepPartial<RankingResponse>): RankingResponse;
    fromPartial(object: DeepPartial<RankingResponse>): RankingResponse;
};
export declare const Campaign: {
    encode(message: Campaign, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Campaign;
    fromJSON(object: any): Campaign;
    toJSON(message: Campaign): unknown;
    create(base?: DeepPartial<Campaign>): Campaign;
    fromPartial(object: DeepPartial<Campaign>): Campaign;
};
export declare const Coin: {
    encode(message: Coin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Coin;
    fromJSON(object: any): Coin;
    toJSON(message: Coin): unknown;
    create(base?: DeepPartial<Coin>): Coin;
    fromPartial(object: DeepPartial<Coin>): Coin;
};
export declare const CampaignUser: {
    encode(message: CampaignUser, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignUser;
    fromJSON(object: any): CampaignUser;
    toJSON(message: CampaignUser): unknown;
    create(base?: DeepPartial<CampaignUser>): CampaignUser;
    fromPartial(object: DeepPartial<CampaignUser>): CampaignUser;
};
export declare const Paging: {
    encode(message: Paging, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Paging;
    fromJSON(object: any): Paging;
    toJSON(message: Paging): unknown;
    create(base?: DeepPartial<Paging>): Paging;
    fromPartial(object: DeepPartial<Paging>): Paging;
};
export declare const CampaignsRequest: {
    encode(message: CampaignsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignsRequest;
    fromJSON(object: any): CampaignsRequest;
    toJSON(message: CampaignsRequest): unknown;
    create(base?: DeepPartial<CampaignsRequest>): CampaignsRequest;
    fromPartial(object: DeepPartial<CampaignsRequest>): CampaignsRequest;
};
export declare const CampaignsResponse: {
    encode(message: CampaignsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignsResponse;
    fromJSON(object: any): CampaignsResponse;
    toJSON(message: CampaignsResponse): unknown;
    create(base?: DeepPartial<CampaignsResponse>): CampaignsResponse;
    fromPartial(object: DeepPartial<CampaignsResponse>): CampaignsResponse;
};
export declare const CampaignsV2Request: {
    encode(message: CampaignsV2Request, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignsV2Request;
    fromJSON(object: any): CampaignsV2Request;
    toJSON(message: CampaignsV2Request): unknown;
    create(base?: DeepPartial<CampaignsV2Request>): CampaignsV2Request;
    fromPartial(object: DeepPartial<CampaignsV2Request>): CampaignsV2Request;
};
export declare const CampaignsV2Response: {
    encode(message: CampaignsV2Response, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignsV2Response;
    fromJSON(object: any): CampaignsV2Response;
    toJSON(message: CampaignsV2Response): unknown;
    create(base?: DeepPartial<CampaignsV2Response>): CampaignsV2Response;
    fromPartial(object: DeepPartial<CampaignsV2Response>): CampaignsV2Response;
};
export declare const CampaignV2: {
    encode(message: CampaignV2, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignV2;
    fromJSON(object: any): CampaignV2;
    toJSON(message: CampaignV2): unknown;
    create(base?: DeepPartial<CampaignV2>): CampaignV2;
    fromPartial(object: DeepPartial<CampaignV2>): CampaignV2;
};
export declare const ListGuildsRequest: {
    encode(message: ListGuildsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListGuildsRequest;
    fromJSON(object: any): ListGuildsRequest;
    toJSON(message: ListGuildsRequest): unknown;
    create(base?: DeepPartial<ListGuildsRequest>): ListGuildsRequest;
    fromPartial(object: DeepPartial<ListGuildsRequest>): ListGuildsRequest;
};
export declare const ListGuildsResponse: {
    encode(message: ListGuildsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListGuildsResponse;
    fromJSON(object: any): ListGuildsResponse;
    toJSON(message: ListGuildsResponse): unknown;
    create(base?: DeepPartial<ListGuildsResponse>): ListGuildsResponse;
    fromPartial(object: DeepPartial<ListGuildsResponse>): ListGuildsResponse;
};
export declare const Guild: {
    encode(message: Guild, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Guild;
    fromJSON(object: any): Guild;
    toJSON(message: Guild): unknown;
    create(base?: DeepPartial<Guild>): Guild;
    fromPartial(object: DeepPartial<Guild>): Guild;
};
export declare const CampaignSummary: {
    encode(message: CampaignSummary, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CampaignSummary;
    fromJSON(object: any): CampaignSummary;
    toJSON(message: CampaignSummary): unknown;
    create(base?: DeepPartial<CampaignSummary>): CampaignSummary;
    fromPartial(object: DeepPartial<CampaignSummary>): CampaignSummary;
};
export declare const ListGuildMembersRequest: {
    encode(message: ListGuildMembersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListGuildMembersRequest;
    fromJSON(object: any): ListGuildMembersRequest;
    toJSON(message: ListGuildMembersRequest): unknown;
    create(base?: DeepPartial<ListGuildMembersRequest>): ListGuildMembersRequest;
    fromPartial(object: DeepPartial<ListGuildMembersRequest>): ListGuildMembersRequest;
};
export declare const ListGuildMembersResponse: {
    encode(message: ListGuildMembersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListGuildMembersResponse;
    fromJSON(object: any): ListGuildMembersResponse;
    toJSON(message: ListGuildMembersResponse): unknown;
    create(base?: DeepPartial<ListGuildMembersResponse>): ListGuildMembersResponse;
    fromPartial(object: DeepPartial<ListGuildMembersResponse>): ListGuildMembersResponse;
};
export declare const GuildMember: {
    encode(message: GuildMember, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GuildMember;
    fromJSON(object: any): GuildMember;
    toJSON(message: GuildMember): unknown;
    create(base?: DeepPartial<GuildMember>): GuildMember;
    fromPartial(object: DeepPartial<GuildMember>): GuildMember;
};
export declare const GetGuildMemberRequest: {
    encode(message: GetGuildMemberRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetGuildMemberRequest;
    fromJSON(object: any): GetGuildMemberRequest;
    toJSON(message: GetGuildMemberRequest): unknown;
    create(base?: DeepPartial<GetGuildMemberRequest>): GetGuildMemberRequest;
    fromPartial(object: DeepPartial<GetGuildMemberRequest>): GetGuildMemberRequest;
};
export declare const GetGuildMemberResponse: {
    encode(message: GetGuildMemberResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetGuildMemberResponse;
    fromJSON(object: any): GetGuildMemberResponse;
    toJSON(message: GetGuildMemberResponse): unknown;
    create(base?: DeepPartial<GetGuildMemberResponse>): GetGuildMemberResponse;
    fromPartial(object: DeepPartial<GetGuildMemberResponse>): GetGuildMemberResponse;
};
/** InjectiveCampaignRPC defined a gRPC service for Injective Campaigns. */
export interface InjectiveCampaignRPC {
    /** Lists all participants in campaign */
    Ranking(request: DeepPartial<RankingRequest>, metadata?: grpc.Metadata): Promise<RankingResponse>;
    /** List current round active campaigns */
    Campaigns(request: DeepPartial<CampaignsRequest>, metadata?: grpc.Metadata): Promise<CampaignsResponse>;
    /** List campaigns v2 */
    CampaignsV2(request: DeepPartial<CampaignsV2Request>, metadata?: grpc.Metadata): Promise<CampaignsV2Response>;
    /** List guilds by campaign */
    ListGuilds(request: DeepPartial<ListGuildsRequest>, metadata?: grpc.Metadata): Promise<ListGuildsResponse>;
    /** List guild members of given campaign and guildId */
    ListGuildMembers(request: DeepPartial<ListGuildMembersRequest>, metadata?: grpc.Metadata): Promise<ListGuildMembersResponse>;
    /** Get single member guild info */
    GetGuildMember(request: DeepPartial<GetGuildMemberRequest>, metadata?: grpc.Metadata): Promise<GetGuildMemberResponse>;
}
export declare class InjectiveCampaignRPCClientImpl implements InjectiveCampaignRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    Ranking(request: DeepPartial<RankingRequest>, metadata?: grpc.Metadata): Promise<RankingResponse>;
    Campaigns(request: DeepPartial<CampaignsRequest>, metadata?: grpc.Metadata): Promise<CampaignsResponse>;
    CampaignsV2(request: DeepPartial<CampaignsV2Request>, metadata?: grpc.Metadata): Promise<CampaignsV2Response>;
    ListGuilds(request: DeepPartial<ListGuildsRequest>, metadata?: grpc.Metadata): Promise<ListGuildsResponse>;
    ListGuildMembers(request: DeepPartial<ListGuildMembersRequest>, metadata?: grpc.Metadata): Promise<ListGuildMembersResponse>;
    GetGuildMember(request: DeepPartial<GetGuildMemberRequest>, metadata?: grpc.Metadata): Promise<GetGuildMemberResponse>;
}
export declare const InjectiveCampaignRPCDesc: {
    serviceName: string;
};
export declare const InjectiveCampaignRPCRankingDesc: UnaryMethodDefinitionish;
export declare const InjectiveCampaignRPCCampaignsDesc: UnaryMethodDefinitionish;
export declare const InjectiveCampaignRPCCampaignsV2Desc: UnaryMethodDefinitionish;
export declare const InjectiveCampaignRPCListGuildsDesc: UnaryMethodDefinitionish;
export declare const InjectiveCampaignRPCListGuildMembersDesc: UnaryMethodDefinitionish;
export declare const InjectiveCampaignRPCGetGuildMemberDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
