import _m0 from "protobufjs/minimal.js";
import { Any } from "../../../google/protobuf/any";
export declare const protobufPackage = "cosmos.crypto.multisig";
/**
 * LegacyAminoPubKey specifies a public key type
 * which nests multiple public keys and a threshold,
 * it uses legacy amino address rules.
 */
export interface LegacyAminoPubKey {
    threshold: number;
    publicKeys: Any[];
}
export declare const LegacyAminoPubKey: {
    encode(message: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LegacyAminoPubKey;
    fromJSON(object: any): LegacyAminoPubKey;
    toJSON(message: <PERSON><PERSON><PERSON>o<PERSON>ubK<PERSON>): unknown;
    create(base?: DeepPartial<LegacyAminoPubKey>): Legacy<PERSON>mino<PERSON><PERSON><PERSON><PERSON>;
    fromPartial(object: DeepPartial<LegacyAminoPubKey>): LegacyAminoPubKey;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
