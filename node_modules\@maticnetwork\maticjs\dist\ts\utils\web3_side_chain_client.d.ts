import { IBaseClientConfig } from "../interfaces";
import { BaseWeb3Client } from "../abstracts";
import { ABIManager } from "../utils";
import { Logger } from "./logger";
export declare class Web3SideChainClient<T_CONFIG> {
    parent: BaseWeb3Client;
    child: BaseWeb3Client;
    config: T_CONFIG;
    abiManager: ABIManager;
    logger: Logger;
    resolution: {};
    init(config: IBaseClientConfig): Promise<void>;
    getABI(name: string, type?: string): Promise<any>;
    getConfig(path: string): any;
    get mainPlasmaContracts(): any;
    get mainPOSContracts(): any;
    get mainZkEvmContracts(): any;
    get zkEvmContracts(): any;
    isEIP1559Supported(chainId: number): boolean;
}
