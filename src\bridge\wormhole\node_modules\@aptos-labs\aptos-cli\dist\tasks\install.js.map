{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../bin/tasks/install.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;AAEhC,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAG9D,MAAM,CAAC,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;IACnC,MAAM,IAAI,GAAG,eAAe,EAAE,CAAC;IAC/B,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,OAAO;IACT,CAAC;IAED,MAAM,gBAAgB,GAAG,MAAM,kBAAkB,EAAE,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,iCAAiC,gBAAgB,EAAE,CAAC,CAAC;IACjE,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC;IAEnB,IAAI,EAAE,KAAK,SAAS,EAAE,CAAC;QACrB,MAAM,GAAG,GAAG,GAAG,mBAAmB,IAAI,KAAK,KAAK,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,IAAI,EAAE,aAAa,CAAC;QAEjH,QAAQ,CACN,oIAAoI,GAAG,4JAA4J,IAAI,KAAK,CAC7S,CAAC;IACJ,CAAC;SAAM,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;QAE1B,aAAa,CAAC,oBAAoB,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;IAC5D,CAAC;SAAM,CAAC;QAGN,IAAI,SAAS,GAAG,QAAQ,CAAC;QACzB,IAAI,eAAe,GAAG,OAAO,CAAC;QAC9B,IAAI,CAAC;YACH,eAAe,GAAG,wBAAwB,EAAE,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CACT,qEAAqE,CACtE,CAAC;QACJ,CAAC;QAED,IAAI,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,SAAS,GAAG,cAAc,CAAC;QAC7B,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC;QACzD,MAAM,GAAG,GAAG,GAAG,mBAAmB,IAAI,KAAK,KAAK,gBAAgB,IAAI,KAAK,IAAI,gBAAgB,IAAI,EAAE,IAAI,SAAS,MAAM,CAAC;QAEvH,QAAQ,CACN,6BAA6B,GAAG,uDAAuD,IAAI,GAAG,CAC/F,CAAC;IACJ,CAAC;AACH,CAAC,CAAC"}