"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prepareSignBytes = prepareSignBytes;
function prepareSignBytes(obj) {
    if (Array.isArray(obj)) {
        return obj.map(prepareSignBytes);
    }
    // string, number, or null
    if (typeof obj !== `object` || obj === null) {
        return obj;
    }
    const sorted = {};
    Object.keys(obj)
        .sort()
        .forEach((key) => {
        if (obj[key] === undefined || obj[key] === null)
            return;
        sorted[key] = prepareSignBytes(obj[key]);
    });
    return sorted;
}
