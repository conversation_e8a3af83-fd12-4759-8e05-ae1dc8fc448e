import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_insurance_rpc";
export interface FundsRequest {
}
export interface FundsResponse {
    funds: InsuranceFund[];
}
export interface InsuranceFund {
    /** Ticker of the derivative market. */
    marketTicker: string;
    /** Derivative Market ID */
    marketId: string;
    /** Coin denom used for the underwriting of the insurance fund. */
    depositDenom: string;
    /** Pool token denom */
    poolTokenDenom: string;
    /** Redemption notice period duration in seconds. */
    redemptionNoticePeriodDuration: string;
    balance: string;
    totalShare: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Oracle Type */
    oracleType: string;
    /** Defines the expiry, if any */
    expiry: string;
    /** Token metadata for the deposit asset */
    depositTokenMeta: TokenMeta | undefined;
}
export interface TokenMeta {
    /** Token full name */
    name: string;
    /** Token contract address (native or not) */
    address: string;
    /** Token symbol short name */
    symbol: string;
    /** URL to the logo image */
    logo: string;
    /** Token decimals */
    decimals: number;
    /** Token metadata fetched timestamp in UNIX millis. */
    updatedAt: string;
}
export interface FundRequest {
    /** denom of insurance fund */
    denom: string;
}
export interface FundResponse {
    /** The insurance fund corresponding to the provided token denom */
    fund: InsuranceFund | undefined;
}
export interface RedemptionsRequest {
    /** Account address of the redemption owner */
    redeemer: string;
    /** Denom of the insurance pool token. */
    redemptionDenom: string;
    /** Status of the redemption. Either pending or disbursed. */
    status: string;
}
export interface RedemptionsResponse {
    redemptionSchedules: RedemptionSchedule[];
}
export interface RedemptionSchedule {
    /** Redemption ID. */
    redemptionId: string;
    /** Status of the redemption. Either pending or disbursed. */
    status: string;
    /** Account address of the redemption owner */
    redeemer: string;
    /** Claimable redemption time in seconds */
    claimableRedemptionTime: string;
    /** Amount of pool tokens being redeemed. */
    redemptionAmount: string;
    /** Pool token denom being redeemed. */
    redemptionDenom: string;
    /** Redemption request time in unix milliseconds. */
    requestedAt: string;
    /** Amount of quote tokens disbursed */
    disbursedAmount: string;
    /** Denom of the quote tokens disbursed */
    disbursedDenom: string;
    /** Redemption disbursement time in unix milliseconds. */
    disbursedAt: string;
}
export declare const FundsRequest: {
    encode(_: FundsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundsRequest;
    fromJSON(_: any): FundsRequest;
    toJSON(_: FundsRequest): unknown;
    create(base?: DeepPartial<FundsRequest>): FundsRequest;
    fromPartial(_: DeepPartial<FundsRequest>): FundsRequest;
};
export declare const FundsResponse: {
    encode(message: FundsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundsResponse;
    fromJSON(object: any): FundsResponse;
    toJSON(message: FundsResponse): unknown;
    create(base?: DeepPartial<FundsResponse>): FundsResponse;
    fromPartial(object: DeepPartial<FundsResponse>): FundsResponse;
};
export declare const InsuranceFund: {
    encode(message: InsuranceFund, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InsuranceFund;
    fromJSON(object: any): InsuranceFund;
    toJSON(message: InsuranceFund): unknown;
    create(base?: DeepPartial<InsuranceFund>): InsuranceFund;
    fromPartial(object: DeepPartial<InsuranceFund>): InsuranceFund;
};
export declare const TokenMeta: {
    encode(message: TokenMeta, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TokenMeta;
    fromJSON(object: any): TokenMeta;
    toJSON(message: TokenMeta): unknown;
    create(base?: DeepPartial<TokenMeta>): TokenMeta;
    fromPartial(object: DeepPartial<TokenMeta>): TokenMeta;
};
export declare const FundRequest: {
    encode(message: FundRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundRequest;
    fromJSON(object: any): FundRequest;
    toJSON(message: FundRequest): unknown;
    create(base?: DeepPartial<FundRequest>): FundRequest;
    fromPartial(object: DeepPartial<FundRequest>): FundRequest;
};
export declare const FundResponse: {
    encode(message: FundResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FundResponse;
    fromJSON(object: any): FundResponse;
    toJSON(message: FundResponse): unknown;
    create(base?: DeepPartial<FundResponse>): FundResponse;
    fromPartial(object: DeepPartial<FundResponse>): FundResponse;
};
export declare const RedemptionsRequest: {
    encode(message: RedemptionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RedemptionsRequest;
    fromJSON(object: any): RedemptionsRequest;
    toJSON(message: RedemptionsRequest): unknown;
    create(base?: DeepPartial<RedemptionsRequest>): RedemptionsRequest;
    fromPartial(object: DeepPartial<RedemptionsRequest>): RedemptionsRequest;
};
export declare const RedemptionsResponse: {
    encode(message: RedemptionsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RedemptionsResponse;
    fromJSON(object: any): RedemptionsResponse;
    toJSON(message: RedemptionsResponse): unknown;
    create(base?: DeepPartial<RedemptionsResponse>): RedemptionsResponse;
    fromPartial(object: DeepPartial<RedemptionsResponse>): RedemptionsResponse;
};
export declare const RedemptionSchedule: {
    encode(message: RedemptionSchedule, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RedemptionSchedule;
    fromJSON(object: any): RedemptionSchedule;
    toJSON(message: RedemptionSchedule): unknown;
    create(base?: DeepPartial<RedemptionSchedule>): RedemptionSchedule;
    fromPartial(object: DeepPartial<RedemptionSchedule>): RedemptionSchedule;
};
/** InjectiveInsuranceRPC defines gRPC API of Insurance provider. */
export interface InjectiveInsuranceRPC {
    /** Funds lists all insurance funds. */
    Funds(request: DeepPartial<FundsRequest>, metadata?: grpc.Metadata): Promise<FundsResponse>;
    /** Funds returns an insurance fund for a given insurance fund token denom. */
    Fund(request: DeepPartial<FundRequest>, metadata?: grpc.Metadata): Promise<FundResponse>;
    /** PendingRedemptions lists all pending redemptions according to a filter */
    Redemptions(request: DeepPartial<RedemptionsRequest>, metadata?: grpc.Metadata): Promise<RedemptionsResponse>;
}
export declare class InjectiveInsuranceRPCClientImpl implements InjectiveInsuranceRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    Funds(request: DeepPartial<FundsRequest>, metadata?: grpc.Metadata): Promise<FundsResponse>;
    Fund(request: DeepPartial<FundRequest>, metadata?: grpc.Metadata): Promise<FundResponse>;
    Redemptions(request: DeepPartial<RedemptionsRequest>, metadata?: grpc.Metadata): Promise<RedemptionsResponse>;
}
export declare const InjectiveInsuranceRPCDesc: {
    serviceName: string;
};
export declare const InjectiveInsuranceRPCFundsDesc: UnaryMethodDefinitionish;
export declare const InjectiveInsuranceRPCFundDesc: UnaryMethodDefinitionish;
export declare const InjectiveInsuranceRPCRedemptionsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
