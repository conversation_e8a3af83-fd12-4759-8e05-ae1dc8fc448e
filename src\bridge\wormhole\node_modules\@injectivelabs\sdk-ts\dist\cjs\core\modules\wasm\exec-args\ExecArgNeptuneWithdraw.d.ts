import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
export declare namespace ExecArgNeptuneWithdraw {
    interface Params {
        amount: string;
        contract: string;
    }
    interface Data {
        amount: string;
        contract: string;
        msg: string;
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecArgNeptuneWithdraw extends ExecArgBase<ExecArgNeptuneWithdraw.Params, ExecArgNeptuneWithdraw.Data> {
    static fromJSON(params: ExecArgNeptuneWithdraw.Params): ExecArgNeptuneWithdraw;
    toData(): ExecArgNeptuneWithdraw.Data;
    toExecData(): ExecDataRepresentation<ExecArgNeptuneWithdraw.Data>;
}
