"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { newObj[key] = obj[key]; } } } newObj.default = obj; return newObj; } } function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; } function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } } function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; } function _optionalChainDelete(ops) { const result = _optionalChain(ops); return result == null ? true : result; }var _chunkZMDE3DNLjs = require('./chunk-ZMDE3DNL.js');var U=class n{constructor(e){this.buffer=new ArrayBuffer(e.length),new Uint8Array(this.buffer).set(e,0),this.offset=0}static fromHex(e){let t=_chunkZMDE3DNLjs.j.hexInputToUint8Array(e);return new n(t)}read(e){if(this.offset+e>this.buffer.byteLength)throw new Error("Reached to the end of buffer");let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}remaining(){return this.buffer.byteLength-this.offset}assertFinished(){if(this.remaining()!==0)throw new Error("Buffer has remaining bytes")}deserializeStr(){let e=this.deserializeBytes();return new TextDecoder().decode(e)}deserializeOptionStr(){return this.deserializeOption("string")}deserializeOption(e,t){if(this.deserializeBool()){if(e==="string")return this.deserializeStr();if(e==="bytes")return this.deserializeBytes();if(e==="fixedBytes"){if(t===void 0)throw new Error("Fixed bytes length not provided");return this.deserializeFixedBytes(t)}return this.deserialize(e)}}deserializeBytes(){let e=this.deserializeUleb128AsU32();return new Uint8Array(this.read(e))}deserializeFixedBytes(e){return new Uint8Array(this.read(e))}deserializeBool(){let e=new Uint8Array(this.read(1))[0];if(e!==1&&e!==0)throw new Error("Invalid boolean value");return e===1}deserializeU8(){return new DataView(this.read(1)).getUint8(0)}deserializeU16(){return new DataView(this.read(2)).getUint16(0,!0)}deserializeU32(){return new DataView(this.read(4)).getUint32(0,!0)}deserializeU64(){let e=this.deserializeU32(),t=this.deserializeU32();return BigInt(BigInt(t)<<BigInt(32)|BigInt(e))}deserializeU128(){let e=this.deserializeU64(),t=this.deserializeU64();return BigInt(t<<BigInt(64)|e)}deserializeU256(){let e=this.deserializeU128(),t=this.deserializeU128();return BigInt(t<<BigInt(128)|e)}deserializeUleb128AsU32(){let e=BigInt(0),t=0;for(;e<_chunkZMDE3DNLjs.d;){let r=this.deserializeU8();if(e|=BigInt(r&127)<<BigInt(t),(r&128)===0)break;t+=7}if(e>_chunkZMDE3DNLjs.d)throw new Error("Overflow while parsing uleb128-encoded uint32 value");return Number(e)}deserialize(e){return e.deserialize(this)}deserializeVector(e){let t=this.deserializeUleb128AsU32(),r=new Array;for(let i=0;i<t;i+=1)r.push(this.deserialize(e));return r}};var _e=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.value=_chunkZMDE3DNLjs.j.fromHexInput(e).toUint8Array()}serialize(e){e.serializeFixedBytes(this.value)}serializeForEntryFunction(e){e.serialize(this)}serializeForScriptFunction(e){e.serialize(this)}static deserialize(e,t){let r=e.deserializeFixedBytes(t);return new n(r)}};var Yn=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.value=new _e(e)}serialize(e){e.serialize(this.value)}serializeForEntryFunction(e){e.serializeU32AsUleb128(this.value.value.length),e.serialize(this)}static deserialize(e,t){let r=_e.deserialize(e,t);return new n(r.value)}};var z=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.n.call(void 0, e),this.value=e}serialize(e){e.serializeBool(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(5),e.serialize(this)}deserialize(e){return new fe(e.deserializeU256())}static deserialize(e){return new n(e.deserializeBool())}},Y= exports.U8 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,0,_chunkZMDE3DNLjs.b),this.value=e}serialize(e){e.serializeU8(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(0),e.serialize(this)}static deserialize(e){return new n(e.deserializeU8())}},xe= exports.U16 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,0,_chunkZMDE3DNLjs.c),this.value=e}serialize(e){e.serializeU16(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(6),e.serialize(this)}static deserialize(e){return new n(e.deserializeU16())}},Ie= exports.U32 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,0,_chunkZMDE3DNLjs.d),this.value=e}serialize(e){e.serializeU32(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(7),e.serialize(this)}static deserialize(e){return new n(e.deserializeU32())}},j= exports.U64 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,BigInt(0),_chunkZMDE3DNLjs.e),this.value=BigInt(e)}serialize(e){e.serializeU64(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(1),e.serialize(this)}static deserialize(e){return new n(e.deserializeU64())}},ve= exports.U128 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,BigInt(0),_chunkZMDE3DNLjs.f),this.value=BigInt(e)}serialize(e){e.serializeU128(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(2),e.serialize(this)}static deserialize(e){return new n(e.deserializeU128())}},fe= exports.U256 =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),_chunkZMDE3DNLjs.p.call(void 0, e,BigInt(0),_chunkZMDE3DNLjs.g),this.value=BigInt(e)}serialize(e){e.serializeU256(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){e.serializeU32AsUleb128(8),e.serialize(this)}static deserialize(e){return new n(e.deserializeU256())}};var S=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.values=e}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){if(this.values[0]!==void 0&&!(this.values[0]instanceof Y)){new gn(this.bcsToBytes()).serializeForScriptFunction(e);return}e.serializeU32AsUleb128(4),e.serialize(this)}static U8(e){let t;if(Array.isArray(e)&&e.length===0)t=[];else if(Array.isArray(e)&&typeof e[0]=="number")t=e;else if(typeof e=="string"){let r=_chunkZMDE3DNLjs.j.fromHexInput(e);t=Array.from(r.toUint8Array())}else if(e instanceof Uint8Array)t=Array.from(e);else throw new Error("Invalid input type, must be an number[], Uint8Array, or hex string");return new n(t.map(r=>new Y(r)))}static U16(e){return new n(e.map(t=>new xe(t)))}static U32(e){return new n(e.map(t=>new Ie(t)))}static U64(e){return new n(e.map(t=>new j(t)))}static U128(e){return new n(e.map(t=>new ve(t)))}static U256(e){return new n(e.map(t=>new fe(t)))}static Bool(e){return new n(e.map(t=>new z(t)))}static MoveString(e){return new n(e.map(t=>new x(t)))}serialize(e){e.serializeVector(this.values)}static deserialize(e,t){let r=e.deserializeUleb128AsU32(),i=new Array;for(let o=0;o<r;o+=1)i.push(t.deserialize(e));return new n(i)}},gn= exports.Serialized =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.value=_chunkZMDE3DNLjs.j.fromHexInput(e).toUint8Array()}serialize(e){e.serializeBytes(this.value)}serializeForEntryFunction(e){this.serialize(e)}serializeForScriptFunction(e){e.serializeU32AsUleb128(9),this.serialize(e)}static deserialize(e){return new n(e.deserializeBytes())}toMoveVector(e){let t=new U(this.bcsToBytes());t.deserializeUleb128AsU32();let r=t.deserializeVector(e);return new S(r)}},x= exports.MoveString =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.value=e}serialize(e){e.serializeStr(this.value)}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}serializeForScriptFunction(e){let r=new TextEncoder().encode(this.value);S.U8(r).serializeForScriptFunction(e)}static deserialize(e){return new n(e.deserializeStr())}},Z= exports.MoveOption =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),typeof e<"u"&&e!==null?this.vec=new S([e]):this.vec=new S([]),[this.value]=this.vec.values}serializeForEntryFunction(e){let t=this.bcsToBytes();e.serializeBytes(t)}unwrap(){if(this.isSome())return this.vec.values[0];throw new Error("Called unwrap on a MoveOption with no value")}isSome(){return this.vec.values.length===1}serialize(e){this.vec.serialize(e)}static U8(e){return new n(e!=null?new Y(e):void 0)}static U16(e){return new n(e!=null?new xe(e):void 0)}static U32(e){return new n(e!=null?new Ie(e):void 0)}static U64(e){return new n(e!=null?new j(e):void 0)}static U128(e){return new n(e!=null?new ve(e):void 0)}static U256(e){return new n(e!=null?new fe(e):void 0)}static Bool(e){return new n(e!=null?new z(e):void 0)}static MoveString(e){return new n(e!=null?new x(e):void 0)}static deserialize(e,t){let r=S.deserialize(e,t);return new n(r.values[0])}};var _sha3 = require('@noble/hashes/sha3');var pt=class pt extends _chunkZMDE3DNLjs.l{constructor(e){super();let{data:t}=e,r=_chunkZMDE3DNLjs.j.fromHexInput(t);if(r.toUint8Array().length!==pt.LENGTH)throw new Error(`Authentication Key length should be ${pt.LENGTH}`);this.data=r}serialize(e){e.serializeFixedBytes(this.data.toUint8Array())}static deserialize(e){let t=e.deserializeFixedBytes(pt.LENGTH);return new pt({data:t})}toUint8Array(){return this.data.toUint8Array()}static fromSchemeAndBytes(e){let{scheme:t,input:r}=e,i=_chunkZMDE3DNLjs.j.fromHexInput(r).toUint8Array(),o=new Uint8Array([...i,t]),s=_sha3.sha3_256.create();s.update(o);let a=s.digest();return new pt({data:a})}static fromPublicKeyAndScheme(e){let{publicKey:t}=e;return t.authKey()}static fromPublicKey(e){let{publicKey:t}=e;return t.authKey()}derivedAddress(){return new (0, _chunkZMDE3DNLjs.Y)(this.data.toUint8Array())}};pt.LENGTH=32;var M=pt;var Pt=class extends _chunkZMDE3DNLjs.l{async verifySignatureAsync(e){return this.verifySignature(e)}toUint8Array(){return this.bcsToBytes()}toString(){let e=this.toUint8Array();return _chunkZMDE3DNLjs.j.fromHexInput(e).toString()}},re= exports.AccountPublicKey =class extends Pt{};var V=class extends _chunkZMDE3DNLjs.l{toUint8Array(){return this.bcsToBytes()}toString(){let e=this.toUint8Array();return _chunkZMDE3DNLjs.j.fromHexInput(e).toString()}};var mn=class n extends V{constructor(e){super(),this.value=_chunkZMDE3DNLjs.j.fromHexInput(e).toUint8Array()}serialize(e){e.serializeBytes(this.value)}static deserialize(e){return new n(e.deserializeBytes())}},Zn= exports.AbstractPublicKey =class extends re{constructor(e){super(),this.accountAddress=e}authKey(){return new M({data:this.accountAddress.toUint8Array()})}verifySignature(e){throw new Error("This function is not implemented for AbstractPublicKey.")}async verifySignatureAsync(e){throw new Error("This function is not implemented for AbstractPublicKey.")}serialize(e){throw new Error("This function is not implemented for AbstractPublicKey.")}};var _ed25519 = require('@noble/curves/ed25519');var _hmac = require('@noble/hashes/hmac');var _sha512 = require('@noble/hashes/sha512');var _bip39 = require('@scure/bip39'); var co = _interopRequireWildcard(_bip39);var lc=/^m\/44'\/637'\/[0-9]+'\/[0-9]+'\/[0-9]+'?$/,gc= exports.APTOS_BIP44_REGEX =/^m\/44'\/637'\/[0-9]+'\/[0-9]+\/[0-9]+$/,mc= exports.KeyType =(e=>(e.ED25519="ed25519 seed",e))(mc||{}),uo= exports.HARDENED_OFFSET =**********;function po(n){return gc.test(n)}function lo(n){return lc.test(n)}var Xr=(n,e)=>{let t=_hmac.hmac.create(_sha512.sha512,n).update(e).digest();return{key:t.slice(0,32),chainCode:t.slice(32)}},go= exports.CKDPriv =({key:n,chainCode:e},t)=>{let r=new ArrayBuffer(4);new DataView(r).setUint32(0,t);let i=new Uint8Array(r),o=new Uint8Array([0]),s=new Uint8Array([...o,...n,...i]);return Xr(e,s)},yc=n=>n.replace(/'/g,""),mo= exports.splitPath =n=>n.split("/").slice(1).map(yc),er= exports.mnemonicToSeed =n=>{let e=n.trim().split(/\s+/).map(t=>t.toLowerCase()).join(" ");return co.mnemonicToSeedSync(e)};var yn=class yn{static formatPrivateKey(e,t){let r=yn.AIP80_PREFIXES[t],i=e;return typeof i=="string"&&i.startsWith(r)&&(i=i.split("-")[2]),`${r}${_chunkZMDE3DNLjs.j.fromHexInput(i).toString()}`}static parseHexInput(e,t,r){let i,o=yn.AIP80_PREFIXES[t];if(typeof e=="string")if(!r&&!e.startsWith(o))i=_chunkZMDE3DNLjs.j.fromHexInput(e),r!==!1&&console.warn("[Aptos SDK] It is recommended that private keys are AIP-80 compliant (https://github.com/aptos-foundation/AIPs/blob/main/aips/aip-80.md). You can fix the private key by formatting it with `PrivateKey.formatPrivateKey(privateKey: string, type: 'ed25519' | 'secp256k1'): string`.");else if(e.startsWith(o))i=_chunkZMDE3DNLjs.j.fromHexString(e.split("-")[2]);else throw r?new Error("Invalid HexString input while parsing private key. Must AIP-80 compliant string."):new Error("Invalid HexString input while parsing private key.");else i=_chunkZMDE3DNLjs.j.fromHexInput(e);return i}};yn.AIP80_PREFIXES={ed25519:"ed25519-priv-",secp256k1:"secp256k1-priv-"};var dt=yn;var qt=n=>typeof n=="string"?_chunkZMDE3DNLjs.j.isValid(n).valid?n:new TextEncoder().encode(n):n;var Yr=[237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16];function fc(n){let e=n.toUint8Array().slice(32);for(let t=Yr.length-1;t>=0;t-=1){if(e[t]<Yr[t])return!0;if(e[t]>Yr[t])return!1}return!1}var lt=class lt extends re{constructor(e){super();let t=_chunkZMDE3DNLjs.j.fromHexInput(e);if(t.toUint8Array().length!==lt.LENGTH)throw new Error(`PublicKey length should be ${lt.LENGTH}`);this.key=t}verifySignature(e){let{message:t,signature:r}=e;if(!fc(r))return!1;let i=qt(t),o=_chunkZMDE3DNLjs.j.fromHexInput(i).toUint8Array(),s=r.toUint8Array(),a=this.key.toUint8Array();return _ed25519.ed25519.verify(s,o,a)}async verifySignatureAsync(e){return this.verifySignature(e)}authKey(){return M.fromSchemeAndBytes({scheme:0,input:this.toUint8Array()})}toUint8Array(){return this.key.toUint8Array()}serialize(e){e.serializeBytes(this.key.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new lt(t)}static isPublicKey(e){return e instanceof lt}static isInstance(e){return"key"in e&&_optionalChain([e, 'access', _2 => _2.key, 'optionalAccess', _3 => _3.data, 'optionalAccess', _4 => _4.length])===lt.LENGTH}};lt.LENGTH=32;var I=lt,Ae=class Ae extends _chunkZMDE3DNLjs.l{constructor(e,t){super();let r=dt.parseHexInput(e,"ed25519",t);if(r.toUint8Array().length!==Ae.LENGTH)throw new Error(`PrivateKey length should be ${Ae.LENGTH}`);this.signingKey=r}static generate(){let e=_ed25519.ed25519.utils.randomPrivateKey();return new Ae(e,!1)}static fromDerivationPath(e,t){if(!lo(e))throw new Error(`Invalid derivation path ${e}`);return Ae.fromDerivationPathInner(e,er(t))}static fromDerivationPathInner(e,t,r=uo){let{key:i,chainCode:o}=Xr(Ae.SLIP_0010_SEED,t),s=mo(e).map(c=>parseInt(c,10)),{key:a}=s.reduce((c,p)=>go(c,p+r),{key:i,chainCode:o});return new Ae(a,!1)}publicKey(){let e=_ed25519.ed25519.getPublicKey(this.signingKey.toUint8Array());return new I(e)}sign(e){let t=qt(e),r=_chunkZMDE3DNLjs.j.fromHexInput(t).toUint8Array(),i=_ed25519.ed25519.sign(r,this.signingKey.toUint8Array());return new v(i)}toUint8Array(){return this.signingKey.toUint8Array()}toString(){return this.toAIP80String()}toHexString(){return this.signingKey.toString()}toAIP80String(){return dt.formatPrivateKey(this.signingKey.toString(),"ed25519")}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new Ae(t,!1)}static isPrivateKey(e){return e instanceof Ae}};Ae.LENGTH=32,Ae.SLIP_0010_SEED="ed25519 seed";var q=Ae,$t=class $t extends V{constructor(e){super();let t=_chunkZMDE3DNLjs.j.fromHexInput(e);if(t.toUint8Array().length!==$t.LENGTH)throw new Error(`Signature length should be ${$t.LENGTH}`);this.data=t}toUint8Array(){return this.data.toUint8Array()}serialize(e){e.serializeBytes(this.data.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new $t(t)}};$t.LENGTH=64;var v=$t;var gt=class n extends Pt{constructor(e){super();let t=e.constructor.name;switch(t){case I.name:this.publicKey=e,this.variant=0;break;default:throw new Error(`Unsupported key for EphemeralPublicKey - ${t}`)}}verifySignature(e){let{message:t,signature:r}=e;return this.publicKey.verifySignature({message:t,signature:r.signature})}async verifySignatureAsync(e){return this.verifySignature(e)}serialize(e){if(this.publicKey instanceof I)e.serializeU32AsUleb128(0),this.publicKey.serialize(e);else throw new Error("Unknown public key type")}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(I.deserialize(e));default:throw new Error(`Unknown variant index for EphemeralPublicKey: ${t}`)}}static isPublicKey(e){return e instanceof n}},Ne= exports.EphemeralSignature =class n extends V{constructor(e){super();let t=e.constructor.name;switch(t){case v.name:this.signature=e;break;default:throw new Error(`Unsupported signature for EphemeralSignature - ${t}`)}}static fromHex(e){let t=_chunkZMDE3DNLjs.j.fromHexInput(e),r=new U(t.toUint8Array());return n.deserialize(r)}serialize(e){if(this.signature instanceof v)e.serializeU32AsUleb128(0),this.signature.serialize(e);else throw new Error("Unknown signature type")}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(v.deserialize(e));default:throw new Error(`Unknown variant index for EphemeralSignature: ${t}`)}}};var _jwtdecode = require('jwt-decode');var _poseidonlite = require('poseidon-lite');var Zr=[_poseidonlite.poseidon1,_poseidonlite.poseidon2,_poseidonlite.poseidon3,_poseidonlite.poseidon4,_poseidonlite.poseidon5,_poseidonlite.poseidon6,_poseidonlite.poseidon7,_poseidonlite.poseidon8,_poseidonlite.poseidon9,_poseidonlite.poseidon10,_poseidonlite.poseidon11,_poseidonlite.poseidon12,_poseidonlite.poseidon13,_poseidonlite.poseidon14,_poseidonlite.poseidon15,_poseidonlite.poseidon16],fo=31,kc=16,yo=(kc-1)*fo;function Oe(n,e){let r=new TextEncoder().encode(n);return Dc(r,e)}function Dc(n,e){if(n.length>e)throw new Error(`Inputted bytes of length ${n} is longer than ${e}`);let t=fn(n,e);return xt(t)}function zc(n,e){if(n.length>e)throw new Error(`Input bytes of length ${n} is longer than ${e}`);let t=Fc(n,e);return Nc(t)}function fn(n,e){if(n.length>e)throw new Error(`Input bytes of length ${n} is longer than ${e}`);return zc(n,e).concat([BigInt(n.length)])}function Nc(n){if(n.length>yo)throw new Error(`Can't pack more than ${yo}.  Was given ${n.length} bytes`);return Oc(n,fo).map(e=>_t(e))}function Oc(n,e){let t=[];for(let r=0;r<n.length;r+=e)t.push(n.subarray(r,r+e));return t}function _t(n){let e=BigInt(0);for(let t=n.length-1;t>=0;t-=1)e=e<<BigInt(8)|BigInt(n[t]);return e}function ei(n,e){let t=BigInt(n),r=new Uint8Array(e);for(let i=0;i<e;i+=1)r[i]=Number(t&BigInt(255)),t>>=BigInt(8);return r}function Fc(n,e){if(e<n.length)throw new Error("Padded size must be greater than or equal to the input array size.");let t=new Uint8Array(e);t.set(n);for(let r=n.length;r<e;r+=1)t[r]=0;return t}function xt(n){if(n.length>Zr.length)throw new Error(`Unable to hash input of length ${n.length}.  Max input length is ${Zr.length}`);return Zr[n.length-1](n)}var nr=class extends _chunkZMDE3DNLjs.l{};var Ao="2.0.1";var ho={mainnet:"https://api.mainnet.aptoslabs.com/v1/graphql",testnet:"https://api.testnet.aptoslabs.com/v1/graphql",devnet:"https://api.devnet.aptoslabs.com/v1/graphql",local:"http://127.0.0.1:8090/v1/graphql"},To= exports.NetworkToNodeAPI ={mainnet:"https://api.mainnet.aptoslabs.com/v1",testnet:"https://api.testnet.aptoslabs.com/v1",devnet:"https://api.devnet.aptoslabs.com/v1",local:"http://127.0.0.1:8080/v1"},bo= exports.NetworkToFaucetAPI ={devnet:"https://faucet.devnet.aptoslabs.com",local:"http://127.0.0.1:8081"},ti= exports.NetworkToPepperAPI ={mainnet:"https://api.mainnet.aptoslabs.com/keyless/pepper/v0",testnet:"https://api.testnet.aptoslabs.com/keyless/pepper/v0",devnet:"https://api.devnet.aptoslabs.com/keyless/pepper/v0",local:"https://api.devnet.aptoslabs.com/keyless/pepper/v0"},ni= exports.NetworkToProverAPI ={mainnet:"https://api.mainnet.aptoslabs.com/keyless/prover/v0",testnet:"https://api.testnet.aptoslabs.com/keyless/prover/v0",devnet:"https://api.devnet.aptoslabs.com/keyless/prover/v0",local:"https://api.devnet.aptoslabs.com/keyless/prover/v0"},ri= exports.Network =(o=>(o.MAINNET="mainnet",o.TESTNET="testnet",o.DEVNET="devnet",o.LOCAL="local",o.CUSTOM="custom",o))(ri||{}),ii= exports.NetworkToChainId ={mainnet:1,testnet:2,local:4},_l= exports.NetworkToNetworkName ={mainnet:"mainnet",testnet:"testnet",devnet:"devnet",local:"local",custom:"custom"};var Wt=(o=>(o.FULLNODE="Fullnode",o.INDEXER="Indexer",o.FAUCET="Faucet",o.PEPPER="Pepper",o.PROVER="Prover",o))(Wt||{}),wo= exports.DEFAULT_MAX_GAS_AMOUNT =2e5,So= exports.DEFAULT_TXN_EXP_SEC_FROM_NOW =20,rr= exports.DEFAULT_TXN_TIMEOUT_SEC =20,It= exports.APTOS_COIN ="0x1::aptos_coin::AptosCoin",Eo= exports.APTOS_FA ="0x000000000000000000000000000000000000000000000000000000000000000a",Po= exports.RAW_TRANSACTION_SALT ="APTOS::RawTransaction",oi= exports.RAW_TRANSACTION_WITH_DATA_SALT ="APTOS::RawTransactionWithData",qe= exports.ProcessorType =(c=>(c.ACCOUNT_TRANSACTION_PROCESSOR="account_transactions_processor",c.DEFAULT="default_processor",c.EVENTS_PROCESSOR="events_processor",c.FUNGIBLE_ASSET_PROCESSOR="fungible_asset_processor",c.STAKE_PROCESSOR="stake_processor",c.TOKEN_V2_PROCESSOR="token_v2_processor",c.USER_TRANSACTION_PROCESSOR="user_transaction_processor",c.OBJECT_PROCESSOR="objects_processor",c))(qe||{}),_o= exports.FIREBASE_AUTH_ISS_PATTERN =/^https:\/\/securetoken\.google\.com\/[a-zA-Z0-9-_]+$/;function xo(n,e){let t=e.bcsToBytes(),r=new U(t);return n.deserialize(r)}var Gc=(s=>(s[s.API_ERROR=0]="API_ERROR",s[s.EXTERNAL_API_ERROR=1]="EXTERNAL_API_ERROR",s[s.SESSION_EXPIRED=2]="SESSION_EXPIRED",s[s.INVALID_STATE=3]="INVALID_STATE",s[s.INVALID_SIGNATURE=4]="INVALID_SIGNATURE",s[s.UNKNOWN=5]="UNKNOWN",s))(Gc||{}),Bc= exports.KeylessErrorResolutionTip =(p=>(p.REAUTHENTICATE="Re-authentiate to continue using your keyless account",p.REAUTHENTICATE_UNSURE="Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support",p.UPDATE_REQUEST_PARAMS="Update the invalid request parameters and reauthenticate.",p.RATE_LIMIT_EXCEEDED="Cache the keyless account and reuse it to avoid making too many requests.  Keyless accounts are valid until either the EphemeralKeyPair expires, when the JWK is rotated, or when the proof verifying key is changed, whichever comes soonest.",p.SERVER_ERROR="Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",p.CALL_PRECHECK="Call `await account.checkKeylessAccountValidity()` to wait for asyncronous changes and check for account validity before signing or serializing.",p.REINSTANTIATE="Try instantiating the account again.  Avoid manipulating the account object directly",p.JOIN_SUPPORT_GROUP="For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",p.UNKNOWN="Error unknown. For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx",p))(Bc||{}),ir= exports.KeylessErrorType =(m=>(m[m.EPHEMERAL_KEY_PAIR_EXPIRED=0]="EPHEMERAL_KEY_PAIR_EXPIRED",m[m.PROOF_NOT_FOUND=1]="PROOF_NOT_FOUND",m[m.ASYNC_PROOF_FETCH_FAILED=2]="ASYNC_PROOF_FETCH_FAILED",m[m.INVALID_PROOF_VERIFICATION_FAILED=3]="INVALID_PROOF_VERIFICATION_FAILED",m[m.INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND=4]="INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND",m[m.INVALID_JWT_SIG=5]="INVALID_JWT_SIG",m[m.INVALID_JWT_JWK_NOT_FOUND=6]="INVALID_JWT_JWK_NOT_FOUND",m[m.INVALID_JWT_ISS_NOT_RECOGNIZED=7]="INVALID_JWT_ISS_NOT_RECOGNIZED",m[m.INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED=8]="INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED",m[m.INVALID_TW_SIG_VERIFICATION_FAILED=9]="INVALID_TW_SIG_VERIFICATION_FAILED",m[m.INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND=10]="INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND",m[m.INVALID_EXPIRY_HORIZON=11]="INVALID_EXPIRY_HORIZON",m[m.JWT_PARSING_ERROR=12]="JWT_PARSING_ERROR",m[m.JWK_FETCH_FAILED=13]="JWK_FETCH_FAILED",m[m.JWK_FETCH_FAILED_FEDERATED=14]="JWK_FETCH_FAILED_FEDERATED",m[m.RATE_LIMIT_EXCEEDED=15]="RATE_LIMIT_EXCEEDED",m[m.PEPPER_SERVICE_INTERNAL_ERROR=16]="PEPPER_SERVICE_INTERNAL_ERROR",m[m.PEPPER_SERVICE_BAD_REQUEST=17]="PEPPER_SERVICE_BAD_REQUEST",m[m.PEPPER_SERVICE_OTHER=18]="PEPPER_SERVICE_OTHER",m[m.PROVER_SERVICE_INTERNAL_ERROR=19]="PROVER_SERVICE_INTERNAL_ERROR",m[m.PROVER_SERVICE_BAD_REQUEST=20]="PROVER_SERVICE_BAD_REQUEST",m[m.PROVER_SERVICE_OTHER=21]="PROVER_SERVICE_OTHER",m[m.FULL_NODE_CONFIG_LOOKUP_ERROR=22]="FULL_NODE_CONFIG_LOOKUP_ERROR",m[m.FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR=23]="FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR",m[m.FULL_NODE_JWKS_LOOKUP_ERROR=24]="FULL_NODE_JWKS_LOOKUP_ERROR",m[m.FULL_NODE_OTHER=25]="FULL_NODE_OTHER",m[m.SIGNATURE_TYPE_INVALID=26]="SIGNATURE_TYPE_INVALID",m[m.SIGNATURE_EXPIRED=27]="SIGNATURE_EXPIRED",m[m.MAX_EXPIRY_HORIZON_EXCEEDED=28]="MAX_EXPIRY_HORIZON_EXCEEDED",m[m.EPHEMERAL_SIGNATURE_VERIFICATION_FAILED=29]="EPHEMERAL_SIGNATURE_VERIFICATION_FAILED",m[m.TRAINING_WHEELS_SIGNATURE_MISSING=30]="TRAINING_WHEELS_SIGNATURE_MISSING",m[m.TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED=31]="TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED",m[m.PROOF_VERIFICATION_FAILED=32]="PROOF_VERIFICATION_FAILED",m[m.UNKNOWN=33]="UNKNOWN",m))(ir||{}),Io={0:["The ephemeral keypair has expired.",2,"Re-authentiate to continue using your keyless account"],1:["The required proof could not be found.",3,"Call `await account.checkKeylessAccountValidity()` to wait for asyncronous changes and check for account validity before signing or serializing."],2:["The required proof failed to fetch.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],3:["The provided proof is invalid.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],4:["The verification key used to authenticate was updated.",2,"Re-authentiate to continue using your keyless account"],5:["The JWK was found, but JWT failed verification",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],6:["The JWK required to verify the JWT could not be found. The JWK may have been rotated out.",2,"Re-authentiate to continue using your keyless account"],7:["The JWT issuer is not recognized.",3,"Update the invalid request parameters and reauthenticate."],8:["The JWT issuer is not supported by the Federated Keyless ",0,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],9:["The training wheels signature is invalid.",3,"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support"],10:["The public key used to verify the training wheels signature was not found.",2,"Re-authentiate to continue using your keyless account"],11:["The expiry horizon is invalid.",2,"Re-authentiate to continue using your keyless account"],13:["Failed to fetch JWKS.",1,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],14:["Failed to fetch JWKS for Federated Keyless provider.",1,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],15:["Rate limit exceeded. Too many requests in a short period.",0,"Cache the keyless account and reuse it to avoid making too many requests.  Keyless accounts are valid until either the EphemeralKeyPair expires, when the JWK is rotated, or when the proof verifying key is changed, whichever comes soonest."],16:["Internal error from Pepper service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],17:["Bad request sent to Pepper service.",0,"Update the invalid request parameters and reauthenticate."],18:["Unknown error from Pepper service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],19:["Internal error from Prover service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],20:["Bad request sent to Prover service.",0,"Update the invalid request parameters and reauthenticate."],21:["Unknown error from Prover service.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],12:["Error when parsing JWT. This should never happen. Join https://t.me/+h5CN-W35yUFiYzkx for support",3,"Try instantiating the account again.  Avoid manipulating the account object directly"],22:["Error when looking up on-chain keyless configuration.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],23:["Error when looking up on-chain verification key.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],24:["Error when looking up on-chain JWKS.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],25:["Unknown error from full node.",0,"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],26:["The signature is not a valid Keyless signature.",4,"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"],27:["The ephemeral key pair used to sign the message has expired.",4,"Re-authentiate to continue using your keyless account"],28:["The expiry horizon on the signature exceeds the maximum allowed value.",4,"Re-authentiate to continue using your keyless account"],29:["Failed to verify the ephemeral signature with the ephemeral public key.",4,"Re-authentiate to continue using your keyless account"],30:["The training wheels signature is missing but is required by the Keyless configuration.",4,"Re-authentiate to continue using your keyless account"],31:["Failed to verify the training wheels signature with the training wheels public key.",4,"Re-authentiate to continue using your keyless account"],32:["The proof verification failed.",4,"Re-authentiate to continue using your keyless account"],33:["An unknown error has occurred.",5,"Error unknown. For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx"]},P= exports.KeylessError =class n extends Error{constructor(e){let{innerError:t,category:r,resolutionTip:i,type:o,message:s=Io[o][0],details:a}=e;super(s),this.name="KeylessError",this.innerError=t,this.category=r,this.resolutionTip=i,this.type=o,this.details=a,this.message=n.constructMessage(s,i,t,a)}static constructMessage(e,t,r,i){let o=`
Message: ${e}`;return i&&(o+=`
Details: ${i}`),r instanceof he?o+=`
AptosApiError: ${r.message}`:r!==void 0&&(o+=`
Error: ${_chunkZMDE3DNLjs.ba.call(void 0, r)}`),o+=`
KeylessErrorResolutionTip: ${t}`,o}static fromErrorType(e){let{error:t,type:r,details:i}=e,[o,s,a]=Io[r];return new n({message:o,details:i,innerError:t,category:s,resolutionTip:a,type:r})}},he= exports.AptosApiError =class extends Error{constructor({apiType:e,aptosRequest:t,aptosResponse:r}){super(Mc({apiType:e,aptosRequest:t,aptosResponse:r})),this.name="AptosApiError",this.url=r.url,this.status=r.status,this.statusText=r.statusText,this.data=r.data,this.request=t}};function Mc({apiType:n,aptosRequest:e,aptosResponse:t}){let r=_optionalChain([t, 'access', _5 => _5.headers, 'optionalAccess', _6 => _6.traceparent, 'optionalAccess', _7 => _7.split, 'call', _8 => _8("-"), 'access', _9 => _9[1]]),i=r?`(trace_id:${r}) `:"",o=`Request to [${n}]: ${e.method} ${_nullishCoalesce(t.url, () => (e.url))} ${i}failed with`;return n==="Indexer"&&_optionalChain([t, 'access', _10 => _10.data, 'optionalAccess', _11 => _11.errors, 'optionalAccess', _12 => _12[0], 'optionalAccess', _13 => _13.message])!=null?`${o}: ${t.data.errors[0].message}`:_optionalChain([t, 'access', _14 => _14.data, 'optionalAccess', _15 => _15.message])!=null&&_optionalChain([t, 'access', _16 => _16.data, 'optionalAccess', _17 => _17.error_code])!=null?`${o}: ${JSON.stringify(t.data)}`:`${o} status: ${t.statusText}(code:${t.status}) and response body: ${Vc(t.data)}`}var si=400;function Vc(n){let e=JSON.stringify(n);return e.length<=si?e:`truncated(original_size:${e.length}): ${e.slice(0,si/2)}...${e.slice(-si/2)}`}async function Hc(n,e){let{url:t,method:r,body:i,contentType:o,params:s,overrides:a,originMethod:c}=n,p={..._optionalChain([a, 'optionalAccess', _18 => _18.HEADERS]),"x-aptos-client":`aptos-typescript-sdk/${Ao}`,"content-type":_nullishCoalesce(o, () => ("application/json")),"x-aptos-typescript-sdk-origin-method":c};return _optionalChain([a, 'optionalAccess', _19 => _19.AUTH_TOKEN])&&(p.Authorization=`Bearer ${_optionalChain([a, 'optionalAccess', _20 => _20.AUTH_TOKEN])}`),_optionalChain([a, 'optionalAccess', _21 => _21.API_KEY])&&(p.Authorization=`Bearer ${_optionalChain([a, 'optionalAccess', _22 => _22.API_KEY])}`),e.provider({url:t,method:r,body:i,params:s,headers:p,overrides:a})}async function or(n,e,t){let{url:r,path:i}=n,o=i?`${r}/${i}`:r,s=await Hc({...n,url:o},e.client),a={status:s.status,statusText:_nullishCoalesce(s.statusText, () => ("No status text provided")),data:s.data,headers:s.headers,config:s.config,request:s.request,url:o};if(a.status===401)throw new he({apiType:t,aptosRequest:n,aptosResponse:a});if(t==="Indexer"){let c=a.data;if(c.errors)throw new he({apiType:t,aptosRequest:n,aptosResponse:a});a.data=c.data}else if((t==="Pepper"||t==="Prover")&&a.status>=400)throw new he({apiType:t,aptosRequest:n,aptosResponse:a});if(a.status>=200&&a.status<300)return a;throw new he({apiType:t,aptosRequest:n,aptosResponse:a})}async function sr(n){let{aptosConfig:e,overrides:t,params:r,contentType:i,acceptType:o,path:s,originMethod:a,type:c}=n,p=e.getRequestUrl(c);return or({url:p,method:"GET",originMethod:a,path:s,contentType:i,acceptType:o,params:r,overrides:{...e.clientConfig,...t}},e,n.type)}async function H(n){let{aptosConfig:e}=n;return sr({...n,type:"Fullnode",overrides:{...e.clientConfig,...e.fullnodeConfig,...n.overrides,HEADERS:{..._optionalChain([e, 'access', _23 => _23.clientConfig, 'optionalAccess', _24 => _24.HEADERS]),..._optionalChain([e, 'access', _25 => _25.fullnodeConfig, 'optionalAccess', _26 => _26.HEADERS])}}})}async function Yl(n){return sr({...n,type:"Pepper"})}async function ar(n){let e=new Array(0),t,r=n.params;do{let i=await sr({type:"Fullnode",aptosConfig:n.aptosConfig,originMethod:n.originMethod,path:n.path,params:r,overrides:n.overrides});t=i.headers["x-aptos-cursor"],delete i.headers,e.push(...i.data),r.start=t}while(t!=null);return e}async function ai(n){let e=new Array(0),t,r=n.params,i=r.limit;do{let{response:o,cursor:s}=await cr({...n});if(t=s,e.push(...o.data),_optionalChain([n, 'optionalAccess', _27 => _27.params])&&(n.params.start=t),i!==void 0){let a=i-e.length;if(a<=0)break;r.limit=a}}while(t!=null);return e}async function cr(n){let e,t={};typeof _optionalChain([n, 'access', _28 => _28.params, 'optionalAccess', _29 => _29.cursor])=="string"&&(t.start=n.params.cursor),typeof _optionalChain([n, 'access', _30 => _30.params, 'optionalAccess', _31 => _31.limit])=="number"&&(t.limit=n.params.limit);let r=await sr({type:"Fullnode",aptosConfig:n.aptosConfig,originMethod:n.originMethod,path:n.path,params:t,overrides:n.overrides});return e=r.headers["x-aptos-cursor"],{response:r,cursor:e}}async function An(n){let{type:e,originMethod:t,path:r,body:i,acceptType:o,contentType:s,params:a,aptosConfig:c,overrides:p}=n,y=c.getRequestUrl(e);return or({url:y,method:"POST",originMethod:t,path:r,body:i,contentType:s,acceptType:o,params:a,overrides:p},c,n.type)}async function mt(n){let{aptosConfig:e}=n;return An({...n,type:"Fullnode",overrides:{...e.clientConfig,...e.fullnodeConfig,...n.overrides,HEADERS:{..._optionalChain([e, 'access', _32 => _32.clientConfig, 'optionalAccess', _33 => _33.HEADERS]),..._optionalChain([e, 'access', _34 => _34.fullnodeConfig, 'optionalAccess', _35 => _35.HEADERS])}}})}async function vo(n){let{aptosConfig:e}=n;return An({...n,type:"Indexer",overrides:{...e.clientConfig,...e.indexerConfig,...n.overrides,HEADERS:{..._optionalChain([e, 'access', _36 => _36.clientConfig, 'optionalAccess', _37 => _37.HEADERS]),..._optionalChain([e, 'access', _38 => _38.indexerConfig, 'optionalAccess', _39 => _39.HEADERS])}}})}async function Co(n){let{aptosConfig:e}=n,t={...e,clientConfig:{...e.clientConfig}};return  _optionalChainDelete([t, 'optionalAccess', _40 => _40.clientConfig, 'optionalAccess', _41 => delete _41.API_KEY]),An({...n,type:"Faucet",overrides:{...t.clientConfig,...t.faucetConfig,...n.overrides,HEADERS:{..._optionalChain([t, 'access', _42 => _42.clientConfig, 'optionalAccess', _43 => _43.HEADERS]),..._optionalChain([t, 'access', _44 => _44.faucetConfig, 'optionalAccess', _45 => _45.HEADERS])}}})}async function Ko(n){return An({...n,type:"Pepper"})}async function Ro(n){return An({...n,type:"Prover"})}var ci=new Map;function Fe(n,e,t){return async(...r)=>{if(ci.has(e)){let{value:o,timestamp:s}=ci.get(e);if(t===void 0||Date.now()-s<=t)return o}let i=await n(...r);return ci.set(e,{value:i,timestamp:Date.now()}),i}}var _bn254 = require('@noble/curves/bn254');var _utils = require('@noble/curves/abstract/utils');var _jsbase64 = require('js-base64');var Wc=1e7,ui= exports.MAX_AUD_VAL_BYTES =120,Qc= exports.MAX_UID_KEY_BYTES =30,jc= exports.MAX_UID_VAL_BYTES =330,Jc= exports.MAX_ISS_VAL_BYTES =120,Xc= exports.MAX_EXTRA_FIELD_BYTES =350,Yc= exports.MAX_JWT_HEADER_B64_BYTES =300,Zc= exports.MAX_COMMITED_EPK_BYTES =93,Ge=class Ge extends re{constructor(e,t){super();let r=_chunkZMDE3DNLjs.j.fromHexInput(t).toUint8Array();if(r.length!==Ge.ID_COMMITMENT_LENGTH)throw new Error(`Id Commitment length in bytes should be ${Ge.ID_COMMITMENT_LENGTH}`);this.iss=e,this.idCommitment=r}authKey(){let e=new _chunkZMDE3DNLjs.m;return e.serializeU32AsUleb128(3),e.serializeFixedBytes(this.bcsToBytes()),M.fromSchemeAndBytes({scheme:2,input:e.toUint8Array()})}verifySignature(e){try{return ur({...e,publicKey:this}),!0}catch(t){if(t instanceof P)return!1;throw t}}async verifySignatureAsync(e){return yi({...e,publicKey:this})}serialize(e){e.serializeStr(this.iss),e.serializeBytes(this.idCommitment)}static deserialize(e){let t=e.deserializeStr(),r=e.deserializeBytes();return new Ge(t,r)}static load(e){let t=e.deserializeStr(),r=e.deserializeBytes();return new Ge(t,r)}static isPublicKey(e){return e instanceof Ge}static create(e){return Uo(e),new Ge(e.iss,Uo(e))}static fromJwtAndPepper(e){let{jwt:t,pepper:r,uidKey:i="sub"}=e,o=_jwtdecode.jwtDecode.call(void 0, t);if(typeof o.iss!="string")throw new Error("iss was not found");if(typeof o.aud!="string")throw new Error("aud was not found or an array of values");let s=o[i];return Ge.create({iss:o.iss,uidKey:i,uidVal:s,aud:o.aud,pepper:r})}static isInstance(e){return"iss"in e&&typeof e.iss=="string"&&"idCommitment"in e&&e.idCommitment instanceof Uint8Array}};Ge.ID_COMMITMENT_LENGTH=32;var K=Ge;async function yi(n){let{aptosConfig:e,publicKey:t,message:r,signature:i,jwk:o,keylessConfig:s=await Jt({aptosConfig:e}),options:a}=n;try{if(!(i instanceof ie))throw P.fromErrorType({type:26,details:"Not a keyless signature"});return ur({message:r,publicKey:t,signature:i,jwk:o||await fi({aptosConfig:e,publicKey:t,kid:i.getJwkKid()}),keylessConfig:s}),!0}catch(c){if(_optionalChain([a, 'optionalAccess', _46 => _46.throwErrorWithReason]))throw c;return!1}}function ur(n){let{publicKey:e,message:t,signature:r,keylessConfig:i,jwk:o}=n,{verificationKey:s,maxExpHorizonSecs:a,trainingWheelsPubkey:c}=i;if(!(r instanceof ie))throw P.fromErrorType({type:26,details:"Not a keyless signature"});if(!(r.ephemeralCertificate.signature instanceof ue))throw P.fromErrorType({type:26,details:"Unsupported ephemeral certificate variant"});let p=r.ephemeralCertificate.signature;if(!(p.proof.proof instanceof yt))throw P.fromErrorType({type:26,details:"Unsupported proof variant for ZeroKnowledgeSig"});let y=p.proof.proof;if(r.expiryDateSecs<_chunkZMDE3DNLjs.ca.call(void 0, ))throw P.fromErrorType({type:27,details:"The expiryDateSecs is in the past"});if(p.expHorizonSecs>a)throw P.fromErrorType({type:28});if(!r.ephemeralPublicKey.verifySignature({message:t,signature:r.ephemeralSignature}))throw P.fromErrorType({type:29});let h=eu({publicKey:e,signature:r,jwk:o,keylessConfig:i});if(!s.verifyProof({publicInputsHash:h,groth16Proof:y}))throw P.fromErrorType({type:32});if(c){if(!p.trainingWheelsSignature)throw P.fromErrorType({type:30});let d=new di(y,h);if(!c.verifySignature({message:d.hash(),signature:p.trainingWheelsSignature}))throw P.fromErrorType({type:31})}}function eu(n){let{publicKey:e,signature:t,jwk:r,keylessConfig:i}=n,o=e instanceof K?e:e.keylessPublicKey;if(!(t.ephemeralCertificate.signature instanceof ue))throw new Error("Signature is not a ZeroKnowledgeSig");let s=t.ephemeralCertificate.signature,a=[];return a.push(...fn(t.ephemeralPublicKey.toUint8Array(),i.maxCommitedEpkBytes)),a.push(_t(o.idCommitment)),a.push(t.expiryDateSecs),a.push(s.expHorizonSecs),a.push(Oe(o.iss,i.maxIssValBytes)),s.extraField?(a.push(1n),a.push(Oe(s.extraField,i.maxExtraFieldBytes))):(a.push(0n),a.push(Oe(" ",i.maxExtraFieldBytes))),a.push(Oe(_jsbase64.encode.call(void 0, t.jwtHeader,!0)+".",i.maxJwtHeaderB64Bytes)),a.push(r.toScalar()),s.overrideAudVal?(a.push(Oe(s.overrideAudVal,ui)),a.push(1n)):(a.push(Oe("",ui)),a.push(0n)),xt(a)}async function fi(n){let{aptosConfig:e,publicKey:t,kid:r}=n,i=t instanceof K?t:t.keylessPublicKey,{iss:o}=i,s,a=t instanceof $?t.jwkAddress:void 0;try{s=await ru({aptosConfig:e,jwkAddr:a})}catch(y){throw P.fromErrorType({type:24,error:y,details:`Failed to fetch ${a?"Federated":"Patched"}JWKs ${a?`for address ${a}`:"0x1"}`})}let c=s.get(o);if(c===void 0)throw P.fromErrorType({type:7,details:`JWKs for issuer ${o} not found.`});let p=c.find(y=>y.kid===r);if(p===void 0)throw P.fromErrorType({type:6,details:`JWK with kid '${r}' for issuer '${o}' not found.`});return p}function Uo(n){let{uidKey:e,uidVal:t,aud:r,pepper:i}=n,o=[_t(_chunkZMDE3DNLjs.j.fromHexInput(i).toUint8Array()),Oe(r,ui),Oe(t,jc),Oe(e,Qc)];return ei(xt(o),K.ID_COMMITMENT_LENGTH)}var ie=class n extends V{constructor(e){super();let{jwtHeader:t,ephemeralCertificate:r,expiryDateSecs:i,ephemeralPublicKey:o,ephemeralSignature:s}=e;this.jwtHeader=t,this.ephemeralCertificate=r,this.expiryDateSecs=i,this.ephemeralPublicKey=o,this.ephemeralSignature=s}getJwkKid(){return ou(this.jwtHeader).kid}serialize(e){this.ephemeralCertificate.serialize(e),e.serializeStr(this.jwtHeader),e.serializeU64(this.expiryDateSecs),this.ephemeralPublicKey.serialize(e),this.ephemeralSignature.serialize(e)}static deserialize(e){let t=Qt.deserialize(e),r=e.deserializeStr(),i=e.deserializeU64(),o=gt.deserialize(e),s=Ne.deserialize(e);return new n({jwtHeader:r,expiryDateSecs:Number(i),ephemeralCertificate:t,ephemeralPublicKey:o,ephemeralSignature:s})}static getSimulationSignature(){return new n({jwtHeader:"{}",ephemeralCertificate:new Qt(new ue({proof:new jt(new yt({a:new Uint8Array(32),b:new Uint8Array(64),c:new Uint8Array(32)}),0),expHorizonSecs:0}),0),expiryDateSecs:0,ephemeralPublicKey:new gt(new I(new Uint8Array(32))),ephemeralSignature:new Ne(new v(new Uint8Array(64)))})}static isSignature(e){return e instanceof n}},Qt= exports.EphemeralCertificate =class n extends V{constructor(e,t){super(),this.signature=e,this.variant=t}toUint8Array(){return this.signature.toUint8Array()}serialize(e){e.serializeU32AsUleb128(this.variant),this.signature.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(ue.deserialize(e),t);default:throw new Error(`Unknown variant index for EphemeralCertificate: ${t}`)}}},hn=class hn extends _chunkZMDE3DNLjs.l{constructor(e){if(super(),this.data=_chunkZMDE3DNLjs.j.fromHexInput(e).toUint8Array(),this.data.length!==32)throw new Error("Input needs to be 32 bytes")}serialize(e){e.serializeFixedBytes(this.data)}static deserialize(e){let t=e.deserializeFixedBytes(32);return new hn(t)}toArray(){let e=this.toProjectivePoint();return[e.x.toString(),e.y.toString(),e.pz.toString()]}toProjectivePoint(){let e=new Uint8Array(this.data);e.reverse();let t=(e[0]&128)>>7,{Fp:r}=_bn254.bn254.fields,i=r.create(pi(e)),o=r.sqrt(r.add(r.pow(i,3n),hn.B)),s=r.neg(o),a=o>s==(t===1)?o:s;return _bn254.bn254.G1.ProjectivePoint.fromAffine({x:i,y:a})}};hn.B=_bn254.bn254.fields.Fp.create(3n);var $e=hn;function pi(n){if(n.length!==32)throw new Error("Input should be 32 bytes");let e=new Uint8Array(n);return e[0]=e[0]&63,_utils.bytesToNumberBE.call(void 0, e)}var Tn=class Tn extends _chunkZMDE3DNLjs.l{constructor(e){if(super(),this.data=_chunkZMDE3DNLjs.j.fromHexInput(e).toUint8Array(),this.data.length!==64)throw new Error("Input needs to be 64 bytes")}serialize(e){e.serializeFixedBytes(this.data)}static deserialize(e){let t=e.deserializeFixedBytes(64);return new Tn(t)}toArray(){let e=this.toProjectivePoint();return[[e.x.c0.toString(),e.x.c1.toString()],[e.y.c0.toString(),e.y.c1.toString()],[e.pz.c0.toString(),e.pz.c1.toString()]]}toProjectivePoint(){let e=new Uint8Array(this.data),t=e.slice(0,32).reverse(),r=e.slice(32,64).reverse(),i=(r[0]&128)>>7,{Fp2:o}=_bn254.bn254.fields,s=o.fromBigTuple([pi(t),pi(r)]),a=o.sqrt(o.add(o.pow(s,3n),Tn.B)),c=o.neg(a),y=(a.c1>c.c1||a.c1===c.c1&&a.c0>c.c0)===(i===1)?a:c;return _bn254.bn254.G2.ProjectivePoint.fromAffine({x:s,y})}};Tn.B=_bn254.bn254.fields.Fp2.fromBigTuple([19485874751759354771024239261021720505790618469301721065564631296452457478373n,266929791119991161246907387137283842545076965332900288569378510910307636690n]);var vt=Tn,yt= exports.Groth16Zkp =class n extends nr{constructor(e){super();let{a:t,b:r,c:i}=e;this.a=new $e(t),this.b=new vt(r),this.c=new $e(i)}serialize(e){this.a.serialize(e),this.b.serialize(e),this.c.serialize(e)}static deserialize(e){let t=$e.deserialize(e).bcsToBytes(),r=vt.deserialize(e).bcsToBytes(),i=$e.deserialize(e).bcsToBytes();return new n({a:t,b:r,c:i})}toSnarkJsJson(){return{protocol:"groth16",curve:"bn128",pi_a:this.a.toArray(),pi_b:this.b.toArray(),pi_c:this.c.toArray()}}},di= exports.Groth16ProofAndStatement =class n extends _chunkZMDE3DNLjs.l{constructor(t,r){super();this.domainSeparator="APTOS::Groth16ProofAndStatement";if(this.proof=t,this.publicInputsHash=typeof r=="bigint"?ei(r,32):_chunkZMDE3DNLjs.j.fromHexInput(r).toUint8Array(),this.publicInputsHash.length!==32)throw new Error("Invalid public inputs hash")}serialize(t){this.proof.serialize(t),t.serializeFixedBytes(this.publicInputsHash)}static deserialize(t){return new n(yt.deserialize(t),t.deserializeFixedBytes(32))}hash(){return ft(this.bcsToBytes(),this.domainSeparator)}},jt= exports.ZkProof =class n extends _chunkZMDE3DNLjs.l{constructor(e,t){super(),this.proof=e,this.variant=t}serialize(e){e.serializeU32AsUleb128(this.variant),this.proof.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(yt.deserialize(e),t);default:throw new Error(`Unknown variant index for ZkProof: ${t}`)}}},ue= exports.ZeroKnowledgeSig =class n extends V{constructor(e){super();let{proof:t,expHorizonSecs:r,trainingWheelsSignature:i,extraField:o,overrideAudVal:s}=e;this.proof=t,this.expHorizonSecs=r,this.trainingWheelsSignature=i,this.extraField=o,this.overrideAudVal=s}static fromBytes(e){return n.deserialize(new U(e))}serialize(e){this.proof.serialize(e),e.serializeU64(this.expHorizonSecs),e.serializeOption(this.extraField),e.serializeOption(this.overrideAudVal),e.serializeOption(this.trainingWheelsSignature)}static deserialize(e){let t=jt.deserialize(e),r=Number(e.deserializeU64()),i=e.deserializeOption("string"),o=e.deserializeOption("string"),s=e.deserializeOption(Ne);return new n({proof:t,expHorizonSecs:r,trainingWheelsSignature:s,extraField:i,overrideAudVal:o})}},li= exports.KeylessConfiguration =class n{constructor(e){let{verificationKey:t,trainingWheelsPubkey:r,maxExpHorizonSecs:i=Wc,maxExtraFieldBytes:o=Xc,maxJwtHeaderB64Bytes:s=Yc,maxIssValBytes:a=Jc,maxCommitedEpkBytes:c=Zc}=e;this.verificationKey=t,this.maxExpHorizonSecs=i,r&&(this.trainingWheelsPubkey=new gt(new I(r))),this.maxExtraFieldBytes=o,this.maxJwtHeaderB64Bytes=s,this.maxIssValBytes=a,this.maxCommitedEpkBytes=c}static create(e,t){return new n({verificationKey:new gi({alphaG1:e.alpha_g1,betaG2:e.beta_g2,deltaG2:e.delta_g2,gammaAbcG1:e.gamma_abc_g1,gammaG2:e.gamma_g2}),maxExpHorizonSecs:Number(t.max_exp_horizon_secs),trainingWheelsPubkey:t.training_wheels_pubkey.vec[0],maxExtraFieldBytes:t.max_extra_field_bytes,maxJwtHeaderB64Bytes:t.max_jwt_header_b64_bytes,maxIssValBytes:t.max_iss_val_bytes,maxCommitedEpkBytes:t.max_commited_epk_bytes})}},gi= exports.Groth16VerificationKey =class n{constructor(e){let{alphaG1:t,betaG2:r,deltaG2:i,gammaAbcG1:o,gammaG2:s}=e;this.alphaG1=new $e(t),this.betaG2=new vt(r),this.deltaG2=new vt(i),this.gammaAbcG1=[new $e(o[0]),new $e(o[1])],this.gammaG2=new vt(s)}hash(){let e=new _chunkZMDE3DNLjs.m;return this.serialize(e),_sha3.sha3_256.create().update(e.toUint8Array()).digest()}serialize(e){this.alphaG1.serialize(e),this.betaG2.serialize(e),this.deltaG2.serialize(e),this.gammaAbcG1[0].serialize(e),this.gammaAbcG1[1].serialize(e),this.gammaG2.serialize(e)}static fromGroth16VerificationKeyResponse(e){return new n({alphaG1:e.alpha_g1,betaG2:e.beta_g2,deltaG2:e.delta_g2,gammaAbcG1:e.gamma_abc_g1,gammaG2:e.gamma_g2})}verifyProof(e){let{publicInputsHash:t,groth16Proof:r}=e;try{let i=r.a.toProjectivePoint(),o=r.b.toProjectivePoint(),s=r.c.toProjectivePoint(),a=this.alphaG1.toProjectivePoint(),c=this.betaG2.toProjectivePoint(),p=this.gammaG2.toProjectivePoint(),y=this.deltaG2.toProjectivePoint(),h=this.gammaAbcG1.map(ct=>ct.toProjectivePoint()),{Fp12:d}=_bn254.bn254.fields,l=h[0].add(h[1].multiply(t)),b=_bn254.bn254.pairing(l,p),R=_bn254.bn254.pairing(i,o),Q=_bn254.bn254.pairing(a,c),He=_bn254.bn254.pairing(s,y),at=d.mul(Q,d.mul(b,He));return d.eql(R,at)}catch(i){throw P.fromErrorType({type:32,error:i,details:"Error encountered when checking zero knowledge relation"})}}toSnarkJsJson(){return{protocol:"groth16",curve:"bn128",nPublic:1,vk_alpha_1:this.alphaG1.toArray(),vk_beta_2:this.betaG2.toArray(),vk_gamma_2:this.gammaG2.toArray(),vk_delta_2:this.deltaG2.toArray(),IC:this.gammaAbcG1.map(e=>e.toArray())}}};async function Jt(n){let{aptosConfig:e}=n;try{return await Fe(async()=>{let[t,r]=await Promise.all([tu(n),nu(n)]);return li.create(r,t)},`keyless-configuration-${e.network}`,1e3*60*5)()}catch(t){throw t instanceof P?t:P.fromErrorType({type:25,error:t})}}function We(n){let{jwt:e,uidKey:t="sub"}=n,r;try{r=_jwtdecode.jwtDecode.call(void 0, e)}catch(o){throw P.fromErrorType({type:12,details:`Failed to parse JWT - ${_chunkZMDE3DNLjs.ba.call(void 0, o)}`})}if(typeof r.iss!="string")throw P.fromErrorType({type:12,details:"JWT is missing 'iss' in the payload. This should never happen."});if(typeof r.aud!="string")throw P.fromErrorType({type:12,details:"JWT is missing 'aud' in the payload or 'aud' is an array of values."});let i=r[t];return{iss:r.iss,aud:r.aud,uidVal:i}}async function tu(n){let{aptosConfig:e,options:t}=n,r="0x1::keyless_account::Configuration";try{let{data:i}=await H({aptosConfig:e,originMethod:"getKeylessConfigurationResource",path:`accounts/${_chunkZMDE3DNLjs.Y.from("0x1").toString()}/resource/${r}`,params:{ledger_version:_optionalChain([t, 'optionalAccess', _47 => _47.ledgerVersion])}});return i.data}catch(i){throw P.fromErrorType({type:22,error:i})}}async function nu(n){let{aptosConfig:e,options:t}=n,r="0x1::keyless_account::Groth16VerificationKey";try{let{data:i}=await H({aptosConfig:e,originMethod:"getGroth16VerificationKeyResource",path:`accounts/${_chunkZMDE3DNLjs.Y.from("0x1").toString()}/resource/${r}`,params:{ledger_version:_optionalChain([t, 'optionalAccess', _48 => _48.ledgerVersion])}});return i.data}catch(i){throw P.fromErrorType({type:23,error:i})}}async function ru(n){let{aptosConfig:e,jwkAddr:t,options:r}=n,i;if(t){let s="0x1::jwks::FederatedJWKs",{data:a}=await H({aptosConfig:e,originMethod:"getKeylessJWKs",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/resource/${s}`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _49 => _49.ledgerVersion])}});i=a}else{let s="0x1::jwks::PatchedJWKs",{data:a}=await H({aptosConfig:e,originMethod:"getKeylessJWKs",path:`accounts/0x1/resource/${s}`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _50 => _50.ledgerVersion])}});i=a}let o=new Map;for(let s of i.data.jwks.entries){let a=[];for(let c of s.jwks){let{data:p}=c.variant,y=new U(_chunkZMDE3DNLjs.j.fromHexInput(p).toUint8Array()),h=mi.deserialize(y);a.push(h)}o.set(_chunkZMDE3DNLjs.k.call(void 0, s.issuer),a)}return o}var mi=class n extends _chunkZMDE3DNLjs.l{constructor(e){super();let{kid:t,kty:r,alg:i,e:o,n:s}=e;this.kid=t,this.kty=r,this.alg=i,this.e=o,this.n=s}serialize(e){e.serializeStr(this.kid),e.serializeStr(this.kty),e.serializeStr(this.alg),e.serializeStr(this.e),e.serializeStr(this.n)}static fromMoveStruct(e){let{data:t}=e.variant,r=new U(_chunkZMDE3DNLjs.j.fromHexInput(t).toUint8Array());return n.deserialize(r)}toScalar(){if(this.alg!=="RS256")throw P.fromErrorType({type:32,details:"Failed to convert JWK to scalar when calculating the public inputs hash. Only RSA 256 is supported currently"});let e=_chunkZMDE3DNLjs.fa.call(void 0, this.n),r=iu(e.reverse()).map(i=>_t(i));return r.push(256n),xt(r)}static deserialize(e){let t=e.deserializeStr(),r=e.deserializeStr(),i=e.deserializeStr(),o=e.deserializeStr(),s=e.deserializeStr();return new n({kid:t,kty:r,alg:i,n:s,e:o})}};function iu(n){let e=[];for(let t=0;t<n.length;t+=24){let r=n.slice(t,Math.min(t+24,n.length));if(r.length<24){let i=new Uint8Array(24);i.set(r),e.push(i)}else e.push(r)}return e}function ou(n){try{let e=JSON.parse(n);if(e.kid===void 0)throw new Error("JWT header missing kid");return e}catch (e2){throw new Error("Failed to parse JWT header.")}}var $=class n extends re{constructor(e,t){super(),this.jwkAddress=_chunkZMDE3DNLjs.Y.from(e),this.keylessPublicKey=t}authKey(){let e=new _chunkZMDE3DNLjs.m;return e.serializeU32AsUleb128(4),e.serializeFixedBytes(this.bcsToBytes()),M.fromSchemeAndBytes({scheme:2,input:e.toUint8Array()})}verifySignature(e){try{return ur({...e,publicKey:this}),!0}catch (e3){return!1}}serialize(e){this.jwkAddress.serialize(e),this.keylessPublicKey.serialize(e)}static deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=K.deserialize(e);return new n(t,r)}static isPublicKey(e){return e instanceof n}async verifySignatureAsync(e){return yi({...e,publicKey:this})}static create(e){return new n(e.jwkAddress,K.create(e))}static fromJwtAndPepper(e){return new n(e.jwkAddress,K.fromJwtAndPepper(e))}static isInstance(e){return"jwkAddress"in e&&e.jwkAddress instanceof _chunkZMDE3DNLjs.Y&&"keylessPublicKey"in e&&e.keylessPublicKey instanceof K}};var _secp256k1 = require('@noble/curves/secp256k1');var _bip32 = require('@scure/bip32');var Ke=class Ke extends Pt{constructor(e){super();let t=_chunkZMDE3DNLjs.j.fromHexInput(e),{length:r}=t.toUint8Array();if(r===Ke.LENGTH)this.key=t;else if(r===Ke.COMPRESSED_LENGTH){let i=_secp256k1.secp256k1.ProjectivePoint.fromHex(t.toUint8Array());this.key=_chunkZMDE3DNLjs.j.fromHexInput(i.toRawBytes(!1))}else throw new Error(`PublicKey length should be ${Ke.LENGTH} or ${Ke.COMPRESSED_LENGTH}, received ${r}`)}verifySignature(e){let{message:t,signature:r}=e,i=qt(t),o=_chunkZMDE3DNLjs.j.fromHexInput(i).toUint8Array(),s=_sha3.sha3_256.call(void 0, o),a=r.toUint8Array();return _secp256k1.secp256k1.verify(a,s,this.key.toUint8Array(),{lowS:!0})}async verifySignatureAsync(e){return this.verifySignature(e)}toUint8Array(){return this.key.toUint8Array()}serialize(e){e.serializeBytes(this.key.toUint8Array())}deserialize(e){let t=e.deserializeBytes();return new je(t)}static deserialize(e){let t=e.deserializeBytes();return new Ke(t)}static isPublicKey(e){return e instanceof Ke}static isInstance(e){return"key"in e&&_optionalChain([e, 'access', _51 => _51.key, 'optionalAccess', _52 => _52.data, 'optionalAccess', _53 => _53.length])===Ke.LENGTH}};Ke.LENGTH=65,Ke.COMPRESSED_LENGTH=33;var Re=Ke,Be=class Be extends _chunkZMDE3DNLjs.l{constructor(e,t){super();let r=dt.parseHexInput(e,"secp256k1",t);if(r.toUint8Array().length!==Be.LENGTH)throw new Error(`PrivateKey length should be ${Be.LENGTH}`);this.key=r}static generate(){let e=_secp256k1.secp256k1.utils.randomPrivateKey();return new Be(e,!1)}static fromDerivationPath(e,t){if(!po(e))throw new Error(`Invalid derivation path ${e}`);return Be.fromDerivationPathInner(e,er(t))}static fromDerivationPathInner(e,t){let{privateKey:r}=_bip32.HDKey.fromMasterSeed(t).derive(e);if(r===null)throw new Error("Invalid key");return new Be(r,!1)}sign(e){let t=qt(e),r=_chunkZMDE3DNLjs.j.fromHexInput(t),i=_sha3.sha3_256.call(void 0, r.toUint8Array()),o=_secp256k1.secp256k1.sign(i,this.key.toUint8Array(),{lowS:!0});return new je(o.toCompactRawBytes())}publicKey(){let e=_secp256k1.secp256k1.getPublicKey(this.key.toUint8Array(),!1);return new Re(e)}toUint8Array(){return this.key.toUint8Array()}toString(){return this.toAIP80String()}toHexString(){return this.key.toString()}toAIP80String(){return dt.formatPrivateKey(this.key.toString(),"secp256k1")}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new Be(t,!1)}static isPrivateKey(e){return e instanceof Be}};Be.LENGTH=32;var Qe=Be,Xt=class Xt extends V{constructor(e){super();let t=_chunkZMDE3DNLjs.j.fromHexInput(e);if(t.toUint8Array().length!==Xt.LENGTH)throw new Error(`Signature length should be ${Xt.LENGTH}, received ${t.toUint8Array().length}`);this.data=t}toUint8Array(){return this.data.toUint8Array()}serialize(e){e.serializeBytes(this.data.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new Xt(t)}};Xt.LENGTH=64;var je=Xt;var N=class n extends re{constructor(e){if(super(),this.publicKey=e,e instanceof I)this.variant=0;else if(e instanceof Re)this.variant=1;else if(e instanceof K)this.variant=3;else if(e instanceof $)this.variant=4;else throw new Error("Unsupported public key type")}verifySignature(e){let{message:t,signature:r}=e;if(this.publicKey instanceof K)throw new Error("Use verifySignatureAsync to verify Keyless signatures");return this.publicKey.verifySignature({message:t,signature:r.signature})}async verifySignatureAsync(e){if(!(e.signature instanceof O)){if(_optionalChain([e, 'access', _54 => _54.options, 'optionalAccess', _55 => _55.throwErrorWithReason]))throw new Error("Signature must be an instance of AnySignature");return!1}return await this.publicKey.verifySignatureAsync({...e,signature:e.signature.signature})}authKey(){return M.fromSchemeAndBytes({scheme:2,input:this.toUint8Array()})}toUint8Array(){return this.bcsToBytes()}serialize(e){e.serializeU32AsUleb128(this.variant),this.publicKey.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=I.deserialize(e);break;case 1:r=Re.deserialize(e);break;case 3:r=K.deserialize(e);break;case 4:r=$.deserialize(e);break;default:throw new Error(`Unknown variant index for AnyPublicKey: ${t}`)}return new n(r)}static isPublicKey(e){return e instanceof n}isEd25519(){return this.publicKey instanceof I}isSecp256k1PublicKey(){return this.publicKey instanceof Re}static isInstance(e){return"publicKey"in e&&"variant"in e}},O= exports.AnySignature =class n extends V{constructor(e){if(super(),this.signature=e,e instanceof v)this.variant=0;else if(e instanceof je)this.variant=1;else if(e instanceof ie)this.variant=3;else throw new Error("Unsupported signature type")}toUint8Array(){return console.warn("[Aptos SDK] Calls to AnySignature.toUint8Array() will soon return the underlying signature bytes. Use AnySignature.bcsToBytes() instead."),this.bcsToBytes()}serialize(e){e.serializeU32AsUleb128(this.variant),this.signature.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=v.deserialize(e);break;case 1:r=je.deserialize(e);break;case 3:r=ie.deserialize(e);break;default:throw new Error(`Unknown variant index for AnySignature: ${t}`)}return new n(r)}static isInstance(e){return"signature"in e&&typeof e.signature=="object"&&e.signature!==null&&"toUint8Array"in e.signature}};function au(n){let e=n;return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>24}var wn=class extends re{constructor(e){super(),this.publicKeys=e.publicKeys}createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach((s,a)=>{if(a+1>this.publicKeys.length)throw new Error(`Signature index ${a+1} is out of public keys range, ${this.publicKeys.length}.`);if(o.has(s))throw new Error(`Duplicate bit ${s} detected.`);o.add(s);let c=Math.floor(s/8),p=i[c];p|=r>>s%8,i[c]=p}),i}getIndex(e){let t=this.publicKeys.findIndex(r=>r.toString()===e.toString());if(t!==-1)return t;throw new Error(`Public key ${e} not found in multi key set ${this.publicKeys}`)}},Te= exports.MultiKey =class n extends wn{constructor(e){let{publicKeys:t,signaturesRequired:r}=e;if(super({publicKeys:t}),r<1)throw new Error("The number of required signatures needs to be greater than 0");if(t.length<r)throw new Error(`Provided ${t.length} public keys is smaller than the ${r} required signatures`);this.publicKeys=t.map(i=>i instanceof N?i:new N(i)),this.signaturesRequired=r}verifySignature(e){let{message:t,signature:r}=e;if(r.signatures.length!==this.signaturesRequired)throw new Error("The number of signatures does not match the number of required signatures");let i=r.bitMapToSignerIndices();for(let o=0;o<r.signatures.length;o+=1){let s=r.signatures[o];if(!this.publicKeys[i[o]].verifySignature({message:t,signature:s}))return!1}return!0}async verifySignatureAsync(e){let{signature:t}=e;try{if(!(t instanceof be))throw new Error("Signature is not a MultiKeySignature");if(t.signatures.length!==this.signaturesRequired)throw new Error("The number of signatures does not match the number of required signatures");let r=t.bitMapToSignerIndices();for(let i=0;i<t.signatures.length;i+=1){let o=t.signatures[i];if(!await this.publicKeys[r[i]].verifySignatureAsync({...e,signature:o}))return!1}return!0}catch(r){if(_optionalChain([e, 'access', _56 => _56.options, 'optionalAccess', _57 => _57.throwErrorWithReason]))throw r;return!1}}authKey(){return M.fromSchemeAndBytes({scheme:3,input:this.toUint8Array()})}serialize(e){e.serializeVector(this.publicKeys),e.serializeU8(this.signaturesRequired)}static deserialize(e){let t=e.deserializeVector(N),r=e.deserializeU8();return new n({publicKeys:t,signaturesRequired:r})}getIndex(e){let t=e instanceof N?e:new N(e);return super.getIndex(t)}static isInstance(e){return"publicKeys"in e&&"signaturesRequired"in e}},pe=class pe extends V{constructor(e){super();let{signatures:t,bitmap:r}=e;if(t.length>pe.MAX_SIGNATURES_SUPPORTED)throw new Error(`The number of signatures cannot be greater than ${pe.MAX_SIGNATURES_SUPPORTED}`);if(this.signatures=t.map(o=>o instanceof O?o:new O(o)),!(r instanceof Uint8Array))this.bitmap=pe.createBitmap({bits:r});else{if(r.length!==pe.BITMAP_LEN)throw new Error(`"bitmap" length should be ${pe.BITMAP_LEN}`);this.bitmap=r}let i=this.bitmap.reduce((o,s)=>o+au(s),0);if(i!==this.signatures.length)throw new Error(`Expecting ${i} signatures from the bitmap, but got ${this.signatures.length}`)}static createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach(s=>{if(s>=pe.MAX_SIGNATURES_SUPPORTED)throw new Error(`Cannot have a signature larger than ${pe.MAX_SIGNATURES_SUPPORTED-1}.`);if(o.has(s))throw new Error("Duplicate bits detected.");o.add(s);let a=Math.floor(s/8),c=i[a];c|=r>>s%8,i[a]=c}),i}bitMapToSignerIndices(){let e=[];for(let t=0;t<this.bitmap.length;t+=1){let r=this.bitmap[t];for(let i=0;i<8;i+=1)(r&128>>i)!==0&&e.push(t*8+i)}return e}serialize(e){e.serializeVector(this.signatures),e.serializeBytes(this.bitmap)}static deserialize(e){let t=e.deserializeVector(O),r=e.deserializeBytes();return new pe({signatures:t,bitmap:r})}};pe.BITMAP_LEN=4,pe.MAX_SIGNATURES_SUPPORTED=pe.BITMAP_LEN*8;var be=pe;var we=class we extends wn{constructor(e){let{publicKeys:t,threshold:r}=e;if(super({publicKeys:t}),t.length>we.MAX_KEYS||t.length<we.MIN_KEYS)throw new Error(`Must have between ${we.MIN_KEYS} and ${we.MAX_KEYS} public keys, inclusive`);if(r<we.MIN_THRESHOLD||r>t.length)throw new Error(`Threshold must be between ${we.MIN_THRESHOLD} and ${t.length}, inclusive`);this.publicKeys=t,this.threshold=r}verifySignature(e){let{message:t,signature:r}=e;if(!(r instanceof Ue))return!1;let i=[];for(let o=0;o<4;o+=1)for(let s=0;s<8;s+=1)if((r.bitmap[o]&1<<7-s)!==0){let c=o*8+s;i.push(c)}if(i.length!==r.signatures.length)throw new Error("Bitmap and signatures length mismatch");if(i.length<this.threshold)throw new Error("Not enough signatures");for(let o=0;o<i.length;o+=1)if(!this.publicKeys[i[o]].verifySignature({message:t,signature:r.signatures[o]}))return!1;return!0}async verifySignatureAsync(e){return this.verifySignature(e)}authKey(){return M.fromSchemeAndBytes({scheme:1,input:this.toUint8Array()})}toUint8Array(){let e=new Uint8Array(this.publicKeys.length*I.LENGTH+1);return this.publicKeys.forEach((t,r)=>{e.set(t.toUint8Array(),r*I.LENGTH)}),e[this.publicKeys.length*I.LENGTH]=this.threshold,e}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes(),r=t[t.length-1],i=[];for(let o=0;o<t.length-1;o+=I.LENGTH){let s=o;i.push(new I(t.subarray(s,s+I.LENGTH)))}return new we({publicKeys:i,threshold:r})}getIndex(e){return super.getIndex(e)}};we.MAX_KEYS=32,we.MIN_KEYS=2,we.MIN_THRESHOLD=1;var At=we,de=class de extends V{constructor(e){super();let{signatures:t,bitmap:r}=e;if(t.length>de.MAX_SIGNATURES_SUPPORTED)throw new Error(`The number of signatures cannot be greater than ${de.MAX_SIGNATURES_SUPPORTED}`);if(this.signatures=t,!(r instanceof Uint8Array))this.bitmap=de.createBitmap({bits:r});else{if(r.length!==de.BITMAP_LEN)throw new Error(`"bitmap" length should be ${de.BITMAP_LEN}`);this.bitmap=r}}toUint8Array(){let e=new Uint8Array(this.signatures.length*v.LENGTH+de.BITMAP_LEN);return this.signatures.forEach((t,r)=>{e.set(t.toUint8Array(),r*v.LENGTH)}),e.set(this.bitmap,this.signatures.length*v.LENGTH),e}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes(),r=t.subarray(t.length-4),i=[];for(let o=0;o<t.length-r.length;o+=v.LENGTH){let s=o;i.push(new v(t.subarray(s,s+v.LENGTH)))}return new de({signatures:i,bitmap:r})}static createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach((s,a)=>{if(s>=de.MAX_SIGNATURES_SUPPORTED)throw new Error(`Cannot have a signature larger than ${de.MAX_SIGNATURES_SUPPORTED-1}.`);if(o.has(s))throw new Error("Duplicate bits detected.");if(a>0&&s<=t[a-1])throw new Error("The bits need to be sorted in ascending order.");o.add(s);let c=Math.floor(s/8),p=i[c];p|=r>>s%8,i[c]=p}),i}};de.MAX_SIGNATURES_SUPPORTED=32,de.BITMAP_LEN=4;var Ue=de;var pr="Multiple possible deserializations found";function ny(n){let e=[I,N,At,Te,K,$,Re],t;for(let r of e)try{let i=U.fromHex(n),o=r.deserialize(i);if(i.assertFinished(),t)throw new Error(`${pr}: ${n}`);t=o}catch(i){if(i instanceof Error&&i.message.includes(pr))throw i}if(!t)throw new Error(`Failed to deserialize public key: ${n}`);return t}function ry(n){let e=[v,O,Ue,be,ie,je],t;for(let r of e)try{let i=U.fromHex(n),o=r.deserialize(i);if(i.assertFinished(),t)throw new Error(`${pr}: ${n}`);t=o}catch(i){if(i instanceof Error&&i.message.includes(pr))throw i}if(!t)throw new Error(`Failed to deserialize signature: ${n}`);return t}var J=class extends _chunkZMDE3DNLjs.l{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return ke.load(e);case 1:return Je.load(e);case 2:return oe.load(e);case 3:return Me.load(e);case 4:return Yt.load(e);case 5:return Ct.load(e);default:throw new Error(`Unknown variant index for AccountAuthenticator: ${t}`)}}isEd25519(){return this instanceof ke}isMultiEd25519(){return this instanceof Je}isSingleKey(){return this instanceof oe}isMultiKey(){return this instanceof Me}},ke= exports.AccountAuthenticatorEd25519 =class n extends J{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(0),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=I.deserialize(e),r=v.deserialize(e);return new n(t,r)}},Je= exports.AccountAuthenticatorMultiEd25519 =class n extends J{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(1),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=At.deserialize(e),r=Ue.deserialize(e);return new n(t,r)}},oe= exports.AccountAuthenticatorSingleKey =class n extends J{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(2),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=N.deserialize(e),r=O.deserialize(e);return new n(t,r)}},Me= exports.AccountAuthenticatorMultiKey =class n extends J{constructor(e,t){super(),this.public_keys=e,this.signatures=t}serialize(e){e.serializeU32AsUleb128(3),this.public_keys.serialize(e),this.signatures.serialize(e)}static load(e){let t=Te.deserialize(e),r=be.deserialize(e);return new n(t,r)}},Yt= exports.AccountAuthenticatorNoAccountAuthenticator =class n extends J{serialize(e){e.serializeU32AsUleb128(4)}static load(e){return new n}},Ct= exports.AccountAuthenticatorAbstraction =class n extends J{constructor(e,t,r,i){if(super(),!_chunkZMDE3DNLjs.la.call(void 0, e))throw new Error(`Invalid function info ${e} passed into AccountAuthenticatorAbstraction`);this.functionInfo=e,this.authenticator=r,this.signingMessageDigest=_chunkZMDE3DNLjs.j.fromHexInput(_chunkZMDE3DNLjs.j.fromHexInput(t).toUint8Array()),this.accountIdentity=i}serialize(e){e.serializeU32AsUleb128(5);let{moduleAddress:t,moduleName:r,functionName:i}=_chunkZMDE3DNLjs.ka.call(void 0, this.functionInfo);_chunkZMDE3DNLjs.Y.fromString(t).serialize(e),e.serializeStr(r),e.serializeStr(i),this.accountIdentity?e.serializeU32AsUleb128(1):e.serializeU32AsUleb128(0),e.serializeBytes(this.signingMessageDigest.toUint8Array()),this.accountIdentity?e.serializeBytes(this.authenticator):e.serializeFixedBytes(this.authenticator),this.accountIdentity&&e.serializeBytes(this.accountIdentity)}static load(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=e.deserializeStr(),i=e.deserializeStr(),o=e.deserializeUleb128AsU32();if(o===0){let s=e.deserializeBytes(),a=e.deserializeFixedBytes(e.remaining());return new n(`${t}::${r}::${i}`,s,a)}if(o===1){let s=e.deserializeBytes(),a=e.deserializeBytes(),c=e.deserializeBytes();return new n(`${t}::${r}::${i}`,s,a,c)}throw new Error(`Unknown variant index for AccountAuthenticatorAbstraction: ${o}`)}};var Zt=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.chainId=e}serialize(e){e.serializeU8(this.chainId)}static deserialize(e){let t=e.deserializeU8();return new n(t)}};var k=class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.identifier=e}serialize(e){e.serializeStr(this.identifier)}static deserialize(e){let t=e.deserializeStr();return new n(t)}};var Sn=class n extends _chunkZMDE3DNLjs.l{constructor(e,t){super(),this.address=e,this.name=t}static fromStr(e){let t=e.split("::");if(t.length!==2)throw new Error("Invalid module id.");return new n(_chunkZMDE3DNLjs.Y.fromString(t[0]),new k(t[1]))}serialize(e){this.address.serialize(e),this.name.serialize(e)}static deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=k.deserialize(e);return new n(t,r)}};var G=class n extends _chunkZMDE3DNLjs.l{deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=k.deserialize(e),i=k.deserialize(e),o=e.deserializeVector(n);return new Ve(t,r,i,o)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return L.load(e);case 1:return le.load(e);case 2:return X.load(e);case 3:return Ze.load(e);case 4:return F.load(e);case 5:return Kt.load(e);case 6:return C.load(e);case 7:return A.load(e);case 8:return Xe.load(e);case 9:return Ye.load(e);case 10:return et.load(e);case 255:return B.load(e);default:throw new Error(`Unknown variant index for TypeTag: ${t}`)}}isBool(){return this instanceof L}isAddress(){return this instanceof F}isGeneric(){return this instanceof B}isSigner(){return this instanceof Kt}isVector(){return this instanceof C}isStruct(){return this instanceof A}isU8(){return this instanceof le}isU16(){return this instanceof Xe}isU32(){return this instanceof Ye}isU64(){return this instanceof X}isU128(){return this instanceof Ze}isU256(){return this instanceof et}isPrimitive(){return this instanceof Kt||this instanceof F||this instanceof L||this instanceof le||this instanceof Xe||this instanceof Ye||this instanceof X||this instanceof Ze||this instanceof et}},L= exports.TypeTagBool =class n extends G{toString(){return"bool"}serialize(e){e.serializeU32AsUleb128(0)}static load(e){return new n}},le= exports.TypeTagU8 =class n extends G{toString(){return"u8"}serialize(e){e.serializeU32AsUleb128(1)}static load(e){return new n}},Xe= exports.TypeTagU16 =class n extends G{toString(){return"u16"}serialize(e){e.serializeU32AsUleb128(8)}static load(e){return new n}},Ye= exports.TypeTagU32 =class n extends G{toString(){return"u32"}serialize(e){e.serializeU32AsUleb128(9)}static load(e){return new n}},X= exports.TypeTagU64 =class n extends G{toString(){return"u64"}serialize(e){e.serializeU32AsUleb128(2)}static load(e){return new n}},Ze= exports.TypeTagU128 =class n extends G{toString(){return"u128"}serialize(e){e.serializeU32AsUleb128(3)}static load(e){return new n}},et= exports.TypeTagU256 =class n extends G{toString(){return"u256"}serialize(e){e.serializeU32AsUleb128(10)}static load(e){return new n}},F= exports.TypeTagAddress =class n extends G{toString(){return"address"}serialize(e){e.serializeU32AsUleb128(4)}static load(e){return new n}},Kt= exports.TypeTagSigner =class n extends G{toString(){return"signer"}serialize(e){e.serializeU32AsUleb128(5)}static load(e){return new n}},dr= exports.TypeTagReference =class n extends G{constructor(t){super();this.value=t}toString(){return`&${this.value.toString()}`}serialize(t){t.serializeU32AsUleb128(254)}static load(t){let r=G.deserialize(t);return new n(r)}},B= exports.TypeTagGeneric =class n extends G{constructor(t){super();this.value=t;if(t<0)throw new Error("Generic type parameter index cannot be negative")}toString(){return`T${this.value}`}serialize(t){t.serializeU32AsUleb128(255),t.serializeU32(this.value)}static load(t){let r=t.deserializeU32();return new n(r)}},C= exports.TypeTagVector =class n extends G{constructor(t){super();this.value=t}toString(){return`vector<${this.value.toString()}>`}static u8(){return new n(new le)}serialize(t){t.serializeU32AsUleb128(6),this.value.serialize(t)}static load(t){let r=G.deserialize(t);return new n(r)}},A= exports.TypeTagStruct =class n extends G{constructor(t){super();this.value=t}toString(){let t="";return this.value.typeArgs.length>0&&(t=`<${this.value.typeArgs.map(r=>r.toString()).join(", ")}>`),`${this.value.address.toString()}::${this.value.moduleName.identifier}::${this.value.name.identifier}${t}`}serialize(t){t.serializeU32AsUleb128(7),this.value.serialize(t)}static load(t){let r=Ve.deserialize(t);return new n(r)}isTypeTag(t,r,i){return this.value.moduleName.identifier===r&&this.value.name.identifier===i&&this.value.address.equals(t)}isString(){return this.isTypeTag(_chunkZMDE3DNLjs.Y.ONE,"string","String")}isOption(){return this.isTypeTag(_chunkZMDE3DNLjs.Y.ONE,"option","Option")}isObject(){return this.isTypeTag(_chunkZMDE3DNLjs.Y.ONE,"object","Object")}isDelegationKey(){return this.isTypeTag(_chunkZMDE3DNLjs.Y.ONE,"permissioned_delegation","DelegationKey")}isRateLimiter(){return this.isTypeTag(_chunkZMDE3DNLjs.Y.ONE,"rate_limiter","RateLimiter")}},Ve= exports.StructTag =class n extends _chunkZMDE3DNLjs.l{constructor(e,t,r,i){super(),this.address=e,this.moduleName=t,this.name=r,this.typeArgs=i}serialize(e){e.serialize(this.address),e.serialize(this.moduleName),e.serialize(this.name),e.serializeVector(this.typeArgs)}static deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=k.deserialize(e),i=k.deserialize(e),o=e.deserializeVector(G);return new n(t,r,i,o)}};function Zy(){return new Ve(_chunkZMDE3DNLjs.Y.ONE,new k("aptos_coin"),new k("AptosCoin"),[])}function _(){return new Ve(_chunkZMDE3DNLjs.Y.ONE,new k("string"),new k("String"),[])}function ef(n){return new Ve(_chunkZMDE3DNLjs.Y.ONE,new k("option"),new k("Option"),[n])}function ge(n){return new Ve(_chunkZMDE3DNLjs.Y.ONE,new k("object"),new k("Object"),[n])}function cu(n){let e=n.deserializeUleb128AsU32();switch(e){case 0:return Y.deserialize(n);case 1:return j.deserialize(n);case 2:return ve.deserialize(n);case 3:return _chunkZMDE3DNLjs.Y.deserialize(n);case 4:return S.deserialize(n,Y);case 5:return z.deserialize(n);case 6:return xe.deserialize(n);case 7:return Ie.deserialize(n);case 8:return fe.deserialize(n);case 9:return gn.deserialize(n);default:throw new Error(`Unknown variant index for ScriptTransactionArgument: ${e}`)}}var Rt=class extends _chunkZMDE3DNLjs.l{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return En.load(e);case 2:return Pn.load(e);case 3:return _n.load(e);default:throw new Error(`Unknown variant index for TransactionPayload: ${t}`)}}},En= exports.TransactionPayloadScript =class n extends Rt{constructor(e){super(),this.script=e}serialize(e){e.serializeU32AsUleb128(0),this.script.serialize(e)}static load(e){let t=xn.deserialize(e);return new n(t)}},Pn= exports.TransactionPayloadEntryFunction =class n extends Rt{constructor(e){super(),this.entryFunction=e}serialize(e){e.serializeU32AsUleb128(2),this.entryFunction.serialize(e)}static load(e){let t=Ut.deserialize(e);return new n(t)}},_n= exports.TransactionPayloadMultiSig =class n extends Rt{constructor(e){super(),this.multiSig=e}serialize(e){e.serializeU32AsUleb128(3),this.multiSig.serialize(e)}static load(e){let t=In.deserialize(e);return new n(t)}},Ut= exports.EntryFunction =class n{constructor(e,t,r,i){this.module_name=e,this.function_name=t,this.type_args=r,this.args=i}static build(e,t,r,i){return new n(Sn.fromStr(e),new k(t),r,i)}serialize(e){this.module_name.serialize(e),this.function_name.serialize(e),e.serializeVector(this.type_args),e.serializeU32AsUleb128(this.args.length),this.args.forEach(t=>{t.serializeForEntryFunction(e)})}static deserialize(e){let t=Sn.deserialize(e),r=k.deserialize(e),i=e.deserializeVector(G),o=e.deserializeUleb128AsU32(),s=new Array;for(let a=0;a<o;a+=1){let c=e.deserializeUleb128AsU32(),p=Yn.deserialize(e,c);s.push(p)}return new n(t,r,i,s)}},xn= exports.Script =class n{constructor(e,t,r){this.bytecode=e,this.type_args=t,this.args=r}serialize(e){e.serializeBytes(this.bytecode),e.serializeVector(this.type_args),e.serializeU32AsUleb128(this.args.length),this.args.forEach(t=>{t.serializeForScriptFunction(e)})}static deserialize(e){let t=e.deserializeBytes(),r=e.deserializeVector(G),i=e.deserializeUleb128AsU32(),o=new Array;for(let s=0;s<i;s+=1){let a=cu(e);o.push(a)}return new n(t,r,o)}},In= exports.MultiSig =class n{constructor(e,t){this.multisig_address=e,this.transaction_payload=t}serialize(e){this.multisig_address.serialize(e),this.transaction_payload===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.transaction_payload.serialize(e))}static deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=e.deserializeBool(),i;return r&&(i=vn.deserialize(e)),new n(t,i)}},vn= exports.MultiSigTransactionPayload =class n extends _chunkZMDE3DNLjs.l{constructor(e){super(),this.transaction_payload=e}serialize(e){e.serializeU32AsUleb128(0),this.transaction_payload.serialize(e)}static deserialize(e){return e.deserializeUleb128AsU32(),new n(Ut.deserialize(e))}};var Se=class n extends _chunkZMDE3DNLjs.l{constructor(e,t,r,i,o,s,a){super(),this.sender=e,this.sequence_number=t,this.payload=r,this.max_gas_amount=i,this.gas_unit_price=o,this.expiration_timestamp_secs=s,this.chain_id=a}serialize(e){this.sender.serialize(e),e.serializeU64(this.sequence_number),this.payload.serialize(e),e.serializeU64(this.max_gas_amount),e.serializeU64(this.gas_unit_price),e.serializeU64(this.expiration_timestamp_secs),this.chain_id.serialize(e)}static deserialize(e){let t=_chunkZMDE3DNLjs.Y.deserialize(e),r=e.deserializeU64(),i=Rt.deserialize(e),o=e.deserializeU64(),s=e.deserializeU64(),a=e.deserializeU64(),c=Zt.deserialize(e);return new n(t,r,i,o,s,a,c)}},lr= exports.RawTransactionWithData =class extends _chunkZMDE3DNLjs.l{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return kt.load(e);case 1:return Dt.load(e);default:throw new Error(`Unknown variant index for RawTransactionWithData: ${t}`)}}},kt= exports.MultiAgentRawTransaction =class n extends lr{constructor(e,t){super(),this.raw_txn=e,this.secondary_signer_addresses=t}serialize(e){e.serializeU32AsUleb128(0),this.raw_txn.serialize(e),e.serializeVector(this.secondary_signer_addresses)}static load(e){let t=Se.deserialize(e),r=e.deserializeVector(_chunkZMDE3DNLjs.Y);return new n(t,r)}},Dt= exports.FeePayerRawTransaction =class n extends lr{constructor(e,t,r){super(),this.raw_txn=e,this.secondary_signer_addresses=t,this.fee_payer_address=r}serialize(e){e.serializeU32AsUleb128(1),this.raw_txn.serialize(e),e.serializeVector(this.secondary_signer_addresses),this.fee_payer_address.serialize(e)}static load(e){let t=Se.deserialize(e),r=e.deserializeVector(_chunkZMDE3DNLjs.Y),i=_chunkZMDE3DNLjs.Y.deserialize(e);return new n(t,r,i)}};var gr=class extends _chunkZMDE3DNLjs.l{constructor(t){super();this.accountAddress=_chunkZMDE3DNLjs.Y.ONE;this.moduleName=new x("account");this.structName=new x("RotationProofChallenge");this.sequenceNumber=new j(t.sequenceNumber),this.originator=t.originator,this.currentAuthKey=t.currentAuthKey,this.newPublicKey=S.U8(t.newPublicKey.toUint8Array())}serialize(t){t.serialize(this.accountAddress),t.serialize(this.moduleName),t.serialize(this.structName),t.serialize(this.sequenceNumber),t.serialize(this.originator),t.serialize(this.currentAuthKey),t.serialize(this.newPublicKey)}};var tt=class extends _chunkZMDE3DNLjs.l{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return zt.load(e);case 1:return en.load(e);case 2:return Nt.load(e);case 3:return Ot.load(e);case 4:return ht.load(e);default:throw new Error(`Unknown variant index for TransactionAuthenticator: ${t}`)}}isEd25519(){return this instanceof zt}isMultiEd25519(){return this instanceof en}isMultiAgent(){return this instanceof Nt}isFeePayer(){return this instanceof Ot}isSingleSender(){return this instanceof ht}},zt= exports.TransactionAuthenticatorEd25519 =class n extends tt{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(0),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=I.deserialize(e),r=v.deserialize(e);return new n(t,r)}},en= exports.TransactionAuthenticatorMultiEd25519 =class n extends tt{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(1),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=At.deserialize(e),r=Ue.deserialize(e);return new n(t,r)}},Nt= exports.TransactionAuthenticatorMultiAgent =class n extends tt{constructor(e,t,r){super(),this.sender=e,this.secondary_signer_addresses=t,this.secondary_signers=r}serialize(e){e.serializeU32AsUleb128(2),this.sender.serialize(e),e.serializeVector(this.secondary_signer_addresses),e.serializeVector(this.secondary_signers)}static load(e){let t=J.deserialize(e),r=e.deserializeVector(_chunkZMDE3DNLjs.Y),i=e.deserializeVector(J);return new n(t,r,i)}},Ot= exports.TransactionAuthenticatorFeePayer =class n extends tt{constructor(e,t,r,i){super(),this.sender=e,this.secondary_signer_addresses=t,this.secondary_signers=r,this.fee_payer=i}serialize(e){e.serializeU32AsUleb128(3),this.sender.serialize(e),e.serializeVector(this.secondary_signer_addresses),e.serializeVector(this.secondary_signers),this.fee_payer.address.serialize(e),this.fee_payer.authenticator.serialize(e)}static load(e){let t=J.deserialize(e),r=e.deserializeVector(_chunkZMDE3DNLjs.Y),i=e.deserializeVector(J),o=_chunkZMDE3DNLjs.Y.deserialize(e),s=J.deserialize(e),a={address:o,authenticator:s};return new n(t,r,i,a)}},ht= exports.TransactionAuthenticatorSingleSender =class n extends tt{constructor(e){super(),this.sender=e}serialize(e){e.serializeU32AsUleb128(4),this.sender.serialize(e)}static load(e){let t=J.deserialize(e);return new n(t)}};var nt=class n extends _chunkZMDE3DNLjs.l{constructor(e,t){super(),this.raw_txn=e,this.authenticator=t}serialize(e){this.raw_txn.serialize(e),this.authenticator.serialize(e)}static deserialize(e){let t=Se.deserialize(e),r=tt.deserialize(e);return new n(t,r)}};var mr=class n extends _chunkZMDE3DNLjs.l{constructor(e,t){super(),this.rawTransaction=e,this.feePayerAddress=t}serialize(e){this.rawTransaction.serialize(e),this.feePayerAddress===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.feePayerAddress.serialize(e))}static deserialize(e){let t=Se.deserialize(e),r=e.deserializeBool(),i;return r&&(i=_chunkZMDE3DNLjs.Y.deserialize(e)),new n(t,i)}};var yr=class n extends _chunkZMDE3DNLjs.l{constructor(e,t,r){super(),this.rawTransaction=e,this.feePayerAddress=r,this.secondarySignerAddresses=t}serialize(e){this.rawTransaction.serialize(e),e.serializeVector(this.secondarySignerAddresses),this.feePayerAddress===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.feePayerAddress.serialize(e))}static deserialize(e){let t=Se.deserialize(e),r=e.deserializeVector(_chunkZMDE3DNLjs.Y),i=e.deserializeBool(),o;return i&&(o=_chunkZMDE3DNLjs.Y.deserialize(e)),new n(t,r,o)}};function fr(n){return n.feePayerAddress?new Dt(n.rawTransaction,_nullishCoalesce(n.secondarySignerAddresses, () => ([])),n.feePayerAddress):n.secondarySignerAddresses?new kt(n.rawTransaction,n.secondarySignerAddresses):n.rawTransaction}function ft(n,e){let t=_sha3.sha3_256.create();if(!e.startsWith("APTOS::"))throw new Error(`Domain separator needs to start with 'APTOS::'.  Provided - ${e}`);t.update(e);let r=t.digest(),i=n,o=new Uint8Array(r.length+i.length);return o.set(r),o.set(i,r.length),o}function lA(n){return ft(n.bcsToBytes(),`APTOS::${n.constructor.name}`)}function De(n){let e=fr(n);return n.feePayerAddress?ft(e.bcsToBytes(),oi):n.secondarySignerAddresses?ft(e.bcsToBytes(),oi):ft(e.bcsToBytes(),Po)}var se=class n{constructor(e){this.signingScheme=0;let{privateKey:t,address:r}=e;this.privateKey=t,this.publicKey=t.publicKey(),this.accountAddress=r?_chunkZMDE3DNLjs.Y.from(r):this.publicKey.authKey().derivedAddress()}static generate(){let e=q.generate();return new n({privateKey:e})}static fromDerivationPath(e){let{path:t,mnemonic:r}=e,i=q.fromDerivationPath(t,r);return new n({privateKey:i})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new ke(this.publicKey,this.privateKey.sign(e))}signTransactionWithAuthenticator(e){return new ke(this.publicKey,this.signTransaction(e))}sign(e){return this.privateKey.sign(e)}signTransaction(e){return this.sign(De(e))}};function Ai(n){return typeof n=="object"&&n!==null&&"getAnyPublicKey"in n&&typeof n.getAnyPublicKey=="function"}var Ee=class n{constructor(e){this.signingScheme=2;let{privateKey:t,address:r}=e;this.privateKey=t,this.publicKey=new N(t.publicKey()),this.accountAddress=r?_chunkZMDE3DNLjs.Y.from(r):this.publicKey.authKey().derivedAddress()}getAnyPublicKey(){return this.publicKey}static generate(e={}){let{scheme:t=0}=e,r;switch(t){case 0:r=q.generate();break;case 2:r=Qe.generate();break;default:throw new Error(`Unsupported signature scheme ${t}`)}return new n({privateKey:r})}static fromDerivationPath(e){let{scheme:t=0,path:r,mnemonic:i}=e,o;switch(t){case 0:o=q.fromDerivationPath(r,i);break;case 2:o=Qe.fromDerivationPath(r,i);break;default:throw new Error(`Unsupported signature scheme ${t}`)}return new n({privateKey:o})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new oe(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new oe(this.publicKey,this.signTransaction(e))}sign(e){return new O(this.privateKey.sign(e))}signTransaction(e){return this.sign(De(e))}static fromEd25519Account(e){return new n({privateKey:e.privateKey,address:e.accountAddress})}};var rt=class{static generate(e={}){let{scheme:t=0,legacy:r=!0}=e;return t===0&&r?se.generate():Ee.generate({scheme:t})}static fromPrivateKey(e){let{privateKey:t,address:r,legacy:i=!0}=e;return t instanceof q&&i?new se({privateKey:t,address:r}):new Ee({privateKey:t,address:r})}static fromPrivateKeyAndAddress(e){return this.fromPrivateKey(e)}static fromDerivationPath(e){let{scheme:t=0,mnemonic:r,path:i,legacy:o=!0}=e;return t===0&&o?se.fromDerivationPath({mnemonic:r,path:i}):Ee.fromDerivationPath({scheme:t,mnemonic:r,path:i})}static authKey(e){let{publicKey:t}=e;return t.authKey()}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync(e)}};var _utils3 = require('@noble/hashes/utils');var du=1209600,tn=class tn extends _chunkZMDE3DNLjs.l{constructor(e){super();let{privateKey:t,expiryDateSecs:r,blinder:i}=e;this.privateKey=t,this.publicKey=new gt(t.publicKey()),this.expiryDateSecs=r||_chunkZMDE3DNLjs.da.call(void 0, _chunkZMDE3DNLjs.ca.call(void 0, )+du),this.blinder=i!==void 0?_chunkZMDE3DNLjs.j.fromHexInput(i).toUint8Array():lu();let o=fn(this.publicKey.bcsToBytes(),93);o.push(BigInt(this.expiryDateSecs)),o.push(_t(this.blinder));let s=xt(o);this.nonce=s.toString()}getPublicKey(){return this.publicKey}isExpired(){return Math.floor(Date.now()/1e3)>this.expiryDateSecs}serialize(e){e.serializeU32AsUleb128(this.publicKey.variant),e.serializeBytes(this.privateKey.toUint8Array()),e.serializeU64(this.expiryDateSecs),e.serializeFixedBytes(this.blinder)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=q.deserialize(e);break;default:throw new Error(`Unknown variant index for EphemeralPublicKey: ${t}`)}let i=e.deserializeU64(),o=e.deserializeFixedBytes(31);return new tn({privateKey:r,expiryDateSecs:Number(i),blinder:o})}static fromBytes(e){return tn.deserialize(new U(e))}static generate(e){let t;switch(_optionalChain([e, 'optionalAccess', _58 => _58.scheme])){case 0:default:t=q.generate()}return new tn({privateKey:t,expiryDateSecs:_optionalChain([e, 'optionalAccess', _59 => _59.expiryDateSecs])})}sign(e){if(this.isExpired())throw new Error("EphemeralKeyPair has expired");return new Ne(this.privateKey.sign(e))}};tn.BLINDER_LENGTH=31;var Ft=tn;function lu(){return _utils3.randomBytes.call(void 0, Ft.BLINDER_LENGTH)}var _eventemitter3 = require('eventemitter3'); var _eventemitter32 = _interopRequireDefault(_eventemitter3);function hr(n){return n!=null&&typeof n.checkKeylessAccountValidity=="function"}var nn=class nn extends _chunkZMDE3DNLjs.l{constructor(t){super();this.signingScheme=2;let{address:r,ephemeralKeyPair:i,publicKey:o,uidKey:s,uidVal:a,aud:c,pepper:p,proof:y,proofFetchCallback:h,jwt:d,verificationKeyHash:l}=t;if(this.ephemeralKeyPair=i,this.publicKey=o,this.accountAddress=r?_chunkZMDE3DNLjs.Y.from(r):this.publicKey.authKey().derivedAddress(),this.uidKey=s,this.uidVal=a,this.aud=c,this.jwt=d,this.emitter=new _eventemitter32.default,this.proofOrPromise=y,y instanceof ue)this.proof=y;else{if(h===void 0)throw new Error("Must provide callback for async proof fetch");this.emitter.on("proofFetchFinish",async R=>{await h(R),this.emitter.removeAllListeners()}),this.init(y)}let b=_chunkZMDE3DNLjs.j.fromHexInput(p).toUint8Array();if(b.length!==nn.PEPPER_LENGTH)throw new Error(`Pepper length in bytes should be ${nn.PEPPER_LENGTH}`);if(this.pepper=b,l!==void 0){if(_chunkZMDE3DNLjs.j.hexInputToUint8Array(l).length!==32)throw new Error("verificationKeyHash must be 32 bytes");this.verificationKeyHash=_chunkZMDE3DNLjs.j.hexInputToUint8Array(l)}}getAnyPublicKey(){return new N(this.publicKey)}async init(t){try{this.proof=await t,this.emitter.emit("proofFetchFinish",{status:"Success"})}catch(r){r instanceof Error?this.emitter.emit("proofFetchFinish",{status:"Failed",error:r.toString()}):this.emitter.emit("proofFetchFinish",{status:"Failed",error:"Unknown"})}}serialize(t){if(this.accountAddress.serialize(t),t.serializeStr(this.jwt),t.serializeStr(this.uidKey),t.serializeFixedBytes(this.pepper),this.ephemeralKeyPair.serialize(t),this.proof===void 0)throw new Error("Cannot serialize - proof undefined");this.proof.serialize(t),t.serializeOption(this.verificationKeyHash,32)}static partialDeserialize(t){let r=_chunkZMDE3DNLjs.Y.deserialize(t),i=t.deserializeStr(),o=t.deserializeStr(),s=t.deserializeFixedBytes(31),a=Ft.deserialize(t),c=ue.deserialize(t),p=t.deserializeOption("fixedBytes",32);return{address:r,jwt:i,uidKey:o,pepper:s,ephemeralKeyPair:a,proof:c,verificationKeyHash:p}}isExpired(){return this.ephemeralKeyPair.isExpired()}signWithAuthenticator(t){let r=new O(this.sign(t)),i=new N(this.publicKey);return new oe(i,r)}signTransactionWithAuthenticator(t){let r=new O(this.signTransaction(t)),i=new N(this.publicKey);return new oe(i,r)}async waitForProofFetch(){this.proofOrPromise instanceof Promise&&await this.proofOrPromise}async checkKeylessAccountValidity(t){if(this.isExpired())throw P.fromErrorType({type:0});if(await this.waitForProofFetch(),this.proof===void 0)throw P.fromErrorType({type:2});let r=_jwtdecode.jwtDecode.call(void 0, this.jwt,{header:!0});if(r.kid===void 0)throw P.fromErrorType({type:12,details:"checkKeylessAccountValidity failed. JWT is missing 'kid' in header. This should never happen."});if(this.verificationKeyHash!==void 0){let{verificationKey:i}=await Jt({aptosConfig:t});if(_chunkZMDE3DNLjs.j.hexInputToString(i.hash())!==_chunkZMDE3DNLjs.j.hexInputToString(this.verificationKeyHash))throw P.fromErrorType({type:4})}else console.warn("[Aptos SDK] The verification key hash was not set. Proof may be invalid if the verification key has rotated.");await nn.fetchJWK({aptosConfig:t,publicKey:this.publicKey,kid:r.kid})}sign(t){let{expiryDateSecs:r}=this.ephemeralKeyPair;if(this.isExpired())throw P.fromErrorType({type:0});if(this.proof===void 0)throw P.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let i=this.ephemeralKeyPair.getPublicKey(),o=this.ephemeralKeyPair.sign(t);return new ie({jwtHeader:_chunkZMDE3DNLjs.ea.call(void 0, this.jwt.split(".")[0]),ephemeralCertificate:new Qt(this.proof,0),expiryDateSecs:r,ephemeralPublicKey:i,ephemeralSignature:o})}signTransaction(t){if(this.proof===void 0)throw P.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let r=fr(t),o=new Ar(r,this.proof.proof).hash();return this.sign(o)}getSigningMessage(t){if(this.proof===void 0)throw P.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let r=fr(t);return new Ar(r,this.proof.proof).hash()}verifySignature(t){return this.publicKey.verifySignature(t)}async verifySignatureAsync(t){return this.publicKey.verifySignatureAsync({...t})}static async fetchJWK(t){return fi(t)}};nn.PEPPER_LENGTH=31;var me=nn,Ar= exports.TransactionAndProof =class extends _chunkZMDE3DNLjs.l{constructor(t,r){super();this.domainSeparator="APTOS::TransactionAndProof";this.transaction=t,this.proof=r}serialize(t){t.serializeFixedBytes(this.transaction.bcsToBytes()),t.serializeOption(this.proof)}hash(){return ft(this.bcsToBytes(),this.domainSeparator)}};var it=class n extends me{constructor(e){let t=K.create(e);super({publicKey:t,...e}),this.publicKey=t}serialize(e){super.serialize(e)}static deserialize(e){let{address:t,proof:r,ephemeralKeyPair:i,jwt:o,uidKey:s,pepper:a,verificationKeyHash:c}=me.partialDeserialize(e),{iss:p,aud:y,uidVal:h}=We({jwt:o,uidKey:s});return new n({address:t,proof:r,ephemeralKeyPair:i,iss:p,uidKey:s,uidVal:h,aud:y,pepper:a,jwt:o,verificationKeyHash:c})}static fromBytes(e){return n.deserialize(new U(_chunkZMDE3DNLjs.j.hexInputToUint8Array(e)))}static create(e){let{address:t,proof:r,jwt:i,ephemeralKeyPair:o,pepper:s,uidKey:a="sub",proofFetchCallback:c,verificationKey:p}=e,{iss:y,aud:h,uidVal:d}=We({jwt:i,uidKey:a});return new n({address:t,proof:r,ephemeralKeyPair:o,iss:y,uidKey:a,uidVal:d,aud:h,pepper:s,jwt:i,proofFetchCallback:c,verificationKeyHash:p?p.hash():void 0})}};var Gt=class n extends me{constructor(e){let t=$.create(e);super({publicKey:t,...e}),this.publicKey=t,this.audless=_nullishCoalesce(e.audless, () => (!1))}serialize(e){super.serialize(e),this.publicKey.jwkAddress.serialize(e)}static deserialize(e){let{address:t,proof:r,ephemeralKeyPair:i,jwt:o,uidKey:s,pepper:a,verificationKeyHash:c}=me.partialDeserialize(e),p=_chunkZMDE3DNLjs.Y.deserialize(e),{iss:y,aud:h,uidVal:d}=We({jwt:o,uidKey:s});return new n({address:t,proof:r,ephemeralKeyPair:i,iss:y,uidKey:s,uidVal:d,aud:h,pepper:a,jwt:o,verificationKeyHash:c,jwkAddress:p})}static fromBytes(e){return n.deserialize(U.fromHex(e))}static create(e){let{address:t,proof:r,jwt:i,ephemeralKeyPair:o,pepper:s,jwkAddress:a,uidKey:c="sub",proofFetchCallback:p,verificationKey:y}=e,{iss:h,aud:d,uidVal:l}=We({jwt:i,uidKey:c});return new n({address:t,proof:r,ephemeralKeyPair:o,iss:h,uidKey:c,uidVal:l,aud:d,pepper:s,jwkAddress:_chunkZMDE3DNLjs.Y.from(a),jwt:i,proofFetchCallback:p,verificationKeyHash:y?y.hash():void 0})}};var Cn=class n{constructor(e){this.signingScheme=3;let{multiKey:t,address:r}=e,i=e.signers.map(a=>a instanceof se?Ee.fromEd25519Account(a):a);if(t.signaturesRequired>i.length)throw new Error(`Not enough signers provided to satisfy the required signatures. Need ${t.signaturesRequired} signers, but only ${i.length} provided`);if(t.signaturesRequired<i.length)throw new Error(`More signers provided than required. Need ${t.signaturesRequired} signers, but ${i.length} provided`);this.publicKey=t,this.accountAddress=r?_chunkZMDE3DNLjs.Y.from(r):this.publicKey.authKey().derivedAddress();let o=[];for(let a of i)o.push(this.publicKey.getIndex(a.getAnyPublicKey()));let s=i.map((a,c)=>[a,o[c]]);s.sort((a,c)=>a[1]-c[1]),this.signers=s.map(a=>a[0]),this.signerIndicies=s.map(a=>a[1]),this.signaturesBitmap=this.publicKey.createBitmap({bits:o})}static fromPublicKeysAndSigners(e){let{address:t,publicKeys:r,signaturesRequired:i,signers:o}=e,s=new Te({publicKeys:r,signaturesRequired:i});return new n({multiKey:s,signers:o,address:t})}static isMultiKeySigner(e){return e instanceof n}signWithAuthenticator(e){return new Me(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new Me(this.publicKey,this.signTransaction(e))}async waitForProofFetch(){let t=this.signers.filter(r=>r instanceof me).map(async r=>r.waitForProofFetch());await Promise.all(t)}async checkKeylessAccountValidity(e){let r=this.signers.filter(i=>i instanceof me).map(i=>i.checkKeylessAccountValidity(e));await Promise.all(r)}sign(e){let t=[];for(let r of this.signers)t.push(r.sign(e));return new be({signatures:t,bitmap:this.signaturesBitmap})}signTransaction(e){let t=[];for(let r of this.signers)t.push(r.signTransaction(e));return new be({signatures:t,bitmap:this.signaturesBitmap})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return await this.publicKey.verifySignatureAsync(e)}};var Tr=class{constructor(e){this.signingScheme=1;let{signers:t,publicKey:r,address:i}=e;if(this.publicKey=r,this.accountAddress=i?_chunkZMDE3DNLjs.Y.from(i):this.publicKey.authKey().derivedAddress(),r.threshold>t.length)throw new Error(`Not enough signers provided to satisfy the required signatures. Need ${r.threshold} signers, but only ${t.length} provided`);if(r.threshold<t.length)throw new Error(`More signers provided than required. Need ${r.threshold} signers, but ${t.length} provided`);let o=[];for(let a of t)o.push(this.publicKey.getIndex(a.publicKey()));let s=t.map((a,c)=>[a,o[c]]);s.sort((a,c)=>a[1]-c[1]),this.signers=s.map(a=>a[0]),this.signerIndices=s.map(a=>a[1]),this.signaturesBitmap=this.publicKey.createBitmap({bits:o})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new Je(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new Je(this.publicKey,this.signTransaction(e))}sign(e){let t=[];for(let r of this.signers)t.push(r.sign(e));return new Ue({signatures:t,bitmap:this.signaturesBitmap})}signTransaction(e){return this.sign(De(e))}};function zo(n){let e=n.deserializeUleb128AsU32();if(!Object.values(_chunkZMDE3DNLjs.U).includes(e))throw new Error(`Deserialization of Account failed: SigningScheme variant ${e} is invalid`);return{address:_chunkZMDE3DNLjs.Y.deserialize(n),signingScheme:e}}function No(n,e){if(e.serializeStr(n.jwt),e.serializeStr(n.uidKey),e.serializeFixedBytes(n.pepper),n.ephemeralKeyPair.serialize(e),n.proof===void 0)throw new Error("Cannot serialize - proof undefined");n.proof.serialize(e),e.serializeOption(n.verificationKeyHash,32)}function Oo(n){let e=n.deserializeStr(),t=n.deserializeStr(),r=n.deserializeFixedBytes(31),i=Ft.deserialize(n),o=ue.deserialize(n),s=n.deserializeOption("fixedBytes",32);return{jwt:e,uidKey:t,pepper:r,ephemeralKeyPair:i,proof:o,verificationKeyHash:s}}var Fo;(h=>{function n(d){let l=new _chunkZMDE3DNLjs.m;switch(l.serializeU32AsUleb128(d.signingScheme),d.accountAddress.serialize(l),d.signingScheme){case 0:return d.privateKey.serialize(l),l.toUint8Array();case 2:{if(!Ai(d))throw new Error("Account is not a SingleKeySigner");let b=d.getAnyPublicKey();switch(l.serializeU32AsUleb128(b.variant),b.variant){case 3:return No(d,l),l.toUint8Array();case 4:{let R=d;return No(R,l),R.publicKey.jwkAddress.serialize(l),l.serializeBool(R.audless),l.toUint8Array()}case 1:case 0:return d.privateKey.serialize(l),l.toUint8Array();default:throw new Error(`Invalid public key variant: ${b.variant}`)}}case 3:{let b=d;return b.publicKey.serialize(l),l.serializeU32AsUleb128(b.signers.length),b.signers.forEach(R=>{l.serializeFixedBytes(n(R))}),l.toUint8Array()}default:throw new Error(`Deserialization of Account failed: invalid signingScheme value ${d.signingScheme}`)}}h.toBytes=n;function e(d){return _chunkZMDE3DNLjs.j.hexInputToStringWithoutPrefix(n(d))}h.toHexStringWithoutPrefix=e;function t(d){return _chunkZMDE3DNLjs.j.hexInputToString(n(d))}h.toHexString=t;function r(d){let{address:l,signingScheme:b}=zo(d);switch(b){case 0:{let R=q.deserialize(d);return new se({privateKey:R,address:l})}case 2:{let R=d.deserializeUleb128AsU32();switch(R){case 0:{let Q=q.deserialize(d);return new Ee({privateKey:Q,address:l})}case 1:{let Q=Qe.deserialize(d);return new Ee({privateKey:Q,address:l})}case 3:{let Q=Oo(d),He=We(Q);return new it({...Q,...He})}case 4:{let Q=Oo(d),He=_chunkZMDE3DNLjs.Y.deserialize(d),at=d.deserializeBool(),ct=We(Q);return new Gt({...Q,...ct,jwkAddress:He,audless:at})}default:throw new Error(`Unsupported public key variant ${R}`)}}case 3:{let R=Te.deserialize(d),Q=d.deserializeUleb128AsU32(),He=new Array;for(let at=0;at<Q;at+=1){let ct=r(d);if(!Ai(ct)&&!(ct instanceof se))throw new Error("Deserialization of MultiKeyAccount failed. Signer is not a SingleKeySigner or Ed25519Account");He.push(ct)}return new Cn({multiKey:R,signers:He,address:l})}default:throw new Error(`Deserialization of Account failed: invalid signingScheme value ${b}`)}}h.deserialize=r;function i(d){let l=p(d);if(!(l instanceof it))throw new Error("Deserialization of KeylessAccount failed");return l}h.keylessAccountFromHex=i;function o(d){let l=p(d);if(!(l instanceof Gt))throw new Error("Deserialization of FederatedKeylessAccount failed");return l}h.federatedKeylessAccountFromHex=o;function s(d){let l=p(d);if(!(l instanceof Cn))throw new Error("Deserialization of MultiKeyAccount failed");return l}h.multiKeyAccountFromHex=s;function a(d){let l=p(d);if(!(l instanceof Ee))throw new Error("Deserialization of SingleKeyAccount failed");return l}h.singleKeyAccountFromHex=a;function c(d){let l=p(d);if(!(l instanceof se))throw new Error("Deserialization of Ed25519Account failed");return l}h.ed25519AccountFromHex=c;function p(d){return r(U.fromHex(d))}h.fromHex=p;function y(d){return p(d)}h.fromBytes=y})(Fo||(Fo= exports.AccountUtils ={}));var br=class n extends rt{constructor({signer:t,accountAddress:r,authenticationFunction:i}){super();this.signingScheme=2;if(!_chunkZMDE3DNLjs.la.call(void 0, i))throw new Error(`Invalid authentication function ${i} passed into AbstractedAccount`);this.authenticationFunction=i,this.accountAddress=r,this.publicKey=new Zn(this.accountAddress),this.sign=o=>new mn(t(o))}static fromPermissionedSigner({signer:t,accountAddress:r}){return new n({signer:i=>{let o=new _chunkZMDE3DNLjs.m;return t.publicKey.serialize(o),t.sign(i).serialize(o),o.toUint8Array()},accountAddress:_nullishCoalesce(r, () => (t.accountAddress)),authenticationFunction:"0x1::permissioned_delegation::authenticate"})}signWithAuthenticator(t){return new Ct(this.authenticationFunction,_sha3.sha3_256.call(void 0, t),this.sign(_sha3.sha3_256.call(void 0, t)).toUint8Array())}signTransactionWithAuthenticator(t){return this.signWithAuthenticator(De(t))}signTransaction(t){return this.sign(De(t))}setSigner(t){this.sign=r=>new mn(t(r))}};var Kn=class Kn extends br{constructor({signer:e,authenticationFunction:t,abstractPublicKey:r}){let i=new (0, _chunkZMDE3DNLjs.Y)(Kn.computeAccountAddress(t,r));super({accountAddress:i,signer:e,authenticationFunction:t}),this.abstractPublicKey=r}static computeAccountAddress(e,t){if(!_chunkZMDE3DNLjs.la.call(void 0, e))throw new Error(`Invalid authentication function ${e} passed into DerivableAbstractedAccount`);let[r,i,o]=e.split("::"),s=_sha3.sha3_256.create(),a=new _chunkZMDE3DNLjs.m;_chunkZMDE3DNLjs.Y.fromString(r).serialize(a),a.serializeStr(i),a.serializeStr(o),s.update(a.toUint8Array());let c=new _chunkZMDE3DNLjs.m;return c.serializeBytes(t),s.update(c.toUint8Array()),s.update(new Uint8Array([Kn.ADDRESS_DOMAIN_SEPERATOR])),s.digest()}signWithAuthenticator(e){return new Ct(this.authenticationFunction,_sha3.sha3_256.call(void 0, e),this.sign(_sha3.sha3_256.call(void 0, e)).value,this.abstractPublicKey)}};Kn.ADDRESS_DOMAIN_SEPERATOR=5;var Bo=Kn;var yu=`
    fragment TokenActivitiesFields on token_activities_v2 {
  after_value
  before_value
  entry_function_id_str
  event_account_address
  event_index
  from_address
  is_fungible_v2
  property_version_v1
  to_address
  token_amount
  token_data_id
  token_standard
  transaction_timestamp
  transaction_version
  type
}
    `,fu=`
    fragment AnsTokenFragment on current_aptos_names {
  domain
  expiration_timestamp
  registered_address
  subdomain
  token_standard
  is_primary
  owner_address
  subdomain_expiration_policy
  domain_expiration_timestamp
}
    `,wr=`
    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {
  token_standard
  token_properties_mutated_v1
  token_data_id
  table_type_v1
  storage_id
  property_version_v1
  owner_address
  last_transaction_version
  last_transaction_timestamp
  is_soulbound_v2
  is_fungible_v2
  amount
  current_token_data {
    collection_id
    description
    is_fungible_v2
    largest_property_version_v1
    last_transaction_timestamp
    last_transaction_version
    maximum
    supply
    token_data_id
    token_name
    token_properties
    token_standard
    token_uri
    decimals
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      max_supply
      mutable_description
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
  }
}
    `,Mo=`
    query getAccountCoinsCount($address: String) {
  current_fungible_asset_balances_aggregate(
    where: {owner_address: {_eq: $address}}
  ) {
    aggregate {
      count
    }
  }
}
    `,Vo=`
    query getAccountCoinsData($where_condition: current_fungible_asset_balances_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_fungible_asset_balances_order_by!]) {
  current_fungible_asset_balances(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    amount
    asset_type
    is_frozen
    is_primary
    last_transaction_timestamp
    last_transaction_version
    owner_address
    storage_id
    token_standard
    metadata {
      token_standard
      symbol
      supply_aggregator_table_key_v1
      supply_aggregator_table_handle_v1
      project_uri
      name
      last_transaction_version
      last_transaction_timestamp
      icon_uri
      decimals
      creator_address
      asset_type
    }
  }
}
    `,Ho=`
    query getAccountCollectionsWithOwnedTokens($where_condition: current_collection_ownership_v2_view_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_collection_ownership_v2_view_order_by!]) {
  current_collection_ownership_v2_view(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      mutable_description
      max_supply
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
    collection_id
    collection_name
    collection_uri
    creator_address
    distinct_tokens
    last_transaction_version
    owner_address
    single_token_uri
  }
}
    `,Lo=`
    query getAccountOwnedTokens($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${wr}`,rb=`
    query getAccountOwnedTokensByTokenData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${wr}`,qo=`
    query getAccountOwnedTokensFromCollection($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${wr}`,$o=`
    query getAccountTokensCount($where_condition: current_token_ownerships_v2_bool_exp, $offset: Int, $limit: Int) {
  current_token_ownerships_v2_aggregate(
    where: $where_condition
    offset: $offset
    limit: $limit
  ) {
    aggregate {
      count
    }
  }
}
    `,Wo=`
    query getAccountTransactionsCount($address: String) {
  account_transactions_aggregate(where: {account_address: {_eq: $address}}) {
    aggregate {
      count
    }
  }
}
    `,Qo=`
    query getChainTopUserTransactions($limit: Int) {
  user_transactions(limit: $limit, order_by: {version: desc}) {
    version
  }
}
    `,jo=`
    query getCollectionData($where_condition: current_collections_v2_bool_exp!) {
  current_collections_v2(where: $where_condition) {
    uri
    total_minted_v2
    token_standard
    table_handle_v1
    mutable_uri
    mutable_description
    max_supply
    collection_id
    collection_name
    creator_address
    current_supply
    description
    last_transaction_timestamp
    last_transaction_version
    cdn_asset_uris {
      cdn_image_uri
      asset_uri
      animation_optimizer_retry_count
      cdn_animation_uri
      cdn_json_uri
      image_optimizer_retry_count
      json_parser_retry_count
      raw_animation_uri
      raw_image_uri
    }
  }
}
    `,Jo=`
    query getCurrentFungibleAssetBalances($where_condition: current_fungible_asset_balances_bool_exp, $offset: Int, $limit: Int) {
  current_fungible_asset_balances(
    where: $where_condition
    offset: $offset
    limit: $limit
  ) {
    amount
    asset_type
    is_frozen
    is_primary
    last_transaction_timestamp
    last_transaction_version
    owner_address
    storage_id
    token_standard
  }
}
    `,Xo=`
    query getDelegatedStakingActivities($delegatorAddress: String, $poolAddress: String) {
  delegated_staking_activities(
    where: {delegator_address: {_eq: $delegatorAddress}, pool_address: {_eq: $poolAddress}}
  ) {
    amount
    delegator_address
    event_index
    event_type
    pool_address
    transaction_version
  }
}
    `,Yo=`
    query getEvents($where_condition: events_bool_exp, $offset: Int, $limit: Int, $order_by: [events_order_by!]) {
  events(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    account_address
    creation_number
    data
    event_index
    sequence_number
    transaction_block_height
    transaction_version
    type
    indexed_type
  }
}
    `,Zo=`
    query getFungibleAssetActivities($where_condition: fungible_asset_activities_bool_exp, $offset: Int, $limit: Int) {
  fungible_asset_activities(
    where: $where_condition
    offset: $offset
    limit: $limit
  ) {
    amount
    asset_type
    block_height
    entry_function_id_str
    event_index
    gas_fee_payer_address
    is_frozen
    is_gas_fee
    is_transaction_success
    owner_address
    storage_id
    storage_refund_amount
    token_standard
    transaction_timestamp
    transaction_version
    type
  }
}
    `,es=`
    query getFungibleAssetMetadata($where_condition: fungible_asset_metadata_bool_exp, $offset: Int, $limit: Int) {
  fungible_asset_metadata(where: $where_condition, offset: $offset, limit: $limit) {
    icon_uri
    project_uri
    supply_aggregator_table_handle_v1
    supply_aggregator_table_key_v1
    creator_address
    asset_type
    decimals
    last_transaction_timestamp
    last_transaction_version
    name
    symbol
    token_standard
    supply_v2
    maximum_v2
  }
}
    `,rn=`
    query getNames($offset: Int, $limit: Int, $where_condition: current_aptos_names_bool_exp, $order_by: [current_aptos_names_order_by!]) {
  current_aptos_names(
    limit: $limit
    where: $where_condition
    order_by: $order_by
    offset: $offset
  ) {
    ...AnsTokenFragment
  }
}
    ${fu}`,Ti=`
    query getNumberOfDelegators($where_condition: num_active_delegator_per_pool_bool_exp, $order_by: [num_active_delegator_per_pool_order_by!]) {
  num_active_delegator_per_pool(where: $where_condition, order_by: $order_by) {
    num_active_delegator
    pool_address
  }
}
    `,Sr=`
    query getObjectData($where_condition: current_objects_bool_exp, $offset: Int, $limit: Int, $order_by: [current_objects_order_by!]) {
  current_objects(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    allow_ungated_transfer
    state_key_hash
    owner_address
    object_address
    last_transaction_version
    last_guid_creation_num
    is_deleted
  }
}
    `,bi=`
    query getProcessorStatus($where_condition: processor_status_bool_exp) {
  processor_status(where: $where_condition) {
    last_success_version
    processor
    last_updated
  }
}
    `,ts=`
    query getTableItemsData($where_condition: table_items_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_items_order_by!]) {
  table_items(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    decoded_key
    decoded_value
    key
    table_handle
    transaction_version
    write_set_change_index
  }
}
    `,ns=`
    query getTableItemsMetadata($where_condition: table_metadatas_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_metadatas_order_by!]) {
  table_metadatas(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    handle
    key_type
    value_type
  }
}
    `,rs=`
    query getTokenActivity($where_condition: token_activities_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [token_activities_v2_order_by!]) {
  token_activities_v2(
    where: $where_condition
    order_by: $order_by
    offset: $offset
    limit: $limit
  ) {
    ...TokenActivitiesFields
  }
}
    ${yu}`,wi=`
    query getCurrentTokenOwnership($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {
  current_token_ownerships_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    ...CurrentTokenOwnershipFields
  }
}
    ${wr}`,is=`
    query getTokenData($where_condition: current_token_datas_v2_bool_exp, $offset: Int, $limit: Int, $order_by: [current_token_datas_v2_order_by!]) {
  current_token_datas_v2(
    where: $where_condition
    offset: $offset
    limit: $limit
    order_by: $order_by
  ) {
    collection_id
    description
    is_fungible_v2
    largest_property_version_v1
    last_transaction_timestamp
    last_transaction_version
    maximum
    supply
    token_data_id
    token_name
    token_properties
    token_standard
    token_uri
    decimals
    current_collection {
      collection_id
      collection_name
      creator_address
      current_supply
      description
      last_transaction_timestamp
      last_transaction_version
      max_supply
      mutable_description
      mutable_uri
      table_handle_v1
      token_standard
      total_minted_v2
      uri
    }
  }
}
    `;async function Er(n){let{aptosConfig:e}=n,{data:t}=await H({aptosConfig:e,originMethod:"getLedgerInfo",path:""});return t}async function os(n){let{aptosConfig:e,limit:t}=n;return(await w({aptosConfig:e,query:{query:Qo,variables:{limit:t}},originMethod:"getChainTopUserTransactions"})).user_transactions}async function w(n){let{aptosConfig:e,query:t,originMethod:r}=n,{data:i}=await vo({aptosConfig:e,originMethod:_nullishCoalesce(r, () => ("queryIndexer")),path:"",body:t,overrides:{WITH_CREDENTIALS:!1}});return i}async function Au(n){let{aptosConfig:e}=n;return(await w({aptosConfig:e,query:{query:bi},originMethod:"getProcessorStatuses"})).processor_status}async function Pr(n){let e=await Au({aptosConfig:n.aptosConfig});return BigInt(e[0].last_success_version)}async function _r(n){let{aptosConfig:e,processorType:t}=n;return(await w({aptosConfig:e,query:{query:bi,variables:{where_condition:{processor:{_eq:t}}}},originMethod:"getProcessorStatus"})).processor_status[0]}async function xr(n){let{aptosConfig:e,accountAddress:t}=n,{data:r}=await H({aptosConfig:e,originMethod:"getInfo",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}`});return r}async function as(n){return _optionalChain([n, 'access', _60 => _60.options, 'optionalAccess', _61 => _61.ledgerVersion])!==void 0?ss(n):Fe(async()=>ss(n),`module-${n.accountAddress}-${n.moduleName}`,1e3*60*5)()}async function ss(n){let{aptosConfig:e,accountAddress:t,moduleName:r,options:i}=n,{data:o}=await H({aptosConfig:e,originMethod:"getModule",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/module/${r}`,params:{ledger_version:_optionalChain([i, 'optionalAccess', _62 => _62.ledgerVersion])}});return o}async function Ir(n){let{aptosConfig:e,handle:t,data:r,options:i}=n;return(await mt({aptosConfig:e,originMethod:"getTableItem",path:`tables/${t}/item`,params:{ledger_version:_optionalChain([i, 'optionalAccess', _63 => _63.ledgerVersion])},body:r})).data}async function cs(n){let{aptosConfig:e,options:t}=n,r={query:ts,variables:{where_condition:_optionalChain([t, 'optionalAccess', _64 => _64.where]),offset:_optionalChain([t, 'optionalAccess', _65 => _65.offset]),limit:_optionalChain([t, 'optionalAccess', _66 => _66.limit]),order_by:_optionalChain([t, 'optionalAccess', _67 => _67.orderBy])}};return(await w({aptosConfig:e,query:r,originMethod:"getTableItemsData"})).table_items}async function us(n){let{aptosConfig:e,options:t}=n,r={query:ns,variables:{where_condition:_optionalChain([t, 'optionalAccess', _68 => _68.where]),offset:_optionalChain([t, 'optionalAccess', _69 => _69.offset]),limit:_optionalChain([t, 'optionalAccess', _70 => _70.limit]),order_by:_optionalChain([t, 'optionalAccess', _71 => _71.orderBy])}};return(await w({aptosConfig:e,query:r,originMethod:"getTableItemsMetadata"})).table_metadatas}async function Ei(n){let{aptosConfig:e,options:t}=n;return ar({aptosConfig:e,originMethod:"getTransactions",path:"transactions",params:{start:_optionalChain([t, 'optionalAccess', _72 => _72.offset]),limit:_optionalChain([t, 'optionalAccess', _73 => _73.limit])}})}async function Cr(n){let{aptosConfig:e}=n;return Fe(async()=>{let{data:t}=await H({aptosConfig:e,originMethod:"getGasPriceEstimation",path:"estimate_gas_price"});return t},`gas-price-${e.network}`,1e3*60*5)()}async function ps(n){let{aptosConfig:e,ledgerVersion:t}=n,{data:r}=await H({aptosConfig:e,originMethod:"getTransactionByVersion",path:`transactions/by_version/${t}`});return r}async function Rn(n){let{aptosConfig:e,transactionHash:t}=n,{data:r}=await H({aptosConfig:e,path:`transactions/by_hash/${t}`,originMethod:"getTransactionByHash"});return r}async function ds(n){let{aptosConfig:e,transactionHash:t}=n;try{return(await Rn({aptosConfig:e,transactionHash:t})).type==="pending_transaction"}catch(r){if(_optionalChain([r, 'optionalAccess', _74 => _74.status])===404)return!0;throw r}}async function hu(n){let{aptosConfig:e,transactionHash:t}=n,{data:r}=await H({aptosConfig:e,path:`transactions/wait_by_hash/${t}`,originMethod:"longWaitForTransaction"});return r}async function Tt(n){let{aptosConfig:e,transactionHash:t,options:r}=n,i=_nullishCoalesce(_optionalChain([r, 'optionalAccess', _75 => _75.timeoutSecs]), () => (20)),o=_nullishCoalesce(_optionalChain([r, 'optionalAccess', _76 => _76.checkSuccess]), () => (!0)),s=!0,a=0,c,p,y=200,h=1.5;function d(l){if(!(l instanceof he)||(p=l,l.status!==404&&l.status>=400&&l.status<500))throw l}try{c=await Rn({aptosConfig:e,transactionHash:t}),s=c.type==="pending_transaction"}catch(l){d(l)}if(s){let l=Date.now();try{c=await hu({aptosConfig:e,transactionHash:t}),s=c.type==="pending_transaction"}catch(b){d(b)}a=(Date.now()-l)/1e3}for(;s&&!(a>=i);){try{if(c=await Rn({aptosConfig:e,transactionHash:t}),s=c.type==="pending_transaction",!s)break}catch(l){d(l)}await _chunkZMDE3DNLjs.aa.call(void 0, y),a+=y/1e3,y*=h}if(c===void 0)throw p||new vr(`Fetching transaction ${t} failed and timed out after ${i} seconds`,c);if(c.type==="pending_transaction")throw new vr(`Transaction ${t} timed out in pending state after ${i} seconds`,c);if(!o)return c;if(!c.success)throw new Si(`Transaction ${t} failed with an error: ${c.vm_status}`,c);return c}async function Kr(n){let{aptosConfig:e,processorType:t}=n,r=BigInt(n.minimumLedgerVersion),i=3e3,o=new Date().getTime(),s=BigInt(-1);for(;s<r;){if(new Date().getTime()-o>i)throw new Error("waitForLastSuccessIndexerVersionSync timeout");if(t===void 0?s=await Pr({aptosConfig:e}):s=(await _r({aptosConfig:e,processorType:t})).last_success_version,s>=r)break;await _chunkZMDE3DNLjs.aa.call(void 0, 200)}}var vr=class extends Error{constructor(e,t){super(e),this.lastSubmittedTransaction=t}},Si=class extends Error{constructor(e,t){super(e),this.transaction=t}};async function ls(n){let{aptosConfig:e,ledgerVersion:t,options:r}=n,{data:i}=await H({aptosConfig:e,originMethod:"getBlockByVersion",path:`blocks/by_version/${t}`,params:{with_transactions:_optionalChain([r, 'optionalAccess', _77 => _77.withTransactions])}});return ms({block:i,...n})}async function gs(n){let{aptosConfig:e,blockHeight:t,options:r}=n,{data:i}=await H({aptosConfig:e,originMethod:"getBlockByHeight",path:`blocks/by_height/${t}`,params:{with_transactions:_optionalChain([r, 'optionalAccess', _78 => _78.withTransactions])}});return ms({block:i,...n})}async function ms(n){let{aptosConfig:e,block:t,options:r}=n;if(_optionalChain([r, 'optionalAccess', _79 => _79.withTransactions])){t.transactions=_nullishCoalesce(t.transactions, () => ([]));let i=t.transactions[t.transactions.length-1],o=BigInt(t.first_version),s=BigInt(t.last_version),a=_optionalChain([i, 'optionalAccess', _80 => _80.version]),c;if(a===void 0?c=o-1n:c=BigInt(a),c===s)return t;let p=[],y=100n;for(let d=c+1n;d<s;d+=BigInt(100))p.push(Ei({aptosConfig:e,options:{offset:d,limit:Math.min(Number(y),Number(s-d+1n))}}));let h=await Promise.all(p);for(let d of h)t.transactions.push(...d)}return t}function ys(n){return!!n.match(/^[_a-zA-Z0-9]+$/)}function fs(n){return!!n.match(/\s/)}function Tu(n){return!!n.match(/^T[0-9]+$/)}function bu(n){return!!n.match(/^&.+$/)}function wu(n){switch(n){case"signer":case"address":case"bool":case"u8":case"u16":case"u32":case"u64":case"u128":case"u256":return!0;default:return!1}}function Su(n,e){let t=e;for(;t<n.length;t+=1){let r=n[t];if(!fs(r))break}return t}var Eu=(b=>(b.InvalidTypeTag="unknown type",b.UnexpectedGenericType="unexpected generic type",b.UnexpectedTypeArgumentClose="unexpected '>'",b.UnexpectedWhitespaceCharacter="unexpected whitespace character",b.UnexpectedComma="unexpected ','",b.TypeArgumentCountMismatch="type argument count doesn't match expected amount",b.MissingTypeArgumentClose="no matching '>' for '<'",b.MissingTypeArgument="no type argument before ','",b.UnexpectedPrimitiveTypeArguments="primitive types not expected to have type arguments",b.UnexpectedVectorTypeArgumentCount="vector type expected to have exactly one type argument",b.UnexpectedStructFormat="unexpected struct format, must be of the form 0xaddress::module_name::struct_name",b.InvalidModuleNameCharacter="module name must only contain alphanumeric or '_' characters",b.InvalidStructNameCharacter="struct name must only contain alphanumeric or '_' characters",b.InvalidAddress="struct address must be valid",b))(Eu||{}),W= exports.TypeTagParserError =class extends Error{constructor(e,t){super(`Failed to parse typeTag '${e}', ${t}`)}};function ze(n,e){let t=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _81 => _81.allowGenerics]), () => (!1)),r=[],i=[],o=[],s=0,a="",c=1;for(;s<n.length;){let p=n[s];if(p==="<")r.push({savedExpectedTypes:c,savedStr:a,savedTypes:o}),a="",o=[],c=1;else if(p===">"){if(a!==""){let b=Un(a,i,t);o.push(b)}let y=r.pop();if(y===void 0)throw new W(n,"unexpected '>'");if(c!==o.length)throw new W(n,"type argument count doesn't match expected amount");let{savedStr:h,savedTypes:d,savedExpectedTypes:l}=y;i=o,o=d,a=h,c=l}else if(p===","){if(r.length===0)throw new W(n,"unexpected ','");if(a.length===0)throw new W(n,"no type argument before ','");let y=Un(a,i,t);i=[],o.push(y),a="",c+=1}else if(fs(p)){let y=!1;if(a.length!==0){let d=Un(a,i,t);i=[],o.push(d),a="",y=!0}s=Su(n,s);let h=n[s];if(s<n.length&&y&&h!==","&&h!==">")throw new W(n,"unexpected whitespace character");continue}else a+=p;s+=1}if(r.length>0)throw new W(n,"no matching '>' for '<'");switch(o.length){case 0:return Un(a,i,t);case 1:if(a==="")return o[0];throw new W(n,"unexpected ','");default:throw new W(n,"unexpected whitespace character")}}function Un(n,e,t){let r=n.trim(),i=r.toLowerCase();if(wu(i)&&e.length>0)throw new W(n,"primitive types not expected to have type arguments");switch(r.toLowerCase()){case"signer":return new Kt;case"bool":return new L;case"address":return new F;case"u8":return new le;case"u16":return new Xe;case"u32":return new Ye;case"u64":return new X;case"u128":return new Ze;case"u256":return new et;case"vector":if(e.length!==1)throw new W(n,"vector type expected to have exactly one type argument");return new C(e[0]);default:if(bu(r)){let a=r.substring(1);return new dr(Un(a,e,t))}if(Tu(r)){if(t)return new B(Number(r.split("T")[1]));throw new W(n,"unexpected generic type")}if(!r.match(/:/))throw new W(n,"unknown type");let o=r.split("::");if(o.length!==3)throw new W(n,"unexpected struct format, must be of the form 0xaddress::module_name::struct_name");let s;try{s=_chunkZMDE3DNLjs.Y.fromString(o[0])}catch (e4){throw new W(n,"struct address must be valid")}if(!ys(o[1]))throw new W(n,"module name must only contain alphanumeric or '_' characters");if(!ys(o[2]))throw new W(n,"struct name must only contain alphanumeric or '_' characters");return new A(new Ve(s,new k(o[1]),new k(o[2]),e))}}function As(n){return typeof n=="boolean"}function ot(n){return typeof n=="string"}function Pu(n){return typeof n=="number"}function Rr(n){if(Pu(n))return n;if(ot(n)&&n!=="")return Number.parseInt(n,10)}function Ur(n){return typeof n=="number"||typeof n=="bigint"||typeof n=="string"}function hs(n){return n==null}function Ts(n){return Pi(n)||xi(n)||Ii(n)||vi(n)||Ci(n)||Ki(n)||Ri(n)||kr(n)||_i(n)||_u(n)||n instanceof S||n instanceof Z}function Pi(n){return n instanceof z}function kr(n){return n instanceof _chunkZMDE3DNLjs.Y}function _i(n){return n instanceof x}function _u(n){return n instanceof _e}function xi(n){return n instanceof Y}function Ii(n){return n instanceof xe}function vi(n){return n instanceof Ie}function Ci(n){return n instanceof j}function Ki(n){return n instanceof ve}function Ri(n){return n instanceof fe}function bs(n){return"bytecode"in n}function D(n,e){throw new Error(`Type mismatch for argument ${e}, expected '${n}'`)}function ws(n){let e=n.params.findIndex(t=>t!=="signer"&&t!=="&signer");return e<0?n.params.length:e}var xu=new TextEncoder;function Dr(n){return _nullishCoalesce(_optionalChain([n, 'optionalAccess', _82 => _82.map, 'call', _83 => _83(e=>ot(e)?ze(e):e)]), () => ([]))}async function Iu(n,e,t){return(await zr({aptosConfig:t,accountAddress:n,moduleName:e})).abi}async function ki(n,e,t,r){let i=await Iu(n,e,r);if(!i)throw new Error(`Could not find module ABI for '${n}::${e}'`);return i.exposed_functions.find(o=>o.name===t)}async function $b(n,e,t,r){let i=await ki(n,e,t,r);if(!i)throw new Error(`Could not find function ABI for '${n}::${e}::${t}'`);let o=[];for(let s=0;s<i.params.length;s+=1)o.push(ze(i.params[s],{allowGenerics:!0}));return{typeParameters:i.generic_type_params,parameters:o}}async function Ss(n,e,t,r){let i=await ki(n,e,t,r);if(!i)throw new Error(`Could not find entry function ABI for '${n}::${e}::${t}'`);if(!i.is_entry)throw new Error(`'${n}::${e}::${t}' is not an entry function`);let o=ws(i),s=[];for(let a=o;a<i.params.length;a+=1)s.push(ze(i.params[a],{allowGenerics:!0}));return{signers:o,typeParameters:i.generic_type_params,parameters:s}}async function Es(n,e,t,r){let i=await ki(n,e,t,r);if(!i)throw new Error(`Could not find view function ABI for '${n}::${e}::${t}'`);if(!i.is_view)throw new Error(`'${n}::${e}::${t}' is not an view function`);let o=[];for(let a=0;a<i.params.length;a+=1)o.push(ze(i.params[a],{allowGenerics:!0}));let s=[];for(let a=0;a<i.return.length;a+=1)s.push(ze(i.return[a],{allowGenerics:!0}));return{typeParameters:i.generic_type_params,parameters:o,returnTypes:s}}function Di(n,e,t,r,i,o){let s;if("exposed_functions"in e){let a=e.exposed_functions.find(c=>c.name===n);if(!a)throw new Error(`Could not find function ABI for '${e.address}::${e.name}::${n}'`);if(r>=a.params.length)throw new Error(`Too many arguments for '${n}', expected ${a.params.length}`);s=ze(a.params[r],{allowGenerics:!0})}else{if(r>=e.parameters.length)throw new Error(`Too many arguments for '${n}', expected ${e.parameters.length}`);s=e.parameters[r]}return Bt(t,s,r,i,"exposed_functions"in e?e:void 0,o)}function Bt(n,e,t,r,i,o){return Ts(n)?(Ui(e,n,t),n):vu(n,e,t,r,i,o)}function vu(n,e,t,r,i,o){if(e.isBool()){if(As(n))return new z(n);if(ot(n)){if(n==="true")return new z(!0);if(n==="false")return new z(!1)}D("boolean",t)}if(e.isAddress()){if(ot(n))return _chunkZMDE3DNLjs.Y.fromString(n);D("string | AccountAddress",t)}if(e.isU8()){let s=Rr(n);if(s!==void 0)return new Y(s);D("number | string",t)}if(e.isU16()){let s=Rr(n);if(s!==void 0)return new xe(s);D("number | string",t)}if(e.isU32()){let s=Rr(n);if(s!==void 0)return new Ie(s);D("number | string",t)}if(e.isU64()){if(Ur(n))return new j(BigInt(n));D("bigint | number | string",t)}if(e.isU128()){if(Ur(n))return new ve(BigInt(n));D("bigint | number | string",t)}if(e.isU256()){if(Ur(n))return new fe(BigInt(n));D("bigint | number | string",t)}if(e.isGeneric()){let s=e.value;if(s<0||s>=r.length)throw new Error(`Generic argument ${e.toString()} is invalid for argument ${t}`);return Bt(n,r[s],t,r,i)}if(e.isVector()){if(e.value.isU8()){if(ot(n))return S.U8(xu.encode(n));if(n instanceof Uint8Array)return S.U8(n);if(n instanceof ArrayBuffer)return S.U8(new Uint8Array(n))}if(ot(n)&&n.startsWith("["))return Bt(JSON.parse(n),e,t,r);if(Array.isArray(n))return new S(n.map(s=>Bt(s,e.value,t,r,i)));throw new Error(`Type mismatch for argument ${t}, type '${e.toString()}'`)}if(e.isStruct()){if(e.isString()){if(ot(n))return new x(n);D("string",t)}if(e.isObject()){if(ot(n))return _chunkZMDE3DNLjs.Y.fromString(n);D("string | AccountAddress",t)}if(e.isDelegationKey()||e.isRateLimiter()){if(n instanceof Uint8Array)return new _e(n);D("Uint8Array",t)}if(e.isOption()){if(hs(n)){let a=e.value.typeArgs[0];return a instanceof L?new Z(null):a instanceof F?new Z(null):a instanceof le?new Z(null):a instanceof Xe?new Z(null):a instanceof Ye?new Z(null):a instanceof X?new Z(null):a instanceof Ze?new Z(null):a instanceof et?new Z(null):new Z(null)}return new Z(Bt(n,e.value.typeArgs[0],t,r,i))}if(_optionalChain([i, 'optionalAccess', _84 => _84.structs, 'access', _85 => _85.find, 'call', _86 => _86(a=>a.name===e.value.name.identifier), 'optionalAccess', _87 => _87.fields, 'access', _88 => _88.length])===0&&n instanceof Uint8Array)return new _e(n);if(n instanceof Uint8Array&&_optionalChain([o, 'optionalAccess', _89 => _89.allowUnknownStructs]))return console.warn(`Unsupported struct input type for argument ${t}. Continuing since 'allowUnknownStructs' is enabled.`),new _e(n);throw new Error(`Unsupported struct input type for argument ${t}, type '${e.toString()}'`)}throw new Error(`Type mismatch for argument ${t}, type '${e.toString()}'`)}function Ui(n,e,t){if(n.isBool()){if(Pi(e))return;D("Bool",t)}if(n.isAddress()){if(kr(e))return;D("AccountAddress",t)}if(n.isU8()){if(xi(e))return;D("U8",t)}if(n.isU16()){if(Ii(e))return;D("U16",t)}if(n.isU32()){if(vi(e))return;D("U32",t)}if(n.isU64()){if(Ci(e))return;D("U64",t)}if(n.isU128()){if(Ki(e))return;D("U128",t)}if(n.isU256()){if(Ri(e))return;D("U256",t)}if(n.isVector()){if(e instanceof S){e.values.length>0&&Ui(n.value,e.values[0],t);return}D("MoveVector",t)}if(n instanceof A){if(n.isString()){if(_i(e))return;D("MoveString",t)}if(n.isObject()){if(kr(e))return;D("AccountAddress",t)}if(n.isOption()){if(e instanceof Z){e.value!==void 0&&Ui(n.value.typeArgs[0],e.value,t);return}D("MoveOption",t)}}throw new Error(`Type mismatch for argument ${t}, expected '${n.toString()}'`)}async function Nr(n){if(bs(n))return Uu(n);let{moduleAddress:e,moduleName:t,functionName:r}=_chunkZMDE3DNLjs.ka.call(void 0, n.function),i=await Is({key:"entry-function",moduleAddress:e,moduleName:t,functionName:r,aptosConfig:n.aptosConfig,abi:n.abi,fetch:Ss});return Ku({...n,abi:i})}function Ku(n){let e=n.abi,{moduleAddress:t,moduleName:r,functionName:i}=_chunkZMDE3DNLjs.ka.call(void 0, n.function),o=Dr(n.typeArguments);if(o.length!==e.typeParameters.length)throw new Error(`Type argument count mismatch, expected ${e.typeParameters.length}, received ${o.length}`);let s=n.functionArguments.map((c,p)=>Di(n.function,e,c,p,o));if(s.length!==e.parameters.length)throw new Error(`Too few arguments for '${t}::${r}::${i}', expected ${e.parameters.length} but got ${s.length}`);let a=Ut.build(`${t}::${r}`,i,o,s);if("multisigAddress"in n){let c=_chunkZMDE3DNLjs.Y.from(n.multisigAddress);return new _n(new In(c,new vn(a)))}return new Pn(a)}async function Ps(n){let{moduleAddress:e,moduleName:t,functionName:r}=_chunkZMDE3DNLjs.ka.call(void 0, n.function),i=await Is({key:"view-function",moduleAddress:e,moduleName:t,functionName:r,aptosConfig:n.aptosConfig,abi:n.abi,fetch:Es});return Ru({abi:i,...n})}function Ru(n){let e=n.abi,{moduleAddress:t,moduleName:r,functionName:i}=_chunkZMDE3DNLjs.ka.call(void 0, n.function),o=Dr(n.typeArguments);if(o.length!==e.typeParameters.length)throw new Error(`Type argument count mismatch, expected ${e.typeParameters.length}, received ${o.length}`);let s=_nullishCoalesce(_optionalChain([n, 'optionalAccess', _90 => _90.functionArguments, 'optionalAccess', _91 => _91.map, 'call', _92 => _92((a,c)=>Di(n.function,e,a,c,o))]), () => ([]));if(s.length!==e.parameters.length)throw new Error(`Too few arguments for '${t}::${r}::${i}', expected ${e.parameters.length} but got ${s.length}`);return Ut.build(`${t}::${r}`,i,o,s)}function Uu(n){return new En(new xn(_chunkZMDE3DNLjs.j.fromHexInput(n.bytecode).toUint8Array(),Dr(n.typeArguments),n.functionArguments))}async function ku(n){let{aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o}=n,s=async()=>ii[e.network]?{chainId:ii[e.network]}:{chainId:(await Er({aptosConfig:e})).chain_id},a=async()=>_optionalChain([i, 'optionalAccess', _93 => _93.gasUnitPrice])?{gasEstimate:i.gasUnitPrice}:{gasEstimate:(await Cr({aptosConfig:e})).gas_estimate},c=async()=>{let R=async()=>_optionalChain([i, 'optionalAccess', _94 => _94.accountSequenceNumber])!==void 0?i.accountSequenceNumber:(await xr({aptosConfig:e,accountAddress:t})).sequence_number;if(o&&_chunkZMDE3DNLjs.Y.from(o).equals(_chunkZMDE3DNLjs.Y.ZERO))try{return await R()}catch (e5){return 0}else return R()},[{chainId:p},{gasEstimate:y},h]=await Promise.all([s(),a(),c()]),{maxGasAmount:d,gasUnitPrice:l,expireTimestamp:b}={maxGasAmount:_optionalChain([i, 'optionalAccess', _95 => _95.maxGasAmount])?BigInt(i.maxGasAmount):BigInt(2e5),gasUnitPrice:_nullishCoalesce(_optionalChain([i, 'optionalAccess', _96 => _96.gasUnitPrice]), () => (BigInt(y))),expireTimestamp:_nullishCoalesce(_optionalChain([i, 'optionalAccess', _97 => _97.expireTimestamp]), () => (BigInt(Math.floor(Date.now()/1e3)+20)))};return new Se(_chunkZMDE3DNLjs.Y.from(t),BigInt(h),r,BigInt(d),BigInt(l),BigInt(b),new Zt(p))}async function zi(n){let{aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o}=n,s=await ku({aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o});if("secondarySignerAddresses"in n){let a=_nullishCoalesce(_optionalChain([n, 'access', _98 => _98.secondarySignerAddresses, 'optionalAccess', _99 => _99.map, 'call', _100 => _100(c=>_chunkZMDE3DNLjs.Y.from(c))]), () => ([]));return new yr(s,a,n.feePayerAddress?_chunkZMDE3DNLjs.Y.from(n.feePayerAddress):void 0)}return new mr(s,n.feePayerAddress?_chunkZMDE3DNLjs.Y.from(n.feePayerAddress):void 0)}function _s(n){let{signerPublicKey:e,transaction:t,secondarySignersPublicKeys:r,feePayerPublicKey:i}=n,o=on(e);if(t.feePayerAddress){let a=new Dt(t.rawTransaction,_nullishCoalesce(t.secondarySignerAddresses, () => ([])),t.feePayerAddress),c=[];t.secondarySignerAddresses&&(r?c=r.map(h=>on(h)):c=Array.from({length:t.secondarySignerAddresses.length},()=>on(void 0)));let p=on(i),y=new Ot(o,_nullishCoalesce(t.secondarySignerAddresses, () => ([])),c,{address:t.feePayerAddress,authenticator:p});return new nt(a.raw_txn,y).bcsToBytes()}if(t.secondarySignerAddresses){let a=new kt(t.rawTransaction,t.secondarySignerAddresses),c=[];r?c=r.map(y=>on(y)):c=Array.from({length:t.secondarySignerAddresses.length},()=>on(void 0));let p=new Nt(o,t.secondarySignerAddresses,c);return new nt(a.raw_txn,p).bcsToBytes()}let s;if(o instanceof ke)s=new zt(o.public_key,o.signature);else if(o instanceof oe||o instanceof Me)s=new ht(o);else if(o instanceof Yt)s=new ht(o);else throw new Error("Invalid public key");return new nt(t.rawTransaction,s).bcsToBytes()}function on(n){if(!n)return new Yt;let t=K.isInstance(n)||$.isInstance(n)||Re.isInstance(n)?new N(n):n,r=new v(new Uint8Array(64));if(I.isInstance(t))return new ke(t,r);if(N.isInstance(t))return K.isInstance(t.publicKey)?new oe(t,new O(ie.getSimulationSignature())):new oe(t,new O(r));if(Te.isInstance(t))return new Me(t,new be({signatures:t.publicKeys.map(i=>K.isInstance(i.publicKey)||$.isInstance(i.publicKey)?new O(ie.getSimulationSignature()):new O(r)),bitmap:t.createBitmap({bits:Array(t.publicKeys.length).fill(0).map((i,o)=>o)})}));throw new Error("Unsupported PublicKey used for simulations")}function Ni(n){let{transaction:e,feePayerAuthenticator:t,additionalSignersAuthenticators:r}=n,i=xo(J,n.senderAuthenticator),o;if(e.feePayerAddress){if(!t)throw new Error("Must provide a feePayerAuthenticator argument to generate a signed fee payer transaction");o=new Ot(i,_nullishCoalesce(e.secondarySignerAddresses, () => ([])),_nullishCoalesce(r, () => ([])),{address:e.feePayerAddress,authenticator:t})}else if(e.secondarySignerAddresses){if(!r)throw new Error("Must provide a additionalSignersAuthenticators argument to generate a signed multi agent transaction");o=new Nt(i,e.secondarySignerAddresses,r)}else i instanceof ke?o=new zt(i.public_key,i.signature):i instanceof Je?o=new en(i.public_key,i.signature):o=new ht(i);return new nt(e.rawTransaction,o).bcsToBytes()}function xs(n){let e=_sha3.sha3_256.create();for(let t of n)e.update(t);return e.digest()}var Du=xs(["APTOS::Transaction"]);function Aw(n){let e=Ni(n);return new (0, _chunkZMDE3DNLjs.j)(xs([Du,new Uint8Array([0]),e])).toString()}async function Is({key:n,moduleAddress:e,moduleName:t,functionName:r,aptosConfig:i,abi:o,fetch:s}){return o!==void 0?o:Fe(async()=>s(e,t,r,i),`${n}-${i.network}-${e}-${t}-${r}`,1e3*60*5)()}async function T(n){let e=await zu(n);return Nu(n,e)}async function zu(n){let{aptosConfig:e,data:t}=n,r,i;return"bytecode"in t?i=await Nr(t):"multisigAddress"in t?(r={aptosConfig:e,multisigAddress:t.multisigAddress,function:t.function,functionArguments:t.functionArguments,typeArguments:t.typeArguments,abi:t.abi},i=await Nr(r)):(r={aptosConfig:e,function:t.function,functionArguments:t.functionArguments,typeArguments:t.typeArguments,abi:t.abi},i=await Nr(r)),i}async function Nu(n,e){let{aptosConfig:t,sender:r,options:i}=n,o;if(Ou(n)&&(o=_chunkZMDE3DNLjs.Y.ZERO.toString()),Fu(n)){let{secondarySignerAddresses:s}=n;return zi({aptosConfig:t,sender:r,payload:e,options:i,secondarySignerAddresses:s,feePayerAddress:o})}return zi({aptosConfig:t,sender:r,payload:e,options:i,feePayerAddress:o})}function Ou(n){return n.withFeePayer===!0}function Fu(n){return"secondarySignerAddresses"in n}function vs(n){let{transaction:e}=n;return De(e)}function Or(n){let{signer:e,transaction:t}=n;return e.signTransactionWithAuthenticator(t)}function Fr(n){let{signer:e,transaction:t}=n;if(!t.feePayerAddress)throw new Error(`Transaction ${t} is not a Fee Payer transaction`);return t.feePayerAddress=e.accountAddress,Or({signer:e,transaction:t})}async function Oi(n){let{aptosConfig:e,transaction:t,signerPublicKey:r,secondarySignersPublicKeys:i,feePayerPublicKey:o,options:s}=n,a=_s({transaction:t,signerPublicKey:r,secondarySignersPublicKeys:i,feePayerPublicKey:o,options:s}),{data:c}=await mt({aptosConfig:e,body:a,path:"transactions/simulate",params:{estimate_gas_unit_price:_nullishCoalesce(_optionalChain([n, 'access', _101 => _101.options, 'optionalAccess', _102 => _102.estimateGasUnitPrice]), () => (!1)),estimate_max_gas_amount:_nullishCoalesce(_optionalChain([n, 'access', _103 => _103.options, 'optionalAccess', _104 => _104.estimateMaxGasAmount]), () => (!1)),estimate_prioritized_gas_unit_price:_nullishCoalesce(_optionalChain([n, 'access', _105 => _105.options, 'optionalAccess', _106 => _106.estimatePrioritizedGasUnitPrice]), () => (!1))},originMethod:"simulateTransaction",contentType:"application/x.aptos.signed_transaction+bcs"});return c}async function kn(n){let{aptosConfig:e}=n,t=Ni({...n});try{let{data:r}=await mt({aptosConfig:e,body:t,path:"transactions",originMethod:"submitTransaction",contentType:"application/x.aptos.signed_transaction+bcs"});return r}catch(r){let i=nt.deserialize(new U(t));throw i.authenticator.isSingleSender()&&i.authenticator.sender.isSingleKey()&&(i.authenticator.sender.public_key.publicKey instanceof K||i.authenticator.sender.public_key.publicKey instanceof $)&&await me.fetchJWK({aptosConfig:e,publicKey:i.authenticator.sender.public_key.publicKey,kid:i.authenticator.sender.signature.signature.getJwkKid()}),r}}async function bt(n){let{aptosConfig:e,signer:t,feePayer:r,transaction:i}=n;hr(t)&&await t.checkKeylessAccountValidity(e),hr(r)&&await r.checkKeylessAccountValidity(e);let o=n.feePayerAuthenticator||r&&Fr({signer:r,transaction:i}),s=Or({signer:t,transaction:i});return kn({aptosConfig:e,transaction:i,senderAuthenticator:s,feePayerAuthenticator:o})}async function Cs(n){let{aptosConfig:e,senderAuthenticator:t,feePayer:r,transaction:i}=n;hr(r)&&await r.checkKeylessAccountValidity(e);let o=Fr({signer:r,transaction:i});return kn({aptosConfig:e,transaction:i,senderAuthenticator:t,feePayerAuthenticator:o})}var Gu={typeParameters:[],parameters:[C.u8(),new C(C.u8())]};async function Ks(n){let{aptosConfig:e,account:t,metadataBytes:r,moduleBytecode:i,options:o}=n,s=i.map(a=>S.U8(a));return T({aptosConfig:e,sender:_chunkZMDE3DNLjs.Y.from(t),data:{function:"0x1::code::publish_package_txn",functionArguments:[S.U8(r),new S(s)],abi:Gu},options:o})}async function Mt(n){return xr(n)}async function Us(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return ai({aptosConfig:e,originMethod:"getModules",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/modules`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _107 => _107.ledgerVersion]),limit:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _108 => _108.limit]), () => (1e3))}})}async function ks(n){let{aptosConfig:e,accountAddress:t,options:r}=n,{response:i,cursor:o}=await cr({aptosConfig:e,originMethod:"getModulesPage",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/modules`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _109 => _109.ledgerVersion]),cursor:_optionalChain([r, 'optionalAccess', _110 => _110.cursor]),limit:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _111 => _111.limit]), () => (100))}});return{modules:i.data,cursor:o}}async function zr(n){return as(n)}async function Ds(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return ar({aptosConfig:e,originMethod:"getTransactions",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/transactions`,params:{start:_optionalChain([r, 'optionalAccess', _112 => _112.offset]),limit:_optionalChain([r, 'optionalAccess', _113 => _113.limit])}})}async function zs(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return ai({aptosConfig:e,originMethod:"getResources",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/resources`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _114 => _114.ledgerVersion]),limit:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _115 => _115.limit]), () => (999))}})}async function Ns(n){let{aptosConfig:e,accountAddress:t,options:r}=n,{response:i,cursor:o}=await cr({aptosConfig:e,originMethod:"getResourcesPage",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/resources`,params:{ledger_version:_optionalChain([r, 'optionalAccess', _116 => _116.ledgerVersion]),cursor:_optionalChain([r, 'optionalAccess', _117 => _117.cursor]),limit:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _118 => _118.limit]), () => (100))}});return{resources:i.data,cursor:o}}async function Gi(n){let{aptosConfig:e,accountAddress:t,resourceType:r,options:i}=n,{data:o}=await H({aptosConfig:e,originMethod:"getResource",path:`accounts/${_chunkZMDE3DNLjs.Y.from(t).toString()}/resource/${r}`,params:{ledger_version:_optionalChain([i, 'optionalAccess', _119 => _119.ledgerVersion])}});return o.data}async function sn(n){let{aptosConfig:e,authenticationKey:t,options:r}=n,i=await Gi({aptosConfig:e,accountAddress:"0x1",resourceType:"0x1::account::OriginatingAddress",options:r}),{address_map:{handle:o}}=i,s=_chunkZMDE3DNLjs.Y.from(t);try{let a=await Ir({aptosConfig:e,handle:o,data:{key:s.toString(),key_type:"address",value_type:"address"},options:r});return _chunkZMDE3DNLjs.Y.from(a)}catch(a){if(a instanceof he&&a.data.error_code==="table_item_not_found")return s;throw a}}async function Os(n){let{aptosConfig:e,accountAddress:t}=n,i={owner_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()},amount:{_gt:0}},s=await w({aptosConfig:e,query:{query:$o,variables:{where_condition:i}},originMethod:"getAccountTokensCount"});return s.current_token_ownerships_v2_aggregate.aggregate?s.current_token_ownerships_v2_aggregate.aggregate.count:0}async function Fs(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()},amount:{_gt:0}};_optionalChain([r, 'optionalAccess', _120 => _120.tokenStandard])&&(o.token_standard={_eq:_optionalChain([r, 'optionalAccess', _121 => _121.tokenStandard])});let s={query:Lo,variables:{where_condition:o,offset:_optionalChain([r, 'optionalAccess', _122 => _122.offset]),limit:_optionalChain([r, 'optionalAccess', _123 => _123.limit]),order_by:_optionalChain([r, 'optionalAccess', _124 => _124.orderBy])}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountOwnedTokens"})).current_token_ownerships_v2}async function Gs(n){let{aptosConfig:e,accountAddress:t,collectionAddress:r,options:i}=n,o=_chunkZMDE3DNLjs.Y.from(t).toStringLong(),s=_chunkZMDE3DNLjs.Y.from(r).toStringLong(),a={owner_address:{_eq:o},current_token_data:{collection_id:{_eq:s}},amount:{_gt:0}};_optionalChain([i, 'optionalAccess', _125 => _125.tokenStandard])&&(a.token_standard={_eq:_optionalChain([i, 'optionalAccess', _126 => _126.tokenStandard])});let c={query:qo,variables:{where_condition:a,offset:_optionalChain([i, 'optionalAccess', _127 => _127.offset]),limit:_optionalChain([i, 'optionalAccess', _128 => _128.limit]),order_by:_optionalChain([i, 'optionalAccess', _129 => _129.orderBy])}};return(await w({aptosConfig:e,query:c,originMethod:"getAccountOwnedTokensFromCollectionAddress"})).current_token_ownerships_v2}async function Bs(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}};_optionalChain([r, 'optionalAccess', _130 => _130.tokenStandard])&&(o.current_collection={token_standard:{_eq:_optionalChain([r, 'optionalAccess', _131 => _131.tokenStandard])}});let s={query:Ho,variables:{where_condition:o,offset:_optionalChain([r, 'optionalAccess', _132 => _132.offset]),limit:_optionalChain([r, 'optionalAccess', _133 => _133.limit]),order_by:_optionalChain([r, 'optionalAccess', _134 => _134.orderBy])}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountCollectionsWithOwnedTokens"})).current_collection_ownership_v2_view}async function Ms(n){let{aptosConfig:e,accountAddress:t}=n,r=_chunkZMDE3DNLjs.Y.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:Wo,variables:{address:r}},originMethod:"getAccountTransactionsCount"});return o.account_transactions_aggregate.aggregate?o.account_transactions_aggregate.aggregate.count:0}async function Vs(n){let{aptosConfig:e,accountAddress:t,options:r}=n,i=_chunkZMDE3DNLjs.Y.from(t).toStringLong(),o={..._optionalChain([r, 'optionalAccess', _135 => _135.where]),owner_address:{_eq:i}},s={query:Vo,variables:{where_condition:o,offset:_optionalChain([r, 'optionalAccess', _136 => _136.offset]),limit:_optionalChain([r, 'optionalAccess', _137 => _137.limit]),order_by:_optionalChain([r, 'optionalAccess', _138 => _138.orderBy])}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountCoinsData"})).current_fungible_asset_balances}async function Hs(n){let{aptosConfig:e,accountAddress:t}=n,r=_chunkZMDE3DNLjs.Y.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:Mo,variables:{address:r}},originMethod:"getAccountCoinsCount"});if(!o.current_fungible_asset_balances_aggregate.aggregate)throw Error("Failed to get the count of account coins");return o.current_fungible_asset_balances_aggregate.aggregate.count}async function Ls(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}},s={query:Sr,variables:{where_condition:o,offset:_optionalChain([r, 'optionalAccess', _139 => _139.offset]),limit:_optionalChain([r, 'optionalAccess', _140 => _140.limit]),order_by:_optionalChain([r, 'optionalAccess', _141 => _141.orderBy])}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountOwnedObjects"})).current_objects}async function qs(n){let{aptosConfig:e,privateKey:t}=n,r=new N(t.publicKey());if(t instanceof Qe){let o=M.fromPublicKey({publicKey:r}).derivedAddress();return rt.fromPrivateKey({privateKey:t,address:o})}if(t instanceof q){let i=M.fromPublicKey({publicKey:r.publicKey});if(await Rs({authKey:i,aptosConfig:e})){let c=i.derivedAddress();return rt.fromPrivateKey({privateKey:t,address:c,legacy:!0})}let s=M.fromPublicKey({publicKey:r});if(await Rs({authKey:s,aptosConfig:e})){let c=s.derivedAddress();return rt.fromPrivateKey({privateKey:t,address:c,legacy:!1})}}throw new Error(`Can't derive account from private key ${t}`)}async function Rs(n){let{aptosConfig:e,authKey:t}=n,r=await sn({aptosConfig:e,authenticationKey:t.derivedAddress()});try{return await Mt({aptosConfig:e,accountAddress:r}),!0}catch(i){if(i.status===404)return!1;throw new Error(`Error while looking for an account info ${r.toString()}`)}}var Bu={typeParameters:[],parameters:[new le,C.u8(),new le,C.u8(),C.u8(),C.u8()]};async function $s(n){let{aptosConfig:e,fromAccount:t,dangerouslySkipVerification:r}=n;if("toNewPrivateKey"in n)return Fi({aptosConfig:e,fromAccount:t,toNewPrivateKey:n.toNewPrivateKey});let i;if("toAccount"in n){if(n.toAccount instanceof se)return Fi({aptosConfig:e,fromAccount:t,toNewPrivateKey:n.toAccount.privateKey});if(n.toAccount instanceof Tr)return Fi({aptosConfig:e,fromAccount:t,toAccount:n.toAccount});i=n.toAccount.publicKey.authKey()}else if("toAuthKey"in n)i=n.toAuthKey;else throw new Error("Invalid arguments");let o=await Vu({aptosConfig:e,fromAccount:t,toAuthKey:i});if(r===!0)return o;let s=await Tt({aptosConfig:e,transactionHash:o.hash});if(!s.success)throw new Error(`Failed to rotate authentication key - ${s}`);let a=await T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::set_originating_address",functionArguments:[]}});return bt({aptosConfig:e,signer:n.toAccount,transaction:a})}async function Fi(n){let{aptosConfig:e,fromAccount:t}=n,r=await Mt({aptosConfig:e,accountAddress:t.accountAddress}),i;"toNewPrivateKey"in n?i=rt.fromPrivateKey({privateKey:n.toNewPrivateKey,legacy:!0}):i=n.toAccount;let s=new gr({sequenceNumber:BigInt(r.sequence_number),originator:t.accountAddress,currentAuthKey:_chunkZMDE3DNLjs.Y.from(r.authentication_key),newPublicKey:i.publicKey}).bcsToBytes(),a=t.sign(s),c=i.sign(s),p=await T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::rotate_authentication_key",functionArguments:[new Y(t.signingScheme),S.U8(t.publicKey.toUint8Array()),new Y(i.signingScheme),S.U8(i.publicKey.toUint8Array()),S.U8(a.toUint8Array()),S.U8(c.toUint8Array())],abi:Bu}});return bt({aptosConfig:e,signer:t,transaction:p})}var Mu={typeParameters:[],parameters:[C.u8()]};async function Vu(n){let{aptosConfig:e,fromAccount:t,toAuthKey:r}=n,i=r,o=await T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::rotate_authentication_key_call",functionArguments:[S.U8(i.toUint8Array())],abi:Mu}});return bt({aptosConfig:e,signer:t,transaction:o})}async function E(n){n.minimumLedgerVersion!==void 0&&await Kr({aptosConfig:n.config,minimumLedgerVersion:n.minimumLedgerVersion,processorType:n.processorType})}async function ae(n){let{aptosConfig:e,payload:t,options:r}=n,i=await Ps({...t,aptosConfig:e}),o=new _chunkZMDE3DNLjs.m;i.serialize(o);let s=o.toUint8Array(),{data:a}=await mt({aptosConfig:e,path:"view",originMethod:"view",contentType:"application/x.aptos.view_function+bcs",params:{ledger_version:_optionalChain([r, 'optionalAccess', _142 => _142.ledgerVersion])},body:s});return a}async function Ws(n){let{aptosConfig:e,payload:t,options:r}=n,{data:i}=await mt({aptosConfig:e,originMethod:"viewJson",path:"view",params:{ledger_version:_optionalChain([r, 'optionalAccess', _143 => _143.ledgerVersion])},body:{function:t.function,type_arguments:_nullishCoalesce(t.typeArguments, () => ([])),arguments:_nullishCoalesce(t.functionArguments, () => ([]))}});return i}async function Qs(n){let{aptosConfig:e,sender:t,authenticationFunction:r,options:i}=n,{moduleAddress:o,moduleName:s,functionName:a}=_chunkZMDE3DNLjs.ka.call(void 0, r);return T({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::add_authentication_function",typeArguments:[],functionArguments:[o,s,a],abi:{typeParameters:[],parameters:[new F,new A(_()),new A(_())]}},options:i})}async function js(n){let{aptosConfig:e,sender:t,authenticationFunction:r,options:i}=n,{moduleAddress:o,moduleName:s,functionName:a}=_chunkZMDE3DNLjs.ka.call(void 0, r);return T({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::remove_authentication_function",typeArguments:[],functionArguments:[o,s,a],abi:{typeParameters:[],parameters:[new F,new A(_()),new A(_())]}},options:i})}async function Js(n){let{aptosConfig:e,sender:t,options:r}=n;return T({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::remove_authenticator",typeArguments:[],functionArguments:[],abi:{typeParameters:[],parameters:[]}},options:r})}var Vt=class{constructor(e){this.config=e;this.isAccountAbstractionEnabled=async e=>{let t=await this.getAuthenticationFunction(e),{moduleAddress:r,moduleName:i,functionName:o}=_chunkZMDE3DNLjs.ka.call(void 0, e.authenticationFunction);return _nullishCoalesce(_optionalChain([t, 'optionalAccess', _144 => _144.some, 'call', _145 => _145(s=>_chunkZMDE3DNLjs.Y.fromString(r).equals(s.moduleAddress)&&i===s.moduleName&&o===s.functionName)]), () => (!1))};this.enableAccountAbstractionTransaction=this.addAuthenticationFunctionTransaction;this.disableAccountAbstractionTransaction=async e=>{let{accountAddress:t,authenticationFunction:r,options:i}=e;return r?this.removeAuthenticationFunctionTransaction({accountAddress:t,authenticationFunction:r,options:i}):this.removeDispatchableAuthenticatorTransaction({accountAddress:t,options:i})}}async addAuthenticationFunctionTransaction(e){let{accountAddress:t,authenticationFunction:r,options:i}=e;return Qs({aptosConfig:this.config,authenticationFunction:r,sender:t,options:i})}async removeAuthenticationFunctionTransaction(e){let{accountAddress:t,authenticationFunction:r,options:i}=e;return js({aptosConfig:this.config,sender:t,authenticationFunction:r,options:i})}async removeDispatchableAuthenticatorTransaction(e){let{accountAddress:t,options:r}=e;return Js({aptosConfig:this.config,sender:t,options:r})}async getAuthenticationFunction(e){let{accountAddress:t}=e,[{vec:r}]=await ae({aptosConfig:this.config,payload:{function:"0x1::account_abstraction::dispatchable_authenticator",functionArguments:[_chunkZMDE3DNLjs.Y.from(t)],abi:{typeParameters:[],parameters:[new F],returnTypes:[]}}});if(r.length!==0)return r[0].map(i=>({moduleAddress:_chunkZMDE3DNLjs.Y.fromString(i.module_address),moduleName:i.module_name,functionName:i.function_name}))}};var Dn=class{constructor(e){this.config=e;this.abstraction=new Vt(e)}async getAccountInfo(e){return Mt({aptosConfig:this.config,...e})}async getAccountModules(e){return Us({aptosConfig:this.config,...e})}async getAccountModulesPage(e){return ks({aptosConfig:this.config,...e})}async getAccountModule(e){return zr({aptosConfig:this.config,...e})}async getAccountTransactions(e){return Ds({aptosConfig:this.config,...e})}async getAccountResources(e){return zs({aptosConfig:this.config,...e})}async getAccountResourcesPage(e){return Ns({aptosConfig:this.config,...e})}async getAccountResource(e){return Gi({aptosConfig:this.config,...e})}async lookupOriginalAccountAddress(e){return sn({aptosConfig:this.config,...e})}async getAccountTokensCount(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"account_transactions_processor"}),Os({aptosConfig:this.config,...e})}async getAccountOwnedTokens(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Fs({aptosConfig:this.config,...e})}async getAccountOwnedTokensFromCollectionAddress(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Gs({aptosConfig:this.config,...e})}async getAccountCollectionsWithOwnedTokens(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Bs({aptosConfig:this.config,...e})}async getAccountTransactionsCount(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"account_transactions_processor"}),Ms({aptosConfig:this.config,...e})}async getAccountCoinsData(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"fungible_asset_processor"}),Vs({aptosConfig:this.config,...e})}async getAccountCoinsCount(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"fungible_asset_processor"}),Hs({aptosConfig:this.config,...e})}async getAccountAPTAmount(e){return this.getAccountCoinAmount({coinType:It,faMetadataAddress:Eo,...e})}async getAccountCoinAmount(e){let{accountAddress:t,coinType:r,faMetadataAddress:i,minimumLedgerVersion:o}=e;o&&console.warn(`minimumLedgerVersion is not used anymore, here for backward 
        compatibility see https://github.com/aptos-labs/aptos-ts-sdk/pull/519, 
        will be removed in the near future`);let s=r;r===void 0&&i!==void 0&&(s=await Fe(async()=>{try{let p=(await ae({aptosConfig:this.config,payload:{function:"0x1::coin::paired_coin",functionArguments:[i]}})).at(0);if(p.vec.length>0&&_chunkZMDE3DNLjs.ja.call(void 0, p.vec[0]))return _chunkZMDE3DNLjs.ia.call(void 0, p.vec[0])}catch (e6){}},`coin-mapping-${i.toString()}`,1e3*60*5)());let a;if(r!==void 0&&i!==void 0)a=_chunkZMDE3DNLjs.Y.from(i).toStringLong();else if(r!==void 0&&i===void 0)r===It?a=_chunkZMDE3DNLjs.Y.A.toStringLong():a=_chunkZMDE3DNLjs.Z.call(void 0, _chunkZMDE3DNLjs.Y.A,r).toStringLong();else if(r===void 0&&i!==void 0){let p=_chunkZMDE3DNLjs.Y.from(i);a=p.toStringLong(),p===_chunkZMDE3DNLjs.Y.A&&(s=It)}else throw new Error("Either coinType, faMetadataAddress, or both must be provided");if(s!==void 0){let[p]=await ae({aptosConfig:this.config,payload:{function:"0x1::coin::balance",typeArguments:[s],functionArguments:[t]}});return parseInt(p,10)}let[c]=await ae({aptosConfig:this.config,payload:{function:"0x1::primary_fungible_store::balance",typeArguments:["0x1::object::ObjectCore"],functionArguments:[t,a]}});return parseInt(c,10)}async getAccountOwnedObjects(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),Ls({aptosConfig:this.config,...e})}async deriveAccountFromPrivateKey(e){return qs({aptosConfig:this.config,...e})}};var _aptosclient = require('@aptos-labs/aptos-client'); var _aptosclient2 = _interopRequireDefault(_aptosclient);var Gr=class{constructor(e){if(_optionalChain([e, 'optionalAccess', _146 => _146.fullnode])||_optionalChain([e, 'optionalAccess', _147 => _147.indexer])||_optionalChain([e, 'optionalAccess', _148 => _148.faucet])||_optionalChain([e, 'optionalAccess', _149 => _149.pepper])||_optionalChain([e, 'optionalAccess', _150 => _150.prover])){if(_optionalChain([e, 'optionalAccess', _151 => _151.network])==="custom")console.info("Note: using CUSTOM network will require queries to lookup ChainId");else if(!_optionalChain([e, 'optionalAccess', _152 => _152.network]))throw new Error("Custom endpoints require a network to be specified")}this.network=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _153 => _153.network]), () => ("devnet")),this.fullnode=_optionalChain([e, 'optionalAccess', _154 => _154.fullnode]),this.faucet=_optionalChain([e, 'optionalAccess', _155 => _155.faucet]),this.pepper=_optionalChain([e, 'optionalAccess', _156 => _156.pepper]),this.prover=_optionalChain([e, 'optionalAccess', _157 => _157.prover]),this.indexer=_optionalChain([e, 'optionalAccess', _158 => _158.indexer]),this.client=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _159 => _159.client]), () => ({provider:_aptosclient2.default})),this.clientConfig=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _160 => _160.clientConfig]), () => ({})),this.fullnodeConfig=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _161 => _161.fullnodeConfig]), () => ({})),this.indexerConfig=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _162 => _162.indexerConfig]), () => ({})),this.faucetConfig=_nullishCoalesce(_optionalChain([e, 'optionalAccess', _163 => _163.faucetConfig]), () => ({}))}getRequestUrl(e){switch(e){case"Fullnode":if(this.fullnode!==void 0)return this.fullnode;if(this.network==="custom")throw new Error("Please provide a custom full node url");return To[this.network];case"Faucet":if(this.faucet!==void 0)return this.faucet;if(this.network==="testnet")throw new Error("There is no way to programmatically mint testnet APT, you must use the minting site at https://aptos.dev/network/faucet");if(this.network==="mainnet")throw new Error("There is no mainnet faucet");if(this.network==="custom")throw new Error("Please provide a custom faucet url");return bo[this.network];case"Indexer":if(this.indexer!==void 0)return this.indexer;if(this.network==="custom")throw new Error("Please provide a custom indexer url");return ho[this.network];case"Pepper":if(this.pepper!==void 0)return this.pepper;if(this.network==="custom")throw new Error("Please provide a custom pepper service url");return ti[this.network];case"Prover":if(this.prover!==void 0)return this.prover;if(this.network==="custom")throw new Error("Please provide a custom prover service url");return ni[this.network];default:throw Error(`apiType ${e} is not supported`)}}isPepperServiceRequest(e){return ti[this.network]===e}isProverServiceRequest(e){return ni[this.network]===e}};var Lu={typeParameters:[{constraints:[]}],parameters:[new F,new X]};async function Xs(n){let{aptosConfig:e,sender:t,recipient:r,amount:i,coinType:o,options:s}=n;return T({aptosConfig:e,sender:t,data:{function:"0x1::aptos_account::transfer_coins",typeArguments:[_nullishCoalesce(o, () => (It))],functionArguments:[r,i],abi:Lu},options:s})}var zn=class{constructor(e){this.config=e}async transferCoinTransaction(e){return Xs({aptosConfig:this.config,...e})}};var wt={BOOLEAN:"bool",U8:"u8",U16:"u16",U32:"u32",U64:"u64",U128:"u128",U256:"u256",ADDRESS:"address",STRING:"0x1::string::String",ARRAY:"vector<u8>"},Pe="0x4::token::Token";async function Ys(n){let{aptosConfig:e,digitalAssetAddress:t}=n,r={token_data_id:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}};return(await w({aptosConfig:e,query:{query:is,variables:{where_condition:r}},originMethod:"getDigitalAssetData"})).current_token_datas_v2[0]}async function Zs(n){let{aptosConfig:e,digitalAssetAddress:t}=n,r={token_data_id:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()},amount:{_gt:0}};return(await w({aptosConfig:e,query:{query:wi,variables:{where_condition:r}},originMethod:"getCurrentDigitalAssetOwnership"})).current_token_ownerships_v2[0]}async function ea(n){let{aptosConfig:e,ownerAddress:t,options:r}=n,i={owner_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()},amount:{_gt:0}},o={query:wi,variables:{where_condition:i,offset:_optionalChain([r, 'optionalAccess', _164 => _164.offset]),limit:_optionalChain([r, 'optionalAccess', _165 => _165.limit]),order_by:_optionalChain([r, 'optionalAccess', _166 => _166.orderBy])}};return(await w({aptosConfig:e,query:o,originMethod:"getOwnedDigitalAssets"})).current_token_ownerships_v2}async function ta(n){let{aptosConfig:e,digitalAssetAddress:t,options:r}=n,i={token_data_id:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}},o={query:rs,variables:{where_condition:i,offset:_optionalChain([r, 'optionalAccess', _167 => _167.offset]),limit:_optionalChain([r, 'optionalAccess', _168 => _168.limit]),order_by:_optionalChain([r, 'optionalAccess', _169 => _169.orderBy])}};return(await w({aptosConfig:e,query:o,originMethod:"getDigitalAssetActivity"})).token_activities_v2}var qu={typeParameters:[],parameters:[new A(_()),new X,new A(_()),new A(_()),new L,new L,new L,new L,new L,new L,new L,new L,new L,new X,new X]};async function na(n){let{aptosConfig:e,options:t,creator:r}=n;return T({aptosConfig:e,sender:r.accountAddress,data:{function:"0x4::aptos_token::create_collection",functionArguments:[new x(n.description),new j(_nullishCoalesce(n.maxSupply, () => (_chunkZMDE3DNLjs.e))),new x(n.name),new x(n.uri),new z(_nullishCoalesce(n.mutableDescription, () => (!0))),new z(_nullishCoalesce(n.mutableRoyalty, () => (!0))),new z(_nullishCoalesce(n.mutableURI, () => (!0))),new z(_nullishCoalesce(n.mutableTokenDescription, () => (!0))),new z(_nullishCoalesce(n.mutableTokenName, () => (!0))),new z(_nullishCoalesce(n.mutableTokenProperties, () => (!0))),new z(_nullishCoalesce(n.mutableTokenURI, () => (!0))),new z(_nullishCoalesce(n.tokensBurnableByCreator, () => (!0))),new z(_nullishCoalesce(n.tokensFreezableByCreator, () => (!0))),new j(_nullishCoalesce(n.royaltyNumerator, () => (0))),new j(_nullishCoalesce(n.royaltyDenominator, () => (1)))],abi:qu},options:t})}async function an(n){let{aptosConfig:e,options:t}=n,r=_optionalChain([t, 'optionalAccess', _170 => _170.where]);_optionalChain([t, 'optionalAccess', _171 => _171.tokenStandard])&&(r.token_standard={_eq:_nullishCoalesce(_optionalChain([t, 'optionalAccess', _172 => _172.tokenStandard]), () => ("v2"))});let i={query:jo,variables:{where_condition:r,offset:_optionalChain([t, 'optionalAccess', _173 => _173.offset]),limit:_optionalChain([t, 'optionalAccess', _174 => _174.limit])}};return(await w({aptosConfig:e,query:i,originMethod:"getCollectionData"})).current_collections_v2[0]}async function ra(n){let{aptosConfig:e,creatorAddress:t,collectionName:r,options:i}=n,o=_chunkZMDE3DNLjs.Y.from(t),s={collection_name:{_eq:r},creator_address:{_eq:o.toStringLong()}};return _optionalChain([i, 'optionalAccess', _175 => _175.tokenStandard])&&(s.token_standard={_eq:_nullishCoalesce(_optionalChain([i, 'optionalAccess', _176 => _176.tokenStandard]), () => ("v2"))}),an({aptosConfig:e,options:{...i,where:s}})}async function ia(n){let{aptosConfig:e,creatorAddress:t,options:r}=n,o={creator_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}};return _optionalChain([r, 'optionalAccess', _177 => _177.tokenStandard])&&(o.token_standard={_eq:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _178 => _178.tokenStandard]), () => ("v2"))}),an({aptosConfig:e,options:{...r,where:o}})}async function oa(n){let{aptosConfig:e,collectionId:t,options:r}=n,o={collection_id:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}};return _optionalChain([r, 'optionalAccess', _179 => _179.tokenStandard])&&(o.token_standard={_eq:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _180 => _180.tokenStandard]), () => ("v2"))}),an({aptosConfig:e,options:{...r,where:o}})}async function sa(n){let{creatorAddress:e,collectionName:t,options:r,aptosConfig:i}=n,o=_chunkZMDE3DNLjs.Y.from(e),s={collection_name:{_eq:t},creator_address:{_eq:o.toStringLong()}};return _optionalChain([r, 'optionalAccess', _181 => _181.tokenStandard])&&(s.token_standard={_eq:_nullishCoalesce(_optionalChain([r, 'optionalAccess', _182 => _182.tokenStandard]), () => ("v2"))}),(await an({aptosConfig:i,options:{where:s}})).collection_id}var $u={typeParameters:[],parameters:[new A(_()),new A(_()),new A(_()),new A(_()),new C(new A(_())),new C(new A(_())),new C(C.u8())]};async function aa(n){let{aptosConfig:e,options:t,creator:r,collection:i,description:o,name:s,uri:a,propertyKeys:c,propertyTypes:p,propertyValues:y}=n,h=_optionalChain([p, 'optionalAccess', _183 => _183.map, 'call', _184 => _184(d=>wt[d])]);return T({aptosConfig:e,sender:r.accountAddress,data:{function:"0x4::aptos_token::mint",functionArguments:[new x(i),new x(o),new x(s),new x(a),S.MoveString(_nullishCoalesce(c, () => ([]))),S.MoveString(_nullishCoalesce(h, () => ([]))),wa(_nullishCoalesce(y, () => ([])),_nullishCoalesce(h, () => ([])))],abi:$u},options:t})}var Wu={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new F]};async function ca(n){let{aptosConfig:e,sender:t,digitalAssetAddress:r,recipient:i,digitalAssetType:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::object::transfer",typeArguments:[_nullishCoalesce(o, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(r),_chunkZMDE3DNLjs.Y.from(i)],abi:Wu},options:s})}var Qu={typeParameters:[],parameters:[new A(_()),new A(_()),new A(_()),new A(_()),new C(new A(_())),new C(new A(_())),new C(C.u8()),new F]};async function ua(n){let{aptosConfig:e,account:t,collection:r,description:i,name:o,uri:s,recipient:a,propertyKeys:c,propertyTypes:p,propertyValues:y,options:h}=n;if(_optionalChain([c, 'optionalAccess', _185 => _185.length])!==_optionalChain([y, 'optionalAccess', _186 => _186.length]))throw new Error("Property keys and property values counts do not match");if(_optionalChain([p, 'optionalAccess', _187 => _187.length])!==_optionalChain([y, 'optionalAccess', _188 => _188.length]))throw new Error("Property types and property values counts do not match");let d=_optionalChain([p, 'optionalAccess', _189 => _189.map, 'call', _190 => _190(l=>wt[l])]);return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::mint_soul_bound",functionArguments:[r,i,o,s,S.MoveString(_nullishCoalesce(c, () => ([]))),S.MoveString(_nullishCoalesce(d, () => ([]))),wa(_nullishCoalesce(y, () => ([])),_nullishCoalesce(d, () => ([]))),a],abi:Qu},options:h})}var ju={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0)))]};async function pa(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::burn",typeArguments:[_nullishCoalesce(i, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(r)],abi:ju},options:o})}var Ju={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0)))]};async function da(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::freeze_transfer",typeArguments:[_nullishCoalesce(i, () => (Pe))],functionArguments:[r],abi:Ju},options:o})}var Xu={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0)))]};async function la(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::unfreeze_transfer",typeArguments:[_nullishCoalesce(i, () => (Pe))],functionArguments:[r],abi:Xu},options:o})}var Yu={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_())]};async function ga(n){let{aptosConfig:e,creator:t,description:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_description",typeArguments:[_nullishCoalesce(o, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(i),new x(r)],abi:Yu},options:s})}var Zu={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_())]};async function ma(n){let{aptosConfig:e,creator:t,name:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_name",typeArguments:[_nullishCoalesce(o, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(i),new x(r)],abi:Zu},options:s})}var ep={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_())]};async function ya(n){let{aptosConfig:e,creator:t,uri:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_uri",typeArguments:[_nullishCoalesce(o, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(i),new x(r)],abi:ep},options:s})}var tp={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_()),new A(_()),C.u8()]};async function fa(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:c}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::add_property",typeArguments:[_nullishCoalesce(a, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(s),new x(r),new x(wt[i]),S.U8(Bi(o,wt[i]))],abi:tp},options:c})}var np={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_())]};async function Aa(n){let{aptosConfig:e,creator:t,propertyKey:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::remove_property",typeArguments:[_nullishCoalesce(o, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(i),new x(r)],abi:np},options:s})}var rp={typeParameters:[{constraints:["key"]}],parameters:[new A(ge(new B(0))),new A(_()),new A(_()),C.u8()]};async function ha(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:c}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::update_property",typeArguments:[_nullishCoalesce(a, () => (Pe))],functionArguments:[_chunkZMDE3DNLjs.Y.from(s),new x(r),new x(wt[i]),Bi(o,wt[i])],abi:rp},options:c})}var ip={typeParameters:[{constraints:["key"]},{constraints:[]}],parameters:[new A(ge(new B(0))),new A(_()),new B(1)]};async function Ta(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:c}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::add_typed_property",typeArguments:[_nullishCoalesce(a, () => (Pe)),wt[i]],functionArguments:[_chunkZMDE3DNLjs.Y.from(s),new x(r),o],abi:ip},options:c})}var op={typeParameters:[{constraints:["key"]},{constraints:[]}],parameters:[new A(ge(new B(0))),new A(_()),new B(1)]};async function ba(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:c}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::update_typed_property",typeArguments:[_nullishCoalesce(a, () => (Pe)),wt[i]],functionArguments:[_chunkZMDE3DNLjs.Y.from(s),new x(r),o],abi:op},options:c})}function wa(n,e){let t=new Array;return e.forEach((r,i)=>{t.push(Bi(n[i],r))}),t}function Bi(n,e){let t=ze(e);return Bt(n,t,0,[]).bcsToBytes()}var Nn=class{constructor(e){this.config=e}async getCollectionData(e){await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"});let{creatorAddress:t,collectionName:r,options:i}=e,o=_chunkZMDE3DNLjs.Y.from(t),s={collection_name:{_eq:r},creator_address:{_eq:o.toStringLong()}};return _optionalChain([i, 'optionalAccess', _191 => _191.tokenStandard])&&(s.token_standard={_eq:_nullishCoalesce(_optionalChain([i, 'optionalAccess', _192 => _192.tokenStandard]), () => ("v2"))}),an({aptosConfig:this.config,options:{where:s}})}async getCollectionDataByCreatorAddressAndCollectionName(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),ra({aptosConfig:this.config,...e})}async getCollectionDataByCreatorAddress(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),ia({aptosConfig:this.config,...e})}async getCollectionDataByCollectionId(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),oa({aptosConfig:this.config,...e})}async getCollectionId(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),sa({aptosConfig:this.config,...e})}async getDigitalAssetData(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Ys({aptosConfig:this.config,...e})}async getCurrentDigitalAssetOwnership(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Zs({aptosConfig:this.config,...e})}async getOwnedDigitalAssets(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),ea({aptosConfig:this.config,...e})}async getDigitalAssetActivity(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),ta({aptosConfig:this.config,...e})}async createCollectionTransaction(e){return na({aptosConfig:this.config,...e})}async mintDigitalAssetTransaction(e){return aa({aptosConfig:this.config,...e})}async transferDigitalAssetTransaction(e){return ca({aptosConfig:this.config,...e})}async mintSoulBoundTransaction(e){return ua({aptosConfig:this.config,...e})}async burnDigitalAssetTransaction(e){return pa({aptosConfig:this.config,...e})}async freezeDigitalAssetTransaferTransaction(e){return da({aptosConfig:this.config,...e})}async unfreezeDigitalAssetTransaferTransaction(e){return la({aptosConfig:this.config,...e})}async setDigitalAssetDescriptionTransaction(e){return ga({aptosConfig:this.config,...e})}async setDigitalAssetNameTransaction(e){return ma({aptosConfig:this.config,...e})}async setDigitalAssetURITransaction(e){return ya({aptosConfig:this.config,...e})}async addDigitalAssetPropertyTransaction(e){return fa({aptosConfig:this.config,...e})}async removeDigitalAssetPropertyTransaction(e){return Aa({aptosConfig:this.config,...e})}async updateDigitalAssetPropertyTransaction(e){return ha({aptosConfig:this.config,...e})}async addDigitalAssetTypedPropertyTransaction(e){return Ta({aptosConfig:this.config,...e})}async updateDigitalAssetTypedPropertyTransaction(e){return ba({aptosConfig:this.config,...e})}};var Sa=300,sp=n=>{if(n&&n.length>Sa)throw new Error(`Event type length exceeds the maximum length of ${Sa}`)};async function Ea(n){let{aptosConfig:e,eventType:t,options:r}=n,i={_or:[{account_address:{_eq:t.split("::")[0]}},{account_address:{_eq:"0x0000000000000000000000000000000000000000000000000000000000000000"},sequence_number:{_eq:0},creation_number:{_eq:0}}],indexed_type:{_eq:t}};return On({aptosConfig:e,options:{...r,where:i}})}async function Pa(n){let{accountAddress:e,aptosConfig:t,creationNumber:r,options:i}=n,s={account_address:{_eq:_chunkZMDE3DNLjs.Y.from(e).toStringLong()},creation_number:{_eq:r}};return On({aptosConfig:t,options:{...i,where:s}})}async function _a(n){let{accountAddress:e,aptosConfig:t,eventType:r,options:i}=n,s={account_address:{_eq:_chunkZMDE3DNLjs.Y.from(e).toStringLong()},indexed_type:{_eq:r}};return On({aptosConfig:t,options:{...i,where:s}})}async function On(n){let{aptosConfig:e,options:t}=n;sp(_optionalChain([t, 'optionalAccess', _193 => _193.where, 'optionalAccess', _194 => _194.indexed_type, 'optionalAccess', _195 => _195._eq]));let r={query:Yo,variables:{where_condition:_optionalChain([t, 'optionalAccess', _196 => _196.where]),offset:_optionalChain([t, 'optionalAccess', _197 => _197.offset]),limit:_optionalChain([t, 'optionalAccess', _198 => _198.limit]),order_by:_optionalChain([t, 'optionalAccess', _199 => _199.orderBy])}};return(await w({aptosConfig:e,query:r,originMethod:"getEvents"})).events}var Fn=class{constructor(e){this.config=e}async getModuleEventsByEventType(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),Ea({aptosConfig:this.config,...e})}async getAccountEventsByCreationNumber(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),Pa({aptosConfig:this.config,...e})}async getAccountEventsByEventType(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),_a({aptosConfig:this.config,...e})}async getEvents(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _200 => _200.minimumLedgerVersion]),processorType:"events_processor"}),On({aptosConfig:this.config,...e})}};async function xa(n){let{aptosConfig:e,accountAddress:t,amount:r,options:i}=n,o=_optionalChain([i, 'optionalAccess', _201 => _201.timeoutSecs])||20,{data:s}=await Co({aptosConfig:e,path:"fund",body:{address:_chunkZMDE3DNLjs.Y.from(t).toString(),amount:r},originMethod:"fundAccount"}),a=s.txn_hashes[0],c=await Tt({aptosConfig:e,transactionHash:a,options:{timeoutSecs:o,checkSuccess:_optionalChain([i, 'optionalAccess', _202 => _202.checkSuccess])}});if(c.type==="user_transaction")return c;throw new Error(`Unexpected transaction received for fund account: ${c.type}`)}var Gn=class{constructor(e){this.config=e}async fundAccount(e){let t=await xa({aptosConfig:this.config,...e});return(_optionalChain([e, 'access', _203 => _203.options, 'optionalAccess', _204 => _204.waitForIndexer])===void 0||_optionalChain([e, 'access', _205 => _205.options, 'optionalAccess', _206 => _206.waitForIndexer]))&&await Kr({aptosConfig:this.config,minimumLedgerVersion:BigInt(t.version),processorType:"fungible_asset_processor"}),t}};async function Br(n){let{aptosConfig:e,options:t}=n,r={query:es,variables:{where_condition:_optionalChain([t, 'optionalAccess', _207 => _207.where]),limit:_optionalChain([t, 'optionalAccess', _208 => _208.limit]),offset:_optionalChain([t, 'optionalAccess', _209 => _209.offset])}};return(await w({aptosConfig:e,query:r,originMethod:"getFungibleAssetMetadata"})).fungible_asset_metadata}async function Ia(n){let{aptosConfig:e,options:t}=n,r={query:Zo,variables:{where_condition:_optionalChain([t, 'optionalAccess', _210 => _210.where]),limit:_optionalChain([t, 'optionalAccess', _211 => _211.limit]),offset:_optionalChain([t, 'optionalAccess', _212 => _212.offset])}};return(await w({aptosConfig:e,query:r,originMethod:"getFungibleAssetActivities"})).fungible_asset_activities}async function va(n){let{aptosConfig:e,options:t}=n,r={query:Jo,variables:{where_condition:_optionalChain([t, 'optionalAccess', _213 => _213.where]),limit:_optionalChain([t, 'optionalAccess', _214 => _214.limit]),offset:_optionalChain([t, 'optionalAccess', _215 => _215.offset])}};return(await w({aptosConfig:e,query:r,originMethod:"getCurrentFungibleAssetBalances"})).current_fungible_asset_balances}var Ca={typeParameters:[{constraints:[]}],parameters:[ze("0x1::object::Object"),new F,new X]};async function Ka(n){let{aptosConfig:e,sender:t,fungibleAssetMetadataAddress:r,recipient:i,amount:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::primary_fungible_store::transfer",typeArguments:["0x1::fungible_asset::Metadata"],functionArguments:[r,i,o],abi:Ca},options:s})}async function Ra(n){let{aptosConfig:e,sender:t,fromStore:r,toStore:i,amount:o,options:s}=n;return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::dispatchable_fungible_asset::transfer",typeArguments:["0x1::fungible_asset::FungibleStore"],functionArguments:[r,i,o],abi:Ca},options:s})}var Bn=class{constructor(e){this.config=e}async getFungibleAssetMetadata(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _216 => _216.minimumLedgerVersion]),processorType:"fungible_asset_processor"}),Br({aptosConfig:this.config,...e})}async getFungibleAssetMetadataByAssetType(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _217 => _217.minimumLedgerVersion]),processorType:"fungible_asset_processor"}),(await Br({aptosConfig:this.config,options:{where:{asset_type:{_eq:e.assetType}}}}))[0]}async getFungibleAssetMetadataByCreatorAddress(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _218 => _218.minimumLedgerVersion]),processorType:"fungible_asset_processor"}),await Br({aptosConfig:this.config,options:{where:{creator_address:{_eq:_chunkZMDE3DNLjs.Y.from(e.creatorAddress).toStringLong()}}}})}async getFungibleAssetActivities(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _219 => _219.minimumLedgerVersion]),processorType:"fungible_asset_processor"}),Ia({aptosConfig:this.config,...e})}async getCurrentFungibleAssetBalances(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _220 => _220.minimumLedgerVersion]),processorType:"fungible_asset_processor"}),va({aptosConfig:this.config,...e})}async transferFungibleAsset(e){return Ka({aptosConfig:this.config,...e})}async transferFungibleAssetBetweenStores(e){return Ra({aptosConfig:this.config,...e})}};var Mn=class{constructor(e){this.config=e}async getLedgerInfo(){return Er({aptosConfig:this.config})}async getChainId(){return(await this.getLedgerInfo()).chain_id}async getBlockByVersion(e){return ls({aptosConfig:this.config,...e})}async getBlockByHeight(e){return gs({aptosConfig:this.config,...e})}async view(e){return ae({aptosConfig:this.config,...e})}async viewJson(e){return Ws({aptosConfig:this.config,...e})}async getChainTopUserTransactions(e){return os({aptosConfig:this.config,...e})}async queryIndexer(e){return w({aptosConfig:this.config,...e})}async getIndexerLastSuccessVersion(){return Pr({aptosConfig:this.config})}async getProcessorStatus(e){return _r({aptosConfig:this.config,processorType:e})}};var Ua=["A name must be between 3 and 63 characters long,","and can only contain lowercase a-z, 0-9, and hyphens.","A name may not start or end with a hyphen."].join(" ");function ka(n){return!(!n||n.length<3||n.length>63||!/^[a-z\d][a-z\d-]{1,61}[a-z\d]$/.test(n))}function St(n){let[e,t,...r]=n.replace(/\.apt$/,"").split(".");if(r.length>0)throw new Error(`${n} is invalid. A name can only have two parts, a domain and a subdomain separated by a "."`);if(!ka(e))throw new Error(`${e} is not valid. ${Ua}`);if(t&&!ka(t))throw new Error(`${t} is not valid. ${Ua}`);return{domainName:t||e,subdomainName:t?e:void 0}}function Da(n){if(!n)return!1;let e=new Date(n.domain_expiration_timestamp).getTime()<Date.now(),t=new Date(n.expiration_timestamp).getTime()<Date.now();return n.subdomain&&e?!1:n.subdomain&&n.subdomain_expiration_policy===1?!0:!t}var ap="0x585fc9f0f0c54183b039ffc770ca282ebd87307916c215a3e692f2f8e4305e82",cp={testnet:"0x5f8fd2347449685cf41d4db97926ec3a096eaf381332be4f1318ad4d16a8497c",mainnet:"0x867ed1f6bf916171b1de3ee92849b8978b7d1b9e0a8cc982a3d19d535dfd9c0c",local:ap,custom:null,devnet:null};function st(n){let e=cp[n.network];if(!e)throw new Error(`The ANS contract is not deployed to ${n.network}`);return e}var Mr=n=>{if(n&&typeof n=="object"&&"vec"in n&&Array.isArray(n.vec))return n.vec[0]};async function za(n){let{aptosConfig:e,name:t}=n,r=st(e),{domainName:i,subdomainName:o}=St(t),s=await ae({aptosConfig:e,payload:{function:`${r}::router::get_owner_addr`,functionArguments:[i,o]}}),a=Mr(s[0]);return a?_chunkZMDE3DNLjs.Y.from(a):void 0}async function Na(n){let{aptosConfig:e,expiration:t,name:r,sender:i,targetAddress:o,toAddress:s,options:a,transferable:c}=n,p=st(e),{domainName:y,subdomainName:h}=St(r),d=t.policy==="subdomain:independent"||t.policy==="subdomain:follow-domain";if(h&&!d)throw new Error("Subdomains must have an expiration policy of either 'subdomain:independent' or 'subdomain:follow-domain'");if(d&&!h)throw new Error(`Policy is set to ${t.policy} but no subdomain was provided`);if(t.policy==="domain"){let Q=_nullishCoalesce(t.years, () => (1));if(Q!==1)throw new Error("For now, names can only be registered for 1 year at a time");let at=Q*31536e3;return await T({aptosConfig:e,sender:i.accountAddress.toString(),data:{function:`${p}::router::register_domain`,functionArguments:[y,at,o,s]},options:a})}if(!h)throw new Error(`${t.policy} requires a subdomain to be provided.`);let l=await Mi({aptosConfig:e,name:y});if(!l)throw new Error("The domain does not exist");let b=t.policy==="subdomain:independent"?t.expirationDate:l;if(b>l)throw new Error("The subdomain expiration time cannot be greater than the domain expiration time");return await T({aptosConfig:e,sender:i.accountAddress.toString(),data:{function:`${p}::router::register_subdomain`,functionArguments:[y,h,Math.round(b/1e3),t.policy==="subdomain:follow-domain"?1:0,!!c,o,s]},options:a})}async function Mi(n){let{aptosConfig:e,name:t}=n,r=st(e),{domainName:i,subdomainName:o}=St(t);try{let s=await ae({aptosConfig:e,payload:{function:`${r}::router::get_expiration`,functionArguments:[i,o]}});return Number(s[0])*1e3}catch (e7){return}}async function Oa(n){let{aptosConfig:e,address:t}=n,r=st(e),i=await ae({aptosConfig:e,payload:{function:`${r}::router::get_primary_name`,functionArguments:[_chunkZMDE3DNLjs.Y.from(t).toString()]}}),o=Mr(i[1]),s=Mr(i[0]);if(o)return[s,o].filter(Boolean).join(".")}async function Fa(n){let{aptosConfig:e,sender:t,name:r,options:i}=n,o=st(e);if(!r)return await T({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${o}::router::clear_primary_name`,functionArguments:[]},options:i});let{domainName:s,subdomainName:a}=St(r);return await T({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${o}::router::set_primary_name`,functionArguments:[s,a]},options:i})}async function Ga(n){let{aptosConfig:e,name:t}=n,r=st(e),{domainName:i,subdomainName:o}=St(t),s=await ae({aptosConfig:e,payload:{function:`${r}::router::get_target_addr`,functionArguments:[i,o]}}),a=Mr(s[0]);return a?_chunkZMDE3DNLjs.Y.from(a):void 0}async function Ba(n){let{aptosConfig:e,sender:t,name:r,address:i,options:o}=n,s=st(e),{domainName:a,subdomainName:c}=St(r);return await T({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${s}::router::set_target_addr`,functionArguments:[a,c,i]},options:o})}async function Ma(n){let{aptosConfig:e,name:t}=n,{domainName:r,subdomainName:i=""}=St(t),a=(await w({aptosConfig:e,query:{query:rn,variables:{where_condition:{domain:{_eq:r},subdomain:{_eq:i}},limit:1}},originMethod:"getName"})).current_aptos_names[0];return a&&(a=Vn(a)),Da(a)?a:void 0}async function Va(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Vi({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountNames",query:{query:rn,variables:{limit:_optionalChain([t, 'optionalAccess', _221 => _221.limit]),offset:_optionalChain([t, 'optionalAccess', _222 => _222.offset]),order_by:_optionalChain([t, 'optionalAccess', _223 => _223.orderBy]),where_condition:{..._nullishCoalesce(_optionalChain([n, 'access', _224 => _224.options, 'optionalAccess', _225 => _225.where]), () => ({})),owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i}}}}})).current_aptos_names.map(Vn)}async function Ha(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Vi({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountDomains",query:{query:rn,variables:{limit:_optionalChain([t, 'optionalAccess', _226 => _226.limit]),offset:_optionalChain([t, 'optionalAccess', _227 => _227.offset]),order_by:_optionalChain([t, 'optionalAccess', _228 => _228.orderBy]),where_condition:{..._nullishCoalesce(_optionalChain([n, 'access', _229 => _229.options, 'optionalAccess', _230 => _230.where]), () => ({})),owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i},subdomain:{_eq:""}}}}})).current_aptos_names.map(Vn)}async function La(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Vi({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountSubdomains",query:{query:rn,variables:{limit:_optionalChain([t, 'optionalAccess', _231 => _231.limit]),offset:_optionalChain([t, 'optionalAccess', _232 => _232.offset]),order_by:_optionalChain([t, 'optionalAccess', _233 => _233.orderBy]),where_condition:{..._nullishCoalesce(_optionalChain([n, 'access', _234 => _234.options, 'optionalAccess', _235 => _235.where]), () => ({})),owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i},subdomain:{_neq:""}}}}})).current_aptos_names.map(Vn)}async function qa(n){let{aptosConfig:e,options:t,domain:r}=n;return(await w({aptosConfig:e,originMethod:"getDomainSubdomains",query:{query:rn,variables:{limit:_optionalChain([t, 'optionalAccess', _236 => _236.limit]),offset:_optionalChain([t, 'optionalAccess', _237 => _237.offset]),order_by:_optionalChain([t, 'optionalAccess', _238 => _238.orderBy]),where_condition:{..._nullishCoalesce(_optionalChain([n, 'access', _239 => _239.options, 'optionalAccess', _240 => _240.where]), () => ({})),domain:{_eq:r},subdomain:{_neq:""}}}}})).current_aptos_names.map(Vn).filter(Da)}async function Vi(n){let{aptosConfig:e}=n,t=st(e),[r]=await ae({aptosConfig:e,payload:{function:`${t}::config::reregistration_grace_sec`,functionArguments:[]}}),i=r/60/60/24,o=()=>new Date;return new Date(o().setDate(o().getDate()-i)).toISOString()}async function $a(n){let{aptosConfig:e,sender:t,name:r,years:i=1,options:o}=n,s=st(e),a=i*31536e3,{domainName:c,subdomainName:p}=St(r);if(p)throw new Error("Subdomains cannot be renewed");if(i!==1)throw new Error("Currently, only 1 year renewals are supported");return await T({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${s}::router::renew_domain`,functionArguments:[c,a]},options:o})}function Vn(n){return{...n,expiration_timestamp:new Date(n.expiration_timestamp).getTime()}}var Hn=class{constructor(e){this.config=e}async getOwnerAddress(e){return za({aptosConfig:this.config,...e})}async getExpiration(e){return Mi({aptosConfig:this.config,...e})}async getTargetAddress(e){return Ga({aptosConfig:this.config,...e})}async setTargetAddress(e){return Ba({aptosConfig:this.config,...e})}async getPrimaryName(e){return Oa({aptosConfig:this.config,...e})}async setPrimaryName(e){return Fa({aptosConfig:this.config,...e})}async registerName(e){return Na({aptosConfig:this.config,...e})}async renewDomain(e){return $a({aptosConfig:this.config,...e})}async getName(e){return Ma({aptosConfig:this.config,...e})}async getAccountNames(e){return Va({aptosConfig:this.config,...e})}async getAccountDomains(e){return Ha({aptosConfig:this.config,...e})}async getAccountSubdomains(e){return La({aptosConfig:this.config,...e})}async getDomainSubdomains(e){return qa({aptosConfig:this.config,...e})}};async function Wa(n){let{aptosConfig:e,poolAddress:t}=n,r=_chunkZMDE3DNLjs.Y.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:Ti,variables:{where_condition:{pool_address:{_eq:r}}}}});return o.num_active_delegator_per_pool[0]?o.num_active_delegator_per_pool[0].num_active_delegator:0}async function Qa(n){let{aptosConfig:e,options:t}=n,r={query:Ti,variables:{order_by:_optionalChain([t, 'optionalAccess', _241 => _241.orderBy])}};return(await w({aptosConfig:e,query:r})).num_active_delegator_per_pool}async function ja(n){let{aptosConfig:e,delegatorAddress:t,poolAddress:r}=n,i={query:Xo,variables:{delegatorAddress:_chunkZMDE3DNLjs.Y.from(t).toStringLong(),poolAddress:_chunkZMDE3DNLjs.Y.from(r).toStringLong()}};return(await w({aptosConfig:e,query:i})).delegated_staking_activities}var Ln=class{constructor(e){this.config=e}async getNumberOfDelegators(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _242 => _242.minimumLedgerVersion]),processorType:"stake_processor"}),Wa({aptosConfig:this.config,...e})}async getNumberOfDelegatorsForAllPools(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _243 => _243.minimumLedgerVersion]),processorType:"stake_processor"}),Qa({aptosConfig:this.config,...e})}async getDelegatedStakingActivities(e){return await E({config:this.config,minimumLedgerVersion:_optionalChain([e, 'optionalAccess', _244 => _244.minimumLedgerVersion]),processorType:"stake_processor"}),ja({aptosConfig:this.config,...e})}};var Vr=class{constructor(e){this.config=e}async simple(e){return T({aptosConfig:this.config,...e})}async multiAgent(e){return T({aptosConfig:this.config,...e})}};function Hi(n,e,t){let r=t.value;return t.value=async function(...i){let[o]=i;if(o.transaction.feePayerAddress&&!o.feePayerAuthenticator)throw new Error("You are submitting a Fee Payer transaction but missing the feePayerAuthenticator");return r.apply(this,i)},t}function Li(n,e,t){let r=t.value;return t.value=async function(...i){return r.apply(this,i)},t}var cn=class{constructor(e){this.config=e}async simple(e){return Oi({aptosConfig:this.config,...e})}async multiAgent(e){return Oi({aptosConfig:this.config,...e})}};_chunkZMDE3DNLjs.a.call(void 0, [Li],cn.prototype,"simple",1),_chunkZMDE3DNLjs.a.call(void 0, [Li],cn.prototype,"multiAgent",1);var un=class{constructor(e){this.config=e}async simple(e){return kn({aptosConfig:this.config,...e})}async multiAgent(e){return kn({aptosConfig:this.config,...e})}};_chunkZMDE3DNLjs.a.call(void 0, [Hi],un.prototype,"simple",1),_chunkZMDE3DNLjs.a.call(void 0, [Hi],un.prototype,"multiAgent",1);var Hr=class{constructor(e,t,r,i,o){this.lastUncommintedNumber=null;this.currentNumber=null;this.lock=!1;this.aptosConfig=e,this.account=t,this.maxWaitTime=r,this.maximumInFlight=i,this.sleepTime=o}async nextSequenceNumber(){for(;this.lock;)await _chunkZMDE3DNLjs.aa.call(void 0, this.sleepTime);this.lock=!0;let e=BigInt(0);try{if((this.lastUncommintedNumber===null||this.currentNumber===null)&&await this.initialize(),this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight){await this.update();let t=_chunkZMDE3DNLjs.ca.call(void 0, );for(;this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight;)await _chunkZMDE3DNLjs.aa.call(void 0, this.sleepTime),_chunkZMDE3DNLjs.ca.call(void 0, )-t>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, re-syncing ${this.account.accountAddress.toString()}`),await this.initialize()):await this.update()}e=this.currentNumber,this.currentNumber+=BigInt(1)}catch(t){console.error("error in getting next sequence number for this account",t)}finally{this.lock=!1}return e}async initialize(){let{sequence_number:e}=await Mt({aptosConfig:this.aptosConfig,accountAddress:this.account.accountAddress});this.currentNumber=BigInt(e),this.lastUncommintedNumber=BigInt(e)}async update(){let{sequence_number:e}=await Mt({aptosConfig:this.aptosConfig,accountAddress:this.account.accountAddress});return this.lastUncommintedNumber=BigInt(e),this.lastUncommintedNumber}async synchronize(){if(this.lastUncommintedNumber!==this.currentNumber){for(;this.lock;)await _chunkZMDE3DNLjs.aa.call(void 0, this.sleepTime);this.lock=!0;try{await this.update();let e=_chunkZMDE3DNLjs.ca.call(void 0, );for(;this.lastUncommintedNumber!==this.currentNumber;)_chunkZMDE3DNLjs.ca.call(void 0, )-e>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, re-syncing ${this.account.accountAddress.toString()}`),await this.initialize()):(await _chunkZMDE3DNLjs.aa.call(void 0, this.sleepTime),await this.update())}catch(e){console.error("error in synchronizing this account sequence number with the one on chain",e)}finally{this.lock=!1}}}};var pn=class{constructor(){this.queue=[];this.pendingDequeue=[];this.cancelled=!1}enqueue(e){if(this.cancelled=!1,this.pendingDequeue.length>0){_optionalChain([this, 'access', _245 => _245.pendingDequeue, 'access', _246 => _246.shift, 'call', _247 => _247(), 'optionalAccess', _248 => _248.resolve, 'call', _249 => _249(e)]);return}this.queue.push(e)}async dequeue(){return this.queue.length>0?Promise.resolve(this.queue.shift()):new Promise((e,t)=>{this.pendingDequeue.push({resolve:e,reject:t})})}isEmpty(){return this.queue.length===0}cancel(){this.cancelled=!0,this.pendingDequeue.forEach(async({reject:e})=>{e(new dn("Task cancelled"))}),this.pendingDequeue=[],this.queue.length=0}isCancelled(){return this.cancelled}pendingDequeueLength(){return this.pendingDequeue.length}},dn=class extends Error{};var Ja="fulfilled",Xa= exports.TransactionWorkerEventsEnum =(o=>(o.TransactionSent="transactionSent",o.TransactionSendFailed="transactionSendFailed",o.TransactionExecuted="transactionExecuted",o.TransactionExecutionFailed="transactionExecutionFailed",o.ExecutionFinish="executionFinish",o))(Xa||{}),Lr= exports.TransactionWorker =class extends _eventemitter32.default{constructor(t,r,i=30,o=100,s=10){super();this.taskQueue=new pn;this.transactionsQueue=new pn;this.outstandingTransactions=new pn;this.sentTransactions=[];this.executedTransactions=[];this.aptosConfig=t,this.account=r,this.started=!1,this.accountSequnceNumber=new Hr(t,r,i,o,s)}async submitNextTransaction(){try{for(;;){let t=await this.accountSequnceNumber.nextSequenceNumber();if(t===null)return;let r=await this.generateNextTransaction(this.account,t);if(!r)return;let i=bt({aptosConfig:this.aptosConfig,transaction:r,signer:this.account});await this.outstandingTransactions.enqueue([i,t])}}catch(t){if(t instanceof dn)return;throw new Error(`Submit transaction failed for ${this.account.accountAddress.toString()} with error ${t}`)}}async processTransactions(){try{for(;;){let t=[],r=[],[i,o]=await this.outstandingTransactions.dequeue();for(t.push(i),r.push(o);!this.outstandingTransactions.isEmpty();)[i,o]=await this.outstandingTransactions.dequeue(),t.push(i),r.push(o);let s=await Promise.allSettled(t);for(let a=0;a<s.length&&a<r.length;a+=1){let c=s[a];o=r[a],c.status===Ja?(this.sentTransactions.push([c.value.hash,o,null]),this.emit("transactionSent",{message:`transaction hash ${c.value.hash} has been committed to chain`,transactionHash:c.value.hash}),await this.checkTransaction(c,o)):(this.sentTransactions.push([c.status,o,c.reason]),this.emit("transactionSendFailed",{message:`failed to commit transaction ${this.sentTransactions.length} with error ${c.reason}`,error:c.reason}))}this.emit("executionFinish",{message:`execute ${s.length} transactions finished`})}}catch(t){if(t instanceof dn)return;throw new Error(`Process execution failed for ${this.account.accountAddress.toString()} with error ${t}`)}}async checkTransaction(t,r){try{let i=[];i.push(Tt({aptosConfig:this.aptosConfig,transactionHash:t.value.hash}));let o=await Promise.allSettled(i);for(let s=0;s<o.length;s+=1){let a=o[s];a.status===Ja?(this.executedTransactions.push([a.value.hash,r,null]),this.emit("transactionExecuted",{message:`transaction hash ${a.value.hash} has been executed on chain`,transactionHash:t.value.hash})):(this.executedTransactions.push([a.status,r,a.reason]),this.emit("transactionExecutionFailed",{message:`failed to execute transaction ${this.executedTransactions.length} with error ${a.reason}`,error:a.reason}))}}catch(i){throw new Error(`Check transaction failed for ${this.account.accountAddress.toString()} with error ${i}`)}}async push(t,r){this.transactionsQueue.enqueue([t,r])}async generateNextTransaction(t,r){if(this.transactionsQueue.isEmpty())return;let[i,o]=await this.transactionsQueue.dequeue();return T({aptosConfig:this.aptosConfig,sender:t.accountAddress,data:i,options:{...o,accountSequenceNumber:r}})}async run(){try{for(;!this.taskQueue.isCancelled();)await(await this.taskQueue.dequeue())()}catch(t){throw new Error(`Unable to start transaction batching: ${t}`)}}start(){if(this.started)throw new Error("worker has already started");this.started=!0,this.taskQueue.enqueue(()=>this.submitNextTransaction()),this.taskQueue.enqueue(()=>this.processTransactions()),this.run()}stop(){if(this.taskQueue.isCancelled())throw new Error("worker has already stopped");this.started=!1,this.taskQueue.cancel()}};var qr=class extends _eventemitter32.default{constructor(e){super(),this.config=e}start(e){let{sender:t}=e;this.account=t,this.transactionWorker=new Lr(this.config,t),this.transactionWorker.start(),this.registerToEvents()}push(e){let{data:t,options:r}=e;for(let i of t)this.transactionWorker.push(i,r)}registerToEvents(){this.transactionWorker.on("transactionSent",async e=>{this.emit("transactionSent",e)}),this.transactionWorker.on("transactionSendFailed",async e=>{this.emit("transactionSendFailed",e)}),this.transactionWorker.on("transactionExecuted",async e=>{this.emit("transactionExecuted",e)}),this.transactionWorker.on("transactionExecutionFailed",async e=>{this.emit("transactionExecutionFailed",e)}),this.transactionWorker.on("executionFinish",async e=>{this.emit("executionFinish",e)})}forSingleAccount(e){try{let{sender:t,data:r,options:i}=e;this.start({sender:t}),this.push({data:r,options:i})}catch(t){throw new Error(`failed to submit transactions with error: ${t}`)}}};var qn=class{constructor(e){this.config=e,this.build=new Vr(this.config),this.simulate=new cn(this.config),this.submit=new un(this.config),this.batch=new qr(this.config)}async getTransactions(e){return Ei({aptosConfig:this.config,...e})}async getTransactionByVersion(e){return ps({aptosConfig:this.config,...e})}async getTransactionByHash(e){return Rn({aptosConfig:this.config,...e})}async isPendingTransaction(e){return ds({aptosConfig:this.config,...e})}async waitForTransaction(e){return Tt({aptosConfig:this.config,...e})}async getGasPriceEstimation(){return Cr({aptosConfig:this.config})}getSigningMessage(e){return vs(e)}async publishPackageTransaction(e){return Ks({aptosConfig:this.config,...e})}async rotateAuthKey(e){return $s({aptosConfig:this.config,...e})}sign(e){return Or({...e})}signAsFeePayer(e){return Fr({...e})}async batchTransactionsForSingleAccount(e){try{let{sender:t,data:r,options:i}=e;this.batch.forSingleAccount({sender:t,data:r,options:i})}catch(t){throw new Error(`failed to submit transactions with error: ${t}`)}}async signAndSubmitTransaction(e){return bt({aptosConfig:this.config,...e})}async signAndSubmitAsFeePayer(e){return Cs({aptosConfig:this.config,...e})}};var $n=class{constructor(e){this.config=e}async getTableItem(e){return Ir({aptosConfig:this.config,...e})}async getTableItemsData(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),cs({aptosConfig:this.config,...e})}async getTableItemsMetadata(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),us({aptosConfig:this.config,...e})}};async function $r(n){let{aptosConfig:e,jwt:t,ephemeralKeyPair:r,uidKey:i="sub",derivationPath:o}=n,s={jwt_b64:t,epk:r.getPublicKey().bcsToHex().toStringWithoutPrefix(),exp_date_secs:r.expiryDateSecs,epk_blinder:_chunkZMDE3DNLjs.j.fromHexInput(r.blinder).toStringWithoutPrefix(),uid_key:i,derivation_path:o},{data:a}=await Ko({aptosConfig:e,path:"fetch",body:s,originMethod:"getPepper",overrides:{WITH_CREDENTIALS:!1}});return _chunkZMDE3DNLjs.j.fromHexInput(a.pepper).toUint8Array()}async function qi(n){let{aptosConfig:e,jwt:t,ephemeralKeyPair:r,pepper:i=await $r(n),uidKey:o="sub",maxExpHorizonSecs:s=(await Jt({aptosConfig:e})).maxExpHorizonSecs}=n;if(_chunkZMDE3DNLjs.j.fromHexInput(i).toUint8Array().length!==it.PEPPER_LENGTH)throw new Error(`Pepper needs to be ${it.PEPPER_LENGTH} bytes`);let a=_jwtdecode.jwtDecode.call(void 0, t);if(typeof a.iat!="number")throw new Error("iat was not found");if(s<r.expiryDateSecs-a.iat)throw Error(`The EphemeralKeyPair is too long lived.  It's lifespan must be less than ${s}`);let c={jwt_b64:t,epk:r.getPublicKey().bcsToHex().toStringWithoutPrefix(),epk_blinder:_chunkZMDE3DNLjs.j.fromHexInput(r.blinder).toStringWithoutPrefix(),exp_date_secs:r.expiryDateSecs,exp_horizon_secs:s,pepper:_chunkZMDE3DNLjs.j.fromHexInput(i).toStringWithoutPrefix(),uid_key:o},{data:p}=await Ro({aptosConfig:e,path:"prove",body:c,originMethod:"getProof",overrides:{WITH_CREDENTIALS:!1}}),y=p.proof,h=new yt({a:y.a,b:y.b,c:y.c});return new ue({proof:new jt(h,0),trainingWheelsSignature:Ne.fromHex(p.training_wheels_signature),expHorizonSecs:s})}async function Ya(n){let{aptosConfig:e,jwt:t,jwkAddress:r,uidKey:i,proofFetchCallback:o,pepper:s=await $r(n)}=n,{verificationKey:a,maxExpHorizonSecs:c}=await Jt({aptosConfig:e}),p=qi({...n,pepper:s,maxExpHorizonSecs:c}),y=o?p:await p;if(r!==void 0){let l=$.fromJwtAndPepper({jwt:t,pepper:s,jwkAddress:r,uidKey:i}),b=await sn({aptosConfig:e,authenticationKey:l.authKey().derivedAddress()});return Gt.create({...n,address:b,proof:y,pepper:s,proofFetchCallback:o,jwkAddress:r,verificationKey:a})}let h=K.fromJwtAndPepper({jwt:t,pepper:s,uidKey:i}),d=await sn({aptosConfig:e,authenticationKey:h.authKey().derivedAddress()});return it.create({...n,address:d,proof:y,pepper:s,proofFetchCallback:o,verificationKey:a})}async function Za(n){let{aptosConfig:e,sender:t,iss:r,options:i}=n,{jwksUrl:o}=n;o===void 0&&(_o.test(r)?o="https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>":o=r.endsWith("/")?`${r}.well-known/jwks.json`:`${r}/.well-known/jwks.json`);let s;try{if(s=await fetch(o),!s.ok)throw new Error(`${s.status} ${s.statusText}`)}catch(c){let p;throw c instanceof Error?p=`${c.message}`:p=`error unknown - ${c}`,P.fromErrorType({type:14,details:`Failed to fetch JWKS at ${o}: ${p}`})}let a=await s.json();return T({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::jwks::update_federated_jwk_set",functionArguments:[r,S.MoveString(a.keys.map(c=>c.kid)),S.MoveString(a.keys.map(c=>c.alg)),S.MoveString(a.keys.map(c=>c.e)),S.MoveString(a.keys.map(c=>c.n))]},options:i})}var Wn=class{constructor(e){this.config=e}async getPepper(e){return $r({aptosConfig:this.config,...e})}async getProof(e){return qi({aptosConfig:this.config,...e})}async deriveKeylessAccount(e){return Ya({aptosConfig:this.config,...e})}async updateFederatedKeylessJwkSetTransaction(e){return Za({aptosConfig:this.config,...e})}};async function lp(n){let{aptosConfig:e,options:t}=n,r={query:Sr,variables:{where_condition:_optionalChain([t, 'optionalAccess', _250 => _250.where]),offset:_optionalChain([t, 'optionalAccess', _251 => _251.offset]),limit:_optionalChain([t, 'optionalAccess', _252 => _252.limit]),order_by:_optionalChain([t, 'optionalAccess', _253 => _253.orderBy])}};return(await w({aptosConfig:e,query:r,originMethod:"getObjectData"})).current_objects}async function ec(n){let{aptosConfig:e,objectAddress:t,options:r}=n,o={object_address:{_eq:_chunkZMDE3DNLjs.Y.from(t).toStringLong()}};return(await lp({aptosConfig:e,options:{...r,where:o}}))[0]}var Qn=class{constructor(e){this.config=e}async getObjectDataByObjectAddress(e){return await E({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"objects_processor"}),ec({aptosConfig:this.config,...e})}};var ee=class{constructor(e){this.config=new Gr(e),this.account=new Dn(this.config),this.abstraction=new Vt(this.config),this.ans=new Hn(this.config),this.coin=new zn(this.config),this.digitalAsset=new Nn(this.config),this.event=new Fn(this.config),this.faucet=new Gn(this.config),this.fungibleAsset=new Bn(this.config),this.general=new Mn(this.config),this.staking=new Ln(this.config),this.transaction=new qn(this.config),this.table=new $n(this.config),this.keyless=new Wn(this.config),this.object=new Qn(this.config)}};function ce(n,e,t){Object.getOwnPropertyNames(e.prototype).forEach(r=>{let i=Object.getOwnPropertyDescriptor(e.prototype,r);i&&(i.value=function(...o){return this[t][r](...o)},Object.defineProperty(n.prototype,r,i))})}ce(ee,Dn,"account");ce(ee,Vt,"abstraction");ce(ee,Hn,"ans");ce(ee,zn,"coin");ce(ee,Nn,"digitalAsset");ce(ee,Fn,"event");ce(ee,Gn,"faucet");ce(ee,Bn,"fungibleAsset");ce(ee,Mn,"general");ce(ee,Ln,"staking");ce(ee,qn,"transaction");ce(ee,$n,"table");ce(ee,Wn,"keyless");ce(ee,Qn,"object");exports.APTOS_BIP44_REGEX = gc; exports.APTOS_COIN = It; exports.APTOS_FA = Eo; exports.APTOS_HARDENED_REGEX = lc; exports.AbstractKeylessAccount = me; exports.AbstractMultiKey = wn; exports.AbstractPublicKey = Zn; exports.AbstractSignature = mn; exports.AbstractedAccount = br; exports.Account = rt; exports.AccountAddress = _chunkZMDE3DNLjs.Y; exports.AccountAuthenticator = J; exports.AccountAuthenticatorAbstraction = Ct; exports.AccountAuthenticatorEd25519 = ke; exports.AccountAuthenticatorMultiEd25519 = Je; exports.AccountAuthenticatorMultiKey = Me; exports.AccountAuthenticatorNoAccountAuthenticator = Yt; exports.AccountAuthenticatorSingleKey = oe; exports.AccountAuthenticatorVariant = _chunkZMDE3DNLjs.w; exports.AccountPublicKey = re; exports.AccountSequenceNumber = Hr; exports.AccountUtils = Fo; exports.AddressInvalidReason = _chunkZMDE3DNLjs.X; exports.AnyPublicKey = N; exports.AnyPublicKeyVariant = _chunkZMDE3DNLjs.y; exports.AnySignature = O; exports.AnySignatureVariant = _chunkZMDE3DNLjs.z; exports.Aptos = ee; exports.AptosApiError = he; exports.AptosApiType = Wt; exports.AptosConfig = Gr; exports.AuthenticationKey = M; exports.Bool = z; exports.CKDPriv = go; exports.ChainId = Zt; exports.DEFAULT_MAX_GAS_AMOUNT = wo; exports.DEFAULT_TXN_EXP_SEC_FROM_NOW = So; exports.DEFAULT_TXN_TIMEOUT_SEC = rr; exports.DerivableAbstractedAccount = Bo; exports.DeriveScheme = _chunkZMDE3DNLjs.W; exports.Deserializer = U; exports.EPK_HORIZON_SECS = Wc; exports.Ed25519Account = se; exports.Ed25519PrivateKey = q; exports.Ed25519PublicKey = I; exports.Ed25519Signature = v; exports.EntryFunction = Ut; exports.EntryFunctionBytes = Yn; exports.EphemeralCertificate = Qt; exports.EphemeralCertificateVariant = _chunkZMDE3DNLjs.C; exports.EphemeralKeyPair = Ft; exports.EphemeralPublicKey = gt; exports.EphemeralPublicKeyVariant = _chunkZMDE3DNLjs.A; exports.EphemeralSignature = Ne; exports.EphemeralSignatureVariant = _chunkZMDE3DNLjs.B; exports.FIREBASE_AUTH_ISS_PATTERN = _o; exports.FederatedKeylessAccount = Gt; exports.FederatedKeylessPublicKey = $; exports.FeePayerRawTransaction = Dt; exports.FixedBytes = _e; exports.Groth16ProofAndStatement = di; exports.Groth16VerificationKey = gi; exports.Groth16Zkp = yt; exports.HARDENED_OFFSET = uo; exports.Hex = _chunkZMDE3DNLjs.j; exports.HexInvalidReason = _chunkZMDE3DNLjs.i; exports.Identifier = k; exports.KeyType = mc; exports.KeylessAccount = it; exports.KeylessConfiguration = li; exports.KeylessError = P; exports.KeylessErrorCategory = Gc; exports.KeylessErrorResolutionTip = Bc; exports.KeylessErrorType = ir; exports.KeylessPublicKey = K; exports.KeylessSignature = ie; exports.MAX_AUD_VAL_BYTES = ui; exports.MAX_COMMITED_EPK_BYTES = Zc; exports.MAX_EXTRA_FIELD_BYTES = Xc; exports.MAX_ISS_VAL_BYTES = Jc; exports.MAX_JWT_HEADER_B64_BYTES = Yc; exports.MAX_UID_KEY_BYTES = Qc; exports.MAX_UID_VAL_BYTES = jc; exports.MimeType = _chunkZMDE3DNLjs.q; exports.ModuleId = Sn; exports.MoveAbility = _chunkZMDE3DNLjs.S; exports.MoveFunctionVisibility = _chunkZMDE3DNLjs.R; exports.MoveJWK = mi; exports.MoveOption = Z; exports.MoveString = x; exports.MoveVector = S; exports.MultiAgentRawTransaction = kt; exports.MultiAgentTransaction = yr; exports.MultiEd25519Account = Tr; exports.MultiEd25519PublicKey = At; exports.MultiEd25519Signature = Ue; exports.MultiKey = Te; exports.MultiKeyAccount = Cn; exports.MultiKeySignature = be; exports.MultiSig = In; exports.MultiSigTransactionPayload = vn; exports.Network = ri; exports.NetworkToChainId = ii; exports.NetworkToFaucetAPI = bo; exports.NetworkToIndexerAPI = ho; exports.NetworkToNetworkName = _l; exports.NetworkToNodeAPI = To; exports.NetworkToPepperAPI = ti; exports.NetworkToProverAPI = ni; exports.ParsingError = _chunkZMDE3DNLjs.h; exports.PrivateKey = dt; exports.PrivateKeyVariants = _chunkZMDE3DNLjs.x; exports.ProcessorType = qe; exports.PublicKey = Pt; exports.RAW_TRANSACTION_SALT = Po; exports.RAW_TRANSACTION_WITH_DATA_SALT = oi; exports.RawTransaction = Se; exports.RawTransactionWithData = lr; exports.RoleType = _chunkZMDE3DNLjs.T; exports.RotationProofChallenge = gr; exports.Script = xn; exports.ScriptTransactionArgumentVariants = _chunkZMDE3DNLjs.s; exports.Secp256k1PrivateKey = Qe; exports.Secp256k1PublicKey = Re; exports.Secp256k1Signature = je; exports.Serializable = _chunkZMDE3DNLjs.l; exports.Serialized = gn; exports.Serializer = _chunkZMDE3DNLjs.m; exports.Signature = V; exports.SignedTransaction = nt; exports.SigningScheme = _chunkZMDE3DNLjs.U; exports.SigningSchemeInput = _chunkZMDE3DNLjs.V; exports.SimpleTransaction = mr; exports.SingleKeyAccount = Ee; exports.StructTag = Ve; exports.TransactionAndProof = Ar; exports.TransactionAuthenticator = tt; exports.TransactionAuthenticatorEd25519 = zt; exports.TransactionAuthenticatorFeePayer = Ot; exports.TransactionAuthenticatorMultiAgent = Nt; exports.TransactionAuthenticatorMultiEd25519 = en; exports.TransactionAuthenticatorSingleSender = ht; exports.TransactionAuthenticatorVariant = _chunkZMDE3DNLjs.v; exports.TransactionPayload = Rt; exports.TransactionPayloadEntryFunction = Pn; exports.TransactionPayloadMultiSig = _n; exports.TransactionPayloadScript = En; exports.TransactionPayloadVariants = _chunkZMDE3DNLjs.t; exports.TransactionResponseType = _chunkZMDE3DNLjs.E; exports.TransactionVariants = _chunkZMDE3DNLjs.u; exports.TransactionWorker = Lr; exports.TransactionWorkerEventsEnum = Xa; exports.TypeTag = G; exports.TypeTagAddress = F; exports.TypeTagBool = L; exports.TypeTagGeneric = B; exports.TypeTagParserError = W; exports.TypeTagParserErrorType = Eu; exports.TypeTagReference = dr; exports.TypeTagSigner = Kt; exports.TypeTagStruct = A; exports.TypeTagU128 = Ze; exports.TypeTagU16 = Xe; exports.TypeTagU256 = et; exports.TypeTagU32 = Ye; exports.TypeTagU64 = X; exports.TypeTagU8 = le; exports.TypeTagVariants = _chunkZMDE3DNLjs.r; exports.TypeTagVector = C; exports.U128 = ve; exports.U16 = xe; exports.U256 = fe; exports.U32 = Ie; exports.U64 = j; exports.U8 = Y; exports.ZeroKnowledgeSig = ue; exports.ZkProof = jt; exports.ZkpVariant = _chunkZMDE3DNLjs.D; exports.aptosCoinStructTag = Zy; exports.aptosRequest = or; exports.base64UrlDecode = _chunkZMDE3DNLjs.ea; exports.base64UrlToBytes = _chunkZMDE3DNLjs.fa; exports.bigIntToBytesLE = ei; exports.buildTransaction = zi; exports.bytesToBigIntLE = _t; exports.checkOrConvertArgument = Bt; exports.convertAmountFromHumanReadableToOnChain = _chunkZMDE3DNLjs.ga; exports.convertAmountFromOnChainToHumanReadable = _chunkZMDE3DNLjs.ha; exports.convertArgument = Di; exports.convertNumber = Rr; exports.createObjectAddress = _chunkZMDE3DNLjs.Z; exports.createResourceAddress = _chunkZMDE3DNLjs._; exports.createTokenAddress = _chunkZMDE3DNLjs.$; exports.deriveKey = Xr; exports.deriveTransactionType = fr; exports.deserializeFromScriptArgument = cu; exports.deserializePublicKey = ny; exports.deserializeSignature = ry; exports.ensureBoolean = _chunkZMDE3DNLjs.n; exports.fetchEntryFunctionAbi = Ss; exports.fetchFunctionAbi = ki; exports.fetchJWK = fi; exports.fetchModuleAbi = Iu; exports.fetchMoveFunctionAbi = $b; exports.fetchViewFunctionAbi = Es; exports.findFirstNonSignerArg = ws; exports.floorToWholeHour = _chunkZMDE3DNLjs.da; exports.generateRawTransaction = ku; exports.generateSignedTransaction = Ni; exports.generateSignedTransactionForSimulation = _s; exports.generateSigningMessage = ft; exports.generateSigningMessageForSerializable = lA; exports.generateSigningMessageForTransaction = De; exports.generateTransactionPayload = Nr; exports.generateTransactionPayloadWithABI = Ku; exports.generateUserTransactionHash = Aw; exports.generateViewFunctionPayload = Ps; exports.generateViewFunctionPayloadWithABI = Ru; exports.get = sr; exports.getAptosFullNode = H; exports.getAptosPepperService = Yl; exports.getAuthenticatorForSimulation = on; exports.getErrorMessage = _chunkZMDE3DNLjs.ba; exports.getFunctionParts = _chunkZMDE3DNLjs.ka; exports.getIssAudAndUidVal = We; exports.getKeylessConfig = Jt; exports.getKeylessJWKs = ru; exports.getPageWithObfuscatedCursor = cr; exports.hashStrToField = Oe; exports.hashValues = xs; exports.hexToAsciiString = _chunkZMDE3DNLjs.k; exports.isBcsAddress = kr; exports.isBcsBool = Pi; exports.isBcsFixedBytes = _u; exports.isBcsString = _i; exports.isBcsU128 = Ki; exports.isBcsU16 = Ii; exports.isBcsU256 = Ri; exports.isBcsU32 = vi; exports.isBcsU64 = Ci; exports.isBcsU8 = xi; exports.isBlockEpilogueTransactionResponse = _chunkZMDE3DNLjs.L; exports.isBlockMetadataTransactionResponse = _chunkZMDE3DNLjs.I; exports.isBool = As; exports.isCanonicalEd25519Signature = fc; exports.isEd25519Signature = _chunkZMDE3DNLjs.M; exports.isEmptyOption = hs; exports.isEncodedEntryFunctionArgument = Ts; exports.isEncodedStruct = _chunkZMDE3DNLjs.ja; exports.isFeePayerSignature = _chunkZMDE3DNLjs.P; exports.isGenesisTransactionResponse = _chunkZMDE3DNLjs.H; exports.isKeylessSigner = hr; exports.isLargeNumber = Ur; exports.isMultiAgentSignature = _chunkZMDE3DNLjs.O; exports.isMultiEd25519Signature = _chunkZMDE3DNLjs.Q; exports.isNumber = Pu; exports.isPendingTransactionResponse = _chunkZMDE3DNLjs.F; exports.isScriptDataInput = bs; exports.isSecp256k1Signature = _chunkZMDE3DNLjs.N; exports.isSingleKeySigner = Ai; exports.isStateCheckpointTransactionResponse = _chunkZMDE3DNLjs.J; exports.isString = ot; exports.isUserTransactionResponse = _chunkZMDE3DNLjs.G; exports.isValidBIP44Path = po; exports.isValidFunctionInfo = _chunkZMDE3DNLjs.la; exports.isValidHardenedPath = lo; exports.isValidatorTransactionResponse = _chunkZMDE3DNLjs.K; exports.mnemonicToSeed = er; exports.normalizeBundle = xo; exports.nowInSeconds = _chunkZMDE3DNLjs.ca; exports.objectStructTag = ge; exports.optionStructTag = ef; exports.outOfRangeErrorMessage = _chunkZMDE3DNLjs.o; exports.padAndPackBytesWithLen = fn; exports.paginateWithCursor = ar; exports.paginateWithObfuscatedCursor = ai; exports.pairedFaMetadataAddress = _chunkZMDE3DNLjs.na; exports.parseEncodedStruct = _chunkZMDE3DNLjs.ia; exports.parseJwtHeader = ou; exports.parseTypeTag = ze; exports.poseidonHash = xt; exports.post = An; exports.postAptosFaucet = Co; exports.postAptosFullNode = mt; exports.postAptosIndexer = vo; exports.postAptosPepperService = Ko; exports.postAptosProvingService = Ro; exports.promiseFulfilledStatus = Ja; exports.request = Hc; exports.sleep = _chunkZMDE3DNLjs.aa; exports.splitPath = mo; exports.standardizeTypeTags = Dr; exports.stringStructTag = _; exports.throwTypeMismatch = D; exports.truncateAddress = _chunkZMDE3DNLjs.ma; exports.validateNumberInRange = _chunkZMDE3DNLjs.p; exports.verifyKeylessSignature = yi; exports.verifyKeylessSignatureWithJwkAndConfig = ur;
//# sourceMappingURL=index.js.map