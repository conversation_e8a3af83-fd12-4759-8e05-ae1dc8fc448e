{"version": 3, "file": "eip_712.js", "sourceRoot": "", "sources": ["../../src/eip_712.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAkDF,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,MAAM,UAAU,GAAG,MAAM,CAAC;AAC1B,MAAM,WAAW,GAAG,oBAAoB,CAAC;AAEzC;;;GAGG;AACH,MAAM,eAAe,GAAG,CACvB,SAA0B,EAC1B,IAAY,EACZ,eAAyB,EAAE,EAChB,EAAE;IACb,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAE,CAAC;IACtC,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QACvC,OAAO,YAAY,CAAC;IACrB,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;QAClC,OAAO,YAAY,CAAC;IACrB,CAAC;IAED,OAAO;QACN,UAAU;QACV,GAAG,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CACpC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;YACpB,GAAG,QAAQ;YACX,GAAG,eAAe,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,MAAM,CACzD,UAAU,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC5C;SACD,EACD,EAAE,CACF;KACD,CAAC;AACH,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,UAAU,GAAG,CAAC,SAA0B,EAAE,IAAY,EAAU,EAAE;IACvE,MAAM,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,GAAG,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACpE,yEAAyE;IACzE,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IAEhD,OAAO,KAAK;SACV,GAAG,CACH,UAAU,CAAC,EAAE;IACZ,4EAA4E;IAC5E,GAAG,UAAU,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAC/C,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,CACtC,GAAG,CACL;SACA,IAAI,CAAC,EAAE,CAAC,CAAC;AACZ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,GAAG,CAAC,SAA0B,EAAE,IAAY,EAAE,EAAE,CAChE,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAExC;;;GAGG;AACH,MAAM,aAAa,GAAG,CACrB,SAA0B,EAC1B,IAAY,EACZ,IAA6B,EAEpB,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAE1D;;;GAGG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,SAA0B,EAAE,IAAc,EAAU,EAAE;IAChF,MAAM,cAAc,GAAG,MAAM,CAAC;IAC9B,MAAM,OAAO,GAAG,KAAK,cAAc,GAAG,aAAa,CAClD,SAAS,EACT,cAAc,EACd,SAAS,CAAC,MAAiC,CAC3C,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,SAAS,CAC5F,CAAC,CACD,EAAE,CAAC;IAEJ,IAAI,IAAI,EAAE,CAAC;QACV,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,WAAW,GAAG,CACnB,SAA0B,EAC1B,IAAY,EACZ,IAAa,EAC4B,EAAE;IAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAEtC,yBAAyB;IACzB,IAAI,KAAK,EAAE,CAAC;QACX,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;QAE7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,QAAQ,CAAC,gDAAgD,EAAE;gBACpE,IAAI;aACJ,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACtC,MAAM,IAAI,QAAQ,CACjB,0CAA0C,MAAM,aAAa,IAAI,CAAC,MAAM,EAAE,EAC1E;gBACC,IAAI;aACJ,CACD,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9E,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,SAAS,EAAE,IAAI,EAAE,IAA+B,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,0DAA0D;IAC1D,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;QACvB,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAc,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;QACtB,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,IAAc,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,IAAc,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,UAAU,GAAG,CAClB,SAA0B,EAC1B,IAAY,EACZ,IAA6B,EACpB,EAAE;IACX,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CACnD,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE;QAC5B,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,QAAQ,CAAC,yCAAyC,KAAK,CAAC,IAAI,GAAG,EAAE;gBAC1E,IAAI;gBACJ,KAAK;aACL,CAAC,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAExE,OAAO;YACN,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC;YAClB,CAAC,GAAG,OAAO,EAAE,YAAY,CAAC;SAC1B,CAAC;IACH,CAAC,EACD,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAC7C,CAAC;IAEF,OAAO,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC,CAAC"}