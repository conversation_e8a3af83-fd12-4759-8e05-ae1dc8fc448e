"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDataFee = void 0;
const util_js_1 = require("../util.js");
const Legacy = require("./legacy.js");
/**
 * The amount of gas paid for the data in this tx
 */
function getDataFee(tx) {
    const eip2930Cost = BigInt(util_js_1.AccessLists.getDataFeeEIP2930(tx.accessList, tx.common));
    const eip7702Cost = BigInt(tx.authorizationList.length * Number(tx.common.param('gasPrices', 'perAuthBaseCost')));
    return Legacy.getDataFee(tx, eip2930Cost + eip7702Cost);
}
exports.getDataFee = getDataFee;
//# sourceMappingURL=eip7702.js.map