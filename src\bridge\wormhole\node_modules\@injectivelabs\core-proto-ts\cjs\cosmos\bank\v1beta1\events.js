"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceUpdate = exports.EventSetBalances = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.bank.v1beta1";
function createBaseEventSetBalances() {
    return { balanceUpdates: [] };
}
exports.EventSetBalances = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.balanceUpdates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.BalanceUpdate.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSetBalances();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balanceUpdates.push(exports.BalanceUpdate.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            balanceUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.balanceUpdates)
                ? object.balanceUpdates.map(function (e) { return exports.BalanceUpdate.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.balanceUpdates) {
            obj.balanceUpdates = message.balanceUpdates.map(function (e) { return e ? exports.BalanceUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.balanceUpdates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventSetBalances.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventSetBalances();
        message.balanceUpdates = ((_a = object.balanceUpdates) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.BalanceUpdate.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseBalanceUpdate() {
    return { addr: new Uint8Array(), denom: new Uint8Array(), amt: "" };
}
exports.BalanceUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.addr.length !== 0) {
            writer.uint32(10).bytes(message.addr);
        }
        if (message.denom.length !== 0) {
            writer.uint32(18).bytes(message.denom);
        }
        if (message.amt !== "") {
            writer.uint32(26).string(message.amt);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBalanceUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.addr = reader.bytes();
                    break;
                case 2:
                    message.denom = reader.bytes();
                    break;
                case 3:
                    message.amt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            addr: isSet(object.addr) ? bytesFromBase64(object.addr) : new Uint8Array(),
            denom: isSet(object.denom) ? bytesFromBase64(object.denom) : new Uint8Array(),
            amt: isSet(object.amt) ? String(object.amt) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.addr !== undefined &&
            (obj.addr = base64FromBytes(message.addr !== undefined ? message.addr : new Uint8Array()));
        message.denom !== undefined &&
            (obj.denom = base64FromBytes(message.denom !== undefined ? message.denom : new Uint8Array()));
        message.amt !== undefined && (obj.amt = message.amt);
        return obj;
    },
    create: function (base) {
        return exports.BalanceUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBalanceUpdate();
        message.addr = (_a = object.addr) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.amt = (_c = object.amt) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
