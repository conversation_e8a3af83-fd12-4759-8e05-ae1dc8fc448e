import { InjectivePermissionsV1Beta1Tx, InjectivePermissionsV1Beta1Permissions } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { PermissionRole, PermissionRoleManager, PermissionPolicyStatus, PermissionPolicyManagerCapability } from './../../../../client/chain/types/permissions.js';
export declare namespace MsgUpdateNamespace {
    interface Params {
        sender: string;
        denom: string;
        contractHook?: string;
        rolePermissions: PermissionRole[];
        roleManagers: PermissionRoleManager[];
        policyStatuses: PermissionPolicyStatus[];
        policyManagerCapabilities: PermissionPolicyManagerCapability[];
    }
    type Proto = InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace;
}
/**
 * @category Messages
 */
export default class MsgUpdateNamespace extends MsgBase<MsgUpdateNamespace.Params, MsgUpdateNamespace.Proto> {
    static fromJSON(params: MsgUpdateNamespace.Params): MsgUpdateNamespace;
    toProto(): InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace;
    toData(): {
        sender: string;
        denom: string;
        contractHook: InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace_SetContractHook | undefined;
        rolePermissions: InjectivePermissionsV1Beta1Permissions.Role[];
        roleManagers: InjectivePermissionsV1Beta1Permissions.RoleManager[];
        policyStatuses: InjectivePermissionsV1Beta1Permissions.PolicyStatus[];
        policyManagerCapabilities: InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability[];
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            policy_statuses: InjectivePermissionsV1Beta1Permissions.PolicyStatus[];
            policy_manager_capabilities: InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability[];
            sender: string;
            denom: string;
            contract_hook: InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace_SetContractHook | undefined;
            role_permissions: InjectivePermissionsV1Beta1Permissions.Role[];
            role_managers: InjectivePermissionsV1Beta1Permissions.RoleManager[];
        };
    };
    toWeb3Gw(): {
        policy_statuses: InjectivePermissionsV1Beta1Permissions.PolicyStatus[];
        policy_manager_capabilities: InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability[];
        sender: string;
        denom: string;
        contract_hook: InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace_SetContractHook | undefined;
        role_permissions: InjectivePermissionsV1Beta1Permissions.Role[];
        role_managers: InjectivePermissionsV1Beta1Permissions.RoleManager[];
        '@type': string;
    };
    toEip712(): never;
    toEip712V2(): {
        policy_statuses: any[];
        policy_manager_capabilities: any[];
        sender: string;
        denom: string;
        contract_hook: InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace_SetContractHook | undefined;
        role_permissions: InjectivePermissionsV1Beta1Permissions.Role[];
        role_managers: InjectivePermissionsV1Beta1Permissions.RoleManager[];
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace;
    };
    toBinary(): Uint8Array;
}
