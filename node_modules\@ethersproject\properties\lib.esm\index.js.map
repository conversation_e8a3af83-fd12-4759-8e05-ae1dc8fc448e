{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;AAEb,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,MAAM,UAAU,cAAc,CAAuB,MAAS,EAAE,IAAO,EAAE,KAAW;IAChF,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;QAChC,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,KAAK;QACZ,QAAQ,EAAE,KAAK;KAClB,CAAC,CAAC;AACP,CAAC;AAED,yDAAyD;AACzD,MAAM,UAAU,SAAS,CAAI,IAAS,EAAE,GAAW;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;SAAE;QACpC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,OAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE;YAAE,MAAM;SAAE;QACtE,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;KAC5D;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AASD,MAAM,UAAgB,iBAAiB,CAAI,MAA+B;;QACtE,MAAM,QAAQ,GAA2B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACrE,MAAM,KAAK,GAAG,MAAM,CAAsB,GAAG,CAAC,CAAC;YAC/C,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE5C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACpC,KAAK,CAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;YAC5C,OAAO,KAAK,CAAC;QACjB,CAAC,EAAK,EAAG,CAAC,CAAC;IACf,CAAC;CAAA;AAED,MAAM,UAAU,eAAe,CAAC,MAAW,EAAE,UAAyC;IAClF,IAAI,CAAC,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QACxC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KACjE;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAClB,MAAM,CAAC,kBAAkB,CAAC,uBAAuB,GAAG,GAAG,EAAE,cAAc,GAAG,GAAG,EAAE,MAAM,CAAC,CAAC;SAC1F;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,MAAM,UAAU,WAAW,CAAI,MAAS;IACpC,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;QAAE,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;KAAE;IACxD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,MAAM,GAA+B,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AAEzH,SAAS,SAAS,CAAC,MAAW;IAE1B,gEAAgE;IAChE,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAM,CAAC,MAAM,CAAC,CAAC,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IAEvF,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QACtD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO,KAAK,CAAC;SAAE;QAE/C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,IAAI,KAAK,GAAQ,IAAI,CAAC;YACtB,IAAI;gBACA,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B;YAAC,OAAO,KAAK,EAAE;gBACZ,yDAAyD;gBACzD,4DAA4D;gBAC5D,SAAS;aACZ;YAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO,KAAK,CAAC;aAAE;SAC3C;QAED,OAAO,IAAI,CAAC;KACf;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,mBAAoB,OAAM,CAAC,MAAM,CAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9F,CAAC;AAED,yEAAyE;AACzE,+CAA+C;AAC/C,SAAS,SAAS,CAAC,MAAW;IAE1B,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IAEzC,kDAAkD;IAClD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC9D;IAED,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC7B,MAAM,MAAM,GAA6B,EAAE,CAAC;QAC5C,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;YACtB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC1B,IAAI,KAAK,KAAK,SAAS,EAAE;gBAAE,SAAS;aAAE;YACtC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SAChD;QAED,OAAO,MAAM,CAAC;KACjB;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,mBAAoB,OAAM,CAAC,MAAM,CAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC9F,CAAC;AAED,MAAM,UAAU,QAAQ,CAAI,MAAS;IACjC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAED,MAAM,OAAO,WAAW;IACpB,YAAY,IAAgC;QACxC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACd,IAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1C;IACL,CAAC;CACJ"}