import { ConcreteException } from '../base.js';
import { ErrorType } from '../types/index.js';
import { mapFailedTransactionMessage, parseErrorMessage } from '../utils/maps.js';
export class TransactionException extends ConcreteException {
    static errorClass = 'TransactionException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.ChainError;
    }
    parse() {
        const { message, context, contextModule, contextCode } = this;
        const { code, message: parsedMessage, contextModule: parsedContextModule, } = mapFailedTransactionMessage(message, { contextCode, contextModule });
        this.setContext(context || 'Unknown');
        this.setMessage(parsedMessage);
        this.setContextCode(code);
        this.setOriginalMessage(parseErrorMessage(message));
        if (parsedContextModule) {
            this.setContextModule(parsedContextModule);
        }
        this.setName(TransactionException.errorClass);
    }
}
