/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import { GetBlockIndexerRetainHeightRequest, GetBlockIndexerRetainHeightResponse, GetBlockResultsRetainHeightRequest, GetBlockResultsRetainHeightResponse, GetBlockRetainHeightRequest, GetBlockRetainHeightResponse, GetTxIndexerRetainHeightRequest, GetTxIndexerRetainHeightResponse, SetBlockIndexerRetainHeightRequest, SetBlockIndexerRetainHeightResponse, SetBlockResultsRetainHeightRequest, SetBlockResultsRetainHeightResponse, SetBlockRetainHeightRequest, SetBlockRetainHeightResponse, SetTxIndexerRetainHeightRequest, SetTxIndexerRetainHeightResponse, } from "./pruning.js";
export const protobufPackage = "cometbft.services.pruning.v1";
export class PruningServiceClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.SetBlockRetainHeight = this.SetBlockRetainHeight.bind(this);
        this.GetBlockRetainHeight = this.GetBlockRetainHeight.bind(this);
        this.SetBlockResultsRetainHeight = this.SetBlockResultsRetainHeight.bind(this);
        this.GetBlockResultsRetainHeight = this.GetBlockResultsRetainHeight.bind(this);
        this.SetTxIndexerRetainHeight = this.SetTxIndexerRetainHeight.bind(this);
        this.GetTxIndexerRetainHeight = this.GetTxIndexerRetainHeight.bind(this);
        this.SetBlockIndexerRetainHeight = this.SetBlockIndexerRetainHeight.bind(this);
        this.GetBlockIndexerRetainHeight = this.GetBlockIndexerRetainHeight.bind(this);
    }
    SetBlockRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceSetBlockRetainHeightDesc, SetBlockRetainHeightRequest.fromPartial(request), metadata);
    }
    GetBlockRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceGetBlockRetainHeightDesc, GetBlockRetainHeightRequest.fromPartial(request), metadata);
    }
    SetBlockResultsRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceSetBlockResultsRetainHeightDesc, SetBlockResultsRetainHeightRequest.fromPartial(request), metadata);
    }
    GetBlockResultsRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceGetBlockResultsRetainHeightDesc, GetBlockResultsRetainHeightRequest.fromPartial(request), metadata);
    }
    SetTxIndexerRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceSetTxIndexerRetainHeightDesc, SetTxIndexerRetainHeightRequest.fromPartial(request), metadata);
    }
    GetTxIndexerRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceGetTxIndexerRetainHeightDesc, GetTxIndexerRetainHeightRequest.fromPartial(request), metadata);
    }
    SetBlockIndexerRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceSetBlockIndexerRetainHeightDesc, SetBlockIndexerRetainHeightRequest.fromPartial(request), metadata);
    }
    GetBlockIndexerRetainHeight(request, metadata) {
        return this.rpc.unary(PruningServiceGetBlockIndexerRetainHeightDesc, GetBlockIndexerRetainHeightRequest.fromPartial(request), metadata);
    }
}
export const PruningServiceDesc = { serviceName: "cometbft.services.pruning.v1.PruningService" };
export const PruningServiceSetBlockRetainHeightDesc = {
    methodName: "SetBlockRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SetBlockRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SetBlockRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceGetBlockRetainHeightDesc = {
    methodName: "GetBlockRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetBlockRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetBlockRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceSetBlockResultsRetainHeightDesc = {
    methodName: "SetBlockResultsRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SetBlockResultsRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SetBlockResultsRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceGetBlockResultsRetainHeightDesc = {
    methodName: "GetBlockResultsRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetBlockResultsRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetBlockResultsRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceSetTxIndexerRetainHeightDesc = {
    methodName: "SetTxIndexerRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SetTxIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SetTxIndexerRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceGetTxIndexerRetainHeightDesc = {
    methodName: "GetTxIndexerRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetTxIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetTxIndexerRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceSetBlockIndexerRetainHeightDesc = {
    methodName: "SetBlockIndexerRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SetBlockIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SetBlockIndexerRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const PruningServiceGetBlockIndexerRetainHeightDesc = {
    methodName: "GetBlockIndexerRetainHeight",
    service: PruningServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetBlockIndexerRetainHeightRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetBlockIndexerRetainHeightResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
