import { MsgBase } from '../../MsgBase.js';
import { CosmwasmWasmV1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgUpdateAdmin {
    interface Params {
        sender: string;
        newAdmin: string;
        contract: string;
    }
    type Proto = CosmwasmWasmV1Tx.MsgUpdateAdmin;
}
/**
 * @category Messages
 */
export default class MsgUpdateAdmin extends MsgBase<MsgUpdateAdmin.Params, MsgUpdateAdmin.Proto> {
    static fromJSON(params: MsgUpdateAdmin.Params): MsgUpdateAdmin;
    toProto(): CosmwasmWasmV1Tx.MsgUpdateAdmin;
    toData(): {
        sender: string;
        newAdmin: string;
        contract: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            new_admin: string;
            contract: string;
        };
    };
    toWeb3Gw(): {
        sender: string;
        new_admin: string;
        contract: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmwasmWasmV1Tx.MsgUpdateAdmin;
    };
    toBinary(): Uint8Array;
}
