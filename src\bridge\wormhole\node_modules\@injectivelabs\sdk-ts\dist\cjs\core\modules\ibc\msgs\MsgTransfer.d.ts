import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { CosmosBaseV1Beta1Coin, IbcCoreClientV1Client, IbcApplicationsTransferV1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgTransfer {
    interface Params {
        amount: {
            denom: string;
            amount: string;
        };
        memo?: string;
        sender: string;
        port: string;
        receiver: string;
        channelId: string;
        timeout?: number;
        height?: {
            revisionHeight: number;
            revisionNumber: number;
        };
    }
    type Proto = IbcApplicationsTransferV1Tx.MsgTransfer;
}
/**
 * @category Messages
 */
export default class MsgTransfer extends MsgBase<MsgTransfer.Params, MsgTransfer.Proto> {
    static fromJSON(params: MsgTransfer.Params): MsgTransfer;
    toProto(): IbcApplicationsTransferV1Tx.MsgTransfer;
    toData(): {
        sourcePort: string;
        sourceChannel: string;
        token: CosmosBaseV1Beta1Coin.Coin | undefined;
        sender: string;
        receiver: string;
        timeoutHeight: IbcCoreClientV1Client.Height | undefined;
        timeoutTimestamp: string;
        memo: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<IbcApplicationsTransferV1Tx.MsgTransfer>;
    };
    toWeb3Gw(): {
        source_port: string;
        source_channel: string;
        token: {
            denom: string;
            amount: string;
        } | undefined;
        sender: string;
        receiver: string;
        timeout_height: {
            revision_number: string;
            revision_height: string;
        } | undefined;
        timeout_timestamp: string;
        memo: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: IbcApplicationsTransferV1Tx.MsgTransfer;
    };
    toBinary(): Uint8Array;
}
