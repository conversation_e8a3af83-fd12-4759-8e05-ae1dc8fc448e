#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
import requests
import yaml
from pathlib import Path
from web3 import Web3

def load_config():
    """
    加载配置文件
    
    Returns:
        配置字典
    """
    config_path = os.path.join("config", "config.yaml")
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

def get_web3_provider(network):
    """
    获取Web3提供者
    
    Args:
        network: 网络名称 (ETH, BSC, MATIC, BASEEVM)
        
    Returns:
        Web3实例或None
    """
    config = load_config()
    
    network_rpc_mapping = {
        "ethereum": config.get("dex", {}).get("ethereum", {}).get("rpc_url", "https://ethereum-rpc.publicnode.com"),
        "bsc": "https://bsc-dataseed.binance.org",
        "polygon": config.get("dex", {}).get("polygon", {}).get("rpc_url", "https://polygon-rpc.com"),
        "base": "https://mainnet.base.org"
    }
    
    if network not in network_rpc_mapping:
        print(f"不支持的网络: {network}")
        return None
    
    rpc_url = network_rpc_mapping[network]
    print(f"使用RPC: {rpc_url}")
    
    try:
        web3 = Web3(Web3.HTTPProvider(rpc_url))
        if web3.is_connected():
            print(f"已连接到{network}网络")
            return web3
        else:
            print(f"无法连接到{network}网络")
            return None
    except Exception as e:
        print(f"创建Web3实例时出错: {str(e)}")
        return None

def get_token_decimals_from_contract(web3, token_address):
    """
    直接从合约获取代币精度
    
    Args:
        web3: Web3实例
        token_address: 代币合约地址
        
    Returns:
        代币精度或None
    """
    if not web3:
        return None
    
    # ERC20代币decimals函数的ABI
    abi = [
        {
            "constant": True,
            "inputs": [],
            "name": "decimals",
            "outputs": [{"name": "", "type": "uint8"}],
            "type": "function"
        }
    ]
    
    try:
        # 检查地址格式
        if not Web3.is_address(token_address):
            print(f"无效的合约地址: {token_address}")
            return None
        
        # 创建合约实例
        checksum_address = web3.to_checksum_address(token_address)
        contract = web3.eth.contract(address=checksum_address, abi=abi)
        
        # 调用decimals函数
        decimals = contract.functions.decimals().call()
        print(f"从合约获取到代币精度: {decimals}")
        return decimals
    except Exception as e:
        print(f"从合约获取代币精度时出错: {str(e)}")
        return None

def get_batch_token_decimals(network, token_addresses):
    """
    批量获取代币精度信息（直接使用合约调用）
    
    Args:
        network: 网络名称
        token_addresses: 代币地址列表
        
    Returns:
        dict: 地址->精度映射字典
    """
    # 获取Web3实例
    web3 = get_web3_provider(network)
    if not web3:
        print(f"无法为网络 {network} 创建Web3实例，跳过该网络")
        return {}
    
    result = {}
    for i, address in enumerate(token_addresses):
        print(f"处理第 {i+1}/{len(token_addresses)} 个代币: {address}...")
        
        # 尝试从合约获取精度
        decimals = get_token_decimals_from_contract(web3, address)
        result[address.lower()] = decimals
        
        # 每次查询后稍微暂停，避免过多请求
        if i < len(token_addresses) - 1:
            time.sleep(0.2)
    
    return result

def get_common_token_decimals(network, token_address):
    """
    获取常见代币的精度信息
    
    Args:
        network: 网络名称
        token_address: 代币地址
        
    Returns:
        代币精度或None
    """
    # 检查常见稳定币地址
    network_stablecoins = {
        "ETH": {
            "******************************************": 6,  # USDT
            "******************************************": 6,  # USDC
            "******************************************": 18  # DAI
        },
        "BSC": {
            "******************************************": 18,  # USDT
            "******************************************": 18,  # USDC
            "******************************************": 18   # BUSD
        },
        "MATIC": {
            "******************************************": 6,   # USDT
            "******************************************": 6,   # USDC
            "******************************************": 18   # DAI
        },
        "BASEEVM": {
            "******************************************": 6,   # USDT
            "******************************************": 6    # USDC
        }
    }
    
    # 检查特定网络的已知代币地址
    if network in network_stablecoins:
        token_address_lower = token_address.lower()
        if token_address_lower in network_stablecoins[network]:
            decimals = network_stablecoins[network][token_address_lower]
            print(f"使用已知地址的代币精度: {decimals}")
            return decimals
    
    return None

def load_existing_decimals(output_path):
    """
    加载已有的代币精度信息
    
    Args:
        output_path: 输出文件路径
        
    Returns:
        dict: 网络->{地址->精度}的映射字典
    """
    existing_decimals = {}
    
    if os.path.exists(output_path):
        try:
            with open(output_path, "r", encoding="utf-8") as f:
                existing_data = json.load(f)
                
            for network, tokens in existing_data.items():
                if network not in existing_decimals:
                    existing_decimals[network] = {}
                    
                for token in tokens:
                    if "contract_address" in token and "decimals" in token:
                        address = token["contract_address"].lower()
                        existing_decimals[network][address] = token["decimals"]
                        
            print(f"已加载 {sum(len(addresses) for addresses in existing_decimals.values())} 个代币的精度信息")
        except Exception as e:
            print(f"加载已有数据时出错: {str(e)}")
    
    return existing_decimals

def main():
    """
    从代币合约获取代币精度信息并更新token数据
    """
    # 定义输入和输出路径
    input_path = "data/utils/token/gate_tokens.json"
    output_dir = "data/utils/token"
    output_path = os.path.join(output_dir, "gate_tokens_with_decimals.json")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"正在从 {input_path} 读取代币信息...")
    
    # 加载已有的精度信息
    existing_decimals = load_existing_decimals(output_path)
    
    # 读取源数据
    with open(input_path, "r", encoding="utf-8") as f:
        tokens_data = json.load(f)
    
    # 初始化结果字典和统计信息
    result = {}
    total_tokens = 0
    skipped_tokens = 0
    processed_tokens = 0
    
    # 遍历每个网络
    for network, tokens in tokens_data.items():
        print(f"\n处理 {network} 网络...")
        
        # 获取该网络已有的精度信息
        network_existing = existing_decimals.get(network, {})
        
        # 初始化当前网络的代币列表和待处理地址列表
        network_tokens = []
        tokens_to_process = []
        token_map = {}  # 地址到代币对象的映射
        
        total_network_tokens = len(tokens)
        total_tokens += total_network_tokens
        network_skipped = 0
        network_processed = 0
        
        # 首先检查哪些代币需要处理
        for token in tokens:
            token_copy = token.copy()  # 创建副本避免修改原始数据
            token_address = token["contract_address"].lower()
            
            # 检查此代币是否已有精度信息
            if token_address in network_existing and network_existing[token_address] is not None:
                # 已有精度信息，直接使用
                token_copy["decimals"] = network_existing[token_address]
                print(f"已有精度信息: {token['symbol']} = {token_copy['decimals']}")
                network_skipped += 1
            else:
                # 需要获取精度信息
                tokens_to_process.append(token_address)
                token_map[token_address] = token_copy
                token_copy["decimals"] = None
            
            network_tokens.append(token_copy)
        
        print(f"网络 {network} 有 {len(tokens_to_process)}/{total_network_tokens} 个代币需要处理")
        
        # 按每批最多30个地址处理
        batch_size = 30
        for i in range(0, len(tokens_to_process), batch_size):
            batch_addresses = tokens_to_process[i:i+batch_size]
            if not batch_addresses:
                continue
                
            print(f"获取第 {i//batch_size + 1} 批代币精度信息 ({len(batch_addresses)} 个代币)...")
            
            # 获取批量代币精度
            decimals_dict = get_batch_token_decimals(network, batch_addresses)
            
            # 处理获取到的精度信息
            for address in batch_addresses:
                address_lower = address.lower()
                token_copy = token_map[address_lower]
                
                if address_lower in decimals_dict and decimals_dict[address_lower] is not None:
                    token_copy["decimals"] = decimals_dict[address_lower]
                    print(f"成功获取到代币 {token_copy['symbol']} 的精度: {decimals_dict[address_lower]}")
                    network_processed += 1
                else:
                    # 尝试从常见代币列表获取
                    common_decimals = get_common_token_decimals(network, address_lower)
                    if common_decimals is not None:
                        token_copy["decimals"] = common_decimals
                        print(f"从常见代币列表获取 {token_copy['symbol']} 的精度: {common_decimals}")
                        network_processed += 1
                    else:
                        print(f"无法获取代币 {token_copy['symbol']} ({address_lower}) 的精度信息，设置为null")
        
        # 将当前网络的代币信息添加到结果字典
        result[network] = network_tokens
        print(f"已处理 {network} 网络的 {total_network_tokens} 个代币")
        print(f"  - 跳过已有精度: {network_skipped} 个")
        print(f"  - 成功处理: {network_processed} 个")
        
        skipped_tokens += network_skipped
        processed_tokens += network_processed
    
    # 将结果保存到文件
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n处理完成! 数据已保存到 {output_path}")
    print(f"总计处理 {total_tokens} 个代币:")
    print(f"  - 跳过已有精度: {skipped_tokens} 个")
    print(f"  - 成功处理: {processed_tokens} 个")

if __name__ == "__main__":
    main() 