import { Web3SideChainClient } from "./web3_side_chain_client";
import { ITransactionRequestConfig, ITransactionOption, IContractInitParam, ITransactionWriteResult } from "../interfaces";
import { BaseContractMethod, BaseContract } from "../abstracts";
import { POSERC1155TransferParam, TYPE_AMOUNT } from "../types";
export interface ITransactionConfigParam {
    txConfig: ITransactionRequestConfig;
    method?: BaseContractMethod;
    isWrite?: boolean;
    isParent?: boolean;
}
export declare class BaseToken<T_CLIENT_CONFIG> {
    protected contractParam: IContractInitParam;
    protected client: Web3SideChainClient<T_CLIENT_CONFIG>;
    private contract_;
    private chainId_;
    constructor(contractParam: IContractInitParam, client: Web3SideChainClient<T_CLIENT_CONFIG>);
    get contractAddress(): string;
    getContract(): Promise<BaseContract>;
    getChainId(): Promise<number>;
    protected processWrite(method: BaseContractMethod, option?: ITransactionOption): Promise<ITransactionWriteResult>;
    protected sendTransaction(option?: ITransactionOption): Promise<ITransactionWriteResult>;
    protected readTransaction(option?: ITransactionOption): Promise<ITransactionWriteResult>;
    private validateTxOption_;
    protected processRead<T>(method: BaseContractMethod, option?: ITransactionOption): Promise<T>;
    protected getClient(isParent: any): import("../abstracts").BaseWeb3Client;
    private getContract_;
    protected get parentDefaultConfig(): {
        from: string;
    };
    protected get childDefaultConfig(): {
        from: string;
    };
    protected createTransactionConfig({ txConfig, method, isParent, isWrite }: ITransactionConfigParam): Promise<ITransactionRequestConfig>;
    protected transferERC20(to: string, amount: TYPE_AMOUNT, option?: ITransactionOption): Promise<ITransactionWriteResult>;
    protected transferERC721(from: string, to: string, tokenId: string, option: ITransactionOption): Promise<ITransactionWriteResult>;
    protected checkForNonNative(methodName: any): void;
    protected checkForRoot(methodName: any): void;
    protected checkForChild(methodName: any): void;
    protected checkAdapterPresent(methodName: any): void;
    protected transferERC1155(param: POSERC1155TransferParam, option: ITransactionOption): Promise<ITransactionWriteResult>;
}
