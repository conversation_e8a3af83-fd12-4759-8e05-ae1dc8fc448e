"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSetVoucher = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
exports.protobufPackage = "injective.permissions.v1beta1";
function createBaseEventSetVoucher() {
    return { addr: "", voucher: undefined };
}
exports.EventSetVoucher = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.addr !== "") {
            writer.uint32(10).string(message.addr);
        }
        if (message.voucher !== undefined) {
            coin_1.Coin.encode(message.voucher, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSetVoucher();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.addr = reader.string();
                    break;
                case 2:
                    message.voucher = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            addr: isSet(object.addr) ? String(object.addr) : "",
            voucher: isSet(object.voucher) ? coin_1.Coin.fromJSON(object.voucher) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.addr !== undefined && (obj.addr = message.addr);
        message.voucher !== undefined && (obj.voucher = message.voucher ? coin_1.Coin.toJSON(message.voucher) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventSetVoucher.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventSetVoucher();
        message.addr = (_a = object.addr) !== null && _a !== void 0 ? _a : "";
        message.voucher = (object.voucher !== undefined && object.voucher !== null)
            ? coin_1.Coin.fromPartial(object.voucher)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
