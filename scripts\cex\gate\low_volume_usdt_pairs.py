#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取所有USDT交易对并筛选出24小时交易量低于588888的交易对
支持多个交易所，默认使用Gate.io
保存结果到JSON文件
增加功能：将结果与network_report.txt中的网络信息合并后按网络保存在同一个文件中
"""

import sys
import os
import json
import pandas as pd
import argparse
import yaml
import time
import re
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

try:
    import ccxt
    from src.utils.logger import logger
except ImportError:
    import logging
    # 如果无法导入logger，创建一个基本的logger
    logger = logging.getLogger("low_volume_usdt_pairs")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.warning("无法导入必要的模块，请确保已设置正确的PYTHONPATH")
    sys.exit(1)

# 默认配置
DEFAULT_VOLUME_THRESHOLD = 18888  # 交易量阈值
DEFAULT_OUTPUT_DIR = os.path.join(project_root, 'data', 'cex', 'gate_info')
SUPPORTED_EXCHANGES = ['gate', 'binance', 'okx', 'kucoin']  # 支持的交易所
NETWORK_REPORT_PATH = os.path.join(project_root, 'data', 'cex', 'gate_info', 'network_report.txt')

def load_config():
    """加载配置文件"""
    config_path = os.path.join(project_root, 'config', 'config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}

def parse_network_report(file_path: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    解析网络报告文件，提取代币和网络信息
    
    Args:
        file_path: 网络报告文件路径
    
    Returns:
        按网络分组的代币信息字典
    """
    if not os.path.exists(file_path):
        print(f"网络报告文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按网络分段
        network_sections = re.split(r'\n([A-Za-z0-9]+\s+网络:)', content)
        
        networks = {}
        current_network = None
        
        for i, section in enumerate(network_sections):
            # 网络标题行
            if re.match(r'^[A-Za-z0-9]+\s+网络:', section):
                current_network = section.split(' ')[0].strip()
                networks[current_network] = []
            # 网络内容
            elif current_network and i > 0:
                # 提取代币信息
                token_blocks = re.findall(r'  - ([^/]+)/USDT \(([^)]+)\)((?:\n    [^:]+: [^\n]+)*)', section)
                
                for symbol, name, details in token_blocks:
                    token_info = {
                        'symbol': symbol.strip(),
                        'name': name.strip(),
                        'network': current_network
                    }
                    
                    # 提取详细信息
                    contract_match = re.search(r'合约地址: ([^\n]+)', details)
                    if contract_match:
                        token_info['contract_address'] = contract_match.group(1).strip()
                    
                    fee_match = re.search(r'提现费用: ([^\n]+)', details)
                    if fee_match:
                        token_info['withdrawal_fee'] = fee_match.group(1).strip()
                    
                    address_match = re.search(r'充值地址: ([^\n]+)', details)
                    if address_match:
                        token_info['deposit_address'] = address_match.group(1).strip()
                    
                    networks[current_network].append(token_info)
        
        return networks
    
    except Exception as e:
        print(f"解析网络报告文件失败: {e}")
        return {}

class LowVolumeTokenFinder:
    def __init__(self, exchange_id='gate', volume_threshold=DEFAULT_VOLUME_THRESHOLD):
        """初始化交易所客户端"""
        self.exchange_id = exchange_id.lower()
        self.volume_threshold = volume_threshold
        
        # 从配置文件中读取API密钥
        config = load_config()
        exchange_config = config.get('cex', {}).get(self.exchange_id, {})
        self.api_key = exchange_config.get('api_key', '')
        self.api_secret = exchange_config.get('secret_key', '')
        
        # 初始化CCXT交易所客户端
        self.exchange = self._init_exchange()
        
        # 存储数据
        self.usdt_pairs = []
        self.low_volume_pairs = []
        self.tickers_data = {}

    def _init_exchange(self):
        """初始化交易所客户端"""
        if self.exchange_id not in ccxt.exchanges:
            raise ValueError(f"不支持的交易所: {self.exchange_id}")
        
        # 创建交易所实例
        exchange_class = getattr(ccxt, self.exchange_id)
        exchange = exchange_class({
            'apiKey': self.api_key,
            'secret': self.api_secret,
            'enableRateLimit': True,
            'timeout': 30000,  # 30秒超时
        })
        
        print(f"成功初始化{self.exchange_id}交易所客户端")
        return exchange
    
    def get_all_usdt_pairs(self):
        """获取所有USDT交易对"""
        try:
            print(f"正在获取{self.exchange_id}的所有交易对...")
            
            # 使用CCXT加载市场数据
            markets = self.exchange.load_markets()
            
            # 筛选出USDT交易对
            usdt_pairs = [
                symbol for symbol in markets.keys() 
                if symbol.endswith('/USDT') 
                and markets[symbol].get('active', False)
                # 排除杠杆交易对
                and not any(suffix in symbol for suffix in ['3S', '3L', '5S', '5L', 'BULL', 'BEAR', 'UP', 'DOWN'])
            ]
            
            print(f"成功获取 {len(usdt_pairs)} 个USDT交易对")
            self.usdt_pairs = usdt_pairs
            return usdt_pairs
            
        except Exception as e:
            print(f"获取USDT交易对时出错: {e}")
            return []
    
    def fetch_tickers(self):
        """获取所有交易对的24小时行情数据"""
        if not self.usdt_pairs:
            self.get_all_usdt_pairs()
            
        if not self.usdt_pairs:
            print("没有找到有效的USDT交易对")
            return {}
        
        print(f"开始获取 {len(self.usdt_pairs)} 个交易对的行情数据...")
        
        try:
            # 尝试批量获取行情
            if hasattr(self.exchange, 'fetch_tickers') and callable(self.exchange.fetch_tickers):
                tickers = self.exchange.fetch_tickers(self.usdt_pairs)
                print(f"成功批量获取 {len(tickers)} 个交易对的行情数据")
            else:
                # 单独获取每个交易对的行情
                tickers = {}
                for symbol in self.usdt_pairs:
                    try:
                        ticker = self.exchange.fetch_ticker(symbol)
                        tickers[symbol] = ticker
                    except Exception as e:
                        print(f"获取 {symbol} 行情失败: {e}")
                        time.sleep(0.5)  # 速率限制
                        continue
                print(f"成功单独获取 {len(tickers)} 个交易对的行情数据")
            
            self.tickers_data = tickers
            return tickers
            
        except Exception as e:
            print(f"获取行情数据失败: {e}")
            return {}
    
    def find_low_volume_pairs(self):
        """找出低交易量的交易对"""
        if not self.tickers_data:
            self.fetch_tickers()
            
        if not self.tickers_data:
            print("没有行情数据可分析")
            return []
        
        low_volume_pairs = []
        
        print(f"正在筛选24小时交易量低于 {self.volume_threshold} USDT 的交易对...")
        
        # 根据交易所的不同，交易量字段可能不同
        volume_field = 'quoteVolume'  # 默认使用计价货币交易量(以USDT计)
        
        # 筛选低交易量交易对
        for symbol, ticker in self.tickers_data.items():
            # 从ticker获取交易量
            volume = float(ticker.get(volume_field, 0))
            
            # 记录详细信息
            pair_info = {
                'symbol': symbol,
                'base_currency': symbol.split('/')[0],
                'quote_currency': symbol.split('/')[1],
                'volume': volume,
                'last_price': ticker.get('last', 0),
                'timestamp': ticker.get('timestamp', 0),
                'datetime': ticker.get('datetime', ''),
                'exchange': self.exchange_id
            }
            
            # 判断是否为低交易量
            if volume < self.volume_threshold:
                low_volume_pairs.append(pair_info)
        
        # 按交易量排序
        low_volume_pairs.sort(key=lambda x: x['volume'])
        
        print(f"找到 {len(low_volume_pairs)} 个低交易量交易对")
        self.low_volume_pairs = low_volume_pairs
        return low_volume_pairs
    
    def merge_with_network_info(self, network_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        将低交易量代币与网络报告中的代币信息合并
        
        Args:
            network_data: 按网络分组的代币信息字典
        
        Returns:
            按网络分组的合并后的代币信息
        """
        if not self.low_volume_pairs:
            print("没有低交易量交易对可合并")
            return {}
        
        if not network_data:
            print("没有网络数据可合并")
            return {}
        
        # 按网络分组整理结果
        result_by_network = {}
        # 未能匹配网络的代币
        unmatched_tokens = []
        
        # 为了快速查询，创建基于代币符号的索引
        token_index = {}
        for network, tokens in network_data.items():
            for token in tokens:
                symbol = token['symbol']
                if symbol not in token_index:
                    token_index[symbol] = []
                token_index[symbol].append((network, token))
        
        # 合并信息
        for pair in self.low_volume_pairs:
            base_symbol = pair['base_currency']
            
            # 查找网络信息
            matched = False
            if base_symbol in token_index:
                for network, token_info in token_index[base_symbol]:
                    if network not in result_by_network:
                        result_by_network[network] = []
                    
                    # 合并信息
                    merged_info = {**pair, **token_info}
                    result_by_network[network].append(merged_info)
                    matched = True
            
            # 如果没有匹配到网络信息，放入未匹配列表
            if not matched:
                unmatched_tokens.append(pair)
        
        # 将未匹配的代币归类到"未知网络"
        if unmatched_tokens:
            result_by_network["Unknown"] = unmatched_tokens
        
        return result_by_network
    
    def save_results(self, output_dir=DEFAULT_OUTPUT_DIR, network_report_path=NETWORK_REPORT_PATH):
        """保存结果到JSON文件，将所有网络的数据合并到一个文件中，使用固定文件名（覆盖模式）"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取低交易量交易对
        if not self.low_volume_pairs:
            self.find_low_volume_pairs()
        
        if not self.low_volume_pairs:
            print("没有低交易量交易对可保存")
            return None
        
        # 解析网络报告
        network_data = parse_network_report(network_report_path)
        
        # 合并信息
        merged_data_by_network = self.merge_with_network_info(network_data)
        
        # 使用固定的文件名，不包含时间戳
        output_file = os.path.join(
            output_dir, 
            f"{self.exchange_id}_low_volume_usdt_pairs_with_network.json"
        )
        
        # 统计每个网络的代币数量
        network_stats = {network: len(tokens) for network, tokens in merged_data_by_network.items()}
        
        # 准备完整的结果数据
        full_result = {
            'metadata': {
                'exchange': self.exchange_id,
                'volume_threshold': self.volume_threshold,
                'total_usdt_pairs': len(self.usdt_pairs),
                'low_volume_pairs_count': len(self.low_volume_pairs),
                'networks_count': len(merged_data_by_network),
                'network_stats': network_stats,
                'timestamp': int(time.time()),
                'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'networks': merged_data_by_network
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(full_result, f, indent=2, ensure_ascii=False)
            print(f"所有结果已保存到固定文件: {output_file}")
            
            # 额外保存一个仅包含低交易量交易对的文件，使用固定名称
            summary_file = os.path.join(
                output_dir, 
                f"{self.exchange_id}_low_volume_usdt_pairs.json"
            )
            
            summary_data = {
                'metadata': {
                    'exchange': self.exchange_id,
                    'volume_threshold': self.volume_threshold,
                    'total_usdt_pairs': len(self.usdt_pairs),
                    'low_volume_pairs_count': len(self.low_volume_pairs),
                    'timestamp': int(time.time()),
                    'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'low_volume_pairs': self.low_volume_pairs
            }
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            print(f"原始低交易量数据已保存到固定文件: {summary_file}")
            
            return output_file
            
        except Exception as e:
            print(f"保存结果失败: {e}")
            return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取所有USDT交易对并筛选出低交易量的交易对')
    parser.add_argument('-e', '--exchange', default='gate', choices=SUPPORTED_EXCHANGES,
                        help=f'交易所 (支持: {", ".join(SUPPORTED_EXCHANGES)}，默认: gate)')
    parser.add_argument('-v', '--volume', type=float, default=DEFAULT_VOLUME_THRESHOLD,
                        help=f'交易量阈值 (默认: {DEFAULT_VOLUME_THRESHOLD} USDT)')
    parser.add_argument('-o', '--output', default=DEFAULT_OUTPUT_DIR,
                        help=f'输出目录 (默认: {DEFAULT_OUTPUT_DIR})')
    parser.add_argument('-n', '--network-report', default=NETWORK_REPORT_PATH,
                        help=f'网络报告文件路径 (默认: {NETWORK_REPORT_PATH})')
    
    args = parser.parse_args()
    
    # 创建低交易量代币查找器
    finder = LowVolumeTokenFinder(
        exchange_id=args.exchange,
        volume_threshold=args.volume
    )
    
    # 获取所有USDT交易对
    finder.get_all_usdt_pairs()
    
    # 找出低交易量的交易对
    finder.find_low_volume_pairs()
    
    # 保存结果
    output_file = finder.save_results(args.output, args.network_report)
    
    if output_file:
        print("\n=== 摘要信息 ===")
        print(f"交易所: {args.exchange}")
        print(f"交易量阈值: {args.volume} USDT")
        print(f"USDT交易对总数: {len(finder.usdt_pairs)}")
        print(f"低交易量交易对数量: {len(finder.low_volume_pairs)}")
        print(f"占比: {len(finder.low_volume_pairs)/len(finder.usdt_pairs)*100:.2f}%")
        print(f"结果已保存到: {output_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc() 