import { InjectiveAccountRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcAccountApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveAccountRpc.InjectiveAccountsRPCClientImpl;
    constructor(endpoint: string);
    /**
     * @deprecated - use IndexerGrpcAccountPortfolioApi.fetchPortfolio instead
     */
    fetchPortfolio(_address: string): Promise<void>;
    fetchRewards({ address, epoch }: {
        address: string;
        epoch: number;
    }): Promise<import("../types/account.js").TradingReward[]>;
    fetchSubaccountsList(address: string): Promise<string[]>;
    fetchSubaccountBalance(subaccountId: string, denom: string): Promise<import("../types/account.js").SubaccountBalance>;
    fetchSubaccountBalancesList(subaccountId: string): Promise<import("../types/account.js").SubaccountBalance[]>;
    fetchSubaccountHistory({ subaccountId, denom, transferTypes, pagination, }: {
        subaccountId: string;
        denom?: string;
        transferTypes?: string[];
        pagination?: PaginationOption;
    }): Promise<{
        transfers: import("../types/account.js").SubaccountTransfer[];
        pagination: import("../../../types/pagination.js").ExchangePagination;
    }>;
    fetchSubaccountOrderSummary({ subaccountId, marketId, orderDirection, }: {
        subaccountId: string;
        marketId?: string;
        orderDirection?: string;
    }): Promise<InjectiveAccountRpc.SubaccountOrderSummaryResponse>;
    fetchOrderStates(params?: {
        spotOrderHashes?: string[];
        derivativeOrderHashes?: string[];
    }): Promise<InjectiveAccountRpc.OrderStatesResponse>;
}
