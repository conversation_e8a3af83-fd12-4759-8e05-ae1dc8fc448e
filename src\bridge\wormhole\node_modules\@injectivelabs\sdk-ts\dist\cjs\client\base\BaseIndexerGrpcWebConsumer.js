"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGrpcIndexerWebImpl = void 0;
const grpc_js_1 = require("../../utils/grpc.js");
const IndexerGrpcWebImpl_js_1 = require("./IndexerGrpcWebImpl.js");
/**
 * @hidden
 */
class BaseIndexerGrpcWebConsumer extends IndexerGrpcWebImpl_js_1.GrpcWebImpl {
    module = '';
    constructor(endpoint) {
        super(endpoint, { transport: (0, grpc_js_1.getGrpcTransport)() });
    }
}
exports.default = BaseIndexerGrpcWebConsumer;
const getGrpcIndexerWebImpl = (endpoint) => new BaseIndexerGrpcWebConsumer(endpoint);
exports.getGrpcIndexerWebImpl = getGrpcIndexerWebImpl;
