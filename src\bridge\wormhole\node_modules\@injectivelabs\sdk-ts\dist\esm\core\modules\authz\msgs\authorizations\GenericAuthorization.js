import { CosmosAuthzV1Beta1Authz, } from '@injectivelabs/core-proto-ts';
import { BaseAuthorization } from './Base.js';
import { GeneralException } from '@injectivelabs/exceptions';
import { getGenericAuthorizationFromMessageType } from '../../utils.js';
/**
 * @category Contract Exec Arguments
 */
export default class GenericAuthorization extends BaseAuthorization {
    static fromJSON(params) {
        return new GenericAuthorization(params);
    }
    toProto() {
        const genericAuthorization = CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return genericAuthorization;
    }
    toAmino() {
        const genericAuthorization = CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return {
            type: 'cosmos-sdk/GenericAuthorization',
            value: { msg: genericAuthorization.msg },
        };
    }
    toWeb3() {
        const genericAuthorization = CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(this.toAny().value);
        return {
            '@type': '/cosmos.authz.v1beta1.GenericAuthorization',
            msg: genericAuthorization.msg,
        };
    }
    toAny() {
        const { params } = this;
        if (!params.authorization && !params.messageTypeUrl) {
            throw new GeneralException(new Error('Either authorization or messageType must be provided'));
        }
        return (params.authorization ||
            getGenericAuthorizationFromMessageType(params.messageTypeUrl));
    }
}
