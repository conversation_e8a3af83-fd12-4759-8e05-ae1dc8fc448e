{"version": 3, "file": "genesisState.js", "sourceRoot": "", "sources": ["../../../src/util/genesisState.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAA;AACrC,OAAO,EACL,OAAO,EACP,UAAU,EACV,WAAW,EACX,UAAU,EACV,oBAAoB,GACrB,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAA;AAE3D,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAIjC;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,YAA0B;IAC/D,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;IAC9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;QACvD,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAA;QAC9E,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAA;QAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;SAChC;aAAM;YACL,MAAM,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,KAA8B,CAAA;YACtE,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;aAClC;YACD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;gBACnF,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC,CAAA;aACxC;YACD,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;gBACrD,KAAK,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE;oBAC9B,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAA;oBAC3E,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAC3B,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAC3E,CAAA;oBACD,MAAM,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;iBAC9C;gBACD,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAA;aACzC;YACD,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;QACD,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAA;KAC7C;IACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAA;AACpB,CAAC"}