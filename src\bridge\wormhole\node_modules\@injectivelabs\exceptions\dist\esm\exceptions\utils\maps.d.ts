import { Error<PERSON>ontext, Error<PERSON>ontextCode, TransactionChainErrorModule } from '../types/index.js';
export declare const parseErrorMessage: (message: string) => string;
export declare const mapFailedTransactionMessageFromString: (message: string) => {
    message: string;
    code: ErrorContextCode;
    module?: TransactionChainErrorModule;
};
export declare const mapFailedTransactionMessage: (message: string, context?: ErrorContext) => {
    message: string;
    code: ErrorContextCode;
    contextModule?: string;
};
export declare const mapMetamaskMessage: (message: string) => string;
