/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin.js";
export const protobufPackage = "injective.permissions.v1beta1";
/** each Action enum value should be a power of two */
export var Action;
(function (Action) {
    /** UNSPECIFIED - 0 is reserved for ACTION_UNSPECIFIED */
    Action[Action["UNSPECIFIED"] = 0] = "UNSPECIFIED";
    /** MINT - 1 is reserved for MINT */
    Action[Action["MINT"] = 1] = "MINT";
    /** RECEIVE - 2 is reserved for RECEIVE */
    Action[Action["RECEIVE"] = 2] = "RECEIVE";
    /** BURN - 4 is reserved for BURN */
    Action[Action["BURN"] = 4] = "BURN";
    /** SEND - 8 is reserved for SEND */
    Action[Action["SEND"] = 8] = "SEND";
    /** SUPER_BURN - 16 is reserved for SUPER_BURN */
    Action[Action["SUPER_BURN"] = 16] = "SUPER_BURN";
    /** MODIFY_POLICY_MANAGERS - 2^27 is reserved for MODIFY_POLICY_MANAGERS */
    Action[Action["MODIFY_POLICY_MANAGERS"] = 134217728] = "MODIFY_POLICY_MANAGERS";
    /** MODIFY_CONTRACT_HOOK - 2^28 is reserved for MODIFY_CONTRACT_HOOK */
    Action[Action["MODIFY_CONTRACT_HOOK"] = 268435456] = "MODIFY_CONTRACT_HOOK";
    /** MODIFY_ROLE_PERMISSIONS - 2^29 is reserved for MODIFY_ROLE_PERMISSIONS */
    Action[Action["MODIFY_ROLE_PERMISSIONS"] = 536870912] = "MODIFY_ROLE_PERMISSIONS";
    /** MODIFY_ROLE_MANAGERS - 2^30 is reserved for MODIFY_ROLE_MANAGERS */
    Action[Action["MODIFY_ROLE_MANAGERS"] = 1073741824] = "MODIFY_ROLE_MANAGERS";
    Action[Action["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Action || (Action = {}));
export function actionFromJSON(object) {
    switch (object) {
        case 0:
        case "UNSPECIFIED":
            return Action.UNSPECIFIED;
        case 1:
        case "MINT":
            return Action.MINT;
        case 2:
        case "RECEIVE":
            return Action.RECEIVE;
        case 4:
        case "BURN":
            return Action.BURN;
        case 8:
        case "SEND":
            return Action.SEND;
        case 16:
        case "SUPER_BURN":
            return Action.SUPER_BURN;
        case 134217728:
        case "MODIFY_POLICY_MANAGERS":
            return Action.MODIFY_POLICY_MANAGERS;
        case 268435456:
        case "MODIFY_CONTRACT_HOOK":
            return Action.MODIFY_CONTRACT_HOOK;
        case 536870912:
        case "MODIFY_ROLE_PERMISSIONS":
            return Action.MODIFY_ROLE_PERMISSIONS;
        case 1073741824:
        case "MODIFY_ROLE_MANAGERS":
            return Action.MODIFY_ROLE_MANAGERS;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Action.UNRECOGNIZED;
    }
}
export function actionToJSON(object) {
    switch (object) {
        case Action.UNSPECIFIED:
            return "UNSPECIFIED";
        case Action.MINT:
            return "MINT";
        case Action.RECEIVE:
            return "RECEIVE";
        case Action.BURN:
            return "BURN";
        case Action.SEND:
            return "SEND";
        case Action.SUPER_BURN:
            return "SUPER_BURN";
        case Action.MODIFY_POLICY_MANAGERS:
            return "MODIFY_POLICY_MANAGERS";
        case Action.MODIFY_CONTRACT_HOOK:
            return "MODIFY_CONTRACT_HOOK";
        case Action.MODIFY_ROLE_PERMISSIONS:
            return "MODIFY_ROLE_PERMISSIONS";
        case Action.MODIFY_ROLE_MANAGERS:
            return "MODIFY_ROLE_MANAGERS";
        case Action.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseNamespace() {
    return {
        denom: "",
        contractHook: "",
        rolePermissions: [],
        actorRoles: [],
        roleManagers: [],
        policyStatuses: [],
        policyManagerCapabilities: [],
    };
}
export const Namespace = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.contractHook !== "") {
            writer.uint32(18).string(message.contractHook);
        }
        for (const v of message.rolePermissions) {
            Role.encode(v, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.actorRoles) {
            ActorRoles.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (const v of message.roleManagers) {
            RoleManager.encode(v, writer.uint32(42).fork()).ldelim();
        }
        for (const v of message.policyStatuses) {
            PolicyStatus.encode(v, writer.uint32(50).fork()).ldelim();
        }
        for (const v of message.policyManagerCapabilities) {
            PolicyManagerCapability.encode(v, writer.uint32(58).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseNamespace();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.contractHook = reader.string();
                    break;
                case 3:
                    message.rolePermissions.push(Role.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.actorRoles.push(ActorRoles.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleManagers.push(RoleManager.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.policyStatuses.push(PolicyStatus.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.policyManagerCapabilities.push(PolicyManagerCapability.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            contractHook: isSet(object.contractHook) ? String(object.contractHook) : "",
            rolePermissions: Array.isArray(object?.rolePermissions)
                ? object.rolePermissions.map((e) => Role.fromJSON(e))
                : [],
            actorRoles: Array.isArray(object?.actorRoles) ? object.actorRoles.map((e) => ActorRoles.fromJSON(e)) : [],
            roleManagers: Array.isArray(object?.roleManagers)
                ? object.roleManagers.map((e) => RoleManager.fromJSON(e))
                : [],
            policyStatuses: Array.isArray(object?.policyStatuses)
                ? object.policyStatuses.map((e) => PolicyStatus.fromJSON(e))
                : [],
            policyManagerCapabilities: Array.isArray(object?.policyManagerCapabilities)
                ? object.policyManagerCapabilities.map((e) => PolicyManagerCapability.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.contractHook !== undefined && (obj.contractHook = message.contractHook);
        if (message.rolePermissions) {
            obj.rolePermissions = message.rolePermissions.map((e) => e ? Role.toJSON(e) : undefined);
        }
        else {
            obj.rolePermissions = [];
        }
        if (message.actorRoles) {
            obj.actorRoles = message.actorRoles.map((e) => e ? ActorRoles.toJSON(e) : undefined);
        }
        else {
            obj.actorRoles = [];
        }
        if (message.roleManagers) {
            obj.roleManagers = message.roleManagers.map((e) => e ? RoleManager.toJSON(e) : undefined);
        }
        else {
            obj.roleManagers = [];
        }
        if (message.policyStatuses) {
            obj.policyStatuses = message.policyStatuses.map((e) => e ? PolicyStatus.toJSON(e) : undefined);
        }
        else {
            obj.policyStatuses = [];
        }
        if (message.policyManagerCapabilities) {
            obj.policyManagerCapabilities = message.policyManagerCapabilities.map((e) => e ? PolicyManagerCapability.toJSON(e) : undefined);
        }
        else {
            obj.policyManagerCapabilities = [];
        }
        return obj;
    },
    create(base) {
        return Namespace.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseNamespace();
        message.denom = object.denom ?? "";
        message.contractHook = object.contractHook ?? "";
        message.rolePermissions = object.rolePermissions?.map((e) => Role.fromPartial(e)) || [];
        message.actorRoles = object.actorRoles?.map((e) => ActorRoles.fromPartial(e)) || [];
        message.roleManagers = object.roleManagers?.map((e) => RoleManager.fromPartial(e)) || [];
        message.policyStatuses = object.policyStatuses?.map((e) => PolicyStatus.fromPartial(e)) || [];
        message.policyManagerCapabilities =
            object.policyManagerCapabilities?.map((e) => PolicyManagerCapability.fromPartial(e)) || [];
        return message;
    },
};
function createBaseActorRoles() {
    return { actor: "", roles: [] };
}
export const ActorRoles = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.actor !== "") {
            writer.uint32(10).string(message.actor);
        }
        for (const v of message.roles) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseActorRoles();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.actor = reader.string();
                    break;
                case 2:
                    message.roles.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            actor: isSet(object.actor) ? String(object.actor) : "",
            roles: Array.isArray(object?.roles) ? object.roles.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.actor !== undefined && (obj.actor = message.actor);
        if (message.roles) {
            obj.roles = message.roles.map((e) => e);
        }
        else {
            obj.roles = [];
        }
        return obj;
    },
    create(base) {
        return ActorRoles.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseActorRoles();
        message.actor = object.actor ?? "";
        message.roles = object.roles?.map((e) => e) || [];
        return message;
    },
};
function createBaseRoleActors() {
    return { role: "", actors: [] };
}
export const RoleActors = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.role !== "") {
            writer.uint32(10).string(message.role);
        }
        for (const v of message.actors) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoleActors();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.role = reader.string();
                    break;
                case 2:
                    message.actors.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            role: isSet(object.role) ? String(object.role) : "",
            actors: Array.isArray(object?.actors) ? object.actors.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.role !== undefined && (obj.role = message.role);
        if (message.actors) {
            obj.actors = message.actors.map((e) => e);
        }
        else {
            obj.actors = [];
        }
        return obj;
    },
    create(base) {
        return RoleActors.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRoleActors();
        message.role = object.role ?? "";
        message.actors = object.actors?.map((e) => e) || [];
        return message;
    },
};
function createBaseRoleManager() {
    return { manager: "", roles: [] };
}
export const RoleManager = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.manager !== "") {
            writer.uint32(10).string(message.manager);
        }
        for (const v of message.roles) {
            writer.uint32(18).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoleManager();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.manager = reader.string();
                    break;
                case 2:
                    message.roles.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            manager: isSet(object.manager) ? String(object.manager) : "",
            roles: Array.isArray(object?.roles) ? object.roles.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.manager !== undefined && (obj.manager = message.manager);
        if (message.roles) {
            obj.roles = message.roles.map((e) => e);
        }
        else {
            obj.roles = [];
        }
        return obj;
    },
    create(base) {
        return RoleManager.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRoleManager();
        message.manager = object.manager ?? "";
        message.roles = object.roles?.map((e) => e) || [];
        return message;
    },
};
function createBasePolicyStatus() {
    return { action: 0, isDisabled: false, isSealed: false };
}
export const PolicyStatus = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.action !== 0) {
            writer.uint32(8).int32(message.action);
        }
        if (message.isDisabled === true) {
            writer.uint32(16).bool(message.isDisabled);
        }
        if (message.isSealed === true) {
            writer.uint32(24).bool(message.isSealed);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePolicyStatus();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.action = reader.int32();
                    break;
                case 2:
                    message.isDisabled = reader.bool();
                    break;
                case 3:
                    message.isSealed = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            action: isSet(object.action) ? actionFromJSON(object.action) : 0,
            isDisabled: isSet(object.isDisabled) ? Boolean(object.isDisabled) : false,
            isSealed: isSet(object.isSealed) ? Boolean(object.isSealed) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.action !== undefined && (obj.action = actionToJSON(message.action));
        message.isDisabled !== undefined && (obj.isDisabled = message.isDisabled);
        message.isSealed !== undefined && (obj.isSealed = message.isSealed);
        return obj;
    },
    create(base) {
        return PolicyStatus.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePolicyStatus();
        message.action = object.action ?? 0;
        message.isDisabled = object.isDisabled ?? false;
        message.isSealed = object.isSealed ?? false;
        return message;
    },
};
function createBaseRole() {
    return { name: "", roleId: 0, permissions: 0 };
}
export const Role = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.roleId !== 0) {
            writer.uint32(16).uint32(message.roleId);
        }
        if (message.permissions !== 0) {
            writer.uint32(24).uint32(message.permissions);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRole();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.roleId = reader.uint32();
                    break;
                case 3:
                    message.permissions = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            roleId: isSet(object.roleId) ? Number(object.roleId) : 0,
            permissions: isSet(object.permissions) ? Number(object.permissions) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.roleId !== undefined && (obj.roleId = Math.round(message.roleId));
        message.permissions !== undefined && (obj.permissions = Math.round(message.permissions));
        return obj;
    },
    create(base) {
        return Role.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRole();
        message.name = object.name ?? "";
        message.roleId = object.roleId ?? 0;
        message.permissions = object.permissions ?? 0;
        return message;
    },
};
function createBasePolicyManagerCapability() {
    return { manager: "", action: 0, canDisable: false, canSeal: false };
}
export const PolicyManagerCapability = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.manager !== "") {
            writer.uint32(10).string(message.manager);
        }
        if (message.action !== 0) {
            writer.uint32(16).int32(message.action);
        }
        if (message.canDisable === true) {
            writer.uint32(24).bool(message.canDisable);
        }
        if (message.canSeal === true) {
            writer.uint32(32).bool(message.canSeal);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePolicyManagerCapability();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.manager = reader.string();
                    break;
                case 2:
                    message.action = reader.int32();
                    break;
                case 3:
                    message.canDisable = reader.bool();
                    break;
                case 4:
                    message.canSeal = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            manager: isSet(object.manager) ? String(object.manager) : "",
            action: isSet(object.action) ? actionFromJSON(object.action) : 0,
            canDisable: isSet(object.canDisable) ? Boolean(object.canDisable) : false,
            canSeal: isSet(object.canSeal) ? Boolean(object.canSeal) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.manager !== undefined && (obj.manager = message.manager);
        message.action !== undefined && (obj.action = actionToJSON(message.action));
        message.canDisable !== undefined && (obj.canDisable = message.canDisable);
        message.canSeal !== undefined && (obj.canSeal = message.canSeal);
        return obj;
    },
    create(base) {
        return PolicyManagerCapability.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePolicyManagerCapability();
        message.manager = object.manager ?? "";
        message.action = object.action ?? 0;
        message.canDisable = object.canDisable ?? false;
        message.canSeal = object.canSeal ?? false;
        return message;
    },
};
function createBaseRoleIDs() {
    return { roleIds: [] };
}
export const RoleIDs = {
    encode(message, writer = _m0.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.roleIds) {
            writer.uint32(v);
        }
        writer.ldelim();
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRoleIDs();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.roleIds.push(reader.uint32());
                        }
                    }
                    else {
                        message.roleIds.push(reader.uint32());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { roleIds: Array.isArray(object?.roleIds) ? object.roleIds.map((e) => Number(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.roleIds) {
            obj.roleIds = message.roleIds.map((e) => Math.round(e));
        }
        else {
            obj.roleIds = [];
        }
        return obj;
    },
    create(base) {
        return RoleIDs.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRoleIDs();
        message.roleIds = object.roleIds?.map((e) => e) || [];
        return message;
    },
};
function createBaseAddressVoucher() {
    return { address: "", voucher: undefined };
}
export const AddressVoucher = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.voucher !== undefined) {
            Coin.encode(message.voucher, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAddressVoucher();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.voucher = Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            voucher: isSet(object.voucher) ? Coin.fromJSON(object.voucher) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.voucher !== undefined && (obj.voucher = message.voucher ? Coin.toJSON(message.voucher) : undefined);
        return obj;
    },
    create(base) {
        return AddressVoucher.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAddressVoucher();
        message.address = object.address ?? "";
        message.voucher = (object.voucher !== undefined && object.voucher !== null)
            ? Coin.fromPartial(object.voucher)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
