import * as grpcPkg from '@injectivelabs/grpc-web';
import { StatusCodes } from 'http-status-codes';
export declare enum GrpcErrorCode {
    OK = 0,
    Canceled = 1,
    Unknown = 2,
    InvalidArgument = 3,
    DeadlineExceeded = 4,
    NotFound = 5,
    AlreadyExists = 6,
    PermissionDenied = 7,
    ResourceExhausted = 8,
    FailedPrecondition = 9,
    Aborted = 10,
    OutOfRange = 11,
    Unimplemented = 12,
    Internal = 13,
    Unavailable = 14,
    DataLoss = 15,
    Unauthenticated = 16
}
export declare const grpcErrorCodeToErrorCode: <T extends number>(grpcErrorCode: T) => GrpcErrorCode;
export declare enum TransactionChainErrorModule {
    Auction = "auction",
    CosmosSdk = "sdk",
    Staking = "staking",
    Bank = "bank",
    Distribution = "distribution",
    Gov = "gov",
    Exchange = "exchange",
    Insurance = "insurance",
    Ocr = "ocr",
    Oracle = "oracle",
    Peggy = "peggy",
    TokenFactory = "tokenfactory",
    Wasmx = "wasmx",
    Wasm = "wasm",
    AuthZ = "authz"
}
export declare enum ChainCosmosErrorCode {
    ErrTxDecode = 2,
    ErrInvalidSequence = 3,
    ErrUnauthorized = 4,
    ErrInsufficientFunds = 5,
    ErrUnknownRequest = 6,
    ErrInvalidAddress = 7,
    ErrInvalidPubKey = 8,
    ErrUnknownAddress = 9,
    ErrInvalidCoins = 10,
    ErrOutOfGas = 11,
    ErrMemoTooLarge = 12,
    ErrInsufficientFee = 13,
    ErrTooManySignatures = 14,
    ErrNoSignatures = 15,
    ErrJSONMarshal = 16,
    ErrJSONUnmarshal = 17,
    ErrInvalidRequest = 18,
    ErrTxInMempoolCache = 19,
    ErrMempoolIsFull = 20,
    ErrTxTooLarge = 21,
    ErrKeyNotFound = 22,
    ErrWrongPassword = 23,
    ErrorInvalidSigner = 24,
    ErrorInvalidGasAdjustment = 25,
    ErrInvalidHeight = 26,
    ErrInvalidVersion = 27,
    ErrInvalidChainID = 28,
    ErrInvalidType = 29,
    ErrTxTimeoutHeight = 30,
    ErrUnknownExtensionOptions = 31,
    ErrWrongSequence = 32,
    ErrPackAny = 33,
    ErrUnpackAny = 34,
    ErrLogic = 35,
    ErrConflict = 36,
    ErrNotSupported = 37,
    ErrNotFound = 38,
    ErrIO = 39,
    ErrAppConfig = 40,
    ErrInvalidGasLimit = 41
}
export declare enum ChainExchangeModuleErrorCode {
    ErrOrderInvalid = 1,
    ErrSpotMarketNotFound = 2,
    ErrSpotMarketExists = 3,
    ErrBadField = 4,
    ErrMarketInvalid = 5,
    ErrInsufficientDeposit = 6,
    ErrUnrecognizedOrderType = 7,
    ErrInsufficientPositionQuantity = 8,
    ErrOrderHashInvalid = 9,
    ErrBadSubaccountID = 10,
    ErrInvalidTicker = 11,
    ErrInvalidBaseDenom = 12,
    ErrInvalidQuoteDenom = 13,
    ErrInvalidOracle = 14,
    ErrInvalidExpiry = 15,
    ErrInvalidPrice = 16,
    ErrInvalidQuantity = 17,
    ErrUnsupportedOracleType = 18,
    ErrOrderDoesntExist = 19,
    ErrOrderbookFillInvalid = 20,
    ErrPerpetualMarketExists = 21,
    ErrExpiryFuturesMarketExists = 22,
    ErrExpiryFuturesMarketExpired = 23,
    ErrNoLiquidity = 24,
    ErrSlippageExceedsWorstPrice = 25,
    ErrInsufficientOrderMargin = 26,
    ErrDerivativeMarketNotFound = 27,
    ErrPositionNotFound = 28,
    ErrInvalidReduceOnlyPositionDirection = 29,
    ErrPriceSurpassesBankruptcyPrice = 30,
    ErrPositionNotLiquidable = 31,
    ErrInvalidTriggerPrice = 32,
    ErrInvalidOracleType = 33,
    ErrInvalidPriceTickSize = 34,
    ErrInvalidQuantityTickSize = 35,
    ErrInvalidMargin = 36,
    ErrExceedsOrderSideCount = 37,
    ErrMarketOrderAlreadyExists = 38,
    ErrConditionalMarketOrderAlreadyExists = 39,
    ErrMarketLaunchProposalAlreadyExists = 40,
    ErrInvalidMarketStatus = 41,
    ErrSameDenoms = 42,
    ErrSameOracles = 43,
    ErrFeeRatesRelation = 44,
    ErrMarginsRelation = 45,
    ErrExceedsMaxOracleScaleFactor = 46,
    ErrSpotExchangeNotEnabled = 47,
    ErrDerivativesExchangeNotEnabled = 48,
    ErrOraclePriceDeltaExceedsThreshold = 49,
    ErrInvalidHourlyInterestRate = 50,
    ErrInvalidHourlyFundingRateCap = 51,
    ErrInvalidMarketFundingParamUpdate = 52,
    ErrInvalidTradingRewardCampaign = 53,
    ErrInvalidFeeDiscountSchedule = 54,
    ErrInvalidLiquidationOrder = 55,
    ErrTradingRewardCampaignDistributionError = 56,
    ErrInvalidTradingRewardsPendingPointsUpdate = 57,
    ErrInvalidBatchMsgUpdate = 58,
    ErrExceedsTopOfBookPrice = 59,
    ErrInvalidOrderTypeForMessage = 60,
    ErrInvalidDMMSender = 61,
    ErrAlreadyOptedOutOfRewards = 62,
    ErrInvalidMarginRatio = 63,
    ErrBelowMinimalContribution = 64,
    ErrLowPositionMargin = 65,
    ErrInvalidTotalSupply = 66,
    ErrInvalidLpTokenBurnAmount = 67,
    ErrUnsupportedAction = 68,
    ErrNegativePositionQuantity = 69,
    ErrBinaryOptionsMarketExists = 70,
    ErrBinaryOptionsMarketNotFound = 71,
    ErrInvalidSettlement = 72,
    ErrAccountDoesntExist = 73,
    ErrSenderIsNotAnAdmin = 74,
    ErrMarketAlreadyScheduledToSettle = 75,
    ErrGenericMarketNotFound = 76,
    ErrInvalidDenomDecimal = 77,
    ErrInvalidState = 78,
    ErrTransientOrdersUpToCancelNotSupported = 79,
    ErrInvalidTrade = 80,
    ErrNoMarginLocked = 81,
    ErrInvalidAccessLevel = 82,
    ErrInvalidAddress = 83,
    ErrInvalidArgument = 84,
    ErrInvalidFundsDirection = 85,
    ErrNoFundsProvided = 86,
    ErrInvalidSignature = 87,
    ErrNoFundsToUnlock = 88,
    ErrNoMsgsProvided = 89,
    ErrNoMsgProvided = 90,
    ErrInvalidAmount = 91,
    ErrFeatureDisabled = 92,
    ErrTooMuchOrderMargin = 93,
    ErrBadSubaccountNonce = 94,
    ErrInsufficientFunds = 95,
    ErrPostOnlyMode = 96,
    ErrClientOrderIdAlreadyExists = 97,
    ErrInvalidCid = 98
}
export declare enum ChainAuctionErrorCodes {
    ErrBidInvalid = 1,
    ErrBidRound = 2
}
export declare enum ChainAuthZErrorCodes {
    ErrNoAuthorizationFound = 2,
    ErrInvalidExpirationTime = 3,
    ErrUnknownAuthorizationType = 4,
    ErrNoGrantKeyFound = 5,
    ErrAuthorizationExpired = 6,
    ErrGranteeIsGranter = 7,
    ErrAuthorizationNumOfSigners = 9,
    ErrNegativeMaxTokens = 12
}
export declare enum ChainInsuranceErrorCodes {
    ErrInsuranceFundAlreadyExists = 1,
    ErrInsuranceFundNotFound = 2,
    ErrRedemptionAlreadyExists = 3,
    ErrInvalidDepositAmount = 4,
    ErrInvalidDepositDenom = 5,
    ErrPayoutTooLarge = 6,
    ErrInvalidTicker = 7,
    ErrInvalidQuoteDenom = 8,
    ErrInvalidOracle = 9,
    ErrInvalidExpirationTime = 10,
    ErrInvalidMarketID = 11,
    ErrInvalidShareDenom = 12
}
export declare enum ChainOcrErrorCodes {
    ErrStaleReport = 1,
    ErrIncompleteProposal = 2,
    ErrRepeatedAddress = 3,
    ErrTooManySigners = 4,
    ErrIncorrectConfig = 5,
    ErrConfigDigestNotMatch = 6,
    ErrWrongNumberOfSignatures = 7,
    ErrIncorrectSignature = 8,
    ErrNoTransmitter = 9,
    ErrIncorrectTransmissionData = 10,
    ErrNoTransmissionsFound = 11,
    ErrMedianValueOutOfBounds = 12,
    ErrIncorrectRewardPoolDenom = 13,
    ErrNoRewardPool = 14,
    ErrInvalidPayees = 15,
    ErrModuleAdminRestricted = 16,
    ErrFeedAlreadyExists = 17,
    ErrFeedDoesntExists = 19,
    ErrAdminRestricted = 20,
    ErrInsufficientRewardPool = 21,
    ErrPayeeAlreadySet = 22,
    ErrPayeeRestricted = 23,
    ErrFeedConfigNotFound = 24
}
export declare enum ChainOracleErrorCodes {
    ErrEmptyRelayerAddr = 1,
    ErrBadRatesCount = 2,
    ErrBadResolveTimesCount = 3,
    ErrBadRequestIDsCount = 4,
    ErrRelayerNotAuthorized = 5,
    ErrBadPriceFeedBaseCount = 6,
    ErrBadPriceFeedQuoteCount = 7,
    ErrUnsupportedOracleType = 8,
    ErrBadMessagesCount = 9,
    ErrBadCoinbaseMessage = 10,
    ErrInvalidEthereumSignature = 11,
    ErrBadCoinbaseMessageTimestamp = 12,
    ErrCoinbasePriceNotFound = 13,
    ErrBadPrice = 14,
    ErrPriceTooLarge = 15,
    ErrInvalidBandIBCRequest = 16,
    ErrSample = 17,
    ErrInvalidPacketTimeout = 18,
    ErrBadSymbolsCount = 19,
    ErrBadIBCPortBind = 20,
    ErrInvalidPortID = 21,
    ErrInvalidChannelID = 22,
    ErrBadRequestInterval = 23,
    ErrInvalidBandIBCUpdateRequest = 24,
    ErrBandIBCRequestNotFound = 25,
    ErrEmptyBaseInfo = 26,
    ErrEmptyProvider = 27,
    ErrInvalidProvider = 28,
    ErrInvalidSymbol = 29,
    ErrRelayerAlreadyExists = 30,
    ErrProviderPriceNotFound = 31,
    ErrInvalidOracleRequest = 32,
    ErrOraclePriceNotFound = 33
}
export declare enum ChainPeggyErrorCodes {
    ErrInternal = 1,
    ErrDuplicate = 2,
    ErrInvalid = 3,
    ErrTimeout = 4,
    ErrUnknown = 5,
    ErrEmpty = 6,
    ErrOutdated = 7,
    ErrUnsupported = 8,
    ErrNonContiguousEventNonce = 9,
    ErrNoUnbatchedTxsFound = 10,
    ErrResetDelegateKeys = 11,
    ErrSupplyOverflow = 12,
    ErrInvalidEthSender = 13,
    ErrInvalidEthDestination = 14
}
export declare enum ChainTokenFactoryErrorCodes {
    ErrDenomExists = 2,
    ErrUnauthorized = 3,
    ErrInvalidDenom = 4,
    ErrInvalidCreator = 5,
    ErrInvalidAuthorityMetadata = 6,
    ErrInvalidGenesis = 7,
    ErrSubdenomTooLong = 8,
    ErrSubdenomTooShort = 9,
    ErrSubdenomNestedTooShort = 10,
    ErrCreatorTooLong = 11,
    ErrDenomDoesNotExist = 12,
    ErrAmountNotPositive = 13
}
export declare enum ChainWasmXErrorCodes {
    ErrInvalidGasLimit = 1,
    ErrInvalidGasPrice = 2,
    ErrInvalidContractAddress = 3,
    ErrAlreadyRegistered = 4,
    ErrDuplicateContract = 5,
    ErrNoContractAddresses = 6,
    ErrInvalidCodeId = 7
}
export declare enum ChainStakingErrorCodes {
    ErrEmptyValidatorAddr = 2,
    ErrNoValidatorFound = 3,
    ErrValidatorOwnerExists = 4,
    ErrValidatorPubKeyExists = 5,
    ErrValidatorPubKeyTypeNotSupported = 6,
    ErrValidatorJailed = 7,
    ErrBadRemoveValidator = 8,
    ErrCommissionNegative = 9,
    ErrCommissionHuge = 10,
    ErrCommissionGTMaxRate = 11,
    ErrCommissionUpdateTime = 12,
    ErrCommissionChangeRateNegative = 13,
    ErrCommissionChangeRateGTMaxRate = 14,
    ErrCommissionGTMaxChangeRate = 15,
    ErrSelfDelegationBelowMinimum = 16,
    ErrMinSelfDelegationDecreased = 17,
    ErrEmptyDelegatorAddr = 18,
    ErrNoDelegation = 19,
    ErrBadDelegatorAddr = 20,
    ErrNoDelegatorForAddress = 21,
    ErrInsufficientShares = 22,
    ErrDelegationValidatorEmpty = 23,
    ErrNotEnoughDelegationShares = 24,
    ErrNotMature = 25,
    ErrNoUnbondingDelegation = 26,
    ErrMaxUnbondingDelegationEntries = 27,
    ErrNoRedelegation = 28,
    ErrSelfRedelegation = 29,
    ErrTinyRedelegationAmount = 30,
    ErrBadRedelegationDst = 31,
    ErrTransitiveRedelegation = 32,
    ErrMaxRedelegationEntries = 33,
    ErrDelegatorShareExRateInvalid = 34,
    ErrBothShareMsgsGiven = 35,
    ErrNeitherShareMsgsGiven = 36,
    ErrInvalidHistoricalInfo = 37,
    ErrNoHistoricalInfo = 38,
    ErrEmptyValidatorPubKey = 39,
    ErrCommissionLTMinRate = 40,
    ErrUnbondingNotFound = 41,
    ErrUnbondingOnHoldRefCountNegative = 42
}
export declare enum ChainGovErrorCodes {
    ErrUnknownProposal = 2,
    ErrInactiveProposal = 3,
    ErrAlreadyActiveProposal = 4,
    ErrInvalidProposalContent = 5,
    ErrInvalidProposalType = 6,
    ErrInvalidVote = 7,
    ErrInvalidGenesis = 8,
    ErrNoProposalHandlerExists = 9,
    ErrUnroutableProposalMsg = 10,
    ErrNoProposalMsgs = 11,
    ErrInvalidProposalMsg = 12,
    ErrInvalidSigner = 13,
    ErrInvalidSignalMsg = 14,
    ErrMetadataTooLong = 15,
    ErrMinDepositTooSmall = 16,
    ErrProposalNotFound = 17,
    ErrInvalidProposer = 18,
    ErrNoDeposits = 19,
    ErrVotingPeriodEnded = 20,
    ErrInvalidProposal = 21
}
export declare enum ChainDistributionErrorCodes {
    ErrEmptyDelegatorAddr = 2,
    ErrEmptyWithdrawAddr = 3,
    ErrEmptyValidatorAddr = 4,
    ErrEmptyDelegationDistInfo = 5,
    ErrNoValidatorDistInfo = 6,
    ErrNoValidatorCommission = 7,
    ErrSetWithdrawAddrDisabled = 8,
    ErrBadDistribution = 9,
    ErrInvalidProposalAmount = 10,
    ErrEmptyProposalRecipient = 11,
    ErrNoValidatorExists = 12,
    ErrNoDelegationExists = 13
}
export declare enum ChainBankErrorCodes {
    ErrNoInputs = 2,
    ErrNoOutputs = 3,
    ErrInputOutputMismatch = 4,
    ErrSendDisabled = 5,
    ErrDenomMetadataNotFound = 6,
    ErrInvalidKey = 7,
    ErrDuplicateEntry = 8,
    ErrMultipleSenders = 9
}
export declare enum ChainWasmErrorCodes {
    ErrCreateFailed = 2,
    ErrAccountExists = 3,
    ErrInstantiateFailed = 4,
    ErrExecuteFailed = 5,
    ErrGasLimit = 6,
    ErrInvalidGenesis = 7,
    ErrNotFound = 8,
    ErrQueryFailed = 9,
    ErrInvalidMsg = 10,
    ErrMigrationFailed = 11,
    ErrEmpty = 12,
    ErrLimit = 13,
    ErrInvalid = 14,
    ErrDuplicate = 15,
    ErrMaxIBCChannels = 16,
    ErrUnsupportedForContract = 17,
    ErrPinContractFailed = 18,
    ErrUnpinContractFailed = 19,
    ErrUnknownMsg = 20,
    ErrInvalidEvent = 21
}
export type IndexerApiErrorCode = number;
export declare const UnspecifiedErrorCode = -1;
export type ErrorCode = grpcPkg.grpc.Code | StatusCodes | typeof UnspecifiedErrorCode | GrpcErrorCode;
export declare const GRPC_REQUEST_FAILED = GrpcErrorCode.Unavailable;
export type ErrorContextCode = ChainAuctionErrorCodes | ChainAuthZErrorCodes | ChainCosmosErrorCode | ChainExchangeModuleErrorCode | ChainInsuranceErrorCodes | ChainOcrErrorCodes | ChainOracleErrorCodes | ChainPeggyErrorCodes | ChainTokenFactoryErrorCodes | ChainWasmXErrorCodes | ChainDistributionErrorCodes | ChainBankErrorCodes | ChainGovErrorCodes | ChainStakingErrorCodes | ChainWasmErrorCodes | ErrorCode | number | typeof UnspecifiedErrorCode;
