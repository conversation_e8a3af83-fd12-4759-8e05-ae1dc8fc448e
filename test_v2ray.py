#!/usr/bin/env python3
"""
测试V2Ray启动
"""

import json
import subprocess
import tempfile
import time
from pathlib import Path

def test_v2ray():
    """测试V2Ray启动"""
    v2ray_path = Path("tools/v2ray/v2ray.exe")
    
    if not v2ray_path.exists():
        print(f"❌ V2Ray不存在: {v2ray_path}")
        return False
    
    # 简单的V2Ray配置
    config = {
        "log": {
            "loglevel": "info"
        },
        "inbounds": [
            {
                "tag": "socks",
                "port": 1080,
                "listen": "127.0.0.1",
                "protocol": "socks",
                "settings": {
                    "auth": "noauth",
                    "udp": True
                }
            }
        ],
        "outbounds": [
            {
                "tag": "direct",
                "protocol": "freedom"
            }
        ]
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(config, f, indent=2)
        config_file = f.name
    
    print(f"✅ 配置文件创建: {config_file}")
    
    try:
        # 启动V2Ray (新版本使用 run -config 命令)
        print("🚀 启动V2Ray...")
        process = subprocess.Popen(
            [str(v2ray_path), "run", "-config", config_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待启动
        time.sleep(3)
        
        if process.poll() is None:
            print(f"✅ V2Ray启动成功，PID: {process.pid}")
            
            # 停止V2Ray
            process.terminate()
            process.wait(timeout=5)
            print("✅ V2Ray已停止")
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ V2Ray启动失败")
            print(f"标准输出: {stdout}")
            print(f"错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理配置文件
        try:
            Path(config_file).unlink()
        except:
            pass

if __name__ == '__main__':
    print("🔧 测试V2Ray启动")
    print("=" * 30)
    
    if test_v2ray():
        print("🎉 V2Ray测试成功！")
    else:
        print("❌ V2Ray测试失败！")
