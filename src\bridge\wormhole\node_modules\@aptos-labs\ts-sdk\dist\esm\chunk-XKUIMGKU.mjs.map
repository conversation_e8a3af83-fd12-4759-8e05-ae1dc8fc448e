{"version": 3, "sources": ["../../src/transactions/instances/chainId.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Serializer, Serializable } from \"../../bcs/serializer\";\nimport { Deserializer } from \"../../bcs/deserializer\";\n\n/**\n * Represents a ChainId that can be serialized and deserialized.\n *\n * @extends Serializable\n * @group Implementation\n * @category Transactions\n */\nexport class ChainId extends Serializable {\n  public readonly chainId: number;\n\n  /**\n   * Initializes a new instance of the class with the specified chain ID.\n   *\n   * @param chainId - The ID of the blockchain network to be used.\n   * @group Implementation\n   * @category Transactions\n   */\n  constructor(chainId: number) {\n    super();\n    this.chainId = chainId;\n  }\n\n  /**\n   * Serializes the current object using the provided serializer.\n   * This function helps in converting the object into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category Transactions\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeU8(this.chainId);\n  }\n\n  /**\n   * Deserializes a ChainId from the provided deserializer.\n   * This function allows you to reconstruct a ChainId object from serialized data.\n   *\n   * @param deserializer - The deserializer instance used to read the serialized data.\n   * @group Implementation\n   * @category Transactions\n   */\n  static deserialize(deserializer: Deserializer): ChainId {\n    const chainId = deserializer.deserializeU8();\n    return new ChainId(chainId);\n  }\n}\n"], "mappings": "yCAaO,IAAMA,EAAN,MAAMC,UAAgBC,CAAa,CAUxC,YAAYC,EAAiB,CAC3B,MAAM,EACN,KAAK,QAAUA,CACjB,CAUA,UAAUC,EAA8B,CACtCA,EAAW,YAAY,KAAK,OAAO,CACrC,CAUA,OAAO,YAAYC,EAAqC,CACtD,IAAMF,EAAUE,EAAa,cAAc,EAC3C,OAAO,IAAIJ,EAAQE,CAAO,CAC5B,CACF", "names": ["ChainId", "_ChainId", "Serializable", "chainId", "serializer", "deserializer"]}