{"version": 3, "sources": ["../../src/utils/memoize.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * The global cache Map shared across all functions.  Must keep care to ensure that the\n * cache keys are unique across all functions.\n * @group Implementation\n * @category Utils\n */\nconst cache = new Map<string, { value: any; timestamp: number }>();\n\n/**\n * A memoize higher-order function to cache the response of an async function.\n * This function helps to improve performance by avoiding repeated calls to the same async function with the same arguments\n * within a specified time-to-live (TTL).\n *\n * @param func The async function to cache the result of.\n * @param key The cache key used to store the result.\n * @param ttlMs The time-to-live in milliseconds for cached data.\n * @returns The cached or latest result.\n * @group Implementation\n * @category Utils\n */\nexport function memoizeAsync<T>(\n  func: (...args: any[]) => Promise<T>,\n  key: string,\n  ttlMs?: number,\n): (...args: any[]) => Promise<T> {\n  return async (...args: any[]) => {\n    // Check if the cached result exists and is within TTL\n    if (cache.has(key)) {\n      const { value, timestamp } = cache.get(key)!;\n      if (ttlMs === undefined || Date.now() - timestamp <= ttlMs) {\n        return value;\n      }\n    }\n\n    // If not cached or TTL expired, compute the result\n    const result = await func(...args);\n\n    // Cache the result with a timestamp\n    cache.set(key, { value: result, timestamp: Date.now() });\n\n    return result;\n  };\n}\n\n/**\n * Caches the result of a function call to improve performance on subsequent calls with the same arguments.\n *\n * @param key - The key to cache on, all accesses by this key will return the cached value.\n * @param func - The function whose result will be cached.\n * @param ttlMs - The time-to-live in milliseconds for cached data.\n * @returns A memoized version of the provided function that returns the cached result if available and within TTL.\n * @group Implementation\n * @category Utils\n */\nexport function memoize<T>(func: (...args: any[]) => T, key: string, ttlMs?: number): (...args: any[]) => T {\n  return (...args: any[]) => {\n    // Check if the cached result exists and is within TTL\n    if (cache.has(key)) {\n      const { value, timestamp } = cache.get(key)!;\n      if (ttlMs === undefined || Date.now() - timestamp <= ttlMs) {\n        return value;\n      }\n    }\n\n    // If not cached or TTL expired, compute the result\n    const result = func(...args);\n\n    // Cache the result with a timestamp\n    cache.set(key, { value: result, timestamp: Date.now() });\n\n    return result;\n  };\n}\n"], "mappings": "AASA,IAAMA,EAAQ,IAAI,IAcX,SAASC,EACdC,EACAC,EACAC,EACgC,CAChC,MAAO,UAAUC,IAAgB,CAE/B,GAAIL,EAAM,IAAIG,CAAG,EAAG,CAClB,GAAM,CAAE,MAAAG,EAAO,UAAAC,CAAU,EAAIP,EAAM,IAAIG,CAAG,EAC1C,GAAIC,IAAU,QAAa,KAAK,IAAI,EAAIG,GAAaH,EACnD,OAAOE,CAEX,CAGA,IAAME,EAAS,MAAMN,EAAK,GAAGG,CAAI,EAGjC,OAAAL,EAAM,IAAIG,EAAK,CAAE,MAAOK,EAAQ,UAAW,KAAK,IAAI,CAAE,CAAC,EAEhDA,CACT,CACF,CAYO,SAASC,EAAWP,EAA6BC,EAAaC,EAAuC,CAC1G,MAAO,IAAIC,IAAgB,CAEzB,GAAIL,EAAM,IAAIG,CAAG,EAAG,CAClB,GAAM,CAAE,MAAAG,EAAO,UAAAC,CAAU,EAAIP,EAAM,IAAIG,CAAG,EAC1C,GAAIC,IAAU,QAAa,KAAK,IAAI,EAAIG,GAAaH,EACnD,OAAOE,CAEX,CAGA,IAAME,EAASN,EAAK,GAAGG,CAAI,EAG3B,OAAAL,EAAM,IAAIG,EAAK,CAAE,MAAOK,EAAQ,UAAW,KAAK,IAAI,CAAE,CAAC,EAEhDA,CACT,CACF", "names": ["cache", "memoizeAsync", "func", "key", "ttlMs", "args", "value", "timestamp", "result", "memoize"]}