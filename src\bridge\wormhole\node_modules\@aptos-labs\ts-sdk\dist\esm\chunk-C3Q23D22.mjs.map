{"version": 3, "sources": ["../../src/core/crypto/hdKey.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { hmac } from \"@noble/hashes/hmac\";\nimport { sha512 } from \"@noble/hashes/sha512\";\nimport * as bip39 from \"@scure/bip39\";\n\n/**\n * Contains the derived cryptographic key as a Uint8Array.\n * @group Implementation\n * @category Serialization\n */\nexport type DerivedKeys = {\n  key: Uint8Array;\n  chainCode: Uint8Array;\n};\n\n/**\n * Aptos derive path is 637\n * @group Implementation\n * @category Serialization\n */\nexport const APTOS_HARDENED_REGEX = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+'\\/[0-9]+'?$/;\n\n/**\n * @group Implementation\n * @category Serialization\n */\nexport const APTOS_BIP44_REGEX = /^m\\/44'\\/637'\\/[0-9]+'\\/[0-9]+\\/[0-9]+$/;\n\n/**\n * Supported key types and their associated seeds.\n * @group Implementation\n * @category Serialization\n */\nexport enum KeyType {\n  ED25519 = \"ed25519 seed\",\n}\n\n/**\n * @group Implementation\n * @category Serialization\n */\nexport const HARDENED_OFFSET = 0x80000000;\n\n/**\n * Validate a BIP-44 derivation path string to ensure it meets the required format.\n * This function checks if the provided path adheres to the BIP-44 standard for Secp256k1.\n * Parse and validate a path that is compliant to BIP-44 in form m/44'/637'/{account_index}'/{change_index}/{address_index}\n * for Secp256k1\n *\n * Note that for Secp256k1, the last two components must be non-hardened.\n *\n * @param path - The path string to validate (e.g. `m/44'/637'/0'/0/0`).\n * @group Implementation\n * @category Serialization\n */\nexport function isValidBIP44Path(path: string): boolean {\n  return APTOS_BIP44_REGEX.test(path);\n}\n\n/**\n * Aptos derive path is 637\n *\n * Parse and validate a path that is compliant to SLIP-0010 and BIP-44\n * in form m/44'/637'/{account_index}'/{change_index}'/{address_index}'.\n * See SLIP-0010 {@link https://github.com/satoshilabs/slips/blob/master/slip-0044.md}\n * See BIP-44 {@link https://github.com/bitcoin/bips/blob/master/bip-0044.mediawiki}\n *\n * Note that for Ed25519, all components must be hardened.\n * This is because non-hardened [PK] derivation would not work due to Ed25519's lack of a key homomorphism.\n * Specifically, you cannot derive the PK associated with derivation path a/b/c given the PK of a/b.\n * This is because the PK in Ed25519 is, more or less, computed as 𝑔𝐻(𝑠𝑘),\n * with the hash function breaking the homomorphism.\n *\n * @param path - The derivation path string to validate (e.g. `m/44'/637'/0'/0'/0'`).\n * @group Implementation\n * @category Serialization\n */\nexport function isValidHardenedPath(path: string): boolean {\n  return APTOS_HARDENED_REGEX.test(path);\n}\n\n/**\n * @group Implementation\n * @category Serialization\n */\nexport const deriveKey = (hashSeed: Uint8Array | string, data: Uint8Array | string): DerivedKeys => {\n  const digest = hmac.create(sha512, hashSeed).update(data).digest();\n  return {\n    key: digest.slice(0, 32),\n    chainCode: digest.slice(32),\n  };\n};\n\n/**\n * Derive a child key from the private key\n * @param key\n * @param chainCode\n * @param index\n * @group Implementation\n * @category Serialization\n */\nexport const CKDPriv = ({ key, chainCode }: DerivedKeys, index: number): DerivedKeys => {\n  const buffer = new ArrayBuffer(4);\n  new DataView(buffer).setUint32(0, index);\n  const indexBytes = new Uint8Array(buffer);\n  const zero = new Uint8Array([0]);\n  const data = new Uint8Array([...zero, ...key, ...indexBytes]);\n  return deriveKey(chainCode, data);\n};\n\nconst removeApostrophes = (val: string): string => val.replace(/'/g, \"\");\n\n/**\n * Splits derive path into segments\n * @param path\n * @group Implementation\n * @category Serialization\n */\nexport const splitPath = (path: string): Array<string> => path.split(\"/\").slice(1).map(removeApostrophes);\n\n/**\n * Normalizes the mnemonic by removing extra whitespace and making it lowercase\n * @param mnemonic the mnemonic seed phrase\n * @group Implementation\n * @category Serialization\n */\nexport const mnemonicToSeed = (mnemonic: string): Uint8Array => {\n  const normalizedMnemonic = mnemonic\n    .trim()\n    .split(/\\s+/)\n    .map((part) => part.toLowerCase())\n    .join(\" \");\n  return bip39.mnemonicToSeedSync(normalizedMnemonic);\n};\n"], "mappings": "AAGA,OAAS,QAAAA,MAAY,qBACrB,OAAS,UAAAC,MAAc,uBACvB,UAAYC,MAAW,eAiBhB,IAAMC,EAAuB,6CAMvBC,EAAoB,0CAOrBC,OACVA,EAAA,QAAU,eADAA,OAAA,IAQCC,EAAkB,WAcxB,SAASC,EAAiBC,EAAuB,CACtD,OAAOJ,EAAkB,KAAKI,CAAI,CACpC,CAoBO,SAASC,EAAoBD,EAAuB,CACzD,OAAOL,EAAqB,KAAKK,CAAI,CACvC,CAMO,IAAME,EAAY,CAACC,EAA+BC,IAA2C,CAClG,IAAMC,EAASb,EAAK,OAAOC,EAAQU,CAAQ,EAAE,OAAOC,CAAI,EAAE,OAAO,EACjE,MAAO,CACL,IAAKC,EAAO,MAAM,EAAG,EAAE,EACvB,UAAWA,EAAO,MAAM,EAAE,CAC5B,CACF,EAUaC,EAAU,CAAC,CAAE,IAAAC,EAAK,UAAAC,CAAU,EAAgBC,IAA+B,CACtF,IAAMC,EAAS,IAAI,YAAY,CAAC,EAChC,IAAI,SAASA,CAAM,EAAE,UAAU,EAAGD,CAAK,EACvC,IAAME,EAAa,IAAI,WAAWD,CAAM,EAClCE,EAAO,IAAI,WAAW,CAAC,CAAC,CAAC,EACzBR,EAAO,IAAI,WAAW,CAAC,GAAGQ,EAAM,GAAGL,EAAK,GAAGI,CAAU,CAAC,EAC5D,OAAOT,EAAUM,EAAWJ,CAAI,CAClC,EAEMS,EAAqBC,GAAwBA,EAAI,QAAQ,KAAM,EAAE,EAQ1DC,EAAaf,GAAgCA,EAAK,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,IAAIa,CAAiB,EAQ3FG,EAAkBC,GAAiC,CAC9D,IAAMC,EAAqBD,EACxB,KAAK,EACL,MAAM,KAAK,EACX,IAAKE,GAASA,EAAK,YAAY,CAAC,EAChC,KAAK,GAAG,EACX,OAAa,qBAAmBD,CAAkB,CACpD", "names": ["hmac", "sha512", "bip39", "APTOS_HARDENED_REGEX", "APTOS_BIP44_REGEX", "KeyType", "HARDENED_OFFSET", "isValidBIP44Path", "path", "isValidHardenedPath", "<PERSON><PERSON><PERSON>", "hashSeed", "data", "digest", "CKDPriv", "key", "chainCode", "index", "buffer", "indexBytes", "zero", "removeApostrophes", "val", "splitPath", "mnemonicToSeed", "mnemonic", "normalizedMnemonic", "part"]}