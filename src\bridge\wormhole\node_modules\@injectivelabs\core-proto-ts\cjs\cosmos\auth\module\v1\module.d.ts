import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cosmos.auth.module.v1";
/** Module is the config object for the auth module. */
export interface Module {
    /** bech32_prefix is the bech32 account prefix for the app. */
    bech32Prefix: string;
    /** module_account_permissions are module account permissions. */
    moduleAccountPermissions: ModuleAccountPermission[];
    /** authority defines the custom module authority. If not set, defaults to the governance module. */
    authority: string;
}
/** ModuleAccountPermission represents permissions for a module account. */
export interface ModuleAccountPermission {
    /** account is the name of the module. */
    account: string;
    /**
     * permissions are the permissions this module has. Currently recognized
     * values are minter, burner and staking.
     */
    permissions: string[];
}
export declare const Module: {
    encode(message: Module, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Module;
    fromJSON(object: any): Module;
    toJSON(message: Module): unknown;
    create(base?: DeepPartial<Module>): Module;
    fromPartial(object: DeepPartial<Module>): Module;
};
export declare const ModuleAccountPermission: {
    encode(message: ModuleAccountPermission, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ModuleAccountPermission;
    fromJSON(object: any): ModuleAccountPermission;
    toJSON(message: ModuleAccountPermission): unknown;
    create(base?: DeepPartial<ModuleAccountPermission>): ModuleAccountPermission;
    fromPartial(object: DeepPartial<ModuleAccountPermission>): ModuleAccountPermission;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
