import { Snake<PERSON>aseKeys } from 'snakecase-keys';
import { TypedDataField } from '../tx/eip712/types.js';
/**
 * @category Messages
 */
export declare abstract class MsgBase<Params, ProtoRepresentation extends Object, ObjectRepresentation extends Record<string, unknown> = {}> {
    params: Params;
    constructor(params: Params);
    abstract toProto(): ProtoRepresentation;
    abstract toData(): ProtoRepresentation & {
        '@type': string;
    };
    abstract toDirectSign(): {
        type: string;
        message: ProtoRepresentation;
    };
    abstract toAmino(): {
        type: string;
        value: ObjectRepresentation | SnakeCaseKeys<ProtoRepresentation>;
    };
    abstract toBinary(): Uint8Array;
    /** @deprecated - use toWeb3Gw instead, renamed for clarity */
    toWeb3(): ObjectRepresentation | (SnakeCaseKeys<ProtoRepresentation> & {
        '@type': string;
    });
    abstract toWeb3Gw(): ObjectRepresentation | (SnakeCaseKeys<ProtoRepresentation> & {
        '@type': string;
    });
    toJSON(): string;
    /**
     * Returns the types of the message for EIP712
     */
    toEip712Types(): Map<string, TypedDataField[]>;
    /**
     * Returns the values of the message for EIP712
     */
    toEip712(): {
        type: string;
        value: ObjectRepresentation | SnakeCaseKeys<ProtoRepresentation>;
    };
    /**
     * Returns the values of the message for EIP712_V2
     */
    toEip712V2(): ObjectRepresentation | (SnakeCaseKeys<ProtoRepresentation> & {
        '@type': string;
    });
    toDirectSignJSON(): string;
}
