#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import yaml
import os
import time
import sys
from pathlib import Path
from datetime import datetime
import logging
import concurrent.futures
from web3 import Web3

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("get_pool_tokens.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 获取脚本所在目录
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
# 项目根目录
PROJECT_ROOT = SCRIPT_DIR.parent.parent
# 输入文件路径
INPUT_FILE = PROJECT_ROOT / "data" / "utils" / "token" / "all_pool_addresses_geckoterminal.txt"
# 输出文件路径
OUTPUT_DIR = PROJECT_ROOT / "data" / "utils" / "token"
OUTPUT_FILE = OUTPUT_DIR / "pool_tokens.json"
# 每个网络的token保存路径
TOKENS_DIR = OUTPUT_DIR / "network_tokens"
# 配置文件路径
CONFIG_FILE = PROJECT_ROOT / "config" / "config.yaml"
# 请求URL基础部分
BASE_URL = "https://api.geckoterminal.com/api/v2"
# 最大重试次数
MAX_RETRIES = 3
# 每批处理的地址数量（GeckoTerminal API限制为30个）
BATCH_SIZE = 30

# ERC20代币的ABI
ERC20_ABI = [
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "name",
        "outputs": [{"name": "", "type": "string"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    }
]

# 网络RPC URL映射
NETWORK_RPC_MAP = {
    "eth": "ethereum",
    "polygon_pos": "polygon",
    "bsc": "bsc",
    "arbitrum": "arbitrum",
    "base": "base",
    "avax": "avalanche",
    "optimism": "optimism"
}

# 网络名称映射
NETWORK_NAME_MAP = {
    "以太坊": "eth",
    "Polygon PoS": "polygon_pos",
    "币安智能链": "bsc",
    "Arbitrum": "arbitrum",
    "Optimism": "optimism",
    "Base": "base",
    "Avalanche": "avax",
    "Fantom": "ftm",
    "zkSync Era": "zksync",
    "Linea": "linea",
    "Manta": "manta",
    "Gnosis": "gnosis",
    "Cronos": "cronos",
    "Moonbeam": "moonbeam",
    "Moonriver": "moonriver",
    "Moonbase": "moonbase"
}
# 反向映射，用于查找中文名
REVERSE_NETWORK_MAP = {v: k for k, v in NETWORK_NAME_MAP.items()}

def load_config():
    """从配置文件加载配置"""
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"读取配置文件时出错: {e}")
        return {}

def get_proxy_from_config(config):
    """从配置中获取代理设置"""
    if not config or 'proxy' not in config or not config['proxy'].get('enabled', False):
        return None
    
    proxies = {}
    if 'http' in config['proxy']:
        proxies['http'] = config['proxy']['http']
    if 'https' in config['proxy']:
        proxies['https'] = config['proxy']['https']
    
    return proxies if proxies else None

def get_rpc_url_for_network(network, config):
    """获取指定网络的RPC URL"""
    config_network = NETWORK_RPC_MAP.get(network)
    if not config_network or not config or 'dex' not in config:
        return None
    
    network_config = config['dex'].get(config_network)
    if not network_config:
        return None
    
    # 获取主RPC URL
    rpc_url = network_config.get('rpc_url')
    if not rpc_url and 'rpc' in network_config:
        rpc_url = network_config['rpc'].get('endpoint')
    
    return rpc_url

def get_backup_rpc_urls(network, config):
    """获取指定网络的备用RPC URL列表"""
    config_network = NETWORK_RPC_MAP.get(network)
    if not config_network or not config or 'dex' not in config:
        return []
    
    network_config = config['dex'].get(config_network)
    if not network_config:
        return []
    
    # 获取备用RPC URL
    backup_urls = network_config.get('backup_rpc_urls', [])
    if not backup_urls and 'rpc' in network_config:
        backup_urls = network_config['rpc'].get('backup_endpoints', [])
    
    return backup_urls

def get_token_decimals_web3(token_address: str, network: str, config):
    """通过Web3接口获取代币小数位数"""
    if not token_address or not token_address.startswith('0x'):
        return None
    
    # 先检查本地保存的代币数据中是否已有精度信息
    tokens = load_existing_network_tokens(network)
    token_address_lower = token_address.lower()
    if token_address_lower in tokens and tokens[token_address_lower].get("decimals") is not None:
        decimals = tokens[token_address_lower]["decimals"]
        logger.info(f"从本地数据中获取到代币 {token_address} 的小数位数: {decimals}")
        return decimals
    
    # 获取网络RPC URL
    rpc_url = get_rpc_url_for_network(network, config)
    if not rpc_url:
        logger.warning(f"无法获取网络 {network} 的RPC URL")
        return None
    
    # 设置Web3连接
    w3 = None
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        if not w3.is_connected():
            # 尝试备用RPC
            backup_urls = get_backup_rpc_urls(network, config)
            for backup_url in backup_urls:
                try:
                    w3 = Web3(Web3.HTTPProvider(backup_url))
                    if w3.is_connected():
                        logger.info(f"使用备用RPC连接到 {network} 网络: {backup_url}")
                        break
                except Exception as e:
                    continue
    except Exception as e:
        logger.error(f"连接到 {network} 网络时出错: {e}")
        return None
    
    if not w3 or not w3.is_connected():
        logger.error(f"无法连接到 {network} 网络")
        return None
    
    # 创建合约实例
    try:
        token_contract = w3.eth.contract(address=w3.to_checksum_address(token_address), abi=ERC20_ABI)
        
        # 获取代币小数位数
        decimals = None
        retries = 0
        max_retries = 3
        while retries < max_retries:
            try:
                decimals = token_contract.functions.decimals().call()
                break
            except Exception as e:
                logger.warning(f"获取代币 {token_address} 小数位数时出错（第{retries+1}次尝试）: {e}")
                retries += 1
                time.sleep(1)
        
        return int(decimals) if decimals is not None else None
    except Exception as e:
        logger.error(f"获取代币 {token_address} 小数位数时出错: {e}")
        return None

def get_token_details_from_blockchain(token_address: str, network: str, config):
    """从区块链获取代币详细信息"""
    if not token_address or not token_address.startswith('0x'):
        return {"address": token_address, "decimals": None, "name": "Unknown", "symbol": "UNK"}
    
    # 先检查本地保存的代币数据中是否已有信息
    tokens = load_existing_network_tokens(network)
    token_address_lower = token_address.lower()
    if token_address_lower in tokens:
        token_info = tokens[token_address_lower]
        logger.info(f"从本地数据中获取到代币 {token_address} 的信息: {token_info.get('symbol')}，小数位: {token_info.get('decimals')}")
        return token_info
    
    # 获取网络RPC URL
    rpc_url = get_rpc_url_for_network(network, config)
    if not rpc_url:
        logger.warning(f"无法获取网络 {network} 的RPC URL")
        return {"address": token_address, "decimals": None, "name": "Unknown", "symbol": "UNK"}
    
    # 设置Web3连接
    w3 = None
    try:
        w3 = Web3(Web3.HTTPProvider(rpc_url))
        if not w3.is_connected():
            # 尝试备用RPC
            backup_urls = get_backup_rpc_urls(network, config)
            for backup_url in backup_urls:
                try:
                    w3 = Web3(Web3.HTTPProvider(backup_url))
                    if w3.is_connected():
                        logger.info(f"使用备用RPC连接到 {network} 网络: {backup_url}")
                        break
                except Exception:
                    continue
    except Exception as e:
        logger.error(f"连接到 {network} 网络时出错: {e}")
        return {"address": token_address, "decimals": None, "name": "Unknown", "symbol": "UNK"}
    
    if not w3 or not w3.is_connected():
        logger.error(f"无法连接到 {network} 网络")
        return {"address": token_address, "decimals": None, "name": "Unknown", "symbol": "UNK"}
    
    # 创建合约实例
    try:
        token_contract = w3.eth.contract(address=w3.to_checksum_address(token_address), abi=ERC20_ABI)
        
        # 获取代币信息
        token_info = {"address": token_address}
        
        # 获取小数位数
        try:
            token_info["decimals"] = token_contract.functions.decimals().call()
        except Exception as e:
            logger.warning(f"获取代币 {token_address} 小数位数时出错: {e}")
            token_info["decimals"] = None
        
        # 获取符号
        try:
            token_info["symbol"] = token_contract.functions.symbol().call()
        except Exception as e:
            logger.warning(f"获取代币 {token_address} 符号时出错: {e}")
            token_info["symbol"] = "UNK"
        
        # 获取名称
        try:
            token_info["name"] = token_contract.functions.name().call()
        except Exception as e:
            logger.warning(f"获取代币 {token_address} 名称时出错: {e}")
            token_info["name"] = "Unknown"
        
        return token_info
    except Exception as e:
        logger.error(f"获取代币 {token_address} 详细信息时出错: {e}")
        return {"address": token_address, "decimals": None, "name": "Unknown", "symbol": "UNK"}

def ensure_output_directory():
    """确保输出目录存在"""
    if not OUTPUT_DIR.exists():
        try:
            OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
            logger.info(f"已创建输出目录: {OUTPUT_DIR}")
        except Exception as e:
            logger.error(f"创建输出目录时出错: {e}")
            return False
    
    # 确保网络token目录存在
    if not TOKENS_DIR.exists():
        try:
            TOKENS_DIR.mkdir(parents=True, exist_ok=True)
            logger.info(f"已创建网络token目录: {TOKENS_DIR}")
        except Exception as e:
            logger.error(f"创建网络token目录时出错: {e}")
            return False
    
    return True

def parse_pool_addresses():
    """从输入文件解析池地址"""
    if not INPUT_FILE.exists():
        logger.error(f"输入文件 {INPUT_FILE} 不存在!")
        return {}
    
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        addresses_by_network = {}
        current_network = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.endswith("网络："):
                # 找到网络标记
                network_name = line[:-3]  # 去掉"网络："
                if network_name in NETWORK_NAME_MAP:
                    current_network = NETWORK_NAME_MAP[network_name]
                    addresses_by_network[current_network] = []
                else:
                    logger.warning(f"未知网络: {network_name}")
                    current_network = None
                continue
                
            if current_network and line.startswith("0x"):
                addresses_by_network[current_network].append(line)  # 保存原始格式的地址
        
        # 记录每个网络读取到的地址数量
        for network, addresses in addresses_by_network.items():
            logger.info(f"从文件中读取到 {len(addresses)} 个 {REVERSE_NETWORK_MAP.get(network, network)} 地址")
        
        return addresses_by_network
    except Exception as e:
        logger.error(f"解析池地址文件时出错: {e}")
        return {}

def load_existing_data():
    """加载已存在的数据"""
    if not OUTPUT_FILE.exists():
        logger.info(f"输出文件 {OUTPUT_FILE} 不存在，将创建新文件")
        return {}
    
    try:
        with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            logger.info(f"成功从 {OUTPUT_FILE} 加载现有数据")
            # 统计每个网络的池数量
            for network, pools in data.items():
                logger.info(f"  - {network} 网络有 {len(pools)} 个池")
            return data
    except Exception as e:
        logger.error(f"读取已存在数据时出错: {e}")
        return {}

def load_existing_network_tokens(network):
    """加载特定网络的已存在token数据"""
    token_file = TOKENS_DIR / f"{network}_tokens.json"
    if not token_file.exists():
        logger.info(f"网络 {network} 的token文件不存在，将创建新文件")
        return {}
    
    try:
        with open(token_file, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
            token_dict = {}
            for token in tokens:
                if "address" in token:
                    token_dict[token["address"].lower()] = token
            logger.info(f"成功从 {token_file} 加载 {len(token_dict)} 个token")
            return token_dict
    except Exception as e:
        logger.error(f"读取 {network} 网络token数据时出错: {e}")
        return {}

def save_data(data):
    """保存数据到输出文件"""
    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.info(f"数据已保存到 {OUTPUT_FILE}")
        return True
    except Exception as e:
        logger.error(f"保存数据时出错: {e}")
        return False

def save_network_data(network, new_pools):
    """保存单个网络的数据（使用追加模式而非替换）"""
    try:
        # 加载已存在的数据
        all_data = load_existing_data()
        
        # 检查是否已存在该网络的数据
        existing_pools = all_data.get(network, [])
        logger.info(f"现有数据中 {network} 网络有 {len(existing_pools)} 个池")
        
        # 创建字典，使用pool_address作为键，方便去重
        pool_dict = {pool.get("pool_address", "").lower(): pool for pool in existing_pools if "pool_address" in pool}
        
        # 添加新的池信息（如果已存在则不覆盖原有数据）
        new_count = 0
        skipped_count = 0
        new_pool_addresses = []
        all_pool_addresses = []  # 收集所有池地址用于保存
        
        for pool in new_pools:
            if "pool_address" in pool:
                pool_address = pool["pool_address"].lower()
                original_address = pool.get("original_address", pool_address)
                
                # 收集所有池地址
                all_pool_addresses.append(pool_address)
                
                # 检查pool_address是否已存在
                if pool_address in pool_dict:
                    skipped_count += 1
                    continue
                
                # 添加新池
                pool_dict[pool_address] = pool
                new_count += 1
                new_pool_addresses.append(original_address)
        
        # 将字典转换回列表
        merged_pools = list(pool_dict.values())
        
        # 更新总数据
        all_data[network] = merged_pools
        
        # 保存到文件
        save_data(all_data)
        
        # 保存所有处理过的池地址到专门的文件
        save_processed_pool_addresses(network, all_pool_addresses)
        
        # 日志输出处理结果
        logger.info(f"已追加 {new_count} 个新池信息到 {network} 网络，跳过 {skipped_count} 个已存在池，总计 {len(merged_pools)} 个池")
        if new_count > 0:
            logger.info(f"新添加的池地址示例: {', '.join(new_pool_addresses[:min(5, len(new_pool_addresses))])}" + 
                      (f"...等 {len(new_pool_addresses)} 个" if len(new_pool_addresses) > 5 else ""))
        
        # 提取并保存该网络的所有唯一token
        extract_and_save_network_tokens(network, merged_pools)
        
        return True
    except Exception as e:
        logger.error(f"保存 {network} 网络数据时出错: {e}")
        return False

def extract_and_save_network_tokens(network, pools):
    """提取并保存网络的唯一token（使用追加模式）"""
    try:
        # 加载已存在的token数据
        existing_tokens = load_existing_network_tokens(network)
        
        # 创建一个字典来存储唯一的token信息
        unique_tokens = existing_tokens.copy()
        new_tokens_count = 0
        updated_tokens_count = 0
        new_token_symbols = []
        
        for pool in pools:
            pool_address = pool.get("pool_address", "")
            if not pool_address:
                continue
                
            # 处理base_token
            if "base_token" in pool and "address" in pool["base_token"] and pool["base_token"]["address"]:
                token_address = pool["base_token"]["address"].lower()
                token_symbol = pool["base_token"].get("symbol", "")
                
                if token_address not in unique_tokens:
                    # 添加新token
                    unique_tokens[token_address] = {
                        "address": token_address,
                        "name": pool["base_token"].get("name", ""),
                        "symbol": token_symbol,
                        "decimals": pool["base_token"].get("decimals", 18),
                        "network": network
                    }
                    new_tokens_count += 1
                    new_token_symbols.append(token_symbol)
            
            # 处理quote_token
            if "quote_token" in pool and "address" in pool["quote_token"] and pool["quote_token"]["address"]:
                token_address = pool["quote_token"]["address"].lower()
                token_symbol = pool["quote_token"].get("symbol", "")
                
                if token_address not in unique_tokens:
                    # 添加新token
                    unique_tokens[token_address] = {
                        "address": token_address,
                        "name": pool["quote_token"].get("name", ""),
                        "symbol": token_symbol,
                        "decimals": pool["quote_token"].get("decimals", 18),
                        "network": network
                    }
                    new_tokens_count += 1
                    new_token_symbols.append(token_symbol)
        
        # 将字典转换为列表
        tokens_list = list(unique_tokens.values())
        
        # 保存到文件
        token_file = TOKENS_DIR / f"{network}_tokens.json"
        with open(token_file, 'w', encoding='utf-8') as f:
            json.dump(tokens_list, f, indent=2, ensure_ascii=False)
        
        logger.info(f"已添加 {new_tokens_count} 个新唯一代币，总计 {len(tokens_list)} 个唯一代币保存到 {token_file}")
        if new_tokens_count > 0:
            logger.info(f"新添加的代币示例: {', '.join(new_token_symbols[:min(5, len(new_token_symbols))])}" + 
                      (f"...等 {len(new_token_symbols)} 个" if len(new_token_symbols) > 5 else ""))
        
        return True
    except Exception as e:
        logger.error(f"提取并保存 {network} 网络token时出错: {e}")
        return False

def get_pool_info_batch(network, addresses, proxies=None):
    """
    批量获取池信息
    
    Args:
        network: 网络名称
        addresses: 地址列表
        proxies: 代理设置
        
    Returns:
        tokens_info: 获取到的代币信息
    """
    if not addresses:
        return []
    
    # 加载配置
    config = load_config()
    
    # 显示要处理的地址
    logger.info(f"准备处理以下地址: {', '.join(addresses[:min(5, len(addresses))])}" + 
               (f"...等 {len(addresses)} 个" if len(addresses) > 5 else ""))
    
    # 确保地址列表不超过API限制
    if len(addresses) > BATCH_SIZE:
        addresses = addresses[:BATCH_SIZE]
    
    # 构建URL
    addresses_str = ','.join(addresses)
    url = f"{BASE_URL}/networks/{network}/pools/multi/{addresses_str}?include=base_token,quote_token"
    
    headers = {
        "Accept": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36"
    }
    
    tokens_info = []
    
    for retry in range(MAX_RETRIES):
        try:
            logger.info(f"发送请求: {url[:100]}...")
            response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
            response.raise_for_status()  # 如果状态码不是200，引发异常
            
            data = response.json()
            
            # 处理响应数据
            if "data" in data and data["data"]:
                for pool in data["data"]:
                    try:
                        # 从pool id提取地址
                        pool_id = pool.get("id", "")
                        logger.info(f"处理池ID: {pool_id}")
                        
                        # 不同的网络可能有不同的ID格式
                        # 示例: 'polygon_pos_0x4d9865903e0590d27130b6ccc50b17b4c7221a71'
                        # 或者: 'eth_uniswap_0x1234...'
                        pool_parts = pool_id.split('_')
                        
                        # 确保ID至少有2部分，且最后一部分是地址
                        if len(pool_parts) >= 2 and pool_parts[-1].startswith('0x'):
                            pool_address = pool_parts[-1]
                        else:
                            # 如果ID格式不对，尝试从attributes中获取地址
                            pool_address = pool.get("attributes", {}).get("address", None)
                            if not pool_address:
                                logger.warning(f"无法从池ID '{pool_id}'提取地址，跳过")
                                continue
                        
                        # 查找此地址在原始地址列表中的格式
                        original_address = None
                        for addr in addresses:
                            if addr.lower() == pool_address.lower():
                                original_address = addr
                                break
                        
                        # 如果找不到，使用API返回的地址
                        if not original_address:
                            original_address = pool_address
                        
                        pool_info = {
                            "pool_address": pool_address,
                            "original_address": original_address,  # 保存原始格式的地址
                            "network": network,
                            "dex": pool["relationships"].get("dex", {}).get("data", {}).get("id", ""),
                            "base_token": {},
                            "quote_token": {}
                        }
                        
                        # 提取base_token信息
                        if "base_token" in pool["relationships"]:
                            base_token_id = pool["relationships"]["base_token"]["data"]["id"]
                            base_token_found = False
                            
                            # 从included中查找token信息
                            for included in data.get("included", []):
                                if included["id"] == base_token_id:
                                    base_token_address = included["attributes"].get("address", "")
                                    # 初始化代币信息
                                    pool_info["base_token"] = {
                                        "address": base_token_address,
                                        "name": included["attributes"].get("name", "Unknown"),
                                        "symbol": included["attributes"].get("symbol", "UNK"),
                                        "decimals": None  # 先设置为None，后面从区块链获取
                                    }
                                    base_token_found = True
                                    
                                    # 从区块链获取小数位数
                                    if base_token_address and base_token_address.startswith('0x'):
                                        decimals = get_token_decimals_web3(base_token_address, network, config)
                                        pool_info["base_token"]["decimals"] = decimals
                                        
                                        if decimals is None:
                                            logger.warning(f"无法获取base_token {base_token_address} 的小数位数，设置为null")
                                    
                                    break
                            
                            # 如果在included中找不到token信息，尝试从ID中提取地址
                            if not base_token_found:
                                # 从token_id中提取地址，格式可能是 'network_tokenaddress' 或 'network_dex_tokenaddress'
                                token_parts = base_token_id.split('_')
                                if len(token_parts) >= 2 and token_parts[-1].startswith('0x'):
                                    token_address = token_parts[-1]
                                    # 从区块链获取代币信息
                                    token_info = get_token_details_from_blockchain(token_address, network, config)
                                    pool_info["base_token"] = token_info
                                    logger.info(f"从区块链获取base_token信息: {token_info}")
                                else:
                                    pool_info["base_token"] = {
                                        "address": "",
                                        "name": "Unknown",
                                        "symbol": "UNK",
                                        "decimals": None
                                    }
                                    logger.warning(f"无法从ID '{base_token_id}'中提取base_token地址")
                        
                        # 提取quote_token信息
                        if "quote_token" in pool["relationships"]:
                            quote_token_id = pool["relationships"]["quote_token"]["data"]["id"]
                            quote_token_found = False
                            
                            # 从included中查找token信息
                            for included in data.get("included", []):
                                if included["id"] == quote_token_id:
                                    quote_token_address = included["attributes"].get("address", "")
                                    # 初始化代币信息
                                    pool_info["quote_token"] = {
                                        "address": quote_token_address,
                                        "name": included["attributes"].get("name", "Unknown"),
                                        "symbol": included["attributes"].get("symbol", "UNK"),
                                        "decimals": None  # 先设置为None，后面从区块链获取
                                    }
                                    quote_token_found = True
                                    
                                    # 从区块链获取小数位数
                                    if quote_token_address and quote_token_address.startswith('0x'):
                                        decimals = get_token_decimals_web3(quote_token_address, network, config)
                                        pool_info["quote_token"]["decimals"] = decimals
                                        
                                        if decimals is None:
                                            logger.warning(f"无法获取quote_token {quote_token_address} 的小数位数，设置为null")
                                    
                                    break
                            
                            # 如果在included中找不到token信息，尝试从ID中提取地址
                            if not quote_token_found:
                                # 从token_id中提取地址，格式可能是 'network_tokenaddress' 或 'network_dex_tokenaddress'
                                token_parts = quote_token_id.split('_')
                                if len(token_parts) >= 2 and token_parts[-1].startswith('0x'):
                                    token_address = token_parts[-1]
                                    # 从区块链获取代币信息
                                    token_info = get_token_details_from_blockchain(token_address, network, config)
                                    pool_info["quote_token"] = token_info
                                    logger.info(f"从区块链获取quote_token信息: {token_info}")
                                else:
                                    pool_info["quote_token"] = {
                                        "address": "",
                                        "name": "Unknown",
                                        "symbol": "UNK",
                                        "decimals": None
                                    }
                                    logger.warning(f"无法从ID '{quote_token_id}'中提取quote_token地址")
                        
                        # 如果成功提取了pool_address和至少一个token信息，将这个池添加到结果中
                        if "pool_address" in pool_info and (pool_info["base_token"] or pool_info["quote_token"]):
                            tokens_info.append(pool_info)
                        else:
                            logger.warning(f"跳过池 {pool.get('id', '未知')}，因为缺少必要信息")
                    except Exception as e:
                        logger.warning(f"处理池 {pool.get('id', '未知')} 数据时出错: {e}")
            
            # 记录找到的池地址
            pool_addresses = [info["pool_address"] for info in tokens_info]
            logger.info(f"API返回 {len(tokens_info)} 个池: {', '.join(pool_addresses[:min(5, len(pool_addresses))])}" + 
                       (f"...等" if len(pool_addresses) > 5 else ""))
            
            return tokens_info
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求 {network} 网络的池信息时出错: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
                time.sleep(1)
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法获取 {network} 网络的池信息")
                return tokens_info
        except json.JSONDecodeError as e:
            logger.error(f"解析 {network} 网络的池信息响应JSON时出错: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
                time.sleep(1)
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法解析 {network} 网络的池信息响应")
                return tokens_info
        except Exception as e:
            logger.error(f"处理 {network} 网络的池信息时发生未知错误: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
                time.sleep(1)
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法处理 {network} 网络的池信息")
                return tokens_info
    
    return tokens_info

def get_processed_pool_addresses(network):
    """获取已处理过的池地址集合（从token数据中提取）"""
    processed_pool_addresses = set()
    
    # 从token数据中提取pool_addresses
    tokens = load_existing_network_tokens(network)
    for token_address, token_data in tokens.items():
        if "pool_addresses" in token_data:
            for pool_address in token_data["pool_addresses"]:
                processed_pool_addresses.add(pool_address.lower())
    
    logger.info(f"从 {network} 网络的token数据中找到 {len(processed_pool_addresses)} 个已处理的池地址")
    return processed_pool_addresses

def process_network_addresses(network, addresses, proxies=None, existing_data=None):
    """处理一个网络的所有地址"""
    if not addresses:
        logger.warning(f"{network} 网络没有地址需要处理")
        return []
    
    # 获取已处理过的池地址
    processed_pool_addresses = load_processed_pool_addresses(network)
    
    # 过滤掉已处理的地址（将输入地址转换为小写进行比较）
    addresses_to_process = []
    for addr in addresses:
        addr_lower = addr.lower()
        if addr_lower not in processed_pool_addresses:
            addresses_to_process.append(addr)
    
    # 地址去重（保持原始格式）
    unique_addresses = []
    unique_lower = set()
    for addr in addresses_to_process:
        addr_lower = addr.lower()
        if addr_lower not in unique_lower:
            unique_addresses.append(addr)
            unique_lower.add(addr_lower)
    
    addresses_to_process = unique_addresses
    
    if not addresses_to_process:
        logger.info(f"{network} 网络的所有地址都已处理")
        return existing_data.get(network, [])
    
    logger.info(f"开始处理 {network} 网络的 {len(addresses_to_process)} 个地址（跳过 {len(addresses) - len(addresses_to_process)} 个已处理地址）")
    
    # 获取已存在的池信息
    all_pool_info = existing_data.get(network, []) if existing_data else []
    
    total_new_pools = 0
    
    # 分批处理地址
    for i in range(0, len(addresses_to_process), BATCH_SIZE):
        batch = addresses_to_process[i:i+BATCH_SIZE]
        logger.info(f"处理 {network} 网络的第 {i//BATCH_SIZE + 1} 批（{len(batch)} 个地址）")
        
        pool_info = get_pool_info_batch(network, batch, proxies)
        if pool_info:
            logger.info(f"成功获取 {len(pool_info)} 个池信息")
            
            # 每批处理后立即更新和保存数据（使用追加模式）
            if save_network_data(network, pool_info):
                total_new_pools += len(pool_info)
            else:
                logger.warning(f"保存 {network} 网络第 {i//BATCH_SIZE + 1} 批数据失败")
        else:
            logger.warning(f"未能获取该批次的池信息")
    
    # 返回最新数据
    latest_data = load_existing_data().get(network, [])
    logger.info(f"{network} 网络处理完成，共添加 {total_new_pools} 个新池信息，总计 {len(latest_data)} 个池")
    return latest_data

def save_processed_pool_addresses(network, pool_addresses):
    """保存已处理的池地址到专门的文件"""
    try:
        file_path = TOKENS_DIR / f"{network}_processed_pools.json"
        
        # 读取现有数据
        existing_addresses = set()
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_addresses = set(json.load(f))
            except Exception as e:
                logger.warning(f"读取已处理池地址文件时出错: {e}")
        
        # 合并新地址
        new_addresses = set(addr.lower() for addr in pool_addresses)
        all_addresses = existing_addresses.union(new_addresses)
        
        # 保存到文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(list(all_addresses), f, indent=2, ensure_ascii=False)
        
        new_count = len(all_addresses) - len(existing_addresses)
        logger.info(f"已添加 {new_count} 个新池地址到 {file_path}，总计 {len(all_addresses)} 个已处理池地址")
        return True
    except Exception as e:
        logger.error(f"保存已处理池地址时出错: {e}")
        return False

def load_processed_pool_addresses(network):
    """加载已处理的池地址集合"""
    file_path = TOKENS_DIR / f"{network}_processed_pools.json"
    
    if not file_path.exists():
        logger.info(f"网络 {network} 的已处理池地址文件不存在，将创建新文件")
        return set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            addresses = set(json.load(f))
            logger.info(f"成功从 {file_path} 加载 {len(addresses)} 个已处理池地址")
            return addresses
    except Exception as e:
        logger.error(f"读取已处理池地址时出错: {e}")
        return set()

def main():
    """主函数"""
    start_time = datetime.now()
    logger.info(f"脚本开始执行时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确保输出目录存在
    if not ensure_output_directory():
        logger.error("无法创建输出目录，脚本退出")
        return
    
    # 加载配置
    config = load_config()
    proxies = get_proxy_from_config(config)
    
    if proxies:
        logger.info(f"将使用代理: {proxies}")
    else:
        logger.warning("未配置代理或代理未启用")
    
    # 解析池地址
    addresses_by_network = parse_pool_addresses()
    if not addresses_by_network:
        logger.error("未找到任何池地址，脚本退出")
        return
    
    # 加载已存在的数据
    existing_data = load_existing_data()
    
    try:
        for network, addresses in addresses_by_network.items():
            if addresses:
                logger.info(f"\n{'='*50}")
                logger.info(f"开始处理 {network} 网络")
                logger.info(f"{'='*50}")
                
                try:
                    pool_info = process_network_addresses(network, addresses, proxies, existing_data)
                    
                    if pool_info:
                        logger.info(f"完成 {network} 网络处理，共 {len(pool_info)} 个池信息")
                except Exception as e:
                    logger.error(f"处理 {network} 网络时出错: {e}")
        
        # 最终统计信息
        updated_data = load_existing_data()
        total_pools = sum(len(pools) for pools in updated_data.values())
        logger.info(f"\n总计处理了 {len(updated_data)} 个网络的 {total_pools} 个池")
        
        # 统计每个网络的token数量和已处理的池地址数量
        for network in updated_data.keys():
            tokens = load_existing_network_tokens(network)
            total_tokens = len(tokens)
            processed_pools = load_processed_pool_addresses(network)
            
            logger.info(f"{network} 网络: {len(updated_data.get(network, []))} 个池, {total_tokens} 个唯一代币, " + 
                       f"{len(processed_pools)} 个已处理的池地址")
    
    except KeyboardInterrupt:
        logger.warning("用户中断，停止处理")
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    logger.info(f"脚本结束执行时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总用时: {duration:.2f} 秒")

if __name__ == "__main__":
    main() 