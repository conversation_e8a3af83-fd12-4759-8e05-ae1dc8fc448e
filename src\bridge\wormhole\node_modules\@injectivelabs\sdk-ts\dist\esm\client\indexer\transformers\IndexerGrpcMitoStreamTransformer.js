import { IndexerGrpcMitoTransformer } from './IndexerGrpcMitoTransformer.js';
/**
 * @category Indexer Stream Transformer
 */
export class IndexerGrpcMitoStreamTransformer {
    static transfersStreamCallback = (response) => ({
        transfer: response.data
            ? IndexerGrpcMitoTransformer.mitoTransferHistoryToTransferHistory(response.data)
            : undefined,
        opType: response.opType,
    });
    static vaultStreamCallback = (response) => ({
        vault: response.data
            ? IndexerGrpcMitoTransformer.mitoVaultToVault(response.data)
            : undefined,
        opType: response.opType,
    });
    static vaultHolderSubscriptionStreamCallback = (response) => ({
        subscription: response.data
            ? IndexerGrpcMitoTransformer.mitoSubscriptionToSubscription(response.data)
            : undefined,
        opType: response.opType,
    });
    static stakingRewardByAccountStreamCallback = (response) => ({
        stakingReward: response.data
            ? IndexerGrpcMitoTransformer.mitoStakingRewardToStakingReward(response.data)
            : undefined,
        opType: response.opType,
    });
    static historicalStakingStreamCallback = (response) => ({
        historicalStaking: response.data
            ? IndexerGrpcMitoTransformer.mitoStakingActivityToStakingActivity(response.data)
            : undefined,
        opType: response.opType,
    });
}
