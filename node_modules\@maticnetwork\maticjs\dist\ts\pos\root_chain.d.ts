import { BaseToken, Web3SideChainClient } from "../utils";
import { TYPE_AMOUNT } from "../types";
import { IPOSClientConfig } from "../interfaces";
import { BaseBigNumber } from "..";
export declare class <PERSON><PERSON>hain extends BaseToken<IPOSClientConfig> {
    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string);
    method(methodName: string, ...args: any[]): Promise<import("..").BaseContractMethod>;
    getLastChildBlock(): Promise<string>;
    findRootBlockFromChild(childBlockNumber: TYPE_AMOUNT): Promise<BaseBigNumber>;
}
