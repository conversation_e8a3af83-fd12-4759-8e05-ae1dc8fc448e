{"version": 3, "sources": ["../../src/internal/general.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file contains the underlying implementations for exposed API surface in\n * the {@link api/general}. By moving the methods out into a separate file,\n * other namespaces and processes can access these methods without depending on the entire\n * general namespace and without having a dependency cycle error.\n * @group Implementation\n */\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { getAptosFullNode, postAptosIndexer } from \"../client\";\nimport { GetChainTopUserTransactionsResponse, GetProcessorStatusResponse, GraphqlQuery, LedgerInfo } from \"../types\";\nimport { GetChainTopUserTransactionsQuery, GetProcessorStatusQuery } from \"../types/generated/operations\";\nimport { GetChainTopUserTransactions, GetProcessorStatus } from \"../types/generated/queries\";\nimport { ProcessorType } from \"../utils/const\";\n\n/**\n * Retrieves information about the current ledger.\n *\n * @param args - The arguments for retrieving ledger information.\n * @param args.aptosConfig - The configuration object for connecting to the Aptos network.\n * @group Implementation\n */\nexport async function getLedgerInfo(args: { aptosConfig: AptosConfig }): Promise<LedgerInfo> {\n  const { aptosConfig } = args;\n  const { data } = await getAptosFullNode<{}, LedgerInfo>({\n    aptosConfig,\n    originMethod: \"getLedgerInfo\",\n    path: \"\",\n  });\n  return data;\n}\n\n/**\n * Retrieves the top user transactions for a specific blockchain chain.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @param args.limit - The maximum number of transactions to retrieve.\n * @returns An array of user transactions.\n * @group Implementation\n */\nexport async function getChainTopUserTransactions(args: {\n  aptosConfig: AptosConfig;\n  limit: number;\n}): Promise<GetChainTopUserTransactionsResponse> {\n  const { aptosConfig, limit } = args;\n  const graphqlQuery = {\n    query: GetChainTopUserTransactions,\n    variables: { limit },\n  };\n\n  const data = await queryIndexer<GetChainTopUserTransactionsQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getChainTopUserTransactions\",\n  });\n\n  return data.user_transactions;\n}\n\n/**\n * Executes a GraphQL query against the Aptos indexer and retrieves the resulting data.\n *\n * @param args - The arguments for the query.\n * @param args.aptosConfig - The configuration settings for the Aptos client.\n * @param args.query - The GraphQL query to be executed.\n * @param args.originMethod - An optional string to specify the origin method for tracking purposes.\n * @returns The data returned from the query execution.\n * @group Implementation\n */\nexport async function queryIndexer<T extends {}>(args: {\n  aptosConfig: AptosConfig;\n  query: GraphqlQuery;\n  originMethod?: string;\n}): Promise<T> {\n  const { aptosConfig, query, originMethod } = args;\n  const { data } = await postAptosIndexer<GraphqlQuery, T>({\n    aptosConfig,\n    originMethod: originMethod ?? \"queryIndexer\",\n    path: \"\",\n    body: query,\n    overrides: { WITH_CREDENTIALS: false },\n  });\n  return data;\n}\n\n/**\n * Retrieves the current statuses of processors.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @returns The statuses of the processors.\n * @group Implementation\n */\nexport async function getProcessorStatuses(args: { aptosConfig: AptosConfig }): Promise<GetProcessorStatusResponse> {\n  const { aptosConfig } = args;\n\n  const graphqlQuery = {\n    query: GetProcessorStatus,\n  };\n\n  const data = await queryIndexer<GetProcessorStatusQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getProcessorStatuses\",\n  });\n\n  return data.processor_status;\n}\n\n/**\n * Retrieves the last success version from the indexer.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @returns The last success version as a BigInt.\n * @group Implementation\n */\nexport async function getIndexerLastSuccessVersion(args: { aptosConfig: AptosConfig }): Promise<bigint> {\n  const response = await getProcessorStatuses({ aptosConfig: args.aptosConfig });\n  return BigInt(response[0].last_success_version);\n}\n\n/**\n * Retrieves the status of a specified processor in the Aptos network.\n * This function allows you to check the current operational status of a processor, which can be useful for monitoring and troubleshooting.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration object for connecting to the Aptos network.\n * @param args.processorType - The type of processor whose status you want to retrieve.\n * @returns The status of the specified processor.\n * @group Implementation\n */\nexport async function getProcessorStatus(args: {\n  aptosConfig: AptosConfig;\n  processorType: ProcessorType;\n}): Promise<GetProcessorStatusResponse[0]> {\n  const { aptosConfig, processorType } = args;\n\n  const whereCondition: { processor: { _eq: string } } = {\n    processor: { _eq: processorType },\n  };\n\n  const graphqlQuery = {\n    query: GetProcessorStatus,\n    variables: {\n      where_condition: whereCondition,\n    },\n  };\n\n  const data = await queryIndexer<GetProcessorStatusQuery>({\n    aptosConfig,\n    query: graphqlQuery,\n    originMethod: \"getProcessorStatus\",\n  });\n\n  return data.processor_status[0];\n}\n"], "mappings": "kIAyBA,eAAsBA,EAAcC,EAAyD,CAC3F,GAAM,CAAE,YAAAC,CAAY,EAAID,EAClB,CAAE,KAAAE,CAAK,EAAI,MAAMC,EAAiC,CACtD,YAAAF,EACA,aAAc,gBACd,KAAM,EACR,CAAC,EACD,OAAOC,CACT,CAWA,eAAsBE,EAA4BJ,EAGD,CAC/C,GAAM,CAAE,YAAAC,EAAa,MAAAI,CAAM,EAAIL,EAY/B,OANa,MAAMM,EAA+C,CAChE,YAAAL,EACA,MAPmB,CACnB,MAAOM,EACP,UAAW,CAAE,MAAAF,CAAM,CACrB,EAKE,aAAc,6BAChB,CAAC,GAEW,iBACd,CAYA,eAAsBC,EAA2BN,EAIlC,CACb,GAAM,CAAE,YAAAC,EAAa,MAAAO,EAAO,aAAAC,CAAa,EAAIT,EACvC,CAAE,KAAAE,CAAK,EAAI,MAAMQ,EAAkC,CACvD,YAAAT,EACA,aAAcQ,GAAgB,eAC9B,KAAM,GACN,KAAMD,EACN,UAAW,CAAE,iBAAkB,EAAM,CACvC,CAAC,EACD,OAAON,CACT,CAUA,eAAsBS,EAAqBX,EAAyE,CAClH,GAAM,CAAE,YAAAC,CAAY,EAAID,EAYxB,OANa,MAAMM,EAAsC,CACvD,YAAAL,EACA,MANmB,CACnB,MAAOW,CACT,EAKE,aAAc,sBAChB,CAAC,GAEW,gBACd,CAUA,eAAsBC,EAA6Bb,EAAqD,CACtG,IAAMc,EAAW,MAAMH,EAAqB,CAAE,YAAaX,EAAK,WAAY,CAAC,EAC7E,OAAO,OAAOc,EAAS,CAAC,EAAE,oBAAoB,CAChD,CAYA,eAAsBC,EAAmBf,EAGE,CACzC,GAAM,CAAE,YAAAC,EAAa,cAAAe,CAAc,EAAIhB,EAmBvC,OANa,MAAMM,EAAsC,CACvD,YAAAL,EACA,MATmB,CACnB,MAAOW,EACP,UAAW,CACT,gBAPmD,CACrD,UAAW,CAAE,IAAKI,CAAc,CAClC,CAME,CACF,EAKE,aAAc,oBAChB,CAAC,GAEW,iBAAiB,CAAC,CAChC", "names": ["getLedgerInfo", "args", "aptosConfig", "data", "getAptosFullNode", "getChainTopUserTransactions", "limit", "queryIndexer", "GetChainTopUserTransactions", "query", "originMethod", "postAptosIndexer", "getProcessorStatuses", "GetProcessorStatus", "getIndexerLastSuccessVersion", "response", "getProcessorStatus", "processorType"]}