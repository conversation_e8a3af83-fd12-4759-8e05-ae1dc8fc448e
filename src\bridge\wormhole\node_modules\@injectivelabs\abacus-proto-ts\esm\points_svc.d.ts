import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "points_svc";
export interface PointsLatestForAccountRequest {
    /** Account address to get stats for. */
    accountAddress: string;
}
export interface PointsLatestForAccountResponse {
    /** Global rank of an account address based on total points. */
    rank: string;
    /** Total amount of points allocated for an account address. */
    totalPoints: string;
    /** Total amount of points allocated for an account address. With float part. */
    totalPointsPrecise: number;
    /** League assigned based on the rank among total participants. */
    league: string;
    /** Latest computation timestamp of the rank and total points. */
    updatedAt: string;
}
export interface PointsStatsDailyForAccountRequest {
    /** Account address to get stats for. */
    accountAddress: string;
    /** Limit amount of days of historical look back. */
    daysLimit?: string | undefined;
}
export interface HistoricalPointsStatsRowCollection {
    rows: HistoricalPointsStatsRow[];
}
export interface HistoricalPointsStatsRow {
    /** Week number in ISO format. */
    week: string;
    /** Day in ISO format. */
    day?: string | undefined;
    /** Amount of points allocated for an account address during time window. */
    points: string;
    /**
     * Amount of points allocated for an account address during time window. With
     * float part.
     */
    pointsPrecise: number;
    /** Notional volume traded account address during time window. */
    volume: number;
}
export interface PointsStatsWeeklyForAccountRequest {
    /** Account address to get stats for. */
    accountAddress: string;
    /** Limit amount of days of historical look back. */
    weeksLimit?: string | undefined;
}
export interface PointsLeaderboardRequest {
}
export interface LeaderboardPointsRowCollection {
    rows: LeaderboardPointsRow[];
}
export interface LeaderboardPointsRow {
    /** Rank of an account address. */
    rank: string;
    /** Total count of ranked participants. */
    totalRank: string;
    /** Account address. */
    accountAddress: string;
    /** Points allocated for an account address. */
    points: number;
    /** League assigned based on the rank among total participants. */
    league: string;
}
export interface PointsSimulateAllocationRequest {
    /** Account address */
    account: string;
    /** The ID of the market that this trade is in */
    marketId: string;
    /** The direction the trade */
    tradeDirection: string;
    /** Trade's execution side, maker/taker */
    executionSide: string;
    /** USD value of the trade at the time of execution */
    usdValue: number;
    /** A list of flag assigned to the trade */
    flags: string[];
    /** Type of market */
    marketType: string;
    /** Target UNIX timestamp in seconds of trade execution. */
    timestamp: string;
    /**
     * Prior daily volume (in USD) of the account address before processing the
     * trade.
     */
    priorDailyVolume?: number | undefined;
}
export interface PointsSimulateAllocationResponse {
    /** Points that would be emitted for this trade */
    pointsEmitted: number;
    /** Breakdown of how points were calculated */
    pointsBreakdown: string;
    /** Trading volume prior to this trade in the same day */
    priorDailyVolume: number;
    /** Volume tiers that apply */
    effectiveVolumeTiers: ModelEffectiveVolumeTier[];
    /** Multipliers that would apply */
    effectiveMultipliers: ModelPointsMultiplier[];
}
export interface ModelEffectiveVolumeTier {
    /** Tier config number. Starts from 1. */
    tierNumber: number;
    /** Maximum USD notional volume for the tier. */
    upperThreshold: number;
    /** Points per unit volume for taker trades. */
    takerRate: number;
    /** Points per unit volume for maker trades. */
    makerRate: number;
    /** Volume allocated to this tier. */
    effectiveVolume: number;
    /** Points rate applied for this tier */
    effectiveRate: number;
}
export interface ModelPointsMultiplier {
    /** Unique identifier */
    id: string;
    /** Human readable label */
    label: string;
    /** Points multiplier value */
    multiplier: number;
    /** List of affected user addresses */
    affectedUsers: string[];
    /** List of affected market IDs */
    affectedMarkets: string[];
    /**
     * List of not-affected market IDs, all other markets will be affected. Cannot
     * be used together with affected_markets.
     */
    allMarketsExcept: string[];
    /** Start date when multiplier becomes effective */
    effectiveDateStart?: string | undefined;
    /** End date when multiplier expires */
    effectiveDateEnd?: string | undefined;
    /** List of trade flags this multiplier applies to */
    effectiveFlags: string[];
}
export interface PointsGetLeagueConfigRequest {
}
export interface PointsGetLeagueConfigResponse {
    /** Percentile threshold for white league */
    whiteThreshold: number;
    /** Percentile threshold for orange league */
    orangeThreshold: number;
    /** Percentile threshold for blue league */
    blueThreshold: number;
    /** Percentile threshold for purple league */
    purpleThreshold: number;
    /** Percentile threshold for black league */
    blackThreshold: number;
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Timestamp of the last update */
    updatedAt: string;
}
export interface PointsSetLeagueConfigRequest {
    /** Percentile threshold for white league */
    whiteThreshold: number;
    /** Percentile threshold for orange league */
    orangeThreshold: number;
    /** Percentile threshold for blue league */
    blueThreshold: number;
    /** Percentile threshold for purple league */
    purpleThreshold: number;
    /** Percentile threshold for black league */
    blackThreshold: number;
}
export interface PointsSetLeagueConfigResponse {
    /** Percentile threshold for white league */
    whiteThreshold: number;
    /** Percentile threshold for orange league */
    orangeThreshold: number;
    /** Percentile threshold for blue league */
    blueThreshold: number;
    /** Percentile threshold for purple league */
    purpleThreshold: number;
    /** Percentile threshold for black league */
    blackThreshold: number;
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Timestamp of the last update */
    updatedAt: string;
}
export interface PointsGetEmissionConfigRequest {
}
export interface PointsGetEmissionConfigResponse {
    /** Base unit volume for points calculation */
    unitVolume: number;
    /** List of volume tiers configuration */
    volumeTiers: ModelVolumeTierConfig[];
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Timestamp of the last update */
    updatedAt: string;
}
export interface ModelVolumeTierConfig {
    /** Maximum USD notional volume for the tier. */
    upperThreshold: number;
    /** Points per unit volume for taker trades. */
    takerRate: number;
    /** Points per unit volume for maker trades. */
    makerRate: number;
}
export interface PointsSetEmissionConfigRequest {
    /** Base unit volume for points calculation */
    unitVolume: number;
    /** List of volume tiers configuration */
    volumeTiers: ModelVolumeTierConfig[];
}
export interface PointsSetEmissionConfigResponse {
    /** Base unit volume for points calculation */
    unitVolume: number;
    /** List of volume tiers configuration */
    volumeTiers: ModelVolumeTierConfig[];
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Timestamp of the last update */
    updatedAt: string;
}
export interface PointsListBanConfigsRequest {
}
export interface BanConfigCollection {
    bans: BanConfig[];
}
export interface BanConfig {
    /** Unique identifier */
    id: string;
    /** Human readable label for the ban */
    label: string;
    /** Account address that is banned */
    accountAddress: string;
    /** Eth address that is banned */
    ethAddress: string;
    /** ID of the admin who created the ban */
    createdBy: string;
    /** Timestamp when the ban was created */
    createdAt: string;
}
export interface PointsCreateBanConfigRequest {
    /** Account addresses to create ban config for. */
    accountAddresses: string[];
    /** Eth addresses to create ban config for (optional). */
    ethAddresses: string[];
    /** Provides a reason for the ban configuration. */
    label: string;
    /** UNIX timestamp in seconds when the ban becomes effective. */
    effectiveFrom: string;
}
export interface PointsDeleteBanConfigRequest {
    /** Unique identifier */
    id: string;
}
export interface PointsDeleteBanConfigResponse {
}
export interface PointsListMultiplierConfigsRequest {
}
export interface PointsMultiplierConfigCollection {
    multipliers: PointsMultiplierConfig[];
}
export interface PointsMultiplierConfig {
    /** Unique identifier */
    id: string;
    /** Human readable label */
    label: string;
    /** Points multiplier value */
    multiplier: number;
    /** List of affected user addresses */
    affectedUsers: string[];
    /** List of affected market IDs */
    affectedMarkets: string[];
    /**
     * List of not-affected market IDs, all other markets will be affected. Cannot
     * be used together with affected_markets.
     */
    allMarketsExcept: string[];
    /** Start date when multiplier becomes effective (UNIX seconds) */
    effectiveDateStart?: string | undefined;
    /** End date when multiplier expires (UNIX seconds) */
    effectiveDateEnd?: string | undefined;
    /** List of trade flags this multiplier applies to */
    effectiveFlags: string[];
    /** Whether to force recalculation of points */
    enforceRecalculate: boolean;
    /** ID of the admin who created the config */
    createdBy: string;
    /** Creation timestamp */
    createdAt: string;
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Last update timestamp */
    updatedAt: string;
}
export interface PointsCreateMultiplierConfigRequest {
    /** List of market IDs to apply multiplier to. Empty list means all markets. */
    marketIds: string[];
    /**
     * Multiplier will apply to all markets except the ones in this list. Cannot be
     * used together with market_ids.
     */
    allMarketsExcept: string[];
    /** List of account addresses to apply multiplier to. */
    accountAddresses: string[];
    /** List of feature flags to apply multiplier to. Empty list means all flags. */
    flags: string[];
    /**
     * Points multiplier value. 1.0 means normal points, 2.0 means double points,
     * etc.
     */
    multiplier: number;
    /** Description of the multiplier configuration. */
    label: string;
    /** UNIX timestamp in seconds when the multiplier becomes effective. */
    effectiveFrom?: string | undefined;
    /** UNIX timestamp in seconds when the multiplier expires. 0 means no expiration. */
    effectiveUntil?: string | undefined;
    /**
     * If true, recalculate points for affected users and markets after the
     * multiplier is set.
     */
    enforceRecalculate: boolean;
}
export interface PointsCreateMultiplierConfigResponse {
    /** Unique identifier */
    id: string;
    /** Human readable label */
    label: string;
    /** Points multiplier value */
    multiplier: number;
    /** List of affected user addresses */
    affectedUsers: string[];
    /** List of affected market IDs */
    affectedMarkets: string[];
    /**
     * List of not-affected market IDs, all other markets will be affected. Cannot
     * be used together with affected_markets.
     */
    allMarketsExcept: string[];
    /** Start date when multiplier becomes effective (UNIX seconds) */
    effectiveDateStart?: string | undefined;
    /** End date when multiplier expires (UNIX seconds) */
    effectiveDateEnd?: string | undefined;
    /** List of trade flags this multiplier applies to */
    effectiveFlags: string[];
    /** Whether to force recalculation of points */
    enforceRecalculate: boolean;
    /** ID of the admin who created the config */
    createdBy: string;
    /** Creation timestamp */
    createdAt: string;
    /** ID of the admin who last updated the config */
    updatedBy: string;
    /** Last update timestamp */
    updatedAt: string;
}
export interface PointsDeleteMultiplierConfigRequest {
    /** Unique identifier */
    id: string;
}
export interface PointsDeleteMultiplierConfigResponse {
}
export interface PointsListAdminCorrectionsRequest {
    /** Filter corrections by type (absolute or relative) */
    correctionType?: string | undefined;
    /** Filter corrections by account address */
    accountAddress?: string | undefined;
}
export interface AdminPointsCorrectionCollection {
    corrections: AdminPointsCorrection[];
}
export interface AdminPointsCorrection {
    /** Unique identifier */
    id: string;
    /** Human readable label for the correction */
    label: string;
    /** Account address for which points are being corrected */
    accountAddress: string;
    /** Type of correction (absolute or relative) */
    correctionType: string;
    /** Correction value (can be negative if relative) */
    value: number;
    /** Timestamp from which the correction is effective */
    effectiveFrom: string;
    /** ID of the admin who created the correction */
    createdBy: string;
    /** Creation timestamp */
    createdAt: string;
}
export interface PointsCreateAdminCorrectionRequest {
    /** Account address to create correction for. */
    accountAddress: string;
    /** Type of correction (absolute or relative) */
    correctionType: string;
    /** Points value to set (absolute) or add/subtract (relative) */
    value: number;
    /** Provides a reason for the correction. */
    label: string;
    /** UNIX timestamp in seconds when the correction becomes effective. */
    effectiveFrom: string;
}
export interface PointsCreateAdminCorrectionResponse {
    /** Unique identifier */
    id: string;
    /** Human readable label for the correction */
    label: string;
    /** Account address for which points are being corrected */
    accountAddress: string;
    /** Type of correction (absolute or relative) */
    correctionType: string;
    /** Correction value (can be negative if relative) */
    value: number;
    /** Timestamp from which the correction is effective */
    effectiveFrom: string;
    /** ID of the admin who created the correction */
    createdBy: string;
    /** Creation timestamp */
    createdAt: string;
}
export interface PointsDeleteAdminCorrectionRequest {
    /** Unique identifier */
    id: string;
}
export interface PointsDeleteAdminCorrectionResponse {
}
export interface PointsReprocessTradesAfterDateRequest {
    /** UNIX timestamp in seconds after which trades will be reprocessed. */
    afterDate: string;
}
export interface PointsReprocessTradesAfterDateResponse {
    /** Number of trades scheduled to be reprocessed. */
    affectedCount: string;
}
export declare const PointsLatestForAccountRequest: {
    encode(message: PointsLatestForAccountRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsLatestForAccountRequest;
    fromJSON(object: any): PointsLatestForAccountRequest;
    toJSON(message: PointsLatestForAccountRequest): unknown;
    create(base?: DeepPartial<PointsLatestForAccountRequest>): PointsLatestForAccountRequest;
    fromPartial(object: DeepPartial<PointsLatestForAccountRequest>): PointsLatestForAccountRequest;
};
export declare const PointsLatestForAccountResponse: {
    encode(message: PointsLatestForAccountResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsLatestForAccountResponse;
    fromJSON(object: any): PointsLatestForAccountResponse;
    toJSON(message: PointsLatestForAccountResponse): unknown;
    create(base?: DeepPartial<PointsLatestForAccountResponse>): PointsLatestForAccountResponse;
    fromPartial(object: DeepPartial<PointsLatestForAccountResponse>): PointsLatestForAccountResponse;
};
export declare const PointsStatsDailyForAccountRequest: {
    encode(message: PointsStatsDailyForAccountRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsStatsDailyForAccountRequest;
    fromJSON(object: any): PointsStatsDailyForAccountRequest;
    toJSON(message: PointsStatsDailyForAccountRequest): unknown;
    create(base?: DeepPartial<PointsStatsDailyForAccountRequest>): PointsStatsDailyForAccountRequest;
    fromPartial(object: DeepPartial<PointsStatsDailyForAccountRequest>): PointsStatsDailyForAccountRequest;
};
export declare const HistoricalPointsStatsRowCollection: {
    encode(message: HistoricalPointsStatsRowCollection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalPointsStatsRowCollection;
    fromJSON(object: any): HistoricalPointsStatsRowCollection;
    toJSON(message: HistoricalPointsStatsRowCollection): unknown;
    create(base?: DeepPartial<HistoricalPointsStatsRowCollection>): HistoricalPointsStatsRowCollection;
    fromPartial(object: DeepPartial<HistoricalPointsStatsRowCollection>): HistoricalPointsStatsRowCollection;
};
export declare const HistoricalPointsStatsRow: {
    encode(message: HistoricalPointsStatsRow, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): HistoricalPointsStatsRow;
    fromJSON(object: any): HistoricalPointsStatsRow;
    toJSON(message: HistoricalPointsStatsRow): unknown;
    create(base?: DeepPartial<HistoricalPointsStatsRow>): HistoricalPointsStatsRow;
    fromPartial(object: DeepPartial<HistoricalPointsStatsRow>): HistoricalPointsStatsRow;
};
export declare const PointsStatsWeeklyForAccountRequest: {
    encode(message: PointsStatsWeeklyForAccountRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsStatsWeeklyForAccountRequest;
    fromJSON(object: any): PointsStatsWeeklyForAccountRequest;
    toJSON(message: PointsStatsWeeklyForAccountRequest): unknown;
    create(base?: DeepPartial<PointsStatsWeeklyForAccountRequest>): PointsStatsWeeklyForAccountRequest;
    fromPartial(object: DeepPartial<PointsStatsWeeklyForAccountRequest>): PointsStatsWeeklyForAccountRequest;
};
export declare const PointsLeaderboardRequest: {
    encode(_: PointsLeaderboardRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsLeaderboardRequest;
    fromJSON(_: any): PointsLeaderboardRequest;
    toJSON(_: PointsLeaderboardRequest): unknown;
    create(base?: DeepPartial<PointsLeaderboardRequest>): PointsLeaderboardRequest;
    fromPartial(_: DeepPartial<PointsLeaderboardRequest>): PointsLeaderboardRequest;
};
export declare const LeaderboardPointsRowCollection: {
    encode(message: LeaderboardPointsRowCollection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LeaderboardPointsRowCollection;
    fromJSON(object: any): LeaderboardPointsRowCollection;
    toJSON(message: LeaderboardPointsRowCollection): unknown;
    create(base?: DeepPartial<LeaderboardPointsRowCollection>): LeaderboardPointsRowCollection;
    fromPartial(object: DeepPartial<LeaderboardPointsRowCollection>): LeaderboardPointsRowCollection;
};
export declare const LeaderboardPointsRow: {
    encode(message: LeaderboardPointsRow, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LeaderboardPointsRow;
    fromJSON(object: any): LeaderboardPointsRow;
    toJSON(message: LeaderboardPointsRow): unknown;
    create(base?: DeepPartial<LeaderboardPointsRow>): LeaderboardPointsRow;
    fromPartial(object: DeepPartial<LeaderboardPointsRow>): LeaderboardPointsRow;
};
export declare const PointsSimulateAllocationRequest: {
    encode(message: PointsSimulateAllocationRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSimulateAllocationRequest;
    fromJSON(object: any): PointsSimulateAllocationRequest;
    toJSON(message: PointsSimulateAllocationRequest): unknown;
    create(base?: DeepPartial<PointsSimulateAllocationRequest>): PointsSimulateAllocationRequest;
    fromPartial(object: DeepPartial<PointsSimulateAllocationRequest>): PointsSimulateAllocationRequest;
};
export declare const PointsSimulateAllocationResponse: {
    encode(message: PointsSimulateAllocationResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSimulateAllocationResponse;
    fromJSON(object: any): PointsSimulateAllocationResponse;
    toJSON(message: PointsSimulateAllocationResponse): unknown;
    create(base?: DeepPartial<PointsSimulateAllocationResponse>): PointsSimulateAllocationResponse;
    fromPartial(object: DeepPartial<PointsSimulateAllocationResponse>): PointsSimulateAllocationResponse;
};
export declare const ModelEffectiveVolumeTier: {
    encode(message: ModelEffectiveVolumeTier, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ModelEffectiveVolumeTier;
    fromJSON(object: any): ModelEffectiveVolumeTier;
    toJSON(message: ModelEffectiveVolumeTier): unknown;
    create(base?: DeepPartial<ModelEffectiveVolumeTier>): ModelEffectiveVolumeTier;
    fromPartial(object: DeepPartial<ModelEffectiveVolumeTier>): ModelEffectiveVolumeTier;
};
export declare const ModelPointsMultiplier: {
    encode(message: ModelPointsMultiplier, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ModelPointsMultiplier;
    fromJSON(object: any): ModelPointsMultiplier;
    toJSON(message: ModelPointsMultiplier): unknown;
    create(base?: DeepPartial<ModelPointsMultiplier>): ModelPointsMultiplier;
    fromPartial(object: DeepPartial<ModelPointsMultiplier>): ModelPointsMultiplier;
};
export declare const PointsGetLeagueConfigRequest: {
    encode(_: PointsGetLeagueConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsGetLeagueConfigRequest;
    fromJSON(_: any): PointsGetLeagueConfigRequest;
    toJSON(_: PointsGetLeagueConfigRequest): unknown;
    create(base?: DeepPartial<PointsGetLeagueConfigRequest>): PointsGetLeagueConfigRequest;
    fromPartial(_: DeepPartial<PointsGetLeagueConfigRequest>): PointsGetLeagueConfigRequest;
};
export declare const PointsGetLeagueConfigResponse: {
    encode(message: PointsGetLeagueConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsGetLeagueConfigResponse;
    fromJSON(object: any): PointsGetLeagueConfigResponse;
    toJSON(message: PointsGetLeagueConfigResponse): unknown;
    create(base?: DeepPartial<PointsGetLeagueConfigResponse>): PointsGetLeagueConfigResponse;
    fromPartial(object: DeepPartial<PointsGetLeagueConfigResponse>): PointsGetLeagueConfigResponse;
};
export declare const PointsSetLeagueConfigRequest: {
    encode(message: PointsSetLeagueConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSetLeagueConfigRequest;
    fromJSON(object: any): PointsSetLeagueConfigRequest;
    toJSON(message: PointsSetLeagueConfigRequest): unknown;
    create(base?: DeepPartial<PointsSetLeagueConfigRequest>): PointsSetLeagueConfigRequest;
    fromPartial(object: DeepPartial<PointsSetLeagueConfigRequest>): PointsSetLeagueConfigRequest;
};
export declare const PointsSetLeagueConfigResponse: {
    encode(message: PointsSetLeagueConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSetLeagueConfigResponse;
    fromJSON(object: any): PointsSetLeagueConfigResponse;
    toJSON(message: PointsSetLeagueConfigResponse): unknown;
    create(base?: DeepPartial<PointsSetLeagueConfigResponse>): PointsSetLeagueConfigResponse;
    fromPartial(object: DeepPartial<PointsSetLeagueConfigResponse>): PointsSetLeagueConfigResponse;
};
export declare const PointsGetEmissionConfigRequest: {
    encode(_: PointsGetEmissionConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsGetEmissionConfigRequest;
    fromJSON(_: any): PointsGetEmissionConfigRequest;
    toJSON(_: PointsGetEmissionConfigRequest): unknown;
    create(base?: DeepPartial<PointsGetEmissionConfigRequest>): PointsGetEmissionConfigRequest;
    fromPartial(_: DeepPartial<PointsGetEmissionConfigRequest>): PointsGetEmissionConfigRequest;
};
export declare const PointsGetEmissionConfigResponse: {
    encode(message: PointsGetEmissionConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsGetEmissionConfigResponse;
    fromJSON(object: any): PointsGetEmissionConfigResponse;
    toJSON(message: PointsGetEmissionConfigResponse): unknown;
    create(base?: DeepPartial<PointsGetEmissionConfigResponse>): PointsGetEmissionConfigResponse;
    fromPartial(object: DeepPartial<PointsGetEmissionConfigResponse>): PointsGetEmissionConfigResponse;
};
export declare const ModelVolumeTierConfig: {
    encode(message: ModelVolumeTierConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ModelVolumeTierConfig;
    fromJSON(object: any): ModelVolumeTierConfig;
    toJSON(message: ModelVolumeTierConfig): unknown;
    create(base?: DeepPartial<ModelVolumeTierConfig>): ModelVolumeTierConfig;
    fromPartial(object: DeepPartial<ModelVolumeTierConfig>): ModelVolumeTierConfig;
};
export declare const PointsSetEmissionConfigRequest: {
    encode(message: PointsSetEmissionConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSetEmissionConfigRequest;
    fromJSON(object: any): PointsSetEmissionConfigRequest;
    toJSON(message: PointsSetEmissionConfigRequest): unknown;
    create(base?: DeepPartial<PointsSetEmissionConfigRequest>): PointsSetEmissionConfigRequest;
    fromPartial(object: DeepPartial<PointsSetEmissionConfigRequest>): PointsSetEmissionConfigRequest;
};
export declare const PointsSetEmissionConfigResponse: {
    encode(message: PointsSetEmissionConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsSetEmissionConfigResponse;
    fromJSON(object: any): PointsSetEmissionConfigResponse;
    toJSON(message: PointsSetEmissionConfigResponse): unknown;
    create(base?: DeepPartial<PointsSetEmissionConfigResponse>): PointsSetEmissionConfigResponse;
    fromPartial(object: DeepPartial<PointsSetEmissionConfigResponse>): PointsSetEmissionConfigResponse;
};
export declare const PointsListBanConfigsRequest: {
    encode(_: PointsListBanConfigsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsListBanConfigsRequest;
    fromJSON(_: any): PointsListBanConfigsRequest;
    toJSON(_: PointsListBanConfigsRequest): unknown;
    create(base?: DeepPartial<PointsListBanConfigsRequest>): PointsListBanConfigsRequest;
    fromPartial(_: DeepPartial<PointsListBanConfigsRequest>): PointsListBanConfigsRequest;
};
export declare const BanConfigCollection: {
    encode(message: BanConfigCollection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BanConfigCollection;
    fromJSON(object: any): BanConfigCollection;
    toJSON(message: BanConfigCollection): unknown;
    create(base?: DeepPartial<BanConfigCollection>): BanConfigCollection;
    fromPartial(object: DeepPartial<BanConfigCollection>): BanConfigCollection;
};
export declare const BanConfig: {
    encode(message: BanConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BanConfig;
    fromJSON(object: any): BanConfig;
    toJSON(message: BanConfig): unknown;
    create(base?: DeepPartial<BanConfig>): BanConfig;
    fromPartial(object: DeepPartial<BanConfig>): BanConfig;
};
export declare const PointsCreateBanConfigRequest: {
    encode(message: PointsCreateBanConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsCreateBanConfigRequest;
    fromJSON(object: any): PointsCreateBanConfigRequest;
    toJSON(message: PointsCreateBanConfigRequest): unknown;
    create(base?: DeepPartial<PointsCreateBanConfigRequest>): PointsCreateBanConfigRequest;
    fromPartial(object: DeepPartial<PointsCreateBanConfigRequest>): PointsCreateBanConfigRequest;
};
export declare const PointsDeleteBanConfigRequest: {
    encode(message: PointsDeleteBanConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteBanConfigRequest;
    fromJSON(object: any): PointsDeleteBanConfigRequest;
    toJSON(message: PointsDeleteBanConfigRequest): unknown;
    create(base?: DeepPartial<PointsDeleteBanConfigRequest>): PointsDeleteBanConfigRequest;
    fromPartial(object: DeepPartial<PointsDeleteBanConfigRequest>): PointsDeleteBanConfigRequest;
};
export declare const PointsDeleteBanConfigResponse: {
    encode(_: PointsDeleteBanConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteBanConfigResponse;
    fromJSON(_: any): PointsDeleteBanConfigResponse;
    toJSON(_: PointsDeleteBanConfigResponse): unknown;
    create(base?: DeepPartial<PointsDeleteBanConfigResponse>): PointsDeleteBanConfigResponse;
    fromPartial(_: DeepPartial<PointsDeleteBanConfigResponse>): PointsDeleteBanConfigResponse;
};
export declare const PointsListMultiplierConfigsRequest: {
    encode(_: PointsListMultiplierConfigsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsListMultiplierConfigsRequest;
    fromJSON(_: any): PointsListMultiplierConfigsRequest;
    toJSON(_: PointsListMultiplierConfigsRequest): unknown;
    create(base?: DeepPartial<PointsListMultiplierConfigsRequest>): PointsListMultiplierConfigsRequest;
    fromPartial(_: DeepPartial<PointsListMultiplierConfigsRequest>): PointsListMultiplierConfigsRequest;
};
export declare const PointsMultiplierConfigCollection: {
    encode(message: PointsMultiplierConfigCollection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsMultiplierConfigCollection;
    fromJSON(object: any): PointsMultiplierConfigCollection;
    toJSON(message: PointsMultiplierConfigCollection): unknown;
    create(base?: DeepPartial<PointsMultiplierConfigCollection>): PointsMultiplierConfigCollection;
    fromPartial(object: DeepPartial<PointsMultiplierConfigCollection>): PointsMultiplierConfigCollection;
};
export declare const PointsMultiplierConfig: {
    encode(message: PointsMultiplierConfig, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsMultiplierConfig;
    fromJSON(object: any): PointsMultiplierConfig;
    toJSON(message: PointsMultiplierConfig): unknown;
    create(base?: DeepPartial<PointsMultiplierConfig>): PointsMultiplierConfig;
    fromPartial(object: DeepPartial<PointsMultiplierConfig>): PointsMultiplierConfig;
};
export declare const PointsCreateMultiplierConfigRequest: {
    encode(message: PointsCreateMultiplierConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsCreateMultiplierConfigRequest;
    fromJSON(object: any): PointsCreateMultiplierConfigRequest;
    toJSON(message: PointsCreateMultiplierConfigRequest): unknown;
    create(base?: DeepPartial<PointsCreateMultiplierConfigRequest>): PointsCreateMultiplierConfigRequest;
    fromPartial(object: DeepPartial<PointsCreateMultiplierConfigRequest>): PointsCreateMultiplierConfigRequest;
};
export declare const PointsCreateMultiplierConfigResponse: {
    encode(message: PointsCreateMultiplierConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsCreateMultiplierConfigResponse;
    fromJSON(object: any): PointsCreateMultiplierConfigResponse;
    toJSON(message: PointsCreateMultiplierConfigResponse): unknown;
    create(base?: DeepPartial<PointsCreateMultiplierConfigResponse>): PointsCreateMultiplierConfigResponse;
    fromPartial(object: DeepPartial<PointsCreateMultiplierConfigResponse>): PointsCreateMultiplierConfigResponse;
};
export declare const PointsDeleteMultiplierConfigRequest: {
    encode(message: PointsDeleteMultiplierConfigRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteMultiplierConfigRequest;
    fromJSON(object: any): PointsDeleteMultiplierConfigRequest;
    toJSON(message: PointsDeleteMultiplierConfigRequest): unknown;
    create(base?: DeepPartial<PointsDeleteMultiplierConfigRequest>): PointsDeleteMultiplierConfigRequest;
    fromPartial(object: DeepPartial<PointsDeleteMultiplierConfigRequest>): PointsDeleteMultiplierConfigRequest;
};
export declare const PointsDeleteMultiplierConfigResponse: {
    encode(_: PointsDeleteMultiplierConfigResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteMultiplierConfigResponse;
    fromJSON(_: any): PointsDeleteMultiplierConfigResponse;
    toJSON(_: PointsDeleteMultiplierConfigResponse): unknown;
    create(base?: DeepPartial<PointsDeleteMultiplierConfigResponse>): PointsDeleteMultiplierConfigResponse;
    fromPartial(_: DeepPartial<PointsDeleteMultiplierConfigResponse>): PointsDeleteMultiplierConfigResponse;
};
export declare const PointsListAdminCorrectionsRequest: {
    encode(message: PointsListAdminCorrectionsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsListAdminCorrectionsRequest;
    fromJSON(object: any): PointsListAdminCorrectionsRequest;
    toJSON(message: PointsListAdminCorrectionsRequest): unknown;
    create(base?: DeepPartial<PointsListAdminCorrectionsRequest>): PointsListAdminCorrectionsRequest;
    fromPartial(object: DeepPartial<PointsListAdminCorrectionsRequest>): PointsListAdminCorrectionsRequest;
};
export declare const AdminPointsCorrectionCollection: {
    encode(message: AdminPointsCorrectionCollection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AdminPointsCorrectionCollection;
    fromJSON(object: any): AdminPointsCorrectionCollection;
    toJSON(message: AdminPointsCorrectionCollection): unknown;
    create(base?: DeepPartial<AdminPointsCorrectionCollection>): AdminPointsCorrectionCollection;
    fromPartial(object: DeepPartial<AdminPointsCorrectionCollection>): AdminPointsCorrectionCollection;
};
export declare const AdminPointsCorrection: {
    encode(message: AdminPointsCorrection, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AdminPointsCorrection;
    fromJSON(object: any): AdminPointsCorrection;
    toJSON(message: AdminPointsCorrection): unknown;
    create(base?: DeepPartial<AdminPointsCorrection>): AdminPointsCorrection;
    fromPartial(object: DeepPartial<AdminPointsCorrection>): AdminPointsCorrection;
};
export declare const PointsCreateAdminCorrectionRequest: {
    encode(message: PointsCreateAdminCorrectionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsCreateAdminCorrectionRequest;
    fromJSON(object: any): PointsCreateAdminCorrectionRequest;
    toJSON(message: PointsCreateAdminCorrectionRequest): unknown;
    create(base?: DeepPartial<PointsCreateAdminCorrectionRequest>): PointsCreateAdminCorrectionRequest;
    fromPartial(object: DeepPartial<PointsCreateAdminCorrectionRequest>): PointsCreateAdminCorrectionRequest;
};
export declare const PointsCreateAdminCorrectionResponse: {
    encode(message: PointsCreateAdminCorrectionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsCreateAdminCorrectionResponse;
    fromJSON(object: any): PointsCreateAdminCorrectionResponse;
    toJSON(message: PointsCreateAdminCorrectionResponse): unknown;
    create(base?: DeepPartial<PointsCreateAdminCorrectionResponse>): PointsCreateAdminCorrectionResponse;
    fromPartial(object: DeepPartial<PointsCreateAdminCorrectionResponse>): PointsCreateAdminCorrectionResponse;
};
export declare const PointsDeleteAdminCorrectionRequest: {
    encode(message: PointsDeleteAdminCorrectionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteAdminCorrectionRequest;
    fromJSON(object: any): PointsDeleteAdminCorrectionRequest;
    toJSON(message: PointsDeleteAdminCorrectionRequest): unknown;
    create(base?: DeepPartial<PointsDeleteAdminCorrectionRequest>): PointsDeleteAdminCorrectionRequest;
    fromPartial(object: DeepPartial<PointsDeleteAdminCorrectionRequest>): PointsDeleteAdminCorrectionRequest;
};
export declare const PointsDeleteAdminCorrectionResponse: {
    encode(_: PointsDeleteAdminCorrectionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsDeleteAdminCorrectionResponse;
    fromJSON(_: any): PointsDeleteAdminCorrectionResponse;
    toJSON(_: PointsDeleteAdminCorrectionResponse): unknown;
    create(base?: DeepPartial<PointsDeleteAdminCorrectionResponse>): PointsDeleteAdminCorrectionResponse;
    fromPartial(_: DeepPartial<PointsDeleteAdminCorrectionResponse>): PointsDeleteAdminCorrectionResponse;
};
export declare const PointsReprocessTradesAfterDateRequest: {
    encode(message: PointsReprocessTradesAfterDateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsReprocessTradesAfterDateRequest;
    fromJSON(object: any): PointsReprocessTradesAfterDateRequest;
    toJSON(message: PointsReprocessTradesAfterDateRequest): unknown;
    create(base?: DeepPartial<PointsReprocessTradesAfterDateRequest>): PointsReprocessTradesAfterDateRequest;
    fromPartial(object: DeepPartial<PointsReprocessTradesAfterDateRequest>): PointsReprocessTradesAfterDateRequest;
};
export declare const PointsReprocessTradesAfterDateResponse: {
    encode(message: PointsReprocessTradesAfterDateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PointsReprocessTradesAfterDateResponse;
    fromJSON(object: any): PointsReprocessTradesAfterDateResponse;
    toJSON(message: PointsReprocessTradesAfterDateResponse): unknown;
    create(base?: DeepPartial<PointsReprocessTradesAfterDateResponse>): PointsReprocessTradesAfterDateResponse;
    fromPartial(object: DeepPartial<PointsReprocessTradesAfterDateResponse>): PointsReprocessTradesAfterDateResponse;
};
/** Service handles trading points calculation and configuration. */
export interface PointsSvc {
    /** Gets user's total points and rank by account address. */
    PointsLatestForAccount(request: DeepPartial<PointsLatestForAccountRequest>, metadata?: grpc.Metadata): Promise<PointsLatestForAccountResponse>;
    /** Gets user's daily incurred points and volume by account address. */
    PointsStatsDailyForAccount(request: DeepPartial<PointsStatsDailyForAccountRequest>, metadata?: grpc.Metadata): Promise<HistoricalPointsStatsRowCollection>;
    /** Gets user's weekly incurred points and volume by account address. */
    PointsStatsWeeklyForAccount(request: DeepPartial<PointsStatsWeeklyForAccountRequest>, metadata?: grpc.Metadata): Promise<HistoricalPointsStatsRowCollection>;
    /**
     * Admin-only endpoint to get the true leaderboard with all participants and
     * ranks.
     */
    PointsLeaderboard(request: DeepPartial<PointsLeaderboardRequest>, metadata?: grpc.Metadata): Promise<LeaderboardPointsRowCollection>;
    /**
     * Admin-only endpoint to simulate points allocation for a trade without
     * persisting to database.
     */
    PointsSimulateAllocation(request: DeepPartial<PointsSimulateAllocationRequest>, metadata?: grpc.Metadata): Promise<PointsSimulateAllocationResponse>;
    /** Admin-only endpoint to get the current league configuration. */
    PointsGetLeagueConfig(request: DeepPartial<PointsGetLeagueConfigRequest>, metadata?: grpc.Metadata): Promise<PointsGetLeagueConfigResponse>;
    /** Admin-only endpoint to update the league configuration. */
    PointsSetLeagueConfig(request: DeepPartial<PointsSetLeagueConfigRequest>, metadata?: grpc.Metadata): Promise<PointsSetLeagueConfigResponse>;
    /** Admin-only endpoint to get the current points emission configuration. */
    PointsGetEmissionConfig(request: DeepPartial<PointsGetEmissionConfigRequest>, metadata?: grpc.Metadata): Promise<PointsGetEmissionConfigResponse>;
    /** Admin-only endpoint to update the points emission configuration. */
    PointsSetEmissionConfig(request: DeepPartial<PointsSetEmissionConfigRequest>, metadata?: grpc.Metadata): Promise<PointsSetEmissionConfigResponse>;
    /** Admin-only endpoint to list all banned account configurations. */
    PointsListBanConfigs(request: DeepPartial<PointsListBanConfigsRequest>, metadata?: grpc.Metadata): Promise<BanConfigCollection>;
    /** Admin-only endpoint to create a new ban configuration for an account. */
    PointsCreateBanConfig(request: DeepPartial<PointsCreateBanConfigRequest>, metadata?: grpc.Metadata): Promise<BanConfigCollection>;
    /** Admin-only endpoint to delete a ban configuration. */
    PointsDeleteBanConfig(request: DeepPartial<PointsDeleteBanConfigRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteBanConfigResponse>;
    /** Admin-only endpoint to list all points multiplier configurations. */
    PointsListMultiplierConfigs(request: DeepPartial<PointsListMultiplierConfigsRequest>, metadata?: grpc.Metadata): Promise<PointsMultiplierConfigCollection>;
    /** Admin-only endpoint to create a new points multiplier configuration. */
    PointsCreateMultiplierConfig(request: DeepPartial<PointsCreateMultiplierConfigRequest>, metadata?: grpc.Metadata): Promise<PointsCreateMultiplierConfigResponse>;
    /** Admin-only endpoint to delete a points multiplier configuration. */
    PointsDeleteMultiplierConfig(request: DeepPartial<PointsDeleteMultiplierConfigRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteMultiplierConfigResponse>;
    /** Admin-only endpoint to list all points corrections. */
    PointsListAdminCorrections(request: DeepPartial<PointsListAdminCorrectionsRequest>, metadata?: grpc.Metadata): Promise<AdminPointsCorrectionCollection>;
    /** Admin-only endpoint to create a new points correction. */
    PointsCreateAdminCorrection(request: DeepPartial<PointsCreateAdminCorrectionRequest>, metadata?: grpc.Metadata): Promise<PointsCreateAdminCorrectionResponse>;
    /** Admin-only endpoint to delete a points correction. */
    PointsDeleteAdminCorrection(request: DeepPartial<PointsDeleteAdminCorrectionRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteAdminCorrectionResponse>;
    /** Admin-only endpoint to reprocess trades after a given date. */
    PointsReprocessTradesAfterDate(request: DeepPartial<PointsReprocessTradesAfterDateRequest>, metadata?: grpc.Metadata): Promise<PointsReprocessTradesAfterDateResponse>;
}
export declare class PointsSvcClientImpl implements PointsSvc {
    private readonly rpc;
    constructor(rpc: Rpc);
    PointsLatestForAccount(request: DeepPartial<PointsLatestForAccountRequest>, metadata?: grpc.Metadata): Promise<PointsLatestForAccountResponse>;
    PointsStatsDailyForAccount(request: DeepPartial<PointsStatsDailyForAccountRequest>, metadata?: grpc.Metadata): Promise<HistoricalPointsStatsRowCollection>;
    PointsStatsWeeklyForAccount(request: DeepPartial<PointsStatsWeeklyForAccountRequest>, metadata?: grpc.Metadata): Promise<HistoricalPointsStatsRowCollection>;
    PointsLeaderboard(request: DeepPartial<PointsLeaderboardRequest>, metadata?: grpc.Metadata): Promise<LeaderboardPointsRowCollection>;
    PointsSimulateAllocation(request: DeepPartial<PointsSimulateAllocationRequest>, metadata?: grpc.Metadata): Promise<PointsSimulateAllocationResponse>;
    PointsGetLeagueConfig(request: DeepPartial<PointsGetLeagueConfigRequest>, metadata?: grpc.Metadata): Promise<PointsGetLeagueConfigResponse>;
    PointsSetLeagueConfig(request: DeepPartial<PointsSetLeagueConfigRequest>, metadata?: grpc.Metadata): Promise<PointsSetLeagueConfigResponse>;
    PointsGetEmissionConfig(request: DeepPartial<PointsGetEmissionConfigRequest>, metadata?: grpc.Metadata): Promise<PointsGetEmissionConfigResponse>;
    PointsSetEmissionConfig(request: DeepPartial<PointsSetEmissionConfigRequest>, metadata?: grpc.Metadata): Promise<PointsSetEmissionConfigResponse>;
    PointsListBanConfigs(request: DeepPartial<PointsListBanConfigsRequest>, metadata?: grpc.Metadata): Promise<BanConfigCollection>;
    PointsCreateBanConfig(request: DeepPartial<PointsCreateBanConfigRequest>, metadata?: grpc.Metadata): Promise<BanConfigCollection>;
    PointsDeleteBanConfig(request: DeepPartial<PointsDeleteBanConfigRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteBanConfigResponse>;
    PointsListMultiplierConfigs(request: DeepPartial<PointsListMultiplierConfigsRequest>, metadata?: grpc.Metadata): Promise<PointsMultiplierConfigCollection>;
    PointsCreateMultiplierConfig(request: DeepPartial<PointsCreateMultiplierConfigRequest>, metadata?: grpc.Metadata): Promise<PointsCreateMultiplierConfigResponse>;
    PointsDeleteMultiplierConfig(request: DeepPartial<PointsDeleteMultiplierConfigRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteMultiplierConfigResponse>;
    PointsListAdminCorrections(request: DeepPartial<PointsListAdminCorrectionsRequest>, metadata?: grpc.Metadata): Promise<AdminPointsCorrectionCollection>;
    PointsCreateAdminCorrection(request: DeepPartial<PointsCreateAdminCorrectionRequest>, metadata?: grpc.Metadata): Promise<PointsCreateAdminCorrectionResponse>;
    PointsDeleteAdminCorrection(request: DeepPartial<PointsDeleteAdminCorrectionRequest>, metadata?: grpc.Metadata): Promise<PointsDeleteAdminCorrectionResponse>;
    PointsReprocessTradesAfterDate(request: DeepPartial<PointsReprocessTradesAfterDateRequest>, metadata?: grpc.Metadata): Promise<PointsReprocessTradesAfterDateResponse>;
}
export declare const PointsSvcDesc: {
    serviceName: string;
};
export declare const PointsSvcPointsLatestForAccountDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsStatsDailyForAccountDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsStatsWeeklyForAccountDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsLeaderboardDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsSimulateAllocationDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsGetLeagueConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsSetLeagueConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsGetEmissionConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsSetEmissionConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsListBanConfigsDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsCreateBanConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsDeleteBanConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsListMultiplierConfigsDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsCreateMultiplierConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsDeleteMultiplierConfigDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsListAdminCorrectionsDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsCreateAdminCorrectionDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsDeleteAdminCorrectionDesc: UnaryMethodDefinitionish;
export declare const PointsSvcPointsReprocessTradesAfterDateDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
