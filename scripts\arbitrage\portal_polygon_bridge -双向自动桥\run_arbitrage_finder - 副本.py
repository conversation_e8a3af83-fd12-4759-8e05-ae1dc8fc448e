#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行Polygon-以太坊跨链套利查找器的简单脚本
已升级为使用真正的多线程处理和代理支持
"""

import os
import sys
import asyncio
import time
import argparse
from datetime import datetime
import threading

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder import BridgeArbFinder, TOKENS_FILE, MIN_USDT_AMOUNT, MAX_USDT_AMOUNT, RESULTS_DIR, DEFAULT_NUM_WORKERS

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Polygon-以太坊跨链套利查找器 (多线程版本)')
    parser.add_argument('--threads', type=int, default=DEFAULT_NUM_WORKERS,
                      help=f'工作线程数 (默认: {DEFAULT_NUM_WORKERS})')
    parser.add_argument('--min-amount', type=float, default=MIN_USDT_AMOUNT,
                      help=f'最小USDT测试金额 (默认: {MIN_USDT_AMOUNT})')
    parser.add_argument('--max-amount', type=float, default=MAX_USDT_AMOUNT,
                      help=f'最大USDT测试金额 (默认: {MAX_USDT_AMOUNT})')
    parser.add_argument('--token-limit', type=int, default=0,
                      help='限制处理的代币数量 (默认: 0, 表示处理所有代币)')
    parser.add_argument('--no-proxy', action='store_true',
                      help='禁用代理 (默认: 使用配置中的代理)')
    parser.add_argument('--profit-threshold', type=float, default=0.0,
                      help='利润阈值(%) (默认: 0.0, 表示只显示利润大于0的机会。设置为负值如-0.5将显示利润率>=-0.5%的所有交易，包括小亏损的交易)')
    args = parser.parse_args()
    
    start_time = time.time()
    print(f"🕒 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔍 使用 {args.threads} 个线程处理任务")
    
    # 判断是否显示代理警告
    if not args.no_proxy:
        print("🔌 将使用config.yaml中配置的代理")
    else:
        print("⚠️ 已禁用代理，将直接连接API")
    
    # 设置自定义测试金额范围，如果提供了
    min_amount = args.min_amount
    max_amount = args.max_amount
    
    if args.min_amount != MIN_USDT_AMOUNT or args.max_amount != MAX_USDT_AMOUNT:
        print(f"💰 使用自定义测试金额范围: {args.min_amount}-{args.max_amount} USDT")
    
    # 显示利润阈值信息
    if args.profit_threshold != 0.0:
        if args.profit_threshold < 0:
            print(f"📊 使用自定义利润阈值: {args.profit_threshold}% (将显示利润率>={args.profit_threshold}%的交易，包括小亏损的交易)")
        else:
            print(f"📊 使用自定义利润阈值: {args.profit_threshold}% (将显示利润率>={args.profit_threshold}%的交易)")
    else:
        print("📊 使用默认利润阈值: 仅显示利润>0的交易")
    
    # 创建套利查找器实例，并传入参数
    finder = BridgeArbFinder(
        num_workers=args.threads, 
        profit_threshold=args.profit_threshold
    )
    
    # 设置测试金额范围
    finder.min_usdt_amount = min_amount
    finder.max_usdt_amount = max_amount
    
    token_count = len(finder.tokens_data)
    
    # 如果设置了token_limit，限制处理的代币数量
    if args.token_limit > 0 and args.token_limit < token_count:
        print(f"⚠️ 已限制处理代币数量: {args.token_limit}/{token_count}")
        # 仅保留指定数量的代币
        keys = list(finder.tokens_data.keys())[:args.token_limit]
        finder.tokens_data = {k: finder.tokens_data[k] for k in keys}
        token_count = len(finder.tokens_data)
    
    print(f"🔍 正在分析 {token_count} 个代币对...")
    
    if token_count == 0:
        print("❌ 未找到有效的代币数据，请检查数据源文件")
        return
    
    print("\n⏳ 开始查找套利机会，详细日志将保存到results目录...")
    print("🔄 使用多线程并行处理，性能将大幅提升...")
    
    # 使用新的多线程实现
    opportunities = finder.find_arbitrage_opportunities()
    
    print("\n====== 套利查找完成 =======")
    if opportunities:
        print(f"✅ 找到 {len(opportunities)} 个潜在套利机会!")
        
        # 按净利润百分比排序
        sorted_opps = sorted(opportunities, key=lambda x: x["net_profit_percentage"], reverse=True)
        
        # 去除重复的套利机会（相同代币、方向和接近的利润率）
        unique_opps = []
        seen_pairs = set()  # 用于跟踪已见过的代币对和方向
        
        for opp in sorted_opps:
            # 创建唯一标识符：代币符号 + 方向
            pair_key = f"{opp['symbol']}_{opp['lower_chain']}_{opp['higher_chain']}"
            
            # 如果这个代币对和方向已经存在，跳过
            if pair_key in seen_pairs:
                continue
                
            # 否则添加到唯一列表和已见过集合
            unique_opps.append(opp)
            seen_pairs.add(pair_key)
        
        # 显示前10个最佳机会
        count = min(10, len(unique_opps))
        print(f"\n🔝 前{count}个最佳套利机会:")
        for i, opp in enumerate(unique_opps[:10], 1):
            print(f"{i}. {opp['symbol']} - {opp['lower_chain']} → {opp['higher_chain']}")
            print(f"   投入: {opp['usdt_input']:.2f} USDT → 获得: {opp['usdt_output']:.2f} USDT")
            print(f"   毛利润: {opp['gross_profit']:.2f} USDT ({opp['profit_percentage']:.2f}%)")
            print(f"   Gas成本: {opp['total_gas_cost']:.2f} USDT (买入: {opp['gas_cost_buy']:.2f}, 卖出: {opp['gas_cost_sell']:.2f})")
            print(f"   净利润: {opp['net_profit']:.2f} USDT ({opp['net_profit_percentage']:.2f}%)")
            print(f"   Polygon地址: {opp.get('polygon_address', '未知')}")
            print(f"   以太坊地址: {opp.get('eth_address', '未知')}")
            print(f"   --------")
    else:
        print("❌ 未找到任何套利机会")
        
    end_time = time.time()
    print(f"\n🕒 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏱️ 总耗时: {(end_time - start_time) / 60:.2f} 分钟")
    
if __name__ == "__main__":
    print("=" * 60)
    print("🔍 Polygon-以太坊跨链套利查找器 (多线程版本)")
    print("=" * 60)
    print(f"📊 数据源: {TOKENS_FILE}")
    print(f"💰 默认测试金额范围: {MIN_USDT_AMOUNT}-{MAX_USDT_AMOUNT} USDT")
    print(f"📂 结果保存路径: {RESULTS_DIR}")
    print("=" * 60)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        # 尝试优雅地关闭线程
        print("正在尝试终止线程...")
        # 给一些时间让线程关闭
        time.sleep(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n✨ 感谢使用多线程套利查找器! ✨") 