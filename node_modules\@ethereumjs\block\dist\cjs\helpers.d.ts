import type { BlockHeaderBytes, HeaderData } from './types.js';
import type { TypedTransaction } from '@ethereumjs/tx';
import type { PrefixedHexString } from '@ethereumjs/util';
/**
 * Returns a 0x-prefixed hex number string from a hex string or string integer.
 * @param {string} input string to check, convert, and return
 */
export declare const numberToHex: (input?: string) => PrefixedHexString | undefined;
export declare function valuesArrayToHeaderData(values: BlockHeaderBytes): HeaderData;
export declare function getDifficulty(headerData: HeaderData): bigint | null;
export declare const getNumBlobs: (transactions: TypedTransaction[]) => number;
/**
 * Approximates `factor * e ** (numerator / denominator)` using Taylor expansion
 */
export declare const fakeExponential: (factor: bigint, numerator: bigint, denominator: bigint) => bigint;
//# sourceMappingURL=helpers.d.ts.map