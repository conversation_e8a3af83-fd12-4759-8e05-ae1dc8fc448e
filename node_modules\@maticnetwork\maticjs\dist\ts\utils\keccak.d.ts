export declare class Keccak {
    /**
     * Throws if input is not a buffer
     * @param {<PERSON><PERSON><PERSON>} input value to check
     */
    static assertIsBuffer: (input: Buffer) => void;
    /**
     * Creates Keccak hash of a Buffer input
     * @param a The input data (Buffer)
     * @param bits (number = 256) The Keccak width
     */
    static keccak: (a: Buffer, bits?: number) => Buffer;
    /**
     * Creates Keccak-256 hash of the input, alias for keccak(a, 256).
     * @param a The input data (Buffer)
     */
    static keccak256: (a: Buffer) => Buffer;
}
