import { IndexerInsuranceFund, GrpcIndexerInsuranceFund, GrpcIndexerRedemptionSchedule, Redemption } from '../types/insurance-funds.js';
import { InjectiveInsuranceRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Grpc Transformer
 */
export declare class IndexerGrpcInsuranceFundTransformer {
    static insuranceFundsResponseToInsuranceFunds(response: InjectiveInsuranceRpc.FundsResponse): IndexerInsuranceFund[];
    static redemptionsResponseToRedemptions(response: InjectiveInsuranceRpc.RedemptionsResponse): Redemption[];
    static grpcInsuranceFundToInsuranceFund(grpcInsuranceFund: GrpcIndexerInsuranceFund): IndexerInsuranceFund;
    static grpcInsuranceFundsToInsuranceFunds(grpcInsuranceFunds: GrpcIndexerInsuranceFund[]): IndexerInsuranceFund[];
    static grpcRedemptionToRedemption(redemption: GrpcIndexerRedemptionSchedule): Redemption;
    static grpcRedemptionsToRedemptions(redemptions: GrpcIndexerRedemptionSchedule[]): Redemption[];
}
