import { Deserializer } from '../deserializer.mjs';
import { Serializable, Serializer } from '../serializer.mjs';
import { TransactionArgument } from '../../transactions/instances/transactionArgument.mjs';
import { AnyNumber, Uint8, Uint16, Uint32 } from '../../types/types.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';

/**
 * Represents a boolean value that can be serialized and deserialized.
 * This class extends the Serializable class and provides methods to serialize
 * the boolean value for different contexts, such as entry functions and script functions.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class Bool extends Serializable implements TransactionArgument {
    readonly value: boolean;
    /**
     * Constructs a new instance with a specified value.
     * This ensures that the value is validated to be within the acceptable range.
     *
     * @param value - The number to be validated and assigned, which must be between 0 and MAX_U256_BIG_INT.
     * @group Implementation
     * @category BCS
     */
    constructor(value: boolean);
    /**
     * Serializes the value using the provided serializer.
     * This function is essential for converting the value into a format suitable for transmission or storage.
     *
     * @param serializer - The serializer instance used to perform the serialization.
     * @group Implementation
     * @category BCS
     */
    serialize(serializer: Serializer): void;
    /**
     * Serializes the current instance for use in an entry function by converting it to a byte sequence.
     * This allows the instance to be properly formatted for serialization in transactions.
     *
     * @param serializer - The serializer instance used to serialize the byte sequence.
     * @group Implementation
     * @category BCS
     */
    serializeForEntryFunction(serializer: Serializer): void;
    /**
     * Serializes the current instance for use in a script function.
     * This allows for the conversion of the instance into a format suitable for transmission or storage.
     *
     * @param serializer - The serializer used to perform the serialization.
     * @group Implementation
     * @category BCS
     */
    serializeForScriptFunction(serializer: Serializer): void;
    /**
     * Deserializes a U256 value from the provided deserializer.
     *
     * @param deserializer - The deserializer instance used to read the U256 data.
     * @group Implementation
     * @category BCS
     */
    deserialize(deserializer: Deserializer): U256;
    static deserialize(deserializer: Deserializer): Bool;
}
/**
 * Represents an unsigned 8-bit integer (U8) value.
 * This class extends the Serializable class and provides methods for serialization and deserialization of U8 values.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U8 extends Serializable implements TransactionArgument {
    readonly value: Uint8;
    constructor(value: Uint8);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U8;
}
/**
 * Represents a 16-bit unsigned integer (U16) value.
 * This class extends the Serializable class and provides methods for serialization
 * and deserialization of the U16 value.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U16 extends Serializable implements TransactionArgument {
    readonly value: Uint16;
    constructor(value: Uint16);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U16;
}
/**
 * Represents a 32-bit unsigned integer (U32) that can be serialized and deserialized.
 * This class ensures that the value is within the valid range for a U32.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U32 extends Serializable implements TransactionArgument {
    readonly value: Uint32;
    constructor(value: Uint32);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U32;
}
/**
 * Represents a 64-bit unsigned integer (U64) and provides methods for serialization.
 *
 * This class ensures that the value is within the valid range for a U64 and provides
 * functionality to serialize the value for various use cases, including entry functions
 * and script functions.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U64 extends Serializable implements TransactionArgument {
    readonly value: bigint;
    constructor(value: AnyNumber);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U64;
}
/**
 * Represents a 128-bit unsigned integer value.
 * This class provides methods for serialization and deserialization
 * of U128 values, ensuring that the values are within the valid range.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U128 extends Serializable implements TransactionArgument {
    readonly value: bigint;
    constructor(value: AnyNumber);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U128;
}
/**
 * Represents a 256-bit unsigned integer (U256) that extends the Serializable class.
 * This class provides methods for serialization and deserialization of U256 values,
 * ensuring that the values are within the valid range.
 *
 * @extends Serializable
 * @group Implementation
 * @category BCS
 */
declare class U256 extends Serializable implements TransactionArgument {
    readonly value: bigint;
    constructor(value: AnyNumber);
    serialize(serializer: Serializer): void;
    serializeForEntryFunction(serializer: Serializer): void;
    serializeForScriptFunction(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): U256;
}

export { Bool, U128, U16, U256, U32, U64, U8 };
