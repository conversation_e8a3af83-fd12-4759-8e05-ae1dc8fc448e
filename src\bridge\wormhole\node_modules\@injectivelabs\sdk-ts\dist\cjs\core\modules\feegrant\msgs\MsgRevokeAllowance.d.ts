import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { CosmosFeegrantV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgRevokeAllowance {
    interface Params {
        granter: string;
        grantee: string;
    }
    type Proto = CosmosFeegrantV1Beta1Tx.MsgRevokeAllowance;
}
/**
 * @category Messages
 */
export default class MsgRevokeAllowance extends MsgBase<MsgRevokeAllowance.Params, MsgRevokeAllowance.Proto> {
    static fromJSON(params: MsgRevokeAllowance.Params): MsgRevokeAllowance;
    toProto(): CosmosFeegrantV1Beta1Tx.MsgRevokeAllowance;
    toData(): {
        granter: string;
        grantee: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<MsgRevokeAllowance>;
    };
    toWeb3Gw(): {
        to_proto: {};
        to_data: {};
        to_amino: {};
        to_web3_gw: {};
        to_direct_sign: {};
        to_binary: {};
        params: {
            granter: string;
            grantee: string;
        };
        to_web3: {};
        to_j_s_o_n: {};
        to_eip712_types: {};
        to_eip712: {};
        to_eip712_v2: {};
        to_direct_sign_j_s_o_n: {};
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosFeegrantV1Beta1Tx.MsgRevokeAllowance;
    };
    toBinary(): Uint8Array;
}
