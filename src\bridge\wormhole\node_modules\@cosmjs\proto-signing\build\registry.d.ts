import { BinaryWriter } from "cosmjs-types/binary";
import { TxBody } from "cosmjs-types/cosmos/tx/v1beta1/tx";
import { Any } from "cosmjs-types/google/protobuf/any";
import type protobuf from "protobufjs";
/**
 * A type generated by Telescope 1.0.
 */
export interface TelescopeGeneratedType {
    /** This may or may not exist depending on the code generator settings. Don't rely on it. */
    readonly typeUrl?: string;
    readonly encode: (message: any | {
        [k: string]: any;
    }, writer?: BinaryWriter) => BinaryWriter;
    readonly decode: (input: Uint8Array, length?: number) => any;
    readonly fromPartial: (object: any) => any;
}
/**
 * A type generated by [ts-proto](https://github.com/stephenh/ts-proto).
 */
export interface TsProtoGeneratedType {
    readonly encode: (message: any | {
        [k: string]: any;
    }, writer?: protobuf.Writer) => protobuf.Writer;
    readonly decode: (input: Uint8Array | protobuf.Reader, length?: number) => any;
    readonly fromPartial: (object: any) => any;
}
/**
 * A type generated by [protobufjs](https://github.com/protobufjs/protobuf.js).
 *
 * This can be used if you want to create types at runtime using pure JavaScript.
 * See https://gist.github.com/fadeev/a4981eff1cf3a805ef10e25313d5f2b7
 */
export interface PbjsGeneratedType {
    readonly create: (properties?: {
        [k: string]: any;
    }) => any;
    readonly encode: (message: any | {
        [k: string]: any;
    }, writer?: protobuf.Writer) => protobuf.Writer;
    readonly decode: (reader: protobuf.Reader | Uint8Array, length?: number) => any;
}
export type GeneratedType = TelescopeGeneratedType | TsProtoGeneratedType | PbjsGeneratedType;
export declare function isTelescopeGeneratedType(type: GeneratedType): type is TelescopeGeneratedType;
export declare function isTsProtoGeneratedType(type: GeneratedType): type is TsProtoGeneratedType;
export declare function isPbjsGeneratedType(type: GeneratedType): type is PbjsGeneratedType;
export interface DecodeObject {
    readonly typeUrl: string;
    readonly value: Uint8Array;
}
export interface EncodeObject {
    readonly typeUrl: string;
    readonly value: any;
}
interface TxBodyValue {
    readonly messages: readonly EncodeObject[];
    readonly memo?: string;
    readonly timeoutHeight?: bigint;
    readonly extensionOptions?: Any[];
    readonly nonCriticalExtensionOptions?: Any[];
}
export interface TxBodyEncodeObject extends EncodeObject {
    readonly typeUrl: "/cosmos.tx.v1beta1.TxBody";
    readonly value: TxBodyValue;
}
export declare function isTxBodyEncodeObject(encodeObject: EncodeObject): encodeObject is TxBodyEncodeObject;
export declare class Registry {
    private readonly types;
    /**
     * Creates a new Registry for mapping protobuf type identifiers/type URLs to
     * actual implementations. Those implementations are typically generated with ts-proto
     * but we also support protobuf.js as a type generator.
     *
     * If there is no parameter given, a `new Registry()` adds the types `Coin` and `MsgSend`
     * for historic reasons. Those can be overriden by customTypes.
     *
     * There are currently two methods for adding new types:
     * 1. Passing types to the constructor.
     * 2. Using the `register()` method
     */
    constructor(customTypes?: Iterable<[string, GeneratedType]>);
    register(typeUrl: string, type: GeneratedType): void;
    /**
     * Looks up a type that was previously added to the registry.
     *
     * The generator information (ts-proto or pbjs) gets lost along the way.
     * If you need to work with the result type in TypeScript, you can use:
     *
     * ```
     * import { assert } from "@cosmjs/utils";
     *
     * const Coin = registry.lookupType("/cosmos.base.v1beta1.Coin");
     * assert(Coin); // Ensures not unset
     * assert(isTsProtoGeneratedType(Coin)); // Ensures this is the type we expect
     *
     * // Coin is typed TsProtoGeneratedType now.
     * ```
     */
    lookupType(typeUrl: string): GeneratedType | undefined;
    private lookupTypeWithError;
    /**
     * Takes a typeUrl/value pair and encodes the value to protobuf if
     * the given type was previously registered.
     *
     * If the value has to be wrapped in an Any, this needs to be done
     * manually after this call. Or use `encodeAsAny` instead.
     */
    encode(encodeObject: EncodeObject): Uint8Array;
    /**
     * Takes a typeUrl/value pair and encodes the value to an Any if
     * the given type was previously registered.
     */
    encodeAsAny(encodeObject: EncodeObject): Any;
    encodeTxBody(txBodyFields: TxBodyValue): Uint8Array;
    decode({ typeUrl, value }: DecodeObject): any;
    decodeTxBody(txBody: Uint8Array): TxBody;
}
export {};
