"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExactDecimalsFromNumber = exports.getSignificantDecimalsFromNumber = exports.getTriggerPrice = exports.getTensMultiplier = exports.numberToCosmosSdkDecString = exports.cosmosSdkDecToBigNumber = exports.spotQuantityFromChainQuantityToFixed = exports.spotQuantityFromChainQuantity = exports.spotQuantityToChainQuantityToFixed = exports.spotQuantityToChainQuantity = exports.spotPriceFromChainPriceToFixed = exports.spotPriceFromChainPrice = exports.spotPriceToChainPriceToFixed = exports.spotPriceToChainPrice = exports.derivativeQuantityFromChainQuantityToFixed = exports.derivativeQuantityFromChainQuantity = exports.derivativeQuantityToChainQuantityToFixed = exports.derivativeQuantityToChainQuantity = exports.derivativePriceFromChainPriceToFixed = exports.derivativePriceFromChainPrice = exports.derivativePriceToChainPriceToFixed = exports.derivativePriceToChainPrice = exports.derivativeMarginFromChainMarginToFixed = exports.derivativeMarginFromChainMargin = exports.derivativeMarginToChainMarginToFixed = exports.derivativeMarginToChainMargin = exports.denomAmountFromChainDenomAmountToFixed = exports.denomAmountFromChainDenomAmount = exports.denomAmountToChainDenomAmountToFixed = exports.denomAmountToChainDenomAmount = exports.amountToCosmosSdkDecAmount = exports.denomAmountToGrpcChainDenomAmount = exports.denomAmountFromGrpcChainDenomAmount = exports.formatPriceToAllowableDecimals = exports.formatAmountToAllowableDecimals = exports.formatPriceToAllowablePrice = exports.formatAmountToAllowableAmount = exports.formatNumberToAllowableTensMultiplier = exports.formatNumberToAllowableDecimals = exports.isNumber = void 0;
const utils_1 = require("@injectivelabs/utils");
Object.defineProperty(exports, "getSignificantDecimalsFromNumber", { enumerable: true, get: function () { return utils_1.getSignificantDecimalsFromNumber; } });
Object.defineProperty(exports, "getExactDecimalsFromNumber", { enumerable: true, get: function () { return utils_1.getExactDecimalsFromNumber; } });
const $BigNumber = utils_1.BigNumber.clone({ ROUNDING_MODE: utils_1.BigNumber.ROUND_DOWN });
const isNumber = (number) => {
    if (typeof number === 'number') {
        return true;
    }
    return !isNaN(parseFloat(number));
};
exports.isNumber = isNumber;
const formatNumberToAllowableDecimals = (value, allowableDecimals, roundingMode) => {
    const decimalPlacesInValue = new utils_1.BigNumberInBase((0, utils_1.getExactDecimalsFromNumber)(value));
    const valueToString = value.toString();
    if (decimalPlacesInValue.lte(0)) {
        return valueToString;
    }
    const decimalMoreThanAllowance = decimalPlacesInValue.gte(allowableDecimals);
    return decimalMoreThanAllowance
        ? new utils_1.BigNumberInBase(valueToString).toFixed(allowableDecimals, roundingMode)
        : valueToString;
};
exports.formatNumberToAllowableDecimals = formatNumberToAllowableDecimals;
const formatNumberToAllowableTensMultiplier = (value, tensMultiplier, roundingMode) => {
    const valueToBn = new utils_1.BigNumberInBase(value);
    if (tensMultiplier === 0) {
        return valueToBn.toFixed(0, roundingMode);
    }
    const tensMul = new utils_1.BigNumberInBase(10).pow(tensMultiplier);
    if (valueToBn.lte(tensMul)) {
        return tensMul.toFixed(0, roundingMode);
    }
    return new utils_1.BigNumberInBase(valueToBn.div(tensMul).toFixed(0, roundingMode))
        .multipliedBy(tensMul)
        .toFixed(0);
};
exports.formatNumberToAllowableTensMultiplier = formatNumberToAllowableTensMultiplier;
const formatAmountToAllowableAmount = (value, tensMultiplier) => {
    return tensMultiplier < 0
        ? (0, exports.formatNumberToAllowableDecimals)(value, -tensMultiplier, utils_1.BigNumberInBase.ROUND_DOWN)
        : (0, exports.formatNumberToAllowableTensMultiplier)(value, tensMultiplier, utils_1.BigNumberInBase.ROUND_DOWN);
};
exports.formatAmountToAllowableAmount = formatAmountToAllowableAmount;
const formatPriceToAllowablePrice = (value, tensMultiplier) => {
    return tensMultiplier <= 0
        ? (0, exports.formatNumberToAllowableDecimals)(value, -tensMultiplier)
        : (0, exports.formatNumberToAllowableTensMultiplier)(value, tensMultiplier);
};
exports.formatPriceToAllowablePrice = formatPriceToAllowablePrice;
/**
 *
 * Legacy function - use formatNumberToAllowableDecimals
 *
 * @param value
 * @param allowableDecimals
 * @returns
 */
const formatAmountToAllowableDecimals = (value, allowableDecimals) => {
    return (0, exports.formatNumberToAllowableDecimals)(value, allowableDecimals, utils_1.BigNumberInBase.ROUND_DOWN);
};
exports.formatAmountToAllowableDecimals = formatAmountToAllowableDecimals;
/**
 *
 * Legacy function - use formatNumberToAllowableDecimals
 *
 * @param value
 * @param allowableDecimals
 * @returns
 */
const formatPriceToAllowableDecimals = (value, allowableDecimals) => {
    return (0, exports.formatNumberToAllowableDecimals)(value, allowableDecimals);
};
exports.formatPriceToAllowableDecimals = formatPriceToAllowableDecimals;
/**
 * On chain amounts queried from a sentry using the
 * gRPC API are returned with an extra decimal point
 * 18 places from the beginning, so we need to remove it
 * to get a workable amount
 */
const denomAmountFromGrpcChainDenomAmount = (value) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(18));
exports.denomAmountFromGrpcChainDenomAmount = denomAmountFromGrpcChainDenomAmount;
/**
 * On chain amounts broadcasted to a sentry directly using the
 * gRPC API should be passed with an extra decimal point
 * 18 places from the beginning, so we need to add it
 * to get a workable amount
 */
const denomAmountToGrpcChainDenomAmount = (value) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(18));
exports.denomAmountToGrpcChainDenomAmount = denomAmountToGrpcChainDenomAmount;
/**
 * On chain amounts (based on the cosmosSdk.Dec type)
 * broadcasted to a sentry directly using the
 * gRPC API should be passed with an extra decimal point
 * 18 places from the beginning (i.e multiplied by 1e18), so we need to add it
 * to get a workable amount
 */
const amountToCosmosSdkDecAmount = (value) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(18));
exports.amountToCosmosSdkDecAmount = amountToCosmosSdkDecAmount;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const denomAmountToChainDenomAmount = ({ value, decimals = 18, }) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(decimals));
exports.denomAmountToChainDenomAmount = denomAmountToChainDenomAmount;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const denomAmountToChainDenomAmountToFixed = ({ value, decimals = 18, tensMultiplier, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.denomAmountToChainDenomAmount)({
        value: flooredValue,
        decimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.denomAmountToChainDenomAmountToFixed = denomAmountToChainDenomAmountToFixed;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const denomAmountFromChainDenomAmount = ({ value, decimals = 18, }) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(decimals));
exports.denomAmountFromChainDenomAmount = denomAmountFromChainDenomAmount;
/**
 *
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
const denomAmountFromChainDenomAmountToFixed = ({ value, decimals = 18, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.denomAmountFromChainDenomAmount)({ value, decimals });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.denomAmountFromChainDenomAmountToFixed = denomAmountFromChainDenomAmountToFixed;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const derivativeMarginToChainMargin = ({ value, quoteDecimals = 18, }) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(quoteDecimals));
exports.derivativeMarginToChainMargin = derivativeMarginToChainMargin;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
const derivativeMarginToChainMarginToFixed = ({ value, quoteDecimals = 18, tensMultiplier, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.derivativeMarginToChainMargin)({
        value: flooredValue,
        quoteDecimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativeMarginToChainMarginToFixed = derivativeMarginToChainMarginToFixed;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const derivativeMarginFromChainMargin = ({ value, quoteDecimals = 18, }) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(quoteDecimals));
exports.derivativeMarginFromChainMargin = derivativeMarginFromChainMargin;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const derivativeMarginFromChainMarginToFixed = ({ value, quoteDecimals = 18, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.derivativeMarginFromChainMargin)({ value, quoteDecimals });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativeMarginFromChainMarginToFixed = derivativeMarginFromChainMarginToFixed;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const derivativePriceToChainPrice = ({ value, quoteDecimals = 18, }) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(quoteDecimals));
exports.derivativePriceToChainPrice = derivativePriceToChainPrice;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
const derivativePriceToChainPriceToFixed = ({ value, tensMultiplier, quoteDecimals = 18, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.derivativePriceToChainPrice)({
        value: flooredValue,
        quoteDecimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativePriceToChainPriceToFixed = derivativePriceToChainPriceToFixed;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
const derivativePriceFromChainPrice = ({ value, quoteDecimals = 18, }) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(quoteDecimals));
exports.derivativePriceFromChainPrice = derivativePriceFromChainPrice;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
const derivativePriceFromChainPriceToFixed = ({ value, quoteDecimals = 18, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.derivativePriceFromChainPrice)({ value, quoteDecimals });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativePriceFromChainPriceToFixed = derivativePriceFromChainPriceToFixed;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number
 */
const derivativeQuantityToChainQuantity = ({ value, }) => new $BigNumber(value);
exports.derivativeQuantityToChainQuantity = derivativeQuantityToChainQuantity;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number stringified
 */
const derivativeQuantityToChainQuantityToFixed = ({ value, decimalPlaces = undefined, tensMultiplier, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.derivativeQuantityToChainQuantity)({ value: flooredValue });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativeQuantityToChainQuantityToFixed = derivativeQuantityToChainQuantityToFixed;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number
 */
const derivativeQuantityFromChainQuantity = ({ value, }) => new $BigNumber(value);
exports.derivativeQuantityFromChainQuantity = derivativeQuantityFromChainQuantity;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number stringified
 */
const derivativeQuantityFromChainQuantityToFixed = ({ value, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.derivativeQuantityFromChainQuantity)({ value });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.derivativeQuantityFromChainQuantityToFixed = derivativeQuantityFromChainQuantityToFixed;
/**
 * Amount that the chain requires is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number
 */
const spotPriceToChainPrice = ({ value, baseDecimals = 18, quoteDecimals = 6, }) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(new $BigNumber(quoteDecimals).minus(baseDecimals)));
exports.spotPriceToChainPrice = spotPriceToChainPrice;
/**
 * Amount that the chain requires is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number stringified
 */
const spotPriceToChainPriceToFixed = ({ value, baseDecimals = 18, quoteDecimals = 6, tensMultiplier, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.spotPriceToChainPrice)({
        value: flooredValue,
        baseDecimals,
        quoteDecimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.spotPriceToChainPriceToFixed = spotPriceToChainPriceToFixed;
/**
 * Amount that the chain returns is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number
 */
const spotPriceFromChainPrice = ({ value, baseDecimals = 18, quoteDecimals = 6, }) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(new $BigNumber(quoteDecimals).minus(baseDecimals)));
exports.spotPriceFromChainPrice = spotPriceFromChainPrice;
/**
 * Amount that the chain returns is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number stringified
 */
const spotPriceFromChainPriceToFixed = ({ value, baseDecimals = 18, quoteDecimals = 6, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.spotPriceFromChainPrice)({ value, baseDecimals, quoteDecimals });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.spotPriceFromChainPriceToFixed = spotPriceFromChainPriceToFixed;
/**
 * Amount that the chain requires is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
const spotQuantityToChainQuantity = ({ value, baseDecimals = 18, }) => new $BigNumber(value).multipliedBy(new $BigNumber(10).pow(baseDecimals));
exports.spotQuantityToChainQuantity = spotQuantityToChainQuantity;
/**
 * Amount that the chain requires is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
const spotQuantityToChainQuantityToFixed = ({ value, baseDecimals = 18, tensMultiplier, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const valueToBn = new utils_1.BigNumberInBase(value).toFixed();
    const flooredValue = tensMultiplier
        ? (0, exports.formatPriceToAllowablePrice)(valueToBn, tensMultiplier)
        : value;
    const number = (0, exports.spotQuantityToChainQuantity)({
        value: flooredValue,
        baseDecimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.spotQuantityToChainQuantityToFixed = spotQuantityToChainQuantityToFixed;
/**
 * Amount that the chain returns is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
const spotQuantityFromChainQuantity = ({ value, baseDecimals = 18, }) => new $BigNumber(value).dividedBy(new $BigNumber(10).pow(baseDecimals));
exports.spotQuantityFromChainQuantity = spotQuantityFromChainQuantity;
/**
 * Amount that the chain returns is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
const spotQuantityFromChainQuantityToFixed = ({ value, baseDecimals = 18, decimalPlaces = undefined, roundingMode = utils_1.BigNumber.ROUND_DOWN, }) => {
    const number = (0, exports.spotQuantityFromChainQuantity)({
        value,
        baseDecimals,
    });
    if (decimalPlaces === undefined) {
        return number.toFixed();
    }
    return number.toFixed((0, utils_1.getSignificantDecimalsFromNumber)(number), roundingMode);
};
exports.spotQuantityFromChainQuantityToFixed = spotQuantityFromChainQuantityToFixed;
const cosmosSdkDecToBigNumber = (number) => new utils_1.BigNumber(number).dividedBy(new utils_1.BigNumber(10).pow(18));
exports.cosmosSdkDecToBigNumber = cosmosSdkDecToBigNumber;
const numberToCosmosSdkDecString = (value) => {
    return new utils_1.BigNumber(value).toFixed(18);
};
exports.numberToCosmosSdkDecString = numberToCosmosSdkDecString;
/**
 * This function returns a multiplier of 10
 * based on the input. There are two cases:
 *
 * 1. If the number is less than 1, it returns a NEGATIVE
 * number which is the number of decimals the number has
 *
 * 2. If the number is higher than 1, it returns a POSITIVE
 * number which is the number of 10 multiplier the number has
 *
 * @param number
 * @returns {number}
 */
const getTensMultiplier = (number) => {
    const numberToBn = new utils_1.BigNumber(number);
    if (numberToBn.eq(1)) {
        return 0;
    }
    if (numberToBn.lt(1)) {
        return -1 * (0, utils_1.getExactDecimalsFromNumber)(numberToBn.toFixed());
    }
    const [, zerosInTheNumber] = numberToBn.toFixed().split('1');
    return zerosInTheNumber.length;
};
exports.getTensMultiplier = getTensMultiplier;
const getTriggerPrice = (triggerPrice) => {
    return triggerPrice ? (0, exports.amountToCosmosSdkDecAmount)(triggerPrice).toFixed() : '';
};
exports.getTriggerPrice = getTriggerPrice;
