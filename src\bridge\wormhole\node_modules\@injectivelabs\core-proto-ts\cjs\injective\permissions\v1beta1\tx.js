"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.MsgClaimVoucherDesc = exports.MsgUpdateActorRolesDesc = exports.MsgUpdateNamespaceDesc = exports.MsgCreateNamespaceDesc = exports.MsgUpdateParamsDesc = exports.MsgDesc = exports.MsgClientImpl = exports.MsgClaimVoucherResponse = exports.MsgClaimVoucher = exports.MsgUpdateActorRolesResponse = exports.MsgUpdateActorRoles = exports.MsgUpdateNamespaceResponse = exports.MsgUpdateNamespace_SetContractHook = exports.MsgUpdateNamespace = exports.MsgCreateNamespaceResponse = exports.MsgCreateNamespace = exports.MsgUpdateParamsResponse = exports.MsgUpdateParams = exports.protobufPackage = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var params_1 = require("./params.js");
var permissions_1 = require("./permissions.js");
exports.protobufPackage = "injective.permissions.v1beta1";
function createBaseMsgUpdateParams() {
    return { authority: "", params: undefined };
}
exports.MsgUpdateParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.authority !== "") {
            writer.uint32(10).string(message.authority);
        }
        if (message.params !== undefined) {
            params_1.Params.encode(message.params, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.authority = reader.string();
                    break;
                case 2:
                    message.params = params_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            authority: isSet(object.authority) ? String(object.authority) : "",
            params: isSet(object.params) ? params_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.authority !== undefined && (obj.authority = message.authority);
        message.params !== undefined && (obj.params = message.params ? params_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgUpdateParams();
        message.authority = (_a = object.authority) !== null && _a !== void 0 ? _a : "";
        message.params = (object.params !== undefined && object.params !== null)
            ? params_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseMsgUpdateParamsResponse() {
    return {};
}
exports.MsgUpdateParamsResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateParamsResponse();
        return message;
    },
};
function createBaseMsgCreateNamespace() {
    return { sender: "", namespace: undefined };
}
exports.MsgCreateNamespace = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.namespace !== undefined) {
            permissions_1.Namespace.encode(message.namespace, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateNamespace();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.namespace = permissions_1.Namespace.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            namespace: isSet(object.namespace) ? permissions_1.Namespace.fromJSON(object.namespace) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.namespace !== undefined &&
            (obj.namespace = message.namespace ? permissions_1.Namespace.toJSON(message.namespace) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateNamespace.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgCreateNamespace();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.namespace = (object.namespace !== undefined && object.namespace !== null)
            ? permissions_1.Namespace.fromPartial(object.namespace)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateNamespaceResponse() {
    return {};
}
exports.MsgCreateNamespaceResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgCreateNamespaceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgCreateNamespaceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgCreateNamespaceResponse();
        return message;
    },
};
function createBaseMsgUpdateNamespace() {
    return {
        sender: "",
        denom: "",
        contractHook: undefined,
        rolePermissions: [],
        roleManagers: [],
        policyStatuses: [],
        policyManagerCapabilities: [],
    };
}
exports.MsgUpdateNamespace = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.contractHook !== undefined) {
            exports.MsgUpdateNamespace_SetContractHook.encode(message.contractHook, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.rolePermissions), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                permissions_1.Role.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _g = __values(message.roleManagers), _h = _g.next(); !_h.done; _h = _g.next()) {
                var v = _h.value;
                permissions_1.RoleManager.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _j = __values(message.policyStatuses), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                permissions_1.PolicyStatus.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _l = __values(message.policyManagerCapabilities), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                permissions_1.PolicyManagerCapability.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateNamespace();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.contractHook = exports.MsgUpdateNamespace_SetContractHook.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.rolePermissions.push(permissions_1.Role.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleManagers.push(permissions_1.RoleManager.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.policyStatuses.push(permissions_1.PolicyStatus.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.policyManagerCapabilities.push(permissions_1.PolicyManagerCapability.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            contractHook: isSet(object.contractHook)
                ? exports.MsgUpdateNamespace_SetContractHook.fromJSON(object.contractHook)
                : undefined,
            rolePermissions: Array.isArray(object === null || object === void 0 ? void 0 : object.rolePermissions)
                ? object.rolePermissions.map(function (e) { return permissions_1.Role.fromJSON(e); })
                : [],
            roleManagers: Array.isArray(object === null || object === void 0 ? void 0 : object.roleManagers)
                ? object.roleManagers.map(function (e) { return permissions_1.RoleManager.fromJSON(e); })
                : [],
            policyStatuses: Array.isArray(object === null || object === void 0 ? void 0 : object.policyStatuses)
                ? object.policyStatuses.map(function (e) { return permissions_1.PolicyStatus.fromJSON(e); })
                : [],
            policyManagerCapabilities: Array.isArray(object === null || object === void 0 ? void 0 : object.policyManagerCapabilities)
                ? object.policyManagerCapabilities.map(function (e) { return permissions_1.PolicyManagerCapability.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        message.contractHook !== undefined && (obj.contractHook = message.contractHook
            ? exports.MsgUpdateNamespace_SetContractHook.toJSON(message.contractHook)
            : undefined);
        if (message.rolePermissions) {
            obj.rolePermissions = message.rolePermissions.map(function (e) { return e ? permissions_1.Role.toJSON(e) : undefined; });
        }
        else {
            obj.rolePermissions = [];
        }
        if (message.roleManagers) {
            obj.roleManagers = message.roleManagers.map(function (e) { return e ? permissions_1.RoleManager.toJSON(e) : undefined; });
        }
        else {
            obj.roleManagers = [];
        }
        if (message.policyStatuses) {
            obj.policyStatuses = message.policyStatuses.map(function (e) { return e ? permissions_1.PolicyStatus.toJSON(e) : undefined; });
        }
        else {
            obj.policyStatuses = [];
        }
        if (message.policyManagerCapabilities) {
            obj.policyManagerCapabilities = message.policyManagerCapabilities.map(function (e) {
                return e ? permissions_1.PolicyManagerCapability.toJSON(e) : undefined;
            });
        }
        else {
            obj.policyManagerCapabilities = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateNamespace.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseMsgUpdateNamespace();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.contractHook = (object.contractHook !== undefined && object.contractHook !== null)
            ? exports.MsgUpdateNamespace_SetContractHook.fromPartial(object.contractHook)
            : undefined;
        message.rolePermissions = ((_c = object.rolePermissions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return permissions_1.Role.fromPartial(e); })) || [];
        message.roleManagers = ((_d = object.roleManagers) === null || _d === void 0 ? void 0 : _d.map(function (e) { return permissions_1.RoleManager.fromPartial(e); })) || [];
        message.policyStatuses = ((_e = object.policyStatuses) === null || _e === void 0 ? void 0 : _e.map(function (e) { return permissions_1.PolicyStatus.fromPartial(e); })) || [];
        message.policyManagerCapabilities =
            ((_f = object.policyManagerCapabilities) === null || _f === void 0 ? void 0 : _f.map(function (e) { return permissions_1.PolicyManagerCapability.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgUpdateNamespace_SetContractHook() {
    return { newValue: "" };
}
exports.MsgUpdateNamespace_SetContractHook = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.newValue !== "") {
            writer.uint32(10).string(message.newValue);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateNamespace_SetContractHook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.newValue = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { newValue: isSet(object.newValue) ? String(object.newValue) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.newValue !== undefined && (obj.newValue = message.newValue);
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateNamespace_SetContractHook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMsgUpdateNamespace_SetContractHook();
        message.newValue = (_a = object.newValue) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseMsgUpdateNamespaceResponse() {
    return {};
}
exports.MsgUpdateNamespaceResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateNamespaceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateNamespaceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateNamespaceResponse();
        return message;
    },
};
function createBaseMsgUpdateActorRoles() {
    return { sender: "", denom: "", roleActorsToAdd: [], roleActorsToRevoke: [] };
}
exports.MsgUpdateActorRoles = {
    encode: function (message, writer) {
        var e_5, _a, e_6, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        try {
            for (var _c = __values(message.roleActorsToAdd), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                permissions_1.RoleActors.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _e = __values(message.roleActorsToRevoke), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                permissions_1.RoleActors.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateActorRoles();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.roleActorsToAdd.push(permissions_1.RoleActors.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleActorsToRevoke.push(permissions_1.RoleActors.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            roleActorsToAdd: Array.isArray(object === null || object === void 0 ? void 0 : object.roleActorsToAdd)
                ? object.roleActorsToAdd.map(function (e) { return permissions_1.RoleActors.fromJSON(e); })
                : [],
            roleActorsToRevoke: Array.isArray(object === null || object === void 0 ? void 0 : object.roleActorsToRevoke)
                ? object.roleActorsToRevoke.map(function (e) { return permissions_1.RoleActors.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        if (message.roleActorsToAdd) {
            obj.roleActorsToAdd = message.roleActorsToAdd.map(function (e) { return e ? permissions_1.RoleActors.toJSON(e) : undefined; });
        }
        else {
            obj.roleActorsToAdd = [];
        }
        if (message.roleActorsToRevoke) {
            obj.roleActorsToRevoke = message.roleActorsToRevoke.map(function (e) { return e ? permissions_1.RoleActors.toJSON(e) : undefined; });
        }
        else {
            obj.roleActorsToRevoke = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateActorRoles.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMsgUpdateActorRoles();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.roleActorsToAdd = ((_c = object.roleActorsToAdd) === null || _c === void 0 ? void 0 : _c.map(function (e) { return permissions_1.RoleActors.fromPartial(e); })) || [];
        message.roleActorsToRevoke = ((_d = object.roleActorsToRevoke) === null || _d === void 0 ? void 0 : _d.map(function (e) { return permissions_1.RoleActors.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMsgUpdateActorRolesResponse() {
    return {};
}
exports.MsgUpdateActorRolesResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgUpdateActorRolesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgUpdateActorRolesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgUpdateActorRolesResponse();
        return message;
    },
};
function createBaseMsgClaimVoucher() {
    return { sender: "", denom: "" };
}
exports.MsgClaimVoucher = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgClaimVoucher();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.MsgClaimVoucher.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMsgClaimVoucher();
        message.sender = (_a = object.sender) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseMsgClaimVoucherResponse() {
    return {};
}
exports.MsgClaimVoucherResponse = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMsgClaimVoucherResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MsgClaimVoucherResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMsgClaimVoucherResponse();
        return message;
    },
};
var MsgClientImpl = /** @class */ (function () {
    function MsgClientImpl(rpc) {
        this.rpc = rpc;
        this.UpdateParams = this.UpdateParams.bind(this);
        this.CreateNamespace = this.CreateNamespace.bind(this);
        this.UpdateNamespace = this.UpdateNamespace.bind(this);
        this.UpdateActorRoles = this.UpdateActorRoles.bind(this);
        this.ClaimVoucher = this.ClaimVoucher.bind(this);
    }
    MsgClientImpl.prototype.UpdateParams = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateParamsDesc, exports.MsgUpdateParams.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.CreateNamespace = function (request, metadata) {
        return this.rpc.unary(exports.MsgCreateNamespaceDesc, exports.MsgCreateNamespace.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateNamespace = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateNamespaceDesc, exports.MsgUpdateNamespace.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.UpdateActorRoles = function (request, metadata) {
        return this.rpc.unary(exports.MsgUpdateActorRolesDesc, exports.MsgUpdateActorRoles.fromPartial(request), metadata);
    };
    MsgClientImpl.prototype.ClaimVoucher = function (request, metadata) {
        return this.rpc.unary(exports.MsgClaimVoucherDesc, exports.MsgClaimVoucher.fromPartial(request), metadata);
    };
    return MsgClientImpl;
}());
exports.MsgClientImpl = MsgClientImpl;
exports.MsgDesc = { serviceName: "injective.permissions.v1beta1.Msg" };
exports.MsgUpdateParamsDesc = {
    methodName: "UpdateParams",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateParams.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgCreateNamespaceDesc = {
    methodName: "CreateNamespace",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgCreateNamespace.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgCreateNamespaceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateNamespaceDesc = {
    methodName: "UpdateNamespace",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateNamespace.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateNamespaceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgUpdateActorRolesDesc = {
    methodName: "UpdateActorRoles",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgUpdateActorRoles.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgUpdateActorRolesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.MsgClaimVoucherDesc = {
    methodName: "ClaimVoucher",
    service: exports.MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MsgClaimVoucher.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MsgClaimVoucherResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
