{"version": 3, "file": "proofs.js", "sourceRoot": "", "sources": ["../src/proofs.ts"], "names": [], "mappings": ";;;AAAA,qDAA8C;AAC9C,+BAA8C;AAC9C,mCAMiB;AAEJ,QAAA,QAAQ,GAAqB;IACxC,QAAQ,EAAE;QACR,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,YAAY,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACjC,UAAU,EAAE,iBAAK,CAAC,MAAM,CAAC,OAAO;QAChC,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;KACjC;IACD,SAAS,EAAE;QACT,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,EAAE;QACnB,SAAS,EAAE,EAAE;QACb,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;KAC1B;CACF,CAAC;AAEW,QAAA,cAAc,GAAqB;IAC9C,QAAQ,EAAE;QACR,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,YAAY,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACjC,UAAU,EAAE,iBAAK,CAAC,MAAM,CAAC,OAAO;QAChC,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;KACjC;IACD,SAAS,EAAE;QACT,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,SAAS,EAAE,EAAE;QACb,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;KAC1B;CACF,CAAC;AAEW,QAAA,OAAO,GAAqB;IACvC,QAAQ,EAAE;QACR,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,UAAU,EAAE,iBAAK,CAAC,MAAM,CAAC,OAAO;QAChC,YAAY,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACjC,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;QAChC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KAC7B;IACD,SAAS,EAAE;QACT,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,SAAS,EAAE,EAAE;QACb,eAAe,EAAE,CAAC;QAClB,eAAe,EAAE,CAAC;QAClB,UAAU,EAAE,IAAI,UAAU,CAAC,EAAE,CAAC;QAC9B,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;KAC1B;IACD,QAAQ,EAAE,GAAG;CACd,CAAC;AAIF,mFAAmF;AACnF,oCAAoC;AACpC,SAAgB,eAAe,CAC7B,KAA4B,EAC5B,IAAsB,EACtB,IAAoB,EACpB,GAAe,EACf,KAAiB;IAEjB,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACxB,MAAM,IAAI,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IAC3C,IAAA,wBAAgB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,IAAA,wBAAgB,EAAC,GAAG,EAAE,KAAK,CAAC,GAAI,CAAC,CAAC;IAClC,IAAA,wBAAgB,EAAC,KAAK,EAAE,KAAK,CAAC,KAAM,CAAC,CAAC;AACxC,CAAC;AAZD,0CAYC;AAED,6EAA6E;AAC7E,+DAA+D;AAC/D,yCAAyC;AACzC,SAAgB,kBAAkB,CAChC,KAA+B,EAC/B,IAAsB,EACtB,IAAoB,EACpB,GAAe;IAEf,IAAI,OAA+B,CAAC;IACpC,IAAI,QAAgC,CAAC;IAErC,IAAI,KAAK,CAAC,IAAI,EAAE;QACd,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAM,CAAC,CAAC;QAC5E,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,GAAI,CAAC;KAC3B;IACD,IAAI,KAAK,CAAC,KAAK,EAAE;QACf,eAAe,CACb,KAAK,CAAC,KAAK,EACX,IAAI,EACJ,IAAI,EACJ,KAAK,CAAC,KAAK,CAAC,GAAI,EAChB,KAAK,CAAC,KAAK,CAAC,KAAM,CACnB,CAAC;QACF,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,GAAI,CAAC;KAC7B;IAED,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IAED,IAAI,OAAO,EAAE;QACX,IAAA,yBAAiB,EAAC,OAAO,EAAE,GAAG,CAAC,CAAC;KACjC;IACD,IAAI,QAAQ,EAAE;QACZ,IAAA,yBAAiB,EAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;KAClC;IAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;IACD,IAAI,CAAC,OAAO,EAAE;QACZ,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,KAAM,CAAC,IAAK,CAAC,CAAC;KACpD;SAAM,IAAI,CAAC,QAAQ,EAAE;QACpB,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAK,CAAC,IAAK,CAAC,CAAC;KACpD;SAAM;QACL,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAK,CAAC,IAAK,EAAE,KAAK,CAAC,KAAM,CAAC,IAAK,CAAC,CAAC;KAC3E;IACD,OAAO;AACT,CAAC;AA9CD,gDA8CC;AAED,mEAAmE;AACnE,6DAA6D;AAC7D,yDAAyD;AACzD,SAAgB,sBAAsB,CACpC,KAA4B;IAE5B,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC5D;IACD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;IACD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;IAE9B,IAAI,GAAG,GAAG,IAAA,eAAS,EAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IACxD,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;QACxB,GAAG,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC9B;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAhBD,wDAgBC;AAED,2DAA2D;AAC3D,SAAgB,UAAU,CACxB,KAA4B,EAC5B,IAAsB;IAEtB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACf,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;IACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAClB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;IACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IACD,IAAA,kBAAU,EAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEtC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;QAChD,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;KACvD;IACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;QAChD,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;KACxD;IACD,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;QACxB,IAAA,mBAAW,EAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;KAC1D;AACH,CAAC;AAzBD,gCAyBC;AAED,SAAS,cAAc,CACrB,IAAsB,EACtB,IAA+B;IAE/B,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAE7D,mEAAmE;IACnE,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;YACnD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;KACF;AACH,CAAC;AAED,SAAS,eAAe,CACtB,IAAsB,EACtB,IAA+B;IAE/B,MAAM,GAAG,GAAG,IAAI,CAAC,UAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAE/D,mEAAmE;IACnE,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;YACnD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACtC;KACF;AACH,CAAC;AAED,SAAgB,kBAAkB,CAChC,IAAsB,EACtB,IAA+B,EAC/B,KAAgC;IAEhC,MAAM,OAAO,GAAqB,CAAC,GAAG,IAAI,CAAC,CAAC;IAC5C,MAAM,QAAQ,GAAqB,CAAC,GAAG,KAAK,CAAC,CAAC;IAE9C,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,EAAG,CAAC;IAC7B,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAC;IAC/B,OACE,IAAA,kBAAU,EAAC,OAAO,CAAC,MAAO,EAAE,QAAQ,CAAC,MAAO,CAAC;QAC7C,IAAA,kBAAU,EAAC,OAAO,CAAC,MAAO,EAAE,QAAQ,CAAC,MAAO,CAAC,EAC7C;QACA,OAAO,GAAG,OAAO,CAAC,GAAG,EAAG,CAAC;QACzB,QAAQ,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAC;KAC5B;IAED,yDAAyD;IACzD,kDAAkD;IAClD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;QACxC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IAED,yEAAyE;IACzE,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC/B,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACjC,CAAC;AA3BD,gDA2BC;AAED,wDAAwD;AACxD,0DAA0D;AAC1D,SAAS,UAAU,CACjB,IAAsB,EACtB,IAAoB,EACpB,KAAqB;IAErB,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7C,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC/C,OAAO,QAAQ,KAAK,OAAO,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAsB,EACtB,KAAqB;IAErB,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,UAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;QAC/D,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClE,IAAI,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;YACnD,OAAO,MAAM,CAAC;SACf;KACF;IACD,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,UAAU,CACjB,EAAkB,EAClB,SAAiB,EACjB,SAAiB,EACjB,MAAc;IAEd,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,SAAS,EAAE;QACxC,OAAO,KAAK,CAAC;KACd;IACD,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,SAAS,EAAE;QACxC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC;AAC7C,CAAC;AAOD,SAAS,UAAU,CAAC,IAAsB,EAAE,MAAc;IACxD,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,UAAW,EAAE,MAAM,CAAC,CAAC;IAElD,4CAA4C;IAC5C,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,SAAU,CAAC;IACrC,MAAM,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,eAAgB,CAAC;IACjD,MAAM,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,eAAgB,CAAC;IAEjD,4CAA4C;IAC5C,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,UAAW,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC;IACrE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;AAC1C,CAAC;AAED,SAAS,WAAW,CAAC,KAAwB,EAAE,MAAc;IAC3D,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;QACxC,MAAM,IAAI,KAAK,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AAClD,CAAC"}