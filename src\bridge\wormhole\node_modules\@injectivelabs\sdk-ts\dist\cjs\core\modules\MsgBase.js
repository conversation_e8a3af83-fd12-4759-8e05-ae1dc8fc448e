"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MsgBase = void 0;
const maps_js_1 = require("../tx/eip712/maps.js");
const utils_js_1 = require("./utils.js");
/**
 * @category Messages
 */
class MsgBase {
    params;
    constructor(params) {
        this.params = params;
    }
    /** @deprecated - use toWeb3Gw instead, renamed for clarity */
    toWeb3() {
        return this.toWeb3Gw();
    }
    toJSON() {
        return JSON.stringify((0, utils_js_1.prepareSignBytes)(this.toData()));
    }
    /**
     * Returns the types of the message for EIP712
     */
    toEip712Types() {
        const amino = this.toAmino();
        return (0, maps_js_1.objectKeysToEip712Types)({
            object: amino.value,
            messageType: amino.type,
        });
    }
    /**
     * Returns the values of the message for EIP712
     */
    toEip712() {
        return this.toAmino();
    }
    /**
     * Returns the values of the message for EIP712_V2
     */
    toEip712V2() {
        return this.toWeb3Gw();
    }
    toDirectSignJSON() {
        return JSON.stringify((0, utils_js_1.prepareSignBytes)(this.toDirectSign()));
    }
}
exports.MsgBase = MsgBase;
