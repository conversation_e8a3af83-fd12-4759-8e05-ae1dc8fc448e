"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Module = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.bank.module.v1";
function createBaseModule() {
    return { blockedModuleAccountsOverride: [], authority: "", restrictionsOrder: [] };
}
exports.Module = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.blockedModuleAccountsOverride), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.authority !== "") {
            writer.uint32(18).string(message.authority);
        }
        try {
            for (var _e = __values(message.restrictionsOrder), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockedModuleAccountsOverride.push(reader.string());
                    break;
                case 2:
                    message.authority = reader.string();
                    break;
                case 3:
                    message.restrictionsOrder.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            blockedModuleAccountsOverride: Array.isArray(object === null || object === void 0 ? void 0 : object.blockedModuleAccountsOverride)
                ? object.blockedModuleAccountsOverride.map(function (e) { return String(e); })
                : [],
            authority: isSet(object.authority) ? String(object.authority) : "",
            restrictionsOrder: Array.isArray(object === null || object === void 0 ? void 0 : object.restrictionsOrder)
                ? object.restrictionsOrder.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.blockedModuleAccountsOverride) {
            obj.blockedModuleAccountsOverride = message.blockedModuleAccountsOverride.map(function (e) { return e; });
        }
        else {
            obj.blockedModuleAccountsOverride = [];
        }
        message.authority !== undefined && (obj.authority = message.authority);
        if (message.restrictionsOrder) {
            obj.restrictionsOrder = message.restrictionsOrder.map(function (e) { return e; });
        }
        else {
            obj.restrictionsOrder = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Module.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseModule();
        message.blockedModuleAccountsOverride = ((_a = object.blockedModuleAccountsOverride) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.authority = (_b = object.authority) !== null && _b !== void 0 ? _b : "";
        message.restrictionsOrder = ((_c = object.restrictionsOrder) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
