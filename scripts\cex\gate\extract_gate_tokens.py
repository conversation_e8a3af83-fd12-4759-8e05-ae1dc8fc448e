#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
from pathlib import Path

def main():
    """
    从Gate交易所信息中提取特定网络的代币信息
    提取ETH、MATIC、BSC和BASEEVM网络的代币symbol和contract_address
    将提取的信息保存到data/utils/token目录下
    """
    # 定义输入和输出路径
    input_path = "data/cex/gate_info/network_groups.json"
    output_dir = "data/utils/token"
    output_path = os.path.join(output_dir, "gate_tokens.json")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义需要提取的网络
    target_networks = ["ETH", "MATIC", "BSC", "BASEEVM"]
    
    print(f"正在从 {input_path} 提取代币信息...")
    
    # 读取源数据
    with open(input_path, "r", encoding="utf-8") as f:
        network_groups = json.load(f)
    
    # 初始化结果字典
    result = {}
    
    # 遍历每个目标网络，提取代币信息
    for network in target_networks:
        if network not in network_groups:
            print(f"警告: 找不到网络 {network}")
            continue
            
        network_tokens = []
        
        for token_data in network_groups[network]:
            # 从币对获取基础代币符号
            symbol = token_data["currency"]
            
            # 获取合约地址
            if "info" in token_data and "contract_address" in token_data["info"]:
                contract_address = token_data["info"]["contract_address"]
                if contract_address:  # 只添加有合约地址的代币
                    network_tokens.append({
                        "symbol": symbol,
                        "contract_address": contract_address
                    })
        
        # 将当前网络的代币信息添加到结果字典
        result[network] = network_tokens
        print(f"已提取 {network} 网络的 {len(network_tokens)} 个代币信息")
    
    # 将结果保存到文件
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"提取完成! 数据已保存到 {output_path}")

if __name__ == "__main__":
    main() 