import { AccountAddress, EthereumChainId } from '@injectivelabs/ts-types';
import { InjectiveExchangeRpc } from '@injectivelabs/indexer-proto-ts';
import { IndexerGrpcTransactionApi } from './IndexerGrpcTransactionApi.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcWeb3GwApi extends IndexerGrpcTransactionApi {
    protected module: string;
    constructor(endpoint: string);
    prepareEip712Request({ address, chainId, message, memo, sequence, accountNumber, estimateGas, gasLimit, feeDenom, feePrice, timeoutHeight, eip712Version, }: {
        address: AccountAddress;
        chainId: EthereumChainId;
        message: any;
        estimateGas?: boolean;
        gasLimit?: number;
        memo?: string | number;
        timeoutHeight?: number;
        feeDenom?: string;
        feePrice?: string;
        sequence?: number;
        accountNumber?: number;
        eip712Version?: string;
    }): Promise<InjectiveExchangeRpc.PrepareEip712Response>;
}
