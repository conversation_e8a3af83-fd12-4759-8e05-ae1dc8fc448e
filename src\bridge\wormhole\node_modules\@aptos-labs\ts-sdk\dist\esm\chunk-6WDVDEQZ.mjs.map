{"version": 3, "sources": ["../../src/errors/index.ts"], "sourcesContent": ["import { AptosApiType } from \"../utils/const\";\nimport { getErrorMessage } from \"../utils/helpers\";\nimport { AptosRequest, AptosResponse } from \"../types\";\n\nexport enum KeylessErrorCategory {\n  API_ERROR,\n  EXTERNAL_API_ERROR,\n  SESSION_EXPIRED,\n  INVALID_STATE,\n  INVALID_SIGNATURE,\n  UNKNOWN,\n}\n\nexport enum KeylessErrorResolutionTip {\n  REAUTHENTICATE = \"Re-authentiate to continue using your keyless account\",\n  // eslint-disable-next-line max-len\n  REAUTHENTICATE_UNSURE = \"Try re-authentiating. If the error persists join the telegram group at https://t.me/+h5CN-W35yUFiYzkx for further support\",\n  UPDATE_REQUEST_PARAMS = \"Update the invalid request parameters and reauthenticate.\",\n  // eslint-disable-next-line max-len\n  RATE_LIMIT_EXCEEDED = \"Cache the keyless account and reuse it to avoid making too many requests.  Keyless accounts are valid until either the EphemeralKeyPair expires, when the JWK is rotated, or when the proof verifying key is changed, whichever comes soonest.\",\n  // eslint-disable-next-line max-len\n  SERVER_ERROR = \"Try again later.  See aptosApiError error for more context. For additional support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx\",\n  // eslint-disable-next-line max-len\n  CALL_PRECHECK = \"Call `await account.checkKeylessAccountValidity()` to wait for asyncronous changes and check for account validity before signing or serializing.\",\n  REINSTANTIATE = \"Try instantiating the account again.  Avoid manipulating the account object directly\",\n  JOIN_SUPPORT_GROUP = \"For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx\",\n  UNKNOWN = \"Error unknown. For support join the telegram group at https://t.me/+h5CN-W35yUFiYzkx\",\n}\n\nexport enum KeylessErrorType {\n  EPHEMERAL_KEY_PAIR_EXPIRED,\n\n  PROOF_NOT_FOUND,\n\n  ASYNC_PROOF_FETCH_FAILED,\n\n  INVALID_PROOF_VERIFICATION_FAILED,\n\n  INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND,\n\n  INVALID_JWT_SIG,\n\n  INVALID_JWT_JWK_NOT_FOUND,\n\n  INVALID_JWT_ISS_NOT_RECOGNIZED,\n\n  INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED,\n\n  INVALID_TW_SIG_VERIFICATION_FAILED,\n\n  INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND,\n\n  INVALID_EXPIRY_HORIZON,\n\n  JWT_PARSING_ERROR,\n\n  JWK_FETCH_FAILED,\n\n  JWK_FETCH_FAILED_FEDERATED,\n\n  RATE_LIMIT_EXCEEDED,\n\n  PEPPER_SERVICE_INTERNAL_ERROR,\n\n  PEPPER_SERVICE_BAD_REQUEST,\n\n  PEPPER_SERVICE_OTHER,\n\n  PROVER_SERVICE_INTERNAL_ERROR,\n\n  PROVER_SERVICE_BAD_REQUEST,\n\n  PROVER_SERVICE_OTHER,\n\n  FULL_NODE_CONFIG_LOOKUP_ERROR,\n\n  FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR,\n\n  FULL_NODE_JWKS_LOOKUP_ERROR,\n\n  FULL_NODE_OTHER,\n\n  SIGNATURE_TYPE_INVALID,\n\n  SIGNATURE_EXPIRED,\n\n  MAX_EXPIRY_HORIZON_EXCEEDED,\n\n  EPHEMERAL_SIGNATURE_VERIFICATION_FAILED,\n\n  TRAINING_WHEELS_SIGNATURE_MISSING,\n\n  TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED,\n\n  PROOF_VERIFICATION_FAILED,\n\n  UNKNOWN,\n}\n\nconst KeylessErrors: { [key in KeylessErrorType]: [string, KeylessErrorCategory, KeylessErrorResolutionTip] } = {\n  [KeylessErrorType.EPHEMERAL_KEY_PAIR_EXPIRED]: [\n    \"The ephemeral keypair has expired.\",\n    KeylessErrorCategory.SESSION_EXPIRED,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.PROOF_NOT_FOUND]: [\n    \"The required proof could not be found.\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.CALL_PRECHECK,\n  ],\n  [KeylessErrorType.ASYNC_PROOF_FETCH_FAILED]: [\n    \"The required proof failed to fetch.\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.REAUTHENTICATE_UNSURE,\n  ],\n  [KeylessErrorType.INVALID_PROOF_VERIFICATION_FAILED]: [\n    \"The provided proof is invalid.\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.REAUTHENTICATE_UNSURE,\n  ],\n  [KeylessErrorType.INVALID_PROOF_VERIFICATION_KEY_NOT_FOUND]: [\n    \"The verification key used to authenticate was updated.\",\n    KeylessErrorCategory.SESSION_EXPIRED,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.INVALID_JWT_SIG]: [\n    \"The JWK was found, but JWT failed verification\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.REAUTHENTICATE_UNSURE,\n  ],\n  [KeylessErrorType.INVALID_JWT_JWK_NOT_FOUND]: [\n    \"The JWK required to verify the JWT could not be found. The JWK may have been rotated out.\",\n    KeylessErrorCategory.SESSION_EXPIRED,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.INVALID_JWT_ISS_NOT_RECOGNIZED]: [\n    \"The JWT issuer is not recognized.\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.UPDATE_REQUEST_PARAMS,\n  ],\n  [KeylessErrorType.INVALID_JWT_FEDERATED_ISS_NOT_SUPPORTED]: [\n    \"The JWT issuer is not supported by the Federated Keyless \",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.REAUTHENTICATE_UNSURE,\n  ],\n  [KeylessErrorType.INVALID_TW_SIG_VERIFICATION_FAILED]: [\n    \"The training wheels signature is invalid.\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.REAUTHENTICATE_UNSURE,\n  ],\n  [KeylessErrorType.INVALID_TW_SIG_PUBLIC_KEY_NOT_FOUND]: [\n    \"The public key used to verify the training wheels signature was not found.\",\n    KeylessErrorCategory.SESSION_EXPIRED,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.INVALID_EXPIRY_HORIZON]: [\n    \"The expiry horizon is invalid.\",\n    KeylessErrorCategory.SESSION_EXPIRED,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.JWK_FETCH_FAILED]: [\n    \"Failed to fetch JWKS.\",\n    KeylessErrorCategory.EXTERNAL_API_ERROR,\n    KeylessErrorResolutionTip.JOIN_SUPPORT_GROUP,\n  ],\n  [KeylessErrorType.JWK_FETCH_FAILED_FEDERATED]: [\n    \"Failed to fetch JWKS for Federated Keyless provider.\",\n    KeylessErrorCategory.EXTERNAL_API_ERROR,\n    KeylessErrorResolutionTip.JOIN_SUPPORT_GROUP,\n  ],\n  [KeylessErrorType.RATE_LIMIT_EXCEEDED]: [\n    \"Rate limit exceeded. Too many requests in a short period.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.RATE_LIMIT_EXCEEDED,\n  ],\n  [KeylessErrorType.PEPPER_SERVICE_INTERNAL_ERROR]: [\n    \"Internal error from Pepper service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.PEPPER_SERVICE_BAD_REQUEST]: [\n    \"Bad request sent to Pepper service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.UPDATE_REQUEST_PARAMS,\n  ],\n  [KeylessErrorType.PEPPER_SERVICE_OTHER]: [\n    \"Unknown error from Pepper service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.PROVER_SERVICE_INTERNAL_ERROR]: [\n    \"Internal error from Prover service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.PROVER_SERVICE_BAD_REQUEST]: [\n    \"Bad request sent to Prover service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.UPDATE_REQUEST_PARAMS,\n  ],\n  [KeylessErrorType.PROVER_SERVICE_OTHER]: [\n    \"Unknown error from Prover service.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.JWT_PARSING_ERROR]: [\n    \"Error when parsing JWT. This should never happen. Join https://t.me/+h5CN-W35yUFiYzkx for support\",\n    KeylessErrorCategory.INVALID_STATE,\n    KeylessErrorResolutionTip.REINSTANTIATE,\n  ],\n  [KeylessErrorType.FULL_NODE_CONFIG_LOOKUP_ERROR]: [\n    \"Error when looking up on-chain keyless configuration.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.FULL_NODE_VERIFICATION_KEY_LOOKUP_ERROR]: [\n    \"Error when looking up on-chain verification key.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.FULL_NODE_JWKS_LOOKUP_ERROR]: [\n    \"Error when looking up on-chain JWKS.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.FULL_NODE_OTHER]: [\n    \"Unknown error from full node.\",\n    KeylessErrorCategory.API_ERROR,\n    KeylessErrorResolutionTip.SERVER_ERROR,\n  ],\n  [KeylessErrorType.SIGNATURE_TYPE_INVALID]: [\n    \"The signature is not a valid Keyless signature.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.JOIN_SUPPORT_GROUP,\n  ],\n  [KeylessErrorType.SIGNATURE_EXPIRED]: [\n    \"The ephemeral key pair used to sign the message has expired.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.MAX_EXPIRY_HORIZON_EXCEEDED]: [\n    \"The expiry horizon on the signature exceeds the maximum allowed value.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.EPHEMERAL_SIGNATURE_VERIFICATION_FAILED]: [\n    \"Failed to verify the ephemeral signature with the ephemeral public key.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.TRAINING_WHEELS_SIGNATURE_MISSING]: [\n    \"The training wheels signature is missing but is required by the Keyless configuration.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.TRAINING_WHEELS_SIGNATURE_VERIFICATION_FAILED]: [\n    \"Failed to verify the training wheels signature with the training wheels public key.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.PROOF_VERIFICATION_FAILED]: [\n    \"The proof verification failed.\",\n    KeylessErrorCategory.INVALID_SIGNATURE,\n    KeylessErrorResolutionTip.REAUTHENTICATE,\n  ],\n  [KeylessErrorType.UNKNOWN]: [\n    \"An unknown error has occurred.\",\n    KeylessErrorCategory.UNKNOWN,\n    KeylessErrorResolutionTip.UNKNOWN,\n  ],\n};\n\nexport class KeylessError extends Error {\n  readonly innerError?: unknown;\n\n  readonly category: KeylessErrorCategory;\n\n  readonly resolutionTip: KeylessErrorResolutionTip;\n\n  readonly type: KeylessErrorType;\n\n  readonly details?: string;\n\n  /** @internal this constructor is for sdk internal use - do not instantiate outside of the SDK codebase */\n  constructor(args: {\n    innerError?: unknown;\n    category: KeylessErrorCategory;\n    resolutionTip: KeylessErrorResolutionTip;\n    type: KeylessErrorType;\n    message?: string;\n    details?: string;\n  }) {\n    const { innerError, category, resolutionTip, type, message = KeylessErrors[type][0], details } = args;\n    super(message);\n    this.name = \"KeylessError\";\n    this.innerError = innerError;\n    this.category = category;\n    this.resolutionTip = resolutionTip;\n    this.type = type;\n    this.details = details;\n    this.message = KeylessError.constructMessage(message, resolutionTip, innerError, details);\n  }\n\n  static constructMessage(\n    message: string,\n    tip: KeylessErrorResolutionTip,\n    innerError?: unknown,\n    details?: string,\n  ): string {\n    let result = `\\nMessage: ${message}`;\n    if (details) {\n      result += `\\nDetails: ${details}`;\n    }\n    if (innerError instanceof AptosApiError) {\n      result += `\\nAptosApiError: ${innerError.message}`;\n    } else if (innerError !== undefined) {\n      result += `\\nError: ${getErrorMessage(innerError)}`;\n    }\n    result += `\\nKeylessErrorResolutionTip: ${tip}`;\n    return result;\n  }\n\n  /**\n   * Static constructor that creates a KeylessError instance using the KeylessErrors constant\n   * @param args.type The type of KeylessError\n   * @param args.aptosApiError optional AptosApiError supplied for api errors\n   * @param args.details optional details to include in the error message\n   * @returns A new KeylessError instance\n   */\n  static fromErrorType(args: { type: KeylessErrorType; error?: unknown; details?: string }): KeylessError {\n    const { error, type, details } = args;\n\n    const [message, category, resolutionTip] = KeylessErrors[type];\n    return new KeylessError({\n      message,\n      details,\n      innerError: error,\n      category,\n      resolutionTip,\n      type,\n    });\n  }\n}\n\n/**\n * Options for handling errors in the Aptos API.\n */\ntype AptosApiErrorOpts = {\n  apiType: AptosApiType;\n  aptosRequest: AptosRequest;\n  aptosResponse: AptosResponse<any, any>;\n};\n\n/**\n * Represents an error returned from the Aptos API.\n * This class encapsulates the details of the error, including the request URL, response status, and additional data.\n *\n * @param name - The name of the error, which is always \"AptosApiError\".\n * @param url - The URL to which the request was made.\n * @param status - The HTTP response status code (e.g., 400).\n * @param statusText - The message associated with the response status.\n * @param data - The response data returned from the API.\n * @param request - The original AptosRequest that triggered the error.\n */\nexport class AptosApiError extends Error {\n  readonly url: string;\n\n  readonly status: number;\n\n  readonly statusText: string;\n\n  readonly data: any;\n\n  readonly request: AptosRequest;\n\n  /**\n   * Constructs an instance of AptosApiError with relevant error details.\n   *\n   * @param opts - The options for creating the AptosApiError.\n   * @param opts.apiType - The type of API that generated the error.\n   * @param opts.aptosRequest - The request object that caused the error.\n   * @param opts.aptosResponse - The response object containing error details.\n   *\n   * @internal This constructor is for SDK internal use - do not instantiate outside the SDK codebase.\n   */\n  constructor({ apiType, aptosRequest, aptosResponse }: AptosApiErrorOpts) {\n    super(deriveErrorMessage({ apiType, aptosRequest, aptosResponse }));\n\n    this.name = \"AptosApiError\";\n    this.url = aptosResponse.url;\n    this.status = aptosResponse.status;\n    this.statusText = aptosResponse.statusText;\n    this.data = aptosResponse.data;\n    this.request = aptosRequest;\n  }\n}\n\n/**\n * Derives an error message from the Aptos API response, providing context for debugging.\n * This function helps in understanding the nature of the error encountered during an API request.\n *\n * @param {AptosApiErrorOpts} opts - The options for deriving the error message.\n * @param {AptosApiType} opts.apiType - The type of API being called.\n * @param {AptosRequest} opts.aptosRequest - The original request made to the Aptos API.\n * @param {AptosResponse} opts.aptosResponse - The response received from the Aptos API.\n */\nfunction deriveErrorMessage({ apiType, aptosRequest, aptosResponse }: AptosApiErrorOpts): string {\n  // eslint-disable-next-line max-len\n  // extract the W3C trace_id from the response headers if it exists. Some services set this in the response, and it's useful for debugging.\n  // See https://www.w3.org/TR/trace-context/#relationship-between-the-headers .\n  const traceId = aptosResponse.headers?.traceparent?.split(\"-\")[1];\n  const traceIdString = traceId ? `(trace_id:${traceId}) ` : \"\";\n\n  const errorPrelude: string = `Request to [${apiType}]: ${aptosRequest.method} ${\n    aptosResponse.url ?? aptosRequest.url\n  } ${traceIdString}failed with`;\n\n  // handle graphql responses from indexer api and extract the error message of the first error\n  if (apiType === AptosApiType.INDEXER && aptosResponse.data?.errors?.[0]?.message != null) {\n    return `${errorPrelude}: ${aptosResponse.data.errors[0].message}`;\n  }\n\n  // Received well-known structured error response body - simply serialize and return it.\n  // We don't need http status codes etc. in this case.\n  if (aptosResponse.data?.message != null && aptosResponse.data?.error_code != null) {\n    return `${errorPrelude}: ${JSON.stringify(aptosResponse.data)}`;\n  }\n\n  // This is the generic/catch-all case. We received some response from the API, but it doesn't appear to be a well-known structure.\n  // We print http status codes and the response body (after some trimming),\n  // in the hope that this gives enough context what went wrong without printing overly huge messages.\n  return `${errorPrelude} status: ${aptosResponse.statusText}(code:${\n    aptosResponse.status\n  }) and response body: ${serializeAnyPayloadForErrorMessage(aptosResponse.data)}`;\n}\n\nconst SERIALIZED_PAYLOAD_TRIM_TO_MAX_LENGTH = 400;\n\n/**\n * This function accepts a payload of any type (probably an object) and serializes it to a string\n * Since we don't know the type or size of the payload, and we don't want to add a huge object in full to the error message\n * we limit the to the first 200 and last 200 characters of the serialized payload and put a \"...\" in the middle.\n * @param payload - The payload to serialize, which can be of any type.\n *\n * @returns A string representation of the serialized payload, potentially truncated.\n */\nfunction serializeAnyPayloadForErrorMessage(payload: any): string {\n  const serializedPayload = JSON.stringify(payload);\n  if (serializedPayload.length <= SERIALIZED_PAYLOAD_TRIM_TO_MAX_LENGTH) {\n    return serializedPayload;\n  }\n  return `truncated(original_size:${serializedPayload.length}): ${serializedPayload.slice(\n    0,\n    SERIALIZED_PAYLOAD_TRIM_TO_MAX_LENGTH / 2,\n  )}...${serializedPayload.slice(-SERIALIZED_PAYLOAD_TRIM_TO_MAX_LENGTH / 2)}`;\n}\n"], "mappings": "yCAIO,IAAKA,OACVA,IAAA,yBACAA,IAAA,2CACAA,IAAA,qCACAA,IAAA,iCACAA,IAAA,yCACAA,IAAA,qBANUA,OAAA,IASAC,OACVA,EAAA,eAAiB,wDAEjBA,EAAA,sBAAwB,4HACxBA,EAAA,sBAAwB,4DAExBA,EAAA,oBAAsB,iPAEtBA,EAAA,aAAe,+IAEfA,EAAA,cAAgB,mJAChBA,EAAA,cAAgB,uFAChBA,EAAA,mBAAqB,wEACrBA,EAAA,QAAU,uFAbAA,OAAA,IAgBAC,OACVA,IAAA,2DAEAA,IAAA,qCAEAA,IAAA,uDAEAA,IAAA,yEAEAA,IAAA,uFAEAA,IAAA,qCAEAA,IAAA,yDAEAA,IAAA,mEAEAA,IAAA,qFAEAA,IAAA,2EAEAA,IAAA,8EAEAA,IAAA,oDAEAA,IAAA,0CAEAA,IAAA,wCAEAA,IAAA,4DAEAA,IAAA,8CAEAA,IAAA,kEAEAA,IAAA,4DAEAA,IAAA,gDAEAA,IAAA,kEAEAA,IAAA,4DAEAA,IAAA,gDAEAA,IAAA,kEAEAA,IAAA,sFAEAA,IAAA,8DAEAA,IAAA,sCAEAA,IAAA,oDAEAA,IAAA,0CAEAA,IAAA,8DAEAA,IAAA,sFAEAA,IAAA,0EAEAA,IAAA,kGAEAA,IAAA,0DAEAA,IAAA,sBAnEUA,OAAA,IAsENC,EAA0G,CAC7G,EAA8C,CAC7C,qCACA,EACA,uDACF,EACC,EAAmC,CAClC,yCACA,EACA,kJACF,EACC,EAA4C,CAC3C,sCACA,EACA,2HACF,EACC,EAAqD,CACpD,iCACA,EACA,2HACF,EACC,EAA4D,CAC3D,yDACA,EACA,uDACF,EACC,EAAmC,CAClC,iDACA,EACA,2HACF,EACC,EAA6C,CAC5C,4FACA,EACA,uDACF,EACC,EAAkD,CACjD,oCACA,EACA,2DACF,EACC,EAA2D,CAC1D,4DACA,EACA,2HACF,EACC,EAAsD,CACrD,4CACA,EACA,2HACF,EACC,GAAuD,CACtD,6EACA,EACA,uDACF,EACC,GAA0C,CACzC,iCACA,EACA,uDACF,EACC,GAAoC,CACnC,wBACA,EACA,uEACF,EACC,GAA8C,CAC7C,uDACA,EACA,uEACF,EACC,GAAuC,CACtC,4DACA,EACA,gPACF,EACC,GAAiD,CAChD,sCACA,EACA,8IACF,EACC,GAA8C,CAC7C,sCACA,EACA,2DACF,EACC,GAAwC,CACvC,qCACA,EACA,8IACF,EACC,GAAiD,CAChD,sCACA,EACA,8IACF,EACC,GAA8C,CAC7C,sCACA,EACA,2DACF,EACC,GAAwC,CACvC,qCACA,EACA,8IACF,EACC,GAAqC,CACpC,oGACA,EACA,sFACF,EACC,GAAiD,CAChD,wDACA,EACA,8IACF,EACC,GAA2D,CAC1D,mDACA,EACA,8IACF,EACC,GAA+C,CAC9C,uCACA,EACA,8IACF,EACC,GAAmC,CAClC,gCACA,EACA,8IACF,EACC,GAA0C,CACzC,kDACA,EACA,uEACF,EACC,GAAqC,CACpC,+DACA,EACA,uDACF,EACC,GAA+C,CAC9C,yEACA,EACA,uDACF,EACC,GAA2D,CAC1D,0EACA,EACA,uDACF,EACC,GAAqD,CACpD,yFACA,EACA,uDACF,EACC,GAAiE,CAChE,sFACA,EACA,uDACF,EACC,GAA6C,CAC5C,iCACA,EACA,uDACF,EACC,GAA2B,CAC1B,iCACA,EACA,sFACF,CACF,EAEaC,EAAN,MAAMC,UAAqB,KAAM,CAYtC,YAAYC,EAOT,CACD,GAAM,CAAE,WAAAC,EAAY,SAAAC,EAAU,cAAAC,EAAe,KAAAC,EAAM,QAAAC,EAAUR,EAAcO,CAAI,EAAE,CAAC,EAAG,QAAAE,CAAQ,EAAIN,EACjG,MAAMK,CAAO,EACb,KAAK,KAAO,eACZ,KAAK,WAAaJ,EAClB,KAAK,SAAWC,EAChB,KAAK,cAAgBC,EACrB,KAAK,KAAOC,EACZ,KAAK,QAAUE,EACf,KAAK,QAAUP,EAAa,iBAAiBM,EAASF,EAAeF,EAAYK,CAAO,CAC1F,CAEA,OAAO,iBACLD,EACAE,EACAN,EACAK,EACQ,CACR,IAAIE,EAAS;AAAA,WAAcH,CAAO,GAClC,OAAIC,IACFE,GAAU;AAAA,WAAcF,CAAO,IAE7BL,aAAsBQ,EACxBD,GAAU;AAAA,iBAAoBP,EAAW,OAAO,GACvCA,IAAe,SACxBO,GAAU;AAAA,SAAYE,EAAgBT,CAAU,CAAC,IAEnDO,GAAU;AAAA,6BAAgCD,CAAG,GACtCC,CACT,CASA,OAAO,cAAcR,EAAmF,CACtG,GAAM,CAAE,MAAAW,EAAO,KAAAP,EAAM,QAAAE,CAAQ,EAAIN,EAE3B,CAACK,EAASH,EAAUC,CAAa,EAAIN,EAAcO,CAAI,EAC7D,OAAO,IAAIL,EAAa,CACtB,QAAAM,EACA,QAAAC,EACA,WAAYK,EACZ,SAAAT,EACA,cAAAC,EACA,KAAAC,CACF,CAAC,CACH,CACF,EAsBaK,EAAN,cAA4B,KAAM,CAqBvC,YAAY,CAAE,QAAAG,EAAS,aAAAC,EAAc,cAAAC,CAAc,EAAsB,CACvE,MAAMC,EAAmB,CAAE,QAAAH,EAAS,aAAAC,EAAc,cAAAC,CAAc,CAAC,CAAC,EAElE,KAAK,KAAO,gBACZ,KAAK,IAAMA,EAAc,IACzB,KAAK,OAASA,EAAc,OAC5B,KAAK,WAAaA,EAAc,WAChC,KAAK,KAAOA,EAAc,KAC1B,KAAK,QAAUD,CACjB,CACF,EAWA,SAASE,EAAmB,CAAE,QAAAH,EAAS,aAAAC,EAAc,cAAAC,CAAc,EAA8B,CAI/F,IAAME,EAAUF,EAAc,SAAS,aAAa,MAAM,GAAG,EAAE,CAAC,EAC1DG,EAAgBD,EAAU,aAAaA,CAAO,KAAO,GAErDE,EAAuB,eAAeN,CAAO,MAAMC,EAAa,MAAM,IAC1EC,EAAc,KAAOD,EAAa,GACpC,IAAII,CAAa,cAGjB,OAAIL,IAAY,WAAwBE,EAAc,MAAM,SAAS,CAAC,GAAG,SAAW,KAC3E,GAAGI,CAAY,KAAKJ,EAAc,KAAK,OAAO,CAAC,EAAE,OAAO,GAK7DA,EAAc,MAAM,SAAW,MAAQA,EAAc,MAAM,YAAc,KACpE,GAAGI,CAAY,KAAK,KAAK,UAAUJ,EAAc,IAAI,CAAC,GAMxD,GAAGI,CAAY,YAAYJ,EAAc,UAAU,SACxDA,EAAc,MAChB,wBAAwBK,EAAmCL,EAAc,IAAI,CAAC,EAChF,CAEA,IAAMM,EAAwC,IAU9C,SAASD,EAAmCE,EAAsB,CAChE,IAAMC,EAAoB,KAAK,UAAUD,CAAO,EAChD,OAAIC,EAAkB,QAAUF,EACvBE,EAEF,2BAA2BA,EAAkB,MAAM,MAAMA,EAAkB,MAChF,EACAF,EAAwC,CAC1C,CAAC,MAAME,EAAkB,MAAM,CAACF,EAAwC,CAAC,CAAC,EAC5E", "names": ["KeylessErrorCategor<PERSON>", "KeylessErrorResolutionTip", "KeylessErrorType", "KeylessErrors", "Key<PERSON><PERSON><PERSON><PERSON>", "_KeylessError", "args", "innerError", "category", "resolutionTip", "type", "message", "details", "tip", "result", "AptosApiError", "getErrorMessage", "error", "apiType", "aptosRequest", "aptosResponse", "deriveErrorMessage", "traceId", "traceIdString", "errorPrelude", "serializeAnyPayloadForErrorMessage", "SERIALIZED_PAYLOAD_TRIM_TO_MAX_LENGTH", "payload", "serializedPayload"]}