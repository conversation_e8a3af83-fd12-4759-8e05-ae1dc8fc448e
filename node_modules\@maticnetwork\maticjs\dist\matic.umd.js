/*!
 * @license :@maticnetwork/maticjs - V3.9.2 - 22/08/2024
 * https://github.com/maticnetwork/matic.js
 * Copyright (c) 2024 @Polygon Labs; Licensed MIT
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@ethereumjs/util"), require("buffer"), require("bn.js"), require("rlp"), require("@ethereumjs/trie"), require("@ethereumjs/block"), require("@ethereumjs/common"), require("node-fetch"));
	else if(typeof define === 'function' && define.amd)
		define("matic", ["@ethereumjs/util", "buffer", "bn.js", "rlp", "@ethereumjs/trie", "@ethereumjs/block", "@ethereumjs/common", "node-fetch"], factory);
	else if(typeof exports === 'object')
		exports["matic"] = factory(require("@ethereumjs/util"), require("buffer"), require("bn.js"), require("rlp"), require("@ethereumjs/trie"), require("@ethereumjs/block"), require("@ethereumjs/common"), require("node-fetch"));
	else
		root["matic"] = factory(root["@ethereumjs/util"], root["buffer"], root["bn.js"], root["rlp"], root["@ethereumjs/trie"], root["@ethereumjs/block"], root["@ethereumjs/common"], root["node-fetch"]);
})(self, (__WEBPACK_EXTERNAL_MODULE__ethereumjs_util__, __WEBPACK_EXTERNAL_MODULE_buffer__, __WEBPACK_EXTERNAL_MODULE_bn_js__, __WEBPACK_EXTERNAL_MODULE_rlp__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_trie__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_block__, __WEBPACK_EXTERNAL_MODULE__ethereumjs_common__, __WEBPACK_EXTERNAL_MODULE_node_fetch__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/safe-buffer/index.js":
/*!*******************************************!*\
  !*** ./node_modules/safe-buffer/index.js ***!
  \*******************************************/
/***/ ((module, exports, __webpack_require__) => {

/* eslint-disable node/no-deprecated-api */
var buffer = __webpack_require__(/*! buffer */ "buffer")
var Buffer = buffer.Buffer

// alternative to using Object.keys for old browsers
function copyProps (src, dst) {
  for (var key in src) {
    dst[key] = src[key]
  }
}
if (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {
  module.exports = buffer
} else {
  // Copy properties from require('buffer')
  copyProps(buffer, exports)
  exports.Buffer = SafeBuffer
}

function SafeBuffer (arg, encodingOrOffset, length) {
  return Buffer(arg, encodingOrOffset, length)
}

// Copy static methods from Buffer
copyProps(Buffer, SafeBuffer)

SafeBuffer.from = function (arg, encodingOrOffset, length) {
  if (typeof arg === 'number') {
    throw new TypeError('Argument must not be a number')
  }
  return Buffer(arg, encodingOrOffset, length)
}

SafeBuffer.alloc = function (size, fill, encoding) {
  if (typeof size !== 'number') {
    throw new TypeError('Argument must be a number')
  }
  var buf = Buffer(size)
  if (fill !== undefined) {
    if (typeof encoding === 'string') {
      buf.fill(fill, encoding)
    } else {
      buf.fill(fill)
    }
  } else {
    buf.fill(0)
  }
  return buf
}

SafeBuffer.allocUnsafe = function (size) {
  if (typeof size !== 'number') {
    throw new TypeError('Argument must be a number')
  }
  return Buffer(size)
}

SafeBuffer.allocUnsafeSlow = function (size) {
  if (typeof size !== 'number') {
    throw new TypeError('Argument must be a number')
  }
  return buffer.SlowBuffer(size)
}


/***/ }),

/***/ "./src/abstracts/base_big_number.ts":
/*!******************************************!*\
  !*** ./src/abstracts/base_big_number.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseBigNumber: () => (/* binding */ BaseBigNumber)
/* harmony export */ });
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! .. */ "./src/index.ts");

var BaseBigNumber = /** @class */ (function () {
    function BaseBigNumber() {
    }
    BaseBigNumber.isBN = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    return BaseBigNumber;
}());



/***/ }),

/***/ "./src/abstracts/base_contract.ts":
/*!****************************************!*\
  !*** ./src/abstracts/base_contract.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseContract: () => (/* binding */ BaseContract)
/* harmony export */ });
var BaseContract = /** @class */ (function () {
    function BaseContract(address, logger) {
        this.address = address;
        this.logger = logger;
    }
    return BaseContract;
}());



/***/ }),

/***/ "./src/abstracts/base_web3_client.ts":
/*!*******************************************!*\
  !*** ./src/abstracts/base_web3_client.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseWeb3Client: () => (/* binding */ BaseWeb3Client)
/* harmony export */ });
var BaseWeb3Client = /** @class */ (function () {
    function BaseWeb3Client(logger) {
        this.logger = logger;
    }
    BaseWeb3Client.prototype.getRootHash = function (startBlock, endBlock) {
        return this.sendRPCRequest({
            jsonrpc: '2.0',
            method: 'eth_getRootHash',
            params: [Number(startBlock), Number(endBlock)],
            id: new Date().getTime()
        }).then(function (payload) {
            return String(payload.result);
        });
    };
    BaseWeb3Client.prototype.getAccountsUsingRPC_ = function () {
        return this.sendRPCRequest({
            jsonrpc: '2.0',
            method: 'eth_accounts',
            params: [],
            id: new Date().getTime()
        }).then(function (payload) {
            return payload.result;
        });
    };
    return BaseWeb3Client;
}());



/***/ }),

/***/ "./src/abstracts/contract_method.ts":
/*!******************************************!*\
  !*** ./src/abstracts/contract_method.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseContractMethod: () => (/* binding */ BaseContractMethod)
/* harmony export */ });
var BaseContractMethod = /** @class */ (function () {
    function BaseContractMethod(logger) {
        this.logger = logger;
    }
    return BaseContractMethod;
}());



/***/ }),

/***/ "./src/abstracts/index.ts":
/*!********************************!*\
  !*** ./src/abstracts/index.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseBigNumber: () => (/* reexport safe */ _base_big_number__WEBPACK_IMPORTED_MODULE_3__.BaseBigNumber),
/* harmony export */   BaseContract: () => (/* reexport safe */ _base_contract__WEBPACK_IMPORTED_MODULE_2__.BaseContract),
/* harmony export */   BaseContractMethod: () => (/* reexport safe */ _contract_method__WEBPACK_IMPORTED_MODULE_0__.BaseContractMethod),
/* harmony export */   BaseWeb3Client: () => (/* reexport safe */ _base_web3_client__WEBPACK_IMPORTED_MODULE_1__.BaseWeb3Client)
/* harmony export */ });
/* harmony import */ var _contract_method__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./contract_method */ "./src/abstracts/contract_method.ts");
/* harmony import */ var _base_web3_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base_web3_client */ "./src/abstracts/base_web3_client.ts");
/* harmony import */ var _base_contract__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base_contract */ "./src/abstracts/base_contract.ts");
/* harmony import */ var _base_big_number__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base_big_number */ "./src/abstracts/base_big_number.ts");






/***/ }),

/***/ "./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   config: () => (/* binding */ config)
/* harmony export */ });
var config = {
    abiStoreUrl: 'https://static.polygon.technology/network/',
    zkEvmBridgeService: 'https://proof-generator.polygon.technology/',
};


/***/ }),

/***/ "./src/constant.ts":
/*!*************************!*\
  !*** ./src/constant.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ADDRESS_ZERO: () => (/* binding */ ADDRESS_ZERO),
/* harmony export */   DAI_PERMIT_TYPEHASH: () => (/* binding */ DAI_PERMIT_TYPEHASH),
/* harmony export */   EIP_2612_DOMAIN_TYPEHASH: () => (/* binding */ EIP_2612_DOMAIN_TYPEHASH),
/* harmony export */   EIP_2612_PERMIT_TYPEHASH: () => (/* binding */ EIP_2612_PERMIT_TYPEHASH),
/* harmony export */   MAX_AMOUNT: () => (/* binding */ MAX_AMOUNT),
/* harmony export */   Permit: () => (/* binding */ Permit),
/* harmony export */   UNISWAP_DOMAIN_TYPEHASH: () => (/* binding */ UNISWAP_DOMAIN_TYPEHASH),
/* harmony export */   _GLOBAL_INDEX_MAINNET_FLAG: () => (/* binding */ _GLOBAL_INDEX_MAINNET_FLAG)
/* harmony export */ });
var MAX_AMOUNT = '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff';
var ADDRESS_ZERO = '0x0000000000000000000000000000000000000000';
var DAI_PERMIT_TYPEHASH = "0xea2aa0a1be11a07ed86d755c93467f4f82362b452371d1ba94d1715123511acb";
var EIP_2612_PERMIT_TYPEHASH = "0x6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c9";
var EIP_2612_DOMAIN_TYPEHASH = "0x8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f";
var UNISWAP_DOMAIN_TYPEHASH = "0x8cad95687ba82c2ce50e74f7b754645e5117c3a5bec8151c0726d5857980a866";
var _GLOBAL_INDEX_MAINNET_FLAG = BigInt(Math.pow(2, 64));
var Permit;
(function (Permit) {
    Permit["DAI"] = "DAI";
    Permit["EIP_2612"] = "EIP_2612";
    Permit["UNISWAP"] = "UNISWAP";
})(Permit || (Permit = {}));


/***/ }),

/***/ "./src/default.ts":
/*!************************!*\
  !*** ./src/default.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   defaultExport: () => (/* binding */ defaultExport)
/* harmony export */ });
/* harmony import */ var _pos__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pos */ "./src/pos/index.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./src/utils/index.ts");


var defaultExport = {
    utils: _utils__WEBPACK_IMPORTED_MODULE_1__.utils,
    use: _utils__WEBPACK_IMPORTED_MODULE_1__.use,
    POSClient: _pos__WEBPACK_IMPORTED_MODULE_0__.POSClient,
};


/***/ }),

/***/ "./src/enums/error_type.ts":
/*!*********************************!*\
  !*** ./src/enums/error_type.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERROR_TYPE: () => (/* binding */ ERROR_TYPE)
/* harmony export */ });
var ERROR_TYPE;
(function (ERROR_TYPE) {
    ERROR_TYPE["AllowedOnRoot"] = "allowed_on_root";
    ERROR_TYPE["AllowedOnChild"] = "allowed_on_child";
    ERROR_TYPE["Unknown"] = "unknown";
    ERROR_TYPE["ProofAPINotSet"] = "proof_api_not_set";
    ERROR_TYPE["TransactionOptionNotObject"] = "transation_object_not_object";
    ERROR_TYPE["BurnTxNotCheckPointed"] = "burn_tx_not_checkpointed";
    ERROR_TYPE["EIP1559NotSupported"] = "eip-1559_not_supported";
    ERROR_TYPE["NullSpenderAddress"] = "null_spender_address";
    ERROR_TYPE["AllowedOnNonNativeTokens"] = "allowed_on_non_native_token";
    ERROR_TYPE["AllowedOnMainnet"] = "allowed_on_mainnet";
    ERROR_TYPE["BridgeAdapterNotFound"] = "bridge_adapter_address_not_passed";
})(ERROR_TYPE || (ERROR_TYPE = {}));


/***/ }),

/***/ "./src/enums/index.ts":
/*!****************************!*\
  !*** ./src/enums/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERROR_TYPE: () => (/* reexport safe */ _error_type__WEBPACK_IMPORTED_MODULE_1__.ERROR_TYPE),
/* harmony export */   Log_Event_Signature: () => (/* reexport safe */ _log_event_signature__WEBPACK_IMPORTED_MODULE_0__.Log_Event_Signature)
/* harmony export */ });
/* harmony import */ var _log_event_signature__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./log_event_signature */ "./src/enums/log_event_signature.ts");
/* harmony import */ var _error_type__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error_type */ "./src/enums/error_type.ts");




/***/ }),

/***/ "./src/enums/log_event_signature.ts":
/*!******************************************!*\
  !*** ./src/enums/log_event_signature.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Log_Event_Signature: () => (/* binding */ Log_Event_Signature)
/* harmony export */ });
var Log_Event_Signature;
(function (Log_Event_Signature) {
    // PlasmaErc20WithdrawEventSig = '0xebff2602b3f468259e1e99f613fed6691f3a6526effe6ef3e768ba7ae7a36c4f',
    // PlasmaErc721WithdrawEventSig = '0x9b1bfa7fa9ee420a16e124f794c35ac9f90472acc99140eb2f6447c714cad8eb',
    Log_Event_Signature["Erc20Transfer"] = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";
    Log_Event_Signature["Erc721Transfer"] = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";
    Log_Event_Signature["Erc1155Transfer"] = "0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62";
    Log_Event_Signature["Erc721BatchTransfer"] = "0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df";
    Log_Event_Signature["Erc1155BatchTransfer"] = "0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb";
    Log_Event_Signature["Erc721TransferWithMetadata"] = "0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14";
})(Log_Event_Signature || (Log_Event_Signature = {}));


/***/ }),

/***/ "./src/implementation/bn.ts":
/*!**********************************!*\
  !*** ./src/implementation/bn.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EmptyBigNumber: () => (/* binding */ EmptyBigNumber)
/* harmony export */ });
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! .. */ "./src/index.ts");
/* harmony import */ var _abstracts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../abstracts */ "./src/abstracts/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();


var EmptyBigNumber = /** @class */ (function (_super) {
    __extends(EmptyBigNumber, _super);
    function EmptyBigNumber(value) {
        return _super.call(this) || this;
    }
    EmptyBigNumber.prototype.toString = function (base) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.toNumber = function () {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.add = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.sub = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.mul = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.div = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.lte = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.lt = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.gte = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.gt = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    EmptyBigNumber.prototype.eq = function (value) {
        return (0,___WEBPACK_IMPORTED_MODULE_0__.throwNotImplemented)();
    };
    return EmptyBigNumber;
}(_abstracts__WEBPACK_IMPORTED_MODULE_1__.BaseBigNumber));



/***/ }),

/***/ "./src/implementation/index.ts":
/*!*************************************!*\
  !*** ./src/implementation/index.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EmptyBigNumber: () => (/* reexport safe */ _bn__WEBPACK_IMPORTED_MODULE_0__.EmptyBigNumber)
/* harmony export */ });
/* harmony import */ var _bn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bn */ "./src/implementation/bn.ts");



/***/ }),

/***/ "./src/index.ts":
/*!**********************!*\
  !*** ./src/index.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ABIManager: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.ABIManager),
/* harmony export */   ADDRESS_ZERO: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.ADDRESS_ZERO),
/* harmony export */   BN: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.BN),
/* harmony export */   BaseBigNumber: () => (/* reexport safe */ _abstracts__WEBPACK_IMPORTED_MODULE_7__.BaseBigNumber),
/* harmony export */   BaseContract: () => (/* reexport safe */ _abstracts__WEBPACK_IMPORTED_MODULE_7__.BaseContract),
/* harmony export */   BaseContractMethod: () => (/* reexport safe */ _abstracts__WEBPACK_IMPORTED_MODULE_7__.BaseContractMethod),
/* harmony export */   BaseToken: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.BaseToken),
/* harmony export */   BaseWeb3Client: () => (/* reexport safe */ _abstracts__WEBPACK_IMPORTED_MODULE_7__.BaseWeb3Client),
/* harmony export */   BridgeClient: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.BridgeClient),
/* harmony export */   BridgeUtil: () => (/* reexport safe */ _zkevm__WEBPACK_IMPORTED_MODULE_9__.BridgeUtil),
/* harmony export */   BufferUtil: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil),
/* harmony export */   Converter: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.Converter),
/* harmony export */   DAI_PERMIT_TYPEHASH: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.DAI_PERMIT_TYPEHASH),
/* harmony export */   EIP_2612_DOMAIN_TYPEHASH: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.EIP_2612_DOMAIN_TYPEHASH),
/* harmony export */   EIP_2612_PERMIT_TYPEHASH: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.EIP_2612_PERMIT_TYPEHASH),
/* harmony export */   ERROR_TYPE: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE),
/* harmony export */   EventBus: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.EventBus),
/* harmony export */   ExitUtil: () => (/* reexport safe */ _pos__WEBPACK_IMPORTED_MODULE_3__.ExitUtil),
/* harmony export */   GasSwapper: () => (/* reexport safe */ _pos__WEBPACK_IMPORTED_MODULE_3__.GasSwapper),
/* harmony export */   HttpRequest: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.HttpRequest),
/* harmony export */   Keccak: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.Keccak),
/* harmony export */   Log_Event_Signature: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature),
/* harmony export */   Logger: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.Logger),
/* harmony export */   MAX_AMOUNT: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.MAX_AMOUNT),
/* harmony export */   NetworkService: () => (/* reexport safe */ _services__WEBPACK_IMPORTED_MODULE_8__.NetworkService),
/* harmony export */   POSClient: () => (/* reexport safe */ _pos__WEBPACK_IMPORTED_MODULE_3__.POSClient),
/* harmony export */   Permit: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.Permit),
/* harmony export */   ProofUtil: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.ProofUtil),
/* harmony export */   RootChain: () => (/* reexport safe */ _pos__WEBPACK_IMPORTED_MODULE_3__.RootChain),
/* harmony export */   RootChainManager: () => (/* reexport safe */ _pos__WEBPACK_IMPORTED_MODULE_3__.RootChainManager),
/* harmony export */   UNISWAP_DOMAIN_TYPEHASH: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__.UNISWAP_DOMAIN_TYPEHASH),
/* harmony export */   Web3SideChainClient: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.Web3SideChainClient),
/* harmony export */   ZkEVMWrapper: () => (/* reexport safe */ _zkevm__WEBPACK_IMPORTED_MODULE_9__.ZkEVMWrapper),
/* harmony export */   ZkEvmBridge: () => (/* reexport safe */ _zkevm__WEBPACK_IMPORTED_MODULE_9__.ZkEvmBridge),
/* harmony export */   ZkEvmBridgeClient: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.ZkEvmBridgeClient),
/* harmony export */   ZkEvmClient: () => (/* reexport safe */ _zkevm__WEBPACK_IMPORTED_MODULE_9__.ZkEvmClient),
/* harmony export */   _GLOBAL_INDEX_MAINNET_FLAG: () => (/* reexport safe */ _constant__WEBPACK_IMPORTED_MODULE_6__._GLOBAL_INDEX_MAINNET_FLAG),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   eventBusPromise: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.eventBusPromise),
/* harmony export */   mapPromise: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.mapPromise),
/* harmony export */   merge: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.merge),
/* harmony export */   promiseAny: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.promiseAny),
/* harmony export */   promiseResolve: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.promiseResolve),
/* harmony export */   resolve: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.resolve),
/* harmony export */   service: () => (/* reexport safe */ _services__WEBPACK_IMPORTED_MODULE_8__.service),
/* harmony export */   setProofApi: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.setProofApi),
/* harmony export */   setZkEvmProofApi: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.setZkEvmProofApi),
/* harmony export */   throwNotImplemented: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.throwNotImplemented),
/* harmony export */   use: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.use),
/* harmony export */   utils: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.utils)
/* harmony export */ });
/* harmony import */ var _default__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./default */ "./src/default.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./src/utils/index.ts");
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./enums */ "./src/enums/index.ts");
/* harmony import */ var _pos__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pos */ "./src/pos/index.ts");
/* harmony import */ var _interfaces__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./interfaces */ "./src/interfaces/index.ts");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./types */ "./src/types/index.ts");
/* harmony import */ var _constant__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./constant */ "./src/constant.ts");
/* harmony import */ var _abstracts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./abstracts */ "./src/abstracts/index.ts");
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./services */ "./src/services/index.ts");
/* harmony import */ var _zkevm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./zkevm */ "./src/zkevm/index.ts");










/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_default__WEBPACK_IMPORTED_MODULE_0__.defaultExport);


/***/ }),

/***/ "./src/interfaces/allowance_transaction_option.ts":
/*!********************************************************!*\
  !*** ./src/interfaces/allowance_transaction_option.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/approve_transaction_option.ts":
/*!******************************************************!*\
  !*** ./src/interfaces/approve_transaction_option.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/base_client_config.ts":
/*!**********************************************!*\
  !*** ./src/interfaces/base_client_config.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/block.ts":
/*!*********************************!*\
  !*** ./src/interfaces/block.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/block_with_transaction.ts":
/*!**************************************************!*\
  !*** ./src/interfaces/block_with_transaction.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/bridge_transaction_option.ts":
/*!*****************************************************!*\
  !*** ./src/interfaces/bridge_transaction_option.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/contract_init_param.ts":
/*!***********************************************!*\
  !*** ./src/interfaces/contract_init_param.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/error.ts":
/*!*********************************!*\
  !*** ./src/interfaces/error.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/exit_transaction_option.ts":
/*!***************************************************!*\
  !*** ./src/interfaces/exit_transaction_option.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/index.ts":
/*!*********************************!*\
  !*** ./src/interfaces/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _plugin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./plugin */ "./src/interfaces/plugin.ts");
/* harmony import */ var _method__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./method */ "./src/interfaces/method.ts");
/* harmony import */ var _transaction_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transaction_config */ "./src/interfaces/transaction_config.ts");
/* harmony import */ var _transaction_write_result__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./transaction_write_result */ "./src/interfaces/transaction_write_result.ts");
/* harmony import */ var _transaction_result__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./transaction_result */ "./src/interfaces/transaction_result.ts");
/* harmony import */ var _transaction_option__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./transaction_option */ "./src/interfaces/transaction_option.ts");
/* harmony import */ var _contract_init_param__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./contract_init_param */ "./src/interfaces/contract_init_param.ts");
/* harmony import */ var _tx_receipt__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tx_receipt */ "./src/interfaces/tx_receipt.ts");
/* harmony import */ var _pos_client_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./pos_client_config */ "./src/interfaces/pos_client_config.ts");
/* harmony import */ var _transaction_data__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./transaction_data */ "./src/interfaces/transaction_data.ts");
/* harmony import */ var _block__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./block */ "./src/interfaces/block.ts");
/* harmony import */ var _block_with_transaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./block_with_transaction */ "./src/interfaces/block_with_transaction.ts");
/* harmony import */ var _rpc_request_payload__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./rpc_request_payload */ "./src/interfaces/rpc_request_payload.ts");
/* harmony import */ var _rpc_response_payload__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./rpc_response_payload */ "./src/interfaces/rpc_response_payload.ts");
/* harmony import */ var _map_promise_option__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./map_promise_option */ "./src/interfaces/map_promise_option.ts");
/* harmony import */ var _base_client_config__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./base_client_config */ "./src/interfaces/base_client_config.ts");
/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./error */ "./src/interfaces/error.ts");
/* harmony import */ var _pos_contracts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./pos_contracts */ "./src/interfaces/pos_contracts.ts");
/* harmony import */ var _root_block_info__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./root_block_info */ "./src/interfaces/root_block_info.ts");
/* harmony import */ var _allowance_transaction_option__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./allowance_transaction_option */ "./src/interfaces/allowance_transaction_option.ts");
/* harmony import */ var _approve_transaction_option__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./approve_transaction_option */ "./src/interfaces/approve_transaction_option.ts");
/* harmony import */ var _exit_transaction_option__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./exit_transaction_option */ "./src/interfaces/exit_transaction_option.ts");
/* harmony import */ var _zkevm_client_config__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./zkevm_client_config */ "./src/interfaces/zkevm_client_config.ts");
/* harmony import */ var _zkevm_contracts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./zkevm_contracts */ "./src/interfaces/zkevm_contracts.ts");
/* harmony import */ var _bridge_transaction_option__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./bridge_transaction_option */ "./src/interfaces/bridge_transaction_option.ts");



























/***/ }),

/***/ "./src/interfaces/map_promise_option.ts":
/*!**********************************************!*\
  !*** ./src/interfaces/map_promise_option.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/method.ts":
/*!**********************************!*\
  !*** ./src/interfaces/method.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/plugin.ts":
/*!**********************************!*\
  !*** ./src/interfaces/plugin.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/pos_client_config.ts":
/*!*********************************************!*\
  !*** ./src/interfaces/pos_client_config.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/pos_contracts.ts":
/*!*****************************************!*\
  !*** ./src/interfaces/pos_contracts.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/root_block_info.ts":
/*!*******************************************!*\
  !*** ./src/interfaces/root_block_info.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/rpc_request_payload.ts":
/*!***********************************************!*\
  !*** ./src/interfaces/rpc_request_payload.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/rpc_response_payload.ts":
/*!************************************************!*\
  !*** ./src/interfaces/rpc_response_payload.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/transaction_config.ts":
/*!**********************************************!*\
  !*** ./src/interfaces/transaction_config.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/transaction_data.ts":
/*!********************************************!*\
  !*** ./src/interfaces/transaction_data.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/transaction_option.ts":
/*!**********************************************!*\
  !*** ./src/interfaces/transaction_option.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/transaction_result.ts":
/*!**********************************************!*\
  !*** ./src/interfaces/transaction_result.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/transaction_write_result.ts":
/*!****************************************************!*\
  !*** ./src/interfaces/transaction_write_result.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/tx_receipt.ts":
/*!**************************************!*\
  !*** ./src/interfaces/tx_receipt.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/zkevm_client_config.ts":
/*!***********************************************!*\
  !*** ./src/interfaces/zkevm_client_config.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/interfaces/zkevm_contracts.ts":
/*!*******************************************!*\
  !*** ./src/interfaces/zkevm_contracts.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/pos/erc1155.ts":
/*!****************************!*\
  !*** ./src/pos/erc1155.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERC1155: () => (/* binding */ ERC1155)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _pos_token__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pos_token */ "./src/pos/pos_token.ts");
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums */ "./src/enums/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();



var ERC1155 = /** @class */ (function (_super) {
    __extends(ERC1155, _super);
    function ERC1155(tokenAddress, isParent, client, getContracts) {
        return _super.call(this, {
            isParent: isParent,
            address: tokenAddress,
            name: 'ChildERC1155',
            bridgeType: 'pos'
        }, client, getContracts) || this;
    }
    Object.defineProperty(ERC1155.prototype, "addressConfig", {
        get: function () {
            return this.client.config.erc1155 || {};
        },
        enumerable: false,
        configurable: true
    });
    ERC1155.prototype.getAddress_ = function (value) {
        var addresses = this.addressConfig;
        if (addresses[value]) {
            return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.promiseResolve)(addresses[value]);
        }
        return this.client.getConfig(value);
    };
    /**
     * get balance of a user for supplied token
     *
     * @param {string} userAddress
     * @param {TYPE_AMOUNT} tokenId
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.getBalance = function (userAddress, tokenId, option) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("balanceOf", userAddress, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId));
            return _this.processRead(method, option);
        });
    };
    /**
     * check if a user is approved for all tokens
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.isApprovedAll = function (userAddress, option) {
        var _this = this;
        this.checkForRoot("isApprovedAll");
        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(function (result) {
            var contract = result[0], predicateAddress = result[1];
            var method = contract.method("isApprovedForAll", userAddress, predicateAddress);
            return _this.processRead(method, option);
        });
    };
    ERC1155.prototype.approveAll_ = function (predicateAddressPromise, option) {
        var _this = this;
        this.checkForRoot("approve");
        return Promise.all([this.getContract(), predicateAddressPromise]).then(function (result) {
            var contract = result[0], predicateAddress = result[1];
            var method = contract.method("setApprovalForAll", predicateAddress, true);
            return _this.processWrite(method, option);
        });
    };
    /**
     * approve all tokens
     *
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.approveAll = function (option) {
        this.checkForRoot("approve");
        return this.approveAll_(this.getPredicateAddress(), option);
    };
    /**
     * approve all tokens for mintable token
     *
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.approveAllForMintable = function (option) {
        this.checkForRoot("approveForMintable");
        var addressPath = "Main.POSContracts.MintableERC1155PredicateProxy";
        return this.approveAll_(this.getAddress_(addressPath), option);
    };
    /**
     * deposit supplied amount of token for a user
     *
     * @param {POSERC1155DepositParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.deposit = function (param, option) {
        this.checkForRoot("deposit");
        return this.depositMany({
            amounts: [param.amount],
            tokenIds: [param.tokenId],
            userAddress: param.userAddress,
            data: param.data
        }, option);
    };
    /**
     * deposit supplied amount of multiple token for user
     *
     * @param {POSERC1155DepositBatchParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.depositMany = function (param, option) {
        this.checkForRoot("depositMany");
        var tokenIds = param.tokenIds, amounts = param.amounts, data = param.data, userAddress = param.userAddress;
        var emptyHex = _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(0);
        var amountInABI = this.client.parent.encodeParameters([
            tokenIds.map(function (t) { return _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(t); }),
            amounts.map(function (a) { return _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(a); }),
            data || emptyHex
        ], ['uint256[]', 'uint256[]', 'bytes']);
        return this.rootChainManager.deposit(userAddress, this.contractParam.address, amountInABI, option);
    };
    /**
     * start withdraw process by burning the required amount for a token
     *
     * @param {string} tokenId
     * @param {TYPE_AMOUNT} amount
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawStart = function (tokenId, amount, option) {
        var _this = this;
        this.checkForChild("withdrawStart");
        return this.getContract().then(function (contract) {
            var method = contract.method("withdrawSingle", _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId), _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount));
            return _this.processWrite(method, option);
        });
    };
    /**
     * start the withdraw process by burning the supplied amount of multiple token at a time
     *
     * @param {TYPE_AMOUNT[]} tokenIds
     * @param {TYPE_AMOUNT[]} amounts
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawStartMany = function (tokenIds, amounts, option) {
        var _this = this;
        this.checkForChild("withdrawStartMany");
        var tokensInHex = tokenIds.map(function (t) {
            return _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(t);
        });
        var amountsInHex = amounts.map(function (t) {
            return _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(t);
        });
        return this.getContract().then(function (contract) {
            var method = contract.method("withdrawBatch", tokensInHex, amountsInHex);
            return _this.processWrite(method, option);
        });
    };
    /**
     * exit the withdraw process and get the burned amount on root chain
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawExit = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExit");
        return this.withdrawExitPOS(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155Transfer, false, option);
    };
    /**
     * exit the withdraw process and get the burned amount on root chain
     *
     * the process is faster because it uses proof api
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawExitFaster = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExitFaster");
        return this.withdrawExitPOS(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155Transfer, true, option);
    };
    /**
     * exit the withdraw process for many burned transaction and get the burned amount on root chain
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawExitMany = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExitMany");
        return this.withdrawExitPOS(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155BatchTransfer, false, option);
    };
    /**
     * exit the withdraw process for many burned transaction and get the burned amount on root chain
     *
     * the process is faster because it uses proof api
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.withdrawExitFasterMany = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExitFasterMany");
        return this.withdrawExitPOS(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155BatchTransfer, true, option);
    };
    /**
     * check if exit has been completed for a transaction hash
     *
     * @param {string} burnTxHash
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.isWithdrawExited = function (txHash) {
        return this.isWithdrawn(txHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155Transfer);
    };
    /**
     * check if batch exit has been completed for a transaction hash
     *
     * @param {string} txHash
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.isWithdrawExitedMany = function (txHash) {
        return this.isWithdrawn(txHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc1155BatchTransfer);
    };
    /**
     * transfer the required amount of a token to another user
     *
     * @param {POSERC1155TransferParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    ERC1155.prototype.transfer = function (param, option) {
        return this.transferERC1155(param, option);
    };
    return ERC1155;
}(_pos_token__WEBPACK_IMPORTED_MODULE_1__.POSToken));



/***/ }),

/***/ "./src/pos/erc20.ts":
/*!**************************!*\
  !*** ./src/pos/erc20.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERC20: () => (/* binding */ ERC20)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _pos_token__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pos_token */ "./src/pos/pos_token.ts");
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums */ "./src/enums/index.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! .. */ "./src/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();




var ERC20 = /** @class */ (function (_super) {
    __extends(ERC20, _super);
    function ERC20(tokenAddress, isParent, client, getContracts) {
        return _super.call(this, {
            isParent: isParent,
            address: tokenAddress,
            name: 'ChildERC20',
            bridgeType: 'pos'
        }, client, getContracts) || this;
    }
    ERC20.prototype.getBalance = function (userAddress, option) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("balanceOf", userAddress);
            return _this.processRead(method, option);
        });
    };
    /**
     * get allowance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getAllowance = function (userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        var spenderAddress = option.spenderAddress;
        var predicatePromise = spenderAddress ? (0,___WEBPACK_IMPORTED_MODULE_3__.promiseResolve)(spenderAddress) : this.getPredicateAddress();
        return Promise.all([predicatePromise, this.getContract()]).then(function (result) {
            var predicateAddress = result[0], contract = result[1];
            var method = contract.method("allowance", userAddress, predicateAddress);
            return _this.processRead(method, option);
        });
    };
    ERC20.prototype.approve = function (amount, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        var spenderAddress = option.spenderAddress;
        if (!spenderAddress && !this.contractParam.isParent) {
            this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.NullSpenderAddress).throw();
        }
        var predicatePromise = spenderAddress ? (0,___WEBPACK_IMPORTED_MODULE_3__.promiseResolve)(spenderAddress) : this.getPredicateAddress();
        return Promise.all([predicatePromise, this.getContract()]).then(function (result) {
            var predicateAddress = result[0], contract = result[1];
            var method = contract.method("approve", predicateAddress, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount));
            return _this.processWrite(method, option);
        });
    };
    ERC20.prototype.approveMax = function (option) {
        if (option === void 0) { option = {}; }
        return this.approve(___WEBPACK_IMPORTED_MODULE_3__.MAX_AMOUNT, option);
    };
    /**
     * Deposit given amount of token for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.deposit = function (amount, userAddress, option) {
        this.checkForRoot("deposit");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount)], ['uint256']);
        return this.rootChainManager.deposit(userAddress, this.contractParam.address, amountInABI, option);
    };
    /**
     * Deposit given amount of token for user along with ETHER for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositWithGas = function (amount, userAddress, swapEthAmount, swapCallData, option) {
        var _this = this;
        this.checkForRoot("deposit");
        return this.getChainId().then(function (chainId) {
            if (chainId !== 1) {
                _this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.AllowedOnMainnet).throw();
            }
            var amountInABI = _this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount)], ['uint256']);
            option.value = _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(swapEthAmount);
            return _this.gasSwapper.depositWithGas(_this.contractParam.address, amountInABI, userAddress, swapCallData, option);
        });
    };
    ERC20.prototype.depositEther_ = function (amount, userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForRoot("depositEther");
        option.value = _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount);
        return this.rootChainManager.method("depositEtherFor", userAddress).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    ERC20.prototype.depositEtherWithGas_ = function (amount, userAddress, swapEthAmount, swapCallData, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForRoot("depositEtherWithGas");
        return this.getChainId().then(function (chainId) {
            if (chainId !== 1) {
                _this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.AllowedOnMainnet).throw();
            }
            var amountInABI = _this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount)], ['uint256']);
            option.value = _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toBN(amount).add(_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toBN(swapEthAmount)));
            return _this.gasSwapper.depositWithGas("******************************************", amountInABI, userAddress, swapCallData, option);
        });
    };
    /**
     * initiate withdraw by burning provided amount
     *
     * @param {TYPE_AMOUNT} amount
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawStart = function (amount, option) {
        var _this = this;
        this.checkForChild("withdrawStart");
        return this.getContract().then(function (contract) {
            var method = contract.method("withdraw", _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount));
            return _this.processWrite(method, option);
        });
    };
    ERC20.prototype.withdrawExit_ = function (burnTransactionHash, isFast, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        var eventSignature = option.burnEventSignature ?
            option.burnEventSignature : _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc20Transfer;
        return this.exitUtil.buildPayloadForExit(burnTransactionHash, eventSignature, isFast).then(function (payload) {
            return _this.rootChainManager.exit(payload, option);
        });
    };
    /**
     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawExit = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExit");
        return this.withdrawExit_(burnTransactionHash, false, option);
    };
    /**
     * complete withdraw process after checkpoint has been submitted for the block containing burn tx.
     *
     *  Note:- It create the proof in api call for fast exit.
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawExitFaster = function (burnTransactionHash, option) {
        this.checkForRoot("withdrawExitFaster");
        return this.withdrawExit_(burnTransactionHash, true, option);
    };
    /**
     * check if exit has been completed for a transaction hash
     *
     * @param {string} burnTxHash
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.isWithdrawExited = function (burnTxHash) {
        return this.isWithdrawn(burnTxHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc20Transfer);
    };
    /**
     * transfer amount to another user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.transfer = function (amount, to, option) {
        return this.transferERC20(to, amount, option);
    };
    return ERC20;
}(_pos_token__WEBPACK_IMPORTED_MODULE_1__.POSToken));



/***/ }),

/***/ "./src/pos/erc721.ts":
/*!***************************!*\
  !*** ./src/pos/erc721.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERC721: () => (/* binding */ ERC721)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _pos_token__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pos_token */ "./src/pos/pos_token.ts");
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums */ "./src/enums/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();



var ERC721 = /** @class */ (function (_super) {
    __extends(ERC721, _super);
    function ERC721(tokenAddress, isParent, client, getContracts) {
        return _super.call(this, {
            isParent: isParent,
            address: tokenAddress,
            name: 'ChildERC721',
            bridgeType: 'pos'
        }, client, getContracts) || this;
    }
    ERC721.prototype.validateMany_ = function (tokenIds) {
        if (tokenIds.length > 20) {
            throw new Error('can not process more than 20 tokens');
        }
        return tokenIds.map(function (tokenId) {
            return _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId);
        });
    };
    /**
     * get tokens count for the user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [options]
     * @returns
     * @memberof ERC721
     */
    ERC721.prototype.getTokensCount = function (userAddress, options) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("balanceOf", userAddress);
            return _this.processRead(method, options);
        }).then(function (count) {
            return Number(count);
        });
    };
    /**
     * returns token id on supplied index for user
     *
     * @param {number} index
     * @param {string} userAddress
     * @param {ITransactionOption} [options]
     * @returns
     * @memberof ERC721
     */
    ERC721.prototype.getTokenIdAtIndexForUser = function (index, userAddress, options) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("tokenOfOwnerByIndex", userAddress, index);
            return _this.processRead(method, options);
        });
    };
    /**
     * get all tokens for user
     *
     * @param {string} userAddress
     * @param {*} [limit=Infinity]
     * @returns
     * @memberof ERC721
     */
    ERC721.prototype.getAllTokens = function (userAddress, limit) {
        var _this = this;
        if (limit === void 0) { limit = Infinity; }
        return this.getTokensCount(userAddress).then(function (count) {
            count = Number(count);
            if (count > limit) {
                count = limit;
            }
            var promises = [];
            for (var i = 0; i < count; i++) {
                promises.push(_this.getTokenIdAtIndexForUser(i, userAddress));
            }
            return Promise.all(promises);
        });
    };
    ERC721.prototype.isApproved = function (tokenId, option) {
        var _this = this;
        this.checkForRoot("isApproved");
        return this.getContract().then(function (contract) {
            var method = contract.method("getApproved", tokenId);
            return Promise.all([
                _this.processRead(method, option),
                _this.getPredicateAddress()
            ]).then(function (result) {
                return result[0] === result[1];
            });
        });
    };
    ERC721.prototype.isApprovedAll = function (userAddress, option) {
        var _this = this;
        this.checkForRoot("isApprovedAll");
        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(function (result) {
            var contract = result[0], predicateAddress = result[1];
            var method = contract.method("isApprovedForAll", userAddress, predicateAddress);
            return _this.processRead(method, option);
        });
    };
    ERC721.prototype.approve = function (tokenId, option) {
        var _this = this;
        this.checkForRoot("approve");
        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(function (result) {
            var contract = result[0], predicateAddress = result[1];
            var method = contract.method("approve", predicateAddress, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId));
            return _this.processWrite(method, option);
        });
    };
    ERC721.prototype.approveAll = function (option) {
        var _this = this;
        this.checkForRoot("approveAll");
        return Promise.all([this.getContract(), this.getPredicateAddress()]).then(function (result) {
            var contract = result[0], predicateAddress = result[1];
            var method = contract.method("setApprovalForAll", predicateAddress, true);
            return _this.processWrite(method, option);
        });
    };
    ERC721.prototype.deposit = function (tokenId, userAddress, option) {
        this.checkForRoot("deposit");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId)], ['uint256']);
        return this.rootChainManager.deposit(userAddress, this.contractParam.address, amountInABI, option);
    };
    ERC721.prototype.depositMany = function (tokenIds, userAddress, option) {
        this.checkForRoot("depositMany");
        var tokensInHex = this.validateMany_(tokenIds);
        var amountInABI = this.client.parent.encodeParameters([tokensInHex], ['uint256[]']);
        return this.rootChainManager.deposit(userAddress, this.contractParam.address, amountInABI, option);
    };
    ERC721.prototype.withdrawStart = function (tokenId, option) {
        var _this = this;
        this.checkForChild("withdrawStart");
        return this.getContract().then(function (contract) {
            var method = contract.method("withdraw", _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId));
            return _this.processWrite(method, option);
        });
    };
    ERC721.prototype.withdrawStartWithMetaData = function (tokenId, option) {
        var _this = this;
        this.checkForChild("withdrawStartWithMetaData");
        return this.getContract().then(function (contract) {
            var method = contract.method("withdrawWithMetadata", _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(tokenId));
            return _this.processWrite(method, option);
        });
    };
    ERC721.prototype.withdrawStartMany = function (tokenIds, option) {
        var _this = this;
        this.checkForChild("withdrawStartMany");
        var tokensInHex = this.validateMany_(tokenIds);
        return this.getContract().then(function (contract) {
            var method = contract.method("withdrawBatch", tokensInHex);
            return _this.processWrite(method, option);
        });
    };
    ERC721.prototype.withdrawExit = function (burnTransactionHash, option) {
        var _this = this;
        this.checkForRoot("withdrawExit");
        return this.exitUtil.buildPayloadForExit(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721Transfer, false).then(function (payload) {
            return _this.rootChainManager.exit(payload, option);
        });
    };
    ERC721.prototype.withdrawExitOnIndex = function (burnTransactionHash, index, option) {
        var _this = this;
        this.checkForRoot("withdrawExit");
        return this.exitUtil.buildPayloadForExit(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721Transfer, false, index).then(function (payload) {
            return _this.rootChainManager.exit(payload, option);
        });
    };
    // async withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption) {
    //     this.checkForRoot("withdrawExitMany");
    //     return this.exitUtil.buildMultiplePayloadsForExit(
    //         burnTransactionHash,
    //         Log_Event_Signature.Erc721BatchTransfer,
    //         false
    //     ).then(async payloads => {
    //         const exitTxs = [];
    //         if()
    //         for(const i in payloads) {
    //           exitTxs.push(this.rootChainManager.exit(
    //             payloads[i], option
    //         ));
    //         }
    //         return Promise.all(exitTxs);
    //         });
    // }
    ERC721.prototype.withdrawExitFaster = function (burnTransactionHash, option) {
        var _this = this;
        this.checkForRoot("withdrawExitFaster");
        return this.exitUtil.buildPayloadForExit(burnTransactionHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721Transfer, true).then(function (payload) {
            return _this.rootChainManager.exit(payload, option);
        });
    };
    // withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption) {
    //     this.checkForRoot("withdrawExitFasterMany");
    //     return this.exitUtil.buildPayloadForExit(
    //         burnTransactionHash,
    //         Log_Event_Signature.Erc721BatchTransfer,
    //         true
    //     ).then(payload => {
    //         return this.rootChainManager.exit(
    //             payload, option
    //         );
    //     });
    // }
    ERC721.prototype.isWithdrawExited = function (txHash) {
        return this.isWithdrawn(txHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721Transfer);
    };
    ERC721.prototype.isWithdrawExitedMany = function (txHash) {
        return this.isWithdrawn(txHash, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721BatchTransfer);
    };
    ERC721.prototype.isWithdrawExitedOnIndex = function (txHash, index) {
        return this.isWithdrawnOnIndex(txHash, index, _enums__WEBPACK_IMPORTED_MODULE_2__.Log_Event_Signature.Erc721Transfer);
    };
    /**
     * transfer to another user
     *
     * @param {string} tokenId
     * @param {string} from
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC721
     */
    ERC721.prototype.transfer = function (tokenId, from, to, option) {
        return this.transferERC721(from, to, tokenId, option);
    };
    return ERC721;
}(_pos_token__WEBPACK_IMPORTED_MODULE_1__.POSToken));



/***/ }),

/***/ "./src/pos/exit_util.ts":
/*!******************************!*\
  !*** ./src/pos/exit_util.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ExitUtil: () => (/* binding */ ExitUtil)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/buffer-utils */ "./src/utils/buffer-utils.ts");
/* harmony import */ var rlp__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rlp */ "rlp");
/* harmony import */ var rlp__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(rlp__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");
/* harmony import */ var _utils_error_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/error_helper */ "./src/utils/error_helper.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! .. */ "./src/index.ts");






var ExitUtil = /** @class */ (function () {
    function ExitUtil(client, rootChain) {
        this.maticClient_ = client.child;
        this.rootChain = rootChain;
        var config = client.config;
        this.config = config;
        this.requestConcurrency = config.requestConcurrency;
    }
    ExitUtil.prototype.getLogIndex_ = function (logEventSig, receipt) {
        var logIndex = -1;
        switch (logEventSig) {
            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':
            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':
                logIndex = receipt.logs.findIndex(function (log) {
                    return log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&
                        log.topics[2].toLowerCase() === '0x0000000000000000000000000000000000000000000000000000000000000000';
                });
                break;
            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':
            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':
                logIndex = receipt.logs.findIndex(function (log) {
                    return log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&
                        log.topics[3].toLowerCase() === '0x0000000000000000000000000000000000000000000000000000000000000000';
                });
                break;
            default:
                logIndex = receipt.logs.findIndex(function (log) { return log.topics[0].toLowerCase() === logEventSig.toLowerCase(); });
        }
        if (logIndex < 0) {
            throw new Error("Log not found in receipt");
        }
        return logIndex;
    };
    ExitUtil.prototype.getAllLogIndices_ = function (logEventSig, receipt) {
        var logIndices = [];
        switch (logEventSig) {
            case '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef':
            case '0xf94915c6d1fd521cee85359239227480c7e8776d7caf1fc3bacad5c269b66a14':
                logIndices = receipt.logs.reduce(function (_, log, index) {
                    return ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&
                        log.topics[2].toLowerCase() === '0x0000000000000000000000000000000000000000000000000000000000000000') &&
                        logIndices.push(index), logIndices);
                }, []);
                break;
            case '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62':
            case '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb':
                logIndices = receipt.logs.reduce(function (_, log, index) {
                    return ((log.topics[0].toLowerCase() === logEventSig.toLowerCase() &&
                        log.topics[3].toLowerCase() === '0x0000000000000000000000000000000000000000000000000000000000000000') &&
                        logIndices.push(index), logIndices);
                }, []);
                break;
            case '0xf871896b17e9cb7a64941c62c188a4f5c621b86800e3d15452ece01ce56073df':
                logIndices = receipt.logs.reduce(function (_, log, index) {
                    return ((log.topics[0].toLowerCase() === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef' &&
                        log.topics[2].toLowerCase() === '0x0000000000000000000000000000000000000000000000000000000000000000') &&
                        logIndices.push(index), logIndices);
                }, []);
                break;
            default:
                logIndices = receipt.logs.reduce(function (_, log, index) {
                    return ((log.topics[0].toLowerCase() === logEventSig.toLowerCase()) &&
                        logIndices.push(index), logIndices);
                }, []);
        }
        if (logIndices.length === 0) {
            throw new Error("Log not found in receipt");
        }
        return logIndices;
    };
    ExitUtil.prototype.getChainBlockInfo = function (burnTxHash) {
        return Promise.all([
            this.rootChain.getLastChildBlock(),
            this.maticClient_.getTransaction(burnTxHash),
        ]).then(function (result) {
            return {
                lastChildBlock: result[0],
                txBlockNumber: result[1].blockNumber
            };
        });
    };
    ExitUtil.prototype.isCheckPointed_ = function (data) {
        // lastchild block is greater equal to transaction block number; 
        return new ___WEBPACK_IMPORTED_MODULE_5__.utils.BN(data.lastChildBlock).gte(new ___WEBPACK_IMPORTED_MODULE_5__.utils.BN(data.txBlockNumber));
    };
    ExitUtil.prototype.isCheckPointed = function (burnTxHash) {
        var _this = this;
        return this.getChainBlockInfo(burnTxHash).then(function (result) {
            return _this.isCheckPointed_(result);
        });
    };
    /**
     * returns info about block number existence on parent chain
     * 1. root block number,
     * 2. start block number,
     * 3. end block number
     *
     * @private
     * @param {number} txBlockNumber - transaction block number on child chain
     * @return {*}
     * @memberof ExitUtil
     */
    ExitUtil.prototype.getRootBlockInfo = function (txBlockNumber) {
        var _this = this;
        // find in which block child was included in parent
        var rootBlockNumber;
        return this.rootChain.findRootBlockFromChild(txBlockNumber).then(function (blockNumber) {
            rootBlockNumber = blockNumber;
            return _this.rootChain.method("headerBlocks", _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(blockNumber));
        }).then(function (method) {
            return method.read();
        }).then(function (rootBlockInfo) {
            return {
                // header block number - root block number in which child block exist 
                headerBlockNumber: rootBlockNumber,
                // range of block
                // end - block end number
                end: rootBlockInfo.end.toString(),
                // start - block start number
                start: rootBlockInfo.start.toString(),
            };
        });
    };
    ExitUtil.prototype.getRootBlockInfoFromAPI = function (txBlockNumber) {
        var _this = this;
        this.maticClient_.logger.log("block info from API 1");
        return _services__WEBPACK_IMPORTED_MODULE_3__.service.network.getBlockIncluded(this.config.version, txBlockNumber).then(function (headerBlock) {
            _this.maticClient_.logger.log("block info from API 2", headerBlock);
            if (!headerBlock || !headerBlock.start || !headerBlock.end || !headerBlock.headerBlockNumber) {
                throw Error('Network API Error');
            }
            return headerBlock;
        }).catch(function (err) {
            _this.maticClient_.logger.log("block info from API", err);
            return _this.getRootBlockInfo(txBlockNumber);
        });
    };
    ExitUtil.prototype.getBlockProof = function (txBlockNumber, rootBlockInfo) {
        return _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.buildBlockProof(this.maticClient_, parseInt(rootBlockInfo.start, 10), parseInt(rootBlockInfo.end, 10), parseInt(txBlockNumber + '', 10));
    };
    ExitUtil.prototype.getBlockProofFromAPI = function (txBlockNumber, rootBlockInfo) {
        var _this = this;
        return _services__WEBPACK_IMPORTED_MODULE_3__.service.network.getProof(this.config.version, rootBlockInfo.start, rootBlockInfo.end, txBlockNumber).then(function (blockProof) {
            if (!blockProof) {
                throw Error('Network API Error');
            }
            _this.maticClient_.logger.log("block proof from API 1");
            return blockProof;
        }).catch(function (_) {
            return _this.getBlockProof(txBlockNumber, rootBlockInfo);
        });
    };
    ExitUtil.prototype.getExitProofFromAPI = function (burnHash, eventSignature) {
        var _this = this;
        return _services__WEBPACK_IMPORTED_MODULE_3__.service.network.getExitProof(this.config.version, burnHash, eventSignature).then(function (exitProof) {
            if (!exitProof) {
                throw Error('Network API Error');
            }
            _this.maticClient_.logger.log("exit proof from API 1");
            return exitProof;
        }).catch(function (_) {
            return _this.buildPayloadForExit(burnHash, eventSignature, false);
        });
    };
    ExitUtil.prototype.buildPayloadForExit = function (burnTxHash, logEventSig, isFast, index) {
        var _this = this;
        if (index === void 0) { index = 0; }
        if (isFast && !_services__WEBPACK_IMPORTED_MODULE_3__.service.network) {
            new _utils_error_helper__WEBPACK_IMPORTED_MODULE_4__.ErrorHelper(___WEBPACK_IMPORTED_MODULE_5__.ERROR_TYPE.ProofAPINotSet).throw();
        }
        if (index < 0) {
            throw new Error('Index must not be a negative integer');
        }
        var txBlockNumber, rootBlockInfo, receipt, block, blockProof;
        if (isFast) {
            return this.getExitProofFromAPI(burnTxHash, logEventSig);
        }
        return this.getChainBlockInfo(burnTxHash).then(function (blockInfo) {
            if (!_this.isCheckPointed_(blockInfo)) {
                throw new Error('Burn transaction has not been checkpointed as yet');
            }
            // step 1 - Get Block number from transaction hash
            txBlockNumber = blockInfo.txBlockNumber;
            // step 2-  get transaction receipt from txhash and 
            // block information from block number
            return Promise.all([
                _this.maticClient_.getTransactionReceipt(burnTxHash),
                _this.maticClient_.getBlockWithTransaction(txBlockNumber)
            ]);
        }).then(function (result) {
            receipt = result[0], block = result[1];
            // step  3 - get information about block saved in parent chain 
            return _this.getRootBlockInfo(txBlockNumber);
        }).then(function (rootBlockInfoResult) {
            rootBlockInfo = rootBlockInfoResult;
            // step 4 - build block proof
            return _this.getBlockProof(txBlockNumber, rootBlockInfo);
        }).then(function (blockProofResult) {
            blockProof = blockProofResult;
            // step 5- create receipt proof
            return _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptProof(receipt, block, _this.maticClient_, _this.requestConcurrency);
        }).then(function (receiptProof) {
            // step 6 - encode payload, convert into hex
            // when token index is not 0
            if (index > 0) {
                var logIndices = _this.getAllLogIndices_(logEventSig, receipt);
                if (index >= logIndices.length) {
                    throw new Error('Index is greater than the number of tokens in this transaction');
                }
                return _this.encodePayload_(rootBlockInfo.headerBlockNumber.toNumber(), blockProof, txBlockNumber, block.timestamp, Buffer.from(block.transactionsRoot.slice(2), 'hex'), Buffer.from(block.receiptsRoot.slice(2), 'hex'), _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptBytes(receipt), // rlp encoded
                receiptProof.parentNodes, receiptProof.path, logIndices[index]);
            }
            // when token index is 0
            var logIndex = _this.getLogIndex_(logEventSig, receipt);
            return _this.encodePayload_(rootBlockInfo.headerBlockNumber.toNumber(), blockProof, txBlockNumber, block.timestamp, Buffer.from(block.transactionsRoot.slice(2), 'hex'), Buffer.from(block.receiptsRoot.slice(2), 'hex'), _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptBytes(receipt), // rlp encoded
            receiptProof.parentNodes, receiptProof.path, logIndex);
        });
    };
    ExitUtil.prototype.buildMultiplePayloadsForExit = function (burnTxHash, logEventSig, isFast) {
        var _this = this;
        if (isFast && !_services__WEBPACK_IMPORTED_MODULE_3__.service.network) {
            new _utils_error_helper__WEBPACK_IMPORTED_MODULE_4__.ErrorHelper(___WEBPACK_IMPORTED_MODULE_5__.ERROR_TYPE.ProofAPINotSet).throw();
        }
        var txBlockNumber, rootBlockInfo, receipt, block, blockProof;
        return this.getChainBlockInfo(burnTxHash).then(function (blockInfo) {
            if (!isFast && !_this.isCheckPointed_(blockInfo)) {
                throw new Error('Burn transaction has not been checkpointed as yet');
            }
            // step 1 - Get Block number from transaction hash
            txBlockNumber = blockInfo.txBlockNumber;
            // step 2-  get transaction receipt from txhash and 
            // block information from block number
            return Promise.all([
                _this.maticClient_.getTransactionReceipt(burnTxHash),
                _this.maticClient_.getBlockWithTransaction(txBlockNumber)
            ]);
        }).then(function (result) {
            receipt = result[0], block = result[1];
            // step  3 - get information about block saved in parent chain 
            return (isFast ? _this.getRootBlockInfoFromAPI(txBlockNumber) :
                _this.getRootBlockInfo(txBlockNumber));
        }).then(function (rootBlockInfoResult) {
            rootBlockInfo = rootBlockInfoResult;
            // step 4 - build block proof
            return (isFast ? _this.getBlockProofFromAPI(txBlockNumber, rootBlockInfo) :
                _this.getBlockProof(txBlockNumber, rootBlockInfo));
        }).then(function (blockProofResult) {
            blockProof = blockProofResult;
            // step 5- create receipt proof
            return _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptProof(receipt, block, _this.maticClient_, _this.requestConcurrency);
        }).then(function (receiptProof) {
            var logIndices = _this.getAllLogIndices_(logEventSig, receipt);
            var payloads = [];
            // step 6 - encode payloads, convert into hex
            for (var _i = 0, logIndices_1 = logIndices; _i < logIndices_1.length; _i++) {
                var logIndex = logIndices_1[_i];
                payloads.push(_this.encodePayload_(rootBlockInfo.headerBlockNumber.toNumber(), blockProof, txBlockNumber, block.timestamp, Buffer.from(block.transactionsRoot.slice(2), 'hex'), Buffer.from(block.receiptsRoot.slice(2), 'hex'), _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptBytes(receipt), // rlp encoded
                receiptProof.parentNodes, receiptProof.path, logIndex));
            }
            return payloads;
        });
    };
    ExitUtil.prototype.encodePayload_ = function (headerNumber, buildBlockProof, blockNumber, timestamp, transactionsRoot, receiptsRoot, receipt, receiptParentNodes, path, logIndex) {
        return _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(rlp__WEBPACK_IMPORTED_MODULE_2___default().encode([
            headerNumber,
            buildBlockProof,
            blockNumber,
            timestamp,
            _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(transactionsRoot),
            _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(receiptsRoot),
            _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(receipt),
            _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(rlp__WEBPACK_IMPORTED_MODULE_2___default().encode(receiptParentNodes)),
            _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(Buffer.concat([Buffer.from('00', 'hex'), path])),
            logIndex,
        ]));
    };
    ExitUtil.prototype.getExitHash = function (burnTxHash, index, logEventSig) {
        var _this = this;
        var lastChildBlock, receipt, block;
        return Promise.all([
            this.rootChain.getLastChildBlock(),
            this.maticClient_.getTransactionReceipt(burnTxHash)
        ]).then(function (result) {
            lastChildBlock = result[0];
            receipt = result[1];
            return _this.maticClient_.getBlockWithTransaction(receipt.blockNumber);
        }).then(function (blockResult) {
            block = blockResult;
            if (!_this.isCheckPointed_({ lastChildBlock: lastChildBlock, txBlockNumber: receipt.blockNumber })) {
                _this.maticClient_.logger.error(___WEBPACK_IMPORTED_MODULE_5__.ERROR_TYPE.BurnTxNotCheckPointed).throw();
            }
            return _utils__WEBPACK_IMPORTED_MODULE_0__.ProofUtil.getReceiptProof(receipt, block, _this.maticClient_, _this.requestConcurrency);
        }).then(function (receiptProof) {
            var logIndex;
            var nibbleArr = [];
            receiptProof.path.forEach(function (byte) {
                nibbleArr.push(Buffer.from('0' + (byte / 0x10).toString(16), 'hex'));
                nibbleArr.push(Buffer.from('0' + (byte % 0x10).toString(16), 'hex'));
            });
            if (index > 0) {
                var logIndices = _this.getAllLogIndices_(logEventSig, receipt);
                logIndex = logIndices[index];
            }
            logIndex = _this.getLogIndex_(logEventSig, receipt);
            return _this.maticClient_.etheriumSha3(receipt.blockNumber, _utils_buffer_utils__WEBPACK_IMPORTED_MODULE_1__.BufferUtil.bufferToHex(Buffer.concat(nibbleArr)), logIndex);
        });
    };
    return ExitUtil;
}());



/***/ }),

/***/ "./src/pos/gas_swapper.ts":
/*!********************************!*\
  !*** ./src/pos/gas_swapper.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GasSwapper: () => (/* binding */ GasSwapper)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var GasSwapper = /** @class */ (function (_super) {
    __extends(GasSwapper, _super);
    function GasSwapper(client_, address) {
        return _super.call(this, {
            address: address,
            name: 'GasSwapper',
            bridgeType: 'pos',
            isParent: true
        }, client_) || this;
    }
    GasSwapper.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    GasSwapper.prototype.depositWithGas = function (tokenAddress, depositAmount, userAddress, swapCallData, option) {
        var _this = this;
        return this.method("swapAndBridge", tokenAddress, depositAmount, userAddress, swapCallData).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    return GasSwapper;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/pos/index.ts":
/*!**************************!*\
  !*** ./src/pos/index.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ExitUtil: () => (/* reexport safe */ _exit_util__WEBPACK_IMPORTED_MODULE_3__.ExitUtil),
/* harmony export */   GasSwapper: () => (/* reexport safe */ _gas_swapper__WEBPACK_IMPORTED_MODULE_7__.GasSwapper),
/* harmony export */   POSClient: () => (/* binding */ POSClient),
/* harmony export */   RootChain: () => (/* reexport safe */ _root_chain__WEBPACK_IMPORTED_MODULE_4__.RootChain),
/* harmony export */   RootChainManager: () => (/* reexport safe */ _root_chain_manager__WEBPACK_IMPORTED_MODULE_1__.RootChainManager)
/* harmony export */ });
/* harmony import */ var _erc20__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./erc20 */ "./src/pos/erc20.ts");
/* harmony import */ var _root_chain_manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./root_chain_manager */ "./src/pos/root_chain_manager.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _exit_util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./exit_util */ "./src/pos/exit_util.ts");
/* harmony import */ var _root_chain__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./root_chain */ "./src/pos/root_chain.ts");
/* harmony import */ var _erc721__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./erc721 */ "./src/pos/erc721.ts");
/* harmony import */ var _erc1155__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./erc1155 */ "./src/pos/erc1155.ts");
/* harmony import */ var _gas_swapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./gas_swapper */ "./src/pos/gas_swapper.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();












var POSClient = /** @class */ (function (_super) {
    __extends(POSClient, _super);
    function POSClient() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    POSClient.prototype.init = function (config) {
        var _this = this;
        var client = this.client;
        return client.init(config).then(function (_) {
            var mainPOSContracts = client.mainPOSContracts;
            client.config = config = Object.assign({
                rootChainManager: mainPOSContracts.RootChainManagerProxy,
                rootChain: client.mainPlasmaContracts.RootChainProxy,
                gasSwapper: mainPOSContracts.GasSwapper
            }, config);
            _this.rootChainManager = new _root_chain_manager__WEBPACK_IMPORTED_MODULE_1__.RootChainManager(_this.client, config.rootChainManager);
            var rootChain = new _root_chain__WEBPACK_IMPORTED_MODULE_4__.RootChain(_this.client, config.rootChain);
            _this.exitUtil = new _exit_util__WEBPACK_IMPORTED_MODULE_3__.ExitUtil(_this.client, rootChain);
            _this.gasSwapper = new _gas_swapper__WEBPACK_IMPORTED_MODULE_7__.GasSwapper(_this.client, config.gasSwapper);
            return _this;
        });
    };
    POSClient.prototype.erc20 = function (tokenAddress, isParent) {
        return new _erc20__WEBPACK_IMPORTED_MODULE_0__.ERC20(tokenAddress, isParent, this.client, this.getContracts_.bind(this));
    };
    POSClient.prototype.erc721 = function (tokenAddress, isParent) {
        return new _erc721__WEBPACK_IMPORTED_MODULE_5__.ERC721(tokenAddress, isParent, this.client, this.getContracts_.bind(this));
    };
    POSClient.prototype.erc1155 = function (tokenAddress, isParent) {
        return new _erc1155__WEBPACK_IMPORTED_MODULE_6__.ERC1155(tokenAddress, isParent, this.client, this.getContracts_.bind(this));
    };
    POSClient.prototype.depositEther = function (amount, userAddress, option) {
        return new _erc20__WEBPACK_IMPORTED_MODULE_0__.ERC20('', true, this.client, this.getContracts_.bind(this))['depositEther_'](amount, userAddress, option);
    };
    POSClient.prototype.depositEtherWithGas = function (amount, userAddress, swapEthAmount, swapCallData, option) {
        return new _erc20__WEBPACK_IMPORTED_MODULE_0__.ERC20('', true, this.client, this.getContracts_.bind(this))['depositEtherWithGas_'](amount, userAddress, swapEthAmount, swapCallData, option);
    };
    POSClient.prototype.getContracts_ = function () {
        return {
            exitUtil: this.exitUtil,
            rootChainManager: this.rootChainManager,
            gasSwapper: this.gasSwapper
        };
    };
    return POSClient;
}(_utils__WEBPACK_IMPORTED_MODULE_2__.BridgeClient));



/***/ }),

/***/ "./src/pos/pos_token.ts":
/*!******************************!*\
  !*** ./src/pos/pos_token.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   POSToken: () => (/* binding */ POSToken)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();

var POSToken = /** @class */ (function (_super) {
    __extends(POSToken, _super);
    function POSToken(contractParam, client, getPOSContracts) {
        var _this = _super.call(this, contractParam, client) || this;
        _this.getPOSContracts = getPOSContracts;
        return _this;
    }
    Object.defineProperty(POSToken.prototype, "rootChainManager", {
        get: function () {
            return this.getPOSContracts().rootChainManager;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(POSToken.prototype, "gasSwapper", {
        get: function () {
            return this.getPOSContracts().gasSwapper;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(POSToken.prototype, "exitUtil", {
        get: function () {
            return this.getPOSContracts().exitUtil;
        },
        enumerable: false,
        configurable: true
    });
    POSToken.prototype.getPredicateAddress = function () {
        var _this = this;
        if (this.predicateAddress) {
            return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.promiseResolve)(this.predicateAddress);
        }
        return this.rootChainManager.method("tokenToType", this.contractParam.address).then(function (method) {
            return method.read();
        }).then(function (tokenType) {
            if (!tokenType) {
                throw new Error('Invalid Token Type');
            }
            return _this.rootChainManager.method("typeToPredicate", tokenType);
        }).then(function (typeToPredicateMethod) {
            return typeToPredicateMethod.read();
        }).then(function (predicateAddress) {
            _this.predicateAddress = predicateAddress;
            return predicateAddress;
        });
    };
    POSToken.prototype.isWithdrawn = function (txHash, eventSignature) {
        var _this = this;
        if (!txHash) {
            throw new Error("txHash not provided");
        }
        return this.exitUtil.getExitHash(txHash, 0, eventSignature).then(function (exitHash) {
            return _this.rootChainManager.isExitProcessed(exitHash);
        });
    };
    POSToken.prototype.isWithdrawnOnIndex = function (txHash, index, eventSignature) {
        var _this = this;
        if (!txHash) {
            throw new Error("txHash not provided");
        }
        return this.exitUtil.getExitHash(txHash, index, eventSignature).then(function (exitHash) {
            return _this.rootChainManager.isExitProcessed(exitHash);
        });
    };
    POSToken.prototype.withdrawExitPOS = function (burnTxHash, eventSignature, isFast, option) {
        var _this = this;
        return this.exitUtil.buildPayloadForExit(burnTxHash, eventSignature, isFast).then(function (payload) {
            return _this.rootChainManager.exit(payload, option);
        });
    };
    return POSToken;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/pos/root_chain.ts":
/*!*******************************!*\
  !*** ./src/pos/root_chain.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RootChain: () => (/* binding */ RootChain)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (undefined && undefined.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var RootChain = /** @class */ (function (_super) {
    __extends(RootChain, _super);
    function RootChain(client_, address) {
        return _super.call(this, {
            address: address,
            name: 'RootChain',
            isParent: true
        }, client_) || this;
    }
    RootChain.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    RootChain.prototype.getLastChildBlock = function () {
        var _this = this;
        return this.method("getLastChildBlock").then(function (method) {
            return method.read({}, _this.client.config.rootChainDefaultBlock || 'safe');
        });
    };
    RootChain.prototype.findRootBlockFromChild = function (childBlockNumber) {
        return __awaiter(this, void 0, void 0, function () {
            var bigOne, bigtwo, checkPointInterval, start, method, currentHeaderBlock, end, ans, mid, headerBlocksMethod, headerBlock, headerStart, headerEnd;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        bigOne = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(1);
                        bigtwo = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(2);
                        checkPointInterval = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(10000);
                        childBlockNumber = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(childBlockNumber);
                        start = bigOne;
                        return [4 /*yield*/, this.method("currentHeaderBlock")];
                    case 1:
                        method = _a.sent();
                        return [4 /*yield*/, method.read()];
                    case 2:
                        currentHeaderBlock = _a.sent();
                        end = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(currentHeaderBlock).div(checkPointInterval);
                        _a.label = 3;
                    case 3:
                        if (!start.lte(end)) return [3 /*break*/, 6];
                        if (start.eq(end)) {
                            ans = start;
                            return [3 /*break*/, 6];
                        }
                        mid = start.add(end).div(bigtwo);
                        return [4 /*yield*/, this.method("headerBlocks", mid.mul(checkPointInterval).toString())];
                    case 4:
                        headerBlocksMethod = _a.sent();
                        return [4 /*yield*/, headerBlocksMethod.read()];
                    case 5:
                        headerBlock = _a.sent();
                        headerStart = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(headerBlock.start);
                        headerEnd = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(headerBlock.end);
                        if (headerStart.lte(childBlockNumber) && childBlockNumber.lte(headerEnd)) {
                            // if childBlockNumber is between the upper and lower bounds of the headerBlock, we found our answer
                            ans = mid;
                            return [3 /*break*/, 6];
                        }
                        else if (headerStart.gt(childBlockNumber)) {
                            // childBlockNumber was checkpointed before this header
                            end = mid.sub(bigOne);
                        }
                        else if (headerEnd.lt(childBlockNumber)) {
                            // childBlockNumber was checkpointed after this header
                            start = mid.add(bigOne);
                        }
                        return [3 /*break*/, 3];
                    case 6: return [2 /*return*/, ans.mul(checkPointInterval)];
                }
            });
        });
    };
    return RootChain;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/pos/root_chain_manager.ts":
/*!***************************************!*\
  !*** ./src/pos/root_chain_manager.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RootChainManager: () => (/* binding */ RootChainManager)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var RootChainManager = /** @class */ (function (_super) {
    __extends(RootChainManager, _super);
    function RootChainManager(client_, address) {
        return _super.call(this, {
            address: address,
            name: 'RootChainManager',
            bridgeType: 'pos',
            isParent: true
        }, client_) || this;
    }
    RootChainManager.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    RootChainManager.prototype.deposit = function (userAddress, tokenAddress, depositData, option) {
        var _this = this;
        return this.method("depositFor", userAddress, tokenAddress, depositData).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    RootChainManager.prototype.exit = function (exitPayload, option) {
        var _this = this;
        return this.method("exit", exitPayload).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    RootChainManager.prototype.isExitProcessed = function (exitHash) {
        var _this = this;
        return this.method("processedExits", exitHash).then(function (method) {
            return _this.processRead(method);
        });
    };
    return RootChainManager;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/services/abi_service.ts":
/*!*************************************!*\
  !*** ./src/services/abi_service.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ABIService: () => (/* binding */ ABIService)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");

var ABIService = /** @class */ (function () {
    function ABIService(baseUrl) {
        this.httpRequest = new _utils__WEBPACK_IMPORTED_MODULE_0__.HttpRequest(baseUrl);
    }
    ABIService.prototype.getABI = function (network, version, bridgeType, contractName) {
        var url = "".concat(network, "/").concat(version, "/artifacts/").concat(bridgeType, "/").concat(contractName, ".json");
        return this.httpRequest.get(url).then(function (result) {
            return result.abi;
        });
    };
    ABIService.prototype.getAddress = function (network, version) {
        var url = "".concat(network, "/").concat(version, "/index.json");
        return this.httpRequest.get(url);
    };
    return ABIService;
}());



/***/ }),

/***/ "./src/services/index.ts":
/*!*******************************!*\
  !*** ./src/services/index.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NetworkService: () => (/* reexport safe */ _network_service__WEBPACK_IMPORTED_MODULE_2__.NetworkService),
/* harmony export */   service: () => (/* binding */ service)
/* harmony export */ });
/* harmony import */ var _abi_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abi_service */ "./src/services/abi_service.ts");
/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../config */ "./src/config.ts");
/* harmony import */ var _network_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./network_service */ "./src/services/network_service.ts");



var Service = /** @class */ (function () {
    function Service() {
    }
    return Service;
}());
var service = new Service();
service.abi = new _abi_service__WEBPACK_IMPORTED_MODULE_0__.ABIService(_config__WEBPACK_IMPORTED_MODULE_1__.config.abiStoreUrl);


/***/ }),

/***/ "./src/services/network_service.ts":
/*!*****************************************!*\
  !*** ./src/services/network_service.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NetworkService: () => (/* binding */ NetworkService)
/* harmony export */ });
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! .. */ "./src/index.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");


var NetworkService = /** @class */ (function () {
    function NetworkService(baseUrl) {
        this.httpRequest = new _utils__WEBPACK_IMPORTED_MODULE_1__.HttpRequest(baseUrl);
    }
    NetworkService.prototype.createUrlForPos = function (version, url) {
        return "".concat(version === 'v1' ? 'matic' : version).concat(url);
    };
    NetworkService.prototype.createUrlForZkEvm = function (version, url) {
        return "".concat(version, "/").concat(url);
    };
    NetworkService.prototype.getBlockIncluded = function (version, blockNumber) {
        var url = this.createUrlForPos(version, "/block-included/".concat(blockNumber));
        return this.httpRequest.get(url).then(function (result) {
            var headerBlockNumber = result.headerBlockNumber;
            var decimalHeaderBlockNumber = headerBlockNumber.slice(0, 2) === '0x' ? parseInt(headerBlockNumber, 16) : headerBlockNumber;
            result.headerBlockNumber = new ___WEBPACK_IMPORTED_MODULE_0__.utils.BN(decimalHeaderBlockNumber);
            return result;
        });
    };
    NetworkService.prototype.getExitProof = function (version, burnTxHash, eventSignature) {
        var url = this.createUrlForPos(version, "/exit-payload/".concat(burnTxHash, "?eventSignature=").concat(eventSignature));
        return this.httpRequest.get(url).then(function (result) {
            return result.result;
        });
    };
    NetworkService.prototype.getProof = function (version, start, end, blockNumber) {
        var url = this.createUrlForPos(version, "/fast-merkle-proof?start=".concat(start, "&end=").concat(end, "&number=").concat(blockNumber));
        return this.httpRequest.get(url).then(function (result) {
            return result.proof;
        });
    };
    NetworkService.prototype.getMerkleProofForZkEvm = function (version, networkID, depositCount) {
        var url = this.createUrlForZkEvm(version, "merkle-proof?net_id=".concat(networkID, "&deposit_cnt=").concat(depositCount));
        return this.httpRequest.get(url).then(function (result) {
            return result.proof;
        });
    };
    NetworkService.prototype.getBridgeTransactionDetails = function (version, networkID, depositCount) {
        var url = this.createUrlForZkEvm(version, "bridge?net_id=".concat(networkID, "&deposit_cnt=").concat(depositCount));
        return this.httpRequest.get(url).then(function (result) {
            return result.deposit;
        });
    };
    return NetworkService;
}());



/***/ }),

/***/ "./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _pos_erc1155_deposit_param__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pos_erc1155_deposit_param */ "./src/types/pos_erc1155_deposit_param.ts");
/* harmony import */ var _pos_erc1155_transfer_param__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pos_erc1155_transfer_param */ "./src/types/pos_erc1155_transfer_param.ts");




/***/ }),

/***/ "./src/types/pos_erc1155_deposit_param.ts":
/*!************************************************!*\
  !*** ./src/types/pos_erc1155_deposit_param.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/types/pos_erc1155_transfer_param.ts":
/*!*************************************************!*\
  !*** ./src/types/pos_erc1155_transfer_param.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);



/***/ }),

/***/ "./src/utils/abi_manager.ts":
/*!**********************************!*\
  !*** ./src/utils/abi_manager.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ABIManager: () => (/* binding */ ABIManager)
/* harmony export */ });
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! . */ "./src/utils/index.ts");


var cache = {};
var ABIManager = /** @class */ (function () {
    function ABIManager(networkName, version) {
        this.networkName = networkName;
        this.version = version;
    }
    ABIManager.prototype.init = function () {
        var _this = this;
        return _services__WEBPACK_IMPORTED_MODULE_0__.service.abi.getAddress(this.networkName, this.version).then(function (result) {
            var _a;
            cache[_this.networkName] = (_a = {},
                _a[_this.version] = {
                    address: result,
                    abi: {}
                },
                _a);
        });
    };
    ABIManager.prototype.getConfig = function (path) {
        return (0,___WEBPACK_IMPORTED_MODULE_1__.resolve)(cache[this.networkName][this.version].address, path);
    };
    ABIManager.prototype.getABI = function (contractName, bridgeType) {
        var _this = this;
        if (bridgeType === void 0) { bridgeType = 'plasma'; }
        var targetBridgeABICache;
        if (cache[this.networkName] && cache[this.networkName][this.version] &&
            cache[this.networkName][this.version].abi) {
            targetBridgeABICache = cache[this.networkName][this.version].abi[bridgeType];
        }
        if (targetBridgeABICache) {
            var abiForContract = targetBridgeABICache[contractName];
            if (abiForContract) {
                return (0,___WEBPACK_IMPORTED_MODULE_1__.promiseResolve)(abiForContract);
            }
        }
        return _services__WEBPACK_IMPORTED_MODULE_0__.service.abi.getABI(this.networkName, this.version, bridgeType, contractName).then(function (result) {
            _this.setABI(contractName, bridgeType, result);
            return result;
        });
    };
    ABIManager.prototype.setABI = function (contractName, bridgeType, abi) {
        var abiStore = cache[this.networkName][this.version].abi;
        if (!abiStore[bridgeType]) {
            abiStore[bridgeType] = {};
        }
        abiStore[bridgeType][contractName] = abi;
    };
    return ABIManager;
}());



/***/ }),

/***/ "./src/utils/base_token.ts":
/*!*********************************!*\
  !*** ./src/utils/base_token.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BaseToken: () => (/* binding */ BaseToken)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _promise_resolve__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./promise_resolve */ "./src/utils/promise_resolve.ts");
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../enums */ "./src/enums/index.ts");
/* harmony import */ var _error_helper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./error_helper */ "./src/utils/error_helper.ts");
/* harmony import */ var _constant__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../constant */ "./src/constant.ts");
var __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (undefined && undefined.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};





var BaseToken = /** @class */ (function () {
    function BaseToken(contractParam, client) {
        this.contractParam = contractParam;
        this.client = client;
    }
    Object.defineProperty(BaseToken.prototype, "contractAddress", {
        get: function () {
            return this.contractParam.address;
        },
        enumerable: false,
        configurable: true
    });
    BaseToken.prototype.getContract = function () {
        var _this = this;
        if (this.contract_) {
            return (0,_promise_resolve__WEBPACK_IMPORTED_MODULE_1__.promiseResolve)(this.contract_);
        }
        var contractParam = this.contractParam;
        return this.client.getABI(contractParam.name, contractParam.bridgeType).then(function (abi) {
            _this.contract_ = _this.getContract_({
                abi: abi,
                isParent: contractParam.isParent,
                tokenAddress: contractParam.address
            });
            return _this.contract_;
        });
    };
    BaseToken.prototype.getChainId = function () {
        var _this = this;
        if (this.chainId_) {
            return (0,_promise_resolve__WEBPACK_IMPORTED_MODULE_1__.promiseResolve)(this.chainId_);
        }
        var client = this.getClient(this.contractParam.isParent);
        return client.getChainId().then(function (chainId) {
            _this.chainId_ = chainId;
            return _this.chainId_;
        });
    };
    BaseToken.prototype.processWrite = function (method, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.validateTxOption_(option);
        this.client.logger.log("process write");
        return this.createTransactionConfig({
            txConfig: option,
            isWrite: true,
            method: method,
            isParent: this.contractParam.isParent
        }).then(function (config) {
            _this.client.logger.log("process write config");
            if (option.returnTransaction) {
                return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.merge)(config, {
                    data: method.encodeABI(),
                    to: method.address
                });
            }
            var methodResult = method.write(config);
            return methodResult;
        });
    };
    BaseToken.prototype.sendTransaction = function (option) {
        if (option === void 0) { option = {}; }
        this.validateTxOption_(option);
        var isParent = this.contractParam.isParent;
        var client = this.getClient(isParent);
        client.logger.log("process write");
        return this.createTransactionConfig({
            txConfig: option,
            isWrite: true,
            method: null,
            isParent: this.contractParam.isParent
        }).then(function (config) {
            client.logger.log("process write config");
            if (option.returnTransaction) {
                return config;
            }
            var methodResult = client.write(config);
            return methodResult;
        });
    };
    BaseToken.prototype.readTransaction = function (option) {
        if (option === void 0) { option = {}; }
        this.validateTxOption_(option);
        var isParent = this.contractParam.isParent;
        var client = this.getClient(isParent);
        client.logger.log("process read");
        return this.createTransactionConfig({
            txConfig: option,
            isWrite: true,
            method: null,
            isParent: this.contractParam.isParent
        }).then(function (config) {
            client.logger.log("write tx config created");
            if (option.returnTransaction) {
                return config;
            }
            return client.read(config);
        });
    };
    BaseToken.prototype.validateTxOption_ = function (option) {
        if (typeof option !== 'object' || Array.isArray(option)) {
            new _error_helper__WEBPACK_IMPORTED_MODULE_3__.ErrorHelper(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.TransactionOptionNotObject).throw();
        }
    };
    BaseToken.prototype.processRead = function (method, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.validateTxOption_(option);
        this.client.logger.log("process read");
        return this.createTransactionConfig({
            txConfig: option,
            isWrite: false,
            method: method,
            isParent: this.contractParam.isParent
        }).then(function (config) {
            _this.client.logger.log("read tx config created");
            if (option.returnTransaction) {
                return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.merge)(config, {
                    data: method.encodeABI(),
                    to: _this.contract_.address
                });
            }
            return method.read(config);
        });
    };
    BaseToken.prototype.getClient = function (isParent) {
        return isParent ? this.client.parent :
            this.client.child;
    };
    BaseToken.prototype.getContract_ = function (_a) {
        var isParent = _a.isParent, tokenAddress = _a.tokenAddress, abi = _a.abi;
        var client = this.getClient(isParent);
        return client.getContract(tokenAddress, abi);
    };
    Object.defineProperty(BaseToken.prototype, "parentDefaultConfig", {
        get: function () {
            var config = this.client.config;
            return config.parent.defaultConfig;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(BaseToken.prototype, "childDefaultConfig", {
        get: function () {
            var config = this.client.config;
            return config.child.defaultConfig;
        },
        enumerable: false,
        configurable: true
    });
    BaseToken.prototype.createTransactionConfig = function (_a) {
        var _this = this;
        var txConfig = _a.txConfig, method = _a.method, isParent = _a.isParent, isWrite = _a.isWrite;
        var defaultConfig = isParent ? this.parentDefaultConfig : this.childDefaultConfig;
        txConfig = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.merge)(defaultConfig, (txConfig || {}));
        var client = isParent ? this.client.parent :
            this.client.child;
        client.logger.log("txConfig", txConfig, "onRoot", isParent, "isWrite", isWrite);
        var estimateGas = function (config) { return __awaiter(_this, void 0, void 0, function () {
            var result, _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!method) return [3 /*break*/, 2];
                        return [4 /*yield*/, method.estimateGas(config)];
                    case 1:
                        _a = _b.sent();
                        return [3 /*break*/, 4];
                    case 2: return [4 /*yield*/, client.estimateGas(config)];
                    case 3:
                        _a = _b.sent();
                        _b.label = 4;
                    case 4:
                        result = _a;
                        return [2 /*return*/, new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(Math.trunc(Number(result) * 1.15)).toString()];
                }
            });
        }); };
        // txConfig.chainId = Converter.toHex(txConfig.chainId) as any;
        if (isWrite) {
            return this.getChainId().then(function (clientChainId) {
                var maxFeePerGas = txConfig.maxFeePerGas, maxPriorityFeePerGas = txConfig.maxPriorityFeePerGas;
                var isEIP1559Supported = _this.client.isEIP1559Supported(clientChainId);
                var isMaxFeeProvided = (maxFeePerGas || maxPriorityFeePerGas);
                txConfig.chainId = txConfig.chainId || clientChainId;
                if (!isEIP1559Supported && isMaxFeeProvided) {
                    client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.EIP1559NotSupported, isParent).throw();
                }
                // const [gasLimit, nonce] = 
                return Promise.all([
                    !(txConfig.gasLimit)
                        ? estimateGas({
                            from: txConfig.from, value: txConfig.value, to: txConfig.to
                        })
                        : txConfig.gasLimit,
                    !txConfig.nonce ?
                        client.getTransactionCount(txConfig.from, 'pending')
                        : txConfig.nonce
                ]).then(function (result) {
                    var gasLimit = result[0], nonce = result[1];
                    client.logger.log("options filled");
                    txConfig.gasLimit = Number(gasLimit);
                    txConfig.nonce = nonce;
                    return txConfig;
                });
            });
        }
        return (0,_promise_resolve__WEBPACK_IMPORTED_MODULE_1__.promiseResolve)(txConfig);
    };
    BaseToken.prototype.transferERC20 = function (to, amount, option) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("transfer", to, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount));
            return _this.processWrite(method, option);
        });
    };
    BaseToken.prototype.transferERC721 = function (from, to, tokenId, option) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("transferFrom", from, to, tokenId);
            return _this.processWrite(method, option);
        });
    };
    BaseToken.prototype.checkForNonNative = function (methodName) {
        if (this.contractParam.address === _constant__WEBPACK_IMPORTED_MODULE_4__.ADDRESS_ZERO) {
            this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.AllowedOnNonNativeTokens, methodName).throw();
        }
    };
    BaseToken.prototype.checkForRoot = function (methodName) {
        if (!this.contractParam.isParent) {
            this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.AllowedOnRoot, methodName).throw();
        }
    };
    BaseToken.prototype.checkForChild = function (methodName) {
        if (this.contractParam.isParent) {
            this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.AllowedOnChild, methodName).throw();
        }
    };
    BaseToken.prototype.checkAdapterPresent = function (methodName) {
        if (!this.contractParam.bridgeAdapterAddress) {
            this.client.logger.error(_enums__WEBPACK_IMPORTED_MODULE_2__.ERROR_TYPE.BridgeAdapterNotFound, methodName).throw();
        }
    };
    BaseToken.prototype.transferERC1155 = function (param, option) {
        var _this = this;
        return this.getContract().then(function (contract) {
            var method = contract.method("safeTransferFrom", param.from, param.to, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(param.tokenId), _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(param.amount), param.data || '0x');
            return _this.processWrite(method, option);
        });
    };
    return BaseToken;
}());



/***/ }),

/***/ "./src/utils/bridge_client.ts":
/*!************************************!*\
  !*** ./src/utils/bridge_client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BridgeClient: () => (/* binding */ BridgeClient)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! .. */ "./src/index.ts");


var BridgeClient = /** @class */ (function () {
    function BridgeClient() {
        this.client = new _utils__WEBPACK_IMPORTED_MODULE_0__.Web3SideChainClient();
    }
    /**
     * check whether a txHash is checkPointed
     *
     * @param {string} txHash
     * @returns
     * @memberof BridgeClient
     */
    BridgeClient.prototype.isCheckPointed = function (txHash) {
        return this.exitUtil.isCheckPointed(txHash);
    };
    BridgeClient.prototype.isDeposited = function (depositTxHash) {
        var client = this.client;
        var token = new ___WEBPACK_IMPORTED_MODULE_1__.BaseToken({
            address: client.abiManager.getConfig("Matic.GenesisContracts.StateReceiver"),
            isParent: false,
            name: 'StateReceiver',
            bridgeType: 'genesis'
        }, client);
        return token.getContract().then(function (contract) {
            return Promise.all([
                client.parent.getTransactionReceipt(depositTxHash),
                token['processRead'](contract.method("lastStateId"))
            ]);
        }).then(function (result) {
            var receipt = result[0], lastStateId = result[1];
            var eventSignature = "0x103fed9db65eac19c4d870f49ab7520fe03b99f1838e5996caf47e9e43308392";
            var targetLog = receipt.logs.find(function (q) { return q.topics[0] === eventSignature; });
            if (!targetLog) {
                throw new Error("StateSynced event not found");
            }
            var rootStateId = client.child.decodeParameters(targetLog.topics[1], ['uint256'])[0];
            var rootStateIdBN = ___WEBPACK_IMPORTED_MODULE_1__.utils.BN.isBN(rootStateId) ? rootStateId : new ___WEBPACK_IMPORTED_MODULE_1__.utils.BN(rootStateId);
            return new ___WEBPACK_IMPORTED_MODULE_1__.utils.BN(lastStateId).gte(rootStateIdBN);
        });
    };
    return BridgeClient;
}());



/***/ }),

/***/ "./src/utils/buffer-utils.ts":
/*!***********************************!*\
  !*** ./src/utils/buffer-utils.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BufferUtil: () => (/* binding */ BufferUtil)
/* harmony export */ });
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ "./src/utils/types.ts");

var BufferUtil = /** @class */ (function () {
    function BufferUtil() {
    }
    BufferUtil.padToEven = function (value) {
        var a = value;
        if (typeof a !== 'string') {
            throw new Error("[padToEven] value must be type 'string', received ".concat(typeof a));
        }
        if (a.length % 2)
            a = "0".concat(a);
        return a;
    };
    BufferUtil.isHexPrefixed = function (str) {
        if (typeof str !== 'string') {
            throw new Error("[isHexPrefixed] input must be type 'string', received type ".concat(typeof str));
        }
        return str[0] === '0' && str[1] === 'x';
    };
    BufferUtil.isHexString = function (value, length) {
        if (typeof value !== 'string' || !value.match(/^0x[0-9A-Fa-f]*$/))
            return false;
        if (length && value.length !== 2 + 2 * length)
            return false;
        return true;
    };
    BufferUtil.intToHex = function (i) {
        if (!Number.isSafeInteger(i) || i < 0) {
            throw new Error("Received an invalid integer type: ".concat(i));
        }
        return "0x".concat(i.toString(16));
    };
    BufferUtil.stripHexPrefix = function (str) {
        if (typeof str !== 'string') {
            throw new Error("[stripHexPrefix] input must be type 'string', received ".concat(typeof str));
        }
        return BufferUtil.isHexPrefixed(str) ? str.slice(2) : str;
    };
    /**
     * Converts an `Number` to a `Buffer`
     * @param {Number} i
     * @return {Buffer}
     */
    BufferUtil.intToBuffer = function (i) {
        var hex = BufferUtil.intToHex(i);
        return Buffer.from(BufferUtil.padToEven(hex.slice(2)), 'hex');
    };
    BufferUtil.toBuffer = function (v) {
        if (v === null || v === undefined) {
            return Buffer.allocUnsafe(0);
        }
        if (Buffer.isBuffer(v)) {
            return Buffer.from(v);
        }
        if (Array.isArray(v) || v instanceof Uint8Array) {
            return Buffer.from(v);
        }
        if (typeof v === 'string') {
            if (!BufferUtil.isHexString(v)) {
                throw new Error("Cannot convert string to buffer. toBuffer only supports 0x-prefixed hex strings and this string was given: ".concat(v));
            }
            return Buffer.from(BufferUtil.padToEven(BufferUtil.stripHexPrefix(v)), 'hex');
        }
        if (typeof v === 'number') {
            return BufferUtil.intToBuffer(v);
        }
        if (_types__WEBPACK_IMPORTED_MODULE_0__.BN.isBN(v)) {
            if (v.isNeg()) {
                throw new Error("Cannot convert negative BN to buffer. Given: ".concat(v));
            }
            return v.toArrayLike(Buffer);
        }
        if (v.toArray) {
            // converts a BN to a Buffer
            return Buffer.from(v.toArray());
        }
        if (v.toBuffer) {
            return Buffer.from(v.toBuffer());
        }
        throw new Error('invalid type');
    };
    /**
     * Converts a `Buffer` into a `0x`-prefixed hex `String`.
     * @param buf `Buffer` object to convert
     */
    BufferUtil.bufferToHex = function (buf) {
        buf = BufferUtil.toBuffer(buf);
        return '0x' + buf.toString('hex');
    };
    return BufferUtil;
}());



/***/ }),

/***/ "./src/utils/converter.ts":
/*!********************************!*\
  !*** ./src/utils/converter.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Converter: () => (/* binding */ Converter)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");

var Converter = /** @class */ (function () {
    function Converter() {
    }
    Converter.toHex = function (amount) {
        var dataType = typeof amount;
        if (dataType === 'number') {
            amount = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(amount);
        }
        else if (dataType === 'string') {
            if (amount.slice(0, 2) === '0x') {
                return amount;
            }
            amount = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(amount);
        }
        if (_utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN.isBN(amount)) {
            return '0x' + amount.toString(16);
        }
        else {
            throw new Error("Invalid value ".concat(amount, ", value is not a number."));
        }
    };
    Converter.toBN = function (amount) {
        var dataType = typeof amount;
        if (dataType === 'string') {
            if (amount.slice(0, 2) === '0x') {
                amount = parseInt(amount, 16);
            }
        }
        if (!_utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN.isBN(amount)) {
            amount = new _utils__WEBPACK_IMPORTED_MODULE_0__.utils.BN(amount);
        }
        return amount;
    };
    return Converter;
}());



/***/ }),

/***/ "./src/utils/error_helper.ts":
/*!***********************************!*\
  !*** ./src/utils/error_helper.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorHelper: () => (/* binding */ ErrorHelper)
/* harmony export */ });
/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../enums */ "./src/enums/index.ts");

var ErrorHelper = /** @class */ (function () {
    function ErrorHelper(type, info) {
        this.type = type;
        this.message = this.getMsg_(info);
    }
    ErrorHelper.prototype.throw = function () {
        throw this.get();
    };
    ErrorHelper.prototype.get = function () {
        return {
            message: this.message,
            type: this.type
        };
    };
    ErrorHelper.prototype.getMsg_ = function (info) {
        var errMsg;
        switch (this.type) {
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.AllowedOnChild:
                errMsg = "The action ".concat(info, " is allowed only on child token.");
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.AllowedOnRoot:
                errMsg = "The action ".concat(info, " is allowed only on root token.");
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.AllowedOnMainnet:
                errMsg = "The action is allowed only on mainnet chains.";
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.ProofAPINotSet:
                errMsg = "Proof api is not set, please set it using \"setProofApi\"";
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.BurnTxNotCheckPointed:
                errMsg = "Burn transaction has not been checkpointed as yet";
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.EIP1559NotSupported:
                errMsg = "".concat(info ? 'Root' : 'Child', " chain doesn't support eip-1559");
                break;
            case _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.NullSpenderAddress:
                errMsg = "Please provide spender address.";
                break;
            default:
                if (!this.type) {
                    this.type = _enums__WEBPACK_IMPORTED_MODULE_0__.ERROR_TYPE.Unknown;
                }
                errMsg = this.message;
                break;
        }
        return errMsg;
    };
    return ErrorHelper;
}());



/***/ }),

/***/ "./src/utils/event_bus.ts":
/*!********************************!*\
  !*** ./src/utils/event_bus.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EventBus: () => (/* binding */ EventBus),
/* harmony export */   eventBusPromise: () => (/* binding */ eventBusPromise)
/* harmony export */ });
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var eventBusPromise = function (executor) {
    var promise = new Promise(executor);
    var eventBus = new EventBus();
    promise.on = eventBus.on.bind(eventBus);
    promise.emit = eventBus.emit.bind(eventBus);
    return promise;
};
var EventBus = /** @class */ (function () {
    function EventBus(ctx) {
        this._events = {};
        this._ctx = ctx;
    }
    EventBus.prototype.on = function (event, cb) {
        if (this._events[event] == null) {
            this._events[event] = [];
        }
        this._events[event].push(cb);
        return this;
    };
    EventBus.prototype.off = function (event, cb) {
        if (this._events[event]) {
            if (cb) {
                var index = this._events[event].indexOf(cb);
                this._events[event].splice(index, 1);
            }
            else {
                this._events[event] = [];
            }
        }
    };
    EventBus.prototype.emit = function (event) {
        var _this = this;
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        var events = this._events[event] || [];
        return Promise.all(events.map(function (cb) {
            var result = cb.call.apply(cb, __spreadArray([_this._ctx], args, false));
            return result && result.then ? result : Promise.resolve(result);
        }));
    };
    EventBus.prototype.destroy = function () {
        this._events = null;
        this._ctx = null;
    };
    return EventBus;
}());



/***/ }),

/***/ "./src/utils/http_request.ts":
/*!***********************************!*\
  !*** ./src/utils/http_request.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HttpRequest: () => (/* binding */ HttpRequest)
/* harmony export */ });
var fetch = (function () {
    if (process.env.BUILD_ENV === "node") {
        return (__webpack_require__(/*! node-fetch */ "node-fetch")["default"]);
    }
    return window.fetch;
})();
var HttpRequest = /** @class */ (function () {
    function HttpRequest(option) {
        if (option === void 0) { option = {}; }
        this.baseUrl = "";
        option = typeof option === "string" ? {
            baseUrl: option
        } : option;
        if (option.baseUrl) {
            this.baseUrl = option.baseUrl;
        }
    }
    HttpRequest.prototype.get = function (url, query) {
        if (url === void 0) { url = ""; }
        if (query === void 0) { query = {}; }
        url = this.baseUrl + url + Object.keys(query).
            map(function (key) { return "".concat(encodeURIComponent(key), "=").concat(encodeURIComponent(query[key])); }).join('&');
        return fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        }).then(function (res) {
            return res.json();
        });
    };
    HttpRequest.prototype.post = function (url, body) {
        if (url === void 0) { url = ""; }
        url = this.baseUrl + url;
        return fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: body ? JSON.stringify(body) : null
        }).then(function (res) {
            return res.json();
        });
    };
    return HttpRequest;
}());



/***/ }),

/***/ "./src/utils/index.ts":
/*!****************************!*\
  !*** ./src/utils/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ABIManager: () => (/* reexport safe */ _abi_manager__WEBPACK_IMPORTED_MODULE_16__.ABIManager),
/* harmony export */   BN: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_21__.BN),
/* harmony export */   BaseToken: () => (/* reexport safe */ _base_token__WEBPACK_IMPORTED_MODULE_11__.BaseToken),
/* harmony export */   BridgeClient: () => (/* reexport safe */ _bridge_client__WEBPACK_IMPORTED_MODULE_15__.BridgeClient),
/* harmony export */   BufferUtil: () => (/* reexport safe */ _buffer_utils__WEBPACK_IMPORTED_MODULE_19__.BufferUtil),
/* harmony export */   Converter: () => (/* reexport safe */ _converter__WEBPACK_IMPORTED_MODULE_2__.Converter),
/* harmony export */   EventBus: () => (/* reexport safe */ _event_bus__WEBPACK_IMPORTED_MODULE_4__.EventBus),
/* harmony export */   HttpRequest: () => (/* reexport safe */ _http_request__WEBPACK_IMPORTED_MODULE_9__.HttpRequest),
/* harmony export */   Keccak: () => (/* reexport safe */ _keccak__WEBPACK_IMPORTED_MODULE_20__.Keccak),
/* harmony export */   Logger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_5__.Logger),
/* harmony export */   ProofUtil: () => (/* reexport safe */ _proof_util__WEBPACK_IMPORTED_MODULE_8__.ProofUtil),
/* harmony export */   Web3SideChainClient: () => (/* reexport safe */ _web3_side_chain_client__WEBPACK_IMPORTED_MODULE_10__.Web3SideChainClient),
/* harmony export */   ZkEvmBridgeClient: () => (/* reexport safe */ _zkevm_bridge_client__WEBPACK_IMPORTED_MODULE_18__.ZkEvmBridgeClient),
/* harmony export */   eventBusPromise: () => (/* reexport safe */ _event_bus__WEBPACK_IMPORTED_MODULE_4__.eventBusPromise),
/* harmony export */   mapPromise: () => (/* reexport safe */ _map_promise__WEBPACK_IMPORTED_MODULE_7__.mapPromise),
/* harmony export */   merge: () => (/* reexport safe */ _merge__WEBPACK_IMPORTED_MODULE_6__.merge),
/* harmony export */   promiseAny: () => (/* reexport safe */ _promise_resolve__WEBPACK_IMPORTED_MODULE_14__.promiseAny),
/* harmony export */   promiseResolve: () => (/* reexport safe */ _promise_resolve__WEBPACK_IMPORTED_MODULE_14__.promiseResolve),
/* harmony export */   resolve: () => (/* reexport safe */ _resolve__WEBPACK_IMPORTED_MODULE_13__.resolve),
/* harmony export */   setProofApi: () => (/* reexport safe */ _set_proof_api_url__WEBPACK_IMPORTED_MODULE_12__.setProofApi),
/* harmony export */   setZkEvmProofApi: () => (/* reexport safe */ _set_proof_api_url__WEBPACK_IMPORTED_MODULE_12__.setZkEvmProofApi),
/* harmony export */   throwNotImplemented: () => (/* reexport safe */ _not_implemented__WEBPACK_IMPORTED_MODULE_17__.throwNotImplemented),
/* harmony export */   use: () => (/* reexport safe */ _use__WEBPACK_IMPORTED_MODULE_3__.use),
/* harmony export */   utils: () => (/* binding */ utils)
/* harmony export */ });
/* harmony import */ var _abstracts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../abstracts */ "./src/abstracts/index.ts");
/* harmony import */ var _implementation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../implementation */ "./src/implementation/index.ts");
/* harmony import */ var _converter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./converter */ "./src/utils/converter.ts");
/* harmony import */ var _use__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use */ "./src/utils/use.ts");
/* harmony import */ var _event_bus__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./event_bus */ "./src/utils/event_bus.ts");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./logger */ "./src/utils/logger.ts");
/* harmony import */ var _merge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./merge */ "./src/utils/merge.ts");
/* harmony import */ var _map_promise__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./map_promise */ "./src/utils/map_promise.ts");
/* harmony import */ var _proof_util__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./proof_util */ "./src/utils/proof_util.ts");
/* harmony import */ var _http_request__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./http_request */ "./src/utils/http_request.ts");
/* harmony import */ var _web3_side_chain_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./web3_side_chain_client */ "./src/utils/web3_side_chain_client.ts");
/* harmony import */ var _base_token__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./base_token */ "./src/utils/base_token.ts");
/* harmony import */ var _set_proof_api_url__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./set_proof_api_url */ "./src/utils/set_proof_api_url.ts");
/* harmony import */ var _resolve__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./resolve */ "./src/utils/resolve.ts");
/* harmony import */ var _promise_resolve__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./promise_resolve */ "./src/utils/promise_resolve.ts");
/* harmony import */ var _bridge_client__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./bridge_client */ "./src/utils/bridge_client.ts");
/* harmony import */ var _abi_manager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./abi_manager */ "./src/utils/abi_manager.ts");
/* harmony import */ var _not_implemented__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./not_implemented */ "./src/utils/not_implemented.ts");
/* harmony import */ var _zkevm_bridge_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./zkevm_bridge_client */ "./src/utils/zkevm_bridge_client.ts");
/* harmony import */ var _buffer_utils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./buffer-utils */ "./src/utils/buffer-utils.ts");
/* harmony import */ var _keccak__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./keccak */ "./src/utils/keccak.ts");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./types */ "./src/utils/types.ts");























var utils = {
    converter: _converter__WEBPACK_IMPORTED_MODULE_2__.Converter,
    Web3Client: _abstracts__WEBPACK_IMPORTED_MODULE_0__.BaseWeb3Client,
    BN: _implementation__WEBPACK_IMPORTED_MODULE_1__.EmptyBigNumber,
    UnstoppableDomains: Object
};


/***/ }),

/***/ "./src/utils/keccak.ts":
/*!*****************************!*\
  !*** ./src/utils/keccak.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Keccak: () => (/* binding */ Keccak)
/* harmony export */ });
/* harmony import */ var ethereum_cryptography_keccak__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethereum-cryptography/keccak */ "./node_modules/ethereum-cryptography/esm/keccak.js");

var Keccak = /** @class */ (function () {
    function Keccak() {
    }
    /**
     * Throws if input is not a buffer
     * @param {Buffer} input value to check
     */
    Keccak.assertIsBuffer = function (input) {
        if (!Buffer.isBuffer(input)) {
            var msg = "This method only supports Buffer but input was: ".concat(input);
            throw new Error(msg);
        }
    };
    /**
     * Creates Keccak hash of a Buffer input
     * @param a The input data (Buffer)
     * @param bits (number = 256) The Keccak width
     */
    Keccak.keccak = function (a, bits) {
        if (bits === void 0) { bits = 256; }
        Keccak.assertIsBuffer(a);
        switch (bits) {
            case 224: {
                return Buffer.from((0,ethereum_cryptography_keccak__WEBPACK_IMPORTED_MODULE_0__.keccak224)(a));
            }
            case 256: {
                return Buffer.from((0,ethereum_cryptography_keccak__WEBPACK_IMPORTED_MODULE_0__.keccak256)(a));
            }
            case 384: {
                return Buffer.from((0,ethereum_cryptography_keccak__WEBPACK_IMPORTED_MODULE_0__.keccak384)(a));
            }
            case 512: {
                return Buffer.from((0,ethereum_cryptography_keccak__WEBPACK_IMPORTED_MODULE_0__.keccak512)(a));
            }
            default: {
                throw new Error("Invald algorithm: keccak".concat(bits));
            }
        }
    };
    /**
     * Creates Keccak-256 hash of the input, alias for keccak(a, 256).
     * @param a The input data (Buffer)
     */
    Keccak.keccak256 = function (a) {
        return Keccak.keccak(a);
    };
    return Keccak;
}());



/***/ }),

/***/ "./src/utils/logger.ts":
/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Logger: () => (/* binding */ Logger)
/* harmony export */ });
/* harmony import */ var _error_helper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error_helper */ "./src/utils/error_helper.ts");

var Logger = /** @class */ (function () {
    function Logger() {
    }
    Logger.prototype.enableLog = function (value) {
        this.isEnabled = value ? true : false;
    };
    Logger.prototype.log = function () {
        var message = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            message[_i] = arguments[_i];
        }
        if (this.isEnabled) {
            console.log.apply(console, message);
        }
    };
    Logger.prototype.error = function (type, info) {
        return new _error_helper__WEBPACK_IMPORTED_MODULE_0__.ErrorHelper(type, info);
    };
    return Logger;
}());



/***/ }),

/***/ "./src/utils/map_promise.ts":
/*!**********************************!*\
  !*** ./src/utils/map_promise.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   mapPromise: () => (/* binding */ mapPromise)
/* harmony export */ });
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! .. */ "./src/index.ts");

var runPromises = function (promises, converter) {
    var maps = promises.map(function (val, index) {
        return converter(val, index);
    });
    return Promise.all(maps);
};
function mapPromise(values, converter, option) {
    if (option === void 0) { option = {}; }
    var valuesLength = values.length;
    var concurrency = option.concurrency || valuesLength;
    var result = [];
    var limitPromiseRun = function () {
        var promises = values.splice(0, concurrency);
        return runPromises(promises, converter).then(function (promiseResult) {
            result = result.concat(promiseResult);
            return valuesLength > result.length ?
                limitPromiseRun() : (0,___WEBPACK_IMPORTED_MODULE_0__.promiseResolve)(result);
        });
    };
    return limitPromiseRun();
}


/***/ }),

/***/ "./src/utils/merge.ts":
/*!****************************!*\
  !*** ./src/utils/merge.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   merge: () => (/* binding */ merge)
/* harmony export */ });
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var merge = function () {
    var obj = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        obj[_i] = arguments[_i];
    }
    return Object.assign.apply(Object, __spreadArray([{}], obj, false));
};


/***/ }),

/***/ "./src/utils/merkle_tree.ts":
/*!**********************************!*\
  !*** ./src/utils/merkle_tree.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MerkleTree: () => (/* binding */ MerkleTree)
/* harmony export */ });
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ethereumjs/util */ "@ethereumjs/util");
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _keccak__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./keccak */ "./src/utils/keccak.ts");
/* harmony import */ var safe_buffer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! safe-buffer */ "./node_modules/safe-buffer/index.js");
/* harmony import */ var safe_buffer__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(safe_buffer__WEBPACK_IMPORTED_MODULE_2__);


var sha3 = _keccak__WEBPACK_IMPORTED_MODULE_1__.Keccak.keccak256;

var MerkleTree = /** @class */ (function () {
    function MerkleTree(leaves) {
        if (leaves === void 0) { leaves = []; }
        if (leaves.length < 1) {
            throw new Error('Atleast 1 leaf needed');
        }
        var depth = Math.ceil(Math.log(leaves.length) / Math.log(2));
        if (depth > 20) {
            throw new Error('Depth must be 20 or less');
        }
        this.leaves = leaves.concat(Array.from(
        // tslint:disable-next-line
        Array(Math.pow(2, depth) - leaves.length), function () { return (0,_ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__.zeros)(32); }));
        this.layers = [this.leaves];
        this.createHashes(this.leaves);
    }
    MerkleTree.prototype.createHashes = function (nodes) {
        if (nodes.length === 1) {
            return false;
        }
        var treeLevel = [];
        for (var i = 0; i < nodes.length; i += 2) {
            var left = nodes[i];
            var right = nodes[i + 1];
            var data = safe_buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.concat([left, right]);
            treeLevel.push(sha3(data));
        }
        // is odd number of nodes
        if (nodes.length % 2 === 1) {
            treeLevel.push(nodes[nodes.length - 1]);
        }
        this.layers.push(treeLevel);
        this.createHashes(treeLevel);
    };
    MerkleTree.prototype.getLeaves = function () {
        return this.leaves;
    };
    MerkleTree.prototype.getLayers = function () {
        return this.layers;
    };
    MerkleTree.prototype.getRoot = function () {
        return this.layers[this.layers.length - 1][0];
    };
    MerkleTree.prototype.getProof = function (leaf) {
        var index = -1;
        for (var i = 0; i < this.leaves.length; i++) {
            if (safe_buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.compare(leaf, this.leaves[i]) === 0) {
                index = i;
            }
        }
        var proof = [];
        if (index <= this.getLeaves().length) {
            var siblingIndex = void 0;
            for (var i = 0; i < this.layers.length - 1; i++) {
                if (index % 2 === 0) {
                    siblingIndex = index + 1;
                }
                else {
                    siblingIndex = index - 1;
                }
                index = Math.floor(index / 2);
                proof.push(this.layers[i][siblingIndex]);
            }
        }
        return proof;
    };
    MerkleTree.prototype.verify = function (value, index, root, proof) {
        if (!Array.isArray(proof) || !value || !root) {
            return false;
        }
        var hash = value;
        for (var i = 0; i < proof.length; i++) {
            var node = proof[i];
            if (index % 2 === 0) {
                hash = sha3(safe_buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.concat([hash, node]));
            }
            else {
                hash = sha3(safe_buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.concat([node, hash]));
            }
            index = Math.floor(index / 2);
        }
        return safe_buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.compare(hash, root) === 0;
    };
    return MerkleTree;
}());



/***/ }),

/***/ "./src/utils/not_implemented.ts":
/*!**************************************!*\
  !*** ./src/utils/not_implemented.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   throwNotImplemented: () => (/* binding */ throwNotImplemented)
/* harmony export */ });
var throwNotImplemented = function () {
    throw new Error("not implemented");
    return '';
};


/***/ }),

/***/ "./src/utils/promise_resolve.ts":
/*!**************************************!*\
  !*** ./src/utils/promise_resolve.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   promiseAny: () => (/* binding */ promiseAny),
/* harmony export */   promiseResolve: () => (/* binding */ promiseResolve)
/* harmony export */ });
var promiseResolve = function (value) {
    return Promise.resolve(value);
};
var promiseAny = function (promisesArray) {
    var promiseErrors = new Array(promisesArray.length);
    var counter = 0;
    //return a new promise
    return new Promise(function (resolve, reject) {
        promisesArray.forEach(function (promise) {
            Promise.resolve(promise)
                .then(resolve) // resolve, when any of the input promise resolves
                .catch(function (error) {
                promiseErrors[counter] = error;
                counter = counter + 1;
                if (counter === promisesArray.length) {
                    // all promises rejected, reject outer promise
                    reject(promiseErrors);
                }
            }); // reject, when any of the input promise rejects
        });
    });
};


/***/ }),

/***/ "./src/utils/proof_util.ts":
/*!*********************************!*\
  !*** ./src/utils/proof_util.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProofUtil: () => (/* binding */ ProofUtil)
/* harmony export */ });
/* harmony import */ var _merkle_tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./merkle_tree */ "./src/utils/merkle_tree.ts");
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ethereumjs/util */ "@ethereumjs/util");
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_util__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _keccak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./keccak */ "./src/utils/keccak.ts");
/* harmony import */ var _buffer_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./buffer-utils */ "./src/utils/buffer-utils.ts");
/* harmony import */ var rlp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rlp */ "rlp");
/* harmony import */ var rlp__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(rlp__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _map_promise__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./map_promise */ "./src/utils/map_promise.ts");
/* harmony import */ var _ethereumjs_trie__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ethereumjs/trie */ "@ethereumjs/trie");
/* harmony import */ var _ethereumjs_trie__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_trie__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _ethereumjs_block__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @ethereumjs/block */ "@ethereumjs/block");
/* harmony import */ var _ethereumjs_block__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_block__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! .. */ "./src/index.ts");
/* harmony import */ var _ethereumjs_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @ethereumjs/common */ "@ethereumjs/common");
/* harmony import */ var _ethereumjs_common__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_common__WEBPACK_IMPORTED_MODULE_9__);
var __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (undefined && undefined.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};










// Implementation adapted from Tom French's `matic-proofs` library used under MIT License
// https://github.com/TomAFrench/matic-proofs
var ProofUtil = /** @class */ (function () {
    function ProofUtil() {
    }
    ProofUtil.getFastMerkleProof = function (web3, blockNumber, startBlock, endBlock) {
        return __awaiter(this, void 0, void 0, function () {
            var merkleTreeDepth, reversedProof, offset, targetIndex, leftBound, rightBound, _loop_1, this_1, depth;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        merkleTreeDepth = Math.ceil(Math.log2(endBlock - startBlock + 1));
                        reversedProof = [];
                        offset = startBlock;
                        targetIndex = blockNumber - offset;
                        leftBound = 0;
                        rightBound = endBlock - offset;
                        _loop_1 = function (depth) {
                            var nLeaves, pivotLeaf, newLeftBound, subTreeMerkleRoot, newRightBound, expectedHeight, subTreeMerkleRoot, subTreeHeight, heightDifference, remainingNodesHash, leafRoots_1, leaves, subTreeMerkleRoot;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        nLeaves = Math.pow(2, (merkleTreeDepth - depth));
                                        pivotLeaf = leftBound + nLeaves / 2 - 1;
                                        if (!(targetIndex > pivotLeaf)) return [3 /*break*/, 2];
                                        newLeftBound = pivotLeaf + 1;
                                        return [4 /*yield*/, this_1.queryRootHash(web3, offset + leftBound, offset + pivotLeaf)];
                                    case 1:
                                        subTreeMerkleRoot = _b.sent();
                                        reversedProof.push(subTreeMerkleRoot);
                                        leftBound = newLeftBound;
                                        return [3 /*break*/, 6];
                                    case 2:
                                        newRightBound = Math.min(rightBound, pivotLeaf);
                                        expectedHeight = merkleTreeDepth - (depth + 1);
                                        if (!(rightBound <= pivotLeaf)) return [3 /*break*/, 3];
                                        subTreeMerkleRoot = this_1.recursiveZeroHash(expectedHeight, web3);
                                        reversedProof.push(subTreeMerkleRoot);
                                        return [3 /*break*/, 5];
                                    case 3:
                                        subTreeHeight = Math.ceil(Math.log2(rightBound - pivotLeaf));
                                        heightDifference = expectedHeight - subTreeHeight;
                                        return [4 /*yield*/, this_1.queryRootHash(web3, offset + pivotLeaf + 1, offset + rightBound)];
                                    case 4:
                                        remainingNodesHash = _b.sent();
                                        leafRoots_1 = this_1.recursiveZeroHash(subTreeHeight, web3);
                                        leaves = Array.from({ length: Math.pow(2, heightDifference) }, function () { return _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(leafRoots_1); });
                                        leaves[0] = remainingNodesHash;
                                        subTreeMerkleRoot = new _merkle_tree__WEBPACK_IMPORTED_MODULE_0__.MerkleTree(leaves).getRoot();
                                        reversedProof.push(subTreeMerkleRoot);
                                        _b.label = 5;
                                    case 5:
                                        rightBound = newRightBound;
                                        _b.label = 6;
                                    case 6: return [2 /*return*/];
                                }
                            });
                        };
                        this_1 = this;
                        depth = 0;
                        _a.label = 1;
                    case 1:
                        if (!(depth < merkleTreeDepth)) return [3 /*break*/, 4];
                        return [5 /*yield**/, _loop_1(depth)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        depth += 1;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/, reversedProof.reverse()];
                }
            });
        });
    };
    ProofUtil.buildBlockProof = function (maticWeb3, startBlock, endBlock, blockNumber) {
        return ProofUtil.getFastMerkleProof(maticWeb3, blockNumber, startBlock, endBlock).then(function (proof) {
            return _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.bufferToHex(Buffer.concat(proof.map(function (p) {
                return _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(p);
            })));
        });
    };
    ProofUtil.queryRootHash = function (client, startBlock, endBlock) {
        return client.getRootHash(startBlock, endBlock).then(function (rootHash) {
            return _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer("0x".concat(rootHash));
        }).catch(function (_) {
            return null;
        });
    };
    ProofUtil.recursiveZeroHash = function (n, client) {
        if (n === 0)
            return '0x0000000000000000000000000000000000000000000000000000000000000000';
        var subHash = this.recursiveZeroHash(n - 1, client);
        return _keccak__WEBPACK_IMPORTED_MODULE_2__.Keccak.keccak256(_buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(client.encodeParameters([subHash, subHash], ['bytes32', 'bytes32'])));
    };
    ProofUtil.getReceiptProof = function (receipt, block, web3, requestConcurrency, receiptsVal) {
        if (requestConcurrency === void 0) { requestConcurrency = Infinity; }
        var stateSyncTxHash = _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.bufferToHex(ProofUtil.getStateSyncTxHash(block));
        var receiptsTrie = new _ethereumjs_trie__WEBPACK_IMPORTED_MODULE_6__.Trie();
        var receiptPromise;
        if (!receiptsVal) {
            var receiptPromises_1 = [];
            block.transactions.forEach(function (tx) {
                if (tx.transactionHash === stateSyncTxHash) {
                    // ignore if tx hash is bor state-sync tx
                    return;
                }
                receiptPromises_1.push(web3.getTransactionReceipt(tx.transactionHash));
            });
            receiptPromise = (0,_map_promise__WEBPACK_IMPORTED_MODULE_5__.mapPromise)(receiptPromises_1, function (val) {
                return val;
            }, {
                concurrency: requestConcurrency,
            });
        }
        else {
            receiptPromise = (0,___WEBPACK_IMPORTED_MODULE_8__.promiseResolve)(receiptsVal);
        }
        return receiptPromise.then(function (receipts) {
            return Promise.all(receipts.map(function (siblingReceipt) {
                var path = rlp__WEBPACK_IMPORTED_MODULE_4___default().encode(siblingReceipt.transactionIndex);
                var rawReceipt = ProofUtil.getReceiptBytes(siblingReceipt);
                return receiptsTrie.put(path, rawReceipt);
            }));
        }).then(function (_) {
            return receiptsTrie.findPath(rlp__WEBPACK_IMPORTED_MODULE_4___default().encode(receipt.transactionIndex), true);
        }).then(function (result) {
            if (result.remaining.length > 0) {
                throw new Error('Node does not contain the key');
            }
            // result.node.value
            var prf = {
                blockHash: _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(receipt.blockHash),
                parentNodes: result.stack.map(function (s) { return s.raw(); }),
                root: ProofUtil.getRawHeader(block).receiptTrie,
                path: rlp__WEBPACK_IMPORTED_MODULE_4___default().encode(receipt.transactionIndex),
                value: ProofUtil.isTypedReceipt(receipt) ? result.node.value : rlp__WEBPACK_IMPORTED_MODULE_4___default().decode(result.node.value.toString())
            };
            return prf;
        });
    };
    ProofUtil.isTypedReceipt = function (receipt) {
        var hexType = ___WEBPACK_IMPORTED_MODULE_8__.Converter.toHex(receipt.type);
        return receipt.status != null && hexType !== "0x0" && hexType !== "0x";
    };
    // getStateSyncTxHash returns block's tx hash for state-sync receipt
    // Bor blockchain includes extra receipt/tx for state-sync logs,
    // but it is not included in transactionRoot or receiptRoot.
    // So, while calculating proof, we have to exclude them.
    //
    // This is derived from block's hash and number
    // state-sync tx hash = keccak256("matic-bor-receipt-" + block.number + block.hash)
    ProofUtil.getStateSyncTxHash = function (block) {
        return _keccak__WEBPACK_IMPORTED_MODULE_2__.Keccak.keccak256(Buffer.concat([
            // prefix for bor receipt
            Buffer.from('matic-bor-receipt-', 'utf-8'),
            (0,_ethereumjs_util__WEBPACK_IMPORTED_MODULE_1__.setLengthLeft)(_buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(block.number), 8),
            _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(block.hash), // block hash
        ]));
    };
    ProofUtil.getReceiptBytes = function (receipt) {
        var encodedData = rlp__WEBPACK_IMPORTED_MODULE_4___default().encode([
            _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(receipt.status !== undefined && receipt.status != null ? (receipt.status ? '0x1' : '0x') : receipt.root),
            _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(receipt.cumulativeGasUsed),
            _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(receipt.logsBloom),
            // encoded log array
            receipt.logs.map(function (l) {
                // [address, [topics array], data]
                return [
                    _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(l.address),
                    l.topics.map(_buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer),
                    _buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(l.data), // convert data to buffer
                ];
            }),
        ]);
        if (ProofUtil.isTypedReceipt(receipt)) {
            encodedData = Buffer.concat([_buffer_utils__WEBPACK_IMPORTED_MODULE_3__.BufferUtil.toBuffer(receipt.type), encodedData]);
        }
        return encodedData;
    };
    ProofUtil.getRawHeader = function (_block) {
        _block.difficulty = ___WEBPACK_IMPORTED_MODULE_8__.Converter.toHex(_block.difficulty);
        var common = new _ethereumjs_common__WEBPACK_IMPORTED_MODULE_9__.Common({
            chain: _ethereumjs_common__WEBPACK_IMPORTED_MODULE_9__.Chain.Mainnet, hardfork: _ethereumjs_common__WEBPACK_IMPORTED_MODULE_9__.Hardfork.London
        });
        var rawHeader = _ethereumjs_block__WEBPACK_IMPORTED_MODULE_7__.BlockHeader.fromHeaderData(_block, {
            common: common,
            skipConsensusFormatValidation: true
        });
        return rawHeader;
    };
    return ProofUtil;
}());



/***/ }),

/***/ "./src/utils/resolve.ts":
/*!******************************!*\
  !*** ./src/utils/resolve.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   resolve: () => (/* binding */ resolve)
/* harmony export */ });
function resolve(obj, path) {
    var properties = Array.isArray(path) ? path : path.split(".");
    return properties.reduce(function (prev, curr) { return prev && prev[curr]; }, obj);
}


/***/ }),

/***/ "./src/utils/set_proof_api_url.ts":
/*!****************************************!*\
  !*** ./src/utils/set_proof_api_url.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   setProofApi: () => (/* binding */ setProofApi),
/* harmony export */   setZkEvmProofApi: () => (/* binding */ setZkEvmProofApi)
/* harmony export */ });
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");

var setProofApi = function (url) {
    var urlLength = url.length;
    if (url[urlLength - 1] !== '/') {
        url += '/';
    }
    url += 'api/v1/';
    _services__WEBPACK_IMPORTED_MODULE_0__.service.network = new _services__WEBPACK_IMPORTED_MODULE_0__.NetworkService(url);
};
var setZkEvmProofApi = function (url) {
    var urlLength = url.length;
    if (url[urlLength - 1] !== '/') {
        url += '/';
    }
    url += 'api/zkevm/';
    _services__WEBPACK_IMPORTED_MODULE_0__.service.zkEvmNetwork = new _services__WEBPACK_IMPORTED_MODULE_0__.NetworkService(url);
};


/***/ }),

/***/ "./src/utils/types.ts":
/*!****************************!*\
  !*** ./src/utils/types.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BN: () => (/* reexport default from dynamic */ bn_js__WEBPACK_IMPORTED_MODULE_0___default.a)
/* harmony export */ });
/* harmony import */ var bn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bn.js */ "bn.js");
/* harmony import */ var bn_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bn_js__WEBPACK_IMPORTED_MODULE_0__);

/**
 * [`BN`](https://github.com/indutny/bn.js)
 */



/***/ }),

/***/ "./src/utils/use.ts":
/*!**************************!*\
  !*** ./src/utils/use.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   use: () => (/* binding */ use)
/* harmony export */ });
/* harmony import */ var _default__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../default */ "./src/default.ts");
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var use = function (plugin) {
    var payload = [];
    for (var _i = 1; _i < arguments.length; _i++) {
        payload[_i - 1] = arguments[_i];
    }
    var pluginInstance = typeof plugin === "function" ? new plugin() : plugin;
    return pluginInstance.setup.apply(pluginInstance, __spreadArray([_default__WEBPACK_IMPORTED_MODULE_0__.defaultExport], payload, false));
};


/***/ }),

/***/ "./src/utils/web3_side_chain_client.ts":
/*!*********************************************!*\
  !*** ./src/utils/web3_side_chain_client.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Web3SideChainClient: () => (/* binding */ Web3SideChainClient)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ "./src/utils/logger.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! .. */ "./src/index.ts");



var chainIdToConfigPath = {
    1: 'Main',
    5: 'Main',
    11155111: 'Main',
    137: 'Matic',
    80001: 'Matic',
    80002: 'Matic',
    1442: 'zkEVM',
    2442: 'zkEVM',
    1101: 'zkEVM'
};
var Web3SideChainClient = /** @class */ (function () {
    function Web3SideChainClient() {
        this.logger = new _logger__WEBPACK_IMPORTED_MODULE_1__.Logger();
    }
    Web3SideChainClient.prototype.init = function (config) {
        config = config || {};
        config.parent.defaultConfig = config.parent.defaultConfig || {};
        config.child.defaultConfig = config.child.defaultConfig || {};
        this.config = config;
        // tslint:disable-next-line
        var Web3Client = ___WEBPACK_IMPORTED_MODULE_2__.utils.Web3Client;
        if (!Web3Client) {
            throw new Error("Web3Client is not set");
        }
        if (___WEBPACK_IMPORTED_MODULE_2__.utils.UnstoppableDomains) {
            this.resolution = ___WEBPACK_IMPORTED_MODULE_2__.utils.UnstoppableDomains;
        }
        this.parent = new Web3Client(config.parent.provider, this.logger);
        this.child = new Web3Client(config.child.provider, this.logger);
        this.logger.enableLog(config.log);
        var network = config.network;
        var version = config.version;
        var abiManager = this.abiManager =
            new _utils__WEBPACK_IMPORTED_MODULE_0__.ABIManager(network, version);
        this.logger.log("init called", abiManager);
        return abiManager.init().catch(function (err) {
            throw new Error("network ".concat(network, " - ").concat(version, " is not supported"));
        });
    };
    Web3SideChainClient.prototype.getABI = function (name, type) {
        return this.abiManager.getABI(name, type);
    };
    Web3SideChainClient.prototype.getConfig = function (path) {
        return this.abiManager.getConfig(path);
    };
    Object.defineProperty(Web3SideChainClient.prototype, "mainPlasmaContracts", {
        get: function () {
            return this.getConfig("Main.Contracts");
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Web3SideChainClient.prototype, "mainPOSContracts", {
        get: function () {
            return this.getConfig("Main.POSContracts");
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Web3SideChainClient.prototype, "mainZkEvmContracts", {
        get: function () {
            return this.getConfig("Main.Contracts");
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(Web3SideChainClient.prototype, "zkEvmContracts", {
        get: function () {
            return this.getConfig("zkEVM.Contracts");
        },
        enumerable: false,
        configurable: true
    });
    Web3SideChainClient.prototype.isEIP1559Supported = function (chainId) {
        return this.getConfig("".concat(chainIdToConfigPath[chainId], ".SupportsEIP1559"));
    };
    return Web3SideChainClient;
}());



/***/ }),

/***/ "./src/utils/zkevm_bridge_client.ts":
/*!******************************************!*\
  !*** ./src/utils/zkevm_bridge_client.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZkEvmBridgeClient: () => (/* binding */ ZkEvmBridgeClient)
/* harmony export */ });
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! . */ "./src/utils/index.ts");
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");


var ZkEvmBridgeClient = /** @class */ (function () {
    function ZkEvmBridgeClient() {
        this.client = new ___WEBPACK_IMPORTED_MODULE_0__.Web3SideChainClient();
    }
    /**
     * check whether a txHash is synced with child chain
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    ZkEvmBridgeClient.prototype.isDepositClaimable = function (txHash) {
        var _this = this;
        return Promise.all([this.rootChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(txHash, true)]).then(function (result) {
            return _services__WEBPACK_IMPORTED_MODULE_1__.service.zkEvmNetwork.getBridgeTransactionDetails(_this.client.config.version, result[0], result[1].depositCount);
        }).then(function (details) {
            return details.ready_for_claim;
        });
    };
    /**
     * check whether proof is submitted on parent chain
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    ZkEvmBridgeClient.prototype.isWithdrawExitable = function (txHash) {
        var _this = this;
        return Promise.all([this.childChainBridge.networkID(), this.bridgeUtil.getBridgeLogData(txHash, false)]).then(function (result) {
            return _services__WEBPACK_IMPORTED_MODULE_1__.service.zkEvmNetwork.getBridgeTransactionDetails(_this.client.config.version, result[0], result[1].depositCount);
        }).then(function (details) {
            return details.ready_for_claim;
        });
    };
    /**
     * check whether deposit is completed
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    ZkEvmBridgeClient.prototype.isDeposited = function (txHash) {
        var _this = this;
        return this.bridgeUtil.getBridgeLogData(txHash, true).then(function (result) {
            return _this.childChainBridge.isClaimed(result.depositCount, 0);
        });
    };
    /**
     * check whether deposit is completed
     *
     * @param {string} txHash
     * @returns
     * @memberof ZkEvmBridgeClient
     */
    ZkEvmBridgeClient.prototype.isExited = function (txHash) {
        var _this = this;
        return this.bridgeUtil.getBridgeLogData(txHash, false).then(function (result) {
            return _this.rootChainBridge.isClaimed(result.depositCount, 1);
        });
    };
    return ZkEvmBridgeClient;
}());



/***/ }),

/***/ "./src/zkevm/bridge_util.ts":
/*!**********************************!*\
  !*** ./src/zkevm/bridge_util.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BridgeUtil: () => (/* binding */ BridgeUtil)
/* harmony export */ });
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! .. */ "./src/index.ts");


var BridgeUtil = /** @class */ (function () {
    function BridgeUtil(client) {
        this.BRIDGE_TOPIC = "0x501781209a1f8899323b96b4ef08b168df93e0a90c673d1e4cce39366cb62f9b";
        this.client_ = client;
    }
    BridgeUtil.prototype.decodedBridgeData_ = function (data, isParent) {
        var client = isParent ? this.client_.parent : this.client_.child;
        return this.client_.getABI("PolygonZkEVMBridge", "zkevm").then(function (abi) {
            var types = abi.filter(function (event) { return event.name === "BridgeEvent"; });
            if (!types.length) {
                throw new Error("Data not decoded");
            }
            var decodedData = client.decodeParameters(data, types[0].inputs);
            var leafType = decodedData[0], originNetwork = decodedData[1], originTokenAddress = decodedData[2], destinationNetwork = decodedData[3], destinationAddress = decodedData[4], amount = decodedData[5], metadata = decodedData[6], depositCount = decodedData[7];
            return {
                leafType: leafType,
                originNetwork: originNetwork,
                originTokenAddress: originTokenAddress,
                destinationNetwork: destinationNetwork,
                destinationAddress: destinationAddress,
                amount: amount,
                metadata: metadata || '0x',
                depositCount: depositCount,
            };
        });
    };
    BridgeUtil.prototype.getBridgeLogData_ = function (transactionHash, isParent) {
        var _this = this;
        var client = isParent ? this.client_.parent : this.client_.child;
        return client.getTransactionReceipt(transactionHash)
            .then(function (receipt) {
            var logs = receipt.logs.filter(function (log) { return log.topics[0].toLowerCase() === _this.BRIDGE_TOPIC; });
            if (!logs.length) {
                throw new Error("Log not found in receipt");
            }
            var data = logs[0].data;
            return _this.decodedBridgeData_(data, isParent);
        });
    };
    BridgeUtil.prototype.getProof_ = function (networkId, depositCount) {
        return _services__WEBPACK_IMPORTED_MODULE_0__.service.zkEvmNetwork.getMerkleProofForZkEvm(this.client_.config.version, networkId, depositCount).then(function (proof) {
            return proof;
        }).catch(function (_) {
            throw new Error("Error in creating proof");
        });
    };
    BridgeUtil.prototype.getBridgeLogData = function (transactionHash, isParent) {
        return this.getBridgeLogData_(transactionHash, isParent);
    };
    BridgeUtil.prototype.computeGlobalIndex = function (indexLocal, indexRollup, sourceNetworkId) {
        if (BigInt(sourceNetworkId) === BigInt(0)) {
            return BigInt(indexLocal) + ___WEBPACK_IMPORTED_MODULE_1__._GLOBAL_INDEX_MAINNET_FLAG;
        }
        else {
            return BigInt(indexLocal) + BigInt(indexRollup) * BigInt(Math.pow(2, 32));
        }
    };
    BridgeUtil.prototype.buildPayloadForClaim = function (transactionHash, isParent, networkId) {
        var _this = this;
        return this.getBridgeLogData_(transactionHash, isParent).then(function (data) {
            var originNetwork = data.originNetwork, originTokenAddress = data.originTokenAddress, destinationNetwork = data.destinationNetwork, destinationAddress = data.destinationAddress, amount = data.amount, metadata = data.metadata, depositCount = data.depositCount;
            return _this.getProof_(networkId, depositCount).then(function (proof) {
                var payload = {};
                payload.smtProof = proof.merkle_proof;
                payload.smtProofRollup = proof.rollup_merkle_proof;
                payload.globalIndex = _this.computeGlobalIndex(depositCount, destinationNetwork, networkId).toString();
                payload.mainnetExitRoot = proof.main_exit_root;
                payload.rollupExitRoot = proof.rollup_exit_root;
                payload.originNetwork = originNetwork;
                payload.originTokenAddress = originTokenAddress;
                payload.destinationNetwork = destinationNetwork;
                payload.destinationAddress = destinationAddress;
                payload.amount = amount;
                payload.metadata = metadata;
                return payload;
            });
        });
    };
    return BridgeUtil;
}());



/***/ }),

/***/ "./src/zkevm/erc20.ts":
/*!****************************!*\
  !*** ./src/zkevm/erc20.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ERC20: () => (/* binding */ ERC20)
/* harmony export */ });
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ethereumjs/util */ "@ethereumjs/util");
/* harmony import */ var _ethereumjs_util__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _zkevm_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./zkevm_token */ "./src/zkevm/zkevm_token.ts");
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! .. */ "./src/index.ts");
/* harmony import */ var _zkevm_custom_bridge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./zkevm_custom_bridge */ "./src/zkevm/zkevm_custom_bridge.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();





var ERC20 = /** @class */ (function (_super) {
    __extends(ERC20, _super);
    function ERC20(tokenAddress, isParent, bridgeAdapterAddress, client, getContracts) {
        var _this = _super.call(this, {
            isParent: isParent,
            address: tokenAddress,
            bridgeAdapterAddress: bridgeAdapterAddress,
            name: 'ERC20',
            bridgeType: 'zkevm'
        }, client, getContracts) || this;
        if (bridgeAdapterAddress) {
            _this.bridgeAdapter = new _zkevm_custom_bridge__WEBPACK_IMPORTED_MODULE_4__.ZkEVMBridgeAdapter(_this.client, bridgeAdapterAddress, isParent);
        }
        return _this;
    }
    /**
     * get bridge for that token
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getBridgeAddress = function () {
        var bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;
        return bridge.contractAddress;
    };
    ERC20.prototype.isEtherToken = function () {
        return this.contractParam.address === ___WEBPACK_IMPORTED_MODULE_3__.ADDRESS_ZERO;
    };
    /**
     * get token balance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getBalance = function (userAddress, option) {
        var _this = this;
        if (this.isEtherToken()) {
            var client = this.contractParam.isParent ? this.client.parent : this.client.child;
            return client.getBalance(userAddress);
        }
        else {
            return this.getContract().then(function (contract) {
                var method = contract.method("balanceOf", userAddress);
                return _this.processRead(method, option);
            });
        }
    };
    /**
     * is Approval needed to bridge tokens to other chains
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.isApprovalNeeded = function () {
        if (this.isEtherToken()) {
            return false;
        }
        var bridge = this.contractParam.isParent ? this.parentBridge : this.childBridge;
        return bridge.getOriginTokenInfo(this.contractParam.address)
            .then(function (tokenInfo) {
            return tokenInfo[1] === ___WEBPACK_IMPORTED_MODULE_3__.ADDRESS_ZERO;
        });
    };
    /**
     * get allowance of user
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getAllowance = function (userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForNonNative("getAllowance");
        var spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();
        return this.getContract().then(function (contract) {
            var method = contract.method("allowance", userAddress, spenderAddress);
            return _this.processRead(method, option);
        });
    };
    /**
     * Approve given amount of tokens for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {IApproveTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.approve = function (amount, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForNonNative("approve");
        var spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();
        return this.getContract().then(function (contract) {
            var method = contract.method("approve", spenderAddress, _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount));
            return _this.processWrite(method, option);
        });
    };
    /**
     * Approve max amount of tokens for user
     *
     * @param {IApproveTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.approveMax = function (option) {
        if (option === void 0) { option = {}; }
        this.checkForNonNative("approveMax");
        return this.approve(___WEBPACK_IMPORTED_MODULE_3__.MAX_AMOUNT, option);
    };
    /**
     * Deposit given amount of token for user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.deposit = function (amount, userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForRoot("deposit");
        var permitData = option.permitData || '0x';
        var forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        if (this.isEtherToken()) {
            option.value = _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount);
        }
        return this.childBridge.networkID().then(function (networkId) {
            return _this.parentBridge.bridgeAsset(networkId, userAddress, amountInABI, _this.contractParam.address, forceUpdateGlobalExitRoot, permitData, option);
        });
    };
    /**
     * Deposit given amount of token for user along with ETH for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositWithGas = function (amount, userAddress, ethGasAmount, option) {
        if (option === void 0) { option = {}; }
        this.checkForRoot("deposit");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        option.value = _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(ethGasAmount);
        if (option.v && option.r && option.s) {
            return this.zkEVMWrapper.depositPermitWithGas(this.contractParam.address, amountInABI, userAddress, Math.floor((Date.now() + 3600000) / 1000).toString(), option.v, option.r, option.s, option);
        }
        return this.zkEVMWrapper.depositWithGas(this.contractParam.address, amountInABI, userAddress, option);
    };
    /**
     * Deposit given amount of token for user along with ETH for gas token
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositPermitWithGas = function (amount, userAddress, ethGasAmount, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForRoot("deposit");
        this.checkForNonNative("getPermitData");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        option.value = _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(ethGasAmount);
        return this.getPermitSignatureParams_(amount, this.zkEVMWrapper.contractAddress).then(function (signatureParams) {
            return _this.zkEVMWrapper.depositPermitWithGas(_this.contractParam.address, amountInABI, userAddress, Math.floor((Date.now() + 3600000) / 1000).toString(), signatureParams.v, signatureParams.r, signatureParams.s, option);
        });
    };
    /**
     * Deposit given amount of token for user with permit call
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositWithPermit = function (amount, userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForRoot("deposit");
        this.checkForNonNative("depositWithPermit");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        var forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;
        return this.getPermitData(amountInABI, option).then(function (permitData) {
            return _this.childBridge.networkID().then(function (networkId) {
                return _this.parentBridge.bridgeAsset(networkId, userAddress, amountInABI, _this.contractParam.address, forceUpdateGlobalExitRoot, permitData, option);
            });
        });
    };
    /**
     * Bridge asset to child chain using Custom ERC20 bridge Adapter
     * @param amount
     * @param userAddress
     * @param forceUpdateGlobalExitRoot
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositCustomERC20 = function (amount, userAddress, forceUpdateGlobalExitRoot) {
        if (forceUpdateGlobalExitRoot === void 0) { forceUpdateGlobalExitRoot = true; }
        // should be allowed to be used only in root chain
        this.checkForRoot("depositCustomERC20");
        this.checkAdapterPresent("depositCustomERC20");
        // should not be allowed to use for native asset
        this.checkForNonNative("depositCustomERC20");
        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);
    };
    /**
     * Claim asset on child chain bridged using custom bridge adapter on root chain
     * @param transactionHash
     * @param option
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.customERC20DepositClaim = function (transactionHash, option) {
        var _this = this;
        this.checkForChild("customERC20DepositClaim");
        return this.parentBridge.networkID().then(function (networkId) {
            return _this.bridgeUtil.buildPayloadForClaim(transactionHash, true, networkId);
        }).then(function (payload) {
            return _this.childBridge.claimMessage(payload.smtProof, payload.smtProofRollup, payload.globalIndex, payload.mainnetExitRoot, payload.rollupExitRoot, payload.originNetwork, payload.originTokenAddress, payload.destinationNetwork, payload.destinationAddress, payload.amount, payload.metadata, option);
        });
    };
    /**
     * Complete deposit after GlobalExitRootManager is synced from Parent to root
     *
     * @param {string} transactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.depositClaim = function (transactionHash, option) {
        var _this = this;
        this.checkForChild("depositClaim");
        return this.parentBridge.networkID().then(function (networkId) {
            return _this.bridgeUtil.buildPayloadForClaim(transactionHash, true, networkId);
        }).then(function (payload) {
            return _this.childBridge.claimAsset(payload.smtProof, payload.smtProofRollup, payload.globalIndex, payload.mainnetExitRoot, payload.rollupExitRoot, payload.originNetwork, payload.originTokenAddress, payload.destinationNetwork, payload.destinationAddress, payload.amount, payload.metadata, option);
        });
    };
    /**
     * initiate withdraw by burning provided amount
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdraw = function (amount, userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForChild("withdraw");
        var permitData = option.permitData || '0x';
        var forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        if (this.isEtherToken()) {
            option.value = _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount);
        }
        return this.parentBridge.networkID().then(function (networkId) {
            return _this.childBridge.bridgeAsset(networkId, userAddress, amountInABI, _this.contractParam.address, forceUpdateGlobalExitRoot, permitData, option);
        });
    };
    /**
     * Bridge asset to root chain using Custom ERC20 bridge Adapter
     * @param amount
     * @param userAddress
     * @param forceUpdateGlobalExitRoot
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawCustomERC20 = function (amount, userAddress, forceUpdateGlobalExitRoot) {
        if (forceUpdateGlobalExitRoot === void 0) { forceUpdateGlobalExitRoot = true; }
        // should be allowed to be used only in root chain
        this.checkForChild("withdrawCustomERC20");
        this.checkAdapterPresent("depositCustomERC20");
        // should not be allowed to use for native asset
        this.checkForNonNative("withdrawCustomERC20");
        return this.bridgeAdapter.bridgeToken(userAddress, amount, forceUpdateGlobalExitRoot);
    };
    /**
     * Claim asset on root chain bridged using custom bridge adapter on child chain
     * @param burnTransactionHash
     * @param option
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.customERC20WithdrawExit = function (burnTransactionHash, option) {
        var _this = this;
        this.checkForRoot("customERC20WithdrawExit");
        return this.childBridge.networkID().then(function (networkId) {
            return _this.bridgeUtil.buildPayloadForClaim(burnTransactionHash, false, networkId);
        }).then(function (payload) {
            return _this.parentBridge.claimMessage(payload.smtProof, payload.smtProofRollup, payload.globalIndex, payload.mainnetExitRoot, payload.rollupExitRoot, payload.originNetwork, payload.originTokenAddress, payload.destinationNetwork, payload.destinationAddress, payload.amount, payload.metadata, option);
        });
    };
    /**
     * initiate withdraw by transferring amount with PermitData for native tokens
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} userAddress
     * @param {IBridgeTransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawWithPermit = function (amount, userAddress, option) {
        var _this = this;
        if (option === void 0) { option = {}; }
        this.checkForChild("withdraw");
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        var forceUpdateGlobalExitRoot = option.forceUpdateGlobalExitRoot || true;
        return this.getPermitData(amountInABI, option).then(function (permitData) {
            return _this.parentBridge.networkID().then(function (networkId) {
                return _this.childBridge.bridgeAsset(networkId, userAddress, amountInABI, _this.contractParam.address, forceUpdateGlobalExitRoot, permitData, option);
            });
        });
    };
    /**
     * Complete deposit after GlobalExitRootManager is synced from Parent to root
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.withdrawExit = function (burnTransactionHash, option) {
        var _this = this;
        this.checkForRoot("withdrawExit");
        return this.childBridge.networkID().then(function (networkId) {
            return _this.bridgeUtil.buildPayloadForClaim(burnTransactionHash, false, networkId);
        }).then(function (payload) {
            return _this.parentBridge.claimAsset(payload.smtProof, payload.smtProofRollup, payload.globalIndex, payload.mainnetExitRoot, payload.rollupExitRoot, payload.originNetwork, payload.originTokenAddress, payload.destinationNetwork, payload.destinationAddress, payload.amount, payload.metadata, option);
        });
    };
    /**
     * transfer amount to another user
     *
     * @param {TYPE_AMOUNT} amount
     * @param {string} to
     * @param {ITransactionOption} [option]
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.transfer = function (amount, to, option) {
        if (option === void 0) { option = {}; }
        if (this.contractParam.address === ___WEBPACK_IMPORTED_MODULE_3__.ADDRESS_ZERO) {
            option.to = to;
            option.value = _utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount);
            return this.sendTransaction(option);
        }
        return this.transferERC20(to, amount, option);
    };
    /**
     * get permitType of the token
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getPermit = function () {
        var _this = this;
        var contract;
        return this.getContract().then(function (contractInstance) {
            contract = contractInstance;
            var method = contract.method("PERMIT_TYPEHASH");
            return _this.processRead(method);
        }).then(function (permitTypehash) {
            switch (permitTypehash) {
                case ___WEBPACK_IMPORTED_MODULE_3__.DAI_PERMIT_TYPEHASH: {
                    return ___WEBPACK_IMPORTED_MODULE_3__.Permit.DAI;
                }
                case ___WEBPACK_IMPORTED_MODULE_3__.EIP_2612_PERMIT_TYPEHASH: {
                    var DOMAIN_TYPEHASH = contract.method("DOMAIN_TYPEHASH");
                    var EIP712DOMAIN_HASH = contract.method("EIP712DOMAIN_HASH");
                    return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.promiseAny)([_this.processRead(DOMAIN_TYPEHASH), _this.processRead(EIP712DOMAIN_HASH)]).then(function (domainTypehash) {
                        switch (domainTypehash) {
                            case ___WEBPACK_IMPORTED_MODULE_3__.EIP_2612_DOMAIN_TYPEHASH: {
                                return ___WEBPACK_IMPORTED_MODULE_3__.Permit.EIP_2612;
                            }
                            case ___WEBPACK_IMPORTED_MODULE_3__.UNISWAP_DOMAIN_TYPEHASH: {
                                return ___WEBPACK_IMPORTED_MODULE_3__.Permit.UNISWAP;
                            }
                            default: {
                                return Promise.reject(new Error("Unsupported domain typehash: ".concat(domainTypehash)));
                            }
                        }
                    });
                }
                default: {
                    return Promise.reject(new Error("Unsupported permit typehash: ".concat(permitTypehash)));
                }
            }
        });
    };
    /**
     * get typedData for signing
     * @param {string} permitType
     * @param {string} account
     * @param {number} chainId
     * @param {string} name
     * @param {string} nonce
     * @param {string} spenderAddress
     * @param {string} amount
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getTypedData_ = function (permitType, account, chainId, name, nonce, spenderAddress, amount) {
        var typedData = {
            types: {
                EIP712Domain: [
                    { name: 'name', type: 'string' },
                    { name: 'version', type: 'string' },
                    { name: 'chainId', type: 'uint256' },
                    { name: 'verifyingContract', type: 'address' }
                ],
                Permit: []
            },
            primaryType: "Permit",
            domain: {
                name: name,
                version: "1",
                chainId: chainId,
                verifyingContract: this.contractParam.address,
            },
            message: {}
        };
        switch (permitType) {
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.DAI:
                typedData.types.Permit = [
                    { name: "holder", type: "address" },
                    { name: "spender", type: "address" },
                    { name: "nonce", type: "uint256" },
                    { name: "expiry", type: "uint256" },
                    { name: "allowed", type: "bool" },
                ];
                typedData.message = {
                    holder: account,
                    spender: spenderAddress,
                    nonce: nonce,
                    expiry: Math.floor((Date.now() + 3600000) / 1000),
                    allowed: true,
                };
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.EIP_2612:
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.UNISWAP:
                if (permitType === ___WEBPACK_IMPORTED_MODULE_3__.Permit.UNISWAP) {
                    typedData.types.EIP712Domain = [
                        { name: 'name', type: 'string' },
                        { name: 'chainId', type: 'uint256' },
                        { name: 'verifyingContract', type: 'address' }
                    ];
                    delete typedData.domain.version;
                }
                typedData.types.Permit = [
                    { name: 'owner', type: 'address' },
                    { name: 'spender', type: 'address' },
                    { name: 'value', type: 'uint256' },
                    { name: 'nonce', type: 'uint256' },
                    { name: 'deadline', type: 'uint256' }
                ];
                typedData.message = {
                    owner: account,
                    spender: spenderAddress,
                    value: amount,
                    nonce: nonce,
                    deadline: Math.floor((Date.now() + 3600000) / 1000),
                };
        }
        return typedData;
    };
    /**
     * get {r, s, v} from signature
     * @param {BaseWeb3Client} client
     * @param {string} signature
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getSignatureParameters_ = function (client, signature) {
        if (!(0,_ethereumjs_util__WEBPACK_IMPORTED_MODULE_0__.isHexString)(signature)) {
            throw new Error('Given value "'.concat(signature, '" is not a valid hex string.'));
        }
        if (signature.slice(0, 2) !== '0x') {
            signature = '0x'.concat(signature);
        }
        var r = signature.slice(0, 66);
        var s = '0x'.concat(signature.slice(66, 130));
        var v = client.hexToNumber('0x'.concat(signature.slice(130, 132)));
        if (![27, 28].includes(v)) {
            v += 27;
        }
        return {
            r: r,
            s: s,
            v: v,
        };
    };
    /**
     * encode permit function data
     * @param {BaseContract} contract
     * @param {string} permitType
     * @param {any} signatureParams
     * @param {string} spenderAddress
     * @param {string} account
     * @param {string} nonce
     * @param {string} amount
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.encodePermitFunctionData_ = function (contract, permitType, signatureParams, spenderAddress, account, nonce, amount) {
        var r = signatureParams.r, s = signatureParams.s, v = signatureParams.v;
        var method;
        switch (permitType) {
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.DAI:
                method = contract.method("permit", account, spenderAddress, nonce, Math.floor((Date.now() + 3600000) / 1000), true, v, r, s);
                break;
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.EIP_2612:
            case ___WEBPACK_IMPORTED_MODULE_3__.Permit.UNISWAP:
                method = contract.method("permit", account, spenderAddress, amount, Math.floor((Date.now() + 3600000) / 1000), v, r, s);
                break;
        }
        return method.encodeABI();
    };
    ERC20.prototype.getPermitSignatureParams_ = function (amount, spenderAddress) {
        var _this = this;
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        var client = this.contractParam.isParent ? this.client.parent : this.client.child;
        var account;
        var chainId;
        var permitType;
        var contract;
        var nonce;
        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(function (result) {
            account = result[0][0];
            contract = result[1];
            chainId = result[2];
            permitType = result[3];
            var nameMethod = contract.method("name");
            var nonceMethod = contract.method("nonces", account);
            return Promise.all([_this.processRead(nameMethod), _this.processRead(nonceMethod)]);
        }).then(function (data) {
            var name = data[0];
            nonce = data[1];
            return _this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);
        }).then(function (typedData) {
            return client.signTypedData(account, typedData);
        }).then(function (signature) {
            return _this.getSignatureParameters_(client, signature);
        });
    };
    /**
     * Get permit data for given spender for given amount
     * @param {TYPE_AMOUNT} amount
     * @param {string} spenderAddress
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getPermitData_ = function (amount, spenderAddress) {
        var _this = this;
        var amountInABI = this.client.parent.encodeParameters([_utils__WEBPACK_IMPORTED_MODULE_1__.Converter.toHex(amount)], ['uint256']);
        var client = this.contractParam.isParent ? this.client.parent : this.client.child;
        var account;
        var chainId;
        var permitType;
        var contract;
        var nonce;
        return Promise.all([client.name === 'WEB3' ? client.getAccountsUsingRPC_() : client.getAccounts(), this.getContract(), client.getChainId(), this.getPermit()]).then(function (result) {
            account = result[0][0];
            contract = result[1];
            chainId = result[2];
            permitType = result[3];
            var nameMethod = contract.method("name");
            var nonceMethod = contract.method("nonces", account);
            return Promise.all([_this.processRead(nameMethod), _this.processRead(nonceMethod)]);
        }).then(function (data) {
            var name = data[0];
            nonce = data[1];
            return _this.getTypedData_(permitType, account, chainId, name, nonce, spenderAddress, amountInABI);
        }).then(function (typedData) {
            return client.signTypedData(account, typedData);
        }).then(function (signature) {
            var signatureParameters = _this.getSignatureParameters_(client, signature);
            return _this.encodePermitFunctionData_(contract, permitType, signatureParameters, spenderAddress, account, nonce, amountInABI);
        });
    };
    /**
     * Get permit data for given amount
     * @param {TYPE_AMOUNT} amount
     * @param {IApproveTransactionOption} option
     *
     * @returns
     * @memberof ERC20
     */
    ERC20.prototype.getPermitData = function (amount, option) {
        if (option === void 0) { option = {}; }
        this.checkForNonNative("getPermitData");
        var spenderAddress = option.spenderAddress ? option.spenderAddress : this.getBridgeAddress();
        return this.getPermitData_(amount, spenderAddress);
    };
    return ERC20;
}(_zkevm_token__WEBPACK_IMPORTED_MODULE_2__.ZkEvmToken));



/***/ }),

/***/ "./src/zkevm/index.ts":
/*!****************************!*\
  !*** ./src/zkevm/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BridgeUtil: () => (/* reexport safe */ _bridge_util__WEBPACK_IMPORTED_MODULE_2__.BridgeUtil),
/* harmony export */   ZkEVMWrapper: () => (/* reexport safe */ _zkevm_wrapper__WEBPACK_IMPORTED_MODULE_6__.ZkEVMWrapper),
/* harmony export */   ZkEvmBridge: () => (/* reexport safe */ _zkevm_bridge__WEBPACK_IMPORTED_MODULE_1__.ZkEvmBridge),
/* harmony export */   ZkEvmClient: () => (/* binding */ ZkEvmClient)
/* harmony export */ });
/* harmony import */ var _erc20__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./erc20 */ "./src/zkevm/erc20.ts");
/* harmony import */ var _zkevm_bridge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./zkevm_bridge */ "./src/zkevm/zkevm_bridge.ts");
/* harmony import */ var _bridge_util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bridge_util */ "./src/zkevm/bridge_util.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config */ "./src/config.ts");
/* harmony import */ var _services__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../services */ "./src/services/index.ts");
/* harmony import */ var _zkevm_wrapper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./zkevm_wrapper */ "./src/zkevm/zkevm_wrapper.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();










var ZkEvmClient = /** @class */ (function (_super) {
    __extends(ZkEvmClient, _super);
    function ZkEvmClient() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ZkEvmClient.prototype.init = function (config) {
        var _this = this;
        var client = this.client;
        return client.init(config).then(function (_) {
            var mainZkEvmContracts = client.mainZkEvmContracts;
            var zkEvmContracts = client.zkEvmContracts;
            client.config = config = Object.assign({
                parentBridge: mainZkEvmContracts.PolygonZkEVMBridgeProxy,
                childBridge: zkEvmContracts.PolygonZkEVMBridge,
                zkEVMWrapper: mainZkEvmContracts.ZkEVMWrapper
            }, config);
            _this.rootChainBridge = new _zkevm_bridge__WEBPACK_IMPORTED_MODULE_1__.ZkEvmBridge(_this.client, config.parentBridge, true);
            _this.childChainBridge = new _zkevm_bridge__WEBPACK_IMPORTED_MODULE_1__.ZkEvmBridge(_this.client, config.childBridge, false);
            _this.zkEVMWrapper = new _zkevm_wrapper__WEBPACK_IMPORTED_MODULE_6__.ZkEVMWrapper(_this.client, config.zkEVMWrapper);
            _this.bridgeUtil = new _bridge_util__WEBPACK_IMPORTED_MODULE_2__.BridgeUtil(_this.client);
            if (!_services__WEBPACK_IMPORTED_MODULE_5__.service.zkEvmNetwork) {
                if (_config__WEBPACK_IMPORTED_MODULE_4__.config.zkEvmBridgeService[_config__WEBPACK_IMPORTED_MODULE_4__.config.zkEvmBridgeService.length - 1] !== '/') {
                    _config__WEBPACK_IMPORTED_MODULE_4__.config.zkEvmBridgeService += '/';
                }
                _config__WEBPACK_IMPORTED_MODULE_4__.config.zkEvmBridgeService += 'api/zkevm/';
                _services__WEBPACK_IMPORTED_MODULE_5__.service.zkEvmNetwork = new _services__WEBPACK_IMPORTED_MODULE_5__.NetworkService(_config__WEBPACK_IMPORTED_MODULE_4__.config.zkEvmBridgeService);
            }
            return _this;
        });
    };
    /**
     * creates instance of ERC20 token
     *
     * @param {string} tokenAddress
     * @param {boolean} isParent
     *
     * @param bridgeAdapterAddress Needed if a custom erc20 token is being bridged
     * @returns
     * @memberof ERC20
     */
    ZkEvmClient.prototype.erc20 = function (tokenAddress, isParent, bridgeAdapterAddress) {
        return new _erc20__WEBPACK_IMPORTED_MODULE_0__.ERC20(tokenAddress, isParent, bridgeAdapterAddress, this.client, this.getContracts_.bind(this));
    };
    ZkEvmClient.prototype.getContracts_ = function () {
        return {
            parentBridge: this.rootChainBridge,
            childBridge: this.childChainBridge,
            bridgeUtil: this.bridgeUtil,
            zkEVMWrapper: this.zkEVMWrapper
        };
    };
    return ZkEvmClient;
}(_utils__WEBPACK_IMPORTED_MODULE_3__.ZkEvmBridgeClient));



/***/ }),

/***/ "./src/zkevm/zkevm_bridge.ts":
/*!***********************************!*\
  !*** ./src/zkevm/zkevm_bridge.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZkEvmBridge: () => (/* binding */ ZkEvmBridge)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var ZkEvmBridge = /** @class */ (function (_super) {
    __extends(ZkEvmBridge, _super);
    function ZkEvmBridge(client_, address, isParent) {
        return _super.call(this, {
            address: address,
            name: 'PolygonZkEVMBridge',
            bridgeType: 'zkevm',
            isParent: isParent
        }, client_) || this;
    }
    ZkEvmBridge.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    /**
     * bridge function to be called on that network from where token is to be transferred to a different network
     *
     * @param {string} token Token address
     * @param {number} destinationNetwork Network at which tokens will be bridged
     * @param {string} destinationAddress Address to which tokens will be bridged
     * @param {TYPE_AMOUNT} amountamount amount of tokens
     * @param {string} [permitData] Permit data to avoid approve call
     * @param {ITransactionOption} [option]
     *
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.bridgeAsset = function (destinationNetwork, destinationAddress, amount, token, forceUpdateGlobalExitRoot, permitData, option) {
        var _this = this;
        if (permitData === void 0) { permitData = '0x'; }
        return this.method("bridgeAsset", destinationNetwork, destinationAddress, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount), token, forceUpdateGlobalExitRoot, permitData).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    /**
     * Claim function to be called on the destination network
     *
     * @param {string[]} smtProof Merkle Proof
     * @param {string[]} smtProofRollup Roll up Merkle Proof
     * @param {string} globalIndex Global Index
     * @param {string} mainnetExitRoot Mainnet Exit Root
     * @param {string} rollupExitRoot RollUP Exit Root
     * @param {number} originNetwork Network at which token was initially deployed
     * @param {string} originTokenAddress Address of token at network where token was initially deployed
     * @param {string} destinationAddress Address to which tokens will be bridged
     * @param {TYPE_AMOUNT} amount amount of tokens
     * @param {string} [metadata] Metadata of token
     * @param {ITransactionOption} [option]
     *
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.claimAsset = function (smtProof, smtProofRollup, globalIndex, mainnetExitRoot, rollupExitRoot, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata, option) {
        var _this = this;
        return this.method("claimAsset", smtProof, smtProofRollup, globalIndex, mainnetExitRoot, rollupExitRoot, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    /**
     * bridge function to be called on that network from where message is to be transferred to a different network
     * @param {number} destinationNetwork Network at which tokens will be bridged
     * @param {string} destinationAddress Address to which tokens will be bridged
     * @param {boolean} forceUpdateGlobalExitRoot Indicates if the new global exit root is updated or not
     * @param {string} [permitData] Permit data to avoid approve call
     * @param {ITransactionOption} [option]
     *
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.bridgeMessage = function (destinationNetwork, destinationAddress, forceUpdateGlobalExitRoot, permitData, option) {
        var _this = this;
        if (permitData === void 0) { permitData = '0x'; }
        return this.method("bridgeMessage", destinationNetwork, destinationAddress, forceUpdateGlobalExitRoot, permitData).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    /**
     * Claim Message new function to be called on the destination network
     * If the receiving address is an EOA, the call will result as a success
     * Which means that the amount of ether will be transferred correctly, but the message
     * will not trigger any execution. this will work after Etrog changes
     * @param {string[]} smtProof Merkle Proof
     * @param {string[]} smtProofRollup Roll up Merkle Proof
     * @param {string} globalIndex Global Index
     * @param {string} mainnetExitRoot Mainnet Exit Root
     * @param {string} rollupExitRoot RollUP Exit Root
     * @param {number} originNetwork Network at which token was initially deployed
     * @param {string} originTokenAddress Address of token at network where token was initially deployed
     * @param {string} destinationAddress Address to which tokens will be bridged
     * @param {TYPE_AMOUNT} amount amount of tokens
     * @param {string} [metadata] Metadata of token
     * @param {ITransactionOption} [option]
     *
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.claimMessage = function (smtProof, smtProofRollup, globalIndex, mainnetExitRoot, rollupExitRoot, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata, option) {
        var _this = this;
        return this.method("claimMessage", smtProof, smtProofRollup, globalIndex, mainnetExitRoot, rollupExitRoot, originNetwork, originTokenAddress, destinationNetwork, destinationAddress, amount, metadata).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    /**
     * get the address of token which is created by the bridge contract on the non origin chain
     *
     * @param {number} originNetwork Network at which the token was initially deployed
     * @param {string} originTokenAddress Address at the network where token was initially deployed
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.getMappedTokenInfo = function (originNetwork, originTokenAddress) {
        var _this = this;
        return this.method("getTokenWrappedAddress", originNetwork, originTokenAddress).then(function (method) {
            return _this.processRead(method);
        });
    };
    /**
     * Tells if claim has already happed or not based on the deposit index
     *
     * @param {number} index
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.isClaimed = function (index, sourceBridgeNetwork) {
        var _this = this;
        return this.method("isClaimed", index, sourceBridgeNetwork).then(function (method) {
            return _this.processRead(method);
        });
    };
    /**
     * Even if the wrapped contract is not deployed on the destination chain, it will tell us the address which is going to be.
     *
     * @param {number} originNetwork Network at which the token was initially deployed
     * @param {string} originTokenAddress Address at the network where token was initially deployed
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.precalculatedMappedTokenInfo = function (originNetwork, originTokenAddress) {
        var _this = this;
        return this.method("precalculatedWrapperAddress", originNetwork, originTokenAddress).then(function (method) {
            return _this.processRead(method);
        });
    };
    /**
     * get the address and network of the wrapped token where it was emerged initially
     *
     * @param {number} wrappedToken
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.getOriginTokenInfo = function (wrappedToken) {
        var _this = this;
        return this.method("wrappedTokenToTokenInfo", wrappedToken).then(function (method) {
            return _this.processRead(method);
        });
    };
    /**
     * get the network ID for chain in which the bridge contract is deployed
     *
     * @returns
     * @memberof ZkEvmBridge
     */
    ZkEvmBridge.prototype.networkID = function () {
        var _this = this;
        if (this.networkID_) {
            return (0,_utils__WEBPACK_IMPORTED_MODULE_0__.promiseResolve)(this.networkID_);
        }
        return this.method("networkID").then(function (method) {
            return _this.processRead(method).then(function (networkId) {
                _this.networkID_ = networkId;
                return networkId;
            });
        });
    };
    return ZkEvmBridge;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/zkevm/zkevm_custom_bridge.ts":
/*!******************************************!*\
  !*** ./src/zkevm/zkevm_custom_bridge.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZkEVMBridgeAdapter: () => (/* binding */ ZkEVMBridgeAdapter)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

/**
 * ZkEVMBridgeAdapter used ZkEVMBridge to implement additional custom features
 * like bridging custom ERC20
 */
var ZkEVMBridgeAdapter = /** @class */ (function (_super) {
    __extends(ZkEVMBridgeAdapter, _super);
    function ZkEVMBridgeAdapter(client_, address, isParent) {
        return _super.call(this, {
            address: address,
            name: 'ZkEVMBridgeAdapter',
            bridgeType: 'zkevm',
            isParent: isParent, // decides if it's a child chain or a root chain adapter
        }, client_) || this;
    }
    ZkEVMBridgeAdapter.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    /**
     * uses the bridge function present in the adapter contract
     * @param recipient
     * @param amount
     * @param forceUpdateGlobalExitRoot
     * @param option
     *
     * @returns
     * @memberof ZkEvmCustomBridge
     */
    ZkEVMBridgeAdapter.prototype.bridgeToken = function (recipient, amount, forceUpdateGlobalExitRoot, option) {
        var _this = this;
        return this.method('bridgeToken', recipient, _utils__WEBPACK_IMPORTED_MODULE_0__.Converter.toHex(amount), forceUpdateGlobalExitRoot).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    return ZkEVMBridgeAdapter;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/zkevm/zkevm_token.ts":
/*!**********************************!*\
  !*** ./src/zkevm/zkevm_token.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZkEvmToken: () => (/* binding */ ZkEvmToken)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();

var ZkEvmToken = /** @class */ (function (_super) {
    __extends(ZkEvmToken, _super);
    function ZkEvmToken(contractParam, client, getZkEvmContracts) {
        var _this = _super.call(this, contractParam, client) || this;
        _this.getZkEvmContracts = getZkEvmContracts;
        return _this;
    }
    Object.defineProperty(ZkEvmToken.prototype, "parentBridge", {
        get: function () {
            return this.getZkEvmContracts().parentBridge;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ZkEvmToken.prototype, "zkEVMWrapper", {
        get: function () {
            return this.getZkEvmContracts().zkEVMWrapper;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ZkEvmToken.prototype, "childBridge", {
        get: function () {
            return this.getZkEvmContracts().childBridge;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(ZkEvmToken.prototype, "bridgeUtil", {
        get: function () {
            return this.getZkEvmContracts().bridgeUtil;
        },
        enumerable: false,
        configurable: true
    });
    return ZkEvmToken;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "./src/zkevm/zkevm_wrapper.ts":
/*!************************************!*\
  !*** ./src/zkevm/zkevm_wrapper.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZkEVMWrapper: () => (/* binding */ ZkEVMWrapper)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.ts");
var __extends = (undefined && undefined.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};

var ZkEVMWrapper = /** @class */ (function (_super) {
    __extends(ZkEVMWrapper, _super);
    function ZkEVMWrapper(client_, address) {
        return _super.call(this, {
            address: address,
            name: 'ZkEVMWrapper',
            bridgeType: 'zkevm',
            isParent: true
        }, client_) || this;
    }
    ZkEVMWrapper.prototype.method = function (methodName) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        return this.getContract().then(function (contract) {
            return contract.method.apply(contract, __spreadArray([methodName], args, false));
        });
    };
    ZkEVMWrapper.prototype.depositWithGas = function (tokenAddress, depositAmount, userAddress, option) {
        var _this = this;
        return this.method("deposit", tokenAddress, depositAmount, userAddress).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    ZkEVMWrapper.prototype.depositPermitWithGas = function (tokenAddress, depositAmount, userAddress, deadline, v, r, s, option) {
        var _this = this;
        return this.method("deposit", tokenAddress, depositAmount, userAddress, deadline, v, r, s).then(function (method) {
            return _this.processWrite(method, option);
        });
    };
    return ZkEVMWrapper;
}(_utils__WEBPACK_IMPORTED_MODULE_0__.BaseToken));



/***/ }),

/***/ "@ethereumjs/block":
/*!************************************!*\
  !*** external "@ethereumjs/block" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_block__;

/***/ }),

/***/ "@ethereumjs/common":
/*!*************************************!*\
  !*** external "@ethereumjs/common" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_common__;

/***/ }),

/***/ "@ethereumjs/trie":
/*!***********************************!*\
  !*** external "@ethereumjs/trie" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_trie__;

/***/ }),

/***/ "@ethereumjs/util":
/*!***********************************!*\
  !*** external "@ethereumjs/util" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE__ethereumjs_util__;

/***/ }),

/***/ "bn.js":
/*!************************!*\
  !*** external "bn.js" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_bn_js__;

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_buffer__;

/***/ }),

/***/ "node-fetch":
/*!*****************************!*\
  !*** external "node-fetch" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_node_fetch__;

/***/ }),

/***/ "rlp":
/*!**********************!*\
  !*** external "rlp" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = __WEBPACK_EXTERNAL_MODULE_rlp__;

/***/ }),

/***/ "./node_modules/@noble/hashes/esm/_assert.js":
/*!***************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_assert.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   bool: () => (/* binding */ bool),
/* harmony export */   bytes: () => (/* binding */ bytes),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   exists: () => (/* binding */ exists),
/* harmony export */   hash: () => (/* binding */ hash),
/* harmony export */   isBytes: () => (/* binding */ isBytes),
/* harmony export */   number: () => (/* binding */ number),
/* harmony export */   output: () => (/* binding */ output)
/* harmony export */ });
function number(n) {
    if (!Number.isSafeInteger(n) || n < 0)
        throw new Error(`positive integer expected, not ${n}`);
}
function bool(b) {
    if (typeof b !== 'boolean')
        throw new Error(`boolean expected, not ${b}`);
}
// copied from utils
function isBytes(a) {
    return (a instanceof Uint8Array ||
        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));
}
function bytes(b, ...lengths) {
    if (!isBytes(b))
        throw new Error('Uint8Array expected');
    if (lengths.length > 0 && !lengths.includes(b.length))
        throw new Error(`Uint8Array expected of length ${lengths}, not of length=${b.length}`);
}
function hash(h) {
    if (typeof h !== 'function' || typeof h.create !== 'function')
        throw new Error('Hash should be wrapped by utils.wrapConstructor');
    number(h.outputLen);
    number(h.blockLen);
}
function exists(instance, checkFinished = true) {
    if (instance.destroyed)
        throw new Error('Hash instance has been destroyed');
    if (checkFinished && instance.finished)
        throw new Error('Hash#digest() has already been called');
}
function output(out, instance) {
    bytes(out);
    const min = instance.outputLen;
    if (out.length < min) {
        throw new Error(`digestInto() expects output buffer of length at least ${min}`);
    }
}

const assert = { number, bool, bytes, hash, exists, output };
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (assert);
//# sourceMappingURL=_assert.js.map

/***/ }),

/***/ "./node_modules/@noble/hashes/esm/_u64.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/_u64.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   add: () => (/* binding */ add),
/* harmony export */   add3H: () => (/* binding */ add3H),
/* harmony export */   add3L: () => (/* binding */ add3L),
/* harmony export */   add4H: () => (/* binding */ add4H),
/* harmony export */   add4L: () => (/* binding */ add4L),
/* harmony export */   add5H: () => (/* binding */ add5H),
/* harmony export */   add5L: () => (/* binding */ add5L),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   fromBig: () => (/* binding */ fromBig),
/* harmony export */   rotlBH: () => (/* binding */ rotlBH),
/* harmony export */   rotlBL: () => (/* binding */ rotlBL),
/* harmony export */   rotlSH: () => (/* binding */ rotlSH),
/* harmony export */   rotlSL: () => (/* binding */ rotlSL),
/* harmony export */   rotr32H: () => (/* binding */ rotr32H),
/* harmony export */   rotr32L: () => (/* binding */ rotr32L),
/* harmony export */   rotrBH: () => (/* binding */ rotrBH),
/* harmony export */   rotrBL: () => (/* binding */ rotrBL),
/* harmony export */   rotrSH: () => (/* binding */ rotrSH),
/* harmony export */   rotrSL: () => (/* binding */ rotrSL),
/* harmony export */   shrSH: () => (/* binding */ shrSH),
/* harmony export */   shrSL: () => (/* binding */ shrSL),
/* harmony export */   split: () => (/* binding */ split),
/* harmony export */   toBig: () => (/* binding */ toBig)
/* harmony export */ });
const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);
const _32n = /* @__PURE__ */ BigInt(32);
// We are not using BigUint64Array, because they are extremely slow as per 2022
function fromBig(n, le = false) {
    if (le)
        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };
    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };
}
function split(lst, le = false) {
    let Ah = new Uint32Array(lst.length);
    let Al = new Uint32Array(lst.length);
    for (let i = 0; i < lst.length; i++) {
        const { h, l } = fromBig(lst[i], le);
        [Ah[i], Al[i]] = [h, l];
    }
    return [Ah, Al];
}
const toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);
// for Shift in [0, 32)
const shrSH = (h, _l, s) => h >>> s;
const shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);
// Right rotate for Shift in [1, 32)
const rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));
const rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);
// Right rotate for Shift in (32, 64), NOTE: 32 is special case.
const rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));
const rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));
// Right rotate for shift===32 (just swaps l&h)
const rotr32H = (_h, l) => l;
const rotr32L = (h, _l) => h;
// Left rotate for Shift in [1, 32)
const rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));
const rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));
// Left rotate for Shift in (32, 64), NOTE: 32 is special case.
const rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));
const rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));
// JS uses 32-bit signed integers for bitwise operations which means we cannot
// simple take carry out of low bit sum by shift, we need to use division.
function add(Ah, Al, Bh, Bl) {
    const l = (Al >>> 0) + (Bl >>> 0);
    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };
}
// Addition with more than 2 elements
const add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);
const add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;
const add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);
const add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;
const add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);
const add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;
// prettier-ignore

// prettier-ignore
const u64 = {
    fromBig, split, toBig,
    shrSH, shrSL,
    rotrSH, rotrSL, rotrBH, rotrBL,
    rotr32H, rotr32L,
    rotlSH, rotlSL, rotlBH, rotlBL,
    add, add3L, add3H, add4L, add4H, add5H, add5L,
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (u64);
//# sourceMappingURL=_u64.js.map

/***/ }),

/***/ "./node_modules/@noble/hashes/esm/crypto.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/crypto.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   crypto: () => (/* binding */ crypto)
/* harmony export */ });
const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;
//# sourceMappingURL=crypto.js.map

/***/ }),

/***/ "./node_modules/@noble/hashes/esm/sha3.js":
/*!************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/sha3.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Keccak: () => (/* binding */ Keccak),
/* harmony export */   keccakP: () => (/* binding */ keccakP),
/* harmony export */   keccak_224: () => (/* binding */ keccak_224),
/* harmony export */   keccak_256: () => (/* binding */ keccak_256),
/* harmony export */   keccak_384: () => (/* binding */ keccak_384),
/* harmony export */   keccak_512: () => (/* binding */ keccak_512),
/* harmony export */   sha3_224: () => (/* binding */ sha3_224),
/* harmony export */   sha3_256: () => (/* binding */ sha3_256),
/* harmony export */   sha3_384: () => (/* binding */ sha3_384),
/* harmony export */   sha3_512: () => (/* binding */ sha3_512),
/* harmony export */   shake128: () => (/* binding */ shake128),
/* harmony export */   shake256: () => (/* binding */ shake256)
/* harmony export */ });
/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./_assert.js */ "./node_modules/@noble/hashes/esm/_assert.js");
/* harmony import */ var _u64_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_u64.js */ "./node_modules/@noble/hashes/esm/_u64.js");
/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ "./node_modules/@noble/hashes/esm/utils.js");



// SHA3 (keccak) is based on a new design: basically, the internal state is bigger than output size.
// It's called a sponge function.
// Various per round constants calculations
const SHA3_PI = [];
const SHA3_ROTL = [];
const _SHA3_IOTA = [];
const _0n = /* @__PURE__ */ BigInt(0);
const _1n = /* @__PURE__ */ BigInt(1);
const _2n = /* @__PURE__ */ BigInt(2);
const _7n = /* @__PURE__ */ BigInt(7);
const _256n = /* @__PURE__ */ BigInt(256);
const _0x71n = /* @__PURE__ */ BigInt(0x71);
for (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {
    // Pi
    [x, y] = [y, (2 * x + 3 * y) % 5];
    SHA3_PI.push(2 * (5 * y + x));
    // Rotational
    SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);
    // Iota
    let t = _0n;
    for (let j = 0; j < 7; j++) {
        R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;
        if (R & _2n)
            t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);
    }
    _SHA3_IOTA.push(t);
}
const [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.split)(_SHA3_IOTA, true);
// Left rotation (without 0, 32, 64)
const rotlH = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBH)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSH)(h, l, s));
const rotlL = (h, l, s) => (s > 32 ? (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlBL)(h, l, s) : (0,_u64_js__WEBPACK_IMPORTED_MODULE_0__.rotlSL)(h, l, s));
// Same as keccakf1600, but allows to skip some rounds
function keccakP(s, rounds = 24) {
    const B = new Uint32Array(5 * 2);
    // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)
    for (let round = 24 - rounds; round < 24; round++) {
        // Theta θ
        for (let x = 0; x < 10; x++)
            B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];
        for (let x = 0; x < 10; x += 2) {
            const idx1 = (x + 8) % 10;
            const idx0 = (x + 2) % 10;
            const B0 = B[idx0];
            const B1 = B[idx0 + 1];
            const Th = rotlH(B0, B1, 1) ^ B[idx1];
            const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];
            for (let y = 0; y < 50; y += 10) {
                s[x + y] ^= Th;
                s[x + y + 1] ^= Tl;
            }
        }
        // Rho (ρ) and Pi (π)
        let curH = s[2];
        let curL = s[3];
        for (let t = 0; t < 24; t++) {
            const shift = SHA3_ROTL[t];
            const Th = rotlH(curH, curL, shift);
            const Tl = rotlL(curH, curL, shift);
            const PI = SHA3_PI[t];
            curH = s[PI];
            curL = s[PI + 1];
            s[PI] = Th;
            s[PI + 1] = Tl;
        }
        // Chi (χ)
        for (let y = 0; y < 50; y += 10) {
            for (let x = 0; x < 10; x++)
                B[x] = s[y + x];
            for (let x = 0; x < 10; x++)
                s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];
        }
        // Iota (ι)
        s[0] ^= SHA3_IOTA_H[round];
        s[1] ^= SHA3_IOTA_L[round];
    }
    B.fill(0);
}
class Keccak extends _utils_js__WEBPACK_IMPORTED_MODULE_1__.Hash {
    // NOTE: we accept arguments in bytes instead of bits here.
    constructor(blockLen, suffix, outputLen, enableXOF = false, rounds = 24) {
        super();
        this.blockLen = blockLen;
        this.suffix = suffix;
        this.outputLen = outputLen;
        this.enableXOF = enableXOF;
        this.rounds = rounds;
        this.pos = 0;
        this.posOut = 0;
        this.finished = false;
        this.destroyed = false;
        // Can be passed from user as dkLen
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(outputLen);
        // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes
        if (0 >= this.blockLen || this.blockLen >= 200)
            throw new Error('Sha3 supports only keccak-f1600 function');
        this.state = new Uint8Array(200);
        this.state32 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.u32)(this.state);
    }
    keccak() {
        if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLE)
            (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.byteSwap32)(this.state32);
        keccakP(this.state32, this.rounds);
        if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isLE)
            (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.byteSwap32)(this.state32);
        this.posOut = 0;
        this.pos = 0;
    }
    update(data) {
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this);
        const { blockLen, state } = this;
        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.toBytes)(data);
        const len = data.length;
        for (let pos = 0; pos < len;) {
            const take = Math.min(blockLen - this.pos, len - pos);
            for (let i = 0; i < take; i++)
                state[this.pos++] ^= data[pos++];
            if (this.pos === blockLen)
                this.keccak();
        }
        return this;
    }
    finish() {
        if (this.finished)
            return;
        this.finished = true;
        const { state, suffix, pos, blockLen } = this;
        // Do the padding
        state[pos] ^= suffix;
        if ((suffix & 0x80) !== 0 && pos === blockLen - 1)
            this.keccak();
        state[blockLen - 1] ^= 0x80;
        this.keccak();
    }
    writeInto(out) {
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.exists)(this, false);
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.bytes)(out);
        this.finish();
        const bufferOut = this.state;
        const { blockLen } = this;
        for (let pos = 0, len = out.length; pos < len;) {
            if (this.posOut >= blockLen)
                this.keccak();
            const take = Math.min(blockLen - this.posOut, len - pos);
            out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);
            this.posOut += take;
            pos += take;
        }
        return out;
    }
    xofInto(out) {
        // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF
        if (!this.enableXOF)
            throw new Error('XOF is not possible for this instance');
        return this.writeInto(out);
    }
    xof(bytes) {
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.number)(bytes);
        return this.xofInto(new Uint8Array(bytes));
    }
    digestInto(out) {
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_2__.output)(out, this);
        if (this.finished)
            throw new Error('digest() was already called');
        this.writeInto(out);
        this.destroy();
        return out;
    }
    digest() {
        return this.digestInto(new Uint8Array(this.outputLen));
    }
    destroy() {
        this.destroyed = true;
        this.state.fill(0);
    }
    _cloneInto(to) {
        const { blockLen, suffix, outputLen, rounds, enableXOF } = this;
        to || (to = new Keccak(blockLen, suffix, outputLen, enableXOF, rounds));
        to.state32.set(this.state32);
        to.pos = this.pos;
        to.posOut = this.posOut;
        to.finished = this.finished;
        to.rounds = rounds;
        // Suffix can change in cSHAKE
        to.suffix = suffix;
        to.outputLen = outputLen;
        to.enableXOF = enableXOF;
        to.destroyed = this.destroyed;
        return to;
    }
}
const gen = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapConstructor)(() => new Keccak(blockLen, suffix, outputLen));
const sha3_224 = /* @__PURE__ */ gen(0x06, 144, 224 / 8);
/**
 * SHA3-256 hash function
 * @param message - that would be hashed
 */
const sha3_256 = /* @__PURE__ */ gen(0x06, 136, 256 / 8);
const sha3_384 = /* @__PURE__ */ gen(0x06, 104, 384 / 8);
const sha3_512 = /* @__PURE__ */ gen(0x06, 72, 512 / 8);
const keccak_224 = /* @__PURE__ */ gen(0x01, 144, 224 / 8);
/**
 * keccak-256 hash function. Different from SHA3-256.
 * @param message - that would be hashed
 */
const keccak_256 = /* @__PURE__ */ gen(0x01, 136, 256 / 8);
const keccak_384 = /* @__PURE__ */ gen(0x01, 104, 384 / 8);
const keccak_512 = /* @__PURE__ */ gen(0x01, 72, 512 / 8);
const genShake = (suffix, blockLen, outputLen) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.wrapXOFConstructorWithOpts)((opts = {}) => new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true));
const shake128 = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);
const shake256 = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);
//# sourceMappingURL=sha3.js.map

/***/ }),

/***/ "./node_modules/@noble/hashes/esm/utils.js":
/*!*************************************************!*\
  !*** ./node_modules/@noble/hashes/esm/utils.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hash: () => (/* binding */ Hash),
/* harmony export */   asyncLoop: () => (/* binding */ asyncLoop),
/* harmony export */   byteSwap: () => (/* binding */ byteSwap),
/* harmony export */   byteSwap32: () => (/* binding */ byteSwap32),
/* harmony export */   byteSwapIfBE: () => (/* binding */ byteSwapIfBE),
/* harmony export */   bytesToHex: () => (/* binding */ bytesToHex),
/* harmony export */   checkOpts: () => (/* binding */ checkOpts),
/* harmony export */   concatBytes: () => (/* binding */ concatBytes),
/* harmony export */   createView: () => (/* binding */ createView),
/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),
/* harmony export */   isBytes: () => (/* binding */ isBytes),
/* harmony export */   isLE: () => (/* binding */ isLE),
/* harmony export */   nextTick: () => (/* binding */ nextTick),
/* harmony export */   randomBytes: () => (/* binding */ randomBytes),
/* harmony export */   rotl: () => (/* binding */ rotl),
/* harmony export */   rotr: () => (/* binding */ rotr),
/* harmony export */   toBytes: () => (/* binding */ toBytes),
/* harmony export */   u32: () => (/* binding */ u32),
/* harmony export */   u8: () => (/* binding */ u8),
/* harmony export */   utf8ToBytes: () => (/* binding */ utf8ToBytes),
/* harmony export */   wrapConstructor: () => (/* binding */ wrapConstructor),
/* harmony export */   wrapConstructorWithOpts: () => (/* binding */ wrapConstructorWithOpts),
/* harmony export */   wrapXOFConstructorWithOpts: () => (/* binding */ wrapXOFConstructorWithOpts)
/* harmony export */ });
/* harmony import */ var _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/crypto */ "./node_modules/@noble/hashes/esm/crypto.js");
/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_assert.js */ "./node_modules/@noble/hashes/esm/_assert.js");
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */
// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.
// node.js versions earlier than v19 don't declare it in global scope.
// For node.js, package.json#exports field mapping rewrites import
// from `crypto` to `cryptoNode`, which imports native module.
// Makes the utils un-importable in browsers without a bundler.
// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.


// export { isBytes } from './_assert.js';
// We can't reuse isBytes from _assert, because somehow this causes huge perf issues
function isBytes(a) {
    return (a instanceof Uint8Array ||
        (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array'));
}
// Cast array to different type
const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);
const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));
// Cast array to view
const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);
// The rotate right (circular right shift) operation for uint32
const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);
// The rotate left (circular left shift) operation for uint32
const rotl = (word, shift) => (word << shift) | ((word >>> (32 - shift)) >>> 0);
const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;
// The byte swap operation for uint32
const byteSwap = (word) => ((word << 24) & 0xff000000) |
    ((word << 8) & 0xff0000) |
    ((word >>> 8) & 0xff00) |
    ((word >>> 24) & 0xff);
// Conditionally byte swap if on a big-endian platform
const byteSwapIfBE = isLE ? (n) => n : (n) => byteSwap(n);
// In place byte swap for Uint32Array
function byteSwap32(arr) {
    for (let i = 0; i < arr.length; i++) {
        arr[i] = byteSwap(arr[i]);
    }
}
// Array where index 0xf0 (240) is mapped to string 'f0'
const hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));
/**
 * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'
 */
function bytesToHex(bytes) {
    (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.bytes)(bytes);
    // pre-caching improves the speed 6x
    let hex = '';
    for (let i = 0; i < bytes.length; i++) {
        hex += hexes[bytes[i]];
    }
    return hex;
}
// We use optimized technique to convert hex string to byte array
const asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 };
function asciiToBase16(char) {
    if (char >= asciis._0 && char <= asciis._9)
        return char - asciis._0;
    if (char >= asciis._A && char <= asciis._F)
        return char - (asciis._A - 10);
    if (char >= asciis._a && char <= asciis._f)
        return char - (asciis._a - 10);
    return;
}
/**
 * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])
 */
function hexToBytes(hex) {
    if (typeof hex !== 'string')
        throw new Error('hex string expected, got ' + typeof hex);
    const hl = hex.length;
    const al = hl / 2;
    if (hl % 2)
        throw new Error('padded hex string expected, got unpadded hex of length ' + hl);
    const array = new Uint8Array(al);
    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {
        const n1 = asciiToBase16(hex.charCodeAt(hi));
        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));
        if (n1 === undefined || n2 === undefined) {
            const char = hex[hi] + hex[hi + 1];
            throw new Error('hex string expected, got non-hex character "' + char + '" at index ' + hi);
        }
        array[ai] = n1 * 16 + n2;
    }
    return array;
}
// There is no setImmediate in browser and setTimeout is slow.
// call of async fn will return Promise, which will be fullfiled only on
// next scheduler queue processing step and this is exactly what we need.
const nextTick = async () => { };
// Returns control to thread each 'tick' ms to avoid blocking
async function asyncLoop(iters, tick, cb) {
    let ts = Date.now();
    for (let i = 0; i < iters; i++) {
        cb(i);
        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too
        const diff = Date.now() - ts;
        if (diff >= 0 && diff < tick)
            continue;
        await nextTick();
        ts += diff;
    }
}
/**
 * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])
 */
function utf8ToBytes(str) {
    if (typeof str !== 'string')
        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);
    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809
}
/**
 * Normalizes (non-hex) string or Uint8Array to Uint8Array.
 * Warning: when Uint8Array is passed, it would NOT get copied.
 * Keep in mind for future mutable operations.
 */
function toBytes(data) {
    if (typeof data === 'string')
        data = utf8ToBytes(data);
    (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.bytes)(data);
    return data;
}
/**
 * Copies several Uint8Arrays into one.
 */
function concatBytes(...arrays) {
    let sum = 0;
    for (let i = 0; i < arrays.length; i++) {
        const a = arrays[i];
        (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.bytes)(a);
        sum += a.length;
    }
    const res = new Uint8Array(sum);
    for (let i = 0, pad = 0; i < arrays.length; i++) {
        const a = arrays[i];
        res.set(a, pad);
        pad += a.length;
    }
    return res;
}
// For runtime check if class implements interface
class Hash {
    // Safe version that clones internal state
    clone() {
        return this._cloneInto();
    }
}
const toStr = {}.toString;
function checkOpts(defaults, opts) {
    if (opts !== undefined && toStr.call(opts) !== '[object Object]')
        throw new Error('Options should be object or undefined');
    const merged = Object.assign(defaults, opts);
    return merged;
}
function wrapConstructor(hashCons) {
    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();
    const tmp = hashCons();
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = () => hashCons();
    return hashC;
}
function wrapConstructorWithOpts(hashCons) {
    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();
    const tmp = hashCons({});
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = (opts) => hashCons(opts);
    return hashC;
}
function wrapXOFConstructorWithOpts(hashCons) {
    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();
    const tmp = hashCons({});
    hashC.outputLen = tmp.outputLen;
    hashC.blockLen = tmp.blockLen;
    hashC.create = (opts) => hashCons(opts);
    return hashC;
}
/**
 * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.
 */
function randomBytes(bytesLength = 32) {
    if (_noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_1__.crypto && typeof _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_1__.crypto.getRandomValues === 'function') {
        return _noble_hashes_crypto__WEBPACK_IMPORTED_MODULE_1__.crypto.getRandomValues(new Uint8Array(bytesLength));
    }
    throw new Error('crypto.getRandomValues must be defined');
}
//# sourceMappingURL=utils.js.map

/***/ }),

/***/ "./node_modules/ethereum-cryptography/esm/keccak.js":
/*!**********************************************************!*\
  !*** ./node_modules/ethereum-cryptography/esm/keccak.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   keccak224: () => (/* binding */ keccak224),
/* harmony export */   keccak256: () => (/* binding */ keccak256),
/* harmony export */   keccak384: () => (/* binding */ keccak384),
/* harmony export */   keccak512: () => (/* binding */ keccak512)
/* harmony export */ });
/* harmony import */ var _noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/sha3 */ "./node_modules/@noble/hashes/esm/sha3.js");
/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ "./node_modules/ethereum-cryptography/esm/utils.js");


const keccak224 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.wrapHash)(_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__.keccak_224);
const keccak256 = (() => {
    const k = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.wrapHash)(_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__.keccak_256);
    k.create = _noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__.keccak_256.create;
    return k;
})();
const keccak384 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.wrapHash)(_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__.keccak_384);
const keccak512 = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.wrapHash)(_noble_hashes_sha3__WEBPACK_IMPORTED_MODULE_1__.keccak_512);


/***/ }),

/***/ "./node_modules/ethereum-cryptography/esm/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/ethereum-cryptography/esm/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   assertBool: () => (/* binding */ assertBool),
/* harmony export */   assertBytes: () => (/* binding */ assertBytes),
/* harmony export */   bytesToHex: () => (/* reexport safe */ _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.bytesToHex),
/* harmony export */   bytesToUtf8: () => (/* binding */ bytesToUtf8),
/* harmony export */   concatBytes: () => (/* reexport safe */ _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.concatBytes),
/* harmony export */   createView: () => (/* reexport safe */ _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.createView),
/* harmony export */   crypto: () => (/* binding */ crypto),
/* harmony export */   equalsBytes: () => (/* binding */ equalsBytes),
/* harmony export */   hexToBytes: () => (/* binding */ hexToBytes),
/* harmony export */   toHex: () => (/* reexport safe */ _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.bytesToHex),
/* harmony export */   utf8ToBytes: () => (/* reexport safe */ _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.utf8ToBytes),
/* harmony export */   wrapHash: () => (/* binding */ wrapHash)
/* harmony export */ });
/* harmony import */ var _noble_hashes_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @noble/hashes/_assert */ "./node_modules/@noble/hashes/esm/_assert.js");
/* harmony import */ var _noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @noble/hashes/utils */ "./node_modules/@noble/hashes/esm/utils.js");


const assertBool = _noble_hashes_assert__WEBPACK_IMPORTED_MODULE_0__["default"].bool;
const assertBytes = _noble_hashes_assert__WEBPACK_IMPORTED_MODULE_0__["default"].bytes;


// buf.toString('utf8') -> bytesToUtf8(buf)
function bytesToUtf8(data) {
    if (!(data instanceof Uint8Array)) {
        throw new TypeError(`bytesToUtf8 expected Uint8Array, got ${typeof data}`);
    }
    return new TextDecoder().decode(data);
}
function hexToBytes(data) {
    const sliced = data.startsWith("0x") ? data.substring(2) : data;
    return (0,_noble_hashes_utils__WEBPACK_IMPORTED_MODULE_1__.hexToBytes)(sliced);
}
// buf.equals(buf2) -> equalsBytes(buf, buf2)
function equalsBytes(a, b) {
    if (a.length !== b.length) {
        return false;
    }
    for (let i = 0; i < a.length; i++) {
        if (a[i] !== b[i]) {
            return false;
        }
    }
    return true;
}
// Internal utils
function wrapHash(hash) {
    return (msg) => {
        _noble_hashes_assert__WEBPACK_IMPORTED_MODULE_0__["default"].bytes(msg);
        return hash(msg);
    };
}
// TODO(v3): switch away from node crypto, remove this unnecessary variable.
const crypto = (() => {
    const webCrypto = typeof globalThis === "object" && "crypto" in globalThis ? globalThis.crypto : undefined;
    const nodeRequire = typeof module !== "undefined" &&
        typeof module.require === "function" &&
        module.require.bind(module);
    return {
        node: nodeRequire && !webCrypto ? nodeRequire("crypto") : undefined,
        web: webCrypto
    };
})();


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__("./src/index.ts");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=matic.umd.js.map