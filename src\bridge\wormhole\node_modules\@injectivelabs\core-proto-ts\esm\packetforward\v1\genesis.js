/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "packetforward.v1";
function createBaseGenesisState() {
    return { inFlightPackets: {} };
}
export const GenesisState = {
    encode(message, writer = _m0.Writer.create()) {
        Object.entries(message.inFlightPackets).forEach(([key, value]) => {
            GenesisState_InFlightPacketsEntry.encode({ key: key, value }, writer.uint32(18).fork()).ldelim();
        });
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGenesisState();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2:
                    const entry2 = GenesisState_InFlightPacketsEntry.decode(reader, reader.uint32());
                    if (entry2.value !== undefined) {
                        message.inFlightPackets[entry2.key] = entry2.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            inFlightPackets: isObject(object.inFlightPackets)
                ? Object.entries(object.inFlightPackets).reduce((acc, [key, value]) => {
                    acc[key] = InFlightPacket.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON(message) {
        const obj = {};
        obj.inFlightPackets = {};
        if (message.inFlightPackets) {
            Object.entries(message.inFlightPackets).forEach(([k, v]) => {
                obj.inFlightPackets[k] = InFlightPacket.toJSON(v);
            });
        }
        return obj;
    },
    create(base) {
        return GenesisState.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGenesisState();
        message.inFlightPackets = Object.entries(object.inFlightPackets ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = InFlightPacket.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseGenesisState_InFlightPacketsEntry() {
    return { key: "", value: undefined };
}
export const GenesisState_InFlightPacketsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            InFlightPacket.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGenesisState_InFlightPacketsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = InFlightPacket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? InFlightPacket.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? InFlightPacket.toJSON(message.value) : undefined);
        return obj;
    },
    create(base) {
        return GenesisState_InFlightPacketsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGenesisState_InFlightPacketsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? InFlightPacket.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseInFlightPacket() {
    return {
        originalSenderAddress: "",
        refundChannelId: "",
        refundPortId: "",
        packetSrcChannelId: "",
        packetSrcPortId: "",
        packetTimeoutTimestamp: "0",
        packetTimeoutHeight: "",
        packetData: new Uint8Array(),
        refundSequence: "0",
        retriesRemaining: 0,
        timeout: "0",
        nonrefundable: false,
    };
}
export const InFlightPacket = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.originalSenderAddress !== "") {
            writer.uint32(10).string(message.originalSenderAddress);
        }
        if (message.refundChannelId !== "") {
            writer.uint32(18).string(message.refundChannelId);
        }
        if (message.refundPortId !== "") {
            writer.uint32(26).string(message.refundPortId);
        }
        if (message.packetSrcChannelId !== "") {
            writer.uint32(34).string(message.packetSrcChannelId);
        }
        if (message.packetSrcPortId !== "") {
            writer.uint32(42).string(message.packetSrcPortId);
        }
        if (message.packetTimeoutTimestamp !== "0") {
            writer.uint32(48).uint64(message.packetTimeoutTimestamp);
        }
        if (message.packetTimeoutHeight !== "") {
            writer.uint32(58).string(message.packetTimeoutHeight);
        }
        if (message.packetData.length !== 0) {
            writer.uint32(66).bytes(message.packetData);
        }
        if (message.refundSequence !== "0") {
            writer.uint32(72).uint64(message.refundSequence);
        }
        if (message.retriesRemaining !== 0) {
            writer.uint32(80).int32(message.retriesRemaining);
        }
        if (message.timeout !== "0") {
            writer.uint32(88).uint64(message.timeout);
        }
        if (message.nonrefundable === true) {
            writer.uint32(96).bool(message.nonrefundable);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInFlightPacket();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.originalSenderAddress = reader.string();
                    break;
                case 2:
                    message.refundChannelId = reader.string();
                    break;
                case 3:
                    message.refundPortId = reader.string();
                    break;
                case 4:
                    message.packetSrcChannelId = reader.string();
                    break;
                case 5:
                    message.packetSrcPortId = reader.string();
                    break;
                case 6:
                    message.packetTimeoutTimestamp = longToString(reader.uint64());
                    break;
                case 7:
                    message.packetTimeoutHeight = reader.string();
                    break;
                case 8:
                    message.packetData = reader.bytes();
                    break;
                case 9:
                    message.refundSequence = longToString(reader.uint64());
                    break;
                case 10:
                    message.retriesRemaining = reader.int32();
                    break;
                case 11:
                    message.timeout = longToString(reader.uint64());
                    break;
                case 12:
                    message.nonrefundable = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            originalSenderAddress: isSet(object.originalSenderAddress) ? String(object.originalSenderAddress) : "",
            refundChannelId: isSet(object.refundChannelId) ? String(object.refundChannelId) : "",
            refundPortId: isSet(object.refundPortId) ? String(object.refundPortId) : "",
            packetSrcChannelId: isSet(object.packetSrcChannelId) ? String(object.packetSrcChannelId) : "",
            packetSrcPortId: isSet(object.packetSrcPortId) ? String(object.packetSrcPortId) : "",
            packetTimeoutTimestamp: isSet(object.packetTimeoutTimestamp) ? String(object.packetTimeoutTimestamp) : "0",
            packetTimeoutHeight: isSet(object.packetTimeoutHeight) ? String(object.packetTimeoutHeight) : "",
            packetData: isSet(object.packetData) ? bytesFromBase64(object.packetData) : new Uint8Array(),
            refundSequence: isSet(object.refundSequence) ? String(object.refundSequence) : "0",
            retriesRemaining: isSet(object.retriesRemaining) ? Number(object.retriesRemaining) : 0,
            timeout: isSet(object.timeout) ? String(object.timeout) : "0",
            nonrefundable: isSet(object.nonrefundable) ? Boolean(object.nonrefundable) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.originalSenderAddress !== undefined && (obj.originalSenderAddress = message.originalSenderAddress);
        message.refundChannelId !== undefined && (obj.refundChannelId = message.refundChannelId);
        message.refundPortId !== undefined && (obj.refundPortId = message.refundPortId);
        message.packetSrcChannelId !== undefined && (obj.packetSrcChannelId = message.packetSrcChannelId);
        message.packetSrcPortId !== undefined && (obj.packetSrcPortId = message.packetSrcPortId);
        message.packetTimeoutTimestamp !== undefined && (obj.packetTimeoutTimestamp = message.packetTimeoutTimestamp);
        message.packetTimeoutHeight !== undefined && (obj.packetTimeoutHeight = message.packetTimeoutHeight);
        message.packetData !== undefined &&
            (obj.packetData = base64FromBytes(message.packetData !== undefined ? message.packetData : new Uint8Array()));
        message.refundSequence !== undefined && (obj.refundSequence = message.refundSequence);
        message.retriesRemaining !== undefined && (obj.retriesRemaining = Math.round(message.retriesRemaining));
        message.timeout !== undefined && (obj.timeout = message.timeout);
        message.nonrefundable !== undefined && (obj.nonrefundable = message.nonrefundable);
        return obj;
    },
    create(base) {
        return InFlightPacket.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseInFlightPacket();
        message.originalSenderAddress = object.originalSenderAddress ?? "";
        message.refundChannelId = object.refundChannelId ?? "";
        message.refundPortId = object.refundPortId ?? "";
        message.packetSrcChannelId = object.packetSrcChannelId ?? "";
        message.packetSrcPortId = object.packetSrcPortId ?? "";
        message.packetTimeoutTimestamp = object.packetTimeoutTimestamp ?? "0";
        message.packetTimeoutHeight = object.packetTimeoutHeight ?? "";
        message.packetData = object.packetData ?? new Uint8Array();
        message.refundSequence = object.refundSequence ?? "0";
        message.retriesRemaining = object.retriesRemaining ?? 0;
        message.timeout = object.timeout ?? "0";
        message.nonrefundable = object.nonrefundable ?? false;
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
