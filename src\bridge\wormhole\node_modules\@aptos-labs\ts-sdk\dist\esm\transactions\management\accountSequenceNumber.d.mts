import { AptosConfig } from '../../api/aptosConfig.mjs';
import { A as Account } from '../../Ed25519Account-D9XrCLfE.mjs';
import '../../types/types.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../utils/const.mjs';
import '../authenticator/account.mjs';
import '../../bcs/deserializer.mjs';
import '../../bcs/serializer.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';
import '../../core/crypto/ed25519.mjs';
import '../../publicKey-CJOcUwJK.mjs';
import '../../core/accountAddress.mjs';
import '../instances/transactionArgument.mjs';
import '../../core/crypto/signature.mjs';
import '../../core/crypto/privateKey.mjs';
import '../../core/crypto/multiEd25519.mjs';
import '../../core/crypto/multiKey.mjs';
import '../../core/crypto/singleKey.mjs';
import '../../core/crypto/secp256k1.mjs';
import '../types.mjs';
import '../../bcs/serializable/moveStructs.mjs';
import '../../bcs/serializable/movePrimitives.mjs';
import '../../bcs/serializable/fixedBytes.mjs';
import '../instances/rawTransaction.mjs';
import '../instances/chainId.mjs';
import '../instances/transactionPayload.mjs';
import '../instances/identifier.mjs';
import '../instances/moduleId.mjs';
import '../typeTag/index.mjs';
import '../instances/simpleTransaction.mjs';
import '../instances/multiAgentTransaction.mjs';

/**
 * A wrapper that handles and manages an account sequence number.
 *
 * Submit up to `maximumInFlight` transactions per account in parallel with a timeout of `sleepTime`
 * If local assumes `maximumInFlight` are in flight, determine the actual committed state from the network
 * If there are less than `maximumInFlight` due to some being committed, adjust the window
 * If `maximumInFlight` are in flight, wait `sleepTime` seconds before re-evaluating
 * If ever waiting more than `maxWaitTime` restart the sequence number to the current on-chain state
 *
 * Assumptions:
 * Accounts are expected to be managed by a single AccountSequenceNumber and not used otherwise.
 * They are initialized to the current on-chain state, so if there are already transactions in
 * flight, they may take some time to reset.
 * Accounts are automatically initialized if not explicitly
 *
 * Notes:
 * This is co-routine safe, that is many async tasks can be reading from this concurrently.
 * The state of an account cannot be used across multiple AccountSequenceNumber services.
 * The synchronize method will create a barrier that prevents additional nextSequenceNumber
 * calls until it is complete.
 * This only manages the distribution of sequence numbers it does not help handle transaction
 * failures.
 * If a transaction fails, you should call synchronize and wait for timeouts.
 * @group Implementation
 * @category Transactions
 */

/**
 * Represents an account's sequence number management for transaction handling on the Aptos blockchain.
 * This class provides methods to retrieve the next available sequence number, synchronize with the on-chain sequence number,
 * and manage local sequence numbers while ensuring thread safety.
 *
 * @param aptosConfig - The configuration settings for Aptos.
 * @param account - The account associated with the sequence number.
 * @param maxWaitTime - The maximum time to wait for a transaction to commit.
 * @param maximumInFlight - The maximum number of transactions that can be in flight at once.
 * @param sleepTime - The time to wait before retrying to get the sequence number.
 * @group Implementation
 * @category Transactions
 */
declare class AccountSequenceNumber {
    readonly aptosConfig: AptosConfig;
    readonly account: Account;
    lastUncommintedNumber: bigint | null;
    currentNumber: bigint | null;
    /**
     * We want to guarantee that we preserve ordering of workers to requests.
     *
     * `lock` is used to try to prevent multiple coroutines from accessing a shared resource at the same time,
     * which can result in race conditions and data inconsistency.
     * This code actually doesn't do it though, since we aren't giving out a slot, it is still somewhat a race condition.
     *
     * The ideal solution is likely that each thread grabs the next number from an incremental integer.
     * When they complete, they increment that number and that entity is able to enter the `lock`.
     * That would guarantee ordering.
     * @group Implementation
     * @category Transactions
     */
    lock: boolean;
    maxWaitTime: number;
    maximumInFlight: number;
    sleepTime: number;
    /**
     * Creates an instance of the class with the specified configuration and account details.
     * This constructor initializes the necessary parameters for managing Aptos transactions.
     *
     * @param aptosConfig - The configuration settings for Aptos.
     * @param account - The account associated with the Aptos transactions.
     * @param maxWaitTime - The maximum time to wait for a transaction to be processed, in milliseconds.
     * @param maximumInFlight - The maximum number of transactions that can be in flight at the same time.
     * @param sleepTime - The time to sleep between transaction checks, in milliseconds.
     * @group Implementation
     * @category Transactions
     */
    constructor(aptosConfig: AptosConfig, account: Account, maxWaitTime: number, maximumInFlight: number, sleepTime: number);
    /**
     * Returns the next available sequence number for this account.
     * This function ensures that the sequence number is updated and synchronized, handling potential delays in transaction commits.
     *
     * @returns {BigInt} The next available sequence number.
     * @group Implementation
     * @category Transactions
     */
    nextSequenceNumber(): Promise<bigint | null>;
    /**
     * Initializes this account with the sequence number on chain.
     *
     * @returns {Promise<void>} A promise that resolves when the account has been initialized.
     *
     * @throws {Error} Throws an error if the account information cannot be retrieved.
     * @group Implementation
     * @category Transactions
     */
    initialize(): Promise<void>;
    /**
     * Updates this account's sequence number with the one on-chain.
     *
     * @returns The on-chain sequence number for this account.
     * @group Implementation
     * @category Transactions
     */
    update(): Promise<bigint>;
    /**
     * Synchronizes the local sequence number with the sequence number on-chain for the specified account.
     * This function polls the network until all submitted transactions have either been committed or until the maximum wait time has elapsed.
     *
     * @throws {Error} Throws an error if there is an issue synchronizing the account sequence number with the one on-chain.
     * @group Implementation
     * @category Transactions
     */
    synchronize(): Promise<void>;
}

export { AccountSequenceNumber };
