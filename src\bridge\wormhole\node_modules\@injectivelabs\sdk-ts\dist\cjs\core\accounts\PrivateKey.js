"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrivateKey = void 0;
const bip39_1 = require("bip39");
const ethers_1 = require("ethers");
const secp256k1_1 = __importDefault(require("secp256k1"));
const keccak256_1 = __importDefault(require("keccak256"));
const PublicKey_js_1 = require("./PublicKey.js");
const Address_js_1 = require("./Address.js");
const BytesUtils = __importStar(require("@ethersproject/bytes"));
const eth_sig_util_1 = require("@metamask/eth-sig-util");
const index_js_1 = require("../../utils/index.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const tx_js_1 = require("../tx/utils/tx.js");
const index_js_2 = require("../tx/eip712/index.js");
const exceptions_1 = require("@injectivelabs/exceptions");
const ts_types_1 = require("@injectivelabs/ts-types");
/**
 * Class for wrapping SigningKey that is used for signature creation and public key derivation.
 *
 * @category Crypto Utility Classes
 */
class PrivateKey {
    wallet;
    constructor(wallet) {
        this.wallet = wallet;
    }
    /**
     * Generate new private key with random mnemonic phrase
     * @returns { privateKey: PrivateKey, mnemonic: string }
     */
    static generate() {
        const mnemonic = (0, bip39_1.generateMnemonic)();
        const privateKey = PrivateKey.fromMnemonic(mnemonic);
        return {
            privateKey,
            mnemonic,
        };
    }
    /**
     * Create a PrivateKey instance from a given mnemonic phrase and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} words the mnemonic phrase
     * @param {string|undefined} path the HD path that follows the BIP32 standard (optional)
     * @returns {PrivateKey} Initialized PrivateKey object
     */
    static fromMnemonic(words, path = index_js_1.DEFAULT_DERIVATION_PATH) {
        const hdNodeWallet = ethers_1.HDNodeWallet.fromPhrase(words, undefined, path);
        return new PrivateKey(new ethers_1.Wallet(hdNodeWallet.privateKey));
    }
    /**
     * Create a PrivateKey instance from a given private key and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} privateKey  the private key
     * @returns {PrivateKey} Initialized PrivateKey object
     *
     * @deprecated - use fromHex instead
     */
    static fromPrivateKey(privateKey) {
        return new PrivateKey(new ethers_1.Wallet(privateKey));
    }
    /**
     * Create a PrivateKey instance from a given private key and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} privateKey  the private key
     * @returns {PrivateKey} Initialized PrivateKey object
     */
    static fromHex(privateKey) {
        const isString = typeof privateKey === 'string';
        const privateKeyHex = isString && privateKey.startsWith('0x') ? privateKey.slice(2) : privateKey;
        const privateKeyBuff = isString
            ? Buffer.from(privateKeyHex.toString(), 'hex')
            : privateKey;
        return new PrivateKey(new ethers_1.Wallet(Buffer.from(privateKeyBuff).toString('hex')));
    }
    /**
     * Return the private key in hex
     * @returns {string}
     **/
    toPrivateKeyHex() {
        return this.wallet.privateKey.startsWith('0x')
            ? this.wallet.privateKey
            : `0x${this.wallet.privateKey}`;
    }
    /**
     * Return the PublicKey associated with this private key.
     * @returns {PublicKey} a Public key that can be used to verify the signatures made with this PrivateKey
     **/
    toPublicKey() {
        return PublicKey_js_1.PublicKey.fromPrivateKeyHex(this.wallet.privateKey);
    }
    /**
     * Return the hex address associated with this private key.
     * @returns {string}
     */
    toHex() {
        return this.wallet.address.startsWith('0x')
            ? this.wallet.address
            : `0x${this.wallet.address}`;
    }
    /**
     * Return the Address associated with this private key.
     * @returns {Address}
     **/
    toAddress() {
        return Address_js_1.Address.fromHex(this.toHex());
    }
    /**
     * Return the Bech32 address associated with this private key.
     * @returns {string}
     **/
    toBech32() {
        return Address_js_1.Address.fromHex(this.toHex()).toBech32();
    }
    /**
     * Sign the given message using the wallet's _signingKey function.
     * @param {string} messageBytes: the message that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    sign(messageBytes) {
        const { wallet } = this;
        const msgHash = (0, keccak256_1.default)(messageBytes);
        const signature = wallet.signingKey.sign(msgHash);
        const splitSignature = BytesUtils.splitSignature(signature);
        return BytesUtils.arrayify(BytesUtils.concat([splitSignature.r, splitSignature.s]));
    }
    /**
     * Sign the given message using the edcsa sign_deterministic function.
     * @param {Buffer} messageBytes: the message that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signEcda(messageBytes) {
        const { wallet } = this;
        const msgHash = (0, keccak256_1.default)(messageBytes);
        const privateKeyHex = wallet.privateKey.startsWith('0x')
            ? wallet.privateKey.slice(2)
            : wallet.privateKey;
        const privateKey = Uint8Array.from(Buffer.from(privateKeyHex, 'hex'));
        const { signature } = secp256k1_1.default.ecdsaSign(msgHash, privateKey);
        return signature;
    }
    /**
     * Sign the given message using the wallet's _signingKey function.
     * @param {string} messageHashedBytes: the message that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashed(messageHashedBytes) {
        const { wallet } = this;
        const signature = wallet.signingKey.sign(messageHashedBytes);
        const splitSignature = BytesUtils.splitSignature(signature);
        return BytesUtils.arrayify(BytesUtils.concat([splitSignature.r, splitSignature.s]));
    }
    /**
     * Sign the given message using the edcsa sign_deterministic function.
     * @param {Buffer} messageHashedBytes: the message that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashedEcda(messageHashedBytes) {
        const { wallet } = this;
        const privateKeyHex = wallet.privateKey.startsWith('0x')
            ? wallet.privateKey.slice(2)
            : wallet.privateKey;
        const privateKey = Uint8Array.from(Buffer.from(privateKeyHex, 'hex'));
        const { signature } = secp256k1_1.default.ecdsaSign(messageHashedBytes, privateKey);
        return signature;
    }
    /**
     * Sign the given typed data using the edcsa sign_deterministic function.
     * @param {Buffer} eip712Data: the typed data that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signTypedData(eip712Data) {
        const { wallet } = this;
        const privateKeyHex = wallet.privateKey.startsWith('0x')
            ? wallet.privateKey.slice(2)
            : wallet.privateKey;
        const signature = (0, eth_sig_util_1.signTypedData)({
            privateKey: Buffer.from(privateKeyHex, 'hex'),
            data: eip712Data,
            version: eth_sig_util_1.SignTypedDataVersion.V4,
        });
        return Buffer.from(signature.replace('0x', ''), 'hex');
    }
    /**
     * Sign the given typed data using the edcsa sign_deterministic function.
     * @param {Buffer} eip712Data: the typed data that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashedTypedData(eip712Data) {
        const { wallet } = this;
        const privateKeyHex = wallet.privateKey.startsWith('0x')
            ? wallet.privateKey.slice(2)
            : wallet.privateKey;
        const privateKey = Uint8Array.from(Buffer.from(privateKeyHex, 'hex'));
        const { signature } = secp256k1_1.default.ecdsaSign(eip712Data, privateKey);
        return signature;
    }
    /**
     * Verify signature using EIP712 typed data
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} eip712: the EIP712 typed data to verify against
     * @param {string} publicKey: the public key to verify against in hex
     * */
    static verifySignature({ signature, eip712, publicKey, }) {
        const publicKeyInHex = publicKey.startsWith('0x')
            ? publicKey
            : `0x${publicKey}`;
        const recoveredPubKey = (0, index_js_1.recoverTypedSignaturePubKey)(eip712, signature);
        const recoveredPubKeyInHex = recoveredPubKey.startsWith('0x')
            ? recoveredPubKey
            : `0x${recoveredPubKey}`;
        /** uncompressed/compressed key */
        if (publicKeyInHex.length !== recoveredPubKeyInHex.length) {
            return (recoveredPubKeyInHex.substring(0, publicKeyInHex.length) ===
                publicKeyInHex);
        }
        return publicKeyInHex === recoveredPubKeyInHex;
    }
    /**
     * Verify signature using EIP712 typed data
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} eip712: the EIP712 typed data to verify against
     * @param {string} publicKey: the public key to verify against in hex
     * */
    verifyThisPkSignature({ signature, eip712, }) {
        const publicKeyInHex = `0x${this.toPublicKey().toHex()}`;
        const recoveredPubKey = (0, index_js_1.recoverTypedSignaturePubKey)(eip712, signature);
        const recoveredPubKeyInHex = recoveredPubKey.startsWith('0x')
            ? recoveredPubKey
            : `0x${recoveredPubKey}`;
        /** uncompressed/compressed key */
        if (publicKeyInHex.length !== recoveredPubKeyInHex.length) {
            return (recoveredPubKeyInHex.substring(0, publicKeyInHex.length) ===
                publicKeyInHex);
        }
        return publicKeyInHex === recoveredPubKeyInHex;
    }
    /**
     * Verify cosmos signature EIP712 typed
     * data from the TxRaw and verify the signature
     * that's included in the TxRaw
     *
     * (params are passed as an object)
     *
     * @param {CosmosTxV1Beta1Tx.TxRaw} txRaw: the signature to verify in hex
     * @param {object} signer: the public key and the account number to verify against
     **/
    static verifyCosmosSignature({ txRaw, signer, }) {
        const { body, authInfo, signatures } = (0, tx_js_1.getTransactionPartsFromTxRaw)(txRaw);
        if (authInfo.signerInfos.length > 1 || signatures.length > 1) {
            throw new exceptions_1.GeneralException(new Error('Validation of multiple signers is not supported'));
        }
        if (body.messages.length > 1) {
            throw new exceptions_1.GeneralException(new Error('Validation of multiple messages is not supported'));
        }
        const getChainIds = () => {
            if (!body.extensionOptions.length) {
                return {
                    ethereumChainId: ts_types_1.EthereumChainId.Mainnet,
                    chainId: ts_types_1.ChainId.Mainnet,
                };
            }
            const extension = body.extensionOptions.find((extension) => extension.typeUrl.includes('ExtensionOptionsWeb3Tx'));
            if (!extension) {
                return {
                    ethereumChainId: ts_types_1.EthereumChainId.Mainnet,
                    chainId: ts_types_1.ChainId.Mainnet,
                };
            }
            const decodedExtension = core_proto_ts_1.InjectiveTypesV1Beta1TxExt.ExtensionOptionsWeb3Tx.decode(extension.value);
            const ethereumChainId = Number(decodedExtension.typedDataChainID);
            return {
                ethereumChainId: ethereumChainId,
                chainId: [
                    ts_types_1.EthereumChainId.Goerli,
                    ts_types_1.EthereumChainId.Kovan,
                    ts_types_1.EthereumChainId.Sepolia,
                ].includes(ethereumChainId)
                    ? ts_types_1.ChainId.Testnet
                    : ts_types_1.ChainId.Mainnet,
            };
        };
        const { ethereumChainId, chainId } = getChainIds();
        const [signerInfo] = authInfo.signerInfos;
        const [signature] = signatures;
        const [msg] = body.messages;
        const decodedMsg = index_js_2.MsgDecoder.decode(msg);
        const eip712TypedData = (0, index_js_2.getEip712TypedData)({
            msgs: [decodedMsg],
            fee: authInfo.fee,
            tx: {
                memo: body.memo,
                accountNumber: signer.accountNumber.toString(),
                sequence: signerInfo.sequence.toString(),
                timeoutHeight: body.timeoutHeight.toString(),
                chainId,
            },
            ethereumChainId,
        });
        return this.verifySignature({
            eip712: eip712TypedData,
            signature: Buffer.from(signature).toString('hex'),
            publicKey: Buffer.from(signer.publicKey, 'base64').toString('hex'),
        });
    }
    /**
     * Verify signature using ADR-36 sign doc
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} signDoc: the signDoc to verify against
     * @param {string} publicKey:the public key to verify against in hex
     * */
    static verifyArbitrarySignature({ signature, signDoc, publicKey, }) {
        return secp256k1_1.default.ecdsaVerify(Buffer.from(signature, 'hex'), (0, keccak256_1.default)(signDoc), Buffer.from(publicKey, 'hex'));
    }
}
exports.PrivateKey = PrivateKey;
