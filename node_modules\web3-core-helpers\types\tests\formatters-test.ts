/*
    This file is part of web3.js.
    web3.js is free software: you can redistribute it and/or modify
    it under the terms of the GNU Lesser General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.
    web3.js is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU Lesser General Public License for more details.
    You should have received a copy of the GNU Lesser General Public License
    along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
/**
 * @file formatters-test.ts
 * <AUTHOR> <<EMAIL>>
 * @date 2018
 */

import { formatters } from 'web3-core-helpers';

// $ExpectType number
formatters.outputBigNumberFormatter(100);

// $ExpectType string
formatters.inputSignFormatter('0x0');

// $ExpectType string
formatters.inputAddressFormatter('0x0');

// $ExpectType boolean
formatters.isPredefinedBlockNumber('latest');

// $ExpectType string | number
formatters.inputBlockNumberFormatter('0x0');

// $ExpectType any
formatters.outputBlockFormatter({});

// $ExpectType any
formatters.txInputFormatter({});

// $ExpectType any
formatters.inputCallFormatter({});

// $ExpectType any
formatters.inputTransactionFormatter({});

// $ExpectType any
formatters.outputTransactionFormatter({});

// $ExpectType any
formatters.outputTransactionReceiptFormatter({});

// $ExpectType any
formatters.inputLogFormatter({});

// $ExpectType any
formatters.outputLogFormatter({});

// $ExpectType any
formatters.inputPostFormatter({});

// $ExpectType any
formatters.outputPostFormatter({});

// $ExpectType any
formatters.outputSyncingFormatter({});
