"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.protoTypeToAminoType = exports.getObjectEip712PropertyType = exports.stringTypeToReflectionStringType = exports.numberTypeToReflectionNumberType = exports.objectKeysToEip712Types = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const utils_1 = require("@injectivelabs/utils");
const msgExecuteContractType = 'wasm/MsgExecuteContract';
/**
 * ONLY USED FOR EIP712_V1
 *
 * Function used to generate EIP712 types based on a message object
 * and its structure (recursive)
 */
const objectKeysToEip712Types = ({ object, messageType, primaryType = 'MsgValue', }) => {
    const numberFieldsWithStringValue = [
        'order_mask',
        'order_type',
        'oracle_type',
        'round',
        'expiration_timestamp',
        'settlement_timestamp',
        'oracle_scale_factor',
        'expiry',
        'option',
        'proposal_id',
        'creation_height',
        'wasm_hook_query_max_gas',
    ];
    const stringFieldsWithNumberValue = [
        'timeout_timestamp',
        'revision_height',
        'revision_number',
    ];
    const stringFieldsToOmitIfEmpty = ['cid'];
    const fieldsToOmitIfEmpty = ['admin_info', 'order'];
    const output = new Map();
    const types = new Array();
    for (const property in (0, snakecase_keys_1.default)(object)) {
        let propertyValue = (0, snakecase_keys_1.default)(object)[property];
        if (property === '@type') {
            continue;
        }
        if (fieldsToOmitIfEmpty.includes(property) && !propertyValue) {
            continue;
        }
        const type = typeof propertyValue;
        if (type === 'boolean') {
            types.push({ name: property, type: 'bool' });
        }
        else if (type === 'number' ||
            type === 'bigint' ||
            numberFieldsWithStringValue.includes(property)) {
            types.push({
                name: property,
                type: (0, exports.numberTypeToReflectionNumberType)(property, messageType),
            });
        }
        else if (type === 'string') {
            if (stringFieldsToOmitIfEmpty.includes(property) && !propertyValue) {
                continue;
            }
            if (stringFieldsWithNumberValue.includes(property)) {
                types.push({
                    name: property,
                    type: (0, exports.stringTypeToReflectionStringType)(property),
                });
                continue;
            }
            types.push({ name: property, type: 'string' });
        }
        else if (type === 'object') {
            if (Array.isArray(propertyValue) && propertyValue.length === 0) {
                throw new exceptions_1.GeneralException(new Error('Array with length 0 found'));
            }
            else if (Array.isArray(propertyValue) && propertyValue.length > 0) {
                const arrayFirstType = typeof propertyValue[0];
                const isPrimitive = arrayFirstType === 'boolean' ||
                    arrayFirstType === 'number' ||
                    arrayFirstType === 'string';
                if (isPrimitive) {
                    for (const arrayEntry in propertyValue) {
                        if (typeof arrayEntry !== arrayFirstType) {
                            throw new exceptions_1.GeneralException(new Error('Array with different types found'));
                        }
                    }
                    if (arrayFirstType === 'boolean') {
                        types.push({ name: property, type: 'bool[]' });
                    }
                    else if (arrayFirstType === 'number') {
                        types.push({ name: property, type: 'number[]' });
                    }
                    else if (arrayFirstType === 'string') {
                        types.push({ name: property, type: 'string[]' });
                    }
                }
                else if (arrayFirstType === 'object') {
                    const propertyType = (0, exports.getObjectEip712PropertyType)({
                        property: (0, utils_1.snakeToPascal)(property),
                        parentProperty: primaryType,
                        messageType,
                    });
                    const recursiveOutput = (0, exports.objectKeysToEip712Types)({
                        object: propertyValue[0],
                        primaryType: propertyType,
                        messageType,
                    });
                    const recursiveTypes = recursiveOutput.get(propertyType);
                    types.push({ name: property, type: `${propertyType}[]` });
                    output.set(propertyType, recursiveTypes);
                    for (const key of recursiveOutput.keys()) {
                        if (key !== primaryType) {
                            output.set(key, recursiveOutput.get(key));
                        }
                    }
                }
                else {
                    throw new exceptions_1.GeneralException(new Error('Array with elements of unknown type found'));
                }
            }
            else if (propertyValue instanceof Date) {
                types.push({ name: property, type: 'string' });
            }
            else {
                const propertyType = (0, exports.getObjectEip712PropertyType)({
                    property: (0, utils_1.snakeToPascal)(property),
                    parentProperty: primaryType,
                    messageType,
                });
                const recursiveOutput = (0, exports.objectKeysToEip712Types)({
                    object: propertyValue,
                    primaryType: propertyType,
                    messageType,
                });
                const recursiveTypes = recursiveOutput.get(propertyType);
                types.push({ name: property, type: propertyType });
                output.set(propertyType, recursiveTypes);
                for (const key of recursiveOutput.keys()) {
                    if (key !== primaryType) {
                        output.set(key, recursiveOutput.get(key));
                    }
                }
            }
        }
        else {
            throw new exceptions_1.GeneralException(new Error(`Type ${property} not found`));
        }
    }
    output.set(primaryType, types);
    return output;
};
exports.objectKeysToEip712Types = objectKeysToEip712Types;
/**
 * JavaScript doesn't know the exact number types that
 * we represent these fields on chain so we have to map
 * them in their chain representation from the number value
 * that is available in JavaScript
 */
const numberTypeToReflectionNumberType = (property, messageType) => {
    const msgTypeKey = messageType;
    const messageTypeToNumberTypeMaps = {
        'cosmos-sdk/MsgSubmitProposal': {
            status: 'int32',
            base_decimals: 'uint32',
            quote_decimals: 'uint32',
        },
        'injective/tokenfactory/set-denom-metadata': {
            decimals: 'uint32',
        },
        'injective/tokenfactory/create-denom': {
            decimals: 'uint32',
        },
        'exchange/MsgAdminUpdateBinaryOptionsMarket': {
            status: 'int32',
            expiration_timestamp: 'int64',
            settlement_timestamp: 'int64',
        },
        'exchange/MsgInstantBinaryOptionsMarketLaunch': {
            status: 'int32',
            expiration_timestamp: 'int64',
            settlement_timestamp: 'int64',
        },
        'exchange/MsgInstantSpotMarketLaunch': {
            base_decimals: 'uint32',
            quote_decimals: 'uint32',
        },
    };
    if (msgTypeKey && messageTypeToNumberTypeMaps[msgTypeKey] && property) {
        const type = messageTypeToNumberTypeMaps[msgTypeKey];
        const typeKey = property;
        if (type[typeKey]) {
            return type[typeKey];
        }
    }
    switch (property) {
        case 'order_mask':
            return 'int32';
        case 'timeout_timestamp':
            return 'timeout_timestamp';
        case 'revision_number':
            return 'uint64';
        case 'revision_height':
            return 'uint64';
        case 'order_type':
            return 'int32';
        case 'admin_permissions':
            return 'uint32';
        case 'oracle_scale_factor':
            return 'uint32';
        case 'oracle_type':
            return 'int32';
        case 'exponent':
            return 'uint32';
        case 'round':
            return 'uint64';
        case 'oracle_scale_factor':
            return 'uint64';
        case 'expiry':
            return 'int64';
        case 'creation_height':
            return 'int64';
        case 'option':
            return 'int32';
        case 'proposal_id':
            return 'uint64';
        default:
            return 'uint64';
    }
};
exports.numberTypeToReflectionNumberType = numberTypeToReflectionNumberType;
/**
 * JavaScript doesn't know the exact string types that
 * we represent these fields on chain so we have to map
 * them in their chain representation from the string value
 * that is available in JavaScript
 */
const stringTypeToReflectionStringType = (property) => {
    switch (property) {
        case 'timeout_timestamp':
            return 'uint64';
        case 'revision_number':
            return 'uint64';
        case 'revision_height':
            return 'uint64';
        default:
            return 'uint64';
    }
};
exports.stringTypeToReflectionStringType = stringTypeToReflectionStringType;
const getObjectEip712PropertyType = ({ property, parentProperty, messageType, }) => {
    if (messageType === msgExecuteContractType) {
        return appendWasmTypePrefixToPropertyType(property, parentProperty);
    }
    return appendTypePrefixToPropertyType(property, parentProperty);
};
exports.getObjectEip712PropertyType = getObjectEip712PropertyType;
/**
 * Append Wasm Type prefix to a Level0 EIP712 type
 * including its parent property type
 */
const appendWasmTypePrefixToPropertyType = (property, parentProperty = '') => {
    const cosmWasmMsgPrefix = 'CosmwasmInnerMsgMarker';
    const propertyWithoutTypePrefix = property.replace('Type', '');
    if (propertyWithoutTypePrefix === 'Msg') {
        return cosmWasmMsgPrefix;
    }
    const parentPropertyWithoutTypePrefix = parentProperty.replace(cosmWasmMsgPrefix, '');
    return `${parentPropertyWithoutTypePrefix + propertyWithoutTypePrefix}Value`;
};
/**
 * Append Type prefix to a Level0 EIP712 type
 * including its parent property type
 */
const appendTypePrefixToPropertyType = (property, parentProperty = '') => {
    const propertyWithoutTypePrefix = property.replace('Type', '');
    const parentPropertyWithoutTypePrefix = parentProperty === 'MsgValue' ? '' : parentProperty.replace('Type', '');
    return `Type${parentPropertyWithoutTypePrefix + propertyWithoutTypePrefix}`;
};
/**
 * Mapping a path type to amino type for messages
 */
const protoTypeToAminoType = (type) => {
    const actualType = type.startsWith('/') ? type.substring(1) : type;
    switch (actualType) {
        // Exchange
        case 'injective.exchange.v1beta1.MsgDeposit':
            return 'exchange/MsgDeposit';
        case 'injective.exchange.v1beta1.MsgWithdraw':
            return 'exchange/MsgWithdraw';
        case 'injective.exchange.v1beta1.MsgInstantSpotMarketLaunch':
            return 'exchange/MsgInstantSpotMarketLaunch';
        case 'injective.exchange.v1beta1.MsgInstantPerpetualMarketLaunch':
            return 'exchange/MsgInstantPerpetualMarketLaunch';
        case 'injective.exchange.v1beta1.MsgInstantExpiryFuturesMarketLaunch':
            return 'exchange/MsgInstantExpiryFuturesMarketLaunch';
        case 'injective.exchange.v1beta1.MsgCreateSpotLimitOrder':
            return 'exchange/MsgCreateSpotLimitOrder';
        case 'injective.exchange.v1beta1.MsgBatchCreateSpotLimitOrders':
            return 'exchange/MsgBatchCreateSpotLimitOrders';
        case 'injective.exchange.v1beta1.MsgCreateSpotMarketOrder':
            return 'exchange/MsgCreateSpotMarketOrder';
        case 'injective.exchange.v1beta1.MsgCancelSpotOrder':
            return 'exchange/MsgCancelSpotOrder';
        case 'injective.exchange.v1beta1.MsgBatchCancelSpotOrders':
            return 'exchange/MsgBatchCancelSpotOrders';
        case 'injective.exchange.v1beta1.MsgCreateDerivativeLimitOrder':
            return 'exchange/MsgCreateDerivativeLimitOrder';
        case 'injective.exchange.v1beta1.MsgBatchCreateDerivativeLimitOrders':
            return 'exchange/MsgBatchCreateDerivativeLimitOrders';
        case 'injective.exchange.v1beta1.MsgCreateDerivativeMarketOrder':
            return 'exchange/MsgCreateDerivativeMarketOrder';
        case 'injective.exchange.v1beta1.MsgCancelDerivativeOrder':
            return 'exchange/MsgCancelDerivativeOrder';
        case 'injective.exchange.v1beta1.MsgBatchCancelDerivativeOrders':
            return 'exchange/MsgBatchCancelDerivativeOrders';
        case 'injective.exchange.v1beta1.MsgBatchCancelBinaryOptionsOrders':
            return 'exchange/MsgBatchCancelBinaryOptionsOrders';
        case 'injective.exchange.v1beta1.MsgSubaccountTransfer':
            return 'exchange/MsgSubaccountTransfer';
        case 'injective.exchange.v1beta1.MsgExternalTransfer':
            return 'exchange/MsgExternalTransfer';
        case 'injective.exchange.v1beta1.MsgIncreasePositionMargin':
            return 'exchange/MsgIncreasePositionMargin';
        case 'injective.exchange.v1beta1.MsgLiquidatePosition':
            return 'exchange/MsgLiquidatePosition';
        case 'injective.exchange.v1beta1.MsgBatchUpdateOrders':
            return 'exchange/MsgBatchUpdateOrders';
        case 'injective.exchange.v1beta1.MsgExec':
            return 'exchange/MsgExec';
        case 'injective.exchange.v1beta1.MsgRegisterAsDMM':
            return 'exchange/MsgRegisterAsDMM';
        case 'injective.exchange.v1beta1.MsgInstantBinaryOptionsMarketLaunch':
            return 'exchange/MsgInstantBinaryOptionsMarketLaunch';
        case 'injective.exchange.v1beta1.MsgCreateBinaryOptionsLimitOrder':
            return 'exchange/MsgCreateBinaryOptionsLimitOrder';
        case 'injective.exchange.v1beta1.MsgCreateBinaryOptionsMarketOrder':
            return 'exchange/MsgCreateBinaryOptionsMarketOrder';
        case 'injective.exchange.v1beta1.MsgCancelBinaryOptionsOrder':
            return 'exchange/MsgCancelBinaryOptionsOrder';
        case 'injective.exchange.v1beta1.MsgAdminUpdateBinaryOptionsMarket':
            return 'exchange/MsgAdminUpdateBinaryOptionsMarket';
        case 'injective.exchange.v1beta1.ExchangeEnableProposal':
            return 'exchange/ExchangeEnableProposal';
        case 'injective.exchange.v1beta1.BatchExchangeModificationProposal':
            return 'exchange/BatchExchangeModificationProposal';
        case 'injective.exchange.v1beta1.SpotMarketParamUpdateProposal':
            return 'exchange/SpotMarketParamUpdateProposal';
        case 'injective.exchange.v1beta1.SpotMarketLaunchProposal':
            return 'exchange/SpotMarketLaunchProposal';
        case 'injective.exchange.v1beta1.PerpetualMarketLaunchProposal':
            return 'exchange/PerpetualMarketLaunchProposal';
        case 'injective.exchange.v1beta1.ExpiryFuturesMarketLaunchProposal':
            return 'exchange/ExpiryFuturesMarketLaunchProposal';
        case 'injective.exchange.v1beta1.DerivativeMarketParamUpdateProposal':
            return 'exchange/DerivativeMarketParamUpdateProposal';
        case 'injective.exchange.v1beta1.MarketForcedSettlementProposal':
            return 'exchange/MarketForcedSettlementProposal';
        case 'injective.exchange.v1beta1.UpdateDenomDecimalsProposal':
            return 'exchange/UpdateDenomDecimalsProposal';
        case 'injective.exchange.v1beta1.TradingRewardCampaignLaunchProposal':
            return 'exchange/TradingRewardCampaignLaunchProposal';
        case 'injective.exchange.v1beta1.TradingRewardCampaignUpdateProposal':
            return 'exchange/TradingRewardCampaignUpdateProposal';
        case 'injective.exchange.v1beta1.TradingRewardPendingPointsUpdateProposal':
            return 'exchange/TradingRewardPendingPointsUpdateProposal';
        case 'injective.exchange.v1beta1.FeeDiscountProposal':
            return 'exchange/FeeDiscountProposal';
        case 'injective.exchange.v1beta1.BatchCommunityPoolSpendProposal':
            return 'exchange/BatchCommunityPoolSpendProposal';
        case 'injective.exchange.v1beta1.BinaryOptionsMarketParamUpdateProposal':
            return 'exchange/BinaryOptionsMarketParamUpdateProposal';
        case 'injective.exchange.v1beta1.BinaryOptionsMarketLaunchProposal':
            return 'exchange/BinaryOptionsMarketLaunchProposal';
        case 'injective.exchange.v1beta1.MsgTransferAndExecute':
            return 'exchange/MsgTransferAndExecute';
        case 'injective.exchange.v1beta1.CreateSpotLimitOrderAuthz':
            return 'exchange/CreateSpotLimitOrderAuthz';
        case 'injective.exchange.v1beta1.CreateSpotMarketOrderAuthz':
            return 'exchange/CreateSpotMarketOrderAuthz';
        case 'injective.exchange.v1beta1.BatchCreateSpotLimitOrdersAuthz':
            return 'exchange/BatchCreateSpotLimitOrdersAuthz';
        case 'injective.exchange.v1beta1.CancelSpotOrderAuthz':
            return 'exchange/CancelSpotOrderAuthz';
        case 'injective.exchange.v1beta1.BatchCancelSpotOrdersAuthz':
            return 'exchange/BatchCancelSpotOrdersAuthz';
        case 'injective.exchange.v1beta1.CreateDerivativeLimitOrderAuthz':
            return 'exchange/CreateDerivativeLimitOrderAuthz';
        case 'injective.exchange.v1beta1.CreateDerivativeMarketOrderAuthz':
            return 'exchange/CreateDerivativeMarketOrderAuthz';
        case 'injective.exchange.v1beta1.BatchCreateDerivativeLimitOrdersAuthz':
            return 'exchange/BatchCreateDerivativeLimitOrdersAuthz';
        case 'injective.exchange.v1beta1.CancelDerivativeOrderAuthz':
            return 'exchange/CancelDerivativeOrderAuthz';
        case 'injective.exchange.v1beta1.BatchCancelDerivativeOrdersAuthz':
            return 'exchange/BatchCancelDerivativeOrdersAuthz';
        case 'injective.exchange.v1beta1.BatchUpdateOrdersAuthz':
            return 'exchange/BatchUpdateOrdersAuthz';
        // Auction
        case 'injective.auction.v1beta1.MsgBid':
            return 'auction/MsgBid';
        // Insurance
        case 'injective.insurance.v1beta1.MsgCreateInsuranceFund':
            return 'insurance/MsgCreateInsuranceFund';
        case 'injective.insurance.v1beta1.MsgUnderwrite':
            return 'insurance/MsgUnderwrite';
        case 'injective.insurance.v1beta1.MsgRequestRedemption':
            return 'insurance/MsgRequestRedemption';
        // Peggy
        case 'injective.peggy.v1beta1.MsgSetOrchestratorAddresses':
            return 'peggy/MsgSetOrchestratorAddresses';
        case 'injective.peggy.v1beta1.MsgValsetConfirm':
            return 'peggy/MsgValsetConfirm';
        case 'injective.peggy.v1beta1.MsgSendToEth':
            return 'peggy/MsgSendToEth';
        case 'injective.peggy.v1beta1.MsgCancelSendToEth':
            return 'peggy/MsgCancelSendToEth';
        case 'injective.peggy.v1beta1.MsgRequestBatch':
            return 'peggy/MsgRequestBatch';
        case 'injective.peggy.v1beta1.MsgConfirmBatch':
            return 'peggy/MsgConfirmBatch';
        case 'injective.peggy.v1beta1.Valset':
            return 'peggy/Valset';
        case 'injective.peggy.v1beta1.MsgDepositClaim':
            return 'peggy/MsgDepositClaim';
        case 'injective.peggy.v1beta1.MsgWithdrawClaim':
            return 'peggy/MsgWithdrawClaim';
        case 'injective.peggy.v1beta1.MsgERC20DeployedClaim':
            return 'peggy/MsgERC20DeployedClaim';
        case 'injective.peggy.v1beta1.MsgValsetUpdatedClaim':
            return 'peggy/MsgValsetUpdatedClaim';
        case 'injective.peggy.v1beta1.OutgoingTxBatch':
            return 'peggy/OutgoingTxBatch';
        case 'injective.peggy.v1beta1.OutgoingTransferTx':
            return 'peggy/OutgoingTransferTx';
        case 'injective.peggy.v1beta1.ERC20Token':
            return 'peggy/ERC20Token';
        case 'injective.peggy.v1beta1.IDSet':
            return 'peggy/IDSet';
        case 'injective.peggy.v1beta1.Attestation':
            return 'peggy/Attestation';
        case 'injective.peggy.v1beta1.MsgSubmitBadSignatureEvidence':
            return 'peggy/MsgSubmitBadSignatureEvidence';
        case 'injective.peggy.v1beta1.BlacklistEthereumAddressesProposal':
            return 'peggy/BlacklistEthereumAddressesProposal';
        case 'injective.peggy.v1beta1.RevokeEthereumBlacklistProposal':
            return 'peggy/RevokeEthereumBlacklistProposal';
        // WasmX
        case 'injective.wasmx.v1beta1.ContractRegistrationRequestProposal':
            return 'wasmx/ContractRegistrationRequestProposal';
        case 'injective.wasmx.v1beta1.BatchContractRegistrationRequestProposal':
            return 'wasmx/BatchContractRegistrationRequestProposal';
        // Token factory
        case 'injective.tokenfactory.v1beta1.MsgCreateDenom':
            return 'injective/tokenfactory/create-denom';
        case 'injective.tokenfactory.v1beta1.MsgMint':
            return 'injective/tokenfactory/mint';
        case 'injective.tokenfactory.v1beta1.MsgBurn':
            return 'injective/tokenfactory/burn';
        case 'injective.tokenfactory.v1beta1.MsgSetDenomMetadata':
            return 'injective/tokenfactory/set-denom-metadata';
        // Auth
        case 'cosmos.auth.v1beta1.MsgUpdateParams':
            return 'cosmos-sdk/x/auth/MsgUpdateParams';
        // Authz
        case 'cosmos.authz.v1beta1.MsgGrant':
            return 'cosmos-sdk/MsgGrant';
        case 'cosmos.authz.v1beta1.MsgRevoke':
            return 'cosmos-sdk/MsgRevoke';
        case 'cosmos.authz.v1beta1.MsgExec':
            return 'cosmos-sdk/MsgExec';
        // Bank
        case 'cosmos.bank.v1beta1.MsgSend':
            return 'cosmos-sdk/MsgSend';
        case 'cosmos.bank.v1beta1.MsgMultiSend':
            return 'cosmos-sdk/MsgMultiSend';
        case 'cosmos.bank.v1beta1.MsgUpdateParams':
            return 'cosmos-sdk/x/bank/MsgUpdateParams';
        // Distribution
        case 'cosmos.distribution.v1beta1.MsgWithdrawDelegatorReward':
            return 'cosmos-sdk/MsgWithdrawDelegationReward';
        case 'cosmos.distribution.v1beta1.MsgWithdrawValidatorCommission':
            return 'cosmos-sdk/MsgWithdrawValCommission';
        case 'cosmos.distribution.v1beta1.MsgSetWithdrawAddress':
            return 'cosmos-sdk/MsgModifyWithdrawAddress';
        case 'cosmos.distribution.v1beta1.MsgFundCommunityPool':
            return 'cosmos-sdk/MsgFundCommunityPool';
        case 'cosmos.distribution.v1beta1.MsgUpdateParams':
            return 'cosmos-sdk/distribution/MsgUpdateParams';
        // Gov
        case 'cosmos.gov.v1beta1.MsgSubmitProposal':
            return 'cosmos-sdk/MsgSubmitProposal';
        case 'cosmos.gov.v1beta1.MsgDeposit':
            return 'cosmos-sdk/MsgDeposit';
        case 'cosmos.gov.v1.MsgDeposit':
            return 'cosmos-sdk/v1/MsgDeposit';
        case 'cosmos.gov.v1beta1.MsgVote':
            return 'cosmos-sdk/MsgVote';
        case 'cosmos.gov.v1.MsgVote':
            return 'cosmos-sdk/v1/MsgVote';
        case 'cosmos.gov.v1beta1.MsgVoteWeighted':
            return 'cosmos-sdk/MsgVoteWeighted';
        // Staking
        case 'cosmos.staking.v1beta1.MsgCreateValidator':
            return 'cosmos-sdk/MsgCreateValidator';
        case 'cosmos.staking.v1beta1.MsgEditValidator':
            return 'cosmos-sdk/MsgEditValidator';
        case 'cosmos.staking.v1beta1.MsgDelegate':
            return 'cosmos-sdk/MsgDelegate';
        case 'cosmos.staking.v1beta1.MsgUndelegate':
            return 'cosmos-sdk/MsgUndelegate';
        case 'cosmos.staking.v1beta1.MsgBeginRedelegate':
            return 'cosmos-sdk/MsgBeginRedelegate';
        case 'cosmos.staking.v1beta1.MsgCancelUnbondingDelegation':
            return 'cosmos-sdk/MsgCancelUnbondingDelegation';
        case 'cosmos.staking.v1beta1.MsgUpdateParams':
            return 'cosmos-sdk/x/staking/MsgUpdateParams';
        // IBC
        case 'ibc.applications.transfer.v1.MsgTransfer':
            return 'cosmos-sdk/MsgTransfer';
        // feegrant
        case 'cosmos.feegrant.v1beta1.MsgGrantAllowance':
            return 'cosmos-sdk/MsgGrantAllowance';
        case 'cosmos.feegrant.v1beta1.MsgRevokeAllowance':
            return 'cosmos-sdk/MsgRevokeAllowance';
        default:
            throw new exceptions_1.GeneralException(new Error('Unknown message type: ' + type));
    }
};
exports.protoTypeToAminoType = protoTypeToAminoType;
