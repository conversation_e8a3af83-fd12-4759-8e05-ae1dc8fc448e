{"name": "oboe", "title": "Oboe.js", "version": "2.1.5", "description": "Oboe.js reads json, giving you the objects as they are found without waiting for the stream to finish", "main": "./dist/oboe-node.js", "browser": {"./dist/oboe-node.js": "./dist/oboe-browser.js", "./src/streamingHttp.node.js": "./src/streamingHttp.browser.js"}, "scripts": {"test": "node ./node_modules/grunt-cli/bin/grunt headless-mode default", "test-start-server": "node ./node_modules/grunt-cli/bin/grunt test-start-server", "test-run": "node ./node_modules/grunt-cli/bin/grunt test-run", "test-node": "jasmine JASMINE_CONFIG_PATH=jasmine.json", "browser-test-auto-run": "node ./node_modules/grunt-cli/bin/grunt test-auto-run", "node-test-auto-run": "node ./node_modules/grunt-cli/bin/grunt node-test-auto-run", "dist-sizes": "node ./node_modules/grunt-cli/bin/grunt dist-sizes", "webpack": "webpack", "standard": "standard", "test-heap": "node ./node_modules/grunt-cli/bin/grunt start-stream-source browser-build karma:single-heap"}, "repository": {"type": "git", "url": "https://github.com/jimhigson/oboe.js.git"}, "keywords": ["json", "parser", "stream", "progressive", "http", "sax", "event", "emitter", "async", "browser"], "homepage": "http://oboejs.com", "author": "<PERSON>", "license": "BSD", "readmeFilename": "README.md", "devDependencies": {"color": "~0.4.4", "cors": "~2.1.1", "doctoc": "~0.4.3", "express": "~3.4.3", "get-json": "0.0.1", "grunt": "~0.4.1", "grunt-clear": "~0.2.1", "grunt-cli": "~0.1.9", "grunt-concurrent": "~0.3.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.1.3", "grunt-contrib-copy": "~0.4.1", "grunt-contrib-uglify": "~0.2.0", "grunt-contrib-watch": "~0.5.1", "grunt-exec": "~0.4.2", "grunt-karma": "2.0.0", "grunt-micro": "~0.1.0", "grunt-wrap": "~0.2.0", "jasmine": "2.5.2", "jasmine-core": "2.5.2", "jasmine-node": "~1.11.0", "karma": "1.3.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "1.1.1", "karma-firefox-launcher": "1.0.0", "karma-jasmine": "1.0.2", "karma-phantomjs-launcher": "1.0.2", "karma-safari-launcher": "1.0.0", "karma-webpack": "^2.0.9", "matchdep": "~0.1.2", "request": "2.81.0", "sinon": "^2.4.1", "standard": "^11.0.1", "uglifyjs-webpack-plugin": "^1.2.1", "webpack": "^3.10.0"}, "dependencies": {"http-https": "^1.0.0"}, "jam": {"main": "dist/oboe-browser.js", "include": ["dist/oboe-browser.js", "LICENCE", "package.json", "README.md"], "dependencies": {}, "categories": ["AJAX & Websockets", "Parsers & Compilers"]}, "standard": {"ignore": ["node_modules/**", "dist", "test/require", "test/libs/es5-*.js"], "env": "jasmine", "globals": ["Platform", "crossDomainUrl", "XMLHttpRequest"]}}