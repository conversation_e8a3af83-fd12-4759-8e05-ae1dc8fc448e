#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试代理功能脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

import argparse
import requests
from src.utils.proxy_manager import proxy_manager
from src.dex.astar.wallet import AstarWallet
from config.config import config


def test_direct_connection():
    """测试直接连接"""
    try:
        print("测试直接连接...")
        response = requests.get('https://httpbin.org/ip', timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"IP地址: {response.json().get('origin')}")
        print("直接连接测试成功")
    except Exception as e:
        print(f"直接连接测试失败: {str(e)}")


def test_proxy_manager():
    """测试代理管理器"""
    try:
        print("\n测试代理管理器...")
        
        # 加载代理列表
        proxy_manager.load_proxies()
        
        # 获取随机代理
        proxy = proxy_manager.get_random_proxy()
        if not proxy:
            print("没有可用代理")
            return
        
        print(f"随机选择代理: {proxy['host']}:{proxy['port']}")
        
        # 测试代理
        print("测试代理连接...")
        proxies = proxy_manager.get_requests_proxies(proxy)
        
        try:
            response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=5)
            print(f"状态码: {response.status_code}")
            print(f"代理IP地址: {response.json().get('origin')}")
            print("代理测试成功")
        except Exception as e:
            print(f"代理测试失败: {str(e)}")
        
        # 测试获取工作代理
        print("\n测试获取工作代理...")
        working_proxy = proxy_manager.get_working_proxy()
        if working_proxy:
            print(f"工作代理: {working_proxy['host']}:{working_proxy['port']}")
        else:
            print("没有找到工作代理")
            
    except Exception as e:
        print(f"代理管理器测试失败: {str(e)}")


def test_wallet_proxy():
    """测试钱包代理功能"""
    try:
        print("\n测试钱包代理功能...")
        
        # 确保配置中启用了代理文件
        astar_config = config.get_dex_config('astar')
        proxy_config = astar_config.get('proxy', {})
        use_proxy_file = proxy_config.get('use_proxy_file', False)
        
        print(f"代理文件功能是否启用: {use_proxy_file}")
        
        # 创建钱包实例
        wallet = AstarWallet()
        
        # 测试RPC连接
        print("测试RPC连接...")
        block_number = wallet.web3.eth.block_number
        print(f"当前区块高度: {block_number}")
        
        # 获取账户余额
        balance = wallet.get_native_balance()
        print(f"钱包余额: {balance} ASTR")
        
        print("钱包代理测试成功")
    except Exception as e:
        print(f"钱包代理测试失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试代理功能')
    parser.add_argument('-d', '--direct', action='store_true', help='测试直接连接')
    parser.add_argument('-p', '--proxy', action='store_true', help='测试代理管理器')
    parser.add_argument('-w', '--wallet', action='store_true', help='测试钱包代理功能')
    parser.add_argument('-a', '--all', action='store_true', help='测试所有功能')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，则测试所有功能
    if not (args.direct or args.proxy or args.wallet):
        args.all = True
    
    # 测试直接连接
    if args.direct or args.all:
        test_direct_connection()
    
    # 测试代理管理器
    if args.proxy or args.all:
        test_proxy_manager()
    
    # 测试钱包代理功能
    if args.wallet or args.all:
        test_wallet_proxy()


if __name__ == "__main__":
    main() 