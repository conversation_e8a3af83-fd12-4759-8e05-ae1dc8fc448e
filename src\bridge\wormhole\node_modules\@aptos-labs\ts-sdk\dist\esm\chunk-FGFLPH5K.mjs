import{b as c}from"./chunk-RGKRCZ36.mjs";import{a as o}from"./chunk-EBMEXURY.mjs";import{b as n}from"./chunk-STY74NUA.mjs";import{sha3_256 as d}from"@noble/hashes/sha3";var r=class r extends o{constructor(t){super();let{data:e}=t,i=n.fromHexInput(e);if(i.toUint8Array().length!==r.LENGTH)throw new Error(`Authentication Key length should be ${r.LENGTH}`);this.data=i}serialize(t){t.serializeFixedBytes(this.data.toUint8Array())}static deserialize(t){let e=t.deserializeFixedBytes(r.LENGTH);return new r({data:e})}toUint8Array(){return this.data.toUint8Array()}static fromSchemeAndBytes(t){let{scheme:e,input:i}=t,u=n.fromHexInput(i).toUint8Array(),h=new Uint8Array([...u,e]),a=d.create();a.update(h);let y=a.digest();return new r({data:y})}static fromPublicKeyAndScheme(t){let{publicKey:e}=t;return e.authKey()}static fromPublicKey(t){let{publicKey:e}=t;return e.authKey()}derivedAddress(){return new c(this.data.toUint8Array())}};r.LENGTH=32;var s=r;export{s as a};
//# sourceMappingURL=chunk-FGFLPH5K.mjs.map