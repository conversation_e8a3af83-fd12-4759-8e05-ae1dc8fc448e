"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcExchangeApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../transformers/index.js");
const index_js_2 = require("../types/index.js");
core_proto_ts_1.InjectiveExchangeV1Beta1Query;
/**
 * @category Chain Grpc API
 */
class ChainGrpcExchangeApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_2.ChainModule.Exchange;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryExchangeParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.QueryExchangeParams(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.moduleParamsResponseToParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'QueryExchangeParams',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'QueryExchangeParams',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchModuleState() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryModuleStateRequest.create();
        try {
            const response = await this.retry(() => this.client.ExchangeModuleState(request, this.metadata));
            return response.state;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'ExchangeModuleState',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'ExchangeModuleState',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchFeeDiscountSchedule() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryFeeDiscountScheduleRequest.create();
        try {
            const response = await this.retry(() => this.client.FeeDiscountSchedule(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.feeDiscountScheduleResponseToFeeDiscountSchedule(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'FeeDiscountSchedule',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'FeeDiscountSchedule',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchFeeDiscountAccountInfo(injectiveAddress) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryFeeDiscountAccountInfoRequest.create();
        request.account = injectiveAddress;
        try {
            const response = await this.retry(() => this.client.FeeDiscountAccountInfo(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.feeDiscountAccountInfoResponseToFeeDiscountAccountInfo(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'FeeDiscountAccountInfo',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'FeeDiscountAccountInfo',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchTradingRewardsCampaign() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryTradeRewardCampaignRequest.create();
        try {
            const response = await this.retry(() => this.client.TradeRewardCampaign(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.tradingRewardsCampaignResponseToTradingRewardsCampaign(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'TradeRewardCampaign',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'TradeRewardCampaign',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchTradeRewardPoints(injectiveAddresses) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryTradeRewardPointsRequest.create();
        request.accounts = injectiveAddresses;
        try {
            const response = await this.retry(() => this.client.TradeRewardPoints(request, this.metadata));
            return response.accountTradeRewardPoints;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'TradeRewardPoints',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'TradeRewardPoints',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchPendingTradeRewardPoints(injectiveAddresses, timestamp) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryTradeRewardPointsRequest.create();
        request.accounts = injectiveAddresses;
        if (timestamp) {
            request.pendingPoolTimestamp = timestamp.toString();
        }
        try {
            const response = await this.retry(() => this.client.PendingTradeRewardPoints(request, this.metadata));
            return response.accountTradeRewardPoints;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PendingTradeRewardPoints',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PendingTradeRewardPoints',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchPositions() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryPositionsRequest.create();
        try {
            const response = await this.retry(() => this.client.Positions(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.positionsResponseToPositions(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Positions',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Positions',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchSubaccountTradeNonce(subaccountId) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QuerySubaccountTradeNonceRequest.create();
        request.subaccountId = subaccountId;
        try {
            const response = await this.retry(() => this.client.SubaccountTradeNonce(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'SubaccountTradeNonce',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'SubaccountTradeNonce',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchIsOptedOutOfRewards(account) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryIsOptedOutOfRewardsRequest.create();
        request.account = account;
        try {
            const response = await this.retry(() => this.client.IsOptedOutOfRewards(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.isOptedOutOfRewardsResponseToIsOptedOutOfRewards(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'IsOptedOutOfRewards',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'IsOptedOutOfRewards',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchActiveStakeGrant(account) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryActiveStakeGrantRequest.create();
        request.grantee = account;
        try {
            const response = await this.retry(() => this.client.ActiveStakeGrant(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.activeStakeGrantResponseToActiveStakeGrant(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'ActiveStakeGrant',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'ActiveStakeGrant',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchDenomDecimal(denom) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryDenomDecimalRequest.create();
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.DenomDecimal(request, this.metadata));
            return response.decimal;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DenomDecimal',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DenomDecimal',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchDenomDecimals() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryDenomDecimalsRequest.create();
        try {
            const response = await this.retry(() => this.client.DenomDecimals(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.denomDecimalsResponseToDenomDecimals(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DenomDecimals',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DenomDecimals',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchDenomMinNotional(denom) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryDenomMinNotionalRequest.create();
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.DenomMinNotional(request, this.metadata));
            return response.amount;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DenomMinNotional',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DenomMinNotional',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchDenomMinNotionals() {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryDenomMinNotionalsRequest.create();
        try {
            const response = await this.retry(() => this.client.DenomMinNotionals(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.denomMinNotionalsResponseToDenomMinNotionals(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DenomMinNotionals',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DenomMinNotionals',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchDerivativeMarkets(status, marketIds) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QueryDerivativeMarketsRequest.create();
        if (status) {
            request.status = status;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        try {
            const response = await this.retry(() => this.client.DerivativeMarkets(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.fullDerivativeMarketsResponseToDerivativeMarkets(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DerivativeMarkets',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DerivativeMarkets',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchSpotMarkets(status, marketIds) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QuerySpotMarketsRequest.create();
        if (status) {
            request.status = status;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        try {
            const response = await this.retry(() => this.client.SpotMarkets(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.spotMarketsResponseToSpotMarkets(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DerivativeMarkets',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DerivativeMarkets',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
    async fetchFullSpotMarkets(status, marketIds) {
        const request = core_proto_ts_1.InjectiveExchangeV1Beta1Query.QuerySpotMarketsRequest.create();
        if (status) {
            request.status = status;
        }
        if (marketIds) {
            request.marketIds = marketIds;
        }
        try {
            const response = await this.retry(() => this.client.FullSpotMarkets(request, this.metadata));
            return index_js_1.ChainGrpcExchangeTransformer.fullSpotMarketsResponseToSpotMarkets(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectiveExchangeV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DerivativeMarkets',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DerivativeMarkets',
                contextModule: index_js_2.ChainModule.Exchange,
            });
        }
    }
}
exports.ChainGrpcExchangeApi = ChainGrpcExchangeApi;
