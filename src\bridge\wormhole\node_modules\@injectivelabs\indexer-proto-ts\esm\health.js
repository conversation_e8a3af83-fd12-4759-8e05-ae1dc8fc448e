/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "api.v1";
function createBaseGetStatusRequest() {
    return {};
}
export const GetStatusRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetStatusRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return GetStatusRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseGetStatusRequest();
        return message;
    },
};
function createBaseGetStatusResponse() {
    return { s: "", errmsg: "", data: undefined, status: "" };
}
export const GetStatusResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.s !== "") {
            writer.uint32(10).string(message.s);
        }
        if (message.errmsg !== "") {
            writer.uint32(18).string(message.errmsg);
        }
        if (message.data !== undefined) {
            HealthStatus.encode(message.data, writer.uint32(26).fork()).ldelim();
        }
        if (message.status !== "") {
            writer.uint32(34).string(message.status);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetStatusResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.s = reader.string();
                    break;
                case 2:
                    message.errmsg = reader.string();
                    break;
                case 3:
                    message.data = HealthStatus.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.status = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            s: isSet(object.s) ? String(object.s) : "",
            errmsg: isSet(object.errmsg) ? String(object.errmsg) : "",
            data: isSet(object.data) ? HealthStatus.fromJSON(object.data) : undefined,
            status: isSet(object.status) ? String(object.status) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.s !== undefined && (obj.s = message.s);
        message.errmsg !== undefined && (obj.errmsg = message.errmsg);
        message.data !== undefined && (obj.data = message.data ? HealthStatus.toJSON(message.data) : undefined);
        message.status !== undefined && (obj.status = message.status);
        return obj;
    },
    create(base) {
        return GetStatusResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetStatusResponse();
        message.s = object.s ?? "";
        message.errmsg = object.errmsg ?? "";
        message.data = (object.data !== undefined && object.data !== null)
            ? HealthStatus.fromPartial(object.data)
            : undefined;
        message.status = object.status ?? "";
        return message;
    },
};
function createBaseHealthStatus() {
    return {
        localHeight: 0,
        localTimestamp: 0,
        horacleHeight: 0,
        horacleTimestamp: 0,
        migrationLastVersion: 0,
        epHeight: 0,
        epTimestamp: 0,
    };
}
export const HealthStatus = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.localHeight !== 0) {
            writer.uint32(8).sint32(message.localHeight);
        }
        if (message.localTimestamp !== 0) {
            writer.uint32(16).sint32(message.localTimestamp);
        }
        if (message.horacleHeight !== 0) {
            writer.uint32(24).sint32(message.horacleHeight);
        }
        if (message.horacleTimestamp !== 0) {
            writer.uint32(32).sint32(message.horacleTimestamp);
        }
        if (message.migrationLastVersion !== 0) {
            writer.uint32(40).sint32(message.migrationLastVersion);
        }
        if (message.epHeight !== 0) {
            writer.uint32(48).sint32(message.epHeight);
        }
        if (message.epTimestamp !== 0) {
            writer.uint32(56).sint32(message.epTimestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHealthStatus();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.localHeight = reader.sint32();
                    break;
                case 2:
                    message.localTimestamp = reader.sint32();
                    break;
                case 3:
                    message.horacleHeight = reader.sint32();
                    break;
                case 4:
                    message.horacleTimestamp = reader.sint32();
                    break;
                case 5:
                    message.migrationLastVersion = reader.sint32();
                    break;
                case 6:
                    message.epHeight = reader.sint32();
                    break;
                case 7:
                    message.epTimestamp = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            localHeight: isSet(object.localHeight) ? Number(object.localHeight) : 0,
            localTimestamp: isSet(object.localTimestamp) ? Number(object.localTimestamp) : 0,
            horacleHeight: isSet(object.horacleHeight) ? Number(object.horacleHeight) : 0,
            horacleTimestamp: isSet(object.horacleTimestamp) ? Number(object.horacleTimestamp) : 0,
            migrationLastVersion: isSet(object.migrationLastVersion) ? Number(object.migrationLastVersion) : 0,
            epHeight: isSet(object.epHeight) ? Number(object.epHeight) : 0,
            epTimestamp: isSet(object.epTimestamp) ? Number(object.epTimestamp) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.localHeight !== undefined && (obj.localHeight = Math.round(message.localHeight));
        message.localTimestamp !== undefined && (obj.localTimestamp = Math.round(message.localTimestamp));
        message.horacleHeight !== undefined && (obj.horacleHeight = Math.round(message.horacleHeight));
        message.horacleTimestamp !== undefined && (obj.horacleTimestamp = Math.round(message.horacleTimestamp));
        message.migrationLastVersion !== undefined && (obj.migrationLastVersion = Math.round(message.migrationLastVersion));
        message.epHeight !== undefined && (obj.epHeight = Math.round(message.epHeight));
        message.epTimestamp !== undefined && (obj.epTimestamp = Math.round(message.epTimestamp));
        return obj;
    },
    create(base) {
        return HealthStatus.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseHealthStatus();
        message.localHeight = object.localHeight ?? 0;
        message.localTimestamp = object.localTimestamp ?? 0;
        message.horacleHeight = object.horacleHeight ?? 0;
        message.horacleTimestamp = object.horacleTimestamp ?? 0;
        message.migrationLastVersion = object.migrationLastVersion ?? 0;
        message.epHeight = object.epHeight ?? 0;
        message.epTimestamp = object.epTimestamp ?? 0;
        return message;
    },
};
export class HealthClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.GetStatus = this.GetStatus.bind(this);
    }
    GetStatus(request, metadata) {
        return this.rpc.unary(HealthGetStatusDesc, GetStatusRequest.fromPartial(request), metadata);
    }
}
export const HealthDesc = { serviceName: "api.v1.Health" };
export const HealthGetStatusDesc = {
    methodName: "GetStatus",
    service: HealthDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetStatusRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetStatusResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
