import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { RequestFinalizeBlock, ResponseCommit, ResponseFinalizeBlock } from "../../../../tendermint/abci/types";
import { StoreKVPair } from "../../v1beta1/listening";
export declare const protobufPackage = "cosmos.store.streaming.abci";
/** ListenEndBlockRequest is the request type for the ListenEndBlock RPC method */
export interface ListenFinalizeBlockRequest {
    req: RequestFinalizeBlock | undefined;
    res: ResponseFinalizeBlock | undefined;
}
/** ListenEndBlockResponse is the response type for the ListenEndBlock RPC method */
export interface ListenFinalizeBlockResponse {
}
/** ListenCommitRequest is the request type for the ListenCommit RPC method */
export interface ListenCommitRequest {
    /** explicitly pass in block height as ResponseCommit does not contain this info */
    blockHeight: string;
    res: ResponseCommit | undefined;
    changeSet: StoreKVPair[];
}
/** ListenCommitResponse is the response type for the ListenCommit RPC method */
export interface ListenCommitResponse {
}
export declare const ListenFinalizeBlockRequest: {
    encode(message: ListenFinalizeBlockRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListenFinalizeBlockRequest;
    fromJSON(object: any): ListenFinalizeBlockRequest;
    toJSON(message: ListenFinalizeBlockRequest): unknown;
    create(base?: DeepPartial<ListenFinalizeBlockRequest>): ListenFinalizeBlockRequest;
    fromPartial(object: DeepPartial<ListenFinalizeBlockRequest>): ListenFinalizeBlockRequest;
};
export declare const ListenFinalizeBlockResponse: {
    encode(_: ListenFinalizeBlockResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListenFinalizeBlockResponse;
    fromJSON(_: any): ListenFinalizeBlockResponse;
    toJSON(_: ListenFinalizeBlockResponse): unknown;
    create(base?: DeepPartial<ListenFinalizeBlockResponse>): ListenFinalizeBlockResponse;
    fromPartial(_: DeepPartial<ListenFinalizeBlockResponse>): ListenFinalizeBlockResponse;
};
export declare const ListenCommitRequest: {
    encode(message: ListenCommitRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListenCommitRequest;
    fromJSON(object: any): ListenCommitRequest;
    toJSON(message: ListenCommitRequest): unknown;
    create(base?: DeepPartial<ListenCommitRequest>): ListenCommitRequest;
    fromPartial(object: DeepPartial<ListenCommitRequest>): ListenCommitRequest;
};
export declare const ListenCommitResponse: {
    encode(_: ListenCommitResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListenCommitResponse;
    fromJSON(_: any): ListenCommitResponse;
    toJSON(_: ListenCommitResponse): unknown;
    create(base?: DeepPartial<ListenCommitResponse>): ListenCommitResponse;
    fromPartial(_: DeepPartial<ListenCommitResponse>): ListenCommitResponse;
};
/** ABCIListenerService is the service for the BaseApp ABCIListener interface */
export interface ABCIListenerService {
    /** ListenFinalizeBlock is the corresponding endpoint for ABCIListener.ListenEndBlock */
    ListenFinalizeBlock(request: DeepPartial<ListenFinalizeBlockRequest>, metadata?: grpc.Metadata): Promise<ListenFinalizeBlockResponse>;
    /** ListenCommit is the corresponding endpoint for ABCIListener.ListenCommit */
    ListenCommit(request: DeepPartial<ListenCommitRequest>, metadata?: grpc.Metadata): Promise<ListenCommitResponse>;
}
export declare class ABCIListenerServiceClientImpl implements ABCIListenerService {
    private readonly rpc;
    constructor(rpc: Rpc);
    ListenFinalizeBlock(request: DeepPartial<ListenFinalizeBlockRequest>, metadata?: grpc.Metadata): Promise<ListenFinalizeBlockResponse>;
    ListenCommit(request: DeepPartial<ListenCommitRequest>, metadata?: grpc.Metadata): Promise<ListenCommitResponse>;
}
export declare const ABCIListenerServiceDesc: {
    serviceName: string;
};
export declare const ABCIListenerServiceListenFinalizeBlockDesc: UnaryMethodDefinitionish;
export declare const ABCIListenerServiceListenCommitDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
