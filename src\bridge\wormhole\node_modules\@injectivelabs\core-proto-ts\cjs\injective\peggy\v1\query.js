"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryValsetConfirmsByNonceDesc = exports.QueryValsetConfirmDesc = exports.QueryValsetRequestDesc = exports.QueryCurrentValsetDesc = exports.QueryParamsDesc = exports.QueryDesc = exports.QueryClientImpl = exports.MissingNoncesResponse = exports.MissingNoncesRequest = exports.QueryModuleStateResponse = exports.QueryModuleStateRequest = exports.QueryPendingSendToEthResponse = exports.QueryPendingSendToEth = exports.QueryDelegateKeysByOrchestratorAddressResponse = exports.QueryDelegateKeysByOrchestratorAddress = exports.QueryDelegateKeysByEthAddressResponse = exports.QueryDelegateKeysByEthAddress = exports.QueryDelegateKeysByValidatorAddressResponse = exports.QueryDelegateKeysByValidatorAddress = exports.QueryDenomToERC20Response = exports.QueryDenomToERC20Request = exports.QueryERC20ToDenomResponse = exports.QueryERC20ToDenomRequest = exports.QueryLastEventByAddrResponse = exports.QueryLastEventByAddrRequest = exports.QueryBatchConfirmsResponse = exports.QueryBatchConfirmsRequest = exports.QueryBatchRequestByNonceResponse = exports.QueryBatchRequestByNonceRequest = exports.QueryOutgoingTxBatchesResponse = exports.QueryOutgoingTxBatchesRequest = exports.QueryLastPendingBatchRequestByAddrResponse = exports.QueryLastPendingBatchRequestByAddrRequest = exports.QueryBatchFeeResponse = exports.QueryBatchFeeRequest = exports.QueryLastPendingValsetRequestByAddrResponse = exports.QueryLastPendingValsetRequestByAddrRequest = exports.QueryLastValsetRequestsResponse = exports.QueryLastValsetRequestsRequest = exports.QueryValsetConfirmsByNonceResponse = exports.QueryValsetConfirmsByNonceRequest = exports.QueryValsetConfirmResponse = exports.QueryValsetConfirmRequest = exports.QueryValsetRequestResponse = exports.QueryValsetRequestRequest = exports.QueryCurrentValsetResponse = exports.QueryCurrentValsetRequest = exports.QueryParamsResponse = exports.QueryParamsRequest = exports.protobufPackage = void 0;
exports.GrpcWebError = exports.GrpcWebImpl = exports.QueryMissingPeggoNoncesDesc = exports.QueryPeggyModuleStateDesc = exports.QueryGetDelegateKeyByOrchestratorDesc = exports.QueryGetDelegateKeyByEthDesc = exports.QueryGetDelegateKeyByValidatorDesc = exports.QueryDenomToERC20Desc = exports.QueryERC20ToDenomDesc = exports.QueryBatchConfirmsDesc = exports.QueryBatchRequestByNonceDesc = exports.QueryLastPendingBatchRequestByAddrDesc = exports.QueryOutgoingTxBatchesDesc = exports.QueryBatchFeesDesc = exports.QueryGetPendingSendToEthDesc = exports.QueryLastEventByAddrDesc = exports.QueryLastPendingValsetRequestByAddrDesc = exports.QueryLastValsetRequestsDesc = void 0;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var batch_1 = require("./batch.js");
var genesis_1 = require("./genesis.js");
var msgs_1 = require("./msgs.js");
var params_1 = require("./params.js");
var pool_1 = require("./pool.js");
var types_1 = require("./types.js");
exports.protobufPackage = "injective.peggy.v1";
function createBaseQueryParamsRequest() {
    return {};
}
exports.QueryParamsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryParamsRequest();
        return message;
    },
};
function createBaseQueryParamsResponse() {
    return { params: undefined };
}
exports.QueryParamsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            params_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = params_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { params: isSet(object.params) ? params_1.Params.fromJSON(object.params) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? params_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? params_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQueryCurrentValsetRequest() {
    return {};
}
exports.QueryCurrentValsetRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryCurrentValsetRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryCurrentValsetRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryCurrentValsetRequest();
        return message;
    },
};
function createBaseQueryCurrentValsetResponse() {
    return { valset: undefined };
}
exports.QueryCurrentValsetResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.valset !== undefined) {
            types_1.Valset.encode(message.valset, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryCurrentValsetResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valset = types_1.Valset.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { valset: isSet(object.valset) ? types_1.Valset.fromJSON(object.valset) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.valset !== undefined && (obj.valset = message.valset ? types_1.Valset.toJSON(message.valset) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryCurrentValsetResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryCurrentValsetResponse();
        message.valset = (object.valset !== undefined && object.valset !== null)
            ? types_1.Valset.fromPartial(object.valset)
            : undefined;
        return message;
    },
};
function createBaseQueryValsetRequestRequest() {
    return { nonce: "0" };
}
exports.QueryValsetRequestRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetRequestRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { nonce: isSet(object.nonce) ? String(object.nonce) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetRequestRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryValsetRequestRequest();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseQueryValsetRequestResponse() {
    return { valset: undefined };
}
exports.QueryValsetRequestResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.valset !== undefined) {
            types_1.Valset.encode(message.valset, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetRequestResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valset = types_1.Valset.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { valset: isSet(object.valset) ? types_1.Valset.fromJSON(object.valset) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.valset !== undefined && (obj.valset = message.valset ? types_1.Valset.toJSON(message.valset) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetRequestResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryValsetRequestResponse();
        message.valset = (object.valset !== undefined && object.valset !== null)
            ? types_1.Valset.fromPartial(object.valset)
            : undefined;
        return message;
    },
};
function createBaseQueryValsetConfirmRequest() {
    return { nonce: "0", address: "" };
}
exports.QueryValsetConfirmRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        if (message.address !== "") {
            writer.uint32(18).string(message.address);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetConfirmRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            address: isSet(object.address) ? String(object.address) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetConfirmRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryValsetConfirmRequest();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.address = (_b = object.address) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryValsetConfirmResponse() {
    return { confirm: undefined };
}
exports.QueryValsetConfirmResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.confirm !== undefined) {
            msgs_1.MsgValsetConfirm.encode(message.confirm, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetConfirmResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.confirm = msgs_1.MsgValsetConfirm.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { confirm: isSet(object.confirm) ? msgs_1.MsgValsetConfirm.fromJSON(object.confirm) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.confirm !== undefined &&
            (obj.confirm = message.confirm ? msgs_1.MsgValsetConfirm.toJSON(message.confirm) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetConfirmResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryValsetConfirmResponse();
        message.confirm = (object.confirm !== undefined && object.confirm !== null)
            ? msgs_1.MsgValsetConfirm.fromPartial(object.confirm)
            : undefined;
        return message;
    },
};
function createBaseQueryValsetConfirmsByNonceRequest() {
    return { nonce: "0" };
}
exports.QueryValsetConfirmsByNonceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetConfirmsByNonceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { nonce: isSet(object.nonce) ? String(object.nonce) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetConfirmsByNonceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryValsetConfirmsByNonceRequest();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseQueryValsetConfirmsByNonceResponse() {
    return { confirms: [] };
}
exports.QueryValsetConfirmsByNonceResponse = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.confirms), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                msgs_1.MsgValsetConfirm.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryValsetConfirmsByNonceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.confirms.push(msgs_1.MsgValsetConfirm.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            confirms: Array.isArray(object === null || object === void 0 ? void 0 : object.confirms) ? object.confirms.map(function (e) { return msgs_1.MsgValsetConfirm.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.confirms) {
            obj.confirms = message.confirms.map(function (e) { return e ? msgs_1.MsgValsetConfirm.toJSON(e) : undefined; });
        }
        else {
            obj.confirms = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryValsetConfirmsByNonceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryValsetConfirmsByNonceResponse();
        message.confirms = ((_a = object.confirms) === null || _a === void 0 ? void 0 : _a.map(function (e) { return msgs_1.MsgValsetConfirm.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryLastValsetRequestsRequest() {
    return {};
}
exports.QueryLastValsetRequestsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastValsetRequestsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryLastValsetRequestsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryLastValsetRequestsRequest();
        return message;
    },
};
function createBaseQueryLastValsetRequestsResponse() {
    return { valsets: [] };
}
exports.QueryLastValsetRequestsResponse = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.valsets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.Valset.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastValsetRequestsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valsets.push(types_1.Valset.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { valsets: Array.isArray(object === null || object === void 0 ? void 0 : object.valsets) ? object.valsets.map(function (e) { return types_1.Valset.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.valsets) {
            obj.valsets = message.valsets.map(function (e) { return e ? types_1.Valset.toJSON(e) : undefined; });
        }
        else {
            obj.valsets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryLastValsetRequestsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLastValsetRequestsResponse();
        message.valsets = ((_a = object.valsets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_1.Valset.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryLastPendingValsetRequestByAddrRequest() {
    return { address: "" };
}
exports.QueryLastPendingValsetRequestByAddrRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastPendingValsetRequestByAddrRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { address: isSet(object.address) ? String(object.address) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create: function (base) {
        return exports.QueryLastPendingValsetRequestByAddrRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLastPendingValsetRequestByAddrRequest();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryLastPendingValsetRequestByAddrResponse() {
    return { valsets: [] };
}
exports.QueryLastPendingValsetRequestByAddrResponse = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.valsets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                types_1.Valset.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastPendingValsetRequestByAddrResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.valsets.push(types_1.Valset.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { valsets: Array.isArray(object === null || object === void 0 ? void 0 : object.valsets) ? object.valsets.map(function (e) { return types_1.Valset.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.valsets) {
            obj.valsets = message.valsets.map(function (e) { return e ? types_1.Valset.toJSON(e) : undefined; });
        }
        else {
            obj.valsets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryLastPendingValsetRequestByAddrResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLastPendingValsetRequestByAddrResponse();
        message.valsets = ((_a = object.valsets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return types_1.Valset.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryBatchFeeRequest() {
    return {};
}
exports.QueryBatchFeeRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchFeeRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchFeeRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryBatchFeeRequest();
        return message;
    },
};
function createBaseQueryBatchFeeResponse() {
    return { batchFees: [] };
}
exports.QueryBatchFeeResponse = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.batchFees), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                pool_1.BatchFees.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchFeeResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batchFees.push(pool_1.BatchFees.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            batchFees: Array.isArray(object === null || object === void 0 ? void 0 : object.batchFees) ? object.batchFees.map(function (e) { return pool_1.BatchFees.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.batchFees) {
            obj.batchFees = message.batchFees.map(function (e) { return e ? pool_1.BatchFees.toJSON(e) : undefined; });
        }
        else {
            obj.batchFees = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchFeeResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBatchFeeResponse();
        message.batchFees = ((_a = object.batchFees) === null || _a === void 0 ? void 0 : _a.map(function (e) { return pool_1.BatchFees.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryLastPendingBatchRequestByAddrRequest() {
    return { address: "" };
}
exports.QueryLastPendingBatchRequestByAddrRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastPendingBatchRequestByAddrRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { address: isSet(object.address) ? String(object.address) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create: function (base) {
        return exports.QueryLastPendingBatchRequestByAddrRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLastPendingBatchRequestByAddrRequest();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryLastPendingBatchRequestByAddrResponse() {
    return { batch: undefined };
}
exports.QueryLastPendingBatchRequestByAddrResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.batch !== undefined) {
            batch_1.OutgoingTxBatch.encode(message.batch, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastPendingBatchRequestByAddrResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batch = batch_1.OutgoingTxBatch.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { batch: isSet(object.batch) ? batch_1.OutgoingTxBatch.fromJSON(object.batch) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.batch !== undefined && (obj.batch = message.batch ? batch_1.OutgoingTxBatch.toJSON(message.batch) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryLastPendingBatchRequestByAddrResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryLastPendingBatchRequestByAddrResponse();
        message.batch = (object.batch !== undefined && object.batch !== null)
            ? batch_1.OutgoingTxBatch.fromPartial(object.batch)
            : undefined;
        return message;
    },
};
function createBaseQueryOutgoingTxBatchesRequest() {
    return {};
}
exports.QueryOutgoingTxBatchesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOutgoingTxBatchesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryOutgoingTxBatchesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryOutgoingTxBatchesRequest();
        return message;
    },
};
function createBaseQueryOutgoingTxBatchesResponse() {
    return { batches: [] };
}
exports.QueryOutgoingTxBatchesResponse = {
    encode: function (message, writer) {
        var e_5, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.batches), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                batch_1.OutgoingTxBatch.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOutgoingTxBatchesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batches.push(batch_1.OutgoingTxBatch.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            batches: Array.isArray(object === null || object === void 0 ? void 0 : object.batches) ? object.batches.map(function (e) { return batch_1.OutgoingTxBatch.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.batches) {
            obj.batches = message.batches.map(function (e) { return e ? batch_1.OutgoingTxBatch.toJSON(e) : undefined; });
        }
        else {
            obj.batches = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryOutgoingTxBatchesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOutgoingTxBatchesResponse();
        message.batches = ((_a = object.batches) === null || _a === void 0 ? void 0 : _a.map(function (e) { return batch_1.OutgoingTxBatch.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryBatchRequestByNonceRequest() {
    return { nonce: "0", contractAddress: "" };
}
exports.QueryBatchRequestByNonceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        if (message.contractAddress !== "") {
            writer.uint32(18).string(message.contractAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchRequestByNonceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchRequestByNonceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryBatchRequestByNonceRequest();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.contractAddress = (_b = object.contractAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryBatchRequestByNonceResponse() {
    return { batch: undefined };
}
exports.QueryBatchRequestByNonceResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.batch !== undefined) {
            batch_1.OutgoingTxBatch.encode(message.batch, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchRequestByNonceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.batch = batch_1.OutgoingTxBatch.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { batch: isSet(object.batch) ? batch_1.OutgoingTxBatch.fromJSON(object.batch) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.batch !== undefined && (obj.batch = message.batch ? batch_1.OutgoingTxBatch.toJSON(message.batch) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchRequestByNonceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryBatchRequestByNonceResponse();
        message.batch = (object.batch !== undefined && object.batch !== null)
            ? batch_1.OutgoingTxBatch.fromPartial(object.batch)
            : undefined;
        return message;
    },
};
function createBaseQueryBatchConfirmsRequest() {
    return { nonce: "0", contractAddress: "" };
}
exports.QueryBatchConfirmsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== "0") {
            writer.uint32(8).uint64(message.nonce);
        }
        if (message.contractAddress !== "") {
            writer.uint32(18).string(message.contractAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchConfirmsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = longToString(reader.uint64());
                    break;
                case 2:
                    message.contractAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            nonce: isSet(object.nonce) ? String(object.nonce) : "0",
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = message.nonce);
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchConfirmsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryBatchConfirmsRequest();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : "0";
        message.contractAddress = (_b = object.contractAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryBatchConfirmsResponse() {
    return { confirms: [] };
}
exports.QueryBatchConfirmsResponse = {
    encode: function (message, writer) {
        var e_6, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.confirms), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                msgs_1.MsgConfirmBatch.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBatchConfirmsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.confirms.push(msgs_1.MsgConfirmBatch.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            confirms: Array.isArray(object === null || object === void 0 ? void 0 : object.confirms) ? object.confirms.map(function (e) { return msgs_1.MsgConfirmBatch.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.confirms) {
            obj.confirms = message.confirms.map(function (e) { return e ? msgs_1.MsgConfirmBatch.toJSON(e) : undefined; });
        }
        else {
            obj.confirms = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBatchConfirmsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBatchConfirmsResponse();
        message.confirms = ((_a = object.confirms) === null || _a === void 0 ? void 0 : _a.map(function (e) { return msgs_1.MsgConfirmBatch.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryLastEventByAddrRequest() {
    return { address: "" };
}
exports.QueryLastEventByAddrRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastEventByAddrRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { address: isSet(object.address) ? String(object.address) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        return obj;
    },
    create: function (base) {
        return exports.QueryLastEventByAddrRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryLastEventByAddrRequest();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryLastEventByAddrResponse() {
    return { lastClaimEvent: undefined };
}
exports.QueryLastEventByAddrResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.lastClaimEvent !== undefined) {
            types_1.LastClaimEvent.encode(message.lastClaimEvent, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryLastEventByAddrResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.lastClaimEvent = types_1.LastClaimEvent.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            lastClaimEvent: isSet(object.lastClaimEvent) ? types_1.LastClaimEvent.fromJSON(object.lastClaimEvent) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.lastClaimEvent !== undefined &&
            (obj.lastClaimEvent = message.lastClaimEvent ? types_1.LastClaimEvent.toJSON(message.lastClaimEvent) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryLastEventByAddrResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryLastEventByAddrResponse();
        message.lastClaimEvent = (object.lastClaimEvent !== undefined && object.lastClaimEvent !== null)
            ? types_1.LastClaimEvent.fromPartial(object.lastClaimEvent)
            : undefined;
        return message;
    },
};
function createBaseQueryERC20ToDenomRequest() {
    return { erc20: "" };
}
exports.QueryERC20ToDenomRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.erc20 !== "") {
            writer.uint32(10).string(message.erc20);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryERC20ToDenomRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.erc20 = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { erc20: isSet(object.erc20) ? String(object.erc20) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.erc20 !== undefined && (obj.erc20 = message.erc20);
        return obj;
    },
    create: function (base) {
        return exports.QueryERC20ToDenomRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryERC20ToDenomRequest();
        message.erc20 = (_a = object.erc20) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryERC20ToDenomResponse() {
    return { denom: "", cosmosOriginated: false };
}
exports.QueryERC20ToDenomResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.cosmosOriginated === true) {
            writer.uint32(16).bool(message.cosmosOriginated);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryERC20ToDenomResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.cosmosOriginated = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            cosmosOriginated: isSet(object.cosmosOriginated) ? Boolean(object.cosmosOriginated) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.cosmosOriginated !== undefined && (obj.cosmosOriginated = message.cosmosOriginated);
        return obj;
    },
    create: function (base) {
        return exports.QueryERC20ToDenomResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryERC20ToDenomResponse();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.cosmosOriginated = (_b = object.cosmosOriginated) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseQueryDenomToERC20Request() {
    return { denom: "" };
}
exports.QueryDenomToERC20Request = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomToERC20Request();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomToERC20Request.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomToERC20Request();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDenomToERC20Response() {
    return { erc20: "", cosmosOriginated: false };
}
exports.QueryDenomToERC20Response = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.erc20 !== "") {
            writer.uint32(10).string(message.erc20);
        }
        if (message.cosmosOriginated === true) {
            writer.uint32(16).bool(message.cosmosOriginated);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomToERC20Response();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.erc20 = reader.string();
                    break;
                case 2:
                    message.cosmosOriginated = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            erc20: isSet(object.erc20) ? String(object.erc20) : "",
            cosmosOriginated: isSet(object.cosmosOriginated) ? Boolean(object.cosmosOriginated) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.erc20 !== undefined && (obj.erc20 = message.erc20);
        message.cosmosOriginated !== undefined && (obj.cosmosOriginated = message.cosmosOriginated);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomToERC20Response.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDenomToERC20Response();
        message.erc20 = (_a = object.erc20) !== null && _a !== void 0 ? _a : "";
        message.cosmosOriginated = (_b = object.cosmosOriginated) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseQueryDelegateKeysByValidatorAddress() {
    return { validatorAddress: "" };
}
exports.QueryDelegateKeysByValidatorAddress = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validatorAddress !== "") {
            writer.uint32(10).string(message.validatorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByValidatorAddress();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validatorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { validatorAddress: isSet(object.validatorAddress) ? String(object.validatorAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.validatorAddress !== undefined && (obj.validatorAddress = message.validatorAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByValidatorAddress.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDelegateKeysByValidatorAddress();
        message.validatorAddress = (_a = object.validatorAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDelegateKeysByValidatorAddressResponse() {
    return { ethAddress: "", orchestratorAddress: "" };
}
exports.QueryDelegateKeysByValidatorAddressResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ethAddress !== "") {
            writer.uint32(10).string(message.ethAddress);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByValidatorAddressResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ethAddress = reader.string();
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByValidatorAddressResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDelegateKeysByValidatorAddressResponse();
        message.ethAddress = (_a = object.ethAddress) !== null && _a !== void 0 ? _a : "";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryDelegateKeysByEthAddress() {
    return { ethAddress: "" };
}
exports.QueryDelegateKeysByEthAddress = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ethAddress !== "") {
            writer.uint32(10).string(message.ethAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByEthAddress();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ethAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByEthAddress.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDelegateKeysByEthAddress();
        message.ethAddress = (_a = object.ethAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDelegateKeysByEthAddressResponse() {
    return { validatorAddress: "", orchestratorAddress: "" };
}
exports.QueryDelegateKeysByEthAddressResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validatorAddress !== "") {
            writer.uint32(10).string(message.validatorAddress);
        }
        if (message.orchestratorAddress !== "") {
            writer.uint32(18).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByEthAddressResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validatorAddress = reader.string();
                    break;
                case 2:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validatorAddress: isSet(object.validatorAddress) ? String(object.validatorAddress) : "",
            orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validatorAddress !== undefined && (obj.validatorAddress = message.validatorAddress);
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByEthAddressResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDelegateKeysByEthAddressResponse();
        message.validatorAddress = (_a = object.validatorAddress) !== null && _a !== void 0 ? _a : "";
        message.orchestratorAddress = (_b = object.orchestratorAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryDelegateKeysByOrchestratorAddress() {
    return { orchestratorAddress: "" };
}
exports.QueryDelegateKeysByOrchestratorAddress = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orchestratorAddress !== "") {
            writer.uint32(10).string(message.orchestratorAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByOrchestratorAddress();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orchestratorAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { orchestratorAddress: isSet(object.orchestratorAddress) ? String(object.orchestratorAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.orchestratorAddress !== undefined && (obj.orchestratorAddress = message.orchestratorAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByOrchestratorAddress.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDelegateKeysByOrchestratorAddress();
        message.orchestratorAddress = (_a = object.orchestratorAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDelegateKeysByOrchestratorAddressResponse() {
    return { validatorAddress: "", ethAddress: "" };
}
exports.QueryDelegateKeysByOrchestratorAddressResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.validatorAddress !== "") {
            writer.uint32(10).string(message.validatorAddress);
        }
        if (message.ethAddress !== "") {
            writer.uint32(18).string(message.ethAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDelegateKeysByOrchestratorAddressResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.validatorAddress = reader.string();
                    break;
                case 2:
                    message.ethAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            validatorAddress: isSet(object.validatorAddress) ? String(object.validatorAddress) : "",
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.validatorAddress !== undefined && (obj.validatorAddress = message.validatorAddress);
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryDelegateKeysByOrchestratorAddressResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDelegateKeysByOrchestratorAddressResponse();
        message.validatorAddress = (_a = object.validatorAddress) !== null && _a !== void 0 ? _a : "";
        message.ethAddress = (_b = object.ethAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryPendingSendToEth() {
    return { senderAddress: "" };
}
exports.QueryPendingSendToEth = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.senderAddress !== "") {
            writer.uint32(10).string(message.senderAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPendingSendToEth();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.senderAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { senderAddress: isSet(object.senderAddress) ? String(object.senderAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.senderAddress !== undefined && (obj.senderAddress = message.senderAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryPendingSendToEth.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPendingSendToEth();
        message.senderAddress = (_a = object.senderAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryPendingSendToEthResponse() {
    return { transfersInBatches: [], unbatchedTransfers: [] };
}
exports.QueryPendingSendToEthResponse = {
    encode: function (message, writer) {
        var e_7, _a, e_8, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.transfersInBatches), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                batch_1.OutgoingTransferTx.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _e = __values(message.unbatchedTransfers), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                batch_1.OutgoingTransferTx.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPendingSendToEthResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.transfersInBatches.push(batch_1.OutgoingTransferTx.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.unbatchedTransfers.push(batch_1.OutgoingTransferTx.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            transfersInBatches: Array.isArray(object === null || object === void 0 ? void 0 : object.transfersInBatches)
                ? object.transfersInBatches.map(function (e) { return batch_1.OutgoingTransferTx.fromJSON(e); })
                : [],
            unbatchedTransfers: Array.isArray(object === null || object === void 0 ? void 0 : object.unbatchedTransfers)
                ? object.unbatchedTransfers.map(function (e) { return batch_1.OutgoingTransferTx.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.transfersInBatches) {
            obj.transfersInBatches = message.transfersInBatches.map(function (e) { return e ? batch_1.OutgoingTransferTx.toJSON(e) : undefined; });
        }
        else {
            obj.transfersInBatches = [];
        }
        if (message.unbatchedTransfers) {
            obj.unbatchedTransfers = message.unbatchedTransfers.map(function (e) { return e ? batch_1.OutgoingTransferTx.toJSON(e) : undefined; });
        }
        else {
            obj.unbatchedTransfers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryPendingSendToEthResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryPendingSendToEthResponse();
        message.transfersInBatches = ((_a = object.transfersInBatches) === null || _a === void 0 ? void 0 : _a.map(function (e) { return batch_1.OutgoingTransferTx.fromPartial(e); })) || [];
        message.unbatchedTransfers = ((_b = object.unbatchedTransfers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return batch_1.OutgoingTransferTx.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
exports.QueryModuleStateRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
exports.QueryModuleStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            genesis_1.GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? genesis_1.GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? genesis_1.GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? genesis_1.GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseMissingNoncesRequest() {
    return {};
}
exports.MissingNoncesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMissingNoncesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MissingNoncesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMissingNoncesRequest();
        return message;
    },
};
function createBaseMissingNoncesResponse() {
    return { operatorAddresses: [] };
}
exports.MissingNoncesResponse = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.operatorAddresses), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMissingNoncesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.operatorAddresses.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            operatorAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.operatorAddresses)
                ? object.operatorAddresses.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.operatorAddresses) {
            obj.operatorAddresses = message.operatorAddresses.map(function (e) { return e; });
        }
        else {
            obj.operatorAddresses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MissingNoncesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMissingNoncesResponse();
        message.operatorAddresses = ((_a = object.operatorAddresses) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
var QueryClientImpl = /** @class */ (function () {
    function QueryClientImpl(rpc) {
        this.rpc = rpc;
        this.Params = this.Params.bind(this);
        this.CurrentValset = this.CurrentValset.bind(this);
        this.ValsetRequest = this.ValsetRequest.bind(this);
        this.ValsetConfirm = this.ValsetConfirm.bind(this);
        this.ValsetConfirmsByNonce = this.ValsetConfirmsByNonce.bind(this);
        this.LastValsetRequests = this.LastValsetRequests.bind(this);
        this.LastPendingValsetRequestByAddr = this.LastPendingValsetRequestByAddr.bind(this);
        this.LastEventByAddr = this.LastEventByAddr.bind(this);
        this.GetPendingSendToEth = this.GetPendingSendToEth.bind(this);
        this.BatchFees = this.BatchFees.bind(this);
        this.OutgoingTxBatches = this.OutgoingTxBatches.bind(this);
        this.LastPendingBatchRequestByAddr = this.LastPendingBatchRequestByAddr.bind(this);
        this.BatchRequestByNonce = this.BatchRequestByNonce.bind(this);
        this.BatchConfirms = this.BatchConfirms.bind(this);
        this.ERC20ToDenom = this.ERC20ToDenom.bind(this);
        this.DenomToERC20 = this.DenomToERC20.bind(this);
        this.GetDelegateKeyByValidator = this.GetDelegateKeyByValidator.bind(this);
        this.GetDelegateKeyByEth = this.GetDelegateKeyByEth.bind(this);
        this.GetDelegateKeyByOrchestrator = this.GetDelegateKeyByOrchestrator.bind(this);
        this.PeggyModuleState = this.PeggyModuleState.bind(this);
        this.MissingPeggoNonces = this.MissingPeggoNonces.bind(this);
    }
    QueryClientImpl.prototype.Params = function (request, metadata) {
        return this.rpc.unary(exports.QueryParamsDesc, exports.QueryParamsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.CurrentValset = function (request, metadata) {
        return this.rpc.unary(exports.QueryCurrentValsetDesc, exports.QueryCurrentValsetRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ValsetRequest = function (request, metadata) {
        return this.rpc.unary(exports.QueryValsetRequestDesc, exports.QueryValsetRequestRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ValsetConfirm = function (request, metadata) {
        return this.rpc.unary(exports.QueryValsetConfirmDesc, exports.QueryValsetConfirmRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ValsetConfirmsByNonce = function (request, metadata) {
        return this.rpc.unary(exports.QueryValsetConfirmsByNonceDesc, exports.QueryValsetConfirmsByNonceRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LastValsetRequests = function (request, metadata) {
        return this.rpc.unary(exports.QueryLastValsetRequestsDesc, exports.QueryLastValsetRequestsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LastPendingValsetRequestByAddr = function (request, metadata) {
        return this.rpc.unary(exports.QueryLastPendingValsetRequestByAddrDesc, exports.QueryLastPendingValsetRequestByAddrRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LastEventByAddr = function (request, metadata) {
        return this.rpc.unary(exports.QueryLastEventByAddrDesc, exports.QueryLastEventByAddrRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GetPendingSendToEth = function (request, metadata) {
        return this.rpc.unary(exports.QueryGetPendingSendToEthDesc, exports.QueryPendingSendToEth.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BatchFees = function (request, metadata) {
        return this.rpc.unary(exports.QueryBatchFeesDesc, exports.QueryBatchFeeRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OutgoingTxBatches = function (request, metadata) {
        return this.rpc.unary(exports.QueryOutgoingTxBatchesDesc, exports.QueryOutgoingTxBatchesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.LastPendingBatchRequestByAddr = function (request, metadata) {
        return this.rpc.unary(exports.QueryLastPendingBatchRequestByAddrDesc, exports.QueryLastPendingBatchRequestByAddrRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BatchRequestByNonce = function (request, metadata) {
        return this.rpc.unary(exports.QueryBatchRequestByNonceDesc, exports.QueryBatchRequestByNonceRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BatchConfirms = function (request, metadata) {
        return this.rpc.unary(exports.QueryBatchConfirmsDesc, exports.QueryBatchConfirmsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ERC20ToDenom = function (request, metadata) {
        return this.rpc.unary(exports.QueryERC20ToDenomDesc, exports.QueryERC20ToDenomRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DenomToERC20 = function (request, metadata) {
        return this.rpc.unary(exports.QueryDenomToERC20Desc, exports.QueryDenomToERC20Request.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GetDelegateKeyByValidator = function (request, metadata) {
        return this.rpc.unary(exports.QueryGetDelegateKeyByValidatorDesc, exports.QueryDelegateKeysByValidatorAddress.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GetDelegateKeyByEth = function (request, metadata) {
        return this.rpc.unary(exports.QueryGetDelegateKeyByEthDesc, exports.QueryDelegateKeysByEthAddress.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GetDelegateKeyByOrchestrator = function (request, metadata) {
        return this.rpc.unary(exports.QueryGetDelegateKeyByOrchestratorDesc, exports.QueryDelegateKeysByOrchestratorAddress.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PeggyModuleState = function (request, metadata) {
        return this.rpc.unary(exports.QueryPeggyModuleStateDesc, exports.QueryModuleStateRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MissingPeggoNonces = function (request, metadata) {
        return this.rpc.unary(exports.QueryMissingPeggoNoncesDesc, exports.MissingNoncesRequest.fromPartial(request), metadata);
    };
    return QueryClientImpl;
}());
exports.QueryClientImpl = QueryClientImpl;
exports.QueryDesc = { serviceName: "injective.peggy.v1.Query" };
exports.QueryParamsDesc = {
    methodName: "Params",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryCurrentValsetDesc = {
    methodName: "CurrentValset",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryCurrentValsetRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryCurrentValsetResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryValsetRequestDesc = {
    methodName: "ValsetRequest",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryValsetRequestRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryValsetRequestResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryValsetConfirmDesc = {
    methodName: "ValsetConfirm",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryValsetConfirmRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryValsetConfirmResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryValsetConfirmsByNonceDesc = {
    methodName: "ValsetConfirmsByNonce",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryValsetConfirmsByNonceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryValsetConfirmsByNonceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLastValsetRequestsDesc = {
    methodName: "LastValsetRequests",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLastValsetRequestsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLastValsetRequestsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLastPendingValsetRequestByAddrDesc = {
    methodName: "LastPendingValsetRequestByAddr",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLastPendingValsetRequestByAddrRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLastPendingValsetRequestByAddrResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLastEventByAddrDesc = {
    methodName: "LastEventByAddr",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLastEventByAddrRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLastEventByAddrResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGetPendingSendToEthDesc = {
    methodName: "GetPendingSendToEth",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPendingSendToEth.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPendingSendToEthResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBatchFeesDesc = {
    methodName: "BatchFees",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBatchFeeRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBatchFeeResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOutgoingTxBatchesDesc = {
    methodName: "OutgoingTxBatches",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOutgoingTxBatchesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOutgoingTxBatchesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryLastPendingBatchRequestByAddrDesc = {
    methodName: "LastPendingBatchRequestByAddr",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryLastPendingBatchRequestByAddrRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryLastPendingBatchRequestByAddrResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBatchRequestByNonceDesc = {
    methodName: "BatchRequestByNonce",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBatchRequestByNonceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBatchRequestByNonceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBatchConfirmsDesc = {
    methodName: "BatchConfirms",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBatchConfirmsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBatchConfirmsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryERC20ToDenomDesc = {
    methodName: "ERC20ToDenom",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryERC20ToDenomRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryERC20ToDenomResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDenomToERC20Desc = {
    methodName: "DenomToERC20",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDenomToERC20Request.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDenomToERC20Response.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGetDelegateKeyByValidatorDesc = {
    methodName: "GetDelegateKeyByValidator",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDelegateKeysByValidatorAddress.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDelegateKeysByValidatorAddressResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGetDelegateKeyByEthDesc = {
    methodName: "GetDelegateKeyByEth",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDelegateKeysByEthAddress.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDelegateKeysByEthAddressResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGetDelegateKeyByOrchestratorDesc = {
    methodName: "GetDelegateKeyByOrchestrator",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDelegateKeysByOrchestratorAddress.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDelegateKeysByOrchestratorAddressResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPeggyModuleStateDesc = {
    methodName: "PeggyModuleState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryModuleStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMissingPeggoNoncesDesc = {
    methodName: "MissingPeggoNonces",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MissingNoncesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MissingNoncesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
