{"version": 3, "file": "transaction_errors.d.ts", "sourceRoot": "", "sources": ["../../../src/errors/transaction_errors.ts"], "names": [], "mappings": "AAmBA,OAAO,EACN,KAAK,EACL,SAAS,EACT,OAAO,EACP,kBAAkB,EAClB,yBAAyB,EACzB,MAAM,YAAY,CAAC;AA6CpB,OAAO,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEzE,qBAAa,gBAAgB,CAAC,WAAW,GAAG,kBAAkB,CAAE,SAAQ,aAAa;IAGzC,OAAO,CAAC,EAAE,WAAW;IAFzD,IAAI,SAAU;gBAEF,OAAO,EAAE,MAAM,EAAS,OAAO,CAAC,EAAE,WAAW,YAAA;IAIzD,MAAM;;;;;;;;CAGb;AAED,qBAAa,sBAAuB,SAAQ,aAAa;IAG9B,MAAM,EAAE,MAAM;IAAS,SAAS,EAAE,MAAM;IAF3D,IAAI,SAA6B;gBAEd,MAAM,EAAE,MAAM,EAAS,SAAS,EAAE,MAAM;IAI3D,MAAM;;;;;;;;;CAGb;AAED,qBAAa,iCAAiC,CAC7C,WAAW,GAAG,kBAAkB,CAC/B,SAAQ,aAAa;IAId,MAAM,EAAE,MAAM;IACd,SAAS,CAAC,EAAE,MAAM;IAClB,OAAO,CAAC,EAAE,WAAW;IACrB,IAAI,CAAC,EAAE,MAAM;IANd,IAAI,SAA6B;gBAGhC,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,MAAM,YAAA,EAClB,OAAO,CAAC,EAAE,WAAW,YAAA,EACrB,IAAI,CAAC,EAAE,MAAM,YAAA;IASd,MAAM;;;;;;;;;;;CASb;AAED;;;;GAIG;AACH,qBAAa,gCAAgC,CAC5C,WAAW,GAAG,kBAAkB,CAC/B,SAAQ,iCAAiC,CAAC,WAAW,CAAC;IAI/C,MAAM,EAAE,MAAM;IACd,eAAe,EAAE,MAAM;IACvB,2BAA2B,EAAE,MAAM;IACnC,oBAAoB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAC7C,SAAS,CAAC,EAAE,MAAM;IAClB,OAAO,CAAC,EAAE,WAAW;IACrB,IAAI,CAAC,EAAE,MAAM;IATd,IAAI,SAA0C;gBAG7C,MAAM,EAAE,MAAM,EACd,eAAe,EAAE,MAAM,EACvB,2BAA2B,EAAE,MAAM,EACnC,oBAAoB,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,EAC7C,SAAS,CAAC,EAAE,MAAM,YAAA,EAClB,OAAO,CAAC,EAAE,WAAW,YAAA,EACrB,IAAI,CAAC,EAAE,MAAM,YAAA;IAKd,MAAM;;;;;;;;;;;;;;CAYb;AAED,qBAAa,2BAA4B,SAAQ,gBAAgB;gBAC7C,OAAO,EAAE,kBAAkB;IAKvC,MAAM;;;;;;;;CAGb;AAED,qBAAa,0BAA2B,SAAQ,gBAAgB;gBAC5C,OAAO,EAAE,kBAAkB;CAI9C;AAED,qBAAa,qCAAqC,CACjD,WAAW,GAAG,kBAAkB,CAC/B,SAAQ,gBAAgB,CAAC,WAAW,CAAC;gBACnB,OAAO,CAAC,EAAE,WAAW;CASxC;AAED,qBAAa,wBAAyB,SAAQ,gBAAgB;gBAC1C,OAAO,EAAE,kBAAkB;CAW9C;AAED,qBAAa,4BAA6B,SAAQ,gBAAgB;;CAKjE;AACD,qBAAa,mBAAoB,SAAQ,gBAAgB;;CAKxD;AAED,qBAAa,4BAA6B,SAAQ,iBAAiB;IAC3D,IAAI,SAAyB;gBAEjB,KAAK,EAAE,OAAO;CAGjC;AACD,qBAAa,8BAA+B,SAAQ,iBAAiB;IAC7D,IAAI,SAA2B;gBAEnB,KAAK,EAAE,OAAO;CAGjC;AACD,qBAAa,sBAAuB,SAAQ,iBAAiB;IACrD,IAAI,SAAuB;gBAEf,KAAK,EAAE,OAAO;CAGjC;AAED,qBAAa,uBAAwB,SAAQ,iBAAiB;IACtD,IAAI,SAA+B;;CAQ1C;AAED,qBAAa,yBAA0B,SAAQ,iBAAiB;IACxD,IAAI,SAAkC;;CAQ7C;AAED,qBAAa,oBAAqB,SAAQ,iBAAiB;IACnD,IAAI,SAA4B;gBAEpB,KAAK,EAAE;QAAE,SAAS,EAAE,OAAO,CAAC;QAAC,aAAa,EAAE,OAAO,CAAA;KAAE;CAOxE;AAED,qBAAa,kBAAmB,SAAQ,iBAAiB;IACjD,IAAI,SAAyB;gBAEjB,KAAK,EAAE;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,SAAS,EAAE,OAAO,CAAA;KAAE;CAGlE;AAED,qBAAa,qBAAsB,SAAQ,iBAAiB;IACpD,IAAI,SAA4B;gBAEpB,KAAK,EAAE;QAAE,UAAU,EAAE,OAAO,CAAC;QAAC,cAAc,EAAE,OAAO,CAAA;KAAE;CAG1E;AAED,qBAAa,6BAA8B,SAAQ,iBAAiB;IAC5D,IAAI,SAA6B;;CAQxC;AAED,qBAAa,2BAA4B,SAAQ,iBAAiB;IAC1D,IAAI,SAA6B;gBAErB,KAAK,EAAE;QAAE,KAAK,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE;CAQrF;AAED,qBAAa,oBAAqB,SAAQ,aAAa;IAC/C,IAAI,SAAkC;;CAO7C;AAED,qBAAa,eAAgB,SAAQ,iBAAiB;IAC9C,IAAI,SAAsB;gBAEd,KAAK,EAAE;QACzB,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;QACzB,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAC;QAC9B,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;QAC1C,YAAY,EAAE,OAAO,GAAG,SAAS,CAAC;KAClC;CAWD;AAED,qBAAa,gCAAiC,SAAQ,aAAa;IAC3D,IAAI,SAAmC;;CAO9C;AAED,qBAAa,2BAA4B,SAAQ,iBAAiB;IAC1D,IAAI,SAAuB;gBAEf,KAAK,EAAE;QACzB,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;QACzB,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAC;QAC9B,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;QAC1C,YAAY,EAAE,OAAO,GAAG,SAAS,CAAC;KAClC;CAWD;AAED,qBAAa,oBAAqB,SAAQ,iBAAiB;IACnD,IAAI,SAA6B;gBAErB,KAAK,EAAE;QAAE,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;QAAC,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAA;KAAE;CAMrF;AAED,qBAAa,yCAA0C,SAAQ,iBAAiB;IACxE,IAAI,SAAiC;gBAEzB,KAAK,EAAE;QACzB,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;QAC1C,YAAY,EAAE,OAAO,GAAG,SAAS,CAAC;KAClC;CAQD;AAED,qBAAa,oBAAqB,SAAQ,iBAAiB;IACnD,IAAI,SAAuC;gBAE/B,KAAK,EAAE,OAAO;CAGjC;AAED,qBAAa,yBAA0B,SAAQ,iBAAiB;IACxD,IAAI,SAAoC;gBAE5B,KAAK,EAAE;QACzB,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;QAC1C,YAAY,EAAE,OAAO,GAAG,SAAS,CAAC;KAClC;CAQD;AAED,qBAAa,6BAA8B,SAAQ,iBAAiB;IAC5D,IAAI,SAAyB;gBAEjB,KAAK,EAAE,OAAO;CAGjC;AAED,qBAAa,0BAA2B,SAAQ,iBAAiB;IACzD,IAAI,SAAoC;gBAE5B,KAAK,EAAE;QAAE,KAAK,EAAE,OAAO,GAAG,SAAS,CAAC;QAAC,OAAO,EAAE,OAAO,GAAG,SAAS,CAAA;KAAE;CAMtF;AAED,qBAAa,0BAA2B,SAAQ,iBAAiB;IACzD,IAAI,SAAmC;;CAK9C;AAED,qBAAa,wBAAyB,SAAQ,iBAAiB;IACvD,IAAI,SAA+B;;CAK1C;AAED,qBAAa,+BAAgC,SAAQ,iBAAiB;IAC9D,IAAI,SAA2B;gBAEnB,KAAK,EAAE,OAAO;CAGjC;AAED,qBAAa,4BAA6B,SAAQ,iBAAiB;IAC3D,IAAI,SAAyB;gBAEjB,KAAK,EAAE;QAAE,IAAI,EAAE,SAAS,GAAG,SAAS,CAAC;QAAC,KAAK,EAAE,SAAS,GAAG,SAAS,CAAA;KAAE;CAMvF;AAED,qBAAa,2BAA4B,SAAQ,aAAa;IACtD,IAAI,SAAuB;gBAEf,KAAK,EAAE;QAAE,eAAe,EAAE,MAAM,CAAC;QAAC,eAAe,CAAC,EAAE,KAAK,CAAA;KAAE;CAS9E;AAQD,qBAAa,8BAA+B,SAAQ,aAAa;IACzD,IAAI,SAA0B;gBAElB,KAAK,EAAE;QAAE,eAAe,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,KAAK,CAAA;KAAE;CAO7E;AAED,qBAAa,4BAA6B,SAAQ,aAAa;IACvD,IAAI,SAAwB;gBAEhB,KAAK,EAAE;QACzB,kBAAkB,EAAE,MAAM,CAAC;QAC3B,cAAc,EAAE,MAAM,CAAC;QACvB,eAAe,CAAC,EAAE,KAAK,CAAC;KACxB;CAOD;AAED,qBAAa,yCAA0C,SAAQ,iBAAiB;IACxE,IAAI,SAA4C;gBAEpC,KAAK,EAAE;QACzB,OAAO,EAAE,kBAAkB,CAAC;QAC5B,SAAS,EAAE,KAAK,CAAC;QACjB,eAAe,EAAE,KAAK,CAAC;KACvB;CAQD;AAED,qBAAa,yCAA0C,SAAQ,iBAAiB;IACxE,IAAI,SAAuC;gBAE/B,KAAK,EAAE;QAAE,OAAO,EAAE,kBAAkB,CAAA;KAAE;CAGzD;AAED,qBAAa,uBAAwB,SAAQ,aAAa;IAClD,IAAI,SAAkB;gBACV,YAAY,EAAE,MAAM;CAGvC;AAED,qBAAa,4BAA6B,SAAQ,iBAAiB;IAC3D,IAAI,SAAqC;;CAQhD;AACD,qBAAa,wCAAyC,SAAQ,aAAa;IACnE,IAAI,SAAsC;gBAGhD,eAAe,EAAE,yBAAyB,EAAE,EAC5C,MAAM,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK;CAU9B"}