"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CosmosWalletException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
class CosmosWalletException extends base_js_1.ConcreteException {
    static errorClass = 'CosmosWalletException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        this.setName(CosmosWalletException.errorClass);
    }
}
exports.CosmosWalletException = CosmosWalletException;
