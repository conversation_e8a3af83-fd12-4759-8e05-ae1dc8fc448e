import { InjectiveDmmRpc } from '@injectivelabs/olp-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
export declare class OLPGrpcApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveDmmRpc.InjectiveDmmV2RPCClientImpl;
    constructor(endpoint: string);
    fetchEpochs(status?: string): Promise<import("./index.js").EpochV2[]>;
    fetchMarketRewards(epochId: string): Promise<import("./index.js").MarketReward[]>;
    fetchEligibleAddresses({ epochId, page, }: {
        epochId: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").EligibleAddresses>;
    fetchEpochScores({ epochId, page, }: {
        epochId: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").EpochScores>;
    fetchEpochScoresHistory({ epochId, accountAddress, page, }: {
        epochId: string;
        accountAddress: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").EpochScoresHistory>;
    fetchTotalScores({ epochId, marketId, page, }: {
        epochId: string;
        marketId: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").TotalScores>;
    fetchTotalScoresHistory({ epochId, marketId, accountAddress, page, }: {
        epochId: string;
        marketId: string;
        accountAddress: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").TotalScoresHistory>;
    fetchRewardsDistribution({ epochId, height, page, }: {
        epochId: string;
        height?: string;
        page?: InjectiveDmmRpc.Pagination;
    }): Promise<import("./index.js").RewardsDistribution>;
    fetchAccountVolumes({ epochId, accountAddress, }: {
        epochId: string;
        accountAddress: string;
    }): Promise<import("./index.js").AccountVolume[]>;
    fetchRewardsEligibility({ epochId, accountAddress, }: {
        epochId?: string;
        accountAddress?: string;
    }): Promise<import("./index.js").RewardsEligibility>;
    fetchMarketRewardsRange({ epochId, marketId, }: {
        epochId: string;
        marketId?: string;
    }): Promise<import("./index.js").MinMaxRewards>;
}
