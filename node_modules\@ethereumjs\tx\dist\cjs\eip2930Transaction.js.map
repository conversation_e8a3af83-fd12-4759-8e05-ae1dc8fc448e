{"version": 3, "file": "eip2930Transaction.js", "sourceRoot": "", "sources": ["../../src/eip2930Transaction.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAUyB;AAEzB,6DAAsD;AACtD,qDAAoD;AACpD,qDAAoD;AACpD,mDAAkD;AAClD,yCAA4C;AAC5C,uCAAoD;AAepD;;;;;GAKG;AACH,MAAa,4BAA6B,SAAQ,oCAAkD;IAuFlG;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,0BAAe,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,CAAA;QACnE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAA;QAEhD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;QAEpC,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAEtE,kCAAkC;QAClC,MAAM,cAAc,GAAG,qBAAW,CAAC,iBAAiB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,qBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,IAAI,CAAC,QAAQ,GAAG,IAAA,oBAAa,EAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC,CAAA;QAEhD,IAAI,CAAC,+BAA+B,CAAC;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,oCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,kBAAW,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAA;YAC3E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAE1B,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IA9HD;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QACzE,IACE,IAAA,kBAAW,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAA,qBAAW,EAAC,0BAAe,CAAC,iBAAiB,CAAC,CAAC;YACtF,KAAK,EACL;YACA,MAAM,IAAI,KAAK,CACb,sFACE,0BAAe,CAAC,iBAClB,eAAe,IAAA,iBAAU,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CACvD,CAAA;SACF;QAED,MAAM,MAAM,GAAG,SAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAElE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,4BAA4B,CAAC,eAAe,CAAC,MAAuB,EAAE,IAAI,CAAC,CAAA;IACpF,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAED,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEzF,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;QACtC,IAAA,8BAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,MAAM,eAAe,GAAe,EAAE,CAAA;QAEtC,OAAO,IAAI,4BAA4B,CACrC;YACE,OAAO,EAAE,IAAA,oBAAa,EAAC,OAAO,CAAC;YAC/B,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,IAAI,eAAe;YACzC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oBAAa,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACjD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAmDD,uBAAuB,CAAC,OAAgB;QACtC,OAAO,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;IACnD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,4BAAqB,EAAC,IAAI,CAAC,OAAO,CAAC;YACnC,IAAA,4BAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAA,4BAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAA,4BAAqB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACzD,IAAA,4BAAqB,EAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;YACxE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,4BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SACzE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS;QACP,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gBAAgB;QACd,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACxD,CAAC;IAED;;;;;;OAMG;IACH,sBAAsB;QACpB,OAAO,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED;;OAEG;IACI,2BAA2B;QAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACtC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,YAAY,CACV,CAAS,EACT,CAAsB,EACtB,CAAsB,EACtB,WAAoB,KAAK;QAEzB,CAAC,GAAG,IAAA,cAAO,EAAC,CAAC,CAAC,CAAA;QACd,CAAC,GAAG,IAAA,cAAO,EAAC,CAAC,CAAC,CAAA;QACd,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,4BAA4B,CAAC,UAAU,CAC5C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,gBAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,EAAE,IAAA,oBAAa,EAAC,CAAC,CAAC;YACnB,CAAC,EAAE,IAAA,oBAAa,EAAC,CAAC,CAAC;SACpB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,cAAc,GAAG,qBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,CAAA;QAE/B,OAAO;YACL,GAAG,QAAQ;YACX,OAAO,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,OAAO,CAAC;YAClC,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,UAAU,EAAE,cAAc;SAC3B,CAAA;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,mFAAmF;QACnF,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,oBAAoB,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,EAAE,CAAA;QACxF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACnC,CAAC;CACF;AArTD,oEAqTC"}