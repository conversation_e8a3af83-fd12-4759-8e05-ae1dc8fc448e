/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import { ApplySnapshotChunkRequest, ApplySnapshotChunkResponse, CheckTxRequest, CheckTxResponse, CommitRequest, CommitResponse, EchoRequest, EchoResponse, ExtendVoteRequest, ExtendVoteResponse, FinalizeBlockRequest, FinalizeBlockResponse, FlushRequest, FlushResponse, InfoRequest, InfoResponse, InitChainRequest, InitChainResponse, ListSnapshotsRequest, ListSnapshotsResponse, LoadSnapshotChunkRequest, LoadSnapshotChunkResponse, OfferSnapshotRequest, OfferSnapshotResponse, PrepareProposalRequest, PrepareProposalResponse, ProcessProposalRequest, ProcessProposalResponse, QueryRequest, QueryResponse, VerifyVoteExtensionRequest, VerifyVoteExtensionResponse, } from "./types.js";
export const protobufPackage = "cometbft.abci.v1";
export class ABCIServiceClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.Echo = this.Echo.bind(this);
        this.Flush = this.Flush.bind(this);
        this.Info = this.Info.bind(this);
        this.CheckTx = this.CheckTx.bind(this);
        this.Query = this.Query.bind(this);
        this.Commit = this.Commit.bind(this);
        this.InitChain = this.InitChain.bind(this);
        this.ListSnapshots = this.ListSnapshots.bind(this);
        this.OfferSnapshot = this.OfferSnapshot.bind(this);
        this.LoadSnapshotChunk = this.LoadSnapshotChunk.bind(this);
        this.ApplySnapshotChunk = this.ApplySnapshotChunk.bind(this);
        this.PrepareProposal = this.PrepareProposal.bind(this);
        this.ProcessProposal = this.ProcessProposal.bind(this);
        this.ExtendVote = this.ExtendVote.bind(this);
        this.VerifyVoteExtension = this.VerifyVoteExtension.bind(this);
        this.FinalizeBlock = this.FinalizeBlock.bind(this);
    }
    Echo(request, metadata) {
        return this.rpc.unary(ABCIServiceEchoDesc, EchoRequest.fromPartial(request), metadata);
    }
    Flush(request, metadata) {
        return this.rpc.unary(ABCIServiceFlushDesc, FlushRequest.fromPartial(request), metadata);
    }
    Info(request, metadata) {
        return this.rpc.unary(ABCIServiceInfoDesc, InfoRequest.fromPartial(request), metadata);
    }
    CheckTx(request, metadata) {
        return this.rpc.unary(ABCIServiceCheckTxDesc, CheckTxRequest.fromPartial(request), metadata);
    }
    Query(request, metadata) {
        return this.rpc.unary(ABCIServiceQueryDesc, QueryRequest.fromPartial(request), metadata);
    }
    Commit(request, metadata) {
        return this.rpc.unary(ABCIServiceCommitDesc, CommitRequest.fromPartial(request), metadata);
    }
    InitChain(request, metadata) {
        return this.rpc.unary(ABCIServiceInitChainDesc, InitChainRequest.fromPartial(request), metadata);
    }
    ListSnapshots(request, metadata) {
        return this.rpc.unary(ABCIServiceListSnapshotsDesc, ListSnapshotsRequest.fromPartial(request), metadata);
    }
    OfferSnapshot(request, metadata) {
        return this.rpc.unary(ABCIServiceOfferSnapshotDesc, OfferSnapshotRequest.fromPartial(request), metadata);
    }
    LoadSnapshotChunk(request, metadata) {
        return this.rpc.unary(ABCIServiceLoadSnapshotChunkDesc, LoadSnapshotChunkRequest.fromPartial(request), metadata);
    }
    ApplySnapshotChunk(request, metadata) {
        return this.rpc.unary(ABCIServiceApplySnapshotChunkDesc, ApplySnapshotChunkRequest.fromPartial(request), metadata);
    }
    PrepareProposal(request, metadata) {
        return this.rpc.unary(ABCIServicePrepareProposalDesc, PrepareProposalRequest.fromPartial(request), metadata);
    }
    ProcessProposal(request, metadata) {
        return this.rpc.unary(ABCIServiceProcessProposalDesc, ProcessProposalRequest.fromPartial(request), metadata);
    }
    ExtendVote(request, metadata) {
        return this.rpc.unary(ABCIServiceExtendVoteDesc, ExtendVoteRequest.fromPartial(request), metadata);
    }
    VerifyVoteExtension(request, metadata) {
        return this.rpc.unary(ABCIServiceVerifyVoteExtensionDesc, VerifyVoteExtensionRequest.fromPartial(request), metadata);
    }
    FinalizeBlock(request, metadata) {
        return this.rpc.unary(ABCIServiceFinalizeBlockDesc, FinalizeBlockRequest.fromPartial(request), metadata);
    }
}
export const ABCIServiceDesc = { serviceName: "cometbft.abci.v1.ABCIService" };
export const ABCIServiceEchoDesc = {
    methodName: "Echo",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return EchoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = EchoResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceFlushDesc = {
    methodName: "Flush",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return FlushRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = FlushResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceInfoDesc = {
    methodName: "Info",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return InfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = InfoResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceCheckTxDesc = {
    methodName: "CheckTx",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return CheckTxRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = CheckTxResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceQueryDesc = {
    methodName: "Query",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return QueryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = QueryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceCommitDesc = {
    methodName: "Commit",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return CommitRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = CommitResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceInitChainDesc = {
    methodName: "InitChain",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return InitChainRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = InitChainResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceListSnapshotsDesc = {
    methodName: "ListSnapshots",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ListSnapshotsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ListSnapshotsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceOfferSnapshotDesc = {
    methodName: "OfferSnapshot",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return OfferSnapshotRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = OfferSnapshotResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceLoadSnapshotChunkDesc = {
    methodName: "LoadSnapshotChunk",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return LoadSnapshotChunkRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = LoadSnapshotChunkResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceApplySnapshotChunkDesc = {
    methodName: "ApplySnapshotChunk",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ApplySnapshotChunkRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ApplySnapshotChunkResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServicePrepareProposalDesc = {
    methodName: "PrepareProposal",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return PrepareProposalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = PrepareProposalResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceProcessProposalDesc = {
    methodName: "ProcessProposal",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ProcessProposalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ProcessProposalResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceExtendVoteDesc = {
    methodName: "ExtendVote",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ExtendVoteRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ExtendVoteResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceVerifyVoteExtensionDesc = {
    methodName: "VerifyVoteExtension",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return VerifyVoteExtensionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = VerifyVoteExtensionResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIServiceFinalizeBlockDesc = {
    methodName: "FinalizeBlock",
    service: ABCIServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return FinalizeBlockRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = FinalizeBlockResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
