# Gate.io 交易所工具脚本

此目录包含与Gate.io交易所交互的各种脚本，用于获取代币信息、查询账户余额、下单交易等操作。

## 环境要求

- Python 3.8+
- 必要的Python包：ccxt、pandas、tabulate、pyyaml等

## 目录下的脚本

- `gate_token_info.py`：获取Gate.io交易所上所有USDT交易对的代币信息，包括代币合约地址、支持的网络、充提状态等
- `gate_balance.py`：查询Gate.io账户的余额信息
- `gate_limit_order.py`：在Gate.io交易所创建限价订单
- `gate_order_book.py`：查询Gate.io交易所的订单簿
- `low_volume_usdt_pairs.py`：获取Gate.io交易所上交易量较低的USDT交易对

## 使用方法

### 设置环境变量

在命令行中运行脚本前，确保能正确访问项目根目录的模块：

```bash
# 在Windows上
set PYTHONPATH=C:\path\to\project_root

# 在Linux/Mac上
export PYTHONPATH=/path/to/project_root
```

对于本项目而言，项目根目录应该是`cex_dex_arb_dev`。

或者，你也可以直接进入项目根目录运行脚本:

```bash
# 从项目根目录运行脚本
python scripts/cex/gate/gate_token_info.py
```

### 脚本使用示例

1. 获取代币信息

```bash
# 获取所有USDT交易对的代币信息
python scripts/cex/gate/gate_token_info.py

# 限制获取前5个USDT交易对的信息
python scripts/cex/gate/gate_token_info.py -l 5

# 获取特定代币的信息
python scripts/cex/gate/gate_token_info.py -c BTC

# 开启调试模式并获取充值地址
python scripts/cex/gate/gate_token_info.py -d -a
```

2. 查询账户余额

```bash
# 查询所有币种余额
python scripts/cex/gate/gate_balance.py

# 查询特定币种余额
python scripts/cex/gate/gate_balance.py BTC ETH USDT

# 只显示余额大于0.001的币种
python scripts/cex/gate/gate_balance.py -m 0.001

# 保存余额数据到文件
python scripts/cex/gate/gate_balance.py -s
```

3. 创建限价订单

```bash
# 以市场价买入0.001 BTC
python scripts/cex/gate/gate_limit_order.py BTC/USDT buy 0.001

# 以市场价下浮1%卖出0.001 BTC
python scripts/cex/gate/gate_limit_order.py BTC/USDT sell 0.001 -o -1

# 以指定价格买入0.001 BTC
python scripts/cex/gate/gate_limit_order.py BTC/USDT buy 0.001 -p 38000
```

4. 查询订单簿

```bash
# 查询BTC/USDT的订单簿
python scripts/cex/gate/gate_order_book.py ATOMARC/USDT

# 限制只显示前10条买卖盘
python scripts/cex/gate/gate_order_book.py BTC/USDT -l 10

# 查询并保存订单簿数据
python scripts/cex/gate/gate_order_book.py BTC/USDT -s
```

5. 获取低交易量的USDT交易对

```bash
# 获取24小时交易量低于100,000 USDT的交易对
python scripts/cex/gate/low_volume_usdt_pairs.py -v 25888 -e gate

# 获取交易量低的前20个交易对
python scripts/cex/gate/low_volume_usdt_pairs.py -t 20

# 获取交易量低的交易对
python scripts/cex/gate/low_volume_usdt_pairs.py

#把各个网络代币合约提取
python -m scripts.cex.gate.extract_gate_tokens
```

6.检查支持充提代币在dex是否支持路由

```bash
python scripts/cex/gate/gate_token_routing.py --workers 18 --token-limit 10

```
7.获取cex_dex价差

```bash
python scripts/cex/gate/gate_dex_price_compare.py --threads 18 --token-threads 4  --max-tokens 15 --symbol SEAT --max-tokens 2
```

## 注意事项

1. 使用API相关功能需要在项目配置文件中设置有效的Gate.io API密钥和密钥
2. 交易相关操作前请谨慎确认参数，避免造成资金损失
3. 密切关注Gate.io的API政策变化，及时更新脚本以适应新的API版本
4. 脚本仅供学习和研究使用，使用相关功能需要遵守Gate.io的服务条款
