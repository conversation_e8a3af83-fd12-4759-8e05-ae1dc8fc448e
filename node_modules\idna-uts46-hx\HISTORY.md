2017-11-06 / 2.3.0 - 2.3.1
==========================
* replaced for .. of loop in uts46.js on Line 111 with simple for loop. I don't expect this to break as no codepoint logic has to be payed heed to.

2017-04-18
==========
  * Switched automated tests to fit our own way
    * dropped out grunt
    * introduced nyc
    * introduced mocha.opts
    * npm test now runs both: tests and coverage report
    * add .nyc_output to gitignore
    * renamed test source code files to .spec.js as this is very common
  * re-validated source codes, still open: idna-map.js
  * updated .jshintrc config
  * added IDN translation cases found on unicode.org (see test-uts46.spec.js). Need to investigate further

2017-04-10
==========
Initial Fork Release covering:
  * dependenncy upgrades
    * chai 3.5.0
    * grunt 0.4.5 -> 1.0.1
    * grunt-mocha-istanbul 3.0.1 -> 5.0.2
    * grunt-mocha-test 0.12.7 -> 0.13.2
    * istanbul 0.3.22 -> 0.4.5
    * mocha 2.5.3 -> 3.2.0
    * punycode 1.4.1 -> 2.1.0
  * Added HISTORY.md (Changelog)
  * Upgraded IDNA Map to unicode 9.0.0
  * Updated README.md to cover: build-unicode-tables.py
  * Add test/IdnaTest.txt to gitignore as it get autogenerated
  * Re-validated test source scripts
