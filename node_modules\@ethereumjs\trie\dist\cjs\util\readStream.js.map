{"version": 3, "file": "readStream.js", "sourceRoot": "", "sources": ["../../../src/util/readStream.ts"], "names": [], "mappings": ";;;AAAA,6DAA6D;AAC7D,qDAA0C;AAE1C,+CAAuD;AAEvD,6CAA6C;AAK7C,MAAM,eAAe,GAAG,KAAK,EAAE,IAAU,EAAE,OAA0B,EAAiB,EAAE;IACtF,MAAM,YAAY,GAAsB,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,cAAc,EAAE,EAAE;QACnF,IAAI,OAAO,GAAG,GAAG,CAAA;QACjB,IAAI,IAAI,YAAY,mBAAQ,EAAE;YAC5B,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;YAChC,mBAAmB;YACnB,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;SAChD;aAAM,IAAI,IAAI,YAAY,qBAAU,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;YACrD,0BAA0B;YAC1B,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;SAChD;aAAM;YACL,+BAA+B;YAC/B,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;aACtC;SACF;IACH,CAAC,CAAA;IACD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,CAAA;AAChD,CAAC,CAAA;AAED,MAAa,cAAe,SAAQ,0BAAQ;IAI1C,YAAY,IAAU;QACpB,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAA;QAE3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;IACvB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAM;SACP;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI;YACF,MAAM,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,cAAc,EAAE,EAAE;gBACtE,IAAI,IAAI,KAAK,IAAI,EAAE;oBACjB,IAAI,CAAC,IAAI,CAAC;wBACR,GAAG,EAAE,IAAA,2BAAc,EAAC,GAAG,CAAC;wBACxB,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;qBACpB,CAAC,CAAA;oBACF,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;iBACtC;YACH,CAAC,CAAC,CAAA;SACH;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,KAAK,CAAC,OAAO,KAAK,oBAAoB,EAAE;gBAC1C,OAAO;aACR;iBAAM;gBACL,MAAM,KAAK,CAAA;aACZ;SACF;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjB,CAAC;CACF;AAnCD,wCAmCC"}