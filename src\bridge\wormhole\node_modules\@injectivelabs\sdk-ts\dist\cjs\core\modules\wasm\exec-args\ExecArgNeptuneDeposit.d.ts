import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
export declare namespace ExecArgNeptuneDeposit {
    interface Params {
    }
    interface Data {
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecArgNeptuneDeposit extends ExecArgBase<ExecArgNeptuneDeposit.Params, ExecArgNeptuneDeposit.Data> {
    static fromJSON(params: ExecArgNeptuneDeposit.Params): ExecArgNeptuneDeposit;
    toData(): ExecArgNeptuneDeposit.Data;
    toExecData(): ExecDataRepresentation<ExecArgNeptuneDeposit.Data>;
}
