/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin.js";
import { InsuranceFund, RedemptionSchedule } from "./insurance.js";
export const protobufPackage = "injective.insurance.v1beta1";
function createBaseEventInsuranceFundUpdate() {
    return { fund: undefined };
}
export const EventInsuranceFundUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.fund !== undefined) {
            InsuranceFund.encode(message.fund, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventInsuranceFundUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fund = InsuranceFund.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { fund: isSet(object.fund) ? InsuranceFund.fromJSON(object.fund) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.fund !== undefined && (obj.fund = message.fund ? InsuranceFund.toJSON(message.fund) : undefined);
        return obj;
    },
    create(base) {
        return EventInsuranceFundUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventInsuranceFundUpdate();
        message.fund = (object.fund !== undefined && object.fund !== null)
            ? InsuranceFund.fromPartial(object.fund)
            : undefined;
        return message;
    },
};
function createBaseEventRequestRedemption() {
    return { schedule: undefined };
}
export const EventRequestRedemption = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.schedule !== undefined) {
            RedemptionSchedule.encode(message.schedule, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventRequestRedemption();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.schedule = RedemptionSchedule.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { schedule: isSet(object.schedule) ? RedemptionSchedule.fromJSON(object.schedule) : undefined };
    },
    toJSON(message) {
        const obj = {};
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? RedemptionSchedule.toJSON(message.schedule) : undefined);
        return obj;
    },
    create(base) {
        return EventRequestRedemption.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventRequestRedemption();
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? RedemptionSchedule.fromPartial(object.schedule)
            : undefined;
        return message;
    },
};
function createBaseEventWithdrawRedemption() {
    return { schedule: undefined, redeemCoin: undefined };
}
export const EventWithdrawRedemption = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.schedule !== undefined) {
            RedemptionSchedule.encode(message.schedule, writer.uint32(10).fork()).ldelim();
        }
        if (message.redeemCoin !== undefined) {
            Coin.encode(message.redeemCoin, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventWithdrawRedemption();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.schedule = RedemptionSchedule.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.redeemCoin = Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            schedule: isSet(object.schedule) ? RedemptionSchedule.fromJSON(object.schedule) : undefined,
            redeemCoin: isSet(object.redeemCoin) ? Coin.fromJSON(object.redeemCoin) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? RedemptionSchedule.toJSON(message.schedule) : undefined);
        message.redeemCoin !== undefined &&
            (obj.redeemCoin = message.redeemCoin ? Coin.toJSON(message.redeemCoin) : undefined);
        return obj;
    },
    create(base) {
        return EventWithdrawRedemption.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventWithdrawRedemption();
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? RedemptionSchedule.fromPartial(object.schedule)
            : undefined;
        message.redeemCoin = (object.redeemCoin !== undefined && object.redeemCoin !== null)
            ? Coin.fromPartial(object.redeemCoin)
            : undefined;
        return message;
    },
};
function createBaseEventUnderwrite() {
    return { underwriter: "", marketId: "", deposit: undefined, shares: undefined };
}
export const EventUnderwrite = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.underwriter !== "") {
            writer.uint32(10).string(message.underwriter);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.deposit !== undefined) {
            Coin.encode(message.deposit, writer.uint32(26).fork()).ldelim();
        }
        if (message.shares !== undefined) {
            Coin.encode(message.shares, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventUnderwrite();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.underwriter = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.deposit = Coin.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.shares = Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            underwriter: isSet(object.underwriter) ? String(object.underwriter) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            deposit: isSet(object.deposit) ? Coin.fromJSON(object.deposit) : undefined,
            shares: isSet(object.shares) ? Coin.fromJSON(object.shares) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.underwriter !== undefined && (obj.underwriter = message.underwriter);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.deposit !== undefined && (obj.deposit = message.deposit ? Coin.toJSON(message.deposit) : undefined);
        message.shares !== undefined && (obj.shares = message.shares ? Coin.toJSON(message.shares) : undefined);
        return obj;
    },
    create(base) {
        return EventUnderwrite.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventUnderwrite();
        message.underwriter = object.underwriter ?? "";
        message.marketId = object.marketId ?? "";
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? Coin.fromPartial(object.deposit)
            : undefined;
        message.shares = (object.shares !== undefined && object.shares !== null)
            ? Coin.fromPartial(object.shares)
            : undefined;
        return message;
    },
};
function createBaseEventInsuranceWithdraw() {
    return { marketId: "", marketTicker: "", withdrawal: undefined };
}
export const EventInsuranceWithdraw = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.marketTicker !== "") {
            writer.uint32(18).string(message.marketTicker);
        }
        if (message.withdrawal !== undefined) {
            Coin.encode(message.withdrawal, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventInsuranceWithdraw();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.marketTicker = reader.string();
                    break;
                case 3:
                    message.withdrawal = Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            marketTicker: isSet(object.marketTicker) ? String(object.marketTicker) : "",
            withdrawal: isSet(object.withdrawal) ? Coin.fromJSON(object.withdrawal) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.marketTicker !== undefined && (obj.marketTicker = message.marketTicker);
        message.withdrawal !== undefined &&
            (obj.withdrawal = message.withdrawal ? Coin.toJSON(message.withdrawal) : undefined);
        return obj;
    },
    create(base) {
        return EventInsuranceWithdraw.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventInsuranceWithdraw();
        message.marketId = object.marketId ?? "";
        message.marketTicker = object.marketTicker ?? "";
        message.withdrawal = (object.withdrawal !== undefined && object.withdrawal !== null)
            ? Coin.fromPartial(object.withdrawal)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
