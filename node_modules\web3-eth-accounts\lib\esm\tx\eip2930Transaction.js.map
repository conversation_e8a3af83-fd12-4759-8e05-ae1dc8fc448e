{"version": 3, "file": "eip2930Transaction.js", "sourceRoot": "", "sources": ["../../../src/tx/eip2930Transaction.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AAC5D,OAAO,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EACN,iBAAiB,EACjB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,GACjB,MAAM,YAAY,CAAC;AACpB,OAAO,EACN,WAAW,EACX,YAAY,EACZ,SAAS,EACT,kBAAkB,EAClB,0BAA0B,GAC1B,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAWvD,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,2BAA2B,GAAG,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAE/F;;;;;GAKG;AACH,gDAAgD;AAChD,MAAM,OAAO,4BAA6B,SAAQ,eAA6C;IAgB9F;;;;;;;;;OASG;IACI,MAAM,CAAC,UAAU,CAAC,MAA+B,EAAE,OAAkB,EAAE;QAC7E,OAAO,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QAC1E,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE,CAAC;YAC/E,MAAM,IAAI,KAAK,CACd,sFAAsF,gBAAgB,eAAe,UAAU,CAC9H,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CACzB,EAAE,CACH,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAC/D,CAAC;QACD,iEAAiE;QACjE,OAAO,4BAA4B,CAAC,eAAe,CAClD,MAAsC,EACtC,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,eAAe,CAAC,MAAoC,EAAE,OAAkB,EAAE;QACvF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CACd,uGAAuG,CACvG,CAAC;QACH,CAAC;QAED,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;QAE1F,IAAI,CAAC,iBAAiB,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;QACvC,uBAAuB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEvE,MAAM,eAAe,GAAe,EAAE,CAAC;QAEvC,OAAO,IAAI,4BAA4B,CACtC;YACC,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC;YACpC,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,UAAU,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,eAAe;YACzC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,uDAAuD;YAC/G,CAAC;YACD,CAAC;SACD,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,YAAmB,MAA+B,EAAE,OAAkB,EAAE;;QACvE,KAAK,iCAAM,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC,CAAC;QA9FpD;;;;;WAKG;QACO,qBAAgB,GAAG,QAAQ,CAAC;QAyFrC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;QAEjD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAErC,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAEvE,kCAAkC;QAClC,MAAM,cAAc,GAAG,iBAAiB,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAC;QACpD,iCAAiC;QACjC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAElC,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC,YAAY,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEpF,IAAI,CAAC,+BAA+B,CAAC;YACpC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACvB,CAAC,CAAC;QAEH,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE,CAAC;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAC;QACpC,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;IACF,CAAC;IAED;;OAEG;IACI,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YAClF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAEhE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACpB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aAChC,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;OAEG;IACI,cAAc;QACpB,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,GAAG;QACT,OAAO;YACN,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC;YACxC,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;SAC/E,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,SAAS;QACf,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACxB,OAAO,gBAAgB,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,MAAM,OAAO,GAAG,gBAAgB,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAChF,IAAI,WAAW,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,2BAA2B;QACjC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC;YACJ,OAAO,SAAS,CACf,OAAO,EACP,CAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACjE,0BAA0B,CAAC,CAAE,CAAC,EAC9B,0BAA0B,CAAC,CAAE,CAAC,CAC9B,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACF,CAAC;IAEM,iBAAiB,CAAC,CAAS,EAAE,CAAa,EAAE,CAAa;QAC/D,MAAM,IAAI,mCAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAC;QAExD,OAAO,4BAA4B,CAAC,UAAU,CAC7C;YACC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,sGAAsG;YACzH,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;SACxB,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM;QACZ,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE1D,OAAO;YACN,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,QAAQ;;QACd,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7C,mFAAmF;QACnF,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,oBAAoB,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,mCAAI,CAAC,EAAE,CAAC;QACzF,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC9B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;IACtC,CAAC;CACD"}