import { InjectiveAuctionRpc } from '@injectivelabs/indexer-proto-ts';
import { Auction, IndexerBid, GrpcAuction, TotalInjBurnt, GrpcIndexerBid } from '../types/auction.js';
/**
 * @category Indexer Grpc Transformer
 */
export declare class IndexerGrpcAuctionTransformer {
    static auctionResponseToAuction(response: InjectiveAuctionRpc.AuctionEndpointResponse): {
        auction: Auction;
        bids: IndexerBid[];
    };
    static auctionsResponseToAuctions(response: InjectiveAuctionRpc.AuctionsResponse): Auction[];
    static grpcBidToBid(grpcBid: GrpcIndexerBid): IndexerBid;
    static grpcAuctionToAuction(grpcAuction: GrpcAuction): Auction;
    static injBurntResponseToInjBurnt(response: InjectiveAuctionRpc.InjBurntEndpointResponse): TotalInjBurnt;
}
