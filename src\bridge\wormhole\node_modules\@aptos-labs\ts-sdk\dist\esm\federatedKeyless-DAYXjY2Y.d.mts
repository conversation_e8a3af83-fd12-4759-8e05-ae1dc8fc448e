import { b as AccountPub<PERSON><PERSON><PERSON>, A as Authenti<PERSON><PERSON><PERSON>, P as <PERSON><PERSON><PERSON> } from './publicKey-CJOcUwJK.mjs';
import { Deserializer } from './bcs/deserializer.mjs';
import { Serializable, Serializer } from './bcs/serializer.mjs';
import { HexInput, EphemeralCertificateVariant, ZkpVariant, LedgerVersionArg } from './types/types.mjs';
import { AccountAddressInput, AccountAddress } from './core/accountAddress.mjs';
import { Signature } from './core/crypto/signature.mjs';
import { EphemeralPublicKey, EphemeralSignature } from './core/crypto/ephemeral.mjs';
import { Proof } from './core/crypto/proof.mjs';
import { MoveAnyStruct, Groth16VerificationKeyResponse, KeylessConfigurationResponse } from './types/keyless.mjs';
import { AptosConfig } from './api/aptosConfig.mjs';
import { ProjPointType } from '@noble/curves/abstract/weierstrass';
import { Fp2 } from '@noble/curves/abstract/tower';

/**
 * @group Implementation
 * @category Serialization
 */
declare const EPK_HORIZON_SECS = ********;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_AUD_VAL_BYTES = 120;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_UID_KEY_BYTES = 30;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_UID_VAL_BYTES = 330;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_ISS_VAL_BYTES = 120;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_EXTRA_FIELD_BYTES = 350;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_JWT_HEADER_B64_BYTES = 300;
/**
 * @group Implementation
 * @category Serialization
 */
declare const MAX_COMMITED_EPK_BYTES = 93;
/**
 * Represents a Keyless Public Key used for authentication.
 *
 * This class encapsulates the public key functionality for keyless authentication,
 * including methods for generating and verifying signatures, as well as serialization
 * and deserialization of the key. The KeylessPublicKey is represented in the SDK
 * as `AnyPublicKey`.
 * @group Implementation
 * @category Serialization
 */
declare class KeylessPublicKey extends AccountPublicKey {
    /**
     * The number of bytes that `idCommitment` should be
     * @group Implementation
     * @category Serialization
     */
    static readonly ID_COMMITMENT_LENGTH: number;
    /**
     * The value of the 'iss' claim on the JWT which identifies the OIDC provider.
     * @group Implementation
     * @category Serialization
     */
    readonly iss: string;
    /**
     * A value representing a cryptographic commitment to a user identity.
     *
     * It is calculated from the aud, uidKey, uidVal, pepper.
     * @group Implementation
     * @category Serialization
     */
    readonly idCommitment: Uint8Array;
    /**
     * Constructs an instance with the specified parameters for cryptographic operations.
     *
     * @param args - The parameters required to initialize the instance.
     * @param args.alphaG1 - The hex representation of the alpha G1 value.
     * @param args.betaG2 - The hex representation of the beta G2 value.
     * @param args.deltaG2 - The hex representation of the delta G2 value.
     * @param args.gammaAbcG1 - An array containing two hex representations for gamma ABC G1 values.
     * @param args.gammaG2 - The hex representation of the gamma G2 value.
     * @group Implementation
     * @category Serialization
     */
    constructor(iss: string, idCommitment: HexInput);
    /**
     * Get the authentication key for the keyless public key.
     *
     * @returns AuthenticationKey - The authentication key derived from the keyless public key.
     * @group Implementation
     * @category Serialization
     */
    authKey(): AuthenticationKey;
    /**
     * Verifies the validity of a signature for a given message.
     *
     * @param args - The arguments for signature verification.
     * @param args.message - The message that was signed.
     * @param args.signature - The signature to verify against the message.
     * @param args.jwk - The JWK to use for verification.
     * @param args.keylessConfig - The keyless configuration to use for verification.
     * @returns true if the signature is valid; otherwise, false.
     * @group Implementation
     * @category Serialization
     */
    verifySignature(args: {
        message: HexInput;
        signature: Signature;
        jwk: MoveJWK;
        keylessConfig: KeylessConfiguration;
    }): boolean;
    /**
     * Verifies a keyless signature for a given message.  It will fetch the keyless configuration and the JWK to
     * use for verification from the appropriate network as defined by the aptosConfig.
     *
     * @param args.aptosConfig The aptos config to use for fetching the keyless configuration.
     * @param args.message The message to verify the signature against.
     * @param args.signature The signature to verify.
     * @param args.options.throwErrorWithReason Whether to throw an error with the reason for the failure instead of returning false.
     * @returns true if the signature is valid
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Serializes the current instance into a format suitable for transmission or storage.
     * This function ensures that all relevant fields are properly serialized, including the proof and optional fields.
     *
     * @param serializer - The serializer instance used to perform the serialization.
     * @param serializer.proof - The proof to be serialized.
     * @param serializer.expHorizonSecs - The expiration horizon in seconds.
     * @param serializer.extraField - An optional additional field for serialization.
     * @param serializer.overrideAudVal - An optional override value for auditing.
     * @param serializer.trainingWheelsSignature - An optional signature for training wheels.
     * @group Implementation
     * @category Serialization
     */
    serialize(serializer: Serializer): void;
    /**
     * Deserializes a ZeroKnowledgeSig object from the provided deserializer.
     * This function allows you to reconstruct a ZeroKnowledgeSig instance from its serialized form.
     *
     * @param deserializer - The deserializer instance used to read the serialized data.
     * @returns A new instance of ZeroKnowledgeSig.
     * @group Implementation
     * @category Serialization
     */
    static deserialize(deserializer: Deserializer): KeylessPublicKey;
    /**
     * Loads a KeylessPublicKey instance from the provided deserializer.
     * This function is used to deserialize the necessary components to create a KeylessPublicKey.
     *
     * @param deserializer - The deserializer used to extract the string and byte data.
     * @param deserializer.deserializeStr - A method to deserialize a string value.
     * @param deserializer.deserializeBytes - A method to deserialize byte data.
     * @returns A new instance of KeylessPublicKey.
     * @group Implementation
     * @category Serialization
     */
    static load(deserializer: Deserializer): KeylessPublicKey;
    /**
     * Determines if the provided public key is an instance of KeylessPublicKey.
     *
     * @param publicKey - The public key to check.
     * @returns A boolean indicating whether the public key is a KeylessPublicKey instance.
     * @group Implementation
     * @category Serialization
     */
    static isPublicKey(publicKey: PublicKey): publicKey is KeylessPublicKey;
    /**
     * Creates a KeylessPublicKey from the JWT components plus pepper
     *
     * @param args.iss the iss of the identity
     * @param args.uidKey the key to use to get the uidVal in the JWT token
     * @param args.uidVal the value of the uidKey in the JWT token
     * @param args.aud the client ID of the application
     * @param args.pepper The pepper used to maintain privacy of the account
     * @returns KeylessPublicKey
     * @group Implementation
     * @category Serialization
     */
    static create(args: {
        iss: string;
        uidKey: string;
        uidVal: string;
        aud: string;
        pepper: HexInput;
    }): KeylessPublicKey;
    /**
     * Creates a KeylessPublicKey instance from a JWT and a pepper value.
     * This function is useful for generating a public key that can be used for authentication based on the provided JWT claims and pepper.
     *
     * @param args - The arguments for creating the KeylessPublicKey.
     * @param args.jwt - The JSON Web Token to decode.
     * @param args.pepper - The pepper value used in the key creation process.
     * @param args.uidKey - An optional key to retrieve the unique identifier from the JWT payload, defaults to "sub".
     * @returns A KeylessPublicKey instance created from the provided JWT and pepper.
     * @group Implementation
     * @category Serialization
     */
    static fromJwtAndPepper(args: {
        jwt: string;
        pepper: HexInput;
        uidKey?: string;
    }): KeylessPublicKey;
    /**
     * Checks if the provided public key is a valid instance by verifying its structure and types.
     *
     * @param publicKey - The public key to validate.
     * @returns A boolean indicating whether the public key is a valid instance.
     * @group Implementation
     * @category Serialization
     */
    static isInstance(publicKey: PublicKey): boolean;
}
declare function verifyKeylessSignature(args: {
    publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
    aptosConfig: AptosConfig;
    message: HexInput;
    signature: Signature;
    keylessConfig?: KeylessConfiguration;
    jwk?: MoveJWK;
    options?: {
        throwErrorWithReason?: boolean;
    };
}): Promise<boolean>;
/**
 * Syncronously verifies a keyless signature for a given message.  You need to provide the keyless configuration and the
 * JWK to use for verification.
 *
 * @param args.message The message to verify the signature against.
 * @param args.signature The signature to verify.
 * @param args.keylessConfig The keyless configuration.
 * @param args.jwk The JWK to use for verification.
 * @returns true if the signature is valid
 * @throws KeylessError if the signature is invalid
 */
declare function verifyKeylessSignatureWithJwkAndConfig(args: {
    publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
    message: HexInput;
    signature: Signature;
    keylessConfig: KeylessConfiguration;
    jwk: MoveJWK;
}): void;
/**
 * Fetches the JWK from the issuer's well-known JWKS endpoint.
 *
 * @param args.publicKey The keyless public key which contains the issuer the address to fetch the JWK from (0x1 if not federated).
 * @param args.kid The kid of the JWK to fetch
 * @returns A JWK matching the `kid` in the JWT header.
 * @throws {KeylessError} If the JWK cannot be fetched
 */
declare function fetchJWK(args: {
    aptosConfig: AptosConfig;
    publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
    kid: string;
}): Promise<MoveJWK>;
/**
 * Represents a signature of a message signed via a Keyless Account, utilizing proofs or a JWT token for authentication.
 * @group Implementation
 * @category Serialization
 */
declare class KeylessSignature extends Signature {
    /**
     * The inner signature ZeroKnowledgeSignature or OpenIdSignature
     * @group Implementation
     * @category Serialization
     */
    readonly ephemeralCertificate: EphemeralCertificate;
    /**
     * The jwt header in the token used to create the proof/signature.  In json string representation.
     * @group Implementation
     * @category Serialization
     */
    readonly jwtHeader: string;
    /**
     * The expiry timestamp in seconds of the EphemeralKeyPair used to sign
     * @group Implementation
     * @category Serialization
     */
    readonly expiryDateSecs: number;
    /**
     * The ephemeral public key used to verify the signature
     * @group Implementation
     * @category Serialization
     */
    readonly ephemeralPublicKey: EphemeralPublicKey;
    /**
     * The signature resulting from signing with the private key of the EphemeralKeyPair
     * @group Implementation
     * @category Serialization
     */
    readonly ephemeralSignature: EphemeralSignature;
    constructor(args: {
        jwtHeader: string;
        ephemeralCertificate: EphemeralCertificate;
        expiryDateSecs: number;
        ephemeralPublicKey: EphemeralPublicKey;
        ephemeralSignature: EphemeralSignature;
    });
    /**
     * Get the kid of the JWT used to derive the Keyless Account used to sign.
     *
     * @returns the kid as a string
     */
    getJwkKid(): string;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): KeylessSignature;
    static getSimulationSignature(): KeylessSignature;
    static isSignature(signature: Signature): signature is KeylessSignature;
}
/**
 * Represents an ephemeral certificate containing a signature, specifically a ZeroKnowledgeSig.
 * This class can be extended to support additional signature types, such as OpenIdSignature.
 *
 * @extends Signature
 * @group Implementation
 * @category Serialization
 */
declare class EphemeralCertificate extends Signature {
    readonly signature: Signature;
    /**
     * Index of the underlying enum variant
     * @group Implementation
     * @category Serialization
     */
    readonly variant: EphemeralCertificateVariant;
    constructor(signature: Signature, variant: EphemeralCertificateVariant);
    /**
     * Get the public key in bytes (Uint8Array).
     *
     * @returns Uint8Array representation of the public key
     * @group Implementation
     * @category Serialization
     */
    toUint8Array(): Uint8Array;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): EphemeralCertificate;
}
/**
 * Represents a fixed-size byte array of 32 bytes, extending the Serializable class.
 * This class is used for handling and serializing G1 bytes in cryptographic operations.
 *
 * @extends Serializable
 * @group Implementation
 * @category Serialization
 */
declare class G1Bytes extends Serializable {
    private static readonly B;
    data: Uint8Array;
    constructor(data: HexInput);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): G1Bytes;
    toArray(): string[];
    /**
     * Converts the G1 bytes to a projective point.
     * @returns The projective point.
     */
    toProjectivePoint(): ProjPointType<bigint>;
}
/**
 * Represents a 64-byte G2 element in a cryptographic context.
 * This class provides methods for serialization and deserialization of G2 bytes.
 *
 * @extends Serializable
 * @group Implementation
 * @category Serialization
 */
declare class G2Bytes extends Serializable {
    /**
     * The constant b value used in G2 point calculations
     */
    private static readonly B;
    data: Uint8Array;
    constructor(data: HexInput);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): G2Bytes;
    toArray(): [string, string][];
    toProjectivePoint(): ProjPointType<Fp2>;
}
/**
 * Represents a Groth16 zero-knowledge proof, consisting of three proof points in compressed serialization format.
 * The points are the compressed serialization of affine representation of the proof.
 *
 * @extends Proof
 * @group Implementation
 * @category Serialization
 */
declare class Groth16Zkp extends Proof {
    /**
     * The bytes of G1 proof point a
     * @group Implementation
     * @category Serialization
     */
    a: G1Bytes;
    /**
     * The bytes of G2 proof point b
     * @group Implementation
     * @category Serialization
     */
    b: G2Bytes;
    /**
     * The bytes of G1 proof point c
     * @group Implementation
     * @category Serialization
     */
    c: G1Bytes;
    constructor(args: {
        a: HexInput;
        b: HexInput;
        c: HexInput;
    });
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Groth16Zkp;
    toSnarkJsJson(): {
        protocol: string;
        curve: string;
        pi_a: string[];
        pi_b: [string, string][];
        pi_c: string[];
    };
}
/**
 * Represents a Groth16 proof and statement, consisting of a Groth16 proof and a public inputs hash.
 * This is used to generate the signing message for the training wheels signature.
 *
 * @extends Serializable
 * @group Implementation
 * @category Serialization
 */
declare class Groth16ProofAndStatement extends Serializable {
    /**
     * The Groth16 proof
     * @group Implementation
     * @category Serialization
     */
    proof: Groth16Zkp;
    /**
     * The public inputs hash as a 32 byte Uint8Array
     * @group Implementation
     * @category Serialization
     */
    publicInputsHash: Uint8Array;
    /**
     * The domain separator prefix used when hashing.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly domainSeparator = "APTOS::Groth16ProofAndStatement";
    constructor(proof: Groth16Zkp, publicInputsHash: HexInput | bigint);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Groth16ProofAndStatement;
    hash(): Uint8Array;
}
/**
 * Represents a container for different types of zero-knowledge proofs.
 *
 * @extends Serializable
 * @group Implementation
 * @category Serialization
 */
declare class ZkProof extends Serializable {
    readonly proof: Proof;
    /**
     * Index of the underlying enum variant
     * @group Implementation
     * @category Serialization
     */
    readonly variant: ZkpVariant;
    constructor(proof: Proof, variant: ZkpVariant);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): ZkProof;
}
/**
 * Represents a zero-knowledge signature, encapsulating the proof and its associated metadata.
 *
 * @extends Signature
 * @group Implementation
 * @category Serialization
 */
declare class ZeroKnowledgeSig extends Signature {
    /**
     * The proof
     * @group Implementation
     * @category Serialization
     */
    readonly proof: ZkProof;
    /**
     * The max lifespan of the proof
     * @group Implementation
     * @category Serialization
     */
    readonly expHorizonSecs: number;
    /**
     * A key value pair on the JWT token that can be specified on the signature which would reveal the value on chain.
     * Can be used to assert identity or other attributes.
     * @group Implementation
     * @category Serialization
     */
    readonly extraField?: string;
    /**
     * The 'aud' value of the recovery service which is set when recovering an account.
     * @group Implementation
     * @category Serialization
     */
    readonly overrideAudVal?: string;
    /**
     * The training wheels signature
     * @group Implementation
     * @category Serialization
     */
    readonly trainingWheelsSignature?: EphemeralSignature;
    constructor(args: {
        proof: ZkProof;
        expHorizonSecs: number;
        extraField?: string;
        overrideAudVal?: string;
        trainingWheelsSignature?: EphemeralSignature;
    });
    /**
     * Deserialize a ZeroKnowledgeSig object from its BCS serialization in bytes.
     *
     * @param bytes - The bytes representing the serialized ZeroKnowledgeSig.
     * @returns ZeroKnowledgeSig - The deserialized ZeroKnowledgeSig object.
     * @group Implementation
     * @category Serialization
     */
    static fromBytes(bytes: Uint8Array): ZeroKnowledgeSig;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): ZeroKnowledgeSig;
}
/**
 * Represents the on-chain configuration for how Keyless accounts operate.
 *
 * @remarks
 * This class encapsulates the verification key and the maximum lifespan of ephemeral key pairs,
 * which are essential for the functionality of Keyless accounts.
 * @group Implementation
 * @category Serialization
 */
declare class KeylessConfiguration {
    /**
     * The verification key used to verify Groth16 proofs on chain
     * @group Implementation
     * @category Serialization
     */
    readonly verificationKey: Groth16VerificationKey;
    /**
     * The maximum lifespan of an ephemeral key pair.  This is configured on chain.
     * @group Implementation
     * @category Serialization
     */
    readonly maxExpHorizonSecs: number;
    /**
     * The public key of the training wheels account.
     * @group Implementation
     * @category Serialization
     */
    readonly trainingWheelsPubkey?: EphemeralPublicKey;
    /**
     * The maximum number of bytes that can be used for the extra field.
     * @group Implementation
     * @category Serialization
     */
    readonly maxExtraFieldBytes: number;
    /**
     * The maximum number of bytes that can be used for the JWT header.
     * @group Implementation
     * @category Serialization
     */
    readonly maxJwtHeaderB64Bytes: number;
    /**
     * The maximum number of bytes that can be used for the issuer value.
     * @group Implementation
     * @category Serialization
     */
    readonly maxIssValBytes: number;
    /**
     * The maximum number of bytes that can be used for the committed ephemeral public key.
     * @group Implementation
     * @category Serialization
     */
    readonly maxCommitedEpkBytes: number;
    constructor(args: {
        verificationKey: Groth16VerificationKey;
        trainingWheelsPubkey?: HexInput;
        maxExpHorizonSecs?: number;
        maxExtraFieldBytes?: number;
        maxJwtHeaderB64Bytes?: number;
        maxIssValBytes?: number;
        maxCommitedEpkBytes?: number;
    });
    /**
     * Creates a new KeylessConfiguration instance from a Groth16VerificationKeyResponse and a KeylessConfigurationResponse.
     * @param res - The Groth16VerificationKeyResponse object containing the verification key data.
     * @param config - The KeylessConfigurationResponse object containing the configuration data.
     * @returns A new KeylessConfiguration instance.
     */
    static create(res: Groth16VerificationKeyResponse, config: KeylessConfigurationResponse): KeylessConfiguration;
}
/**
 * Represents the verification key stored on-chain used to verify Groth16 proofs.
 * @group Implementation
 * @category Serialization
 */
declare class Groth16VerificationKey {
    /**
     * The `alpha * G`, where `G` is the generator of G1
     * @group Implementation
     * @category Serialization
     */
    readonly alphaG1: G1Bytes;
    /**
     * The `alpha * H`, where `H` is the generator of G2
     * @group Implementation
     * @category Serialization
     */
    readonly betaG2: G2Bytes;
    /**
     * The `delta * H`, where `H` is the generator of G2
     * @group Implementation
     * @category Serialization
     */
    readonly deltaG2: G2Bytes;
    /**
     * The `gamma^{-1} * (beta * a_i + alpha * b_i + c_i) * H`, where H is the generator of G1
     * @group Implementation
     * @category Serialization
     */
    readonly gammaAbcG1: [G1Bytes, G1Bytes];
    /**
     * The `gamma * H`, where `H` is the generator of G2
     * @group Implementation
     * @category Serialization
     */
    readonly gammaG2: G2Bytes;
    constructor(args: {
        alphaG1: HexInput;
        betaG2: HexInput;
        deltaG2: HexInput;
        gammaAbcG1: [HexInput, HexInput];
        gammaG2: HexInput;
    });
    /**
     * Calculates the hash of the serialized form of the verification key.
     * This is useful for comparing verification keys or using them as unique identifiers.
     *
     * @returns The SHA3-256 hash of the serialized verification key as a Uint8Array
     */
    hash(): Uint8Array;
    serialize(serializer: Serializer): void;
    /**
     * Converts a Groth16VerificationKeyResponse object into a Groth16VerificationKey instance.
     *
     * @param res - The Groth16VerificationKeyResponse object containing the verification key data.
     * @param res.alpha_g1 - The alpha G1 value from the response.
     * @param res.beta_g2 - The beta G2 value from the response.
     * @param res.delta_g2 - The delta G2 value from the response.
     * @param res.gamma_abc_g1 - The gamma ABC G1 value from the response.
     * @param res.gamma_g2 - The gamma G2 value from the response.
     * @returns A Groth16VerificationKey instance constructed from the provided response data.
     * @group Implementation
     * @category Serialization
     */
    static fromGroth16VerificationKeyResponse(res: Groth16VerificationKeyResponse): Groth16VerificationKey;
    /**
     * Verifies a Groth16 proof using the verification key given the public inputs hash and the proof.
     *
     * @param args.publicInputsHash The public inputs hash
     * @param args.groth16Proof The Groth16 proof
     * @returns true if the proof is valid
     */
    verifyProof(args: {
        publicInputsHash: bigint;
        groth16Proof: Groth16Zkp;
    }): boolean;
    /**
     * Converts the verification key to a JSON format compatible with snarkjs groth16.verify
     *
     * @returns An object containing the verification key in snarkjs format
     * @group Implementation
     * @category Serialization
     */
    toSnarkJsJson(): {
        protocol: string;
        curve: string;
        nPublic: number;
        vk_alpha_1: string[];
        vk_beta_2: [string, string][];
        vk_gamma_2: [string, string][];
        vk_delta_2: [string, string][];
        IC: string[][];
    };
}
/**
 * Retrieves the configuration parameters for Keyless Accounts on the blockchain, including the verifying key and the maximum
 * expiry horizon.
 *
 * @param args - The arguments for retrieving the keyless configuration.
 * @param args.aptosConfig - The Aptos configuration object containing network details.
 * @param args.options - Optional parameters for the request.
 * @param args.options.ledgerVersion - The ledger version to query; if not provided, the latest version will be used.
 * @returns KeylessConfiguration - The configuration object containing the verifying key and maximum expiry horizon.
 * @group Implementation
 * @category Serialization
 */
declare function getKeylessConfig(args: {
    aptosConfig: AptosConfig;
    options?: LedgerVersionArg;
}): Promise<KeylessConfiguration>;
/**
 * Parses a JWT and returns the 'iss', 'aud', and 'uid' values.
 *
 * @param args - The arguments for parsing the JWT.
 * @param args.jwt - The JWT to parse.
 * @param args.uidKey - The key to use for the 'uid' value; defaults to 'sub'.
 * @returns The 'iss', 'aud', and 'uid' values from the JWT.
 */
declare function getIssAudAndUidVal(args: {
    jwt: string;
    uidKey?: string;
}): {
    iss: string;
    aud: string;
    uidVal: string;
};
declare function getKeylessJWKs(args: {
    aptosConfig: AptosConfig;
    jwkAddr?: AccountAddressInput;
    options?: LedgerVersionArg;
}): Promise<Map<string, MoveJWK[]>>;
declare class MoveJWK extends Serializable {
    kid: string;
    kty: string;
    alg: string;
    e: string;
    n: string;
    constructor(args: {
        kid: string;
        kty: string;
        alg: string;
        e: string;
        n: string;
    });
    serialize(serializer: Serializer): void;
    static fromMoveStruct(struct: MoveAnyStruct): MoveJWK;
    toScalar(): bigint;
    static deserialize(deserializer: Deserializer): MoveJWK;
}
interface JwtHeader {
    kid: string;
}
/**
 * Safely parses the JWT header.
 * @param jwtHeader The JWT header string
 * @returns Parsed JWT header as an object.
 */
declare function parseJwtHeader(jwtHeader: string): JwtHeader;

/**
 * Represents the FederatedKeylessPublicKey public key
 *
 * These keys use an on-chain address as a source of truth for the JWK used to verify signatures.
 *
 * FederatedKeylessPublicKey authentication key is represented in the SDK as `AnyPublicKey`.
 * @group Implementation
 * @category Serialization
 */
declare class FederatedKeylessPublicKey extends AccountPublicKey {
    /**
     * The address that contains the JWK set to be used for verification.
     * @group Implementation
     * @category Serialization
     */
    readonly jwkAddress: AccountAddress;
    /**
     * The inner public key which contains the standard Keyless public key.
     * @group Implementation
     * @category Serialization
     */
    readonly keylessPublicKey: KeylessPublicKey;
    constructor(jwkAddress: AccountAddressInput, keylessPublicKey: KeylessPublicKey);
    /**
     * Get the authentication key for the federated keyless public key
     *
     * @returns AuthenticationKey
     * @group Implementation
     * @category Serialization
     */
    authKey(): AuthenticationKey;
    /**
     * Verifies a signed data with a public key
     *
     * @param args.message message
     * @param args.signature The signature
     * @param args.jwk - The JWK to use for verification.
     * @param args.keylessConfig - The keyless configuration to use for verification.
     * @returns true if the signature is valid
     * @group Implementation
     * @category Serialization
     */
    verifySignature(args: {
        message: HexInput;
        signature: Signature;
        jwk: MoveJWK;
        keylessConfig: KeylessConfiguration;
    }): boolean;
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): FederatedKeylessPublicKey;
    static isPublicKey(publicKey: PublicKey): publicKey is FederatedKeylessPublicKey;
    /**
     * Verifies a keyless signature for a given message.  It will fetch the keyless configuration and the JWK to
     * use for verification from the appropriate network as defined by the aptosConfig.
     *
     * @param args.aptosConfig The aptos config to use for fetching the keyless configuration.
     * @param args.message The message to verify the signature against.
     * @param args.signature The signature to verify.
     * @param args.options.throwErrorWithReason Whether to throw an error with the reason for the failure instead of returning false.
     * @returns true if the signature is valid
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: KeylessSignature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Creates a FederatedKeylessPublicKey from the JWT components plus pepper
     *
     * @param args.iss the iss of the identity
     * @param args.uidKey the key to use to get the uidVal in the JWT token
     * @param args.uidVal the value of the uidKey in the JWT token
     * @param args.aud the client ID of the application
     * @param args.pepper The pepper used to maintain privacy of the account
     * @returns FederatedKeylessPublicKey
     * @group Implementation
     * @category Serialization
     */
    static create(args: {
        iss: string;
        uidKey: string;
        uidVal: string;
        aud: string;
        pepper: HexInput;
        jwkAddress: AccountAddressInput;
    }): FederatedKeylessPublicKey;
    static fromJwtAndPepper(args: {
        jwt: string;
        pepper: HexInput;
        jwkAddress: AccountAddressInput;
        uidKey?: string;
    }): FederatedKeylessPublicKey;
    static isInstance(publicKey: PublicKey): boolean;
}

export { EPK_HORIZON_SECS as E, FederatedKeylessPublicKey as F, Groth16Zkp as G, KeylessPublicKey as K, MAX_AUD_VAL_BYTES as M, ZkProof as Z, MAX_UID_KEY_BYTES as a, MAX_UID_VAL_BYTES as b, MAX_ISS_VAL_BYTES as c, MAX_EXTRA_FIELD_BYTES as d, MAX_JWT_HEADER_B64_BYTES as e, MAX_COMMITED_EPK_BYTES as f, verifyKeylessSignatureWithJwkAndConfig as g, fetchJWK as h, KeylessSignature as i, EphemeralCertificate as j, Groth16ProofAndStatement as k, ZeroKnowledgeSig as l, KeylessConfiguration as m, Groth16VerificationKey as n, getKeylessConfig as o, getIssAudAndUidVal as p, getKeylessJWKs as q, MoveJWK as r, parseJwtHeader as s, verifyKeylessSignature as v };
