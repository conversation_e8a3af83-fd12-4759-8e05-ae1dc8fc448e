import{poseidon1 as h,poseidon2 as u,poseidon3 as a,poseidon4 as l,poseidon5 as c,poseidon6 as d,poseidon7 as p,poseidon8 as f,poseidon9 as A,poseidon10 as U,poseidon11 as b,poseidon12 as w,poseidon13 as E,poseidon14 as I,poseidon15 as y,poseidon16 as B}from"poseidon-lite";var i=[h,u,a,l,c,d,p,f,A,U,b,w,E,I,y,B],g=31,m=16,s=(m-1)*g;function C(n,t){let o=new TextEncoder().encode(n);return $(o,t)}function $(n,t){if(n.length>t)throw new Error(`Inputted bytes of length ${n} is longer than ${t}`);let r=_(n,t);return M(r)}function T(n,t){if(n.length>t)throw new Error(`Input bytes of length ${n} is longer than ${t}`);let r=k(n,t);return P(r)}function _(n,t){if(n.length>t)throw new Error(`Input bytes of length ${n} is longer than ${t}`);return T(n,t).concat([BigInt(n.length)])}function P(n){if(n.length>s)throw new Error(`Can't pack more than ${s}.  Was given ${n.length} bytes`);return L(n,g).map(t=>N(t))}function L(n,t){let r=[];for(let o=0;o<n.length;o+=t)r.push(n.subarray(o,o+t));return r}function N(n){let t=BigInt(0);for(let r=n.length-1;r>=0;r-=1)t=t<<BigInt(8)|BigInt(n[r]);return t}function W(n,t){let r=BigInt(n),o=new Uint8Array(t);for(let e=0;e<t;e+=1)o[e]=Number(r&BigInt(255)),r>>=BigInt(8);return o}function k(n,t){if(t<n.length)throw new Error("Padded size must be greater than or equal to the input array size.");let r=new Uint8Array(t);r.set(n);for(let o=n.length;o<t;o+=1)r[o]=0;return r}function M(n){if(n.length>i.length)throw new Error(`Unable to hash input of length ${n.length}.  Max input length is ${i.length}`);return i[n.length-1](n)}export{C as a,_ as b,N as c,W as d,M as e};
//# sourceMappingURL=chunk-GOXRBEIJ.mjs.map