"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorReceipt = exports.UpgradeFields = exports.Upgrade = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var channel_1 = require("./channel.js");
exports.protobufPackage = "ibc.core.channel.v1";
function createBaseUpgrade() {
    return { fields: undefined, timeout: undefined, nextSequenceSend: "0" };
}
exports.Upgrade = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.fields !== undefined) {
            exports.UpgradeFields.encode(message.fields, writer.uint32(10).fork()).ldelim();
        }
        if (message.timeout !== undefined) {
            channel_1.Timeout.encode(message.timeout, writer.uint32(18).fork()).ldelim();
        }
        if (message.nextSequenceSend !== "0") {
            writer.uint32(24).uint64(message.nextSequenceSend);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseUpgrade();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fields = exports.UpgradeFields.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.timeout = channel_1.Timeout.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.nextSequenceSend = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            fields: isSet(object.fields) ? exports.UpgradeFields.fromJSON(object.fields) : undefined,
            timeout: isSet(object.timeout) ? channel_1.Timeout.fromJSON(object.timeout) : undefined,
            nextSequenceSend: isSet(object.nextSequenceSend) ? String(object.nextSequenceSend) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.fields !== undefined && (obj.fields = message.fields ? exports.UpgradeFields.toJSON(message.fields) : undefined);
        message.timeout !== undefined && (obj.timeout = message.timeout ? channel_1.Timeout.toJSON(message.timeout) : undefined);
        message.nextSequenceSend !== undefined && (obj.nextSequenceSend = message.nextSequenceSend);
        return obj;
    },
    create: function (base) {
        return exports.Upgrade.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseUpgrade();
        message.fields = (object.fields !== undefined && object.fields !== null)
            ? exports.UpgradeFields.fromPartial(object.fields)
            : undefined;
        message.timeout = (object.timeout !== undefined && object.timeout !== null)
            ? channel_1.Timeout.fromPartial(object.timeout)
            : undefined;
        message.nextSequenceSend = (_a = object.nextSequenceSend) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseUpgradeFields() {
    return { ordering: 0, connectionHops: [], version: "" };
}
exports.UpgradeFields = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ordering !== 0) {
            writer.uint32(8).int32(message.ordering);
        }
        try {
            for (var _b = __values(message.connectionHops), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.version !== "") {
            writer.uint32(26).string(message.version);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseUpgradeFields();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ordering = reader.int32();
                    break;
                case 2:
                    message.connectionHops.push(reader.string());
                    break;
                case 3:
                    message.version = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ordering: isSet(object.ordering) ? (0, channel_1.orderFromJSON)(object.ordering) : 0,
            connectionHops: Array.isArray(object === null || object === void 0 ? void 0 : object.connectionHops) ? object.connectionHops.map(function (e) { return String(e); }) : [],
            version: isSet(object.version) ? String(object.version) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ordering !== undefined && (obj.ordering = (0, channel_1.orderToJSON)(message.ordering));
        if (message.connectionHops) {
            obj.connectionHops = message.connectionHops.map(function (e) { return e; });
        }
        else {
            obj.connectionHops = [];
        }
        message.version !== undefined && (obj.version = message.version);
        return obj;
    },
    create: function (base) {
        return exports.UpgradeFields.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseUpgradeFields();
        message.ordering = (_a = object.ordering) !== null && _a !== void 0 ? _a : 0;
        message.connectionHops = ((_b = object.connectionHops) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.version = (_c = object.version) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseErrorReceipt() {
    return { sequence: "0", message: "" };
}
exports.ErrorReceipt = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sequence !== "0") {
            writer.uint32(8).uint64(message.sequence);
        }
        if (message.message !== "") {
            writer.uint32(18).string(message.message);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseErrorReceipt();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sequence = longToString(reader.uint64());
                    break;
                case 2:
                    message.message = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sequence: isSet(object.sequence) ? String(object.sequence) : "0",
            message: isSet(object.message) ? String(object.message) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sequence !== undefined && (obj.sequence = message.sequence);
        message.message !== undefined && (obj.message = message.message);
        return obj;
    },
    create: function (base) {
        return exports.ErrorReceipt.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseErrorReceipt();
        message.sequence = (_a = object.sequence) !== null && _a !== void 0 ? _a : "0";
        message.message = (_b = object.message) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
