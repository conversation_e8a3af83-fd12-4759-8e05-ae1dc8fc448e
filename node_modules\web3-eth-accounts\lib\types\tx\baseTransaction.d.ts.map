{"version": 3, "file": "baseTransaction.d.ts", "sourceRoot": "", "sources": ["../../../src/tx/baseTransaction.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAwB,OAAO,EAAE,MAAM,YAAY,CAAC;AAI3D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,KAAK,EACX,uBAAuB,EACvB,4BAA4B,EAC5B,sBAAsB,EACtB,2BAA2B,EAC3B,MAAM,EACN,MAAM,EACN,SAAS,EACT,aAAa,EACb,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAGvC,UAAU,gBAAgB;IACzB,IAAI,EAAE,UAAU,GAAG,SAAS,CAAC;IAC7B,OAAO,CAAC,EAAE;QACT,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,GAAG,QAAQ,CAAC;KAC5B,CAAC;CACF;AAED;;;;;;GAMG;AACH,8BAAsB,eAAe,CAAC,iBAAiB;IACtD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAS;IAE/B,SAAgB,KAAK,EAAE,MAAM,CAAC;IAC9B,SAAgB,QAAQ,EAAE,MAAM,CAAC;IACjC,SAAgB,EAAE,CAAC,EAAE,OAAO,CAAC;IAC7B,SAAgB,KAAK,EAAE,MAAM,CAAC;IAC9B,SAAgB,IAAI,EAAE,UAAU,CAAC;IAEjC,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAC;IAC3B,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAC;IAC3B,SAAgB,CAAC,CAAC,EAAE,MAAM,CAAC;IAE3B,SAAgB,MAAM,EAAG,MAAM,CAAC;IAEhC,SAAS,CAAC,KAAK,EAAE,gBAAgB,CAG/B;IAEF,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;IAExC;;;;OAIG;IACH,SAAS,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAM;IAE5C;;;;;;;OAOG;IACH,SAAS,CAAC,aAAa,QAAiB;IAExC;;;;;OAKG;IACH,SAAS,CAAC,gBAAgB,EAAE,MAAM,GAAG,QAAQ,CAAkB;gBAG9D,MAAM,EAAE,MAAM,GAAG,uBAAuB,GAAG,sBAAsB,EACjE,IAAI,EAAE,SAAS;IAsChB;;;;OAIG;IACH,IAAW,IAAI,WAEd;IAED;;;;;;;;;;;;;;;OAeG;IACI,QAAQ,CAAC,UAAU,EAAE,UAAU;IAItC;;;OAGG;IACI,QAAQ,IAAI,OAAO;IACnB,QAAQ,CAAC,WAAW,EAAE,KAAK,GAAG,OAAO;IACrC,QAAQ,CAAC,WAAW,EAAE,IAAI,GAAG,MAAM,EAAE;IAiB5C,SAAS,CAAC,gBAAgB;IAQ1B;;;OAGG;IACH,SAAS,CAAC,cAAc;IAUxB;;OAEG;IACI,UAAU,IAAI,MAAM;IAW3B;;OAEG;IACI,UAAU,IAAI,MAAM;IAoB3B;;OAEG;aACa,cAAc,IAAI,MAAM;IAExC;;OAEG;IACI,iBAAiB,IAAI,OAAO;IAInC;;;;;;;;;OASG;aACa,GAAG,IAChB,aAAa,GACb,4BAA4B,GAC5B,2BAA2B;IAE9B;;OAEG;aACa,SAAS,IAAI,UAAU;aAMvB,gBAAgB,CAAC,WAAW,EAAE,KAAK,GAAG,UAAU,GAAG,UAAU,EAAE;aAC/D,gBAAgB,CAAC,WAAW,CAAC,EAAE,IAAI,GAAG,UAAU;aAEhD,IAAI,IAAI,UAAU;aAElB,2BAA2B,IAAI,UAAU;IAElD,QAAQ,IAAI,OAAO;IAQ1B;;OAEG;IACI,eAAe,IAAI,OAAO;IAUjC;;OAEG;IACI,gBAAgB,IAAI,OAAO;IAIlC;;OAEG;aACa,kBAAkB,IAAI,UAAU;IAEhD;;;;;;;;OAQG;IACI,IAAI,CAAC,UAAU,EAAE,UAAU,GAAG,iBAAiB;IAmCtD;;OAEG;aACa,MAAM,IAAI,MAAM;IAGhC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CACnC,CAAC,EAAE,MAAM,EACT,CAAC,EAAE,UAAU,EACb,CAAC,EAAE,UAAU,GACX,iBAAiB;IAEpB;;;;;;;OAOG;IACH,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO;IAuEvD;;;;;OAKG;IACH,SAAS,CAAC,+BAA+B,CACxC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,EAC7C,IAAI,SAAM,EACV,WAAW,UAAQ;IA2CpB,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE;IAyBjE;;OAEG;aACa,QAAQ,IAAI,MAAM;IAElC;;;;;OAKG;IACH,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM;IAEjD;;;OAGG;IACH,SAAS,CAAC,sBAAsB;IA0BhC,SAAS,CAAC,OAAO,CAChB,OAAO,EAAE,UAAU,EACnB,UAAU,EAAE,UAAU,EACtB,OAAO,CAAC,EAAE,MAAM,GACd,cAAc;WAgBH,gBAAgB,CAE7B,UAAU,EAAE,UAAU,EAEtB,IAAI,GAAE,SAAc,GAElB,GAAG;WAGQ,UAAU,CAGvB,MAAM,EAAE,GAAG,EAEX,IAAI,GAAE,SAAc,GAElB,GAAG;CACN"}