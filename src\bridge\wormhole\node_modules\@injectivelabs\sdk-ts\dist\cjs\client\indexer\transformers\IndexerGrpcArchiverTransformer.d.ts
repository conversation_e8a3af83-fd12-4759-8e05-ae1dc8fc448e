import { DenomHolders, HistoricalRPNL, LeaderboardRow, PnlLeaderboard, VolLeaderboard, HistoricalBalance, HistoricalVolumes } from '../types/archiver.js';
import { InjectiveArchiverRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Grpc Transformer
 */
export declare class IndexerGrpcArchiverTransformer {
    static grpcHistoricalBalanceToHistoricalBalance(historicalBalance: InjectiveArchiverRpc.HistoricalBalance): HistoricalBalance;
    static grpcHistoricalRPNLToHistoricalRPNL(historicalRPNL: InjectiveArchiverRpc.HistoricalRPNL): HistoricalRPNL;
    static grpcHistoricalVolumesToHistoricalVolumes(historicalVolumes: InjectiveArchiverRpc.HistoricalVolumes): HistoricalVolumes;
    static grpcLeaderboardRowToLeaderboardRow(leaderboardRow: InjectiveArchiverRpc.LeaderboardRow): LeaderboardRow;
    static grpcHistoricalBalanceResponseToHistoricalBalances(response: InjectiveArchiverRpc.BalanceResponse): HistoricalBalance;
    static grpcHistoricalRPNLResponseToHistoricalRPNL(response: InjectiveArchiverRpc.RpnlResponse): HistoricalRPNL;
    static grpcHistoricalVolumesResponseToHistoricalVolumes(response: InjectiveArchiverRpc.VolumesResponse): HistoricalVolumes;
    static grpcPnlLeaderboardResponseToPnlLeaderboard(response: InjectiveArchiverRpc.PnlLeaderboardResponse): PnlLeaderboard;
    static grpcVolLeaderboardResponseToVolLeaderboard(response: InjectiveArchiverRpc.VolLeaderboardResponse): VolLeaderboard;
    static grpcPnlLeaderboardFixedResolutionResponseToPnlLeaderboard(response: InjectiveArchiverRpc.PnlLeaderboardFixedResolutionResponse): PnlLeaderboard;
    static grpcVolLeaderboardFixedResolutionResponseToVolLeaderboard(response: InjectiveArchiverRpc.VolLeaderboardFixedResolutionResponse): VolLeaderboard;
    static grpcDenomHoldersResponseToDenomHolders(response: InjectiveArchiverRpc.DenomHoldersResponse): DenomHolders;
}
