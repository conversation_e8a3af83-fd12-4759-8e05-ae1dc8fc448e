import { BigNumber, getSignificantDecimalsFromNumber, getExactDecimalsFromNumber } from '@injectivelabs/utils';
export declare const isNumber: (number: string | number) => boolean;
export declare const formatNumberToAllowableDecimals: (value: string | number, allowableDecimals: number, roundingMode?: BigNumber.RoundingMode) => string;
export declare const formatNumberToAllowableTensMultiplier: (value: string | number, tensMultiplier: number, roundingMode?: BigNumber.RoundingMode) => string;
export declare const formatAmountToAllowableAmount: (value: string | number, tensMultiplier: number) => string;
export declare const formatPriceToAllowablePrice: (value: string | number, tensMultiplier: number) => string;
/**
 *
 * Legacy function - use formatNumberToAllowableDecimals
 *
 * @param value
 * @param allowableDecimals
 * @returns
 */
export declare const formatAmountToAllowableDecimals: (value: string | number, allowableDecimals: number) => string;
/**
 *
 * Legacy function - use formatNumberToAllowableDecimals
 *
 * @param value
 * @param allowableDecimals
 * @returns
 */
export declare const formatPriceToAllowableDecimals: (value: string | number, allowableDecimals: number) => string;
/**
 * On chain amounts queried from a sentry using the
 * gRPC API are returned with an extra decimal point
 * 18 places from the beginning, so we need to remove it
 * to get a workable amount
 */
export declare const denomAmountFromGrpcChainDenomAmount: (value: string | number | BigNumber) => BigNumber;
/**
 * On chain amounts broadcasted to a sentry directly using the
 * gRPC API should be passed with an extra decimal point
 * 18 places from the beginning, so we need to add it
 * to get a workable amount
 */
export declare const denomAmountToGrpcChainDenomAmount: (value: string | number | BigNumber) => BigNumber;
/**
 * On chain amounts (based on the cosmosSdk.Dec type)
 * broadcasted to a sentry directly using the
 * gRPC API should be passed with an extra decimal point
 * 18 places from the beginning (i.e multiplied by 1e18), so we need to add it
 * to get a workable amount
 */
export declare const amountToCosmosSdkDecAmount: (value: string | number | BigNumber) => BigNumber;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const denomAmountToChainDenomAmount: ({ value, decimals, }: {
    value: number | string | BigNumber;
    decimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const denomAmountToChainDenomAmountToFixed: ({ value, decimals, tensMultiplier, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    decimals?: number | string;
    tensMultiplier?: number;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const denomAmountFromChainDenomAmount: ({ value, decimals, }: {
    value: number | string | BigNumber;
    decimals?: number | string;
}) => BigNumber;
/**
 *
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
export declare const denomAmountFromChainDenomAmountToFixed: ({ value, decimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    decimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const derivativeMarginToChainMargin: ({ value, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
export declare const derivativeMarginToChainMarginToFixed: ({ value, quoteDecimals, tensMultiplier, decimalPlaces, roundingMode, }: {
    decimalPlaces?: number;
    tensMultiplier?: number;
    roundingMode?: BigNumber.RoundingMode;
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
}) => string;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const derivativeMarginFromChainMargin: ({ value, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const derivativeMarginFromChainMarginToFixed: ({ value, quoteDecimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const derivativePriceToChainPrice: ({ value, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
export declare const derivativePriceToChainPriceToFixed: ({ value, tensMultiplier, quoteDecimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    tensMultiplier?: number;
    quoteDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number
 */
export declare const derivativePriceFromChainPrice: ({ value, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain returns is in the x * 10^(quoteDecimals) format
 * where x is a human readable number stringified
 */
export declare const derivativePriceFromChainPriceToFixed: ({ value, quoteDecimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number
 */
export declare const derivativeQuantityToChainQuantity: ({ value, }: {
    value: number | string | BigNumber;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number stringified
 */
export declare const derivativeQuantityToChainQuantityToFixed: ({ value, decimalPlaces, tensMultiplier, roundingMode, }: {
    value: number | string | BigNumber;
    decimalPlaces?: number;
    tensMultiplier?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number
 */
export declare const derivativeQuantityFromChainQuantity: ({ value, }: {
    value: number | string | BigNumber;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x format
 * where x is a human readable number stringified
 */
export declare const derivativeQuantityFromChainQuantityToFixed: ({ value, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number
 */
export declare const spotPriceToChainPrice: ({ value, baseDecimals, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    baseDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number stringified
 */
export declare const spotPriceToChainPriceToFixed: ({ value, baseDecimals, quoteDecimals, tensMultiplier, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    baseDecimals?: number | string;
    decimalPlaces?: number;
    tensMultiplier?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain returns is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number
 */
export declare const spotPriceFromChainPrice: ({ value, baseDecimals, quoteDecimals, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    baseDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain returns is in the x / 10^(quoteDecimals - baseDecimals) format
 * where x is a human readable number stringified
 */
export declare const spotPriceFromChainPriceToFixed: ({ value, baseDecimals, quoteDecimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    quoteDecimals?: number | string;
    baseDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain requires is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
export declare const spotQuantityToChainQuantity: ({ value, baseDecimals, }: {
    value: number | string | BigNumber;
    baseDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain requires is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
export declare const spotQuantityToChainQuantityToFixed: ({ value, baseDecimals, tensMultiplier, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    tensMultiplier?: number;
    baseDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
/**
 * Amount that the chain returns is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
export declare const spotQuantityFromChainQuantity: ({ value, baseDecimals, }: {
    value: number | string | BigNumber;
    baseDecimals?: number | string;
}) => BigNumber;
/**
 * Amount that the chain returns is in the x * 10^(baseDecimals) format
 * where x is a human readable number
 */
export declare const spotQuantityFromChainQuantityToFixed: ({ value, baseDecimals, decimalPlaces, roundingMode, }: {
    value: number | string | BigNumber;
    baseDecimals?: number | string;
    decimalPlaces?: number;
    roundingMode?: BigNumber.RoundingMode;
}) => string;
export declare const cosmosSdkDecToBigNumber: (number: string | number | BigNumber) => BigNumber;
export declare const numberToCosmosSdkDecString: (value: string | number | BigNumber) => string;
/**
 * This function returns a multiplier of 10
 * based on the input. There are two cases:
 *
 * 1. If the number is less than 1, it returns a NEGATIVE
 * number which is the number of decimals the number has
 *
 * 2. If the number is higher than 1, it returns a POSITIVE
 * number which is the number of 10 multiplier the number has
 *
 * @param number
 * @returns {number}
 */
export declare const getTensMultiplier: (number: number | string) => number;
export declare const getTriggerPrice: (triggerPrice?: number | string) => string;
export { getSignificantDecimalsFromNumber, getExactDecimalsFromNumber };
