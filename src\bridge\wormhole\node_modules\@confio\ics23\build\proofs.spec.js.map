{"version": 3, "file": "proofs.spec.js", "sourceRoot": "", "sources": ["../src/proofs.spec.ts"], "names": [], "mappings": ";;AAAA,qDAA8C;AAC9C,qCAAwE;AACxE,yDAAsD;AAEtD,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACtC,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;SACtB,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,+BAAsB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACxD,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAChC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,MAAM,CAAC;YACpB,KAAK,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;YAClC,IAAI,EAAE;gBACJ,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;gBACzB,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;aACjC;SACF,CAAC;QACF,MAAM,QAAQ,GAAG,IAAA,0BAAO,EACtB,kEAAkE,CACnE,CAAC;QACF,MAAM,CAAC,IAAA,+BAAsB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACpC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,MAAM,CAAC;YACpB,KAAK,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;YAClC,IAAI,EAAE;gBACJ;oBACE,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;oBACzB,MAAM,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;iBACpC;aACF;SACF,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,+BAAsB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACxD,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,MAAM,CAAC;YACpB,KAAK,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;YAClC,IAAI,EAAE;gBACJ,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;gBACzB,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;aACjC;YACD,2EAA2E;YAC3E,IAAI,EAAE;gBACJ;oBACE,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;oBACzB,MAAM,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;iBACpC;gBACD,mHAAmH;aACpH;SACF,CAAC;QACF,MAAM,QAAQ,GAAG,IAAA,0BAAO,EACtB,kEAAkE,CACnE,CAAC;QACF,MAAM,CAAC,IAAA,+BAAsB,EAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;IAC1B,MAAM,SAAS,GAAG,iBAAQ,CAAC,QAAQ,CAAC;IACpC,MAAM,WAAW,GAAG;QAClB,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,YAAY,EAAE,iBAAK,CAAC,MAAM,CAAC,OAAO;QAClC,UAAU,EAAE,iBAAK,CAAC,MAAM,CAAC,OAAO;QAChC,MAAM,EAAE,iBAAK,CAAC,QAAQ,CAAC,SAAS;KACjC,CAAC;IAEF,MAAM,UAAU,GAAG;QACjB,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,MAAM,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;QACnC,+BAA+B;KAChC,CAAC;IACF,MAAM,YAAY,GAAG;QACnB,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,MAAM,EAAE,IAAA,0BAAO,EAAC,IAAI,CAAC;KACtB,CAAC;IACF,MAAM,gBAAgB,GAAG;QACvB,IAAI,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM;QACzB,MAAM,EAAE,IAAA,0BAAO,EAAC,kBAAkB,CAAC;KACpC,CAAC;IAEF,MAAM,gBAAgB,GAAG;QACvB,QAAQ,EAAE,iBAAQ,CAAC,QAAQ;QAC3B,SAAS,EAAE,iBAAQ,CAAC,SAAS;QAC7B,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,CAAC;KACZ,CAAC;IAEF,EAAE,CAAC,qBAAqB,EAAE,GAAG,EAAE;QAC7B,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;SACtB,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAChC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;SAChB,CAAC;QACF,qCAAqC;QACrC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;QAC9B,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,WAAW;SAClB,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACpC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,CAAC,UAAU,CAAC;SACnB,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,UAAU,CAAC;SACnB,CAAC;QACF,qCAAqC;QACrC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC7C,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;SACjC,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC;SACrC,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,iBAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;QAC3D,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;SAC3C,CAAC;QACF,qCAAqC;QACrC,IAAA,mBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;QAClD,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,UAAU,CAAC;SACnB,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACnD,MAAM,KAAK,GAA0B;YACnC,GAAG,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACnB,KAAK,EAAE,IAAA,0BAAO,EAAC,KAAK,CAAC;YACrB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;SACnE,CAAC;QACF,MAAM,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAC9D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}