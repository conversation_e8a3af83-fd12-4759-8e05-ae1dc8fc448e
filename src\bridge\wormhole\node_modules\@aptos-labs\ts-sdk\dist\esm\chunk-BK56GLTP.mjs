import{a as fi}from"./chunk-V74WPKSY.mjs";import{a as _n,b as gr}from"./chunk-A5L76YP7.mjs";import{a as Kn}from"./chunk-XKUIMGKU.mjs";import{a as F}from"./chunk-N6YTF76Q.mjs";import{a as je,b as it,c as Wt}from"./chunk-CO67Y6YE.mjs";import{a as In,b as Oi}from"./chunk-57J5YBMT.mjs";import{a as xe,b as Pn,c as Ct,d as ir,e as It}from"./chunk-GOXRBEIJ.mjs";import{a as Yr}from"./chunk-XJJVJOX5.mjs";import{a as rr,b as Zr}from"./chunk-RQX6JOEN.mjs";import{a as Et,b as We}from"./chunk-CFQFFP6N.mjs";import{b as k,c as $,d as z}from"./chunk-UQWF24SS.mjs";import{b as $e}from"./chunk-WSR5EBJM.mjs";import{a as Pe}from"./chunk-WCMW2L3P.mjs";import{a as pr,b as lr}from"./chunk-W4BSN6SK.mjs";import{a as Z}from"./chunk-FGFLPH5K.mjs";import{a as mr}from"./chunk-U7HD6PQV.mjs";import{a as zi}from"./chunk-AMAPBD4D.mjs";import{a as h}from"./chunk-V2QSMVJ5.mjs";import{a as Gi,b as vn,c as Bi,d as Ni,e as Mi,g as Qe,h as Vi,k as Hi,l as Li}from"./chunk-KRBZ54CY.mjs";import{a as kt}from"./chunk-YOZBVVKL.mjs";import{a as Cn,b as Ui,c as w,e as Di,f as Ri}from"./chunk-GBNAG7KK.mjs";import{A as Fi,d as hi,e as bi,f as wi,g as Ti,i as Si,j as Pi,k as xi,m as Ki,n as Ei,o as Ci,p as Ii,q as vi,r as _i,s as Ft,t as ur,u as En,y as ki,z as dr}from"./chunk-VHNX2NUR.mjs";import{b as _t,d as mi,e as yi,f as Ai}from"./chunk-UOP7GBXB.mjs";import{b as rt,d as gi,e as ar,f as cr}from"./chunk-CZYH3G7E.mjs";import{a as oi}from"./chunk-CW35YAMN.mjs";import{d as b,e as li}from"./chunk-6WDVDEQZ.mjs";import{a as qi}from"./chunk-XTMUMN74.mjs";import{g as or}from"./chunk-4RXKALLC.mjs";import{b as Xr,c as ei,d as ti,e as ve,f as ni,g as ri,h as sr,j as ii}from"./chunk-RJ7F4JDV.mjs";import{a as P,b as Jr,c as _,d as J}from"./chunk-Q4W3WJ2U.mjs";import{a as B,b as Te,c as St,d as Pt,e as Se,f as xt,g as Kt}from"./chunk-ORMOQWWH.mjs";import{a as Qr}from"./chunk-TOBQ5UE6.mjs";import{a as Tt}from"./chunk-MT2RJ7H3.mjs";import{a as H}from"./chunk-FLZPUYXQ.mjs";import{a as $t,b as si,c as _e,d as ai,e as ci,f as ui,i as di,j as pi,k as ue,l as vt}from"./chunk-7DQDJ2SA.mjs";import{a as xn}from"./chunk-HNBVYE3N.mjs";import{b as c}from"./chunk-RGKRCZ36.mjs";import{E as Q}from"./chunk-ODAAZLPK.mjs";import{a as S,b as j}from"./chunk-EBMEXURY.mjs";import{b as T,c as jr}from"./chunk-STY74NUA.mjs";import{d as Wr}from"./chunk-56CNRT2K.mjs";import{a as wt}from"./chunk-KDMSOCZY.mjs";import{jwtDecode as Ns}from"jwt-decode";import{sha3_256 as Wa}from"@noble/hashes/sha3";import{bn254 as we}from"@noble/curves/bn254";import{bytesToNumberBE as ja}from"@noble/curves/abstract/utils";var N=class n extends $e{constructor(e,t){super(),this.jwkAddress=c.from(e),this.keylessPublicKey=t}authKey(){let e=new j;return e.serializeU32AsUleb128(4),e.serializeFixedBytes(this.bcsToBytes()),Z.fromSchemeAndBytes({scheme:2,input:e.toUint8Array()})}verifySignature(e){try{return kn({...e,publicKey:this}),!0}catch{return!1}}serialize(e){this.jwkAddress.serialize(e),this.keylessPublicKey.serialize(e)}static deserialize(e){let t=c.deserialize(e),r=E.deserialize(e);return new n(t,r)}static isPublicKey(e){return e instanceof n}async verifySignatureAsync(e){return Ar({...e,publicKey:this})}static create(e){return new n(e.jwkAddress,E.create(e))}static fromJwtAndPepper(e){return new n(e.jwkAddress,E.fromJwtAndPepper(e))}static isInstance(e){return"jwkAddress"in e&&e.jwkAddress instanceof c&&"keylessPublicKey"in e&&e.keylessPublicKey instanceof E}};import{encode as Qa}from"js-base64";var v=class n extends $e{constructor(e){if(super(),this.publicKey=e,e instanceof k)this.variant=0;else if(e instanceof je)this.variant=1;else if(e instanceof E)this.variant=3;else if(e instanceof N)this.variant=4;else throw new Error("Unsupported public key type")}verifySignature(e){let{message:t,signature:r}=e;if(this.publicKey instanceof E)throw new Error("Use verifySignatureAsync to verify Keyless signatures");return this.publicKey.verifySignature({message:t,signature:r.signature})}async verifySignatureAsync(e){if(!(e.signature instanceof U)){if(e.options?.throwErrorWithReason)throw new Error("Signature must be an instance of AnySignature");return!1}return await this.publicKey.verifySignatureAsync({...e,signature:e.signature.signature})}authKey(){return Z.fromSchemeAndBytes({scheme:2,input:this.toUint8Array()})}toUint8Array(){return this.bcsToBytes()}serialize(e){e.serializeU32AsUleb128(this.variant),this.publicKey.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=k.deserialize(e);break;case 1:r=je.deserialize(e);break;case 3:r=E.deserialize(e);break;case 4:r=N.deserialize(e);break;default:throw new Error(`Unknown variant index for AnyPublicKey: ${t}`)}return new n(r)}static isPublicKey(e){return e instanceof n}isEd25519(){return this.publicKey instanceof k}isSecp256k1PublicKey(){return this.publicKey instanceof je}static isInstance(e){return"publicKey"in e&&"variant"in e}},U=class n extends Pe{constructor(e){if(super(),this.signature=e,e instanceof z)this.variant=0;else if(e instanceof Wt)this.variant=1;else if(e instanceof Y)this.variant=3;else throw new Error("Unsupported signature type")}toUint8Array(){return console.warn("[Aptos SDK] Calls to AnySignature.toUint8Array() will soon return the underlying signature bytes. Use AnySignature.bcsToBytes() instead."),this.bcsToBytes()}serialize(e){e.serializeU32AsUleb128(this.variant),this.signature.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=z.deserialize(e);break;case 1:r=Wt.deserialize(e);break;case 3:r=Y.deserialize(e);break;default:throw new Error(`Unknown variant index for AnySignature: ${t}`)}return new n(r)}static isInstance(e){return"signature"in e&&typeof e.signature=="object"&&e.signature!==null&&"toUint8Array"in e.signature}};function Ms(n){let e=n;return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>24}var jt=class extends $e{constructor(e){super(),this.publicKeys=e.publicKeys}createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach((s,a)=>{if(a+1>this.publicKeys.length)throw new Error(`Signature index ${a+1} is out of public keys range, ${this.publicKeys.length}.`);if(o.has(s))throw new Error(`Duplicate bit ${s} detected.`);o.add(s);let u=Math.floor(s/8),d=i[u];d|=r>>s%8,i[u]=d}),i}getIndex(e){let t=this.publicKeys.findIndex(r=>r.toString()===e.toString());if(t!==-1)return t;throw new Error(`Public key ${e} not found in multi key set ${this.publicKeys}`)}},de=class n extends jt{constructor(e){let{publicKeys:t,signaturesRequired:r}=e;if(super({publicKeys:t}),r<1)throw new Error("The number of required signatures needs to be greater than 0");if(t.length<r)throw new Error(`Provided ${t.length} public keys is smaller than the ${r} required signatures`);this.publicKeys=t.map(i=>i instanceof v?i:new v(i)),this.signaturesRequired=r}verifySignature(e){let{message:t,signature:r}=e;if(r.signatures.length!==this.signaturesRequired)throw new Error("The number of signatures does not match the number of required signatures");let i=r.bitMapToSignerIndices();for(let o=0;o<r.signatures.length;o+=1){let s=r.signatures[o];if(!this.publicKeys[i[o]].verifySignature({message:t,signature:s}))return!1}return!0}async verifySignatureAsync(e){let{signature:t}=e;try{if(!(t instanceof pe))throw new Error("Signature is not a MultiKeySignature");if(t.signatures.length!==this.signaturesRequired)throw new Error("The number of signatures does not match the number of required signatures");let r=t.bitMapToSignerIndices();for(let i=0;i<t.signatures.length;i+=1){let o=t.signatures[i];if(!await this.publicKeys[r[i]].verifySignatureAsync({...e,signature:o}))return!1}return!0}catch(r){if(e.options?.throwErrorWithReason)throw r;return!1}}authKey(){return Z.fromSchemeAndBytes({scheme:3,input:this.toUint8Array()})}serialize(e){e.serializeVector(this.publicKeys),e.serializeU8(this.signaturesRequired)}static deserialize(e){let t=e.deserializeVector(v),r=e.deserializeU8();return new n({publicKeys:t,signaturesRequired:r})}getIndex(e){let t=e instanceof v?e:new v(e);return super.getIndex(t)}static isInstance(e){return"publicKeys"in e&&"signaturesRequired"in e}},re=class re extends Pe{constructor(e){super();let{signatures:t,bitmap:r}=e;if(t.length>re.MAX_SIGNATURES_SUPPORTED)throw new Error(`The number of signatures cannot be greater than ${re.MAX_SIGNATURES_SUPPORTED}`);if(this.signatures=t.map(o=>o instanceof U?o:new U(o)),!(r instanceof Uint8Array))this.bitmap=re.createBitmap({bits:r});else{if(r.length!==re.BITMAP_LEN)throw new Error(`"bitmap" length should be ${re.BITMAP_LEN}`);this.bitmap=r}let i=this.bitmap.reduce((o,s)=>o+Ms(s),0);if(i!==this.signatures.length)throw new Error(`Expecting ${i} signatures from the bitmap, but got ${this.signatures.length}`)}static createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach(s=>{if(s>=re.MAX_SIGNATURES_SUPPORTED)throw new Error(`Cannot have a signature larger than ${re.MAX_SIGNATURES_SUPPORTED-1}.`);if(o.has(s))throw new Error("Duplicate bits detected.");o.add(s);let a=Math.floor(s/8),u=i[a];u|=r>>s%8,i[a]=u}),i}bitMapToSignerIndices(){let e=[];for(let t=0;t<this.bitmap.length;t+=1){let r=this.bitmap[t];for(let i=0;i<8;i+=1)(r&128>>i)!==0&&e.push(t*8+i)}return e}serialize(e){e.serializeVector(this.signatures),e.serializeBytes(this.bitmap)}static deserialize(e){let t=e.deserializeVector(U),r=e.deserializeBytes();return new re({signatures:t,bitmap:r})}};re.BITMAP_LEN=4,re.MAX_SIGNATURES_SUPPORTED=re.BITMAP_LEN*8;var pe=re;var le=class le extends jt{constructor(e){let{publicKeys:t,threshold:r}=e;if(super({publicKeys:t}),t.length>le.MAX_KEYS||t.length<le.MIN_KEYS)throw new Error(`Must have between ${le.MIN_KEYS} and ${le.MAX_KEYS} public keys, inclusive`);if(r<le.MIN_THRESHOLD||r>t.length)throw new Error(`Threshold must be between ${le.MIN_THRESHOLD} and ${t.length}, inclusive`);this.publicKeys=t,this.threshold=r}verifySignature(e){let{message:t,signature:r}=e;if(!(r instanceof Ae))return!1;let i=[];for(let o=0;o<4;o+=1)for(let s=0;s<8;s+=1)if((r.bitmap[o]&1<<7-s)!==0){let u=o*8+s;i.push(u)}if(i.length!==r.signatures.length)throw new Error("Bitmap and signatures length mismatch");if(i.length<this.threshold)throw new Error("Not enough signatures");for(let o=0;o<i.length;o+=1)if(!this.publicKeys[i[o]].verifySignature({message:t,signature:r.signatures[o]}))return!1;return!0}async verifySignatureAsync(e){return this.verifySignature(e)}authKey(){return Z.fromSchemeAndBytes({scheme:1,input:this.toUint8Array()})}toUint8Array(){let e=new Uint8Array(this.publicKeys.length*k.LENGTH+1);return this.publicKeys.forEach((t,r)=>{e.set(t.toUint8Array(),r*k.LENGTH)}),e[this.publicKeys.length*k.LENGTH]=this.threshold,e}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes(),r=t[t.length-1],i=[];for(let o=0;o<t.length-1;o+=k.LENGTH){let s=o;i.push(new k(t.subarray(s,s+k.LENGTH)))}return new le({publicKeys:i,threshold:r})}getIndex(e){return super.getIndex(e)}};le.MAX_KEYS=32,le.MIN_KEYS=2,le.MIN_THRESHOLD=1;var Je=le,ie=class ie extends Pe{constructor(e){super();let{signatures:t,bitmap:r}=e;if(t.length>ie.MAX_SIGNATURES_SUPPORTED)throw new Error(`The number of signatures cannot be greater than ${ie.MAX_SIGNATURES_SUPPORTED}`);if(this.signatures=t,!(r instanceof Uint8Array))this.bitmap=ie.createBitmap({bits:r});else{if(r.length!==ie.BITMAP_LEN)throw new Error(`"bitmap" length should be ${ie.BITMAP_LEN}`);this.bitmap=r}}toUint8Array(){let e=new Uint8Array(this.signatures.length*z.LENGTH+ie.BITMAP_LEN);return this.signatures.forEach((t,r)=>{e.set(t.toUint8Array(),r*z.LENGTH)}),e.set(this.bitmap,this.signatures.length*z.LENGTH),e}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes(),r=t.subarray(t.length-4),i=[];for(let o=0;o<t.length-r.length;o+=z.LENGTH){let s=o;i.push(new z(t.subarray(s,s+z.LENGTH)))}return new ie({signatures:i,bitmap:r})}static createBitmap(e){let{bits:t}=e,r=128,i=new Uint8Array([0,0,0,0]),o=new Set;return t.forEach((s,a)=>{if(s>=ie.MAX_SIGNATURES_SUPPORTED)throw new Error(`Cannot have a signature larger than ${ie.MAX_SIGNATURES_SUPPORTED-1}.`);if(o.has(s))throw new Error("Duplicate bits detected.");if(a>0&&s<=t[a-1])throw new Error("The bits need to be sorted in ascending order.");o.add(s);let u=Math.floor(s/8),d=i[u];d|=r>>s%8,i[u]=d}),i}};ie.MAX_SIGNATURES_SUPPORTED=32,ie.BITMAP_LEN=4;var Ae=ie;var Fn="Multiple possible deserializations found";function du(n){let e=[k,v,Je,de,E,N,je],t;for(let r of e)try{let i=H.fromHex(n),o=r.deserialize(i);if(i.assertFinished(),t)throw new Error(`${Fn}: ${n}`);t=o}catch(i){if(i instanceof Error&&i.message.includes(Fn))throw i}if(!t)throw new Error(`Failed to deserialize public key: ${n}`);return t}function pu(n){let e=[z,U,Ae,pe,Y,Wt],t;for(let r of e)try{let i=H.fromHex(n),o=r.deserialize(i);if(i.assertFinished(),t)throw new Error(`${Fn}: ${n}`);t=o}catch(i){if(i instanceof Error&&i.message.includes(Fn))throw i}if(!t)throw new Error(`Failed to deserialize signature: ${n}`);return t}var L=class extends S{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return fe.load(e);case 1:return ke.load(e);case 2:return X.load(e);case 3:return Ke.load(e);case 4:return Ut.load(e);case 5:return ot.load(e);default:throw new Error(`Unknown variant index for AccountAuthenticator: ${t}`)}}isEd25519(){return this instanceof fe}isMultiEd25519(){return this instanceof ke}isSingleKey(){return this instanceof X}isMultiKey(){return this instanceof Ke}},fe=class n extends L{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(0),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=k.deserialize(e),r=z.deserialize(e);return new n(t,r)}},ke=class n extends L{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(1),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=Je.deserialize(e),r=Ae.deserialize(e);return new n(t,r)}},X=class n extends L{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(2),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=v.deserialize(e),r=U.deserialize(e);return new n(t,r)}},Ke=class n extends L{constructor(e,t){super(),this.public_keys=e,this.signatures=t}serialize(e){e.serializeU32AsUleb128(3),this.public_keys.serialize(e),this.signatures.serialize(e)}static load(e){let t=de.deserialize(e),r=pe.deserialize(e);return new n(t,r)}},Ut=class n extends L{serialize(e){e.serializeU32AsUleb128(4)}static load(e){return new n}},ot=class n extends L{constructor(e,t,r,i){if(super(),!vt(e))throw new Error(`Invalid function info ${e} passed into AccountAuthenticatorAbstraction`);this.functionInfo=e,this.authenticator=r,this.signingMessageDigest=T.fromHexInput(T.fromHexInput(t).toUint8Array()),this.accountIdentity=i}serialize(e){e.serializeU32AsUleb128(5);let{moduleAddress:t,moduleName:r,functionName:i}=ue(this.functionInfo);c.fromString(t).serialize(e),e.serializeStr(r),e.serializeStr(i),this.accountIdentity?e.serializeU32AsUleb128(1):e.serializeU32AsUleb128(0),e.serializeBytes(this.signingMessageDigest.toUint8Array()),this.accountIdentity?e.serializeBytes(this.authenticator):e.serializeFixedBytes(this.authenticator),this.accountIdentity&&e.serializeBytes(this.accountIdentity)}static load(e){let t=c.deserialize(e),r=e.deserializeStr(),i=e.deserializeStr(),o=e.deserializeUleb128AsU32();if(o===0){let s=e.deserializeBytes(),a=e.deserializeFixedBytes(e.remaining());return new n(`${t}::${r}::${i}`,s,a)}if(o===1){let s=e.deserializeBytes(),a=e.deserializeBytes(),u=e.deserializeBytes();return new n(`${t}::${r}::${i}`,s,a,u)}throw new Error(`Unknown variant index for AccountAuthenticatorAbstraction: ${o}`)}};import{sha3_256 as Hs}from"@noble/hashes/sha3";var Qt=class n extends S{constructor(e,t){super(),this.address=e,this.name=t}static fromStr(e){let t=e.split("::");if(t.length!==2)throw new Error("Invalid module id.");return new n(c.fromString(t[0]),new F(t[1]))}serialize(e){this.address.serialize(e),this.name.serialize(e)}static deserialize(e){let t=c.deserialize(e),r=F.deserialize(e);return new n(t,r)}};var R=class n extends S{deserialize(e){let t=c.deserialize(e),r=F.deserialize(e),i=F.deserialize(e),o=e.deserializeVector(n);return new Ee(t,r,i,o)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return G.load(e);case 1:return oe.load(e);case 2:return q.load(e);case 3:return De.load(e);case 4:return D.load(e);case 5:return st.load(e);case 6:return K.load(e);case 7:return m.load(e);case 8:return Fe.load(e);case 9:return Ue.load(e);case 10:return Re.load(e);case 255:return O.load(e);default:throw new Error(`Unknown variant index for TypeTag: ${t}`)}}isBool(){return this instanceof G}isAddress(){return this instanceof D}isGeneric(){return this instanceof O}isSigner(){return this instanceof st}isVector(){return this instanceof K}isStruct(){return this instanceof m}isU8(){return this instanceof oe}isU16(){return this instanceof Fe}isU32(){return this instanceof Ue}isU64(){return this instanceof q}isU128(){return this instanceof De}isU256(){return this instanceof Re}isPrimitive(){return this instanceof st||this instanceof D||this instanceof G||this instanceof oe||this instanceof Fe||this instanceof Ue||this instanceof q||this instanceof De||this instanceof Re}},G=class n extends R{toString(){return"bool"}serialize(e){e.serializeU32AsUleb128(0)}static load(e){return new n}},oe=class n extends R{toString(){return"u8"}serialize(e){e.serializeU32AsUleb128(1)}static load(e){return new n}},Fe=class n extends R{toString(){return"u16"}serialize(e){e.serializeU32AsUleb128(8)}static load(e){return new n}},Ue=class n extends R{toString(){return"u32"}serialize(e){e.serializeU32AsUleb128(9)}static load(e){return new n}},q=class n extends R{toString(){return"u64"}serialize(e){e.serializeU32AsUleb128(2)}static load(e){return new n}},De=class n extends R{toString(){return"u128"}serialize(e){e.serializeU32AsUleb128(3)}static load(e){return new n}},Re=class n extends R{toString(){return"u256"}serialize(e){e.serializeU32AsUleb128(10)}static load(e){return new n}},D=class n extends R{toString(){return"address"}serialize(e){e.serializeU32AsUleb128(4)}static load(e){return new n}},st=class n extends R{toString(){return"signer"}serialize(e){e.serializeU32AsUleb128(5)}static load(e){return new n}},Un=class n extends R{constructor(t){super();this.value=t}toString(){return`&${this.value.toString()}`}serialize(t){t.serializeU32AsUleb128(254)}static load(t){let r=R.deserialize(t);return new n(r)}},O=class n extends R{constructor(t){super();this.value=t;if(t<0)throw new Error("Generic type parameter index cannot be negative")}toString(){return`T${this.value}`}serialize(t){t.serializeU32AsUleb128(255),t.serializeU32(this.value)}static load(t){let r=t.deserializeU32();return new n(r)}},K=class n extends R{constructor(t){super();this.value=t}toString(){return`vector<${this.value.toString()}>`}static u8(){return new n(new oe)}serialize(t){t.serializeU32AsUleb128(6),this.value.serialize(t)}static load(t){let r=R.deserialize(t);return new n(r)}},m=class n extends R{constructor(t){super();this.value=t}toString(){let t="";return this.value.typeArgs.length>0&&(t=`<${this.value.typeArgs.map(r=>r.toString()).join(", ")}>`),`${this.value.address.toString()}::${this.value.moduleName.identifier}::${this.value.name.identifier}${t}`}serialize(t){t.serializeU32AsUleb128(7),this.value.serialize(t)}static load(t){let r=Ee.deserialize(t);return new n(r)}isTypeTag(t,r,i){return this.value.moduleName.identifier===r&&this.value.name.identifier===i&&this.value.address.equals(t)}isString(){return this.isTypeTag(c.ONE,"string","String")}isOption(){return this.isTypeTag(c.ONE,"option","Option")}isObject(){return this.isTypeTag(c.ONE,"object","Object")}isDelegationKey(){return this.isTypeTag(c.ONE,"permissioned_delegation","DelegationKey")}isRateLimiter(){return this.isTypeTag(c.ONE,"rate_limiter","RateLimiter")}},Ee=class n extends S{constructor(e,t,r,i){super(),this.address=e,this.moduleName=t,this.name=r,this.typeArgs=i}serialize(e){e.serialize(this.address),e.serialize(this.moduleName),e.serialize(this.name),e.serializeVector(this.typeArgs)}static deserialize(e){let t=c.deserialize(e),r=F.deserialize(e),i=F.deserialize(e),o=e.deserializeVector(R);return new n(t,r,i,o)}};function nd(){return new Ee(c.ONE,new F("aptos_coin"),new F("AptosCoin"),[])}function x(){return new Ee(c.ONE,new F("string"),new F("String"),[])}function rd(n){return new Ee(c.ONE,new F("option"),new F("Option"),[n])}function se(n){return new Ee(c.ONE,new F("object"),new F("Object"),[n])}function Vs(n){let e=n.deserializeUleb128AsU32();switch(e){case 0:return Te.deserialize(n);case 1:return Se.deserialize(n);case 2:return xt.deserialize(n);case 3:return c.deserialize(n);case 4:return P.deserialize(n,Te);case 5:return B.deserialize(n);case 6:return St.deserialize(n);case 7:return Pt.deserialize(n);case 8:return Kt.deserialize(n);case 9:return Jr.deserialize(n);default:throw new Error(`Unknown variant index for ScriptTransactionArgument: ${e}`)}}var at=class extends S{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return Jt.load(e);case 2:return Zt.load(e);case 3:return Yt.load(e);default:throw new Error(`Unknown variant index for TransactionPayload: ${t}`)}}},Jt=class n extends at{constructor(e){super(),this.script=e}serialize(e){e.serializeU32AsUleb128(0),this.script.serialize(e)}static load(e){let t=Xt.deserialize(e);return new n(t)}},Zt=class n extends at{constructor(e){super(),this.entryFunction=e}serialize(e){e.serializeU32AsUleb128(2),this.entryFunction.serialize(e)}static load(e){let t=ct.deserialize(e);return new n(t)}},Yt=class n extends at{constructor(e){super(),this.multiSig=e}serialize(e){e.serializeU32AsUleb128(3),this.multiSig.serialize(e)}static load(e){let t=en.deserialize(e);return new n(t)}},ct=class n{constructor(e,t,r,i){this.module_name=e,this.function_name=t,this.type_args=r,this.args=i}static build(e,t,r,i){return new n(Qt.fromStr(e),new F(t),r,i)}serialize(e){this.module_name.serialize(e),this.function_name.serialize(e),e.serializeVector(this.type_args),e.serializeU32AsUleb128(this.args.length),this.args.forEach(t=>{t.serializeForEntryFunction(e)})}static deserialize(e){let t=Qt.deserialize(e),r=F.deserialize(e),i=e.deserializeVector(R),o=e.deserializeUleb128AsU32(),s=new Array;for(let a=0;a<o;a+=1){let u=e.deserializeUleb128AsU32(),d=Qr.deserialize(e,u);s.push(d)}return new n(t,r,i,s)}},Xt=class n{constructor(e,t,r){this.bytecode=e,this.type_args=t,this.args=r}serialize(e){e.serializeBytes(this.bytecode),e.serializeVector(this.type_args),e.serializeU32AsUleb128(this.args.length),this.args.forEach(t=>{t.serializeForScriptFunction(e)})}static deserialize(e){let t=e.deserializeBytes(),r=e.deserializeVector(R),i=e.deserializeUleb128AsU32(),o=new Array;for(let s=0;s<i;s+=1){let a=Vs(e);o.push(a)}return new n(t,r,o)}},en=class n{constructor(e,t){this.multisig_address=e,this.transaction_payload=t}serialize(e){this.multisig_address.serialize(e),this.transaction_payload===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.transaction_payload.serialize(e))}static deserialize(e){let t=c.deserialize(e),r=e.deserializeBool(),i;return r&&(i=tn.deserialize(e)),new n(t,i)}},tn=class n extends S{constructor(e){super(),this.transaction_payload=e}serialize(e){e.serializeU32AsUleb128(0),this.transaction_payload.serialize(e)}static deserialize(e){return e.deserializeUleb128AsU32(),new n(ct.deserialize(e))}};var ge=class n extends S{constructor(e,t,r,i,o,s,a){super(),this.sender=e,this.sequence_number=t,this.payload=r,this.max_gas_amount=i,this.gas_unit_price=o,this.expiration_timestamp_secs=s,this.chain_id=a}serialize(e){this.sender.serialize(e),e.serializeU64(this.sequence_number),this.payload.serialize(e),e.serializeU64(this.max_gas_amount),e.serializeU64(this.gas_unit_price),e.serializeU64(this.expiration_timestamp_secs),this.chain_id.serialize(e)}static deserialize(e){let t=c.deserialize(e),r=e.deserializeU64(),i=at.deserialize(e),o=e.deserializeU64(),s=e.deserializeU64(),a=e.deserializeU64(),u=Kn.deserialize(e);return new n(t,r,i,o,s,a,u)}},Dn=class extends S{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return ut.load(e);case 1:return dt.load(e);default:throw new Error(`Unknown variant index for RawTransactionWithData: ${t}`)}}},ut=class n extends Dn{constructor(e,t){super(),this.raw_txn=e,this.secondary_signer_addresses=t}serialize(e){e.serializeU32AsUleb128(0),this.raw_txn.serialize(e),e.serializeVector(this.secondary_signer_addresses)}static load(e){let t=ge.deserialize(e),r=e.deserializeVector(c);return new n(t,r)}},dt=class n extends Dn{constructor(e,t,r){super(),this.raw_txn=e,this.secondary_signer_addresses=t,this.fee_payer_address=r}serialize(e){e.serializeU32AsUleb128(1),this.raw_txn.serialize(e),e.serializeVector(this.secondary_signer_addresses),this.fee_payer_address.serialize(e)}static load(e){let t=ge.deserialize(e),r=e.deserializeVector(c),i=c.deserialize(e);return new n(t,r,i)}};var Oe=class extends S{static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return pt.load(e);case 1:return Dt.load(e);case 2:return lt.load(e);case 3:return gt.load(e);case 4:return Ze.load(e);default:throw new Error(`Unknown variant index for TransactionAuthenticator: ${t}`)}}isEd25519(){return this instanceof pt}isMultiEd25519(){return this instanceof Dt}isMultiAgent(){return this instanceof lt}isFeePayer(){return this instanceof gt}isSingleSender(){return this instanceof Ze}},pt=class n extends Oe{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(0),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=k.deserialize(e),r=z.deserialize(e);return new n(t,r)}},Dt=class n extends Oe{constructor(e,t){super(),this.public_key=e,this.signature=t}serialize(e){e.serializeU32AsUleb128(1),this.public_key.serialize(e),this.signature.serialize(e)}static load(e){let t=Je.deserialize(e),r=Ae.deserialize(e);return new n(t,r)}},lt=class n extends Oe{constructor(e,t,r){super(),this.sender=e,this.secondary_signer_addresses=t,this.secondary_signers=r}serialize(e){e.serializeU32AsUleb128(2),this.sender.serialize(e),e.serializeVector(this.secondary_signer_addresses),e.serializeVector(this.secondary_signers)}static load(e){let t=L.deserialize(e),r=e.deserializeVector(c),i=e.deserializeVector(L);return new n(t,r,i)}},gt=class n extends Oe{constructor(e,t,r,i){super(),this.sender=e,this.secondary_signer_addresses=t,this.secondary_signers=r,this.fee_payer=i}serialize(e){e.serializeU32AsUleb128(3),this.sender.serialize(e),e.serializeVector(this.secondary_signer_addresses),e.serializeVector(this.secondary_signers),this.fee_payer.address.serialize(e),this.fee_payer.authenticator.serialize(e)}static load(e){let t=L.deserialize(e),r=e.deserializeVector(c),i=e.deserializeVector(L),o=c.deserialize(e),s=L.deserialize(e),a={address:o,authenticator:s};return new n(t,r,i,a)}},Ze=class n extends Oe{constructor(e){super(),this.sender=e}serialize(e){e.serializeU32AsUleb128(4),this.sender.serialize(e)}static load(e){let t=L.deserialize(e);return new n(t)}};var ze=class n extends S{constructor(e,t){super(),this.raw_txn=e,this.authenticator=t}serialize(e){this.raw_txn.serialize(e),this.authenticator.serialize(e)}static deserialize(e){let t=ge.deserialize(e),r=Oe.deserialize(e);return new n(t,r)}};var Rn=class n extends S{constructor(e,t){super(),this.rawTransaction=e,this.feePayerAddress=t}serialize(e){this.rawTransaction.serialize(e),this.feePayerAddress===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.feePayerAddress.serialize(e))}static deserialize(e){let t=ge.deserialize(e),r=e.deserializeBool(),i;return r&&(i=c.deserialize(e)),new n(t,i)}};var On=class n extends S{constructor(e,t,r){super(),this.rawTransaction=e,this.feePayerAddress=r,this.secondarySignerAddresses=t}serialize(e){this.rawTransaction.serialize(e),e.serializeVector(this.secondarySignerAddresses),this.feePayerAddress===void 0?e.serializeBool(!1):(e.serializeBool(!0),this.feePayerAddress.serialize(e))}static deserialize(e){let t=ge.deserialize(e),r=e.deserializeVector(c),i=e.deserializeBool(),o;return i&&(o=c.deserialize(e)),new n(t,r,o)}};function zn(n){return n.feePayerAddress?new dt(n.rawTransaction,n.secondarySignerAddresses??[],n.feePayerAddress):n.secondarySignerAddresses?new ut(n.rawTransaction,n.secondarySignerAddresses):n.rawTransaction}function Ye(n,e){let t=Hs.create();if(!e.startsWith("APTOS::"))throw new Error(`Domain separator needs to start with 'APTOS::'.  Provided - ${e}`);t.update(e);let r=t.digest(),i=n,o=new Uint8Array(r.length+i.length);return o.set(r),o.set(i,r.length),o}function lp(n){return Ye(n.bcsToBytes(),`APTOS::${n.constructor.name}`)}function he(n){let e=zn(n);return n.feePayerAddress?Ye(e.bcsToBytes(),sr):n.secondarySignerAddresses?Ye(e.bcsToBytes(),sr):Ye(e.bcsToBytes(),ri)}var ee=class n{constructor(e){this.signingScheme=0;let{privateKey:t,address:r}=e;this.privateKey=t,this.publicKey=t.publicKey(),this.accountAddress=r?c.from(r):this.publicKey.authKey().derivedAddress()}static generate(){let e=$.generate();return new n({privateKey:e})}static fromDerivationPath(e){let{path:t,mnemonic:r}=e,i=$.fromDerivationPath(t,r);return new n({privateKey:i})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new fe(this.publicKey,this.privateKey.sign(e))}signTransactionWithAuthenticator(e){return new fe(this.publicKey,this.signTransaction(e))}sign(e){return this.privateKey.sign(e)}signTransaction(e){return this.sign(he(e))}};function fr(n){return typeof n=="object"&&n!==null&&"getAnyPublicKey"in n&&typeof n.getAnyPublicKey=="function"}var me=class n{constructor(e){this.signingScheme=2;let{privateKey:t,address:r}=e;this.privateKey=t,this.publicKey=new v(t.publicKey()),this.accountAddress=r?c.from(r):this.publicKey.authKey().derivedAddress()}getAnyPublicKey(){return this.publicKey}static generate(e={}){let{scheme:t=0}=e,r;switch(t){case 0:r=$.generate();break;case 2:r=it.generate();break;default:throw new Error(`Unsupported signature scheme ${t}`)}return new n({privateKey:r})}static fromDerivationPath(e){let{scheme:t=0,path:r,mnemonic:i}=e,o;switch(t){case 0:o=$.fromDerivationPath(r,i);break;case 2:o=it.fromDerivationPath(r,i);break;default:throw new Error(`Unsupported signature scheme ${t}`)}return new n({privateKey:o})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new X(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new X(this.publicKey,this.signTransaction(e))}sign(e){return new U(this.privateKey.sign(e))}signTransaction(e){return this.sign(he(e))}static fromEd25519Account(e){return new n({privateKey:e.privateKey,address:e.accountAddress})}};var Ge=class{static generate(e={}){let{scheme:t=0,legacy:r=!0}=e;return t===0&&r?ee.generate():me.generate({scheme:t})}static fromPrivateKey(e){let{privateKey:t,address:r,legacy:i=!0}=e;return t instanceof $&&i?new ee({privateKey:t,address:r}):new me({privateKey:t,address:r})}static fromPrivateKeyAndAddress(e){return this.fromPrivateKey(e)}static fromDerivationPath(e){let{scheme:t=0,mnemonic:r,path:i,legacy:o=!0}=e;return t===0&&o?ee.fromDerivationPath({mnemonic:r,path:i}):me.fromDerivationPath({scheme:t,mnemonic:r,path:i})}static authKey(e){let{publicKey:t}=e;return t.authKey()}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync(e)}};import{randomBytes as qs}from"@noble/hashes/utils";var $s=1209600,Rt=class Rt extends S{constructor(e){super();let{privateKey:t,expiryDateSecs:r,blinder:i}=e;this.privateKey=t,this.publicKey=new Et(t.publicKey()),this.expiryDateSecs=r||ai(_e()+$s),this.blinder=i!==void 0?T.fromHexInput(i).toUint8Array():Ws();let o=Pn(this.publicKey.bcsToBytes(),93);o.push(BigInt(this.expiryDateSecs)),o.push(Ct(this.blinder));let s=It(o);this.nonce=s.toString()}getPublicKey(){return this.publicKey}isExpired(){return Math.floor(Date.now()/1e3)>this.expiryDateSecs}serialize(e){e.serializeU32AsUleb128(this.publicKey.variant),e.serializeBytes(this.privateKey.toUint8Array()),e.serializeU64(this.expiryDateSecs),e.serializeFixedBytes(this.blinder)}static deserialize(e){let t=e.deserializeUleb128AsU32(),r;switch(t){case 0:r=$.deserialize(e);break;default:throw new Error(`Unknown variant index for EphemeralPublicKey: ${t}`)}let i=e.deserializeU64(),o=e.deserializeFixedBytes(31);return new Rt({privateKey:r,expiryDateSecs:Number(i),blinder:o})}static fromBytes(e){return Rt.deserialize(new H(e))}static generate(e){let t;switch(e?.scheme){case 0:default:t=$.generate()}return new Rt({privateKey:t,expiryDateSecs:e?.expiryDateSecs})}sign(e){if(this.isExpired())throw new Error("EphemeralKeyPair has expired");return new We(this.privateKey.sign(e))}};Rt.BLINDER_LENGTH=31;var mt=Rt;function Ws(){return qs(mt.BLINDER_LENGTH)}import Qs from"eventemitter3";import{jwtDecode as Js}from"jwt-decode";function Bn(n){return n!=null&&typeof n.checkKeylessAccountValidity=="function"}var Ot=class Ot extends S{constructor(t){super();this.signingScheme=2;let{address:r,ephemeralKeyPair:i,publicKey:o,uidKey:s,uidVal:a,aud:u,pepper:d,proof:l,proofFetchCallback:A,jwt:p,verificationKeyHash:g}=t;if(this.ephemeralKeyPair=i,this.publicKey=o,this.accountAddress=r?c.from(r):this.publicKey.authKey().derivedAddress(),this.uidKey=s,this.uidVal=a,this.aud=u,this.jwt=p,this.emitter=new Qs,this.proofOrPromise=l,l instanceof ae)this.proof=l;else{if(A===void 0)throw new Error("Must provide callback for async proof fetch");this.emitter.on("proofFetchFinish",async C=>{await A(C),this.emitter.removeAllListeners()}),this.init(l)}let f=T.fromHexInput(d).toUint8Array();if(f.length!==Ot.PEPPER_LENGTH)throw new Error(`Pepper length in bytes should be ${Ot.PEPPER_LENGTH}`);if(this.pepper=f,g!==void 0){if(T.hexInputToUint8Array(g).length!==32)throw new Error("verificationKeyHash must be 32 bytes");this.verificationKeyHash=T.hexInputToUint8Array(g)}}getAnyPublicKey(){return new v(this.publicKey)}async init(t){try{this.proof=await t,this.emitter.emit("proofFetchFinish",{status:"Success"})}catch(r){r instanceof Error?this.emitter.emit("proofFetchFinish",{status:"Failed",error:r.toString()}):this.emitter.emit("proofFetchFinish",{status:"Failed",error:"Unknown"})}}serialize(t){if(this.accountAddress.serialize(t),t.serializeStr(this.jwt),t.serializeStr(this.uidKey),t.serializeFixedBytes(this.pepper),this.ephemeralKeyPair.serialize(t),this.proof===void 0)throw new Error("Cannot serialize - proof undefined");this.proof.serialize(t),t.serializeOption(this.verificationKeyHash,32)}static partialDeserialize(t){let r=c.deserialize(t),i=t.deserializeStr(),o=t.deserializeStr(),s=t.deserializeFixedBytes(31),a=mt.deserialize(t),u=ae.deserialize(t),d=t.deserializeOption("fixedBytes",32);return{address:r,jwt:i,uidKey:o,pepper:s,ephemeralKeyPair:a,proof:u,verificationKeyHash:d}}isExpired(){return this.ephemeralKeyPair.isExpired()}signWithAuthenticator(t){let r=new U(this.sign(t)),i=new v(this.publicKey);return new X(i,r)}signTransactionWithAuthenticator(t){let r=new U(this.signTransaction(t)),i=new v(this.publicKey);return new X(i,r)}async waitForProofFetch(){this.proofOrPromise instanceof Promise&&await this.proofOrPromise}async checkKeylessAccountValidity(t){if(this.isExpired())throw b.fromErrorType({type:0});if(await this.waitForProofFetch(),this.proof===void 0)throw b.fromErrorType({type:2});let r=Js(this.jwt,{header:!0});if(r.kid===void 0)throw b.fromErrorType({type:12,details:"checkKeylessAccountValidity failed. JWT is missing 'kid' in header. This should never happen."});if(this.verificationKeyHash!==void 0){let{verificationKey:i}=await Gt({aptosConfig:t});if(T.hexInputToString(i.hash())!==T.hexInputToString(this.verificationKeyHash))throw b.fromErrorType({type:4})}else console.warn("[Aptos SDK] The verification key hash was not set. Proof may be invalid if the verification key has rotated.");await Ot.fetchJWK({aptosConfig:t,publicKey:this.publicKey,kid:r.kid})}sign(t){let{expiryDateSecs:r}=this.ephemeralKeyPair;if(this.isExpired())throw b.fromErrorType({type:0});if(this.proof===void 0)throw b.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let i=this.ephemeralKeyPair.getPublicKey(),o=this.ephemeralKeyPair.sign(t);return new Y({jwtHeader:ci(this.jwt.split(".")[0]),ephemeralCertificate:new zt(this.proof,0),expiryDateSecs:r,ephemeralPublicKey:i,ephemeralSignature:o})}signTransaction(t){if(this.proof===void 0)throw b.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let r=zn(t),o=new Gn(r,this.proof.proof).hash();return this.sign(o)}getSigningMessage(t){if(this.proof===void 0)throw b.fromErrorType({type:1,details:"Proof not found - make sure to call `await account.checkKeylessAccountValidity()` before signing."});let r=zn(t);return new Gn(r,this.proof.proof).hash()}verifySignature(t){return this.publicKey.verifySignature(t)}async verifySignatureAsync(t){return this.publicKey.verifySignatureAsync({...t})}static async fetchJWK(t){return hr(t)}};Ot.PEPPER_LENGTH=31;var ce=Ot,Gn=class extends S{constructor(t,r){super();this.domainSeparator="APTOS::TransactionAndProof";this.transaction=t,this.proof=r}serialize(t){t.serializeFixedBytes(this.transaction.bcsToBytes()),t.serializeOption(this.proof)}hash(){return Ye(this.bcsToBytes(),this.domainSeparator)}};var Ne=class n extends ce{constructor(e){let t=E.create(e);super({publicKey:t,...e}),this.publicKey=t}serialize(e){super.serialize(e)}static deserialize(e){let{address:t,proof:r,ephemeralKeyPair:i,jwt:o,uidKey:s,pepper:a,verificationKeyHash:u}=ce.partialDeserialize(e),{iss:d,aud:l,uidVal:A}=Be({jwt:o,uidKey:s});return new n({address:t,proof:r,ephemeralKeyPair:i,iss:d,uidKey:s,uidVal:A,aud:l,pepper:a,jwt:o,verificationKeyHash:u})}static fromBytes(e){return n.deserialize(new H(T.hexInputToUint8Array(e)))}static create(e){let{address:t,proof:r,jwt:i,ephemeralKeyPair:o,pepper:s,uidKey:a="sub",proofFetchCallback:u,verificationKey:d}=e,{iss:l,aud:A,uidVal:p}=Be({jwt:i,uidKey:a});return new n({address:t,proof:r,ephemeralKeyPair:o,iss:l,uidKey:a,uidVal:p,aud:A,pepper:s,jwt:i,proofFetchCallback:u,verificationKeyHash:d?d.hash():void 0})}};var yt=class n extends ce{constructor(e){let t=N.create(e);super({publicKey:t,...e}),this.publicKey=t,this.audless=e.audless??!1}serialize(e){super.serialize(e),this.publicKey.jwkAddress.serialize(e)}static deserialize(e){let{address:t,proof:r,ephemeralKeyPair:i,jwt:o,uidKey:s,pepper:a,verificationKeyHash:u}=ce.partialDeserialize(e),d=c.deserialize(e),{iss:l,aud:A,uidVal:p}=Be({jwt:o,uidKey:s});return new n({address:t,proof:r,ephemeralKeyPair:i,iss:l,uidKey:s,uidVal:p,aud:A,pepper:a,jwt:o,verificationKeyHash:u,jwkAddress:d})}static fromBytes(e){return n.deserialize(H.fromHex(e))}static create(e){let{address:t,proof:r,jwt:i,ephemeralKeyPair:o,pepper:s,jwkAddress:a,uidKey:u="sub",proofFetchCallback:d,verificationKey:l}=e,{iss:A,aud:p,uidVal:g}=Be({jwt:i,uidKey:u});return new n({address:t,proof:r,ephemeralKeyPair:o,iss:A,uidKey:u,uidVal:g,aud:p,pepper:s,jwkAddress:c.from(a),jwt:i,proofFetchCallback:d,verificationKeyHash:l?l.hash():void 0})}};var nn=class n{constructor(e){this.signingScheme=3;let{multiKey:t,address:r}=e,i=e.signers.map(a=>a instanceof ee?me.fromEd25519Account(a):a);if(t.signaturesRequired>i.length)throw new Error(`Not enough signers provided to satisfy the required signatures. Need ${t.signaturesRequired} signers, but only ${i.length} provided`);if(t.signaturesRequired<i.length)throw new Error(`More signers provided than required. Need ${t.signaturesRequired} signers, but ${i.length} provided`);this.publicKey=t,this.accountAddress=r?c.from(r):this.publicKey.authKey().derivedAddress();let o=[];for(let a of i)o.push(this.publicKey.getIndex(a.getAnyPublicKey()));let s=i.map((a,u)=>[a,o[u]]);s.sort((a,u)=>a[1]-u[1]),this.signers=s.map(a=>a[0]),this.signerIndicies=s.map(a=>a[1]),this.signaturesBitmap=this.publicKey.createBitmap({bits:o})}static fromPublicKeysAndSigners(e){let{address:t,publicKeys:r,signaturesRequired:i,signers:o}=e,s=new de({publicKeys:r,signaturesRequired:i});return new n({multiKey:s,signers:o,address:t})}static isMultiKeySigner(e){return e instanceof n}signWithAuthenticator(e){return new Ke(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new Ke(this.publicKey,this.signTransaction(e))}async waitForProofFetch(){let t=this.signers.filter(r=>r instanceof ce).map(async r=>r.waitForProofFetch());await Promise.all(t)}async checkKeylessAccountValidity(e){let r=this.signers.filter(i=>i instanceof ce).map(i=>i.checkKeylessAccountValidity(e));await Promise.all(r)}sign(e){let t=[];for(let r of this.signers)t.push(r.sign(e));return new pe({signatures:t,bitmap:this.signaturesBitmap})}signTransaction(e){let t=[];for(let r of this.signers)t.push(r.signTransaction(e));return new pe({signatures:t,bitmap:this.signaturesBitmap})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return await this.publicKey.verifySignatureAsync(e)}};var Nn=class{constructor(e){this.signingScheme=1;let{signers:t,publicKey:r,address:i}=e;if(this.publicKey=r,this.accountAddress=i?c.from(i):this.publicKey.authKey().derivedAddress(),r.threshold>t.length)throw new Error(`Not enough signers provided to satisfy the required signatures. Need ${r.threshold} signers, but only ${t.length} provided`);if(r.threshold<t.length)throw new Error(`More signers provided than required. Need ${r.threshold} signers, but ${t.length} provided`);let o=[];for(let a of t)o.push(this.publicKey.getIndex(a.publicKey()));let s=t.map((a,u)=>[a,o[u]]);s.sort((a,u)=>a[1]-u[1]),this.signers=s.map(a=>a[0]),this.signerIndices=s.map(a=>a[1]),this.signaturesBitmap=this.publicKey.createBitmap({bits:o})}verifySignature(e){return this.publicKey.verifySignature(e)}async verifySignatureAsync(e){return this.publicKey.verifySignatureAsync({...e,signature:e.signature})}signWithAuthenticator(e){return new ke(this.publicKey,this.sign(e))}signTransactionWithAuthenticator(e){return new ke(this.publicKey,this.signTransaction(e))}sign(e){let t=[];for(let r of this.signers)t.push(r.sign(e));return new Ae({signatures:t,bitmap:this.signaturesBitmap})}signTransaction(e){return this.sign(he(e))}};function Wi(n){let e=n.deserializeUleb128AsU32();if(!Object.values(Q).includes(e))throw new Error(`Deserialization of Account failed: SigningScheme variant ${e} is invalid`);return{address:c.deserialize(n),signingScheme:e}}function ji(n,e){if(e.serializeStr(n.jwt),e.serializeStr(n.uidKey),e.serializeFixedBytes(n.pepper),n.ephemeralKeyPair.serialize(e),n.proof===void 0)throw new Error("Cannot serialize - proof undefined");n.proof.serialize(e),e.serializeOption(n.verificationKeyHash,32)}function Qi(n){let e=n.deserializeStr(),t=n.deserializeStr(),r=n.deserializeFixedBytes(31),i=mt.deserialize(n),o=ae.deserialize(n),s=n.deserializeOption("fixedBytes",32);return{jwt:e,uidKey:t,pepper:r,ephemeralKeyPair:i,proof:o,verificationKeyHash:s}}var Ji;(A=>{function n(p){let g=new j;switch(g.serializeU32AsUleb128(p.signingScheme),p.accountAddress.serialize(g),p.signingScheme){case 0:return p.privateKey.serialize(g),g.toUint8Array();case 2:{if(!fr(p))throw new Error("Account is not a SingleKeySigner");let f=p.getAnyPublicKey();switch(g.serializeU32AsUleb128(f.variant),f.variant){case 3:return ji(p,g),g.toUint8Array();case 4:{let C=p;return ji(C,g),C.publicKey.jwkAddress.serialize(g),g.serializeBool(C.audless),g.toUint8Array()}case 1:case 0:return p.privateKey.serialize(g),g.toUint8Array();default:throw new Error(`Invalid public key variant: ${f.variant}`)}}case 3:{let f=p;return f.publicKey.serialize(g),g.serializeU32AsUleb128(f.signers.length),f.signers.forEach(C=>{g.serializeFixedBytes(n(C))}),g.toUint8Array()}default:throw new Error(`Deserialization of Account failed: invalid signingScheme value ${p.signingScheme}`)}}A.toBytes=n;function e(p){return T.hexInputToStringWithoutPrefix(n(p))}A.toHexStringWithoutPrefix=e;function t(p){return T.hexInputToString(n(p))}A.toHexString=t;function r(p){let{address:g,signingScheme:f}=Wi(p);switch(f){case 0:{let C=$.deserialize(p);return new ee({privateKey:C,address:g})}case 2:{let C=p.deserializeUleb128AsU32();switch(C){case 0:{let V=$.deserialize(p);return new me({privateKey:V,address:g})}case 1:{let V=it.deserialize(p);return new me({privateKey:V,address:g})}case 3:{let V=Qi(p),Ie=Be(V);return new Ne({...V,...Ie})}case 4:{let V=Qi(p),Ie=c.deserialize(p),Le=p.deserializeBool(),qe=Be(V);return new yt({...V,...qe,jwkAddress:Ie,audless:Le})}default:throw new Error(`Unsupported public key variant ${C}`)}}case 3:{let C=de.deserialize(p),V=p.deserializeUleb128AsU32(),Ie=new Array;for(let Le=0;Le<V;Le+=1){let qe=r(p);if(!fr(qe)&&!(qe instanceof ee))throw new Error("Deserialization of MultiKeyAccount failed. Signer is not a SingleKeySigner or Ed25519Account");Ie.push(qe)}return new nn({multiKey:C,signers:Ie,address:g})}default:throw new Error(`Deserialization of Account failed: invalid signingScheme value ${f}`)}}A.deserialize=r;function i(p){let g=d(p);if(!(g instanceof Ne))throw new Error("Deserialization of KeylessAccount failed");return g}A.keylessAccountFromHex=i;function o(p){let g=d(p);if(!(g instanceof yt))throw new Error("Deserialization of FederatedKeylessAccount failed");return g}A.federatedKeylessAccountFromHex=o;function s(p){let g=d(p);if(!(g instanceof nn))throw new Error("Deserialization of MultiKeyAccount failed");return g}A.multiKeyAccountFromHex=s;function a(p){let g=d(p);if(!(g instanceof me))throw new Error("Deserialization of SingleKeyAccount failed");return g}A.singleKeyAccountFromHex=a;function u(p){let g=d(p);if(!(g instanceof ee))throw new Error("Deserialization of Ed25519Account failed");return g}A.ed25519AccountFromHex=u;function d(p){return r(H.fromHex(p))}A.fromHex=d;function l(p){return d(p)}A.fromBytes=l})(Ji||(Ji={}));import{sha3_256 as Zi}from"@noble/hashes/sha3";var Mn=class n extends Ge{constructor({signer:t,accountAddress:r,authenticationFunction:i}){super();this.signingScheme=2;if(!vt(i))throw new Error(`Invalid authentication function ${i} passed into AbstractedAccount`);this.authenticationFunction=i,this.accountAddress=r,this.publicKey=new Zr(this.accountAddress),this.sign=o=>new rr(t(o))}static fromPermissionedSigner({signer:t,accountAddress:r}){return new n({signer:i=>{let o=new j;return t.publicKey.serialize(o),t.sign(i).serialize(o),o.toUint8Array()},accountAddress:r??t.accountAddress,authenticationFunction:"0x1::permissioned_delegation::authenticate"})}signWithAuthenticator(t){return new ot(this.authenticationFunction,Zi(t),this.sign(Zi(t)).toUint8Array())}signTransactionWithAuthenticator(t){return this.signWithAuthenticator(he(t))}signTransaction(t){return this.sign(he(t))}setSigner(t){this.sign=r=>new rr(t(r))}};import{sha3_256 as br}from"@noble/hashes/sha3";var rn=class rn extends Mn{constructor({signer:e,authenticationFunction:t,abstractPublicKey:r}){let i=new c(rn.computeAccountAddress(t,r));super({accountAddress:i,signer:e,authenticationFunction:t}),this.abstractPublicKey=r}static computeAccountAddress(e,t){if(!vt(e))throw new Error(`Invalid authentication function ${e} passed into DerivableAbstractedAccount`);let[r,i,o]=e.split("::"),s=br.create(),a=new j;c.fromString(r).serialize(a),a.serializeStr(i),a.serializeStr(o),s.update(a.toUint8Array());let u=new j;return u.serializeBytes(t),s.update(u.toUint8Array()),s.update(new Uint8Array([rn.ADDRESS_DOMAIN_SEPERATOR])),s.digest()}signWithAuthenticator(e){return new ot(this.authenticationFunction,br(e),this.sign(br(e)).value,this.abstractPublicKey)}};rn.ADDRESS_DOMAIN_SEPERATOR=5;var Yi=rn;import{sha3_256 as aa}from"@noble/hashes/sha3";function Xi(n){return!!n.match(/^[_a-zA-Z0-9]+$/)}function eo(n){return!!n.match(/\s/)}function Zs(n){return!!n.match(/^T[0-9]+$/)}function Ys(n){return!!n.match(/^&.+$/)}function Xs(n){switch(n){case"signer":case"address":case"bool":case"u8":case"u16":case"u32":case"u64":case"u128":case"u256":return!0;default:return!1}}function ea(n,e){let t=e;for(;t<n.length;t+=1){let r=n[t];if(!eo(r))break}return t}var ta=(f=>(f.InvalidTypeTag="unknown type",f.UnexpectedGenericType="unexpected generic type",f.UnexpectedTypeArgumentClose="unexpected '>'",f.UnexpectedWhitespaceCharacter="unexpected whitespace character",f.UnexpectedComma="unexpected ','",f.TypeArgumentCountMismatch="type argument count doesn't match expected amount",f.MissingTypeArgumentClose="no matching '>' for '<'",f.MissingTypeArgument="no type argument before ','",f.UnexpectedPrimitiveTypeArguments="primitive types not expected to have type arguments",f.UnexpectedVectorTypeArgumentCount="vector type expected to have exactly one type argument",f.UnexpectedStructFormat="unexpected struct format, must be of the form 0xaddress::module_name::struct_name",f.InvalidModuleNameCharacter="module name must only contain alphanumeric or '_' characters",f.InvalidStructNameCharacter="struct name must only contain alphanumeric or '_' characters",f.InvalidAddress="struct address must be valid",f))(ta||{}),M=class extends Error{constructor(e,t){super(`Failed to parse typeTag '${e}', ${t}`)}};function be(n,e){let t=e?.allowGenerics??!1,r=[],i=[],o=[],s=0,a="",u=1;for(;s<n.length;){let d=n[s];if(d==="<")r.push({savedExpectedTypes:u,savedStr:a,savedTypes:o}),a="",o=[],u=1;else if(d===">"){if(a!==""){let f=on(a,i,t);o.push(f)}let l=r.pop();if(l===void 0)throw new M(n,"unexpected '>'");if(u!==o.length)throw new M(n,"type argument count doesn't match expected amount");let{savedStr:A,savedTypes:p,savedExpectedTypes:g}=l;i=o,o=p,a=A,u=g}else if(d===","){if(r.length===0)throw new M(n,"unexpected ','");if(a.length===0)throw new M(n,"no type argument before ','");let l=on(a,i,t);i=[],o.push(l),a="",u+=1}else if(eo(d)){let l=!1;if(a.length!==0){let p=on(a,i,t);i=[],o.push(p),a="",l=!0}s=ea(n,s);let A=n[s];if(s<n.length&&l&&A!==","&&A!==">")throw new M(n,"unexpected whitespace character");continue}else a+=d;s+=1}if(r.length>0)throw new M(n,"no matching '>' for '<'");switch(o.length){case 0:return on(a,i,t);case 1:if(a==="")return o[0];throw new M(n,"unexpected ','");default:throw new M(n,"unexpected whitespace character")}}function on(n,e,t){let r=n.trim(),i=r.toLowerCase();if(Xs(i)&&e.length>0)throw new M(n,"primitive types not expected to have type arguments");switch(r.toLowerCase()){case"signer":return new st;case"bool":return new G;case"address":return new D;case"u8":return new oe;case"u16":return new Fe;case"u32":return new Ue;case"u64":return new q;case"u128":return new De;case"u256":return new Re;case"vector":if(e.length!==1)throw new M(n,"vector type expected to have exactly one type argument");return new K(e[0]);default:if(Ys(r)){let a=r.substring(1);return new Un(on(a,e,t))}if(Zs(r)){if(t)return new O(Number(r.split("T")[1]));throw new M(n,"unexpected generic type")}if(!r.match(/:/))throw new M(n,"unknown type");let o=r.split("::");if(o.length!==3)throw new M(n,"unexpected struct format, must be of the form 0xaddress::module_name::struct_name");let s;try{s=c.fromString(o[0])}catch{throw new M(n,"struct address must be valid")}if(!Xi(o[1]))throw new M(n,"module name must only contain alphanumeric or '_' characters");if(!Xi(o[2]))throw new M(n,"struct name must only contain alphanumeric or '_' characters");return new m(new Ee(s,new F(o[1]),new F(o[2]),e))}}function to(n){return typeof n=="boolean"}function Me(n){return typeof n=="string"}function na(n){return typeof n=="number"}function Vn(n){if(na(n))return n;if(Me(n)&&n!=="")return Number.parseInt(n,10)}function Hn(n){return typeof n=="number"||typeof n=="bigint"||typeof n=="string"}function no(n){return n==null}function ro(n){return wr(n)||Sr(n)||Pr(n)||xr(n)||Kr(n)||Er(n)||Cr(n)||Ln(n)||Tr(n)||ra(n)||n instanceof P||n instanceof J}function wr(n){return n instanceof B}function Ln(n){return n instanceof c}function Tr(n){return n instanceof _}function ra(n){return n instanceof Tt}function Sr(n){return n instanceof Te}function Pr(n){return n instanceof St}function xr(n){return n instanceof Pt}function Kr(n){return n instanceof Se}function Er(n){return n instanceof xt}function Cr(n){return n instanceof Kt}function io(n){return"bytecode"in n}function I(n,e){throw new Error(`Type mismatch for argument ${e}, expected '${n}'`)}function oo(n){let e=n.params.findIndex(t=>t!=="signer"&&t!=="&signer");return e<0?n.params.length:e}var ia=new TextEncoder;function qn(n){return n?.map(e=>Me(e)?be(e):e)??[]}async function oa(n,e,t){return(await $n({aptosConfig:t,accountAddress:n,moduleName:e})).abi}async function vr(n,e,t,r){let i=await oa(n,e,r);if(!i)throw new Error(`Could not find module ABI for '${n}::${e}'`);return i.exposed_functions.find(o=>o.name===t)}async function bm(n,e,t,r){let i=await vr(n,e,t,r);if(!i)throw new Error(`Could not find function ABI for '${n}::${e}::${t}'`);let o=[];for(let s=0;s<i.params.length;s+=1)o.push(be(i.params[s],{allowGenerics:!0}));return{typeParameters:i.generic_type_params,parameters:o}}async function so(n,e,t,r){let i=await vr(n,e,t,r);if(!i)throw new Error(`Could not find entry function ABI for '${n}::${e}::${t}'`);if(!i.is_entry)throw new Error(`'${n}::${e}::${t}' is not an entry function`);let o=oo(i),s=[];for(let a=o;a<i.params.length;a+=1)s.push(be(i.params[a],{allowGenerics:!0}));return{signers:o,typeParameters:i.generic_type_params,parameters:s}}async function ao(n,e,t,r){let i=await vr(n,e,t,r);if(!i)throw new Error(`Could not find view function ABI for '${n}::${e}::${t}'`);if(!i.is_view)throw new Error(`'${n}::${e}::${t}' is not an view function`);let o=[];for(let a=0;a<i.params.length;a+=1)o.push(be(i.params[a],{allowGenerics:!0}));let s=[];for(let a=0;a<i.return.length;a+=1)s.push(be(i.return[a],{allowGenerics:!0}));return{typeParameters:i.generic_type_params,parameters:o,returnTypes:s}}function _r(n,e,t,r,i,o){let s;if("exposed_functions"in e){let a=e.exposed_functions.find(u=>u.name===n);if(!a)throw new Error(`Could not find function ABI for '${e.address}::${e.name}::${n}'`);if(r>=a.params.length)throw new Error(`Too many arguments for '${n}', expected ${a.params.length}`);s=be(a.params[r],{allowGenerics:!0})}else{if(r>=e.parameters.length)throw new Error(`Too many arguments for '${n}', expected ${e.parameters.length}`);s=e.parameters[r]}return At(t,s,r,i,"exposed_functions"in e?e:void 0,o)}function At(n,e,t,r,i,o){return ro(n)?(Ir(e,n,t),n):sa(n,e,t,r,i,o)}function sa(n,e,t,r,i,o){if(e.isBool()){if(to(n))return new B(n);if(Me(n)){if(n==="true")return new B(!0);if(n==="false")return new B(!1)}I("boolean",t)}if(e.isAddress()){if(Me(n))return c.fromString(n);I("string | AccountAddress",t)}if(e.isU8()){let s=Vn(n);if(s!==void 0)return new Te(s);I("number | string",t)}if(e.isU16()){let s=Vn(n);if(s!==void 0)return new St(s);I("number | string",t)}if(e.isU32()){let s=Vn(n);if(s!==void 0)return new Pt(s);I("number | string",t)}if(e.isU64()){if(Hn(n))return new Se(BigInt(n));I("bigint | number | string",t)}if(e.isU128()){if(Hn(n))return new xt(BigInt(n));I("bigint | number | string",t)}if(e.isU256()){if(Hn(n))return new Kt(BigInt(n));I("bigint | number | string",t)}if(e.isGeneric()){let s=e.value;if(s<0||s>=r.length)throw new Error(`Generic argument ${e.toString()} is invalid for argument ${t}`);return At(n,r[s],t,r,i)}if(e.isVector()){if(e.value.isU8()){if(Me(n))return P.U8(ia.encode(n));if(n instanceof Uint8Array)return P.U8(n);if(n instanceof ArrayBuffer)return P.U8(new Uint8Array(n))}if(Me(n)&&n.startsWith("["))return At(JSON.parse(n),e,t,r);if(Array.isArray(n))return new P(n.map(s=>At(s,e.value,t,r,i)));throw new Error(`Type mismatch for argument ${t}, type '${e.toString()}'`)}if(e.isStruct()){if(e.isString()){if(Me(n))return new _(n);I("string",t)}if(e.isObject()){if(Me(n))return c.fromString(n);I("string | AccountAddress",t)}if(e.isDelegationKey()||e.isRateLimiter()){if(n instanceof Uint8Array)return new Tt(n);I("Uint8Array",t)}if(e.isOption()){if(no(n)){let a=e.value.typeArgs[0];return a instanceof G?new J(null):a instanceof D?new J(null):a instanceof oe?new J(null):a instanceof Fe?new J(null):a instanceof Ue?new J(null):a instanceof q?new J(null):a instanceof De?new J(null):a instanceof Re?new J(null):new J(null)}return new J(At(n,e.value.typeArgs[0],t,r,i))}if(i?.structs.find(a=>a.name===e.value.name.identifier)?.fields.length===0&&n instanceof Uint8Array)return new Tt(n);if(n instanceof Uint8Array&&o?.allowUnknownStructs)return console.warn(`Unsupported struct input type for argument ${t}. Continuing since 'allowUnknownStructs' is enabled.`),new Tt(n);throw new Error(`Unsupported struct input type for argument ${t}, type '${e.toString()}'`)}throw new Error(`Type mismatch for argument ${t}, type '${e.toString()}'`)}function Ir(n,e,t){if(n.isBool()){if(wr(e))return;I("Bool",t)}if(n.isAddress()){if(Ln(e))return;I("AccountAddress",t)}if(n.isU8()){if(Sr(e))return;I("U8",t)}if(n.isU16()){if(Pr(e))return;I("U16",t)}if(n.isU32()){if(xr(e))return;I("U32",t)}if(n.isU64()){if(Kr(e))return;I("U64",t)}if(n.isU128()){if(Er(e))return;I("U128",t)}if(n.isU256()){if(Cr(e))return;I("U256",t)}if(n.isVector()){if(e instanceof P){e.values.length>0&&Ir(n.value,e.values[0],t);return}I("MoveVector",t)}if(n instanceof m){if(n.isString()){if(Tr(e))return;I("MoveString",t)}if(n.isObject()){if(Ln(e))return;I("AccountAddress",t)}if(n.isOption()){if(e instanceof J){e.value!==void 0&&Ir(n.value.typeArgs[0],e.value,t);return}I("MoveOption",t)}}throw new Error(`Type mismatch for argument ${t}, expected '${n.toString()}'`)}async function Wn(n){if(io(n))return da(n);let{moduleAddress:e,moduleName:t,functionName:r}=ue(n.function),i=await lo({key:"entry-function",moduleAddress:e,moduleName:t,functionName:r,aptosConfig:n.aptosConfig,abi:n.abi,fetch:so});return ca({...n,abi:i})}function ca(n){let e=n.abi,{moduleAddress:t,moduleName:r,functionName:i}=ue(n.function),o=qn(n.typeArguments);if(o.length!==e.typeParameters.length)throw new Error(`Type argument count mismatch, expected ${e.typeParameters.length}, received ${o.length}`);let s=n.functionArguments.map((u,d)=>_r(n.function,e,u,d,o));if(s.length!==e.parameters.length)throw new Error(`Too few arguments for '${t}::${r}::${i}', expected ${e.parameters.length} but got ${s.length}`);let a=ct.build(`${t}::${r}`,i,o,s);if("multisigAddress"in n){let u=c.from(n.multisigAddress);return new Yt(new en(u,new tn(a)))}return new Zt(a)}async function co(n){let{moduleAddress:e,moduleName:t,functionName:r}=ue(n.function),i=await lo({key:"view-function",moduleAddress:e,moduleName:t,functionName:r,aptosConfig:n.aptosConfig,abi:n.abi,fetch:ao});return ua({abi:i,...n})}function ua(n){let e=n.abi,{moduleAddress:t,moduleName:r,functionName:i}=ue(n.function),o=qn(n.typeArguments);if(o.length!==e.typeParameters.length)throw new Error(`Type argument count mismatch, expected ${e.typeParameters.length}, received ${o.length}`);let s=n?.functionArguments?.map((a,u)=>_r(n.function,e,a,u,o))??[];if(s.length!==e.parameters.length)throw new Error(`Too few arguments for '${t}::${r}::${i}', expected ${e.parameters.length} but got ${s.length}`);return ct.build(`${t}::${r}`,i,o,s)}function da(n){return new Jt(new Xt(T.fromHexInput(n.bytecode).toUint8Array(),qn(n.typeArguments),n.functionArguments))}async function pa(n){let{aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o}=n,s=async()=>or[e.network]?{chainId:or[e.network]}:{chainId:(await Cn({aptosConfig:e})).chain_id},a=async()=>i?.gasUnitPrice?{gasEstimate:i.gasUnitPrice}:{gasEstimate:(await vn({aptosConfig:e})).gas_estimate},u=async()=>{let C=async()=>i?.accountSequenceNumber!==void 0?i.accountSequenceNumber:(await In({aptosConfig:e,accountAddress:t})).sequence_number;if(o&&c.from(o).equals(c.ZERO))try{return await C()}catch{return 0}else return C()},[{chainId:d},{gasEstimate:l},A]=await Promise.all([s(),a(),u()]),{maxGasAmount:p,gasUnitPrice:g,expireTimestamp:f}={maxGasAmount:i?.maxGasAmount?BigInt(i.maxGasAmount):BigInt(2e5),gasUnitPrice:i?.gasUnitPrice??BigInt(l),expireTimestamp:i?.expireTimestamp??BigInt(Math.floor(Date.now()/1e3)+20)};return new ge(c.from(t),BigInt(A),r,BigInt(p),BigInt(g),BigInt(f),new Kn(d))}async function kr(n){let{aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o}=n,s=await pa({aptosConfig:e,sender:t,payload:r,options:i,feePayerAddress:o});if("secondarySignerAddresses"in n){let a=n.secondarySignerAddresses?.map(u=>c.from(u))??[];return new On(s,a,n.feePayerAddress?c.from(n.feePayerAddress):void 0)}return new Rn(s,n.feePayerAddress?c.from(n.feePayerAddress):void 0)}function uo(n){let{signerPublicKey:e,transaction:t,secondarySignersPublicKeys:r,feePayerPublicKey:i}=n,o=Bt(e);if(t.feePayerAddress){let a=new dt(t.rawTransaction,t.secondarySignerAddresses??[],t.feePayerAddress),u=[];t.secondarySignerAddresses&&(r?u=r.map(A=>Bt(A)):u=Array.from({length:t.secondarySignerAddresses.length},()=>Bt(void 0)));let d=Bt(i),l=new gt(o,t.secondarySignerAddresses??[],u,{address:t.feePayerAddress,authenticator:d});return new ze(a.raw_txn,l).bcsToBytes()}if(t.secondarySignerAddresses){let a=new ut(t.rawTransaction,t.secondarySignerAddresses),u=[];r?u=r.map(l=>Bt(l)):u=Array.from({length:t.secondarySignerAddresses.length},()=>Bt(void 0));let d=new lt(o,t.secondarySignerAddresses,u);return new ze(a.raw_txn,d).bcsToBytes()}let s;if(o instanceof fe)s=new pt(o.public_key,o.signature);else if(o instanceof X||o instanceof Ke)s=new Ze(o);else if(o instanceof Ut)s=new Ze(o);else throw new Error("Invalid public key");return new ze(t.rawTransaction,s).bcsToBytes()}function Bt(n){if(!n)return new Ut;let t=E.isInstance(n)||N.isInstance(n)||je.isInstance(n)?new v(n):n,r=new z(new Uint8Array(64));if(k.isInstance(t))return new fe(t,r);if(v.isInstance(t))return E.isInstance(t.publicKey)?new X(t,new U(Y.getSimulationSignature())):new X(t,new U(r));if(de.isInstance(t))return new Ke(t,new pe({signatures:t.publicKeys.map(i=>E.isInstance(i.publicKey)||N.isInstance(i.publicKey)?new U(Y.getSimulationSignature()):new U(r)),bitmap:t.createBitmap({bits:Array(t.publicKeys.length).fill(0).map((i,o)=>o)})}));throw new Error("Unsupported PublicKey used for simulations")}function Fr(n){let{transaction:e,feePayerAuthenticator:t,additionalSignersAuthenticators:r}=n,i=oi(L,n.senderAuthenticator),o;if(e.feePayerAddress){if(!t)throw new Error("Must provide a feePayerAuthenticator argument to generate a signed fee payer transaction");o=new gt(i,e.secondarySignerAddresses??[],r??[],{address:e.feePayerAddress,authenticator:t})}else if(e.secondarySignerAddresses){if(!r)throw new Error("Must provide a additionalSignersAuthenticators argument to generate a signed multi agent transaction");o=new lt(i,e.secondarySignerAddresses,r)}else i instanceof fe?o=new pt(i.public_key,i.signature):i instanceof ke?o=new Dt(i.public_key,i.signature):o=new Ze(i);return new ze(e.rawTransaction,o).bcsToBytes()}function po(n){let e=aa.create();for(let t of n)e.update(t);return e.digest()}var la=po(["APTOS::Transaction"]);function Lm(n){let e=Fr(n);return new T(po([la,new Uint8Array([0]),e])).toString()}async function lo({key:n,moduleAddress:e,moduleName:t,functionName:r,aptosConfig:i,abi:o,fetch:s}){return o!==void 0?o:kt(async()=>s(e,t,r,i),`${n}-${i.network}-${e}-${t}-${r}`,1e3*60*5)()}async function y(n){let e=await ma(n);return ya(n,e)}async function ma(n){let{aptosConfig:e,data:t}=n,r,i;return"bytecode"in t?i=await Wn(t):"multisigAddress"in t?(r={aptosConfig:e,multisigAddress:t.multisigAddress,function:t.function,functionArguments:t.functionArguments,typeArguments:t.typeArguments,abi:t.abi},i=await Wn(r)):(r={aptosConfig:e,function:t.function,functionArguments:t.functionArguments,typeArguments:t.typeArguments,abi:t.abi},i=await Wn(r)),i}async function ya(n,e){let{aptosConfig:t,sender:r,options:i}=n,o;if(Aa(n)&&(o=c.ZERO.toString()),fa(n)){let{secondarySignerAddresses:s}=n;return kr({aptosConfig:t,sender:r,payload:e,options:i,secondarySignerAddresses:s,feePayerAddress:o})}return kr({aptosConfig:t,sender:r,payload:e,options:i,feePayerAddress:o})}function Aa(n){return n.withFeePayer===!0}function fa(n){return"secondarySignerAddresses"in n}function go(n){let{transaction:e}=n;return he(e)}function jn(n){let{signer:e,transaction:t}=n;return e.signTransactionWithAuthenticator(t)}function Qn(n){let{signer:e,transaction:t}=n;if(!t.feePayerAddress)throw new Error(`Transaction ${t} is not a Fee Payer transaction`);return t.feePayerAddress=e.accountAddress,jn({signer:e,transaction:t})}async function Ur(n){let{aptosConfig:e,transaction:t,signerPublicKey:r,secondarySignersPublicKeys:i,feePayerPublicKey:o,options:s}=n,a=uo({transaction:t,signerPublicKey:r,secondarySignersPublicKeys:i,feePayerPublicKey:o,options:s}),{data:u}=await _t({aptosConfig:e,body:a,path:"transactions/simulate",params:{estimate_gas_unit_price:n.options?.estimateGasUnitPrice??!1,estimate_max_gas_amount:n.options?.estimateMaxGasAmount??!1,estimate_prioritized_gas_unit_price:n.options?.estimatePrioritizedGasUnitPrice??!1},originMethod:"simulateTransaction",contentType:"application/x.aptos.signed_transaction+bcs"});return u}async function sn(n){let{aptosConfig:e}=n,t=Fr({...n});try{let{data:r}=await _t({aptosConfig:e,body:t,path:"transactions",originMethod:"submitTransaction",contentType:"application/x.aptos.signed_transaction+bcs"});return r}catch(r){let i=ze.deserialize(new H(t));throw i.authenticator.isSingleSender()&&i.authenticator.sender.isSingleKey()&&(i.authenticator.sender.public_key.publicKey instanceof E||i.authenticator.sender.public_key.publicKey instanceof N)&&await ce.fetchJWK({aptosConfig:e,publicKey:i.authenticator.sender.public_key.publicKey,kid:i.authenticator.sender.signature.signature.getJwkKid()}),r}}async function Xe(n){let{aptosConfig:e,signer:t,feePayer:r,transaction:i}=n;Bn(t)&&await t.checkKeylessAccountValidity(e),Bn(r)&&await r.checkKeylessAccountValidity(e);let o=n.feePayerAuthenticator||r&&Qn({signer:r,transaction:i}),s=jn({signer:t,transaction:i});return sn({aptosConfig:e,transaction:i,senderAuthenticator:s,feePayerAuthenticator:o})}async function mo(n){let{aptosConfig:e,senderAuthenticator:t,feePayer:r,transaction:i}=n;Bn(r)&&await r.checkKeylessAccountValidity(e);let o=Qn({signer:r,transaction:i});return sn({aptosConfig:e,transaction:i,senderAuthenticator:t,feePayerAuthenticator:o})}var ha={typeParameters:[],parameters:[K.u8(),new K(K.u8())]};async function yo(n){let{aptosConfig:e,account:t,metadataBytes:r,moduleBytecode:i,options:o}=n,s=i.map(a=>P.U8(a));return y({aptosConfig:e,sender:c.from(t),data:{function:"0x1::code::publish_package_txn",functionArguments:[P.U8(r),new P(s)],abi:ha},options:o})}async function ft(n){return In(n)}async function fo(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return ar({aptosConfig:e,originMethod:"getModules",path:`accounts/${c.from(t).toString()}/modules`,params:{ledger_version:r?.ledgerVersion,limit:r?.limit??1e3}})}async function ho(n){let{aptosConfig:e,accountAddress:t,options:r}=n,{response:i,cursor:o}=await cr({aptosConfig:e,originMethod:"getModulesPage",path:`accounts/${c.from(t).toString()}/modules`,params:{ledger_version:r?.ledgerVersion,cursor:r?.cursor,limit:r?.limit??100}});return{modules:i.data,cursor:o}}async function $n(n){return Oi(n)}async function bo(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return gi({aptosConfig:e,originMethod:"getTransactions",path:`accounts/${c.from(t).toString()}/transactions`,params:{start:r?.offset,limit:r?.limit}})}async function wo(n){let{aptosConfig:e,accountAddress:t,options:r}=n;return ar({aptosConfig:e,originMethod:"getResources",path:`accounts/${c.from(t).toString()}/resources`,params:{ledger_version:r?.ledgerVersion,limit:r?.limit??999}})}async function To(n){let{aptosConfig:e,accountAddress:t,options:r}=n,{response:i,cursor:o}=await cr({aptosConfig:e,originMethod:"getResourcesPage",path:`accounts/${c.from(t).toString()}/resources`,params:{ledger_version:r?.ledgerVersion,cursor:r?.cursor,limit:r?.limit??100}});return{resources:i.data,cursor:o}}async function Rr(n){let{aptosConfig:e,accountAddress:t,resourceType:r,options:i}=n,{data:o}=await rt({aptosConfig:e,originMethod:"getResource",path:`accounts/${c.from(t).toString()}/resource/${r}`,params:{ledger_version:i?.ledgerVersion}});return o.data}async function Nt(n){let{aptosConfig:e,authenticationKey:t,options:r}=n,i=await Rr({aptosConfig:e,accountAddress:"0x1",resourceType:"0x1::account::OriginatingAddress",options:r}),{address_map:{handle:o}}=i,s=c.from(t);try{let a=await zi({aptosConfig:e,handle:o,data:{key:s.toString(),key_type:"address",value_type:"address"},options:r});return c.from(a)}catch(a){if(a instanceof li&&a.data.error_code==="table_item_not_found")return s;throw a}}async function So(n){let{aptosConfig:e,accountAddress:t}=n,i={owner_address:{_eq:c.from(t).toStringLong()},amount:{_gt:0}},s=await w({aptosConfig:e,query:{query:Pi,variables:{where_condition:i}},originMethod:"getAccountTokensCount"});return s.current_token_ownerships_v2_aggregate.aggregate?s.current_token_ownerships_v2_aggregate.aggregate.count:0}async function Po(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:c.from(t).toStringLong()},amount:{_gt:0}};r?.tokenStandard&&(o.token_standard={_eq:r?.tokenStandard});let s={query:Ti,variables:{where_condition:o,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountOwnedTokens"})).current_token_ownerships_v2}async function xo(n){let{aptosConfig:e,accountAddress:t,collectionAddress:r,options:i}=n,o=c.from(t).toStringLong(),s=c.from(r).toStringLong(),a={owner_address:{_eq:o},current_token_data:{collection_id:{_eq:s}},amount:{_gt:0}};i?.tokenStandard&&(a.token_standard={_eq:i?.tokenStandard});let u={query:Si,variables:{where_condition:a,offset:i?.offset,limit:i?.limit,order_by:i?.orderBy}};return(await w({aptosConfig:e,query:u,originMethod:"getAccountOwnedTokensFromCollectionAddress"})).current_token_ownerships_v2}async function Ko(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:c.from(t).toStringLong()}};r?.tokenStandard&&(o.current_collection={token_standard:{_eq:r?.tokenStandard}});let s={query:wi,variables:{where_condition:o,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountCollectionsWithOwnedTokens"})).current_collection_ownership_v2_view}async function Eo(n){let{aptosConfig:e,accountAddress:t}=n,r=c.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:xi,variables:{address:r}},originMethod:"getAccountTransactionsCount"});return o.account_transactions_aggregate.aggregate?o.account_transactions_aggregate.aggregate.count:0}async function My(n){let{aptosConfig:e,accountAddress:t,coinType:r,faMetadataAddress:i}=n,o=r,s;if(r!==void 0&&i!==void 0)s=c.from(i).toStringLong();else if(r!==void 0&&i===void 0)r===ve?s=c.A.toStringLong():s=xn(c.A,r).toStringLong();else if(r===void 0&&i!==void 0){let l=c.from(i);s=l.toStringLong(),l===c.A&&(o=ve)}else throw new Error("Either coinType, fungibleAssetAddress, or both must be provided");let a=c.from(t).toStringLong(),u={asset_type:{_eq:s}};o!==void 0&&(u={asset_type:{_in:[o,s]}});let d=await Or({aptosConfig:e,accountAddress:a,options:{where:u}});return d[0]?d[0].amount:0}async function Or(n){let{aptosConfig:e,accountAddress:t,options:r}=n,i=c.from(t).toStringLong(),o={...r?.where,owner_address:{_eq:i}},s={query:bi,variables:{where_condition:o,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountCoinsData"})).current_fungible_asset_balances}async function Co(n){let{aptosConfig:e,accountAddress:t}=n,r=c.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:hi,variables:{address:r}},originMethod:"getAccountCoinsCount"});if(!o.current_fungible_asset_balances_aggregate.aggregate)throw Error("Failed to get the count of account coins");return o.current_fungible_asset_balances_aggregate.aggregate.count}async function Io(n){let{aptosConfig:e,accountAddress:t,options:r}=n,o={owner_address:{_eq:c.from(t).toStringLong()}},s={query:En,variables:{where_condition:o,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:s,originMethod:"getAccountOwnedObjects"})).current_objects}async function vo(n){let{aptosConfig:e,privateKey:t}=n,r=new v(t.publicKey());if(t instanceof it){let o=Z.fromPublicKey({publicKey:r}).derivedAddress();return Ge.fromPrivateKey({privateKey:t,address:o})}if(t instanceof $){let i=Z.fromPublicKey({publicKey:r.publicKey});if(await Ao({authKey:i,aptosConfig:e})){let u=i.derivedAddress();return Ge.fromPrivateKey({privateKey:t,address:u,legacy:!0})}let s=Z.fromPublicKey({publicKey:r});if(await Ao({authKey:s,aptosConfig:e})){let u=s.derivedAddress();return Ge.fromPrivateKey({privateKey:t,address:u,legacy:!1})}}throw new Error(`Can't derive account from private key ${t}`)}async function Ao(n){let{aptosConfig:e,authKey:t}=n,r=await Nt({aptosConfig:e,authenticationKey:t.derivedAddress()});try{return await ft({aptosConfig:e,accountAddress:r}),!0}catch(i){if(i.status===404)return!1;throw new Error(`Error while looking for an account info ${r.toString()}`)}}var ba={typeParameters:[],parameters:[new oe,K.u8(),new oe,K.u8(),K.u8(),K.u8()]};async function _o(n){let{aptosConfig:e,fromAccount:t,dangerouslySkipVerification:r}=n;if("toNewPrivateKey"in n)return Dr({aptosConfig:e,fromAccount:t,toNewPrivateKey:n.toNewPrivateKey});let i;if("toAccount"in n){if(n.toAccount instanceof ee)return Dr({aptosConfig:e,fromAccount:t,toNewPrivateKey:n.toAccount.privateKey});if(n.toAccount instanceof Nn)return Dr({aptosConfig:e,fromAccount:t,toAccount:n.toAccount});i=n.toAccount.publicKey.authKey()}else if("toAuthKey"in n)i=n.toAuthKey;else throw new Error("Invalid arguments");let o=await Ta({aptosConfig:e,fromAccount:t,toAuthKey:i});if(r===!0)return o;let s=await Qe({aptosConfig:e,transactionHash:o.hash});if(!s.success)throw new Error(`Failed to rotate authentication key - ${s}`);let a=await y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::set_originating_address",functionArguments:[]}});return Xe({aptosConfig:e,signer:n.toAccount,transaction:a})}async function Dr(n){let{aptosConfig:e,fromAccount:t}=n,r=await ft({aptosConfig:e,accountAddress:t.accountAddress}),i;"toNewPrivateKey"in n?i=Ge.fromPrivateKey({privateKey:n.toNewPrivateKey,legacy:!0}):i=n.toAccount;let s=new fi({sequenceNumber:BigInt(r.sequence_number),originator:t.accountAddress,currentAuthKey:c.from(r.authentication_key),newPublicKey:i.publicKey}).bcsToBytes(),a=t.sign(s),u=i.sign(s),d=await y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::rotate_authentication_key",functionArguments:[new Te(t.signingScheme),P.U8(t.publicKey.toUint8Array()),new Te(i.signingScheme),P.U8(i.publicKey.toUint8Array()),P.U8(a.toUint8Array()),P.U8(u.toUint8Array())],abi:ba}});return Xe({aptosConfig:e,signer:t,transaction:d})}var wa={typeParameters:[],parameters:[K.u8()]};async function Ta(n){let{aptosConfig:e,fromAccount:t,toAuthKey:r}=n,i=r,o=await y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::account::rotate_authentication_key_call",functionArguments:[P.U8(i.toUint8Array())],abi:wa}});return Xe({aptosConfig:e,signer:t,transaction:o})}async function te(n){let{aptosConfig:e,payload:t,options:r}=n,i=await co({...t,aptosConfig:e}),o=new j;i.serialize(o);let s=o.toUint8Array(),{data:a}=await _t({aptosConfig:e,path:"view",originMethod:"view",contentType:"application/x.aptos.view_function+bcs",params:{ledger_version:r?.ledgerVersion},body:s});return a}async function ko(n){let{aptosConfig:e,payload:t,options:r}=n,{data:i}=await _t({aptosConfig:e,originMethod:"viewJson",path:"view",params:{ledger_version:r?.ledgerVersion},body:{function:t.function,type_arguments:t.typeArguments??[],arguments:t.functionArguments??[]}});return i}async function Fo(n){let{aptosConfig:e,sender:t,authenticationFunction:r,options:i}=n,{moduleAddress:o,moduleName:s,functionName:a}=ue(r);return y({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::add_authentication_function",typeArguments:[],functionArguments:[o,s,a],abi:{typeParameters:[],parameters:[new D,new m(x()),new m(x())]}},options:i})}async function Uo(n){let{aptosConfig:e,sender:t,authenticationFunction:r,options:i}=n,{moduleAddress:o,moduleName:s,functionName:a}=ue(r);return y({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::remove_authentication_function",typeArguments:[],functionArguments:[o,s,a],abi:{typeParameters:[],parameters:[new D,new m(x()),new m(x())]}},options:i})}async function Do(n){let{aptosConfig:e,sender:t,options:r}=n;return y({aptosConfig:e,sender:t,data:{function:"0x1::account_abstraction::remove_authenticator",typeArguments:[],functionArguments:[],abi:{typeParameters:[],parameters:[]}},options:r})}var ht=class{constructor(e){this.config=e;this.isAccountAbstractionEnabled=async e=>{let t=await this.getAuthenticationFunction(e),{moduleAddress:r,moduleName:i,functionName:o}=ue(e.authenticationFunction);return t?.some(s=>c.fromString(r).equals(s.moduleAddress)&&i===s.moduleName&&o===s.functionName)??!1};this.enableAccountAbstractionTransaction=this.addAuthenticationFunctionTransaction;this.disableAccountAbstractionTransaction=async e=>{let{accountAddress:t,authenticationFunction:r,options:i}=e;return r?this.removeAuthenticationFunctionTransaction({accountAddress:t,authenticationFunction:r,options:i}):this.removeDispatchableAuthenticatorTransaction({accountAddress:t,options:i})}}async addAuthenticationFunctionTransaction(e){let{accountAddress:t,authenticationFunction:r,options:i}=e;return Fo({aptosConfig:this.config,authenticationFunction:r,sender:t,options:i})}async removeAuthenticationFunctionTransaction(e){let{accountAddress:t,authenticationFunction:r,options:i}=e;return Uo({aptosConfig:this.config,sender:t,authenticationFunction:r,options:i})}async removeDispatchableAuthenticatorTransaction(e){let{accountAddress:t,options:r}=e;return Do({aptosConfig:this.config,sender:t,options:r})}async getAuthenticationFunction(e){let{accountAddress:t}=e,[{vec:r}]=await te({aptosConfig:this.config,payload:{function:"0x1::account_abstraction::dispatchable_authenticator",functionArguments:[c.from(t)],abi:{typeParameters:[],parameters:[new D],returnTypes:[]}}});if(r.length!==0)return r[0].map(i=>({moduleAddress:c.fromString(i.module_address),moduleName:i.module_name,functionName:i.function_name}))}};var an=class{constructor(e){this.config=e;this.abstraction=new ht(e)}async getAccountInfo(e){return ft({aptosConfig:this.config,...e})}async getAccountModules(e){return fo({aptosConfig:this.config,...e})}async getAccountModulesPage(e){return ho({aptosConfig:this.config,...e})}async getAccountModule(e){return $n({aptosConfig:this.config,...e})}async getAccountTransactions(e){return bo({aptosConfig:this.config,...e})}async getAccountResources(e){return wo({aptosConfig:this.config,...e})}async getAccountResourcesPage(e){return To({aptosConfig:this.config,...e})}async getAccountResource(e){return Rr({aptosConfig:this.config,...e})}async lookupOriginalAccountAddress(e){return Nt({aptosConfig:this.config,...e})}async getAccountTokensCount(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"account_transactions_processor"}),So({aptosConfig:this.config,...e})}async getAccountOwnedTokens(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Po({aptosConfig:this.config,...e})}async getAccountOwnedTokensFromCollectionAddress(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),xo({aptosConfig:this.config,...e})}async getAccountCollectionsWithOwnedTokens(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Ko({aptosConfig:this.config,...e})}async getAccountTransactionsCount(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"account_transactions_processor"}),Eo({aptosConfig:this.config,...e})}async getAccountCoinsData(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"fungible_asset_processor"}),Or({aptosConfig:this.config,...e})}async getAccountCoinsCount(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"fungible_asset_processor"}),Co({aptosConfig:this.config,...e})}async getAccountAPTAmount(e){return this.getAccountCoinAmount({coinType:ve,faMetadataAddress:ni,...e})}async getAccountCoinAmount(e){let{accountAddress:t,coinType:r,faMetadataAddress:i,minimumLedgerVersion:o}=e;o&&console.warn(`minimumLedgerVersion is not used anymore, here for backward 
        compatibility see https://github.com/aptos-labs/aptos-ts-sdk/pull/519, 
        will be removed in the near future`);let s=r;r===void 0&&i!==void 0&&(s=await kt(async()=>{try{let d=(await te({aptosConfig:this.config,payload:{function:"0x1::coin::paired_coin",functionArguments:[i]}})).at(0);if(d.vec.length>0&&pi(d.vec[0]))return di(d.vec[0])}catch{}},`coin-mapping-${i.toString()}`,1e3*60*5)());let a;if(r!==void 0&&i!==void 0)a=c.from(i).toStringLong();else if(r!==void 0&&i===void 0)r===ve?a=c.A.toStringLong():a=xn(c.A,r).toStringLong();else if(r===void 0&&i!==void 0){let d=c.from(i);a=d.toStringLong(),d===c.A&&(s=ve)}else throw new Error("Either coinType, faMetadataAddress, or both must be provided");if(s!==void 0){let[d]=await te({aptosConfig:this.config,payload:{function:"0x1::coin::balance",typeArguments:[s],functionArguments:[t]}});return parseInt(d,10)}let[u]=await te({aptosConfig:this.config,payload:{function:"0x1::primary_fungible_store::balance",typeArguments:["0x1::object::ObjectCore"],functionArguments:[t,a]}});return parseInt(u,10)}async getAccountOwnedObjects(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"default_processor"}),Io({aptosConfig:this.config,...e})}async deriveAccountFromPrivateKey(e){return vo({aptosConfig:this.config,...e})}};var Sa={typeParameters:[{constraints:[]}],parameters:[new D,new q]};async function Ro(n){let{aptosConfig:e,sender:t,recipient:r,amount:i,coinType:o,options:s}=n;return y({aptosConfig:e,sender:t,data:{function:"0x1::aptos_account::transfer_coins",typeArguments:[o??ve],functionArguments:[r,i],abi:Sa},options:s})}var cn=class{constructor(e){this.config=e}async transferCoinTransaction(e){return Ro({aptosConfig:this.config,...e})}};var et={BOOLEAN:"bool",U8:"u8",U16:"u16",U32:"u32",U64:"u64",U128:"u128",U256:"u256",ADDRESS:"address",STRING:"0x1::string::String",ARRAY:"vector<u8>"},ye="0x4::token::Token";async function Oo(n){let{aptosConfig:e,digitalAssetAddress:t}=n,r={token_data_id:{_eq:c.from(t).toStringLong()}};return(await w({aptosConfig:e,query:{query:Fi,variables:{where_condition:r}},originMethod:"getDigitalAssetData"})).current_token_datas_v2[0]}async function zo(n){let{aptosConfig:e,digitalAssetAddress:t}=n,r={token_data_id:{_eq:c.from(t).toStringLong()},amount:{_gt:0}};return(await w({aptosConfig:e,query:{query:dr,variables:{where_condition:r}},originMethod:"getCurrentDigitalAssetOwnership"})).current_token_ownerships_v2[0]}async function Go(n){let{aptosConfig:e,ownerAddress:t,options:r}=n,i={owner_address:{_eq:c.from(t).toStringLong()},amount:{_gt:0}},o={query:dr,variables:{where_condition:i,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:o,originMethod:"getOwnedDigitalAssets"})).current_token_ownerships_v2}async function Bo(n){let{aptosConfig:e,digitalAssetAddress:t,options:r}=n,i={token_data_id:{_eq:c.from(t).toStringLong()}},o={query:ki,variables:{where_condition:i,offset:r?.offset,limit:r?.limit,order_by:r?.orderBy}};return(await w({aptosConfig:e,query:o,originMethod:"getDigitalAssetActivity"})).token_activities_v2}var Pa={typeParameters:[],parameters:[new m(x()),new q,new m(x()),new m(x()),new G,new G,new G,new G,new G,new G,new G,new G,new G,new q,new q]};async function No(n){let{aptosConfig:e,options:t,creator:r}=n;return y({aptosConfig:e,sender:r.accountAddress,data:{function:"0x4::aptos_token::create_collection",functionArguments:[new _(n.description),new Se(n.maxSupply??Wr),new _(n.name),new _(n.uri),new B(n.mutableDescription??!0),new B(n.mutableRoyalty??!0),new B(n.mutableURI??!0),new B(n.mutableTokenDescription??!0),new B(n.mutableTokenName??!0),new B(n.mutableTokenProperties??!0),new B(n.mutableTokenURI??!0),new B(n.tokensBurnableByCreator??!0),new B(n.tokensFreezableByCreator??!0),new Se(n.royaltyNumerator??0),new Se(n.royaltyDenominator??1)],abi:Pa},options:t})}async function Vt(n){let{aptosConfig:e,options:t}=n,r=t?.where;t?.tokenStandard&&(r.token_standard={_eq:t?.tokenStandard??"v2"});let i={query:Ki,variables:{where_condition:r,offset:t?.offset,limit:t?.limit}};return(await w({aptosConfig:e,query:i,originMethod:"getCollectionData"})).current_collections_v2[0]}async function Mo(n){let{aptosConfig:e,creatorAddress:t,collectionName:r,options:i}=n,o=c.from(t),s={collection_name:{_eq:r},creator_address:{_eq:o.toStringLong()}};return i?.tokenStandard&&(s.token_standard={_eq:i?.tokenStandard??"v2"}),Vt({aptosConfig:e,options:{...i,where:s}})}async function Vo(n){let{aptosConfig:e,creatorAddress:t,options:r}=n,o={creator_address:{_eq:c.from(t).toStringLong()}};return r?.tokenStandard&&(o.token_standard={_eq:r?.tokenStandard??"v2"}),Vt({aptosConfig:e,options:{...r,where:o}})}async function Ho(n){let{aptosConfig:e,collectionId:t,options:r}=n,o={collection_id:{_eq:c.from(t).toStringLong()}};return r?.tokenStandard&&(o.token_standard={_eq:r?.tokenStandard??"v2"}),Vt({aptosConfig:e,options:{...r,where:o}})}async function Lo(n){let{creatorAddress:e,collectionName:t,options:r,aptosConfig:i}=n,o=c.from(e),s={collection_name:{_eq:t},creator_address:{_eq:o.toStringLong()}};return r?.tokenStandard&&(s.token_standard={_eq:r?.tokenStandard??"v2"}),(await Vt({aptosConfig:i,options:{where:s}})).collection_id}var xa={typeParameters:[],parameters:[new m(x()),new m(x()),new m(x()),new m(x()),new K(new m(x())),new K(new m(x())),new K(K.u8())]};async function qo(n){let{aptosConfig:e,options:t,creator:r,collection:i,description:o,name:s,uri:a,propertyKeys:u,propertyTypes:d,propertyValues:l}=n,A=d?.map(p=>et[p]);return y({aptosConfig:e,sender:r.accountAddress,data:{function:"0x4::aptos_token::mint",functionArguments:[new _(i),new _(o),new _(s),new _(a),P.MoveString(u??[]),P.MoveString(A??[]),os(l??[],A??[])],abi:xa},options:t})}var Ka={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new D]};async function $o(n){let{aptosConfig:e,sender:t,digitalAssetAddress:r,recipient:i,digitalAssetType:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::object::transfer",typeArguments:[o??ye],functionArguments:[c.from(r),c.from(i)],abi:Ka},options:s})}var Ea={typeParameters:[],parameters:[new m(x()),new m(x()),new m(x()),new m(x()),new K(new m(x())),new K(new m(x())),new K(K.u8()),new D]};async function Wo(n){let{aptosConfig:e,account:t,collection:r,description:i,name:o,uri:s,recipient:a,propertyKeys:u,propertyTypes:d,propertyValues:l,options:A}=n;if(u?.length!==l?.length)throw new Error("Property keys and property values counts do not match");if(d?.length!==l?.length)throw new Error("Property types and property values counts do not match");let p=d?.map(g=>et[g]);return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::mint_soul_bound",functionArguments:[r,i,o,s,P.MoveString(u??[]),P.MoveString(p??[]),os(l??[],p??[]),a],abi:Ea},options:A})}var Ca={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0)))]};async function jo(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::burn",typeArguments:[i??ye],functionArguments:[c.from(r)],abi:Ca},options:o})}var Ia={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0)))]};async function Qo(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::freeze_transfer",typeArguments:[i??ye],functionArguments:[r],abi:Ia},options:o})}var va={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0)))]};async function Jo(n){let{aptosConfig:e,creator:t,digitalAssetAddress:r,digitalAssetType:i,options:o}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::unfreeze_transfer",typeArguments:[i??ye],functionArguments:[r],abi:va},options:o})}var _a={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x())]};async function Zo(n){let{aptosConfig:e,creator:t,description:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_description",typeArguments:[o??ye],functionArguments:[c.from(i),new _(r)],abi:_a},options:s})}var ka={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x())]};async function Yo(n){let{aptosConfig:e,creator:t,name:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_name",typeArguments:[o??ye],functionArguments:[c.from(i),new _(r)],abi:ka},options:s})}var Fa={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x())]};async function Xo(n){let{aptosConfig:e,creator:t,uri:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::set_uri",typeArguments:[o??ye],functionArguments:[c.from(i),new _(r)],abi:Fa},options:s})}var Ua={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x()),new m(x()),K.u8()]};async function es(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:u}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::add_property",typeArguments:[a??ye],functionArguments:[c.from(s),new _(r),new _(et[i]),P.U8(zr(o,et[i]))],abi:Ua},options:u})}var Da={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x())]};async function ts(n){let{aptosConfig:e,creator:t,propertyKey:r,digitalAssetAddress:i,digitalAssetType:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::remove_property",typeArguments:[o??ye],functionArguments:[c.from(i),new _(r)],abi:Da},options:s})}var Ra={typeParameters:[{constraints:["key"]}],parameters:[new m(se(new O(0))),new m(x()),new m(x()),K.u8()]};async function ns(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:u}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::update_property",typeArguments:[a??ye],functionArguments:[c.from(s),new _(r),new _(et[i]),zr(o,et[i])],abi:Ra},options:u})}var Oa={typeParameters:[{constraints:["key"]},{constraints:[]}],parameters:[new m(se(new O(0))),new m(x()),new O(1)]};async function rs(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:u}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::add_typed_property",typeArguments:[a??ye,et[i]],functionArguments:[c.from(s),new _(r),o],abi:Oa},options:u})}var za={typeParameters:[{constraints:["key"]},{constraints:[]}],parameters:[new m(se(new O(0))),new m(x()),new O(1)]};async function is(n){let{aptosConfig:e,creator:t,propertyKey:r,propertyType:i,propertyValue:o,digitalAssetAddress:s,digitalAssetType:a,options:u}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x4::aptos_token::update_typed_property",typeArguments:[a??ye,et[i]],functionArguments:[c.from(s),new _(r),o],abi:za},options:u})}function os(n,e){let t=new Array;return e.forEach((r,i)=>{t.push(zr(n[i],r))}),t}function zr(n,e){let t=be(e);return At(n,t,0,[]).bcsToBytes()}var un=class{constructor(e){this.config=e}async getCollectionData(e){await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"});let{creatorAddress:t,collectionName:r,options:i}=e,o=c.from(t),s={collection_name:{_eq:r},creator_address:{_eq:o.toStringLong()}};return i?.tokenStandard&&(s.token_standard={_eq:i?.tokenStandard??"v2"}),Vt({aptosConfig:this.config,options:{where:s}})}async getCollectionDataByCreatorAddressAndCollectionName(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Mo({aptosConfig:this.config,...e})}async getCollectionDataByCreatorAddress(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Vo({aptosConfig:this.config,...e})}async getCollectionDataByCollectionId(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Ho({aptosConfig:this.config,...e})}async getCollectionId(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Lo({aptosConfig:this.config,...e})}async getDigitalAssetData(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Oo({aptosConfig:this.config,...e})}async getCurrentDigitalAssetOwnership(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),zo({aptosConfig:this.config,...e})}async getOwnedDigitalAssets(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Go({aptosConfig:this.config,...e})}async getDigitalAssetActivity(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"token_v2_processor"}),Bo({aptosConfig:this.config,...e})}async createCollectionTransaction(e){return No({aptosConfig:this.config,...e})}async mintDigitalAssetTransaction(e){return qo({aptosConfig:this.config,...e})}async transferDigitalAssetTransaction(e){return $o({aptosConfig:this.config,...e})}async mintSoulBoundTransaction(e){return Wo({aptosConfig:this.config,...e})}async burnDigitalAssetTransaction(e){return jo({aptosConfig:this.config,...e})}async freezeDigitalAssetTransaferTransaction(e){return Qo({aptosConfig:this.config,...e})}async unfreezeDigitalAssetTransaferTransaction(e){return Jo({aptosConfig:this.config,...e})}async setDigitalAssetDescriptionTransaction(e){return Zo({aptosConfig:this.config,...e})}async setDigitalAssetNameTransaction(e){return Yo({aptosConfig:this.config,...e})}async setDigitalAssetURITransaction(e){return Xo({aptosConfig:this.config,...e})}async addDigitalAssetPropertyTransaction(e){return es({aptosConfig:this.config,...e})}async removeDigitalAssetPropertyTransaction(e){return ts({aptosConfig:this.config,...e})}async updateDigitalAssetPropertyTransaction(e){return ns({aptosConfig:this.config,...e})}async addDigitalAssetTypedPropertyTransaction(e){return rs({aptosConfig:this.config,...e})}async updateDigitalAssetTypedPropertyTransaction(e){return is({aptosConfig:this.config,...e})}};var ss=300,Ga=n=>{if(n&&n.length>ss)throw new Error(`Event type length exceeds the maximum length of ${ss}`)};async function as(n){let{aptosConfig:e,eventType:t,options:r}=n,i={_or:[{account_address:{_eq:t.split("::")[0]}},{account_address:{_eq:"0x0000000000000000000000000000000000000000000000000000000000000000"},sequence_number:{_eq:0},creation_number:{_eq:0}}],indexed_type:{_eq:t}};return dn({aptosConfig:e,options:{...r,where:i}})}async function cs(n){let{accountAddress:e,aptosConfig:t,creationNumber:r,options:i}=n,s={account_address:{_eq:c.from(e).toStringLong()},creation_number:{_eq:r}};return dn({aptosConfig:t,options:{...i,where:s}})}async function us(n){let{accountAddress:e,aptosConfig:t,eventType:r,options:i}=n,s={account_address:{_eq:c.from(e).toStringLong()},indexed_type:{_eq:r}};return dn({aptosConfig:t,options:{...i,where:s}})}async function dn(n){let{aptosConfig:e,options:t}=n;Ga(t?.where?.indexed_type?._eq);let r={query:Ii,variables:{where_condition:t?.where,offset:t?.offset,limit:t?.limit,order_by:t?.orderBy}};return(await w({aptosConfig:e,query:r,originMethod:"getEvents"})).events}var pn=class{constructor(e){this.config=e}async getModuleEventsByEventType(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),as({aptosConfig:this.config,...e})}async getAccountEventsByCreationNumber(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),cs({aptosConfig:this.config,...e})}async getAccountEventsByEventType(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"events_processor"}),us({aptosConfig:this.config,...e})}async getEvents(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"events_processor"}),dn({aptosConfig:this.config,...e})}};async function ds(n){let{aptosConfig:e,accountAddress:t,amount:r,options:i}=n,o=i?.timeoutSecs||20,{data:s}=await mi({aptosConfig:e,path:"fund",body:{address:c.from(t).toString(),amount:r},originMethod:"fundAccount"}),a=s.txn_hashes[0],u=await Qe({aptosConfig:e,transactionHash:a,options:{timeoutSecs:o,checkSuccess:i?.checkSuccess}});if(u.type==="user_transaction")return u;throw new Error(`Unexpected transaction received for fund account: ${u.type}`)}var ln=class{constructor(e){this.config=e}async fundAccount(e){let t=await ds({aptosConfig:this.config,...e});return(e.options?.waitForIndexer===void 0||e.options?.waitForIndexer)&&await Vi({aptosConfig:this.config,minimumLedgerVersion:BigInt(t.version),processorType:"fungible_asset_processor"}),t}};async function Jn(n){let{aptosConfig:e,options:t}=n,r={query:_i,variables:{where_condition:t?.where,limit:t?.limit,offset:t?.offset}};return(await w({aptosConfig:e,query:r,originMethod:"getFungibleAssetMetadata"})).fungible_asset_metadata}async function ps(n){let{aptosConfig:e,options:t}=n,r={query:vi,variables:{where_condition:t?.where,limit:t?.limit,offset:t?.offset}};return(await w({aptosConfig:e,query:r,originMethod:"getFungibleAssetActivities"})).fungible_asset_activities}async function ls(n){let{aptosConfig:e,options:t}=n,r={query:Ei,variables:{where_condition:t?.where,limit:t?.limit,offset:t?.offset}};return(await w({aptosConfig:e,query:r,originMethod:"getCurrentFungibleAssetBalances"})).current_fungible_asset_balances}var gs={typeParameters:[{constraints:[]}],parameters:[be("0x1::object::Object"),new D,new q]};async function ms(n){let{aptosConfig:e,sender:t,fungibleAssetMetadataAddress:r,recipient:i,amount:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::primary_fungible_store::transfer",typeArguments:["0x1::fungible_asset::Metadata"],functionArguments:[r,i,o],abi:gs},options:s})}async function ys(n){let{aptosConfig:e,sender:t,fromStore:r,toStore:i,amount:o,options:s}=n;return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::dispatchable_fungible_asset::transfer",typeArguments:["0x1::fungible_asset::FungibleStore"],functionArguments:[r,i,o],abi:gs},options:s})}var gn=class{constructor(e){this.config=e}async getFungibleAssetMetadata(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"fungible_asset_processor"}),Jn({aptosConfig:this.config,...e})}async getFungibleAssetMetadataByAssetType(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"fungible_asset_processor"}),(await Jn({aptosConfig:this.config,options:{where:{asset_type:{_eq:e.assetType}}}}))[0]}async getFungibleAssetMetadataByCreatorAddress(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"fungible_asset_processor"}),await Jn({aptosConfig:this.config,options:{where:{creator_address:{_eq:c.from(e.creatorAddress).toStringLong()}}}})}async getFungibleAssetActivities(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"fungible_asset_processor"}),ps({aptosConfig:this.config,...e})}async getCurrentFungibleAssetBalances(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"fungible_asset_processor"}),ls({aptosConfig:this.config,...e})}async transferFungibleAsset(e){return ms({aptosConfig:this.config,...e})}async transferFungibleAssetBetweenStores(e){return ys({aptosConfig:this.config,...e})}};var mn=class{constructor(e){this.config=e}async getLedgerInfo(){return Cn({aptosConfig:this.config})}async getChainId(){return(await this.getLedgerInfo()).chain_id}async getBlockByVersion(e){return Hi({aptosConfig:this.config,...e})}async getBlockByHeight(e){return Li({aptosConfig:this.config,...e})}async view(e){return te({aptosConfig:this.config,...e})}async viewJson(e){return ko({aptosConfig:this.config,...e})}async getChainTopUserTransactions(e){return Ui({aptosConfig:this.config,...e})}async queryIndexer(e){return w({aptosConfig:this.config,...e})}async getIndexerLastSuccessVersion(){return Di({aptosConfig:this.config})}async getProcessorStatus(e){return Ri({aptosConfig:this.config,processorType:e})}};var As=["A name must be between 3 and 63 characters long,","and can only contain lowercase a-z, 0-9, and hyphens.","A name may not start or end with a hyphen."].join(" ");function fs(n){return!(!n||n.length<3||n.length>63||!/^[a-z\d][a-z\d-]{1,61}[a-z\d]$/.test(n))}function tt(n){let[e,t,...r]=n.replace(/\.apt$/,"").split(".");if(r.length>0)throw new Error(`${n} is invalid. A name can only have two parts, a domain and a subdomain separated by a "."`);if(!fs(e))throw new Error(`${e} is not valid. ${As}`);if(t&&!fs(t))throw new Error(`${t} is not valid. ${As}`);return{domainName:t||e,subdomainName:t?e:void 0}}var Ba=(t=>(t[t.Independent=0]="Independent",t[t.FollowsDomain=1]="FollowsDomain",t))(Ba||{});function hs(n){if(!n)return!1;let e=new Date(n.domain_expiration_timestamp).getTime()<Date.now(),t=new Date(n.expiration_timestamp).getTime()<Date.now();return n.subdomain&&e?!1:n.subdomain&&n.subdomain_expiration_policy===1?!0:!t}var nh="0x37368b46ce665362562c6d1d4ec01a08c8644c488690df5a17e13ba163e20221",Na="0x585fc9f0f0c54183b039ffc770ca282ebd87307916c215a3e692f2f8e4305e82",Ma={testnet:"0x5f8fd2347449685cf41d4db97926ec3a096eaf381332be4f1318ad4d16a8497c",mainnet:"0x867ed1f6bf916171b1de3ee92849b8978b7d1b9e0a8cc982a3d19d535dfd9c0c",local:Na,custom:null,devnet:null};function Ve(n){let e=Ma[n.network];if(!e)throw new Error(`The ANS contract is not deployed to ${n.network}`);return e}var Zn=n=>{if(n&&typeof n=="object"&&"vec"in n&&Array.isArray(n.vec))return n.vec[0]};async function bs(n){let{aptosConfig:e,name:t}=n,r=Ve(e),{domainName:i,subdomainName:o}=tt(t),s=await te({aptosConfig:e,payload:{function:`${r}::router::get_owner_addr`,functionArguments:[i,o]}}),a=Zn(s[0]);return a?c.from(a):void 0}async function ws(n){let{aptosConfig:e,expiration:t,name:r,sender:i,targetAddress:o,toAddress:s,options:a,transferable:u}=n,d=Ve(e),{domainName:l,subdomainName:A}=tt(r),p=t.policy==="subdomain:independent"||t.policy==="subdomain:follow-domain";if(A&&!p)throw new Error("Subdomains must have an expiration policy of either 'subdomain:independent' or 'subdomain:follow-domain'");if(p&&!A)throw new Error(`Policy is set to ${t.policy} but no subdomain was provided`);if(t.policy==="domain"){let V=t.years??1;if(V!==1)throw new Error("For now, names can only be registered for 1 year at a time");let Le=V*31536e3;return await y({aptosConfig:e,sender:i.accountAddress.toString(),data:{function:`${d}::router::register_domain`,functionArguments:[l,Le,o,s]},options:a})}if(!A)throw new Error(`${t.policy} requires a subdomain to be provided.`);let g=await Gr({aptosConfig:e,name:l});if(!g)throw new Error("The domain does not exist");let f=t.policy==="subdomain:independent"?t.expirationDate:g;if(f>g)throw new Error("The subdomain expiration time cannot be greater than the domain expiration time");return await y({aptosConfig:e,sender:i.accountAddress.toString(),data:{function:`${d}::router::register_subdomain`,functionArguments:[l,A,Math.round(f/1e3),t.policy==="subdomain:follow-domain"?1:0,!!u,o,s]},options:a})}async function Gr(n){let{aptosConfig:e,name:t}=n,r=Ve(e),{domainName:i,subdomainName:o}=tt(t);try{let s=await te({aptosConfig:e,payload:{function:`${r}::router::get_expiration`,functionArguments:[i,o]}});return Number(s[0])*1e3}catch{return}}async function Ts(n){let{aptosConfig:e,address:t}=n,r=Ve(e),i=await te({aptosConfig:e,payload:{function:`${r}::router::get_primary_name`,functionArguments:[c.from(t).toString()]}}),o=Zn(i[1]),s=Zn(i[0]);if(o)return[s,o].filter(Boolean).join(".")}async function Ss(n){let{aptosConfig:e,sender:t,name:r,options:i}=n,o=Ve(e);if(!r)return await y({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${o}::router::clear_primary_name`,functionArguments:[]},options:i});let{domainName:s,subdomainName:a}=tt(r);return await y({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${o}::router::set_primary_name`,functionArguments:[s,a]},options:i})}async function Ps(n){let{aptosConfig:e,name:t}=n,r=Ve(e),{domainName:i,subdomainName:o}=tt(t),s=await te({aptosConfig:e,payload:{function:`${r}::router::get_target_addr`,functionArguments:[i,o]}}),a=Zn(s[0]);return a?c.from(a):void 0}async function xs(n){let{aptosConfig:e,sender:t,name:r,address:i,options:o}=n,s=Ve(e),{domainName:a,subdomainName:u}=tt(r);return await y({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${s}::router::set_target_addr`,functionArguments:[a,u,i]},options:o})}async function Ks(n){let{aptosConfig:e,name:t}=n,{domainName:r,subdomainName:i=""}=tt(t),a=(await w({aptosConfig:e,query:{query:Ft,variables:{where_condition:{domain:{_eq:r},subdomain:{_eq:i}},limit:1}},originMethod:"getName"})).current_aptos_names[0];return a&&(a=yn(a)),hs(a)?a:void 0}async function Es(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Br({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountNames",query:{query:Ft,variables:{limit:t?.limit,offset:t?.offset,order_by:t?.orderBy,where_condition:{...n.options?.where??{},owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i}}}}})).current_aptos_names.map(yn)}async function Cs(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Br({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountDomains",query:{query:Ft,variables:{limit:t?.limit,offset:t?.offset,order_by:t?.orderBy,where_condition:{...n.options?.where??{},owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i},subdomain:{_eq:""}}}}})).current_aptos_names.map(yn)}async function Is(n){let{aptosConfig:e,options:t,accountAddress:r}=n,i=await Br({aptosConfig:e});return(await w({aptosConfig:e,originMethod:"getAccountSubdomains",query:{query:Ft,variables:{limit:t?.limit,offset:t?.offset,order_by:t?.orderBy,where_condition:{...n.options?.where??{},owner_address:{_eq:r.toString()},expiration_timestamp:{_gte:i},subdomain:{_neq:""}}}}})).current_aptos_names.map(yn)}async function vs(n){let{aptosConfig:e,options:t,domain:r}=n;return(await w({aptosConfig:e,originMethod:"getDomainSubdomains",query:{query:Ft,variables:{limit:t?.limit,offset:t?.offset,order_by:t?.orderBy,where_condition:{...n.options?.where??{},domain:{_eq:r},subdomain:{_neq:""}}}}})).current_aptos_names.map(yn).filter(hs)}async function Br(n){let{aptosConfig:e}=n,t=Ve(e),[r]=await te({aptosConfig:e,payload:{function:`${t}::config::reregistration_grace_sec`,functionArguments:[]}}),i=r/60/60/24,o=()=>new Date;return new Date(o().setDate(o().getDate()-i)).toISOString()}async function _s(n){let{aptosConfig:e,sender:t,name:r,years:i=1,options:o}=n,s=Ve(e),a=i*31536e3,{domainName:u,subdomainName:d}=tt(r);if(d)throw new Error("Subdomains cannot be renewed");if(i!==1)throw new Error("Currently, only 1 year renewals are supported");return await y({aptosConfig:e,sender:t.accountAddress.toString(),data:{function:`${s}::router::renew_domain`,functionArguments:[u,a]},options:o})}function yn(n){return{...n,expiration_timestamp:new Date(n.expiration_timestamp).getTime()}}var An=class{constructor(e){this.config=e}async getOwnerAddress(e){return bs({aptosConfig:this.config,...e})}async getExpiration(e){return Gr({aptosConfig:this.config,...e})}async getTargetAddress(e){return Ps({aptosConfig:this.config,...e})}async setTargetAddress(e){return xs({aptosConfig:this.config,...e})}async getPrimaryName(e){return Ts({aptosConfig:this.config,...e})}async setPrimaryName(e){return Ss({aptosConfig:this.config,...e})}async registerName(e){return ws({aptosConfig:this.config,...e})}async renewDomain(e){return _s({aptosConfig:this.config,...e})}async getName(e){return Ks({aptosConfig:this.config,...e})}async getAccountNames(e){return Es({aptosConfig:this.config,...e})}async getAccountDomains(e){return Cs({aptosConfig:this.config,...e})}async getAccountSubdomains(e){return Is({aptosConfig:this.config,...e})}async getDomainSubdomains(e){return vs({aptosConfig:this.config,...e})}};async function ks(n){let{aptosConfig:e,poolAddress:t}=n,r=c.from(t).toStringLong(),o=await w({aptosConfig:e,query:{query:ur,variables:{where_condition:{pool_address:{_eq:r}}}}});return o.num_active_delegator_per_pool[0]?o.num_active_delegator_per_pool[0].num_active_delegator:0}async function Fs(n){let{aptosConfig:e,options:t}=n,r={query:ur,variables:{order_by:t?.orderBy}};return(await w({aptosConfig:e,query:r})).num_active_delegator_per_pool}async function Us(n){let{aptosConfig:e,delegatorAddress:t,poolAddress:r}=n,i={query:Ci,variables:{delegatorAddress:c.from(t).toStringLong(),poolAddress:c.from(r).toStringLong()}};return(await w({aptosConfig:e,query:i})).delegated_staking_activities}var fn=class{constructor(e){this.config=e}async getNumberOfDelegators(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"stake_processor"}),ks({aptosConfig:this.config,...e})}async getNumberOfDelegatorsForAllPools(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"stake_processor"}),Fs({aptosConfig:this.config,...e})}async getDelegatedStakingActivities(e){return await h({config:this.config,minimumLedgerVersion:e?.minimumLedgerVersion,processorType:"stake_processor"}),Us({aptosConfig:this.config,...e})}};var Yn=class{constructor(e){this.config=e}async simple(e){return y({aptosConfig:this.config,...e})}async multiAgent(e){return y({aptosConfig:this.config,...e})}};var Ht=class{constructor(e){this.config=e}async simple(e){return Ur({aptosConfig:this.config,...e})}async multiAgent(e){return Ur({aptosConfig:this.config,...e})}};wt([lr],Ht.prototype,"simple",1),wt([lr],Ht.prototype,"multiAgent",1);var Lt=class{constructor(e){this.config=e}async simple(e){return sn({aptosConfig:this.config,...e})}async multiAgent(e){return sn({aptosConfig:this.config,...e})}};wt([pr],Lt.prototype,"simple",1),wt([pr],Lt.prototype,"multiAgent",1);import Ha from"eventemitter3";var Xn=class{constructor(e,t,r,i,o){this.lastUncommintedNumber=null;this.currentNumber=null;this.lock=!1;this.aptosConfig=e,this.account=t,this.maxWaitTime=r,this.maximumInFlight=i,this.sleepTime=o}async nextSequenceNumber(){for(;this.lock;)await $t(this.sleepTime);this.lock=!0;let e=BigInt(0);try{if((this.lastUncommintedNumber===null||this.currentNumber===null)&&await this.initialize(),this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight){await this.update();let t=_e();for(;this.currentNumber-this.lastUncommintedNumber>=this.maximumInFlight;)await $t(this.sleepTime),_e()-t>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, re-syncing ${this.account.accountAddress.toString()}`),await this.initialize()):await this.update()}e=this.currentNumber,this.currentNumber+=BigInt(1)}catch(t){console.error("error in getting next sequence number for this account",t)}finally{this.lock=!1}return e}async initialize(){let{sequence_number:e}=await ft({aptosConfig:this.aptosConfig,accountAddress:this.account.accountAddress});this.currentNumber=BigInt(e),this.lastUncommintedNumber=BigInt(e)}async update(){let{sequence_number:e}=await ft({aptosConfig:this.aptosConfig,accountAddress:this.account.accountAddress});return this.lastUncommintedNumber=BigInt(e),this.lastUncommintedNumber}async synchronize(){if(this.lastUncommintedNumber!==this.currentNumber){for(;this.lock;)await $t(this.sleepTime);this.lock=!0;try{await this.update();let e=_e();for(;this.lastUncommintedNumber!==this.currentNumber;)_e()-e>this.maxWaitTime?(console.warn(`Waited over 30 seconds for a transaction to commit, re-syncing ${this.account.accountAddress.toString()}`),await this.initialize()):(await $t(this.sleepTime),await this.update())}catch(e){console.error("error in synchronizing this account sequence number with the one on chain",e)}finally{this.lock=!1}}}};import Va from"eventemitter3";var Ds="fulfilled",Rs=(o=>(o.TransactionSent="transactionSent",o.TransactionSendFailed="transactionSendFailed",o.TransactionExecuted="transactionExecuted",o.TransactionExecutionFailed="transactionExecutionFailed",o.ExecutionFinish="executionFinish",o))(Rs||{}),er=class extends Va{constructor(t,r,i=30,o=100,s=10){super();this.taskQueue=new _n;this.transactionsQueue=new _n;this.outstandingTransactions=new _n;this.sentTransactions=[];this.executedTransactions=[];this.aptosConfig=t,this.account=r,this.started=!1,this.accountSequnceNumber=new Xn(t,r,i,o,s)}async submitNextTransaction(){try{for(;;){let t=await this.accountSequnceNumber.nextSequenceNumber();if(t===null)return;let r=await this.generateNextTransaction(this.account,t);if(!r)return;let i=Xe({aptosConfig:this.aptosConfig,transaction:r,signer:this.account});await this.outstandingTransactions.enqueue([i,t])}}catch(t){if(t instanceof gr)return;throw new Error(`Submit transaction failed for ${this.account.accountAddress.toString()} with error ${t}`)}}async processTransactions(){try{for(;;){let t=[],r=[],[i,o]=await this.outstandingTransactions.dequeue();for(t.push(i),r.push(o);!this.outstandingTransactions.isEmpty();)[i,o]=await this.outstandingTransactions.dequeue(),t.push(i),r.push(o);let s=await Promise.allSettled(t);for(let a=0;a<s.length&&a<r.length;a+=1){let u=s[a];o=r[a],u.status===Ds?(this.sentTransactions.push([u.value.hash,o,null]),this.emit("transactionSent",{message:`transaction hash ${u.value.hash} has been committed to chain`,transactionHash:u.value.hash}),await this.checkTransaction(u,o)):(this.sentTransactions.push([u.status,o,u.reason]),this.emit("transactionSendFailed",{message:`failed to commit transaction ${this.sentTransactions.length} with error ${u.reason}`,error:u.reason}))}this.emit("executionFinish",{message:`execute ${s.length} transactions finished`})}}catch(t){if(t instanceof gr)return;throw new Error(`Process execution failed for ${this.account.accountAddress.toString()} with error ${t}`)}}async checkTransaction(t,r){try{let i=[];i.push(Qe({aptosConfig:this.aptosConfig,transactionHash:t.value.hash}));let o=await Promise.allSettled(i);for(let s=0;s<o.length;s+=1){let a=o[s];a.status===Ds?(this.executedTransactions.push([a.value.hash,r,null]),this.emit("transactionExecuted",{message:`transaction hash ${a.value.hash} has been executed on chain`,transactionHash:t.value.hash})):(this.executedTransactions.push([a.status,r,a.reason]),this.emit("transactionExecutionFailed",{message:`failed to execute transaction ${this.executedTransactions.length} with error ${a.reason}`,error:a.reason}))}}catch(i){throw new Error(`Check transaction failed for ${this.account.accountAddress.toString()} with error ${i}`)}}async push(t,r){this.transactionsQueue.enqueue([t,r])}async generateNextTransaction(t,r){if(this.transactionsQueue.isEmpty())return;let[i,o]=await this.transactionsQueue.dequeue();return y({aptosConfig:this.aptosConfig,sender:t.accountAddress,data:i,options:{...o,accountSequenceNumber:r}})}async run(){try{for(;!this.taskQueue.isCancelled();)await(await this.taskQueue.dequeue())()}catch(t){throw new Error(`Unable to start transaction batching: ${t}`)}}start(){if(this.started)throw new Error("worker has already started");this.started=!0,this.taskQueue.enqueue(()=>this.submitNextTransaction()),this.taskQueue.enqueue(()=>this.processTransactions()),this.run()}stop(){if(this.taskQueue.isCancelled())throw new Error("worker has already stopped");this.started=!1,this.taskQueue.cancel()}};var tr=class extends Ha{constructor(e){super(),this.config=e}start(e){let{sender:t}=e;this.account=t,this.transactionWorker=new er(this.config,t),this.transactionWorker.start(),this.registerToEvents()}push(e){let{data:t,options:r}=e;for(let i of t)this.transactionWorker.push(i,r)}registerToEvents(){this.transactionWorker.on("transactionSent",async e=>{this.emit("transactionSent",e)}),this.transactionWorker.on("transactionSendFailed",async e=>{this.emit("transactionSendFailed",e)}),this.transactionWorker.on("transactionExecuted",async e=>{this.emit("transactionExecuted",e)}),this.transactionWorker.on("transactionExecutionFailed",async e=>{this.emit("transactionExecutionFailed",e)}),this.transactionWorker.on("executionFinish",async e=>{this.emit("executionFinish",e)})}forSingleAccount(e){try{let{sender:t,data:r,options:i}=e;this.start({sender:t}),this.push({data:r,options:i})}catch(t){throw new Error(`failed to submit transactions with error: ${t}`)}}};var hn=class{constructor(e){this.config=e,this.build=new Yn(this.config),this.simulate=new Ht(this.config),this.submit=new Lt(this.config),this.batch=new tr(this.config)}async getTransactions(e){return Gi({aptosConfig:this.config,...e})}async getTransactionByVersion(e){return Bi({aptosConfig:this.config,...e})}async getTransactionByHash(e){return Ni({aptosConfig:this.config,...e})}async isPendingTransaction(e){return Mi({aptosConfig:this.config,...e})}async waitForTransaction(e){return Qe({aptosConfig:this.config,...e})}async getGasPriceEstimation(){return vn({aptosConfig:this.config})}getSigningMessage(e){return go(e)}async publishPackageTransaction(e){return yo({aptosConfig:this.config,...e})}async rotateAuthKey(e){return _o({aptosConfig:this.config,...e})}sign(e){return jn({...e})}signAsFeePayer(e){return Qn({...e})}async batchTransactionsForSingleAccount(e){try{let{sender:t,data:r,options:i}=e;this.batch.forSingleAccount({sender:t,data:r,options:i})}catch(t){throw new Error(`failed to submit transactions with error: ${t}`)}}async signAndSubmitTransaction(e){return Xe({aptosConfig:this.config,...e})}async signAndSubmitAsFeePayer(e){return mo({aptosConfig:this.config,...e})}};import{jwtDecode as qa}from"jwt-decode";async function nr(n){let{aptosConfig:e,jwt:t,ephemeralKeyPair:r,uidKey:i="sub",derivationPath:o}=n,s={jwt_b64:t,epk:r.getPublicKey().bcsToHex().toStringWithoutPrefix(),exp_date_secs:r.expiryDateSecs,epk_blinder:T.fromHexInput(r.blinder).toStringWithoutPrefix(),uid_key:i,derivation_path:o},{data:a}=await yi({aptosConfig:e,path:"fetch",body:s,originMethod:"getPepper",overrides:{WITH_CREDENTIALS:!1}});return T.fromHexInput(a.pepper).toUint8Array()}async function Nr(n){let{aptosConfig:e,jwt:t,ephemeralKeyPair:r,pepper:i=await nr(n),uidKey:o="sub",maxExpHorizonSecs:s=(await Gt({aptosConfig:e})).maxExpHorizonSecs}=n;if(T.fromHexInput(i).toUint8Array().length!==Ne.PEPPER_LENGTH)throw new Error(`Pepper needs to be ${Ne.PEPPER_LENGTH} bytes`);let a=qa(t);if(typeof a.iat!="number")throw new Error("iat was not found");if(s<r.expiryDateSecs-a.iat)throw Error(`The EphemeralKeyPair is too long lived.  It's lifespan must be less than ${s}`);let u={jwt_b64:t,epk:r.getPublicKey().bcsToHex().toStringWithoutPrefix(),epk_blinder:T.fromHexInput(r.blinder).toStringWithoutPrefix(),exp_date_secs:r.expiryDateSecs,exp_horizon_secs:s,pepper:T.fromHexInput(i).toStringWithoutPrefix(),uid_key:o},{data:d}=await Ai({aptosConfig:e,path:"prove",body:u,originMethod:"getProof",overrides:{WITH_CREDENTIALS:!1}}),l=d.proof,A=new nt({a:l.a,b:l.b,c:l.c});return new ae({proof:new qt(A,0),trainingWheelsSignature:We.fromHex(d.training_wheels_signature),expHorizonSecs:s})}async function Os(n){let{aptosConfig:e,jwt:t,jwkAddress:r,uidKey:i,proofFetchCallback:o,pepper:s=await nr(n)}=n,{verificationKey:a,maxExpHorizonSecs:u}=await Gt({aptosConfig:e}),d=Nr({...n,pepper:s,maxExpHorizonSecs:u}),l=o?d:await d;if(r!==void 0){let g=N.fromJwtAndPepper({jwt:t,pepper:s,jwkAddress:r,uidKey:i}),f=await Nt({aptosConfig:e,authenticationKey:g.authKey().derivedAddress()});return yt.create({...n,address:f,proof:l,pepper:s,proofFetchCallback:o,jwkAddress:r,verificationKey:a})}let A=E.fromJwtAndPepper({jwt:t,pepper:s,uidKey:i}),p=await Nt({aptosConfig:e,authenticationKey:A.authKey().derivedAddress()});return Ne.create({...n,address:p,proof:l,pepper:s,proofFetchCallback:o,verificationKey:a})}async function zs(n){let{aptosConfig:e,sender:t,iss:r,options:i}=n,{jwksUrl:o}=n;o===void 0&&(ii.test(r)?o="https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>":o=r.endsWith("/")?`${r}.well-known/jwks.json`:`${r}/.well-known/jwks.json`);let s;try{if(s=await fetch(o),!s.ok)throw new Error(`${s.status} ${s.statusText}`)}catch(u){let d;throw u instanceof Error?d=`${u.message}`:d=`error unknown - ${u}`,b.fromErrorType({type:14,details:`Failed to fetch JWKS at ${o}: ${d}`})}let a=await s.json();return y({aptosConfig:e,sender:t.accountAddress,data:{function:"0x1::jwks::update_federated_jwk_set",functionArguments:[r,P.MoveString(a.keys.map(u=>u.kid)),P.MoveString(a.keys.map(u=>u.alg)),P.MoveString(a.keys.map(u=>u.e)),P.MoveString(a.keys.map(u=>u.n))]},options:i})}var bn=class{constructor(e){this.config=e}async getPepper(e){return nr({aptosConfig:this.config,...e})}async getProof(e){return Nr({aptosConfig:this.config,...e})}async deriveKeylessAccount(e){return Os({aptosConfig:this.config,...e})}async updateFederatedKeylessJwkSetTransaction(e){return zs({aptosConfig:this.config,...e})}};async function $a(n){let{aptosConfig:e,options:t}=n,r={query:En,variables:{where_condition:t?.where,offset:t?.offset,limit:t?.limit,order_by:t?.orderBy}};return(await w({aptosConfig:e,query:r,originMethod:"getObjectData"})).current_objects}async function Gs(n){let{aptosConfig:e,objectAddress:t,options:r}=n,o={object_address:{_eq:c.from(t).toStringLong()}};return(await $a({aptosConfig:e,options:{...r,where:o}}))[0]}var wn=class{constructor(e){this.config=e}async getObjectDataByObjectAddress(e){return await h({config:this.config,minimumLedgerVersion:e.minimumLedgerVersion,processorType:"objects_processor"}),Gs({aptosConfig:this.config,...e})}};var W=class{constructor(e){this.config=new qi(e),this.account=new an(this.config),this.abstraction=new ht(this.config),this.ans=new An(this.config),this.coin=new cn(this.config),this.digitalAsset=new un(this.config),this.event=new pn(this.config),this.faucet=new ln(this.config),this.fungibleAsset=new gn(this.config),this.general=new mn(this.config),this.staking=new fn(this.config),this.transaction=new hn(this.config),this.table=new mr(this.config),this.keyless=new bn(this.config),this.object=new wn(this.config)}};function ne(n,e,t){Object.getOwnPropertyNames(e.prototype).forEach(r=>{let i=Object.getOwnPropertyDescriptor(e.prototype,r);i&&(i.value=function(...o){return this[t][r](...o)},Object.defineProperty(n.prototype,r,i))})}ne(W,an,"account");ne(W,ht,"abstraction");ne(W,An,"ans");ne(W,cn,"coin");ne(W,un,"digitalAsset");ne(W,pn,"event");ne(W,ln,"faucet");ne(W,gn,"fungibleAsset");ne(W,mn,"general");ne(W,fn,"staking");ne(W,hn,"transaction");ne(W,mr,"table");ne(W,bn,"keyless");ne(W,wn,"object");var Ja=1e7,Mr=120,Za=30,Ya=330,Xa=120,ec=350,tc=300,nc=93,Ce=class Ce extends $e{constructor(e,t){super();let r=T.fromHexInput(t).toUint8Array();if(r.length!==Ce.ID_COMMITMENT_LENGTH)throw new Error(`Id Commitment length in bytes should be ${Ce.ID_COMMITMENT_LENGTH}`);this.iss=e,this.idCommitment=r}authKey(){let e=new j;return e.serializeU32AsUleb128(3),e.serializeFixedBytes(this.bcsToBytes()),Z.fromSchemeAndBytes({scheme:2,input:e.toUint8Array()})}verifySignature(e){try{return kn({...e,publicKey:this}),!0}catch(t){if(t instanceof b)return!1;throw t}}async verifySignatureAsync(e){return Ar({...e,publicKey:this})}serialize(e){e.serializeStr(this.iss),e.serializeBytes(this.idCommitment)}static deserialize(e){let t=e.deserializeStr(),r=e.deserializeBytes();return new Ce(t,r)}static load(e){let t=e.deserializeStr(),r=e.deserializeBytes();return new Ce(t,r)}static isPublicKey(e){return e instanceof Ce}static create(e){return Bs(e),new Ce(e.iss,Bs(e))}static fromJwtAndPepper(e){let{jwt:t,pepper:r,uidKey:i="sub"}=e,o=Ns(t);if(typeof o.iss!="string")throw new Error("iss was not found");if(typeof o.aud!="string")throw new Error("aud was not found or an array of values");let s=o[i];return Ce.create({iss:o.iss,uidKey:i,uidVal:s,aud:o.aud,pepper:r})}static isInstance(e){return"iss"in e&&typeof e.iss=="string"&&"idCommitment"in e&&e.idCommitment instanceof Uint8Array}};Ce.ID_COMMITMENT_LENGTH=32;var E=Ce;async function Ar(n){let{aptosConfig:e,publicKey:t,message:r,signature:i,jwk:o,keylessConfig:s=await Gt({aptosConfig:e}),options:a}=n;try{if(!(i instanceof Y))throw b.fromErrorType({type:26,details:"Not a keyless signature"});return kn({message:r,publicKey:t,signature:i,jwk:o||await hr({aptosConfig:e,publicKey:t,kid:i.getJwkKid()}),keylessConfig:s}),!0}catch(u){if(a?.throwErrorWithReason)throw u;return!1}}function kn(n){let{publicKey:e,message:t,signature:r,keylessConfig:i,jwk:o}=n,{verificationKey:s,maxExpHorizonSecs:a,trainingWheelsPubkey:u}=i;if(!(r instanceof Y))throw b.fromErrorType({type:26,details:"Not a keyless signature"});if(!(r.ephemeralCertificate.signature instanceof ae))throw b.fromErrorType({type:26,details:"Unsupported ephemeral certificate variant"});let d=r.ephemeralCertificate.signature;if(!(d.proof.proof instanceof nt))throw b.fromErrorType({type:26,details:"Unsupported proof variant for ZeroKnowledgeSig"});let l=d.proof.proof;if(r.expiryDateSecs<_e())throw b.fromErrorType({type:27,details:"The expiryDateSecs is in the past"});if(d.expHorizonSecs>a)throw b.fromErrorType({type:28});if(!r.ephemeralPublicKey.verifySignature({message:t,signature:r.ephemeralSignature}))throw b.fromErrorType({type:29});let A=rc({publicKey:e,signature:r,jwk:o,keylessConfig:i});if(!s.verifyProof({publicInputsHash:A,groth16Proof:l}))throw b.fromErrorType({type:32});if(u){if(!d.trainingWheelsSignature)throw b.fromErrorType({type:30});let p=new Hr(l,A);if(!u.verifySignature({message:p.hash(),signature:d.trainingWheelsSignature}))throw b.fromErrorType({type:31})}}function rc(n){let{publicKey:e,signature:t,jwk:r,keylessConfig:i}=n,o=e instanceof E?e:e.keylessPublicKey;if(!(t.ephemeralCertificate.signature instanceof ae))throw new Error("Signature is not a ZeroKnowledgeSig");let s=t.ephemeralCertificate.signature,a=[];return a.push(...Pn(t.ephemeralPublicKey.toUint8Array(),i.maxCommitedEpkBytes)),a.push(Ct(o.idCommitment)),a.push(t.expiryDateSecs),a.push(s.expHorizonSecs),a.push(xe(o.iss,i.maxIssValBytes)),s.extraField?(a.push(1n),a.push(xe(s.extraField,i.maxExtraFieldBytes))):(a.push(0n),a.push(xe(" ",i.maxExtraFieldBytes))),a.push(xe(Qa(t.jwtHeader,!0)+".",i.maxJwtHeaderB64Bytes)),a.push(r.toScalar()),s.overrideAudVal?(a.push(xe(s.overrideAudVal,Mr)),a.push(1n)):(a.push(xe("",Mr)),a.push(0n)),It(a)}async function hr(n){let{aptosConfig:e,publicKey:t,kid:r}=n,i=t instanceof E?t:t.keylessPublicKey,{iss:o}=i,s,a=t instanceof N?t.jwkAddress:void 0;try{s=await sc({aptosConfig:e,jwkAddr:a})}catch(l){throw b.fromErrorType({type:24,error:l,details:`Failed to fetch ${a?"Federated":"Patched"}JWKs ${a?`for address ${a}`:"0x1"}`})}let u=s.get(o);if(u===void 0)throw b.fromErrorType({type:7,details:`JWKs for issuer ${o} not found.`});let d=u.find(l=>l.kid===r);if(d===void 0)throw b.fromErrorType({type:6,details:`JWK with kid '${r}' for issuer '${o}' not found.`});return d}function Bs(n){let{uidKey:e,uidVal:t,aud:r,pepper:i}=n,o=[Ct(T.fromHexInput(i).toUint8Array()),xe(r,Mr),xe(t,Ya),xe(e,Za)];return ir(It(o),E.ID_COMMITMENT_LENGTH)}var Y=class n extends Pe{constructor(e){super();let{jwtHeader:t,ephemeralCertificate:r,expiryDateSecs:i,ephemeralPublicKey:o,ephemeralSignature:s}=e;this.jwtHeader=t,this.ephemeralCertificate=r,this.expiryDateSecs=i,this.ephemeralPublicKey=o,this.ephemeralSignature=s}getJwkKid(){return cc(this.jwtHeader).kid}serialize(e){this.ephemeralCertificate.serialize(e),e.serializeStr(this.jwtHeader),e.serializeU64(this.expiryDateSecs),this.ephemeralPublicKey.serialize(e),this.ephemeralSignature.serialize(e)}static deserialize(e){let t=zt.deserialize(e),r=e.deserializeStr(),i=e.deserializeU64(),o=Et.deserialize(e),s=We.deserialize(e);return new n({jwtHeader:r,expiryDateSecs:Number(i),ephemeralCertificate:t,ephemeralPublicKey:o,ephemeralSignature:s})}static getSimulationSignature(){return new n({jwtHeader:"{}",ephemeralCertificate:new zt(new ae({proof:new qt(new nt({a:new Uint8Array(32),b:new Uint8Array(64),c:new Uint8Array(32)}),0),expHorizonSecs:0}),0),expiryDateSecs:0,ephemeralPublicKey:new Et(new k(new Uint8Array(32))),ephemeralSignature:new We(new z(new Uint8Array(64)))})}static isSignature(e){return e instanceof n}},zt=class n extends Pe{constructor(e,t){super(),this.signature=e,this.variant=t}toUint8Array(){return this.signature.toUint8Array()}serialize(e){e.serializeU32AsUleb128(this.variant),this.signature.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(ae.deserialize(e),t);default:throw new Error(`Unknown variant index for EphemeralCertificate: ${t}`)}}},Tn=class Tn extends S{constructor(e){if(super(),this.data=T.fromHexInput(e).toUint8Array(),this.data.length!==32)throw new Error("Input needs to be 32 bytes")}serialize(e){e.serializeFixedBytes(this.data)}static deserialize(e){let t=e.deserializeFixedBytes(32);return new Tn(t)}toArray(){let e=this.toProjectivePoint();return[e.x.toString(),e.y.toString(),e.pz.toString()]}toProjectivePoint(){let e=new Uint8Array(this.data);e.reverse();let t=(e[0]&128)>>7,{Fp:r}=we.fields,i=r.create(Vr(e)),o=r.sqrt(r.add(r.pow(i,3n),Tn.B)),s=r.neg(o),a=o>s==(t===1)?o:s;return we.G1.ProjectivePoint.fromAffine({x:i,y:a})}};Tn.B=we.fields.Fp.create(3n);var He=Tn;function Vr(n){if(n.length!==32)throw new Error("Input should be 32 bytes");let e=new Uint8Array(n);return e[0]=e[0]&63,ja(e)}var Sn=class Sn extends S{constructor(e){if(super(),this.data=T.fromHexInput(e).toUint8Array(),this.data.length!==64)throw new Error("Input needs to be 64 bytes")}serialize(e){e.serializeFixedBytes(this.data)}static deserialize(e){let t=e.deserializeFixedBytes(64);return new Sn(t)}toArray(){let e=this.toProjectivePoint();return[[e.x.c0.toString(),e.x.c1.toString()],[e.y.c0.toString(),e.y.c1.toString()],[e.pz.c0.toString(),e.pz.c1.toString()]]}toProjectivePoint(){let e=new Uint8Array(this.data),t=e.slice(0,32).reverse(),r=e.slice(32,64).reverse(),i=(r[0]&128)>>7,{Fp2:o}=we.fields,s=o.fromBigTuple([Vr(t),Vr(r)]),a=o.sqrt(o.add(o.pow(s,3n),Sn.B)),u=o.neg(a),l=(a.c1>u.c1||a.c1===u.c1&&a.c0>u.c0)===(i===1)?a:u;return we.G2.ProjectivePoint.fromAffine({x:s,y:l})}};Sn.B=we.fields.Fp2.fromBigTuple([19485874751759354771024239261021720505790618469301721065564631296452457478373n,266929791119991161246907387137283842545076965332900288569378510910307636690n]);var bt=Sn,nt=class n extends Yr{constructor(e){super();let{a:t,b:r,c:i}=e;this.a=new He(t),this.b=new bt(r),this.c=new He(i)}serialize(e){this.a.serialize(e),this.b.serialize(e),this.c.serialize(e)}static deserialize(e){let t=He.deserialize(e).bcsToBytes(),r=bt.deserialize(e).bcsToBytes(),i=He.deserialize(e).bcsToBytes();return new n({a:t,b:r,c:i})}toSnarkJsJson(){return{protocol:"groth16",curve:"bn128",pi_a:this.a.toArray(),pi_b:this.b.toArray(),pi_c:this.c.toArray()}}},Hr=class n extends S{constructor(t,r){super();this.domainSeparator="APTOS::Groth16ProofAndStatement";if(this.proof=t,this.publicInputsHash=typeof r=="bigint"?ir(r,32):T.fromHexInput(r).toUint8Array(),this.publicInputsHash.length!==32)throw new Error("Invalid public inputs hash")}serialize(t){this.proof.serialize(t),t.serializeFixedBytes(this.publicInputsHash)}static deserialize(t){return new n(nt.deserialize(t),t.deserializeFixedBytes(32))}hash(){return Ye(this.bcsToBytes(),this.domainSeparator)}},qt=class n extends S{constructor(e,t){super(),this.proof=e,this.variant=t}serialize(e){e.serializeU32AsUleb128(this.variant),this.proof.serialize(e)}static deserialize(e){let t=e.deserializeUleb128AsU32();switch(t){case 0:return new n(nt.deserialize(e),t);default:throw new Error(`Unknown variant index for ZkProof: ${t}`)}}},ae=class n extends Pe{constructor(e){super();let{proof:t,expHorizonSecs:r,trainingWheelsSignature:i,extraField:o,overrideAudVal:s}=e;this.proof=t,this.expHorizonSecs=r,this.trainingWheelsSignature=i,this.extraField=o,this.overrideAudVal=s}static fromBytes(e){return n.deserialize(new H(e))}serialize(e){this.proof.serialize(e),e.serializeU64(this.expHorizonSecs),e.serializeOption(this.extraField),e.serializeOption(this.overrideAudVal),e.serializeOption(this.trainingWheelsSignature)}static deserialize(e){let t=qt.deserialize(e),r=Number(e.deserializeU64()),i=e.deserializeOption("string"),o=e.deserializeOption("string"),s=e.deserializeOption(We);return new n({proof:t,expHorizonSecs:r,trainingWheelsSignature:s,extraField:i,overrideAudVal:o})}},Lr=class n{constructor(e){let{verificationKey:t,trainingWheelsPubkey:r,maxExpHorizonSecs:i=Ja,maxExtraFieldBytes:o=ec,maxJwtHeaderB64Bytes:s=tc,maxIssValBytes:a=Xa,maxCommitedEpkBytes:u=nc}=e;this.verificationKey=t,this.maxExpHorizonSecs=i,r&&(this.trainingWheelsPubkey=new Et(new k(r))),this.maxExtraFieldBytes=o,this.maxJwtHeaderB64Bytes=s,this.maxIssValBytes=a,this.maxCommitedEpkBytes=u}static create(e,t){return new n({verificationKey:new qr({alphaG1:e.alpha_g1,betaG2:e.beta_g2,deltaG2:e.delta_g2,gammaAbcG1:e.gamma_abc_g1,gammaG2:e.gamma_g2}),maxExpHorizonSecs:Number(t.max_exp_horizon_secs),trainingWheelsPubkey:t.training_wheels_pubkey.vec[0],maxExtraFieldBytes:t.max_extra_field_bytes,maxJwtHeaderB64Bytes:t.max_jwt_header_b64_bytes,maxIssValBytes:t.max_iss_val_bytes,maxCommitedEpkBytes:t.max_commited_epk_bytes})}},qr=class n{constructor(e){let{alphaG1:t,betaG2:r,deltaG2:i,gammaAbcG1:o,gammaG2:s}=e;this.alphaG1=new He(t),this.betaG2=new bt(r),this.deltaG2=new bt(i),this.gammaAbcG1=[new He(o[0]),new He(o[1])],this.gammaG2=new bt(s)}hash(){let e=new j;return this.serialize(e),Wa.create().update(e.toUint8Array()).digest()}serialize(e){this.alphaG1.serialize(e),this.betaG2.serialize(e),this.deltaG2.serialize(e),this.gammaAbcG1[0].serialize(e),this.gammaAbcG1[1].serialize(e),this.gammaG2.serialize(e)}static fromGroth16VerificationKeyResponse(e){return new n({alphaG1:e.alpha_g1,betaG2:e.beta_g2,deltaG2:e.delta_g2,gammaAbcG1:e.gamma_abc_g1,gammaG2:e.gamma_g2})}verifyProof(e){let{publicInputsHash:t,groth16Proof:r}=e;try{let i=r.a.toProjectivePoint(),o=r.b.toProjectivePoint(),s=r.c.toProjectivePoint(),a=this.alphaG1.toProjectivePoint(),u=this.betaG2.toProjectivePoint(),d=this.gammaG2.toProjectivePoint(),l=this.deltaG2.toProjectivePoint(),A=this.gammaAbcG1.map(qe=>qe.toProjectivePoint()),{Fp12:p}=we.fields,g=A[0].add(A[1].multiply(t)),f=we.pairing(g,d),C=we.pairing(i,o),V=we.pairing(a,u),Ie=we.pairing(s,l),Le=p.mul(V,p.mul(f,Ie));return p.eql(C,Le)}catch(i){throw b.fromErrorType({type:32,error:i,details:"Error encountered when checking zero knowledge relation"})}}toSnarkJsJson(){return{protocol:"groth16",curve:"bn128",nPublic:1,vk_alpha_1:this.alphaG1.toArray(),vk_beta_2:this.betaG2.toArray(),vk_gamma_2:this.gammaG2.toArray(),vk_delta_2:this.deltaG2.toArray(),IC:this.gammaAbcG1.map(e=>e.toArray())}}};async function Gt(n){let{aptosConfig:e}=n;try{return await kt(async()=>{let[t,r]=await Promise.all([ic(n),oc(n)]);return Lr.create(r,t)},`keyless-configuration-${e.network}`,1e3*60*5)()}catch(t){throw t instanceof b?t:b.fromErrorType({type:25,error:t})}}function Be(n){let{jwt:e,uidKey:t="sub"}=n,r;try{r=Ns(e)}catch(o){throw b.fromErrorType({type:12,details:`Failed to parse JWT - ${si(o)}`})}if(typeof r.iss!="string")throw b.fromErrorType({type:12,details:"JWT is missing 'iss' in the payload. This should never happen."});if(typeof r.aud!="string")throw b.fromErrorType({type:12,details:"JWT is missing 'aud' in the payload or 'aud' is an array of values."});let i=r[t];return{iss:r.iss,aud:r.aud,uidVal:i}}async function ic(n){let{aptosConfig:e,options:t}=n,r="0x1::keyless_account::Configuration";try{let{data:i}=await rt({aptosConfig:e,originMethod:"getKeylessConfigurationResource",path:`accounts/${c.from("0x1").toString()}/resource/${r}`,params:{ledger_version:t?.ledgerVersion}});return i.data}catch(i){throw b.fromErrorType({type:22,error:i})}}async function oc(n){let{aptosConfig:e,options:t}=n,r="0x1::keyless_account::Groth16VerificationKey";try{let{data:i}=await rt({aptosConfig:e,originMethod:"getGroth16VerificationKeyResource",path:`accounts/${c.from("0x1").toString()}/resource/${r}`,params:{ledger_version:t?.ledgerVersion}});return i.data}catch(i){throw b.fromErrorType({type:23,error:i})}}async function sc(n){let{aptosConfig:e,jwkAddr:t,options:r}=n,i;if(t){let s="0x1::jwks::FederatedJWKs",{data:a}=await rt({aptosConfig:e,originMethod:"getKeylessJWKs",path:`accounts/${c.from(t).toString()}/resource/${s}`,params:{ledger_version:r?.ledgerVersion}});i=a}else{let s="0x1::jwks::PatchedJWKs",{data:a}=await rt({aptosConfig:e,originMethod:"getKeylessJWKs",path:`accounts/0x1/resource/${s}`,params:{ledger_version:r?.ledgerVersion}});i=a}let o=new Map;for(let s of i.data.jwks.entries){let a=[];for(let u of s.jwks){let{data:d}=u.variant,l=new H(T.fromHexInput(d).toUint8Array()),A=$r.deserialize(l);a.push(A)}o.set(jr(s.issuer),a)}return o}var $r=class n extends S{constructor(e){super();let{kid:t,kty:r,alg:i,e:o,n:s}=e;this.kid=t,this.kty=r,this.alg=i,this.e=o,this.n=s}serialize(e){e.serializeStr(this.kid),e.serializeStr(this.kty),e.serializeStr(this.alg),e.serializeStr(this.e),e.serializeStr(this.n)}static fromMoveStruct(e){let{data:t}=e.variant,r=new H(T.fromHexInput(t).toUint8Array());return n.deserialize(r)}toScalar(){if(this.alg!=="RS256")throw b.fromErrorType({type:32,details:"Failed to convert JWK to scalar when calculating the public inputs hash. Only RSA 256 is supported currently"});let e=ui(this.n),r=ac(e.reverse()).map(i=>Ct(i));return r.push(256n),It(r)}static deserialize(e){let t=e.deserializeStr(),r=e.deserializeStr(),i=e.deserializeStr(),o=e.deserializeStr(),s=e.deserializeStr();return new n({kid:t,kty:r,alg:i,n:s,e:o})}};function ac(n){let e=[];for(let t=0;t<n.length;t+=24){let r=n.slice(t,Math.min(t+24,n.length));if(r.length<24){let i=new Uint8Array(24);i.set(r),e.push(i)}else e.push(r)}return e}function cc(n){try{let e=JSON.parse(n);if(e.kid===void 0)throw new Error("JWT header missing kid");return e}catch{throw new Error("Failed to parse JWT header.")}}export{Ja as a,Mr as b,Za as c,Ya as d,Xa as e,ec as f,tc as g,nc as h,E as i,Ar as j,kn as k,hr as l,Y as m,zt as n,nt as o,Hr as p,qt as q,ae as r,Lr as s,qr as t,Gt as u,Be as v,sc as w,$r as x,cc as y,N as z,v as A,U as B,jt as C,de as D,pe as E,Je as F,Ae as G,du as H,pu as I,L as J,fe as K,ke as L,X as M,Ke as N,Ut as O,ot as P,Qt as Q,R,G as S,oe as T,Fe as U,Ue as V,q as W,De as X,Re as Y,D as Z,st as _,Un as $,O as aa,K as ba,m as ca,Ee as da,nd as ea,x as fa,rd as ga,se as ha,Vs as ia,at as ja,Jt as ka,Zt as la,Yt as ma,ct as na,Xt as oa,en as pa,tn as qa,ge as ra,Dn as sa,ut as ta,dt as ua,Oe as va,pt as wa,Dt as xa,lt as ya,gt as za,Ze as Aa,ze as Ba,Rn as Ca,On as Da,zn as Ea,Ye as Fa,lp as Ga,he as Ha,ee as Ia,fr as Ja,me as Ka,Ge as La,mt as Ma,Bn as Na,ce as Oa,Gn as Pa,Ne as Qa,yt as Ra,nn as Sa,Nn as Ta,Wi as Ua,Ji as Va,Mn as Wa,Yi as Xa,ta as Ya,M as Za,be as _a,to as $a,Me as ab,na as bb,Vn as cb,Hn as db,no as eb,ro as fb,wr as gb,Ln as hb,Tr as ib,ra as jb,Sr as kb,Pr as lb,xr as mb,Kr as nb,Er as ob,Cr as pb,io as qb,I as rb,oo as sb,qn as tb,oa as ub,vr as vb,bm as wb,so as xb,ao as yb,_r as zb,At as Ab,Wn as Bb,ca as Cb,co as Db,ua as Eb,pa as Fb,kr as Gb,uo as Hb,Bt as Ib,Fr as Jb,po as Kb,Lm as Lb,y as Mb,ma as Nb,ya as Ob,go as Pb,jn as Qb,Qn as Rb,Ur as Sb,sn as Tb,Xe as Ub,mo as Vb,yo as Wb,ft as Xb,fo as Yb,ho as Zb,$n as _b,bo as $b,wo as ac,To as bc,Rr as cc,Nt as dc,So as ec,Po as fc,xo as gc,Ko as hc,Eo as ic,My as jc,Or as kc,Co as lc,Io as mc,vo as nc,Ao as oc,_o as pc,te as qc,ko as rc,Fo as sc,Uo as tc,Do as uc,ht as vc,an as wc,Ro as xc,cn as yc,Oo as zc,zo as Ac,Go as Bc,Bo as Cc,No as Dc,Vt as Ec,Mo as Fc,Vo as Gc,Ho as Hc,Lo as Ic,qo as Jc,$o as Kc,Wo as Lc,jo as Mc,Qo as Nc,Jo as Oc,Zo as Pc,Yo as Qc,Xo as Rc,es as Sc,ts as Tc,ns as Uc,rs as Vc,is as Wc,un as Xc,as as Yc,cs as Zc,us as _c,dn as $c,pn as ad,ds as bd,ln as cd,Jn as dd,ps as ed,ls as fd,ms as gd,ys as hd,gn as id,mn as jd,As as kd,fs as ld,tt as md,Ba as nd,hs as od,nh as pd,Na as qd,bs as rd,ws as sd,Gr as td,Ts as ud,Ss as vd,Ps as wd,xs as xd,Ks as yd,Es as zd,Cs as Ad,Is as Bd,vs as Cd,_s as Dd,An as Ed,ks as Fd,Fs as Gd,Us as Hd,fn as Id,Yn as Jd,Ht as Kd,Lt as Ld,Xn as Md,Ds as Nd,Rs as Od,er as Pd,tr as Qd,hn as Rd,nr as Sd,Nr as Td,Os as Ud,zs as Vd,bn as Wd,$a as Xd,Gs as Yd,wn as Zd,W as _d};
//# sourceMappingURL=chunk-BK56GLTP.mjs.map