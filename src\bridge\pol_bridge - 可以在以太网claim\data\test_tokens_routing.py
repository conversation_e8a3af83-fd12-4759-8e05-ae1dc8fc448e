#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试tokens.json中的代币在以太坊和Polygon网络上是否可以正常交易
筛选出能够在两个网络上都正常执行交易的代币
测试通过KyberSwap的API检查是否有可用的交易路由
支持多线程并发处理，提高效率
"""

import os
import sys
import json
import asyncio
import argparse
import time
import concurrent.futures
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime
from queue import Queue
from threading import Lock

# 添加项目根目录到系统路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
sys.path.append(project_root)

# 导入KyberSwap客户端
from src.dex.KyberSwap.client import KyberSwapClient
from src.dex.KyberSwap.constants import SUPPORTED_CHAINS

# 文件路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
TOKENS_PATH = os.path.join(CURRENT_DIR, "tokens.json")
OUTPUT_PATH = os.path.join(CURRENT_DIR, "routable_tokens.json")

# 在以太坊和Polygon上的USDT地址
TARGET_TOKENS = {
    "ethereum": {
        "symbol": "USDT",
        "address": "******************************************"
    },
    "polygon": {
        "symbol": "USDT",
        "address": "******************************************"
    }
}

# 交易测试设置
TEST_AMOUNT = 0.1  # 测试0.1个代币
MAX_RETRIES = 3  # 重试次数
RETRY_DELAY = 2  # 重试间隔（秒）
DEFAULT_WORKERS = 4  # 默认工作线程数

# 全局状态变量
stats_lock = Lock()
progress_stats = {
    "processed": 0,
    "ethereum_success": 0,
    "polygon_success": 0,
    "both_success": 0,
    "total": 0,
    "start_time": 0
}

async def check_token_route(
    chain: str, 
    token_address: str, 
    token_decimals: int,
    test_amount: float = 0.1
) -> Tuple[bool, Optional[str]]:
    """
    检查代币是否可以交易到USDT
    
    Args:
        chain: 链名称 (ethereum 或 polygon)
        token_address: 代币地址
        token_decimals: 代币小数位
        test_amount: 测试金额
        
    Returns:
        (是否可路由, 错误信息/None)
    """
    try:
        # 计算测试金额
        amount_in_wei = int(test_amount * (10 ** token_decimals))
        
        # 创建客户端
        client = KyberSwapClient(chain=chain)
        
        # 获取目标代币地址
        target_token = TARGET_TOKENS[chain]["address"]
        
        print(f"测试 {chain} 上的代币 {token_address} -> {target_token}")
        print(f"测试金额: {test_amount} ({amount_in_wei} wei)")
        
        # 获取路由
        routes = await client.get_routes(
            token_in=token_address,
            token_out=target_token,
            amount_in=str(amount_in_wei),
            slippage=1.0,  # 1%滑点
            save_gas=True
        )
        
        # 检查是否有路由
        if "error" in routes:
            print(f"❌ 路由错误: {routes['error']}")
            return False, routes["error"]
            
        # 检查是否有可用路由
        if not routes.get("routeSummary"):
            print(f"❌ 未找到可用路由")
            return False, "未找到可用路由"
            
        # 路由数据后处理
        route_summary = routes["routeSummary"]
        
        # 检查输出金额
        output_amount = int(route_summary.get("amountOut", 0))
        if output_amount <= 0:
            print(f"❌ 输出金额为零或负数")
            return False, "输出金额为零或负数"
        
        print(f"✅ 找到可用路由，输出金额: {output_amount}")
        return True, None
        
    except Exception as e:
        print(f"❌ 检查路由时出错: {str(e)}")
        return False, str(e)

def process_token(token_symbol: str, token_data: Dict, queue: Queue) -> Dict:
    """
    处理单个代币的路由测试
    优化：如果一个网络测试失败，不再测试另一个网络
    
    Args:
        token_symbol: 代币符号
        token_data: 代币数据
        queue: 用于更新进度的队列
        
    Returns:
        处理结果
    """
    result = {
        "token_symbol": token_symbol,
        "ethereum_routable": False,
        "polygon_routable": False,
        "status": "failed",
        "error": None
    }
    
    print(f"\n测试代币: {token_symbol}")
    
    try:
        # 首先检查以太坊网络
        if "1" in token_data:
            eth_address = token_data["1"]["address"]
            eth_decimals = token_data["1"].get("decimals", 18)
            
            print(f"测试以太坊上的 {token_symbol}: {eth_address}")
            
            # 尝试多次获取路由
            for retry in range(MAX_RETRIES):
                if retry > 0:
                    print(f"第 {retry+1}/{MAX_RETRIES} 次重试...")
                    time.sleep(RETRY_DELAY)
                    
                # 使用asyncio运行异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                eth_routable, eth_error = loop.run_until_complete(
                    check_token_route("ethereum", eth_address, eth_decimals, TEST_AMOUNT)
                )
                loop.close()
                
                if eth_routable:
                    result["ethereum_routable"] = True
                    break
            
            if not result["ethereum_routable"]:
                print(f"❌ 以太坊上的 {token_symbol} 无法路由到USDT")
                result["status"] = "failed"
                result["error"] = "以太坊路由失败"
                # 如果以太坊网络测试失败，根据优化2，直接返回结果不再测试Polygon
                queue.put(("update", {
                    "ethereum_result": False
                }))
                return result
            else:
                queue.put(("update", {
                    "ethereum_result": True
                }))
        else:
            print(f"跳过以太坊上的 {token_symbol} (无数据)")
            result["status"] = "skipped_eth"
            # 如果没有以太坊数据，也无需测试Polygon，因为我们需要两个网络都支持
            queue.put(("update", {
                "ethereum_result": False
            }))
            return result
        
        # 然后检查Polygon网络
        if "137" in token_data:
            pol_address = token_data["137"]["address"]
            pol_decimals = token_data["137"].get("decimals", 18)
            
            print(f"测试Polygon上的 {token_symbol}: {pol_address}")
            
            # 尝试多次获取路由
            for retry in range(MAX_RETRIES):
                if retry > 0:
                    print(f"第 {retry+1}/{MAX_RETRIES} 次重试...")
                    time.sleep(RETRY_DELAY)
                    
                # 使用asyncio运行异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                pol_routable, pol_error = loop.run_until_complete(
                    check_token_route("polygon", pol_address, pol_decimals, TEST_AMOUNT)
                )
                loop.close()
                
                if pol_routable:
                    result["polygon_routable"] = True
                    break
            
            if not result["polygon_routable"]:
                print(f"❌ Polygon上的 {token_symbol} 无法路由到USDT")
                result["status"] = "failed"
                result["error"] = "Polygon路由失败"
                queue.put(("update", {
                    "polygon_result": False
                }))
            else:
                queue.put(("update", {
                    "polygon_result": True
                }))
        else:
            print(f"跳过Polygon上的 {token_symbol} (无数据)")
            result["status"] = "skipped_pol"
            queue.put(("update", {
                "polygon_result": False
            }))
            return result
        
        # 最终状态判断
        if result["ethereum_routable"] and result["polygon_routable"]:
            print(f"✅ {token_symbol} 可以在两个网络上路由到USDT")
            result["status"] = "success"
            queue.put(("update", {
                "both_result": True
            }))
        else:
            result["status"] = "failed"
            queue.put(("update", {
                "both_result": False
            }))
        
        return result
        
    except Exception as e:
        print(f"处理代币 {token_symbol} 时出错: {str(e)}")
        result["status"] = "error"
        result["error"] = str(e)
        return result
    finally:
        # 更新处理计数
        queue.put(("processed", None))

def update_progress(queue: Queue):
    """处理进度更新"""
    while True:
        try:
            item = queue.get()
            if item is None:  # 结束信号
                break
                
            action, data = item
            
            with stats_lock:
                if action == "processed":
                    progress_stats["processed"] += 1
                elif action == "update":
                    if "ethereum_result" in data and data["ethereum_result"]:
                        progress_stats["ethereum_success"] += 1
                    if "polygon_result" in data and data["polygon_result"]:
                        progress_stats["polygon_success"] += 1
                    if "both_result" in data and data["both_result"]:
                        progress_stats["both_success"] += 1
                
                # 显示进度
                processed = progress_stats["processed"]
                total = progress_stats["total"]
                eth_success = progress_stats["ethereum_success"]
                pol_success = progress_stats["polygon_success"]
                both_success = progress_stats["both_success"]
                
                # 计算进度
                elapsed = time.time() - progress_stats["start_time"]
                tokens_per_second = processed / elapsed if elapsed > 0 else 0
                remaining = (total - processed) / tokens_per_second if tokens_per_second > 0 else 0
                
                print(f"\n进度: {processed}/{total} ({processed/total*100:.1f}%)")
                print(f"成功率: 以太坊 {eth_success}/{processed}, Polygon {pol_success}/{processed}, 两网络都成功 {both_success}/{processed}")
                print(f"耗时: {elapsed:.1f}秒, 速率: {tokens_per_second:.2f}个/秒, 预计剩余时间: {remaining:.1f}秒")
            
            queue.task_done()
        except Exception as e:
            print(f"更新进度时出错: {e}")

def test_tokens_mt(tokens: Dict, num_workers: int = DEFAULT_WORKERS):
    """使用多线程测试所有代币"""
    print(f"使用 {num_workers} 个工作线程处理 {len(tokens)} 个代币")
    
    # 初始化结果
    routable_tokens = {}
    failed_tokens = {}
    
    # 创建进度更新队列
    progress_queue = Queue()
    
    # 初始化进度统计
    with stats_lock:
        progress_stats["processed"] = 0
        progress_stats["ethereum_success"] = 0
        progress_stats["polygon_success"] = 0
        progress_stats["both_success"] = 0
        progress_stats["total"] = len(tokens)
        progress_stats["start_time"] = time.time()
    
    # 启动进度更新线程
    import threading
    progress_thread = threading.Thread(target=update_progress, args=(progress_queue,))
    progress_thread.daemon = True
    progress_thread.start()
    
    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        # 提交任务
        future_to_token = {
            executor.submit(process_token, token_symbol, token_data, progress_queue): (token_symbol, token_data)
            for token_symbol, token_data in tokens.items()
        }
        
        # 处理结果
        for future in concurrent.futures.as_completed(future_to_token):
            token_symbol, token_data = future_to_token[future]
            try:
                result = future.result()
                
                # 根据结果分类
                if result["status"] == "success":
                    routable_tokens[token_symbol] = token_data
                else:
                    failed_reason = {
                        "ethereum": result["ethereum_routable"],
                        "polygon": result["polygon_routable"],
                        "error": result["error"],
                        "status": result["status"]
                    }
                    failed_tokens[token_symbol] = {
                        "token_data": token_data,
                        "routing_status": failed_reason
                    }
                
            except Exception as e:
                print(f"处理 {token_symbol} 结果时出错: {e}")
                failed_tokens[token_symbol] = {
                    "token_data": token_data,
                    "routing_status": {
                        "ethereum": False,
                        "polygon": False,
                        "error": str(e),
                        "status": "error"
                    }
                }
    
    # 发送结束信号给进度线程
    progress_queue.put(None)
    progress_thread.join()
    
    return routable_tokens, failed_tokens

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="测试tokens.json中的代币在以太坊和Polygon网络上是否可以通过KyberSwap进行交易")
    parser.add_argument("--amount", type=float, default=0.1, help="测试交易金额 (默认: 0.1)")
    parser.add_argument("--workers", type=int, default=DEFAULT_WORKERS, help=f"并发工作线程数 (默认: {DEFAULT_WORKERS})")
    
    args = parser.parse_args()
    
    # 设置全局测试金额
    global TEST_AMOUNT
    TEST_AMOUNT = args.amount
    
    try:
        # 加载tokens.json
        print(f"从 {TOKENS_PATH} 加载代币数据...")
        with open(TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
        
        print(f"成功加载 {len(tokens)} 个代币")
        
        # 使用多线程测试代币
        routable_tokens, failed_tokens = test_tokens_mt(tokens, args.workers)
        
        # 保存结果
        output_data = {
            "metadata": {
                "total_tested": len(tokens),
                "ethereum_success": progress_stats["ethereum_success"],
                "polygon_success": progress_stats["polygon_success"],
                "both_success": progress_stats["both_success"],
                "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "test_amount": TEST_AMOUNT,
                "workers": args.workers
            },
            "routable_tokens": routable_tokens,
            "failed_tokens": failed_tokens
        }
        
        with open(OUTPUT_PATH, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n测试完成！结果已保存到 {OUTPUT_PATH}")
        print(f"总测试: {len(tokens)} 个代币")
        print(f"以太坊成功: {progress_stats['ethereum_success']} 个")
        print(f"Polygon成功: {progress_stats['polygon_success']} 个")
        print(f"两网络都成功: {progress_stats['both_success']} 个")
        
        # 创建仅包含可路由代币的简化版tokens.json
        simple_output_path = os.path.join(CURRENT_DIR, "routable_tokens_only.json")
        with open(simple_output_path, 'w', encoding='utf-8') as f:
            json.dump(routable_tokens, f, indent=2, ensure_ascii=False)
        
        print(f"简化版可路由代币已保存到 {simple_output_path}")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 