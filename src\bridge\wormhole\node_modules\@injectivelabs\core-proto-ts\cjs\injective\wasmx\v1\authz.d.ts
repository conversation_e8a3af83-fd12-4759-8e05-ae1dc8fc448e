import _m0 from "protobufjs/minimal.js";
import { ContractGrant } from "../../../cosmwasm/wasm/v1/authz";
export declare const protobufPackage = "injective.wasmx.v1";
/**
 * ContractExecutionAuthorization defines authorization for wasm execute.
 * Since: wasmd 0.30
 */
export interface ContractExecutionCompatAuthorization {
    /** Grants for contract executions */
    grants: ContractGrant[];
}
export declare const ContractExecutionCompatAuthorization: {
    encode(message: ContractExecutionCompatAuthorization, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ContractExecutionCompatAuthorization;
    fromJSON(object: any): ContractExecutionCompatAuthorization;
    toJSON(message: ContractExecutionCompatAuthorization): unknown;
    create(base?: DeepPartial<ContractExecutionCompatAuthorization>): ContractExecutionCompatAuthorization;
    fromPartial(object: DeepPartial<ContractExecutionCompatAuthorization>): ContractExecutionCompatAuthorization;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
