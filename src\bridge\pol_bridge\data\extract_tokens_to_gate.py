#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import sys

def main():
    """
    将routable_tokens_only.json中的数据提取到gate_tokens.json中
    使用追加的方式写入数据（在同网络后面添加新的代币，去重复）
    """
    # 源文件和目标文件路径
    source_file = os.path.join("src", "bridge", "pol_bridge", "data", "routable_tokens_only.json")
    target_file = os.path.join("data", "utils", "token", "gate_tokens.json")
    
    # 确保文件存在
    if not os.path.exists(source_file):
        print(f"错误: 源文件 {source_file} 不存在")
        sys.exit(1)
    
    if not os.path.exists(target_file):
        print(f"错误: 目标文件 {target_file} 不存在")
        sys.exit(1)
    
    print(f"正在读取源文件 {source_file}...")
    try:
        with open(source_file, "r", encoding="utf-8") as f:
            source_data = json.load(f)
    except Exception as e:
        print(f"读取源文件时出错: {str(e)}")
        sys.exit(1)
    
    print(f"正在读取目标文件 {target_file}...")
    try:
        with open(target_file, "r", encoding="utf-8") as f:
            target_data = json.load(f)
    except Exception as e:
        print(f"读取目标文件时出错: {str(e)}")
        sys.exit(1)
    
    # 转换网络ID映射
    network_map = {
        "1": "ETH",
        "137": "MATIC"
    }
    
    # 处理ETH和MATIC网络
    processed_count = 0
    skipped_count = 0
    
    # 对于每个网络，创建一个地址到符号的映射用于去重
    existing_tokens = {}
    for network in target_data:
        existing_tokens[network] = {}
        for token in target_data[network]:
            if "contract_address" in token:
                existing_tokens[network][token["contract_address"].lower()] = token["symbol"]
    
    for token_symbol, token_data in source_data.items():
        for chain_id, token_info in token_data.items():
            if chain_id not in network_map:
                continue
                
            network = network_map[chain_id]
            token_address = token_info["address"].lower()
            token_symbol_source = token_info["symbol"]
            
            # 检查此代币是否已存在于目标文件中
            if network in existing_tokens and token_address in existing_tokens[network]:
                print(f"跳过已存在的代币: {network} - {token_symbol_source} ({token_address})")
                skipped_count += 1
                continue
            
            # 创建新代币条目
            new_token = {
                "symbol": token_symbol_source,
                "contract_address": token_address
            }
            
            # 添加到目标数据中
            if network not in target_data:
                target_data[network] = []
            
            target_data[network].append(new_token)
            
            # 更新现有代币记录以防止重复添加
            if network not in existing_tokens:
                existing_tokens[network] = {}
            existing_tokens[network][token_address] = token_symbol_source
            
            processed_count += 1
            print(f"添加新代币: {network} - {token_symbol_source} ({token_address})")
    
    # 将更新后的数据写回目标文件
    try:
        with open(target_file, "w", encoding="utf-8") as f:
            json.dump(target_data, f, indent=2, ensure_ascii=False)
        print(f"\n处理完成! 数据已保存到 {target_file}")
        print(f"总计处理 {processed_count + skipped_count} 个代币:")
        print(f"  - 跳过已存在的代币: {skipped_count} 个")
        print(f"  - 成功添加新代币: {processed_count} 个")
    except Exception as e:
        print(f"写入目标文件时出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 