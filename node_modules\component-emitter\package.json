{"name": "component-emitter", "version": "2.0.0", "description": "Simple event emitter", "license": "MIT", "repository": "sindresorhus/component-emitter", "funding": "https://github.com/sponsors/sindresorhus", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "main": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["event", "emitter", "events", "emit", "listener", "pubsub", "observe"], "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}}