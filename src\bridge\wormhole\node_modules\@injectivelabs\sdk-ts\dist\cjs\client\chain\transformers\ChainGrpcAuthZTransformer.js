"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcAuthZTransformer = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const pagination_js_1 = require("../../../utils/pagination.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcAuthZTransformer {
    static grpcGrantToGrant(grant) {
        const authorization = decodeAuthorizationNoThrow(grant.authorization);
        return {
            authorization: authorization?.authorization,
            authorizationType: authorization?.authorizationType || '',
            expiration: grant.expiration,
        };
    }
    static grpcGrantAuthorizationToGrantAuthorization(grant) {
        const authorization = decodeAuthorizationNoThrow(grant.authorization);
        return {
            granter: grant.granter,
            grantee: grant.grantee,
            authorization: authorization?.authorization,
            authorizationType: authorization?.authorizationType || '',
            expiration: grant.expiration,
        };
    }
    static grpcGrantsToGrants(response) {
        return {
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantToGrant),
        };
    }
    static grpcGranteeGrantsToGranteeGrants(response) {
        return {
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantAuthorizationToGrantAuthorization),
        };
    }
    static grpcGranterGrantsToGranterGrants(response) {
        return {
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(response.pagination),
            grants: response.grants.map(ChainGrpcAuthZTransformer.grpcGrantAuthorizationToGrantAuthorization),
        };
    }
}
exports.ChainGrpcAuthZTransformer = ChainGrpcAuthZTransformer;
const decodeAuthorization = (authorization) => {
    switch (authorization.typeUrl) {
        case '/cosmos.authz.v1beta1.GenericAuthorization':
            return {
                authorization: core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(authorization.value),
                authorizationType: '/cosmos.authz.v1beta1.GenericAuthorization',
            };
        default:
            throw new exceptions_1.GeneralException(new Error('Unsupported authorization type'));
    }
};
const decodeAuthorizationNoThrow = (authorization) => {
    if (!authorization) {
        return undefined;
    }
    try {
        return decodeAuthorization(authorization);
    }
    catch {
        return undefined;
    }
};
