expect = require('expect.js');

// Node.js env
var sha3 = require('../src/sha3.js');
keccak_512 = sha3.keccak_512;
keccak_384 = sha3.keccak_384;
keccak_256 = sha3.keccak_256;
keccak_224 = sha3.keccak_224;
sha3_512 = sha3.sha3_512;
sha3_384 = sha3.sha3_384;
sha3_256 = sha3.sha3_256;
sha3_224 = sha3.sha3_224;
shake_128 = sha3.shake_128;
shake_256 = sha3.shake_256;
require('./test.js');
require('./test-shake.js');

delete require.cache[require.resolve('../src/sha3.js')];
delete require.cache[require.resolve('./test.js')];
delete require.cache[require.resolve('./test-shake.js')];

// Webpack browser env
JS_SHA3_NO_NODE_JS = true;
window = global;
expect = require('expect.js');
var sha3 = require('../src/sha3.js');
keccak_512 = sha3.keccak_512;
keccak_384 = sha3.keccak_384;
keccak_256 = sha3.keccak_256;
keccak_224 = sha3.keccak_224;
sha3_512 = sha3.sha3_512;
sha3_384 = sha3.sha3_384;
sha3_256 = sha3.sha3_256;
sha3_224 = sha3.sha3_224;
shake_128 = sha3.shake_128;
shake_256 = sha3.shake_256;
require('./test.js');
require('./test-shake.js');

delete require.cache[require.resolve('../src/sha3.js')];
delete require.cache[require.resolve('./test.js')];
delete require.cache[require.resolve('./test-shake.js')];
sha3_512 = null;
sha3_384 = null;
sha3_256 = null;
sha3_224 = null;
keccak_512 = null;
keccak_384 = null;
keccak_256 = null;
keccak_224 = null;
shake_128 = null;
shake_256 = null;

// browser env
JS_SHA3_NO_NODE_JS = true;
JS_SHA3_NO_COMMON_JS = true;
window = global;
require('../src/sha3.js');
require('./test.js');
require('./test-shake.js');
