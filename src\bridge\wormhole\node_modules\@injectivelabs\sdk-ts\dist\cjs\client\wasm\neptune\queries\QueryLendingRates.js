"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryGetAllLendingRates = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryGetAllLendingRates extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = {
            get_all_lending_rates: {
                ...(this.params.limit !== undefined ? { limit: this.params.limit } : {}),
                ...(this.params.startAfter ? { start_after: this.params.startAfter } : {}),
            },
        };
        return (0, index_js_1.toBase64)(payload);
    }
}
exports.QueryGetAllLendingRates = QueryGetAllLendingRates;
