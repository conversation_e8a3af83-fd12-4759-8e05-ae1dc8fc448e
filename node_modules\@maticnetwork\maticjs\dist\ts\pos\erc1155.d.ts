import { IPOSClientConfig, ITransactionOption } from "../interfaces";
import { Web3SideChainClient } from "../utils";
import { POSToken } from "./pos_token";
import { IPOSContracts, IPOSERC1155Address } from "../interfaces";
import { POSERC1155DepositBatchParam, POSERC1155DepositParam, POSERC1155TransferParam, TYPE_AMOUNT } from "..";
export declare class ERC1155 extends POSToken {
    mintablePredicateAddress: string;
    get addressConfig(): IPOSERC1155Address;
    constructor(tokenAddress: string, isParent: boolean, client: Web3SideChainClient<IPOSClientConfig>, getContracts: () => IPOSContracts);
    private getAddress_;
    /**
     * get balance of a user for supplied token
     *
     * @param {string} userAddress
     * @param {TYPE_AMOUNT} tokenId
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    getBalance(userAddress: string, tokenId: TYPE_AMOUNT, option?: ITransactionOption): Promise<string>;
    /**
     * check if a user is approved for all tokens
     *
     * @param {string} userAddress
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    isApprovedAll(userAddress: string, option?: ITransactionOption): Promise<boolean>;
    private approveAll_;
    /**
     * approve all tokens
     *
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    approveAll(option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * approve all tokens for mintable token
     *
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    approveAllForMintable(option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * deposit supplied amount of token for a user
     *
     * @param {POSERC1155DepositParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    deposit(param: POSERC1155DepositParam, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * deposit supplied amount of multiple token for user
     *
     * @param {POSERC1155DepositBatchParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    depositMany(param: POSERC1155DepositBatchParam, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * start withdraw process by burning the required amount for a token
     *
     * @param {string} tokenId
     * @param {TYPE_AMOUNT} amount
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawStart(tokenId: TYPE_AMOUNT, amount: TYPE_AMOUNT, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * start the withdraw process by burning the supplied amount of multiple token at a time
     *
     * @param {TYPE_AMOUNT[]} tokenIds
     * @param {TYPE_AMOUNT[]} amounts
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawStartMany(tokenIds: TYPE_AMOUNT[], amounts: TYPE_AMOUNT[], option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
    /**
     * exit the withdraw process and get the burned amount on root chain
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawExit(burnTransactionHash: string, option?: ITransactionOption): any;
    /**
     * exit the withdraw process and get the burned amount on root chain
     *
     * the process is faster because it uses proof api
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawExitFaster(burnTransactionHash: string, option?: ITransactionOption): any;
    /**
     * exit the withdraw process for many burned transaction and get the burned amount on root chain
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawExitMany(burnTransactionHash: string, option?: ITransactionOption): any;
    /**
     * exit the withdraw process for many burned transaction and get the burned amount on root chain
     *
     * the process is faster because it uses proof api
     *
     * @param {string} burnTransactionHash
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    withdrawExitFasterMany(burnTransactionHash: string, option?: ITransactionOption): any;
    /**
     * check if exit has been completed for a transaction hash
     *
     * @param {string} burnTxHash
     * @return {*}
     * @memberof ERC1155
     */
    isWithdrawExited(txHash: string): Promise<boolean>;
    /**
     * check if batch exit has been completed for a transaction hash
     *
     * @param {string} txHash
     * @return {*}
     * @memberof ERC1155
     */
    isWithdrawExitedMany(txHash: string): Promise<boolean>;
    /**
     * transfer the required amount of a token to another user
     *
     * @param {POSERC1155TransferParam} param
     * @param {ITransactionOption} [option]
     * @return {*}
     * @memberof ERC1155
     */
    transfer(param: POSERC1155TransferParam, option?: ITransactionOption): Promise<import("..").ITransactionWriteResult>;
}
