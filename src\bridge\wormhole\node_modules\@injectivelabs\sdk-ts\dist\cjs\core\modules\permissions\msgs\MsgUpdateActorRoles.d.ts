import { InjectivePermissionsV1Beta1Tx, InjectivePermissionsV1Beta1Permissions } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import { PermissionRoleActors } from './../../../../client/chain/types/permissions.js';
export declare namespace MsgUpdateActorRoles {
    interface Params {
        sender: string;
        denom: string;
        roleActorsToAdd: PermissionRoleActors[];
        roleActorsToRevoke: PermissionRoleActors[];
    }
    type Proto = InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles;
}
/**
 * @category Messages
 */
export default class MsgUpdateActorRoles extends MsgBase<MsgUpdateActorRoles.Params, MsgUpdateActorRoles.Proto> {
    static fromJSON(params: MsgUpdateActorRoles.Params): MsgUpdateActorRoles;
    toProto(): InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles;
    toData(): {
        sender: string;
        denom: string;
        roleActorsToAdd: InjectivePermissionsV1Beta1Permissions.RoleActors[];
        roleActorsToRevoke: InjectivePermissionsV1Beta1Permissions.RoleActors[];
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            denom: string;
            role_actors_to_add: InjectivePermissionsV1Beta1Permissions.RoleActors[];
            role_actors_to_revoke: InjectivePermissionsV1Beta1Permissions.RoleActors[];
        };
    };
    toWeb3Gw(): {
        sender: string;
        denom: string;
        role_actors_to_add: InjectivePermissionsV1Beta1Permissions.RoleActors[];
        role_actors_to_revoke: InjectivePermissionsV1Beta1Permissions.RoleActors[];
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectivePermissionsV1Beta1Tx.MsgUpdateActorRoles;
    };
    toBinary(): Uint8Array;
}
