"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcClient = void 0;
const IndexerGrpcMetaApi_js_1 = require("./grpc/IndexerGrpcMetaApi.js");
const IndexerGrpcAccountApi_js_1 = require("./grpc/IndexerGrpcAccountApi.js");
const IndexerGrpcPortfolioApi_js_1 = require("./grpc/IndexerGrpcPortfolioApi.js");
const IndexerGrpcAuctionApi_js_1 = require("./grpc/IndexerGrpcAuctionApi.js");
const IndexerGrpcExplorerApi_js_1 = require("./grpc/IndexerGrpcExplorerApi.js");
const IndexerGrpcOracleApi_js_1 = require("./grpc/IndexerGrpcOracleApi.js");
const IndexerGrpcInsuranceFundApi_js_1 = require("./grpc/IndexerGrpcInsuranceFundApi.js");
const IndexerGrpcDerivativesApi_js_1 = require("./grpc/IndexerGrpcDerivativesApi.js");
const IndexerGrpcSpotApi_js_1 = require("./grpc/IndexerGrpcSpotApi.js");
/**
 * @category Indexer Grpc API
 * @hidden
 */
class IndexerGrpcClient {
    account;
    accountPortfolio;
    auction;
    explorer;
    meta;
    oracle;
    insuranceFund;
    derivatives;
    spot;
    constructor(endpoint) {
        this.account = new IndexerGrpcAccountApi_js_1.IndexerGrpcAccountApi(endpoint);
        this.accountPortfolio = new IndexerGrpcPortfolioApi_js_1.IndexerGrpcAccountPortfolioApi(endpoint);
        this.auction = new IndexerGrpcAuctionApi_js_1.IndexerGrpcAuctionApi(endpoint);
        this.explorer = new IndexerGrpcExplorerApi_js_1.IndexerGrpcExplorerApi(endpoint);
        this.meta = new IndexerGrpcMetaApi_js_1.IndexerGrpcMetaApi(endpoint);
        this.oracle = new IndexerGrpcOracleApi_js_1.IndexerGrpcOracleApi(endpoint);
        this.insuranceFund = new IndexerGrpcInsuranceFundApi_js_1.IndexerGrpcInsuranceFundApi(endpoint);
        this.derivatives = new IndexerGrpcDerivativesApi_js_1.IndexerGrpcDerivativesApi(endpoint);
        this.spot = new IndexerGrpcSpotApi_js_1.IndexerGrpcSpotApi(endpoint);
    }
}
exports.IndexerGrpcClient = IndexerGrpcClient;
