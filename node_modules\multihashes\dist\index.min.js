/*! For license information please see index.min.js.LICENSE.txt */
!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.Multihashes=n():e.Multihashes=n()}(window,(function(){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var i in e)t.d(r,i,function(n){return e[n]}.bind(null,i));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=1)}([function(e,n,t){"use strict";(function(e){var r=t(4),i=t(5),o=t(6);function a(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function s(e,n){if(a()<n)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(n)).__proto__=f.prototype:(null===e&&(e=new f(n)),e.length=n),e}function f(e,n,t){if(!(f.TYPED_ARRAY_SUPPORT||this instanceof f))return new f(e,n,t);if("number"==typeof e){if("string"==typeof n)throw new Error("If encoding is specified then the first argument must be a string");return h(this,e)}return u(this,e,n,t)}function u(e,n,t,r){if("number"==typeof n)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&n instanceof ArrayBuffer?function(e,n,t,r){if(n.byteLength,t<0||n.byteLength<t)throw new RangeError("'offset' is out of bounds");if(n.byteLength<t+(r||0))throw new RangeError("'length' is out of bounds");n=void 0===t&&void 0===r?new Uint8Array(n):void 0===r?new Uint8Array(n,t):new Uint8Array(n,t,r);f.TYPED_ARRAY_SUPPORT?(e=n).__proto__=f.prototype:e=l(e,n);return e}(e,n,t,r):"string"==typeof n?function(e,n,t){"string"==typeof t&&""!==t||(t="utf8");if(!f.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var r=0|S(n,t),i=(e=s(e,r)).write(n,t);i!==r&&(e=e.slice(0,i));return e}(e,n,t):function(e,n){if(f.isBuffer(n)){var t=0|c(n.length);return 0===(e=s(e,t)).length||n.copy(e,0,0,t),e}if(n){if("undefined"!=typeof ArrayBuffer&&n.buffer instanceof ArrayBuffer||"length"in n)return"number"!=typeof n.length||(r=n.length)!=r?s(e,0):l(e,n);if("Buffer"===n.type&&o(n.data))return l(e,n.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,n)}function k(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,n){if(k(n),e=s(e,n<0?0:0|c(n)),!f.TYPED_ARRAY_SUPPORT)for(var t=0;t<n;++t)e[t]=0;return e}function l(e,n){var t=n.length<0?0:0|c(n.length);e=s(e,t);for(var r=0;r<t;r+=1)e[r]=255&n[r];return e}function c(e){if(e>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|e}function S(e,n){if(f.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var t=e.length;if(0===t)return 0;for(var r=!1;;)switch(n){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":case void 0:return z(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*t;case"hex":return t>>>1;case"base64":return N(e).length;default:if(r)return z(e).length;n=(""+n).toLowerCase(),r=!0}}function b(e,n,t){var r=!1;if((void 0===n||n<0)&&(n=0),n>this.length)return"";if((void 0===t||t>this.length)&&(t=this.length),t<=0)return"";if((t>>>=0)<=(n>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return P(this,n,t);case"utf8":case"utf-8":return R(this,n,t);case"ascii":return B(this,n,t);case"latin1":case"binary":return T(this,n,t);case"base64":return _(this,n,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,n,t);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function p(e,n,t){var r=e[n];e[n]=e[t],e[t]=r}function g(e,n,t,r,i){if(0===e.length)return-1;if("string"==typeof t?(r=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,isNaN(t)&&(t=i?0:e.length-1),t<0&&(t=e.length+t),t>=e.length){if(i)return-1;t=e.length-1}else if(t<0){if(!i)return-1;t=0}if("string"==typeof n&&(n=f.from(n,r)),f.isBuffer(n))return 0===n.length?-1:d(e,n,t,r,i);if("number"==typeof n)return n&=255,f.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,n,t):Uint8Array.prototype.lastIndexOf.call(e,n,t):d(e,[n],t,r,i);throw new TypeError("val must be string, number or Buffer")}function d(e,n,t,r,i){var o,a=1,s=e.length,f=n.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||n.length<2)return-1;a=2,s/=2,f/=2,t/=2}function u(e,n){return 1===a?e[n]:e.readUInt16BE(n*a)}if(i){var k=-1;for(o=t;o<s;o++)if(u(e,o)===u(n,-1===k?0:o-k)){if(-1===k&&(k=o),o-k+1===f)return k*a}else-1!==k&&(o-=o-k),k=-1}else for(t+f>s&&(t=s-f),o=t;o>=0;o--){for(var h=!0,l=0;l<f;l++)if(u(e,o+l)!==u(n,l)){h=!1;break}if(h)return o}return-1}function y(e,n,t,r){t=Number(t)||0;var i=e.length-t;r?(r=Number(r))>i&&(r=i):r=i;var o=n.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var a=0;a<r;++a){var s=parseInt(n.substr(2*a,2),16);if(isNaN(s))return a;e[t+a]=s}return a}function w(e,n,t,r){return q(z(n,e.length-t),e,t,r)}function m(e,n,t,r){return q(function(e){for(var n=[],t=0;t<e.length;++t)n.push(255&e.charCodeAt(t));return n}(n),e,t,r)}function v(e,n,t,r){return m(e,n,t,r)}function E(e,n,t,r){return q(N(n),e,t,r)}function A(e,n,t,r){return q(function(e,n){for(var t,r,i,o=[],a=0;a<e.length&&!((n-=2)<0);++a)t=e.charCodeAt(a),r=t>>8,i=t%256,o.push(i),o.push(r);return o}(n,e.length-t),e,t,r)}function _(e,n,t){return 0===n&&t===e.length?r.fromByteArray(e):r.fromByteArray(e.slice(n,t))}function R(e,n,t){t=Math.min(e.length,t);for(var r=[],i=n;i<t;){var o,a,s,f,u=e[i],k=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=t)switch(h){case 1:u<128&&(k=u);break;case 2:128==(192&(o=e[i+1]))&&(f=(31&u)<<6|63&o)>127&&(k=f);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(f=(15&u)<<12|(63&o)<<6|63&a)>2047&&(f<55296||f>57343)&&(k=f);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(f=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&f<1114112&&(k=f)}null===k?(k=65533,h=1):k>65535&&(k-=65536,r.push(k>>>10&1023|55296),k=56320|1023&k),r.push(k),i+=h}return function(e){var n=e.length;if(n<=4096)return String.fromCharCode.apply(String,e);var t="",r=0;for(;r<n;)t+=String.fromCharCode.apply(String,e.slice(r,r+=4096));return t}(r)}n.Buffer=f,n.SlowBuffer=function(e){+e!=e&&(e=0);return f.alloc(+e)},n.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(n){return!1}}(),n.kMaxLength=a(),f.poolSize=8192,f._augment=function(e){return e.__proto__=f.prototype,e},f.from=function(e,n,t){return u(null,e,n,t)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(e,n,t){return function(e,n,t,r){return k(n),n<=0?s(e,n):void 0!==t?"string"==typeof r?s(e,n).fill(t,r):s(e,n).fill(t):s(e,n)}(null,e,n,t)},f.allocUnsafe=function(e){return h(null,e)},f.allocUnsafeSlow=function(e){return h(null,e)},f.isBuffer=function(e){return!(null==e||!e._isBuffer)},f.compare=function(e,n){if(!f.isBuffer(e)||!f.isBuffer(n))throw new TypeError("Arguments must be Buffers");if(e===n)return 0;for(var t=e.length,r=n.length,i=0,o=Math.min(t,r);i<o;++i)if(e[i]!==n[i]){t=e[i],r=n[i];break}return t<r?-1:r<t?1:0},f.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(e,n){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return f.alloc(0);var t;if(void 0===n)for(n=0,t=0;t<e.length;++t)n+=e[t].length;var r=f.allocUnsafe(n),i=0;for(t=0;t<e.length;++t){var a=e[t];if(!f.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,i),i+=a.length}return r},f.byteLength=S,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var n=0;n<e;n+=2)p(this,n,n+1);return this},f.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var n=0;n<e;n+=4)p(this,n,n+3),p(this,n+1,n+2);return this},f.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var n=0;n<e;n+=8)p(this,n,n+7),p(this,n+1,n+6),p(this,n+2,n+5),p(this,n+3,n+4);return this},f.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?R(this,0,e):b.apply(this,arguments)},f.prototype.equals=function(e){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===f.compare(this,e)},f.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},f.prototype.compare=function(e,n,t,r,i){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===n&&(n=0),void 0===t&&(t=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),n<0||t>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&n>=t)return 0;if(r>=i)return-1;if(n>=t)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(r>>>=0),a=(t>>>=0)-(n>>>=0),s=Math.min(o,a),u=this.slice(r,i),k=e.slice(n,t),h=0;h<s;++h)if(u[h]!==k[h]){o=u[h],a=k[h];break}return o<a?-1:a<o?1:0},f.prototype.includes=function(e,n,t){return-1!==this.indexOf(e,n,t)},f.prototype.indexOf=function(e,n,t){return g(this,e,n,t,!0)},f.prototype.lastIndexOf=function(e,n,t){return g(this,e,n,t,!1)},f.prototype.write=function(e,n,t,r){if(void 0===n)r="utf8",t=this.length,n=0;else if(void 0===t&&"string"==typeof n)r=n,t=this.length,n=0;else{if(!isFinite(n))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");n|=0,isFinite(t)?(t|=0,void 0===r&&(r="utf8")):(r=t,t=void 0)}var i=this.length-n;if((void 0===t||t>i)&&(t=i),e.length>0&&(t<0||n<0)||n>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return y(this,e,n,t);case"utf8":case"utf-8":return w(this,e,n,t);case"ascii":return m(this,e,n,t);case"latin1":case"binary":return v(this,e,n,t);case"base64":return E(this,e,n,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,n,t);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function B(e,n,t){var r="";t=Math.min(e.length,t);for(var i=n;i<t;++i)r+=String.fromCharCode(127&e[i]);return r}function T(e,n,t){var r="";t=Math.min(e.length,t);for(var i=n;i<t;++i)r+=String.fromCharCode(e[i]);return r}function P(e,n,t){var r=e.length;(!n||n<0)&&(n=0),(!t||t<0||t>r)&&(t=r);for(var i="",o=n;o<t;++o)i+=D(e[o]);return i}function U(e,n,t){for(var r=e.slice(n,t),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function x(e,n,t){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+n>t)throw new RangeError("Trying to access beyond buffer length")}function O(e,n,t,r,i,o){if(!f.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(n>i||n<o)throw new RangeError('"value" argument is out of bounds');if(t+r>e.length)throw new RangeError("Index out of range")}function M(e,n,t,r){n<0&&(n=65535+n+1);for(var i=0,o=Math.min(e.length-t,2);i<o;++i)e[t+i]=(n&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function Y(e,n,t,r){n<0&&(n=4294967295+n+1);for(var i=0,o=Math.min(e.length-t,4);i<o;++i)e[t+i]=n>>>8*(r?i:3-i)&255}function C(e,n,t,r,i,o){if(t+r>e.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function I(e,n,t,r,o){return o||C(e,0,t,4),i.write(e,n,t,r,23,4),t+4}function L(e,n,t,r,o){return o||C(e,0,t,8),i.write(e,n,t,r,52,8),t+8}f.prototype.slice=function(e,n){var t,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(n=void 0===n?r:~~n)<0?(n+=r)<0&&(n=0):n>r&&(n=r),n<e&&(n=e),f.TYPED_ARRAY_SUPPORT)(t=this.subarray(e,n)).__proto__=f.prototype;else{var i=n-e;t=new f(i,void 0);for(var o=0;o<i;++o)t[o]=this[o+e]}return t},f.prototype.readUIntLE=function(e,n,t){e|=0,n|=0,t||x(e,n,this.length);for(var r=this[e],i=1,o=0;++o<n&&(i*=256);)r+=this[e+o]*i;return r},f.prototype.readUIntBE=function(e,n,t){e|=0,n|=0,t||x(e,n,this.length);for(var r=this[e+--n],i=1;n>0&&(i*=256);)r+=this[e+--n]*i;return r},f.prototype.readUInt8=function(e,n){return n||x(e,1,this.length),this[e]},f.prototype.readUInt16LE=function(e,n){return n||x(e,2,this.length),this[e]|this[e+1]<<8},f.prototype.readUInt16BE=function(e,n){return n||x(e,2,this.length),this[e]<<8|this[e+1]},f.prototype.readUInt32LE=function(e,n){return n||x(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},f.prototype.readUInt32BE=function(e,n){return n||x(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},f.prototype.readIntLE=function(e,n,t){e|=0,n|=0,t||x(e,n,this.length);for(var r=this[e],i=1,o=0;++o<n&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*n)),r},f.prototype.readIntBE=function(e,n,t){e|=0,n|=0,t||x(e,n,this.length);for(var r=n,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*n)),o},f.prototype.readInt8=function(e,n){return n||x(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},f.prototype.readInt16LE=function(e,n){n||x(e,2,this.length);var t=this[e]|this[e+1]<<8;return 32768&t?4294901760|t:t},f.prototype.readInt16BE=function(e,n){n||x(e,2,this.length);var t=this[e+1]|this[e]<<8;return 32768&t?4294901760|t:t},f.prototype.readInt32LE=function(e,n){return n||x(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},f.prototype.readInt32BE=function(e,n){return n||x(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},f.prototype.readFloatLE=function(e,n){return n||x(e,4,this.length),i.read(this,e,!0,23,4)},f.prototype.readFloatBE=function(e,n){return n||x(e,4,this.length),i.read(this,e,!1,23,4)},f.prototype.readDoubleLE=function(e,n){return n||x(e,8,this.length),i.read(this,e,!0,52,8)},f.prototype.readDoubleBE=function(e,n){return n||x(e,8,this.length),i.read(this,e,!1,52,8)},f.prototype.writeUIntLE=function(e,n,t,r){(e=+e,n|=0,t|=0,r)||O(this,e,n,t,Math.pow(2,8*t)-1,0);var i=1,o=0;for(this[n]=255&e;++o<t&&(i*=256);)this[n+o]=e/i&255;return n+t},f.prototype.writeUIntBE=function(e,n,t,r){(e=+e,n|=0,t|=0,r)||O(this,e,n,t,Math.pow(2,8*t)-1,0);var i=t-1,o=1;for(this[n+i]=255&e;--i>=0&&(o*=256);)this[n+i]=e/o&255;return n+t},f.prototype.writeUInt8=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,1,255,0),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[n]=255&e,n+1},f.prototype.writeUInt16LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8):M(this,e,n,!0),n+2},f.prototype.writeUInt16BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=255&e):M(this,e,n,!1),n+2},f.prototype.writeUInt32LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[n+3]=e>>>24,this[n+2]=e>>>16,this[n+1]=e>>>8,this[n]=255&e):Y(this,e,n,!0),n+4},f.prototype.writeUInt32BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=255&e):Y(this,e,n,!1),n+4},f.prototype.writeIntLE=function(e,n,t,r){if(e=+e,n|=0,!r){var i=Math.pow(2,8*t-1);O(this,e,n,t,i-1,-i)}var o=0,a=1,s=0;for(this[n]=255&e;++o<t&&(a*=256);)e<0&&0===s&&0!==this[n+o-1]&&(s=1),this[n+o]=(e/a>>0)-s&255;return n+t},f.prototype.writeIntBE=function(e,n,t,r){if(e=+e,n|=0,!r){var i=Math.pow(2,8*t-1);O(this,e,n,t,i-1,-i)}var o=t-1,a=1,s=0;for(this[n+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[n+o+1]&&(s=1),this[n+o]=(e/a>>0)-s&255;return n+t},f.prototype.writeInt8=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,1,127,-128),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[n]=255&e,n+1},f.prototype.writeInt16LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8):M(this,e,n,!0),n+2},f.prototype.writeInt16BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[n]=e>>>8,this[n+1]=255&e):M(this,e,n,!1),n+2},f.prototype.writeInt32LE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[n]=255&e,this[n+1]=e>>>8,this[n+2]=e>>>16,this[n+3]=e>>>24):Y(this,e,n,!0),n+4},f.prototype.writeInt32BE=function(e,n,t){return e=+e,n|=0,t||O(this,e,n,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),f.TYPED_ARRAY_SUPPORT?(this[n]=e>>>24,this[n+1]=e>>>16,this[n+2]=e>>>8,this[n+3]=255&e):Y(this,e,n,!1),n+4},f.prototype.writeFloatLE=function(e,n,t){return I(this,e,n,!0,t)},f.prototype.writeFloatBE=function(e,n,t){return I(this,e,n,!1,t)},f.prototype.writeDoubleLE=function(e,n,t){return L(this,e,n,!0,t)},f.prototype.writeDoubleBE=function(e,n,t){return L(this,e,n,!1,t)},f.prototype.copy=function(e,n,t,r){if(t||(t=0),r||0===r||(r=this.length),n>=e.length&&(n=e.length),n||(n=0),r>0&&r<t&&(r=t),r===t)return 0;if(0===e.length||0===this.length)return 0;if(n<0)throw new RangeError("targetStart out of bounds");if(t<0||t>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-n<r-t&&(r=e.length-n+t);var i,o=r-t;if(this===e&&t<n&&n<r)for(i=o-1;i>=0;--i)e[i+n]=this[i+t];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+n]=this[i+t];else Uint8Array.prototype.set.call(e,this.subarray(t,t+o),n);return o},f.prototype.fill=function(e,n,t,r){if("string"==typeof e){if("string"==typeof n?(r=n,n=0,t=this.length):"string"==typeof t&&(r=t,t=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!f.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(n<0||this.length<n||this.length<t)throw new RangeError("Out of range index");if(t<=n)return this;var o;if(n>>>=0,t=void 0===t?this.length:t>>>0,e||(e=0),"number"==typeof e)for(o=n;o<t;++o)this[o]=e;else{var a=f.isBuffer(e)?e:z(new f(e,r).toString()),s=a.length;for(o=0;o<t-n;++o)this[o+n]=a[o%s]}return this};var j=/[^+\/0-9A-Za-z-_]/g;function D(e){return e<16?"0"+e.toString(16):e.toString(16)}function z(e,n){var t;n=n||1/0;for(var r=e.length,i=null,o=[],a=0;a<r;++a){if((t=e.charCodeAt(a))>55295&&t<57344){if(!i){if(t>56319){(n-=3)>-1&&o.push(239,191,189);continue}if(a+1===r){(n-=3)>-1&&o.push(239,191,189);continue}i=t;continue}if(t<56320){(n-=3)>-1&&o.push(239,191,189),i=t;continue}t=65536+(i-55296<<10|t-56320)}else i&&(n-=3)>-1&&o.push(239,191,189);if(i=null,t<128){if((n-=1)<0)break;o.push(t)}else if(t<2048){if((n-=2)<0)break;o.push(t>>6|192,63&t|128)}else if(t<65536){if((n-=3)<0)break;o.push(t>>12|224,t>>6&63|128,63&t|128)}else{if(!(t<1114112))throw new Error("Invalid code point");if((n-=4)<0)break;o.push(t>>18|240,t>>12&63|128,t>>6&63|128,63&t|128)}}return o}function N(e){return r.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(j,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function q(e,n,t,r){for(var i=0;i<r&&!(i+t>=n.length||i>=e.length);++i)n[i+t]=e[i];return i}}).call(this,t(3))},function(e,n,t){e.exports=t(2)},function(e,n,t){"use strict";const{Buffer:r}=t(0),i=t(7),o=t(15),a=t(19);function s(e){n.decode(e)}n.names=a.names,n.codes=a.codes,n.defaultLengths=a.defaultLengths,n.toHexString=function(e){if(!r.isBuffer(e))throw new Error("must be passed a buffer");return e.toString("hex")},n.fromHexString=function(e){return r.from(e,"hex")},n.toB58String=function(e){if(!r.isBuffer(e))throw new Error("must be passed a buffer");return i.encode("base58btc",e).toString().slice(1)},n.fromB58String=function(e){let n=e;return r.isBuffer(e)&&(n=e.toString()),i.decode("z"+n)},n.decode=function(e){if(!r.isBuffer(e))throw new Error("multihash must be a Buffer");if(e.length<2)throw new Error("multihash too short. must be > 2 bytes.");const t=o.decode(e);if(!n.isValidCode(t))throw new Error("multihash unknown function code: 0x".concat(t.toString(16)));e=e.slice(o.decode.bytes);const i=o.decode(e);if(i<0)throw new Error("multihash invalid length: ".concat(i));if((e=e.slice(o.decode.bytes)).length!==i)throw new Error("multihash length inconsistent: 0x".concat(e.toString("hex")));return{code:t,name:a.codes[t],length:i,digest:e}},n.encode=function(e,t,i){if(!e||void 0===t)throw new Error("multihash encode requires at least two args: digest, code");const a=n.coerceCode(t);if(!r.isBuffer(e))throw new Error("digest should be a Buffer");if(null==i&&(i=e.length),i&&e.length!==i)throw new Error("digest length should be equal to specified length.");return r.concat([r.from(o.encode(a)),r.from(o.encode(i)),e])},n.coerceCode=function(e){let t=e;if("string"==typeof e){if(void 0===a.names[e])throw new Error("Unrecognized hash function named: ".concat(e));t=a.names[e]}if("number"!=typeof t)throw new Error("Hash function code should be a number. Got: ".concat(t));if(void 0===a.codes[t]&&!n.isAppCode(t))throw new Error("Unrecognized function code: ".concat(t));return t},n.isAppCode=function(e){return e>0&&e<16},n.isValidCode=function(e){return!!n.isAppCode(e)||!!a.codes[e]},n.validate=s,n.prefix=function(e){return s(e),e.slice(0,2)}},function(e,n,t){"use strict";var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(i){"object"==typeof window&&(r=window)}e.exports=r},function(e,n,t){"use strict";n.byteLength=function(e){var n=u(e),t=n[0],r=n[1];return 3*(t+r)/4-r},n.toByteArray=function(e){var n,t,r=u(e),a=r[0],s=r[1],f=new o(function(e,n,t){return 3*(n+t)/4-t}(0,a,s)),k=0,h=s>0?a-4:a;for(t=0;t<h;t+=4)n=i[e.charCodeAt(t)]<<18|i[e.charCodeAt(t+1)]<<12|i[e.charCodeAt(t+2)]<<6|i[e.charCodeAt(t+3)],f[k++]=n>>16&255,f[k++]=n>>8&255,f[k++]=255&n;2===s&&(n=i[e.charCodeAt(t)]<<2|i[e.charCodeAt(t+1)]>>4,f[k++]=255&n);1===s&&(n=i[e.charCodeAt(t)]<<10|i[e.charCodeAt(t+1)]<<4|i[e.charCodeAt(t+2)]>>2,f[k++]=n>>8&255,f[k++]=255&n);return f},n.fromByteArray=function(e){for(var n,t=e.length,i=t%3,o=[],a=0,s=t-i;a<s;a+=16383)o.push(k(e,a,a+16383>s?s:a+16383));1===i?(n=e[t-1],o.push(r[n>>2]+r[n<<4&63]+"==")):2===i&&(n=(e[t-2]<<8)+e[t-1],o.push(r[n>>10]+r[n>>4&63]+r[n<<2&63]+"="));return o.join("")};for(var r=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,f=a.length;s<f;++s)r[s]=a[s],i[a.charCodeAt(s)]=s;function u(e){var n=e.length;if(n%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=e.indexOf("=");return-1===t&&(t=n),[t,t===n?0:4-t%4]}function k(e,n,t){for(var i,o,a=[],s=n;s<t;s+=3)i=(e[s]<<16&16711680)+(e[s+1]<<8&65280)+(255&e[s+2]),a.push(r[(o=i)>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return a.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(e,n,t){"use strict";n.read=function(e,n,t,r,i){var o,a,s=8*i-r-1,f=(1<<s)-1,u=f>>1,k=-7,h=t?i-1:0,l=t?-1:1,c=e[n+h];for(h+=l,o=c&(1<<-k)-1,c>>=-k,k+=s;k>0;o=256*o+e[n+h],h+=l,k-=8);for(a=o&(1<<-k)-1,o>>=-k,k+=r;k>0;a=256*a+e[n+h],h+=l,k-=8);if(0===o)o=1-u;else{if(o===f)return a?NaN:1/0*(c?-1:1);a+=Math.pow(2,r),o-=u}return(c?-1:1)*a*Math.pow(2,o-r)},n.write=function(e,n,t,r,i,o){var a,s,f,u=8*o-i-1,k=(1<<u)-1,h=k>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,c=r?0:o-1,S=r?1:-1,b=n<0||0===n&&1/n<0?1:0;for(n=Math.abs(n),isNaN(n)||n===1/0?(s=isNaN(n)?1:0,a=k):(a=Math.floor(Math.log(n)/Math.LN2),n*(f=Math.pow(2,-a))<1&&(a--,f*=2),(n+=a+h>=1?l/f:l*Math.pow(2,1-h))*f>=2&&(a++,f/=2),a+h>=k?(s=0,a=k):a+h>=1?(s=(n*f-1)*Math.pow(2,i),a+=h):(s=n*Math.pow(2,h-1)*Math.pow(2,i),a=0));i>=8;e[t+c]=255&s,c+=S,s/=256,i-=8);for(a=a<<i|s,u+=i;u>0;e[t+c]=255&a,c+=S,a/=256,u-=8);e[t+c-S]|=128*b}},function(e,n,t){"use strict";var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},function(e,n,t){"use strict";const{Buffer:r}=t(0),i=t(8);function o(e,n){if(!n)throw new Error("requires an encoded buffer");const t=a(e),i=r.from(t.code);return function(e,n){a(e).decode(n.toString())}(t.name,n),r.concat([i,n])}function a(e){let n;if(i.names[e])n=i.names[e];else{if(!i.codes[e])throw new Error("Unsupported encoding");n=i.codes[e]}if(!n.isImplemented())throw new Error("Base "+e+" is not implemented yet");return n}(n=e.exports=o).encode=function(e,n){const t=a(e);return o(t.name,r.from(t.encode(n)))},n.decode=function(e){r.isBuffer(e)&&(e=e.toString());const n=e.substring(0,1);"string"==typeof(e=e.substring(1,e.length))&&(e=r.from(e));const t=a(n);return r.from(t.decode(e.toString()))},n.isEncoded=function(e){r.isBuffer(e)&&(e=e.toString());if("[object String]"!==Object.prototype.toString.call(e))return!1;const n=e.substring(0,1);try{return a(n).name}catch(t){return!1}},n.names=Object.freeze(Object.keys(i.names)),n.codes=Object.freeze(Object.keys(i.codes))},function(e,n,t){"use strict";const r=t(9),i=t(10),o=t(12),a=t(13),s=t(14),f=[["base1","1","","1"],["base2","0",i,"01"],["base8","7",i,"01234567"],["base10","9",i,"0123456789"],["base16","f",o,"0123456789abcdef"],["base32","b",a,"abcdefghijklmnopqrstuvwxyz234567"],["base32pad","c",a,"abcdefghijklmnopqrstuvwxyz234567="],["base32hex","v",a,"0123456789abcdefghijklmnopqrstuv"],["base32hexpad","t",a,"0123456789abcdefghijklmnopqrstuv="],["base32z","h",a,"ybndrfg8ejkmcpqxot1uwisza345h769"],["base58flickr","Z",i,"**********************************************************"],["base58btc","z",i,"**********************************************************"],["base64","m",s,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"],["base64pad","M",s,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="],["base64url","u",s,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"],["base64urlpad","U",s,"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_="]],u=f.reduce((e,n)=>(e[n[0]]=new r(n[0],n[1],n[2],n[3]),e),{}),k=f.reduce((e,n)=>(e[n[1]]=u[n[0]],e),{});e.exports={names:u,codes:k}},function(e,n,t){"use strict";e.exports=class{constructor(e,n,t,r){this.name=e,this.code=n,this.alphabet=r,t&&r&&(this.engine=t(r))}encode(e){return this.engine.encode(e)}decode(e){return this.engine.decode(e)}isImplemented(){return this.engine}}},function(e,n,t){"use strict";var r=t(11).Buffer;e.exports=function(e){if(e.length>=255)throw new TypeError("Alphabet too long");for(var n=new Uint8Array(256),t=0;t<n.length;t++)n[t]=255;for(var i=0;i<e.length;i++){var o=e.charAt(i),a=o.charCodeAt(0);if(255!==n[a])throw new TypeError(o+" is ambiguous");n[a]=i}var s=e.length,f=e.charAt(0),u=Math.log(s)/Math.log(256),k=Math.log(256)/Math.log(s);function h(e){if("string"!=typeof e)throw new TypeError("Expected String");if(0===e.length)return r.alloc(0);var t=0;if(" "!==e[t]){for(var i=0,o=0;e[t]===f;)i++,t++;for(var a=(e.length-t)*u+1>>>0,k=new Uint8Array(a);e[t];){var h=n[e.charCodeAt(t)];if(255===h)return;for(var l=0,c=a-1;(0!==h||l<o)&&-1!==c;c--,l++)h+=s*k[c]>>>0,k[c]=h%256>>>0,h=h/256>>>0;if(0!==h)throw new Error("Non-zero carry");o=l,t++}if(" "!==e[t]){for(var S=a-o;S!==a&&0===k[S];)S++;var b=r.allocUnsafe(i+(a-S));b.fill(0,0,i);for(var p=i;S!==a;)b[p++]=k[S++];return b}}}return{encode:function(n){if((Array.isArray(n)||n instanceof Uint8Array)&&(n=r.from(n)),!r.isBuffer(n))throw new TypeError("Expected Buffer");if(0===n.length)return"";for(var t=0,i=0,o=0,a=n.length;o!==a&&0===n[o];)o++,t++;for(var u=(a-o)*k+1>>>0,h=new Uint8Array(u);o!==a;){for(var l=n[o],c=0,S=u-1;(0!==l||c<i)&&-1!==S;S--,c++)l+=256*h[S]>>>0,h[S]=l%s>>>0,l=l/s>>>0;if(0!==l)throw new Error("Non-zero carry");i=c,o++}for(var b=u-i;b!==u&&0===h[b];)b++;for(var p=f.repeat(t);b<u;++b)p+=e.charAt(h[b]);return p},decodeUnsafe:h,decode:function(e){var n=h(e);if(n)return n;throw new Error("Non-base"+s+" character")}}}},function(e,n,t){"use strict";var r=t(0),i=r.Buffer;function o(e,n){for(var t in e)n[t]=e[t]}function a(e,n,t){return i(e,n,t)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=r:(o(r,n),n.Buffer=a),a.prototype=Object.create(i.prototype),o(i,a),a.from=function(e,n,t){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,n,t)},a.alloc=function(e,n,t){if("number"!=typeof e)throw new TypeError("Argument must be a number");var r=i(e);return void 0!==n?"string"==typeof t?r.fill(n,t):r.fill(n):r.fill(0),r},a.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return r.SlowBuffer(e)}},function(e,n,t){"use strict";const{Buffer:r}=t(0);e.exports=function(e){return{encode:e=>"string"==typeof e?r.from(e).toString("hex"):e.toString("hex"),decode(n){for(const t of n)if(e.indexOf(t)<0)throw new Error("invalid base16 character");return r.from(n,"hex")}}}},function(e,n,t){"use strict";function r(e,n){const t=e.byteLength,r=new Uint8Array(e),i=n.indexOf("=")===n.length-1;i&&(n=n.substring(0,n.length-1));let o=0,a=0,s="";for(let f=0;f<t;f++)for(a=a<<8|r[f],o+=8;o>=5;)s+=n[a>>>o-5&31],o-=5;if(o>0&&(s+=n[a<<5-o&31]),i)for(;s.length%8!=0;)s+="=";return s}e.exports=function(e){return{encode:n=>r("string"==typeof n?Uint8Array.from(n):n,e),decode(n){for(const t of n)if(e.indexOf(t)<0)throw new Error("invalid base32 character");return function(e,n){const t=(e=e.replace(new RegExp("=","g"),"")).length;let r=0,i=0,o=0;const a=new Uint8Array(5*t/8|0);for(let s=0;s<t;s++)i=i<<5|n.indexOf(e[s]),r+=5,r>=8&&(a[o++]=i>>>r-8&255,r-=8);return a.buffer}(n,e)}}}},function(e,n,t){"use strict";const{Buffer:r}=t(0);e.exports=function(e){const n=e.indexOf("=")>-1,t=e.indexOf("-")>-1&&e.indexOf("_")>-1;return{encode(e){let i="";i="string"==typeof e?r.from(e).toString("base64"):e.toString("base64"),t&&(i=i.replace(/\+/g,"-").replace(/\//g,"_"));const o=i.indexOf("=");return o>0&&!n&&(i=i.substring(0,o)),i},decode(n){for(const t of n)if(e.indexOf(t)<0)throw new Error("invalid base64 character");return r.from(n,"base64")}}}},function(e,n,t){"use strict";e.exports={encode:t(16),decode:t(17),encodingLength:t(18)}},function(e,n,t){"use strict";e.exports=function e(n,t,i){t=t||[];var o=i=i||0;for(;n>=r;)t[i++]=255&n|128,n/=128;for(;-128&n;)t[i++]=255&n|128,n>>>=7;return t[i]=0|n,e.bytes=i-o+1,t};var r=Math.pow(2,31)},function(e,n,t){"use strict";e.exports=function e(n,t){var r,i=0,o=0,a=t=t||0,s=n.length;do{if(a>=s)throw e.bytes=0,new RangeError("Could not decode varint");r=n[a++],i+=o<28?(127&r)<<o:(127&r)*Math.pow(2,o),o+=7}while(r>=128);return e.bytes=a-t,i}},function(e,n,t){"use strict";var r=Math.pow(2,7),i=Math.pow(2,14),o=Math.pow(2,21),a=Math.pow(2,28),s=Math.pow(2,35),f=Math.pow(2,42),u=Math.pow(2,49),k=Math.pow(2,56),h=Math.pow(2,63);e.exports=function(e){return e<r?1:e<i?2:e<o?3:e<a?4:e<s?5:e<f?6:e<u?7:e<k?8:e<h?9:10}},function(e,n,t){"use strict";n.names=Object.freeze({identity:0,sha1:17,"sha2-256":18,"sha2-512":19,"dbl-sha2-256":86,"sha3-224":23,"sha3-256":22,"sha3-384":21,"sha3-512":20,"shake-128":24,"shake-256":25,"keccak-224":26,"keccak-256":27,"keccak-384":28,"keccak-512":29,"murmur3-128":34,"murmur3-32":35,md4:212,md5:213,"blake2b-8":45569,"blake2b-16":45570,"blake2b-24":45571,"blake2b-32":45572,"blake2b-40":45573,"blake2b-48":45574,"blake2b-56":45575,"blake2b-64":45576,"blake2b-72":45577,"blake2b-80":45578,"blake2b-88":45579,"blake2b-96":45580,"blake2b-104":45581,"blake2b-112":45582,"blake2b-120":45583,"blake2b-128":45584,"blake2b-136":45585,"blake2b-144":45586,"blake2b-152":45587,"blake2b-160":45588,"blake2b-168":45589,"blake2b-176":45590,"blake2b-184":45591,"blake2b-192":45592,"blake2b-200":45593,"blake2b-208":45594,"blake2b-216":45595,"blake2b-224":45596,"blake2b-232":45597,"blake2b-240":45598,"blake2b-248":45599,"blake2b-256":45600,"blake2b-264":45601,"blake2b-272":45602,"blake2b-280":45603,"blake2b-288":45604,"blake2b-296":45605,"blake2b-304":45606,"blake2b-312":45607,"blake2b-320":45608,"blake2b-328":45609,"blake2b-336":45610,"blake2b-344":45611,"blake2b-352":45612,"blake2b-360":45613,"blake2b-368":45614,"blake2b-376":45615,"blake2b-384":45616,"blake2b-392":45617,"blake2b-400":45618,"blake2b-408":45619,"blake2b-416":45620,"blake2b-424":45621,"blake2b-432":45622,"blake2b-440":45623,"blake2b-448":45624,"blake2b-456":45625,"blake2b-464":45626,"blake2b-472":45627,"blake2b-480":45628,"blake2b-488":45629,"blake2b-496":45630,"blake2b-504":45631,"blake2b-512":45632,"blake2s-8":45633,"blake2s-16":45634,"blake2s-24":45635,"blake2s-32":45636,"blake2s-40":45637,"blake2s-48":45638,"blake2s-56":45639,"blake2s-64":45640,"blake2s-72":45641,"blake2s-80":45642,"blake2s-88":45643,"blake2s-96":45644,"blake2s-104":45645,"blake2s-112":45646,"blake2s-120":45647,"blake2s-128":45648,"blake2s-136":45649,"blake2s-144":45650,"blake2s-152":45651,"blake2s-160":45652,"blake2s-168":45653,"blake2s-176":45654,"blake2s-184":45655,"blake2s-192":45656,"blake2s-200":45657,"blake2s-208":45658,"blake2s-216":45659,"blake2s-224":45660,"blake2s-232":45661,"blake2s-240":45662,"blake2s-248":45663,"blake2s-256":45664,"Skein256-8":45825,"Skein256-16":45826,"Skein256-24":45827,"Skein256-32":45828,"Skein256-40":45829,"Skein256-48":45830,"Skein256-56":45831,"Skein256-64":45832,"Skein256-72":45833,"Skein256-80":45834,"Skein256-88":45835,"Skein256-96":45836,"Skein256-104":45837,"Skein256-112":45838,"Skein256-120":45839,"Skein256-128":45840,"Skein256-136":45841,"Skein256-144":45842,"Skein256-152":45843,"Skein256-160":45844,"Skein256-168":45845,"Skein256-176":45846,"Skein256-184":45847,"Skein256-192":45848,"Skein256-200":45849,"Skein256-208":45850,"Skein256-216":45851,"Skein256-224":45852,"Skein256-232":45853,"Skein256-240":45854,"Skein256-248":45855,"Skein256-256":45856,"Skein512-8":45857,"Skein512-16":45858,"Skein512-24":45859,"Skein512-32":45860,"Skein512-40":45861,"Skein512-48":45862,"Skein512-56":45863,"Skein512-64":45864,"Skein512-72":45865,"Skein512-80":45866,"Skein512-88":45867,"Skein512-96":45868,"Skein512-104":45869,"Skein512-112":45870,"Skein512-120":45871,"Skein512-128":45872,"Skein512-136":45873,"Skein512-144":45874,"Skein512-152":45875,"Skein512-160":45876,"Skein512-168":45877,"Skein512-176":45878,"Skein512-184":45879,"Skein512-192":45880,"Skein512-200":45881,"Skein512-208":45882,"Skein512-216":45883,"Skein512-224":45884,"Skein512-232":45885,"Skein512-240":45886,"Skein512-248":45887,"Skein512-256":45888,"Skein512-264":45889,"Skein512-272":45890,"Skein512-280":45891,"Skein512-288":45892,"Skein512-296":45893,"Skein512-304":45894,"Skein512-312":45895,"Skein512-320":45896,"Skein512-328":45897,"Skein512-336":45898,"Skein512-344":45899,"Skein512-352":45900,"Skein512-360":45901,"Skein512-368":45902,"Skein512-376":45903,"Skein512-384":45904,"Skein512-392":45905,"Skein512-400":45906,"Skein512-408":45907,"Skein512-416":45908,"Skein512-424":45909,"Skein512-432":45910,"Skein512-440":45911,"Skein512-448":45912,"Skein512-456":45913,"Skein512-464":45914,"Skein512-472":45915,"Skein512-480":45916,"Skein512-488":45917,"Skein512-496":45918,"Skein512-504":45919,"Skein512-512":45920,"Skein1024-8":45921,"Skein1024-16":45922,"Skein1024-24":45923,"Skein1024-32":45924,"Skein1024-40":45925,"Skein1024-48":45926,"Skein1024-56":45927,"Skein1024-64":45928,"Skein1024-72":45929,"Skein1024-80":45930,"Skein1024-88":45931,"Skein1024-96":45932,"Skein1024-104":45933,"Skein1024-112":45934,"Skein1024-120":45935,"Skein1024-128":45936,"Skein1024-136":45937,"Skein1024-144":45938,"Skein1024-152":45939,"Skein1024-160":45940,"Skein1024-168":45941,"Skein1024-176":45942,"Skein1024-184":45943,"Skein1024-192":45944,"Skein1024-200":45945,"Skein1024-208":45946,"Skein1024-216":45947,"Skein1024-224":45948,"Skein1024-232":45949,"Skein1024-240":45950,"Skein1024-248":45951,"Skein1024-256":45952,"Skein1024-264":45953,"Skein1024-272":45954,"Skein1024-280":45955,"Skein1024-288":45956,"Skein1024-296":45957,"Skein1024-304":45958,"Skein1024-312":45959,"Skein1024-320":45960,"Skein1024-328":45961,"Skein1024-336":45962,"Skein1024-344":45963,"Skein1024-352":45964,"Skein1024-360":45965,"Skein1024-368":45966,"Skein1024-376":45967,"Skein1024-384":45968,"Skein1024-392":45969,"Skein1024-400":45970,"Skein1024-408":45971,"Skein1024-416":45972,"Skein1024-424":45973,"Skein1024-432":45974,"Skein1024-440":45975,"Skein1024-448":45976,"Skein1024-456":45977,"Skein1024-464":45978,"Skein1024-472":45979,"Skein1024-480":45980,"Skein1024-488":45981,"Skein1024-496":45982,"Skein1024-504":45983,"Skein1024-512":45984,"Skein1024-520":45985,"Skein1024-528":45986,"Skein1024-536":45987,"Skein1024-544":45988,"Skein1024-552":45989,"Skein1024-560":45990,"Skein1024-568":45991,"Skein1024-576":45992,"Skein1024-584":45993,"Skein1024-592":45994,"Skein1024-600":45995,"Skein1024-608":45996,"Skein1024-616":45997,"Skein1024-624":45998,"Skein1024-632":45999,"Skein1024-640":46e3,"Skein1024-648":46001,"Skein1024-656":46002,"Skein1024-664":46003,"Skein1024-672":46004,"Skein1024-680":46005,"Skein1024-688":46006,"Skein1024-696":46007,"Skein1024-704":46008,"Skein1024-712":46009,"Skein1024-720":46010,"Skein1024-728":46011,"Skein1024-736":46012,"Skein1024-744":46013,"Skein1024-752":46014,"Skein1024-760":46015,"Skein1024-768":46016,"Skein1024-776":46017,"Skein1024-784":46018,"Skein1024-792":46019,"Skein1024-800":46020,"Skein1024-808":46021,"Skein1024-816":46022,"Skein1024-824":46023,"Skein1024-832":46024,"Skein1024-840":46025,"Skein1024-848":46026,"Skein1024-856":46027,"Skein1024-864":46028,"Skein1024-872":46029,"Skein1024-880":46030,"Skein1024-888":46031,"Skein1024-896":46032,"Skein1024-904":46033,"Skein1024-912":46034,"Skein1024-920":46035,"Skein1024-928":46036,"Skein1024-936":46037,"Skein1024-944":46038,"Skein1024-952":46039,"Skein1024-960":46040,"Skein1024-968":46041,"Skein1024-976":46042,"Skein1024-984":46043,"Skein1024-992":46044,"Skein1024-1000":46045,"Skein1024-1008":46046,"Skein1024-1016":46047,"Skein1024-1024":46048}),n.codes=Object.freeze({0:"identity",17:"sha1",18:"sha2-256",19:"sha2-512",86:"dbl-sha2-256",23:"sha3-224",22:"sha3-256",21:"sha3-384",20:"sha3-512",24:"shake-128",25:"shake-256",26:"keccak-224",27:"keccak-256",28:"keccak-384",29:"keccak-512",34:"murmur3-128",35:"murmur3-32",212:"md4",213:"md5",45569:"blake2b-8",45570:"blake2b-16",45571:"blake2b-24",45572:"blake2b-32",45573:"blake2b-40",45574:"blake2b-48",45575:"blake2b-56",45576:"blake2b-64",45577:"blake2b-72",45578:"blake2b-80",45579:"blake2b-88",45580:"blake2b-96",45581:"blake2b-104",45582:"blake2b-112",45583:"blake2b-120",45584:"blake2b-128",45585:"blake2b-136",45586:"blake2b-144",45587:"blake2b-152",45588:"blake2b-160",45589:"blake2b-168",45590:"blake2b-176",45591:"blake2b-184",45592:"blake2b-192",45593:"blake2b-200",45594:"blake2b-208",45595:"blake2b-216",45596:"blake2b-224",45597:"blake2b-232",45598:"blake2b-240",45599:"blake2b-248",45600:"blake2b-256",45601:"blake2b-264",45602:"blake2b-272",45603:"blake2b-280",45604:"blake2b-288",45605:"blake2b-296",45606:"blake2b-304",45607:"blake2b-312",45608:"blake2b-320",45609:"blake2b-328",45610:"blake2b-336",45611:"blake2b-344",45612:"blake2b-352",45613:"blake2b-360",45614:"blake2b-368",45615:"blake2b-376",45616:"blake2b-384",45617:"blake2b-392",45618:"blake2b-400",45619:"blake2b-408",45620:"blake2b-416",45621:"blake2b-424",45622:"blake2b-432",45623:"blake2b-440",45624:"blake2b-448",45625:"blake2b-456",45626:"blake2b-464",45627:"blake2b-472",45628:"blake2b-480",45629:"blake2b-488",45630:"blake2b-496",45631:"blake2b-504",45632:"blake2b-512",45633:"blake2s-8",45634:"blake2s-16",45635:"blake2s-24",45636:"blake2s-32",45637:"blake2s-40",45638:"blake2s-48",45639:"blake2s-56",45640:"blake2s-64",45641:"blake2s-72",45642:"blake2s-80",45643:"blake2s-88",45644:"blake2s-96",45645:"blake2s-104",45646:"blake2s-112",45647:"blake2s-120",45648:"blake2s-128",45649:"blake2s-136",45650:"blake2s-144",45651:"blake2s-152",45652:"blake2s-160",45653:"blake2s-168",45654:"blake2s-176",45655:"blake2s-184",45656:"blake2s-192",45657:"blake2s-200",45658:"blake2s-208",45659:"blake2s-216",45660:"blake2s-224",45661:"blake2s-232",45662:"blake2s-240",45663:"blake2s-248",45664:"blake2s-256",45825:"Skein256-8",45826:"Skein256-16",45827:"Skein256-24",45828:"Skein256-32",45829:"Skein256-40",45830:"Skein256-48",45831:"Skein256-56",45832:"Skein256-64",45833:"Skein256-72",45834:"Skein256-80",45835:"Skein256-88",45836:"Skein256-96",45837:"Skein256-104",45838:"Skein256-112",45839:"Skein256-120",45840:"Skein256-128",45841:"Skein256-136",45842:"Skein256-144",45843:"Skein256-152",45844:"Skein256-160",45845:"Skein256-168",45846:"Skein256-176",45847:"Skein256-184",45848:"Skein256-192",45849:"Skein256-200",45850:"Skein256-208",45851:"Skein256-216",45852:"Skein256-224",45853:"Skein256-232",45854:"Skein256-240",45855:"Skein256-248",45856:"Skein256-256",45857:"Skein512-8",45858:"Skein512-16",45859:"Skein512-24",45860:"Skein512-32",45861:"Skein512-40",45862:"Skein512-48",45863:"Skein512-56",45864:"Skein512-64",45865:"Skein512-72",45866:"Skein512-80",45867:"Skein512-88",45868:"Skein512-96",45869:"Skein512-104",45870:"Skein512-112",45871:"Skein512-120",45872:"Skein512-128",45873:"Skein512-136",45874:"Skein512-144",45875:"Skein512-152",45876:"Skein512-160",45877:"Skein512-168",45878:"Skein512-176",45879:"Skein512-184",45880:"Skein512-192",45881:"Skein512-200",45882:"Skein512-208",45883:"Skein512-216",45884:"Skein512-224",45885:"Skein512-232",45886:"Skein512-240",45887:"Skein512-248",45888:"Skein512-256",45889:"Skein512-264",45890:"Skein512-272",45891:"Skein512-280",45892:"Skein512-288",45893:"Skein512-296",45894:"Skein512-304",45895:"Skein512-312",45896:"Skein512-320",45897:"Skein512-328",45898:"Skein512-336",45899:"Skein512-344",45900:"Skein512-352",45901:"Skein512-360",45902:"Skein512-368",45903:"Skein512-376",45904:"Skein512-384",45905:"Skein512-392",45906:"Skein512-400",45907:"Skein512-408",45908:"Skein512-416",45909:"Skein512-424",45910:"Skein512-432",45911:"Skein512-440",45912:"Skein512-448",45913:"Skein512-456",45914:"Skein512-464",45915:"Skein512-472",45916:"Skein512-480",45917:"Skein512-488",45918:"Skein512-496",45919:"Skein512-504",45920:"Skein512-512",45921:"Skein1024-8",45922:"Skein1024-16",45923:"Skein1024-24",45924:"Skein1024-32",45925:"Skein1024-40",45926:"Skein1024-48",45927:"Skein1024-56",45928:"Skein1024-64",45929:"Skein1024-72",45930:"Skein1024-80",45931:"Skein1024-88",45932:"Skein1024-96",45933:"Skein1024-104",45934:"Skein1024-112",45935:"Skein1024-120",45936:"Skein1024-128",45937:"Skein1024-136",45938:"Skein1024-144",45939:"Skein1024-152",45940:"Skein1024-160",45941:"Skein1024-168",45942:"Skein1024-176",45943:"Skein1024-184",45944:"Skein1024-192",45945:"Skein1024-200",45946:"Skein1024-208",45947:"Skein1024-216",45948:"Skein1024-224",45949:"Skein1024-232",45950:"Skein1024-240",45951:"Skein1024-248",45952:"Skein1024-256",45953:"Skein1024-264",45954:"Skein1024-272",45955:"Skein1024-280",45956:"Skein1024-288",45957:"Skein1024-296",45958:"Skein1024-304",45959:"Skein1024-312",45960:"Skein1024-320",45961:"Skein1024-328",45962:"Skein1024-336",45963:"Skein1024-344",45964:"Skein1024-352",45965:"Skein1024-360",45966:"Skein1024-368",45967:"Skein1024-376",45968:"Skein1024-384",45969:"Skein1024-392",45970:"Skein1024-400",45971:"Skein1024-408",45972:"Skein1024-416",45973:"Skein1024-424",45974:"Skein1024-432",45975:"Skein1024-440",45976:"Skein1024-448",45977:"Skein1024-456",45978:"Skein1024-464",45979:"Skein1024-472",45980:"Skein1024-480",45981:"Skein1024-488",45982:"Skein1024-496",45983:"Skein1024-504",45984:"Skein1024-512",45985:"Skein1024-520",45986:"Skein1024-528",45987:"Skein1024-536",45988:"Skein1024-544",45989:"Skein1024-552",45990:"Skein1024-560",45991:"Skein1024-568",45992:"Skein1024-576",45993:"Skein1024-584",45994:"Skein1024-592",45995:"Skein1024-600",45996:"Skein1024-608",45997:"Skein1024-616",45998:"Skein1024-624",45999:"Skein1024-632",46e3:"Skein1024-640",46001:"Skein1024-648",46002:"Skein1024-656",46003:"Skein1024-664",46004:"Skein1024-672",46005:"Skein1024-680",46006:"Skein1024-688",46007:"Skein1024-696",46008:"Skein1024-704",46009:"Skein1024-712",46010:"Skein1024-720",46011:"Skein1024-728",46012:"Skein1024-736",46013:"Skein1024-744",46014:"Skein1024-752",46015:"Skein1024-760",46016:"Skein1024-768",46017:"Skein1024-776",46018:"Skein1024-784",46019:"Skein1024-792",46020:"Skein1024-800",46021:"Skein1024-808",46022:"Skein1024-816",46023:"Skein1024-824",46024:"Skein1024-832",46025:"Skein1024-840",46026:"Skein1024-848",46027:"Skein1024-856",46028:"Skein1024-864",46029:"Skein1024-872",46030:"Skein1024-880",46031:"Skein1024-888",46032:"Skein1024-896",46033:"Skein1024-904",46034:"Skein1024-912",46035:"Skein1024-920",46036:"Skein1024-928",46037:"Skein1024-936",46038:"Skein1024-944",46039:"Skein1024-952",46040:"Skein1024-960",46041:"Skein1024-968",46042:"Skein1024-976",46043:"Skein1024-984",46044:"Skein1024-992",46045:"Skein1024-1000",46046:"Skein1024-1008",46047:"Skein1024-1016",46048:"Skein1024-1024"}),n.defaultLengths=Object.freeze({17:20,18:32,19:64,86:32,23:28,22:32,21:48,20:64,24:32,25:64,26:28,27:32,28:48,29:64,34:32,45569:1,45570:2,45571:3,45572:4,45573:5,45574:6,45575:7,45576:8,45577:9,45578:10,45579:11,45580:12,45581:13,45582:14,45583:15,45584:16,45585:17,45586:18,45587:19,45588:20,45589:21,45590:22,45591:23,45592:24,45593:25,45594:26,45595:27,45596:28,45597:29,45598:30,45599:31,45600:32,45601:33,45602:34,45603:35,45604:36,45605:37,45606:38,45607:39,45608:40,45609:41,45610:42,45611:43,45612:44,45613:45,45614:46,45615:47,45616:48,45617:49,45618:50,45619:51,45620:52,45621:53,45622:54,45623:55,45624:56,45625:57,45626:58,45627:59,45628:60,45629:61,45630:62,45631:63,45632:64,45633:1,45634:2,45635:3,45636:4,45637:5,45638:6,45639:7,45640:8,45641:9,45642:10,45643:11,45644:12,45645:13,45646:14,45647:15,45648:16,45649:17,45650:18,45651:19,45652:20,45653:21,45654:22,45655:23,45656:24,45657:25,45658:26,45659:27,45660:28,45661:29,45662:30,45663:31,45664:32,45825:1,45826:2,45827:3,45828:4,45829:5,45830:6,45831:7,45832:8,45833:9,45834:10,45835:11,45836:12,45837:13,45838:14,45839:15,45840:16,45841:17,45842:18,45843:19,45844:20,45845:21,45846:22,45847:23,45848:24,45849:25,45850:26,45851:27,45852:28,45853:29,45854:30,45855:31,45856:32,45857:1,45858:2,45859:3,45860:4,45861:5,45862:6,45863:7,45864:8,45865:9,45866:10,45867:11,45868:12,45869:13,45870:14,45871:15,45872:16,45873:17,45874:18,45875:19,45876:20,45877:21,45878:22,45879:23,45880:24,45881:25,45882:26,45883:27,45884:28,45885:29,45886:30,45887:31,45888:32,45889:33,45890:34,45891:35,45892:36,45893:37,45894:38,45895:39,45896:40,45897:41,45898:42,45899:43,45900:44,45901:45,45902:46,45903:47,45904:48,45905:49,45906:50,45907:51,45908:52,45909:53,45910:54,45911:55,45912:56,45913:57,45914:58,45915:59,45916:60,45917:61,45918:62,45919:63,45920:64,45921:1,45922:2,45923:3,45924:4,45925:5,45926:6,45927:7,45928:8,45929:9,45930:10,45931:11,45932:12,45933:13,45934:14,45935:15,45936:16,45937:17,45938:18,45939:19,45940:20,45941:21,45942:22,45943:23,45944:24,45945:25,45946:26,45947:27,45948:28,45949:29,45950:30,45951:31,45952:32,45953:33,45954:34,45955:35,45956:36,45957:37,45958:38,45959:39,45960:40,45961:41,45962:42,45963:43,45964:44,45965:45,45966:46,45967:47,45968:48,45969:49,45970:50,45971:51,45972:52,45973:53,45974:54,45975:55,45976:56,45977:57,45978:58,45979:59,45980:60,45981:61,45982:62,45983:63,45984:64,45985:65,45986:66,45987:67,45988:68,45989:69,45990:70,45991:71,45992:72,45993:73,45994:74,45995:75,45996:76,45997:77,45998:78,45999:79,46e3:80,46001:81,46002:82,46003:83,46004:84,46005:85,46006:86,46007:87,46008:88,46009:89,46010:90,46011:91,46012:92,46013:93,46014:94,46015:95,46016:96,46017:97,46018:98,46019:99,46020:100,46021:101,46022:102,46023:103,46024:104,46025:105,46026:106,46027:107,46028:108,46029:109,46030:110,46031:111,46032:112,46033:113,46034:114,46035:115,46036:116,46037:117,46038:118,46039:119,46040:120,46041:121,46042:122,46043:123,46044:124,46045:125,46046:126,46047:127,46048:128})}])}));
//# sourceMappingURL=index.min.js.map