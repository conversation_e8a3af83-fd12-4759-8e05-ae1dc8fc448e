{"version": 3, "file": "web3_subscription_manager.d.ts", "sourceRoot": "", "sources": ["../../src/web3_subscription_manager.ts"], "names": [], "mappings": "AAiBA,OAAO,EACN,UAAU,EAGV,mBAAmB,EACnB,yBAAyB,EACzB,4BAA4B,EAC5B,GAAG,EACH,WAAW,EAEX,MAAM,YAAY,CAAC;AAIpB,OAAO,EAAE,kBAAkB,EAA2B,MAAM,2BAA2B,CAAC;AAExF,OAAO,EAAE,2BAA2B,EAAE,MAAM,yBAAyB,CAAC;AAEtE,KAAK,0BAA0B,GAAG,CAAC,EAClC,EAAE,EACF,GAAG,GACH,EAAE;IACF,EAAE,EAAE,MAAM,CAAC;IACX,GAAG,EAAE,OAAO,CAAC;CACb,KAAK,OAAO,GAAG,SAAS,CAAC;AAE1B,qBAAa,uBAAuB,CACnC,GAAG,SAAS,WAAW,GAAG,WAAW,EACrC,cAAc,SAAS;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAA;CAAE,GAAG;IAC5E,CAAC,GAAG,EAAE,MAAM,GAAG,2BAA2B,CAAC,GAAG,CAAC,CAAC;CAChD;aA+BgB,cAAc,EAAE,kBAAkB,CAAC,GAAG,CAAC;aACvC,uBAAuB,EAAE,cAAc;IACvD,OAAO,CAAC,QAAQ,CAAC,4BAA4B;IA/B9C,OAAO,CAAC,QAAQ,CAAC,cAAc,CAGjB;IAEd;;;;;;;;;;OAUG;gBAEF,cAAc,EAAE,kBAAkB,CAAC,GAAG,CAAC,EACvC,uBAAuB,EAAE,cAAc;IAExC;;OAEG;gBAEF,cAAc,EAAE,kBAAkB,CAAC,GAAG,CAAC,EACvC,uBAAuB,EAAE,cAAc,EACvC,4BAA4B,EAAE,OAAO;IAmBtC,OAAO,CAAC,sBAAsB;IA2B9B,SAAS,CAAC,eAAe,CACxB,IAAI,CAAC,EACF,yBAAyB,GACzB,4BAA4B,CAAC,GAAG,CAAC,GACjC,mBAAmB,CAAC,GAAG,CAAC;IAgB5B;;;;;;;;;OASG;IACU,SAAS,CAAC,CAAC,SAAS,MAAM,cAAc,EACpD,IAAI,EAAE,CAAC,EACP,IAAI,CAAC,EAAE,qBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAClD,YAAY,GAAE,UAAkC,GAC9C,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAkB3C;;OAEG;IACH,IAAW,aAAa,oEAEvB;IAED;;;;;OAKG;IACU,eAAe,CAAC,GAAG,EAAE,YAAY,CAAC,cAAc,CAAC,MAAM,cAAc,CAAC,CAAC;IAwBpF;;;;OAIG;IACU,kBAAkB,CAAC,GAAG,EAAE,YAAY,CAAC,cAAc,CAAC,MAAM,cAAc,CAAC,CAAC;IAiBvF;;;;;OAKG;IACU,WAAW,CAAC,SAAS,CAAC,EAAE,0BAA0B;IAW/D;;OAEG;IACI,KAAK;IAIZ;;;;OAIG;IACI,qBAAqB,IAAI,OAAO;CAKvC"}