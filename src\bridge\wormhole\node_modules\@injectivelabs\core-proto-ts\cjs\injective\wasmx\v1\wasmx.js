"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RegisteredContract = exports.Params = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var types_1 = require("../../../cosmwasm/wasm/v1/types.js");
var proposal_1 = require("./proposal.js");
exports.protobufPackage = "injective.wasmx.v1";
function createBaseParams() {
    return {
        isExecutionEnabled: false,
        maxBeginBlockTotalGas: "0",
        maxContractGasLimit: "0",
        minGasPrice: "0",
        registerContractAccess: undefined,
    };
}
exports.Params = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.isExecutionEnabled === true) {
            writer.uint32(8).bool(message.isExecutionEnabled);
        }
        if (message.maxBeginBlockTotalGas !== "0") {
            writer.uint32(16).uint64(message.maxBeginBlockTotalGas);
        }
        if (message.maxContractGasLimit !== "0") {
            writer.uint32(24).uint64(message.maxContractGasLimit);
        }
        if (message.minGasPrice !== "0") {
            writer.uint32(32).uint64(message.minGasPrice);
        }
        if (message.registerContractAccess !== undefined) {
            types_1.AccessConfig.encode(message.registerContractAccess, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.isExecutionEnabled = reader.bool();
                    break;
                case 2:
                    message.maxBeginBlockTotalGas = longToString(reader.uint64());
                    break;
                case 3:
                    message.maxContractGasLimit = longToString(reader.uint64());
                    break;
                case 4:
                    message.minGasPrice = longToString(reader.uint64());
                    break;
                case 5:
                    message.registerContractAccess = types_1.AccessConfig.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            isExecutionEnabled: isSet(object.isExecutionEnabled) ? Boolean(object.isExecutionEnabled) : false,
            maxBeginBlockTotalGas: isSet(object.maxBeginBlockTotalGas) ? String(object.maxBeginBlockTotalGas) : "0",
            maxContractGasLimit: isSet(object.maxContractGasLimit) ? String(object.maxContractGasLimit) : "0",
            minGasPrice: isSet(object.minGasPrice) ? String(object.minGasPrice) : "0",
            registerContractAccess: isSet(object.registerContractAccess)
                ? types_1.AccessConfig.fromJSON(object.registerContractAccess)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.isExecutionEnabled !== undefined && (obj.isExecutionEnabled = message.isExecutionEnabled);
        message.maxBeginBlockTotalGas !== undefined && (obj.maxBeginBlockTotalGas = message.maxBeginBlockTotalGas);
        message.maxContractGasLimit !== undefined && (obj.maxContractGasLimit = message.maxContractGasLimit);
        message.minGasPrice !== undefined && (obj.minGasPrice = message.minGasPrice);
        message.registerContractAccess !== undefined && (obj.registerContractAccess = message.registerContractAccess
            ? types_1.AccessConfig.toJSON(message.registerContractAccess)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseParams();
        message.isExecutionEnabled = (_a = object.isExecutionEnabled) !== null && _a !== void 0 ? _a : false;
        message.maxBeginBlockTotalGas = (_b = object.maxBeginBlockTotalGas) !== null && _b !== void 0 ? _b : "0";
        message.maxContractGasLimit = (_c = object.maxContractGasLimit) !== null && _c !== void 0 ? _c : "0";
        message.minGasPrice = (_d = object.minGasPrice) !== null && _d !== void 0 ? _d : "0";
        message.registerContractAccess =
            (object.registerContractAccess !== undefined && object.registerContractAccess !== null)
                ? types_1.AccessConfig.fromPartial(object.registerContractAccess)
                : undefined;
        return message;
    },
};
function createBaseRegisteredContract() {
    return {
        gasLimit: "0",
        gasPrice: "0",
        isExecutable: false,
        codeId: "0",
        adminAddress: "",
        granterAddress: "",
        fundMode: 0,
    };
}
exports.RegisteredContract = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.gasLimit !== "0") {
            writer.uint32(8).uint64(message.gasLimit);
        }
        if (message.gasPrice !== "0") {
            writer.uint32(16).uint64(message.gasPrice);
        }
        if (message.isExecutable === true) {
            writer.uint32(24).bool(message.isExecutable);
        }
        if (message.codeId !== "0") {
            writer.uint32(32).uint64(message.codeId);
        }
        if (message.adminAddress !== "") {
            writer.uint32(42).string(message.adminAddress);
        }
        if (message.granterAddress !== "") {
            writer.uint32(50).string(message.granterAddress);
        }
        if (message.fundMode !== 0) {
            writer.uint32(56).int32(message.fundMode);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRegisteredContract();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.gasLimit = longToString(reader.uint64());
                    break;
                case 2:
                    message.gasPrice = longToString(reader.uint64());
                    break;
                case 3:
                    message.isExecutable = reader.bool();
                    break;
                case 4:
                    message.codeId = longToString(reader.uint64());
                    break;
                case 5:
                    message.adminAddress = reader.string();
                    break;
                case 6:
                    message.granterAddress = reader.string();
                    break;
                case 7:
                    message.fundMode = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            gasLimit: isSet(object.gasLimit) ? String(object.gasLimit) : "0",
            gasPrice: isSet(object.gasPrice) ? String(object.gasPrice) : "0",
            isExecutable: isSet(object.isExecutable) ? Boolean(object.isExecutable) : false,
            codeId: isSet(object.codeId) ? String(object.codeId) : "0",
            adminAddress: isSet(object.adminAddress) ? String(object.adminAddress) : "",
            granterAddress: isSet(object.granterAddress) ? String(object.granterAddress) : "",
            fundMode: isSet(object.fundMode) ? (0, proposal_1.fundingModeFromJSON)(object.fundMode) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.gasLimit !== undefined && (obj.gasLimit = message.gasLimit);
        message.gasPrice !== undefined && (obj.gasPrice = message.gasPrice);
        message.isExecutable !== undefined && (obj.isExecutable = message.isExecutable);
        message.codeId !== undefined && (obj.codeId = message.codeId);
        message.adminAddress !== undefined && (obj.adminAddress = message.adminAddress);
        message.granterAddress !== undefined && (obj.granterAddress = message.granterAddress);
        message.fundMode !== undefined && (obj.fundMode = (0, proposal_1.fundingModeToJSON)(message.fundMode));
        return obj;
    },
    create: function (base) {
        return exports.RegisteredContract.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseRegisteredContract();
        message.gasLimit = (_a = object.gasLimit) !== null && _a !== void 0 ? _a : "0";
        message.gasPrice = (_b = object.gasPrice) !== null && _b !== void 0 ? _b : "0";
        message.isExecutable = (_c = object.isExecutable) !== null && _c !== void 0 ? _c : false;
        message.codeId = (_d = object.codeId) !== null && _d !== void 0 ? _d : "0";
        message.adminAddress = (_e = object.adminAddress) !== null && _e !== void 0 ? _e : "";
        message.granterAddress = (_f = object.granterAddress) !== null && _f !== void 0 ? _f : "";
        message.fundMode = (_g = object.fundMode) !== null && _g !== void 0 ? _g : 0;
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
