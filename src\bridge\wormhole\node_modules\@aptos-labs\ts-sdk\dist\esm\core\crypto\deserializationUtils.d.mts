import { HexInput } from '../../types/types.mjs';
import { P as PublicKey } from '../../publicKey-CJOcUwJK.mjs';
import { Signature } from './signature.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../bcs/serializer.mjs';
import '../hex.mjs';
import '../common.mjs';
import '../accountAddress.mjs';
import '../../bcs/deserializer.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import '../../api/aptosConfig.mjs';
import '../../utils/const.mjs';

/**
 * Deserializes a public key from a hex string.
 * Attempts to deserialize using various public key types in sequence until one succeeds.
 *
 * @param publicKey - The hex string representation of the public key to deserialize
 * @returns The deserialized public key
 * @throws Error if deserialization fails for all supported key types or if multiple deserializations are found
 */
declare function deserializePublicKey(publicKey: HexInput): PublicKey;
/**
 * Deserializes a signature from a hex string.
 * Attempts to deserialize using various signature types in sequence until one succeeds.
 *
 * @param signature - The hex string representation of the signature to deserialize
 * @returns The deserialized signature
 * @throws Error if deserialization fails for all supported signature types or if multiple deserializations are found
 */
declare function deserializeSignature(signature: HexInput): Signature;

export { deserializePublicKey, deserializeSignature };
