import { InjectiveCampaignRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcCampaignApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveCampaignRpc.InjectiveCampaignRPCClientImpl;
    constructor(endpoint: string);
    fetchCampaign({ skip, limit, marketId, campaignId, accountAddress, contractAddress, }: {
        skip?: string;
        limit?: number;
        marketId?: string;
        campaignId: string;
        accountAddress?: string;
        contractAddress?: string;
    }): Promise<{
        campaign: import("../types/campaign.js").Campaign | undefined;
        users: import("../types/campaign.js").CampaignUser[];
        paging: import("../../../index.js").ExchangePagination;
    }>;
    fetchCampaigns({ type, active, limit, cursor, status, }: {
        type?: string;
        active?: boolean;
        limit?: number;
        cursor?: string;
        status?: string;
    }): Promise<{
        campaigns: import("../types/campaign.js").CampaignV2[];
        cursor: string;
    }>;
    fetchRound({ roundId, toRoundId, accountAddress, contractAddress, }: {
        roundId?: string;
        toRoundId?: number;
        accountAddress?: string;
        contractAddress?: string;
    }): Promise<{
        campaigns: import("../types/campaign.js").Campaign[];
        accumulatedRewards: InjectiveCampaignRpc.Coin[];
        rewardCount: number;
    }>;
    fetchGuilds({ skip, limit, sortBy, campaignContract, }: {
        skip?: number;
        limit?: number;
        sortBy: string;
        campaignContract: string;
    }): Promise<{
        guilds: import("../types/campaign.js").Guild[];
        paging: import("../../../index.js").ExchangePagination;
        updatedAt: number;
        summary: import("../types/campaign.js").GuildCampaignSummary | undefined;
    }>;
    fetchGuildMember({ address, campaignContract, }: {
        address: string;
        campaignContract: string;
    }): Promise<{
        info: import("../types/campaign.js").GuildMember | undefined;
    }>;
    fetchGuildMembers({ skip, limit, sortBy, guildId, campaignContract, includeGuildInfo, }: {
        skip?: number;
        limit?: number;
        sortBy?: string;
        guildId: string;
        campaignContract: string;
        includeGuildInfo: boolean;
    }): Promise<{
        members: import("../types/campaign.js").GuildMember[];
        paging: import("../../../index.js").ExchangePagination;
        guildInfo: import("../types/campaign.js").Guild | undefined;
    }>;
}
