"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const MsgBase_js_1 = require("../../MsgBase.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgSend extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgSend(params);
    }
    toProto() {
        const { params } = this;
        const amounts = Array.isArray(params.amount)
            ? params.amount
            : [params.amount];
        const amountsToSend = amounts.map((amount) => {
            const amountToSend = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
            amountToSend.denom = amount.denom;
            amountToSend.amount = amount.amount;
            return amountToSend;
        });
        const message = core_proto_ts_1.CosmosBankV1Beta1Tx.MsgSend.create();
        message.fromAddress = params.srcInjectiveAddress;
        message.toAddress = params.dstInjectiveAddress;
        message.amount = amountsToSend;
        return core_proto_ts_1.CosmosBankV1Beta1Tx.MsgSend.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.bank.v1beta1.MsgSend',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgSend',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.bank.v1beta1.MsgSend',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.bank.v1beta1.MsgSend',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosBankV1Beta1Tx.MsgSend.encode(this.toProto()).finish();
    }
}
exports.default = MsgSend;
