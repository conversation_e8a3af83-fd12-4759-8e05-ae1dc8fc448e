{"root": ["../../src/account.ts", "../../src/index.ts", "../../src/schemas.ts", "../../src/types.ts", "../../src/wallet.ts", "../../src/common/common.ts", "../../src/common/enums.ts", "../../src/common/index.ts", "../../src/common/types.ts", "../../src/common/utils.ts", "../../src/common/chains/goerli.ts", "../../src/common/chains/mainnet.ts", "../../src/common/chains/sepolia.ts", "../../src/common/eips/1153.ts", "../../src/common/eips/1559.ts", "../../src/common/eips/2315.ts", "../../src/common/eips/2537.ts", "../../src/common/eips/2565.ts", "../../src/common/eips/2718.ts", "../../src/common/eips/2929.ts", "../../src/common/eips/2930.ts", "../../src/common/eips/3074.ts", "../../src/common/eips/3198.ts", "../../src/common/eips/3529.ts", "../../src/common/eips/3540.ts", "../../src/common/eips/3541.ts", "../../src/common/eips/3554.ts", "../../src/common/eips/3607.ts", "../../src/common/eips/3651.ts", "../../src/common/eips/3670.ts", "../../src/common/eips/3675.ts", "../../src/common/eips/3855.ts", "../../src/common/eips/3860.ts", "../../src/common/eips/4345.ts", "../../src/common/eips/4399.ts", "../../src/common/eips/4844.ts", "../../src/common/eips/4895.ts", "../../src/common/eips/5133.ts", "../../src/common/eips/index.ts", "../../src/common/hardforks/arrowglacier.ts", "../../src/common/hardforks/berlin.ts", "../../src/common/hardforks/byzantium.ts", "../../src/common/hardforks/chainstart.ts", "../../src/common/hardforks/constantinople.ts", "../../src/common/hardforks/dao.ts", "../../src/common/hardforks/grayglacier.ts", "../../src/common/hardforks/homestead.ts", "../../src/common/hardforks/index.ts", "../../src/common/hardforks/istanbul.ts", "../../src/common/hardforks/london.ts", "../../src/common/hardforks/merge.ts", "../../src/common/hardforks/mergeforkidtransition.ts", "../../src/common/hardforks/muirglacier.ts", "../../src/common/hardforks/petersburg.ts", "../../src/common/hardforks/shanghai.ts", "../../src/common/hardforks/sharding.ts", "../../src/common/hardforks/spuriousdragon.ts", "../../src/common/hardforks/tangerinewhistle.ts", "../../src/tx/address.ts", "../../src/tx/basetransaction.ts", "../../src/tx/constants.ts", "../../src/tx/eip1559transaction.ts", "../../src/tx/eip2930transaction.ts", "../../src/tx/index.ts", "../../src/tx/legacytransaction.ts", "../../src/tx/transactionfactory.ts", "../../src/tx/types.ts", "../../src/tx/utils.ts"], "version": "5.6.2"}