import{a as r}from"./chunk-EBMEXURY.mjs";import{b as i}from"./chunk-STY74NUA.mjs";var t=class a extends r{constructor(e){super(),this.value=i.fromHexInput(e).toUint8Array()}serialize(e){e.serializeFixedBytes(this.value)}serializeForEntryFunction(e){e.serialize(this)}serializeForScriptFunction(e){e.serialize(this)}static deserialize(e,s){let o=e.deserializeFixedBytes(s);return new a(o)}};export{t as a};
//# sourceMappingURL=chunk-MT2RJ7H3.mjs.map