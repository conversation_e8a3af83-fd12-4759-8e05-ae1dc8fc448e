{"version": 3, "file": "bytes.js", "sourceRoot": "", "sources": ["../../src.ts/coders/bytes.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEzD,OAAO,EAAE,KAAK,EAAkB,MAAM,kBAAkB,CAAC;AAEzD,MAAM,OAAO,iBAAkB,SAAQ,KAAK;IACxC,YAAY,IAAY,EAAE,SAAiB;QACxC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED,YAAY;QACR,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,KAAU;QAC7B,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IACjE,CAAC;CACJ;AAED,MAAM,OAAO,UAAW,SAAQ,iBAAiB;IAC7C,YAAY,SAAiB;QACzB,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;CACJ"}