{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/coders/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAcF,sBAOC;AAOD,kCAOC;AAED,kEAQC;AAED,wCAOC;AAED,kCAsBC;AAED,4CAgBC;AAOD,8BASC;AA9GD,qCAAkF;AAClF,6CAAuC;AAEvC,2CAAuC;AACvC,0CAIqB;AAER,QAAA,SAAS,GAAG,EAAE,CAAC;AAE5B,SAAgB,KAAK,CAAC,IAAI,GAAG,CAAC;;IAC7B,IAAI,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,KAAK,MAAK,SAAS,EAAE,CAAC;QAC5C,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1C,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,IAAI,GAAG,CAAC;;IACnC,IAAI,CAAA,MAAA,UAAU,CAAC,MAAM,0CAAE,WAAW,MAAK,SAAS,EAAE,CAAC;QAClD,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAChD,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC;AAED,SAAgB,2BAA2B,CAAC,QAA8B;;IACzE,uCACI,QAAQ,KACX,IAAI,EAAE,MAAA,QAAQ,CAAC,IAAI,mCAAI,EAAE,EACzB,UAAU,EAAE,MAAC,QAAoD,CAAC,UAAU,0CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CACrF,2BAA2B,CAAC,CAAC,CAAC,CAC9B,IACA;AACH,CAAC;AAED,SAAgB,cAAc,CAAC,KAAc;IAC5C,OAAO,CACN,CAAC,IAAA,sBAAS,EAAC,KAAK,CAAC;QACjB,OAAO,KAAK,KAAK,QAAQ;QACzB,CAAC,IAAA,sBAAS,EAAE,KAA2B,CAAC,IAAI,CAAC;QAC7C,OAAQ,KAA2B,CAAC,IAAI,KAAK,QAAQ,CACrD,CAAC;AACH,CAAC;AAED,SAAgB,WAAW,CAAC,GAA4B;IACvD,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;;QACtB,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAC;QACd,CAAC;QACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,2BAA2B,CAAC,IAAA,2BAAiB,EAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,IAAA,mCAAwB,EAAC,KAAK,CAAC,EAAE,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,IAAA,+BAAoB,EAAC,UAAU,CAAC,CAAC;YACpD,UAAU,CAAC,IAAI,GAAG,MAAA,UAAU,CAAC,IAAI,mCAAI,EAAE,CAAC;YACxC,uCACI,UAAU,KACb,UAAU,EAAE,IAAA,iCAAsB,EACjC,KAAK,CAAC,UAAgC,CAAyB,CAC/D,IACA;QACH,CAAC;QACD,MAAM,IAAI,sBAAQ,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,SAAgB,gBAAgB,CAAC,KAAmB;IACnD,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC1D,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC;IACtE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;IAC/D,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;IACd,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QACzB,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,iDAAiD;QACjD,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,sBAAQ,CAAC,0BAA0B,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QACtE,CAAC;IACF,CAAC;IACD,OAAO;QACN,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,CAAC,UAAU,EAAE;QACvE,IAAI;KACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CAAC,KAAmB;;IAC5C,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAAE,OAAO,IAAI,CAAC;IAChG,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC5B,OAAO,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,IAAI,CAAC,SAAS,CAAC,mCAAI,KAAK,CAAC;IACnD,CAAC;IACD,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9B,OAAO,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IACD,OAAO,KAAK,CAAC;AACd,CAAC"}