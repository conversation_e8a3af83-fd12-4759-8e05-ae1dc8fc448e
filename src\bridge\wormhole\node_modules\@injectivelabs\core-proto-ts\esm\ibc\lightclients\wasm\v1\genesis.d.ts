import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "ibc.lightclients.wasm.v1";
/** GenesisState defines 08-wasm's keeper genesis state */
export interface GenesisState {
    /** uploaded light client wasm contracts */
    contracts: Contract[];
}
/** Contract stores contract code */
export interface Contract {
    /** contract byte code */
    codeBytes: Uint8Array;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSO<PERSON>(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
export declare const Contract: {
    encode(message: Contract, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Contract;
    fromJSON(object: any): Contract;
    toJSO<PERSON>(message: Contract): unknown;
    create(base?: DeepPartial<Contract>): Contract;
    fromPartial(object: DeepPartial<Contract>): Contract;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
