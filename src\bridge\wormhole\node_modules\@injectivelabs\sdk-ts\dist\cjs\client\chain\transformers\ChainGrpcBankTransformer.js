"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcBankTransformer = void 0;
const ChainGrpcCommonTransformer_js_1 = require("./ChainGrpcCommonTransformer.js");
const pagination_js_1 = require("../../../utils/pagination.js");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcBankTransformer {
    static metadataToMetadata(metadata) {
        return metadata;
    }
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            sendEnabledList: params.sendEnabled,
            defaultSendEnabled: params.defaultSendEnabled,
        };
    }
    static denomOwnersResponseToDenomOwners(response) {
        const denomOwners = response.denomOwners;
        const pagination = response.pagination;
        return {
            denomOwners,
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(pagination),
        };
    }
    static totalSupplyResponseToTotalSupply(response) {
        const balances = response.supply;
        const pagination = response.pagination;
        return {
            supply: balances.map(ChainGrpcCommonTransformer_js_1.ChainGrpcCommonTransformer.grpcCoinToCoin),
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(pagination),
        };
    }
    static denomsMetadataResponseToDenomsMetadata(response) {
        const metadatas = response.metadatas;
        const pagination = response.pagination;
        return {
            metadatas: metadatas.map(ChainGrpcBankTransformer.metadataToMetadata),
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(pagination),
        };
    }
    static balanceResponseToBalance(response) {
        return ChainGrpcCommonTransformer_js_1.ChainGrpcCommonTransformer.grpcCoinToCoin(response.balance);
    }
    static balancesResponseToBalances(response) {
        const balances = response.balances;
        const pagination = response.pagination;
        return {
            balances: balances.map(ChainGrpcCommonTransformer_js_1.ChainGrpcCommonTransformer.grpcCoinToCoin),
            pagination: (0, pagination_js_1.grpcPaginationToPagination)(pagination),
        };
    }
}
exports.ChainGrpcBankTransformer = ChainGrpcBankTransformer;
