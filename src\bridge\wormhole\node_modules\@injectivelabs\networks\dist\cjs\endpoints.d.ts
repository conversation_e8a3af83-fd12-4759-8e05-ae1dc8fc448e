import { NetworkEndpoints } from './types.js';
export declare const endpointsMainnetSentry: NetworkEndpoints;
export declare const endpointsMainnet: NetworkEndpoints;
export declare const endpointsStaging: NetworkEndpoints;
export declare const endpointsInternal: NetworkEndpoints;
export declare const endpointsTestnetSentry: NetworkEndpoints;
export declare const endpointsTestnet: NetworkEndpoints;
export declare const endpointsDevnet: NetworkEndpoints;
export declare const endpointsDevnet1: NetworkEndpoints;
export declare const endpointsDevnet2: NetworkEndpoints;
export declare const endpointsDevnet3: NetworkEndpoints;
export declare const endpointsLocal: NetworkEndpoints;
/**
 * @deprecated use TestnetSentry instead
 */
export declare const endpointsTestnetOld: NetworkEndpoints;
/**
 * @deprecated use TestnetSentry instead
 */
export declare const endpointsTestnetK8s: NetworkEndpoints;
/**
 * @deprecated use MainnetSentry instead
 */
export declare const endpointsMainnetLB: NetworkEndpoints;
/**
 * @deprecated use MainnetSentry instead
 */
export declare const endpointsMainnetOld: NetworkEndpoints;
/**
 * @deprecated use MainnetSentry instead
 */
export declare const endpointsMainnetK8s: NetworkEndpoints;
