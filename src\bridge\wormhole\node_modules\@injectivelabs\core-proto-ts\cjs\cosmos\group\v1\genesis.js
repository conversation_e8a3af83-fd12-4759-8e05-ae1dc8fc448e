"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var types_1 = require("./types.js");
exports.protobufPackage = "cosmos.group.v1";
function createBaseGenesisState() {
    return {
        groupSeq: "0",
        groups: [],
        groupMembers: [],
        groupPolicySeq: "0",
        groupPolicies: [],
        proposalSeq: "0",
        proposals: [],
        votes: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.groupSeq !== "0") {
            writer.uint32(8).uint64(message.groupSeq);
        }
        try {
            for (var _f = __values(message.groups), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                types_1.GroupInfo.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_a = _f.return)) _a.call(_f);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _h = __values(message.groupMembers), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                types_1.GroupMember.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_b = _h.return)) _b.call(_h);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.groupPolicySeq !== "0") {
            writer.uint32(32).uint64(message.groupPolicySeq);
        }
        try {
            for (var _k = __values(message.groupPolicies), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                types_1.GroupPolicyInfo.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_c = _k.return)) _c.call(_k);
            }
            finally { if (e_3) throw e_3.error; }
        }
        if (message.proposalSeq !== "0") {
            writer.uint32(48).uint64(message.proposalSeq);
        }
        try {
            for (var _m = __values(message.proposals), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                types_1.Proposal.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_d = _m.return)) _d.call(_m);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _p = __values(message.votes), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                types_1.Vote.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_e = _p.return)) _e.call(_p);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.groupSeq = longToString(reader.uint64());
                    break;
                case 2:
                    message.groups.push(types_1.GroupInfo.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.groupMembers.push(types_1.GroupMember.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.groupPolicySeq = longToString(reader.uint64());
                    break;
                case 5:
                    message.groupPolicies.push(types_1.GroupPolicyInfo.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.proposalSeq = longToString(reader.uint64());
                    break;
                case 7:
                    message.proposals.push(types_1.Proposal.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.votes.push(types_1.Vote.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            groupSeq: isSet(object.groupSeq) ? String(object.groupSeq) : "0",
            groups: Array.isArray(object === null || object === void 0 ? void 0 : object.groups) ? object.groups.map(function (e) { return types_1.GroupInfo.fromJSON(e); }) : [],
            groupMembers: Array.isArray(object === null || object === void 0 ? void 0 : object.groupMembers)
                ? object.groupMembers.map(function (e) { return types_1.GroupMember.fromJSON(e); })
                : [],
            groupPolicySeq: isSet(object.groupPolicySeq) ? String(object.groupPolicySeq) : "0",
            groupPolicies: Array.isArray(object === null || object === void 0 ? void 0 : object.groupPolicies)
                ? object.groupPolicies.map(function (e) { return types_1.GroupPolicyInfo.fromJSON(e); })
                : [],
            proposalSeq: isSet(object.proposalSeq) ? String(object.proposalSeq) : "0",
            proposals: Array.isArray(object === null || object === void 0 ? void 0 : object.proposals) ? object.proposals.map(function (e) { return types_1.Proposal.fromJSON(e); }) : [],
            votes: Array.isArray(object === null || object === void 0 ? void 0 : object.votes) ? object.votes.map(function (e) { return types_1.Vote.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.groupSeq !== undefined && (obj.groupSeq = message.groupSeq);
        if (message.groups) {
            obj.groups = message.groups.map(function (e) { return e ? types_1.GroupInfo.toJSON(e) : undefined; });
        }
        else {
            obj.groups = [];
        }
        if (message.groupMembers) {
            obj.groupMembers = message.groupMembers.map(function (e) { return e ? types_1.GroupMember.toJSON(e) : undefined; });
        }
        else {
            obj.groupMembers = [];
        }
        message.groupPolicySeq !== undefined && (obj.groupPolicySeq = message.groupPolicySeq);
        if (message.groupPolicies) {
            obj.groupPolicies = message.groupPolicies.map(function (e) { return e ? types_1.GroupPolicyInfo.toJSON(e) : undefined; });
        }
        else {
            obj.groupPolicies = [];
        }
        message.proposalSeq !== undefined && (obj.proposalSeq = message.proposalSeq);
        if (message.proposals) {
            obj.proposals = message.proposals.map(function (e) { return e ? types_1.Proposal.toJSON(e) : undefined; });
        }
        else {
            obj.proposals = [];
        }
        if (message.votes) {
            obj.votes = message.votes.map(function (e) { return e ? types_1.Vote.toJSON(e) : undefined; });
        }
        else {
            obj.votes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseGenesisState();
        message.groupSeq = (_a = object.groupSeq) !== null && _a !== void 0 ? _a : "0";
        message.groups = ((_b = object.groups) === null || _b === void 0 ? void 0 : _b.map(function (e) { return types_1.GroupInfo.fromPartial(e); })) || [];
        message.groupMembers = ((_c = object.groupMembers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return types_1.GroupMember.fromPartial(e); })) || [];
        message.groupPolicySeq = (_d = object.groupPolicySeq) !== null && _d !== void 0 ? _d : "0";
        message.groupPolicies = ((_e = object.groupPolicies) === null || _e === void 0 ? void 0 : _e.map(function (e) { return types_1.GroupPolicyInfo.fromPartial(e); })) || [];
        message.proposalSeq = (_f = object.proposalSeq) !== null && _f !== void 0 ? _f : "0";
        message.proposals = ((_g = object.proposals) === null || _g === void 0 ? void 0 : _g.map(function (e) { return types_1.Proposal.fromPartial(e); })) || [];
        message.votes = ((_h = object.votes) === null || _h === void 0 ? void 0 : _h.map(function (e) { return types_1.Vote.fromPartial(e); })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
