import { SigningScheme, HexInput } from '../types/types.mjs';
import { AccountAddress } from '../core/accountAddress.mjs';
import { Deserializer } from '../bcs/deserializer.mjs';
import { Serializable, Serializer } from '../bcs/serializer.mjs';
import { AccountAuthenticatorSingleKey } from '../transactions/authenticator/account.mjs';
import { A as Account, S as SingleKeySigner } from '../Ed25519Account-D9XrCLfE.mjs';
import { AnyRawTransaction, AnyRawTransactionInstance } from '../transactions/types.mjs';
import { EphemeralKeyPair } from './EphemeralKeyPair.mjs';
import { K as KeylessPublicKey, F as FederatedKeylessPublicKey, l as ZeroKnowledgeSig, i as KeylessSignature, r as MoveJWK, m as KeylessConfiguration, Z as ZkProof } from '../federatedKeyless-DAYXjY2Y.mjs';
import { AptosConfig } from '../api/aptosConfig.mjs';
import { AnyPublicKey } from '../core/crypto/singleKey.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../core/common.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/hex.mjs';
import '../core/crypto/ed25519.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import '../core/crypto/ephemeral.mjs';
import '../utils/const.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';
import '../core/crypto/secp256k1.mjs';

/**
 * An interface which defines if an Account utilizes Keyless signing.
 */
interface KeylessSigner extends Account {
    checkKeylessAccountValidity(aptosConfig: AptosConfig): Promise<void>;
}
declare function isKeylessSigner(obj: any): obj is KeylessSigner;
/**
 * Account implementation for the Keyless authentication scheme.  This abstract class is used for standard Keyless Accounts
 * and Federated Keyless Accounts.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare abstract class AbstractKeylessAccount extends Serializable implements KeylessSigner, SingleKeySigner {
    static readonly PEPPER_LENGTH: number;
    /**
     * The KeylessPublicKey associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
    /**
     * The EphemeralKeyPair used to generate sign.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly ephemeralKeyPair: EphemeralKeyPair;
    /**
     * The claim on the JWT to identify a user.  This is typically 'sub' or 'email'.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly uidKey: string;
    /**
     * The value of the uidKey claim on the JWT.  This intended to be a stable user identifier.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly uidVal: string;
    /**
     * The value of the 'aud' claim on the JWT, also known as client ID.  This is the identifier for the dApp's
     * OIDC registration with the identity provider.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly aud: string;
    /**
     * A value contains 31 bytes of entropy that preserves privacy of the account. Typically fetched from a pepper provider.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly pepper: Uint8Array;
    /**
     * Account address associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly accountAddress: AccountAddress;
    /**
     * The zero knowledge signature (if ready) which contains the proof used to validate the EphemeralKeyPair.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    proof: ZeroKnowledgeSig | undefined;
    /**
     * The proof of the EphemeralKeyPair or a promise that provides the proof.  This is used to allow for awaiting on
     * fetching the proof.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly proofOrPromise: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;
    /**
     * Signing scheme used to sign transactions
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signingScheme: SigningScheme;
    /**
     * The JWT token used to derive the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly jwt: string;
    /**
     * The hash of the verification key used to verify the proof. This is optional and can be used to check verifying key
     * rotations which may invalidate the proof.
     */
    readonly verificationKeyHash?: Uint8Array;
    /**
     * An event emitter used to assist in handling asynchronous proof fetching.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    private readonly emitter;
    /**
     * Use the static generator `create(...)` instead.
     * Creates an instance of the KeylessAccount with an optional proof.
     *
     * @param args - The parameters for creating a KeylessAccount.
     * @param args.address - Optional account address associated with the KeylessAccount.
     * @param args.publicKey - A KeylessPublicKey or FederatedKeylessPublicKey.
     * @param args.ephemeralKeyPair - The ephemeral key pair used in the account creation.
     * @param args.iss - A JWT issuer.
     * @param args.uidKey - The claim on the JWT to identify a user.  This is typically 'sub' or 'email'.
     * @param args.uidVal - The unique id for this user, intended to be a stable user identifier.
     * @param args.aud - The value of the 'aud' claim on the JWT, also known as client ID.  This is the identifier for the dApp's
     * OIDC registration with the identity provider.
     * @param args.pepper - A hexadecimal input used for additional security.
     * @param args.proof - A Zero Knowledge Signature or a promise that resolves to one.
     * @param args.proofFetchCallback - Optional callback function for fetching proof.
     * @param args.jwt - A JSON Web Token used for authentication.
     * @param args.verificationKeyHash Optional 32-byte verification key hash as hex input used to check proof validity.
     */
    protected constructor(args: {
        address?: AccountAddress;
        publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
        ephemeralKeyPair: EphemeralKeyPair;
        iss: string;
        uidKey: string;
        uidVal: string;
        aud: string;
        pepper: HexInput;
        proof: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;
        proofFetchCallback?: ProofFetchCallback;
        jwt: string;
        verificationKeyHash?: HexInput;
    });
    getAnyPublicKey(): AnyPublicKey;
    /**
     * This initializes the asynchronous proof fetch
     * @return Emits whether the proof succeeds or fails, but has no return.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    init(promise: Promise<ZeroKnowledgeSig>): Promise<void>;
    /**
     * Serializes the jwt data into a format suitable for transmission or storage.
     * This function ensures that both the jwt data and the proof are properly serialized.
     *
     * @param serializer - The serializer instance used to convert the jwt data into bytes.
     */
    serialize(serializer: Serializer): void;
    static partialDeserialize(deserializer: Deserializer): {
        address: AccountAddress;
        jwt: string;
        uidKey: string;
        pepper: Uint8Array;
        ephemeralKeyPair: EphemeralKeyPair;
        proof: ZeroKnowledgeSig;
        verificationKeyHash?: Uint8Array;
    };
    /**
     * Checks if the proof is expired.  If so the account must be re-derived with a new EphemeralKeyPair
     * and JWT token.
     * @return boolean
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    isExpired(): boolean;
    /**
     * Sign a message using Keyless.
     * @param message the message to sign, as binary input
     * @return the AccountAuthenticator containing the signature, together with the account's public key
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signWithAuthenticator(message: HexInput): AccountAuthenticatorSingleKey;
    /**
     * Sign a transaction using Keyless.
     * @param transaction the raw transaction
     * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorSingleKey;
    /**
     * Waits for asynchronous proof fetching to finish.
     * @return
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    waitForProofFetch(): Promise<void>;
    /**
     * Validates that the Keyless Account can be used to sign transactions.
     * @return
     */
    checkKeylessAccountValidity(aptosConfig: AptosConfig): Promise<void>;
    /**
     * Sign the given message using Keyless.
     * @param message in HexInput format
     * @returns Signature
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    sign(message: HexInput): KeylessSignature;
    /**
     * Sign the given transaction with Keyless.
     * Signs the transaction and proof to guard against proof malleability.
     * @param transaction the transaction to be signed
     * @returns KeylessSignature
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    signTransaction(transaction: AnyRawTransaction): KeylessSignature;
    getSigningMessage(transaction: AnyRawTransaction): Uint8Array;
    /**
     * Note - This function is currently incomplete and should only be used to verify ownership of the KeylessAccount
     *
     * Verifies a signature given the message.
     *
     * @param args.message the message that was signed.
     * @param args.signature the KeylessSignature to verify
     * @returns boolean
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignature(args: {
        message: HexInput;
        signature: KeylessSignature;
        jwk: MoveJWK;
        keylessConfig: KeylessConfiguration;
    }): boolean;
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: KeylessSignature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Fetches the JWK from the issuer's well-known JWKS endpoint.
     *
     * @param args.publicKey The keyless public key to query
     * @param args.kid The kid of the JWK to fetch
     * @returns A JWK matching the `kid` in the JWT header.
     * @throws {KeylessError} If the JWK cannot be fetched
     */
    static fetchJWK(args: {
        aptosConfig: AptosConfig;
        publicKey: KeylessPublicKey | FederatedKeylessPublicKey;
        kid: string;
    }): Promise<MoveJWK>;
}
/**
 * A container class to hold a transaction and a proof.  It implements CryptoHashable which is used to create
 * the signing message for Keyless transactions.  We sign over the proof to ensure non-malleability.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare class TransactionAndProof extends Serializable {
    /**
     * The transaction to sign.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    transaction: AnyRawTransactionInstance;
    /**
     * The zero knowledge proof used in signing the transaction.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    proof?: ZkProof;
    /**
     * The domain separator prefix used when hashing.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly domainSeparator = "APTOS::TransactionAndProof";
    constructor(transaction: AnyRawTransactionInstance, proof?: ZkProof);
    /**
     * Serializes the transaction data into a format suitable for transmission or storage.
     * This function ensures that both the transaction bytes and the proof are properly serialized.
     *
     * @param serializer - The serializer instance used to convert the transaction data into bytes.
     */
    serialize(serializer: Serializer): void;
    /**
     * Hashes the bcs serialized from of the class. This is the typescript corollary to the BCSCryptoHash macro in aptos-core.
     *
     * @returns Uint8Array
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    hash(): Uint8Array;
}
/**
 * @group Implementation
 * @category Account (On-Chain Model)
 */
type ProofFetchSuccess = {
    status: "Success";
};
/**
 * @group Implementation
 * @category Account (On-Chain Model)
 */
type ProofFetchFailure = {
    status: "Failed";
    error: string;
};
/**
 * @group Implementation
 * @category Account (On-Chain Model)
 */
type ProofFetchStatus = ProofFetchSuccess | ProofFetchFailure;
/**
 * @group Implementation
 * @category Account (On-Chain Model)
 */
type ProofFetchCallback = (status: ProofFetchStatus) => Promise<void>;
/**
 * @group Implementation
 * @category Account (On-Chain Model)
 */
interface ProofFetchEvents {
    proofFetchFinish: (status: ProofFetchStatus) => void;
}

export { AbstractKeylessAccount, type KeylessSigner, type ProofFetchCallback, type ProofFetchEvents, type ProofFetchFailure, type ProofFetchStatus, type ProofFetchSuccess, TransactionAndProof, isKeylessSigner };
