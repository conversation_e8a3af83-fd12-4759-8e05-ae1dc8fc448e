import type { <PERSON><PERSON> } from '../trie.js';
import type { TrieNode } from '../types.js';
export declare type NodeFilter = (node: TrieN<PERSON>, key: number[]) => Promise<boolean>;
export declare type OnFound = (node: TrieNode, key: number[]) => Promise<any>;
/**
 * Walk Trie via async generator
 * @param nodeHash - The root key to walk on.
 * @param currentKey - The current (partial) key.
 * @param onFound - Called on every node found (before filter)
 * @param filter - Filter nodes yielded by the generator.
 * @param visited - Set of visited nodes
 * @returns AsyncIterable<{ node: TrieNode; currentKey: number[] }>
 * Iterate through nodes with
 * `for await (const { node, currentKey } of trie._walkTrie(root)) { ... }`
 */
export declare function _walkTrie(this: Trie, nodeHash: Uint8Array, currentKey?: number[], onFound?: OnFound, filter?: NodeFilter, visited?: Set<string>): AsyncIterable<{
    node: TrieNode;
    currentKey: number[];
}>;
//# sourceMappingURL=asyncWalk.d.ts.map