import { Web3SideChainClient } from "../utils";
import { ExitUtil } from "../pos";
export declare class BridgeClient<T> {
    client: Web3SideChainClient<T>;
    exitUtil: ExitUtil;
    /**
     * check whether a txHash is checkPointed
     *
     * @param {string} txHash
     * @returns
     * @memberof BridgeClient
     */
    isCheckPointed(txHash: string): Promise<boolean>;
    isDeposited(depositTxHash: string): Promise<boolean>;
}
