import{a as i}from"./chunk-IF4UU2MT.mjs";import{bytesToHex as o,hexToBytes as s}from"@noble/hashes/utils";var u=(n=>(n.TOO_SHORT="too_short",n.INVALID_LENGTH="invalid_length",n.INVALID_HEX_CHARS="invalid_hex_chars",n))(u||{}),a=class e{constructor(t){this.data=t}toUint8Array(){return this.data}toStringWithoutPrefix(){return o(this.data)}toString(){return`0x${this.toStringWithoutPrefix()}`}static fromHexString(t){let r=t;if(r.startsWith("0x")&&(r=r.slice(2)),r.length===0)throw new i("Hex string is too short, must be at least 1 char long, excluding the optional leading 0x.","too_short");if(r.length%2!==0)throw new i("Hex string must be an even number of hex characters.","invalid_length");try{return new e(s(r))}catch(n){throw new i(`Hex string contains invalid hex characters: ${n?.message}`,"invalid_hex_chars")}}static fromHexInput(t){return t instanceof Uint8Array?new e(t):e.fromHexString(t)}static hexInputToUint8Array(t){return t instanceof Uint8Array?t:e.fromHexString(t).toUint8Array()}static hexInputToString(t){return e.fromHexInput(t).toString()}static hexInputToStringWithoutPrefix(t){return e.fromHexInput(t).toStringWithoutPrefix()}static isValid(t){try{return e.fromHexString(t),{valid:!0}}catch(r){return{valid:!1,invalidReason:r?.invalidReason,invalidReasonMessage:r?.message}}}equals(t){return this.data.length!==t.data.length?!1:this.data.every((r,n)=>r===t.data[n])}},l=e=>new TextDecoder().decode(a.fromHexInput(e).toUint8Array());export{u as a,a as b,l as c};
//# sourceMappingURL=chunk-STY74NUA.mjs.map