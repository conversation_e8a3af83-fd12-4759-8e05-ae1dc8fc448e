{"name": "utf8", "version": "3.0.0", "description": "A well-tested UTF-8 encoder/decoder written in JavaScript.", "homepage": "https://mths.be/utf8js", "main": "utf8.js", "keywords": ["charset", "encoding", "unicode", "utf8"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/utf8.js.git"}, "bugs": "https://github.com/mathiasbynens/utf8.js/issues", "files": ["LICENSE-MIT.txt", "utf8.js"], "scripts": {"test": "node tests/tests.js", "test-extended": "node tests/tests.js --extended", "test-browser": "open tests/index.html", "ci": "npm run build && npm test", "build": "cd tests; if [ ! -f data.json ]; then python generate-test-data.py; fi", "cover-html": "istanbul cover --report html --verbose --dir coverage tests/tests.js; istanbul report --root coverage --format html", "cover-coveralls": "istanbul cover --verbose --dir coverage tests/tests.js && coveralls < coverage/lcov.info; rm -rf coverage/lcov*"}, "devDependencies": {"coveralls": "^2.11.14", "istanbul": "^0.4.5", "qunit-extras": "^1.4.2", "qunitjs": "~1.11.0"}}