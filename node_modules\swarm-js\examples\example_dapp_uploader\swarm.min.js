var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,e,f){a!=Array.prototype&&a!=Object.prototype&&(a[e]=f.value)};$jscomp.getGlobal=function(a){return"undefined"!=typeof window&&window===a?a:"undefined"!=typeof global&&null!=global?global:a};$jscomp.global=$jscomp.getGlobal(this);$jscomp.SYMBOL_PREFIX="jscomp_symbol_";
$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};$jscomp.Symbol=function(){var a=0;return function(e){return $jscomp.SYMBOL_PREFIX+(e||"")+a++}}();
$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var a=$jscomp.global.Symbol.iterator;a||(a=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator"));"function"!=typeof Array.prototype[a]&&$jscomp.defineProperty(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}});$jscomp.initSymbolIterator=function(){}};$jscomp.arrayIterator=function(a){var e=0;return $jscomp.iteratorPrototype(function(){return e<a.length?{done:!1,value:a[e++]}:{done:!0}})};
$jscomp.iteratorPrototype=function(a){$jscomp.initSymbolIterator();a={next:a};a[$jscomp.global.Symbol.iterator]=function(){return this};return a};$jscomp.makeIterator=function(a){$jscomp.initSymbolIterator();var e=a[Symbol.iterator];return e?e.call(a):$jscomp.arrayIterator(a)};
$jscomp.polyfill=function(a,e,f,h){if(e){f=$jscomp.global;a=a.split(".");for(h=0;h<a.length-1;h++){var c=a[h];c in f||(f[c]={});f=f[c]}a=a[a.length-1];h=f[a];e=e(h);e!=h&&null!=e&&$jscomp.defineProperty(f,a,{configurable:!0,writable:!0,value:e})}};$jscomp.FORCE_POLYFILL_PROMISE=!1;
$jscomp.polyfill("Promise",function(a){function e(){this.batch_=null}function f(b){return b instanceof c?b:new c(function(a,c){a(b)})}if(a&&!$jscomp.FORCE_POLYFILL_PROMISE)return a;e.prototype.asyncExecute=function(b){null==this.batch_&&(this.batch_=[],this.asyncExecuteBatch_());this.batch_.push(b);return this};e.prototype.asyncExecuteBatch_=function(){var b=this;this.asyncExecuteFunction(function(){b.executeBatch_()})};var h=$jscomp.global.setTimeout;e.prototype.asyncExecuteFunction=function(b){h(b,
0)};e.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var b=this.batch_;this.batch_=[];for(var a=0;a<b.length;++a){var c=b[a];b[a]=null;try{c()}catch(q){this.asyncThrow_(q)}}}this.batch_=null};e.prototype.asyncThrow_=function(b){this.asyncExecuteFunction(function(){throw b;})};var c=function(b){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];var a=this.createResolveAndReject_();try{b(a.resolve,a.reject)}catch(k){a.reject(k)}};c.prototype.createResolveAndReject_=
function(){function b(b){return function(m){c||(c=!0,b.call(a,m))}}var a=this,c=!1;return{resolve:b(this.resolveTo_),reject:b(this.reject_)}};c.prototype.resolveTo_=function(b){if(b===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(b instanceof c)this.settleSameAsPromise_(b);else{a:switch(typeof b){case "object":var a=null!=b;break a;case "function":a=!0;break a;default:a=!1}a?this.resolveToNonPromiseObj_(b):this.fulfill_(b)}};c.prototype.resolveToNonPromiseObj_=function(a){var b=
void 0;try{b=a.then}catch(k){this.reject_(k);return}"function"==typeof b?this.settleSameAsThenable_(b,a):this.fulfill_(a)};c.prototype.reject_=function(a){this.settle_(2,a)};c.prototype.fulfill_=function(a){this.settle_(1,a)};c.prototype.settle_=function(a,c){if(0!=this.state_)throw Error("Cannot settle("+a+", "+c+"): Promise already settled in state"+this.state_);this.state_=a;this.result_=c;this.executeOnSettledCallbacks_()};c.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var a=
0;a<this.onSettledCallbacks_.length;++a)u.asyncExecute(this.onSettledCallbacks_[a]);this.onSettledCallbacks_=null}};var u=new e;c.prototype.settleSameAsPromise_=function(a){var b=this.createResolveAndReject_();a.callWhenSettled_(b.resolve,b.reject)};c.prototype.settleSameAsThenable_=function(a,c){var b=this.createResolveAndReject_();try{a.call(c,b.resolve,b.reject)}catch(q){b.reject(q)}};c.prototype.then=function(a,e){function b(a,b){return"function"==typeof a?function(b){try{h(a(b))}catch(n){f(n)}}:
b}var h,f,m=new c(function(a,b){h=a;f=b});this.callWhenSettled_(b(a,h),b(e,f));return m};c.prototype.catch=function(a){return this.then(void 0,a)};c.prototype.callWhenSettled_=function(a,c){function b(){switch(e.state_){case 1:a(e.result_);break;case 2:c(e.result_);break;default:throw Error("Unexpected state: "+e.state_);}}var e=this;null==this.onSettledCallbacks_?u.asyncExecute(b):this.onSettledCallbacks_.push(b)};c.resolve=f;c.reject=function(a){return new c(function(b,c){c(a)})};c.race=function(a){return new c(function(b,
c){for(var e=$jscomp.makeIterator(a),h=e.next();!h.done;h=e.next())f(h.value).callWhenSettled_(b,c)})};c.all=function(a){var b=$jscomp.makeIterator(a),e=b.next();return e.done?f([]):new c(function(a,c){function h(b){return function(c){m[b]=c;k--;0==k&&a(m)}}var m=[],k=0;do m.push(void 0),k++,f(e.value).callWhenSettled_(h(m.length-1),c),e=b.next();while(!e.done)})};return c},"es6","es3");
$jscomp.iteratorFromArray=function(a,e){$jscomp.initSymbolIterator();a instanceof String&&(a+="");var f=0,h={next:function(){if(f<a.length){var c=f++;return{value:e(c,a[c]),done:!1}}h.next=function(){return{done:!0,value:void 0}};return h.next()}};h[Symbol.iterator]=function(){return h};return h};$jscomp.polyfill("Array.prototype.entries",function(a){return a?a:function(){return $jscomp.iteratorFromArray(this,function(a,f){return[a,f]})}},"es6","es3");
$jscomp.polyfill("Array.prototype.keys",function(a){return a?a:function(){return $jscomp.iteratorFromArray(this,function(a){return a})}},"es6","es3");
(function(){function a(e,f,h){function c(b,k){if(!f[b]){if(!e[b]){var m="function"==typeof require&&require;if(!k&&m)return m(b,!0);if(u)return u(b,!0);k=Error("Cannot find module '"+b+"'");throw k.code="MODULE_NOT_FOUND",k;}k=f[b]={exports:{}};e[b][0].call(k.exports,function(a){return c(e[b][1][a]||a)},k,k.exports,a,e,f,h)}return f[b].exports}for(var u="function"==typeof require&&require,b=0;b<h.length;b++)c(h[b]);return c}return a})()({1:[function(a,e,f){e.exports=function(a){var c=a.fsp,e=a.files,
b=a.os,f=a.path,h=a.child_process,q=a.mimetype,T=a.defaultArchives,A=a.request,U=a.downloadUrl,w=a.bytes,L=a.hash,n=a.pick,V=function(a){return function(d){return function(g){return g[a]=d,g}}},B=function(a){return function(d){for(var g={},b=0,c=a.length;b<c;++b)g[a[b]]=d[b];return g}},X=function(a){return function(d){var g={},b;for(b in a)g[b]=a[b];for(var c in d)g[c]=d[c];return g}},Y=function(a){return function(d){if(a.length!==d.length)return!1;for(var b=0;b<a;++b)if(a[b]!==d[b])return!1;return!0}},
M=function(a){return function(d){return a+"/bzz-raw:/"+d}},r=function(a){return function(d){return A(M(a)(d),{responseType:"arraybuffer"}).then(function(a){a=new Uint8Array(a);if(Y(a)([52,48,52,32,112,97,103,101,32,110,111,116,32,102,111,117,110,100,10]))throw"Error 404.";return a})}},y=function(a){return function(d){return function W(d){return function(b){return function(g){var c=function(a){return void 0===a.path?Promise.resolve():"application/bzz-manifest+json"===a.contentType?W(a.hash)(b+a.path)(g):
Promise.resolve(V(b+a.path)({type:a.contentType,hash:a.hash})(g))};return r(a)(d).then(function(a){return JSON.parse(x(a)).entries}).then(function(a){return Promise.all(a.map(c))}).then(function(){return g})}}}(d)("")({})}},C=function(a){return function(d){return y(a)(d).then(function(a){return B(Object.keys(a))(Object.keys(a).map(function(d){return a[d].hash}))})}},D=function(a){return function(d){return y(a)(d).then(function(d){var b=Object.keys(d),g=b.map(function(a){return d[a].hash}),c=b.map(function(a){return d[a].type});
g=g.map(r(a));var e=function(a){return a.map(function(a,d){return{type:c[d],data:a}})};return Promise.all(g).then(function(a){return B(b)(e(a))})})}},z=function(a){return function(d){return function(b){return e.download(M(a)(d))(b)}}},E=function(a){return function(d){return function(b){return C(a)(d).then(function(d){var g=[],c;for(c in d)if(0<c.length){var e=f.join(b,c);g.push(z(a)(d[c])(e))}return Promise.all(g).then(function(){return b})})}}},p=function(a){return function(d){return A(a+"/bzz-raw:/",
{body:"string"===typeof d?F(d):d,method:"POST"})}},G=function(a){return function(d){return function(b){return function(g){return function Z(c){return A(a+"/bzz:/"+d+("/"===b[0]?b:"/"+b),{method:"PUT",headers:{"Content-Type":g.type},body:g.data}).then(function(a){if(-1!==a.indexOf("error"))throw a;return a}).catch(function(a){return 0<c&&Z(c-1)})}(3)}}}},v=function(a){return function(d){return t(a)({"":d})}},N=function(a){return function(d){return c.readFile(d).then(function(b){return v(a)({type:q.lookup(d),
data:b})})}},t=function(a){return function(d){return p(a)("{}").then(function(b){var c=function(b){return function(c){return G(a)(c)(b)(d[b])}};return Object.keys(d).reduce(function(a,d){return a.then(c(d))},Promise.resolve(b))})}},H=function(a){return function(d){return c.readFile(d).then(p(a))}},I=function(a){return function(d){return function(b){return e.directoryTree(b).then(function(a){return Promise.all(a.map(function(a){return c.readFile(a)})).then(function(d){var c=a.map(function(a){return a.slice(b.length)}),
g=a.map(function(a){return q.lookup(a)||"text/plain"});return B(c)(d.map(function(a,d){return{type:g[d],data:a}}))})}).then(function(a){return X(d?{"":a[d]}:{})(a)}).then(t(a))}}},O=function(a){return function(d){if("data"===d.pick)return n.data().then(p(a));if("file"===d.pick)return n.file().then(v(a));if("directory"===d.pick)return n.directory().then(t(a));if(d.path)switch(d.kind){case "data":return H(a)(d.path);case "file":return N(a)(d.path);case "directory":return I(a)(d.defaultFile)(d.path)}else{if(d.length||
"string"===typeof d)return p(a)(d);if(d instanceof Object)return t(a)(d)}return Promise.reject(Error("Bad arguments"))}},P=function(a){return function(d){return function(b){return aa(a)(d).then(function(c){return c?b?E(a)(d)(b):D(a)(d):b?z(a)(d)(b):r(a)(d)})}}},Q=function(a,c){var d=b.platform().replace("win32","windows")+"-"+("x64"===b.arch()?"amd64":"386");c=(c||T)[d];d=c.archiveMD5;var g=c.binaryMD5;return e.safeDownloadArchived(U+c.archive+".tar.gz")(d)(g)(a)},R=function(a){return new Promise(function(d,
b){var c=h.spawn,e=function(a){return function(d){return-1!==(""+d).indexOf(a)}},g=a.password,f=a.privateKey,k=0,l=c(a.binPath,["--bzzaccount",a.account||f,"--datadir",a.dataDir,"--ens-api",a.ensApi]);c=function(a){0===k&&e("Passphrase")(a)?setTimeout(function(){k=1;l.stdin.write(g+"\n")},500):e("Swarm http proxy started")(a)&&(k=2,clearTimeout(m),d(l))};l.stdout.on("data",c);l.stderr.on("data",c);var m=setTimeout(function(){return b(Error("Couldn't start swarm process."))},2E4)})},S=function(a){return new Promise(function(d,
b){a.stderr.removeAllListeners("data");a.stdout.removeAllListeners("data");a.stdin.removeAllListeners("error");a.removeAllListeners("error");a.removeAllListeners("exit");a.kill("SIGINT");var c=setTimeout(function(){return a.kill("SIGKILL")},8E3);a.once("close",function(){clearTimeout(c);d()})})},J=function(a){return p(a)("test").then(function(a){return"c9a99c7d326dcc6316f32fe2625b311f6dc49a175e6877681ded93137d3569e7"===a}).catch(function(){return!1})},aa=function(a){return function(b){return r(a)(b).then(function(a){try{return!!JSON.parse(x(a)).entries}catch(ba){return!1}})}},
l=function(a){return function(b,d,c,e,f){var g;"undefined"!==typeof b&&(g=a(b));"undefined"!==typeof d&&(g=a(d));"undefined"!==typeof c&&(g=a(c));"undefined"!==typeof e&&(g=a(e));"undefined"!==typeof f&&(g=a(f));return g}},x=function(a){return w.toString(w.fromUint8Array(a))},F=function(a){return w.toUint8Array(w.fromString(a))},K=function(a){return{download:function(b,c){return P(a)(b)(c)},downloadData:l(r(a)),downloadDataToDisk:l(z(a)),downloadDirectory:l(D(a)),downloadDirectoryToDisk:l(E(a)),downloadEntries:l(y(a)),
downloadRoutes:l(C(a)),isAvailable:function(){return J(a)},upload:function(b){return O(a)(b)},uploadData:l(p(a)),uploadFile:l(v(a)),uploadFileFromDisk:l(v(a)),uploadDataFromDisk:l(H(a)),uploadDirectory:l(t(a)),uploadDirectoryFromDisk:l(I(a)),uploadToManifest:l(G(a)),pick:n,hash:L,fromString:F,toString:x}};return{at:K,local:function(a){return function(b){return J("http://localhost:8500").then(function(c){return c?b(K("http://localhost:8500")).then(function(){}):Q(a.binPath,a.archives).onData(function(b){return(a.onProgress||
function(){})(b.length)}).then(function(){return R(a)}).then(function(a){return b(K("http://localhost:8500")).then(function(){return a})}).then(S)})}},download:P,downloadBinary:Q,downloadData:r,downloadDataToDisk:z,downloadDirectory:D,downloadDirectoryToDisk:E,downloadEntries:y,downloadRoutes:C,isAvailable:J,startProcess:R,stopProcess:S,upload:O,uploadData:p,uploadDataFromDisk:H,uploadFile:v,uploadFileFromDisk:N,uploadDirectory:t,uploadDirectoryFromDisk:I,uploadToManifest:G,pick:n,hash:L,fromString:F,
toString:x}}},{}]},{},[1]);
