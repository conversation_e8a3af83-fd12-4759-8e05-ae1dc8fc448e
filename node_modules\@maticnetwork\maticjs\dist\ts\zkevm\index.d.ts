import { ERC20 } from "./erc20";
import { ZkEvmBridgeClient } from "../utils";
import { IZkEvmClientConfig } from "../interfaces";
import { ZkEVMWrapper } from "./zkevm_wrapper";
export * from "./zkevm_bridge";
export * from "./bridge_util";
export * from "./zkevm_wrapper";
export declare class ZkEvmClient extends ZkEvmBridgeClient {
    zkEVMWrapper: ZkEVMWrapper;
    init(config: IZkEvmClientConfig): Promise<this>;
    /**
     * creates instance of ERC20 token
     *
     * @param {string} tokenAddress
     * @param {boolean} isParent
     *
     * @param bridgeAdapterAddress Needed if a custom erc20 token is being bridged
     * @returns
     * @memberof ERC20
     */
    erc20(tokenAddress: string, isParent?: boolean, bridgeAdapterAddress?: string): ERC20;
    private getContracts_;
}
