import { Network } from './types.js';
export declare const CW20_CODE_IDS_BY_NETWORK: (network?: Network) => string[];
export declare const getCw20AdapterContractForNetwork: (network?: Network) => string;
export declare const getCw20SwapContractForNetwork: (network?: Network) => string;
export declare const getIncentivesContractForNetwork: (network?: Network) => string;
export declare const getInjNameRegistryContractForNetwork: (network?: Network) => string;
export declare const getInjNameReverseResolverContractForNetwork: (network?: Network) => string;
export declare const getPeggyGraphQlEndpointForNetwork: (network: Network) => string;
export declare const getAssetPriceServiceForNetwork: (network: Network) => string;
