import { BaseToken, Web3SideChainClient } from "../utils";
import { IContractInitParam, IZkEvmClientConfig } from "../interfaces";
import { IZkEvmContracts } from "../interfaces";
export declare class ZkEvmToken extends BaseToken<IZkEvmClientConfig> {
    protected getZkEvmContracts: () => IZkEvmContracts;
    constructor(contractParam: IContractInitParam, client: Web3SideChainClient<IZkEvmClientConfig>, getZkEvmContracts: () => IZkEvmContracts);
    protected get parentBridge(): import("./zkevm_bridge").ZkEvmBridge;
    protected get zkEVMWrapper(): import("./zkevm_wrapper").ZkEVMWrapper;
    protected get childBridge(): import("./zkevm_bridge").ZkEvmBridge;
    protected get bridgeUtil(): import("./bridge_util").BridgeUtil;
}
