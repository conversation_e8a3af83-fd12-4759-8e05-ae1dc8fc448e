{"name": "@cosmjs/stargate", "version": "0.32.4", "description": "Utilities for Cosmos SDK 0.40", "contributors": ["<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "main": "build/index.js", "types": "build/index.d.ts", "files": ["build/", "*.md", "!*.spec.*", "!**/testdata/"], "repository": {"type": "git", "url": "https://github.com/cosmos/cosmjs/tree/main/packages/stargate"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "scripts": {"docs": "typedoc --options typedoc.js", "lint": "eslint --max-warnings 0 \"./**/*.ts\" \"./*.js\"", "lint-fix": "eslint --fix --max-warnings 0 \"./**/*.ts\" \"./*.js\"", "format": "prettier --write --loglevel warn \"./src/**/*.ts\"", "format-text": "prettier --write \"./*.md\"", "build": "rm -rf ./build && tsc", "build-or-skip": "[ -n \"$SKIP_BUILD\" ] || yarn build", "test-node": "yarn node jasmine-testrunner.js", "test-firefox": "yarn pack-web && karma start --single-run --browsers Firefox", "test-chrome": "yarn pack-web && karma start --single-run --browsers ChromeHeadless", "test": "yarn build-or-skip && yarn test-node", "coverage": "nyc --reporter=text --reporter=lcov yarn test --quiet", "pack-web": "yarn build-or-skip && webpack --mode development --config webpack.web.config.js"}, "dependencies": {"@confio/ics23": "^0.6.8", "@cosmjs/amino": "^0.32.4", "@cosmjs/encoding": "^0.32.4", "@cosmjs/math": "^0.32.4", "@cosmjs/proto-signing": "^0.32.4", "@cosmjs/stream": "^0.32.4", "@cosmjs/tendermint-rpc": "^0.32.4", "@cosmjs/utils": "^0.32.4", "cosmjs-types": "^0.9.0", "xstream": "^11.14.0"}, "devDependencies": {"@cosmjs/crypto": "^0.32.4", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/eslint-plugin-prettier": "^3", "@types/jasmine": "^4", "@types/karma-firefox-launcher": "^2", "@types/karma-jasmine": "^4", "@types/karma-jasmine-html-reporter": "^1", "@types/long": "^4.0.1", "@types/node": "^18", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^7.5", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-node": "^0.3.4", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-simple-import-sort": "^7.0.0", "esm": "^3.2.25", "glob": "^7.1.6", "jasmine": "^4", "jasmine-spec-reporter": "^6", "karma": "^6.3.14", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-jasmine": "^5", "karma-jasmine-html-reporter": "^1.5.4", "nyc": "^15.1.0", "prettier": "^2.8.1", "readonly-date": "^1.0.0", "ses": "^0.11.0", "source-map-support": "^0.5.19", "ts-node": "^8", "typedoc": "^0.23", "typescript": "~4.9", "webpack": "^5.76.0", "webpack-cli": "^4.6.0"}}