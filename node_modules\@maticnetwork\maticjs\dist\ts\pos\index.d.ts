import { ERC20 } from "./erc20";
import { RootChainManager } from "./root_chain_manager";
import { BridgeClient } from "../utils";
import { IPOSClientConfig, ITransactionOption } from "../interfaces";
import { ERC721 } from "./erc721";
import { TYPE_AMOUNT } from "../types";
import { ERC1155 } from "./erc1155";
import { GasSwapper } from "./gas_swapper";
export * from "./exit_util";
export * from "./root_chain_manager";
export * from "./root_chain";
export * from "./gas_swapper";
export declare class POSClient extends BridgeClient<IPOSClientConfig> {
    rootChainManager: RootChainManager;
    gasSwapper: GasSwapper;
    init(config: IPOSClientConfig): Promise<this>;
    erc20(tokenAddress: any, isParent?: boolean): ERC20;
    erc721(tokenAddress: any, isParent?: boolean): ERC721;
    erc1155(tokenAddress: any, isParent?: boolean): ERC1155;
    depositEther(amount: TYPE_AMOUNT, userAddress: string, option: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    depositEtherWithGas(amount: TYPE_AMOUNT, userAddress: string, swapEthAmount: TYPE_AMOUNT, swapCallData: string, option: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    private getContracts_;
}
