# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="1.1.0"></a>
# [1.1.0](https://github.com/moxystudio/js-class-is/compare/v1.0.0...v1.1.0) (2018-04-04)


### Features

* add `withoutNew` option ([e6b0c18](https://github.com/moxystudio/js-class-is/commit/e6b0c18))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/moxystudio/js-class-is/compare/v0.5.1...v1.0.0) (2018-03-30)



<a name="0.5.1"></a>
## [0.5.1](https://github.com/moxystudio/js-class-is/compare/v0.5.0...v0.5.1) (2018-03-30)


### Bug Fixes

* fix non-boolean being returned for null/undefined values ([e5abd55](https://github.com/moxystudio/js-class-is/commit/e5abd55))



<a name="0.5.0"></a>
# [0.5.0](https://github.com/moxystudio/js-class-is/compare/v0.4.0...v0.5.0) (2018-03-28)


### Features

* add support to ES5 classes ([0beb84f](https://github.com/moxystudio/js-class-is/commit/0beb84f))



<a name="0.4.0"></a>
# [0.4.0](https://github.com/moxystudio/js-class-is/compare/v0.3.1...v0.4.0) (2018-03-27)


### Bug Fixes

* remove npm build from prerelease ([56a4b73](https://github.com/moxystudio/js-class-is/commit/56a4b73))


### Features

* remove babel ([e384228](https://github.com/moxystudio/js-class-is/commit/e384228))



<a name="0.3.1"></a>
## [0.3.1](https://github.com/moxystudio/js-class-is/compare/v0.3.0...v0.3.1) (2018-03-23)



<a name="0.3.0"></a>
# [0.3.0](https://github.com/moxystudio/js-class-is/compare/v0.2.1...v0.3.0) (2018-03-23)


### Features

* add support for symbol.toStringTag ([3b86374](https://github.com/moxystudio/js-class-is/commit/3b86374))



<a name="0.2.1"></a>
## [0.2.1](https://github.com/moxystudio/js-class-is/compare/v0.2.0...v0.2.1) (2018-03-23)


### Bug Fixes

* update readme examples ([dc4ef01](https://github.com/moxystudio/js-class-is/commit/dc4ef01))



<a name="0.2.0"></a>
# [0.2.0](https://github.com/moxystudio/js-class-is/compare/v0.1.1...v0.2.0) (2018-03-23)


### Features

* add name and symbol params ([#1](https://github.com/moxystudio/js-class-is/issues/1)) ([07cfdc9](https://github.com/moxystudio/js-class-is/commit/07cfdc9))



<a name="0.1.1"></a>
## [0.1.1](https://github.com/moxystudio/js-is-class-decorator/compare/v0.1.0...v0.1.1) (2018-03-22)


### Bug Fixes

* missing semicolon in package.json ([1615c58](https://github.com/moxystudio/js-is-class-decorator/commit/1615c58))



<a name="0.1.0"></a>
# 0.1.0 (2018-03-22)


### Features

* initial commit ([9b3d9b9](https://github.com/moxystudio/js-is-class-decorator/commit/9b3d9b9))
