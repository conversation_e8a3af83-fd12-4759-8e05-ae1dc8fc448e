import { grpc } from "@injectivelabs/grpc-web";
import { GetBlockIndexerRetainHeightRequest, GetBlockIndexerRetainHeightResponse, GetBlockResultsRetainHeightRequest, GetBlockResultsRetainHeightResponse, GetBlockRetainHeightRequest, GetBlockRetainHeightResponse, GetTxIndexerRetainHeightRequest, GetTxIndexerRetainHeightResponse, SetBlockIndexerRetainHeightRequest, SetBlockIndexerRetainHeightResponse, SetBlockResultsRetainHeightRequest, SetBlockResultsRetainHeightResponse, SetBlockRetainHeightRequest, SetBlockRetainHeightResponse, SetTxIndexerRetainHeightRequest, SetTxIndexerRetainHeightResponse } from "./pruning";
export declare const protobufPackage = "cometbft.services.pruning.v1";
/**
 * PruningService provides privileged access to specialized pruning
 * functionality on the CometBFT node to help control node storage.
 */
export interface PruningService {
    /**
     * SetBlockRetainHeightRequest indicates to the node that it can safely
     * prune all block data up to the specified retain height.
     *
     * The lower of this retain height and that set by the application in its
     * Commit response will be used by the node to determine which heights' data
     * can be pruned.
     */
    SetBlockRetainHeight(request: DeepPartial<SetBlockRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockRetainHeightResponse>;
    /**
     * GetBlockRetainHeight returns information about the retain height
     * parameters used by the node to influence block retention/pruning.
     */
    GetBlockRetainHeight(request: DeepPartial<GetBlockRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockRetainHeightResponse>;
    /**
     * SetBlockResultsRetainHeightRequest indicates to the node that it can
     * safely prune all block results data up to the specified height.
     *
     * The node will always store the block results for the latest height to
     * help facilitate crash recovery.
     */
    SetBlockResultsRetainHeight(request: DeepPartial<SetBlockResultsRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockResultsRetainHeightResponse>;
    /**
     * GetBlockResultsRetainHeight returns information about the retain height
     * parameters used by the node to influence block results retention/pruning.
     */
    GetBlockResultsRetainHeight(request: DeepPartial<GetBlockResultsRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockResultsRetainHeightResponse>;
    /**
     * SetTxIndexerRetainHeightRequest indicates to the node that it can safely
     * prune all tx indices up to the specified retain height.
     */
    SetTxIndexerRetainHeight(request: DeepPartial<SetTxIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetTxIndexerRetainHeightResponse>;
    /**
     * GetTxIndexerRetainHeight returns information about the retain height
     * parameters used by the node to influence TxIndexer pruning
     */
    GetTxIndexerRetainHeight(request: DeepPartial<GetTxIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetTxIndexerRetainHeightResponse>;
    /**
     * SetBlockIndexerRetainHeightRequest indicates to the node that it can safely
     * prune all block indices up to the specified retain height.
     */
    SetBlockIndexerRetainHeight(request: DeepPartial<SetBlockIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockIndexerRetainHeightResponse>;
    /**
     * GetBlockIndexerRetainHeight returns information about the retain height
     * parameters used by the node to influence BlockIndexer pruning
     */
    GetBlockIndexerRetainHeight(request: DeepPartial<GetBlockIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockIndexerRetainHeightResponse>;
}
export declare class PruningServiceClientImpl implements PruningService {
    private readonly rpc;
    constructor(rpc: Rpc);
    SetBlockRetainHeight(request: DeepPartial<SetBlockRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockRetainHeightResponse>;
    GetBlockRetainHeight(request: DeepPartial<GetBlockRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockRetainHeightResponse>;
    SetBlockResultsRetainHeight(request: DeepPartial<SetBlockResultsRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockResultsRetainHeightResponse>;
    GetBlockResultsRetainHeight(request: DeepPartial<GetBlockResultsRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockResultsRetainHeightResponse>;
    SetTxIndexerRetainHeight(request: DeepPartial<SetTxIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetTxIndexerRetainHeightResponse>;
    GetTxIndexerRetainHeight(request: DeepPartial<GetTxIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetTxIndexerRetainHeightResponse>;
    SetBlockIndexerRetainHeight(request: DeepPartial<SetBlockIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<SetBlockIndexerRetainHeightResponse>;
    GetBlockIndexerRetainHeight(request: DeepPartial<GetBlockIndexerRetainHeightRequest>, metadata?: grpc.Metadata): Promise<GetBlockIndexerRetainHeightResponse>;
}
export declare const PruningServiceDesc: {
    serviceName: string;
};
export declare const PruningServiceSetBlockRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceGetBlockRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceSetBlockResultsRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceGetBlockResultsRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceSetTxIndexerRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceGetTxIndexerRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceSetBlockIndexerRetainHeightDesc: UnaryMethodDefinitionish;
export declare const PruningServiceGetBlockIndexerRetainHeightDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
