import { TypedDataField } from './types.js';
/**
 * ONLY USED FOR EIP712_V1
 *
 * Function used to generate EIP712 types based on a message object
 * and its structure (recursive)
 */
export declare const objectKeysToEip712Types: ({ object, messageType, primaryType, }: {
    object: Record<string, any>;
    messageType?: string;
    primaryType?: string;
}) => Map<string, TypedDataField[]>;
/**
 * JavaScript doesn't know the exact number types that
 * we represent these fields on chain so we have to map
 * them in their chain representation from the number value
 * that is available in JavaScript
 */
export declare const numberTypeToReflectionNumberType: (property?: string, messageType?: string) => "timeout_timestamp" | "int32" | "uint32" | "int64" | "uint64";
/**
 * JavaScript doesn't know the exact string types that
 * we represent these fields on chain so we have to map
 * them in their chain representation from the string value
 * that is available in JavaScript
 */
export declare const stringTypeToReflectionStringType: (property?: string) => string;
export declare const getObjectEip712PropertyType: ({ property, parentProperty, messageType, }: {
    property: string;
    parentProperty: string;
    messageType?: string;
}) => string;
/**
 * Mapping a path type to amino type for messages
 */
export declare const protoTypeToAminoType: (type: string) => string;
