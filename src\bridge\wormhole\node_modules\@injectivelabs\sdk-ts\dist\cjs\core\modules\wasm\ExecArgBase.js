"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecArgBase = exports.dataToExecData = void 0;
const utf8_js_1 = require("../../../utils/utf8.js");
const dataToExecData = (action, data) => {
    return { [action]: data };
};
exports.dataToExecData = dataToExecData;
class ExecArgBase {
    params;
    constructor(params) {
        this.params = params;
    }
    toJSON() {
        return (0, utf8_js_1.fromUtf8)(JSON.stringify(this.params));
    }
    toExecJSON() {
        return (0, utf8_js_1.fromUtf8)(JSON.stringify(this.toExecData()));
    }
}
exports.ExecArgBase = ExecArgBase;
