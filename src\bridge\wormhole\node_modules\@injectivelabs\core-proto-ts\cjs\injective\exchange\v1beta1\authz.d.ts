import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective.exchange.v1beta1";
/** spot authz messages */
export interface CreateSpotLimitOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface CreateSpotMarketOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface BatchCreateSpotLimitOrdersAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface CancelSpotOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface BatchCancelSpotOrdersAuthz {
    subaccountId: string;
    marketIds: string[];
}
/** derivative authz messages */
export interface CreateDerivativeLimitOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface CreateDerivativeMarketOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface BatchCreateDerivativeLimitOrdersAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface CancelDerivativeOrderAuthz {
    subaccountId: string;
    marketIds: string[];
}
export interface BatchCancelDerivativeOrdersAuthz {
    subaccountId: string;
    marketIds: string[];
}
/** common authz message used in both spot & derivative markets */
export interface BatchUpdateOrdersAuthz {
    subaccountId: string;
    spotMarkets: string[];
    derivativeMarkets: string[];
}
export declare const CreateSpotLimitOrderAuthz: {
    encode(message: CreateSpotLimitOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CreateSpotLimitOrderAuthz;
    fromJSON(object: any): CreateSpotLimitOrderAuthz;
    toJSON(message: CreateSpotLimitOrderAuthz): unknown;
    create(base?: DeepPartial<CreateSpotLimitOrderAuthz>): CreateSpotLimitOrderAuthz;
    fromPartial(object: DeepPartial<CreateSpotLimitOrderAuthz>): CreateSpotLimitOrderAuthz;
};
export declare const CreateSpotMarketOrderAuthz: {
    encode(message: CreateSpotMarketOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CreateSpotMarketOrderAuthz;
    fromJSON(object: any): CreateSpotMarketOrderAuthz;
    toJSON(message: CreateSpotMarketOrderAuthz): unknown;
    create(base?: DeepPartial<CreateSpotMarketOrderAuthz>): CreateSpotMarketOrderAuthz;
    fromPartial(object: DeepPartial<CreateSpotMarketOrderAuthz>): CreateSpotMarketOrderAuthz;
};
export declare const BatchCreateSpotLimitOrdersAuthz: {
    encode(message: BatchCreateSpotLimitOrdersAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchCreateSpotLimitOrdersAuthz;
    fromJSON(object: any): BatchCreateSpotLimitOrdersAuthz;
    toJSON(message: BatchCreateSpotLimitOrdersAuthz): unknown;
    create(base?: DeepPartial<BatchCreateSpotLimitOrdersAuthz>): BatchCreateSpotLimitOrdersAuthz;
    fromPartial(object: DeepPartial<BatchCreateSpotLimitOrdersAuthz>): BatchCreateSpotLimitOrdersAuthz;
};
export declare const CancelSpotOrderAuthz: {
    encode(message: CancelSpotOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CancelSpotOrderAuthz;
    fromJSON(object: any): CancelSpotOrderAuthz;
    toJSON(message: CancelSpotOrderAuthz): unknown;
    create(base?: DeepPartial<CancelSpotOrderAuthz>): CancelSpotOrderAuthz;
    fromPartial(object: DeepPartial<CancelSpotOrderAuthz>): CancelSpotOrderAuthz;
};
export declare const BatchCancelSpotOrdersAuthz: {
    encode(message: BatchCancelSpotOrdersAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchCancelSpotOrdersAuthz;
    fromJSON(object: any): BatchCancelSpotOrdersAuthz;
    toJSON(message: BatchCancelSpotOrdersAuthz): unknown;
    create(base?: DeepPartial<BatchCancelSpotOrdersAuthz>): BatchCancelSpotOrdersAuthz;
    fromPartial(object: DeepPartial<BatchCancelSpotOrdersAuthz>): BatchCancelSpotOrdersAuthz;
};
export declare const CreateDerivativeLimitOrderAuthz: {
    encode(message: CreateDerivativeLimitOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CreateDerivativeLimitOrderAuthz;
    fromJSON(object: any): CreateDerivativeLimitOrderAuthz;
    toJSON(message: CreateDerivativeLimitOrderAuthz): unknown;
    create(base?: DeepPartial<CreateDerivativeLimitOrderAuthz>): CreateDerivativeLimitOrderAuthz;
    fromPartial(object: DeepPartial<CreateDerivativeLimitOrderAuthz>): CreateDerivativeLimitOrderAuthz;
};
export declare const CreateDerivativeMarketOrderAuthz: {
    encode(message: CreateDerivativeMarketOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CreateDerivativeMarketOrderAuthz;
    fromJSON(object: any): CreateDerivativeMarketOrderAuthz;
    toJSON(message: CreateDerivativeMarketOrderAuthz): unknown;
    create(base?: DeepPartial<CreateDerivativeMarketOrderAuthz>): CreateDerivativeMarketOrderAuthz;
    fromPartial(object: DeepPartial<CreateDerivativeMarketOrderAuthz>): CreateDerivativeMarketOrderAuthz;
};
export declare const BatchCreateDerivativeLimitOrdersAuthz: {
    encode(message: BatchCreateDerivativeLimitOrdersAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchCreateDerivativeLimitOrdersAuthz;
    fromJSON(object: any): BatchCreateDerivativeLimitOrdersAuthz;
    toJSON(message: BatchCreateDerivativeLimitOrdersAuthz): unknown;
    create(base?: DeepPartial<BatchCreateDerivativeLimitOrdersAuthz>): BatchCreateDerivativeLimitOrdersAuthz;
    fromPartial(object: DeepPartial<BatchCreateDerivativeLimitOrdersAuthz>): BatchCreateDerivativeLimitOrdersAuthz;
};
export declare const CancelDerivativeOrderAuthz: {
    encode(message: CancelDerivativeOrderAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CancelDerivativeOrderAuthz;
    fromJSON(object: any): CancelDerivativeOrderAuthz;
    toJSON(message: CancelDerivativeOrderAuthz): unknown;
    create(base?: DeepPartial<CancelDerivativeOrderAuthz>): CancelDerivativeOrderAuthz;
    fromPartial(object: DeepPartial<CancelDerivativeOrderAuthz>): CancelDerivativeOrderAuthz;
};
export declare const BatchCancelDerivativeOrdersAuthz: {
    encode(message: BatchCancelDerivativeOrdersAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchCancelDerivativeOrdersAuthz;
    fromJSON(object: any): BatchCancelDerivativeOrdersAuthz;
    toJSON(message: BatchCancelDerivativeOrdersAuthz): unknown;
    create(base?: DeepPartial<BatchCancelDerivativeOrdersAuthz>): BatchCancelDerivativeOrdersAuthz;
    fromPartial(object: DeepPartial<BatchCancelDerivativeOrdersAuthz>): BatchCancelDerivativeOrdersAuthz;
};
export declare const BatchUpdateOrdersAuthz: {
    encode(message: BatchUpdateOrdersAuthz, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BatchUpdateOrdersAuthz;
    fromJSON(object: any): BatchUpdateOrdersAuthz;
    toJSON(message: BatchUpdateOrdersAuthz): unknown;
    create(base?: DeepPartial<BatchUpdateOrdersAuthz>): BatchUpdateOrdersAuthz;
    fromPartial(object: DeepPartial<BatchUpdateOrdersAuthz>): BatchUpdateOrdersAuthz;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
