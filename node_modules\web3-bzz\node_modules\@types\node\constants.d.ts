/** @deprecated since v6.3.0 - use constants property exposed by the relevant module instead. */
declare module 'constants' {
    /** @deprecated since v6.3.0 - use `os.constants.errno.E2BIG` instead. */
    const E2BIG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EACCES` instead. */
    const EACCES: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EADDRINUSE` instead. */
    const EADDRINUSE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EADDRNOTAVAIL` instead. */
    const EADDRNOTAVAIL: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EAFNOSUPPORT` instead. */
    const EAFNOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EAGAIN` instead. */
    const EAGAIN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EALREADY` instead. */
    const EALREADY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EBADF` instead. */
    const EBADF: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EBADMSG` instead. */
    const EBADMSG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EBUSY` instead. */
    const EBUSY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ECANCELED` instead. */
    const ECANCELED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ECHILD` instead. */
    const ECHILD: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ECONNABORTED` instead. */
    const ECONNABORTED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ECONNREFUSED` instead. */
    const ECONNREFUSED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ECONNRESET` instead. */
    const ECONNRESET: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EDEADLK` instead. */
    const EDEADLK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EDESTADDRREQ` instead. */
    const EDESTADDRREQ: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EDOM` instead. */
    const EDOM: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EEXIST` instead. */
    const EEXIST: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EFAULT` instead. */
    const EFAULT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EFBIG` instead. */
    const EFBIG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EHOSTUNREACH` instead. */
    const EHOSTUNREACH: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EIDRM` instead. */
    const EIDRM: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EILSEQ` instead. */
    const EILSEQ: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EINPROGRESS` instead. */
    const EINPROGRESS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EINTR` instead. */
    const EINTR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EINVAL` instead. */
    const EINVAL: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EIO` instead. */
    const EIO: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EISCONN` instead. */
    const EISCONN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EISDIR` instead. */
    const EISDIR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ELOOP` instead. */
    const ELOOP: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EMFILE` instead. */
    const EMFILE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EMLINK` instead. */
    const EMLINK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EMSGSIZE` instead. */
    const EMSGSIZE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENAMETOOLONG` instead. */
    const ENAMETOOLONG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENETDOWN` instead. */
    const ENETDOWN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENETRESET` instead. */
    const ENETRESET: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENETUNREACH` instead. */
    const ENETUNREACH: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENFILE` instead. */
    const ENFILE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOBUFS` instead. */
    const ENOBUFS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENODATA` instead. */
    const ENODATA: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENODEV` instead. */
    const ENODEV: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOENT` instead. */
    const ENOENT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOEXEC` instead. */
    const ENOEXEC: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOLCK` instead. */
    const ENOLCK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOLINK` instead. */
    const ENOLINK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOMEM` instead. */
    const ENOMEM: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOMSG` instead. */
    const ENOMSG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOPROTOOPT` instead. */
    const ENOPROTOOPT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOSPC` instead. */
    const ENOSPC: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOSR` instead. */
    const ENOSR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOSTR` instead. */
    const ENOSTR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOSYS` instead. */
    const ENOSYS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTCONN` instead. */
    const ENOTCONN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTDIR` instead. */
    const ENOTDIR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTEMPTY` instead. */
    const ENOTEMPTY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTSOCK` instead. */
    const ENOTSOCK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTSUP` instead. */
    const ENOTSUP: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENOTTY` instead. */
    const ENOTTY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ENXIO` instead. */
    const ENXIO: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EOPNOTSUPP` instead. */
    const EOPNOTSUPP: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EOVERFLOW` instead. */
    const EOVERFLOW: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EPERM` instead. */
    const EPERM: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EPIPE` instead. */
    const EPIPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EPROTO` instead. */
    const EPROTO: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EPROTONOSUPPORT` instead. */
    const EPROTONOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EPROTOTYPE` instead. */
    const EPROTOTYPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ERANGE` instead. */
    const ERANGE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EROFS` instead. */
    const EROFS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ESPIPE` instead. */
    const ESPIPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ESRCH` instead. */
    const ESRCH: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ETIME` instead. */
    const ETIME: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ETIMEDOUT` instead. */
    const ETIMEDOUT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.ETXTBSY` instead. */
    const ETXTBSY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EWOULDBLOCK` instead. */
    const EWOULDBLOCK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.EXDEV` instead. */
    const EXDEV: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEINTR` instead. */
    const WSAEINTR: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEBADF` instead. */
    const WSAEBADF: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEACCES` instead. */
    const WSAEACCES: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEFAULT` instead. */
    const WSAEFAULT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEINVAL` instead. */
    const WSAEINVAL: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEMFILE` instead. */
    const WSAEMFILE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEWOULDBLOCK` instead. */
    const WSAEWOULDBLOCK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEINPROGRESS` instead. */
    const WSAEINPROGRESS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEALREADY` instead. */
    const WSAEALREADY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOTSOCK` instead. */
    const WSAENOTSOCK: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEDESTADDRREQ` instead. */
    const WSAEDESTADDRREQ: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEMSGSIZE` instead. */
    const WSAEMSGSIZE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEPROTOTYPE` instead. */
    const WSAEPROTOTYPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOPROTOOPT` instead. */
    const WSAENOPROTOOPT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEPROTONOSUPPORT` instead. */
    const WSAEPROTONOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAESOCKTNOSUPPORT` instead. */
    const WSAESOCKTNOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEOPNOTSUPP` instead. */
    const WSAEOPNOTSUPP: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEPFNOSUPPORT` instead. */
    const WSAEPFNOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEAFNOSUPPORT` instead. */
    const WSAEAFNOSUPPORT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEADDRINUSE` instead. */
    const WSAEADDRINUSE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEADDRNOTAVAIL` instead. */
    const WSAEADDRNOTAVAIL: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENETDOWN` instead. */
    const WSAENETDOWN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENETUNREACH` instead. */
    const WSAENETUNREACH: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENETRESET` instead. */
    const WSAENETRESET: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAECONNABORTED` instead. */
    const WSAECONNABORTED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAECONNRESET` instead. */
    const WSAECONNRESET: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOBUFS` instead. */
    const WSAENOBUFS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEISCONN` instead. */
    const WSAEISCONN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOTCONN` instead. */
    const WSAENOTCONN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAESHUTDOWN` instead. */
    const WSAESHUTDOWN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAETOOMANYREFS` instead. */
    const WSAETOOMANYREFS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAETIMEDOUT` instead. */
    const WSAETIMEDOUT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAECONNREFUSED` instead. */
    const WSAECONNREFUSED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAELOOP` instead. */
    const WSAELOOP: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENAMETOOLONG` instead. */
    const WSAENAMETOOLONG: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEHOSTDOWN` instead. */
    const WSAEHOSTDOWN: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEHOSTUNREACH` instead. */
    const WSAEHOSTUNREACH: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOTEMPTY` instead. */
    const WSAENOTEMPTY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEPROCLIM` instead. */
    const WSAEPROCLIM: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEUSERS` instead. */
    const WSAEUSERS: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEDQUOT` instead. */
    const WSAEDQUOT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAESTALE` instead. */
    const WSAESTALE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEREMOTE` instead. */
    const WSAEREMOTE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSASYSNOTREADY` instead. */
    const WSASYSNOTREADY: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAVERNOTSUPPORTED` instead. */
    const WSAVERNOTSUPPORTED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSANOTINITIALISED` instead. */
    const WSANOTINITIALISED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEDISCON` instead. */
    const WSAEDISCON: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAENOMORE` instead. */
    const WSAENOMORE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAECANCELLED` instead. */
    const WSAECANCELLED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEINVALIDPROCTABLE` instead. */
    const WSAEINVALIDPROCTABLE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEINVALIDPROVIDER` instead. */
    const WSAEINVALIDPROVIDER: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEPROVIDERFAILEDINIT` instead. */
    const WSAEPROVIDERFAILEDINIT: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSASYSCALLFAILURE` instead. */
    const WSASYSCALLFAILURE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSASERVICE_NOT_FOUND` instead. */
    const WSASERVICE_NOT_FOUND: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSATYPE_NOT_FOUND` instead. */
    const WSATYPE_NOT_FOUND: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSA_E_NO_MORE` instead. */
    const WSA_E_NO_MORE: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSA_E_CANCELLED` instead. */
    const WSA_E_CANCELLED: number;
    /** @deprecated since v6.3.0 - use `os.constants.errno.WSAEREFUSED` instead. */
    const WSAEREFUSED: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGHUP` instead. */
    const SIGHUP: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGINT` instead. */
    const SIGINT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGILL` instead. */
    const SIGILL: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGABRT` instead. */
    const SIGABRT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGFPE` instead. */
    const SIGFPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGKILL` instead. */
    const SIGKILL: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGSEGV` instead. */
    const SIGSEGV: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGTERM` instead. */
    const SIGTERM: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGBREAK` instead. */
    const SIGBREAK: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGWINCH` instead. */
    const SIGWINCH: number;
    const SSL_OP_ALL: number;
    const SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION: number;
    const SSL_OP_CIPHER_SERVER_PREFERENCE: number;
    const SSL_OP_CISCO_ANYCONNECT: number;
    const SSL_OP_COOKIE_EXCHANGE: number;
    const SSL_OP_CRYPTOPRO_TLSEXT_BUG: number;
    const SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS: number;
    const SSL_OP_EPHEMERAL_RSA: number;
    const SSL_OP_LEGACY_SERVER_CONNECT: number;
    const SSL_OP_MICROSOFT_BIG_SSLV3_BUFFER: number;
    const SSL_OP_MICROSOFT_SESS_ID_BUG: number;
    const SSL_OP_MSIE_SSLV2_RSA_PADDING: number;
    const SSL_OP_NETSCAPE_CA_DN_BUG: number;
    const SSL_OP_NETSCAPE_CHALLENGE_BUG: number;
    const SSL_OP_NETSCAPE_DEMO_CIPHER_CHANGE_BUG: number;
    const SSL_OP_NETSCAPE_REUSE_CIPHER_CHANGE_BUG: number;
    const SSL_OP_NO_COMPRESSION: number;
    const SSL_OP_NO_QUERY_MTU: number;
    const SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION: number;
    const SSL_OP_NO_SSLv2: number;
    const SSL_OP_NO_SSLv3: number;
    const SSL_OP_NO_TICKET: number;
    const SSL_OP_NO_TLSv1: number;
    const SSL_OP_NO_TLSv1_1: number;
    const SSL_OP_NO_TLSv1_2: number;
    const SSL_OP_PKCS1_CHECK_1: number;
    const SSL_OP_PKCS1_CHECK_2: number;
    const SSL_OP_SINGLE_DH_USE: number;
    const SSL_OP_SINGLE_ECDH_USE: number;
    const SSL_OP_SSLEAY_080_CLIENT_DH_BUG: number;
    const SSL_OP_SSLREF2_REUSE_CERT_TYPE_BUG: number;
    const SSL_OP_TLS_BLOCK_PADDING_BUG: number;
    const SSL_OP_TLS_D5_BUG: number;
    const SSL_OP_TLS_ROLLBACK_BUG: number;
    const ENGINE_METHOD_DSA: number;
    const ENGINE_METHOD_DH: number;
    const ENGINE_METHOD_RAND: number;
    const ENGINE_METHOD_ECDH: number;
    const ENGINE_METHOD_ECDSA: number;
    const ENGINE_METHOD_CIPHERS: number;
    const ENGINE_METHOD_DIGESTS: number;
    const ENGINE_METHOD_STORE: number;
    const ENGINE_METHOD_PKEY_METHS: number;
    const ENGINE_METHOD_PKEY_ASN1_METHS: number;
    const ENGINE_METHOD_ALL: number;
    const ENGINE_METHOD_NONE: number;
    const DH_CHECK_P_NOT_SAFE_PRIME: number;
    const DH_CHECK_P_NOT_PRIME: number;
    const DH_UNABLE_TO_CHECK_GENERATOR: number;
    const DH_NOT_SUITABLE_GENERATOR: number;
    const RSA_PKCS1_PADDING: number;
    const RSA_SSLV23_PADDING: number;
    const RSA_NO_PADDING: number;
    const RSA_PKCS1_OAEP_PADDING: number;
    const RSA_X931_PADDING: number;
    const RSA_PKCS1_PSS_PADDING: number;
    const POINT_CONVERSION_COMPRESSED: number;
    const POINT_CONVERSION_UNCOMPRESSED: number;
    const POINT_CONVERSION_HYBRID: number;
    const O_RDONLY: number;
    const O_WRONLY: number;
    const O_RDWR: number;
    const S_IFMT: number;
    const S_IFREG: number;
    const S_IFDIR: number;
    const S_IFCHR: number;
    const S_IFBLK: number;
    const S_IFIFO: number;
    const S_IFSOCK: number;
    const S_IRWXU: number;
    const S_IRUSR: number;
    const S_IWUSR: number;
    const S_IXUSR: number;
    const S_IRWXG: number;
    const S_IRGRP: number;
    const S_IWGRP: number;
    const S_IXGRP: number;
    const S_IRWXO: number;
    const S_IROTH: number;
    const S_IWOTH: number;
    const S_IXOTH: number;
    const S_IFLNK: number;
    const O_CREAT: number;
    const O_EXCL: number;
    const O_NOCTTY: number;
    const O_DIRECTORY: number;
    const O_NOATIME: number;
    const O_NOFOLLOW: number;
    const O_SYNC: number;
    const O_DSYNC: number;
    const O_SYMLINK: number;
    const O_DIRECT: number;
    const O_NONBLOCK: number;
    const O_TRUNC: number;
    const O_APPEND: number;
    const F_OK: number;
    const R_OK: number;
    const W_OK: number;
    const X_OK: number;
    const COPYFILE_EXCL: number;
    const COPYFILE_FICLONE: number;
    const COPYFILE_FICLONE_FORCE: number;
    const UV_UDP_REUSEADDR: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGQUIT` instead. */
    const SIGQUIT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGTRAP` instead. */
    const SIGTRAP: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGIOT` instead. */
    const SIGIOT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGBUS` instead. */
    const SIGBUS: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGUSR1` instead. */
    const SIGUSR1: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGUSR2` instead. */
    const SIGUSR2: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGPIPE` instead. */
    const SIGPIPE: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGALRM` instead. */
    const SIGALRM: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGCHLD` instead. */
    const SIGCHLD: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGSTKFLT` instead. */
    const SIGSTKFLT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGCONT` instead. */
    const SIGCONT: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGSTOP` instead. */
    const SIGSTOP: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGTSTP` instead. */
    const SIGTSTP: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGTTIN` instead. */
    const SIGTTIN: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGTTOU` instead. */
    const SIGTTOU: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGURG` instead. */
    const SIGURG: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGXCPU` instead. */
    const SIGXCPU: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGXFSZ` instead. */
    const SIGXFSZ: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGVTALRM` instead. */
    const SIGVTALRM: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGPROF` instead. */
    const SIGPROF: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGIO` instead. */
    const SIGIO: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGPOLL` instead. */
    const SIGPOLL: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGPWR` instead. */
    const SIGPWR: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGSYS` instead. */
    const SIGSYS: number;
    /** @deprecated since v6.3.0 - use `os.constants.signals.SIGUNUSED` instead. */
    const SIGUNUSED: number;
    const defaultCoreCipherList: string;
    const defaultCipherList: string;
    const ENGINE_METHOD_RSA: number;
    const ALPN_ENABLED: number;
}
