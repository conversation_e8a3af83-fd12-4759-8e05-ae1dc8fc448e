"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Level = exports.TradeRecord = exports.SubaccountIDs = exports.TradeRecords = exports.AccountRewards = exports.VolumeRecord = exports.FeeDiscountTierTTL = exports.FeeDiscountSchedule = exports.FeeDiscountTierInfo = exports.TradingRewardCampaignInfo = exports.CampaignRewardPool = exports.TradingRewardCampaignBoostInfo = exports.PointsMultiplier = exports.DepositUpdate = exports.SubaccountDeposit = exports.SubaccountPosition = exports.DerivativeTradeLog = exports.PositionDelta = exports.TradeLog = exports.MarketOrderIndicator = exports.Position = exports.DerivativeMarketOrder = exports.DerivativeLimitOrder = exports.SubaccountOrderData = exports.SubaccountOrder = exports.SubaccountOrderbookMetadata = exports.DerivativeOrder = exports.SpotMarketOrder = exports.SpotLimitOrder = exports.SpotOrder = exports.OrderInfo = exports.SubaccountTradeNonce = exports.Deposit = exports.SpotMarket = exports.MidPriceAndTOB = exports.NextFundingTimestamp = exports.DerivativeMarketSettlementInfo = exports.PerpetualMarketFunding = exports.PerpetualMarketInfo = exports.ExpiryFuturesMarketInfo = exports.BinaryOptionsMarket = exports.DerivativeMarket = exports.MarketFeeMultiplier = exports.Params = exports.OrderMask = exports.ExecutionType = exports.OrderType = exports.MarketStatus = exports.AtomicMarketOrderAccessLevel = exports.protobufPackage = void 0;
exports.DenomMinNotional = exports.EffectiveGrant = exports.ActiveGrant = exports.GrantAuthorization = exports.DenomDecimals = exports.MarketVolume = exports.AggregateAccountVolumeRecord = exports.AggregateSubaccountVolumeRecord = void 0;
exports.atomicMarketOrderAccessLevelFromJSON = atomicMarketOrderAccessLevelFromJSON;
exports.atomicMarketOrderAccessLevelToJSON = atomicMarketOrderAccessLevelToJSON;
exports.marketStatusFromJSON = marketStatusFromJSON;
exports.marketStatusToJSON = marketStatusToJSON;
exports.orderTypeFromJSON = orderTypeFromJSON;
exports.orderTypeToJSON = orderTypeToJSON;
exports.executionTypeFromJSON = executionTypeFromJSON;
exports.executionTypeToJSON = executionTypeToJSON;
exports.orderMaskFromJSON = orderMaskFromJSON;
exports.orderMaskToJSON = orderMaskToJSON;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var oracle_1 = require("../../oracle/v1beta1/oracle.js");
exports.protobufPackage = "injective.exchange.v1beta1";
var AtomicMarketOrderAccessLevel;
(function (AtomicMarketOrderAccessLevel) {
    AtomicMarketOrderAccessLevel[AtomicMarketOrderAccessLevel["Nobody"] = 0] = "Nobody";
    /** BeginBlockerSmartContractsOnly - currently unsupported */
    AtomicMarketOrderAccessLevel[AtomicMarketOrderAccessLevel["BeginBlockerSmartContractsOnly"] = 1] = "BeginBlockerSmartContractsOnly";
    AtomicMarketOrderAccessLevel[AtomicMarketOrderAccessLevel["SmartContractsOnly"] = 2] = "SmartContractsOnly";
    AtomicMarketOrderAccessLevel[AtomicMarketOrderAccessLevel["Everyone"] = 3] = "Everyone";
    AtomicMarketOrderAccessLevel[AtomicMarketOrderAccessLevel["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(AtomicMarketOrderAccessLevel || (exports.AtomicMarketOrderAccessLevel = AtomicMarketOrderAccessLevel = {}));
function atomicMarketOrderAccessLevelFromJSON(object) {
    switch (object) {
        case 0:
        case "Nobody":
            return AtomicMarketOrderAccessLevel.Nobody;
        case 1:
        case "BeginBlockerSmartContractsOnly":
            return AtomicMarketOrderAccessLevel.BeginBlockerSmartContractsOnly;
        case 2:
        case "SmartContractsOnly":
            return AtomicMarketOrderAccessLevel.SmartContractsOnly;
        case 3:
        case "Everyone":
            return AtomicMarketOrderAccessLevel.Everyone;
        case -1:
        case "UNRECOGNIZED":
        default:
            return AtomicMarketOrderAccessLevel.UNRECOGNIZED;
    }
}
function atomicMarketOrderAccessLevelToJSON(object) {
    switch (object) {
        case AtomicMarketOrderAccessLevel.Nobody:
            return "Nobody";
        case AtomicMarketOrderAccessLevel.BeginBlockerSmartContractsOnly:
            return "BeginBlockerSmartContractsOnly";
        case AtomicMarketOrderAccessLevel.SmartContractsOnly:
            return "SmartContractsOnly";
        case AtomicMarketOrderAccessLevel.Everyone:
            return "Everyone";
        case AtomicMarketOrderAccessLevel.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
var MarketStatus;
(function (MarketStatus) {
    MarketStatus[MarketStatus["Unspecified"] = 0] = "Unspecified";
    MarketStatus[MarketStatus["Active"] = 1] = "Active";
    MarketStatus[MarketStatus["Paused"] = 2] = "Paused";
    MarketStatus[MarketStatus["Demolished"] = 3] = "Demolished";
    MarketStatus[MarketStatus["Expired"] = 4] = "Expired";
    MarketStatus[MarketStatus["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(MarketStatus || (exports.MarketStatus = MarketStatus = {}));
function marketStatusFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return MarketStatus.Unspecified;
        case 1:
        case "Active":
            return MarketStatus.Active;
        case 2:
        case "Paused":
            return MarketStatus.Paused;
        case 3:
        case "Demolished":
            return MarketStatus.Demolished;
        case 4:
        case "Expired":
            return MarketStatus.Expired;
        case -1:
        case "UNRECOGNIZED":
        default:
            return MarketStatus.UNRECOGNIZED;
    }
}
function marketStatusToJSON(object) {
    switch (object) {
        case MarketStatus.Unspecified:
            return "Unspecified";
        case MarketStatus.Active:
            return "Active";
        case MarketStatus.Paused:
            return "Paused";
        case MarketStatus.Demolished:
            return "Demolished";
        case MarketStatus.Expired:
            return "Expired";
        case MarketStatus.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
var OrderType;
(function (OrderType) {
    OrderType[OrderType["UNSPECIFIED"] = 0] = "UNSPECIFIED";
    OrderType[OrderType["BUY"] = 1] = "BUY";
    OrderType[OrderType["SELL"] = 2] = "SELL";
    OrderType[OrderType["STOP_BUY"] = 3] = "STOP_BUY";
    OrderType[OrderType["STOP_SELL"] = 4] = "STOP_SELL";
    OrderType[OrderType["TAKE_BUY"] = 5] = "TAKE_BUY";
    OrderType[OrderType["TAKE_SELL"] = 6] = "TAKE_SELL";
    OrderType[OrderType["BUY_PO"] = 7] = "BUY_PO";
    OrderType[OrderType["SELL_PO"] = 8] = "SELL_PO";
    OrderType[OrderType["BUY_ATOMIC"] = 9] = "BUY_ATOMIC";
    OrderType[OrderType["SELL_ATOMIC"] = 10] = "SELL_ATOMIC";
    OrderType[OrderType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OrderType || (exports.OrderType = OrderType = {}));
function orderTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "UNSPECIFIED":
            return OrderType.UNSPECIFIED;
        case 1:
        case "BUY":
            return OrderType.BUY;
        case 2:
        case "SELL":
            return OrderType.SELL;
        case 3:
        case "STOP_BUY":
            return OrderType.STOP_BUY;
        case 4:
        case "STOP_SELL":
            return OrderType.STOP_SELL;
        case 5:
        case "TAKE_BUY":
            return OrderType.TAKE_BUY;
        case 6:
        case "TAKE_SELL":
            return OrderType.TAKE_SELL;
        case 7:
        case "BUY_PO":
            return OrderType.BUY_PO;
        case 8:
        case "SELL_PO":
            return OrderType.SELL_PO;
        case 9:
        case "BUY_ATOMIC":
            return OrderType.BUY_ATOMIC;
        case 10:
        case "SELL_ATOMIC":
            return OrderType.SELL_ATOMIC;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OrderType.UNRECOGNIZED;
    }
}
function orderTypeToJSON(object) {
    switch (object) {
        case OrderType.UNSPECIFIED:
            return "UNSPECIFIED";
        case OrderType.BUY:
            return "BUY";
        case OrderType.SELL:
            return "SELL";
        case OrderType.STOP_BUY:
            return "STOP_BUY";
        case OrderType.STOP_SELL:
            return "STOP_SELL";
        case OrderType.TAKE_BUY:
            return "TAKE_BUY";
        case OrderType.TAKE_SELL:
            return "TAKE_SELL";
        case OrderType.BUY_PO:
            return "BUY_PO";
        case OrderType.SELL_PO:
            return "SELL_PO";
        case OrderType.BUY_ATOMIC:
            return "BUY_ATOMIC";
        case OrderType.SELL_ATOMIC:
            return "SELL_ATOMIC";
        case OrderType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
var ExecutionType;
(function (ExecutionType) {
    ExecutionType[ExecutionType["UnspecifiedExecutionType"] = 0] = "UnspecifiedExecutionType";
    ExecutionType[ExecutionType["Market"] = 1] = "Market";
    ExecutionType[ExecutionType["LimitFill"] = 2] = "LimitFill";
    ExecutionType[ExecutionType["LimitMatchRestingOrder"] = 3] = "LimitMatchRestingOrder";
    ExecutionType[ExecutionType["LimitMatchNewOrder"] = 4] = "LimitMatchNewOrder";
    ExecutionType[ExecutionType["MarketLiquidation"] = 5] = "MarketLiquidation";
    ExecutionType[ExecutionType["ExpiryMarketSettlement"] = 6] = "ExpiryMarketSettlement";
    ExecutionType[ExecutionType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ExecutionType || (exports.ExecutionType = ExecutionType = {}));
function executionTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "UnspecifiedExecutionType":
            return ExecutionType.UnspecifiedExecutionType;
        case 1:
        case "Market":
            return ExecutionType.Market;
        case 2:
        case "LimitFill":
            return ExecutionType.LimitFill;
        case 3:
        case "LimitMatchRestingOrder":
            return ExecutionType.LimitMatchRestingOrder;
        case 4:
        case "LimitMatchNewOrder":
            return ExecutionType.LimitMatchNewOrder;
        case 5:
        case "MarketLiquidation":
            return ExecutionType.MarketLiquidation;
        case 6:
        case "ExpiryMarketSettlement":
            return ExecutionType.ExpiryMarketSettlement;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ExecutionType.UNRECOGNIZED;
    }
}
function executionTypeToJSON(object) {
    switch (object) {
        case ExecutionType.UnspecifiedExecutionType:
            return "UnspecifiedExecutionType";
        case ExecutionType.Market:
            return "Market";
        case ExecutionType.LimitFill:
            return "LimitFill";
        case ExecutionType.LimitMatchRestingOrder:
            return "LimitMatchRestingOrder";
        case ExecutionType.LimitMatchNewOrder:
            return "LimitMatchNewOrder";
        case ExecutionType.MarketLiquidation:
            return "MarketLiquidation";
        case ExecutionType.ExpiryMarketSettlement:
            return "ExpiryMarketSettlement";
        case ExecutionType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
var OrderMask;
(function (OrderMask) {
    OrderMask[OrderMask["UNUSED"] = 0] = "UNUSED";
    OrderMask[OrderMask["ANY"] = 1] = "ANY";
    OrderMask[OrderMask["REGULAR"] = 2] = "REGULAR";
    OrderMask[OrderMask["CONDITIONAL"] = 4] = "CONDITIONAL";
    /** DIRECTION_BUY_OR_HIGHER - for conditional orders means HIGHER */
    OrderMask[OrderMask["DIRECTION_BUY_OR_HIGHER"] = 8] = "DIRECTION_BUY_OR_HIGHER";
    /** DIRECTION_SELL_OR_LOWER - for conditional orders means LOWER */
    OrderMask[OrderMask["DIRECTION_SELL_OR_LOWER"] = 16] = "DIRECTION_SELL_OR_LOWER";
    OrderMask[OrderMask["TYPE_MARKET"] = 32] = "TYPE_MARKET";
    OrderMask[OrderMask["TYPE_LIMIT"] = 64] = "TYPE_LIMIT";
    OrderMask[OrderMask["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OrderMask || (exports.OrderMask = OrderMask = {}));
function orderMaskFromJSON(object) {
    switch (object) {
        case 0:
        case "UNUSED":
            return OrderMask.UNUSED;
        case 1:
        case "ANY":
            return OrderMask.ANY;
        case 2:
        case "REGULAR":
            return OrderMask.REGULAR;
        case 4:
        case "CONDITIONAL":
            return OrderMask.CONDITIONAL;
        case 8:
        case "DIRECTION_BUY_OR_HIGHER":
            return OrderMask.DIRECTION_BUY_OR_HIGHER;
        case 16:
        case "DIRECTION_SELL_OR_LOWER":
            return OrderMask.DIRECTION_SELL_OR_LOWER;
        case 32:
        case "TYPE_MARKET":
            return OrderMask.TYPE_MARKET;
        case 64:
        case "TYPE_LIMIT":
            return OrderMask.TYPE_LIMIT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OrderMask.UNRECOGNIZED;
    }
}
function orderMaskToJSON(object) {
    switch (object) {
        case OrderMask.UNUSED:
            return "UNUSED";
        case OrderMask.ANY:
            return "ANY";
        case OrderMask.REGULAR:
            return "REGULAR";
        case OrderMask.CONDITIONAL:
            return "CONDITIONAL";
        case OrderMask.DIRECTION_BUY_OR_HIGHER:
            return "DIRECTION_BUY_OR_HIGHER";
        case OrderMask.DIRECTION_SELL_OR_LOWER:
            return "DIRECTION_SELL_OR_LOWER";
        case OrderMask.TYPE_MARKET:
            return "TYPE_MARKET";
        case OrderMask.TYPE_LIMIT:
            return "TYPE_LIMIT";
        case OrderMask.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseParams() {
    return {
        spotMarketInstantListingFee: undefined,
        derivativeMarketInstantListingFee: undefined,
        defaultSpotMakerFeeRate: "",
        defaultSpotTakerFeeRate: "",
        defaultDerivativeMakerFeeRate: "",
        defaultDerivativeTakerFeeRate: "",
        defaultInitialMarginRatio: "",
        defaultMaintenanceMarginRatio: "",
        defaultFundingInterval: "0",
        fundingMultiple: "0",
        relayerFeeShareRate: "",
        defaultHourlyFundingRateCap: "",
        defaultHourlyInterestRate: "",
        maxDerivativeOrderSideCount: 0,
        injRewardStakedRequirementThreshold: "",
        tradingRewardsVestingDuration: "0",
        liquidatorRewardShareRate: "",
        binaryOptionsMarketInstantListingFee: undefined,
        atomicMarketOrderAccessLevel: 0,
        spotAtomicMarketOrderFeeMultiplier: "",
        derivativeAtomicMarketOrderFeeMultiplier: "",
        binaryOptionsAtomicMarketOrderFeeMultiplier: "",
        minimalProtocolFeeRate: "",
        isInstantDerivativeMarketLaunchEnabled: false,
        postOnlyModeHeightThreshold: "0",
        marginDecreasePriceTimestampThresholdSeconds: "0",
        exchangeAdmins: [],
        injAuctionMaxCap: "",
    };
}
exports.Params = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.spotMarketInstantListingFee !== undefined) {
            coin_1.Coin.encode(message.spotMarketInstantListingFee, writer.uint32(10).fork()).ldelim();
        }
        if (message.derivativeMarketInstantListingFee !== undefined) {
            coin_1.Coin.encode(message.derivativeMarketInstantListingFee, writer.uint32(18).fork()).ldelim();
        }
        if (message.defaultSpotMakerFeeRate !== "") {
            writer.uint32(26).string(message.defaultSpotMakerFeeRate);
        }
        if (message.defaultSpotTakerFeeRate !== "") {
            writer.uint32(34).string(message.defaultSpotTakerFeeRate);
        }
        if (message.defaultDerivativeMakerFeeRate !== "") {
            writer.uint32(42).string(message.defaultDerivativeMakerFeeRate);
        }
        if (message.defaultDerivativeTakerFeeRate !== "") {
            writer.uint32(50).string(message.defaultDerivativeTakerFeeRate);
        }
        if (message.defaultInitialMarginRatio !== "") {
            writer.uint32(58).string(message.defaultInitialMarginRatio);
        }
        if (message.defaultMaintenanceMarginRatio !== "") {
            writer.uint32(66).string(message.defaultMaintenanceMarginRatio);
        }
        if (message.defaultFundingInterval !== "0") {
            writer.uint32(72).int64(message.defaultFundingInterval);
        }
        if (message.fundingMultiple !== "0") {
            writer.uint32(80).int64(message.fundingMultiple);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(90).string(message.relayerFeeShareRate);
        }
        if (message.defaultHourlyFundingRateCap !== "") {
            writer.uint32(98).string(message.defaultHourlyFundingRateCap);
        }
        if (message.defaultHourlyInterestRate !== "") {
            writer.uint32(106).string(message.defaultHourlyInterestRate);
        }
        if (message.maxDerivativeOrderSideCount !== 0) {
            writer.uint32(112).uint32(message.maxDerivativeOrderSideCount);
        }
        if (message.injRewardStakedRequirementThreshold !== "") {
            writer.uint32(122).string(message.injRewardStakedRequirementThreshold);
        }
        if (message.tradingRewardsVestingDuration !== "0") {
            writer.uint32(128).int64(message.tradingRewardsVestingDuration);
        }
        if (message.liquidatorRewardShareRate !== "") {
            writer.uint32(138).string(message.liquidatorRewardShareRate);
        }
        if (message.binaryOptionsMarketInstantListingFee !== undefined) {
            coin_1.Coin.encode(message.binaryOptionsMarketInstantListingFee, writer.uint32(146).fork()).ldelim();
        }
        if (message.atomicMarketOrderAccessLevel !== 0) {
            writer.uint32(152).int32(message.atomicMarketOrderAccessLevel);
        }
        if (message.spotAtomicMarketOrderFeeMultiplier !== "") {
            writer.uint32(162).string(message.spotAtomicMarketOrderFeeMultiplier);
        }
        if (message.derivativeAtomicMarketOrderFeeMultiplier !== "") {
            writer.uint32(170).string(message.derivativeAtomicMarketOrderFeeMultiplier);
        }
        if (message.binaryOptionsAtomicMarketOrderFeeMultiplier !== "") {
            writer.uint32(178).string(message.binaryOptionsAtomicMarketOrderFeeMultiplier);
        }
        if (message.minimalProtocolFeeRate !== "") {
            writer.uint32(186).string(message.minimalProtocolFeeRate);
        }
        if (message.isInstantDerivativeMarketLaunchEnabled === true) {
            writer.uint32(192).bool(message.isInstantDerivativeMarketLaunchEnabled);
        }
        if (message.postOnlyModeHeightThreshold !== "0") {
            writer.uint32(200).int64(message.postOnlyModeHeightThreshold);
        }
        if (message.marginDecreasePriceTimestampThresholdSeconds !== "0") {
            writer.uint32(208).int64(message.marginDecreasePriceTimestampThresholdSeconds);
        }
        try {
            for (var _b = __values(message.exchangeAdmins), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(218).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (message.injAuctionMaxCap !== "") {
            writer.uint32(226).string(message.injAuctionMaxCap);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.spotMarketInstantListingFee = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.derivativeMarketInstantListingFee = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.defaultSpotMakerFeeRate = reader.string();
                    break;
                case 4:
                    message.defaultSpotTakerFeeRate = reader.string();
                    break;
                case 5:
                    message.defaultDerivativeMakerFeeRate = reader.string();
                    break;
                case 6:
                    message.defaultDerivativeTakerFeeRate = reader.string();
                    break;
                case 7:
                    message.defaultInitialMarginRatio = reader.string();
                    break;
                case 8:
                    message.defaultMaintenanceMarginRatio = reader.string();
                    break;
                case 9:
                    message.defaultFundingInterval = longToString(reader.int64());
                    break;
                case 10:
                    message.fundingMultiple = longToString(reader.int64());
                    break;
                case 11:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 12:
                    message.defaultHourlyFundingRateCap = reader.string();
                    break;
                case 13:
                    message.defaultHourlyInterestRate = reader.string();
                    break;
                case 14:
                    message.maxDerivativeOrderSideCount = reader.uint32();
                    break;
                case 15:
                    message.injRewardStakedRequirementThreshold = reader.string();
                    break;
                case 16:
                    message.tradingRewardsVestingDuration = longToString(reader.int64());
                    break;
                case 17:
                    message.liquidatorRewardShareRate = reader.string();
                    break;
                case 18:
                    message.binaryOptionsMarketInstantListingFee = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.atomicMarketOrderAccessLevel = reader.int32();
                    break;
                case 20:
                    message.spotAtomicMarketOrderFeeMultiplier = reader.string();
                    break;
                case 21:
                    message.derivativeAtomicMarketOrderFeeMultiplier = reader.string();
                    break;
                case 22:
                    message.binaryOptionsAtomicMarketOrderFeeMultiplier = reader.string();
                    break;
                case 23:
                    message.minimalProtocolFeeRate = reader.string();
                    break;
                case 24:
                    message.isInstantDerivativeMarketLaunchEnabled = reader.bool();
                    break;
                case 25:
                    message.postOnlyModeHeightThreshold = longToString(reader.int64());
                    break;
                case 26:
                    message.marginDecreasePriceTimestampThresholdSeconds = longToString(reader.int64());
                    break;
                case 27:
                    message.exchangeAdmins.push(reader.string());
                    break;
                case 28:
                    message.injAuctionMaxCap = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            spotMarketInstantListingFee: isSet(object.spotMarketInstantListingFee)
                ? coin_1.Coin.fromJSON(object.spotMarketInstantListingFee)
                : undefined,
            derivativeMarketInstantListingFee: isSet(object.derivativeMarketInstantListingFee)
                ? coin_1.Coin.fromJSON(object.derivativeMarketInstantListingFee)
                : undefined,
            defaultSpotMakerFeeRate: isSet(object.defaultSpotMakerFeeRate) ? String(object.defaultSpotMakerFeeRate) : "",
            defaultSpotTakerFeeRate: isSet(object.defaultSpotTakerFeeRate) ? String(object.defaultSpotTakerFeeRate) : "",
            defaultDerivativeMakerFeeRate: isSet(object.defaultDerivativeMakerFeeRate)
                ? String(object.defaultDerivativeMakerFeeRate)
                : "",
            defaultDerivativeTakerFeeRate: isSet(object.defaultDerivativeTakerFeeRate)
                ? String(object.defaultDerivativeTakerFeeRate)
                : "",
            defaultInitialMarginRatio: isSet(object.defaultInitialMarginRatio)
                ? String(object.defaultInitialMarginRatio)
                : "",
            defaultMaintenanceMarginRatio: isSet(object.defaultMaintenanceMarginRatio)
                ? String(object.defaultMaintenanceMarginRatio)
                : "",
            defaultFundingInterval: isSet(object.defaultFundingInterval) ? String(object.defaultFundingInterval) : "0",
            fundingMultiple: isSet(object.fundingMultiple) ? String(object.fundingMultiple) : "0",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            defaultHourlyFundingRateCap: isSet(object.defaultHourlyFundingRateCap)
                ? String(object.defaultHourlyFundingRateCap)
                : "",
            defaultHourlyInterestRate: isSet(object.defaultHourlyInterestRate)
                ? String(object.defaultHourlyInterestRate)
                : "",
            maxDerivativeOrderSideCount: isSet(object.maxDerivativeOrderSideCount)
                ? Number(object.maxDerivativeOrderSideCount)
                : 0,
            injRewardStakedRequirementThreshold: isSet(object.injRewardStakedRequirementThreshold)
                ? String(object.injRewardStakedRequirementThreshold)
                : "",
            tradingRewardsVestingDuration: isSet(object.tradingRewardsVestingDuration)
                ? String(object.tradingRewardsVestingDuration)
                : "0",
            liquidatorRewardShareRate: isSet(object.liquidatorRewardShareRate)
                ? String(object.liquidatorRewardShareRate)
                : "",
            binaryOptionsMarketInstantListingFee: isSet(object.binaryOptionsMarketInstantListingFee)
                ? coin_1.Coin.fromJSON(object.binaryOptionsMarketInstantListingFee)
                : undefined,
            atomicMarketOrderAccessLevel: isSet(object.atomicMarketOrderAccessLevel)
                ? atomicMarketOrderAccessLevelFromJSON(object.atomicMarketOrderAccessLevel)
                : 0,
            spotAtomicMarketOrderFeeMultiplier: isSet(object.spotAtomicMarketOrderFeeMultiplier)
                ? String(object.spotAtomicMarketOrderFeeMultiplier)
                : "",
            derivativeAtomicMarketOrderFeeMultiplier: isSet(object.derivativeAtomicMarketOrderFeeMultiplier)
                ? String(object.derivativeAtomicMarketOrderFeeMultiplier)
                : "",
            binaryOptionsAtomicMarketOrderFeeMultiplier: isSet(object.binaryOptionsAtomicMarketOrderFeeMultiplier)
                ? String(object.binaryOptionsAtomicMarketOrderFeeMultiplier)
                : "",
            minimalProtocolFeeRate: isSet(object.minimalProtocolFeeRate) ? String(object.minimalProtocolFeeRate) : "",
            isInstantDerivativeMarketLaunchEnabled: isSet(object.isInstantDerivativeMarketLaunchEnabled)
                ? Boolean(object.isInstantDerivativeMarketLaunchEnabled)
                : false,
            postOnlyModeHeightThreshold: isSet(object.postOnlyModeHeightThreshold)
                ? String(object.postOnlyModeHeightThreshold)
                : "0",
            marginDecreasePriceTimestampThresholdSeconds: isSet(object.marginDecreasePriceTimestampThresholdSeconds)
                ? String(object.marginDecreasePriceTimestampThresholdSeconds)
                : "0",
            exchangeAdmins: Array.isArray(object === null || object === void 0 ? void 0 : object.exchangeAdmins)
                ? object.exchangeAdmins.map(function (e) { return String(e); })
                : [],
            injAuctionMaxCap: isSet(object.injAuctionMaxCap) ? String(object.injAuctionMaxCap) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.spotMarketInstantListingFee !== undefined &&
            (obj.spotMarketInstantListingFee = message.spotMarketInstantListingFee
                ? coin_1.Coin.toJSON(message.spotMarketInstantListingFee)
                : undefined);
        message.derivativeMarketInstantListingFee !== undefined &&
            (obj.derivativeMarketInstantListingFee = message.derivativeMarketInstantListingFee
                ? coin_1.Coin.toJSON(message.derivativeMarketInstantListingFee)
                : undefined);
        message.defaultSpotMakerFeeRate !== undefined && (obj.defaultSpotMakerFeeRate = message.defaultSpotMakerFeeRate);
        message.defaultSpotTakerFeeRate !== undefined && (obj.defaultSpotTakerFeeRate = message.defaultSpotTakerFeeRate);
        message.defaultDerivativeMakerFeeRate !== undefined &&
            (obj.defaultDerivativeMakerFeeRate = message.defaultDerivativeMakerFeeRate);
        message.defaultDerivativeTakerFeeRate !== undefined &&
            (obj.defaultDerivativeTakerFeeRate = message.defaultDerivativeTakerFeeRate);
        message.defaultInitialMarginRatio !== undefined &&
            (obj.defaultInitialMarginRatio = message.defaultInitialMarginRatio);
        message.defaultMaintenanceMarginRatio !== undefined &&
            (obj.defaultMaintenanceMarginRatio = message.defaultMaintenanceMarginRatio);
        message.defaultFundingInterval !== undefined && (obj.defaultFundingInterval = message.defaultFundingInterval);
        message.fundingMultiple !== undefined && (obj.fundingMultiple = message.fundingMultiple);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.defaultHourlyFundingRateCap !== undefined &&
            (obj.defaultHourlyFundingRateCap = message.defaultHourlyFundingRateCap);
        message.defaultHourlyInterestRate !== undefined &&
            (obj.defaultHourlyInterestRate = message.defaultHourlyInterestRate);
        message.maxDerivativeOrderSideCount !== undefined &&
            (obj.maxDerivativeOrderSideCount = Math.round(message.maxDerivativeOrderSideCount));
        message.injRewardStakedRequirementThreshold !== undefined &&
            (obj.injRewardStakedRequirementThreshold = message.injRewardStakedRequirementThreshold);
        message.tradingRewardsVestingDuration !== undefined &&
            (obj.tradingRewardsVestingDuration = message.tradingRewardsVestingDuration);
        message.liquidatorRewardShareRate !== undefined &&
            (obj.liquidatorRewardShareRate = message.liquidatorRewardShareRate);
        message.binaryOptionsMarketInstantListingFee !== undefined &&
            (obj.binaryOptionsMarketInstantListingFee = message.binaryOptionsMarketInstantListingFee
                ? coin_1.Coin.toJSON(message.binaryOptionsMarketInstantListingFee)
                : undefined);
        message.atomicMarketOrderAccessLevel !== undefined &&
            (obj.atomicMarketOrderAccessLevel = atomicMarketOrderAccessLevelToJSON(message.atomicMarketOrderAccessLevel));
        message.spotAtomicMarketOrderFeeMultiplier !== undefined &&
            (obj.spotAtomicMarketOrderFeeMultiplier = message.spotAtomicMarketOrderFeeMultiplier);
        message.derivativeAtomicMarketOrderFeeMultiplier !== undefined &&
            (obj.derivativeAtomicMarketOrderFeeMultiplier = message.derivativeAtomicMarketOrderFeeMultiplier);
        message.binaryOptionsAtomicMarketOrderFeeMultiplier !== undefined &&
            (obj.binaryOptionsAtomicMarketOrderFeeMultiplier = message.binaryOptionsAtomicMarketOrderFeeMultiplier);
        message.minimalProtocolFeeRate !== undefined && (obj.minimalProtocolFeeRate = message.minimalProtocolFeeRate);
        message.isInstantDerivativeMarketLaunchEnabled !== undefined &&
            (obj.isInstantDerivativeMarketLaunchEnabled = message.isInstantDerivativeMarketLaunchEnabled);
        message.postOnlyModeHeightThreshold !== undefined &&
            (obj.postOnlyModeHeightThreshold = message.postOnlyModeHeightThreshold);
        message.marginDecreasePriceTimestampThresholdSeconds !== undefined &&
            (obj.marginDecreasePriceTimestampThresholdSeconds = message.marginDecreasePriceTimestampThresholdSeconds);
        if (message.exchangeAdmins) {
            obj.exchangeAdmins = message.exchangeAdmins.map(function (e) { return e; });
        }
        else {
            obj.exchangeAdmins = [];
        }
        message.injAuctionMaxCap !== undefined && (obj.injAuctionMaxCap = message.injAuctionMaxCap);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0;
        var message = createBaseParams();
        message.spotMarketInstantListingFee =
            (object.spotMarketInstantListingFee !== undefined && object.spotMarketInstantListingFee !== null)
                ? coin_1.Coin.fromPartial(object.spotMarketInstantListingFee)
                : undefined;
        message.derivativeMarketInstantListingFee =
            (object.derivativeMarketInstantListingFee !== undefined && object.derivativeMarketInstantListingFee !== null)
                ? coin_1.Coin.fromPartial(object.derivativeMarketInstantListingFee)
                : undefined;
        message.defaultSpotMakerFeeRate = (_a = object.defaultSpotMakerFeeRate) !== null && _a !== void 0 ? _a : "";
        message.defaultSpotTakerFeeRate = (_b = object.defaultSpotTakerFeeRate) !== null && _b !== void 0 ? _b : "";
        message.defaultDerivativeMakerFeeRate = (_c = object.defaultDerivativeMakerFeeRate) !== null && _c !== void 0 ? _c : "";
        message.defaultDerivativeTakerFeeRate = (_d = object.defaultDerivativeTakerFeeRate) !== null && _d !== void 0 ? _d : "";
        message.defaultInitialMarginRatio = (_e = object.defaultInitialMarginRatio) !== null && _e !== void 0 ? _e : "";
        message.defaultMaintenanceMarginRatio = (_f = object.defaultMaintenanceMarginRatio) !== null && _f !== void 0 ? _f : "";
        message.defaultFundingInterval = (_g = object.defaultFundingInterval) !== null && _g !== void 0 ? _g : "0";
        message.fundingMultiple = (_h = object.fundingMultiple) !== null && _h !== void 0 ? _h : "0";
        message.relayerFeeShareRate = (_j = object.relayerFeeShareRate) !== null && _j !== void 0 ? _j : "";
        message.defaultHourlyFundingRateCap = (_k = object.defaultHourlyFundingRateCap) !== null && _k !== void 0 ? _k : "";
        message.defaultHourlyInterestRate = (_l = object.defaultHourlyInterestRate) !== null && _l !== void 0 ? _l : "";
        message.maxDerivativeOrderSideCount = (_m = object.maxDerivativeOrderSideCount) !== null && _m !== void 0 ? _m : 0;
        message.injRewardStakedRequirementThreshold = (_o = object.injRewardStakedRequirementThreshold) !== null && _o !== void 0 ? _o : "";
        message.tradingRewardsVestingDuration = (_p = object.tradingRewardsVestingDuration) !== null && _p !== void 0 ? _p : "0";
        message.liquidatorRewardShareRate = (_q = object.liquidatorRewardShareRate) !== null && _q !== void 0 ? _q : "";
        message.binaryOptionsMarketInstantListingFee =
            (object.binaryOptionsMarketInstantListingFee !== undefined &&
                object.binaryOptionsMarketInstantListingFee !== null)
                ? coin_1.Coin.fromPartial(object.binaryOptionsMarketInstantListingFee)
                : undefined;
        message.atomicMarketOrderAccessLevel = (_r = object.atomicMarketOrderAccessLevel) !== null && _r !== void 0 ? _r : 0;
        message.spotAtomicMarketOrderFeeMultiplier = (_s = object.spotAtomicMarketOrderFeeMultiplier) !== null && _s !== void 0 ? _s : "";
        message.derivativeAtomicMarketOrderFeeMultiplier = (_t = object.derivativeAtomicMarketOrderFeeMultiplier) !== null && _t !== void 0 ? _t : "";
        message.binaryOptionsAtomicMarketOrderFeeMultiplier = (_u = object.binaryOptionsAtomicMarketOrderFeeMultiplier) !== null && _u !== void 0 ? _u : "";
        message.minimalProtocolFeeRate = (_v = object.minimalProtocolFeeRate) !== null && _v !== void 0 ? _v : "";
        message.isInstantDerivativeMarketLaunchEnabled = (_w = object.isInstantDerivativeMarketLaunchEnabled) !== null && _w !== void 0 ? _w : false;
        message.postOnlyModeHeightThreshold = (_x = object.postOnlyModeHeightThreshold) !== null && _x !== void 0 ? _x : "0";
        message.marginDecreasePriceTimestampThresholdSeconds = (_y = object.marginDecreasePriceTimestampThresholdSeconds) !== null && _y !== void 0 ? _y : "0";
        message.exchangeAdmins = ((_z = object.exchangeAdmins) === null || _z === void 0 ? void 0 : _z.map(function (e) { return e; })) || [];
        message.injAuctionMaxCap = (_0 = object.injAuctionMaxCap) !== null && _0 !== void 0 ? _0 : "";
        return message;
    },
};
function createBaseMarketFeeMultiplier() {
    return { marketId: "", feeMultiplier: "" };
}
exports.MarketFeeMultiplier = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.feeMultiplier !== "") {
            writer.uint32(18).string(message.feeMultiplier);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMarketFeeMultiplier();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.feeMultiplier = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            feeMultiplier: isSet(object.feeMultiplier) ? String(object.feeMultiplier) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.feeMultiplier !== undefined && (obj.feeMultiplier = message.feeMultiplier);
        return obj;
    },
    create: function (base) {
        return exports.MarketFeeMultiplier.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMarketFeeMultiplier();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.feeMultiplier = (_b = object.feeMultiplier) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseDerivativeMarket() {
    return {
        ticker: "",
        oracleBase: "",
        oracleQuote: "",
        oracleType: 0,
        oracleScaleFactor: 0,
        quoteDenom: "",
        marketId: "",
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        isPerpetual: false,
        status: 0,
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        admin: "",
        adminPermissions: 0,
        quoteDecimals: 0,
    };
}
exports.DerivativeMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ticker !== "") {
            writer.uint32(10).string(message.ticker);
        }
        if (message.oracleBase !== "") {
            writer.uint32(18).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(26).string(message.oracleQuote);
        }
        if (message.oracleType !== 0) {
            writer.uint32(32).int32(message.oracleType);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(40).uint32(message.oracleScaleFactor);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(50).string(message.quoteDenom);
        }
        if (message.marketId !== "") {
            writer.uint32(58).string(message.marketId);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(66).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(74).string(message.maintenanceMarginRatio);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(82).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(90).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(98).string(message.relayerFeeShareRate);
        }
        if (message.isPerpetual === true) {
            writer.uint32(104).bool(message.isPerpetual);
        }
        if (message.status !== 0) {
            writer.uint32(112).int32(message.status);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(122).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(130).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(138).string(message.minNotional);
        }
        if (message.admin !== "") {
            writer.uint32(146).string(message.admin);
        }
        if (message.adminPermissions !== 0) {
            writer.uint32(152).uint32(message.adminPermissions);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(160).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ticker = reader.string();
                    break;
                case 2:
                    message.oracleBase = reader.string();
                    break;
                case 3:
                    message.oracleQuote = reader.string();
                    break;
                case 4:
                    message.oracleType = reader.int32();
                    break;
                case 5:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 6:
                    message.quoteDenom = reader.string();
                    break;
                case 7:
                    message.marketId = reader.string();
                    break;
                case 8:
                    message.initialMarginRatio = reader.string();
                    break;
                case 9:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 10:
                    message.makerFeeRate = reader.string();
                    break;
                case 11:
                    message.takerFeeRate = reader.string();
                    break;
                case 12:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 13:
                    message.isPerpetual = reader.bool();
                    break;
                case 14:
                    message.status = reader.int32();
                    break;
                case 15:
                    message.minPriceTickSize = reader.string();
                    break;
                case 16:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 17:
                    message.minNotional = reader.string();
                    break;
                case 18:
                    message.admin = reader.string();
                    break;
                case 19:
                    message.adminPermissions = reader.uint32();
                    break;
                case 20:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            isPerpetual: isSet(object.isPerpetual) ? Boolean(object.isPerpetual) : false,
            status: isSet(object.status) ? marketStatusFromJSON(object.status) : 0,
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            admin: isSet(object.admin) ? String(object.admin) : "",
            adminPermissions: isSet(object.adminPermissions) ? Number(object.adminPermissions) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.isPerpetual !== undefined && (obj.isPerpetual = message.isPerpetual);
        message.status !== undefined && (obj.status = marketStatusToJSON(message.status));
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.admin !== undefined && (obj.admin = message.admin);
        message.adminPermissions !== undefined && (obj.adminPermissions = Math.round(message.adminPermissions));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
        var message = createBaseDerivativeMarket();
        message.ticker = (_a = object.ticker) !== null && _a !== void 0 ? _a : "";
        message.oracleBase = (_b = object.oracleBase) !== null && _b !== void 0 ? _b : "";
        message.oracleQuote = (_c = object.oracleQuote) !== null && _c !== void 0 ? _c : "";
        message.oracleType = (_d = object.oracleType) !== null && _d !== void 0 ? _d : 0;
        message.oracleScaleFactor = (_e = object.oracleScaleFactor) !== null && _e !== void 0 ? _e : 0;
        message.quoteDenom = (_f = object.quoteDenom) !== null && _f !== void 0 ? _f : "";
        message.marketId = (_g = object.marketId) !== null && _g !== void 0 ? _g : "";
        message.initialMarginRatio = (_h = object.initialMarginRatio) !== null && _h !== void 0 ? _h : "";
        message.maintenanceMarginRatio = (_j = object.maintenanceMarginRatio) !== null && _j !== void 0 ? _j : "";
        message.makerFeeRate = (_k = object.makerFeeRate) !== null && _k !== void 0 ? _k : "";
        message.takerFeeRate = (_l = object.takerFeeRate) !== null && _l !== void 0 ? _l : "";
        message.relayerFeeShareRate = (_m = object.relayerFeeShareRate) !== null && _m !== void 0 ? _m : "";
        message.isPerpetual = (_o = object.isPerpetual) !== null && _o !== void 0 ? _o : false;
        message.status = (_p = object.status) !== null && _p !== void 0 ? _p : 0;
        message.minPriceTickSize = (_q = object.minPriceTickSize) !== null && _q !== void 0 ? _q : "";
        message.minQuantityTickSize = (_r = object.minQuantityTickSize) !== null && _r !== void 0 ? _r : "";
        message.minNotional = (_s = object.minNotional) !== null && _s !== void 0 ? _s : "";
        message.admin = (_t = object.admin) !== null && _t !== void 0 ? _t : "";
        message.adminPermissions = (_u = object.adminPermissions) !== null && _u !== void 0 ? _u : 0;
        message.quoteDecimals = (_v = object.quoteDecimals) !== null && _v !== void 0 ? _v : 0;
        return message;
    },
};
function createBaseBinaryOptionsMarket() {
    return {
        ticker: "",
        oracleSymbol: "",
        oracleProvider: "",
        oracleType: 0,
        oracleScaleFactor: 0,
        expirationTimestamp: "0",
        settlementTimestamp: "0",
        admin: "",
        quoteDenom: "",
        marketId: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        status: 0,
        minPriceTickSize: "",
        minQuantityTickSize: "",
        settlementPrice: "",
        minNotional: "",
        adminPermissions: 0,
        quoteDecimals: 0,
    };
}
exports.BinaryOptionsMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ticker !== "") {
            writer.uint32(10).string(message.ticker);
        }
        if (message.oracleSymbol !== "") {
            writer.uint32(18).string(message.oracleSymbol);
        }
        if (message.oracleProvider !== "") {
            writer.uint32(26).string(message.oracleProvider);
        }
        if (message.oracleType !== 0) {
            writer.uint32(32).int32(message.oracleType);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(40).uint32(message.oracleScaleFactor);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(48).int64(message.expirationTimestamp);
        }
        if (message.settlementTimestamp !== "0") {
            writer.uint32(56).int64(message.settlementTimestamp);
        }
        if (message.admin !== "") {
            writer.uint32(66).string(message.admin);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(74).string(message.quoteDenom);
        }
        if (message.marketId !== "") {
            writer.uint32(82).string(message.marketId);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(90).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(98).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(106).string(message.relayerFeeShareRate);
        }
        if (message.status !== 0) {
            writer.uint32(112).int32(message.status);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(122).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(130).string(message.minQuantityTickSize);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(138).string(message.settlementPrice);
        }
        if (message.minNotional !== "") {
            writer.uint32(146).string(message.minNotional);
        }
        if (message.adminPermissions !== 0) {
            writer.uint32(152).uint32(message.adminPermissions);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(160).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBinaryOptionsMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ticker = reader.string();
                    break;
                case 2:
                    message.oracleSymbol = reader.string();
                    break;
                case 3:
                    message.oracleProvider = reader.string();
                    break;
                case 4:
                    message.oracleType = reader.int32();
                    break;
                case 5:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 6:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 7:
                    message.settlementTimestamp = longToString(reader.int64());
                    break;
                case 8:
                    message.admin = reader.string();
                    break;
                case 9:
                    message.quoteDenom = reader.string();
                    break;
                case 10:
                    message.marketId = reader.string();
                    break;
                case 11:
                    message.makerFeeRate = reader.string();
                    break;
                case 12:
                    message.takerFeeRate = reader.string();
                    break;
                case 13:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 14:
                    message.status = reader.int32();
                    break;
                case 15:
                    message.minPriceTickSize = reader.string();
                    break;
                case 16:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 17:
                    message.settlementPrice = reader.string();
                    break;
                case 18:
                    message.minNotional = reader.string();
                    break;
                case 19:
                    message.adminPermissions = reader.uint32();
                    break;
                case 20:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            oracleSymbol: isSet(object.oracleSymbol) ? String(object.oracleSymbol) : "",
            oracleProvider: isSet(object.oracleProvider) ? String(object.oracleProvider) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            settlementTimestamp: isSet(object.settlementTimestamp) ? String(object.settlementTimestamp) : "0",
            admin: isSet(object.admin) ? String(object.admin) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            status: isSet(object.status) ? marketStatusFromJSON(object.status) : 0,
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminPermissions: isSet(object.adminPermissions) ? Number(object.adminPermissions) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.oracleSymbol !== undefined && (obj.oracleSymbol = message.oracleSymbol);
        message.oracleProvider !== undefined && (obj.oracleProvider = message.oracleProvider);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.settlementTimestamp !== undefined && (obj.settlementTimestamp = message.settlementTimestamp);
        message.admin !== undefined && (obj.admin = message.admin);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.status !== undefined && (obj.status = marketStatusToJSON(message.status));
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminPermissions !== undefined && (obj.adminPermissions = Math.round(message.adminPermissions));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.BinaryOptionsMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v;
        var message = createBaseBinaryOptionsMarket();
        message.ticker = (_a = object.ticker) !== null && _a !== void 0 ? _a : "";
        message.oracleSymbol = (_b = object.oracleSymbol) !== null && _b !== void 0 ? _b : "";
        message.oracleProvider = (_c = object.oracleProvider) !== null && _c !== void 0 ? _c : "";
        message.oracleType = (_d = object.oracleType) !== null && _d !== void 0 ? _d : 0;
        message.oracleScaleFactor = (_e = object.oracleScaleFactor) !== null && _e !== void 0 ? _e : 0;
        message.expirationTimestamp = (_f = object.expirationTimestamp) !== null && _f !== void 0 ? _f : "0";
        message.settlementTimestamp = (_g = object.settlementTimestamp) !== null && _g !== void 0 ? _g : "0";
        message.admin = (_h = object.admin) !== null && _h !== void 0 ? _h : "";
        message.quoteDenom = (_j = object.quoteDenom) !== null && _j !== void 0 ? _j : "";
        message.marketId = (_k = object.marketId) !== null && _k !== void 0 ? _k : "";
        message.makerFeeRate = (_l = object.makerFeeRate) !== null && _l !== void 0 ? _l : "";
        message.takerFeeRate = (_m = object.takerFeeRate) !== null && _m !== void 0 ? _m : "";
        message.relayerFeeShareRate = (_o = object.relayerFeeShareRate) !== null && _o !== void 0 ? _o : "";
        message.status = (_p = object.status) !== null && _p !== void 0 ? _p : 0;
        message.minPriceTickSize = (_q = object.minPriceTickSize) !== null && _q !== void 0 ? _q : "";
        message.minQuantityTickSize = (_r = object.minQuantityTickSize) !== null && _r !== void 0 ? _r : "";
        message.settlementPrice = (_s = object.settlementPrice) !== null && _s !== void 0 ? _s : "";
        message.minNotional = (_t = object.minNotional) !== null && _t !== void 0 ? _t : "";
        message.adminPermissions = (_u = object.adminPermissions) !== null && _u !== void 0 ? _u : 0;
        message.quoteDecimals = (_v = object.quoteDecimals) !== null && _v !== void 0 ? _v : 0;
        return message;
    },
};
function createBaseExpiryFuturesMarketInfo() {
    return {
        marketId: "",
        expirationTimestamp: "0",
        twapStartTimestamp: "0",
        expirationTwapStartPriceCumulative: "",
        settlementPrice: "",
    };
}
exports.ExpiryFuturesMarketInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(16).int64(message.expirationTimestamp);
        }
        if (message.twapStartTimestamp !== "0") {
            writer.uint32(24).int64(message.twapStartTimestamp);
        }
        if (message.expirationTwapStartPriceCumulative !== "") {
            writer.uint32(34).string(message.expirationTwapStartPriceCumulative);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(42).string(message.settlementPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExpiryFuturesMarketInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 3:
                    message.twapStartTimestamp = longToString(reader.int64());
                    break;
                case 4:
                    message.expirationTwapStartPriceCumulative = reader.string();
                    break;
                case 5:
                    message.settlementPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            twapStartTimestamp: isSet(object.twapStartTimestamp) ? String(object.twapStartTimestamp) : "0",
            expirationTwapStartPriceCumulative: isSet(object.expirationTwapStartPriceCumulative)
                ? String(object.expirationTwapStartPriceCumulative)
                : "",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.twapStartTimestamp !== undefined && (obj.twapStartTimestamp = message.twapStartTimestamp);
        message.expirationTwapStartPriceCumulative !== undefined &&
            (obj.expirationTwapStartPriceCumulative = message.expirationTwapStartPriceCumulative);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        return obj;
    },
    create: function (base) {
        return exports.ExpiryFuturesMarketInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseExpiryFuturesMarketInfo();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.expirationTimestamp = (_b = object.expirationTimestamp) !== null && _b !== void 0 ? _b : "0";
        message.twapStartTimestamp = (_c = object.twapStartTimestamp) !== null && _c !== void 0 ? _c : "0";
        message.expirationTwapStartPriceCumulative = (_d = object.expirationTwapStartPriceCumulative) !== null && _d !== void 0 ? _d : "";
        message.settlementPrice = (_e = object.settlementPrice) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBasePerpetualMarketInfo() {
    return {
        marketId: "",
        hourlyFundingRateCap: "",
        hourlyInterestRate: "",
        nextFundingTimestamp: "0",
        fundingInterval: "0",
    };
}
exports.PerpetualMarketInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.hourlyFundingRateCap !== "") {
            writer.uint32(18).string(message.hourlyFundingRateCap);
        }
        if (message.hourlyInterestRate !== "") {
            writer.uint32(26).string(message.hourlyInterestRate);
        }
        if (message.nextFundingTimestamp !== "0") {
            writer.uint32(32).int64(message.nextFundingTimestamp);
        }
        if (message.fundingInterval !== "0") {
            writer.uint32(40).int64(message.fundingInterval);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePerpetualMarketInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.hourlyFundingRateCap = reader.string();
                    break;
                case 3:
                    message.hourlyInterestRate = reader.string();
                    break;
                case 4:
                    message.nextFundingTimestamp = longToString(reader.int64());
                    break;
                case 5:
                    message.fundingInterval = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            hourlyFundingRateCap: isSet(object.hourlyFundingRateCap) ? String(object.hourlyFundingRateCap) : "",
            hourlyInterestRate: isSet(object.hourlyInterestRate) ? String(object.hourlyInterestRate) : "",
            nextFundingTimestamp: isSet(object.nextFundingTimestamp) ? String(object.nextFundingTimestamp) : "0",
            fundingInterval: isSet(object.fundingInterval) ? String(object.fundingInterval) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.hourlyFundingRateCap !== undefined && (obj.hourlyFundingRateCap = message.hourlyFundingRateCap);
        message.hourlyInterestRate !== undefined && (obj.hourlyInterestRate = message.hourlyInterestRate);
        message.nextFundingTimestamp !== undefined && (obj.nextFundingTimestamp = message.nextFundingTimestamp);
        message.fundingInterval !== undefined && (obj.fundingInterval = message.fundingInterval);
        return obj;
    },
    create: function (base) {
        return exports.PerpetualMarketInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBasePerpetualMarketInfo();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.hourlyFundingRateCap = (_b = object.hourlyFundingRateCap) !== null && _b !== void 0 ? _b : "";
        message.hourlyInterestRate = (_c = object.hourlyInterestRate) !== null && _c !== void 0 ? _c : "";
        message.nextFundingTimestamp = (_d = object.nextFundingTimestamp) !== null && _d !== void 0 ? _d : "0";
        message.fundingInterval = (_e = object.fundingInterval) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBasePerpetualMarketFunding() {
    return { cumulativeFunding: "", cumulativePrice: "", lastTimestamp: "0" };
}
exports.PerpetualMarketFunding = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.cumulativeFunding !== "") {
            writer.uint32(10).string(message.cumulativeFunding);
        }
        if (message.cumulativePrice !== "") {
            writer.uint32(18).string(message.cumulativePrice);
        }
        if (message.lastTimestamp !== "0") {
            writer.uint32(24).int64(message.lastTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePerpetualMarketFunding();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.cumulativeFunding = reader.string();
                    break;
                case 2:
                    message.cumulativePrice = reader.string();
                    break;
                case 3:
                    message.lastTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            cumulativeFunding: isSet(object.cumulativeFunding) ? String(object.cumulativeFunding) : "",
            cumulativePrice: isSet(object.cumulativePrice) ? String(object.cumulativePrice) : "",
            lastTimestamp: isSet(object.lastTimestamp) ? String(object.lastTimestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.cumulativeFunding !== undefined && (obj.cumulativeFunding = message.cumulativeFunding);
        message.cumulativePrice !== undefined && (obj.cumulativePrice = message.cumulativePrice);
        message.lastTimestamp !== undefined && (obj.lastTimestamp = message.lastTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.PerpetualMarketFunding.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePerpetualMarketFunding();
        message.cumulativeFunding = (_a = object.cumulativeFunding) !== null && _a !== void 0 ? _a : "";
        message.cumulativePrice = (_b = object.cumulativePrice) !== null && _b !== void 0 ? _b : "";
        message.lastTimestamp = (_c = object.lastTimestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseDerivativeMarketSettlementInfo() {
    return { marketId: "", settlementPrice: "" };
}
exports.DerivativeMarketSettlementInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(18).string(message.settlementPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarketSettlementInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.settlementPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarketSettlementInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDerivativeMarketSettlementInfo();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.settlementPrice = (_b = object.settlementPrice) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseNextFundingTimestamp() {
    return { nextTimestamp: "0" };
}
exports.NextFundingTimestamp = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nextTimestamp !== "0") {
            writer.uint32(8).int64(message.nextTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseNextFundingTimestamp();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nextTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { nextTimestamp: isSet(object.nextTimestamp) ? String(object.nextTimestamp) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.nextTimestamp !== undefined && (obj.nextTimestamp = message.nextTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.NextFundingTimestamp.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseNextFundingTimestamp();
        message.nextTimestamp = (_a = object.nextTimestamp) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseMidPriceAndTOB() {
    return { midPrice: "", bestBuyPrice: "", bestSellPrice: "" };
}
exports.MidPriceAndTOB = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.midPrice !== "") {
            writer.uint32(10).string(message.midPrice);
        }
        if (message.bestBuyPrice !== "") {
            writer.uint32(18).string(message.bestBuyPrice);
        }
        if (message.bestSellPrice !== "") {
            writer.uint32(26).string(message.bestSellPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMidPriceAndTOB();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.midPrice = reader.string();
                    break;
                case 2:
                    message.bestBuyPrice = reader.string();
                    break;
                case 3:
                    message.bestSellPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            midPrice: isSet(object.midPrice) ? String(object.midPrice) : "",
            bestBuyPrice: isSet(object.bestBuyPrice) ? String(object.bestBuyPrice) : "",
            bestSellPrice: isSet(object.bestSellPrice) ? String(object.bestSellPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.midPrice !== undefined && (obj.midPrice = message.midPrice);
        message.bestBuyPrice !== undefined && (obj.bestBuyPrice = message.bestBuyPrice);
        message.bestSellPrice !== undefined && (obj.bestSellPrice = message.bestSellPrice);
        return obj;
    },
    create: function (base) {
        return exports.MidPriceAndTOB.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseMidPriceAndTOB();
        message.midPrice = (_a = object.midPrice) !== null && _a !== void 0 ? _a : "";
        message.bestBuyPrice = (_b = object.bestBuyPrice) !== null && _b !== void 0 ? _b : "";
        message.bestSellPrice = (_c = object.bestSellPrice) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseSpotMarket() {
    return {
        ticker: "",
        baseDenom: "",
        quoteDenom: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        marketId: "",
        status: 0,
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        admin: "",
        adminPermissions: 0,
        baseDecimals: 0,
        quoteDecimals: 0,
    };
}
exports.SpotMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ticker !== "") {
            writer.uint32(10).string(message.ticker);
        }
        if (message.baseDenom !== "") {
            writer.uint32(18).string(message.baseDenom);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(26).string(message.quoteDenom);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(34).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(42).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(50).string(message.relayerFeeShareRate);
        }
        if (message.marketId !== "") {
            writer.uint32(58).string(message.marketId);
        }
        if (message.status !== 0) {
            writer.uint32(64).int32(message.status);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(74).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(82).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(90).string(message.minNotional);
        }
        if (message.admin !== "") {
            writer.uint32(98).string(message.admin);
        }
        if (message.adminPermissions !== 0) {
            writer.uint32(104).uint32(message.adminPermissions);
        }
        if (message.baseDecimals !== 0) {
            writer.uint32(112).uint32(message.baseDecimals);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(120).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ticker = reader.string();
                    break;
                case 2:
                    message.baseDenom = reader.string();
                    break;
                case 3:
                    message.quoteDenom = reader.string();
                    break;
                case 4:
                    message.makerFeeRate = reader.string();
                    break;
                case 5:
                    message.takerFeeRate = reader.string();
                    break;
                case 6:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 7:
                    message.marketId = reader.string();
                    break;
                case 8:
                    message.status = reader.int32();
                    break;
                case 9:
                    message.minPriceTickSize = reader.string();
                    break;
                case 10:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 11:
                    message.minNotional = reader.string();
                    break;
                case 12:
                    message.admin = reader.string();
                    break;
                case 13:
                    message.adminPermissions = reader.uint32();
                    break;
                case 14:
                    message.baseDecimals = reader.uint32();
                    break;
                case 15:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            baseDenom: isSet(object.baseDenom) ? String(object.baseDenom) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            status: isSet(object.status) ? marketStatusFromJSON(object.status) : 0,
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            admin: isSet(object.admin) ? String(object.admin) : "",
            adminPermissions: isSet(object.adminPermissions) ? Number(object.adminPermissions) : 0,
            baseDecimals: isSet(object.baseDecimals) ? Number(object.baseDecimals) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.baseDenom !== undefined && (obj.baseDenom = message.baseDenom);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.status !== undefined && (obj.status = marketStatusToJSON(message.status));
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.admin !== undefined && (obj.admin = message.admin);
        message.adminPermissions !== undefined && (obj.adminPermissions = Math.round(message.adminPermissions));
        message.baseDecimals !== undefined && (obj.baseDecimals = Math.round(message.baseDecimals));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.SpotMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseSpotMarket();
        message.ticker = (_a = object.ticker) !== null && _a !== void 0 ? _a : "";
        message.baseDenom = (_b = object.baseDenom) !== null && _b !== void 0 ? _b : "";
        message.quoteDenom = (_c = object.quoteDenom) !== null && _c !== void 0 ? _c : "";
        message.makerFeeRate = (_d = object.makerFeeRate) !== null && _d !== void 0 ? _d : "";
        message.takerFeeRate = (_e = object.takerFeeRate) !== null && _e !== void 0 ? _e : "";
        message.relayerFeeShareRate = (_f = object.relayerFeeShareRate) !== null && _f !== void 0 ? _f : "";
        message.marketId = (_g = object.marketId) !== null && _g !== void 0 ? _g : "";
        message.status = (_h = object.status) !== null && _h !== void 0 ? _h : 0;
        message.minPriceTickSize = (_j = object.minPriceTickSize) !== null && _j !== void 0 ? _j : "";
        message.minQuantityTickSize = (_k = object.minQuantityTickSize) !== null && _k !== void 0 ? _k : "";
        message.minNotional = (_l = object.minNotional) !== null && _l !== void 0 ? _l : "";
        message.admin = (_m = object.admin) !== null && _m !== void 0 ? _m : "";
        message.adminPermissions = (_o = object.adminPermissions) !== null && _o !== void 0 ? _o : 0;
        message.baseDecimals = (_p = object.baseDecimals) !== null && _p !== void 0 ? _p : 0;
        message.quoteDecimals = (_q = object.quoteDecimals) !== null && _q !== void 0 ? _q : 0;
        return message;
    },
};
function createBaseDeposit() {
    return { availableBalance: "", totalBalance: "" };
}
exports.Deposit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.availableBalance !== "") {
            writer.uint32(10).string(message.availableBalance);
        }
        if (message.totalBalance !== "") {
            writer.uint32(18).string(message.totalBalance);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDeposit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.availableBalance = reader.string();
                    break;
                case 2:
                    message.totalBalance = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            availableBalance: isSet(object.availableBalance) ? String(object.availableBalance) : "",
            totalBalance: isSet(object.totalBalance) ? String(object.totalBalance) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.availableBalance !== undefined && (obj.availableBalance = message.availableBalance);
        message.totalBalance !== undefined && (obj.totalBalance = message.totalBalance);
        return obj;
    },
    create: function (base) {
        return exports.Deposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDeposit();
        message.availableBalance = (_a = object.availableBalance) !== null && _a !== void 0 ? _a : "";
        message.totalBalance = (_b = object.totalBalance) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseSubaccountTradeNonce() {
    return { nonce: 0 };
}
exports.SubaccountTradeNonce = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== 0) {
            writer.uint32(8).uint32(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountTradeNonce();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { nonce: isSet(object.nonce) ? Number(object.nonce) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = Math.round(message.nonce));
        return obj;
    },
    create: function (base) {
        return exports.SubaccountTradeNonce.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountTradeNonce();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseOrderInfo() {
    return { subaccountId: "", feeRecipient: "", price: "", quantity: "", cid: "" };
}
exports.OrderInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.feeRecipient !== "") {
            writer.uint32(18).string(message.feeRecipient);
        }
        if (message.price !== "") {
            writer.uint32(26).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(34).string(message.quantity);
        }
        if (message.cid !== "") {
            writer.uint32(42).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.feeRecipient = reader.string();
                    break;
                case 3:
                    message.price = reader.string();
                    break;
                case 4:
                    message.quantity = reader.string();
                    break;
                case 5:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            feeRecipient: isSet(object.feeRecipient) ? String(object.feeRecipient) : "",
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.feeRecipient !== undefined && (obj.feeRecipient = message.feeRecipient);
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.OrderInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseOrderInfo();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.feeRecipient = (_b = object.feeRecipient) !== null && _b !== void 0 ? _b : "";
        message.price = (_c = object.price) !== null && _c !== void 0 ? _c : "";
        message.quantity = (_d = object.quantity) !== null && _d !== void 0 ? _d : "";
        message.cid = (_e = object.cid) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseSpotOrder() {
    return { marketId: "", orderInfo: undefined, orderType: 0, triggerPrice: "" };
}
exports.SpotOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.orderType !== 0) {
            writer.uint32(24).int32(message.orderType);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(34).string(message.triggerPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.orderType = reader.int32();
                    break;
                case 4:
                    message.triggerPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        return obj;
    },
    create: function (base) {
        return exports.SpotOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSpotOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.orderType = (_b = object.orderType) !== null && _b !== void 0 ? _b : 0;
        message.triggerPrice = (_c = object.triggerPrice) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseSpotLimitOrder() {
    return { orderInfo: undefined, orderType: 0, fillable: "", triggerPrice: "", orderHash: new Uint8Array() };
}
exports.SpotLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.orderType !== 0) {
            writer.uint32(16).int32(message.orderType);
        }
        if (message.fillable !== "") {
            writer.uint32(26).string(message.fillable);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(34).string(message.triggerPrice);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(42).bytes(message.orderHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.orderType = reader.int32();
                    break;
                case 3:
                    message.fillable = reader.string();
                    break;
                case 4:
                    message.triggerPrice = reader.string();
                    break;
                case 5:
                    message.orderHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            fillable: isSet(object.fillable) ? String(object.fillable) : "",
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.fillable !== undefined && (obj.fillable = message.fillable);
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.SpotLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSpotLimitOrder();
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.orderType = (_a = object.orderType) !== null && _a !== void 0 ? _a : 0;
        message.fillable = (_b = object.fillable) !== null && _b !== void 0 ? _b : "";
        message.triggerPrice = (_c = object.triggerPrice) !== null && _c !== void 0 ? _c : "";
        message.orderHash = (_d = object.orderHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        return message;
    },
};
function createBaseSpotMarketOrder() {
    return { orderInfo: undefined, balanceHold: "", orderHash: new Uint8Array(), orderType: 0, triggerPrice: "" };
}
exports.SpotMarketOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.balanceHold !== "") {
            writer.uint32(18).string(message.balanceHold);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(26).bytes(message.orderHash);
        }
        if (message.orderType !== 0) {
            writer.uint32(32).int32(message.orderType);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(42).string(message.triggerPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotMarketOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.balanceHold = reader.string();
                    break;
                case 3:
                    message.orderHash = reader.bytes();
                    break;
                case 4:
                    message.orderType = reader.int32();
                    break;
                case 5:
                    message.triggerPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            balanceHold: isSet(object.balanceHold) ? String(object.balanceHold) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.balanceHold !== undefined && (obj.balanceHold = message.balanceHold);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        return obj;
    },
    create: function (base) {
        return exports.SpotMarketOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSpotMarketOrder();
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.balanceHold = (_a = object.balanceHold) !== null && _a !== void 0 ? _a : "";
        message.orderHash = (_b = object.orderHash) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.orderType = (_c = object.orderType) !== null && _c !== void 0 ? _c : 0;
        message.triggerPrice = (_d = object.triggerPrice) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseDerivativeOrder() {
    return { marketId: "", orderInfo: undefined, orderType: 0, margin: "", triggerPrice: "" };
}
exports.DerivativeOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.orderType !== 0) {
            writer.uint32(24).int32(message.orderType);
        }
        if (message.margin !== "") {
            writer.uint32(34).string(message.margin);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(42).string(message.triggerPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.orderType = reader.int32();
                    break;
                case 4:
                    message.margin = reader.string();
                    break;
                case 5:
                    message.triggerPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            margin: isSet(object.margin) ? String(object.margin) : "",
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.margin !== undefined && (obj.margin = message.margin);
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseDerivativeOrder();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.orderType = (_b = object.orderType) !== null && _b !== void 0 ? _b : 0;
        message.margin = (_c = object.margin) !== null && _c !== void 0 ? _c : "";
        message.triggerPrice = (_d = object.triggerPrice) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseSubaccountOrderbookMetadata() {
    return {
        vanillaLimitOrderCount: 0,
        reduceOnlyLimitOrderCount: 0,
        aggregateReduceOnlyQuantity: "",
        aggregateVanillaQuantity: "",
        vanillaConditionalOrderCount: 0,
        reduceOnlyConditionalOrderCount: 0,
    };
}
exports.SubaccountOrderbookMetadata = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.vanillaLimitOrderCount !== 0) {
            writer.uint32(8).uint32(message.vanillaLimitOrderCount);
        }
        if (message.reduceOnlyLimitOrderCount !== 0) {
            writer.uint32(16).uint32(message.reduceOnlyLimitOrderCount);
        }
        if (message.aggregateReduceOnlyQuantity !== "") {
            writer.uint32(26).string(message.aggregateReduceOnlyQuantity);
        }
        if (message.aggregateVanillaQuantity !== "") {
            writer.uint32(34).string(message.aggregateVanillaQuantity);
        }
        if (message.vanillaConditionalOrderCount !== 0) {
            writer.uint32(40).uint32(message.vanillaConditionalOrderCount);
        }
        if (message.reduceOnlyConditionalOrderCount !== 0) {
            writer.uint32(48).uint32(message.reduceOnlyConditionalOrderCount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountOrderbookMetadata();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.vanillaLimitOrderCount = reader.uint32();
                    break;
                case 2:
                    message.reduceOnlyLimitOrderCount = reader.uint32();
                    break;
                case 3:
                    message.aggregateReduceOnlyQuantity = reader.string();
                    break;
                case 4:
                    message.aggregateVanillaQuantity = reader.string();
                    break;
                case 5:
                    message.vanillaConditionalOrderCount = reader.uint32();
                    break;
                case 6:
                    message.reduceOnlyConditionalOrderCount = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            vanillaLimitOrderCount: isSet(object.vanillaLimitOrderCount) ? Number(object.vanillaLimitOrderCount) : 0,
            reduceOnlyLimitOrderCount: isSet(object.reduceOnlyLimitOrderCount) ? Number(object.reduceOnlyLimitOrderCount) : 0,
            aggregateReduceOnlyQuantity: isSet(object.aggregateReduceOnlyQuantity)
                ? String(object.aggregateReduceOnlyQuantity)
                : "",
            aggregateVanillaQuantity: isSet(object.aggregateVanillaQuantity) ? String(object.aggregateVanillaQuantity) : "",
            vanillaConditionalOrderCount: isSet(object.vanillaConditionalOrderCount)
                ? Number(object.vanillaConditionalOrderCount)
                : 0,
            reduceOnlyConditionalOrderCount: isSet(object.reduceOnlyConditionalOrderCount)
                ? Number(object.reduceOnlyConditionalOrderCount)
                : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.vanillaLimitOrderCount !== undefined &&
            (obj.vanillaLimitOrderCount = Math.round(message.vanillaLimitOrderCount));
        message.reduceOnlyLimitOrderCount !== undefined &&
            (obj.reduceOnlyLimitOrderCount = Math.round(message.reduceOnlyLimitOrderCount));
        message.aggregateReduceOnlyQuantity !== undefined &&
            (obj.aggregateReduceOnlyQuantity = message.aggregateReduceOnlyQuantity);
        message.aggregateVanillaQuantity !== undefined && (obj.aggregateVanillaQuantity = message.aggregateVanillaQuantity);
        message.vanillaConditionalOrderCount !== undefined &&
            (obj.vanillaConditionalOrderCount = Math.round(message.vanillaConditionalOrderCount));
        message.reduceOnlyConditionalOrderCount !== undefined &&
            (obj.reduceOnlyConditionalOrderCount = Math.round(message.reduceOnlyConditionalOrderCount));
        return obj;
    },
    create: function (base) {
        return exports.SubaccountOrderbookMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseSubaccountOrderbookMetadata();
        message.vanillaLimitOrderCount = (_a = object.vanillaLimitOrderCount) !== null && _a !== void 0 ? _a : 0;
        message.reduceOnlyLimitOrderCount = (_b = object.reduceOnlyLimitOrderCount) !== null && _b !== void 0 ? _b : 0;
        message.aggregateReduceOnlyQuantity = (_c = object.aggregateReduceOnlyQuantity) !== null && _c !== void 0 ? _c : "";
        message.aggregateVanillaQuantity = (_d = object.aggregateVanillaQuantity) !== null && _d !== void 0 ? _d : "";
        message.vanillaConditionalOrderCount = (_e = object.vanillaConditionalOrderCount) !== null && _e !== void 0 ? _e : 0;
        message.reduceOnlyConditionalOrderCount = (_f = object.reduceOnlyConditionalOrderCount) !== null && _f !== void 0 ? _f : 0;
        return message;
    },
};
function createBaseSubaccountOrder() {
    return { price: "", quantity: "", isReduceOnly: false, cid: "" };
}
exports.SubaccountOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.isReduceOnly === true) {
            writer.uint32(24).bool(message.isReduceOnly);
        }
        if (message.cid !== "") {
            writer.uint32(34).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.isReduceOnly = reader.bool();
                    break;
                case 4:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            isReduceOnly: isSet(object.isReduceOnly) ? Boolean(object.isReduceOnly) : false,
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.isReduceOnly !== undefined && (obj.isReduceOnly = message.isReduceOnly);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.SubaccountOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSubaccountOrder();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.isReduceOnly = (_c = object.isReduceOnly) !== null && _c !== void 0 ? _c : false;
        message.cid = (_d = object.cid) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseSubaccountOrderData() {
    return { order: undefined, orderHash: new Uint8Array() };
}
exports.SubaccountOrderData = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.order !== undefined) {
            exports.SubaccountOrder.encode(message.order, writer.uint32(10).fork()).ldelim();
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(18).bytes(message.orderHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountOrderData();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.order = exports.SubaccountOrder.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.orderHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            order: isSet(object.order) ? exports.SubaccountOrder.fromJSON(object.order) : undefined,
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.order !== undefined && (obj.order = message.order ? exports.SubaccountOrder.toJSON(message.order) : undefined);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.SubaccountOrderData.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountOrderData();
        message.order = (object.order !== undefined && object.order !== null)
            ? exports.SubaccountOrder.fromPartial(object.order)
            : undefined;
        message.orderHash = (_a = object.orderHash) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseDerivativeLimitOrder() {
    return {
        orderInfo: undefined,
        orderType: 0,
        margin: "",
        fillable: "",
        triggerPrice: "",
        orderHash: new Uint8Array(),
    };
}
exports.DerivativeLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.orderType !== 0) {
            writer.uint32(16).int32(message.orderType);
        }
        if (message.margin !== "") {
            writer.uint32(26).string(message.margin);
        }
        if (message.fillable !== "") {
            writer.uint32(34).string(message.fillable);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(42).string(message.triggerPrice);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(50).bytes(message.orderHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.orderType = reader.int32();
                    break;
                case 3:
                    message.margin = reader.string();
                    break;
                case 4:
                    message.fillable = reader.string();
                    break;
                case 5:
                    message.triggerPrice = reader.string();
                    break;
                case 6:
                    message.orderHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            margin: isSet(object.margin) ? String(object.margin) : "",
            fillable: isSet(object.fillable) ? String(object.fillable) : "",
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.margin !== undefined && (obj.margin = message.margin);
        message.fillable !== undefined && (obj.fillable = message.fillable);
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.DerivativeLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseDerivativeLimitOrder();
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.orderType = (_a = object.orderType) !== null && _a !== void 0 ? _a : 0;
        message.margin = (_b = object.margin) !== null && _b !== void 0 ? _b : "";
        message.fillable = (_c = object.fillable) !== null && _c !== void 0 ? _c : "";
        message.triggerPrice = (_d = object.triggerPrice) !== null && _d !== void 0 ? _d : "";
        message.orderHash = (_e = object.orderHash) !== null && _e !== void 0 ? _e : new Uint8Array();
        return message;
    },
};
function createBaseDerivativeMarketOrder() {
    return {
        orderInfo: undefined,
        orderType: 0,
        margin: "",
        marginHold: "",
        triggerPrice: "",
        orderHash: new Uint8Array(),
    };
}
exports.DerivativeMarketOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.orderInfo !== undefined) {
            exports.OrderInfo.encode(message.orderInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.orderType !== 0) {
            writer.uint32(16).int32(message.orderType);
        }
        if (message.margin !== "") {
            writer.uint32(26).string(message.margin);
        }
        if (message.marginHold !== "") {
            writer.uint32(34).string(message.marginHold);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(42).string(message.triggerPrice);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(50).bytes(message.orderHash);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarketOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orderInfo = exports.OrderInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.orderType = reader.int32();
                    break;
                case 3:
                    message.margin = reader.string();
                    break;
                case 4:
                    message.marginHold = reader.string();
                    break;
                case 5:
                    message.triggerPrice = reader.string();
                    break;
                case 6:
                    message.orderHash = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orderInfo: isSet(object.orderInfo) ? exports.OrderInfo.fromJSON(object.orderInfo) : undefined,
            orderType: isSet(object.orderType) ? orderTypeFromJSON(object.orderType) : 0,
            margin: isSet(object.margin) ? String(object.margin) : "",
            marginHold: isSet(object.marginHold) ? String(object.marginHold) : "",
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.orderInfo !== undefined &&
            (obj.orderInfo = message.orderInfo ? exports.OrderInfo.toJSON(message.orderInfo) : undefined);
        message.orderType !== undefined && (obj.orderType = orderTypeToJSON(message.orderType));
        message.margin !== undefined && (obj.margin = message.margin);
        message.marginHold !== undefined && (obj.marginHold = message.marginHold);
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarketOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseDerivativeMarketOrder();
        message.orderInfo = (object.orderInfo !== undefined && object.orderInfo !== null)
            ? exports.OrderInfo.fromPartial(object.orderInfo)
            : undefined;
        message.orderType = (_a = object.orderType) !== null && _a !== void 0 ? _a : 0;
        message.margin = (_b = object.margin) !== null && _b !== void 0 ? _b : "";
        message.marginHold = (_c = object.marginHold) !== null && _c !== void 0 ? _c : "";
        message.triggerPrice = (_d = object.triggerPrice) !== null && _d !== void 0 ? _d : "";
        message.orderHash = (_e = object.orderHash) !== null && _e !== void 0 ? _e : new Uint8Array();
        return message;
    },
};
function createBasePosition() {
    return { isLong: false, quantity: "", entryPrice: "", margin: "", cumulativeFundingEntry: "" };
}
exports.Position = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.isLong === true) {
            writer.uint32(8).bool(message.isLong);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.entryPrice !== "") {
            writer.uint32(26).string(message.entryPrice);
        }
        if (message.margin !== "") {
            writer.uint32(34).string(message.margin);
        }
        if (message.cumulativeFundingEntry !== "") {
            writer.uint32(42).string(message.cumulativeFundingEntry);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.isLong = reader.bool();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.entryPrice = reader.string();
                    break;
                case 4:
                    message.margin = reader.string();
                    break;
                case 5:
                    message.cumulativeFundingEntry = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            isLong: isSet(object.isLong) ? Boolean(object.isLong) : false,
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            entryPrice: isSet(object.entryPrice) ? String(object.entryPrice) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            cumulativeFundingEntry: isSet(object.cumulativeFundingEntry) ? String(object.cumulativeFundingEntry) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.isLong !== undefined && (obj.isLong = message.isLong);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.entryPrice !== undefined && (obj.entryPrice = message.entryPrice);
        message.margin !== undefined && (obj.margin = message.margin);
        message.cumulativeFundingEntry !== undefined && (obj.cumulativeFundingEntry = message.cumulativeFundingEntry);
        return obj;
    },
    create: function (base) {
        return exports.Position.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBasePosition();
        message.isLong = (_a = object.isLong) !== null && _a !== void 0 ? _a : false;
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.entryPrice = (_c = object.entryPrice) !== null && _c !== void 0 ? _c : "";
        message.margin = (_d = object.margin) !== null && _d !== void 0 ? _d : "";
        message.cumulativeFundingEntry = (_e = object.cumulativeFundingEntry) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseMarketOrderIndicator() {
    return { marketId: "", isBuy: false };
}
exports.MarketOrderIndicator = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(16).bool(message.isBuy);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMarketOrderIndicator();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuy = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        return obj;
    },
    create: function (base) {
        return exports.MarketOrderIndicator.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMarketOrderIndicator();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseTradeLog() {
    return {
        quantity: "",
        price: "",
        subaccountId: new Uint8Array(),
        fee: "",
        orderHash: new Uint8Array(),
        feeRecipientAddress: new Uint8Array(),
        cid: "",
    };
}
exports.TradeLog = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.quantity !== "") {
            writer.uint32(10).string(message.quantity);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.subaccountId.length !== 0) {
            writer.uint32(26).bytes(message.subaccountId);
        }
        if (message.fee !== "") {
            writer.uint32(34).string(message.fee);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(42).bytes(message.orderHash);
        }
        if (message.feeRecipientAddress.length !== 0) {
            writer.uint32(50).bytes(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(58).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradeLog();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.quantity = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.subaccountId = reader.bytes();
                    break;
                case 4:
                    message.fee = reader.string();
                    break;
                case 5:
                    message.orderHash = reader.bytes();
                    break;
                case 6:
                    message.feeRecipientAddress = reader.bytes();
                    break;
                case 7:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            price: isSet(object.price) ? String(object.price) : "",
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
            feeRecipientAddress: isSet(object.feeRecipientAddress)
                ? bytesFromBase64(object.feeRecipientAddress)
                : new Uint8Array(),
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.price !== undefined && (obj.price = message.price);
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        message.feeRecipientAddress !== undefined &&
            (obj.feeRecipientAddress = base64FromBytes(message.feeRecipientAddress !== undefined ? message.feeRecipientAddress : new Uint8Array()));
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.TradeLog.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseTradeLog();
        message.quantity = (_a = object.quantity) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.subaccountId = (_c = object.subaccountId) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.fee = (_d = object.fee) !== null && _d !== void 0 ? _d : "";
        message.orderHash = (_e = object.orderHash) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.feeRecipientAddress = (_f = object.feeRecipientAddress) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.cid = (_g = object.cid) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBasePositionDelta() {
    return { isLong: false, executionQuantity: "", executionMargin: "", executionPrice: "" };
}
exports.PositionDelta = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.isLong === true) {
            writer.uint32(8).bool(message.isLong);
        }
        if (message.executionQuantity !== "") {
            writer.uint32(18).string(message.executionQuantity);
        }
        if (message.executionMargin !== "") {
            writer.uint32(26).string(message.executionMargin);
        }
        if (message.executionPrice !== "") {
            writer.uint32(34).string(message.executionPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePositionDelta();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.isLong = reader.bool();
                    break;
                case 2:
                    message.executionQuantity = reader.string();
                    break;
                case 3:
                    message.executionMargin = reader.string();
                    break;
                case 4:
                    message.executionPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            isLong: isSet(object.isLong) ? Boolean(object.isLong) : false,
            executionQuantity: isSet(object.executionQuantity) ? String(object.executionQuantity) : "",
            executionMargin: isSet(object.executionMargin) ? String(object.executionMargin) : "",
            executionPrice: isSet(object.executionPrice) ? String(object.executionPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.isLong !== undefined && (obj.isLong = message.isLong);
        message.executionQuantity !== undefined && (obj.executionQuantity = message.executionQuantity);
        message.executionMargin !== undefined && (obj.executionMargin = message.executionMargin);
        message.executionPrice !== undefined && (obj.executionPrice = message.executionPrice);
        return obj;
    },
    create: function (base) {
        return exports.PositionDelta.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBasePositionDelta();
        message.isLong = (_a = object.isLong) !== null && _a !== void 0 ? _a : false;
        message.executionQuantity = (_b = object.executionQuantity) !== null && _b !== void 0 ? _b : "";
        message.executionMargin = (_c = object.executionMargin) !== null && _c !== void 0 ? _c : "";
        message.executionPrice = (_d = object.executionPrice) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseDerivativeTradeLog() {
    return {
        subaccountId: new Uint8Array(),
        positionDelta: undefined,
        payout: "",
        fee: "",
        orderHash: new Uint8Array(),
        feeRecipientAddress: new Uint8Array(),
        cid: "",
        pnl: "",
    };
}
exports.DerivativeTradeLog = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId.length !== 0) {
            writer.uint32(10).bytes(message.subaccountId);
        }
        if (message.positionDelta !== undefined) {
            exports.PositionDelta.encode(message.positionDelta, writer.uint32(18).fork()).ldelim();
        }
        if (message.payout !== "") {
            writer.uint32(26).string(message.payout);
        }
        if (message.fee !== "") {
            writer.uint32(34).string(message.fee);
        }
        if (message.orderHash.length !== 0) {
            writer.uint32(42).bytes(message.orderHash);
        }
        if (message.feeRecipientAddress.length !== 0) {
            writer.uint32(50).bytes(message.feeRecipientAddress);
        }
        if (message.cid !== "") {
            writer.uint32(58).string(message.cid);
        }
        if (message.pnl !== "") {
            writer.uint32(66).string(message.pnl);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeTradeLog();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.bytes();
                    break;
                case 2:
                    message.positionDelta = exports.PositionDelta.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.payout = reader.string();
                    break;
                case 4:
                    message.fee = reader.string();
                    break;
                case 5:
                    message.orderHash = reader.bytes();
                    break;
                case 6:
                    message.feeRecipientAddress = reader.bytes();
                    break;
                case 7:
                    message.cid = reader.string();
                    break;
                case 8:
                    message.pnl = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            positionDelta: isSet(object.positionDelta) ? exports.PositionDelta.fromJSON(object.positionDelta) : undefined,
            payout: isSet(object.payout) ? String(object.payout) : "",
            fee: isSet(object.fee) ? String(object.fee) : "",
            orderHash: isSet(object.orderHash) ? bytesFromBase64(object.orderHash) : new Uint8Array(),
            feeRecipientAddress: isSet(object.feeRecipientAddress)
                ? bytesFromBase64(object.feeRecipientAddress)
                : new Uint8Array(),
            cid: isSet(object.cid) ? String(object.cid) : "",
            pnl: isSet(object.pnl) ? String(object.pnl) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.positionDelta !== undefined &&
            (obj.positionDelta = message.positionDelta ? exports.PositionDelta.toJSON(message.positionDelta) : undefined);
        message.payout !== undefined && (obj.payout = message.payout);
        message.fee !== undefined && (obj.fee = message.fee);
        message.orderHash !== undefined &&
            (obj.orderHash = base64FromBytes(message.orderHash !== undefined ? message.orderHash : new Uint8Array()));
        message.feeRecipientAddress !== undefined &&
            (obj.feeRecipientAddress = base64FromBytes(message.feeRecipientAddress !== undefined ? message.feeRecipientAddress : new Uint8Array()));
        message.cid !== undefined && (obj.cid = message.cid);
        message.pnl !== undefined && (obj.pnl = message.pnl);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeTradeLog.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseDerivativeTradeLog();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.positionDelta = (object.positionDelta !== undefined && object.positionDelta !== null)
            ? exports.PositionDelta.fromPartial(object.positionDelta)
            : undefined;
        message.payout = (_b = object.payout) !== null && _b !== void 0 ? _b : "";
        message.fee = (_c = object.fee) !== null && _c !== void 0 ? _c : "";
        message.orderHash = (_d = object.orderHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.feeRecipientAddress = (_e = object.feeRecipientAddress) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.cid = (_f = object.cid) !== null && _f !== void 0 ? _f : "";
        message.pnl = (_g = object.pnl) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseSubaccountPosition() {
    return { position: undefined, subaccountId: new Uint8Array() };
}
exports.SubaccountPosition = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.position !== undefined) {
            exports.Position.encode(message.position, writer.uint32(10).fork()).ldelim();
        }
        if (message.subaccountId.length !== 0) {
            writer.uint32(18).bytes(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountPosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.position = exports.Position.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.subaccountId = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            position: isSet(object.position) ? exports.Position.fromJSON(object.position) : undefined,
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.position !== undefined && (obj.position = message.position ? exports.Position.toJSON(message.position) : undefined);
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.SubaccountPosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountPosition();
        message.position = (object.position !== undefined && object.position !== null)
            ? exports.Position.fromPartial(object.position)
            : undefined;
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseSubaccountDeposit() {
    return { subaccountId: new Uint8Array(), deposit: undefined };
}
exports.SubaccountDeposit = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId.length !== 0) {
            writer.uint32(10).bytes(message.subaccountId);
        }
        if (message.deposit !== undefined) {
            exports.Deposit.encode(message.deposit, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountDeposit();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.bytes();
                    break;
                case 2:
                    message.deposit = exports.Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? bytesFromBase64(object.subaccountId) : new Uint8Array(),
            deposit: isSet(object.deposit) ? exports.Deposit.fromJSON(object.deposit) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined &&
            (obj.subaccountId = base64FromBytes(message.subaccountId !== undefined ? message.subaccountId : new Uint8Array()));
        message.deposit !== undefined && (obj.deposit = message.deposit ? exports.Deposit.toJSON(message.deposit) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SubaccountDeposit.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountDeposit();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? exports.Deposit.fromPartial(object.deposit)
            : undefined;
        return message;
    },
};
function createBaseDepositUpdate() {
    return { denom: "", deposits: [] };
}
exports.DepositUpdate = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        try {
            for (var _b = __values(message.deposits), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SubaccountDeposit.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDepositUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.deposits.push(exports.SubaccountDeposit.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            deposits: Array.isArray(object === null || object === void 0 ? void 0 : object.deposits) ? object.deposits.map(function (e) { return exports.SubaccountDeposit.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        if (message.deposits) {
            obj.deposits = message.deposits.map(function (e) { return e ? exports.SubaccountDeposit.toJSON(e) : undefined; });
        }
        else {
            obj.deposits = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.DepositUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDepositUpdate();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.deposits = ((_b = object.deposits) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.SubaccountDeposit.fromPartial(e); })) || [];
        return message;
    },
};
function createBasePointsMultiplier() {
    return { makerPointsMultiplier: "", takerPointsMultiplier: "" };
}
exports.PointsMultiplier = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.makerPointsMultiplier !== "") {
            writer.uint32(10).string(message.makerPointsMultiplier);
        }
        if (message.takerPointsMultiplier !== "") {
            writer.uint32(18).string(message.takerPointsMultiplier);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePointsMultiplier();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.makerPointsMultiplier = reader.string();
                    break;
                case 2:
                    message.takerPointsMultiplier = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            makerPointsMultiplier: isSet(object.makerPointsMultiplier) ? String(object.makerPointsMultiplier) : "",
            takerPointsMultiplier: isSet(object.takerPointsMultiplier) ? String(object.takerPointsMultiplier) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.makerPointsMultiplier !== undefined && (obj.makerPointsMultiplier = message.makerPointsMultiplier);
        message.takerPointsMultiplier !== undefined && (obj.takerPointsMultiplier = message.takerPointsMultiplier);
        return obj;
    },
    create: function (base) {
        return exports.PointsMultiplier.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePointsMultiplier();
        message.makerPointsMultiplier = (_a = object.makerPointsMultiplier) !== null && _a !== void 0 ? _a : "";
        message.takerPointsMultiplier = (_b = object.takerPointsMultiplier) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTradingRewardCampaignBoostInfo() {
    return {
        boostedSpotMarketIds: [],
        spotMarketMultipliers: [],
        boostedDerivativeMarketIds: [],
        derivativeMarketMultipliers: [],
    };
}
exports.TradingRewardCampaignBoostInfo = {
    encode: function (message, writer) {
        var e_3, _a, e_4, _b, e_5, _c, e_6, _d;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _e = __values(message.boostedSpotMarketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _g = __values(message.spotMarketMultipliers), _h = _g.next(); !_h.done; _h = _g.next()) {
                var v = _h.value;
                exports.PointsMultiplier.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _j = __values(message.boostedDerivativeMarketIds), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _l = __values(message.derivativeMarketMultipliers), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                exports.PointsMultiplier.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignBoostInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.boostedSpotMarketIds.push(reader.string());
                    break;
                case 2:
                    message.spotMarketMultipliers.push(exports.PointsMultiplier.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.boostedDerivativeMarketIds.push(reader.string());
                    break;
                case 4:
                    message.derivativeMarketMultipliers.push(exports.PointsMultiplier.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            boostedSpotMarketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.boostedSpotMarketIds)
                ? object.boostedSpotMarketIds.map(function (e) { return String(e); })
                : [],
            spotMarketMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarketMultipliers)
                ? object.spotMarketMultipliers.map(function (e) { return exports.PointsMultiplier.fromJSON(e); })
                : [],
            boostedDerivativeMarketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.boostedDerivativeMarketIds)
                ? object.boostedDerivativeMarketIds.map(function (e) { return String(e); })
                : [],
            derivativeMarketMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarketMultipliers)
                ? object.derivativeMarketMultipliers.map(function (e) { return exports.PointsMultiplier.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.boostedSpotMarketIds) {
            obj.boostedSpotMarketIds = message.boostedSpotMarketIds.map(function (e) { return e; });
        }
        else {
            obj.boostedSpotMarketIds = [];
        }
        if (message.spotMarketMultipliers) {
            obj.spotMarketMultipliers = message.spotMarketMultipliers.map(function (e) { return e ? exports.PointsMultiplier.toJSON(e) : undefined; });
        }
        else {
            obj.spotMarketMultipliers = [];
        }
        if (message.boostedDerivativeMarketIds) {
            obj.boostedDerivativeMarketIds = message.boostedDerivativeMarketIds.map(function (e) { return e; });
        }
        else {
            obj.boostedDerivativeMarketIds = [];
        }
        if (message.derivativeMarketMultipliers) {
            obj.derivativeMarketMultipliers = message.derivativeMarketMultipliers.map(function (e) {
                return e ? exports.PointsMultiplier.toJSON(e) : undefined;
            });
        }
        else {
            obj.derivativeMarketMultipliers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignBoostInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTradingRewardCampaignBoostInfo();
        message.boostedSpotMarketIds = ((_a = object.boostedSpotMarketIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.spotMarketMultipliers = ((_b = object.spotMarketMultipliers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.PointsMultiplier.fromPartial(e); })) || [];
        message.boostedDerivativeMarketIds = ((_c = object.boostedDerivativeMarketIds) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.derivativeMarketMultipliers =
            ((_d = object.derivativeMarketMultipliers) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.PointsMultiplier.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseCampaignRewardPool() {
    return { startTimestamp: "0", maxCampaignRewards: [] };
}
exports.CampaignRewardPool = {
    encode: function (message, writer) {
        var e_7, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.startTimestamp !== "0") {
            writer.uint32(8).int64(message.startTimestamp);
        }
        try {
            for (var _b = __values(message.maxCampaignRewards), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseCampaignRewardPool();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.startTimestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.maxCampaignRewards.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            startTimestamp: isSet(object.startTimestamp) ? String(object.startTimestamp) : "0",
            maxCampaignRewards: Array.isArray(object === null || object === void 0 ? void 0 : object.maxCampaignRewards)
                ? object.maxCampaignRewards.map(function (e) { return coin_1.Coin.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.startTimestamp !== undefined && (obj.startTimestamp = message.startTimestamp);
        if (message.maxCampaignRewards) {
            obj.maxCampaignRewards = message.maxCampaignRewards.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.maxCampaignRewards = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.CampaignRewardPool.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseCampaignRewardPool();
        message.startTimestamp = (_a = object.startTimestamp) !== null && _a !== void 0 ? _a : "0";
        message.maxCampaignRewards = ((_b = object.maxCampaignRewards) === null || _b === void 0 ? void 0 : _b.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseTradingRewardCampaignInfo() {
    return {
        campaignDurationSeconds: "0",
        quoteDenoms: [],
        tradingRewardBoostInfo: undefined,
        disqualifiedMarketIds: [],
    };
}
exports.TradingRewardCampaignInfo = {
    encode: function (message, writer) {
        var e_8, _a, e_9, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.campaignDurationSeconds !== "0") {
            writer.uint32(8).int64(message.campaignDurationSeconds);
        }
        try {
            for (var _c = __values(message.quoteDenoms), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_8) throw e_8.error; }
        }
        if (message.tradingRewardBoostInfo !== undefined) {
            exports.TradingRewardCampaignBoostInfo.encode(message.tradingRewardBoostInfo, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _e = __values(message.disqualifiedMarketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.campaignDurationSeconds = longToString(reader.int64());
                    break;
                case 2:
                    message.quoteDenoms.push(reader.string());
                    break;
                case 3:
                    message.tradingRewardBoostInfo = exports.TradingRewardCampaignBoostInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.disqualifiedMarketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            campaignDurationSeconds: isSet(object.campaignDurationSeconds) ? String(object.campaignDurationSeconds) : "0",
            quoteDenoms: Array.isArray(object === null || object === void 0 ? void 0 : object.quoteDenoms) ? object.quoteDenoms.map(function (e) { return String(e); }) : [],
            tradingRewardBoostInfo: isSet(object.tradingRewardBoostInfo)
                ? exports.TradingRewardCampaignBoostInfo.fromJSON(object.tradingRewardBoostInfo)
                : undefined,
            disqualifiedMarketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.disqualifiedMarketIds)
                ? object.disqualifiedMarketIds.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.campaignDurationSeconds !== undefined && (obj.campaignDurationSeconds = message.campaignDurationSeconds);
        if (message.quoteDenoms) {
            obj.quoteDenoms = message.quoteDenoms.map(function (e) { return e; });
        }
        else {
            obj.quoteDenoms = [];
        }
        message.tradingRewardBoostInfo !== undefined && (obj.tradingRewardBoostInfo = message.tradingRewardBoostInfo
            ? exports.TradingRewardCampaignBoostInfo.toJSON(message.tradingRewardBoostInfo)
            : undefined);
        if (message.disqualifiedMarketIds) {
            obj.disqualifiedMarketIds = message.disqualifiedMarketIds.map(function (e) { return e; });
        }
        else {
            obj.disqualifiedMarketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTradingRewardCampaignInfo();
        message.campaignDurationSeconds = (_a = object.campaignDurationSeconds) !== null && _a !== void 0 ? _a : "0";
        message.quoteDenoms = ((_b = object.quoteDenoms) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.tradingRewardBoostInfo =
            (object.tradingRewardBoostInfo !== undefined && object.tradingRewardBoostInfo !== null)
                ? exports.TradingRewardCampaignBoostInfo.fromPartial(object.tradingRewardBoostInfo)
                : undefined;
        message.disqualifiedMarketIds = ((_c = object.disqualifiedMarketIds) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseFeeDiscountTierInfo() {
    return { makerDiscountRate: "", takerDiscountRate: "", stakedAmount: "", volume: "" };
}
exports.FeeDiscountTierInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.makerDiscountRate !== "") {
            writer.uint32(10).string(message.makerDiscountRate);
        }
        if (message.takerDiscountRate !== "") {
            writer.uint32(18).string(message.takerDiscountRate);
        }
        if (message.stakedAmount !== "") {
            writer.uint32(26).string(message.stakedAmount);
        }
        if (message.volume !== "") {
            writer.uint32(34).string(message.volume);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountTierInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.makerDiscountRate = reader.string();
                    break;
                case 2:
                    message.takerDiscountRate = reader.string();
                    break;
                case 3:
                    message.stakedAmount = reader.string();
                    break;
                case 4:
                    message.volume = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            makerDiscountRate: isSet(object.makerDiscountRate) ? String(object.makerDiscountRate) : "",
            takerDiscountRate: isSet(object.takerDiscountRate) ? String(object.takerDiscountRate) : "",
            stakedAmount: isSet(object.stakedAmount) ? String(object.stakedAmount) : "",
            volume: isSet(object.volume) ? String(object.volume) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.makerDiscountRate !== undefined && (obj.makerDiscountRate = message.makerDiscountRate);
        message.takerDiscountRate !== undefined && (obj.takerDiscountRate = message.takerDiscountRate);
        message.stakedAmount !== undefined && (obj.stakedAmount = message.stakedAmount);
        message.volume !== undefined && (obj.volume = message.volume);
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountTierInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseFeeDiscountTierInfo();
        message.makerDiscountRate = (_a = object.makerDiscountRate) !== null && _a !== void 0 ? _a : "";
        message.takerDiscountRate = (_b = object.takerDiscountRate) !== null && _b !== void 0 ? _b : "";
        message.stakedAmount = (_c = object.stakedAmount) !== null && _c !== void 0 ? _c : "";
        message.volume = (_d = object.volume) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseFeeDiscountSchedule() {
    return { bucketCount: "0", bucketDuration: "0", quoteDenoms: [], tierInfos: [], disqualifiedMarketIds: [] };
}
exports.FeeDiscountSchedule = {
    encode: function (message, writer) {
        var e_10, _a, e_11, _b, e_12, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bucketCount !== "0") {
            writer.uint32(8).uint64(message.bucketCount);
        }
        if (message.bucketDuration !== "0") {
            writer.uint32(16).int64(message.bucketDuration);
        }
        try {
            for (var _d = __values(message.quoteDenoms), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_10) throw e_10.error; }
        }
        try {
            for (var _f = __values(message.tierInfos), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exports.FeeDiscountTierInfo.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_11) throw e_11.error; }
        }
        try {
            for (var _h = __values(message.disqualifiedMarketIds), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(42).string(v);
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountSchedule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bucketCount = longToString(reader.uint64());
                    break;
                case 2:
                    message.bucketDuration = longToString(reader.int64());
                    break;
                case 3:
                    message.quoteDenoms.push(reader.string());
                    break;
                case 4:
                    message.tierInfos.push(exports.FeeDiscountTierInfo.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.disqualifiedMarketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bucketCount: isSet(object.bucketCount) ? String(object.bucketCount) : "0",
            bucketDuration: isSet(object.bucketDuration) ? String(object.bucketDuration) : "0",
            quoteDenoms: Array.isArray(object === null || object === void 0 ? void 0 : object.quoteDenoms) ? object.quoteDenoms.map(function (e) { return String(e); }) : [],
            tierInfos: Array.isArray(object === null || object === void 0 ? void 0 : object.tierInfos)
                ? object.tierInfos.map(function (e) { return exports.FeeDiscountTierInfo.fromJSON(e); })
                : [],
            disqualifiedMarketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.disqualifiedMarketIds)
                ? object.disqualifiedMarketIds.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bucketCount !== undefined && (obj.bucketCount = message.bucketCount);
        message.bucketDuration !== undefined && (obj.bucketDuration = message.bucketDuration);
        if (message.quoteDenoms) {
            obj.quoteDenoms = message.quoteDenoms.map(function (e) { return e; });
        }
        else {
            obj.quoteDenoms = [];
        }
        if (message.tierInfos) {
            obj.tierInfos = message.tierInfos.map(function (e) { return e ? exports.FeeDiscountTierInfo.toJSON(e) : undefined; });
        }
        else {
            obj.tierInfos = [];
        }
        if (message.disqualifiedMarketIds) {
            obj.disqualifiedMarketIds = message.disqualifiedMarketIds.map(function (e) { return e; });
        }
        else {
            obj.disqualifiedMarketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountSchedule.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseFeeDiscountSchedule();
        message.bucketCount = (_a = object.bucketCount) !== null && _a !== void 0 ? _a : "0";
        message.bucketDuration = (_b = object.bucketDuration) !== null && _b !== void 0 ? _b : "0";
        message.quoteDenoms = ((_c = object.quoteDenoms) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.tierInfos = ((_d = object.tierInfos) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.FeeDiscountTierInfo.fromPartial(e); })) || [];
        message.disqualifiedMarketIds = ((_e = object.disqualifiedMarketIds) === null || _e === void 0 ? void 0 : _e.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseFeeDiscountTierTTL() {
    return { tier: "0", ttlTimestamp: "0" };
}
exports.FeeDiscountTierTTL = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tier !== "0") {
            writer.uint32(8).uint64(message.tier);
        }
        if (message.ttlTimestamp !== "0") {
            writer.uint32(16).int64(message.ttlTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountTierTTL();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tier = longToString(reader.uint64());
                    break;
                case 2:
                    message.ttlTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tier: isSet(object.tier) ? String(object.tier) : "0",
            ttlTimestamp: isSet(object.ttlTimestamp) ? String(object.ttlTimestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tier !== undefined && (obj.tier = message.tier);
        message.ttlTimestamp !== undefined && (obj.ttlTimestamp = message.ttlTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountTierTTL.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeeDiscountTierTTL();
        message.tier = (_a = object.tier) !== null && _a !== void 0 ? _a : "0";
        message.ttlTimestamp = (_b = object.ttlTimestamp) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseVolumeRecord() {
    return { makerVolume: "", takerVolume: "" };
}
exports.VolumeRecord = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.makerVolume !== "") {
            writer.uint32(10).string(message.makerVolume);
        }
        if (message.takerVolume !== "") {
            writer.uint32(18).string(message.takerVolume);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseVolumeRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.makerVolume = reader.string();
                    break;
                case 2:
                    message.takerVolume = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            makerVolume: isSet(object.makerVolume) ? String(object.makerVolume) : "",
            takerVolume: isSet(object.takerVolume) ? String(object.takerVolume) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.makerVolume !== undefined && (obj.makerVolume = message.makerVolume);
        message.takerVolume !== undefined && (obj.takerVolume = message.takerVolume);
        return obj;
    },
    create: function (base) {
        return exports.VolumeRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseVolumeRecord();
        message.makerVolume = (_a = object.makerVolume) !== null && _a !== void 0 ? _a : "";
        message.takerVolume = (_b = object.takerVolume) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseAccountRewards() {
    return { account: "", rewards: [] };
}
exports.AccountRewards = {
    encode: function (message, writer) {
        var e_13, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        try {
            for (var _b = __values(message.rewards), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAccountRewards();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.rewards.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            rewards: Array.isArray(object === null || object === void 0 ? void 0 : object.rewards) ? object.rewards.map(function (e) { return coin_1.Coin.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        if (message.rewards) {
            obj.rewards = message.rewards.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.rewards = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.AccountRewards.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAccountRewards();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.rewards = ((_b = object.rewards) === null || _b === void 0 ? void 0 : _b.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseTradeRecords() {
    return { marketId: "", latestTradeRecords: [] };
}
exports.TradeRecords = {
    encode: function (message, writer) {
        var e_14, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _b = __values(message.latestTradeRecords), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TradeRecord.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradeRecords();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.latestTradeRecords.push(exports.TradeRecord.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            latestTradeRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.latestTradeRecords)
                ? object.latestTradeRecords.map(function (e) { return exports.TradeRecord.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.latestTradeRecords) {
            obj.latestTradeRecords = message.latestTradeRecords.map(function (e) { return e ? exports.TradeRecord.toJSON(e) : undefined; });
        }
        else {
            obj.latestTradeRecords = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradeRecords.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTradeRecords();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.latestTradeRecords = ((_b = object.latestTradeRecords) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.TradeRecord.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSubaccountIDs() {
    return { subaccountIds: [] };
}
exports.SubaccountIDs = {
    encode: function (message, writer) {
        var e_15, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.subaccountIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_15) throw e_15.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountIDs();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountIds.push(reader.bytes());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountIds: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountIds)
                ? object.subaccountIds.map(function (e) { return bytesFromBase64(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.subaccountIds) {
            obj.subaccountIds = message.subaccountIds.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.subaccountIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SubaccountIDs.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountIDs();
        message.subaccountIds = ((_a = object.subaccountIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseTradeRecord() {
    return { timestamp: "0", price: "", quantity: "" };
}
exports.TradeRecord = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.timestamp !== "0") {
            writer.uint32(8).int64(message.timestamp);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(26).string(message.quantity);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradeRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.timestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.quantity = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        return obj;
    },
    create: function (base) {
        return exports.TradeRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTradeRecord();
        message.timestamp = (_a = object.timestamp) !== null && _a !== void 0 ? _a : "0";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.quantity = (_c = object.quantity) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseLevel() {
    return { p: "", q: "" };
}
exports.Level = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.p !== "") {
            writer.uint32(10).string(message.p);
        }
        if (message.q !== "") {
            writer.uint32(18).string(message.q);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseLevel();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.p = reader.string();
                    break;
                case 2:
                    message.q = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { p: isSet(object.p) ? String(object.p) : "", q: isSet(object.q) ? String(object.q) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.p !== undefined && (obj.p = message.p);
        message.q !== undefined && (obj.q = message.q);
        return obj;
    },
    create: function (base) {
        return exports.Level.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseLevel();
        message.p = (_a = object.p) !== null && _a !== void 0 ? _a : "";
        message.q = (_b = object.q) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseAggregateSubaccountVolumeRecord() {
    return { subaccountId: "", marketVolumes: [] };
}
exports.AggregateSubaccountVolumeRecord = {
    encode: function (message, writer) {
        var e_16, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.marketVolumes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.MarketVolume.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAggregateSubaccountVolumeRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketVolumes.push(exports.MarketVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.marketVolumes)
                ? object.marketVolumes.map(function (e) { return exports.MarketVolume.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.marketVolumes) {
            obj.marketVolumes = message.marketVolumes.map(function (e) { return e ? exports.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.marketVolumes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.AggregateSubaccountVolumeRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAggregateSubaccountVolumeRecord();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketVolumes = ((_b = object.marketVolumes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.MarketVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseAggregateAccountVolumeRecord() {
    return { account: "", marketVolumes: [] };
}
exports.AggregateAccountVolumeRecord = {
    encode: function (message, writer) {
        var e_17, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        try {
            for (var _b = __values(message.marketVolumes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.MarketVolume.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_17) throw e_17.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAggregateAccountVolumeRecord();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.marketVolumes.push(exports.MarketVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            marketVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.marketVolumes)
                ? object.marketVolumes.map(function (e) { return exports.MarketVolume.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        if (message.marketVolumes) {
            obj.marketVolumes = message.marketVolumes.map(function (e) { return e ? exports.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.marketVolumes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.AggregateAccountVolumeRecord.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAggregateAccountVolumeRecord();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.marketVolumes = ((_b = object.marketVolumes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.MarketVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMarketVolume() {
    return { marketId: "", volume: undefined };
}
exports.MarketVolume = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.volume !== undefined) {
            exports.VolumeRecord.encode(message.volume, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMarketVolume();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.volume = exports.VolumeRecord.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            volume: isSet(object.volume) ? exports.VolumeRecord.fromJSON(object.volume) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.volume !== undefined && (obj.volume = message.volume ? exports.VolumeRecord.toJSON(message.volume) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.MarketVolume.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseMarketVolume();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.volume = (object.volume !== undefined && object.volume !== null)
            ? exports.VolumeRecord.fromPartial(object.volume)
            : undefined;
        return message;
    },
};
function createBaseDenomDecimals() {
    return { denom: "", decimals: "0" };
}
exports.DenomDecimals = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.decimals !== "0") {
            writer.uint32(16).uint64(message.decimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDenomDecimals();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.decimals = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            decimals: isSet(object.decimals) ? String(object.decimals) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.decimals !== undefined && (obj.decimals = message.decimals);
        return obj;
    },
    create: function (base) {
        return exports.DenomDecimals.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDenomDecimals();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.decimals = (_b = object.decimals) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseGrantAuthorization() {
    return { grantee: "", amount: "" };
}
exports.GrantAuthorization = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grantee !== "") {
            writer.uint32(10).string(message.grantee);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGrantAuthorization();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grantee = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            grantee: isSet(object.grantee) ? String(object.grantee) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.grantee !== undefined && (obj.grantee = message.grantee);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.GrantAuthorization.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseGrantAuthorization();
        message.grantee = (_a = object.grantee) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseActiveGrant() {
    return { granter: "", amount: "" };
}
exports.ActiveGrant = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseActiveGrant();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            granter: isSet(object.granter) ? String(object.granter) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.ActiveGrant.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseActiveGrant();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseEffectiveGrant() {
    return { granter: "", netGrantedStake: "", isValid: false };
}
exports.EffectiveGrant = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        if (message.netGrantedStake !== "") {
            writer.uint32(18).string(message.netGrantedStake);
        }
        if (message.isValid === true) {
            writer.uint32(24).bool(message.isValid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEffectiveGrant();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                case 2:
                    message.netGrantedStake = reader.string();
                    break;
                case 3:
                    message.isValid = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            granter: isSet(object.granter) ? String(object.granter) : "",
            netGrantedStake: isSet(object.netGrantedStake) ? String(object.netGrantedStake) : "",
            isValid: isSet(object.isValid) ? Boolean(object.isValid) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        message.netGrantedStake !== undefined && (obj.netGrantedStake = message.netGrantedStake);
        message.isValid !== undefined && (obj.isValid = message.isValid);
        return obj;
    },
    create: function (base) {
        return exports.EffectiveGrant.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEffectiveGrant();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        message.netGrantedStake = (_b = object.netGrantedStake) !== null && _b !== void 0 ? _b : "";
        message.isValid = (_c = object.isValid) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseDenomMinNotional() {
    return { denom: "", minNotional: "" };
}
exports.DenomMinNotional = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.minNotional !== "") {
            writer.uint32(18).string(message.minNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDenomMinNotional();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.minNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        return obj;
    },
    create: function (base) {
        return exports.DenomMinNotional.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDenomMinNotional();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.minNotional = (_b = object.minNotional) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
