{"version": 3, "sources": ["../../src/utils/const.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * Types of API endpoints used for routing requests in the Aptos network.\n * @group Implementation\n * @category Utils\n */\nexport enum AptosApiType {\n  FULLNODE = \"Fullnode\",\n  INDEXER = \"Indexer\",\n  FAUCET = \"Faucet\",\n  PEPPER = \"Pepper\",\n  PROVER = \"Prover\",\n}\n\n/**\n * The default max gas amount when none is given.\n *\n * This is the maximum number of gas units that will be used by a transaction before being rejected.\n *\n * Note that max gas amount varies based on the transaction.  A larger transaction will go over this\n * default gas amount, and the value will need to be changed for the specific transaction.\n * @group Implementation\n * @category Utils\n */\nexport const DEFAULT_MAX_GAS_AMOUNT = 200000;\n\n/**\n * The default transaction expiration seconds from now.\n *\n * This time is how long until the blockchain nodes will reject the transaction.\n *\n * Note that the transaction expiration time varies based on network connection and network load.  It may need to be\n * increased for the transaction to be processed.\n * @group Implementation\n * @category Utils\n */\nexport const DEFAULT_TXN_EXP_SEC_FROM_NOW = 20;\n\n/**\n * The default number of seconds to wait for a transaction to be processed.\n *\n * This time is the amount of time that the SDK will wait for a transaction to be processed when waiting for\n * the results of the transaction.  It may take longer based on network connection and network load.\n * @group Implementation\n * @category Utils\n */\nexport const DEFAULT_TXN_TIMEOUT_SEC = 20;\n\n/**\n * The default gas currency for the network.\n * @group Implementation\n * @category Utils\n */\nexport const APTOS_COIN = \"0x1::aptos_coin::AptosCoin\";\n/**\n * @group Implementation\n * @category Utils\n */\nexport const APTOS_FA = \"0x000000000000000000000000000000000000000000000000000000000000000a\";\n/**\n * @group Implementation\n * @category Utils\n */\nexport const RAW_TRANSACTION_SALT = \"APTOS::RawTransaction\";\n/**\n * @group Implementation\n * @category Utils\n */\nexport const RAW_TRANSACTION_WITH_DATA_SALT = \"APTOS::RawTransactionWithData\";\n\n/**\n * Supported processor types for the indexer API, sourced from the processor_status table in the indexer database.\n * {@link https://cloud.hasura.io/public/graphiql?endpoint=https://api.mainnet.aptoslabs.com/v1/graphql}\n * @group Implementation\n * @category Utils\n */\nexport enum ProcessorType {\n  ACCOUNT_TRANSACTION_PROCESSOR = \"account_transactions_processor\",\n  DEFAULT = \"default_processor\",\n  EVENTS_PROCESSOR = \"events_processor\",\n  // Fungible asset processor also handles coins\n  FUNGIBLE_ASSET_PROCESSOR = \"fungible_asset_processor\",\n  STAKE_PROCESSOR = \"stake_processor\",\n  // Token V2 processor replaces Token processor (not only for digital assets)\n  TOKEN_V2_PROCESSOR = \"token_v2_processor\",\n  USER_TRANSACTION_PROCESSOR = \"user_transaction_processor\",\n  OBJECT_PROCESSOR = \"objects_processor\",\n}\n\n/**\n * Regular expression pattern for Firebase Auth issuer URLs\n * Matches URLs in the format: https://securetoken.google.com/[project-id]\n * where project-id can contain letters, numbers, hyphens, and underscores\n */\nexport const FIREBASE_AUTH_ISS_PATTERN = /^https:\\/\\/securetoken\\.google\\.com\\/[a-zA-Z0-9-_]+$/;\n"], "mappings": "AAQO,IAAKA,OACVA,EAAA,SAAW,WACXA,EAAA,QAAU,UACVA,EAAA,OAAS,SACTA,EAAA,OAAS,SACTA,EAAA,OAAS,SALCA,OAAA,IAkBCC,EAAyB,IAYzBC,EAA+B,GAU/BC,EAA0B,GAO1BC,EAAa,6BAKbC,EAAW,qEAKXC,EAAuB,wBAKvBC,EAAiC,gCAQlCC,OACVA,EAAA,8BAAgC,iCAChCA,EAAA,QAAU,oBACVA,EAAA,iBAAmB,mBAEnBA,EAAA,yBAA2B,2BAC3BA,EAAA,gBAAkB,kBAElBA,EAAA,mBAAqB,qBACrBA,EAAA,2BAA6B,6BAC7BA,EAAA,iBAAmB,oBAVTA,OAAA,IAkBCC,EAA4B", "names": ["AptosApiType", "DEFAULT_MAX_GAS_AMOUNT", "DEFAULT_TXN_EXP_SEC_FROM_NOW", "DEFAULT_TXN_TIMEOUT_SEC", "APTOS_COIN", "APTOS_FA", "RAW_TRANSACTION_SALT", "RAW_TRANSACTION_WITH_DATA_SALT", "ProcessorType", "FIREBASE_AUTH_ISS_PATTERN"]}