{"version": 3, "sources": ["../../src/bcs/serializable/moveStructs.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Bool, U128, U16, U256, U32, U64, U8 } from \"./movePrimitives\";\nimport { Serializable, Serializer } from \"../serializer\";\nimport { Deserializable, Deserializer } from \"../deserializer\";\nimport { AnyNumber, HexInput, ScriptTransactionArgumentVariants } from \"../../types\";\nimport { Hex } from \"../../core/hex\";\nimport { EntryFunctionArgument, TransactionArgument } from \"../../transactions/instances/transactionArgument\";\n\n/**\n * This class is the Aptos Typescript SDK representation of a Move `vector<T>`,\n * where `T` represents either a primitive type (`bool`, `u8`, `u64`, ...)\n * or a BCS-serializable struct itself.\n *\n * It is a BCS-serializable, array-like type that contains an array of values of type `T`,\n * where `T` is a class that implements `Serializable`.\n *\n * The purpose of this class is to facilitate easy construction of BCS-serializable\n * Move `vector<T>` types.\n *\n * @example\n * // in Move: `vector<u8> [1, 2, 3, 4];`\n * const vecOfU8s = new MoveVector<U8>([new U8(1), new U8(2), new U8(3), new U8(4)]);\n * // in Move: `std::bcs::to_bytes(vector<u8> [1, 2, 3, 4]);`\n * const bcsBytes = vecOfU8s.toUint8Array();\n *\n * // vector<vector<u8>> [ vector<u8> [1], vector<u8> [1, 2, 3, 4], vector<u8> [5, 6, 7, 8] ];\n * const vecOfVecs = new MoveVector<MoveVector<U8>>([\n *   new MoveVector<U8>([new U8(1)]),\n *   MoveVector.U8([1, 2, 3, 4]),\n *   MoveVector.U8([5, 6, 7, 8]),\n * ]);\n *\n * // vector<Option<u8>> [ std::option::some<u8>(1), std::option::some<u8>(2) ];\n * const vecOfOptionU8s = new MoveVector<MoveOption<U8>>([\n *    MoveOption.U8(1),\n *    MoveOption.U8(2),\n * ]);\n *\n * // vector<MoveString> [ std::string::utf8(b\"hello\"), std::string::utf8(b\"world\") ];\n * const vecOfStrings = new MoveVector([new MoveString(\"hello\"), new MoveString(\"world\")]);\n * const vecOfStrings2 = MoveVector.MoveString([\"hello\", \"world\"]);\n *\n * @param values an Array<T> of values where T is a class that implements Serializable\n * @returns a `MoveVector<T>` with the values `values`\n * @group Implementation\n * @category BCS\n */\nexport class MoveVector<T extends Serializable & EntryFunctionArgument>\n  extends Serializable\n  implements TransactionArgument\n{\n  public values: Array<T>;\n\n  /**\n   * Initializes a new instance of the class with an optional value.\n   * This constructor sets up the internal vector based on the provided value.\n   *\n   * @param values - The initial value to be stored in the vector, or null to initialize an empty vector.\n   * @group Implementation\n   * @category BCS\n   */\n  constructor(values: Array<T>) {\n    super();\n    this.values = values;\n  }\n\n  /**\n   * Serializes the current instance into a byte sequence suitable for entry functions.\n   * This allows the data to be properly formatted for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to serialize the byte sequence.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * NOTE: This function will only work when the inner values in the `MoveVector` are `U8`s.\n   * @param serializer\n   * @group Implementation\n   * @category BCS\n   */\n\n  /**\n   * Serialize the string as a fixed byte string without the length prefix for use in a script function.\n   * @param serializer - The serializer used to convert the byte vector into a format suitable for a script function.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForScriptFunction(serializer: Serializer): void {\n    // This checks if the type of a non-empty vector is of type other than U8.  If so, we use the Serialized\n    // transaction argument type to serialize the argument.\n    if (this.values[0] !== undefined && !(this.values[0] instanceof U8)) {\n      const serialized = new Serialized(this.bcsToBytes());\n      serialized.serializeForScriptFunction(serializer);\n      return;\n    }\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U8Vector);\n    serializer.serialize(this);\n  }\n\n  /**\n   * Factory method to generate a MoveVector<U8> from a `number` or `undefined`.\n   *\n   * This method allows you to create a MoveVector that encapsulates a U8 value, enabling you to handle optional U8 values\n   * effectively.\n   *\n   * @param values - The values used to fill the MoveVector. If `values` is undefined or null, the resulting MoveVector's\n   * `.isSome()` method will return false.\n   * @returns A MoveVector<U8> with an inner value `value`.\n   *\n   * @example\n   * ```typescript\n   * const v = MoveVector.U8([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  static U8(values: Array<number> | HexInput): MoveVector<U8> {\n    let numbers: Array<number>;\n\n    if (Array.isArray(values) && values.length === 0) {\n      // Handle empty array, since it won't have a \"first value\"\n      numbers = [];\n    } else if (Array.isArray(values) && typeof values[0] === \"number\") {\n      numbers = values;\n    } else if (typeof values === \"string\") {\n      const hex = Hex.fromHexInput(values);\n      numbers = Array.from(hex.toUint8Array());\n    } else if (values instanceof Uint8Array) {\n      numbers = Array.from(values);\n    } else {\n      throw new Error(\"Invalid input type, must be an number[], Uint8Array, or hex string\");\n    }\n\n    return new MoveVector<U8>(numbers.map((v) => new U8(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U16> from a `number` or `null`.\n   *\n   * This method allows you to create a MoveVector that can either hold a U16 value or be empty.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is null or undefined, the resulting MoveVector's\n   * `.isSome()` method will return false.\n   * @returns A MoveVector<U16> with an inner value `value`.\n   * @example\n   * ```typescript\n   * const v = MoveVector.U16([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n\n   */\n  static U16(values: Array<number>): MoveVector<U16> {\n    return new MoveVector<U16>(values.map((v) => new U16(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<U32> from a `number` or `null`.\n   *\n   * This method allows you to create a MoveVector that can either hold a U32 value or be empty.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is null or undefined,\n   * the resulting MoveVector's .isSome() method will return false.\n   * @returns A MoveVector<U32> with an inner value `value`.\n   *\n   * @example\n   * ```\n   * const v = MoveVector.U32([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n\n   */\n  static U32(values: Array<number>): MoveVector<U32> {\n    return new MoveVector<U32>(values.map((v) => new U32(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<U64> from a number, bigint, or null/undefined.\n   * This allows for the creation of an optional U64 value that can be checked for presence.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is undefined or null, the resulting MoveVector's\n   * `.isSome()` method will return false.\n   * @returns A MoveVector<U64> with an inner value `value`.\n   *\n   * @example\n   * ```typescript\n   * const v = MoveVector.U64([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  static U64(values: Array<AnyNumber>): MoveVector<U64> {\n    return new MoveVector<U64>(values.map((v) => new U64(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<U128> from a number, bigint, or undefined.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is undefined, the resulting MoveVector's `.isSome()`\n   * method will return false.\n   * @returns A MoveVector<U128> with an inner value `value`.\n   *\n   * @example\n   * ```typescript\n   * const v = MoveVector.U128([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  static U128(values: Array<AnyNumber>): MoveVector<U128> {\n    return new MoveVector<U128>(values.map((v) => new U128(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<U256> from a number, bigint, or null/undefined.\n   * This allows for the creation of an optional U256 value, enabling checks for presence or absence of a value.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is undefined or null,\n   *                the resulting MoveVector's .isSome() method will return false.\n   * @returns A MoveVector<U256> with an inner value `value`.\n   *\n   * @example\n   * ```typescript\n   * const v = MoveVector.U256([1, 2, 3, 4]);\n   * ```\n   * @group Implementation\n   * @category BCS\n   */\n  static U256(values: Array<AnyNumber>): MoveVector<U256> {\n    return new MoveVector<U256>(values.map((v) => new U256(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<Bool> from a `boolean` or `undefined`.\n   * This method allows you to create an optional boolean value that can be used in various contexts where a boolean may or may\n   * not be present.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is undefined, the resulting MoveVector's .isSome() method\n   * will return false.\n   * @returns A MoveVector<Bool> with an inner value `value`.\n   *\n   * @example\n   *    * const v = MoveVector.Bool([true, false, true, false]);\n   * @group Implementation\n   * @category BCS\n   */\n  static Bool(values: Array<boolean>): MoveVector<Bool> {\n    return new MoveVector<Bool>(values.map((v) => new Bool(v)));\n  }\n\n  /**\n   * Factory method to generate a MoveVector<MoveString> from a `string` or `undefined`.\n   * This function creates a MoveVector that encapsulates a MoveString if the provided value is not null or undefined.\n   *\n   * @param values - The value used to fill the MoveVector. If `value` is undefined, the resulting MoveVector's .isSome() method\n   * will return false.\n   * @returns A MoveVector<MoveString> with an inner value `value`.\n   *\n   * @example\n   * const v = MoveVector.MoveString([\"hello\", \"world\"]);\n   * @group Implementation\n   * @category BCS\n   */\n  static MoveString(values: Array<string>): MoveVector<MoveString> {\n    return new MoveVector<MoveString>(values.map((v) => new MoveString(v)));\n  }\n\n  /**\n   * Serializes the current object using the provided serializer.\n   * This function will serialize the value if it is present.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serialize(serializer: Serializer): void;\n  serialize(serializer: Serializer): void {\n    serializer.serializeVector(this.values);\n  }\n\n  /**\n   * Deserialize a MoveVector of type T, specifically where T is a Serializable and Deserializable type.\n   *\n   * NOTE: This only works with a depth of one. Generics will not work.\n   *\n   * NOTE: This will not work with types that aren't of the Serializable class.\n   *\n   * If you're looking for a more flexible deserialization function, you can use the deserializeVector function\n   * in the Deserializer class.\n   *\n   * @example\n   * const vec = MoveVector.deserialize(deserializer, U64);\n   * @param deserializer the Deserializer instance to use, with bytes loaded into it already.\n   * @param cls the class to typecast the input values to, must be a Serializable and Deserializable type.\n   * @returns a MoveVector of the corresponding class T\n   *\n   * @group Implementation\n   * @category BCS\n   */\n  static deserialize<T extends Serializable & EntryFunctionArgument>(\n    deserializer: Deserializer,\n    cls: Deserializable<T>,\n  ): MoveVector<T> {\n    const length = deserializer.deserializeUleb128AsU32();\n    const values = new Array<T>();\n    for (let i = 0; i < length; i += 1) {\n      values.push(cls.deserialize(deserializer));\n    }\n    return new MoveVector(values);\n  }\n}\n\n/**\n * Represents a serialized data structure that encapsulates a byte array.\n * This class extends the Serializable class and provides methods for serialization\n * and deserialization of byte data, as well as converting to a MoveVector.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class Serialized extends Serializable implements TransactionArgument {\n  public readonly value: Uint8Array;\n\n  constructor(value: HexInput) {\n    super();\n    this.value = Hex.fromHexInput(value).toUint8Array();\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeBytes(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    this.serialize(serializer);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.Serialized);\n    this.serialize(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): Serialized {\n    return new Serialized(deserializer.deserializeBytes());\n  }\n\n  /**\n   * Deserialize the bytecode into a MoveVector of the specified type.\n   * This function allows you to convert serialized data into a usable MoveVector format.\n   *\n   * @param cls - The class type of the elements in the MoveVector.\n   * @group Implementation\n   * @category BCS\n   */\n  toMoveVector<T extends Serializable & EntryFunctionArgument>(cls: Deserializable<T>): MoveVector<T> {\n    const deserializer = new Deserializer(this.bcsToBytes());\n    deserializer.deserializeUleb128AsU32();\n    const vec = deserializer.deserializeVector(cls);\n    return new MoveVector(vec);\n  }\n}\n\n/**\n * Represents a string value that can be serialized and deserialized.\n * This class extends the Serializable base class and provides methods\n * for serializing the string in different contexts, such as for entry\n * functions and script functions.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class MoveString extends Serializable implements TransactionArgument {\n  public value: string;\n\n  constructor(value: string) {\n    super();\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeStr(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    // Serialize the string as a fixed byte string, i.e., without the length prefix\n    const textEncoder = new TextEncoder();\n    const fixedStringBytes = textEncoder.encode(this.value);\n    // Put those bytes into a vector<u8> and serialize it as a script function argument\n    const vectorU8 = MoveVector.U8(fixedStringBytes);\n    vectorU8.serializeForScriptFunction(serializer);\n  }\n\n  static deserialize(deserializer: Deserializer): MoveString {\n    return new MoveString(deserializer.deserializeStr());\n  }\n}\n\nexport class MoveOption<T extends Serializable & EntryFunctionArgument>\n  extends Serializable\n  implements EntryFunctionArgument\n{\n  private vec: MoveVector<T>;\n\n  public readonly value?: T;\n\n  constructor(value?: T | null) {\n    super();\n    if (typeof value !== \"undefined\" && value !== null) {\n      this.vec = new MoveVector([value]);\n    } else {\n      this.vec = new MoveVector([]);\n    }\n\n    [this.value] = this.vec.values;\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * Retrieves the inner value of the MoveOption.\n   *\n   * This method is inspired by Rust's `Option<T>.unwrap()`, where attempting to unwrap a `None` value results in a panic.\n   * This method will throw an error if the value is not present.\n   *\n   * @example\n   * const option = new MoveOption<Bool>(new Bool(true));\n   * const value = option.unwrap();  // Returns the Bool instance\n   *\n   * @throws {Error} Throws an error if the MoveOption does not contain a value.\n   *\n   * @returns {T} The contained value if present.\n   * @group Implementation\n   * @category BCS\n   */\n  unwrap(): T {\n    if (!this.isSome()) {\n      throw new Error(\"Called unwrap on a MoveOption with no value\");\n    } else {\n      return this.vec.values[0];\n    }\n  }\n\n  /**\n   * Check if the MoveOption has a value.\n   *\n   * @returns {boolean} Returns true if there is exactly one value in the MoveOption.\n   * @group Implementation\n   * @category BCS\n   */\n  isSome(): boolean {\n    return this.vec.values.length === 1;\n  }\n\n  serialize(serializer: Serializer): void {\n    // serialize 0 or 1\n    // if 1, serialize the value\n    this.vec.serialize(serializer);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U8> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U8(1).isSome() === true;\n   * MoveOption.U8().isSome() === false;\n   * MoveOption.U8(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U8> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U8(value?: number | null): MoveOption<U8> {\n    return new MoveOption<U8>(value !== null && value !== undefined ? new U8(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U16> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U16(1).isSome() === true;\n   * MoveOption.U16().isSome() === false;\n   * MoveOption.U16(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U16> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U16(value?: number | null): MoveOption<U16> {\n    return new MoveOption<U16>(value !== null && value !== undefined ? new U16(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U32> from a `number` or `undefined`.\n   *\n   * @example\n   * MoveOption.U32(1).isSome() === true;\n   * MoveOption.U32().isSome() === false;\n   * MoveOption.U32(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U32> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U32(value?: number | null): MoveOption<U32> {\n    return new MoveOption<U32>(value !== null && value !== undefined ? new U32(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U64> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U64(1).isSome() === true;\n   * MoveOption.U64().isSome() === false;\n   * MoveOption.U64(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U64> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U64(value?: AnyNumber | null): MoveOption<U64> {\n    return new MoveOption<U64>(value !== null && value !== undefined ? new U64(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U128> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U128(1).isSome() === true;\n   * MoveOption.U128().isSome() === false;\n   * MoveOption.U128(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U128> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U128(value?: AnyNumber | null): MoveOption<U128> {\n    return new MoveOption<U128>(value !== null && value !== undefined ? new U128(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<U256> from a `number` or a `bigint` or `undefined`.\n   *\n   * @example\n   * MoveOption.U256(1).isSome() === true;\n   * MoveOption.U256().isSome() === false;\n   * MoveOption.U256(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<U256> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static U256(value?: AnyNumber | null): MoveOption<U256> {\n    return new MoveOption<U256>(value !== null && value !== undefined ? new U256(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<Bool> from a `boolean` or `undefined`.\n   *\n   * @example\n   * MoveOption.Bool(true).isSome() === true;\n   * MoveOption.Bool().isSome() === false;\n   * MoveOption.Bool(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<Bool> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static Bool(value?: boolean | null): MoveOption<Bool> {\n    return new MoveOption<Bool>(value !== null && value !== undefined ? new Bool(value) : undefined);\n  }\n\n  /**\n   * Factory method to generate a MoveOption<MoveString> from a `string` or `undefined`.\n   *\n   * @example\n   * MoveOption.MoveString(\"hello\").isSome() === true;\n   * MoveOption.MoveString(\"\").isSome() === true;\n   * MoveOption.MoveString().isSome() === false;\n   * MoveOption.MoveString(undefined).isSome() === false;\n   * @param value the value used to fill the MoveOption. If `value` is undefined\n   * the resulting MoveOption's .isSome() method will return false.\n   * @returns a MoveOption<MoveString> with an inner value `value`\n   * @group Implementation\n   * @category BCS\n   */\n  static MoveString(value?: string | null): MoveOption<MoveString> {\n    return new MoveOption<MoveString>(value !== null && value !== undefined ? new MoveString(value) : undefined);\n  }\n\n  static deserialize<U extends Serializable & EntryFunctionArgument>(\n    deserializer: Deserializer,\n    cls: Deserializable<U>,\n  ): MoveOption<U> {\n    const vector = MoveVector.deserialize(deserializer, cls);\n    return new MoveOption(vector.values[0]);\n  }\n}\n"], "mappings": "8MAiDO,IAAMA,EAAN,MAAMC,UACHC,CAEV,CAWE,YAAYC,EAAkB,CAC5B,MAAM,EACN,KAAK,OAASA,CAChB,CAUA,0BAA0BC,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAeA,2BAA2BD,EAA8B,CAGvD,GAAI,KAAK,OAAO,CAAC,IAAM,QAAa,EAAE,KAAK,OAAO,CAAC,YAAaE,GAAK,CAChD,IAAIC,EAAW,KAAK,WAAW,CAAC,EACxC,2BAA2BH,CAAU,EAChD,MACF,CACAA,EAAW,uBAAgE,EAC3EA,EAAW,UAAU,IAAI,CAC3B,CAmBA,OAAO,GAAGD,EAAkD,CAC1D,IAAIK,EAEJ,GAAI,MAAM,QAAQL,CAAM,GAAKA,EAAO,SAAW,EAE7CK,EAAU,CAAC,UACF,MAAM,QAAQL,CAAM,GAAK,OAAOA,EAAO,CAAC,GAAM,SACvDK,EAAUL,UACD,OAAOA,GAAW,SAAU,CACrC,IAAMM,EAAMC,EAAI,aAAaP,CAAM,EACnCK,EAAU,MAAM,KAAKC,EAAI,aAAa,CAAC,CACzC,SAAWN,aAAkB,WAC3BK,EAAU,MAAM,KAAKL,CAAM,MAE3B,OAAM,IAAI,MAAM,oEAAoE,EAGtF,OAAO,IAAIF,EAAeO,EAAQ,IAAKG,GAAM,IAAIL,EAAGK,CAAC,CAAC,CAAC,CACzD,CAkBA,OAAO,IAAIR,EAAwC,CACjD,OAAO,IAAIF,EAAgBE,EAAO,IAAKQ,GAAM,IAAIC,EAAID,CAAC,CAAC,CAAC,CAC1D,CAmBA,OAAO,IAAIR,EAAwC,CACjD,OAAO,IAAIF,EAAgBE,EAAO,IAAKQ,GAAM,IAAIE,EAAIF,CAAC,CAAC,CAAC,CAC1D,CAiBA,OAAO,IAAIR,EAA2C,CACpD,OAAO,IAAIF,EAAgBE,EAAO,IAAKQ,GAAM,IAAIG,EAAIH,CAAC,CAAC,CAAC,CAC1D,CAgBA,OAAO,KAAKR,EAA4C,CACtD,OAAO,IAAIF,EAAiBE,EAAO,IAAKQ,GAAM,IAAII,EAAKJ,CAAC,CAAC,CAAC,CAC5D,CAiBA,OAAO,KAAKR,EAA4C,CACtD,OAAO,IAAIF,EAAiBE,EAAO,IAAKQ,GAAM,IAAIK,EAAKL,CAAC,CAAC,CAAC,CAC5D,CAgBA,OAAO,KAAKR,EAA0C,CACpD,OAAO,IAAIF,EAAiBE,EAAO,IAAKQ,GAAM,IAAIM,EAAKN,CAAC,CAAC,CAAC,CAC5D,CAeA,OAAO,WAAWR,EAA+C,CAC/D,OAAO,IAAIF,EAAuBE,EAAO,IAAKQ,GAAM,IAAIO,EAAWP,CAAC,CAAC,CAAC,CACxE,CAWA,UAAUP,EAA8B,CACtCA,EAAW,gBAAgB,KAAK,MAAM,CACxC,CAqBA,OAAO,YACLe,EACAC,EACe,CACf,IAAMC,EAASF,EAAa,wBAAwB,EAC9ChB,EAAS,IAAI,MACnB,QAASmB,EAAI,EAAGA,EAAID,EAAQC,GAAK,EAC/BnB,EAAO,KAAKiB,EAAI,YAAYD,CAAY,CAAC,EAE3C,OAAO,IAAIlB,EAAWE,CAAM,CAC9B,CACF,EAWaI,EAAN,MAAMgB,UAAmBrB,CAA4C,CAG1E,YAAYsB,EAAiB,CAC3B,MAAM,EACN,KAAK,MAAQd,EAAI,aAAac,CAAK,EAAE,aAAa,CACpD,CAEA,UAAUpB,EAA8B,CACtCA,EAAW,eAAe,KAAK,KAAK,CACtC,CAEA,0BAA0BA,EAA8B,CACtD,KAAK,UAAUA,CAAU,CAC3B,CAEA,2BAA2BA,EAA8B,CACvDA,EAAW,uBAAkE,EAC7E,KAAK,UAAUA,CAAU,CAC3B,CAEA,OAAO,YAAYe,EAAwC,CACzD,OAAO,IAAII,EAAWJ,EAAa,iBAAiB,CAAC,CACvD,CAUA,aAA6DC,EAAuC,CAClG,IAAMD,EAAe,IAAIM,EAAa,KAAK,WAAW,CAAC,EACvDN,EAAa,wBAAwB,EACrC,IAAMO,EAAMP,EAAa,kBAAkBC,CAAG,EAC9C,OAAO,IAAIpB,EAAW0B,CAAG,CAC3B,CACF,EAYaR,EAAN,MAAMS,UAAmBzB,CAA4C,CAG1E,YAAYsB,EAAe,CACzB,MAAM,EACN,KAAK,MAAQA,CACf,CAEA,UAAUpB,EAA8B,CACtCA,EAAW,aAAa,KAAK,KAAK,CACpC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CAGvD,IAAMwB,EADc,IAAI,YAAY,EACC,OAAO,KAAK,KAAK,EAErC5B,EAAW,GAAG4B,CAAgB,EACtC,2BAA2BxB,CAAU,CAChD,CAEA,OAAO,YAAYe,EAAwC,CACzD,OAAO,IAAIQ,EAAWR,EAAa,eAAe,CAAC,CACrD,CACF,EAEaU,EAAN,MAAMC,UACH5B,CAEV,CAKE,YAAYsB,EAAkB,CAC5B,MAAM,EACF,OAAOA,EAAU,KAAeA,IAAU,KAC5C,KAAK,IAAM,IAAIxB,EAAW,CAACwB,CAAK,CAAC,EAEjC,KAAK,IAAM,IAAIxB,EAAW,CAAC,CAAC,EAG9B,CAAC,KAAK,KAAK,EAAI,KAAK,IAAI,MAC1B,CAEA,0BAA0BI,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAkBA,QAAY,CACV,GAAK,KAAK,OAAO,EAGf,OAAO,KAAK,IAAI,OAAO,CAAC,EAFxB,MAAM,IAAI,MAAM,6CAA6C,CAIjE,CASA,QAAkB,CAChB,OAAO,KAAK,IAAI,OAAO,SAAW,CACpC,CAEA,UAAUD,EAA8B,CAGtC,KAAK,IAAI,UAAUA,CAAU,CAC/B,CAeA,OAAO,GAAGoB,EAAuC,CAC/C,OAAO,IAAIM,EAAeN,GAAU,KAA8B,IAAIlB,EAAGkB,CAAK,EAAI,MAAS,CAC7F,CAeA,OAAO,IAAIA,EAAwC,CACjD,OAAO,IAAIM,EAAgBN,GAAU,KAA8B,IAAIZ,EAAIY,CAAK,EAAI,MAAS,CAC/F,CAeA,OAAO,IAAIA,EAAwC,CACjD,OAAO,IAAIM,EAAgBN,GAAU,KAA8B,IAAIX,EAAIW,CAAK,EAAI,MAAS,CAC/F,CAeA,OAAO,IAAIA,EAA2C,CACpD,OAAO,IAAIM,EAAgBN,GAAU,KAA8B,IAAIV,EAAIU,CAAK,EAAI,MAAS,CAC/F,CAeA,OAAO,KAAKA,EAA4C,CACtD,OAAO,IAAIM,EAAiBN,GAAU,KAA8B,IAAIT,EAAKS,CAAK,EAAI,MAAS,CACjG,CAeA,OAAO,KAAKA,EAA4C,CACtD,OAAO,IAAIM,EAAiBN,GAAU,KAA8B,IAAIR,EAAKQ,CAAK,EAAI,MAAS,CACjG,CAeA,OAAO,KAAKA,EAA0C,CACpD,OAAO,IAAIM,EAAiBN,GAAU,KAA8B,IAAIP,EAAKO,CAAK,EAAI,MAAS,CACjG,CAgBA,OAAO,WAAWA,EAA+C,CAC/D,OAAO,IAAIM,EAAuBN,GAAU,KAA8B,IAAIN,EAAWM,CAAK,EAAI,MAAS,CAC7G,CAEA,OAAO,YACLL,EACAC,EACe,CACf,IAAMW,EAAS/B,EAAW,YAAYmB,EAAcC,CAAG,EACvD,OAAO,IAAIU,EAAWC,EAAO,OAAO,CAAC,CAAC,CACxC,CACF", "names": ["MoveVector", "_MoveVector", "Serializable", "values", "serializer", "bcsBytes", "U8", "Serialized", "numbers", "hex", "Hex", "v", "U16", "U32", "U64", "U128", "U256", "Bool", "MoveString", "deserializer", "cls", "length", "i", "_Serialized", "value", "Deserializer", "vec", "_MoveString", "fixedStringBytes", "MoveOption", "_MoveOption", "vector"]}