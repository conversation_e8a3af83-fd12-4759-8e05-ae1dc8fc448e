{"version": 3, "sources": ["../../src/core/crypto/publicKey.ts"], "sourcesContent": ["import { AptosConfig } from \"../../api\";\nimport { Serializable } from \"../../bcs\";\nimport { HexInput } from \"../../types\";\nimport { AuthenticationKey } from \"../authenticationKey\";\nimport { Hex } from \"../hex\";\nimport { Signature } from \"./signature\";\n\n/**\n * Represents the arguments required to verify a digital signature.\n *\n * @param message - The original message that was signed.\n * @param signature - The signature to be verified against the message.\n * @group Implementation\n * @category Serialization\n */\nexport interface VerifySignatureArgs {\n  message: HexInput;\n  signature: Signature;\n}\n\n/**\n * Represents the arguments required to verify a digital signature asynchronously.\n *\n * The validity of certain types of signatures are dependent on network state.  This is the case for\n * Keyless signatures which need to lookup the verification key and keyless configuration.\n *\n * @param aptosConfig - The Aptos configuration to use\n * @param message - The original message that was signed.\n * @param signature - The signature to be verified against the message.\n * @group Implementation\n * @category Serialization\n */\nexport type VerifySignatureAsyncArgs = VerifySignatureArgs & {\n  aptosConfig: AptosConfig;\n  options?: any;\n};\n\n/**\n * Represents an abstract public key.\n *\n * This class provides a common interface for verifying signatures associated with the public key.\n * It allows for the retrieval of the raw public key bytes and the public key in a hexadecimal string format.\n * @group Implementation\n * @category Serialization\n */\nexport abstract class PublicKey extends Serializable {\n  /**\n   * Verifies that the private key associated with this public key signed the message with the given signature.\n   * @param args.message The message that was signed\n   * @param args.signature The signature to verify\n   * @group Implementation\n   * @category Serialization\n   */\n  abstract verifySignature(args: VerifySignatureArgs): boolean;\n\n  /**\n   * Verifies signature with the public key and makes any network calls required to get state required to verify the signature.\n   * @param args.aptosConfig The Aptos configuration\n   * @param args.message The message that was signed\n   * @param args.signature The signature to verify\n   * @group Implementation\n   * @category Serialization\n   */\n  async verifySignatureAsync(args: VerifySignatureAsyncArgs): Promise<boolean> {\n    return this.verifySignature(args);\n  }\n\n  /**\n   * Get the raw public key bytes\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  /**\n   * Get the public key as a hex string with a 0x prefix.\n   *\n   * @returns The public key in hex format.\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): string {\n    const bytes = this.toUint8Array();\n    return Hex.fromHexInput(bytes).toString();\n  }\n}\n\n/**\n * An abstract representation of an account public key.\n *\n * Provides a common interface for deriving an authentication key.\n *\n * @abstract\n * @group Implementation\n * @category Serialization\n */\nexport abstract class AccountPublicKey extends PublicKey {\n  /**\n   * Get the authentication key associated with this public key\n   * @group Implementation\n   * @category Serialization\n   */\n  abstract authKey(): AuthenticationKey;\n}\n"], "mappings": "kFA6CO,IAAeA,EAAf,cAAiCC,CAAa,CAkBnD,MAAM,qBAAqBC,EAAkD,CAC3E,OAAO,KAAK,gBAAgBA,CAAI,CAClC,CAOA,cAA2B,CACzB,OAAO,KAAK,WAAW,CACzB,CASA,UAAmB,CACjB,IAAMC,EAAQ,KAAK,aAAa,EAChC,OAAOC,EAAI,aAAaD,CAAK,EAAE,SAAS,CAC1C,CACF,EAWsBE,EAAf,cAAwCL,CAAU,CAOzD", "names": ["PublicKey", "Serializable", "args", "bytes", "Hex", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>"]}