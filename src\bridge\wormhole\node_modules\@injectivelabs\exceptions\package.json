{"name": "@injectivelabs/exceptions", "description": "List of exceptions that can be reused throughout Injective's projects.", "version": "1.15.17", "sideEffects": false, "license": "Apache-2.0", "type": "module", "types": "dist/cjs/index.d.ts", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "author": {"name": "InjectiveLabs", "email": "<EMAIL>"}, "files": ["dist"], "_moduleAliases": {"~exceptions": "dist"}, "exports": {".": {"react-native": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "default": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "scripts": {"build": "yarn build:cjs && yarn build:esm && yarn build:post", "build:cjs": "tsc --build --force tsconfig.build.json", "build:esm": "tsc --build --force tsconfig.build.esm.json", "build:watch": "tsc --build -w tsconfig.build.json && tsc -w --build tsconfig.build.esm.json && yarn build:post", "build:post": "shx cp ../../etc/stub/package.json.stub dist/cjs/package.json && shx cp ../../etc/stub/package.esm.json.stub dist/esm/package.json", "clean": "tsc --build tsconfig.build.json --clean && tsc --build tsconfig.build.esm.json --clean && shx rm -rf coverage *.log junit.xml dist && jest --clearCache && shx mkdir -p dist", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --coverage --ci --reporters='jest-junit'", "coverage": "jest --coverage", "coverage:show": "live-server coverage", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "start": "node dist/index.js"}, "dependencies": {"http-status-codes": "^2.3.0"}, "devDependencies": {"shx": "^0.3.4"}, "gitHead": "a80d42fd4220e1e9a6d0e8b211dcd229f84dd54e"}