"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const exceptions_1 = require("@injectivelabs/exceptions");
const MsgBase_js_1 = require("../../MsgBase.js");
const utils_js_1 = require("../utils.js");
const types_js_1 = require("./../types.js");
/**
 * @category Messages
 */
class MsgGrant extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgGrant(params);
    }
    toProto() {
        const { params } = this;
        const timestamp = this.getTimestamp();
        const grant = core_proto_ts_1.CosmosAuthzV1Beta1Authz.Grant.create();
        if (!params.authorization && !params.messageType) {
            throw new exceptions_1.GeneralException(new Error('Either authorization or messageType must be provided'));
        }
        const authorization = params.authorization ||
            (0, utils_js_1.getGenericAuthorizationFromMessageType)(params.messageType);
        grant.authorization = authorization;
        grant.expiration = new Date(Number(timestamp.seconds) * 1000);
        const message = core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.create();
        message.granter = params.granter;
        message.grantee = params.grantee;
        message.grant = grant;
        return core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.fromJSON(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const proto = this.toProto();
        const timestamp = this.getTimestamp();
        const message = proto;
        if (!params.authorization && !params.messageType) {
            throw new exceptions_1.GeneralException(new Error('Either authorization or messageType must be provided'));
        }
        const authorization = params.authorization ||
            (0, utils_js_1.getGenericAuthorizationFromMessageType)(params.messageType);
        if (!authorization.typeUrl.includes(types_js_1.GrantAuthorizationType.GenericAuthorization)) {
            throw new exceptions_1.GeneralException(new Error('Currently, only GenericAuthorization type is supported'));
        }
        const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(authorization.value);
        const messageWithAuthorizationType = (0, snakecase_keys_1.default)({
            ...message,
            grant: {
                authorization: {
                    type: 'cosmos-sdk/GenericAuthorization',
                    value: { msg: genericAuthorization.msg },
                },
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        });
        return {
            type: 'cosmos-sdk/MsgGrant',
            value: messageWithAuthorizationType,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.authz.v1beta1.MsgGrant',
            message: proto,
        };
    }
    toWeb3Gw() {
        const { params } = this;
        const amino = this.toAmino();
        const timestamp = this.getTimestamp();
        if (!params.authorization && !params.messageType) {
            throw new exceptions_1.GeneralException(new Error('Either authorization or messageType must be provided'));
        }
        const authorization = params.authorization ||
            (0, utils_js_1.getGenericAuthorizationFromMessageType)(params.messageType);
        if (!authorization.typeUrl.includes(types_js_1.GrantAuthorizationType.GenericAuthorization)) {
            throw new exceptions_1.GeneralException(new Error('Currently, only GenericAuthorization type is supported'));
        }
        const genericAuthorization = core_proto_ts_1.CosmosAuthzV1Beta1Authz.GenericAuthorization.decode(authorization.value);
        const messageWithAuthorizationType = {
            granter: amino.value.granter,
            grantee: amino.value.grantee,
            grant: {
                authorization: {
                    '@type': '/cosmos.authz.v1beta1.GenericAuthorization',
                    msg: genericAuthorization.msg,
                },
                expiration: new Date(Number(timestamp.seconds) * 1000)
                    .toISOString()
                    .replace('.000Z', 'Z'),
            },
        };
        return {
            '@type': '/cosmos.authz.v1beta1.MsgGrant',
            ...messageWithAuthorizationType,
        };
    }
    getTimestamp() {
        const { params } = this;
        if (params.expiration) {
            const timestamp = core_proto_ts_1.GoogleProtobufTimestamp.Timestamp.create();
            timestamp.seconds = params.expiration.toString();
            return timestamp;
        }
        const defaultExpiryYears = params.expiryInSeconds ? 0 : 5;
        const dateNow = new Date();
        const expiration = new Date(dateNow.getFullYear() + (params.expiryInYears || defaultExpiryYears), dateNow.getMonth(), dateNow.getDate());
        const timestamp = core_proto_ts_1.GoogleProtobufTimestamp.Timestamp.create();
        const timestampInSeconds = (expiration.getTime() / 1000 +
            (params.expiryInSeconds || 0)).toString();
        timestamp.seconds = timestampInSeconds;
        return timestamp;
    }
    toBinary() {
        return core_proto_ts_1.CosmosAuthzV1Beta1Tx.MsgGrant.encode(this.toProto()).finish();
    }
}
exports.default = MsgGrant;
