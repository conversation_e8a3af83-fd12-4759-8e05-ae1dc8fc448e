"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcTradingApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcTradingApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_1.IndexerModule.Trading;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new indexer_proto_ts_1.InjectiveTradingRpc.InjectiveTradingRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchTradingStats() {
        const request = indexer_proto_ts_1.InjectiveTradingRpc.GetTradingStatsRequest.create();
        try {
            const response = await this.retry(() => this.client.GetTradingStats(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveTradingRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'TradingStats',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'TradingStats',
                contextModule: this.module,
            });
        }
    }
    async fetchGridStrategies({ skip, state, limit, withTvl, endTime, marketId, startTime, marketType, strategyType, subaccountId, accountAddress, withPerformance, pendingExecution, lastExecutedTime, isTrailingStrategy, }) {
        const request = indexer_proto_ts_1.InjectiveTradingRpc.ListTradingStrategiesRequest.create();
        if (accountAddress) {
            request.accountAddress = accountAddress;
        }
        if (subaccountId) {
            request.subaccountId = subaccountId;
        }
        if (strategyType) {
            request.strategyType = strategyType;
        }
        if (marketType) {
            request.marketType = marketType;
        }
        if (state) {
            request.state = state;
        }
        if (limit) {
            request.limit = limit;
        }
        if (skip) {
            request.skip = skip.toString();
        }
        if (marketId) {
            request.marketId = marketId;
        }
        if (withTvl) {
            request.withTvl = withTvl;
        }
        if (withPerformance) {
            request.withPerformance = withPerformance;
        }
        if (isTrailingStrategy) {
            request.isTrailingStrategy = isTrailingStrategy;
        }
        if (startTime) {
            request.startTime = startTime.toString();
        }
        if (endTime) {
            request.endTime = endTime.toString();
        }
        if (pendingExecution) {
            request.pendingExecution = pendingExecution;
        }
        if (lastExecutedTime) {
            request.lastExecutedTime = lastExecutedTime.toString();
        }
        try {
            const response = await this.retry(() => this.client.ListTradingStrategies(request, this.metadata));
            return response;
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveTradingRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'GridStrategies',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'GridStrategies',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcTradingApi = IndexerGrpcTradingApi;
