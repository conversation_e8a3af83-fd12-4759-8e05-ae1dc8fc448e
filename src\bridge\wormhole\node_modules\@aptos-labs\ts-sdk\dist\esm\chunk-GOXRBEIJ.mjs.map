{"version": 3, "sources": ["../../src/core/crypto/poseidon.ts"], "sourcesContent": ["/* eslint-disable no-bitwise */\nimport {\n  poseidon1,\n  poseidon2,\n  poseidon3,\n  poseidon4,\n  poseidon5,\n  poseidon6,\n  poseidon7,\n  poseidon8,\n  poseidon9,\n  poseidon10,\n  poseidon11,\n  poseidon12,\n  poseidon13,\n  poseidon14,\n  poseidon15,\n  poseidon16,\n} from \"poseidon-lite\";\n\nconst numInputsToPoseidonFunc = [\n  poseidon1,\n  poseidon2,\n  poseidon3,\n  poseidon4,\n  poseidon5,\n  poseidon6,\n  poseidon7,\n  poseidon8,\n  poseidon9,\n  poseidon10,\n  poseidon11,\n  poseidon12,\n  poseidon13,\n  poseidon14,\n  poseidon15,\n  poseidon16,\n];\n\nconst BYTES_PACKED_PER_SCALAR = 31;\nconst MAX_NUM_INPUT_SCALARS = 16;\nconst MAX_NUM_INPUT_BYTES = (MAX_NUM_INPUT_SCALARS - 1) * BYTES_PACKED_PER_SCALAR;\n\n/**\n * Hashes a string to a field element via Poseidon hashing.\n * This function is useful for converting a string into a fixed-size hash that can be used in cryptographic applications.\n *\n * @param str - The string to be hashed.\n * @param maxSizeBytes - The maximum size in bytes for the resulting hash.\n * @returns bigint - The result of the hash.\n * @group Implementation\n * @category Serialization\n */\nexport function hashStrToField(str: string, maxSizeBytes: number): bigint {\n  const textEncoder = new TextEncoder();\n  const strBytes = textEncoder.encode(str);\n  return hashBytesWithLen(strBytes, maxSizeBytes);\n}\n\n/**\n * Computes a Poseidon hash of the provided byte array, ensuring that the byte array does not exceed the specified maximum size.\n * This function is useful for generating a hash from a byte array while enforcing size constraints.\n *\n * @param bytes - The byte array to be hashed.\n * @param maxSizeBytes - The maximum allowed size for the byte array.\n * @throws Error if the length of the inputted bytes exceeds the specified maximum size.\n * @group Implementation\n * @category Serialization\n */\nfunction hashBytesWithLen(bytes: Uint8Array, maxSizeBytes: number): bigint {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Inputted bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  const packed = padAndPackBytesWithLen(bytes, maxSizeBytes);\n  return poseidonHash(packed);\n}\n\n/**\n * Pads the input byte array with zeros to a specified maximum size and then packs the bytes.\n * This function ensures that the byte array does not exceed the specified maximum size, throwing an error if it does.\n *\n * @param bytes - The byte array to be padded and packed.\n * @param maxSizeBytes - The maximum size in bytes that the input array can have.\n * @throws Error if the input byte array exceeds the specified maximum size.\n * @group Implementation\n * @category Serialization\n */\nfunction padAndPackBytesNoLen(bytes: Uint8Array, maxSizeBytes: number): bigint[] {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Input bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  const paddedStrBytes = padUint8ArrayWithZeros(bytes, maxSizeBytes);\n  return packBytes(paddedStrBytes);\n}\n\n/**\n * Pads and packs the given byte array to a specified maximum size and appends its length.\n * This function ensures that the byte array does not exceed the maximum size, throwing an error if it does.\n * It is useful for preparing byte data for further processing or transmission by ensuring a consistent format.\n *\n * @param bytes - The byte array to be padded and packed.\n * @param maxSizeBytes - The maximum allowed size for the byte array.\n * @throws Error if the length of the input bytes exceeds the maximum size.\n * @returns A new Uint8Array that contains the padded and packed bytes along with the length of the original byte array.\n * @group Implementation\n * @category Serialization\n */\nexport function padAndPackBytesWithLen(bytes: Uint8Array, maxSizeBytes: number): bigint[] {\n  if (bytes.length > maxSizeBytes) {\n    throw new Error(`Input bytes of length ${bytes} is longer than ${maxSizeBytes}`);\n  }\n  return padAndPackBytesNoLen(bytes, maxSizeBytes).concat([BigInt(bytes.length)]);\n}\n\n/**\n * Packs a Uint8Array into an array of BigInts, ensuring the input does not exceed the maximum allowed bytes.\n * @param bytes - The Uint8Array to be packed.\n * @throws {Error} Throws an error if the input exceeds the maximum number of bytes allowed.\n * @group Implementation\n * @category Serialization\n */\nfunction packBytes(bytes: Uint8Array): bigint[] {\n  if (bytes.length > MAX_NUM_INPUT_BYTES) {\n    throw new Error(`Can't pack more than ${MAX_NUM_INPUT_BYTES}.  Was given ${bytes.length} bytes`);\n  }\n  return chunkUint8Array(bytes, BYTES_PACKED_PER_SCALAR).map((chunk) => bytesToBigIntLE(chunk));\n}\n\n/**\n * Splits a Uint8Array into smaller chunks of the specified size.\n * This function is useful for processing large arrays in manageable segments.\n *\n * @param array - The Uint8Array to be split into chunks.\n * @param chunkSize - The size of each chunk.\n * @returns An array of Uint8Array chunks.\n * @group Implementation\n * @category Serialization\n */\nfunction chunkUint8Array(array: Uint8Array, chunkSize: number): Uint8Array[] {\n  const result: Uint8Array[] = [];\n  for (let i = 0; i < array.length; i += chunkSize) {\n    result.push(array.subarray(i, i + chunkSize));\n  }\n  return result;\n}\n\n/**\n * Converts a little-endian byte array into a BigInt.\n * This function is useful for interpreting byte data as a numerical value in a way that respects the little-endian format.\n *\n * @param bytes - The byte array to convert.\n * @returns The resulting BigInt representation of the byte array.\n * @group Implementation\n * @category Serialization\n */\nexport function bytesToBigIntLE(bytes: Uint8Array): bigint {\n  let result = BigInt(0);\n  for (let i = bytes.length - 1; i >= 0; i -= 1) {\n    result = (result << BigInt(8)) | BigInt(bytes[i]);\n  }\n  return result;\n}\n\n/**\n * Converts a bigint value into a little-endian byte array of a specified length.\n * This function is useful for representing large integers in a byte format, which is often required for cryptographic operations\n * or binary data manipulation.\n *\n * @param value - The number to convert into bytes.\n * @param length - The desired length of the resulting byte array.\n * @returns A Uint8Array containing the little-endian representation of the bigint value.\n * @group Implementation\n * @category Serialization\n */\nexport function bigIntToBytesLE(value: bigint | number, length: number): Uint8Array {\n  let val = BigInt(value);\n  const bytes = new Uint8Array(length);\n  for (let i = 0; i < length; i += 1) {\n    bytes[i] = Number(val & BigInt(0xff));\n    val >>= BigInt(8);\n  }\n  return bytes;\n}\n\n/**\n * Pads the input Uint8Array with zeros to achieve the specified size.\n * This function is useful for ensuring that a byte array meets a required length for further processing.\n *\n * @param inputArray - The Uint8Array to be padded.\n * @param paddedSize - The desired size of the padded array, which must be greater than or equal to the input array size.\n * @throws Error if paddedSize is less than the length of inputArray.\n * @group Implementation\n * @category Serialization\n */\nfunction padUint8ArrayWithZeros(inputArray: Uint8Array, paddedSize: number): Uint8Array {\n  if (paddedSize < inputArray.length) {\n    throw new Error(\"Padded size must be greater than or equal to the input array size.\");\n  }\n\n  // Create a new Uint8Array with the padded size\n  const paddedArray = new Uint8Array(paddedSize);\n\n  // Copy the content of the input array to the new array\n  paddedArray.set(inputArray);\n\n  // Fill the remaining space with zeros\n  for (let i = inputArray.length; i < paddedSize; i += 1) {\n    paddedArray[i] = 0;\n  }\n\n  return paddedArray;\n}\n\n/**\n * Hashes up to 16 scalar elements via the Poseidon hashing algorithm.\n * Each element must be scalar fields of the BN254 elliptic curve group.\n *\n * @param inputs - An array of elements to be hashed, which can be of type number, bigint, or string.\n * @returns bigint - The result of the hash.\n * @throws Error - Throws an error if the input length exceeds the maximum allowed.\n * @group Implementation\n * @category Serialization\n */\nexport function poseidonHash(inputs: (number | bigint | string)[]): bigint {\n  if (inputs.length > numInputsToPoseidonFunc.length) {\n    throw new Error(\n      `Unable to hash input of length ${inputs.length}.  Max input length is ${numInputsToPoseidonFunc.length}`,\n    );\n  }\n  return numInputsToPoseidonFunc[inputs.length - 1](inputs);\n}\n"], "mappings": "AACA,OACE,aAAAA,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,aAAAC,EACA,cAAAC,EACA,cAAAC,EACA,cAAAC,EACA,cAAAC,EACA,cAAAC,EACA,cAAAC,EACA,cAAAC,MACK,gBAEP,IAAMC,EAA0B,CAC9BhB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CACF,EAEME,EAA0B,GAC1BC,EAAwB,GACxBC,GAAuBD,EAAwB,GAAKD,EAYnD,SAASG,EAAeC,EAAaC,EAA8B,CAExE,IAAMC,EADc,IAAI,YAAY,EACP,OAAOF,CAAG,EACvC,OAAOG,EAAiBD,EAAUD,CAAY,CAChD,CAYA,SAASE,EAAiBC,EAAmBH,EAA8B,CACzE,GAAIG,EAAM,OAASH,EACjB,MAAM,IAAI,MAAM,4BAA4BG,CAAK,mBAAmBH,CAAY,EAAE,EAEpF,IAAMI,EAASC,EAAuBF,EAAOH,CAAY,EACzD,OAAOM,EAAaF,CAAM,CAC5B,CAYA,SAASG,EAAqBJ,EAAmBH,EAAgC,CAC/E,GAAIG,EAAM,OAASH,EACjB,MAAM,IAAI,MAAM,yBAAyBG,CAAK,mBAAmBH,CAAY,EAAE,EAEjF,IAAMQ,EAAiBC,EAAuBN,EAAOH,CAAY,EACjE,OAAOU,EAAUF,CAAc,CACjC,CAcO,SAASH,EAAuBF,EAAmBH,EAAgC,CACxF,GAAIG,EAAM,OAASH,EACjB,MAAM,IAAI,MAAM,yBAAyBG,CAAK,mBAAmBH,CAAY,EAAE,EAEjF,OAAOO,EAAqBJ,EAAOH,CAAY,EAAE,OAAO,CAAC,OAAOG,EAAM,MAAM,CAAC,CAAC,CAChF,CASA,SAASO,EAAUP,EAA6B,CAC9C,GAAIA,EAAM,OAASN,EACjB,MAAM,IAAI,MAAM,wBAAwBA,CAAmB,gBAAgBM,EAAM,MAAM,QAAQ,EAEjG,OAAOQ,EAAgBR,EAAOR,CAAuB,EAAE,IAAKiB,GAAUC,EAAgBD,CAAK,CAAC,CAC9F,CAYA,SAASD,EAAgBG,EAAmBC,EAAiC,CAC3E,IAAMC,EAAuB,CAAC,EAC9B,QAASC,EAAI,EAAGA,EAAIH,EAAM,OAAQG,GAAKF,EACrCC,EAAO,KAAKF,EAAM,SAASG,EAAGA,EAAIF,CAAS,CAAC,EAE9C,OAAOC,CACT,CAWO,SAASH,EAAgBV,EAA2B,CACzD,IAAIa,EAAS,OAAO,CAAC,EACrB,QAASC,EAAId,EAAM,OAAS,EAAGc,GAAK,EAAGA,GAAK,EAC1CD,EAAUA,GAAU,OAAO,CAAC,EAAK,OAAOb,EAAMc,CAAC,CAAC,EAElD,OAAOD,CACT,CAaO,SAASE,EAAgBC,EAAwBC,EAA4B,CAClF,IAAIC,EAAM,OAAOF,CAAK,EAChBhB,EAAQ,IAAI,WAAWiB,CAAM,EACnC,QAASH,EAAI,EAAGA,EAAIG,EAAQH,GAAK,EAC/Bd,EAAMc,CAAC,EAAI,OAAOI,EAAM,OAAO,GAAI,CAAC,EACpCA,IAAQ,OAAO,CAAC,EAElB,OAAOlB,CACT,CAYA,SAASM,EAAuBa,EAAwBC,EAAgC,CACtF,GAAIA,EAAaD,EAAW,OAC1B,MAAM,IAAI,MAAM,oEAAoE,EAItF,IAAME,EAAc,IAAI,WAAWD,CAAU,EAG7CC,EAAY,IAAIF,CAAU,EAG1B,QAASL,EAAIK,EAAW,OAAQL,EAAIM,EAAYN,GAAK,EACnDO,EAAYP,CAAC,EAAI,EAGnB,OAAOO,CACT,CAYO,SAASlB,EAAamB,EAA8C,CACzE,GAAIA,EAAO,OAAS/B,EAAwB,OAC1C,MAAM,IAAI,MACR,kCAAkC+B,EAAO,MAAM,0BAA0B/B,EAAwB,MAAM,EACzG,EAEF,OAAOA,EAAwB+B,EAAO,OAAS,CAAC,EAAEA,CAAM,CAC1D", "names": ["poseidon1", "poseidon2", "poseidon3", "poseidon4", "poseidon5", "poseidon6", "poseidon7", "poseidon8", "poseidon9", "poseidon10", "poseidon11", "poseidon12", "poseidon13", "poseidon14", "poseidon15", "poseidon16", "numInputsToPoseidonFunc", "BYTES_PACKED_PER_SCALAR", "MAX_NUM_INPUT_SCALARS", "MAX_NUM_INPUT_BYTES", "hashStrToField", "str", "maxSizeBytes", "strBytes", "hashBytesWithLen", "bytes", "packed", "padAndPackBytesWithLen", "poseidonHash", "padAndPackBytesNoLen", "paddedStrBytes", "padUint8ArrayWithZeros", "packBytes", "chunkUint8Array", "chunk", "bytesToBigIntLE", "array", "chunkSize", "result", "i", "bigIntToBytesLE", "value", "length", "val", "inputArray", "paddedSize", "paddedArray", "inputs"]}