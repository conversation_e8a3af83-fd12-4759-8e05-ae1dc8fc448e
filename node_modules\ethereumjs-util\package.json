{"name": "ethereumjs-util", "version": "7.1.5", "description": "A collection of utility functions for Ethereum", "license": "MPL-2.0", "author": "mjbecze <<EMAIL>>", "keywords": ["ethereum", "utilities", "utils"], "engines": {"node": ">=10.0.0"}, "files": ["dist", "dist.browser", "src"], "main": "dist/index.js", "types": "dist/index.d.ts", "browser": "dist.browser/index.js", "scripts": {"build": "npm run build:node && npm run build:browser", "build:node": "../../config/cli/ts-build.sh node", "build:browser": "../../config/cli/ts-build.sh browser", "prepublishOnly": "../../config/cli/prepublish.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "docs:build": "npx typedoc --options typedoc.js", "lint": "../../config/cli/lint.sh", "lint:fix": "../../config/cli/lint-fix.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node", "test:browser": "karma start karma.conf.js", "test:node": "npm run tape -- test/*.spec.ts", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@types/bn.js": "^5.1.0", "bn.js": "^5.1.2", "create-hash": "^1.1.2", "ethereum-cryptography": "^0.1.3", "rlp": "^2.2.4"}, "devDependencies": {"@types/assert": "^1.5.4", "@types/node": "^16.11.7", "@types/secp256k1": "^4.0.1", "@types/tape": "^4.13.2", "eslint": "^6.8.0", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-tap": "^4.2.0", "karma-typescript": "^5.5.3", "nyc": "^15.1.0", "prettier": "^2.0.5", "tape": "^4.10.1", "ts-node": "^10.2.1", "typescript": "^4.4.2"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/util#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+util%22"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/tcoulter"}, {"name": "<PERSON>", "url": "https://github.com/SilentCicero"}, {"name": "Mr. <PERSON>", "url": "https://github.com/MrChico"}, {"name": "Dũng Trần", "email": "<EMAIL>", "url": "https://github.com/tad88dev"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic"}, {"name": "<PERSON>", "url": "https://github.com/tgerring"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fanatid"}, {"name": "kuma<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kumavis"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/asinyagin"}]}