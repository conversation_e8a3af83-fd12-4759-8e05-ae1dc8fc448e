"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NeptuneQueryTransformer = void 0;
const index_js_1 = require("../../../utils/index.js");
class NeptuneQueryTransformer {
    static contractPricesResponseToPrices(response) {
        const data = JSON.parse((0, index_js_1.toUtf8)(response.data));
        return data.map(([assetInfo, priceInfo]) => ({
            assetInfo,
            price: priceInfo.price,
        }));
    }
    static contractLendingRatesResponseToLendingRates(response) {
        const data = JSON.parse((0, index_js_1.toUtf8)(response.data));
        return data.map(([assetInfo, lendingRate]) => ({
            assetInfo,
            lendingRate,
        }));
    }
}
exports.NeptuneQueryTransformer = NeptuneQueryTransformer;
