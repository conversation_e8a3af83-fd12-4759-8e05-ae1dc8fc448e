import { grpc } from "@injectivelabs/grpc-web";
import { GetVersionRequest, GetVersionResponse } from "./version";
export declare const protobufPackage = "cometbft.services.version.v1";
/**
 * VersionService simply provides version information about the node and the
 * protocols it uses.
 *
 * The intention with this service is to offer a stable interface through which
 * clients can access version information. This means that the version of the
 * service should be kept stable at v1, with GetVersionResponse evolving only
 * in non-breaking ways.
 */
export interface VersionService {
    /**
     * GetVersion retrieves version information about the node and the protocols
     * it implements.
     */
    GetVersion(request: DeepPartial<GetVersionRequest>, metadata?: grpc.Metadata): Promise<GetVersionResponse>;
}
export declare class VersionServiceClientImpl implements VersionService {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetVersion(request: DeepPartial<GetVersionRequest>, metadata?: grpc.Metadata): Promise<GetVersionResponse>;
}
export declare const VersionServiceDesc: {
    serviceName: string;
};
export declare const VersionServiceGetVersionDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
