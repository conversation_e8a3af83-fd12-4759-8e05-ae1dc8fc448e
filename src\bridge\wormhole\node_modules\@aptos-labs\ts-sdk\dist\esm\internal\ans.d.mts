import { AptosConfig } from '../api/aptosConfig.mjs';
import { GetANSNameResponse } from '../types/indexer.mjs';
import { PaginationArgs, OrderByArg, WhereArg } from '../types/types.mjs';
import { AccountAddressInput, AccountAddress } from '../core/accountAddress.mjs';
import { CurrentAptosNamesBoolExp } from '../types/generated/types.mjs';
import { SimpleTransaction } from '../transactions/instances/simpleTransaction.mjs';
import { InputGenerateTransactionOptions } from '../transactions/types.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/generated/operations.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

/**
 * This file contains the underlying implementations for exposed API surface in
 * the {@link api/name}. By moving the methods out into a separate file,
 * other namespaces and processes can access these methods without depending on the entire
 * name namespace and without having a dependency cycle error.
 * @group Implementation
 */

declare const VALIDATION_RULES_DESCRIPTION: string;
/**
 * Validate if a given fragment is a valid ANS segment.
 * This function checks the length and character constraints of the fragment to ensure it meets the ANS standards.
 *
 * @param fragment - A fragment of a name, either the domain or subdomain.
 * @returns A boolean indicating if the fragment is a valid fragment.
 * @group Implementation
 */
declare function isValidANSSegment(fragment: string): boolean;
/**
 * Checks if an ANS name is valid or not.
 *
 * @param name - A string of the domain name, which can include or exclude the .apt suffix.
 * @group Implementation
 */
declare function isValidANSName(name: string): {
    domainName: string;
    subdomainName?: string;
};
/**
 * Policy for determining how subdomains expire in relation to their parent domain.
 * @group Implementation
 */
declare enum SubdomainExpirationPolicy {
    Independent = 0,
    FollowsDomain = 1
}
/**
 * Determine if a given ANS name is considered active based on its expiration dates.
 * Domains are active if their expiration date is in the future, while subdomains may
 * follow their parent's expiration policy (1) or expire independently (0).
 * If the subdomain is expiring independently, it can expire before their parent, but not after.
 *
 * @param name - An ANS name returned from one of the functions of the SDK.
 * @returns A boolean indicating whether the contract considers the name active or not.
 * @group Implementation
 */
declare function isActiveANSName(name: GetANSNameResponse[0]): boolean;
declare const LOCAL_ANS_ACCOUNT_PK: string;
declare const LOCAL_ANS_ACCOUNT_ADDRESS: string;
/**
 * Retrieve the owner address of a specified domain or subdomain.
 *
 * @param args - The arguments for retrieving the owner address.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.name - The name of the domain or subdomain to query.
 * @returns The account address of the owner, or undefined if not found.
 * @group Implementation
 */
declare function getOwnerAddress(args: {
    aptosConfig: AptosConfig;
    name: string;
}): Promise<AccountAddress | undefined>;
/**
 * Parameters for registering a name in the Aptos network.
 *
 * @param aptosConfig - Configuration settings for the Aptos network.
 * @param sender - The account initiating the name registration.
 * @param name - The name to be registered.
 * @param expiration - The expiration policy for the name registration.
 * @group Implementation
 */
interface RegisterNameParameters {
    aptosConfig: AptosConfig;
    sender: Account;
    name: string;
    expiration: {
        policy: "domain";
        years?: 1;
    } | {
        policy: "subdomain:follow-domain";
    } | {
        policy: "subdomain:independent";
        expirationDate: number;
    };
    transferable?: boolean;
    toAddress?: AccountAddressInput;
    targetAddress?: AccountAddressInput;
    options?: InputGenerateTransactionOptions;
}
/**
 * Registers a domain or subdomain with the specified parameters. This function ensures that the provided names and expiration
 * policies are valid before proceeding with the registration process.
 *
 * @param args - The parameters required for registering a name.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.expiration - The expiration details for the registration.
 * @param args.name - The name to be registered, which can be a domain or subdomain.
 * @param args.sender - The account details of the sender initiating the registration.
 * @param args.targetAddress - The target address for the registration.
 * @param args.toAddress - The address to which the registration is associated.
 * @param args.options - Additional options for the registration process.
 * @param args.transferable - Indicates whether the registered name is transferable.
 *
 * @throws Error if the provided expiration policy is invalid for subdomains.
 * @throws Error if the domain does not exist.
 * @throws Error if the subdomain expiration time exceeds the domain expiration time.
 *
 * @returns A transaction object representing the registration process.
 * @group Implementation
 */
declare function registerName(args: RegisterNameParameters): Promise<SimpleTransaction>;
/**
 * Retrieves the expiration time of a specified domain or subdomain in epoch milliseconds.
 *
 * @param args - The arguments for the function.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.name - The name of the domain or subdomain to check.
 * @returns The expiration time in epoch milliseconds, or undefined if an error occurs.
 * @group Implementation
 */
declare function getExpiration(args: {
    aptosConfig: AptosConfig;
    name: string;
}): Promise<number | undefined>;
/**
 * Retrieves the primary name associated with a given account address.
 * This function helps in obtaining the complete domain name by combining the subdomain and domain names.
 *
 * @param args - The arguments for retrieving the primary name.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.address - The account address for which to retrieve the primary name.
 * @returns The primary name as a string, or undefined if no domain name exists.
 * @group Implementation
 */
declare function getPrimaryName(args: {
    aptosConfig: AptosConfig;
    address: AccountAddressInput;
}): Promise<string | undefined>;
/**
 * Sets the primary name for the specified account, allowing for the association of a domain or subdomain with the account.
 * If no name is provided, it clears the existing primary name.
 *
 * @param args - The arguments for setting the primary name.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.sender - The account that is sending the transaction.
 * @param args.name - The name to set as the primary name. If omitted, the function will clear the primary name.
 * @param args.options - Optional transaction generation options.
 * @returns A transaction object representing the operation.
 * @group Implementation
 */
declare function setPrimaryName(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    name?: string;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;
/**
 * Retrieves the target address associated with a given domain name and subdomain name.
 *
 * @param args - The arguments for retrieving the target address.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.name - The name of the domain, which may include a subdomain.
 * @returns The target address as an AccountAddress, or undefined if not found.
 * @group Implementation
 */
declare function getTargetAddress(args: {
    aptosConfig: AptosConfig;
    name: string;
}): Promise<AccountAddress | undefined>;
/**
 * Sets the target address for a specified domain and subdomain in the Aptos network.
 * This function helps to associate a given address with a domain name, allowing for easier access and management of resources.
 *
 * @param args - The arguments for setting the target address.
 * @param args.aptosConfig - The configuration settings for the Aptos network.
 * @param args.sender - The account that is sending the transaction.
 * @param args.name - The name of the domain or subdomain to be set.
 * @param args.address - The address to be associated with the domain or subdomain.
 * @param args.options - Optional parameters for generating the transaction.
 *
 * @returns A transaction object representing the set target address operation.
 * @group Implementation
 */
declare function setTargetAddress(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    name: string;
    address: AccountAddressInput;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;
/**
 * Retrieves the active Aptos name associated with the specified domain and subdomain.
 *
 * @param args - The parameters for the function.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.name - The name to look up, which includes the domain and optional subdomain.
 * @returns The active Aptos name if it exists; otherwise, returns undefined.
 * @group Implementation
 */
declare function getName(args: {
    aptosConfig: AptosConfig;
    name: string;
}): Promise<GetANSNameResponse[0] | undefined>;
/**
 * Options for querying names, including pagination, ordering, and filtering criteria.
 *
 * @param options - Pagination and filtering options for the query.
 * @group Implementation
 */
interface QueryNamesOptions {
    options?: PaginationArgs & OrderByArg<GetANSNameResponse[0]> & WhereArg<CurrentAptosNamesBoolExp>;
}
/**
 * Arguments for retrieving account names based on the specified account address.
 *
 * @param accountAddress - The address of the account for which names are to be retrieved.
 * @group Implementation
 */
interface GetAccountNamesArgs extends QueryNamesOptions {
    accountAddress: AccountAddressInput;
}
/**
 * Retrieves the current Aptos names associated with a specific account address.
 *
 * @param args - The arguments for retrieving account names.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.options - Optional parameters for querying account names.
 * @param args.options.limit - The maximum number of names to retrieve.
 * @param args.options.offset - The number of names to skip before starting to collect the result set.
 * @param args.options.orderBy - The field by which to order the results.
 * @param args.options.where - Additional conditions to filter the results.
 * @param args.accountAddress - The address of the account for which to retrieve names.
 *
 * @returns An array of sanitized Aptos names associated with the specified account address.
 * @group Implementation
 */
declare function getAccountNames(args: {
    aptosConfig: AptosConfig;
} & GetAccountNamesArgs): Promise<GetANSNameResponse>;
/**
 * Arguments for retrieving the domains associated with a specific account.
 *
 * @param accountAddress - The address of the account for which to fetch domains.
 * @group Implementation
 */
interface GetAccountDomainsArgs extends QueryNamesOptions {
    accountAddress: AccountAddressInput;
}
/**
 * Retrieves the list of top-level domains owned by a specified account.
 *
 * @param args - The arguments for retrieving account domains.
 * @param args.aptosConfig - The Aptos configuration object.
 * @param args.options - Optional parameters for the query.
 * @param args.options.limit - The maximum number of results to return.
 * @param args.options.offset - The number of results to skip before starting to collect the result set.
 * @param args.options.orderBy - The field by which to order the results.
 * @param args.options.where - Additional conditions to filter the results.
 * @param args.options.where.owner_address - The address of the account whose domains are being queried.
 * @param args.options.where.expiration_timestamp - The minimum expiration timestamp for the domains.
 * @param args.options.where.subdomain - The specific subdomain to filter by.
 *
 * @returns An array of sanitized domain names owned by the specified account.
 * @group Implementation
 */
declare function getAccountDomains(args: {
    aptosConfig: AptosConfig;
} & GetAccountDomainsArgs): Promise<GetANSNameResponse>;
/**
 * Arguments for retrieving subdomains associated with a specific account.
 *
 * @param accountAddress - The address of the account for which to fetch subdomains.
 * @group Implementation
 */
interface GetAccountSubdomainsArgs extends QueryNamesOptions {
    accountAddress: AccountAddressInput;
}
/**
 * Retrieves a list of subdomains owned by a specified account address.
 * This function helps you identify all subdomains associated with a given account.
 *
 * @param args - The arguments for retrieving account subdomains.
 * @param args.aptosConfig - The configuration object for Aptos.
 * @param args.options - Optional parameters for the query.
 * @param args.options.limit - The maximum number of results to return.
 * @param args.options.offset - The number of results to skip before starting to collect the result set.
 * @param args.options.orderBy - The field by which to order the results.
 * @param args.options.where - Additional conditions to filter the results.
 * @param args.options.where.owner_address - The address of the account to filter by.
 * @param args.options.where.expiration_timestamp - The expiration timestamp to filter by.
 * @param args.options.where.subdomain - The subdomain condition to filter by.
 * @param args.accountAddress - The address of the account whose subdomains are being queried.
 * @group Implementation
 */
declare function getAccountSubdomains(args: {
    aptosConfig: AptosConfig;
} & GetAccountSubdomainsArgs): Promise<GetANSNameResponse>;
/**
 * Arguments for retrieving subdomains associated with a specific domain.
 *
 * @param domain - The domain for which to fetch subdomains.
 * @group Implementation
 */
interface GetDomainSubdomainsArgs extends QueryNamesOptions {
    domain: string;
}
/**
 * Retrieve the active subdomains associated with a specified domain.
 *
 * @param args - The arguments for retrieving subdomains.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.options - Optional parameters for the query.
 * @param args.options.limit - The maximum number of results to return.
 * @param args.options.offset - The number of results to skip before starting to collect the results.
 * @param args.options.orderBy - The field by which to order the results.
 * @param args.options.where - Additional conditions to filter the results.
 * @param args.domain - The domain for which to retrieve subdomains.
 *
 * @returns An array of active subdomain names.
 * @group Implementation
 */
declare function getDomainSubdomains(args: {
    aptosConfig: AptosConfig;
} & GetDomainSubdomainsArgs): Promise<GetANSNameResponse>;
/**
 * Renews a domain for a specified duration. This function allows you to extend the registration of a domain for one year.
 *
 * @param args - The parameters required to renew the domain.
 * @param args.aptosConfig - The configuration settings for Aptos.
 * @param args.sender - The account that is sending the renewal transaction.
 * @param args.name - The name of the domain to renew.
 * @param args.years - The number of years to renew the domain for. Currently, only 1 year renewals are supported. (optional, default is 1)
 * @param args.options - Additional options for generating the transaction. (optional)
 * @throws Error if the name contains a subdomain or if the years parameter is not equal to 1.
 * @group Implementation
 */
declare function renewDomain(args: {
    aptosConfig: AptosConfig;
    sender: Account;
    name: string;
    years?: 1;
    options?: InputGenerateTransactionOptions;
}): Promise<SimpleTransaction>;

export { type GetAccountDomainsArgs, type GetAccountNamesArgs, type GetAccountSubdomainsArgs, type GetDomainSubdomainsArgs, LOCAL_ANS_ACCOUNT_ADDRESS, LOCAL_ANS_ACCOUNT_PK, type RegisterNameParameters, SubdomainExpirationPolicy, VALIDATION_RULES_DESCRIPTION, getAccountDomains, getAccountNames, getAccountSubdomains, getDomainSubdomains, getExpiration, getName, getOwnerAddress, getPrimaryName, getTargetAddress, isActiveANSName, isValidANSName, isValidANSSegment, registerName, renewDomain, setPrimaryName, setTargetAddress };
