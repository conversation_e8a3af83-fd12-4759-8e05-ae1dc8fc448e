#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试tokens.json中的代币在以太坊和Polygon网络上是否可以正常交易
筛选出能够在两个网络上都正常执行交易的代币
通过KyberSwap的双向API路由验证（USDT -> Token -> USDT）
支持多线程并发处理，提高效率
"""

import os
import sys
import json
import asyncio
import argparse
import time
import concurrent.futures
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime
from queue import Queue
from threading import Lock

# 添加项目根目录到系统路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
sys.path.append(project_root)

# 导入KyberSwap客户端
from src.dex.KyberSwap.client import KyberSwapClient
from src.dex.KyberSwap.constants import SUPPORTED_CHAINS

# 文件路径
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
TOKENS_PATH = os.path.join(CURRENT_DIR, "tokens.json")
ROUTABLE_TOKENS_PATH = os.path.join(CURRENT_DIR, "routable_tokens_only.json")  # 可路由代币的固定路径
DETAILED_RESULTS_PATH = os.path.join(CURRENT_DIR, "routable_tokens_detailed.json")  # 详细结果的固定路径

# 在以太坊和Polygon上的USDT地址
TARGET_TOKENS = {
    "ethereum": {
        "symbol": "USDT",
        "address": "******************************************"
    },
    "polygon": {
        "symbol": "USDT",
        "address": "******************************************"
    }
}

# 用于模拟交易的虚拟钱包地址 (使用真实的测试地址)
SIMULATION_WALLET = "******************************************"

# 交易测试设置
USDT_TEST_AMOUNT = 10  # 测试10个USDT
MAX_RETRIES = 3  # 重试次数
RETRY_DELAY = 2  # 重试间隔（秒）
DEFAULT_WORKERS = 4  # 默认工作线程数
SLIPPAGE = 0.5  # 0.5%滑点
EXCLUDED_SOURCES = "bebop"  # 排除的流动性来源

# 全局状态变量
stats_lock = Lock()
progress_stats = {
    "processed": 0,
    "ethereum_success": 0,
    "polygon_success": 0,
    "both_success": 0,
    "total": 0,
    "start_time": 0
}

async def test_dual_routes(
    chain: str,
    token_address: str,
    token_decimals: int,
    usdt_amount: float = USDT_TEST_AMOUNT
) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    测试双向API路由：USDT -> Token -> USDT
    只使用get_routes API获取路由信息，不执行实际交易

    Args:
        chain: 链名称 (ethereum 或 polygon)
        token_address: 代币地址
        token_decimals: 代币小数位
        usdt_amount: USDT测试金额

    Returns:
        (是否成功, 错误信息/None, 预期输出金额)
    """
    for retry in range(MAX_RETRIES):
        try:
            if retry > 0:
                print(f"第 {retry+1}/{MAX_RETRIES} 次尝试获取路由...")
                await asyncio.sleep(RETRY_DELAY * (retry + 1))  # 指数退避

            # 创建客户端
            client = KyberSwapClient(chain=chain)

            # 获取USDT地址
            usdt_address = TARGET_TOKENS[chain]["address"]

            # 计算USDT输入金额（考虑6位小数）
            usdt_amount_wei = int(usdt_amount * (10 ** 6))  # USDT固定6位小数

            print(f"\n第一步: 获取 {chain} 上的路由 {usdt_amount} USDT -> Token")

            # 1. 获取第一次交易路由 USDT -> Token
            first_routes = await client.get_routes(
                token_in=usdt_address,
                token_out=token_address,
                amount_in=str(usdt_amount_wei),
                slippage=SLIPPAGE,
                save_gas=False,
                excluded_sources=EXCLUDED_SOURCES,
                use_proxy=True  # 使用代理获取路由
            )

            if "error" in first_routes:
                print(f"❌ 获取第一次路由失败: {first_routes['error']}")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, f"获取第一次路由失败: {first_routes['error']}", None

            # 检查路由数据结构
            if "data" not in first_routes or "routeSummary" not in first_routes["data"]:
                print(f"❌ 第一次路由数据结构异常")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, "第一次路由数据结构异常", None

            first_route_summary = first_routes["data"]["routeSummary"]
            expected_token_out = first_route_summary.get("amountOut", "0")

            if not expected_token_out or int(expected_token_out) <= 0:
                print(f"❌ USDT -> Token 预期输出金额无效: {expected_token_out}")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, "USDT -> Token 预期输出金额无效", None

            print(f"📊 第一次路由信息:")
            print(f"   预期Token输出: {expected_token_out}")
            print(f"   Gas预估: {first_route_summary.get('gas', 'N/A')}")
            print(f"   价格影响: {first_route_summary.get('priceImpact', 'N/A')}%")

            print(f"\n第二步: 获取 {chain} 上的路由 Token -> USDT")

            # 2. 获取第二次交易路由 Token -> USDT
            second_routes = await client.get_routes(
                token_in=token_address,
                token_out=usdt_address,
                amount_in=expected_token_out,
                slippage=SLIPPAGE,
                save_gas=False,
                excluded_sources=EXCLUDED_SOURCES,
                use_proxy=True  # 使用代理获取路由
            )

            if "error" in second_routes:
                print(f"❌ 获取第二次路由失败: {second_routes['error']}")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, f"获取第二次路由失败: {second_routes['error']}", None

            # 检查路由数据结构
            if "data" not in second_routes or "routeSummary" not in second_routes["data"]:
                print(f"❌ 第二次路由数据结构异常")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, "第二次路由数据结构异常", None

            second_route_summary = second_routes["data"]["routeSummary"]
            expected_usdt_out = second_route_summary.get("amountOut", "0")

            if not expected_usdt_out or int(expected_usdt_out) <= 0:
                print(f"❌ Token -> USDT 预期输出金额无效: {expected_usdt_out}")
                if retry < MAX_RETRIES - 1:
                    continue
                return False, "Token -> USDT 预期输出金额无效", None

            print(f"📊 第二次路由信息:")
            print(f"   预期USDT返回: {int(expected_usdt_out) / 10**6}")
            print(f"   Gas预估: {second_route_summary.get('gas', 'N/A')}")
            print(f"   价格影响: {second_route_summary.get('priceImpact', 'N/A')}%")

            # 计算总体信息
            total_gas = int(first_route_summary.get('gas', 0)) + int(second_route_summary.get('gas', 0))
            total_price_impact = float(first_route_summary.get('priceImpact', 0)) + float(second_route_summary.get('priceImpact', 0))
            final_usdt_amount = int(expected_usdt_out) / 10**6

            print(f"\n✅ 双向路由测试成功:")
            print(f"   输入: {usdt_amount} USDT")
            print(f"   预期返回: {final_usdt_amount} USDT")
            print(f"   总Gas预估: {total_gas}")
            print(f"   总价格影响: {total_price_impact}%")
            print(f"   净损失: {usdt_amount - final_usdt_amount} USDT ({((usdt_amount - final_usdt_amount) / usdt_amount * 100):.2f}%)")

            return True, None, expected_usdt_out

        except Exception as e:
            print(f"❌ 获取路由时出错: {str(e)}")
            if retry < MAX_RETRIES - 1:
                continue
            return False, str(e), None

    return False, "达到最大重试次数", None

def process_token(token_symbol: str, token_data: Dict, queue: Queue) -> Dict:
    """
    处理单个代币的模拟交易测试
    优化：如果一个网络测试失败，不再测试另一个网络
    
    Args:
        token_symbol: 代币符号
        token_data: 代币数据
        queue: 用于更新进度的队列
        
    Returns:
        处理结果
    """
    result = {
        "token_symbol": token_symbol,
        "ethereum_tradeable": False,
        "polygon_tradeable": False,
        "status": "failed",
        "error": None,
        "ethereum_output": None,
        "polygon_output": None
    }
    
    print(f"\n测试代币: {token_symbol}")
    
    try:
        # 首先检查以太坊网络
        if "1" in token_data:
            eth_address = token_data["1"]["address"]
            eth_decimals = token_data["1"].get("decimals", 18)
            
            print(f"测试以太坊上的 {token_symbol}: {eth_address}")
            
            # 使用asyncio运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            eth_success, eth_error, eth_output = loop.run_until_complete(
                test_dual_routes("ethereum", eth_address, eth_decimals, USDT_TEST_AMOUNT)
            )
            loop.close()
            
            if eth_success:
                result["ethereum_tradeable"] = True
                result["ethereum_output"] = eth_output
            else:
                print(f"❌ 以太坊上的 {token_symbol} 模拟交易失败")
                result["status"] = "failed"
                result["error"] = f"以太坊交易失败: {eth_error}"
                # 如果以太坊网络测试失败，直接返回结果不再测试Polygon
                queue.put(("update", {
                    "ethereum_result": False
                }))
                return result
            
            queue.put(("update", {
                "ethereum_result": True
            }))
        else:
            print(f"跳过以太坊上的 {token_symbol} (无数据)")
            result["status"] = "skipped_eth"
            queue.put(("update", {
                "ethereum_result": False
            }))
            return result
        
        # 然后检查Polygon网络
        if "137" in token_data:
            pol_address = token_data["137"]["address"]
            pol_decimals = token_data["137"].get("decimals", 18)
            
            print(f"测试Polygon上的 {token_symbol}: {pol_address}")
            
            # 使用asyncio运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            pol_success, pol_error, pol_output = loop.run_until_complete(
                test_dual_routes("polygon", pol_address, pol_decimals, USDT_TEST_AMOUNT)
            )
            loop.close()
            
            if pol_success:
                result["polygon_tradeable"] = True
                result["polygon_output"] = pol_output
            else:
                print(f"❌ Polygon上的 {token_symbol} 模拟交易失败")
                result["status"] = "failed"
                result["error"] = f"Polygon交易失败: {pol_error}"
                queue.put(("update", {
                    "polygon_result": False
                }))
                return result
            
            queue.put(("update", {
                "polygon_result": True
            }))
        else:
            print(f"跳过Polygon上的 {token_symbol} (无数据)")
            result["status"] = "skipped_pol"
            queue.put(("update", {
                "polygon_result": False
            }))
            return result
        
        # 最终状态判断
        if result["ethereum_tradeable"] and result["polygon_tradeable"]:
            print(f"✅ {token_symbol} 可以在两个网络上成功交易")
            result["status"] = "success"
            queue.put(("update", {
                "both_result": True
            }))
        else:
            result["status"] = "failed"
            queue.put(("update", {
                "both_result": False
            }))
        
        return result
        
    except Exception as e:
        print(f"处理代币 {token_symbol} 时出错: {str(e)}")
        result["status"] = "error"
        result["error"] = str(e)
        return result
    finally:
        # 更新处理计数
        queue.put(("processed", None))

def update_progress(queue: Queue):
    """处理进度更新"""
    while True:
        try:
            item = queue.get()
            if item is None:  # 结束信号
                break
                
            action, data = item
            
            with stats_lock:
                if action == "processed":
                    progress_stats["processed"] += 1
                elif action == "update":
                    if "ethereum_result" in data and data["ethereum_result"]:
                        progress_stats["ethereum_success"] += 1
                    if "polygon_result" in data and data["polygon_result"]:
                        progress_stats["polygon_success"] += 1
                    if "both_result" in data and data["both_result"]:
                        progress_stats["both_success"] += 1
                
                # 显示进度
                processed = progress_stats["processed"]
                total = progress_stats["total"]
                eth_success = progress_stats["ethereum_success"]
                pol_success = progress_stats["polygon_success"]
                both_success = progress_stats["both_success"]
                
                # 计算进度
                elapsed = time.time() - progress_stats["start_time"]
                tokens_per_second = processed / elapsed if elapsed > 0 else 0
                remaining = (total - processed) / tokens_per_second if tokens_per_second > 0 else 0
                
                print(f"\n进度: {processed}/{total} ({processed/total*100:.1f}%)")
                print(f"成功率: 以太坊 {eth_success}/{processed}, Polygon {pol_success}/{processed}, 两网络都成功 {both_success}/{processed}")
                print(f"耗时: {elapsed:.1f}秒, 速率: {tokens_per_second:.2f}个/秒, 预计剩余时间: {remaining:.1f}秒")
            
            queue.task_done()
        except Exception as e:
            print(f"更新进度时出错: {e}")

def test_tokens_mt(tokens: Dict, num_workers: int = DEFAULT_WORKERS):
    """使用多线程测试所有代币"""
    print(f"使用 {num_workers} 个工作线程处理 {len(tokens)} 个代币")
    
    # 初始化结果
    routable_tokens = {}
    failed_tokens = {}
    
    # 创建进度更新队列
    progress_queue = Queue()
    
    # 初始化进度统计
    with stats_lock:
        progress_stats["processed"] = 0
        progress_stats["ethereum_success"] = 0
        progress_stats["polygon_success"] = 0
        progress_stats["both_success"] = 0
        progress_stats["total"] = len(tokens)
        progress_stats["start_time"] = time.time()
    
    # 启动进度更新线程
    import threading
    progress_thread = threading.Thread(target=update_progress, args=(progress_queue,))
    progress_thread.daemon = True
    progress_thread.start()
    
    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
        # 提交任务
        future_to_token = {
            executor.submit(process_token, token_symbol, token_data, progress_queue): (token_symbol, token_data)
            for token_symbol, token_data in tokens.items()
        }
        
        # 处理结果
        for future in concurrent.futures.as_completed(future_to_token):
            token_symbol, token_data = future_to_token[future]
            try:
                result = future.result()
                
                # 根据结果分类
                if result["status"] == "success":
                    routable_tokens[token_symbol] = token_data
                else:
                    failed_reason = {
                        "ethereum": result["ethereum_tradeable"],
                        "polygon": result["polygon_tradeable"],
                        "error": result["error"],
                        "status": result["status"]
                    }
                    failed_tokens[token_symbol] = {
                        "token_data": token_data,
                        "trading_status": failed_reason
                    }
                
            except Exception as e:
                print(f"处理 {token_symbol} 结果时出错: {e}")
                failed_tokens[token_symbol] = {
                    "token_data": token_data,
                    "trading_status": {
                        "ethereum": False,
                        "polygon": False,
                        "error": str(e),
                        "status": "error"
                    }
                }
    
    # 发送结束信号给进度线程
    progress_queue.put(None)
    progress_thread.join()
    
    return routable_tokens, failed_tokens

def main():
    """命令行入口点"""
    global USDT_TEST_AMOUNT  # 移到函数开始处
    
    parser = argparse.ArgumentParser(description="测试代币交易")
    parser.add_argument("--token-count", type=int, help="要处理的代币数量")
    parser.add_argument("--start-index", type=int, default=0, help="开始处理的代币索引")
    parser.add_argument("--workers", type=int, default=DEFAULT_WORKERS, help="并发工作线程数")
    parser.add_argument("--append", action="store_true", help="是否追加到现有的routable_tokens_only.json文件")
    parser.add_argument("--amount", type=float, default=USDT_TEST_AMOUNT, help="USDT测试金额")
    
    args = parser.parse_args()
    
    try:
        # 加载tokens.json
        print(f"从 {TOKENS_PATH} 加载代币数据...")
        with open(TOKENS_PATH, 'r', encoding='utf-8') as f:
            tokens = json.load(f)
        
        print(f"成功加载 {len(tokens)} 个代币")
        
        # 处理代币数量限制
        if args.token_count is not None:
            token_items = list(tokens.items())[args.start_index:args.start_index + args.token_count]
            tokens = dict(token_items)
            print(f"将处理从索引 {args.start_index} 开始的 {len(tokens)} 个代币")
        elif args.start_index > 0:
            token_items = list(tokens.items())[args.start_index:]
            tokens = dict(token_items)
            print(f"将从索引 {args.start_index} 开始处理剩余的 {len(tokens)} 个代币")
        
        # 更新测试金额
        USDT_TEST_AMOUNT = args.amount
        print(f"使用 {USDT_TEST_AMOUNT} USDT作为测试金额")
        
        # 使用多线程测试代币
        routable_tokens, failed_tokens = test_tokens_mt(tokens, args.workers)
        
        # 保存详细结果
        output_data = {
            "metadata": {
                "total_tested": len(tokens),
                "ethereum_success": progress_stats["ethereum_success"],
                "polygon_success": progress_stats["polygon_success"],
                "both_success": progress_stats["both_success"],
                "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "test_amount": USDT_TEST_AMOUNT,
                "workers": args.workers,
                "start_index": args.start_index,
                "token_count": args.token_count
            },
            "routable_tokens": routable_tokens,
            "failed_tokens": failed_tokens
        }
        
        # 保存详细结果到固定路径
        with open(DETAILED_RESULTS_PATH, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n详细测试结果已保存到 {DETAILED_RESULTS_PATH}")
        
        # 处理可交易代币的保存
        if args.append and os.path.exists(ROUTABLE_TOKENS_PATH):
            # 如果指定追加模式且文件存在，则加载现有文件
            try:
                with open(ROUTABLE_TOKENS_PATH, 'r', encoding='utf-8') as f:
                    existing_tokens = json.load(f)
                # 合并新的可交易代币
                existing_tokens.update(routable_tokens)
                routable_tokens = existing_tokens
                print(f"已将新的可交易代币追加到现有文件中")
            except Exception as e:
                print(f"读取现有文件失败，将创建新文件: {str(e)}")
        
        # 保存可交易代币到固定路径
        with open(ROUTABLE_TOKENS_PATH, 'w', encoding='utf-8') as f:
            json.dump(routable_tokens, f, indent=2, ensure_ascii=False)
        
        print(f"可交易代币已保存到固定路径: {ROUTABLE_TOKENS_PATH}")
        print(f"总测试: {len(tokens)} 个代币")
        print(f"以太坊成功: {progress_stats['ethereum_success']} 个")
        print(f"Polygon成功: {progress_stats['polygon_success']} 个")
        print(f"两网络都成功: {progress_stats['both_success']} 个")
        print(f"可交易代币总数: {len(routable_tokens)} 个")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 