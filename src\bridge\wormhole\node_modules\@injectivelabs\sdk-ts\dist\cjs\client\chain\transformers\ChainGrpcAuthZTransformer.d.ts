import { CosmosAuthzV1Beta1Authz, CosmosAuthzV1Beta1Query } from '@injectivelabs/core-proto-ts';
import { GrantAuthorizationWithDecodedAuthorization, GrantWithDecodedAuthorization } from '../types/index.js';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcAuthZTransformer {
    static grpcGrantToGrant(grant: CosmosAuthzV1Beta1Authz.Grant): GrantWithDecodedAuthorization;
    static grpcGrantAuthorizationToGrantAuthorization(grant: CosmosAuthzV1Beta1Authz.GrantAuthorization): GrantAuthorizationWithDecodedAuthorization;
    static grpcGrantsToGrants(response: CosmosAuthzV1Beta1Query.QueryGrantsResponse): {
        pagination: import("../../../index.js").Pagination;
        grants: GrantWithDecodedAuthorization[];
    };
    static grpcGranteeGrantsToGranteeGrants(response: CosmosAuthzV1Beta1Query.QueryGranteeGrantsResponse): {
        pagination: import("../../../index.js").Pagination;
        grants: GrantAuthorizationWithDecodedAuthorization[];
    };
    static grpcGranterGrantsToGranterGrants(response: CosmosAuthzV1Beta1Query.QueryGranterGrantsResponse): {
        pagination: import("../../../index.js").Pagination;
        grants: GrantAuthorizationWithDecodedAuthorization[];
    };
}
