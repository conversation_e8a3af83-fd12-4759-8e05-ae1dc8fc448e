"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const grpc_js_1 = require("../../utils/grpc.js");
const IndexerGrpcWebImpl_js_1 = require("./IndexerGrpcWebImpl.js");
class BaseIndexerGrpcConsumer extends IndexerGrpcWebImpl_js_1.GrpcWebImpl {
    module = '';
    metadata;
    constructor(endpoint) {
        super(endpoint, {
            transport: (0, grpc_js_1.getGrpcTransport)(),
        });
    }
    getGrpcWebImpl(endpoint) {
        return new BaseIndexerGrpcConsumer(endpoint);
    }
    setMetadata(map) {
        const metadata = new grpc_js_1.grpc.Metadata();
        Object.keys(map).forEach((key) => metadata.set(key, map[key]));
        this.metadata = metadata;
        return this;
    }
    clearMetadata() {
        this.metadata = undefined;
    }
    retry(grpcCall, retries = 3, delay = 1000) {
        const retryGrpcCall = async (attempt = 1) => {
            try {
                return await grpcCall();
            }
            catch (e) {
                if (attempt >= retries) {
                    throw e;
                }
                return new Promise((resolve) => setTimeout(() => resolve(retryGrpcCall(attempt + 1)), delay * attempt));
            }
        };
        return retryGrpcCall();
    }
}
exports.default = BaseIndexerGrpcConsumer;
