import { InjectiveAbacusRpc } from '@injectivelabs/abacus-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
export declare class AbacusGrpcApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveAbacusRpc.PointsSvcClientImpl;
    constructor(endpoint: string);
    fetchAccountLatestPoints(address: string): Promise<InjectiveAbacusRpc.PointsLatestForAccountResponse>;
    fetchAccountDailyPoints(address: string, daysLimit?: number): Promise<InjectiveAbacusRpc.HistoricalPointsStatsRow[]>;
    fetchAccountWeeklyPoints(address: string, weeksLimit?: number): Promise<InjectiveAbacusRpc.HistoricalPointsStatsRow[]>;
}
