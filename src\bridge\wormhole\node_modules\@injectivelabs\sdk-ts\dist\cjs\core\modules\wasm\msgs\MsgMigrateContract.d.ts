import { MsgBase } from '../../MsgBase.js';
import { CosmwasmWasmV1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgMigrateContract {
    interface Params {
        sender: string;
        contract: string;
        codeId: number;
        msg: object;
    }
    type Proto = CosmwasmWasmV1Tx.MsgMigrateContract;
    type Object = Omit<CosmwasmWasmV1Tx.MsgMigrateContract, 'msg'> & {
        msg: any;
    };
}
/**
 * @category Messages
 */
export default class MsgMigrateContract extends MsgBase<MsgMigrateContract.Params, MsgMigrateContract.Proto, MsgMigrateContract.Object> {
    static fromJSON(params: MsgMigrateContract.Params): MsgMigrateContract;
    toProto(): CosmwasmWasmV1Tx.MsgMigrateContract;
    toData(): {
        sender: string;
        contract: string;
        codeId: string;
        msg: Uint8Array;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: MsgMigrateContract.Object;
    };
    toWeb3Gw(): {
        sender: string;
        contract: string;
        codeId: string;
        msg: any;
        '@type': string;
    };
    toEip712(): never;
    toDirectSign(): {
        type: string;
        message: CosmwasmWasmV1Tx.MsgMigrateContract;
    };
    toBinary(): Uint8Array;
}
