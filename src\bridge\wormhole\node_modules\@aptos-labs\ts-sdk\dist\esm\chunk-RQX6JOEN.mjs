import{b as o}from"./chunk-WSR5EBJM.mjs";import{a as s}from"./chunk-WCMW2L3P.mjs";import{a as t}from"./chunk-FGFLPH5K.mjs";import{b as i}from"./chunk-STY74NUA.mjs";var n=class e extends s{constructor(r){super(),this.value=i.fromHexInput(r).toUint8Array()}serialize(r){r.serializeBytes(this.value)}static deserialize(r){return new e(r.deserializeBytes())}},a=class extends o{constructor(r){super(),this.accountAddress=r}authKey(){return new t({data:this.accountAddress.toUint8Array()})}verifySignature(r){throw new Error("This function is not implemented for AbstractPublicKey.")}async verifySignatureAsync(r){throw new Error("This function is not implemented for AbstractPublicKey.")}serialize(r){throw new Error("This function is not implemented for AbstractPublicKey.")}};export{n as a,a as b};
//# sourceMappingURL=chunk-RQX6JOEN.mjs.map