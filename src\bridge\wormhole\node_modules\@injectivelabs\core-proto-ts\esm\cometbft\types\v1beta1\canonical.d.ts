import _m0 from "protobufjs/minimal.js";
import { SignedMsgType } from "./types";
export declare const protobufPackage = "cometbft.types.v1beta1";
/**
 * CanonicalBlockID is a canonical representation of a BlockID, which gets
 * serialized and signed.
 */
export interface CanonicalBlockID {
    hash: Uint8Array;
    partSetHeader: CanonicalPartSetHeader | undefined;
}
/**
 * CanonicalPartSetHeader is a canonical representation of a PartSetHeader,
 * which gets serialized and signed.
 */
export interface CanonicalPartSetHeader {
    total: number;
    hash: Uint8Array;
}
/**
 * CanonicalProposal is a canonical representation of a Proposal, which gets
 * serialized and signed.
 */
export interface CanonicalProposal {
    /** type alias for byte */
    type: SignedMsgType;
    /** canonicalization requires fixed size encoding here */
    height: string;
    /** canonicalization requires fixed size encoding here */
    round: string;
    polRound: string;
    blockId: CanonicalBlockID | undefined;
    timestamp: Date | undefined;
    chainId: string;
}
/**
 * CanonicalVote is a canonical representation of a Vote, which gets
 * serialized and signed.
 */
export interface CanonicalVote {
    /** type alias for byte */
    type: SignedMsgType;
    /** canonicalization requires fixed size encoding here */
    height: string;
    /** canonicalization requires fixed size encoding here */
    round: string;
    blockId: CanonicalBlockID | undefined;
    timestamp: Date | undefined;
    chainId: string;
}
export declare const CanonicalBlockID: {
    encode(message: CanonicalBlockID, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CanonicalBlockID;
    fromJSON(object: any): CanonicalBlockID;
    toJSON(message: CanonicalBlockID): unknown;
    create(base?: DeepPartial<CanonicalBlockID>): CanonicalBlockID;
    fromPartial(object: DeepPartial<CanonicalBlockID>): CanonicalBlockID;
};
export declare const CanonicalPartSetHeader: {
    encode(message: CanonicalPartSetHeader, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CanonicalPartSetHeader;
    fromJSON(object: any): CanonicalPartSetHeader;
    toJSON(message: CanonicalPartSetHeader): unknown;
    create(base?: DeepPartial<CanonicalPartSetHeader>): CanonicalPartSetHeader;
    fromPartial(object: DeepPartial<CanonicalPartSetHeader>): CanonicalPartSetHeader;
};
export declare const CanonicalProposal: {
    encode(message: CanonicalProposal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CanonicalProposal;
    fromJSON(object: any): CanonicalProposal;
    toJSON(message: CanonicalProposal): unknown;
    create(base?: DeepPartial<CanonicalProposal>): CanonicalProposal;
    fromPartial(object: DeepPartial<CanonicalProposal>): CanonicalProposal;
};
export declare const CanonicalVote: {
    encode(message: CanonicalVote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CanonicalVote;
    fromJSON(object: any): CanonicalVote;
    toJSON(message: CanonicalVote): unknown;
    create(base?: DeepPartial<CanonicalVote>): CanonicalVote;
    fromPartial(object: DeepPartial<CanonicalVote>): CanonicalVote;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
