{"version": 3, "sources": ["../../src/core/crypto/signature.ts"], "sourcesContent": ["import { Serializable } from \"../../bcs\";\nimport { Hex } from \"../hex\";\n\n/**\n * An abstract representation of a crypto signature,\n * associated with a specific signature scheme, e.g., Ed25519 or Secp256k1.\n *\n * This class represents the product of signing a message directly from a\n * PrivateKey and can be verified against a CryptoPublicKey.\n * @group Implementation\n * @category Serialization\n */\nexport abstract class Signature extends Serializable {\n  /**\n   * Get the raw signature bytes\n   * @group Implementation\n   * @category Serialization\n   */\n  toUint8Array(): Uint8Array {\n    return this.bcsToBytes();\n  }\n\n  /**\n   * Get the signature as a hex string with a 0x prefix e.g. 0x123456...\n   * @returns The hex string representation of the signature.\n   * @group Implementation\n   * @category Serialization\n   */\n  toString(): string {\n    const bytes = this.toUint8Array();\n    return Hex.fromHexInput(bytes).toString();\n  }\n}\n\n/**\n * An abstract representation of an account signature,\n * associated to a specific authentication scheme e.g. Ed25519 or <PERSON><PERSON>ey\n *\n * This is the product of signing a message through an account,\n * and can be verified against an AccountPublicKey.\n * @group Implementation\n * @category Serialization\n */\n// export abstract class AccountSignature extends Serializable {\n//   /**\n//    * Get the raw signature bytes\n//    */\n//   abstract toUint8Array(): Uint8Array;\n//\n//   /**\n//    * Get the signature as a hex string with a 0x prefix e.g. 0x123456...\n//    */\n//   toString(): string {\n//     const bytes = this.toUint8Array();\n//     return Hex.fromHexInput(bytes).toString();\n//   }\n// }\n"], "mappings": "kFAYO,IAAeA,EAAf,cAAiCC,CAAa,CAMnD,cAA2B,CACzB,OAAO,KAAK,WAAW,CACzB,CAQA,UAAmB,CACjB,IAAMC,EAAQ,KAAK,aAAa,EAChC,OAAOC,EAAI,aAAaD,CAAK,EAAE,SAAS,CAC1C,CACF", "names": ["Signature", "Serializable", "bytes", "Hex"]}