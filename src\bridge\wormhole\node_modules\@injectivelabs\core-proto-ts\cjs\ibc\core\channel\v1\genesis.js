"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PacketSequence = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var channel_1 = require("./channel.js");
exports.protobufPackage = "ibc.core.channel.v1";
function createBaseGenesisState() {
    return {
        channels: [],
        acknowledgements: [],
        commitments: [],
        receipts: [],
        sendSequences: [],
        recvSequences: [],
        ackSequences: [],
        nextChannelSequence: "0",
        params: undefined,
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _h = __values(message.channels), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                channel_1.IdentifiedChannel.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_a = _h.return)) _a.call(_h);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _k = __values(message.acknowledgements), _l = _k.next(); !_l.done; _l = _k.next()) {
                var v = _l.value;
                channel_1.PacketState.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_l && !_l.done && (_b = _k.return)) _b.call(_k);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _m = __values(message.commitments), _o = _m.next(); !_o.done; _o = _m.next()) {
                var v = _o.value;
                channel_1.PacketState.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_o && !_o.done && (_c = _m.return)) _c.call(_m);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _p = __values(message.receipts), _q = _p.next(); !_q.done; _q = _p.next()) {
                var v = _q.value;
                channel_1.PacketState.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_q && !_q.done && (_d = _p.return)) _d.call(_p);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _r = __values(message.sendSequences), _s = _r.next(); !_s.done; _s = _r.next()) {
                var v = _s.value;
                exports.PacketSequence.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_s && !_s.done && (_e = _r.return)) _e.call(_r);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _t = __values(message.recvSequences), _u = _t.next(); !_u.done; _u = _t.next()) {
                var v = _u.value;
                exports.PacketSequence.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_u && !_u.done && (_f = _t.return)) _f.call(_t);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _v = __values(message.ackSequences), _w = _v.next(); !_w.done; _w = _v.next()) {
                var v = _w.value;
                exports.PacketSequence.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_w && !_w.done && (_g = _v.return)) _g.call(_v);
            }
            finally { if (e_7) throw e_7.error; }
        }
        if (message.nextChannelSequence !== "0") {
            writer.uint32(64).uint64(message.nextChannelSequence);
        }
        if (message.params !== undefined) {
            channel_1.Params.encode(message.params, writer.uint32(74).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.channels.push(channel_1.IdentifiedChannel.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.acknowledgements.push(channel_1.PacketState.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.commitments.push(channel_1.PacketState.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.receipts.push(channel_1.PacketState.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.sendSequences.push(exports.PacketSequence.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.recvSequences.push(exports.PacketSequence.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.ackSequences.push(exports.PacketSequence.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.nextChannelSequence = longToString(reader.uint64());
                    break;
                case 9:
                    message.params = channel_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            channels: Array.isArray(object === null || object === void 0 ? void 0 : object.channels) ? object.channels.map(function (e) { return channel_1.IdentifiedChannel.fromJSON(e); }) : [],
            acknowledgements: Array.isArray(object === null || object === void 0 ? void 0 : object.acknowledgements)
                ? object.acknowledgements.map(function (e) { return channel_1.PacketState.fromJSON(e); })
                : [],
            commitments: Array.isArray(object === null || object === void 0 ? void 0 : object.commitments)
                ? object.commitments.map(function (e) { return channel_1.PacketState.fromJSON(e); })
                : [],
            receipts: Array.isArray(object === null || object === void 0 ? void 0 : object.receipts) ? object.receipts.map(function (e) { return channel_1.PacketState.fromJSON(e); }) : [],
            sendSequences: Array.isArray(object === null || object === void 0 ? void 0 : object.sendSequences)
                ? object.sendSequences.map(function (e) { return exports.PacketSequence.fromJSON(e); })
                : [],
            recvSequences: Array.isArray(object === null || object === void 0 ? void 0 : object.recvSequences)
                ? object.recvSequences.map(function (e) { return exports.PacketSequence.fromJSON(e); })
                : [],
            ackSequences: Array.isArray(object === null || object === void 0 ? void 0 : object.ackSequences)
                ? object.ackSequences.map(function (e) { return exports.PacketSequence.fromJSON(e); })
                : [],
            nextChannelSequence: isSet(object.nextChannelSequence) ? String(object.nextChannelSequence) : "0",
            params: isSet(object.params) ? channel_1.Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.channels) {
            obj.channels = message.channels.map(function (e) { return e ? channel_1.IdentifiedChannel.toJSON(e) : undefined; });
        }
        else {
            obj.channels = [];
        }
        if (message.acknowledgements) {
            obj.acknowledgements = message.acknowledgements.map(function (e) { return e ? channel_1.PacketState.toJSON(e) : undefined; });
        }
        else {
            obj.acknowledgements = [];
        }
        if (message.commitments) {
            obj.commitments = message.commitments.map(function (e) { return e ? channel_1.PacketState.toJSON(e) : undefined; });
        }
        else {
            obj.commitments = [];
        }
        if (message.receipts) {
            obj.receipts = message.receipts.map(function (e) { return e ? channel_1.PacketState.toJSON(e) : undefined; });
        }
        else {
            obj.receipts = [];
        }
        if (message.sendSequences) {
            obj.sendSequences = message.sendSequences.map(function (e) { return e ? exports.PacketSequence.toJSON(e) : undefined; });
        }
        else {
            obj.sendSequences = [];
        }
        if (message.recvSequences) {
            obj.recvSequences = message.recvSequences.map(function (e) { return e ? exports.PacketSequence.toJSON(e) : undefined; });
        }
        else {
            obj.recvSequences = [];
        }
        if (message.ackSequences) {
            obj.ackSequences = message.ackSequences.map(function (e) { return e ? exports.PacketSequence.toJSON(e) : undefined; });
        }
        else {
            obj.ackSequences = [];
        }
        message.nextChannelSequence !== undefined && (obj.nextChannelSequence = message.nextChannelSequence);
        message.params !== undefined && (obj.params = message.params ? channel_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseGenesisState();
        message.channels = ((_a = object.channels) === null || _a === void 0 ? void 0 : _a.map(function (e) { return channel_1.IdentifiedChannel.fromPartial(e); })) || [];
        message.acknowledgements = ((_b = object.acknowledgements) === null || _b === void 0 ? void 0 : _b.map(function (e) { return channel_1.PacketState.fromPartial(e); })) || [];
        message.commitments = ((_c = object.commitments) === null || _c === void 0 ? void 0 : _c.map(function (e) { return channel_1.PacketState.fromPartial(e); })) || [];
        message.receipts = ((_d = object.receipts) === null || _d === void 0 ? void 0 : _d.map(function (e) { return channel_1.PacketState.fromPartial(e); })) || [];
        message.sendSequences = ((_e = object.sendSequences) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.PacketSequence.fromPartial(e); })) || [];
        message.recvSequences = ((_f = object.recvSequences) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.PacketSequence.fromPartial(e); })) || [];
        message.ackSequences = ((_g = object.ackSequences) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.PacketSequence.fromPartial(e); })) || [];
        message.nextChannelSequence = (_h = object.nextChannelSequence) !== null && _h !== void 0 ? _h : "0";
        message.params = (object.params !== undefined && object.params !== null)
            ? channel_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBasePacketSequence() {
    return { portId: "", channelId: "", sequence: "0" };
}
exports.PacketSequence = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.portId !== "") {
            writer.uint32(10).string(message.portId);
        }
        if (message.channelId !== "") {
            writer.uint32(18).string(message.channelId);
        }
        if (message.sequence !== "0") {
            writer.uint32(24).uint64(message.sequence);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePacketSequence();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.portId = reader.string();
                    break;
                case 2:
                    message.channelId = reader.string();
                    break;
                case 3:
                    message.sequence = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            portId: isSet(object.portId) ? String(object.portId) : "",
            channelId: isSet(object.channelId) ? String(object.channelId) : "",
            sequence: isSet(object.sequence) ? String(object.sequence) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.portId !== undefined && (obj.portId = message.portId);
        message.channelId !== undefined && (obj.channelId = message.channelId);
        message.sequence !== undefined && (obj.sequence = message.sequence);
        return obj;
    },
    create: function (base) {
        return exports.PacketSequence.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBasePacketSequence();
        message.portId = (_a = object.portId) !== null && _a !== void 0 ? _a : "";
        message.channelId = (_b = object.channelId) !== null && _b !== void 0 ? _b : "";
        message.sequence = (_c = object.sequence) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
