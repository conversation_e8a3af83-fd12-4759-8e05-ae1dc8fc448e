#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
查询Gate.io交易所订单簿的脚本
可以作为独立脚本运行，也可以被其他脚本导入调用

用法：
1. 作为脚本运行：
   python gate_order_book.py BTC/USDT -d 5

2. 作为模块导入：
   from scripts.cex.gate.gate_order_book import get_order_book
   order_book = get_order_book("BTC/USDT", depth=5)
"""

import sys
import os
import json
import argparse
from datetime import datetime
import pandas as pd
import traceback

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

try:
    from src.cex.gate.client import GateClient
    from src.utils.logger import logger
except ImportError:
    import logging
    # 如果无法导入logger，创建一个基本的logger
    logger = logging.getLogger("gate_order_book")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.warning("无法导入必要的模块，请确保已设置正确的PYTHONPATH")
    sys.exit(1)

# 设置默认输出目录
DEFAULT_OUTPUT_DIR = os.path.join(project_root, 'data', 'cex', 'gate_info')

def get_order_book(symbol, depth=10, raw=False):
    """
    获取代币订单簿数据的API函数，方便其他脚本直接调用
    
    Args:
        symbol (str): 交易对，如BTC/USDT
        depth (int): 订单深度
        raw (bool): 是否返回原始数据，默认False表示返回处理后的数据
        
    Returns:
        dict: 订单簿数据
    """
    try:
        client = GateClient()
        logger.info(f"获取 {symbol} 的订单簿数据，深度: {depth}")
        
        # 获取订单簿数据
        order_book = client.get_order_book(symbol, limit=depth)
        
        # 调试输出原始数据
        logger.debug(f"原始订单簿数据: {order_book}")
        
        # 如果需要原始数据，直接返回
        if raw:
            return order_book
        
        # 返回格式化后的订单簿
        return format_order_book(order_book, depth=depth)
    except Exception as e:
        logger.error(f"获取订单簿时出错: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e)}

def format_order_book(order_book, depth=10):
    """
    格式化订单簿数据
    
    Args:
        order_book: 原始订单簿数据
        depth: 显示深度
        
    Returns:
        dict: 格式化后的订单簿数据
    """
    try:
        # 确保深度不超过订单簿中的数据量
        asks_depth = min(depth, len(order_book.get('asks', [])))
        bids_depth = min(depth, len(order_book.get('bids', [])))
        
        # 提取指定深度的卖单（升序）
        asks = []
        for i in range(asks_depth):
            if i < len(order_book.get('asks', [])):
                price, amount = order_book['asks'][i]
                asks.append({
                    'price': float(price),
                    'amount': float(amount),
                    'total': float(price) * float(amount)
                })
        
        # 提取指定深度的买单（降序）
        bids = []
        for i in range(bids_depth):
            if i < len(order_book.get('bids', [])):
                price, amount = order_book['bids'][i]
                bids.append({
                    'price': float(price),
                    'amount': float(amount),
                    'total': float(price) * float(amount)
                })
        
        # 计算买卖盘的累计数量和总值
        ask_total_amount = sum(ask['amount'] for ask in asks)
        ask_total_value = sum(ask['total'] for ask in asks)
        bid_total_amount = sum(bid['amount'] for bid in bids)
        bid_total_value = sum(bid['total'] for bid in bids)
        
        # 返回格式化的订单簿
        return {
            'symbol': order_book.get('symbol', ''),
            'timestamp': order_book.get('timestamp', 0),
            'datetime': order_book.get('datetime', ''),
            'asks': asks,  # 卖单
            'bids': bids,  # 买单
            'ask_total_amount': ask_total_amount,  # 卖单总数量
            'ask_total_value': ask_total_value,  # 卖单总价值
            'bid_total_amount': bid_total_amount,  # 买单总数量
            'bid_total_value': bid_total_value,  # 买单总价值
            'spread': asks[0]['price'] - bids[0]['price'] if asks and bids else 0,  # 买卖价差
            'spread_percentage': (asks[0]['price'] / bids[0]['price'] - 1) * 100 if asks and bids else 0  # 价差百分比
        }
    except Exception as e:
        logger.error(f"格式化订单簿数据时出错: {e}")
        logger.error(traceback.format_exc())
        return {
            'symbol': order_book.get('symbol', ''),
            'timestamp': order_book.get('timestamp', 0),
            'datetime': order_book.get('datetime', ''),
            'asks': [],
            'bids': [],
            'ask_total_amount': 0,
            'ask_total_value': 0,
            'bid_total_amount': 0,
            'bid_total_value': 0,
            'spread': 0,
            'spread_percentage': 0
        }

def print_order_book_table(order_book):
    """
    打印格式化的订单簿表格
    
    Args:
        order_book: 订单簿数据
    """
    try:
        print(f"\n订单簿:")
        print(f"时间: {order_book.get('datetime', '')}")
        
        # 创建买单和卖单的DataFrame
        bids = order_book.get('bids', [])
        asks = order_book.get('asks', [])
        
        # 格式化买单数据
        bids_data = []
        for bid in bids:
            bids_data.append([bid['price'], bid['amount'], bid.get('total', bid['price'] * bid['amount'])])
        
        # 格式化卖单数据
        asks_data = []
        for ask in asks:
            asks_data.append([ask['price'], ask['amount'], ask.get('total', ask['price'] * ask['amount'])])
        
        # 创建DataFrame
        bids_df = pd.DataFrame(bids_data, columns=['价格', '数量', '总价值'])
        asks_df = pd.DataFrame(asks_data, columns=['价格', '数量', '总价值'])
        
        # 添加累计数量列
        if not bids_df.empty:
            bids_df['累计数量'] = bids_df['数量'].cumsum()
        
        if not asks_df.empty:
            asks_df['累计数量'] = asks_df['数量'].cumsum()
        
        # 设置显示精度
        pd.set_option('display.precision', 8)
        
        # 打印买单
        print("\n买单(Bids):")
        if bids_df.empty:
            print("无买单数据")
        else:
            print(bids_df[['价格', '数量', '累计数量']])
        
        # 打印卖单
        print("\n卖单(Asks):")
        if asks_df.empty:
            print("无卖单数据")
        else:
            print(asks_df[['价格', '数量', '累计数量']])
        
        # 打印买卖价差信息
        if bids and asks:
            spread = order_book.get('spread', asks[0]['price'] - bids[0]['price'])
            spread_percentage = order_book.get('spread_percentage', (asks[0]['price'] / bids[0]['price'] - 1) * 100)
            print(f"\n买卖价差: {spread:.8f} ({spread_percentage:.4f}%)")
    
    except Exception as e:
        logger.error(f"打印订单簿表格时出错: {e}")
        logger.error(traceback.format_exc())
        print("\n处理订单簿数据时出错，可能是数据格式问题或API返回异常")

def get_and_print_order_book(symbol, depth=10, output_format='table', save=False, output_dir=DEFAULT_OUTPUT_DIR):
    """
    获取并打印订单簿信息
    
    Args:
        symbol: 交易对，如BTC/USDT
        depth: 订单深度
        output_format: 输出格式，'table'或'json'
        save: 是否保存订单簿数据到文件
        output_dir: 数据保存目录
    """
    try:
        # 使用封装的API函数获取订单簿
        formatted_order_book = get_order_book(symbol, depth=depth)
        
        # 检查是否有错误
        if "error" in formatted_order_book:
            print(f"获取 {symbol} 的订单簿数据失败: {formatted_order_book['error']}")
            return
        
        # 根据指定格式输出
        if output_format.lower() == 'json':
            print(json.dumps(formatted_order_book, indent=2))
        else:
            print_order_book_table(formatted_order_book)
        
        # 保存订单簿数据到文件
        if save:
            save_order_book_to_file(formatted_order_book, symbol, output_dir)
            
    except Exception as e:
        logger.error(f"获取并打印订单簿时出错: {e}")
        logger.error(traceback.format_exc())
        print(f"获取 {symbol} 的订单簿数据失败: {e}")

def save_order_book_to_file(order_book_data, symbol, output_dir=DEFAULT_OUTPUT_DIR):
    """
    保存订单簿数据到文件
    
    Args:
        order_book_data: 订单簿数据
        symbol: 交易对
        output_dir: 输出目录
    """
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 格式化交易对名称(去除/符号)
        symbol_formatted = symbol.replace('/', '_')
        
        # 构建输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(output_dir, f"gate_orderbook_{symbol_formatted}_{timestamp}.json")
        
        # 添加元数据
        data = {
            'symbol': symbol,
            'timestamp': int(datetime.now().timestamp()),
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'exchange': 'gate',
            'orderbook': order_book_data
        }
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"订单簿数据已保存到: {output_file}")
        return True
    except Exception as e:
        logger.error(f"保存订单簿数据时出错: {e}")
        return False

def get_best_price(symbol, side='ask'):
    """
    获取最优价格的API函数，方便其他脚本直接调用
    
    Args:
        symbol (str): 交易对，如BTC/USDT
        side (str): 'ask'表示卖一价，'bid'表示买一价
        
    Returns:
        float: 最优价格，如果出错则返回None
    """
    try:
        # 只需要获取深度为1的订单簿
        order_book = get_order_book(symbol, depth=1)
        
        if "error" in order_book:
            logger.error(f"获取最优价格失败: {order_book['error']}")
            return None
        
        if side.lower() == 'ask' and order_book['asks']:
            return order_book['asks'][0]['price']
        elif side.lower() == 'bid' and order_book['bids']:
            return order_book['bids'][0]['price']
        else:
            logger.warning(f"没有找到{side}方向的价格")
            return None
    except Exception as e:
        logger.error(f"获取最优价格时出错: {e}")
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="查询Gate.io订单簿")
    parser.add_argument("symbol", help="交易对，例如BTC/USDT")
    parser.add_argument("-d", "--depth", type=int, default=10, help="订单深度，默认为10")
    parser.add_argument("-f", "--format", choices=['table', 'json'], default='table', 
                        help="输出格式，默认为table")
    parser.add_argument("-s", "--save", action="store_true", help="保存订单簿数据到文件")
    parser.add_argument("-o", "--output-dir", default=DEFAULT_OUTPUT_DIR, help="输出目录路径")
    
    args = parser.parse_args()
    
    get_and_print_order_book(args.symbol, args.depth, args.format, args.save, args.output_dir) 