import { AccountAuthenticatorMultiEd25519 } from '../transactions/authenticator/account.mjs';
import { HexInput, SigningScheme } from '../types/types.mjs';
import { AccountAddressInput, AccountAddress } from '../core/accountAddress.mjs';
import { Signature } from '../core/crypto/signature.mjs';
import { Ed25519PrivateKey } from '../core/crypto/ed25519.mjs';
import { AptosConfig } from '../api/aptosConfig.mjs';
import { MultiEd25519PublicKey, MultiEd25519Signature } from '../core/crypto/multiEd25519.mjs';
import { A as Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { AnyRawTransaction } from '../transactions/types.mjs';
import '../bcs/deserializer.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../core/crypto/multiKey.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../core/crypto/privateKey.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../utils/const.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

interface MultiEd25519SignerConstructorArgs {
    publicKey: MultiEd25519PublicKey;
    signers: Ed25519PrivateKey[];
    address?: AccountAddressInput;
}
interface VerifyMultiEd25519SignatureArgs {
    message: HexInput;
    signature: MultiEd25519Signature;
}
/**
 * Signer implementation for the Multi-Ed25519 authentication scheme.
 *
 * Note: This authentication scheme is a legacy authentication scheme.  Prefer using MultiKeyAccounts as a
 * MultiKeyAccount can support any type of signer, not just Ed25519.  Generating a signer instance does not
 * create the account on-chain.
 */
declare class MultiEd25519Account implements Account {
    readonly publicKey: MultiEd25519PublicKey;
    readonly accountAddress: AccountAddress;
    readonly signingScheme = SigningScheme.MultiEd25519;
    /**
     * The signers used to sign messages.  These signers should correspond to public keys in the
     * MultiEd25519Account.  The number of signers should be equal to this.publicKey.threshold.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signers: Ed25519PrivateKey[];
    /**
     * An array of indices where for signer[i], signerIndicies[i] is the index of the corresponding public key in
     * publicKey.publicKeys.  Used to derive the right public key to use for verification.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly signerIndices: number[];
    readonly signaturesBitmap: Uint8Array;
    constructor(args: MultiEd25519SignerConstructorArgs);
    /**
     * Verify the given message and signature with the public key.
     *
     * @param args.message raw message data in HexInput format
     * @param args.signature signed message Signature
     * @returns
     */
    verifySignature(args: VerifyMultiEd25519SignatureArgs): boolean;
    /**
     * Verify the given message and signature with the public key.
     *
     * MultiEd25519 signatures do not depend on chain state, so this function is
     * equivalent to the synchronous verifySignature method.
     *
     * @param args - The arguments for verifying the signature.
     * @param args.aptosConfig - The configuration object for connecting to the Aptos network
     * @param args.message - Raw message data in HexInput format.
     * @param args.signature - Signed message signature.
     * @returns A boolean indicating whether the signature is valid.
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    verifySignatureAsync(args: {
        aptosConfig: AptosConfig;
        message: HexInput;
        signature: Signature;
        options?: {
            throwErrorWithReason?: boolean;
        };
    }): Promise<boolean>;
    /**
     * Sign a message using the account's Ed25519 private key.
     * @param message the signing message, as binary input
     * @return the AccountAuthenticator containing the signature, together with the account's public key
     */
    signWithAuthenticator(message: HexInput): AccountAuthenticatorMultiEd25519;
    /**
     * Sign a transaction using the account's Ed25519 private keys.
     * @param transaction the raw transaction
     * @return the AccountAuthenticator containing the signature of the transaction, together with the account's public key
     */
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorMultiEd25519;
    /**
     * Sign the given message using the account's Ed25519 private keys.
     * @param message in HexInput format
     * @returns MultiEd25519Signature
     */
    sign(message: HexInput): MultiEd25519Signature;
    /**
     * Sign the given transaction using the available signing capabilities.
     * @param transaction the transaction to be signed
     * @returns Signature
     */
    signTransaction(transaction: AnyRawTransaction): MultiEd25519Signature;
}

export { MultiEd25519Account, type MultiEd25519SignerConstructorArgs, type VerifyMultiEd25519SignatureArgs };
