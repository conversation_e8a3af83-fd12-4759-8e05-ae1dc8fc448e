"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSetPythPrices = exports.EventSetStorkPrices = exports.SetCoinbasePriceEvent = exports.SetProviderPriceEvent = exports.SetPriceFeedPriceEvent = exports.EventBandIBCResponseTimeout = exports.EventBandIBCAckError = exports.EventBandIBCAckSuccess = exports.SetBandIBCPriceEvent = exports.SetBandPriceEvent = exports.SetChainlinkPriceEvent = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var oracle_1 = require("./oracle.js");
exports.protobufPackage = "injective.oracle.v1beta1";
function createBaseSetChainlinkPriceEvent() {
    return { feedId: "", answer: "", timestamp: "0" };
}
exports.SetChainlinkPriceEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.answer !== "") {
            writer.uint32(18).string(message.answer);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).uint64(message.timestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetChainlinkPriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.answer = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            answer: isSet(object.answer) ? String(object.answer) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.answer !== undefined && (obj.answer = message.answer);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create: function (base) {
        return exports.SetChainlinkPriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSetChainlinkPriceEvent();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.answer = (_b = object.answer) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseSetBandPriceEvent() {
    return { relayer: "", symbol: "", price: "", resolveTime: "0", requestId: "0" };
}
exports.SetBandPriceEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.relayer !== "") {
            writer.uint32(10).string(message.relayer);
        }
        if (message.symbol !== "") {
            writer.uint32(18).string(message.symbol);
        }
        if (message.price !== "") {
            writer.uint32(26).string(message.price);
        }
        if (message.resolveTime !== "0") {
            writer.uint32(32).uint64(message.resolveTime);
        }
        if (message.requestId !== "0") {
            writer.uint32(40).uint64(message.requestId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetBandPriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.relayer = reader.string();
                    break;
                case 2:
                    message.symbol = reader.string();
                    break;
                case 3:
                    message.price = reader.string();
                    break;
                case 4:
                    message.resolveTime = longToString(reader.uint64());
                    break;
                case 5:
                    message.requestId = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            price: isSet(object.price) ? String(object.price) : "",
            resolveTime: isSet(object.resolveTime) ? String(object.resolveTime) : "0",
            requestId: isSet(object.requestId) ? String(object.requestId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.relayer !== undefined && (obj.relayer = message.relayer);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.price !== undefined && (obj.price = message.price);
        message.resolveTime !== undefined && (obj.resolveTime = message.resolveTime);
        message.requestId !== undefined && (obj.requestId = message.requestId);
        return obj;
    },
    create: function (base) {
        return exports.SetBandPriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseSetBandPriceEvent();
        message.relayer = (_a = object.relayer) !== null && _a !== void 0 ? _a : "";
        message.symbol = (_b = object.symbol) !== null && _b !== void 0 ? _b : "";
        message.price = (_c = object.price) !== null && _c !== void 0 ? _c : "";
        message.resolveTime = (_d = object.resolveTime) !== null && _d !== void 0 ? _d : "0";
        message.requestId = (_e = object.requestId) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseSetBandIBCPriceEvent() {
    return { relayer: "", symbols: [], prices: [], resolveTime: "0", requestId: "0", clientId: "0" };
}
exports.SetBandIBCPriceEvent = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.relayer !== "") {
            writer.uint32(10).string(message.relayer);
        }
        try {
            for (var _c = __values(message.symbols), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.prices), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.resolveTime !== "0") {
            writer.uint32(32).uint64(message.resolveTime);
        }
        if (message.requestId !== "0") {
            writer.uint32(40).uint64(message.requestId);
        }
        if (message.clientId !== "0") {
            writer.uint32(48).int64(message.clientId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetBandIBCPriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.relayer = reader.string();
                    break;
                case 2:
                    message.symbols.push(reader.string());
                    break;
                case 3:
                    message.prices.push(reader.string());
                    break;
                case 4:
                    message.resolveTime = longToString(reader.uint64());
                    break;
                case 5:
                    message.requestId = longToString(reader.uint64());
                    break;
                case 6:
                    message.clientId = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            symbols: Array.isArray(object === null || object === void 0 ? void 0 : object.symbols) ? object.symbols.map(function (e) { return String(e); }) : [],
            prices: Array.isArray(object === null || object === void 0 ? void 0 : object.prices) ? object.prices.map(function (e) { return String(e); }) : [],
            resolveTime: isSet(object.resolveTime) ? String(object.resolveTime) : "0",
            requestId: isSet(object.requestId) ? String(object.requestId) : "0",
            clientId: isSet(object.clientId) ? String(object.clientId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.relayer !== undefined && (obj.relayer = message.relayer);
        if (message.symbols) {
            obj.symbols = message.symbols.map(function (e) { return e; });
        }
        else {
            obj.symbols = [];
        }
        if (message.prices) {
            obj.prices = message.prices.map(function (e) { return e; });
        }
        else {
            obj.prices = [];
        }
        message.resolveTime !== undefined && (obj.resolveTime = message.resolveTime);
        message.requestId !== undefined && (obj.requestId = message.requestId);
        message.clientId !== undefined && (obj.clientId = message.clientId);
        return obj;
    },
    create: function (base) {
        return exports.SetBandIBCPriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseSetBandIBCPriceEvent();
        message.relayer = (_a = object.relayer) !== null && _a !== void 0 ? _a : "";
        message.symbols = ((_b = object.symbols) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.prices = ((_c = object.prices) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.resolveTime = (_d = object.resolveTime) !== null && _d !== void 0 ? _d : "0";
        message.requestId = (_e = object.requestId) !== null && _e !== void 0 ? _e : "0";
        message.clientId = (_f = object.clientId) !== null && _f !== void 0 ? _f : "0";
        return message;
    },
};
function createBaseEventBandIBCAckSuccess() {
    return { ackResult: "", clientId: "0" };
}
exports.EventBandIBCAckSuccess = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ackResult !== "") {
            writer.uint32(10).string(message.ackResult);
        }
        if (message.clientId !== "0") {
            writer.uint32(16).int64(message.clientId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBandIBCAckSuccess();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ackResult = reader.string();
                    break;
                case 2:
                    message.clientId = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ackResult: isSet(object.ackResult) ? String(object.ackResult) : "",
            clientId: isSet(object.clientId) ? String(object.clientId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ackResult !== undefined && (obj.ackResult = message.ackResult);
        message.clientId !== undefined && (obj.clientId = message.clientId);
        return obj;
    },
    create: function (base) {
        return exports.EventBandIBCAckSuccess.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventBandIBCAckSuccess();
        message.ackResult = (_a = object.ackResult) !== null && _a !== void 0 ? _a : "";
        message.clientId = (_b = object.clientId) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventBandIBCAckError() {
    return { ackError: "", clientId: "0" };
}
exports.EventBandIBCAckError = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.ackError !== "") {
            writer.uint32(10).string(message.ackError);
        }
        if (message.clientId !== "0") {
            writer.uint32(16).int64(message.clientId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBandIBCAckError();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ackError = reader.string();
                    break;
                case 2:
                    message.clientId = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            ackError: isSet(object.ackError) ? String(object.ackError) : "",
            clientId: isSet(object.clientId) ? String(object.clientId) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.ackError !== undefined && (obj.ackError = message.ackError);
        message.clientId !== undefined && (obj.clientId = message.clientId);
        return obj;
    },
    create: function (base) {
        return exports.EventBandIBCAckError.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventBandIBCAckError();
        message.ackError = (_a = object.ackError) !== null && _a !== void 0 ? _a : "";
        message.clientId = (_b = object.clientId) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventBandIBCResponseTimeout() {
    return { clientId: "0" };
}
exports.EventBandIBCResponseTimeout = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.clientId !== "0") {
            writer.uint32(8).int64(message.clientId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventBandIBCResponseTimeout();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.clientId = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { clientId: isSet(object.clientId) ? String(object.clientId) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.clientId !== undefined && (obj.clientId = message.clientId);
        return obj;
    },
    create: function (base) {
        return exports.EventBandIBCResponseTimeout.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventBandIBCResponseTimeout();
        message.clientId = (_a = object.clientId) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseSetPriceFeedPriceEvent() {
    return { relayer: "", base: "", quote: "", price: "" };
}
exports.SetPriceFeedPriceEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.relayer !== "") {
            writer.uint32(10).string(message.relayer);
        }
        if (message.base !== "") {
            writer.uint32(18).string(message.base);
        }
        if (message.quote !== "") {
            writer.uint32(26).string(message.quote);
        }
        if (message.price !== "") {
            writer.uint32(34).string(message.price);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetPriceFeedPriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.relayer = reader.string();
                    break;
                case 2:
                    message.base = reader.string();
                    break;
                case 3:
                    message.quote = reader.string();
                    break;
                case 4:
                    message.price = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            base: isSet(object.base) ? String(object.base) : "",
            quote: isSet(object.quote) ? String(object.quote) : "",
            price: isSet(object.price) ? String(object.price) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.relayer !== undefined && (obj.relayer = message.relayer);
        message.base !== undefined && (obj.base = message.base);
        message.quote !== undefined && (obj.quote = message.quote);
        message.price !== undefined && (obj.price = message.price);
        return obj;
    },
    create: function (base) {
        return exports.SetPriceFeedPriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSetPriceFeedPriceEvent();
        message.relayer = (_a = object.relayer) !== null && _a !== void 0 ? _a : "";
        message.base = (_b = object.base) !== null && _b !== void 0 ? _b : "";
        message.quote = (_c = object.quote) !== null && _c !== void 0 ? _c : "";
        message.price = (_d = object.price) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseSetProviderPriceEvent() {
    return { provider: "", relayer: "", symbol: "", price: "" };
}
exports.SetProviderPriceEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.provider !== "") {
            writer.uint32(10).string(message.provider);
        }
        if (message.relayer !== "") {
            writer.uint32(18).string(message.relayer);
        }
        if (message.symbol !== "") {
            writer.uint32(26).string(message.symbol);
        }
        if (message.price !== "") {
            writer.uint32(34).string(message.price);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetProviderPriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.provider = reader.string();
                    break;
                case 2:
                    message.relayer = reader.string();
                    break;
                case 3:
                    message.symbol = reader.string();
                    break;
                case 4:
                    message.price = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            provider: isSet(object.provider) ? String(object.provider) : "",
            relayer: isSet(object.relayer) ? String(object.relayer) : "",
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            price: isSet(object.price) ? String(object.price) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.provider !== undefined && (obj.provider = message.provider);
        message.relayer !== undefined && (obj.relayer = message.relayer);
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.price !== undefined && (obj.price = message.price);
        return obj;
    },
    create: function (base) {
        return exports.SetProviderPriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseSetProviderPriceEvent();
        message.provider = (_a = object.provider) !== null && _a !== void 0 ? _a : "";
        message.relayer = (_b = object.relayer) !== null && _b !== void 0 ? _b : "";
        message.symbol = (_c = object.symbol) !== null && _c !== void 0 ? _c : "";
        message.price = (_d = object.price) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseSetCoinbasePriceEvent() {
    return { symbol: "", price: "", timestamp: "0" };
}
exports.SetCoinbasePriceEvent = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.price !== "") {
            writer.uint32(18).string(message.price);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).uint64(message.timestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetCoinbasePriceEvent();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.price = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            price: isSet(object.price) ? String(object.price) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.price !== undefined && (obj.price = message.price);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create: function (base) {
        return exports.SetCoinbasePriceEvent.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSetCoinbasePriceEvent();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.price = (_b = object.price) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseEventSetStorkPrices() {
    return { prices: [] };
}
exports.EventSetStorkPrices = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.prices), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.StorkPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSetStorkPrices();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.prices.push(oracle_1.StorkPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { prices: Array.isArray(object === null || object === void 0 ? void 0 : object.prices) ? object.prices.map(function (e) { return oracle_1.StorkPriceState.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.prices) {
            obj.prices = message.prices.map(function (e) { return e ? oracle_1.StorkPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.prices = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventSetStorkPrices.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventSetStorkPrices();
        message.prices = ((_a = object.prices) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.StorkPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseEventSetPythPrices() {
    return { prices: [] };
}
exports.EventSetPythPrices = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.prices), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                oracle_1.PythPriceState.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventSetPythPrices();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.prices.push(oracle_1.PythPriceState.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { prices: Array.isArray(object === null || object === void 0 ? void 0 : object.prices) ? object.prices.map(function (e) { return oracle_1.PythPriceState.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.prices) {
            obj.prices = message.prices.map(function (e) { return e ? oracle_1.PythPriceState.toJSON(e) : undefined; });
        }
        else {
            obj.prices = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.EventSetPythPrices.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseEventSetPythPrices();
        message.prices = ((_a = object.prices) === null || _a === void 0 ? void 0 : _a.map(function (e) { return oracle_1.PythPriceState.fromPartial(e); })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
