import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx, InjectiveExchangeV1Beta1Exchange } from '@injectivelabs/core-proto-ts';
interface SpotOrderToCreate {
    orderType: InjectiveExchangeV1Beta1Exchange.OrderType;
    triggerPrice?: string;
    marketId: string;
    feeRecipient: string;
    price: string;
    quantity: string;
    cid?: string;
}
interface DerivativeOrderToCreate {
    orderType: InjectiveExchangeV1Beta1Exchange.OrderType;
    triggerPrice?: string;
    feeRecipient: string;
    marketId: string;
    price: string;
    margin: string;
    quantity: string;
    cid?: string;
}
interface BinaryOptionOrderToCreate {
    orderType: InjectiveExchangeV1Beta1Exchange.OrderType;
    triggerPrice?: string;
    feeRecipient: string;
    marketId: string;
    price: string;
    margin: string;
    quantity: string;
    cid?: string;
}
export declare namespace MsgBatchUpdateOrders {
    interface Params {
        subaccountId: string;
        spotMarketIdsToCancelAll?: string[];
        derivativeMarketIdsToCancelAll?: string[];
        binaryOptionsMarketIdsToCancelAll?: string[];
        spotOrdersToCancel?: {
            marketId: string;
            subaccountId: string;
            orderHash?: string;
            cid?: string;
        }[];
        derivativeOrdersToCancel?: {
            marketId: string;
            subaccountId: string;
            orderHash?: string;
            cid?: string;
        }[];
        binaryOptionsOrdersToCancel?: {
            marketId: string;
            subaccountId: string;
            orderHash?: string;
            cid?: string;
        }[];
        spotOrdersToCreate?: SpotOrderToCreate[];
        derivativeOrdersToCreate?: DerivativeOrderToCreate[];
        binaryOptionsOrdersToCreate?: BinaryOptionOrderToCreate[];
        injectiveAddress: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders;
}
/**
 * @category Messages
 */
export default class MsgBatchUpdateOrders extends MsgBase<MsgBatchUpdateOrders.Params, MsgBatchUpdateOrders.Proto> {
    static fromJSON(params: MsgBatchUpdateOrders.Params): MsgBatchUpdateOrders;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders;
    toData(): {
        sender: string;
        subaccountId: string;
        spotMarketIdsToCancelAll: string[];
        derivativeMarketIdsToCancelAll: string[];
        spotOrdersToCancel: InjectiveExchangeV1Beta1Tx.OrderData[];
        derivativeOrdersToCancel: InjectiveExchangeV1Beta1Tx.OrderData[];
        spotOrdersToCreate: InjectiveExchangeV1Beta1Exchange.SpotOrder[];
        derivativeOrdersToCreate: InjectiveExchangeV1Beta1Exchange.DerivativeOrder[];
        binaryOptionsOrdersToCancel: InjectiveExchangeV1Beta1Tx.OrderData[];
        binaryOptionsMarketIdsToCancelAll: string[];
        binaryOptionsOrdersToCreate: InjectiveExchangeV1Beta1Exchange.DerivativeOrder[];
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders>;
    };
    toWeb3Gw(): {
        sender: string;
        subaccount_id: string;
        spot_market_ids_to_cancel_all: string[];
        derivative_market_ids_to_cancel_all: string[];
        spot_orders_to_cancel: {
            market_id: string;
            subaccount_id: string;
            order_hash: string;
            order_mask: number;
            cid: string;
        }[];
        derivative_orders_to_cancel: {
            market_id: string;
            subaccount_id: string;
            order_hash: string;
            order_mask: number;
            cid: string;
        }[];
        spot_orders_to_create: {
            market_id: string;
            order_info: {
                subaccount_id: string;
                fee_recipient: string;
                price: string;
                quantity: string;
                cid: string;
            } | undefined;
            order_type: InjectiveExchangeV1Beta1Exchange.OrderType;
            trigger_price: string;
        }[];
        derivative_orders_to_create: {
            market_id: string;
            order_info: {
                subaccount_id: string;
                fee_recipient: string;
                price: string;
                quantity: string;
                cid: string;
            } | undefined;
            order_type: InjectiveExchangeV1Beta1Exchange.OrderType;
            margin: string;
            trigger_price: string;
        }[];
        binary_options_orders_to_cancel: {
            market_id: string;
            subaccount_id: string;
            order_hash: string;
            order_mask: number;
            cid: string;
        }[];
        binary_options_market_ids_to_cancel_all: string[];
        binary_options_orders_to_create: {
            market_id: string;
            order_info: {
                subaccount_id: string;
                fee_recipient: string;
                price: string;
                quantity: string;
                cid: string;
            } | undefined;
            order_type: InjectiveExchangeV1Beta1Exchange.OrderType;
            margin: string;
            trigger_price: string;
        }[];
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgBatchUpdateOrders;
    };
    toBinary(): Uint8Array;
}
export {};
