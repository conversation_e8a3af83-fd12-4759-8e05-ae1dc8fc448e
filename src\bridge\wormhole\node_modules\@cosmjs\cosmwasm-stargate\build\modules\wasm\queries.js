"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupWasmExtension = void 0;
const encoding_1 = require("@cosmjs/encoding");
const stargate_1 = require("@cosmjs/stargate");
const query_1 = require("cosmjs-types/cosmwasm/wasm/v1/query");
function setupWasmExtension(base) {
    const rpc = (0, stargate_1.createProtobufRpcClient)(base);
    // Use this service to get easy typed access to query methods
    // This cannot be used for proof verification
    const queryService = new query_1.QueryClientImpl(rpc);
    return {
        wasm: {
            listCodeInfo: async (paginationKey) => {
                const request = {
                    pagination: (0, stargate_1.createPagination)(paginationKey),
                };
                return queryService.Codes(request);
            },
            getCode: async (id) => {
                const request = query_1.QueryCodeRequest.fromPartial({ codeId: BigInt(id) });
                return queryService.Code(request);
            },
            listContractsByCodeId: async (id, paginationKey) => {
                const request = query_1.QueryContractsByCodeRequest.fromPartial({
                    codeId: BigInt(id),
                    pagination: (0, stargate_1.createPagination)(paginationKey),
                });
                return queryService.ContractsByCode(request);
            },
            listContractsByCreator: async (creator, paginationKey) => {
                const request = {
                    creatorAddress: creator,
                    pagination: (0, stargate_1.createPagination)(paginationKey),
                };
                return queryService.ContractsByCreator(request);
            },
            getContractInfo: async (address) => {
                const request = { address: address };
                return queryService.ContractInfo(request);
            },
            getContractCodeHistory: async (address, paginationKey) => {
                const request = {
                    address: address,
                    pagination: (0, stargate_1.createPagination)(paginationKey),
                };
                return queryService.ContractHistory(request);
            },
            getAllContractState: async (address, paginationKey) => {
                const request = {
                    address: address,
                    pagination: (0, stargate_1.createPagination)(paginationKey),
                };
                return queryService.AllContractState(request);
            },
            queryContractRaw: async (address, key) => {
                const request = { address: address, queryData: key };
                return queryService.RawContractState(request);
            },
            queryContractSmart: async (address, query) => {
                const request = { address: address, queryData: (0, encoding_1.toUtf8)(JSON.stringify(query)) };
                const { data } = await queryService.SmartContractState(request);
                // By convention, smart queries must return a valid JSON document (see https://github.com/CosmWasm/cosmwasm/issues/144)
                let responseText;
                try {
                    responseText = (0, encoding_1.fromUtf8)(data);
                }
                catch (error) {
                    throw new Error(`Could not UTF-8 decode smart query response from contract: ${error}`);
                }
                try {
                    return JSON.parse(responseText);
                }
                catch (error) {
                    throw new Error(`Could not JSON parse smart query response from contract: ${error}`);
                }
            },
        },
    };
}
exports.setupWasmExtension = setupWasmExtension;
//# sourceMappingURL=queries.js.map