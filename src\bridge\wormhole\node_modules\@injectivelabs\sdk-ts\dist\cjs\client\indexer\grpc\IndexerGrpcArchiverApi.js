"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerGrpcArchiverApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const indexer_proto_ts_1 = require("@injectivelabs/indexer-proto-ts");
const BaseIndexerGrpcConsumer_js_1 = __importDefault(require("../../base/BaseIndexerGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const index_js_2 = require("../transformers/index.js");
/**
 * @category Indexer Grpc API
 */
class IndexerGrpcArchiverApi extends BaseIndexerGrpcConsumer_js_1.default {
    module = index_js_1.IndexerModule.Archiver;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new indexer_proto_ts_1.InjectiveArchiverRpc.InjectiveArchiverRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchHistoricalBalance({ account, resolution, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.BalanceRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Balance(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcHistoricalBalanceResponseToHistoricalBalances(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Historical Balance',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Historical Balance',
                contextModule: this.module,
            });
        }
    }
    async fetchHistoricalRpnl({ account, resolution, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.RpnlRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Rpnl(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcHistoricalRPNLResponseToHistoricalRPNL(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Historical Rpnl',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Historical Rpnl',
                contextModule: this.module,
            });
        }
    }
    async fetchHistoricalVolumes({ account, resolution, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.VolumesRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Volumes(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcHistoricalVolumesResponseToHistoricalVolumes(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Historical Volumes',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Historical Volumes',
                contextModule: this.module,
            });
        }
    }
    async fetchPnlLeaderboard({ startDate, endDate, limit, account, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.PnlLeaderboardRequest.create();
        request.startDate = startDate;
        request.endDate = endDate;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.PnlLeaderboard(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcPnlLeaderboardResponseToPnlLeaderboard(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Pnl Leaderboard',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Pnl Leaderboard',
                contextModule: this.module,
            });
        }
    }
    async fetchVolLeaderboard({ startDate, endDate, limit, account, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.VolLeaderboardRequest.create();
        request.startDate = startDate;
        request.endDate = endDate;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.VolLeaderboard(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcVolLeaderboardResponseToVolLeaderboard(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Vol Leaderboard',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Vol Leaderboard',
                contextModule: this.module,
            });
        }
    }
    async fetchPnlLeaderboardFixedResolution({ resolution, limit, account, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.PnlLeaderboardFixedResolutionRequest.create();
        request.resolution = resolution;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.PnlLeaderboardFixedResolution(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcPnlLeaderboardFixedResolutionResponseToPnlLeaderboard(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Pnl Leaderboard Fixed Resolution',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Pnl Leaderboard Fixed Resolution',
                contextModule: this.module,
            });
        }
    }
    async fetchVolLeaderboardFixedResolution({ resolution, limit, account, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.VolLeaderboardFixedResolutionRequest.create();
        request.resolution = resolution;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.VolLeaderboardFixedResolution(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcVolLeaderboardFixedResolutionResponseToVolLeaderboard(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Vol Leaderboard Fixed Resolution',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Vol Leaderboard Fixed Resolution',
                contextModule: this.module,
            });
        }
    }
    async fetchDenomHolders({ denom, token, limit, }) {
        const request = indexer_proto_ts_1.InjectiveArchiverRpc.DenomHoldersRequest.create();
        request.denom = denom;
        if (token) {
            request.token = token;
        }
        if (limit) {
            request.limit = limit;
        }
        try {
            const response = await this.retry(() => this.client.DenomHolders(request, this.metadata));
            return index_js_2.IndexerGrpcArchiverTransformer.grpcDenomHoldersResponseToDenomHolders(response);
        }
        catch (e) {
            if (e instanceof indexer_proto_ts_1.InjectiveArchiverRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'DenomHolders',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'DenomHolders',
                contextModule: this.module,
            });
        }
    }
}
exports.IndexerGrpcArchiverApi = IndexerGrpcArchiverApi;
