{"version": 3, "file": "aminomessages.js", "sourceRoot": "", "sources": ["../../../src/modules/staking/aminomessages.ts"], "names": [], "mappings": ";;;AAEA,uCAAuC;AACvC,yDAAmE;AACnE,yCAAwD;AA4BxD,SAAgB,kBAAkB,CAAC,OAAe;IAChD,MAAM,MAAM,GAAG,cAAO,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzD,OAAO,GAAG,KAAK,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC;AAC1D,CAAC;AAJD,gDAIC;AAED,SAAS,kBAAkB,CAAC,OAAe;IACzC,MAAM,MAAM,GAAG,cAAO,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAClD,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AAmBD,SAAgB,yBAAyB,CAAC,GAAa;IACrD,OAAO,GAAG,CAAC,IAAI,KAAK,+BAA+B,CAAC;AACtD,CAAC;AAFD,8DAEC;AA0BD,SAAgB,uBAAuB,CAAC,GAAa;IACnD,OAAO,GAAG,CAAC,IAAI,KAAK,6BAA6B,CAAC;AACpD,CAAC;AAFD,0DAEC;AAkBD,SAAgB,kBAAkB,CAAC,GAAa;IAC9C,OAAO,GAAG,CAAC,IAAI,KAAK,wBAAwB,CAAC;AAC/C,CAAC;AAFD,gDAEC;AAgBD,SAAgB,yBAAyB,CAAC,GAAa;IACrD,OAAO,GAAG,CAAC,IAAI,KAAK,+BAA+B,CAAC;AACtD,CAAC;AAFD,8DAEC;AAcD,SAAgB,oBAAoB,CAAC,GAAa;IAChD,OAAO,GAAG,CAAC,IAAI,KAAK,0BAA0B,CAAC;AACjD,CAAC;AAFD,oDAEC;AAYD,SAAgB,mCAAmC,CAAC,GAAa;IAC/D,OAAO,GAAG,CAAC,IAAI,KAAK,yCAAyC,CAAC;AAChE,CAAC;AAFD,kFAEC;AAED,SAAgB,4BAA4B;IAC1C,OAAO;QACL,4CAA4C,EAAE;YAC5C,SAAS,EAAE,+BAA+B;YAC1C,OAAO,EAAE,CAAC,EACR,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,MAAM,GACa,EAAoC,EAAE;gBACzD,IAAA,+BAAuB,EAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAClD,OAAO;oBACL,iBAAiB,EAAE,gBAAgB;oBACnC,qBAAqB,EAAE,mBAAmB;oBAC1C,qBAAqB,EAAE,mBAAmB;oBAC1C,MAAM,EAAE,MAAM;iBACf,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,iBAAiB,EACjB,qBAAqB,EACrB,qBAAqB,EACrB,MAAM,GAC2B,EAAsB,EAAE,CAAC,CAAC;gBAC3D,gBAAgB,EAAE,iBAAiB;gBACnC,mBAAmB,EAAE,qBAAqB;gBAC1C,mBAAmB,EAAE,qBAAqB;gBAC1C,MAAM,EAAE,MAAM;aACf,CAAC;SACH;QACD,4CAA4C,EAAE;YAC5C,SAAS,EAAE,+BAA+B;YAC1C,OAAO,EAAE,CAAC,EACR,WAAW,EACX,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,MAAM,EACN,KAAK,GACc,EAAoC,EAAE;gBACzD,IAAA,+BAAuB,EAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;gBAC5D,IAAA,+BAAuB,EAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;gBAC1D,IAAA,+BAAuB,EAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAClD,IAAA,+BAAuB,EAAC,KAAK,EAAE,eAAe,CAAC,CAAC;gBAChD,OAAO;oBACL,WAAW,EAAE;wBACX,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,QAAQ,EAAE,WAAW,CAAC,QAAQ;wBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,gBAAgB,EAAE,WAAW,CAAC,eAAe;wBAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;qBAC7B;oBACD,UAAU,EAAE;wBACV,IAAI,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC;wBACzC,QAAQ,EAAE,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC;wBAChD,eAAe,EAAE,kBAAkB,CAAC,UAAU,CAAC,aAAa,CAAC;qBAC9D;oBACD,mBAAmB,EAAE,iBAAiB;oBACtC,iBAAiB,EAAE,gBAAgB;oBACnC,iBAAiB,EAAE,gBAAgB;oBACnC,MAAM,EAAE,IAAA,4BAAY,EAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,KAAK;iBACb,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,WAAW,EACX,UAAU,EACV,mBAAmB,EACnB,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,EACN,KAAK,GAC4B,EAAsB,EAAE;gBACzD,OAAO;oBACL,WAAW,EAAE;wBACX,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,QAAQ,EAAE,WAAW,CAAC,QAAQ;wBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,eAAe,EAAE,WAAW,CAAC,gBAAgB;wBAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;qBAC7B;oBACD,UAAU,EAAE;wBACV,IAAI,EAAE,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC;wBACzC,OAAO,EAAE,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC;wBAChD,aAAa,EAAE,kBAAkB,CAAC,UAAU,CAAC,eAAe,CAAC;qBAC9D;oBACD,iBAAiB,EAAE,mBAAmB;oBACtC,gBAAgB,EAAE,iBAAiB;oBACnC,gBAAgB,EAAE,iBAAiB;oBACnC,MAAM,EAAE,IAAA,4BAAY,EAAC,MAAM,CAAC;oBAC5B,KAAK,EAAE,KAAK;iBACb,CAAC;YACJ,CAAC;SACF;QACD,qCAAqC,EAAE;YACrC,SAAS,EAAE,wBAAwB;YACnC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,EAAe,EAA6B,EAAE;gBAClG,IAAA,+BAAuB,EAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAClD,OAAO;oBACL,iBAAiB,EAAE,gBAAgB;oBACnC,iBAAiB,EAAE,gBAAgB;oBACnC,MAAM,EAAE,MAAM;iBACf,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,GACoB,EAAe,EAAE,CAAC,CAAC;gBAC7C,gBAAgB,EAAE,iBAAiB;gBACnC,gBAAgB,EAAE,iBAAiB;gBACnC,MAAM,EAAE,MAAM;aACf,CAAC;SACH;QACD,0CAA0C,EAAE;YAC1C,SAAS,EAAE,6BAA6B;YACxC,OAAO,EAAE,CAAC,EACR,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,gBAAgB,GACC,EAAkC,EAAE;gBACrD,IAAA,+BAAuB,EAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;gBAC5D,OAAO;oBACL,WAAW,EAAE;wBACX,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,QAAQ,EAAE,WAAW,CAAC,QAAQ;wBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;wBAC5B,gBAAgB,EAAE,WAAW,CAAC,eAAe;wBAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;qBAC7B;oBACD,8DAA8D;oBAC9D,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;oBAChF,8DAA8D;oBAC9D,mBAAmB,EAAE,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS;oBACtE,iBAAiB,EAAE,gBAAgB;iBACpC,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,iBAAiB,GACc,EAAoB,EAAE,CAAC,CAAC;gBACvD,WAAW,EAAE;oBACX,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,eAAe,EAAE,WAAW,CAAC,gBAAgB;oBAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;iBAC7B;gBACD,8DAA8D;gBAC9D,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1E,8DAA8D;gBAC9D,iBAAiB,EAAE,mBAAmB,IAAI,EAAE;gBAC5C,gBAAgB,EAAE,iBAAiB;aACpC,CAAC;SACH;QACD,uCAAuC,EAAE;YACvC,SAAS,EAAE,0BAA0B;YACrC,OAAO,EAAE,CAAC,EACR,gBAAgB,EAChB,gBAAgB,EAChB,MAAM,GACQ,EAA+B,EAAE;gBAC/C,IAAA,+BAAuB,EAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAClD,OAAO;oBACL,iBAAiB,EAAE,gBAAgB;oBACnC,iBAAiB,EAAE,gBAAgB;oBACnC,MAAM,EAAE,MAAM;iBACf,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,GACsB,EAAiB,EAAE,CAAC,CAAC;gBACjD,gBAAgB,EAAE,iBAAiB;gBACnC,gBAAgB,EAAE,iBAAiB;gBACnC,MAAM,EAAE,MAAM;aACf,CAAC;SACH;QACD,sDAAsD,EAAE;YACtD,SAAS,EAAE,yCAAyC;YACpD,OAAO,EAAE,CAAC,EACR,gBAAgB,EAChB,gBAAgB,EAChB,MAAM,EACN,cAAc,GACe,EAA8C,EAAE;gBAC7E,IAAA,+BAAuB,EAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBAClD,OAAO;oBACL,iBAAiB,EAAE,gBAAgB;oBACnC,iBAAiB,EAAE,gBAAgB;oBACnC,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,cAAc,CAAC,QAAQ,EAAE;iBAC3C,CAAC;YACJ,CAAC;YACD,SAAS,EAAE,CAAC,EACV,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,EACN,eAAe,GAC4B,EAAgC,EAAE,CAAC,CAAC;gBAC/E,gBAAgB,EAAE,iBAAiB;gBACnC,gBAAgB,EAAE,iBAAiB;gBACnC,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,MAAM,CAAC,eAAe,CAAC;aACxC,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AApND,oEAoNC"}