"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchStoreCodeProposal = exports.ContractRegistrationRequest = exports.BatchContractDeregistrationProposal = exports.BatchContractRegistrationRequestProposal = exports.ContractRegistrationRequestProposal = exports.FundingMode = exports.protobufPackage = void 0;
exports.fundingModeFromJSON = fundingModeFromJSON;
exports.fundingModeToJSON = fundingModeToJSON;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var proposal_legacy_1 = require("../../../cosmwasm/wasm/v1/proposal_legacy.js");
exports.protobufPackage = "injective.wasmx.v1";
var FundingMode;
(function (FundingMode) {
    FundingMode[FundingMode["Unspecified"] = 0] = "Unspecified";
    FundingMode[FundingMode["SelfFunded"] = 1] = "SelfFunded";
    FundingMode[FundingMode["GrantOnly"] = 2] = "GrantOnly";
    FundingMode[FundingMode["Dual"] = 3] = "Dual";
    FundingMode[FundingMode["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(FundingMode || (exports.FundingMode = FundingMode = {}));
function fundingModeFromJSON(object) {
    switch (object) {
        case 0:
        case "Unspecified":
            return FundingMode.Unspecified;
        case 1:
        case "SelfFunded":
            return FundingMode.SelfFunded;
        case 2:
        case "GrantOnly":
            return FundingMode.GrantOnly;
        case 3:
        case "Dual":
            return FundingMode.Dual;
        case -1:
        case "UNRECOGNIZED":
        default:
            return FundingMode.UNRECOGNIZED;
    }
}
function fundingModeToJSON(object) {
    switch (object) {
        case FundingMode.Unspecified:
            return "Unspecified";
        case FundingMode.SelfFunded:
            return "SelfFunded";
        case FundingMode.GrantOnly:
            return "GrantOnly";
        case FundingMode.Dual:
            return "Dual";
        case FundingMode.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseContractRegistrationRequestProposal() {
    return { title: "", description: "", contractRegistrationRequest: undefined };
}
exports.ContractRegistrationRequestProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.contractRegistrationRequest !== undefined) {
            exports.ContractRegistrationRequest.encode(message.contractRegistrationRequest, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseContractRegistrationRequestProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.contractRegistrationRequest = exports.ContractRegistrationRequest.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            contractRegistrationRequest: isSet(object.contractRegistrationRequest)
                ? exports.ContractRegistrationRequest.fromJSON(object.contractRegistrationRequest)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.contractRegistrationRequest !== undefined &&
            (obj.contractRegistrationRequest = message.contractRegistrationRequest
                ? exports.ContractRegistrationRequest.toJSON(message.contractRegistrationRequest)
                : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ContractRegistrationRequestProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseContractRegistrationRequestProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.contractRegistrationRequest =
            (object.contractRegistrationRequest !== undefined && object.contractRegistrationRequest !== null)
                ? exports.ContractRegistrationRequest.fromPartial(object.contractRegistrationRequest)
                : undefined;
        return message;
    },
};
function createBaseBatchContractRegistrationRequestProposal() {
    return { title: "", description: "", contractRegistrationRequests: [] };
}
exports.BatchContractRegistrationRequestProposal = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.contractRegistrationRequests), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.ContractRegistrationRequest.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchContractRegistrationRequestProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.contractRegistrationRequests.push(exports.ContractRegistrationRequest.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            contractRegistrationRequests: Array.isArray(object === null || object === void 0 ? void 0 : object.contractRegistrationRequests)
                ? object.contractRegistrationRequests.map(function (e) { return exports.ContractRegistrationRequest.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.contractRegistrationRequests) {
            obj.contractRegistrationRequests = message.contractRegistrationRequests.map(function (e) {
                return e ? exports.ContractRegistrationRequest.toJSON(e) : undefined;
            });
        }
        else {
            obj.contractRegistrationRequests = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchContractRegistrationRequestProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBatchContractRegistrationRequestProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.contractRegistrationRequests =
            ((_c = object.contractRegistrationRequests) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.ContractRegistrationRequest.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseBatchContractDeregistrationProposal() {
    return { title: "", description: "", contracts: [] };
}
exports.BatchContractDeregistrationProposal = {
    encode: function (message, writer) {
        var e_2, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.contracts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchContractDeregistrationProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.contracts.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            contracts: Array.isArray(object === null || object === void 0 ? void 0 : object.contracts) ? object.contracts.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.contracts) {
            obj.contracts = message.contracts.map(function (e) { return e; });
        }
        else {
            obj.contracts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchContractDeregistrationProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBatchContractDeregistrationProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.contracts = ((_c = object.contracts) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseContractRegistrationRequest() {
    return {
        contractAddress: "",
        gasLimit: "0",
        gasPrice: "0",
        shouldPinContract: false,
        isMigrationAllowed: false,
        codeId: "0",
        adminAddress: "",
        granterAddress: "",
        fundingMode: 0,
    };
}
exports.ContractRegistrationRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.contractAddress !== "") {
            writer.uint32(10).string(message.contractAddress);
        }
        if (message.gasLimit !== "0") {
            writer.uint32(16).uint64(message.gasLimit);
        }
        if (message.gasPrice !== "0") {
            writer.uint32(24).uint64(message.gasPrice);
        }
        if (message.shouldPinContract === true) {
            writer.uint32(32).bool(message.shouldPinContract);
        }
        if (message.isMigrationAllowed === true) {
            writer.uint32(40).bool(message.isMigrationAllowed);
        }
        if (message.codeId !== "0") {
            writer.uint32(48).uint64(message.codeId);
        }
        if (message.adminAddress !== "") {
            writer.uint32(58).string(message.adminAddress);
        }
        if (message.granterAddress !== "") {
            writer.uint32(66).string(message.granterAddress);
        }
        if (message.fundingMode !== 0) {
            writer.uint32(72).int32(message.fundingMode);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseContractRegistrationRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.contractAddress = reader.string();
                    break;
                case 2:
                    message.gasLimit = longToString(reader.uint64());
                    break;
                case 3:
                    message.gasPrice = longToString(reader.uint64());
                    break;
                case 4:
                    message.shouldPinContract = reader.bool();
                    break;
                case 5:
                    message.isMigrationAllowed = reader.bool();
                    break;
                case 6:
                    message.codeId = longToString(reader.uint64());
                    break;
                case 7:
                    message.adminAddress = reader.string();
                    break;
                case 8:
                    message.granterAddress = reader.string();
                    break;
                case 9:
                    message.fundingMode = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            contractAddress: isSet(object.contractAddress) ? String(object.contractAddress) : "",
            gasLimit: isSet(object.gasLimit) ? String(object.gasLimit) : "0",
            gasPrice: isSet(object.gasPrice) ? String(object.gasPrice) : "0",
            shouldPinContract: isSet(object.shouldPinContract) ? Boolean(object.shouldPinContract) : false,
            isMigrationAllowed: isSet(object.isMigrationAllowed) ? Boolean(object.isMigrationAllowed) : false,
            codeId: isSet(object.codeId) ? String(object.codeId) : "0",
            adminAddress: isSet(object.adminAddress) ? String(object.adminAddress) : "",
            granterAddress: isSet(object.granterAddress) ? String(object.granterAddress) : "",
            fundingMode: isSet(object.fundingMode) ? fundingModeFromJSON(object.fundingMode) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.contractAddress !== undefined && (obj.contractAddress = message.contractAddress);
        message.gasLimit !== undefined && (obj.gasLimit = message.gasLimit);
        message.gasPrice !== undefined && (obj.gasPrice = message.gasPrice);
        message.shouldPinContract !== undefined && (obj.shouldPinContract = message.shouldPinContract);
        message.isMigrationAllowed !== undefined && (obj.isMigrationAllowed = message.isMigrationAllowed);
        message.codeId !== undefined && (obj.codeId = message.codeId);
        message.adminAddress !== undefined && (obj.adminAddress = message.adminAddress);
        message.granterAddress !== undefined && (obj.granterAddress = message.granterAddress);
        message.fundingMode !== undefined && (obj.fundingMode = fundingModeToJSON(message.fundingMode));
        return obj;
    },
    create: function (base) {
        return exports.ContractRegistrationRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseContractRegistrationRequest();
        message.contractAddress = (_a = object.contractAddress) !== null && _a !== void 0 ? _a : "";
        message.gasLimit = (_b = object.gasLimit) !== null && _b !== void 0 ? _b : "0";
        message.gasPrice = (_c = object.gasPrice) !== null && _c !== void 0 ? _c : "0";
        message.shouldPinContract = (_d = object.shouldPinContract) !== null && _d !== void 0 ? _d : false;
        message.isMigrationAllowed = (_e = object.isMigrationAllowed) !== null && _e !== void 0 ? _e : false;
        message.codeId = (_f = object.codeId) !== null && _f !== void 0 ? _f : "0";
        message.adminAddress = (_g = object.adminAddress) !== null && _g !== void 0 ? _g : "";
        message.granterAddress = (_h = object.granterAddress) !== null && _h !== void 0 ? _h : "";
        message.fundingMode = (_j = object.fundingMode) !== null && _j !== void 0 ? _j : 0;
        return message;
    },
};
function createBaseBatchStoreCodeProposal() {
    return { title: "", description: "", proposals: [] };
}
exports.BatchStoreCodeProposal = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.proposals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                proposal_legacy_1.StoreCodeProposal.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchStoreCodeProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.proposals.push(proposal_legacy_1.StoreCodeProposal.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            proposals: Array.isArray(object === null || object === void 0 ? void 0 : object.proposals)
                ? object.proposals.map(function (e) { return proposal_legacy_1.StoreCodeProposal.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.proposals) {
            obj.proposals = message.proposals.map(function (e) { return e ? proposal_legacy_1.StoreCodeProposal.toJSON(e) : undefined; });
        }
        else {
            obj.proposals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchStoreCodeProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBatchStoreCodeProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.proposals = ((_c = object.proposals) === null || _c === void 0 ? void 0 : _c.map(function (e) { return proposal_legacy_1.StoreCodeProposal.fromPartial(e); })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
