"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FullActiveGrant = exports.FullGrantAuthorizations = exports.PerpetualMarketFundingState = exports.ExpiryFuturesMarketInfoState = exports.SubaccountNonce = exports.DerivativePosition = exports.Balance = exports.ConditionalDerivativeOrderBook = exports.DerivativeOrderBook = exports.SpotOrderBook = exports.TradingRewardCampaignAccountPendingPoints = exports.TradingRewardCampaignAccountPoints = exports.AccountVolume = exports.FeeDiscountBucketVolumeAccounts = exports.FeeDiscountAccountTierTTL = exports.OrderbookSequence = exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var exchange_1 = require("./exchange.js");
exports.protobufPackage = "injective.exchange.v1beta1";
function createBaseGenesisState() {
    return {
        params: undefined,
        spotMarkets: [],
        derivativeMarkets: [],
        spotOrderbook: [],
        derivativeOrderbook: [],
        balances: [],
        positions: [],
        subaccountTradeNonces: [],
        expiryFuturesMarketInfoState: [],
        perpetualMarketInfo: [],
        perpetualMarketFundingState: [],
        derivativeMarketSettlementScheduled: [],
        isSpotExchangeEnabled: false,
        isDerivativesExchangeEnabled: false,
        tradingRewardCampaignInfo: undefined,
        tradingRewardPoolCampaignSchedule: [],
        tradingRewardCampaignAccountPoints: [],
        feeDiscountSchedule: undefined,
        feeDiscountAccountTierTtl: [],
        feeDiscountBucketVolumeAccounts: [],
        isFirstFeeCycleFinished: false,
        pendingTradingRewardPoolCampaignSchedule: [],
        pendingTradingRewardCampaignAccountPoints: [],
        rewardsOptOutAddresses: [],
        historicalTradeRecords: [],
        binaryOptionsMarkets: [],
        binaryOptionsMarketIdsScheduledForSettlement: [],
        spotMarketIdsScheduledToForceClose: [],
        denomDecimals: [],
        conditionalDerivativeOrderbooks: [],
        marketFeeMultipliers: [],
        orderbookSequences: [],
        subaccountVolumes: [],
        marketVolumes: [],
        grantAuthorizations: [],
        activeGrants: [],
        denomMinNotionals: [],
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h, e_9, _j, e_10, _k, e_11, _l, e_12, _m, e_13, _o, e_14, _p, e_15, _q, e_16, _r, e_17, _s, e_18, _t, e_19, _u, e_20, _v, e_21, _w, e_22, _x, e_23, _y, e_24, _z, e_25, _0, e_26, _1, e_27, _2, e_28, _3, e_29, _4, e_30, _5, e_31, _6;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            exchange_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _7 = __values(message.spotMarkets), _8 = _7.next(); !_8.done; _8 = _7.next()) {
                var v = _8.value;
                exchange_1.SpotMarket.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_8 && !_8.done && (_a = _7.return)) _a.call(_7);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _9 = __values(message.derivativeMarkets), _10 = _9.next(); !_10.done; _10 = _9.next()) {
                var v = _10.value;
                exchange_1.DerivativeMarket.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_10 && !_10.done && (_b = _9.return)) _b.call(_9);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _11 = __values(message.spotOrderbook), _12 = _11.next(); !_12.done; _12 = _11.next()) {
                var v = _12.value;
                exports.SpotOrderBook.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_12 && !_12.done && (_c = _11.return)) _c.call(_11);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _13 = __values(message.derivativeOrderbook), _14 = _13.next(); !_14.done; _14 = _13.next()) {
                var v = _14.value;
                exports.DerivativeOrderBook.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_14 && !_14.done && (_d = _13.return)) _d.call(_13);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _15 = __values(message.balances), _16 = _15.next(); !_16.done; _16 = _15.next()) {
                var v = _16.value;
                exports.Balance.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_16 && !_16.done && (_e = _15.return)) _e.call(_15);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _17 = __values(message.positions), _18 = _17.next(); !_18.done; _18 = _17.next()) {
                var v = _18.value;
                exports.DerivativePosition.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_18 && !_18.done && (_f = _17.return)) _f.call(_17);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _19 = __values(message.subaccountTradeNonces), _20 = _19.next(); !_20.done; _20 = _19.next()) {
                var v = _20.value;
                exports.SubaccountNonce.encode(v, writer.uint32(66).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_20 && !_20.done && (_g = _19.return)) _g.call(_19);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _21 = __values(message.expiryFuturesMarketInfoState), _22 = _21.next(); !_22.done; _22 = _21.next()) {
                var v = _22.value;
                exports.ExpiryFuturesMarketInfoState.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_22 && !_22.done && (_h = _21.return)) _h.call(_21);
            }
            finally { if (e_8) throw e_8.error; }
        }
        try {
            for (var _23 = __values(message.perpetualMarketInfo), _24 = _23.next(); !_24.done; _24 = _23.next()) {
                var v = _24.value;
                exchange_1.PerpetualMarketInfo.encode(v, writer.uint32(82).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_24 && !_24.done && (_j = _23.return)) _j.call(_23);
            }
            finally { if (e_9) throw e_9.error; }
        }
        try {
            for (var _25 = __values(message.perpetualMarketFundingState), _26 = _25.next(); !_26.done; _26 = _25.next()) {
                var v = _26.value;
                exports.PerpetualMarketFundingState.encode(v, writer.uint32(90).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_26 && !_26.done && (_k = _25.return)) _k.call(_25);
            }
            finally { if (e_10) throw e_10.error; }
        }
        try {
            for (var _27 = __values(message.derivativeMarketSettlementScheduled), _28 = _27.next(); !_28.done; _28 = _27.next()) {
                var v = _28.value;
                exchange_1.DerivativeMarketSettlementInfo.encode(v, writer.uint32(98).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_28 && !_28.done && (_l = _27.return)) _l.call(_27);
            }
            finally { if (e_11) throw e_11.error; }
        }
        if (message.isSpotExchangeEnabled === true) {
            writer.uint32(104).bool(message.isSpotExchangeEnabled);
        }
        if (message.isDerivativesExchangeEnabled === true) {
            writer.uint32(112).bool(message.isDerivativesExchangeEnabled);
        }
        if (message.tradingRewardCampaignInfo !== undefined) {
            exchange_1.TradingRewardCampaignInfo.encode(message.tradingRewardCampaignInfo, writer.uint32(122).fork()).ldelim();
        }
        try {
            for (var _29 = __values(message.tradingRewardPoolCampaignSchedule), _30 = _29.next(); !_30.done; _30 = _29.next()) {
                var v = _30.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(130).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_30 && !_30.done && (_m = _29.return)) _m.call(_29);
            }
            finally { if (e_12) throw e_12.error; }
        }
        try {
            for (var _31 = __values(message.tradingRewardCampaignAccountPoints), _32 = _31.next(); !_32.done; _32 = _31.next()) {
                var v = _32.value;
                exports.TradingRewardCampaignAccountPoints.encode(v, writer.uint32(138).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_32 && !_32.done && (_o = _31.return)) _o.call(_31);
            }
            finally { if (e_13) throw e_13.error; }
        }
        if (message.feeDiscountSchedule !== undefined) {
            exchange_1.FeeDiscountSchedule.encode(message.feeDiscountSchedule, writer.uint32(146).fork()).ldelim();
        }
        try {
            for (var _33 = __values(message.feeDiscountAccountTierTtl), _34 = _33.next(); !_34.done; _34 = _33.next()) {
                var v = _34.value;
                exports.FeeDiscountAccountTierTTL.encode(v, writer.uint32(154).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_34 && !_34.done && (_p = _33.return)) _p.call(_33);
            }
            finally { if (e_14) throw e_14.error; }
        }
        try {
            for (var _35 = __values(message.feeDiscountBucketVolumeAccounts), _36 = _35.next(); !_36.done; _36 = _35.next()) {
                var v = _36.value;
                exports.FeeDiscountBucketVolumeAccounts.encode(v, writer.uint32(162).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_36 && !_36.done && (_q = _35.return)) _q.call(_35);
            }
            finally { if (e_15) throw e_15.error; }
        }
        if (message.isFirstFeeCycleFinished === true) {
            writer.uint32(168).bool(message.isFirstFeeCycleFinished);
        }
        try {
            for (var _37 = __values(message.pendingTradingRewardPoolCampaignSchedule), _38 = _37.next(); !_38.done; _38 = _37.next()) {
                var v = _38.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(178).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_38 && !_38.done && (_r = _37.return)) _r.call(_37);
            }
            finally { if (e_16) throw e_16.error; }
        }
        try {
            for (var _39 = __values(message.pendingTradingRewardCampaignAccountPoints), _40 = _39.next(); !_40.done; _40 = _39.next()) {
                var v = _40.value;
                exports.TradingRewardCampaignAccountPendingPoints.encode(v, writer.uint32(186).fork()).ldelim();
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_40 && !_40.done && (_s = _39.return)) _s.call(_39);
            }
            finally { if (e_17) throw e_17.error; }
        }
        try {
            for (var _41 = __values(message.rewardsOptOutAddresses), _42 = _41.next(); !_42.done; _42 = _41.next()) {
                var v = _42.value;
                writer.uint32(194).string(v);
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_42 && !_42.done && (_t = _41.return)) _t.call(_41);
            }
            finally { if (e_18) throw e_18.error; }
        }
        try {
            for (var _43 = __values(message.historicalTradeRecords), _44 = _43.next(); !_44.done; _44 = _43.next()) {
                var v = _44.value;
                exchange_1.TradeRecords.encode(v, writer.uint32(202).fork()).ldelim();
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_44 && !_44.done && (_u = _43.return)) _u.call(_43);
            }
            finally { if (e_19) throw e_19.error; }
        }
        try {
            for (var _45 = __values(message.binaryOptionsMarkets), _46 = _45.next(); !_46.done; _46 = _45.next()) {
                var v = _46.value;
                exchange_1.BinaryOptionsMarket.encode(v, writer.uint32(210).fork()).ldelim();
            }
        }
        catch (e_20_1) { e_20 = { error: e_20_1 }; }
        finally {
            try {
                if (_46 && !_46.done && (_v = _45.return)) _v.call(_45);
            }
            finally { if (e_20) throw e_20.error; }
        }
        try {
            for (var _47 = __values(message.binaryOptionsMarketIdsScheduledForSettlement), _48 = _47.next(); !_48.done; _48 = _47.next()) {
                var v = _48.value;
                writer.uint32(218).string(v);
            }
        }
        catch (e_21_1) { e_21 = { error: e_21_1 }; }
        finally {
            try {
                if (_48 && !_48.done && (_w = _47.return)) _w.call(_47);
            }
            finally { if (e_21) throw e_21.error; }
        }
        try {
            for (var _49 = __values(message.spotMarketIdsScheduledToForceClose), _50 = _49.next(); !_50.done; _50 = _49.next()) {
                var v = _50.value;
                writer.uint32(226).string(v);
            }
        }
        catch (e_22_1) { e_22 = { error: e_22_1 }; }
        finally {
            try {
                if (_50 && !_50.done && (_x = _49.return)) _x.call(_49);
            }
            finally { if (e_22) throw e_22.error; }
        }
        try {
            for (var _51 = __values(message.denomDecimals), _52 = _51.next(); !_52.done; _52 = _51.next()) {
                var v = _52.value;
                exchange_1.DenomDecimals.encode(v, writer.uint32(234).fork()).ldelim();
            }
        }
        catch (e_23_1) { e_23 = { error: e_23_1 }; }
        finally {
            try {
                if (_52 && !_52.done && (_y = _51.return)) _y.call(_51);
            }
            finally { if (e_23) throw e_23.error; }
        }
        try {
            for (var _53 = __values(message.conditionalDerivativeOrderbooks), _54 = _53.next(); !_54.done; _54 = _53.next()) {
                var v = _54.value;
                exports.ConditionalDerivativeOrderBook.encode(v, writer.uint32(242).fork()).ldelim();
            }
        }
        catch (e_24_1) { e_24 = { error: e_24_1 }; }
        finally {
            try {
                if (_54 && !_54.done && (_z = _53.return)) _z.call(_53);
            }
            finally { if (e_24) throw e_24.error; }
        }
        try {
            for (var _55 = __values(message.marketFeeMultipliers), _56 = _55.next(); !_56.done; _56 = _55.next()) {
                var v = _56.value;
                exchange_1.MarketFeeMultiplier.encode(v, writer.uint32(250).fork()).ldelim();
            }
        }
        catch (e_25_1) { e_25 = { error: e_25_1 }; }
        finally {
            try {
                if (_56 && !_56.done && (_0 = _55.return)) _0.call(_55);
            }
            finally { if (e_25) throw e_25.error; }
        }
        try {
            for (var _57 = __values(message.orderbookSequences), _58 = _57.next(); !_58.done; _58 = _57.next()) {
                var v = _58.value;
                exports.OrderbookSequence.encode(v, writer.uint32(258).fork()).ldelim();
            }
        }
        catch (e_26_1) { e_26 = { error: e_26_1 }; }
        finally {
            try {
                if (_58 && !_58.done && (_1 = _57.return)) _1.call(_57);
            }
            finally { if (e_26) throw e_26.error; }
        }
        try {
            for (var _59 = __values(message.subaccountVolumes), _60 = _59.next(); !_60.done; _60 = _59.next()) {
                var v = _60.value;
                exchange_1.AggregateSubaccountVolumeRecord.encode(v, writer.uint32(266).fork()).ldelim();
            }
        }
        catch (e_27_1) { e_27 = { error: e_27_1 }; }
        finally {
            try {
                if (_60 && !_60.done && (_2 = _59.return)) _2.call(_59);
            }
            finally { if (e_27) throw e_27.error; }
        }
        try {
            for (var _61 = __values(message.marketVolumes), _62 = _61.next(); !_62.done; _62 = _61.next()) {
                var v = _62.value;
                exchange_1.MarketVolume.encode(v, writer.uint32(274).fork()).ldelim();
            }
        }
        catch (e_28_1) { e_28 = { error: e_28_1 }; }
        finally {
            try {
                if (_62 && !_62.done && (_3 = _61.return)) _3.call(_61);
            }
            finally { if (e_28) throw e_28.error; }
        }
        try {
            for (var _63 = __values(message.grantAuthorizations), _64 = _63.next(); !_64.done; _64 = _63.next()) {
                var v = _64.value;
                exports.FullGrantAuthorizations.encode(v, writer.uint32(282).fork()).ldelim();
            }
        }
        catch (e_29_1) { e_29 = { error: e_29_1 }; }
        finally {
            try {
                if (_64 && !_64.done && (_4 = _63.return)) _4.call(_63);
            }
            finally { if (e_29) throw e_29.error; }
        }
        try {
            for (var _65 = __values(message.activeGrants), _66 = _65.next(); !_66.done; _66 = _65.next()) {
                var v = _66.value;
                exports.FullActiveGrant.encode(v, writer.uint32(290).fork()).ldelim();
            }
        }
        catch (e_30_1) { e_30 = { error: e_30_1 }; }
        finally {
            try {
                if (_66 && !_66.done && (_5 = _65.return)) _5.call(_65);
            }
            finally { if (e_30) throw e_30.error; }
        }
        try {
            for (var _67 = __values(message.denomMinNotionals), _68 = _67.next(); !_68.done; _68 = _67.next()) {
                var v = _68.value;
                exchange_1.DenomMinNotional.encode(v, writer.uint32(298).fork()).ldelim();
            }
        }
        catch (e_31_1) { e_31 = { error: e_31_1 }; }
        finally {
            try {
                if (_68 && !_68.done && (_6 = _67.return)) _6.call(_67);
            }
            finally { if (e_31) throw e_31.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = exchange_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.spotMarkets.push(exchange_1.SpotMarket.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.derivativeMarkets.push(exchange_1.DerivativeMarket.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.spotOrderbook.push(exports.SpotOrderBook.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.derivativeOrderbook.push(exports.DerivativeOrderBook.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.balances.push(exports.Balance.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.positions.push(exports.DerivativePosition.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.subaccountTradeNonces.push(exports.SubaccountNonce.decode(reader, reader.uint32()));
                    break;
                case 9:
                    message.expiryFuturesMarketInfoState.push(exports.ExpiryFuturesMarketInfoState.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.perpetualMarketInfo.push(exchange_1.PerpetualMarketInfo.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.perpetualMarketFundingState.push(exports.PerpetualMarketFundingState.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.derivativeMarketSettlementScheduled.push(exchange_1.DerivativeMarketSettlementInfo.decode(reader, reader.uint32()));
                    break;
                case 13:
                    message.isSpotExchangeEnabled = reader.bool();
                    break;
                case 14:
                    message.isDerivativesExchangeEnabled = reader.bool();
                    break;
                case 15:
                    message.tradingRewardCampaignInfo = exchange_1.TradingRewardCampaignInfo.decode(reader, reader.uint32());
                    break;
                case 16:
                    message.tradingRewardPoolCampaignSchedule.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                case 17:
                    message.tradingRewardCampaignAccountPoints.push(exports.TradingRewardCampaignAccountPoints.decode(reader, reader.uint32()));
                    break;
                case 18:
                    message.feeDiscountSchedule = exchange_1.FeeDiscountSchedule.decode(reader, reader.uint32());
                    break;
                case 19:
                    message.feeDiscountAccountTierTtl.push(exports.FeeDiscountAccountTierTTL.decode(reader, reader.uint32()));
                    break;
                case 20:
                    message.feeDiscountBucketVolumeAccounts.push(exports.FeeDiscountBucketVolumeAccounts.decode(reader, reader.uint32()));
                    break;
                case 21:
                    message.isFirstFeeCycleFinished = reader.bool();
                    break;
                case 22:
                    message.pendingTradingRewardPoolCampaignSchedule.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                case 23:
                    message.pendingTradingRewardCampaignAccountPoints.push(exports.TradingRewardCampaignAccountPendingPoints.decode(reader, reader.uint32()));
                    break;
                case 24:
                    message.rewardsOptOutAddresses.push(reader.string());
                    break;
                case 25:
                    message.historicalTradeRecords.push(exchange_1.TradeRecords.decode(reader, reader.uint32()));
                    break;
                case 26:
                    message.binaryOptionsMarkets.push(exchange_1.BinaryOptionsMarket.decode(reader, reader.uint32()));
                    break;
                case 27:
                    message.binaryOptionsMarketIdsScheduledForSettlement.push(reader.string());
                    break;
                case 28:
                    message.spotMarketIdsScheduledToForceClose.push(reader.string());
                    break;
                case 29:
                    message.denomDecimals.push(exchange_1.DenomDecimals.decode(reader, reader.uint32()));
                    break;
                case 30:
                    message.conditionalDerivativeOrderbooks.push(exports.ConditionalDerivativeOrderBook.decode(reader, reader.uint32()));
                    break;
                case 31:
                    message.marketFeeMultipliers.push(exchange_1.MarketFeeMultiplier.decode(reader, reader.uint32()));
                    break;
                case 32:
                    message.orderbookSequences.push(exports.OrderbookSequence.decode(reader, reader.uint32()));
                    break;
                case 33:
                    message.subaccountVolumes.push(exchange_1.AggregateSubaccountVolumeRecord.decode(reader, reader.uint32()));
                    break;
                case 34:
                    message.marketVolumes.push(exchange_1.MarketVolume.decode(reader, reader.uint32()));
                    break;
                case 35:
                    message.grantAuthorizations.push(exports.FullGrantAuthorizations.decode(reader, reader.uint32()));
                    break;
                case 36:
                    message.activeGrants.push(exports.FullActiveGrant.decode(reader, reader.uint32()));
                    break;
                case 37:
                    message.denomMinNotionals.push(exchange_1.DenomMinNotional.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? exchange_1.Params.fromJSON(object.params) : undefined,
            spotMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarkets) ? object.spotMarkets.map(function (e) { return exchange_1.SpotMarket.fromJSON(e); }) : [],
            derivativeMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarkets)
                ? object.derivativeMarkets.map(function (e) { return exchange_1.DerivativeMarket.fromJSON(e); })
                : [],
            spotOrderbook: Array.isArray(object === null || object === void 0 ? void 0 : object.spotOrderbook)
                ? object.spotOrderbook.map(function (e) { return exports.SpotOrderBook.fromJSON(e); })
                : [],
            derivativeOrderbook: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeOrderbook)
                ? object.derivativeOrderbook.map(function (e) { return exports.DerivativeOrderBook.fromJSON(e); })
                : [],
            balances: Array.isArray(object === null || object === void 0 ? void 0 : object.balances) ? object.balances.map(function (e) { return exports.Balance.fromJSON(e); }) : [],
            positions: Array.isArray(object === null || object === void 0 ? void 0 : object.positions)
                ? object.positions.map(function (e) { return exports.DerivativePosition.fromJSON(e); })
                : [],
            subaccountTradeNonces: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountTradeNonces)
                ? object.subaccountTradeNonces.map(function (e) { return exports.SubaccountNonce.fromJSON(e); })
                : [],
            expiryFuturesMarketInfoState: Array.isArray(object === null || object === void 0 ? void 0 : object.expiryFuturesMarketInfoState)
                ? object.expiryFuturesMarketInfoState.map(function (e) { return exports.ExpiryFuturesMarketInfoState.fromJSON(e); })
                : [],
            perpetualMarketInfo: Array.isArray(object === null || object === void 0 ? void 0 : object.perpetualMarketInfo)
                ? object.perpetualMarketInfo.map(function (e) { return exchange_1.PerpetualMarketInfo.fromJSON(e); })
                : [],
            perpetualMarketFundingState: Array.isArray(object === null || object === void 0 ? void 0 : object.perpetualMarketFundingState)
                ? object.perpetualMarketFundingState.map(function (e) { return exports.PerpetualMarketFundingState.fromJSON(e); })
                : [],
            derivativeMarketSettlementScheduled: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarketSettlementScheduled)
                ? object.derivativeMarketSettlementScheduled.map(function (e) { return exchange_1.DerivativeMarketSettlementInfo.fromJSON(e); })
                : [],
            isSpotExchangeEnabled: isSet(object.isSpotExchangeEnabled) ? Boolean(object.isSpotExchangeEnabled) : false,
            isDerivativesExchangeEnabled: isSet(object.isDerivativesExchangeEnabled)
                ? Boolean(object.isDerivativesExchangeEnabled)
                : false,
            tradingRewardCampaignInfo: isSet(object.tradingRewardCampaignInfo)
                ? exchange_1.TradingRewardCampaignInfo.fromJSON(object.tradingRewardCampaignInfo)
                : undefined,
            tradingRewardPoolCampaignSchedule: Array.isArray(object === null || object === void 0 ? void 0 : object.tradingRewardPoolCampaignSchedule)
                ? object.tradingRewardPoolCampaignSchedule.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
            tradingRewardCampaignAccountPoints: Array.isArray(object === null || object === void 0 ? void 0 : object.tradingRewardCampaignAccountPoints)
                ? object.tradingRewardCampaignAccountPoints.map(function (e) { return exports.TradingRewardCampaignAccountPoints.fromJSON(e); })
                : [],
            feeDiscountSchedule: isSet(object.feeDiscountSchedule)
                ? exchange_1.FeeDiscountSchedule.fromJSON(object.feeDiscountSchedule)
                : undefined,
            feeDiscountAccountTierTtl: Array.isArray(object === null || object === void 0 ? void 0 : object.feeDiscountAccountTierTtl)
                ? object.feeDiscountAccountTierTtl.map(function (e) { return exports.FeeDiscountAccountTierTTL.fromJSON(e); })
                : [],
            feeDiscountBucketVolumeAccounts: Array.isArray(object === null || object === void 0 ? void 0 : object.feeDiscountBucketVolumeAccounts)
                ? object.feeDiscountBucketVolumeAccounts.map(function (e) { return exports.FeeDiscountBucketVolumeAccounts.fromJSON(e); })
                : [],
            isFirstFeeCycleFinished: isSet(object.isFirstFeeCycleFinished) ? Boolean(object.isFirstFeeCycleFinished) : false,
            pendingTradingRewardPoolCampaignSchedule: Array.isArray(object === null || object === void 0 ? void 0 : object.pendingTradingRewardPoolCampaignSchedule)
                ? object.pendingTradingRewardPoolCampaignSchedule.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
            pendingTradingRewardCampaignAccountPoints: Array.isArray(object === null || object === void 0 ? void 0 : object.pendingTradingRewardCampaignAccountPoints)
                ? object.pendingTradingRewardCampaignAccountPoints.map(function (e) {
                    return exports.TradingRewardCampaignAccountPendingPoints.fromJSON(e);
                })
                : [],
            rewardsOptOutAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.rewardsOptOutAddresses)
                ? object.rewardsOptOutAddresses.map(function (e) { return String(e); })
                : [],
            historicalTradeRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.historicalTradeRecords)
                ? object.historicalTradeRecords.map(function (e) { return exchange_1.TradeRecords.fromJSON(e); })
                : [],
            binaryOptionsMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsMarkets)
                ? object.binaryOptionsMarkets.map(function (e) { return exchange_1.BinaryOptionsMarket.fromJSON(e); })
                : [],
            binaryOptionsMarketIdsScheduledForSettlement: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsMarketIdsScheduledForSettlement)
                ? object.binaryOptionsMarketIdsScheduledForSettlement.map(function (e) { return String(e); })
                : [],
            spotMarketIdsScheduledToForceClose: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarketIdsScheduledToForceClose)
                ? object.spotMarketIdsScheduledToForceClose.map(function (e) { return String(e); })
                : [],
            denomDecimals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomDecimals)
                ? object.denomDecimals.map(function (e) { return exchange_1.DenomDecimals.fromJSON(e); })
                : [],
            conditionalDerivativeOrderbooks: Array.isArray(object === null || object === void 0 ? void 0 : object.conditionalDerivativeOrderbooks)
                ? object.conditionalDerivativeOrderbooks.map(function (e) { return exports.ConditionalDerivativeOrderBook.fromJSON(e); })
                : [],
            marketFeeMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.marketFeeMultipliers)
                ? object.marketFeeMultipliers.map(function (e) { return exchange_1.MarketFeeMultiplier.fromJSON(e); })
                : [],
            orderbookSequences: Array.isArray(object === null || object === void 0 ? void 0 : object.orderbookSequences)
                ? object.orderbookSequences.map(function (e) { return exports.OrderbookSequence.fromJSON(e); })
                : [],
            subaccountVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.subaccountVolumes)
                ? object.subaccountVolumes.map(function (e) { return exchange_1.AggregateSubaccountVolumeRecord.fromJSON(e); })
                : [],
            marketVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.marketVolumes)
                ? object.marketVolumes.map(function (e) { return exchange_1.MarketVolume.fromJSON(e); })
                : [],
            grantAuthorizations: Array.isArray(object === null || object === void 0 ? void 0 : object.grantAuthorizations)
                ? object.grantAuthorizations.map(function (e) { return exports.FullGrantAuthorizations.fromJSON(e); })
                : [],
            activeGrants: Array.isArray(object === null || object === void 0 ? void 0 : object.activeGrants)
                ? object.activeGrants.map(function (e) { return exports.FullActiveGrant.fromJSON(e); })
                : [],
            denomMinNotionals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomMinNotionals)
                ? object.denomMinNotionals.map(function (e) { return exchange_1.DenomMinNotional.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? exchange_1.Params.toJSON(message.params) : undefined);
        if (message.spotMarkets) {
            obj.spotMarkets = message.spotMarkets.map(function (e) { return e ? exchange_1.SpotMarket.toJSON(e) : undefined; });
        }
        else {
            obj.spotMarkets = [];
        }
        if (message.derivativeMarkets) {
            obj.derivativeMarkets = message.derivativeMarkets.map(function (e) { return e ? exchange_1.DerivativeMarket.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeMarkets = [];
        }
        if (message.spotOrderbook) {
            obj.spotOrderbook = message.spotOrderbook.map(function (e) { return e ? exports.SpotOrderBook.toJSON(e) : undefined; });
        }
        else {
            obj.spotOrderbook = [];
        }
        if (message.derivativeOrderbook) {
            obj.derivativeOrderbook = message.derivativeOrderbook.map(function (e) { return e ? exports.DerivativeOrderBook.toJSON(e) : undefined; });
        }
        else {
            obj.derivativeOrderbook = [];
        }
        if (message.balances) {
            obj.balances = message.balances.map(function (e) { return e ? exports.Balance.toJSON(e) : undefined; });
        }
        else {
            obj.balances = [];
        }
        if (message.positions) {
            obj.positions = message.positions.map(function (e) { return e ? exports.DerivativePosition.toJSON(e) : undefined; });
        }
        else {
            obj.positions = [];
        }
        if (message.subaccountTradeNonces) {
            obj.subaccountTradeNonces = message.subaccountTradeNonces.map(function (e) { return e ? exports.SubaccountNonce.toJSON(e) : undefined; });
        }
        else {
            obj.subaccountTradeNonces = [];
        }
        if (message.expiryFuturesMarketInfoState) {
            obj.expiryFuturesMarketInfoState = message.expiryFuturesMarketInfoState.map(function (e) {
                return e ? exports.ExpiryFuturesMarketInfoState.toJSON(e) : undefined;
            });
        }
        else {
            obj.expiryFuturesMarketInfoState = [];
        }
        if (message.perpetualMarketInfo) {
            obj.perpetualMarketInfo = message.perpetualMarketInfo.map(function (e) { return e ? exchange_1.PerpetualMarketInfo.toJSON(e) : undefined; });
        }
        else {
            obj.perpetualMarketInfo = [];
        }
        if (message.perpetualMarketFundingState) {
            obj.perpetualMarketFundingState = message.perpetualMarketFundingState.map(function (e) {
                return e ? exports.PerpetualMarketFundingState.toJSON(e) : undefined;
            });
        }
        else {
            obj.perpetualMarketFundingState = [];
        }
        if (message.derivativeMarketSettlementScheduled) {
            obj.derivativeMarketSettlementScheduled = message.derivativeMarketSettlementScheduled.map(function (e) {
                return e ? exchange_1.DerivativeMarketSettlementInfo.toJSON(e) : undefined;
            });
        }
        else {
            obj.derivativeMarketSettlementScheduled = [];
        }
        message.isSpotExchangeEnabled !== undefined && (obj.isSpotExchangeEnabled = message.isSpotExchangeEnabled);
        message.isDerivativesExchangeEnabled !== undefined &&
            (obj.isDerivativesExchangeEnabled = message.isDerivativesExchangeEnabled);
        message.tradingRewardCampaignInfo !== undefined &&
            (obj.tradingRewardCampaignInfo = message.tradingRewardCampaignInfo
                ? exchange_1.TradingRewardCampaignInfo.toJSON(message.tradingRewardCampaignInfo)
                : undefined);
        if (message.tradingRewardPoolCampaignSchedule) {
            obj.tradingRewardPoolCampaignSchedule = message.tradingRewardPoolCampaignSchedule.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.tradingRewardPoolCampaignSchedule = [];
        }
        if (message.tradingRewardCampaignAccountPoints) {
            obj.tradingRewardCampaignAccountPoints = message.tradingRewardCampaignAccountPoints.map(function (e) {
                return e ? exports.TradingRewardCampaignAccountPoints.toJSON(e) : undefined;
            });
        }
        else {
            obj.tradingRewardCampaignAccountPoints = [];
        }
        message.feeDiscountSchedule !== undefined && (obj.feeDiscountSchedule = message.feeDiscountSchedule
            ? exchange_1.FeeDiscountSchedule.toJSON(message.feeDiscountSchedule)
            : undefined);
        if (message.feeDiscountAccountTierTtl) {
            obj.feeDiscountAccountTierTtl = message.feeDiscountAccountTierTtl.map(function (e) {
                return e ? exports.FeeDiscountAccountTierTTL.toJSON(e) : undefined;
            });
        }
        else {
            obj.feeDiscountAccountTierTtl = [];
        }
        if (message.feeDiscountBucketVolumeAccounts) {
            obj.feeDiscountBucketVolumeAccounts = message.feeDiscountBucketVolumeAccounts.map(function (e) {
                return e ? exports.FeeDiscountBucketVolumeAccounts.toJSON(e) : undefined;
            });
        }
        else {
            obj.feeDiscountBucketVolumeAccounts = [];
        }
        message.isFirstFeeCycleFinished !== undefined && (obj.isFirstFeeCycleFinished = message.isFirstFeeCycleFinished);
        if (message.pendingTradingRewardPoolCampaignSchedule) {
            obj.pendingTradingRewardPoolCampaignSchedule = message.pendingTradingRewardPoolCampaignSchedule.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.pendingTradingRewardPoolCampaignSchedule = [];
        }
        if (message.pendingTradingRewardCampaignAccountPoints) {
            obj.pendingTradingRewardCampaignAccountPoints = message.pendingTradingRewardCampaignAccountPoints.map(function (e) {
                return e ? exports.TradingRewardCampaignAccountPendingPoints.toJSON(e) : undefined;
            });
        }
        else {
            obj.pendingTradingRewardCampaignAccountPoints = [];
        }
        if (message.rewardsOptOutAddresses) {
            obj.rewardsOptOutAddresses = message.rewardsOptOutAddresses.map(function (e) { return e; });
        }
        else {
            obj.rewardsOptOutAddresses = [];
        }
        if (message.historicalTradeRecords) {
            obj.historicalTradeRecords = message.historicalTradeRecords.map(function (e) { return e ? exchange_1.TradeRecords.toJSON(e) : undefined; });
        }
        else {
            obj.historicalTradeRecords = [];
        }
        if (message.binaryOptionsMarkets) {
            obj.binaryOptionsMarkets = message.binaryOptionsMarkets.map(function (e) { return e ? exchange_1.BinaryOptionsMarket.toJSON(e) : undefined; });
        }
        else {
            obj.binaryOptionsMarkets = [];
        }
        if (message.binaryOptionsMarketIdsScheduledForSettlement) {
            obj.binaryOptionsMarketIdsScheduledForSettlement = message.binaryOptionsMarketIdsScheduledForSettlement.map(function (e) {
                return e;
            });
        }
        else {
            obj.binaryOptionsMarketIdsScheduledForSettlement = [];
        }
        if (message.spotMarketIdsScheduledToForceClose) {
            obj.spotMarketIdsScheduledToForceClose = message.spotMarketIdsScheduledToForceClose.map(function (e) { return e; });
        }
        else {
            obj.spotMarketIdsScheduledToForceClose = [];
        }
        if (message.denomDecimals) {
            obj.denomDecimals = message.denomDecimals.map(function (e) { return e ? exchange_1.DenomDecimals.toJSON(e) : undefined; });
        }
        else {
            obj.denomDecimals = [];
        }
        if (message.conditionalDerivativeOrderbooks) {
            obj.conditionalDerivativeOrderbooks = message.conditionalDerivativeOrderbooks.map(function (e) {
                return e ? exports.ConditionalDerivativeOrderBook.toJSON(e) : undefined;
            });
        }
        else {
            obj.conditionalDerivativeOrderbooks = [];
        }
        if (message.marketFeeMultipliers) {
            obj.marketFeeMultipliers = message.marketFeeMultipliers.map(function (e) { return e ? exchange_1.MarketFeeMultiplier.toJSON(e) : undefined; });
        }
        else {
            obj.marketFeeMultipliers = [];
        }
        if (message.orderbookSequences) {
            obj.orderbookSequences = message.orderbookSequences.map(function (e) { return e ? exports.OrderbookSequence.toJSON(e) : undefined; });
        }
        else {
            obj.orderbookSequences = [];
        }
        if (message.subaccountVolumes) {
            obj.subaccountVolumes = message.subaccountVolumes.map(function (e) {
                return e ? exchange_1.AggregateSubaccountVolumeRecord.toJSON(e) : undefined;
            });
        }
        else {
            obj.subaccountVolumes = [];
        }
        if (message.marketVolumes) {
            obj.marketVolumes = message.marketVolumes.map(function (e) { return e ? exchange_1.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.marketVolumes = [];
        }
        if (message.grantAuthorizations) {
            obj.grantAuthorizations = message.grantAuthorizations.map(function (e) {
                return e ? exports.FullGrantAuthorizations.toJSON(e) : undefined;
            });
        }
        else {
            obj.grantAuthorizations = [];
        }
        if (message.activeGrants) {
            obj.activeGrants = message.activeGrants.map(function (e) { return e ? exports.FullActiveGrant.toJSON(e) : undefined; });
        }
        else {
            obj.activeGrants = [];
        }
        if (message.denomMinNotionals) {
            obj.denomMinNotionals = message.denomMinNotionals.map(function (e) { return e ? exchange_1.DenomMinNotional.toJSON(e) : undefined; });
        }
        else {
            obj.denomMinNotionals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? exchange_1.Params.fromPartial(object.params)
            : undefined;
        message.spotMarkets = ((_a = object.spotMarkets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.SpotMarket.fromPartial(e); })) || [];
        message.derivativeMarkets = ((_b = object.derivativeMarkets) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.DerivativeMarket.fromPartial(e); })) || [];
        message.spotOrderbook = ((_c = object.spotOrderbook) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.SpotOrderBook.fromPartial(e); })) || [];
        message.derivativeOrderbook = ((_d = object.derivativeOrderbook) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.DerivativeOrderBook.fromPartial(e); })) || [];
        message.balances = ((_e = object.balances) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.Balance.fromPartial(e); })) || [];
        message.positions = ((_f = object.positions) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.DerivativePosition.fromPartial(e); })) || [];
        message.subaccountTradeNonces = ((_g = object.subaccountTradeNonces) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.SubaccountNonce.fromPartial(e); })) || [];
        message.expiryFuturesMarketInfoState =
            ((_h = object.expiryFuturesMarketInfoState) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.ExpiryFuturesMarketInfoState.fromPartial(e); })) || [];
        message.perpetualMarketInfo = ((_j = object.perpetualMarketInfo) === null || _j === void 0 ? void 0 : _j.map(function (e) { return exchange_1.PerpetualMarketInfo.fromPartial(e); })) || [];
        message.perpetualMarketFundingState =
            ((_k = object.perpetualMarketFundingState) === null || _k === void 0 ? void 0 : _k.map(function (e) { return exports.PerpetualMarketFundingState.fromPartial(e); })) || [];
        message.derivativeMarketSettlementScheduled =
            ((_l = object.derivativeMarketSettlementScheduled) === null || _l === void 0 ? void 0 : _l.map(function (e) { return exchange_1.DerivativeMarketSettlementInfo.fromPartial(e); })) || [];
        message.isSpotExchangeEnabled = (_m = object.isSpotExchangeEnabled) !== null && _m !== void 0 ? _m : false;
        message.isDerivativesExchangeEnabled = (_o = object.isDerivativesExchangeEnabled) !== null && _o !== void 0 ? _o : false;
        message.tradingRewardCampaignInfo =
            (object.tradingRewardCampaignInfo !== undefined && object.tradingRewardCampaignInfo !== null)
                ? exchange_1.TradingRewardCampaignInfo.fromPartial(object.tradingRewardCampaignInfo)
                : undefined;
        message.tradingRewardPoolCampaignSchedule =
            ((_p = object.tradingRewardPoolCampaignSchedule) === null || _p === void 0 ? void 0 : _p.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        message.tradingRewardCampaignAccountPoints =
            ((_q = object.tradingRewardCampaignAccountPoints) === null || _q === void 0 ? void 0 : _q.map(function (e) { return exports.TradingRewardCampaignAccountPoints.fromPartial(e); })) || [];
        message.feeDiscountSchedule = (object.feeDiscountSchedule !== undefined && object.feeDiscountSchedule !== null)
            ? exchange_1.FeeDiscountSchedule.fromPartial(object.feeDiscountSchedule)
            : undefined;
        message.feeDiscountAccountTierTtl =
            ((_r = object.feeDiscountAccountTierTtl) === null || _r === void 0 ? void 0 : _r.map(function (e) { return exports.FeeDiscountAccountTierTTL.fromPartial(e); })) || [];
        message.feeDiscountBucketVolumeAccounts =
            ((_s = object.feeDiscountBucketVolumeAccounts) === null || _s === void 0 ? void 0 : _s.map(function (e) { return exports.FeeDiscountBucketVolumeAccounts.fromPartial(e); })) || [];
        message.isFirstFeeCycleFinished = (_t = object.isFirstFeeCycleFinished) !== null && _t !== void 0 ? _t : false;
        message.pendingTradingRewardPoolCampaignSchedule =
            ((_u = object.pendingTradingRewardPoolCampaignSchedule) === null || _u === void 0 ? void 0 : _u.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        message.pendingTradingRewardCampaignAccountPoints =
            ((_v = object.pendingTradingRewardCampaignAccountPoints) === null || _v === void 0 ? void 0 : _v.map(function (e) {
                return exports.TradingRewardCampaignAccountPendingPoints.fromPartial(e);
            })) || [];
        message.rewardsOptOutAddresses = ((_w = object.rewardsOptOutAddresses) === null || _w === void 0 ? void 0 : _w.map(function (e) { return e; })) || [];
        message.historicalTradeRecords = ((_x = object.historicalTradeRecords) === null || _x === void 0 ? void 0 : _x.map(function (e) { return exchange_1.TradeRecords.fromPartial(e); })) || [];
        message.binaryOptionsMarkets = ((_y = object.binaryOptionsMarkets) === null || _y === void 0 ? void 0 : _y.map(function (e) { return exchange_1.BinaryOptionsMarket.fromPartial(e); })) || [];
        message.binaryOptionsMarketIdsScheduledForSettlement =
            ((_z = object.binaryOptionsMarketIdsScheduledForSettlement) === null || _z === void 0 ? void 0 : _z.map(function (e) { return e; })) || [];
        message.spotMarketIdsScheduledToForceClose = ((_0 = object.spotMarketIdsScheduledToForceClose) === null || _0 === void 0 ? void 0 : _0.map(function (e) { return e; })) || [];
        message.denomDecimals = ((_1 = object.denomDecimals) === null || _1 === void 0 ? void 0 : _1.map(function (e) { return exchange_1.DenomDecimals.fromPartial(e); })) || [];
        message.conditionalDerivativeOrderbooks =
            ((_2 = object.conditionalDerivativeOrderbooks) === null || _2 === void 0 ? void 0 : _2.map(function (e) { return exports.ConditionalDerivativeOrderBook.fromPartial(e); })) || [];
        message.marketFeeMultipliers = ((_3 = object.marketFeeMultipliers) === null || _3 === void 0 ? void 0 : _3.map(function (e) { return exchange_1.MarketFeeMultiplier.fromPartial(e); })) || [];
        message.orderbookSequences = ((_4 = object.orderbookSequences) === null || _4 === void 0 ? void 0 : _4.map(function (e) { return exports.OrderbookSequence.fromPartial(e); })) || [];
        message.subaccountVolumes = ((_5 = object.subaccountVolumes) === null || _5 === void 0 ? void 0 : _5.map(function (e) { return exchange_1.AggregateSubaccountVolumeRecord.fromPartial(e); })) ||
            [];
        message.marketVolumes = ((_6 = object.marketVolumes) === null || _6 === void 0 ? void 0 : _6.map(function (e) { return exchange_1.MarketVolume.fromPartial(e); })) || [];
        message.grantAuthorizations = ((_7 = object.grantAuthorizations) === null || _7 === void 0 ? void 0 : _7.map(function (e) { return exports.FullGrantAuthorizations.fromPartial(e); })) || [];
        message.activeGrants = ((_8 = object.activeGrants) === null || _8 === void 0 ? void 0 : _8.map(function (e) { return exports.FullActiveGrant.fromPartial(e); })) || [];
        message.denomMinNotionals = ((_9 = object.denomMinNotionals) === null || _9 === void 0 ? void 0 : _9.map(function (e) { return exchange_1.DenomMinNotional.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOrderbookSequence() {
    return { sequence: "0", marketId: "" };
}
exports.OrderbookSequence = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.sequence !== "0") {
            writer.uint32(8).uint64(message.sequence);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOrderbookSequence();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sequence = longToString(reader.uint64());
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            sequence: isSet(object.sequence) ? String(object.sequence) : "0",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.sequence !== undefined && (obj.sequence = message.sequence);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.OrderbookSequence.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseOrderbookSequence();
        message.sequence = (_a = object.sequence) !== null && _a !== void 0 ? _a : "0";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseFeeDiscountAccountTierTTL() {
    return { account: "", tierTtl: undefined };
}
exports.FeeDiscountAccountTierTTL = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.tierTtl !== undefined) {
            exchange_1.FeeDiscountTierTTL.encode(message.tierTtl, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountAccountTierTTL();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.tierTtl = exchange_1.FeeDiscountTierTTL.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            tierTtl: isSet(object.tierTtl) ? exchange_1.FeeDiscountTierTTL.fromJSON(object.tierTtl) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.tierTtl !== undefined &&
            (obj.tierTtl = message.tierTtl ? exchange_1.FeeDiscountTierTTL.toJSON(message.tierTtl) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountAccountTierTTL.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseFeeDiscountAccountTierTTL();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.tierTtl = (object.tierTtl !== undefined && object.tierTtl !== null)
            ? exchange_1.FeeDiscountTierTTL.fromPartial(object.tierTtl)
            : undefined;
        return message;
    },
};
function createBaseFeeDiscountBucketVolumeAccounts() {
    return { bucketStartTimestamp: "0", accountVolume: [] };
}
exports.FeeDiscountBucketVolumeAccounts = {
    encode: function (message, writer) {
        var e_32, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.bucketStartTimestamp !== "0") {
            writer.uint32(8).int64(message.bucketStartTimestamp);
        }
        try {
            for (var _b = __values(message.accountVolume), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.AccountVolume.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_32_1) { e_32 = { error: e_32_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_32) throw e_32.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountBucketVolumeAccounts();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bucketStartTimestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.accountVolume.push(exports.AccountVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            bucketStartTimestamp: isSet(object.bucketStartTimestamp) ? String(object.bucketStartTimestamp) : "0",
            accountVolume: Array.isArray(object === null || object === void 0 ? void 0 : object.accountVolume)
                ? object.accountVolume.map(function (e) { return exports.AccountVolume.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.bucketStartTimestamp !== undefined && (obj.bucketStartTimestamp = message.bucketStartTimestamp);
        if (message.accountVolume) {
            obj.accountVolume = message.accountVolume.map(function (e) { return e ? exports.AccountVolume.toJSON(e) : undefined; });
        }
        else {
            obj.accountVolume = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountBucketVolumeAccounts.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeeDiscountBucketVolumeAccounts();
        message.bucketStartTimestamp = (_a = object.bucketStartTimestamp) !== null && _a !== void 0 ? _a : "0";
        message.accountVolume = ((_b = object.accountVolume) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.AccountVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseAccountVolume() {
    return { account: "", volume: "" };
}
exports.AccountVolume = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.volume !== "") {
            writer.uint32(18).string(message.volume);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAccountVolume();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.volume = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            volume: isSet(object.volume) ? String(object.volume) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.volume !== undefined && (obj.volume = message.volume);
        return obj;
    },
    create: function (base) {
        return exports.AccountVolume.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAccountVolume();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.volume = (_b = object.volume) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTradingRewardCampaignAccountPoints() {
    return { account: "", points: "" };
}
exports.TradingRewardCampaignAccountPoints = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.points !== "") {
            writer.uint32(18).string(message.points);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignAccountPoints();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.points = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            points: isSet(object.points) ? String(object.points) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.points !== undefined && (obj.points = message.points);
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignAccountPoints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTradingRewardCampaignAccountPoints();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.points = (_b = object.points) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTradingRewardCampaignAccountPendingPoints() {
    return { rewardPoolStartTimestamp: "0", accountPoints: [] };
}
exports.TradingRewardCampaignAccountPendingPoints = {
    encode: function (message, writer) {
        var e_33, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.rewardPoolStartTimestamp !== "0") {
            writer.uint32(8).int64(message.rewardPoolStartTimestamp);
        }
        try {
            for (var _b = __values(message.accountPoints), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TradingRewardCampaignAccountPoints.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_33_1) { e_33 = { error: e_33_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_33) throw e_33.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignAccountPendingPoints();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rewardPoolStartTimestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.accountPoints.push(exports.TradingRewardCampaignAccountPoints.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            rewardPoolStartTimestamp: isSet(object.rewardPoolStartTimestamp) ? String(object.rewardPoolStartTimestamp) : "0",
            accountPoints: Array.isArray(object === null || object === void 0 ? void 0 : object.accountPoints)
                ? object.accountPoints.map(function (e) { return exports.TradingRewardCampaignAccountPoints.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.rewardPoolStartTimestamp !== undefined && (obj.rewardPoolStartTimestamp = message.rewardPoolStartTimestamp);
        if (message.accountPoints) {
            obj.accountPoints = message.accountPoints.map(function (e) {
                return e ? exports.TradingRewardCampaignAccountPoints.toJSON(e) : undefined;
            });
        }
        else {
            obj.accountPoints = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignAccountPendingPoints.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTradingRewardCampaignAccountPendingPoints();
        message.rewardPoolStartTimestamp = (_a = object.rewardPoolStartTimestamp) !== null && _a !== void 0 ? _a : "0";
        message.accountPoints = ((_b = object.accountPoints) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.TradingRewardCampaignAccountPoints.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSpotOrderBook() {
    return { marketId: "", isBuySide: false, orders: [] };
}
exports.SpotOrderBook = {
    encode: function (message, writer) {
        var e_34, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuySide === true) {
            writer.uint32(16).bool(message.isBuySide);
        }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.SpotLimitOrder.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_34_1) { e_34 = { error: e_34_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_34) throw e_34.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotOrderBook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuySide = reader.bool();
                    break;
                case 3:
                    message.orders.push(exchange_1.SpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuySide: isSet(object.isBuySide) ? Boolean(object.isBuySide) : false,
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exchange_1.SpotLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuySide !== undefined && (obj.isBuySide = message.isBuySide);
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exchange_1.SpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SpotOrderBook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseSpotOrderBook();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuySide = (_b = object.isBuySide) !== null && _b !== void 0 ? _b : false;
        message.orders = ((_c = object.orders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.SpotLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseDerivativeOrderBook() {
    return { marketId: "", isBuySide: false, orders: [] };
}
exports.DerivativeOrderBook = {
    encode: function (message, writer) {
        var e_35, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.isBuySide === true) {
            writer.uint32(16).bool(message.isBuySide);
        }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DerivativeLimitOrder.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_35_1) { e_35 = { error: e_35_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_35) throw e_35.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeOrderBook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.isBuySide = reader.bool();
                    break;
                case 3:
                    message.orders.push(exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuySide: isSet(object.isBuySide) ? Boolean(object.isBuySide) : false,
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exchange_1.DerivativeLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuySide !== undefined && (obj.isBuySide = message.isBuySide);
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exchange_1.DerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.DerivativeOrderBook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseDerivativeOrderBook();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuySide = (_b = object.isBuySide) !== null && _b !== void 0 ? _b : false;
        message.orders = ((_c = object.orders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.DerivativeLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseConditionalDerivativeOrderBook() {
    return { marketId: "", limitBuyOrders: [], marketBuyOrders: [], limitSellOrders: [], marketSellOrders: [] };
}
exports.ConditionalDerivativeOrderBook = {
    encode: function (message, writer) {
        var e_36, _a, e_37, _b, e_38, _c, e_39, _d;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        try {
            for (var _e = __values(message.limitBuyOrders), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.DerivativeLimitOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_36_1) { e_36 = { error: e_36_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            }
            finally { if (e_36) throw e_36.error; }
        }
        try {
            for (var _g = __values(message.marketBuyOrders), _h = _g.next(); !_h.done; _h = _g.next()) {
                var v = _h.value;
                exchange_1.DerivativeMarketOrder.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_37_1) { e_37 = { error: e_37_1 }; }
        finally {
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            }
            finally { if (e_37) throw e_37.error; }
        }
        try {
            for (var _j = __values(message.limitSellOrders), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                exchange_1.DerivativeLimitOrder.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_38_1) { e_38 = { error: e_38_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            }
            finally { if (e_38) throw e_38.error; }
        }
        try {
            for (var _l = __values(message.marketSellOrders), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                exchange_1.DerivativeMarketOrder.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_39_1) { e_39 = { error: e_39_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            }
            finally { if (e_39) throw e_39.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseConditionalDerivativeOrderBook();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.limitBuyOrders.push(exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.marketBuyOrders.push(exchange_1.DerivativeMarketOrder.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.limitSellOrders.push(exchange_1.DerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.marketSellOrders.push(exchange_1.DerivativeMarketOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            limitBuyOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.limitBuyOrders)
                ? object.limitBuyOrders.map(function (e) { return exchange_1.DerivativeLimitOrder.fromJSON(e); })
                : [],
            marketBuyOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.marketBuyOrders)
                ? object.marketBuyOrders.map(function (e) { return exchange_1.DerivativeMarketOrder.fromJSON(e); })
                : [],
            limitSellOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.limitSellOrders)
                ? object.limitSellOrders.map(function (e) { return exchange_1.DerivativeLimitOrder.fromJSON(e); })
                : [],
            marketSellOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.marketSellOrders)
                ? object.marketSellOrders.map(function (e) { return exchange_1.DerivativeMarketOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        if (message.limitBuyOrders) {
            obj.limitBuyOrders = message.limitBuyOrders.map(function (e) { return e ? exchange_1.DerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.limitBuyOrders = [];
        }
        if (message.marketBuyOrders) {
            obj.marketBuyOrders = message.marketBuyOrders.map(function (e) { return e ? exchange_1.DerivativeMarketOrder.toJSON(e) : undefined; });
        }
        else {
            obj.marketBuyOrders = [];
        }
        if (message.limitSellOrders) {
            obj.limitSellOrders = message.limitSellOrders.map(function (e) { return e ? exchange_1.DerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.limitSellOrders = [];
        }
        if (message.marketSellOrders) {
            obj.marketSellOrders = message.marketSellOrders.map(function (e) { return e ? exchange_1.DerivativeMarketOrder.toJSON(e) : undefined; });
        }
        else {
            obj.marketSellOrders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.ConditionalDerivativeOrderBook.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseConditionalDerivativeOrderBook();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.limitBuyOrders = ((_b = object.limitBuyOrders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.DerivativeLimitOrder.fromPartial(e); })) || [];
        message.marketBuyOrders = ((_c = object.marketBuyOrders) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.DerivativeMarketOrder.fromPartial(e); })) || [];
        message.limitSellOrders = ((_d = object.limitSellOrders) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exchange_1.DerivativeLimitOrder.fromPartial(e); })) || [];
        message.marketSellOrders = ((_e = object.marketSellOrders) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exchange_1.DerivativeMarketOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseBalance() {
    return { subaccountId: "", denom: "", deposits: undefined };
}
exports.Balance = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.deposits !== undefined) {
            exchange_1.Deposit.encode(message.deposits, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.deposits = exchange_1.Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            deposits: isSet(object.deposits) ? exchange_1.Deposit.fromJSON(object.deposits) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.denom !== undefined && (obj.denom = message.denom);
        message.deposits !== undefined && (obj.deposits = message.deposits ? exchange_1.Deposit.toJSON(message.deposits) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Balance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseBalance();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.deposits = (object.deposits !== undefined && object.deposits !== null)
            ? exchange_1.Deposit.fromPartial(object.deposits)
            : undefined;
        return message;
    },
};
function createBaseDerivativePosition() {
    return { subaccountId: "", marketId: "", position: undefined };
}
exports.DerivativePosition = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.position !== undefined) {
            exchange_1.Position.encode(message.position, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.position = exchange_1.Position.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            position: isSet(object.position) ? exchange_1.Position.fromJSON(object.position) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.position !== undefined && (obj.position = message.position ? exchange_1.Position.toJSON(message.position) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.DerivativePosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseDerivativePosition();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.position = (object.position !== undefined && object.position !== null)
            ? exchange_1.Position.fromPartial(object.position)
            : undefined;
        return message;
    },
};
function createBaseSubaccountNonce() {
    return { subaccountId: "", subaccountTradeNonce: undefined };
}
exports.SubaccountNonce = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.subaccountTradeNonce !== undefined) {
            exchange_1.SubaccountTradeNonce.encode(message.subaccountTradeNonce, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountNonce();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.subaccountTradeNonce = exchange_1.SubaccountTradeNonce.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            subaccountTradeNonce: isSet(object.subaccountTradeNonce)
                ? exchange_1.SubaccountTradeNonce.fromJSON(object.subaccountTradeNonce)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.subaccountTradeNonce !== undefined && (obj.subaccountTradeNonce = message.subaccountTradeNonce
            ? exchange_1.SubaccountTradeNonce.toJSON(message.subaccountTradeNonce)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SubaccountNonce.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseSubaccountNonce();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.subaccountTradeNonce = (object.subaccountTradeNonce !== undefined && object.subaccountTradeNonce !== null)
            ? exchange_1.SubaccountTradeNonce.fromPartial(object.subaccountTradeNonce)
            : undefined;
        return message;
    },
};
function createBaseExpiryFuturesMarketInfoState() {
    return { marketId: "", marketInfo: undefined };
}
exports.ExpiryFuturesMarketInfoState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.marketInfo !== undefined) {
            exchange_1.ExpiryFuturesMarketInfo.encode(message.marketInfo, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExpiryFuturesMarketInfoState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.marketInfo = exchange_1.ExpiryFuturesMarketInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            marketInfo: isSet(object.marketInfo) ? exchange_1.ExpiryFuturesMarketInfo.fromJSON(object.marketInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.marketInfo !== undefined &&
            (obj.marketInfo = message.marketInfo ? exchange_1.ExpiryFuturesMarketInfo.toJSON(message.marketInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ExpiryFuturesMarketInfoState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseExpiryFuturesMarketInfoState();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.marketInfo = (object.marketInfo !== undefined && object.marketInfo !== null)
            ? exchange_1.ExpiryFuturesMarketInfo.fromPartial(object.marketInfo)
            : undefined;
        return message;
    },
};
function createBasePerpetualMarketFundingState() {
    return { marketId: "", funding: undefined };
}
exports.PerpetualMarketFundingState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.funding !== undefined) {
            exchange_1.PerpetualMarketFunding.encode(message.funding, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePerpetualMarketFundingState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.funding = exchange_1.PerpetualMarketFunding.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            funding: isSet(object.funding) ? exchange_1.PerpetualMarketFunding.fromJSON(object.funding) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.funding !== undefined &&
            (obj.funding = message.funding ? exchange_1.PerpetualMarketFunding.toJSON(message.funding) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.PerpetualMarketFundingState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBasePerpetualMarketFundingState();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.funding = (object.funding !== undefined && object.funding !== null)
            ? exchange_1.PerpetualMarketFunding.fromPartial(object.funding)
            : undefined;
        return message;
    },
};
function createBaseFullGrantAuthorizations() {
    return { granter: "", totalGrantAmount: "", lastDelegationsCheckedTime: "0", grants: [] };
}
exports.FullGrantAuthorizations = {
    encode: function (message, writer) {
        var e_40, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        if (message.totalGrantAmount !== "") {
            writer.uint32(18).string(message.totalGrantAmount);
        }
        if (message.lastDelegationsCheckedTime !== "0") {
            writer.uint32(24).int64(message.lastDelegationsCheckedTime);
        }
        try {
            for (var _b = __values(message.grants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.GrantAuthorization.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_40_1) { e_40 = { error: e_40_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_40) throw e_40.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFullGrantAuthorizations();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                case 2:
                    message.totalGrantAmount = reader.string();
                    break;
                case 3:
                    message.lastDelegationsCheckedTime = longToString(reader.int64());
                    break;
                case 4:
                    message.grants.push(exchange_1.GrantAuthorization.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            granter: isSet(object.granter) ? String(object.granter) : "",
            totalGrantAmount: isSet(object.totalGrantAmount) ? String(object.totalGrantAmount) : "",
            lastDelegationsCheckedTime: isSet(object.lastDelegationsCheckedTime)
                ? String(object.lastDelegationsCheckedTime)
                : "0",
            grants: Array.isArray(object === null || object === void 0 ? void 0 : object.grants) ? object.grants.map(function (e) { return exchange_1.GrantAuthorization.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        message.totalGrantAmount !== undefined && (obj.totalGrantAmount = message.totalGrantAmount);
        message.lastDelegationsCheckedTime !== undefined &&
            (obj.lastDelegationsCheckedTime = message.lastDelegationsCheckedTime);
        if (message.grants) {
            obj.grants = message.grants.map(function (e) { return e ? exchange_1.GrantAuthorization.toJSON(e) : undefined; });
        }
        else {
            obj.grants = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.FullGrantAuthorizations.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseFullGrantAuthorizations();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        message.totalGrantAmount = (_b = object.totalGrantAmount) !== null && _b !== void 0 ? _b : "";
        message.lastDelegationsCheckedTime = (_c = object.lastDelegationsCheckedTime) !== null && _c !== void 0 ? _c : "0";
        message.grants = ((_d = object.grants) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exchange_1.GrantAuthorization.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseFullActiveGrant() {
    return { grantee: "", activeGrant: undefined };
}
exports.FullActiveGrant = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grantee !== "") {
            writer.uint32(10).string(message.grantee);
        }
        if (message.activeGrant !== undefined) {
            exchange_1.ActiveGrant.encode(message.activeGrant, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFullActiveGrant();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grantee = reader.string();
                    break;
                case 2:
                    message.activeGrant = exchange_1.ActiveGrant.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            grantee: isSet(object.grantee) ? String(object.grantee) : "",
            activeGrant: isSet(object.activeGrant) ? exchange_1.ActiveGrant.fromJSON(object.activeGrant) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.grantee !== undefined && (obj.grantee = message.grantee);
        message.activeGrant !== undefined &&
            (obj.activeGrant = message.activeGrant ? exchange_1.ActiveGrant.toJSON(message.activeGrant) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FullActiveGrant.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseFullActiveGrant();
        message.grantee = (_a = object.grantee) !== null && _a !== void 0 ? _a : "";
        message.activeGrant = (object.activeGrant !== undefined && object.activeGrant !== null)
            ? exchange_1.ActiveGrant.fromPartial(object.activeGrant)
            : undefined;
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
