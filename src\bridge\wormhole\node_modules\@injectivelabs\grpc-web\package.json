{"name": "@injectivelabs/grpc-web", "version": "0.0.1", "description": "gRPC-Web client for browsers (JS/TS)", "main": "dist/grpc-web-client.js", "browser": "dist/grpc-web-client.umd.js", "types": "dist/typings/index.d.ts", "scripts": {"clean": "rm -rf dist", "postbootstrap": "npm run lib:build", "lib:build": "npm run clean && webpack"}, "publishConfig": {"access": "public"}, "author": "Improbable", "license": "Apache-2.0", "repository": {"type": "git", "url": "github.com/improbable-eng/grpc-web"}, "keywords": ["grpc", "grpc-web", "protobuf", "typescript", "ts"], "files": ["dist"], "peerDependencies": {"google-protobuf": "^3.14.0"}, "dependencies": {"browser-headers": "^0.4.1"}, "devDependencies": {"@types/google-protobuf": "^3.7.4", "@types/node": "^14.14.22", "google-protobuf": "^3.14.0", "ts-loader": "^8.0.14", "typescript": "4.1.3", "webpack": "^5.19.0", "webpack-cli": "^4.4.0"}}