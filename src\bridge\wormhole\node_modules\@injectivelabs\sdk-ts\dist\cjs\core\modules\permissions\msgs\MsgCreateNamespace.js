"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Messages
 */
class MsgCreateNamespace extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgCreateNamespace(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgCreateNamespace.create();
        message.sender = params.sender;
        const namespace = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.Namespace.create();
        namespace.denom = params.namespace.denom;
        namespace.contractHook = params.namespace.contractHook;
        const rolePermissions = params.namespace.rolePermissions.map((rolePermission) => {
            const permission = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.Role.create();
            permission.name = rolePermission.name;
            permission.roleId = rolePermission.roleId;
            permission.permissions = rolePermission.permissions;
            return permission;
        }) || [];
        namespace.rolePermissions = rolePermissions;
        const actorRoles = params.namespace.actorRoles.map((actorRole) => {
            const role = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.ActorRoles.create();
            role.roles = actorRole.roles;
            role.actor = actorRole.actor;
            return role;
        });
        namespace.actorRoles = actorRoles;
        const roleManagers = params.namespace.roleManagers.map((roleManager) => {
            const role = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.RoleManager.create();
            role.roles = roleManager.roles;
            role.manager = roleManager.manager;
            return role;
        });
        namespace.roleManagers = roleManagers;
        const policyStatuses = params.namespace.policyStatuses.map((policyStatus) => {
            const policy = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.PolicyStatus.create();
            policy.action = policyStatus.action;
            policy.isDisabled = policyStatus.isDisabled;
            policy.isSealed = policyStatus.isSealed;
            return policy;
        });
        namespace.policyStatuses = policyStatuses;
        const policyManagerCapabilities = params.namespace.policyManagerCapabilities.map((policyManagerCapability) => {
            const capability = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability.create();
            capability.manager = policyManagerCapability.manager;
            capability.action = policyManagerCapability.action;
            capability.canDisable = policyManagerCapability.canDisable;
            capability.canSeal = policyManagerCapability.canSeal;
            return capability;
        });
        namespace.policyManagerCapabilities = policyManagerCapabilities;
        message.namespace = namespace;
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgCreateNamespace.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.permissions.v1beta1.MsgCreateNamespace',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = (0, snakecase_keys_1.default)({
            ...proto,
            namespace: {
                ...proto.namespace,
                policyStatuses: proto.namespace?.policyStatuses || [],
                policyManagerCapabilities: proto.namespace?.policyManagerCapabilities || [],
            },
        });
        return {
            type: 'permissions/MsgCreateNamespace',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.permissions.v1beta1.MsgCreateNamespace',
            ...value,
        };
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712_v1 is not supported for MsgCreateNamespace. Please use EIP712_v2'));
    }
    toEip712V2() {
        const web3gw = this.toWeb3Gw();
        const namespace = web3gw.namespace;
        const policyStatuses = (namespace?.policy_statuses || []).map((policyStatus) => ({
            ...policyStatus,
            action: core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.actionToJSON(policyStatus.action),
        }));
        const policyManagerCapabilities = (namespace?.policy_manager_capabilities || []).map((policyManagerCapability) => ({
            ...policyManagerCapability,
            action: core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.actionToJSON(policyManagerCapability.action),
        }));
        const messageAdjusted = {
            ...web3gw,
            namespace: {
                denom: namespace.denom,
                contract_hook: namespace.contract_hook,
                role_permissions: namespace.role_permissions,
                actor_roles: namespace.actor_roles,
                role_managers: namespace.role_managers,
                policy_statuses: policyStatuses,
                policy_manager_capabilities: policyManagerCapabilities,
            },
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.permissions.v1beta1.MsgCreateNamespace',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgCreateNamespace.encode(this.toProto()).finish();
    }
}
exports.default = MsgCreateNamespace;
