import { IndexerRestDerivativesChronosApi } from './rest/IndexerRestDerivativesChronosApi.js';
import { IndexerRestExplorerApi } from './rest/IndexerRestExplorerApi.js';
import { IndexerRestSpotChronosApi } from './rest/IndexerRestSpotChronosApi.js';
/**
 * @category Indexer Grpc API
 * @hidden
 */
export declare class IndexerRestClient {
    derivativesChronos: IndexerRestDerivativesChronosApi;
    spotChronos: IndexerRestSpotChronosApi;
    explorer: IndexerRestExplorerApi;
    constructor(endpoints: {
        indexerApi: string;
        chronosApi?: string;
    });
}
