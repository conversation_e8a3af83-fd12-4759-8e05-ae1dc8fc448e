import { spawn } from "child_process";
import { existsSync } from "fs";
import { getOS } from "../utils/getUserOs.js";
import { getLocalBinPath } from "../utils/getLocalBinPath.js";
export const runCLI = async (args = []) => {
    const path = getLocalBinPath();
    if (!existsSync(path)) {
        console.log("Aptos CLI not installed, run `npx aptos --install` to install");
        return;
    }
    const os = getOS();
    spawn(path, args, {
        stdio: "inherit",
        shell: os === "Windows",
    });
};
//# sourceMappingURL=run.js.map