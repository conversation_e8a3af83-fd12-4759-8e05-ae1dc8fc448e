2025-05-19 09:02:29,575 - INFO - ================================================================================
2025-05-19 09:02:29,575 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-19 09:02:29
2025-05-19 09:02:29,575 - INFO - 链: ethereum, 投入金额: 207.55 USDT
2025-05-19 09:02:29,576 - INFO - 代币地址: ******************************************
2025-05-19 09:02:29,576 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:207.55USDT
2025-05-19 09:02:29,576 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 09:02:29,576 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行207.55USDT买入CHAIN交易
2025-05-19 09:02:29,576 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 09:02:29,576 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 09:02:29,576 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-19 09:02:29,576 - INFO -   chain: ethereum
2025-05-19 09:02:29,576 - INFO -   token_in: USDT
2025-05-19 09:02:29,576 - INFO -   token_out: ******************************************
2025-05-19 09:02:29,576 - INFO -   amount: 207.55
2025-05-19 09:02:29,576 - INFO -   slippage: 0.5%
2025-05-19 09:02:29,576 - INFO -   real: True
2025-05-19 09:02:32,063 - INFO - 已在新线程 Trade-DODO 中启动DODO交易
2025-05-19 09:02:32,065 - INFO - ================================================================================
2025-05-19 09:02:32,065 - INFO - 开始执行 DODO 买入交易 - 时间: 2025-05-19 09:02:32
2025-05-19 09:02:32,065 - INFO - 链: ethereum, 投入金额: 60.29 USDT
2025-05-19 09:02:32,065 - INFO - 代币地址: ******************************************
2025-05-19 09:02:32,065 - INFO - 收到DODO买入请求 - 链:ethereum, 投入:60.29USDT
2025-05-19 09:02:32,065 - INFO - DODO: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 09:02:32,066 - INFO - DODO: 准备使用KyberSwap在ethereum上执行60.29USDT买入DODO交易
2025-05-19 09:02:32,066 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 09:02:32,066 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 09:02:32,066 - INFO - DODO: 准备调用swap_tokens函数，参数：
2025-05-19 09:02:32,066 - INFO -   chain: ethereum
2025-05-19 09:02:32,066 - INFO -   token_in: USDT
2025-05-19 09:02:32,066 - INFO -   token_out: ******************************************
2025-05-19 09:02:32,066 - INFO -   amount: 60.29
2025-05-19 09:02:32,066 - INFO -   slippage: 0.5%
2025-05-19 09:02:32,066 - INFO -   real: True
2025-05-19 09:03:05,387 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-19 09:03:05,387 - INFO - CHAIN: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 6667.692734863269 ****************************************** 到地址 ******************************************', 'error': None, 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 207.55, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '207550000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '6667692734863268919637', 'amount_out': 6667.692734863269, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '207550000', 'amountInUsd': '207.71472941245096', 'tokenOut': '******************************************', 'amountOut': '6667692734863268919637', 'amountOutUsd': '207.59991518464454', 'gas': '260000', 'gasPrice': '591204878', 'gasUsd': '0.3755677176330642', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '207550000', 'amountOut': '84830376265495866', 'exchange': 'uniswap-v4', 'poolType': 'uniswap-v4', 'poolExtra': {'router': '0x66a9893cc07d91d95644aedd05d03f95e1dba8af', 'permit2Addr': '0x000000000022d473030f116ddee9f6b43ac78ba3', 'tokenIn': '******************************************', 'tokenOut': '0x0000000000000000000000000000000000000000', 'fee': 500, 'tickSpacing': 10, 'hookAddress': '0x0000000000000000000000000000000000000000', 'hookData': '', 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '3917976402987065780006745', 'ri': '904f1669-1605-46c3-b05f-6ba9e69331d8'}}, {'pool': '0x33906431e44553411b8668543ffc20aaa24f75f9', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '84830376265495866', 'amountOut': '6667692734863268919637', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22513523}, 'extra': None}]], 'routeID': '904f1669-1605-46c3-b05f-6ba9e69331d8', 'checksum': '16153237873536486003', 'timestamp': 1747616555}, 'tx_hash': '0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5', 'receipt': AttributeDict({'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'blockNumber': 22513614, 'contractAddress': None, 'cumulativeGasUsed': 6414154, 'effectiveGasPrice': 842416638, 'from': '******************************************', 'gasUsed': 352212, 'logs': [AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 150, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 151, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 152, 'removed': False}), AttributeDict({'address': '0x000000000022D473030F116dDEE9F6B43aC78BA3', 'topics': [HexBytes('0xda9fa7c1b00402c17d0161b249b1ab8bbec047c5a52207b9c112deffd817036b'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec7'), HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90')], 'data': HexBytes('0x000000000000000000000000ffffffffffffffffffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000ffffffffffff'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 153, 'removed': False}), AttributeDict({'address': '0x000000000004444c5dc75cB358380D2e3dE08A90', 'topics': [HexBytes('0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f'), HexBytes('0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4fffffffffffffffffffffffffffffffffffffffffffffffffffffffff3a109d0000000000000000000000000000000000000000000033da9ed281c176c44ef59000000000000000000000000000000000000000000000000043a9364558d8891fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcf96300000000000000000000000000000000000000000000000000000000000001f4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 154, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 155, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 156, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90000000000000000000000000000000000000000000000000012d60019920f8e40000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 157, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 158, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 159, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x000000000000000000000000000000000000000000000003a5e4a1f2533615c3000000000000000000000000000000000000000000046200d5120a369cc9a32a'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 160, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000012d60019920f8e40000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 161, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f900000000000000000000000000000000000000000000016972f9b8d1194fe624000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 162, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 163, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec7000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000c5ef63000000000000000000000000000000000000000000000016972f9b8d1194fe624'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 164, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec600000000000000000000000000000000000000000000016972f9b8d1194fe624000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 165, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002977b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223230372e3731383236363735313738363235222c22416d6f756e744f7574555344223a223230372e3539393931353138343634343534222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2236363637363932373334383633323638393139363337222c2254696d657374616d70223a313734373631363536342c22526f7574654944223a2231353133663938612d643939622d343030302d383636652d6436646539353730633762643a35306435313833632d646339392d346336652d613331642d633034313863666166376333222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a2258515879444156307065494e68767a3871727379614a62596168546d4b4e4d446c373264625a6c302b592f33454b7536444f766c2b36787a43507362354731716d6358336f4d6e2f6775614f6c2b39737368716a73574e2b746c6e304e4a2f786933743046693275514d6f4c655836374872704b6d684d2b39583576674b6d5746716a384c3445797834596a6e30675569725258565a5a6c744c575076445951755a6e6331556a7643597a634d78366557667249624d6a4939705533695672416b787741424c7475346e315055597157715730314247696448386d71375551386d36357750724a2b4e326d5668325a454c4f5a786c5247426f6c6c3031554b2f627931562f57414a446a45654d5a784673664243653053676b32333436684a564d6774766271674847544d7731594b623768724f30644f6c5a485257596568414b514b35326c6a33744d336d675666593269387a6a413d3d227d7d000000000000000000'), 'blockNumber': 22513614, 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'blockHash': HexBytes('0xf617a01c417474f4e99afe8df13b0e043353aaedbe79d281a763e2dad00862b1'), 'logIndex': 166, 'removed': False})], 'logsBloom': HexBytes('0x002120000100000000000000800000000000000000000000000000002001000000000010200000000010000000000100060000008c0000000000000000280000000000000080000000004028000100200020002000000820000000008020000000000000000000000000000000002000000000200000000000000010000200020022000800000000040000000000000000000001000008080000084000100000020000000000000000000080100800000000400000000000004000080000004000000802100000000000000000201000000000420400001040000000000000000010200000802000004000001000000000408200010000400000000000000400'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5'), 'transactionIndex': 50, 'type': 0})}
2025-05-19 09:03:05,389 - INFO - CHAIN: 交易消息: 交换完成，接收了 6667.692734863269 ****************************************** 到地址 ******************************************
2025-05-19 09:03:05,389 - INFO - CHAIN: 从消息中提取到代币数量: 6667.692734863269
2025-05-19 09:03:05,389 - INFO - CHAIN: 交易成功 - 哈希: 0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5
2025-05-19 09:03:05,389 - INFO - CHAIN: 最终确认的购买数量: 6667.692734863269
2025-05-19 09:03:05,390 - INFO - 读取到 124 条现有交易记录
2025-05-19 09:03:05,390 - INFO - 添加新交易记录: CHAIN (CHAIN_207.55_2025-05-19 09:02:29)
2025-05-19 09:03:05,393 - INFO - 成功保存 125 条交易记录
2025-05-19 09:03:05,393 - INFO - CHAIN: 交易成功，从区块链获取实际买入数量...
2025-05-19 09:03:05,401 - INFO - CHAIN: 使用tx_token_change_tracker获取交易 0x8c8146230b18989986716cd9d65bfe00ee122d22911c9307ed4c77816e8122e5 的代币数量...
2025-05-19 09:03:08,925 - INFO - CHAIN: 从区块链获取到实际买入数量: 6667.************ CHAIN
2025-05-19 09:03:12,481 - INFO - CHAIN: 开始从以太坊桥接到Polygon...
2025-05-19 09:03:25,806 - ERROR - CHAIN: 桥接失败: 错误: 无法获取代币信息，请确保这是一个有效的ERC20代币: 发送交易失败: 'PolygonEthereumBridge' object has no attribute 'account'
2025-05-19 09:03:25,806 - INFO - CHAIN: 等待桥接完成...
2025-05-19 09:03:25,806 - WARNING - CHAIN: 未获取到Polygon到账交易哈希
2025-05-19 09:03:25,807 - INFO - CHAIN: 桥接操作完成
2025-05-19 09:03:25,807 - INFO - CHAIN: 桥接操作完成，结果: {'success': False, 'message': "错误: 无法获取代币信息，请确保这是一个有效的ERC20代币: 发送交易失败: 'PolygonEthereumBridge' object has no attribute 'account'", 'bridge_tx': None}
2025-05-19 09:03:25,807 - INFO - CHAIN: 买入交易处理完成，耗时: 56.23秒
2025-05-19 09:03:25,807 - INFO - ================================================================================
2025-05-19 10:26:03,429 - INFO - ================================================================================
2025-05-19 10:26:03,429 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-19 10:26:03
2025-05-19 10:26:03,429 - INFO - 链: ethereum, 投入金额: 188.68 USDT
2025-05-19 10:26:03,430 - INFO - 代币地址: ******************************************
2025-05-19 10:26:03,430 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:188.68USDT
2025-05-19 10:26:03,430 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:26:03,430 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行188.68USDT买入CHAIN交易
2025-05-19 10:26:03,430 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:26:03,430 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:26:03,430 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-19 10:26:03,430 - INFO -   chain: ethereum
2025-05-19 10:26:03,430 - INFO -   token_in: USDT
2025-05-19 10:26:03,430 - INFO -   token_out: ******************************************
2025-05-19 10:26:03,430 - INFO -   amount: 188.68
2025-05-19 10:26:03,430 - INFO -   slippage: 0.5%
2025-05-19 10:26:03,430 - INFO -   real: True
2025-05-19 10:26:04,958 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:26:04,959 - INFO - CHAIN: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 188.68 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 188.68, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '188680000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:26:04,959 - ERROR - CHAIN: 交易失败 - 代币余额不足。请求: 188.68 USDT，可用: 67.81312 USDT
2025-05-19 10:26:04,960 - INFO - 读取到 131 条现有交易记录
2025-05-19 10:26:04,960 - INFO - 添加新交易记录: CHAIN (CHAIN_188.68_2025-05-19 10:26:03)
2025-05-19 10:26:04,963 - INFO - 成功保存 132 条交易记录
2025-05-19 10:26:04,963 - INFO - CHAIN: 买入交易处理完成，耗时: 1.53秒
2025-05-19 10:26:04,963 - INFO - ================================================================================
2025-05-19 11:10:46,048 - INFO - ================================================================================
2025-05-19 11:10:46,048 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-19 11:10:46
2025-05-19 11:10:46,048 - INFO - 链: ethereum, 投入金额: 188.68 USDT
2025-05-19 11:10:46,048 - INFO - 代币地址: ******************************************
2025-05-19 11:10:46,048 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:188.68USDT
2025-05-19 11:10:46,048 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:10:46,049 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行188.68USDT买入CHAIN交易
2025-05-19 11:10:46,092 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:10:46,092 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:10:46,092 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-19 11:10:46,092 - INFO -   chain: ethereum
2025-05-19 11:10:46,092 - INFO -   token_in: USDT
2025-05-19 11:10:46,092 - INFO -   token_out: ******************************************
2025-05-19 11:10:46,092 - INFO -   amount: 188.68
2025-05-19 11:10:46,092 - INFO -   slippage: 0.5%
2025-05-19 11:10:46,092 - INFO -   real: True
2025-05-19 11:10:49,135 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:10:49,135 - INFO - CHAIN: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 188.68 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 188.68, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '188680000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:10:49,135 - ERROR - CHAIN: 交易失败 - 代币余额不足。请求: 188.68 USDT，可用: 67.81312 USDT
2025-05-19 11:10:49,136 - INFO - 读取到 135 条现有交易记录
2025-05-19 11:10:49,136 - INFO - 添加新交易记录: CHAIN (CHAIN_188.68_2025-05-19 11:10:46)
2025-05-19 11:10:49,138 - INFO - 成功保存 136 条交易记录
2025-05-19 11:10:49,138 - INFO - CHAIN: 买入交易处理完成，耗时: 3.09秒
2025-05-19 11:10:49,138 - INFO - ================================================================================
2025-05-20 07:24:24,981 - INFO - ================================================================================
2025-05-20 07:24:24,981 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-20 07:24:24
2025-05-20 07:24:24,981 - INFO - 链: polygon, 投入金额: 207.55 USDT
2025-05-20 07:24:24,982 - INFO - 代币地址: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-20 07:24:24,982 - INFO - 收到CHAIN买入请求 - 链:polygon, 投入:207.55USDT
2025-05-20 07:24:24,982 - INFO - CHAIN: 将在polygon链上执行买入，代币地址: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-20 07:24:24,982 - INFO - CHAIN: 准备使用KyberSwap在polygon上执行207.55USDT买入CHAIN交易
2025-05-20 07:24:24,982 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-20 07:24:24,995 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-20 07:24:25,012 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-20 07:24:25,013 - INFO -   chain: polygon
2025-05-20 07:24:25,013 - INFO -   token_in: USDT
2025-05-20 07:24:25,013 - INFO -   token_out: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-20 07:24:25,013 - INFO -   amount: 207.55
2025-05-20 07:24:25,013 - INFO -   slippage: 0.5%
2025-05-20 07:24:25,014 - INFO -   real: True
2025-05-20 07:24:45,475 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-20 07:24:45,475 - INFO - CHAIN: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 6762.429486944444 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509 到地址 ******************************************', 'error': None, 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amount': 207.55, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amount_in_wei': '207550000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '6762429486944443768911', 'amount_out': 6762.429486944444, 'route_summary': {'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amountIn': '207550000', 'amountInUsd': '194.77262334686628', 'tokenOut': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amountOut': '6762429486944443768911', 'amountOutUsd': '196.98234349857023', 'gas': '727000', 'gasPrice': '29323885041', 'gasUsd': '0.004652458421070761', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x0e3eb2c75bd7dd0e12249d96b1321d9570764d77', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'swapAmount': '207550000', 'amountOut': '207616402', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '1457652066949847389969617340386294118487833376467'}, 'extra': {'GlobalState': {'community_fee_token0': 150, 'community_fee_token1': 150, 'feeOtz': 10, 'feeZto': 10, 'price': '79215182736458831417914644635', 'tick': -4, 'timepoint_index': 32378, 'unlocked': False}, 'Liquidity': '94293754653709', 'ri': '209b7729-ab04-4891-93a1-e97e8821db45'}}, {'pool': '0x5520385bfcf07ec87c4c53a7d8d65595dff69fa4', 'tokenIn': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'tokenOut': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'swapAmount': '207616402', 'amountOut': '892942746359156004840', 'exchange': 'woofi-v3', 'poolType': 'woofi-v21', 'poolExtra': {'BlockNumber': 0}, 'extra': {}}, {'pool': '0xa54e44c7bfbcd2cf756af6fe9a112d782968fdd4', 'tokenIn': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'tokenOut': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'swapAmount': '892942746359156004840', 'amountOut': '6762429486944443768911', 'exchange': 'quickswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 71726541}, 'extra': None}]], 'routeID': '209b7729-ab04-4891-93a1-e97e8821db45', 'checksum': '11651500539907860407', 'timestamp': 1747697070}, 'tx_hash': '0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75', 'receipt': AttributeDict({'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'blockNumber': 71726643, 'contractAddress': None, 'cumulativeGasUsed': 2374434, 'effectiveGasPrice': 40929353694, 'from': '******************************************', 'gasUsed': 693968, 'logs': [AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 73, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006131b5fae19ea4f9d964eac0408e4408b66337b5')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffff39902647'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 74, 'removed': False}), AttributeDict({'address': '0x0e3Eb2C75Bd7dD0e12249d96b1321d9570764D77', 'topics': [HexBytes('0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000000a'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 75, 'removed': False}), AttributeDict({'address': '0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ff992'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 76, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ef630'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 77, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77'), HexBytes('0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000137'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 78, 'removed': False}), AttributeDict({'address': '0x0e3Eb2C75Bd7dD0e12249d96b1321d9570764D77', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffff3a0066e000000000000000000000000000000000000000000000000000000000c5ef6300000000000000000000000000000000000000000fff5436cd5d29649b0d7589b000000000000000000000000000000000000000000000000000055c279c02c0dfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 79, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000000e3eb2c75bd7dd0e12249d96b1321d9570764d77000000000000000000000000000000000000000000000000000000000c5ff9920000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c3359'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 80, 'removed': False}), AttributeDict({'address': '0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000005520385bfcf07ec87c4c53a7d8d65595dff69fa4')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ff992'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 81, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000005520385bfcf07ec87c4c53a7d8d65595dff69fa4'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000003067e57ca59c292fa0'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 82, 'removed': False}), AttributeDict({'address': '0x5520385bFcf07Ec87C4c53A7d8d65595Dff69FA4', 'topics': [HexBytes('0x0e8e403c2d36126272b08c75823e988381d9dc47f2f0a9a080d95f891d95c469'), HexBytes('0x0000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c3359'), HexBytes('0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000c5ff99200000000000000000000000000000000000000000000003067e57ca59c292fa00000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec60000000000000000000000004f82e73edb06d29ff62c91ec8f5ff06571bdeb29000000000000000000000000000000000000000000000000000000000c5ff992000000000000000000000000000000000000000000000000000000000000cac0'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 83, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000005520385bfcf07ec87c4c53a7d8d65595dff69fa400000000000000000000000000000000000000000000003067e57ca59c292fa00000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 84, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd4')], 'data': HexBytes('0x00000000000000000000000000000000000000000000003067e57ca59c292fa0'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 85, 'removed': False}), AttributeDict({'address': '0xd55fCe7CDaB84d84f2EF3F99816D765a2a94a509', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd4'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016e9551667b273187b7'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 86, 'removed': False}), AttributeDict({'address': '0xA54E44c7bfBCd2cf756Af6fE9A112D782968fdd4', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x000000000000000000000000000000000000000000001b49ba1990efa1bac20a00000000000000000000000000000000000000000000cdd740248093e0b73e00'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 87, 'removed': False}), AttributeDict({'address': '0xA54E44c7bfBCd2cf756Af6fE9A112D782968fdd4', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000003067e57ca59c292fa00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016e9551667b273187b7'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 88, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd400000000000000000000000000000000000000000000016e9551667b273187b7000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 89, 'removed': False}), AttributeDict({'address': '0xd55fCe7CDaB84d84f2EF3F99816D765a2a94a509', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x00000000000000000000000000000000000000000000016e9551667b273187b6'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 90, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000c2132d05d31c914a87c6611c10748aeb04b58e8f000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000c5ef63000000000000000000000000000000000000000000000016e9551667b273187b6'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 91, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec600000000000000000000000000000000000000000000016e9551667b273187b6000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 92, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002977b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223139342e3737323632333334363836363238222c22416d6f756e744f7574555344223a223139362e3938323334333439383537303236222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2236373632343239343836393434343433373638393130222c2254696d657374616d70223a313734373639373037392c22526f7574654944223a2234656265386430372d373834352d346532632d623436662d3936376662373239383630363a37303937646533382d393037612d346430342d383432332d346264326239356665666237222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a22566751564c75563135552b4545626c2f4f526c4f5446782b5963765770682b73686b39734d6a334130694742344d42705a59454443597638536548684e434c41656348787a456f2f71584b50504d74444d41716f68795433744574734635447844756959534b4e5a4c546a7567466f416d6d484b42564f385655544c79496357785131413577593750356b544e4f4a5661612b58623037576b56744e423367637669657464416864324e6d4e65713039434b474a4d376247506f67634c6846653755686778413037485a715a2f65443761314c2f4859485876395256447978334930625669344c524d2b37467064566444725a50312b587a784b4757797379494f6f79317a6d77656b7a725a72546263786f58392f62415877414a78556371746e36752f617933367a34373154733542547950364b4c337150622b572b33726868615a562b464837454e6f47786a39576e744e5339513d3d227d7d000000000000000000'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 93, 'removed': False}), AttributeDict({'address': '0x0000000000000000000000000000000000001010', 'topics': [HexBytes('0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63'), HexBytes('0x0000000000000000000000000000000000000000000000000000000000001010'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x00000000000000000000000067b94473d81d0cd00849d563c94d0432ac988b49')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000627d20e497e4d00000000000000000000000000000000000000000000000000566445c96fa9b7200000000000000000000000000000000000000000000103939c8b68a58fe0e7f0000000000000000000000000000000000000000000000000503c73bb262b6a20000000000000000000000000000000000000000000010393a2b33ab3d95f34f'), 'blockNumber': 71726643, 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'blockHash': HexBytes('0xeceba4cdf48ff2147325ba80df08756eab6decb4288a5502b90fe0c03e76ddb7'), 'logIndex': 94, 'removed': False})], 'logsBloom': HexBytes('0x00202000110000000000000080000000000000000000000000400000200000000000001000000000040000000004000104808000000424020000000000200004000000000040401800000028000000a00000042000000000000101000020020000000001000000000000000000000000000000000000000080000010000a00030000000000080000040000840000008000000008000008080000004000200000220000000200080000000000000884804840400000000008004000000040004020800002000000000201080000209000404000000400001000100000000000000010020000002000000408000000010000009800190000000000000000100800'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75'), 'transactionIndex': 21, 'type': 0})}
2025-05-20 07:24:45,477 - INFO - CHAIN: 交易消息: 交换完成，接收了 6762.429486944444 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509 到地址 ******************************************
2025-05-20 07:24:45,477 - INFO - CHAIN: 从消息中提取到代币数量: 6762.429486944444
2025-05-20 07:24:45,477 - INFO - CHAIN: 交易成功 - 哈希: 0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75
2025-05-20 07:24:45,477 - INFO - CHAIN: 最终确认的购买数量: 6762.429486944444
2025-05-20 07:24:45,480 - INFO - 读取到 152 条现有交易记录
2025-05-20 07:24:45,480 - INFO - 添加新交易记录: CHAIN (CHAIN_207.55_2025-05-20 07:24:24)
2025-05-20 07:24:45,482 - INFO - 成功保存 153 条交易记录
2025-05-20 07:24:45,493 - INFO - CHAIN: 交易成功，从区块链获取实际买入数量...
2025-05-20 07:24:45,501 - INFO - CHAIN: 使用tx_token_change_tracker获取交易 0xc7a0a72c89d8d8a5e435495edabe75ecd322bab44088cffdcea867522192bc75 的代币数量...
2025-05-20 07:24:48,840 - INFO - CHAIN: 开始从Polygon桥接到以太坊...
2025-05-20 07:24:48,851 - INFO - CHAIN: 等待桥接完成...
2025-05-20 07:24:48,851 - WARNING - CHAIN: 未获取到Claim交易哈希
2025-05-20 07:24:48,851 - INFO - CHAIN: 桥接操作完成
2025-05-20 07:24:48,851 - INFO - CHAIN: 桥接操作完成，结果: {'success': False, 'message': '错误: 找不到代币信息: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509，请检查地址是否正确，或者使用 list 命令查看支持的代币', 'burn_tx': None, 'claim_tx': None}
2025-05-20 07:24:48,851 - INFO - CHAIN: 买入交易处理完成，耗时: 23.87秒
2025-05-20 07:24:48,851 - INFO - ================================================================================
2025-05-21 08:30:51,842 - INFO - ================================================================================
2025-05-21 08:30:51,842 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-21 08:30:51
2025-05-21 08:30:51,842 - INFO - 链: polygon, 投入金额: 275.66 USDT
2025-05-21 08:30:51,842 - INFO - 代币地址: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-21 08:30:51,842 - INFO - 收到CHAIN买入请求 - 链:polygon, 投入:275.66USDT
2025-05-21 08:30:51,842 - INFO - CHAIN: 将在polygon链上执行买入，代币地址: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-21 08:30:51,842 - INFO - CHAIN: 准备使用KyberSwap在polygon上执行275.66USDT买入CHAIN交易
2025-05-21 08:30:51,842 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-21 08:30:51,842 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-21 08:30:51,843 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-21 08:30:51,843 - INFO -   chain: polygon
2025-05-21 08:30:51,843 - INFO -   token_in: USDT
2025-05-21 08:30:51,843 - INFO -   token_out: 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509
2025-05-21 08:30:51,843 - INFO -   amount: 275.66
2025-05-21 08:30:51,843 - INFO -   slippage: 0.5%
2025-05-21 08:30:51,843 - INFO -   real: True
2025-05-21 08:31:04,907 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-21 08:31:04,907 - INFO - CHAIN: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 9003.678517932158 0xd55fce7cdab84d84f2ef3f99816d765a2a94a509 到地址 ******************************************', 'error': None, 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amount': 275.66, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amount_in_wei': '275660000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '9005903734553684519880', 'amount_out': 9005.903734553685, 'route_summary': {'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'amountIn': '275660000', 'amountInUsd': '259.63930509750816', 'tokenOut': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'amountOut': '9005903734553684519880', 'amountOutUsd': '261.64435575091585', 'gas': '597000', 'gasPrice': '28240691908', 'gasUsd': '0.0036793935732358034', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0xeffa9e5e63ba18160ee26bda56b42f3368719615', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'swapAmount': '110264000', 'amountOut': '110300308296543632991', 'exchange': 'quickswap-v3', 'poolType': 'algebra-v1', 'poolExtra': {'swapFee': 0, 'priceLimit': '1457652066949847389969617340386294118487833376467'}, 'extra': {'GlobalState': {'community_fee_token0': 150, 'community_fee_token1': 150, 'feeOtz': 10, 'feeZto': 10, 'price': '79215027275367953428640', 'tick': -276328, 'timepoint_index': 23357, 'unlocked': False}, 'Liquidity': '14459907330534966447', 'ri': 'c88f1cd3-7f58-45f7-ba9d-267ced2be5e0'}}], [{'pool': '0x9b08288c3be4f62bbf8d1c20ac9c5e6f9467d8b7', 'tokenIn': '0xc2132d05d31c914a87c6611c10748aeb04b58e8f', 'tokenOut': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'swapAmount': '165396000', 'amountOut': '713193067819174539449', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 500, 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '38145468747451861162396'}}], [{'pool': '0xfe530931da161232ec76a7c3bea7d36cf3811a0d', 'tokenIn': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'tokenOut': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'swapAmount': '110300308296543632991', 'amountOut': '475571959340512289848', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 3000, 'priceLimit': '1457652066949847389969617340386294118487833376467'}, 'extra': {'nSqrtRx96': '38102603467066366891877675714'}}], [{'pool': '0xa54e44c7bfbcd2cf756af6fe9a112d782968fdd4', 'tokenIn': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'tokenOut': '0xd55fce7cdab84d84f2ef3f99816d765a2a94a509', 'swapAmount': '1188765027159686829297', 'amountOut': '9005903734553684519880', 'exchange': 'quickswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 71768548}, 'extra': None}]], 'routeID': 'c88f1cd3-7f58-45f7-ba9d-267ced2be5e0', 'checksum': '8847776704414371061', 'timestamp': 1747787456}, 'tx_hash': '0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6', 'receipt': AttributeDict({'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'blockNumber': 71768830, 'contractAddress': None, 'cumulativeGasUsed': 13968715, 'effectiveGasPrice': 39681288134, 'from': '******************************************', 'gasUsed': 560629, 'logs': [AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000106e3ce0'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 241, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006131b5fae19ea4f9d964eac0408e4408b66337b5')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffff24a4c5b7'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 242, 'removed': False}), AttributeDict({'address': '0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000effa9e5e63ba18160ee26bda56b42f3368719615'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000005fa9ad7bf2f3c27ac'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 243, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000effa9e5e63ba18160ee26bda56b42f3368719615')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000006927ec0'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 244, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000effa9e5e63ba18160ee26bda56b42f3368719615'), HexBytes('0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000000000a5'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 245, 'removed': False}), AttributeDict({'address': '0xefFA9E5e63ba18160Ee26BdA56b42F3368719615', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xfffffffffffffffffffffffffffffffffffffffffffffffa05652840d0c3d8540000000000000000000000000000000000000000000000000000000006927ec00000000000000000000000000000000000000000000010c66c395ab265d62425000000000000000000000000000000000000000000000000c8abe91af19a84affffffffffffffffffffffffffffffffffffffffffffffffffffffffffffbc899'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 246, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000effa9e5e63ba18160ee26bda56b42f3368719615000000000000000000000000000000000000000000000005fa9ad7bf2f3c27ac0000000000000000000000008f3cf7ad23cd3cadbd9735aff958023239c6a063'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 247, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000009b08288c3be4f62bbf8d1c20ac9c5e6f9467d8b7'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000026a677455be7194a04'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 248, 'removed': False}), AttributeDict({'address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000009b08288c3be4f62bbf8d1c20ac9c5e6f9467d8b7')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000009dbbe20'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 249, 'removed': False}), AttributeDict({'address': '0x9B08288C3Be4F62bbf8d1C20Ac9C5e6f9467d8B7', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffd95988baa418e6b5fc0000000000000000000000000000000000000000000000000000000009dbbe200000000000000000000000000000000000000000000008142e56184e83bbe6a70000000000000000000000000000000000000000000000005f72474348aacde4fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8f83'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 250, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000009b08288c3be4f62bbf8d1c20ac9c5e6f9467d8b7000000000000000000000000000000000000000000000026a677455be7194a040000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 251, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000fe530931da161232ec76a7c3bea7d36cf3811a0d'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000019c1a270410c7867b4'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 252, 'removed': False}), AttributeDict({'address': '0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000fe530931da161232ec76a7c3bea7d36cf3811a0d')], 'data': HexBytes('0x000000000000000000000000000000000000000000000005fa9ad7bf2f3c27ac'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 253, 'removed': False}), AttributeDict({'address': '0xFE530931dA161232Ec76A7c3bEA7D36cF3811A0d', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffe63e5d8fbef387984c000000000000000000000000000000000000000000000005fa9ad7bf2f3c27ac00000000000000000000000000000000000000007b2b3fff839a2e35584d018200000000000000000000000000000000000000000000ebd04fb508c48eeafba3ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc6d6'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 254, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000fe530931da161232ec76a7c3bea7d36cf3811a0d000000000000000000000000000000000000000000000019c1a270410c7867b40000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 255, 'removed': False}), AttributeDict({'address': '0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd4')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000406819b59cf391b1b9'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 256, 'removed': False}), AttributeDict({'address': '0xd55fCe7CDaB84d84f2EF3F99816D765a2a94a509', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd4'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000001e7eec02411bb3d19cb'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 257, 'removed': False}), AttributeDict({'address': '0xA54E44c7bfBCd2cf756Af6fE9A112D782968fdd4', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x000000000000000000000000000000000000000000001b511410d08d5e1cee2a00000000000000000000000000000000000000000000cda8428932e10d190668'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 258, 'removed': False}), AttributeDict({'address': '0xA54E44c7bfBCd2cf756Af6fE9A112D782968fdd4', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000406819b59cf391b1b9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001e7eec02411bb3d19cb'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 259, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000a54e44c7bfbcd2cf756af6fe9a112d782968fdd40000000000000000000000000000000000000000000001e7eec02411bb3d19cb000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 260, 'removed': False}), AttributeDict({'address': '0xd55fCe7CDaB84d84f2EF3F99816D765a2a94a509', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x0000000000000000000000000000000000000000000001e7eec02411bb3d19cb'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 261, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000c2132d05d31c914a87c6611c10748aeb04b58e8f000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f43400000000000000000000000000000000000000000000000000000000106e3ce00000000000000000000000000000000000000000000001e7eec02411bb3d19cb'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 262, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec60000000000000000000000000000000000000000000001e7eec02411bb3d19cb000000000000000000000000d55fce7cdab84d84f2ef3f99816d765a2a94a509'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 263, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002957b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223235392e36333933303530393735303831222c22416d6f756e744f7574555344223a223236312e35373937303735363136323734222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2239303033363738353137393332313538343631303436222c2254696d657374616d70223a313734373738373436312c22526f7574654944223a2236316632666630322d613131362d346334362d386332622d3734643264643833303566303a35313138356437312d306432372d343533632d626363652d303262383433356432353731222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a224276776c6a357778346a453852736b616668317164674c43626a495a56584235366e372b66697a5a7954474531594633325a3351726e54446d65504b6f50566778766b437851532b6d657a424b59346a644e68746d7a75575039326265504868692f4f6d4f79324251742f344966544d79732f79463155776d6461554b48754e45574e31364b74594b4a4c537630576b53414c2f2f32664368653743416c5339596b495679686a585854382b335a38623631365234366839646d61594267483077324b502f59355a6f685a7473425758514356556f7a2f576355765036624a3153574b6359436739506d374f585547704b6e6f7741435859767846584848766c31384f7a692b4c734d6e7a584b5530466e304a5a4c4358376539334e496c3952756574715953566a32446e796369504f324a4950453155555370483458536f734b7a67707554326c3754796f326751616165537252673d3d227d7d0000000000000000000000'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 264, 'removed': False}), AttributeDict({'address': '0x0000000000000000000000000000000000001010', 'topics': [HexBytes('0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63'), HexBytes('0x0000000000000000000000000000000000000000000000000000000000001010'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x000000000000000000000000eedba2484aaf940f37cd3cd21a5d7c4a7dafbfc0')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000496cc62360bf7e00000000000000000000000000000000000000000000000003a048cf5b851d95000000000000000000000000000000000000000000020aec524ffc8abfb0ebe70000000000000000000000000000000000000000000000000356dc0938245e17000000000000000000000000000000000000000000020aec52996950e311ab65'), 'blockNumber': 71768830, 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'blockHash': HexBytes('0xc891f294248318a771af9257adc3f866d1874bd42d816a527565889f969d70ef'), 'logIndex': 265, 'removed': False})], 'logsBloom': HexBytes('0x0020200011000000000000009000000000000000000000000000000020000000000000100000008004000000000000010400800000002400200000000020000000000000000040080000402c080000a00000002000000100000300000020000000000000000000000000000000000080000000000100000080004010404a0002000000c000080000040000000000008000000008000008080000004000000000220000020000080000000000000884804000400000000008004020000000004000000002040000000001000000201000604000000400081800100000040000000010000000002000000408000000000000008800090000000000000800100800'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6'), 'transactionIndex': 45, 'type': 0})}
2025-05-21 08:31:04,909 - INFO - CHAIN: 交易成功 - 哈希: 0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6
2025-05-21 08:31:04,916 - INFO - CHAIN: 使用tx_token_change_tracker获取交易 0x5e323fc0ae24cc406ddfb8ef3d34b1f44cc63b3699013f70f059d15e83e47bc6 的代币数量...
2025-05-21 08:31:05,368 - INFO - CHAIN: 从tx_token_change_tracker成功获取到代币数量: 9000.768154131681319371
2025-05-21 08:31:07,815 - INFO - CHAIN: 开始从Polygon桥接到以太坊...
2025-05-21 09:23:11,568 - INFO - CHAIN: 等待桥接完成...
2025-05-21 09:23:11,569 - INFO - CHAIN: Burn交易哈希: 0x504e98059f431002b0e5a3737b0ba27d2bf26c10aff2068981ca6783c3a12d62
2025-05-21 09:23:11,569 - WARNING - CHAIN: 未获取到Claim交易哈希
2025-05-21 09:23:11,569 - INFO - CHAIN: 桥接操作完成
2025-05-21 09:23:11,569 - INFO - CHAIN: 桥接操作完成，结果: {'success': False, 'burn_tx': '0x504e98059f431002b0e5a3737b0ba27d2bf26c10aff2068981ca6783c3a12d62', 'claim_tx': None, 'message': '未知状态', 'claim_status': 'confirmed'}
2025-05-21 09:23:11,569 - INFO - CHAIN: 买入交易处理完成，耗时: 3139.73秒
2025-05-21 09:23:11,569 - INFO - ================================================================================
2025-05-23 06:42:15,977 - INFO - ================================================================================
2025-05-23 06:42:15,978 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-23 06:42:15
2025-05-23 06:42:15,978 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-23 06:42:15,978 - INFO - 代币地址: ******************************************
2025-05-23 06:42:15,978 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-23 06:42:15,978 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-23 06:42:15,978 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行300.0USDT买入CHAIN交易
2025-05-23 06:42:15,978 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 06:42:15,992 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 06:42:15,992 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-23 06:42:15,992 - INFO -   chain: ethereum
2025-05-23 06:42:15,992 - INFO -   token_in: USDT
2025-05-23 06:42:15,992 - INFO -   token_out: ******************************************
2025-05-23 06:42:15,992 - INFO -   amount: 300.0
2025-05-23 06:42:15,992 - INFO -   slippage: 0.5%
2025-05-23 06:42:15,992 - INFO -   real: True
2025-05-23 06:42:18,135 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-23 06:42:18,135 - INFO - CHAIN: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-23 06:42:18,136 - ERROR - CHAIN: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 0.21782 USDT
2025-05-23 06:42:18,137 - INFO - 读取到 176 条现有交易记录
2025-05-23 06:42:18,137 - INFO - 添加新交易记录: CHAIN (CHAIN_300.0_2025-05-23 06:42:15)
2025-05-23 06:42:18,140 - INFO - 成功保存 177 条交易记录
2025-05-23 06:42:18,140 - INFO - CHAIN: 买入交易处理完成，耗时: 2.16秒
2025-05-23 06:42:18,140 - INFO - ================================================================================
2025-05-23 08:10:26,017 - INFO - ================================================================================
2025-05-23 08:10:26,017 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-23 08:10:26
2025-05-23 08:10:26,017 - INFO - 链: ethereum, 投入金额: 235.67 USDT
2025-05-23 08:10:26,017 - INFO - 代币地址: ******************************************
2025-05-23 08:10:26,017 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:235.67USDT
2025-05-23 08:10:26,017 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-23 08:10:26,017 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行235.67USDT买入CHAIN交易
2025-05-23 08:10:26,017 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 08:10:26,017 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 08:10:26,017 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-23 08:10:26,017 - INFO -   chain: ethereum
2025-05-23 08:10:26,017 - INFO -   token_in: USDT
2025-05-23 08:10:26,017 - INFO -   token_out: ******************************************
2025-05-23 08:10:26,018 - INFO -   amount: 235.67
2025-05-23 08:10:26,018 - INFO -   slippage: 0.5%
2025-05-23 08:10:26,018 - INFO -   real: True
2025-05-23 08:10:28,644 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-23 08:10:28,644 - INFO - CHAIN: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 235.67 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 235.67, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '235670000', 'is_native_in': False, 'is_native_out': False}
2025-05-23 08:10:28,644 - ERROR - CHAIN: 交易失败 - 代币余额不足。请求: 235.67 USDT，可用: 0.21782 USDT
2025-05-23 08:10:28,645 - INFO - 读取到 179 条现有交易记录
2025-05-23 08:10:28,645 - INFO - 添加新交易记录: CHAIN (CHAIN_235.67_2025-05-23 08:10:26)
2025-05-23 08:10:28,647 - INFO - 成功保存 180 条交易记录
2025-05-23 08:10:28,647 - INFO - CHAIN: 买入交易处理完成，耗时: 2.63秒
2025-05-23 08:10:28,647 - INFO - ================================================================================
2025-05-23 08:41:24,178 - INFO - ================================================================================
2025-05-23 08:41:24,178 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-23 08:41:24
2025-05-23 08:41:24,178 - INFO - 链: ethereum, 投入金额: 278.56 USDT
2025-05-23 08:41:24,178 - INFO - 代币地址: ******************************************
2025-05-23 08:41:24,178 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:278.56USDT
2025-05-23 08:41:24,178 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-23 08:41:24,178 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行278.56USDT买入CHAIN交易
2025-05-23 08:41:24,178 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 08:41:24,178 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 08:41:24,178 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-23 08:41:24,178 - INFO -   chain: ethereum
2025-05-23 08:41:24,178 - INFO -   token_in: USDT
2025-05-23 08:41:24,178 - INFO -   token_out: ******************************************
2025-05-23 08:41:24,178 - INFO -   amount: 278.56
2025-05-23 08:41:24,179 - INFO -   slippage: 0.5%
2025-05-23 08:41:24,179 - INFO -   real: True
2025-05-23 08:41:25,715 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-23 08:41:25,716 - INFO - CHAIN: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 278.56 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 278.56, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '278560000', 'is_native_in': False, 'is_native_out': False}
2025-05-23 08:41:25,716 - ERROR - CHAIN: 交易失败 - 代币余额不足。请求: 278.56 USDT，可用: 0.21782 USDT
2025-05-23 08:41:25,717 - INFO - 读取到 181 条现有交易记录
2025-05-23 08:41:25,718 - INFO - 添加新交易记录: CHAIN (CHAIN_278.56_2025-05-23 08:41:24)
2025-05-23 08:41:25,720 - INFO - 成功保存 182 条交易记录
2025-05-23 08:41:25,720 - INFO - CHAIN: 买入交易处理完成，耗时: 1.54秒
2025-05-23 08:41:25,720 - INFO - ================================================================================
2025-05-23 09:18:28,962 - INFO - ================================================================================
2025-05-23 09:18:28,962 - INFO - 开始执行 CHAIN 买入交易 - 时间: 2025-05-23 09:18:28
2025-05-23 09:18:28,962 - INFO - 链: ethereum, 投入金额: 278.56 USDT
2025-05-23 09:18:28,962 - INFO - 代币地址: ******************************************
2025-05-23 09:18:28,963 - INFO - 收到CHAIN买入请求 - 链:ethereum, 投入:278.56USDT
2025-05-23 09:18:28,963 - INFO - CHAIN: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-23 09:18:28,963 - INFO - CHAIN: 准备使用KyberSwap在ethereum上执行278.56USDT买入CHAIN交易
2025-05-23 09:18:28,963 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 09:18:28,974 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 09:18:28,974 - INFO - CHAIN: 准备调用swap_tokens函数，参数：
2025-05-23 09:18:28,974 - INFO -   chain: ethereum
2025-05-23 09:18:28,974 - INFO -   token_in: USDT
2025-05-23 09:18:28,975 - INFO -   token_out: ******************************************
2025-05-23 09:18:28,975 - INFO -   amount: 278.56
2025-05-23 09:18:28,975 - INFO -   slippage: 0.5%
2025-05-23 09:18:28,975 - INFO -   real: True
2025-05-23 09:18:52,414 - INFO - 已在新线程 Trade-MNW 中启动MNW交易
2025-05-23 09:18:52,415 - INFO - ================================================================================
2025-05-23 09:18:52,416 - INFO - 开始执行 MNW 买入交易 - 时间: 2025-05-23 09:18:52
2025-05-23 09:18:52,416 - INFO - 链: polygon, 投入金额: 256.18 USDT
2025-05-23 09:18:52,416 - INFO - 代币地址: ******************************************
2025-05-23 09:18:52,416 - INFO - 收到MNW买入请求 - 链:polygon, 投入:256.18USDT
2025-05-23 09:18:52,416 - INFO - MNW: 将在polygon链上执行买入，代币地址: ******************************************
2025-05-23 09:18:52,416 - INFO - MNW: 准备使用KyberSwap在polygon上执行256.18USDT买入MNW交易
2025-05-23 09:18:52,416 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 09:18:52,416 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 09:18:52,416 - INFO - MNW: 准备调用swap_tokens函数，参数：
2025-05-23 09:18:52,416 - INFO -   chain: polygon
2025-05-23 09:18:52,416 - INFO -   token_in: USDT
2025-05-23 09:18:52,416 - INFO -   token_out: ******************************************
2025-05-23 09:18:52,416 - INFO -   amount: 256.18
2025-05-23 09:18:52,416 - INFO -   slippage: 0.5%
2025-05-23 09:18:52,417 - INFO -   real: True
2025-05-23 09:18:53,611 - INFO - MNW: swap_tokens返回值类型: <class 'dict'>
2025-05-23 09:18:53,611 - INFO - MNW: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 256.18 USDT，可用: 136.401607 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 256.18, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '******************************************', 'amount_in_wei': '256180000', 'is_native_in': False, 'is_native_out': False}
2025-05-23 09:18:53,611 - ERROR - MNW: 交易失败 - 代币余额不足。请求: 256.18 USDT，可用: 136.401607 USDT
2025-05-23 09:18:53,612 - INFO - 读取到 182 条现有交易记录
2025-05-23 09:18:53,613 - INFO - 添加新交易记录: MNW (MNW_256.18_2025-05-23 09:18:52)
2025-05-23 09:18:53,615 - INFO - 成功保存 183 条交易记录
2025-05-23 09:18:53,615 - INFO - MNW: 买入交易处理完成，耗时: 1.20秒
2025-05-23 09:18:53,615 - INFO - ================================================================================
2025-05-23 09:19:31,271 - INFO - CHAIN: swap_tokens返回值类型: <class 'dict'>
2025-05-23 09:19:31,271 - INFO - CHAIN: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 8590.583814421007 ****************************************** 到地址 ******************************************', 'error': None, 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 278.56, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '278560000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '8596317992755522236399', 'amount_out': 8596.317992755523, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '278560000', 'amountInUsd': '278.6408577481223', 'tokenOut': '******************************************', 'amountOut': '8596317992755522236399', 'amountOutUsd': '278.96012012760366', 'gas': '260000', 'gasPrice': '1099740419', 'gasUsd': '0.7598693270687864', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '278560000', 'amountOut': '104837459217714540', 'exchange': 'uniswap-v4', 'poolType': 'uniswap-v4', 'poolExtra': {'router': '0x66a9893cc07d91d95644aedd05d03f95e1dba8af', 'permit2Addr': '0x000000000022d473030f116ddee9f6b43ac78ba3', 'tokenIn': '******************************************', 'tokenOut': '0x0000000000000000000000000000000000000000', 'fee': 500, 'tickSpacing': 10, 'hookAddress': '0x0000000000000000000000000000000000000000', 'hookData': '', 'priceLimit': '1461300573427867316570072651998408279850435624080'}, 'extra': {'nSqrtRx96': '4082969716111602153519551', 'ri': 'c62b034c-66bd-47b8-a6e0-61372ec148d8'}}, {'pool': '0x33906431e44553411b8668543ffc20aaa24f75f9', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '104837459217714540', 'amountOut': '8596317992755522236399', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22541389}, 'extra': None}]], 'routeID': 'c62b034c-66bd-47b8-a6e0-61372ec148d8', 'checksum': '6466626246749740319', 'timestamp': 1747963121}, 'tx_hash': '0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674', 'receipt': AttributeDict({'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'blockNumber': 22542245, 'contractAddress': None, 'cumulativeGasUsed': 16813881, 'effectiveGasPrice': 1615796688, 'from': '******************************************', 'gasUsed': 346339, 'logs': [AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000109a7d00'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 488, 'removed': False}), AttributeDict({'address': '0x000000000004444c5dc75cB358380D2e3dE08A90', 'topics': [HexBytes('0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f'), HexBytes('0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000017429a61c0c4d35ffffffffffffffffffffffffffffffffffffffffffffffffffffffffef6583000000000000000000000000000000000000000000000360f0c42a60125da8e86100000000000000000000000000000000000000000000000008b3deb359c2a162fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcfca400000000000000000000000000000000000000000000000000000000000001f4'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 489, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90')], 'data': HexBytes('0x00000000000000000000000000000000000000000000000000000000109a7d00'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 490, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000017429a61c0c4d35'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 491, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90000000000000000000000000000000000000000000000000017429a61c0c4d350000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 492, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000017429a61c0c4d35'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 493, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f9'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000001d1a26a550c699f88d1'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 494, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x000000000000000000000000000000000000000000000003928cc13cc056db950000000000000000000000000000000000000000000479dd8476f821a0cb2a12'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 495, 'removed': False}), AttributeDict({'address': '0x33906431E44553411b8668543FfC20AaA24F75F9', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000017429a61c0c4d35000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001d1a26a550c699f88d1'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 496, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x00000000000000000000000033906431e44553411b8668543ffc20aaa24f75f90000000000000000000000000000000000000000000001d1a26a550c699f88d1000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 497, 'removed': False}), AttributeDict({'address': '0xC4C2614E694cF534D407Ee49F8E44D125E4681c4', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x0000000000000000000000000000000000000000000001d1a26a550c699f88d1'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 498, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec7000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f43400000000000000000000000000000000000000000000000000000000109a7d000000000000000000000000000000000000000000000001d1a26a550c699f88d1'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 499, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec60000000000000000000000000000000000000000000001d1a26a550c699f88d1000000000000000000000000c4c2614e694cf534d407ee49f8e44d125e4681c4'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 500, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002967b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223237382e3636333835343333303737363235222c22416d6f756e744f7574555344223a223237382e37373430333936333631323134222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2238353930353833383134343231303036383732363233222c2254696d657374616d70223a313734373936333133332c22526f7574654944223a2233646263616334382d353739352d343564632d616565302d3231616639653864623239613a66643832333939302d616139302d346465322d393030652d393365356662383537393566222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a22552f55444145476b5164736751486e777869346f63704b78687272413738445765584268586e4f55574f306242396a344b724d566935776b484b7a3378594f376d5863414264457a415063696848444649535a4b444445445334423942686a524c64494156724848472b564c7a416d55524949683746384f325a4a667269712f6a354d736171664a4d74673271434e4a4f4478435550513736543433536d6845324751714c5a4f6a57727365706a7a4c525369415851432f59456e62305978685a654c4362554f70657672367a4835486973776b4a6b6f4c475a2b4c39476233746f6e4e41625249317a707975665648524f6b6e583148375236792b2f516b74526b474941486264614d6e5a36395335414c5677697061756768624e4538524d69544a4c616d303776445555666473572b4936754d6f422f6c4b654430386a7059313155506d483351733154524679504b66463376513d3d227d7d00000000000000000000'), 'blockNumber': 22542245, 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'blockHash': HexBytes('0x349a5d035a2aba25bd64c3eeab677ff845eed890911a1949e2f4acdb6b4edcb0'), 'logIndex': 501, 'removed': False})], 'logsBloom': HexBytes('0x002020000100000000000000800000000000000000000000000000002001000000000010200000000010000000000100060000000c0000000000000000000000000000000080000000000028000100200020002000000020000000008020000000000000000000000000000000002000000000000000000000000010000200020002000800000000040000000000000000000001000008080000084000100000000000000000000000000080100800000000400000000000004000000000000000000802100000000000000000201000000000400400001000000000000000000000200000802000004000001000000000408200010000400000000000000000'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674'), 'transactionIndex': 122, 'type': 0})}
2025-05-23 09:19:31,273 - INFO - CHAIN: 交易成功 - 哈希: 0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674
2025-05-23 09:19:31,280 - INFO - CHAIN: 使用tx_token_change_tracker获取交易 0x06a2ef8bd00c7780333d24bcee70f8062713fc53088f94e363e8ee1e3c572674 的代币数量...
2025-05-23 09:19:32,078 - INFO - CHAIN: 从tx_token_change_tracker成功获取到代币数量: 8589.439254368417188049
2025-05-23 09:19:38,774 - INFO - CHAIN: 开始从以太坊桥接到Polygon...
2025-05-23 09:25:01,252 - INFO - 已在新线程 Trade-SMT 中启动SMT交易
2025-05-23 09:25:01,253 - INFO - ================================================================================
2025-05-23 09:25:01,253 - INFO - 开始执行 SMT 买入交易 - 时间: 2025-05-23 09:25:01
2025-05-23 09:25:01,253 - INFO - 链: polygon, 投入金额: 300.0 USDT
2025-05-23 09:25:01,253 - INFO - 代币地址: 0xe631dabef60c37a37d70d3b4f812871df663226f
2025-05-23 09:25:01,253 - INFO - 收到SMT买入请求 - 链:polygon, 投入:300.0USDT
2025-05-23 09:25:01,253 - INFO - SMT: 将在polygon链上执行买入，代币地址: 0xe631dabef60c37a37d70d3b4f812871df663226f
2025-05-23 09:25:01,253 - INFO - SMT: 准备使用KyberSwap在polygon上执行300.0USDT买入SMT交易
2025-05-23 09:25:01,254 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-23 09:25:01,254 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-23 09:25:01,254 - INFO - SMT: 准备调用swap_tokens函数，参数：
2025-05-23 09:25:01,254 - INFO -   chain: polygon
2025-05-23 09:25:01,254 - INFO -   token_in: USDT
2025-05-23 09:25:01,254 - INFO -   token_out: 0xe631dabef60c37a37d70d3b4f812871df663226f
2025-05-23 09:25:01,254 - INFO -   amount: 300.0
2025-05-23 09:25:01,254 - INFO -   slippage: 0.5%
2025-05-23 09:25:01,254 - INFO -   real: True
2025-05-23 09:25:02,356 - INFO - SMT: swap_tokens返回值类型: <class 'dict'>
2025-05-23 09:25:02,356 - INFO - SMT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 136.401607 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xe631dabef60c37a37d70d3b4f812871df663226f', 'amount': 300.0, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xe631dabef60c37a37d70d3b4f812871df663226f', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-23 09:25:02,356 - ERROR - SMT: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 136.401607 USDT
2025-05-23 09:25:02,357 - INFO - 读取到 183 条现有交易记录
2025-05-23 09:25:02,357 - INFO - 添加新交易记录: SMT (SMT_300.0_2025-05-23 09:25:01)
2025-05-23 09:25:02,360 - INFO - 成功保存 184 条交易记录
2025-05-23 09:25:02,360 - INFO - SMT: 买入交易处理完成，耗时: 1.11秒
2025-05-23 09:25:02,360 - INFO - ================================================================================
2025-05-23 09:39:55,258 - INFO - CHAIN: 等待桥接完成...
2025-05-23 09:39:55,258 - INFO - CHAIN: 桥接交易哈希: 0x0729a138a1546e5c8d5449691bb23f2f57d0aafd453f5ceed92e4ae427ee32dd
2025-05-23 09:39:55,258 - INFO - CHAIN: Polygon到账交易哈希: 0x59581881f7838929b2021675d46e54dddef7eb039a5bea3fb611872a36313f5f
2025-05-23 09:39:55,258 - INFO - CHAIN: 桥接操作完成
2025-05-23 09:39:55,258 - INFO - CHAIN: 桥接操作完成，结果: {'success': True, 'message': '桥接和监控成功完成', 'bridge_tx': '0x0729a138a1546e5c8d5449691bb23f2f57d0aafd453f5ceed92e4ae427ee32dd', 'amount': 8589.439254368417, 'polygon_tx': '0x59581881f7838929b2021675d46e54dddef7eb039a5bea3fb611872a36313f5f'}
2025-05-23 09:39:55,258 - INFO - CHAIN: 买入交易处理完成，耗时: 1286.30秒
2025-05-23 09:39:55,258 - INFO - ================================================================================
