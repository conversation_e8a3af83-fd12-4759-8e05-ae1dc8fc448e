"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BroadcastModeKeplr = exports.BroadcastMode = void 0;
var BroadcastMode;
(function (BroadcastMode) {
    BroadcastMode["Sync"] = "BROADCAST_MODE_SYNC";
    BroadcastMode["Async"] = "BROADCAST_MODE_ASYNC";
    BroadcastMode["Block"] = "BROADCAST_MODE_BLOCK";
})(BroadcastMode || (exports.BroadcastMode = BroadcastMode = {}));
var BroadcastModeKeplr;
(function (BroadcastModeKeplr) {
    BroadcastModeKeplr["Sync"] = "sync";
    BroadcastModeKeplr["Async"] = "async";
    BroadcastModeKeplr["Block"] = "block";
})(BroadcastModeKeplr || (exports.BroadcastModeKeplr = BroadcastModeKeplr = {}));
