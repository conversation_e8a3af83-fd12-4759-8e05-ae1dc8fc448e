import { Network } from './types.js';
export const CW20_ADAPTER_CONTRACT_BY_NETWORK = {
    [Network.Mainnet]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.MainnetLB]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.MainnetOld]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.MainnetK8s]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.MainnetSentry]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.Staging]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.Internal]: 'inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk',
    [Network.Testnet]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [Network.TestnetK8s]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [Network.TestnetSentry]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [Network.TestnetOld]: 'inj1hdvy6tl89llqy3ze8lv6mz5qh66sx9enn0jxg6',
    [Network.Devnet]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [Network.Devnet1]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [Network.Devnet2]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [Network.Devnet3]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
    [Network.Local]: 'inj1uukt3kqela4vsllvrqnrgllkna5wn3cm588w6k',
};
export const CW20_SWAP_CONTRACT_BY_NETWORK = {
    [Network.Mainnet]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.MainnetLB]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.MainnetOld]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.MainnetK8s]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.MainnetSentry]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.Staging]: 'inj12yj3mtjarujkhcp6lg3klxjjfrx2v7v8yswgp9',
    [Network.Internal]: 'inj1psk3468yr9teahgz73amwvpfjehnhczvkrhhqx',
    [Network.Testnet]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [Network.TestnetK8s]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [Network.TestnetSentry]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [Network.TestnetOld]: 'inj14d7h5j6ddq6pqppl65z24w7xrtmpcrqjxj8d43',
    [Network.Devnet]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [Network.Devnet1]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [Network.Devnet2]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [Network.Devnet3]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
    [Network.Local]: 'inj177yh38g3ctu7cemxpa3c2kvwh2yslfxfmfa66h',
};
export const INCENTIVES_CONTRACT_BY_NETWORK = {
    [Network.Mainnet]: '',
    [Network.MainnetLB]: '',
    [Network.MainnetOld]: '',
    [Network.MainnetK8s]: '',
    [Network.MainnetSentry]: '',
    [Network.Staging]: '',
    [Network.Internal]: '',
    [Network.Testnet]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [Network.TestnetK8s]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [Network.TestnetSentry]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [Network.TestnetOld]: 'inj16twru668nsl7tqzahxd9q033swhr6a5xuslpkt',
    [Network.Devnet]: '',
    [Network.Devnet1]: '',
    [Network.Devnet2]: '',
    [Network.Devnet3]: '',
    [Network.Local]: '',
};
export const INJ_NAME_REGISTRY_CONTRACT_BY_NETWORK = {
    [Network.Mainnet]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.MainnetLB]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.MainnetK8s]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.MainnetSentry]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.MainnetOld]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.Staging]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.Internal]: 'inj1hm8vs8sr2h9nk0x66vctfs528wrp6k3gtgg275',
    [Network.Testnet]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.TestnetK8s]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.TestnetSentry]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.TestnetOld]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.Devnet]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.Devnet1]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.Devnet2]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.Devnet3]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
    [Network.Local]: 'inj1aw59rkpd9afp2ws6rx23nz5mrvq8dlckeslwfa',
};
export const INJ_NAME_REVERSE_RESOLVER_CONTRACT_BY_NETWORK = {
    [Network.Mainnet]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.MainnetLB]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.MainnetK8s]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.MainnetSentry]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.MainnetOld]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.Staging]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.Internal]: 'inj1x9m0hceug9qylcyrrtwqtytslv2jrph433thgu',
    [Network.Testnet]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.TestnetK8s]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.TestnetSentry]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.TestnetOld]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.Devnet]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.Devnet1]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.Devnet2]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.Devnet3]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
    [Network.Local]: 'inj1knf6puyscuuqqhgqglskfc0k99d4885qw5uv7v',
};
export const PEGGY_GRAPH_URL_BY_NETWORK = {
    [Network.Mainnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.MainnetLB]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.MainnetK8s]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.MainnetSentry]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.MainnetOld]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.Staging]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.Internal]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-mainnet',
    [Network.Testnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [Network.TestnetK8s]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [Network.TestnetSentry]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [Network.TestnetOld]: 'https://api.thegraph.com/subgraphs/name/injectivelabs/injective-peggo-goerli',
    [Network.Devnet]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [Network.Devnet1]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [Network.Devnet2]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [Network.Devnet3]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
    [Network.Local]: 'https://api.thegraph.com/subgraphs/name/injectivelabsdev/injective-peggo-devnet',
};
export const ASSET_PRICE_URL_BY_NETWORK = {
    [Network.Mainnet]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.MainnetLB]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.MainnetK8s]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.MainnetSentry]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.MainnetOld]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.Staging]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.Internal]: 'https://k8s.mainnet.asset.injective.network/asset-price/v1',
    [Network.Testnet]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [Network.TestnetK8s]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [Network.TestnetSentry]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [Network.TestnetOld]: 'https://k8s.testnet.asset.injective.network/asset-price/v1',
    [Network.Devnet]: 'https://devnet.asset.injective.dev/asset-price/v1',
    [Network.Devnet1]: 'https://devnet.api.injective.dev/asset-price/v1',
    [Network.Devnet2]: 'https://devnet.api.injective.dev/asset-price/v1',
    [Network.Devnet3]: 'https://devnet.api.injective.dev/asset-price/v1',
    [Network.Local]: 'https://devnet.api.injective.dev/asset-price/v1',
};
