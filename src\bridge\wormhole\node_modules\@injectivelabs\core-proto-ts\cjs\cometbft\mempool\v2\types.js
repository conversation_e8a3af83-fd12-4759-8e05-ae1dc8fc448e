"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = exports.ResetRoute = exports.HaveTx = exports.Txs = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cometbft.mempool.v2";
function createBaseTxs() {
    return { txs: [] };
}
exports.Txs = {
    encode: function (message, writer) {
        var e_1, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.txs), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).bytes(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTxs();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs.push(reader.bytes());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { txs: Array.isArray(object === null || object === void 0 ? void 0 : object.txs) ? object.txs.map(function (e) { return bytesFromBase64(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.txs) {
            obj.txs = message.txs.map(function (e) { return base64FromBytes(e !== undefined ? e : new Uint8Array()); });
        }
        else {
            obj.txs = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Txs.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseTxs();
        message.txs = ((_a = object.txs) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseHaveTx() {
    return { txKey: new Uint8Array() };
}
exports.HaveTx = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.txKey.length !== 0) {
            writer.uint32(10).bytes(message.txKey);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseHaveTx();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txKey = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { txKey: isSet(object.txKey) ? bytesFromBase64(object.txKey) : new Uint8Array() };
    },
    toJSON: function (message) {
        var obj = {};
        message.txKey !== undefined &&
            (obj.txKey = base64FromBytes(message.txKey !== undefined ? message.txKey : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.HaveTx.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseHaveTx();
        message.txKey = (_a = object.txKey) !== null && _a !== void 0 ? _a : new Uint8Array();
        return message;
    },
};
function createBaseResetRoute() {
    return {};
}
exports.ResetRoute = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseResetRoute();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.ResetRoute.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseResetRoute();
        return message;
    },
};
function createBaseMessage() {
    return { txs: undefined, haveTx: undefined, resetRoute: undefined };
}
exports.Message = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.txs !== undefined) {
            exports.Txs.encode(message.txs, writer.uint32(10).fork()).ldelim();
        }
        if (message.haveTx !== undefined) {
            exports.HaveTx.encode(message.haveTx, writer.uint32(18).fork()).ldelim();
        }
        if (message.resetRoute !== undefined) {
            exports.ResetRoute.encode(message.resetRoute, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessage();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.txs = exports.Txs.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.haveTx = exports.HaveTx.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.resetRoute = exports.ResetRoute.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            txs: isSet(object.txs) ? exports.Txs.fromJSON(object.txs) : undefined,
            haveTx: isSet(object.haveTx) ? exports.HaveTx.fromJSON(object.haveTx) : undefined,
            resetRoute: isSet(object.resetRoute) ? exports.ResetRoute.fromJSON(object.resetRoute) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.txs !== undefined && (obj.txs = message.txs ? exports.Txs.toJSON(message.txs) : undefined);
        message.haveTx !== undefined && (obj.haveTx = message.haveTx ? exports.HaveTx.toJSON(message.haveTx) : undefined);
        message.resetRoute !== undefined &&
            (obj.resetRoute = message.resetRoute ? exports.ResetRoute.toJSON(message.resetRoute) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Message.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseMessage();
        message.txs = (object.txs !== undefined && object.txs !== null) ? exports.Txs.fromPartial(object.txs) : undefined;
        message.haveTx = (object.haveTx !== undefined && object.haveTx !== null)
            ? exports.HaveTx.fromPartial(object.haveTx)
            : undefined;
        message.resetRoute = (object.resetRoute !== undefined && object.resetRoute !== null)
            ? exports.ResetRoute.fromPartial(object.resetRoute)
            : undefined;
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
