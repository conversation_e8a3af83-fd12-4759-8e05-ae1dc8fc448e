"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrpcWebError = exports.GrpcWebImpl = exports.InjectiveAuctionRPCInjBurntEndpointDesc = exports.InjectiveAuctionRPCStreamBidsDesc = exports.InjectiveAuctionRPCAuctionsDesc = exports.InjectiveAuctionRPCAuctionEndpointDesc = exports.InjectiveAuctionRPCDesc = exports.InjectiveAuctionRPCClientImpl = exports.InjBurntEndpointResponse = exports.InjBurntEndpointRequest = exports.StreamBidsResponse = exports.StreamBidsRequest = exports.AuctionsResponse = exports.AuctionsRequest = exports.Bid = exports.Coin = exports.Auction = exports.AuctionEndpointResponse = exports.AuctionEndpointRequest = exports.protobufPackage = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const long_1 = __importDefault(require("long"));
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
exports.protobufPackage = "injective_auction_rpc";
function createBaseAuctionEndpointRequest() {
    return { round: "0" };
}
exports.AuctionEndpointRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.round !== "0") {
            writer.uint32(8).sint64(message.round);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAuctionEndpointRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.round = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { round: isSet(object.round) ? String(object.round) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.round !== undefined && (obj.round = message.round);
        return obj;
    },
    create(base) {
        return exports.AuctionEndpointRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAuctionEndpointRequest();
        message.round = (_a = object.round) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseAuctionEndpointResponse() {
    return { auction: undefined, bids: [] };
}
exports.AuctionEndpointResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.auction !== undefined) {
            exports.Auction.encode(message.auction, writer.uint32(10).fork()).ldelim();
        }
        for (const v of message.bids) {
            exports.Bid.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAuctionEndpointResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.auction = exports.Auction.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.bids.push(exports.Bid.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            auction: isSet(object.auction) ? exports.Auction.fromJSON(object.auction) : undefined,
            bids: Array.isArray(object === null || object === void 0 ? void 0 : object.bids) ? object.bids.map((e) => exports.Bid.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.auction !== undefined && (obj.auction = message.auction ? exports.Auction.toJSON(message.auction) : undefined);
        if (message.bids) {
            obj.bids = message.bids.map((e) => e ? exports.Bid.toJSON(e) : undefined);
        }
        else {
            obj.bids = [];
        }
        return obj;
    },
    create(base) {
        return exports.AuctionEndpointResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAuctionEndpointResponse();
        message.auction = (object.auction !== undefined && object.auction !== null)
            ? exports.Auction.fromPartial(object.auction)
            : undefined;
        message.bids = ((_a = object.bids) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Bid.fromPartial(e))) || [];
        return message;
    },
};
function createBaseAuction() {
    return { winner: "", basket: [], winningBidAmount: "", round: "0", endTimestamp: "0", updatedAt: "0" };
}
exports.Auction = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.winner !== "") {
            writer.uint32(10).string(message.winner);
        }
        for (const v of message.basket) {
            exports.Coin.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.winningBidAmount !== "") {
            writer.uint32(26).string(message.winningBidAmount);
        }
        if (message.round !== "0") {
            writer.uint32(32).uint64(message.round);
        }
        if (message.endTimestamp !== "0") {
            writer.uint32(40).sint64(message.endTimestamp);
        }
        if (message.updatedAt !== "0") {
            writer.uint32(48).sint64(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAuction();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.winner = reader.string();
                    break;
                case 2:
                    message.basket.push(exports.Coin.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.winningBidAmount = reader.string();
                    break;
                case 4:
                    message.round = longToString(reader.uint64());
                    break;
                case 5:
                    message.endTimestamp = longToString(reader.sint64());
                    break;
                case 6:
                    message.updatedAt = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            winner: isSet(object.winner) ? String(object.winner) : "",
            basket: Array.isArray(object === null || object === void 0 ? void 0 : object.basket) ? object.basket.map((e) => exports.Coin.fromJSON(e)) : [],
            winningBidAmount: isSet(object.winningBidAmount) ? String(object.winningBidAmount) : "",
            round: isSet(object.round) ? String(object.round) : "0",
            endTimestamp: isSet(object.endTimestamp) ? String(object.endTimestamp) : "0",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.winner !== undefined && (obj.winner = message.winner);
        if (message.basket) {
            obj.basket = message.basket.map((e) => e ? exports.Coin.toJSON(e) : undefined);
        }
        else {
            obj.basket = [];
        }
        message.winningBidAmount !== undefined && (obj.winningBidAmount = message.winningBidAmount);
        message.round !== undefined && (obj.round = message.round);
        message.endTimestamp !== undefined && (obj.endTimestamp = message.endTimestamp);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.Auction.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseAuction();
        message.winner = (_a = object.winner) !== null && _a !== void 0 ? _a : "";
        message.basket = ((_b = object.basket) === null || _b === void 0 ? void 0 : _b.map((e) => exports.Coin.fromPartial(e))) || [];
        message.winningBidAmount = (_c = object.winningBidAmount) !== null && _c !== void 0 ? _c : "";
        message.round = (_d = object.round) !== null && _d !== void 0 ? _d : "0";
        message.endTimestamp = (_e = object.endTimestamp) !== null && _e !== void 0 ? _e : "0";
        message.updatedAt = (_f = object.updatedAt) !== null && _f !== void 0 ? _f : "0";
        return message;
    },
};
function createBaseCoin() {
    return { denom: "", amount: "", usdValue: "" };
}
exports.Coin = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        if (message.usdValue !== "") {
            writer.uint32(26).string(message.usdValue);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseCoin();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                case 3:
                    message.usdValue = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            denom: isSet(object.denom) ? String(object.denom) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            usdValue: isSet(object.usdValue) ? String(object.usdValue) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        message.amount !== undefined && (obj.amount = message.amount);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        return obj;
    },
    create(base) {
        return exports.Coin.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseCoin();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        message.usdValue = (_c = object.usdValue) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseBid() {
    return { bidder: "", amount: "", timestamp: "0" };
}
exports.Bid = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.bidder !== "") {
            writer.uint32(10).string(message.bidder);
        }
        if (message.amount !== "") {
            writer.uint32(18).string(message.amount);
        }
        if (message.timestamp !== "0") {
            writer.uint32(24).sint64(message.timestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBid();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bidder = reader.string();
                    break;
                case 2:
                    message.amount = reader.string();
                    break;
                case 3:
                    message.timestamp = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            bidder: isSet(object.bidder) ? String(object.bidder) : "",
            amount: isSet(object.amount) ? String(object.amount) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.bidder !== undefined && (obj.bidder = message.bidder);
        message.amount !== undefined && (obj.amount = message.amount);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create(base) {
        return exports.Bid.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseBid();
        message.bidder = (_a = object.bidder) !== null && _a !== void 0 ? _a : "";
        message.amount = (_b = object.amount) !== null && _b !== void 0 ? _b : "";
        message.timestamp = (_c = object.timestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseAuctionsRequest() {
    return {};
}
exports.AuctionsRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAuctionsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.AuctionsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBaseAuctionsRequest();
        return message;
    },
};
function createBaseAuctionsResponse() {
    return { auctions: [] };
}
exports.AuctionsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.auctions) {
            exports.Auction.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAuctionsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.auctions.push(exports.Auction.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { auctions: Array.isArray(object === null || object === void 0 ? void 0 : object.auctions) ? object.auctions.map((e) => exports.Auction.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.auctions) {
            obj.auctions = message.auctions.map((e) => e ? exports.Auction.toJSON(e) : undefined);
        }
        else {
            obj.auctions = [];
        }
        return obj;
    },
    create(base) {
        return exports.AuctionsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAuctionsResponse();
        message.auctions = ((_a = object.auctions) === null || _a === void 0 ? void 0 : _a.map((e) => exports.Auction.fromPartial(e))) || [];
        return message;
    },
};
function createBaseStreamBidsRequest() {
    return {};
}
exports.StreamBidsRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamBidsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.StreamBidsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBaseStreamBidsRequest();
        return message;
    },
};
function createBaseStreamBidsResponse() {
    return { bidder: "", bidAmount: "", round: "0", timestamp: "0" };
}
exports.StreamBidsResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.bidder !== "") {
            writer.uint32(10).string(message.bidder);
        }
        if (message.bidAmount !== "") {
            writer.uint32(18).string(message.bidAmount);
        }
        if (message.round !== "0") {
            writer.uint32(24).uint64(message.round);
        }
        if (message.timestamp !== "0") {
            writer.uint32(32).sint64(message.timestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseStreamBidsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bidder = reader.string();
                    break;
                case 2:
                    message.bidAmount = reader.string();
                    break;
                case 3:
                    message.round = longToString(reader.uint64());
                    break;
                case 4:
                    message.timestamp = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            bidder: isSet(object.bidder) ? String(object.bidder) : "",
            bidAmount: isSet(object.bidAmount) ? String(object.bidAmount) : "",
            round: isSet(object.round) ? String(object.round) : "0",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.bidder !== undefined && (obj.bidder = message.bidder);
        message.bidAmount !== undefined && (obj.bidAmount = message.bidAmount);
        message.round !== undefined && (obj.round = message.round);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        return obj;
    },
    create(base) {
        return exports.StreamBidsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseStreamBidsResponse();
        message.bidder = (_a = object.bidder) !== null && _a !== void 0 ? _a : "";
        message.bidAmount = (_b = object.bidAmount) !== null && _b !== void 0 ? _b : "";
        message.round = (_c = object.round) !== null && _c !== void 0 ? _c : "0";
        message.timestamp = (_d = object.timestamp) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function createBaseInjBurntEndpointRequest() {
    return {};
}
exports.InjBurntEndpointRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInjBurntEndpointRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.InjBurntEndpointRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBaseInjBurntEndpointRequest();
        return message;
    },
};
function createBaseInjBurntEndpointResponse() {
    return { totalInjBurnt: "" };
}
exports.InjBurntEndpointResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.totalInjBurnt !== "") {
            writer.uint32(10).string(message.totalInjBurnt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseInjBurntEndpointResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.totalInjBurnt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { totalInjBurnt: isSet(object.totalInjBurnt) ? String(object.totalInjBurnt) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.totalInjBurnt !== undefined && (obj.totalInjBurnt = message.totalInjBurnt);
        return obj;
    },
    create(base) {
        return exports.InjBurntEndpointResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseInjBurntEndpointResponse();
        message.totalInjBurnt = (_a = object.totalInjBurnt) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
class InjectiveAuctionRPCClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.AuctionEndpoint = this.AuctionEndpoint.bind(this);
        this.Auctions = this.Auctions.bind(this);
        this.StreamBids = this.StreamBids.bind(this);
        this.InjBurntEndpoint = this.InjBurntEndpoint.bind(this);
    }
    AuctionEndpoint(request, metadata) {
        return this.rpc.unary(exports.InjectiveAuctionRPCAuctionEndpointDesc, exports.AuctionEndpointRequest.fromPartial(request), metadata);
    }
    Auctions(request, metadata) {
        return this.rpc.unary(exports.InjectiveAuctionRPCAuctionsDesc, exports.AuctionsRequest.fromPartial(request), metadata);
    }
    StreamBids(request, metadata) {
        return this.rpc.invoke(exports.InjectiveAuctionRPCStreamBidsDesc, exports.StreamBidsRequest.fromPartial(request), metadata);
    }
    InjBurntEndpoint(request, metadata) {
        return this.rpc.unary(exports.InjectiveAuctionRPCInjBurntEndpointDesc, exports.InjBurntEndpointRequest.fromPartial(request), metadata);
    }
}
exports.InjectiveAuctionRPCClientImpl = InjectiveAuctionRPCClientImpl;
exports.InjectiveAuctionRPCDesc = { serviceName: "injective_auction_rpc.InjectiveAuctionRPC" };
exports.InjectiveAuctionRPCAuctionEndpointDesc = {
    methodName: "AuctionEndpoint",
    service: exports.InjectiveAuctionRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AuctionEndpointRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AuctionEndpointResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveAuctionRPCAuctionsDesc = {
    methodName: "Auctions",
    service: exports.InjectiveAuctionRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.AuctionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AuctionsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveAuctionRPCStreamBidsDesc = {
    methodName: "StreamBids",
    service: exports.InjectiveAuctionRPCDesc,
    requestStream: false,
    responseStream: true,
    requestType: {
        serializeBinary() {
            return exports.StreamBidsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.StreamBidsResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.InjectiveAuctionRPCInjBurntEndpointDesc = {
    methodName: "InjBurntEndpoint",
    service: exports.InjectiveAuctionRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.InjBurntEndpointRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.InjBurntEndpointResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
    invoke(methodDesc, _request, metadata) {
        var _a;
        const upStreamCodes = this.options.upStreamRetryCodes || [];
        const DEFAULT_TIMEOUT_TIME = 3000;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new rxjs_1.Observable((observer) => {
            const upStream = (() => {
                const client = grpc_web_1.grpc.invoke(methodDesc, {
                    host: this.host,
                    request,
                    transport: this.options.streamingTransport || this.options.transport,
                    metadata: maybeCombinedMetadata,
                    debug: this.options.debug,
                    onMessage: (next) => observer.next(next),
                    onEnd: (code, message, trailers) => {
                        if (code === 0) {
                            observer.complete();
                        }
                        else if (upStreamCodes.includes(code)) {
                            setTimeout(upStream, DEFAULT_TIMEOUT_TIME);
                        }
                        else {
                            const err = new Error(message);
                            err.code = code;
                            err.metadata = trailers;
                            observer.error(err);
                        }
                    },
                });
                observer.add(() => {
                    if (!observer.closed) {
                        return client.close();
                    }
                });
            });
            upStream();
        }).pipe((0, operators_1.share)());
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
