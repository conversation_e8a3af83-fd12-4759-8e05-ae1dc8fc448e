import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
import { ExitType } from '../types.js';
export declare namespace ExecArgCreatePerpGridStrategy {
    interface Params {
        subaccountId: string;
        lowerBound: string;
        upperBound: string;
        levels: number;
        slippage?: string;
        stopLoss?: string;
        takeProfit?: string;
        marginRatio: string;
        feeRecipient?: string;
    }
    interface Data {
        subaccount_id: string;
        bounds: [string, string];
        slippage?: string;
        stop_loss?: {
            exit_type: ExitType;
            exit_price: string;
        };
        take_profit?: {
            exit_type: ExitType;
            exit_price: string;
        };
        levels: number;
        strategy_type: {
            perpetual: {
                margin_ratio: string;
            };
        };
        fee_recipient?: string;
    }
}
/**
 * @category Contract Exec Arguments
 */
/** @deprecated */
export default class ExecArgCreatePerpGridStrategy extends ExecArgBase<ExecArgCreatePerpGridStrategy.Params, ExecArgCreatePerpGridStrategy.Data> {
    static fromJSON(params: ExecArgCreatePerpGridStrategy.Params): ExecArgCreatePerpGridStrategy;
    toData(): ExecArgCreatePerpGridStrategy.Data;
    toExecData(): ExecDataRepresentation<ExecArgCreatePerpGridStrategy.Data>;
}
