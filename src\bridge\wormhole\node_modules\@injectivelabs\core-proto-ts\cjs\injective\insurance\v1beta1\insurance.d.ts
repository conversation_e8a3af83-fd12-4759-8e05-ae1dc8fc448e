import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { Duration } from "../../../google/protobuf/duration";
import { OracleType } from "../../oracle/v1beta1/oracle";
export declare const protobufPackage = "injective.insurance.v1beta1";
export interface Params {
    /**
     * default_redemption_notice_period_duration defines the default minimum
     * notice period duration that must pass after an underwriter sends a
     * redemption request before the underwriter can claim his tokens
     */
    defaultRedemptionNoticePeriodDuration: Duration | undefined;
}
export interface InsuranceFund {
    /** deposit denomination for the given insurance fund */
    depositDenom: string;
    /** insurance fund pool token denomination for the given insurance fund */
    insurancePoolTokenDenom: string;
    /**
     * redemption_notice_period_duration defines the minimum notice period
     * duration that must pass after an underwriter sends a redemption request
     * before the underwriter can claim his tokens
     */
    redemptionNoticePeriodDuration: Duration | undefined;
    /** balance of fund */
    balance: string;
    /** total share tokens minted */
    totalShare: string;
    /** marketID of the derivative market */
    marketId: string;
    /** ticker of the derivative market */
    marketTicker: string;
    /**
     * Oracle base currency of the derivative market OR the oracle symbol for the
     * binary options market.
     */
    oracleBase: string;
    /**
     * Oracle quote currency of the derivative market OR the oracle provider for
     * the binary options market.
     */
    oracleQuote: string;
    /** Oracle type of the binary options or derivative market */
    oracleType: OracleType;
    /**
     * Expiration time of the derivative market. Should be -1 for perpetual or -2
     * for binary options markets.
     */
    expiry: string;
}
export interface RedemptionSchedule {
    /** id of redemption schedule */
    id: string;
    /** marketId of insurance fund for the redemption */
    marketId: string;
    /** address of the redeemer */
    redeemer: string;
    /** the time after which the redemption can be claimed */
    claimableRedemptionTime: Date | undefined;
    /** the insurance_pool_token amount to redeem */
    redemptionAmount: Coin | undefined;
}
export declare const Params: {
    encode(message: Params, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Params;
    fromJSON(object: any): Params;
    toJSON(message: Params): unknown;
    create(base?: DeepPartial<Params>): Params;
    fromPartial(object: DeepPartial<Params>): Params;
};
export declare const InsuranceFund: {
    encode(message: InsuranceFund, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InsuranceFund;
    fromJSON(object: any): InsuranceFund;
    toJSON(message: InsuranceFund): unknown;
    create(base?: DeepPartial<InsuranceFund>): InsuranceFund;
    fromPartial(object: DeepPartial<InsuranceFund>): InsuranceFund;
};
export declare const RedemptionSchedule: {
    encode(message: RedemptionSchedule, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RedemptionSchedule;
    fromJSON(object: any): RedemptionSchedule;
    toJSON(message: RedemptionSchedule): unknown;
    create(base?: DeepPartial<RedemptionSchedule>): RedemptionSchedule;
    fromPartial(object: DeepPartial<RedemptionSchedule>): RedemptionSchedule;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
