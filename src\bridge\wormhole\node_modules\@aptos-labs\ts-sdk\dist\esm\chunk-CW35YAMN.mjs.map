{"version": 3, "sources": ["../../src/utils/normalizeBundle.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Deserializer, Serializable } from \"../bcs\";\n/**\n * @group Implementation\n * @category Utils\n */\nexport type DeserializableClass<T extends Serializable> = {\n  /**\n   * Deserializes a serialized object using the provided deserializer.\n   * This function allows you to reconstruct an object from its serialized form.\n   *\n   * @param deserializer - An instance of the Deserializer used to read the serialized data.\n   * @group Implementation\n   * @category Utils\n   */\n  deserialize(deserializer: Deserializer): T;\n};\n\n/**\n * Normalizes an instance of a class by deserializing it from its byte representation.\n * This function allows the `instanceof` operator to work correctly when the input objects originate from a different bundle.\n *\n * @param cls - The class of the object to normalize.\n * @param value - The instance to normalize.\n * @group Implementation\n * @category Utils\n */\nexport function normalizeBundle<T extends Serializable>(cls: DeserializableClass<T>, value: T) {\n  const serializedBytes = value.bcsToBytes();\n  const deserializer = new Deserializer(serializedBytes);\n  return cls.deserialize(deserializer);\n}\n"], "mappings": "yCA6BO,SAASA,EAAwCC,EAA6BC,EAAU,CAC7F,IAAMC,EAAkBD,EAAM,WAAW,EACnCE,EAAe,IAAIC,EAAaF,CAAe,EACrD,OAAOF,EAAI,YAAYG,CAAY,CACrC", "names": ["normalizeBundle", "cls", "value", "serializedBytes", "deserializer", "Deserializer"]}