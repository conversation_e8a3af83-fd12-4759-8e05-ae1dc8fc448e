"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenStaticFactory = void 0;
const index_js_1 = require("./../utils/index.js");
const index_js_2 = require("./../types/index.js");
class TokenStaticFactory {
    registry;
    denomVerifiedMap;
    denomBlacklistedMap;
    denomUnverifiedMap;
    cw20AddressVerifiedMap;
    cw20AddressUnverifiedMap;
    factoryTokenDenomVerifiedMap;
    factoryTokenDenomUnverifiedMap;
    ibcDenomsVerifiedMap;
    ibcDenomsUnverifiedMap;
    ibcBaseDenomsVerifiedMap;
    ibcBaseDenomsUnverifiedMap;
    symbolTokensMap;
    insuranceTokensMap;
    constructor(registry) {
        this.registry = registry;
        this.denomVerifiedMap = {};
        this.denomBlacklistedMap = {};
        this.denomUnverifiedMap = {};
        this.cw20AddressVerifiedMap = {};
        this.cw20AddressUnverifiedMap = {};
        this.factoryTokenDenomVerifiedMap = {};
        this.factoryTokenDenomUnverifiedMap = {};
        this.ibcDenomsVerifiedMap = {};
        this.ibcDenomsUnverifiedMap = {};
        this.ibcBaseDenomsVerifiedMap = {};
        this.ibcBaseDenomsUnverifiedMap = {};
        this.symbolTokensMap = {};
        this.insuranceTokensMap = {};
        if (registry.length > 0) {
            this.mapRegistry(registry);
        }
    }
    mapRegistry(registry) {
        for (const token of registry) {
            const { denom, baseDenom, symbol, address, tokenType, tokenVerification, } = token;
            if (tokenVerification === index_js_2.TokenVerification.Verified) {
                this.denomVerifiedMap[denom] = token;
                this.symbolTokensMap[symbol.toLowerCase()] = token;
            }
            else {
                this.denomUnverifiedMap[denom] = token;
            }
            if (tokenType === index_js_2.TokenType.InsuranceFund) {
                this.insuranceTokensMap[symbol.toLowerCase()] = token;
            }
            if (denom.startsWith('factory/')) {
                if (tokenVerification === index_js_2.TokenVerification.Verified) {
                    this.factoryTokenDenomVerifiedMap[denom] = token;
                }
                else {
                    this.factoryTokenDenomUnverifiedMap[denom] = token;
                }
            }
            if (tokenType === index_js_2.TokenType.Cw20) {
                if (tokenVerification === index_js_2.TokenVerification.Verified) {
                    this.cw20AddressVerifiedMap[address] = token;
                }
                else {
                    this.cw20AddressUnverifiedMap[address] = token;
                }
            }
            if (tokenType === index_js_2.TokenType.Ibc) {
                if (tokenVerification === index_js_2.TokenVerification.Verified) {
                    this.ibcDenomsVerifiedMap[denom] = token;
                    if (baseDenom) {
                        const existingIbcBaseDenomToken = this.ibcBaseDenomsVerifiedMap[baseDenom];
                        if (!existingIbcBaseDenomToken ||
                            !existingIbcBaseDenomToken.isNative) {
                            this.ibcBaseDenomsVerifiedMap[baseDenom] = token;
                        }
                    }
                }
                else {
                    this.ibcDenomsUnverifiedMap[denom] = token;
                    if (baseDenom && token.baseDenom !== 'Unknown') {
                        this.ibcBaseDenomsUnverifiedMap[baseDenom] = token;
                    }
                }
            }
        }
    }
    getSymbolToken(symbol) {
        return this.symbolTokensMap[symbol.toLowerCase()];
    }
    getInsuranceToken(symbol) {
        return this.insuranceTokensMap[symbol.toLowerCase()];
    }
    getIbcToken(denom, { source, tokenVerification, } = {}) {
        const denomTrimmed = denom.trim();
        if (source) {
            const list = tokenVerification === index_js_2.TokenVerification.Verified
                ? Object.values(this.ibcDenomsVerifiedMap)
                : [
                    ...Object.values(this.ibcDenomsVerifiedMap),
                    ...Object.values(this.ibcDenomsVerifiedMap).flat(),
                ];
            return list.find((token) => token.source === source &&
                (token.denom === denomTrimmed || token?.baseDenom === denomTrimmed));
        }
        if (tokenVerification === index_js_2.TokenVerification.Verified) {
            return (this.ibcBaseDenomsVerifiedMap[denomTrimmed] ||
                this.ibcDenomsVerifiedMap[denomTrimmed]);
        }
        return (this.ibcBaseDenomsVerifiedMap[denomTrimmed] ||
            this.ibcDenomsVerifiedMap[denomTrimmed] ||
            this.ibcBaseDenomsUnverifiedMap[denomTrimmed] ||
            this.ibcDenomsUnverifiedMap[denomTrimmed]);
    }
    getCw20Token(address, { tokenVerification } = {}) {
        if (tokenVerification === index_js_2.TokenVerification.Verified) {
            return this.cw20AddressVerifiedMap[address];
        }
        return (this.cw20AddressVerifiedMap[address] ||
            this.cw20AddressUnverifiedMap[address]);
    }
    getTokenFactoryToken(denom, { tokenVerification } = {}) {
        if (tokenVerification === index_js_2.TokenVerification.Verified) {
            return this.factoryTokenDenomVerifiedMap[denom];
        }
        return (this.factoryTokenDenomVerifiedMap[denom] ||
            this.factoryTokenDenomUnverifiedMap[denom]);
    }
    toToken(denomOrSymbol, { source, verification, } = {}) {
        const denomOrSymbolTrimmed = denomOrSymbol.trim();
        if (denomOrSymbolTrimmed === 'inj') {
            return this.denomVerifiedMap[denomOrSymbolTrimmed];
        }
        if (source) {
            return this.getIbcToken(denomOrSymbol, {
                source,
                tokenVerification: verification,
            });
        }
        if (denomOrSymbolTrimmed.startsWith('factory/wormhole')) {
            return this.getIbcToken(denomOrSymbolTrimmed, {
                tokenVerification: verification,
            });
        }
        if (denomOrSymbolTrimmed.length < 42) {
            return (this.getSymbolToken(denomOrSymbolTrimmed) ||
                this.getInsuranceToken(denomOrSymbolTrimmed) ||
                this.getIbcToken(denomOrSymbolTrimmed, {
                    tokenVerification: verification,
                }) ||
                this.denomVerifiedMap[denomOrSymbolTrimmed]);
        }
        if ((0, index_js_1.isCw20ContractAddress)(denomOrSymbolTrimmed)) {
            return this.getCw20Token(denomOrSymbolTrimmed, {
                tokenVerification: verification,
            });
        }
        if (denomOrSymbolTrimmed.startsWith('factory/')) {
            return this.getTokenFactoryToken(denomOrSymbolTrimmed, {
                tokenVerification: verification,
            });
        }
        return (this.denomVerifiedMap[denomOrSymbolTrimmed] ||
            this.denomUnverifiedMap[denomOrSymbolTrimmed]);
    }
}
exports.TokenStaticFactory = TokenStaticFactory;
