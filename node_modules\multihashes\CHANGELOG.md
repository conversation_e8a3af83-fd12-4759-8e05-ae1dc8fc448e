<a name="0.4.21"></a>
## [0.4.21](https://github.com/multiformats/js-multihash/compare/v0.4.19...v0.4.21) (2020-06-19)



<a name="0.4.20"></a>
## [0.4.20](https://github.com/multiformats/js-multihash/compare/v0.4.19...v0.4.20) (2020-06-19)



<a name="0.4.19"></a>
## [0.4.19](https://github.com/multiformats/js-multihash/compare/v0.4.18...v0.4.19) (2020-03-31)


### Bug Fixes

* add missing md{4,5} hash codes ([010f8cd](https://github.com/multiformats/js-multihash/commit/010f8cd))
* support zero-length multihashes ([86f556f](https://github.com/multiformats/js-multihash/commit/86f556f))
* sync test fixtures with go-multihash ([00d5e23](https://github.com/multiformats/js-multihash/commit/00d5e23))



<a name="0.4.18"></a>
## [0.4.18](https://github.com/multiformats/js-multihash/compare/v0.4.17...v0.4.18) (2020-03-24)



<a name="0.4.17"></a>
## [0.4.17](https://github.com/multiformats/js-multihash/compare/v0.4.16...v0.4.17) (2020-03-17)


### Bug Fixes

* remove unnecessary deps ([#72](https://github.com/multiformats/js-multihash/issues/72)) ([1e0ac42](https://github.com/multiformats/js-multihash/commit/1e0ac42))



<a name="0.4.16"></a>
## [0.4.16](https://github.com/multiformats/js-multihash/compare/v0.4.15...v0.4.16) (2020-03-16)


### Bug Fixes

* add buffer dependency ([#71](https://github.com/multiformats/js-multihash/issues/71)) ([1e91e64](https://github.com/multiformats/js-multihash/commit/1e91e64))



<a name="0.4.15"></a>
## [0.4.15](https://github.com/multiformats/js-multihash/compare/v0.4.14...v0.4.15) (2019-07-10)


### Features

* support identity hashes ([b85999d](https://github.com/multiformats/js-multihash/commit/b85999d))



<a name="0.4.14"></a>
## [0.4.14](https://github.com/multiformats/js-multihash/compare/v0.4.13...v0.4.14) (2018-08-08)


### Bug Fixes

* missing id name constant ([42b9f0c](https://github.com/multiformats/js-multihash/commit/42b9f0c))



<a name="0.4.13"></a>
## [0.4.13](https://github.com/multiformats/js-multihash/compare/v0.4.12...v0.4.13) (2018-01-04)


### Bug Fixes

* add missing dbl-sha2-256 ([#44](https://github.com/multiformats/js-multihash/issues/44)) ([4421157](https://github.com/multiformats/js-multihash/commit/4421157)), closes [#38](https://github.com/multiformats/js-multihash/issues/38)



<a name="0.4.12"></a>
## [0.4.12](https://github.com/multiformats/js-multihash/compare/v0.4.11...v0.4.12) (2017-10-20)


### Bug Fixes

* add missing blake2s-256 ([72b856f](https://github.com/multiformats/js-multihash/commit/72b856f))



<a name="0.4.11"></a>
## [0.4.11](https://github.com/multiformats/js-multihash/compare/v0.4.10...v0.4.11) (2017-10-20)



<a name="0.4.10"></a>
## [0.4.10](https://github.com/multiformats/js-multihash/compare/v0.4.9...v0.4.10) (2017-10-13)


### Features

* Adding Skein hash to the mix ([#34](https://github.com/multiformats/js-multihash/issues/34)) ([edf94d5](https://github.com/multiformats/js-multihash/commit/edf94d5))



<a name="0.4.9"></a>
## [0.4.9](https://github.com/multiformats/js-multihash/compare/v0.4.8...v0.4.9) (2017-09-01)



<a name="0.4.8"></a>
## [0.4.8](https://github.com/multiformats/js-multihash/compare/v0.4.7...v0.4.8) (2017-08-23)



<a name="0.4.7"></a>
## [0.4.7](https://github.com/multiformats/js-multihash/compare/v0.4.6...v0.4.7) (2017-08-23)



<a name="0.4.6"></a>
## [0.4.6](https://github.com/multiformats/js-multihash/compare/v0.4.5...v0.4.6) (2017-08-23)



<a name="0.4.5"></a>
## [0.4.5](https://github.com/multiformats/js-multihash/compare/v0.4.4...v0.4.5) (2017-03-23)


### Bug Fixes

* changed name of murmur3 codec to murmur3-128 ([34a9496](https://github.com/multiformats/js-multihash/commit/34a9496))



<a name="0.4.4"></a>
## [0.4.4](https://github.com/multiformats/js-multihash/compare/v0.4.3...v0.4.4) (2017-03-16)



<a name="0.4.3"></a>
## [0.4.3](https://github.com/multiformats/js-multihash/compare/0.4.2...v0.4.3) (2017-02-24)



<a name="0.4.2"></a>
## [0.4.2](https://github.com/multiformats/js-multihash/compare/v0.4.1...0.4.2) (2017-02-24)



<a name="0.4.1"></a>
## [0.4.1](https://github.com/multiformats/js-multihash/compare/v0.4.0...v0.4.1) (2017-02-24)



<a name="0.4.0"></a>
# [0.4.0](https://github.com/multiformats/js-multihash/compare/v0.3.3...v0.4.0) (2017-02-24)



<a name="0.3.3"></a>
## [0.3.3](https://github.com/multiformats/js-multihash/compare/v0.3.2...v0.3.3) (2017-02-09)



<a name="0.3.2"></a>
## [0.3.2](https://github.com/multiformats/js-multihash/compare/v0.3.1...v0.3.2) (2017-01-27)



<a name="0.3.1"></a>
## [0.3.1](https://github.com/multiformats/js-multihash/compare/v0.3.0...v0.3.1) (2016-12-16)


### Features

* add .prefix function ([8fd714c](https://github.com/multiformats/js-multihash/commit/8fd714c))



<a name="0.3.0"></a>
# [0.3.0](https://github.com/multiformats/js-multihash/compare/v0.2.2...v0.3.0) (2016-11-26)


### Bug Fixes

* update package.json ([bca3681](https://github.com/multiformats/js-multihash/commit/bca3681))



<a name="0.2.2"></a>
## [0.2.2](https://github.com/multiformats/js-multihash/compare/v0.2.1...v0.2.2) (2016-05-16)


### Bug Fixes

* Throw errors, do not return them ([1215fa6](https://github.com/multiformats/js-multihash/commit/1215fa6))



<a name="0.2.1"></a>
## 0.2.1 (2016-04-17)



