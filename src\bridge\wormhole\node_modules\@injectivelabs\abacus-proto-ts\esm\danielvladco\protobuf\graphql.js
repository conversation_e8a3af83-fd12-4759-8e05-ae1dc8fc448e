/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "danielvladco.protobuf.graphql";
export var Type;
(function (Type) {
    Type[Type["DEFAULT"] = 0] = "DEFAULT";
    Type[Type["MUTATION"] = 1] = "MUTATION";
    Type[Type["QUERY"] = 2] = "QUERY";
    Type[Type["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Type || (Type = {}));
export function typeFromJSON(object) {
    switch (object) {
        case 0:
        case "DEFAULT":
            return Type.DEFAULT;
        case 1:
        case "MUTATION":
            return Type.MUTATION;
        case 2:
        case "QUERY":
            return Type.QUERY;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Type.UNRECOGNIZED;
    }
}
export function typeToJSON(object) {
    switch (object) {
        case Type.DEFAULT:
            return "DEFAULT";
        case Type.MUTATION:
            return "MUTATION";
        case Type.QUERY:
            return "QUERY";
        case Type.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
export var Upstream;
(function (Upstream) {
    Upstream[Upstream["UPSTREAM_UNSPECIFIED"] = 0] = "UPSTREAM_UNSPECIFIED";
    Upstream[Upstream["UPSTREAM_SERVER"] = 1] = "UPSTREAM_SERVER";
    Upstream[Upstream["UPSTREAM_CLIENT"] = 2] = "UPSTREAM_CLIENT";
    Upstream[Upstream["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Upstream || (Upstream = {}));
export function upstreamFromJSON(object) {
    switch (object) {
        case 0:
        case "UPSTREAM_UNSPECIFIED":
            return Upstream.UPSTREAM_UNSPECIFIED;
        case 1:
        case "UPSTREAM_SERVER":
            return Upstream.UPSTREAM_SERVER;
        case 2:
        case "UPSTREAM_CLIENT":
            return Upstream.UPSTREAM_CLIENT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Upstream.UNRECOGNIZED;
    }
}
export function upstreamToJSON(object) {
    switch (object) {
        case Upstream.UPSTREAM_UNSPECIFIED:
            return "UPSTREAM_UNSPECIFIED";
        case Upstream.UPSTREAM_SERVER:
            return "UPSTREAM_SERVER";
        case Upstream.UPSTREAM_CLIENT:
            return "UPSTREAM_CLIENT";
        case Upstream.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseOneof() {
    return { ignore: false, name: "" };
}
export const Oneof = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.ignore === true) {
            writer.uint32(32).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(42).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOneof();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 4:
                    message.ignore = reader.bool();
                    break;
                case 5:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return Oneof.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseOneof();
        message.ignore = object.ignore ?? false;
        message.name = object.name ?? "";
        return message;
    },
};
function createBaseField() {
    return { required: false, params: "", dirs: "", ignore: false, name: "" };
}
export const Field = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.required === true) {
            writer.uint32(8).bool(message.required);
        }
        if (message.params !== "") {
            writer.uint32(18).string(message.params);
        }
        if (message.dirs !== "") {
            writer.uint32(26).string(message.dirs);
        }
        if (message.ignore === true) {
            writer.uint32(32).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(42).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseField();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.required = reader.bool();
                    break;
                case 2:
                    message.params = reader.string();
                    break;
                case 3:
                    message.dirs = reader.string();
                    break;
                case 4:
                    message.ignore = reader.bool();
                    break;
                case 5:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            required: isSet(object.required) ? Boolean(object.required) : false,
            params: isSet(object.params) ? String(object.params) : "",
            dirs: isSet(object.dirs) ? String(object.dirs) : "",
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.required !== undefined && (obj.required = message.required);
        message.params !== undefined && (obj.params = message.params);
        message.dirs !== undefined && (obj.dirs = message.dirs);
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return Field.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseField();
        message.required = object.required ?? false;
        message.params = object.params ?? "";
        message.dirs = object.dirs ?? "";
        message.ignore = object.ignore ?? false;
        message.name = object.name ?? "";
        return message;
    },
};
function createBaseRpc() {
    return { type: 0, ignore: false, name: "" };
}
export const Rpc = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.type !== 0) {
            writer.uint32(8).int32(message.type);
        }
        if (message.ignore === true) {
            writer.uint32(16).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(26).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpc();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.int32();
                    break;
                case 2:
                    message.ignore = reader.bool();
                    break;
                case 3:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? typeFromJSON(object.type) : 0,
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = typeToJSON(message.type));
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return Rpc.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRpc();
        message.type = object.type ?? 0;
        message.ignore = object.ignore ?? false;
        message.name = object.name ?? "";
        return message;
    },
};
function createBaseSvc() {
    return { type: 0, ignore: false, name: "", upstream: 0 };
}
export const Svc = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.type !== 0) {
            writer.uint32(8).int32(message.type);
        }
        if (message.ignore === true) {
            writer.uint32(16).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(26).string(message.name);
        }
        if (message.upstream !== 0) {
            writer.uint32(32).int32(message.upstream);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSvc();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.int32();
                    break;
                case 2:
                    message.ignore = reader.bool();
                    break;
                case 3:
                    message.name = reader.string();
                    break;
                case 4:
                    message.upstream = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? typeFromJSON(object.type) : 0,
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
            upstream: isSet(object.upstream) ? upstreamFromJSON(object.upstream) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = typeToJSON(message.type));
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        message.upstream !== undefined && (obj.upstream = upstreamToJSON(message.upstream));
        return obj;
    },
    create(base) {
        return Svc.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSvc();
        message.type = object.type ?? 0;
        message.ignore = object.ignore ?? false;
        message.name = object.name ?? "";
        message.upstream = object.upstream ?? 0;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
