import { Network } from '@injectivelabs/networks';
import MsgCreateSpotLimitOrder from '../../msgs/MsgCreateSpotLimitOrder.js';
import MsgCreateDerivativeLimitOrder from '../../msgs/MsgCreateDerivativeLimitOrder.js';
interface OrderInfo {
    subaccountId: string;
    feeRecipient: string;
    price: string;
    quantity: string;
}
interface SpotOrder {
    marketId: string;
    orderInfo: OrderInfo;
    orderType: number;
    triggerPrice?: string;
}
interface DerivativeOrder extends SpotOrder {
    margin: string;
}
export declare class OrderHashManager {
    subaccountIndex: number;
    address: string;
    network: Network;
    nonce: number;
    constructor({ network, address, subaccountIndex, }: {
        network: Network;
        address: string;
        subaccountIndex?: number;
    });
    incrementNonce(): void;
    setNonce(nonce: number): void;
    /**
     * Keep in mind that the order params have to be transformed
     * in proper format that's supported on the chain
     */
    getOrderHashes({ spotOrders, derivativeOrders, }: {
        spotOrders: SpotOrder[];
        derivativeOrders: DerivativeOrder[];
    }): Promise<{
        derivativeOrderHashes: string[];
        spotOrderHashes: string[];
    }>;
    /**
     * Keep in mind that the order params have to be transformed
     * in proper format that's supported on the chain
     */
    getDerivativeOrderHashes(orders: DerivativeOrder[]): Promise<string[]>;
    /**
     * Keep in mind that the order params have to be transformed
     * in proper format that's supported on the chain
     */
    getSpotOrderHashes(orders: SpotOrder[]): Promise<string[]>;
    getSpotOrderHashFromMsg(msg: MsgCreateSpotLimitOrder): Promise<string>;
    getDerivativeOrderHashFromMsg(msg: MsgCreateDerivativeLimitOrder): Promise<string>;
    private initSubaccountNonce;
    private hashTypedData;
    private incrementNonceAndReturn;
}
export {};
