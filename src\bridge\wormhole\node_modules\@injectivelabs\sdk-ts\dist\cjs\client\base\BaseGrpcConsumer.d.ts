import { grpcPkg } from '../../utils/grpc.js';
import { GrpcWebImpl } from './GrpcWebImpl.js';
export default class BaseGrpcConsumer extends GrpcWebImpl {
    protected module: string;
    protected metadata?: grpcPkg.grpc.Metadata;
    constructor(endpoint: string);
    setMetadata(map: Record<string, string>): this;
    clearMetadata(): void;
    getGrpcWebImpl(endpoint: string): BaseGrpcConsumer;
    protected retry<TResponse>(grpcCall: Function, retries?: number, delay?: number): Promise<TResponse>;
}
