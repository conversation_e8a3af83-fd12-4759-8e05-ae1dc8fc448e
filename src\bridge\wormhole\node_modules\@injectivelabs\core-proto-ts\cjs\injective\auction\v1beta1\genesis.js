"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenesisState = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var auction_1 = require("./auction.js");
exports.protobufPackage = "injective.auction.v1beta1";
function createBaseGenesisState() {
    return {
        params: undefined,
        auctionRound: "0",
        highestBid: undefined,
        auctionEndingTimestamp: "0",
        lastAuctionResult: undefined,
    };
}
exports.GenesisState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            auction_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        if (message.auctionRound !== "0") {
            writer.uint32(16).uint64(message.auctionRound);
        }
        if (message.highestBid !== undefined) {
            auction_1.Bid.encode(message.highestBid, writer.uint32(26).fork()).ldelim();
        }
        if (message.auctionEndingTimestamp !== "0") {
            writer.uint32(32).int64(message.auctionEndingTimestamp);
        }
        if (message.lastAuctionResult !== undefined) {
            auction_1.LastAuctionResult.encode(message.lastAuctionResult, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGenesisState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = auction_1.Params.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.auctionRound = longToString(reader.uint64());
                    break;
                case 3:
                    message.highestBid = auction_1.Bid.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.auctionEndingTimestamp = longToString(reader.int64());
                    break;
                case 5:
                    message.lastAuctionResult = auction_1.LastAuctionResult.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            params: isSet(object.params) ? auction_1.Params.fromJSON(object.params) : undefined,
            auctionRound: isSet(object.auctionRound) ? String(object.auctionRound) : "0",
            highestBid: isSet(object.highestBid) ? auction_1.Bid.fromJSON(object.highestBid) : undefined,
            auctionEndingTimestamp: isSet(object.auctionEndingTimestamp) ? String(object.auctionEndingTimestamp) : "0",
            lastAuctionResult: isSet(object.lastAuctionResult)
                ? auction_1.LastAuctionResult.fromJSON(object.lastAuctionResult)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? auction_1.Params.toJSON(message.params) : undefined);
        message.auctionRound !== undefined && (obj.auctionRound = message.auctionRound);
        message.highestBid !== undefined &&
            (obj.highestBid = message.highestBid ? auction_1.Bid.toJSON(message.highestBid) : undefined);
        message.auctionEndingTimestamp !== undefined && (obj.auctionEndingTimestamp = message.auctionEndingTimestamp);
        message.lastAuctionResult !== undefined && (obj.lastAuctionResult = message.lastAuctionResult
            ? auction_1.LastAuctionResult.toJSON(message.lastAuctionResult)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.GenesisState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseGenesisState();
        message.params = (object.params !== undefined && object.params !== null)
            ? auction_1.Params.fromPartial(object.params)
            : undefined;
        message.auctionRound = (_a = object.auctionRound) !== null && _a !== void 0 ? _a : "0";
        message.highestBid = (object.highestBid !== undefined && object.highestBid !== null)
            ? auction_1.Bid.fromPartial(object.highestBid)
            : undefined;
        message.auctionEndingTimestamp = (_b = object.auctionEndingTimestamp) !== null && _b !== void 0 ? _b : "0";
        message.lastAuctionResult = (object.lastAuctionResult !== undefined && object.lastAuctionResult !== null)
            ? auction_1.LastAuctionResult.fromPartial(object.lastAuctionResult)
            : undefined;
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
