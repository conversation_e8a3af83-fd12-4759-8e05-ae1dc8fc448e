{"version": 3, "file": "eip2930Transaction.js", "sourceRoot": "", "sources": ["../src/eip2930Transaction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAUwB;AAExB,qDAAmD;AACnD,iCAQgB;AAEhB,+BAA0D;AAE1D,IAAM,gBAAgB,GAAG,CAAC,CAAA;AAC1B,IAAM,uBAAuB,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAA;AAElG;;;;;GAKG;AACH;IAA0D,gDAA6C;IAmIrG;;;;;;OAMG;IACH,sCAAmB,MAA+B,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QAAxE,iBAiDC;;gBAhDC,wCAAW,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC;QAnIpD;;;;;WAKG;QACO,sBAAgB,GAAG,QAAQ,CAAA;QA8H3B,IAAA,OAAO,GAA2B,MAAM,QAAjC,EAAE,UAAU,GAAe,MAAM,WAArB,EAAE,QAAQ,GAAK,MAAM,SAAX,CAAW;QAEhD,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnD,KAAI,CAAC,OAAO,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAA;QAEtC,mCAAmC;QACnC,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;SAClD;QACD,KAAI,CAAC,kBAAkB,GAAG,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QAEtE,kCAAkC;QAClC,IAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC,CAAA;QACtE,KAAI,CAAC,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;QAC3C,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC,cAAc,CAAA;QACnD,iCAAiC;QACjC,kBAAW,CAAC,gBAAgB,CAAC,KAAI,CAAC,UAAU,CAAC,CAAA;QAE7C,KAAI,CAAC,QAAQ,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEnE,KAAI,CAAC,+BAA+B,CAAC;YACnC,QAAQ,EAAE,KAAI,CAAC,QAAQ;SACxB,CAAC,CAAA;QAEF,IAAI,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,6BAAW,CAAC,EAAE;YACpD,IAAM,GAAG,GAAG,KAAI,CAAC,SAAS,CAAC,+CAA+C,CAAC,CAAA;YAC3E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAI,KAAI,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC9C,IAAM,GAAG,GAAG,KAAI,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAA;YACrF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,KAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,IAAM,GAAG,GAAG,KAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,KAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAA,2BAAoB,EAAC,KAAI,CAAC,MAAM,EAAE,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACpD;QACD,IAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,CAAA;SACpB;;IACH,CAAC;IAtKD,sBAAI,iDAAO;QALX;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,CAAC,CAAA;QACf,CAAC;;;OAAA;IAOD,sBAAI,iDAAO;QALX;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,CAAC,CAAA;QACf,CAAC;;;OAAA;IAOD,sBAAI,iDAAO;QALX;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,CAAC,CAAA;QACf,CAAC;;;OAAA;IAED;;;;;;;;;OASG;IACW,uCAAU,GAAxB,UAAyB,MAA+B,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QAC5E,OAAO,IAAI,4BAA4B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACW,6CAAgB,GAA9B,UAA+B,UAAkB,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACrE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,uBAAuB,CAAC,EAAE;YAC3D,MAAM,IAAI,KAAK,CACb,6FAAsF,gBAAgB,yBAAe,UAAU;iBAC5H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;iBACX,QAAQ,CAAC,KAAK,CAAC,CAAE,CACrB,CAAA;SACF;QAED,IAAM,MAAM,GAAG,qBAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,4BAA4B,CAAC,eAAe,CAAC,MAAa,EAAE,IAAI,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;OAQG;IACW,gDAAmB,GAAjC,UAAkC,UAAkB,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACxE,OAAO,4BAA4B,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IACxE,CAAC;IAED;;;;;OAKG;IACW,4CAAe,GAA7B,UAA8B,MAAoC,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACtF,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,EAAE,EAAE;YAC/C,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAA;SACF;QAEK,IAAA,KAAA,OAA6E,MAAM,KAAA,EAAlF,OAAO,QAAA,EAAE,KAAK,QAAA,EAAE,QAAQ,QAAA,EAAE,QAAQ,QAAA,EAAE,EAAE,QAAA,EAAE,KAAK,QAAA,EAAE,IAAI,QAAA,EAAE,UAAU,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,SAAU,CAAA;QAEzF,IAAA,yCAAuB,EAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,KAAK,OAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAA;QAEtE,IAAM,eAAe,GAAe,EAAE,CAAA;QAEtC,OAAO,IAAI,4BAA4B,CACrC;YACE,OAAO,EAAE,IAAI,oBAAE,CAAC,OAAO,CAAC;YACxB,KAAK,OAAA;YACL,QAAQ,UAAA;YACR,QAAQ,UAAA;YACR,EAAE,IAAA;YACF,KAAK,OAAA;YACL,IAAI,MAAA;YACJ,UAAU,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,eAAe;YACzC,CAAC,EAAE,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,oBAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YAC1C,CAAC,GAAA;YACD,CAAC,GAAA;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA4DD;;OAEG;IACH,iDAAU,GAAV;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAM,IAAI,GAAG,iBAAM,UAAU,WAAE,CAAA;QAC/B,IAAI,CAAC,KAAK,CAAC,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAEvE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,qDAAc,GAAd;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,0CAAG,GAAH;QACE,OAAO;YACL,IAAA,oCAAkB,EAAC,IAAI,CAAC,OAAO,CAAC;YAChC,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACpE,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,gDAAS,GAAT;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACvB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,qBAAG,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,uDAAgB,GAAhB,UAAiB,WAAkB;QAAlB,4BAAA,EAAA,kBAAkB;QACjC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QACnC,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,qBAAG,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC,CAAC,CAAA;QACjF,IAAI,WAAW,EAAE;YACf,OAAO,IAAA,2BAAS,EAAC,OAAO,CAAC,CAAA;SAC1B;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;;;;OAKG;IACI,2CAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;aAC9C;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,IAAA,2BAAS,EAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACI,kEAA2B,GAAlC;QACE,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAChC,CAAC;IAED;;OAEG;IACI,yDAAkB,GAAzB;;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAElD,uGAAuG;QACvG,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,IAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAEK,IAAA,KAAoB,IAAI,EAAtB,OAAO,aAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAS,CAAA;QAC9B,IAAI;YACF,OAAO,IAAA,2BAAS,EACd,OAAO,EACP,OAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,gDAAgD;YACnE,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAA,oCAAkB,EAAC,CAAE,CAAC,CACvB,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED,wDAAiB,GAAjB,UAAkB,CAAS,EAAE,CAAS,EAAE,CAAS;QAC/C,IAAM,IAAI,yBAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAA;QAEvD,OAAO,4BAA4B,CAAC,UAAU,CAC5C;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,GAAG,EAAE,CAAC;YACjB,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;SACb,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,6CAAM,GAAN;QACE,IAAM,cAAc,GAAG,kBAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAErE,OAAO;YACL,OAAO,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,OAAO,CAAC;YAC9B,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,UAAU,EAAE,cAAc;YAC1B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACtD,CAAA;IACH,CAAC;IAED;;OAEG;IACI,+CAAQ,GAAf;;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,mFAAmF;QACnF,QAAQ,IAAI,oBAAa,IAAI,CAAC,QAAQ,8BAAoB,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,mCAAI,CAAC,CAAE,CAAA;QACxF,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,gDAAS,GAAnB,UAAoB,GAAW;QAC7B,OAAO,UAAG,GAAG,eAAK,IAAI,CAAC,QAAQ,EAAE,MAAG,CAAA;IACtC,CAAC;IACH,mCAAC;AAAD,CAAC,AAxZD,CAA0D,iCAAe,GAwZxE"}