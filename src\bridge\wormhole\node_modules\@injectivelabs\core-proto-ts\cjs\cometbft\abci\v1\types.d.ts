import _m0 from "protobufjs/minimal.js";
import { Duration } from "../../../google/protobuf/duration";
import { ProofOps } from "../../crypto/v1/proof";
import { ConsensusParams } from "../../types/v1/params";
import { BlockIDFlag } from "../../types/v1/validator";
export declare const protobufPackage = "cometbft.abci.v1";
/**
 * Type of the transaction check request.
 *
 * This enumeration is incompatible with the CheckTxType definition in
 * cometbft.abci.v1beta1 and therefore shall not be used in encoding with the same
 * field number.
 */
export declare enum CheckTxType {
    /** CHECK_TX_TYPE_UNKNOWN - Unknown */
    CHECK_TX_TYPE_UNKNOWN = 0,
    /** CHECK_TX_TYPE_RECHECK - Recheck (2nd, 3rd, etc.) */
    CHECK_TX_TYPE_RECHECK = 1,
    /** CHECK_TX_TYPE_CHECK - Check (1st time) */
    CHECK_TX_TYPE_CHECK = 2,
    UNRECOGNIZED = -1
}
export declare function checkTxTypeFromJSON(object: any): CheckTxType;
export declare function checkTxTypeToJSON(object: CheckTxType): string;
/** The result of offering a snapshot. */
export declare enum OfferSnapshotResult {
    /** OFFER_SNAPSHOT_RESULT_UNKNOWN - Unknown result, abort all snapshot restoration */
    OFFER_SNAPSHOT_RESULT_UNKNOWN = 0,
    /** OFFER_SNAPSHOT_RESULT_ACCEPT - Snapshot accepted, apply chunks */
    OFFER_SNAPSHOT_RESULT_ACCEPT = 1,
    /** OFFER_SNAPSHOT_RESULT_ABORT - Abort all snapshot restoration */
    OFFER_SNAPSHOT_RESULT_ABORT = 2,
    /** OFFER_SNAPSHOT_RESULT_REJECT - Reject this specific snapshot, try others */
    OFFER_SNAPSHOT_RESULT_REJECT = 3,
    /** OFFER_SNAPSHOT_RESULT_REJECT_FORMAT - Reject all snapshots of this format, try others */
    OFFER_SNAPSHOT_RESULT_REJECT_FORMAT = 4,
    /** OFFER_SNAPSHOT_RESULT_REJECT_SENDER - Reject all snapshots from the sender(s), try others */
    OFFER_SNAPSHOT_RESULT_REJECT_SENDER = 5,
    UNRECOGNIZED = -1
}
export declare function offerSnapshotResultFromJSON(object: any): OfferSnapshotResult;
export declare function offerSnapshotResultToJSON(object: OfferSnapshotResult): string;
/** The result of applying a snapshot chunk. */
export declare enum ApplySnapshotChunkResult {
    /** APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN - Unknown result, abort all snapshot restoration */
    APPLY_SNAPSHOT_CHUNK_RESULT_UNKNOWN = 0,
    /** APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT - Chunk successfully accepted */
    APPLY_SNAPSHOT_CHUNK_RESULT_ACCEPT = 1,
    /** APPLY_SNAPSHOT_CHUNK_RESULT_ABORT - Abort all snapshot restoration */
    APPLY_SNAPSHOT_CHUNK_RESULT_ABORT = 2,
    /** APPLY_SNAPSHOT_CHUNK_RESULT_RETRY - Retry chunk (combine with refetch and reject) */
    APPLY_SNAPSHOT_CHUNK_RESULT_RETRY = 3,
    /** APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT - Retry snapshot (combine with refetch and reject) */
    APPLY_SNAPSHOT_CHUNK_RESULT_RETRY_SNAPSHOT = 4,
    /** APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT - Reject this snapshot, try others */
    APPLY_SNAPSHOT_CHUNK_RESULT_REJECT_SNAPSHOT = 5,
    UNRECOGNIZED = -1
}
export declare function applySnapshotChunkResultFromJSON(object: any): ApplySnapshotChunkResult;
export declare function applySnapshotChunkResultToJSON(object: ApplySnapshotChunkResult): string;
/** ProcessProposalStatus is the status of the proposal processing. */
export declare enum ProcessProposalStatus {
    /** PROCESS_PROPOSAL_STATUS_UNKNOWN - Unknown */
    PROCESS_PROPOSAL_STATUS_UNKNOWN = 0,
    /** PROCESS_PROPOSAL_STATUS_ACCEPT - Accepted */
    PROCESS_PROPOSAL_STATUS_ACCEPT = 1,
    /** PROCESS_PROPOSAL_STATUS_REJECT - Rejected */
    PROCESS_PROPOSAL_STATUS_REJECT = 2,
    UNRECOGNIZED = -1
}
export declare function processProposalStatusFromJSON(object: any): ProcessProposalStatus;
export declare function processProposalStatusToJSON(object: ProcessProposalStatus): string;
/** VerifyVoteExtensionStatus is the status of the vote extension verification. */
export declare enum VerifyVoteExtensionStatus {
    /** VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN - Unknown */
    VERIFY_VOTE_EXTENSION_STATUS_UNKNOWN = 0,
    /** VERIFY_VOTE_EXTENSION_STATUS_ACCEPT - Accepted */
    VERIFY_VOTE_EXTENSION_STATUS_ACCEPT = 1,
    /**
     * VERIFY_VOTE_EXTENSION_STATUS_REJECT - Rejecting the vote extension will reject the entire precommit by the sender.
     * Incorrectly implementing this thus has liveness implications as it may affect
     * CometBFT's ability to receive 2/3+ valid votes to finalize the block.
     * Honest nodes should never be rejected.
     */
    VERIFY_VOTE_EXTENSION_STATUS_REJECT = 2,
    UNRECOGNIZED = -1
}
export declare function verifyVoteExtensionStatusFromJSON(object: any): VerifyVoteExtensionStatus;
export declare function verifyVoteExtensionStatusToJSON(object: VerifyVoteExtensionStatus): string;
/** The type of misbehavior committed by a validator. */
export declare enum MisbehaviorType {
    /** MISBEHAVIOR_TYPE_UNKNOWN - Unknown */
    MISBEHAVIOR_TYPE_UNKNOWN = 0,
    /** MISBEHAVIOR_TYPE_DUPLICATE_VOTE - Duplicate vote */
    MISBEHAVIOR_TYPE_DUPLICATE_VOTE = 1,
    /** MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK - Light client attack */
    MISBEHAVIOR_TYPE_LIGHT_CLIENT_ATTACK = 2,
    UNRECOGNIZED = -1
}
export declare function misbehaviorTypeFromJSON(object: any): MisbehaviorType;
export declare function misbehaviorTypeToJSON(object: MisbehaviorType): string;
/** Request represents a request to the ABCI application. */
export interface Request {
    echo?: EchoRequest | undefined;
    flush?: FlushRequest | undefined;
    info?: InfoRequest | undefined;
    initChain?: InitChainRequest | undefined;
    query?: QueryRequest | undefined;
    checkTx?: CheckTxRequest | undefined;
    commit?: CommitRequest | undefined;
    listSnapshots?: ListSnapshotsRequest | undefined;
    offerSnapshot?: OfferSnapshotRequest | undefined;
    loadSnapshotChunk?: LoadSnapshotChunkRequest | undefined;
    applySnapshotChunk?: ApplySnapshotChunkRequest | undefined;
    prepareProposal?: PrepareProposalRequest | undefined;
    processProposal?: ProcessProposalRequest | undefined;
    extendVote?: ExtendVoteRequest | undefined;
    verifyVoteExtension?: VerifyVoteExtensionRequest | undefined;
    finalizeBlock?: FinalizeBlockRequest | undefined;
}
/** EchoRequest is a request to "echo" the given string. */
export interface EchoRequest {
    message: string;
}
/** FlushRequest is a request to flush the write buffer. */
export interface FlushRequest {
}
/** InfoRequest is a request for the ABCI application version. */
export interface InfoRequest {
    version: string;
    blockVersion: string;
    p2pVersion: string;
    abciVersion: string;
}
/** InitChainRequest is a request to initialize the blockchain. */
export interface InitChainRequest {
    time: Date | undefined;
    chainId: string;
    consensusParams: ConsensusParams | undefined;
    validators: ValidatorUpdate[];
    appStateBytes: Uint8Array;
    initialHeight: string;
}
/** QueryRequest is a request to query the application state. */
export interface QueryRequest {
    data: Uint8Array;
    path: string;
    height: string;
    prove: boolean;
}
/** CheckTxRequest is a request to check that the transaction is valid. */
export interface CheckTxRequest {
    tx: Uint8Array;
    type: CheckTxType;
}
/** CommitRequest is a request to commit the pending application state. */
export interface CommitRequest {
}
/** Request to list available snapshots. */
export interface ListSnapshotsRequest {
}
/** Request offering a snapshot to the application. */
export interface OfferSnapshotRequest {
    /** snapshot offered by peers */
    snapshot: Snapshot | undefined;
    /** light client-verified app hash for snapshot height */
    appHash: Uint8Array;
}
/** Request to load a snapshot chunk. */
export interface LoadSnapshotChunkRequest {
    height: string;
    format: number;
    chunk: number;
}
/** Request to apply a snapshot chunk. */
export interface ApplySnapshotChunkRequest {
    index: number;
    chunk: Uint8Array;
    sender: string;
}
/**
 * PrepareProposalRequest is a request for the ABCI application to prepare a new
 * block proposal.
 */
export interface PrepareProposalRequest {
    /** the modified transactions cannot exceed this size. */
    maxTxBytes: string;
    /**
     * txs is an array of transactions that will be included in a block,
     * sent to the app for possible modifications.
     */
    txs: Uint8Array[];
    localLastCommit: ExtendedCommitInfo | undefined;
    misbehavior: Misbehavior[];
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the validator proposing the block. */
    proposerAddress: Uint8Array;
}
/**
 * ProcessProposalRequest is a request for the ABCI application to process a proposal
 * received from another validator.
 */
export interface ProcessProposalRequest {
    txs: Uint8Array[];
    proposedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    /** Merkle root hash of the fields of the proposed block. */
    hash: Uint8Array;
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
}
/** ExtendVoteRequest extends a precommit vote with application-injected data. */
export interface ExtendVoteRequest {
    /** the hash of the block that this vote may be referring to */
    hash: Uint8Array;
    /** the height of the extended vote */
    height: string;
    /** info of the block that this vote may be referring to */
    time: Date | undefined;
    txs: Uint8Array[];
    proposedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
}
/**
 * VerifyVoteExtensionRequest is a request for the application to verify a vote extension
 * produced by a different validator.
 */
export interface VerifyVoteExtensionRequest {
    /** the hash of the block that this received vote corresponds to */
    hash: Uint8Array;
    /** the validator that signed the vote extension */
    validatorAddress: Uint8Array;
    height: string;
    voteExtension: Uint8Array;
}
/** FinalizeBlockRequest is a request to finalize the block. */
export interface FinalizeBlockRequest {
    txs: Uint8Array[];
    decidedLastCommit: CommitInfo | undefined;
    misbehavior: Misbehavior[];
    /** Merkle root hash of the fields of the decided block. */
    hash: Uint8Array;
    height: string;
    time: Date | undefined;
    nextValidatorsHash: Uint8Array;
    /** address of the public key of the original proposer of the block. */
    proposerAddress: Uint8Array;
    /** If the node is syncing/replaying blocks - target height. If not, syncing_to == height. */
    syncingToHeight: string;
}
/** Response represents a response from the ABCI application. */
export interface Response {
    exception?: ExceptionResponse | undefined;
    echo?: EchoResponse | undefined;
    flush?: FlushResponse | undefined;
    info?: InfoResponse | undefined;
    initChain?: InitChainResponse | undefined;
    query?: QueryResponse | undefined;
    checkTx?: CheckTxResponse | undefined;
    commit?: CommitResponse | undefined;
    listSnapshots?: ListSnapshotsResponse | undefined;
    offerSnapshot?: OfferSnapshotResponse | undefined;
    loadSnapshotChunk?: LoadSnapshotChunkResponse | undefined;
    applySnapshotChunk?: ApplySnapshotChunkResponse | undefined;
    prepareProposal?: PrepareProposalResponse | undefined;
    processProposal?: ProcessProposalResponse | undefined;
    extendVote?: ExtendVoteResponse | undefined;
    verifyVoteExtension?: VerifyVoteExtensionResponse | undefined;
    finalizeBlock?: FinalizeBlockResponse | undefined;
}
/** nondeterministic */
export interface ExceptionResponse {
    error: string;
}
/** EchoResponse indicates that the connection is still alive. */
export interface EchoResponse {
    message: string;
}
/** FlushResponse indicates that the write buffer was flushed. */
export interface FlushResponse {
}
/** InfoResponse contains the ABCI application version information. */
export interface InfoResponse {
    data: string;
    version: string;
    appVersion: string;
    lastBlockHeight: string;
    lastBlockAppHash: Uint8Array;
    lanePriorities: {
        [key: string]: number;
    };
    defaultLane: string;
}
export interface InfoResponse_LanePrioritiesEntry {
    key: string;
    value: number;
}
/**
 * InitChainResponse contains the ABCI application's hash and updates to the
 * validator set and/or the consensus params, if any.
 */
export interface InitChainResponse {
    consensusParams: ConsensusParams | undefined;
    validators: ValidatorUpdate[];
    appHash: Uint8Array;
}
/** QueryResponse contains the ABCI application data along with a proof. */
export interface QueryResponse {
    code: number;
    /** bytes data = 2; // use "value" instead. */
    log: string;
    /** nondeterministic */
    info: string;
    index: string;
    key: Uint8Array;
    value: Uint8Array;
    proofOps: ProofOps | undefined;
    height: string;
    codespace: string;
}
/**
 * CheckTxResponse shows if the transaction was deemed valid by the ABCI
 * application.
 */
export interface CheckTxResponse {
    code: number;
    data: Uint8Array;
    /** nondeterministic */
    log: string;
    /** nondeterministic */
    info: string;
    gasWanted: string;
    gasUsed: string;
    /** nondeterministic */
    events: Event[];
    codespace: string;
    laneId: string;
}
/** CommitResponse indicates how much blocks should CometBFT retain. */
export interface CommitResponse {
    retainHeight: string;
}
/** ListSnapshotsResponse contains the list of snapshots. */
export interface ListSnapshotsResponse {
    snapshots: Snapshot[];
}
/**
 * OfferSnapshotResponse indicates the ABCI application decision whenever to
 * provide a snapshot to the requester or not.
 */
export interface OfferSnapshotResponse {
    result: OfferSnapshotResult;
}
/** LoadSnapshotChunkResponse returns a snapshot's chunk. */
export interface LoadSnapshotChunkResponse {
    chunk: Uint8Array;
}
/** ApplySnapshotChunkResponse returns a result of applying the specified chunk. */
export interface ApplySnapshotChunkResponse {
    result: ApplySnapshotChunkResult;
    /** Chunks to refetch and reapply */
    refetchChunks: number[];
    /** Chunk senders to reject and ban */
    rejectSenders: string[];
}
/** PrepareProposalResponse contains a list of transactions, which will form a block. */
export interface PrepareProposalResponse {
    txs: Uint8Array[];
}
/**
 * ProcessProposalResponse indicates the ABCI application's decision whenever
 * the given proposal should be accepted or not.
 */
export interface ProcessProposalResponse {
    status: ProcessProposalStatus;
}
/**
 * ExtendVoteResponse contains the vote extension that the application would like to
 * attach to its next precommit vote.
 */
export interface ExtendVoteResponse {
    voteExtension: Uint8Array;
}
/**
 * VerifyVoteExtensionResponse indicates the ABCI application's decision
 * whenever the vote extension should be accepted or not.
 */
export interface VerifyVoteExtensionResponse {
    status: VerifyVoteExtensionStatus;
}
/** FinalizeBlockResponse contains the result of executing the block. */
export interface FinalizeBlockResponse {
    /** set of block events emitted as part of executing the block */
    events: Event[];
    /**
     * the result of executing each transaction including the events
     * the particular transaction emitted. This should match the order
     * of the transactions delivered in the block itself
     */
    txResults: ExecTxResult[];
    /** a list of updates to the validator set. These will reflect the validator set at current height + 2. */
    validatorUpdates: ValidatorUpdate[];
    /** updates to the consensus params, if any. */
    consensusParamUpdates: ConsensusParams | undefined;
    /**
     * app_hash is the hash of the applications' state which is used to confirm
     * that execution of the transactions was deterministic.
     * It is up to the application to decide which algorithm to use.
     */
    appHash: Uint8Array;
    /**
     * delay between the time when this block is committed and the next height is started.
     * previously `timeout_commit` in config.toml
     */
    nextBlockDelay: Duration | undefined;
}
/** CommitInfo contains votes for the particular round. */
export interface CommitInfo {
    round: number;
    votes: VoteInfo[];
}
/**
 * ExtendedCommitInfo is similar to CommitInfo except that it is only used in
 * the PrepareProposal request such that Tendermint can provide vote extensions
 * to the application.
 */
export interface ExtendedCommitInfo {
    /** The round at which the block proposer decided in the previous height. */
    round: number;
    /**
     * List of validators' addresses in the last validator set with their voting
     * information, including vote extensions.
     */
    votes: ExtendedVoteInfo[];
}
/**
 * Event allows application developers to attach additional information to
 * ResponseFinalizeBlock and ResponseCheckTx.
 * Up to 0.37, this could also be used in ResponseBeginBlock, ResponseEndBlock,
 * and ResponseDeliverTx.
 * Later, transactions may be queried using these events.
 */
export interface Event {
    type: string;
    attributes: EventAttribute[];
}
/** EventAttribute is a single key-value pair, associated with an event. */
export interface EventAttribute {
    key: string;
    value: string;
    /** nondeterministic */
    index: boolean;
}
/**
 * ExecTxResult contains results of executing one individual transaction.
 *
 * * Its structure is equivalent to #ResponseDeliverTx which will be deprecated/deleted
 */
export interface ExecTxResult {
    code: number;
    data: Uint8Array;
    /** nondeterministic */
    log: string;
    /** nondeterministic */
    info: string;
    gasWanted: string;
    gasUsed: string;
    /** nondeterministic */
    events: Event[];
    codespace: string;
}
/**
 * TxResult contains results of executing the transaction.
 *
 * One usage is indexing transaction results.
 */
export interface TxResult {
    height: string;
    index: number;
    tx: Uint8Array;
    result: ExecTxResult | undefined;
}
/** Validator in the validator set. */
export interface Validator {
    /** The first 20 bytes of SHA256(public key) */
    address: Uint8Array;
    /** PubKey pub_key = 2 [(gogoproto.nullable)=false]; */
    power: string;
}
/** ValidatorUpdate is a singular update to a validator set. */
export interface ValidatorUpdate {
    power: string;
    pubKeyBytes: Uint8Array;
    pubKeyType: string;
}
/** VoteInfo contains the information about the vote. */
export interface VoteInfo {
    validator: Validator | undefined;
    blockIdFlag: BlockIDFlag;
}
/** ExtendedVoteInfo extends VoteInfo with the vote extensions (non-deterministic). */
export interface ExtendedVoteInfo {
    /** The validator that sent the vote. */
    validator: Validator | undefined;
    /** Non-deterministic extension provided by the sending validator's application. */
    voteExtension: Uint8Array;
    /** Vote extension signature created by CometBFT */
    extensionSignature: Uint8Array;
    /** block_id_flag indicates whether the validator voted for a block, nil, or did not vote at all */
    blockIdFlag: BlockIDFlag;
}
/** Misbehavior is a type of misbehavior committed by a validator. */
export interface Misbehavior {
    type: MisbehaviorType;
    /** The offending validator */
    validator: Validator | undefined;
    /** The height when the offense occurred */
    height: string;
    /** The corresponding time where the offense occurred */
    time: Date | undefined;
    /**
     * Total voting power of the validator set in case the ABCI application does
     * not store historical validators.
     * https://github.com/tendermint/tendermint/issues/4581
     */
    totalVotingPower: string;
}
/** Snapshot of the ABCI application state. */
export interface Snapshot {
    /** The height at which the snapshot was taken */
    height: string;
    /** The application-specific snapshot format */
    format: number;
    /** Number of chunks in the snapshot */
    chunks: number;
    /** Arbitrary snapshot hash, equal only if identical */
    hash: Uint8Array;
    /** Arbitrary application metadata */
    metadata: Uint8Array;
}
export declare const Request: {
    encode(message: Request, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Request;
    fromJSON(object: any): Request;
    toJSON(message: Request): unknown;
    create(base?: DeepPartial<Request>): Request;
    fromPartial(object: DeepPartial<Request>): Request;
};
export declare const EchoRequest: {
    encode(message: EchoRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EchoRequest;
    fromJSON(object: any): EchoRequest;
    toJSON(message: EchoRequest): unknown;
    create(base?: DeepPartial<EchoRequest>): EchoRequest;
    fromPartial(object: DeepPartial<EchoRequest>): EchoRequest;
};
export declare const FlushRequest: {
    encode(_: FlushRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FlushRequest;
    fromJSON(_: any): FlushRequest;
    toJSON(_: FlushRequest): unknown;
    create(base?: DeepPartial<FlushRequest>): FlushRequest;
    fromPartial(_: DeepPartial<FlushRequest>): FlushRequest;
};
export declare const InfoRequest: {
    encode(message: InfoRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InfoRequest;
    fromJSON(object: any): InfoRequest;
    toJSON(message: InfoRequest): unknown;
    create(base?: DeepPartial<InfoRequest>): InfoRequest;
    fromPartial(object: DeepPartial<InfoRequest>): InfoRequest;
};
export declare const InitChainRequest: {
    encode(message: InitChainRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InitChainRequest;
    fromJSON(object: any): InitChainRequest;
    toJSON(message: InitChainRequest): unknown;
    create(base?: DeepPartial<InitChainRequest>): InitChainRequest;
    fromPartial(object: DeepPartial<InitChainRequest>): InitChainRequest;
};
export declare const QueryRequest: {
    encode(message: QueryRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRequest;
    fromJSON(object: any): QueryRequest;
    toJSON(message: QueryRequest): unknown;
    create(base?: DeepPartial<QueryRequest>): QueryRequest;
    fromPartial(object: DeepPartial<QueryRequest>): QueryRequest;
};
export declare const CheckTxRequest: {
    encode(message: CheckTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CheckTxRequest;
    fromJSON(object: any): CheckTxRequest;
    toJSON(message: CheckTxRequest): unknown;
    create(base?: DeepPartial<CheckTxRequest>): CheckTxRequest;
    fromPartial(object: DeepPartial<CheckTxRequest>): CheckTxRequest;
};
export declare const CommitRequest: {
    encode(_: CommitRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CommitRequest;
    fromJSON(_: any): CommitRequest;
    toJSON(_: CommitRequest): unknown;
    create(base?: DeepPartial<CommitRequest>): CommitRequest;
    fromPartial(_: DeepPartial<CommitRequest>): CommitRequest;
};
export declare const ListSnapshotsRequest: {
    encode(_: ListSnapshotsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListSnapshotsRequest;
    fromJSON(_: any): ListSnapshotsRequest;
    toJSON(_: ListSnapshotsRequest): unknown;
    create(base?: DeepPartial<ListSnapshotsRequest>): ListSnapshotsRequest;
    fromPartial(_: DeepPartial<ListSnapshotsRequest>): ListSnapshotsRequest;
};
export declare const OfferSnapshotRequest: {
    encode(message: OfferSnapshotRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OfferSnapshotRequest;
    fromJSON(object: any): OfferSnapshotRequest;
    toJSON(message: OfferSnapshotRequest): unknown;
    create(base?: DeepPartial<OfferSnapshotRequest>): OfferSnapshotRequest;
    fromPartial(object: DeepPartial<OfferSnapshotRequest>): OfferSnapshotRequest;
};
export declare const LoadSnapshotChunkRequest: {
    encode(message: LoadSnapshotChunkRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LoadSnapshotChunkRequest;
    fromJSON(object: any): LoadSnapshotChunkRequest;
    toJSON(message: LoadSnapshotChunkRequest): unknown;
    create(base?: DeepPartial<LoadSnapshotChunkRequest>): LoadSnapshotChunkRequest;
    fromPartial(object: DeepPartial<LoadSnapshotChunkRequest>): LoadSnapshotChunkRequest;
};
export declare const ApplySnapshotChunkRequest: {
    encode(message: ApplySnapshotChunkRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ApplySnapshotChunkRequest;
    fromJSON(object: any): ApplySnapshotChunkRequest;
    toJSON(message: ApplySnapshotChunkRequest): unknown;
    create(base?: DeepPartial<ApplySnapshotChunkRequest>): ApplySnapshotChunkRequest;
    fromPartial(object: DeepPartial<ApplySnapshotChunkRequest>): ApplySnapshotChunkRequest;
};
export declare const PrepareProposalRequest: {
    encode(message: PrepareProposalRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareProposalRequest;
    fromJSON(object: any): PrepareProposalRequest;
    toJSON(message: PrepareProposalRequest): unknown;
    create(base?: DeepPartial<PrepareProposalRequest>): PrepareProposalRequest;
    fromPartial(object: DeepPartial<PrepareProposalRequest>): PrepareProposalRequest;
};
export declare const ProcessProposalRequest: {
    encode(message: ProcessProposalRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProcessProposalRequest;
    fromJSON(object: any): ProcessProposalRequest;
    toJSON(message: ProcessProposalRequest): unknown;
    create(base?: DeepPartial<ProcessProposalRequest>): ProcessProposalRequest;
    fromPartial(object: DeepPartial<ProcessProposalRequest>): ProcessProposalRequest;
};
export declare const ExtendVoteRequest: {
    encode(message: ExtendVoteRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendVoteRequest;
    fromJSON(object: any): ExtendVoteRequest;
    toJSON(message: ExtendVoteRequest): unknown;
    create(base?: DeepPartial<ExtendVoteRequest>): ExtendVoteRequest;
    fromPartial(object: DeepPartial<ExtendVoteRequest>): ExtendVoteRequest;
};
export declare const VerifyVoteExtensionRequest: {
    encode(message: VerifyVoteExtensionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VerifyVoteExtensionRequest;
    fromJSON(object: any): VerifyVoteExtensionRequest;
    toJSON(message: VerifyVoteExtensionRequest): unknown;
    create(base?: DeepPartial<VerifyVoteExtensionRequest>): VerifyVoteExtensionRequest;
    fromPartial(object: DeepPartial<VerifyVoteExtensionRequest>): VerifyVoteExtensionRequest;
};
export declare const FinalizeBlockRequest: {
    encode(message: FinalizeBlockRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FinalizeBlockRequest;
    fromJSON(object: any): FinalizeBlockRequest;
    toJSON(message: FinalizeBlockRequest): unknown;
    create(base?: DeepPartial<FinalizeBlockRequest>): FinalizeBlockRequest;
    fromPartial(object: DeepPartial<FinalizeBlockRequest>): FinalizeBlockRequest;
};
export declare const Response: {
    encode(message: Response, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Response;
    fromJSON(object: any): Response;
    toJSON(message: Response): unknown;
    create(base?: DeepPartial<Response>): Response;
    fromPartial(object: DeepPartial<Response>): Response;
};
export declare const ExceptionResponse: {
    encode(message: ExceptionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExceptionResponse;
    fromJSON(object: any): ExceptionResponse;
    toJSON(message: ExceptionResponse): unknown;
    create(base?: DeepPartial<ExceptionResponse>): ExceptionResponse;
    fromPartial(object: DeepPartial<ExceptionResponse>): ExceptionResponse;
};
export declare const EchoResponse: {
    encode(message: EchoResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EchoResponse;
    fromJSON(object: any): EchoResponse;
    toJSON(message: EchoResponse): unknown;
    create(base?: DeepPartial<EchoResponse>): EchoResponse;
    fromPartial(object: DeepPartial<EchoResponse>): EchoResponse;
};
export declare const FlushResponse: {
    encode(_: FlushResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FlushResponse;
    fromJSON(_: any): FlushResponse;
    toJSON(_: FlushResponse): unknown;
    create(base?: DeepPartial<FlushResponse>): FlushResponse;
    fromPartial(_: DeepPartial<FlushResponse>): FlushResponse;
};
export declare const InfoResponse: {
    encode(message: InfoResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InfoResponse;
    fromJSON(object: any): InfoResponse;
    toJSON(message: InfoResponse): unknown;
    create(base?: DeepPartial<InfoResponse>): InfoResponse;
    fromPartial(object: DeepPartial<InfoResponse>): InfoResponse;
};
export declare const InfoResponse_LanePrioritiesEntry: {
    encode(message: InfoResponse_LanePrioritiesEntry, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InfoResponse_LanePrioritiesEntry;
    fromJSON(object: any): InfoResponse_LanePrioritiesEntry;
    toJSON(message: InfoResponse_LanePrioritiesEntry): unknown;
    create(base?: DeepPartial<InfoResponse_LanePrioritiesEntry>): InfoResponse_LanePrioritiesEntry;
    fromPartial(object: DeepPartial<InfoResponse_LanePrioritiesEntry>): InfoResponse_LanePrioritiesEntry;
};
export declare const InitChainResponse: {
    encode(message: InitChainResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): InitChainResponse;
    fromJSON(object: any): InitChainResponse;
    toJSON(message: InitChainResponse): unknown;
    create(base?: DeepPartial<InitChainResponse>): InitChainResponse;
    fromPartial(object: DeepPartial<InitChainResponse>): InitChainResponse;
};
export declare const QueryResponse: {
    encode(message: QueryResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryResponse;
    fromJSON(object: any): QueryResponse;
    toJSON(message: QueryResponse): unknown;
    create(base?: DeepPartial<QueryResponse>): QueryResponse;
    fromPartial(object: DeepPartial<QueryResponse>): QueryResponse;
};
export declare const CheckTxResponse: {
    encode(message: CheckTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CheckTxResponse;
    fromJSON(object: any): CheckTxResponse;
    toJSON(message: CheckTxResponse): unknown;
    create(base?: DeepPartial<CheckTxResponse>): CheckTxResponse;
    fromPartial(object: DeepPartial<CheckTxResponse>): CheckTxResponse;
};
export declare const CommitResponse: {
    encode(message: CommitResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CommitResponse;
    fromJSON(object: any): CommitResponse;
    toJSON(message: CommitResponse): unknown;
    create(base?: DeepPartial<CommitResponse>): CommitResponse;
    fromPartial(object: DeepPartial<CommitResponse>): CommitResponse;
};
export declare const ListSnapshotsResponse: {
    encode(message: ListSnapshotsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ListSnapshotsResponse;
    fromJSON(object: any): ListSnapshotsResponse;
    toJSON(message: ListSnapshotsResponse): unknown;
    create(base?: DeepPartial<ListSnapshotsResponse>): ListSnapshotsResponse;
    fromPartial(object: DeepPartial<ListSnapshotsResponse>): ListSnapshotsResponse;
};
export declare const OfferSnapshotResponse: {
    encode(message: OfferSnapshotResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OfferSnapshotResponse;
    fromJSON(object: any): OfferSnapshotResponse;
    toJSON(message: OfferSnapshotResponse): unknown;
    create(base?: DeepPartial<OfferSnapshotResponse>): OfferSnapshotResponse;
    fromPartial(object: DeepPartial<OfferSnapshotResponse>): OfferSnapshotResponse;
};
export declare const LoadSnapshotChunkResponse: {
    encode(message: LoadSnapshotChunkResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LoadSnapshotChunkResponse;
    fromJSON(object: any): LoadSnapshotChunkResponse;
    toJSON(message: LoadSnapshotChunkResponse): unknown;
    create(base?: DeepPartial<LoadSnapshotChunkResponse>): LoadSnapshotChunkResponse;
    fromPartial(object: DeepPartial<LoadSnapshotChunkResponse>): LoadSnapshotChunkResponse;
};
export declare const ApplySnapshotChunkResponse: {
    encode(message: ApplySnapshotChunkResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ApplySnapshotChunkResponse;
    fromJSON(object: any): ApplySnapshotChunkResponse;
    toJSON(message: ApplySnapshotChunkResponse): unknown;
    create(base?: DeepPartial<ApplySnapshotChunkResponse>): ApplySnapshotChunkResponse;
    fromPartial(object: DeepPartial<ApplySnapshotChunkResponse>): ApplySnapshotChunkResponse;
};
export declare const PrepareProposalResponse: {
    encode(message: PrepareProposalResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareProposalResponse;
    fromJSON(object: any): PrepareProposalResponse;
    toJSON(message: PrepareProposalResponse): unknown;
    create(base?: DeepPartial<PrepareProposalResponse>): PrepareProposalResponse;
    fromPartial(object: DeepPartial<PrepareProposalResponse>): PrepareProposalResponse;
};
export declare const ProcessProposalResponse: {
    encode(message: ProcessProposalResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ProcessProposalResponse;
    fromJSON(object: any): ProcessProposalResponse;
    toJSON(message: ProcessProposalResponse): unknown;
    create(base?: DeepPartial<ProcessProposalResponse>): ProcessProposalResponse;
    fromPartial(object: DeepPartial<ProcessProposalResponse>): ProcessProposalResponse;
};
export declare const ExtendVoteResponse: {
    encode(message: ExtendVoteResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendVoteResponse;
    fromJSON(object: any): ExtendVoteResponse;
    toJSON(message: ExtendVoteResponse): unknown;
    create(base?: DeepPartial<ExtendVoteResponse>): ExtendVoteResponse;
    fromPartial(object: DeepPartial<ExtendVoteResponse>): ExtendVoteResponse;
};
export declare const VerifyVoteExtensionResponse: {
    encode(message: VerifyVoteExtensionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VerifyVoteExtensionResponse;
    fromJSON(object: any): VerifyVoteExtensionResponse;
    toJSON(message: VerifyVoteExtensionResponse): unknown;
    create(base?: DeepPartial<VerifyVoteExtensionResponse>): VerifyVoteExtensionResponse;
    fromPartial(object: DeepPartial<VerifyVoteExtensionResponse>): VerifyVoteExtensionResponse;
};
export declare const FinalizeBlockResponse: {
    encode(message: FinalizeBlockResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FinalizeBlockResponse;
    fromJSON(object: any): FinalizeBlockResponse;
    toJSON(message: FinalizeBlockResponse): unknown;
    create(base?: DeepPartial<FinalizeBlockResponse>): FinalizeBlockResponse;
    fromPartial(object: DeepPartial<FinalizeBlockResponse>): FinalizeBlockResponse;
};
export declare const CommitInfo: {
    encode(message: CommitInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CommitInfo;
    fromJSON(object: any): CommitInfo;
    toJSON(message: CommitInfo): unknown;
    create(base?: DeepPartial<CommitInfo>): CommitInfo;
    fromPartial(object: DeepPartial<CommitInfo>): CommitInfo;
};
export declare const ExtendedCommitInfo: {
    encode(message: ExtendedCommitInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendedCommitInfo;
    fromJSON(object: any): ExtendedCommitInfo;
    toJSON(message: ExtendedCommitInfo): unknown;
    create(base?: DeepPartial<ExtendedCommitInfo>): ExtendedCommitInfo;
    fromPartial(object: DeepPartial<ExtendedCommitInfo>): ExtendedCommitInfo;
};
export declare const Event: {
    encode(message: Event, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Event;
    fromJSON(object: any): Event;
    toJSON(message: Event): unknown;
    create(base?: DeepPartial<Event>): Event;
    fromPartial(object: DeepPartial<Event>): Event;
};
export declare const EventAttribute: {
    encode(message: EventAttribute, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAttribute;
    fromJSON(object: any): EventAttribute;
    toJSON(message: EventAttribute): unknown;
    create(base?: DeepPartial<EventAttribute>): EventAttribute;
    fromPartial(object: DeepPartial<EventAttribute>): EventAttribute;
};
export declare const ExecTxResult: {
    encode(message: ExecTxResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExecTxResult;
    fromJSON(object: any): ExecTxResult;
    toJSON(message: ExecTxResult): unknown;
    create(base?: DeepPartial<ExecTxResult>): ExecTxResult;
    fromPartial(object: DeepPartial<ExecTxResult>): ExecTxResult;
};
export declare const TxResult: {
    encode(message: TxResult, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TxResult;
    fromJSON(object: any): TxResult;
    toJSON(message: TxResult): unknown;
    create(base?: DeepPartial<TxResult>): TxResult;
    fromPartial(object: DeepPartial<TxResult>): TxResult;
};
export declare const Validator: {
    encode(message: Validator, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Validator;
    fromJSON(object: any): Validator;
    toJSON(message: Validator): unknown;
    create(base?: DeepPartial<Validator>): Validator;
    fromPartial(object: DeepPartial<Validator>): Validator;
};
export declare const ValidatorUpdate: {
    encode(message: ValidatorUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ValidatorUpdate;
    fromJSON(object: any): ValidatorUpdate;
    toJSON(message: ValidatorUpdate): unknown;
    create(base?: DeepPartial<ValidatorUpdate>): ValidatorUpdate;
    fromPartial(object: DeepPartial<ValidatorUpdate>): ValidatorUpdate;
};
export declare const VoteInfo: {
    encode(message: VoteInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): VoteInfo;
    fromJSON(object: any): VoteInfo;
    toJSON(message: VoteInfo): unknown;
    create(base?: DeepPartial<VoteInfo>): VoteInfo;
    fromPartial(object: DeepPartial<VoteInfo>): VoteInfo;
};
export declare const ExtendedVoteInfo: {
    encode(message: ExtendedVoteInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExtendedVoteInfo;
    fromJSON(object: any): ExtendedVoteInfo;
    toJSON(message: ExtendedVoteInfo): unknown;
    create(base?: DeepPartial<ExtendedVoteInfo>): ExtendedVoteInfo;
    fromPartial(object: DeepPartial<ExtendedVoteInfo>): ExtendedVoteInfo;
};
export declare const Misbehavior: {
    encode(message: Misbehavior, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Misbehavior;
    fromJSON(object: any): Misbehavior;
    toJSON(message: Misbehavior): unknown;
    create(base?: DeepPartial<Misbehavior>): Misbehavior;
    fromPartial(object: DeepPartial<Misbehavior>): Misbehavior;
};
export declare const Snapshot: {
    encode(message: Snapshot, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Snapshot;
    fromJSON(object: any): Snapshot;
    toJSON(message: Snapshot): unknown;
    create(base?: DeepPartial<Snapshot>): Snapshot;
    fromPartial(object: DeepPartial<Snapshot>): Snapshot;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
