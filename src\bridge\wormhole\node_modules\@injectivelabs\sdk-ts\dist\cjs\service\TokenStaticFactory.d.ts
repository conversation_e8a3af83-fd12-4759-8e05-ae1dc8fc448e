import { TokenSource, TokenStatic, TokenVerification } from './../types/index.js';
export declare class TokenStaticFactory {
    registry: TokenStatic[];
    denomVerifiedMap: Record<string, TokenStatic>;
    denomBlacklistedMap: Record<string, TokenStatic>;
    denomUnverifiedMap: Record<string, TokenStatic>;
    cw20AddressVerifiedMap: Record<string, TokenStatic>;
    cw20AddressUnverifiedMap: Record<string, TokenStatic>;
    factoryTokenDenomVerifiedMap: Record<string, TokenStatic>;
    factoryTokenDenomUnverifiedMap: Record<string, TokenStatic>;
    ibcDenomsVerifiedMap: Record<string, TokenStatic>;
    ibcDenomsUnverifiedMap: Record<string, TokenStatic>;
    ibcBaseDenomsVerifiedMap: Record<string, TokenStatic>;
    ibcBaseDenomsUnverifiedMap: Record<string, TokenStatic>;
    symbolTokensMap: Record<string, TokenStatic>;
    insuranceTokensMap: Record<string, TokenStatic>;
    constructor(registry: TokenStatic[]);
    mapRegistry(registry: TokenStatic[]): void;
    getSymbolToken(symbol: string): TokenStatic | undefined;
    getInsuranceToken(symbol: string): TokenStatic | undefined;
    getIbcToken(denom: string, { source, tokenVerification, }?: {
        source?: TokenSource;
        tokenVerification?: TokenVerification;
    }): TokenStatic | undefined;
    getCw20Token(address: string, { tokenVerification }?: {
        tokenVerification?: TokenVerification;
    }): TokenStatic | undefined;
    getTokenFactoryToken(denom: string, { tokenVerification }?: {
        tokenVerification?: TokenVerification;
    }): TokenStatic | undefined;
    toToken(denomOrSymbol: string, { source, verification, }?: {
        source?: TokenSource;
        verification?: TokenVerification;
    }): TokenStatic | undefined;
}
