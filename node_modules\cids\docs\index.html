<!doctype html>
<html lang="en">
<head>
  <meta charset='utf-8'>
  <title>cids 0.7.5 | Documentation</title>
  <meta name='description' content='CID Implementation in JavaScript'>
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <link href='assets/bass.css' rel='stylesheet'>
  <link href='assets/style.css' rel='stylesheet'>
  <link href='assets/github.css' rel='stylesheet'>
  <link href='assets/split.css' rel='stylesheet'>
</head>
<body class='documentation m0'>
    <div class='flex'>
      <div id='split-left' class='overflow-auto fs0 height-viewport-100'>
        <div class='py1 px2'>
          <h3 class='mb0 no-anchor'>cids</h3>
          <div class='mb1'><code>0.7.5</code></div>
          <input
            placeholder='Filter'
            id='filter-input'
            class='col12 block input'
            type='text' />
          <div id='toc'>
            <ul class='list-reset h5 py1-ul'>
              
                
                <li><a
                  href='#intro'
                  class="h5 bold black caps">
                  Intro
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#serializedcid'
                  class="">
                  SerializedCID
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#buffer'
                  class="">
                  buffer
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#prefix'
                  class="">
                  prefix
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tov0'
                  class="">
                  toV0
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tov1'
                  class="">
                  toV1
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tobaseencodedstring'
                  class="">
                  toBaseEncodedString
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tostring'
                  class="">
                  toString
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#tojson'
                  class="">
                  toJSON
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#equals'
                  class="">
                  equals
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#validatecid'
                  class="">
                  validateCID
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#cid'
                  class=" toggle-sibling">
                  CID
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#cidiscid'
                        class='regular pre-open'>
                        .isCID
                      </a></li>
                    
                    </ul>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#cidversion'
                        class='regular pre-open'>
                        #version
                      </a></li>
                      
                      <li><a
                        href='#cidcodec'
                        class='regular pre-open'>
                        #codec
                      </a></li>
                      
                      <li><a
                        href='#cidmultihash'
                        class='regular pre-open'>
                        #multihash
                      </a></li>
                      
                      <li><a
                        href='#cidmultibasename'
                        class='regular pre-open'>
                        #multibaseName
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#checkcidcomponents'
                  class="">
                  checkCIDComponents
                  
                </a>
                
                </li>
              
            </ul>
          </div>
          <div class='mt1 h6 quiet'>
            <a href='https://documentation.js.org/reading-documentation.html'>Need help reading this?</a>
          </div>
        </div>
      </div>
      <div id='split-right' class='relative overflow-auto height-viewport-100'>
        
          
            <div class='keyline-top-not py2'><section class='py2 clearfix'>

  <h2 id='intro' class='mt0'>
    Intro
  </h2>

  
    <p>Installable via  <code>npm install --save cids</code>, it can also be used directly in the browser.</p>
<h2>Download</h2>
<p>The source is available for download from <a href="git://github.com/multiformats/js-cid.git">GitHub</a>. Alternatively, you can install using npm:</p>
<pre class='hljs'>$ npm install --save cids</pre>
<p>You can then <code>require()</code> cids as normal:</p>
<pre class='hljs'><span class="hljs-keyword">const</span> cids = <span class="hljs-built_in">require</span>(<span class="hljs-string">'cids'</span>)</pre>
<h2>In the Browser</h2>
<p>Cids should work in any ES2015 environment out of the box.</p>
<p>Usage:</p>
<pre class='hljs'><span class="hljs-tag">&lt;<span class="hljs-name">script</span> <span class="hljs-attr">type</span>=<span class="hljs-string">"text/javascript"</span> <span class="hljs-attr">src</span>=<span class="hljs-string">"index.js"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">script</span>&gt;</span></pre>
<p>The portable versions of cids, including <code>index.js</code> and <code>index.min.js</code>, are included in the <code>/dist</code> folder. Cids can also be found on <a href="https://unpkg.com">unpkg.com</a> under</p>
<ul>
<li><a href="https://unpkg.com/cids/dist/index.min.js">https://unpkg.com/cids/dist/index.min.js</a></li>
<li><a href="https://unpkg.com/cids/dist/index.js">https://unpkg.com/cids/dist/index.js</a></li>
</ul>

  
</section></div>
          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='serializedcid'>
      SerializedCID
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L11-L16'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  
    <div class='pre p1 fill-light mt0'>SerializedCID</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>
    </p>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>codec</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>version</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>multihash</span> <code class='quiet'>(<a href="https://nodejs.org/api/buffer.html">Buffer</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='buffer'>
      buffer
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L151-L172'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>The CID as a <code>Buffer</code></p>

    <div class='pre p1 fill-light mt0'>buffer</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://nodejs.org/api/buffer.html">Buffer</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='prefix'>
      prefix
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L180-L186'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Get the prefix of the CID.</p>

    <div class='pre p1 fill-light mt0'>prefix</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://nodejs.org/api/buffer.html">Buffer</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tov0'>
      toV0
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L193-L209'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Convert to a CID of version <code>0</code>.</p>

    <div class='pre p1 fill-light mt0'>toV0(): <a href="#cid">CID</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#cid">CID</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tov1'>
      toV1
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L216-L218'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Convert to a CID of version <code>1</code>.</p>

    <div class='pre p1 fill-light mt0'>toV1(): <a href="#cid">CID</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#cid">CID</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tobaseencodedstring'>
      toBaseEncodedString
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L226-L246'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Encode the CID into a string.</p>

    <div class='pre p1 fill-light mt0'>toBaseEncodedString(base: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>this.multibaseName</code>)</code>
	    Base encoding to use.

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tostring'>
      toString
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L253-L255'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>CID(QmdfTbBqBPQ7VNxZEYEj14VmRuZBkqFbiwReogJgS1zR1n)</p>

    <div class='pre p1 fill-light mt0'>toString(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">String</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">String</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tojson'>
      toJSON
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L266-L272'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Serialize to a plain object.</p>

    <div class='pre p1 fill-light mt0'>toJSON(): <a href="#serializedcid">SerializedCID</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#serializedcid">SerializedCID</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='equals'>
      equals
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L280-L284'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Compare equality with another CID.</p>

    <div class='pre p1 fill-light mt0'>equals(other: <a href="#cid">CID</a>): bool</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>other</span> <code class='quiet'>(<a href="#cid">CID</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>bool</code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='validatecid'>
      validateCID
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L293-L298'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Test if the given input is a valid CID object.
Throws if it is not.</p>

    <div class='pre p1 fill-light mt0'>validateCID(other: any): void</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>other</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>void</code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='cid'>
      CID
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L32-L299'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Class representing a CID <code>&#x3C;mbase>&#x3C;version>&#x3C;mcodec>&#x3C;mhash></code>
, as defined in <a href="https://github.com/multiformats/cid">ipld/cid</a>.</p>

    <div class='pre p1 fill-light mt0'>new CID()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='cidiscid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isCID(other)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L32-L299'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  <p>Test if the given input is a CID.</p>

    <div class='pre p1 fill-light mt0'>isCID(other: any): bool</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>other</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>bool</code>:
        

      
    
  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='cidversion'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>version</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L123-L123'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  
    <div class='pre p1 fill-light mt0'>version</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='cidcodec'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>codec</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L128-L128'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  
    <div class='pre p1 fill-light mt0'>codec</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='cidmultihash'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>multihash</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L133-L133'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  
    <div class='pre p1 fill-light mt0'>multihash</div>
  
    <p>
      Type:
      <a href="https://nodejs.org/api/buffer.html">Buffer</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='cidmultibasename'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>multibaseName</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/index.js#L138-L138'>
      <span>src/index.js</span>
      </a>
    
  </div>
  

  
    <div class='pre p1 fill-light mt0'>multibaseName</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
          
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='checkcidcomponents'>
      checkCIDComponents
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='https://github.com/multiformats/js-cid/blob/dce38fb59042863daca6fc41a8a86eeeb401e461/src/cid-util.js#L14-L49'>
      <span>src/cid-util.js</span>
      </a>
    
  </div>
  

  <p>Test if the given input is a valid CID object.
Returns an error message if it is not.
Returns undefined if it is a valid CID.</p>

    <div class='pre p1 fill-light mt0'>checkCIDComponents(other: any): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>other</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  

  
</section>

          
        
      </div>
    </div>
  <script src='assets/anchor.js'></script>
  <script src='assets/split.js'></script>
  <script src='assets/site.js'></script>
</body>
</html>
