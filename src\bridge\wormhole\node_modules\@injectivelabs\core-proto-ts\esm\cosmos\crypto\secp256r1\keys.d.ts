import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cosmos.crypto.secp256r1";
/** Since: cosmos-sdk 0.43 */
/** PubKey defines a secp256r1 ECDSA public key. */
export interface PubKey {
    /**
     * Point on secp256r1 curve in a compressed representation as specified in section
     * 4.3.6 of ANSI X9.62: https://webstore.ansi.org/standards/ascx9/ansix9621998
     */
    key: Uint8Array;
}
/** Priv<PERSON>ey defines a secp256r1 ECDSA private key. */
export interface PrivKey {
    /** secret number serialized using big-endian encoding */
    secret: Uint8Array;
}
export declare const PubKey: {
    encode(message: <PERSON><PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PubKey;
    fromJSON(object: any): <PERSON><PERSON><PERSON>;
    toJSON(message: <PERSON><PERSON><PERSON>): unknown;
    create(base?: DeepPartial<PubKey>): Pub<PERSON><PERSON>;
    fromPartial(object: DeepPartial<PubKey>): Pub<PERSON><PERSON>;
};
export declare const PrivKey: {
    encode(message: Priv<PERSON><PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrivKey;
    fromJSON(object: any): PrivKey;
    toJSON(message: PrivKey): unknown;
    create(base?: DeepPartial<PrivKey>): PrivKey;
    fromPartial(object: DeepPartial<PrivKey>): PrivKey;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
