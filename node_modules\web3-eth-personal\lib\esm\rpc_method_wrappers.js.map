{"version": 3, "file": "rpc_method_wrappers.js", "sourceRoot": "", "sources": ["../../src/rpc_method_wrappers.ts"], "names": [], "mappings": ";;;;;;;;;AAiBA,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,UAAU,CAAC;AAC7C,OAAO,EAA2B,eAAe,EAA0B,MAAM,YAAY,CAAC;AAC9F,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAEtD,MAAM,CAAC,MAAM,WAAW,GAAG,CAAO,cAAkD,EAAE,EAAE;IACvF,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;IAEpE,OAAO,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACtC,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAG,CACzB,cAAkD,EAClD,QAAgB,EACf,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE3C,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAE7E,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG,CAC5B,cAAkD,EAClD,OAAgB,EAChB,QAAgB,EAChB,cAAsB,EACrB,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;IAEvF,OAAO,kBAAkB,CAAC,aAAa,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC5F,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAG,CAC1B,cAAkD,EAClD,OAAgB,EACf,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAE3C,OAAO,kBAAkB,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAC3B,cAAkD,EAClD,OAAkB,EAClB,UAAkB,EACjB,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;IAEhE,OAAO,kBAAkB,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7E,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAC9B,cAAkD,EAClD,EAAe,EACf,UAAkB,EAClB,MAA0B,EACzB,EAAE;IACH,MAAM,WAAW,GAAG,iBAAiB,CAAC,EAAE,EAAE,eAAe,EAAE;QAC1D,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,uBAAuB;KAClD,CAAC,CAAC;IAEH,OAAO,kBAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACpF,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAC9B,cAAkD,EAClD,EAAe,EACf,UAAkB,EAClB,MAA0B,EACzB,EAAE;IACH,MAAM,WAAW,GAAG,iBAAiB,CAAC,EAAE,EAAE,eAAe,EAAE;QAC1D,iBAAiB,EAAE,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,uBAAuB;KAClD,CAAC,CAAC;IAEH,OAAO,kBAAkB,CAAC,eAAe,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACpF,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAG,CACnB,cAAkD,EAClD,IAAe,EACf,OAAgB,EAChB,UAAkB,EACjB,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;IAEjF,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAE9D,OAAO,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AACjF,CAAC,CAAA,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAG,CACxB,cAAkD,EAClD,UAAqB,EACrB,SAAiB,EAChB,EAAE;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;IAElE,MAAM,gBAAgB,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAEtF,OAAO,kBAAkB,CAAC,SAAS,CAAC,cAAc,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAClF,CAAC,CAAA,CAAC"}