import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cometbft.services.version.v1";
/** GetVersionRequest is the request for the ABCI version. */
export interface GetVersionRequest {
}
/** GetVersionResponse contains the ABCI application version info. */
export interface GetVersionResponse {
    /** The semantic version of the node software. */
    node: string;
    /** The version of ABCI used by the node. */
    abci: string;
    /** The version of the P2P protocol. */
    p2p: string;
    /** The version of the block protocol. */
    block: string;
}
export declare const GetVersionRequest: {
    encode(_: GetVersionRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetVersionRequest;
    fromJSON(_: any): GetVersionRequest;
    toJSON(_: GetVersionRequest): unknown;
    create(base?: DeepPartial<GetVersionRequest>): GetVersionRequest;
    fromPartial(_: DeepPartial<GetVersionRequest>): GetVersionRequest;
};
export declare const GetVersionResponse: {
    encode(message: GetVersionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetVersionResponse;
    fromJSON(object: any): GetVersionResponse;
    toJSON(message: GetVersionResponse): unknown;
    create(base?: DeepPartial<GetVersionResponse>): GetVersionResponse;
    fromPartial(object: DeepPartial<GetVersionResponse>): GetVersionResponse;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
