import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_exchange_rpc";
export interface GetTxRequest {
    /** Transaction hash in hex without 0x prefix (Cosmos-like). */
    hash: string;
}
export interface GetTxResponse {
    /** Hex-encoded Tendermint transaction hash */
    txHash: string;
    /** The block height */
    height: string;
    /** Tx index in the block */
    index: number;
    /** Namespace for the resp code */
    codespace: string;
    /** Response code */
    code: number;
    /** Result bytes, if any */
    data: Uint8Array;
    /**
     * The output of the application's logger (raw string). May be
     * non-deterministic.
     */
    rawLog: string;
    /** Time of the previous block. */
    timestamp: string;
}
export interface PrepareTxRequest {
    /** Specify chainID for the target tx */
    chainId: string;
    /** Specify Ethereum address of a signer */
    signerAddress: string;
    /** Deprecated: this field is ignored */
    sequence: string;
    /** Textual memo information to attach with tx */
    memo: string;
    /** Block height until which the transaction is valid. */
    timeoutHeight: string;
    /** Transaction fee details. */
    fee: CosmosTxFee | undefined;
    /** List of Cosmos proto3-encoded Msgs to include in a single tx */
    msgs: Uint8Array[];
    /** The wrapper of the EIP712 message, 'v1'/'v2' or 'V1'/'V2' */
    eip712Wrapper: string;
}
export interface CosmosTxFee {
    /** Transaction gas price */
    price: CosmosCoin[];
    /** Transaction gas limit */
    gas: string;
    /**
     * Explicitly require fee delegation when set to true. Or don't care = false.
     * Will be replaced by other flag in the next version.
     */
    delegateFee: boolean;
}
export interface CosmosCoin {
    /** Coin denominator */
    denom: string;
    /** Coin amount (big int) */
    amount: string;
}
export interface PrepareTxResponse {
    /** EIP712-compatible message suitable for signing with eth_signTypedData_v4 */
    data: string;
    /** Account tx sequence (nonce) */
    sequence: string;
    /** Sign mode for the resulting tx */
    signMode: string;
    /** Specify proto-URL of a public key, which defines the signature format */
    pubKeyType: string;
    /** Fee payer address provided by service */
    feePayer: string;
    /** Hex-encoded ethsecp256k1 signature bytes from fee payer */
    feePayerSig: string;
}
export interface PrepareEip712Request {
    /** Specify chainID for the target tx */
    chainId: string;
    /** Specify Ethereum address of a signer */
    signerAddress: string;
    /** Sequence number of the transaction signer */
    sequence: string;
    /** Account number of the transaction signer */
    accountNumber: string;
    /** Textual memo information to attach with tx */
    memo: string;
    /** Block height until which the transaction is valid. */
    timeoutHeight: string;
    /** Transaction fee details. */
    fee: CosmosTxFee | undefined;
    /** List of Cosmos proto3-encoded Msgs to include in a single tx */
    msgs: Uint8Array[];
    /** The wrapper of the EIP712 message, 'v1'/'v2' or 'V1'/'V2' */
    eip712Wrapper: string;
}
export interface PrepareEip712Response {
    /** EIP712-compatible message suitable for signing with eth_signTypedData_v4 */
    data: string;
}
export interface BroadcastTxRequest {
    /** Specify Web3 chainID (from prepateTx) for the target Tx */
    chainId: string;
    /** Amino-encoded Tx JSON data (except Msgs) */
    tx: Uint8Array;
    /** List of Cosmos proto3-encoded Msgs from tx */
    msgs: Uint8Array[];
    /** Specify ethsecp256k1 pubkey of the signer */
    pubKey: CosmosPubKey | undefined;
    /** Hex-encoded ethsecp256k1 signature bytes */
    signature: string;
    /** Fee payer address provided by service */
    feePayer: string;
    /** Hex-encoded ethsecp256k1 signature bytes from fee payer */
    feePayerSig: string;
    /** Broadcast mode */
    mode: string;
}
export interface CosmosPubKey {
    /** Pubkey type URL */
    type: string;
    /** Hex-encoded string of the public key */
    key: string;
}
export interface BroadcastTxResponse {
    /** Hex-encoded Tendermint transaction hash */
    txHash: string;
    /** The block height */
    height: string;
    /** Tx index in the block */
    index: number;
    /** Namespace for the resp code */
    codespace: string;
    /** Response code */
    code: number;
    /** Result bytes, if any */
    data: Uint8Array;
    /**
     * The output of the application's logger (raw string). May be
     * non-deterministic.
     */
    rawLog: string;
    /** Time of the previous block. */
    timestamp: string;
}
export interface PrepareCosmosTxRequest {
    /** Specify chainID for the target tx */
    chainId: string;
    /** sender address provided */
    senderAddress: string;
    /** Textual memo information to attach with tx */
    memo: string;
    /** Block height until which the transaction is valid. */
    timeoutHeight: string;
    /** Transaction fee details. */
    fee: CosmosTxFee | undefined;
    /** List of Cosmos proto3-encoded Msgs to include in a single tx */
    msgs: Uint8Array[];
}
export interface PrepareCosmosTxResponse {
    /** proto encoded tx */
    tx: Uint8Array;
    /** Sign mode for the resulting tx */
    signMode: string;
    /** Specify proto-URL of a public key, which defines the signature format */
    pubKeyType: string;
    /** Fee payer address provided by service */
    feePayer: string;
    /** Hex-encoded ethsecp256k1 signature bytes from fee payer */
    feePayerSig: string;
    /** ethsecp256k1 feePayer pubkey */
    feePayerPubKey: CosmosPubKey | undefined;
}
export interface BroadcastCosmosTxRequest {
    /** proto encoded tx */
    tx: Uint8Array;
    /** Specify ethsecp256k1 sender pubkey */
    pubKey: CosmosPubKey | undefined;
    /** Hex-encoded ethsecp256k1 sender signature bytes */
    signature: string;
    /** sender address */
    senderAddress: string;
}
export interface BroadcastCosmosTxResponse {
    /** Hex-encoded Tendermint transaction hash */
    txHash: string;
    /** The block height */
    height: string;
    /** Tx index in the block */
    index: number;
    /** Namespace for the resp code */
    codespace: string;
    /** Response code */
    code: number;
    /** Result bytes, if any */
    data: Uint8Array;
    /**
     * The output of the application's logger (raw string). May be
     * non-deterministic.
     */
    rawLog: string;
    /** Time of the previous block. */
    timestamp: string;
}
export interface GetFeePayerRequest {
}
export interface GetFeePayerResponse {
    /** Fee payer address provided by service */
    feePayer: string;
    /** ethsecp256k1 feePayer pubkey */
    feePayerPubKey: CosmosPubKey | undefined;
}
export declare const GetTxRequest: {
    encode(message: GetTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTxRequest;
    fromJSON(object: any): GetTxRequest;
    toJSON(message: GetTxRequest): unknown;
    create(base?: DeepPartial<GetTxRequest>): GetTxRequest;
    fromPartial(object: DeepPartial<GetTxRequest>): GetTxRequest;
};
export declare const GetTxResponse: {
    encode(message: GetTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetTxResponse;
    fromJSON(object: any): GetTxResponse;
    toJSON(message: GetTxResponse): unknown;
    create(base?: DeepPartial<GetTxResponse>): GetTxResponse;
    fromPartial(object: DeepPartial<GetTxResponse>): GetTxResponse;
};
export declare const PrepareTxRequest: {
    encode(message: PrepareTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareTxRequest;
    fromJSON(object: any): PrepareTxRequest;
    toJSON(message: PrepareTxRequest): unknown;
    create(base?: DeepPartial<PrepareTxRequest>): PrepareTxRequest;
    fromPartial(object: DeepPartial<PrepareTxRequest>): PrepareTxRequest;
};
export declare const CosmosTxFee: {
    encode(message: CosmosTxFee, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CosmosTxFee;
    fromJSON(object: any): CosmosTxFee;
    toJSON(message: CosmosTxFee): unknown;
    create(base?: DeepPartial<CosmosTxFee>): CosmosTxFee;
    fromPartial(object: DeepPartial<CosmosTxFee>): CosmosTxFee;
};
export declare const CosmosCoin: {
    encode(message: CosmosCoin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CosmosCoin;
    fromJSON(object: any): CosmosCoin;
    toJSON(message: CosmosCoin): unknown;
    create(base?: DeepPartial<CosmosCoin>): CosmosCoin;
    fromPartial(object: DeepPartial<CosmosCoin>): CosmosCoin;
};
export declare const PrepareTxResponse: {
    encode(message: PrepareTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareTxResponse;
    fromJSON(object: any): PrepareTxResponse;
    toJSON(message: PrepareTxResponse): unknown;
    create(base?: DeepPartial<PrepareTxResponse>): PrepareTxResponse;
    fromPartial(object: DeepPartial<PrepareTxResponse>): PrepareTxResponse;
};
export declare const PrepareEip712Request: {
    encode(message: PrepareEip712Request, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareEip712Request;
    fromJSON(object: any): PrepareEip712Request;
    toJSON(message: PrepareEip712Request): unknown;
    create(base?: DeepPartial<PrepareEip712Request>): PrepareEip712Request;
    fromPartial(object: DeepPartial<PrepareEip712Request>): PrepareEip712Request;
};
export declare const PrepareEip712Response: {
    encode(message: PrepareEip712Response, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareEip712Response;
    fromJSON(object: any): PrepareEip712Response;
    toJSON(message: PrepareEip712Response): unknown;
    create(base?: DeepPartial<PrepareEip712Response>): PrepareEip712Response;
    fromPartial(object: DeepPartial<PrepareEip712Response>): PrepareEip712Response;
};
export declare const BroadcastTxRequest: {
    encode(message: BroadcastTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BroadcastTxRequest;
    fromJSON(object: any): BroadcastTxRequest;
    toJSON(message: BroadcastTxRequest): unknown;
    create(base?: DeepPartial<BroadcastTxRequest>): BroadcastTxRequest;
    fromPartial(object: DeepPartial<BroadcastTxRequest>): BroadcastTxRequest;
};
export declare const CosmosPubKey: {
    encode(message: CosmosPubKey, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): CosmosPubKey;
    fromJSON(object: any): CosmosPubKey;
    toJSON(message: CosmosPubKey): unknown;
    create(base?: DeepPartial<CosmosPubKey>): CosmosPubKey;
    fromPartial(object: DeepPartial<CosmosPubKey>): CosmosPubKey;
};
export declare const BroadcastTxResponse: {
    encode(message: BroadcastTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BroadcastTxResponse;
    fromJSON(object: any): BroadcastTxResponse;
    toJSON(message: BroadcastTxResponse): unknown;
    create(base?: DeepPartial<BroadcastTxResponse>): BroadcastTxResponse;
    fromPartial(object: DeepPartial<BroadcastTxResponse>): BroadcastTxResponse;
};
export declare const PrepareCosmosTxRequest: {
    encode(message: PrepareCosmosTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareCosmosTxRequest;
    fromJSON(object: any): PrepareCosmosTxRequest;
    toJSON(message: PrepareCosmosTxRequest): unknown;
    create(base?: DeepPartial<PrepareCosmosTxRequest>): PrepareCosmosTxRequest;
    fromPartial(object: DeepPartial<PrepareCosmosTxRequest>): PrepareCosmosTxRequest;
};
export declare const PrepareCosmosTxResponse: {
    encode(message: PrepareCosmosTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PrepareCosmosTxResponse;
    fromJSON(object: any): PrepareCosmosTxResponse;
    toJSON(message: PrepareCosmosTxResponse): unknown;
    create(base?: DeepPartial<PrepareCosmosTxResponse>): PrepareCosmosTxResponse;
    fromPartial(object: DeepPartial<PrepareCosmosTxResponse>): PrepareCosmosTxResponse;
};
export declare const BroadcastCosmosTxRequest: {
    encode(message: BroadcastCosmosTxRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BroadcastCosmosTxRequest;
    fromJSON(object: any): BroadcastCosmosTxRequest;
    toJSON(message: BroadcastCosmosTxRequest): unknown;
    create(base?: DeepPartial<BroadcastCosmosTxRequest>): BroadcastCosmosTxRequest;
    fromPartial(object: DeepPartial<BroadcastCosmosTxRequest>): BroadcastCosmosTxRequest;
};
export declare const BroadcastCosmosTxResponse: {
    encode(message: BroadcastCosmosTxResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BroadcastCosmosTxResponse;
    fromJSON(object: any): BroadcastCosmosTxResponse;
    toJSON(message: BroadcastCosmosTxResponse): unknown;
    create(base?: DeepPartial<BroadcastCosmosTxResponse>): BroadcastCosmosTxResponse;
    fromPartial(object: DeepPartial<BroadcastCosmosTxResponse>): BroadcastCosmosTxResponse;
};
export declare const GetFeePayerRequest: {
    encode(_: GetFeePayerRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetFeePayerRequest;
    fromJSON(_: any): GetFeePayerRequest;
    toJSON(_: GetFeePayerRequest): unknown;
    create(base?: DeepPartial<GetFeePayerRequest>): GetFeePayerRequest;
    fromPartial(_: DeepPartial<GetFeePayerRequest>): GetFeePayerRequest;
};
export declare const GetFeePayerResponse: {
    encode(message: GetFeePayerResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetFeePayerResponse;
    fromJSON(object: any): GetFeePayerResponse;
    toJSON(message: GetFeePayerResponse): unknown;
    create(base?: DeepPartial<GetFeePayerResponse>): GetFeePayerResponse;
    fromPartial(object: DeepPartial<GetFeePayerResponse>): GetFeePayerResponse;
};
/** InjectiveExchangeRPC defines gRPC API of an Injective Exchange service. */
export interface InjectiveExchangeRPC {
    /** GetTx gets transaction details by hash. */
    GetTx(request: DeepPartial<GetTxRequest>, metadata?: grpc.Metadata): Promise<GetTxResponse>;
    /** PrepareTx generates a Web3-signable body for a Cosmos transaction */
    PrepareTx(request: DeepPartial<PrepareTxRequest>, metadata?: grpc.Metadata): Promise<PrepareTxResponse>;
    /** prepareEip712 generates EIP712 for an Injective Message */
    PrepareEip712(request: DeepPartial<PrepareEip712Request>, metadata?: grpc.Metadata): Promise<PrepareEip712Response>;
    /** BroadcastTx broadcasts a signed Web3 transaction */
    BroadcastTx(request: DeepPartial<BroadcastTxRequest>, metadata?: grpc.Metadata): Promise<BroadcastTxResponse>;
    /** PrepareCosmosTx generates a Web3-signable body for a Cosmos transaction */
    PrepareCosmosTx(request: DeepPartial<PrepareCosmosTxRequest>, metadata?: grpc.Metadata): Promise<PrepareCosmosTxResponse>;
    /** BroadcastCosmosTx broadcasts a signed Web3 transaction */
    BroadcastCosmosTx(request: DeepPartial<BroadcastCosmosTxRequest>, metadata?: grpc.Metadata): Promise<BroadcastCosmosTxResponse>;
    /** Return fee payer information's */
    GetFeePayer(request: DeepPartial<GetFeePayerRequest>, metadata?: grpc.Metadata): Promise<GetFeePayerResponse>;
}
export declare class InjectiveExchangeRPCClientImpl implements InjectiveExchangeRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetTx(request: DeepPartial<GetTxRequest>, metadata?: grpc.Metadata): Promise<GetTxResponse>;
    PrepareTx(request: DeepPartial<PrepareTxRequest>, metadata?: grpc.Metadata): Promise<PrepareTxResponse>;
    PrepareEip712(request: DeepPartial<PrepareEip712Request>, metadata?: grpc.Metadata): Promise<PrepareEip712Response>;
    BroadcastTx(request: DeepPartial<BroadcastTxRequest>, metadata?: grpc.Metadata): Promise<BroadcastTxResponse>;
    PrepareCosmosTx(request: DeepPartial<PrepareCosmosTxRequest>, metadata?: grpc.Metadata): Promise<PrepareCosmosTxResponse>;
    BroadcastCosmosTx(request: DeepPartial<BroadcastCosmosTxRequest>, metadata?: grpc.Metadata): Promise<BroadcastCosmosTxResponse>;
    GetFeePayer(request: DeepPartial<GetFeePayerRequest>, metadata?: grpc.Metadata): Promise<GetFeePayerResponse>;
}
export declare const InjectiveExchangeRPCDesc: {
    serviceName: string;
};
export declare const InjectiveExchangeRPCGetTxDesc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCPrepareTxDesc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCPrepareEip712Desc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCBroadcastTxDesc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCPrepareCosmosTxDesc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCBroadcastCosmosTxDesc: UnaryMethodDefinitionish;
export declare const InjectiveExchangeRPCGetFeePayerDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
