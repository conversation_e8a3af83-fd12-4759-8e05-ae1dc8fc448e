import _m0 from "protobufjs/minimal.js";
import { PublicKey } from "../../crypto/v1/keys";
import { Proposal, Vote } from "../../types/v1beta1/types";
export declare const protobufPackage = "cometbft.privval.v1beta1";
/** Errors is a list of error codes that can be returned by the remote signer. */
export declare enum Errors {
    /** ERRORS_UNKNOWN - Unknown error */
    ERRORS_UNKNOWN = 0,
    /** ERRORS_UNEXPECTED_RESPONSE - Unexpected response */
    ERRORS_UNEXPECTED_RESPONSE = 1,
    /** ERRORS_NO_CONNECTION - Connection lost */
    ERRORS_NO_CONNECTION = 2,
    /** ERRORS_CONNECTION_TIMEOUT - Connection timeout */
    ERRORS_CONNECTION_TIMEOUT = 3,
    /** ERRORS_READ_TIMEOUT - Read timeout */
    ERRORS_READ_TIMEOUT = 4,
    /** ERRORS_WRITE_TIMEOUT - Write timeout */
    ERRORS_WRITE_TIMEOUT = 5,
    UNRECOGNIZED = -1
}
export declare function errorsFromJSON(object: any): Errors;
export declare function errorsToJSON(object: Errors): string;
/** A service for broadcasting transactions. */
export interface RemoteSignerError {
    code: number;
    description: string;
}
/** PubKeyRequest requests the consensus public key from the remote signer. */
export interface PubKeyRequest {
    chainId: string;
}
/** PubKeyResponse is a response message containing the public key. */
export interface PubKeyResponse {
    pubKey: PublicKey | undefined;
    error: RemoteSignerError | undefined;
}
/** SignVoteRequest is a request to sign a vote */
export interface SignVoteRequest {
    vote: Vote | undefined;
    chainId: string;
}
/** SignedVoteResponse is a response containing a signed vote or an error */
export interface SignedVoteResponse {
    vote: Vote | undefined;
    error: RemoteSignerError | undefined;
}
/** SignProposalRequest is a request to sign a proposal */
export interface SignProposalRequest {
    proposal: Proposal | undefined;
    chainId: string;
}
/** SignedProposalResponse is response containing a signed proposal or an error */
export interface SignedProposalResponse {
    proposal: Proposal | undefined;
    error: RemoteSignerError | undefined;
}
/** PingRequest is a request to confirm that the connection is alive. */
export interface PingRequest {
}
/** PingResponse is a response to confirm that the connection is alive. */
export interface PingResponse {
}
/** Message is an abstract message to/from the remote signer. */
export interface Message {
    pubKeyRequest?: PubKeyRequest | undefined;
    pubKeyResponse?: PubKeyResponse | undefined;
    signVoteRequest?: SignVoteRequest | undefined;
    signedVoteResponse?: SignedVoteResponse | undefined;
    signProposalRequest?: SignProposalRequest | undefined;
    signedProposalResponse?: SignedProposalResponse | undefined;
    pingRequest?: PingRequest | undefined;
    pingResponse?: PingResponse | undefined;
}
export declare const RemoteSignerError: {
    encode(message: RemoteSignerError, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RemoteSignerError;
    fromJSON(object: any): RemoteSignerError;
    toJSON(message: RemoteSignerError): unknown;
    create(base?: DeepPartial<RemoteSignerError>): RemoteSignerError;
    fromPartial(object: DeepPartial<RemoteSignerError>): RemoteSignerError;
};
export declare const PubKeyRequest: {
    encode(message: PubKeyRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PubKeyRequest;
    fromJSON(object: any): PubKeyRequest;
    toJSON(message: PubKeyRequest): unknown;
    create(base?: DeepPartial<PubKeyRequest>): PubKeyRequest;
    fromPartial(object: DeepPartial<PubKeyRequest>): PubKeyRequest;
};
export declare const PubKeyResponse: {
    encode(message: PubKeyResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PubKeyResponse;
    fromJSON(object: any): PubKeyResponse;
    toJSON(message: PubKeyResponse): unknown;
    create(base?: DeepPartial<PubKeyResponse>): PubKeyResponse;
    fromPartial(object: DeepPartial<PubKeyResponse>): PubKeyResponse;
};
export declare const SignVoteRequest: {
    encode(message: SignVoteRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignVoteRequest;
    fromJSON(object: any): SignVoteRequest;
    toJSON(message: SignVoteRequest): unknown;
    create(base?: DeepPartial<SignVoteRequest>): SignVoteRequest;
    fromPartial(object: DeepPartial<SignVoteRequest>): SignVoteRequest;
};
export declare const SignedVoteResponse: {
    encode(message: SignedVoteResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignedVoteResponse;
    fromJSON(object: any): SignedVoteResponse;
    toJSON(message: SignedVoteResponse): unknown;
    create(base?: DeepPartial<SignedVoteResponse>): SignedVoteResponse;
    fromPartial(object: DeepPartial<SignedVoteResponse>): SignedVoteResponse;
};
export declare const SignProposalRequest: {
    encode(message: SignProposalRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignProposalRequest;
    fromJSON(object: any): SignProposalRequest;
    toJSON(message: SignProposalRequest): unknown;
    create(base?: DeepPartial<SignProposalRequest>): SignProposalRequest;
    fromPartial(object: DeepPartial<SignProposalRequest>): SignProposalRequest;
};
export declare const SignedProposalResponse: {
    encode(message: SignedProposalResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SignedProposalResponse;
    fromJSON(object: any): SignedProposalResponse;
    toJSON(message: SignedProposalResponse): unknown;
    create(base?: DeepPartial<SignedProposalResponse>): SignedProposalResponse;
    fromPartial(object: DeepPartial<SignedProposalResponse>): SignedProposalResponse;
};
export declare const PingRequest: {
    encode(_: PingRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PingRequest;
    fromJSON(_: any): PingRequest;
    toJSON(_: PingRequest): unknown;
    create(base?: DeepPartial<PingRequest>): PingRequest;
    fromPartial(_: DeepPartial<PingRequest>): PingRequest;
};
export declare const PingResponse: {
    encode(_: PingResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PingResponse;
    fromJSON(_: any): PingResponse;
    toJSON(_: PingResponse): unknown;
    create(base?: DeepPartial<PingResponse>): PingResponse;
    fromPartial(_: DeepPartial<PingResponse>): PingResponse;
};
export declare const Message: {
    encode(message: Message, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Message;
    fromJSON(object: any): Message;
    toJSON(message: Message): unknown;
    create(base?: DeepPartial<Message>): Message;
    fromPartial(object: DeepPartial<Message>): Message;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
