import _m0 from "protobufjs/minimal.js";
import { ValidatorUpdate } from "../../abci/v1beta1/types";
import { Event } from "../../abci/v1beta2/types";
import { ExecTxResult, ResponseFinalizeBlock } from "../../abci/v1beta3/types";
import { ConsensusParams } from "../../types/v1/params";
import { BlockID } from "../../types/v1beta1/types";
import { ValidatorSet } from "../../types/v1beta1/validator";
import { Version } from "../v1beta1/types";
export declare const protobufPackage = "cometbft.state.v1beta3";
/**
 * LegacyABCIResponses retains the responses
 * of the legacy ABCI calls during block processing.
 * Note ReponseDeliverTx is renamed to ExecTxResult but they are semantically the same
 * Kept for backwards compatibility for versions prior to v0.38
 */
export interface LegacyABCIResponses {
    deliverTxs: ExecTxResult[];
    endBlock: ResponseEndBlock | undefined;
    beginBlock: ResponseBeginBlock | undefined;
}
/**
 * ResponseBeginBlock is kept for backward compatibility for versions prior to v0.38,
 * as it was then defined in the cometbft.abci packages.
 */
export interface ResponseBeginBlock {
    events: Event[];
}
/**
 * ResponseEndBlock is kept for backward compatibility for versions prior to v0.38,
 * its earlier revisions were defined in the cometbft.abci packages.
 * It uses an updated definition for the consensus_param_updates field to keep the
 * generated data types interoperable with the latest protocol.
 */
export interface ResponseEndBlock {
    validatorUpdates: ValidatorUpdate[];
    consensusParamUpdates: ConsensusParams | undefined;
    events: Event[];
}
/** ConsensusParamsInfo represents the latest consensus params, or the last height it changed */
export interface ConsensusParamsInfo {
    consensusParams: ConsensusParams | undefined;
    lastHeightChanged: string;
}
/** ABCIResponsesInfo retains the responses of the ABCI calls during block processing. */
export interface ABCIResponsesInfo {
    /** Retains the responses of the legacy ABCI calls during block processing. */
    legacyAbciResponses: LegacyABCIResponses | undefined;
    height: string;
    responseFinalizeBlock: ResponseFinalizeBlock | undefined;
}
/** State represents the state of the blockchain. */
export interface State {
    version: Version | undefined;
    /** immutable */
    chainId: string;
    initialHeight: string;
    /** LastBlockHeight=0 at genesis (ie. block(H=0) does not exist) */
    lastBlockHeight: string;
    lastBlockId: BlockID | undefined;
    lastBlockTime: Date | undefined;
    /**
     * LastValidators is used to validate block.LastCommit.
     * Validators are persisted to the database separately every time they change,
     * so we can query for historical validator sets.
     * Note that if s.LastBlockHeight causes a valset change,
     * we set s.LastHeightValidatorsChanged = s.LastBlockHeight + 1 + 1
     * Extra +1 due to nextValSet delay.
     */
    nextValidators: ValidatorSet | undefined;
    validators: ValidatorSet | undefined;
    lastValidators: ValidatorSet | undefined;
    lastHeightValidatorsChanged: string;
    /**
     * Consensus parameters used for validating blocks.
     * Changes returned by EndBlock and updated after Commit.
     */
    consensusParams: ConsensusParams | undefined;
    lastHeightConsensusParamsChanged: string;
    /** Merkle root of the results from executing prev block */
    lastResultsHash: Uint8Array;
    /** the latest AppHash we've received from calling abci.Commit() */
    appHash: Uint8Array;
}
export declare const LegacyABCIResponses: {
    encode(message: LegacyABCIResponses, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): LegacyABCIResponses;
    fromJSON(object: any): LegacyABCIResponses;
    toJSON(message: LegacyABCIResponses): unknown;
    create(base?: DeepPartial<LegacyABCIResponses>): LegacyABCIResponses;
    fromPartial(object: DeepPartial<LegacyABCIResponses>): LegacyABCIResponses;
};
export declare const ResponseBeginBlock: {
    encode(message: ResponseBeginBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseBeginBlock;
    fromJSON(object: any): ResponseBeginBlock;
    toJSON(message: ResponseBeginBlock): unknown;
    create(base?: DeepPartial<ResponseBeginBlock>): ResponseBeginBlock;
    fromPartial(object: DeepPartial<ResponseBeginBlock>): ResponseBeginBlock;
};
export declare const ResponseEndBlock: {
    encode(message: ResponseEndBlock, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseEndBlock;
    fromJSON(object: any): ResponseEndBlock;
    toJSON(message: ResponseEndBlock): unknown;
    create(base?: DeepPartial<ResponseEndBlock>): ResponseEndBlock;
    fromPartial(object: DeepPartial<ResponseEndBlock>): ResponseEndBlock;
};
export declare const ConsensusParamsInfo: {
    encode(message: ConsensusParamsInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConsensusParamsInfo;
    fromJSON(object: any): ConsensusParamsInfo;
    toJSON(message: ConsensusParamsInfo): unknown;
    create(base?: DeepPartial<ConsensusParamsInfo>): ConsensusParamsInfo;
    fromPartial(object: DeepPartial<ConsensusParamsInfo>): ConsensusParamsInfo;
};
export declare const ABCIResponsesInfo: {
    encode(message: ABCIResponsesInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ABCIResponsesInfo;
    fromJSON(object: any): ABCIResponsesInfo;
    toJSON(message: ABCIResponsesInfo): unknown;
    create(base?: DeepPartial<ABCIResponsesInfo>): ABCIResponsesInfo;
    fromPartial(object: DeepPartial<ABCIResponsesInfo>): ABCIResponsesInfo;
};
export declare const State: {
    encode(message: State, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): State;
    fromJSON(object: any): State;
    toJSON(message: State): unknown;
    create(base?: DeepPartial<State>): State;
    fromPartial(object: DeepPartial<State>): State;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
