{"name": "content-hash", "version": "2.5.2", "description": "simple tool to encode/decode content hash for EIP 1577 compliant ENS Resolvers", "main": "./src/index.js", "repository": {"type": "git", "url": "https://github.com/pldespaigne/content-hash"}, "scripts": {"build": "browserify ./src/index.js --s contentHash > ./dist/index.js", "demo": "static .", "deploy-demo": "echo \"surge demo https://content-hash.surge.sh\"", "test": "mocha", "lint": "npx eslint ."}, "author": "pldespaigne", "license": "ISC", "dependencies": {"cids": "^0.7.1", "multicodec": "^0.5.5", "multihashes": "^0.4.15"}, "devDependencies": {"browserify": "^16.5.0", "chai": "^4.2.0", "eslint": "^5.16.0", "mocha": "^5.2.0", "node-static": "^0.7.11"}, "keywords": ["ethereum", "ens", "eip-1577", "resolver", "ipfs", "swarm", "content-hash", "content", "hash", "<PERSON><PERSON><PERSON>", "contentHash"]}