import _m0 from "protobufjs/minimal.js";
import { ClaimType } from "./attestation";
import { BridgeValidator } from "./types";
export declare const protobufPackage = "injective.peggy.v1";
export interface EventAttestationObserved {
    attestationType: ClaimType;
    bridgeContract: string;
    bridgeChainId: string;
    attestationId: Uint8Array;
    nonce: string;
}
export interface EventBridgeWithdrawCanceled {
    bridgeContract: string;
    bridgeChainId: string;
}
export interface EventOutgoingBatch {
    denom: string;
    orchestratorAddress: string;
    batchNonce: string;
    batchTimeout: string;
    batchTxIds: string[];
}
export interface EventOutgoingBatchCanceled {
    bridgeContract: string;
    bridgeChainId: string;
    batchId: string;
    nonce: string;
}
export interface EventValsetUpdateRequest {
    valsetNonce: string;
    valsetHeight: string;
    valsetMembers: BridgeValidator[];
    rewardAmount: string;
    rewardToken: string;
}
export interface EventSetOrchestratorAddresses {
    validatorAddress: string;
    orchestratorAddress: string;
    operatorEthAddress: string;
}
export interface EventValsetConfirm {
    valsetNonce: string;
    orchestratorAddress: string;
}
export interface EventSendToEth {
    outgoingTxId: string;
    sender: string;
    receiver: string;
    amount: string;
    bridgeFee: string;
}
export interface EventConfirmBatch {
    batchNonce: string;
    orchestratorAddress: string;
}
export interface EventAttestationVote {
    eventNonce: string;
    attestationId: Uint8Array;
    voter: string;
}
export interface EventDepositClaim {
    eventNonce: string;
    eventHeight: string;
    attestationId: Uint8Array;
    ethereumSender: string;
    cosmosReceiver: string;
    tokenContract: string;
    amount: string;
    orchestratorAddress: string;
    data: string;
}
export interface EventWithdrawClaim {
    eventNonce: string;
    eventHeight: string;
    attestationId: Uint8Array;
    batchNonce: string;
    tokenContract: string;
    orchestratorAddress: string;
}
export interface EventERC20DeployedClaim {
    eventNonce: string;
    eventHeight: string;
    attestationId: Uint8Array;
    cosmosDenom: string;
    tokenContract: string;
    name: string;
    symbol: string;
    decimals: string;
    orchestratorAddress: string;
}
export interface EventValsetUpdateClaim {
    eventNonce: string;
    eventHeight: string;
    attestationId: Uint8Array;
    valsetNonce: string;
    valsetMembers: BridgeValidator[];
    rewardAmount: string;
    rewardToken: string;
    orchestratorAddress: string;
}
export interface EventCancelSendToEth {
    outgoingTxId: string;
}
export interface EventSubmitBadSignatureEvidence {
    badEthSignature: string;
    badEthSignatureSubject: string;
}
export interface EventValidatorSlash {
    power: string;
    reason: string;
    consensusAddress: string;
    operatorAddress: string;
    moniker: string;
}
export interface EventDepositReceived {
    /** Ethereum sender address */
    sender: string;
    /** Injective receiver address */
    receiver: string;
    /** Coin deposited to Injective */
    amount: string;
}
export interface EventWithdrawalsCompleted {
    /** token denom of each withdrawal */
    denom: string;
    /** individual withdrawals */
    withdrawals: Withdrawal[];
}
export interface Withdrawal {
    /** Injective sender address */
    sender: string;
    /** Ethereum receiver address */
    receiver: string;
    /** Amount of tokens withdrawn to Ethereum */
    amount: string;
}
export declare const EventAttestationObserved: {
    encode(message: EventAttestationObserved, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAttestationObserved;
    fromJSON(object: any): EventAttestationObserved;
    toJSON(message: EventAttestationObserved): unknown;
    create(base?: DeepPartial<EventAttestationObserved>): EventAttestationObserved;
    fromPartial(object: DeepPartial<EventAttestationObserved>): EventAttestationObserved;
};
export declare const EventBridgeWithdrawCanceled: {
    encode(message: EventBridgeWithdrawCanceled, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBridgeWithdrawCanceled;
    fromJSON(object: any): EventBridgeWithdrawCanceled;
    toJSON(message: EventBridgeWithdrawCanceled): unknown;
    create(base?: DeepPartial<EventBridgeWithdrawCanceled>): EventBridgeWithdrawCanceled;
    fromPartial(object: DeepPartial<EventBridgeWithdrawCanceled>): EventBridgeWithdrawCanceled;
};
export declare const EventOutgoingBatch: {
    encode(message: EventOutgoingBatch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOutgoingBatch;
    fromJSON(object: any): EventOutgoingBatch;
    toJSON(message: EventOutgoingBatch): unknown;
    create(base?: DeepPartial<EventOutgoingBatch>): EventOutgoingBatch;
    fromPartial(object: DeepPartial<EventOutgoingBatch>): EventOutgoingBatch;
};
export declare const EventOutgoingBatchCanceled: {
    encode(message: EventOutgoingBatchCanceled, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventOutgoingBatchCanceled;
    fromJSON(object: any): EventOutgoingBatchCanceled;
    toJSON(message: EventOutgoingBatchCanceled): unknown;
    create(base?: DeepPartial<EventOutgoingBatchCanceled>): EventOutgoingBatchCanceled;
    fromPartial(object: DeepPartial<EventOutgoingBatchCanceled>): EventOutgoingBatchCanceled;
};
export declare const EventValsetUpdateRequest: {
    encode(message: EventValsetUpdateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventValsetUpdateRequest;
    fromJSON(object: any): EventValsetUpdateRequest;
    toJSON(message: EventValsetUpdateRequest): unknown;
    create(base?: DeepPartial<EventValsetUpdateRequest>): EventValsetUpdateRequest;
    fromPartial(object: DeepPartial<EventValsetUpdateRequest>): EventValsetUpdateRequest;
};
export declare const EventSetOrchestratorAddresses: {
    encode(message: EventSetOrchestratorAddresses, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetOrchestratorAddresses;
    fromJSON(object: any): EventSetOrchestratorAddresses;
    toJSON(message: EventSetOrchestratorAddresses): unknown;
    create(base?: DeepPartial<EventSetOrchestratorAddresses>): EventSetOrchestratorAddresses;
    fromPartial(object: DeepPartial<EventSetOrchestratorAddresses>): EventSetOrchestratorAddresses;
};
export declare const EventValsetConfirm: {
    encode(message: EventValsetConfirm, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventValsetConfirm;
    fromJSON(object: any): EventValsetConfirm;
    toJSON(message: EventValsetConfirm): unknown;
    create(base?: DeepPartial<EventValsetConfirm>): EventValsetConfirm;
    fromPartial(object: DeepPartial<EventValsetConfirm>): EventValsetConfirm;
};
export declare const EventSendToEth: {
    encode(message: EventSendToEth, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSendToEth;
    fromJSON(object: any): EventSendToEth;
    toJSON(message: EventSendToEth): unknown;
    create(base?: DeepPartial<EventSendToEth>): EventSendToEth;
    fromPartial(object: DeepPartial<EventSendToEth>): EventSendToEth;
};
export declare const EventConfirmBatch: {
    encode(message: EventConfirmBatch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventConfirmBatch;
    fromJSON(object: any): EventConfirmBatch;
    toJSON(message: EventConfirmBatch): unknown;
    create(base?: DeepPartial<EventConfirmBatch>): EventConfirmBatch;
    fromPartial(object: DeepPartial<EventConfirmBatch>): EventConfirmBatch;
};
export declare const EventAttestationVote: {
    encode(message: EventAttestationVote, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventAttestationVote;
    fromJSON(object: any): EventAttestationVote;
    toJSON(message: EventAttestationVote): unknown;
    create(base?: DeepPartial<EventAttestationVote>): EventAttestationVote;
    fromPartial(object: DeepPartial<EventAttestationVote>): EventAttestationVote;
};
export declare const EventDepositClaim: {
    encode(message: EventDepositClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventDepositClaim;
    fromJSON(object: any): EventDepositClaim;
    toJSON(message: EventDepositClaim): unknown;
    create(base?: DeepPartial<EventDepositClaim>): EventDepositClaim;
    fromPartial(object: DeepPartial<EventDepositClaim>): EventDepositClaim;
};
export declare const EventWithdrawClaim: {
    encode(message: EventWithdrawClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventWithdrawClaim;
    fromJSON(object: any): EventWithdrawClaim;
    toJSON(message: EventWithdrawClaim): unknown;
    create(base?: DeepPartial<EventWithdrawClaim>): EventWithdrawClaim;
    fromPartial(object: DeepPartial<EventWithdrawClaim>): EventWithdrawClaim;
};
export declare const EventERC20DeployedClaim: {
    encode(message: EventERC20DeployedClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventERC20DeployedClaim;
    fromJSON(object: any): EventERC20DeployedClaim;
    toJSON(message: EventERC20DeployedClaim): unknown;
    create(base?: DeepPartial<EventERC20DeployedClaim>): EventERC20DeployedClaim;
    fromPartial(object: DeepPartial<EventERC20DeployedClaim>): EventERC20DeployedClaim;
};
export declare const EventValsetUpdateClaim: {
    encode(message: EventValsetUpdateClaim, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventValsetUpdateClaim;
    fromJSON(object: any): EventValsetUpdateClaim;
    toJSON(message: EventValsetUpdateClaim): unknown;
    create(base?: DeepPartial<EventValsetUpdateClaim>): EventValsetUpdateClaim;
    fromPartial(object: DeepPartial<EventValsetUpdateClaim>): EventValsetUpdateClaim;
};
export declare const EventCancelSendToEth: {
    encode(message: EventCancelSendToEth, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventCancelSendToEth;
    fromJSON(object: any): EventCancelSendToEth;
    toJSON(message: EventCancelSendToEth): unknown;
    create(base?: DeepPartial<EventCancelSendToEth>): EventCancelSendToEth;
    fromPartial(object: DeepPartial<EventCancelSendToEth>): EventCancelSendToEth;
};
export declare const EventSubmitBadSignatureEvidence: {
    encode(message: EventSubmitBadSignatureEvidence, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSubmitBadSignatureEvidence;
    fromJSON(object: any): EventSubmitBadSignatureEvidence;
    toJSON(message: EventSubmitBadSignatureEvidence): unknown;
    create(base?: DeepPartial<EventSubmitBadSignatureEvidence>): EventSubmitBadSignatureEvidence;
    fromPartial(object: DeepPartial<EventSubmitBadSignatureEvidence>): EventSubmitBadSignatureEvidence;
};
export declare const EventValidatorSlash: {
    encode(message: EventValidatorSlash, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventValidatorSlash;
    fromJSON(object: any): EventValidatorSlash;
    toJSON(message: EventValidatorSlash): unknown;
    create(base?: DeepPartial<EventValidatorSlash>): EventValidatorSlash;
    fromPartial(object: DeepPartial<EventValidatorSlash>): EventValidatorSlash;
};
export declare const EventDepositReceived: {
    encode(message: EventDepositReceived, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventDepositReceived;
    fromJSON(object: any): EventDepositReceived;
    toJSON(message: EventDepositReceived): unknown;
    create(base?: DeepPartial<EventDepositReceived>): EventDepositReceived;
    fromPartial(object: DeepPartial<EventDepositReceived>): EventDepositReceived;
};
export declare const EventWithdrawalsCompleted: {
    encode(message: EventWithdrawalsCompleted, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventWithdrawalsCompleted;
    fromJSON(object: any): EventWithdrawalsCompleted;
    toJSON(message: EventWithdrawalsCompleted): unknown;
    create(base?: DeepPartial<EventWithdrawalsCompleted>): EventWithdrawalsCompleted;
    fromPartial(object: DeepPartial<EventWithdrawalsCompleted>): EventWithdrawalsCompleted;
};
export declare const Withdrawal: {
    encode(message: Withdrawal, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Withdrawal;
    fromJSON(object: any): Withdrawal;
    toJSON(message: Withdrawal): unknown;
    create(base?: DeepPartial<Withdrawal>): Withdrawal;
    fromPartial(object: DeepPartial<Withdrawal>): Withdrawal;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
