/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "injective_chart_rpc";
function createBaseSpotMarketHistoryRequest() {
    return { symbol: "", marketId: "", resolution: "", from: 0, to: 0, countback: 0 };
}
export const SpotMarketHistoryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        if (message.from !== 0) {
            writer.uint32(32).sint32(message.from);
        }
        if (message.to !== 0) {
            writer.uint32(40).sint32(message.to);
        }
        if (message.countback !== 0) {
            writer.uint32(48).sint32(message.countback);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketHistoryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                case 4:
                    message.from = reader.sint32();
                    break;
                case 5:
                    message.to = reader.sint32();
                    break;
                case 6:
                    message.countback = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            from: isSet(object.from) ? Number(object.from) : 0,
            to: isSet(object.to) ? Number(object.to) : 0,
            countback: isSet(object.countback) ? Number(object.countback) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.from !== undefined && (obj.from = Math.round(message.from));
        message.to !== undefined && (obj.to = Math.round(message.to));
        message.countback !== undefined && (obj.countback = Math.round(message.countback));
        return obj;
    },
    create(base) {
        return SpotMarketHistoryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotMarketHistoryRequest();
        message.symbol = object.symbol ?? "";
        message.marketId = object.marketId ?? "";
        message.resolution = object.resolution ?? "";
        message.from = object.from ?? 0;
        message.to = object.to ?? 0;
        message.countback = object.countback ?? 0;
        return message;
    },
};
function createBaseSpotMarketHistoryResponse() {
    return { t: [], o: [], h: [], l: [], c: [], v: [], s: "" };
}
export const SpotMarketHistoryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.o) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(26).fork();
        for (const v of message.h) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(34).fork();
        for (const v of message.l) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(42).fork();
        for (const v of message.c) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(50).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        if (message.s !== "") {
            writer.uint32(58).string(message.s);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketHistoryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.o.push(reader.double());
                        }
                    }
                    else {
                        message.o.push(reader.double());
                    }
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.h.push(reader.double());
                        }
                    }
                    else {
                        message.h.push(reader.double());
                    }
                    break;
                case 4:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.l.push(reader.double());
                        }
                    }
                    else {
                        message.l.push(reader.double());
                    }
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.c.push(reader.double());
                        }
                    }
                    else {
                        message.c.push(reader.double());
                    }
                    break;
                case 6:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                case 7:
                    message.s = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object?.t) ? object.t.map((e) => Number(e)) : [],
            o: Array.isArray(object?.o) ? object.o.map((e) => Number(e)) : [],
            h: Array.isArray(object?.h) ? object.h.map((e) => Number(e)) : [],
            l: Array.isArray(object?.l) ? object.l.map((e) => Number(e)) : [],
            c: Array.isArray(object?.c) ? object.c.map((e) => Number(e)) : [],
            v: Array.isArray(object?.v) ? object.v.map((e) => Number(e)) : [],
            s: isSet(object.s) ? String(object.s) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.o) {
            obj.o = message.o.map((e) => e);
        }
        else {
            obj.o = [];
        }
        if (message.h) {
            obj.h = message.h.map((e) => e);
        }
        else {
            obj.h = [];
        }
        if (message.l) {
            obj.l = message.l.map((e) => e);
        }
        else {
            obj.l = [];
        }
        if (message.c) {
            obj.c = message.c.map((e) => e);
        }
        else {
            obj.c = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        message.s !== undefined && (obj.s = message.s);
        return obj;
    },
    create(base) {
        return SpotMarketHistoryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotMarketHistoryResponse();
        message.t = object.t?.map((e) => e) || [];
        message.o = object.o?.map((e) => e) || [];
        message.h = object.h?.map((e) => e) || [];
        message.l = object.l?.map((e) => e) || [];
        message.c = object.c?.map((e) => e) || [];
        message.v = object.v?.map((e) => e) || [];
        message.s = object.s ?? "";
        return message;
    },
};
function createBaseDerivativeMarketHistoryRequest() {
    return { symbol: "", marketId: "", resolution: "", from: 0, to: 0, countback: 0 };
}
export const DerivativeMarketHistoryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        if (message.from !== 0) {
            writer.uint32(32).sint32(message.from);
        }
        if (message.to !== 0) {
            writer.uint32(40).sint32(message.to);
        }
        if (message.countback !== 0) {
            writer.uint32(48).sint32(message.countback);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketHistoryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                case 4:
                    message.from = reader.sint32();
                    break;
                case 5:
                    message.to = reader.sint32();
                    break;
                case 6:
                    message.countback = reader.sint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
            from: isSet(object.from) ? Number(object.from) : 0,
            to: isSet(object.to) ? Number(object.to) : 0,
            countback: isSet(object.countback) ? Number(object.countback) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        message.from !== undefined && (obj.from = Math.round(message.from));
        message.to !== undefined && (obj.to = Math.round(message.to));
        message.countback !== undefined && (obj.countback = Math.round(message.countback));
        return obj;
    },
    create(base) {
        return DerivativeMarketHistoryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeMarketHistoryRequest();
        message.symbol = object.symbol ?? "";
        message.marketId = object.marketId ?? "";
        message.resolution = object.resolution ?? "";
        message.from = object.from ?? 0;
        message.to = object.to ?? 0;
        message.countback = object.countback ?? 0;
        return message;
    },
};
function createBaseDerivativeMarketHistoryResponse() {
    return { t: [], o: [], h: [], l: [], c: [], v: [], s: "" };
}
export const DerivativeMarketHistoryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        writer.uint32(10).fork();
        for (const v of message.t) {
            writer.sint32(v);
        }
        writer.ldelim();
        writer.uint32(18).fork();
        for (const v of message.o) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(26).fork();
        for (const v of message.h) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(34).fork();
        for (const v of message.l) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(42).fork();
        for (const v of message.c) {
            writer.double(v);
        }
        writer.ldelim();
        writer.uint32(50).fork();
        for (const v of message.v) {
            writer.double(v);
        }
        writer.ldelim();
        if (message.s !== "") {
            writer.uint32(58).string(message.s);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketHistoryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.t.push(reader.sint32());
                        }
                    }
                    else {
                        message.t.push(reader.sint32());
                    }
                    break;
                case 2:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.o.push(reader.double());
                        }
                    }
                    else {
                        message.o.push(reader.double());
                    }
                    break;
                case 3:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.h.push(reader.double());
                        }
                    }
                    else {
                        message.h.push(reader.double());
                    }
                    break;
                case 4:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.l.push(reader.double());
                        }
                    }
                    else {
                        message.l.push(reader.double());
                    }
                    break;
                case 5:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.c.push(reader.double());
                        }
                    }
                    else {
                        message.c.push(reader.double());
                    }
                    break;
                case 6:
                    if ((tag & 7) === 2) {
                        const end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.v.push(reader.double());
                        }
                    }
                    else {
                        message.v.push(reader.double());
                    }
                    break;
                case 7:
                    message.s = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            t: Array.isArray(object?.t) ? object.t.map((e) => Number(e)) : [],
            o: Array.isArray(object?.o) ? object.o.map((e) => Number(e)) : [],
            h: Array.isArray(object?.h) ? object.h.map((e) => Number(e)) : [],
            l: Array.isArray(object?.l) ? object.l.map((e) => Number(e)) : [],
            c: Array.isArray(object?.c) ? object.c.map((e) => Number(e)) : [],
            v: Array.isArray(object?.v) ? object.v.map((e) => Number(e)) : [],
            s: isSet(object.s) ? String(object.s) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.t) {
            obj.t = message.t.map((e) => Math.round(e));
        }
        else {
            obj.t = [];
        }
        if (message.o) {
            obj.o = message.o.map((e) => e);
        }
        else {
            obj.o = [];
        }
        if (message.h) {
            obj.h = message.h.map((e) => e);
        }
        else {
            obj.h = [];
        }
        if (message.l) {
            obj.l = message.l.map((e) => e);
        }
        else {
            obj.l = [];
        }
        if (message.c) {
            obj.c = message.c.map((e) => e);
        }
        else {
            obj.c = [];
        }
        if (message.v) {
            obj.v = message.v.map((e) => e);
        }
        else {
            obj.v = [];
        }
        message.s !== undefined && (obj.s = message.s);
        return obj;
    },
    create(base) {
        return DerivativeMarketHistoryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeMarketHistoryResponse();
        message.t = object.t?.map((e) => e) || [];
        message.o = object.o?.map((e) => e) || [];
        message.h = object.h?.map((e) => e) || [];
        message.l = object.l?.map((e) => e) || [];
        message.c = object.c?.map((e) => e) || [];
        message.v = object.v?.map((e) => e) || [];
        message.s = object.s ?? "";
        return message;
    },
};
function createBaseSpotMarketSummaryRequest() {
    return { marketId: "", resolution: "" };
}
export const SpotMarketSummaryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.resolution !== "") {
            writer.uint32(18).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return SpotMarketSummaryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotMarketSummaryRequest();
        message.marketId = object.marketId ?? "";
        message.resolution = object.resolution ?? "";
        return message;
    },
};
function createBaseSpotMarketSummaryResponse() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
export const SpotMarketSummaryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSpotMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return SpotMarketSummaryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSpotMarketSummaryResponse();
        message.marketId = object.marketId ?? "";
        message.open = object.open ?? 0;
        message.high = object.high ?? 0;
        message.low = object.low ?? 0;
        message.volume = object.volume ?? 0;
        message.price = object.price ?? 0;
        message.change = object.change ?? 0;
        return message;
    },
};
function createBaseAllSpotMarketSummaryRequest() {
    return { resolution: "" };
}
export const AllSpotMarketSummaryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllSpotMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { resolution: isSet(object.resolution) ? String(object.resolution) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return AllSpotMarketSummaryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAllSpotMarketSummaryRequest();
        message.resolution = object.resolution ?? "";
        return message;
    },
};
function createBaseAllSpotMarketSummaryResponse() {
    return { field: [] };
}
export const AllSpotMarketSummaryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.field) {
            MarketSummaryResp.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllSpotMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.field.push(MarketSummaryResp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { field: Array.isArray(object?.field) ? object.field.map((e) => MarketSummaryResp.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.field) {
            obj.field = message.field.map((e) => e ? MarketSummaryResp.toJSON(e) : undefined);
        }
        else {
            obj.field = [];
        }
        return obj;
    },
    create(base) {
        return AllSpotMarketSummaryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAllSpotMarketSummaryResponse();
        message.field = object.field?.map((e) => MarketSummaryResp.fromPartial(e)) || [];
        return message;
    },
};
function createBaseMarketSummaryResp() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
export const MarketSummaryResp = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMarketSummaryResp();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return MarketSummaryResp.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMarketSummaryResp();
        message.marketId = object.marketId ?? "";
        message.open = object.open ?? 0;
        message.high = object.high ?? 0;
        message.low = object.low ?? 0;
        message.volume = object.volume ?? 0;
        message.price = object.price ?? 0;
        message.change = object.change ?? 0;
        return message;
    },
};
function createBaseDerivativeMarketSummaryRequest() {
    return { marketId: "", indexPrice: false, resolution: "" };
}
export const DerivativeMarketSummaryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.indexPrice === true) {
            writer.uint32(16).bool(message.indexPrice);
        }
        if (message.resolution !== "") {
            writer.uint32(26).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.indexPrice = reader.bool();
                    break;
                case 3:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            indexPrice: isSet(object.indexPrice) ? Boolean(object.indexPrice) : false,
            resolution: isSet(object.resolution) ? String(object.resolution) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.indexPrice !== undefined && (obj.indexPrice = message.indexPrice);
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return DerivativeMarketSummaryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeMarketSummaryRequest();
        message.marketId = object.marketId ?? "";
        message.indexPrice = object.indexPrice ?? false;
        message.resolution = object.resolution ?? "";
        return message;
    },
};
function createBaseDerivativeMarketSummaryResponse() {
    return { marketId: "", open: 0, high: 0, low: 0, volume: 0, price: 0, change: 0 };
}
export const DerivativeMarketSummaryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.open !== 0) {
            writer.uint32(17).double(message.open);
        }
        if (message.high !== 0) {
            writer.uint32(25).double(message.high);
        }
        if (message.low !== 0) {
            writer.uint32(33).double(message.low);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        if (message.price !== 0) {
            writer.uint32(49).double(message.price);
        }
        if (message.change !== 0) {
            writer.uint32(57).double(message.change);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseDerivativeMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.open = reader.double();
                    break;
                case 3:
                    message.high = reader.double();
                    break;
                case 4:
                    message.low = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                case 6:
                    message.price = reader.double();
                    break;
                case 7:
                    message.change = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            open: isSet(object.open) ? Number(object.open) : 0,
            high: isSet(object.high) ? Number(object.high) : 0,
            low: isSet(object.low) ? Number(object.low) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
            price: isSet(object.price) ? Number(object.price) : 0,
            change: isSet(object.change) ? Number(object.change) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.open !== undefined && (obj.open = message.open);
        message.high !== undefined && (obj.high = message.high);
        message.low !== undefined && (obj.low = message.low);
        message.volume !== undefined && (obj.volume = message.volume);
        message.price !== undefined && (obj.price = message.price);
        message.change !== undefined && (obj.change = message.change);
        return obj;
    },
    create(base) {
        return DerivativeMarketSummaryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseDerivativeMarketSummaryResponse();
        message.marketId = object.marketId ?? "";
        message.open = object.open ?? 0;
        message.high = object.high ?? 0;
        message.low = object.low ?? 0;
        message.volume = object.volume ?? 0;
        message.price = object.price ?? 0;
        message.change = object.change ?? 0;
        return message;
    },
};
function createBaseAllDerivativeMarketSummaryRequest() {
    return { resolution: "" };
}
export const AllDerivativeMarketSummaryRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.resolution !== "") {
            writer.uint32(10).string(message.resolution);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllDerivativeMarketSummaryRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.resolution = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { resolution: isSet(object.resolution) ? String(object.resolution) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.resolution !== undefined && (obj.resolution = message.resolution);
        return obj;
    },
    create(base) {
        return AllDerivativeMarketSummaryRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAllDerivativeMarketSummaryRequest();
        message.resolution = object.resolution ?? "";
        return message;
    },
};
function createBaseAllDerivativeMarketSummaryResponse() {
    return { field: [] };
}
export const AllDerivativeMarketSummaryResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.field) {
            MarketSummaryResp.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAllDerivativeMarketSummaryResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.field.push(MarketSummaryResp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { field: Array.isArray(object?.field) ? object.field.map((e) => MarketSummaryResp.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.field) {
            obj.field = message.field.map((e) => e ? MarketSummaryResp.toJSON(e) : undefined);
        }
        else {
            obj.field = [];
        }
        return obj;
    },
    create(base) {
        return AllDerivativeMarketSummaryResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseAllDerivativeMarketSummaryResponse();
        message.field = object.field?.map((e) => MarketSummaryResp.fromPartial(e)) || [];
        return message;
    },
};
export class InjectiveChartRPCClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.SpotMarketHistory = this.SpotMarketHistory.bind(this);
        this.DerivativeMarketHistory = this.DerivativeMarketHistory.bind(this);
        this.SpotMarketSummary = this.SpotMarketSummary.bind(this);
        this.AllSpotMarketSummary = this.AllSpotMarketSummary.bind(this);
        this.DerivativeMarketSummary = this.DerivativeMarketSummary.bind(this);
        this.AllDerivativeMarketSummary = this.AllDerivativeMarketSummary.bind(this);
    }
    SpotMarketHistory(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCSpotMarketHistoryDesc, SpotMarketHistoryRequest.fromPartial(request), metadata);
    }
    DerivativeMarketHistory(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCDerivativeMarketHistoryDesc, DerivativeMarketHistoryRequest.fromPartial(request), metadata);
    }
    SpotMarketSummary(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCSpotMarketSummaryDesc, SpotMarketSummaryRequest.fromPartial(request), metadata);
    }
    AllSpotMarketSummary(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCAllSpotMarketSummaryDesc, AllSpotMarketSummaryRequest.fromPartial(request), metadata);
    }
    DerivativeMarketSummary(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCDerivativeMarketSummaryDesc, DerivativeMarketSummaryRequest.fromPartial(request), metadata);
    }
    AllDerivativeMarketSummary(request, metadata) {
        return this.rpc.unary(InjectiveChartRPCAllDerivativeMarketSummaryDesc, AllDerivativeMarketSummaryRequest.fromPartial(request), metadata);
    }
}
export const InjectiveChartRPCDesc = { serviceName: "injective_chart_rpc.InjectiveChartRPC" };
export const InjectiveChartRPCSpotMarketHistoryDesc = {
    methodName: "SpotMarketHistory",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SpotMarketHistoryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SpotMarketHistoryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveChartRPCDerivativeMarketHistoryDesc = {
    methodName: "DerivativeMarketHistory",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return DerivativeMarketHistoryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = DerivativeMarketHistoryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveChartRPCSpotMarketSummaryDesc = {
    methodName: "SpotMarketSummary",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return SpotMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = SpotMarketSummaryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveChartRPCAllSpotMarketSummaryDesc = {
    methodName: "AllSpotMarketSummary",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return AllSpotMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = AllSpotMarketSummaryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveChartRPCDerivativeMarketSummaryDesc = {
    methodName: "DerivativeMarketSummary",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return DerivativeMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = DerivativeMarketSummaryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveChartRPCAllDerivativeMarketSummaryDesc = {
    methodName: "AllDerivativeMarketSummary",
    service: InjectiveChartRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return AllDerivativeMarketSummaryRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = AllDerivativeMarketSummaryResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
