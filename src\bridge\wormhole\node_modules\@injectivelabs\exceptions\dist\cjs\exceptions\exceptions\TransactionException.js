"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
const maps_js_1 = require("../utils/maps.js");
class TransactionException extends base_js_1.ConcreteException {
    static errorClass = 'TransactionException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.ChainError;
    }
    parse() {
        const { message, context, contextModule, contextCode } = this;
        const { code, message: parsedMessage, contextModule: parsedContextModule, } = (0, maps_js_1.mapFailedTransactionMessage)(message, { contextCode, contextModule });
        this.setContext(context || 'Unknown');
        this.setMessage(parsedMessage);
        this.setContextCode(code);
        this.setOriginalMessage((0, maps_js_1.parseErrorMessage)(message));
        if (parsedContextModule) {
            this.setContextModule(parsedContextModule);
        }
        this.setName(TransactionException.errorClass);
    }
}
exports.TransactionException = TransactionException;
