"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventConfigSet = exports.EventNewTransmission = exports.EventTransmitted = exports.EventNewRound = exports.EventAnswerUpdated = exports.EventOraclePaid = exports.ReportToSign = exports.Report = exports.EpochAndRound = exports.Transmission = exports.Payee = exports.GasReimbursements = exports.OracleObservationsCounts = exports.SetBatchConfigProposal = exports.FeedProperties = exports.SetConfigProposal = exports.ContractConfig = exports.ModuleParams = exports.FeedConfigInfo = exports.FeedConfig = exports.Params = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var timestamp_1 = require("../../../google/protobuf/timestamp.js");
exports.protobufPackage = "injective.ocr.v1beta1";
function createBaseParams() {
    return { linkDenom: "", payoutBlockInterval: "0", moduleAdmin: "" };
}
exports.Params = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.linkDenom !== "") {
            writer.uint32(10).string(message.linkDenom);
        }
        if (message.payoutBlockInterval !== "0") {
            writer.uint32(16).uint64(message.payoutBlockInterval);
        }
        if (message.moduleAdmin !== "") {
            writer.uint32(26).string(message.moduleAdmin);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.linkDenom = reader.string();
                    break;
                case 2:
                    message.payoutBlockInterval = longToString(reader.uint64());
                    break;
                case 3:
                    message.moduleAdmin = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            linkDenom: isSet(object.linkDenom) ? String(object.linkDenom) : "",
            payoutBlockInterval: isSet(object.payoutBlockInterval) ? String(object.payoutBlockInterval) : "0",
            moduleAdmin: isSet(object.moduleAdmin) ? String(object.moduleAdmin) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.linkDenom !== undefined && (obj.linkDenom = message.linkDenom);
        message.payoutBlockInterval !== undefined && (obj.payoutBlockInterval = message.payoutBlockInterval);
        message.moduleAdmin !== undefined && (obj.moduleAdmin = message.moduleAdmin);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseParams();
        message.linkDenom = (_a = object.linkDenom) !== null && _a !== void 0 ? _a : "";
        message.payoutBlockInterval = (_b = object.payoutBlockInterval) !== null && _b !== void 0 ? _b : "0";
        message.moduleAdmin = (_c = object.moduleAdmin) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseFeedConfig() {
    return {
        signers: [],
        transmitters: [],
        f: 0,
        onchainConfig: new Uint8Array(),
        offchainConfigVersion: "0",
        offchainConfig: new Uint8Array(),
        moduleParams: undefined,
    };
}
exports.FeedConfig = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.signers), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.transmitters), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        if (message.f !== 0) {
            writer.uint32(24).uint32(message.f);
        }
        if (message.onchainConfig.length !== 0) {
            writer.uint32(34).bytes(message.onchainConfig);
        }
        if (message.offchainConfigVersion !== "0") {
            writer.uint32(40).uint64(message.offchainConfigVersion);
        }
        if (message.offchainConfig.length !== 0) {
            writer.uint32(50).bytes(message.offchainConfig);
        }
        if (message.moduleParams !== undefined) {
            exports.ModuleParams.encode(message.moduleParams, writer.uint32(58).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedConfig();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.signers.push(reader.string());
                    break;
                case 2:
                    message.transmitters.push(reader.string());
                    break;
                case 3:
                    message.f = reader.uint32();
                    break;
                case 4:
                    message.onchainConfig = reader.bytes();
                    break;
                case 5:
                    message.offchainConfigVersion = longToString(reader.uint64());
                    break;
                case 6:
                    message.offchainConfig = reader.bytes();
                    break;
                case 7:
                    message.moduleParams = exports.ModuleParams.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            signers: Array.isArray(object === null || object === void 0 ? void 0 : object.signers) ? object.signers.map(function (e) { return String(e); }) : [],
            transmitters: Array.isArray(object === null || object === void 0 ? void 0 : object.transmitters) ? object.transmitters.map(function (e) { return String(e); }) : [],
            f: isSet(object.f) ? Number(object.f) : 0,
            onchainConfig: isSet(object.onchainConfig) ? bytesFromBase64(object.onchainConfig) : new Uint8Array(),
            offchainConfigVersion: isSet(object.offchainConfigVersion) ? String(object.offchainConfigVersion) : "0",
            offchainConfig: isSet(object.offchainConfig) ? bytesFromBase64(object.offchainConfig) : new Uint8Array(),
            moduleParams: isSet(object.moduleParams) ? exports.ModuleParams.fromJSON(object.moduleParams) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.signers) {
            obj.signers = message.signers.map(function (e) { return e; });
        }
        else {
            obj.signers = [];
        }
        if (message.transmitters) {
            obj.transmitters = message.transmitters.map(function (e) { return e; });
        }
        else {
            obj.transmitters = [];
        }
        message.f !== undefined && (obj.f = Math.round(message.f));
        message.onchainConfig !== undefined &&
            (obj.onchainConfig = base64FromBytes(message.onchainConfig !== undefined ? message.onchainConfig : new Uint8Array()));
        message.offchainConfigVersion !== undefined && (obj.offchainConfigVersion = message.offchainConfigVersion);
        message.offchainConfig !== undefined &&
            (obj.offchainConfig = base64FromBytes(message.offchainConfig !== undefined ? message.offchainConfig : new Uint8Array()));
        message.moduleParams !== undefined &&
            (obj.moduleParams = message.moduleParams ? exports.ModuleParams.toJSON(message.moduleParams) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FeedConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseFeedConfig();
        message.signers = ((_a = object.signers) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.transmitters = ((_b = object.transmitters) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.f = (_c = object.f) !== null && _c !== void 0 ? _c : 0;
        message.onchainConfig = (_d = object.onchainConfig) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.offchainConfigVersion = (_e = object.offchainConfigVersion) !== null && _e !== void 0 ? _e : "0";
        message.offchainConfig = (_f = object.offchainConfig) !== null && _f !== void 0 ? _f : new Uint8Array();
        message.moduleParams = (object.moduleParams !== undefined && object.moduleParams !== null)
            ? exports.ModuleParams.fromPartial(object.moduleParams)
            : undefined;
        return message;
    },
};
function createBaseFeedConfigInfo() {
    return { latestConfigDigest: new Uint8Array(), f: 0, n: 0, configCount: "0", latestConfigBlockNumber: "0" };
}
exports.FeedConfigInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.latestConfigDigest.length !== 0) {
            writer.uint32(10).bytes(message.latestConfigDigest);
        }
        if (message.f !== 0) {
            writer.uint32(16).uint32(message.f);
        }
        if (message.n !== 0) {
            writer.uint32(24).uint32(message.n);
        }
        if (message.configCount !== "0") {
            writer.uint32(32).uint64(message.configCount);
        }
        if (message.latestConfigBlockNumber !== "0") {
            writer.uint32(40).int64(message.latestConfigBlockNumber);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedConfigInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.latestConfigDigest = reader.bytes();
                    break;
                case 2:
                    message.f = reader.uint32();
                    break;
                case 3:
                    message.n = reader.uint32();
                    break;
                case 4:
                    message.configCount = longToString(reader.uint64());
                    break;
                case 5:
                    message.latestConfigBlockNumber = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            latestConfigDigest: isSet(object.latestConfigDigest)
                ? bytesFromBase64(object.latestConfigDigest)
                : new Uint8Array(),
            f: isSet(object.f) ? Number(object.f) : 0,
            n: isSet(object.n) ? Number(object.n) : 0,
            configCount: isSet(object.configCount) ? String(object.configCount) : "0",
            latestConfigBlockNumber: isSet(object.latestConfigBlockNumber) ? String(object.latestConfigBlockNumber) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.latestConfigDigest !== undefined &&
            (obj.latestConfigDigest = base64FromBytes(message.latestConfigDigest !== undefined ? message.latestConfigDigest : new Uint8Array()));
        message.f !== undefined && (obj.f = Math.round(message.f));
        message.n !== undefined && (obj.n = Math.round(message.n));
        message.configCount !== undefined && (obj.configCount = message.configCount);
        message.latestConfigBlockNumber !== undefined && (obj.latestConfigBlockNumber = message.latestConfigBlockNumber);
        return obj;
    },
    create: function (base) {
        return exports.FeedConfigInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseFeedConfigInfo();
        message.latestConfigDigest = (_a = object.latestConfigDigest) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.f = (_b = object.f) !== null && _b !== void 0 ? _b : 0;
        message.n = (_c = object.n) !== null && _c !== void 0 ? _c : 0;
        message.configCount = (_d = object.configCount) !== null && _d !== void 0 ? _d : "0";
        message.latestConfigBlockNumber = (_e = object.latestConfigBlockNumber) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBaseModuleParams() {
    return {
        feedId: "",
        minAnswer: "",
        maxAnswer: "",
        linkPerObservation: "",
        linkPerTransmission: "",
        linkDenom: "",
        uniqueReports: false,
        description: "",
        feedAdmin: "",
        billingAdmin: "",
    };
}
exports.ModuleParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.minAnswer !== "") {
            writer.uint32(18).string(message.minAnswer);
        }
        if (message.maxAnswer !== "") {
            writer.uint32(26).string(message.maxAnswer);
        }
        if (message.linkPerObservation !== "") {
            writer.uint32(34).string(message.linkPerObservation);
        }
        if (message.linkPerTransmission !== "") {
            writer.uint32(42).string(message.linkPerTransmission);
        }
        if (message.linkDenom !== "") {
            writer.uint32(50).string(message.linkDenom);
        }
        if (message.uniqueReports === true) {
            writer.uint32(56).bool(message.uniqueReports);
        }
        if (message.description !== "") {
            writer.uint32(66).string(message.description);
        }
        if (message.feedAdmin !== "") {
            writer.uint32(74).string(message.feedAdmin);
        }
        if (message.billingAdmin !== "") {
            writer.uint32(82).string(message.billingAdmin);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModuleParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.minAnswer = reader.string();
                    break;
                case 3:
                    message.maxAnswer = reader.string();
                    break;
                case 4:
                    message.linkPerObservation = reader.string();
                    break;
                case 5:
                    message.linkPerTransmission = reader.string();
                    break;
                case 6:
                    message.linkDenom = reader.string();
                    break;
                case 7:
                    message.uniqueReports = reader.bool();
                    break;
                case 8:
                    message.description = reader.string();
                    break;
                case 9:
                    message.feedAdmin = reader.string();
                    break;
                case 10:
                    message.billingAdmin = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            minAnswer: isSet(object.minAnswer) ? String(object.minAnswer) : "",
            maxAnswer: isSet(object.maxAnswer) ? String(object.maxAnswer) : "",
            linkPerObservation: isSet(object.linkPerObservation) ? String(object.linkPerObservation) : "",
            linkPerTransmission: isSet(object.linkPerTransmission) ? String(object.linkPerTransmission) : "",
            linkDenom: isSet(object.linkDenom) ? String(object.linkDenom) : "",
            uniqueReports: isSet(object.uniqueReports) ? Boolean(object.uniqueReports) : false,
            description: isSet(object.description) ? String(object.description) : "",
            feedAdmin: isSet(object.feedAdmin) ? String(object.feedAdmin) : "",
            billingAdmin: isSet(object.billingAdmin) ? String(object.billingAdmin) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.minAnswer !== undefined && (obj.minAnswer = message.minAnswer);
        message.maxAnswer !== undefined && (obj.maxAnswer = message.maxAnswer);
        message.linkPerObservation !== undefined && (obj.linkPerObservation = message.linkPerObservation);
        message.linkPerTransmission !== undefined && (obj.linkPerTransmission = message.linkPerTransmission);
        message.linkDenom !== undefined && (obj.linkDenom = message.linkDenom);
        message.uniqueReports !== undefined && (obj.uniqueReports = message.uniqueReports);
        message.description !== undefined && (obj.description = message.description);
        message.feedAdmin !== undefined && (obj.feedAdmin = message.feedAdmin);
        message.billingAdmin !== undefined && (obj.billingAdmin = message.billingAdmin);
        return obj;
    },
    create: function (base) {
        return exports.ModuleParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        var message = createBaseModuleParams();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.minAnswer = (_b = object.minAnswer) !== null && _b !== void 0 ? _b : "";
        message.maxAnswer = (_c = object.maxAnswer) !== null && _c !== void 0 ? _c : "";
        message.linkPerObservation = (_d = object.linkPerObservation) !== null && _d !== void 0 ? _d : "";
        message.linkPerTransmission = (_e = object.linkPerTransmission) !== null && _e !== void 0 ? _e : "";
        message.linkDenom = (_f = object.linkDenom) !== null && _f !== void 0 ? _f : "";
        message.uniqueReports = (_g = object.uniqueReports) !== null && _g !== void 0 ? _g : false;
        message.description = (_h = object.description) !== null && _h !== void 0 ? _h : "";
        message.feedAdmin = (_j = object.feedAdmin) !== null && _j !== void 0 ? _j : "";
        message.billingAdmin = (_k = object.billingAdmin) !== null && _k !== void 0 ? _k : "";
        return message;
    },
};
function createBaseContractConfig() {
    return {
        configCount: "0",
        signers: [],
        transmitters: [],
        f: 0,
        onchainConfig: new Uint8Array(),
        offchainConfigVersion: "0",
        offchainConfig: new Uint8Array(),
    };
}
exports.ContractConfig = {
    encode: function (message, writer) {
        var e_3, _a, e_4, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.configCount !== "0") {
            writer.uint32(8).uint64(message.configCount);
        }
        try {
            for (var _c = __values(message.signers), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _e = __values(message.transmitters), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (message.f !== 0) {
            writer.uint32(32).uint32(message.f);
        }
        if (message.onchainConfig.length !== 0) {
            writer.uint32(42).bytes(message.onchainConfig);
        }
        if (message.offchainConfigVersion !== "0") {
            writer.uint32(48).uint64(message.offchainConfigVersion);
        }
        if (message.offchainConfig.length !== 0) {
            writer.uint32(58).bytes(message.offchainConfig);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseContractConfig();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.configCount = longToString(reader.uint64());
                    break;
                case 2:
                    message.signers.push(reader.string());
                    break;
                case 3:
                    message.transmitters.push(reader.string());
                    break;
                case 4:
                    message.f = reader.uint32();
                    break;
                case 5:
                    message.onchainConfig = reader.bytes();
                    break;
                case 6:
                    message.offchainConfigVersion = longToString(reader.uint64());
                    break;
                case 7:
                    message.offchainConfig = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            configCount: isSet(object.configCount) ? String(object.configCount) : "0",
            signers: Array.isArray(object === null || object === void 0 ? void 0 : object.signers) ? object.signers.map(function (e) { return String(e); }) : [],
            transmitters: Array.isArray(object === null || object === void 0 ? void 0 : object.transmitters) ? object.transmitters.map(function (e) { return String(e); }) : [],
            f: isSet(object.f) ? Number(object.f) : 0,
            onchainConfig: isSet(object.onchainConfig) ? bytesFromBase64(object.onchainConfig) : new Uint8Array(),
            offchainConfigVersion: isSet(object.offchainConfigVersion) ? String(object.offchainConfigVersion) : "0",
            offchainConfig: isSet(object.offchainConfig) ? bytesFromBase64(object.offchainConfig) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.configCount !== undefined && (obj.configCount = message.configCount);
        if (message.signers) {
            obj.signers = message.signers.map(function (e) { return e; });
        }
        else {
            obj.signers = [];
        }
        if (message.transmitters) {
            obj.transmitters = message.transmitters.map(function (e) { return e; });
        }
        else {
            obj.transmitters = [];
        }
        message.f !== undefined && (obj.f = Math.round(message.f));
        message.onchainConfig !== undefined &&
            (obj.onchainConfig = base64FromBytes(message.onchainConfig !== undefined ? message.onchainConfig : new Uint8Array()));
        message.offchainConfigVersion !== undefined && (obj.offchainConfigVersion = message.offchainConfigVersion);
        message.offchainConfig !== undefined &&
            (obj.offchainConfig = base64FromBytes(message.offchainConfig !== undefined ? message.offchainConfig : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ContractConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseContractConfig();
        message.configCount = (_a = object.configCount) !== null && _a !== void 0 ? _a : "0";
        message.signers = ((_b = object.signers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.transmitters = ((_c = object.transmitters) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.f = (_d = object.f) !== null && _d !== void 0 ? _d : 0;
        message.onchainConfig = (_e = object.onchainConfig) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.offchainConfigVersion = (_f = object.offchainConfigVersion) !== null && _f !== void 0 ? _f : "0";
        message.offchainConfig = (_g = object.offchainConfig) !== null && _g !== void 0 ? _g : new Uint8Array();
        return message;
    },
};
function createBaseSetConfigProposal() {
    return { title: "", description: "", config: undefined };
}
exports.SetConfigProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.config !== undefined) {
            exports.FeedConfig.encode(message.config, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetConfigProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.config = exports.FeedConfig.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            config: isSet(object.config) ? exports.FeedConfig.fromJSON(object.config) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.config !== undefined && (obj.config = message.config ? exports.FeedConfig.toJSON(message.config) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.SetConfigProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseSetConfigProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.config = (object.config !== undefined && object.config !== null)
            ? exports.FeedConfig.fromPartial(object.config)
            : undefined;
        return message;
    },
};
function createBaseFeedProperties() {
    return {
        feedId: "",
        f: 0,
        onchainConfig: new Uint8Array(),
        offchainConfigVersion: "0",
        offchainConfig: new Uint8Array(),
        minAnswer: "",
        maxAnswer: "",
        linkPerObservation: "",
        linkPerTransmission: "",
        uniqueReports: false,
        description: "",
    };
}
exports.FeedProperties = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.f !== 0) {
            writer.uint32(16).uint32(message.f);
        }
        if (message.onchainConfig.length !== 0) {
            writer.uint32(26).bytes(message.onchainConfig);
        }
        if (message.offchainConfigVersion !== "0") {
            writer.uint32(32).uint64(message.offchainConfigVersion);
        }
        if (message.offchainConfig.length !== 0) {
            writer.uint32(42).bytes(message.offchainConfig);
        }
        if (message.minAnswer !== "") {
            writer.uint32(50).string(message.minAnswer);
        }
        if (message.maxAnswer !== "") {
            writer.uint32(58).string(message.maxAnswer);
        }
        if (message.linkPerObservation !== "") {
            writer.uint32(66).string(message.linkPerObservation);
        }
        if (message.linkPerTransmission !== "") {
            writer.uint32(74).string(message.linkPerTransmission);
        }
        if (message.uniqueReports === true) {
            writer.uint32(80).bool(message.uniqueReports);
        }
        if (message.description !== "") {
            writer.uint32(90).string(message.description);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeedProperties();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.f = reader.uint32();
                    break;
                case 3:
                    message.onchainConfig = reader.bytes();
                    break;
                case 4:
                    message.offchainConfigVersion = longToString(reader.uint64());
                    break;
                case 5:
                    message.offchainConfig = reader.bytes();
                    break;
                case 6:
                    message.minAnswer = reader.string();
                    break;
                case 7:
                    message.maxAnswer = reader.string();
                    break;
                case 8:
                    message.linkPerObservation = reader.string();
                    break;
                case 9:
                    message.linkPerTransmission = reader.string();
                    break;
                case 10:
                    message.uniqueReports = reader.bool();
                    break;
                case 11:
                    message.description = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            f: isSet(object.f) ? Number(object.f) : 0,
            onchainConfig: isSet(object.onchainConfig) ? bytesFromBase64(object.onchainConfig) : new Uint8Array(),
            offchainConfigVersion: isSet(object.offchainConfigVersion) ? String(object.offchainConfigVersion) : "0",
            offchainConfig: isSet(object.offchainConfig) ? bytesFromBase64(object.offchainConfig) : new Uint8Array(),
            minAnswer: isSet(object.minAnswer) ? String(object.minAnswer) : "",
            maxAnswer: isSet(object.maxAnswer) ? String(object.maxAnswer) : "",
            linkPerObservation: isSet(object.linkPerObservation) ? String(object.linkPerObservation) : "",
            linkPerTransmission: isSet(object.linkPerTransmission) ? String(object.linkPerTransmission) : "",
            uniqueReports: isSet(object.uniqueReports) ? Boolean(object.uniqueReports) : false,
            description: isSet(object.description) ? String(object.description) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.f !== undefined && (obj.f = Math.round(message.f));
        message.onchainConfig !== undefined &&
            (obj.onchainConfig = base64FromBytes(message.onchainConfig !== undefined ? message.onchainConfig : new Uint8Array()));
        message.offchainConfigVersion !== undefined && (obj.offchainConfigVersion = message.offchainConfigVersion);
        message.offchainConfig !== undefined &&
            (obj.offchainConfig = base64FromBytes(message.offchainConfig !== undefined ? message.offchainConfig : new Uint8Array()));
        message.minAnswer !== undefined && (obj.minAnswer = message.minAnswer);
        message.maxAnswer !== undefined && (obj.maxAnswer = message.maxAnswer);
        message.linkPerObservation !== undefined && (obj.linkPerObservation = message.linkPerObservation);
        message.linkPerTransmission !== undefined && (obj.linkPerTransmission = message.linkPerTransmission);
        message.uniqueReports !== undefined && (obj.uniqueReports = message.uniqueReports);
        message.description !== undefined && (obj.description = message.description);
        return obj;
    },
    create: function (base) {
        return exports.FeedProperties.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
        var message = createBaseFeedProperties();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.f = (_b = object.f) !== null && _b !== void 0 ? _b : 0;
        message.onchainConfig = (_c = object.onchainConfig) !== null && _c !== void 0 ? _c : new Uint8Array();
        message.offchainConfigVersion = (_d = object.offchainConfigVersion) !== null && _d !== void 0 ? _d : "0";
        message.offchainConfig = (_e = object.offchainConfig) !== null && _e !== void 0 ? _e : new Uint8Array();
        message.minAnswer = (_f = object.minAnswer) !== null && _f !== void 0 ? _f : "";
        message.maxAnswer = (_g = object.maxAnswer) !== null && _g !== void 0 ? _g : "";
        message.linkPerObservation = (_h = object.linkPerObservation) !== null && _h !== void 0 ? _h : "";
        message.linkPerTransmission = (_j = object.linkPerTransmission) !== null && _j !== void 0 ? _j : "";
        message.uniqueReports = (_k = object.uniqueReports) !== null && _k !== void 0 ? _k : false;
        message.description = (_l = object.description) !== null && _l !== void 0 ? _l : "";
        return message;
    },
};
function createBaseSetBatchConfigProposal() {
    return { title: "", description: "", signers: [], transmitters: [], linkDenom: "", feedProperties: [] };
}
exports.SetBatchConfigProposal = {
    encode: function (message, writer) {
        var e_5, _a, e_6, _b, e_7, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _d = __values(message.signers), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _f = __values(message.transmitters), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_6) throw e_6.error; }
        }
        if (message.linkDenom !== "") {
            writer.uint32(42).string(message.linkDenom);
        }
        try {
            for (var _h = __values(message.feedProperties), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                exports.FeedProperties.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_7) throw e_7.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSetBatchConfigProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.signers.push(reader.string());
                    break;
                case 4:
                    message.transmitters.push(reader.string());
                    break;
                case 5:
                    message.linkDenom = reader.string();
                    break;
                case 6:
                    message.feedProperties.push(exports.FeedProperties.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            signers: Array.isArray(object === null || object === void 0 ? void 0 : object.signers) ? object.signers.map(function (e) { return String(e); }) : [],
            transmitters: Array.isArray(object === null || object === void 0 ? void 0 : object.transmitters) ? object.transmitters.map(function (e) { return String(e); }) : [],
            linkDenom: isSet(object.linkDenom) ? String(object.linkDenom) : "",
            feedProperties: Array.isArray(object === null || object === void 0 ? void 0 : object.feedProperties)
                ? object.feedProperties.map(function (e) { return exports.FeedProperties.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.signers) {
            obj.signers = message.signers.map(function (e) { return e; });
        }
        else {
            obj.signers = [];
        }
        if (message.transmitters) {
            obj.transmitters = message.transmitters.map(function (e) { return e; });
        }
        else {
            obj.transmitters = [];
        }
        message.linkDenom !== undefined && (obj.linkDenom = message.linkDenom);
        if (message.feedProperties) {
            obj.feedProperties = message.feedProperties.map(function (e) { return e ? exports.FeedProperties.toJSON(e) : undefined; });
        }
        else {
            obj.feedProperties = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.SetBatchConfigProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseSetBatchConfigProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.signers = ((_c = object.signers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.transmitters = ((_d = object.transmitters) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.linkDenom = (_e = object.linkDenom) !== null && _e !== void 0 ? _e : "";
        message.feedProperties = ((_f = object.feedProperties) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.FeedProperties.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseOracleObservationsCounts() {
    return { counts: [] };
}
exports.OracleObservationsCounts = {
    encode: function (message, writer) {
        var e_8, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        writer.uint32(10).fork();
        try {
            for (var _b = __values(message.counts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_8) throw e_8.error; }
        }
        writer.ldelim();
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOracleObservationsCounts();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    if ((tag & 7) === 2) {
                        var end2 = reader.uint32() + reader.pos;
                        while (reader.pos < end2) {
                            message.counts.push(reader.uint32());
                        }
                    }
                    else {
                        message.counts.push(reader.uint32());
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { counts: Array.isArray(object === null || object === void 0 ? void 0 : object.counts) ? object.counts.map(function (e) { return Number(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.counts) {
            obj.counts = message.counts.map(function (e) { return Math.round(e); });
        }
        else {
            obj.counts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.OracleObservationsCounts.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseOracleObservationsCounts();
        message.counts = ((_a = object.counts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseGasReimbursements() {
    return { reimbursements: [] };
}
exports.GasReimbursements = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.reimbursements), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                coin_1.Coin.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseGasReimbursements();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.reimbursements.push(coin_1.Coin.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            reimbursements: Array.isArray(object === null || object === void 0 ? void 0 : object.reimbursements)
                ? object.reimbursements.map(function (e) { return coin_1.Coin.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.reimbursements) {
            obj.reimbursements = message.reimbursements.map(function (e) { return e ? coin_1.Coin.toJSON(e) : undefined; });
        }
        else {
            obj.reimbursements = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.GasReimbursements.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseGasReimbursements();
        message.reimbursements = ((_a = object.reimbursements) === null || _a === void 0 ? void 0 : _a.map(function (e) { return coin_1.Coin.fromPartial(e); })) || [];
        return message;
    },
};
function createBasePayee() {
    return { transmitterAddr: "", paymentAddr: "" };
}
exports.Payee = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.transmitterAddr !== "") {
            writer.uint32(10).string(message.transmitterAddr);
        }
        if (message.paymentAddr !== "") {
            writer.uint32(18).string(message.paymentAddr);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePayee();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.transmitterAddr = reader.string();
                    break;
                case 2:
                    message.paymentAddr = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            transmitterAddr: isSet(object.transmitterAddr) ? String(object.transmitterAddr) : "",
            paymentAddr: isSet(object.paymentAddr) ? String(object.paymentAddr) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.transmitterAddr !== undefined && (obj.transmitterAddr = message.transmitterAddr);
        message.paymentAddr !== undefined && (obj.paymentAddr = message.paymentAddr);
        return obj;
    },
    create: function (base) {
        return exports.Payee.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePayee();
        message.transmitterAddr = (_a = object.transmitterAddr) !== null && _a !== void 0 ? _a : "";
        message.paymentAddr = (_b = object.paymentAddr) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTransmission() {
    return { answer: "", observationsTimestamp: "0", transmissionTimestamp: "0" };
}
exports.Transmission = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.answer !== "") {
            writer.uint32(10).string(message.answer);
        }
        if (message.observationsTimestamp !== "0") {
            writer.uint32(16).int64(message.observationsTimestamp);
        }
        if (message.transmissionTimestamp !== "0") {
            writer.uint32(24).int64(message.transmissionTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTransmission();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.answer = reader.string();
                    break;
                case 2:
                    message.observationsTimestamp = longToString(reader.int64());
                    break;
                case 3:
                    message.transmissionTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            answer: isSet(object.answer) ? String(object.answer) : "",
            observationsTimestamp: isSet(object.observationsTimestamp) ? String(object.observationsTimestamp) : "0",
            transmissionTimestamp: isSet(object.transmissionTimestamp) ? String(object.transmissionTimestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.answer !== undefined && (obj.answer = message.answer);
        message.observationsTimestamp !== undefined && (obj.observationsTimestamp = message.observationsTimestamp);
        message.transmissionTimestamp !== undefined && (obj.transmissionTimestamp = message.transmissionTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.Transmission.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTransmission();
        message.answer = (_a = object.answer) !== null && _a !== void 0 ? _a : "";
        message.observationsTimestamp = (_b = object.observationsTimestamp) !== null && _b !== void 0 ? _b : "0";
        message.transmissionTimestamp = (_c = object.transmissionTimestamp) !== null && _c !== void 0 ? _c : "0";
        return message;
    },
};
function createBaseEpochAndRound() {
    return { epoch: "0", round: "0" };
}
exports.EpochAndRound = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.epoch !== "0") {
            writer.uint32(8).uint64(message.epoch);
        }
        if (message.round !== "0") {
            writer.uint32(16).uint64(message.round);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEpochAndRound();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.epoch = longToString(reader.uint64());
                    break;
                case 2:
                    message.round = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            epoch: isSet(object.epoch) ? String(object.epoch) : "0",
            round: isSet(object.round) ? String(object.round) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.epoch !== undefined && (obj.epoch = message.epoch);
        message.round !== undefined && (obj.round = message.round);
        return obj;
    },
    create: function (base) {
        return exports.EpochAndRound.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEpochAndRound();
        message.epoch = (_a = object.epoch) !== null && _a !== void 0 ? _a : "0";
        message.round = (_b = object.round) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseReport() {
    return { observationsTimestamp: "0", observers: new Uint8Array(), observations: [] };
}
exports.Report = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.observationsTimestamp !== "0") {
            writer.uint32(8).int64(message.observationsTimestamp);
        }
        if (message.observers.length !== 0) {
            writer.uint32(18).bytes(message.observers);
        }
        try {
            for (var _b = __values(message.observations), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseReport();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.observationsTimestamp = longToString(reader.int64());
                    break;
                case 2:
                    message.observers = reader.bytes();
                    break;
                case 3:
                    message.observations.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            observationsTimestamp: isSet(object.observationsTimestamp) ? String(object.observationsTimestamp) : "0",
            observers: isSet(object.observers) ? bytesFromBase64(object.observers) : new Uint8Array(),
            observations: Array.isArray(object === null || object === void 0 ? void 0 : object.observations) ? object.observations.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.observationsTimestamp !== undefined && (obj.observationsTimestamp = message.observationsTimestamp);
        message.observers !== undefined &&
            (obj.observers = base64FromBytes(message.observers !== undefined ? message.observers : new Uint8Array()));
        if (message.observations) {
            obj.observations = message.observations.map(function (e) { return e; });
        }
        else {
            obj.observations = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Report.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseReport();
        message.observationsTimestamp = (_a = object.observationsTimestamp) !== null && _a !== void 0 ? _a : "0";
        message.observers = (_b = object.observers) !== null && _b !== void 0 ? _b : new Uint8Array();
        message.observations = ((_c = object.observations) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseReportToSign() {
    return {
        configDigest: new Uint8Array(),
        epoch: "0",
        round: "0",
        extraHash: new Uint8Array(),
        report: new Uint8Array(),
    };
}
exports.ReportToSign = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.configDigest.length !== 0) {
            writer.uint32(10).bytes(message.configDigest);
        }
        if (message.epoch !== "0") {
            writer.uint32(16).uint64(message.epoch);
        }
        if (message.round !== "0") {
            writer.uint32(24).uint64(message.round);
        }
        if (message.extraHash.length !== 0) {
            writer.uint32(34).bytes(message.extraHash);
        }
        if (message.report.length !== 0) {
            writer.uint32(42).bytes(message.report);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseReportToSign();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.configDigest = reader.bytes();
                    break;
                case 2:
                    message.epoch = longToString(reader.uint64());
                    break;
                case 3:
                    message.round = longToString(reader.uint64());
                    break;
                case 4:
                    message.extraHash = reader.bytes();
                    break;
                case 5:
                    message.report = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            configDigest: isSet(object.configDigest) ? bytesFromBase64(object.configDigest) : new Uint8Array(),
            epoch: isSet(object.epoch) ? String(object.epoch) : "0",
            round: isSet(object.round) ? String(object.round) : "0",
            extraHash: isSet(object.extraHash) ? bytesFromBase64(object.extraHash) : new Uint8Array(),
            report: isSet(object.report) ? bytesFromBase64(object.report) : new Uint8Array(),
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.configDigest !== undefined &&
            (obj.configDigest = base64FromBytes(message.configDigest !== undefined ? message.configDigest : new Uint8Array()));
        message.epoch !== undefined && (obj.epoch = message.epoch);
        message.round !== undefined && (obj.round = message.round);
        message.extraHash !== undefined &&
            (obj.extraHash = base64FromBytes(message.extraHash !== undefined ? message.extraHash : new Uint8Array()));
        message.report !== undefined &&
            (obj.report = base64FromBytes(message.report !== undefined ? message.report : new Uint8Array()));
        return obj;
    },
    create: function (base) {
        return exports.ReportToSign.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseReportToSign();
        message.configDigest = (_a = object.configDigest) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.epoch = (_b = object.epoch) !== null && _b !== void 0 ? _b : "0";
        message.round = (_c = object.round) !== null && _c !== void 0 ? _c : "0";
        message.extraHash = (_d = object.extraHash) !== null && _d !== void 0 ? _d : new Uint8Array();
        message.report = (_e = object.report) !== null && _e !== void 0 ? _e : new Uint8Array();
        return message;
    },
};
function createBaseEventOraclePaid() {
    return { transmitterAddr: "", payeeAddr: "", amount: undefined };
}
exports.EventOraclePaid = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.transmitterAddr !== "") {
            writer.uint32(10).string(message.transmitterAddr);
        }
        if (message.payeeAddr !== "") {
            writer.uint32(18).string(message.payeeAddr);
        }
        if (message.amount !== undefined) {
            coin_1.Coin.encode(message.amount, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventOraclePaid();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.transmitterAddr = reader.string();
                    break;
                case 2:
                    message.payeeAddr = reader.string();
                    break;
                case 3:
                    message.amount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            transmitterAddr: isSet(object.transmitterAddr) ? String(object.transmitterAddr) : "",
            payeeAddr: isSet(object.payeeAddr) ? String(object.payeeAddr) : "",
            amount: isSet(object.amount) ? coin_1.Coin.fromJSON(object.amount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.transmitterAddr !== undefined && (obj.transmitterAddr = message.transmitterAddr);
        message.payeeAddr !== undefined && (obj.payeeAddr = message.payeeAddr);
        message.amount !== undefined && (obj.amount = message.amount ? coin_1.Coin.toJSON(message.amount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventOraclePaid.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventOraclePaid();
        message.transmitterAddr = (_a = object.transmitterAddr) !== null && _a !== void 0 ? _a : "";
        message.payeeAddr = (_b = object.payeeAddr) !== null && _b !== void 0 ? _b : "";
        message.amount = (object.amount !== undefined && object.amount !== null)
            ? coin_1.Coin.fromPartial(object.amount)
            : undefined;
        return message;
    },
};
function createBaseEventAnswerUpdated() {
    return { current: "", roundId: "", updatedAt: undefined };
}
exports.EventAnswerUpdated = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.current !== "") {
            writer.uint32(10).string(message.current);
        }
        if (message.roundId !== "") {
            writer.uint32(18).string(message.roundId);
        }
        if (message.updatedAt !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.updatedAt), writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventAnswerUpdated();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.current = reader.string();
                    break;
                case 2:
                    message.roundId = reader.string();
                    break;
                case 3:
                    message.updatedAt = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            current: isSet(object.current) ? String(object.current) : "",
            roundId: isSet(object.roundId) ? String(object.roundId) : "",
            updatedAt: isSet(object.updatedAt) ? fromJsonTimestamp(object.updatedAt) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.current !== undefined && (obj.current = message.current);
        message.roundId !== undefined && (obj.roundId = message.roundId);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt.toISOString());
        return obj;
    },
    create: function (base) {
        return exports.EventAnswerUpdated.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventAnswerUpdated();
        message.current = (_a = object.current) !== null && _a !== void 0 ? _a : "";
        message.roundId = (_b = object.roundId) !== null && _b !== void 0 ? _b : "";
        message.updatedAt = (_c = object.updatedAt) !== null && _c !== void 0 ? _c : undefined;
        return message;
    },
};
function createBaseEventNewRound() {
    return { roundId: "", startedBy: "", startedAt: undefined };
}
exports.EventNewRound = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.roundId !== "") {
            writer.uint32(10).string(message.roundId);
        }
        if (message.startedBy !== "") {
            writer.uint32(18).string(message.startedBy);
        }
        if (message.startedAt !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.startedAt), writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNewRound();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.roundId = reader.string();
                    break;
                case 2:
                    message.startedBy = reader.string();
                    break;
                case 3:
                    message.startedAt = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            roundId: isSet(object.roundId) ? String(object.roundId) : "",
            startedBy: isSet(object.startedBy) ? String(object.startedBy) : "",
            startedAt: isSet(object.startedAt) ? fromJsonTimestamp(object.startedAt) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.roundId !== undefined && (obj.roundId = message.roundId);
        message.startedBy !== undefined && (obj.startedBy = message.startedBy);
        message.startedAt !== undefined && (obj.startedAt = message.startedAt.toISOString());
        return obj;
    },
    create: function (base) {
        return exports.EventNewRound.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseEventNewRound();
        message.roundId = (_a = object.roundId) !== null && _a !== void 0 ? _a : "";
        message.startedBy = (_b = object.startedBy) !== null && _b !== void 0 ? _b : "";
        message.startedAt = (_c = object.startedAt) !== null && _c !== void 0 ? _c : undefined;
        return message;
    },
};
function createBaseEventTransmitted() {
    return { configDigest: new Uint8Array(), epoch: "0" };
}
exports.EventTransmitted = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.configDigest.length !== 0) {
            writer.uint32(10).bytes(message.configDigest);
        }
        if (message.epoch !== "0") {
            writer.uint32(16).uint64(message.epoch);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventTransmitted();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.configDigest = reader.bytes();
                    break;
                case 2:
                    message.epoch = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            configDigest: isSet(object.configDigest) ? bytesFromBase64(object.configDigest) : new Uint8Array(),
            epoch: isSet(object.epoch) ? String(object.epoch) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.configDigest !== undefined &&
            (obj.configDigest = base64FromBytes(message.configDigest !== undefined ? message.configDigest : new Uint8Array()));
        message.epoch !== undefined && (obj.epoch = message.epoch);
        return obj;
    },
    create: function (base) {
        return exports.EventTransmitted.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventTransmitted();
        message.configDigest = (_a = object.configDigest) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.epoch = (_b = object.epoch) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseEventNewTransmission() {
    return {
        feedId: "",
        aggregatorRoundId: 0,
        answer: "",
        transmitter: "",
        observationsTimestamp: "0",
        observations: [],
        observers: new Uint8Array(),
        configDigest: new Uint8Array(),
        epochAndRound: undefined,
    };
}
exports.EventNewTransmission = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feedId !== "") {
            writer.uint32(10).string(message.feedId);
        }
        if (message.aggregatorRoundId !== 0) {
            writer.uint32(16).uint32(message.aggregatorRoundId);
        }
        if (message.answer !== "") {
            writer.uint32(26).string(message.answer);
        }
        if (message.transmitter !== "") {
            writer.uint32(34).string(message.transmitter);
        }
        if (message.observationsTimestamp !== "0") {
            writer.uint32(40).int64(message.observationsTimestamp);
        }
        try {
            for (var _b = __values(message.observations), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(50).string(v);
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        if (message.observers.length !== 0) {
            writer.uint32(58).bytes(message.observers);
        }
        if (message.configDigest.length !== 0) {
            writer.uint32(66).bytes(message.configDigest);
        }
        if (message.epochAndRound !== undefined) {
            exports.EpochAndRound.encode(message.epochAndRound, writer.uint32(74).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventNewTransmission();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feedId = reader.string();
                    break;
                case 2:
                    message.aggregatorRoundId = reader.uint32();
                    break;
                case 3:
                    message.answer = reader.string();
                    break;
                case 4:
                    message.transmitter = reader.string();
                    break;
                case 5:
                    message.observationsTimestamp = longToString(reader.int64());
                    break;
                case 6:
                    message.observations.push(reader.string());
                    break;
                case 7:
                    message.observers = reader.bytes();
                    break;
                case 8:
                    message.configDigest = reader.bytes();
                    break;
                case 9:
                    message.epochAndRound = exports.EpochAndRound.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feedId: isSet(object.feedId) ? String(object.feedId) : "",
            aggregatorRoundId: isSet(object.aggregatorRoundId) ? Number(object.aggregatorRoundId) : 0,
            answer: isSet(object.answer) ? String(object.answer) : "",
            transmitter: isSet(object.transmitter) ? String(object.transmitter) : "",
            observationsTimestamp: isSet(object.observationsTimestamp) ? String(object.observationsTimestamp) : "0",
            observations: Array.isArray(object === null || object === void 0 ? void 0 : object.observations) ? object.observations.map(function (e) { return String(e); }) : [],
            observers: isSet(object.observers) ? bytesFromBase64(object.observers) : new Uint8Array(),
            configDigest: isSet(object.configDigest) ? bytesFromBase64(object.configDigest) : new Uint8Array(),
            epochAndRound: isSet(object.epochAndRound) ? exports.EpochAndRound.fromJSON(object.epochAndRound) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feedId !== undefined && (obj.feedId = message.feedId);
        message.aggregatorRoundId !== undefined && (obj.aggregatorRoundId = Math.round(message.aggregatorRoundId));
        message.answer !== undefined && (obj.answer = message.answer);
        message.transmitter !== undefined && (obj.transmitter = message.transmitter);
        message.observationsTimestamp !== undefined && (obj.observationsTimestamp = message.observationsTimestamp);
        if (message.observations) {
            obj.observations = message.observations.map(function (e) { return e; });
        }
        else {
            obj.observations = [];
        }
        message.observers !== undefined &&
            (obj.observers = base64FromBytes(message.observers !== undefined ? message.observers : new Uint8Array()));
        message.configDigest !== undefined &&
            (obj.configDigest = base64FromBytes(message.configDigest !== undefined ? message.configDigest : new Uint8Array()));
        message.epochAndRound !== undefined &&
            (obj.epochAndRound = message.epochAndRound ? exports.EpochAndRound.toJSON(message.epochAndRound) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventNewTransmission.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseEventNewTransmission();
        message.feedId = (_a = object.feedId) !== null && _a !== void 0 ? _a : "";
        message.aggregatorRoundId = (_b = object.aggregatorRoundId) !== null && _b !== void 0 ? _b : 0;
        message.answer = (_c = object.answer) !== null && _c !== void 0 ? _c : "";
        message.transmitter = (_d = object.transmitter) !== null && _d !== void 0 ? _d : "";
        message.observationsTimestamp = (_e = object.observationsTimestamp) !== null && _e !== void 0 ? _e : "0";
        message.observations = ((_f = object.observations) === null || _f === void 0 ? void 0 : _f.map(function (e) { return e; })) || [];
        message.observers = (_g = object.observers) !== null && _g !== void 0 ? _g : new Uint8Array();
        message.configDigest = (_h = object.configDigest) !== null && _h !== void 0 ? _h : new Uint8Array();
        message.epochAndRound = (object.epochAndRound !== undefined && object.epochAndRound !== null)
            ? exports.EpochAndRound.fromPartial(object.epochAndRound)
            : undefined;
        return message;
    },
};
function createBaseEventConfigSet() {
    return { configDigest: new Uint8Array(), previousConfigBlockNumber: "0", config: undefined, configInfo: undefined };
}
exports.EventConfigSet = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.configDigest.length !== 0) {
            writer.uint32(10).bytes(message.configDigest);
        }
        if (message.previousConfigBlockNumber !== "0") {
            writer.uint32(16).int64(message.previousConfigBlockNumber);
        }
        if (message.config !== undefined) {
            exports.FeedConfig.encode(message.config, writer.uint32(26).fork()).ldelim();
        }
        if (message.configInfo !== undefined) {
            exports.FeedConfigInfo.encode(message.configInfo, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventConfigSet();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.configDigest = reader.bytes();
                    break;
                case 2:
                    message.previousConfigBlockNumber = longToString(reader.int64());
                    break;
                case 3:
                    message.config = exports.FeedConfig.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.configInfo = exports.FeedConfigInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            configDigest: isSet(object.configDigest) ? bytesFromBase64(object.configDigest) : new Uint8Array(),
            previousConfigBlockNumber: isSet(object.previousConfigBlockNumber)
                ? String(object.previousConfigBlockNumber)
                : "0",
            config: isSet(object.config) ? exports.FeedConfig.fromJSON(object.config) : undefined,
            configInfo: isSet(object.configInfo) ? exports.FeedConfigInfo.fromJSON(object.configInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.configDigest !== undefined &&
            (obj.configDigest = base64FromBytes(message.configDigest !== undefined ? message.configDigest : new Uint8Array()));
        message.previousConfigBlockNumber !== undefined &&
            (obj.previousConfigBlockNumber = message.previousConfigBlockNumber);
        message.config !== undefined && (obj.config = message.config ? exports.FeedConfig.toJSON(message.config) : undefined);
        message.configInfo !== undefined &&
            (obj.configInfo = message.configInfo ? exports.FeedConfigInfo.toJSON(message.configInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventConfigSet.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventConfigSet();
        message.configDigest = (_a = object.configDigest) !== null && _a !== void 0 ? _a : new Uint8Array();
        message.previousConfigBlockNumber = (_b = object.previousConfigBlockNumber) !== null && _b !== void 0 ? _b : "0";
        message.config = (object.config !== undefined && object.config !== null)
            ? exports.FeedConfig.fromPartial(object.config)
            : undefined;
        message.configInfo = (object.configInfo !== undefined && object.configInfo !== null)
            ? exports.FeedConfigInfo.fromPartial(object.configInfo)
            : undefined;
        return message;
    },
};
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        var bin = tsProtoGlobalThis.atob(b64);
        var arr = new Uint8Array(bin.length);
        for (var i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        var bin_1 = [];
        arr.forEach(function (byte) {
            bin_1.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin_1.join(""));
    }
}
function toTimestamp(date) {
    var seconds = Math.trunc(date.getTime() / 1000).toString();
    var nanos = (date.getTime() % 1000) * 1000000;
    return { seconds: seconds, nanos: nanos };
}
function fromTimestamp(t) {
    var millis = Number(t.seconds) * 1000;
    millis += t.nanos / 1000000;
    return new Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
