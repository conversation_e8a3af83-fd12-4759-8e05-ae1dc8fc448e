"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateArbitrarySignDoc = void 0;
const helpers_js_1 = require("../../utils/helpers.js");
const utf8_js_1 = require("../../utils/utf8.js");
const generateArbitrarySignDoc = (message, signer) => {
    const signDoc = {
        account_number: '0',
        chain_id: '',
        fee: {
            amount: [],
            gas: '0',
        },
        memo: '',
        msgs: [
            {
                type: 'sign/MsgSignData',
                value: {
                    data: Buffer.from((0, utf8_js_1.toUtf8)(message)).toString('base64'),
                    signer: signer,
                },
            },
        ],
        sequence: '0',
    };
    const stringified = (0, utf8_js_1.toUtf8)(JSON.stringify((0, helpers_js_1.sortObjectByKeys)(signDoc)));
    return {
        signDoc,
        signDocBuff: Buffer.from(stringified),
        stringifiedSignDoc: stringified,
    };
};
exports.generateArbitrarySignDoc = generateArbitrarySignDoc;
