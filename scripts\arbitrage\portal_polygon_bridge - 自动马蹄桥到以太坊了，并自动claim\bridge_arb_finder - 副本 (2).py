#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Polygon和以太坊网络间套利机会查找脚本
使用zero_tx_tokens.json中的数据和KyberSwap进行路由
"""

import os
import sys
import json
import asyncio
import time
import logging
import concurrent.futures
import random
from typing import Dict, List, Tuple, Any, Optional, Callable
from datetime import datetime
import pandas as pd
import threading
import aiohttp
import yaml
import requests  # 导入同步请求库，避免Windows上的aiodns问题

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.dex.KyberSwap.client import KyberSwapClient

# 为旧版Python提供asyncio.to_thread的兼容性实现
if not hasattr(asyncio, 'to_thread'):
    def to_thread(func: Callable, *args, **kwargs):
        """在线程池中运行函数并返回协程"""
        loop = asyncio.get_event_loop()
        return loop.run_in_executor(None, lambda: func(*args, **kwargs))
    
    asyncio.to_thread = to_thread

# 常量定义
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
DATA_PATH = os.path.join(PROJECT_ROOT, "src", "bridge", "pol_bridge", "data")
TOKENS_FILE = os.path.join(DATA_PATH, "zero_tx_tokens.json")
RESULTS_DIR = os.path.join(os.path.dirname(__file__), "results")
MIN_USDT_AMOUNT = 8
MAX_USDT_AMOUNT = 300
DEFAULT_NUM_WORKERS = 18  # 默认工作线程数

# 稳定币地址
ETH_USDT_ADDRESS = "******************************************"
POLYGON_USDT_ADDRESS = "******************************************"

# 确保结果目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)

# 配置日志
def setup_logging(log_name="arb_finder"):
    """设置日志配置，使用固定文件名并管理文件大小"""
    log_file = os.path.join(RESULTS_DIR, f"{log_name}.log")
    
    # 检查现有日志文件大小
    if os.path.exists(log_file):
        file_size = os.path.getsize(log_file)
        if file_size > 18 * 1024 * 1024:  # 18MB
            print(f"日志文件{log_name}.log大小超过18MB，删除一半旧数据")
            try:
                # 读取现有日志
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 保留后半部分（较新的日志）
                new_lines = lines[len(lines)//2:]
                
                # 写入新内容
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.writelines(new_lines)
                
                print(f"已清理日志文件{log_name}.log，保留 {len(new_lines)} 行")
            except Exception as e:
                print(f"清理日志文件{log_name}.log失败: {str(e)}")
    
    # 获取日志记录器
    logger = logging.getLogger(log_name)
    
    # 如果已经配置过这个记录器，直接返回
    if hasattr(logger, '_configured') and logger._configured:
        return logger
        
    # 设置日志级别
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器，避免重复
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 添加文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 防止日志冒泡传递到根日志记录器
    logger.propagate = False
    
    # 标记为已配置
    logger._configured = True
    
    return logger

# 初始化日志 - 只初始化一次
if not hasattr(sys.modules[__name__], 'logger'):
    logger = setup_logging("arb_finder")
    # 创建二次分析专用的日志
    secondary_logger = setup_logging("secondary_analysis")

# 禁用KyberSwap client的详细日志
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("web3").setLevel(logging.WARNING)

# 新增gas费用计算相关配置
GAS_COSTS = {
    "polygon_to_ethereum": 260000,  # 当交易方向为polygon->以太坊时的gas常量
    "ethereum_to_polygon": 100000,   # 当交易方向为以太坊->polygon时的gas常量
}

class BridgeArbFinder:
    def __init__(self, num_workers: int = DEFAULT_NUM_WORKERS, profit_threshold: float = 0.0, config_path: str = None):
        """
        初始化跨链桥套利查找器
        
        Args:
            num_workers: 并行工作线程数量
            profit_threshold: 利润率阈值（百分比），设置为0时表示记录所有有正利润的机会
            config_path: 配置文件路径，如果提供则覆盖其他参数
        """
        # 初始化基本参数
        self.num_workers = num_workers
        self.profit_threshold = profit_threshold
        
        # 创建配置字典 - 确保config属性总是存在
        self.config = {
            "wallet_address": "******************************************",  # 默认钱包地址
            "eth_usdt_address": ETH_USDT_ADDRESS,
            "polygon_usdt_address": POLYGON_USDT_ADDRESS,
            "proxies": None
        }
        
        # 加载配置文件（如果提供）
        if config_path:
            self.load_config(config_path)
        else:
            # 设置默认参数
            self.min_usdt_amount = MIN_USDT_AMOUNT
            self.max_usdt_amount = MAX_USDT_AMOUNT
        
        # 加载代币数据
        self.tokens_data = self._load_tokens_data()
        self.opportunities = []
        
        # 进度跟踪
        self.processed_count = 0
        self.total_count = len(self.tokens_data)
        
        # 创建API客户端
        logger.info("初始化API客户端...")
        self.eth_client = KyberSwapClient(chain="ethereum")
        self.polygon_client = KyberSwapClient(chain="polygon")
        
        # 初始化ETH价格缓存
        self.eth_price_usd_cache = None
        self.eth_price_last_updated = 0
        self.eth_price_cache_duration = 600  # 10分钟
        
        # 初始化Gas价格缓存
        self.eth_gas_price_wei_cache = None
        self.polygon_gas_price_wei_cache = None
        self.eth_gas_price_last_updated = 0
        self.polygon_gas_price_last_updated = 0
        self.gas_price_cache_duration = 10  # 10秒缓存
        
        # 添加线程安全锁
        self.lock = threading.Lock()  # 保留原有锁
        self.eth_price_lock = threading.Lock()
        self.matic_price_lock = threading.Lock()
        self.web3_eth_lock = threading.Lock()
        self.web3_polygon_lock = threading.Lock()
        self.gas_price_lock = threading.Lock()
        self.log_lock = threading.Lock()  # 日志锁
        
        # 记录客户端重置时间
        self.last_client_reset_time = 0
        
        # 初始化进度队列，防止process_token_sync方法中引用错误
        self.progress_queue = None
        
        # 日志收集字典，用于按代币整理日志
        self.token_logs = {}
        
        logger.info(f"初始化完成，共加载 {self.total_count} 个代币")
        logger.info(f"设置利润率阈值为 {self.profit_threshold}%，工作线程数量为 {self.num_workers}")
        
    def _load_tokens_data(self) -> Dict:
        """加载token数据"""
        try:
            with open(TOKENS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 筛选出同时有ETH地址的代币
                filtered_data = {addr: info for addr, info in data.items() if "eth_address" in info}
                logger.info(f"已加载 {len(filtered_data)}/{len(data)} 个有ETH地址的代币数据")
                return filtered_data
        except Exception as e:
            logger.error(f"加载token数据失败: {str(e)}")
            return {}
    
    def get_gas_price_wei(self, chain: str) -> int:
        """
        获取给定链的Gas价格，使用缓存减少API请求
        
        Args:
            chain: 链名称 (ethereum/polygon)
            
        Returns:
            int: Gas价格(wei)
        """
        current_time = time.time()
        
        with self.gas_price_lock:
            if chain == "ethereum":
                # 检查ETH Gas价格缓存是否有效
                if (self.eth_gas_price_wei_cache is not None and 
                    current_time - self.eth_gas_price_last_updated < self.gas_price_cache_duration):
                    return self.eth_gas_price_wei_cache
                
                # 尝试获取ETH Gas价格
                gas_price_wei = None
                try:
                    # 首先尝试从Etherscan获取
                    etherscan_url = "https://api.etherscan.com/api?module=gastracker&action=gasoracle"
                    response = requests.get(etherscan_url, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("status") == "1" and "result" in data:
                            gas_price_gwei = float(data["result"]["ProposeGasPrice"])
                            gas_price_wei = int(gas_price_gwei * 10**9)
                            logger.info(f"从Etherscan获取当前以太坊Gas价格: {gas_price_gwei} Gwei")
                except Exception as e:
                    logger.warning(f"从Etherscan获取Gas价格失败: {str(e)}")
                
                # 如果Etherscan失败，尝试使用Web3
                if gas_price_wei is None:
                    try:
                        gas_price_wei = self.eth_client.web3.eth.gas_price
                        logger.info(f"从Web3获取以太坊Gas价格: {gas_price_wei / 10**9} Gwei")
                    except Exception as e:
                        logger.warning(f"从Web3获取以太坊Gas价格失败: {str(e)}")
                
                # 如果所有方法都失败，使用默认值
                if gas_price_wei is None:
                    gas_price_wei = 30 * 10**9  # 默认30 Gwei
                    logger.warning(f"使用默认以太坊Gas价格: {gas_price_wei / 10**9} Gwei")
                
                # 更新缓存
                self.eth_gas_price_wei_cache = gas_price_wei
                self.eth_gas_price_last_updated = current_time
                return gas_price_wei
                
            else:  # polygon
                # 检查Polygon Gas价格缓存是否有效
                if (self.polygon_gas_price_wei_cache is not None and 
                    current_time - self.polygon_gas_price_last_updated < self.gas_price_cache_duration):
                    return self.polygon_gas_price_wei_cache
                
                # 尝试获取Polygon Gas价格
                gas_price_wei = None
                try:
                    gas_price_wei = self.polygon_client.web3.eth.gas_price
                    logger.info(f"从Web3获取Polygon Gas价格: {gas_price_wei / 10**9} Gwei")
                except Exception as e:
                    logger.warning(f"从Web3获取Polygon Gas价格失败: {str(e)}")
                
                # 如果失败，使用默认值
                if gas_price_wei is None:
                    gas_price_wei = 50 * 10**9  # 默认50 Gwei
                    logger.warning(f"使用默认Polygon Gas价格: {gas_price_wei / 10**9} Gwei")
                
                # 更新缓存
                self.polygon_gas_price_wei_cache = gas_price_wei
                self.polygon_gas_price_last_updated = current_time
                return gas_price_wei
    
    async def check_token_price(self, token_address: str, chain: str, amount: float = 1.0) -> Dict:
        """
        检查代币价格
        
        Args:
            token_address: 代币地址
            chain: 链名称 (polygon 或 ethereum)
            amount: 代币数量
            
        Returns:
            Dict: 价格数据
        """
        # 随机延迟0.2-0.5秒，减轻API限流问题但不过度延迟
        await asyncio.sleep(0.2 + random.random() * 0.3)
        
        try:
            # 确保地址是小写的
            token_address = token_address.lower()
            
            # 选择对应链的客户端
            if chain == "polygon":
                client = self.polygon_client
                usdt_address = POLYGON_USDT_ADDRESS
            else:
                client = self.eth_client
                usdt_address = ETH_USDT_ADDRESS
                
            if not client:
                return {
                    "success": False,
                    "error": f"未初始化{chain}客户端"
                }
            
            # 查询代币精度
            decimals = self.get_token_decimals(token_address, chain)
            logger.info(f"{chain}链上代币{token_address}的精度为: {decimals}")
            amount_wei = str(int(amount * (10 ** decimals)))
            
            # 获取路由 - 默认排除bebop路由
            routes_data = await client.get_routes(
                token_in=token_address,
                token_out=usdt_address,
                amount_in=amount_wei,
                slippage=1.0,
                is_native_in=False,
                save_gas=True,
                excluded_sources="bebop"  # 默认排除bebop路由
            )
            
            if "error" in routes_data:
                return {
                    "success": False,
                    "error": routes_data["error"]
                }
                
            # 适配KyberSwap API v1格式 - 从data字段中获取routeSummary
            if "data" in routes_data and "routeSummary" in routes_data["data"]:
                route_summary = routes_data["data"]["routeSummary"]
            elif "routeSummary" in routes_data:  # 向后兼容旧格式
                route_summary = routes_data["routeSummary"]
            else:
                return {
                    "success": False,
                    "error": "无可用路由"
                }
            
            usdt_decimals = 6  # USDT精度
            
            # 计算输出金额
            output_amount = int(route_summary["amountOut"])
            output_formatted = output_amount / (10 ** usdt_decimals)
            
            # 计算价格影响
            price_impact = 0
            if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
                amount_in_usd = float(route_summary['amountInUsd'])
                amount_out_usd = float(route_summary['amountOutUsd'])
                if amount_in_usd > 0:
                    price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
            
            # 获取预估gas用量
            estimated_gas = int(route_summary.get("gas", 200000))  # 默认值200000
            
            # 获取当前gas价格
            gas_price_wei = self.get_gas_price_wei(chain)
            
            # 计算基础Gas成本 (Wei)
            gas_cost_wei = estimated_gas * gas_price_wei
            
            # 获取ETH/MATIC价格
            if chain == "ethereum":
                native_token_price_usd = self.get_eth_price_usd()
            else:  # polygon
                native_token_price_usd = self.get_matic_price_usd()
                
            # 计算Gas成本 (USD)，并应用1.4倍安全系数
            base_gas_cost_usd = (gas_cost_wei / 10**18) * native_token_price_usd
            gas_cost_usd = base_gas_cost_usd * 1.4
            
            return {
                "success": True,
                "price_per_token": output_formatted / amount if amount > 0 else 0,
                "token_amount": amount,
                "token_out_amount": output_formatted,
                "price_impact_percent": price_impact,
                "gas_price_gwei": gas_price_wei / 10**9,
                "gas_used_estimate": estimated_gas,
                "native_token_price_usd": native_token_price_usd,
                "base_gas_cost_usd": base_gas_cost_usd,
                "final_gas_cost_usd": gas_cost_usd
            }
            
        except Exception as e:
            error_str = str(e)
            logger.error(f"检查{chain}上{token_address}价格时发生错误: {error_str}")
            
            return {
                "success": False,
                "error": error_str
            }
    
    async def check_usdt_to_token(self, token_address: str, chain: str, usdt_amount: float) -> Dict:
        """
        检查使用USDT购买指定代币的价格
        
        Args:
            token_address: 代币地址
            chain: 链名称 (ethereum/polygon)
            usdt_amount: USDT数量
            
        Returns:
            Dict: 价格信息
        """
        try:
            # 添加详细日志，记录真正使用的代币地址和链
            logger.info(f"check_usdt_to_token: 在{chain}链上购买代币: {token_address}")
            
            client = self.eth_client if chain == "ethereum" else self.polygon_client
            usdt_address = ETH_USDT_ADDRESS if chain == "ethereum" else POLYGON_USDT_ADDRESS
            
            # USDT精度为6
            usdt_decimals = 6
            usdt_amount_wei = str(int(usdt_amount * (10 ** usdt_decimals)))
            
            # 获取目标代币精度
            token_decimals = self.get_token_decimals(token_address, chain)
            logger.info(f"{chain}链上代币{token_address}的精度为: {token_decimals}")
            
            # 获取路由 - 默认排除bebop路由
            routes_data = await client.get_routes(
                token_in=usdt_address,
                token_out=token_address,
                amount_in=usdt_amount_wei,
                slippage=1.0,
                is_native_in=False,
                save_gas=True,
                excluded_sources="bebop"  # 默认排除bebop路由
            )
            
            if "error" in routes_data:
                return {
                    "success": False,
                    "error": routes_data["error"],
                    "chain": chain,
                    "token_address": token_address,
                    "usdt_amount": usdt_amount
                }
                
            # 适配KyberSwap API v1格式 - 从data字段中获取routeSummary
            if "data" in routes_data and "routeSummary" in routes_data["data"]:
                route_summary = routes_data["data"]["routeSummary"]
            elif "routeSummary" in routes_data:  # 向后兼容旧格式
                route_summary = routes_data["routeSummary"]
            else:
                return {
                    "success": False,
                    "error": "无可用路由",
                    "chain": chain,
                    "token_address": token_address,
                    "usdt_amount": usdt_amount
                }
            
            # 计算输出金额 - 使用获取到的代币精度
            output_amount = int(route_summary["amountOut"])
            output_formatted = output_amount / (10 ** token_decimals)
            
            # 计算价格影响
            price_impact = 0
            if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
                amount_in_usd = float(route_summary['amountInUsd'])
                amount_out_usd = float(route_summary['amountOutUsd'])
                if amount_in_usd > 0:
                    price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
            
            # 获取预估gas用量
            estimated_gas = int(route_summary.get("gas", 200000))  # 默认值200000
            
            # 获取当前gas价格（使用缓存）
            gas_price_wei = self.get_gas_price_wei(chain)
            
            # 计算基础Gas成本 (Wei)
            gas_cost_wei = estimated_gas * gas_price_wei
            
            # 获取ETH/MATIC价格
            if chain == "ethereum":
                native_token_price_usd = self.get_eth_price_usd()
            else:  # polygon
                native_token_price_usd = self.get_matic_price_usd()
            
            # 计算Gas成本 (USD)，并应用1.4倍安全系数
            base_gas_cost_usd = (gas_cost_wei / 10**18) * native_token_price_usd
            gas_cost_usd = base_gas_cost_usd * 1.4
            
            logger.info(f"{chain}链上USDT → {token_address}: 估算gas: {estimated_gas}, gas价格: {gas_price_wei/10**9} Gwei")
            logger.info(f"基础Gas成本: ${base_gas_cost_usd:.4f}, 含1.4倍安全系数: ${gas_cost_usd:.4f}")
            
            return {
                "success": True,
                "chain": chain,
                "token_address": token_address,
                "usdt_amount": usdt_amount,
                "token_amount": output_formatted,
                "token_amount_raw": output_amount,
                "tokens_per_usdt": output_formatted / usdt_amount,
                "price_impact": price_impact,
                "gas": estimated_gas,
                "gas_price_wei": gas_price_wei,
                "base_gas_cost_usd": base_gas_cost_usd,
                "gas_cost_usd": gas_cost_usd
            }
            
        except Exception as e:
            logger.error(f"USDT兑换代币出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "chain": chain,
                "token_address": token_address,
                "usdt_amount": usdt_amount
            }
    
    async def check_token_to_usdt(self, token_address: str, chain: str, token_amount: float) -> Dict:
        """
        检查将指定量的代币兑换为USDT的价格
        
        Args:
            token_address: 代币地址
            chain: 链名称 (ethereum/polygon)
            token_amount: 代币数量
            
        Returns:
            Dict: 价格信息
        """
        try:
            client = self.eth_client if chain == "ethereum" else self.polygon_client
            usdt_address = ETH_USDT_ADDRESS if chain == "ethereum" else POLYGON_USDT_ADDRESS
            
            # 获取代币精度，不再使用固定值18
            decimals = self.get_token_decimals(token_address, chain)
            logger.info(f"{chain}链上代币{token_address}的精度为: {decimals}")
            
            # 将代币数量转换为Wei格式
            amount_wei = str(int(token_amount * (10 ** decimals)))
            
            # 获取路由 - 默认排除bebop路由
            routes_data = await client.get_routes(
                token_in=token_address,
                token_out=usdt_address,
                amount_in=amount_wei,
                slippage=1.0,
                is_native_in=False,
                save_gas=True,
                excluded_sources="bebop"  # 默认排除bebop路由
            )
            
            if "error" in routes_data:
                return {
                    "success": False,
                    "error": routes_data["error"],
                    "chain": chain,
                    "token_address": token_address,
                    "token_amount": token_amount
                }
                
            # 适配KyberSwap API v1格式 - 从data字段中获取routeSummary
            if "data" in routes_data and "routeSummary" in routes_data["data"]:
                route_summary = routes_data["data"]["routeSummary"]
            elif "routeSummary" in routes_data:  # 向后兼容旧格式
                route_summary = routes_data["routeSummary"]
            else:
                return {
                    "success": False,
                    "error": "无可用路由",
                    "chain": chain,
                    "token_address": token_address,
                    "token_amount": token_amount
                }
            
            usdt_decimals = 6  # USDT精度
            
            # 计算输出金额
            output_amount = int(route_summary["amountOut"])
            output_formatted = output_amount / (10 ** usdt_decimals)
            
            # 计算价格影响
            price_impact = 0
            if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
                amount_in_usd = float(route_summary['amountInUsd'])
                amount_out_usd = float(route_summary['amountOutUsd'])
                if amount_in_usd > 0:
                    price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
            
            # 获取预估gas用量
            estimated_gas = int(route_summary.get("gas", 200000))  # 默认值200000
            
            # 获取当前gas价格（使用缓存）
            gas_price_wei = self.get_gas_price_wei(chain)
            
            # 计算基础Gas成本 (Wei)
            gas_cost_wei = estimated_gas * gas_price_wei
            
            # 获取ETH/MATIC价格
            if chain == "ethereum":
                native_token_price_usd = self.get_eth_price_usd()
            else:  # polygon
                native_token_price_usd = self.get_matic_price_usd()
            
            # 计算Gas成本 (USD)，并应用1.4倍安全系数
            base_gas_cost_usd = (gas_cost_wei / 10**18) * native_token_price_usd
            gas_cost_usd = base_gas_cost_usd * 1.4
            
            logger.info(f"{chain}链上{token_address} → USDT: 估算gas: {estimated_gas}, gas价格: {gas_price_wei/10**9} Gwei")
            logger.info(f"基础Gas成本: ${base_gas_cost_usd:.4f}, 含1.4倍安全系数: ${gas_cost_usd:.4f}")
                
            # 如果gas成本超过预期输出的50%，给出警告但仍然认为是有效路由
            if gas_cost_usd > 0 and output_formatted > 0 and gas_cost_usd > output_formatted * 0.5:
                logger.warning(f"高Gas成本警告(卖出): ${gas_cost_usd:.4f} USDT (输出: ${output_formatted:.4f} USDT)")
            
            return {
                "success": True,
                "chain": chain,
                "token_address": token_address,
                "token_amount": token_amount,
                "usdt_amount": output_formatted,
                "usdt_amount_raw": output_amount,
                "usdt_per_token": output_formatted / token_amount,
                "price_impact": price_impact,
                "gas": estimated_gas,
                "gas_price_wei": gas_price_wei,
                "base_gas_cost_usd": base_gas_cost_usd,
                "gas_cost_usd": gas_cost_usd
            }
            
        except Exception as e:
            logger.error(f"代币兑换USDT出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "chain": chain,
                "token_address": token_address,
                "token_amount": token_amount
            }
    
    def get_test_amounts(self, token_data: Dict) -> List[float]:
        """
        根据代币交易数据确定测试的USDT金额，确保返回唯一值
        
        Args:
            token_data: 代币数据
            
        Returns:
            List[float]: 测试金额列表（唯一值）
        """
        tx_count = token_data.get("zero_tx_count", 0)
        transactions = token_data.get("transactions", [])
        
        # 如果没有交易数据，使用最小值
        if not transactions:
            return [self.min_usdt_amount]
        
        # 提取交易金额并确保唯一性
        volumes = set()  # 使用集合确保唯一性
        for tx in transactions:
            volume = tx.get("volume_usd", 0)
            if volume > 0:
                # 将金额限制在范围内并四舍五入到2位小数
                volume = round(max(min(volume, self.max_usdt_amount), self.min_usdt_amount), 2)
                volumes.add(volume)
        
        if not volumes:
            return [self.min_usdt_amount]
        
        # 转换为列表并排序
        volumes = sorted(list(volumes))
        
        # 根据交易数量确定测试金额
        if tx_count >= 3:
            # 取最小值、平均值和最大值
            min_vol = volumes[0]
            max_vol = volumes[-1]
            avg_vol = round(sum(volumes) / len(volumes), 2)  # 计算平均值并四舍五入到2位小数
            
            # 如果三个值都相同，只返回一个值
            if min_vol == avg_vol == max_vol:
                return [min_vol]
            # 如果平均值等于最小值或最大值，只返回最小值和最大值
            elif avg_vol == min_vol or avg_vol == max_vol:
                return [min_vol, max_vol]
            # 否则返回三个不同的值
            else:
                return [min_vol, avg_vol, max_vol]
        elif tx_count == 2:
            # 如果最小值和最大值相同，只返回一个值
            if volumes[0] == volumes[-1]:
                return [volumes[0]]
            return [volumes[0], volumes[-1]]
        else:  # tx_count == 1
            return [volumes[0]]
    
    def get_token_decimals(self, token_address: str, chain: str) -> int:
        """
        获取代币精度
        
        Args:
            token_address: 代币地址
            chain: 链名称 (ethereum/polygon)
            
        Returns:
            int: 代币精度，默认为18
        """
        try:
            # 添加MIMO代币特殊处理的日志
            if token_address.lower() == "******************************************".lower() and chain == "ethereum":
                logger.warning(f"检测到在以太坊链上使用MIMO的Polygon地址! 这可能是错误: {token_address}")
            
            if token_address.lower() == "******************************************".lower():
                logger.info(f"检测到MIMO的ETH地址: {token_address}，链: {chain}")
                
            # 从gate_tokens_with_decimals.json读取代币精度
            tokens_file = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
            if os.path.exists(tokens_file):
                with open(tokens_file, "r", encoding="utf-8") as f:
                    tokens_data = json.load(f)
                
                chain_key = "ETH" if chain == "ethereum" else "MATIC" if chain == "polygon" else None
                
                if chain_key and chain_key in tokens_data:
                    # 遍历该链上的所有代币
                    for token_info in tokens_data[chain_key]:
                        if token_info.get("contract_address", "").lower() == token_address.lower():
                            decimals = token_info.get("decimals")
                            if decimals is not None:
                                logger.info(f"从gate_tokens_with_decimals.json读取到代币精度: {decimals}")
                                return decimals
            
            # 如果无法获取到精度，返回默认值18
            return 18
        except Exception as e:
            logger.error(f"获取代币精度失败: {str(e)}")
            return 18
    
    def get_eth_price_usd(self) -> float:
        """
        获取ETH的USD价格，使用缓存减少API调用
        
        Returns:
            float: ETH价格（USD）
        """
        current_time = time.time()
        
        # 如果缓存有效，直接返回缓存的价格
        if (self.eth_price_usd_cache is not None and 
            current_time - self.eth_price_last_updated < self.eth_price_cache_duration):
            logger.info(f"使用缓存的ETH价格: ${self.eth_price_usd_cache:.2f} (缓存时间: {int(current_time - self.eth_price_last_updated)}秒)")
            return self.eth_price_usd_cache
        
        try:
            # 从GeckoTerminal获取ETH价格 - 使用正确的API URL
            weth_address = "******************************************"  # WETH地址
            url = f"https://api.geckoterminal.com/api/v2/simple/networks/eth/token_price/{weth_address}"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "attributes" in data["data"] and "token_prices" in data["data"]["attributes"]:
                    token_prices = data["data"]["attributes"]["token_prices"]
                    if weth_address.lower() in token_prices:
                        eth_price = float(token_prices[weth_address.lower()])
                        logger.info(f"从GeckoTerminal获取实时ETH价格: ${eth_price:.2f}")
                        
                        # 更新缓存
                        self.eth_price_usd_cache = eth_price
                        self.eth_price_last_updated = current_time
                        return eth_price
        except Exception as e:
            logger.warning(f"从GeckoTerminal获取ETH价格失败: {str(e)}")
        
        # 备选方案: 从CoinGecko获取ETH价格
        try:
            url = "https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "ethereum" in data and "usd" in data["ethereum"]:
                    eth_price = float(data["ethereum"]["usd"])
                    logger.info(f"从CoinGecko获取实时ETH价格: ${eth_price:.2f}")
                    
                    # 更新缓存
                    self.eth_price_usd_cache = eth_price
                    self.eth_price_last_updated = current_time
                    return eth_price
        except Exception as e:
            logger.warning(f"从CoinGecko获取ETH价格失败: {str(e)}")
        
        # 如果所有API都失败，使用最后的缓存或默认值
        if self.eth_price_usd_cache is not None:
            logger.warning(f"使用过期的ETH价格缓存: ${self.eth_price_usd_cache:.2f}")
            return self.eth_price_usd_cache
        else:
            # 使用默认值
            default_price = 3000
            logger.warning(f"使用默认ETH价格: ${default_price}")
            self.eth_price_usd_cache = default_price
            self.eth_price_last_updated = current_time
            return default_price
    
    def get_matic_price_usd(self) -> float:
        """
        获取MATIC的USD价格，尝试从GeckoTerminal获取，失败则使用缓存或默认值
        
        Returns:
            float: MATIC价格（USD）
        """
        # 添加缓存属性，如果不存在就初始化
        if not hasattr(self, 'matic_price_usd_cache'):
            self.matic_price_usd_cache = None
            self.matic_price_last_updated = 0
        
        # 缓存检查
        current_time = time.time()
        if self.matic_price_usd_cache is not None:
            # 如果缓存时间不超过10分钟（600秒），直接返回缓存值
            cache_age = current_time - self.matic_price_last_updated
            if cache_age < 600:  # 10分钟缓存
                logger.info(f"使用缓存中的MATIC价格: ${self.matic_price_usd_cache:.2f} (缓存时间: {int(cache_age)}秒)")
                return self.matic_price_usd_cache
                
            # 如果API请求在10分钟内失败过，继续使用缓存值
            if hasattr(self, 'matic_price_last_failed') and current_time - self.matic_price_last_failed < 600:
                logger.info(f"使用缓存中的MATIC价格: ${self.matic_price_usd_cache:.2f} (上次请求失败，继续使用缓存)")
                return self.matic_price_usd_cache
        
        # 尝试从GeckoTerminal获取价格
        try:
            # Polygon WMATIC地址
            wmatic_address = "0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270"
            
            # 使用GeckoTerminal API
            url = f"https://api.geckoterminal.com/api/v2/networks/polygon_pos/tokens/{wmatic_address}"
            headers = {'Accept': 'application/json'}
            
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "data" in data and "attributes" in data["data"] and "price_usd" in data["data"]["attributes"]:
                    matic_price = float(data["data"]["attributes"]["price_usd"])
                    logger.info(f"从GeckoTerminal获取实时MATIC价格: ${matic_price:.2f}")
                    
                    # 更新缓存
                    self.matic_price_usd_cache = matic_price
                    self.matic_price_last_updated = current_time
                    if hasattr(self, 'matic_price_last_failed'):
                        delattr(self, 'matic_price_last_failed')
                    return matic_price
        except Exception as e:
            logger.warning(f"从GeckoTerminal获取MATIC价格失败: {str(e)}")
            # 记录失败时间
            self.matic_price_last_failed = current_time
        
        # 备选方案: 从CoinGecko获取MATIC价格
        try:
            url = "https://api.coingecko.com/api/v3/simple/price?ids=matic-network&vs_currencies=usd"
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "matic-network" in data and "usd" in data["matic-network"]:
                    matic_price = float(data["matic-network"]["usd"])
                    logger.info(f"从CoinGecko获取实时MATIC价格: ${matic_price:.2f}")
                    
                    # 更新缓存
                    self.matic_price_usd_cache = matic_price
                    self.matic_price_last_updated = current_time
                    if hasattr(self, 'matic_price_last_failed'):
                        delattr(self, 'matic_price_last_failed')
                    return matic_price
        except Exception as e:
            logger.warning(f"从CoinGecko获取MATIC价格失败: {str(e)}")
            # 记录失败时间
            self.matic_price_last_failed = current_time
        
        # 如果所有API都失败，使用最后的缓存或默认值
        if self.matic_price_usd_cache is not None:
            logger.warning(f"使用过期的MATIC价格缓存: ${self.matic_price_usd_cache:.2f}")
            return self.matic_price_usd_cache
        else:
            # 使用默认值
            default_price = 0.55
            logger.warning(f"使用默认MATIC价格: ${default_price}")
            self.matic_price_usd_cache = default_price
            self.matic_price_last_updated = current_time
            return default_price
    
    async def estimate_bridge_gas_costs(self, direction: str, symbol: str = None) -> Dict:
        """
        估算跨链桥的gas成本
        
        Args:
            direction: 桥方向，可以是 'polygon_to_ethereum' 或 'ethereum_to_polygon'
            symbol: 代币符号，用于记录日志
            
        Returns:
            Dict: 包含gas成本的字典，包括wei数量和对应的USD价值
        """
        try:
            # 获取所需的gas数量
            if direction == "polygon_to_ethereum":
                gas_amount = GAS_COSTS["polygon_to_ethereum"]  # 从Polygon到Ethereum需要260000 gas
                chain_for_gas_price = "ethereum"  # 在Ethereum上执行claim需要支付ETH gas
                logger.info(f"{symbol if symbol else ''}: 使用Polygon→ETH桥的gas估计: {gas_amount} gas单位")
            elif direction == "ethereum_to_polygon":
                gas_amount = GAS_COSTS["ethereum_to_polygon"]  # 从Ethereum到Polygon需要100000 gas
                chain_for_gas_price = "ethereum"  # 在Ethereum上执行deposit需要支付ETH gas
                logger.info(f"{symbol if symbol else ''}: 使用ETH→Polygon桥的gas估计: {gas_amount} gas单位")
            else:
                logger.warning(f"未知的桥方向: {direction}, 使用默认gas值")
                gas_amount = 100000
                chain_for_gas_price = "ethereum"
            
            # 使用缓存获取gas价格
            eth_gas_price_wei = self.get_gas_price_wei("ethereum")
                
            # 计算基础Gas成本 (Wei)
            base_gas_cost_wei = gas_amount * eth_gas_price_wei
            native_token = "ETH"  # 始终使用ETH进行gas成本计算
            base_gas_cost_native = base_gas_cost_wei / 10**18  # 转换为ETH
            
            # 获取ETH的USD价格 - 使用缓存系统
            native_token_price_usd = self.get_eth_price_usd() if chain_for_gas_price == "ethereum" else self.get_matic_price_usd()
            
            # 计算基础Gas成本 (USD)
            base_gas_cost_usd = base_gas_cost_native * native_token_price_usd
            
            # 应用1.4倍安全系数
            final_gas_cost_usd = base_gas_cost_usd * 1.4
            logger.info(f"基础Gas成本: ${base_gas_cost_usd:.4f} USD, 最终Gas成本(含1.4倍安全系数): ${final_gas_cost_usd:.4f} USD")
            
            return {
                "direction": direction,
                "gas_amount": gas_amount,
                "gas_price_wei": eth_gas_price_wei,
                "gas_cost_wei": base_gas_cost_wei,
                "gas_cost_native": base_gas_cost_native,
                "native_token": native_token,
                "native_token_price_usd": native_token_price_usd,
                "base_gas_cost_usd": base_gas_cost_usd,
                "gas_cost_usd": final_gas_cost_usd  # 使用含安全系数的最终成本
            }
        except Exception as e:
            logger.error(f"估算桥Gas成本时出错: {str(e)}")
            
            # 返回默认估计值
            if direction == "polygon_to_ethereum":
                return {
                    "direction": direction,
                    "gas_amount": GAS_COSTS["polygon_to_ethereum"],
                    "gas_cost_usd": 15.0  # 默认估计值
                }
            else:  # ethereum_to_polygon
                return {
                    "direction": direction,
                    "gas_amount": GAS_COSTS["ethereum_to_polygon"],
                    "gas_cost_usd": 5.0   # 默认估计值
                }
    
    def collect_log(self, token_id: str, message: str, level: str = "INFO") -> None:
        """
        收集特定代币的处理日志
        
        Args:
            token_id: 代币ID (可以是symbol或地址)
            message: 日志消息
            level: 日志级别 (INFO, WARNING, ERROR)
        """
        with self.log_lock:
            if token_id not in self.token_logs:
                self.token_logs[token_id] = []
            
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] [{level}] {message}"
            self.token_logs[token_id].append(log_entry)
    
    def print_token_logs(self, token_id: str) -> None:
        """
        打印特定代币的所有收集到的日志，并同时写入日志文件
        
        Args:
            token_id: 代币ID (可以是symbol或地址)
        """
        with self.log_lock:
            if token_id in self.token_logs and self.token_logs[token_id]:
                log_entries = self.token_logs[token_id]
                
                # 准备完整的日志内容
                separator_line = f"{'='*30} {token_id} 处理日志 {'='*30}"
                log_content = [separator_line]
                log_content.extend(log_entries)
                log_content.append(f"{'='*30} 处理结束 {'='*30}")
                
                # 将完整的日志内容写入日志文件
                for entry in log_content:
                    logger.info(entry)
                
                # 输出后清空该代币的日志，避免重复展示和内存占用
                self.token_logs[token_id] = []
    
    def check_recent_opportunities(self, symbol: str) -> bool:
        """
        检查最近10分钟内是否存在该币种的套利机会
        
        Args:
            symbol: 代币符号
            
        Returns:
            bool: 如果存在套利机会返回True，否则返回False
        """
        csv_file = os.path.join(RESULTS_DIR, "arbitrage_opportunities.csv")
        if not os.path.exists(csv_file):
            return False
            
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file)
            
            # 获取当前时间
            current_time = pd.Timestamp.now()
            
            # 将time列转换为datetime
            df['time'] = pd.to_datetime(df['time'])
            
            # 筛选最近10分钟内的记录
            recent_df = df[df['time'] > (current_time - pd.Timedelta(minutes=10))]
            
            # 检查是否存在该币种的记录
            symbol_opportunities = recent_df[recent_df['symbol'] == symbol]
            
            # 如果存在记录且净利润大于0
            if not symbol_opportunities.empty and (symbol_opportunities['net_profit'] > 0).any():
                logger.info(f"发现{symbol}在最近10分钟内有套利机会，跳过分析")
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"检查历史套利机会时出错: {str(e)}")
            return False

    async def check_arbitrage_opportunity(self, token_pair: Tuple[str, str], token_data: Dict, check_recent: bool = True, fixed_usdt_amount: float = None) -> Dict:
        """
        检查两个链之间的套利机会
        
        Args:
            token_pair: (polygon地址, 以太坊地址)
            token_data: 代币数据
            check_recent: 是否检查最近的机会，二次分析时设为False
            fixed_usdt_amount: 指定的USDT输入值，如果提供则只测试这个金额
            
        Returns:
            Dict: 套利机会数据
        """
        polygon_address, eth_address = token_pair
        symbol = token_data.get("symbol", "UNKNOWN")
        name = token_data.get("name", "Unknown Token")
        
        # 记录代币地址信息以便调试
        logger.info(f"检查 {symbol} 套利机会: Polygon地址={polygon_address}, ETH地址={eth_address}")
        
        # 只在初次分析时检查最近10分钟内的套利机会
        if check_recent and self.check_recent_opportunities(symbol):
            return {
                "success": True,
                "symbol": symbol,
                "name": name,
                "polygon_address": polygon_address,
                "eth_address": eth_address,
                "skipped": True,
                "reason": "最近10分钟内存在套利机会"
            }
        
        # 使用日志收集器记录日志，而不是直接输出
        token_id = f"{symbol}({polygon_address[:8]}...)"
        self.collect_log(token_id, f"开始检查 {symbol} 的套利机会...")
        
        # 简化网络健康检查，只在明确需要时执行
        check_time = getattr(self, '_last_health_check_time', 0)
        current_time = time.time()
        if current_time - check_time > 600:  # 每10分钟检查一次，减少频率
            self.collect_log(token_id, "执行定期网络健康检查...")
            # 简化的网络检查
            if hasattr(self, 'eth_client') and hasattr(self.eth_client, 'web3'):
                try:
                    eth_status = self.eth_client.web3.is_connected()
                    self.collect_log(token_id, f"以太坊网络连接状态: {'正常' if eth_status else '异常'}")
                except Exception as e:
                    self.collect_log(token_id, f"以太坊网络连接检查失败: {str(e)}", "WARNING")
            
            self._last_health_check_time = current_time
        
        # 首先检查1个代币兑换为USDT的价格
        max_retries = 2  # 减少最大重试次数
        retry_count = 0
        polygon_price = None
        eth_price = None
        
        # 获取Polygon价格，带重试机制
        while retry_count < max_retries:
            try:
                # 适度的随机延迟，避免API限流
                delay = 0.3 + random.random() * 0.7  # 随机延迟0.3-1秒
                await asyncio.sleep(delay)
                
                polygon_price = await self.check_token_price(polygon_address, "polygon")
                if polygon_price.get("success"):
                    break
                else:
                    error_msg = polygon_price.get("error", "未知错误")
                    self.collect_log(token_id, f"Polygon价格检查失败，尝试重试 {retry_count+1}/{max_retries} - {error_msg}", "WARNING")
                    retry_count += 1
                    await asyncio.sleep(1 * retry_count)  # 简单线性增长延迟
            except Exception as e:
                self.collect_log(token_id, f"Polygon价格检查异常，尝试重试 {retry_count+1}/{max_retries} - {str(e)}", "WARNING")
                retry_count += 1
                await asyncio.sleep(1 * retry_count)
        
        # 如果所有重试都失败，则退出
        if not polygon_price or not polygon_price.get("success"):
            error_msg = polygon_price.get("error", "多次尝试后仍然失败") if polygon_price else "获取价格失败"
            self.collect_log(token_id, f"Polygon价格检查最终失败 - {error_msg}", "ERROR")
            
            # 集中展示该代币的所有日志
            self.print_token_logs(token_id)
            
            return {
                "success": False,
                "error": error_msg,
                "symbol": symbol,
                "name": name,
                "polygon_address": polygon_address,
                "eth_address": eth_address
            }
        
        # 获取ETH价格，带重试机制
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 适度的随机延迟，避免API限流
                delay = 0.3 + random.random() * 0.7  # 随机延迟0.3-1秒
                await asyncio.sleep(delay)
                
                eth_price = await self.check_token_price(eth_address, "ethereum")
                if eth_price.get("success"):
                    break
                else:
                    error_msg = eth_price.get("error", "未知错误")
                    self.collect_log(token_id, f"ETH价格检查失败，尝试重试 {retry_count+1}/{max_retries} - {error_msg}", "WARNING")
                    retry_count += 1
                    await asyncio.sleep(1 * retry_count)  # 简单线性增长延迟
            except Exception as e:
                self.collect_log(token_id, f"ETH价格检查异常，尝试重试 {retry_count+1}/{max_retries} - {str(e)}", "WARNING")
                retry_count += 1
                await asyncio.sleep(1 * retry_count)
        
        # 如果所有重试都失败，则退出
        if not eth_price or not eth_price.get("success"):
            error_msg = eth_price.get("error", "多次尝试后仍然失败") if eth_price else "获取价格失败"
            self.collect_log(token_id, f"ETH价格检查最终失败 - {error_msg}", "ERROR")
            
            # 集中展示该代币的所有日志
            self.print_token_logs(token_id)
            
            return {
                "success": False,
                "error": error_msg,
                "symbol": symbol,
                "name": name,
                "polygon_address": polygon_address,
                "eth_address": eth_address
            }
        
        # 比较两个链上的价格
        polygon_token_price = polygon_price.get("price_per_token", 0)
        eth_token_price = eth_price.get("price_per_token", 0)
        
        self.collect_log(token_id, f"Polygon={polygon_token_price:.6f} USDT, ETH={eth_token_price:.6f} USDT")
        
        # 确定哪个链的价格更低
        lower_chain = "polygon" if polygon_token_price <= eth_token_price else "ethereum"
        higher_chain = "ethereum" if lower_chain == "polygon" else "polygon"
        
        # 修正地址分配逻辑，确保使用正确的地址
        if lower_chain == "polygon":
            lower_address = polygon_address
            higher_address = eth_address
        else:  # lower_chain == "ethereum"
            lower_address = eth_address
            higher_address = polygon_address
        
        # 记录详细的链和地址信息
        self.collect_log(token_id, f"低价链={lower_chain}, 低价链地址={lower_address}")
        self.collect_log(token_id, f"高价链={higher_chain}, 高价链地址={higher_address}")
        
        # 获取测试的USDT金额
        if fixed_usdt_amount is not None:
            # 如果提供了固定的USDT金额，只使用这个金额
            test_amounts = [fixed_usdt_amount]
            self.collect_log(token_id, f"使用指定的USDT测试金额: {fixed_usdt_amount}")
        else:
            # 否则使用常规方法获取测试金额
            test_amounts = self.get_test_amounts(token_data)
            self.collect_log(token_id, f"使用计算的USDT测试金额: {test_amounts}")
        
        # 计算桥的gas成本
        bridge_direction = "polygon_to_ethereum" if lower_chain == "polygon" else "ethereum_to_polygon"
        bridge_gas_costs = await self.estimate_bridge_gas_costs(bridge_direction, symbol)
        bridge_gas_cost_usd = bridge_gas_costs.get("gas_cost_usd", 0)
        
        self.collect_log(token_id, f"桥方向={bridge_direction}, 桥Gas成本=${bridge_gas_cost_usd:.4f} USDT")
        
        opportunities = []
        
        # 对每个测试金额进行测试
        for usdt_amount in test_amounts:
            # 1. 在低价链上用USDT购买代币
            buy_result = await self.check_usdt_to_token(lower_address, lower_chain, usdt_amount)
            if not buy_result or not buy_result.get("success"):
                error_msg = buy_result.get("error", "购买失败") if buy_result else "购买失败"
                self.collect_log(token_id, f"在{lower_chain}上购买失败 - {error_msg}", "WARNING")
                continue
            
            token_amount = buy_result.get("token_amount", 0)
            
            # 2. 在高价链上卖出代币获取USDT
            sell_result = await self.check_token_to_usdt(higher_address, higher_chain, token_amount)
            if not sell_result or not sell_result.get("success"):
                error_msg = sell_result.get("error", "卖出失败") if sell_result else "卖出失败"
                self.collect_log(token_id, f"在{higher_chain}上卖出失败 - {error_msg}", "WARNING")
                continue
            
            usdt_received = sell_result.get("usdt_amount", 0)
            
            # 3. 计算利润
            profit = usdt_received - usdt_amount
            
            # 4. 考虑gas成本 - 包括DEX交易和桥的成本
            buy_gas_cost = buy_result.get("gas_cost_usd", 0)
            sell_gas_cost = sell_result.get("gas_cost_usd", 0)
            dex_gas_cost = buy_gas_cost + sell_gas_cost
            total_gas_cost = dex_gas_cost + bridge_gas_cost_usd
            
            # 计算减去gas成本后的实际利润
            net_profit = profit - total_gas_cost
            
            # 计算利润百分比 - 考虑gas成本
            profit_percentage = (profit / usdt_amount) * 100 if usdt_amount > 0 else 0
            net_profit_percentage = (net_profit / usdt_amount) * 100 if usdt_amount > 0 else 0
            
            # 记录gas成本详情
            self.collect_log(token_id, f"Gas成本 - 买入: ${buy_gas_cost:.4f}, 卖出: ${sell_gas_cost:.4f}, 桥: ${bridge_gas_cost_usd:.4f}, 总计: ${total_gas_cost:.4f} USDT")
            
            # 根据设置决定是否记录套利机会
            # 如果profit_threshold为0（默认值），则只记录有净利润的机会
            # 如果设置了自定义的profit_threshold，则记录符合阈值的机会
            profitable = net_profit > 0
            meets_threshold = net_profit_percentage >= self.profit_threshold
            
            if (self.profit_threshold == 0 and profitable) or (self.profit_threshold != 0 and meets_threshold):
                opportunity = {
                    "symbol": symbol,
                    "name": name,
                    "lower_chain": lower_chain,
                    "higher_chain": higher_chain,
                    "usdt_input": usdt_amount,
                    "token_amount": token_amount,
                    "usdt_output": usdt_received,
                    "gas_cost_buy": buy_gas_cost,
                    "gas_cost_sell": sell_gas_cost,
                    "gas_cost_bridge": bridge_gas_cost_usd,
                    "total_gas_cost": total_gas_cost,
                    "gross_profit": profit,
                    "net_profit": net_profit,
                    "profit_percentage": profit_percentage,
                    "net_profit_percentage": net_profit_percentage,
                    "polygon_address": polygon_address,
                    "eth_address": eth_address,
                    "bridge_direction": bridge_direction,
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                if profitable:
                    self.collect_log(token_id, f"发现套利机会! 投入={usdt_amount:.2f} USDT, 毛利润={profit:.2f} USDT, Gas成本={total_gas_cost:.2f} USDT, 净利润={net_profit:.2f} USDT ({net_profit_percentage:.2f}%)")
                else:
                    self.collect_log(token_id, f"发现接近盈亏平衡的交易. 投入={usdt_amount:.2f} USDT, 毛利润={profit:.2f} USDT, Gas成本={total_gas_cost:.2f} USDT, 净利润={net_profit:.2f} USDT ({net_profit_percentage:.2f}%)")
                
                opportunities.append(opportunity)
                
                # 添加到全局列表
                with self.lock:
                    self.opportunities.append(opportunity)
                    # 有利润时保存一次结果
                    self.save_opportunities()
                    
                # 如果发现利润，立即启动二次分析
                if profitable and fixed_usdt_amount is None:  # 只在初次分析时启动二次分析
                    try:
                        # 导入二次分析模块
                        from scripts.arbitrage.portal_polygon_bridge.bridge_arb_finder_secondary import SecondaryArbAnalyzer
                        
                        # 创建二次分析器实例
                        secondary_analyzer = SecondaryArbAnalyzer()
                        
                        # 创建事件循环
                        loop = asyncio.get_event_loop()
                        
                        # 运行二次分析
                        self.collect_log(token_id, f"发现利润，启动二次分析...")
                        secondary_logger.info(f"开始对 {symbol} 进行二次分析，最优USDT输入值: {usdt_amount}")
                        
                        # 创建二次分析所需的数据，确保包含bridge_direction和token_amount
                        analysis_data = {
                            'symbol': symbol,
                            'name': name,
                            'polygon_address': polygon_address,
                            'eth_address': eth_address,
                            'optimal_usdt': usdt_amount,
                            'token_amount': token_amount,  # 重要！明确传递token_amount
                            'token_data': token_data,
                            'bridge_direction': bridge_direction,  # 明确传递桥方向
                            'polygon_price': polygon_token_price,  # 添加价格信息
                            'eth_price': eth_token_price,          # 添加价格信息
                            'lower_chain': lower_chain,            # 添加低价链信息
                            'higher_chain': higher_chain           # 添加高价链信息
                        }
                        
                        # 确保token_data中也包含bridge_direction
                        token_data_copy = token_data.copy()
                        token_data_copy['bridge_direction'] = bridge_direction
                        analysis_data['token_data'] = token_data_copy
                        
                        # 记录日志，确认传递了bridge_direction和token_amount
                        self.collect_log(token_id, f"传递给二次分析的桥方向: {bridge_direction}")
                        self.collect_log(token_id, f"传递给二次分析的代币数量: {token_amount}")
                        secondary_logger.info(f"接收到的桥方向: {bridge_direction}")
                        secondary_logger.info(f"接收到的代币数量: {token_amount}")
                        
                        await secondary_analyzer.analyze_token(analysis_data)
                        
                        self.collect_log(token_id, f"二次分析完成")
                        secondary_logger.info(f"{symbol} 的二次分析已完成")
                    except Exception as e:
                        error_msg = str(e)
                        self.collect_log(token_id, f"启动二次分析失败: {error_msg}", "ERROR")
                        secondary_logger.error(f"对 {symbol} 进行二次分析时出错: {error_msg}")
                    
                    # 停止继续分析该币种
                    break
            else:
                self.collect_log(token_id, f"无套利机会. 投入={usdt_amount:.2f} USDT, 毛利润={profit:.2f} USDT, Gas成本={total_gas_cost:.2f} USDT, 净利润={net_profit:.2f} USDT ({net_profit_percentage:.2f}%)")
        
        # 更新进度
        with self.lock:
            self.processed_count += 1
            progress_percent = self.processed_count/self.total_count*100
            self.collect_log(token_id, f"进度: {self.processed_count}/{self.total_count} ({progress_percent:.1f}%)")
        
        # 集中展示该代币的所有日志
        self.print_token_logs(token_id)
            
        # 返回结果
        return {
            "success": True,
            "symbol": symbol,
            "name": name,
            "polygon_address": polygon_address,
            "eth_address": eth_address,
            "polygon_price": polygon_token_price,
            "eth_price": eth_token_price,
            "opportunities": opportunities
        }
        
    async def process_token(self, token_addr: str, token_data: Dict) -> Dict:
        """
        处理单个代币的套利检查
        
        Args:
            token_addr: Polygon代币地址
            token_data: 代币数据
            
        Returns:
            Dict: 套利机会数据
        """
        if "eth_address" not in token_data:
            logger.info(f"跳过 {token_addr}: 没有对应的ETH地址")
            return {
                "success": False,
                "error": "没有对应的ETH地址",
                "polygon_address": token_addr
            }
            
        eth_addr = token_data["eth_address"]
        symbol = token_data.get("symbol", "UNKNOWN")
        token_id = f"{symbol}({token_addr[:8]}...)"
        
        # 添加调试信息
        logger.info(f"处理代币 {symbol}: Polygon地址={token_addr}, ETH地址={eth_addr}")
        
        # 检查套利机会，带重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 确保正确传递代币地址对
                result = await self.check_arbitrage_opportunity((token_addr, eth_addr), token_data)
                # 随机延迟50-300ms避免API限制
                await asyncio.sleep(0.05 + (0.25 * random.random()))
                return result
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    self.collect_log(token_id, f"处理代币时出错，正在重试 {attempt+1}/{max_retries}: {str(e)}", "WARNING")
                    self.collect_log(token_id, f"等待 {wait_time} 秒后重试...", "INFO")
                    await asyncio.sleep(wait_time)
                else:
                    self.collect_log(token_id, f"处理代币最终失败: {str(e)}", "ERROR")
                    self.print_token_logs(token_id)
            return {
                "success": False,
                "error": str(e),
                        "symbol": symbol,
                "polygon_address": token_addr,
                "eth_address": eth_addr
            }
    
        # 默认返回，正常不会执行到这里
        return {
            "success": False,
            "error": "未知错误",
            "polygon_address": token_addr,
            "eth_address": eth_addr
        }
    
    def process_token_sync(self, token_addr, token_data):
        if "eth_address" not in token_data:
            logger.info(f"跳过 {token_addr}: 没有对应的ETH地址")
            return {
                "success": False,
                "error": "没有对应的ETH地址",
                "polygon_address": token_addr
            }
                
        eth_addr = token_data["eth_address"]
        symbol = token_data.get("symbol", "UNKNOWN")
        token_id = f"{symbol}({token_addr[:8]}...)"  # 创建代币标识
        
        # 添加调试信息
        logger.info(f"同步处理代币 {symbol}: Polygon地址={token_addr}, ETH地址={eth_addr}")
        
        # 最大重试次数
        max_retries = 4  # 增加至4次重试
        # 是否尝试重置API客户端
        reset_attempted = False
            
        # 检查套利机会，带重试机制
        for attempt in range(max_retries):
            try:
                # 创建新的事件循环并运行异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 确保正确传递代币地址对
                result = loop.run_until_complete(self.check_arbitrage_opportunity(
                    (token_addr, eth_addr), token_data))
                loop.close()
                
                # 更新进度
                if self.progress_queue:
                    self.progress_queue.put(1)
                
                # 随机延迟50-300ms避免API限制
                time.sleep(0.05 + (0.25 * random.random()))
                return result
            except Exception as e:
                # 检查是否是网络相关错误
                error_str = str(e).lower()
                is_network_error = any(term in error_str for term in [
                    "connection", "timeout", "connect", "ssl", "http", "api", 
                    "network", "refused", "reset", "eof", "certificate"
                ])
                
                if is_network_error and not reset_attempted:
                    # 第一次遇到网络错误时，尝试重置API客户端
                    self.collect_log(token_id, f"检测到网络错误: {str(e)}", "WARNING")
                    reset_success = self.reset_api_clients()
                    reset_attempted = True
                    
                    if reset_success:
                        self.collect_log(token_id, f"已重置API客户端，继续处理代币 {symbol}({token_addr})")
                        # 延迟稍长一点，给新的连接一些建立时间
                        time.sleep(5)  # 增加至5秒
                        continue
                
                if attempt < max_retries - 1:
                    # 使用指数退避策略
                    wait_time = min(30, 2 ** (attempt + 1))  # 最多等待30秒
                    self.collect_log(token_id, f"处理代币时出错，正在重试 {attempt+1}/{max_retries}: {str(e)}", "WARNING")
                    self.collect_log(token_id, f"等待 {wait_time} 秒后重试...", "INFO")
                    time.sleep(wait_time)
                    
                    # 随机延迟0.5-2秒，分散API请求
                    random_delay = 0.5 + random.random() * 1.5
                    time.sleep(random_delay)
                else:
                    if self.progress_queue:
                        self.progress_queue.put(1)  # 确保失败时也更新进度
                    self.collect_log(token_id, f"处理代币最终失败: {str(e)}", "ERROR")
                    # 打印所有收集的日志
                    self.print_token_logs(token_id)
                    return {
                        "success": False,
                        "error": str(e),
                        "symbol": symbol,
                        "polygon_address": token_addr,
                        "eth_address": eth_addr
                    }
                    
        # 默认情况
        if self.progress_queue:
            self.progress_queue.put(1)
        self.collect_log(token_id, "处理完成但遇到未知错误", "ERROR")
        self.print_token_logs(token_id)
        return {
            "success": False,
            "error": "未知错误",
            "symbol": symbol,
            "polygon_address": token_addr,
            "eth_address": eth_addr
        }
    
    def find_arbitrage_opportunities(self) -> List[Dict]:
        """查找所有可能的套利机会（真正的多线程实现）"""
        # 导入多线程库
        from queue import Queue
        from concurrent.futures import ThreadPoolExecutor, as_completed
        
        # 重置计数器
        self.total_count = len(self.tokens_data)
        self.processed_count = 0
        self.opportunities = []
        
        # 创建进度队列和报告线程
        self.progress_queue = Queue()
        stop_event = threading.Event()
        
        # 定义进度报告函数
        def progress_reporter():
            last_update_time = time.time()
            last_processed = 0
            processed = 0
            
            while not stop_event.is_set():
                try:
                    # 获取进度更新（非阻塞）
                    try:
                        while not self.progress_queue.empty():
                            self.progress_queue.get(block=False)
                            processed += 1
                    except Exception:
                        pass
                    
                    current_time = time.time()
                    time_elapsed = current_time - last_update_time
                    
                    # 每5秒或处理了10个代币后更新一次进度
                    if time_elapsed >= 5 or processed - last_processed >= 10:
                        rate = (processed - last_processed) / time_elapsed if time_elapsed > 0 else 0
                        percent_complete = (processed / self.total_count) * 100 if self.total_count > 0 else 0
                        
                        logger.info(f"进度: {processed}/{self.total_count} ({percent_complete:.1f}%), "
                                  f"速率: {rate:.2f} 代币/秒, "
                                  f"已找到: {len(self.opportunities)} 个机会")
                        
                        last_update_time = current_time
                        last_processed = processed
                    
                    # 检查是否所有任务已完成
                    if processed >= self.total_count:
                        logger.info(f"所有 {self.total_count} 个代币处理完成!")
                        break
                        
                    time.sleep(0.5)  # 短暂休眠，减少CPU占用
                    
                except Exception as e:
                    logger.error(f"进度报告线程错误: {str(e)}")
                    time.sleep(1)
        
        # 启动进度报告线程
        progress_thread = threading.Thread(target=progress_reporter)
        progress_thread.daemon = True
        progress_thread.start()
            
        logger.info(f"启动 {self.num_workers} 个线程处理 {self.total_count} 个代币...")
        
        try:
            # 创建线程池
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                # 提交所有任务
                future_to_token = {
                    executor.submit(self.process_token_sync, token_addr, token_data): token_addr
                    for token_addr, token_data in self.tokens_data.items()
                }
                
                # 处理结果
                for future in as_completed(future_to_token):
                    token_addr = future_to_token[future]
                    try:
                        result = future.result()
                        # 只记录处理结果，不再重复添加套利机会
                        if result.get("success"):
                            logger.info(f"成功处理代币 {token_addr}")
                        else:
                            logger.warning(f"处理代币 {token_addr} 失败: {result.get('error', '未知错误')}")
                    except Exception as e:
                        logger.error(f"获取 {token_addr} 的结果时出错: {str(e)}")
        finally:
            # 通知进度线程停止
            stop_event.set()
            # 等待进度报告线程完成
            if progress_thread and progress_thread.is_alive():
                progress_thread.join(timeout=2.0)
            
        # 最后保存结果
        self.save_opportunities()
            
        return self.opportunities
    
    def save_opportunities(self) -> None:
        """保存套利机会到单一文件，并在文件过大时删除旧数据"""
        # 定义文件路径
        json_file = os.path.join(RESULTS_DIR, "arbitrage_opportunities.json")
        csv_file = os.path.join(RESULTS_DIR, "arbitrage_opportunities.csv")
        
        # 只保存有利润的套利机会
        if self.opportunities:
            # 根据利润百分比排序
            sorted_opps = sorted(self.opportunities, key=lambda x: x["net_profit_percentage"], reverse=True)
            
            # 检查现有文件
            existing_opps = []
            if os.path.exists(json_file):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        existing_opps = json.load(f)
                except Exception as e:
                    logger.warning(f"读取现有JSON文件失败: {str(e)}")
            
            # 创建唯一标识字典，用于去重
            unique_opps = {}
            # 首先处理现有数据
            for opp in existing_opps:
                key = f"{opp['symbol']}_{opp['usdt_input']}_{opp['time']}"
                unique_opps[key] = opp
            
            # 然后处理新数据，如果有相同key则更新
            for opp in sorted_opps:
                key = f"{opp['symbol']}_{opp['usdt_input']}_{opp['time']}"
                unique_opps[key] = opp
            
            # 转换回列表
            all_opps = list(unique_opps.values())
            
            # 检查文件大小
            if os.path.exists(json_file):
                file_size = os.path.getsize(json_file)
                if file_size > 18 * 1024 * 1024:  # 18MB
                    logger.info("文件大小超过18MB，删除一半旧数据")
                    # 保留后半部分数据（较新的数据）
                    all_opps = all_opps[len(all_opps)//2:]
            
            # 保存JSON
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(all_opps, f, indent=2, ensure_ascii=False)
                
            # 生成CSV报告
            df = pd.DataFrame(all_opps)
            df.to_csv(csv_file, index=False)
            
            logger.info(f"已保存 {len(all_opps)} 个套利机会到文件")

    def reset_api_clients(self):
        """
        重置API客户端，不重置代理
        """
        with self.lock:  # 使用线程锁确保线程安全
            logger.warning("重置API客户端...")
            try:
                # 关闭现有客户端的会话
                if hasattr(self, 'eth_client') and self.eth_client:
                    if hasattr(self.eth_client, 'close_session'):
                        self.eth_client.close_session()
                if hasattr(self, 'polygon_client') and self.polygon_client:
                    if hasattr(self.polygon_client, 'close_session'):
                        self.polygon_client.close_session()
                
                # 短暂延迟，确保所有连接已关闭
                time.sleep(1)
                
                # 重新初始化客户端 - 简化，不使用代理
                self.eth_client = KyberSwapClient(chain="ethereum")
                self.polygon_client = KyberSwapClient(chain="polygon")
                
                logger.info("API客户端重置成功")
                # 记录当前时间，避免频繁重置
                self.last_client_reset_time = time.time()
                return True
            except Exception as e:
                logger.error(f"重置API客户端失败: {str(e)}")
                return False
                
    async def perform_network_health_check(self):
        """
        执行网络健康检查，尝试解决连接问题
        """
        logger.info("执行网络健康检查...")
        test_networks = []
        
        # 测试以太坊网络连接
        try:
            if self.eth_client:
                eth_status = await asyncio.wait_for(
                    asyncio.to_thread(lambda: self.eth_client.web3.is_connected()), 
                    timeout=5
                )
                logger.info(f"以太坊网络连接状态: {'正常' if eth_status else '异常'}")
                if not eth_status:
                    test_networks.append("ethereum")
        except Exception as e:
            logger.warning(f"测试以太坊网络连接失败: {str(e)}")
            test_networks.append("ethereum")
            
        # 测试Polygon网络连接
        try:
            if self.polygon_client:
                polygon_status = await asyncio.wait_for(
                    asyncio.to_thread(lambda: self.polygon_client.web3.is_connected()), 
                    timeout=5
                )
                logger.info(f"Polygon网络连接状态: {'正常' if polygon_status else '异常'}")
                if not polygon_status:
                    test_networks.append("polygon")
        except Exception as e:
            logger.warning(f"测试Polygon网络连接失败: {str(e)}")
            test_networks.append("polygon")
            
        # 如果有网络异常，尝试重置代理或客户端
        if test_networks:
            # 1. 首先尝试轮换代理
            if self.config.get("proxy_rotation_enabled", False):
                await self.rotate_proxies()
                
            # 2. 如果依然无法连接，重置API客户端
            current_time = time.time()
            if (not hasattr(self, 'last_client_reset_time') or 
                current_time - getattr(self, 'last_client_reset_time', 0) > 300):  # 5分钟内不重复重置
                
                await asyncio.to_thread(self.reset_api_clients)
                return True
        
        return len(test_networks) == 0  # 如果没有问题网络，返回True
        
    async def rotate_proxies(self):
        """
        轮换代理设置
        """
        if not self.config.get("proxy_list"):
            logger.info("没有配置代理列表，无法轮换代理")
            return False
            
        with self.lock:
            try:
                # 获取代理列表和当前代理索引
                proxy_list = self.config.get("proxy_list", [])
                current_index = getattr(self, "current_proxy_index", 0)
                
                # 计算下一个代理索引
                next_index = (current_index + 1) % len(proxy_list)
                new_proxy = proxy_list[next_index]
                
                # 更新代理配置
                self.config["proxies"] = {
                    "http": new_proxy,
                    "https": new_proxy
                }
                
                # 保存当前索引
                self.current_proxy_index = next_index
                
                logger.info(f"已轮换到新代理 [{next_index+1}/{len(proxy_list)}]: {new_proxy}")
                
                # 应用新代理到API客户端
                await asyncio.to_thread(self.reset_api_clients)
                return True
                
            except Exception as e:
                logger.error(f"轮换代理失败: {str(e)}")
                return False

def main(num_workers: int = DEFAULT_NUM_WORKERS):
    """主函数"""
    finder = BridgeArbFinder(num_workers=num_workers)
    if not finder.tokens_data:
        logger.error("未找到有效的代币数据，退出")
        return
    
    logger.info(f"使用 {num_workers} 个工作线程处理 {len(finder.tokens_data)} 个代币...")
    
    start_time = time.time()
    opportunities = finder.find_arbitrage_opportunities()
    end_time = time.time()
    
    logger.info(f"套利查找完成! 总耗时: {(end_time - start_time) / 60:.2f} 分钟")
    
    if opportunities:
        logger.info(f"找到 {len(opportunities)} 个套利机会!")
        # 最后保存一次，确保所有机会都被保存
        finder.save_opportunities()
    else:
        logger.info("未找到任何套利机会")
    
if __name__ == "__main__":
    # 确保结果目录存在
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    logger.info("开始搜索Polygon和以太坊之间的套利机会...")
    logger.info(f"使用数据源: {TOKENS_FILE}")
    logger.info(f"测试金额范围: {MIN_USDT_AMOUNT}-{MAX_USDT_AMOUNT} USDT")
    logger.info(f"结果将保存在: {RESULTS_DIR}")
    
    # 获取命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='Polygon-以太坊跨链套利查找器')
    parser.add_argument('--threads', type=int, default=DEFAULT_NUM_WORKERS,
                        help=f'工作线程数 (默认: {DEFAULT_NUM_WORKERS})')
    args = parser.parse_args()
    
    try:
        main(num_workers=args.threads)
    except KeyboardInterrupt:
        logger.info("\n用户中断操作")
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc() 