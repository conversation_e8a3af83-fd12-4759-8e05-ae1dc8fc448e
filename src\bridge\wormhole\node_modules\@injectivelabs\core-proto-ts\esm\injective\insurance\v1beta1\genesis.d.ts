import _m0 from "protobufjs/minimal.js";
import { InsuranceFund, Params, RedemptionSchedule } from "./insurance";
export declare const protobufPackage = "injective.insurance.v1beta1";
/** GenesisState defines the insurance module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to insurance. */
    params: Params | undefined;
    /**
     * insurance_funds describes the insurance funds available for derivative
     * markets
     */
    insuranceFunds: InsuranceFund[];
    /** redemption_schedule describes the redemption requests pending */
    redemptionSchedule: RedemptionSchedule[];
    /**
     * next_share_denom_id describes the next share denom id to be used for newly
     * creating insurance fund incremented by 1 per insurance fund creation
     */
    nextShareDenomId: string;
    /**
     * next_redemption_schedule_id describes next redemption schedule id to be
     * used for next schedule incremented by 1 per redemption request
     */
    nextRedemptionScheduleId: string;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
