{"version": 3, "file": "legacyTransaction.js", "sourceRoot": "", "sources": ["../src/legacyTransaction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAWwB;AACxB,iCAAuF;AACvF,qDAAmD;AAEnD,+BAA6C;AAE7C,IAAM,gBAAgB,GAAG,CAAC,CAAA;AAE1B;;GAEG;AACH;IAAyC,+BAA4B;IA6EnE;;;;;;OAMG;IACH,qBAAmB,MAAc,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QAAvD,iBAwCC;;gBAvCC,wCAAW,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC;QAElD,KAAI,CAAC,MAAM,GAAG,KAAI,CAAC,YAAY,CAAC,KAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAEpD,KAAI,CAAC,QAAQ,GAAG,IAAI,oBAAE,CAAC,IAAA,0BAAQ,EAAC,MAAM,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEjF,IAAI,KAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,6BAAW,CAAC,EAAE;YACpD,IAAM,GAAG,GAAG,KAAI,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAA;YACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,KAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,KAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QAEjE,IAAI,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC7C,IAAI,CAAC,KAAI,CAAC,QAAQ,EAAE,EAAE;gBACpB,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;aAChE;iBAAM;gBACL,eAAe;gBACf,kFAAkF;gBAClF,sFAAsF;gBACtF,mGAAmG;gBACnG,oEAAoE;gBACpE,IAAM,CAAC,GAAG,KAAI,CAAC,CAAE,CAAA;gBACjB,IAAM,cAAc,GAAG,KAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAEtD,yCAAyC;gBACzC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;oBAClE,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;iBAChE;aACF;SACF;QAED,IAAI,KAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACpC,IAAA,2BAAoB,EAAC,KAAI,CAAC,MAAM,EAAE,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACpD;QAED,IAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,CAAA;SACpB;;IACH,CAAC;IAvHD;;;;;;;OAOG;IACW,sBAAU,GAAxB,UAAyB,MAAc,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QAC3D,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED;;;;OAIG;IACW,4BAAgB,GAA9B,UAA+B,UAAkB,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACrE,IAAM,MAAM,GAAG,qBAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QAErC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED;;;;;;OAMG;IACW,+BAAmB,GAAjC,UAAkC,UAAkB,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACxE,OAAO,WAAW,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED;;;;OAIG;IACW,2BAAe,GAA7B,UAA8B,MAAqB,EAAE,IAAoB;QAApB,qBAAA,EAAA,SAAoB;QACvE,uGAAuG;QACvG,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;SACF;QAEK,IAAA,KAAA,OAAwD,MAAM,IAAA,EAA7D,KAAK,QAAA,EAAE,QAAQ,QAAA,EAAE,QAAQ,QAAA,EAAE,EAAE,QAAA,EAAE,KAAK,QAAA,EAAE,IAAI,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAU,CAAA;QAEpE,IAAA,yCAAuB,EAAC,EAAE,KAAK,OAAA,EAAE,QAAQ,UAAA,EAAE,QAAQ,UAAA,EAAE,KAAK,OAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAA;QAEtE,OAAO,IAAI,WAAW,CACpB;YACE,KAAK,OAAA;YACL,QAAQ,UAAA;YACR,QAAQ,UAAA;YACR,EAAE,IAAA;YACF,KAAK,OAAA;YACL,IAAI,MAAA;YACJ,CAAC,GAAA;YACD,CAAC,GAAA;YACD,CAAC,GAAA;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAmDD;;;;;;;;;;;;OAYG;IACH,yBAAG,GAAH;QACE,OAAO;YACL,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,oCAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACpE,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,+BAAS,GAAT;QACE,OAAO,qBAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC/B,CAAC;IAEO,uCAAiB,GAAzB;QACE,IAAM,MAAM,GAAG;YACb,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAA,oCAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACjC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,oCAAkB,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,IAAI;SACV,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,MAAM,CAAC,IAAI,CAAC,IAAA,0BAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA;YAC9C,MAAM,CAAC,IAAI,CAAC,IAAA,6BAAW,EAAC,IAAA,0BAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACrC,MAAM,CAAC,IAAI,CAAC,IAAA,6BAAW,EAAC,IAAA,0BAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAmBD,sCAAgB,GAAhB,UAAiB,WAAkB;QAAlB,4BAAA,EAAA,kBAAkB;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,IAAI,WAAW,EAAE;YACf,OAAO,IAAA,yBAAO,EAAC,OAAO,CAAC,CAAA;SACxB;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;OAEG;IACH,gCAAU,GAAV;QACE,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,iBAAM,UAAU,WAAE;gBACzB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,iBAAM,UAAU,WAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,oCAAc,GAAd;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzD,CAAC;IAED;;;;;OAKG;IACH,0BAAI,GAAJ;QACE,+EAA+E;QAC/E,4DAA4D;QAC5D,0EAA0E;QAC1E,0EAA0E;QAC1E,EAAE;QACF,oFAAoF;QACpF,+CAA+C;QAC/C,EAAE;QACF,yEAAyE;QACzE,EAAE;QACF,yBAAyB;QACzB,sFAAsF;QACtF,wBAAwB;QACxB,GAAG;QAEH,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;aACtC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,IAAA,yBAAO,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iDAA2B,GAA3B;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,OAAO,IAAA,yBAAO,EAAC,OAAO,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,wCAAkB,GAAlB;;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAElD,uGAAuG;QACvG,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,KAAI,MAAA,IAAI,CAAC,CAAC,0CAAE,EAAE,CAAC,eAAO,CAAC,CAAA,EAAE;YAC/D,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CACxB,8EAA8E,CAC/E,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAEK,IAAA,KAAc,IAAI,EAAhB,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAS,CAAA;QACxB,IAAI;YACF,OAAO,IAAA,2BAAS,EACd,OAAO,EACP,CAAE,EACF,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAA,oCAAkB,EAAC,CAAE,CAAC,EACtB,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CACvF,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED;;OAEG;IACO,uCAAiB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,CAAS;QACzD,IAAM,GAAG,GAAG,IAAI,oBAAE,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;SAClD;QAED,IAAM,IAAI,yBAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAA;QAEvD,OAAO,WAAW,CAAC,UAAU,CAC3B;YACE,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,IAAI,oBAAE,CAAC,CAAC,CAAC;SACb,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,4BAAM,GAAN;QACE,OAAO;YACL,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,QAAQ,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,QAAQ,CAAC;YAChC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,yBAAO,EAAC,IAAI,CAAC,KAAK,CAAC;YAC1B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACrD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,yBAAO,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACtD,CAAA;IACH,CAAC;IAED;;OAEG;IACK,kCAAY,GAApB,UAAqB,CAAM,EAAE,MAAe;QAC1C,8DAA8D;QAC9D,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBACzC,MAAM,IAAI,KAAK,CACb,2FAAoF,CAAC,CAAE,CACxF,CAAA;aACF;SACF;QAED,IAAI,SAAS,CAAA;QACb,6DAA6D;QAC7D,IACE,CAAC,KAAK,SAAS;YACf,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACV,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EACV;YACA,IAAI,MAAM,EAAE;gBACV,IAAM,cAAc,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACjD,IAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;gBAErF,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CACb,sCAA+B,CAAC,2BAAiB,MAAM,CAAC,SAAS,EAAE,mFAAgF,CACpJ,CAAA;iBACF;aACF;iBAAM;gBACL,+BAA+B;gBAC/B,IAAI,MAAM,SAAA,CAAA;gBACV,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,MAAM,GAAG,EAAE,CAAA;iBACZ;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,iDAAiD;gBACjD,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACnC;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACK,iDAA2B,GAAnC;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACK,+CAAyB,GAAjC;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;QAEtE,eAAe;QACf,4UAA4U;QAC5U,IAAM,CAAC,GAAG,IAAI,CAAC,CAAE,CAAA;QAEjB,IAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEtD,IAAM,+BAA+B,GACnC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QAEhE,OAAO,+BAA+B,IAAI,oBAAoB,CAAA;IAChE,CAAC;IAED;;OAEG;IACI,8BAAQ,GAAf;QACE,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,oBAAa,IAAI,CAAC,QAAQ,CAAE,CAAA;QACxC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,+BAAS,GAAnB,UAAoB,GAAW;QAC7B,OAAO,UAAG,GAAG,eAAK,IAAI,CAAC,QAAQ,EAAE,MAAG,CAAA;IACtC,CAAC;IACH,kBAAC;AAAD,CAAC,AAhcD,CAAyC,iCAAe,GAgcvD"}