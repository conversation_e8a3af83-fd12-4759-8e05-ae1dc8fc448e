import { ConcreteException } from '../base.js';
import { ErrorType } from '../types/index.js';
import { mapMetamaskMessage } from '../utils/maps.js';
const removeMetamaskFromErrorString = (message) => message
    .replaceAll('Metamask', '')
    .replaceAll('MetaMask', '')
    .replaceAll('Metamask:', '');
export class MetamaskException extends ConcreteException {
    static errorClass = 'MetamaskException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage(mapMetamaskMessage(removeMetamaskFromErrorString(message)));
        this.setName(MetamaskException.errorClass);
    }
}
