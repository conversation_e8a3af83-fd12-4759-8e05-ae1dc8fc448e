{"version": 3, "file": "web3_api_types.d.ts", "sourceRoot": "", "sources": ["../../src/web3_api_types.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAEnE,MAAM,WAAW,eAAe;IAC/B,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;CACvB;AAED,MAAM,WAAW,eAAgB,SAAQ,eAAe;IACvD,QAAQ,CAAC,IAAI,EAAE,kBAAkB,CAAC;IAClC,QAAQ,CAAC,IAAI,EAAE;QACd,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC;QAC9B,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;KACzB,CAAC;CACF;AAED,MAAM,WAAW,gBAAiB,SAAQ,KAAK;IAC9C,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,OAAO,CAAC;CACf;AAED,MAAM,WAAW,mBAAmB;IACnC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CACzB;AAED,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC;AAC5E,MAAM,MAAM,aAAa,CAAC,CAAC,SAAS,WAAW,IAAI,MAAM,GAAG,MAAM,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACtF,MAAM,MAAM,aAAa,CACxB,GAAG,SAAS,WAAW,EACvB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,IAC9B,GAAG,SAAS,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;AAElF,MAAM,WAAW,cAAc,CAAC,GAAG,SAAS,WAAW,EAAE,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC;IACzF,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,SAAS,OAAO,EAAE,GAAG,MAAM,CAAC;CAClE;AAED,MAAM,WAAW,cAAc,CAAC,GAAG,SAAS,WAAW,EAAE,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,CACzF,SAAQ,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC;IACnC,QAAQ,CAAC,OAAO,CAAC,EAAE,iBAAiB,CAAC;IACrC,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC;IACxB,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC;CAClC;AAED,MAAM,MAAM,iBAAiB,CAC5B,GAAG,SAAS,WAAW,EACvB,MAAM,SAAS,aAAa,CAAC,GAAG,CAAC,IAE9B,GAAG,SAAS,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC"}