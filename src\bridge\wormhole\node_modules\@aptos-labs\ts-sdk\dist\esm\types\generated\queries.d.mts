import { GetAccountCoinsCountQueryVariables, GetAccountCoinsCountQuery, GetAccountCoinsDataQueryVariables, GetAccountCoinsDataQuery, GetAccountCollectionsWithOwnedTokensQueryVariables, GetAccountCollectionsWithOwnedTokensQuery, GetAccountOwnedTokensQueryVariables, GetAccountOwnedTokensQuery, GetAccountOwnedTokensByTokenDataQueryVariables, GetAccountOwnedTokensByTokenDataQuery, GetAccountOwnedTokensFromCollectionQueryVariables, GetAccountOwnedTokensFromCollectionQuery, GetAccountTokensCountQueryVariables, GetAccountTokensCountQuery, GetAccountTransactionsCountQueryVariables, GetAccountTransactionsCountQuery, GetChainTopUserTransactionsQueryVariables, GetChainTopUserTransactionsQuery, GetCollectionDataQueryVariables, GetCollectionDataQuery, GetCurrentFungibleAssetBalancesQueryVariables, GetCurrentFungibleAssetBalancesQuery, GetDelegatedStakingActivitiesQueryVariables, GetDelegatedStakingActivitiesQuery, GetEventsQueryVariables, GetEventsQuery, GetFungibleAssetActivitiesQueryVariables, GetFungibleAssetActivitiesQuery, GetFungibleAssetMetadataQueryVariables, GetFungibleAssetMetadataQuery, GetNamesQueryVariables, GetNamesQuery, GetNumberOfDelegatorsQueryVariables, GetNumberOfDelegatorsQuery, GetObjectDataQueryVariables, GetObjectDataQuery, GetProcessorStatusQueryVariables, GetProcessorStatusQuery, GetTableItemsDataQueryVariables, GetTableItemsDataQuery, GetTableItemsMetadataQueryVariables, GetTableItemsMetadataQuery, GetTokenActivityQueryVariables, GetTokenActivityQuery, GetCurrentTokenOwnershipQueryVariables, GetCurrentTokenOwnershipQuery, GetTokenDataQueryVariables, GetTokenDataQuery } from './operations.mjs';
import { GraphQLClient, RequestOptions } from 'graphql-request';
import './types.mjs';

type GraphQLClientRequestHeaders = RequestOptions["requestHeaders"];
declare const TokenActivitiesFieldsFragmentDoc = "\n    fragment TokenActivitiesFields on token_activities_v2 {\n  after_value\n  before_value\n  entry_function_id_str\n  event_account_address\n  event_index\n  from_address\n  is_fungible_v2\n  property_version_v1\n  to_address\n  token_amount\n  token_data_id\n  token_standard\n  transaction_timestamp\n  transaction_version\n  type\n}\n    ";
declare const AnsTokenFragmentFragmentDoc = "\n    fragment AnsTokenFragment on current_aptos_names {\n  domain\n  expiration_timestamp\n  registered_address\n  subdomain\n  token_standard\n  is_primary\n  owner_address\n  subdomain_expiration_policy\n  domain_expiration_timestamp\n}\n    ";
declare const CurrentTokenOwnershipFieldsFragmentDoc = "\n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
declare const GetAccountCoinsCount = "\n    query getAccountCoinsCount($address: String) {\n  current_fungible_asset_balances_aggregate(\n    where: {owner_address: {_eq: $address}}\n  ) {\n    aggregate {\n      count\n    }\n  }\n}\n    ";
declare const GetAccountCoinsData = "\n    query getAccountCoinsData($where_condition: current_fungible_asset_balances_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_fungible_asset_balances_order_by!]) {\n  current_fungible_asset_balances(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    amount\n    asset_type\n    is_frozen\n    is_primary\n    last_transaction_timestamp\n    last_transaction_version\n    owner_address\n    storage_id\n    token_standard\n    metadata {\n      token_standard\n      symbol\n      supply_aggregator_table_key_v1\n      supply_aggregator_table_handle_v1\n      project_uri\n      name\n      last_transaction_version\n      last_transaction_timestamp\n      icon_uri\n      decimals\n      creator_address\n      asset_type\n    }\n  }\n}\n    ";
declare const GetAccountCollectionsWithOwnedTokens = "\n    query getAccountCollectionsWithOwnedTokens($where_condition: current_collection_ownership_v2_view_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_collection_ownership_v2_view_order_by!]) {\n  current_collection_ownership_v2_view(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      mutable_description\n      max_supply\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n    collection_id\n    collection_name\n    collection_uri\n    creator_address\n    distinct_tokens\n    last_transaction_version\n    owner_address\n    single_token_uri\n  }\n}\n    ";
declare const GetAccountOwnedTokens = "\n    query getAccountOwnedTokens($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    \n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
declare const GetAccountOwnedTokensByTokenData = "\n    query getAccountOwnedTokensByTokenData($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    \n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
declare const GetAccountOwnedTokensFromCollection = "\n    query getAccountOwnedTokensFromCollection($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    \n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
declare const GetAccountTokensCount = "\n    query getAccountTokensCount($where_condition: current_token_ownerships_v2_bool_exp, $offset: Int, $limit: Int) {\n  current_token_ownerships_v2_aggregate(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    aggregate {\n      count\n    }\n  }\n}\n    ";
declare const GetAccountTransactionsCount = "\n    query getAccountTransactionsCount($address: String) {\n  account_transactions_aggregate(where: {account_address: {_eq: $address}}) {\n    aggregate {\n      count\n    }\n  }\n}\n    ";
declare const GetChainTopUserTransactions = "\n    query getChainTopUserTransactions($limit: Int) {\n  user_transactions(limit: $limit, order_by: {version: desc}) {\n    version\n  }\n}\n    ";
declare const GetCollectionData = "\n    query getCollectionData($where_condition: current_collections_v2_bool_exp!) {\n  current_collections_v2(where: $where_condition) {\n    uri\n    total_minted_v2\n    token_standard\n    table_handle_v1\n    mutable_uri\n    mutable_description\n    max_supply\n    collection_id\n    collection_name\n    creator_address\n    current_supply\n    description\n    last_transaction_timestamp\n    last_transaction_version\n    cdn_asset_uris {\n      cdn_image_uri\n      asset_uri\n      animation_optimizer_retry_count\n      cdn_animation_uri\n      cdn_json_uri\n      image_optimizer_retry_count\n      json_parser_retry_count\n      raw_animation_uri\n      raw_image_uri\n    }\n  }\n}\n    ";
declare const GetCurrentFungibleAssetBalances = "\n    query getCurrentFungibleAssetBalances($where_condition: current_fungible_asset_balances_bool_exp, $offset: Int, $limit: Int) {\n  current_fungible_asset_balances(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    amount\n    asset_type\n    is_frozen\n    is_primary\n    last_transaction_timestamp\n    last_transaction_version\n    owner_address\n    storage_id\n    token_standard\n  }\n}\n    ";
declare const GetDelegatedStakingActivities = "\n    query getDelegatedStakingActivities($delegatorAddress: String, $poolAddress: String) {\n  delegated_staking_activities(\n    where: {delegator_address: {_eq: $delegatorAddress}, pool_address: {_eq: $poolAddress}}\n  ) {\n    amount\n    delegator_address\n    event_index\n    event_type\n    pool_address\n    transaction_version\n  }\n}\n    ";
declare const GetEvents = "\n    query getEvents($where_condition: events_bool_exp, $offset: Int, $limit: Int, $order_by: [events_order_by!]) {\n  events(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    account_address\n    creation_number\n    data\n    event_index\n    sequence_number\n    transaction_block_height\n    transaction_version\n    type\n    indexed_type\n  }\n}\n    ";
declare const GetFungibleAssetActivities = "\n    query getFungibleAssetActivities($where_condition: fungible_asset_activities_bool_exp, $offset: Int, $limit: Int) {\n  fungible_asset_activities(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n  ) {\n    amount\n    asset_type\n    block_height\n    entry_function_id_str\n    event_index\n    gas_fee_payer_address\n    is_frozen\n    is_gas_fee\n    is_transaction_success\n    owner_address\n    storage_id\n    storage_refund_amount\n    token_standard\n    transaction_timestamp\n    transaction_version\n    type\n  }\n}\n    ";
declare const GetFungibleAssetMetadata = "\n    query getFungibleAssetMetadata($where_condition: fungible_asset_metadata_bool_exp, $offset: Int, $limit: Int) {\n  fungible_asset_metadata(where: $where_condition, offset: $offset, limit: $limit) {\n    icon_uri\n    project_uri\n    supply_aggregator_table_handle_v1\n    supply_aggregator_table_key_v1\n    creator_address\n    asset_type\n    decimals\n    last_transaction_timestamp\n    last_transaction_version\n    name\n    symbol\n    token_standard\n    supply_v2\n    maximum_v2\n  }\n}\n    ";
declare const GetNames = "\n    query getNames($offset: Int, $limit: Int, $where_condition: current_aptos_names_bool_exp, $order_by: [current_aptos_names_order_by!]) {\n  current_aptos_names(\n    limit: $limit\n    where: $where_condition\n    order_by: $order_by\n    offset: $offset\n  ) {\n    ...AnsTokenFragment\n  }\n}\n    \n    fragment AnsTokenFragment on current_aptos_names {\n  domain\n  expiration_timestamp\n  registered_address\n  subdomain\n  token_standard\n  is_primary\n  owner_address\n  subdomain_expiration_policy\n  domain_expiration_timestamp\n}\n    ";
declare const GetNumberOfDelegators = "\n    query getNumberOfDelegators($where_condition: num_active_delegator_per_pool_bool_exp, $order_by: [num_active_delegator_per_pool_order_by!]) {\n  num_active_delegator_per_pool(where: $where_condition, order_by: $order_by) {\n    num_active_delegator\n    pool_address\n  }\n}\n    ";
declare const GetObjectData = "\n    query getObjectData($where_condition: current_objects_bool_exp, $offset: Int, $limit: Int, $order_by: [current_objects_order_by!]) {\n  current_objects(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    allow_ungated_transfer\n    state_key_hash\n    owner_address\n    object_address\n    last_transaction_version\n    last_guid_creation_num\n    is_deleted\n  }\n}\n    ";
declare const GetProcessorStatus = "\n    query getProcessorStatus($where_condition: processor_status_bool_exp) {\n  processor_status(where: $where_condition) {\n    last_success_version\n    processor\n    last_updated\n  }\n}\n    ";
declare const GetTableItemsData = "\n    query getTableItemsData($where_condition: table_items_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_items_order_by!]) {\n  table_items(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    decoded_key\n    decoded_value\n    key\n    table_handle\n    transaction_version\n    write_set_change_index\n  }\n}\n    ";
declare const GetTableItemsMetadata = "\n    query getTableItemsMetadata($where_condition: table_metadatas_bool_exp!, $offset: Int, $limit: Int, $order_by: [table_metadatas_order_by!]) {\n  table_metadatas(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    handle\n    key_type\n    value_type\n  }\n}\n    ";
declare const GetTokenActivity = "\n    query getTokenActivity($where_condition: token_activities_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [token_activities_v2_order_by!]) {\n  token_activities_v2(\n    where: $where_condition\n    order_by: $order_by\n    offset: $offset\n    limit: $limit\n  ) {\n    ...TokenActivitiesFields\n  }\n}\n    \n    fragment TokenActivitiesFields on token_activities_v2 {\n  after_value\n  before_value\n  entry_function_id_str\n  event_account_address\n  event_index\n  from_address\n  is_fungible_v2\n  property_version_v1\n  to_address\n  token_amount\n  token_data_id\n  token_standard\n  transaction_timestamp\n  transaction_version\n  type\n}\n    ";
declare const GetCurrentTokenOwnership = "\n    query getCurrentTokenOwnership($where_condition: current_token_ownerships_v2_bool_exp!, $offset: Int, $limit: Int, $order_by: [current_token_ownerships_v2_order_by!]) {\n  current_token_ownerships_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    ...CurrentTokenOwnershipFields\n  }\n}\n    \n    fragment CurrentTokenOwnershipFields on current_token_ownerships_v2 {\n  token_standard\n  token_properties_mutated_v1\n  token_data_id\n  table_type_v1\n  storage_id\n  property_version_v1\n  owner_address\n  last_transaction_version\n  last_transaction_timestamp\n  is_soulbound_v2\n  is_fungible_v2\n  amount\n  current_token_data {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
declare const GetTokenData = "\n    query getTokenData($where_condition: current_token_datas_v2_bool_exp, $offset: Int, $limit: Int, $order_by: [current_token_datas_v2_order_by!]) {\n  current_token_datas_v2(\n    where: $where_condition\n    offset: $offset\n    limit: $limit\n    order_by: $order_by\n  ) {\n    collection_id\n    description\n    is_fungible_v2\n    largest_property_version_v1\n    last_transaction_timestamp\n    last_transaction_version\n    maximum\n    supply\n    token_data_id\n    token_name\n    token_properties\n    token_standard\n    token_uri\n    decimals\n    current_collection {\n      collection_id\n      collection_name\n      creator_address\n      current_supply\n      description\n      last_transaction_timestamp\n      last_transaction_version\n      max_supply\n      mutable_description\n      mutable_uri\n      table_handle_v1\n      token_standard\n      total_minted_v2\n      uri\n    }\n  }\n}\n    ";
type SdkFunctionWrapper = <T>(action: (requestHeaders?: Record<string, string>) => Promise<T>, operationName: string, operationType?: string, variables?: any) => Promise<T>;
declare function getSdk(client: GraphQLClient, withWrapper?: SdkFunctionWrapper): {
    getAccountCoinsCount(variables?: GetAccountCoinsCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountCoinsCountQuery>;
    getAccountCoinsData(variables: GetAccountCoinsDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountCoinsDataQuery>;
    getAccountCollectionsWithOwnedTokens(variables: GetAccountCollectionsWithOwnedTokensQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountCollectionsWithOwnedTokensQuery>;
    getAccountOwnedTokens(variables: GetAccountOwnedTokensQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountOwnedTokensQuery>;
    getAccountOwnedTokensByTokenData(variables: GetAccountOwnedTokensByTokenDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountOwnedTokensByTokenDataQuery>;
    getAccountOwnedTokensFromCollection(variables: GetAccountOwnedTokensFromCollectionQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountOwnedTokensFromCollectionQuery>;
    getAccountTokensCount(variables?: GetAccountTokensCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountTokensCountQuery>;
    getAccountTransactionsCount(variables?: GetAccountTransactionsCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetAccountTransactionsCountQuery>;
    getChainTopUserTransactions(variables?: GetChainTopUserTransactionsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetChainTopUserTransactionsQuery>;
    getCollectionData(variables: GetCollectionDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCollectionDataQuery>;
    getCurrentFungibleAssetBalances(variables?: GetCurrentFungibleAssetBalancesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCurrentFungibleAssetBalancesQuery>;
    getDelegatedStakingActivities(variables?: GetDelegatedStakingActivitiesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetDelegatedStakingActivitiesQuery>;
    getEvents(variables?: GetEventsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetEventsQuery>;
    getFungibleAssetActivities(variables?: GetFungibleAssetActivitiesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetFungibleAssetActivitiesQuery>;
    getFungibleAssetMetadata(variables?: GetFungibleAssetMetadataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetFungibleAssetMetadataQuery>;
    getNames(variables?: GetNamesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetNamesQuery>;
    getNumberOfDelegators(variables?: GetNumberOfDelegatorsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetNumberOfDelegatorsQuery>;
    getObjectData(variables?: GetObjectDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetObjectDataQuery>;
    getProcessorStatus(variables?: GetProcessorStatusQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetProcessorStatusQuery>;
    getTableItemsData(variables: GetTableItemsDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetTableItemsDataQuery>;
    getTableItemsMetadata(variables: GetTableItemsMetadataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetTableItemsMetadataQuery>;
    getTokenActivity(variables: GetTokenActivityQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetTokenActivityQuery>;
    getCurrentTokenOwnership(variables: GetCurrentTokenOwnershipQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCurrentTokenOwnershipQuery>;
    getTokenData(variables?: GetTokenDataQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetTokenDataQuery>;
};
type Sdk = ReturnType<typeof getSdk>;

export { AnsTokenFragmentFragmentDoc, CurrentTokenOwnershipFieldsFragmentDoc, GetAccountCoinsCount, GetAccountCoinsData, GetAccountCollectionsWithOwnedTokens, GetAccountOwnedTokens, GetAccountOwnedTokensByTokenData, GetAccountOwnedTokensFromCollection, GetAccountTokensCount, GetAccountTransactionsCount, GetChainTopUserTransactions, GetCollectionData, GetCurrentFungibleAssetBalances, GetCurrentTokenOwnership, GetDelegatedStakingActivities, GetEvents, GetFungibleAssetActivities, GetFungibleAssetMetadata, GetNames, GetNumberOfDelegators, GetObjectData, GetProcessorStatus, GetTableItemsData, GetTableItemsMetadata, GetTokenActivity, GetTokenData, type Sdk, type SdkFunctionWrapper, TokenActivitiesFieldsFragmentDoc, getSdk };
