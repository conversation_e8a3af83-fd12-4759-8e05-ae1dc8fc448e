import{b as s}from"./chunk-STY74NUA.mjs";import{c as t}from"./chunk-56CNRT2K.mjs";var a=class d{constructor(e){this.buffer=new ArrayBuffer(e.length),new Uint8Array(this.buffer).set(e,0),this.offset=0}static fromHex(e){let i=s.hexInputToUint8Array(e);return new d(i)}read(e){if(this.offset+e>this.buffer.byteLength)throw new Error("Reached to the end of buffer");let i=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,i}remaining(){return this.buffer.byteLength-this.offset}assertFinished(){if(this.remaining()!==0)throw new Error("<PERSON><PERSON><PERSON> has remaining bytes")}deserializeStr(){let e=this.deserializeBytes();return new TextDecoder().decode(e)}deserializeOptionStr(){return this.deserializeOption("string")}deserializeOption(e,i){if(this.deserializeBool()){if(e==="string")return this.deserializeStr();if(e==="bytes")return this.deserializeBytes();if(e==="fixedBytes"){if(i===void 0)throw new Error("Fixed bytes length not provided");return this.deserializeFixedBytes(i)}return this.deserialize(e)}}deserializeBytes(){let e=this.deserializeUleb128AsU32();return new Uint8Array(this.read(e))}deserializeFixedBytes(e){return new Uint8Array(this.read(e))}deserializeBool(){let e=new Uint8Array(this.read(1))[0];if(e!==1&&e!==0)throw new Error("Invalid boolean value");return e===1}deserializeU8(){return new DataView(this.read(1)).getUint8(0)}deserializeU16(){return new DataView(this.read(2)).getUint16(0,!0)}deserializeU32(){return new DataView(this.read(4)).getUint32(0,!0)}deserializeU64(){let e=this.deserializeU32(),i=this.deserializeU32();return BigInt(BigInt(i)<<BigInt(32)|BigInt(e))}deserializeU128(){let e=this.deserializeU64(),i=this.deserializeU64();return BigInt(i<<BigInt(64)|e)}deserializeU256(){let e=this.deserializeU128(),i=this.deserializeU128();return BigInt(i<<BigInt(128)|e)}deserializeUleb128AsU32(){let e=BigInt(0),i=0;for(;e<t;){let r=this.deserializeU8();if(e|=BigInt(r&127)<<BigInt(i),(r&128)===0)break;i+=7}if(e>t)throw new Error("Overflow while parsing uleb128-encoded uint32 value");return Number(e)}deserialize(e){return e.deserialize(this)}deserializeVector(e){let i=this.deserializeUleb128AsU32(),r=new Array;for(let n=0;n<i;n+=1)r.push(this.deserialize(e));return r}};export{a};
//# sourceMappingURL=chunk-FLZPUYXQ.mjs.map