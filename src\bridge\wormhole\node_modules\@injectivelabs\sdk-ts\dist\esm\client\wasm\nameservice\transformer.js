import { binaryToBase64, fromBase64 } from '../../../utils/index.js';
export class InjNameServiceQueryTransformer {
    static resolverAddressResponseToResolverAddress(response) {
        const data = fromBase64(binaryToBase64(response.data));
        return data.resolver || '';
    }
    static injectiveAddressResponseToInjectiveAddress(response) {
        const data = fromBase64(binaryToBase64(response.data));
        return data.address || '';
    }
    static injectiveNameResponseToInjectiveName(response) {
        const data = fromBase64(binaryToBase64(response.data));
        return data.name || '';
    }
}
