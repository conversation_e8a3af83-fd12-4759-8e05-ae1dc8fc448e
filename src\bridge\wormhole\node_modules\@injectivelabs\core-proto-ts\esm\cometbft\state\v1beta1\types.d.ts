import _m0 from "protobufjs/minimal.js";
import { ResponseBeginBlock, ResponseDeliverTx, ResponseEndBlock } from "../../abci/v1beta1/types";
import { ConsensusParams } from "../../types/v1beta1/params";
import { BlockID } from "../../types/v1beta1/types";
import { ValidatorSet } from "../../types/v1beta1/validator";
import { Consensus } from "../../version/v1/types";
export declare const protobufPackage = "cometbft.state.v1beta1";
/**
 * ABCIResponses retains the responses
 * of the various ABCI calls during block processing.
 * It is persisted to disk for each height before calling Commit.
 */
export interface ABCIResponses {
    deliverTxs: ResponseDeliverTx[];
    endBlock: ResponseEndBlock | undefined;
    beginBlock: ResponseBeginBlock | undefined;
}
/** ValidatorsInfo represents the latest validator set, or the last height it changed */
export interface ValidatorsInfo {
    validatorSet: ValidatorSet | undefined;
    lastHeightChanged: string;
}
/** ConsensusParamsInfo represents the latest consensus params, or the last height it changed */
export interface ConsensusParamsInfo {
    consensusParams: ConsensusParams | undefined;
    lastHeightChanged: string;
}
/** ABCIResponsesInfo retains the responses of the ABCI calls during block processing. */
export interface ABCIResponsesInfo {
    abciResponses: ABCIResponses | undefined;
    height: string;
}
/** Version is a message for storing versioning information. */
export interface Version {
    consensus: Consensus | undefined;
    software: string;
}
/** State represents the state of the blockchain. */
export interface State {
    version: Version | undefined;
    /** immutable */
    chainId: string;
    initialHeight: string;
    /** LastBlockHeight=0 at genesis (ie. block(H=0) does not exist) */
    lastBlockHeight: string;
    lastBlockId: BlockID | undefined;
    lastBlockTime: Date | undefined;
    /**
     * LastValidators is used to validate block.LastCommit.
     * Validators are persisted to the database separately every time they change,
     * so we can query for historical validator sets.
     * Note that if s.LastBlockHeight causes a valset change,
     * we set s.LastHeightValidatorsChanged = s.LastBlockHeight + 1 + 1
     * Extra +1 due to nextValSet delay.
     */
    nextValidators: ValidatorSet | undefined;
    validators: ValidatorSet | undefined;
    lastValidators: ValidatorSet | undefined;
    lastHeightValidatorsChanged: string;
    /**
     * Consensus parameters used for validating blocks.
     * Changes returned by EndBlock and updated after Commit.
     */
    consensusParams: ConsensusParams | undefined;
    lastHeightConsensusParamsChanged: string;
    /** Merkle root of the results from executing prev block */
    lastResultsHash: Uint8Array;
    /** the latest AppHash we've received from calling abci.Commit() */
    appHash: Uint8Array;
}
export declare const ABCIResponses: {
    encode(message: ABCIResponses, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ABCIResponses;
    fromJSON(object: any): ABCIResponses;
    toJSON(message: ABCIResponses): unknown;
    create(base?: DeepPartial<ABCIResponses>): ABCIResponses;
    fromPartial(object: DeepPartial<ABCIResponses>): ABCIResponses;
};
export declare const ValidatorsInfo: {
    encode(message: ValidatorsInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ValidatorsInfo;
    fromJSON(object: any): ValidatorsInfo;
    toJSON(message: ValidatorsInfo): unknown;
    create(base?: DeepPartial<ValidatorsInfo>): ValidatorsInfo;
    fromPartial(object: DeepPartial<ValidatorsInfo>): ValidatorsInfo;
};
export declare const ConsensusParamsInfo: {
    encode(message: ConsensusParamsInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConsensusParamsInfo;
    fromJSON(object: any): ConsensusParamsInfo;
    toJSON(message: ConsensusParamsInfo): unknown;
    create(base?: DeepPartial<ConsensusParamsInfo>): ConsensusParamsInfo;
    fromPartial(object: DeepPartial<ConsensusParamsInfo>): ConsensusParamsInfo;
};
export declare const ABCIResponsesInfo: {
    encode(message: ABCIResponsesInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ABCIResponsesInfo;
    fromJSON(object: any): ABCIResponsesInfo;
    toJSON(message: ABCIResponsesInfo): unknown;
    create(base?: DeepPartial<ABCIResponsesInfo>): ABCIResponsesInfo;
    fromPartial(object: DeepPartial<ABCIResponsesInfo>): ABCIResponsesInfo;
};
export declare const Version: {
    encode(message: Version, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Version;
    fromJSON(object: any): Version;
    toJSON(message: Version): unknown;
    create(base?: DeepPartial<Version>): Version;
    fromPartial(object: DeepPartial<Version>): Version;
};
export declare const State: {
    encode(message: State, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): State;
    fromJSON(object: any): State;
    toJSON(message: State): unknown;
    create(base?: DeepPartial<State>): State;
    fromPartial(object: DeepPartial<State>): State;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
