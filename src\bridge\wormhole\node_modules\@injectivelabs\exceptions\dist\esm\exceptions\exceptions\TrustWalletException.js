import { ConcreteException } from '../base.js';
import { ErrorType } from '../types/index.js';
import { mapMetamaskMessage } from '../utils/maps.js';
const removeTrustWalletFromErrorString = (message) => message
    .replaceAll('TrustWallet', '')
    .replaceAll('Trust Wallet', '')
    .replaceAll('Trustwallet', '')
    .replaceAll('TrustWallet:', '')
    .replaceAll('Trust Wallet:', '');
export class TrustWalletException extends ConcreteException {
    static errorClass = 'TrustWalletException';
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage(mapMetamaskMessage(removeTrustWalletFromErrorString(message)));
        this.setName(TrustWalletException.errorClass);
    }
}
