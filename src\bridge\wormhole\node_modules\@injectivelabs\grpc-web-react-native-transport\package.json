{"name": "@injectivelabs/grpc-web-react-native-transport", "version": "0.0.2", "description": "Transport for use with @injectivelabs/grpc-web that works with React Native.", "main": "lib/index.js", "repository": {"type": "git", "url": "github.com/improbable-eng/grpc-web"}, "scripts": {"clean": "rm -rf lib", "postbootstrap": "npm run build", "build": "tsc"}, "publishConfig": {"access": "public"}, "author": "Improbable", "license": "Apache-2.0", "peerDependencies": {"@injectivelabs/grpc-web": ">=0.0.1"}, "devDependencies": {"@injectivelabs/grpc-web": "^0.0.1", "google-protobuf": "^3.14.0", "typescript": "^4.1.3"}}