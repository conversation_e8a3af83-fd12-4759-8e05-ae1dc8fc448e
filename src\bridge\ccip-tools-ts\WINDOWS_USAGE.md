# CCIP Tools - Windows 使用指南

本指南专门为Windows用户提供CCIP工具的使用说明，支持网络名称和简化命令。

## 🚀 快速开始

### 1. 环境要求

- **Node.js 18+**: [下载安装](https://nodejs.org/)
- **PowerShell 5.0+** 或 **命令提示符**
- **配置文件**: `config/rpc.yaml` 在项目根目录

### 2. 安装依赖

```cmd
cd src\bridge\ccip-tools-ts
npm install
```

### 3. 验证安装

```cmd
# 使用批处理文件
ccip.bat list-networks

# 或使用PowerShell
.\ccip.ps1 list-networks
```

## 📋 支持的网络名称

### 主网络
| 网络名称 | 别名 | 链ID | 描述 |
|---------|------|------|------|
| `ethereum` | `eth`, `mainnet` | 1 | 以太坊主网 |
| `polygon` | `matic` | 137 | Polygon主网 |
| `bsc` | `bnb`, `binance` | 56 | BSC主网 |
| `avalanche` | `avax` | 43114 | Avalanche主网 |
| `arbitrum` | `arb` | 42161 | Arbitrum主网 |
| `optimism` | `op` | 10 | Optimism主网 |
| `base` | - | 8453 | Base主网 |

### 测试网络
| 网络名称 | 别名 | 链ID | 描述 |
|---------|------|------|------|
| `sepolia` | `eth-sepolia` | 11155111 | Ethereum Sepolia |
| `fuji` | `avax-fuji`, `avalanche-fuji` | 43113 | Avalanche Fuji |
| `polygon-amoy` | `amoy`, `matic-amoy` | 80002 | Polygon Amoy |
| `bsc-testnet` | `bnb-testnet` | 97 | BSC测试网 |
| `arbitrum-sepolia` | `arb-sepolia` | 421614 | Arbitrum Sepolia |
| `optimism-sepolia` | `op-sepolia` | 11155420 | Optimism Sepolia |
| `base-sepolia` | - | 84532 | Base Sepolia |

## 🛠️ 使用方法

### 方式1: 批处理文件 (推荐)

```cmd
# 查看所有支持的网络
ccip.bat list-networks

# 查看CCIP消息信息
ccip.bat show 0x1234567890abcdef...

# 发送跨链消息
ccip.bat send ethereum 0xRouterAddress polygon --receiver 0xReceiverAddress

# 获取支持的代币
ccip.bat getSupportedTokens ethereum

# 手动执行消息
ccip.bat manualExec 0x1234567890abcdef...
```

### 方式2: PowerShell脚本

```powershell
# 查看所有支持的网络
.\ccip.ps1 list-networks

# 查看CCIP消息信息
.\ccip.ps1 show 0x1234567890abcdef...

# 发送跨链消息
.\ccip.ps1 send ethereum 0xRouterAddress polygon --receiver 0xReceiverAddress

# 获取支持的代币
.\ccip.ps1 getSupportedTokens ethereum
```

### 方式3: 直接使用 (高级用户)

```cmd
npx tsx src\index.ts --yaml-config ..\..\..\config\rpc.yaml send ethereum 0xRouter polygon --receiver 0xAddress
```

## 📝 常用命令示例

### 1. 查看网络信息

```cmd
# 列出所有支持的网络
ccip.bat list-networks

# 查看特定网络的支持代币
ccip.bat getSupportedTokens ethereum
ccip.bat getSupportedTokens polygon
ccip.bat getSupportedTokens sepolia
```

### 2. 发送跨链消息

```cmd
# 基本消息发送
ccip.bat send sepolia ****************************************** fuji --receiver 0xYourAddress

# 发送带数据的消息
ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --data "Hello Cross-Chain"

# 发送代币
ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --transfer-tokens 0xTokenAddress=1.5

# 指定Gas限制
ccip.bat send ethereum 0xRouter arbitrum --receiver 0xAddress --gas-limit 200000
```

### 3. 查看和管理消息

```cmd
# 查看消息状态
ccip.bat show 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# 手动执行失败的消息
ccip.bat manualExec 0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# 估算Gas费用
ccip.bat estimateGas sepolia 0xRouter fuji --receiver 0xAddress --data "test"
```

### 4. 高级功能

```cmd
# 使用详细日志
ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --verbose

# 指定费用代币
ccip.bat send sepolia 0xRouter fuji --receiver 0xAddress --fee-token 0xLinkTokenAddress

# 允许乱序执行
ccip.bat send ethereum 0xRouter arbitrum --receiver 0xAddress --allow-out-of-order-exec
```

## 🔧 配置文件

确保您的 `config/rpc.yaml` 文件包含所需网络的RPC配置：

```yaml
rpc:
  ethereum:
    rpc_url: "https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY"
    backup_rpc_urls:
      - "https://mainnet.infura.io/v3/YOUR_KEY"
      - "https://rpc.ankr.com/eth"
  
  polygon:
    endpoint: "https://polygon-mainnet.g.alchemy.com/v2/YOUR_KEY"
    backup_endpoints:
      - "https://polygon-rpc.com"
  
  avalanche-testnet-fuji:
    rpc_url: "https://api.avax-test.network/ext/bc/C/rpc"
```

## ❌ 故障排除

### 常见错误

1. **网络名称无效**
   ```
   ❌ Invalid source network: "etherem"
   💡 Did you mean: ethereum, ethereum-mainnet
   ```
   解决：使用 `ccip.bat list-networks` 查看正确的网络名称

2. **配置文件未找到**
   ```
   ❌ Configuration file not found: ..\..\..\config\rpc.yaml
   ```
   解决：确保配置文件路径正确

3. **RPC连接失败**
   ```
   ❌ Could not find provider for chain "ethereum" [1]
   ```
   解决：检查RPC配置和网络连接

### 调试技巧

```cmd
# 启用详细日志
ccip.bat send ethereum 0xRouter polygon --receiver 0xAddress --verbose

# 检查网络连接
ccip.bat getSupportedTokens ethereum

# 验证地址格式
# 确保所有地址都是有效的以太坊地址格式 (0x...)
```

## 🎯 最佳实践

1. **使用网络别名**: 使用简短的网络名称如 `eth`, `polygon`, `sepolia`
2. **验证地址**: 确保所有地址都是正确的格式
3. **测试网先试**: 在主网操作前先在测试网验证
4. **备份RPC**: 配置多个RPC端点以提高可靠性
5. **监控Gas**: 注意Gas费用，特别是在网络拥堵时

## 📚 更多资源

- [CCIP官方文档](https://docs.chain.link/ccip/)
- [RPC集成文档](./RPC_INTEGRATION.md)
- [网络配置示例](../../../config/rpc.yaml)

## 🆘 获取帮助

```cmd
# 查看命令帮助
ccip.bat --help

# 查看特定命令帮助
ccip.bat send --help

# 查看版本信息
ccip.bat --version
```
