{"version": 3, "file": "parseCommandOptions.js", "sourceRoot": "", "sources": ["../../bin/utils/parseCommandOptions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAEvD,MAAM,CAAC,MAAM,mBAAmB,GAAG,KAAK,EACtC,OAA8C,EAC9C,cAAwB,EACxB,EAAE;IAEF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,MAAM,UAAU,EAAE,CAAC;QACnB,OAAO;IACT,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,SAAS,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAGD,MAAM,IAAI,GAAG,eAAe,EAAE,CAAC;IAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACtB,MAAM,UAAU,EAAE,CAAC;IACrB,CAAC;IACD,MAAM,MAAM,CAAC,cAAc,CAAC,CAAC;AAC/B,CAAC,CAAC"}