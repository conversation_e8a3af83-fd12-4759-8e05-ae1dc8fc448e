import{a as c}from"./chunk-EBMEXURY.mjs";import{a as i}from"./chunk-IF4UU2MT.mjs";import{bytesToHex as a,hexToBytes as g}from"@noble/hashes/utils";var u=(s=>(s.INCORRECT_NUMBER_OF_BYTES="incorrect_number_of_bytes",s.INVALID_HEX_CHARS="invalid_hex_chars",s.TOO_SHORT="too_short",s.TOO_LONG="too_long",s.LEADING_ZERO_X_REQUIRED="leading_zero_x_required",s.LONG_FORM_REQUIRED_UNLESS_SPECIAL="long_form_required_unless_special",s.INVALID_PADDING_ZEROES="INVALID_PADDING_ZEROES",s.INVALID_PADDING_STRICTNESS="INVALID_PADDING_STRICTNESS",s))(u||{}),e=class e extends c{constructor(t){if(super(),t.length!==e.LENGTH)throw new i("AccountAddress data should be exactly 32 bytes long","incorrect_number_of_bytes");this.data=t}isSpecial(){return this.data.slice(0,this.data.length-1).every(t=>t===0)&&this.data[this.data.length-1]<16}toString(){return`0x${this.toStringWithoutPrefix()}`}toStringWithoutPrefix(){let t=a(this.data);return this.isSpecial()&&(t=t[t.length-1]),t}toStringLong(){return`0x${this.toStringLongWithoutPrefix()}`}toStringLongWithoutPrefix(){return a(this.data)}toStringShort(){return`0x${this.toStringShortWithoutPrefix()}`}toStringShortWithoutPrefix(){let t=a(this.data).replace(/^0+/,"");return t===""?"0":t}toUint8Array(){return this.data}serialize(t){t.serializeFixedBytes(this.data)}serializeForEntryFunction(t){let r=this.bcsToBytes();t.serializeBytes(r)}serializeForScriptFunction(t){t.serializeU32AsUleb128(3),t.serialize(this)}static deserialize(t){let r=t.deserializeFixedBytes(e.LENGTH);return new e(r)}static fromStringStrict(t){if(!t.startsWith("0x"))throw new i("Hex string must start with a leading 0x.","leading_zero_x_required");let r=e.fromString(t);if(t.length!==e.LONG_STRING_LENGTH+2)if(r.isSpecial()){if(t.length!==3)throw new i(`The given hex string ${t} is a special address not in LONG form, it must be 0x0 to 0xf without padding zeroes.`,"INVALID_PADDING_ZEROES")}else throw new i(`The given hex string ${t} is not a special address, it must be represented as 0x + 64 chars.`,"long_form_required_unless_special");return r}static fromString(t,{maxMissingChars:r=4}={}){let n=t;if(t.startsWith("0x")&&(n=t.slice(2)),n.length===0)throw new i("Hex string is too short, must be 1 to 64 chars long, excluding the leading 0x.","too_short");if(n.length>64)throw new i("Hex string is too long, must be 1 to 64 chars long, excluding the leading 0x.","too_long");if(r>63||r<0)throw new i(`maxMissingChars must be between or equal to 0 and 63. Received ${r}`,"INVALID_PADDING_STRICTNESS");let o;try{o=g(n.padStart(64,"0"))}catch(h){throw new i(`Hex characters are invalid: ${h?.message}`,"invalid_hex_chars")}let d=new e(o);if(n.length<64-r&&!d.isSpecial())throw new i(`Hex string is too short, must be ${64-r} to 64 chars long, excluding the leading 0x. You may need to fix 
the addresss by padding it with 0s before passing it to \`fromString\` (e.g. <addressString>.padStart(64, '0')). 
Received ${t}`,"too_short");return d}static from(t,{maxMissingChars:r=4}={}){return typeof t=="string"?e.fromString(t,{maxMissingChars:r}):t instanceof Uint8Array?new e(t):t}static fromStrict(t){return typeof t=="string"?e.fromStringStrict(t):t instanceof Uint8Array?new e(t):t}static isValid(t){try{return t.strict?e.fromStrict(t.input):e.from(t.input),{valid:!0}}catch(r){return{valid:!1,invalidReason:r?.invalidReason,invalidReasonMessage:r?.message}}}equals(t){return this.data.length!==t.data.length?!1:this.data.every((r,n)=>r===t.data[n])}};e.LENGTH=32,e.LONG_STRING_LENGTH=64,e.ZERO=e.from("0x0"),e.ONE=e.from("0x1"),e.TWO=e.from("0x2"),e.THREE=e.from("0x3"),e.FOUR=e.from("0x4"),e.A=e.from("0xA");var l=e;export{u as a,l as b};
//# sourceMappingURL=chunk-RGKRCZ36.mjs.map