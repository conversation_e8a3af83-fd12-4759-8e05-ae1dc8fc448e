import { HttpRequestException, UnspecifiedErrorCode, } from '@injectivelabs/exceptions';
import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { ChainModule } from '../types/index.js';
/**
 * @category Chain Wasm API
 */
export class ChainRestWasmApi extends BaseRestConsumer {
    async fetchSmartContractState(contractAddress, query, params = {}) {
        const endpoint = `cosmwasm/wasm/v1/contract/${contractAddress}/smart/${query}`;
        try {
            const response = await this.retry(() => this.get(endpoint, params));
            return response.data;
        }
        catch (e) {
            if (e instanceof HttpRequestException) {
                throw e;
            }
            throw new HttpRequestException(new Error(e), {
                code: UnspecifiedErrorCode,
                context: `${this.endpoint}/${endpoint}`,
                contextModule: ChainModule.Bank,
            });
        }
    }
}
