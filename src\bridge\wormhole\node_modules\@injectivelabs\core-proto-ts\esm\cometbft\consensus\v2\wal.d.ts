import _m0 from "protobufjs/minimal.js";
import { Duration } from "../../../google/protobuf/duration";
import { EventDataRoundState } from "../../types/v2/events";
import { Message } from "./types";
export declare const protobufPackage = "cometbft.consensus.v2";
/** MsgInfo are msgs from the reactor which may update the state */
export interface MsgInfo {
    msg: Message | undefined;
    peerId: string;
    receiveTime: Date | undefined;
}
/** TimeoutInfo internally generated messages which may update the state */
export interface TimeoutInfo {
    duration: Duration | undefined;
    height: string;
    round: number;
    step: number;
}
/**
 * EndHeight marks the end of the given height inside WAL.
 * @internal used by scripts/wal2json util.
 */
export interface EndHeight {
    height: string;
}
/** WALMessage describes a consensus WAL (Write Ahead Log) entry. */
export interface WALMessage {
    eventDataRoundState?: EventDataRoundState | undefined;
    msgInfo?: MsgInfo | undefined;
    timeoutInfo?: TimeoutInfo | undefined;
    endHeight?: EndHeight | undefined;
}
/** TimedWALMessage wraps WALMessage and adds Time for debugging purposes. */
export interface TimedWALMessage {
    time: Date | undefined;
    msg: WALMessage | undefined;
}
export declare const MsgInfo: {
    encode(message: MsgInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInfo;
    fromJSON(object: any): MsgInfo;
    toJSON(message: MsgInfo): unknown;
    create(base?: DeepPartial<MsgInfo>): MsgInfo;
    fromPartial(object: DeepPartial<MsgInfo>): MsgInfo;
};
export declare const TimeoutInfo: {
    encode(message: TimeoutInfo, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TimeoutInfo;
    fromJSON(object: any): TimeoutInfo;
    toJSON(message: TimeoutInfo): unknown;
    create(base?: DeepPartial<TimeoutInfo>): TimeoutInfo;
    fromPartial(object: DeepPartial<TimeoutInfo>): TimeoutInfo;
};
export declare const EndHeight: {
    encode(message: EndHeight, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EndHeight;
    fromJSON(object: any): EndHeight;
    toJSON(message: EndHeight): unknown;
    create(base?: DeepPartial<EndHeight>): EndHeight;
    fromPartial(object: DeepPartial<EndHeight>): EndHeight;
};
export declare const WALMessage: {
    encode(message: WALMessage, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): WALMessage;
    fromJSON(object: any): WALMessage;
    toJSON(message: WALMessage): unknown;
    create(base?: DeepPartial<WALMessage>): WALMessage;
    fromPartial(object: DeepPartial<WALMessage>): WALMessage;
};
export declare const TimedWALMessage: {
    encode(message: TimedWALMessage, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TimedWALMessage;
    fromJSON(object: any): TimedWALMessage;
    toJSON(message: TimedWALMessage): unknown;
    create(base?: DeepPartial<TimedWALMessage>): TimedWALMessage;
    fromPartial(object: DeepPartial<TimedWALMessage>): TimedWALMessage;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
