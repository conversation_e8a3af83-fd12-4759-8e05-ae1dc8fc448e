{"version": 3, "sources": ["../../src/core/crypto/proof.ts"], "sourcesContent": ["import { Serializable } from \"../../bcs\";\n\n/**\n * An abstract representation of a cryptographic proof associated with specific\n * zero-knowledge proof schemes, such as Groth16 and PLONK.\n * @group Implementation\n * @category Serialization\n */\nexport abstract class Proof extends Serializable {}\n"], "mappings": "oCAQO,IAAeA,EAAf,cAA6BC,CAAa,CAAC", "names": ["Proof", "Serializable"]}