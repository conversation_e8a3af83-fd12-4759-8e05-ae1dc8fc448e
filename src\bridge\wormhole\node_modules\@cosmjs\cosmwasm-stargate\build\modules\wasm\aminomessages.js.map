{"version": 3, "file": "aminomessages.js", "sourceRoot": "", "sources": ["../../../src/modules/wasm/aminomessages.ts"], "names": [], "mappings": ";;;AAAA,yDAAyD;AACzD,yCAA4C;AAC5C,+CAA0E;AAW1E,+DAA+E;AAE/E,SAAgB,oBAAoB,CAAC,GAAW;IAC9C,QAAQ,GAAG,EAAE;QACX,KAAK,aAAa;YAChB,OAAO,kBAAU,CAAC,uBAAuB,CAAC;QAC5C,KAAK,QAAQ;YACX,OAAO,kBAAU,CAAC,kBAAkB,CAAC;QACvC,KAAK,aAAa;YAChB,OAAO,kBAAU,CAAC,wBAAwB,CAAC;QAC7C,KAAK,WAAW;YACd,OAAO,kBAAU,CAAC,qBAAqB,CAAC;QAC1C,KAAK,gBAAgB;YACnB,OAAO,kBAAU,CAAC,4BAA4B,CAAC;QACjD;YACE,OAAO,kBAAU,CAAC,YAAY,CAAC;KAClC;AACH,CAAC;AAfD,oDAeC;AAED,SAAgB,kBAAkB,CAAC,MAAW;IAC5C,QAAQ,MAAM,EAAE;QACd,KAAK,kBAAU,CAAC,uBAAuB;YACrC,OAAO,aAAa,CAAC;QACvB,KAAK,kBAAU,CAAC,kBAAkB;YAChC,OAAO,QAAQ,CAAC;QAClB,KAAK,kBAAU,CAAC,wBAAwB;YACtC,OAAO,aAAa,CAAC;QACvB,KAAK,kBAAU,CAAC,qBAAqB;YACnC,OAAO,WAAW,CAAC;QACrB,KAAK,kBAAU,CAAC,4BAA4B;YAC1C,OAAO,gBAAgB,CAAC;QAC1B,KAAK,kBAAU,CAAC,YAAY,CAAC;QAC7B;YACE,OAAO,cAAc,CAAC;KACzB;AACH,CAAC;AAhBD,gDAgBC;AA2JD,SAAgB,yBAAyB;IACvC,OAAO;QACL,gCAAgC,EAAE;YAChC,SAAS,EAAE,mBAAmB;YAC9B,OAAO,EAAE,CAAC,EACR,MAAM,EACN,YAAY,EACZ,qBAAqB,GACR,EAA8B,EAAE,CAAC,CAAC;gBAC/C,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,IAAA,mBAAQ,EAAC,YAAY,CAAC;gBACtC,sBAAsB,EAAE,qBAAqB;oBAC3C,CAAC,CAAC;wBACE,UAAU,EAAE,kBAAkB,CAAC,qBAAqB,CAAC,UAAU,CAAC;wBAChE,OAAO,EAAE,qBAAqB,CAAC,OAAO,IAAI,SAAS;wBACnD,SAAS,EACP,qBAAqB,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;qBAC7F;oBACH,CAAC,CAAC,SAAS;aACd,CAAC;YACF,SAAS,EAAE,CAAC,EACV,MAAM,EACN,cAAc,EACd,sBAAsB,GACK,EAAgB,EAAE,CAAC,CAAC;gBAC/C,MAAM,EAAE,MAAM;gBACd,YAAY,EAAE,IAAA,qBAAU,EAAC,cAAc,CAAC;gBACxC,qBAAqB,EAAE,sBAAsB;oBAC3C,CAAC,CAAC,oBAAY,CAAC,WAAW,CAAC;wBACvB,UAAU,EAAE,oBAAoB,CAAC,sBAAsB,CAAC,UAAU,CAAC;wBACnE,OAAO,EAAE,sBAAsB,CAAC,OAAO,IAAI,EAAE;wBAC7C,SAAS,EAAE,sBAAsB,CAAC,SAAS,IAAI,EAAE;qBAClD,CAAC;oBACJ,CAAC,CAAC,SAAS;aACd,CAAC;SACH;QACD,0CAA0C,EAAE;YAC1C,SAAS,EAAE,6BAA6B;YACxC,OAAO,EAAE,CAAC,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,GAAG,EACH,KAAK,EACL,KAAK,GACkB,EAAwC,EAAE,CAAC,CAAC;gBACnE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE;gBAC1B,KAAK,EAAE,KAAK;gBACZ,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,GAAG,CAAC,CAAC;gBAC9B,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,IAAA,mBAAW,EAAC,KAAK,CAAC;aAC1B,CAAC;YACF,SAAS,EAAE,CAAC,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,GAAG,EACH,KAAK,EACL,KAAK,GACgC,EAA0B,EAAE,CAAC,CAAC;gBACnE,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;gBACvB,KAAK,EAAE,KAAK;gBACZ,GAAG,EAAE,IAAA,iBAAM,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAChC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;gBACjB,KAAK,EAAE,KAAK,IAAI,EAAE;aACnB,CAAC;SACH;QACD,2CAA2C,EAAE;YAC3C,SAAS,EAAE,8BAA8B;YACzC,OAAO,EAAE,CAAC,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,GAAG,EACH,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM,GACkB,EAAyC,EAAE,CAAC,CAAC;gBACrE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE;gBAC1B,KAAK,EAAE,KAAK;gBACZ,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,GAAG,CAAC,CAAC;gBAC9B,KAAK,EAAE,KAAK;gBACZ,KAAK,EAAE,IAAA,mBAAW,EAAC,KAAK,CAAC;gBACzB,IAAI,EAAE,IAAA,mBAAQ,EAAC,IAAI,CAAC;gBACpB,OAAO,EAAE,IAAA,mBAAW,EAAC,MAAM,CAAC;aAC7B,CAAC;YACF,SAAS,EAAE,CAAC,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,GAAG,EACH,KAAK,EACL,KAAK,EACL,IAAI,EACJ,OAAO,GAC+B,EAA2B,EAAE,CAAC,CAAC;gBACrE,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;gBACvB,KAAK,EAAE,KAAK;gBACZ,GAAG,EAAE,IAAA,iBAAM,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAChC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;gBACjB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAClB,IAAI,EAAE,IAAA,qBAAU,EAAC,IAAI,CAAC;gBACtB,MAAM,EAAE,OAAO,IAAI,KAAK;aACzB,CAAC;SACH;QACD,kCAAkC,EAAE;YAClC,SAAS,EAAE,qBAAqB;YAChC,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAkB,EAAgC,EAAE,CAAC,CAAC;gBAC1F,MAAM,EAAE,MAAM;gBACd,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,QAAQ;aACnB,CAAC;YACF,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAgC,EAAkB,EAAE,CAAC,CAAC;gBAC7F,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,SAAS;gBACnB,QAAQ,EAAE,QAAQ;aACnB,CAAC;SACH;QACD,iCAAiC,EAAE;YACjC,SAAS,EAAE,oBAAoB;YAC/B,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAiB,EAA+B,EAAE,CAAC,CAAC;gBAC9E,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;aACnB,CAAC;YACF,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAA+B,EAAiB,EAAE,CAAC,CAAC;gBAChF,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;aACnB,CAAC;SACH;QACD,sCAAsC,EAAE;YACtC,SAAS,EAAE,yBAAyB;YACpC,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAsB,EAAoC,EAAE,CAAC,CAAC;gBACpG,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,GAAG,CAAC,CAAC;gBAC9B,KAAK,EAAE,KAAK;aACb,CAAC;YACF,SAAS,EAAE,CAAC,EACV,MAAM,EACN,QAAQ,EACR,GAAG,EACH,KAAK,GAC4B,EAAsB,EAAE,CAAC,CAAC;gBAC3D,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;gBAClB,GAAG,EAAE,IAAA,iBAAM,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAChC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;aAClB,CAAC;SACH;QACD,sCAAsC,EAAE;YACtC,SAAS,EAAE,yBAAyB;YACpC,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAsB,EAAoC,EAAE,CAAC,CAAC;gBACrG,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE;gBAC1B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,mBAAQ,EAAC,GAAG,CAAC,CAAC;aAC/B,CAAC;YACF,SAAS,EAAE,CAAC,EACV,MAAM,EACN,QAAQ,EACR,OAAO,EACP,GAAG,GAC8B,EAAsB,EAAE,CAAC,CAAC;gBAC3D,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;gBACvB,GAAG,EAAE,IAAA,iBAAM,EAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACjC,CAAC;SACH;KACF,CAAC;AACJ,CAAC;AA/KD,8DA+KC"}