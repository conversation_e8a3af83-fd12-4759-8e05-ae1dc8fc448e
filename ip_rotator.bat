@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM IP轮换器批处理脚本
REM 使用方法: ip_rotator.bat [command] [options]

set PYTHON_SCRIPT=src\utils\ip\ip_rotator.py
set CONFIG_FILE=config\ip.yaml

REM 检查Python脚本是否存在
if not exist "%PYTHON_SCRIPT%" (
    echo Error: Cannot find Python script %PYTHON_SCRIPT%
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "%CONFIG_FILE%" (
    echo Error: Cannot find config file %CONFIG_FILE%
    exit /b 1
)

REM 如果没有参数，显示帮助
if "%1"=="" (
    goto :show_help
)

REM 处理命令
if /i "%1"=="start" goto :start
if /i "%1"=="stop" goto :stop
if /i "%1"=="status" goto :status
if /i "%1"=="test" goto :test
if /i "%1"=="switch" goto :switch
if /i "%1"=="list" goto :list
if /i "%1"=="help" goto :show_help

echo Unknown command: %1
goto :show_help

:start
echo 启动IP轮换器...
python "%PYTHON_SCRIPT%" start --config "%CONFIG_FILE%"
goto :end

:stop
echo 停止IP轮换器...
python "%PYTHON_SCRIPT%" stop --config "%CONFIG_FILE%"
goto :end

:status
echo 查询IP轮换器状态...
python "%PYTHON_SCRIPT%" status --config "%CONFIG_FILE%"
goto :end

:test
echo 测试代理连接...
python "%PYTHON_SCRIPT%" test --config "%CONFIG_FILE%"
goto :end

:switch
if "%2"=="" (
    echo 随机切换代理...
    python "%PYTHON_SCRIPT%" switch --config "%CONFIG_FILE%"
) else (
    echo 切换到代理: %2
    python "%PYTHON_SCRIPT%" switch --config "%CONFIG_FILE%" --name "%2"
)
goto :end

:list
echo 列出所有代理...
python "%PYTHON_SCRIPT%" list --config "%CONFIG_FILE%"
goto :end

:show_help
echo.
echo IP轮换器 - 自动切换代理IP
echo.
echo 使用方法:
echo   .\ip_rotator.bat [command] [options]
echo.
echo 可用命令:
echo   start          启动IP自动轮换（每分钟切换一次）
echo   stop           停止IP轮换
echo   status         显示当前状态
echo   test           测试所有代理连接
echo   switch [name]  手动切换代理（可指定代理名称）
echo   list           列出所有可用代理
echo   help           显示此帮助信息
echo.
echo 示例:
echo   .\ip_rotator.bat start                启动自动轮换
echo   .\ip_rotator.bat status               查看状态
echo   .\ip_rotator.bat switch 香港          切换到香港节点
echo   .\ip_rotator.bat test                 测试所有代理
echo.
goto :end

:end
endlocal
