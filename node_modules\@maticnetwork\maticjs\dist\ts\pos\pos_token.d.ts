import { BaseToken, Web3SideChainClient } from "../utils";
import { IContractInitParam, IPOSClientConfig, ITransactionOption } from "../interfaces";
import { IPOSContracts } from "../interfaces";
export declare class POSToken extends BaseToken<IPOSClientConfig> {
    protected getPOSContracts: () => IPOSContracts;
    private predicateAddress;
    constructor(contractParam: IContractInitParam, client: Web3SideChainClient<IPOSClientConfig>, getPOSContracts: () => IPOSContracts);
    protected get rootChainManager(): import("./root_chain_manager").RootChainManager;
    protected get gasSwapper(): import("./gas_swapper").GasSwapper;
    protected get exitUtil(): import("./exit_util").ExitUtil;
    getPredicateAddress(): Promise<string>;
    protected isWithdrawn(txHash: string, eventSignature: string): Promise<boolean>;
    protected isWithdrawnOnIndex(txHash: string, index: number, eventSignature: string): Promise<boolean>;
    protected withdrawExitPOS(burnTxHash: string, eventSignature: string, isFast: boolean, option: ITransactionOption): any;
}
