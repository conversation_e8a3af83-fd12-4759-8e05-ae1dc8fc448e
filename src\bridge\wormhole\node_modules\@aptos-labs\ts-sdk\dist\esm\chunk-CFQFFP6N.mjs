import{b as t,d as n}from"./chunk-UQWF24SS.mjs";import{a as u}from"./chunk-WSR5EBJM.mjs";import{a as l}from"./chunk-WCMW2L3P.mjs";import{a as o}from"./chunk-FLZPUYXQ.mjs";import{b as s}from"./chunk-STY74NUA.mjs";var c=class i extends u{constructor(e){super();let r=e.constructor.name;switch(r){case t.name:this.publicKey=e,this.variant=0;break;default:throw new Error(`Unsupported key for EphemeralPublicKey - ${r}`)}}verifySignature(e){let{message:r,signature:a}=e;return this.publicKey.verifySignature({message:r,signature:a.signature})}async verifySignatureAsync(e){return this.verifySignature(e)}serialize(e){if(this.publicKey instanceof t)e.serializeU32AsUleb128(0),this.publicKey.serialize(e);else throw new Error("Unknown public key type")}static deserialize(e){let r=e.deserializeUleb128AsU32();switch(r){case 0:return new i(t.deserialize(e));default:throw new Error(`Unknown variant index for EphemeralPublicKey: ${r}`)}}static isPublicKey(e){return e instanceof i}},p=class i extends l{constructor(e){super();let r=e.constructor.name;switch(r){case n.name:this.signature=e;break;default:throw new Error(`Unsupported signature for EphemeralSignature - ${r}`)}}static fromHex(e){let r=s.fromHexInput(e),a=new o(r.toUint8Array());return i.deserialize(a)}serialize(e){if(this.signature instanceof n)e.serializeU32AsUleb128(0),this.signature.serialize(e);else throw new Error("Unknown signature type")}static deserialize(e){let r=e.deserializeUleb128AsU32();switch(r){case 0:return new i(n.deserialize(e));default:throw new Error(`Unknown variant index for EphemeralSignature: ${r}`)}}};export{c as a,p as b};
//# sourceMappingURL=chunk-CFQFFP6N.mjs.map