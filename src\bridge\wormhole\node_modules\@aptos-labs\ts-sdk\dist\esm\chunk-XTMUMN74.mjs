import{a as t,b as n,c as i,d as r,e as o}from"./chunk-4RXKALLC.mjs";import f from"@aptos-labs/aptos-client";var u=class{constructor(e){if(e?.fullnode||e?.indexer||e?.faucet||e?.pepper||e?.prover){if(e?.network==="custom")console.info("Note: using CUSTOM network will require queries to lookup ChainId");else if(!e?.network)throw new Error("Custom endpoints require a network to be specified")}this.network=e?.network??"devnet",this.fullnode=e?.fullnode,this.faucet=e?.faucet,this.pepper=e?.pepper,this.prover=e?.prover,this.indexer=e?.indexer,this.client=e?.client??{provider:f},this.clientConfig=e?.clientConfig??{},this.fullnodeConfig=e?.fullnodeConfig??{},this.indexerConfig=e?.indexerConfig??{},this.faucetConfig=e?.faucetConfig??{}}getRequestUrl(e){switch(e){case"Fullnode":if(this.fullnode!==void 0)return this.fullnode;if(this.network==="custom")throw new Error("Please provide a custom full node url");return n[this.network];case"Faucet":if(this.faucet!==void 0)return this.faucet;if(this.network==="testnet")throw new Error("There is no way to programmatically mint testnet APT, you must use the minting site at https://aptos.dev/network/faucet");if(this.network==="mainnet")throw new Error("There is no mainnet faucet");if(this.network==="custom")throw new Error("Please provide a custom faucet url");return i[this.network];case"Indexer":if(this.indexer!==void 0)return this.indexer;if(this.network==="custom")throw new Error("Please provide a custom indexer url");return t[this.network];case"Pepper":if(this.pepper!==void 0)return this.pepper;if(this.network==="custom")throw new Error("Please provide a custom pepper service url");return r[this.network];case"Prover":if(this.prover!==void 0)return this.prover;if(this.network==="custom")throw new Error("Please provide a custom prover service url");return o[this.network];default:throw Error(`apiType ${e} is not supported`)}}isPepperServiceRequest(e){return r[this.network]===e}isProverServiceRequest(e){return o[this.network]===e}};export{u as a};
//# sourceMappingURL=chunk-XTMUMN74.mjs.map