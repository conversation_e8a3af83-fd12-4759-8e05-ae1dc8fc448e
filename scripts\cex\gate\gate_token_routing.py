#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
这个脚本用于从network_report.txt文件中提取polygon网络的所有代币合约地址，
然后使用KyberSwap的模拟交易功能测试双向交易可行性：
1. 1 USDT到目标代币的交易
2. 1 目标代币到USDT的交易
如果上述测试失败，则尝试：
1. 10 USDT到目标代币的交易
2. 10 目标代币到USDT的交易
如果仍然失败，最后尝试：
1. 100 USDT到目标代币的交易
2. 100 目标代币到USDT的交易

只有双向都能成功交易的代币才会被保存。
支持限制处理的代币数量。
"""

import os
import re
import json
import logging
import sys
import time
import asyncio
import argparse
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

# 导入swap模块
from src.dex.KyberSwap.swap import swap_tokens

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('data', 'cex', 'gate_info', 'token_routing_check.log'), encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 常量定义
NETWORK_REPORT_PATH = os.path.join('data', 'cex', 'gate_info', 'network_report_deposit_withdraw_enabled.txt')
OUTPUT_DIR = os.path.join('data', 'cex', 'gate_info')
RESULTS_PATH = os.path.join(OUTPUT_DIR, 'bidirectional_routable_tokens_polygon.json')
DECIMALS_FILE_PATH = os.path.join('data', 'utils', 'token', 'gate_tokens_with_decimals.json')

# USDT在Polygon上的合约地址
USDT_ADDRESS = '0xc2132d05d31c914a87c6611c10748aeb04b58e8f'  # Polygon USDT

# 默认配置
DEFAULT_CONFIG = {
    'max_workers': 5,         # 最大线程数
    'delay': 1.0,             # 请求间隔(秒)
    'max_retries': 3,         # 最大重试次数
    'chain': 'polygon',       # 区块链网络
    'usdt_address': USDT_ADDRESS,  # USDT地址
    'slippage': 0.5,          # 滑点百分比
    'token_limit': None,      # 处理代币数量限制，None表示处理所有代币
    'test_amounts': [1.0, 10.0, 100.0]  # 测试金额列表，从小到大尝试
}

def get_token_decimals_from_file(token_address: str, chain: str = "polygon") -> Optional[int]:
    """
    从本地文件获取代币精度
    
    Args:
        token_address: 代币地址
        chain: 链名称
        
    Returns:
        Optional[int]: 代币精度，如果未找到则返回None
    """
    try:
        if os.path.exists(DECIMALS_FILE_PATH):
            with open(DECIMALS_FILE_PATH, 'r', encoding='utf-8') as f:
                tokens_data = json.load(f)
                
            if chain in tokens_data:
                token_address_lower = token_address.lower()
                for token_info in tokens_data[chain]:
                    if token_info.get("contract_address", "").lower() == token_address_lower:
                        return token_info.get("decimals")
    except Exception as e:
        logger.warning(f"从文件读取代币精度失败: {str(e)}")
    return None

def extract_polygon_token_addresses() -> List[Dict]:
    """从network_report.txt文件中提取polygon网络的所有代币合约地址"""
    logger.info("开始提取polygon网络代币合约地址...")
    tokens = []
    in_polygon_section = False
    token_pattern = re.compile(r'\s*-\s*([\w/]+)\s*\(([\w]+)\)')
    contract_pattern = re.compile(r'\s*合约地址:\s*(0x[a-fA-F0-9]{40})')
    
    try:
        with open(NETWORK_REPORT_PATH, 'r', encoding='utf-8') as file:
            for line in file:
                if 'polygon 网络:' in line:
                    in_polygon_section = True
                    continue
                    
                if in_polygon_section:
                    if ' 网络:' in line and 'polygon 网络:' not in line:
                        in_polygon_section = False
                        break
                    
                    token_match = token_pattern.search(line)
                    if token_match:
                        token_name = token_match.group(1)
                        token_symbol = token_match.group(2)
                        
                        contract_line = next(file, '')
                        contract_match = contract_pattern.search(contract_line)
                        
                        if contract_match:
                            contract_address = contract_match.group(1)
                            if contract_address.lower() != USDT_ADDRESS.lower():
                                tokens.append({
                                    'name': token_name,
                                    'symbol': token_symbol,
                                    'address': contract_address
                                })
        
        logger.info(f"成功提取 {len(tokens)} 个polygon网络代币合约地址")
        return tokens
    except Exception as e:
        logger.error(f"提取polygon网络代币合约地址时出错: {str(e)}")
        return []

async def test_swap_with_amount(token_address: str, token_symbol: str, amount: float, is_usdt_to_token: bool, config: Dict) -> Tuple[bool, Dict]:
    """
    测试特定金额的交易
    
    Args:
        token_address: 代币地址
        token_symbol: 代币符号
        amount: 测试金额
        is_usdt_to_token: 是否是USDT到代币的交易
        config: 配置参数
        
    Returns:
        Tuple[bool, Dict]: (是否成功, 交易结果)
    """
    try:
        # 确定输入和输出代币
        token_in = USDT_ADDRESS if is_usdt_to_token else token_address
        token_out = token_address if is_usdt_to_token else USDT_ADDRESS
        
        # 获取代币精度（仅用于日志显示）
        token_decimals = get_token_decimals_from_file(token_address)
        if token_decimals is None:
            token_decimals = 18  # 默认精度
            logger.warning(f"未找到代币 {token_symbol} 的精度信息，使用默认值18")
        
        # 直接使用原始金额，不需要调整精度，因为swap_tokens函数会处理精度转换
        adjusted_amount = amount
        
        # 执行模拟交易
        result = await swap_tokens(
            chain=config['chain'],
            token_in=token_in,
            token_out=token_out,
            amount=adjusted_amount,
            slippage=config['slippage'],
            real=False,  # 始终使用模拟模式
            skip_confirmation=True
        )
        
        success = result.get("success", False)
        if success:
            logger.info(f"测试成功: {'USDT→' if is_usdt_to_token else ''}{token_symbol}{'→USDT' if not is_usdt_to_token else ''} ({amount})")
        else:
            logger.info(f"测试失败: {'USDT→' if is_usdt_to_token else ''}{token_symbol}{'→USDT' if not is_usdt_to_token else ''} ({amount})")
            
        return success, result
    except Exception as e:
        logger.error(f"测试交易时出错: {str(e)}")
        return False, {"error": str(e)}

async def check_bidirectional_routing(token: Dict, config: Dict) -> Tuple[bool, Dict]:
    """
    检查代币的双向交易可行性
    
    Args:
        token: 代币信息
        config: 配置参数
        
    Returns:
        Tuple[bool, Dict]: (是否双向可交易, 代币信息)
    """
    token_address = token['address']
    symbol = token['symbol']
    
    logger.info(f"\n开始测试代币 {symbol} ({token_address})")
    
    # 依次尝试不同的测试金额
    usdt_to_token_success = False
    token_to_usdt_success = False
    usdt_to_token_result = None
    token_to_usdt_result = None
    
    for amount in config['test_amounts']:
        if not usdt_to_token_success:
            logger.info(f"尝试 USDT→{symbol} 交易金额: {amount} USDT")
            usdt_to_token_success, usdt_to_token_result = await test_swap_with_amount(
                token_address, symbol, amount, True, config
            )
        
        if not token_to_usdt_success:
            logger.info(f"尝试 {symbol}→USDT 交易金额: {amount} {symbol}")
            token_to_usdt_success, token_to_usdt_result = await test_swap_with_amount(
                token_address, symbol, amount, False, config
            )
        
        # 如果双向都成功了，就不需要继续尝试更大的金额
        if usdt_to_token_success and token_to_usdt_success:
            break
        
        if not (usdt_to_token_success and token_to_usdt_success):
            logger.info(f"金额 {amount} 测试未完全成功，将尝试更大金额")
    
    # 最终结果
    success = usdt_to_token_success and token_to_usdt_success
    
    if success:
        logger.info(f"[成功] 代币 {symbol} 双向交易测试通过")
        # 更新代币信息
        token.update({
            "check_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "usdt_to_token_result": usdt_to_token_result,
            "token_to_usdt_result": token_to_usdt_result
        })
    else:
        logger.info(f"[失败] 代币 {symbol} 双向交易测试失败")
    
    return success, token

async def process_token_batch(batch: List[Dict], config: Dict) -> List[Dict]:
    """处理一批代币的双向交易检查"""
    bidirectional_routable_tokens = []
    
    for token in batch:
        max_retries = config['max_retries']
        retry_count = 0
        success = False
        
        while retry_count < max_retries and not success:
            try:
                bidirectional_routable, token_info = await check_bidirectional_routing(token, config)
                
                if bidirectional_routable:
                    # 只保存基本信息
                    simplified_token = {
                        'name': token['name'],
                        'symbol': token['symbol'],
                        'address': token['address']
                    }
                    bidirectional_routable_tokens.append(simplified_token)
                    logger.info(f"[双向成功] 代币 {token['symbol']} 已保存")
                    success = True
                else:
                    if retry_count == max_retries - 1:
                        logger.info(f"[双向失败] 代币 {token['symbol']} 未能双向交易")
                    else:
                        logger.info(f"尝试 #{retry_count+1} 失败，准备重试代币 {token['symbol']}")
                        await asyncio.sleep(1 + retry_count)
            except Exception as e:
                logger.warning(f"检查代币 {token['symbol']} 双向交易时发生错误: {str(e)}，尝试 #{retry_count+1}")
                if retry_count < max_retries - 1:
                    await asyncio.sleep(1 + retry_count)
            
            retry_count += 1
        
        # 添加延迟，避免请求频率过高
        await asyncio.sleep(config['delay'])
    
    return bidirectional_routable_tokens

def run_batch_worker(batch: List[Dict], config: Dict) -> List[Dict]:
    """运行单个批次的工作者函数"""
    return asyncio.run(process_token_batch(batch, config))

async def check_token_routings_concurrent(tokens: List[Dict], config: Dict) -> Dict:
    """并发检查所有代币的双向交易可行性"""
    # 如果设置了代币数量限制，只处理指定数量的代币
    if config['token_limit'] is not None:
        tokens = tokens[:config['token_limit']]
        logger.info(f"根据限制只处理前 {config['token_limit']} 个代币")
    
    tokens_per_worker = max(1, len(tokens) // config['max_workers'])
    batches = []
    
    for i in range(0, len(tokens), tokens_per_worker):
        batches.append(tokens[i:i + tokens_per_worker])
    
    logger.info(f"开始并发检查 {len(tokens)} 个代币的双向交易可行性...")
    logger.info(f"创建了 {len(batches)} 个批次，每个批次处理约 {tokens_per_worker} 个代币")
    
    bidirectional_routable_tokens = []
    
    with ThreadPoolExecutor(max_workers=config['max_workers']) as executor:
        futures = [executor.submit(run_batch_worker, batch, config) for batch in batches]
        
        for future in futures:
            batch_result = future.result()
            bidirectional_routable_tokens.extend(batch_result)
    
    results = {
        'network': 'polygon',
        'tokens': bidirectional_routable_tokens,
        'total_checked': len(tokens),
        'bidirectional_routable_count': len(bidirectional_routable_tokens),
        'config': {
            'test_amounts': config['test_amounts'],
            'token_limit': config['token_limit']
        }
    }
    
    return results

def save_results(results: Dict) -> None:
    """保存路由检查结果到文件"""
    try:
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        
        with open(RESULTS_PATH, 'w', encoding='utf-8') as file:
            json.dump(results, file, indent=2, ensure_ascii=False)
            
        logger.info(f"结果已保存到 {RESULTS_PATH}")
    except Exception as e:
        logger.error(f"保存结果时出错: {str(e)}")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='检查代币的双向交易可行性')
    parser.add_argument('--workers', type=int, default=DEFAULT_CONFIG['max_workers'],
                        help=f'最大工作线程数 (默认: {DEFAULT_CONFIG["max_workers"]})')
    parser.add_argument('--delay', type=float, default=DEFAULT_CONFIG['delay'],
                        help=f'请求间隔(秒) (默认: {DEFAULT_CONFIG["delay"]})')
    parser.add_argument('--retries', type=int, default=DEFAULT_CONFIG['max_retries'],
                        help=f'最大重试次数 (默认: {DEFAULT_CONFIG["max_retries"]})')
    parser.add_argument('--chain', type=str, default=DEFAULT_CONFIG['chain'],
                        help=f'区块链网络 (默认: {DEFAULT_CONFIG["chain"]})')
    parser.add_argument('--slippage', type=float, default=DEFAULT_CONFIG['slippage'],
                        help=f'滑点百分比 (默认: {DEFAULT_CONFIG["slippage"]})')
    parser.add_argument('--token-limit', type=int, default=DEFAULT_CONFIG['token_limit'],
                        help='限制处理的代币数量 (默认: 无限制)')
    parser.add_argument('--test-amounts', type=float, nargs='+',
                        default=DEFAULT_CONFIG['test_amounts'],
                        help='测试金额列表，从小到大排序 (默认: 1.0 10.0 100.0)')
    
    return parser.parse_args()

async def async_main():
    """异步主函数"""
    args = parse_args()
    
    # 确保测试金额是从小到大排序的
    test_amounts = sorted(args.test_amounts)
    
    config = {
        'max_workers': args.workers,
        'delay': args.delay,
        'max_retries': args.retries,
        'chain': args.chain,
        'slippage': args.slippage,
        'usdt_address': USDT_ADDRESS,
        'token_limit': args.token_limit,
        'test_amounts': test_amounts
    }
    
    logger.info("开始执行代币双向交易检查脚本")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    if config['token_limit']:
        logger.info(f"将只处理前 {config['token_limit']} 个代币")
    logger.info(f"测试金额序列: {config['test_amounts']}")
    
    tokens = extract_polygon_token_addresses()
    
    if not tokens:
        logger.error("未能找到任何polygon网络代币，程序退出")
        return
    
    start_time = time.time()
    
    results = await check_token_routings_concurrent(tokens, config)
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    save_results(results)
    
    logger.info(f"双向交易检查完成。总计检查 {results['total_checked']} 个代币，"
                f"双向可交易代币数量: {results['bidirectional_routable_count']}。")
    logger.info(f"耗时: {elapsed_time:.2f}秒")
    
    logger.info("脚本执行完毕")

def main():
    """主函数，启动异步主函数"""
    asyncio.run(async_main())

if __name__ == "__main__":
    main() 