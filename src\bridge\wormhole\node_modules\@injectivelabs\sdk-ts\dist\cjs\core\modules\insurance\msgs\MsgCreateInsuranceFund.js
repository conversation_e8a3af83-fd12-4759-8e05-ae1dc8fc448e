"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgCreateInsuranceFund extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgCreateInsuranceFund(params);
    }
    toProto() {
        const { params } = this;
        const coin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        coin.denom = params.deposit.denom;
        coin.amount = params.deposit.amount;
        const message = core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund.create();
        message.sender = params.injectiveAddress;
        message.ticker = params.fund.ticker;
        message.quoteDenom = params.fund.quoteDenom;
        message.oracleBase = params.fund.oracleBase;
        message.oracleQuote = params.fund.oracleQuote;
        message.oracleType = params.fund.oracleType;
        message.expiry = (params.fund.expiry ? params.fund.expiry : -1).toString();
        message.initialDeposit = coin;
        return core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.insurance.v1beta1.MsgCreateInsuranceFund',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'insurance/MsgCreateInsuranceFund',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.insurance.v1beta1.MsgCreateInsuranceFund',
            ...value,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            oracle_type: core_proto_ts_1.InjectiveOracleV1Beta1Oracle.oracleTypeToJSON(params.fund.oracleType),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.insurance.v1beta1.MsgCreateInsuranceFund',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveInsuranceV1Beta1Tx.MsgCreateInsuranceFund.encode(this.toProto()).finish();
    }
}
exports.default = MsgCreateInsuranceFund;
