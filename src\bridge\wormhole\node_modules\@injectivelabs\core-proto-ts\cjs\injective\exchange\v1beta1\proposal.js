"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DenomMinNotionalProposal = exports.AtomicMarketOrderFeeMultiplierScheduleProposal = exports.BatchCommunityPoolSpendProposal = exports.FeeDiscountProposal = exports.TradingRewardPendingPointsUpdateProposal = exports.RewardPointUpdate = exports.TradingRewardCampaignUpdateProposal = exports.TradingRewardCampaignLaunchProposal = exports.OracleParams = exports.ProviderOracleParams = exports.BinaryOptionsMarketParamUpdateProposal = exports.UpdateDenomDecimalsProposal = exports.MarketForcedSettlementProposal = exports.AdminInfo = exports.DerivativeMarketParamUpdateProposal = exports.ExpiryFuturesMarketLaunchProposal = exports.BinaryOptionsMarketLaunchProposal = exports.PerpetualMarketLaunchProposal = exports.SpotMarketLaunchProposal = exports.BatchExchangeModificationProposal = exports.ExchangeEnableProposal = exports.SpotMarketParamUpdateProposal = exports.ExchangeType = exports.protobufPackage = void 0;
exports.exchangeTypeFromJSON = exchangeTypeFromJSON;
exports.exchangeTypeToJSON = exchangeTypeToJSON;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var distribution_1 = require("../../../cosmos/distribution/v1beta1/distribution.js");
var oracle_1 = require("../../oracle/v1beta1/oracle.js");
var exchange_1 = require("./exchange.js");
exports.protobufPackage = "injective.exchange.v1beta1";
var ExchangeType;
(function (ExchangeType) {
    ExchangeType[ExchangeType["EXCHANGE_UNSPECIFIED"] = 0] = "EXCHANGE_UNSPECIFIED";
    ExchangeType[ExchangeType["SPOT"] = 1] = "SPOT";
    ExchangeType[ExchangeType["DERIVATIVES"] = 2] = "DERIVATIVES";
    ExchangeType[ExchangeType["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ExchangeType || (exports.ExchangeType = ExchangeType = {}));
function exchangeTypeFromJSON(object) {
    switch (object) {
        case 0:
        case "EXCHANGE_UNSPECIFIED":
            return ExchangeType.EXCHANGE_UNSPECIFIED;
        case 1:
        case "SPOT":
            return ExchangeType.SPOT;
        case 2:
        case "DERIVATIVES":
            return ExchangeType.DERIVATIVES;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ExchangeType.UNRECOGNIZED;
    }
}
function exchangeTypeToJSON(object) {
    switch (object) {
        case ExchangeType.EXCHANGE_UNSPECIFIED:
            return "EXCHANGE_UNSPECIFIED";
        case ExchangeType.SPOT:
            return "SPOT";
        case ExchangeType.DERIVATIVES:
            return "DERIVATIVES";
        case ExchangeType.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseSpotMarketParamUpdateProposal() {
    return {
        title: "",
        description: "",
        marketId: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        status: 0,
        ticker: "",
        minNotional: "",
        adminInfo: undefined,
        baseDecimals: 0,
        quoteDecimals: 0,
    };
}
exports.SpotMarketParamUpdateProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(34).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(42).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(50).string(message.relayerFeeShareRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(58).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(66).string(message.minQuantityTickSize);
        }
        if (message.status !== 0) {
            writer.uint32(72).int32(message.status);
        }
        if (message.ticker !== "") {
            writer.uint32(82).string(message.ticker);
        }
        if (message.minNotional !== "") {
            writer.uint32(90).string(message.minNotional);
        }
        if (message.adminInfo !== undefined) {
            exports.AdminInfo.encode(message.adminInfo, writer.uint32(98).fork()).ldelim();
        }
        if (message.baseDecimals !== 0) {
            writer.uint32(104).uint32(message.baseDecimals);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(112).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotMarketParamUpdateProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.makerFeeRate = reader.string();
                    break;
                case 5:
                    message.takerFeeRate = reader.string();
                    break;
                case 6:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 7:
                    message.minPriceTickSize = reader.string();
                    break;
                case 8:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 9:
                    message.status = reader.int32();
                    break;
                case 10:
                    message.ticker = reader.string();
                    break;
                case 11:
                    message.minNotional = reader.string();
                    break;
                case 12:
                    message.adminInfo = exports.AdminInfo.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.baseDecimals = reader.uint32();
                    break;
                case 14:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            status: isSet(object.status) ? (0, exchange_1.marketStatusFromJSON)(object.status) : 0,
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminInfo: isSet(object.adminInfo) ? exports.AdminInfo.fromJSON(object.adminInfo) : undefined,
            baseDecimals: isSet(object.baseDecimals) ? Number(object.baseDecimals) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.status !== undefined && (obj.status = (0, exchange_1.marketStatusToJSON)(message.status));
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminInfo !== undefined &&
            (obj.adminInfo = message.adminInfo ? exports.AdminInfo.toJSON(message.adminInfo) : undefined);
        message.baseDecimals !== undefined && (obj.baseDecimals = Math.round(message.baseDecimals));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.SpotMarketParamUpdateProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        var message = createBaseSpotMarketParamUpdateProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.makerFeeRate = (_d = object.makerFeeRate) !== null && _d !== void 0 ? _d : "";
        message.takerFeeRate = (_e = object.takerFeeRate) !== null && _e !== void 0 ? _e : "";
        message.relayerFeeShareRate = (_f = object.relayerFeeShareRate) !== null && _f !== void 0 ? _f : "";
        message.minPriceTickSize = (_g = object.minPriceTickSize) !== null && _g !== void 0 ? _g : "";
        message.minQuantityTickSize = (_h = object.minQuantityTickSize) !== null && _h !== void 0 ? _h : "";
        message.status = (_j = object.status) !== null && _j !== void 0 ? _j : 0;
        message.ticker = (_k = object.ticker) !== null && _k !== void 0 ? _k : "";
        message.minNotional = (_l = object.minNotional) !== null && _l !== void 0 ? _l : "";
        message.adminInfo = (object.adminInfo !== undefined && object.adminInfo !== null)
            ? exports.AdminInfo.fromPartial(object.adminInfo)
            : undefined;
        message.baseDecimals = (_m = object.baseDecimals) !== null && _m !== void 0 ? _m : 0;
        message.quoteDecimals = (_o = object.quoteDecimals) !== null && _o !== void 0 ? _o : 0;
        return message;
    },
};
function createBaseExchangeEnableProposal() {
    return { title: "", description: "", exchangeType: 0 };
}
exports.ExchangeEnableProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.exchangeType !== 0) {
            writer.uint32(24).int32(message.exchangeType);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExchangeEnableProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.exchangeType = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            exchangeType: isSet(object.exchangeType) ? exchangeTypeFromJSON(object.exchangeType) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.exchangeType !== undefined && (obj.exchangeType = exchangeTypeToJSON(message.exchangeType));
        return obj;
    },
    create: function (base) {
        return exports.ExchangeEnableProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseExchangeEnableProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.exchangeType = (_c = object.exchangeType) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBaseBatchExchangeModificationProposal() {
    return {
        title: "",
        description: "",
        spotMarketParamUpdateProposals: [],
        derivativeMarketParamUpdateProposals: [],
        spotMarketLaunchProposals: [],
        perpetualMarketLaunchProposals: [],
        expiryFuturesMarketLaunchProposals: [],
        tradingRewardCampaignUpdateProposal: undefined,
        binaryOptionsMarketLaunchProposals: [],
        binaryOptionsParamUpdateProposals: [],
        denomDecimalsUpdateProposal: undefined,
        feeDiscountProposal: undefined,
        marketForcedSettlementProposals: [],
        denomMinNotionalProposal: undefined,
    };
}
exports.BatchExchangeModificationProposal = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _j = __values(message.spotMarketParamUpdateProposals), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                exports.SpotMarketParamUpdateProposal.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_a = _j.return)) _a.call(_j);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _l = __values(message.derivativeMarketParamUpdateProposals), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                exports.DerivativeMarketParamUpdateProposal.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_b = _l.return)) _b.call(_l);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _o = __values(message.spotMarketLaunchProposals), _p = _o.next(); !_p.done; _p = _o.next()) {
                var v = _p.value;
                exports.SpotMarketLaunchProposal.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_p && !_p.done && (_c = _o.return)) _c.call(_o);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _q = __values(message.perpetualMarketLaunchProposals), _r = _q.next(); !_r.done; _r = _q.next()) {
                var v = _r.value;
                exports.PerpetualMarketLaunchProposal.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_r && !_r.done && (_d = _q.return)) _d.call(_q);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _s = __values(message.expiryFuturesMarketLaunchProposals), _t = _s.next(); !_t.done; _t = _s.next()) {
                var v = _t.value;
                exports.ExpiryFuturesMarketLaunchProposal.encode(v, writer.uint32(58).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_t && !_t.done && (_e = _s.return)) _e.call(_s);
            }
            finally { if (e_5) throw e_5.error; }
        }
        if (message.tradingRewardCampaignUpdateProposal !== undefined) {
            exports.TradingRewardCampaignUpdateProposal.encode(message.tradingRewardCampaignUpdateProposal, writer.uint32(66).fork())
                .ldelim();
        }
        try {
            for (var _u = __values(message.binaryOptionsMarketLaunchProposals), _v = _u.next(); !_v.done; _v = _u.next()) {
                var v = _v.value;
                exports.BinaryOptionsMarketLaunchProposal.encode(v, writer.uint32(74).fork()).ldelim();
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_v && !_v.done && (_f = _u.return)) _f.call(_u);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _w = __values(message.binaryOptionsParamUpdateProposals), _x = _w.next(); !_x.done; _x = _w.next()) {
                var v = _x.value;
                exports.BinaryOptionsMarketParamUpdateProposal.encode(v, writer.uint32(82).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_x && !_x.done && (_g = _w.return)) _g.call(_w);
            }
            finally { if (e_7) throw e_7.error; }
        }
        if (message.denomDecimalsUpdateProposal !== undefined) {
            exports.UpdateDenomDecimalsProposal.encode(message.denomDecimalsUpdateProposal, writer.uint32(90).fork()).ldelim();
        }
        if (message.feeDiscountProposal !== undefined) {
            exports.FeeDiscountProposal.encode(message.feeDiscountProposal, writer.uint32(98).fork()).ldelim();
        }
        try {
            for (var _y = __values(message.marketForcedSettlementProposals), _z = _y.next(); !_z.done; _z = _y.next()) {
                var v = _z.value;
                exports.MarketForcedSettlementProposal.encode(v, writer.uint32(106).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_z && !_z.done && (_h = _y.return)) _h.call(_y);
            }
            finally { if (e_8) throw e_8.error; }
        }
        if (message.denomMinNotionalProposal !== undefined) {
            exports.DenomMinNotionalProposal.encode(message.denomMinNotionalProposal, writer.uint32(114).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchExchangeModificationProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.spotMarketParamUpdateProposals.push(exports.SpotMarketParamUpdateProposal.decode(reader, reader.uint32()));
                    break;
                case 4:
                    message.derivativeMarketParamUpdateProposals.push(exports.DerivativeMarketParamUpdateProposal.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.spotMarketLaunchProposals.push(exports.SpotMarketLaunchProposal.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.perpetualMarketLaunchProposals.push(exports.PerpetualMarketLaunchProposal.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.expiryFuturesMarketLaunchProposals.push(exports.ExpiryFuturesMarketLaunchProposal.decode(reader, reader.uint32()));
                    break;
                case 8:
                    message.tradingRewardCampaignUpdateProposal = exports.TradingRewardCampaignUpdateProposal.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.binaryOptionsMarketLaunchProposals.push(exports.BinaryOptionsMarketLaunchProposal.decode(reader, reader.uint32()));
                    break;
                case 10:
                    message.binaryOptionsParamUpdateProposals.push(exports.BinaryOptionsMarketParamUpdateProposal.decode(reader, reader.uint32()));
                    break;
                case 11:
                    message.denomDecimalsUpdateProposal = exports.UpdateDenomDecimalsProposal.decode(reader, reader.uint32());
                    break;
                case 12:
                    message.feeDiscountProposal = exports.FeeDiscountProposal.decode(reader, reader.uint32());
                    break;
                case 13:
                    message.marketForcedSettlementProposals.push(exports.MarketForcedSettlementProposal.decode(reader, reader.uint32()));
                    break;
                case 14:
                    message.denomMinNotionalProposal = exports.DenomMinNotionalProposal.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            spotMarketParamUpdateProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarketParamUpdateProposals)
                ? object.spotMarketParamUpdateProposals.map(function (e) { return exports.SpotMarketParamUpdateProposal.fromJSON(e); })
                : [],
            derivativeMarketParamUpdateProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeMarketParamUpdateProposals)
                ? object.derivativeMarketParamUpdateProposals.map(function (e) { return exports.DerivativeMarketParamUpdateProposal.fromJSON(e); })
                : [],
            spotMarketLaunchProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.spotMarketLaunchProposals)
                ? object.spotMarketLaunchProposals.map(function (e) { return exports.SpotMarketLaunchProposal.fromJSON(e); })
                : [],
            perpetualMarketLaunchProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.perpetualMarketLaunchProposals)
                ? object.perpetualMarketLaunchProposals.map(function (e) { return exports.PerpetualMarketLaunchProposal.fromJSON(e); })
                : [],
            expiryFuturesMarketLaunchProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.expiryFuturesMarketLaunchProposals)
                ? object.expiryFuturesMarketLaunchProposals.map(function (e) { return exports.ExpiryFuturesMarketLaunchProposal.fromJSON(e); })
                : [],
            tradingRewardCampaignUpdateProposal: isSet(object.tradingRewardCampaignUpdateProposal)
                ? exports.TradingRewardCampaignUpdateProposal.fromJSON(object.tradingRewardCampaignUpdateProposal)
                : undefined,
            binaryOptionsMarketLaunchProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsMarketLaunchProposals)
                ? object.binaryOptionsMarketLaunchProposals.map(function (e) { return exports.BinaryOptionsMarketLaunchProposal.fromJSON(e); })
                : [],
            binaryOptionsParamUpdateProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.binaryOptionsParamUpdateProposals)
                ? object.binaryOptionsParamUpdateProposals.map(function (e) { return exports.BinaryOptionsMarketParamUpdateProposal.fromJSON(e); })
                : [],
            denomDecimalsUpdateProposal: isSet(object.denomDecimalsUpdateProposal)
                ? exports.UpdateDenomDecimalsProposal.fromJSON(object.denomDecimalsUpdateProposal)
                : undefined,
            feeDiscountProposal: isSet(object.feeDiscountProposal)
                ? exports.FeeDiscountProposal.fromJSON(object.feeDiscountProposal)
                : undefined,
            marketForcedSettlementProposals: Array.isArray(object === null || object === void 0 ? void 0 : object.marketForcedSettlementProposals)
                ? object.marketForcedSettlementProposals.map(function (e) { return exports.MarketForcedSettlementProposal.fromJSON(e); })
                : [],
            denomMinNotionalProposal: isSet(object.denomMinNotionalProposal)
                ? exports.DenomMinNotionalProposal.fromJSON(object.denomMinNotionalProposal)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.spotMarketParamUpdateProposals) {
            obj.spotMarketParamUpdateProposals = message.spotMarketParamUpdateProposals.map(function (e) {
                return e ? exports.SpotMarketParamUpdateProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.spotMarketParamUpdateProposals = [];
        }
        if (message.derivativeMarketParamUpdateProposals) {
            obj.derivativeMarketParamUpdateProposals = message.derivativeMarketParamUpdateProposals.map(function (e) {
                return e ? exports.DerivativeMarketParamUpdateProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.derivativeMarketParamUpdateProposals = [];
        }
        if (message.spotMarketLaunchProposals) {
            obj.spotMarketLaunchProposals = message.spotMarketLaunchProposals.map(function (e) {
                return e ? exports.SpotMarketLaunchProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.spotMarketLaunchProposals = [];
        }
        if (message.perpetualMarketLaunchProposals) {
            obj.perpetualMarketLaunchProposals = message.perpetualMarketLaunchProposals.map(function (e) {
                return e ? exports.PerpetualMarketLaunchProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.perpetualMarketLaunchProposals = [];
        }
        if (message.expiryFuturesMarketLaunchProposals) {
            obj.expiryFuturesMarketLaunchProposals = message.expiryFuturesMarketLaunchProposals.map(function (e) {
                return e ? exports.ExpiryFuturesMarketLaunchProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.expiryFuturesMarketLaunchProposals = [];
        }
        message.tradingRewardCampaignUpdateProposal !== undefined &&
            (obj.tradingRewardCampaignUpdateProposal = message.tradingRewardCampaignUpdateProposal
                ? exports.TradingRewardCampaignUpdateProposal.toJSON(message.tradingRewardCampaignUpdateProposal)
                : undefined);
        if (message.binaryOptionsMarketLaunchProposals) {
            obj.binaryOptionsMarketLaunchProposals = message.binaryOptionsMarketLaunchProposals.map(function (e) {
                return e ? exports.BinaryOptionsMarketLaunchProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.binaryOptionsMarketLaunchProposals = [];
        }
        if (message.binaryOptionsParamUpdateProposals) {
            obj.binaryOptionsParamUpdateProposals = message.binaryOptionsParamUpdateProposals.map(function (e) {
                return e ? exports.BinaryOptionsMarketParamUpdateProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.binaryOptionsParamUpdateProposals = [];
        }
        message.denomDecimalsUpdateProposal !== undefined &&
            (obj.denomDecimalsUpdateProposal = message.denomDecimalsUpdateProposal
                ? exports.UpdateDenomDecimalsProposal.toJSON(message.denomDecimalsUpdateProposal)
                : undefined);
        message.feeDiscountProposal !== undefined && (obj.feeDiscountProposal = message.feeDiscountProposal
            ? exports.FeeDiscountProposal.toJSON(message.feeDiscountProposal)
            : undefined);
        if (message.marketForcedSettlementProposals) {
            obj.marketForcedSettlementProposals = message.marketForcedSettlementProposals.map(function (e) {
                return e ? exports.MarketForcedSettlementProposal.toJSON(e) : undefined;
            });
        }
        else {
            obj.marketForcedSettlementProposals = [];
        }
        message.denomMinNotionalProposal !== undefined && (obj.denomMinNotionalProposal = message.denomMinNotionalProposal
            ? exports.DenomMinNotionalProposal.toJSON(message.denomMinNotionalProposal)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.BatchExchangeModificationProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        var message = createBaseBatchExchangeModificationProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.spotMarketParamUpdateProposals =
            ((_c = object.spotMarketParamUpdateProposals) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exports.SpotMarketParamUpdateProposal.fromPartial(e); })) || [];
        message.derivativeMarketParamUpdateProposals =
            ((_d = object.derivativeMarketParamUpdateProposals) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.DerivativeMarketParamUpdateProposal.fromPartial(e); })) || [];
        message.spotMarketLaunchProposals =
            ((_e = object.spotMarketLaunchProposals) === null || _e === void 0 ? void 0 : _e.map(function (e) { return exports.SpotMarketLaunchProposal.fromPartial(e); })) || [];
        message.perpetualMarketLaunchProposals =
            ((_f = object.perpetualMarketLaunchProposals) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.PerpetualMarketLaunchProposal.fromPartial(e); })) || [];
        message.expiryFuturesMarketLaunchProposals =
            ((_g = object.expiryFuturesMarketLaunchProposals) === null || _g === void 0 ? void 0 : _g.map(function (e) { return exports.ExpiryFuturesMarketLaunchProposal.fromPartial(e); })) || [];
        message.tradingRewardCampaignUpdateProposal =
            (object.tradingRewardCampaignUpdateProposal !== undefined && object.tradingRewardCampaignUpdateProposal !== null)
                ? exports.TradingRewardCampaignUpdateProposal.fromPartial(object.tradingRewardCampaignUpdateProposal)
                : undefined;
        message.binaryOptionsMarketLaunchProposals =
            ((_h = object.binaryOptionsMarketLaunchProposals) === null || _h === void 0 ? void 0 : _h.map(function (e) { return exports.BinaryOptionsMarketLaunchProposal.fromPartial(e); })) || [];
        message.binaryOptionsParamUpdateProposals =
            ((_j = object.binaryOptionsParamUpdateProposals) === null || _j === void 0 ? void 0 : _j.map(function (e) { return exports.BinaryOptionsMarketParamUpdateProposal.fromPartial(e); })) || [];
        message.denomDecimalsUpdateProposal =
            (object.denomDecimalsUpdateProposal !== undefined && object.denomDecimalsUpdateProposal !== null)
                ? exports.UpdateDenomDecimalsProposal.fromPartial(object.denomDecimalsUpdateProposal)
                : undefined;
        message.feeDiscountProposal = (object.feeDiscountProposal !== undefined && object.feeDiscountProposal !== null)
            ? exports.FeeDiscountProposal.fromPartial(object.feeDiscountProposal)
            : undefined;
        message.marketForcedSettlementProposals =
            ((_k = object.marketForcedSettlementProposals) === null || _k === void 0 ? void 0 : _k.map(function (e) { return exports.MarketForcedSettlementProposal.fromPartial(e); })) || [];
        message.denomMinNotionalProposal =
            (object.denomMinNotionalProposal !== undefined && object.denomMinNotionalProposal !== null)
                ? exports.DenomMinNotionalProposal.fromPartial(object.denomMinNotionalProposal)
                : undefined;
        return message;
    },
};
function createBaseSpotMarketLaunchProposal() {
    return {
        title: "",
        description: "",
        ticker: "",
        baseDenom: "",
        quoteDenom: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        makerFeeRate: "",
        takerFeeRate: "",
        minNotional: "",
        adminInfo: undefined,
        baseDecimals: 0,
        quoteDecimals: 0,
    };
}
exports.SpotMarketLaunchProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.ticker !== "") {
            writer.uint32(26).string(message.ticker);
        }
        if (message.baseDenom !== "") {
            writer.uint32(34).string(message.baseDenom);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(42).string(message.quoteDenom);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(50).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(58).string(message.minQuantityTickSize);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(66).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(74).string(message.takerFeeRate);
        }
        if (message.minNotional !== "") {
            writer.uint32(82).string(message.minNotional);
        }
        if (message.adminInfo !== undefined) {
            exports.AdminInfo.encode(message.adminInfo, writer.uint32(90).fork()).ldelim();
        }
        if (message.baseDecimals !== 0) {
            writer.uint32(112).uint32(message.baseDecimals);
        }
        if (message.quoteDecimals !== 0) {
            writer.uint32(120).uint32(message.quoteDecimals);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSpotMarketLaunchProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.ticker = reader.string();
                    break;
                case 4:
                    message.baseDenom = reader.string();
                    break;
                case 5:
                    message.quoteDenom = reader.string();
                    break;
                case 6:
                    message.minPriceTickSize = reader.string();
                    break;
                case 7:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 8:
                    message.makerFeeRate = reader.string();
                    break;
                case 9:
                    message.takerFeeRate = reader.string();
                    break;
                case 10:
                    message.minNotional = reader.string();
                    break;
                case 11:
                    message.adminInfo = exports.AdminInfo.decode(reader, reader.uint32());
                    break;
                case 14:
                    message.baseDecimals = reader.uint32();
                    break;
                case 15:
                    message.quoteDecimals = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            baseDenom: isSet(object.baseDenom) ? String(object.baseDenom) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminInfo: isSet(object.adminInfo) ? exports.AdminInfo.fromJSON(object.adminInfo) : undefined,
            baseDecimals: isSet(object.baseDecimals) ? Number(object.baseDecimals) : 0,
            quoteDecimals: isSet(object.quoteDecimals) ? Number(object.quoteDecimals) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.baseDenom !== undefined && (obj.baseDenom = message.baseDenom);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminInfo !== undefined &&
            (obj.adminInfo = message.adminInfo ? exports.AdminInfo.toJSON(message.adminInfo) : undefined);
        message.baseDecimals !== undefined && (obj.baseDecimals = Math.round(message.baseDecimals));
        message.quoteDecimals !== undefined && (obj.quoteDecimals = Math.round(message.quoteDecimals));
        return obj;
    },
    create: function (base) {
        return exports.SpotMarketLaunchProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        var message = createBaseSpotMarketLaunchProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.ticker = (_c = object.ticker) !== null && _c !== void 0 ? _c : "";
        message.baseDenom = (_d = object.baseDenom) !== null && _d !== void 0 ? _d : "";
        message.quoteDenom = (_e = object.quoteDenom) !== null && _e !== void 0 ? _e : "";
        message.minPriceTickSize = (_f = object.minPriceTickSize) !== null && _f !== void 0 ? _f : "";
        message.minQuantityTickSize = (_g = object.minQuantityTickSize) !== null && _g !== void 0 ? _g : "";
        message.makerFeeRate = (_h = object.makerFeeRate) !== null && _h !== void 0 ? _h : "";
        message.takerFeeRate = (_j = object.takerFeeRate) !== null && _j !== void 0 ? _j : "";
        message.minNotional = (_k = object.minNotional) !== null && _k !== void 0 ? _k : "";
        message.adminInfo = (object.adminInfo !== undefined && object.adminInfo !== null)
            ? exports.AdminInfo.fromPartial(object.adminInfo)
            : undefined;
        message.baseDecimals = (_l = object.baseDecimals) !== null && _l !== void 0 ? _l : 0;
        message.quoteDecimals = (_m = object.quoteDecimals) !== null && _m !== void 0 ? _m : 0;
        return message;
    },
};
function createBasePerpetualMarketLaunchProposal() {
    return {
        title: "",
        description: "",
        ticker: "",
        quoteDenom: "",
        oracleBase: "",
        oracleQuote: "",
        oracleScaleFactor: 0,
        oracleType: 0,
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        makerFeeRate: "",
        takerFeeRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        adminInfo: undefined,
    };
}
exports.PerpetualMarketLaunchProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.ticker !== "") {
            writer.uint32(26).string(message.ticker);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(34).string(message.quoteDenom);
        }
        if (message.oracleBase !== "") {
            writer.uint32(42).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(50).string(message.oracleQuote);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(56).uint32(message.oracleScaleFactor);
        }
        if (message.oracleType !== 0) {
            writer.uint32(64).int32(message.oracleType);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(74).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(82).string(message.maintenanceMarginRatio);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(90).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(98).string(message.takerFeeRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(106).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(114).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(122).string(message.minNotional);
        }
        if (message.adminInfo !== undefined) {
            exports.AdminInfo.encode(message.adminInfo, writer.uint32(130).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePerpetualMarketLaunchProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.ticker = reader.string();
                    break;
                case 4:
                    message.quoteDenom = reader.string();
                    break;
                case 5:
                    message.oracleBase = reader.string();
                    break;
                case 6:
                    message.oracleQuote = reader.string();
                    break;
                case 7:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 8:
                    message.oracleType = reader.int32();
                    break;
                case 9:
                    message.initialMarginRatio = reader.string();
                    break;
                case 10:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 11:
                    message.makerFeeRate = reader.string();
                    break;
                case 12:
                    message.takerFeeRate = reader.string();
                    break;
                case 13:
                    message.minPriceTickSize = reader.string();
                    break;
                case 14:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 15:
                    message.minNotional = reader.string();
                    break;
                case 16:
                    message.adminInfo = exports.AdminInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminInfo: isSet(object.adminInfo) ? exports.AdminInfo.fromJSON(object.adminInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminInfo !== undefined &&
            (obj.adminInfo = message.adminInfo ? exports.AdminInfo.toJSON(message.adminInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.PerpetualMarketLaunchProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBasePerpetualMarketLaunchProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.ticker = (_c = object.ticker) !== null && _c !== void 0 ? _c : "";
        message.quoteDenom = (_d = object.quoteDenom) !== null && _d !== void 0 ? _d : "";
        message.oracleBase = (_e = object.oracleBase) !== null && _e !== void 0 ? _e : "";
        message.oracleQuote = (_f = object.oracleQuote) !== null && _f !== void 0 ? _f : "";
        message.oracleScaleFactor = (_g = object.oracleScaleFactor) !== null && _g !== void 0 ? _g : 0;
        message.oracleType = (_h = object.oracleType) !== null && _h !== void 0 ? _h : 0;
        message.initialMarginRatio = (_j = object.initialMarginRatio) !== null && _j !== void 0 ? _j : "";
        message.maintenanceMarginRatio = (_k = object.maintenanceMarginRatio) !== null && _k !== void 0 ? _k : "";
        message.makerFeeRate = (_l = object.makerFeeRate) !== null && _l !== void 0 ? _l : "";
        message.takerFeeRate = (_m = object.takerFeeRate) !== null && _m !== void 0 ? _m : "";
        message.minPriceTickSize = (_o = object.minPriceTickSize) !== null && _o !== void 0 ? _o : "";
        message.minQuantityTickSize = (_p = object.minQuantityTickSize) !== null && _p !== void 0 ? _p : "";
        message.minNotional = (_q = object.minNotional) !== null && _q !== void 0 ? _q : "";
        message.adminInfo = (object.adminInfo !== undefined && object.adminInfo !== null)
            ? exports.AdminInfo.fromPartial(object.adminInfo)
            : undefined;
        return message;
    },
};
function createBaseBinaryOptionsMarketLaunchProposal() {
    return {
        title: "",
        description: "",
        ticker: "",
        oracleSymbol: "",
        oracleProvider: "",
        oracleType: 0,
        oracleScaleFactor: 0,
        expirationTimestamp: "0",
        settlementTimestamp: "0",
        admin: "",
        quoteDenom: "",
        makerFeeRate: "",
        takerFeeRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        adminPermissions: 0,
    };
}
exports.BinaryOptionsMarketLaunchProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.ticker !== "") {
            writer.uint32(26).string(message.ticker);
        }
        if (message.oracleSymbol !== "") {
            writer.uint32(34).string(message.oracleSymbol);
        }
        if (message.oracleProvider !== "") {
            writer.uint32(42).string(message.oracleProvider);
        }
        if (message.oracleType !== 0) {
            writer.uint32(48).int32(message.oracleType);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(56).uint32(message.oracleScaleFactor);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(64).int64(message.expirationTimestamp);
        }
        if (message.settlementTimestamp !== "0") {
            writer.uint32(72).int64(message.settlementTimestamp);
        }
        if (message.admin !== "") {
            writer.uint32(82).string(message.admin);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(90).string(message.quoteDenom);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(98).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(106).string(message.takerFeeRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(114).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(122).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(130).string(message.minNotional);
        }
        if (message.adminPermissions !== 0) {
            writer.uint32(136).uint32(message.adminPermissions);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBinaryOptionsMarketLaunchProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.ticker = reader.string();
                    break;
                case 4:
                    message.oracleSymbol = reader.string();
                    break;
                case 5:
                    message.oracleProvider = reader.string();
                    break;
                case 6:
                    message.oracleType = reader.int32();
                    break;
                case 7:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 8:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 9:
                    message.settlementTimestamp = longToString(reader.int64());
                    break;
                case 10:
                    message.admin = reader.string();
                    break;
                case 11:
                    message.quoteDenom = reader.string();
                    break;
                case 12:
                    message.makerFeeRate = reader.string();
                    break;
                case 13:
                    message.takerFeeRate = reader.string();
                    break;
                case 14:
                    message.minPriceTickSize = reader.string();
                    break;
                case 15:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 16:
                    message.minNotional = reader.string();
                    break;
                case 17:
                    message.adminPermissions = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            oracleSymbol: isSet(object.oracleSymbol) ? String(object.oracleSymbol) : "",
            oracleProvider: isSet(object.oracleProvider) ? String(object.oracleProvider) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            settlementTimestamp: isSet(object.settlementTimestamp) ? String(object.settlementTimestamp) : "0",
            admin: isSet(object.admin) ? String(object.admin) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminPermissions: isSet(object.adminPermissions) ? Number(object.adminPermissions) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.oracleSymbol !== undefined && (obj.oracleSymbol = message.oracleSymbol);
        message.oracleProvider !== undefined && (obj.oracleProvider = message.oracleProvider);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.settlementTimestamp !== undefined && (obj.settlementTimestamp = message.settlementTimestamp);
        message.admin !== undefined && (obj.admin = message.admin);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminPermissions !== undefined && (obj.adminPermissions = Math.round(message.adminPermissions));
        return obj;
    },
    create: function (base) {
        return exports.BinaryOptionsMarketLaunchProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
        var message = createBaseBinaryOptionsMarketLaunchProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.ticker = (_c = object.ticker) !== null && _c !== void 0 ? _c : "";
        message.oracleSymbol = (_d = object.oracleSymbol) !== null && _d !== void 0 ? _d : "";
        message.oracleProvider = (_e = object.oracleProvider) !== null && _e !== void 0 ? _e : "";
        message.oracleType = (_f = object.oracleType) !== null && _f !== void 0 ? _f : 0;
        message.oracleScaleFactor = (_g = object.oracleScaleFactor) !== null && _g !== void 0 ? _g : 0;
        message.expirationTimestamp = (_h = object.expirationTimestamp) !== null && _h !== void 0 ? _h : "0";
        message.settlementTimestamp = (_j = object.settlementTimestamp) !== null && _j !== void 0 ? _j : "0";
        message.admin = (_k = object.admin) !== null && _k !== void 0 ? _k : "";
        message.quoteDenom = (_l = object.quoteDenom) !== null && _l !== void 0 ? _l : "";
        message.makerFeeRate = (_m = object.makerFeeRate) !== null && _m !== void 0 ? _m : "";
        message.takerFeeRate = (_o = object.takerFeeRate) !== null && _o !== void 0 ? _o : "";
        message.minPriceTickSize = (_p = object.minPriceTickSize) !== null && _p !== void 0 ? _p : "";
        message.minQuantityTickSize = (_q = object.minQuantityTickSize) !== null && _q !== void 0 ? _q : "";
        message.minNotional = (_r = object.minNotional) !== null && _r !== void 0 ? _r : "";
        message.adminPermissions = (_s = object.adminPermissions) !== null && _s !== void 0 ? _s : 0;
        return message;
    },
};
function createBaseExpiryFuturesMarketLaunchProposal() {
    return {
        title: "",
        description: "",
        ticker: "",
        quoteDenom: "",
        oracleBase: "",
        oracleQuote: "",
        oracleScaleFactor: 0,
        oracleType: 0,
        expiry: "0",
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        makerFeeRate: "",
        takerFeeRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        minNotional: "",
        adminInfo: undefined,
    };
}
exports.ExpiryFuturesMarketLaunchProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.ticker !== "") {
            writer.uint32(26).string(message.ticker);
        }
        if (message.quoteDenom !== "") {
            writer.uint32(34).string(message.quoteDenom);
        }
        if (message.oracleBase !== "") {
            writer.uint32(42).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(50).string(message.oracleQuote);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(56).uint32(message.oracleScaleFactor);
        }
        if (message.oracleType !== 0) {
            writer.uint32(64).int32(message.oracleType);
        }
        if (message.expiry !== "0") {
            writer.uint32(72).int64(message.expiry);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(82).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(90).string(message.maintenanceMarginRatio);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(98).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(106).string(message.takerFeeRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(114).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(122).string(message.minQuantityTickSize);
        }
        if (message.minNotional !== "") {
            writer.uint32(130).string(message.minNotional);
        }
        if (message.adminInfo !== undefined) {
            exports.AdminInfo.encode(message.adminInfo, writer.uint32(138).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseExpiryFuturesMarketLaunchProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.ticker = reader.string();
                    break;
                case 4:
                    message.quoteDenom = reader.string();
                    break;
                case 5:
                    message.oracleBase = reader.string();
                    break;
                case 6:
                    message.oracleQuote = reader.string();
                    break;
                case 7:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 8:
                    message.oracleType = reader.int32();
                    break;
                case 9:
                    message.expiry = longToString(reader.int64());
                    break;
                case 10:
                    message.initialMarginRatio = reader.string();
                    break;
                case 11:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 12:
                    message.makerFeeRate = reader.string();
                    break;
                case 13:
                    message.takerFeeRate = reader.string();
                    break;
                case 14:
                    message.minPriceTickSize = reader.string();
                    break;
                case 15:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 16:
                    message.minNotional = reader.string();
                    break;
                case 17:
                    message.adminInfo = exports.AdminInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            quoteDenom: isSet(object.quoteDenom) ? String(object.quoteDenom) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            expiry: isSet(object.expiry) ? String(object.expiry) : "0",
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminInfo: isSet(object.adminInfo) ? exports.AdminInfo.fromJSON(object.adminInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.quoteDenom !== undefined && (obj.quoteDenom = message.quoteDenom);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.expiry !== undefined && (obj.expiry = message.expiry);
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminInfo !== undefined &&
            (obj.adminInfo = message.adminInfo ? exports.AdminInfo.toJSON(message.adminInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.ExpiryFuturesMarketLaunchProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;
        var message = createBaseExpiryFuturesMarketLaunchProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.ticker = (_c = object.ticker) !== null && _c !== void 0 ? _c : "";
        message.quoteDenom = (_d = object.quoteDenom) !== null && _d !== void 0 ? _d : "";
        message.oracleBase = (_e = object.oracleBase) !== null && _e !== void 0 ? _e : "";
        message.oracleQuote = (_f = object.oracleQuote) !== null && _f !== void 0 ? _f : "";
        message.oracleScaleFactor = (_g = object.oracleScaleFactor) !== null && _g !== void 0 ? _g : 0;
        message.oracleType = (_h = object.oracleType) !== null && _h !== void 0 ? _h : 0;
        message.expiry = (_j = object.expiry) !== null && _j !== void 0 ? _j : "0";
        message.initialMarginRatio = (_k = object.initialMarginRatio) !== null && _k !== void 0 ? _k : "";
        message.maintenanceMarginRatio = (_l = object.maintenanceMarginRatio) !== null && _l !== void 0 ? _l : "";
        message.makerFeeRate = (_m = object.makerFeeRate) !== null && _m !== void 0 ? _m : "";
        message.takerFeeRate = (_o = object.takerFeeRate) !== null && _o !== void 0 ? _o : "";
        message.minPriceTickSize = (_p = object.minPriceTickSize) !== null && _p !== void 0 ? _p : "";
        message.minQuantityTickSize = (_q = object.minQuantityTickSize) !== null && _q !== void 0 ? _q : "";
        message.minNotional = (_r = object.minNotional) !== null && _r !== void 0 ? _r : "";
        message.adminInfo = (object.adminInfo !== undefined && object.adminInfo !== null)
            ? exports.AdminInfo.fromPartial(object.adminInfo)
            : undefined;
        return message;
    },
};
function createBaseDerivativeMarketParamUpdateProposal() {
    return {
        title: "",
        description: "",
        marketId: "",
        initialMarginRatio: "",
        maintenanceMarginRatio: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        HourlyInterestRate: "",
        HourlyFundingRateCap: "",
        status: 0,
        oracleParams: undefined,
        ticker: "",
        minNotional: "",
        adminInfo: undefined,
    };
}
exports.DerivativeMarketParamUpdateProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.initialMarginRatio !== "") {
            writer.uint32(34).string(message.initialMarginRatio);
        }
        if (message.maintenanceMarginRatio !== "") {
            writer.uint32(42).string(message.maintenanceMarginRatio);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(50).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(58).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(66).string(message.relayerFeeShareRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(74).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(82).string(message.minQuantityTickSize);
        }
        if (message.HourlyInterestRate !== "") {
            writer.uint32(90).string(message.HourlyInterestRate);
        }
        if (message.HourlyFundingRateCap !== "") {
            writer.uint32(98).string(message.HourlyFundingRateCap);
        }
        if (message.status !== 0) {
            writer.uint32(104).int32(message.status);
        }
        if (message.oracleParams !== undefined) {
            exports.OracleParams.encode(message.oracleParams, writer.uint32(114).fork()).ldelim();
        }
        if (message.ticker !== "") {
            writer.uint32(122).string(message.ticker);
        }
        if (message.minNotional !== "") {
            writer.uint32(130).string(message.minNotional);
        }
        if (message.adminInfo !== undefined) {
            exports.AdminInfo.encode(message.adminInfo, writer.uint32(138).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDerivativeMarketParamUpdateProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.initialMarginRatio = reader.string();
                    break;
                case 5:
                    message.maintenanceMarginRatio = reader.string();
                    break;
                case 6:
                    message.makerFeeRate = reader.string();
                    break;
                case 7:
                    message.takerFeeRate = reader.string();
                    break;
                case 8:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 9:
                    message.minPriceTickSize = reader.string();
                    break;
                case 10:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 11:
                    message.HourlyInterestRate = reader.string();
                    break;
                case 12:
                    message.HourlyFundingRateCap = reader.string();
                    break;
                case 13:
                    message.status = reader.int32();
                    break;
                case 14:
                    message.oracleParams = exports.OracleParams.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.ticker = reader.string();
                    break;
                case 16:
                    message.minNotional = reader.string();
                    break;
                case 17:
                    message.adminInfo = exports.AdminInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            initialMarginRatio: isSet(object.initialMarginRatio) ? String(object.initialMarginRatio) : "",
            maintenanceMarginRatio: isSet(object.maintenanceMarginRatio) ? String(object.maintenanceMarginRatio) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            HourlyInterestRate: isSet(object.HourlyInterestRate) ? String(object.HourlyInterestRate) : "",
            HourlyFundingRateCap: isSet(object.HourlyFundingRateCap) ? String(object.HourlyFundingRateCap) : "",
            status: isSet(object.status) ? (0, exchange_1.marketStatusFromJSON)(object.status) : 0,
            oracleParams: isSet(object.oracleParams) ? exports.OracleParams.fromJSON(object.oracleParams) : undefined,
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
            adminInfo: isSet(object.adminInfo) ? exports.AdminInfo.fromJSON(object.adminInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.initialMarginRatio !== undefined && (obj.initialMarginRatio = message.initialMarginRatio);
        message.maintenanceMarginRatio !== undefined && (obj.maintenanceMarginRatio = message.maintenanceMarginRatio);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.HourlyInterestRate !== undefined && (obj.HourlyInterestRate = message.HourlyInterestRate);
        message.HourlyFundingRateCap !== undefined && (obj.HourlyFundingRateCap = message.HourlyFundingRateCap);
        message.status !== undefined && (obj.status = (0, exchange_1.marketStatusToJSON)(message.status));
        message.oracleParams !== undefined &&
            (obj.oracleParams = message.oracleParams ? exports.OracleParams.toJSON(message.oracleParams) : undefined);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        message.adminInfo !== undefined &&
            (obj.adminInfo = message.adminInfo ? exports.AdminInfo.toJSON(message.adminInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.DerivativeMarketParamUpdateProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseDerivativeMarketParamUpdateProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.initialMarginRatio = (_d = object.initialMarginRatio) !== null && _d !== void 0 ? _d : "";
        message.maintenanceMarginRatio = (_e = object.maintenanceMarginRatio) !== null && _e !== void 0 ? _e : "";
        message.makerFeeRate = (_f = object.makerFeeRate) !== null && _f !== void 0 ? _f : "";
        message.takerFeeRate = (_g = object.takerFeeRate) !== null && _g !== void 0 ? _g : "";
        message.relayerFeeShareRate = (_h = object.relayerFeeShareRate) !== null && _h !== void 0 ? _h : "";
        message.minPriceTickSize = (_j = object.minPriceTickSize) !== null && _j !== void 0 ? _j : "";
        message.minQuantityTickSize = (_k = object.minQuantityTickSize) !== null && _k !== void 0 ? _k : "";
        message.HourlyInterestRate = (_l = object.HourlyInterestRate) !== null && _l !== void 0 ? _l : "";
        message.HourlyFundingRateCap = (_m = object.HourlyFundingRateCap) !== null && _m !== void 0 ? _m : "";
        message.status = (_o = object.status) !== null && _o !== void 0 ? _o : 0;
        message.oracleParams = (object.oracleParams !== undefined && object.oracleParams !== null)
            ? exports.OracleParams.fromPartial(object.oracleParams)
            : undefined;
        message.ticker = (_p = object.ticker) !== null && _p !== void 0 ? _p : "";
        message.minNotional = (_q = object.minNotional) !== null && _q !== void 0 ? _q : "";
        message.adminInfo = (object.adminInfo !== undefined && object.adminInfo !== null)
            ? exports.AdminInfo.fromPartial(object.adminInfo)
            : undefined;
        return message;
    },
};
function createBaseAdminInfo() {
    return { admin: "", adminPermissions: 0 };
}
exports.AdminInfo = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.admin !== "") {
            writer.uint32(10).string(message.admin);
        }
        if (message.adminPermissions !== 0) {
            writer.uint32(16).uint32(message.adminPermissions);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAdminInfo();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.admin = reader.string();
                    break;
                case 2:
                    message.adminPermissions = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            admin: isSet(object.admin) ? String(object.admin) : "",
            adminPermissions: isSet(object.adminPermissions) ? Number(object.adminPermissions) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.admin !== undefined && (obj.admin = message.admin);
        message.adminPermissions !== undefined && (obj.adminPermissions = Math.round(message.adminPermissions));
        return obj;
    },
    create: function (base) {
        return exports.AdminInfo.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseAdminInfo();
        message.admin = (_a = object.admin) !== null && _a !== void 0 ? _a : "";
        message.adminPermissions = (_b = object.adminPermissions) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseMarketForcedSettlementProposal() {
    return { title: "", description: "", marketId: "", settlementPrice: "" };
}
exports.MarketForcedSettlementProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(34).string(message.settlementPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMarketForcedSettlementProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.settlementPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        return obj;
    },
    create: function (base) {
        return exports.MarketForcedSettlementProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMarketForcedSettlementProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.settlementPrice = (_d = object.settlementPrice) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseUpdateDenomDecimalsProposal() {
    return { title: "", description: "", denomDecimals: [] };
}
exports.UpdateDenomDecimalsProposal = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.denomDecimals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DenomDecimals.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseUpdateDenomDecimalsProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.denomDecimals.push(exchange_1.DenomDecimals.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            denomDecimals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomDecimals)
                ? object.denomDecimals.map(function (e) { return exchange_1.DenomDecimals.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.denomDecimals) {
            obj.denomDecimals = message.denomDecimals.map(function (e) { return e ? exchange_1.DenomDecimals.toJSON(e) : undefined; });
        }
        else {
            obj.denomDecimals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.UpdateDenomDecimalsProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseUpdateDenomDecimalsProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.denomDecimals = ((_c = object.denomDecimals) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.DenomDecimals.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseBinaryOptionsMarketParamUpdateProposal() {
    return {
        title: "",
        description: "",
        marketId: "",
        makerFeeRate: "",
        takerFeeRate: "",
        relayerFeeShareRate: "",
        minPriceTickSize: "",
        minQuantityTickSize: "",
        expirationTimestamp: "0",
        settlementTimestamp: "0",
        settlementPrice: "",
        admin: "",
        status: 0,
        oracleParams: undefined,
        ticker: "",
        minNotional: "",
    };
}
exports.BinaryOptionsMarketParamUpdateProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.marketId !== "") {
            writer.uint32(26).string(message.marketId);
        }
        if (message.makerFeeRate !== "") {
            writer.uint32(34).string(message.makerFeeRate);
        }
        if (message.takerFeeRate !== "") {
            writer.uint32(42).string(message.takerFeeRate);
        }
        if (message.relayerFeeShareRate !== "") {
            writer.uint32(50).string(message.relayerFeeShareRate);
        }
        if (message.minPriceTickSize !== "") {
            writer.uint32(58).string(message.minPriceTickSize);
        }
        if (message.minQuantityTickSize !== "") {
            writer.uint32(66).string(message.minQuantityTickSize);
        }
        if (message.expirationTimestamp !== "0") {
            writer.uint32(72).int64(message.expirationTimestamp);
        }
        if (message.settlementTimestamp !== "0") {
            writer.uint32(80).int64(message.settlementTimestamp);
        }
        if (message.settlementPrice !== "") {
            writer.uint32(90).string(message.settlementPrice);
        }
        if (message.admin !== "") {
            writer.uint32(98).string(message.admin);
        }
        if (message.status !== 0) {
            writer.uint32(104).int32(message.status);
        }
        if (message.oracleParams !== undefined) {
            exports.ProviderOracleParams.encode(message.oracleParams, writer.uint32(114).fork()).ldelim();
        }
        if (message.ticker !== "") {
            writer.uint32(122).string(message.ticker);
        }
        if (message.minNotional !== "") {
            writer.uint32(130).string(message.minNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBinaryOptionsMarketParamUpdateProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.marketId = reader.string();
                    break;
                case 4:
                    message.makerFeeRate = reader.string();
                    break;
                case 5:
                    message.takerFeeRate = reader.string();
                    break;
                case 6:
                    message.relayerFeeShareRate = reader.string();
                    break;
                case 7:
                    message.minPriceTickSize = reader.string();
                    break;
                case 8:
                    message.minQuantityTickSize = reader.string();
                    break;
                case 9:
                    message.expirationTimestamp = longToString(reader.int64());
                    break;
                case 10:
                    message.settlementTimestamp = longToString(reader.int64());
                    break;
                case 11:
                    message.settlementPrice = reader.string();
                    break;
                case 12:
                    message.admin = reader.string();
                    break;
                case 13:
                    message.status = reader.int32();
                    break;
                case 14:
                    message.oracleParams = exports.ProviderOracleParams.decode(reader, reader.uint32());
                    break;
                case 15:
                    message.ticker = reader.string();
                    break;
                case 16:
                    message.minNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            makerFeeRate: isSet(object.makerFeeRate) ? String(object.makerFeeRate) : "",
            takerFeeRate: isSet(object.takerFeeRate) ? String(object.takerFeeRate) : "",
            relayerFeeShareRate: isSet(object.relayerFeeShareRate) ? String(object.relayerFeeShareRate) : "",
            minPriceTickSize: isSet(object.minPriceTickSize) ? String(object.minPriceTickSize) : "",
            minQuantityTickSize: isSet(object.minQuantityTickSize) ? String(object.minQuantityTickSize) : "",
            expirationTimestamp: isSet(object.expirationTimestamp) ? String(object.expirationTimestamp) : "0",
            settlementTimestamp: isSet(object.settlementTimestamp) ? String(object.settlementTimestamp) : "0",
            settlementPrice: isSet(object.settlementPrice) ? String(object.settlementPrice) : "",
            admin: isSet(object.admin) ? String(object.admin) : "",
            status: isSet(object.status) ? (0, exchange_1.marketStatusFromJSON)(object.status) : 0,
            oracleParams: isSet(object.oracleParams) ? exports.ProviderOracleParams.fromJSON(object.oracleParams) : undefined,
            ticker: isSet(object.ticker) ? String(object.ticker) : "",
            minNotional: isSet(object.minNotional) ? String(object.minNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.makerFeeRate !== undefined && (obj.makerFeeRate = message.makerFeeRate);
        message.takerFeeRate !== undefined && (obj.takerFeeRate = message.takerFeeRate);
        message.relayerFeeShareRate !== undefined && (obj.relayerFeeShareRate = message.relayerFeeShareRate);
        message.minPriceTickSize !== undefined && (obj.minPriceTickSize = message.minPriceTickSize);
        message.minQuantityTickSize !== undefined && (obj.minQuantityTickSize = message.minQuantityTickSize);
        message.expirationTimestamp !== undefined && (obj.expirationTimestamp = message.expirationTimestamp);
        message.settlementTimestamp !== undefined && (obj.settlementTimestamp = message.settlementTimestamp);
        message.settlementPrice !== undefined && (obj.settlementPrice = message.settlementPrice);
        message.admin !== undefined && (obj.admin = message.admin);
        message.status !== undefined && (obj.status = (0, exchange_1.marketStatusToJSON)(message.status));
        message.oracleParams !== undefined &&
            (obj.oracleParams = message.oracleParams ? exports.ProviderOracleParams.toJSON(message.oracleParams) : undefined);
        message.ticker !== undefined && (obj.ticker = message.ticker);
        message.minNotional !== undefined && (obj.minNotional = message.minNotional);
        return obj;
    },
    create: function (base) {
        return exports.BinaryOptionsMarketParamUpdateProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q;
        var message = createBaseBinaryOptionsMarketParamUpdateProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.marketId = (_c = object.marketId) !== null && _c !== void 0 ? _c : "";
        message.makerFeeRate = (_d = object.makerFeeRate) !== null && _d !== void 0 ? _d : "";
        message.takerFeeRate = (_e = object.takerFeeRate) !== null && _e !== void 0 ? _e : "";
        message.relayerFeeShareRate = (_f = object.relayerFeeShareRate) !== null && _f !== void 0 ? _f : "";
        message.minPriceTickSize = (_g = object.minPriceTickSize) !== null && _g !== void 0 ? _g : "";
        message.minQuantityTickSize = (_h = object.minQuantityTickSize) !== null && _h !== void 0 ? _h : "";
        message.expirationTimestamp = (_j = object.expirationTimestamp) !== null && _j !== void 0 ? _j : "0";
        message.settlementTimestamp = (_k = object.settlementTimestamp) !== null && _k !== void 0 ? _k : "0";
        message.settlementPrice = (_l = object.settlementPrice) !== null && _l !== void 0 ? _l : "";
        message.admin = (_m = object.admin) !== null && _m !== void 0 ? _m : "";
        message.status = (_o = object.status) !== null && _o !== void 0 ? _o : 0;
        message.oracleParams = (object.oracleParams !== undefined && object.oracleParams !== null)
            ? exports.ProviderOracleParams.fromPartial(object.oracleParams)
            : undefined;
        message.ticker = (_p = object.ticker) !== null && _p !== void 0 ? _p : "";
        message.minNotional = (_q = object.minNotional) !== null && _q !== void 0 ? _q : "";
        return message;
    },
};
function createBaseProviderOracleParams() {
    return { symbol: "", provider: "", oracleScaleFactor: 0, oracleType: 0 };
}
exports.ProviderOracleParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.symbol !== "") {
            writer.uint32(10).string(message.symbol);
        }
        if (message.provider !== "") {
            writer.uint32(18).string(message.provider);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(24).uint32(message.oracleScaleFactor);
        }
        if (message.oracleType !== 0) {
            writer.uint32(32).int32(message.oracleType);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseProviderOracleParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.symbol = reader.string();
                    break;
                case 2:
                    message.provider = reader.string();
                    break;
                case 3:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 4:
                    message.oracleType = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            symbol: isSet(object.symbol) ? String(object.symbol) : "",
            provider: isSet(object.provider) ? String(object.provider) : "",
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.symbol !== undefined && (obj.symbol = message.symbol);
        message.provider !== undefined && (obj.provider = message.provider);
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        return obj;
    },
    create: function (base) {
        return exports.ProviderOracleParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseProviderOracleParams();
        message.symbol = (_a = object.symbol) !== null && _a !== void 0 ? _a : "";
        message.provider = (_b = object.provider) !== null && _b !== void 0 ? _b : "";
        message.oracleScaleFactor = (_c = object.oracleScaleFactor) !== null && _c !== void 0 ? _c : 0;
        message.oracleType = (_d = object.oracleType) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function createBaseOracleParams() {
    return { oracleBase: "", oracleQuote: "", oracleScaleFactor: 0, oracleType: 0 };
}
exports.OracleParams = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.oracleBase !== "") {
            writer.uint32(10).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(18).string(message.oracleQuote);
        }
        if (message.oracleScaleFactor !== 0) {
            writer.uint32(24).uint32(message.oracleScaleFactor);
        }
        if (message.oracleType !== 0) {
            writer.uint32(32).int32(message.oracleType);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseOracleParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.oracleBase = reader.string();
                    break;
                case 2:
                    message.oracleQuote = reader.string();
                    break;
                case 3:
                    message.oracleScaleFactor = reader.uint32();
                    break;
                case 4:
                    message.oracleType = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleScaleFactor: isSet(object.oracleScaleFactor) ? Number(object.oracleScaleFactor) : 0,
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleScaleFactor !== undefined && (obj.oracleScaleFactor = Math.round(message.oracleScaleFactor));
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        return obj;
    },
    create: function (base) {
        return exports.OracleParams.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseOracleParams();
        message.oracleBase = (_a = object.oracleBase) !== null && _a !== void 0 ? _a : "";
        message.oracleQuote = (_b = object.oracleQuote) !== null && _b !== void 0 ? _b : "";
        message.oracleScaleFactor = (_c = object.oracleScaleFactor) !== null && _c !== void 0 ? _c : 0;
        message.oracleType = (_d = object.oracleType) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function createBaseTradingRewardCampaignLaunchProposal() {
    return { title: "", description: "", campaignInfo: undefined, campaignRewardPools: [] };
}
exports.TradingRewardCampaignLaunchProposal = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.campaignInfo !== undefined) {
            exchange_1.TradingRewardCampaignInfo.encode(message.campaignInfo, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.campaignRewardPools), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignLaunchProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.campaignInfo = exchange_1.TradingRewardCampaignInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.campaignRewardPools.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            campaignInfo: isSet(object.campaignInfo) ? exchange_1.TradingRewardCampaignInfo.fromJSON(object.campaignInfo) : undefined,
            campaignRewardPools: Array.isArray(object === null || object === void 0 ? void 0 : object.campaignRewardPools)
                ? object.campaignRewardPools.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.campaignInfo !== undefined &&
            (obj.campaignInfo = message.campaignInfo ? exchange_1.TradingRewardCampaignInfo.toJSON(message.campaignInfo) : undefined);
        if (message.campaignRewardPools) {
            obj.campaignRewardPools = message.campaignRewardPools.map(function (e) { return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined; });
        }
        else {
            obj.campaignRewardPools = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignLaunchProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseTradingRewardCampaignLaunchProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.campaignInfo = (object.campaignInfo !== undefined && object.campaignInfo !== null)
            ? exchange_1.TradingRewardCampaignInfo.fromPartial(object.campaignInfo)
            : undefined;
        message.campaignRewardPools = ((_c = object.campaignRewardPools) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseTradingRewardCampaignUpdateProposal() {
    return {
        title: "",
        description: "",
        campaignInfo: undefined,
        campaignRewardPoolsAdditions: [],
        campaignRewardPoolsUpdates: [],
    };
}
exports.TradingRewardCampaignUpdateProposal = {
    encode: function (message, writer) {
        var e_11, _a, e_12, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.campaignInfo !== undefined) {
            exchange_1.TradingRewardCampaignInfo.encode(message.campaignInfo, writer.uint32(26).fork()).ldelim();
        }
        try {
            for (var _c = __values(message.campaignRewardPoolsAdditions), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_11) throw e_11.error; }
        }
        try {
            for (var _e = __values(message.campaignRewardPoolsUpdates), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(42).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardCampaignUpdateProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.campaignInfo = exchange_1.TradingRewardCampaignInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.campaignRewardPoolsAdditions.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.campaignRewardPoolsUpdates.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            campaignInfo: isSet(object.campaignInfo) ? exchange_1.TradingRewardCampaignInfo.fromJSON(object.campaignInfo) : undefined,
            campaignRewardPoolsAdditions: Array.isArray(object === null || object === void 0 ? void 0 : object.campaignRewardPoolsAdditions)
                ? object.campaignRewardPoolsAdditions.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
            campaignRewardPoolsUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.campaignRewardPoolsUpdates)
                ? object.campaignRewardPoolsUpdates.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.campaignInfo !== undefined &&
            (obj.campaignInfo = message.campaignInfo ? exchange_1.TradingRewardCampaignInfo.toJSON(message.campaignInfo) : undefined);
        if (message.campaignRewardPoolsAdditions) {
            obj.campaignRewardPoolsAdditions = message.campaignRewardPoolsAdditions.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.campaignRewardPoolsAdditions = [];
        }
        if (message.campaignRewardPoolsUpdates) {
            obj.campaignRewardPoolsUpdates = message.campaignRewardPoolsUpdates.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.campaignRewardPoolsUpdates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardCampaignUpdateProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTradingRewardCampaignUpdateProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.campaignInfo = (object.campaignInfo !== undefined && object.campaignInfo !== null)
            ? exchange_1.TradingRewardCampaignInfo.fromPartial(object.campaignInfo)
            : undefined;
        message.campaignRewardPoolsAdditions =
            ((_c = object.campaignRewardPoolsAdditions) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        message.campaignRewardPoolsUpdates =
            ((_d = object.campaignRewardPoolsUpdates) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseRewardPointUpdate() {
    return { accountAddress: "", newPoints: "" };
}
exports.RewardPointUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.newPoints !== "") {
            writer.uint32(98).string(message.newPoints);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRewardPointUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 12:
                    message.newPoints = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            newPoints: isSet(object.newPoints) ? String(object.newPoints) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.newPoints !== undefined && (obj.newPoints = message.newPoints);
        return obj;
    },
    create: function (base) {
        return exports.RewardPointUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseRewardPointUpdate();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.newPoints = (_b = object.newPoints) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTradingRewardPendingPointsUpdateProposal() {
    return { title: "", description: "", pendingPoolTimestamp: "0", rewardPointUpdates: [] };
}
exports.TradingRewardPendingPointsUpdateProposal = {
    encode: function (message, writer) {
        var e_13, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.pendingPoolTimestamp !== "0") {
            writer.uint32(24).int64(message.pendingPoolTimestamp);
        }
        try {
            for (var _b = __values(message.rewardPointUpdates), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.RewardPointUpdate.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradingRewardPendingPointsUpdateProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.pendingPoolTimestamp = longToString(reader.int64());
                    break;
                case 4:
                    message.rewardPointUpdates.push(exports.RewardPointUpdate.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            pendingPoolTimestamp: isSet(object.pendingPoolTimestamp) ? String(object.pendingPoolTimestamp) : "0",
            rewardPointUpdates: Array.isArray(object === null || object === void 0 ? void 0 : object.rewardPointUpdates)
                ? object.rewardPointUpdates.map(function (e) { return exports.RewardPointUpdate.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.pendingPoolTimestamp !== undefined && (obj.pendingPoolTimestamp = message.pendingPoolTimestamp);
        if (message.rewardPointUpdates) {
            obj.rewardPointUpdates = message.rewardPointUpdates.map(function (e) { return e ? exports.RewardPointUpdate.toJSON(e) : undefined; });
        }
        else {
            obj.rewardPointUpdates = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.TradingRewardPendingPointsUpdateProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTradingRewardPendingPointsUpdateProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.pendingPoolTimestamp = (_c = object.pendingPoolTimestamp) !== null && _c !== void 0 ? _c : "0";
        message.rewardPointUpdates = ((_d = object.rewardPointUpdates) === null || _d === void 0 ? void 0 : _d.map(function (e) { return exports.RewardPointUpdate.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseFeeDiscountProposal() {
    return { title: "", description: "", schedule: undefined };
}
exports.FeeDiscountProposal = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        if (message.schedule !== undefined) {
            exchange_1.FeeDiscountSchedule.encode(message.schedule, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFeeDiscountProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.schedule = exchange_1.FeeDiscountSchedule.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            schedule: isSet(object.schedule) ? exchange_1.FeeDiscountSchedule.fromJSON(object.schedule) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? exchange_1.FeeDiscountSchedule.toJSON(message.schedule) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FeeDiscountProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseFeeDiscountProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? exchange_1.FeeDiscountSchedule.fromPartial(object.schedule)
            : undefined;
        return message;
    },
};
function createBaseBatchCommunityPoolSpendProposal() {
    return { title: "", description: "", proposals: [] };
}
exports.BatchCommunityPoolSpendProposal = {
    encode: function (message, writer) {
        var e_14, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.proposals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                distribution_1.CommunityPoolSpendProposal.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBatchCommunityPoolSpendProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.proposals.push(distribution_1.CommunityPoolSpendProposal.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            proposals: Array.isArray(object === null || object === void 0 ? void 0 : object.proposals)
                ? object.proposals.map(function (e) { return distribution_1.CommunityPoolSpendProposal.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.proposals) {
            obj.proposals = message.proposals.map(function (e) { return e ? distribution_1.CommunityPoolSpendProposal.toJSON(e) : undefined; });
        }
        else {
            obj.proposals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.BatchCommunityPoolSpendProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseBatchCommunityPoolSpendProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.proposals = ((_c = object.proposals) === null || _c === void 0 ? void 0 : _c.map(function (e) { return distribution_1.CommunityPoolSpendProposal.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseAtomicMarketOrderFeeMultiplierScheduleProposal() {
    return { title: "", description: "", marketFeeMultipliers: [] };
}
exports.AtomicMarketOrderFeeMultiplierScheduleProposal = {
    encode: function (message, writer) {
        var e_15, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.marketFeeMultipliers), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.MarketFeeMultiplier.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_15) throw e_15.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseAtomicMarketOrderFeeMultiplierScheduleProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.marketFeeMultipliers.push(exchange_1.MarketFeeMultiplier.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            marketFeeMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.marketFeeMultipliers)
                ? object.marketFeeMultipliers.map(function (e) { return exchange_1.MarketFeeMultiplier.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.marketFeeMultipliers) {
            obj.marketFeeMultipliers = message.marketFeeMultipliers.map(function (e) { return e ? exchange_1.MarketFeeMultiplier.toJSON(e) : undefined; });
        }
        else {
            obj.marketFeeMultipliers = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.AtomicMarketOrderFeeMultiplierScheduleProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseAtomicMarketOrderFeeMultiplierScheduleProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.marketFeeMultipliers = ((_c = object.marketFeeMultipliers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.MarketFeeMultiplier.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseDenomMinNotionalProposal() {
    return { title: "", description: "", denomMinNotionals: [] };
}
exports.DenomMinNotionalProposal = {
    encode: function (message, writer) {
        var e_16, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.title !== "") {
            writer.uint32(10).string(message.title);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        try {
            for (var _b = __values(message.denomMinNotionals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DenomMinNotional.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseDenomMinNotionalProposal();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.title = reader.string();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                case 3:
                    message.denomMinNotionals.push(exchange_1.DenomMinNotional.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            title: isSet(object.title) ? String(object.title) : "",
            description: isSet(object.description) ? String(object.description) : "",
            denomMinNotionals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomMinNotionals)
                ? object.denomMinNotionals.map(function (e) { return exchange_1.DenomMinNotional.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.title !== undefined && (obj.title = message.title);
        message.description !== undefined && (obj.description = message.description);
        if (message.denomMinNotionals) {
            obj.denomMinNotionals = message.denomMinNotionals.map(function (e) { return e ? exchange_1.DenomMinNotional.toJSON(e) : undefined; });
        }
        else {
            obj.denomMinNotionals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.DenomMinNotionalProposal.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseDenomMinNotionalProposal();
        message.title = (_a = object.title) !== null && _a !== void 0 ? _a : "";
        message.description = (_b = object.description) !== null && _b !== void 0 ? _b : "";
        message.denomMinNotionals = ((_c = object.denomMinNotionals) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.DenomMinNotional.fromPartial(e); })) || [];
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
