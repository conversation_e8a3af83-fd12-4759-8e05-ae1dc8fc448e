{"version": 3, "file": "legacyTransaction.js", "sourceRoot": "", "sources": ["../../../src/tx/legacyTransaction.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,EAAE,uBAAuB,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,EACN,WAAW,EACX,0BAA0B,EAC1B,SAAS,EACT,YAAY,EACZ,kBAAkB,EAClB,eAAe,GACf,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAIvD,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAExC,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAE3B,SAAS,WAAW,CAAC,EAAU,EAAE,OAAe;IAC/C,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IACrB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC3C,OAAO,CAAC,KAAK,cAAc,GAAG,EAAE,IAAI,CAAC,KAAK,cAAc,GAAG,EAAE,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,gDAAgD;AAChD,MAAM,OAAO,WAAY,SAAQ,eAA4B;IAK5D;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC5D,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAsB,EAAE,OAAkB,EAAE;QAC1E,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAsB,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACxE,0GAA0G;QAC1G,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CACd,6FAA6F,CAC7F,CAAC;QACH,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC;QAErE,uBAAuB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAEvE,OAAO,IAAI,WAAW,CACrB;YACC,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,CAAC;YACD,CAAC;YACD,CAAC;SACD,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;;QACtD,KAAK,iCAAM,MAAM,KAAE,IAAI,EAAE,gBAAgB,KAAI,IAAI,CAAC,CAAC;QAEnD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CACjC,YAAY,CAAC,MAAM,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAC7D,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,WAAW,EAAE,CAAC;YACjD,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAC;YACvF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAClE,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACtB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACP,eAAe;gBACf,kFAAkF;gBAClF,sFAAsF;gBACtF,mGAAmG;gBACnG,oEAAoE;gBACpE,yCAAyC;gBACzC,wCAAwC;gBACxC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;oBACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;gBACjE,CAAC;YACF,CAAC;QACF,CAAC;QAED,MAAM,MAAM,GAAG,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,mCAAI,IAAI,CAAC;QACpC,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;IACF,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,GAAG;QACT,OAAO;YACN,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;SAC/E,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,SAAS;QACf,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,iBAAiB;QACxB,MAAM,MAAM,GAAG;YACd,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;YACzC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzD,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC;YACtC,IAAI,CAAC,IAAI;SACT,CAAC;QAEF,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAoBM,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,WAAW,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,UAAU;QAChB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YAClF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACpB,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aAChC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,cAAc;QACpB,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;IACnD,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAC;YACnF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACtB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACxB,CAAC;QAED,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,2BAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,kBAAkB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,IAAI,CAAC;YACJ,OAAO,SAAS,CACf,OAAO,EACP,CAAE,EACF,0BAA0B,CAAC,CAAE,CAAC,EAC9B,0BAA0B,CAAC,CAAE,CAAC,EAC9B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,sBAAsB,CAAC;gBAC/C,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACvB,CAAC,CAAC,SAAS,CACZ,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;IACF,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,EAAU,EAAE,CAAa,EAAE,CAAa;QACnE,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACtD,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,IAAI,mCAAQ,IAAI,CAAC,SAAS,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,GAAE,CAAC;QAExD,OAAO,WAAW,CAAC,UAAU,CAC5B;YACC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,CAAC;YACD,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;SACxB,EACD,IAAI,CACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM;QACZ,OAAO;YACN,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,EAAW,EAAE,MAAe;QAChD,IAAI,aAAa,CAAC;QAClB,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACpD,8DAA8D;QAC9D,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YACrB,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CACd,oFAAoF,CAAC,EAAE,CACvF,CAAC;YACH,CAAC;QACF,CAAC;QAED,6DAA6D;QAC7D,IACC,CAAC,KAAK,SAAS;YACf,CAAC,KAAK,CAAC;YACP,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACjD,CAAC,KAAK,EAAE;YACR,CAAC,KAAK,EAAE,EACP,CAAC;YACF,IAAI,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;oBAC/C,MAAM,IAAI,KAAK,CACd,+BAA+B,CAAC,iBAAiB,MAAM,CAAC,OAAO,EAAE,gFAAgF,CACjJ,CAAC;gBACH,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,+BAA+B;gBAC/B,IAAI,MAAM,CAAC;gBACX,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxB,MAAM,GAAG,EAAE,CAAC;gBACb,CAAC;qBAAM,CAAC;oBACP,MAAM,GAAG,EAAE,CAAC;gBACb,CAAC;gBACD,iDAAiD;gBACjD,aAAa,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,QAAQ;QACd,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7C,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC9B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC;IACtC,CAAC;CACD"}