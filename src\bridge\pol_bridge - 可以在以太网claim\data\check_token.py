#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import requests
import yaml
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
from web3 import Web3
from web3.contract import Contract
from eth_typing import ChecksumAddress

# 加载配置文件
def load_config():
    """
    从config.yaml加载配置
    """
    try:
        # 调整配置文件路径到项目根目录
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))), 'config', 'config.yaml')
        
        if not os.path.exists(config_path):
            print(f"警告: 找不到配置文件: {config_path}")
            return {}
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

CONFIG = load_config()

# 链ID配置
CHAIN_IDS = {
    "ethereum": 1,
    "polygon": 137
}

# 合约地址配置
CONTRACT_ADDRESSES = {
    "ethereum": {
        "RootChainManager": [
            "******************************************",  # 主要
            "******************************************"   # 备用
        ],
        "PredRootChainManager": "******************************************",
        "PoSBridge": "******************************************"
    },
    "polygon": {
        "ChildChainManager": [
            "******************************************",  # 主要
            "******************************************"   # 备用
        ],
        "PoSBridge": "******************************************"
    }
}

# API端点
API_ENDPOINTS = {
    "polygon_mapper": "https://mapper.polygon.technology/api/v1/mapping",
    "polygon_scan": "https://api.polygonscan.com/api",
    "etherscan": "https://api.etherscan.io/api"
}

# 本地代币映射文件路径 - 调整为当前目录
TOKENS_JSON_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tokens.json")

# 合约ABI定义
ROOT_CHAIN_MANAGER_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "rootToken", "type": "address"}],
        "name": "rootToChildToken",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "_token", "type": "address"}],
        "name": "getMappedToken",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    }
]

CHILD_CHAIN_MANAGER_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "childToken", "type": "address"}],
        "name": "childToRootToken",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "_token", "type": "address"}],
        "name": "getMappedToken", 
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    }
]

POS_BRIDGE_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "_token", "type": "address"}],
        "name": "rootToChildToken",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "_token", "type": "address"}],
        "name": "childToRootToken",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    }
]

ERC20_ABI = [
    {
        "constant": True,
        "inputs": [],
        "name": "name",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    }
]

def is_valid_address(address: str) -> bool:
    """
    检查地址是否为有效的以太坊地址
    
    Args:
        address: 待检查的地址
        
    Returns:
        bool: 是否为有效地址
    """
    return Web3.is_address(address)

def get_web3(chain: str, testnet: bool = False) -> Web3:
    """
    从配置文件获取Web3连接实例
    
    Args:
        chain: 链名称 ("ethereum" 或 "polygon")
        testnet: 是否使用测试网
        
    Returns:
        Web3: Web3连接实例
    """
    # 从配置文件获取RPC URL
    if not CONFIG or 'dex' not in CONFIG:
        raise ValueError("配置文件不存在或格式错误")
    
    if chain not in CONFIG['dex']:
        raise ValueError(f"配置文件中未找到{chain}的配置")
    
    chain_config = CONFIG['dex'][chain]
    
    # 获取主要RPC URL
    if 'rpc_url' in chain_config:
        primary_rpc = chain_config['rpc_url']
    elif 'rpc' in chain_config and 'endpoint' in chain_config['rpc']:
        primary_rpc = chain_config['rpc']['endpoint']
    else:
        raise ValueError(f"配置文件中未找到{chain}的RPC URL")
    
    # 获取备用RPC URLs
    backup_rpcs = []
    if 'backup_rpc_urls' in chain_config:
        backup_rpcs = chain_config['backup_rpc_urls']
    elif 'rpc' in chain_config and 'backup_endpoints' in chain_config['rpc']:
        backup_rpcs = chain_config['rpc']['backup_endpoints']
    
    # 尝试连接主RPC
    try:
        web3 = Web3(Web3.HTTPProvider(primary_rpc))
        if web3.is_connected():
            return web3
    except Exception as e:
        print(f"连接主RPC失败: {str(e)}")
    
    # 尝试备用RPC
    for rpc_url in backup_rpcs:
        try:
            web3 = Web3(Web3.HTTPProvider(rpc_url))
            if web3.is_connected():
                return web3
        except Exception:
            continue
    
    # 如果所有RPC都失败，使用公共RPC作为最后的尝试
    fallback_rpcs = {
        "ethereum": [
            "https://mainnet.infura.io/v3/********************************",
            "https://eth-mainnet.public.blastapi.io",
            "https://ethereum.publicnode.com"
        ],
        "polygon": [
            "https://polygon-rpc.com",
            "https://polygon-mainnet.public.blastapi.io",
            "https://polygon.llamarpc.com"
        ]
    }
    
    if chain in fallback_rpcs:
        for rpc_url in fallback_rpcs[chain]:
            try:
                web3 = Web3(Web3.HTTPProvider(rpc_url))
                if web3.is_connected():
                    return web3
            except Exception:
                continue
    
    raise ConnectionError(f"无法连接到任何 {chain} RPC节点")

def get_erc20_info(web3: Web3, token_address: str) -> Optional[Dict[str, Any]]:
    """
    获取ERC20代币的基本信息
    
    Args:
        web3: Web3实例
        token_address: 代币合约地址
        
    Returns:
        Optional[Dict[str, Any]]: 代币信息，如果不是有效的ERC20则返回None
    """
    if not is_valid_address(token_address):
        return None
        
    try:
        token_contract = web3.eth.contract(
            address=web3.to_checksum_address(token_address),
            abi=ERC20_ABI
        )
        
        # 尝试获取基本信息
        try:
            name = token_contract.functions.name().call()
        except Exception:
            name = "Unknown"
            
        try:
            symbol = token_contract.functions.symbol().call()
        except Exception:
            symbol = "UNKNOWN"
            
        try:
            decimals = token_contract.functions.decimals().call()
        except Exception:
            decimals = 18
            
        return {
            "address": token_address,
            "name": name,
            "symbol": symbol,
            "decimals": decimals,
            "is_erc20": True
        }
        
    except Exception as e:
        print(f"获取ERC20信息失败: {str(e)}")
        return None

def load_token_mappings() -> Dict[str, Dict[str, Any]]:
    """
    加载本地代币映射数据
    
    Returns:
        Dict[str, Dict[str, Any]]: 代币映射数据
    """
    try:
        if not os.path.exists(TOKENS_JSON_PATH):
            return {}
            
        with open(TOKENS_JSON_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            # 确保数据格式正确
            if not isinstance(data, dict):
                return {}
                
            return data
    except Exception as e:
        print(f"加载代币映射数据失败: {str(e)}")
        return {}

def query_mapping_from_local(address: str, source_chain: str = "polygon") -> Optional[Dict[str, Any]]:
    """
    从本地文件查询代币映射
    
    Args:
        address: 代币地址
        source_chain: 源链 ("ethereum" 或 "polygon")
        
    Returns:
        Optional[Dict[str, Any]]: 映射信息，如果未找到则返回None
    """
    if not is_valid_address(address):
        return None
        
    address = address.lower()
    chain_id = str(CHAIN_IDS[source_chain])  # 转为字符串
    target_chain = "ethereum" if source_chain == "polygon" else "polygon"
    target_chain_id = str(CHAIN_IDS[target_chain])  # 转为字符串
    
    try:
        # 加载映射数据
        mappings = load_token_mappings()
        
        # 按地址在所有代币中查找
        for symbol, chain_data in mappings.items():
            if chain_id in chain_data and chain_data[chain_id].get("address", "").lower() == address:
                # 找到了源链上的代币
                source_data = chain_data[chain_id]
                
                # 检查目标链上是否有映射
                if target_chain_id in chain_data:
                    target_data = chain_data[target_chain_id]
                    target_address = target_data.get("address", "").lower()
                    
                    if target_address:
                        # 构建查询结果
                        result = {
                            "success": True,
                            "method": "local",
                            "address": address,
                            "source_chain": source_chain,
                            "name": source_data.get("name", "Unknown"),
                            "symbol": source_data.get("symbol", "Unknown"),
                            "decimals": source_data.get("decimals", 18),
                            "is_erc20": True,
                            f"{target_chain}_address": target_address,
                            f"mapping_name": target_data.get("name", "Unknown"),
                            f"mapping_symbol": target_data.get("symbol", "Unknown"),
                            f"mapping_decimals": target_data.get("decimals", 18)
                        }
                        
                        return result
    except Exception as e:
        print(f"本地查询代币映射失败: {str(e)}")
    
    return None

def query_mapping_via_contract(address: str, source_chain: str = "polygon", retry_count: int = 3) -> Optional[str]:
    """
    通过合约查询代币映射地址
    
    Args:
        address: 代币地址
        source_chain: 源链 ("ethereum" 或 "polygon")
        retry_count: 重试次数
        
    Returns:
        Optional[str]: 映射的代币地址，如果未找到则返回None
    """
    if not is_valid_address(address):
        return None
        
    address = address.lower()
    
    try:
        # 连接源链
        web3_source = get_web3(source_chain)
        
        # 根据源链使用不同的合约方法查询
        if source_chain == "polygon":
            # 从Polygon查询以太坊地址
            contracts = CONTRACT_ADDRESSES["polygon"]["ChildChainManager"]
            abi = CHILD_CHAIN_MANAGER_ABI
            function_names = ["childToRootToken", "getMappedToken"]
        else:
            # 从以太坊查询Polygon地址
            contracts = CONTRACT_ADDRESSES["ethereum"]["RootChainManager"]
            abi = ROOT_CHAIN_MANAGER_ABI
            function_names = ["rootToChildToken", "getMappedToken"]
        
        # 确保contracts是列表
        if not isinstance(contracts, list):
            contracts = [contracts]
            
        # 尝试每个合约地址
        for contract_address in contracts:
            contract = web3_source.eth.contract(
                address=web3_source.to_checksum_address(contract_address),
                abi=abi
            )
            
            # 尝试每个函数名
            for function_name in function_names:
                try:
                    for attempt in range(retry_count):
                        try:
                            function = getattr(contract.functions, function_name)
                            result = function(web3_source.to_checksum_address(address)).call()
                            
                            if result and result != "******************************************":
                                return result.lower()
                        except Exception as e:
                            if "execution reverted" in str(e) or "function not found" in str(e).lower():
                                # 函数不存在或执行失败，尝试下一个函数
                                break
                            elif attempt < retry_count - 1:
                                # 其他错误，重试
                                time.sleep(1)
                                continue
                            else:
                                print(f"{function_name}调用失败: {str(e)}")
                                break
                except Exception as e:
                    print(f"查询函数{function_name}失败: {str(e)}")
                    continue
        
        # 尝试PoS桥接合约
        try:
            if source_chain == "polygon":
                pos_contract_address = CONTRACT_ADDRESSES["polygon"]["PoSBridge"]
                function_name = "childToRootToken"
            else:
                pos_contract_address = CONTRACT_ADDRESSES["ethereum"]["PoSBridge"]
                function_name = "rootToChildToken"
                
            pos_contract = web3_source.eth.contract(
                address=web3_source.to_checksum_address(pos_contract_address),
                abi=POS_BRIDGE_ABI
            )
            
            for attempt in range(retry_count):
                try:
                    result = getattr(pos_contract.functions, function_name)(
                        web3_source.to_checksum_address(address)
                    ).call()
                    
                    if result and result != "******************************************":
                        return result.lower()
                except Exception:
                    if attempt < retry_count - 1:
                        # 重试
                        time.sleep(1)
                        continue
                    else:
                        break
        except Exception as e:
            print(f"查询PoS桥接合约失败: {str(e)}")
        
        return None
    
    except Exception as e:
        print(f"通过合约查询映射失败: {str(e)}")
        return None

def query_token_mapping(address: str, source_chain: str = "polygon", use_local: bool = True) -> Dict[str, Any]:
    """
    综合查询代币映射
    
    Args:
        address: 代币地址
        source_chain: 源链 ("ethereum" 或 "polygon")
        use_local: 是否优先使用本地查询
        
    Returns:
        Dict[str, Any]: 查询结果，包含成功标志和映射信息
    """
    if not is_valid_address(address):
        return {
            "success": False,
            "error": "无效的地址格式"
        }
    
    address = address.lower()
    target_chain = "ethereum" if source_chain == "polygon" else "polygon"
    
    result = {
        "success": False,
        "address": address,
        "source_chain": source_chain
    }
    
    # 1. 如果启用本地查询，先查询本地文件
    if use_local:
        local_result = query_mapping_from_local(address, source_chain)
        if local_result and local_result.get("success"):
            return local_result
    
    # 2. 获取当前链上的代币信息
    try:
        web3_source = get_web3(source_chain)
        token_info = get_erc20_info(web3_source, address)
        
        if token_info:
            result.update(token_info)
            result["is_erc20"] = True
        else:
            return {
                "success": False,
                "address": address,
                "source_chain": source_chain,
                "message": "无效的ERC20代币"
            }
            
    except Exception as e:
        return {
            "success": False,
            "address": address,
            "source_chain": source_chain,
            "error": f"获取代币信息失败: {str(e)}"
        }
    
    # 3. 通过合约查询
    mapping_address = query_mapping_via_contract(address, source_chain)
    
    if mapping_address:
        # 获取目标链上的代币信息
        try:
            web3_target = get_web3(target_chain)
            mapping_info = get_erc20_info(web3_target, mapping_address)
            
            if mapping_info:
                result["success"] = True
                result["method"] = "contract"
                result[f"{target_chain}_address"] = mapping_address
                
                for key, value in mapping_info.items():
                    if key not in ["address", "is_erc20"]:
                        result[f"mapping_{key}"] = value
                
                # 保存结果到本地
                save_token_mapping(result)
                
                return result
        except Exception as e:
            print(f"获取目标链代币信息失败: {str(e)}")
    
    # 如果是有效的ERC20代币但没有找到映射
    result["message"] = "有效的ERC20代币，但未找到映射地址"
    return result

def save_token_mapping(result: Dict[str, Any]) -> bool:
    """保存代币映射到tokens.json文件"""
    try:
        # 加载现有的映射
        mappings = load_token_mappings()
        
        source_chain = result.get("source_chain", "polygon")
        source_address = result.get("address", "").lower()
        
        if source_chain == "polygon":
            target_chain = "ethereum"
            target_address = result.get("ethereum_address", "").lower()
            source_chain_id = "137"
            target_chain_id = "1"
        else:
            target_chain = "polygon"
            target_address = result.get("polygon_address", "").lower()
            source_chain_id = "1"
            target_chain_id = "137"
        
        # 获取代币符号
        symbol = result.get("symbol", "UNKNOWN")
        
        # 如果符号不存在于映射中，则创建
        if symbol not in mappings:
            mappings[symbol] = {}
        
        # 如果链ID不存在于映射中，则创建
        if source_chain_id not in mappings[symbol]:
            mappings[symbol][source_chain_id] = {}
            
        if target_chain_id not in mappings[symbol]:
            mappings[symbol][target_chain_id] = {}
        
        # 更新源链信息
        mappings[symbol][source_chain_id] = {
            "address": source_address,
            "name": result.get("name", "Unknown"),
            "symbol": symbol,
            "decimals": result.get("decimals", 18),
            "chainId": int(source_chain_id)
        }
        
        # 更新目标链信息
        mappings[symbol][target_chain_id] = {
            "address": target_address,
            "name": result.get(f"mapping_name", "Unknown"),
            "symbol": result.get(f"mapping_symbol", symbol),
            "decimals": result.get(f"mapping_decimals", 18),
            "chainId": int(target_chain_id)
        }
        
        # 保存到文件
        with open(TOKENS_JSON_PATH, 'w', encoding='utf-8') as f:
            json.dump(mappings, f, indent=2, ensure_ascii=False)
            
        return True
    except Exception as e:
        print(f"保存代币映射失败: {str(e)}")
        return False

def load_eth_pol_addresses() -> List[str]:
    """从root_eth_pol.json加载以太坊地址列表"""
    try:
        # 构建文件路径 - 调整为当前目录
        filepath = os.path.join(os.path.dirname(os.path.abspath(__file__)), "root_eth_pol.json")
        
        if not os.path.exists(filepath):
            print(f"错误: 文件不存在 - {filepath}")
            return []
            
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        if 'addresses' in data and isinstance(data['addresses'], list):
            addresses = [addr.lower() for addr in data['addresses'] if is_valid_address(addr)]
            return addresses
        else:
            print("错误: root_eth_pol.json文件格式无效，找不到addresses字段")
            return []
    except Exception as e:
        print(f"加载以太坊地址列表失败: {str(e)}")
        return []

def main():
    """命令行入口点"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="检查代币在以太坊和Polygon之间的映射关系")
    parser.add_argument("address", nargs='?', help="代币地址")
    parser.add_argument("--chain", choices=["polygon", "ethereum"], default="polygon", help="源链 (默认: polygon)")
    parser.add_argument("--query-contract", action="store_true", help="仅使用合约查询")
    parser.add_argument("--direct", action="store_true", help="直接使用综合查询方法")
    parser.add_argument("--skip-contract", action="store_true", help="跳过合约查询")
    parser.add_argument("--skip-local", action="store_true", help="跳过本地文件查询")
    parser.add_argument("--save", action="store_true", help="保存查询结果到本地文件")
    parser.add_argument("--no-save", action="store_true", help="不保存查询结果到本地文件")
    parser.add_argument("--timeout", type=int, default=30, help="查询超时时间(秒)")
    parser.add_argument("--retry", type=int, default=3, help="重试次数")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细信息")
    parser.add_argument("--root-eth-pol", action="store_true", help="使用root_eth_pol.json中的所有地址查询映射关系")
    
    args = parser.parse_args()
    
    # 检查是否使用批量查询
    if args.root_eth_pol:
        addresses = load_eth_pol_addresses()
        if not addresses:
            print("错误: 无法从root_eth_pol.json加载地址或地址列表为空")
            return 1
            
        print(f"从root_eth_pol.json加载了 {len(addresses)} 个地址")
        
        success_count = 0
        for i, address in enumerate(addresses):
            print(f"\n正在处理 {i+1}/{len(addresses)}: {address}")
            
            # 设置超时
            if args.timeout > 0:
                import socket
                socket.setdefaulttimeout(args.timeout)
            
            # 检查是否需要保存结果
            save_result = not args.no_save
            
            # 使用综合查询方法
            result = query_token_mapping(address, "ethereum", use_local=not args.skip_local)
            
            if result["success"]:
                success_count += 1
                print(f"✓ 已找到代币映射 (通过{result['method']})")
                print(f"  符号: {result.get('symbol', 'Unknown')}")
                print(f"  名称: {result.get('name', 'Unknown')}")
                print(f"  Polygon地址: {result.get('polygon_address', 'Unknown')}")
                
                # 如果需要保存结果且尚未保存
                if save_result and result["method"] != "local":
                    if save_token_mapping(result):
                        print("  √ 已保存映射到本地文件")
            else:
                print(f"✗ 未找到映射: {result.get('message', '未知错误')}")
                
        print(f"\n批量查询完成: 找到 {success_count}/{len(addresses)} 个映射")
        return 0
    
    # 单地址查询
    if not args.address:
        parser.print_help()
        return 1
        
    # 检查地址格式
    if not is_valid_address(args.address):
        print(f"错误: 无效的地址格式 - {args.address}")
        return 1
        
    address = args.address.lower()
    
    print(f"查询 {args.chain} 链上的代币: {address}")
    
    try:
        # 设置超时
        if args.timeout > 0:
            import socket
            socket.setdefaulttimeout(args.timeout)
        
        # 检查是否需要使用本地查询
        use_local = not args.skip_local
        
        # 检查是否需要保存结果
        save_result = args.save and not args.no_save
            
        # 如果使用本地查询，先查询本地文件
        if use_local and not args.query_contract:
            local_result = query_mapping_from_local(address, args.chain)
            
            if local_result and local_result.get("success"):
                print(f"\n✓ 已从本地文件找到代币映射")
                print(f"  符号: {local_result.get('symbol', 'Unknown')}")
                print(f"  名称: {local_result.get('name', 'Unknown')}")
                
                target_chain = "ethereum" if args.chain == "polygon" else "polygon"
                target_field = f"{target_chain}_address"
                
                if target_field in local_result:
                    print(f"  {target_chain.capitalize()}地址: {local_result[target_field]}")
                    print(f"  映射符号: {local_result.get(f'mapping_symbol', 'Unknown')}")
                    print(f"  映射名称: {local_result.get(f'mapping_name', 'Unknown')}")
                    
                print(f"  小数位: {local_result.get('decimals', 18)}")
                
                if args.verbose:
                    print("\n详细信息:")
                    for key, value in local_result.items():
                        if key not in ["success", "method"]:
                            print(f"  {key}: {value}")
                            
                return 0
        
        # 直接使用综合查询方法
        if args.direct or (not args.query_contract):
            result = query_token_mapping(address, args.chain, use_local=use_local)
            
            if result["success"]:
                print(f"\n✓ 已找到代币映射 (通过{result['method']})")
                print(f"  符号: {result.get('symbol', 'Unknown')}")
                print(f"  名称: {result.get('name', 'Unknown')}")
                
                target_chain = "ethereum" if args.chain == "polygon" else "polygon"
                target_field = f"{target_chain}_address"
                
                if target_field in result:
                    print(f"  {target_chain.capitalize()}地址: {result[target_field]}")
                    print(f"  映射符号: {result.get(f'mapping_symbol', 'Unknown')}")
                    print(f"  映射名称: {result.get(f'mapping_name', 'Unknown')}")
                    
                print(f"  小数位: {result.get('decimals', 18)}")
                
                # 如果需要保存结果且尚未保存
                if save_result and result["method"] != "local":
                    if save_token_mapping(result):
                        print("\n√ 已保存映射到本地文件")
                
            elif result.get("is_erc20"):
                print(f"\n! 有效的ERC20代币，但未找到映射")
                print(f"  符号: {result.get('symbol', 'Unknown')}")
                print(f"  名称: {result.get('name', 'Unknown')}")
                print(f"  小数位: {result.get('decimals', 18)}")
            else:
                print(f"\n✗ 查询失败: {result.get('message', '未知错误')}")
                
            if args.verbose:
                print("\n详细信息:")
                for key, value in result.items():
                    if key not in ["success", "method"]:
                        print(f"  {key}: {value}")
        else:
            # 分步查询
            mapping_found = False
            mapping_address = None
            mapping_info = None
            
            # 首先检查本地映射
            if use_local:
                print("\n正在从本地查询...")
                local_result = query_mapping_from_local(address, args.chain)
                
                if local_result and local_result.get("success"):
                    target_chain = "ethereum" if args.chain == "polygon" else "polygon"
                    mapping_address = local_result.get(f"{target_chain}_address")
                    
                    if mapping_address:
                        print(f"✓ 从本地文件找到映射地址: {mapping_address}")
                        print(f"  目标链符号: {local_result.get(f'mapping_symbol', 'Unknown')}")
                        print(f"  目标链名称: {local_result.get(f'mapping_name', 'Unknown')}")
                        print(f"  目标链小数位: {local_result.get(f'mapping_decimals', 18)}")
                        
                        mapping_found = True
                        mapping_info = local_result
                else:
                    print("✗ 未从本地文件找到映射地址")
            
            if args.query_contract and not args.skip_contract and not mapping_found:
                print("\n正在通过合约查询...")
                mapping_address = query_mapping_via_contract(address, args.chain, args.retry)
                
                if mapping_address:
                    print(f"✓ 通过合约找到映射地址: {mapping_address}")
                    
                    # 获取目标链代币信息
                    target_chain = "ethereum" if args.chain == "polygon" else "polygon"
                    web3_target = get_web3(target_chain)
                    mapping_info = get_erc20_info(web3_target, mapping_address)
                    
                    if mapping_info:
                        print(f"  目标链符号: {mapping_info.get('symbol', 'Unknown')}")
                        print(f"  目标链名称: {mapping_info.get('name', 'Unknown')}")
                        print(f"  目标链小数位: {mapping_info.get('decimals', 18)}")
                        
                        mapping_found = True
                else:
                    print("✗ 未通过合约找到映射地址")
            
            # 如果找到了映射且需要保存
            if mapping_found and mapping_address and mapping_info and save_result:
                # 构建完整结果
                web3_source = get_web3(args.chain)
                token_info = get_erc20_info(web3_source, address)
                
                if token_info:
                    result = {
                        "success": True,
                        "method": "contract",
                        "address": address,
                        "source_chain": args.chain,
                        "name": token_info.get("name", "Unknown"),
                        "symbol": token_info.get("symbol", "Unknown"),
                        "decimals": token_info.get("decimals", 18),
                        "is_erc20": True
                    }
                    
                    target_chain = "ethereum" if args.chain == "polygon" else "polygon"
                    result[f"{target_chain}_address"] = mapping_address
                    
                    for key, value in mapping_info.items():
                        if key not in ["address", "is_erc20"]:
                            result[f"mapping_{key}"] = value
                    
                    if save_token_mapping(result):
                        print("\n√ 已保存映射到本地文件")
                        
    except KeyboardInterrupt:
        print("\n操作已取消")
        return 130
    except Exception as e:
        print(f"\n错误: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1
        
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main()) 