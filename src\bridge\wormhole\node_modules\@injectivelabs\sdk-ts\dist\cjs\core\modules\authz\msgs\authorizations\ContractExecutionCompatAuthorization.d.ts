import { Coin } from '@injectivelabs/ts-types';
import { GoogleProtobufAny, InjectiveWasmxV1Beta1Authz } from '@injectivelabs/core-proto-ts';
import { BaseAuthorization } from './Base.js';
export declare namespace ContractExecutionCompatAuthorization {
    interface Params {
        contract: string;
        limit?: {
            maxCalls?: number;
            amounts?: Coin[];
        };
        filter?: {
            acceptedMessagesKeys: string[];
        };
    }
    type Any = GoogleProtobufAny.Any;
    type Proto = InjectiveWasmxV1Beta1Authz.ContractExecutionCompatAuthorization;
    type Amino = Object;
}
/**
 * @category Contract Exec Arguments
 */
export default class ContractExecutionCompatAuthorization extends BaseAuthorization<ContractExecutionCompatAuthorization.Params, ContractExecutionCompatAuthorization.Proto, ContractExecutionCompatAuthorization.Amino> {
    static fromJSON(params: ContractExecutionCompatAuthorization.Params): ContractExecutionCompatAuthorization;
    toAny(): GoogleProtobufAny.Any;
    toProto(): ContractExecutionCompatAuthorization.Proto;
    toAmino(): ContractExecutionCompatAuthorization.Amino;
    toWeb3(): ContractExecutionCompatAuthorization.Amino;
}
