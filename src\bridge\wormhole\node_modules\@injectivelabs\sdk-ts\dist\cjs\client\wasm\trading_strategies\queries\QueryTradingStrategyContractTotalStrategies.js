"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryTradingStrategyContractTotalStrategies = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryTradingStrategyContractTotalStrategies extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        const payload = (0, index_js_1.toBase64)({
            total_strategies: {},
        });
        return payload;
    }
}
exports.QueryTradingStrategyContractTotalStrategies = QueryTradingStrategyContractTotalStrategies;
