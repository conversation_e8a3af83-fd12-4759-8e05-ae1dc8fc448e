import { AccountAuthenticatorAbstraction } from '../transactions/authenticator/account.mjs';
import { HexInput } from '../types/types.mjs';
import { AbstractedAccount } from './AbstractedAccount.mjs';
import '../bcs/deserializer.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../core/crypto/ed25519.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/accountAddress.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/crypto/signature.mjs';
import '../api/aptosConfig.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../core/crypto/abstraction.mjs';
import '../Ed25519Account-D9XrCLfE.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

type DerivableAbstractedAccountArgs = {
    /**
     * The signer function signs transactions and returns the `authenticator` bytes in the `AbstractionAuthData`.
     *
     * @param digest - The SHA256 hash of the transaction signing message
     * @returns The `authenticator` bytes that can be used to verify the signature.
     */
    signer: (digest: HexInput) => Uint8Array;
    /**
     * The authentication function that will be used to verify the signature.
     *
     * @example
     * ```ts
     * const authenticationFunction = `${accountAddress}::permissioned_delegation::authenticate`;
     * ```
     */
    authenticationFunction: string;
    /**
     * The abstract public key that is used to identify the account.
     * Depends on the use cases, most of the time it is the public key of the source wallet
     */
    abstractPublicKey: Uint8Array;
};
declare class DerivableAbstractedAccount extends AbstractedAccount {
    /**
     * The abstract public key that is used to identify the account.
     * Depends on the use cases, most of the time it is the public key of the source wallet
     */
    readonly abstractPublicKey: Uint8Array;
    /**
     * The domain separator used to calculate the DAA account address.
     */
    static readonly ADDRESS_DOMAIN_SEPERATOR: number;
    constructor({ signer, authenticationFunction, abstractPublicKey }: DerivableAbstractedAccountArgs);
    /**
     * Compute the account address of the DAA
     * The DAA account address is computed by hashing the function info and the account identity
     * and appending the domain separator (5)
     *
     * @param functionInfo - The authentication function
     * @param accountIdentifier - The account identity
     * @returns The account address
     */
    static computeAccountAddress(functionInfo: string, accountIdentifier: Uint8Array): Uint8Array;
    signWithAuthenticator(message: HexInput): AccountAuthenticatorAbstraction;
}

export { DerivableAbstractedAccount };
