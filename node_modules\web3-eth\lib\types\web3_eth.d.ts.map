{"version": 3, "file": "web3_eth.d.ts", "sourceRoot": "", "sources": ["../../src/web3_eth.ts"], "names": [], "mappings": "AAoBA,OAAO,EACN,kBAAkB,EAClB,OAAO,EACP,KAAK,EACL,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,OAAO,EACP,gBAAgB,EAEhB,WAAW,EACX,eAAe,EACf,mBAAmB,EACnB,mCAAmC,EACnC,iCAAiC,EACjC,wCAAwC,EACxC,wBAAwB,EACxB,UAAU,EACV,qBAAqB,EACrB,eAAe,EAGf,YAAY,EACZ,MAAM,YAAY,CAAC;AACpB,OAAO,EAAuB,WAAW,EAAE,sBAAsB,EAAE,MAAM,WAAW,CAAC;AAOrF,OAAO,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,MAAM,YAAY,CAAC;AAC3E,OAAO,EACN,gBAAgB,EAChB,kCAAkC,EAClC,oBAAoB,EACpB,mBAAmB,EACnB,MAAM,yBAAyB,CAAC;AAEjC,MAAM,MAAM,sBAAsB,GAAG;IACpC,IAAI,EAAE,OAAO,gBAAgB,CAAC;IAC9B,sBAAsB,EAAE,OAAO,kCAAkC,CAAC;IAClE,mBAAmB,EAAE,OAAO,kCAAkC,CAAC;IAC/D,QAAQ,EAAE,OAAO,oBAAoB,CAAC;IACtC,eAAe,EAAE,OAAO,oBAAoB,CAAC;IAC7C,OAAO,EAAE,OAAO,mBAAmB,CAAC;CACpC,CAAC;AAEF,eAAO,MAAM,uBAAuB;;;;;;;CAOnC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,qBAAa,OAAQ,SAAQ,WAAW,CAAC,mBAAmB,EAAE,sBAAsB,CAAC;IACpF,OAAO,CAAC,qBAAqB,CAAC,CAAwB;gBAIrD,iBAAiB,CAAC,EAAE,kBAAkB,CAAC,GAAG,CAAC,GAAG,sBAAsB,GAAG,MAAM;IA4BvE,wBAAwB,CAAC,qBAAqB,EAAE,qBAAqB;IAIrE,wBAAwB;IAI/B;;;;;;;OAOG;IACU,kBAAkB;IAK/B;;;;;;;;;;;;;;;OAeG;IACU,SAAS;IAKtB;;;;;;;OAOG;IACU,WAAW;IAIxB;;;;;;;;;OASG;IACU,QAAQ;IAIrB;;;;;;;;;;;;;OAaG;IACU,WAAW,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACtF,YAAY,GAAE,YACyB;IAKxC;;;;;;;;;;;OAWG;IACU,WAAW,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACtF,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;OAWG;IACU,WAAW,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACtF,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;OAWG;IACU,uBAAuB,CACnC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC7D,YAAY,GAAE,YAAuD;IAIvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IAEU,gBAAgB,CAC5B,mBAAmB,GAAE,MAAM,GAAG,MAAkB,EAChD,+BAA+B,SAAkB,GAC/C,OAAO,CAAC,OAAO,CAAC;IAsDZ,UAAU,yBAxDK,MAAM,GAAG,MAAM,+CAElC,OAAO,CAAC,OAAO,CAAC,CAsDuB;IAE1C;;;;;;;OAOG;IACU,WAAW;IAKxB;;;;;;;;;;;OAWG;IACU,cAAc,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACzF,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;OAeG;IACU,UAAU,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACrF,OAAO,EAAE,OAAO,EAChB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACU,YAAY,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACvF,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,OAAO,EACpB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAWtE;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACU,OAAO,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAClF,OAAO,EAAE,OAAO,EAChB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+DG;IACU,QAAQ,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACnF,KAAK,GAAE,gBAAgB,GAAG,gBAAoC,EAC9D,QAAQ,UAAQ,EAChB,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKtE;;;;;;;;;;;;;;;OAeG;IACU,wBAAwB,CACpC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAE9D,KAAK,GAAE,gBAAgB,GAAG,gBAAoC,EAC9D,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;OAeG;IACU,kBAAkB,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC7F,KAAK,GAAE,gBAAgB,GAAG,gBAAoC,EAC9D,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8DG;IACU,QAAQ,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACnF,KAAK,GAAE,gBAAgB,GAAG,gBAAgB,aAAoB,EAC9D,UAAU,EAAE,OAAO,EACnB,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACU,cAAc,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACzF,eAAe,EAAE,KAAK,EACtB,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAatE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiFG;IACU,sBAAsB,CAClC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC7D,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAIvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiDG;IACU,uBAAuB,CACnC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAE9D,KAAK,GAAE,gBAAgB,GAAG,gBAAgB,aAAoB,EAC9D,gBAAgB,EAAE,OAAO,EACzB,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACU,qBAAqB,CACjC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAE9D,eAAe,EAAE,KAAK,EACtB,YAAY,GAAE,YAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAatE;;;;;;;;;;;;;;;;;OAiBG;IACU,mBAAmB,CAC/B,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAE9D,OAAO,EAAE,OAAO,EAChB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmJG;IACI,eAAe,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACpF,WAAW,EACR,WAAW,GACX,mCAAmC,GACnC,iCAAiC,GACjC,wCAAwC,EAC3C,YAAY,GAAE,YAAuD,EACrE,OAAO,CAAC,EAAE,sBAAsB;IAWjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmFG;IACI,qBAAqB,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC1F,WAAW,EAAE,KAAK,EAClB,YAAY,GAAE,YAAuD,EACrE,OAAO,CAAC,EAAE,sBAAsB;IAKjC;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACU,IAAI,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC/E,OAAO,EAAE,KAAK,EACd,cAAc,EAAE,OAAO,GAAG,MAAM,EAChC,YAAY,GAAE,YAAuD;;;;;;;;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkDG;IACU,eAAe,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC1F,WAAW,EAAE,WAAW,EACxB,YAAY,GAAE,YAAuD;IAOtE;;;;;;;;OAQG;IACU,IAAI,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC/E,WAAW,EAAE,eAAe,EAC5B,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACU,WAAW,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACtF,WAAW,EAAE,WAAW,EACxB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACU,WAAW,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACtF,MAAM,EAAE,MAAM,EACd,YAAY,GAAE,YAAuD;;;;;;;;;;;;IAKtE;;;;;;;;;;;;;;;;;OAiBG;IACU,OAAO;IAIpB;;;;;;;;;;;;;;;;OAgBG;IACU,UAAU,CACtB,KAAK,EAAE,eAAe,EACtB,IAAI,EAAE,gBAAgB,EACtB,MAAM,EAAE,gBAAgB;IAMzB;;;;;;;;;;;;OAYG;IACU,eAAe;IAI5B;;;;;;;;;;;OAWG;IACU,UAAU,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACrF,YAAY,GAAE,YAAuD;IAKtE;;;;;;;OAOG;IACU,WAAW;IAIxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuEG;IACU,QAAQ,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACnF,OAAO,EAAE,OAAO,EAChB,WAAW,EAAE,KAAK,EAAE,EACpB,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;;;;;;;;;;;;IAOtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA0DG;IACU,aAAa,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACxF,UAAU,EAAE,OAAO,EACnB,WAAW,EAAE,gBAAgB,YAAoB,EACjD,iBAAiB,EAAE,OAAO,EAAE,EAC5B,YAAY,GAAE,YAAuD;;;;;;IAWtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACU,gBAAgB,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC3F,WAAW,EAAE,wBAAwB,EACrC,WAAW,GAAE,gBAAoC,EACjD,YAAY,GAAE,YAAuD;;;;;;;IAKtE;;;;;;;;OAQG;IACU,aAAa,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACxF,OAAO,EAAE,OAAO,EAChB,SAAS,EAAE,eAAe,EAC1B,SAAS,UAAQ,EACjB,YAAY,GAAE,YACyB;IAKxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmIG;IAEU,SAAS,CACrB,CAAC,SAAS,MAAM,sBAAsB,EACtC,UAAU,SAAS,UAAU,GAAG,UAAU,EAE1C,IAAI,EAAE,CAAC,EACP,IAAI,CAAC,EAAE,qBAAqB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1D,YAAY,GAAE,UACuB,GACnC,OAAO,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IAwBnD,OAAO,CAAC,MAAM,CAAC,uBAAuB;IAItC;;;;;;;;;;OAUG;IACI,kBAAkB,CAAC,eAAe,UAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS;IAOjF;;;;;;;;;;;;;OAaG;IACU,iCAAiC,CAC7C,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC7D,YAAY,GAAE,YAAuD;IAIvE;;;;;;;;;;;;;;OAcG;IACU,eAAe,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC1F,MAAM,EAAE,YAAY,EACpB,YAAY,GAAE,YAAuD;IAKtE;;;;;;;;;;;;;OAaG;IACU,oBAAoB,CAChC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC7D,YAAY,GAAE,YAAuD;IAIvE;;;;;;;;;;;;;;;;OAgBG;IACU,eAAe,CAAC,gBAAgB,EAAE,OAAO;IAItD;;;;;;;;;;;;;;;;;;;;OAoBG;IACU,gBAAgB,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EAC3F,gBAAgB,EAAE,OAAO,EACzB,YAAY,GAAE,YAAuD;;;;;;;;;;;;IAKtE;;;;;;;;;;;;;;;;;;;;OAoBG;IACU,aAAa,CAAC,YAAY,SAAS,UAAU,GAAG,OAAO,qBAAqB,EACxF,gBAAgB,EAAE,OAAO,EACzB,YAAY,GAAE,YAAuD;;;;;;;;;;;;CAItE"}