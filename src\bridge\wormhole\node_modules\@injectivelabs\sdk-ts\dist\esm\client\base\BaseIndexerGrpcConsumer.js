import { getGrpcTransport, grpc } from '../../utils/grpc.js';
import { GrpcWebImpl } from './IndexerGrpcWebImpl.js';
export default class BaseIndexerGrpcConsumer extends GrpcWebImpl {
    module = '';
    metadata;
    constructor(endpoint) {
        super(endpoint, {
            transport: getGrpcTransport(),
        });
    }
    getGrpcWebImpl(endpoint) {
        return new BaseIndexerGrpcConsumer(endpoint);
    }
    setMetadata(map) {
        const metadata = new grpc.Metadata();
        Object.keys(map).forEach((key) => metadata.set(key, map[key]));
        this.metadata = metadata;
        return this;
    }
    clearMetadata() {
        this.metadata = undefined;
    }
    retry(grpcCall, retries = 3, delay = 1000) {
        const retryGrpcCall = async (attempt = 1) => {
            try {
                return await grpcCall();
            }
            catch (e) {
                if (attempt >= retries) {
                    throw e;
                }
                return new Promise((resolve) => setTimeout(() => resolve(retryGrpcCall(attempt + 1)), delay * attempt));
            }
        };
        return retryGrpcCall();
    }
}
