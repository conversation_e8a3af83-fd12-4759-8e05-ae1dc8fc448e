{"version": 3, "sources": ["/home/<USER>/src/pl/js-cid/webpack/universalModuleDefinition", "/home/<USER>/src/pl/js-cid/webpack/bootstrap", "/home/<USER>/src/pl/js-cid/node_modules/buffer/index.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multihashes/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/base-x/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/util.js", "/home/<USER>/src/pl/js-cid/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/webpack/buildin/global.js", "/home/<USER>/src/pl/js-cid/node_modules/base64-js/index.js", "/home/<USER>/src/pl/js-cid/node_modules/ieee754/index.js", "/home/<USER>/src/pl/js-cid/node_modules/isarray/index.js", "/home/<USER>/src/pl/js-cid/node_modules/bs58/index.js", "/home/<USER>/src/pl/js-cid/node_modules/safe-buffer/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multihashes/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/encode.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/decode.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/length.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base16.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base32.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base64.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/int-table.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/varint-table.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/print.js", "/home/<USER>/src/pl/js-cid/src/cid-util.js", "/home/<USER>/src/pl/js-cid/node_modules/class-is/index.js"], "names": ["<PERSON><PERSON><PERSON>", "require", "mh", "multibase", "multicodec", "codecs", "CIDUtil", "withIs", "CID", "constructor", "version", "codec", "multihash", "multibaseName", "_CID", "isCID", "cid", "from", "baseName", "isEncoded", "decode", "parseInt", "slice", "toString", "getCodec", "rmPrefix", "fromB58String", "validateCID", "Object", "defineProperty", "value", "<PERSON><PERSON><PERSON><PERSON>", "firstByte", "v", "buffer", "_buffer", "concat", "getCodeVarint", "Error", "prefix", "toV0", "name", "length", "toV1", "toBaseEncodedString", "base", "string", "str", "toB58String", "encode", "Symbol", "for", "toJSON", "hash", "equals", "other", "errorMsg", "checkCIDComponents", "className", "symbolName", "module", "exports", "validate", "err", "message"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;AClFA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACa;;AAEb,aAAa,mBAAO,CAAC,CAAW;;AAEhC,cAAc,mBAAO,CAAC,EAAS;;AAE/B,cAAc,mBAAO,CAAC,EAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uBAAuB;AACvB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,mBAAmB,UAAU;AAC7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;;AAEA;AACA,mBAAmB;;AAEnB;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uCAAuC,SAAS;AAChD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA;;AAEA,aAAa,iBAAiB;AAC9B;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,0BAA0B;;AAE1B;;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,2DAA2D;;AAE3D;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gDAAgD,EAAE;AAClD;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,yCAAyC;AACzC;AACA;;;AAGA;AACA;AACA,qCAAqC;;AAErC;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA,2BAA2B;;AAE3B;AACA;AACA;AACA,GAAG;;;AAGH;;AAEA;AACA,uBAAuB;AACvB,GAAG;AACH,4BAA4B;AAC5B,GAAG;;;AAGH;AACA;AACA,GAAG;;;AAGH;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;AACH,qBAAqB;;AAErB;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA,wBAAwB,eAAe;AACvC;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA,wBAAwB,QAAQ;AAChC;;AAEA,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf,GAAG;AACH;AACA;AACA,eAAe;AACf,GAAG;AACH;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;;AAEL,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,SAAS;AACT;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,CAAC;AACD;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA,yDAAyD;AACzD,GAAG;;;AAGH;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,kBAAkB;AACnC;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA,mBAAmB,cAAc;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,uDAAuD,OAAO;AAC9D;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,EAAE;;;AAGF;AACA;AACA;AACA;AACA;AACA,0CAA0C,iBAAiB;;AAE3D;AACA,yDAAyD;;AAEzD;AACA;AACA;;AAEA;AACA,+DAA+D;;AAE/D;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,qBAAqB,QAAQ;AAC7B;AACA;AACA,GAAG;AACH;AACA,eAAe,SAAS;AACxB;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA,EAAE;AACF;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;;;AAGH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,mBAAmB,SAAS;AAC5B;AACA;AACA,GAAG;AACH;AACA;;AAEA,eAAe,iBAAiB;AAChC;AACA;AACA;;AAEA;AACA,EAAE;AACF;;;AAGA;;AAEA;AACA;AACA,uDAAuD;;AAEvD,gCAAgC;;AAEhC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B,qCAAqC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;;;AAGT;AACA;AACA,OAAO;;;AAGP;AACA;AACA;AACA;AACA,OAAO;;;AAGP;AACA,KAAK;AACL;AACA;AACA;;AAEA,yBAAyB;;AAEzB;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,gBAAgB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iBAAiB,YAAY;AAC7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA,qBAAqB;AACrB,C;;;;;;;;;;;;;;AC5zDa;;AAEb;AACA,UAAU,mBAAO,CAAC,EAAa;AAC/B,UAAU,mBAAO,CAAC,EAAa;AAC/B,kBAAkB,mBAAO,CAAC,EAAa;AACvC,E;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACa;;AAEb,aAAa,mBAAO,CAAC,EAAM;;AAE3B,WAAW,mBAAO,CAAC,EAAa;;AAEhC;AACA;AACA;;AAEA,eAAe,mBAAO,CAAC,CAAQ;AAC/B;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;;;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,cAAc,4DAA4D;AAC1E;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb,YAAY;AACZ;;;AAGA;AACA,4BAA4B;AAC5B;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb,YAAY;AACZ;;AAEA;AACA;AACA;AACA,E;;;;;;;;AC3Oa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,mBAAO,CAAC,EAAa;;AAElC;AACA;AACA;AACA,kCAAkC;;AAElC,iBAAiB,qBAAqB;AACtC;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,mBAAmB,mBAAmB;AACtC,wCAAwC,mBAAmB;AAC3D;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,oBAAoB;;AAEpB,mBAAmB,0CAA0C,uBAAuB;;;AAGpF,mCAAmC,QAAQ;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;;AAEA,mBAAmB,mBAAmB;AACtC;AACA;;AAEA,oCAAoC,kBAAkB;AACtD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;;;AAGL,mBAAmB,+CAA+C;AAClE;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,E;;;;;;;ACzFA,8CAAa;;AAEb,eAAe,mBAAO,CAAC,CAAQ;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,C;;;;;;;;;;;;;;;ACpCA;;AAEA,MAAM;AAAEA;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AACA,MAAMC,EAAE,GAAGD,mBAAO,CAAC,CAAD,CAAlB;;AACA,MAAME,SAAS,GAAGF,mBAAO,CAAC,EAAD,CAAzB;;AACA,MAAMG,UAAU,GAAGH,mBAAO,CAAC,EAAD,CAA1B;;AACA,MAAMI,MAAM,GAAGJ,mBAAO,CAAC,CAAD,CAAtB;;AACA,MAAMK,OAAO,GAAGL,mBAAO,CAAC,EAAD,CAAvB;;AACA,MAAMM,MAAM,GAAGN,mBAAO,CAAC,EAAD,CAAtB;AAEA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAKA,MAAMO,GAAN,CAAU;AACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BAC,aAAW,CAAEC,OAAF,EAAWC,KAAX,EAAkBC,SAAlB,EAA6BC,aAA7B,EAA4C;AACrD,QAAIC,IAAI,CAACC,KAAL,CAAWL,OAAX,CAAJ,EAAyB;AACvB;AACA,YAAMM,GAAG,GAAGN,OAAZ;AACA,WAAKA,OAAL,GAAeM,GAAG,CAACN,OAAnB;AACA,WAAKC,KAAL,GAAaK,GAAG,CAACL,KAAjB;AACA,WAAKC,SAAL,GAAiBZ,MAAM,CAACiB,IAAP,CAAYD,GAAG,CAACJ,SAAhB,CAAjB,CALuB,CAMvB;;AACA,WAAKC,aAAL,GAAqBG,GAAG,CAACH,aAAJ,KAAsBG,GAAG,CAACN,OAAJ,KAAgB,CAAhB,GAAoB,WAApB,GAAkC,QAAxD,CAArB;AACA;AACD;;AAED,QAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;AAC/B;AACA,YAAMQ,QAAQ,GAAGf,SAAS,CAACgB,SAAV,CAAoBT,OAApB,CAAjB;;AACA,UAAIQ,QAAJ,EAAc;AACZ;AACA,cAAMF,GAAG,GAAGb,SAAS,CAACiB,MAAV,CAAiBV,OAAjB,CAAZ;AACA,aAAKA,OAAL,GAAeW,QAAQ,CAACL,GAAG,CAACM,KAAJ,CAAU,CAAV,EAAa,CAAb,EAAgBC,QAAhB,CAAyB,KAAzB,CAAD,EAAkC,EAAlC,CAAvB;AACA,aAAKZ,KAAL,GAAaP,UAAU,CAACoB,QAAX,CAAoBR,GAAG,CAACM,KAAJ,CAAU,CAAV,CAApB,CAAb;AACA,aAAKV,SAAL,GAAiBR,UAAU,CAACqB,QAAX,CAAoBT,GAAG,CAACM,KAAJ,CAAU,CAAV,CAApB,CAAjB;AACA,aAAKT,aAAL,GAAqBK,QAArB;AACD,OAPD,MAOO;AACL;AACA,aAAKR,OAAL,GAAe,CAAf;AACA,aAAKC,KAAL,GAAa,QAAb;AACA,aAAKC,SAAL,GAAiBV,EAAE,CAACwB,aAAH,CAAiBhB,OAAjB,CAAjB;AACA,aAAKG,aAAL,GAAqB,WAArB;AACD;;AACDL,SAAG,CAACmB,WAAJ,CAAgB,IAAhB;AACAC,YAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,QAA5B,EAAsC;AAAEC,aAAK,EAAEpB;AAAT,OAAtC;AACA;AACD;;AAED,QAAIV,MAAM,CAAC+B,QAAP,CAAgBrB,OAAhB,CAAJ,EAA8B;AAC5B,YAAMsB,SAAS,GAAGtB,OAAO,CAACY,KAAR,CAAc,CAAd,EAAiB,CAAjB,CAAlB;AACA,YAAMW,CAAC,GAAGZ,QAAQ,CAACW,SAAS,CAACT,QAAV,CAAmB,KAAnB,CAAD,EAA4B,EAA5B,CAAlB;;AACA,UAAIU,CAAC,KAAK,CAAV,EAAa;AACX;AACA,cAAMjB,GAAG,GAAGN,OAAZ;AACA,aAAKA,OAAL,GAAeuB,CAAf;AACA,aAAKtB,KAAL,GAAaP,UAAU,CAACoB,QAAX,CAAoBR,GAAG,CAACM,KAAJ,CAAU,CAAV,CAApB,CAAb;AACA,aAAKV,SAAL,GAAiBR,UAAU,CAACqB,QAAX,CAAoBT,GAAG,CAACM,KAAJ,CAAU,CAAV,CAApB,CAAjB;AACA,aAAKT,aAAL,GAAqB,QAArB;AACD,OAPD,MAOO;AACL;AACA,aAAKH,OAAL,GAAe,CAAf;AACA,aAAKC,KAAL,GAAa,QAAb;AACA,aAAKC,SAAL,GAAiBF,OAAjB;AACA,aAAKG,aAAL,GAAqB,WAArB;AACD;;AACDL,SAAG,CAACmB,WAAJ,CAAgB,IAAhB;AACA;AACD,KArDoD,CAuDrD;;AAEA;;;;;AAGA,SAAKjB,OAAL,GAAeA,OAAf;AAEA;;;;AAGA,SAAKC,KAAL,GAAaA,KAAb;AAEA;;;;AAGA,SAAKC,SAAL,GAAiBA,SAAjB;AAEA;;;;AAGA,SAAKC,aAAL,GAAqBA,aAAa,KAAKH,OAAO,KAAK,CAAZ,GAAgB,WAAhB,GAA8B,QAAnC,CAAlC;AAEAF,OAAG,CAACmB,WAAJ,CAAgB,IAAhB;AACD;AAED;;;;;;;;;;AAQA,MAAIO,MAAJ,GAAc;AACZ,QAAIA,MAAM,GAAG,KAAKC,OAAlB;;AAEA,QAAI,CAACD,MAAL,EAAa;AACX,UAAI,KAAKxB,OAAL,KAAiB,CAArB,EAAwB;AACtBwB,cAAM,GAAG,KAAKtB,SAAd;AACD,OAFD,MAEO,IAAI,KAAKF,OAAL,KAAiB,CAArB,EAAwB;AAC7BwB,cAAM,GAAGlC,MAAM,CAACoC,MAAP,CAAc,CACrBpC,MAAM,CAACiB,IAAP,CAAY,IAAZ,EAAkB,KAAlB,CADqB,EAErBb,UAAU,CAACiC,aAAX,CAAyB,KAAK1B,KAA9B,CAFqB,EAGrB,KAAKC,SAHgB,CAAd,CAAT;AAKD,OANM,MAMA;AACL,cAAM,IAAI0B,KAAJ,CAAU,qBAAV,CAAN;AACD,OAXU,CAaX;;;AACAV,YAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,SAA5B,EAAuC;AAAEC,aAAK,EAAEI;AAAT,OAAvC;AACD;;AAED,WAAOA,MAAP;AACD;AAED;;;;;;;;AAMA,MAAIK,MAAJ,GAAc;AACZ,WAAOvC,MAAM,CAACoC,MAAP,CAAc,CACnBpC,MAAM,CAACiB,IAAP,YAAgB,KAAKP,OAArB,GAAgC,KAAhC,CADmB,EAEnBN,UAAU,CAACiC,aAAX,CAAyB,KAAK1B,KAA9B,CAFmB,EAGnBT,EAAE,CAACqC,MAAH,CAAU,KAAK3B,SAAf,CAHmB,CAAd,CAAP;AAKD;AAED;;;;;;;AAKA4B,MAAI,GAAI;AACN,QAAI,KAAK7B,KAAL,KAAe,QAAnB,EAA6B;AAC3B,YAAM,IAAI2B,KAAJ,CAAU,0CAAV,CAAN;AACD;;AAED,UAAM;AAAEG,UAAF;AAAQC;AAAR,QAAmBxC,EAAE,CAACkB,MAAH,CAAU,KAAKR,SAAf,CAAzB;;AAEA,QAAI6B,IAAI,KAAK,UAAb,EAAyB;AACvB,YAAM,IAAIH,KAAJ,CAAU,oDAAV,CAAN;AACD;;AAED,QAAII,MAAM,KAAK,EAAf,EAAmB;AACjB,YAAM,IAAIJ,KAAJ,CAAU,mDAAV,CAAN;AACD;;AAED,WAAO,IAAIxB,IAAJ,CAAS,CAAT,EAAY,KAAKH,KAAjB,EAAwB,KAAKC,SAA7B,CAAP;AACD;AAED;;;;;;;AAKA+B,MAAI,GAAI;AACN,WAAO,IAAI7B,IAAJ,CAAS,CAAT,EAAY,KAAKH,KAAjB,EAAwB,KAAKC,SAA7B,CAAP;AACD;AAED;;;;;;;;AAMAgC,qBAAmB,CAAEC,IAAI,GAAG,KAAKhC,aAAd,EAA6B;AAC9C,QAAI,KAAKiC,MAAL,IAAeD,IAAI,KAAK,KAAKhC,aAAjC,EAAgD;AAC9C,aAAO,KAAKiC,MAAZ;AACD;;AACD,QAAIC,GAAG,GAAG,IAAV;;AACA,QAAI,KAAKrC,OAAL,KAAiB,CAArB,EAAwB;AACtB,UAAImC,IAAI,KAAK,WAAb,EAA0B;AACxB,cAAM,IAAIP,KAAJ,CAAU,gIAAV,CAAN;AACD;;AACDS,SAAG,GAAG7C,EAAE,CAAC8C,WAAH,CAAe,KAAKpC,SAApB,CAAN;AACD,KALD,MAKO,IAAI,KAAKF,OAAL,KAAiB,CAArB,EAAwB;AAC7BqC,SAAG,GAAG5C,SAAS,CAAC8C,MAAV,CAAiBJ,IAAjB,EAAuB,KAAKX,MAA5B,EAAoCX,QAApC,EAAN;AACD,KAFM,MAEA;AACL,YAAM,IAAIe,KAAJ,CAAU,qBAAV,CAAN;AACD;;AACD,QAAIO,IAAI,KAAK,KAAKhC,aAAlB,EAAiC;AAC/B;AACAe,YAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,QAA5B,EAAsC;AAAEC,aAAK,EAAEiB;AAAT,OAAtC;AACD;;AACD,WAAOA,GAAP;AACD;AAED;;;;;;;AAKA,GAACG,MAAM,CAACC,GAAP,CAAW,4BAAX,CAAD,IAA8C;AAC5C,WAAO,SAAS,KAAK5B,QAAL,EAAT,GAA2B,GAAlC;AACD;;AAEDA,UAAQ,CAAEsB,IAAF,EAAQ;AACd,WAAO,KAAKD,mBAAL,CAAyBC,IAAzB,CAAP;AACD;AAED;;;;;;;AAKAO,QAAM,GAAI;AACR,WAAO;AACLzC,WAAK,EAAE,KAAKA,KADP;AAELD,aAAO,EAAE,KAAKA,OAFT;AAGL2C,UAAI,EAAE,KAAKzC;AAHN,KAAP;AAKD;AAED;;;;;;;;AAMA0C,QAAM,CAAEC,KAAF,EAAS;AACb,WAAO,KAAK5C,KAAL,KAAe4C,KAAK,CAAC5C,KAArB,IACL,KAAKD,OAAL,KAAiB6C,KAAK,CAAC7C,OADlB,IAEL,KAAKE,SAAL,CAAe0C,MAAf,CAAsBC,KAAK,CAAC3C,SAA5B,CAFF;AAGD;AAED;;;;;;;;;AAOA,SAAOe,WAAP,CAAoB4B,KAApB,EAA2B;AACzB,UAAMC,QAAQ,GAAGlD,OAAO,CAACmD,kBAAR,CAA2BF,KAA3B,CAAjB;;AACA,QAAIC,QAAJ,EAAc;AACZ,YAAM,IAAIlB,KAAJ,CAAUkB,QAAV,CAAN;AACD;AACF;;AA1QO;;AA6QV,MAAM1C,IAAI,GAAGP,MAAM,CAACC,GAAD,EAAM;AACvBkD,WAAS,EAAE,KADY;AAEvBC,YAAU,EAAE;AAFW,CAAN,CAAnB;;AAKA7C,IAAI,CAACT,MAAL,GAAcA,MAAd;AAEAuD,MAAM,CAACC,OAAP,GAAiB/C,IAAjB,C;;;;;;;ACnTa;;AAEb,MAAM;;AAEN;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,4CAA4C;;;AAG5C,mB;;;;;;;ACnBa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,kCAAkC,SAAS;AAC3C;AACA;AACA,CAAC;AACD;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;;;AAGA;AACA;AACA;AACA;AACA,CAAC;;;AAGD;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;;AAElB;AACA;;AAEA,aAAa,SAAS;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,qBAAqB,SAAS;AAC9B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;;AAE3B;AACA,6BAA6B;AAC7B;;AAEA,0CAA0C,UAAU;AACpD;AACA,GAAG;;;AAGH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA,C;;;;;;;ACtHa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA,QAAQ,WAAW;;AAEnB;AACA;;AAEA,QAAQ,UAAU;;AAElB;AACA,E;;;;;;;ACzFa;;AAEb,iBAAiB;;AAEjB;AACA;AACA,E;;;;;;;ACNa;;AAEb,YAAY,mBAAO,CAAC,CAAQ;;AAE5B;AACA,iC;;;;;;;ACLa;;AAEb;AACA,aAAa,mBAAO,CAAC,CAAQ;;AAE7B,2BAA2B;;AAE3B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,uDAAuD;;AAEvD;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,E;;;;;;;ACvEA;;AAEA;AACa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,E;;;;;;;AC9/BY;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,C;;;;;;;AC1Ba;;AAEb;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,C;;;;;;;AC3Ba;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,E;;;;;;;ACdA;AACA;AACA;AACA;AACa;;AAEb,kBAAkB,mBAAO,CAAC,EAAa;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB;AACA,aAAa;AACb;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB,aAAa;AACb;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,aAAa;AACb;AACA;;;AAGA;AACA;AACA;AACA,GAAG;;;AAGH;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB;AACA,aAAa;AACb;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA;;AAEA;AACA,C;;;;;;;;ACrIa;;AAEb,aAAa,mBAAO,CAAC,EAAW;;AAEhC,cAAc,mBAAO,CAAC,CAAQ;;AAE9B,eAAe,mBAAO,CAAC,EAAU;;AAEjC,eAAe,mBAAO,CAAC,EAAU;;AAEjC,eAAe,mBAAO,CAAC,EAAU,EAAE;;;AAGnC;AACA;AACA;AACA;AACA,CAAC,IAAI;AACL;AACA;AACA;AACA,CAAC,IAAI;AACL;AACA;AACA;AACA,E;;;;;;;ACzBa;;AAEb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA,sB;;;;;;;AC3BA,8CAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;;ACvBA,8CAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,iBAAiB,YAAY;AAC7B;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;;AChFA,8CAAa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,E;;;;;;;;AC5CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa;;AAEb,eAAe,mBAAO,CAAC,CAAQ;;AAE/B,iBAAiB,mBAAO,CAAC,EAAa;;AAEtC,8BAA8B,mBAAO,CAAC,EAAgB;;AAEtD,aAAa,mBAAO,CAAC,CAAQ;;AAE7B;AACA;AACA;AACA;AACA,WAAW,cAAc;AACzB,WAAW,OAAO;AAClB,aAAa;AACb;;AAEA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa;AACb;;;AAGA;AACA;AACA,EAAE;;;AAGF,kBAAkB,mBAAO,CAAC,EAAa;;AAEvC,kCAAkC;;AAElC,gBAAgB,mBAAO,CAAC,EAAS,E;;;;;;;;AC9IpB;;AAEb,kBAAkB,mBAAO,CAAC,CAAmB,EAAE;;;AAG/C;;AAEA;AACA;AACA;AACA;;AAEA,0C;;;;;;;ACZa;;AAEb,kBAAkB,mBAAO,CAAC,CAAmB;;AAE7C,qBAAqB,mBAAO,CAAC,CAAQ,eAAe;;;AAGpD;;AAEA;AACA;AACA;AACA;;AAEA,4C;;;;;;;ACda;;AAEb,cAAc,mBAAO,CAAC,CAAmB,EAAE;;;AAG3C;;AAEA;AACA;AACA;;AAEA,0C;;;;;;;ACXa;;AAEb,cAAc,mBAAO,CAAC,CAAmB,EAAE;;;AAG3C;;AAEA;AACA;AACA;;AAEA,4C;;;;;;;ACXA;;AAEA,MAAMZ,EAAE,GAAGD,mBAAO,CAAC,CAAD,CAAlB;;AACA,MAAM;AAAED;AAAF,IAAaC,mBAAO,CAAC,CAAD,CAA1B;;AACA,IAAIK,OAAO,GAAG;AACZ;;;;;;;;AAQAmD,oBAAkB,EAAE,4BAAUF,KAAV,EAAiB;AACnC,QAAIA,KAAK,IAAI,IAAb,EAAmB;AACjB,aAAO,gCAAP;AACD;;AAED,QAAI,EAAEA,KAAK,CAAC7C,OAAN,KAAkB,CAAlB,IAAuB6C,KAAK,CAAC7C,OAAN,KAAkB,CAA3C,CAAJ,EAAmD;AACjD,aAAO,mDAAP;AACD;;AAED,QAAI,OAAO6C,KAAK,CAAC5C,KAAb,KAAuB,QAA3B,EAAqC;AACnC,aAAO,sBAAP;AACD;;AAED,QAAI4C,KAAK,CAAC7C,OAAN,KAAkB,CAAtB,EAAyB;AACvB,UAAI6C,KAAK,CAAC5C,KAAN,KAAgB,QAApB,EAA8B;AAC5B,eAAO,kCAAP;AACD;;AACD,UAAI4C,KAAK,CAAC1C,aAAN,KAAwB,WAA5B,EAAyC;AACvC,eAAO,6CAAP;AACD;AACF;;AAED,QAAI,CAACb,MAAM,CAAC+B,QAAP,CAAgBwB,KAAK,CAAC3C,SAAtB,CAAL,EAAuC;AACrC,aAAO,4BAAP;AACD;;AAED,QAAI;AACFV,QAAE,CAAC4D,QAAH,CAAYP,KAAK,CAAC3C,SAAlB;AACD,KAFD,CAEE,OAAOmD,GAAP,EAAY;AACZ,UAAIP,QAAQ,GAAGO,GAAG,CAACC,OAAnB;;AACA,UAAI,CAACR,QAAL,EAAe;AAAE;AACfA,gBAAQ,GAAG,6BAAX;AACD;;AACD,aAAOA,QAAP;AACD;AACF;AA5CW,CAAd;AA+CAI,MAAM,CAACC,OAAP,GAAiBvD,OAAjB,C;;;;;;;ACnDa;;AAEb;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,GAAG;;AAEH;;AAEA;AACA;;AAEA;AACA,mC", "file": "index.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Cids\"] = factory();\n\telse\n\t\troot[\"Cids\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 6);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "\"use strict\";\n\nmodule.exports = {\n  encode: require('./encode.js'),\n  decode: require('./decode.js'),\n  encodingLength: require('./length.js')\n};", "/**\n * Multihash implementation in JavaScript.\n *\n * @module multihash\n */\n'use strict';\n\nconst bs58 = require('bs58');\n\nconst cs = require('./constants');\n\nexports.names = cs.names;\nexports.codes = cs.codes;\nexports.defaultLengths = cs.defaultLengths;\n\nconst varint = require('varint');\n/**\n * Convert the given multihash to a hex encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\n\n\nexports.toHexString = function toHexString(hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer');\n  }\n\n  return hash.toString('hex');\n};\n/**\n * Convert the given hex encoded string to a multihash.\n *\n * @param {string} hash\n * @returns {Buffer}\n */\n\n\nexports.fromHexString = function fromHexString(hash) {\n  return Buffer.from(hash, 'hex');\n};\n/**\n * Convert the given multihash to a base58 encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\n\n\nexports.toB58String = function toB58String(hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer');\n  }\n\n  return bs58.encode(hash);\n};\n/**\n * Convert the given base58 encoded string to a multihash.\n *\n * @param {string|Buffer} hash\n * @returns {Buffer}\n */\n\n\nexports.fromB58String = function fromB58String(hash) {\n  let encoded = hash;\n\n  if (Buffer.isBuffer(hash)) {\n    encoded = hash.toString();\n  }\n\n  return Buffer.from(bs58.decode(encoded));\n};\n/**\n * Decode a hash from the given multihash.\n *\n * @param {Buffer} buf\n * @returns {{code: number, name: string, length: number, digest: Buffer}} result\n */\n\n\nexports.decode = function decode(buf) {\n  if (!Buffer.isBuffer(buf)) {\n    throw new Error('multihash must be a Buffer');\n  }\n\n  if (buf.length < 3) {\n    throw new Error('multihash too short. must be > 3 bytes.');\n  }\n\n  const code = varint.decode(buf);\n\n  if (!exports.isValidCode(code)) {\n    throw new Error(\"multihash unknown function code: 0x\".concat(code.toString(16)));\n  }\n\n  buf = buf.slice(varint.decode.bytes);\n  const len = varint.decode(buf);\n\n  if (len < 1) {\n    throw new Error(\"multihash invalid length: 0x\".concat(len.toString(16)));\n  }\n\n  buf = buf.slice(varint.decode.bytes);\n\n  if (buf.length !== len) {\n    throw new Error(\"multihash length inconsistent: 0x\".concat(buf.toString('hex')));\n  }\n\n  return {\n    code: code,\n    name: cs.codes[code],\n    length: len,\n    digest: buf\n  };\n};\n/**\n *  Encode a hash digest along with the specified function code.\n *\n * > **Note:** the length is derived from the length of the digest itself.\n *\n * @param {Buffer} digest\n * @param {string|number} code\n * @param {number} [length]\n * @returns {Buffer}\n */\n\n\nexports.encode = function encode(digest, code, length) {\n  if (!digest || code === undefined) {\n    throw new Error('multihash encode requires at least two args: digest, code');\n  } // ensure it's a hashfunction code.\n\n\n  const hashfn = exports.coerceCode(code);\n\n  if (!Buffer.isBuffer(digest)) {\n    throw new Error('digest should be a Buffer');\n  }\n\n  if (length == null) {\n    length = digest.length;\n  }\n\n  if (length && digest.length !== length) {\n    throw new Error('digest length should be equal to specified length.');\n  }\n\n  return Buffer.concat([Buffer.from(varint.encode(hashfn)), Buffer.from(varint.encode(length)), digest]);\n};\n/**\n * Converts a hash function name into the matching code.\n * If passed a number it will return the number if it's a valid code.\n * @param {string|number} name\n * @returns {number}\n */\n\n\nexports.coerceCode = function coerceCode(name) {\n  let code = name;\n\n  if (typeof name === 'string') {\n    if (cs.names[name] === undefined) {\n      throw new Error(\"Unrecognized hash function named: \".concat(name));\n    }\n\n    code = cs.names[name];\n  }\n\n  if (typeof code !== 'number') {\n    throw new Error(\"Hash function code should be a number. Got: \".concat(code));\n  }\n\n  if (cs.codes[code] === undefined && !exports.isAppCode(code)) {\n    throw new Error(\"Unrecognized function code: \".concat(code));\n  }\n\n  return code;\n};\n/**\n * Checks wether a code is part of the app range\n *\n * @param {number} code\n * @returns {boolean}\n */\n\n\nexports.isAppCode = function appCode(code) {\n  return code > 0 && code < 0x10;\n};\n/**\n * Checks whether a multihash code is valid.\n *\n * @param {number} code\n * @returns {boolean}\n */\n\n\nexports.isValidCode = function validCode(code) {\n  if (exports.isAppCode(code)) {\n    return true;\n  }\n\n  if (cs.codes[code]) {\n    return true;\n  }\n\n  return false;\n};\n/**\n * Check if the given buffer is a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\n\n\nfunction validate(multihash) {\n  exports.decode(multihash); // throws if bad.\n}\n\nexports.validate = validate;\n/**\n * Returns a prefix from a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\n\nexports.prefix = function prefix(multihash) {\n  validate(multihash);\n  return multihash.slice(0, 2);\n};", "\"use strict\";\n\n// base-x encoding\n// Forked from https://github.com/cryptocoinjs/bs58\n// Originally written by <PERSON> for BitcoinJ\n// Copyright (c) 2011 Google Inc\n// Ported to JavaScript by <PERSON>\n// Merged Buffer refactorings from base58-native by <PERSON>\n// Copyright (c) 2013 BitPay Inc\nvar Buffer = require('safe-buffer').Buffer;\n\nmodule.exports = function base(ALPHABET) {\n  var ALPHABET_MAP = {};\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0); // pre-compute lookup table\n\n  for (var z = 0; z < ALPHABET.length; z++) {\n    var x = ALPHABET.charAt(z);\n    if (ALPHABET_MAP[x] !== undefined) throw new TypeError(x + ' is ambiguous');\n    ALPHABET_MAP[x] = z;\n  }\n\n  function encode(source) {\n    if (source.length === 0) return '';\n    var digits = [0];\n\n    for (var i = 0; i < source.length; ++i) {\n      for (var j = 0, carry = source[i]; j < digits.length; ++j) {\n        carry += digits[j] << 8;\n        digits[j] = carry % BASE;\n        carry = carry / BASE | 0;\n      }\n\n      while (carry > 0) {\n        digits.push(carry % BASE);\n        carry = carry / BASE | 0;\n      }\n    }\n\n    var string = ''; // deal with leading zeros\n\n    for (var k = 0; source[k] === 0 && k < source.length - 1; ++k) string += LEADER; // convert digits to a string\n\n\n    for (var q = digits.length - 1; q >= 0; --q) string += ALPHABET[digits[q]];\n\n    return string;\n  }\n\n  function decodeUnsafe(string) {\n    if (typeof string !== 'string') throw new TypeError('Expected String');\n    if (string.length === 0) return Buffer.allocUnsafe(0);\n    var bytes = [0];\n\n    for (var i = 0; i < string.length; i++) {\n      var value = ALPHABET_MAP[string[i]];\n      if (value === undefined) return;\n\n      for (var j = 0, carry = value; j < bytes.length; ++j) {\n        carry += bytes[j] * BASE;\n        bytes[j] = carry & 0xff;\n        carry >>= 8;\n      }\n\n      while (carry > 0) {\n        bytes.push(carry & 0xff);\n        carry >>= 8;\n      }\n    } // deal with leading zeros\n\n\n    for (var k = 0; string[k] === LEADER && k < string.length - 1; ++k) {\n      bytes.push(0);\n    }\n\n    return Buffer.from(bytes.reverse());\n  }\n\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) return buffer;\n    throw new Error('Non-base' + BASE + ' character');\n  }\n\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n};", "'use strict';\n\nconst varint = require('varint');\n\nmodule.exports = {\n  numberTo<PERSON>uffer,\n  bufferToNumber,\n  varintBufferEncode,\n  varintBufferDecode,\n  varintEncode\n};\n\nfunction bufferToNumber(buf) {\n  return parseInt(buf.toString('hex'), 16);\n}\n\nfunction numberToBuffer(num) {\n  let hexString = num.toString(16);\n\n  if (hexString.length % 2 === 1) {\n    hexString = '0' + hexString;\n  }\n\n  return Buffer.from(hexString, 'hex');\n}\n\nfunction varintBufferEncode(input) {\n  return Buffer.from(varint.encode(bufferToNumber(input)));\n}\n\nfunction varintBufferDecode(input) {\n  return numberToBuffer(varint.decode(input));\n}\n\nfunction varintEncode(num) {\n  return Buffer.from(varint.encode(num));\n}", "'use strict'\n\nconst { Buffer } = require('buffer')\nconst mh = require('multihashes')\nconst multibase = require('multibase')\nconst multicodec = require('multicodec')\nconst codecs = require('multicodec/src/base-table.json')\nconst CIDUtil = require('./cid-util')\nconst withIs = require('class-is')\n\n/**\n * @typedef {Object} SerializedCID\n * @param {string} codec\n * @param {number} version\n * @param {Buffer} multihash\n */\n\n/**\n * Test if the given input is a CID.\n * @function isCID\n * @memberof CID\n * @static\n * @param {any} other\n * @returns {bool}\n */\n\n/**\n * Class representing a CID `<mbase><version><mcodec><mhash>`\n * , as defined in [ipld/cid](https://github.com/multiformats/cid).\n * @class CID\n */\nclass CID {\n  /**\n   * Create a new CID.\n   *\n   * The algorithm for argument input is roughly:\n   * ```\n   * if (cid)\n   *   -> create a copy\n   * else if (str)\n   *   if (1st char is on multibase table) -> CID String\n   *   else -> bs58 encoded multihash\n   * else if (Buffer)\n   *   if (1st byte is 0 or 1) -> CID\n   *   else -> multihash\n   * else if (Number)\n   *   -> construct CID by parts\n   * ```\n   *\n   * @param {string|Buffer|CID} version\n   * @param {string} [codec]\n   * @param {Buffer} [multihash]\n   * @param {string} [multibaseName]\n   *\n   * @example\n   * new CID(<version>, <codec>, <multihash>, <multibaseName>)\n   * new CID(<cidStr>)\n   * new CID(<cid.buffer>)\n   * new CID(<multihash>)\n   * new CID(<bs58 encoded multihash>)\n   * new CID(<cid>)\n   */\n  constructor (version, codec, multihash, multibaseName) {\n    if (_CID.isCID(version)) {\n      // version is an exising CID instance\n      const cid = version\n      this.version = cid.version\n      this.codec = cid.codec\n      this.multihash = Buffer.from(cid.multihash)\n      // Default guard for when a CID < 0.7 is passed with no multibaseName\n      this.multibaseName = cid.multibaseName || (cid.version === 0 ? 'base58btc' : 'base32')\n      return\n    }\n\n    if (typeof version === 'string') {\n      // e.g. 'base32' or false\n      const baseName = multibase.isEncoded(version)\n      if (baseName) {\n        // version is a CID String encoded with multibase, so v1\n        const cid = multibase.decode(version)\n        this.version = parseInt(cid.slice(0, 1).toString('hex'), 16)\n        this.codec = multicodec.getCodec(cid.slice(1))\n        this.multihash = multicodec.rmPrefix(cid.slice(1))\n        this.multibaseName = baseName\n      } else {\n        // version is a base58btc string multihash, so v0\n        this.version = 0\n        this.codec = 'dag-pb'\n        this.multihash = mh.fromB58String(version)\n        this.multibaseName = 'base58btc'\n      }\n      CID.validateCID(this)\n      Object.defineProperty(this, 'string', { value: version })\n      return\n    }\n\n    if (Buffer.isBuffer(version)) {\n      const firstByte = version.slice(0, 1)\n      const v = parseInt(firstByte.toString('hex'), 16)\n      if (v === 1) {\n        // version is a CID buffer\n        const cid = version\n        this.version = v\n        this.codec = multicodec.getCodec(cid.slice(1))\n        this.multihash = multicodec.rmPrefix(cid.slice(1))\n        this.multibaseName = 'base32'\n      } else {\n        // version is a raw multihash buffer, so v0\n        this.version = 0\n        this.codec = 'dag-pb'\n        this.multihash = version\n        this.multibaseName = 'base58btc'\n      }\n      CID.validateCID(this)\n      return\n    }\n\n    // otherwise, assemble the CID from the parameters\n\n    /**\n     * @type {number}\n     */\n    this.version = version\n\n    /**\n     * @type {string}\n     */\n    this.codec = codec\n\n    /**\n     * @type {Buffer}\n     */\n    this.multihash = multihash\n\n    /**\n     * @type {string}\n     */\n    this.multibaseName = multibaseName || (version === 0 ? 'base58btc' : 'base32')\n\n    CID.validateCID(this)\n  }\n\n  /**\n   * The CID as a `Buffer`\n   *\n   * @return {Buffer}\n   * @readonly\n   *\n   * @memberOf CID\n   */\n  get buffer () {\n    let buffer = this._buffer\n\n    if (!buffer) {\n      if (this.version === 0) {\n        buffer = this.multihash\n      } else if (this.version === 1) {\n        buffer = Buffer.concat([\n          Buffer.from('01', 'hex'),\n          multicodec.getCodeVarint(this.codec),\n          this.multihash\n        ])\n      } else {\n        throw new Error('unsupported version')\n      }\n\n      // Cache this buffer so it doesn't have to be recreated\n      Object.defineProperty(this, '_buffer', { value: buffer })\n    }\n\n    return buffer\n  }\n\n  /**\n   * Get the prefix of the CID.\n   *\n   * @returns {Buffer}\n   * @readonly\n   */\n  get prefix () {\n    return Buffer.concat([\n      Buffer.from(`0${this.version}`, 'hex'),\n      multicodec.getCodeVarint(this.codec),\n      mh.prefix(this.multihash)\n    ])\n  }\n\n  /**\n   * Convert to a CID of version `0`.\n   *\n   * @returns {CID}\n   */\n  toV0 () {\n    if (this.codec !== 'dag-pb') {\n      throw new Error('Cannot convert a non dag-pb CID to CIDv0')\n    }\n\n    const { name, length } = mh.decode(this.multihash)\n\n    if (name !== 'sha2-256') {\n      throw new Error('Cannot convert non sha2-256 multihash CID to CIDv0')\n    }\n\n    if (length !== 32) {\n      throw new Error('Cannot convert non 32 byte multihash CID to CIDv0')\n    }\n\n    return new _CID(0, this.codec, this.multihash)\n  }\n\n  /**\n   * Convert to a CID of version `1`.\n   *\n   * @returns {CID}\n   */\n  toV1 () {\n    return new _CID(1, this.codec, this.multihash)\n  }\n\n  /**\n   * Encode the CID into a string.\n   *\n   * @param {string} [base=this.multibaseName] - Base encoding to use.\n   * @returns {string}\n   */\n  toBaseEncodedString (base = this.multibaseName) {\n    if (this.string && base === this.multibaseName) {\n      return this.string\n    }\n    let str = null\n    if (this.version === 0) {\n      if (base !== 'base58btc') {\n        throw new Error('not supported with CIDv0, to support different bases, please migrate the instance do CIDv1, you can do that through cid.toV1()')\n      }\n      str = mh.toB58String(this.multihash)\n    } else if (this.version === 1) {\n      str = multibase.encode(base, this.buffer).toString()\n    } else {\n      throw new Error('unsupported version')\n    }\n    if (base === this.multibaseName) {\n      // cache the string value\n      Object.defineProperty(this, 'string', { value: str })\n    }\n    return str\n  }\n\n  /**\n   * CID(QmdfTbBqBPQ7VNxZEYEj14VmRuZBkqFbiwReogJgS1zR1n)\n   *\n   * @returns {String}\n   */\n  [Symbol.for('nodejs.util.inspect.custom')] () {\n    return 'CID(' + this.toString() + ')'\n  }\n\n  toString (base) {\n    return this.toBaseEncodedString(base)\n  }\n\n  /**\n   * Serialize to a plain object.\n   *\n   * @returns {SerializedCID}\n   */\n  toJSON () {\n    return {\n      codec: this.codec,\n      version: this.version,\n      hash: this.multihash\n    }\n  }\n\n  /**\n   * Compare equality with another CID.\n   *\n   * @param {CID} other\n   * @returns {bool}\n   */\n  equals (other) {\n    return this.codec === other.codec &&\n      this.version === other.version &&\n      this.multihash.equals(other.multihash)\n  }\n\n  /**\n   * Test if the given input is a valid CID object.\n   * Throws if it is not.\n   *\n   * @param {any} other\n   * @returns {void}\n   */\n  static validateCID (other) {\n    const errorMsg = CIDUtil.checkCIDComponents(other)\n    if (errorMsg) {\n      throw new Error(errorMsg)\n    }\n  }\n}\n\nconst _CID = withIs(CID, {\n  className: 'CID',\n  symbolName: '@ipld/js-cid/CID'\n})\n\n_CID.codecs = codecs\n\nmodule.exports = _CID\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "\"use strict\";\n\nvar basex = require('base-x');\n\nvar ALPHABET = '**********************************************************';\nmodule.exports = basex(ALPHABET);", "\"use strict\";\n\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\n\nvar Buffer = buffer.Buffer; // alternative to using Object.keys for old browsers\n\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\n\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\n\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype); // Copy static methods from Buffer\n\ncopyProps(Buffer, SafeBuffer);\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n\n  return Buffer(arg, encodingOrOffset, length);\n};\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  var buf = Buffer(size);\n\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n\n  return buf;\n};\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return Buffer(size);\n};\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return buffer.SlowBuffer(size);\n};", "/* eslint quote-props: off */\n\n/* eslint key-spacing: off */\n'use strict';\n\nexports.names = Object.freeze({\n  'identity': 0x0,\n  'sha1': 0x11,\n  'sha2-256': 0x12,\n  'sha2-512': 0x13,\n  'dbl-sha2-256': 0x56,\n  'sha3-224': 0x17,\n  'sha3-256': 0x16,\n  'sha3-384': 0x15,\n  'sha3-512': 0x14,\n  'shake-128': 0x18,\n  'shake-256': 0x19,\n  'keccak-224': 0x1A,\n  'keccak-256': 0x1B,\n  'keccak-384': 0x1C,\n  'keccak-512': 0x1D,\n  'murmur3-128': 0x22,\n  'murmur3-32': 0x23,\n  'blake2b-8': 0xb201,\n  'blake2b-16': 0xb202,\n  'blake2b-24': 0xb203,\n  'blake2b-32': 0xb204,\n  'blake2b-40': 0xb205,\n  'blake2b-48': 0xb206,\n  'blake2b-56': 0xb207,\n  'blake2b-64': 0xb208,\n  'blake2b-72': 0xb209,\n  'blake2b-80': 0xb20a,\n  'blake2b-88': 0xb20b,\n  'blake2b-96': 0xb20c,\n  'blake2b-104': 0xb20d,\n  'blake2b-112': 0xb20e,\n  'blake2b-120': 0xb20f,\n  'blake2b-128': 0xb210,\n  'blake2b-136': 0xb211,\n  'blake2b-144': 0xb212,\n  'blake2b-152': 0xb213,\n  'blake2b-160': 0xb214,\n  'blake2b-168': 0xb215,\n  'blake2b-176': 0xb216,\n  'blake2b-184': 0xb217,\n  'blake2b-192': 0xb218,\n  'blake2b-200': 0xb219,\n  'blake2b-208': 0xb21a,\n  'blake2b-216': 0xb21b,\n  'blake2b-224': 0xb21c,\n  'blake2b-232': 0xb21d,\n  'blake2b-240': 0xb21e,\n  'blake2b-248': 0xb21f,\n  'blake2b-256': 0xb220,\n  'blake2b-264': 0xb221,\n  'blake2b-272': 0xb222,\n  'blake2b-280': 0xb223,\n  'blake2b-288': 0xb224,\n  'blake2b-296': 0xb225,\n  'blake2b-304': 0xb226,\n  'blake2b-312': 0xb227,\n  'blake2b-320': 0xb228,\n  'blake2b-328': 0xb229,\n  'blake2b-336': 0xb22a,\n  'blake2b-344': 0xb22b,\n  'blake2b-352': 0xb22c,\n  'blake2b-360': 0xb22d,\n  'blake2b-368': 0xb22e,\n  'blake2b-376': 0xb22f,\n  'blake2b-384': 0xb230,\n  'blake2b-392': 0xb231,\n  'blake2b-400': 0xb232,\n  'blake2b-408': 0xb233,\n  'blake2b-416': 0xb234,\n  'blake2b-424': 0xb235,\n  'blake2b-432': 0xb236,\n  'blake2b-440': 0xb237,\n  'blake2b-448': 0xb238,\n  'blake2b-456': 0xb239,\n  'blake2b-464': 0xb23a,\n  'blake2b-472': 0xb23b,\n  'blake2b-480': 0xb23c,\n  'blake2b-488': 0xb23d,\n  'blake2b-496': 0xb23e,\n  'blake2b-504': 0xb23f,\n  'blake2b-512': 0xb240,\n  'blake2s-8': 0xb241,\n  'blake2s-16': 0xb242,\n  'blake2s-24': 0xb243,\n  'blake2s-32': 0xb244,\n  'blake2s-40': 0xb245,\n  'blake2s-48': 0xb246,\n  'blake2s-56': 0xb247,\n  'blake2s-64': 0xb248,\n  'blake2s-72': 0xb249,\n  'blake2s-80': 0xb24a,\n  'blake2s-88': 0xb24b,\n  'blake2s-96': 0xb24c,\n  'blake2s-104': 0xb24d,\n  'blake2s-112': 0xb24e,\n  'blake2s-120': 0xb24f,\n  'blake2s-128': 0xb250,\n  'blake2s-136': 0xb251,\n  'blake2s-144': 0xb252,\n  'blake2s-152': 0xb253,\n  'blake2s-160': 0xb254,\n  'blake2s-168': 0xb255,\n  'blake2s-176': 0xb256,\n  'blake2s-184': 0xb257,\n  'blake2s-192': 0xb258,\n  'blake2s-200': 0xb259,\n  'blake2s-208': 0xb25a,\n  'blake2s-216': 0xb25b,\n  'blake2s-224': 0xb25c,\n  'blake2s-232': 0xb25d,\n  'blake2s-240': 0xb25e,\n  'blake2s-248': 0xb25f,\n  'blake2s-256': 0xb260,\n  'Skein256-8': 0xb301,\n  'Skein256-16': 0xb302,\n  'Skein256-24': 0xb303,\n  'Skein256-32': 0xb304,\n  'Skein256-40': 0xb305,\n  'Skein256-48': 0xb306,\n  'Skein256-56': 0xb307,\n  'Skein256-64': 0xb308,\n  'Skein256-72': 0xb309,\n  'Skein256-80': 0xb30a,\n  'Skein256-88': 0xb30b,\n  'Skein256-96': 0xb30c,\n  'Skein256-104': 0xb30d,\n  'Skein256-112': 0xb30e,\n  'Skein256-120': 0xb30f,\n  'Skein256-128': 0xb310,\n  'Skein256-136': 0xb311,\n  'Skein256-144': 0xb312,\n  'Skein256-152': 0xb313,\n  'Skein256-160': 0xb314,\n  'Skein256-168': 0xb315,\n  'Skein256-176': 0xb316,\n  'Skein256-184': 0xb317,\n  'Skein256-192': 0xb318,\n  'Skein256-200': 0xb319,\n  'Skein256-208': 0xb31a,\n  'Skein256-216': 0xb31b,\n  'Skein256-224': 0xb31c,\n  'Skein256-232': 0xb31d,\n  'Skein256-240': 0xb31e,\n  'Skein256-248': 0xb31f,\n  'Skein256-256': 0xb320,\n  'Skein512-8': 0xb321,\n  'Skein512-16': 0xb322,\n  'Skein512-24': 0xb323,\n  'Skein512-32': 0xb324,\n  'Skein512-40': 0xb325,\n  'Skein512-48': 0xb326,\n  'Skein512-56': 0xb327,\n  'Skein512-64': 0xb328,\n  'Skein512-72': 0xb329,\n  'Skein512-80': 0xb32a,\n  'Skein512-88': 0xb32b,\n  'Skein512-96': 0xb32c,\n  'Skein512-104': 0xb32d,\n  'Skein512-112': 0xb32e,\n  'Skein512-120': 0xb32f,\n  'Skein512-128': 0xb330,\n  'Skein512-136': 0xb331,\n  'Skein512-144': 0xb332,\n  'Skein512-152': 0xb333,\n  'Skein512-160': 0xb334,\n  'Skein512-168': 0xb335,\n  'Skein512-176': 0xb336,\n  'Skein512-184': 0xb337,\n  'Skein512-192': 0xb338,\n  'Skein512-200': 0xb339,\n  'Skein512-208': 0xb33a,\n  'Skein512-216': 0xb33b,\n  'Skein512-224': 0xb33c,\n  'Skein512-232': 0xb33d,\n  'Skein512-240': 0xb33e,\n  'Skein512-248': 0xb33f,\n  'Skein512-256': 0xb340,\n  'Skein512-264': 0xb341,\n  'Skein512-272': 0xb342,\n  'Skein512-280': 0xb343,\n  'Skein512-288': 0xb344,\n  'Skein512-296': 0xb345,\n  'Skein512-304': 0xb346,\n  'Skein512-312': 0xb347,\n  'Skein512-320': 0xb348,\n  'Skein512-328': 0xb349,\n  'Skein512-336': 0xb34a,\n  'Skein512-344': 0xb34b,\n  'Skein512-352': 0xb34c,\n  'Skein512-360': 0xb34d,\n  'Skein512-368': 0xb34e,\n  'Skein512-376': 0xb34f,\n  'Skein512-384': 0xb350,\n  'Skein512-392': 0xb351,\n  'Skein512-400': 0xb352,\n  'Skein512-408': 0xb353,\n  'Skein512-416': 0xb354,\n  'Skein512-424': 0xb355,\n  'Skein512-432': 0xb356,\n  'Skein512-440': 0xb357,\n  'Skein512-448': 0xb358,\n  'Skein512-456': 0xb359,\n  'Skein512-464': 0xb35a,\n  'Skein512-472': 0xb35b,\n  'Skein512-480': 0xb35c,\n  'Skein512-488': 0xb35d,\n  'Skein512-496': 0xb35e,\n  'Skein512-504': 0xb35f,\n  'Skein512-512': 0xb360,\n  'Skein1024-8': 0xb361,\n  'Skein1024-16': 0xb362,\n  'Skein1024-24': 0xb363,\n  'Skein1024-32': 0xb364,\n  'Skein1024-40': 0xb365,\n  'Skein1024-48': 0xb366,\n  'Skein1024-56': 0xb367,\n  'Skein1024-64': 0xb368,\n  'Skein1024-72': 0xb369,\n  'Skein1024-80': 0xb36a,\n  'Skein1024-88': 0xb36b,\n  'Skein1024-96': 0xb36c,\n  'Skein1024-104': 0xb36d,\n  'Skein1024-112': 0xb36e,\n  'Skein1024-120': 0xb36f,\n  'Skein1024-128': 0xb370,\n  'Skein1024-136': 0xb371,\n  'Skein1024-144': 0xb372,\n  'Skein1024-152': 0xb373,\n  'Skein1024-160': 0xb374,\n  'Skein1024-168': 0xb375,\n  'Skein1024-176': 0xb376,\n  'Skein1024-184': 0xb377,\n  'Skein1024-192': 0xb378,\n  'Skein1024-200': 0xb379,\n  'Skein1024-208': 0xb37a,\n  'Skein1024-216': 0xb37b,\n  'Skein1024-224': 0xb37c,\n  'Skein1024-232': 0xb37d,\n  'Skein1024-240': 0xb37e,\n  'Skein1024-248': 0xb37f,\n  'Skein1024-256': 0xb380,\n  'Skein1024-264': 0xb381,\n  'Skein1024-272': 0xb382,\n  'Skein1024-280': 0xb383,\n  'Skein1024-288': 0xb384,\n  'Skein1024-296': 0xb385,\n  'Skein1024-304': 0xb386,\n  'Skein1024-312': 0xb387,\n  'Skein1024-320': 0xb388,\n  'Skein1024-328': 0xb389,\n  'Skein1024-336': 0xb38a,\n  'Skein1024-344': 0xb38b,\n  'Skein1024-352': 0xb38c,\n  'Skein1024-360': 0xb38d,\n  'Skein1024-368': 0xb38e,\n  'Skein1024-376': 0xb38f,\n  'Skein1024-384': 0xb390,\n  'Skein1024-392': 0xb391,\n  'Skein1024-400': 0xb392,\n  'Skein1024-408': 0xb393,\n  'Skein1024-416': 0xb394,\n  'Skein1024-424': 0xb395,\n  'Skein1024-432': 0xb396,\n  'Skein1024-440': 0xb397,\n  'Skein1024-448': 0xb398,\n  'Skein1024-456': 0xb399,\n  'Skein1024-464': 0xb39a,\n  'Skein1024-472': 0xb39b,\n  'Skein1024-480': 0xb39c,\n  'Skein1024-488': 0xb39d,\n  'Skein1024-496': 0xb39e,\n  'Skein1024-504': 0xb39f,\n  'Skein1024-512': 0xb3a0,\n  'Skein1024-520': 0xb3a1,\n  'Skein1024-528': 0xb3a2,\n  'Skein1024-536': 0xb3a3,\n  'Skein1024-544': 0xb3a4,\n  'Skein1024-552': 0xb3a5,\n  'Skein1024-560': 0xb3a6,\n  'Skein1024-568': 0xb3a7,\n  'Skein1024-576': 0xb3a8,\n  'Skein1024-584': 0xb3a9,\n  'Skein1024-592': 0xb3aa,\n  'Skein1024-600': 0xb3ab,\n  'Skein1024-608': 0xb3ac,\n  'Skein1024-616': 0xb3ad,\n  'Skein1024-624': 0xb3ae,\n  'Skein1024-632': 0xb3af,\n  'Skein1024-640': 0xb3b0,\n  'Skein1024-648': 0xb3b1,\n  'Skein1024-656': 0xb3b2,\n  'Skein1024-664': 0xb3b3,\n  'Skein1024-672': 0xb3b4,\n  'Skein1024-680': 0xb3b5,\n  'Skein1024-688': 0xb3b6,\n  'Skein1024-696': 0xb3b7,\n  'Skein1024-704': 0xb3b8,\n  'Skein1024-712': 0xb3b9,\n  'Skein1024-720': 0xb3ba,\n  'Skein1024-728': 0xb3bb,\n  'Skein1024-736': 0xb3bc,\n  'Skein1024-744': 0xb3bd,\n  'Skein1024-752': 0xb3be,\n  'Skein1024-760': 0xb3bf,\n  'Skein1024-768': 0xb3c0,\n  'Skein1024-776': 0xb3c1,\n  'Skein1024-784': 0xb3c2,\n  'Skein1024-792': 0xb3c3,\n  'Skein1024-800': 0xb3c4,\n  'Skein1024-808': 0xb3c5,\n  'Skein1024-816': 0xb3c6,\n  'Skein1024-824': 0xb3c7,\n  'Skein1024-832': 0xb3c8,\n  'Skein1024-840': 0xb3c9,\n  'Skein1024-848': 0xb3ca,\n  'Skein1024-856': 0xb3cb,\n  'Skein1024-864': 0xb3cc,\n  'Skein1024-872': 0xb3cd,\n  'Skein1024-880': 0xb3ce,\n  'Skein1024-888': 0xb3cf,\n  'Skein1024-896': 0xb3d0,\n  'Skein1024-904': 0xb3d1,\n  'Skein1024-912': 0xb3d2,\n  'Skein1024-920': 0xb3d3,\n  'Skein1024-928': 0xb3d4,\n  'Skein1024-936': 0xb3d5,\n  'Skein1024-944': 0xb3d6,\n  'Skein1024-952': 0xb3d7,\n  'Skein1024-960': 0xb3d8,\n  'Skein1024-968': 0xb3d9,\n  'Skein1024-976': 0xb3da,\n  'Skein1024-984': 0xb3db,\n  'Skein1024-992': 0xb3dc,\n  'Skein1024-1000': 0xb3dd,\n  'Skein1024-1008': 0xb3de,\n  'Skein1024-1016': 0xb3df,\n  'Skein1024-1024': 0xb3e0\n});\nexports.codes = Object.freeze({\n  0x0: 'identity',\n  // sha family\n  0x11: 'sha1',\n  0x12: 'sha2-256',\n  0x13: 'sha2-512',\n  0x56: 'dbl-sha2-256',\n  0x17: 'sha3-224',\n  0x16: 'sha3-256',\n  0x15: 'sha3-384',\n  0x14: 'sha3-512',\n  0x18: 'shake-128',\n  0x19: 'shake-256',\n  0x1A: 'keccak-224',\n  0x1B: 'keccak-256',\n  0x1C: 'keccak-384',\n  0x1D: 'keccak-512',\n  0x22: 'murmur3-128',\n  0x23: 'murmur3-32',\n  // blake2\n  0xb201: 'blake2b-8',\n  0xb202: 'blake2b-16',\n  0xb203: 'blake2b-24',\n  0xb204: 'blake2b-32',\n  0xb205: 'blake2b-40',\n  0xb206: 'blake2b-48',\n  0xb207: 'blake2b-56',\n  0xb208: 'blake2b-64',\n  0xb209: 'blake2b-72',\n  0xb20a: 'blake2b-80',\n  0xb20b: 'blake2b-88',\n  0xb20c: 'blake2b-96',\n  0xb20d: 'blake2b-104',\n  0xb20e: 'blake2b-112',\n  0xb20f: 'blake2b-120',\n  0xb210: 'blake2b-128',\n  0xb211: 'blake2b-136',\n  0xb212: 'blake2b-144',\n  0xb213: 'blake2b-152',\n  0xb214: 'blake2b-160',\n  0xb215: 'blake2b-168',\n  0xb216: 'blake2b-176',\n  0xb217: 'blake2b-184',\n  0xb218: 'blake2b-192',\n  0xb219: 'blake2b-200',\n  0xb21a: 'blake2b-208',\n  0xb21b: 'blake2b-216',\n  0xb21c: 'blake2b-224',\n  0xb21d: 'blake2b-232',\n  0xb21e: 'blake2b-240',\n  0xb21f: 'blake2b-248',\n  0xb220: 'blake2b-256',\n  0xb221: 'blake2b-264',\n  0xb222: 'blake2b-272',\n  0xb223: 'blake2b-280',\n  0xb224: 'blake2b-288',\n  0xb225: 'blake2b-296',\n  0xb226: 'blake2b-304',\n  0xb227: 'blake2b-312',\n  0xb228: 'blake2b-320',\n  0xb229: 'blake2b-328',\n  0xb22a: 'blake2b-336',\n  0xb22b: 'blake2b-344',\n  0xb22c: 'blake2b-352',\n  0xb22d: 'blake2b-360',\n  0xb22e: 'blake2b-368',\n  0xb22f: 'blake2b-376',\n  0xb230: 'blake2b-384',\n  0xb231: 'blake2b-392',\n  0xb232: 'blake2b-400',\n  0xb233: 'blake2b-408',\n  0xb234: 'blake2b-416',\n  0xb235: 'blake2b-424',\n  0xb236: 'blake2b-432',\n  0xb237: 'blake2b-440',\n  0xb238: 'blake2b-448',\n  0xb239: 'blake2b-456',\n  0xb23a: 'blake2b-464',\n  0xb23b: 'blake2b-472',\n  0xb23c: 'blake2b-480',\n  0xb23d: 'blake2b-488',\n  0xb23e: 'blake2b-496',\n  0xb23f: 'blake2b-504',\n  0xb240: 'blake2b-512',\n  0xb241: 'blake2s-8',\n  0xb242: 'blake2s-16',\n  0xb243: 'blake2s-24',\n  0xb244: 'blake2s-32',\n  0xb245: 'blake2s-40',\n  0xb246: 'blake2s-48',\n  0xb247: 'blake2s-56',\n  0xb248: 'blake2s-64',\n  0xb249: 'blake2s-72',\n  0xb24a: 'blake2s-80',\n  0xb24b: 'blake2s-88',\n  0xb24c: 'blake2s-96',\n  0xb24d: 'blake2s-104',\n  0xb24e: 'blake2s-112',\n  0xb24f: 'blake2s-120',\n  0xb250: 'blake2s-128',\n  0xb251: 'blake2s-136',\n  0xb252: 'blake2s-144',\n  0xb253: 'blake2s-152',\n  0xb254: 'blake2s-160',\n  0xb255: 'blake2s-168',\n  0xb256: 'blake2s-176',\n  0xb257: 'blake2s-184',\n  0xb258: 'blake2s-192',\n  0xb259: 'blake2s-200',\n  0xb25a: 'blake2s-208',\n  0xb25b: 'blake2s-216',\n  0xb25c: 'blake2s-224',\n  0xb25d: 'blake2s-232',\n  0xb25e: 'blake2s-240',\n  0xb25f: 'blake2s-248',\n  0xb260: 'blake2s-256',\n  // skein\n  0xb301: 'Skein256-8',\n  0xb302: 'Skein256-16',\n  0xb303: 'Skein256-24',\n  0xb304: 'Skein256-32',\n  0xb305: 'Skein256-40',\n  0xb306: 'Skein256-48',\n  0xb307: 'Skein256-56',\n  0xb308: 'Skein256-64',\n  0xb309: 'Skein256-72',\n  0xb30a: 'Skein256-80',\n  0xb30b: 'Skein256-88',\n  0xb30c: 'Skein256-96',\n  0xb30d: 'Skein256-104',\n  0xb30e: 'Skein256-112',\n  0xb30f: 'Skein256-120',\n  0xb310: 'Skein256-128',\n  0xb311: 'Skein256-136',\n  0xb312: 'Skein256-144',\n  0xb313: 'Skein256-152',\n  0xb314: 'Skein256-160',\n  0xb315: 'Skein256-168',\n  0xb316: 'Skein256-176',\n  0xb317: 'Skein256-184',\n  0xb318: 'Skein256-192',\n  0xb319: 'Skein256-200',\n  0xb31a: 'Skein256-208',\n  0xb31b: 'Skein256-216',\n  0xb31c: 'Skein256-224',\n  0xb31d: 'Skein256-232',\n  0xb31e: 'Skein256-240',\n  0xb31f: 'Skein256-248',\n  0xb320: 'Skein256-256',\n  0xb321: 'Skein512-8',\n  0xb322: 'Skein512-16',\n  0xb323: 'Skein512-24',\n  0xb324: 'Skein512-32',\n  0xb325: 'Skein512-40',\n  0xb326: 'Skein512-48',\n  0xb327: 'Skein512-56',\n  0xb328: 'Skein512-64',\n  0xb329: 'Skein512-72',\n  0xb32a: 'Skein512-80',\n  0xb32b: 'Skein512-88',\n  0xb32c: 'Skein512-96',\n  0xb32d: 'Skein512-104',\n  0xb32e: 'Skein512-112',\n  0xb32f: 'Skein512-120',\n  0xb330: 'Skein512-128',\n  0xb331: 'Skein512-136',\n  0xb332: 'Skein512-144',\n  0xb333: 'Skein512-152',\n  0xb334: 'Skein512-160',\n  0xb335: 'Skein512-168',\n  0xb336: 'Skein512-176',\n  0xb337: 'Skein512-184',\n  0xb338: 'Skein512-192',\n  0xb339: 'Skein512-200',\n  0xb33a: 'Skein512-208',\n  0xb33b: 'Skein512-216',\n  0xb33c: 'Skein512-224',\n  0xb33d: 'Skein512-232',\n  0xb33e: 'Skein512-240',\n  0xb33f: 'Skein512-248',\n  0xb340: 'Skein512-256',\n  0xb341: 'Skein512-264',\n  0xb342: 'Skein512-272',\n  0xb343: 'Skein512-280',\n  0xb344: 'Skein512-288',\n  0xb345: 'Skein512-296',\n  0xb346: 'Skein512-304',\n  0xb347: 'Skein512-312',\n  0xb348: 'Skein512-320',\n  0xb349: 'Skein512-328',\n  0xb34a: 'Skein512-336',\n  0xb34b: 'Skein512-344',\n  0xb34c: 'Skein512-352',\n  0xb34d: 'Skein512-360',\n  0xb34e: 'Skein512-368',\n  0xb34f: 'Skein512-376',\n  0xb350: 'Skein512-384',\n  0xb351: 'Skein512-392',\n  0xb352: 'Skein512-400',\n  0xb353: 'Skein512-408',\n  0xb354: 'Skein512-416',\n  0xb355: 'Skein512-424',\n  0xb356: 'Skein512-432',\n  0xb357: 'Skein512-440',\n  0xb358: 'Skein512-448',\n  0xb359: 'Skein512-456',\n  0xb35a: 'Skein512-464',\n  0xb35b: 'Skein512-472',\n  0xb35c: 'Skein512-480',\n  0xb35d: 'Skein512-488',\n  0xb35e: 'Skein512-496',\n  0xb35f: 'Skein512-504',\n  0xb360: 'Skein512-512',\n  0xb361: 'Skein1024-8',\n  0xb362: 'Skein1024-16',\n  0xb363: 'Skein1024-24',\n  0xb364: 'Skein1024-32',\n  0xb365: 'Skein1024-40',\n  0xb366: 'Skein1024-48',\n  0xb367: 'Skein1024-56',\n  0xb368: 'Skein1024-64',\n  0xb369: 'Skein1024-72',\n  0xb36a: 'Skein1024-80',\n  0xb36b: 'Skein1024-88',\n  0xb36c: 'Skein1024-96',\n  0xb36d: 'Skein1024-104',\n  0xb36e: 'Skein1024-112',\n  0xb36f: 'Skein1024-120',\n  0xb370: 'Skein1024-128',\n  0xb371: 'Skein1024-136',\n  0xb372: 'Skein1024-144',\n  0xb373: 'Skein1024-152',\n  0xb374: 'Skein1024-160',\n  0xb375: 'Skein1024-168',\n  0xb376: 'Skein1024-176',\n  0xb377: 'Skein1024-184',\n  0xb378: 'Skein1024-192',\n  0xb379: 'Skein1024-200',\n  0xb37a: 'Skein1024-208',\n  0xb37b: 'Skein1024-216',\n  0xb37c: 'Skein1024-224',\n  0xb37d: 'Skein1024-232',\n  0xb37e: 'Skein1024-240',\n  0xb37f: 'Skein1024-248',\n  0xb380: 'Skein1024-256',\n  0xb381: 'Skein1024-264',\n  0xb382: 'Skein1024-272',\n  0xb383: 'Skein1024-280',\n  0xb384: 'Skein1024-288',\n  0xb385: 'Skein1024-296',\n  0xb386: 'Skein1024-304',\n  0xb387: 'Skein1024-312',\n  0xb388: 'Skein1024-320',\n  0xb389: 'Skein1024-328',\n  0xb38a: 'Skein1024-336',\n  0xb38b: 'Skein1024-344',\n  0xb38c: 'Skein1024-352',\n  0xb38d: 'Skein1024-360',\n  0xb38e: 'Skein1024-368',\n  0xb38f: 'Skein1024-376',\n  0xb390: 'Skein1024-384',\n  0xb391: 'Skein1024-392',\n  0xb392: 'Skein1024-400',\n  0xb393: 'Skein1024-408',\n  0xb394: 'Skein1024-416',\n  0xb395: 'Skein1024-424',\n  0xb396: 'Skein1024-432',\n  0xb397: 'Skein1024-440',\n  0xb398: 'Skein1024-448',\n  0xb399: 'Skein1024-456',\n  0xb39a: 'Skein1024-464',\n  0xb39b: 'Skein1024-472',\n  0xb39c: 'Skein1024-480',\n  0xb39d: 'Skein1024-488',\n  0xb39e: 'Skein1024-496',\n  0xb39f: 'Skein1024-504',\n  0xb3a0: 'Skein1024-512',\n  0xb3a1: 'Skein1024-520',\n  0xb3a2: 'Skein1024-528',\n  0xb3a3: 'Skein1024-536',\n  0xb3a4: 'Skein1024-544',\n  0xb3a5: 'Skein1024-552',\n  0xb3a6: 'Skein1024-560',\n  0xb3a7: 'Skein1024-568',\n  0xb3a8: 'Skein1024-576',\n  0xb3a9: 'Skein1024-584',\n  0xb3aa: 'Skein1024-592',\n  0xb3ab: 'Skein1024-600',\n  0xb3ac: 'Skein1024-608',\n  0xb3ad: 'Skein1024-616',\n  0xb3ae: 'Skein1024-624',\n  0xb3af: 'Skein1024-632',\n  0xb3b0: 'Skein1024-640',\n  0xb3b1: 'Skein1024-648',\n  0xb3b2: 'Skein1024-656',\n  0xb3b3: 'Skein1024-664',\n  0xb3b4: 'Skein1024-672',\n  0xb3b5: 'Skein1024-680',\n  0xb3b6: 'Skein1024-688',\n  0xb3b7: 'Skein1024-696',\n  0xb3b8: 'Skein1024-704',\n  0xb3b9: 'Skein1024-712',\n  0xb3ba: 'Skein1024-720',\n  0xb3bb: 'Skein1024-728',\n  0xb3bc: 'Skein1024-736',\n  0xb3bd: 'Skein1024-744',\n  0xb3be: 'Skein1024-752',\n  0xb3bf: 'Skein1024-760',\n  0xb3c0: 'Skein1024-768',\n  0xb3c1: 'Skein1024-776',\n  0xb3c2: 'Skein1024-784',\n  0xb3c3: 'Skein1024-792',\n  0xb3c4: 'Skein1024-800',\n  0xb3c5: 'Skein1024-808',\n  0xb3c6: 'Skein1024-816',\n  0xb3c7: 'Skein1024-824',\n  0xb3c8: 'Skein1024-832',\n  0xb3c9: 'Skein1024-840',\n  0xb3ca: 'Skein1024-848',\n  0xb3cb: 'Skein1024-856',\n  0xb3cc: 'Skein1024-864',\n  0xb3cd: 'Skein1024-872',\n  0xb3ce: 'Skein1024-880',\n  0xb3cf: 'Skein1024-888',\n  0xb3d0: 'Skein1024-896',\n  0xb3d1: 'Skein1024-904',\n  0xb3d2: 'Skein1024-912',\n  0xb3d3: 'Skein1024-920',\n  0xb3d4: 'Skein1024-928',\n  0xb3d5: 'Skein1024-936',\n  0xb3d6: 'Skein1024-944',\n  0xb3d7: 'Skein1024-952',\n  0xb3d8: 'Skein1024-960',\n  0xb3d9: 'Skein1024-968',\n  0xb3da: 'Skein1024-976',\n  0xb3db: 'Skein1024-984',\n  0xb3dc: 'Skein1024-992',\n  0xb3dd: 'Skein1024-1000',\n  0xb3de: 'Skein1024-1008',\n  0xb3df: 'Skein1024-1016',\n  0xb3e0: 'Skein1024-1024'\n});\nexports.defaultLengths = Object.freeze({\n  0x11: 20,\n  0x12: 32,\n  0x13: 64,\n  0x56: 32,\n  0x17: 28,\n  0x16: 32,\n  0x15: 48,\n  0x14: 64,\n  0x18: 32,\n  0x19: 64,\n  0x1A: 28,\n  0x1B: 32,\n  0x1C: 48,\n  0x1D: 64,\n  0x22: 32,\n  0xb201: 0x01,\n  0xb202: 0x02,\n  0xb203: 0x03,\n  0xb204: 0x04,\n  0xb205: 0x05,\n  0xb206: 0x06,\n  0xb207: 0x07,\n  0xb208: 0x08,\n  0xb209: 0x09,\n  0xb20a: 0x0a,\n  0xb20b: 0x0b,\n  0xb20c: 0x0c,\n  0xb20d: 0x0d,\n  0xb20e: 0x0e,\n  0xb20f: 0x0f,\n  0xb210: 0x10,\n  0xb211: 0x11,\n  0xb212: 0x12,\n  0xb213: 0x13,\n  0xb214: 0x14,\n  0xb215: 0x15,\n  0xb216: 0x16,\n  0xb217: 0x17,\n  0xb218: 0x18,\n  0xb219: 0x19,\n  0xb21a: 0x1a,\n  0xb21b: 0x1b,\n  0xb21c: 0x1c,\n  0xb21d: 0x1d,\n  0xb21e: 0x1e,\n  0xb21f: 0x1f,\n  0xb220: 0x20,\n  0xb221: 0x21,\n  0xb222: 0x22,\n  0xb223: 0x23,\n  0xb224: 0x24,\n  0xb225: 0x25,\n  0xb226: 0x26,\n  0xb227: 0x27,\n  0xb228: 0x28,\n  0xb229: 0x29,\n  0xb22a: 0x2a,\n  0xb22b: 0x2b,\n  0xb22c: 0x2c,\n  0xb22d: 0x2d,\n  0xb22e: 0x2e,\n  0xb22f: 0x2f,\n  0xb230: 0x30,\n  0xb231: 0x31,\n  0xb232: 0x32,\n  0xb233: 0x33,\n  0xb234: 0x34,\n  0xb235: 0x35,\n  0xb236: 0x36,\n  0xb237: 0x37,\n  0xb238: 0x38,\n  0xb239: 0x39,\n  0xb23a: 0x3a,\n  0xb23b: 0x3b,\n  0xb23c: 0x3c,\n  0xb23d: 0x3d,\n  0xb23e: 0x3e,\n  0xb23f: 0x3f,\n  0xb240: 0x40,\n  0xb241: 0x01,\n  0xb242: 0x02,\n  0xb243: 0x03,\n  0xb244: 0x04,\n  0xb245: 0x05,\n  0xb246: 0x06,\n  0xb247: 0x07,\n  0xb248: 0x08,\n  0xb249: 0x09,\n  0xb24a: 0x0a,\n  0xb24b: 0x0b,\n  0xb24c: 0x0c,\n  0xb24d: 0x0d,\n  0xb24e: 0x0e,\n  0xb24f: 0x0f,\n  0xb250: 0x10,\n  0xb251: 0x11,\n  0xb252: 0x12,\n  0xb253: 0x13,\n  0xb254: 0x14,\n  0xb255: 0x15,\n  0xb256: 0x16,\n  0xb257: 0x17,\n  0xb258: 0x18,\n  0xb259: 0x19,\n  0xb25a: 0x1a,\n  0xb25b: 0x1b,\n  0xb25c: 0x1c,\n  0xb25d: 0x1d,\n  0xb25e: 0x1e,\n  0xb25f: 0x1f,\n  0xb260: 0x20,\n  0xb301: 0x01,\n  0xb302: 0x02,\n  0xb303: 0x03,\n  0xb304: 0x04,\n  0xb305: 0x05,\n  0xb306: 0x06,\n  0xb307: 0x07,\n  0xb308: 0x08,\n  0xb309: 0x09,\n  0xb30a: 0x0a,\n  0xb30b: 0x0b,\n  0xb30c: 0x0c,\n  0xb30d: 0x0d,\n  0xb30e: 0x0e,\n  0xb30f: 0x0f,\n  0xb310: 0x10,\n  0xb311: 0x11,\n  0xb312: 0x12,\n  0xb313: 0x13,\n  0xb314: 0x14,\n  0xb315: 0x15,\n  0xb316: 0x16,\n  0xb317: 0x17,\n  0xb318: 0x18,\n  0xb319: 0x19,\n  0xb31a: 0x1a,\n  0xb31b: 0x1b,\n  0xb31c: 0x1c,\n  0xb31d: 0x1d,\n  0xb31e: 0x1e,\n  0xb31f: 0x1f,\n  0xb320: 0x20,\n  0xb321: 0x01,\n  0xb322: 0x02,\n  0xb323: 0x03,\n  0xb324: 0x04,\n  0xb325: 0x05,\n  0xb326: 0x06,\n  0xb327: 0x07,\n  0xb328: 0x08,\n  0xb329: 0x09,\n  0xb32a: 0x0a,\n  0xb32b: 0x0b,\n  0xb32c: 0x0c,\n  0xb32d: 0x0d,\n  0xb32e: 0x0e,\n  0xb32f: 0x0f,\n  0xb330: 0x10,\n  0xb331: 0x11,\n  0xb332: 0x12,\n  0xb333: 0x13,\n  0xb334: 0x14,\n  0xb335: 0x15,\n  0xb336: 0x16,\n  0xb337: 0x17,\n  0xb338: 0x18,\n  0xb339: 0x19,\n  0xb33a: 0x1a,\n  0xb33b: 0x1b,\n  0xb33c: 0x1c,\n  0xb33d: 0x1d,\n  0xb33e: 0x1e,\n  0xb33f: 0x1f,\n  0xb340: 0x20,\n  0xb341: 0x21,\n  0xb342: 0x22,\n  0xb343: 0x23,\n  0xb344: 0x24,\n  0xb345: 0x25,\n  0xb346: 0x26,\n  0xb347: 0x27,\n  0xb348: 0x28,\n  0xb349: 0x29,\n  0xb34a: 0x2a,\n  0xb34b: 0x2b,\n  0xb34c: 0x2c,\n  0xb34d: 0x2d,\n  0xb34e: 0x2e,\n  0xb34f: 0x2f,\n  0xb350: 0x30,\n  0xb351: 0x31,\n  0xb352: 0x32,\n  0xb353: 0x33,\n  0xb354: 0x34,\n  0xb355: 0x35,\n  0xb356: 0x36,\n  0xb357: 0x37,\n  0xb358: 0x38,\n  0xb359: 0x39,\n  0xb35a: 0x3a,\n  0xb35b: 0x3b,\n  0xb35c: 0x3c,\n  0xb35d: 0x3d,\n  0xb35e: 0x3e,\n  0xb35f: 0x3f,\n  0xb360: 0x40,\n  0xb361: 0x01,\n  0xb362: 0x02,\n  0xb363: 0x03,\n  0xb364: 0x04,\n  0xb365: 0x05,\n  0xb366: 0x06,\n  0xb367: 0x07,\n  0xb368: 0x08,\n  0xb369: 0x09,\n  0xb36a: 0x0a,\n  0xb36b: 0x0b,\n  0xb36c: 0x0c,\n  0xb36d: 0x0d,\n  0xb36e: 0x0e,\n  0xb36f: 0x0f,\n  0xb370: 0x10,\n  0xb371: 0x11,\n  0xb372: 0x12,\n  0xb373: 0x13,\n  0xb374: 0x14,\n  0xb375: 0x15,\n  0xb376: 0x16,\n  0xb377: 0x17,\n  0xb378: 0x18,\n  0xb379: 0x19,\n  0xb37a: 0x1a,\n  0xb37b: 0x1b,\n  0xb37c: 0x1c,\n  0xb37d: 0x1d,\n  0xb37e: 0x1e,\n  0xb37f: 0x1f,\n  0xb380: 0x20,\n  0xb381: 0x21,\n  0xb382: 0x22,\n  0xb383: 0x23,\n  0xb384: 0x24,\n  0xb385: 0x25,\n  0xb386: 0x26,\n  0xb387: 0x27,\n  0xb388: 0x28,\n  0xb389: 0x29,\n  0xb38a: 0x2a,\n  0xb38b: 0x2b,\n  0xb38c: 0x2c,\n  0xb38d: 0x2d,\n  0xb38e: 0x2e,\n  0xb38f: 0x2f,\n  0xb390: 0x30,\n  0xb391: 0x31,\n  0xb392: 0x32,\n  0xb393: 0x33,\n  0xb394: 0x34,\n  0xb395: 0x35,\n  0xb396: 0x36,\n  0xb397: 0x37,\n  0xb398: 0x38,\n  0xb399: 0x39,\n  0xb39a: 0x3a,\n  0xb39b: 0x3b,\n  0xb39c: 0x3c,\n  0xb39d: 0x3d,\n  0xb39e: 0x3e,\n  0xb39f: 0x3f,\n  0xb3a0: 0x40,\n  0xb3a1: 0x41,\n  0xb3a2: 0x42,\n  0xb3a3: 0x43,\n  0xb3a4: 0x44,\n  0xb3a5: 0x45,\n  0xb3a6: 0x46,\n  0xb3a7: 0x47,\n  0xb3a8: 0x48,\n  0xb3a9: 0x49,\n  0xb3aa: 0x4a,\n  0xb3ab: 0x4b,\n  0xb3ac: 0x4c,\n  0xb3ad: 0x4d,\n  0xb3ae: 0x4e,\n  0xb3af: 0x4f,\n  0xb3b0: 0x50,\n  0xb3b1: 0x51,\n  0xb3b2: 0x52,\n  0xb3b3: 0x53,\n  0xb3b4: 0x54,\n  0xb3b5: 0x55,\n  0xb3b6: 0x56,\n  0xb3b7: 0x57,\n  0xb3b8: 0x58,\n  0xb3b9: 0x59,\n  0xb3ba: 0x5a,\n  0xb3bb: 0x5b,\n  0xb3bc: 0x5c,\n  0xb3bd: 0x5d,\n  0xb3be: 0x5e,\n  0xb3bf: 0x5f,\n  0xb3c0: 0x60,\n  0xb3c1: 0x61,\n  0xb3c2: 0x62,\n  0xb3c3: 0x63,\n  0xb3c4: 0x64,\n  0xb3c5: 0x65,\n  0xb3c6: 0x66,\n  0xb3c7: 0x67,\n  0xb3c8: 0x68,\n  0xb3c9: 0x69,\n  0xb3ca: 0x6a,\n  0xb3cb: 0x6b,\n  0xb3cc: 0x6c,\n  0xb3cd: 0x6d,\n  0xb3ce: 0x6e,\n  0xb3cf: 0x6f,\n  0xb3d0: 0x70,\n  0xb3d1: 0x71,\n  0xb3d2: 0x72,\n  0xb3d3: 0x73,\n  0xb3d4: 0x74,\n  0xb3d5: 0x75,\n  0xb3d6: 0x76,\n  0xb3d7: 0x77,\n  0xb3d8: 0x78,\n  0xb3d9: 0x79,\n  0xb3da: 0x7a,\n  0xb3db: 0x7b,\n  0xb3dc: 0x7c,\n  0xb3dd: 0x7d,\n  0xb3de: 0x7e,\n  0xb3df: 0x7f,\n  0xb3e0: 0x80\n});", "\"use strict\";\n\nmodule.exports = encode;\nvar MSB = 0x80,\n    REST = 0x7F,\n    MSBALL = ~REST,\n    INT = Math.pow(2, 31);\n\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n\n  while (num >= INT) {\n    out[offset++] = num & 0xFF | MSB;\n    num /= 128;\n  }\n\n  while (num & MSBALL) {\n    out[offset++] = num & 0xFF | MSB;\n    num >>>= 7;\n  }\n\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}", "\"use strict\";\n\nmodule.exports = read;\nvar MSB = 0x80,\n    REST = 0x7F;\n\nfunction read(buf, offset) {\n  var res = 0,\n      offset = offset || 0,\n      shift = 0,\n      counter = offset,\n      b,\n      l = buf.length;\n\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST) << shift : (b & REST) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB);\n\n  read.bytes = counter - offset;\n  return res;\n}", "\"use strict\";\n\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\n\nmodule.exports = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};", "/**\n * Implementation of the [multibase](https://github.com/multiformats/multibase) specification.\n * @module Multibase\n */\n'use strict';\n\nconst constants = require('./constants');\n\nexports = module.exports = multibase;\nexports.encode = encode;\nexports.decode = decode;\nexports.isEncoded = isEncoded;\nexports.names = Object.freeze(Object.keys(constants.names));\nexports.codes = Object.freeze(Object.keys(constants.codes));\nconst errNotSupported = new Error('Unsupported encoding');\n/**\n * Create a new buffer with the multibase varint+code.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be prefixed with multibase.\n * @memberof Multibase\n * @returns {Buffer}\n */\n\nfunction multibase(nameOrCode, buf) {\n  if (!buf) {\n    throw new Error('requires an encoded buffer');\n  }\n\n  const base = getBase(nameOrCode);\n  const codeBuf = Buffer.from(base.code);\n  const name = base.name;\n  validEncode(name, buf);\n  return Buffer.concat([codeBuf, buf]);\n}\n/**\n * Encode data with the specified base and add the multibase prefix.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be encoded.\n * @returns {Buffer}\n * @memberof Multibase\n */\n\n\nfunction encode(nameOrCode, buf) {\n  const base = getBase(nameOrCode);\n  const name = base.name;\n  return multibase(name, Buffer.from(base.encode(buf)));\n}\n/**\n * Takes a buffer or string encoded with multibase header, decodes it and\n * returns the decoded buffer\n *\n * @param {Buffer|string} bufOrString\n * @returns {Buffer}\n * @memberof Multibase\n *\n */\n\n\nfunction decode(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  }\n\n  const code = bufOrString.substring(0, 1);\n  bufOrString = bufOrString.substring(1, bufOrString.length);\n\n  if (typeof bufOrString === 'string') {\n    bufOrString = Buffer.from(bufOrString);\n  }\n\n  const base = getBase(code);\n  return Buffer.from(base.decode(bufOrString.toString()));\n}\n/**\n * Is the given data multibase encoded?\n *\n * @param {Buffer|string} bufOrString\n * @returns {boolean}\n * @memberof Multibase\n */\n\n\nfunction isEncoded(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  } // Ensure bufOrString is a string\n\n\n  if (Object.prototype.toString.call(bufOrString) !== '[object String]') {\n    return false;\n  }\n\n  const code = bufOrString.substring(0, 1);\n\n  try {\n    const base = getBase(code);\n    return base.name;\n  } catch (err) {\n    return false;\n  }\n}\n/**\n * @param {string} name\n * @param {Buffer} buf\n * @private\n * @returns {undefined}\n */\n\n\nfunction validEncode(name, buf) {\n  const base = getBase(name);\n  base.decode(buf.toString());\n}\n\nfunction getBase(nameOrCode) {\n  let base;\n\n  if (constants.names[nameOrCode]) {\n    base = constants.names[nameOrCode];\n  } else if (constants.codes[nameOrCode]) {\n    base = constants.codes[nameOrCode];\n  } else {\n    throw errNotSupported;\n  }\n\n  if (!base.isImplemented()) {\n    throw new Error('Base ' + nameOrCode + ' is not implemented yet');\n  }\n\n  return base;\n}", "'use strict';\n\nconst Base = require('./base.js');\n\nconst baseX = require('base-x');\n\nconst base16 = require('./base16');\n\nconst base32 = require('./base32');\n\nconst base64 = require('./base64'); // name, code, implementation, alphabet\n\n\nconst constants = [['base1', '1', '', '1'], ['base2', '0', baseX, '01'], ['base8', '7', baseX, '01234567'], ['base10', '9', baseX, '0123456789'], ['base16', 'f', base16, '0123456789abcdef'], ['base32', 'b', base32, 'abcdefghijklmnopqrstuvwxyz234567'], ['base32pad', 'c', base32, 'abcdefghijklmnopqrstuvwxyz234567='], ['base32hex', 'v', base32, '0123456789abcdefghijklmnopqrstuv'], ['base32hexpad', 't', base32, '0123456789abcdefghijklmnopqrstuv='], ['base32z', 'h', base32, 'ybndrfg8ejkmcpqxot1uwisza345h769'], ['base58flickr', 'Z', baseX, '**********************************************************'], ['base58btc', 'z', baseX, '**********************************************************'], ['base64', 'm', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'], ['base64pad', 'M', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='], ['base64url', 'u', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'], ['base64urlpad', 'U', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=']];\nconst names = constants.reduce((prev, tupple) => {\n  prev[tupple[0]] = new Base(tupple[0], tupple[1], tupple[2], tupple[3]);\n  return prev;\n}, {});\nconst codes = constants.reduce((prev, tupple) => {\n  prev[tupple[1]] = names[tupple[0]];\n  return prev;\n}, {});\nmodule.exports = {\n  names: names,\n  codes: codes\n};", "'use strict';\n\nclass Base {\n  constructor(name, code, implementation, alphabet) {\n    this.name = name;\n    this.code = code;\n    this.alphabet = alphabet;\n\n    if (implementation && alphabet) {\n      this.engine = implementation(alphabet);\n    }\n  }\n\n  encode(stringOrBuffer) {\n    return this.engine.encode(stringOrBuffer);\n  }\n\n  decode(stringOrBuffer) {\n    return this.engine.decode(stringOrBuffer);\n  }\n\n  isImplemented() {\n    return this.engine;\n  }\n\n}\n\nmodule.exports = Base;", "'use strict';\n\nmodule.exports = function base16(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return Buffer.from(input).toString('hex');\n      }\n\n      return input.toString('hex');\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base16 character');\n        }\n      }\n\n      return Buffer.from(input, 'hex');\n    }\n\n  };\n};", "'use strict';\n\nfunction decode(input, alphabet) {\n  input = input.replace(new RegExp('=', 'g'), '');\n  let length = input.length;\n  let bits = 0;\n  let value = 0;\n  let index = 0;\n  let output = new Uint8Array(length * 5 / 8 | 0);\n\n  for (let i = 0; i < length; i++) {\n    value = value << 5 | alphabet.indexOf(input[i]);\n    bits += 5;\n\n    if (bits >= 8) {\n      output[index++] = value >>> bits - 8 & 255;\n      bits -= 8;\n    }\n  }\n\n  return output.buffer;\n}\n\nfunction encode(buffer, alphabet) {\n  let length = buffer.byteLength;\n  let view = new Uint8Array(buffer);\n  let padding = alphabet.indexOf('=') === alphabet.length - 1;\n\n  if (padding) {\n    alphabet = alphabet.substring(0, alphabet.length - 2);\n  }\n\n  let bits = 0;\n  let value = 0;\n  let output = '';\n\n  for (let i = 0; i < length; i++) {\n    value = value << 8 | view[i];\n    bits += 8;\n\n    while (bits >= 5) {\n      output += alphabet[value >>> bits - 5 & 31];\n      bits -= 5;\n    }\n  }\n\n  if (bits > 0) {\n    output += alphabet[value << 5 - bits & 31];\n  }\n\n  if (padding) {\n    while (output.length % 8 !== 0) {\n      output += '=';\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = function base32(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return encode(Buffer.from(input), alphabet);\n      }\n\n      return encode(input, alphabet);\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base32 character');\n        }\n      }\n\n      return decode(input, alphabet);\n    }\n\n  };\n};", "'use strict';\n\nmodule.exports = function base64(alphabet) {\n  // The alphabet is only used to know:\n  //   1. If padding is enabled (must contain '=')\n  //   2. If the output must be url-safe (must contain '-' and '_')\n  //   3. If the input of the output function is valid\n  // The alphabets from RFC 4648 are always used.\n  const padding = alphabet.indexOf('=') > -1;\n  const url = alphabet.indexOf('-') > -1 && alphabet.indexOf('_') > -1;\n  return {\n    encode(input) {\n      let output = '';\n\n      if (typeof input === 'string') {\n        output = Buffer.from(input).toString('base64');\n      } else {\n        output = input.toString('base64');\n      }\n\n      if (url) {\n        output = output.replace(/\\+/g, '-').replace(/\\//g, '_');\n      }\n\n      const pad = output.indexOf('=');\n\n      if (pad > 0 && !padding) {\n        output = output.substring(0, pad);\n      }\n\n      return output;\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base64 character');\n        }\n      }\n\n      return Buffer.from(input, 'base64');\n    }\n\n  };\n};", "/**\n * Implementation of the multicodec specification.\n *\n * @module multicodec\n * @example\n * const multicodec = require('multicodec')\n *\n * const prefixedProtobuf = multicodec.addPrefix('protobuf', protobufBuffer)\n * // prefixedProtobuf 0x50...\n *\n */\n'use strict';\n\nconst varint = require('varint');\n\nconst intTable = require('./int-table');\n\nconst codecNameToCodeVarint = require('./varint-table');\n\nconst util = require('./util');\n\nexports = module.exports;\n/**\n * Prefix a buffer with a multicodec-packed.\n *\n * @param {string|number} multicodecStrOrCode\n * @param {Buffer} data\n * @returns {Buffer}\n */\n\nexports.addPrefix = (multicodecStrOrCode, data) => {\n  let prefix;\n\n  if (Buffer.isBuffer(multicodecStrOrCode)) {\n    prefix = util.varintBufferEncode(multicodecStrOrCode);\n  } else {\n    if (codecNameToCodeVarint[multicodecStrOrCode]) {\n      prefix = codecNameToCodeVarint[multicodecStrOrCode];\n    } else {\n      throw new Error('multicodec not recognized');\n    }\n  }\n\n  return Buffer.concat([prefix, data]);\n};\n/**\n * Decapsulate the multicodec-packed prefix from the data.\n *\n * @param {Buffer} data\n * @returns {Buffer}\n */\n\n\nexports.rmPrefix = data => {\n  varint.decode(data);\n  return data.slice(varint.decode.bytes);\n};\n/**\n * Get the codec of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {string}\n */\n\n\nexports.getCodec = prefixedData => {\n  const code = varint.decode(prefixedData);\n  const codecName = intTable.get(code);\n\n  if (codecName === undefined) {\n    throw new Error(\"Code \".concat(code, \" not found\"));\n  }\n\n  return codecName;\n};\n/**\n * Get the name of the codec.\n * @param {number} codec\n * @returns {string}\n */\n\n\nexports.getName = codec => {\n  return intTable.get(codec);\n};\n/**\n * Get the code of the codec\n * @param {string} name\n * @returns {number}\n */\n\n\nexports.getNumber = name => {\n  const code = codecNameToCodeVarint[name];\n\n  if (code === undefined) {\n    throw new Error('Codec `' + name + '` not found');\n  }\n\n  return util.varintBufferDecode(code)[0];\n};\n/**\n * Get the code of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {number}\n */\n\n\nexports.getCode = prefixedData => {\n  return varint.decode(prefixedData);\n};\n/**\n * Get the code as varint of a codec name.\n * @param {string} codecName\n * @returns {Buffer}\n */\n\n\nexports.getCodeVarint = codecName => {\n  const code = codecNameToCodeVarint[codecName];\n\n  if (code === undefined) {\n    throw new Error('Codec `' + codecName + '` not found');\n  }\n\n  return code;\n};\n/**\n * Get the varint of a code.\n * @param {Number} code\n * @returns {Array.<number>}\n */\n\n\nexports.getVarint = code => {\n  return varint.encode(code);\n}; // Make the constants top-level constants\n\n\nconst constants = require('./constants');\n\nObject.assign(exports, constants); // Human friendly names for printing, e.g. in error messages\n\nexports.print = require('./print');", "'use strict';\n\nconst baseTable = require('./base-table.json'); // map for hexString -> codecName\n\n\nconst nameTable = new Map();\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName];\n  nameTable.set(code, encodingName);\n}\n\nmodule.exports = Object.freeze(nameTable);", "'use strict';\n\nconst baseTable = require('./base-table.json');\n\nconst varintEncode = require('./util').varintEncode; // map for codecName -> codeVarintBuffer\n\n\nconst varintTable = {};\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName];\n  varintTable[encodingName] = varintEncode(code);\n}\n\nmodule.exports = Object.freeze(varintTable);", "'use strict';\n\nconst table = require('./base-table.json'); // map for codecConstant -> code\n\n\nconst constants = {};\n\nfor (const [name, code] of Object.entries(table)) {\n  constants[name.toUpperCase().replace(/-/g, '_')] = code;\n}\n\nmodule.exports = Object.freeze(constants);", "'use strict';\n\nconst table = require('./base-table.json'); // map for code -> print friendly name\n\n\nconst tableByCode = {};\n\nfor (const [name, code] of Object.entries(table)) {\n  if (tableByCode[code] === undefined) tableByCode[code] = name;\n}\n\nmodule.exports = Object.freeze(tableByCode);", "'use strict'\n\nconst mh = require('multihashes')\nconst { <PERSON><PERSON><PERSON> } = require('buffer')\nvar CIDUtil = {\n  /**\n   * Test if the given input is a valid CID object.\n   * Returns an error message if it is not.\n   * Returns undefined if it is a valid CID.\n   *\n   * @param {any} other\n   * @returns {string}\n   */\n  checkCIDComponents: function (other) {\n    if (other == null) {\n      return 'null values are not valid CIDs'\n    }\n\n    if (!(other.version === 0 || other.version === 1)) {\n      return 'Invalid version, must be a number equal to 1 or 0'\n    }\n\n    if (typeof other.codec !== 'string') {\n      return 'codec must be string'\n    }\n\n    if (other.version === 0) {\n      if (other.codec !== 'dag-pb') {\n        return \"codec must be 'dag-pb' for CIDv0\"\n      }\n      if (other.multibaseName !== 'base58btc') {\n        return \"multibaseName must be 'base58btc' for CIDv0\"\n      }\n    }\n\n    if (!Buffer.isBuffer(other.multihash)) {\n      return 'multihash must be a Buffer'\n    }\n\n    try {\n      mh.validate(other.multihash)\n    } catch (err) {\n      let errorMsg = err.message\n      if (!errorMsg) { // Just in case mh.validate() throws an error with empty error message\n        errorMsg = 'Multihash validation failed'\n      }\n      return errorMsg\n    }\n  }\n}\n\nmodule.exports = CIDUtil\n", "'use strict';\n\nfunction withIs(Class, {\n  className,\n  symbolName\n}) {\n  const symbol = Symbol.for(symbolName);\n  const ClassIsWrapper = {\n    // The code below assigns the class wrapper to an object to trick\n    // JavaScript engines to show the name of the extended class when\n    // logging an instances.\n    // We are assigning an anonymous class (class wrapper) to the object\n    // with key `className` to keep the correct name.\n    // If this is not supported it falls back to logging `ClassIsWrapper`.\n    [className]: class extends Class {\n      constructor(...args) {\n        super(...args);\n        Object.defineProperty(this, symbol, {\n          value: true\n        });\n      }\n\n      get [Symbol.toStringTag]() {\n        return className;\n      }\n\n    }\n  }[className];\n\n  ClassIsWrapper[\"is\".concat(className)] = obj => !!(obj && obj[symbol]);\n\n  return ClassIsWrapper;\n}\n\nfunction withIsProto(Class, {\n  className,\n  symbolName,\n  withoutNew\n}) {\n  const symbol = Symbol.for(symbolName);\n  /* eslint-disable object-shorthand */\n\n  const ClassIsWrapper = {\n    [className]: function (...args) {\n      if (withoutNew && !(this instanceof ClassIsWrapper)) {\n        return new ClassIsWrapper(...args);\n      }\n\n      const _this = Class.call(this, ...args) || this;\n\n      if (_this && !_this[symbol]) {\n        Object.defineProperty(_this, symbol, {\n          value: true\n        });\n      }\n\n      return _this;\n    }\n  }[className];\n  /* eslint-enable object-shorthand */\n\n  ClassIsWrapper.prototype = Object.create(Class.prototype);\n  ClassIsWrapper.prototype.constructor = ClassIsWrapper;\n  Object.defineProperty(ClassIsWrapper.prototype, Symbol.toStringTag, {\n    get() {\n      return className;\n    }\n\n  });\n\n  ClassIsWrapper[\"is\".concat(className)] = obj => !!(obj && obj[symbol]);\n\n  return ClassIsWrapper;\n}\n\nmodule.exports = withIs;\nmodule.exports.proto = withIsProto;"], "sourceRoot": ""}