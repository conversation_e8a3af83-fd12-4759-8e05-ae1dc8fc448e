{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["../src/secp256k1.ts"], "names": [], "mappings": ";;;;;;AAAA,+CAAkD;AAClD,kDAAuB;AACvB,wDAAgC;AAEhC,6DAAsF;AAEtF,MAAM,SAAS,GAAG,IAAI,kBAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;AAC/C,MAAM,UAAU,GAAG,IAAI,eAAE,CAAC,kEAAkE,EAAE,KAAK,CAAC,CAAC;AAgBrG,MAAa,SAAS;IACpB;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,OAAmB;QACjD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,yDAAyD;YACzD,iDAAiD;YACjD,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,MAAM,KAAK,IAAI,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QAED,6DAA6D;QAC7D,MAAM,mBAAmB,GAAG,IAAI,eAAE,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;YACvC,8BAA8B;YAC9B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;SACpE;QAED,MAAM,GAAG,GAAqB;YAC5B,OAAO,EAAE,IAAA,kBAAO,EAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC3C,0BAA0B;YAC1B,uBAAuB;YACvB,yBAAyB;YACzB,yBAAyB;YACzB,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACpD,CAAC;QACF,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,eAAe,CACjC,WAAuB,EACvB,OAAmB;QAEnB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QACD,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACjE;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,4EAA4E;QAC5E,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC/E,IAAI,OAAO,aAAa,KAAK,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACjF,OAAO,IAAI,+CAA0B,CACnC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAC5B,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,EAC5B,aAAa,CACd,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,eAAe,CACjC,SAA6B,EAC7B,WAAuB,EACvB,MAAkB;QAElB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QACD,IAAI,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;SACjE;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAEhD,4CAA4C;QAC5C,EAAE;QACF,wEAAwE;QACxE,EAAE;QACF,gCAAgC;QAChC,iDAAiD;QACjD,6CAA6C;QAC7C,6DAA6D;QAC7D,qDAAqD;QACrD,EAAE;QACF,gFAAgF;QAChF,gFAAgF;QAChF,UAAU;QACV,IAAI;YACF,OAAO,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;SACvD;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,SAAqC,EAAE,WAAuB;QACxF,MAAM,oBAAoB,GAAG,EAAE,CAAC,EAAE,IAAA,gBAAK,EAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAA,gBAAK,EAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClF,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,EAAE,oBAAoB,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC7F,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/C,OAAO,IAAA,kBAAO,EAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,cAAc,CAAC,MAAkB;QAC7C,QAAQ,MAAM,CAAC,MAAM,EAAE;YACrB,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC;YAChB,KAAK,EAAE;gBACL,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;YACnF;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;IACH,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,MAAkB;QAC/C,QAAQ,MAAM,CAAC,MAAM,EAAE;YACrB,KAAK,EAAE;gBACL,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;YACpF,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC;YAChB;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC5C;IACH,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,SAAqB;QAClD,QAAQ,SAAS,CAAC,MAAM,EAAE;YACxB,KAAK,EAAE;gBACL,OAAO,SAAS,CAAC;YACnB,KAAK,EAAE;gBACL,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAChC;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;SAC/C;IACH,CAAC;CACF;AApJD,8BAoJC"}