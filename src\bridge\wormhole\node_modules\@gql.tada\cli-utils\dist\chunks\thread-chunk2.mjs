import e from "typescript";

import { getSchemaNamesFromConfig as a } from "@gql.tada/internal";

import { findAllCallExpressions as i } from "@0no-co/graphqlsp/api";

import { e as r } from "./index-chunk2.mjs";

import { p as t } from "./index-chunk.mjs";

var o = r((async function* _runTurbo(r) {
  var o = a(r.pluginConfig);
  var l = t(r);
  l.addSourceFile({
    fileId: "__gql-tada-override__.d.ts",
    sourceText: s,
    scriptKind: e.ScriptKind.TS
  });
  var c = l.createExternalFiles();
  if (c.length) {
    yield {
      kind: "EXTERNAL_WARNING"
    };
    await l.addVirtualFiles(c);
  }
  var m = l.build();
  var u = m.buildPluginInfo(r.pluginConfig);
  var d = m.getSourceFiles();
  yield {
    kind: "FILE_COUNT",
    fileCount: d.length
  };
  var p = m.program.getTypeChecker();
  for (var g of d) {
    var f = g.fileName;
    var y = [];
    var T = [];
    var h = i(g, u, !1).nodes;
    for (var v of h) {
      var F = v.node.parent;
      if (!e.isCallExpression(F)) {
        continue;
      }
      var N = m.getSourcePosition(g, F.getStart());
      f = N.fileName;
      if (!o.has(v.schema)) {
        T.push({
          message: v.schema ? `The '${v.schema}' schema is not in the configuration but was referenced by document.` : o.size > 1 ? "The document is not for a known schema. Have you re-generated the output file?" : "Multiple schemas are configured, but the document is not for a specific schema.",
          file: N.fileName,
          line: N.line,
          col: N.col
        });
        continue;
      }
      var S = p.getTypeAtLocation(F);
      var b = p.getTypeAtLocation(v.node);
      if (!S.symbol || "TadaDocumentNode" !== S.symbol.getEscapedName()) {
        T.push({
          message: 'The discovered document is not of type "TadaDocumentNode".\nIf this is unexpected, please file an issue describing your case.',
          file: N.fileName,
          line: N.line,
          col: N.col
        });
        continue;
      }
      var _ = "value" in b && "string" == typeof b.value && !(b.flags & e.TypeFlags.StringLiteral) ? JSON.stringify(b.value) : p.typeToString(b, F, n);
      var A = p.typeToString(S, F, n);
      y.push({
        schemaName: v.schema,
        argumentKey: _,
        documentType: A
      });
    }
    yield {
      kind: "FILE_TURBO",
      filePath: f,
      documents: y,
      warnings: T
    };
  }
}));

var n = e.TypeFormatFlags.NoTruncation | e.TypeFormatFlags.NoTypeReduction | e.TypeFormatFlags.InTypeAlias | e.TypeFormatFlags.UseFullyQualifiedType | e.TypeFormatFlags.GenerateNamesForShadowedTypeParams | e.TypeFormatFlags.UseAliasDefinedOutsideCurrentScope | e.TypeFormatFlags.AllowUniqueESSymbolType | e.TypeFormatFlags.WriteTypeArgumentsOfSignature;

var s = "\nimport * as _gqlTada from 'gql.tada';\ndeclare module 'gql.tada' {\n  interface setupCache {\n    readonly __cacheDisabled: true;\n  }\n}\n".trim();

export { o as runTurbo };
//# sourceMappingURL=thread-chunk2.mjs.map
