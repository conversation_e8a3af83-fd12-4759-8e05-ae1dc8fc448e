"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecPrivilegedArgBase_js_1 = require("../ExecPrivilegedArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecPrivilegedArgVaultSubscribe extends ExecPrivilegedArgBase_js_1.ExecPrivilegedArgBase {
    static fromJSON(params) {
        return new ExecPrivilegedArgVaultSubscribe(params);
    }
    toData() {
        const { params } = this;
        return {
            vault_subaccount_id: params.vaultSubaccountId,
            trader_subaccount_id: params.traderSubaccountId,
            msg: {
                subscribe: params.args,
            },
        };
    }
    toExecData() {
        const { params } = this;
        return (0, ExecPrivilegedArgBase_js_1.dataToExecData)(this.toData(), {
            origin: params.origin,
            name: 'VaultSubscribe',
        });
    }
}
exports.default = ExecPrivilegedArgVaultSubscribe;
