import _m0 from "protobufjs/minimal.js";
import { PythPriceState, StorkPriceState } from "./oracle";
export declare const protobufPackage = "injective.oracle.v1beta1";
export interface SetChainlinkPriceEvent {
    feedId: string;
    answer: string;
    timestamp: string;
}
/** Event type upon set ref */
export interface SetBandPriceEvent {
    relayer: string;
    symbol: string;
    price: string;
    resolveTime: string;
    requestId: string;
}
export interface SetBandIBCPriceEvent {
    relayer: string;
    symbols: string[];
    prices: string[];
    resolveTime: string;
    requestId: string;
    clientId: string;
}
export interface EventBandIBCAckSuccess {
    ackResult: string;
    clientId: string;
}
export interface EventBandIBCAckError {
    ackError: string;
    clientId: string;
}
export interface EventBandIBCResponseTimeout {
    clientId: string;
}
export interface SetPriceFeedPriceEvent {
    relayer: string;
    base: string;
    quote: string;
    /** price defines the price of the oracle base and quote */
    price: string;
}
export interface SetProviderPriceEvent {
    provider: string;
    relayer: string;
    symbol: string;
    price: string;
}
export interface SetCoinbasePriceEvent {
    symbol: string;
    price: string;
    timestamp: string;
}
export interface EventSetStorkPrices {
    prices: StorkPriceState[];
}
export interface EventSetPythPrices {
    prices: PythPriceState[];
}
export declare const SetChainlinkPriceEvent: {
    encode(message: SetChainlinkPriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetChainlinkPriceEvent;
    fromJSON(object: any): SetChainlinkPriceEvent;
    toJSON(message: SetChainlinkPriceEvent): unknown;
    create(base?: DeepPartial<SetChainlinkPriceEvent>): SetChainlinkPriceEvent;
    fromPartial(object: DeepPartial<SetChainlinkPriceEvent>): SetChainlinkPriceEvent;
};
export declare const SetBandPriceEvent: {
    encode(message: SetBandPriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBandPriceEvent;
    fromJSON(object: any): SetBandPriceEvent;
    toJSON(message: SetBandPriceEvent): unknown;
    create(base?: DeepPartial<SetBandPriceEvent>): SetBandPriceEvent;
    fromPartial(object: DeepPartial<SetBandPriceEvent>): SetBandPriceEvent;
};
export declare const SetBandIBCPriceEvent: {
    encode(message: SetBandIBCPriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetBandIBCPriceEvent;
    fromJSON(object: any): SetBandIBCPriceEvent;
    toJSON(message: SetBandIBCPriceEvent): unknown;
    create(base?: DeepPartial<SetBandIBCPriceEvent>): SetBandIBCPriceEvent;
    fromPartial(object: DeepPartial<SetBandIBCPriceEvent>): SetBandIBCPriceEvent;
};
export declare const EventBandIBCAckSuccess: {
    encode(message: EventBandIBCAckSuccess, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBandIBCAckSuccess;
    fromJSON(object: any): EventBandIBCAckSuccess;
    toJSON(message: EventBandIBCAckSuccess): unknown;
    create(base?: DeepPartial<EventBandIBCAckSuccess>): EventBandIBCAckSuccess;
    fromPartial(object: DeepPartial<EventBandIBCAckSuccess>): EventBandIBCAckSuccess;
};
export declare const EventBandIBCAckError: {
    encode(message: EventBandIBCAckError, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBandIBCAckError;
    fromJSON(object: any): EventBandIBCAckError;
    toJSON(message: EventBandIBCAckError): unknown;
    create(base?: DeepPartial<EventBandIBCAckError>): EventBandIBCAckError;
    fromPartial(object: DeepPartial<EventBandIBCAckError>): EventBandIBCAckError;
};
export declare const EventBandIBCResponseTimeout: {
    encode(message: EventBandIBCResponseTimeout, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBandIBCResponseTimeout;
    fromJSON(object: any): EventBandIBCResponseTimeout;
    toJSON(message: EventBandIBCResponseTimeout): unknown;
    create(base?: DeepPartial<EventBandIBCResponseTimeout>): EventBandIBCResponseTimeout;
    fromPartial(object: DeepPartial<EventBandIBCResponseTimeout>): EventBandIBCResponseTimeout;
};
export declare const SetPriceFeedPriceEvent: {
    encode(message: SetPriceFeedPriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetPriceFeedPriceEvent;
    fromJSON(object: any): SetPriceFeedPriceEvent;
    toJSON(message: SetPriceFeedPriceEvent): unknown;
    create(base?: DeepPartial<SetPriceFeedPriceEvent>): SetPriceFeedPriceEvent;
    fromPartial(object: DeepPartial<SetPriceFeedPriceEvent>): SetPriceFeedPriceEvent;
};
export declare const SetProviderPriceEvent: {
    encode(message: SetProviderPriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetProviderPriceEvent;
    fromJSON(object: any): SetProviderPriceEvent;
    toJSON(message: SetProviderPriceEvent): unknown;
    create(base?: DeepPartial<SetProviderPriceEvent>): SetProviderPriceEvent;
    fromPartial(object: DeepPartial<SetProviderPriceEvent>): SetProviderPriceEvent;
};
export declare const SetCoinbasePriceEvent: {
    encode(message: SetCoinbasePriceEvent, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SetCoinbasePriceEvent;
    fromJSON(object: any): SetCoinbasePriceEvent;
    toJSON(message: SetCoinbasePriceEvent): unknown;
    create(base?: DeepPartial<SetCoinbasePriceEvent>): SetCoinbasePriceEvent;
    fromPartial(object: DeepPartial<SetCoinbasePriceEvent>): SetCoinbasePriceEvent;
};
export declare const EventSetStorkPrices: {
    encode(message: EventSetStorkPrices, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetStorkPrices;
    fromJSON(object: any): EventSetStorkPrices;
    toJSON(message: EventSetStorkPrices): unknown;
    create(base?: DeepPartial<EventSetStorkPrices>): EventSetStorkPrices;
    fromPartial(object: DeepPartial<EventSetStorkPrices>): EventSetStorkPrices;
};
export declare const EventSetPythPrices: {
    encode(message: EventSetPythPrices, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetPythPrices;
    fromJSON(object: any): EventSetPythPrices;
    toJSON(message: EventSetPythPrices): unknown;
    create(base?: DeepPartial<EventSetPythPrices>): EventSetPythPrices;
    fromPartial(object: DeepPartial<EventSetPythPrices>): EventSetPythPrices;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
