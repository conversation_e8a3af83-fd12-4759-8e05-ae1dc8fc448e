{"version": 3, "sources": ["../../src/internal/transaction.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * This file contains the underlying implementations for exposed API surface in\n * the {@link api/transaction}. By moving the methods out into a separate file,\n * other namespaces and processes can access these methods without depending on the entire\n * transaction namespace and without having a dependency cycle error.\n * @group Implementation\n */\n\nimport { AptosConfig } from \"../api/aptosConfig\";\nimport { getAptosFullNode, paginateWithCursor } from \"../client\";\nimport { AptosApiError } from \"../errors\";\nimport {\n  TransactionResponseType,\n  type AnyNumber,\n  type GasEstimation,\n  type HexInput,\n  type PaginationArgs,\n  type TransactionResponse,\n  WaitForTransactionOptions,\n  CommittedTransactionResponse,\n  Block,\n} from \"../types\";\nimport { DEFAULT_TXN_TIMEOUT_SEC, ProcessorType } from \"../utils/const\";\nimport { sleep } from \"../utils/helpers\";\nimport { memoizeAsync } from \"../utils/memoize\";\nimport { getIndexerLastSuccessVersion, getProcessorStatus } from \"./general\";\n\n/**\n * Retrieve a list of transactions based on the specified options.\n *\n * @param {Object} args - The parameters for retrieving transactions.\n * @param {Object} args.aptosConfig - The configuration object for Aptos.\n * @param {Object} args.options - The options for pagination.\n * @param {number} args.options.offset - The number of transactions to skip before starting to collect the result set.\n * @param {number} args.options.limit - The maximum number of transactions to return.\n * @group Implementation\n */\nexport async function getTransactions(args: {\n  aptosConfig: AptosConfig;\n  options?: PaginationArgs;\n}): Promise<TransactionResponse[]> {\n  const { aptosConfig, options } = args;\n  return paginateWithCursor<{}, TransactionResponse[]>({\n    aptosConfig,\n    originMethod: \"getTransactions\",\n    path: \"transactions\",\n    params: { start: options?.offset, limit: options?.limit },\n  });\n}\n\n/**\n * Retrieves the estimated gas price for transactions on the Aptos network.\n * This function helps users understand the current gas price, which is essential for transaction planning and cost estimation.\n *\n * @param args - The configuration parameters for the Aptos network.\n * @param args.aptosConfig - The configuration object containing network details.\n * @group Implementation\n */\nexport async function getGasPriceEstimation(args: { aptosConfig: AptosConfig }) {\n  const { aptosConfig } = args;\n\n  return memoizeAsync(\n    async () => {\n      const { data } = await getAptosFullNode<{}, GasEstimation>({\n        aptosConfig,\n        originMethod: \"getGasPriceEstimation\",\n        path: \"estimate_gas_price\",\n      });\n      return data;\n    },\n    `gas-price-${aptosConfig.network}`,\n    1000 * 60 * 5, // 5 minutes\n  )();\n}\n\n/**\n * Retrieves the transaction details associated with a specific ledger version.\n *\n * @param args - The arguments for the transaction retrieval.\n * @param args.aptosConfig - The configuration settings for the Aptos client.\n * @param args.ledgerVersion - The ledger version for which to retrieve the transaction.\n * @returns The transaction details for the specified ledger version.\n * @group Implementation\n */\nexport async function getTransactionByVersion(args: {\n  aptosConfig: AptosConfig;\n  ledgerVersion: AnyNumber;\n}): Promise<TransactionResponse> {\n  const { aptosConfig, ledgerVersion } = args;\n  const { data } = await getAptosFullNode<{}, TransactionResponse>({\n    aptosConfig,\n    originMethod: \"getTransactionByVersion\",\n    path: `transactions/by_version/${ledgerVersion}`,\n  });\n  return data;\n}\n\n/**\n * Retrieves transaction details using the specified transaction hash.\n *\n * @param args - The arguments for retrieving the transaction.\n * @param args.aptosConfig - The configuration settings for the Aptos client.\n * @param args.transactionHash - The hash of the transaction to retrieve.\n * @returns A promise that resolves to the transaction details.\n * @group Implementation\n */\nexport async function getTransactionByHash(args: {\n  aptosConfig: AptosConfig;\n  transactionHash: HexInput;\n}): Promise<TransactionResponse> {\n  const { aptosConfig, transactionHash } = args;\n  const { data } = await getAptosFullNode<{}, TransactionResponse>({\n    aptosConfig,\n    path: `transactions/by_hash/${transactionHash}`,\n    originMethod: \"getTransactionByHash\",\n  });\n  return data;\n}\n\n/**\n * Checks if a transaction is currently pending based on its hash.\n * This function helps determine the status of a transaction in the Aptos network.\n *\n * @param args - The arguments for checking the transaction status.\n * @param args.aptosConfig - The configuration settings for connecting to the Aptos network.\n * @param args.transactionHash - The hash of the transaction to check.\n * @returns A boolean indicating whether the transaction is pending.\n * @throws An error if the transaction cannot be retrieved due to reasons other than a 404 status.\n * @group Implementation\n */\nexport async function isTransactionPending(args: {\n  aptosConfig: AptosConfig;\n  transactionHash: HexInput;\n}): Promise<boolean> {\n  const { aptosConfig, transactionHash } = args;\n  try {\n    const transaction = await getTransactionByHash({ aptosConfig, transactionHash });\n    return transaction.type === TransactionResponseType.Pending;\n  } catch (e: any) {\n    if (e?.status === 404) {\n      return true;\n    }\n    throw e;\n  }\n}\n\n/**\n * Waits for a transaction to be confirmed by its hash.\n * This function allows you to monitor the status of a transaction until it is finalized.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration settings for the Aptos client.\n * @param args.transactionHash - The hash of the transaction to wait for.\n * @group Implementation\n */\nexport async function longWaitForTransaction(args: {\n  aptosConfig: AptosConfig;\n  transactionHash: HexInput;\n}): Promise<TransactionResponse> {\n  const { aptosConfig, transactionHash } = args;\n  const { data } = await getAptosFullNode<{}, TransactionResponse>({\n    aptosConfig,\n    path: `transactions/wait_by_hash/${transactionHash}`,\n    originMethod: \"longWaitForTransaction\",\n  });\n  return data;\n}\n\n/**\n * Waits for a transaction to be confirmed on the blockchain and handles potential errors during the process.\n * This function allows you to monitor the status of a transaction until it is either confirmed or fails.\n *\n * @param args - The arguments for waiting for a transaction.\n * @param args.aptosConfig - The configuration settings for Aptos.\n * @param args.transactionHash - The hash of the transaction to wait for.\n * @param args.options - Optional settings for waiting, including timeout and success check.\n * @param args.options.timeoutSecs - The maximum time to wait for the transaction in seconds. Defaults to a predefined value.\n * @param args.options.checkSuccess - A flag indicating whether to check the success status of the transaction. Defaults to true.\n * @returns A promise that resolves to the transaction response once the transaction is confirmed.\n * @throws WaitForTransactionError if the transaction times out or remains pending.\n * @throws FailedTransactionError if the transaction fails.\n * @group Implementation\n */\nexport async function waitForTransaction(args: {\n  aptosConfig: AptosConfig;\n  transactionHash: HexInput;\n  options?: WaitForTransactionOptions;\n}): Promise<CommittedTransactionResponse> {\n  const { aptosConfig, transactionHash, options } = args;\n  const timeoutSecs = options?.timeoutSecs ?? DEFAULT_TXN_TIMEOUT_SEC;\n  const checkSuccess = options?.checkSuccess ?? true;\n\n  let isPending = true;\n  let timeElapsed = 0;\n  let lastTxn: TransactionResponse | undefined;\n  let lastError: AptosApiError | undefined;\n  let backoffIntervalMs = 200;\n  const backoffMultiplier = 1.5;\n\n  /**\n   * Handles API errors by throwing the last error or a timeout error for a failed transaction.\n   *\n   * @param e - The error object that occurred during the API call.\n   * @throws {Error} Throws the last error if it exists; otherwise, throws a WaitForTransactionError indicating a timeout.\n   * @group Implementation\n   */\n  function handleAPIError(e: any) {\n    // In short, this means we will retry if it was an AptosApiError and the code was 404 or 5xx.\n    const isAptosApiError = e instanceof AptosApiError;\n    if (!isAptosApiError) {\n      throw e; // This would be unexpected\n    }\n    lastError = e;\n    const isRequestError = e.status !== 404 && e.status >= 400 && e.status < 500;\n    if (isRequestError) {\n      throw e;\n    }\n  }\n\n  // check to see if the txn is already on the blockchain\n  try {\n    lastTxn = await getTransactionByHash({ aptosConfig, transactionHash });\n    isPending = lastTxn.type === TransactionResponseType.Pending;\n  } catch (e) {\n    handleAPIError(e);\n  }\n\n  // If the transaction is pending, we do a long wait once to avoid polling\n  if (isPending) {\n    const startTime = Date.now();\n    try {\n      lastTxn = await longWaitForTransaction({ aptosConfig, transactionHash });\n      isPending = lastTxn.type === TransactionResponseType.Pending;\n    } catch (e) {\n      handleAPIError(e);\n    }\n    timeElapsed = (Date.now() - startTime) / 1000;\n  }\n\n  // Now we do polling to see if the transaction is still pending\n  while (isPending) {\n    if (timeElapsed >= timeoutSecs) {\n      break;\n    }\n    try {\n      // eslint-disable-next-line no-await-in-loop\n      lastTxn = await getTransactionByHash({ aptosConfig, transactionHash });\n\n      isPending = lastTxn.type === TransactionResponseType.Pending;\n\n      if (!isPending) {\n        break;\n      }\n    } catch (e) {\n      handleAPIError(e);\n    }\n    // eslint-disable-next-line no-await-in-loop\n    await sleep(backoffIntervalMs);\n    timeElapsed += backoffIntervalMs / 1000; // Convert to seconds\n    backoffIntervalMs *= backoffMultiplier;\n  }\n\n  // There is a chance that lastTxn is still undefined. Let's throw the last error otherwise a WaitForTransactionError\n  if (lastTxn === undefined) {\n    if (lastError) {\n      throw lastError;\n    } else {\n      throw new WaitForTransactionError(\n        `Fetching transaction ${transactionHash} failed and timed out after ${timeoutSecs} seconds`,\n        lastTxn,\n      );\n    }\n  }\n\n  if (lastTxn.type === TransactionResponseType.Pending) {\n    throw new WaitForTransactionError(\n      `Transaction ${transactionHash} timed out in pending state after ${timeoutSecs} seconds`,\n      lastTxn,\n    );\n  }\n  if (!checkSuccess) {\n    return lastTxn;\n  }\n  if (!lastTxn.success) {\n    throw new FailedTransactionError(\n      `Transaction ${transactionHash} failed with an error: ${lastTxn.vm_status}`,\n      lastTxn,\n    );\n  }\n\n  return lastTxn;\n}\n\n/**\n * Waits for the indexer to sync up to the specified ledger version. The timeout is 3 seconds.\n *\n * @param args - The arguments for the function.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @param args.minimumLedgerVersion - The minimum ledger version that the indexer should sync to.\n * @param args.processorType - (Optional) The type of processor to check the last success version from.\n * @group Implementation\n */\nexport async function waitForIndexer(args: {\n  aptosConfig: AptosConfig;\n  minimumLedgerVersion: AnyNumber;\n  processorType?: ProcessorType;\n}): Promise<void> {\n  const { aptosConfig, processorType } = args;\n  const minimumLedgerVersion = BigInt(args.minimumLedgerVersion);\n  const timeoutMilliseconds = 3000; // 3 seconds\n  const startTime = new Date().getTime();\n  let indexerVersion = BigInt(-1);\n\n  while (indexerVersion < minimumLedgerVersion) {\n    // check for timeout\n    if (new Date().getTime() - startTime > timeoutMilliseconds) {\n      throw new Error(\"waitForLastSuccessIndexerVersionSync timeout\");\n    }\n\n    if (processorType === undefined) {\n      // Get the last success version from all processor\n      // eslint-disable-next-line no-await-in-loop\n      indexerVersion = await getIndexerLastSuccessVersion({ aptosConfig });\n    } else {\n      // Get the last success version from the specific processor\n      // eslint-disable-next-line no-await-in-loop\n      const processor = await getProcessorStatus({ aptosConfig, processorType });\n      indexerVersion = processor.last_success_version;\n    }\n\n    if (indexerVersion >= minimumLedgerVersion) {\n      // break out immediately if we are synced\n      break;\n    }\n\n    // eslint-disable-next-line no-await-in-loop\n    await sleep(200);\n  }\n}\n\n/**\n * Represents an error that occurs when waiting for a transaction to complete.\n * This error is thrown by the `waitForTransaction` function when a transaction\n * times out or when the transaction response is undefined.\n *\n * @param message - A descriptive message for the error.\n * @param lastSubmittedTransaction - The last submitted transaction response, if available.\n * @group Implementation\n */\nexport class WaitForTransactionError extends Error {\n  public readonly lastSubmittedTransaction: TransactionResponse | undefined;\n\n  /**\n   * Constructs an instance of the class with a specified message and transaction response.\n   *\n   * @param message - The message associated with the transaction.\n   * @param lastSubmittedTransaction - The transaction response object containing details about the transaction.\n   * @group Implementation\n   */\n  constructor(message: string, lastSubmittedTransaction: TransactionResponse | undefined) {\n    super(message);\n    this.lastSubmittedTransaction = lastSubmittedTransaction;\n  }\n}\n\n/**\n * Represents an error that occurs when a transaction fails.\n * This error is thrown by the `waitForTransaction` function when the `checkSuccess` parameter is set to true.\n *\n * @param message - A description of the error.\n * @param transaction - The transaction response associated with the failure.\n * @group Implementation\n */\nexport class FailedTransactionError extends Error {\n  public readonly transaction: TransactionResponse;\n\n  constructor(message: string, transaction: TransactionResponse) {\n    super(message);\n    this.transaction = transaction;\n  }\n}\n\n/**\n * Retrieves a block from the Aptos blockchain by its ledger version.\n * This function allows you to obtain detailed information about a specific block, including its transactions if requested.\n *\n * @param args - The arguments for retrieving the block.\n * @param args.aptosConfig - The configuration object for connecting to the Aptos node.\n * @param args.ledgerVersion - The ledger version of the block to retrieve.\n * @param args.options - Optional parameters for the request.\n * @param args.options.withTransactions - Indicates whether to include transactions in the block data.\n * @group Implementation\n */\nexport async function getBlockByVersion(args: {\n  aptosConfig: AptosConfig;\n  ledgerVersion: AnyNumber;\n  options?: { withTransactions?: boolean };\n}): Promise<Block> {\n  const { aptosConfig, ledgerVersion, options } = args;\n  const { data: block } = await getAptosFullNode<{}, Block>({\n    aptosConfig,\n    originMethod: \"getBlockByVersion\",\n    path: `blocks/by_version/${ledgerVersion}`,\n    params: { with_transactions: options?.withTransactions },\n  });\n\n  return fillBlockTransactions({ block, ...args });\n}\n\n/**\n * Retrieves a block from the Aptos blockchain by its height.\n *\n * @param args - The parameters for retrieving the block.\n * @param args.aptosConfig - The configuration object for connecting to the Aptos network.\n * @param args.blockHeight - The height of the block to retrieve.\n * @param args.options - Optional parameters for the request.\n * @param args.options.withTransactions - Indicates whether to include transactions in the block data.\n * @returns A promise that resolves to the block data, potentially including its transactions.\n * @group Implementation\n */\nexport async function getBlockByHeight(args: {\n  aptosConfig: AptosConfig;\n  blockHeight: AnyNumber;\n  options?: { withTransactions?: boolean };\n}): Promise<Block> {\n  const { aptosConfig, blockHeight, options } = args;\n  const { data: block } = await getAptosFullNode<{}, Block>({\n    aptosConfig,\n    originMethod: \"getBlockByHeight\",\n    path: `blocks/by_height/${blockHeight}`,\n    params: { with_transactions: options?.withTransactions },\n  });\n  return fillBlockTransactions({ block, ...args });\n}\n\n/**\n * Fills in the block with transactions if not enough were returned. This function ensures that the block contains all relevant\n * transactions by fetching any missing ones based on the specified options.\n * @param args - The arguments for filling the block transactions.\n * @param args.aptosConfig - The configuration settings for Aptos.\n * @param args.block - The block object that will be filled with transactions.\n * @param args.options - Optional settings for fetching transactions.\n * @param args.options.withTransactions - Indicates whether to include transactions in the block.\n * @group Implementation\n */\nasync function fillBlockTransactions(args: {\n  aptosConfig: AptosConfig;\n  block: Block;\n  options?: { withTransactions?: boolean };\n}) {\n  const { aptosConfig, block, options } = args;\n  if (options?.withTransactions) {\n    // Transactions should be filled, but this ensures it\n    block.transactions = block.transactions ?? [];\n\n    const lastTxn = block.transactions[block.transactions.length - 1];\n    const firstVersion = BigInt(block.first_version);\n    const lastVersion = BigInt(block.last_version);\n\n    // Convert the transaction to the type\n    const curVersion: string | undefined = (lastTxn as any)?.version;\n    let latestVersion;\n\n    // This time, if we don't have any transactions, we will try once with the start of the block\n    if (curVersion === undefined) {\n      latestVersion = firstVersion - 1n;\n    } else {\n      latestVersion = BigInt(curVersion);\n    }\n\n    // If we have all the transactions in the block, we can skip out, otherwise we need to fill the transactions\n    if (latestVersion === lastVersion) {\n      return block;\n    }\n\n    // For now, we will grab all the transactions in groups of 100, but we can make this more efficient by trying larger\n    // amounts\n    const fetchFutures = [];\n    const pageSize = 100n;\n    for (let i = latestVersion + 1n; i < lastVersion; i += BigInt(100)) {\n      fetchFutures.push(\n        getTransactions({\n          aptosConfig,\n          options: {\n            offset: i,\n            limit: Math.min(Number(pageSize), Number(lastVersion - i + 1n)),\n          },\n        }),\n      );\n    }\n\n    // Combine all the futures\n    const responses = await Promise.all(fetchFutures);\n    for (const txns of responses) {\n      block.transactions.push(...txns);\n    }\n  }\n\n  return block;\n}\n"], "mappings": "oQAwCA,eAAsBA,EAAgBC,EAGH,CACjC,GAAM,CAAE,YAAAC,EAAa,QAAAC,CAAQ,EAAIF,EACjC,OAAOG,EAA8C,CACnD,YAAAF,EACA,aAAc,kBACd,KAAM,eACN,OAAQ,CAAE,MAAOC,GAAS,OAAQ,MAAOA,GAAS,KAAM,CAC1D,CAAC,CACH,CAUA,eAAsBE,EAAsBJ,EAAoC,CAC9E,GAAM,CAAE,YAAAC,CAAY,EAAID,EAExB,OAAOK,EACL,SAAY,CACV,GAAM,CAAE,KAAAC,CAAK,EAAI,MAAMC,EAAoC,CACzD,YAAAN,EACA,aAAc,wBACd,KAAM,oBACR,CAAC,EACD,OAAOK,CACT,EACA,aAAaL,EAAY,OAAO,GAChC,IAAO,GAAK,CACd,EAAE,CACJ,CAWA,eAAsBO,EAAwBR,EAGb,CAC/B,GAAM,CAAE,YAAAC,EAAa,cAAAQ,CAAc,EAAIT,EACjC,CAAE,KAAAM,CAAK,EAAI,MAAMC,EAA0C,CAC/D,YAAAN,EACA,aAAc,0BACd,KAAM,2BAA2BQ,CAAa,EAChD,CAAC,EACD,OAAOH,CACT,CAWA,eAAsBI,EAAqBV,EAGV,CAC/B,GAAM,CAAE,YAAAC,EAAa,gBAAAU,CAAgB,EAAIX,EACnC,CAAE,KAAAM,CAAK,EAAI,MAAMC,EAA0C,CAC/D,YAAAN,EACA,KAAM,wBAAwBU,CAAe,GAC7C,aAAc,sBAChB,CAAC,EACD,OAAOL,CACT,CAaA,eAAsBM,EAAqBZ,EAGtB,CACnB,GAAM,CAAE,YAAAC,EAAa,gBAAAU,CAAgB,EAAIX,EACzC,GAAI,CAEF,OADoB,MAAMU,EAAqB,CAAE,YAAAT,EAAa,gBAAAU,CAAgB,CAAC,GAC5D,OAAS,qBAC9B,OAASE,EAAQ,CACf,GAAIA,GAAG,SAAW,IAChB,MAAO,GAET,MAAMA,CACR,CACF,CAWA,eAAsBC,EAAuBd,EAGZ,CAC/B,GAAM,CAAE,YAAAC,EAAa,gBAAAU,CAAgB,EAAIX,EACnC,CAAE,KAAAM,CAAK,EAAI,MAAMC,EAA0C,CAC/D,YAAAN,EACA,KAAM,6BAA6BU,CAAe,GAClD,aAAc,wBAChB,CAAC,EACD,OAAOL,CACT,CAiBA,eAAsBS,EAAmBf,EAIC,CACxC,GAAM,CAAE,YAAAC,EAAa,gBAAAU,EAAiB,QAAAT,CAAQ,EAAIF,EAC5CgB,EAAcd,GAAS,aAAe,GACtCe,EAAef,GAAS,cAAgB,GAE1CgB,EAAY,GACZC,EAAc,EACdC,EACAC,EACAC,EAAoB,IAClBC,EAAoB,IAS1B,SAASC,EAAeX,EAAQ,CAQ9B,GALI,EADoBA,aAAaY,KAIrCJ,EAAYR,EACWA,EAAE,SAAW,KAAOA,EAAE,QAAU,KAAOA,EAAE,OAAS,KAEvE,MAAMA,CAEV,CAGA,GAAI,CACFO,EAAU,MAAMV,EAAqB,CAAE,YAAAT,EAAa,gBAAAU,CAAgB,CAAC,EACrEO,EAAYE,EAAQ,OAAS,qBAC/B,OAASP,EAAG,CACVW,EAAeX,CAAC,CAClB,CAGA,GAAIK,EAAW,CACb,IAAMQ,EAAY,KAAK,IAAI,EAC3B,GAAI,CACFN,EAAU,MAAMN,EAAuB,CAAE,YAAAb,EAAa,gBAAAU,CAAgB,CAAC,EACvEO,EAAYE,EAAQ,OAAS,qBAC/B,OAASP,EAAG,CACVW,EAAeX,CAAC,CAClB,CACAM,GAAe,KAAK,IAAI,EAAIO,GAAa,GAC3C,CAGA,KAAOR,GACD,EAAAC,GAAeH,IADH,CAIhB,GAAI,CAMF,GAJAI,EAAU,MAAMV,EAAqB,CAAE,YAAAT,EAAa,gBAAAU,CAAgB,CAAC,EAErEO,EAAYE,EAAQ,OAAS,sBAEzB,CAACF,EACH,KAEJ,OAASL,EAAG,CACVW,EAAeX,CAAC,CAClB,CAEA,MAAMc,EAAML,CAAiB,EAC7BH,GAAeG,EAAoB,IACnCA,GAAqBC,CACvB,CAGA,GAAIH,IAAY,OACd,MAAIC,GAGI,IAAIO,EACR,wBAAwBjB,CAAe,+BAA+BK,CAAW,WACjFI,CACF,EAIJ,GAAIA,EAAQ,OAAS,sBACnB,MAAM,IAAIQ,EACR,eAAejB,CAAe,qCAAqCK,CAAW,WAC9EI,CACF,EAEF,GAAI,CAACH,EACH,OAAOG,EAET,GAAI,CAACA,EAAQ,QACX,MAAM,IAAIS,EACR,eAAelB,CAAe,0BAA0BS,EAAQ,SAAS,GACzEA,CACF,EAGF,OAAOA,CACT,CAWA,eAAsBU,EAAe9B,EAInB,CAChB,GAAM,CAAE,YAAAC,EAAa,cAAA8B,CAAc,EAAI/B,EACjCgC,EAAuB,OAAOhC,EAAK,oBAAoB,EACvDiC,EAAsB,IACtBP,EAAY,IAAI,KAAK,EAAE,QAAQ,EACjCQ,EAAiB,OAAO,EAAE,EAE9B,KAAOA,EAAiBF,GAAsB,CAE5C,GAAI,IAAI,KAAK,EAAE,QAAQ,EAAIN,EAAYO,EACrC,MAAM,IAAI,MAAM,8CAA8C,EAchE,GAXIF,IAAkB,OAGpBG,EAAiB,MAAMC,EAA6B,CAAE,YAAAlC,CAAY,CAAC,EAKnEiC,GADkB,MAAME,EAAmB,CAAE,YAAAnC,EAAa,cAAA8B,CAAc,CAAC,GAC9C,qBAGzBG,GAAkBF,EAEpB,MAIF,MAAML,EAAM,GAAG,CACjB,CACF,CAWO,IAAMC,EAAN,cAAsC,KAAM,CAUjD,YAAYS,EAAiBC,EAA2D,CACtF,MAAMD,CAAO,EACb,KAAK,yBAA2BC,CAClC,CACF,EAUaT,EAAN,cAAqC,KAAM,CAGhD,YAAYQ,EAAiBE,EAAkC,CAC7D,MAAMF,CAAO,EACb,KAAK,YAAcE,CACrB,CACF,EAaA,eAAsBC,EAAkBxC,EAIrB,CACjB,GAAM,CAAE,YAAAC,EAAa,cAAAQ,EAAe,QAAAP,CAAQ,EAAIF,EAC1C,CAAE,KAAMyC,CAAM,EAAI,MAAMlC,EAA4B,CACxD,YAAAN,EACA,aAAc,oBACd,KAAM,qBAAqBQ,CAAa,GACxC,OAAQ,CAAE,kBAAmBP,GAAS,gBAAiB,CACzD,CAAC,EAED,OAAOwC,EAAsB,CAAE,MAAAD,EAAO,GAAGzC,CAAK,CAAC,CACjD,CAaA,eAAsB2C,EAAiB3C,EAIpB,CACjB,GAAM,CAAE,YAAAC,EAAa,YAAA2C,EAAa,QAAA1C,CAAQ,EAAIF,EACxC,CAAE,KAAMyC,CAAM,EAAI,MAAMlC,EAA4B,CACxD,YAAAN,EACA,aAAc,mBACd,KAAM,oBAAoB2C,CAAW,GACrC,OAAQ,CAAE,kBAAmB1C,GAAS,gBAAiB,CACzD,CAAC,EACD,OAAOwC,EAAsB,CAAE,MAAAD,EAAO,GAAGzC,CAAK,CAAC,CACjD,CAYA,eAAe0C,EAAsB1C,EAIlC,CACD,GAAM,CAAE,YAAAC,EAAa,MAAAwC,EAAO,QAAAvC,CAAQ,EAAIF,EACxC,GAAIE,GAAS,iBAAkB,CAE7BuC,EAAM,aAAeA,EAAM,cAAgB,CAAC,EAE5C,IAAMrB,EAAUqB,EAAM,aAAaA,EAAM,aAAa,OAAS,CAAC,EAC1DI,EAAe,OAAOJ,EAAM,aAAa,EACzCK,EAAc,OAAOL,EAAM,YAAY,EAGvCM,EAAkC3B,GAAiB,QACrD4B,EAUJ,GAPID,IAAe,OACjBC,EAAgBH,EAAe,GAE/BG,EAAgB,OAAOD,CAAU,EAI/BC,IAAkBF,EACpB,OAAOL,EAKT,IAAMQ,EAAe,CAAC,EAChBC,EAAW,KACjB,QAASC,EAAIH,EAAgB,GAAIG,EAAIL,EAAaK,GAAK,OAAO,GAAG,EAC/DF,EAAa,KACXlD,EAAgB,CACd,YAAAE,EACA,QAAS,CACP,OAAQkD,EACR,MAAO,KAAK,IAAI,OAAOD,CAAQ,EAAG,OAAOJ,EAAcK,EAAI,EAAE,CAAC,CAChE,CACF,CAAC,CACH,EAIF,IAAMC,EAAY,MAAM,QAAQ,IAAIH,CAAY,EAChD,QAAWI,KAAQD,EACjBX,EAAM,aAAa,KAAK,GAAGY,CAAI,CAEnC,CAEA,OAAOZ,CACT", "names": ["getTransactions", "args", "aptosConfig", "options", "paginateWithCursor", "getGasPriceEstimation", "memoizeAsync", "data", "getAptosFullNode", "getTransactionByVersion", "ledgerVersion", "getTransactionByHash", "transactionHash", "isTransactionPending", "e", "longWaitForTransaction", "waitForTransaction", "timeoutSecs", "checkSuccess", "isPending", "timeElapsed", "lastTxn", "lastError", "backoffIntervalMs", "backoffMultiplier", "handleAPIError", "AptosApiError", "startTime", "sleep", "WaitForTransactionError", "FailedTransactionError", "waitForIndexer", "processorType", "minimumLedgerVersion", "timeoutMilliseconds", "indexerVersion", "getIndexerLastSuccessVersion", "getProcessorStatus", "message", "lastSubmittedTransaction", "transaction", "getBlockByVersion", "block", "fillBlockTransactions", "getBlockByHeight", "blockHeight", "firstVersion", "lastVersion", "curVersion", "latestVersion", "fetchFutures", "pageSize", "i", "responses", "txns"]}