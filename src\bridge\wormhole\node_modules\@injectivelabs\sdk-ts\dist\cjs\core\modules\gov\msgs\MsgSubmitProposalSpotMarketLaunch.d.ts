import { SnakeCaseKeys } from 'snakecase-keys';
import { GoogleProtobufAny, CosmosGovV1Beta1Tx, CosmosBaseV1Beta1Coin } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgSubmitProposalSpotMarketLaunch {
    interface Params {
        market: {
            title: string;
            description: string;
            ticker: string;
            baseDenom: string;
            quoteDenom: string;
            minPriceTickSize: string;
            minQuantityTickSize: string;
            makerFeeRate: string;
            takerFeeRate: string;
            minNotional: string;
            adminInfo?: {
                admin: string;
                adminPermissions: number;
            };
            baseDecimals: number;
            quoteDecimals: number;
        };
        proposer: string;
        deposit: {
            amount: string;
            denom: string;
        };
    }
    type Proto = CosmosGovV1Beta1Tx.MsgSubmitProposal;
    type Object = Omit<CosmosGovV1Beta1Tx.MsgSubmitProposal, 'content'> & {
        content: {
            type_url: string;
            value: any;
        };
    };
}
/**
 * @category Messages
 */
export default class MsgSubmitProposalSpotMarketLaunch extends MsgBase<MsgSubmitProposalSpotMarketLaunch.Params, MsgSubmitProposalSpotMarketLaunch.Proto, MsgSubmitProposalSpotMarketLaunch.Object> {
    static fromJSON(params: MsgSubmitProposalSpotMarketLaunch.Params): MsgSubmitProposalSpotMarketLaunch;
    toProto(): MsgSubmitProposalSpotMarketLaunch.Proto;
    toData(): {
        content: GoogleProtobufAny.Any | undefined;
        initialDeposit: CosmosBaseV1Beta1Coin.Coin[];
        proposer: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<MsgSubmitProposalSpotMarketLaunch.Object>;
    };
    toWeb3Gw(): {
        initial_deposit: {
            denom: string;
            amount: string;
        }[];
        proposer: string;
        content: {
            type_url: string;
            value: any;
        };
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: SnakeCaseKeys<MsgSubmitProposalSpotMarketLaunch.Object>;
    };
    toEip712V2(): {
        content: any;
        initial_deposit: {
            denom: string;
            amount: string;
        }[];
        proposer: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosGovV1Beta1Tx.MsgSubmitProposal;
    };
    toBinary(): Uint8Array;
}
