{"version": 3, "file": "graphqlsp.js", "sources": ["../src/graphql/getSchema.ts", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/autocompleteUtils.js", "../../../node_modules/.pnpm/vscode-languageserver-types@3.17.2/node_modules/vscode-languageserver-types/lib/esm/main.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/types.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/types.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getAutocompleteSuggestions.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getHoverInformation.js", "../src/ast/cursor.ts", "../src/ast/token.ts", "../src/graphql/getFragmentSpreadSuggestions.ts", "../src/autoComplete.ts", "../src/index.ts", "../src/quickInfo.ts"], "sourcesContent": ["import type { Stats, PathLike } from 'node:fs';\nimport fs from 'node:fs/promises';\nimport path from 'path';\n\nimport type { IntrospectionQuery } from 'graphql';\n\nimport {\n  type SchemaLoaderResult,\n  type SchemaRef as _SchemaRef,\n  type GraphQLSPConfig,\n  loadRef,\n  minifyIntrospection,\n  outputIntrospectionFile,\n  resolveTypeScriptRootDir,\n} from '@gql.tada/internal';\n\nimport { ts } from '../ts';\nimport { Logger } from '../index';\n\nconst statFile = (\n  file: PathLike,\n  predicate: (stat: Stats) => boolean\n): Promise<boolean> => {\n  return fs\n    .stat(file)\n    .then(predicate)\n    .catch(() => false);\n};\n\nconst touchFile = async (file: PathLike): Promise<void> => {\n  try {\n    const now = new Date();\n    await fs.utimes(file, now, now);\n  } catch (_error) {}\n};\n\n/** Writes a file to a swapfile then moves it into place to prevent excess change events. */\nexport const swapWrite = async (\n  target: PathLike,\n  contents: string\n): Promise<void> => {\n  if (!(await statFile(target, stat => stat.isFile()))) {\n    // If the file doesn't exist, we can write directly, and not\n    // try-catch so the error falls through\n    await fs.writeFile(target, contents);\n  } else {\n    // If the file exists, we write to a swap-file, then rename (i.e. move)\n    // the file into place. No try-catch around `writeFile` for proper\n    // directory/permission errors\n    const tempTarget = target + '.tmp';\n    await fs.writeFile(tempTarget, contents);\n    try {\n      await fs.rename(tempTarget, target);\n    } catch (error) {\n      await fs.unlink(tempTarget);\n      throw error;\n    } finally {\n      // When we move the file into place, we also update its access and\n      // modification time manually, in case the rename doesn't trigger\n      // a change event\n      await touchFile(target);\n    }\n  }\n};\n\nasync function saveTadaIntrospection(\n  introspection: IntrospectionQuery,\n  tadaOutputLocation: string,\n  disablePreprocessing: boolean,\n  logger: Logger\n) {\n  const minified = minifyIntrospection(introspection);\n  const contents = outputIntrospectionFile(minified, {\n    fileType: tadaOutputLocation,\n    shouldPreprocess: !disablePreprocessing,\n  });\n\n  let output = tadaOutputLocation;\n\n  if (await statFile(output, stat => stat.isDirectory())) {\n    output = path.join(output, 'introspection.d.ts');\n  } else if (\n    !(await statFile(path.dirname(output), stat => stat.isDirectory()))\n  ) {\n    logger(`Output file is not inside a directory @ ${output}`);\n    return;\n  }\n\n  try {\n    await swapWrite(output, contents);\n    logger(`Introspection saved to path @ ${output}`);\n  } catch (error) {\n    logger(`Failed to write introspection @ ${error}`);\n  }\n}\n\nexport type SchemaRef = _SchemaRef<SchemaLoaderResult | null>;\n\nexport const loadSchema = (\n  // TODO: abstract info away\n  info: ts.server.PluginCreateInfo,\n  origin: GraphQLSPConfig,\n  logger: Logger\n): _SchemaRef<SchemaLoaderResult | null> => {\n  const ref = loadRef(origin);\n\n  (async () => {\n    const rootPath =\n      (await resolveTypeScriptRootDir(info.project.getProjectName())) ||\n      path.dirname(info.project.getProjectName());\n\n    const tadaDisablePreprocessing =\n      info.config.tadaDisablePreprocessing ?? false;\n    const tadaOutputLocation =\n      info.config.tadaOutputLocation &&\n      path.resolve(rootPath, info.config.tadaOutputLocation);\n\n    logger('Got root-directory to resolve schema from: ' + rootPath);\n    logger('Resolving schema from \"schema\" config: ' + JSON.stringify(origin));\n\n    try {\n      logger(`Loading schema...`);\n      await ref.load({ rootPath });\n    } catch (error) {\n      logger(`Failed to load schema: ${error}`);\n    }\n\n    if (ref.current) {\n      if (ref.current && ref.current.tadaOutputLocation !== undefined) {\n        saveTadaIntrospection(\n          ref.current.introspection,\n          tadaOutputLocation,\n          tadaDisablePreprocessing,\n          logger\n        );\n      }\n    } else if (ref.multi) {\n      Object.values(ref.multi).forEach(value => {\n        if (!value) return;\n\n        if (value.tadaOutputLocation) {\n          saveTadaIntrospection(\n            value.introspection,\n            path.resolve(rootPath, value.tadaOutputLocation),\n            tadaDisablePreprocessing,\n            logger\n          );\n        }\n      });\n    }\n\n    ref.autoupdate({ rootPath }, (schemaRef, value) => {\n      if (!value) return;\n\n      if (value.tadaOutputLocation) {\n        const found = schemaRef.multi\n          ? schemaRef.multi[value.name as string]\n          : schemaRef.current;\n        if (!found) return;\n        saveTadaIntrospection(\n          found.introspection,\n          path.resolve(rootPath, value.tadaOutputLocation),\n          tadaDisablePreprocessing,\n          logger\n        );\n      }\n    });\n  })();\n\n  return ref as any;\n};\n", "import { isCompositeType, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, } from 'graphql';\nexport function getDefinitionState(tokenState) {\n    let definitionState;\n    forEachState(tokenState, (state) => {\n        switch (state.kind) {\n            case 'Query':\n            case 'ShortQuery':\n            case 'Mutation':\n            case 'Subscription':\n            case 'FragmentDefinition':\n                definitionState = state;\n                break;\n        }\n    });\n    return definitionState;\n}\nexport function getFieldDef(schema, type, fieldName) {\n    if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === type) {\n        return SchemaMetaFieldDef;\n    }\n    if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === type) {\n        return TypeMetaFieldDef;\n    }\n    if (fieldName === TypeNameMetaFieldDef.name && isCompositeType(type)) {\n        return TypeNameMetaFieldDef;\n    }\n    if ('getFields' in type) {\n        return type.getFields()[fieldName];\n    }\n    return null;\n}\nexport function forEachState(stack, fn) {\n    const reverseStateStack = [];\n    let state = stack;\n    while (state === null || state === void 0 ? void 0 : state.kind) {\n        reverseStateStack.push(state);\n        state = state.prevState;\n    }\n    for (let i = reverseStateStack.length - 1; i >= 0; i--) {\n        fn(reverseStateStack[i]);\n    }\n}\nexport function objectValues(object) {\n    const keys = Object.keys(object);\n    const len = keys.length;\n    const values = new Array(len);\n    for (let i = 0; i < len; ++i) {\n        values[i] = object[keys[i]];\n    }\n    return values;\n}\nexport function hintList(token, list) {\n    return filterAndSortList(list, normalizeText(token.string));\n}\nfunction filterAndSortList(list, text) {\n    if (!text) {\n        return filterNonEmpty(list, entry => !entry.isDeprecated);\n    }\n    const byProximity = list.map(entry => ({\n        proximity: getProximity(normalizeText(entry.label), text),\n        entry,\n    }));\n    return filterNonEmpty(filterNonEmpty(byProximity, pair => pair.proximity <= 2), pair => !pair.entry.isDeprecated)\n        .sort((a, b) => (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.label.length - b.entry.label.length)\n        .map(pair => pair.entry);\n}\nfunction filterNonEmpty(array, predicate) {\n    const filtered = array.filter(predicate);\n    return filtered.length === 0 ? array : filtered;\n}\nfunction normalizeText(text) {\n    return text.toLowerCase().replaceAll(/\\W/g, '');\n}\nfunction getProximity(suggestion, text) {\n    let proximity = lexicalDistance(text, suggestion);\n    if (suggestion.length > text.length) {\n        proximity -= suggestion.length - text.length - 1;\n        proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n    }\n    return proximity;\n}\nfunction lexicalDistance(a, b) {\n    let i;\n    let j;\n    const d = [];\n    const aLength = a.length;\n    const bLength = b.length;\n    for (i = 0; i <= aLength; i++) {\n        d[i] = [i];\n    }\n    for (j = 1; j <= bLength; j++) {\n        d[0][j] = j;\n    }\n    for (i = 1; i <= aLength; i++) {\n        for (j = 1; j <= bLength; j++) {\n            const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n            d[i][j] = Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);\n            if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n                d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);\n            }\n        }\n    }\n    return d[aLength][bLength];\n}\n//# sourceMappingURL=autocompleteUtils.js.map", "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var DocumentUri;\n(function (DocumentUri) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    DocumentUri.is = is;\n})(DocumentUri || (DocumentUri = {}));\nexport var URI;\n(function (URI) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    URI.is = is;\n})(URI || (URI = {}));\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;\n    }\n    integer.is = is;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;\n    }\n    uinteger.is = is;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * [Position](#Position) literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line: line, character: character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Position](#Position) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * [Range](#Range) literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(\"Range#create called with invalid arguments[\".concat(one, \", \").concat(two, \", \").concat(three, \", \").concat(four, \"]\"));\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Range](#Range) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * [Location](#Location) literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri: uri, range: range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Location](#Location) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * [LocationLink](#LocationLink) literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri: targetUri, targetRange: targetRange, targetSelectionRange: targetSelectionRange, originSelectionRange: originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [LocationLink](#LocationLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && Range.is(candidate.targetSelectionRange)\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [Color](#Color) literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Color](#Color) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * [ColorInformation](#ColorInformation) literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range: range,\n            color: color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [ColorPresentation](#ColorPresentation) literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label: label,\n            textEdit: textEdit,\n            additionalTextEdits: additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * A set of predefined range kinds.\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind.Comment = 'comment';\n    /**\n     * Folding range for an import or include\n     */\n    FoldingRangeKind.Imports = 'imports';\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind.Region = 'region';\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * [FoldingRange](#FoldingRange) literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n        var result = {\n            startLine: startLine,\n            endLine: endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        if (Is.defined(collapsedText)) {\n            result.collapsedText = collapsedText;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FoldingRange](#FoldingRange) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location: location,\n            message: message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * [Diagnostic](#Diagnostic) literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        var result = { range: range, message: message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Diagnostic](#Diagnostic) interface.\n     */\n    function is(value) {\n        var _a;\n        var candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * [Command](#Command) literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var result = { title: title, command: command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Command](#Command) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range: range, newText: newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates an insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText: newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range: range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        var result = { label: label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate);\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range: range, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range: range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument: textDocument, edits: edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'create',\n            uri: uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        var result = {\n            kind: 'rename',\n            oldUri: oldUri,\n            newUri: newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'delete',\n            uri: uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every(function (change) {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = /** @class */ (function () {\n    function TextEditChangeImpl(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    TextEditChangeImpl.prototype.insert = function (position, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.replace = function (range, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.delete = function (range, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.add = function (edit) {\n        this.edits.push(edit);\n    };\n    TextEditChangeImpl.prototype.all = function () {\n        return this.edits;\n    };\n    TextEditChangeImpl.prototype.clear = function () {\n        this.edits.splice(0, this.edits.length);\n    };\n    TextEditChangeImpl.prototype.assertChangeAnnotations = function (value) {\n        if (value === undefined) {\n            throw new Error(\"Text edit change is not configured to manage change annotations.\");\n        }\n    };\n    return TextEditChangeImpl;\n}());\n/**\n * A helper class\n */\nvar ChangeAnnotations = /** @class */ (function () {\n    function ChangeAnnotations(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    ChangeAnnotations.prototype.all = function () {\n        return this._annotations;\n    };\n    Object.defineProperty(ChangeAnnotations.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ChangeAnnotations.prototype.manage = function (idOrAnnotation, annotation) {\n        var id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(\"Id \".concat(id, \" is already in use.\"));\n        }\n        if (annotation === undefined) {\n            throw new Error(\"No annotation provided for id \".concat(id));\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    };\n    ChangeAnnotations.prototype.nextId = function () {\n        this._counter++;\n        return this._counter.toString();\n    };\n    return ChangeAnnotations;\n}());\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nvar WorkspaceChange = /** @class */ (function () {\n    function WorkspaceChange(workspaceEdit) {\n        var _this = this;\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach(function (change) {\n                    if (TextDocumentEdit.is(change)) {\n                        var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n                        _this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach(function (key) {\n                    var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    _this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    Object.defineProperty(WorkspaceChange.prototype, \"edit\", {\n        /**\n         * Returns the underlying [WorkspaceEdit](#WorkspaceEdit) literal\n         * use to be returned from a workspace edit operation like rename.\n         */\n        get: function () {\n            this.initDocumentChanges();\n            if (this._changeAnnotations !== undefined) {\n                if (this._changeAnnotations.size === 0) {\n                    this._workspaceEdit.changeAnnotations = undefined;\n                }\n                else {\n                    this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                }\n            }\n            return this._workspaceEdit;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WorkspaceChange.prototype.getTextEditChange = function (key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            var textDocument = { uri: key.uri, version: key.version };\n            var result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                var edits = [];\n                var textDocumentEdit = {\n                    textDocument: textDocument,\n                    edits: edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            var result = this._textEditChanges[key];\n            if (!result) {\n                var edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    };\n    WorkspaceChange.prototype.initDocumentChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    };\n    WorkspaceChange.prototype.initChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    };\n    WorkspaceChange.prototype.createFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.renameFile = function (oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.deleteFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    return WorkspaceChange;\n}());\nexport { WorkspaceChange };\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * [TextDocumentIdentifier](#TextDocumentIdentifier) literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri: uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentIdentifier](#TextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * [TextDocumentItem](#TextDocumentItem) literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri: uri, languageId: languageId, version: version, text: text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentItem](#TextDocumentItem) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n    /**\n     * Checks whether the given value is a value of the [MarkupKind](#MarkupKind) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the [MarkupContent](#MarkupContent) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText: newText, insert: insert, replace: replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InsertReplaceEdit](#InsertReplaceEdit) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nexport var CompletionItemLabelDetails;\n(function (CompletionItemLabelDetails) {\n    function is(value) {\n        var candidate = value;\n        return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    CompletionItemLabelDetails.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label: label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the [MarkedString](#MarkedString) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the [Hover](#Hover) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * [ParameterInformation](#ParameterInformation) literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label: label, documentation: documentation } : { label: label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * [SignatureInformation](#SignatureInformation) literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation) {\n        var parameters = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            parameters[_i - 2] = arguments[_i];\n        }\n        var result = { label: label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * [DocumentHighlight](#DocumentHighlight) literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     * @param kind The highlight kind\n     */\n    function create(range, kind) {\n        var result = { range: range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n *\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        var result = {\n            name: name,\n            kind: kind,\n            location: { uri: uri, range: range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var WorkspaceSymbol;\n(function (WorkspaceSymbol) {\n    /**\n     * Create a new workspace symbol.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param uri The resource of the location of the symbol.\n     * @param range An options range of the location.\n     * @returns A WorkspaceSymbol.\n     */\n    function create(name, kind, uri, range) {\n        return range !== undefined\n            ? { name: name, kind: kind, location: { uri: uri, range: range } }\n            : { name: name, kind: kind, location: { uri: uri } };\n    }\n    WorkspaceSymbol.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        var result = {\n            name: name,\n            detail: detail,\n            kind: kind,\n            range: range,\n            selectionRange: selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentSymbol](#DocumentSymbol) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The reason why code actions were requested.\n *\n * @since 3.17.0\n */\nexport var CodeActionTriggerKind;\n(function (CodeActionTriggerKind) {\n    /**\n     * Code actions were explicitly requested by the user or by an extension.\n     */\n    CodeActionTriggerKind.Invoked = 1;\n    /**\n     * Code actions were requested automatically.\n     *\n     * This typically happens when current selection in a file changes, but can\n     * also be triggered when file content changes.\n     */\n    CodeActionTriggerKind.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * [CodeActionContext](#CodeActionContext) literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only, triggerKind) {\n        var result = { diagnostics: diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        if (triggerKind !== undefined && triggerKind !== null) {\n            result.triggerKind = triggerKind;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeActionContext](#CodeActionContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is)\n            && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string))\n            && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        var result = { title: title };\n        var checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * [CodeLens](#CodeLens) literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        var result = { range: range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeLens](#CodeLens) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * [FormattingOptions](#FormattingOptions) literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize: tabSize, insertSpaces: insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FormattingOptions](#FormattingOptions) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * [DocumentLink](#DocumentLink) literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range: range, target: target, data: data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentLink](#DocumentLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range: range, parent: parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\n/**\n * A set of predefined token types. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenTypes;\n(function (SemanticTokenTypes) {\n    SemanticTokenTypes[\"namespace\"] = \"namespace\";\n    /**\n     * Represents a generic type. Acts as a fallback for types which can't be mapped to\n     * a specific type like class or enum.\n     */\n    SemanticTokenTypes[\"type\"] = \"type\";\n    SemanticTokenTypes[\"class\"] = \"class\";\n    SemanticTokenTypes[\"enum\"] = \"enum\";\n    SemanticTokenTypes[\"interface\"] = \"interface\";\n    SemanticTokenTypes[\"struct\"] = \"struct\";\n    SemanticTokenTypes[\"typeParameter\"] = \"typeParameter\";\n    SemanticTokenTypes[\"parameter\"] = \"parameter\";\n    SemanticTokenTypes[\"variable\"] = \"variable\";\n    SemanticTokenTypes[\"property\"] = \"property\";\n    SemanticTokenTypes[\"enumMember\"] = \"enumMember\";\n    SemanticTokenTypes[\"event\"] = \"event\";\n    SemanticTokenTypes[\"function\"] = \"function\";\n    SemanticTokenTypes[\"method\"] = \"method\";\n    SemanticTokenTypes[\"macro\"] = \"macro\";\n    SemanticTokenTypes[\"keyword\"] = \"keyword\";\n    SemanticTokenTypes[\"modifier\"] = \"modifier\";\n    SemanticTokenTypes[\"comment\"] = \"comment\";\n    SemanticTokenTypes[\"string\"] = \"string\";\n    SemanticTokenTypes[\"number\"] = \"number\";\n    SemanticTokenTypes[\"regexp\"] = \"regexp\";\n    SemanticTokenTypes[\"operator\"] = \"operator\";\n    /**\n     * @since 3.17.0\n     */\n    SemanticTokenTypes[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\n/**\n * A set of predefined token modifiers. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenModifiers;\n(function (SemanticTokenModifiers) {\n    SemanticTokenModifiers[\"declaration\"] = \"declaration\";\n    SemanticTokenModifiers[\"definition\"] = \"definition\";\n    SemanticTokenModifiers[\"readonly\"] = \"readonly\";\n    SemanticTokenModifiers[\"static\"] = \"static\";\n    SemanticTokenModifiers[\"deprecated\"] = \"deprecated\";\n    SemanticTokenModifiers[\"abstract\"] = \"abstract\";\n    SemanticTokenModifiers[\"async\"] = \"async\";\n    SemanticTokenModifiers[\"modification\"] = \"modification\";\n    SemanticTokenModifiers[\"documentation\"] = \"documentation\";\n    SemanticTokenModifiers[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\n/**\n * @since 3.16.0\n */\nexport var SemanticTokens;\n(function (SemanticTokens) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') &&\n            Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');\n    }\n    SemanticTokens.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\n/**\n * The InlineValueText namespace provides functions to deal with InlineValueTexts.\n *\n * @since 3.17.0\n */\nexport var InlineValueText;\n(function (InlineValueText) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, text) {\n        return { range: range, text: text };\n    }\n    InlineValueText.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n    }\n    InlineValueText.is = is;\n})(InlineValueText || (InlineValueText = {}));\n/**\n * The InlineValueVariableLookup namespace provides functions to deal with InlineValueVariableLookups.\n *\n * @since 3.17.0\n */\nexport var InlineValueVariableLookup;\n(function (InlineValueVariableLookup) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, variableName, caseSensitiveLookup) {\n        return { range: range, variableName: variableName, caseSensitiveLookup: caseSensitiveLookup };\n    }\n    InlineValueVariableLookup.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup)\n            && (Is.string(candidate.variableName) || candidate.variableName === undefined);\n    }\n    InlineValueVariableLookup.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\n/**\n * The InlineValueEvaluatableExpression namespace provides functions to deal with InlineValueEvaluatableExpression.\n *\n * @since 3.17.0\n */\nexport var InlineValueEvaluatableExpression;\n(function (InlineValueEvaluatableExpression) {\n    /**\n     * Creates a new InlineValueEvaluatableExpression literal.\n     */\n    function create(range, expression) {\n        return { range: range, expression: expression };\n    }\n    InlineValueEvaluatableExpression.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range)\n            && (Is.string(candidate.expression) || candidate.expression === undefined);\n    }\n    InlineValueEvaluatableExpression.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\n/**\n * The InlineValueContext namespace provides helper functions to work with\n * [InlineValueContext](#InlineValueContext) literals.\n *\n * @since 3.17.0\n */\nexport var InlineValueContext;\n(function (InlineValueContext) {\n    /**\n     * Creates a new InlineValueContext literal.\n     */\n    function create(frameId, stoppedLocation) {\n        return { frameId: frameId, stoppedLocation: stoppedLocation };\n    }\n    InlineValueContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InlineValueContext](#InlineValueContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(value.stoppedLocation);\n    }\n    InlineValueContext.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\n/**\n * Inlay hint kinds.\n *\n * @since 3.17.0\n */\nexport var InlayHintKind;\n(function (InlayHintKind) {\n    /**\n     * An inlay hint that for a type annotation.\n     */\n    InlayHintKind.Type = 1;\n    /**\n     * An inlay hint that is for a parameter.\n     */\n    InlayHintKind.Parameter = 2;\n    function is(value) {\n        return value === 1 || value === 2;\n    }\n    InlayHintKind.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nexport var InlayHintLabelPart;\n(function (InlayHintLabelPart) {\n    function create(value) {\n        return { value: value };\n    }\n    InlayHintLabelPart.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.location === undefined || Location.is(candidate.location))\n            && (candidate.command === undefined || Command.is(candidate.command));\n    }\n    InlayHintLabelPart.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nexport var InlayHint;\n(function (InlayHint) {\n    function create(position, label, kind) {\n        var result = { position: position, label: label };\n        if (kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    InlayHint.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.position)\n            && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is))\n            && (candidate.kind === undefined || InlayHintKind.is(candidate.kind))\n            && (candidate.textEdits === undefined) || Is.typedArray(candidate.textEdits, TextEdit.is)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft))\n            && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));\n    }\n    InlayHint.is = is;\n})(InlayHint || (InlayHint = {}));\nexport var WorkspaceFolder;\n(function (WorkspaceFolder) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n    }\n    WorkspaceFolder.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nexport var EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId The document's language Id.\n     * @param version The document's version.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ITextDocument](#ITextDocument) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        var text = document.getText();\n        var sortedEdits = mergeSort(edits, function (a, b) {\n            var diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        var lastModifiedOffset = text.length;\n        for (var i = sortedEdits.length - 1; i >= 0; i--) {\n            var e = sortedEdits[i];\n            var startOffset = document.offsetAt(e.range.start);\n            var endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        var p = (data.length / 2) | 0;\n        var left = data.slice(0, p);\n        var right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        var leftIdx = 0;\n        var rightIdx = 0;\n        var i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            var ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nvar FullTextDocument = /** @class */ (function () {\n    function FullTextDocument(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    Object.defineProperty(FullTextDocument.prototype, \"uri\", {\n        get: function () {\n            return this._uri;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"languageId\", {\n        get: function () {\n            return this._languageId;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"version\", {\n        get: function () {\n            return this._version;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FullTextDocument.prototype.getText = function (range) {\n        if (range) {\n            var start = this.offsetAt(range.start);\n            var end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    };\n    FullTextDocument.prototype.update = function (event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    };\n    FullTextDocument.prototype.getLineOffsets = function () {\n        if (this._lineOffsets === undefined) {\n            var lineOffsets = [];\n            var text = this._content;\n            var isLineStart = true;\n            for (var i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                var ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    };\n    FullTextDocument.prototype.positionAt = function (offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        var lineOffsets = this.getLineOffsets();\n        var low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            var mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        var line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    };\n    FullTextDocument.prototype.offsetAt = function (position) {\n        var lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        var lineOffset = lineOffsets[position.line];\n        var nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    };\n    Object.defineProperty(FullTextDocument.prototype, \"lineCount\", {\n        get: function () {\n            return this.getLineOffsets().length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return FullTextDocument;\n}());\nvar Is;\n(function (Is) {\n    var toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n", "export { InsertTextFormat } from 'vscode-languageserver-types';\nexport const FileChangeTypeKind = {\n    Created: 1,\n    Changed: 2,\n    Deleted: 3,\n};\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n//# sourceMappingURL=types.js.map", "import { Kind } from 'graphql';\nexport const AdditionalRuleKinds = {\n    ALIASED_FIELD: 'AliasedField',\n    ARGUMENTS: 'Arguments',\n    SHORT_QUERY: 'ShortQuery',\n    QUERY: 'Query',\n    MUTATION: 'Mutation',\n    SUBSCRIPTION: 'Subscription',\n    TYPE_CONDITION: 'TypeCondition',\n    INVALID: 'Invalid',\n    COMMENT: 'Comment',\n    SCHEMA_DEF: 'SchemaDef',\n    SCALAR_DEF: 'ScalarDef',\n    OBJECT_TYPE_DEF: 'ObjectTypeDef',\n    OBJECT_VALUE: 'ObjectValue',\n    LIST_VALUE: 'ListValue',\n    INTERFACE_DEF: 'InterfaceDef',\n    UNION_DEF: 'UnionDef',\n    ENUM_DEF: 'EnumDef',\n    ENUM_VALUE: 'EnumValue',\n    FIELD_DEF: 'FieldDef',\n    INPUT_DEF: 'InputDef',\n    INPUT_VALUE_DEF: 'InputValueDef',\n    ARGUMENTS_DEF: 'ArgumentsDef',\n    EXTEND_DEF: 'ExtendDef',\n    EXTENSION_DEFINITION: 'ExtensionDefinition',\n    DIRECTIVE_DEF: 'DirectiveDef',\n    IMPLEMENTS: 'Implements',\n    VARIABLE_DEFINITIONS: 'VariableDefinitions',\n    TYPE: 'Type',\n};\nexport const RuleKinds = Object.assign(Object.assign({}, Kind), AdditionalRuleKinds);\n//# sourceMappingURL=types.js.map", "import { isInterfaceType, GraphQLInterfaceType, GraphQLObjectType, Kind, DirectiveLocation, isListType, isNonNullType, isScalarType, isObjectType, isUnionType, isEnumType, isInputObjectType, isOutputType, GraphQLBoolean, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, assertAbstractType, doTypesOverlap, getNamedType, getNullableType, isAbstractType, isCompositeType, isInputType, visit, BREAK, parse, } from 'graphql';\nimport { CompletionItemKind, InsertTextFormat, } from '../types';\nimport { CharacterStream, onlineParser, RuleKinds, } from '../parser';\nimport { forEachState, getDefinitionState, getFieldDef, hintList, objectValues, } from './autocompleteUtils';\nexport const SuggestionCommand = {\n    command: 'editor.action.triggerSuggest',\n    title: 'Suggestions',\n};\nconst collectFragmentDefs = (op) => {\n    const externalFragments = [];\n    if (op) {\n        try {\n            visit(parse(op), {\n                FragmentDefinition(def) {\n                    externalFragments.push(def);\n                },\n            });\n        }\n        catch (_a) {\n            return [];\n        }\n    }\n    return externalFragments;\n};\nconst typeSystemKinds = [\n    Kind.SCHEMA_DEFINITION,\n    Kind.OPERATION_TYPE_DEFINITION,\n    Kind.SCALAR_TYPE_DEFINITION,\n    Kind.OBJECT_TYPE_DEFINITION,\n    Kind.INTERFACE_TYPE_DEFINITION,\n    Kind.UNION_TYPE_DEFINITION,\n    Kind.ENUM_TYPE_DEFINITION,\n    Kind.INPUT_OBJECT_TYPE_DEFINITION,\n    Kind.DIRECTIVE_DEFINITION,\n    Kind.SCHEMA_EXTENSION,\n    Kind.SCALAR_TYPE_EXTENSION,\n    Kind.OBJECT_TYPE_EXTENSION,\n    Kind.INTERFACE_TYPE_EXTENSION,\n    Kind.UNION_TYPE_EXTENSION,\n    Kind.ENUM_TYPE_EXTENSION,\n    Kind.INPUT_OBJECT_TYPE_EXTENSION,\n];\nconst hasTypeSystemDefinitions = (sdl) => {\n    let hasTypeSystemDef = false;\n    if (sdl) {\n        try {\n            visit(parse(sdl), {\n                enter(node) {\n                    if (node.kind === 'Document') {\n                        return;\n                    }\n                    if (typeSystemKinds.includes(node.kind)) {\n                        hasTypeSystemDef = true;\n                        return BREAK;\n                    }\n                    return false;\n                },\n            });\n        }\n        catch (_a) {\n            return hasTypeSystemDef;\n        }\n    }\n    return hasTypeSystemDef;\n};\nexport function getAutocompleteSuggestions(schema, queryText, cursor, contextToken, fragmentDefs, options) {\n    var _a;\n    const opts = Object.assign(Object.assign({}, options), { schema });\n    const token = contextToken || getTokenAtPosition(queryText, cursor, 1);\n    const state = token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n    const mode = (options === null || options === void 0 ? void 0 : options.mode) || getDocumentMode(queryText, options === null || options === void 0 ? void 0 : options.uri);\n    if (!state) {\n        return [];\n    }\n    const { kind, step, prevState } = state;\n    const typeInfo = getTypeInfo(schema, token.state);\n    if (kind === RuleKinds.DOCUMENT) {\n        if (mode === GraphQLDocumentMode.TYPE_SYSTEM) {\n            return getSuggestionsForTypeSystemDefinitions(token);\n        }\n        return getSuggestionsForExecutableDefinitions(token);\n    }\n    if (kind === RuleKinds.EXTEND_DEF) {\n        return getSuggestionsForExtensionDefinitions(token);\n    }\n    if (((_a = prevState === null || prevState === void 0 ? void 0 : prevState.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.EXTENSION_DEFINITION &&\n        state.name) {\n        return hintList(token, []);\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.SCALAR_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isScalarType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.OBJECT_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(type => isObjectType(type) && !type.name.startsWith('__'))\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INTERFACE_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isInterfaceType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.UNION_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isUnionType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.ENUM_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(type => isEnumType(type) && !type.name.startsWith('__'))\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INPUT_OBJECT_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isInputObjectType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if (kind === RuleKinds.IMPLEMENTS ||\n        (kind === RuleKinds.NAMED_TYPE && (prevState === null || prevState === void 0 ? void 0 : prevState.kind) === RuleKinds.IMPLEMENTS)) {\n        return getSuggestionsForImplements(token, state, schema, queryText, typeInfo);\n    }\n    if (kind === RuleKinds.SELECTION_SET ||\n        kind === RuleKinds.FIELD ||\n        kind === RuleKinds.ALIASED_FIELD) {\n        return getSuggestionsForFieldNames(token, typeInfo, opts);\n    }\n    if (kind === RuleKinds.ARGUMENTS ||\n        (kind === RuleKinds.ARGUMENT && step === 0)) {\n        const { argDefs } = typeInfo;\n        if (argDefs) {\n            return hintList(token, argDefs.map((argDef) => {\n                var _a;\n                return ({\n                    label: argDef.name,\n                    insertText: argDef.name + ': ',\n                    command: SuggestionCommand,\n                    detail: String(argDef.type),\n                    documentation: (_a = argDef.description) !== null && _a !== void 0 ? _a : undefined,\n                    kind: CompletionItemKind.Variable,\n                    type: argDef.type,\n                });\n            }));\n        }\n    }\n    if ((kind === RuleKinds.OBJECT_VALUE ||\n        (kind === RuleKinds.OBJECT_FIELD && step === 0)) &&\n        typeInfo.objectFieldDefs) {\n        const objectFields = objectValues(typeInfo.objectFieldDefs);\n        const completionKind = kind === RuleKinds.OBJECT_VALUE\n            ? CompletionItemKind.Value\n            : CompletionItemKind.Field;\n        return hintList(token, objectFields.map(field => {\n            var _a;\n            return ({\n                label: field.name,\n                detail: String(field.type),\n                documentation: (_a = field.description) !== null && _a !== void 0 ? _a : undefined,\n                kind: completionKind,\n                type: field.type,\n            });\n        }));\n    }\n    if (kind === RuleKinds.ENUM_VALUE ||\n        (kind === RuleKinds.LIST_VALUE && step === 1) ||\n        (kind === RuleKinds.OBJECT_FIELD && step === 2) ||\n        (kind === RuleKinds.ARGUMENT && step === 2)) {\n        return getSuggestionsForInputValues(token, typeInfo, queryText, schema);\n    }\n    if (kind === RuleKinds.VARIABLE && step === 1) {\n        const namedInputType = getNamedType(typeInfo.inputType);\n        const variableDefinitions = getVariableCompletions(queryText, schema, token);\n        return hintList(token, variableDefinitions.filter(v => v.detail === (namedInputType === null || namedInputType === void 0 ? void 0 : namedInputType.name)));\n    }\n    if ((kind === RuleKinds.TYPE_CONDITION && step === 1) ||\n        (kind === RuleKinds.NAMED_TYPE &&\n            prevState != null &&\n            prevState.kind === RuleKinds.TYPE_CONDITION)) {\n        return getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, kind);\n    }\n    if (kind === RuleKinds.FRAGMENT_SPREAD && step === 1) {\n        return getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, Array.isArray(fragmentDefs)\n            ? fragmentDefs\n            : collectFragmentDefs(fragmentDefs));\n    }\n    const unwrappedState = unwrapType(state);\n    if ((mode === GraphQLDocumentMode.TYPE_SYSTEM &&\n        !unwrappedState.needsAdvance &&\n        kind === RuleKinds.NAMED_TYPE) ||\n        kind === RuleKinds.LIST_TYPE) {\n        if (unwrappedState.kind === RuleKinds.FIELD_DEF) {\n            return hintList(token, Object.values(schema.getTypeMap())\n                .filter(type => isOutputType(type) && !type.name.startsWith('__'))\n                .map(type => ({\n                label: type.name,\n                kind: CompletionItemKind.Function,\n            })));\n        }\n        if (unwrappedState.kind === RuleKinds.INPUT_VALUE_DEF) {\n            return hintList(token, Object.values(schema.getTypeMap())\n                .filter(type => isInputType(type) && !type.name.startsWith('__'))\n                .map(type => ({\n                label: type.name,\n                kind: CompletionItemKind.Function,\n            })));\n        }\n    }\n    if ((kind === RuleKinds.VARIABLE_DEFINITION && step === 2) ||\n        (kind === RuleKinds.LIST_TYPE && step === 1) ||\n        (kind === RuleKinds.NAMED_TYPE &&\n            prevState &&\n            (prevState.kind === RuleKinds.VARIABLE_DEFINITION ||\n                prevState.kind === RuleKinds.LIST_TYPE ||\n                prevState.kind === RuleKinds.NON_NULL_TYPE))) {\n        return getSuggestionsForVariableDefinition(token, schema, kind);\n    }\n    if (kind === RuleKinds.DIRECTIVE) {\n        return getSuggestionsForDirective(token, state, schema, kind);\n    }\n    return [];\n}\nconst insertSuffix = ' {\\n  $1\\n}';\nconst getInsertText = (field) => {\n    const { type } = field;\n    if (isCompositeType(type)) {\n        return insertSuffix;\n    }\n    if (isListType(type) && isCompositeType(type.ofType)) {\n        return insertSuffix;\n    }\n    if (isNonNullType(type)) {\n        if (isCompositeType(type.ofType)) {\n            return insertSuffix;\n        }\n        if (isListType(type.ofType) && isCompositeType(type.ofType.ofType)) {\n            return insertSuffix;\n        }\n    }\n    return null;\n};\nfunction getSuggestionsForTypeSystemDefinitions(token) {\n    return hintList(token, [\n        { label: 'extend', kind: CompletionItemKind.Function },\n        { label: 'type', kind: CompletionItemKind.Function },\n        { label: 'interface', kind: CompletionItemKind.Function },\n        { label: 'union', kind: CompletionItemKind.Function },\n        { label: 'input', kind: CompletionItemKind.Function },\n        { label: 'scalar', kind: CompletionItemKind.Function },\n        { label: 'schema', kind: CompletionItemKind.Function },\n    ]);\n}\nfunction getSuggestionsForExecutableDefinitions(token) {\n    return hintList(token, [\n        { label: 'query', kind: CompletionItemKind.Function },\n        { label: 'mutation', kind: CompletionItemKind.Function },\n        { label: 'subscription', kind: CompletionItemKind.Function },\n        { label: 'fragment', kind: CompletionItemKind.Function },\n        { label: '{', kind: CompletionItemKind.Constructor },\n    ]);\n}\nfunction getSuggestionsForExtensionDefinitions(token) {\n    return hintList(token, [\n        { label: 'type', kind: CompletionItemKind.Function },\n        { label: 'interface', kind: CompletionItemKind.Function },\n        { label: 'union', kind: CompletionItemKind.Function },\n        { label: 'input', kind: CompletionItemKind.Function },\n        { label: 'scalar', kind: CompletionItemKind.Function },\n        { label: 'schema', kind: CompletionItemKind.Function },\n    ]);\n}\nfunction getSuggestionsForFieldNames(token, typeInfo, options) {\n    var _a;\n    if (typeInfo.parentType) {\n        const { parentType } = typeInfo;\n        let fields = [];\n        if ('getFields' in parentType) {\n            fields = objectValues(parentType.getFields());\n        }\n        if (isCompositeType(parentType)) {\n            fields.push(TypeNameMetaFieldDef);\n        }\n        if (parentType === ((_a = options === null || options === void 0 ? void 0 : options.schema) === null || _a === void 0 ? void 0 : _a.getQueryType())) {\n            fields.push(SchemaMetaFieldDef, TypeMetaFieldDef);\n        }\n        return hintList(token, fields.map((field, index) => {\n            var _a;\n            const suggestion = {\n                sortText: String(index) + field.name,\n                label: field.name,\n                detail: String(field.type),\n                documentation: (_a = field.description) !== null && _a !== void 0 ? _a : undefined,\n                deprecated: Boolean(field.deprecationReason),\n                isDeprecated: Boolean(field.deprecationReason),\n                deprecationReason: field.deprecationReason,\n                kind: CompletionItemKind.Field,\n                type: field.type,\n            };\n            if (options === null || options === void 0 ? void 0 : options.fillLeafsOnComplete) {\n                const insertText = getInsertText(field);\n                if (insertText) {\n                    suggestion.insertText = field.name + insertText;\n                    suggestion.insertTextFormat = InsertTextFormat.Snippet;\n                    suggestion.command = SuggestionCommand;\n                }\n            }\n            return suggestion;\n        }));\n    }\n    return [];\n}\nfunction getSuggestionsForInputValues(token, typeInfo, queryText, schema) {\n    const namedInputType = getNamedType(typeInfo.inputType);\n    const queryVariables = getVariableCompletions(queryText, schema, token).filter(v => v.detail === namedInputType.name);\n    if (namedInputType instanceof GraphQLEnumType) {\n        const values = namedInputType.getValues();\n        return hintList(token, values\n            .map((value) => {\n            var _a;\n            return ({\n                label: value.name,\n                detail: String(namedInputType),\n                documentation: (_a = value.description) !== null && _a !== void 0 ? _a : undefined,\n                deprecated: Boolean(value.deprecationReason),\n                isDeprecated: Boolean(value.deprecationReason),\n                deprecationReason: value.deprecationReason,\n                kind: CompletionItemKind.EnumMember,\n                type: namedInputType,\n            });\n        })\n            .concat(queryVariables));\n    }\n    if (namedInputType === GraphQLBoolean) {\n        return hintList(token, queryVariables.concat([\n            {\n                label: 'true',\n                detail: String(GraphQLBoolean),\n                documentation: 'Not false.',\n                kind: CompletionItemKind.Variable,\n                type: GraphQLBoolean,\n            },\n            {\n                label: 'false',\n                detail: String(GraphQLBoolean),\n                documentation: 'Not true.',\n                kind: CompletionItemKind.Variable,\n                type: GraphQLBoolean,\n            },\n        ]));\n    }\n    return queryVariables;\n}\nfunction getSuggestionsForImplements(token, tokenState, schema, documentText, typeInfo) {\n    if (tokenState.needsSeparator) {\n        return [];\n    }\n    const typeMap = schema.getTypeMap();\n    const schemaInterfaces = objectValues(typeMap).filter(isInterfaceType);\n    const schemaInterfaceNames = schemaInterfaces.map(({ name }) => name);\n    const inlineInterfaces = new Set();\n    runOnlineParser(documentText, (_, state) => {\n        var _a, _b, _c, _d, _e;\n        if (state.name) {\n            if (state.kind === RuleKinds.INTERFACE_DEF &&\n                !schemaInterfaceNames.includes(state.name)) {\n                inlineInterfaces.add(state.name);\n            }\n            if (state.kind === RuleKinds.NAMED_TYPE &&\n                ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.IMPLEMENTS) {\n                if (typeInfo.interfaceDef) {\n                    const existingType = (_b = typeInfo.interfaceDef) === null || _b === void 0 ? void 0 : _b.getInterfaces().find(({ name }) => name === state.name);\n                    if (existingType) {\n                        return;\n                    }\n                    const type = schema.getType(state.name);\n                    const interfaceConfig = (_c = typeInfo.interfaceDef) === null || _c === void 0 ? void 0 : _c.toConfig();\n                    typeInfo.interfaceDef = new GraphQLInterfaceType(Object.assign(Object.assign({}, interfaceConfig), { interfaces: [\n                            ...interfaceConfig.interfaces,\n                            type ||\n                                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n                        ] }));\n                }\n                else if (typeInfo.objectTypeDef) {\n                    const existingType = (_d = typeInfo.objectTypeDef) === null || _d === void 0 ? void 0 : _d.getInterfaces().find(({ name }) => name === state.name);\n                    if (existingType) {\n                        return;\n                    }\n                    const type = schema.getType(state.name);\n                    const objectTypeConfig = (_e = typeInfo.objectTypeDef) === null || _e === void 0 ? void 0 : _e.toConfig();\n                    typeInfo.objectTypeDef = new GraphQLObjectType(Object.assign(Object.assign({}, objectTypeConfig), { interfaces: [\n                            ...objectTypeConfig.interfaces,\n                            type ||\n                                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n                        ] }));\n                }\n            }\n        }\n    });\n    const currentTypeToExtend = typeInfo.interfaceDef || typeInfo.objectTypeDef;\n    const siblingInterfaces = (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.getInterfaces()) || [];\n    const siblingInterfaceNames = siblingInterfaces.map(({ name }) => name);\n    const possibleInterfaces = schemaInterfaces\n        .concat([...inlineInterfaces].map(name => ({ name })))\n        .filter(({ name }) => name !== (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.name) &&\n        !siblingInterfaceNames.includes(name));\n    return hintList(token, possibleInterfaces.map(type => {\n        const result = {\n            label: type.name,\n            kind: CompletionItemKind.Interface,\n            type,\n        };\n        if (type === null || type === void 0 ? void 0 : type.description) {\n            result.documentation = type.description;\n        }\n        return result;\n    }));\n}\nfunction getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, _kind) {\n    let possibleTypes;\n    if (typeInfo.parentType) {\n        if (isAbstractType(typeInfo.parentType)) {\n            const abstractType = assertAbstractType(typeInfo.parentType);\n            const possibleObjTypes = schema.getPossibleTypes(abstractType);\n            const possibleIfaceMap = Object.create(null);\n            for (const type of possibleObjTypes) {\n                for (const iface of type.getInterfaces()) {\n                    possibleIfaceMap[iface.name] = iface;\n                }\n            }\n            possibleTypes = possibleObjTypes.concat(objectValues(possibleIfaceMap));\n        }\n        else {\n            possibleTypes = [typeInfo.parentType];\n        }\n    }\n    else {\n        const typeMap = schema.getTypeMap();\n        possibleTypes = objectValues(typeMap).filter(type => isCompositeType(type) && !type.name.startsWith('__'));\n    }\n    return hintList(token, possibleTypes.map(type => {\n        const namedType = getNamedType(type);\n        return {\n            label: String(type),\n            documentation: (namedType === null || namedType === void 0 ? void 0 : namedType.description) || '',\n            kind: CompletionItemKind.Field,\n        };\n    }));\n}\nfunction getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, fragmentDefs) {\n    if (!queryText) {\n        return [];\n    }\n    const typeMap = schema.getTypeMap();\n    const defState = getDefinitionState(token.state);\n    const fragments = getFragmentDefinitions(queryText);\n    if (fragmentDefs && fragmentDefs.length > 0) {\n        fragments.push(...fragmentDefs);\n    }\n    const relevantFrags = fragments.filter(frag => typeMap[frag.typeCondition.name.value] &&\n        !(defState &&\n            defState.kind === RuleKinds.FRAGMENT_DEFINITION &&\n            defState.name === frag.name.value) &&\n        isCompositeType(typeInfo.parentType) &&\n        isCompositeType(typeMap[frag.typeCondition.name.value]) &&\n        doTypesOverlap(schema, typeInfo.parentType, typeMap[frag.typeCondition.name.value]));\n    return hintList(token, relevantFrags.map(frag => ({\n        label: frag.name.value,\n        detail: String(typeMap[frag.typeCondition.name.value]),\n        documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n        kind: CompletionItemKind.Field,\n        type: typeMap[frag.typeCondition.name.value],\n    })));\n}\nconst getParentDefinition = (state, kind) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n    if (((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === kind) {\n        return state.prevState;\n    }\n    if (((_c = (_b = state.prevState) === null || _b === void 0 ? void 0 : _b.prevState) === null || _c === void 0 ? void 0 : _c.kind) === kind) {\n        return state.prevState.prevState;\n    }\n    if (((_f = (_e = (_d = state.prevState) === null || _d === void 0 ? void 0 : _d.prevState) === null || _e === void 0 ? void 0 : _e.prevState) === null || _f === void 0 ? void 0 : _f.kind) === kind) {\n        return state.prevState.prevState.prevState;\n    }\n    if (((_k = (_j = (_h = (_g = state.prevState) === null || _g === void 0 ? void 0 : _g.prevState) === null || _h === void 0 ? void 0 : _h.prevState) === null || _j === void 0 ? void 0 : _j.prevState) === null || _k === void 0 ? void 0 : _k.kind) === kind) {\n        return state.prevState.prevState.prevState.prevState;\n    }\n};\nexport function getVariableCompletions(queryText, schema, token) {\n    let variableName = null;\n    let variableType;\n    const definitions = Object.create({});\n    runOnlineParser(queryText, (_, state) => {\n        if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.VARIABLE && state.name) {\n            variableName = state.name;\n        }\n        if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.NAMED_TYPE && variableName) {\n            const parentDefinition = getParentDefinition(state, RuleKinds.TYPE);\n            if (parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type) {\n                variableType = schema.getType(parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type);\n            }\n        }\n        if (variableName && variableType && !definitions[variableName]) {\n            definitions[variableName] = {\n                detail: variableType.toString(),\n                insertText: token.string === '$' ? variableName : '$' + variableName,\n                label: variableName,\n                type: variableType,\n                kind: CompletionItemKind.Variable,\n            };\n            variableName = null;\n            variableType = null;\n        }\n    });\n    return objectValues(definitions);\n}\nexport function getFragmentDefinitions(queryText) {\n    const fragmentDefs = [];\n    runOnlineParser(queryText, (_, state) => {\n        if (state.kind === RuleKinds.FRAGMENT_DEFINITION &&\n            state.name &&\n            state.type) {\n            fragmentDefs.push({\n                kind: RuleKinds.FRAGMENT_DEFINITION,\n                name: {\n                    kind: Kind.NAME,\n                    value: state.name,\n                },\n                selectionSet: {\n                    kind: RuleKinds.SELECTION_SET,\n                    selections: [],\n                },\n                typeCondition: {\n                    kind: RuleKinds.NAMED_TYPE,\n                    name: {\n                        kind: Kind.NAME,\n                        value: state.type,\n                    },\n                },\n            });\n        }\n    });\n    return fragmentDefs;\n}\nfunction getSuggestionsForVariableDefinition(token, schema, _kind) {\n    const inputTypeMap = schema.getTypeMap();\n    const inputTypes = objectValues(inputTypeMap).filter(isInputType);\n    return hintList(token, inputTypes.map((type) => ({\n        label: type.name,\n        documentation: type.description,\n        kind: CompletionItemKind.Variable,\n    })));\n}\nfunction getSuggestionsForDirective(token, state, schema, _kind) {\n    var _a;\n    if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) {\n        const directives = schema\n            .getDirectives()\n            .filter(directive => canUseDirective(state.prevState, directive));\n        return hintList(token, directives.map(directive => ({\n            label: directive.name,\n            documentation: directive.description || '',\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    return [];\n}\nexport function getTokenAtPosition(queryText, cursor, offset = 0) {\n    let styleAtCursor = null;\n    let stateAtCursor = null;\n    let stringAtCursor = null;\n    const token = runOnlineParser(queryText, (stream, state, style, index) => {\n        if (index !== cursor.line ||\n            stream.getCurrentPosition() + offset < cursor.character + 1) {\n            return;\n        }\n        styleAtCursor = style;\n        stateAtCursor = Object.assign({}, state);\n        stringAtCursor = stream.current();\n        return 'BREAK';\n    });\n    return {\n        start: token.start,\n        end: token.end,\n        string: stringAtCursor || token.string,\n        state: stateAtCursor || token.state,\n        style: styleAtCursor || token.style,\n    };\n}\nexport function runOnlineParser(queryText, callback) {\n    const lines = queryText.split('\\n');\n    const parser = onlineParser();\n    let state = parser.startState();\n    let style = '';\n    let stream = new CharacterStream('');\n    for (let i = 0; i < lines.length; i++) {\n        stream = new CharacterStream(lines[i]);\n        while (!stream.eol()) {\n            style = parser.token(stream, state);\n            const code = callback(stream, state, style, i);\n            if (code === 'BREAK') {\n                break;\n            }\n        }\n        callback(stream, state, style, i);\n        if (!state.kind) {\n            state = parser.startState();\n        }\n    }\n    return {\n        start: stream.getStartOfToken(),\n        end: stream.getCurrentPosition(),\n        string: stream.current(),\n        state,\n        style,\n    };\n}\nexport function canUseDirective(state, directive) {\n    if (!(state === null || state === void 0 ? void 0 : state.kind)) {\n        return false;\n    }\n    const { kind, prevState } = state;\n    const { locations } = directive;\n    switch (kind) {\n        case RuleKinds.QUERY:\n            return locations.includes(DirectiveLocation.QUERY);\n        case RuleKinds.MUTATION:\n            return locations.includes(DirectiveLocation.MUTATION);\n        case RuleKinds.SUBSCRIPTION:\n            return locations.includes(DirectiveLocation.SUBSCRIPTION);\n        case RuleKinds.FIELD:\n        case RuleKinds.ALIASED_FIELD:\n            return locations.includes(DirectiveLocation.FIELD);\n        case RuleKinds.FRAGMENT_DEFINITION:\n            return locations.includes(DirectiveLocation.FRAGMENT_DEFINITION);\n        case RuleKinds.FRAGMENT_SPREAD:\n            return locations.includes(DirectiveLocation.FRAGMENT_SPREAD);\n        case RuleKinds.INLINE_FRAGMENT:\n            return locations.includes(DirectiveLocation.INLINE_FRAGMENT);\n        case RuleKinds.SCHEMA_DEF:\n            return locations.includes(DirectiveLocation.SCHEMA);\n        case RuleKinds.SCALAR_DEF:\n            return locations.includes(DirectiveLocation.SCALAR);\n        case RuleKinds.OBJECT_TYPE_DEF:\n            return locations.includes(DirectiveLocation.OBJECT);\n        case RuleKinds.FIELD_DEF:\n            return locations.includes(DirectiveLocation.FIELD_DEFINITION);\n        case RuleKinds.INTERFACE_DEF:\n            return locations.includes(DirectiveLocation.INTERFACE);\n        case RuleKinds.UNION_DEF:\n            return locations.includes(DirectiveLocation.UNION);\n        case RuleKinds.ENUM_DEF:\n            return locations.includes(DirectiveLocation.ENUM);\n        case RuleKinds.ENUM_VALUE:\n            return locations.includes(DirectiveLocation.ENUM_VALUE);\n        case RuleKinds.INPUT_DEF:\n            return locations.includes(DirectiveLocation.INPUT_OBJECT);\n        case RuleKinds.INPUT_VALUE_DEF:\n            const prevStateKind = prevState === null || prevState === void 0 ? void 0 : prevState.kind;\n            switch (prevStateKind) {\n                case RuleKinds.ARGUMENTS_DEF:\n                    return locations.includes(DirectiveLocation.ARGUMENT_DEFINITION);\n                case RuleKinds.INPUT_DEF:\n                    return locations.includes(DirectiveLocation.INPUT_FIELD_DEFINITION);\n            }\n    }\n    return false;\n}\nexport function getTypeInfo(schema, tokenState) {\n    let argDef;\n    let argDefs;\n    let directiveDef;\n    let enumValue;\n    let fieldDef;\n    let inputType;\n    let objectTypeDef;\n    let objectFieldDefs;\n    let parentType;\n    let type;\n    let interfaceDef;\n    forEachState(tokenState, state => {\n        var _a;\n        switch (state.kind) {\n            case RuleKinds.QUERY:\n            case 'ShortQuery':\n                type = schema.getQueryType();\n                break;\n            case RuleKinds.MUTATION:\n                type = schema.getMutationType();\n                break;\n            case RuleKinds.SUBSCRIPTION:\n                type = schema.getSubscriptionType();\n                break;\n            case RuleKinds.INLINE_FRAGMENT:\n            case RuleKinds.FRAGMENT_DEFINITION:\n                if (state.type) {\n                    type = schema.getType(state.type);\n                }\n                break;\n            case RuleKinds.FIELD:\n            case RuleKinds.ALIASED_FIELD: {\n                if (!type || !state.name) {\n                    fieldDef = null;\n                }\n                else {\n                    fieldDef = parentType\n                        ? getFieldDef(schema, parentType, state.name)\n                        : null;\n                    type = fieldDef ? fieldDef.type : null;\n                }\n                break;\n            }\n            case RuleKinds.SELECTION_SET:\n                parentType = getNamedType(type);\n                break;\n            case RuleKinds.DIRECTIVE:\n                directiveDef = state.name ? schema.getDirective(state.name) : null;\n                break;\n            case RuleKinds.INTERFACE_DEF:\n                if (state.name) {\n                    objectTypeDef = null;\n                    interfaceDef = new GraphQLInterfaceType({\n                        name: state.name,\n                        interfaces: [],\n                        fields: {},\n                    });\n                }\n                break;\n            case RuleKinds.OBJECT_TYPE_DEF:\n                if (state.name) {\n                    interfaceDef = null;\n                    objectTypeDef = new GraphQLObjectType({\n                        name: state.name,\n                        interfaces: [],\n                        fields: {},\n                    });\n                }\n                break;\n            case RuleKinds.ARGUMENTS: {\n                if (state.prevState) {\n                    switch (state.prevState.kind) {\n                        case RuleKinds.FIELD:\n                            argDefs = fieldDef && fieldDef.args;\n                            break;\n                        case RuleKinds.DIRECTIVE:\n                            argDefs =\n                                directiveDef && directiveDef.args;\n                            break;\n                        case RuleKinds.ALIASED_FIELD: {\n                            const name = (_a = state.prevState) === null || _a === void 0 ? void 0 : _a.name;\n                            if (!name) {\n                                argDefs = null;\n                                break;\n                            }\n                            const field = parentType\n                                ? getFieldDef(schema, parentType, name)\n                                : null;\n                            if (!field) {\n                                argDefs = null;\n                                break;\n                            }\n                            argDefs = field.args;\n                            break;\n                        }\n                        default:\n                            argDefs = null;\n                            break;\n                    }\n                }\n                else {\n                    argDefs = null;\n                }\n                break;\n            }\n            case RuleKinds.ARGUMENT:\n                if (argDefs) {\n                    for (let i = 0; i < argDefs.length; i++) {\n                        if (argDefs[i].name === state.name) {\n                            argDef = argDefs[i];\n                            break;\n                        }\n                    }\n                }\n                inputType = argDef === null || argDef === void 0 ? void 0 : argDef.type;\n                break;\n            case RuleKinds.ENUM_VALUE:\n                const enumType = getNamedType(inputType);\n                enumValue =\n                    enumType instanceof GraphQLEnumType\n                        ? enumType\n                            .getValues()\n                            .find((val) => val.value === state.name)\n                        : null;\n                break;\n            case RuleKinds.LIST_VALUE:\n                const nullableType = getNullableType(inputType);\n                inputType =\n                    nullableType instanceof GraphQLList ? nullableType.ofType : null;\n                break;\n            case RuleKinds.OBJECT_VALUE:\n                const objectType = getNamedType(inputType);\n                objectFieldDefs =\n                    objectType instanceof GraphQLInputObjectType\n                        ? objectType.getFields()\n                        : null;\n                break;\n            case RuleKinds.OBJECT_FIELD:\n                const objectField = state.name && objectFieldDefs ? objectFieldDefs[state.name] : null;\n                inputType = objectField === null || objectField === void 0 ? void 0 : objectField.type;\n                break;\n            case RuleKinds.NAMED_TYPE:\n                if (state.name) {\n                    type = schema.getType(state.name);\n                }\n                break;\n        }\n    });\n    return {\n        argDef,\n        argDefs,\n        directiveDef,\n        enumValue,\n        fieldDef,\n        inputType,\n        objectFieldDefs,\n        parentType,\n        type,\n        interfaceDef,\n        objectTypeDef,\n    };\n}\nexport var GraphQLDocumentMode;\n(function (GraphQLDocumentMode) {\n    GraphQLDocumentMode[\"TYPE_SYSTEM\"] = \"TYPE_SYSTEM\";\n    GraphQLDocumentMode[\"EXECUTABLE\"] = \"EXECUTABLE\";\n})(GraphQLDocumentMode || (GraphQLDocumentMode = {}));\nfunction getDocumentMode(documentText, uri) {\n    if (uri === null || uri === void 0 ? void 0 : uri.endsWith('.graphqls')) {\n        return GraphQLDocumentMode.TYPE_SYSTEM;\n    }\n    return hasTypeSystemDefinitions(documentText)\n        ? GraphQLDocumentMode.TYPE_SYSTEM\n        : GraphQLDocumentMode.EXECUTABLE;\n}\nfunction unwrapType(state) {\n    if (state.prevState &&\n        state.kind &&\n        [\n            RuleKinds.NAMED_TYPE,\n            RuleKinds.LIST_TYPE,\n            RuleKinds.TYPE,\n            RuleKinds.NON_NULL_TYPE,\n        ].includes(state.kind)) {\n        return unwrapType(state.prevState);\n    }\n    return state;\n}\n//# sourceMappingURL=getAutocompleteSuggestions.js.map", "import { Graph<PERSON><PERSON>on<PERSON>ull, GraphQLList, } from 'graphql';\nimport { getTokenAtPosition, getTypeInfo } from './getAutocompleteSuggestions';\nexport function getHoverInformation(schema, queryText, cursor, contextToken, config) {\n    const token = contextToken || getTokenAtPosition(queryText, cursor);\n    if (!schema || !token || !token.state) {\n        return '';\n    }\n    const { kind, step } = token.state;\n    const typeInfo = getTypeInfo(schema, token.state);\n    const options = Object.assign(Object.assign({}, config), { schema });\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef)) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderField(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.fieldDef);\n        return into.join('').trim();\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderDirective(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.directiveDef);\n        return into.join('').trim();\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderArg(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.argDef);\n        return into.join('').trim();\n    }\n    if (kind === 'EnumValue' &&\n        typeInfo.enumValue &&\n        'description' in typeInfo.enumValue) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderEnumValue(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.enumValue);\n        return into.join('').trim();\n    }\n    if (kind === 'NamedType' && typeInfo.type && 'description' in typeInfo.type) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderType(into, typeInfo, options, typeInfo.type);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.type);\n        return into.join('').trim();\n    }\n    return '';\n}\nfunction renderMdCodeStart(into, options) {\n    if (options.useMarkdown) {\n        text(into, '```graphql\\n');\n    }\n}\nfunction renderMdCodeEnd(into, options) {\n    if (options.useMarkdown) {\n        text(into, '\\n```');\n    }\n}\nfunction renderField(into, typeInfo, options) {\n    renderQualifiedField(into, typeInfo, options);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.type);\n}\nfunction renderQualifiedField(into, typeInfo, options) {\n    if (!typeInfo.fieldDef) {\n        return;\n    }\n    const fieldName = typeInfo.fieldDef.name;\n    if (fieldName.slice(0, 2) !== '__') {\n        renderType(into, typeInfo, options, typeInfo.parentType);\n        text(into, '.');\n    }\n    text(into, fieldName);\n}\nfunction renderDirective(into, typeInfo, _options) {\n    if (!typeInfo.directiveDef) {\n        return;\n    }\n    const name = '@' + typeInfo.directiveDef.name;\n    text(into, name);\n}\nfunction renderArg(into, typeInfo, options) {\n    if (typeInfo.directiveDef) {\n        renderDirective(into, typeInfo, options);\n    }\n    else if (typeInfo.fieldDef) {\n        renderQualifiedField(into, typeInfo, options);\n    }\n    if (!typeInfo.argDef) {\n        return;\n    }\n    const { name } = typeInfo.argDef;\n    text(into, '(');\n    text(into, name);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.inputType);\n    text(into, ')');\n}\nfunction renderTypeAnnotation(into, typeInfo, options, t) {\n    text(into, ': ');\n    renderType(into, typeInfo, options, t);\n}\nfunction renderEnumValue(into, typeInfo, options) {\n    if (!typeInfo.enumValue) {\n        return;\n    }\n    const { name } = typeInfo.enumValue;\n    renderType(into, typeInfo, options, typeInfo.inputType);\n    text(into, '.');\n    text(into, name);\n}\nfunction renderType(into, typeInfo, options, t) {\n    if (!t) {\n        return;\n    }\n    if (t instanceof GraphQLNonNull) {\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(into, '[');\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, ']');\n    }\n    else {\n        text(into, t.name);\n    }\n}\nfunction renderDescription(into, options, def) {\n    if (!def) {\n        return;\n    }\n    const description = typeof def.description === 'string' ? def.description : null;\n    if (description) {\n        text(into, '\\n\\n');\n        text(into, description);\n    }\n    renderDeprecation(into, options, def);\n}\nfunction renderDeprecation(into, _options, def) {\n    if (!def) {\n        return;\n    }\n    const reason = def.deprecationReason || null;\n    if (!reason) {\n        return;\n    }\n    text(into, '\\n\\n');\n    text(into, 'Deprecated: ');\n    text(into, reason);\n}\nfunction text(into, content) {\n    into.push(content);\n}\n//# sourceMappingURL=getHoverInformation.js.map", "import { IPosition } from 'graphql-language-service';\n\nexport class Cursor implements IPosition {\n  line: number;\n  character: number;\n\n  constructor(line: number, char: number) {\n    this.line = line;\n    this.character = char;\n  }\n\n  setLine(line: number) {\n    this.line = line;\n  }\n\n  setCharacter(character: number) {\n    this.character = character;\n  }\n\n  lessThanOrEqualTo(position: IPosition) {\n    return (\n      this.line < position.line ||\n      (this.line === position.line && this.character <= position.character)\n    );\n  }\n}\n", "import { ts } from '../ts';\nimport { onlineParser, State, CharacterStream } from 'graphql-language-service';\n\nexport interface Token {\n  start: number;\n  end: number;\n  string: string;\n  tokenKind: string;\n  line: number;\n  state: State;\n}\n\nexport const getToken = (\n  template: ts.Expression,\n  cursorPosition: number\n): Token | undefined => {\n  if (!ts.isTemplateLiteral(template) && !ts.isStringLiteralLike(template)) {\n    return undefined;\n  }\n\n  const text = template.getText().slice(1, -1);\n  const input = text.split('\\n');\n  const parser = onlineParser();\n  const state = parser.startState();\n  let cPos = template.getStart() + 1;\n\n  let foundToken: Token | undefined = undefined;\n  let prevToken: Token | undefined = undefined;\n  for (let line = 0; line < input.length; line++) {\n    if (foundToken) continue;\n    const lPos = cPos - 1;\n    const stream = new CharacterStream(input[line] + '\\n');\n    while (!stream.eol()) {\n      const token = parser.token(stream, state);\n      const string = stream.current();\n\n      if (\n        lPos + stream.getStartOfToken() + 1 <= cursorPosition &&\n        lPos + stream.getCurrentPosition() >= cursorPosition\n      ) {\n        foundToken = prevToken\n          ? prevToken\n          : {\n              line,\n              start: stream.getStartOfToken() + 1,\n              end: stream.getCurrentPosition(),\n              string,\n              state,\n              tokenKind: token,\n            };\n        break;\n      } else if (string === 'on') {\n        prevToken = {\n          line,\n          start: stream.getStartOfToken() + 1,\n          end: stream.getCurrentPosition(),\n          string,\n          state,\n          tokenKind: token,\n        };\n      } else if (string === '.' || string === '..') {\n        prevToken = {\n          line,\n          start: stream.getStartOfToken() + 1,\n          end: stream.getCurrentPosition(),\n          string,\n          state,\n          tokenKind: token,\n        };\n      } else {\n        prevToken = undefined;\n      }\n    }\n\n    cPos += input[line]!.length + 1;\n  }\n\n  return foundToken;\n};\n", "import {\n  CompletionItem,\n  Completion<PERSON>temKind,\n  ContextToken,\n  ContextTokenUnion,\n  Maybe,\n  RuleKinds,\n  getDefinitionState,\n} from 'graphql-language-service';\nimport {\n  FragmentDefinitionNode,\n  GraphQLArgument,\n  GraphQLCompositeType,\n  GraphQLDirective,\n  GraphQLEnumValue,\n  GraphQLField,\n  GraphQLInputFieldMap,\n  GraphQLInterfaceType,\n  GraphQLObjectType,\n  GraphQLSchema,\n  GraphQLType,\n  doTypesOverlap,\n  isCompositeType,\n} from 'graphql';\n\n/**\n * This part is vendored from https://github.com/graphql/graphiql/blob/main/packages/graphql-language-service/src/interface/autocompleteUtils.ts#L97\n */\ntype CompletionItemBase = {\n  label: string;\n  isDeprecated?: boolean;\n};\n\n// Create the expected hint response given a possible list and a token\nfunction hintList<T extends CompletionItemBase>(\n  token: ContextTokenUnion,\n  list: Array<T>\n): Array<T> {\n  return filterAndSortList(list, normalizeText(token.string));\n}\n\n// Given a list of hint entries and currently typed text, sort and filter to\n// provide a concise list.\nfunction filterAndSortList<T extends CompletionItemBase>(\n  list: Array<T>,\n  text: string\n): Array<T> {\n  if (!text) {\n    return filterNonEmpty<T>(list, entry => !entry.isDeprecated);\n  }\n\n  const byProximity = list.map(entry => ({\n    proximity: getProximity(normalizeText(entry.label), text),\n    entry,\n  }));\n\n  return filterNonEmpty(\n    filterNonEmpty(byProximity, pair => pair.proximity <= 2),\n    pair => !pair.entry.isDeprecated\n  )\n    .sort(\n      (a, b) =>\n        (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.label.length - b.entry.label.length\n    )\n    .map(pair => pair.entry);\n}\n\n// Filters the array by the predicate, unless it results in an empty array,\n// in which case return the original array.\nfunction filterNonEmpty<T>(\n  array: Array<T>,\n  predicate: (entry: T) => boolean\n): Array<T> {\n  const filtered = array.filter(predicate);\n  return filtered.length === 0 ? array : filtered;\n}\n\nfunction normalizeText(text: string): string {\n  return text.toLowerCase().replace(/\\W/g, '');\n}\n\n// Determine a numeric proximity for a suggestion based on current text.\nfunction getProximity(suggestion: string, text: string): number {\n  // start with lexical distance\n  let proximity = lexicalDistance(text, suggestion);\n  if (suggestion.length > text.length) {\n    // do not penalize long suggestions.\n    proximity -= suggestion.length - text.length - 1;\n    // penalize suggestions not starting with this phrase\n    proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n  }\n  return proximity;\n}\n\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * This distance can be useful for detecting typos in input or sorting\n *\n * @param {string} a\n * @param {string} b\n * @return {int} distance in number of edits\n */\nfunction lexicalDistance(a: string, b: string): number {\n  let i;\n  let j;\n  const d = [];\n  const aLength = a.length;\n  const bLength = b.length;\n\n  for (i = 0; i <= aLength; i++) {\n    d[i] = [i];\n  }\n\n  for (j = 1; j <= bLength; j++) {\n    d[0]![j] = j;\n  }\n\n  for (i = 1; i <= aLength; i++) {\n    for (j = 1; j <= bLength; j++) {\n      const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n\n      d[i]![j] = Math.min(\n        d[i - 1]![j]! + 1,\n        d[i]![j - 1]! + 1,\n        d[i - 1]![j - 1]! + cost\n      );\n\n      if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n        d[i]![j] = Math.min(d[i]![j]!, d[i - 2]![j - 2]! + cost);\n      }\n    }\n  }\n\n  return d[aLength]![bLength]!;\n}\n\nexport type AllTypeInfo = {\n  type: Maybe<GraphQLType>;\n  parentType: Maybe<GraphQLType>;\n  inputType: Maybe<GraphQLType>;\n  directiveDef: Maybe<GraphQLDirective>;\n  fieldDef: Maybe<GraphQLField<any, any>>;\n  enumValue: Maybe<GraphQLEnumValue>;\n  argDef: Maybe<GraphQLArgument>;\n  argDefs: Maybe<GraphQLArgument[]>;\n  objectFieldDefs: Maybe<GraphQLInputFieldMap>;\n  interfaceDef: Maybe<GraphQLInterfaceType>;\n  objectTypeDef: Maybe<GraphQLObjectType>;\n};\n\n/**\n * This is vendored from https://github.com/graphql/graphiql/blob/main/packages/graphql-language-service/src/interface/getAutocompleteSuggestions.ts#L779\n */\nexport function getSuggestionsForFragmentSpread(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  schema: GraphQLSchema,\n  queryText: string,\n  fragments: FragmentDefinitionNode[]\n): Array<CompletionItem> {\n  if (!queryText) {\n    return [];\n  }\n\n  const typeMap = schema.getTypeMap();\n  const defState = getDefinitionState(token.state);\n\n  // Filter down to only the fragments which may exist here.\n  const relevantFrags = fragments.filter(\n    frag =>\n      // Only include fragments with known types.\n      typeMap[frag.typeCondition.name.value] &&\n      // Only include fragments which are not cyclic.\n      !(\n        defState &&\n        defState.kind === RuleKinds.FRAGMENT_DEFINITION &&\n        defState.name === frag.name.value\n      ) &&\n      // Only include fragments which could possibly be spread here.\n      isCompositeType(typeInfo.parentType) &&\n      isCompositeType(typeMap[frag.typeCondition.name.value]) &&\n      doTypesOverlap(\n        schema,\n        typeInfo.parentType,\n        typeMap[frag.typeCondition.name.value] as GraphQLCompositeType\n      )\n  );\n\n  return hintList(\n    token,\n    relevantFrags.map(frag => ({\n      label: frag.name.value,\n      detail: String(typeMap[frag.typeCondition.name.value]),\n      documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n      kind: CompletionItemKind.Field,\n      type: typeMap[frag.typeCondition.name.value],\n    }))\n  );\n}\n", "import { ts } from './ts';\n\nimport {\n  getAutocompleteSuggestions,\n  getTokenAtPosition,\n  getTypeInfo,\n  RuleKinds,\n  State,\n  RuleKind,\n  CompletionItem,\n  onlineParser,\n  CharacterStream,\n  ContextToken,\n} from 'graphql-language-service';\nimport { FragmentDefinitionNode, GraphQLSchema, Kind, parse } from 'graphql';\nimport { print } from '@0no-co/graphql.web';\n\nimport * as checks from './ast/checks';\nimport {\n  bubbleUpCallExpression,\n  bubbleUpTemplate,\n  findNode,\n  getAllFragments,\n  getSource,\n} from './ast';\nimport { Cursor } from './ast/cursor';\nimport { resolveTemplate } from './ast/resolve';\nimport { getToken } from './ast/token';\nimport { getSuggestionsForFragmentSpread } from './graphql/getFragmentSpreadSuggestions';\nimport { SchemaRef } from './graphql/getSchema';\n\nexport function getGraphQLCompletions(\n  filename: string,\n  cursorPosition: number,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.WithMetadata<ts.CompletionInfo> | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  const source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let node = findNode(source, cursorPosition);\n  if (!node) return undefined;\n\n  node = isCallExpression\n    ? bubbleUpCallExpression(node)\n    : bubbleUpTemplate(node);\n\n  let text, cursor, schemaToUse: GraphQLSchema | undefined;\n  if (isCallExpression && checks.isGraphQLCall(node, typeChecker)) {\n    const schemaName = checks.getSchemaName(node, typeChecker);\n\n    schemaToUse =\n      schemaName && schema.multi[schemaName]\n        ? schema.multi[schemaName]?.schema\n        : schema.current?.schema;\n\n    const foundToken = getToken(node.arguments[0], cursorPosition);\n    if (\n      !schemaToUse ||\n      !foundToken ||\n      foundToken.string === '.' ||\n      foundToken.string === '..'\n    )\n      return undefined;\n\n    const queryText = node.arguments[0].getText().slice(1, -1);\n    const fragments = getAllFragments(filename, node, info);\n\n    text = `${queryText}\\n${fragments.map(x => print(x)).join('\\n')}`;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n  } else if (!isCallExpression && checks.isGraphQLTag(node)) {\n    const foundToken = getToken(node.template, cursorPosition);\n    if (\n      !foundToken ||\n      !schema.current ||\n      foundToken.string === '.' ||\n      foundToken.string === '..'\n    )\n      return undefined;\n\n    const { combinedText, resolvedSpans } = resolveTemplate(\n      node,\n      filename,\n      info\n    );\n\n    const amountOfLines = resolvedSpans\n      .filter(\n        x =>\n          x.original.start < cursorPosition &&\n          x.original.start + x.original.length < cursorPosition\n      )\n      .reduce((acc, span) => acc + (span.lines - 1), 0);\n\n    foundToken.line = foundToken.line + amountOfLines;\n\n    text = combinedText;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n    schemaToUse = schema.current.schema;\n  } else {\n    return undefined;\n  }\n\n  const [suggestions, spreadSuggestions] = getSuggestionsInternal(\n    schemaToUse,\n    text,\n    cursor\n  );\n\n  return {\n    isGlobalCompletion: false,\n    isMemberCompletion: false,\n    isNewIdentifierLocation: false,\n    entries: [\n      ...suggestions.map(suggestion => ({\n        ...suggestion,\n        kind: ts.ScriptElementKind.variableElement,\n        name: suggestion.label,\n        kindModifiers: 'declare',\n        sortText: suggestion.sortText || '0',\n        labelDetails: {\n          detail: suggestion.type\n            ? ' ' + suggestion.type?.toString()\n            : undefined,\n          description: suggestion.documentation,\n        },\n      })),\n      ...spreadSuggestions.map(suggestion => ({\n        ...suggestion,\n        kind: ts.ScriptElementKind.variableElement,\n        name: suggestion.label,\n        insertText: '...' + suggestion.label,\n        kindModifiers: 'declare',\n        sortText: '0',\n        labelDetails: {\n          description: suggestion.documentation,\n        },\n      })),\n    ],\n  };\n}\n\nexport function getSuggestionsInternal(\n  schema: GraphQLSchema,\n  queryText: string,\n  cursor: Cursor\n): [CompletionItem[], CompletionItem[]] {\n  const token = getTokenAtPosition(queryText, cursor);\n\n  let fragments: Array<FragmentDefinitionNode> = [];\n  try {\n    const parsed = parse(queryText, { noLocation: true });\n    fragments = parsed.definitions.filter(\n      x => x.kind === Kind.FRAGMENT_DEFINITION\n    ) as Array<FragmentDefinitionNode>;\n  } catch (e) {}\n\n  const isOnTypeCondition =\n    token.string === 'on' && token.state.kind === 'TypeCondition';\n  let suggestions = getAutocompleteSuggestions(\n    schema,\n    queryText,\n    cursor,\n    isOnTypeCondition\n      ? {\n          ...token,\n          state: {\n            ...token.state,\n            step: 1,\n          },\n          type: null,\n        }\n      : undefined\n  );\n  let spreadSuggestions = !isOnTypeCondition\n    ? getSuggestionsForFragmentSpread(\n        token,\n        getTypeInfo(schema, token.state),\n        schema,\n        queryText,\n        fragments\n      )\n    : [];\n\n  const state =\n    token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n  const parentName = getParentDefinition(token.state, RuleKinds.FIELD)?.name;\n\n  if (state && parentName) {\n    const { kind } = state;\n\n    // Argument names\n    if (kind === RuleKinds.ARGUMENTS || kind === RuleKinds.ARGUMENT) {\n      const usedArguments = new Set<String>();\n\n      runOnlineParser(queryText, (_, state) => {\n        if (state.kind === RuleKinds.ARGUMENT) {\n          const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n          if (\n            parentName &&\n            state.name &&\n            parentDefinition?.name === parentName\n          ) {\n            usedArguments.add(state.name);\n          }\n        }\n      });\n\n      suggestions = suggestions.filter(\n        suggestion => !usedArguments.has(suggestion.label)\n      );\n    }\n\n    // Field names\n    if (\n      kind === RuleKinds.SELECTION_SET ||\n      kind === RuleKinds.FIELD ||\n      kind === RuleKinds.ALIASED_FIELD\n    ) {\n      const usedFields = new Set<string>();\n      const usedFragments = getUsedFragments(queryText, parentName);\n\n      runOnlineParser(queryText, (_, state) => {\n        if (\n          state.kind === RuleKinds.FIELD ||\n          state.kind === RuleKinds.ALIASED_FIELD\n        ) {\n          const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n          if (\n            parentDefinition &&\n            parentDefinition.name === parentName &&\n            state.name\n          ) {\n            usedFields.add(state.name);\n          }\n        }\n      });\n\n      suggestions = suggestions.filter(\n        suggestion => !usedFields.has(suggestion.label)\n      );\n      spreadSuggestions = spreadSuggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n    }\n\n    // Fragment spread names\n    if (kind === RuleKinds.FRAGMENT_SPREAD) {\n      const usedFragments = getUsedFragments(queryText, parentName);\n      suggestions = suggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n      spreadSuggestions = spreadSuggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n    }\n  }\n\n  return [suggestions, spreadSuggestions];\n}\n\nfunction getUsedFragments(queryText: string, parentName: string | undefined) {\n  const usedFragments = new Set<string>();\n\n  runOnlineParser(queryText, (_, state) => {\n    if (state.kind === RuleKinds.FRAGMENT_SPREAD && state.name) {\n      const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n      if (parentName && parentDefinition?.name === parentName) {\n        usedFragments.add(state.name);\n      }\n    }\n  });\n\n  return usedFragments;\n}\n\n/**\n * This is vendored from https://github.com/graphql/graphiql/blob/aeedf7614e422c783f5cfb5e226c5effa46318fd/packages/graphql-language-service/src/interface/getAutocompleteSuggestions.ts#L831\n */\nfunction getParentDefinition(state: State, kind: RuleKind) {\n  if (state.prevState?.kind === kind) {\n    return state.prevState;\n  }\n  if (state.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState.prevState;\n  }\n}\n\nfunction runOnlineParser(\n  queryText: string,\n  callback: (\n    stream: CharacterStream,\n    state: State,\n    style: string,\n    index: number\n  ) => void | 'BREAK'\n): ContextToken {\n  const lines = queryText.split('\\n');\n  const parser = onlineParser();\n  let state = parser.startState();\n  let style = '';\n\n  let stream: CharacterStream = new CharacterStream('');\n\n  for (let i = 0; i < lines.length; i++) {\n    stream = new CharacterStream(lines[i]!);\n    while (!stream.eol()) {\n      style = parser.token(stream, state);\n      const code = callback(stream, state, style, i);\n      if (code === 'BREAK') {\n        break;\n      }\n    }\n\n    // Above while loop won't run if there is an empty line.\n    // Run the callback one more time to catch this.\n    callback(stream, state, style, i);\n\n    if (!state.kind) {\n      state = parser.startState();\n    }\n  }\n\n  return {\n    start: stream.getStartOfToken(),\n    end: stream.getCurrentPosition(),\n    string: stream.current(),\n    state,\n    style,\n  };\n}\n", "import type { SchemaOrigin } from '@gql.tada/internal';\n\nimport { ts, init as initTypeScript } from './ts';\nimport { loadSchema } from './graphql/getSchema';\nimport { getGraphQLCompletions } from './autoComplete';\nimport { getGraphQLQuickInfo } from './quickInfo';\nimport { ALL_DIAGNOSTICS, getGraphQLDiagnostics } from './diagnostics';\nimport { templates } from './ast/templates';\nimport { getPersistedCodeFixAtPosition } from './persisted';\n\nfunction createBasicDecorator(info: ts.server.PluginCreateInfo) {\n  const proxy: ts.LanguageService = Object.create(null);\n  for (let k of Object.keys(info.languageService) as Array<\n    keyof ts.LanguageService\n  >) {\n    const x = info.languageService[k]!;\n    // @ts-expect-error - JS runtime trickery which is tricky to type tersely\n    proxy[k] = (...args: Array<{}>) => x.apply(info.languageService, args);\n  }\n\n  return proxy;\n}\n\nexport type Logger = (msg: string) => void;\n\ninterface Config {\n  schema: SchemaOrigin;\n  schemas: SchemaOrigin[];\n  tadaDisablePreprocessing?: boolean;\n  templateIsCallExpression?: boolean;\n  shouldCheckForColocatedFragments?: boolean;\n  template?: string;\n  trackFieldUsage?: boolean;\n  tadaOutputLocation?: string;\n}\n\nfunction create(info: ts.server.PluginCreateInfo) {\n  const logger: Logger = (msg: string) =>\n    info.project.projectService.logger.info(`[GraphQLSP] ${msg}`);\n  const config: Config = info.config;\n\n  logger('config: ' + JSON.stringify(config));\n  if (!config.schema && !config.schemas) {\n    logger('Missing \"schema\" option in configuration.');\n    throw new Error('Please provide a GraphQL Schema!');\n  }\n\n  logger('Setting up the GraphQL Plugin');\n\n  if (config.template) {\n    templates.add(config.template);\n  }\n\n  const proxy = createBasicDecorator(info);\n\n  const schema = loadSchema(info, config, logger);\n\n  proxy.getSemanticDiagnostics = (filename: string): ts.Diagnostic[] => {\n    const originalDiagnostics =\n      info.languageService.getSemanticDiagnostics(filename);\n\n    const hasGraphQLDiagnostics = originalDiagnostics.some(x =>\n      ALL_DIAGNOSTICS.includes(x.code)\n    );\n    if (hasGraphQLDiagnostics) return originalDiagnostics;\n\n    const graphQLDiagnostics = getGraphQLDiagnostics(filename, schema, info);\n\n    return graphQLDiagnostics\n      ? [...graphQLDiagnostics, ...originalDiagnostics]\n      : originalDiagnostics;\n  };\n\n  proxy.getCompletionsAtPosition = (\n    filename: string,\n    cursorPosition: number,\n    options: any\n  ): ts.WithMetadata<ts.CompletionInfo> | undefined => {\n    const completions = getGraphQLCompletions(\n      filename,\n      cursorPosition,\n      schema,\n      info\n    );\n\n    if (completions && completions.entries.length) {\n      return completions;\n    } else {\n      return (\n        info.languageService.getCompletionsAtPosition(\n          filename,\n          cursorPosition,\n          options\n        ) || {\n          isGlobalCompletion: false,\n          isMemberCompletion: false,\n          isNewIdentifierLocation: false,\n          entries: [],\n        }\n      );\n    }\n  };\n\n  proxy.getEditsForRefactor = (\n    filename,\n    formatOptions,\n    positionOrRange,\n    refactorName,\n    actionName,\n    preferences,\n    interactive\n  ) => {\n    const original = info.languageService.getEditsForRefactor(\n      filename,\n      formatOptions,\n      positionOrRange,\n      refactorName,\n      actionName,\n      preferences,\n      interactive\n    );\n\n    const codefix = getPersistedCodeFixAtPosition(\n      filename,\n      typeof positionOrRange === 'number'\n        ? positionOrRange\n        : positionOrRange.pos,\n      info\n    );\n    if (!codefix) return original;\n    return {\n      edits: [\n        {\n          fileName: filename,\n          textChanges: [{ newText: codefix.replacement, span: codefix.span }],\n        },\n      ],\n    };\n  };\n\n  proxy.getApplicableRefactors = (\n    filename,\n    positionOrRange,\n    preferences,\n    reason,\n    kind,\n    includeInteractive\n  ) => {\n    const original = info.languageService.getApplicableRefactors(\n      filename,\n      positionOrRange,\n      preferences,\n      reason,\n      kind,\n      includeInteractive\n    );\n\n    const codefix = getPersistedCodeFixAtPosition(\n      filename,\n      typeof positionOrRange === 'number'\n        ? positionOrRange\n        : positionOrRange.pos,\n      info\n    );\n\n    if (codefix) {\n      return [\n        {\n          name: 'GraphQL',\n          description: 'Operations specific to gql.tada!',\n          actions: [\n            {\n              name: 'Insert document-id',\n              description:\n                'Generate a document-id for your persisted-operation, by default a SHA256 hash.',\n            },\n          ],\n          inlineable: true,\n        },\n        ...original,\n      ];\n    } else {\n      return original;\n    }\n  };\n\n  proxy.getQuickInfoAtPosition = (filename: string, cursorPosition: number) => {\n    const quickInfo = getGraphQLQuickInfo(\n      filename,\n      cursorPosition,\n      schema,\n      info\n    );\n\n    if (quickInfo) return quickInfo;\n\n    return info.languageService.getQuickInfoAtPosition(\n      filename,\n      cursorPosition\n    );\n  };\n\n  logger('proxy: ' + JSON.stringify(proxy));\n\n  return proxy;\n}\n\nconst init: ts.server.PluginModuleFactory = ts => {\n  initTypeScript(ts);\n  return { create };\n};\n\nexport default init;\n", "import { ts } from './ts';\nimport { getHoverInformation } from 'graphql-language-service';\nimport { GraphQLSchema } from 'graphql';\n\nimport {\n  bubbleUpCallExpression,\n  bubbleUpTemplate,\n  findNode,\n  getSchemaName,\n  getSource,\n} from './ast';\n\nimport * as checks from './ast/checks';\nimport { resolveTemplate } from './ast/resolve';\nimport { getToken } from './ast/token';\nimport { Cursor } from './ast/cursor';\nimport { templates } from './ast/templates';\nimport { SchemaRef } from './graphql/getSchema';\n\nexport function getGraphQLQuickInfo(\n  filename: string,\n  cursorPosition: number,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.QuickInfo | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n\n  const source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let node = findNode(source, cursorPosition);\n  if (!node) return undefined;\n\n  node = isCallExpression\n    ? bubbleUpCallExpression(node)\n    : bubbleUpTemplate(node);\n\n  let cursor, text, schemaToUse: GraphQLSchema | undefined;\n  if (isCallExpression && checks.isGraphQLCall(node, typeChecker)) {\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    const schemaName = getSchemaName(node, typeChecker);\n\n    schemaToUse =\n      schemaName && schema.multi[schemaName]\n        ? schema.multi[schemaName]?.schema\n        : schema.current?.schema;\n\n    const foundToken = getToken(node.arguments[0], cursorPosition);\n    if (!schemaToUse || !foundToken) return undefined;\n\n    text = node.arguments[0].getText();\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n  } else if (!isCallExpression && checks.isGraphQLTag(node)) {\n    const foundToken = getToken(node.template, cursorPosition);\n    if (!foundToken || !schema.current) return undefined;\n\n    const { combinedText, resolvedSpans } = resolveTemplate(\n      node,\n      filename,\n      info\n    );\n\n    const amountOfLines = resolvedSpans\n      .filter(\n        x =>\n          x.original.start < cursorPosition &&\n          x.original.start + x.original.length < cursorPosition\n      )\n      .reduce((acc, span) => acc + (span.lines - 1), 0);\n\n    foundToken.line = foundToken.line + amountOfLines;\n    text = combinedText;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n    schemaToUse = schema.current.schema;\n  } else {\n    return undefined;\n  }\n\n  const hoverInfo = getHoverInformation(schemaToUse, text, cursor);\n\n  return {\n    kind: ts.ScriptElementKind.label,\n    textSpan: {\n      start: cursorPosition,\n      length: 1,\n    },\n    kindModifiers: 'text',\n    documentation: Array.isArray(hoverInfo)\n      ? hoverInfo.map(item => ({ kind: 'text', text: item as string }))\n      : [{ kind: 'text', text: hoverInfo as string }],\n  } as ts.QuickInfo;\n}\n"], "names": ["statFile", "file", "predicate", "fs", "stat", "then", "catch", "swapWrite", "async", "target", "contents", "isFile", "writeFile", "tempTarget", "rename", "error", "unlink", "now", "Date", "utimes", "_error", "touchFile", "saveTadaIntrospection", "introspection", "tadaOutputLocation", "disablePreprocessing", "logger", "minified", "minifyIntrospection", "outputIntrospectionFile", "fileType", "shouldPreprocess", "output", "isDirectory", "path", "join", "dirname", "getDefinitionState", "tokenState", "definitionState", "forEachState", "state", "kind", "getFieldDef", "schema", "type", "fieldName", "SchemaMetaFieldDef", "name", "getQueryType", "TypeMetaFieldDef", "TypeNameMetaFieldDef", "isCompositeType", "getFields", "stack", "fn", "reverseStateStack", "push", "prevState", "i", "length", "objectValues", "object", "keys", "Object", "len", "values", "Array", "hintList", "token", "list", "filterAndSortList", "text", "filterNonEmpty", "entry", "isDeprecated", "byProximity", "map", "proximity", "getProximity", "normalizeText", "label", "pair", "sort", "a", "b", "string", "array", "filtered", "filter", "toLowerCase", "replaceAll", "suggestion", "lexicalDistance", "j", "d", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "cost", "Math", "min", "indexOf", "DocumentUri", "is", "value", "URI", "integer", "MIN_VALUE", "MAX_VALUE", "<PERSON><PERSON><PERSON><PERSON>", "Position", "create", "line", "character", "Number", "candidate", "Is", "objectLiteral", "Range", "one", "two", "three", "four", "start", "end", "Error", "concat", "Location", "uri", "range", "undefined", "LocationLink", "targetUri", "targetRange", "targetSelectionRange", "originSelectionRange", "Color", "red", "green", "blue", "alpha", "numberRange", "ColorInformation", "color", "ColorPresentation", "textEdit", "additionalTextEdits", "TextEdit", "typedArray", "FoldingRangeKind", "Comment", "Imports", "Region", "FoldingRange", "startLine", "endLine", "startCharacter", "endCharacter", "collapsedText", "result", "defined", "DiagnosticRelatedInformation", "location", "message", "DiagnosticSeverity", "Warning", "Information", "Hint", "DiagnosticTag", "Unnecessary", "Deprecated", "CodeDescription", "href", "Diagnostic", "severity", "code", "source", "relatedInformation", "_a", "number", "codeDescription", "Command", "title", "command", "args", "_i", "arguments", "replace", "newText", "insert", "position", "del", "ChangeAnnotation", "needsConfirmation", "description", "boolean", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "annotation", "annotationId", "TextDocumentEdit", "textDocument", "edits", "OptionalVersionedTextDocumentIdentifier", "isArray", "CreateFile", "options", "overwrite", "ignoreIfExists", "RenameFile", "old<PERSON><PERSON>", "newUri", "DeleteFile", "recursive", "ignoreIfNotExists", "WorkspaceEdit", "changes", "documentChanges", "every", "change", "TextEditChangeImpl", "changeAnnotations", "this", "prototype", "edit", "id", "assertChangeAnnotations", "manage", "delete", "add", "all", "clear", "splice", "ChangeAnnotations", "annotations", "_annotations", "_counter", "_size", "defineProperty", "get", "enumerable", "configurable", "idOrAnnotation", "nextId", "toString", "WorkspaceChange", "workspaceEdit", "_this", "_textEditChanges", "_workspaceEdit", "_changeAnnotations", "for<PERSON>ach", "textEditChange", "key", "initDocumentChanges", "size", "getTextEditChange", "version", "initChanges", "createFile", "optionsOrAnnotation", "operation", "renameFile", "deleteFile", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "TextDocumentItem", "languageId", "<PERSON><PERSON><PERSON><PERSON>", "PlainText", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "Text", "Method", "Function", "<PERSON><PERSON><PERSON><PERSON>", "Field", "Variable", "Class", "Interface", "<PERSON><PERSON><PERSON>", "Property", "Unit", "Value", "Enum", "Keyword", "Snippet", "File", "Reference", "Folder", "EnumMember", "Constant", "Struct", "Event", "Operator", "TypeParameter", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "InsertTextMode", "asIs", "adjustIndentation", "CompletionItemLabelDetails", "detail", "CompletionItem", "CompletionList", "items", "isIncomplete", "MarkedString", "fromPlainText", "plainText", "language", "Hover", "ParameterInformation", "documentation", "SignatureInformation", "parameters", "DocumentHighlightKind", "Read", "Write", "DocumentHighlight", "SymbolKind", "Namespace", "Package", "String", "Boolean", "Key", "<PERSON><PERSON>", "SymbolTag", "SymbolInformation", "containerName", "WorkspaceSymbol", "DocumentSymbol", "<PERSON><PERSON><PERSON><PERSON>", "children", "deprecated", "tags", "CodeActionKind", "Empty", "QuickFix", "Refa<PERSON>", "RefactorExtract", "RefactorInline", "RefactorRewrite", "Source", "SourceOrganizeImports", "SourceFixAll", "CodeActionTriggerKind", "Invoked", "Automatic", "CodeActionContext", "diagnostics", "only", "trigger<PERSON>ind", "CodeAction", "kindOrCommandOrEdit", "checkKind", "isPreferred", "CodeLens", "data", "FormattingOptions", "tabSize", "insertSpaces", "DocumentLink", "SelectionRange", "parent", "SemanticTokenTypes", "SemanticTokenModifiers", "SemanticTokens", "resultId", "InlineValueText", "InlineValueVariableLookup", "variableName", "caseSensitiveLookup", "InlineValueEvaluatableExpression", "expression", "InlineValueContext", "frameId", "stoppedLocation", "InlayHintKind", "Type", "Parameter", "InlayHintLabelPart", "tooltip", "InlayHint", "textEdits", "paddingLeft", "paddingRight", "WorkspaceFolder", "TextDocument", "content", "FullTextDocument", "lineCount", "func", "getText", "positionAt", "offsetAt", "applyEdits", "document", "sortedEdits", "mergeSort", "diff", "lastModifiedOffset", "e", "startOffset", "endOffset", "substring", "compare", "p", "left", "slice", "right", "leftIdx", "rightIdx", "_uri", "_languageId", "_version", "_content", "_lineOffsets", "update", "event", "getLineOffsets", "lineOffsets", "isLineStart", "ch", "char<PERSON>t", "offset", "max", "low", "high", "mid", "floor", "lineOffset", "call", "check", "RuleKinds", "assign", "Kind", "ALIASED_FIELD", "ARGUMENTS", "SHORT_QUERY", "QUERY", "MUTATION", "SUBSCRIPTION", "TYPE_CONDITION", "INVALID", "COMMENT", "SCHEMA_DEF", "SCALAR_DEF", "OBJECT_TYPE_DEF", "OBJECT_VALUE", "LIST_VALUE", "INTERFACE_DEF", "UNION_DEF", "ENUM_DEF", "ENUM_VALUE", "FIELD_DEF", "INPUT_DEF", "INPUT_VALUE_DEF", "ARGUMENTS_DEF", "EXTEND_DEF", "EXTENSION_DEFINITION", "DIRECTIVE_DEF", "IMPLEMENTS", "VARIABLE_DEFINITIONS", "TYPE", "SuggestionCommand", "collectFragmentDefs", "op", "externalFragments", "visit", "parse", "FragmentDefinition", "def", "typeSystemKinds", "SCHEMA_DEFINITION", "OPERATION_TYPE_DEFINITION", "SCALAR_TYPE_DEFINITION", "OBJECT_TYPE_DEFINITION", "INTERFACE_TYPE_DEFINITION", "UNION_TYPE_DEFINITION", "ENUM_TYPE_DEFINITION", "INPUT_OBJECT_TYPE_DEFINITION", "DIRECTIVE_DEFINITION", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "hasTypeSystemDefinitions", "sdl", "hasTypeSystemDef", "enter", "node", "includes", "BREAK", "getAutocompleteSuggestions", "queryText", "cursor", "contextToken", "fragmentDefs", "opts", "getTokenAtPosition", "mode", "getDocumentMode", "documentText", "endsWith", "GraphQLDocumentMode", "TYPE_SYSTEM", "EXECUTABLE", "step", "typeInfo", "getTypeInfo", "DOCUMENT", "getSuggestionsForTypeSystemDefinitions", "getSuggestionsForExecutableDefinitions", "getSuggestionsForExtensionDefinitions", "getTypeMap", "isScalarType", "isObjectType", "startsWith", "isInterfaceType", "isUnionType", "isEnumType", "isInputObjectType", "NAMED_TYPE", "getSuggestionsForImplements", "needsSeparator", "typeMap", "schemaInterfaces", "schemaInterfaceNames", "inlineInterfaces", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "interfaceDef", "_b", "getInterfaces", "find", "getType", "interfaceConfig", "_c", "toConfig", "GraphQLInterfaceType", "interfaces", "fields", "objectTypeDef", "_d", "objectTypeConfig", "_e", "GraphQLObjectType", "currentTypeToExtend", "siblingInterfaceNames", "possibleInterfaces", "SELECTION_SET", "FIELD", "getSuggestionsForFieldNames", "parentType", "field", "index", "sortText", "deprecationReason", "fillLeafsOnComplete", "insertText", "getInsertText", "insertTextFormat", "ARGUMENT", "argDefs", "argDef", "OBJECT_FIELD", "objectFieldDefs", "objectFields", "completionKind", "getSuggestionsForInputValues", "namedInputType", "getNamedType", "inputType", "queryVariables", "getVariableCompletions", "v", "GraphQLEnumType", "getV<PERSON>ues", "GraphQLBoolean", "VARIABLE", "getSuggestionsForFragmentTypeConditions", "_kind", "possibleTypes", "isAbstractType", "abstractType", "assertAbstractType", "possibleObjTypes", "getPossibleTypes", "possibleIfaceMap", "iface", "namedType", "FRAGMENT_SPREAD", "getSuggestionsForFragmentSpread", "defState", "fragments", "getFragmentDefinitions", "FRAGMENT_DEFINITION", "NAME", "selectionSet", "selections", "typeCondition", "relevantFrags", "frag", "doTypesOverlap", "unwrappedState", "unwrapType", "needsAdvance", "LIST_TYPE", "isOutputType", "isInputType", "VARIABLE_DEFINITION", "NON_NULL_TYPE", "getSuggestionsForVariableDefinition", "inputTypeMap", "inputTypes", "DIRECTIVE", "getSuggestionsForDirective", "directives", "getDirectives", "directive", "canUseDirective", "locations", "DirectiveLocation", "INLINE_FRAGMENT", "SCHEMA", "SCALAR", "OBJECT", "FIELD_DEFINITION", "INTERFACE", "UNION", "ENUM", "INPUT_OBJECT", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "insertSuffix", "isListType", "ofType", "isNonNullType", "getParentDefinition", "_f", "_k", "_j", "_h", "_g", "variableType", "definitions", "parentDefinition", "styleAtCursor", "stateAtCursor", "stringAtCursor", "stream", "style", "getCurrentPosition", "current", "callback", "lines", "split", "parser", "onlineParser", "startState", "CharacterStream", "eol", "getStartOfToken", "directiveDef", "enumValue", "fieldDef", "getMutationType", "getSubscriptionType", "getDirective", "enumType", "val", "nullableType", "getNullableType", "GraphQLList", "objectType", "GraphQLInputObjectType", "objectField", "getHoverInformation", "config", "into", "renderMdCodeStart", "renderField", "renderQualifiedField", "renderTypeAnnotation", "renderMdCodeEnd", "renderDescription", "trim", "renderDirective", "renderArg", "renderEnumValue", "renderType", "useMarkdown", "_options", "t", "GraphQLNonNull", "renderDeprecation", "reason", "<PERSON><PERSON><PERSON>", "constructor", "char", "setLine", "<PERSON><PERSON><PERSON><PERSON>", "lessThanOrEqualTo", "getToken", "template", "cursorPosition", "ts", "isTemplateLiteral", "isStringLiteralLike", "input", "cPos", "getStart", "foundToken", "prevToken", "lPos", "tokenKind", "getGraphQLCompletions", "filename", "info", "isCallExpression", "templateIsCallExpression", "typeC<PERSON>cker", "languageService", "getProgram", "getType<PERSON><PERSON>cker", "getSource", "findNode", "bubbleUpCallExpression", "bubbleUpTemplate", "schemaToUse", "checks", "schemaName", "multi", "getAllFragments", "x", "print", "isGraphQLTag", "combinedText", "resolvedSpans", "resolveTemplate", "amountOfLines", "original", "reduce", "acc", "span", "suggestions", "spreadSuggestions", "getSuggestionsInternal", "noLocation", "isOnTypeCondition", "parentName", "usedArguments", "has", "usedFields", "usedFragments", "getUsedFragments", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "ScriptElementKind", "variableElement", "kindModifiers", "labelDetails", "msg", "project", "projectService", "JSON", "stringify", "schemas", "templates", "proxy", "createBasicDecorator", "_loop", "k", "apply", "loadSchema", "origin", "ref", "loadRef", "rootPath", "resolveTypeScriptRootDir", "getProjectName", "tadaDisablePreprocessing", "resolve", "load", "autoupdate", "schemaRef", "found", "getSemanticDiagnostics", "originalDiagnostics", "some", "ALL_DIAGNOSTICS", "graphQLDiagnostics", "getGraphQLDiagnostics", "getCompletionsAtPosition", "completions", "getEditsForRefactor", "formatOptions", "position<PERSON>r<PERSON>ang<PERSON>", "refactorName", "actionName", "preferences", "interactive", "codefix", "getPersistedCodeFixAtPosition", "pos", "fileName", "textChanges", "replacement", "getApplicableRefactors", "includeInteractive", "actions", "inlineable", "getQuickInfoAtPosition", "quickInfo", "getGraphQLQuickInfo", "getSchemaName", "hoverInfo", "textSpan", "item", "initTypeScript", "init"], "mappings": ";;;;;;;;;;AAmBA,IAAMA,WAAWA,CACfC,GACAC,MAEOC,EACJC,KAAKH,GACLI,KAAKH,GACLI,OAAM,OAAM;;AAWV,IAAMC,YAAYC,OACvBC,GACAC;EAEA,WAAYV,SAASS,IAAQL,KAAQA,EAAKO;UAGlCR,EAAGS,UAAUH,GAAQC;SACtB;IAIL,IAAMG,IAAaJ,IAAS;UACtBN,EAAGS,UAAUC,GAAYH;IAC/B;YACQP,EAAGW,OAAOD,GAAYJ;AAC7B,MAAC,OAAOM;YACDZ,EAAGa,OAAOH;MAChB,MAAME;AACR,MAAU;YA3BIP;QAChB;UACE,IAAMS,IAAM,IAAIC;gBACVf,EAAGgB,OAAOlB,GAAMgB,GAAKA;AAC7B,UAAE,OAAOG,IAAS;AAAA,QA2BRC,CAAUZ;AAClB;AACF;AAAA;;AAGFD,eAAec,sBACbC,GACAC,GACAC,GACAC;EAEA,IAAMC,IAAWC,sBAAoBL;EACrC,IAAMb,IAAWmB,EAAuBA,wBAACF,GAAU;IACjDG,UAAUN;IACVO,mBAAmBN;;EAGrB,IAAIO,IAASR;EAEb,UAAUxB,SAASgC,IAAQ5B,KAAQA,EAAK6B;IACtCD,IAASE,EAAKC,KAAKH,GAAQ;SACtB,WACGhC,SAASkC,EAAKE,QAAQJ,KAAS5B,KAAQA,EAAK6B,iBACpD;IACAP,EAAQ,2CAA0CM;IAClD;AACF;EAEA;UACQzB,UAAUyB,GAAQtB;IACxBgB,EAAQ,iCAAgCM;AACzC,IAAC,OAAOjB;IACPW,EAAQ,mCAAkCX;AAC5C;AACF;;ACvEM,SAAUsB,mBACdC;EAEA,IAAIC;EAGJC,aAAaF,IAAaG;IACxB,QAAQA,EAAMC;KACZ,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;MACHH,IAAkBE;;;EAKxB,OAAOF;AACT;;AAGM,SAAUI,YACdC,GACAC,GACAC;EAEA,IAAIA,MAAcC,EAAAA,mBAAmBC,QAAQJ,EAAOK,mBAAmBJ;IACrE,OAAOE;;EAET,IAAID,MAAcI,EAAAA,iBAAiBF,QAAQJ,EAAOK,mBAAmBJ;IACnE,OAAOK;;EAET,IAAIJ,MAAcK,EAAoBA,qBAACH,QAAQI,EAAeA,gBAACP;IAC7D,OAAOM;;EAET,IAAI,eAAeN;IACjB,OAAOA,EAAKQ,YAAYP;;EAG1B,OAAO;AACT;;AAGM,SAAUN,aACdc,GACAC;EAEA,IAAMC,IAAoB;EAC1B,IAAIf,IAAkCa;EACtC,OAAOb,qBAAAA,EAAOC,MAAM;IAClBc,EAAkBC,KAAKhB;IACvBA,IAAQA,EAAMiB;;EAEhB,KAAK,IAAIC,IAAIH,EAAkBI,SAAS,GAAGD,KAAK,GAAGA;IACjDJ,EAAGC,EAAkBG;;AAEzB;;AAEM,SAAUE,aAAgBC;EAC9B,IAAMC,IAAOC,OAAOD,KAAKD;EACzB,IAAMG,IAAMF,EAAKH;EACjB,IAAMM,IAAS,IAAIC,MAAMF;EACzB,KAAK,IAAIN,IAAI,GAAGA,IAAIM,KAAON;IACzBO,EAAOP,KAAKG,EAAOC,EAAKJ;;EAE1B,OAAOO;AACT;;AAGM,SAAUE,WACdC,GACAC;EAEA,OAKF,SAASC,oBACPD,GACAE;IAEA,KAAKA;MACH,OAAOC,iBAAkBH,IAAMI,MAAUA,EAAMC;;IAGjD,IAAMC,IAAcN,EAAKO,KAAIH,MAAU;MACrCI,WAAWC,eAAaC,gBAAcN,EAAMO,QAAQT;MACpDE;;IAGF,OAAOD,iBACLA,iBAAeG,IAAaM,KAAQA,EAAKJ,aAAa,MACtDI,MAASA,EAAKR,MAAMC,eAEnBQ,MACC,CAACC,GAAGC,OACDD,EAAEV,MAAMC,eAAe,IAAI,MAAMU,EAAEX,MAAMC,eAAe,IAAI,MAC7DS,EAAEN,YAAYO,EAAEP,aAChBM,EAAEV,MAAMO,MAAMrB,SAASyB,EAAEX,MAAMO,MAAMrB,SAExCiB,KAAIK,KAAQA,EAAKR;AACtB,GA7BSH,CAAkBD,GAAMU,gBAAcX,EAAMiB;AACrD;;AAgCA,SAASb,iBACPc,GACArF;EAEA,IAAMsF,IAAWD,EAAME,OAAOvF;EAC9B,OAA2B,MAApBsF,EAAS5B,SAAe2B,IAAQC;AACzC;;AAEA,SAASR,gBAAcR;EACrB,OAAOA,EAAKkB,cAAcC,WAAW,OAAO;AAC9C;;AAGA,SAASZ,eAAaa,GAAoBpB;EAExC,IAAIM,IAwBN,SAASe,kBAAgBT,GAAWC;IAClC,IAAI1B;IACJ,IAAImC;IACJ,IAAMC,IAAI;IACV,IAAMC,IAAUZ,EAAExB;IAClB,IAAMqC,IAAUZ,EAAEzB;IAElB,KAAKD,IAAI,GAAGA,KAAKqC,GAASrC;MACxBoC,EAAEpC,KAAK,EAACA;;IAGV,KAAKmC,IAAI,GAAGA,KAAKG,GAASH;MACxBC,EAAE,GAAGD,KAAKA;;IAGZ,KAAKnC,IAAI,GAAGA,KAAKqC,GAASrC;MACxB,KAAKmC,IAAI,GAAGA,KAAKG,GAASH,KAAK;QAC7B,IAAMI,IAAOd,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,KAAK,IAAI;QAEzCC,EAAEpC,GAAGmC,KAAKK,KAAKC,IACbL,EAAEpC,IAAI,GAAGmC,KAAK,GACdC,EAAEpC,GAAGmC,IAAI,KAAK,GACdC,EAAEpC,IAAI,GAAGmC,IAAI,KAAKI;QAGpB,IAAIvC,IAAI,KAAKmC,IAAI,KAAKV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,MAAMV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI;UAChEC,EAAEpC,GAAGmC,KAAKK,KAAKC,IAAIL,EAAEpC,GAAGmC,IAAIC,EAAEpC,IAAI,GAAGmC,IAAI,KAAKI;;;;IAKpD,OAAOH,EAAEC,GAASC;AACpB,GAxDkBJ,CAAgBrB,GAAMoB;EACtC,IAAIA,EAAWhC,SAASY,EAAKZ,QAAQ;IAEnCkB,KAAac,EAAWhC,SAASY,EAAKZ,SAAS;IAE/CkB,KAA0C,MAA7Bc,EAAWS,QAAQ7B,KAAc,IAAI;;EAEpD,OAAOM;AACT;;ACpJO,IAAIwB;;CACX,SAAWA;EAIPA,EAAYC,KAHZ,SAASA,GAAGC;IACR,OAAwB,mBAAVA;AAClB;AAEH,CALD,CAKGF,MAAgBA,IAAc,CAAE;;AAC5B,IAAIG;;CACX,SAAWA;EAIPA,EAAIF,KAHJ,SAASA,GAAGC;IACR,OAAwB,mBAAVA;AAClB;AAEH,CALD,CAKGC,MAAQA,IAAM,CAAE;;AACZ,IAAIC;;CACX,SAAWA;EACPA,EAAQC,aAAa;EACrBD,EAAQE,YAAY;EAIpBF,EAAQH,KAHR,SAASA,GAAGC;IACR,OAAwB,mBAAVA,KAAsBE,EAAQC,aAAaH,KAASA,KAASE,EAAQE;AACvF;AAEH,CAPD,CAOGF,MAAYA,IAAU,CAAE;;AACpB,IAAIG;;CACX,SAAWA;EACPA,EAASF,YAAY;EACrBE,EAASD,YAAY;EAIrBC,EAASN,KAHT,SAASA,GAAGC;IACR,OAAwB,mBAAVA,KAAsBK,EAASF,aAAaH,KAASA,KAASK,EAASD;AACzF;AAEH,CAPD,CAOGC,MAAaA,IAAW,CAAE;;AAKtB,IAAIC;;CACX,SAAWA;EAePA,EAASC,SATT,SAASA,OAAOC,GAAMC;IAClB,IAAID,MAASE,OAAON;MAChBI,IAAOH,EAASD;;IAEpB,IAAIK,MAAcC,OAAON;MACrBK,IAAYJ,EAASD;;IAEzB,OAAO;MAAEI,MAAMA;MAAMC,WAAWA;;AACpC;EASAH,EAASP,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGP,SAASM,EAAUH,SAASI,GAAGP,SAASM,EAAUF;AAC/F;AAEH,CAxBD,CAwBGH,MAAaA,IAAW,CAAE;;AAKtB,IAAIQ;;CACX,SAAWA;EAYPA,EAAMP,SAXN,SAASA,OAAOQ,GAAKC,GAAKC,GAAOC;IAC7B,IAAIN,GAAGP,SAASU,MAAQH,GAAGP,SAASW,MAAQJ,GAAGP,SAASY,MAAUL,GAAGP,SAASa;MAC1E,OAAO;QAAEC,OAAOb,EAASC,OAAOQ,GAAKC;QAAMI,KAAKd,EAASC,OAAOU,GAAOC;;WAEtE,IAAIZ,EAASP,GAAGgB,MAAQT,EAASP,GAAGiB;MACrC,OAAO;QAAEG,OAAOJ;QAAKK,KAAKJ;;;MAG1B,MAAM,IAAIK,MAAM,8CAA8CC,OAAOP,GAAK,MAAMO,OAAON,GAAK,MAAMM,OAAOL,GAAO,MAAMK,OAAOJ,GAAM;;AAE3I;EASAJ,EAAMf,KAJN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcL,EAASP,GAAGY,EAAUQ,UAAUb,EAASP,GAAGY,EAAUS;AAChG;AAEH,CArBD,CAqBGN,MAAUA,IAAQ,CAAE;;AAKhB,IAAIS;;CACX,SAAWA;EASPA,EAAShB,SAHT,SAASA,OAAOiB,GAAKC;IACjB,OAAO;MAAED,KAAKA;MAAKC,OAAOA;;AAC9B;EASAF,EAASxB,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAGc,UAAUf,EAAUa;AAC3H;AAEH,CAlBD,CAkBGD,MAAaA,IAAW,CAAE;;AAKtB,IAAII;;CACX,SAAWA;EAWPA,EAAapB,SAHb,SAASA,OAAOqB,GAAWC,GAAaC,GAAsBC;IAC1D,OAAO;MAAEH,WAAWA;MAAWC,aAAaA;MAAaC,sBAAsBA;MAAsBC,sBAAsBA;;AAC/H;EAWAJ,EAAa5B,KANb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUkB,gBAAgBjB,GAAG9B,OAAO6B,EAAUiB,cACtFd,EAAMf,GAAGY,EAAUmB,0BAClBhB,EAAMf,GAAGY,EAAUoB,yBAAyBnB,GAAGc,UAAUf,EAAUoB;AAC/E;AAEH,CAtBD,CAsBGJ,MAAiBA,IAAe,CAAE;;AAK9B,IAAIK;;CACX,SAAWA;EAYPA,EAAMzB,SARN,SAASA,OAAO0B,GAAKC,GAAOC,GAAMC;IAC9B,OAAO;MACHH,KAAKA;MACLC,OAAOA;MACPC,MAAMA;MACNC,OAAOA;;AAEf;EAYAJ,EAAMjC,KAPN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGyB,YAAY1B,EAAUsB,KAAK,GAAG,MAChErB,GAAGyB,YAAY1B,EAAUuB,OAAO,GAAG,MACnCtB,GAAGyB,YAAY1B,EAAUwB,MAAM,GAAG,MAClCvB,GAAGyB,YAAY1B,EAAUyB,OAAO,GAAG;AAC9C;AAEH,CAxBD,CAwBGJ,MAAUA,IAAQ,CAAE;;AAKhB,IAAIM;;CACX,SAAWA;EAUPA,EAAiB/B,SANjB,SAASA,OAAOkB,GAAOc;IACnB,OAAO;MACHd,OAAOA;MACPc,OAAOA;;AAEf;EASAD,EAAiBvC,KAJjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,UAAUO,EAAMjC,GAAGY,EAAU4B;AAC1F;AAEH,CAnBD,CAmBGD,MAAqBA,IAAmB,CAAE;;AAKtC,IAAIE;;CACX,SAAWA;EAWPA,EAAkBjC,SAPlB,SAASA,OAAO9B,GAAOgE,GAAUC;IAC7B,OAAO;MACHjE,OAAOA;MACPgE,UAAUA;MACVC,qBAAqBA;;AAE7B;EAWAF,EAAkBzC,KANlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUlC,WAClDmC,GAAGc,UAAUf,EAAU8B,aAAaE,EAAS5C,GAAGY,QAChDC,GAAGc,UAAUf,EAAU+B,wBAAwB9B,GAAGgC,WAAWjC,EAAU+B,qBAAqBC,EAAS5C;AACjH;AAEH,CAtBD,CAsBGyC,MAAsBA,IAAoB,CAAE;;AAIxC,IAAIK;;CACX,SAAWA;EAIPA,EAAiBC,UAAU;EAI3BD,EAAiBE,UAAU;EAI3BF,EAAiBG,SAAS;AAC7B,CAbD,CAaGH,MAAqBA,IAAmB,CAAE;;AAKtC,IAAII;;CACX,SAAWA;EAuBPA,EAAa1C,SAnBb,SAASA,OAAO2C,GAAWC,GAASC,GAAgBC,GAAcnH,GAAMoH;IACpE,IAAIC,IAAS;MACTL,WAAWA;MACXC,SAASA;;IAEb,IAAIvC,GAAG4C,QAAQJ;MACXG,EAAOH,iBAAiBA;;IAE5B,IAAIxC,GAAG4C,QAAQH;MACXE,EAAOF,eAAeA;;IAE1B,IAAIzC,GAAG4C,QAAQtH;MACXqH,EAAOrH,OAAOA;;IAElB,IAAI0E,GAAG4C,QAAQF;MACXC,EAAOD,gBAAgBA;;IAE3B,OAAOC;AACX;EAYAN,EAAalD,KAPb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGP,SAASM,EAAUuC,cAActC,GAAGP,SAASM,EAAUuC,eACxFtC,GAAGc,UAAUf,EAAUyC,mBAAmBxC,GAAGP,SAASM,EAAUyC,qBAChExC,GAAGc,UAAUf,EAAU0C,iBAAiBzC,GAAGP,SAASM,EAAU0C,mBAC9DzC,GAAGc,UAAUf,EAAUzE,SAAS0E,GAAG9B,OAAO6B,EAAUzE;AAChE;AAEH,CAnCD,CAmCG+G,MAAiBA,IAAe,CAAE;;AAK9B,IAAIQ;;CACX,SAAWA;EAUPA,EAA6BlD,SAN7B,SAASA,OAAOmD,GAAUC;IACtB,OAAO;MACHD,UAAUA;MACVC,SAASA;;AAEjB;EASAF,EAA6B1D,KAJ7B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcY,EAASxB,GAAGY,EAAU+C,aAAa9C,GAAG9B,OAAO6B,EAAUgD;AAC3F;AAEH,CAnBD,CAmBGF,MAAiCA,IAA+B,CAAE;;AAI9D,IAAIG;;CACX,SAAWA;EAIPA,EAAmBvC,QAAQ;EAI3BuC,EAAmBC,UAAU;EAI7BD,EAAmBE,cAAc;EAIjCF,EAAmBG,OAAO;AAC7B,CAjBD,CAiBGH,MAAuBA,IAAqB,CAAE;;AAM1C,IAAII;;CACX,SAAWA;EAOPA,EAAcC,cAAc;EAM5BD,EAAcE,aAAa;AAC9B,CAdD,CAcGF,MAAkBA,IAAgB,CAAE;;AAMhC,IAAIG;;CACX,SAAWA;EAKPA,EAAgBpE,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUyD;AAC9D;AAEH,CAND,CAMGD,MAAoBA,IAAkB,CAAE;;AAKpC,IAAIE;;CACX,SAAWA;EAoBPA,EAAW9D,SAhBX,SAASA,OAAOkB,GAAOkC,GAASW,GAAUC,GAAMC,GAAQC;IACpD,IAAIlB,IAAS;MAAE9B,OAAOA;MAAOkC,SAASA;;IACtC,IAAI/C,GAAG4C,QAAQc;MACXf,EAAOe,WAAWA;;IAEtB,IAAI1D,GAAG4C,QAAQe;MACXhB,EAAOgB,OAAOA;;IAElB,IAAI3D,GAAG4C,QAAQgB;MACXjB,EAAOiB,SAASA;;IAEpB,IAAI5D,GAAG4C,QAAQiB;MACXlB,EAAOkB,qBAAqBA;;IAEhC,OAAOlB;AACX;EAiBAc,EAAWtE,KAZX,SAASA,GAAGC;IACR,IAAI0E;IACJ,IAAI/D,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MACXG,EAAMf,GAAGY,EAAUc,UACnBb,GAAG9B,OAAO6B,EAAUgD,aACnB/C,GAAG+D,OAAOhE,EAAU2D,aAAa1D,GAAGc,UAAUf,EAAU2D,eACxD1D,GAAGV,QAAQS,EAAU4D,SAAS3D,GAAG9B,OAAO6B,EAAU4D,SAAS3D,GAAGc,UAAUf,EAAU4D,WAClF3D,GAAGc,UAAUf,EAAUiE,oBAAqBhE,GAAG9B,OAA4C,UAApC4F,IAAK/D,EAAUiE,yBAAoC,MAAPF,SAAgB,IAASA,EAAGN,WAC/HxD,GAAG9B,OAAO6B,EAAU6D,WAAW5D,GAAGc,UAAUf,EAAU6D,aACtD5D,GAAGc,UAAUf,EAAU8D,uBAAuB7D,GAAGgC,WAAWjC,EAAU8D,oBAAoBhB,EAA6B1D;AACnI;AAEH,CArCD,CAqCGsE,MAAeA,IAAa,CAAE;;AAK1B,IAAIQ;;CACX,SAAWA;EAePA,EAAQtE,SAXR,SAASA,OAAOuE,GAAOC;IACnB,IAAIC,IAAO;IACX,KAAK,IAAIC,IAAK,GAAGA,IAAKC,UAAU9H,QAAQ6H;MACpCD,EAAKC,IAAK,KAAKC,UAAUD;;IAE7B,IAAI1B,IAAS;MAAEuB,OAAOA;MAAOC,SAASA;;IACtC,IAAInE,GAAG4C,QAAQwB,MAASA,EAAK5H,SAAS;MAClCmG,EAAO2B,YAAYF;;IAEvB,OAAOzB;AACX;EASAsB,EAAQ9E,KAJR,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUmE,UAAUlE,GAAG9B,OAAO6B,EAAUoE;AACtF;AAEH,CAxBD,CAwBGF,MAAYA,IAAU,CAAE;;AAKpB,IAAIlC;;CACX,SAAWA;EASPA,EAASwC,UAHT,SAASA,QAAQ1D,GAAO2D;IACpB,OAAO;MAAE3D,OAAOA;MAAO2D,SAASA;;AACpC;EAUAzC,EAAS0C,SAHT,SAASA,OAAOC,GAAUF;IACtB,OAAO;MAAE3D,OAAO;QAAEN,OAAOmE;QAAUlE,KAAKkE;;MAAYF,SAASA;;AACjE;EASAzC,EAAS4C,MAHT,SAASA,IAAI9D;IACT,OAAO;MAAEA,OAAOA;MAAO2D,SAAS;;AACpC;EAQAzC,EAAS5C,KANT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MACjBC,GAAG9B,OAAO6B,EAAUyE,YACpBtE,EAAMf,GAAGY,EAAUc;AAC9B;AAEH,CAlCD,CAkCGkB,MAAaA,IAAW,CAAE;;AACtB,IAAI6C;;CACX,SAAWA;EAWPA,EAAiBjF,SAVjB,SAASA,OAAO9B,GAAOgH,GAAmBC;IACtC,IAAInC,IAAS;MAAE9E,OAAOA;;IACtB,SAA0BiD,MAAtB+D;MACAlC,EAAOkC,oBAAoBA;;IAE/B,SAAoB/D,MAAhBgE;MACAnC,EAAOmC,cAAcA;;IAEzB,OAAOnC;AACX;EAQAiC,EAAiBzF,KANjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUlC,WACrDmC,GAAG+E,QAAQhF,EAAU8E,2BAAsD/D,MAAhCf,EAAU8E,uBACrD7E,GAAG9B,OAAO6B,EAAU+E,qBAA0ChE,MAA1Bf,EAAU+E;AACvD;AAEH,CAnBD,CAmBGF,MAAqBA,IAAmB,CAAE;;AACtC,IAAII;;CACX,SAAWA;EAKPA,EAA2B7F,KAJ3B,SAASA,GAAGC;IAER,OAAOY,GAAG9B,OADMkB;AAEpB;AAEH,CAND,CAMG4F,MAA+BA,IAA6B,CAAE;;AAC1D,IAAIC;;CACX,SAAWA;EAWPA,EAAkBV,UAHlB,SAASA,QAAQ1D,GAAO2D,GAASU;IAC7B,OAAO;MAAErE,OAAOA;MAAO2D,SAASA;MAASW,cAAcD;;AAC3D;EAYAD,EAAkBR,SAHlB,SAASA,OAAOC,GAAUF,GAASU;IAC/B,OAAO;MAAErE,OAAO;QAAEN,OAAOmE;QAAUlE,KAAKkE;;MAAYF,SAASA;MAASW,cAAcD;;AACxF;EAWAD,EAAkBN,MAHlB,SAASA,IAAI9D,GAAOqE;IAChB,OAAO;MAAErE,OAAOA;MAAO2D,SAAS;MAAIW,cAAcD;;AACtD;EAMAD,EAAkB9F,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAO2C,EAAS5C,GAAGY,OAAe6E,EAAiBzF,GAAGY,EAAUoF,iBAAiBH,EAA2B7F,GAAGY,EAAUoF;AAC7H;AAEH,CAtCD,CAsCGF,MAAsBA,IAAoB,CAAE;;AAKxC,IAAIG;;CACX,SAAWA;EAOPA,EAAiBzF,SAHjB,SAASA,OAAO0F,GAAcC;IAC1B,OAAO;MAAED,cAAcA;MAAcC,OAAOA;;AAChD;EAQAF,EAAiBjG,KANjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MACXwF,EAAwCpG,GAAGY,EAAUsF,iBACrDtI,MAAMyI,QAAQzF,EAAUuF;AACnC;AAEH,CAfD,CAeGF,MAAqBA,IAAmB,CAAE;;AACtC,IAAIK;;CACX,SAAWA;EAcPA,EAAW9F,SAbX,SAASA,OAAOiB,GAAK8E,GAASR;IAC1B,IAAIvC,IAAS;MACTrH,MAAM;MACNsF,KAAKA;;IAET,SAAgBE,MAAZ4E,WAAgD5E,MAAtB4E,EAAQC,kBAAsD7E,MAA3B4E,EAAQE;MACrEjD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOA8C,EAAWtG,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAUa,cAA+BE,MAAtBf,EAAU2F,iBACpD5E,MAAhCf,EAAU2F,QAAQC,aAA2B3F,GAAG+E,QAAQhF,EAAU2F,QAAQC,qBAAqD7E,MAArCf,EAAU2F,QAAQE,kBAAgC5F,GAAG+E,QAAQhF,EAAU2F,QAAQE,2BAAkD9E,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAC1R;AAEH,CArBD,CAqBGM,MAAeA,IAAa,CAAE;;AAC1B,IAAII;;CACX,SAAWA;EAePA,EAAWlG,SAdX,SAASA,OAAOmG,GAAQC,GAAQL,GAASR;IACrC,IAAIvC,IAAS;MACTrH,MAAM;MACNwK,QAAQA;MACRC,QAAQA;;IAEZ,SAAgBjF,MAAZ4E,WAAgD5E,MAAtB4E,EAAQC,kBAAsD7E,MAA3B4E,EAAQE;MACrEjD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOAkD,EAAW1G,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAU+F,WAAW9F,GAAG9B,OAAO6B,EAAUgG,iBAAkCjF,MAAtBf,EAAU2F,iBACtF5E,MAAhCf,EAAU2F,QAAQC,aAA2B3F,GAAG+E,QAAQhF,EAAU2F,QAAQC,qBAAqD7E,MAArCf,EAAU2F,QAAQE,kBAAgC5F,GAAG+E,QAAQhF,EAAU2F,QAAQE,2BAAkD9E,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAC1R;AAEH,CAtBD,CAsBGU,MAAeA,IAAa,CAAE;;AAC1B,IAAIG;;CACX,SAAWA;EAcPA,EAAWrG,SAbX,SAASA,OAAOiB,GAAK8E,GAASR;IAC1B,IAAIvC,IAAS;MACTrH,MAAM;MACNsF,KAAKA;;IAET,SAAgBE,MAAZ4E,WAAgD5E,MAAtB4E,EAAQO,kBAAyDnF,MAA9B4E,EAAQQ;MACrEvD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOAqD,EAAW7G,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAUa,cAA+BE,MAAtBf,EAAU2F,iBACpD5E,MAAhCf,EAAU2F,QAAQO,aAA2BjG,GAAG+E,QAAQhF,EAAU2F,QAAQO,qBAAwDnF,MAAxCf,EAAU2F,QAAQQ,qBAAmClG,GAAG+E,QAAQhF,EAAU2F,QAAQQ,8BAAqDpF,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAChS;AAEH,CArBD,CAqBGa,MAAeA,IAAa,CAAE;;AAC1B,IAAIG;;CACX,SAAWA;EAcPA,EAAchH,KAbd,SAASA,GAAGC;IAER,OADgBA,WAEW0B,MAFX1B,EAEDgH,gBAAuDtF,MAFtD1B,EAEkCiH,0BACfvF,MAHnB1B,EAGDiH,mBAHCjH,EAG0CiH,gBAAgBC,OAAM,SAAUC;MAClF,IAAIvG,GAAG9B,OAAOqI,EAAOjL;QACjB,OAAOmK,EAAWtG,GAAGoH,MAAWV,EAAW1G,GAAGoH,MAAWP,EAAW7G,GAAGoH;;QAGvE,OAAOnB,EAAiBjG,GAAGoH;;AAElC;AACT;AAEH,CAfD,CAeGJ,MAAkBA,IAAgB,CAAE;;AACvC,IAAIK,IAAoC;EACpC,SAASA,mBAAmBlB,GAAOmB;IAC/BC,KAAKpB,QAAQA;IACboB,KAAKD,oBAAoBA;AAC7B;EACAD,mBAAmBG,UAAUlC,SAAS,SAAUC,GAAUF,GAASU;IAC/D,IAAI0B;IACJ,IAAIC;IACJ,SAAmB/F,MAAfoE;MACA0B,IAAO7E,EAAS0C,OAAOC,GAAUF;WAEhC,IAAIQ,EAA2B7F,GAAG+F,IAAa;MAChD2B,IAAK3B;MACL0B,IAAO3B,EAAkBR,OAAOC,GAAUF,GAASU;AACvD,WACK;MACDwB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO7B;MACnC0B,IAAO3B,EAAkBR,OAAOC,GAAUF,GAASqC;AACvD;IACAH,KAAKpB,MAAMjJ,KAAKuK;IAChB,SAAW9F,MAAP+F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUpC,UAAU,SAAU1D,GAAO2D,GAASU;IAC7D,IAAI0B;IACJ,IAAIC;IACJ,SAAmB/F,MAAfoE;MACA0B,IAAO7E,EAASwC,QAAQ1D,GAAO2D;WAE9B,IAAIQ,EAA2B7F,GAAG+F,IAAa;MAChD2B,IAAK3B;MACL0B,IAAO3B,EAAkBV,QAAQ1D,GAAO2D,GAASU;AACrD,WACK;MACDwB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO7B;MACnC0B,IAAO3B,EAAkBV,QAAQ1D,GAAO2D,GAASqC;AACrD;IACAH,KAAKpB,MAAMjJ,KAAKuK;IAChB,SAAW9F,MAAP+F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUK,SAAS,SAAUnG,GAAOqE;IACnD,IAAI0B;IACJ,IAAIC;IACJ,SAAmB/F,MAAfoE;MACA0B,IAAO7E,EAAS4C,IAAI9D;WAEnB,IAAImE,EAA2B7F,GAAG+F,IAAa;MAChD2B,IAAK3B;MACL0B,IAAO3B,EAAkBN,IAAI9D,GAAOqE;AACxC,WACK;MACDwB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO7B;MACnC0B,IAAO3B,EAAkBN,IAAI9D,GAAOgG;AACxC;IACAH,KAAKpB,MAAMjJ,KAAKuK;IAChB,SAAW9F,MAAP+F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUM,MAAM,SAAUL;IACzCF,KAAKpB,MAAMjJ,KAAKuK;;EAEpBJ,mBAAmBG,UAAUO,MAAM;IAC/B,OAAOR,KAAKpB;;EAEhBkB,mBAAmBG,UAAUQ,QAAQ;IACjCT,KAAKpB,MAAM8B,OAAO,GAAGV,KAAKpB,MAAM9I;;EAEpCgK,mBAAmBG,UAAUG,0BAA0B,SAAU1H;IAC7D,SAAc0B,MAAV1B;MACA,MAAM,IAAIqB,MAAM;;;EAGxB,OAAO+F;AACX,CAhFwC;;AAoFxC,IAAIa,IAAmC;EACnC,SAASA,kBAAkBC;IACvBZ,KAAKa,oBAA+BzG,MAAhBwG,IAA4B1K,OAAO+C,OAAO,QAAQ2H;IACtEZ,KAAKc,WAAW;IAChBd,KAAKe,QAAQ;AACjB;EACAJ,kBAAkBV,UAAUO,MAAM;IAC9B,OAAOR,KAAKa;;EAEhB3K,OAAO8K,eAAeL,kBAAkBV,WAAW,QAAQ;IACvDgB,KAAK;MACD,OAAOjB,KAAKe;AACf;IACDG,aAAY;IACZC,eAAc;;EAElBR,kBAAkBV,UAAUI,SAAS,SAAUe,GAAgB5C;IAC3D,IAAI2B;IACJ,IAAI7B,EAA2B7F,GAAG2I;MAC9BjB,IAAKiB;WAEJ;MACDjB,IAAKH,KAAKqB;MACV7C,IAAa4C;AACjB;IACA,SAA8BhH,MAA1B4F,KAAKa,aAAaV;MAClB,MAAM,IAAIpG,MAAM,MAAMC,OAAOmG,GAAI;;IAErC,SAAmB/F,MAAfoE;MACA,MAAM,IAAIzE,MAAM,iCAAiCC,OAAOmG;;IAE5DH,KAAKa,aAAaV,KAAM3B;IACxBwB,KAAKe;IACL,OAAOZ;;EAEXQ,kBAAkBV,UAAUoB,SAAS;IACjCrB,KAAKc;IACL,OAAOd,KAAKc,SAASQ;;EAEzB,OAAOX;AACX,CAxCuC;;CA4CjB;EAClB,SAASY,gBAAgBC;IACrB,IAAIC,IAAQzB;IACZA,KAAK0B,mBAAmBxL,OAAO+C,OAAO;IACtC,SAAsBmB,MAAlBoH,GAA6B;MAC7BxB,KAAK2B,iBAAiBH;MACtB,IAAIA,EAAc7B,iBAAiB;QAC/BK,KAAK4B,qBAAqB,IAAIjB,EAAkBa,EAAczB;QAC9DyB,EAAczB,oBAAoBC,KAAK4B,mBAAmBpB;QAC1DgB,EAAc7B,gBAAgBkC,SAAQ,SAAUhC;UAC5C,IAAInB,EAAiBjG,GAAGoH,IAAS;YAC7B,IAAIiC,IAAiB,IAAIhC,EAAmBD,EAAOjB,OAAO6C,EAAMG;YAChEH,EAAMC,iBAAiB7B,EAAOlB,aAAazE,OAAO4H;AACtD;AACJ;AACJ,aACK,IAAIN,EAAc9B;QACnBxJ,OAAOD,KAAKuL,EAAc9B,SAASmC,SAAQ,SAAUE;UACjD,IAAID,IAAiB,IAAIhC,EAAmB0B,EAAc9B,QAAQqC;UAClEN,EAAMC,iBAAiBK,KAAOD;AAClC;;AAER;MAEI9B,KAAK2B,iBAAiB;;AAE9B;EACAzL,OAAO8K,eAAeO,gBAAgBtB,WAAW,QAAQ;IAKrDgB,KAAK;MACDjB,KAAKgC;MACL,SAAgC5H,MAA5B4F,KAAK4B;QACL,IAAqC,MAAjC5B,KAAK4B,mBAAmBK;UACxBjC,KAAK2B,eAAe5B,yBAAoB3F;;UAGxC4F,KAAK2B,eAAe5B,oBAAoBC,KAAK4B,mBAAmBpB;;;MAGxE,OAAOR,KAAK2B;AACf;IACDT,aAAY;IACZC,eAAc;;EAElBI,gBAAgBtB,UAAUiC,oBAAoB,SAAUH;IACpD,IAAIlD,EAAwCpG,GAAGsJ,IAAM;MACjD/B,KAAKgC;MACL,SAA4C5H,MAAxC4F,KAAK2B,eAAehC;QACpB,MAAM,IAAI5F,MAAM;;MAEpB,IAAI4E,IAAe;QAAEzE,KAAK6H,EAAI7H;QAAKiI,SAASJ,EAAII;;MAEhD,MADIlG,IAAS+D,KAAK0B,iBAAiB/C,EAAazE,OACnC;QAMT8F,KAAK2B,eAAehC,gBAAgBhK,KAJb;UACnBgJ,cAAcA;UACdC,OAHAA,IAAQ;;QAMZ3C,IAAS,IAAI6D,EAAmBlB,GAAOoB,KAAK4B;QAC5C5B,KAAK0B,iBAAiB/C,EAAazE,OAAO+B;AAC9C;MACA,OAAOA;AACX,WACK;MACD+D,KAAKoC;MACL,SAAoChI,MAAhC4F,KAAK2B,eAAejC;QACpB,MAAM,IAAI3F,MAAM;;MAEpB,IAAIkC;MACJ,MADIA,IAAS+D,KAAK0B,iBAAiBK,KACtB;QACT,IAAInD;QACJoB,KAAK2B,eAAejC,QAAQqC,KADxBnD,IAAQ;QAEZ3C,IAAS,IAAI6D,EAAmBlB;QAChCoB,KAAK0B,iBAAiBK,KAAO9F;AACjC;MACA,OAAOA;AACX;;EAEJsF,gBAAgBtB,UAAU+B,sBAAsB;IAC5C,SAA4C5H,MAAxC4F,KAAK2B,eAAehC,wBAAiEvF,MAAhC4F,KAAK2B,eAAejC,SAAuB;MAChGM,KAAK4B,qBAAqB,IAAIjB;MAC9BX,KAAK2B,eAAehC,kBAAkB;MACtCK,KAAK2B,eAAe5B,oBAAoBC,KAAK4B,mBAAmBpB;AACpE;;EAEJe,gBAAgBtB,UAAUmC,cAAc;IACpC,SAA4ChI,MAAxC4F,KAAK2B,eAAehC,wBAAiEvF,MAAhC4F,KAAK2B,eAAejC;MACzEM,KAAK2B,eAAejC,UAAUxJ,OAAO+C,OAAO;;;EAGpDsI,gBAAgBtB,UAAUoC,aAAa,SAAUnI,GAAKoI,GAAqBtD;IACvEgB,KAAKgC;IACL,SAA4C5H,MAAxC4F,KAAK2B,eAAehC;MACpB,MAAM,IAAI5F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG6J,MAAwBhE,EAA2B7F,GAAG6J;MAC1E9D,IAAa8D;;MAGbtD,IAAUsD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB/F,MAAfoE;MACA+D,IAAYxD,EAAW9F,OAAOiB,GAAK8E;WAElC;MACDmB,IAAK7B,EAA2B7F,GAAG+F,KAAcA,IAAawB,KAAK4B,mBAAmBvB,OAAO7B;MAC7F+D,IAAYxD,EAAW9F,OAAOiB,GAAK8E,GAASmB;AAChD;IACAH,KAAK2B,eAAehC,gBAAgBhK,KAAK4M;IACzC,SAAWnI,MAAP+F;MACA,OAAOA;;;EAGfoB,gBAAgBtB,UAAUuC,aAAa,SAAUpD,GAAQC,GAAQiD,GAAqBtD;IAClFgB,KAAKgC;IACL,SAA4C5H,MAAxC4F,KAAK2B,eAAehC;MACpB,MAAM,IAAI5F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG6J,MAAwBhE,EAA2B7F,GAAG6J;MAC1E9D,IAAa8D;;MAGbtD,IAAUsD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB/F,MAAfoE;MACA+D,IAAYpD,EAAWlG,OAAOmG,GAAQC,GAAQL;WAE7C;MACDmB,IAAK7B,EAA2B7F,GAAG+F,KAAcA,IAAawB,KAAK4B,mBAAmBvB,OAAO7B;MAC7F+D,IAAYpD,EAAWlG,OAAOmG,GAAQC,GAAQL,GAASmB;AAC3D;IACAH,KAAK2B,eAAehC,gBAAgBhK,KAAK4M;IACzC,SAAWnI,MAAP+F;MACA,OAAOA;;;EAGfoB,gBAAgBtB,UAAUwC,aAAa,SAAUvI,GAAKoI,GAAqBtD;IACvEgB,KAAKgC;IACL,SAA4C5H,MAAxC4F,KAAK2B,eAAehC;MACpB,MAAM,IAAI5F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG6J,MAAwBhE,EAA2B7F,GAAG6J;MAC1E9D,IAAa8D;;MAGbtD,IAAUsD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB/F,MAAfoE;MACA+D,IAAYjD,EAAWrG,OAAOiB,GAAK8E;WAElC;MACDmB,IAAK7B,EAA2B7F,GAAG+F,KAAcA,IAAawB,KAAK4B,mBAAmBvB,OAAO7B;MAC7F+D,IAAYjD,EAAWrG,OAAOiB,GAAK8E,GAASmB;AAChD;IACAH,KAAK2B,eAAehC,gBAAgBhK,KAAK4M;IACzC,SAAWnI,MAAP+F;MACA,OAAOA;;;AAInB,CA7KsB;;AAmLf,IAAIuC;;CACX,SAAWA;EAQPA,EAAuBzJ,SAHvB,SAASA,OAAOiB;IACZ,OAAO;MAAEA,KAAKA;;AAClB;EASAwI,EAAuBjK,KAJvB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa;AACxD;AAEH,CAjBD,CAiBGwI,MAA2BA,IAAyB,CAAE;;AAKlD,IAAIC;;CACX,SAAWA;EASPA,EAAgC1J,SAHhC,SAASA,OAAOiB,GAAKiI;IACjB,OAAO;MAAEjI,KAAKA;MAAKiI,SAASA;;AAChC;EASAQ,EAAgClK,KAJhC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAGV,QAAQS,EAAU8I;AACrF;AAEH,CAlBD,CAkBGQ,MAAoCA,IAAkC,CAAE;;AAKpE,IAAI9D;;CACX,SAAWA;EASPA,EAAwC5F,SAHxC,SAASA,OAAOiB,GAAKiI;IACjB,OAAO;MAAEjI,KAAKA;MAAKiI,SAASA;;AAChC;EASAtD,EAAwCpG,KAJxC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,SAA+B,SAAtBb,EAAU8I,WAAoB7I,GAAGV,QAAQS,EAAU8I;AACpH;AAEH,CAlBD,CAkBGtD,MAA4CA,IAA0C,CAAE;;AAKpF,IAAI+D;;CACX,SAAWA;EAWPA,EAAiB3J,SAHjB,SAASA,OAAOiB,GAAK2I,GAAYV,GAASzL;IACtC,OAAO;MAAEwD,KAAKA;MAAK2I,YAAYA;MAAYV,SAASA;MAASzL,MAAMA;;AACvE;EASAkM,EAAiBnK,KAJjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAG9B,OAAO6B,EAAUwJ,eAAevJ,GAAGV,QAAQS,EAAU8I,YAAY7I,GAAG9B,OAAO6B,EAAU3C;AACxJ;AAEH,CApBD,CAoBGkM,MAAqBA,IAAmB,CAAE;;AAQtC,IAAIE;;CACX,SAAWA;EAIPA,EAAWC,YAAY;EAIvBD,EAAWE,WAAW;EAQtBF,EAAWrK,KAJX,SAASA,GAAGC;IAER,OADgBA,MACKoK,EAAWC,aADhBrK,MAC2CoK,EAAWE;AAC1E;AAEH,CAjBD,CAiBGF,MAAeA,IAAa,CAAE;;AAC1B,IAAIG;;CACX,SAAWA;EAQPA,EAAcxK,KAJd,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcb,MAAUoK,EAAWrK,GAAGY,EAAUzE,SAAS0E,GAAG9B,OAAO6B,EAAUX;AAC3F;AAEH,CATD,CASGuK,MAAkBA,IAAgB,CAAE;;AAIhC,IAAIC;;CACX,SAAWA;EACPA,EAAmBC,OAAO;EAC1BD,EAAmBE,SAAS;EAC5BF,EAAmBG,WAAW;EAC9BH,EAAmBI,cAAc;EACjCJ,EAAmBK,QAAQ;EAC3BL,EAAmBM,WAAW;EAC9BN,EAAmBO,QAAQ;EAC3BP,EAAmBQ,YAAY;EAC/BR,EAAmBS,SAAS;EAC5BT,EAAmBU,WAAW;EAC9BV,EAAmBW,OAAO;EAC1BX,EAAmBY,QAAQ;EAC3BZ,EAAmBa,OAAO;EAC1Bb,EAAmBc,UAAU;EAC7Bd,EAAmBe,UAAU;EAC7Bf,EAAmBxI,QAAQ;EAC3BwI,EAAmBgB,OAAO;EAC1BhB,EAAmBiB,YAAY;EAC/BjB,EAAmBkB,SAAS;EAC5BlB,EAAmBmB,aAAa;EAChCnB,EAAmBoB,WAAW;EAC9BpB,EAAmBqB,SAAS;EAC5BrB,EAAmBsB,QAAQ;EAC3BtB,EAAmBuB,WAAW;EAC9BvB,EAAmBwB,gBAAgB;AACtC,CA1BD,CA0BGxB,MAAuBA,IAAqB,CAAE;;AAK1C,IAAIyB;;CACX,SAAWA;EAIPA,EAAiB5B,YAAY;EAW7B4B,EAAiBV,UAAU;AAC9B,CAhBD,CAgBGU,MAAqBA,IAAmB,CAAE;;AAOtC,IAAIC;;CACX,SAAWA;EAIPA,EAAkBhI,aAAa;AAClC,CALD,CAKGgI,MAAsBA,IAAoB,CAAE;;AAMxC,IAAIC;;CACX,SAAWA;EAOPA,EAAkB5L,SAHlB,SAASA,OAAO6E,GAASC,GAAQF;IAC7B,OAAO;MAAEC,SAASA;MAASC,QAAQA;MAAQF,SAASA;;AACxD;EASAgH,EAAkBpM,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAaC,GAAG9B,OAAO6B,EAAUyE,YAAYtE,EAAMf,GAAGY,EAAU0E,WAAWvE,EAAMf,GAAGY,EAAUwE;AACzG;AAEH,CAhBD,CAgBGgH,MAAsBA,IAAoB,CAAE;;AAOxC,IAAIC;;CACX,SAAWA;EAQPA,EAAeC,OAAO;EAUtBD,EAAeE,oBAAoB;AACtC,CAnBD,CAmBGF,MAAmBA,IAAiB,CAAE;;AAClC,IAAIG;;CACX,SAAWA;EAMPA,EAA2BxM,KAL3B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,MAAcC,GAAG9B,OAAO6B,EAAU6L,gBAAgC9K,MAArBf,EAAU6L,YACzD5L,GAAG9B,OAAO6B,EAAU+E,qBAA0ChE,MAA1Bf,EAAU+E;AACvD;AAEH,CAPD,CAOG6G,MAA+BA,IAA6B,CAAE;;AAK1D,IAAIE;;CACX,SAAWA;EAQPA,EAAelM,SAHf,SAASA,OAAO9B;IACZ,OAAO;MAAEA,OAAOA;;AACpB;AAEH,CATD,CASGgO,MAAmBA,IAAiB,CAAE;;AAKlC,IAAIC;;CACX,SAAWA;EAUPA,EAAenM,SAHf,SAASA,OAAOoM,GAAOC;IACnB,OAAO;MAAED,OAAOA,IAAQA,IAAQ;MAAIC,gBAAgBA;;AACxD;AAEH,CAXD,CAWGF,MAAmBA,IAAiB,CAAE;;AAClC,IAAIG;;CACX,SAAWA;EASPA,EAAaC,gBAHb,SAASA,cAAcC;IACnB,OAAOA,EAAU5H,QAAQ,yBAAyB;AACtD;EASA0H,EAAa9M,KAJb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG9B,OAAO6B,MAAeC,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUqM,aAAapM,GAAG9B,OAAO6B,EAAUX;AACxH;AAEH,CAlBD,CAkBG6M,MAAiBA,IAAe,CAAE;;AAC9B,IAAII;;CACX,SAAWA;EAUPA,EAAMlN,KANN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,SAASW,KAAaC,GAAGC,cAAcF,OAAe4J,EAAcxK,GAAGY,EAAUzG,aAC7E2S,EAAa9M,GAAGY,EAAUzG,aAC1B0G,GAAGgC,WAAWjC,EAAUzG,UAAU2S,EAAa9M,cAAyB2B,MAAhB1B,EAAMyB,SAAuBX,EAAMf,GAAGC,EAAMyB;AAC5G;AAEH,CAXD,CAWGwL,MAAUA,IAAQ,CAAE;;AAKhB,IAAIC;;CACX,SAAWA;EAUPA,EAAqB3M,SAHrB,SAASA,OAAO9B,GAAO0O;IACnB,OAAOA,IAAgB;MAAE1O,OAAOA;MAAO0O,eAAeA;QAAkB;MAAE1O,OAAOA;;AACrF;AAEH,CAXD,CAWGyO,MAAyBA,IAAuB,CAAE;;AAK9C,IAAIE;;CACX,SAAWA;EAkBPA,EAAqB7M,SAjBrB,SAASA,OAAO9B,GAAO0O;IACnB,IAAIE,IAAa;IACjB,KAAK,IAAIpI,IAAK,GAAGA,IAAKC,UAAU9H,QAAQ6H;MACpCoI,EAAWpI,IAAK,KAAKC,UAAUD;;IAEnC,IAAI1B,IAAS;MAAE9E,OAAOA;;IACtB,IAAImC,GAAG4C,QAAQ2J;MACX5J,EAAO4J,gBAAgBA;;IAE3B,IAAIvM,GAAG4C,QAAQ6J;MACX9J,EAAO8J,aAAaA;;MAGpB9J,EAAO8J,aAAa;;IAExB,OAAO9J;AACX;AAEH,CAnBD,CAmBG6J,MAAyBA,IAAuB,CAAE;;AAI9C,IAAIE;;CACX,SAAWA;EAIPA,EAAsB7C,OAAO;EAI7B6C,EAAsBC,OAAO;EAI7BD,EAAsBE,QAAQ;AACjC,CAbD,CAaGF,MAA0BA,IAAwB,CAAE;;AAKhD,IAAIG;;CACX,SAAWA;EAaPA,EAAkBlN,SAPlB,SAASA,OAAOkB,GAAOvF;IACnB,IAAIqH,IAAS;MAAE9B,OAAOA;;IACtB,IAAIb,GAAG+D,OAAOzI;MACVqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;AAEH,CAdD,CAcGkK,OAAsBA,KAAoB,CAAE;;AAIxC,IAAIC;;CACX,SAAWA;EACPA,EAAWlC,OAAO;EAClBkC,EAAWzC,SAAS;EACpByC,EAAWC,YAAY;EACvBD,EAAWE,UAAU;EACrBF,EAAW3C,QAAQ;EACnB2C,EAAWhD,SAAS;EACpBgD,EAAWxC,WAAW;EACtBwC,EAAW7C,QAAQ;EACnB6C,EAAW9C,cAAc;EACzB8C,EAAWrC,OAAO;EAClBqC,EAAW1C,YAAY;EACvB0C,EAAW/C,WAAW;EACtB+C,EAAW5C,WAAW;EACtB4C,EAAW9B,WAAW;EACtB8B,EAAWG,SAAS;EACpBH,EAAWhN,SAAS;EACpBgN,EAAWI,UAAU;EACrBJ,EAAW/P,QAAQ;EACnB+P,EAAWlQ,SAAS;EACpBkQ,EAAWK,MAAM;EACjBL,EAAWM,OAAO;EAClBN,EAAW/B,aAAa;EACxB+B,EAAW7B,SAAS;EACpB6B,EAAW5B,QAAQ;EACnB4B,EAAW3B,WAAW;EACtB2B,EAAW1B,gBAAgB;AAC9B,CA3BD,CA2BG0B,OAAeA,KAAa,CAAE;;AAM1B,IAAIO;;CACX,SAAWA;EAIPA,EAAU/J,aAAa;AAC1B,CALD,CAKG+J,OAAcA,KAAY,CAAE;;AACxB,IAAIC;;CACX,SAAWA;EAqBPA,EAAkB3N,SAXlB,SAASA,OAAO/D,GAAMN,GAAMuF,GAAOD,GAAK2M;IACpC,IAAI5K,IAAS;MACT/G,MAAMA;MACNN,MAAMA;MACNwH,UAAU;QAAElC,KAAKA;QAAKC,OAAOA;;;IAEjC,IAAI0M;MACA5K,EAAO4K,gBAAgBA;;IAE3B,OAAO5K;AACX;AAEH,CAtBD,CAsBG2K,OAAsBA,KAAoB,CAAE;;AACxC,IAAIE;;CACX,SAAWA;EAePA,EAAgB7N,SALhB,SAASA,OAAO/D,GAAMN,GAAMsF,GAAKC;IAC7B,YAAiBC,MAAVD,IACD;MAAEjF,MAAMA;MAAMN,MAAMA;MAAMwH,UAAU;QAAElC,KAAKA;QAAKC,OAAOA;;QACvD;MAAEjF,MAAMA;MAAMN,MAAMA;MAAMwH,UAAU;QAAElC,KAAKA;;;AACrD;AAEH,CAhBD,CAgBG4M,OAAoBA,KAAkB,CAAE;;AACpC,IAAIC;;CACX,SAAWA;EAwBPA,EAAe9N,SAbf,SAASA,OAAO/D,GAAMgQ,GAAQtQ,GAAMuF,GAAO6M,GAAgBC;IACvD,IAAIhL,IAAS;MACT/G,MAAMA;MACNgQ,QAAQA;MACRtQ,MAAMA;MACNuF,OAAOA;MACP6M,gBAAgBA;;IAEpB,SAAiB5M,MAAb6M;MACAhL,EAAOgL,WAAWA;;IAEtB,OAAOhL;AACX;EAeA8K,EAAetO,KAVf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KACHC,GAAG9B,OAAO6B,EAAUnE,SAASoE,GAAG+D,OAAOhE,EAAUzE,SACjD4E,EAAMf,GAAGY,EAAUc,UAAUX,EAAMf,GAAGY,EAAU2N,yBAC1B5M,MAArBf,EAAU6L,UAAwB5L,GAAG9B,OAAO6B,EAAU6L,kBAC7B9K,MAAzBf,EAAU6N,cAA4B5N,GAAG+E,QAAQhF,EAAU6N,sBACpC9M,MAAvBf,EAAU4N,YAA0B5Q,MAAMyI,QAAQzF,EAAU4N,oBACzC7M,MAAnBf,EAAU8N,QAAsB9Q,MAAMyI,QAAQzF,EAAU8N;AACjE;AAEH,CAvCD,CAuCGJ,OAAmBA,KAAiB,CAAE;;AAIlC,IAAIK;;CACX,SAAWA;EAIPA,EAAeC,QAAQ;EAIvBD,EAAeE,WAAW;EAI1BF,EAAeG,WAAW;EAY1BH,EAAeI,kBAAkB;EAWjCJ,EAAeK,iBAAiB;EAahCL,EAAeM,kBAAkB;EAMjCN,EAAeO,SAAS;EAIxBP,EAAeQ,wBAAwB;EASvCR,EAAeS,eAAe;AACjC,CApED,CAoEGT,OAAmBA,KAAiB,CAAE;;AAMlC,IAAIU;;CACX,SAAWA;EAIPA,EAAsBC,UAAU;EAOhCD,EAAsBE,YAAY;AACrC,CAZD,CAYGF,OAA0BA,KAAwB,CAAE;;AAKhD,IAAIG;;CACX,SAAWA;EAcPA,EAAkBhP,SAVlB,SAASA,OAAOiP,GAAaC,GAAMC;IAC/B,IAAInM,IAAS;MAAEiM,aAAaA;;IAC5B,IAAIC;MACAlM,EAAOkM,OAAOA;;IAElB,IAAIC;MACAnM,EAAOmM,cAAcA;;IAEzB,OAAOnM;AACX;EAWAgM,EAAkBxP,KANlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAGgC,WAAWjC,EAAU6O,aAAanL,EAAWtE,aACrD2B,MAAnBf,EAAU8O,QAAsB7O,GAAGgC,WAAWjC,EAAU8O,MAAM7O,GAAG9B,kBACvC4C,MAA1Bf,EAAU+O,eAA6B/O,EAAU+O,gBAAgBN,GAAsBC,WAAW1O,EAAU+O,gBAAgBN,GAAsBE;AAC9J;AAEH,CAzBD,CAyBGC,OAAsBA,KAAoB,CAAE;;AACxC,IAAII;;CACX,SAAWA;EAmBPA,EAAWpP,SAlBX,SAASA,OAAOuE,GAAO8K,GAAqB1T;IACxC,IAAIqH,IAAS;MAAEuB,OAAOA;;IACtB,IAAI+K,KAAY;IAChB,IAAmC,mBAAxBD,GAAkC;MACzCC,KAAY;MACZtM,EAAOrH,OAAO0T;AACjB,WACI,IAAI/K,EAAQ9E,GAAG6P;MAChBrM,EAAOwB,UAAU6K;;MAGjBrM,EAAOiE,OAAOoI;;IAElB,IAAIC,UAAsBnO,MAATxF;MACbqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;EAYAoM,EAAW5P,KAVX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAaC,GAAG9B,OAAO6B,EAAUmE,gBACTpD,MAA1Bf,EAAU6O,eAA6B5O,GAAGgC,WAAWjC,EAAU6O,aAAanL,EAAWtE,cACpE2B,MAAnBf,EAAUzE,QAAsB0E,GAAG9B,OAAO6B,EAAUzE,gBACjCwF,MAAnBf,EAAU6G,aAA4C9F,MAAtBf,EAAUoE,kBACpBrD,MAAtBf,EAAUoE,WAAyBF,EAAQ9E,GAAGY,EAAUoE,mBAC9BrD,MAA1Bf,EAAUmP,eAA6BlP,GAAG+E,QAAQhF,EAAUmP,uBACzCpO,MAAnBf,EAAU6G,QAAsBT,EAAchH,GAAGY,EAAU6G;AACpE;AAEH,CA/BD,CA+BGmI,OAAeA,KAAa,CAAE;;AAK1B,IAAII;;CACX,SAAWA;EAWPA,EAASxP,SAPT,SAASA,OAAOkB,GAAOuO;IACnB,IAAIzM,IAAS;MAAE9B,OAAOA;;IACtB,IAAIb,GAAG4C,QAAQwM;MACXzM,EAAOyM,OAAOA;;IAElB,OAAOzM;AACX;EASAwM,EAAShQ,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAGc,UAAUf,EAAUoE,YAAYF,EAAQ9E,GAAGY,EAAUoE;AAC1H;AAEH,CApBD,CAoBGgL,OAAaA,KAAW,CAAE;;AAKtB,IAAIE;;CACX,SAAWA;EAOPA,EAAkB1P,SAHlB,SAASA,OAAO2P,GAASC;IACrB,OAAO;MAAED,SAASA;MAASC,cAAcA;;AAC7C;EASAF,EAAkBlQ,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAGP,SAASM,EAAUuP,YAAYtP,GAAG+E,QAAQhF,EAAUwP;AAC3F;AAEH,CAhBD,CAgBGF,OAAsBA,KAAoB,CAAE;;AAKxC,IAAIG;;CACX,SAAWA;EAOPA,EAAa7P,SAHb,SAASA,OAAOkB,GAAOxH,GAAQ+V;IAC3B,OAAO;MAAEvO,OAAOA;MAAOxH,QAAQA;MAAQ+V,MAAMA;;AACjD;EASAI,EAAarQ,KAJb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAGc,UAAUf,EAAU1G,WAAW2G,GAAG9B,OAAO6B,EAAU1G;AACxH;AAEH,CAhBD,CAgBGmW,OAAiBA,KAAe,CAAE;;AAK9B,IAAIC;;CACX,SAAWA;EASPA,EAAe9P,SAHf,SAASA,OAAOkB,GAAO6O;IACnB,OAAO;MAAE7O,OAAOA;MAAO6O,QAAQA;;AACnC;EAMAD,EAAetQ,KAJf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,gBAAgCC,MAArBf,EAAU2P,UAAwBD,EAAetQ,GAAGY,EAAU2P;AACtI;AAEH,CAfD,CAeGD,OAAmBA,KAAiB,CAAE;;AAQlC,IAAIE;;CACX,SAAWA;EACPA,EAA8B,YAAI;EAKlCA,EAAyB,OAAI;EAC7BA,EAA0B,QAAI;EAC9BA,EAAyB,OAAI;EAC7BA,EAA8B,YAAI;EAClCA,EAA2B,SAAI;EAC/BA,EAAkC,gBAAI;EACtCA,EAA8B,YAAI;EAClCA,EAA6B,WAAI;EACjCA,EAA6B,WAAI;EACjCA,EAA+B,aAAI;EACnCA,EAA0B,QAAI;EAC9BA,EAA6B,WAAI;EACjCA,EAA2B,SAAI;EAC/BA,EAA0B,QAAI;EAC9BA,EAA4B,UAAI;EAChCA,EAA6B,WAAI;EACjCA,EAA4B,UAAI;EAChCA,EAA2B,SAAI;EAC/BA,EAA2B,SAAI;EAC/BA,EAA2B,SAAI;EAC/BA,EAA6B,WAAI;EAIjCA,EAA8B,YAAI;AACrC,CA/BD,CA+BGA,OAAuBA,KAAqB,CAAE;;AAQ1C,IAAIC;;CACX,SAAWA;EACPA,EAAoC,cAAI;EACxCA,EAAmC,aAAI;EACvCA,EAAiC,WAAI;EACrCA,EAA+B,SAAI;EACnCA,EAAmC,aAAI;EACvCA,EAAiC,WAAI;EACrCA,EAA8B,QAAI;EAClCA,EAAqC,eAAI;EACzCA,EAAsC,gBAAI;EAC1CA,EAAuC,iBAAI;AAC9C,CAXD,CAWGA,OAA2BA,KAAyB,CAAE;;AAIlD,IAAIC;;CACX,SAAWA;EAMPA,EAAe1Q,KALf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,YAAsCe,MAAvBf,EAAU+P,YAAwD,mBAAvB/P,EAAU+P,aACxF/S,MAAMyI,QAAQzF,EAAUqP,UAAoC,MAA1BrP,EAAUqP,KAAK5S,UAA6C,mBAAtBuD,EAAUqP,KAAK;AAC/F;AAEH,CAPD,CAOGS,OAAmBA,KAAiB,CAAE;;AAMlC,IAAIE;;CACX,SAAWA;EAOPA,EAAgBpQ,SAHhB,SAASA,OAAOkB,GAAOzD;IACnB,OAAO;MAAEyD,OAAOA;MAAOzD,MAAMA;;AACjC;EAMA2S,EAAgB5Q,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,UAAUb,GAAG9B,OAAO6B,EAAU3C;AAC7G;AAEH,CAbD,CAaG2S,OAAoBA,KAAkB,CAAE;;AAMpC,IAAIC;;CACX,SAAWA;EAOPA,EAA0BrQ,SAH1B,SAASA,OAAOkB,GAAOoP,GAAcC;IACjC,OAAO;MAAErP,OAAOA;MAAOoP,cAAcA;MAAcC,qBAAqBA;;AAC5E;EAOAF,EAA0B7Q,KAL1B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,UAAUb,GAAG+E,QAAQhF,EAAUmQ,yBAClGlQ,GAAG9B,OAAO6B,EAAUkQ,sBAA4CnP,MAA3Bf,EAAUkQ;AAC3D;AAEH,CAdD,CAcGD,OAA8BA,KAA4B,CAAE;;AAMxD,IAAIG;;CACX,SAAWA;EAOPA,EAAiCxQ,SAHjC,SAASA,OAAOkB,GAAOuP;IACnB,OAAO;MAAEvP,OAAOA;MAAOuP,YAAYA;;AACvC;EAOAD,EAAiChR,KALjC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,WACnEb,GAAG9B,OAAO6B,EAAUqQ,oBAAwCtP,MAAzBf,EAAUqQ;AACzD;AAEH,CAdD,CAcGD,OAAqCA,KAAmC,CAAE;;AAOtE,IAAIE;;CACX,SAAWA;EAOPA,EAAmB1Q,SAHnB,SAASA,OAAO2Q,GAASC;IACrB,OAAO;MAAED,SAASA;MAASC,iBAAiBA;;AAChD;EASAF,EAAmBlR,KAJnB,SAASA,GAAGC;IAER,OAAOY,GAAG4C,QADMxD,MACgBc,EAAMf,GAAGC,EAAMmR;AACnD;AAEH,CAhBD,CAgBGF,OAAuBA,KAAqB,CAAE;;AAM1C,IAAIG;;CACX,SAAWA;EAIPA,EAAcC,OAAO;EAIrBD,EAAcE,YAAY;EAI1BF,EAAcrR,KAHd,SAASA,GAAGC;IACR,OAAiB,MAAVA,KAAyB,MAAVA;AAC1B;AAEH,CAbD,CAaGoR,OAAkBA,KAAgB,CAAE;;AAChC,IAAIG;;CACX,SAAWA;EAIPA,EAAmBhR,SAHnB,SAASA,OAAOP;IACZ,OAAO;MAAEA,OAAOA;;AACpB;EASAuR,EAAmBxR,KAPnB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,YACMe,MAAtBf,EAAU6Q,WAAyB5Q,GAAG9B,OAAO6B,EAAU6Q,YAAYjH,EAAcxK,GAAGY,EAAU6Q,mBACvE9P,MAAvBf,EAAU+C,YAA0BnC,EAASxB,GAAGY,EAAU+C,oBACpChC,MAAtBf,EAAUoE,WAAyBF,EAAQ9E,GAAGY,EAAUoE;AACpE;AAEH,CAbD,CAaGwM,OAAuBA,KAAqB,CAAE;;AAC1C,IAAIE;;CACX,SAAWA;EAQPA,EAAUlR,SAPV,SAASA,OAAO+E,GAAU7G,GAAOvC;IAC7B,IAAIqH,IAAS;MAAE+B,UAAUA;MAAU7G,OAAOA;;IAC1C,SAAaiD,MAATxF;MACAqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;EAYAkO,EAAU1R,KAVV,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcL,EAASP,GAAGY,EAAU2E,cACpD1E,GAAG9B,OAAO6B,EAAUlC,UAAUmC,GAAGgC,WAAWjC,EAAUlC,OAAO8S,GAAmBxR,cAC7D2B,MAAnBf,EAAUzE,QAAsBkV,GAAcrR,GAAGY,EAAUzE,eACnCwF,MAAxBf,EAAU+Q,aAA4B9Q,GAAGgC,WAAWjC,EAAU+Q,WAAW/O,EAAS5C,aAC5D2B,MAAtBf,EAAU6Q,WAAyB5Q,GAAG9B,OAAO6B,EAAU6Q,YAAYjH,EAAcxK,GAAGY,EAAU6Q,mBACpE9P,MAA1Bf,EAAUgR,eAA6B/Q,GAAG+E,QAAQhF,EAAUgR,uBACjCjQ,MAA3Bf,EAAUiR,gBAA8BhR,GAAG+E,QAAQhF,EAAUiR;AACzE;AAEH,CApBD,CAoBGH,OAAcA,KAAY,CAAE;;AACxB,IAAII;;CACX,SAAWA;EAKPA,EAAgB9R,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcV,EAAIF,GAAGY,EAAUa,QAAQZ,GAAG9B,OAAO6B,EAAUnE;AACvF;AAEH,CAND,CAMGqV,OAAoBA,KAAkB,CAAE;;AAKpC,IAAIC;;CACX,SAAWA;EAWPA,EAAavR,SAHb,SAASA,OAAOiB,GAAK2I,GAAYV,GAASsI;IACtC,OAAO,IAAIC,GAAiBxQ,GAAK2I,GAAYV,GAASsI;AAC1D;EAUAD,EAAa/R,KALb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,SAASZ,GAAGc,UAAUf,EAAUwJ,eAAevJ,GAAG9B,OAAO6B,EAAUwJ,gBAAgBvJ,GAAGP,SAASM,EAAUsR,cACtJrR,GAAGsR,KAAKvR,EAAUwR,YAAYvR,GAAGsR,KAAKvR,EAAUyR,eAAexR,GAAGsR,KAAKvR,EAAU0R,aAAY,KAAO;AAC/G;EA0BAP,EAAaQ,aAxBb,SAASA,WAAWC,GAAUrM;IAC1B,IAAIlI,IAAOuU,EAASJ;IACpB,IAAIK,IAAcC,UAAUvM,IAAO,SAAUtH,GAAGC;MAC5C,IAAI6T,IAAO9T,EAAE6C,MAAMN,MAAMX,OAAO3B,EAAE4C,MAAMN,MAAMX;MAC9C,IAAa,MAATkS;QACA,OAAO9T,EAAE6C,MAAMN,MAAMV,YAAY5B,EAAE4C,MAAMN,MAAMV;;MAEnD,OAAOiS;AACX;IACA,IAAIC,IAAqB3U,EAAKZ;IAC9B,KAAK,IAAID,IAAIqV,EAAYpV,SAAS,GAAGD,KAAK,GAAGA,KAAK;MAC9C,IAAIyV,IAAIJ,EAAYrV;MACpB,IAAI0V,IAAcN,EAASF,SAASO,EAAEnR,MAAMN;MAC5C,IAAI2R,IAAYP,EAASF,SAASO,EAAEnR,MAAML;MAC1C,IAAI0R,KAAaH;QACb3U,IAAOA,EAAK+U,UAAU,GAAGF,KAAeD,EAAExN,UAAUpH,EAAK+U,UAAUD,GAAW9U,EAAKZ;;QAGnF,MAAM,IAAIiE,MAAM;;MAEpBsR,IAAqBE;AACzB;IACA,OAAO7U;AACX;EAEA,SAASyU,UAAUzC,GAAMgD;IACrB,IAAIhD,EAAK5S,UAAU;MAEf,OAAO4S;;IAEX,IAAIiD,IAAKjD,EAAK5S,SAAS,IAAK;IAC5B,IAAI8V,IAAOlD,EAAKmD,MAAM,GAAGF;IACzB,IAAIG,IAAQpD,EAAKmD,MAAMF;IACvBR,UAAUS,GAAMF;IAChBP,UAAUW,GAAOJ;IACjB,IAAIK,IAAU;IACd,IAAIC,IAAW;IACf,IAAInW,IAAI;IACR,OAAOkW,IAAUH,EAAK9V,UAAUkW,IAAWF,EAAMhW,QAAQ;MAErD,IADU4V,EAAQE,EAAKG,IAAUD,EAAME,OAC5B;QAEPtD,EAAK7S,OAAO+V,EAAKG;;QAIjBrD,EAAK7S,OAAOiW,EAAME;;AAE1B;IACA,OAAOD,IAAUH,EAAK9V;MAClB4S,EAAK7S,OAAO+V,EAAKG;;IAErB,OAAOC,IAAWF,EAAMhW;MACpB4S,EAAK7S,OAAOiW,EAAME;;IAEtB,OAAOtD;AACX;AACH,CA9ED,CA8EG8B,OAAiBA,KAAe,CAAE;;AAIrC,IAAIE,KAAkC;EAClC,SAASA,iBAAiBxQ,GAAK2I,GAAYV,GAASsI;IAChDzK,KAAKiM,OAAO/R;IACZ8F,KAAKkM,cAAcrJ;IACnB7C,KAAKmM,WAAWhK;IAChBnC,KAAKoM,WAAW3B;IAChBzK,KAAKqM,oBAAejS;AACxB;EACAlE,OAAO8K,eAAe0J,iBAAiBzK,WAAW,OAAO;IACrDgB,KAAK;MACD,OAAOjB,KAAKiM;AACf;IACD/K,aAAY;IACZC,eAAc;;EAElBjL,OAAO8K,eAAe0J,iBAAiBzK,WAAW,cAAc;IAC5DgB,KAAK;MACD,OAAOjB,KAAKkM;AACf;IACDhL,aAAY;IACZC,eAAc;;EAElBjL,OAAO8K,eAAe0J,iBAAiBzK,WAAW,WAAW;IACzDgB,KAAK;MACD,OAAOjB,KAAKmM;AACf;IACDjL,aAAY;IACZC,eAAc;;EAElBuJ,iBAAiBzK,UAAU4K,UAAU,SAAU1Q;IAC3C,IAAIA,GAAO;MACP,IAAIN,IAAQmG,KAAK+K,SAAS5Q,EAAMN;MAChC,IAAIC,IAAMkG,KAAK+K,SAAS5Q,EAAML;MAC9B,OAAOkG,KAAKoM,SAASX,UAAU5R,GAAOC;AAC1C;IACA,OAAOkG,KAAKoM;;EAEhB1B,iBAAiBzK,UAAUqM,SAAS,SAAUC,GAAOpK;IACjDnC,KAAKoM,WAAWG,EAAM7V;IACtBsJ,KAAKmM,WAAWhK;IAChBnC,KAAKqM,oBAAejS;;EAExBsQ,iBAAiBzK,UAAUuM,iBAAiB;IACxC,SAA0BpS,MAAtB4F,KAAKqM,cAA4B;MACjC,IAAII,IAAc;MAClB,IAAI/V,IAAOsJ,KAAKoM;MAChB,IAAIM,KAAc;MAClB,KAAK,IAAI7W,IAAI,GAAGA,IAAIa,EAAKZ,QAAQD,KAAK;QAClC,IAAI6W,GAAa;UACbD,EAAY9W,KAAKE;UACjB6W,KAAc;AAClB;QACA,IAAIC,IAAKjW,EAAKkW,OAAO/W;QACrB6W,IAAsB,SAAPC,KAAsB,SAAPA;QAC9B,IAAW,SAAPA,KAAe9W,IAAI,IAAIa,EAAKZ,UAAiC,SAAvBY,EAAKkW,OAAO/W,IAAI;UACtDA;;AAER;MACA,IAAI6W,KAAehW,EAAKZ,SAAS;QAC7B2W,EAAY9W,KAAKe,EAAKZ;;MAE1BkK,KAAKqM,eAAeI;AACxB;IACA,OAAOzM,KAAKqM;;EAEhB3B,iBAAiBzK,UAAU6K,aAAa,SAAU+B;IAC9CA,IAASxU,KAAKyU,IAAIzU,KAAKC,IAAIuU,GAAQ7M,KAAKoM,SAAStW,SAAS;IAC1D,IAAI2W,IAAczM,KAAKwM;IACvB,IAAIO,IAAM,GAAGC,IAAOP,EAAY3W;IAChC,IAAa,MAATkX;MACA,OAAOhU,EAASC,OAAO,GAAG4T;;IAE9B,OAAOE,IAAMC,GAAM;MACf,IAAIC,IAAM5U,KAAK6U,OAAOH,IAAMC,KAAQ;MACpC,IAAIP,EAAYQ,KAAOJ;QACnBG,IAAOC;;QAGPF,IAAME,IAAM;;AAEpB;IAGA,IAAI/T,IAAO6T,IAAM;IACjB,OAAO/T,EAASC,OAAOC,GAAM2T,IAASJ,EAAYvT;;EAEtDwR,iBAAiBzK,UAAU8K,WAAW,SAAU/M;IAC5C,IAAIyO,IAAczM,KAAKwM;IACvB,IAAIxO,EAAS9E,QAAQuT,EAAY3W;MAC7B,OAAOkK,KAAKoM,SAAStW;WAEpB,IAAIkI,EAAS9E,OAAO;MACrB,OAAO;;IAEX,IAAIiU,IAAaV,EAAYzO,EAAS9E;IAEtC,OAAOb,KAAKyU,IAAIzU,KAAKC,IAAI6U,IAAanP,EAAS7E,WADzB6E,EAAS9E,OAAO,IAAIuT,EAAY3W,SAAU2W,EAAYzO,EAAS9E,OAAO,KAAK8G,KAAKoM,SAAStW,SACpCqX;;EAE/EjX,OAAO8K,eAAe0J,iBAAiBzK,WAAW,aAAa;IAC3DgB,KAAK;MACD,OAAOjB,KAAKwM,iBAAiB1W;AAChC;IACDoL,aAAY;IACZC,eAAc;;EAElB,OAAOuJ;AACX,CA1GsC;;AA2GtC,IAAIpR;;CACJ,SAAWA;EACP,IAAIgI,IAAWpL,OAAO+J,UAAUqB;EAIhChI,EAAG4C,UAHH,SAASA,QAAQxD;IACb,YAAwB,MAAVA;AAClB;EAKAY,EAAGc,YAHH,SAASA,YAAU1B;IACf,YAAwB,MAAVA;AAClB;EAKAY,EAAG+E,UAHH,SAASA,QAAQ3F;IACb,QAAiB,MAAVA,MAA4B,MAAVA;AAC7B;EAKAY,EAAG9B,SAHH,SAASA,OAAOkB;IACZ,OAAgC,sBAAzB4I,EAAS8L,KAAK1U;AACzB;EAKAY,EAAG+D,SAHH,SAASA,OAAO3E;IACZ,OAAgC,sBAAzB4I,EAAS8L,KAAK1U;AACzB;EAKAY,EAAGyB,cAHH,SAASA,YAAYrC,GAAOJ,GAAKwU;IAC7B,OAAgC,sBAAzBxL,EAAS8L,KAAK1U,MAAgCJ,KAAOI,KAASA,KAASoU;AAClF;EAKAxT,EAAGV,UAHH,SAASA,QAAQF;IACb,OAAgC,sBAAzB4I,EAAS8L,KAAK1U,OAAiC,cAAcA,KAASA,KAAS;AAC1F;EAKAY,EAAGP,WAHH,SAASA,SAASL;IACd,OAAgC,sBAAzB4I,EAAS8L,KAAK1U,MAAgC,KAAKA,KAASA,KAAS;AAChF;EAKAY,EAAGsR,OAHH,SAASA,KAAKlS;IACV,OAAgC,wBAAzB4I,EAAS8L,KAAK1U;AACzB;EAQAY,EAAGC,gBANH,SAASA,cAAcb;IAInB,OAAiB,SAAVA,KAAmC,mBAAVA;AACpC;EAKAY,EAAGgC,aAHH,SAASA,WAAW5C,GAAO2U;IACvB,OAAOhX,MAAMyI,QAAQpG,MAAUA,EAAMkH,MAAMyN;AAC/C;AAEH,CAjDD,CAiDG/T,OAAOA,KAAK,CAAA;;ACr5DT,IAAW4J;;CAAjB,SAAiBA;EACFA,EAAAC,OAAO;EACPD,EAAAE,SAAS;EACTF,EAAAG,WAAW;EACXH,EAAAI,cAAc;EACdJ,EAAAK,QAAQ;EACRL,EAAAM,WAAW;EACXN,EAAAO,QAAQ;EACRP,EAAAQ,YAAY;EACZR,EAAAS,SAAS;EACTT,EAAAU,WAAW;EACXV,EAAAW,OAAO;EACPX,EAAAY,QAAQ;EACRZ,EAAAa,OAAO;EACPb,EAAAc,UAAU;EACVd,EAAAe,UAAU;EACVf,EAAAxI,QAAQ;EACRwI,EAAAgB,OAAO;EACPhB,EAAAiB,YAAY;EACZjB,EAAAkB,SAAS;EACTlB,EAAAmB,aAAa;EACbnB,EAAAoB,WAAW;EACXpB,EAAAqB,SAAS;EACTrB,EAAAsB,QAAQ;EACRtB,EAAAuB,WAAW;EACXvB,EAAAwB,gBAAgB;AAC9B,CA1BD,CAAiBxB,OAAAA,KAAkB,CAAA;;ACnJ5B,IAAMoK,KAASpX,OAAAqX,OAAArX,OAAAqX,OAAA,IACjBC,EAAAA,OA/DoD;EACvDC,eAAe;EACfC,WAAW;EACXC,aAAa;EACbC,OAAO;EACPC,UAAU;EACVC,cAAc;EACdC,gBAAgB;EAChBC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,YAAY;EACZC,iBAAiB;EACjBC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,WAAW;EACXC,UAAU;EACVC,YAAY;EACZC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC,eAAe;EACfC,YAAY;EACZC,sBAAsB;EACtBC,eAAe;EACfC,YAAY;EACZC,sBAAsB;EACtBC,MAAM;;;ACPD,IAAMC,KAAoB;EAC/B5R,SAAS;EACTD,OAAO;;;AAGT,IAAM8R,sBAAuBC;EAC3B,IAAMC,IAA8C;EACpD,IAAID;IACF;MACEE,QAAMC,EAAAA,MAAMH,IAAK;QACfI,kBAAAA,CAAmBC;UACjBJ,EAAkB7Z,KAAKia;AACzB;;AAEH,MAAC,OAAAxS;MACA,OAAO;;;EAGX,OAAOoS;AAAiB;;AAG1B,IAAMK,KAA0B,EAE9BrC,EAAAA,KAAKsC,mBACLtC,EAAAA,KAAKuC,2BACLvC,EAAAA,KAAKwC,wBACLxC,EAAIA,KAACyC,wBACLzC,EAAIA,KAAC0C,2BACL1C,EAAAA,KAAK2C,uBACL3C,EAAAA,KAAK4C,sBACL5C,EAAIA,KAAC6C,8BACL7C,EAAIA,KAAC8C,sBAEL9C,OAAK+C,kBACL/C,EAAAA,KAAKgD,uBACLhD,EAAAA,KAAKiD,uBACLjD,EAAIA,KAACkD,0BACLlD,EAAIA,KAACmD,sBACLnD,EAAAA,KAAKoD,qBACLpD,EAAAA,KAAKqD;;AAGP,IAAMC,2BAA4BC;EAChC,IAAIC,KAAmB;EACvB,IAAID;IACF;MACEtB,QAAMC,EAAAA,MAAMqB,IAAM;QAChBE,KAAAA,CAAMC;UACJ,IAAkB,eAAdA,EAAKtc;YACP;;UAEF,IAAIib,GAAgBsB,SAASD,EAAKtc,OAAO;YACvCoc,KAAmB;YACnB,OAAOI;;UAET,QAAO;AACT;;AAEH,MAAC,OAAAhU;MACA,OAAO4T;;;EAGX,OAAOA;AAAgB;;AAcnB,SAAUK,2BACdvc,GACAwc,GACAC,GACAC,GACAC,GACAzS;;EAEA,IAAM0S,IAAIxb,OAAAqX,OAAArX,OAAAqX,OAAA,CAAA,GACLvO,IAAO;IACVlK;;EAEF,IAAMyB,IACJib,KAAgBG,mBAAmBL,GAAWC,GAAQ;EAExD,IAAM5c,IACiB,cAArB4B,EAAM5B,MAAMC,OAAqB2B,EAAM5B,MAAMiB,YAAYW,EAAM5B;EAEjE,IAAMid,KAAO5S,iBAAO,IAAPA,EAAS4S,SA+lCxB,SAASC,gBACPC,GACA5X;IAEA,IAAIA,iBAAAA,IAAAA,EAAK6X,SAAS;MAChB,OAAOC,GAAoBC;;IAE7B,OAAOnB,yBAAyBgB,KAC5BE,GAAoBC,cACpBD,GAAoBE;AAC1B,GAzmCgCL,CAAgBP,GAAWtS,iBAAAA,IAAAA,EAAS9E;EAGlE,KAAKvF;IACH,OAAO;;EAGT,KAAMC,MAAEA,GAAIud,MAAEA,GAAIvc,WAAEA,KAAcjB;EAClC,IAAMyd,IAAWC,YAAYvd,GAAQyB,EAAM5B;EAG3C,IAAIC,MAAS0Y,GAAUgF,UAAU;IAC/B,IAAIV,MAASI,GAAoBC;MAC/B,OAsSN,SAASM,uCAAuChc;QAC9C,OAAOD,WAASC,GAAO,EACrB;UAAEY,OAAO;UAAUvC,MAAMsO,GAAmBG;WAC5C;UAAElM,OAAO;UAAQvC,MAAMsO,GAAmBG;WAC1C;UAAElM,OAAO;UAAavC,MAAMsO,GAAmBG;WAC/C;UAAElM,OAAO;UAASvC,MAAMsO,GAAmBG;WAC3C;UAAElM,OAAO;UAASvC,MAAMsO,GAAmBG;WAC3C;UAAElM,OAAO;UAAUvC,MAAMsO,GAAmBG;WAC5C;UAAElM,OAAO;UAAUvC,MAAMsO,GAAmBG;;AAEhD,OAhTakP,CAAuChc;;IAEhD,OAgTJ,SAASic,uCAAuCjc;MAC9C,OAAOD,WAASC,GAAO,EACrB;QAAEY,OAAO;QAASvC,MAAMsO,GAAmBG;SAC3C;QAAElM,OAAO;QAAYvC,MAAMsO,GAAmBG;SAC9C;QAAElM,OAAO;QAAgBvC,MAAMsO,GAAmBG;SAClD;QAAElM,OAAO;QAAYvC,MAAMsO,GAAmBG;SAC9C;QAAElM,OAAO;QAAKvC,MAAMsO,GAAmBI;;AAE3C,KAxTWkP,CAAuCjc;;EAGhD,IAAI3B,MAAS0Y,GAAUyB;IACrB,OAsTJ,SAAS0D,sCAAsClc;MAC7C,OAAOD,WAASC,GAAO,EACrB;QAAEY,OAAO;QAAQvC,MAAMsO,GAAmBG;SAC1C;QAAElM,OAAO;QAAavC,MAAMsO,GAAmBG;SAC/C;QAAElM,OAAO;QAASvC,MAAMsO,GAAmBG;SAC3C;QAAElM,OAAO;QAASvC,MAAMsO,GAAmBG;SAC3C;QAAElM,OAAO;QAAUvC,MAAMsO,GAAmBG;SAC5C;QAAElM,OAAO;QAAUvC,MAAMsO,GAAmBG;;AAEhD,KA/TWoP,CAAsClc;;EAG/C,KACsB6G,UAApBA,IAAAxH,iBAAAA,IAAAA,EAAWA,yBAASwH,aAAAA,EAAExI,UAAS0Y,GAAU0B,wBACzCra,EAAMO;IAEN,OAAOoB,WAASC,GAAO;;EAIzB,KAAIX,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKgD;IAC3B,OAAOla,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,OAAOgb,EAAAA,cACP5b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAMjC,KAAIzN,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKiD;IAC3B,OAAOna,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,QAAO5C,KAAQ6d,EAAYA,aAAC7d,OAAUA,EAAKG,KAAK2d,WAAW,QAC3D9b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAMjC,KAAIzN,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKkD;IAC3B,OAAOpa,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,OAAOmb,EAAAA,iBACP/b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAMjC,KAAIzN,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKmD;IAC3B,OAAOra,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,OAAOob,EAAAA,aACPhc,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAMjC,KAAIzN,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKoD;IAC3B,OAAOta,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,QAAO5C,KAAQie,EAAUA,WAACje,OAAUA,EAAKG,KAAK2d,WAAW,QACzD9b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAMjC,KAAIzN,iBAAAA,IAAAA,EAAWhB,UAAS4Y,EAAAA,KAAKqD;IAC3B,OAAOva,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,OAAOsb,EAAAA,mBACPlc,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMsO,GAAmBG;;;EAKjC,IACEzO,MAAS0Y,GAAU4B,cAClBta,MAAS0Y,GAAU4F,eAActd,iBAAS,IAATA,EAAWhB,UAAS0Y,GAAU4B;IAEhE,OAoVJ,SAASiE,4BACP5c,GACA/B,GACAM,GACAgd,GACAM;MAGA,IAAI5d,EAAW4e;QACb,OAAO;;MAET,IAAMC,IAAUve,EAAO4d;MAEvB,IAAMY,IAAmBvd,aAAasd,GAAS1b,OAAOmb,EAAeA;MACrE,IAAMS,IAAuBD,EAAiBvc,KAAI,EAAG7B,aAAWA;MAChE,IAAMse,IAAgC,IAAIC;MAC1CC,kBAAgB5B,IAAc,CAAC6B,GAAGhf;;QAChC,IAAIA,EAAMO,MAAM;UAEd,IACEP,EAAMC,SAAS0Y,GAAUiB,kBACxBgF,EAAqBpC,SAASxc,EAAMO;YAErCse,EAAiBjT,IAAI5L,EAAMO;;UAI7B,IACEP,EAAMC,SAAS0Y,GAAU4F,eACV,UAAf9V,IAAAzI,EAAMiB,mBAAS,MAAAwH,SAAA,IAAAA,EAAExI,UAAS0Y,GAAU4B;YAEpC,IAAIkD,EAASwB,cAAc;cAIzB,IAH0CC,UAArBA,IAAAzB,EAASwB,4BAAYC,aAAAA,EACtCC,gBACDC,MAAK,EAAG7e,aAAWA,MAASP,EAAMO;gBAEnC;;cAEF,IAAMH,IAAOD,EAAOkf,QAAQrf,EAAMO;cAClC,IAAM+e,IAAuC,UAArBC,IAAA9B,EAASwB,sBAAY,MAAAM,SAAA,IAAAA,EAAEC;cAC/C/B,EAASwB,eAAe,IAAIQ,EAAAA,qBAAoBle,OAAAqX,OAAArX,OAAAqX,WAC3C0G,IAAe;gBAClBI,YAAY,KACPJ,EAAgBI,YAClBtf,KACC,IAAIqf,EAAAA,qBAAqB;kBAAElf,MAAMP,EAAMO;kBAAMof,QAAQ,CAAA;;;AAG5D,mBAAM,IAAIlC,EAASmC,eAAe;cAIjC,IAH2CC,UAAtBA,IAAApC,EAASmC,6BAAaC,aAAAA,EACvCV,gBACDC,MAAK,EAAG7e,aAAWA,MAASP,EAAMO;gBAEnC;;cAEF,IAAMH,IAAOD,EAAOkf,QAAQrf,EAAMO;cAClC,IAAMuf,IAAyC,UAAtBC,IAAAtC,EAASmC,uBAAa,MAAAG,SAAA,IAAAA,EAAEP;cACjD/B,EAASmC,gBAAgB,IAAII,EAAAA,kBAAiBze,OAAAqX,OAAArX,OAAAqX,WACzCkH,IAAgB;gBACnBJ,YAAY,KACPI,EAAiBJ,YACnBtf,KACC,IAAIqf,EAAAA,qBAAqB;kBAAElf,MAAMP,EAAMO;kBAAMof,QAAQ,CAAA;;;;;;;MAQnE,IAAMM,IAAsBxC,EAASwB,gBAAgBxB,EAASmC;MAG9D,IAAMM,MADoBD,iBAAAA,IAAAA,EAAqBd,oBAAmB,IAClB/c,KAAI,EAAG7B,aAAWA;MAGlE,IAAM4f,IAAqBxB,EACxBtZ,OACC,KAAIwZ,IAAkBzc,KAAI7B,MAAS;QAAEA;YAEtCyC,QACC,EAAGzC,aACDA,OAAS0f,qBAAAA,EAAqB1f,UAC7B2f,EAAsB1D,SAASjc;MAGtC,OAAOoB,WACLC,GACAue,EAAmB/d,KAAIhC;QACrB,IAAMkH,IAAS;UACb9E,OAAOpC,EAAKG;UACZN,MAAMsO,GAAmBQ;UACzB3O;;QAEF,IAAIA,qBAAAA,EAAMqJ;UACRnC,EAAO4J,gBAAgB9Q,EAAKqJ;;QAW9B,OAAOnC;AAAM;AAGnB,KAjcWkX,CACL5c,GACA5B,GACAG,GACAwc,GACAc;;EAKJ,IACExd,MAAS0Y,GAAUyH,iBACnBngB,MAAS0Y,GAAU0H,SACnBpgB,MAAS0Y,GAAUG;IAEnB,OAsNJ,SAASwH,4BACP1e,GACA6b,GACApT;;MAEA,IAAIoT,EAAS8C,YAAY;QACvB,KAAMA,YAAEA,KAAe9C;QACvB,IAAIkC,IAAqC;QACzC,IAAI,eAAeY;UACjBZ,IAASve,aAEPmf,EAAW3f;;QAIf,IAAID,EAAAA,gBAAgB4f;UAClBZ,EAAO3e,KAAKN,EAAAA;;QAEd,IAAI6f,iBAAe9X,IAAA4B,iBAAO,IAAPA,EAASlK,gBAAM,MAAAsI,SAAA,IAAAA,EAAEjI;UAClCmf,EAAO3e,KAAKV,sBAAoBG,EAAAA;;QAElC,OAAOkB,WACLC,GACA+d,EAAOvd,KAAoB,CAACoe,GAAOC;;UACjC,IAAMtd,IAA6B;YAEjCud,UAAU9O,OAAO6O,KAASD,EAAMjgB;YAChCiC,OAAOge,EAAMjgB;YACbgQ,QAAQqB,OAAO4O,EAAMpgB;YACrB8Q,eAAgC,UAAjBzI,IAAA+X,EAAM/W,qBAAW,MAAAhB,IAAAA,SAAIhD;YACpC8M,YAAYV,QAAQ2O,EAAMG;YAC1Bze,cAAc2P,QAAQ2O,EAAMG;YAC5BA,mBAAmBH,EAAMG;YACzB1gB,MAAMsO,GAAmBK;YACzBxO,MAAMogB,EAAMpgB;;UAGd,IAAIiK,qBAAAA,EAASuW,qBAAqB;YAEhC,IAAMC,IAAaC,cAAcN;YACjC,IAAIK,GAAY;cACd1d,EAAW0d,aAAaL,EAAMjgB,OAAOsgB;cACrC1d,EAAW4d,mBAAmB/Q,EAAiBV;cAC/CnM,EAAW2F,UAAU4R;;;UAIzB,OAAOvX;AAAU;;MAIvB,OAAO;AACT,KA1QWmd,CAA4B1e,GAAO6b,GAAUV;;EAItD,IACE9c,MAAS0Y,GAAUI,aAClB9Y,MAAS0Y,GAAUqI,YAAqB,MAATxD,GAChC;IACA,KAAMyD,SAAEA,KAAYxD;IACpB,IAAIwD;MACF,OAAOtf,WACLC,GACAqf,EAAQ7e,KACL8e;;QAA4C,OAAC;UAC5C1e,OAAO0e,EAAO3gB;UACdsgB,YAAYK,EAAO3gB,OAAO;UAC1BuI,SAAS4R;UACTnK,QAAQqB,OAAOsP,EAAO9gB;UACtB8Q,eAAiC,UAAlBzI,IAAAyY,EAAOzX,qBAAW,MAAAhB,IAAAA,SAAIhD;UACrCxF,MAAMsO,GAAmBM;UACzBzO,MAAM8gB,EAAO9gB;;AACd;;;EAOT,KACGH,MAAS0Y,GAAUe,gBACjBzZ,MAAS0Y,GAAUwI,gBAAyB,MAAT3D,MACtCC,EAAS2D,iBACT;IACA,IAAMC,IAAejgB,aAAaqc,EAAS2D;IAC3C,IAAME,IACJrhB,MAAS0Y,GAAUe,eACfnL,GAAmBY,QACnBZ,GAAmBK;IACzB,OAAOjN,WACLC,GACAyf,EAAajf,KAAIoe;;MAAS,OAAC;QACzBhe,OAAOge,EAAMjgB;QACbgQ,QAAQqB,OAAO4O,EAAMpgB;QACrB8Q,eAAgC,UAAjBzI,IAAA+X,EAAM/W,qBAAW,MAAAhB,IAAAA,SAAIhD;QACpCxF,MAAMqhB;QACNlhB,MAAMogB,EAAMpgB;;AACb;;EAKL,IACEH,MAAS0Y,GAAUoB,cAClB9Z,MAAS0Y,GAAUgB,cAAuB,MAAT6D,KACjCvd,MAAS0Y,GAAUwI,gBAAyB,MAAT3D,KACnCvd,MAAS0Y,GAAUqI,YAAqB,MAATxD;IAEhC,OAmNJ,SAAS+D,6BACP3f,GACA6b,GACAd,GACAxc;MAEA,IAAMqhB,IAAiBC,EAAAA,aAAahE,EAASiE;MAE7C,IAAMC,IAAmCC,uBACvCjF,GACAxc,GACAyB,GACAoB,QAAO6e,KAAKA,EAAEtR,WAAWiR,EAAejhB;MAE1C,IAAIihB,aAA0BM,EAAAA,iBAAiB;QAE7C,OAAOngB,WACLC,GAFa4f,EAAeO,YAIzB3f,KAAqB2B;;UAA4B,OAAC;YACjDvB,OAAOuB,EAAMxD;YACbgQ,QAAQqB,OAAO4P;YACftQ,eAAgC,UAAjBzI,IAAA1E,EAAM0F,qBAAW,MAAAhB,IAAAA,SAAIhD;YACpC8M,YAAYV,QAAQ9N,EAAM4c;YAC1Bze,cAAc2P,QAAQ9N,EAAM4c;YAC5BA,mBAAmB5c,EAAM4c;YACzB1gB,MAAMsO,GAAmBmB;YACzBtP,MAAMohB;;AACP,YACAnc,OAAOsc;;MAGd,IAAIH,MAAmBQ,EAAAA;QACrB,OAAOrgB,WACLC,GACA+f,EAAetc,OAAO,EACpB;UACE7C,OAAO;UACP+N,QAAQqB,OAAOoQ;UACf9Q,eAAe;UACfjR,MAAMsO,GAAmBM;UACzBzO,MAAM4hB,EAAAA;WAER;UACExf,OAAO;UACP+N,QAAQqB,OAAOoQ;UACf9Q,eAAe;UACfjR,MAAMsO,GAAmBM;UACzBzO,MAAM4hB,EAAAA;;;MAMd,OAAOL;AACT,KA1QWJ,CAA6B3f,GAAO6b,GAAUd,GAAWxc;;EAGlE,IAAIF,MAAS0Y,GAAUsJ,YAAqB,MAATzE,GAAY;IAC7C,IAAMgE,IAAiBC,EAAAA,aAAahE,EAASiE;IAM7C,OAAO/f,WACLC,GAN0BggB,uBAC1BjF,GACAxc,GACAyB,GAIoBoB,QAAO6e,KAAKA,EAAEtR,YAAWiR,qBAAAA,EAAgBjhB;;EAKjE,IACGN,MAAS0Y,GAAUS,kBAA2B,MAAToE,KACrCvd,MAAS0Y,GAAU4F,cACL,QAAbtd,KACAA,EAAUhB,SAAS0Y,GAAUS;IAE/B,OAoWJ,SAAS8I,wCACPtgB,GACA6b,GACAtd,GACAgiB;MAEA,IAAIC;MACJ,IAAI3E,EAAS8C;QACX,IAAI8B,EAAcA,eAAC5E,EAAS8C,aAAa;UACvC,IAAM+B,IAAeC,EAAAA,mBAAmB9E,EAAS8C;UAGjD,IAAMiC,IAAmBriB,EAAOsiB,iBAAiBH;UACjD,IAAMI,IAAmBnhB,OAAO+C,OAAO;UACvC,KAAK,IAAMlE,KAAQoiB;YACjB,KAAK,IAAMG,KAASviB,EAAK+e;cACvBuD,EAAiBC,EAAMpiB,QAAQoiB;;;UAGnCP,IAAgBI,EAAiBnd,OAAOjE,aAAashB;;UAIrDN,IAAgB,EAAC3E,EAAS8C;;aAEvB;QAEL6B,IAAgBhhB,aADAjB,EAAO4d,cACe/a,QACpC5C,KAAQO,EAAeA,gBAACP,OAAUA,EAAKG,KAAK2d,WAAW;;MAG3D,OAAOvc,WACLC,GACAwgB,EAAchgB,KAAIhC;QAChB,IAAMwiB,IAAYnB,eAAarhB;QAC/B,OAAO;UACLoC,OAAOoP,OAAOxR;UACd8Q,gBAAe0R,qBAAAA,EAAWnZ,gBAAe;UACzCxJ,MAAMsO,GAAmBK;;AAC1B;AAGP,KA9YWsT,CACLtgB,GACA6b,GACAtd;;EAMJ,IAAIF,MAAS0Y,GAAUkK,mBAA4B,MAATrF;IACxC,OAsYJ,SAASsF,kCACPlhB,GACA6b,GACAtd,GACAwc,GACAG;MAEA,KAAKH;QACH,OAAO;;MAET,IAAM+B,IAAUve,EAAO4d;MACvB,IAAMgF,IAAWnjB,mBAAmBgC,EAAM5B;MAC1C,IAAMgjB,IAgGF,SAAUC,uBACdtG;QAEA,IAAMG,IAAyC;QAC/CiC,kBAAgBpC,IAAW,CAACqC,GAAGhf;UAC7B,IACEA,EAAMC,SAAS0Y,GAAUuK,uBACzBljB,EAAMO,QACNP,EAAMI;YAEN0c,EAAa9b,KAAK;cAChBf,MAAM0Y,GAAUuK;cAChB3iB,MAAM;gBACJN,MAAM4Y,EAAIA,KAACsK;gBACXpf,OAAO/D,EAAMO;;cAGf6iB,cAAc;gBACZnjB,MAAM0Y,GAAUyH;gBAChBiD,YAAY;;cAGdC,eAAe;gBACbrjB,MAAM0Y,GAAU4F;gBAChBhe,MAAM;kBACJN,MAAM4Y,EAAIA,KAACsK;kBACXpf,OAAO/D,EAAMI;;;;;;QAOvB,OAAO0c;AACT,OAlIoBmG,CAAuBtG;MAEzC,IAAIG,KAAgBA,EAAa3b,SAAS;QACxC6hB,EAAUhiB,QAAQ8b;;MAIpB,IAAMyG,IAAgBP,EAAUhgB,QAC9BwgB,KAEE9E,EAAQ8E,EAAKF,cAAc/iB,KAAKwD,YAG9Bgf,KACAA,EAAS9iB,SAAS0Y,GAAUuK,uBAC5BH,EAASxiB,SAASijB,EAAKjjB,KAAKwD,UAG9BpD,EAAAA,gBAAgB8c,EAAS8C,eACzB5f,kBAAgB+d,EAAQ8E,EAAKF,cAAc/iB,KAAKwD,WAChD0f,EAAcA,eACZtjB,GACAsd,EAAS8C,YACT7B,EAAQ8E,EAAKF,cAAc/iB,KAAKwD;MAItC,OAAOpC,WACLC,GACA2hB,EAAcnhB,KAAIohB,MAAS;QACzBhhB,OAAOghB,EAAKjjB,KAAKwD;QACjBwM,QAAQqB,OAAO8M,EAAQ8E,EAAKF,cAAc/iB,KAAKwD;QAC/CmN,eAAe,YAAYsS,EAAKjjB,KAAKwD,YAAYyf,EAAKF,cAAc/iB,KAAKwD;QACzE9D,MAAMsO,GAAmBK;QACzBxO,MAAMse,EAAQ8E,EAAKF,cAAc/iB,KAAKwD;;AAG5C,KAvbW+e,CACLlhB,GACA6b,GACAtd,GACAwc,GACAjb,MAAMyI,QAAQ2S,KACVA,IACAnC,oBAAoBmC;;EAI5B,IAAM4G,IAAiBC,WAAW3jB;EAElC,IACGid,MAASI,GAAoBC,gBAC3BoG,EAAeE,gBAChB3jB,MAAS0Y,GAAU4F,cACrBte,MAAS0Y,GAAUkL,WACnB;IACA,IAAIH,EAAezjB,SAAS0Y,GAAUqB;MACpC,OAAOrY,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,QAAO5C,KAAQ0jB,EAAYA,aAAC1jB,OAAUA,EAAKG,KAAK2d,WAAW,QAC3D9b,KAAIhC,MAAS;QACZoC,OAAOpC,EAAKG;QACZN,MAAMsO,GAAmBG;;;IAIjC,IAAIgV,EAAezjB,SAAS0Y,GAAUuB;MACpC,OAAOvY,WACLC,GACAL,OAAOE,OAAOtB,EAAO4d,cAClB/a,QAAO5C,KAAQ2jB,EAAWA,YAAC3jB,OAAUA,EAAKG,KAAK2d,WAAW,QAC1D9b,KAAIhC,MAAS;QACZoC,OAAOpC,EAAKG;QACZN,MAAMsO,GAAmBG;;;;EAOnC,IACGzO,MAAS0Y,GAAUqL,uBAAgC,MAATxG,KAC1Cvd,MAAS0Y,GAAUkL,aAAsB,MAATrG,KAChCvd,MAAS0Y,GAAU4F,cAClBtd,MACCA,EAAUhB,SAAS0Y,GAAUqL,uBAC5B/iB,EAAUhB,SAAS0Y,GAAUkL,aAC7B5iB,EAAUhB,SAAS0Y,GAAUsL;IAEjC,OAieJ,SAASC,oCACPtiB,GACAzB,GACAgiB;MAEA,IAAMgC,IAAehkB,EAAO4d;MAC5B,IAAMqG,IAAahjB,aAAa+iB,GAAcnhB,OAAO+gB,EAAWA;MAChE,OAAOpiB,WACLC,GAEAwiB,EAAWhiB,KAAKhC,MAA4B;QAC1CoC,OAAOpC,EAAKG;QACZ2Q,eAAe9Q,EAAKqJ;QACpBxJ,MAAMsO,GAAmBM;;AAG/B,KAjfWqV,CAAoCtiB,GAAOzB;;EAIpD,IAAIF,MAAS0Y,GAAU0L;IACrB,OA8eJ,SAASC,2BACP1iB,GACA5B,GACAG,GACAgiB;;MAEA,IAAmB1Z,UAAfA,IAAAzI,EAAMiB,mBAASwH,MAAAA,SAAAA,IAAAA,EAAExI,MAAM;QACzB,IAAMskB,IAAapkB,EAChBqkB,gBACAxhB,QAAOyhB,KAkGR,SAAUC,gBACd1kB,GACAykB;UAEA,MAAKzkB,qBAAAA,EAAOC;YACV,QAAO;;UAET,KAAMA,MAAEA,GAAIgB,WAAEA,KAAcjB;UAC5B,KAAM2kB,WAAEA,KAAcF;UACtB,QAAQxkB;WACN,KAAK0Y,GAAUM;YACb,OAAO0L,EAAUnI,SAASoI,EAAiBA,kBAAC3L;;WAC9C,KAAKN,GAAUO;YACb,OAAOyL,EAAUnI,SAASoI,EAAiBA,kBAAC1L;;WAC9C,KAAKP,GAAUQ;YACb,OAAOwL,EAAUnI,SAASoI,EAAiBA,kBAACzL;;WAC9C,KAAKR,GAAU0H;WACf,KAAK1H,GAAUG;YACb,OAAO6L,EAAUnI,SAASoI,EAAiBA,kBAACvE;;WAC9C,KAAK1H,GAAUuK;YACb,OAAOyB,EAAUnI,SAASoI,EAAiBA,kBAAC1B;;WAC9C,KAAKvK,GAAUkK;YACb,OAAO8B,EAAUnI,SAASoI,EAAiBA,kBAAC/B;;WAC9C,KAAKlK,GAAUkM;YACb,OAAOF,EAAUnI,SAASoI,EAAiBA,kBAACC;;WAG9C,KAAKlM,GAAUY;YACb,OAAOoL,EAAUnI,SAASoI,EAAiBA,kBAACE;;WAC9C,KAAKnM,GAAUa;YACb,OAAOmL,EAAUnI,SAASoI,EAAiBA,kBAACG;;WAC9C,KAAKpM,GAAUc;YACb,OAAOkL,EAAUnI,SAASoI,EAAiBA,kBAACI;;WAC9C,KAAKrM,GAAUqB;YACb,OAAO2K,EAAUnI,SAASoI,EAAiBA,kBAACK;;WAC9C,KAAKtM,GAAUiB;YACb,OAAO+K,EAAUnI,SAASoI,EAAiBA,kBAACM;;WAC9C,KAAKvM,GAAUkB;YACb,OAAO8K,EAAUnI,SAASoI,EAAiBA,kBAACO;;WAC9C,KAAKxM,GAAUmB;YACb,OAAO6K,EAAUnI,SAASoI,EAAiBA,kBAACQ;;WAC9C,KAAKzM,GAAUoB;YACb,OAAO4K,EAAUnI,SAASoI,EAAiBA,kBAAC7K;;WAC9C,KAAKpB,GAAUsB;YACb,OAAO0K,EAAUnI,SAASoI,EAAiBA,kBAACS;;WAC9C,KAAK1M,GAAUuB;YAEb,QADsBjZ,iBAAAA,IAAAA,EAAWhB;aAE/B,KAAK0Y,GAAUwB;cACb,OAAOwK,EAAUnI,SAASoI,EAAiBA,kBAACU;;aAC9C,KAAK3M,GAAUsB;cACb,OAAO0K,EAAUnI,SAASoI,EAAiBA,kBAACW;;;UAIpD,QAAO;AACT,SA1J2Bb,CAAgB1kB,EAAMiB,WAAWwjB;QACxD,OAAO9iB,WACLC,GACA2iB,EAAWniB,KAAIqiB,MAAc;UAC3BjiB,OAAOiiB,EAAUlkB;UACjB2Q,eAAeuT,EAAUhb,eAAe;UACxCxJ,MAAMsO,GAAmBG;;;MAI/B,OAAO;AACT,KAlgBW4V,CAA2B1iB,GAAO5B,GAAOG;;EAGlD,OAAO;AACT;;AAEA,IAAMqlB,KAAe;;AAOrB,IAAM1E,gBAAiBN;EACrB,KAAMpgB,MAAEA,KAASogB;EACjB,IAAI7f,EAAAA,gBAAgBP;IAClB,OAAOolB;;EAET,IAAIC,EAAAA,WAAWrlB,MAASO,EAAeA,gBAACP,EAAKslB;IAC3C,OAAOF;;EAET,IAAIG,EAAAA,cAAcvlB,IAAO;IACvB,IAAIO,EAAeA,gBAACP,EAAKslB;MACvB,OAAOF;;IAET,IAAIC,EAAUA,WAACrlB,EAAKslB,WAAW/kB,EAAeA,gBAACP,EAAKslB,OAAOA;MACzD,OAAOF;;;EAGX,OAAO;AAAI;;AAmWb,IAAMI,wBAAsBA,CAAC5lB,GAAcC;;EACzC,KAAmB,UAAfwI,IAAAzI,EAAMiB,mBAAS,MAAAwH,SAAA,IAAAA,EAAExI,UAASA;IAC5B,OAAOD,EAAMiB;;EAEf,eAAIse,IAAe,UAAfL,IAAAlf,EAAMiB,mBAAS,MAAAie,SAAA,IAAAA,EAAEje,mBAAS,MAAAse,SAAA,IAAAA,EAAEtf,UAASA;IACvC,OAAOD,EAAMiB,UAAUA;;EAEzB,KAAyC,UAArC4kB,IAA0B,UAA1B9F,IAAeF,UAAfA,IAAA7f,EAAMiB,yBAAS4e,aAAAA,EAAE5e,mBAAS,MAAA8e,SAAA,IAAAA,EAAE9e,mBAAS,MAAA4kB,SAAA,IAAAA,EAAE5lB,UAASA;IAClD,OAAOD,EAAMiB,UAAUA,UAAUA;;EAEnC,KAAoD,UAAhD6kB,IAAqCC,UAArCA,IAA0B,UAA1BC,IAAe,UAAfC,IAAAjmB,EAAMiB,mBAASglB,MAAAA,SAAAA,IAAAA,EAAEhlB,mBAAS,MAAA+kB,SAAA,IAAAA,EAAE/kB,yBAAS8kB,aAAAA,EAAE9kB,mBAAS6kB,MAAAA,SAAAA,IAAAA,EAAE7lB,UAASA;IAC7D,OAAOD,EAAMiB,UAAUA,UAAUA,UAAUA;;;;AAIzC,SAAU2gB,uBACdjF,GACAxc,GACAyB;EAEA,IAAIgT,IAA8B;EAClC,IAAIsR;EACJ,IAAMC,IAAmC5kB,OAAO+C,OAAO,CAAE;EACzDya,kBAAgBpC,IAAW,CAACqC,GAAGhf;IAE7B,KAAIA,iBAAK,IAALA,EAAOC,UAAS0Y,GAAUsJ,YAAYjiB,EAAMO;MAC9CqU,IAAe5U,EAAMO;;IAEvB,KAAIP,qBAAAA,EAAOC,UAAS0Y,GAAU4F,cAAc3J,GAAc;MACxD,IAAMwR,IAAmBR,sBAAoB5lB,GAAO2Y,GAAU8B;MAC9D,IAAI2L,qBAAAA,EAAkBhmB;QACpB8lB,IAAe/lB,EAAOkf,QACpB+G,iBAAAA,IAAAA,EAAkBhmB;;;IAKxB,IAAIwU,KAAgBsR,MAAiBC,EAAYvR,IAAe;MAG9DuR,EAAYvR,KAAgB;QAC1BrE,QAAQ2V,EAAavZ;QACrBkU,YAA6B,QAAjBjf,EAAMiB,SAAiB+R,IAAe,MAAMA;QACxDpS,OAAOoS;QACPxU,MAAM8lB;QACNjmB,MAAMsO,GAAmBM;;MAG3B+F,IAAe;MACfsR,IAAe;;;EAInB,OAAO9kB,aAAa+kB;AACtB;;AA8EM,SAAUnJ,mBACdL,GACAC,GACA1E,IAAS;EAET,IAAImO,IAAgB;EACpB,IAAIC,IAAgB;EACpB,IAAIC,IAAiB;EACrB,IAAM3kB,IAAQmd,kBAAgBpC,IAAW,CAAC6J,GAAQxmB,GAAOymB,GAAOhG;IAC9D,IACEA,MAAU7D,EAAOrY,QACjBiiB,EAAOE,uBAAuBxO,IAAS0E,EAAOpY,YAAY;MAE1D;;IAEF6hB,IAAgBI;IAChBH,IAAa/kB,OAAAqX,OAAA,CAAA,GAAQ5Y;IACrBumB,IAAiBC,EAAOG;IACxB,OAAO;AAAO;EAKhB,OAAO;IACLzhB,OAAOtD,EAAMsD;IACbC,KAAKvD,EAAMuD;IACXtC,QAAQ0jB,KAAkB3kB,EAAMiB;IAChC7C,OAAOsmB,KAAiB1kB,EAAM5B;IAC9BymB,OAAOJ,KAAiBzkB,EAAM6kB;;AAElC;;AAgBM,SAAU1H,kBACdpC,GACAiK;EAEA,IAAMC,IAAQlK,EAAUmK,MAAM;EAC9B,IAAMC,IAASC,EAAAA;EACf,IAAIhnB,IAAQ+mB,EAAOE;EACnB,IAAIR,IAAQ;EAEZ,IAAID,IAA0B,IAAIU,kBAAgB;EAElD,KAAK,IAAIhmB,IAAI,GAAGA,IAAI2lB,EAAM1lB,QAAQD,KAAK;IACrCslB,IAAS,IAAIU,EAAeA,gBAACL,EAAM3lB;IACnC,QAAQslB,EAAOW,OAAO;MAGpB,IAAa,YADAP,EAASJ,GAAQxmB,GAD9BymB,IAAQM,EAAOnlB,MAAM4kB,GAAQxmB,IACekB;QAE1C;;;IAMJ0lB,EAASJ,GAAQxmB,GAAOymB,GAAOvlB;IAE/B,KAAKlB,EAAMC;MACTD,IAAQ+mB,EAAOE;;;EAInB,OAAO;IACL/hB,OAAOshB,EAAOY;IACdjiB,KAAKqhB,EAAOE;IACZ7jB,QAAQ2jB,EAAOG;IACf3mB;IACAymB;;AAEJ;;AA8DM,SAAU/I,YACdvd,GACAN;EAEA,IAAIqhB;EACJ,IAAID;EACJ,IAAIoG;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAI7F;EACJ,IAAI9B;EACJ,IAAIwB;EACJ,IAAIb;EACJ,IAAIngB;EACJ,IAAI6e;EACJlf,aAAaF,IAAYG;;IACvB,QAAQA,EAAMC;KACZ,KAAK0Y,GAAUM;KACf,KAAK;MACH7Y,IAAOD,EAAOK;MACd;;KACF,KAAKmY,GAAUO;MACb9Y,IAAOD,EAAOqnB;MACd;;KACF,KAAK7O,GAAUQ;MACb/Y,IAAOD,EAAOsnB;MACd;;KACF,KAAK9O,GAAUkM;KACf,KAAKlM,GAAUuK;MACb,IAAIljB,EAAMI;QACRA,IAAOD,EAAOkf,QAAQrf,EAAMI;;MAE9B;;KACF,KAAKuY,GAAU0H;KACf,KAAK1H,GAAUG;MACb,KAAK1Y,MAASJ,EAAMO;QAClBgnB,IAAW;aACN;QACLA,IAAWhH,IACPrgB,YAAYC,GAAQogB,GAAYvgB,EAAMO,QACtC;QACJH,IAAOmnB,IAAWA,EAASnnB,OAAO;;MAEpC;;KAEF,KAAKuY,GAAUyH;MACbG,IAAakB,EAAAA,aAAarhB;MAC1B;;KACF,KAAKuY,GAAU0L;MACbgD,IAAernB,EAAMO,OAAOJ,EAAOunB,aAAa1nB,EAAMO,QAAQ;MAC9D;;KAEF,KAAKoY,GAAUiB;MACb,IAAI5Z,EAAMO,MAAM;QACdqf,IAAgB;QAChBX,IAAe,IAAIQ,EAAAA,qBAAqB;UACtClf,MAAMP,EAAMO;UACZmf,YAAY;UACZC,QAAQ,CAAA;;;MAIZ;;KAEF,KAAKhH,GAAUc;MACb,IAAIzZ,EAAMO,MAAM;QACd0e,IAAe;QACfW,IAAgB,IAAII,EAAAA,kBAAkB;UACpCzf,MAAMP,EAAMO;UACZmf,YAAY;UACZC,QAAQ,CAAA;;;MAIZ;;KACF,KAAKhH,GAAUI;MACb,IAAI/Y,EAAMiB;QACR,QAAQjB,EAAMiB,UAAUhB;SACtB,KAAK0Y,GAAU0H;UACbY,IAAUsG,KAAaA,EAASxe;UAChC;;SACF,KAAK4P,GAAU0L;UACbpD,IACEoG,KAAiBA,EAAate;UAChC;;SAEF,KAAK4P,GAAUG;UACb,IAAMvY,IAAsBkI,UAAfA,IAAAzI,EAAMiB,mBAASwH,MAAAA,SAAAA,IAAAA,EAAElI;UAC9B,KAAKA,GAAM;YACT0gB,IAAU;YACV;;UAEF,IAAMT,IAAQD,IACVrgB,YAAYC,GAAQogB,GAAYhgB,KAChC;UACJ,KAAKigB,GAAO;YACVS,IAAU;YACV;;UAEFA,IAAUT,EAAMzX;UAChB;;SAEF;UACEkY,IAAU;;;QAIdA,IAAU;;MAEZ;;KAEF,KAAKtI,GAAUqI;MACb,IAAIC;QACF,KAAK,IAAI/f,IAAI,GAAGA,IAAI+f,EAAQ9f,QAAQD;UAClC,IAAI+f,EAAQ/f,GAAGX,SAASP,EAAMO,MAAM;YAClC2gB,IAASD,EAAQ/f;YACjB;;;;MAINwgB,IAAYR,iBAAAA,IAAAA,EAAQ9gB;MACpB;;KAEF,KAAKuY,GAAUoB;MACb,IAAM4N,IAAWlG,eAAaC;MAC9B4F,IACEK,aAAoB7F,EAAAA,kBAChB6F,EACG5F,YACA3C,MAAMwI,KAA0BA,EAAI7jB,UAAU/D,EAAMO,SACvD;MACN;;KAEF,KAAKoY,GAAUgB;MACb,IAAMkO,IAAeC,kBAAgBpG;MACrCA,IACEmG,aAAwBE,EAAAA,cAAcF,EAAanC,SAAS;MAC9D;;KACF,KAAK/M,GAAUe;MACb,IAAMsO,IAAavG,eAAaC;MAChCN,IACE4G,aAAsBC,EAAsBA,yBACxCD,EAAWpnB,cACX;MACN;;KAEF,KAAK+X,GAAUwI;MACb,IAAM+G,IACJloB,EAAMO,QAAQ6gB,IAAkBA,EAAgBphB,EAAMO,QAAQ;MAChEmhB,IAAYwG,iBAAAA,IAAAA,EAAa9nB;MAEzB;;KACF,KAAKuY,GAAU4F;MACb,IAAIve,EAAMO;QACRH,IAAOD,EAAOkf,QAAQrf,EAAMO;;;;EAUpC,OAAO;IACL2gB;IACAD;IACAoG;IACAC;IACAC;IACA7F;IACAN;IACAb;IACAngB;IACA6e;IACAW;;AAEJ;;AAEA,IAAYvC;;CAAZ,SAAYA;EACVA,EAA2B,cAAA;EAC3BA,EAAyB,aAAA;AAC1B,CAHD,CAAYA,OAAAA,KAAmB,CAAA;;AAiB/B,SAASsG,WAAW3jB;EAClB,IACEA,EAAMiB,aACNjB,EAAMC,QAEJ,EACE0Y,GAAU4F,YACV5F,GAAUkL,WACVlL,GAAU8B,MACV9B,GAAUsL,gBAEZzH,SAASxc,EAAMC;IAEjB,OAAO0jB,WAAW3jB,EAAMiB;;EAE1B,OAAOjB;AACT;;ACzwCM,SAAUmoB,oBACdhoB,GACAwc,GACAC,GACAC,GACAuL;EAEA,IAAMxmB,IAAQib,KAAgBG,mBAAmBL,GAAWC;EAE5D,KAAKzc,MAAWyB,MAAUA,EAAM5B;IAC9B,OAAO;;EAGT,KAAMC,MAAEA,GAAIud,MAAEA,KAAS5b,EAAM5B;EAC7B,IAAMyd,IAAWC,YAAYvd,GAAQyB,EAAM5B;EAC3C,IAAMqK,IAAO9I,OAAAqX,OAAArX,OAAAqX,OAAA,CAAA,GAAQwP,IAAM;IAAEjoB;;EAK7B,IACY,YAATF,KAA6B,MAATud,KAAcC,EAAS8J,YAClC,mBAATtnB,KAAoC,MAATud,KAAcC,EAAS8J,UACnD;IACA,IAAMc,IAAiB;IACvBC,kBAAkBD,GAAMhe;KAwD5B,SAASke,YAAYF,GAAgB5K,GAAuBpT;MAC1Dme,qBAAqBH,GAAM5K,GAAUpT;MACrCoe,qBAAqBJ,GAAM5K,GAAUpT,GAASoT,EAASrd;AACzD,KA1DImoB,CAAYF,GAAM5K,GAAUpT;IAC5Bqe,gBAAgBL,GAAMhe;IACtBse,kBAAkBN,GAAMhe,GAASoT,EAAS8J;IAC1C,OAAOc,EAAK3oB,KAAK,IAAIkpB;;EAEvB,IAAa,gBAAT3oB,KAAiC,MAATud,KAAcC,EAAS4J,cAAc;IAC/D,IAAMgB,IAAiB;IACvBC,kBAAkBD,GAAMhe;IACxBwe,gBAAgBR,GAAM5K;IACtBiL,gBAAgBL,GAAMhe;IACtBse,kBAAkBN,GAAMhe,GAASoT,EAAS4J;IAC1C,OAAOgB,EAAK3oB,KAAK,IAAIkpB;;EAEvB,IAAa,eAAT3oB,KAAgC,MAATud,KAAcC,EAASyD,QAAQ;IACxD,IAAMmH,IAAiB;IACvBC,kBAAkBD,GAAMhe;KAqE5B,SAASye,UAAUT,GAAgB5K,GAAuBpT;MACxD,IAAIoT,EAAS4J;QACXwB,gBAAgBR,GAAM5K;aACjB,IAAIA,EAAS8J;QAClBiB,qBAAqBH,GAAM5K,GAAUpT;;MAGvC,KAAKoT,EAASyD;QACZ;;MAGF,KAAM3gB,MAAEA,KAASkd,EAASyD;MAC1Bnf,KAAKsmB,GAAM;MACXtmB,KAAKsmB,GAAM9nB;MACXkoB,qBAAqBJ,GAAM5K,GAAUpT,GAASoT,EAASiE;MACvD3f,KAAKsmB,GAAM;AACb,KApFIS,CAAUT,GAAM5K,GAAUpT;IAC1Bqe,gBAAgBL,GAAMhe;IACtBse,kBAAkBN,GAAMhe,GAASoT,EAASyD;IAC1C,OAAOmH,EAAK3oB,KAAK,IAAIkpB;;EAEvB,IACW,gBAAT3oB,KACAwd,EAAS6J,aACT,iBAAiB7J,EAAS6J,WAC1B;IACA,IAAMe,IAAiB;IACvBC,kBAAkBD,GAAMhe;KAqF5B,SAAS0e,gBAAgBV,GAAgB5K,GAAuBpT;MAC9D,KAAKoT,EAAS6J;QACZ;;MAEF,KAAM/mB,MAAEA,KAASkd,EAAS6J;MAC1B0B,WAAWX,GAAM5K,GAAUpT,GAASoT,EAASiE;MAC7C3f,KAAKsmB,GAAM;MACXtmB,KAAKsmB,GAAM9nB;AACb,KA5FIwoB,CAAgBV,GAAM5K,GAAUpT;IAChCqe,gBAAgBL,GAAMhe;IACtBse,kBAAkBN,GAAMhe,GAASoT,EAAS6J;IAC1C,OAAOe,EAAK3oB,KAAK,IAAIkpB;;EAEvB,IAAa,gBAAT3oB,KAAwBwd,EAASrd,QAAQ,iBAAiBqd,EAASrd,MAAM;IAC3E,IAAMioB,IAAiB;IACvBC,kBAAkBD,GAAMhe;IACxB2e,WAAWX,GAAM5K,GAAUpT,GAASoT,EAASrd;IAC7CsoB,gBAAgBL,GAAMhe;IACtBse,kBAAkBN,GAAMhe,GAASoT,EAASrd;IAC1C,OAAOioB,EAAK3oB,KAAK,IAAIkpB;;EAEvB,OAAO;AACT;;AAEA,SAASN,kBAAkBD,GAAgBhe;EACzC,IAAIA,EAAQ4e;IACVlnB,KAAKsmB,GAAM;;AAEf;;AACA,SAASK,gBAAgBL,GAAgBhe;EACvC,IAAIA,EAAQ4e;IACVlnB,KAAKsmB,GAAM;;AAEf;;AAOA,SAASG,qBACPH,GACA5K,GACApT;EAEA,KAAKoT,EAAS8J;IACZ;;EAEF,IAAMlnB,IAAYod,EAAS8J,SAAShnB;EACpC,IAA8B,SAA1BF,EAAU6W,MAAM,GAAG,IAAa;IAClC8R,WAAWX,GAAM5K,GAAUpT,GAASoT,EAAS8C;IAC7Cxe,KAAKsmB,GAAM;;EAEbtmB,KAAKsmB,GAAMhoB;AACb;;AAEA,SAASwoB,gBAAgBR,GAAgB5K,GAAuByL;EAC9D,KAAKzL,EAAS4J;IACZ;;EAGFtlB,KAAKsmB,GADQ,MAAM5K,EAAS4J,aAAa9mB;AAE3C;;AAoBA,SAASkoB,qBACPJ,GACA5K,GACApT,GACA8e;EAEApnB,KAAKsmB,GAAM;EACXW,WAAWX,GAAM5K,GAAUpT,GAAS8e;AACtC;;AAYA,SAASH,WACPX,GACA5K,GACApT,GACA8e;EAEA,KAAKA;IACH;;EAGF,IAAIA,aAAaC,EAAAA,gBAAgB;IAC/BJ,WAAWX,GAAM5K,GAAUpT,GAAS8e,EAAEzD;IACtC3jB,KAAKsmB,GAAM;AACZ,SAAM,IAAIc,aAAapB,eAAa;IACnChmB,KAAKsmB,GAAM;IACXW,WAAWX,GAAM5K,GAAUpT,GAAS8e,EAAEzD;IACtC3jB,KAAKsmB,GAAM;;IAEXtmB,KAAKsmB,GAAMc,EAAE5oB;;AAEjB;;AAEA,SAASooB,kBACPN,GACAhe,GAEA4Q;EAEA,KAAKA;IACH;;EAEF,IAAMxR,IACuB,mBAApBwR,EAAIxR,cAA2BwR,EAAIxR,cAAc;EAC1D,IAAIA,GAAa;IACf1H,KAAKsmB,GAAM;IACXtmB,KAAKsmB,GAAM5e;;GAKf,SAAS4f,kBACPhB,GACAa,GACAjO;IAEA,KAAKA;MACH;;IAGF,IAAMqO,IAASrO,EAAI0F,qBAAqB;IACxC,KAAK2I;MACH;;IAEFvnB,KAAKsmB,GAAM;IACXtmB,KAAKsmB,GAAM;IACXtmB,KAAKsmB,GAAMiB;AACb,GAnBED,CAAkBhB,GAAMhe,GAAS4Q;AACnC;;AAoBA,SAASlZ,KAAKsmB,GAAgBvS;EAC5BuS,EAAKrnB,KAAK8U;AACZ;;AC5OO,MAAMyT;EAIXC,WAAAA,CAAYjlB,GAAcklB;IACxBpe,KAAK9G,OAAOA;IACZ8G,KAAK7G,YAAYilB;AACnB;EAEAC,OAAAA,CAAQnlB;IACN8G,KAAK9G,OAAOA;AACd;EAEAolB,YAAAA,CAAanlB;IACX6G,KAAK7G,YAAYA;AACnB;EAEAolB,iBAAAA,CAAkBvgB;IAChB,OACEgC,KAAK9G,OAAO8E,EAAS9E,QACpB8G,KAAK9G,SAAS8E,EAAS9E,QAAQ8G,KAAK7G,aAAa6E,EAAS7E;AAE/D;;;ACZK,IAAMqlB,WAAWA,CACtBC,GACAC;EAEA,KAAKC,EAAAA,GAAGC,kBAAkBH,OAAcE,KAAGE,oBAAoBJ;IAC7D;;EAIF,IAAMK,IADOL,EAAS5T,UAAUgB,MAAM,IAAI,GACvB4P,MAAM;EACzB,IAAMC,IAASC,EAAAA;EACf,IAAMhnB,IAAQ+mB,EAAOE;EACrB,IAAImD,IAAON,EAASO,aAAa;EAEjC,IAAIC,SAAgC7kB;EACpC,IAAI8kB,SAA+B9kB;EACnC,KAAK,IAAIlB,IAAO,GAAGA,IAAO4lB,EAAMhpB,QAAQoD,KAAQ;IAC9C,IAAI+lB;MAAY;;IAChB,IAAME,IAAOJ,IAAO;IACpB,IAAM5D,IAAS,IAAIU,EAAeA,gBAACiD,EAAM5lB,KAAQ;IACjD,QAAQiiB,EAAOW,OAAO;MACpB,IAAMvlB,IAAQmlB,EAAOnlB,MAAM4kB,GAAQxmB;MACnC,IAAM6C,IAAS2jB,EAAOG;MAEtB,IACE6D,IAAOhE,EAAOY,oBAAoB,KAAK2C,KACvCS,IAAOhE,EAAOE,wBAAwBqD,GACtC;QACAO,IAAaC,IACTA,IACA;UACEhmB;UACAW,OAAOshB,EAAOY,oBAAoB;UAClCjiB,KAAKqhB,EAAOE;UACZ7jB;UACA7C;UACAyqB,WAAW7oB;;QAEjB;AACF,aAAO,IAAe,SAAXiB;QACT0nB,IAAY;UACVhmB;UACAW,OAAOshB,EAAOY,oBAAoB;UAClCjiB,KAAKqhB,EAAOE;UACZ7jB;UACA7C;UACAyqB,WAAW7oB;;aAER,IAAe,QAAXiB,KAA6B,SAAXA;QAC3B0nB,IAAY;UACVhmB;UACAW,OAAOshB,EAAOY,oBAAoB;UAClCjiB,KAAKqhB,EAAOE;UACZ7jB;UACA7C;UACAyqB,WAAW7oB;;;QAGb2oB,SAAY9kB;;AAEhB;IAEA2kB,KAAQD,EAAM5lB,GAAOpD,SAAS;AAChC;EAEA,OAAOmpB;AAAU;;AC3CnB,SAAS3oB,SACPC,GACAC;EAEA,OAKF,SAASC,kBACPD,GACAE;IAEA,KAAKA;MACH,OAAOC,eAAkBH,IAAMI,MAAUA,EAAMC;;IAGjD,IAAMC,IAAcN,EAAKO,KAAIH,MAAU;MACrCI,WAAWC,aAAaC,cAAcN,EAAMO,QAAQT;MACpDE;;IAGF,OAAOD,eACLA,eAAeG,IAAaM,KAAQA,EAAKJ,aAAa,MACtDI,MAASA,EAAKR,MAAMC,eAEnBQ,MACC,CAACC,GAAGC,OACDD,EAAEV,MAAMC,eAAe,IAAI,MAAMU,EAAEX,MAAMC,eAAe,IAAI,MAC7DS,EAAEN,YAAYO,EAAEP,aAChBM,EAAEV,MAAMO,MAAMrB,SAASyB,EAAEX,MAAMO,MAAMrB,SAExCiB,KAAIK,KAAQA,EAAKR;AACtB,GA7BSH,CAAkBD,GAAMU,cAAcX,EAAMiB;AACrD;;AAgCA,SAASb,eACPc,GACArF;EAEA,IAAMsF,IAAWD,EAAME,OAAOvF;EAC9B,OAA2B,MAApBsF,EAAS5B,SAAe2B,IAAQC;AACzC;;AAEA,SAASR,cAAcR;EACrB,OAAOA,EAAKkB,cAAciG,QAAQ,OAAO;AAC3C;;AAGA,SAAS5G,aAAaa,GAAoBpB;EAExC,IAAIM,IAwBN,SAASe,gBAAgBT,GAAWC;IAClC,IAAI1B;IACJ,IAAImC;IACJ,IAAMC,IAAI;IACV,IAAMC,IAAUZ,EAAExB;IAClB,IAAMqC,IAAUZ,EAAEzB;IAElB,KAAKD,IAAI,GAAGA,KAAKqC,GAASrC;MACxBoC,EAAEpC,KAAK,EAACA;;IAGV,KAAKmC,IAAI,GAAGA,KAAKG,GAASH;MACxBC,EAAE,GAAID,KAAKA;;IAGb,KAAKnC,IAAI,GAAGA,KAAKqC,GAASrC;MACxB,KAAKmC,IAAI,GAAGA,KAAKG,GAASH,KAAK;QAC7B,IAAMI,IAAOd,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,KAAK,IAAI;QAEzCC,EAAEpC,GAAImC,KAAKK,KAAKC,IACdL,EAAEpC,IAAI,GAAImC,KAAM,GAChBC,EAAEpC,GAAImC,IAAI,KAAM,GAChBC,EAAEpC,IAAI,GAAImC,IAAI,KAAMI;QAGtB,IAAIvC,IAAI,KAAKmC,IAAI,KAAKV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,MAAMV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI;UAChEC,EAAEpC,GAAImC,KAAKK,KAAKC,IAAIL,EAAEpC,GAAImC,IAAKC,EAAEpC,IAAI,GAAImC,IAAI,KAAMI;;AAEvD;;IAGF,OAAOH,EAAEC,GAAUC;AACrB,GAxDkBJ,CAAgBrB,GAAMoB;EACtC,IAAIA,EAAWhC,SAASY,EAAKZ,QAAQ;IAEnCkB,KAAac,EAAWhC,SAASY,EAAKZ,SAAS;IAE/CkB,KAA0C,MAA7Bc,EAAWS,QAAQ7B,KAAc,IAAI;AACpD;EACA,OAAOM;AACT;;AC/DO,SAASqoB,sBACdC,GACAZ,GACA5pB,GACAyqB;EAEA,IAAMC,IAAmBD,EAAKxC,OAAO0C,6BAA4B;EACjE,IAAMC,IAAcH,EAAKI,gBAAgBC,cAAcC;EACvD,IAAM3iB,IAAS4iB,EAAAA,UAAUP,GAAMD;EAC/B,KAAKpiB;IAAQ;;EAEb,IAAIgU,IAAO6O,EAAAA,SAAS7iB,GAAQwhB;EAC5B,KAAKxN;IAAM;;EAEXA,IAAOsO,IACHQ,EAAsBA,uBAAC9O,KACvB+O,EAAAA,iBAAiB/O;EAErB,IAAIxa,GAAM6a,GAAQ2O;EAClB,IAAIV,KAAoBW,EAAAA,cAAqBjP,GAAMwO,IAAc;IAC/D,IAAMU,IAAaD,EAAAA,cAAqBjP,GAAMwO;IAE9CQ,IACEE,KAActrB,EAAOurB,MAAMD,KACvBtrB,EAAOurB,MAAMD,IAAatrB,SAC1BA,EAAOwmB,SAASxmB;IAEtB,IAAMmqB,IAAaT,SAAStN,EAAKtT,UAAU,IAAI8gB;IAC/C,KACGwB,MACAjB,KACqB,QAAtBA,EAAWznB,UACW,SAAtBynB,EAAWznB;MAEX;;IAKFd,IAAQ,GAHUwa,EAAKtT,UAAU,GAAGiN,UAAUgB,MAAM,IAAI,OACtCyU,EAAeA,gBAAChB,GAAUpO,GAAMqO,GAEhBxoB,KAAIwpB,KAAKC,EAAKA,MAACD,KAAIlsB,KAAK;IAC1Dkd,IAAS,IAAI2M,OAAOe,EAAW/lB,MAAM+lB,EAAWplB,QAAQ;AACzD,SAAM,KAAK2lB,KAAoBW,EAAmBM,aAACvP,IAAO;IACzD,IAAM+N,IAAaT,SAAStN,EAAKuN,UAAUC;IAC3C,KACGO,MACAnqB,EAAOwmB,WACc,QAAtB2D,EAAWznB,UACW,SAAtBynB,EAAWznB;MAEX;;IAEF,KAAMkpB,cAAEA,GAAYC,eAAEA,KAAkBC,kBACtC1P,GACAoO,GACAC;IAGF,IAAMsB,IAAgBF,EACnBhpB,QACC4oB,KACEA,EAAEO,SAASjnB,QAAQ6kB,KACnB6B,EAAEO,SAASjnB,QAAQ0mB,EAAEO,SAAShrB,SAAS4oB,IAE1CqC,QAAO,CAACC,GAAKC,MAASD,KAAOC,EAAKzF,QAAQ,KAAI;IAEjDyD,EAAW/lB,OAAO+lB,EAAW/lB,OAAO2nB;IAEpCnqB,IAAOgqB;IACPnP,IAAS,IAAI2M,OAAOe,EAAW/lB,MAAM+lB,EAAWplB,QAAQ;IACxDqmB,IAAcprB,EAAOwmB,QAAQxmB;AAC/B;IACE;;EAGF,KAAOosB,GAAaC,KAuCf,SAASC,uBACdtsB,GACAwc,GACAC;IAEA,IAAMhb,IAAQob,mBAAmBL,GAAWC;IAE5C,IAAIoG,IAA2C;IAC/C;MAEEA,IADejI,EAAKA,MAAC4B,GAAW;QAAE+P,aAAY;SAC3BvG,YAAYnjB,QAC7B4oB,KAAKA,EAAE3rB,SAAS4Y,OAAKqK;AAEzB,MAAE,OAAOvM,IAAI;IAEb,IAAMgW,IACa,SAAjB/qB,EAAMiB,UAAwC,oBAArBjB,EAAM5B,MAAMC;IACvC,IAAIssB,IAAc7P,2BAChBvc,GACAwc,GACAC,GACA+P,IACI;SACK/qB;MACH5B,OAAO;WACF4B,EAAM5B;QACTwd,MAAM;;MAERpd,MAAM;aAERqF;IAEN,IAAI+mB,KAAqBG,IDfpB,SAAS7J,gCACdlhB,GACA6b,GACAtd,GACAwc,GACAqG;MAEA,KAAKrG;QACH,OAAO;;MAGT,IAAM+B,IAAUve,EAAO4d;MACvB,IAAMgF,IAAWnjB,mBAAmBgC,EAAM5B;MAuB1C,OAAO2B,SACLC,GArBoBohB,EAAUhgB,QAC9BwgB,KAEE9E,EAAQ8E,EAAKF,cAAc/iB,KAAKwD,YAG9Bgf,KACAA,EAAS9iB,SAAS0Y,GAAUuK,uBAC5BH,EAASxiB,SAASijB,EAAKjjB,KAAKwD,UAG9BpD,kBAAgB8c,EAAS8C,eACzB5f,EAAeA,gBAAC+d,EAAQ8E,EAAKF,cAAc/iB,KAAKwD,WAChD0f,EAAAA,eACEtjB,GACAsd,EAAS8C,YACT7B,EAAQ8E,EAAKF,cAAc/iB,KAAKwD,UAMtB3B,KAAIohB,MAAS;QACzBhhB,OAAOghB,EAAKjjB,KAAKwD;QACjBwM,QAAQqB,OAAO8M,EAAQ8E,EAAKF,cAAc/iB,KAAKwD;QAC/CmN,eAAgB,YAAWsS,EAAKjjB,KAAKwD,YAAYyf,EAAKF,cAAc/iB,KAAKwD;QACzE9D,MAAMsO,GAAmBK;QACzBxO,MAAMse,EAAQ8E,EAAKF,cAAc/iB,KAAKwD;;AAG5C,KC7BM+e,CACElhB,GACA8b,YAAYvd,GAAQyB,EAAM5B,QAC1BG,GACAwc,GACAqG,KAEF;IAEJ,IAAMhjB,IACiB,cAArB4B,EAAM5B,MAAMC,OAAqB2B,EAAM5B,MAAMiB,YAAYW,EAAM5B;IACjE,IAAM4sB,IAAahH,oBAAoBhkB,EAAM5B,OAAO2Y,GAAU0H,QAAQ9f;IAEtE,IAAIP,KAAS4sB,GAAY;MACvB,KAAM3sB,MAAEA,KAASD;MAGjB,IAAIC,MAAS0Y,GAAUI,aAAa9Y,MAAS0Y,GAAUqI,UAAU;QAC/D,IAAM6L,IAAgB,IAAI/N;QAE1BC,gBAAgBpC,IAAW,CAACqC,GAAGhf;UAC7B,IAAIA,EAAMC,SAAS0Y,GAAUqI,UAAU;YACrC,IAAMoF,IAAmBR,oBAAoB5lB,GAAO2Y,GAAU0H;YAC9D,IACEuM,KACA5sB,EAAMO,QACN6lB,GAAkB7lB,SAASqsB;cAE3BC,EAAcjhB,IAAI5L,EAAMO;;AAE5B;AAAA;QAGFgsB,IAAcA,EAAYvpB,QACxBG,MAAe0pB,EAAcC,IAAI3pB,EAAWX;AAEhD;MAGA,IACEvC,MAAS0Y,GAAUyH,iBACnBngB,MAAS0Y,GAAU0H,SACnBpgB,MAAS0Y,GAAUG,eACnB;QACA,IAAMiU,IAAa,IAAIjO;QACvB,IAAMkO,IAAgBC,iBAAiBtQ,GAAWiQ;QAElD7N,gBAAgBpC,IAAW,CAACqC,GAAGhf;UAC7B,IACEA,EAAMC,SAAS0Y,GAAU0H,SACzBrgB,EAAMC,SAAS0Y,GAAUG,eACzB;YACA,IAAMsN,IAAmBR,oBAAoB5lB,GAAO2Y,GAAU0H;YAC9D,IACE+F,KACAA,EAAiB7lB,SAASqsB,KAC1B5sB,EAAMO;cAENwsB,EAAWnhB,IAAI5L,EAAMO;;AAEzB;AAAA;QAGFgsB,IAAcA,EAAYvpB,QACxBG,MAAe4pB,EAAWD,IAAI3pB,EAAWX;QAE3CgqB,IAAoBA,EAAkBxpB,QACpCG,MAAe6pB,EAAcF,IAAI3pB,EAAWX;AAEhD;MAGA,IAAIvC,MAAS0Y,GAAUkK,iBAAiB;QACtC,IAAMmK,IAAgBC,iBAAiBtQ,GAAWiQ;QAClDL,IAAcA,EAAYvpB,QACxBG,MAAe6pB,EAAcF,IAAI3pB,EAAWX;QAE9CgqB,IAAoBA,EAAkBxpB,QACpCG,MAAe6pB,EAAcF,IAAI3pB,EAAWX;AAEhD;AACF;IAEA,OAAO,EAAC+pB,GAAaC;AACvB,GA5J2CC,CACvClB,GACAxpB,GACA6a;EAGF,OAAO;IACLsQ,qBAAoB;IACpBC,qBAAoB;IACpBC,0BAAyB;IACzBC,SAAS,KACJd,EAAYnqB,KAAIe,MAAe;SAC7BA;MACHlD,MAAM+pB,EAAAA,GAAGsD,kBAAkBC;MAC3BhtB,MAAM4C,EAAWX;MACjBgrB,eAAe;MACf9M,UAAUvd,EAAWud,YAAY;MACjC+M,cAAc;QACZld,QAAQpN,EAAW/C,OACf,MAAM+C,EAAW/C,MAAMuM,kBACvBlH;QACJgE,aAAatG,EAAW+N;;aAGzBsb,EAAkBpqB,KAAIe,MAAe;SACnCA;MACHlD,MAAM+pB,EAAAA,GAAGsD,kBAAkBC;MAC3BhtB,MAAM4C,EAAWX;MACjBqe,YAAY,QAAQ1d,EAAWX;MAC/BgrB,eAAe;MACf9M,UAAU;MACV+M,cAAc;QACZhkB,aAAatG,EAAW+N;;;;AAKlC;;AAyHA,SAAS+b,iBAAiBtQ,GAAmBiQ;EAC3C,IAAMI,IAAgB,IAAIlO;EAE1BC,gBAAgBpC,IAAW,CAACqC,GAAGhf;IAC7B,IAAIA,EAAMC,SAAS0Y,GAAUkK,mBAAmB7iB,EAAMO,MAAM;MAC1D,IAAM6lB,IAAmBR,oBAAoB5lB,GAAO2Y,GAAU0H;MAC9D,IAAIuM,KAAcxG,GAAkB7lB,SAASqsB;QAC3CI,EAAcphB,IAAI5L,EAAMO;;AAE5B;AAAA;EAGF,OAAOysB;AACT;;AAKA,SAASpH,oBAAoB5lB,GAAcC;EACzC,IAAID,EAAMiB,WAAWhB,SAASA;IAC5B,OAAOD,EAAMiB;;EAEf,IAAIjB,EAAMiB,WAAWA,WAAWhB,SAASA;IACvC,OAAOD,EAAMiB,UAAUA;;EAEzB,IAAIjB,EAAMiB,WAAWA,WAAWA,WAAWhB,SAASA;IAClD,OAAOD,EAAMiB,UAAUA,UAAUA;;EAEnC,IAAIjB,EAAMiB,WAAWA,WAAWA,WAAWA,WAAWhB,SAASA;IAC7D,OAAOD,EAAMiB,UAAUA,UAAUA,UAAUA;;AAE/C;;AAEA,SAAS8d,gBACPpC,GACAiK;EAOA,IAAMC,IAAQlK,EAAUmK,MAAM;EAC9B,IAAMC,IAASC,EAAAA;EACf,IAAIhnB,IAAQ+mB,EAAOE;EACnB,IAAIR,IAAQ;EAEZ,IAAID,IAA0B,IAAIU,kBAAgB;EAElD,KAAK,IAAIhmB,IAAI,GAAGA,IAAI2lB,EAAM1lB,QAAQD,KAAK;IACrCslB,IAAS,IAAIU,EAAeA,gBAACL,EAAM3lB;IACnC,QAAQslB,EAAOW,OAAO;MAGpB,IAAa,YADAP,EAASJ,GAAQxmB,GAD9BymB,IAAQM,EAAOnlB,MAAM4kB,GAAQxmB,IACekB;QAE1C;;AAEJ;IAIA0lB,EAASJ,GAAQxmB,GAAOymB,GAAOvlB;IAE/B,KAAKlB,EAAMC;MACTD,IAAQ+mB,EAAOE;;AAEnB;EAEA,OAAO;IACL/hB,OAAOshB,EAAOY;IACdjiB,KAAKqhB,EAAOE;IACZ7jB,QAAQ2jB,EAAOG;IACf3mB;IACAymB;;AAEJ;;AC9SA,SAASniB,OAAOsmB;EACd,IAAM3rB,SAAkByuB,KACtB9C,EAAK+C,QAAQC,eAAe3uB,OAAO2rB,KAAM,eAAc8C;EACzD,IAAMtF,IAAiBwC,EAAKxC;EAE5BnpB,OAAO,aAAa4uB,KAAKC,UAAU1F;EACnC,KAAKA,EAAOjoB,WAAWioB,EAAO2F,SAAS;IACrC9uB,OAAO;IACP,MAAM,IAAImG,MAAM;AAClB;EAEAnG,OAAO;EAEP,IAAImpB,EAAO0B;IACTkE,EAAAA,UAAUpiB,IAAIwc,EAAO0B;;EAGvB,IAAMmE,IA3CR,SAASC,qBAAqBtD;IAC5B,IAAMqD,IAA4B1sB,OAAO+C,OAAO;IAAM,IAAA6pB,QAAAA;MAIpD,IAAMvC,IAAIhB,EAAKI,gBAAgBoD;MAE/BH,EAAMG,KAAK,IAAIrlB,MAAoB6iB,EAAEyC,MAAMzD,EAAKI,iBAAiBjiB;;IALnE,KAAK,IAAIqlB,KAAK7sB,OAAOD,KAAKspB,EAAKI;MAAgBmD;;IAQ/C,OAAOF;AACT,GAgCgBC,CAAqBtD;EAEnC,IAAMzqB,IX2CkBmuB,EAExB1D,GACA2D,GACAtvB;IAEA,IAAMuvB,IAAMC,UAAQF;IAEpB;MACE,IAAMG,UACGC,EAAwBA,yBAAC/D,EAAK+C,QAAQiB,qBAC7CnvB,EAAKE,QAAQirB,EAAK+C,QAAQiB;MAE5B,IAAMC,IACJjE,EAAKxC,OAAOyG,6BAA4B;MAC1C,IAAM9vB,IACJ6rB,EAAKxC,OAAOrpB,sBACZU,EAAKqvB,QAAQJ,GAAU9D,EAAKxC,OAAOrpB;MAErCE,EAAO,gDAAgDyvB;MACvDzvB,EAAO,4CAA4C4uB,KAAKC,UAAUS;MAElE;QACEtvB,EAAQ;cACFuvB,EAAIO,KAAK;UAAEL;;AAClB,QAAC,OAAOpwB;QACPW,EAAQ,0BAAyBX;AACnC;MAEA,IAAIkwB,EAAI7H;QACN,IAAI6H,EAAI7H,gBAA8ClhB,MAAnC+oB,EAAI7H,QAAQ5nB;UAC7BF,sBACE2vB,EAAI7H,QAAQ7nB,eACZC,GACA8vB,GACA5vB;;aAGC,IAAIuvB,EAAI9C;QACbnqB,OAAOE,OAAO+sB,EAAI9C,OAAOxe,SAAQnJ;UAC/B,KAAKA;YAAO;;UAEZ,IAAIA,EAAMhF;YACRF,sBACEkF,EAAMjF,eACNW,EAAKqvB,QAAQJ,GAAU3qB,EAAMhF,qBAC7B8vB,GACA5vB;;AAEJ;;MAIJuvB,EAAIQ,WAAW;QAAEN;UAAY,CAACO,GAAWlrB;QACvC,KAAKA;UAAO;;QAEZ,IAAIA,EAAMhF,oBAAoB;UAC5B,IAAMmwB,IAAQD,EAAUvD,QACpBuD,EAAUvD,MAAM3nB,EAAMxD,QACtB0uB,EAAUtI;UACd,KAAKuI;YAAO;;UACZrwB,sBACEqwB,EAAMpwB,eACNW,EAAKqvB,QAAQJ,GAAU3qB,EAAMhF,qBAC7B8vB,GACA5vB;AAEJ;AAAA;AAEH,MA7DD;IA+DA,OAAOuvB;AAAG,IWlHKF,CAAW1D,GAAMxC,GAAQnpB;EAExCgvB,EAAMkB,yBAA0BxE;IAC9B,IAAMyE,IACJxE,EAAKI,gBAAgBmE,uBAAuBxE;IAK9C,IAH8ByE,EAAoBC,MAAKzD,KACrD0D,EAAeA,gBAAC9S,SAASoP,EAAEtjB;MAEF,OAAO8mB;;IAElC,IAAMG,IAAqBC,EAAqBA,sBAAC7E,GAAUxqB,GAAQyqB;IAEnE,OAAO2E,IACH,KAAIA,MAAuBH,MAC3BA;AAAmB;EAGzBnB,EAAMwB,2BAA2B,CAC/B9E,GACAZ,GACA1f;IAEA,IAAMqlB,IAAchF,sBAClBC,GACAZ,GACA5pB,GACAyqB;IAGF,IAAI8E,KAAeA,EAAYrC,QAAQlsB;MACrC,OAAOuuB;;MAEP,OACE9E,EAAKI,gBAAgByE,yBACnB9E,GACAZ,GACA1f,MACG;QACH6iB,qBAAoB;QACpBC,qBAAoB;QACpBC,0BAAyB;QACzBC,SAAS;;;AAGf;EAGFY,EAAM0B,sBAAsB,CAC1BhF,GACAiF,GACAC,GACAC,GACAC,GACAC,GACAC;IAEA,IAAM9D,IAAWvB,EAAKI,gBAAgB2E,oBACpChF,GACAiF,GACAC,GACAC,GACAC,GACAC,GACAC;IAGF,IAAMC,IAAUC,EAAAA,8BACdxF,GAC2B,mBAApBkF,IACHA,IACAA,EAAgBO,KACpBxF;IAEF,KAAKsF;MAAS,OAAO/D;;IACrB,OAAO;MACLliB,OAAO,EACL;QACEomB,UAAU1F;QACV2F,aAAa,EAAC;UAAEnnB,SAAS+mB,EAAQK;UAAajE,MAAM4D,EAAQ5D;;;;AAGjE;EAGH2B,EAAMuC,yBAAyB,CAC7B7F,GACAkF,GACAG,GACA1G,GACArpB,GACAwwB;IAEA,IAAMtE,IAAWvB,EAAKI,gBAAgBwF,uBACpC7F,GACAkF,GACAG,GACA1G,GACArpB,GACAwwB;IAWF,IARgBN,EAAAA,8BACdxF,GAC2B,mBAApBkF,IACHA,IACAA,EAAgBO,KACpBxF;MAIA,OAAO,EACL;QACErqB,MAAM;QACNkJ,aAAa;QACbinB,SAAS,EACP;UACEnwB,MAAM;UACNkJ,aACE;;QAGNknB,aAAY;YAEXxE;;MAGL,OAAOA;;AACT;EAGF8B,EAAM2C,yBAAyB,CAACjG,GAAkBZ;IAChD,IAAM8G,ICxKH,SAASC,oBACdnG,GACAZ,GACA5pB,GACAyqB;MAEA,IAAMC,IAAmBD,EAAKxC,OAAO0C,6BAA4B;MACjE,IAAMC,IAAcH,EAAKI,gBAAgBC,cAAcC;MAEvD,IAAM3iB,IAAS4iB,EAAAA,UAAUP,GAAMD;MAC/B,KAAKpiB;QAAQ;;MAEb,IAAIgU,IAAO6O,EAAAA,SAAS7iB,GAAQwhB;MAC5B,KAAKxN;QAAM;;MAEXA,IAAOsO,IACHQ,EAAsBA,uBAAC9O,KACvB+O,EAAAA,iBAAiB/O;MAErB,IAAIK,GAAQ7a,GAAMwpB;MAClB,IAAIV,KAAoBW,EAAAA,cAAqBjP,GAAMwO,IAAc;QAC/D,IAAMA,IAAcH,EAAKI,gBAAgBC,cAAcC;QACvD,IAAMO,IAAasF,EAAAA,cAAcxU,GAAMwO;QAEvCQ,IACEE,KAActrB,EAAOurB,MAAMD,KACvBtrB,EAAOurB,MAAMD,IAAatrB,SAC1BA,EAAOwmB,SAASxmB;QAEtB,IAAMmqB,IAAaT,SAAStN,EAAKtT,UAAU,IAAI8gB;QAC/C,KAAKwB,MAAgBjB;UAAY;;QAEjCvoB,IAAOwa,EAAKtT,UAAU,GAAGiN;QACzB0G,IAAS,IAAI2M,OAAOe,EAAW/lB,MAAM+lB,EAAWplB,QAAQ;AACzD,aAAM,KAAK2lB,KAAoBW,EAAmBM,aAACvP,IAAO;QACzD,IAAM+N,IAAaT,SAAStN,EAAKuN,UAAUC;QAC3C,KAAKO,MAAenqB,EAAOwmB;UAAS;;QAEpC,KAAMoF,cAAEA,GAAYC,eAAEA,KAAkBC,kBACtC1P,GACAoO,GACAC;QAGF,IAAMsB,IAAgBF,EACnBhpB,QACC4oB,KACEA,EAAEO,SAASjnB,QAAQ6kB,KACnB6B,EAAEO,SAASjnB,QAAQ0mB,EAAEO,SAAShrB,SAAS4oB,IAE1CqC,QAAO,CAACC,GAAKC,MAASD,KAAOC,EAAKzF,QAAQ,KAAI;QAEjDyD,EAAW/lB,OAAO+lB,EAAW/lB,OAAO2nB;QACpCnqB,IAAOgqB;QACPnP,IAAS,IAAI2M,OAAOe,EAAW/lB,MAAM+lB,EAAWplB,QAAQ;QACxDqmB,IAAcprB,EAAOwmB,QAAQxmB;AAC/B;QACE;;MAGF,IAAM6wB,IAAY7I,oBAAoBoD,GAAaxpB,GAAM6a;MAEzD,OAAO;QACL3c,MAAM+pB,EAAAA,GAAGsD,kBAAkB9qB;QAC3ByuB,UAAU;UACR/rB,OAAO6kB;UACP5oB,QAAQ;;QAEVqsB,eAAe;QACftc,eAAexP,MAAMyI,QAAQ6mB,KACzBA,EAAU5uB,KAAI8uB,MAAS;UAAEjxB,MAAM;UAAQ8B,MAAMmvB;eAC7C,EAAC;UAAEjxB,MAAM;UAAQ8B,MAAMivB;;;AAE/B,KD+FsBF,CAChBnG,GACAZ,GACA5pB,GACAyqB;IAGF,IAAIiG;MAAW,OAAOA;;IAEtB,OAAOjG,EAAKI,gBAAgB4F,uBAC1BjG,GACAZ;AACD;EAGH9qB,OAAO,YAAY4uB,KAAKC,UAAUG;EAElC,OAAOA;AACT;;iBAE4CjE;EAC1CmH,EAAcC,KAACpH;EACf,OAAO;IAAE1lB;;AAAQ", "x_google_ignoreList": [1, 2, 3, 4, 5, 6]}