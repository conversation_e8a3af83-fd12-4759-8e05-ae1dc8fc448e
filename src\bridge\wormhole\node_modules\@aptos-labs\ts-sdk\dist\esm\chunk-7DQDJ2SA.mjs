import{a as i}from"./chunk-HNBVYE3N.mjs";import{b as r}from"./chunk-RGKRCZ36.mjs";import{decode as u}from"js-base64";async function f(n){return new Promise(e=>{setTimeout(e,n)})}function A(n){return n instanceof Error?n.message:String(n)}var _=()=>Math.floor(Date.now()/1e3);function x(n){let e=new Date(n*1e3);return e.setMinutes(0),e.setSeconds(0),e.setMilliseconds(0),Math.floor(e.getTime()/1e3)}function S(n){let e=n.replace(/-/g,"+").replace(/_/g,"/"),t=e+"==".substring(0,(3-e.length%3)%3);return u(t)}function h(n){let e=n.replace(/-/g,"+").replace(/_/g,"/");for(;e.length%4!==0;)e+="=";return new Uint8Array(Buffer.from(e,"base64"))}var T=(n,e)=>n*10**e,b=(n,e)=>n/10**e,a=n=>{let e="";for(let t=2;t<n.length;t+=2)e+=String.fromCharCode(parseInt(n.substring(t,t+2),16));return e},M=n=>{let{account_address:e,module_name:t,struct_name:o}=n,s=a(t),c=a(o);return`${e}::${s}::${c}`},y=n=>typeof n=="object"&&!Array.isArray(n)&&n!==null&&"account_address"in n&&"module_name"in n&&"struct_name"in n&&typeof n.account_address=="string"&&typeof n.module_name=="string"&&typeof n.struct_name=="string";function v(n){let e=n.split("::");if(e.length!==3)throw new Error(`Invalid function ${n}`);let t=e[0],o=e[1],s=e[2];return{moduleAddress:t,moduleName:o,functionName:s}}function w(n){let e=n.split("::");return e.length===3&&r.isValid({input:e[0]}).valid}function E(n,e=6,t=5){return`${n.slice(0,e)}...${n.slice(-t)}`}var d="0x1::aptos_coin::AptosCoin",I=r.A.toStringLong();function m(n){let e=/0x[0-9a-fA-F]+/g;return n.replace(e,t=>r.from(t,{maxMissingChars:63}).toStringShort())}function $(n){let e=m(n);return e===d?r.A:i(r.A,e)}export{f as a,A as b,_ as c,x as d,S as e,h as f,T as g,b as h,M as i,y as j,v as k,w as l,E as m,$ as n};
//# sourceMappingURL=chunk-7DQDJ2SA.mjs.map