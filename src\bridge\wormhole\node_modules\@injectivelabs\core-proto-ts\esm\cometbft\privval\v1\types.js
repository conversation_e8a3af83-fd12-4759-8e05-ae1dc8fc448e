/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
import { Proposal, Vote } from "../../types/v1/types.js";
export const protobufPackage = "cometbft.privval.v1";
function createBaseRemoteSignerError() {
    return { code: 0, description: "" };
}
export const RemoteSignerError = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.code !== 0) {
            writer.uint32(8).int32(message.code);
        }
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRemoteSignerError();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.code = reader.int32();
                    break;
                case 2:
                    message.description = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            code: isSet(object.code) ? Number(object.code) : 0,
            description: isSet(object.description) ? String(object.description) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.code !== undefined && (obj.code = Math.round(message.code));
        message.description !== undefined && (obj.description = message.description);
        return obj;
    },
    create(base) {
        return RemoteSignerError.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRemoteSignerError();
        message.code = object.code ?? 0;
        message.description = object.description ?? "";
        return message;
    },
};
function createBasePubKeyRequest() {
    return { chainId: "" };
}
export const PubKeyRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.chainId !== "") {
            writer.uint32(10).string(message.chainId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePubKeyRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.chainId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { chainId: isSet(object.chainId) ? String(object.chainId) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.chainId !== undefined && (obj.chainId = message.chainId);
        return obj;
    },
    create(base) {
        return PubKeyRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePubKeyRequest();
        message.chainId = object.chainId ?? "";
        return message;
    },
};
function createBasePubKeyResponse() {
    return { error: undefined, pubKeyBytes: new Uint8Array(), pubKeyType: "" };
}
export const PubKeyResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.error !== undefined) {
            RemoteSignerError.encode(message.error, writer.uint32(18).fork()).ldelim();
        }
        if (message.pubKeyBytes.length !== 0) {
            writer.uint32(26).bytes(message.pubKeyBytes);
        }
        if (message.pubKeyType !== "") {
            writer.uint32(34).string(message.pubKeyType);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePubKeyResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2:
                    message.error = RemoteSignerError.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.pubKeyBytes = reader.bytes();
                    break;
                case 4:
                    message.pubKeyType = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            error: isSet(object.error) ? RemoteSignerError.fromJSON(object.error) : undefined,
            pubKeyBytes: isSet(object.pubKeyBytes) ? bytesFromBase64(object.pubKeyBytes) : new Uint8Array(),
            pubKeyType: isSet(object.pubKeyType) ? String(object.pubKeyType) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.error !== undefined && (obj.error = message.error ? RemoteSignerError.toJSON(message.error) : undefined);
        message.pubKeyBytes !== undefined &&
            (obj.pubKeyBytes = base64FromBytes(message.pubKeyBytes !== undefined ? message.pubKeyBytes : new Uint8Array()));
        message.pubKeyType !== undefined && (obj.pubKeyType = message.pubKeyType);
        return obj;
    },
    create(base) {
        return PubKeyResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePubKeyResponse();
        message.error = (object.error !== undefined && object.error !== null)
            ? RemoteSignerError.fromPartial(object.error)
            : undefined;
        message.pubKeyBytes = object.pubKeyBytes ?? new Uint8Array();
        message.pubKeyType = object.pubKeyType ?? "";
        return message;
    },
};
function createBaseSignVoteRequest() {
    return { vote: undefined, chainId: "", skipExtensionSigning: false };
}
export const SignVoteRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.vote !== undefined) {
            Vote.encode(message.vote, writer.uint32(10).fork()).ldelim();
        }
        if (message.chainId !== "") {
            writer.uint32(18).string(message.chainId);
        }
        if (message.skipExtensionSigning === true) {
            writer.uint32(24).bool(message.skipExtensionSigning);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignVoteRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.vote = Vote.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.chainId = reader.string();
                    break;
                case 3:
                    message.skipExtensionSigning = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            vote: isSet(object.vote) ? Vote.fromJSON(object.vote) : undefined,
            chainId: isSet(object.chainId) ? String(object.chainId) : "",
            skipExtensionSigning: isSet(object.skipExtensionSigning) ? Boolean(object.skipExtensionSigning) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.vote !== undefined && (obj.vote = message.vote ? Vote.toJSON(message.vote) : undefined);
        message.chainId !== undefined && (obj.chainId = message.chainId);
        message.skipExtensionSigning !== undefined && (obj.skipExtensionSigning = message.skipExtensionSigning);
        return obj;
    },
    create(base) {
        return SignVoteRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignVoteRequest();
        message.vote = (object.vote !== undefined && object.vote !== null) ? Vote.fromPartial(object.vote) : undefined;
        message.chainId = object.chainId ?? "";
        message.skipExtensionSigning = object.skipExtensionSigning ?? false;
        return message;
    },
};
function createBaseSignedVoteResponse() {
    return { vote: undefined, error: undefined };
}
export const SignedVoteResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.vote !== undefined) {
            Vote.encode(message.vote, writer.uint32(10).fork()).ldelim();
        }
        if (message.error !== undefined) {
            RemoteSignerError.encode(message.error, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignedVoteResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.vote = Vote.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.error = RemoteSignerError.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            vote: isSet(object.vote) ? Vote.fromJSON(object.vote) : undefined,
            error: isSet(object.error) ? RemoteSignerError.fromJSON(object.error) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.vote !== undefined && (obj.vote = message.vote ? Vote.toJSON(message.vote) : undefined);
        message.error !== undefined && (obj.error = message.error ? RemoteSignerError.toJSON(message.error) : undefined);
        return obj;
    },
    create(base) {
        return SignedVoteResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignedVoteResponse();
        message.vote = (object.vote !== undefined && object.vote !== null) ? Vote.fromPartial(object.vote) : undefined;
        message.error = (object.error !== undefined && object.error !== null)
            ? RemoteSignerError.fromPartial(object.error)
            : undefined;
        return message;
    },
};
function createBaseSignProposalRequest() {
    return { proposal: undefined, chainId: "" };
}
export const SignProposalRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.proposal !== undefined) {
            Proposal.encode(message.proposal, writer.uint32(10).fork()).ldelim();
        }
        if (message.chainId !== "") {
            writer.uint32(18).string(message.chainId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignProposalRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.proposal = Proposal.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.chainId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            proposal: isSet(object.proposal) ? Proposal.fromJSON(object.proposal) : undefined,
            chainId: isSet(object.chainId) ? String(object.chainId) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.proposal !== undefined && (obj.proposal = message.proposal ? Proposal.toJSON(message.proposal) : undefined);
        message.chainId !== undefined && (obj.chainId = message.chainId);
        return obj;
    },
    create(base) {
        return SignProposalRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignProposalRequest();
        message.proposal = (object.proposal !== undefined && object.proposal !== null)
            ? Proposal.fromPartial(object.proposal)
            : undefined;
        message.chainId = object.chainId ?? "";
        return message;
    },
};
function createBaseSignedProposalResponse() {
    return { proposal: undefined, error: undefined };
}
export const SignedProposalResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.proposal !== undefined) {
            Proposal.encode(message.proposal, writer.uint32(10).fork()).ldelim();
        }
        if (message.error !== undefined) {
            RemoteSignerError.encode(message.error, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignedProposalResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.proposal = Proposal.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.error = RemoteSignerError.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            proposal: isSet(object.proposal) ? Proposal.fromJSON(object.proposal) : undefined,
            error: isSet(object.error) ? RemoteSignerError.fromJSON(object.error) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.proposal !== undefined && (obj.proposal = message.proposal ? Proposal.toJSON(message.proposal) : undefined);
        message.error !== undefined && (obj.error = message.error ? RemoteSignerError.toJSON(message.error) : undefined);
        return obj;
    },
    create(base) {
        return SignedProposalResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignedProposalResponse();
        message.proposal = (object.proposal !== undefined && object.proposal !== null)
            ? Proposal.fromPartial(object.proposal)
            : undefined;
        message.error = (object.error !== undefined && object.error !== null)
            ? RemoteSignerError.fromPartial(object.error)
            : undefined;
        return message;
    },
};
function createBaseSignBytesRequest() {
    return { value: new Uint8Array() };
}
export const SignBytesRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.value.length !== 0) {
            writer.uint32(10).bytes(message.value);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignBytesRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.value = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { value: isSet(object.value) ? bytesFromBase64(object.value) : new Uint8Array() };
    },
    toJSON(message) {
        const obj = {};
        message.value !== undefined &&
            (obj.value = base64FromBytes(message.value !== undefined ? message.value : new Uint8Array()));
        return obj;
    },
    create(base) {
        return SignBytesRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignBytesRequest();
        message.value = object.value ?? new Uint8Array();
        return message;
    },
};
function createBaseSignBytesResponse() {
    return { signature: new Uint8Array(), error: undefined };
}
export const SignBytesResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.signature.length !== 0) {
            writer.uint32(10).bytes(message.signature);
        }
        if (message.error !== undefined) {
            RemoteSignerError.encode(message.error, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSignBytesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.signature = reader.bytes();
                    break;
                case 2:
                    message.error = RemoteSignerError.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            signature: isSet(object.signature) ? bytesFromBase64(object.signature) : new Uint8Array(),
            error: isSet(object.error) ? RemoteSignerError.fromJSON(object.error) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.signature !== undefined &&
            (obj.signature = base64FromBytes(message.signature !== undefined ? message.signature : new Uint8Array()));
        message.error !== undefined && (obj.error = message.error ? RemoteSignerError.toJSON(message.error) : undefined);
        return obj;
    },
    create(base) {
        return SignBytesResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSignBytesResponse();
        message.signature = object.signature ?? new Uint8Array();
        message.error = (object.error !== undefined && object.error !== null)
            ? RemoteSignerError.fromPartial(object.error)
            : undefined;
        return message;
    },
};
function createBasePingRequest() {
    return {};
}
export const PingRequest = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePingRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PingRequest.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePingRequest();
        return message;
    },
};
function createBasePingResponse() {
    return {};
}
export const PingResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePingResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return PingResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBasePingResponse();
        return message;
    },
};
function createBaseMessage() {
    return {
        pubKeyRequest: undefined,
        pubKeyResponse: undefined,
        signVoteRequest: undefined,
        signedVoteResponse: undefined,
        signProposalRequest: undefined,
        signedProposalResponse: undefined,
        pingRequest: undefined,
        pingResponse: undefined,
        signBytesRequest: undefined,
        signBytesResponse: undefined,
    };
}
export const Message = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.pubKeyRequest !== undefined) {
            PubKeyRequest.encode(message.pubKeyRequest, writer.uint32(10).fork()).ldelim();
        }
        if (message.pubKeyResponse !== undefined) {
            PubKeyResponse.encode(message.pubKeyResponse, writer.uint32(18).fork()).ldelim();
        }
        if (message.signVoteRequest !== undefined) {
            SignVoteRequest.encode(message.signVoteRequest, writer.uint32(26).fork()).ldelim();
        }
        if (message.signedVoteResponse !== undefined) {
            SignedVoteResponse.encode(message.signedVoteResponse, writer.uint32(34).fork()).ldelim();
        }
        if (message.signProposalRequest !== undefined) {
            SignProposalRequest.encode(message.signProposalRequest, writer.uint32(42).fork()).ldelim();
        }
        if (message.signedProposalResponse !== undefined) {
            SignedProposalResponse.encode(message.signedProposalResponse, writer.uint32(50).fork()).ldelim();
        }
        if (message.pingRequest !== undefined) {
            PingRequest.encode(message.pingRequest, writer.uint32(58).fork()).ldelim();
        }
        if (message.pingResponse !== undefined) {
            PingResponse.encode(message.pingResponse, writer.uint32(66).fork()).ldelim();
        }
        if (message.signBytesRequest !== undefined) {
            SignBytesRequest.encode(message.signBytesRequest, writer.uint32(74).fork()).ldelim();
        }
        if (message.signBytesResponse !== undefined) {
            SignBytesResponse.encode(message.signBytesResponse, writer.uint32(82).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pubKeyRequest = PubKeyRequest.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.pubKeyResponse = PubKeyResponse.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.signVoteRequest = SignVoteRequest.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.signedVoteResponse = SignedVoteResponse.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.signProposalRequest = SignProposalRequest.decode(reader, reader.uint32());
                    break;
                case 6:
                    message.signedProposalResponse = SignedProposalResponse.decode(reader, reader.uint32());
                    break;
                case 7:
                    message.pingRequest = PingRequest.decode(reader, reader.uint32());
                    break;
                case 8:
                    message.pingResponse = PingResponse.decode(reader, reader.uint32());
                    break;
                case 9:
                    message.signBytesRequest = SignBytesRequest.decode(reader, reader.uint32());
                    break;
                case 10:
                    message.signBytesResponse = SignBytesResponse.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            pubKeyRequest: isSet(object.pubKeyRequest) ? PubKeyRequest.fromJSON(object.pubKeyRequest) : undefined,
            pubKeyResponse: isSet(object.pubKeyResponse) ? PubKeyResponse.fromJSON(object.pubKeyResponse) : undefined,
            signVoteRequest: isSet(object.signVoteRequest) ? SignVoteRequest.fromJSON(object.signVoteRequest) : undefined,
            signedVoteResponse: isSet(object.signedVoteResponse)
                ? SignedVoteResponse.fromJSON(object.signedVoteResponse)
                : undefined,
            signProposalRequest: isSet(object.signProposalRequest)
                ? SignProposalRequest.fromJSON(object.signProposalRequest)
                : undefined,
            signedProposalResponse: isSet(object.signedProposalResponse)
                ? SignedProposalResponse.fromJSON(object.signedProposalResponse)
                : undefined,
            pingRequest: isSet(object.pingRequest) ? PingRequest.fromJSON(object.pingRequest) : undefined,
            pingResponse: isSet(object.pingResponse) ? PingResponse.fromJSON(object.pingResponse) : undefined,
            signBytesRequest: isSet(object.signBytesRequest) ? SignBytesRequest.fromJSON(object.signBytesRequest) : undefined,
            signBytesResponse: isSet(object.signBytesResponse)
                ? SignBytesResponse.fromJSON(object.signBytesResponse)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.pubKeyRequest !== undefined &&
            (obj.pubKeyRequest = message.pubKeyRequest ? PubKeyRequest.toJSON(message.pubKeyRequest) : undefined);
        message.pubKeyResponse !== undefined &&
            (obj.pubKeyResponse = message.pubKeyResponse ? PubKeyResponse.toJSON(message.pubKeyResponse) : undefined);
        message.signVoteRequest !== undefined &&
            (obj.signVoteRequest = message.signVoteRequest ? SignVoteRequest.toJSON(message.signVoteRequest) : undefined);
        message.signedVoteResponse !== undefined && (obj.signedVoteResponse = message.signedVoteResponse
            ? SignedVoteResponse.toJSON(message.signedVoteResponse)
            : undefined);
        message.signProposalRequest !== undefined && (obj.signProposalRequest = message.signProposalRequest
            ? SignProposalRequest.toJSON(message.signProposalRequest)
            : undefined);
        message.signedProposalResponse !== undefined && (obj.signedProposalResponse = message.signedProposalResponse
            ? SignedProposalResponse.toJSON(message.signedProposalResponse)
            : undefined);
        message.pingRequest !== undefined &&
            (obj.pingRequest = message.pingRequest ? PingRequest.toJSON(message.pingRequest) : undefined);
        message.pingResponse !== undefined &&
            (obj.pingResponse = message.pingResponse ? PingResponse.toJSON(message.pingResponse) : undefined);
        message.signBytesRequest !== undefined &&
            (obj.signBytesRequest = message.signBytesRequest ? SignBytesRequest.toJSON(message.signBytesRequest) : undefined);
        message.signBytesResponse !== undefined && (obj.signBytesResponse = message.signBytesResponse
            ? SignBytesResponse.toJSON(message.signBytesResponse)
            : undefined);
        return obj;
    },
    create(base) {
        return Message.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMessage();
        message.pubKeyRequest = (object.pubKeyRequest !== undefined && object.pubKeyRequest !== null)
            ? PubKeyRequest.fromPartial(object.pubKeyRequest)
            : undefined;
        message.pubKeyResponse = (object.pubKeyResponse !== undefined && object.pubKeyResponse !== null)
            ? PubKeyResponse.fromPartial(object.pubKeyResponse)
            : undefined;
        message.signVoteRequest = (object.signVoteRequest !== undefined && object.signVoteRequest !== null)
            ? SignVoteRequest.fromPartial(object.signVoteRequest)
            : undefined;
        message.signedVoteResponse = (object.signedVoteResponse !== undefined && object.signedVoteResponse !== null)
            ? SignedVoteResponse.fromPartial(object.signedVoteResponse)
            : undefined;
        message.signProposalRequest = (object.signProposalRequest !== undefined && object.signProposalRequest !== null)
            ? SignProposalRequest.fromPartial(object.signProposalRequest)
            : undefined;
        message.signedProposalResponse =
            (object.signedProposalResponse !== undefined && object.signedProposalResponse !== null)
                ? SignedProposalResponse.fromPartial(object.signedProposalResponse)
                : undefined;
        message.pingRequest = (object.pingRequest !== undefined && object.pingRequest !== null)
            ? PingRequest.fromPartial(object.pingRequest)
            : undefined;
        message.pingResponse = (object.pingResponse !== undefined && object.pingResponse !== null)
            ? PingResponse.fromPartial(object.pingResponse)
            : undefined;
        message.signBytesRequest = (object.signBytesRequest !== undefined && object.signBytesRequest !== null)
            ? SignBytesRequest.fromPartial(object.signBytesRequest)
            : undefined;
        message.signBytesResponse = (object.signBytesResponse !== undefined && object.signBytesResponse !== null)
            ? SignBytesResponse.fromPartial(object.signBytesResponse)
            : undefined;
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
