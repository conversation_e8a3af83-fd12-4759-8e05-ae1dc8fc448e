{"version": 3, "file": "net_rpc_methods.js", "sourceRoot": "", "sources": ["../../src/net_rpc_methods.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAmBA,SAAsB,KAAK,CAAC,cAA8C;;QACzE,OAAO,cAAc,CAAC,IAAI,CAAC;YAC1B,MAAM,EAAE,aAAa;YACrB,MAAM,EAAE,EAAE;SACV,CAAC,CAAC;IACJ,CAAC;CAAA;AALD,sBAKC;AAED,SAAsB,YAAY,CAAC,cAA8C;;QAChF,OAAO,cAAc,CAAC,IAAI,CAAC;YAC1B,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,EAAE;SACV,CAAC,CAAC;IACJ,CAAC;CAAA;AALD,oCAKC;AAED,SAAsB,WAAW,CAAC,cAA8C;;QAC/E,OAAO,cAAc,CAAC,IAAI,CAAC;YAC1B,MAAM,EAAE,eAAe;YACvB,MAAM,EAAE,EAAE;SACV,CAAC,CAAC;IACJ,CAAC;CAAA;AALD,kCAKC"}