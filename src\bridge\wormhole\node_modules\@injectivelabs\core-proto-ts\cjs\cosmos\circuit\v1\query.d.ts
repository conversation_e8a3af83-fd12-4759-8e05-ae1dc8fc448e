import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { PageRequest, PageResponse } from "../../base/query/v1beta1/pagination";
import { GenesisAccountPermissions, Permissions } from "./types";
export declare const protobufPackage = "cosmos.circuit.v1";
/** QueryAccountRequest is the request type for the Query/Account RPC method. */
export interface QueryAccountRequest {
    address: string;
}
/** AccountResponse is the response type for the Query/Account RPC method. */
export interface AccountResponse {
    permission: Permissions | undefined;
}
/** QueryAccountsRequest is the request type for the Query/Accounts RPC method. */
export interface QueryAccountsRequest {
    /** pagination defines an optional pagination for the request. */
    pagination: PageRequest | undefined;
}
/** AccountsResponse is the response type for the Query/Accounts RPC method. */
export interface AccountsResponse {
    accounts: GenesisAccountPermissions[];
    /** pagination defines the pagination in the response. */
    pagination: PageResponse | undefined;
}
/** QueryDisableListRequest is the request type for the Query/DisabledList RPC method. */
export interface QueryDisabledListRequest {
}
/** DisabledListResponse is the response type for the Query/DisabledList RPC method. */
export interface DisabledListResponse {
    disabledList: string[];
}
export declare const QueryAccountRequest: {
    encode(message: QueryAccountRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryAccountRequest;
    fromJSON(object: any): QueryAccountRequest;
    toJSON(message: QueryAccountRequest): unknown;
    create(base?: DeepPartial<QueryAccountRequest>): QueryAccountRequest;
    fromPartial(object: DeepPartial<QueryAccountRequest>): QueryAccountRequest;
};
export declare const AccountResponse: {
    encode(message: AccountResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountResponse;
    fromJSON(object: any): AccountResponse;
    toJSON(message: AccountResponse): unknown;
    create(base?: DeepPartial<AccountResponse>): AccountResponse;
    fromPartial(object: DeepPartial<AccountResponse>): AccountResponse;
};
export declare const QueryAccountsRequest: {
    encode(message: QueryAccountsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryAccountsRequest;
    fromJSON(object: any): QueryAccountsRequest;
    toJSON(message: QueryAccountsRequest): unknown;
    create(base?: DeepPartial<QueryAccountsRequest>): QueryAccountsRequest;
    fromPartial(object: DeepPartial<QueryAccountsRequest>): QueryAccountsRequest;
};
export declare const AccountsResponse: {
    encode(message: AccountsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountsResponse;
    fromJSON(object: any): AccountsResponse;
    toJSON(message: AccountsResponse): unknown;
    create(base?: DeepPartial<AccountsResponse>): AccountsResponse;
    fromPartial(object: DeepPartial<AccountsResponse>): AccountsResponse;
};
export declare const QueryDisabledListRequest: {
    encode(_: QueryDisabledListRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryDisabledListRequest;
    fromJSON(_: any): QueryDisabledListRequest;
    toJSON(_: QueryDisabledListRequest): unknown;
    create(base?: DeepPartial<QueryDisabledListRequest>): QueryDisabledListRequest;
    fromPartial(_: DeepPartial<QueryDisabledListRequest>): QueryDisabledListRequest;
};
export declare const DisabledListResponse: {
    encode(message: DisabledListResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DisabledListResponse;
    fromJSON(object: any): DisabledListResponse;
    toJSON(message: DisabledListResponse): unknown;
    create(base?: DeepPartial<DisabledListResponse>): DisabledListResponse;
    fromPartial(object: DeepPartial<DisabledListResponse>): DisabledListResponse;
};
/** Query defines the circuit gRPC querier service. */
export interface Query {
    /** Account returns account permissions. */
    Account(request: DeepPartial<QueryAccountRequest>, metadata?: grpc.Metadata): Promise<AccountResponse>;
    /** Account returns account permissions. */
    Accounts(request: DeepPartial<QueryAccountsRequest>, metadata?: grpc.Metadata): Promise<AccountsResponse>;
    /** DisabledList returns a list of disabled message urls */
    DisabledList(request: DeepPartial<QueryDisabledListRequest>, metadata?: grpc.Metadata): Promise<DisabledListResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Account(request: DeepPartial<QueryAccountRequest>, metadata?: grpc.Metadata): Promise<AccountResponse>;
    Accounts(request: DeepPartial<QueryAccountsRequest>, metadata?: grpc.Metadata): Promise<AccountsResponse>;
    DisabledList(request: DeepPartial<QueryDisabledListRequest>, metadata?: grpc.Metadata): Promise<DisabledListResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryAccountDesc: UnaryMethodDefinitionish;
export declare const QueryAccountsDesc: UnaryMethodDefinitionish;
export declare const QueryDisabledListDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
