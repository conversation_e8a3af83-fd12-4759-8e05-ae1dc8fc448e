import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "injective_referral_rpc";
export interface GetReferrerDetailsRequest {
    /** Address of the referrer */
    referrerAddress: string;
}
export interface GetReferrerDetailsResponse {
    /** List of invitees */
    invitees: ReferralInvitee[];
    /** Total commission earned */
    totalCommission: string;
    /** Total trading volume */
    totalTradingVolume: string;
    /** Referrer code */
    referrerCode: string;
}
export interface ReferralInvitee {
    /** Address of the invitee */
    address: string;
    /** Commission earned from this invitee */
    commission: string;
    /** Trading volume of this invitee */
    tradingVolume: string;
    /** Join date in ISO 8601 format */
    joinDate: string;
}
export interface GetInviteeDetailsRequest {
    /** Address of the invitee */
    inviteeAddress: string;
}
export interface GetInviteeDetailsResponse {
    /** Address of the referrer */
    referrer: string;
    /** Referral code used */
    usedCode: string;
    /** Total trading volume */
    tradingVolume: string;
    /** Join date in ISO 8601 format */
    joinedAt: string;
    /** Whether the referral is still active */
    active: boolean;
}
export interface GetReferrerByCodeRequest {
    /** Referral code to look up */
    referralCode: string;
}
export interface GetReferrerByCodeResponse {
    /** Address of the referrer */
    referrerAddress: string;
}
export declare const GetReferrerDetailsRequest: {
    encode(message: GetReferrerDetailsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetReferrerDetailsRequest;
    fromJSON(object: any): GetReferrerDetailsRequest;
    toJSON(message: GetReferrerDetailsRequest): unknown;
    create(base?: DeepPartial<GetReferrerDetailsRequest>): GetReferrerDetailsRequest;
    fromPartial(object: DeepPartial<GetReferrerDetailsRequest>): GetReferrerDetailsRequest;
};
export declare const GetReferrerDetailsResponse: {
    encode(message: GetReferrerDetailsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetReferrerDetailsResponse;
    fromJSON(object: any): GetReferrerDetailsResponse;
    toJSON(message: GetReferrerDetailsResponse): unknown;
    create(base?: DeepPartial<GetReferrerDetailsResponse>): GetReferrerDetailsResponse;
    fromPartial(object: DeepPartial<GetReferrerDetailsResponse>): GetReferrerDetailsResponse;
};
export declare const ReferralInvitee: {
    encode(message: ReferralInvitee, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ReferralInvitee;
    fromJSON(object: any): ReferralInvitee;
    toJSON(message: ReferralInvitee): unknown;
    create(base?: DeepPartial<ReferralInvitee>): ReferralInvitee;
    fromPartial(object: DeepPartial<ReferralInvitee>): ReferralInvitee;
};
export declare const GetInviteeDetailsRequest: {
    encode(message: GetInviteeDetailsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetInviteeDetailsRequest;
    fromJSON(object: any): GetInviteeDetailsRequest;
    toJSON(message: GetInviteeDetailsRequest): unknown;
    create(base?: DeepPartial<GetInviteeDetailsRequest>): GetInviteeDetailsRequest;
    fromPartial(object: DeepPartial<GetInviteeDetailsRequest>): GetInviteeDetailsRequest;
};
export declare const GetInviteeDetailsResponse: {
    encode(message: GetInviteeDetailsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetInviteeDetailsResponse;
    fromJSON(object: any): GetInviteeDetailsResponse;
    toJSON(message: GetInviteeDetailsResponse): unknown;
    create(base?: DeepPartial<GetInviteeDetailsResponse>): GetInviteeDetailsResponse;
    fromPartial(object: DeepPartial<GetInviteeDetailsResponse>): GetInviteeDetailsResponse;
};
export declare const GetReferrerByCodeRequest: {
    encode(message: GetReferrerByCodeRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetReferrerByCodeRequest;
    fromJSON(object: any): GetReferrerByCodeRequest;
    toJSON(message: GetReferrerByCodeRequest): unknown;
    create(base?: DeepPartial<GetReferrerByCodeRequest>): GetReferrerByCodeRequest;
    fromPartial(object: DeepPartial<GetReferrerByCodeRequest>): GetReferrerByCodeRequest;
};
export declare const GetReferrerByCodeResponse: {
    encode(message: GetReferrerByCodeResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GetReferrerByCodeResponse;
    fromJSON(object: any): GetReferrerByCodeResponse;
    toJSON(message: GetReferrerByCodeResponse): unknown;
    create(base?: DeepPartial<GetReferrerByCodeResponse>): GetReferrerByCodeResponse;
    fromPartial(object: DeepPartial<GetReferrerByCodeResponse>): GetReferrerByCodeResponse;
};
/** InjectiveReferralRPC defines gRPC API for referral system */
export interface InjectiveReferralRPC {
    /**
     * Get referrer details including their invitees, commissions and trading
     * volumes
     */
    GetReferrerDetails(request: DeepPartial<GetReferrerDetailsRequest>, metadata?: grpc.Metadata): Promise<GetReferrerDetailsResponse>;
    /** Get invitee details including their referrer, trading volume and join date */
    GetInviteeDetails(request: DeepPartial<GetInviteeDetailsRequest>, metadata?: grpc.Metadata): Promise<GetInviteeDetailsResponse>;
    /** Get referrer details by their referral code */
    GetReferrerByCode(request: DeepPartial<GetReferrerByCodeRequest>, metadata?: grpc.Metadata): Promise<GetReferrerByCodeResponse>;
}
export declare class InjectiveReferralRPCClientImpl implements InjectiveReferralRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    GetReferrerDetails(request: DeepPartial<GetReferrerDetailsRequest>, metadata?: grpc.Metadata): Promise<GetReferrerDetailsResponse>;
    GetInviteeDetails(request: DeepPartial<GetInviteeDetailsRequest>, metadata?: grpc.Metadata): Promise<GetInviteeDetailsResponse>;
    GetReferrerByCode(request: DeepPartial<GetReferrerByCodeRequest>, metadata?: grpc.Metadata): Promise<GetReferrerByCodeResponse>;
}
export declare const InjectiveReferralRPCDesc: {
    serviceName: string;
};
export declare const InjectiveReferralRPCGetReferrerDetailsDesc: UnaryMethodDefinitionish;
export declare const InjectiveReferralRPCGetInviteeDetailsDesc: UnaryMethodDefinitionish;
export declare const InjectiveReferralRPCGetReferrerByCodeDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
