import { StreamOperation } from '../../../types/index.js';
import { InjectiveAccountRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Stream Transformer
 */
export declare class IndexerAccountStreamTransformer {
    static balanceStreamCallback: (response: InjectiveAccountRpc.StreamSubaccountBalanceResponse) => {
        balance: import("../index.js").SubaccountBalance | undefined;
        operation: StreamOperation;
        timestamp: string;
    };
}
