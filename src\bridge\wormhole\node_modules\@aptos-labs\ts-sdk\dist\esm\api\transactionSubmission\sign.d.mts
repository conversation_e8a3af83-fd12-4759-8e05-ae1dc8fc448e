import { AccountAuthenticator } from '../../transactions/authenticator/account.mjs';
import { AptosConfig } from '../aptosConfig.mjs';
import { AnyRawTransaction } from '../../transactions/types.mjs';
import { A as Account } from '../../Ed25519Account-D9XrCLfE.mjs';
import '../../bcs/deserializer.mjs';
import '../../types/types.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../bcs/serializer.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';
import '../../core/crypto/ed25519.mjs';
import '../../publicKey-CJOcUwJK.mjs';
import '../../core/accountAddress.mjs';
import '../../transactions/instances/transactionArgument.mjs';
import '../../core/crypto/signature.mjs';
import '../../core/crypto/privateKey.mjs';
import '../../utils/const.mjs';
import '../../core/crypto/multiEd25519.mjs';
import '../../core/crypto/multiKey.mjs';
import '../../core/crypto/singleKey.mjs';
import '../../core/crypto/secp256k1.mjs';
import '../../bcs/serializable/moveStructs.mjs';
import '../../bcs/serializable/movePrimitives.mjs';
import '../../bcs/serializable/fixedBytes.mjs';
import '../../transactions/instances/rawTransaction.mjs';
import '../../transactions/instances/chainId.mjs';
import '../../transactions/instances/transactionPayload.mjs';
import '../../transactions/instances/identifier.mjs';
import '../../transactions/instances/moduleId.mjs';
import '../../transactions/typeTag/index.mjs';
import '../../transactions/instances/simpleTransaction.mjs';
import '../../transactions/instances/multiAgentTransaction.mjs';

/**
 * A class to handle all `Sign` transaction operations.
 *
 * @param config - The configuration object for Aptos.
 * @group Implementation
 */
declare class Sign {
    readonly config: AptosConfig;
    /**
     * Creates an instance of the Aptos client with the specified configuration.
     *
     * @param config - The configuration settings for the Aptos client.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * async function runExample() {
     *     // Create a new Aptos client with testnet configuration
     *     const config = new AptosConfig({ network: Network.TESTNET });
     *     const aptos = new Aptos(config);
     *
     *     console.log("Aptos client created with config:", config);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    constructor(config: AptosConfig);
    /**
     * Signs a transaction using the provided account signer. This function is essential for ensuring that transactions are properly
     * authenticated before being sent to the network.
     *
     * @param args - The arguments for signing the transaction.
     * @param args.signer - The account that will sign the transaction.
     * @param args.transaction - The raw transaction data to be signed.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network, Account } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   const sender = Account.generate(); // Generate a new account for signing
     *   const transaction = await aptos.transaction.build.simple({
     *     sender: sender.accountAddress,
     *     data: {
     *       function: "0x1::aptos_account::transfer",
     *       functionArguments: [ "0x1", 100 ], // replace with a real account address and amount
     *     },
     *   });
     *
     *   // Sign the transaction
     *   const signedTransaction = await aptos.transaction.sign({
     *     signer: sender,
     *     transaction: transaction,
     *   });
     *
     *   console.log("Signed Transaction:", signedTransaction);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    transaction(args: {
        signer: Account;
        transaction: AnyRawTransaction;
    }): AccountAuthenticator;
    /**
     * Sets the fee payer address for a transaction and signs it with the provided account.
     * This function is essential for transactions that require a designated fee payer.
     *
     * @param args - The arguments for the function.
     * @param args.signer - The account that will sign the transaction.
     * @param args.transaction - The transaction object that requires a fee payer address.
     *
     * @throws Error if the transaction does not have a feePayerAddress property.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network, Account } from "@aptos-labs/ts-sdk";
     *
     * const config = new AptosConfig({ network: Network.TESTNET });
     * const aptos = new Aptos(config);
     *
     * async function runExample() {
     *   const sender = Account.generate(); // Generate a new account for signing
     *   const transaction = await aptos.transaction.build.simple({
     *     sender: sender.accountAddress,
     *     data: {
     *       function: "0x1::aptos_account::transfer",
     *       functionArguments: ["0x1", 100], // replace with a real recipient address
     *     },
     *   });
     *
     *   // Set the fee payer for the transaction
     *   transaction.feePayerAddress = "0x1"; // replace with a real fee payer address
     *
     *   const signedTransaction = await aptos.transactionAsFeePayer({ signer: sender, transaction });
     *
     *   console.log("Signed Transaction:", signedTransaction);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Implementation
     */
    transactionAsFeePayer(args: {
        signer: Account;
        transaction: AnyRawTransaction;
    }): AccountAuthenticator;
}

export { Sign };
