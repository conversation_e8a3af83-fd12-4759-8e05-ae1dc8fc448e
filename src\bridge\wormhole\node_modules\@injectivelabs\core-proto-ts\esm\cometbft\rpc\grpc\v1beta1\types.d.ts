import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { ResponseCheckTx, ResponseDeliverTx } from "../../../abci/v1beta1/types";
export declare const protobufPackage = "cometbft.rpc.grpc.v1beta1";
/** RequestPing is a request to confirm that the connection is alive. */
export interface RequestPing {
}
/** RequestBroadcastTx is a request to broadcast the transaction. */
export interface RequestBroadcastTx {
    tx: Uint8Array;
}
/** ResponsePing is a response to confirm that the connection is alive. */
export interface ResponsePing {
}
/** ResponseBroadcastTx is a response of broadcasting the transaction. */
export interface ResponseBroadcastTx {
    checkTx: ResponseCheckTx | undefined;
    deliverTx: ResponseDeliverTx | undefined;
}
export declare const RequestPing: {
    encode(_: RequestPing, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestPing;
    fromJSON(_: any): RequestPing;
    toJSON(_: RequestPing): unknown;
    create(base?: DeepPartial<RequestPing>): RequestPing;
    fromPartial(_: DeepPartial<RequestPing>): RequestPing;
};
export declare const RequestBroadcastTx: {
    encode(message: RequestBroadcastTx, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RequestBroadcastTx;
    fromJSON(object: any): RequestBroadcastTx;
    toJSON(message: RequestBroadcastTx): unknown;
    create(base?: DeepPartial<RequestBroadcastTx>): RequestBroadcastTx;
    fromPartial(object: DeepPartial<RequestBroadcastTx>): RequestBroadcastTx;
};
export declare const ResponsePing: {
    encode(_: ResponsePing, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponsePing;
    fromJSON(_: any): ResponsePing;
    toJSON(_: ResponsePing): unknown;
    create(base?: DeepPartial<ResponsePing>): ResponsePing;
    fromPartial(_: DeepPartial<ResponsePing>): ResponsePing;
};
export declare const ResponseBroadcastTx: {
    encode(message: ResponseBroadcastTx, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ResponseBroadcastTx;
    fromJSON(object: any): ResponseBroadcastTx;
    toJSON(message: ResponseBroadcastTx): unknown;
    create(base?: DeepPartial<ResponseBroadcastTx>): ResponseBroadcastTx;
    fromPartial(object: DeepPartial<ResponseBroadcastTx>): ResponseBroadcastTx;
};
/** BroadcastAPI is an API for broadcasting transactions. */
export interface BroadcastAPI {
    /** Ping the connection. */
    Ping(request: DeepPartial<RequestPing>, metadata?: grpc.Metadata): Promise<ResponsePing>;
    /** BroadcastTx broadcasts the transaction. */
    BroadcastTx(request: DeepPartial<RequestBroadcastTx>, metadata?: grpc.Metadata): Promise<ResponseBroadcastTx>;
}
export declare class BroadcastAPIClientImpl implements BroadcastAPI {
    private readonly rpc;
    constructor(rpc: Rpc);
    Ping(request: DeepPartial<RequestPing>, metadata?: grpc.Metadata): Promise<ResponsePing>;
    BroadcastTx(request: DeepPartial<RequestBroadcastTx>, metadata?: grpc.Metadata): Promise<ResponseBroadcastTx>;
}
export declare const BroadcastAPIDesc: {
    serviceName: string;
};
export declare const BroadcastAPIPingDesc: UnaryMethodDefinitionish;
export declare const BroadcastAPIBroadcastTxDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
