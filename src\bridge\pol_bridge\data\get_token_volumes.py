#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import requests
from typing import Dict, Any, List, Optional
import math

# 文件路径
ROUTABLE_TOKENS_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "routable_tokens_only.json")
OUTPUT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "low_volume_routable_tokens.json")

# 最大交易量阈值
MAX_VOLUME_USD = 288888

# API 相关配置
BASE_URL = "https://api.geckoterminal.com/api/v2"
MULTI_TOKEN_ENDPOINT = "/networks/polygon_pos/tokens/multi/{}"
BATCH_SIZE = 30  # GeckoTerminal API 限制每次最多查询30个地址
WAIT_TIME = 2  # 请求间隔时间(秒)

def load_json_file(file_path: str) -> Dict[str, Any]:
    """
    加载JSON文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {str(e)}")
        return {}

def save_json_file(data: Dict[str, Any], file_path: str) -> None:
    """
    保存数据到JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"数据已保存到 {file_path}")
    except Exception as e:
        print(f"保存文件 {file_path} 失败: {str(e)}")

def get_token_volumes(addresses: List[str]) -> Dict[str, float]:
    """
    获取多个代币的交易量数据
    
    Args:
        addresses: 代币地址列表
        
    Returns:
        代币地址到交易量的映射
    """
    if not addresses:
        return {}
    
    result = {}
    address_str = ",".join(addresses)
    url = f"{BASE_URL}{MULTI_TOKEN_ENDPOINT.format(address_str)}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            for token in data.get("data", []):
                token_address = token.get("id", "").split("_")[-1].lower()
                attributes = token.get("attributes", {})
                volume_usd = attributes.get("volume_usd", {}).get("h24", 0)
                if volume_usd is not None:
                    result[token_address] = float(volume_usd)
        else:
            print(f"API请求失败: 状态码 {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"获取代币交易量时出错: {str(e)}")
    
    return result

def extract_polygon_addresses(routable_tokens: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
    """
    从可路由代币数据中提取Polygon网络地址
    
    Args:
        routable_tokens: 可路由代币数据
        
    Returns:
        代币符号到Polygon地址和数据的映射
    """
    result = {}
    for symbol, token_data in routable_tokens.items():
        if "137" in token_data:
            polygon_data = token_data["137"]
            polygon_address = polygon_data.get("address", "").lower()
            if polygon_address:
                result[symbol] = {
                    "polygon_address": polygon_address,
                    "token_data": token_data
                }
    
    return result

def process_tokens_in_batches(tokens_with_addresses: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    按批次处理代币并获取交易量
    
    Args:
        tokens_with_addresses: 代币符号到Polygon地址和数据的映射
        
    Returns:
        更新后的代币数据
    """
    # 提取所有Polygon代币地址
    polygon_addresses = []
    symbols_to_addresses = {}
    
    for symbol, data in tokens_with_addresses.items():
        polygon_address = data["polygon_address"]
        polygon_addresses.append(polygon_address)
        symbols_to_addresses[polygon_address] = symbol
    
    # 计算需要处理的批次数
    batch_count = math.ceil(len(polygon_addresses) / BATCH_SIZE)
    print(f"共有 {len(polygon_addresses)} 个代币地址，将分为 {batch_count} 批处理")
    
    # 存储交易量数据
    volume_data = {}
    
    # 按批次处理
    for i in range(batch_count):
        start_idx = i * BATCH_SIZE
        end_idx = min((i + 1) * BATCH_SIZE, len(polygon_addresses))
        batch_addresses = polygon_addresses[start_idx:end_idx]
        
        print(f"处理第 {i+1}/{batch_count} 批，包含 {len(batch_addresses)} 个地址")
        batch_volumes = get_token_volumes(batch_addresses)
        volume_data.update(batch_volumes)
        
        if i < batch_count - 1:
            print(f"等待 {WAIT_TIME} 秒后处理下一批...")
            time.sleep(WAIT_TIME)
    
    # 根据交易量过滤代币
    filtered_tokens = {}
    high_volume_tokens = {}
    
    for polygon_address, volume in volume_data.items():
        symbol = symbols_to_addresses.get(polygon_address)
        if not symbol:
            continue
            
        token_data = tokens_with_addresses[symbol]["token_data"]
        
        # 添加交易量到代币数据
        if "137" in token_data:
            token_data["137"]["volume_usd"] = volume
        
        # 如果交易量低于阈值，保留该代币
        if volume <= MAX_VOLUME_USD:
            filtered_tokens[symbol] = token_data
            print(f"保留低交易量代币: {symbol} ({polygon_address}), 交易量: ${volume}")
        else:
            high_volume_tokens[symbol] = token_data
            print(f"过滤掉高交易量代币: {symbol} ({polygon_address}), 交易量: ${volume}")
    
    # 处理没有获取到交易量数据的代币
    for symbol, data in tokens_with_addresses.items():
        polygon_address = data["polygon_address"]
        if polygon_address not in volume_data:
            # 如果没有找到交易量数据，默认保留(可能是新代币)
            token_data = data["token_data"]
            if "137" in token_data:
                token_data["137"]["volume_usd"] = 0
            filtered_tokens[symbol] = token_data
            print(f"未找到交易量数据，默认保留: {symbol} ({polygon_address})")
    
    return filtered_tokens, high_volume_tokens

def main():
    """主函数"""
    print("开始获取代币交易量数据...")
    
    # 加载可路由代币数据
    routable_tokens = load_json_file(ROUTABLE_TOKENS_PATH)
    
    if not routable_tokens:
        print("加载数据失败，请确保routable_tokens_only.json文件存在且格式正确")
        return
    
    print(f"已加载 {len(routable_tokens)} 个可路由代币")
    
    # 提取Polygon地址
    tokens_with_addresses = extract_polygon_addresses(routable_tokens)
    print(f"找到 {len(tokens_with_addresses)} 个有Polygon地址的代币")
    
    # 处理代币并获取交易量
    filtered_tokens, high_volume_tokens = process_tokens_in_batches(tokens_with_addresses)
    
    # 保存结果
    save_json_file(filtered_tokens, OUTPUT_FILE)
    
    # 输出统计信息
    total_tokens = len(tokens_with_addresses)
    filtered_count = len(filtered_tokens)
    removed_count = len(high_volume_tokens)
    
    print(f"\n统计信息:")
    print(f"  原始代币总数: {total_tokens}")
    print(f"  保留的低交易量代币数: {filtered_count}")
    print(f"  移除的高交易量代币数: {removed_count}")
    print(f"  未获取交易量数据代币数: {total_tokens - filtered_count - removed_count}")
    print(f"  过滤比例: {(removed_count / total_tokens * 100):.2f}%")
    print(f"  交易量阈值: ${MAX_VOLUME_USD}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc() 