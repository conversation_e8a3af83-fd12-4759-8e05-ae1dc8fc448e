2025-05-18 20:24:38,477 - INFO - ================================================================================
2025-05-18 20:24:38,477 - INFO - 开始执行 OLAS 买入交易 - 时间: 2025-05-18 20:24:38
2025-05-18 20:24:38,477 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-18 20:24:38,477 - INFO - 代币地址: ******************************************
2025-05-18 20:24:38,478 - INFO - 收到OLAS买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-18 20:24:38,478 - INFO - OLAS: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-18 20:24:38,478 - INFO - OLAS: 准备使用KyberSwap在ethereum上执行300.0USDT买入OLAS交易
2025-05-18 20:24:38,478 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-18 20:24:38,500 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-18 20:24:38,500 - INFO - OLAS: 准备调用swap_tokens函数，参数：
2025-05-18 20:24:38,500 - INFO -   chain: ethereum
2025-05-18 20:24:38,500 - INFO -   token_in: USDT
2025-05-18 20:24:38,500 - INFO -   token_out: ******************************************
2025-05-18 20:24:38,500 - INFO -   amount: 300.0
2025-05-18 20:24:38,500 - INFO -   slippage: 0.5%
2025-05-18 20:24:38,500 - INFO -   real: True
2025-05-18 20:24:40,947 - INFO - OLAS: swap_tokens返回值类型: <class 'dict'>
2025-05-18 20:24:40,947 - INFO - OLAS: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-18 20:24:40,947 - ERROR - OLAS: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT
2025-05-18 20:24:40,947 - INFO - 读取到 115 条现有交易记录
2025-05-18 20:24:40,947 - INFO - 添加新交易记录: OLAS (OLAS_300.0_2025-05-18 20:24:38)
2025-05-18 20:24:40,949 - INFO - 成功保存 116 条交易记录
2025-05-18 20:24:40,949 - INFO - OLAS: 买入交易处理完成，耗时: 2.47秒
2025-05-18 20:24:40,950 - INFO - ================================================================================
2025-05-18 21:00:52,744 - INFO - ================================================================================
2025-05-18 21:00:52,744 - INFO - 开始执行 OLAS 买入交易 - 时间: 2025-05-18 21:00:52
2025-05-18 21:00:52,744 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-18 21:00:52,744 - INFO - 代币地址: ******************************************
2025-05-18 21:00:52,744 - INFO - 收到OLAS买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-18 21:00:52,744 - INFO - OLAS: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-18 21:00:52,744 - INFO - OLAS: 准备使用KyberSwap在ethereum上执行300.0USDT买入OLAS交易
2025-05-18 21:00:52,744 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-18 21:00:52,752 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-18 21:00:52,752 - INFO - OLAS: 准备调用swap_tokens函数，参数：
2025-05-18 21:00:52,752 - INFO -   chain: ethereum
2025-05-18 21:00:52,752 - INFO -   token_in: USDT
2025-05-18 21:00:52,752 - INFO -   token_out: ******************************************
2025-05-18 21:00:52,752 - INFO -   amount: 300.0
2025-05-18 21:00:52,752 - INFO -   slippage: 0.5%
2025-05-18 21:00:52,752 - INFO -   real: True
2025-05-18 21:00:55,514 - INFO - OLAS: swap_tokens返回值类型: <class 'dict'>
2025-05-18 21:00:55,514 - INFO - OLAS: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-18 21:00:55,514 - ERROR - OLAS: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT
2025-05-18 21:00:55,514 - INFO - 读取到 116 条现有交易记录
2025-05-18 21:00:55,514 - INFO - 添加新交易记录: OLAS (OLAS_300.0_2025-05-18 21:00:52)
2025-05-18 21:00:55,516 - INFO - 成功保存 117 条交易记录
2025-05-18 21:00:55,516 - INFO - OLAS: 买入交易处理完成，耗时: 2.77秒
2025-05-18 21:00:55,516 - INFO - ================================================================================
2025-05-18 21:35:44,985 - INFO - ================================================================================
2025-05-18 21:35:44,985 - INFO - 开始执行 OLAS 买入交易 - 时间: 2025-05-18 21:35:44
2025-05-18 21:35:44,985 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-18 21:35:44,985 - INFO - 代币地址: ******************************************
2025-05-18 21:35:44,986 - INFO - 收到OLAS买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-18 21:35:44,986 - INFO - OLAS: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-18 21:35:44,986 - INFO - OLAS: 准备使用KyberSwap在ethereum上执行300.0USDT买入OLAS交易
2025-05-18 21:35:44,986 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-18 21:35:44,986 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-18 21:35:44,986 - INFO - OLAS: 准备调用swap_tokens函数，参数：
2025-05-18 21:35:44,986 - INFO -   chain: ethereum
2025-05-18 21:35:44,986 - INFO -   token_in: USDT
2025-05-18 21:35:44,986 - INFO -   token_out: ******************************************
2025-05-18 21:35:44,986 - INFO -   amount: 300.0
2025-05-18 21:35:44,987 - INFO -   slippage: 0.5%
2025-05-18 21:35:44,987 - INFO -   real: True
2025-05-18 21:35:47,524 - INFO - OLAS: swap_tokens返回值类型: <class 'dict'>
2025-05-18 21:35:47,524 - INFO - OLAS: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-18 21:35:47,525 - ERROR - OLAS: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT
2025-05-18 21:35:47,525 - INFO - 读取到 117 条现有交易记录
2025-05-18 21:35:47,525 - INFO - 添加新交易记录: OLAS (OLAS_300.0_2025-05-18 21:35:44)
2025-05-18 21:35:47,527 - INFO - 成功保存 118 条交易记录
2025-05-18 21:35:47,527 - INFO - OLAS: 买入交易处理完成，耗时: 2.54秒
2025-05-18 21:35:47,527 - INFO - ================================================================================
2025-05-18 22:17:50,085 - INFO - ================================================================================
2025-05-18 22:17:50,085 - INFO - 开始执行 OLAS 买入交易 - 时间: 2025-05-18 22:17:50
2025-05-18 22:17:50,085 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-18 22:17:50,086 - INFO - 代币地址: ******************************************
2025-05-18 22:17:50,086 - INFO - 收到OLAS买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-18 22:17:50,086 - INFO - OLAS: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-18 22:17:50,086 - INFO - OLAS: 准备使用KyberSwap在ethereum上执行300.0USDT买入OLAS交易
2025-05-18 22:17:50,086 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-18 22:17:50,086 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-18 22:17:50,086 - INFO - OLAS: 准备调用swap_tokens函数，参数：
2025-05-18 22:17:50,086 - INFO -   chain: ethereum
2025-05-18 22:17:50,086 - INFO -   token_in: USDT
2025-05-18 22:17:50,086 - INFO -   token_out: ******************************************
2025-05-18 22:17:50,086 - INFO -   amount: 300.0
2025-05-18 22:17:50,086 - INFO -   slippage: 0.5%
2025-05-18 22:17:50,086 - INFO -   real: True
2025-05-18 22:17:52,673 - INFO - OLAS: swap_tokens返回值类型: <class 'dict'>
2025-05-18 22:17:52,673 - INFO - OLAS: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-18 22:17:52,673 - ERROR - OLAS: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 128.27626 USDT
2025-05-18 22:17:52,674 - INFO - 读取到 118 条现有交易记录
2025-05-18 22:17:52,674 - INFO - 添加新交易记录: OLAS (OLAS_300.0_2025-05-18 22:17:50)
2025-05-18 22:17:52,675 - INFO - 成功保存 119 条交易记录
2025-05-18 22:17:52,676 - INFO - OLAS: 买入交易处理完成，耗时: 2.59秒
2025-05-18 22:17:52,676 - INFO - ================================================================================
