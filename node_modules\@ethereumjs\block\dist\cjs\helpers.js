"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fakeExponential = exports.getNumBlobs = exports.getDifficulty = exports.valuesArrayToHeaderData = exports.numberToHex = void 0;
const tx_1 = require("@ethereumjs/tx");
const util_1 = require("@ethereumjs/util");
/**
 * Returns a 0x-prefixed hex number string from a hex string or string integer.
 * @param {string} input string to check, convert, and return
 */
const numberToHex = function (input) {
    if (input === undefined)
        return undefined;
    if (!(0, util_1.isHexString)(input)) {
        const regex = new RegExp(/^\d+$/); // test to make sure input contains only digits
        if (!regex.test(input)) {
            const msg = `Cannot convert string to hex string. numberToHex only supports 0x-prefixed hex or integer strings but the given string was: ${input}`;
            throw new Error(msg);
        }
        return `0x${parseInt(input, 10).toString(16)}`;
    }
    return input;
};
exports.numberToHex = numberToHex;
function valuesArrayToHeaderData(values) {
    const [parentHash, uncleHash, coinbase, stateRoot, transactionsTrie, receiptTrie, logsBloom, difficulty, number, gasLimit, gasUsed, timestamp, extraData, mixHash, nonce, baseFeePerGas, withdrawalsRoot, blobGasUsed, excessBlobGas, parentBeaconBlockRoot, requestsRoot,] = values;
    if (values.length > 21) {
        throw new Error(`invalid header. More values than expected were received. Max: 20, got: ${values.length}`);
    }
    if (values.length < 15) {
        throw new Error(`invalid header. Less values than expected were received. Min: 15, got: ${values.length}`);
    }
    return {
        parentHash,
        uncleHash,
        coinbase,
        stateRoot,
        transactionsTrie,
        receiptTrie,
        logsBloom,
        difficulty,
        number,
        gasLimit,
        gasUsed,
        timestamp,
        extraData,
        mixHash,
        nonce,
        baseFeePerGas,
        withdrawalsRoot,
        blobGasUsed,
        excessBlobGas,
        parentBeaconBlockRoot,
        requestsRoot,
    };
}
exports.valuesArrayToHeaderData = valuesArrayToHeaderData;
function getDifficulty(headerData) {
    const { difficulty } = headerData;
    if (difficulty !== undefined) {
        return (0, util_1.toType)(difficulty, util_1.TypeOutput.BigInt);
    }
    return null;
}
exports.getDifficulty = getDifficulty;
const getNumBlobs = (transactions) => {
    let numBlobs = 0;
    for (const tx of transactions) {
        if (tx instanceof tx_1.BlobEIP4844Transaction) {
            numBlobs += tx.blobVersionedHashes.length;
        }
    }
    return numBlobs;
};
exports.getNumBlobs = getNumBlobs;
/**
 * Approximates `factor * e ** (numerator / denominator)` using Taylor expansion
 */
const fakeExponential = (factor, numerator, denominator) => {
    let i = util_1.BIGINT_1;
    let output = util_1.BIGINT_0;
    let numerator_accum = factor * denominator;
    while (numerator_accum > util_1.BIGINT_0) {
        output += numerator_accum;
        numerator_accum = (numerator_accum * numerator) / (denominator * i);
        i++;
    }
    return output / denominator;
};
exports.fakeExponential = fakeExponential;
//# sourceMappingURL=helpers.js.map