"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const MsgBase_js_1 = require("../../MsgBase.js");
/**
 * @category Messages
 */
class MsgUpdateParams extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgUpdateParams(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateParams.create();
        message.authority = params.authority;
        message.params = params.params;
        const messageParams = core_proto_ts_1.InjectivePermissionsV1Beta1Params.Params.create();
        messageParams.wasmHookQueryMaxGas = params.params.wasmHookQueryMaxGas;
        message.params = messageParams;
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateParams.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateParams',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'permissions/MsgUpdateParams',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateParams',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.permissions.v1beta1.MsgUpdateParams',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateParams.encode(this.toProto()).finish();
    }
}
exports.default = MsgUpdateParams;
