"""
IP代理管理器
支持自动轮换IP代理节点
"""

import yaml
import time
import random
import logging
import requests
import subprocess
import threading
from pathlib import Path
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

class IPManager:
    """IP代理管理器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化IP管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.current_proxy = None
        self.proxy_list = []
        self.rotation_thread = None
        self.is_running = False
        self.last_rotation_time = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化代理列表
        self._initialize_proxy_list()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _initialize_proxy_list(self):
        """初始化代理列表"""
        if 'proxies' not in self.config:
            self.logger.error("配置文件中未找到代理列表")
            return
            
        self.proxy_list = []
        preferred_regions = self.config.get('rotation_config', {}).get('preferred_regions', [])
        excluded_nodes = self.config.get('rotation_config', {}).get('excluded_nodes', [])
        
        # 按地区优先级排序
        for region in preferred_regions:
            for proxy in self.config['proxies']:
                if region in proxy['name'] and proxy['name'] not in excluded_nodes:
                    self.proxy_list.append(proxy)
        
        # 添加其他未分类的代理
        for proxy in self.config['proxies']:
            if proxy not in self.proxy_list and proxy['name'] not in excluded_nodes:
                self.proxy_list.append(proxy)
                
        self.logger.info(f"初始化代理列表完成，共 {len(self.proxy_list)} 个节点")
    
    def _test_proxy_connection(self, proxy: Dict[str, Any]) -> bool:
        """
        测试代理连接（简化版本，只检查端口连通性）

        Args:
            proxy: 代理配置

        Returns:
            bool: 连接是否成功
        """
        try:
            import socket

            # 简单的TCP连接测试
            test_timeout = self.config.get('rotation_config', {}).get('test_timeout', 5)

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(test_timeout)

            result = sock.connect_ex((proxy['server'], proxy['port']))
            sock.close()

            if result == 0:
                self.logger.info(f"代理 {proxy['name']} 端口连通性测试成功")
                return True
            else:
                self.logger.warning(f"代理 {proxy['name']} 端口连通性测试失败")
                return False

        except Exception as e:
            self.logger.warning(f"代理 {proxy['name']} 连接测试失败: {e}")
            return False
    
    def _set_system_proxy(self, proxy: Dict[str, Any]) -> bool:
        """
        设置系统代理（记录代理信息，实际需要配合代理客户端使用）

        Args:
            proxy: 代理配置

        Returns:
            bool: 设置是否成功
        """
        try:
            server = proxy['server']
            port = proxy['port']

            # 这里我们只是记录当前使用的代理信息
            # 实际的代理连接需要使用专门的代理客户端（如Clash、V2Ray等）
            self.logger.info(f"当前代理节点: {proxy['name']}")
            self.logger.info(f"代理服务器: {server}:{port}")
            self.logger.info(f"代理类型: {proxy['type']}")

            # 可以在这里添加启动代理客户端的逻辑
            # 例如：启动Clash并使用当前代理配置

            return True

        except Exception as e:
            self.logger.error(f"设置代理信息失败: {e}")
            return False
    
    def _disable_system_proxy(self) -> bool:
        """
        禁用系统代理
        
        Returns:
            bool: 禁用是否成功
        """
        try:
            cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
            subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
            
            self.logger.info("系统代理已禁用")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"禁用系统代理失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"禁用系统代理异常: {e}")
            return False
    
    def switch_proxy(self, proxy: Optional[Dict[str, Any]] = None) -> bool:
        """
        切换代理
        
        Args:
            proxy: 指定的代理配置，如果为None则随机选择
            
        Returns:
            bool: 切换是否成功
        """
        if not proxy:
            if not self.proxy_list:
                self.logger.error("代理列表为空")
                return False
            proxy = random.choice(self.proxy_list)
        
        max_retries = self.config.get('rotation_config', {}).get('max_retries', 3)
        
        for attempt in range(max_retries):
            self.logger.info(f"尝试切换到代理: {proxy['name']} (第 {attempt + 1} 次)")
            
            # 测试代理连接
            if self._test_proxy_connection(proxy):
                # 设置系统代理
                if self._set_system_proxy(proxy):
                    self.current_proxy = proxy
                    self.last_rotation_time = datetime.now()
                    self.logger.info(f"成功切换到代理: {proxy['name']}")
                    return True
            
            # 如果失败，尝试下一个代理
            if attempt < max_retries - 1:
                if self.proxy_list:
                    proxy = random.choice(self.proxy_list)
                time.sleep(2)  # 等待2秒后重试
        
        self.logger.error(f"切换代理失败，已尝试 {max_retries} 次")
        return False
    
    def get_current_ip(self) -> Optional[str]:
        """
        获取当前IP地址
        
        Returns:
            str: 当前IP地址
        """
        try:
            response = requests.get('http://httpbin.org/ip', timeout=10)
            if response.status_code == 200:
                return response.json().get('origin')
        except Exception as e:
            self.logger.error(f"获取当前IP失败: {e}")
        return None
    
    def start_rotation(self):
        """启动自动轮换"""
        if not self.config.get('rotation_config', {}).get('enabled', True):
            self.logger.info("IP轮换功能已禁用")
            return
            
        if self.is_running:
            self.logger.warning("IP轮换已在运行中")
            return
            
        self.is_running = True
        self.rotation_thread = threading.Thread(target=self._rotation_loop, daemon=True)
        self.rotation_thread.start()
        self.logger.info("IP自动轮换已启动")
    
    def stop_rotation(self):
        """停止自动轮换"""
        self.is_running = False
        if self.rotation_thread:
            self.rotation_thread.join(timeout=5)
        self._disable_system_proxy()
        self.logger.info("IP自动轮换已停止")
    
    def _rotation_loop(self):
        """轮换循环"""
        interval = self.config.get('rotation_config', {}).get('interval', 60)
        
        # 首次切换
        self.switch_proxy()
        
        while self.is_running:
            try:
                time.sleep(interval)
                if self.is_running:
                    self.switch_proxy()
            except Exception as e:
                self.logger.error(f"轮换循环异常: {e}")
                time.sleep(10)  # 异常时等待10秒
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取当前状态
        
        Returns:
            dict: 状态信息
        """
        return {
            'is_running': self.is_running,
            'current_proxy': self.current_proxy['name'] if self.current_proxy else None,
            'current_ip': self.get_current_ip(),
            'last_rotation_time': self.last_rotation_time.isoformat() if self.last_rotation_time else None,
            'total_proxies': len(self.proxy_list),
            'rotation_interval': self.config.get('rotation_config', {}).get('interval', 60)
        }
