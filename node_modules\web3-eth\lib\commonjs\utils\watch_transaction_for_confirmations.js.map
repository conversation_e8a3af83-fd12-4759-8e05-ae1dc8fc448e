{"version": 3, "file": "watch_transaction_for_confirmations.js", "sourceRoot": "", "sources": ["../../../src/utils/watch_transaction_for_confirmations.ts"], "names": [], "mappings": ";;AAiCA,4EAoDC;AAnED,2CAAoC;AACpC,mDAAuD;AAEvD,6CAGqB;AAErB,8CAAyD;AACzD,uFAG2C;AAC3C,iGAAwF;AAExF,SAAgB,gCAAgC,CAK/C,WAAyC,EACzC,qBAA2E,EAC3E,kBAAsC,EACtC,eAAsB,EACtB,YAA0B,EAC1B,8BAA2C;IAE3C,IAAI,IAAA,0BAAS,EAAC,kBAAkB,CAAC,IAAI,IAAA,0BAAS,EAAC,kBAAkB,CAAC,SAAS,CAAC;QAC3E,MAAM,IAAI,uDAAyC,CAAC;YACnD,OAAO,EAAE,kBAAkB;YAC3B,SAAS,EAAE,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,SAAS,EAAE,YAAY,CAAC;YACrF,eAAe,EAAE,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,eAAe,EAAE,YAAY,CAAC;SAC7E,CAAC,CAAC;IAEJ,IAAI,CAAC,kBAAkB,CAAC,WAAW;QAClC,MAAM,IAAI,uDAAyC,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAEtF,2EAA2E;IAC3E,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE;QAC1C,aAAa,EAAE,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,YAAY,CAAC;QAC1D,OAAO,EAAE,IAAA,mBAAM,EACd,8BAA8B,aAA9B,8BAA8B,cAA9B,8BAA8B,GAAI,qCAAwB,EAC1D,kBAAkB,EAClB,YAAY,CACZ;QACD,eAAe,EAAE,IAAA,mBAAM,EAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC;KAC/C,CAAC,CAAC;IAE9C,uEAAuE;IACvE,MAAM,QAAQ,GAAqB,WAAW,CAAC,cAAc,CAAC,QAA4B,CAAC;IAC3F,IAAI,QAAQ,IAAI,uBAAuB,IAAI,QAAQ,IAAI,QAAQ,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACzF,IAAA,qEAA8B,EAAC;YAC9B,WAAW;YACX,kBAAkB;YAClB,qBAAqB;YACrB,8BAA8B;YAC9B,YAAY;SACZ,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACP,IAAA,2DAAyB,EAAC;YACzB,WAAW;YACX,kBAAkB;YAClB,qBAAqB;YACrB,8BAA8B;YAC9B,YAAY;SACZ,CAAC,CAAC;IACJ,CAAC;AACF,CAAC"}