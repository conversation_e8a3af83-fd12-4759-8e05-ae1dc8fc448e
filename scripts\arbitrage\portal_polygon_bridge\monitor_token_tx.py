#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import argparse
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import yaml
from web3 import Web3
from web3.middleware import geth_poa_middleware

# ERC20 ABI - 只包含我们需要的函数
ERC20_ABI = [
    {
        "constant": True,
        "inputs": [],
        "name": "name",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    },
    {
        "anonymous": False,
        "inputs": [
            {"indexed": True, "name": "from", "type": "address"},
            {"indexed": True, "name": "to", "type": "address"},
            {"indexed": False, "name": "value", "type": "uint256"}
        ],
        "name": "Transfer",
        "type": "event"
    }
]

def load_config():
    """
    从config.yaml加载配置
    """
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'config', 'config.yaml')
        
        if not os.path.exists(config_path):
            print(f"警告: 找不到配置文件: {config_path}")
            return {}
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        return {}

def get_web3_client(chain: str) -> Web3:
    """
    获取Web3客户端实例，支持RPC轮换
    
    Args:
        chain: 链名称 (ethereum或polygon)
        
    Returns:
        Web3: Web3客户端实例
    """
    config = load_config()
    if not config:
        raise Exception("无法加载配置文件")
    
    # 获取RPC配置
    chain_config = config.get("dex", {}).get(chain.lower(), {})
    if not chain_config:
        raise Exception(f"找不到{chain}的配置信息")
    
    # 获取主RPC和备用RPC
    rpc_url = chain_config.get("rpc_url")
    backup_rpcs = chain_config.get("backup_rpc_urls", [])
    
    if not rpc_url and not backup_rpcs:
        raise Exception(f"找不到{chain}的RPC配置")
    
    # 合并所有RPC到一个列表
    all_rpcs = [rpc_url] if rpc_url else []
    all_rpcs.extend(backup_rpcs)
    
    if not all_rpcs:
        raise Exception(f"没有可用的RPC节点")
    
    return all_rpcs

def create_web3_instance(rpc_url: str, chain: str) -> Web3:
    """
    创建Web3实例
    
    Args:
        rpc_url: RPC URL
        chain: 链名称
        
    Returns:
        Web3: Web3实例
    """
    web3 = Web3(Web3.HTTPProvider(rpc_url))
    if web3.is_connected():
        if chain.lower() == "polygon":
            web3.middleware_onion.inject(geth_poa_middleware, layer=0)
        return web3
    return None

def get_token_info(token_address: str, chain: str) -> Dict[str, Any]:
    """
    使用Web3直接从合约获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 首先尝试从本地配置文件中获取代币信息
    token_info = get_token_from_local_config(token_address, chain)
    if token_info:
        print(f"从本地配置找到{chain}代币信息: {token_info['symbol']}")
        return token_info
    
    try:
        # 获取Web3客户端
        web3 = get_web3_client(chain)
        
        # 创建合约实例
        token_contract = web3.eth.contract(
            address=Web3.to_checksum_address(token_address),
            abi=ERC20_ABI
        )
        
        # 获取代币信息
        try:
            name = token_contract.functions.name().call()
        except:
            name = "Unknown Token"
            
        try:
            symbol = token_contract.functions.symbol().call()
        except:
            symbol = "UNKNOWN"
            
        try:
            decimals = token_contract.functions.decimals().call()
        except:
            decimals = 18
            
        return {
            "name": name,
            "symbol": symbol,
            "decimals": decimals
        }
        
    except Exception as e:
        print(f"获取代币信息失败: {str(e)}")
        return {
            "name": "Unknown Token",
            "symbol": "UNKNOWN",
            "decimals": 18
        }

def get_token_from_local_config(token_address: str, chain: str) -> Dict[str, Any]:
    """
    从本地配置文件中获取代币信息
    
    Args:
        token_address: 代币地址
        chain: 链名称
        
    Returns:
        Dict[str, Any]: 代币信息
    """
    # 尝试从tokens.json加载代币信息
    tokens_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tokens.json")
    
    if not os.path.exists(tokens_file):
        return {}
    
    try:
        with open(tokens_file, 'r', encoding='utf-8') as f:
            tokens_data = json.load(f)
        
        # 获取当前链的chain_id
        chain_id = "1" if chain == "ethereum" else "137" if chain == "polygon" else None
        
        if not chain_id:
            return {}
        
        # 遍历所有代币
        for symbol, token_info in tokens_data.items():
            if chain_id in token_info:
                if token_info[chain_id].get("address", "").lower() == token_address.lower():
                    return {
                        "name": token_info[chain_id].get("name", symbol),
                        "symbol": symbol,
                        "decimals": token_info[chain_id].get("decimals", 18)
                    }
        
        return {}
    
    except Exception as e:
        print(f"从本地配置读取代币信息失败: {str(e)}")
        return {}

def get_token_transactions(token_address: str, chain: str, minutes: int = 10) -> List[Dict[str, Any]]:
    """
    使用Web3直接从区块链获取代币最近的交易，找到一笔目标地址的交易就返回
    从最新区块开始向前扫描
    
    Args:
        token_address: 代币地址
        chain: 链名称 (ethereum或polygon)
        minutes: 过去几分钟的交易
        
    Returns:
        List[Dict[str, Any]]: 交易列表
    """
    try:
        # 获取所有可用的RPC
        all_rpcs = get_web3_client(chain)
        current_rpc_index = 0
        max_retries = 20
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 获取当前RPC
                current_rpc = all_rpcs[current_rpc_index]
                print(f"尝试使用RPC: {current_rpc}")
                
                # 创建Web3实例
                web3 = create_web3_instance(current_rpc, chain)
                if not web3:
                    raise Exception("无法连接到RPC")
                
                # 获取代币信息
                token_info = get_token_info(token_address, chain)
                decimals = token_info["decimals"]
                symbol = token_info["symbol"]
                
                # 创建合约实例
                token_contract = web3.eth.contract(
                    address=Web3.to_checksum_address(token_address),
                    abi=ERC20_ABI
                )
                
                # 计算时间范围
                current_block = web3.eth.block_number
                current_block_data = web3.eth.get_block(current_block)
                current_timestamp = current_block_data['timestamp']
                target_timestamp = current_timestamp - (minutes * 60)  # minutes * 60 seconds
                
                # 估算开始区块（使用更大范围以确保不遗漏）
                blocks_per_minute = 15 if chain == "ethereum" else 30
                estimated_blocks = minutes * blocks_per_minute
                start_block = max(0, current_block - int(estimated_blocks * 1.2))  # 增加20%的余量
                
                print(f"正在获取从区块 {current_block} 到 {start_block} 的交易...")
                
                # 获取Transfer事件的签名
                transfer_event_signature = web3.keccak(text="Transfer(address,address,uint256)").hex()
                
                # 设置目标接收地址
                target_address = "******************************************" if chain.lower() == "ethereum" else "******************************************"
                target_topic = "0x" + "0" * 24 + target_address[2:]
                
                # 增加批量处理的区块数，从新到旧处理
                BATCH_SIZE = 2000
                current_end = current_block
                
                while current_end >= start_block:
                    batch_start = max(start_block, current_end - BATCH_SIZE + 1)
                    print(f"处理区块 {current_end} 到 {batch_start}...")
                    
                    event_filter = {
                        'fromBlock': batch_start,
                        'toBlock': current_end,
                        'address': Web3.to_checksum_address(token_address),
                        'topics': [
                            transfer_event_signature,
                            None,
                            target_topic
                        ]
                    }
                    
                    try:
                        logs = web3.eth.get_logs(event_filter)
                        
                        if logs:
                            # 对日志按区块号从新到旧排序
                            logs.sort(key=lambda x: x['blockNumber'], reverse=True)
                            
                            # 找到第一笔交易就处理并返回
                            for log in logs:
                                try:
                                    # 获取区块时间戳
                                    block = web3.eth.get_block(log['blockNumber'])
                                    timestamp = block['timestamp']
                                    
                                    # 检查时间戳是否在指定范围内
                                    if timestamp < target_timestamp:
                                        print(f"跳过较早的交易，时间戳: {datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')}")
                                        continue
                                    
                                    # 从topics中获取from地址
                                    from_address = '0x' + log['topics'][1].hex()[-40:]
                                    
                                    # 解析value
                                    value = int(log['data'].hex(), 16)
                                    real_value = value / (10 ** decimals)
                                    
                                    # 创建交易记录
                                    transaction = {
                                        "hash": log['transactionHash'].hex(),
                                        "from": from_address.lower(),
                                        "to": target_address.lower(),
                                        "value": real_value,
                                        "token_symbol": symbol,
                                        "timestamp": timestamp,
                                        "datetime": datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                                        "gas_used": web3.eth.get_transaction_receipt(log['transactionHash'])['gasUsed'],
                                        "block_number": log['blockNumber'],
                                        "type": "Transfer",
                                        "chain": chain
                                    }
                                    
                                    print(f"\n找到目标地址接收交易!")
                                    print(f"时间: {transaction['datetime']}")
                                    print(f"数量: {real_value} {symbol}")
                                    print(f"From: {transaction['from']}")
                                    print(f"Hash: {transaction['hash']}")
                                    print(f"区块: {transaction['block_number']}")
                                    
                                    return [transaction]  # 返回只包含这一笔交易的列表
                                    
                                except Exception as e:
                                    print(f"处理日志时出错: {str(e)}")
                                    continue
                    
                    except Exception as e:
                        print(f"获取区块 {batch_start} 到 {current_end} 的日志时出错: {str(e)}")
                        raise  # 抛出异常以触发RPC轮换
                    
                    # 更新结束区块
                    current_end = batch_start - 1
                
                print(f"在指定时间范围内未找到任何转入目标地址的 {symbol} 交易")
                return []
                
            except Exception as e:
                print(f"RPC调用失败: {str(e)}")
                retry_count += 1
                current_rpc_index = (current_rpc_index + 1) % len(all_rpcs)
                print(f"切换到下一个RPC，重试次数: {retry_count}/{max_retries}")
                
                if retry_count >= max_retries:
                    print("达到最大重试次数，退出")
                    break
        
        return []
        
    except Exception as e:
        print(f"获取{chain}代币交易失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return []

def get_dex_contracts(chain: str) -> List[Dict[str, Any]]:
    """
    获取链上已知的DEX合约地址
    
    Args:
        chain: 链名称
        
    Returns:
        List[Dict[str, Any]]: DEX合约信息
    """
    if chain == "polygon":
        return [
            {
                "name": "QuickSwap",
                "addresses": [
                    "0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff",  # QuickSwap Router
                    "0xf5b509bb0909a69b1c207e495f687a596c168e12"   # QuickSwap V3 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "0x1b02da8cb0d097eb8d57a175b88c7d8b47997506"  # SushiSwap Router
                ]
            },
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************"   # Uniswap V3 Router 2
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "******************************************",  # AAVE Lending Pool
                    "******************************************"   # AAVE Pool
                ]
            }
        ]
    elif chain == "ethereum":
        return [
            {
                "name": "Uniswap",
                "addresses": [
                    "******************************************",  # Uniswap V3 Router
                    "******************************************",  # Uniswap V3 Router 2
                    "******************************************"   # Uniswap V2 Router
                ]
            },
            {
                "name": "SushiSwap",
                "addresses": [
                    "******************************************"  # SushiSwap Router
                ]
            },
            {
                "name": "1inch",
                "addresses": [
                    "******************************************"  # 1inch Router
                ]
            },
            {
                "name": "Curve",
                "addresses": [
                    "******************************************",  # Curve.fi Pool
                    "0xbEbc44782C7dB0a1A60Cb6fe97d0b483032FF1C7"   # Curve 3pool
                ]
            },
            {
                "name": "AAVE",
                "addresses": [
                    "0x7d2768de32b0b80b7a3454c06bdac94a69ddc7a9",  # AAVE Lending Pool
                    "0x87870bca3f3fd6335c3f4ce8392d69350b4fa4e2"   # AAVE V3 Pool
                ]
            }
        ]
    else:
        return []

def format_transactions(transactions: List[Dict[str, Any]], verbose: bool = False) -> str:
    """
    格式化交易信息以便显示
    
    Args:
        transactions: 交易列表
        verbose: 是否显示详细信息
        
    Returns:
        str: 格式化后的输出
    """
    if not transactions:
        return "未找到任何交易"
    
    output = []
    output.append(f"找到 {len(transactions)} 笔交易")
    
    # 分类计数
    transfer_count = sum(1 for tx in transactions if tx.get("type") == "Transfer")
    dex_count = sum(1 for tx in transactions if tx.get("type") == "DEX")
    
    output.append(f"普通转账: {transfer_count} 笔, DEX交易: {dex_count} 笔")
    
    # 按类型分组
    dex_transactions = [tx for tx in transactions if tx.get("type") == "DEX"]
    transfer_transactions = [tx for tx in transactions if tx.get("type") == "Transfer"]
    
    # 先显示DEX交易
    if dex_transactions:
        output.append("\nDEX交易:")
        for i, tx in enumerate(dex_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  DEX: {tx.get('dex', 'Unknown')}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    # 然后显示普通转账
    if transfer_transactions:
        output.append("\n普通转账:")
        for i, tx in enumerate(transfer_transactions, 1):
            output.append(f"\n交易 {i}:")
            output.append(f"  哈希: {tx['hash']}")
            output.append(f"  时间: {tx['datetime']}")
            output.append(f"  从: {tx['from']}")
            output.append(f"  到: {tx['to']}")
            output.append(f"  数量: {tx['value']:,.6f} {tx['token_symbol']}")
            
            if verbose:
                output.append(f"  区块号: {tx['block_number']}")
                output.append(f"  Gas消耗: {tx['gas_used']}")
    
    return "\n".join(output)

def monitor_token_transactions(token_address: str, chains: List[str], minutes: int = 10, verbose: bool = False):
    """
    监控特定代币在多个链上的交易
    
    Args:
        token_address: 代币合约地址
        chains: 要监控的链列表
        minutes: 过去几分钟的交易
        verbose: 是否显示详细信息
    """
    all_transactions = []
    
    for chain in chains:
        print(f"\n===== 正在查询 {chain.upper()} 链上最近 {minutes} 分钟的交易 =====")
        
        # 规范化链名称
        chain_lower = chain.lower()
        
        # 获取代币交易
        transactions = get_token_transactions(token_address, chain_lower, minutes)
        
        # 格式化并显示结果
        result = format_transactions(transactions, verbose)
        print(result)
        
        # 添加到所有交易列表
        all_transactions.extend(transactions)
    
    # 显示所有链上交易的汇总
    if len(chains) > 1 and all_transactions:
        print("\n===== 所有链上交易汇总 =====")
        print(f"总计找到 {len(all_transactions)} 笔交易")
        
        # 按链和类型统计
        chain_stats = {}
        for tx in all_transactions:
            chain = tx.get("chain", "unknown")
            tx_type = tx.get("type", "unknown")
            
            if chain not in chain_stats:
                chain_stats[chain] = {"Transfer": 0, "DEX": 0}
            
            chain_stats[chain][tx_type] += 1
        
        # 显示统计
        for chain, stats in chain_stats.items():
            print(f"{chain.upper()}: 普通转账: {stats['Transfer']} 笔, DEX交易: {stats['DEX']} 笔")
        
        # 计算最近的交易
        if all_transactions:
            all_transactions.sort(key=lambda x: x["timestamp"], reverse=True)
            latest_tx = all_transactions[0]
            
            print(f"\n最新交易 ({latest_tx['chain'].upper()}):")
            print(f"  时间: {latest_tx['datetime']}")
            print(f"  类型: {latest_tx['type']}")
            if latest_tx['type'] == "DEX":
                print(f"  DEX: {latest_tx.get('dex', 'Unknown')}")
            print(f"  数量: {latest_tx['value']:,.6f} {latest_tx['token_symbol']}")
            print(f"  哈希: {latest_tx['hash']}")

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="监控特定代币的最近交易")
    parser.add_argument("token_address", help="代币合约地址")
    parser.add_argument("--minutes", type=int, default=10, help="查询过去几分钟的交易 (默认: 10)")
    parser.add_argument("--chains", nargs="+", default=["polygon", "ethereum"], help="要监控的链 (默认: polygon ethereum)")
    parser.add_argument("--verbose", action="store_true", help="显示详细信息")
    
    args = parser.parse_args()
    
    # 监控特定代币在多个链上的交易
    monitor_token_transactions(
        args.token_address,
        args.chains,
        args.minutes,
        args.verbose
    )

if __name__ == "__main__":
    main() 