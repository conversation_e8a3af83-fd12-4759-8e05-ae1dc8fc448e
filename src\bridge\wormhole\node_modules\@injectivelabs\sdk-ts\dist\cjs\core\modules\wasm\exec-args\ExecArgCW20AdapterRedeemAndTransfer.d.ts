import { ExecArgBase, ExecDataRepresentation } from '../ExecArgBase.js';
export declare namespace ExecArgCW20AdapterRedeemAndTransfer {
    interface Params {
        recipient: string;
    }
    interface Data {
        recipient: string;
    }
}
/**
 * @category Contract Exec Arguments
 */
export default class ExecArgCW20AdapterRedeemAndTransfer extends ExecArgBase<ExecArgCW20AdapterRedeemAndTransfer.Params, ExecArgCW20AdapterRedeemAndTransfer.Data> {
    static fromJSON(params: ExecArgCW20AdapterRedeemAndTransfer.Params): ExecArgCW20AdapterRedeemAndTransfer;
    toData(): ExecArgCW20AdapterRedeemAndTransfer.Data;
    toExecData(): ExecDataRepresentation<ExecArgCW20AdapterRedeemAndTransfer.Data>;
}
