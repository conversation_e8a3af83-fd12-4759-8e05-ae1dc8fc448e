"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTestnetOrDevnet = exports.isTestnet = exports.isDevnet = exports.isMainnet = exports.getNetworkInfo = exports.getNetworkChainInfo = exports.getChainInfoForNetwork = exports.getNetworkEndpoints = exports.chainInfos = exports.networkEndpoints = void 0;
const chainInfos_js_1 = require("./chainInfos.js");
const endpoints_js_1 = require("./endpoints.js");
const types_js_1 = require("./types.js");
exports.networkEndpoints = {
    [types_js_1.Network.MainnetLB]: endpoints_js_1.endpointsMainnetLB,
    [types_js_1.Network.MainnetK8s]: endpoints_js_1.endpointsMainnetK8s,
    [types_js_1.Network.MainnetSentry]: endpoints_js_1.endpointsMainnetSentry,
    [types_js_1.Network.MainnetOld]: endpoints_js_1.endpointsMainnetOld,
    [types_js_1.Network.Staging]: endpoints_js_1.endpointsStaging,
    [types_js_1.Network.Mainnet]: endpoints_js_1.endpointsMainnet,
    [types_js_1.Network.Internal]: endpoints_js_1.endpointsInternal,
    [types_js_1.Network.Devnet]: endpoints_js_1.endpointsDevnet,
    [types_js_1.Network.Devnet1]: endpoints_js_1.endpointsDevnet1,
    [types_js_1.Network.Devnet2]: endpoints_js_1.endpointsDevnet2,
    [types_js_1.Network.Devnet3]: endpoints_js_1.endpointsDevnet3,
    [types_js_1.Network.Testnet]: endpoints_js_1.endpointsTestnet,
    [types_js_1.Network.TestnetK8s]: endpoints_js_1.endpointsTestnetK8s,
    [types_js_1.Network.TestnetOld]: endpoints_js_1.endpointsTestnetOld,
    [types_js_1.Network.TestnetSentry]: endpoints_js_1.endpointsTestnetSentry,
    [types_js_1.Network.Local]: endpoints_js_1.endpointsLocal,
};
exports.chainInfos = {
    [types_js_1.Network.MainnetLB]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.MainnetK8s]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.MainnetSentry]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.MainnetOld]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.Staging]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.Mainnet]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.Internal]: chainInfos_js_1.mainnetChainInfo,
    [types_js_1.Network.Devnet]: chainInfos_js_1.devnetChainInfo,
    [types_js_1.Network.Devnet1]: chainInfos_js_1.devnetChainInfo,
    [types_js_1.Network.Devnet2]: chainInfos_js_1.devnetChainInfo,
    [types_js_1.Network.Devnet3]: chainInfos_js_1.devnetChainInfo,
    [types_js_1.Network.Testnet]: chainInfos_js_1.testnetChainInfo,
    [types_js_1.Network.TestnetOld]: chainInfos_js_1.testnetChainInfo,
    [types_js_1.Network.TestnetK8s]: chainInfos_js_1.testnetChainInfo,
    [types_js_1.Network.TestnetSentry]: chainInfos_js_1.testnetChainInfo,
    [types_js_1.Network.Local]: chainInfos_js_1.localChainInfo,
};
const getNetworkEndpoints = (network) => exports.networkEndpoints[network];
exports.getNetworkEndpoints = getNetworkEndpoints;
/**
 * @deprecated - use getNetworkChainInfo instead
 * @param network de
 * @returns
 */
const getChainInfoForNetwork = (network) => exports.chainInfos[network];
exports.getChainInfoForNetwork = getChainInfoForNetwork;
const getNetworkChainInfo = (network) => exports.chainInfos[network];
exports.getNetworkChainInfo = getNetworkChainInfo;
const getNetworkInfo = (network) => ({
    ...exports.chainInfos[network],
    ...exports.networkEndpoints[network],
});
exports.getNetworkInfo = getNetworkInfo;
const isMainnet = (network) => [
    types_js_1.Network.Staging,
    types_js_1.Network.Mainnet,
    types_js_1.Network.MainnetOld,
    types_js_1.Network.MainnetK8s,
    types_js_1.Network.MainnetSentry,
    types_js_1.Network.Internal,
    types_js_1.Network.MainnetLB,
].includes(network);
exports.isMainnet = isMainnet;
const isDevnet = (network) => [types_js_1.Network.Devnet, types_js_1.Network.Devnet1, types_js_1.Network.Devnet2, types_js_1.Network.Local].includes(network);
exports.isDevnet = isDevnet;
const isTestnet = (network) => [
    types_js_1.Network.Testnet,
    types_js_1.Network.TestnetOld,
    types_js_1.Network.TestnetK8s,
    types_js_1.Network.TestnetSentry,
].includes(network);
exports.isTestnet = isTestnet;
const isTestnetOrDevnet = (network) => (0, exports.isDevnet)(network) || (0, exports.isTestnet)(network);
exports.isTestnetOrDevnet = isTestnetOrDevnet;
