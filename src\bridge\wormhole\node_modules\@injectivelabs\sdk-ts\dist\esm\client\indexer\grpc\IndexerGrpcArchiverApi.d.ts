import { InjectiveArchiverRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcArchiverApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveArchiverRpc.InjectiveArchiverRPCClientImpl;
    constructor(endpoint: string);
    fetchHistoricalBalance({ account, resolution, }: {
        account: string;
        resolution: string;
    }): Promise<import("../types/archiver.js").HistoricalBalance>;
    fetchHistoricalRpnl({ account, resolution, }: {
        account: string;
        resolution: string;
    }): Promise<import("../types/archiver.js").HistoricalRPNL>;
    fetchHistoricalVolumes({ account, resolution, }: {
        account: string;
        resolution: string;
    }): Promise<import("../types/archiver.js").HistoricalVolumes>;
    fetchPnlLeaderboard({ startDate, endDate, limit, account, }: {
        startDate: string;
        endDate: string;
        limit?: number;
        account?: string;
    }): Promise<import("../types/archiver.js").PnlLeaderboard>;
    fetchVolLeaderboard({ startDate, endDate, limit, account, }: {
        startDate: string;
        endDate: string;
        limit?: number;
        account?: string;
    }): Promise<import("../types/archiver.js").VolLeaderboard>;
    fetchPnlLeaderboardFixedResolution({ resolution, limit, account, }: {
        resolution: string;
        limit?: number;
        account?: string;
    }): Promise<import("../types/archiver.js").PnlLeaderboard>;
    fetchVolLeaderboardFixedResolution({ resolution, limit, account, }: {
        resolution: string;
        limit?: number;
        account?: string;
    }): Promise<import("../types/archiver.js").VolLeaderboard>;
    fetchDenomHolders({ denom, token, limit, }: {
        denom: string;
        token?: string;
        limit?: number;
    }): Promise<import("../types/archiver.js").DenomHolders>;
}
