import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { HttpRequestException, UnspecifiedErrorCode, } from '@injectivelabs/exceptions';
/**
 * @category Indexer Chronos API
 */
export class IndexerRestLeaderboardChronosApi extends BaseRestConsumer {
    async fetchLeaderboard(resolution) {
        const path = ``;
        try {
            const { data } = await this.retry(() => this.get(path, {
                resolution,
            }));
            return data;
        }
        catch (e) {
            if (e instanceof HttpRequestException) {
                throw e;
            }
            throw new HttpRequestException(new Error(e), {
                code: UnspecifiedErrorCode,
                context: `${this.endpoint}`,
                contextModule: 'Leaderboard',
            });
        }
    }
}
