/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.bank.v1beta1";
function createBaseEventSetBalances() {
    return { balanceUpdates: [] };
}
export const EventSetBalances = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.balanceUpdates) {
            BalanceUpdate.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEventSetBalances();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balanceUpdates.push(BalanceUpdate.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            balanceUpdates: Array.isArray(object?.balanceUpdates)
                ? object.balanceUpdates.map((e) => BalanceUpdate.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.balanceUpdates) {
            obj.balanceUpdates = message.balanceUpdates.map((e) => e ? BalanceUpdate.toJSON(e) : undefined);
        }
        else {
            obj.balanceUpdates = [];
        }
        return obj;
    },
    create(base) {
        return EventSetBalances.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEventSetBalances();
        message.balanceUpdates = object.balanceUpdates?.map((e) => BalanceUpdate.fromPartial(e)) || [];
        return message;
    },
};
function createBaseBalanceUpdate() {
    return { addr: new Uint8Array(), denom: new Uint8Array(), amt: "" };
}
export const BalanceUpdate = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.addr.length !== 0) {
            writer.uint32(10).bytes(message.addr);
        }
        if (message.denom.length !== 0) {
            writer.uint32(18).bytes(message.denom);
        }
        if (message.amt !== "") {
            writer.uint32(26).string(message.amt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBalanceUpdate();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.addr = reader.bytes();
                    break;
                case 2:
                    message.denom = reader.bytes();
                    break;
                case 3:
                    message.amt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            addr: isSet(object.addr) ? bytesFromBase64(object.addr) : new Uint8Array(),
            denom: isSet(object.denom) ? bytesFromBase64(object.denom) : new Uint8Array(),
            amt: isSet(object.amt) ? String(object.amt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.addr !== undefined &&
            (obj.addr = base64FromBytes(message.addr !== undefined ? message.addr : new Uint8Array()));
        message.denom !== undefined &&
            (obj.denom = base64FromBytes(message.denom !== undefined ? message.denom : new Uint8Array()));
        message.amt !== undefined && (obj.amt = message.amt);
        return obj;
    },
    create(base) {
        return BalanceUpdate.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseBalanceUpdate();
        message.addr = object.addr ?? new Uint8Array();
        message.denom = object.denom ?? new Uint8Array();
        message.amt = object.amt ?? "";
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
