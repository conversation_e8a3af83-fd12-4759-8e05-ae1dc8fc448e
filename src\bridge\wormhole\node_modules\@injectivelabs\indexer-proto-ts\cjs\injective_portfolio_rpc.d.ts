import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
export declare const protobufPackage = "injective_portfolio_rpc";
export interface TokenHoldersRequest {
    /** Denom of the token */
    denom: string;
    /** Cursor for pagination */
    cursor: string;
    limit: number;
}
export interface TokenHoldersResponse {
    holders: Holder[];
    /** Next cursors for pagination */
    nextCursors: string[];
}
export interface Holder {
    /** Account address for the holder */
    accountAddress: string;
    /** The balance of the holder */
    balance: string;
}
export interface AccountPortfolioRequest {
    /** Account address */
    accountAddress: string;
}
export interface AccountPortfolioResponse {
    /** The portfolio of this account */
    portfolio: Portfolio | undefined;
}
export interface Portfolio {
    /** The account's portfolio address */
    accountAddress: string;
    /** Account available bank balances */
    bankBalances: Coin[];
    /** Subaccounts list */
    subaccounts: SubaccountBalanceV2[];
    /** All positions for all subaccounts, with unrealized PNL */
    positionsWithUpnl: PositionsWithUPNL[];
}
export interface Coin {
    /** Denom of the coin */
    denom: string;
    amount: string;
    usdValue: string;
}
export interface SubaccountBalanceV2 {
    /** Related subaccount ID */
    subaccountId: string;
    /** Coin denom on the chain. */
    denom: string;
    deposit: SubaccountDeposit | undefined;
}
export interface SubaccountDeposit {
    totalBalance: string;
    availableBalance: string;
    totalBalanceUsd: string;
    availableBalanceUsd: string;
}
export interface PositionsWithUPNL {
    position: DerivativePosition | undefined;
    /** Unrealized PNL */
    unrealizedPnl: string;
}
export interface DerivativePosition {
    /** Ticker of the derivative market */
    ticker: string;
    /** Derivative Market ID */
    marketId: string;
    /** The subaccountId that the position belongs to */
    subaccountId: string;
    /** Direction of the position */
    direction: string;
    /** Quantity of the position */
    quantity: string;
    /** Price of the position */
    entryPrice: string;
    /** Margin of the position */
    margin: string;
    /** LiquidationPrice of the position */
    liquidationPrice: string;
    /** MarkPrice of the position */
    markPrice: string;
    /** Aggregate Quantity of the Reduce Only orders associated with the position */
    aggregateReduceOnlyQuantity: string;
    /** Position updated timestamp in UNIX millis. */
    updatedAt: string;
    /** Position created timestamp in UNIX millis. */
    createdAt: string;
}
export interface AccountPortfolioBalancesRequest {
    /** Account address */
    accountAddress: string;
    /** Whether to return USD values for the balances */
    usd: boolean;
}
export interface AccountPortfolioBalancesResponse {
    /** The portfolio balances of this account */
    portfolio: PortfolioBalances | undefined;
}
export interface PortfolioBalances {
    /** The account's portfolio address */
    accountAddress: string;
    /** Account available bank balances */
    bankBalances: Coin[];
    /** Subaccounts list */
    subaccounts: SubaccountBalanceV2[];
    /** USD value of the portfolio */
    totalUsd: string;
}
export interface StreamAccountPortfolioRequest {
    /** The account's portfolio address */
    accountAddress: string;
    /** Related subaccount ID */
    subaccountId: string;
    type: string;
}
export interface StreamAccountPortfolioResponse {
    /** type of portfolio entry */
    type: string;
    /** denom of portfolio entry */
    denom: string;
    /** amount of portfolio entry */
    amount: string;
    /** subaccount id of portfolio entry */
    subaccountId: string;
    /** Operation timestamp in UNIX millis. */
    timestamp: string;
}
export declare const TokenHoldersRequest: {
    encode(message: TokenHoldersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TokenHoldersRequest;
    fromJSON(object: any): TokenHoldersRequest;
    toJSON(message: TokenHoldersRequest): unknown;
    create(base?: DeepPartial<TokenHoldersRequest>): TokenHoldersRequest;
    fromPartial(object: DeepPartial<TokenHoldersRequest>): TokenHoldersRequest;
};
export declare const TokenHoldersResponse: {
    encode(message: TokenHoldersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TokenHoldersResponse;
    fromJSON(object: any): TokenHoldersResponse;
    toJSON(message: TokenHoldersResponse): unknown;
    create(base?: DeepPartial<TokenHoldersResponse>): TokenHoldersResponse;
    fromPartial(object: DeepPartial<TokenHoldersResponse>): TokenHoldersResponse;
};
export declare const Holder: {
    encode(message: Holder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Holder;
    fromJSON(object: any): Holder;
    toJSON(message: Holder): unknown;
    create(base?: DeepPartial<Holder>): Holder;
    fromPartial(object: DeepPartial<Holder>): Holder;
};
export declare const AccountPortfolioRequest: {
    encode(message: AccountPortfolioRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountPortfolioRequest;
    fromJSON(object: any): AccountPortfolioRequest;
    toJSON(message: AccountPortfolioRequest): unknown;
    create(base?: DeepPartial<AccountPortfolioRequest>): AccountPortfolioRequest;
    fromPartial(object: DeepPartial<AccountPortfolioRequest>): AccountPortfolioRequest;
};
export declare const AccountPortfolioResponse: {
    encode(message: AccountPortfolioResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountPortfolioResponse;
    fromJSON(object: any): AccountPortfolioResponse;
    toJSON(message: AccountPortfolioResponse): unknown;
    create(base?: DeepPartial<AccountPortfolioResponse>): AccountPortfolioResponse;
    fromPartial(object: DeepPartial<AccountPortfolioResponse>): AccountPortfolioResponse;
};
export declare const Portfolio: {
    encode(message: Portfolio, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Portfolio;
    fromJSON(object: any): Portfolio;
    toJSON(message: Portfolio): unknown;
    create(base?: DeepPartial<Portfolio>): Portfolio;
    fromPartial(object: DeepPartial<Portfolio>): Portfolio;
};
export declare const Coin: {
    encode(message: Coin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Coin;
    fromJSON(object: any): Coin;
    toJSON(message: Coin): unknown;
    create(base?: DeepPartial<Coin>): Coin;
    fromPartial(object: DeepPartial<Coin>): Coin;
};
export declare const SubaccountBalanceV2: {
    encode(message: SubaccountBalanceV2, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountBalanceV2;
    fromJSON(object: any): SubaccountBalanceV2;
    toJSON(message: SubaccountBalanceV2): unknown;
    create(base?: DeepPartial<SubaccountBalanceV2>): SubaccountBalanceV2;
    fromPartial(object: DeepPartial<SubaccountBalanceV2>): SubaccountBalanceV2;
};
export declare const SubaccountDeposit: {
    encode(message: SubaccountDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDeposit;
    fromJSON(object: any): SubaccountDeposit;
    toJSON(message: SubaccountDeposit): unknown;
    create(base?: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
    fromPartial(object: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
};
export declare const PositionsWithUPNL: {
    encode(message: PositionsWithUPNL, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PositionsWithUPNL;
    fromJSON(object: any): PositionsWithUPNL;
    toJSON(message: PositionsWithUPNL): unknown;
    create(base?: DeepPartial<PositionsWithUPNL>): PositionsWithUPNL;
    fromPartial(object: DeepPartial<PositionsWithUPNL>): PositionsWithUPNL;
};
export declare const DerivativePosition: {
    encode(message: DerivativePosition, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativePosition;
    fromJSON(object: any): DerivativePosition;
    toJSON(message: DerivativePosition): unknown;
    create(base?: DeepPartial<DerivativePosition>): DerivativePosition;
    fromPartial(object: DeepPartial<DerivativePosition>): DerivativePosition;
};
export declare const AccountPortfolioBalancesRequest: {
    encode(message: AccountPortfolioBalancesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountPortfolioBalancesRequest;
    fromJSON(object: any): AccountPortfolioBalancesRequest;
    toJSON(message: AccountPortfolioBalancesRequest): unknown;
    create(base?: DeepPartial<AccountPortfolioBalancesRequest>): AccountPortfolioBalancesRequest;
    fromPartial(object: DeepPartial<AccountPortfolioBalancesRequest>): AccountPortfolioBalancesRequest;
};
export declare const AccountPortfolioBalancesResponse: {
    encode(message: AccountPortfolioBalancesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountPortfolioBalancesResponse;
    fromJSON(object: any): AccountPortfolioBalancesResponse;
    toJSON(message: AccountPortfolioBalancesResponse): unknown;
    create(base?: DeepPartial<AccountPortfolioBalancesResponse>): AccountPortfolioBalancesResponse;
    fromPartial(object: DeepPartial<AccountPortfolioBalancesResponse>): AccountPortfolioBalancesResponse;
};
export declare const PortfolioBalances: {
    encode(message: PortfolioBalances, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PortfolioBalances;
    fromJSON(object: any): PortfolioBalances;
    toJSON(message: PortfolioBalances): unknown;
    create(base?: DeepPartial<PortfolioBalances>): PortfolioBalances;
    fromPartial(object: DeepPartial<PortfolioBalances>): PortfolioBalances;
};
export declare const StreamAccountPortfolioRequest: {
    encode(message: StreamAccountPortfolioRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamAccountPortfolioRequest;
    fromJSON(object: any): StreamAccountPortfolioRequest;
    toJSON(message: StreamAccountPortfolioRequest): unknown;
    create(base?: DeepPartial<StreamAccountPortfolioRequest>): StreamAccountPortfolioRequest;
    fromPartial(object: DeepPartial<StreamAccountPortfolioRequest>): StreamAccountPortfolioRequest;
};
export declare const StreamAccountPortfolioResponse: {
    encode(message: StreamAccountPortfolioResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamAccountPortfolioResponse;
    fromJSON(object: any): StreamAccountPortfolioResponse;
    toJSON(message: StreamAccountPortfolioResponse): unknown;
    create(base?: DeepPartial<StreamAccountPortfolioResponse>): StreamAccountPortfolioResponse;
    fromPartial(object: DeepPartial<StreamAccountPortfolioResponse>): StreamAccountPortfolioResponse;
};
/** InjectivePortfolioRPC defines gRPC API of Exchange Portfolio provider. */
export interface InjectivePortfolioRPC {
    /** Provide a list of addresses holding a specific token */
    TokenHolders(request: DeepPartial<TokenHoldersRequest>, metadata?: grpc.Metadata): Promise<TokenHoldersResponse>;
    /** Provide the account's portfolio */
    AccountPortfolio(request: DeepPartial<AccountPortfolioRequest>, metadata?: grpc.Metadata): Promise<AccountPortfolioResponse>;
    /** Provide the account's portfolio balances */
    AccountPortfolioBalances(request: DeepPartial<AccountPortfolioBalancesRequest>, metadata?: grpc.Metadata): Promise<AccountPortfolioBalancesResponse>;
    /** Stream the account's portfolio */
    StreamAccountPortfolio(request: DeepPartial<StreamAccountPortfolioRequest>, metadata?: grpc.Metadata): Observable<StreamAccountPortfolioResponse>;
}
export declare class InjectivePortfolioRPCClientImpl implements InjectivePortfolioRPC {
    private readonly rpc;
    constructor(rpc: Rpc);
    TokenHolders(request: DeepPartial<TokenHoldersRequest>, metadata?: grpc.Metadata): Promise<TokenHoldersResponse>;
    AccountPortfolio(request: DeepPartial<AccountPortfolioRequest>, metadata?: grpc.Metadata): Promise<AccountPortfolioResponse>;
    AccountPortfolioBalances(request: DeepPartial<AccountPortfolioBalancesRequest>, metadata?: grpc.Metadata): Promise<AccountPortfolioBalancesResponse>;
    StreamAccountPortfolio(request: DeepPartial<StreamAccountPortfolioRequest>, metadata?: grpc.Metadata): Observable<StreamAccountPortfolioResponse>;
}
export declare const InjectivePortfolioRPCDesc: {
    serviceName: string;
};
export declare const InjectivePortfolioRPCTokenHoldersDesc: UnaryMethodDefinitionish;
export declare const InjectivePortfolioRPCAccountPortfolioDesc: UnaryMethodDefinitionish;
export declare const InjectivePortfolioRPCAccountPortfolioBalancesDesc: UnaryMethodDefinitionish;
export declare const InjectivePortfolioRPCStreamAccountPortfolioDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
