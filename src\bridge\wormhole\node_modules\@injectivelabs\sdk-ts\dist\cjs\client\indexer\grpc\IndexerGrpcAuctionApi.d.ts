import { InjectiveAuctionRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcAuctionApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveAuctionRpc.InjectiveAuctionRPCClientImpl;
    constructor(endpoint: string);
    fetchAuction(round?: number): Promise<{
        auction: import("../types/auction.js").Auction;
        bids: import("../types/auction.js").IndexerBid[];
    }>;
    fetchAuctions(): Promise<import("../types/auction.js").Auction[]>;
    fetchInjBurnt(): Promise<number>;
}
