"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tx_js_1 = require("cosmjs-types/ibc/applications/transfer/v1/tx.js");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 *
 * @deprecated use MsgTransfer with SIGN_DIRECT and a Cosmos wallet
 */
class MsgTransferCosmjs {
    params;
    constructor(params) {
        this.params = params;
    }
    static fromJSON(params) {
        return new MsgTransferCosmjs(params);
    }
    toProto() {
        const { params } = this;
        const token = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        token.denom = params.amount.denom;
        token.amount = params.amount.amount;
        const message = core_proto_ts_1.IbcApplicationsTransferV1Tx.MsgTransfer.create();
        message.sourcePort = params.port;
        message.sourceChannel = params.channelId;
        message.token = token;
        message.sender = params.sender;
        message.receiver = params.receiver;
        if (params.height) {
            const timeoutHeight = core_proto_ts_1.IbcCoreClientV1Client.Height.create();
            timeoutHeight.revisionHeight = params.height.revisionHeight.toString();
            timeoutHeight.revisionNumber = params.height.revisionNumber.toString();
            message.timeoutHeight = timeoutHeight;
        }
        if (params.timeout) {
            message.timeoutTimestamp = params.timeout.toString();
        }
        return tx_js_1.MsgTransfer.fromJSON(message);
    }
    toData() {
        throw new Error('Method not implemented.');
    }
    toAmino() {
        const { params } = this;
        const message = tx_js_1.MsgTransfer.fromPartial({
            sourcePort: params.port,
            sourceChannel: params.channelId,
            sender: params.sender,
            receiver: params.receiver,
            token: params.amount,
            timeoutHeight: params.height
                ? {
                    revisionHeight: BigInt(params.height.revisionHeight),
                    revisionNumber: BigInt(params.height.revisionNumber),
                }
                : undefined,
            timeoutTimestamp: params.timeout ? BigInt(params.timeout) : undefined,
        });
        return {
            type: '/ibc.applications.transfer.v1.MsgTransfer',
            value: {
                ...message,
            },
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/ibc.applications.transfer.v1.MsgTransfer',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/ibc.applications.transfer.v1.MsgTransfer',
            message: proto,
        };
    }
    toBinary() {
        return tx_js_1.MsgTransfer.encode(this.toProto()).finish();
    }
}
exports.default = MsgTransferCosmjs;
