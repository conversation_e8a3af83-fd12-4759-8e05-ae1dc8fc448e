import _m0 from "protobufjs/minimal.js";
import { Metadata } from "../../../cosmos/bank/v1beta1/bank";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
export declare const protobufPackage = "injective.tokenfactory.v1beta1";
export interface EventCreateDenom {
    account: string;
    denom: string;
}
export interface EventMint {
    minter: string;
    amount: Coin | undefined;
    receiver: string;
}
export interface EventBurn {
    burner: string;
    amount: Coin | undefined;
    burnFrom: string;
}
export interface EventChangeAdmin {
    denom: string;
    newAdminAddress: string;
}
export interface EventSetDenomMetadata {
    denom: string;
    metadata: Metadata | undefined;
}
export declare const EventCreateDenom: {
    encode(message: EventCreateDenom, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventCreateDenom;
    fromJSON(object: any): EventCreateDenom;
    toJSON(message: EventCreateDenom): unknown;
    create(base?: DeepPartial<EventCreateDenom>): EventCreateDenom;
    fromPartial(object: DeepPartial<EventCreateDenom>): EventCreateDenom;
};
export declare const EventMint: {
    encode(message: EventMint, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventMint;
    fromJSON(object: any): EventMint;
    toJSON(message: EventMint): unknown;
    create(base?: DeepPartial<EventMint>): EventMint;
    fromPartial(object: DeepPartial<EventMint>): EventMint;
};
export declare const EventBurn: {
    encode(message: EventBurn, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventBurn;
    fromJSON(object: any): EventBurn;
    toJSON(message: EventBurn): unknown;
    create(base?: DeepPartial<EventBurn>): EventBurn;
    fromPartial(object: DeepPartial<EventBurn>): EventBurn;
};
export declare const EventChangeAdmin: {
    encode(message: EventChangeAdmin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventChangeAdmin;
    fromJSON(object: any): EventChangeAdmin;
    toJSON(message: EventChangeAdmin): unknown;
    create(base?: DeepPartial<EventChangeAdmin>): EventChangeAdmin;
    fromPartial(object: DeepPartial<EventChangeAdmin>): EventChangeAdmin;
};
export declare const EventSetDenomMetadata: {
    encode(message: EventSetDenomMetadata, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetDenomMetadata;
    fromJSON(object: any): EventSetDenomMetadata;
    toJSON(message: EventSetDenomMetadata): unknown;
    create(base?: DeepPartial<EventSetDenomMetadata>): EventSetDenomMetadata;
    fromPartial(object: DeepPartial<EventSetDenomMetadata>): EventSetDenomMetadata;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
