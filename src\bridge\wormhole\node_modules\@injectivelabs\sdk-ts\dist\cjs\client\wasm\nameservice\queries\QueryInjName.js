"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryInjName = void 0;
const BaseWasmQuery_js_1 = require("../../BaseWasmQuery.js");
const index_js_1 = require("../../../../utils/index.js");
class QueryInjName extends BaseWasmQuery_js_1.BaseWasmQuery {
    toPayload() {
        return (0, index_js_1.toBase64)({
            name: {
                address: this.params.address,
            },
        });
    }
}
exports.QueryInjName = QueryInjName;
