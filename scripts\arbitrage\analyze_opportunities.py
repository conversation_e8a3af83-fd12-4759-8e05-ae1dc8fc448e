#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
套利机会分析脚本 - 分析CEX和DEX之间的套利机会
"""

import os
import sys
import time
import asyncio
import argparse
import json
from decimal import Decimal
from datetime import datetime
import aiohttp
import concurrent.futures
from functools import partial
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.cex.gate.client import GateClient
from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.logger import logger
from scripts.dex.local_swap_simulator import LocalSwapSimulator

# 套利参数设置
DEFAULT_MAX_USDT = 250  # 默认最大USDT交易金额
MIN_USDT_AMOUNT = 100   # 最小USDT交易金额
MIN_ASTR_AMOUNT = 3000  # 最小ASTR/WASTR交易数量
CEX_FEE_RATE = 0.001    # CEX手续费率 (0.1%)
DEX_GAS_COST = 0.2      # DEX Gas成本 (ASTR)

# 网络配置
NETWORK_CONFIG = {
    'max_retries': 3,
    'retry_delay': 1,
    'timeout': 10,
    'max_concurrent_requests': 8,
    'connection_timeout': 5,
    'read_timeout': 8
}

class NetworkManager:
    """网络请求管理器"""
    _instance = None  # 单例实例
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.session = None
        self.connector = None
        self.timeout = aiohttp.ClientTimeout(
            total=NETWORK_CONFIG['timeout'],
            connect=NETWORK_CONFIG['connection_timeout'],
            sock_read=NETWORK_CONFIG['read_timeout']
        )
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=NETWORK_CONFIG['max_concurrent_requests']
        )
    
    @classmethod
    async def get_instance(cls):
        """获取单例实例"""
        if not cls._instance:
            async with cls._lock:
                if not cls._instance:
                    cls._instance = cls()
                    cls._instance.connector = aiohttp.TCPConnector(
                        limit=NETWORK_CONFIG['max_concurrent_requests'],
                        ttl_dns_cache=300,
                        use_dns_cache=True
                    )
                    cls._instance.session = aiohttp.ClientSession(
                        connector=cls._instance.connector,
                        timeout=cls._instance.timeout
                    )
        return cls._instance
    
    async def close(self):
        """关闭资源"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self.connector:
            await self.connector.close()
        if self.thread_pool:
            self.thread_pool.shutdown(wait=True)

async def run_in_thread(func, *args, **kwargs):
    """在线程池中运行同步函数"""
    network_mgr = await NetworkManager.get_instance()
    loop = asyncio.get_event_loop()
    
    # 创建一个包装函数来处理关键字参数
    def wrapper():
        return func(*args, **kwargs)
    
    return await loop.run_in_executor(network_mgr.thread_pool, wrapper)

async def get_cex_balances(currencies):
    """获取CEX账户余额"""
    try:
        client = GateClient()
        all_balances = client.get_balance()
        
        balances = {
            currency: float(all_balances.get(currency, 0))
            for currency in currencies
        }
        
        logger.info(f"成功获取CEX账户余额: {balances}")
        return balances
    except Exception as e:
        logger.error(f"获取CEX账户余额时出错: {e}")
        return None

async def get_dex_balances(tokens):
    """获取DEX钱包余额"""
    try:
        client = ArthSwapClient()
        balances = {}
        for token in tokens:
            try:
                if token == 'ASTR':
                    balance = client.wallet.get_native_balance()
                else:
                    balance = client.wallet.get_token_balance(token)
                balances[token] = float(balance) if balance is not None else 0
            except Exception as e:
                logger.error(f"获取{token}余额时出错: {e}")
                balances[token] = 0
            
        logger.info(f"成功获取DEX钱包余额: {balances}")
        return balances
    except Exception as e:
        logger.error(f"获取DEX钱包余额时出错: {e}")
        return None

async def get_cex_order_book(symbol="ASTR/USDT"):
    """获取CEX订单簿数据"""
    try:
        client = GateClient()
        order_book = client.get_order_book(symbol, limit=20)
        if order_book:
            logger.info(f"成功获取CEX {symbol} 订单簿数据")
            return order_book
        else:
            logger.error("获取订单簿数据失败")
            return None
    except Exception as e:
        logger.error(f"获取CEX订单簿数据时出错: {e}")
        return None

def calculate_cex_buy_amount(order_book, usdt_amount):
    """计算在CEX使用USDT可以购买的ASTR数量"""
    if not order_book or 'asks' not in order_book or not order_book['asks']:
        return 0
    
    total_astr = 0
    remaining_usdt = usdt_amount
    
    for ask in order_book['asks']:
        price = float(ask[0])
        amount = float(ask[1])
        
        usdt_needed = price * amount
        if usdt_needed <= remaining_usdt:
            total_astr += amount
            remaining_usdt -= usdt_needed
        else:
            partial_amount = remaining_usdt / price
            total_astr += partial_amount
            break
    
    return total_astr

def calculate_cex_sell_amount(order_book, astr_amount):
    """计算在CEX卖出ASTR可以获得的USDT数量"""
    if not order_book or 'bids' not in order_book or not order_book['bids']:
        return 0
    
    total_usdt = 0
    remaining_astr = astr_amount
    
    for bid in order_book['bids']:
        price = float(bid[0])
        amount = float(bid[1])
        
        if amount <= remaining_astr:
            total_usdt += price * amount
            remaining_astr -= amount
        else:
            total_usdt += price * remaining_astr
            break
    
    return total_usdt

async def binary_search_profit_cex_buy_dex_sell(cex_order_book, simulator, usdt_balance, wastr_balance):
    """使用二分搜索寻找CEX买入DEX卖出的最优交易量"""
    try:
        # 计算DEX最大可卖出获得的USDT数量
        max_wastr = min(float(wastr_balance), DEFAULT_MAX_USDT / float(cex_order_book['asks'][0][0]))
        max_usdt_from_dex = float(simulator.simulate_swap_1_to_0(Decimal(str(max_wastr))))
        
        # 使用DEX可获得的USDT和默认最大值中的较小值作为搜索上限
        max_usdt = min(DEFAULT_MAX_USDT, max_usdt_from_dex)
        
        if max_usdt < MIN_USDT_AMOUNT:
            logger.warning(f"可用USDT数量 {max_usdt:.4f} 小于最小交易量 {MIN_USDT_AMOUNT}")
            return {"status": "error", "message": "可用USDT数量不足"}
        
        left = MIN_USDT_AMOUNT
        right = max_usdt
        best_profit = 0
        best_result = None
        
        while left <= right:
            mid = (left + right) / 2
            
            # 计算CEX买入成本和获得的ASTR数量
            astr_amount = calculate_cex_buy_amount(cex_order_book, mid)
            cex_cost = mid * (1 + CEX_FEE_RATE)  # 包含手续费的成本
            
            # 使用本地模拟器计算DEX卖出获得的USDT数量
            wastr_amount = Decimal(str(astr_amount))
            usdt_received = simulator.simulate_swap_1_to_0(wastr_amount)
            
            if usdt_received is None:
                logger.error("DEX交易模拟失败")
                return {"status": "error", "message": "DEX交易模拟失败"}
            
            # 计算利润（考虑Gas成本）
            gas_cost_in_usdt = DEX_GAS_COST * float(cex_order_book['asks'][0][0])
            profit = float(usdt_received) - cex_cost - gas_cost_in_usdt
            
            logger.info(f"测试交易量: {mid:.2f} USDT")
            logger.info(f"CEX买入: {astr_amount:.4f} ASTR，成本: {cex_cost:.4f} USDT")
            logger.info(f"DEX卖出: {float(usdt_received):.4f} USDT，Gas成本: {gas_cost_in_usdt:.4f} USDT")
            logger.info(f"预期利润: {profit:.4f} USDT")
            
            if profit > best_profit:
                best_profit = profit
                best_result = {
                    "status": "success",
                    "amount_usdt": mid,
                    "amount_astr": astr_amount,
                    "profit": profit,
                    "cex_cost": cex_cost,
                    "dex_received": float(usdt_received),
                    "gas_cost": gas_cost_in_usdt
                }
            
            if profit > 0:
                left = mid + 0.1
            else:
                right = mid - 0.1
        
        if best_result:
            return best_result
        else:
            return {"status": "error", "message": "未找到盈利机会"}
            
    except Exception as e:
        logger.error(f"二分查找最优交易量时出错: {e}")
        return {"status": "error", "message": str(e)}

async def binary_search_profit_dex_buy_cex_sell(cex_order_book, simulator, usdt_balance, astr_balance):
    """使用二分搜索寻找DEX买入CEX卖出的最优交易量"""
    try:
        # 计算DEX最大可用USDT
        max_usdt = min(DEFAULT_MAX_USDT, float(usdt_balance))
        
        # 计算使用最大USDT在DEX能买到的WASTR数量
        max_wastr = simulator.simulate_swap_0_to_1(Decimal(str(max_usdt)))
        if max_wastr is None:
            logger.error("DEX交易模拟失败")
            return {"status": "error", "message": "DEX交易模拟失败"}
            
        # 计算在CEX能卖出的最大USDT数量
        max_usdt_from_cex = calculate_cex_sell_amount(cex_order_book, float(max_wastr))
        
        # 使用两者中的较小值作为搜索上限
        max_usdt = min(max_usdt, max_usdt_from_cex)
        
        if max_usdt < MIN_USDT_AMOUNT:
            logger.warning(f"可用USDT数量 {max_usdt:.4f} 小于最小交易量 {MIN_USDT_AMOUNT}")
            return {"status": "error", "message": "可用USDT数量不足"}
        
        left = MIN_USDT_AMOUNT
        right = max_usdt
        best_profit = 0
        best_result = None
        
        while left <= right:
            mid = (left + right) / 2
            
            # 使用本地模拟器计算DEX买入获得的WASTR数量
            usdt_amount = Decimal(str(mid))
            wastr_received = simulator.simulate_swap_0_to_1(usdt_amount)
            
            if wastr_received is None:
                logger.error("DEX交易模拟失败")
                return {"status": "error", "message": "DEX交易模拟失败"}
            
            # 计算CEX卖出获得的USDT数量
            astr_amount = float(wastr_received)
            usdt_received = calculate_cex_sell_amount(cex_order_book, astr_amount)
            cex_received = usdt_received * (1 - CEX_FEE_RATE)  # 扣除手续费
            
            # 计算利润（考虑Gas成本）
            gas_cost_in_usdt = DEX_GAS_COST * float(cex_order_book['asks'][0][0])
            profit = cex_received - mid - gas_cost_in_usdt
            
            logger.info(f"测试交易量: {mid:.2f} USDT")
            logger.info(f"DEX买入: {float(wastr_received):.4f} WASTR")
            logger.info(f"CEX卖出: {cex_received:.4f} USDT，Gas成本: {gas_cost_in_usdt:.4f} USDT")
            logger.info(f"预期利润: {profit:.4f} USDT")
            
            if profit > best_profit:
                best_profit = profit
                best_result = {
                    "status": "success",
                    "amount_usdt": mid,
                    "amount_astr": float(wastr_received),
                    "profit": profit,
                    "cex_received": cex_received,
                    "dex_cost": mid,
                    "gas_cost": gas_cost_in_usdt
                }
            
            if profit > 0:
                left = mid + 0.1
            else:
                right = mid - 0.1
        
        if best_result:
            return best_result
        else:
            return {"status": "error", "message": "未找到盈利机会"}
    except Exception as e:
        logger.error(f"DEX买入CEX卖出二分查找时出错: {e}")
        return {"status": "error", "message": str(e)}

async def analyze_cex_buy_dex_sell(cex_order_book, simulator, usdt_balance, wastr_balance):
    """分析CEX买入DEX卖出的套利机会"""
    try:
        return await binary_search_profit_cex_buy_dex_sell(
            cex_order_book, simulator, usdt_balance, wastr_balance
        )
    except Exception as e:
        logger.error(f"分析CEX买入DEX卖出套利时出错: {e}")
        return {"status": "error", "message": str(e)}

async def analyze_dex_buy_cex_sell(cex_order_book, simulator, usdt_balance, astr_balance):
    """分析DEX买入CEX卖出的套利机会"""
    try:
        return await binary_search_profit_dex_buy_cex_sell(
            cex_order_book, simulator, usdt_balance, astr_balance
        )
    except Exception as e:
        logger.error(f"分析DEX买入CEX卖出套利时出错: {e}")
        return {"status": "error", "message": str(e)}

def init_clients():
    """初始化客户端"""
    global gate_client, dex_client
    
    # 初始化客户端
    gate_client = GateClient()
    dex_client = ArthSwapClient()
    
    return gate_client, dex_client

async def get_prices_and_balances(symbol="ASTR/USDT"):
    """并发获取所有价格和余额数据"""
    try:
        start_time = time.time()
        
        # 预先创建客户端实例
        gate_client = GateClient()
        dex_client = ArthSwapClient()
        simulator = LocalSwapSimulator("USDT", "WASTR")
        
        # 创建线程池
        with concurrent.futures.ThreadPoolExecutor(max_workers=8, thread_name_prefix="arb_worker") as executor:
            loop = asyncio.get_event_loop()
            
            # 定义所有需要执行的任务
            async def get_order_book():
                try:
                    return await loop.run_in_executor(executor, gate_client.get_order_book, symbol, 20)
                except Exception as e:
                    logger.error(f"获取订单簿出错: {e}")
                    return None
                
            async def get_dex_usdt():
                try:
                    return await loop.run_in_executor(executor, dex_client.wallet.get_token_balance, "USDT")
                except Exception as e:
                    logger.error(f"获取DEX USDT余额出错: {e}")
                    return None
                
            async def get_dex_wastr():
                try:
                    return await loop.run_in_executor(executor, dex_client.wallet.get_token_balance, "WASTR")
                except Exception as e:
                    logger.error(f"获取DEX WASTR余额出错: {e}")
                    return None
                
            async def get_cex_balance():
                try:
                    return await loop.run_in_executor(executor, gate_client.get_balance)
                except Exception as e:
                    logger.error(f"获取CEX余额出错: {e}")
                    return None
                
            async def update_dex_pool():
                try:
                    success = await loop.run_in_executor(executor, simulator.update_reserves)
                    if success:
                        # 获取精确价格，使用1 WASTR作为基准量计算价格
                        exact_price = await loop.run_in_executor(
                            executor, 
                            simulator.get_exact_price, 
                            Decimal('1.0'),  # amount_in
                            False  # is_0_to_1 (WASTR->USDT)
                        )
                        return success, exact_price
                    return False, None
                except Exception as e:
                    logger.error(f"更新DEX池子状态出错: {e}")
                    return False, None
            
            # 并发执行所有任务，设置超时
            tasks = [
                asyncio.create_task(get_order_book()),
                asyncio.create_task(get_dex_usdt()),
                asyncio.create_task(get_dex_wastr()),
                asyncio.create_task(get_cex_balance()),
                asyncio.create_task(update_dex_pool())
            ]
            
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=False),
                    timeout=NETWORK_CONFIG['timeout']
                )
            except asyncio.TimeoutError:
                logger.error(f"获取数据超时 (>{NETWORK_CONFIG['timeout']}秒)")
                return None, None, None, None, None
            except Exception as e:
                logger.error(f"并发执行任务出错: {e}")
                return None, None, None, None, None
            
            # 处理结果
            order_book, dex_usdt, dex_wastr, cex_balances, (dex_updated, dex_price) = results
            
            # 检查结果是否有效
            if not order_book:
                logger.error("获取订单簿失败")
                return None, None, None, None, None
                
            if dex_usdt is None or dex_wastr is None:
                logger.error(f"获取DEX余额失败: USDT={dex_usdt}, WASTR={dex_wastr}")
                return None, None, None, None, None
                
            if not cex_balances:
                logger.error("获取CEX余额失败")
                return None, None, None, None, None
                
            if not dex_updated or not dex_price:
                logger.error("获取DEX价格失败")
                return None, None, None, None, None
            
            # 使用精确价格，这个价格已经是WASTR/USDT格式
            dex_price_value = float(dex_price)
            
            # 构建返回数据
            dex_balances = {
                "USDT": float(dex_usdt) if dex_usdt is not None else 0,
                "WASTR": float(dex_wastr) if dex_wastr is not None else 0
            }
            
            cex_balances_formatted = {
                "USDT": float(cex_balances.get("USDT", 0)),
                "ASTR": float(cex_balances.get("ASTR", 0))
            }
            
            # 记录执行时间和结果
            execution_time = time.time() - start_time
            logger.info(f"并发获取数据完成，耗时: {execution_time:.3f}秒")
            logger.info(f"DEX价格: {dex_price_value:.6f} USDT/WASTR")
            logger.info(f"DEX余额: {dex_balances}")
            logger.info(f"CEX余额: {cex_balances_formatted}")
            
            return order_book, dex_price_value, cex_balances_formatted, dex_balances, simulator
            
    except Exception as e:
        logger.error(f"获取价格和余额时出错: {e}")
        return None, None, None, None, None

async def retry_with_timeout(func, *args, **kwargs):
    """带重试和超时的异步函数包装器"""
    for attempt in range(NETWORK_CONFIG['max_retries']):
        try:
            # 如果是协程函数，直接await
            if asyncio.iscoroutinefunction(func):
                return await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=NETWORK_CONFIG['timeout']
                )
            # 如果是同步函数，使用线程池执行
            else:
                return await run_in_thread(func, *args, **kwargs)
        except asyncio.TimeoutError:
            if attempt == NETWORK_CONFIG['max_retries'] - 1:
                raise
            await asyncio.sleep(NETWORK_CONFIG['retry_delay'])
        except Exception as e:
            if attempt == NETWORK_CONFIG['max_retries'] - 1:
                raise
            logger.warning(f"重试 {attempt + 1}/{NETWORK_CONFIG['max_retries']}: {str(e)}")
            await asyncio.sleep(NETWORK_CONFIG['retry_delay'])

async def analyze_opportunities(symbol="ASTR/USDT"):
    """分析套利机会"""
    try:
        # 异步获取所有数据
        cex_order_book, dex_price, cex_balances, dex_balances, simulator = await get_prices_and_balances(symbol)
        
        if not all([cex_order_book, dex_price, cex_balances, dex_balances, simulator]):
            logger.error("无法获取完整数据，无法进行套利分析")
            return
        
        # 计算价差
        cex_price = float(cex_order_book['asks'][0][0])  # CEX卖价
        cex_buy_price = float(cex_order_book['bids'][0][0])  # CEX买价
        
        # 计算价差百分比
        cex_dex_spread = (cex_buy_price - dex_price) / dex_price * 100  # DEX买入CEX卖出
        dex_cex_spread = (dex_price - cex_price) / cex_price * 100  # CEX买入DEX卖出
        
        logger.info(f"CEX卖价: {cex_price:.6f} USDT")
        logger.info(f"CEX买价: {cex_buy_price:.6f} USDT")
        logger.info(f"DEX价格: {dex_price:.6f} USDT")
        logger.info(f"DEX买入CEX卖出价差: {cex_dex_spread:.2f}%")
        logger.info(f"CEX买入DEX卖出价差: {dex_cex_spread:.2f}%")
        
        # 根据价差选择最优路径
        if cex_dex_spread > dex_cex_spread:
            # DEX价格低于CEX，选择DEX买入CEX卖出
            result = await analyze_dex_buy_cex_sell(
                cex_order_book, simulator, dex_balances["USDT"], cex_balances["ASTR"]
            )
            direction = "DEX买入CEX卖出"
            price_diff_percent = cex_dex_spread
        else:
            # CEX价格低于DEX，选择CEX买入DEX卖出
            result = await analyze_cex_buy_dex_sell(
                cex_order_book, simulator, cex_balances["USDT"], dex_balances["WASTR"]
            )
            direction = "CEX买入DEX卖出"
            price_diff_percent = dex_cex_spread
        
        # 输出分析结果
        print("\n===== 套利机会分析结果 =====")
        print(f"交易对: {symbol}")
        print(f"CEX卖价: {cex_price:.6f} USDT")
        print(f"CEX买价: {cex_buy_price:.6f} USDT")
        print(f"DEX价格: {dex_price:.6f} USDT")
        print(f"DEX买入CEX卖出价差: {cex_dex_spread:.2f}%")
        print(f"CEX买入DEX卖出价差: {dex_cex_spread:.2f}%")
        print(f"最优路径: {direction}")
        
        print("\nCEX账户余额:")
        print(f"  USDT: {cex_balances['USDT']:.4f}")
        print(f"  ASTR: {cex_balances['ASTR']:.4f}")
        
        print("\nDEX账户余额:")
        print(f"  USDT: {dex_balances['USDT']:.4f}")
        print(f"  WASTR: {dex_balances['WASTR']:.4f}")
        
        print(f"\n{direction}分析:")
        if result["status"] == "success":
            print(f"  状态: 找到套利机会")
            if "amount_usdt" in result:
                print(f"  投入USDT: {result['amount_usdt']:.4f}")
            if "amount_astr" in result:
                print(f"  交易数量: {result['amount_astr']:.4f} ASTR")
            print(f"  预期利润: {result['profit']:.4f} USDT")
            if "cex_cost" in result:
                print(f"  CEX成本: {result['cex_cost']:.4f} USDT")
            if "dex_received" in result:
                print(f"  DEX获得: {result['dex_received']:.4f} USDT")
            if "gas_cost" in result:
                print(f"  Gas成本: {result['gas_cost']:.4f} USDT")
        else:
            print(f"  状态: {result['message']}")
        
        # 返回分析结果
        return {
            "symbol": symbol,
            "cex_price": cex_price,
            "cex_buy_price": cex_buy_price,
            "dex_price": dex_price,
            "cex_dex_spread": cex_dex_spread,
            "dex_cex_spread": dex_cex_spread,
            "optimal_direction": direction,
            "cex_balances": cex_balances,
            "dex_balances": dex_balances,
            "analysis_result": result
        }
    
    except Exception as e:
        logger.error(f"分析套利机会时出错: {e}")
        return None

async def main():
    """主函数"""
    try:
        parser = argparse.ArgumentParser(description="分析CEX和DEX之间的套利机会")
        parser.add_argument("symbol", nargs="?", default="ASTR/USDT", help="要分析的交易对，默认为ASTR/USDT")
        parser.add_argument("-o", "--output", help="将分析结果保存到文件")
        
        args = parser.parse_args()
        
        # 初始化客户端
        gate_client, dex_client = init_clients()
        
        logger.info(f"开始分析 {args.symbol} 的套利机会")
        result = await analyze_opportunities(args.symbol)
        
        if result and args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            logger.info(f"分析结果已保存到 {args.output}")
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
    finally:
        # 清理资源
        network_mgr = await NetworkManager.get_instance()
        await network_mgr.close()

if __name__ == "__main__":
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main()) 