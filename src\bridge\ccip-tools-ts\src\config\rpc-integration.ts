import { resolve } from 'node:path'
import { readFile } from 'node:fs/promises'
import { parse } from 'yaml'

/**
 * RPC配置整合工具
 * 用于将项目的RPC配置整合到CCIP工具中
 */

// 网络名称到链ID的映射 (支持多种别名)
export const NETWORK_CHAIN_ID_MAP: Record<string, number> = {
  // 主网 - 多种别名支持
  'ethereum': 1,
  'eth': 1,
  'mainnet': 1,
  'ethereum-mainnet': 1,

  'polygon': 137,
  'matic': 137,
  'polygon-mainnet': 137,

  'bsc': 56,
  'bnb': 56,
  'binance': 56,
  'binance_smart_chain-mainnet': 56,

  'avalanche': 43114,
  'avax': 43114,
  'avalanche-mainnet': 43114,

  'arbitrum': 42161,
  'arb': 42161,
  'ethereum-mainnet-arbitrum-1': 42161,

  'optimism': 10,
  'op': 10,
  'ethereum-mainnet-optimism-1': 10,

  'base': 8453,
  'ethereum-mainnet-base-1': 8453,

  'astar': 592,
  'polkadot-mainnet-astar': 592,

  'ronin': 2020,
  'ronin-mainnet': 2020,

  // 测试网 - 多种别名支持
  'sepolia': 11155111,
  'ethereum-testnet-sepolia': 11155111,
  'eth-sepolia': 11155111,

  'polygon-amoy': 80002,
  'amoy': 80002,
  'polygon-testnet-amoy': 80002,
  'matic-amoy': 80002,

  'bsc-testnet': 97,
  'bnb-testnet': 97,
  'binance_smart_chain-testnet': 97,

  'fuji': 43113,
  'avalanche-fuji': 43113,
  'avalanche-testnet-fuji': 43113,
  'avax-fuji': 43113,

  'arbitrum-sepolia': 421614,
  'arb-sepolia': 421614,
  'ethereum-testnet-sepolia-arbitrum-1': 421614,

  'optimism-sepolia': 11155420,
  'op-sepolia': 11155420,
  'ethereum-testnet-sepolia-optimism-1': 11155420,

  'base-sepolia': 84532,
  'ethereum-testnet-sepolia-base-1': 84532,
}

// 链ID到网络名称的反向映射
export const CHAIN_ID_NETWORK_MAP: Record<number, string> = Object.fromEntries(
  Object.entries(NETWORK_CHAIN_ID_MAP).map(([name, id]) => [id, name])
)

interface RpcConfig {
  rpc_url?: string
  endpoint?: string
  backup_rpc_urls?: string[]
  backup_endpoints?: string[]
}

interface YamlConfig {
  rpc?: Record<string, RpcConfig>
}

/**
 * 从YAML配置文件加载RPC配置
 */
export async function loadRpcConfig(yamlPath: string): Promise<Map<number, string[]>> {
  try {
    const yamlContent = await readFile(yamlPath, 'utf8')
    const config: YamlConfig = parse(yamlContent)
    const chainRpcs = new Map<number, string[]>()
    
    if (config.rpc) {
      for (const [networkName, rpcConfig] of Object.entries(config.rpc)) {
        const chainId = NETWORK_CHAIN_ID_MAP[networkName]
        if (!chainId) {
          console.warn(`Unknown network: ${networkName}`)
          continue
        }
        
        const rpcs: string[] = []
        
        // 添加主要RPC端点
        if (rpcConfig.rpc_url) {
          rpcs.push(rpcConfig.rpc_url)
        }
        if (rpcConfig.endpoint) {
          rpcs.push(rpcConfig.endpoint)
        }
        
        // 添加备用RPC端点
        if (rpcConfig.backup_rpc_urls) {
          rpcs.push(...rpcConfig.backup_rpc_urls)
        }
        if (rpcConfig.backup_endpoints) {
          rpcs.push(...rpcConfig.backup_endpoints)
        }
        
        if (rpcs.length > 0) {
          chainRpcs.set(chainId, rpcs)
          console.log(`Loaded ${rpcs.length} RPC endpoints for ${networkName} (Chain ID: ${chainId})`)
        }
      }
    }
    
    console.log(`Total networks configured: ${chainRpcs.size}`)
    return chainRpcs
  } catch (error) {
    console.error(`Failed to load RPC config from ${yamlPath}:`, error)
    return new Map()
  }
}

/**
 * 获取指定链ID的RPC端点列表
 */
export function getRpcEndpointsForChain(chainRpcs: Map<number, string[]>, chainId: number): string[] {
  return chainRpcs.get(chainId) || []
}

/**
 * 获取所有RPC端点的扁平列表
 */
export function getAllRpcEndpoints(chainRpcs: Map<number, string[]>): string[] {
  const allRpcs: string[] = []
  for (const rpcs of chainRpcs.values()) {
    allRpcs.push(...rpcs)
  }
  return allRpcs
}

/**
 * 创建带有RPC轮换功能的Provider工厂
 */
export class RpcRotationManager {
  private chainRpcs: Map<number, string[]>
  private currentRpcIndex: Map<number, number> = new Map()
  private failedRpcs: Set<string> = new Set()
  private retryAfter: Map<string, number> = new Map()
  private readonly retryDelay = 60000 // 1分钟后重试失败的RPC

  constructor(chainRpcs: Map<number, string[]>) {
    this.chainRpcs = chainRpcs
  }

  /**
   * 获取指定链的下一个可用RPC端点
   */
  getNextRpcForChain(chainId: number): string | null {
    const rpcs = this.chainRpcs.get(chainId)
    if (!rpcs || rpcs.length === 0) {
      return null
    }

    const currentIndex = this.currentRpcIndex.get(chainId) || 0
    const now = Date.now()
    
    // 尝试找到一个可用的RPC
    for (let i = 0; i < rpcs.length; i++) {
      const index = (currentIndex + i) % rpcs.length
      const rpc = rpcs[index]
      
      // 检查RPC是否在重试延迟期内
      const retryTime = this.retryAfter.get(rpc)
      if (retryTime && now < retryTime) {
        continue
      }
      
      // 如果RPC之前失败过但已过重试时间，清除失败标记
      if (this.failedRpcs.has(rpc) && (!retryTime || now >= retryTime)) {
        this.failedRpcs.delete(rpc)
        this.retryAfter.delete(rpc)
      }
      
      // 更新当前索引到下一个RPC
      this.currentRpcIndex.set(chainId, (index + 1) % rpcs.length)
      return rpc
    }
    
    return null // 所有RPC都不可用
  }

  /**
   * 标记RPC为失败状态
   */
  markRpcAsFailed(rpc: string): void {
    this.failedRpcs.add(rpc)
    this.retryAfter.set(rpc, Date.now() + this.retryDelay)
    console.warn(`Marked RPC as failed: ${rpc}, will retry after ${this.retryDelay}ms`)
  }

  /**
   * 标记RPC为成功状态
   */
  markRpcAsSuccess(rpc: string): void {
    if (this.failedRpcs.has(rpc)) {
      this.failedRpcs.delete(rpc)
      this.retryAfter.delete(rpc)
      console.log(`RPC recovered: ${rpc}`)
    }
  }

  /**
   * 获取指定链的所有可用RPC端点
   */
  getAvailableRpcsForChain(chainId: number): string[] {
    const rpcs = this.chainRpcs.get(chainId) || []
    const now = Date.now()
    
    return rpcs.filter(rpc => {
      const retryTime = this.retryAfter.get(rpc)
      return !this.failedRpcs.has(rpc) || (retryTime && now >= retryTime)
    })
  }
}

/**
 * 网络名称解析函数
 */
export function resolveNetworkName(networkInput: string): number | null {
  // 如果输入是纯数字，直接返回链ID
  const numericInput = parseInt(networkInput, 10)
  if (!isNaN(numericInput) && numericInput > 0) {
    return numericInput
  }

  // 转换为小写并查找映射
  const normalizedInput = networkInput.toLowerCase().trim()
  const chainId = NETWORK_CHAIN_ID_MAP[normalizedInput]

  return chainId || null
}

/**
 * 获取网络的友好名称
 */
export function getNetworkFriendlyName(chainId: number): string {
  const networkEntry = Object.entries(NETWORK_CHAIN_ID_MAP).find(([, id]) => id === chainId)
  if (networkEntry) {
    // 返回最短的网络名称
    const allNames = Object.entries(NETWORK_CHAIN_ID_MAP)
      .filter(([, id]) => id === chainId)
      .map(([name]) => name)
      .sort((a, b) => a.length - b.length)

    return allNames[0] || `Chain-${chainId}`
  }
  return `Chain-${chainId}`
}

/**
 * 列出所有支持的网络名称
 */
export function listSupportedNetworks(): Record<number, string[]> {
  const networks: Record<number, string[]> = {}

  for (const [name, chainId] of Object.entries(NETWORK_CHAIN_ID_MAP)) {
    if (!networks[chainId]) {
      networks[chainId] = []
    }
    networks[chainId].push(name)
  }

  return networks
}

/**
 * 验证网络名称或链ID
 */
export function validateNetwork(networkInput: string): { valid: boolean; chainId?: number; suggestions?: string[] } {
  const chainId = resolveNetworkName(networkInput)

  if (chainId) {
    return { valid: true, chainId }
  }

  // 提供建议
  const normalizedInput = networkInput.toLowerCase()
  const suggestions = Object.keys(NETWORK_CHAIN_ID_MAP)
    .filter(name => name.toLowerCase().includes(normalizedInput))
    .slice(0, 5)

  return { valid: false, suggestions }
}

/**
 * 默认配置文件路径
 */
export const DEFAULT_RPC_CONFIG_PATH = resolve(process.cwd(), '../../../config/rpc.yaml')

/**
 * 初始化RPC配置管理器
 */
export async function initializeRpcManager(configPath?: string): Promise<RpcRotationManager> {
  const yamlPath = configPath || DEFAULT_RPC_CONFIG_PATH
  const chainRpcs = await loadRpcConfig(yamlPath)
  return new RpcRotationManager(chainRpcs)
}
