#!/usr/bin/env python3
"""
V2Ray IP轮换器 - 真正的网络代理
使用V2Ray实现完整的代理功能，支持顺序轮换所有IP
"""

import yaml
import json
import time
import logging
import subprocess
import tempfile
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

class V2RayRotator:
    """V2Ray IP轮换器"""
    
    def __init__(self, config_path: str = "config/ip.yaml", v2ray_path: str = "tools/v2ray/v2ray.exe"):
        """
        初始化V2Ray轮换器
        
        Args:
            config_path: 配置文件路径
            v2ray_path: V2Ray可执行文件路径
        """
        self.config_path = Path(config_path)
        self.v2ray_path = Path(v2ray_path)
        self.current_proxy_index = 0
        self.current_proxy = None
        self.v2ray_process = None
        self.v2ray_config_file = None
        self.is_running = False
        self.rotation_thread = None
        self.start_time = None
        self.switch_count = 0
        self.proxy_list = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/v2ray_rotator.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 确保日志目录存在
        Path('logs').mkdir(exist_ok=True)
        
        # 加载代理列表
        self._load_proxy_list()
        
    def _check_v2ray_exists(self) -> bool:
        """检查V2Ray是否存在"""
        if not self.v2ray_path.exists():
            print(f"❌ V2Ray可执行文件不存在: {self.v2ray_path}")
            print("请运行以下命令安装V2Ray:")
            print("  .\\install_v2ray.bat")
            return False
        return True
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _load_proxy_list(self):
        """加载代理列表"""
        config = self._load_config()
        self.proxy_list = config.get('proxies', [])
        
        if self.proxy_list:
            print(f"✅ 加载了 {len(self.proxy_list)} 个代理节点")
            
            # 统计地区分布
            regions = {}
            for proxy in self.proxy_list:
                name = proxy.get('name', '')
                for region in ['香港', '台湾', '日本', '新加坡', '美国', '韩国']:
                    if region in name:
                        regions[region] = regions.get(region, 0) + 1
                        break
            
            if regions:
                print("📊 节点分布:")
                for region, count in sorted(regions.items()):
                    print(f"   {region}: {count}个")
        else:
            print("❌ 代理列表为空")
    
    def _generate_v2ray_config(self, proxy: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成V2Ray配置
        
        Args:
            proxy: 代理配置
            
        Returns:
            dict: V2Ray配置
        """
        config = {
            "log": {
                "loglevel": "warning"
            },
            "inbounds": [
                {
                    "tag": "socks",
                    "port": 1080,
                    "listen": "127.0.0.1",
                    "protocol": "socks",
                    "settings": {
                        "auth": "noauth",
                        "udp": True
                    }
                },
                {
                    "tag": "http",
                    "port": 1081,
                    "listen": "127.0.0.1",
                    "protocol": "http"
                }
            ],
            "outbounds": [
                {
                    "tag": "proxy",
                    "protocol": "shadowsocks",
                    "settings": {
                        "servers": [
                            {
                                "address": proxy['server'],
                                "port": proxy['port'],
                                "method": proxy.get('cipher', 'aes-128-gcm'),
                                "password": proxy['password']
                            }
                        ]
                    }
                },
                {
                    "tag": "direct",
                    "protocol": "freedom"
                }
            ],
            "routing": {
                "rules": [
                    {
                        "type": "field",
                        "outboundTag": "proxy",
                        "network": "tcp,udp"
                    }
                ]
            }
        }
        
        return config
    
    def _set_system_proxy(self, enable: bool = True, port: int = 1081) -> bool:
        """
        设置系统代理
        
        Args:
            enable: 是否启用代理
            port: 代理端口
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if enable:
                # 启用系统代理，指向V2Ray的HTTP代理
                cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
                cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:{port}" /f'
                
                subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
                subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
                
                self.logger.info(f"系统代理已启用: 127.0.0.1:{port}")
            else:
                # 禁用系统代理
                cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
                subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
                
                self.logger.info("系统代理已禁用")
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置系统代理失败: {e}")
            return False
    
    def _get_next_proxy(self) -> Optional[Dict[str, Any]]:
        """
        获取下一个代理（顺序轮换）
        
        Returns:
            dict: 下一个代理配置
        """
        if not self.proxy_list:
            return None
        
        proxy = self.proxy_list[self.current_proxy_index]
        
        # 移动到下一个代理
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_list)
        
        return proxy
    
    def _start_v2ray(self, proxy: Dict[str, Any]) -> bool:
        """
        启动V2Ray
        
        Args:
            proxy: 代理配置
            
        Returns:
            bool: 启动是否成功
        """
        if not self._check_v2ray_exists():
            return False
        
        try:
            # 停止现有的V2Ray进程
            self._stop_v2ray()
            
            # 生成V2Ray配置
            v2ray_config = self._generate_v2ray_config(proxy)
            
            # 创建临时配置文件
            self.v2ray_config_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.json', 
                delete=False,
                encoding='utf-8'
            )
            json.dump(v2ray_config, self.v2ray_config_file, indent=2)
            self.v2ray_config_file.close()
            
            # 启动V2Ray (新版本使用 run -config 命令)
            self.v2ray_process = subprocess.Popen(
                [str(self.v2ray_path), "run", "-config", self.v2ray_config_file.name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 等待V2Ray启动
            time.sleep(2)

            if self.v2ray_process.poll() is None:
                self.logger.info(f"V2Ray启动成功，PID: {self.v2ray_process.pid}")
                return True
            else:
                # 获取错误输出
                stdout, stderr = self.v2ray_process.communicate()
                error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "未知错误"
                self.logger.error(f"V2Ray启动失败: {error_msg}")
                print(f"❌ V2Ray启动失败: {error_msg}")

                # 打印生成的配置用于调试
                print(f"🔍 配置文件路径: {self.v2ray_config_file.name}")
                try:
                    with open(self.v2ray_config_file.name, 'r', encoding='utf-8') as f:
                        config_content = f.read()
                        print(f"🔍 V2Ray配置内容:\n{config_content}")
                except Exception as e:
                    print(f"❌ 无法读取配置文件: {e}")

                return False
                
        except Exception as e:
            self.logger.error(f"启动V2Ray失败: {e}")
            return False
    
    def _stop_v2ray(self):
        """停止V2Ray"""
        if self.v2ray_process:
            try:
                self.v2ray_process.terminate()
                self.v2ray_process.wait(timeout=5)
                self.logger.info("V2Ray已停止")
            except subprocess.TimeoutExpired:
                self.v2ray_process.kill()
                self.logger.warning("强制终止V2Ray")
            except Exception as e:
                self.logger.error(f"停止V2Ray失败: {e}")
            finally:
                self.v2ray_process = None
        
        # 清理临时配置文件
        if self.v2ray_config_file:
            try:
                Path(self.v2ray_config_file.name).unlink(missing_ok=True)
            except Exception:
                pass
            self.v2ray_config_file = None
    
    def switch_proxy(self, proxy_name: str = None) -> bool:
        """
        切换代理
        
        Args:
            proxy_name: 指定的代理名称（部分匹配）
            
        Returns:
            bool: 切换是否成功
        """
        if not self.proxy_list:
            print("❌ 代理列表为空")
            return False
        
        # 选择代理
        if proxy_name:
            proxy = None
            for i, p in enumerate(self.proxy_list):
                if proxy_name.lower() in p.get('name', '').lower():
                    proxy = p
                    self.current_proxy_index = i
                    break
            if not proxy:
                print(f"❌ 未找到包含 '{proxy_name}' 的代理")
                return False
        else:
            proxy = self._get_next_proxy()
        
        if not proxy:
            return False
        
        # 记录切换信息
        self.current_proxy = proxy
        self.switch_count += 1
        
        print(f"🔄 [{self.switch_count}] 切换到: {proxy['name']}")
        print(f"   服务器: {proxy['server']}:{proxy['port']}")
        print(f"   类型: {proxy['type']}")
        print(f"   时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"   进度: {self.current_proxy_index}/{len(self.proxy_list)}")
        
        # 启动V2Ray
        if self._start_v2ray(proxy):
            # 设置系统代理指向V2Ray的HTTP代理
            if self._set_system_proxy(True, 1081):
                self.logger.info(f"切换到代理: {proxy['name']}")
                print("✅ V2Ray代理启动成功")
                print("🌐 系统代理已设置: HTTP 127.0.0.1:1081")
                print("🧦 SOCKS5代理可用: 127.0.0.1:1080")
                return True
        
        print("❌ 代理切换失败")
        return False

    def start_rotation(self, interval: int = 60):
        """
        启动自动轮换

        Args:
            interval: 轮换间隔（秒）
        """
        if self.is_running:
            print("⚠️  V2Ray代理轮换已在运行中")
            return

        if not self.proxy_list:
            print("❌ 代理列表为空")
            return

        if not self._check_v2ray_exists():
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.switch_count = 0
        self.current_proxy_index = 0  # 从第一个代理开始

        print(f"🚀 启动V2Ray真实代理轮换")
        print(f"   轮换间隔: {interval}秒")
        print(f"   可用节点: {len(self.proxy_list)}个")
        print(f"   轮换模式: 顺序轮换所有代理")
        print(f"   HTTP代理: 127.0.0.1:1081")
        print(f"   SOCKS5代理: 127.0.0.1:1080")
        print(f"   开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("   按 Ctrl+C 停止轮换")
        print("=" * 50)

        # 首次切换
        self.switch_proxy()

        try:
            while self.is_running:
                time.sleep(interval)
                if self.is_running:
                    self.switch_proxy()
        except KeyboardInterrupt:
            self.stop_rotation()

    def stop_rotation(self):
        """停止自动轮换"""
        if not self.is_running:
            return

        self.is_running = False
        end_time = datetime.now()
        duration = end_time - self.start_time if self.start_time else None

        # 停止V2Ray
        self._stop_v2ray()

        # 禁用系统代理
        self._set_system_proxy(False)

        print("\n" + "=" * 50)
        print("🛑 V2Ray代理轮换已停止")
        if duration:
            print(f"   运行时长: {duration}")
        print(f"   总切换次数: {self.switch_count}")
        print(f"   轮换进度: {self.current_proxy_index}/{len(self.proxy_list)}")
        print(f"   停止时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")

        self.logger.info(f"V2Ray代理轮换停止，总切换次数: {self.switch_count}")

    def list_proxies(self, region: str = None):
        """列出代理"""
        proxies = self.proxy_list

        if region:
            proxies = [p for p in proxies if region in p.get('name', '')]
            print(f"📋 {region}地区代理列表 ({len(proxies)}个):")
        else:
            print(f"📋 所有代理列表 ({len(proxies)}个):")

        print("-" * 80)

        for i, proxy in enumerate(proxies[:20], 1):  # 只显示前20个
            status = "🟢 当前" if proxy == self.current_proxy else "⚪"
            print(f"{i:2d}. {status} {proxy['name']}")
            print(f"     服务器: {proxy['server']}:{proxy['port']}")
            print(f"     类型: {proxy['type']}")
            print()

        if len(proxies) > 20:
            print(f"... 还有 {len(proxies) - 20} 个代理")

    def get_status(self):
        """显示当前状态"""
        print("📊 V2Ray代理轮换器状态")
        print("-" * 30)
        print(f"运行状态: {'🟢 运行中' if self.is_running else '🔴 已停止'}")
        print(f"V2Ray状态: {'🟢 运行中' if self.v2ray_process and self.v2ray_process.poll() is None else '🔴 已停止'}")
        print(f"当前代理: {self.current_proxy['name'] if self.current_proxy else '无'}")
        if self.current_proxy:
            print(f"代理服务器: {self.current_proxy['server']}:{self.current_proxy['port']}")
        print(f"可用节点: {len(self.proxy_list)}个")
        print(f"切换次数: {self.switch_count}")
        print(f"轮换进度: {self.current_proxy_index}/{len(self.proxy_list)}")
        print(f"HTTP代理: 127.0.0.1:1081")
        print(f"SOCKS5代理: 127.0.0.1:1080")

        if self.start_time:
            runtime = datetime.now() - self.start_time
            print(f"运行时长: {runtime}")

def main():
    """主函数"""
    import sys

    if len(sys.argv) < 2:
        print("🔧 V2Ray真实代理轮换器")
        print("=" * 30)
        print("功能特点:")
        print("✅ 真正的网络代理 - 所有网络流量都通过代理")
        print("✅ 自动IP轮换 - 顺序轮换所有代理节点")
        print("✅ 内置V2Ray客户端 - 支持Shadowsocks协议")
        print("✅ 双协议支持 - HTTP和SOCKS5代理")
        print()
        print("使用方法:")
        print("  python v2ray_rotator.py start [间隔秒数]  - 启动自动轮换")
        print("  python v2ray_rotator.py switch [名称]     - 手动切换代理")
        print("  python v2ray_rotator.py list [地区]       - 列出代理")
        print("  python v2ray_rotator.py status           - 显示状态")
        print("  python v2ray_rotator.py stop             - 停止代理")
        print()
        print("示例:")
        print("  python v2ray_rotator.py start 60         - 每60秒切换一次")
        print("  python v2ray_rotator.py switch 香港       - 切换到香港节点")
        print("  python v2ray_rotator.py list 日本         - 列出日本节点")
        print()
        print("注意: 需要先运行 install_v2ray.bat 安装V2Ray")
        return

    command = sys.argv[1].lower()
    rotator = V2RayRotator()

    if command == "start":
        interval = 60
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                print("❌ 间隔时间必须是数字")
                return
        rotator.start_rotation(interval)

    elif command == "switch":
        proxy_name = sys.argv[2] if len(sys.argv) > 2 else None
        if rotator.switch_proxy(proxy_name):
            print("✅ 代理切换成功")
        else:
            print("❌ 代理切换失败")

    elif command == "list":
        region = sys.argv[2] if len(sys.argv) > 2 else None
        rotator.list_proxies(region)

    elif command == "status":
        rotator.get_status()

    elif command == "stop":
        rotator._stop_v2ray()
        rotator._set_system_proxy(False)
        print("✅ V2Ray代理已停止")

    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()
