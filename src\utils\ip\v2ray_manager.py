"""
V2Ray代理管理器
管理V2Ray客户端的启动、停止和配置
"""

import json
import yaml
import time
import logging
import subprocess
import tempfile
import threading
from pathlib import Path
from typing import Dict, Any, Optional

class V2RayManager:
    """V2Ray代理管理器"""
    
    def __init__(self, config_path: str = "config/ip.yaml", v2ray_path: str = "tools/v2ray/v2ray.exe"):
        """
        初始化V2Ray管理器
        
        Args:
            config_path: IP配置文件路径
            v2ray_path: V2Ray可执行文件路径
        """
        self.config_path = Path(config_path)
        self.v2ray_path = Path(v2ray_path)
        self.v2ray_process = None
        self.v2ray_config_file = None
        self.current_proxy = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 检查V2Ray是否存在
        if not self.v2ray_path.exists():
            self.logger.error(f"V2Ray可执行文件不存在: {self.v2ray_path}")
            self.logger.info("请先运行 install_v2ray.bat 安装V2Ray")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _generate_v2ray_config(self, proxy: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成V2Ray配置文件
        
        Args:
            proxy: 代理配置
            
        Returns:
            dict: V2Ray配置
        """
        # 基础配置
        v2ray_config = {
            "log": {
                "loglevel": "warning"
            },
            "inbounds": [
                {
                    "tag": "proxy",
                    "port": 7890,
                    "protocol": "http",
                    "settings": {
                        "allowTransparent": False
                    }
                },
                {
                    "tag": "socks",
                    "port": 7891,
                    "protocol": "socks",
                    "settings": {
                        "auth": "noauth",
                        "udp": True
                    }
                }
            ],
            "outbounds": [],
            "routing": {
                "rules": [
                    {
                        "type": "field",
                        "inboundTag": ["proxy", "socks"],
                        "outboundTag": "proxy-out"
                    }
                ]
            }
        }
        
        # 根据代理类型生成outbound配置
        if proxy['type'] == 'ss':
            # Shadowsocks配置
            outbound = {
                "tag": "proxy-out",
                "protocol": "shadowsocks",
                "settings": {
                    "servers": [
                        {
                            "address": proxy['server'],
                            "port": proxy['port'],
                            "method": proxy['cipher'],
                            "password": proxy['password']
                        }
                    ]
                },
                "streamSettings": {},
                "mux": {
                    "enabled": False
                }
            }
        else:
            # 其他协议可以在这里添加
            self.logger.error(f"不支持的代理类型: {proxy['type']}")
            return None
        
        v2ray_config["outbounds"].append(outbound)
        
        # 添加直连outbound
        v2ray_config["outbounds"].append({
            "tag": "direct",
            "protocol": "freedom",
            "settings": {}
        })
        
        return v2ray_config
    
    def _start_v2ray(self, proxy: Dict[str, Any]) -> bool:
        """
        启动V2Ray
        
        Args:
            proxy: 代理配置
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 停止现有的V2Ray进程
            self._stop_v2ray()
            
            # 检查V2Ray可执行文件
            if not self.v2ray_path.exists():
                self.logger.error(f"V2Ray可执行文件不存在: {self.v2ray_path}")
                return False
            
            # 生成V2Ray配置
            v2ray_config = self._generate_v2ray_config(proxy)
            if not v2ray_config:
                return False
            
            # 创建临时配置文件
            self.v2ray_config_file = tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.json', 
                delete=False,
                encoding='utf-8'
            )
            
            json.dump(v2ray_config, self.v2ray_config_file, indent=2, ensure_ascii=False)
            self.v2ray_config_file.close()
            
            # 启动V2Ray
            v2ray_cmd = [
                str(self.v2ray_path),
                '-config', self.v2ray_config_file.name
            ]
            
            self.v2ray_process = subprocess.Popen(
                v2ray_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
            )
            
            # 等待启动
            time.sleep(3)
            
            # 检查进程是否正常运行
            if self.v2ray_process.poll() is None:
                self.current_proxy = proxy
                self.logger.info(f"V2Ray启动成功，使用节点: {proxy['name']}")
                return True
            else:
                # 读取错误信息
                stdout, stderr = self.v2ray_process.communicate()
                self.logger.error(f"V2Ray启动失败: {stderr.decode('utf-8', errors='ignore')}")
                return False
                
        except Exception as e:
            self.logger.error(f"启动V2Ray失败: {e}")
            return False
    
    def _stop_v2ray(self):
        """停止V2Ray"""
        try:
            if self.v2ray_process:
                self.v2ray_process.terminate()
                try:
                    self.v2ray_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.v2ray_process.kill()
                    self.v2ray_process.wait()
                
                self.v2ray_process = None
                self.logger.info("V2Ray已停止")
            
            # 删除临时配置文件
            if self.v2ray_config_file and Path(self.v2ray_config_file.name).exists():
                Path(self.v2ray_config_file.name).unlink()
                self.v2ray_config_file = None
                
        except Exception as e:
            self.logger.error(f"停止V2Ray失败: {e}")
    
    def _set_system_proxy(self, enable: bool = True):
        """设置系统代理"""
        try:
            if enable:
                # 启用系统代理，指向V2Ray HTTP端口
                cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
                cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f'
                
                subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
                subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
                
                self.logger.info("系统代理已启用: 127.0.0.1:7890")
            else:
                # 禁用系统代理
                cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
                subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
                
                self.logger.info("系统代理已禁用")
                
        except Exception as e:
            self.logger.error(f"设置系统代理失败: {e}")
    
    def switch_proxy(self, proxy: Dict[str, Any]) -> bool:
        """
        切换代理
        
        Args:
            proxy: 代理配置
            
        Returns:
            bool: 切换是否成功
        """
        self.logger.info(f"正在切换到代理: {proxy['name']}")
        
        # 启动V2Ray
        if self._start_v2ray(proxy):
            # 设置系统代理
            self._set_system_proxy(True)
            self.logger.info(f"代理切换成功: {proxy['name']}")
            return True
        else:
            self.logger.error(f"代理切换失败: {proxy['name']}")
            return False
    
    def stop_proxy(self):
        """停止代理"""
        self._stop_v2ray()
        self._set_system_proxy(False)
        self.current_proxy = None
        self.logger.info("代理已停止")
    
    def is_running(self) -> bool:
        """
        检查V2Ray是否运行
        
        Returns:
            bool: 是否运行
        """
        return self.v2ray_process is not None and self.v2ray_process.poll() is None
    
    def get_current_proxy(self) -> Optional[Dict[str, Any]]:
        """
        获取当前代理信息
        
        Returns:
            dict: 当前代理配置
        """
        return self.current_proxy
    
    def test_connection(self) -> bool:
        """
        测试代理连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            import requests
            
            # 通过代理测试连接
            proxies = {
                'http': 'http://127.0.0.1:7890',
                'https': 'http://127.0.0.1:7890'
            }
            
            response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
            if response.status_code == 200:
                ip_info = response.json()
                self.logger.info(f"代理连接正常，当前IP: {ip_info.get('origin', 'Unknown')}")
                return True
            else:
                self.logger.warning(f"代理连接测试失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.warning(f"代理连接测试失败: {e}")
            return False
