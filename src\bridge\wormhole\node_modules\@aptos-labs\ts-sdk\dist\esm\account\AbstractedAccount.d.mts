import { AccountAddress } from '../core/accountAddress.mjs';
import { AbstractPublicKey, AbstractSignature } from '../core/crypto/abstraction.mjs';
import { SigningScheme, HexInput } from '../types/types.mjs';
import { A as Account, b as Ed25519Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { AccountAuthenticatorAbstraction } from '../transactions/authenticator/account.mjs';
import { AnyRawTransaction } from '../transactions/types.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../api/aptosConfig.mjs';
import '../utils/apiEndpoints.mjs';
import '../utils/const.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';

type AbstractedAccountConstructorArgs = {
    /**
     * The account address of the account.
     */
    accountAddress: AccountAddress;
    /**
     * The signer function signs transactions and returns the `authenticator` bytes in the `AbstractionAuthData`.
     *
     * @param digest - The SHA256 hash of the transaction signing message
     * @returns The `authenticator` bytes that can be used to verify the signature.
     */
    signer: (digest: HexInput) => Uint8Array;
    /**
     * The authentication function that will be used to verify the signature.
     *
     * @example
     * ```ts
     * const authenticationFunction = `${accountAddress}::permissioned_delegation::authenticate`;
     * ```
     */
    authenticationFunction: string;
};
declare class AbstractedAccount extends Account {
    readonly publicKey: AbstractPublicKey;
    readonly accountAddress: AccountAddress;
    readonly authenticationFunction: string;
    readonly signingScheme = SigningScheme.SingleKey;
    constructor({ signer, accountAddress, authenticationFunction }: AbstractedAccountConstructorArgs);
    /**
     * Creates an `AbstractedAccount` from an `Ed25519Account` that has a permissioned signer function and
     * using the `0x1::permissioned_delegation::authenticate` function to verify the signature.
     *
     * @param signer - The `Ed25519Account` that can be used to sign permissioned transactions.
     * @returns The `AbstractedAccount`
     */
    static fromPermissionedSigner({ signer, accountAddress, }: {
        signer: Ed25519Account;
        accountAddress?: AccountAddress;
    }): AbstractedAccount;
    signWithAuthenticator(message: HexInput): AccountAuthenticatorAbstraction;
    signTransactionWithAuthenticator(transaction: AnyRawTransaction): AccountAuthenticatorAbstraction;
    sign: (message: HexInput) => AbstractSignature;
    signTransaction(transaction: AnyRawTransaction): AbstractSignature;
    /**
     * Update the signer function for the account. This can be done after asynchronous operations are complete
     * to update the context of the signer function.
     *
     * @param signer - The new signer function to use for the account.
     */
    setSigner(signer: (digest: HexInput) => HexInput): void;
}

export { AbstractedAccount };
