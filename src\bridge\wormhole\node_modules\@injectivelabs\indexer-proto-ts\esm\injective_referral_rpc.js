/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "injective_referral_rpc";
function createBaseGetReferrerDetailsRequest() {
    return { referrerAddress: "" };
}
export const GetReferrerDetailsRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.referrerAddress !== "") {
            writer.uint32(10).string(message.referrerAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerDetailsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrerAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referrerAddress: isSet(object.referrerAddress) ? String(object.referrerAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referrerAddress !== undefined && (obj.referrerAddress = message.referrerAddress);
        return obj;
    },
    create(base) {
        return GetReferrerDetailsRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetReferrerDetailsRequest();
        message.referrerAddress = object.referrerAddress ?? "";
        return message;
    },
};
function createBaseGetReferrerDetailsResponse() {
    return { invitees: [], totalCommission: "", totalTradingVolume: "", referrerCode: "" };
}
export const GetReferrerDetailsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        for (const v of message.invitees) {
            ReferralInvitee.encode(v, writer.uint32(10).fork()).ldelim();
        }
        if (message.totalCommission !== "") {
            writer.uint32(18).string(message.totalCommission);
        }
        if (message.totalTradingVolume !== "") {
            writer.uint32(26).string(message.totalTradingVolume);
        }
        if (message.referrerCode !== "") {
            writer.uint32(34).string(message.referrerCode);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerDetailsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.invitees.push(ReferralInvitee.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.totalCommission = reader.string();
                    break;
                case 3:
                    message.totalTradingVolume = reader.string();
                    break;
                case 4:
                    message.referrerCode = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            invitees: Array.isArray(object?.invitees) ? object.invitees.map((e) => ReferralInvitee.fromJSON(e)) : [],
            totalCommission: isSet(object.totalCommission) ? String(object.totalCommission) : "",
            totalTradingVolume: isSet(object.totalTradingVolume) ? String(object.totalTradingVolume) : "",
            referrerCode: isSet(object.referrerCode) ? String(object.referrerCode) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.invitees) {
            obj.invitees = message.invitees.map((e) => e ? ReferralInvitee.toJSON(e) : undefined);
        }
        else {
            obj.invitees = [];
        }
        message.totalCommission !== undefined && (obj.totalCommission = message.totalCommission);
        message.totalTradingVolume !== undefined && (obj.totalTradingVolume = message.totalTradingVolume);
        message.referrerCode !== undefined && (obj.referrerCode = message.referrerCode);
        return obj;
    },
    create(base) {
        return GetReferrerDetailsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetReferrerDetailsResponse();
        message.invitees = object.invitees?.map((e) => ReferralInvitee.fromPartial(e)) || [];
        message.totalCommission = object.totalCommission ?? "";
        message.totalTradingVolume = object.totalTradingVolume ?? "";
        message.referrerCode = object.referrerCode ?? "";
        return message;
    },
};
function createBaseReferralInvitee() {
    return { address: "", commission: "", tradingVolume: "", joinDate: "" };
}
export const ReferralInvitee = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.commission !== "") {
            writer.uint32(18).string(message.commission);
        }
        if (message.tradingVolume !== "") {
            writer.uint32(26).string(message.tradingVolume);
        }
        if (message.joinDate !== "") {
            writer.uint32(34).string(message.joinDate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseReferralInvitee();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.commission = reader.string();
                    break;
                case 3:
                    message.tradingVolume = reader.string();
                    break;
                case 4:
                    message.joinDate = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            commission: isSet(object.commission) ? String(object.commission) : "",
            tradingVolume: isSet(object.tradingVolume) ? String(object.tradingVolume) : "",
            joinDate: isSet(object.joinDate) ? String(object.joinDate) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.commission !== undefined && (obj.commission = message.commission);
        message.tradingVolume !== undefined && (obj.tradingVolume = message.tradingVolume);
        message.joinDate !== undefined && (obj.joinDate = message.joinDate);
        return obj;
    },
    create(base) {
        return ReferralInvitee.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseReferralInvitee();
        message.address = object.address ?? "";
        message.commission = object.commission ?? "";
        message.tradingVolume = object.tradingVolume ?? "";
        message.joinDate = object.joinDate ?? "";
        return message;
    },
};
function createBaseGetInviteeDetailsRequest() {
    return { inviteeAddress: "" };
}
export const GetInviteeDetailsRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.inviteeAddress !== "") {
            writer.uint32(10).string(message.inviteeAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetInviteeDetailsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.inviteeAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { inviteeAddress: isSet(object.inviteeAddress) ? String(object.inviteeAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.inviteeAddress !== undefined && (obj.inviteeAddress = message.inviteeAddress);
        return obj;
    },
    create(base) {
        return GetInviteeDetailsRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetInviteeDetailsRequest();
        message.inviteeAddress = object.inviteeAddress ?? "";
        return message;
    },
};
function createBaseGetInviteeDetailsResponse() {
    return { referrer: "", usedCode: "", tradingVolume: "", joinedAt: "", active: false };
}
export const GetInviteeDetailsResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.referrer !== "") {
            writer.uint32(10).string(message.referrer);
        }
        if (message.usedCode !== "") {
            writer.uint32(18).string(message.usedCode);
        }
        if (message.tradingVolume !== "") {
            writer.uint32(26).string(message.tradingVolume);
        }
        if (message.joinedAt !== "") {
            writer.uint32(34).string(message.joinedAt);
        }
        if (message.active === true) {
            writer.uint32(40).bool(message.active);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetInviteeDetailsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrer = reader.string();
                    break;
                case 2:
                    message.usedCode = reader.string();
                    break;
                case 3:
                    message.tradingVolume = reader.string();
                    break;
                case 4:
                    message.joinedAt = reader.string();
                    break;
                case 5:
                    message.active = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            referrer: isSet(object.referrer) ? String(object.referrer) : "",
            usedCode: isSet(object.usedCode) ? String(object.usedCode) : "",
            tradingVolume: isSet(object.tradingVolume) ? String(object.tradingVolume) : "",
            joinedAt: isSet(object.joinedAt) ? String(object.joinedAt) : "",
            active: isSet(object.active) ? Boolean(object.active) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.referrer !== undefined && (obj.referrer = message.referrer);
        message.usedCode !== undefined && (obj.usedCode = message.usedCode);
        message.tradingVolume !== undefined && (obj.tradingVolume = message.tradingVolume);
        message.joinedAt !== undefined && (obj.joinedAt = message.joinedAt);
        message.active !== undefined && (obj.active = message.active);
        return obj;
    },
    create(base) {
        return GetInviteeDetailsResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetInviteeDetailsResponse();
        message.referrer = object.referrer ?? "";
        message.usedCode = object.usedCode ?? "";
        message.tradingVolume = object.tradingVolume ?? "";
        message.joinedAt = object.joinedAt ?? "";
        message.active = object.active ?? false;
        return message;
    },
};
function createBaseGetReferrerByCodeRequest() {
    return { referralCode: "" };
}
export const GetReferrerByCodeRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.referralCode !== "") {
            writer.uint32(10).string(message.referralCode);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerByCodeRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referralCode = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referralCode: isSet(object.referralCode) ? String(object.referralCode) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referralCode !== undefined && (obj.referralCode = message.referralCode);
        return obj;
    },
    create(base) {
        return GetReferrerByCodeRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetReferrerByCodeRequest();
        message.referralCode = object.referralCode ?? "";
        return message;
    },
};
function createBaseGetReferrerByCodeResponse() {
    return { referrerAddress: "" };
}
export const GetReferrerByCodeResponse = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.referrerAddress !== "") {
            writer.uint32(10).string(message.referrerAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseGetReferrerByCodeResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.referrerAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { referrerAddress: isSet(object.referrerAddress) ? String(object.referrerAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.referrerAddress !== undefined && (obj.referrerAddress = message.referrerAddress);
        return obj;
    },
    create(base) {
        return GetReferrerByCodeResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseGetReferrerByCodeResponse();
        message.referrerAddress = object.referrerAddress ?? "";
        return message;
    },
};
export class InjectiveReferralRPCClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.GetReferrerDetails = this.GetReferrerDetails.bind(this);
        this.GetInviteeDetails = this.GetInviteeDetails.bind(this);
        this.GetReferrerByCode = this.GetReferrerByCode.bind(this);
    }
    GetReferrerDetails(request, metadata) {
        return this.rpc.unary(InjectiveReferralRPCGetReferrerDetailsDesc, GetReferrerDetailsRequest.fromPartial(request), metadata);
    }
    GetInviteeDetails(request, metadata) {
        return this.rpc.unary(InjectiveReferralRPCGetInviteeDetailsDesc, GetInviteeDetailsRequest.fromPartial(request), metadata);
    }
    GetReferrerByCode(request, metadata) {
        return this.rpc.unary(InjectiveReferralRPCGetReferrerByCodeDesc, GetReferrerByCodeRequest.fromPartial(request), metadata);
    }
}
export const InjectiveReferralRPCDesc = { serviceName: "injective_referral_rpc.InjectiveReferralRPC" };
export const InjectiveReferralRPCGetReferrerDetailsDesc = {
    methodName: "GetReferrerDetails",
    service: InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetReferrerDetailsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetReferrerDetailsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveReferralRPCGetInviteeDetailsDesc = {
    methodName: "GetInviteeDetails",
    service: InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetInviteeDetailsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetInviteeDetailsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const InjectiveReferralRPCGetReferrerByCodeDesc = {
    methodName: "GetReferrerByCode",
    service: InjectiveReferralRPCDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return GetReferrerByCodeRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = GetReferrerByCodeResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
