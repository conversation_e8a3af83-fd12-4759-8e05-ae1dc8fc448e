"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgClaimVoucher extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgClaimVoucher(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgClaimVoucher.create();
        message.sender = params.sender;
        message.denom = params.denom;
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgClaimVoucher.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.permissions.v1beta1.MsgClaimVoucher',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'permissions/MsgClaimVoucher',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.permissions.v1beta1.MsgClaimVoucher',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.permissions.v1beta1.MsgClaimVoucher',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgClaimVoucher.encode(this.toProto()).finish();
    }
}
exports.default = MsgClaimVoucher;
