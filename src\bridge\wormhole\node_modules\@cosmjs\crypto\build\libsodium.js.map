{"version": 3, "file": "libsodium.js", "sourceRoot": "", "sources": ["../src/libsodium.ts"], "names": [], "mappings": ";AAAA,yEAAyE;AACzE,wEAAwE;AACxE,EAAE;AACF,0FAA0F;;;;;;AAE1F,yCAAgD;AAChD,qEAAqE;AACrE,kEAAkE;AAClE,sDAAsD;AACtD,sFAA6C;AAqB7C,SAAgB,iBAAiB,CAAC,KAAc;IAC9C,IAAI,CAAC,IAAA,uBAAe,EAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAC1C,IAAI,OAAQ,KAAyB,CAAC,YAAY,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC9E,IAAI,OAAQ,KAAyB,CAAC,QAAQ,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC1E,IAAI,OAAQ,KAAyB,CAAC,WAAW,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC;AAND,8CAMC;AAED,MAAa,QAAQ;IACZ,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,QAAgB,EAChB,IAAgB,EAChB,OAAwB;QAExB,MAAM,iCAAM,CAAC,KAAK,CAAC;QACnB,OAAO,iCAAM,CAAC,aAAa,CACzB,OAAO,CAAC,YAAY,EACpB,QAAQ,EACR,IAAI,EAAE,mFAAmF;QACzF,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,WAAW,GAAG,IAAI,EAC1B,iCAAM,CAAC,4BAA4B,CACpC,CAAC;IACJ,CAAC;CACF;AAhBD,4BAgBC;AAED,MAAa,cAAc;IACzB,4EAA4E;IACrE,MAAM,CAAC,oBAAoB,CAAC,gBAA4B;QAC7D,IAAI,gBAAgB,CAAC,MAAM,KAAK,EAAE,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,yBAAyB,gBAAgB,CAAC,MAAM,eAAe,CAAC,CAAC;SAClF;QACD,OAAO,IAAI,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3F,CAAC;IAKD,YAAmB,OAAmB,EAAE,MAAkB;QACxD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAEM,kBAAkB;QACvB,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF;AApBD,wCAoBC;AAED,MAAa,OAAO;IAClB;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAgB;QAC9C,MAAM,iCAAM,CAAC,KAAK,CAAC;QACnB,MAAM,OAAO,GAAG,iCAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACtD,OAAO,cAAc,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,OAAmB,EAAE,OAAuB;QAC9E,MAAM,iCAAM,CAAC,KAAK,CAAC;QACnB,OAAO,iCAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAC5E,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,eAAe,CACjC,SAAqB,EACrB,OAAmB,EACnB,MAAkB;QAElB,MAAM,iCAAM,CAAC,KAAK,CAAC;QACnB,OAAO,iCAAM,CAAC,2BAA2B,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACxE,CAAC;CACF;AA5BD,0BA4BC;AAED;;;;GAIG;AACU,QAAA,oBAAoB,GAAG,EAAE,CAAC;AAEvC,MAAa,qBAAqB;IACzB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAmB,EAAE,GAAe,EAAE,KAAiB;QACjF,MAAM,iCAAM,CAAC,KAAK,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC;QAE5B,OAAO,iCAAM,CAAC,0CAA0C,CACtD,OAAO,EACP,cAAc,EACd,IAAI,EAAE,8JAA8J;QACpK,KAAK,EACL,GAAG,CACJ,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,UAAsB,EACtB,GAAe,EACf,KAAiB;QAEjB,MAAM,iCAAM,CAAC,KAAK,CAAC;QAEnB,MAAM,cAAc,GAAG,IAAI,CAAC;QAE5B,OAAO,iCAAM,CAAC,0CAA0C,CACtD,IAAI,EAAE,8JAA8J;QACpK,UAAU,EACV,cAAc,EACd,KAAK,EACL,GAAG,CACJ,CAAC;IACJ,CAAC;CACF;AAhCD,sDAgCC"}