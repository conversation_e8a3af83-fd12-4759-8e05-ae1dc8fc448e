var s={mainnet:"https://api.mainnet.aptoslabs.com/v1/graphql",testnet:"https://api.testnet.aptoslabs.com/v1/graphql",devnet:"https://api.devnet.aptoslabs.com/v1/graphql",local:"http://127.0.0.1:8090/v1/graphql"},o={mainnet:"https://api.mainnet.aptoslabs.com/v1",testnet:"https://api.testnet.aptoslabs.com/v1",devnet:"https://api.devnet.aptoslabs.com/v1",local:"http://127.0.0.1:8080/v1"},p={devnet:"https://faucet.devnet.aptoslabs.com",local:"http://127.0.0.1:8081"},a={mainnet:"https://api.mainnet.aptoslabs.com/keyless/pepper/v0",testnet:"https://api.testnet.aptoslabs.com/keyless/pepper/v0",devnet:"https://api.devnet.aptoslabs.com/keyless/pepper/v0",local:"https://api.devnet.aptoslabs.com/keyless/pepper/v0"},n={mainnet:"https://api.mainnet.aptoslabs.com/keyless/prover/v0",testnet:"https://api.testnet.aptoslabs.com/keyless/prover/v0",devnet:"https://api.devnet.aptoslabs.com/keyless/prover/v0",local:"https://api.devnet.aptoslabs.com/keyless/prover/v0"},e=(t=>(t.MAINNET="mainnet",t.TESTNET="testnet",t.DEVNET="devnet",t.LOCAL="local",t.CUSTOM="custom",t))(e||{}),r={mainnet:1,testnet:2,local:4},l={mainnet:"mainnet",testnet:"testnet",devnet:"devnet",local:"local",custom:"custom"};export{s as a,o as b,p as c,a as d,n as e,e as f,r as g,l as h};
//# sourceMappingURL=chunk-4RXKALLC.mjs.map