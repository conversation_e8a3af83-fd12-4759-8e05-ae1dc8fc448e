import { InjectiveExchangeV1Beta1Exchange, InjectiveExchangeV1Beta1Tx, } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
import snakecaseKeys from 'snakecase-keys';
import { numberToCosmosSdkDecString, amountToCosmosSdkDecAmount, } from '../../../../utils/numbers.js';
const createMessage = (params) => {
    const message = InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.create();
    message.sender = params.injectiveAddress;
    message.subaccountId = params.subaccountId;
    message.marketId = params.marketId;
    if (params.order) {
        const orderInfo = InjectiveExchangeV1Beta1Exchange.OrderInfo.create();
        orderInfo.subaccountId = params.order.subaccountId;
        orderInfo.feeRecipient = params.order.feeRecipient;
        orderInfo.price = params.order.price;
        orderInfo.quantity = params.order.quantity;
        if (params.order.cid) {
            orderInfo.cid = params.order.cid;
        }
        const order = InjectiveExchangeV1Beta1Exchange.DerivativeOrder.create();
        order.marketId = params.order.marketId;
        order.margin = params.order.margin;
        order.orderInfo = orderInfo;
        order.orderType = params.order.orderType;
        order.triggerPrice = params.order.triggerPrice || '0';
        message.order = order;
    }
    return InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.fromPartial(message);
};
/**
 * @category Messages
 */
export default class MsgLiquidatePosition extends MsgBase {
    static fromJSON(params) {
        return new MsgLiquidatePosition(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            order: initialParams.order
                ? {
                    ...initialParams.order,
                    price: amountToCosmosSdkDecAmount(initialParams.order.price).toFixed(),
                    margin: amountToCosmosSdkDecAmount(initialParams.order.margin).toFixed(),
                    triggerPrice: amountToCosmosSdkDecAmount(initialParams.order.triggerPrice || 0).toFixed(),
                    quantity: amountToCosmosSdkDecAmount(initialParams.order.quantity).toFixed(),
                }
                : undefined,
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgLiquidatePosition',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const order = createMessage(params);
        const message = {
            ...snakecaseKeys(order),
        };
        return {
            type: 'exchange/MsgLiquidatePosition',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgLiquidatePosition',
            ...value,
        };
    }
    toEip712V2() {
        const web3gw = this.toWeb3Gw();
        const order = web3gw.order;
        const messageAdjusted = {
            ...web3gw,
            order: order
                ? {
                    ...order,
                    order_info: {
                        ...order?.order_info,
                        price: numberToCosmosSdkDecString(order.order_info.price),
                        quantity: numberToCosmosSdkDecString(order.order_info.quantity),
                    },
                    margin: numberToCosmosSdkDecString(order.margin),
                    trigger_price: numberToCosmosSdkDecString(order.trigger_price || '0'),
                    order_type: InjectiveExchangeV1Beta1Exchange.orderTypeToJSON(order.order_type),
                }
                : undefined,
        };
        return messageAdjusted;
    }
    toEip712() {
        const { params } = this;
        const amino = this.toAmino();
        const { value, type } = amino;
        const order = value.order;
        const messageAdjusted = {
            ...value,
            order: params.order
                ? {
                    ...order,
                    order_info: {
                        ...order?.order_info,
                        price: amountToCosmosSdkDecAmount(order.order_info.price).toFixed(),
                        quantity: amountToCosmosSdkDecAmount(order.order_info.quantity).toFixed(),
                    },
                    margin: amountToCosmosSdkDecAmount(order.margin).toFixed(),
                    trigger_price: amountToCosmosSdkDecAmount(order.trigger_price || '0').toFixed(),
                }
                : undefined,
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgLiquidatePosition',
            message: proto,
        };
    }
    toBinary() {
        return InjectiveExchangeV1Beta1Tx.MsgLiquidatePosition.encode(this.toProto()).finish();
    }
}
