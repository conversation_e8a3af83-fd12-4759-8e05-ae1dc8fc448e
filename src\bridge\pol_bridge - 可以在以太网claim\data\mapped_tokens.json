{"******************************************": {"symbol": "WMATIC", "polygon_info": {"address": "******************************************", "name": "Wrapped MATIC", "symbol": "WMATIC", "decimals": 18, "chainId": 137, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", "tags": ["wrapped"]}, "ethereum_info": {"address": "******************************************", "name": "Polygon", "symbol": "MATIC", "decimals": 18, "chainId": 1, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "tags": ["pos"]}}, "******************************************": {"symbol": "USDT", "polygon_info": {"address": "******************************************", "name": "Tether USD (PoS)", "symbol": "USDT", "decimals": 6, "chainId": 137, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", "tags": ["stablecoin"]}, "ethereum_info": {"address": "******************************************", "name": "Tether USD", "symbol": "USDT", "decimals": 6, "chainId": 1, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "tags": ["stablecoin"]}}, "******************************************": {"symbol": "USDC", "polygon_info": {"address": "******************************************", "name": "USD Coin (PoS)", "symbol": "USDC", "decimals": 6, "chainId": 137, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", "tags": ["stablecoin"]}, "ethereum_info": {"address": "******************************************", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "chainId": 1, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "tags": ["stablecoin"]}}, "******************************************": {"symbol": "RNDR", "polygon_info": {"address": "******************************************", "name": "Render Token", "symbol": "RNDR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Render Token", "symbol": "RNDR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "NITRO", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "NITRO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "NITRO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "LINK", "polygon_info": {"address": "******************************************", "name": "ChainLink Token", "symbol": "LINK", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ChainLink Token", "symbol": "LINK", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WETH", "polygon_info": {"address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18, "chainId": 137, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", "tags": ["wrapped"]}, "ethereum_info": {"address": "******************************************", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 18, "chainId": 1, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "tags": ["wrapped"]}}, "******************************************": {"symbol": "DAI", "polygon_info": {"address": "******************************************", "name": "Dai Stablecoin", "symbol": "DAI", "decimals": 18, "chainId": 137, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", "tags": ["stablecoin"]}, "ethereum_info": {"address": "******************************************", "name": "Dai Stablecoin", "symbol": "DAI", "decimals": 18, "chainId": 1, "logoURI": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "tags": ["stablecoin"]}}, "******************************************": {"symbol": "AAVE", "polygon_info": {"address": "******************************************", "name": "Aave (PoS)", "symbol": "AAVE", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "AAVE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CRV", "polygon_info": {"address": "******************************************", "name": "CRV (PoS)", "symbol": "CRV", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Curve DAO Token", "symbol": "CRV", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GHST", "polygon_info": {"address": "******************************************", "name": "Aavegotchi GHST Token (PoS)", "symbol": "GHST", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Aavegotchi GHST Token", "symbol": "GHST", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WBTC", "polygon_info": {"address": "******************************************", "name": "(PoS) Wrapped BTC", "symbol": "WBTC", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "TEL", "polygon_info": {"address": "******************************************", "name": "Telcoin (PoS)", "symbol": "TEL", "decimals": 2, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Telcoin", "symbol": "TEL", "decimals": 2, "chainId": 1}}, "******************************************": {"symbol": "SAND", "polygon_info": {"address": "******************************************", "name": "SAND", "symbol": "SAND", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SAND", "symbol": "SAND", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "COMP", "polygon_info": {"address": "******************************************", "name": "(PoS) Compound", "symbol": "COMP", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Compound", "symbol": "COMP", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "UNI", "polygon_info": {"address": "******************************************", "name": "Uniswap (PoS)", "symbol": "UNI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Uniswap", "symbol": "UNI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WCHI", "polygon_info": {"address": "******************************************", "name": "Wrapped CHI (PoS)", "symbol": "WCHI", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wrapped CHI", "symbol": "WCHI", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "LDO", "polygon_info": {"address": "******************************************", "name": "Lido DAO Token (PoS)", "symbol": "LDO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Lido DAO Token", "symbol": "LDO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "wstETH", "polygon_info": {"address": "******************************************", "name": "Wrapped liquid staked Ether 2.0 (PoS)", "symbol": "wstETH", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wrapped liquid staked Ether 2.0", "symbol": "wstETH", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "QUICK", "polygon_info": {"address": "******************************************", "name": "Quickswap", "symbol": "QUICK", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Quickswap", "symbol": "QUICK", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "PAXG", "polygon_info": {"address": "******************************************", "name": "Paxos Gold (PoS)", "symbol": "PAXG", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Paxos Gold", "symbol": "PAXG", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CXO", "polygon_info": {"address": "******************************************", "name": "CargoX Token (PoS)", "symbol": "CXO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "CargoX Token", "symbol": "CXO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ORBS", "polygon_info": {"address": "******************************************", "name": "Orbs (PoS)", "symbol": "ORBS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Or<PERSON>", "symbol": "ORBS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BAL", "polygon_info": {"address": "******************************************", "name": "Balancer (PoS)", "symbol": "BAL", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Balancer", "symbol": "BAL", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "stMATIC", "polygon_info": {"address": "******************************************", "name": "Staked MATIC (PoS)", "symbol": "stMATIC", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Staked MATIC", "symbol": "stMATIC", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SUPER", "polygon_info": {"address": "******************************************", "name": "SuperFarm (PoS)", "symbol": "SUPER", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SuperFarm", "symbol": "SUPER", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GRT", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "GRT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Graph <PERSON>", "symbol": "GRT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ELON", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "ELON", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "ELON", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MVI", "polygon_info": {"address": "******************************************", "name": "Metaverse Index (PoS)", "symbol": "MVI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Metaverse Index", "symbol": "MVI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "APE", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PoS)", "symbol": "APE", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ApeCoin", "symbol": "APE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MNW", "polygon_info": {"address": "******************************************", "name": "Morpheus.Network (PoS)", "symbol": "MNW", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Morpheus.Network", "symbol": "MNW", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ALI", "polygon_info": {"address": "******************************************", "name": "Artificial Liquid Intelligence Token", "symbol": "ALI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Artificial Liquid Intelligence Token", "symbol": "ALI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MANA", "polygon_info": {"address": "******************************************", "name": "(PoS) Decentraland MANA", "symbol": "MANA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Decentraland MANA", "symbol": "MANA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "EURA", "polygon_info": {"address": "******************************************", "name": "EURA (previously agEUR)", "symbol": "EURA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "EURA (previously agEUR)", "symbol": "EURA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SUSHI", "polygon_info": {"address": "******************************************", "name": "SushiToken (PoS)", "symbol": "SUSHI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SushiToken", "symbol": "SUSHI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MYST", "polygon_info": {"address": "******************************************", "name": "Mysterium (PoS)", "symbol": "MYST", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Mysterium", "symbol": "MYST", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MaticX", "polygon_info": {"address": "******************************************", "name": "Liquid Staking Matic (PoS)", "symbol": "MaticX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Liquid Staking Matic", "symbol": "MaticX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FORT", "polygon_info": {"address": "******************************************", "name": "Forta", "symbol": "FORT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Forta", "symbol": "FORT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WOO", "polygon_info": {"address": "******************************************", "name": "Wootrade Network (PoS)", "symbol": "WOO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wootrade Network", "symbol": "WOO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SG", "polygon_info": {"address": "******************************************", "name": "SocialGood (PoS)", "symbol": "SG", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SocialGood", "symbol": "SG", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "HEX", "polygon_info": {"address": "******************************************", "name": "HEX (PoS)", "symbol": "HEX", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "HEX", "symbol": "HEX", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "PYR", "polygon_info": {"address": "******************************************", "name": "PYR Token", "symbol": "PYR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "PYR Token", "symbol": "PYR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DMT", "polygon_info": {"address": "******************************************", "name": "Dragon Master Token (PoS)", "symbol": "DMT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Dragon Master Token", "symbol": "DMT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SUKU", "polygon_info": {"address": "******************************************", "name": "SUKU (PoS)", "symbol": "SUKU", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SUKU", "symbol": "SUKU", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "APW", "polygon_info": {"address": "******************************************", "name": "AP<PERSON>ine <PERSON> (PoS)", "symbol": "APW", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "APWine <PERSON>", "symbol": "APW", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DOLZ", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "DOLZ", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Dolz", "symbol": "DOLZ", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BANANA", "polygon_info": {"address": "******************************************", "name": "Banana", "symbol": "BANANA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Banana", "symbol": "BANANA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "NEUY", "polygon_info": {"address": "******************************************", "name": "NEUY (PoS)", "symbol": "NEUY", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "NEUY", "symbol": "NEUY", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DPI", "polygon_info": {"address": "******************************************", "name": "DefiPulse Index (PoS)", "symbol": "DPI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DefiPulse Index", "symbol": "DPI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "EURS", "polygon_info": {"address": "******************************************", "name": "STASIS EURS Token (PoS)", "symbol": "EURS", "decimals": 2, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "STASIS EURS Token", "symbol": "EURS", "decimals": 2, "chainId": 1}}, "******************************************": {"symbol": "SNX", "polygon_info": {"address": "******************************************", "name": "Synthetix Network Token (PoS)", "symbol": "SNX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Synthetix Network Token", "symbol": "SNX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DFYN", "polygon_info": {"address": "******************************************", "name": "DFYN Token (PoS)", "symbol": "DFYN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DFYN Token", "symbol": "DFYN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ICHI", "polygon_info": {"address": "******************************************", "name": "ICHI", "symbol": "ICHI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ICHI", "symbol": "ICHI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "REVV", "polygon_info": {"address": "******************************************", "name": "REVV", "symbol": "REVV", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "REVV", "symbol": "REVV", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FACTR", "polygon_info": {"address": "******************************************", "name": "Defactor (PoS)", "symbol": "FACTR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Defactor", "symbol": "FACTR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BCUT", "polygon_info": {"address": "******************************************", "name": "bitsCrunch Token(PoS)", "symbol": "BCUT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "bitsCrunch Token", "symbol": "BCUT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OCEAN", "polygon_info": {"address": "******************************************", "name": "Ocean Token (PoS)", "symbol": "mOCEAN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Ocean Token", "symbol": "OCEAN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "crvUSD", "polygon_info": {"address": "******************************************", "name": "Curve.Fi USD Stablecoin(PoS)", "symbol": "crvUSD", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Curve.Fi USD Stablecoin", "symbol": "crvUSD", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DHT", "polygon_info": {"address": "******************************************", "name": "dHedge DAO Token (PoS)", "symbol": "DHT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "dHedge DAO Token", "symbol": "DHT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ADS", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "ADS", "decimals": 11, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "ADS", "decimals": 11, "chainId": 1}}, "******************************************": {"symbol": "ROUTE", "polygon_info": {"address": "******************************************", "name": "Route", "symbol": "ROUTE (PoS)", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Route", "symbol": "ROUTE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FLUID", "polygon_info": {"address": "******************************************", "name": "Instadapp (PoS)", "symbol": "INST", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Fluid", "symbol": "FLUID", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "dEURO", "polygon_info": {"address": "******************************************", "name": "DecentralizedEURO(PoS)", "symbol": "dEURO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DecentralizedEURO", "symbol": "dEURO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BOSON", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "BOSON", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON>son <PERSON>", "symbol": "BOSON", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "KNC", "polygon_info": {"address": "******************************************", "name": "Kyber Network Crystal v2 (PoS)", "symbol": "KNC", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Kyber Network Crystal v2", "symbol": "KNC", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WOMBAT", "polygon_info": {"address": "******************************************", "name": "Wombat", "symbol": "WOMBAT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wombat", "symbol": "WOMBAT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CGG", "polygon_info": {"address": "******************************************", "name": "ChainGuardians Governance Token (PoS)", "symbol": "CGG", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ChainGuardians Governance Token", "symbol": "CGG", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "COMBO", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "symbol": "COMBO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Furucombo", "symbol": "COMBO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DFX", "polygon_info": {"address": "******************************************", "name": "DFX Token (PoS)", "symbol": "DFX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DFX Token", "symbol": "DFX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MCHC", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON><PERSON> (PoS)", "symbol": "MCHC", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MCHCoin", "symbol": "MCHC", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "IOEN", "polygon_info": {"address": "******************************************", "name": "Internet of Energy Network", "symbol": "IOEN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Internet of Energy Network", "symbol": "IOEN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MASK", "polygon_info": {"address": "******************************************", "name": "Mask Network (PoS)", "symbol": "MASK", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Mask Network", "symbol": "MASK", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CHP", "polygon_info": {"address": "******************************************", "name": "CoinPoker Chips (PoS)", "symbol": "CHP", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "CoinPoker Chips", "symbol": "CHP", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MUST", "polygon_info": {"address": "******************************************", "name": "Must", "symbol": "MUST", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Must", "symbol": "MUST", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FOX", "polygon_info": {"address": "******************************************", "name": "FOX (PoS)", "symbol": "FOX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "FOX", "symbol": "FOX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "NEXO", "polygon_info": {"address": "******************************************", "name": "Nexo (PoS)", "symbol": "NEXO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Nexo", "symbol": "NEXO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SWASH", "polygon_info": {"address": "******************************************", "name": "Swash Token", "symbol": "SWASH", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Swash Token", "symbol": "SWASH", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "UST", "polygon_info": {"address": "******************************************", "name": "Wrapped UST Token (PoS)", "symbol": "UST", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Wrapped UST Token", "symbol": "UST", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CEL", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "CEL", "decimals": 4, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "CEL", "decimals": 4, "chainId": 1}}, "******************************************": {"symbol": "SX", "polygon_info": {"address": "******************************************", "name": "SportX (PoS)", "symbol": "SX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SportX", "symbol": "SX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SD", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "SD", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "SD", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "NEX", "polygon_info": {"address": "******************************************", "name": "Nash Exchange Token (PoS)", "symbol": "NEX", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Nash Exchange Token", "symbol": "NEX", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "RMV", "polygon_info": {"address": "******************************************", "name": "RealityMetaverse(PoS)", "symbol": "RMV", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "RealityMetaverse", "symbol": "RMV", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "WINTER", "polygon_info": {"address": "******************************************", "name": "Winter Token (PoS)", "symbol": "WINTER", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Winter Token", "symbol": "WINTER", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "HANU", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "HANU", "decimals": 12, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "HANU", "decimals": 12, "chainId": 1}}, "******************************************": {"symbol": "DGEN", "polygon_info": {"address": "******************************************", "name": "DEGEN (PoS)", "symbol": "DGEN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DEGEN", "symbol": "DGEN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "EURT", "polygon_info": {"address": "******************************************", "name": "Euro Tether (PoS)", "symbol": "EURT", "decimals": 6, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Euro Tether", "symbol": "EURT", "decimals": 6, "chainId": 1}}, "******************************************": {"symbol": "SMT", "polygon_info": {"address": "******************************************", "name": "Swarm Markets", "symbol": "SMT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Swarm Markets", "symbol": "SMT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "$ZKP", "polygon_info": {"address": "******************************************", "name": "$ZKP Token", "symbol": "$ZKP", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "$ZKP Token", "symbol": "$ZKP", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "PLR", "polygon_info": {"address": "******************************************", "name": "PILLAR (PoS)", "symbol": "PLR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "PILLAR", "symbol": "PLR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OM", "polygon_info": {"address": "******************************************", "name": "MANTRA DAO (PoS)", "symbol": "OM", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MANTRA DAO", "symbol": "OM", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "PLOT", "polygon_info": {"address": "******************************************", "name": "PlotX", "symbol": "PLOT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "PLOT", "symbol": "PLOT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BLES", "polygon_info": {"address": "******************************************", "name": "Blind Boxes Token (PoS)", "symbol": "BLES", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Blind Boxes Token", "symbol": "BLES", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "1INCH", "polygon_info": {"address": "******************************************", "name": "1Inch (PoS)", "symbol": "1INCH", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "1INCH Token", "symbol": "1INCH", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GALAXIS", "polygon_info": {"address": "******************************************", "name": "GALAXIS Token(PoS)", "symbol": "GALAXIS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "GALAXIS Token", "symbol": "GALAXIS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "AUTUMN", "polygon_info": {"address": "******************************************", "name": "Autumn Token (PoS)", "symbol": "AUTUMN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Autumn Token", "symbol": "AUTUMN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DINO", "polygon_info": {"address": "******************************************", "name": "DinoS<PERSON>p (PoS)", "symbol": "DINO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "DinoSwap", "symbol": "DINO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SHIB", "polygon_info": {"address": "******************************************", "name": "SHIBA INU (PoS)", "symbol": "SHIB", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SHIBA INU", "symbol": "SHIB", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BC", "polygon_info": {"address": "******************************************", "name": "Blood Crystal (PoS)", "symbol": "BC", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Blood Crystal", "symbol": "BC", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "VAI", "polygon_info": {"address": "******************************************", "name": "VAIOT Token", "symbol": "VAI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "VAIOT Token", "symbol": "VAI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MIMO", "polygon_info": {"address": "******************************************", "name": "MIMO Parallel Governance Token (PoS)", "symbol": "MIMO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MIMO Parallel Governance Token", "symbol": "MIMO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MEED", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> Token (PoS)", "symbol": "MEED", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Meeds Token", "symbol": "MEED", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "EMON", "polygon_info": {"address": "******************************************", "name": "EthermonToken", "symbol": "EMON", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "EthermonToken", "symbol": "EMON", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "UBT", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "UBT", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "UniBright", "symbol": "UBT", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "GET", "polygon_info": {"address": "******************************************", "name": "GET Protocol (PoS)", "symbol": "GET", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "GET", "symbol": "GET", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "AMKT", "polygon_info": {"address": "******************************************", "name": "Alongside Crypto Market Index (PoS)", "symbol": "AMKT", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Alongside Crypto Market Index", "symbol": "AMKT", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SPORK", "polygon_info": {"address": "******************************************", "name": "The SporkDAO Token (PoS)", "symbol": "SPORK", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "The SporkDAO Token", "symbol": "SPORK", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CRO", "polygon_info": {"address": "******************************************", "name": "CRO (PoS)", "symbol": "CRO", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "CRO", "symbol": "CRO", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "DYDX", "polygon_info": {"address": "******************************************", "name": "dYdX (PoS)", "symbol": "DYDX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "dYdX", "symbol": "DYDX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "TOWER", "polygon_info": {"address": "******************************************", "name": "TOWER", "symbol": "TOWER", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "TOWER", "symbol": "TOWER", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OPN", "polygon_info": {"address": "******************************************", "name": "Open Ecosystem Token(PoS)", "symbol": "OPN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Open Ecosystem Token", "symbol": "OPN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CHAIN", "polygon_info": {"address": "******************************************", "name": "Chain Games", "symbol": "CHAIN", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Chain Games", "symbol": "CHAIN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MASQ", "polygon_info": {"address": "******************************************", "name": "MASQ (PoS)", "symbol": "MASQ", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MASQ", "symbol": "MASQ", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "PSP", "polygon_info": {"address": "******************************************", "name": "ParaSwap (PoS)", "symbol": "PSP", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ParaSwap", "symbol": "PSP", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OVR", "polygon_info": {"address": "******************************************", "name": "OVR (PoS)", "symbol": "OVR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "OVR", "symbol": "OVR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SUMMER", "polygon_info": {"address": "******************************************", "name": "Summer Token (PoS)", "symbol": "SUMMER", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Summer Token", "symbol": "SUMMER", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "VEE", "polygon_info": {"address": "******************************************", "name": "BLOCKv Token (PoS)", "symbol": "VEE", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "BLOCKv Token", "symbol": "VEE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "TUSD", "polygon_info": {"address": "******************************************", "name": "TrueUSD (PoS)", "symbol": "TUSD", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "TrueUSD", "symbol": "TUSD", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "INSUR", "polygon_info": {"address": "******************************************", "name": "InsurAce (PoS)", "symbol": "INSUR", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "InsurAce", "symbol": "INSUR", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BLANK", "polygon_info": {"address": "******************************************", "name": "GoBlank <PERSON> (PoS)", "symbol": "BLANK", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "GoBlank Token", "symbol": "BLANK", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OLAS", "polygon_info": {"address": "******************************************", "name": "Autonolas(PoS)", "symbol": "OLAS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Autonolas", "symbol": "OLAS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "UNKNOWN", "polygon_info": {"address": "******************************************", "name": "Darwinia (PoS)", "symbol": "Ring", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Unknown", "symbol": "UNKNOWN", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MAHA", "polygon_info": {"address": "******************************************", "name": "MahaDAO (PoS)", "symbol": "MAHA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MahaDAO", "symbol": "MAHA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ATA", "polygon_info": {"address": "******************************************", "name": "Automata (PoS)", "symbol": "ATA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Automata", "symbol": "ATA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "DEPS", "polygon_info": {"address": "******************************************", "name": "Decentralized Euro Protocol Share(PoS)", "symbol": "DEPS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Decentralized Euro Protocol Share", "symbol": "DEPS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MTA", "polygon_info": {"address": "******************************************", "name": "Meta (PoS)", "symbol": "MTA", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Meta", "symbol": "MTA", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GLM", "polygon_info": {"address": "******************************************", "name": "Golem Network Token (PoS)", "symbol": "GLM", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Golem Network Token", "symbol": "GLM", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FOLO", "polygon_info": {"address": "******************************************", "name": "Follow (PoS)", "symbol": "FOLO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Follow", "symbol": "FOLO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "SGT", "polygon_info": {"address": "******************************************", "name": "SGT (PoS)", "symbol": "SGT", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SGT", "symbol": "SGT", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "ISP", "polygon_info": {"address": "******************************************", "name": "Ispolink Token (PoS)", "symbol": "ISP", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Ispolink Token", "symbol": "ISP", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "XYO", "polygon_info": {"address": "******************************************", "name": "XY Oracle (PoS)", "symbol": "XYO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "XY Oracle", "symbol": "XYO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FLEATO", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON> (PoS)", "symbol": "FLEATO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON><PERSON>", "symbol": "FLEATO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "RAGE", "polygon_info": {"address": "******************************************", "name": "RAGEMATIC (PoS)", "symbol": "RAGE", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "RageToken", "symbol": "RAGE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "VERI", "polygon_info": {"address": "******************************************", "name": "Veritaseum (PoS)", "symbol": "VERI", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Veritaseum", "symbol": "VERI", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "AGIX", "polygon_info": {"address": "******************************************", "name": "SingularityNET Token (PoS)", "symbol": "AGIX", "decimals": 8, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "SingularityNET Token", "symbol": "AGIX", "decimals": 8, "chainId": 1}}, "******************************************": {"symbol": "TRAXX", "polygon_info": {"address": "******************************************", "name": "TRAXX", "symbol": "TRAXX", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "TRAXX", "symbol": "TRAXX", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ENS", "polygon_info": {"address": "******************************************", "name": "Ethereum Name Service (PoS)", "symbol": "ENS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Ethereum Name Service", "symbol": "ENS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GLQ", "polygon_info": {"address": "******************************************", "name": "GraphLinq (PoS)", "symbol": "GLQ", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "GraphLinq", "symbol": "GLQ", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "TruMATIC", "polygon_info": {"address": "******************************************", "name": "TruStake MATIC Vault Shares (PoS)", "symbol": "TruMATIC", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "TruStake MATIC Vault Shares", "symbol": "TruMATIC", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "OKB", "polygon_info": {"address": "******************************************", "name": "OKB (PoS)", "symbol": "OKB", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "OKB", "symbol": "OKB", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FET", "polygon_info": {"address": "******************************************", "name": "<PERSON>tch (PoS)", "symbol": "FET", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON>tch", "symbol": "FET", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "MEMO", "polygon_info": {"address": "******************************************", "name": "MetaMEMO (PoS)", "symbol": "MEMO", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "MetaMEMO", "symbol": "MEMO", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "ANGLE", "polygon_info": {"address": "******************************************", "name": "ANGLE", "symbol": "ANGLE", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "ANGLE", "symbol": "ANGLE", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "BSJ", "polygon_info": {"address": "******************************************", "name": "BASENJI (PoS)", "symbol": "BSJ", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "BASENJI", "symbol": "BSJ", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "FTM", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "FTM", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "Fantom <PERSON>", "symbol": "FTM", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "CROS", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON>(PoS)", "symbol": "CROS", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "<PERSON><PERSON>", "symbol": "CROS", "decimals": 18, "chainId": 1}}, "******************************************": {"symbol": "GRG", "polygon_info": {"address": "******************************************", "name": "<PERSON><PERSON> (PoS)", "symbol": "GRG", "decimals": 18, "chainId": 137}, "ethereum_info": {"address": "******************************************", "name": "R<PERSON>", "symbol": "GRG", "decimals": 18, "chainId": 1}}}