"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Svc = exports.Rpc = exports.Field = exports.Oneof = exports.Upstream = exports.Type = exports.protobufPackage = void 0;
exports.typeFromJSON = typeFromJSON;
exports.typeToJSON = typeToJSON;
exports.upstreamFromJSON = upstreamFromJSON;
exports.upstreamToJSON = upstreamToJSON;
/* eslint-disable */
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "danielvladco.protobuf.graphql";
var Type;
(function (Type) {
    Type[Type["DEFAULT"] = 0] = "DEFAULT";
    Type[Type["MUTATION"] = 1] = "MUTATION";
    Type[Type["QUERY"] = 2] = "QUERY";
    Type[Type["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Type || (exports.Type = Type = {}));
function typeFromJSON(object) {
    switch (object) {
        case 0:
        case "DEFAULT":
            return Type.DEFAULT;
        case 1:
        case "MUTATION":
            return Type.MUTATION;
        case 2:
        case "QUERY":
            return Type.QUERY;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Type.UNRECOGNIZED;
    }
}
function typeToJSON(object) {
    switch (object) {
        case Type.DEFAULT:
            return "DEFAULT";
        case Type.MUTATION:
            return "MUTATION";
        case Type.QUERY:
            return "QUERY";
        case Type.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
var Upstream;
(function (Upstream) {
    Upstream[Upstream["UPSTREAM_UNSPECIFIED"] = 0] = "UPSTREAM_UNSPECIFIED";
    Upstream[Upstream["UPSTREAM_SERVER"] = 1] = "UPSTREAM_SERVER";
    Upstream[Upstream["UPSTREAM_CLIENT"] = 2] = "UPSTREAM_CLIENT";
    Upstream[Upstream["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(Upstream || (exports.Upstream = Upstream = {}));
function upstreamFromJSON(object) {
    switch (object) {
        case 0:
        case "UPSTREAM_UNSPECIFIED":
            return Upstream.UPSTREAM_UNSPECIFIED;
        case 1:
        case "UPSTREAM_SERVER":
            return Upstream.UPSTREAM_SERVER;
        case 2:
        case "UPSTREAM_CLIENT":
            return Upstream.UPSTREAM_CLIENT;
        case -1:
        case "UNRECOGNIZED":
        default:
            return Upstream.UNRECOGNIZED;
    }
}
function upstreamToJSON(object) {
    switch (object) {
        case Upstream.UPSTREAM_UNSPECIFIED:
            return "UPSTREAM_UNSPECIFIED";
        case Upstream.UPSTREAM_SERVER:
            return "UPSTREAM_SERVER";
        case Upstream.UPSTREAM_CLIENT:
            return "UPSTREAM_CLIENT";
        case Upstream.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseOneof() {
    return { ignore: false, name: "" };
}
exports.Oneof = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.ignore === true) {
            writer.uint32(32).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(42).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseOneof();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 4:
                    message.ignore = reader.bool();
                    break;
                case 5:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return exports.Oneof.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBaseOneof();
        message.ignore = (_a = object.ignore) !== null && _a !== void 0 ? _a : false;
        message.name = (_b = object.name) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseField() {
    return { required: false, params: "", dirs: "", ignore: false, name: "" };
}
exports.Field = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.required === true) {
            writer.uint32(8).bool(message.required);
        }
        if (message.params !== "") {
            writer.uint32(18).string(message.params);
        }
        if (message.dirs !== "") {
            writer.uint32(26).string(message.dirs);
        }
        if (message.ignore === true) {
            writer.uint32(32).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(42).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseField();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.required = reader.bool();
                    break;
                case 2:
                    message.params = reader.string();
                    break;
                case 3:
                    message.dirs = reader.string();
                    break;
                case 4:
                    message.ignore = reader.bool();
                    break;
                case 5:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            required: isSet(object.required) ? Boolean(object.required) : false,
            params: isSet(object.params) ? String(object.params) : "",
            dirs: isSet(object.dirs) ? String(object.dirs) : "",
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.required !== undefined && (obj.required = message.required);
        message.params !== undefined && (obj.params = message.params);
        message.dirs !== undefined && (obj.dirs = message.dirs);
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return exports.Field.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseField();
        message.required = (_a = object.required) !== null && _a !== void 0 ? _a : false;
        message.params = (_b = object.params) !== null && _b !== void 0 ? _b : "";
        message.dirs = (_c = object.dirs) !== null && _c !== void 0 ? _c : "";
        message.ignore = (_d = object.ignore) !== null && _d !== void 0 ? _d : false;
        message.name = (_e = object.name) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseRpc() {
    return { type: 0, ignore: false, name: "" };
}
exports.Rpc = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.type !== 0) {
            writer.uint32(8).int32(message.type);
        }
        if (message.ignore === true) {
            writer.uint32(16).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(26).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpc();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.int32();
                    break;
                case 2:
                    message.ignore = reader.bool();
                    break;
                case 3:
                    message.name = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? typeFromJSON(object.type) : 0,
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = typeToJSON(message.type));
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        return obj;
    },
    create(base) {
        return exports.Rpc.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseRpc();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : 0;
        message.ignore = (_b = object.ignore) !== null && _b !== void 0 ? _b : false;
        message.name = (_c = object.name) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseSvc() {
    return { type: 0, ignore: false, name: "", upstream: 0 };
}
exports.Svc = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.type !== 0) {
            writer.uint32(8).int32(message.type);
        }
        if (message.ignore === true) {
            writer.uint32(16).bool(message.ignore);
        }
        if (message.name !== "") {
            writer.uint32(26).string(message.name);
        }
        if (message.upstream !== 0) {
            writer.uint32(32).int32(message.upstream);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSvc();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.type = reader.int32();
                    break;
                case 2:
                    message.ignore = reader.bool();
                    break;
                case 3:
                    message.name = reader.string();
                    break;
                case 4:
                    message.upstream = reader.int32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? typeFromJSON(object.type) : 0,
            ignore: isSet(object.ignore) ? Boolean(object.ignore) : false,
            name: isSet(object.name) ? String(object.name) : "",
            upstream: isSet(object.upstream) ? upstreamFromJSON(object.upstream) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.type !== undefined && (obj.type = typeToJSON(message.type));
        message.ignore !== undefined && (obj.ignore = message.ignore);
        message.name !== undefined && (obj.name = message.name);
        message.upstream !== undefined && (obj.upstream = upstreamToJSON(message.upstream));
        return obj;
    },
    create(base) {
        return exports.Svc.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBaseSvc();
        message.type = (_a = object.type) !== null && _a !== void 0 ? _a : 0;
        message.ignore = (_b = object.ignore) !== null && _b !== void 0 ? _b : false;
        message.name = (_c = object.name) !== null && _c !== void 0 ? _c : "";
        message.upstream = (_d = object.upstream) !== null && _d !== void 0 ? _d : 0;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
