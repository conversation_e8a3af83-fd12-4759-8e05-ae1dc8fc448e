{"name": "@0no-co/graphqlsp", "version": "1.12.16", "description": "TypeScript LSP plugin that finds GraphQL documents in your code and provides hints and auto-generates types.", "main": "./dist/graphqlsp", "module": "./dist/graphqlsp.mjs", "types": "./dist/graphqlsp.d.ts", "exports": {".": {"types": "./dist/graphqlsp.d.ts", "import": "./dist/graphqlsp.mjs", "require": "./dist/graphqlsp.js", "source": "./src/index.ts"}, "./api": {"types": "./dist/api.d.ts", "import": "./dist/api.mjs", "require": "./dist/api.js", "source": "./src/api.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/0no-co/GraphQLSP.git"}, "keywords": ["GraphQL", "TypeScript", "LSP", "Typed-document-node"], "author": "0no.co <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/0no-co/GraphQLSP/issues"}, "homepage": "https://github.com/0no-co/GraphQLSP#readme", "devDependencies": {"@0no-co/graphql.web": "^1.0.4", "@sindresorhus/fnv1a": "^2.0.0", "@types/node": "^18.15.11", "graphql-language-service": "^5.2.0", "lru-cache": "^10.0.1", "typescript": "^5.3.3"}, "dependencies": {"@gql.tada/internal": "^1.0.0", "graphql": "^15.5.0 || ^16.0.0 || ^17.0.0"}, "peerDependencies": {"graphql": "^15.5.0 || ^16.0.0 || ^17.0.0", "typescript": "^5.0.0"}, "publishConfig": {"provenance": true}, "scripts": {"build": "rollup -c ../../scripts/rollup.config.mjs", "dev": "NODE_ENV=development pnpm build --watch"}}