import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { AssetPair, Params, PriceAttestation } from "./oracle";
export declare const protobufPackage = "injective.oracle.v1beta1";
/**
 * MsgRelayProviderPrice defines a SDK message for setting a price through the
 * provider oracle.
 */
export interface MsgRelayProviderPrices {
    sender: string;
    provider: string;
    symbols: string[];
    prices: string[];
}
export interface MsgRelayProviderPricesResponse {
}
/**
 * MsgRelayPriceFeedPrice defines a SDK message for setting a price through the
 * pricefeed oracle.
 */
export interface MsgRelayPriceFeedPrice {
    sender: string;
    base: string[];
    quote: string[];
    /** price defines the price of the oracle base and quote */
    price: string[];
}
export interface MsgRelayPriceFeedPriceResponse {
}
export interface MsgRelayBandRates {
    relayer: string;
    symbols: string[];
    rates: string[];
    resolveTimes: string[];
    requestIDs: string[];
}
export interface MsgRelayBandRatesResponse {
}
/**
 * MsgRelayCoinbaseMessages defines a SDK message for relaying price messages
 * from Coinbase API.
 */
export interface MsgRelayCoinbaseMessages {
    sender: string;
    messages: Uint8Array[];
    signatures: Uint8Array[];
}
export interface MsgRelayCoinbaseMessagesResponse {
}
/**
 * MsgRelayStorkPrices defines a SDK message for relaying price message
 * from Stork API.
 */
export interface MsgRelayStorkPrices {
    sender: string;
    assetPairs: AssetPair[];
}
export interface MsgRelayStorkPricesResponse {
}
/**
 * MsgRequestBandIBCRates defines a SDK message for requesting data from
 * BandChain using IBC.
 */
export interface MsgRequestBandIBCRates {
    sender: string;
    requestId: string;
}
/** MsgRequestDataResponse defines the Msg/RequestBandIBCRates response type. */
export interface MsgRequestBandIBCRatesResponse {
}
/** MsgRelayPythPrices defines a SDK message for updating Pyth prices */
export interface MsgRelayPythPrices {
    sender: string;
    priceAttestations: PriceAttestation[];
}
/** MsgRelayPythPricesResponse defines the Msg/RelayPythPrices response type. */
export interface MsgRelayPythPricesResponse {
}
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the oracle parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
export declare const MsgRelayProviderPrices: {
    encode(message: MsgRelayProviderPrices, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayProviderPrices;
    fromJSON(object: any): MsgRelayProviderPrices;
    toJSON(message: MsgRelayProviderPrices): unknown;
    create(base?: DeepPartial<MsgRelayProviderPrices>): MsgRelayProviderPrices;
    fromPartial(object: DeepPartial<MsgRelayProviderPrices>): MsgRelayProviderPrices;
};
export declare const MsgRelayProviderPricesResponse: {
    encode(_: MsgRelayProviderPricesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayProviderPricesResponse;
    fromJSON(_: any): MsgRelayProviderPricesResponse;
    toJSON(_: MsgRelayProviderPricesResponse): unknown;
    create(base?: DeepPartial<MsgRelayProviderPricesResponse>): MsgRelayProviderPricesResponse;
    fromPartial(_: DeepPartial<MsgRelayProviderPricesResponse>): MsgRelayProviderPricesResponse;
};
export declare const MsgRelayPriceFeedPrice: {
    encode(message: MsgRelayPriceFeedPrice, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayPriceFeedPrice;
    fromJSON(object: any): MsgRelayPriceFeedPrice;
    toJSON(message: MsgRelayPriceFeedPrice): unknown;
    create(base?: DeepPartial<MsgRelayPriceFeedPrice>): MsgRelayPriceFeedPrice;
    fromPartial(object: DeepPartial<MsgRelayPriceFeedPrice>): MsgRelayPriceFeedPrice;
};
export declare const MsgRelayPriceFeedPriceResponse: {
    encode(_: MsgRelayPriceFeedPriceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayPriceFeedPriceResponse;
    fromJSON(_: any): MsgRelayPriceFeedPriceResponse;
    toJSON(_: MsgRelayPriceFeedPriceResponse): unknown;
    create(base?: DeepPartial<MsgRelayPriceFeedPriceResponse>): MsgRelayPriceFeedPriceResponse;
    fromPartial(_: DeepPartial<MsgRelayPriceFeedPriceResponse>): MsgRelayPriceFeedPriceResponse;
};
export declare const MsgRelayBandRates: {
    encode(message: MsgRelayBandRates, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayBandRates;
    fromJSON(object: any): MsgRelayBandRates;
    toJSON(message: MsgRelayBandRates): unknown;
    create(base?: DeepPartial<MsgRelayBandRates>): MsgRelayBandRates;
    fromPartial(object: DeepPartial<MsgRelayBandRates>): MsgRelayBandRates;
};
export declare const MsgRelayBandRatesResponse: {
    encode(_: MsgRelayBandRatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayBandRatesResponse;
    fromJSON(_: any): MsgRelayBandRatesResponse;
    toJSON(_: MsgRelayBandRatesResponse): unknown;
    create(base?: DeepPartial<MsgRelayBandRatesResponse>): MsgRelayBandRatesResponse;
    fromPartial(_: DeepPartial<MsgRelayBandRatesResponse>): MsgRelayBandRatesResponse;
};
export declare const MsgRelayCoinbaseMessages: {
    encode(message: MsgRelayCoinbaseMessages, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayCoinbaseMessages;
    fromJSON(object: any): MsgRelayCoinbaseMessages;
    toJSON(message: MsgRelayCoinbaseMessages): unknown;
    create(base?: DeepPartial<MsgRelayCoinbaseMessages>): MsgRelayCoinbaseMessages;
    fromPartial(object: DeepPartial<MsgRelayCoinbaseMessages>): MsgRelayCoinbaseMessages;
};
export declare const MsgRelayCoinbaseMessagesResponse: {
    encode(_: MsgRelayCoinbaseMessagesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayCoinbaseMessagesResponse;
    fromJSON(_: any): MsgRelayCoinbaseMessagesResponse;
    toJSON(_: MsgRelayCoinbaseMessagesResponse): unknown;
    create(base?: DeepPartial<MsgRelayCoinbaseMessagesResponse>): MsgRelayCoinbaseMessagesResponse;
    fromPartial(_: DeepPartial<MsgRelayCoinbaseMessagesResponse>): MsgRelayCoinbaseMessagesResponse;
};
export declare const MsgRelayStorkPrices: {
    encode(message: MsgRelayStorkPrices, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayStorkPrices;
    fromJSON(object: any): MsgRelayStorkPrices;
    toJSON(message: MsgRelayStorkPrices): unknown;
    create(base?: DeepPartial<MsgRelayStorkPrices>): MsgRelayStorkPrices;
    fromPartial(object: DeepPartial<MsgRelayStorkPrices>): MsgRelayStorkPrices;
};
export declare const MsgRelayStorkPricesResponse: {
    encode(_: MsgRelayStorkPricesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayStorkPricesResponse;
    fromJSON(_: any): MsgRelayStorkPricesResponse;
    toJSON(_: MsgRelayStorkPricesResponse): unknown;
    create(base?: DeepPartial<MsgRelayStorkPricesResponse>): MsgRelayStorkPricesResponse;
    fromPartial(_: DeepPartial<MsgRelayStorkPricesResponse>): MsgRelayStorkPricesResponse;
};
export declare const MsgRequestBandIBCRates: {
    encode(message: MsgRequestBandIBCRates, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestBandIBCRates;
    fromJSON(object: any): MsgRequestBandIBCRates;
    toJSON(message: MsgRequestBandIBCRates): unknown;
    create(base?: DeepPartial<MsgRequestBandIBCRates>): MsgRequestBandIBCRates;
    fromPartial(object: DeepPartial<MsgRequestBandIBCRates>): MsgRequestBandIBCRates;
};
export declare const MsgRequestBandIBCRatesResponse: {
    encode(_: MsgRequestBandIBCRatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRequestBandIBCRatesResponse;
    fromJSON(_: any): MsgRequestBandIBCRatesResponse;
    toJSON(_: MsgRequestBandIBCRatesResponse): unknown;
    create(base?: DeepPartial<MsgRequestBandIBCRatesResponse>): MsgRequestBandIBCRatesResponse;
    fromPartial(_: DeepPartial<MsgRequestBandIBCRatesResponse>): MsgRequestBandIBCRatesResponse;
};
export declare const MsgRelayPythPrices: {
    encode(message: MsgRelayPythPrices, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayPythPrices;
    fromJSON(object: any): MsgRelayPythPrices;
    toJSON(message: MsgRelayPythPrices): unknown;
    create(base?: DeepPartial<MsgRelayPythPrices>): MsgRelayPythPrices;
    fromPartial(object: DeepPartial<MsgRelayPythPrices>): MsgRelayPythPrices;
};
export declare const MsgRelayPythPricesResponse: {
    encode(_: MsgRelayPythPricesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRelayPythPricesResponse;
    fromJSON(_: any): MsgRelayPythPricesResponse;
    toJSON(_: MsgRelayPythPricesResponse): unknown;
    create(base?: DeepPartial<MsgRelayPythPricesResponse>): MsgRelayPythPricesResponse;
    fromPartial(_: DeepPartial<MsgRelayPythPricesResponse>): MsgRelayPythPricesResponse;
};
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
/** Msg defines the oracle Msg service. */
export interface Msg {
    /**
     * RelayProviderPrice defines a method for relaying a price for a
     * provider-based oracle
     */
    RelayProviderPrices(request: DeepPartial<MsgRelayProviderPrices>, metadata?: grpc.Metadata): Promise<MsgRelayProviderPricesResponse>;
    /**
     * RelayPriceFeedPrice defines a method for relaying a price for a price
     * feeder-based oracle
     */
    RelayPriceFeedPrice(request: DeepPartial<MsgRelayPriceFeedPrice>, metadata?: grpc.Metadata): Promise<MsgRelayPriceFeedPriceResponse>;
    /** RelayBandRates defines a method for relaying rates from Band */
    RelayBandRates(request: DeepPartial<MsgRelayBandRates>, metadata?: grpc.Metadata): Promise<MsgRelayBandRatesResponse>;
    /** RequestBandIBCRates defines a method for fetching rates from Band ibc */
    RequestBandIBCRates(request: DeepPartial<MsgRequestBandIBCRates>, metadata?: grpc.Metadata): Promise<MsgRequestBandIBCRatesResponse>;
    /**
     * RelayCoinbaseMessages defines a method for relaying price messages from
     * Coinbase API
     */
    RelayCoinbaseMessages(request: DeepPartial<MsgRelayCoinbaseMessages>, metadata?: grpc.Metadata): Promise<MsgRelayCoinbaseMessagesResponse>;
    /**
     * RelayStorkMessage defines a method for relaying price message from
     * Stork API
     */
    RelayStorkMessage(request: DeepPartial<MsgRelayStorkPrices>, metadata?: grpc.Metadata): Promise<MsgRelayStorkPricesResponse>;
    /** RelayPythPrices defines a method for relaying rates from the Pyth contract */
    RelayPythPrices(request: DeepPartial<MsgRelayPythPrices>, metadata?: grpc.Metadata): Promise<MsgRelayPythPricesResponse>;
    /** UpdateParams enables updating oracle module's params via governance */
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    RelayProviderPrices(request: DeepPartial<MsgRelayProviderPrices>, metadata?: grpc.Metadata): Promise<MsgRelayProviderPricesResponse>;
    RelayPriceFeedPrice(request: DeepPartial<MsgRelayPriceFeedPrice>, metadata?: grpc.Metadata): Promise<MsgRelayPriceFeedPriceResponse>;
    RelayBandRates(request: DeepPartial<MsgRelayBandRates>, metadata?: grpc.Metadata): Promise<MsgRelayBandRatesResponse>;
    RequestBandIBCRates(request: DeepPartial<MsgRequestBandIBCRates>, metadata?: grpc.Metadata): Promise<MsgRequestBandIBCRatesResponse>;
    RelayCoinbaseMessages(request: DeepPartial<MsgRelayCoinbaseMessages>, metadata?: grpc.Metadata): Promise<MsgRelayCoinbaseMessagesResponse>;
    RelayStorkMessage(request: DeepPartial<MsgRelayStorkPrices>, metadata?: grpc.Metadata): Promise<MsgRelayStorkPricesResponse>;
    RelayPythPrices(request: DeepPartial<MsgRelayPythPrices>, metadata?: grpc.Metadata): Promise<MsgRelayPythPricesResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgRelayProviderPricesDesc: UnaryMethodDefinitionish;
export declare const MsgRelayPriceFeedPriceDesc: UnaryMethodDefinitionish;
export declare const MsgRelayBandRatesDesc: UnaryMethodDefinitionish;
export declare const MsgRequestBandIBCRatesDesc: UnaryMethodDefinitionish;
export declare const MsgRelayCoinbaseMessagesDesc: UnaryMethodDefinitionish;
export declare const MsgRelayStorkMessageDesc: UnaryMethodDefinitionish;
export declare const MsgRelayPythPricesDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
