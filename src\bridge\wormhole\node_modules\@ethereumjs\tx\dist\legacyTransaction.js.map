{"version": 3, "file": "legacyTransaction.js", "sourceRoot": "", "sources": ["../src/legacyTransaction.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,2CAWyB;AACzB,yDAAwD;AAExD,uDAAmD;AACnD,mCAAoC;AAKpC,MAAM,gBAAgB,GAAG,CAAC,CAAA;AAE1B,SAAS,WAAW,CAAC,EAAU,EAAE,OAAe;IAC9C,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAA;IACpB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAC1C,OAAO,CAAC,KAAK,cAAc,GAAG,EAAE,IAAI,CAAC,KAAK,cAAc,GAAG,EAAE,CAAA;AAC/D,CAAC;AAED;;GAEG;AACH,MAAa,WAAY,SAAQ,iCAA4B;IAkE3D;;;;;;OAMG;IACH,YAAmB,MAAc,EAAE,OAAkB,EAAE;QACrD,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,IAAI,CAAC,CAAA;QAElD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QAEpD,IAAI,CAAC,QAAQ,GAAG,IAAA,qBAAc,EAAC,IAAA,eAAQ,EAAC,MAAM,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QAEzF,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,kBAAW,EAAE;YAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,0DAA0D,CAAC,CAAA;YACtF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,IAAI,CAAC,+BAA+B,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAA;QACjE,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAEzC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACpB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;aAChE;iBAAM;gBACL,eAAe;gBACf,kFAAkF;gBAClF,sFAAsF;gBACtF,mGAAmG;gBACnG,oEAAoE;gBACpE,yCAAyC;gBACzC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAE,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;oBAC/C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAA;iBAChE;aACF;SACF;QAED,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,IAAI,IAAI,CAAA;QACnC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SACpB;IACH,CAAC;IAtGD;;;;;;;OAOG;IACI,MAAM,CAAC,UAAU,CAAC,MAAc,EAAE,OAAkB,EAAE;QAC3D,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,UAAkB,EAAE,OAAkB,EAAE;QACrE,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,SAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAa,CAAA;QAE/E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;SAC9D;QAED,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,eAAe,CAAC,MAAqB,EAAE,OAAkB,EAAE;QACvE,uGAAuG;QACvG,oDAAoD;QACpD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAA;SACF;QAED,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QAEpE,IAAA,8BAAuB,EAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;QAEtE,OAAO,IAAI,WAAW,CACpB;YACE,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,EAAE;YACF,KAAK;YACL,IAAI;YACJ,CAAC;YACD,CAAC;YACD,CAAC;SACF,EACD,IAAI,CACL,CAAA;IACH,CAAC;IA6CD;;;;;;;;;;;;OAYG;IACH,GAAG;QACD,OAAO;YACL,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SACxE,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,SAAS;QACP,OAAO,MAAM,CAAC,IAAI,CAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IACzD,CAAC;IAEO,iBAAiB;QACvB,MAAM,MAAM,GAAG;YACb,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAA,6BAAsB,EAAC,IAAI,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,IAAA,6BAAsB,EAAC,IAAI,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,IAAI;SACV,CAAA;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,MAAM,CAAC,IAAI,CAAC,IAAA,6BAAsB,EAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;YAC1D,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAW,EAAC,IAAA,eAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACrC,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAW,EAAC,IAAA,eAAQ,EAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAoBD,gBAAgB,CAAC,WAAW,GAAG,IAAI;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,IAAI,WAAW,EAAE;YACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;SAChE;aAAM;YACL,OAAO,OAAO,CAAA;SACf;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE;YAChF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA;SAChC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBACnB,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE;gBACzB,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;aACjC,CAAA;SACF;QAED,OAAO,KAAK,CAAC,UAAU,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAA;IACnD,CAAC;IAED;;;;;OAKG;IACH,IAAI;QACF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,sDAAsD,CAAC,CAAA;YAClF,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACpB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;aAC9E;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA;SACvB;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAA;YAC5D,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAA,kBAAS,EAAC,SAAG,CAAC,MAAM,CAAC,IAAA,kBAAW,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,OAAO,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAElD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAA;QAExB,IAAI,CAAC,cAAc,EAAE,CAAA;QAErB,IAAI;YACF,OAAO,IAAA,gBAAS,EACd,OAAO,EACP,CAAE,EACF,IAAA,6BAAsB,EAAC,CAAE,CAAC,EAC1B,IAAA,6BAAsB,EAAC,CAAE,CAAC,EAC1B,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CACrF,CAAA;SACF;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAA;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;SACrB;IACH,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS;QACzD,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAU,CAAC,sBAAsB,CAAC,EAAE;YACpD,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;SACnD;QAED,MAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAA;QAEvD,OAAO,WAAW,CAAC,UAAU,CAC3B;YACE,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,CAAC;YACD,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;YACpB,CAAC,EAAE,IAAA,qBAAc,EAAC,CAAC,CAAC;SACrB,EACD,IAAI,CACL,CAAA;IACH,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,QAAQ,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,QAAQ,CAAC;YACpC,EAAE,EAAE,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS;YAC1D,KAAK,EAAE,IAAA,kBAAW,EAAC,IAAI,CAAC,KAAK,CAAC;YAC9B,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YACtC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;YACzD,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1D,CAAA;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,EAAW,EAAE,MAAe;QAC/C,IAAI,aAAa,CAAA;QACjB,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACnD,8DAA8D;QAC9D,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;gBAClC,MAAM,IAAI,KAAK,CACb,oFAAoF,CAAC,EAAE,CACxF,CAAA;aACF;SACF;QAED,6DAA6D;QAC7D,IACE,CAAC,KAAK,SAAS;YACf,CAAC,KAAK,CAAC;YACP,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;YACjD,CAAC,KAAK,EAAE;YACR,CAAC,KAAK,EAAE,EACR;YACA,IAAI,MAAM,EAAE;gBACV,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE;oBAC7C,MAAM,IAAI,KAAK,CACb,+BAA+B,CAAC,iBAAiB,MAAM,CAAC,OAAO,EAAE,gFAAgF,CAClJ,CAAA;iBACF;aACF;iBAAM;gBACL,+BAA+B;gBAC/B,IAAI,MAAM,CAAA;gBACV,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACtB,MAAM,GAAG,EAAE,CAAA;iBACZ;qBAAM;oBACL,MAAM,GAAG,EAAE,CAAA;iBACZ;gBACD,iDAAiD;gBACjD,aAAa,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;aAC/C;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAC5C,QAAQ,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE,CAAA;QACxC,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACO,SAAS,CAAC,GAAW;QAC7B,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAA;IACtC,CAAC;CACF;AAjYD,kCAiYC"}