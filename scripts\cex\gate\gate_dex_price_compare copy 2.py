#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Gate.io与DEX价格比较分析脚本
功能：
1. 读取bidirectional_routable_tokens_polygon.json中的代币列表
2. 同时获取每个代币在Gate和链上的价格
3. 比较价格差异，标记潜在的套利机会
4. 支持多线程处理代币列表和每个代币的CEX/DEX价格查询
5. 使用固定文件名保存结果，当文件大于18MB时自动清理一半旧数据

用法：
python gate_dex_price_compare.py [--threads 4] [--token-threads 4] [--output-file gate_dex_price_comparison.json]
"""

import os
import sys
import json
import time
import logging
import argparse
import asyncio
import threading
import concurrent.futures
import re
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
from pathlib import Path

# 添加项目根目录到系统路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

# 导入相关模块
try:
    from scripts.cex.gate.gate_order_book import get_order_book, get_best_price
    from src.dex.KyberSwap.swap import (
        get_token_address, 
        swap_tokens, 
        get_token_decimals,
        get_dex_price
    )
    from src.utils.logger import setup_logger
except ImportError:
    # 如果导入失败，尝试直接导入
    import logging
    print("无法导入所需模块，请确保已设置正确的PYTHONPATH")
    
    # 创建基本日志记录器
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger

# 创建日志记录器
logger = setup_logger("gate_dex_price_compare")

# 创建文件处理器，捕获完整日志
log_file_path = os.path.join('logs', 'gate_dex_price_compare.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# 文件路径
TOKEN_DATA_PATH = os.path.join(project_root, "data", "cex", "gate_info", "bidirectional_routable_tokens_polygon.json")
DEFAULT_OUTPUT_DIR = os.path.join(project_root, "data", "cex", "gate_info")
DEFAULT_OUTPUT_FILE = os.path.join(DEFAULT_OUTPUT_DIR, "gate_dex_price_comparison.json")

# 文件大小阈值（18MB）
MAX_FILE_SIZE_MB = 18
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024

class LogCaptureHandler(logging.Handler):
    """自定义日志处理器，用于捕获和分析日志消息"""
    
    def __init__(self):
        super().__init__()
        self.log_records = []
        
    def emit(self, record):
        """捕获日志记录"""
        self.log_records.append(self.format(record))
        
    def get_logs(self):
        """获取捕获的日志记录"""
        return self.log_records
        
    def clear(self):
        """清空日志记录"""
        self.log_records = []

class PotentialArbitrageDiscovery:
    """初步套利机会发现类，用于发现CEX和DEX间的价格差异"""
    
    def __init__(self, token_threads: int = 2):
        """
        初始化套利机会发现器
        
        Args:
            token_threads: 每个代币的CEX/DEX价格获取线程数
        """
        self.token_threads = token_threads
        self.usdt_address = "0xc2132d05d31c914a87c6611c10748aeb04b58e8f"  # Polygon USDT地址
        self.chain = "polygon"
        
        # 创建日志捕获器
        self.log_capture = LogCaptureHandler()
        self.log_capture.setFormatter(logging.Formatter('%(message)s'))
        logger.addHandler(self.log_capture)
        
    def extract_amount_from_logs(self, token_symbol: str) -> Tuple[float, float]:
        """
        从捕获的日志中提取交易金额
        
        Args:
            token_symbol: 代币符号，用于日志筛选
            
        Returns:
            Tuple[float, float]: (输入金额, 输出金额)
        """
        input_amount = 100.0  # 默认输入金额
        output_amount = 0.0   # 默认输出金额
        
        # 获取所有日志记录
        logs = self.log_capture.get_logs()
        logger.info(f"处理 {token_symbol} 共捕获 {len(logs)} 条日志")
        
        # 1. 尝试从"预计输出"行提取
        output_pattern = r"预计输出: (\d+\.\d+) 0x[a-fA-F0-9]+"
        
        # 分析日志记录
        for log in logs:
            # 查找预计输出
            match = re.search(output_pattern, log)
            if match:
                try:
                    output_amount = float(match.group(1))
                    logger.info(f"从日志中提取到 {token_symbol} 的预计输出: {output_amount} USDT")
                    break  # 找到后退出循环
                except (ValueError, IndexError) as e:
                    logger.warning(f"解析 {token_symbol} 的预计输出金额时出错: {e}")
        
        # 2. 如果没找到匹配的输出金额，尝试直接从路由摘要中提取
        if output_amount == 0:
            route_pattern = r'"amountOut": "(\d+)"'
            for log in logs:
                match = re.search(route_pattern, log)
                if match:
                    try:
                        # USDT通常有6位小数
                        raw_amount = int(match.group(1))
                        output_amount = raw_amount / 1000000  # 除以10^6得到实际USDT数量
                        logger.info(f"从路由摘要中提取到 {token_symbol} 的输出金额: {output_amount} USDT")
                        break
                    except (ValueError, IndexError) as e:
                        logger.warning(f"从路由摘要解析 {token_symbol} 的输出金额时出错: {e}")
        
        # 3. 仍未找到时，检查是否存在交易预览部分
        if output_amount == 0:
            # 先打印部分日志用于调试
            if logs:
                preview_section_found = False
                for i, log in enumerate(logs):
                    if "交易预览:" in log:
                        preview_section_found = True
                        # 输出交易预览部分周围的几行日志
                        start_idx = max(0, i-1)
                        end_idx = min(len(logs), i+5)
                        logger.info(f"找到交易预览部分，以下是相关内容:")
                        for j in range(start_idx, end_idx):
                            logger.info(f"日志[{j}]: {logs[j]}")
                            
                            # 尝试从"预计输出"行直接提取
                            if "预计输出:" in logs[j]:
                                try:
                                    # 分割行并提取数值
                                    parts = logs[j].strip().split()
                                    for idx, part in enumerate(parts):
                                        if part == "预计输出:":
                                            output_amount = float(parts[idx+1])
                                            logger.info(f"成功从预览行提取输出金额: {output_amount}")
                                            break
                                except Exception as e:
                                    logger.warning(f"从预览行提取时出错: {e}")
                
                if not preview_section_found:
                    logger.warning(f"未找到交易预览部分，尝试解析完整日志...")
                    # 打印日志查找路由摘要
                    for i, log in enumerate(logs):
                        if '"routeSummary"' in log:
                            logger.info(f"找到路由摘要: {log[:200]}...")
            else:
                logger.warning(f"没有捕获到日志")
        
        return input_amount, output_amount
        
    async def get_dex_price(self, token: Dict) -> Dict:
        """
        获取代币在DEX上的价格（使用100个代币兑换为USDT的路由测试）
        
        Args:
            token: 代币信息字典，包含name, symbol, address字段
            
        Returns:
            Dict: 包含DEX价格信息的字典
        """
        token_address = token["address"]
        token_symbol = token["symbol"]
        
        logger.info(f"获取 {token_symbol} 在DEX上的价格...")
        
        try:
            # 使用优化后的get_dex_price函数获取价格
            amount = 100.0  # 测试金额：100个代币
            result = await get_dex_price(
                chain=self.chain,
                token_in=token_address,
                token_out=self.usdt_address,
                amount=amount
            )
            
            if not result["success"]:
                logger.error(f"获取 {token_symbol} 在DEX上的价格失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "获取DEX价格失败"),
                    "timestamp": time.time()
                }
                
            # 记录价格信息
            logger.info(f"DEX价格信息:")
            logger.info(f"输入: {amount} {token_symbol}")
            logger.info(f"输出: {result['amount_out']} USDT")
            logger.info(f"价格: {result['price']} USDT/{token_symbol}")
            logger.info(f"价格影响: {result['price_impact']:.2f}%")
            logger.info(f"输入USD价值: ${result['amount_in_usd']:.4f}")
            logger.info(f"输出USD价值: ${result['amount_out_usd']:.4f}")
            
            return {
                "success": True,
                "price": result["price"],
                "amount_out": result["amount_out"],
                "amount_in": amount,
                "timestamp": time.time(),
                "details": {
                    "success": True,
                    "price_impact": result["price_impact"],
                    "amount_in_usd": result["amount_in_usd"],
                    "amount_out_usd": result["amount_out_usd"],
                    "gas_usd": result["route_summary"]["gasUsd"]
                }
            }
            
        except Exception as e:
            logger.error(f"获取 {token_symbol} 在DEX上的价格时发生异常: {str(e)}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_cex_price(self, token: Dict) -> Dict:
        """
        获取代币在Gate.io上的价格（订单簿）
        
        Args:
            token: 代币信息字典，包含name, symbol, address字段
            
        Returns:
            Dict: 包含CEX价格信息的字典
        """
        symbol = token["symbol"]
        trading_pair = f"{symbol}/USDT"
        
        logger.info(f"获取 {trading_pair} 在Gate.io上的价格...")
        
        try:
            # 获取订单簿数据
            order_book = get_order_book(trading_pair, depth=5)
            
            if "error" in order_book:
                logger.error(f"获取 {trading_pair} 订单簿失败: {order_book['error']}")
                return {
                    "success": False,
                    "error": order_book["error"],
                    "timestamp": time.time()
                }
            
            # 提取买一价和卖一价
            bid_price = order_book["bids"][0]["price"] if order_book["bids"] else 0
            ask_price = order_book["asks"][0]["price"] if order_book["asks"] else 0
            
            # 计算价差
            spread = ask_price - bid_price
            spread_percentage = (spread / bid_price * 100) if bid_price > 0 else 0
            
            logger.info(f"成功获取 {trading_pair} 在Gate.io上的价格: 买一 {bid_price} USDT, 卖一 {ask_price} USDT")
            
            return {
                "success": True,
                "bid_price": bid_price,  # 买一价（可以卖出的最高价）
                "ask_price": ask_price,  # 卖一价（可以买入的最低价）
                "spread": spread,
                "spread_percentage": spread_percentage,
                "timestamp": time.time(),
                "order_book": order_book
            }
        except Exception as e:
            logger.error(f"获取 {trading_pair} 在Gate.io上的价格时发生异常: {str(e)}")
            
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def process_token_prices(self, token: Dict) -> Dict:
        """
        并行处理单个代币的CEX和DEX价格
        
        Args:
            token: 代币信息字典
            
        Returns:
            Dict: 包含代币信息和价格比较结果的字典
        """
        token_symbol = token["symbol"]
        token_name = token["name"]
        token_address = token["address"]
        
        logger.info(f"开始处理代币 {token_symbol}...")
        result = {
            "token": token,
            "timestamp": datetime.now().isoformat(),
            "cex_price": None,
            "dex_price": None,
            "arbitrage_opportunity": None
        }
        
        # 使用线程池并行获取CEX和DEX价格
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.token_threads) as executor:
            # 提交任务
            cex_future = executor.submit(self.get_cex_price, token)
            
            # 使用异步线程获取DEX价格
            dex_future_thread = threading.Thread(
                target=lambda: asyncio.run(self.get_dex_price_wrapper(token, result)),
                daemon=True
            )
            dex_future_thread.start()
            
            # 等待CEX价格任务完成
            cex_result = cex_future.result()
            result["cex_price"] = cex_result
            
            # 等待DEX价格任务完成
            dex_future_thread.join()
            
        # 如果两个价格都获取成功，则比较价格差异
        if (result["cex_price"] and result["cex_price"].get("success") and 
            result["dex_price"] and result["dex_price"].get("success")):
            
            # 提取CEX和DEX价格
            cex_bid_price = result["cex_price"]["bid_price"]  # Gate.io上可以卖出的最高价
            cex_ask_price = result["cex_price"]["ask_price"]  # Gate.io上可以买入的最低价
            dex_price = result["dex_price"]["price"]  # DEX上每个代币的USDT价格
            
            # 计算潜在套利机会
            arb_opportunity = None
            
            # 情况1: CEX买一价格高于DEX价格 => DEX买入，CEX卖出
            if cex_bid_price > dex_price and cex_bid_price > 0 and dex_price > 0:
                price_diff = cex_bid_price - dex_price
                price_diff_pct = (price_diff / dex_price) * 100
                
                arb_opportunity = {
                    "type": "DEX_BUY_CEX_SELL",
                    "description": "在DEX买入并在Gate.io卖出",
                    "price_diff": price_diff,
                    "price_diff_percentage": price_diff_pct,
                    "profitable": price_diff_pct > 1.0  # 如果价差大于1%，标记为有利可图
                }
            
            # 情况2: CEX卖一价格低于DEX价格 => CEX买入，DEX卖出
            elif cex_ask_price < dex_price and cex_ask_price > 0 and dex_price > 0:
                price_diff = dex_price - cex_ask_price
                price_diff_pct = (price_diff / cex_ask_price) * 100
                
                arb_opportunity = {
                    "type": "CEX_BUY_DEX_SELL",
                    "description": "在Gate.io买入并在DEX卖出",
                    "price_diff": price_diff,
                    "price_diff_percentage": price_diff_pct,
                    "profitable": price_diff_pct > 1.0  # 如果价差大于1%，标记为有利可图
                }
            
            # 没有显著的套利机会
            else:
                arb_opportunity = {
                    "type": "NO_OPPORTUNITY",
                    "description": "无明显套利机会",
                    "cex_bid_price": cex_bid_price,
                    "cex_ask_price": cex_ask_price,
                    "dex_price": dex_price,
                    "profitable": False
                }
            
            result["arbitrage_opportunity"] = arb_opportunity
            
            # 记录套利机会
            if arb_opportunity and arb_opportunity["type"] != "NO_OPPORTUNITY":
                logger.info(f"发现 {token_symbol} 的套利机会: {arb_opportunity['type']}, "
                          f"价差百分比: {arb_opportunity.get('price_diff_percentage', 0):.2f}%")
        else:
            # 如果任一价格获取失败，设置无套利机会
            result["arbitrage_opportunity"] = {
                "type": "NO_OPPORTUNITY",
                "description": "价格获取失败",
                "profitable": False
            }
        
        logger.info(f"完成代币 {token_symbol} 的处理")
        return result
    
    async def get_dex_price_wrapper(self, token: Dict, result_dict: Dict) -> None:
        """
        DEX价格获取的异步包装函数
        
        Args:
            token: 代币信息字典
            result_dict: 用于存储结果的字典
        """
        dex_result = await self.get_dex_price(token)
        result_dict["dex_price"] = dex_result

class DetailedArbitrageAnalysis:
    """详细套利分析类，用于深入分析具体的套利机会"""
    
    def __init__(self):
        """初始化详细套利分析器"""
        self.usdt_amounts = [5, 20, 80]
        self.min_profit = 0.1
        self.usdt_address = "0xc2132d05d31c914a87c6611c10748aeb04b58e8f"
        self.chain = "polygon"

    def get_dex_price_threaded(self, token: Dict, amount: float) -> Dict:
        """在独立线程中执行get_dex_price"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # 根据交易方向设置输入输出token
            return loop.run_until_complete(get_dex_price(
                chain=self.chain,
                token_in=token["address"],  # 输入是token
                token_out=self.usdt_address,  # 输出是USDT
                amount=amount
            ))
        finally:
            loop.close()

    async def analyze_dex_buy_cex_sell(self, token: Dict) -> Dict:
        result = {
            "token": token,
            "direction": "DEX_BUY_CEX_SELL",
            "opportunities": []
        }
        
        try:
            # 1. 获取CEX订单簿
            order_book = self.get_cex_order_book(token)
            if not order_book or "bids" not in order_book:
                return result

            # 2. 创建线程池并发执行DEX请求
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for usdt_amount in self.usdt_amounts:
                    future = executor.submit(self.get_dex_price_threaded, token, usdt_amount)
                    futures.append((usdt_amount, future))

                # 3. 处理所有DEX请求结果
                for usdt_amount, future in futures:
                    try:
                        dex_result = future.result()
                        if not dex_result["success"]:
                            continue
                            
                        tokens_received = dex_result["amount_out"]
                        
                        # 计算在CEX能卖出的USDT金额
                        total_output = 0
                        remaining_tokens = tokens_received
                        
                        for bid in order_book["bids"]:
                            price = float(bid["price"])
                            available = float(bid["amount"])
                            
                            if remaining_tokens <= 0:
                                break
                                
                            can_sell = min(remaining_tokens, available)
                            total_output += can_sell * price
                            remaining_tokens -= can_sell
                            
                            if remaining_tokens <= 0:
                                break
                        
                        # 计算利润
                        profit = total_output - usdt_amount
                        
                        if profit > self.min_profit:
                            result["opportunities"].append({
                                "usdt_investment": usdt_amount,
                                "token_amount": tokens_received,
                                "cex_output": total_output,
                                "profit": profit,
                                "profit_percentage": (profit / usdt_amount) * 100,
                                "price_impact": dex_result["price_impact"],
                                "gas_cost_usd": dex_result["route_summary"]["gasUsd"]
                            })
                    except Exception as e:
                        logger.error(f"处理DEX结果时出错: {str(e)}")
                    
        except Exception as e:
            logger.error(f"分析DEX买入CEX卖出方向时出错: {str(e)}")
            result["error"] = str(e)
            
        return result

    async def analyze_cex_buy_dex_sell(self, token: Dict) -> Dict:
        result = {
            "token": token,
            "direction": "CEX_BUY_DEX_SELL",
            "opportunities": []
        }
        
        try:
            # 1. 获取CEX订单簿
            order_book = self.get_cex_order_book(token)
            if not order_book or "asks" not in order_book:
                return result

            # 2. 创建线程池并发执行DEX请求
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for usdt_amount in self.usdt_amounts:
                    # 计算在CEX能买到的代币数量
                    tokens_to_buy = 0
                    total_cost = 0
                    
                    for ask in order_book["asks"]:
                        price = float(ask["price"])
                        available = float(ask["amount"])
                        
                        if total_cost >= usdt_amount:
                            break
                            
                        can_buy = min(available, (usdt_amount - total_cost) / price)
                        tokens_to_buy += can_buy
                        total_cost += can_buy * price
                        
                        if total_cost >= usdt_amount:
                            break
                    
                    if tokens_to_buy <= 0:
                        continue

                    # 修改这里：现在用tokens_to_buy作为输入，交易方向改为token到USDT
                    future = executor.submit(
                        self.get_dex_price_threaded, 
                        {
                            **token,
                            "address": token["address"]  # 确保token地址正确
                        }, 
                        tokens_to_buy
                    )
                    futures.append((usdt_amount, total_cost, tokens_to_buy, future))

                # 3. 处理所有DEX请求结果
                for usdt_amount, total_cost, tokens_to_buy, future in futures:
                    try:
                        dex_result = future.result()
                        if not dex_result["success"]:
                            continue
                        
                        # 计算利润
                        dex_output = dex_result["amount_out"]  # 这是USDT数量
                        profit = dex_output - total_cost
                        
                        if profit > self.min_profit:
                            result["opportunities"].append({
                                "usdt_investment": total_cost,
                                "token_amount": tokens_to_buy,
                                "dex_output": dex_output,
                                "profit": profit,
                                "profit_percentage": (profit / total_cost) * 100,
                                "price_impact": dex_result["price_impact"],
                                "gas_cost_usd": dex_result["route_summary"]["gasUsd"]
                            })
                    except Exception as e:
                        logger.error(f"处理DEX结果时出错: {str(e)}")
                    
        except Exception as e:
            logger.error(f"分析CEX买入DEX卖出方向时出错: {str(e)}")
            result["error"] = str(e)
            
        return result

    def get_cex_order_book(self, token: Dict) -> Dict:
        """
        获取CEX订单簿数据
        
        Args:
            token: 代币信息
            
        Returns:
            Dict: 订单簿数据
        """
        try:
            return get_order_book(f"{token['symbol']}/USDT", depth=10)
        except Exception as e:
            logger.error(f"获取订单簿数据时出错: {str(e)}")
            return None

    async def analyze_token(self, token: Dict, direction: str) -> Dict:
        """
        根据指定方向分析代币的套利机会
        
        Args:
            token: 代币信息
            direction: 套利方向 ("CEX_BUY_DEX_SELL" 或 "DEX_BUY_CEX_SELL")
            
        Returns:
            Dict: 分析结果
        """
        if direction == "CEX_BUY_DEX_SELL":
            return await self.analyze_cex_buy_dex_sell(token)
        elif direction == "DEX_BUY_CEX_SELL":
            return await self.analyze_dex_buy_cex_sell(token)
        else:
            return {
                "token": token,
                "direction": direction,
                "error": "不支持的套利方向",
                "opportunities": []
            }

def load_tokens() -> List[Dict]:
    """
    加载双向可路由的代币列表
    
    Returns:
        List[Dict]: 代币列表
    """
    try:
        with open(TOKEN_DATA_PATH, 'r', encoding='utf-8') as f:
            token_data = json.load(f)
            
        tokens = token_data.get("tokens", [])
        logger.info(f"成功加载 {len(tokens)} 个代币")
        return tokens
    except Exception as e:
        logger.error(f"加载代币数据失败: {str(e)}")
        return []

def process_single_token(token: Dict, token_threads: int) -> Tuple[Dict, Optional[Dict]]:
    """
    处理单个代币的完整分析流程
    
    Args:
        token: 代币信息
        token_threads: 每个代币的CEX/DEX价格获取线程数
        
    Returns:
        Tuple[Dict, Optional[Dict]]: (初步分析结果, 详细分析结果)
    """
    try:
        # 初始化分析器
        discoverer = PotentialArbitrageDiscovery(token_threads=token_threads)
        analyzer = DetailedArbitrageAnalysis()
        
        logger.info(f"\n开始分析代币 {token['symbol']}...")
        
        # 1. 初步分析
        initial_result = discoverer.process_token_prices(token)
        
        # 2. 检查是否满足详细分析条件
        detailed_result = None
        if initial_result and "arbitrage_opportunity" in initial_result:
            arb = initial_result.get("arbitrage_opportunity")
            if (arb and arb.get("profitable", False) and 
                isinstance(arb.get("price_diff_percentage"), (int, float)) and  # 确保价差是数值类型
                arb.get("price_diff_percentage", 0) > 1.0):  # 价差大于1%
                
                logger.info(f"代币 {token['symbol']} 发现初步套利机会:")
                logger.info(f"  方向: {arb['type']}")
                logger.info(f"  价差: {str(arb['price_diff_percentage'])}%")
                
                # 3. 进行详细分析
                try:
                    detailed_result = asyncio.run(analyzer.analyze_token(
                        token,
                        arb["type"]
                    ))
                    
                    # 只保存有具体套利机会（利润大于0.1 USDT）的结果
                    if detailed_result.get("opportunities"):
                        logger.info(f"发现 {len(detailed_result['opportunities'])} 个具体套利机会:")
                        for opportunity in detailed_result["opportunities"]:
                            # 确保所有数值都转换为字符串进行格式化
                            logger.info(f"  投资: {str(opportunity['usdt_investment'])} USDT")
                            logger.info(f"  利润: {str(opportunity['profit'])} USDT ({str(opportunity['profit_percentage'])}%)")
                            logger.info(f"  Gas成本: ${str(opportunity['gas_cost_usd'])}")
                    else:
                        logger.info("详细分析后未发现足够利润的套利机会")
                        detailed_result = None
                        
                except Exception as e:
                    logger.error(f"详细分析 {token['symbol']} 时出错: {str(e)}")
                    detailed_result = None
            else:
                logger.info(f"代币 {token['symbol']} 未发现显著套利机会")
        else:
            logger.info(f"代币 {token['symbol']} 初步分析未返回有效结果")
            
        logger.info(f"完成代币 {token['symbol']} 的分析\n")
        return initial_result, detailed_result
        
    except Exception as e:
        logger.error(f"处理代币 {token['symbol']} 时发生异常: {str(e)}")
        return {
            "token": token,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }, None

def process_tokens(tokens: List[Dict], num_threads: int, token_threads: int, max_tokens: Optional[int] = None) -> Tuple[List[Dict], List[Dict]]:
    """
    多线程处理代币列表，每个代币进行完整的分析流程
    
    Args:
        tokens: 代币列表
        num_threads: 处理代币的线程数
        token_threads: 每个代币的CEX/DEX价格获取线程数
        max_tokens: 最大处理代币数量，None表示处理所有代币
        
    Returns:
        Tuple[List[Dict], List[Dict]]: (初步分析结果, 详细分析结果)
    """
    # 限制处理的代币数量（如果指定）
    if max_tokens is not None and max_tokens > 0:
        tokens = tokens[:max_tokens]
    
    # 初始化结果列表
    initial_results = []
    detailed_results = []
    
    logger.info(f"开始分析 {len(tokens)} 个代币，使用 {num_threads} 个线程...")
    
    # 使用线程池并行处理代币
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有任务
        future_to_token = {
            executor.submit(process_single_token, token, token_threads): token 
            for token in tokens
        }
        
        # 处理完成的任务
        for future in concurrent.futures.as_completed(future_to_token):
            token = future_to_token[future]
            try:
                initial_result, detailed_result = future.result()
                initial_results.append(initial_result)
                if detailed_result:
                    detailed_results.append(detailed_result)
            except Exception as e:
                logger.error(f"获取代币 {token['symbol']} 的处理结果时出错: {str(e)}")
    
    # 输出总结
    total_opportunities = sum(len(result.get("opportunities", [])) for result in detailed_results)
    logger.info(f"\n分析总结:")
    logger.info(f"处理完成 {len(tokens)} 个代币")
    logger.info(f"发现 {len(detailed_results)} 个代币存在套利机会")
    logger.info(f"共计 {total_opportunities} 个具体套利机会")
    
    return initial_results, detailed_results

def manage_file_size(file_path: str, data: Dict) -> None:
    """
    管理文件大小，如果文件大于阈值则清理一半的数据
    
    Args:
        file_path: 文件路径
        data: 要保存的数据
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    
    # 如果文件大小超过阈值，清理数据
    if file_size > MAX_FILE_SIZE_BYTES:
        logger.info(f"文件大小({file_size/1024/1024:.2f}MB)超过阈值({MAX_FILE_SIZE_MB}MB)，进行清理...")
        
        try:
            # 读取现有数据
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
            
            # 获取现有结果
            existing_results = existing_data.get("results", [])
            
            # 只保留最新的一半数据
            if existing_results:
                half_count = len(existing_results) // 2
                existing_results = existing_results[half_count:]
                
                # 更新数据并重新保存
                existing_data["results"] = existing_results
                
                # 更新摘要
                existing_data["summary"].update({
                    "total_tokens": len(existing_results),
                    "tokens_with_opportunity": sum(1 for r in existing_results if r.get("arbitrage_opportunity") and r["arbitrage_opportunity"]["type"] != "NO_OPPORTUNITY"),
                    "profitable_opportunities": sum(1 for r in existing_results if r.get("arbitrage_opportunity") and r["arbitrage_opportunity"].get("profitable", False)),
                    "timestamp": datetime.now().isoformat(),
                    "cleaned": True
                })
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"已清理旧数据，保留了{len(existing_results)}条记录")
        except Exception as e:
            logger.error(f"清理文件数据时出错: {str(e)}")

def save_results(initial_results: List[Dict], detailed_results: List[Dict], output_file: str) -> None:
    """
    保存处理结果到文件
    
    Args:
        initial_results: 初步分析结果列表
        detailed_results: 详细分析结果列表
        output_file: 输出文件路径
    """
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 添加摘要信息
    summary = {
        "total_tokens": len(initial_results),
        "tokens_with_opportunity": sum(1 for r in initial_results if r.get("arbitrage_opportunity") and r["arbitrage_opportunity"]["type"] != "NO_OPPORTUNITY"),
        "profitable_opportunities": sum(1 for r in initial_results if r.get("arbitrage_opportunity") and r["arbitrage_opportunity"].get("profitable", False)),
        "detailed_opportunities": len(detailed_results),
        "timestamp": datetime.now().isoformat()
    }
    
    # 首先检查文件大小，如果太大则清理
    manage_file_size(output_file, {
        "summary": summary,
        "initial_results": initial_results,
        "detailed_results": detailed_results
    })
    
    # 准备要保存的数据
    output_data = {
        "summary": summary,
        "initial_results": initial_results,
        "detailed_results": detailed_results
    }
    
    # 保存到文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        logger.info(f"结果已保存到 {output_file}")
    except Exception as e:
        logger.error(f"保存结果失败: {str(e)}")

def main():
    """主函数"""
    start_time = time.time()  # 添加开始时间
    
    parser = argparse.ArgumentParser(description="Gate.io与DEX价格比较分析工具")
    parser.add_argument("--threads", type=int, default=4, help="处理代币的线程数，默认为4")
    parser.add_argument("--token-threads", type=int, default=4, help="每个代币的CEX/DEX价格获取线程数，默认为4")
    parser.add_argument("--max-tokens", type=int, help="最大处理代币数量，默认处理所有代币")
    parser.add_argument("--output-file", default=DEFAULT_OUTPUT_FILE,
                      help=f"输出文件路径，默认为{DEFAULT_OUTPUT_FILE}")
    # 添加单独处理代币的参数
    parser.add_argument("--symbol", type=str, help="指定要分析的代币符号，例如: 'USDC'")
    
    args = parser.parse_args()
    
    # 加载代币
    tokens = load_tokens()
    if not tokens:
        logger.error("未能加载任何代币，程序退出")
        return
    
    # 如果指定了代币符号，只处理该代币
    if args.symbol:
        target_token = None
        for token in tokens:
            if token["symbol"].upper() == args.symbol.upper():
                target_token = token
                break
        
        if target_token:
            logger.info(f"开始分析指定代币: {args.symbol}")
            initial_result, detailed_result = process_single_token(target_token, args.token_threads)
            
            # 转换为列表以保持与原有保存逻辑兼容
            initial_results = [initial_result] if initial_result else []
            detailed_results = [detailed_result] if detailed_result else []
            
            # 输出详细结果
            if detailed_result and detailed_result.get("opportunities"):
                logger.info(f"\n发现 {len(detailed_result['opportunities'])} 个具体套利机会:")
                for opp in detailed_result["opportunities"]:
                    logger.info(f"  投资: {opp['usdt_investment']} USDT")
                    logger.info(f"  利润: {opp['profit']} USDT ({opp['profit_percentage']}%)")
                    logger.info(f"  Gas成本: ${opp['gas_cost_usd']}")
        else:
            logger.error(f"未找到符号为 {args.symbol} 的代币")
            return
    else:
        # 处理所有代币
        initial_results, detailed_results = process_tokens(
            tokens,
            args.threads,
            args.token_threads,
            args.max_tokens
        )
    
    # 保存结果
    save_results(initial_results, detailed_results, args.output_file)
    
    # 计算总运行时间
    end_time = time.time()
    run_time = end_time - start_time
    hours = int(run_time // 3600)
    minutes = int((run_time % 3600) // 60)
    seconds = run_time % 60
    
    # 输出总结
    logger.info(f"\n分析总结:")
    logger.info(f"处理完成 {len(initial_results)} 个代币")
    logger.info(f"发现 {len(detailed_results)} 个代币存在套利机会")
    logger.info(f"总运行时间: {hours}小时 {minutes}分钟 {seconds:.2f}秒")

if __name__ == "__main__":
    main() 