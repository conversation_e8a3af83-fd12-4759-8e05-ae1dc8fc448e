@echo off
chcp 65001 >nul
title V2Ray IP轮换器

echo.
echo ========================================
echo         V2Ray IP轮换器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

:: 检查配置文件
if not exist "config\ip.yaml" (
    echo 错误: 找不到配置文件 config\ip.yaml
    pause
    exit /b 1
)

:: 检查V2Ray是否安装
if not exist "tools\v2ray\v2ray.exe" (
    echo 错误: 未找到V2Ray，正在启动安装脚本...
    call install_v2ray.bat
    if not exist "tools\v2ray\v2ray.exe" (
        echo V2Ray安装失败，请手动安装
        pause
        exit /b 1
    )
)

:: 创建日志目录
if not exist "logs" mkdir logs

echo 正在启动V2Ray IP轮换器...
echo.
echo 功能说明:
echo - 每60秒自动切换一次IP代理
echo - 使用V2Ray客户端实现真正的网络代理
echo - 本地端口: HTTP 7890, SOCKS 7891
echo - 支持香港、台湾、日本、新加坡、美国、韩国等地区节点
echo - 按 Ctrl+C 可以停止轮换
echo.
echo 开始轮换...
echo ========================================

:: 启动V2Ray IP轮换器
python v2ray_ip_rotator.py start 60

echo.
echo V2Ray IP轮换器已停止
pause
