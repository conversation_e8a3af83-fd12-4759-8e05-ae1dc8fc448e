@echo off
REM CCIP Tools Windows Setup Script
REM 为Windows用户设置CCIP工具和网络名称支持

setlocal enabledelayedexpansion

echo 🚀 CCIP Tools Windows Setup
echo ============================

REM 检查Node.js版本
echo 📋 Checking Node.js version...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed.
    echo Please download and install Node.js 18+ from: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js version: %NODE_VERSION%

REM 检查npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm is not available.
    echo Please ensure npm is properly installed with Node.js.
    pause
    exit /b 1
)

echo ✅ npm is available

REM 检查配置文件
echo.
echo 📁 Checking configuration files...
set "CONFIG_PATH=..\..\..\config\rpc.yaml"

if not exist "%CONFIG_PATH%" (
    echo ❌ RPC configuration file not found: %CONFIG_PATH%
    echo.
    echo Please ensure you have the config\rpc.yaml file in your project root.
    echo The file should contain RPC endpoints for different networks.
    echo.
    echo Example structure:
    echo rpc:
    echo   ethereum:
    echo     rpc_url: "https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY"
    echo     backup_rpc_urls:
    echo       - "https://mainnet.infura.io/v3/YOUR_KEY"
    echo.
    pause
    exit /b 1
)

echo ✅ Configuration file found: %CONFIG_PATH%

REM 安装依赖
echo.
echo 📦 Installing dependencies...
if not exist "node_modules" (
    echo Installing npm packages...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Dependencies already installed
)

REM 运行类型检查
echo.
echo 🔍 Running type check...
npm run typecheck
if %errorlevel% neq 0 (
    echo ❌ Type check failed
    pause
    exit /b 1
)

echo ✅ Type check passed

REM 测试网络名称解析
echo.
echo 🧪 Testing network name resolution...
npm run test:networks
if %errorlevel% neq 0 (
    echo ❌ Network name tests failed
    pause
    exit /b 1
)

echo ✅ Network name resolution tests passed

REM 测试RPC整合
echo.
echo 🔗 Testing RPC integration...
npm run test:rpc
if %errorlevel% neq 0 (
    echo ⚠️  RPC integration test failed, but continuing...
    echo This might be due to network connectivity or RPC configuration.
) else (
    echo ✅ RPC integration tests passed
)

REM 创建快捷方式脚本
echo.
echo 🔧 Setting up convenience scripts...

REM 确保批处理文件可执行
if exist "ccip.bat" (
    echo ✅ ccip.bat is ready
) else (
    echo ❌ ccip.bat not found
)

if exist "ccip.ps1" (
    echo ✅ ccip.ps1 is ready
) else (
    echo ❌ ccip.ps1 not found
)

echo.
echo ✨ Setup completed successfully!
echo.
echo 🎉 You can now use CCIP tools with network names:
echo.
echo ==========================================
echo Windows Command Prompt / PowerShell Usage:
echo ==========================================
echo.
echo # List all supported networks
echo ccip.bat list-networks
echo.
echo # Show CCIP message info
echo ccip.bat show 0x1234567890abcdef...
echo.
echo # Send cross-chain message using network names
echo ccip.bat send ethereum 0xRouterAddress polygon --receiver 0xReceiverAddress
echo ccip.bat send sepolia 0xRouterAddress fuji --receiver 0xReceiverAddress
echo.
echo # Get supported tokens for a network
echo ccip.bat getSupportedTokens ethereum
echo ccip.bat getSupportedTokens polygon
echo.
echo # Send message with data
echo ccip.bat send ethereum 0xRouter arbitrum --receiver 0xAddress --data "Hello World"
echo.
echo # Manual execution of pending messages
echo ccip.bat manualExec 0x1234567890abcdef...
echo.
echo ==========================================
echo PowerShell Alternative:
echo ==========================================
echo.
echo # Use PowerShell script instead
echo .\ccip.ps1 list-networks
echo .\ccip.ps1 send ethereum 0xRouter polygon --receiver 0xAddress
echo.
echo ==========================================
echo Network Name Examples:
echo ==========================================
echo.
echo Mainnet networks:
echo   ethereum, eth, mainnet     ^(Ethereum Mainnet^)
echo   polygon, matic             ^(Polygon Mainnet^)
echo   bsc, bnb, binance         ^(BSC Mainnet^)
echo   avalanche, avax           ^(Avalanche Mainnet^)
echo   arbitrum, arb             ^(Arbitrum Mainnet^)
echo   optimism, op              ^(Optimism Mainnet^)
echo   base                      ^(Base Mainnet^)
echo.
echo Testnet networks:
echo   sepolia, eth-sepolia      ^(Ethereum Sepolia^)
echo   fuji, avax-fuji           ^(Avalanche Fuji^)
echo   polygon-amoy, amoy        ^(Polygon Amoy^)
echo   bsc-testnet, bnb-testnet  ^(BSC Testnet^)
echo.
echo ==========================================
echo Getting Help:
echo ==========================================
echo.
echo # Show general help
echo ccip.bat --help
echo.
echo # Show command-specific help
echo ccip.bat send --help
echo.
echo # Check version
echo ccip.bat --version
echo.
echo For detailed documentation, see:
echo - WINDOWS_USAGE.md
echo - RPC_INTEGRATION.md
echo.
echo Happy cross-chaining! 🌉
echo.
pause

endlocal
