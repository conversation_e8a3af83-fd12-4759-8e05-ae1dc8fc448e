import { BaseBigNumber } from "../abstracts";
export declare class EmptyBigNumber extends BaseBigNumber {
    constructor(value: any);
    toString(base?: any): string;
    toNumber(): number;
    add(value: BaseBigNumber): BaseBigNumber;
    sub(value: BaseBigNumber): BaseBigNumber;
    mul(value: BaseBigNumber): BaseBigNumber;
    div(value: BaseBigNumber): BaseBigNumber;
    lte(value: BaseBigNumber): boolean;
    lt(value: BaseBigNumber): boolean;
    gte(value: BaseBigNumber): boolean;
    gt(value: BaseBigNumber): boolean;
    eq(value: BaseBigNumber): boolean;
}
