import { InjectivePermissionsV1Beta1Tx, InjectivePermissionsV1Beta1Params } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgUpdateParams {
    interface Params {
        authority: string;
        params: {
            wasmHookQueryMaxGas: string;
        };
    }
    type Proto = InjectivePermissionsV1Beta1Tx.MsgUpdateParams;
}
/**
 * @category Messages
 */
export default class MsgUpdateParams extends MsgBase<MsgUpdateParams.Params, MsgUpdateParams.Proto> {
    static fromJSON(params: MsgUpdateParams.Params): MsgUpdateParams;
    toProto(): InjectivePermissionsV1Beta1Tx.MsgUpdateParams;
    toData(): {
        authority: string;
        params: InjectivePermissionsV1Beta1Params.Params | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            authority: string;
            params: InjectivePermissionsV1Beta1Params.Params | undefined;
        };
    };
    toWeb3Gw(): {
        authority: string;
        params: InjectivePermissionsV1Beta1Params.Params | undefined;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectivePermissionsV1Beta1Tx.MsgUpdateParams;
    };
    toBinary(): Uint8Array;
}
