import _m0 from "protobufjs/minimal.js";
import { Attestation } from "./attestation";
import { OutgoingTransferTx, OutgoingTxBatch } from "./batch";
import { MsgConfirmBatch, MsgSetOrchestratorAddresses, MsgValsetConfirm } from "./msgs";
import { Params } from "./params";
import { ERC20ToDenom, Valset } from "./types";
export declare const protobufPackage = "injective.peggy.v1";
/** GenesisState struct */
export interface GenesisState {
    params: Params | undefined;
    lastObservedNonce: string;
    valsets: Valset[];
    valsetConfirms: MsgValsetConfirm[];
    batches: OutgoingTxBatch[];
    batchConfirms: MsgConfirmBatch[];
    attestations: Attestation[];
    orchestratorAddresses: MsgSetOrchestratorAddresses[];
    erc20ToDenoms: ERC20ToDenom[];
    unbatchedTransfers: OutgoingTransferTx[];
    lastObservedEthereumHeight: string;
    lastOutgoingBatchId: string;
    lastOutgoingPoolId: string;
    lastObservedValset: Valset | undefined;
    ethereumBlacklist: string[];
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
