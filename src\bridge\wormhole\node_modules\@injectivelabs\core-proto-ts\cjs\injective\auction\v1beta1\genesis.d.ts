import _m0 from "protobufjs/minimal.js";
import { Bid, LastAuctionResult, Params } from "./auction";
export declare const protobufPackage = "injective.auction.v1beta1";
/** GenesisState defines the auction module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to auction. */
    params: Params | undefined;
    /** current auction round */
    auctionRound: string;
    /** current highest bid */
    highestBid: Bid | undefined;
    /** auction ending timestamp */
    auctionEndingTimestamp: string;
    /** last auction result */
    lastAuctionResult: LastAuctionResult | undefined;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
