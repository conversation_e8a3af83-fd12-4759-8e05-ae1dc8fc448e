"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Messages
 */
class MsgUpdateNamespace extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgUpdateNamespace(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace.create();
        message.sender = params.sender;
        message.denom = params.denom;
        message.rolePermissions = params.rolePermissions;
        message.roleManagers = params.roleManagers;
        message.policyStatuses = params.policyStatuses;
        message.policyManagerCapabilities = params.policyManagerCapabilities;
        if (params.contractHook) {
            const contractHook = core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace_SetContractHook.create();
            contractHook.newValue = params.contractHook;
            message.contractHook = contractHook;
        }
        const permissions = params.rolePermissions.map((rolePermission) => {
            const permission = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.Role.create();
            permission.name = rolePermission.name;
            permission.permissions = rolePermission.permissions;
            permission.roleId = rolePermission.roleId;
            return permission;
        }) || [];
        message.rolePermissions = permissions;
        const roleManagers = params.roleManagers.map((roleManager) => {
            const manager = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.RoleManager.create();
            manager.roles = roleManager.roles;
            manager.manager = roleManager.manager;
            return manager;
        }) || [];
        message.roleManagers = roleManagers;
        const policyStatuses = params.policyStatuses.map((policyStatus) => {
            const policy = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.PolicyStatus.create();
            policy.action = policyStatus.action;
            policy.isDisabled = policyStatus.isDisabled;
            policy.isSealed = policyStatus.isSealed;
            return policy;
        }) || [];
        message.policyStatuses = policyStatuses;
        const policyManagerCapabilities = params.policyManagerCapabilities.map((capability) => {
            const policy = core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.PolicyManagerCapability.create();
            policy.action = capability.action;
            policy.canDisable = capability.canDisable;
            policy.canSeal = capability.canSeal;
            policy.manager = capability.manager;
            return policy;
        }) || [];
        message.policyManagerCapabilities = policyManagerCapabilities;
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateNamespace',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = (0, snakecase_keys_1.default)({
            ...proto,
            policyStatuses: proto.policyStatuses || [],
            policyManagerCapabilities: proto.policyManagerCapabilities || [],
        });
        return {
            type: 'permissions/MsgUpdateNamespace',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.permissions.v1beta1.MsgUpdateNamespace',
            ...value,
        };
    }
    toEip712() {
        throw new exceptions_1.GeneralException(new Error('EIP712_v1 is not supported for MsgUpdateNamespace. Please use EIP712_v2'));
    }
    toEip712V2() {
        const web3gw = this.toWeb3Gw();
        const policyStatuses = (web3gw.policy_statuses || []).map((policyStatus) => ({
            ...policyStatus,
            action: core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.actionToJSON(policyStatus.action),
        }));
        const policyManagerCapabilities = (web3gw.policy_manager_capabilities || []).map((policyManagerCapability) => ({
            ...policyManagerCapability,
            action: core_proto_ts_1.InjectivePermissionsV1Beta1Permissions.actionToJSON(policyManagerCapability.action),
        }));
        const messageAdjusted = {
            ...web3gw,
            policy_statuses: policyStatuses,
            policy_manager_capabilities: policyManagerCapabilities,
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.permissions.v1beta1.MsgUpdateNamespace',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectivePermissionsV1Beta1Tx.MsgUpdateNamespace.encode(this.toProto()).finish();
    }
}
exports.default = MsgUpdateNamespace;
