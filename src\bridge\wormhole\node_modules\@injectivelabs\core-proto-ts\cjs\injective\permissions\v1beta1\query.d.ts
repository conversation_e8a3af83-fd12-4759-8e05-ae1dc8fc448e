import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { GenesisState } from "./genesis";
import { Params } from "./params";
import { AddressVoucher, Namespace, PolicyManagerCapability, PolicyStatus, RoleManager } from "./permissions";
export declare const protobufPackage = "injective.permissions.v1beta1";
/** QueryParamsRequest is the request type for the Query/Params RPC method. */
export interface QueryParamsRequest {
}
/** QueryParamsResponse is the response type for the Query/Params RPC method. */
export interface QueryParamsResponse {
    /** params defines the parameters of the module. */
    params: Params | undefined;
}
/**
 * QueryNamespaceDenomsRequest is the request type for the Query/NamespaceDenoms RPC
 * method.
 */
export interface QueryNamespaceDenomsRequest {
}
/**
 * QueryNamespaceDenomsResponse is the response type for the Query/NamespaceDenoms
 * RPC method.
 */
export interface QueryNamespaceDenomsResponse {
    denoms: string[];
}
/**
 * QueryNamespacesRequest is the request type for the Query/Namespaces RPC
 * method.
 */
export interface QueryNamespacesRequest {
}
/**
 * QueryNamespacesResponse is the response type for the Query/Namespaces
 * RPC method.
 */
export interface QueryNamespacesResponse {
    namespaces: Namespace[];
}
/**
 * QueryNamespaceRequest is the request type for the
 * Query/Namespace RPC method.
 */
export interface QueryNamespaceRequest {
    denom: string;
}
/**
 * QueryNamespaceResponse is the response type for the
 * Query/NamespaceByDenom RPC method.
 */
export interface QueryNamespaceResponse {
    namespace: Namespace | undefined;
}
/**
 * QueryAddressesByRoleRequest is the request type for the Query/AddressesByRole
 * RPC method.
 */
export interface QueryActorsByRoleRequest {
    denom: string;
    role: string;
}
/**
 * QueryAddressesByRoleResponse is the response type for the
 * Query/AddressesByRole RPC method.
 */
export interface QueryActorsByRoleResponse {
    actors: string[];
}
/**
 * QueryRolesByActorRequest is the request type for the
 * Query/RolesByActor RPC method.
 */
export interface QueryRolesByActorRequest {
    denom: string;
    actor: string;
}
/**
 * QueryRolesByActorResponse is the response type for the
 * Query/RolesByActor RPC method.
 */
export interface QueryRolesByActorResponse {
    roles: string[];
}
/**
 * QueryRoleManagersRequest is the request type for the Query/RoleManagers
 * RPC method.
 */
export interface QueryRoleManagersRequest {
    denom: string;
}
/**
 * QueryRoleManagersResponse is the response type for the
 * Query/RoleManagers RPC method.
 */
export interface QueryRoleManagersResponse {
    roleManagers: RoleManager[];
}
/**
 * QueryRoleManagerRequest is the request type for the Query/RoleManager
 * RPC method.
 */
export interface QueryRoleManagerRequest {
    denom: string;
    manager: string;
}
/**
 * QueryRoleManagerResponse is the response type for the
 * Query/RoleManager RPC method.
 */
export interface QueryRoleManagerResponse {
    roleManager: RoleManager | undefined;
}
/**
 * QueryPolicyStatusesRequest is the request type for the Query/PolicyStatuses
 * RPC method.
 */
export interface QueryPolicyStatusesRequest {
    denom: string;
}
/**
 * QueryRoleManagerResponse is the response type for the
 * Query/RoleManager RPC method.
 */
export interface QueryPolicyStatusesResponse {
    policyStatuses: PolicyStatus[];
}
/**
 * QueryPolicyManagerCapabilitiesRequest is the request type for the Query/PolicyManagerCapabilities
 * RPC method.
 */
export interface QueryPolicyManagerCapabilitiesRequest {
    denom: string;
}
/**
 * QueryPolicyManagerCapabilitiesResponse is the response type for the
 * Query/PolicyManagerCapabilities RPC method.
 */
export interface QueryPolicyManagerCapabilitiesResponse {
    policyManagerCapabilities: PolicyManagerCapability[];
}
export interface QueryVouchersRequest {
    denom: string;
}
export interface QueryVouchersResponse {
    vouchers: AddressVoucher[];
}
export interface QueryVoucherRequest {
    denom: string;
    address: string;
}
export interface QueryVoucherResponse {
    voucher: Coin | undefined;
}
/**
 * QueryModuleStateRequest is the request type for the Query/PermissionsModuleState
 * RPC method.
 */
export interface QueryModuleStateRequest {
}
/**
 * QueryModuleStateResponse is the response type for the Query/PermissionsModuleState
 * RPC method.
 */
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
export declare const QueryParamsRequest: {
    encode(_: QueryParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsRequest;
    fromJSON(_: any): QueryParamsRequest;
    toJSON(_: QueryParamsRequest): unknown;
    create(base?: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
    fromPartial(_: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
};
export declare const QueryParamsResponse: {
    encode(message: QueryParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsResponse;
    fromJSON(object: any): QueryParamsResponse;
    toJSON(message: QueryParamsResponse): unknown;
    create(base?: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
    fromPartial(object: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
};
export declare const QueryNamespaceDenomsRequest: {
    encode(_: QueryNamespaceDenomsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespaceDenomsRequest;
    fromJSON(_: any): QueryNamespaceDenomsRequest;
    toJSON(_: QueryNamespaceDenomsRequest): unknown;
    create(base?: DeepPartial<QueryNamespaceDenomsRequest>): QueryNamespaceDenomsRequest;
    fromPartial(_: DeepPartial<QueryNamespaceDenomsRequest>): QueryNamespaceDenomsRequest;
};
export declare const QueryNamespaceDenomsResponse: {
    encode(message: QueryNamespaceDenomsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespaceDenomsResponse;
    fromJSON(object: any): QueryNamespaceDenomsResponse;
    toJSON(message: QueryNamespaceDenomsResponse): unknown;
    create(base?: DeepPartial<QueryNamespaceDenomsResponse>): QueryNamespaceDenomsResponse;
    fromPartial(object: DeepPartial<QueryNamespaceDenomsResponse>): QueryNamespaceDenomsResponse;
};
export declare const QueryNamespacesRequest: {
    encode(_: QueryNamespacesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespacesRequest;
    fromJSON(_: any): QueryNamespacesRequest;
    toJSON(_: QueryNamespacesRequest): unknown;
    create(base?: DeepPartial<QueryNamespacesRequest>): QueryNamespacesRequest;
    fromPartial(_: DeepPartial<QueryNamespacesRequest>): QueryNamespacesRequest;
};
export declare const QueryNamespacesResponse: {
    encode(message: QueryNamespacesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespacesResponse;
    fromJSON(object: any): QueryNamespacesResponse;
    toJSON(message: QueryNamespacesResponse): unknown;
    create(base?: DeepPartial<QueryNamespacesResponse>): QueryNamespacesResponse;
    fromPartial(object: DeepPartial<QueryNamespacesResponse>): QueryNamespacesResponse;
};
export declare const QueryNamespaceRequest: {
    encode(message: QueryNamespaceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespaceRequest;
    fromJSON(object: any): QueryNamespaceRequest;
    toJSON(message: QueryNamespaceRequest): unknown;
    create(base?: DeepPartial<QueryNamespaceRequest>): QueryNamespaceRequest;
    fromPartial(object: DeepPartial<QueryNamespaceRequest>): QueryNamespaceRequest;
};
export declare const QueryNamespaceResponse: {
    encode(message: QueryNamespaceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryNamespaceResponse;
    fromJSON(object: any): QueryNamespaceResponse;
    toJSON(message: QueryNamespaceResponse): unknown;
    create(base?: DeepPartial<QueryNamespaceResponse>): QueryNamespaceResponse;
    fromPartial(object: DeepPartial<QueryNamespaceResponse>): QueryNamespaceResponse;
};
export declare const QueryActorsByRoleRequest: {
    encode(message: QueryActorsByRoleRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryActorsByRoleRequest;
    fromJSON(object: any): QueryActorsByRoleRequest;
    toJSON(message: QueryActorsByRoleRequest): unknown;
    create(base?: DeepPartial<QueryActorsByRoleRequest>): QueryActorsByRoleRequest;
    fromPartial(object: DeepPartial<QueryActorsByRoleRequest>): QueryActorsByRoleRequest;
};
export declare const QueryActorsByRoleResponse: {
    encode(message: QueryActorsByRoleResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryActorsByRoleResponse;
    fromJSON(object: any): QueryActorsByRoleResponse;
    toJSON(message: QueryActorsByRoleResponse): unknown;
    create(base?: DeepPartial<QueryActorsByRoleResponse>): QueryActorsByRoleResponse;
    fromPartial(object: DeepPartial<QueryActorsByRoleResponse>): QueryActorsByRoleResponse;
};
export declare const QueryRolesByActorRequest: {
    encode(message: QueryRolesByActorRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRolesByActorRequest;
    fromJSON(object: any): QueryRolesByActorRequest;
    toJSON(message: QueryRolesByActorRequest): unknown;
    create(base?: DeepPartial<QueryRolesByActorRequest>): QueryRolesByActorRequest;
    fromPartial(object: DeepPartial<QueryRolesByActorRequest>): QueryRolesByActorRequest;
};
export declare const QueryRolesByActorResponse: {
    encode(message: QueryRolesByActorResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRolesByActorResponse;
    fromJSON(object: any): QueryRolesByActorResponse;
    toJSON(message: QueryRolesByActorResponse): unknown;
    create(base?: DeepPartial<QueryRolesByActorResponse>): QueryRolesByActorResponse;
    fromPartial(object: DeepPartial<QueryRolesByActorResponse>): QueryRolesByActorResponse;
};
export declare const QueryRoleManagersRequest: {
    encode(message: QueryRoleManagersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRoleManagersRequest;
    fromJSON(object: any): QueryRoleManagersRequest;
    toJSON(message: QueryRoleManagersRequest): unknown;
    create(base?: DeepPartial<QueryRoleManagersRequest>): QueryRoleManagersRequest;
    fromPartial(object: DeepPartial<QueryRoleManagersRequest>): QueryRoleManagersRequest;
};
export declare const QueryRoleManagersResponse: {
    encode(message: QueryRoleManagersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRoleManagersResponse;
    fromJSON(object: any): QueryRoleManagersResponse;
    toJSON(message: QueryRoleManagersResponse): unknown;
    create(base?: DeepPartial<QueryRoleManagersResponse>): QueryRoleManagersResponse;
    fromPartial(object: DeepPartial<QueryRoleManagersResponse>): QueryRoleManagersResponse;
};
export declare const QueryRoleManagerRequest: {
    encode(message: QueryRoleManagerRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRoleManagerRequest;
    fromJSON(object: any): QueryRoleManagerRequest;
    toJSON(message: QueryRoleManagerRequest): unknown;
    create(base?: DeepPartial<QueryRoleManagerRequest>): QueryRoleManagerRequest;
    fromPartial(object: DeepPartial<QueryRoleManagerRequest>): QueryRoleManagerRequest;
};
export declare const QueryRoleManagerResponse: {
    encode(message: QueryRoleManagerResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryRoleManagerResponse;
    fromJSON(object: any): QueryRoleManagerResponse;
    toJSON(message: QueryRoleManagerResponse): unknown;
    create(base?: DeepPartial<QueryRoleManagerResponse>): QueryRoleManagerResponse;
    fromPartial(object: DeepPartial<QueryRoleManagerResponse>): QueryRoleManagerResponse;
};
export declare const QueryPolicyStatusesRequest: {
    encode(message: QueryPolicyStatusesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPolicyStatusesRequest;
    fromJSON(object: any): QueryPolicyStatusesRequest;
    toJSON(message: QueryPolicyStatusesRequest): unknown;
    create(base?: DeepPartial<QueryPolicyStatusesRequest>): QueryPolicyStatusesRequest;
    fromPartial(object: DeepPartial<QueryPolicyStatusesRequest>): QueryPolicyStatusesRequest;
};
export declare const QueryPolicyStatusesResponse: {
    encode(message: QueryPolicyStatusesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPolicyStatusesResponse;
    fromJSON(object: any): QueryPolicyStatusesResponse;
    toJSON(message: QueryPolicyStatusesResponse): unknown;
    create(base?: DeepPartial<QueryPolicyStatusesResponse>): QueryPolicyStatusesResponse;
    fromPartial(object: DeepPartial<QueryPolicyStatusesResponse>): QueryPolicyStatusesResponse;
};
export declare const QueryPolicyManagerCapabilitiesRequest: {
    encode(message: QueryPolicyManagerCapabilitiesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPolicyManagerCapabilitiesRequest;
    fromJSON(object: any): QueryPolicyManagerCapabilitiesRequest;
    toJSON(message: QueryPolicyManagerCapabilitiesRequest): unknown;
    create(base?: DeepPartial<QueryPolicyManagerCapabilitiesRequest>): QueryPolicyManagerCapabilitiesRequest;
    fromPartial(object: DeepPartial<QueryPolicyManagerCapabilitiesRequest>): QueryPolicyManagerCapabilitiesRequest;
};
export declare const QueryPolicyManagerCapabilitiesResponse: {
    encode(message: QueryPolicyManagerCapabilitiesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPolicyManagerCapabilitiesResponse;
    fromJSON(object: any): QueryPolicyManagerCapabilitiesResponse;
    toJSON(message: QueryPolicyManagerCapabilitiesResponse): unknown;
    create(base?: DeepPartial<QueryPolicyManagerCapabilitiesResponse>): QueryPolicyManagerCapabilitiesResponse;
    fromPartial(object: DeepPartial<QueryPolicyManagerCapabilitiesResponse>): QueryPolicyManagerCapabilitiesResponse;
};
export declare const QueryVouchersRequest: {
    encode(message: QueryVouchersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryVouchersRequest;
    fromJSON(object: any): QueryVouchersRequest;
    toJSON(message: QueryVouchersRequest): unknown;
    create(base?: DeepPartial<QueryVouchersRequest>): QueryVouchersRequest;
    fromPartial(object: DeepPartial<QueryVouchersRequest>): QueryVouchersRequest;
};
export declare const QueryVouchersResponse: {
    encode(message: QueryVouchersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryVouchersResponse;
    fromJSON(object: any): QueryVouchersResponse;
    toJSON(message: QueryVouchersResponse): unknown;
    create(base?: DeepPartial<QueryVouchersResponse>): QueryVouchersResponse;
    fromPartial(object: DeepPartial<QueryVouchersResponse>): QueryVouchersResponse;
};
export declare const QueryVoucherRequest: {
    encode(message: QueryVoucherRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryVoucherRequest;
    fromJSON(object: any): QueryVoucherRequest;
    toJSON(message: QueryVoucherRequest): unknown;
    create(base?: DeepPartial<QueryVoucherRequest>): QueryVoucherRequest;
    fromPartial(object: DeepPartial<QueryVoucherRequest>): QueryVoucherRequest;
};
export declare const QueryVoucherResponse: {
    encode(message: QueryVoucherResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryVoucherResponse;
    fromJSON(object: any): QueryVoucherResponse;
    toJSON(message: QueryVoucherResponse): unknown;
    create(base?: DeepPartial<QueryVoucherResponse>): QueryVoucherResponse;
    fromPartial(object: DeepPartial<QueryVoucherResponse>): QueryVoucherResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
/** Query defines the gRPC querier service. */
export interface Query {
    /**
     * Params defines a gRPC query method that returns the permissions module's
     * parameters.
     */
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    /** NamespaceDenoms defines a gRPC query method that returns the denoms for which a namespace exists */
    NamespaceDenoms(request: DeepPartial<QueryNamespaceDenomsRequest>, metadata?: grpc.Metadata): Promise<QueryNamespaceDenomsResponse>;
    /**
     * Namespaces defines a gRPC query method that returns the permissions
     * module's created namespaces.
     */
    Namespaces(request: DeepPartial<QueryNamespacesRequest>, metadata?: grpc.Metadata): Promise<QueryNamespacesResponse>;
    /**
     * Namespace defines a gRPC query method that returns the permissions
     * module's namespace associated with the provided denom.
     */
    Namespace(request: DeepPartial<QueryNamespaceRequest>, metadata?: grpc.Metadata): Promise<QueryNamespaceResponse>;
    /** RolesByActor defines a gRPC query method that returns roles for the actor in the namespace */
    RolesByActor(request: DeepPartial<QueryRolesByActorRequest>, metadata?: grpc.Metadata): Promise<QueryRolesByActorResponse>;
    /** ActorsByRole defines a gRPC query method that returns a namespace's roles associated with the provided actor. */
    ActorsByRole(request: DeepPartial<QueryActorsByRoleRequest>, metadata?: grpc.Metadata): Promise<QueryActorsByRoleResponse>;
    /** RoleManagers defines a gRPC query method that returns a namespace's role managers */
    RoleManagers(request: DeepPartial<QueryRoleManagersRequest>, metadata?: grpc.Metadata): Promise<QueryRoleManagersResponse>;
    /** RoleManager defines a gRPC query method that returns the roles a given role manager manages for a given namespace */
    RoleManager(request: DeepPartial<QueryRoleManagerRequest>, metadata?: grpc.Metadata): Promise<QueryRoleManagerResponse>;
    /** PolicyStatuses defines a gRPC query method that returns a namespace's policy statuses */
    PolicyStatuses(request: DeepPartial<QueryPolicyStatusesRequest>, metadata?: grpc.Metadata): Promise<QueryPolicyStatusesResponse>;
    /** PolicyManagerCapabilities defines a gRPC query method that returns a namespace's policy manager capabilities */
    PolicyManagerCapabilities(request: DeepPartial<QueryPolicyManagerCapabilitiesRequest>, metadata?: grpc.Metadata): Promise<QueryPolicyManagerCapabilitiesResponse>;
    /** Vouchers defines a gRPC query method for the vouchers for a given denom */
    Vouchers(request: DeepPartial<QueryVouchersRequest>, metadata?: grpc.Metadata): Promise<QueryVouchersResponse>;
    /** Voucher defines a gRPC query method for the vouchers for a given denom and address */
    Voucher(request: DeepPartial<QueryVoucherRequest>, metadata?: grpc.Metadata): Promise<QueryVoucherResponse>;
    /** Retrieves the entire permissions module's state */
    PermissionsModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    NamespaceDenoms(request: DeepPartial<QueryNamespaceDenomsRequest>, metadata?: grpc.Metadata): Promise<QueryNamespaceDenomsResponse>;
    Namespaces(request: DeepPartial<QueryNamespacesRequest>, metadata?: grpc.Metadata): Promise<QueryNamespacesResponse>;
    Namespace(request: DeepPartial<QueryNamespaceRequest>, metadata?: grpc.Metadata): Promise<QueryNamespaceResponse>;
    RolesByActor(request: DeepPartial<QueryRolesByActorRequest>, metadata?: grpc.Metadata): Promise<QueryRolesByActorResponse>;
    ActorsByRole(request: DeepPartial<QueryActorsByRoleRequest>, metadata?: grpc.Metadata): Promise<QueryActorsByRoleResponse>;
    RoleManagers(request: DeepPartial<QueryRoleManagersRequest>, metadata?: grpc.Metadata): Promise<QueryRoleManagersResponse>;
    RoleManager(request: DeepPartial<QueryRoleManagerRequest>, metadata?: grpc.Metadata): Promise<QueryRoleManagerResponse>;
    PolicyStatuses(request: DeepPartial<QueryPolicyStatusesRequest>, metadata?: grpc.Metadata): Promise<QueryPolicyStatusesResponse>;
    PolicyManagerCapabilities(request: DeepPartial<QueryPolicyManagerCapabilitiesRequest>, metadata?: grpc.Metadata): Promise<QueryPolicyManagerCapabilitiesResponse>;
    Vouchers(request: DeepPartial<QueryVouchersRequest>, metadata?: grpc.Metadata): Promise<QueryVouchersResponse>;
    Voucher(request: DeepPartial<QueryVoucherRequest>, metadata?: grpc.Metadata): Promise<QueryVoucherResponse>;
    PermissionsModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryParamsDesc: UnaryMethodDefinitionish;
export declare const QueryNamespaceDenomsDesc: UnaryMethodDefinitionish;
export declare const QueryNamespacesDesc: UnaryMethodDefinitionish;
export declare const QueryNamespaceDesc: UnaryMethodDefinitionish;
export declare const QueryRolesByActorDesc: UnaryMethodDefinitionish;
export declare const QueryActorsByRoleDesc: UnaryMethodDefinitionish;
export declare const QueryRoleManagersDesc: UnaryMethodDefinitionish;
export declare const QueryRoleManagerDesc: UnaryMethodDefinitionish;
export declare const QueryPolicyStatusesDesc: UnaryMethodDefinitionish;
export declare const QueryPolicyManagerCapabilitiesDesc: UnaryMethodDefinitionish;
export declare const QueryVouchersDesc: UnaryMethodDefinitionish;
export declare const QueryVoucherDesc: UnaryMethodDefinitionish;
export declare const QueryPermissionsModuleStateDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
