import { AccountAddress, Ethereum<PERSON>hainId } from '@injectivelabs/ts-types';
import { InjectiveExchangeRpc } from '@injectivelabs/indexer-proto-ts';
import { CosmosTxV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
interface PrepareTxArgs {
    address: AccountAddress;
    chainId: EthereumChainId;
    message: any;
    estimateGas?: boolean;
    gasLimit?: number;
    memo?: string | number;
    timeoutHeight?: number;
    feeDenom?: string;
    feePrice?: string;
}
/**
 * @category Indexer Grpc API
 * @deprecated use IndexerGrpcWeb3GwApi
 */
export declare class IndexerGrpcTransactionApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveExchangeRpc.InjectiveExchangeRPCClientImpl;
    constructor(endpoint: string);
    prepareTxRequest(args: PrepareTxArgs): Promise<InjectiveExchangeRpc.PrepareTxResponse>;
    prepareExchangeTxRequest(args: PrepareTxArgs): Promise<InjectiveExchangeRpc.PrepareTxResponse>;
    prepareCosmosTxRequest({ memo, address, message, estimateGas, gasLimit, feeDenom, feePrice, timeoutHeight, }: {
        address: string;
        message: any;
        estimateGas?: boolean;
        gasLimit?: number;
        memo?: string | number;
        timeoutHeight?: number;
        feeDenom?: string;
        feePrice?: string;
    }): Promise<InjectiveExchangeRpc.PrepareCosmosTxResponse>;
    /**
     * Keep in mind that the transaction is just added
     * to the mempool, we need to query the transaction hash
     * if we want to ensure that the transaction is included
     * in the block
     */
    broadcastTxRequest({ signature, chainId, message, txResponse, }: {
        signature: string;
        chainId: EthereumChainId;
        useCorrectEIP712Hash?: boolean;
        txResponse: InjectiveExchangeRpc.PrepareTxResponse;
        message: Record<string, any>;
    }): Promise<InjectiveExchangeRpc.BroadcastTxResponse>;
    /**
     * Keep in mind that the transaction is just added
     * to the mempool, we need to query the transaction hash
     * if we want to ensure that the transaction is included
     * in the block
     */
    broadcastCosmosTxRequest({ address, signature, txRaw, pubKey, }: {
        address: string;
        signature: string;
        txRaw: CosmosTxV1Beta1Tx.TxRaw;
        pubKey: {
            type: string;
            value: string;
        };
    }): Promise<InjectiveExchangeRpc.BroadcastCosmosTxResponse>;
    fetchFeePayer(): Promise<InjectiveExchangeRpc.GetFeePayerResponse>;
}
export {};
