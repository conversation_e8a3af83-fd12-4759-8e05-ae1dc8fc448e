#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import asyncio
import argparse
import yaml
import pathlib
from decimal import Decimal
from typing import Dict, Any, Optional, Tuple
from web3 import Web3
from eth_account import Account
import time
import json
import requests

from .token_list import PolygonBridgeTokens

class PolygonEthereumBridge:
    """
    Polygon和以太坊之间的双向桥接工具
    使用官方的Polygon PoS桥实现代币的双向转移
    提供简单易用的接口，用于在两个网络之间桥接代币
    """
    
    # Polygon PoS桥合约地址
    # 主网地址
    POS_ROOT_CHAIN_MANAGER = "******************************************"  # RootChainManager on Ethereum
    ETHER_PREDICATE = "******************************************"         # EtherPredicate
    ERC20_PREDICATE = "******************************************"          # ERC20Predicate
    ERC721_PREDICATE = "******************************************"         # ERC721Predicate
    
    # 以太坊上的WETH合约
    WETH_ADDRESS = "******************************************"
    
    def __init__(
        self, 
        ethereum_rpc_url: str, 
        polygon_rpc_url: str,
        private_key: Optional[str] = None,
        private_key_env: Optional[str] = None,
        gas_price_gwei: float = 50,
        gas_limit: int = 500000,
        max_fee_per_gas: Optional[float] = None,
        max_priority_fee_per_gas: Optional[float] = None
    ):
        """
        初始化桥接工具
        
        Args:
            ethereum_rpc_url: 以太坊RPC URL
            polygon_rpc_url: Polygon RPC URL
            private_key: 私钥（可选，二选一）
            private_key_env: 包含私钥的环境变量名（可选，二选一）
            gas_price_gwei: 燃气价格（Gwei）
            gas_limit: 燃气上限
            max_fee_per_gas: EIP-1559最大费用（Gwei）
            max_priority_fee_per_gas: EIP-1559最大优先费用（Gwei）
        """
        # 获取私钥
        if private_key:
            self.private_key = private_key
        elif private_key_env:
            self.private_key = os.environ.get(private_key_env)
            if not self.private_key:
                raise ValueError(f"环境变量 {private_key_env} 未设置")
        else:
            raise ValueError("必须提供私钥或私钥环境变量名")
            
        # 确保私钥格式正确
        if not self.private_key.startswith('0x'):
            self.private_key = f"0x{self.private_key}"
            
        # 创建账户
        self.account = Account.from_key(self.private_key)
        self.address = self.account.address
        
        # 创建Web3连接
        self.ethereum_web3 = Web3(Web3.HTTPProvider(ethereum_rpc_url))
        self.polygon_web3 = Web3(Web3.HTTPProvider(polygon_rpc_url))
        
        # 设置交易参数
        self.gas_price_gwei = gas_price_gwei
        self.gas_limit = gas_limit
        self.max_fee_per_gas = max_fee_per_gas
        self.max_priority_fee_per_gas = max_priority_fee_per_gas
        
        # 检查连接状态
        if not self.ethereum_web3.is_connected():
            raise ConnectionError("无法连接到以太坊节点")
        if not self.polygon_web3.is_connected():
            raise ConnectionError("无法连接到Polygon节点")
        
        # 检查网络ID是否正确
        eth_chain_id = self.ethereum_web3.eth.chain_id
        if eth_chain_id != PolygonBridgeTokens.ETHEREUM_CHAIN_ID:
            raise ValueError(f"以太坊网络ID错误，期望 {PolygonBridgeTokens.ETHEREUM_CHAIN_ID}，实际 {eth_chain_id}")
            
        polygon_chain_id = self.polygon_web3.eth.chain_id
        if polygon_chain_id != PolygonBridgeTokens.POLYGON_CHAIN_ID:
            raise ValueError(f"Polygon网络ID错误，期望 {PolygonBridgeTokens.POLYGON_CHAIN_ID}，实际 {polygon_chain_id}")
    
    def _parse_amount(self, amount: str, token_info: Dict[str, Any]) -> int:
        """将人类可读的金额转换为链上表示"""
        decimals = token_info.get("decimals", 18)
        amount_decimal = Decimal(amount)
        return int(amount_decimal * (10 ** decimals))
    
    def get_token_balance(self, token_address: str, chain_id: int) -> int:
        """
        获取指定链上特定代币的余额
        
        Args:
            token_address: 代币合约地址
            chain_id: 链ID (1=以太坊, 137=Polygon)
            
        Returns:
            int: 代币余额（原始值，未按小数位转换）
        """
        # 确定使用哪个web3实例
        web3 = self.ethereum_web3 if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else self.polygon_web3
        
        # ERC20 ABI中的balanceOf方法
        abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            }
        ]
        
        # 创建合约实例并调用balanceOf
        token_contract = web3.eth.contract(address=web3.to_checksum_address(token_address), abi=abi)
        balance = token_contract.functions.balanceOf(self.address).call()
        
        return balance
    
    def format_token_amount(self, amount: int, token_info: Dict[str, Any]) -> str:
        """
        将原始代币数量格式化为人类可读的形式
        
        Args:
            amount: 原始代币数量
            token_info: 代币信息
            
        Returns:
            str: 格式化后的代币数量
        """
        decimals = token_info.get("decimals", 18)
        formatted = amount / (10 ** decimals)
        return f"{formatted} {token_info['symbol']}"
    
    def get_token_info(self, token_symbol: Optional[str] = None, token_address: Optional[str] = None, chain: str = "polygon") -> Dict[str, Any]:
        """
        获取代币信息
        
        Args:
            token_symbol: 代币符号（可选，与token_address二选一）
            token_address: 代币地址（可选，与token_symbol二选一）
            chain: 链名称（'polygon'或'ethereum'）
            
        Returns:
            Dict[str, Any]: 代币信息
        """
        if not token_symbol and not token_address:
            raise ValueError("必须提供代币符号或代币地址")
            
        chain_id = PolygonBridgeTokens.POLYGON_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.ETHEREUM_CHAIN_ID
        chain_id_str = str(chain_id)
            
        # 检查token_symbol是否实际上是一个地址（以0x开头）
        if token_symbol and token_symbol.startswith('0x') and len(token_symbol) >= 40:
            # 如果token_symbol像地址，将其视为地址处理
            token_address = token_symbol
            token_symbol = None
            print(f"检测到 --token-symbol 参数提供的是代币地址：{token_address}，自动转为使用地址查询")
        
        if token_symbol:
            token_info = PolygonBridgeTokens.get_token_by_symbol(token_symbol, chain_id)
        else:
            token_info = PolygonBridgeTokens.get_token_by_address(token_address, chain_id)
            
        if not token_info:
            # 尝试在另一个链上查找
            other_chain_id = PolygonBridgeTokens.ETHEREUM_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.POLYGON_CHAIN_ID
            other_chain_name = "ethereum" if chain == "polygon" else "polygon"
            
            if token_symbol:
                other_token_info = PolygonBridgeTokens.get_token_by_symbol(token_symbol, other_chain_id)
            else:
                other_token_info = PolygonBridgeTokens.get_token_by_address(token_address, other_chain_id)
                
            if other_token_info:
                print(f"警告: 在{chain}链上找不到该代币，但在{other_chain_name}链上找到了。")
                print(f"请使用 --chain {other_chain_name} 参数指定正确的链。")
            
            raise ValueError(f"找不到代币信息: {token_symbol or token_address}，请检查地址是否正确，或者使用 list 命令查看支持的代币")
            
        return token_info
    
    def get_balance(self, token_symbol: Optional[str] = None, token_address: Optional[str] = None, chain: str = "polygon") -> str:
        """
        获取代币余额
        
        Args:
            token_symbol: 代币符号（可选，与token_address二选一）
            token_address: 代币地址（可选，与token_symbol二选一）
            chain: 链名称（'polygon'或'ethereum'）
            
        Returns:
            str: 格式化后的代币余额
        """
        token_info = self.get_token_info(token_symbol, token_address, chain)
        chain_id = PolygonBridgeTokens.POLYGON_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.ETHEREUM_CHAIN_ID
        
        balance = self.get_token_balance(token_info["address"], chain_id)
        return self.format_token_amount(balance, token_info)
    
    async def withdraw_from_polygon(self, token_address: str, amount: int) -> str:
        """
        从Polygon提取ERC20代币到以太坊（通过PoS桥）
        
        Args:
            token_address: Polygon上的代币合约地址
            amount: 提取的代币数量
            
        Returns:
            str: 交易哈希
        """
        # 获取代币信息
        token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
        if token_info is None:
            raise ValueError(f"找不到代币信息: {token_address}")
            
        # 检查余额
        balance = self.get_token_balance(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
        if balance < amount:
            formatted_balance = self.format_token_amount(balance, token_info)
            formatted_amount = self.format_token_amount(amount, token_info)
            raise ValueError(f"余额不足，需要 {formatted_amount}，但只有 {formatted_balance}")
            
        print(f"开始从Polygon提取 {self.format_token_amount(amount, token_info)} 到以太坊")
        
        # 子链上的ERC20合约ABI中的withdraw方法
        abi = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amount", "type": "uint256"}
                ],
                "name": "withdraw",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]
        
        # 创建合约实例并准备调用withdraw
        token_contract = self.polygon_web3.eth.contract(
            address=self.polygon_web3.to_checksum_address(token_address), 
            abi=abi
        )
        
        # 构建交易
        tx_params = {
            'from': self.address,
            'gas': self.gas_limit * 2,  # 提高Gas限制
            'gasPrice': self.polygon_web3.eth.gas_price,  # 使用当前网络gas价格
            'nonce': self.polygon_web3.eth.get_transaction_count(self.address),
            'chainId': PolygonBridgeTokens.POLYGON_CHAIN_ID
        }
        
        tx = token_contract.functions.withdraw(amount).build_transaction(tx_params)
        
        # 签名并发送交易
        signed_tx = self.polygon_web3.eth.account.sign_transaction(tx, self.account.key)
        tx_hash = self.polygon_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
        
        print(f"已发送提取交易，交易哈希: {tx_hash.hex()}")
        
        # 等待交易确认
        receipt = self.polygon_web3.eth.wait_for_transaction_receipt(tx_hash)
        if receipt.status != 1:
            raise ValueError(f"提取交易失败，请检查交易详情: https://polygonscan.com/tx/{tx_hash.hex()}")
        
        print(f"代币已在Polygon上销毁，等待检查点后即可在以太坊上接收")
        print(f"注意: PoS桥一般需要3-6小时才能在以太坊上收到代币")
        
        return tx_hash.hex()
    
    async def ethereum_to_polygon(self, amount: str, token_symbol: Optional[str] = None, token_address: Optional[str] = None) -> Dict[str, Any]:
        """
        从以太坊到Polygon桥接代币
        
        Args:
            amount: 桥接金额（人类可读格式）
            token_symbol: 代币符号（可选，与token_address二选一）
            token_address: 代币地址（可选，与token_symbol二选一）
            
        Returns:
            Dict[str, Any]: 桥接结果
        """
        token_info = self.get_token_info(token_symbol, token_address, "ethereum")
        raw_amount = self._parse_amount(amount, token_info)
        
        # 获取Polygon上的对应代币信息
        polygon_token_info = PolygonBridgeTokens.get_equivalent_token(
            token_info["address"], 
            PolygonBridgeTokens.ETHEREUM_CHAIN_ID, 
            PolygonBridgeTokens.POLYGON_CHAIN_ID
        )
        if polygon_token_info is None:
            raise ValueError(f"找不到Polygon上对应的代币: {token_info['address']}")
        
        # 存入代币到Polygon
        deposit_tx_hash = await self.deposit_to_polygon(token_info["address"], raw_amount)
        
        # 返回结果
        return {
            "token_symbol": token_info["symbol"],
            "amount": self.format_token_amount(raw_amount, token_info),
            "ethereum_token_address": token_info["address"],
            "polygon_token_address": polygon_token_info["address"],
            "deposit_transaction_hash": deposit_tx_hash,
            "account": self.address,
            "status": "待接收",
            "message": "代币已从以太坊发送到Polygon网络，通常需要约15-20分钟才能在Polygon上到账"
        }
    
    async def polygon_to_ethereum(self, amount: str, token_symbol: str = None, token_address: str = None, wait_for_claim: bool = False,
                        auto_check_interval: int = 60, max_check_time: int = 21600, initial_wait_time: int = 1800) -> dict:
        """
        将代币从Polygon桥接到以太坊
        
        参数:
            amount: 要桥接的代币数量，可以是小数
            token_symbol: 代币符号，如USDT
            token_address: 代币地址，如果提供了符号则忽略
            wait_for_claim: 是否等待自动认领
            auto_check_interval: 自动检查间隔（秒），默认60秒
            max_check_time: 最大等待时间（秒），默认21600秒（6小时）
            initial_wait_time: 初始等待时间（秒），默认1800秒（30分钟）
        
        返回:
            dict: 包含burn交易hash和claim状态的字典
        """
        # 获取代币信息
        token_info = self.get_token_info(token_symbol, token_address, "polygon")
        
        # 检查代币余额是否足够
        balance = await self.get_token_balance(token_info["address"], PolygonBridgeTokens.POLYGON_CHAIN_ID)
        amount_wei = self._parse_amount(amount, token_info)
        
        if balance < amount_wei:
            formatted_balance = self.format_token_amount(balance, token_info)
            formatted_amount = self.format_token_amount(amount_wei, token_info)
            raise ValueError(
                f"余额不足，需要 {formatted_amount}，但您只有 {formatted_balance}"
            )
            
        print(f"开始将 {amount} {token_info['symbol']} 从 Polygon 桥接到以太坊...")
        
        # 获取合约ABI
        erc20_abi = [
            {
                "constant": False,
                "inputs": [
                    {"name": "_to", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "transfer",
                "outputs": [{"name": "", "type": "bool"}],
                "payable": False,
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "name",
                "outputs": [{"name": "", "type": "string"}],
                "payable": False,
                "stateMutability": "view",
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "payable": False,
                "stateMutability": "view",
                "type": "function"
            }
        ]
        
        # 构建合约实例
        token_contract = self.polygon_web3.eth.contract(address=token_info["address"], abi=erc20_abi)
        
        try:
            nonce = self.polygon_web3.eth.get_transaction_count(self.address)
            
            # 准备交易参数
            tx_params = {
                'from': self.address,
                'nonce': nonce,
            }
            
            # 如果使用EIP-1559，添加maxFeePerGas和maxPriorityFeePerGas
            if self.max_fee_per_gas is not None and self.max_priority_fee_per_gas is not None:
                # 获取当前gas价格
                fee_history = self.polygon_web3.eth.fee_history(1, 'latest', [25])
                base_fee = fee_history.baseFeePerGas[0]
                max_priority_fee = fee_history.reward[0][0]
                
                tx_params['maxFeePerGas'] = int(base_fee * 1.5) + max_priority_fee
                tx_params['maxPriorityFeePerGas'] = max_priority_fee
            else:
                # 如果不使用EIP-1559，使用常规gas价格
                tx_params['gasPrice'] = self.polygon_web3.eth.gas_price
            
            # 构建交易
            tx = token_contract.functions.transfer(
                self.ethereum_web3.to_checksum_address(PolygonBridgeTokens.get_equivalent_token(token_info["address"], PolygonBridgeTokens.POLYGON_CHAIN_ID, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)["address"]), amount_wei
            ).build_transaction(tx_params)
            
            # 估算gas
            estimated_gas = self.polygon_web3.eth.estimate_gas(tx)
            tx['gas'] = int(estimated_gas * 1.2)  # 添加20%的gas缓冲
            
            # 签名并发送交易
            signed_tx = self.polygon_web3.eth.account.sign_transaction(tx, self.private_key)
            tx_hash = self.polygon_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            print(f"交易已提交: {tx_hash.hex()}")
            
            print("\n等待交易确认...")
            receipt = self.polygon_web3.eth.wait_for_transaction_receipt(tx_hash, timeout=600)
            
            if receipt['status'] == 1:
                print(f"代币已成功在Polygon上销毁，交易哈希: {tx_hash.hex()}")
                print("您可以在这里查看交易详情: " + 
                      f"https://polygonscan.com/tx/{tx_hash.hex()}")
                
                result = {
                    "burn_transaction_hash": tx_hash.hex(),
                    "burn_success": True,
                    "claim_status": "pending",
                    "claim_success": False
                }
                
                # 是否等待认领
                if wait_for_claim:
                    print("\n交易已确认，将进入自动监控模式...")
                    print(f"系统将先等待 {initial_wait_time//60} 分钟后开始监控检查点状态")
                    print(f"之后将每 {auto_check_interval} 秒检查一次，最长等待总时间 {max_check_time//3600:.1f} 小时")
                    
                    # 先等待初始时间
                    if initial_wait_time > 0:
                        print(f"\n请耐心等待 {initial_wait_time//60} 分钟...")
                        await asyncio.sleep(initial_wait_time)
                        print("初始等待时间已过，开始定期检查...")
                    
                    start_time = time.time()
                    claim_status = "pending"
                    check_count = 0
                    
                    while time.time() - start_time < max_check_time:
                        try:
                            check_count += 1
                            print(f"\n开始第 {check_count} 次检查...")
                            claim_result = await self.claim_tokens(tx_hash.hex())
                            
                            # 如果已确认或已经接收到代币
                            if claim_result.get("status") == "confirmed" or claim_result.get("token_received", False):
                                print("\n🎉 代币已成功桥接到以太坊!")
                                result.update({
                                    "claim_status": "confirmed",
                                    "claim_success": True,
                                    "claim_details": claim_result
                                })
                                break
                            
                            # 更新状态信息
                            confirmations = claim_result.get("confirmations", 0)
                            current_block = claim_result.get("current_block", 0)
                            checkpoint_block = claim_result.get("checkpoint_block", 0)
                            
                            # 显示进度
                            elapsed = time.time() - start_time + initial_wait_time  # 加上初始等待时间
                            print(f"总等待时间: {elapsed//60:.0f} 分钟 ({elapsed/3600:.2f} 小时)")
                            print(f"当前确认数: {confirmations}")
                            
                            if checkpoint_block and checkpoint_block > 0:
                                blocks_remaining = max(0, checkpoint_block - current_block)
                                print(f"距离目标检查点还有 {blocks_remaining} 个区块")
                                
                                # 估算剩余时间 (假设平均每2秒一个区块)
                                est_time_remaining = blocks_remaining * 2
                                print(f"预计剩余时间: ~{est_time_remaining//60:.0f} 分钟")
                            
                            # 更新结果，即使还未完成
                            result.update({
                                "claim_status": claim_result.get("status", "pending"),
                                "claim_details": claim_result
                            })
                            
                            # 等待下一次检查
                            print(f"\n将在 {auto_check_interval} 秒后再次检查...")
                            await asyncio.sleep(auto_check_interval)
                            
                        except Exception as e:
                            print(f"检查时发生错误: {str(e)}")
                            await asyncio.sleep(auto_check_interval)
                    
                    # 检查是否超时
                    if time.time() - start_time >= max_check_time:
                        print("\n⚠️ 已达到最大等待时间，但代币尚未被确认")
                        print("您可以稍后使用以下命令继续检查状态:")
                        print(f"python -m src.bridge.pol_bridge.bridge_tokens claim --burn-tx-hash {tx_hash.hex()}")
                        
                        result.update({
                            "claim_status": "timeout", 
                            "claim_success": False
                        })
                
                return result
            else:
                print(f"交易失败: {receipt}")
                return {
                    "burn_transaction_hash": tx_hash.hex(),
                    "burn_success": False,
                    "error": "交易已提交但未成功执行"
                }
        except Exception as e:
            error_msg = str(e)
            print(f"错误: {error_msg}")
            
            if "insufficient funds" in error_msg.lower():
                print("\n您的MATIC余额不足以支付gas费。请确保您的钱包中有足够的MATIC。")
                print(f"当前余额: {self.polygon_web3.from_wei(self.polygon_web3.eth.get_balance(self.address), 'ether')} MATIC")
            
            raise ValueError(f"桥接过程中出错: {error_msg}")
    
    async def claim_tokens(self, burn_tx_hash: str) -> dict:
        """
        查询从Polygon提取到以太坊的代币状态并在以太坊上执行claim操作
        
        参数:
            burn_tx_hash: Polygon上销毁代币的交易哈希
            
        返回:
            dict: 包含提取状态信息的字典
        """
        # 检查交易哈希格式
        if not burn_tx_hash.startswith('0x') or len(burn_tx_hash) != 66:
            raise ValueError("无效的交易哈希，应为以'0x'开头的66个字符")
            
        print(f"正在查询交易 {burn_tx_hash} 的状态...")
        
        try:
            # 首先查询交易是否存在于Polygon链上
            try:
                receipt = self.polygon_web3.eth.get_transaction_receipt(burn_tx_hash)
                if not receipt:
                    return {
                        "status": "not_found",
                        "message": "在Polygon链上未找到该交易"
                    }
                
                if receipt['status'] != 1:
                    return {
                        "status": "failed",
                        "message": "该交易在Polygon链上执行失败"
                    }
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"查询交易状态时出错: {str(e)}"
                }
            
            # 获取当前区块高度
            current_block = self.ethereum_web3.eth.block_number
            
            # 获取交易区块号
            tx = self.polygon_web3.eth.get_transaction(burn_tx_hash)
            tx_block_number = tx['blockNumber']
            
            # 获取当前Polygon区块高度
            polygon_current_block = self.polygon_web3.eth.block_number
            
            # 计算已经过的区块数
            blocks_passed = polygon_current_block - tx_block_number
            
            # 检查点通常每10-30分钟处理一次，约300-900个区块
            # 假设一个检查点大约需要700个区块
            checkpoint_interval = 700
            
            # 构建结果数据
            result = {
                "status": "pending",
                "tx_block_number": tx_block_number,
                "current_block": polygon_current_block,
                "blocks_passed": blocks_passed,
                "confirmations": blocks_passed,
                "checkpoint_block": tx_block_number + checkpoint_interval
            }
            
            # 检查是否足够的区块已经过去
            if blocks_passed < checkpoint_interval:
                # 估算还需要多少区块
                blocks_needed = checkpoint_interval - blocks_passed
                
                # 估算还需要多少时间（区块生成速度大约每2秒一个）
                time_estimate = blocks_needed * 2
                
                result.update({
                    "message": f"交易尚未包含在检查点中，预计还需等待约 {time_estimate} 秒",
                    "blocks_needed": blocks_needed,
                })
                return result
            
            # 如果已经过了足够区块，尝试执行claim操作
            print("交易已经过足够的区块确认，准备从以太坊上提取代币...")
            
            # 尝试在以太坊上执行claim操作
            try:
                # 获取交易详情，确定代币地址和金额
                tx_details = await self._get_burn_tx_details(burn_tx_hash)
                if not tx_details or "token_address" not in tx_details:
                    return {
                        "status": "error", 
                        "message": "无法获取交易详情，请确保这是一个有效的Polygon to Ethereum桥接交易"
                    }
                
                # 在以太坊上执行claim操作
                claim_tx_hash = await self._execute_claim_on_ethereum(burn_tx_hash, tx_details)
                
                if claim_tx_hash:
                    return {
                        "status": "claimed",
                        "message": "已成功在以太坊上执行代币提取操作",
                        "tx_block_number": tx_block_number,
                        "current_block": polygon_current_block,
                        "blocks_passed": blocks_passed,
                        "claim_tx_hash": claim_tx_hash,
                        "token_received": True
                    }
                else:
                    return {
                        "status": "claim_failed",
                        "message": "尝试在以太坊上提取代币失败，请稍后再试",
                        "tx_block_number": tx_block_number,
                        "current_block": polygon_current_block,
                        "blocks_passed": blocks_passed
                    }
            except Exception as e:
                print(f"执行claim操作时出错: {str(e)}")
                return {
                    "status": "confirmed",
                    "message": "交易已包含在检查点中，但执行claim操作失败。您可以稍后再试，或使用Polygon桥接官方网站手动claim",
                    "tx_block_number": tx_block_number,
                    "current_block": polygon_current_block,
                    "blocks_passed": blocks_passed,
                    "error": str(e)
                }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"查询交易状态时出错: {str(e)}"
            }
    
    async def _get_burn_tx_details(self, burn_tx_hash: str) -> dict:
        """获取Polygon上burn交易的详细信息"""
        try:
            # 获取交易信息
            tx = self.polygon_web3.eth.get_transaction(burn_tx_hash)
            receipt = self.polygon_web3.eth.get_transaction_receipt(burn_tx_hash)
            
            # 解析交易输入数据，找出代币地址和金额
            token_address = receipt['to']  # 通常是代币合约地址
            
            print(f"已找到交易 {burn_tx_hash} 的收据:")
            print(f"代币合约地址: {token_address}")
            
            # 获取代币信息 - 添加更多错误处理
            try:
                token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
                if not token_info:
                    print(f"警告: 无法在Polygon上找到代币信息，尝试通过ERC20接口获取基本信息")
                    
                    # 通过ERC20接口获取基本信息
                    erc20_abi = [
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "name",
                            "outputs": [{"name": "", "type": "string"}],
                            "type": "function"
                        },
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "symbol",
                            "outputs": [{"name": "", "type": "string"}],
                            "type": "function"
                        },
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "decimals",
                            "outputs": [{"name": "", "type": "uint8"}],
                            "type": "function"
                        }
                    ]
                    
                    try:
                        token_contract = self.polygon_web3.eth.contract(
                            address=self.polygon_web3.to_checksum_address(token_address),
                            abi=erc20_abi
                        )
                        
                        token_name = token_contract.functions.name().call()
                        token_symbol = token_contract.functions.symbol().call()
                        token_decimals = token_contract.functions.decimals().call()
                        
                        # 创建一个基本的代币信息对象
                        token_info = {
                            "address": token_address,
                            "name": token_name,
                            "symbol": token_symbol,
                            "decimals": token_decimals,
                            "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                        }
                        print(f"已通过ERC20接口获取代币信息: {token_symbol} ({token_name})")
                    except Exception as e:
                        print(f"无法通过ERC20接口读取代币信息: {str(e)}")
                        # 创建一个占位符代币信息
                        token_info = {
                            "address": token_address,
                            "name": "Unknown Token",
                            "symbol": "UNKNOWN",
                            "decimals": 18,  # 默认18位精度
                            "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                        }
            except Exception as e:
                print(f"获取Polygon上的代币信息时出错: {str(e)}")
                # 创建一个占位符代币信息
                token_info = {
                    "address": token_address,
                    "name": "Unknown Token",
                    "symbol": "UNKNOWN",
                    "decimals": 18,  # 默认18位精度
                    "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                }
            
            print(f"Polygon代币信息: {token_info.get('symbol')} ({token_info.get('name')})")
            
            # 获取对应的以太坊代币 - 添加错误处理
            try:
                eth_token_info = PolygonBridgeTokens.get_equivalent_token(
                    token_address, 
                    PolygonBridgeTokens.POLYGON_CHAIN_ID, 
                    PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                )
                
                if not eth_token_info:
                    print(f"警告: 在以太坊上找不到对应的代币映射，使用相同信息")
                    # 使用相同的信息，但更改chainId
                    eth_token_info = token_info.copy()
                    eth_token_info["chainId"] = PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                    # 移除可能包含"PoS"的名称
                    if "name" in eth_token_info and " (PoS)" in eth_token_info["name"]:
                        eth_token_info["name"] = eth_token_info["name"].replace(" (PoS)", "")
            except Exception as e:
                print(f"获取以太坊上对应代币时出错: {str(e)}")
                # 创建一个占位符代币信息
                eth_token_info = token_info.copy()
                eth_token_info["chainId"] = PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                if "name" in eth_token_info and " (PoS)" in eth_token_info["name"]:
                    eth_token_info["name"] = eth_token_info["name"].replace(" (PoS)", "")
                
            print(f"以太坊代币信息: {eth_token_info.get('symbol')} ({eth_token_info.get('name')})")
            
            return {
                "token_address": token_address,
                "polygon_token_info": token_info,
                "ethereum_token_info": eth_token_info,
                "transaction": tx,
                "receipt": receipt
            }
        except Exception as e:
            error_msg = str(e)
            print(f"获取burn交易详情时出错: {error_msg}")
            
            if "invalid literal for int()" in error_msg:
                print("可能是代币数据格式问题，请确保tokens.json中的chainId是整数")
            
            if "not found" in error_msg.lower():
                print(f"交易哈希 {burn_tx_hash} 在Polygon上找不到，请确认哈希是否正确")
                
            return {}
    
    async def _execute_claim_on_ethereum(self, burn_tx_hash: str, tx_details: dict) -> Optional[str]:
        """在以太坊上执行claim操作"""
        try:
            # 打印钱包地址和余额信息进行调试
            eth_balance = self.ethereum_web3.eth.get_balance(self.address)
            eth_balance_in_ether = self.ethereum_web3.from_wei(eth_balance, 'ether')
            print(f"\n以太坊钱包信息:")
            print(f"地址: {self.address}")
            print(f"余额: {eth_balance_in_ether} ETH")
            
            if eth_balance < self.ethereum_web3.to_wei(0.01, 'ether'):
                print(f"警告: 以太坊余额可能不足以支付gas费用，建议至少有0.01 ETH")
            
            # RootChainManager的ABI
            root_chain_manager_abi = [
                {
                    "inputs": [
                        {"internalType": "bytes", "name": "inputData", "type": "bytes"}
                    ],
                    "name": "exit",
                    "outputs": [],
                    "stateMutability": "nonpayable",
                    "type": "function"
                }
            ]
            
            # 创建RootChainManager合约实例
            root_chain_manager = self.ethereum_web3.eth.contract(
                address=self.ethereum_web3.to_checksum_address(self.POS_ROOT_CHAIN_MANAGER),
                abi=root_chain_manager_abi
            )
            
            # 获取交易收据
            receipt = tx_details.get("receipt")
            if not receipt or 'logs' not in receipt:
                raise ValueError("无法获取交易日志")
            
            # 从Polygon网络获取proof证明
            proof_data = await self._get_exit_proof(burn_tx_hash)
            if not proof_data:
                raise ValueError("无法获取exit proof证明，请稍后再试或使用Polygon官方桥接界面完成操作")
            
            # 获取当前gas价格
            gas_price = self.ethereum_web3.eth.gas_price
            # 使用当前gas价格的1.1倍，确保交易能够被处理
            suggested_gas_price = int(gas_price * 1.1)
            print(f"当前以太坊gas价格: {self.ethereum_web3.from_wei(gas_price, 'gwei')} Gwei")
            print(f"建议gas价格: {self.ethereum_web3.from_wei(suggested_gas_price, 'gwei')} Gwei")
            
            # 估算gas限制
            try:
                estimated_gas = root_chain_manager.functions.exit(proof_data).estimate_gas({'from': self.address})
                # 添加20%的缓冲
                gas_limit = int(estimated_gas * 1.2)
                print(f"估算gas限制: {estimated_gas}，使用{gas_limit}（含20%缓冲）")
            except Exception as e:
                print(f"无法估算gas限制: {str(e)}，使用默认值: {self.gas_limit * 2}")
                gas_limit = self.gas_limit * 2
            
            # 获取当前nonce
            nonce = self.ethereum_web3.eth.get_transaction_count(self.address)
            print(f"当前nonce: {nonce}")
            
            # 构建交易
            tx_params = {
                'from': self.address,
                'gas': gas_limit,
                'nonce': nonce,
                'chainId': PolygonBridgeTokens.ETHEREUM_CHAIN_ID
            }
            
            # 根据网络条件设置gas价格
            current_block = self.ethereum_web3.eth.get_block('latest')
            if 'baseFeePerGas' in current_block:
                # EIP-1559交易
                print("使用EIP-1559交易类型")
                base_fee = current_block['baseFeePerGas']
                priority_fee = min(self.ethereum_web3.to_wei(2, 'gwei'), gas_price - base_fee) if gas_price > base_fee else self.ethereum_web3.to_wei(1, 'gwei')
                
                tx_params['maxFeePerGas'] = base_fee * 2 + priority_fee
                tx_params['maxPriorityFeePerGas'] = priority_fee
                
                print(f"Base fee: {self.ethereum_web3.from_wei(base_fee, 'gwei')} Gwei")
                print(f"Priority fee: {self.ethereum_web3.from_wei(priority_fee, 'gwei')} Gwei")
                print(f"Max fee: {self.ethereum_web3.from_wei(tx_params['maxFeePerGas'], 'gwei')} Gwei")
            else:
                # 传统交易
                print("使用传统交易类型")
                tx_params['gasPrice'] = suggested_gas_price
                print(f"Gas价格: {self.ethereum_web3.from_wei(suggested_gas_price, 'gwei')} Gwei")
            
            # 使用proof_data构建exit交易
            tx = root_chain_manager.functions.exit(proof_data).build_transaction(tx_params)
            
            # 计算交易费用
            if 'maxFeePerGas' in tx:
                max_tx_fee = tx['gas'] * tx['maxFeePerGas']
            else:
                max_tx_fee = tx['gas'] * tx['gasPrice']
                
            max_tx_fee_ether = self.ethereum_web3.from_wei(max_tx_fee, 'ether')
            print(f"最大可能交易费用: {max_tx_fee_ether} ETH")
            
            if eth_balance < max_tx_fee:
                print(f"严重警告: 钱包余额 ({eth_balance_in_ether} ETH) 不足以支付最大可能的交易费用 ({max_tx_fee_ether} ETH)")
                print("建议先向钱包充值ETH")
            
            # 签名交易
            print("正在签名交易...")
            signed_tx = self.ethereum_web3.eth.account.sign_transaction(tx, self.account.key)
            
            # 发送交易
            print("正在发送交易...")
            try:
                tx_hash = self.ethereum_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                print(f"已发送claim交易，交易哈希: {tx_hash.hex()}")
                
                # 等待交易确认
                print("等待交易确认...")
                receipt = self.ethereum_web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
                if receipt.status != 1:
                    raise ValueError(f"claim交易失败，请查看详情: https://etherscan.io/tx/{tx_hash.hex()}")
                    
                print(f"交易确认成功！代币已成功提取到以太坊地址: {self.address}")
                return tx_hash.hex()
            except Exception as e:
                error_msg = str(e)
                print(f"发送交易失败: {error_msg}")
                
                if "insufficient funds" in error_msg:
                    print("\n错误原因: 钱包中没有足够的ETH支付gas费用")
                    print(f"当前余额: {eth_balance_in_ether} ETH")
                    print(f"预估需要: {max_tx_fee_ether} ETH")
                    print("解决方案: 向您的钱包地址充值ETH")
                elif "nonce too low" in error_msg:
                    print("\n错误原因: nonce太低，可能是账户已经有一个挂起的交易")
                    print("解决方案: 等待挂起的交易确认，或使用更高的nonce值")
                elif "already known" in error_msg:
                    print("\n错误原因: 交易已经提交过")
                    print("解决方案: 等待之前的交易确认")
                elif "underpriced" in error_msg:
                    print("\n错误原因: gas价格太低")
                    print(f"当前设置: {self.ethereum_web3.from_wei(suggested_gas_price, 'gwei')} Gwei")
                    print("解决方案: 增加gas价格")
                    
                raise ValueError(f"无法发送交易: {error_msg}")
                
        except Exception as e:
            print(f"执行以太坊claim操作时出错: {str(e)}")
            return None

    def list_tokens(self) -> Dict[str, Dict[int, Any]]:
        """
        列出可桥接的代币
        
        Returns:
            Dict[str, Dict[int, Any]]: 可桥接的代币列表
        """
        print("使用官方Polygon PoS桥提供桥接服务")
        return PolygonBridgeTokens.get_common_tokens()

    async def approve_token(self, token_address: str, spender_address: str, amount: int, chain_id: int) -> Optional[str]:
        """
        授权代币合约使用代币
        
        Args:
            token_address: 代币合约地址
            spender_address: 授权使用代币的合约地址
            amount: 需要授权的金额
            chain_id: 链ID
            
        Returns:
            Optional[str]: 如果发送了授权交易，返回交易哈希；否则返回None
        """
        w3 = self.ethereum_web3 if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else self.polygon_web3
        
        # ERC20 ABI 定义
        erc20_abi = [
            {
                "constant": True,
                "inputs": [
                    {"name": "_owner", "type": "address"},
                    {"name": "_spender", "type": "address"}
                ],
                "name": "allowance",
                "outputs": [{"name": "remaining", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": False,
                "inputs": [
                    {"name": "_spender", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "approve",
                "outputs": [{"name": "success", "type": "bool"}],
                "type": "function"
            }
        ]
        
        # 创建合约实例
        token_contract = w3.eth.contract(
            address=w3.to_checksum_address(token_address),
            abi=erc20_abi
        )
        
        # 获取当前授权金额
        try:
            allowance = token_contract.functions.allowance(
                self.address, 
                spender_address
            ).call()
            
            # 如果授权不足，则进行授权
            if allowance < amount:
                print(f"当前授权额度不足: {allowance} < {amount}")
                
                # 使用最大整数值进行无限授权
                max_uint256 = 2**256 - 1
                
                # 构建授权交易
                tx_params = {
                    'from': self.address,
                    'nonce': w3.eth.get_transaction_count(self.address),
                    'gas': self.gas_limit * 2,  # 提高Gas限制
                    'chainId': chain_id
                }
                
                # 使用EIP-1559交易类型（如果提供了相关参数）
                if self.max_fee_per_gas is not None and self.max_priority_fee_per_gas is not None and chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID:
                    tx_params['maxFeePerGas'] = Web3.to_wei(self.max_fee_per_gas, 'gwei')
                    tx_params['maxPriorityFeePerGas'] = Web3.to_wei(self.max_priority_fee_per_gas, 'gwei')
                else:
                    tx_params['gasPrice'] = w3.eth.gas_price if chain_id == PolygonBridgeTokens.POLYGON_CHAIN_ID else Web3.to_wei(self.gas_price_gwei, 'gwei')
                
                # 构建授权交易
                tx = token_contract.functions.approve(
                    spender_address,
                    max_uint256  # 使用最大整数值进行无限授权
                ).build_transaction(tx_params)
                
                # 签名交易
                signed_tx = w3.eth.account.sign_transaction(tx, self.account.key)
                
                # 发送交易
                tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
                print(f"已发送授权交易，交易哈希: {tx_hash.hex()}")
                
                # 等待交易确认
                try:
                    receipt = w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
                    if receipt.status != 1:
                        chain_explorer = "etherscan.io" if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else "polygonscan.com"
                        raise Exception(f"授权交易失败，详情: https://{chain_explorer}/tx/{tx_hash.hex()}")
                    print("授权交易已确认，无限授权成功")
                except Exception as e:
                    print(f"等待授权交易确认时出错: {e}")
                    print("继续执行，但后续操作可能会失败...")
                
                return tx_hash.hex()
            
            print(f"授权额度已足够: {allowance} >= {amount}")
            return None
            
        except Exception as e:
            print(f"检查授权额度时出错: {e}")
            raise ValueError(f"无法检查或授权代币: {str(e)}")
    
    async def deposit_to_polygon(self, token_address: str, amount: int) -> str:
        """
        从以太坊存入ERC20代币到Polygon（通过PoS桥）
        
        Args:
            token_address: 以太坊上的代币合约地址
            amount: 存入的代币数量
            
        Returns:
            str: 存入交易的哈希
        """
        # 获取代币信息
        token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
        if token_info is None:
            raise ValueError(f"找不到代币信息: {token_address}")
            
        # 检查余额
        balance = self.get_token_balance(token_address, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
        if balance < amount:
            formatted_balance = self.format_token_amount(balance, token_info)
            formatted_amount = self.format_token_amount(amount, token_info)
            raise ValueError(f"余额不足，需要 {formatted_amount}，但只有 {formatted_balance}")
            
        print(f"开始从以太坊存入 {self.format_token_amount(amount, token_info)} 到Polygon")
        
        # 授权RootChainManager合约使用代币
        await self.approve_token(token_address, self.POS_ROOT_CHAIN_MANAGER, amount, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
        
        # RootChainManager ABI中的depositFor方法
        abi = [
            {
                "inputs": [
                    {"internalType": "address", "name": "user", "type": "address"},
                    {"internalType": "address", "name": "rootToken", "type": "address"},
                    {"internalType": "bytes", "name": "depositData", "type": "bytes"}
                ],
                "name": "depositFor",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]
        
        # 创建合约实例并准备调用depositFor
        root_chain_manager = self.ethereum_web3.eth.contract(
            address=self.ethereum_web3.to_checksum_address(self.POS_ROOT_CHAIN_MANAGER), 
            abi=abi
        )
        
        # 准备depositData (amount的ABI编码)
        deposit_data = self.ethereum_web3.codec.encode_single('uint256', amount)
        
        # 构建交易
        tx_params = {
            'from': self.address,
            'gas': self.gas_limit,
            'nonce': self.ethereum_web3.eth.get_transaction_count(self.address),
            'chainId': PolygonBridgeTokens.ETHEREUM_CHAIN_ID
        }
        
        # 使用EIP-1559交易类型（如果提供了相关参数）
        if self.max_fee_per_gas is not None and self.max_priority_fee_per_gas is not None:
            tx_params['maxFeePerGas'] = Web3.to_wei(self.max_fee_per_gas, 'gwei')
            tx_params['maxPriorityFeePerGas'] = Web3.to_wei(self.max_priority_fee_per_gas, 'gwei')
        else:
            tx_params['gasPrice'] = Web3.to_wei(self.gas_price_gwei, 'gwei')
        
        tx = root_chain_manager.functions.depositFor(
            self.address,  # user
            self.ethereum_web3.to_checksum_address(token_address),  # rootToken
            deposit_data   # depositData
        ).build_transaction(tx_params)
        
        # 签名并发送交易
        signed_tx = self.ethereum_web3.eth.account.sign_transaction(tx, self.account.key)
        tx_hash = self.ethereum_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
        
        print(f"已发送存款交易，交易哈希: {tx_hash.hex()}")
        
        # 等待交易确认
        receipt = self.ethereum_web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        if receipt.status != 1:
            raise ValueError(f"存款交易失败，请检查交易详情: https://etherscan.io/tx/{tx_hash.hex()}")
        
        print(f"代币已在以太坊上存入，等待约15-20分钟左右即可在Polygon上接收")
        
        return tx_hash.hex()

    async def _get_exit_proof(self, burn_tx_hash: str) -> Optional[bytes]:
        """
        获取用于exit操作的证明
        
        这个功能调用Polygon的API获取proof证明
        """
        try:
            print("正在从Polygon API获取exit proof...")
            
            # 从配置文件获取代理信息
            config = load_config()
            proxy_config = config.get("proxy", {})
            proxy_enabled = proxy_config.get("enabled", False)
            
            # 设置代理
            proxies = None
            if proxy_enabled:
                http_proxy = proxy_config.get("http")
                https_proxy = proxy_config.get("https")
                if http_proxy or https_proxy:
                    proxies = {}
                    if http_proxy:
                        proxies["http"] = http_proxy
                    if https_proxy:
                        proxies["https"] = https_proxy
                    print(f"将使用代理: {proxies}")
            
            # 设置不需要验证SSL证书
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            # 根据官方文档设置多个自定义的proof API
            # 参考: https://docs.polygon.technology/tools/matic-js/set-proof-api/
            exit_payload_urls = [
                # 官方公共API
                f"https://proof-generator.polygon.technology/api/v1/matic/exit-payload/{burn_tx_hash}?eventSignature=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
                
                # Gateway.fm API (非官方备用)
                f"https://polygon-proof.gateway.fm/api/v1/matic/exit-payload/{burn_tx_hash}?eventSignature=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
                
                # 直接使用RPC节点调用的URL (结构不同)
                f"https://polygon-rpc.com",
                
                # 备用公共API
                f"https://proof-generator.polygon.technology/api/v1/matic/exit-payload/{burn_tx_hash}",
                f"https://proof-generator.polygon.technology/api/v2/matic/exit-payload/{burn_tx_hash}",
            ]
            
            session = requests.Session()
            
            # 尝试不同的API端点，不使用代理
            for url in exit_payload_urls:
                print(f"尝试API端点 (无代理): {url}")
                try:
                    if 'polygon-rpc.com' in url:
                        # 如果是RPC节点，使用JSON-RPC调用
                        rpc_payload = {
                            "jsonrpc": "2.0",
                            "id": 1,
                            "method": "eth_getProof",
                            "params": [
                                "******************************************",  # 任意地址
                                [],  # 存储位置
                                "latest"
                            ]
                        }
                        response = session.post(
                            url, 
                            json=rpc_payload,
                            timeout=30,
                            verify=False
                        )
                        # 这里只是测试连接，不期望获得实际的proof
                        if response.status_code == 200:
                            print("RPC节点连接成功，但此方法无法直接获取proof")
                        continue
                    else:
                        # 普通的proof API
                        response = session.get(
                            url,
                            timeout=30,
                            verify=False
                        )
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if 'result' in data:
                                print(f"通过API成功获取exit proof")
                                return Web3.to_bytes(hexstr=data['result'])
                            elif 'message' in data and data['message'] == 'success' and 'result' in data:
                                print(f"通过API成功获取exit proof")
                                return Web3.to_bytes(hexstr=data['result'])
                            else:
                                print(f"API响应格式不符合预期: {data}")
                        except Exception as e:
                            print(f"解析API响应时出错: {str(e)}")
                    else:
                        print(f"API返回状态码: {response.status_code}")
                except Exception as e:
                    print(f"API请求失败: {str(e)}")
            
            # 如果无代理失败，尝试使用代理
            if proxies:
                session = requests.Session()
                for url in exit_payload_urls:
                    if 'polygon-rpc.com' in url:
                        continue  # 跳过RPC节点
                        
                    print(f"尝试API端点 (使用代理): {url}")
                    try:
                        response = session.get(
                            url,
                            proxies=proxies,
                            timeout=30,
                            verify=False
                        )
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                if 'result' in data:
                                    print(f"通过API成功获取exit proof (使用代理)")
                                    return Web3.to_bytes(hexstr=data['result'])
                                elif 'message' in data and data['message'] == 'success' and 'result' in data:
                                    print(f"通过API成功获取exit proof (使用代理)")
                                    return Web3.to_bytes(hexstr=data['result'])
                                else:
                                    print(f"API响应格式不符合预期: {data}")
                            except Exception as e:
                                print(f"解析API响应时出错: {str(e)}")
                        else:
                            print(f"API返回状态码: {response.status_code}")
                    except Exception as e:
                        print(f"API请求失败: {str(e)}")
            
            # 尝试最后的方法：curl命令
            print("\n尝试使用curl命令获取proof...")
            import subprocess
            import json
            
            curl_cmd = [
                'curl', '-s', '-k',
                exit_payload_urls[0]
            ]
            
            try:
                result = subprocess.run(curl_cmd, capture_output=True, text=True)
                if result.returncode == 0 and result.stdout:
                    try:
                        data = json.loads(result.stdout)
                        if 'result' in data:
                            print(f"通过curl成功获取exit proof")
                            return Web3.to_bytes(hexstr=data['result'])
                    except Exception as e:
                        print(f"解析curl响应时出错: {str(e)}")
            except Exception as e:
                print(f"执行curl命令时出错: {str(e)}")
            
            # 如果所有API都失败，提供更详细的错误信息和建议
            print("\n所有API请求尝试均已失败。请考虑以下选项:")
            print("1. 使用Polygon桥接官方网站手动完成claim操作: https://wallet.polygon.technology/bridge")
            print("2. 检查交易哈希是否正确，以及该交易是否已经可以被claim")
            print(f"3. 检查交易在Polygonscan上的状态: https://polygonscan.com/tx/{burn_tx_hash}")
            return None
            
        except Exception as e:
            print(f"获取exit proof时出错: {str(e)}")
            print("请使用Polygon桥接官方网站手动完成claim操作: https://wallet.polygon.technology/bridge")
            return None

def load_config() -> Dict[str, Any]:
    """
    从配置文件加载配置
    
    Returns:
        Dict[str, Any]: 配置信息
    """
    # 确定项目根目录，并加载配置文件
    # 首先尝试查找项目根目录（包含config文件夹的目录）
    current_dir = pathlib.Path(__file__).resolve().parent
    
    # 向上查找三级，直到找到config目录
    for _ in range(5):
        config_path = current_dir / "config" / "config.yaml"
        if config_path.exists():
            break
        current_dir = current_dir.parent
    
    # 如果仍然找不到，尝试从工作目录找
    if not config_path.exists():
        project_root = pathlib.Path.cwd()
        while project_root.name and not (project_root / "config" / "config.yaml").exists():
            project_root = project_root.parent
        
        config_path = project_root / "config" / "config.yaml"
        
    # 如果仍然找不到，则使用绝对路径
    if not config_path.exists():
        config_path = pathlib.Path("C:/Users/<USER>/CascadeProjects/cex_dex_arb_dev/config/config.yaml")
    
    # 如果仍然找不到，抛出异常
    if not config_path.exists():
        raise FileNotFoundError(f"找不到配置文件: {config_path}")
    
    # 加载YAML配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config

def get_network_config() -> Tuple[str, str, str]:
    """
    从配置文件中获取网络配置
    
    Returns:
        Tuple[str, str, str]: 以太坊RPC、Polygon RPC、私钥
    """
    config = load_config()
    
    # 获取以太坊RPC
    eth_rpc = config.get("dex", {}).get("ethereum", {}).get("rpc_url", "")
    if not eth_rpc:
        raise ValueError("配置文件中未找到以太坊RPC URL")
    
    # 获取Polygon RPC
    polygon_rpc = config.get("dex", {}).get("polygon", {}).get("rpc_url", "")
    if not polygon_rpc:
        raise ValueError("配置文件中未找到Polygon RPC URL")
    
    # 获取私钥
    # 首先尝试从polygon配置获取
    private_key = config.get("dex", {}).get("polygon", {}).get("wallet", {}).get("private_key", "")
    
    # 如果未找到，尝试从ethereum配置获取
    if not private_key:
        private_key = config.get("dex", {}).get("ethereum", {}).get("wallet", {}).get("private_key", "")
    
    if not private_key:
        raise ValueError("配置文件中未找到私钥")
    
    return eth_rpc, polygon_rpc, private_key

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="Polygon和以太坊之间的双向桥接工具")
    
    # RPC和账户参数
    parser.add_argument("--eth-rpc", help="以太坊RPC URL (如不提供将从配置文件读取)")
    parser.add_argument("--polygon-rpc", help="Polygon RPC URL (如不提供将从配置文件读取)")
    
    # 私钥参数（二选一）
    key_group = parser.add_mutually_exclusive_group()
    key_group.add_argument("--private-key", help="私钥 (如不提供将从配置文件读取)")
    key_group.add_argument("--private-key-env", help="包含私钥的环境变量名")
    
    # 燃气参数
    parser.add_argument("--gas-price", type=float, default=50, help="燃气价格（Gwei）")
    parser.add_argument("--gas-limit", type=int, default=500000, help="燃气上限")
    parser.add_argument("--max-fee-per-gas", type=float, help="EIP-1559最大费用（Gwei）")
    parser.add_argument("--max-priority-fee-per-gas", type=float, help="EIP-1559最大优先费用（Gwei）")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", required=True, help="要执行的操作")
    
    # 列出代币
    list_tokens_parser = subparsers.add_parser("list", help="列出可桥接的代币")
    
    # 查询余额
    balance_parser = subparsers.add_parser("balance", help="查询代币余额")
    balance_parser.add_argument("--chain", choices=["ethereum", "polygon"], required=True, help="查询的网络")
    token_group = balance_parser.add_mutually_exclusive_group(required=True)
    token_group.add_argument("--token-symbol", help="代币符号")
    token_group.add_argument("--token-address", help="代币合约地址")
    
    # 从Polygon到以太坊桥接
    to_eth_parser = subparsers.add_parser("to-ethereum", help="从Polygon到以太坊桥接代币")
    to_eth_token_group = to_eth_parser.add_mutually_exclusive_group(required=True)
    to_eth_token_group.add_argument("--token-symbol", help="代币符号")
    to_eth_token_group.add_argument("--token-address", help="代币合约地址")
    to_eth_parser.add_argument("--amount", required=True, help="桥接金额")
    to_eth_parser.add_argument("--no-wait", action="store_true", help="不等待自动认领，仅执行销毁步骤")
    to_eth_parser.add_argument("--auto-claim", action="store_true", help="自动监控检查点并认领代币（可能需要等待数小时）")
    to_eth_parser.add_argument("--check-interval", type=int, default=60, help="自动检查间隔（秒），默认60秒")
    to_eth_parser.add_argument("--max-wait-time", type=int, default=21600, help="最大等待时间（秒），默认6小时")
    to_eth_parser.add_argument("--initial-wait-time", type=int, default=1800, help="初始等待时间（秒），默认30分钟")
    
    # 从以太坊到Polygon桥接
    to_polygon_parser = subparsers.add_parser("to-polygon", help="从以太坊到Polygon桥接代币")
    to_polygon_token_group = to_polygon_parser.add_mutually_exclusive_group(required=True)
    to_polygon_token_group.add_argument("--token-symbol", help="代币符号")
    to_polygon_token_group.add_argument("--token-address", help="代币合约地址")
    to_polygon_parser.add_argument("--amount", required=True, help="桥接金额")
    
    # 认领代币
    claim_parser = subparsers.add_parser("claim", help="查询从Polygon提取到以太坊的代币状态")
    claim_parser.add_argument("--burn-tx-hash", required=True, help="Polygon上的销毁交易哈希")
    
    args = parser.parse_args()
    
    # 获取配置
    try:
        # 尝试从命令行参数获取配置，如果不存在则从配置文件获取
        if args.eth_rpc and args.polygon_rpc and (args.private_key or args.private_key_env):
            eth_rpc = args.eth_rpc
            polygon_rpc = args.polygon_rpc
            private_key = args.private_key
            private_key_env = args.private_key_env
        else:
            print("从配置文件读取网络信息...")
            eth_rpc, polygon_rpc, private_key = get_network_config()
            private_key_env = None
            
            # 如命令行参数有提供，则覆盖配置文件
            if args.eth_rpc:
                eth_rpc = args.eth_rpc
            if args.polygon_rpc:
                polygon_rpc = args.polygon_rpc
            if args.private_key:
                private_key = args.private_key
            if args.private_key_env:
                private_key_env = args.private_key_env
                private_key = None
                
        # 打印网络信息
        print(f"以太坊RPC: {eth_rpc}")
        print(f"Polygon RPC: {polygon_rpc}")
        print(f"使用的私钥: {'从环境变量获取' if private_key_env else '从配置文件或参数获取'}")
        
        # 创建桥接工具
        bridge = PolygonEthereumBridge(
            ethereum_rpc_url=eth_rpc,
            polygon_rpc_url=polygon_rpc,
            private_key=private_key,
            private_key_env=private_key_env,
            gas_price_gwei=args.gas_price,
            gas_limit=args.gas_limit,
            max_fee_per_gas=args.max_fee_per_gas,
            max_priority_fee_per_gas=args.max_priority_fee_per_gas
        )
        
    except Exception as e:
        print(f"配置加载失败: {str(e)}")
        print("请检查配置文件或提供必要的命令行参数")
        sys.exit(1)
    
    # 执行操作
    if args.command == "list":
        tokens = bridge.list_tokens()
        print("可桥接的代币列表:")
        print("-" * 80)
        print(f"{'符号':<10} {'名称':<30} {'Polygon地址':<42} {'以太坊地址':<42}")
        print("-" * 80)
        
        for symbol, chain_tokens in tokens.items():
            polygon_token = chain_tokens.get(PolygonBridgeTokens.POLYGON_CHAIN_ID, {})
            eth_token = chain_tokens.get(PolygonBridgeTokens.ETHEREUM_CHAIN_ID, {})
            
            print(f"{symbol:<10} {polygon_token.get('name', ''):<30} {polygon_token.get('address', ''):<42} {eth_token.get('address', ''):<42}")
    
    elif args.command == "balance":
        try:
            # 如果提供的是地址而不是符号，显示提示信息
            if args.token_address or (args.token_symbol and args.token_symbol.startswith('0x')):
                address = args.token_address or args.token_symbol
                print(f"\n提示: 您正在使用代币地址 {address} 进行操作")
                if address.startswith('0x'):
                    print("您可以使用以下命令获取更多信息:")
                    print(f"python -m src.bridge.pol_bridge.check_token {address} --chain {args.chain} --direct")
                
            balance = bridge.get_balance(args.token_symbol, args.token_address, args.chain)
            print(f"地址: {bridge.account.address}")
            print(f"余额: {balance}")
        except ValueError as e:
            print(f"错误: {str(e)}")
            print("\n如果您确信该代币应该可以查询余额，请尝试以下操作:")
            print("1. 使用list命令查看支持的代币列表: python -m src.bridge.pol_bridge.bridge_tokens list")
            print(f"2. 使用check_token工具检查代币是否有跨链映射: python -m src.bridge.pol_bridge.check_token <代币地址> --chain {args.chain} --direct")
            sys.exit(1)
    
    elif args.command == "to-ethereum":
        try:
            # 如果提供的是地址而不是符号，显示提示信息
            if args.token_address or (args.token_symbol and args.token_symbol.startswith('0x')):
                address = args.token_address or args.token_symbol
                print(f"\n提示: 您正在使用代币地址 {address} 进行操作")
                print("如果这是一个新的代币，您可能需要先检查它是否支持桥接")
                print("建议先使用以下命令检查代币映射关系:")
                print(f"python -m src.bridge.pol_bridge.check_token {address} --chain polygon --direct")
                print("如果映射成功，您才能进行桥接操作\n")
            
            # 决定是否自动认领
            wait_for_claim = args.auto_claim and not args.no_wait
            
            result = await bridge.polygon_to_ethereum(
                amount=args.amount, 
                token_symbol=args.token_symbol, 
                token_address=args.token_address,
                wait_for_claim=wait_for_claim,
                auto_check_interval=args.check_interval,
                max_check_time=args.max_wait_time,
                initial_wait_time=args.initial_wait_time
            )
            print("\n桥接结果:")
            print(json.dumps(result, indent=2))
            
            if not wait_for_claim:
                print("\n注意: 代币已在Polygon上销毁，但尚未在以太坊上认领")
                print("请等待交易被包含在检查点中（几个小时），然后运行以下命令查询状态:")
                print(f"python -m src.bridge.pol_bridge.bridge_tokens claim --burn-tx-hash {result['burn_transaction_hash']}")
        except ValueError as e:
            print(f"错误: {str(e)}")
            print("\n如果您确信该代币可以桥接，请尝试以下操作:")
            print("1. 使用list命令查看支持的代币列表: python -m src.bridge.pol_bridge.bridge_tokens list")
            print("2. 使用check_token工具检查代币是否有跨链映射: python -m src.bridge.pol_bridge.check_token <代币地址> --chain polygon --direct")
            sys.exit(1)
    
    elif args.command == "to-polygon":
        try:
            # 如果提供的是地址而不是符号，显示提示信息
            if args.token_address or (args.token_symbol and args.token_symbol.startswith('0x')):
                address = args.token_address or args.token_symbol
                print(f"\n提示: 您正在使用代币地址 {address} 进行操作")
                print("如果这是一个新的代币，您可能需要先检查它是否支持桥接")
                print("建议先使用以下命令检查代币映射关系:")
                print(f"python -m src.bridge.pol_bridge.check_token {address} --chain ethereum --direct")
                print("如果映射成功，您才能进行桥接操作\n")
            
            result = await bridge.ethereum_to_polygon(
                amount=args.amount, 
                token_symbol=args.token_symbol, 
                token_address=args.token_address
            )
            print("\n桥接结果:")
            print(json.dumps(result, indent=2))
            print("\n注意: 代币已从以太坊发送到Polygon网络")
            print("通常需要约20分钟才能在Polygon上到账")
        except ValueError as e:
            print(f"错误: {str(e)}")
            print("\n如果您确信该代币可以桥接，请尝试以下操作:")
            print("1. 使用list命令查看支持的代币列表: python -m src.bridge.pol_bridge.bridge_tokens list")
            print("2. 使用check_token工具检查代币是否有跨链映射: python -m src.bridge.pol_bridge.check_token <代币地址> --chain ethereum --direct")
            sys.exit(1)
    
    elif args.command == "claim":
        try:
            result = await bridge.claim_tokens(args.burn_tx_hash)
            print("\n查询结果:")
            
            if result.get("status") == "confirmed" or result.get("token_received", False):
                print("✅ 交易已确认！代币已成功桥接到以太坊")
                if "tx_block_number" in result and "current_block" in result:
                    print(f"交易区块: {result['tx_block_number']}")
                    print(f"当前区块: {result['current_block']}")
                    print(f"已确认区块: {result['confirmations']}")
            elif result.get("status") == "pending":
                print("⏳ 交易正在等待检查点确认")
                if "tx_block_number" in result and "current_block" in result:
                    print(f"交易区块: {result['tx_block_number']}")
                    print(f"当前区块: {result['current_block']}")
                    print(f"已确认区块: {result['confirmations']}")
                    if "blocks_needed" in result:
                        print(f"还需确认区块: {result['blocks_needed']}")
                    if "message" in result:
                        print(f"估计等待时间: {result['message'].split('预计还需等待约 ')[1] if '预计还需等待约 ' in result['message'] else '未知'}")
            elif result.get("status") in ["not_found", "failed", "error"]:
                print(f"❌ 错误: {result.get('message', '未知错误')}")
            else:
                print(json.dumps(result, indent=2))
                
            # 添加Polygonscan链接
            print(f"\n您可以在这里查看交易: https://polygonscan.com/tx/{args.burn_tx_hash}")
        except ValueError as e:
            print(f"错误: {str(e)}")
            sys.exit(1)

def main():
    """主函数"""
    # 处理Windows特定的问题
    if sys.platform == 'win32':
        # 解决Windows上的asyncio事件循环策略问题
        if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # 设置控制台编码以正确显示中文（Windows特定）
    if sys.platform == 'win32':
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleCP(65001)
            kernel32.SetConsoleOutputCP(65001)
        except Exception:
            print("警告: 无法设置控制台编码，中文显示可能不正确")
    
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n程序已被用户中断")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 