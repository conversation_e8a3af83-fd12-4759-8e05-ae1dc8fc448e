{"version": 3, "file": "web3_request_manager.js", "sourceRoot": "", "sources": ["../../src/web3_request_manager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;AAEF,OAAO,EACN,sBAAsB,EACtB,oBAAoB,EACpB,aAAa,EACb,aAAa,EACb,YAAY,EACZ,QAAQ,GACR,MAAM,aAAa,CAAC;AACrB,OAAO,YAAY,MAAM,qBAAqB,CAAC;AAC/C,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAmB3C,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAC/E,OAAO,EACN,iBAAiB,EACjB,uBAAuB,EACvB,yBAAyB,EACzB,oBAAoB,EACpB,cAAc,GACd,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAG3D,MAAM,CAAN,IAAY,uBAGX;AAHD,WAAY,uBAAuB;IAClC,gEAAqC,CAAA;IACrC,4EAAiD,CAAA;AAClD,CAAC,EAHW,uBAAuB,KAAvB,uBAAuB,QAGlC;AAED,MAAM,kBAAkB,GAGpB;IACH,YAAY,EAAE,YAA2C;IACzD,iBAAiB,EAAE,UAAyC;CAC5D,CAAC;AAEF,MAAM,OAAO,kBAEX,SAAQ,gBAER;IAKD,YACC,QAA2C,EAC3C,uBAAiC,EACjC,wBAAwD;QAExD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;QAEvD,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC;YAAE,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC;IACtF,CAAC;IAED;;OAEG;IACI,MAAM,KAAK,SAAS;QAC1B,OAAO,kBAAkB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QAClB,OAAO,IAAI,CAAC,SAAS,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,kDAAkD;IAClD,IAAW,SAAS;QACnB,OAAO,kBAAkB,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,QAA2C;QAC7D,IAAI,WAAgD,CAAC;QAErD,sBAAsB;QACtB,IAAI,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAChE,OAAO;YACP,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtC,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAM,QAAQ,CAAC,CAAC;gBAE7D,KAAK;YACN,CAAC;iBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3C,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAM,QAAQ,CAAC,CAAC;YACnE,CAAC;iBAAM,CAAC;gBACP,MAAM,IAAI,aAAa,CAAC,kCAAkC,QAAQ,GAAG,CAAC,CAAC;YACxE,CAAC;QACF,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,qCAAqC;YACrC,WAAW,GAAG,SAAS,CAAC;QACzB,CAAC;aAAM,CAAC;YACP,WAAW,GAAG,QAAmC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACb,CAAC;IAEM,aAAa,CAAC,wBAAuD;QAC3E,IAAI,CAAC,UAAU,GAAG,wBAAwB,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG;IACU,IAAI,CAGf,OAAoC;;YACrC,MAAM,UAAU,qBAAQ,OAAO,CAAE,CAAC;YAElC,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAuB,UAAU,CAAC,CAAC;YAEzE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;gBAAE,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAE5F,IAAI,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5C,OAAO,QAAQ,CAAC,MAAM,CAAC;YACxB,CAAC;YAED,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;KAAA;IAED;;;;OAIG;IACU,SAAS,CAAC,OAA4B;;YAClD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAe,OAAO,CAAC,CAAC;YAEhE,OAAO,QAAyC,CAAC;QAClD,CAAC;KAAA;IAEa,YAAY,CAIzB,OAA0D;;YAE1D,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAE1B,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,aAAa,CACtB,wFAAwF,CACxF,CAAC;YACH,CAAC;YAED,IAAI,OAAO,GAAG,CACb,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9B,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBACjC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CACX,CAAC;YAEpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,IAAI,QAAQ,CAAC;gBAEb,IAAI,CAAC;oBACJ,QAAQ,GAAG,MAAM,QAAQ,CAAC,OAAO,CAChC,OAAsC,CACtC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBAChB,oEAAoE;oBACpE,QAAQ,GAAG,KAAsC,CAAC;gBACnD,CAAC;gBACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjC,OAAQ,QAAkC;qBACxC,OAAO,CAAuB,OAAsC,CAAC;qBACrE,IAAI,CACJ,GAAG,CAAC,EAAE,CACL,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,GAAG,EAAE;oBAC1C,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,KAAK;iBACZ,CAA4C,CAC9C;qBACA,KAAK,CAAC,KAAK,CAAC,EAAE,CACd,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,KAA+C,EAC/C,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAC7B,CACD,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,OAAO,CAAgC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrE,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,EAAE;wBACxC,MAAM,CACL,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,GAAoC,EACpC;4BACC,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,IAAI;yBACX,CACD,CACD,CAAC;oBACH,CAAC,CAAC;oBAEF,MAAM,mBAAmB,GAAG,CAAC,QAAuC,EAAE,EAAE,CACvE,OAAO,CACN,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE;wBAC/C,MAAM,EAAE,IAAI;wBACZ,KAAK,EAAE,KAAK;qBACZ,CAAC,CACF,CAAC;oBACH,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAC9B,OAAO;oBACP,uEAAuE;oBACvE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;wBACjB,IAAI,GAAG,EAAE,CAAC;4BACT,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC;wBAC7B,CAAC;wBAED,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBACtC,CAAC,CACD,CAAC;oBACF,8FAA8F;oBAC9F,4DAA4D;oBAC5D,gFAAgF;oBAChF,qHAAqH;oBACrH,gMAAgM;oBAChM,iFAAiF;oBACjF,+EAA+E;oBAC/E,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;wBACvB,MAAM,eAAe,GAAG,MAEvB,CAAC;wBACF,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;4BACvD,IAAI,CAAC;gCACJ,wCAAwC;gCACxC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAClD,OAAO,EACP,KAA+C,EAC/C,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAC7B,CAAC;gCACF,MAAM,CAAC,cAAc,CAAC,CAAC;4BACxB,CAAC;4BAAC,OAAO,eAAe,EAAE,CAAC;gCAC1B,0DAA0D;gCAC1D,MAAM,CAAC,eAAe,CAAC,CAAC;4BACzB,CAAC;wBACF,CAAC,CAAC,CAAC;oBACJ,CAAC;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,OAAO,IAAI,OAAO,CAAgC,CAAC,OAAO,EAAE,MAAM,EAAQ,EAAE;oBAC3E,QAAQ,CAAC,IAAI,CAAe,OAAO,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;wBACtD,IAAI,GAAG,EAAE,CAAC;4BACT,OAAO,MAAM,CACZ,IAAI,CAAC,uBAAuB,CAC3B,OAAO,EACP,GAA+C,EAC/C;gCACC,MAAM,EAAE,IAAI;gCACZ,KAAK,EAAE,IAAI;6BACX,CACD,CACD,CAAC;wBACH,CAAC;wBAED,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACzB,MAAM,IAAI,aAAa,CACtB,EAAW,EACX,yCAAyC,CACzC,CAAC;wBACH,CAAC;wBAED,OAAO,OAAO,CACb,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE;4BAC/C,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,KAAK;yBACZ,CAAC,CACF,CAAC;oBACH,CAAC,CAAC,CAAC;gBACJ,CAAC,CAAC,CAAC;YACJ,CAAC;YAED,8CAA8C;YAC9C,IAAI,yBAAyB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,OAAO,QAAQ;qBACb,SAAS,CAAe,OAAO,CAAC;qBAChC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAChB,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAC/E;qBACA,KAAK,CAAC,KAAK,CAAC,EAAE,CACd,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAsC,EAAE;oBAC7E,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,IAAI;iBACX,CAAC,CACF,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,aAAa,CAAC,yDAAyD,CAAC,CAAC;QACpF,CAAC;KAAA;IAED,kDAAkD;IAC1C,uBAAuB,CAC9B,OAAoC,EACpC,QAAgD,EAChD,EAAE,MAAM,EAAE,KAAK,EAAuC;QAEtD,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,cAAc,CACzB,OAAO;YACP,qDAAqD;YACrD,2CAA2C;YAC3C,IAAyD,EACzD,KAAK,CACL,CAAC;QACH,CAAC;QAED,yDAAyD;QACzD,8CAA8C;QAC9C,IAAI,OAAO,CAAC,mBAAmB,CAAY,QAAQ,CAAC,EAAE,CAAC;YACtD,4BAA4B;YAC5B,IACC,IAAI,CAAC,uBAAuB;gBAC5B,kBAAkB,CAAC,QAAoC,CAAC,EACvD,CAAC;gBACF,MAAM,gBAAgB,GAAG,QAAoC,CAAC;gBAC9D,uGAAuG;gBACvG,IAAI,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnD,oEAAoE;oBACpE,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC,KAAK,CAAC;oBACjE,MAAM,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACP,MAAM,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBACtC,CAAC;YACF,CAAC;iBAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,oBAAoB,CAAyB,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC3E,CAAC;QACF,CAAC;QAED,yDAAyD;QACzD,+CAA+C;QAC/C,IAAI,OAAO,CAAC,oBAAoB,CAAa,QAAQ,CAAC,EAAE,CAAC;YACxD,OAAO,QAAQ,CAAC;QACjB,CAAC;QAED,IAAK,QAAoB,YAAY,KAAK,EAAE,CAAC;YAC5C,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,QAAQ,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrF,OAAO,QAA4C,CAAC;QACrD,CAAC;QAED,IAAI,MAAM,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACzD,OAAO,QAA4C,CAAC;QACrD,CAAC;QAED,IAAI,MAAM,IAAI,KAAK,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YACxD,0EAA0E;YAC1E,MAAM,QAAQ,CAAC;QAChB,CAAC;QAED,IACC,MAAM;YACN,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC;YACtC,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EACtC,CAAC;YACF,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE,0CAA0C,CAAC,CAAC;QAC/E,CAAC;QAED,MAAM,IAAI,aAAa,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACvD,CAAC;IAEO,MAAM,CAAC,WAAW,CACzB,QAAgD;QAEhD,IAAI,KAA+B,CAAC;QAEpC,IAAI,OAAO,CAAC,mBAAmB,CAAY,QAAQ,CAAC,EAAE,CAAC;YACtD,KAAK,GAAI,QAAqC,CAAC,KAAK,CAAC;QACtD,CAAC;aAAM,IAAK,QAAoB,YAAY,KAAK,EAAE,CAAC;YACnD,KAAK,GAAG,QAAmC,CAAC;QAC7C,CAAC;QAED,4FAA4F;QAC5F,mFAAmF;QACnF,iEAAiE;QACjE,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,MAAM,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAE/E,OAAO,KAAK,CAAC;IACd,CAAC;IACD,0FAA0F;IAC1F,kDAAkD;IAC1C,cAAc,CACrB,OAAoC,EACpC,QAAgD,EAChD,KAAc;QAEd,MAAM,GAAG,GAAG;YACX,OAAO,EAAE,KAAK;YACd,6CAA6C;YAC7C,EAAE,EAAE,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gBAClC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;gBACf,CAAC,CAAC,IAAI,IAAI,OAAO;oBACjB,CAAC,CAAC,OAAO,CAAC,EAAE;oBACZ,CAAC,CAAC,uCAAuC;wBACvC,2CAA2C;wBAC3C,IAAI;SACP,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACX,OAAO,gCACH,GAAG,KACN,KAAK,EAAE,QAAmB,GACK,CAAC;QAClC,CAAC;QAED,OAAO,gCACH,GAAG,KACN,MAAM,EAAE,QAAmB,GACI,CAAC;IAClC,CAAC;CACD"}