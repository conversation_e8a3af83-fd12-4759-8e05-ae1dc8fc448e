{"version": 3, "file": "slip10.js", "sourceRoot": "", "sources": ["../src/slip10.ts"], "names": [], "mappings": ";;;;;;AAAA,+CAAoD;AACpD,uCAA8C;AAC9C,kDAAuB;AACvB,wDAAgC;AAEhC,iCAA8B;AAC9B,+BAA+B;AAO/B;;;;GAIG;AACH,IAAY,WAGX;AAHD,WAAY,WAAW;IACrB,yCAA0B,CAAA;IAC1B,uCAAwB,CAAA;AAC1B,CAAC,EAHW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAGtB;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,WAAmB;IACvD,QAAQ,WAAW,EAAE;QACnB,KAAK,WAAW,CAAC,OAAO;YACtB,OAAO,WAAW,CAAC,OAAO,CAAC;QAC7B,KAAK,WAAW,CAAC,SAAS;YACxB,OAAO,WAAW,CAAC,SAAS,CAAC;QAC/B;YACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,WAAW,GAAG,CAAC,CAAC;KAC7D;AACH,CAAC;AATD,sDASC;AAED,MAAa,cAAe,SAAQ,aAAM;IACjC,MAAM,CAAC,QAAQ,CAAC,aAAqB;QAC1C,OAAO,IAAI,cAAc,CAAC,aAAa,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAEM,MAAM,CAAC,MAAM,CAAC,WAAmB;QACtC,OAAO,IAAI,cAAc,CAAC,WAAW,CAAC,CAAC;IACzC,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;CACF;AAZD,wCAYC;AA8BD,MAAM,SAAS,GAAG,IAAI,kBAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;AAE/C,+CAA+C;AAC/C,gEAAgE;AAChE,MAAa,MAAM;IACV,MAAM,CAAC,UAAU,CAAC,KAAkB,EAAE,IAAgB,EAAE,IAAY;QACzE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE;YAC3B,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACxE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,MAAM,CAAC,KAAkB,EAAE,IAAgB;QACxD,MAAM,CAAC,GAAG,IAAI,WAAI,CAAC,YAAM,EAAE,IAAA,kBAAO,EAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;QACjE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,IAAI,KAAK,KAAK,WAAW,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;YAChF,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC9B;QAED,OAAO;YACL,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAE;SACZ,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,KAAK,CAClB,KAAkB,EAClB,aAAyB,EACzB,eAA2B,EAC3B,QAAwB;QAExB,IAAI,CAAa,CAAC;QAClB,IAAI,QAAQ,CAAC,UAAU,EAAE,EAAE;YACzB,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,aAAa,EAAE,GAAG,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;YACzF,CAAC,GAAG,IAAI,WAAI,CAAC,YAAM,EAAE,eAAe,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;SAChE;aAAM;YACL,IAAI,KAAK,KAAK,WAAW,CAAC,OAAO,EAAE;gBACjC,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;aAC7D;iBAAM;gBACL,gHAAgH;gBAChH,mFAAmF;gBACnF,kEAAkE;gBAClE,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC;oBAC1B,GAAG,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,eAAE,CAAC,aAAa,CAAC,CAAC;oBACvD,GAAG,QAAQ,CAAC,gBAAgB,EAAE;iBAC/B,CAAC,CAAC;gBACH,CAAC,GAAG,IAAI,WAAI,CAAC,YAAM,EAAE,eAAe,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;aAC7D;SACF;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,eAAe,CAAC,KAAkB,EAAE,CAAK;QACtD,QAAQ,KAAK,EAAE;YACb,KAAK,WAAW,CAAC,SAAS;gBACxB,OAAO,IAAA,kBAAO,EAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7D;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;IACH,CAAC;IAEO,MAAM,CAAC,SAAS,CACtB,KAAkB,EAClB,aAAyB,EACzB,eAA2B,EAC3B,QAAwB,EACxB,CAAa;QAEb,mEAAmE;QAEnE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1B,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAE3B,SAAS;QACT,MAAM,eAAe,GAAG,EAAE,CAAC;QAE3B,SAAS;QACT,IAAI,KAAK,KAAK,WAAW,CAAC,OAAO,EAAE;YACjC,OAAO;gBACL,SAAS,EAAE,eAAe;gBAC1B,OAAO,EAAE,EAAE;aACZ,CAAC;SACH;QAED,SAAS;QACT,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACxB,MAAM,sBAAsB,GAAG,IAAI,eAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,eAAE,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5E,MAAM,cAAc,GAAG,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QAEjF,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;YACzD,MAAM,IAAI,GAAG,IAAI,WAAI,CAAC,YAAM,EAAE,eAAe,CAAC;iBAC3C,MAAM,CAAC,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,GAAG,QAAQ,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;iBACrE,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC9E;QAED,SAAS;QACT,OAAO;YACL,SAAS,EAAE,eAAe;YAC1B,OAAO,EAAE,cAAc;SACxB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,MAAM,CAAC,OAAmB;QACvC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEO,MAAM,CAAC,MAAM,CAAC,KAAkB,EAAE,OAAmB;QAC3D,MAAM,WAAW,GAAG,IAAI,eAAE,CAAC,OAAO,CAAC,CAAC;QACpC,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACxC,CAAC;IAEO,MAAM,CAAC,CAAC,CAAC,KAAkB;QACjC,QAAQ,KAAK,EAAE;YACb,KAAK,WAAW,CAAC,SAAS;gBACxB,OAAO,IAAI,eAAE,CAAC,kEAAkE,EAAE,EAAE,CAAC,CAAC;YACxF;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SAC1C;IACH,CAAC;CACF;AA9HD,wBA8HC;AAED,SAAgB,YAAY,CAAC,IAAY;IACvC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,SAAS,EAAU,EAAE;QAChD,MAAM,eAAe,GAAG,SAAS,CAAC,UAAU,EAAE;YAC5C,CAAC,CAAC,GAAG,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG;YACtC,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACzB,OAAO,OAAO,GAAG,GAAG,GAAG,eAAe,CAAC;IACzC,CAAC,EAAE,GAAG,CAAC,CAAC;AACV,CAAC;AAPD,oCAOC;AAED,SAAgB,YAAY,CAAC,KAAa;IACxC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC/E,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE1B,MAAM,GAAG,GAAG,IAAI,KAAK,EAAkB,CAAC;IACxC,OAAO,IAAI,EAAE;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK;YAAE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACzE,MAAM,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC;QACpD,MAAM,KAAK,GAAG,aAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;QACzD,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC5F,IAAI,UAAU;YAAE,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;YACpD,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACrC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAhBD,oCAgBC"}