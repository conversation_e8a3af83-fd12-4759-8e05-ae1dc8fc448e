"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerRestClient = void 0;
const IndexerRestDerivativesChronosApi_js_1 = require("./rest/IndexerRestDerivativesChronosApi.js");
const IndexerRestExplorerApi_js_1 = require("./rest/IndexerRestExplorerApi.js");
const IndexerRestSpotChronosApi_js_1 = require("./rest/IndexerRestSpotChronosApi.js");
/**
 * @category Indexer Grpc API
 * @hidden
 */
class IndexerRestClient {
    derivativesChronos;
    spotChronos;
    explorer;
    constructor(endpoints) {
        const chronosBase = `${endpoints.chronosApi
            ? `${endpoints.chronosApi}/api/v1`
            : `${endpoints.indexerApi}/api/chronos/v1`}`;
        this.explorer = new IndexerRestExplorerApi_js_1.IndexerRestExplorerApi(`${chronosBase}/api/explorer/v1`);
        this.derivativesChronos = new IndexerRestDerivativesChronosApi_js_1.IndexerRestDerivativesChronosApi(`${chronosBase}/derivative`);
        this.spotChronos = new IndexerRestSpotChronosApi_js_1.IndexerRestSpotChronosApi(`${chronosBase}/spot`);
    }
}
exports.IndexerRestClient = IndexerRestClient;
