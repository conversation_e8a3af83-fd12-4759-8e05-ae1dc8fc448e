{"version": 3, "sources": ["../../src/bcs/consts.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Uint8, Uint16, Uint32, Uint64, Uint128, Uint256 } from \"../types\";\n\n// Upper bound values for uint8, uint16, uint64 etc.  These are all derived as\n// 2^N - 1, where N is the number of bits in the type.\nexport const MAX_U8_NUMBER: Uint8 = 255;\nexport const MAX_U16_NUMBER: Uint16 = 65535;\nexport const MAX_U32_NUMBER: Uint32 = **********;\nexport const MAX_U64_BIG_INT: Uint64 = 18446744073709551615n;\nexport const MAX_U128_BIG_INT: Uint128 = 340282366920938463463374607431768211455n;\nexport const MAX_U256_BIG_INT: Uint256 =\n  115792089237316195423570985008687907853269984665640564039457584007913129639935n;\n"], "mappings": "AAOO,IAAMA,EAAuB,IACvBC,EAAyB,MACzBC,EAAyB,WACzBC,EAA0B,sBAC1BC,EAA4B,yCAC5BC,EACX", "names": ["MAX_U8_NUMBER", "MAX_U16_NUMBER", "MAX_U32_NUMBER", "MAX_U64_BIG_INT", "MAX_U128_BIG_INT", "MAX_U256_BIG_INT"]}