{"version": 3, "file": "websocketclient.js", "sourceRoot": "", "sources": ["../../src/rpcclients/websocketclient.ts"], "names": [], "mappings": ";;;AAAA,+CAO0B;AAC1B,2CAAiG;AACjG,2CAA4C;AAC5C,qCAAmE;AAEnE,2CAAiF;AAEjF,SAAS,mBAAmB,CAAC,KAAU;IACrC,MAAM,KAAK,CAAC;AACd,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAkC;IAC3D,2DAA2D;IAC3D,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,yCAAyC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;KAC1E;IAED,MAAM,YAAY,GAAG,IAAA,+BAAoB,EAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,MAAM,gBAAgB;IAOpB,YAAmB,OAAuB,EAAE,MAA0B;QAH9D,YAAO,GAAG,KAAK,CAAC;QAChB,kBAAa,GAAmB,EAAE,CAAC;QAGzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAqC;QAChD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,KAAK,CAAC,uDAAuD,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG;IACI,IAAI;QACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,yFAAyF;QACzF,uFAAuF;QACvF,MAAM,UAAU,GAAmB,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;QAC9E,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;SACtD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE;gBAC7E,SAAS;aACV;iBAAM;gBACL,MAAM,KAAK,CAAC;aACb;SACF;IACH,CAAC;IAES,eAAe,CAAC,QAAqC;QAC7D,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEjE,2EAA2E;QAC3E,MAAM,cAAc,GAAG,cAAc;aAClC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;aACrD,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,IAAA,iCAAsB,EAAC,QAAQ,CAAC,EAAE;oBACpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;iBAChD;gBACD,cAAc,CAAC,WAAW,EAAE,CAAC;YAC/B,CAAC;SACF,CAAC,CAAC;QAEL,kDAAkD;QAClD,oFAAoF;QACpF,gFAAgF;QAChF,MAAM,mBAAmB,GAAG,cAAc;aACvC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;aACrD,SAAS,CAAC;YACT,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACjB,IAAI,IAAA,iCAAsB,EAAC,QAAQ,CAAC,EAAE;oBACpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;iBAChD;qBAAM;oBACL,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAA2B,CAAC,CAAC;iBACrD;YACH,CAAC;SACF,CAAC,CAAC;QAEL,2DAA2D;QAC3D,MAAM,uBAAuB,GAAG,cAAc,CAAC,SAAS,CAAC;YACvD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;YACD,QAAQ,EAAE,GAAG,EAAE;gBACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,mBAAmB,EAAE,uBAAuB,CAAC,CAAC;IACxF,CAAC;IAES,kBAAkB;QAC1B,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,EAAE;YAC7C,YAAY,CAAC,WAAW,EAAE,CAAC;SAC5B;QACD,6BAA6B;QAC7B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;CACF;AAED,MAAa,eAAe;IAY1B,YAAmB,OAAe,EAAE,UAA8B,mBAAmB;QANrF,wFAAwF;QACxF,EAAE;QACF,yGAAyG;QACzG,6FAA6F;QAC5E,wBAAmB,GAAG,IAAI,GAAG,EAAqC,CAAC;QAGlF,IAAI,CAAC,IAAA,uBAAW,EAAC,OAAO,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;SAClF;QAED,gDAAgD;QAChD,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;QAChE,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,MAAM,GAAG,IAAI,2BAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YACrD,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC,CAAC;gBACf,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAClC,CAAC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAEvE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,OAAuB;QAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAElD,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC;QACvC,IAAI,IAAA,iCAAsB,EAAC,QAAQ,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,OAAuB;QACnC,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;SAChF;QAED,MAAM,KAAK,GAAI,OAAO,CAAC,MAAc,CAAC,KAAK,CAAC;QAC5C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5D,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SAC7C;QACD,oEAAoE;QACpE,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;IACjG,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,SAAS;QACpB,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,yBAAgB,CAAC,SAAS,CAAC,CAAC;IACzE,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAES,KAAK,CAAC,oBAAoB,CAAC,EAAa;QAChD,OAAO,IAAA,mBAAU,EAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;CACF;AAhFD,0CAgFC"}