import{b as u}from"./chunk-HETYL3WN.mjs";async function i(e){let{aptosConfig:t,overrides:s,params:r,contentType:n,acceptType:a,path:p,originMethod:o,type:R}=e,d=t.getRequestUrl(R);return u({url:d,method:"GET",originMethod:o,path:p,contentType:n,acceptType:a,params:r,overrides:{...t.clientConfig,...s}},t,e.type)}async function y(e){let{aptosConfig:t}=e;return i({...e,type:"Fullnode",overrides:{...t.clientConfig,...t.fullnodeConfig,...e.overrides,HEADERS:{...t.clientConfig?.HEADERS,...t.fullnodeConfig?.HEADERS}}})}async function l(e){return i({...e,type:"Pepper"})}async function q(e){let t=new Array(0),s,r=e.params;do{let n=await i({type:"Fullnode",aptosConfig:e.aptosConfig,originMethod:e.originMethod,path:e.path,params:r,overrides:e.overrides});s=n.headers["x-aptos-cursor"],delete n.headers,t.push(...n.data),r.start=s}while(s!=null);return t}async function A(e){let t=new Array(0),s,r=e.params,n=r.limit;do{let{response:a,cursor:p}=await m({...e});if(s=p,t.push(...a.data),e?.params&&(e.params.start=s),n!==void 0){let o=n-t.length;if(o<=0)break;r.limit=o}}while(s!=null);return t}async function m(e){let t,s={};typeof e.params?.cursor=="string"&&(s.start=e.params.cursor),typeof e.params?.limit=="number"&&(s.limit=e.params.limit);let r=await i({type:"Fullnode",aptosConfig:e.aptosConfig,originMethod:e.originMethod,path:e.path,params:s,overrides:e.overrides});return t=r.headers["x-aptos-cursor"],{response:r,cursor:t}}export{i as a,y as b,l as c,q as d,A as e,m as f};
//# sourceMappingURL=chunk-CZYH3G7E.mjs.map