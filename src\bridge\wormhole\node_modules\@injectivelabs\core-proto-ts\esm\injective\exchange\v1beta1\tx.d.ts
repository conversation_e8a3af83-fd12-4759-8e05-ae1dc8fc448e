import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { OracleType } from "../../oracle/v1beta1/oracle";
import { DerivativeOrder, GrantAuthorization, MarketStatus, Params, PositionDelta, SpotOrder } from "./exchange";
import { BatchExchangeModificationProposal } from "./proposal";
export declare const protobufPackage = "injective.exchange.v1beta1";
export interface MsgUpdateSpotMarket {
    /** current admin address of the associated market */
    admin: string;
    /** id of the market to be updated */
    marketId: string;
    /** (optional) updated ticker value */
    newTicker: string;
    /** (optional) updated min price tick size value */
    newMinPriceTickSize: string;
    /** (optional) updated min quantity tick size value */
    newMinQuantityTickSize: string;
    /** (optional) updated min notional */
    newMinNotional: string;
}
export interface MsgUpdateSpotMarketResponse {
}
export interface MsgUpdateDerivativeMarket {
    /** current admin address of the associated market */
    admin: string;
    /** id of the market to be updated */
    marketId: string;
    /** (optional) updated value for ticker */
    newTicker: string;
    /** (optional) updated value for min_price_tick_size */
    newMinPriceTickSize: string;
    /** (optional) updated value min_quantity_tick_size */
    newMinQuantityTickSize: string;
    /** (optional) updated min notional */
    newMinNotional: string;
    /** (optional) updated value for initial_margin_ratio */
    newInitialMarginRatio: string;
    /** (optional) updated value for maintenance_margin_ratio */
    newMaintenanceMarginRatio: string;
}
export interface MsgUpdateDerivativeMarketResponse {
}
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the exchange parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
/**
 * MsgDeposit defines a SDK message for transferring coins from the sender's
 * bank balance into the subaccount's exchange deposits
 */
export interface MsgDeposit {
    sender: string;
    /**
     * (Optional) bytes32 subaccount ID to deposit funds into. If empty, the coin
     * will be deposited to the sender's default subaccount address.
     */
    subaccountId: string;
    amount: Coin | undefined;
}
/** MsgDepositResponse defines the Msg/Deposit response type. */
export interface MsgDepositResponse {
}
/**
 * MsgWithdraw defines a SDK message for withdrawing coins from a subaccount's
 * deposits to the user's bank balance
 */
export interface MsgWithdraw {
    sender: string;
    /** bytes32 subaccount ID to withdraw funds from */
    subaccountId: string;
    amount: Coin | undefined;
}
/** MsgWithdraw defines the Msg/Withdraw response type. */
export interface MsgWithdrawResponse {
}
/**
 * MsgCreateSpotLimitOrder defines a SDK message for creating a new spot limit
 * order.
 */
export interface MsgCreateSpotLimitOrder {
    sender: string;
    order: SpotOrder | undefined;
}
/**
 * MsgCreateSpotLimitOrderResponse defines the Msg/CreateSpotOrder response
 * type.
 */
export interface MsgCreateSpotLimitOrderResponse {
    orderHash: string;
    cid: string;
}
/**
 * MsgBatchCreateSpotLimitOrders defines a SDK message for creating a new batch
 * of spot limit orders.
 */
export interface MsgBatchCreateSpotLimitOrders {
    sender: string;
    orders: SpotOrder[];
}
/**
 * MsgBatchCreateSpotLimitOrdersResponse defines the
 * Msg/BatchCreateSpotLimitOrders response type.
 */
export interface MsgBatchCreateSpotLimitOrdersResponse {
    orderHashes: string[];
    createdOrdersCids: string[];
    failedOrdersCids: string[];
}
/**
 * MsgInstantSpotMarketLaunch defines a SDK message for creating a new spot
 * market by paying listing fee without governance
 */
export interface MsgInstantSpotMarketLaunch {
    sender: string;
    /** Ticker for the spot market. */
    ticker: string;
    /** type of coin to use as the base currency */
    baseDenom: string;
    /** type of coin to use as the quote currency */
    quoteDenom: string;
    /** min_price_tick_size defines the minimum tick size of the order's price */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
    /** base token decimals */
    baseDecimals: number;
    /** quote token decimals */
    quoteDecimals: number;
}
/**
 * MsgInstantSpotMarketLaunchResponse defines the Msg/InstantSpotMarketLaunch
 * response type.
 */
export interface MsgInstantSpotMarketLaunchResponse {
}
/**
 * MsgInstantPerpetualMarketLaunch defines a SDK message for creating a new
 * perpetual futures market by paying listing fee without governance
 */
export interface MsgInstantPerpetualMarketLaunch {
    sender: string;
    /** Ticker for the derivative market. */
    ticker: string;
    /** type of coin to use as the base currency */
    quoteDenom: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Oracle type */
    oracleType: OracleType;
    /**
     * maker_fee_rate defines the trade fee rate for makers on the perpetual
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the trade fee rate for takers on the perpetual
     * market
     */
    takerFeeRate: string;
    /**
     * initial_margin_ratio defines the initial margin ratio for the perpetual
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio for the
     * perpetual market
     */
    maintenanceMarginRatio: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
}
/**
 * MsgInstantPerpetualMarketLaunchResponse defines the
 * Msg/InstantPerpetualMarketLaunchResponse response type.
 */
export interface MsgInstantPerpetualMarketLaunchResponse {
}
/**
 * MsgInstantBinaryOptionsMarketLaunch defines a SDK message for creating a new
 * perpetual futures market by paying listing fee without governance
 */
export interface MsgInstantBinaryOptionsMarketLaunch {
    sender: string;
    /** Ticker for the derivative contract. */
    ticker: string;
    /** Oracle symbol */
    oracleSymbol: string;
    /** Oracle Provider */
    oracleProvider: string;
    /** Oracle type */
    oracleType: OracleType;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /**
     * maker_fee_rate defines the trade fee rate for makers on the perpetual
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the trade fee rate for takers on the perpetual
     * market
     */
    takerFeeRate: string;
    /** expiration timestamp */
    expirationTimestamp: string;
    /** expiration timestamp */
    settlementTimestamp: string;
    /** admin of the market */
    admin: string;
    /** Address of the quote currency denomination for the binary options contract */
    quoteDenom: string;
    /**
     * min_price_tick_size defines the minimum tick size that the price and margin
     * required for orders in the market
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the quantity
     * required for orders in the market
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
}
/**
 * MsgInstantBinaryOptionsMarketLaunchResponse defines the
 * Msg/InstantBinaryOptionsMarketLaunchResponse response type.
 */
export interface MsgInstantBinaryOptionsMarketLaunchResponse {
}
/**
 * MsgInstantExpiryFuturesMarketLaunch defines a SDK message for creating a new
 * expiry futures market by paying listing fee without governance
 */
export interface MsgInstantExpiryFuturesMarketLaunch {
    sender: string;
    /** Ticker for the derivative market. */
    ticker: string;
    /** type of coin to use as the quote currency */
    quoteDenom: string;
    /** Oracle base currency */
    oracleBase: string;
    /** Oracle quote currency */
    oracleQuote: string;
    /** Oracle type */
    oracleType: OracleType;
    /** Scale factor for oracle prices. */
    oracleScaleFactor: number;
    /** Expiration time of the market */
    expiry: string;
    /**
     * maker_fee_rate defines the trade fee rate for makers on the expiry futures
     * market
     */
    makerFeeRate: string;
    /**
     * taker_fee_rate defines the trade fee rate for takers on the expiry futures
     * market
     */
    takerFeeRate: string;
    /**
     * initial_margin_ratio defines the initial margin ratio for the derivative
     * market
     */
    initialMarginRatio: string;
    /**
     * maintenance_margin_ratio defines the maintenance margin ratio for the
     * derivative market
     */
    maintenanceMarginRatio: string;
    /**
     * min_price_tick_size defines the minimum tick size of the order's price and
     * margin
     */
    minPriceTickSize: string;
    /**
     * min_quantity_tick_size defines the minimum tick size of the order's
     * quantity
     */
    minQuantityTickSize: string;
    /**
     * min_notional defines the minimum notional (in quote asset) required for
     * orders in the market
     */
    minNotional: string;
}
/**
 * MsgInstantExpiryFuturesMarketLaunchResponse defines the
 * Msg/InstantExpiryFuturesMarketLaunch response type.
 */
export interface MsgInstantExpiryFuturesMarketLaunchResponse {
}
/**
 * MsgCreateSpotMarketOrder defines a SDK message for creating a new spot market
 * order.
 */
export interface MsgCreateSpotMarketOrder {
    sender: string;
    order: SpotOrder | undefined;
}
/**
 * MsgCreateSpotMarketOrderResponse defines the Msg/CreateSpotMarketLimitOrder
 * response type.
 */
export interface MsgCreateSpotMarketOrderResponse {
    orderHash: string;
    results: SpotMarketOrderResults | undefined;
    cid: string;
}
export interface SpotMarketOrderResults {
    quantity: string;
    price: string;
    fee: string;
}
/** A Cosmos-SDK MsgCreateDerivativeLimitOrder */
export interface MsgCreateDerivativeLimitOrder {
    sender: string;
    order: DerivativeOrder | undefined;
}
/**
 * MsgCreateDerivativeLimitOrderResponse defines the
 * Msg/CreateDerivativeMarketOrder response type.
 */
export interface MsgCreateDerivativeLimitOrderResponse {
    orderHash: string;
    cid: string;
}
/** A Cosmos-SDK MsgCreateBinaryOptionsLimitOrder */
export interface MsgCreateBinaryOptionsLimitOrder {
    sender: string;
    order: DerivativeOrder | undefined;
}
/**
 * MsgCreateBinaryOptionsLimitOrderResponse defines the
 * Msg/CreateBinaryOptionsLimitOrder response type.
 */
export interface MsgCreateBinaryOptionsLimitOrderResponse {
    orderHash: string;
    cid: string;
}
/** A Cosmos-SDK MsgBatchCreateDerivativeLimitOrders */
export interface MsgBatchCreateDerivativeLimitOrders {
    sender: string;
    orders: DerivativeOrder[];
}
/**
 * MsgBatchCreateDerivativeLimitOrdersResponse defines the
 * Msg/BatchCreateDerivativeLimitOrders response type.
 */
export interface MsgBatchCreateDerivativeLimitOrdersResponse {
    orderHashes: string[];
    createdOrdersCids: string[];
    failedOrdersCids: string[];
}
/** MsgCancelSpotOrder defines the Msg/CancelSpotOrder response type. */
export interface MsgCancelSpotOrder {
    sender: string;
    marketId: string;
    subaccountId: string;
    orderHash: string;
    cid: string;
}
/** MsgCancelSpotOrderResponse defines the Msg/CancelSpotOrder response type. */
export interface MsgCancelSpotOrderResponse {
}
/** MsgBatchCancelSpotOrders defines the Msg/BatchCancelSpotOrders response type. */
export interface MsgBatchCancelSpotOrders {
    sender: string;
    data: OrderData[];
}
/**
 * MsgBatchCancelSpotOrdersResponse defines the Msg/BatchCancelSpotOrders
 * response type.
 */
export interface MsgBatchCancelSpotOrdersResponse {
    success: boolean[];
}
/**
 * MsgBatchCancelBinaryOptionsOrders defines the
 * Msg/BatchCancelBinaryOptionsOrders response type.
 */
export interface MsgBatchCancelBinaryOptionsOrders {
    sender: string;
    data: OrderData[];
}
/**
 * BatchCancelBinaryOptionsOrdersResponse defines the
 * Msg/BatchCancelBinaryOptionsOrders response type.
 */
export interface MsgBatchCancelBinaryOptionsOrdersResponse {
    success: boolean[];
}
/** MsgBatchUpdateOrders defines the Msg/BatchUpdateOrders response type. */
export interface MsgBatchUpdateOrders {
    sender: string;
    /**
     * subaccount_id only used for the spot_market_ids_to_cancel_all and
     * derivative_market_ids_to_cancel_all.
     */
    subaccountId: string;
    spotMarketIdsToCancelAll: string[];
    derivativeMarketIdsToCancelAll: string[];
    spotOrdersToCancel: OrderData[];
    derivativeOrdersToCancel: OrderData[];
    spotOrdersToCreate: SpotOrder[];
    derivativeOrdersToCreate: DerivativeOrder[];
    binaryOptionsOrdersToCancel: OrderData[];
    binaryOptionsMarketIdsToCancelAll: string[];
    binaryOptionsOrdersToCreate: DerivativeOrder[];
}
/** MsgBatchUpdateOrdersResponse defines the Msg/BatchUpdateOrders response type. */
export interface MsgBatchUpdateOrdersResponse {
    spotCancelSuccess: boolean[];
    derivativeCancelSuccess: boolean[];
    spotOrderHashes: string[];
    derivativeOrderHashes: string[];
    binaryOptionsCancelSuccess: boolean[];
    binaryOptionsOrderHashes: string[];
    createdSpotOrdersCids: string[];
    failedSpotOrdersCids: string[];
    createdDerivativeOrdersCids: string[];
    failedDerivativeOrdersCids: string[];
    createdBinaryOptionsOrdersCids: string[];
    failedBinaryOptionsOrdersCids: string[];
}
/** A Cosmos-SDK MsgCreateDerivativeMarketOrder */
export interface MsgCreateDerivativeMarketOrder {
    sender: string;
    order: DerivativeOrder | undefined;
}
/**
 * MsgCreateDerivativeMarketOrderResponse defines the
 * Msg/CreateDerivativeMarketOrder response type.
 */
export interface MsgCreateDerivativeMarketOrderResponse {
    orderHash: string;
    results: DerivativeMarketOrderResults | undefined;
    cid: string;
}
export interface DerivativeMarketOrderResults {
    quantity: string;
    price: string;
    fee: string;
    positionDelta: PositionDelta | undefined;
    payout: string;
}
/** A Cosmos-SDK MsgCreateBinaryOptionsMarketOrder */
export interface MsgCreateBinaryOptionsMarketOrder {
    sender: string;
    order: DerivativeOrder | undefined;
}
/**
 * MsgCreateBinaryOptionsMarketOrderResponse defines the
 * Msg/CreateBinaryOptionsMarketOrder response type.
 */
export interface MsgCreateBinaryOptionsMarketOrderResponse {
    orderHash: string;
    results: DerivativeMarketOrderResults | undefined;
    cid: string;
}
/** MsgCancelDerivativeOrder defines the Msg/CancelDerivativeOrder response type. */
export interface MsgCancelDerivativeOrder {
    sender: string;
    marketId: string;
    subaccountId: string;
    orderHash: string;
    /** bitwise combination of OrderMask enum values */
    orderMask: number;
    cid: string;
}
/**
 * MsgCancelDerivativeOrderResponse defines the
 * Msg/CancelDerivativeOrderResponse response type.
 */
export interface MsgCancelDerivativeOrderResponse {
}
/**
 * MsgCancelBinaryOptionsOrder defines the Msg/CancelBinaryOptionsOrder response
 * type.
 */
export interface MsgCancelBinaryOptionsOrder {
    sender: string;
    marketId: string;
    subaccountId: string;
    orderHash: string;
    /** bitwise combination of OrderMask enum values */
    orderMask: number;
    cid: string;
}
/**
 * MsgCancelBinaryOptionsOrderResponse defines the
 * Msg/CancelBinaryOptionsOrderResponse response type.
 */
export interface MsgCancelBinaryOptionsOrderResponse {
}
export interface OrderData {
    marketId: string;
    subaccountId: string;
    orderHash: string;
    /** bitwise combination of OrderMask enum values */
    orderMask: number;
    cid: string;
}
/**
 * MsgBatchCancelDerivativeOrders defines the Msg/CancelDerivativeOrders
 * response type.
 */
export interface MsgBatchCancelDerivativeOrders {
    sender: string;
    data: OrderData[];
}
/**
 * MsgBatchCancelDerivativeOrdersResponse defines the
 * Msg/CancelDerivativeOrderResponse response type.
 */
export interface MsgBatchCancelDerivativeOrdersResponse {
    success: boolean[];
}
/** A Cosmos-SDK MsgSubaccountTransfer */
export interface MsgSubaccountTransfer {
    sender: string;
    sourceSubaccountId: string;
    destinationSubaccountId: string;
    amount: Coin | undefined;
}
/**
 * MsgSubaccountTransferResponse defines the Msg/SubaccountTransfer response
 * type.
 */
export interface MsgSubaccountTransferResponse {
}
/** A Cosmos-SDK MsgExternalTransfer */
export interface MsgExternalTransfer {
    sender: string;
    sourceSubaccountId: string;
    destinationSubaccountId: string;
    amount: Coin | undefined;
}
/** MsgExternalTransferResponse defines the Msg/ExternalTransfer response type. */
export interface MsgExternalTransferResponse {
}
/** A Cosmos-SDK MsgLiquidatePosition */
export interface MsgLiquidatePosition {
    sender: string;
    subaccountId: string;
    marketId: string;
    /** optional order to provide for liquidation */
    order: DerivativeOrder | undefined;
}
/** MsgLiquidatePositionResponse defines the Msg/LiquidatePosition response type. */
export interface MsgLiquidatePositionResponse {
}
/** A Cosmos-SDK MsgEmergencySettleMarket */
export interface MsgEmergencySettleMarket {
    sender: string;
    subaccountId: string;
    marketId: string;
}
/**
 * MsgEmergencySettleMarketResponse defines the Msg/EmergencySettleMarket
 * response type.
 */
export interface MsgEmergencySettleMarketResponse {
}
/** A Cosmos-SDK MsgIncreasePositionMargin */
export interface MsgIncreasePositionMargin {
    sender: string;
    sourceSubaccountId: string;
    destinationSubaccountId: string;
    marketId: string;
    /** amount defines the amount of margin to add to the position */
    amount: string;
}
/**
 * MsgIncreasePositionMarginResponse defines the Msg/IncreasePositionMargin
 * response type.
 */
export interface MsgIncreasePositionMarginResponse {
}
/** A Cosmos-SDK MsgDecreasePositionMargin */
export interface MsgDecreasePositionMargin {
    sender: string;
    sourceSubaccountId: string;
    destinationSubaccountId: string;
    marketId: string;
    /** amount defines the amount of margin to withdraw from the position */
    amount: string;
}
/**
 * MsgDecreasePositionMarginResponse defines the Msg/MsgDecreasePositionMargin
 * response type.
 */
export interface MsgDecreasePositionMarginResponse {
}
/** MsgPrivilegedExecuteContract defines the Msg/Exec message type */
export interface MsgPrivilegedExecuteContract {
    sender: string;
    /**
     * funds defines the user's bank coins used to fund the execution (e.g.
     * 100inj).
     */
    funds: string;
    /** contract_address defines the contract address to execute */
    contractAddress: string;
    /** data defines the call data used when executing the contract */
    data: string;
}
/** MsgPrivilegedExecuteContractResponse defines the Msg/Exec response type. */
export interface MsgPrivilegedExecuteContractResponse {
    fundsDiff: Coin[];
}
/** A Cosmos-SDK MsgRewardsOptOut */
export interface MsgRewardsOptOut {
    sender: string;
}
/** MsgRewardsOptOutResponse defines the Msg/RewardsOptOut response type. */
export interface MsgRewardsOptOutResponse {
}
/** A Cosmos-SDK MsgReclaimLockedFunds */
export interface MsgReclaimLockedFunds {
    sender: string;
    lockedAccountPubKey: Uint8Array;
    signature: Uint8Array;
}
/**
 * MsgReclaimLockedFundsResponse defines the Msg/ReclaimLockedFunds response
 * type.
 */
export interface MsgReclaimLockedFundsResponse {
}
/** MsgSignData defines an arbitrary, general-purpose, off-chain message */
export interface MsgSignData {
    /** Signer is the sdk.AccAddress of the message signer */
    Signer: Uint8Array;
    /**
     * Data represents the raw bytes of the content that is signed (text, json,
     * etc)
     */
    Data: Uint8Array;
}
/** MsgSignDoc defines an arbitrary, general-purpose, off-chain message */
export interface MsgSignDoc {
    signType: string;
    value: MsgSignData | undefined;
}
/**
 * MsgAdminUpdateBinaryOptionsMarket is used by the market Admin to operate the
 * market
 */
export interface MsgAdminUpdateBinaryOptionsMarket {
    sender: string;
    marketId: string;
    /** new price at which market will be settled */
    settlementPrice: string;
    /** expiration timestamp */
    expirationTimestamp: string;
    /** expiration timestamp */
    settlementTimestamp: string;
    /** Status of the market */
    status: MarketStatus;
}
/**
 * MsgAdminUpdateBinaryOptionsMarketResponse is the response for
 * AdminUpdateBinaryOptionsMarket rpc method
 */
export interface MsgAdminUpdateBinaryOptionsMarketResponse {
}
/** MsgAuthorizeStakeGrants grants stakes to grantees. */
export interface MsgAuthorizeStakeGrants {
    sender: string;
    grants: GrantAuthorization[];
}
export interface MsgAuthorizeStakeGrantsResponse {
}
/** MsgActivateStakeGrant allows a grantee to activate a stake grant. */
export interface MsgActivateStakeGrant {
    sender: string;
    granter: string;
}
export interface MsgActivateStakeGrantResponse {
}
export interface MsgBatchExchangeModification {
    /** message sender, that is also the TX signer */
    sender: string;
    proposal: BatchExchangeModificationProposal | undefined;
}
export interface MsgBatchExchangeModificationResponse {
}
export declare const MsgUpdateSpotMarket: {
    encode(message: MsgUpdateSpotMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateSpotMarket;
    fromJSON(object: any): MsgUpdateSpotMarket;
    toJSON(message: MsgUpdateSpotMarket): unknown;
    create(base?: DeepPartial<MsgUpdateSpotMarket>): MsgUpdateSpotMarket;
    fromPartial(object: DeepPartial<MsgUpdateSpotMarket>): MsgUpdateSpotMarket;
};
export declare const MsgUpdateSpotMarketResponse: {
    encode(_: MsgUpdateSpotMarketResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateSpotMarketResponse;
    fromJSON(_: any): MsgUpdateSpotMarketResponse;
    toJSON(_: MsgUpdateSpotMarketResponse): unknown;
    create(base?: DeepPartial<MsgUpdateSpotMarketResponse>): MsgUpdateSpotMarketResponse;
    fromPartial(_: DeepPartial<MsgUpdateSpotMarketResponse>): MsgUpdateSpotMarketResponse;
};
export declare const MsgUpdateDerivativeMarket: {
    encode(message: MsgUpdateDerivativeMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateDerivativeMarket;
    fromJSON(object: any): MsgUpdateDerivativeMarket;
    toJSON(message: MsgUpdateDerivativeMarket): unknown;
    create(base?: DeepPartial<MsgUpdateDerivativeMarket>): MsgUpdateDerivativeMarket;
    fromPartial(object: DeepPartial<MsgUpdateDerivativeMarket>): MsgUpdateDerivativeMarket;
};
export declare const MsgUpdateDerivativeMarketResponse: {
    encode(_: MsgUpdateDerivativeMarketResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateDerivativeMarketResponse;
    fromJSON(_: any): MsgUpdateDerivativeMarketResponse;
    toJSON(_: MsgUpdateDerivativeMarketResponse): unknown;
    create(base?: DeepPartial<MsgUpdateDerivativeMarketResponse>): MsgUpdateDerivativeMarketResponse;
    fromPartial(_: DeepPartial<MsgUpdateDerivativeMarketResponse>): MsgUpdateDerivativeMarketResponse;
};
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
export declare const MsgDeposit: {
    encode(message: MsgDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDeposit;
    fromJSON(object: any): MsgDeposit;
    toJSON(message: MsgDeposit): unknown;
    create(base?: DeepPartial<MsgDeposit>): MsgDeposit;
    fromPartial(object: DeepPartial<MsgDeposit>): MsgDeposit;
};
export declare const MsgDepositResponse: {
    encode(_: MsgDepositResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDepositResponse;
    fromJSON(_: any): MsgDepositResponse;
    toJSON(_: MsgDepositResponse): unknown;
    create(base?: DeepPartial<MsgDepositResponse>): MsgDepositResponse;
    fromPartial(_: DeepPartial<MsgDepositResponse>): MsgDepositResponse;
};
export declare const MsgWithdraw: {
    encode(message: MsgWithdraw, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdraw;
    fromJSON(object: any): MsgWithdraw;
    toJSON(message: MsgWithdraw): unknown;
    create(base?: DeepPartial<MsgWithdraw>): MsgWithdraw;
    fromPartial(object: DeepPartial<MsgWithdraw>): MsgWithdraw;
};
export declare const MsgWithdrawResponse: {
    encode(_: MsgWithdrawResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgWithdrawResponse;
    fromJSON(_: any): MsgWithdrawResponse;
    toJSON(_: MsgWithdrawResponse): unknown;
    create(base?: DeepPartial<MsgWithdrawResponse>): MsgWithdrawResponse;
    fromPartial(_: DeepPartial<MsgWithdrawResponse>): MsgWithdrawResponse;
};
export declare const MsgCreateSpotLimitOrder: {
    encode(message: MsgCreateSpotLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateSpotLimitOrder;
    fromJSON(object: any): MsgCreateSpotLimitOrder;
    toJSON(message: MsgCreateSpotLimitOrder): unknown;
    create(base?: DeepPartial<MsgCreateSpotLimitOrder>): MsgCreateSpotLimitOrder;
    fromPartial(object: DeepPartial<MsgCreateSpotLimitOrder>): MsgCreateSpotLimitOrder;
};
export declare const MsgCreateSpotLimitOrderResponse: {
    encode(message: MsgCreateSpotLimitOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateSpotLimitOrderResponse;
    fromJSON(object: any): MsgCreateSpotLimitOrderResponse;
    toJSON(message: MsgCreateSpotLimitOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateSpotLimitOrderResponse>): MsgCreateSpotLimitOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateSpotLimitOrderResponse>): MsgCreateSpotLimitOrderResponse;
};
export declare const MsgBatchCreateSpotLimitOrders: {
    encode(message: MsgBatchCreateSpotLimitOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCreateSpotLimitOrders;
    fromJSON(object: any): MsgBatchCreateSpotLimitOrders;
    toJSON(message: MsgBatchCreateSpotLimitOrders): unknown;
    create(base?: DeepPartial<MsgBatchCreateSpotLimitOrders>): MsgBatchCreateSpotLimitOrders;
    fromPartial(object: DeepPartial<MsgBatchCreateSpotLimitOrders>): MsgBatchCreateSpotLimitOrders;
};
export declare const MsgBatchCreateSpotLimitOrdersResponse: {
    encode(message: MsgBatchCreateSpotLimitOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCreateSpotLimitOrdersResponse;
    fromJSON(object: any): MsgBatchCreateSpotLimitOrdersResponse;
    toJSON(message: MsgBatchCreateSpotLimitOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchCreateSpotLimitOrdersResponse>): MsgBatchCreateSpotLimitOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchCreateSpotLimitOrdersResponse>): MsgBatchCreateSpotLimitOrdersResponse;
};
export declare const MsgInstantSpotMarketLaunch: {
    encode(message: MsgInstantSpotMarketLaunch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantSpotMarketLaunch;
    fromJSON(object: any): MsgInstantSpotMarketLaunch;
    toJSON(message: MsgInstantSpotMarketLaunch): unknown;
    create(base?: DeepPartial<MsgInstantSpotMarketLaunch>): MsgInstantSpotMarketLaunch;
    fromPartial(object: DeepPartial<MsgInstantSpotMarketLaunch>): MsgInstantSpotMarketLaunch;
};
export declare const MsgInstantSpotMarketLaunchResponse: {
    encode(_: MsgInstantSpotMarketLaunchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantSpotMarketLaunchResponse;
    fromJSON(_: any): MsgInstantSpotMarketLaunchResponse;
    toJSON(_: MsgInstantSpotMarketLaunchResponse): unknown;
    create(base?: DeepPartial<MsgInstantSpotMarketLaunchResponse>): MsgInstantSpotMarketLaunchResponse;
    fromPartial(_: DeepPartial<MsgInstantSpotMarketLaunchResponse>): MsgInstantSpotMarketLaunchResponse;
};
export declare const MsgInstantPerpetualMarketLaunch: {
    encode(message: MsgInstantPerpetualMarketLaunch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantPerpetualMarketLaunch;
    fromJSON(object: any): MsgInstantPerpetualMarketLaunch;
    toJSON(message: MsgInstantPerpetualMarketLaunch): unknown;
    create(base?: DeepPartial<MsgInstantPerpetualMarketLaunch>): MsgInstantPerpetualMarketLaunch;
    fromPartial(object: DeepPartial<MsgInstantPerpetualMarketLaunch>): MsgInstantPerpetualMarketLaunch;
};
export declare const MsgInstantPerpetualMarketLaunchResponse: {
    encode(_: MsgInstantPerpetualMarketLaunchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantPerpetualMarketLaunchResponse;
    fromJSON(_: any): MsgInstantPerpetualMarketLaunchResponse;
    toJSON(_: MsgInstantPerpetualMarketLaunchResponse): unknown;
    create(base?: DeepPartial<MsgInstantPerpetualMarketLaunchResponse>): MsgInstantPerpetualMarketLaunchResponse;
    fromPartial(_: DeepPartial<MsgInstantPerpetualMarketLaunchResponse>): MsgInstantPerpetualMarketLaunchResponse;
};
export declare const MsgInstantBinaryOptionsMarketLaunch: {
    encode(message: MsgInstantBinaryOptionsMarketLaunch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantBinaryOptionsMarketLaunch;
    fromJSON(object: any): MsgInstantBinaryOptionsMarketLaunch;
    toJSON(message: MsgInstantBinaryOptionsMarketLaunch): unknown;
    create(base?: DeepPartial<MsgInstantBinaryOptionsMarketLaunch>): MsgInstantBinaryOptionsMarketLaunch;
    fromPartial(object: DeepPartial<MsgInstantBinaryOptionsMarketLaunch>): MsgInstantBinaryOptionsMarketLaunch;
};
export declare const MsgInstantBinaryOptionsMarketLaunchResponse: {
    encode(_: MsgInstantBinaryOptionsMarketLaunchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantBinaryOptionsMarketLaunchResponse;
    fromJSON(_: any): MsgInstantBinaryOptionsMarketLaunchResponse;
    toJSON(_: MsgInstantBinaryOptionsMarketLaunchResponse): unknown;
    create(base?: DeepPartial<MsgInstantBinaryOptionsMarketLaunchResponse>): MsgInstantBinaryOptionsMarketLaunchResponse;
    fromPartial(_: DeepPartial<MsgInstantBinaryOptionsMarketLaunchResponse>): MsgInstantBinaryOptionsMarketLaunchResponse;
};
export declare const MsgInstantExpiryFuturesMarketLaunch: {
    encode(message: MsgInstantExpiryFuturesMarketLaunch, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantExpiryFuturesMarketLaunch;
    fromJSON(object: any): MsgInstantExpiryFuturesMarketLaunch;
    toJSON(message: MsgInstantExpiryFuturesMarketLaunch): unknown;
    create(base?: DeepPartial<MsgInstantExpiryFuturesMarketLaunch>): MsgInstantExpiryFuturesMarketLaunch;
    fromPartial(object: DeepPartial<MsgInstantExpiryFuturesMarketLaunch>): MsgInstantExpiryFuturesMarketLaunch;
};
export declare const MsgInstantExpiryFuturesMarketLaunchResponse: {
    encode(_: MsgInstantExpiryFuturesMarketLaunchResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgInstantExpiryFuturesMarketLaunchResponse;
    fromJSON(_: any): MsgInstantExpiryFuturesMarketLaunchResponse;
    toJSON(_: MsgInstantExpiryFuturesMarketLaunchResponse): unknown;
    create(base?: DeepPartial<MsgInstantExpiryFuturesMarketLaunchResponse>): MsgInstantExpiryFuturesMarketLaunchResponse;
    fromPartial(_: DeepPartial<MsgInstantExpiryFuturesMarketLaunchResponse>): MsgInstantExpiryFuturesMarketLaunchResponse;
};
export declare const MsgCreateSpotMarketOrder: {
    encode(message: MsgCreateSpotMarketOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateSpotMarketOrder;
    fromJSON(object: any): MsgCreateSpotMarketOrder;
    toJSON(message: MsgCreateSpotMarketOrder): unknown;
    create(base?: DeepPartial<MsgCreateSpotMarketOrder>): MsgCreateSpotMarketOrder;
    fromPartial(object: DeepPartial<MsgCreateSpotMarketOrder>): MsgCreateSpotMarketOrder;
};
export declare const MsgCreateSpotMarketOrderResponse: {
    encode(message: MsgCreateSpotMarketOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateSpotMarketOrderResponse;
    fromJSON(object: any): MsgCreateSpotMarketOrderResponse;
    toJSON(message: MsgCreateSpotMarketOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateSpotMarketOrderResponse>): MsgCreateSpotMarketOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateSpotMarketOrderResponse>): MsgCreateSpotMarketOrderResponse;
};
export declare const SpotMarketOrderResults: {
    encode(message: SpotMarketOrderResults, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotMarketOrderResults;
    fromJSON(object: any): SpotMarketOrderResults;
    toJSON(message: SpotMarketOrderResults): unknown;
    create(base?: DeepPartial<SpotMarketOrderResults>): SpotMarketOrderResults;
    fromPartial(object: DeepPartial<SpotMarketOrderResults>): SpotMarketOrderResults;
};
export declare const MsgCreateDerivativeLimitOrder: {
    encode(message: MsgCreateDerivativeLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateDerivativeLimitOrder;
    fromJSON(object: any): MsgCreateDerivativeLimitOrder;
    toJSON(message: MsgCreateDerivativeLimitOrder): unknown;
    create(base?: DeepPartial<MsgCreateDerivativeLimitOrder>): MsgCreateDerivativeLimitOrder;
    fromPartial(object: DeepPartial<MsgCreateDerivativeLimitOrder>): MsgCreateDerivativeLimitOrder;
};
export declare const MsgCreateDerivativeLimitOrderResponse: {
    encode(message: MsgCreateDerivativeLimitOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateDerivativeLimitOrderResponse;
    fromJSON(object: any): MsgCreateDerivativeLimitOrderResponse;
    toJSON(message: MsgCreateDerivativeLimitOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateDerivativeLimitOrderResponse>): MsgCreateDerivativeLimitOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateDerivativeLimitOrderResponse>): MsgCreateDerivativeLimitOrderResponse;
};
export declare const MsgCreateBinaryOptionsLimitOrder: {
    encode(message: MsgCreateBinaryOptionsLimitOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateBinaryOptionsLimitOrder;
    fromJSON(object: any): MsgCreateBinaryOptionsLimitOrder;
    toJSON(message: MsgCreateBinaryOptionsLimitOrder): unknown;
    create(base?: DeepPartial<MsgCreateBinaryOptionsLimitOrder>): MsgCreateBinaryOptionsLimitOrder;
    fromPartial(object: DeepPartial<MsgCreateBinaryOptionsLimitOrder>): MsgCreateBinaryOptionsLimitOrder;
};
export declare const MsgCreateBinaryOptionsLimitOrderResponse: {
    encode(message: MsgCreateBinaryOptionsLimitOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateBinaryOptionsLimitOrderResponse;
    fromJSON(object: any): MsgCreateBinaryOptionsLimitOrderResponse;
    toJSON(message: MsgCreateBinaryOptionsLimitOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateBinaryOptionsLimitOrderResponse>): MsgCreateBinaryOptionsLimitOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateBinaryOptionsLimitOrderResponse>): MsgCreateBinaryOptionsLimitOrderResponse;
};
export declare const MsgBatchCreateDerivativeLimitOrders: {
    encode(message: MsgBatchCreateDerivativeLimitOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCreateDerivativeLimitOrders;
    fromJSON(object: any): MsgBatchCreateDerivativeLimitOrders;
    toJSON(message: MsgBatchCreateDerivativeLimitOrders): unknown;
    create(base?: DeepPartial<MsgBatchCreateDerivativeLimitOrders>): MsgBatchCreateDerivativeLimitOrders;
    fromPartial(object: DeepPartial<MsgBatchCreateDerivativeLimitOrders>): MsgBatchCreateDerivativeLimitOrders;
};
export declare const MsgBatchCreateDerivativeLimitOrdersResponse: {
    encode(message: MsgBatchCreateDerivativeLimitOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCreateDerivativeLimitOrdersResponse;
    fromJSON(object: any): MsgBatchCreateDerivativeLimitOrdersResponse;
    toJSON(message: MsgBatchCreateDerivativeLimitOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchCreateDerivativeLimitOrdersResponse>): MsgBatchCreateDerivativeLimitOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchCreateDerivativeLimitOrdersResponse>): MsgBatchCreateDerivativeLimitOrdersResponse;
};
export declare const MsgCancelSpotOrder: {
    encode(message: MsgCancelSpotOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelSpotOrder;
    fromJSON(object: any): MsgCancelSpotOrder;
    toJSON(message: MsgCancelSpotOrder): unknown;
    create(base?: DeepPartial<MsgCancelSpotOrder>): MsgCancelSpotOrder;
    fromPartial(object: DeepPartial<MsgCancelSpotOrder>): MsgCancelSpotOrder;
};
export declare const MsgCancelSpotOrderResponse: {
    encode(_: MsgCancelSpotOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelSpotOrderResponse;
    fromJSON(_: any): MsgCancelSpotOrderResponse;
    toJSON(_: MsgCancelSpotOrderResponse): unknown;
    create(base?: DeepPartial<MsgCancelSpotOrderResponse>): MsgCancelSpotOrderResponse;
    fromPartial(_: DeepPartial<MsgCancelSpotOrderResponse>): MsgCancelSpotOrderResponse;
};
export declare const MsgBatchCancelSpotOrders: {
    encode(message: MsgBatchCancelSpotOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelSpotOrders;
    fromJSON(object: any): MsgBatchCancelSpotOrders;
    toJSON(message: MsgBatchCancelSpotOrders): unknown;
    create(base?: DeepPartial<MsgBatchCancelSpotOrders>): MsgBatchCancelSpotOrders;
    fromPartial(object: DeepPartial<MsgBatchCancelSpotOrders>): MsgBatchCancelSpotOrders;
};
export declare const MsgBatchCancelSpotOrdersResponse: {
    encode(message: MsgBatchCancelSpotOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelSpotOrdersResponse;
    fromJSON(object: any): MsgBatchCancelSpotOrdersResponse;
    toJSON(message: MsgBatchCancelSpotOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchCancelSpotOrdersResponse>): MsgBatchCancelSpotOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchCancelSpotOrdersResponse>): MsgBatchCancelSpotOrdersResponse;
};
export declare const MsgBatchCancelBinaryOptionsOrders: {
    encode(message: MsgBatchCancelBinaryOptionsOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelBinaryOptionsOrders;
    fromJSON(object: any): MsgBatchCancelBinaryOptionsOrders;
    toJSON(message: MsgBatchCancelBinaryOptionsOrders): unknown;
    create(base?: DeepPartial<MsgBatchCancelBinaryOptionsOrders>): MsgBatchCancelBinaryOptionsOrders;
    fromPartial(object: DeepPartial<MsgBatchCancelBinaryOptionsOrders>): MsgBatchCancelBinaryOptionsOrders;
};
export declare const MsgBatchCancelBinaryOptionsOrdersResponse: {
    encode(message: MsgBatchCancelBinaryOptionsOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelBinaryOptionsOrdersResponse;
    fromJSON(object: any): MsgBatchCancelBinaryOptionsOrdersResponse;
    toJSON(message: MsgBatchCancelBinaryOptionsOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchCancelBinaryOptionsOrdersResponse>): MsgBatchCancelBinaryOptionsOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchCancelBinaryOptionsOrdersResponse>): MsgBatchCancelBinaryOptionsOrdersResponse;
};
export declare const MsgBatchUpdateOrders: {
    encode(message: MsgBatchUpdateOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchUpdateOrders;
    fromJSON(object: any): MsgBatchUpdateOrders;
    toJSON(message: MsgBatchUpdateOrders): unknown;
    create(base?: DeepPartial<MsgBatchUpdateOrders>): MsgBatchUpdateOrders;
    fromPartial(object: DeepPartial<MsgBatchUpdateOrders>): MsgBatchUpdateOrders;
};
export declare const MsgBatchUpdateOrdersResponse: {
    encode(message: MsgBatchUpdateOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchUpdateOrdersResponse;
    fromJSON(object: any): MsgBatchUpdateOrdersResponse;
    toJSON(message: MsgBatchUpdateOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchUpdateOrdersResponse>): MsgBatchUpdateOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchUpdateOrdersResponse>): MsgBatchUpdateOrdersResponse;
};
export declare const MsgCreateDerivativeMarketOrder: {
    encode(message: MsgCreateDerivativeMarketOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateDerivativeMarketOrder;
    fromJSON(object: any): MsgCreateDerivativeMarketOrder;
    toJSON(message: MsgCreateDerivativeMarketOrder): unknown;
    create(base?: DeepPartial<MsgCreateDerivativeMarketOrder>): MsgCreateDerivativeMarketOrder;
    fromPartial(object: DeepPartial<MsgCreateDerivativeMarketOrder>): MsgCreateDerivativeMarketOrder;
};
export declare const MsgCreateDerivativeMarketOrderResponse: {
    encode(message: MsgCreateDerivativeMarketOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateDerivativeMarketOrderResponse;
    fromJSON(object: any): MsgCreateDerivativeMarketOrderResponse;
    toJSON(message: MsgCreateDerivativeMarketOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateDerivativeMarketOrderResponse>): MsgCreateDerivativeMarketOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateDerivativeMarketOrderResponse>): MsgCreateDerivativeMarketOrderResponse;
};
export declare const DerivativeMarketOrderResults: {
    encode(message: DerivativeMarketOrderResults, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeMarketOrderResults;
    fromJSON(object: any): DerivativeMarketOrderResults;
    toJSON(message: DerivativeMarketOrderResults): unknown;
    create(base?: DeepPartial<DerivativeMarketOrderResults>): DerivativeMarketOrderResults;
    fromPartial(object: DeepPartial<DerivativeMarketOrderResults>): DerivativeMarketOrderResults;
};
export declare const MsgCreateBinaryOptionsMarketOrder: {
    encode(message: MsgCreateBinaryOptionsMarketOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateBinaryOptionsMarketOrder;
    fromJSON(object: any): MsgCreateBinaryOptionsMarketOrder;
    toJSON(message: MsgCreateBinaryOptionsMarketOrder): unknown;
    create(base?: DeepPartial<MsgCreateBinaryOptionsMarketOrder>): MsgCreateBinaryOptionsMarketOrder;
    fromPartial(object: DeepPartial<MsgCreateBinaryOptionsMarketOrder>): MsgCreateBinaryOptionsMarketOrder;
};
export declare const MsgCreateBinaryOptionsMarketOrderResponse: {
    encode(message: MsgCreateBinaryOptionsMarketOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateBinaryOptionsMarketOrderResponse;
    fromJSON(object: any): MsgCreateBinaryOptionsMarketOrderResponse;
    toJSON(message: MsgCreateBinaryOptionsMarketOrderResponse): unknown;
    create(base?: DeepPartial<MsgCreateBinaryOptionsMarketOrderResponse>): MsgCreateBinaryOptionsMarketOrderResponse;
    fromPartial(object: DeepPartial<MsgCreateBinaryOptionsMarketOrderResponse>): MsgCreateBinaryOptionsMarketOrderResponse;
};
export declare const MsgCancelDerivativeOrder: {
    encode(message: MsgCancelDerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelDerivativeOrder;
    fromJSON(object: any): MsgCancelDerivativeOrder;
    toJSON(message: MsgCancelDerivativeOrder): unknown;
    create(base?: DeepPartial<MsgCancelDerivativeOrder>): MsgCancelDerivativeOrder;
    fromPartial(object: DeepPartial<MsgCancelDerivativeOrder>): MsgCancelDerivativeOrder;
};
export declare const MsgCancelDerivativeOrderResponse: {
    encode(_: MsgCancelDerivativeOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelDerivativeOrderResponse;
    fromJSON(_: any): MsgCancelDerivativeOrderResponse;
    toJSON(_: MsgCancelDerivativeOrderResponse): unknown;
    create(base?: DeepPartial<MsgCancelDerivativeOrderResponse>): MsgCancelDerivativeOrderResponse;
    fromPartial(_: DeepPartial<MsgCancelDerivativeOrderResponse>): MsgCancelDerivativeOrderResponse;
};
export declare const MsgCancelBinaryOptionsOrder: {
    encode(message: MsgCancelBinaryOptionsOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelBinaryOptionsOrder;
    fromJSON(object: any): MsgCancelBinaryOptionsOrder;
    toJSON(message: MsgCancelBinaryOptionsOrder): unknown;
    create(base?: DeepPartial<MsgCancelBinaryOptionsOrder>): MsgCancelBinaryOptionsOrder;
    fromPartial(object: DeepPartial<MsgCancelBinaryOptionsOrder>): MsgCancelBinaryOptionsOrder;
};
export declare const MsgCancelBinaryOptionsOrderResponse: {
    encode(_: MsgCancelBinaryOptionsOrderResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCancelBinaryOptionsOrderResponse;
    fromJSON(_: any): MsgCancelBinaryOptionsOrderResponse;
    toJSON(_: MsgCancelBinaryOptionsOrderResponse): unknown;
    create(base?: DeepPartial<MsgCancelBinaryOptionsOrderResponse>): MsgCancelBinaryOptionsOrderResponse;
    fromPartial(_: DeepPartial<MsgCancelBinaryOptionsOrderResponse>): MsgCancelBinaryOptionsOrderResponse;
};
export declare const OrderData: {
    encode(message: OrderData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderData;
    fromJSON(object: any): OrderData;
    toJSON(message: OrderData): unknown;
    create(base?: DeepPartial<OrderData>): OrderData;
    fromPartial(object: DeepPartial<OrderData>): OrderData;
};
export declare const MsgBatchCancelDerivativeOrders: {
    encode(message: MsgBatchCancelDerivativeOrders, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelDerivativeOrders;
    fromJSON(object: any): MsgBatchCancelDerivativeOrders;
    toJSON(message: MsgBatchCancelDerivativeOrders): unknown;
    create(base?: DeepPartial<MsgBatchCancelDerivativeOrders>): MsgBatchCancelDerivativeOrders;
    fromPartial(object: DeepPartial<MsgBatchCancelDerivativeOrders>): MsgBatchCancelDerivativeOrders;
};
export declare const MsgBatchCancelDerivativeOrdersResponse: {
    encode(message: MsgBatchCancelDerivativeOrdersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchCancelDerivativeOrdersResponse;
    fromJSON(object: any): MsgBatchCancelDerivativeOrdersResponse;
    toJSON(message: MsgBatchCancelDerivativeOrdersResponse): unknown;
    create(base?: DeepPartial<MsgBatchCancelDerivativeOrdersResponse>): MsgBatchCancelDerivativeOrdersResponse;
    fromPartial(object: DeepPartial<MsgBatchCancelDerivativeOrdersResponse>): MsgBatchCancelDerivativeOrdersResponse;
};
export declare const MsgSubaccountTransfer: {
    encode(message: MsgSubaccountTransfer, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSubaccountTransfer;
    fromJSON(object: any): MsgSubaccountTransfer;
    toJSON(message: MsgSubaccountTransfer): unknown;
    create(base?: DeepPartial<MsgSubaccountTransfer>): MsgSubaccountTransfer;
    fromPartial(object: DeepPartial<MsgSubaccountTransfer>): MsgSubaccountTransfer;
};
export declare const MsgSubaccountTransferResponse: {
    encode(_: MsgSubaccountTransferResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSubaccountTransferResponse;
    fromJSON(_: any): MsgSubaccountTransferResponse;
    toJSON(_: MsgSubaccountTransferResponse): unknown;
    create(base?: DeepPartial<MsgSubaccountTransferResponse>): MsgSubaccountTransferResponse;
    fromPartial(_: DeepPartial<MsgSubaccountTransferResponse>): MsgSubaccountTransferResponse;
};
export declare const MsgExternalTransfer: {
    encode(message: MsgExternalTransfer, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgExternalTransfer;
    fromJSON(object: any): MsgExternalTransfer;
    toJSON(message: MsgExternalTransfer): unknown;
    create(base?: DeepPartial<MsgExternalTransfer>): MsgExternalTransfer;
    fromPartial(object: DeepPartial<MsgExternalTransfer>): MsgExternalTransfer;
};
export declare const MsgExternalTransferResponse: {
    encode(_: MsgExternalTransferResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgExternalTransferResponse;
    fromJSON(_: any): MsgExternalTransferResponse;
    toJSON(_: MsgExternalTransferResponse): unknown;
    create(base?: DeepPartial<MsgExternalTransferResponse>): MsgExternalTransferResponse;
    fromPartial(_: DeepPartial<MsgExternalTransferResponse>): MsgExternalTransferResponse;
};
export declare const MsgLiquidatePosition: {
    encode(message: MsgLiquidatePosition, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgLiquidatePosition;
    fromJSON(object: any): MsgLiquidatePosition;
    toJSON(message: MsgLiquidatePosition): unknown;
    create(base?: DeepPartial<MsgLiquidatePosition>): MsgLiquidatePosition;
    fromPartial(object: DeepPartial<MsgLiquidatePosition>): MsgLiquidatePosition;
};
export declare const MsgLiquidatePositionResponse: {
    encode(_: MsgLiquidatePositionResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgLiquidatePositionResponse;
    fromJSON(_: any): MsgLiquidatePositionResponse;
    toJSON(_: MsgLiquidatePositionResponse): unknown;
    create(base?: DeepPartial<MsgLiquidatePositionResponse>): MsgLiquidatePositionResponse;
    fromPartial(_: DeepPartial<MsgLiquidatePositionResponse>): MsgLiquidatePositionResponse;
};
export declare const MsgEmergencySettleMarket: {
    encode(message: MsgEmergencySettleMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgEmergencySettleMarket;
    fromJSON(object: any): MsgEmergencySettleMarket;
    toJSON(message: MsgEmergencySettleMarket): unknown;
    create(base?: DeepPartial<MsgEmergencySettleMarket>): MsgEmergencySettleMarket;
    fromPartial(object: DeepPartial<MsgEmergencySettleMarket>): MsgEmergencySettleMarket;
};
export declare const MsgEmergencySettleMarketResponse: {
    encode(_: MsgEmergencySettleMarketResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgEmergencySettleMarketResponse;
    fromJSON(_: any): MsgEmergencySettleMarketResponse;
    toJSON(_: MsgEmergencySettleMarketResponse): unknown;
    create(base?: DeepPartial<MsgEmergencySettleMarketResponse>): MsgEmergencySettleMarketResponse;
    fromPartial(_: DeepPartial<MsgEmergencySettleMarketResponse>): MsgEmergencySettleMarketResponse;
};
export declare const MsgIncreasePositionMargin: {
    encode(message: MsgIncreasePositionMargin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIncreasePositionMargin;
    fromJSON(object: any): MsgIncreasePositionMargin;
    toJSON(message: MsgIncreasePositionMargin): unknown;
    create(base?: DeepPartial<MsgIncreasePositionMargin>): MsgIncreasePositionMargin;
    fromPartial(object: DeepPartial<MsgIncreasePositionMargin>): MsgIncreasePositionMargin;
};
export declare const MsgIncreasePositionMarginResponse: {
    encode(_: MsgIncreasePositionMarginResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgIncreasePositionMarginResponse;
    fromJSON(_: any): MsgIncreasePositionMarginResponse;
    toJSON(_: MsgIncreasePositionMarginResponse): unknown;
    create(base?: DeepPartial<MsgIncreasePositionMarginResponse>): MsgIncreasePositionMarginResponse;
    fromPartial(_: DeepPartial<MsgIncreasePositionMarginResponse>): MsgIncreasePositionMarginResponse;
};
export declare const MsgDecreasePositionMargin: {
    encode(message: MsgDecreasePositionMargin, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDecreasePositionMargin;
    fromJSON(object: any): MsgDecreasePositionMargin;
    toJSON(message: MsgDecreasePositionMargin): unknown;
    create(base?: DeepPartial<MsgDecreasePositionMargin>): MsgDecreasePositionMargin;
    fromPartial(object: DeepPartial<MsgDecreasePositionMargin>): MsgDecreasePositionMargin;
};
export declare const MsgDecreasePositionMarginResponse: {
    encode(_: MsgDecreasePositionMarginResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgDecreasePositionMarginResponse;
    fromJSON(_: any): MsgDecreasePositionMarginResponse;
    toJSON(_: MsgDecreasePositionMarginResponse): unknown;
    create(base?: DeepPartial<MsgDecreasePositionMarginResponse>): MsgDecreasePositionMarginResponse;
    fromPartial(_: DeepPartial<MsgDecreasePositionMarginResponse>): MsgDecreasePositionMarginResponse;
};
export declare const MsgPrivilegedExecuteContract: {
    encode(message: MsgPrivilegedExecuteContract, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgPrivilegedExecuteContract;
    fromJSON(object: any): MsgPrivilegedExecuteContract;
    toJSON(message: MsgPrivilegedExecuteContract): unknown;
    create(base?: DeepPartial<MsgPrivilegedExecuteContract>): MsgPrivilegedExecuteContract;
    fromPartial(object: DeepPartial<MsgPrivilegedExecuteContract>): MsgPrivilegedExecuteContract;
};
export declare const MsgPrivilegedExecuteContractResponse: {
    encode(message: MsgPrivilegedExecuteContractResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgPrivilegedExecuteContractResponse;
    fromJSON(object: any): MsgPrivilegedExecuteContractResponse;
    toJSON(message: MsgPrivilegedExecuteContractResponse): unknown;
    create(base?: DeepPartial<MsgPrivilegedExecuteContractResponse>): MsgPrivilegedExecuteContractResponse;
    fromPartial(object: DeepPartial<MsgPrivilegedExecuteContractResponse>): MsgPrivilegedExecuteContractResponse;
};
export declare const MsgRewardsOptOut: {
    encode(message: MsgRewardsOptOut, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRewardsOptOut;
    fromJSON(object: any): MsgRewardsOptOut;
    toJSON(message: MsgRewardsOptOut): unknown;
    create(base?: DeepPartial<MsgRewardsOptOut>): MsgRewardsOptOut;
    fromPartial(object: DeepPartial<MsgRewardsOptOut>): MsgRewardsOptOut;
};
export declare const MsgRewardsOptOutResponse: {
    encode(_: MsgRewardsOptOutResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgRewardsOptOutResponse;
    fromJSON(_: any): MsgRewardsOptOutResponse;
    toJSON(_: MsgRewardsOptOutResponse): unknown;
    create(base?: DeepPartial<MsgRewardsOptOutResponse>): MsgRewardsOptOutResponse;
    fromPartial(_: DeepPartial<MsgRewardsOptOutResponse>): MsgRewardsOptOutResponse;
};
export declare const MsgReclaimLockedFunds: {
    encode(message: MsgReclaimLockedFunds, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgReclaimLockedFunds;
    fromJSON(object: any): MsgReclaimLockedFunds;
    toJSON(message: MsgReclaimLockedFunds): unknown;
    create(base?: DeepPartial<MsgReclaimLockedFunds>): MsgReclaimLockedFunds;
    fromPartial(object: DeepPartial<MsgReclaimLockedFunds>): MsgReclaimLockedFunds;
};
export declare const MsgReclaimLockedFundsResponse: {
    encode(_: MsgReclaimLockedFundsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgReclaimLockedFundsResponse;
    fromJSON(_: any): MsgReclaimLockedFundsResponse;
    toJSON(_: MsgReclaimLockedFundsResponse): unknown;
    create(base?: DeepPartial<MsgReclaimLockedFundsResponse>): MsgReclaimLockedFundsResponse;
    fromPartial(_: DeepPartial<MsgReclaimLockedFundsResponse>): MsgReclaimLockedFundsResponse;
};
export declare const MsgSignData: {
    encode(message: MsgSignData, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSignData;
    fromJSON(object: any): MsgSignData;
    toJSON(message: MsgSignData): unknown;
    create(base?: DeepPartial<MsgSignData>): MsgSignData;
    fromPartial(object: DeepPartial<MsgSignData>): MsgSignData;
};
export declare const MsgSignDoc: {
    encode(message: MsgSignDoc, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgSignDoc;
    fromJSON(object: any): MsgSignDoc;
    toJSON(message: MsgSignDoc): unknown;
    create(base?: DeepPartial<MsgSignDoc>): MsgSignDoc;
    fromPartial(object: DeepPartial<MsgSignDoc>): MsgSignDoc;
};
export declare const MsgAdminUpdateBinaryOptionsMarket: {
    encode(message: MsgAdminUpdateBinaryOptionsMarket, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAdminUpdateBinaryOptionsMarket;
    fromJSON(object: any): MsgAdminUpdateBinaryOptionsMarket;
    toJSON(message: MsgAdminUpdateBinaryOptionsMarket): unknown;
    create(base?: DeepPartial<MsgAdminUpdateBinaryOptionsMarket>): MsgAdminUpdateBinaryOptionsMarket;
    fromPartial(object: DeepPartial<MsgAdminUpdateBinaryOptionsMarket>): MsgAdminUpdateBinaryOptionsMarket;
};
export declare const MsgAdminUpdateBinaryOptionsMarketResponse: {
    encode(_: MsgAdminUpdateBinaryOptionsMarketResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAdminUpdateBinaryOptionsMarketResponse;
    fromJSON(_: any): MsgAdminUpdateBinaryOptionsMarketResponse;
    toJSON(_: MsgAdminUpdateBinaryOptionsMarketResponse): unknown;
    create(base?: DeepPartial<MsgAdminUpdateBinaryOptionsMarketResponse>): MsgAdminUpdateBinaryOptionsMarketResponse;
    fromPartial(_: DeepPartial<MsgAdminUpdateBinaryOptionsMarketResponse>): MsgAdminUpdateBinaryOptionsMarketResponse;
};
export declare const MsgAuthorizeStakeGrants: {
    encode(message: MsgAuthorizeStakeGrants, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAuthorizeStakeGrants;
    fromJSON(object: any): MsgAuthorizeStakeGrants;
    toJSON(message: MsgAuthorizeStakeGrants): unknown;
    create(base?: DeepPartial<MsgAuthorizeStakeGrants>): MsgAuthorizeStakeGrants;
    fromPartial(object: DeepPartial<MsgAuthorizeStakeGrants>): MsgAuthorizeStakeGrants;
};
export declare const MsgAuthorizeStakeGrantsResponse: {
    encode(_: MsgAuthorizeStakeGrantsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgAuthorizeStakeGrantsResponse;
    fromJSON(_: any): MsgAuthorizeStakeGrantsResponse;
    toJSON(_: MsgAuthorizeStakeGrantsResponse): unknown;
    create(base?: DeepPartial<MsgAuthorizeStakeGrantsResponse>): MsgAuthorizeStakeGrantsResponse;
    fromPartial(_: DeepPartial<MsgAuthorizeStakeGrantsResponse>): MsgAuthorizeStakeGrantsResponse;
};
export declare const MsgActivateStakeGrant: {
    encode(message: MsgActivateStakeGrant, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgActivateStakeGrant;
    fromJSON(object: any): MsgActivateStakeGrant;
    toJSON(message: MsgActivateStakeGrant): unknown;
    create(base?: DeepPartial<MsgActivateStakeGrant>): MsgActivateStakeGrant;
    fromPartial(object: DeepPartial<MsgActivateStakeGrant>): MsgActivateStakeGrant;
};
export declare const MsgActivateStakeGrantResponse: {
    encode(_: MsgActivateStakeGrantResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgActivateStakeGrantResponse;
    fromJSON(_: any): MsgActivateStakeGrantResponse;
    toJSON(_: MsgActivateStakeGrantResponse): unknown;
    create(base?: DeepPartial<MsgActivateStakeGrantResponse>): MsgActivateStakeGrantResponse;
    fromPartial(_: DeepPartial<MsgActivateStakeGrantResponse>): MsgActivateStakeGrantResponse;
};
export declare const MsgBatchExchangeModification: {
    encode(message: MsgBatchExchangeModification, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchExchangeModification;
    fromJSON(object: any): MsgBatchExchangeModification;
    toJSON(message: MsgBatchExchangeModification): unknown;
    create(base?: DeepPartial<MsgBatchExchangeModification>): MsgBatchExchangeModification;
    fromPartial(object: DeepPartial<MsgBatchExchangeModification>): MsgBatchExchangeModification;
};
export declare const MsgBatchExchangeModificationResponse: {
    encode(_: MsgBatchExchangeModificationResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgBatchExchangeModificationResponse;
    fromJSON(_: any): MsgBatchExchangeModificationResponse;
    toJSON(_: MsgBatchExchangeModificationResponse): unknown;
    create(base?: DeepPartial<MsgBatchExchangeModificationResponse>): MsgBatchExchangeModificationResponse;
    fromPartial(_: DeepPartial<MsgBatchExchangeModificationResponse>): MsgBatchExchangeModificationResponse;
};
/** Msg defines the exchange Msg service. */
export interface Msg {
    /**
     * Deposit defines a method for transferring coins from the sender's bank
     * balance into the subaccount's exchange deposits
     */
    Deposit(request: DeepPartial<MsgDeposit>, metadata?: grpc.Metadata): Promise<MsgDepositResponse>;
    /**
     * Withdraw defines a method for withdrawing coins from a subaccount's
     * deposits to the user's bank balance
     */
    Withdraw(request: DeepPartial<MsgWithdraw>, metadata?: grpc.Metadata): Promise<MsgWithdrawResponse>;
    /**
     * InstantSpotMarketLaunch defines method for creating a spot market by paying
     * listing fee without governance
     */
    InstantSpotMarketLaunch(request: DeepPartial<MsgInstantSpotMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantSpotMarketLaunchResponse>;
    /**
     * InstantPerpetualMarketLaunch defines a method for creating a new perpetual
     * futures market by paying listing fee without governance
     */
    InstantPerpetualMarketLaunch(request: DeepPartial<MsgInstantPerpetualMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantPerpetualMarketLaunchResponse>;
    /**
     * InstantExpiryFuturesMarketLaunch defines a method for creating a new expiry
     * futures market by paying listing fee without governance
     */
    InstantExpiryFuturesMarketLaunch(request: DeepPartial<MsgInstantExpiryFuturesMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantExpiryFuturesMarketLaunchResponse>;
    /** CreateSpotLimitOrder defines a method for creating a new spot limit order. */
    CreateSpotLimitOrder(request: DeepPartial<MsgCreateSpotLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateSpotLimitOrderResponse>;
    /**
     * BatchCreateSpotLimitOrder defines a method for creating a new batch of spot
     * limit orders.
     */
    BatchCreateSpotLimitOrders(request: DeepPartial<MsgBatchCreateSpotLimitOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCreateSpotLimitOrdersResponse>;
    /**
     * CreateSpotMarketOrder defines a method for creating a new spot market
     * order.
     */
    CreateSpotMarketOrder(request: DeepPartial<MsgCreateSpotMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateSpotMarketOrderResponse>;
    /** MsgCancelSpotOrder defines a method for cancelling a spot order. */
    CancelSpotOrder(request: DeepPartial<MsgCancelSpotOrder>, metadata?: grpc.Metadata): Promise<MsgCancelSpotOrderResponse>;
    /**
     * BatchCancelSpotOrders defines a method for cancelling a batch of spot
     * orders in a given market.
     */
    BatchCancelSpotOrders(request: DeepPartial<MsgBatchCancelSpotOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelSpotOrdersResponse>;
    /** BatchUpdateOrders defines a method for updating a batch of orders. */
    BatchUpdateOrders(request: DeepPartial<MsgBatchUpdateOrders>, metadata?: grpc.Metadata): Promise<MsgBatchUpdateOrdersResponse>;
    /**
     * PrivilegedExecuteContract defines a method for executing a Cosmwasm
     * contract from the exchange module with privileged capabilities.
     */
    PrivilegedExecuteContract(request: DeepPartial<MsgPrivilegedExecuteContract>, metadata?: grpc.Metadata): Promise<MsgPrivilegedExecuteContractResponse>;
    /**
     * CreateDerivativeLimitOrder defines a method for creating a new derivative
     * limit order.
     */
    CreateDerivativeLimitOrder(request: DeepPartial<MsgCreateDerivativeLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateDerivativeLimitOrderResponse>;
    /**
     * BatchCreateDerivativeLimitOrders defines a method for creating a new batch
     * of derivative limit orders.
     */
    BatchCreateDerivativeLimitOrders(request: DeepPartial<MsgBatchCreateDerivativeLimitOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCreateDerivativeLimitOrdersResponse>;
    /**
     * MsgCreateDerivativeLimitOrder defines a method for creating a new
     * derivative market order.
     */
    CreateDerivativeMarketOrder(request: DeepPartial<MsgCreateDerivativeMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateDerivativeMarketOrderResponse>;
    /**
     * MsgCancelDerivativeOrder defines a method for cancelling a derivative
     * order.
     */
    CancelDerivativeOrder(request: DeepPartial<MsgCancelDerivativeOrder>, metadata?: grpc.Metadata): Promise<MsgCancelDerivativeOrderResponse>;
    /**
     * MsgBatchCancelDerivativeOrders defines a method for cancelling a batch of
     * derivative limit orders.
     */
    BatchCancelDerivativeOrders(request: DeepPartial<MsgBatchCancelDerivativeOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelDerivativeOrdersResponse>;
    /**
     * InstantBinaryOptionsMarketLaunch defines method for creating a binary
     * options market by paying listing fee without governance
     */
    InstantBinaryOptionsMarketLaunch(request: DeepPartial<MsgInstantBinaryOptionsMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantBinaryOptionsMarketLaunchResponse>;
    /**
     * CreateBinaryOptionsLimitOrder defines a method for creating a new binary
     * options limit order.
     */
    CreateBinaryOptionsLimitOrder(request: DeepPartial<MsgCreateBinaryOptionsLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateBinaryOptionsLimitOrderResponse>;
    /**
     * CreateBinaryOptionsMarketOrder defines a method for creating a new binary
     * options market order.
     */
    CreateBinaryOptionsMarketOrder(request: DeepPartial<MsgCreateBinaryOptionsMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateBinaryOptionsMarketOrderResponse>;
    /**
     * MsgCancelBinaryOptionsOrder defines a method for cancelling a binary
     * options order.
     */
    CancelBinaryOptionsOrder(request: DeepPartial<MsgCancelBinaryOptionsOrder>, metadata?: grpc.Metadata): Promise<MsgCancelBinaryOptionsOrderResponse>;
    /**
     * BatchCancelBinaryOptionsOrders defines a method for cancelling a batch of
     * binary options limit orders.
     */
    BatchCancelBinaryOptionsOrders(request: DeepPartial<MsgBatchCancelBinaryOptionsOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelBinaryOptionsOrdersResponse>;
    /** SubaccountTransfer defines a method for transfer between subaccounts */
    SubaccountTransfer(request: DeepPartial<MsgSubaccountTransfer>, metadata?: grpc.Metadata): Promise<MsgSubaccountTransferResponse>;
    /** ExternalTransfer defines a method for transfer between external accounts */
    ExternalTransfer(request: DeepPartial<MsgExternalTransfer>, metadata?: grpc.Metadata): Promise<MsgExternalTransferResponse>;
    /** LiquidatePosition defines a method for liquidating a position */
    LiquidatePosition(request: DeepPartial<MsgLiquidatePosition>, metadata?: grpc.Metadata): Promise<MsgLiquidatePositionResponse>;
    /** EmergencySettleMarket defines a method for emergency settling a market */
    EmergencySettleMarket(request: DeepPartial<MsgEmergencySettleMarket>, metadata?: grpc.Metadata): Promise<MsgEmergencySettleMarketResponse>;
    /** IncreasePositionMargin defines a method for increasing margin of a position */
    IncreasePositionMargin(request: DeepPartial<MsgIncreasePositionMargin>, metadata?: grpc.Metadata): Promise<MsgIncreasePositionMarginResponse>;
    /** DecreasePositionMargin defines a method for decreasing margin of a position */
    DecreasePositionMargin(request: DeepPartial<MsgDecreasePositionMargin>, metadata?: grpc.Metadata): Promise<MsgDecreasePositionMarginResponse>;
    /** RewardsOptOut defines a method for opting out of rewards */
    RewardsOptOut(request: DeepPartial<MsgRewardsOptOut>, metadata?: grpc.Metadata): Promise<MsgRewardsOptOutResponse>;
    /**
     * AdminUpdateBinaryOptionsMarket defines method for updating a binary options
     * market by admin
     */
    AdminUpdateBinaryOptionsMarket(request: DeepPartial<MsgAdminUpdateBinaryOptionsMarket>, metadata?: grpc.Metadata): Promise<MsgAdminUpdateBinaryOptionsMarketResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    /** UpdateSpotMarket modifies certain spot market fields (admin only) */
    UpdateSpotMarket(request: DeepPartial<MsgUpdateSpotMarket>, metadata?: grpc.Metadata): Promise<MsgUpdateSpotMarketResponse>;
    /**
     * UpdateDerivativeMarket modifies certain derivative market fields (admin
     * only)
     */
    UpdateDerivativeMarket(request: DeepPartial<MsgUpdateDerivativeMarket>, metadata?: grpc.Metadata): Promise<MsgUpdateDerivativeMarketResponse>;
    AuthorizeStakeGrants(request: DeepPartial<MsgAuthorizeStakeGrants>, metadata?: grpc.Metadata): Promise<MsgAuthorizeStakeGrantsResponse>;
    ActivateStakeGrant(request: DeepPartial<MsgActivateStakeGrant>, metadata?: grpc.Metadata): Promise<MsgActivateStakeGrantResponse>;
    BatchExchangeModification(request: DeepPartial<MsgBatchExchangeModification>, metadata?: grpc.Metadata): Promise<MsgBatchExchangeModificationResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    Deposit(request: DeepPartial<MsgDeposit>, metadata?: grpc.Metadata): Promise<MsgDepositResponse>;
    Withdraw(request: DeepPartial<MsgWithdraw>, metadata?: grpc.Metadata): Promise<MsgWithdrawResponse>;
    InstantSpotMarketLaunch(request: DeepPartial<MsgInstantSpotMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantSpotMarketLaunchResponse>;
    InstantPerpetualMarketLaunch(request: DeepPartial<MsgInstantPerpetualMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantPerpetualMarketLaunchResponse>;
    InstantExpiryFuturesMarketLaunch(request: DeepPartial<MsgInstantExpiryFuturesMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantExpiryFuturesMarketLaunchResponse>;
    CreateSpotLimitOrder(request: DeepPartial<MsgCreateSpotLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateSpotLimitOrderResponse>;
    BatchCreateSpotLimitOrders(request: DeepPartial<MsgBatchCreateSpotLimitOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCreateSpotLimitOrdersResponse>;
    CreateSpotMarketOrder(request: DeepPartial<MsgCreateSpotMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateSpotMarketOrderResponse>;
    CancelSpotOrder(request: DeepPartial<MsgCancelSpotOrder>, metadata?: grpc.Metadata): Promise<MsgCancelSpotOrderResponse>;
    BatchCancelSpotOrders(request: DeepPartial<MsgBatchCancelSpotOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelSpotOrdersResponse>;
    BatchUpdateOrders(request: DeepPartial<MsgBatchUpdateOrders>, metadata?: grpc.Metadata): Promise<MsgBatchUpdateOrdersResponse>;
    PrivilegedExecuteContract(request: DeepPartial<MsgPrivilegedExecuteContract>, metadata?: grpc.Metadata): Promise<MsgPrivilegedExecuteContractResponse>;
    CreateDerivativeLimitOrder(request: DeepPartial<MsgCreateDerivativeLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateDerivativeLimitOrderResponse>;
    BatchCreateDerivativeLimitOrders(request: DeepPartial<MsgBatchCreateDerivativeLimitOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCreateDerivativeLimitOrdersResponse>;
    CreateDerivativeMarketOrder(request: DeepPartial<MsgCreateDerivativeMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateDerivativeMarketOrderResponse>;
    CancelDerivativeOrder(request: DeepPartial<MsgCancelDerivativeOrder>, metadata?: grpc.Metadata): Promise<MsgCancelDerivativeOrderResponse>;
    BatchCancelDerivativeOrders(request: DeepPartial<MsgBatchCancelDerivativeOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelDerivativeOrdersResponse>;
    InstantBinaryOptionsMarketLaunch(request: DeepPartial<MsgInstantBinaryOptionsMarketLaunch>, metadata?: grpc.Metadata): Promise<MsgInstantBinaryOptionsMarketLaunchResponse>;
    CreateBinaryOptionsLimitOrder(request: DeepPartial<MsgCreateBinaryOptionsLimitOrder>, metadata?: grpc.Metadata): Promise<MsgCreateBinaryOptionsLimitOrderResponse>;
    CreateBinaryOptionsMarketOrder(request: DeepPartial<MsgCreateBinaryOptionsMarketOrder>, metadata?: grpc.Metadata): Promise<MsgCreateBinaryOptionsMarketOrderResponse>;
    CancelBinaryOptionsOrder(request: DeepPartial<MsgCancelBinaryOptionsOrder>, metadata?: grpc.Metadata): Promise<MsgCancelBinaryOptionsOrderResponse>;
    BatchCancelBinaryOptionsOrders(request: DeepPartial<MsgBatchCancelBinaryOptionsOrders>, metadata?: grpc.Metadata): Promise<MsgBatchCancelBinaryOptionsOrdersResponse>;
    SubaccountTransfer(request: DeepPartial<MsgSubaccountTransfer>, metadata?: grpc.Metadata): Promise<MsgSubaccountTransferResponse>;
    ExternalTransfer(request: DeepPartial<MsgExternalTransfer>, metadata?: grpc.Metadata): Promise<MsgExternalTransferResponse>;
    LiquidatePosition(request: DeepPartial<MsgLiquidatePosition>, metadata?: grpc.Metadata): Promise<MsgLiquidatePositionResponse>;
    EmergencySettleMarket(request: DeepPartial<MsgEmergencySettleMarket>, metadata?: grpc.Metadata): Promise<MsgEmergencySettleMarketResponse>;
    IncreasePositionMargin(request: DeepPartial<MsgIncreasePositionMargin>, metadata?: grpc.Metadata): Promise<MsgIncreasePositionMarginResponse>;
    DecreasePositionMargin(request: DeepPartial<MsgDecreasePositionMargin>, metadata?: grpc.Metadata): Promise<MsgDecreasePositionMarginResponse>;
    RewardsOptOut(request: DeepPartial<MsgRewardsOptOut>, metadata?: grpc.Metadata): Promise<MsgRewardsOptOutResponse>;
    AdminUpdateBinaryOptionsMarket(request: DeepPartial<MsgAdminUpdateBinaryOptionsMarket>, metadata?: grpc.Metadata): Promise<MsgAdminUpdateBinaryOptionsMarketResponse>;
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    UpdateSpotMarket(request: DeepPartial<MsgUpdateSpotMarket>, metadata?: grpc.Metadata): Promise<MsgUpdateSpotMarketResponse>;
    UpdateDerivativeMarket(request: DeepPartial<MsgUpdateDerivativeMarket>, metadata?: grpc.Metadata): Promise<MsgUpdateDerivativeMarketResponse>;
    AuthorizeStakeGrants(request: DeepPartial<MsgAuthorizeStakeGrants>, metadata?: grpc.Metadata): Promise<MsgAuthorizeStakeGrantsResponse>;
    ActivateStakeGrant(request: DeepPartial<MsgActivateStakeGrant>, metadata?: grpc.Metadata): Promise<MsgActivateStakeGrantResponse>;
    BatchExchangeModification(request: DeepPartial<MsgBatchExchangeModification>, metadata?: grpc.Metadata): Promise<MsgBatchExchangeModificationResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgDepositDesc: UnaryMethodDefinitionish;
export declare const MsgWithdrawDesc: UnaryMethodDefinitionish;
export declare const MsgInstantSpotMarketLaunchDesc: UnaryMethodDefinitionish;
export declare const MsgInstantPerpetualMarketLaunchDesc: UnaryMethodDefinitionish;
export declare const MsgInstantExpiryFuturesMarketLaunchDesc: UnaryMethodDefinitionish;
export declare const MsgCreateSpotLimitOrderDesc: UnaryMethodDefinitionish;
export declare const MsgBatchCreateSpotLimitOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgCreateSpotMarketOrderDesc: UnaryMethodDefinitionish;
export declare const MsgCancelSpotOrderDesc: UnaryMethodDefinitionish;
export declare const MsgBatchCancelSpotOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgBatchUpdateOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgPrivilegedExecuteContractDesc: UnaryMethodDefinitionish;
export declare const MsgCreateDerivativeLimitOrderDesc: UnaryMethodDefinitionish;
export declare const MsgBatchCreateDerivativeLimitOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgCreateDerivativeMarketOrderDesc: UnaryMethodDefinitionish;
export declare const MsgCancelDerivativeOrderDesc: UnaryMethodDefinitionish;
export declare const MsgBatchCancelDerivativeOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgInstantBinaryOptionsMarketLaunchDesc: UnaryMethodDefinitionish;
export declare const MsgCreateBinaryOptionsLimitOrderDesc: UnaryMethodDefinitionish;
export declare const MsgCreateBinaryOptionsMarketOrderDesc: UnaryMethodDefinitionish;
export declare const MsgCancelBinaryOptionsOrderDesc: UnaryMethodDefinitionish;
export declare const MsgBatchCancelBinaryOptionsOrdersDesc: UnaryMethodDefinitionish;
export declare const MsgSubaccountTransferDesc: UnaryMethodDefinitionish;
export declare const MsgExternalTransferDesc: UnaryMethodDefinitionish;
export declare const MsgLiquidatePositionDesc: UnaryMethodDefinitionish;
export declare const MsgEmergencySettleMarketDesc: UnaryMethodDefinitionish;
export declare const MsgIncreasePositionMarginDesc: UnaryMethodDefinitionish;
export declare const MsgDecreasePositionMarginDesc: UnaryMethodDefinitionish;
export declare const MsgRewardsOptOutDesc: UnaryMethodDefinitionish;
export declare const MsgAdminUpdateBinaryOptionsMarketDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateSpotMarketDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateDerivativeMarketDesc: UnaryMethodDefinitionish;
export declare const MsgAuthorizeStakeGrantsDesc: UnaryMethodDefinitionish;
export declare const MsgActivateStakeGrantDesc: UnaryMethodDefinitionish;
export declare const MsgBatchExchangeModificationDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
