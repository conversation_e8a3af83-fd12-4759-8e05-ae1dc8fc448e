import BaseRestConsumer from '../../base/BaseRestConsumer.js';
import { BlockLatestRestResponse, NodeInfoRestResponse } from './../types/tendermint-rest.js';
/**
 * @category Chain Rest API
 */
export declare class ChainRestTendermintApi extends BaseRestConsumer {
    fetchLatestBlock(params?: Record<string, any>): Promise<BlockLatestRestResponse['block']>;
    fetchBlock(height: number | string, params?: Record<string, any>): Promise<BlockLatestRestResponse['block']>;
    fetchNodeInfo(params?: Record<string, any>): Promise<{
        nodeInfo: NodeInfoRestResponse['default_node_info'];
        applicationVersion: NodeInfoRestResponse['application_version'];
    }>;
}
