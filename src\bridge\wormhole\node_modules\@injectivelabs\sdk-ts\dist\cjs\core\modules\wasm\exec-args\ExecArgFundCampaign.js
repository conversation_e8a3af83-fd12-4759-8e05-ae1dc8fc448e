"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgFundCampaign extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgFundCampaign(params);
    }
    toData() {
        const { params } = this;
        return {
            campaign_id: params.campaignId,
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('fund_campaign', this.toData());
    }
}
exports.default = ExecArgFundCampaign;
