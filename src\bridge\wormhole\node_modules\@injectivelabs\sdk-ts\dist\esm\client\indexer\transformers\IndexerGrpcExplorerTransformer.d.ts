import { Block, Gas<PERSON>ee, GrpcGasFee, Transaction, BlockWithTxs, IBCTransferTx, ExplorerStats, PeggyDepositTx, ValidatorUptime, ExplorerValidator, PeggyWithdrawalTx, GrpcIBCTransferTx, GrpcPeggyDepositTx, GrpcValidatorUptime, GrpcPeggyWithdrawalTx, BankMsgSendTransaction, ValidatorSlashingEvent, IndexerStreamTransaction, GrpcValidatorSlashingEvent, ExplorerValidatorDescription, GrpcIndexerValidatorDescription, ExplorerTransaction, ContractTransaction } from '../types/explorer.js';
import { InjectiveExplorerRpc } from '@injectivelabs/indexer-proto-ts';
/**
 * @category Indexer Grpc Transformer
 */
export declare class IndexerGrpcExplorerTransformer {
    static getTxByTxHashResponseToTx(tx: InjectiveExplorerRpc.GetTxByTxHashResponse): Transaction;
    static getAccountTxsResponseToAccountTxs(response: InjectiveExplorerRpc.GetAccountTxsResponse): {
        txs: Transaction[];
        pagination: import("../../../index.js").ExchangePagination;
    };
    static getValidatorUptimeResponseToValidatorUptime(response: InjectiveExplorerRpc.GetValidatorUptimeResponse): ValidatorUptime[];
    static getPeggyDepositTxsResponseToPeggyDepositTxs(response: InjectiveExplorerRpc.GetPeggyDepositTxsResponse): PeggyDepositTx[];
    static getPeggyWithdrawalTxsResponseToPeggyWithdrawalTxs(response: InjectiveExplorerRpc.GetPeggyWithdrawalTxsResponse): PeggyWithdrawalTx[];
    static getIBCTransferTxsResponseToIBCTransferTxs(response: InjectiveExplorerRpc.GetIBCTransferTxsResponse): IBCTransferTx[];
    static validatorResponseToValidator(validator: InjectiveExplorerRpc.GetValidatorResponse): ExplorerValidator;
    static streamTxResponseToTxs(response: InjectiveExplorerRpc.StreamTxsResponse): IndexerStreamTransaction;
    static grpcGasFeeToGasFee(gasFee: GrpcGasFee): GasFee;
    static grpcTransactionToBankMsgSendTransaction(tx: InjectiveExplorerRpc.GetTxByTxHashResponse): BankMsgSendTransaction;
    static grpcTransactionToTransaction(tx: InjectiveExplorerRpc.GetTxByTxHashResponse): Transaction;
    static grpcTransactionsToTransactions(txs: Array<InjectiveExplorerRpc.GetTxByTxHashResponse>): Array<Transaction>;
    static grpcTransactionToTransactionFromDetail(tx: InjectiveExplorerRpc.TxDetailData): Transaction;
    static grpcTransactionsToTransactionsFromDetail(txs: InjectiveExplorerRpc.TxDetailData[]): Array<Transaction>;
    static grpcBlockToBlock(block: InjectiveExplorerRpc.BlockInfo): Block;
    static grpcBlockToBlockWithTxs(block: InjectiveExplorerRpc.BlockInfo): BlockWithTxs;
    static grpcBlocksToBlocks(blocks: Array<InjectiveExplorerRpc.BlockInfo>): Array<Block>;
    static grpcBlocksToBlocksWithTxs(blocks: Array<InjectiveExplorerRpc.BlockInfo>): Array<BlockWithTxs>;
    static grpcValidatorDescriptionToValidatorDescription(validatorDescription: GrpcIndexerValidatorDescription): ExplorerValidatorDescription;
    static grpcValidatorUptimeToValidatorUptime(validatorUptime: GrpcValidatorUptime): ValidatorUptime;
    static grpcValidatorSlashingEventToValidatorSlashingEvent(validatorUptime: GrpcValidatorSlashingEvent): ValidatorSlashingEvent;
    static grpcIBCTransferTxToIBCTransferTx(grpcIBCTransferTx: GrpcIBCTransferTx): IBCTransferTx;
    static grpcPeggyDepositTx(grpcPeggyDepositTx: GrpcPeggyDepositTx): PeggyDepositTx;
    static grpcPeggyWithdrawalTx(grpcPeggyWithdrawalTx: GrpcPeggyWithdrawalTx): PeggyWithdrawalTx;
    static getExplorerStatsResponseToExplorerStats(response: InjectiveExplorerRpc.GetStatsResponse): ExplorerStats;
    static getTxsV2ResponseToTxs(response: InjectiveExplorerRpc.GetTxsV2Response): {
        data: ExplorerTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    };
    static grpcTxV2ToTransaction(tx: InjectiveExplorerRpc.TxData): ExplorerTransaction;
    static getAccountTxsV2ResponseToAccountTxs(response: InjectiveExplorerRpc.GetAccountTxsV2Response): {
        data: ExplorerTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    };
    static grpcAccountTxV2ToTransaction(tx: InjectiveExplorerRpc.TxDetailData): ExplorerTransaction;
    static getBlocksV2ResponseToBlocks(response: InjectiveExplorerRpc.GetBlocksV2Response): {
        paging: InjectiveExplorerRpc.Cursor | undefined;
        data: Block[];
    };
    static grpcBlockV2ToBlock(block: InjectiveExplorerRpc.BlockInfo): Block;
    static getContractTxsV2ResponseToContractTxs(response: InjectiveExplorerRpc.GetContractTxsV2Response): {
        data: ContractTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    };
    static grpcContractTxV2ToTransaction(tx: InjectiveExplorerRpc.TxDetailData): ContractTransaction;
}
