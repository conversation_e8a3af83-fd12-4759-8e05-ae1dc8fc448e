import _m0 from "protobufjs/minimal.js";
import { ActiveGrant, AggregateSubaccountVolumeRecord, BinaryOptionsMarket, CampaignRewardPool, DenomDecimals, DenomMinNotional, Deposit, DerivativeLimitOrder, DerivativeMarket, DerivativeMarketOrder, DerivativeMarketSettlementInfo, ExpiryFuturesMarketInfo, FeeDiscountSchedule, FeeDiscountTierTTL, GrantAuthorization, MarketFeeMultiplier, MarketVolume, Params, PerpetualMarketFunding, PerpetualMarketInfo, Position, SpotLimitOrder, SpotMarket, SubaccountTradeNonce, TradeRecords, TradingRewardCampaignInfo } from "./exchange";
export declare const protobufPackage = "injective.exchange.v1beta1";
/** GenesisState defines the exchange module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to exchange. */
    params: Params | undefined;
    /** spot_markets is an array containing the genesis trade pairs */
    spotMarkets: SpotMarket[];
    /** derivative_markets is an array containing the genesis derivative markets */
    derivativeMarkets: DerivativeMarket[];
    /** spot_orderbook defines the spot exchange limit orderbook active at genesis. */
    spotOrderbook: SpotOrderBook[];
    /**
     * derivative_orderbook defines the derivative exchange limit orderbook active
     * at genesis.
     */
    derivativeOrderbook: DerivativeOrderBook[];
    /** balances defines the exchange users balances active at genesis. */
    balances: Balance[];
    /** positions defines the exchange derivative positions at genesis */
    positions: DerivativePosition[];
    /**
     * subaccount_trade_nonces defines the subaccount trade nonces for the
     * subaccounts at genesis
     */
    subaccountTradeNonces: SubaccountNonce[];
    /**
     * expiry_futures_market_info defines the market info for the expiry futures
     * markets at genesis
     */
    expiryFuturesMarketInfoState: ExpiryFuturesMarketInfoState[];
    /**
     * perpetual_market_info defines the market info for the perpetual derivative
     * markets at genesis
     */
    perpetualMarketInfo: PerpetualMarketInfo[];
    /**
     * perpetual_market_funding_state defines the funding state for the perpetual
     * derivative markets at genesis
     */
    perpetualMarketFundingState: PerpetualMarketFundingState[];
    /**
     * derivative_market_settlement_scheduled defines the scheduled markets for
     * settlement at genesis
     */
    derivativeMarketSettlementScheduled: DerivativeMarketSettlementInfo[];
    /** sets spot markets as enabled */
    isSpotExchangeEnabled: boolean;
    /** sets derivative markets as enabled */
    isDerivativesExchangeEnabled: boolean;
    /** the current trading reward campaign info */
    tradingRewardCampaignInfo: TradingRewardCampaignInfo | undefined;
    /** the current and upcoming trading reward campaign pools */
    tradingRewardPoolCampaignSchedule: CampaignRewardPool[];
    /** the current trading reward account points */
    tradingRewardCampaignAccountPoints: TradingRewardCampaignAccountPoints[];
    /** the fee discount schedule */
    feeDiscountSchedule: FeeDiscountSchedule | undefined;
    /** the cached fee discount account tiers with TTL */
    feeDiscountAccountTierTtl: FeeDiscountAccountTierTTL[];
    /** the fee discount paid by accounts in all buckets */
    feeDiscountBucketVolumeAccounts: FeeDiscountBucketVolumeAccounts[];
    /** sets the first fee cycle as finished */
    isFirstFeeCycleFinished: boolean;
    /** the current and upcoming trading reward campaign pending pools */
    pendingTradingRewardPoolCampaignSchedule: CampaignRewardPool[];
    /** the pending trading reward account points */
    pendingTradingRewardCampaignAccountPoints: TradingRewardCampaignAccountPendingPoints[];
    /** the addresses opting out of trading rewards */
    rewardsOptOutAddresses: string[];
    historicalTradeRecords: TradeRecords[];
    /**
     * binary_options_markets is an array containing the genesis binary options
     * markets
     */
    binaryOptionsMarkets: BinaryOptionsMarket[];
    /**
     * binary_options_markets_scheduled_for_settlement contains the marketIDs of
     * binary options markets scheduled for next-block settlement
     */
    binaryOptionsMarketIdsScheduledForSettlement: string[];
    /**
     * spot_market_ids_scheduled_to_force_close defines the scheduled markets for
     * forced closings at genesis
     */
    spotMarketIdsScheduledToForceClose: string[];
    /** denom_decimals defines the denom decimals for the exchange. */
    denomDecimals: DenomDecimals[];
    /**
     * conditional_derivative_orderbook contains conditional orderbooks for all
     * markets (both lmit and market conditional orders)
     */
    conditionalDerivativeOrderbooks: ConditionalDerivativeOrderBook[];
    /**
     * market_fee_multipliers contains any non-default atomic order fee
     * multipliers
     */
    marketFeeMultipliers: MarketFeeMultiplier[];
    orderbookSequences: OrderbookSequence[];
    subaccountVolumes: AggregateSubaccountVolumeRecord[];
    marketVolumes: MarketVolume[];
    grantAuthorizations: FullGrantAuthorizations[];
    activeGrants: FullActiveGrant[];
    denomMinNotionals: DenomMinNotional[];
}
export interface OrderbookSequence {
    sequence: string;
    marketId: string;
}
export interface FeeDiscountAccountTierTTL {
    account: string;
    tierTtl: FeeDiscountTierTTL | undefined;
}
export interface FeeDiscountBucketVolumeAccounts {
    bucketStartTimestamp: string;
    accountVolume: AccountVolume[];
}
export interface AccountVolume {
    account: string;
    volume: string;
}
export interface TradingRewardCampaignAccountPoints {
    account: string;
    points: string;
}
export interface TradingRewardCampaignAccountPendingPoints {
    rewardPoolStartTimestamp: string;
    accountPoints: TradingRewardCampaignAccountPoints[];
}
/** Spot Exchange Limit Orderbook */
export interface SpotOrderBook {
    marketId: string;
    isBuySide: boolean;
    orders: SpotLimitOrder[];
}
/** Derivative Exchange Limit Orderbook */
export interface DerivativeOrderBook {
    marketId: string;
    isBuySide: boolean;
    orders: DerivativeLimitOrder[];
}
/** Orderbook containing limit & market conditional orders */
export interface ConditionalDerivativeOrderBook {
    marketId: string;
    limitBuyOrders: DerivativeLimitOrder[];
    marketBuyOrders: DerivativeMarketOrder[];
    limitSellOrders: DerivativeLimitOrder[];
    marketSellOrders: DerivativeMarketOrder[];
}
export interface Balance {
    subaccountId: string;
    denom: string;
    deposits: Deposit | undefined;
}
export interface DerivativePosition {
    subaccountId: string;
    marketId: string;
    position: Position | undefined;
}
export interface SubaccountNonce {
    subaccountId: string;
    subaccountTradeNonce: SubaccountTradeNonce | undefined;
}
export interface ExpiryFuturesMarketInfoState {
    marketId: string;
    marketInfo: ExpiryFuturesMarketInfo | undefined;
}
export interface PerpetualMarketFundingState {
    marketId: string;
    funding: PerpetualMarketFunding | undefined;
}
export interface FullGrantAuthorizations {
    granter: string;
    totalGrantAmount: string;
    lastDelegationsCheckedTime: string;
    grants: GrantAuthorization[];
}
export interface FullActiveGrant {
    grantee: string;
    activeGrant: ActiveGrant | undefined;
}
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
export declare const OrderbookSequence: {
    encode(message: OrderbookSequence, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderbookSequence;
    fromJSON(object: any): OrderbookSequence;
    toJSON(message: OrderbookSequence): unknown;
    create(base?: DeepPartial<OrderbookSequence>): OrderbookSequence;
    fromPartial(object: DeepPartial<OrderbookSequence>): OrderbookSequence;
};
export declare const FeeDiscountAccountTierTTL: {
    encode(message: FeeDiscountAccountTierTTL, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountAccountTierTTL;
    fromJSON(object: any): FeeDiscountAccountTierTTL;
    toJSON(message: FeeDiscountAccountTierTTL): unknown;
    create(base?: DeepPartial<FeeDiscountAccountTierTTL>): FeeDiscountAccountTierTTL;
    fromPartial(object: DeepPartial<FeeDiscountAccountTierTTL>): FeeDiscountAccountTierTTL;
};
export declare const FeeDiscountBucketVolumeAccounts: {
    encode(message: FeeDiscountBucketVolumeAccounts, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FeeDiscountBucketVolumeAccounts;
    fromJSON(object: any): FeeDiscountBucketVolumeAccounts;
    toJSON(message: FeeDiscountBucketVolumeAccounts): unknown;
    create(base?: DeepPartial<FeeDiscountBucketVolumeAccounts>): FeeDiscountBucketVolumeAccounts;
    fromPartial(object: DeepPartial<FeeDiscountBucketVolumeAccounts>): FeeDiscountBucketVolumeAccounts;
};
export declare const AccountVolume: {
    encode(message: AccountVolume, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AccountVolume;
    fromJSON(object: any): AccountVolume;
    toJSON(message: AccountVolume): unknown;
    create(base?: DeepPartial<AccountVolume>): AccountVolume;
    fromPartial(object: DeepPartial<AccountVolume>): AccountVolume;
};
export declare const TradingRewardCampaignAccountPoints: {
    encode(message: TradingRewardCampaignAccountPoints, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignAccountPoints;
    fromJSON(object: any): TradingRewardCampaignAccountPoints;
    toJSON(message: TradingRewardCampaignAccountPoints): unknown;
    create(base?: DeepPartial<TradingRewardCampaignAccountPoints>): TradingRewardCampaignAccountPoints;
    fromPartial(object: DeepPartial<TradingRewardCampaignAccountPoints>): TradingRewardCampaignAccountPoints;
};
export declare const TradingRewardCampaignAccountPendingPoints: {
    encode(message: TradingRewardCampaignAccountPendingPoints, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradingRewardCampaignAccountPendingPoints;
    fromJSON(object: any): TradingRewardCampaignAccountPendingPoints;
    toJSON(message: TradingRewardCampaignAccountPendingPoints): unknown;
    create(base?: DeepPartial<TradingRewardCampaignAccountPendingPoints>): TradingRewardCampaignAccountPendingPoints;
    fromPartial(object: DeepPartial<TradingRewardCampaignAccountPendingPoints>): TradingRewardCampaignAccountPendingPoints;
};
export declare const SpotOrderBook: {
    encode(message: SpotOrderBook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotOrderBook;
    fromJSON(object: any): SpotOrderBook;
    toJSON(message: SpotOrderBook): unknown;
    create(base?: DeepPartial<SpotOrderBook>): SpotOrderBook;
    fromPartial(object: DeepPartial<SpotOrderBook>): SpotOrderBook;
};
export declare const DerivativeOrderBook: {
    encode(message: DerivativeOrderBook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeOrderBook;
    fromJSON(object: any): DerivativeOrderBook;
    toJSON(message: DerivativeOrderBook): unknown;
    create(base?: DeepPartial<DerivativeOrderBook>): DerivativeOrderBook;
    fromPartial(object: DeepPartial<DerivativeOrderBook>): DerivativeOrderBook;
};
export declare const ConditionalDerivativeOrderBook: {
    encode(message: ConditionalDerivativeOrderBook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ConditionalDerivativeOrderBook;
    fromJSON(object: any): ConditionalDerivativeOrderBook;
    toJSON(message: ConditionalDerivativeOrderBook): unknown;
    create(base?: DeepPartial<ConditionalDerivativeOrderBook>): ConditionalDerivativeOrderBook;
    fromPartial(object: DeepPartial<ConditionalDerivativeOrderBook>): ConditionalDerivativeOrderBook;
};
export declare const Balance: {
    encode(message: Balance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Balance;
    fromJSON(object: any): Balance;
    toJSON(message: Balance): unknown;
    create(base?: DeepPartial<Balance>): Balance;
    fromPartial(object: DeepPartial<Balance>): Balance;
};
export declare const DerivativePosition: {
    encode(message: DerivativePosition, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativePosition;
    fromJSON(object: any): DerivativePosition;
    toJSON(message: DerivativePosition): unknown;
    create(base?: DeepPartial<DerivativePosition>): DerivativePosition;
    fromPartial(object: DeepPartial<DerivativePosition>): DerivativePosition;
};
export declare const SubaccountNonce: {
    encode(message: SubaccountNonce, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountNonce;
    fromJSON(object: any): SubaccountNonce;
    toJSON(message: SubaccountNonce): unknown;
    create(base?: DeepPartial<SubaccountNonce>): SubaccountNonce;
    fromPartial(object: DeepPartial<SubaccountNonce>): SubaccountNonce;
};
export declare const ExpiryFuturesMarketInfoState: {
    encode(message: ExpiryFuturesMarketInfoState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ExpiryFuturesMarketInfoState;
    fromJSON(object: any): ExpiryFuturesMarketInfoState;
    toJSON(message: ExpiryFuturesMarketInfoState): unknown;
    create(base?: DeepPartial<ExpiryFuturesMarketInfoState>): ExpiryFuturesMarketInfoState;
    fromPartial(object: DeepPartial<ExpiryFuturesMarketInfoState>): ExpiryFuturesMarketInfoState;
};
export declare const PerpetualMarketFundingState: {
    encode(message: PerpetualMarketFundingState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PerpetualMarketFundingState;
    fromJSON(object: any): PerpetualMarketFundingState;
    toJSON(message: PerpetualMarketFundingState): unknown;
    create(base?: DeepPartial<PerpetualMarketFundingState>): PerpetualMarketFundingState;
    fromPartial(object: DeepPartial<PerpetualMarketFundingState>): PerpetualMarketFundingState;
};
export declare const FullGrantAuthorizations: {
    encode(message: FullGrantAuthorizations, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FullGrantAuthorizations;
    fromJSON(object: any): FullGrantAuthorizations;
    toJSON(message: FullGrantAuthorizations): unknown;
    create(base?: DeepPartial<FullGrantAuthorizations>): FullGrantAuthorizations;
    fromPartial(object: DeepPartial<FullGrantAuthorizations>): FullGrantAuthorizations;
};
export declare const FullActiveGrant: {
    encode(message: FullActiveGrant, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): FullActiveGrant;
    fromJSON(object: any): FullActiveGrant;
    toJSON(message: FullActiveGrant): unknown;
    create(base?: DeepPartial<FullActiveGrant>): FullActiveGrant;
    fromPartial(object: DeepPartial<FullActiveGrant>): FullActiveGrant;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
