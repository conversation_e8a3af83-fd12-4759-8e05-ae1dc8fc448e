{"version": 3, "file": "bytes.d.ts", "sourceRoot": "", "sources": ["../../src/bytes.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,IAAI,qBAAqB,EAAE,MAAM,gCAAgC,CAAA;AAKpF,OAAO,KAAK,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,YAAY,CAAA;AAIzE;;GAEG;AACH,eAAO,MAAM,oBAAoB,8BAAwB,CAAA;AA8BzD;;GAEG;AACH,eAAO,MAAM,oBAAoB,QAAS,MAAM,eAM/C,CAAA;AAMD,eAAO,MAAM,UAAU,UAAW,UAAU,KAAG,iBAO9C,CAAA;AAQD;;;;GAIG;AACH,eAAO,MAAM,aAAa,UAAW,UAAU,6BAAyB,MAgBvE,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,UAAU,UAAW,UAAU,KAAG,MAI9C,CAAA;AAGD;;;;;GAKG;AACH,eAAO,MAAM,UAAU,QAAS,iBAAiB,GAAG,MAAM,KAAG,UAc5D,CAAA;AAED,4CAA4C;AAE5C;;;;GAIG;AACH,eAAO,MAAM,QAAQ,MAAO,MAAM,KAAG,iBAKpC,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,UAAU,MAAO,MAAM,KAAG,UAGtC,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,aAAa,QAAS,MAAM,6BAAyB,UAKjE,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,KAAK,UAAW,MAAM,KAAG,UAErC,CAAA;AAwBD;;;;;;GAMG;AACH,eAAO,MAAM,aAAa,QAAS,UAAU,UAAU,MAAM,KAAG,UAG/D,CAAA;AAED;;;;;;GAMG;AACH,eAAO,MAAM,cAAc,QAAS,UAAU,UAAU,MAAM,KAAG,UAGhE,CAAA;AAkBD;;;;GAIG;AACH,eAAO,MAAM,UAAU,MAAO,UAAU,KAAG,UAG1C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,UAAU,MAAO,MAAM,EAAE,KAAG,MAAM,EAG9C,CAAA;AAGD;;;;GAIG;AACH,eAAO,MAAM,QAAQ,MAAO,iBAAiB,GAAG,MAAM,KAAG,iBAGxD,CAAA;AAGD,oBAAY,iBAAiB,GACzB,iBAAiB,GACjB,MAAM,GACN,MAAM,GACN,MAAM,GACN,UAAU,GACV,MAAM,EAAE,GACR,oBAAoB,GACpB,IAAI,GACJ,SAAS,CAAA;AAEb;;;;;;GAMG;AAEH,eAAO,MAAM,OAAO,MAAO,iBAAiB,KAAG,UAqC9C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,UAAU,QAAS,UAAU,KAAG,MAE5C,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,UAAU,QAAS,MAAM,KAAG,UAExC,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,YAAY,QAAS,MAAM,KAAG,iBAM1C,CAAA;AAED;;;;;;;;;;GAUG;AACH,eAAO,MAAM,KAAK,UAAW,UAAU,GAAG,MAAM,cAAa,MAAM,KAAQ,MAO1E,CAAA;AAED;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,uBAAuB;;UAMnC,CAAA;AAED;;;;GAIG;AACH,eAAO,MAAM,WAAW,QAAS,MAAM,KAAG,iBAEzC,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,SAAS,YAAa,MAAM,EAAE,WAA2C,CAAA;AAEtF;;;GAGG;AACH,eAAO,MAAM,SAAS,YAAa,MAAM,EAAE,WAA2C,CAAA;AAEtF;;;;;GAKG;AACH,eAAO,MAAM,qBAAqB,UAAW,MAAM,KAAG,UAErD,CAAA;AAED,eAAO,MAAM,oBAAoB,UAAW,MAAM,WAAU,OAAO,KAAU,UAQ5E,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,UAAW,MAAM,KAAG,UAElD,CAAA;AAED;;;;;;;;GAQG;AACH,eAAO,MAAM,YAAY,WAAY,UAAU,UAAU,UAAU,KAAG,MAIrE,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,WAAW,WAAY,MAAM,KAAG,UAE5C,CAAA;AAED;;;;;;;GAOG;AACH,eAAO,MAAM,WAAW,cAAe,UAAU,EAAE,KAAG,UAUrD,CAAA;AAED;;;;;GAKG;AACH,wBAAgB,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,GAAE,OAAe,GAAG,MAAM,CAMrF;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,GAAE,OAAe,GAAG,MAAM,CAMxF;AAED;;;;;GAKG;AACH,wBAAgB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,GAAE,OAAe,GAAG,UAAU,CAKrF;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,GAAE,OAAe,GAAG,UAAU,CAKxF;AAGD,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAGtF,wBAAgB,WAAW,CAAC,KAAK,EAAE,iBAAiB,GAAG,MAAM,GAAG,MAAM,CAErE"}