import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Params } from "./params";
import { Namespace, PolicyManagerCapability, PolicyStatus, Role, RoleActors, RoleManager } from "./permissions";
export declare const protobufPackage = "injective.permissions.v1beta1";
export interface MsgUpdateParams {
    /** authority is the address of the governance account. */
    authority: string;
    /**
     * params defines the permissions parameters to update.
     *
     * NOTE: All parameters must be supplied.
     */
    params: Params | undefined;
}
export interface MsgUpdateParamsResponse {
}
export interface MsgCreateNamespace {
    sender: string;
    namespace: Namespace | undefined;
}
export interface MsgCreateNamespaceResponse {
}
export interface MsgUpdateNamespace {
    sender: string;
    /** denom whose namespace updates are to be applied */
    denom: string;
    /** address of smart contract to apply code-based restrictions */
    contractHook: MsgUpdateNamespace_SetContractHook | undefined;
    /** role permissions to update */
    rolePermissions: Role[];
    /** role managers to update */
    roleManagers: RoleManager[];
    /** policy statuses to update */
    policyStatuses: PolicyStatus[];
    /** policy manager capabilities to update */
    policyManagerCapabilities: PolicyManagerCapability[];
}
export interface MsgUpdateNamespace_SetContractHook {
    newValue: string;
}
export interface MsgUpdateNamespaceResponse {
}
export interface MsgUpdateActorRoles {
    sender: string;
    /** namespace denom to which this updates are applied */
    denom: string;
    /** roles to add for given actors */
    roleActorsToAdd: RoleActors[];
    /** roles to revoke from given actors */
    roleActorsToRevoke: RoleActors[];
}
export interface MsgUpdateActorRolesResponse {
}
export interface MsgClaimVoucher {
    sender: string;
    denom: string;
}
export interface MsgClaimVoucherResponse {
}
export declare const MsgUpdateParams: {
    encode(message: MsgUpdateParams, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParams;
    fromJSON(object: any): MsgUpdateParams;
    toJSON(message: MsgUpdateParams): unknown;
    create(base?: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
    fromPartial(object: DeepPartial<MsgUpdateParams>): MsgUpdateParams;
};
export declare const MsgUpdateParamsResponse: {
    encode(_: MsgUpdateParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateParamsResponse;
    fromJSON(_: any): MsgUpdateParamsResponse;
    toJSON(_: MsgUpdateParamsResponse): unknown;
    create(base?: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
    fromPartial(_: DeepPartial<MsgUpdateParamsResponse>): MsgUpdateParamsResponse;
};
export declare const MsgCreateNamespace: {
    encode(message: MsgCreateNamespace, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateNamespace;
    fromJSON(object: any): MsgCreateNamespace;
    toJSON(message: MsgCreateNamespace): unknown;
    create(base?: DeepPartial<MsgCreateNamespace>): MsgCreateNamespace;
    fromPartial(object: DeepPartial<MsgCreateNamespace>): MsgCreateNamespace;
};
export declare const MsgCreateNamespaceResponse: {
    encode(_: MsgCreateNamespaceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgCreateNamespaceResponse;
    fromJSON(_: any): MsgCreateNamespaceResponse;
    toJSON(_: MsgCreateNamespaceResponse): unknown;
    create(base?: DeepPartial<MsgCreateNamespaceResponse>): MsgCreateNamespaceResponse;
    fromPartial(_: DeepPartial<MsgCreateNamespaceResponse>): MsgCreateNamespaceResponse;
};
export declare const MsgUpdateNamespace: {
    encode(message: MsgUpdateNamespace, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateNamespace;
    fromJSON(object: any): MsgUpdateNamespace;
    toJSON(message: MsgUpdateNamespace): unknown;
    create(base?: DeepPartial<MsgUpdateNamespace>): MsgUpdateNamespace;
    fromPartial(object: DeepPartial<MsgUpdateNamespace>): MsgUpdateNamespace;
};
export declare const MsgUpdateNamespace_SetContractHook: {
    encode(message: MsgUpdateNamespace_SetContractHook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateNamespace_SetContractHook;
    fromJSON(object: any): MsgUpdateNamespace_SetContractHook;
    toJSON(message: MsgUpdateNamespace_SetContractHook): unknown;
    create(base?: DeepPartial<MsgUpdateNamespace_SetContractHook>): MsgUpdateNamespace_SetContractHook;
    fromPartial(object: DeepPartial<MsgUpdateNamespace_SetContractHook>): MsgUpdateNamespace_SetContractHook;
};
export declare const MsgUpdateNamespaceResponse: {
    encode(_: MsgUpdateNamespaceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateNamespaceResponse;
    fromJSON(_: any): MsgUpdateNamespaceResponse;
    toJSON(_: MsgUpdateNamespaceResponse): unknown;
    create(base?: DeepPartial<MsgUpdateNamespaceResponse>): MsgUpdateNamespaceResponse;
    fromPartial(_: DeepPartial<MsgUpdateNamespaceResponse>): MsgUpdateNamespaceResponse;
};
export declare const MsgUpdateActorRoles: {
    encode(message: MsgUpdateActorRoles, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateActorRoles;
    fromJSON(object: any): MsgUpdateActorRoles;
    toJSON(message: MsgUpdateActorRoles): unknown;
    create(base?: DeepPartial<MsgUpdateActorRoles>): MsgUpdateActorRoles;
    fromPartial(object: DeepPartial<MsgUpdateActorRoles>): MsgUpdateActorRoles;
};
export declare const MsgUpdateActorRolesResponse: {
    encode(_: MsgUpdateActorRolesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgUpdateActorRolesResponse;
    fromJSON(_: any): MsgUpdateActorRolesResponse;
    toJSON(_: MsgUpdateActorRolesResponse): unknown;
    create(base?: DeepPartial<MsgUpdateActorRolesResponse>): MsgUpdateActorRolesResponse;
    fromPartial(_: DeepPartial<MsgUpdateActorRolesResponse>): MsgUpdateActorRolesResponse;
};
export declare const MsgClaimVoucher: {
    encode(message: MsgClaimVoucher, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgClaimVoucher;
    fromJSON(object: any): MsgClaimVoucher;
    toJSON(message: MsgClaimVoucher): unknown;
    create(base?: DeepPartial<MsgClaimVoucher>): MsgClaimVoucher;
    fromPartial(object: DeepPartial<MsgClaimVoucher>): MsgClaimVoucher;
};
export declare const MsgClaimVoucherResponse: {
    encode(_: MsgClaimVoucherResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): MsgClaimVoucherResponse;
    fromJSON(_: any): MsgClaimVoucherResponse;
    toJSON(_: MsgClaimVoucherResponse): unknown;
    create(base?: DeepPartial<MsgClaimVoucherResponse>): MsgClaimVoucherResponse;
    fromPartial(_: DeepPartial<MsgClaimVoucherResponse>): MsgClaimVoucherResponse;
};
/** Msg defines the permissions module's gRPC message service. */
export interface Msg {
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    CreateNamespace(request: DeepPartial<MsgCreateNamespace>, metadata?: grpc.Metadata): Promise<MsgCreateNamespaceResponse>;
    UpdateNamespace(request: DeepPartial<MsgUpdateNamespace>, metadata?: grpc.Metadata): Promise<MsgUpdateNamespaceResponse>;
    UpdateActorRoles(request: DeepPartial<MsgUpdateActorRoles>, metadata?: grpc.Metadata): Promise<MsgUpdateActorRolesResponse>;
    ClaimVoucher(request: DeepPartial<MsgClaimVoucher>, metadata?: grpc.Metadata): Promise<MsgClaimVoucherResponse>;
}
export declare class MsgClientImpl implements Msg {
    private readonly rpc;
    constructor(rpc: Rpc);
    UpdateParams(request: DeepPartial<MsgUpdateParams>, metadata?: grpc.Metadata): Promise<MsgUpdateParamsResponse>;
    CreateNamespace(request: DeepPartial<MsgCreateNamespace>, metadata?: grpc.Metadata): Promise<MsgCreateNamespaceResponse>;
    UpdateNamespace(request: DeepPartial<MsgUpdateNamespace>, metadata?: grpc.Metadata): Promise<MsgUpdateNamespaceResponse>;
    UpdateActorRoles(request: DeepPartial<MsgUpdateActorRoles>, metadata?: grpc.Metadata): Promise<MsgUpdateActorRolesResponse>;
    ClaimVoucher(request: DeepPartial<MsgClaimVoucher>, metadata?: grpc.Metadata): Promise<MsgClaimVoucherResponse>;
}
export declare const MsgDesc: {
    serviceName: string;
};
export declare const MsgUpdateParamsDesc: UnaryMethodDefinitionish;
export declare const MsgCreateNamespaceDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateNamespaceDesc: UnaryMethodDefinitionish;
export declare const MsgUpdateActorRolesDesc: UnaryMethodDefinitionish;
export declare const MsgClaimVoucherDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
