{"version": 3, "file": "array.js", "sourceRoot": "", "sources": ["../../src.ts/coders/array.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AACtC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,OAAO,EAAE,KAAK,EAAkB,MAAM,EAAE,MAAM,kBAAkB,CAAC;AACjE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAE7C,MAAM,UAAU,IAAI,CAAC,MAAc,EAAE,MAA4B,EAAE,MAA8C;IAC7G,IAAI,WAAW,GAAe,IAAI,CAAC;IAEnC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACxB,WAAW,GAAG,MAAM,CAAC;KAEvB;SAAM,IAAI,MAAM,IAAI,OAAM,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;QAC9C,IAAI,MAAM,GAAkC,EAAG,CAAC;QAEhD,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE;gBACP,MAAM,CAAC,UAAU,CAAC,uDAAuD,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACvG,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,MAAM;iBAChB,CAAC,CAAC;aACN;YAED,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;gBACd,MAAM,CAAC,UAAU,CAAC,yDAAyD,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACzG,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,KAAK;oBACZ,KAAK,EAAE,MAAM;iBAChB,CAAC,CAAC;aACN;YAED,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;YAEpB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;KAEN;SAAM;QACH,MAAM,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KACrE;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;QACtC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;KAC7E;IAED,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/C,IAAI,aAAa,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAEhD,IAAI,WAAW,GAAwC,EAAE,CAAC;IAC1D,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC5B,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,sDAAsD;YACtD,IAAI,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;YAEzC,kDAAkD;YAClD,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAEnC,0DAA0D;YAC1D,IAAI,UAAU,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACpD,WAAW,CAAC,IAAI,CAAC,CAAC,UAAkB,EAAE,EAAE;gBACpC,UAAU,CAAC,UAAU,GAAG,aAAa,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;SAEN;aAAM;YACH,KAAK,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SACrC;IACL,CAAC,CAAC,CAAC;IAEH,uEAAuE;IACvE,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,IAAI,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC/C,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAC7C,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,MAAc,EAAE,MAAoB;IACvD,IAAI,MAAM,GAAQ,EAAE,CAAC;IAErB,iCAAiC;IACjC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAErC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;QACrB,IAAI,KAAK,GAAQ,IAAI,CAAC;QAEtB,IAAI,KAAK,CAAC,OAAO,EAAE;YACf,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3D,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aACtC;YAAC,OAAO,KAAK,EAAE;gBACZ,2BAA2B;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAAE,MAAM,KAAK,CAAC;iBAAE;gBACjE,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SAEJ;aAAM;YACH,IAAI;gBACA,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAChC;YAAC,OAAO,KAAK,EAAE;gBACZ,2BAA2B;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAAE,MAAM,KAAK,CAAC;iBAAE;gBACjE,KAAK,GAAG,KAAK,CAAC;gBACd,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;gBAC7B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SACJ;QAED,IAAI,KAAK,IAAI,SAAS,EAAE;YACpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;IACL,CAAC,CAAC,CAAC;IAEH,4DAA4D;IAC5D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;QAC7B,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAAE;YACtC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;SACjB;QACD,OAAO,KAAK,CAAC;IACjB,CAAC,EAAgC,EAAG,CAAC,CAAC;IAEtC,yCAAyC;IACzC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,KAAa,EAAE,EAAE;QAC3C,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;QAC3B,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAAE,OAAO;SAAE;QAEjD,IAAI,IAAI,KAAK,QAAQ,EAAE;YAAE,IAAI,GAAG,SAAS,CAAC;SAAE;QAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;YAAE,OAAO;SAAE;QAErC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAE5B,IAAI,KAAK,YAAY,KAAK,EAAE;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;gBAChC,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;aAC9B,CAAC,CAAC;SACN;aAAM;YACH,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;SACxB;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,KAAK,YAAY,KAAK,EAAE;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE;gBAC7B,UAAU,EAAE,IAAI;gBAChB,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;aAC9B,CAAC,CAAC;SACN;KACJ;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAGD,MAAM,OAAO,UAAW,SAAQ,KAAK;IAIjC,YAAY,KAAY,EAAE,MAAc,EAAE,SAAiB;QACvD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,YAAY;QACR,+EAA+E;QAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAE/C,MAAM,MAAM,GAAe,EAAE,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7B;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,KAAiB;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;SACnD;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAExB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;YACrB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACnC;QAED,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAA,CAAC,CAAC,CAAC,GAAG,GAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5G,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAAE;QAEnE,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,KAAK,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC;YAEtC,sDAAsD;YACtD,wDAAwD;YACxD,yDAAyD;YACzD,sDAAsD;YACtD,4DAA4D;YAC5D,IAAI,KAAK,GAAG,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE;gBAClC,MAAM,CAAC,UAAU,CAAC,0BAA0B,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBACxE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;oBAC3B,KAAK,EAAE,KAAK;iBACf,CAAC,CAAC;aACN;SACJ;QACD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;SAAE;QAEhF,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5D,CAAC;CACJ"}