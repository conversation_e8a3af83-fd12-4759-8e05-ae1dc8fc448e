import { MsgBase } from '../../MsgBase.js';
import { CosmwasmWasmV1Tx, CosmwasmWasmV1Types } from '@injectivelabs/core-proto-ts';
export declare namespace MsgStoreCode {
    interface Params {
        sender: string;
        wasmBytes: Uint8Array | string;
        instantiatePermission?: {
            permission: CosmwasmWasmV1Types.AccessType;
            addresses: string[];
        };
    }
    type Proto = CosmwasmWasmV1Tx.MsgStoreCode;
}
/**
 * @category Messages
 */
export default class MsgStoreCode extends MsgBase<MsgStoreCode.Params, MsgStoreCode.Proto> {
    static fromJSON(params: MsgStoreCode.Params): MsgStoreCode;
    toProto(): CosmwasmWasmV1Tx.MsgStoreCode;
    toData(): {
        sender: string;
        wasmByteCode: Uint8Array;
        instantiatePermission: CosmwasmWasmV1Types.AccessConfig | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            wasm_byte_code: string;
            instantiate_permission: {
                permission: string;
                addresses: string[];
            } | undefined;
            sender: string;
        };
    };
    toWeb3Gw(): {
        wasm_byte_code: string;
        instantiate_permission: {
            permission: string;
            addresses: string[];
        } | undefined;
        sender: string;
        '@type': string;
    };
    toEip712(): never;
    toDirectSign(): {
        type: string;
        message: CosmwasmWasmV1Tx.MsgStoreCode;
    };
    toBinary(): Uint8Array;
}
