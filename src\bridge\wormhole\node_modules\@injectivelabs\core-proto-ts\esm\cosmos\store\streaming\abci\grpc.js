/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import Long from "long";
import _m0 from "protobufjs/minimal.js";
import { RequestFinalizeBlock, ResponseCommit, ResponseFinalizeBlock } from "../../../../tendermint/abci/types.js";
import { StoreKVPair } from "../../v1beta1/listening.js";
export const protobufPackage = "cosmos.store.streaming.abci";
function createBaseListenFinalizeBlockRequest() {
    return { req: undefined, res: undefined };
}
export const ListenFinalizeBlockRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.req !== undefined) {
            RequestFinalizeBlock.encode(message.req, writer.uint32(10).fork()).ldelim();
        }
        if (message.res !== undefined) {
            ResponseFinalizeBlock.encode(message.res, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListenFinalizeBlockRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.req = RequestFinalizeBlock.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.res = ResponseFinalizeBlock.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            req: isSet(object.req) ? RequestFinalizeBlock.fromJSON(object.req) : undefined,
            res: isSet(object.res) ? ResponseFinalizeBlock.fromJSON(object.res) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.req !== undefined && (obj.req = message.req ? RequestFinalizeBlock.toJSON(message.req) : undefined);
        message.res !== undefined && (obj.res = message.res ? ResponseFinalizeBlock.toJSON(message.res) : undefined);
        return obj;
    },
    create(base) {
        return ListenFinalizeBlockRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseListenFinalizeBlockRequest();
        message.req = (object.req !== undefined && object.req !== null)
            ? RequestFinalizeBlock.fromPartial(object.req)
            : undefined;
        message.res = (object.res !== undefined && object.res !== null)
            ? ResponseFinalizeBlock.fromPartial(object.res)
            : undefined;
        return message;
    },
};
function createBaseListenFinalizeBlockResponse() {
    return {};
}
export const ListenFinalizeBlockResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListenFinalizeBlockResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return ListenFinalizeBlockResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseListenFinalizeBlockResponse();
        return message;
    },
};
function createBaseListenCommitRequest() {
    return { blockHeight: "0", res: undefined, changeSet: [] };
}
export const ListenCommitRequest = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.blockHeight !== "0") {
            writer.uint32(8).int64(message.blockHeight);
        }
        if (message.res !== undefined) {
            ResponseCommit.encode(message.res, writer.uint32(18).fork()).ldelim();
        }
        for (const v of message.changeSet) {
            StoreKVPair.encode(v, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListenCommitRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockHeight = longToString(reader.int64());
                    break;
                case 2:
                    message.res = ResponseCommit.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.changeSet.push(StoreKVPair.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            blockHeight: isSet(object.blockHeight) ? String(object.blockHeight) : "0",
            res: isSet(object.res) ? ResponseCommit.fromJSON(object.res) : undefined,
            changeSet: Array.isArray(object?.changeSet) ? object.changeSet.map((e) => StoreKVPair.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.blockHeight !== undefined && (obj.blockHeight = message.blockHeight);
        message.res !== undefined && (obj.res = message.res ? ResponseCommit.toJSON(message.res) : undefined);
        if (message.changeSet) {
            obj.changeSet = message.changeSet.map((e) => e ? StoreKVPair.toJSON(e) : undefined);
        }
        else {
            obj.changeSet = [];
        }
        return obj;
    },
    create(base) {
        return ListenCommitRequest.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseListenCommitRequest();
        message.blockHeight = object.blockHeight ?? "0";
        message.res = (object.res !== undefined && object.res !== null)
            ? ResponseCommit.fromPartial(object.res)
            : undefined;
        message.changeSet = object.changeSet?.map((e) => StoreKVPair.fromPartial(e)) || [];
        return message;
    },
};
function createBaseListenCommitResponse() {
    return {};
}
export const ListenCommitResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseListenCommitResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return ListenCommitResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseListenCommitResponse();
        return message;
    },
};
export class ABCIListenerServiceClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.ListenFinalizeBlock = this.ListenFinalizeBlock.bind(this);
        this.ListenCommit = this.ListenCommit.bind(this);
    }
    ListenFinalizeBlock(request, metadata) {
        return this.rpc.unary(ABCIListenerServiceListenFinalizeBlockDesc, ListenFinalizeBlockRequest.fromPartial(request), metadata);
    }
    ListenCommit(request, metadata) {
        return this.rpc.unary(ABCIListenerServiceListenCommitDesc, ListenCommitRequest.fromPartial(request), metadata);
    }
}
export const ABCIListenerServiceDesc = { serviceName: "cosmos.store.streaming.abci.ABCIListenerService" };
export const ABCIListenerServiceListenFinalizeBlockDesc = {
    methodName: "ListenFinalizeBlock",
    service: ABCIListenerServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ListenFinalizeBlockRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ListenFinalizeBlockResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const ABCIListenerServiceListenCommitDesc = {
    methodName: "ListenCommit",
    service: ABCIListenerServiceDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return ListenCommitRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = ListenCommitResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (_m0.util.Long !== Long) {
    _m0.util.Long = Long;
    _m0.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
