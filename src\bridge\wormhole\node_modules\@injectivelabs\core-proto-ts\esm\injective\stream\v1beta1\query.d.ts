import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { Observable } from "rxjs";
import { Coin } from "../../../cosmos/base/v1beta1/coin";
import { Deposit, DerivativeLimitOrder, Level, PositionDelta, SpotLimitOrder } from "../../exchange/v1beta1/exchange";
export declare const protobufPackage = "injective.stream.v1beta1";
export declare enum OrderUpdateStatus {
    Unspecified = 0,
    Booked = 1,
    Matched = 2,
    Cancelled = 3,
    UNRECOGNIZED = -1
}
export declare function orderUpdateStatusFromJSON(object: any): OrderUpdateStatus;
export declare function orderUpdateStatusToJSON(object: OrderUpdateStatus): string;
export interface StreamRequest {
    bankBalancesFilter: BankBalancesFilter | undefined;
    subaccountDepositsFilter: SubaccountDepositsFilter | undefined;
    spotTradesFilter: TradesFilter | undefined;
    derivativeTradesFilter: TradesFilter | undefined;
    spotOrdersFilter: OrdersFilter | undefined;
    derivativeOrdersFilter: OrdersFilter | undefined;
    spotOrderbooksFilter: OrderbookFilter | undefined;
    derivativeOrderbooksFilter: OrderbookFilter | undefined;
    positionsFilter: PositionsFilter | undefined;
    oraclePriceFilter: OraclePriceFilter | undefined;
}
export interface StreamResponse {
    blockHeight: string;
    blockTime: string;
    bankBalances: BankBalance[];
    subaccountDeposits: SubaccountDeposits[];
    spotTrades: SpotTrade[];
    derivativeTrades: DerivativeTrade[];
    spotOrders: SpotOrderUpdate[];
    derivativeOrders: DerivativeOrderUpdate[];
    spotOrderbookUpdates: OrderbookUpdate[];
    derivativeOrderbookUpdates: OrderbookUpdate[];
    positions: Position[];
    oraclePrices: OraclePrice[];
}
export interface OrderbookUpdate {
    seq: string;
    orderbook: Orderbook | undefined;
}
export interface Orderbook {
    marketId: string;
    buyLevels: Level[];
    sellLevels: Level[];
}
export interface BankBalance {
    account: string;
    balances: Coin[];
}
export interface SubaccountDeposits {
    subaccountId: string;
    deposits: SubaccountDeposit[];
}
export interface SubaccountDeposit {
    denom: string;
    deposit: Deposit | undefined;
}
export interface SpotOrderUpdate {
    status: OrderUpdateStatus;
    orderHash: string;
    cid: string;
    order: SpotOrder | undefined;
}
export interface SpotOrder {
    marketId: string;
    order: SpotLimitOrder | undefined;
}
export interface DerivativeOrderUpdate {
    status: OrderUpdateStatus;
    orderHash: string;
    cid: string;
    order: DerivativeOrder | undefined;
}
export interface DerivativeOrder {
    marketId: string;
    order: DerivativeLimitOrder | undefined;
    isMarket: boolean;
}
export interface Position {
    marketId: string;
    subaccountId: string;
    isLong: boolean;
    quantity: string;
    entryPrice: string;
    margin: string;
    cumulativeFundingEntry: string;
}
export interface OraclePrice {
    symbol: string;
    price: string;
    type: string;
}
export interface SpotTrade {
    marketId: string;
    isBuy: boolean;
    executionType: string;
    quantity: string;
    price: string;
    /** bytes32 subaccount ID that executed the trade */
    subaccountId: string;
    fee: string;
    orderHash: string;
    feeRecipientAddress: string;
    cid: string;
    tradeId: string;
}
export interface DerivativeTrade {
    marketId: string;
    isBuy: boolean;
    executionType: string;
    subaccountId: string;
    positionDelta: PositionDelta | undefined;
    payout: string;
    fee: string;
    orderHash: string;
    feeRecipientAddress: string;
    cid: string;
    tradeId: string;
}
export interface TradesFilter {
    subaccountIds: string[];
    marketIds: string[];
}
export interface PositionsFilter {
    subaccountIds: string[];
    marketIds: string[];
}
export interface OrdersFilter {
    subaccountIds: string[];
    marketIds: string[];
}
export interface OrderbookFilter {
    marketIds: string[];
}
export interface BankBalancesFilter {
    accounts: string[];
}
export interface SubaccountDepositsFilter {
    subaccountIds: string[];
}
export interface OraclePriceFilter {
    symbol: string[];
}
export declare const StreamRequest: {
    encode(message: StreamRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamRequest;
    fromJSON(object: any): StreamRequest;
    toJSON(message: StreamRequest): unknown;
    create(base?: DeepPartial<StreamRequest>): StreamRequest;
    fromPartial(object: DeepPartial<StreamRequest>): StreamRequest;
};
export declare const StreamResponse: {
    encode(message: StreamResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): StreamResponse;
    fromJSON(object: any): StreamResponse;
    toJSON(message: StreamResponse): unknown;
    create(base?: DeepPartial<StreamResponse>): StreamResponse;
    fromPartial(object: DeepPartial<StreamResponse>): StreamResponse;
};
export declare const OrderbookUpdate: {
    encode(message: OrderbookUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderbookUpdate;
    fromJSON(object: any): OrderbookUpdate;
    toJSON(message: OrderbookUpdate): unknown;
    create(base?: DeepPartial<OrderbookUpdate>): OrderbookUpdate;
    fromPartial(object: DeepPartial<OrderbookUpdate>): OrderbookUpdate;
};
export declare const Orderbook: {
    encode(message: Orderbook, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Orderbook;
    fromJSON(object: any): Orderbook;
    toJSON(message: Orderbook): unknown;
    create(base?: DeepPartial<Orderbook>): Orderbook;
    fromPartial(object: DeepPartial<Orderbook>): Orderbook;
};
export declare const BankBalance: {
    encode(message: BankBalance, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BankBalance;
    fromJSON(object: any): BankBalance;
    toJSON(message: BankBalance): unknown;
    create(base?: DeepPartial<BankBalance>): BankBalance;
    fromPartial(object: DeepPartial<BankBalance>): BankBalance;
};
export declare const SubaccountDeposits: {
    encode(message: SubaccountDeposits, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDeposits;
    fromJSON(object: any): SubaccountDeposits;
    toJSON(message: SubaccountDeposits): unknown;
    create(base?: DeepPartial<SubaccountDeposits>): SubaccountDeposits;
    fromPartial(object: DeepPartial<SubaccountDeposits>): SubaccountDeposits;
};
export declare const SubaccountDeposit: {
    encode(message: SubaccountDeposit, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDeposit;
    fromJSON(object: any): SubaccountDeposit;
    toJSON(message: SubaccountDeposit): unknown;
    create(base?: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
    fromPartial(object: DeepPartial<SubaccountDeposit>): SubaccountDeposit;
};
export declare const SpotOrderUpdate: {
    encode(message: SpotOrderUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotOrderUpdate;
    fromJSON(object: any): SpotOrderUpdate;
    toJSON(message: SpotOrderUpdate): unknown;
    create(base?: DeepPartial<SpotOrderUpdate>): SpotOrderUpdate;
    fromPartial(object: DeepPartial<SpotOrderUpdate>): SpotOrderUpdate;
};
export declare const SpotOrder: {
    encode(message: SpotOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotOrder;
    fromJSON(object: any): SpotOrder;
    toJSON(message: SpotOrder): unknown;
    create(base?: DeepPartial<SpotOrder>): SpotOrder;
    fromPartial(object: DeepPartial<SpotOrder>): SpotOrder;
};
export declare const DerivativeOrderUpdate: {
    encode(message: DerivativeOrderUpdate, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeOrderUpdate;
    fromJSON(object: any): DerivativeOrderUpdate;
    toJSON(message: DerivativeOrderUpdate): unknown;
    create(base?: DeepPartial<DerivativeOrderUpdate>): DerivativeOrderUpdate;
    fromPartial(object: DeepPartial<DerivativeOrderUpdate>): DerivativeOrderUpdate;
};
export declare const DerivativeOrder: {
    encode(message: DerivativeOrder, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeOrder;
    fromJSON(object: any): DerivativeOrder;
    toJSON(message: DerivativeOrder): unknown;
    create(base?: DeepPartial<DerivativeOrder>): DerivativeOrder;
    fromPartial(object: DeepPartial<DerivativeOrder>): DerivativeOrder;
};
export declare const Position: {
    encode(message: Position, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Position;
    fromJSON(object: any): Position;
    toJSON(message: Position): unknown;
    create(base?: DeepPartial<Position>): Position;
    fromPartial(object: DeepPartial<Position>): Position;
};
export declare const OraclePrice: {
    encode(message: OraclePrice, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OraclePrice;
    fromJSON(object: any): OraclePrice;
    toJSON(message: OraclePrice): unknown;
    create(base?: DeepPartial<OraclePrice>): OraclePrice;
    fromPartial(object: DeepPartial<OraclePrice>): OraclePrice;
};
export declare const SpotTrade: {
    encode(message: SpotTrade, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SpotTrade;
    fromJSON(object: any): SpotTrade;
    toJSON(message: SpotTrade): unknown;
    create(base?: DeepPartial<SpotTrade>): SpotTrade;
    fromPartial(object: DeepPartial<SpotTrade>): SpotTrade;
};
export declare const DerivativeTrade: {
    encode(message: DerivativeTrade, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): DerivativeTrade;
    fromJSON(object: any): DerivativeTrade;
    toJSON(message: DerivativeTrade): unknown;
    create(base?: DeepPartial<DerivativeTrade>): DerivativeTrade;
    fromPartial(object: DeepPartial<DerivativeTrade>): DerivativeTrade;
};
export declare const TradesFilter: {
    encode(message: TradesFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): TradesFilter;
    fromJSON(object: any): TradesFilter;
    toJSON(message: TradesFilter): unknown;
    create(base?: DeepPartial<TradesFilter>): TradesFilter;
    fromPartial(object: DeepPartial<TradesFilter>): TradesFilter;
};
export declare const PositionsFilter: {
    encode(message: PositionsFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PositionsFilter;
    fromJSON(object: any): PositionsFilter;
    toJSON(message: PositionsFilter): unknown;
    create(base?: DeepPartial<PositionsFilter>): PositionsFilter;
    fromPartial(object: DeepPartial<PositionsFilter>): PositionsFilter;
};
export declare const OrdersFilter: {
    encode(message: OrdersFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrdersFilter;
    fromJSON(object: any): OrdersFilter;
    toJSON(message: OrdersFilter): unknown;
    create(base?: DeepPartial<OrdersFilter>): OrdersFilter;
    fromPartial(object: DeepPartial<OrdersFilter>): OrdersFilter;
};
export declare const OrderbookFilter: {
    encode(message: OrderbookFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OrderbookFilter;
    fromJSON(object: any): OrderbookFilter;
    toJSON(message: OrderbookFilter): unknown;
    create(base?: DeepPartial<OrderbookFilter>): OrderbookFilter;
    fromPartial(object: DeepPartial<OrderbookFilter>): OrderbookFilter;
};
export declare const BankBalancesFilter: {
    encode(message: BankBalancesFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BankBalancesFilter;
    fromJSON(object: any): BankBalancesFilter;
    toJSON(message: BankBalancesFilter): unknown;
    create(base?: DeepPartial<BankBalancesFilter>): BankBalancesFilter;
    fromPartial(object: DeepPartial<BankBalancesFilter>): BankBalancesFilter;
};
export declare const SubaccountDepositsFilter: {
    encode(message: SubaccountDepositsFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): SubaccountDepositsFilter;
    fromJSON(object: any): SubaccountDepositsFilter;
    toJSON(message: SubaccountDepositsFilter): unknown;
    create(base?: DeepPartial<SubaccountDepositsFilter>): SubaccountDepositsFilter;
    fromPartial(object: DeepPartial<SubaccountDepositsFilter>): SubaccountDepositsFilter;
};
export declare const OraclePriceFilter: {
    encode(message: OraclePriceFilter, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OraclePriceFilter;
    fromJSON(object: any): OraclePriceFilter;
    toJSON(message: OraclePriceFilter): unknown;
    create(base?: DeepPartial<OraclePriceFilter>): OraclePriceFilter;
    fromPartial(object: DeepPartial<OraclePriceFilter>): OraclePriceFilter;
};
/** ChainStream defines the gRPC streaming service. */
export interface Stream {
    Stream(request: DeepPartial<StreamRequest>, metadata?: grpc.Metadata): Observable<StreamResponse>;
}
export declare class StreamClientImpl implements Stream {
    private readonly rpc;
    constructor(rpc: Rpc);
    Stream(request: DeepPartial<StreamRequest>, metadata?: grpc.Metadata): Observable<StreamResponse>;
}
export declare const StreamDesc: {
    serviceName: string;
};
export declare const StreamStreamDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        streamingTransport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
    invoke<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Observable<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
