"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = exports.BlockResponse = exports.StatusResponse = exports.StatusRequest = exports.NoBlockResponse = exports.BlockRequest = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var block_1 = require("../../types/v2/block.js");
var types_1 = require("../../types/v2/types.js");
exports.protobufPackage = "cometbft.blocksync.v2";
function createBaseBlockRequest() {
    return { height: "0" };
}
exports.BlockRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBlockRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create: function (base) {
        return exports.BlockRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseBlockRequest();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseNoBlockResponse() {
    return { height: "0" };
}
exports.NoBlockResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseNoBlockResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { height: isSet(object.height) ? String(object.height) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        return obj;
    },
    create: function (base) {
        return exports.NoBlockResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseNoBlockResponse();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseStatusRequest() {
    return {};
}
exports.StatusRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStatusRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.StatusRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseStatusRequest();
        return message;
    },
};
function createBaseStatusResponse() {
    return { height: "0", base: "0" };
}
exports.StatusResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.height !== "0") {
            writer.uint32(8).int64(message.height);
        }
        if (message.base !== "0") {
            writer.uint32(16).int64(message.base);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStatusResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.height = longToString(reader.int64());
                    break;
                case 2:
                    message.base = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            height: isSet(object.height) ? String(object.height) : "0",
            base: isSet(object.base) ? String(object.base) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.height !== undefined && (obj.height = message.height);
        message.base !== undefined && (obj.base = message.base);
        return obj;
    },
    create: function (base) {
        return exports.StatusResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseStatusResponse();
        message.height = (_a = object.height) !== null && _a !== void 0 ? _a : "0";
        message.base = (_b = object.base) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseBlockResponse() {
    return { block: undefined, extCommit: undefined };
}
exports.BlockResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.block !== undefined) {
            block_1.Block.encode(message.block, writer.uint32(10).fork()).ldelim();
        }
        if (message.extCommit !== undefined) {
            types_1.ExtendedCommit.encode(message.extCommit, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBlockResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.block = block_1.Block.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.extCommit = types_1.ExtendedCommit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            block: isSet(object.block) ? block_1.Block.fromJSON(object.block) : undefined,
            extCommit: isSet(object.extCommit) ? types_1.ExtendedCommit.fromJSON(object.extCommit) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.block !== undefined && (obj.block = message.block ? block_1.Block.toJSON(message.block) : undefined);
        message.extCommit !== undefined &&
            (obj.extCommit = message.extCommit ? types_1.ExtendedCommit.toJSON(message.extCommit) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.BlockResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseBlockResponse();
        message.block = (object.block !== undefined && object.block !== null) ? block_1.Block.fromPartial(object.block) : undefined;
        message.extCommit = (object.extCommit !== undefined && object.extCommit !== null)
            ? types_1.ExtendedCommit.fromPartial(object.extCommit)
            : undefined;
        return message;
    },
};
function createBaseMessage() {
    return {
        blockRequest: undefined,
        noBlockResponse: undefined,
        blockResponse: undefined,
        statusRequest: undefined,
        statusResponse: undefined,
    };
}
exports.Message = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.blockRequest !== undefined) {
            exports.BlockRequest.encode(message.blockRequest, writer.uint32(10).fork()).ldelim();
        }
        if (message.noBlockResponse !== undefined) {
            exports.NoBlockResponse.encode(message.noBlockResponse, writer.uint32(18).fork()).ldelim();
        }
        if (message.blockResponse !== undefined) {
            exports.BlockResponse.encode(message.blockResponse, writer.uint32(26).fork()).ldelim();
        }
        if (message.statusRequest !== undefined) {
            exports.StatusRequest.encode(message.statusRequest, writer.uint32(34).fork()).ldelim();
        }
        if (message.statusResponse !== undefined) {
            exports.StatusResponse.encode(message.statusResponse, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMessage();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.blockRequest = exports.BlockRequest.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.noBlockResponse = exports.NoBlockResponse.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.blockResponse = exports.BlockResponse.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.statusRequest = exports.StatusRequest.decode(reader, reader.uint32());
                    break;
                case 5:
                    message.statusResponse = exports.StatusResponse.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            blockRequest: isSet(object.blockRequest) ? exports.BlockRequest.fromJSON(object.blockRequest) : undefined,
            noBlockResponse: isSet(object.noBlockResponse) ? exports.NoBlockResponse.fromJSON(object.noBlockResponse) : undefined,
            blockResponse: isSet(object.blockResponse) ? exports.BlockResponse.fromJSON(object.blockResponse) : undefined,
            statusRequest: isSet(object.statusRequest) ? exports.StatusRequest.fromJSON(object.statusRequest) : undefined,
            statusResponse: isSet(object.statusResponse) ? exports.StatusResponse.fromJSON(object.statusResponse) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.blockRequest !== undefined &&
            (obj.blockRequest = message.blockRequest ? exports.BlockRequest.toJSON(message.blockRequest) : undefined);
        message.noBlockResponse !== undefined &&
            (obj.noBlockResponse = message.noBlockResponse ? exports.NoBlockResponse.toJSON(message.noBlockResponse) : undefined);
        message.blockResponse !== undefined &&
            (obj.blockResponse = message.blockResponse ? exports.BlockResponse.toJSON(message.blockResponse) : undefined);
        message.statusRequest !== undefined &&
            (obj.statusRequest = message.statusRequest ? exports.StatusRequest.toJSON(message.statusRequest) : undefined);
        message.statusResponse !== undefined &&
            (obj.statusResponse = message.statusResponse ? exports.StatusResponse.toJSON(message.statusResponse) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Message.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseMessage();
        message.blockRequest = (object.blockRequest !== undefined && object.blockRequest !== null)
            ? exports.BlockRequest.fromPartial(object.blockRequest)
            : undefined;
        message.noBlockResponse = (object.noBlockResponse !== undefined && object.noBlockResponse !== null)
            ? exports.NoBlockResponse.fromPartial(object.noBlockResponse)
            : undefined;
        message.blockResponse = (object.blockResponse !== undefined && object.blockResponse !== null)
            ? exports.BlockResponse.fromPartial(object.blockResponse)
            : undefined;
        message.statusRequest = (object.statusRequest !== undefined && object.statusRequest !== null)
            ? exports.StatusRequest.fromPartial(object.statusRequest)
            : undefined;
        message.statusResponse = (object.statusResponse !== undefined && object.statusResponse !== null)
            ? exports.StatusResponse.fromPartial(object.statusResponse)
            : undefined;
        return message;
    },
};
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
