import{l as c,v as n}from"./chunk-VHNX2NUR.mjs";import{c as p}from"./chunk-UOP7GBXB.mjs";import{b as i}from"./chunk-CZYH3G7E.mjs";async function d(o){let{aptosConfig:s}=o,{data:e}=await i({aptosConfig:s,originMethod:"getLedgerInfo",path:""});return e}async function C(o){let{aptosConfig:s,limit:e}=o;return(await a({aptosConfig:s,query:{query:c,variables:{limit:e}},originMethod:"getChainTopUserTransactions"})).user_transactions}async function a(o){let{aptosConfig:s,query:e,originMethod:t}=o,{data:r}=await p({aptosConfig:s,originMethod:t??"queryIndexer",path:"",body:e,overrides:{WITH_CREDENTIALS:!1}});return r}async function u(o){let{aptosConfig:s}=o;return(await a({aptosConfig:s,query:{query:n},originMethod:"getProcessorStatuses"})).processor_status}async function h(o){let s=await u({aptosConfig:o.aptosConfig});return BigInt(s[0].last_success_version)}async function T(o){let{aptosConfig:s,processorType:e}=o;return(await a({aptosConfig:s,query:{query:n,variables:{where_condition:{processor:{_eq:e}}}},originMethod:"getProcessorStatus"})).processor_status[0]}export{d as a,C as b,a as c,u as d,h as e,T as f};
//# sourceMappingURL=chunk-GBNAG7KK.mjs.map