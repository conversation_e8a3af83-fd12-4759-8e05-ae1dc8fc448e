import { MsgBase } from '../../MsgBase.js';
import { CosmosBaseV1Beta1Coin, InjectiveTokenFactoryV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgMint {
    interface Params {
        sender: string;
        receiver?: string;
        amount: {
            amount: string;
            denom: string;
        };
    }
    type Proto = InjectiveTokenFactoryV1Beta1Tx.MsgMint;
}
/**
 * @category Messages
 */
export default class MsgMint extends MsgBase<MsgMint.Params, MsgMint.Proto> {
    static fromJSON(params: MsgMint.Params): MsgMint;
    toProto(): InjectiveTokenFactoryV1Beta1Tx.MsgMint;
    toData(): {
        sender: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        receiver: string;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            amount: CosmosBaseV1Beta1Coin.Coin | undefined;
            receiver: string;
        };
    };
    toWeb3Gw(): {
        sender: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        receiver: string;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveTokenFactoryV1Beta1Tx.MsgMint;
    };
    toBinary(): Uint8Array;
}
