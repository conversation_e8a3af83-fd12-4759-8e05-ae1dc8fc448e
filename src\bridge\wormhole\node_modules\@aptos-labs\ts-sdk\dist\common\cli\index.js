"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; } function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } } function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }var _chunkZMDE3DNLjs = require('../chunk-ZMDE3DNL.js');var _child_process = require('child_process');var _treekill = require('tree-kill'); var _treekill2 = _interopRequireDefault(_treekill);var _os = require('os');var u=class{constructor(s){this.MAXIMUM_WAIT_TIME_SEC=75;this.READINESS_ENDPOINT="http://127.0.0.1:8070/";this.showStdout=!0;this.process=null;this.showStdout=_nullishCoalesce(_optionalChain([s, 'optionalAccess', _ => _.showStdout]), () => (!0))}async stop(){await new Promise((s,e)=>{_optionalChain([this, 'access', _2 => _2.process, 'optionalAccess', _3 => _3.pid])&&_treekill2.default.call(void 0, this.process.pid,r=>{r?e(r):s(!0)})})}async run(){await this.checkIfProcessIsUp()||(this.start(),await this.waitUntilProcessIsUp())}start(){let s="npx",e=["aptos","node","run-localnet","--force-restart","--assume-yes","--with-indexer-api"],r=_os.platform.call(void 0, ),o={env:{...process.env,ENABLE_KEYLESS_DEFAULT:"1"},...r==="win32"&&{shell:!0}};this.process=_child_process.spawn.call(void 0, s,e,o),_optionalChain([this, 'access', _4 => _4.process, 'access', _5 => _5.stdout, 'optionalAccess', _6 => _6.on, 'call', _7 => _7("data",a=>{let t=a.toString();this.showStdout&&console.log(t)})])}async waitUntilProcessIsUp(){let s=await this.checkIfProcessIsUp(),e=Date.now()/1e3,r=e;for(;!s&&e+this.MAXIMUM_WAIT_TIME_SEC>r;)await _chunkZMDE3DNLjs.aa.call(void 0, 1e3),s=await this.checkIfProcessIsUp(),r=Date.now()/1e3;if(!s)throw new Error("Process failed to start");return!0}async checkIfProcessIsUp(){try{return(await fetch(this.READINESS_ENDPOINT)).status===200}catch (e2){return!1}}};var l=class{async init(s){let{network:e,profile:r,extraArguments:o,showStdout:a}=s,t=["aptos","init",`--network=${_nullishCoalesce(e, () => ("local"))}`,`--profile=${_nullishCoalesce(r, () => ("default"))}`];return o&&t.push(...o),this.runCommand(t,a)}async compile(s){let{packageDirectoryPath:e,namedAddresses:r,extraArguments:o,showStdout:a}=s,t=["aptos","move","compile","--package-dir",e],n=this.parseNamedAddresses(r);return t.push(...this.prepareNamedAddresses(n)),o&&t.push(...o),this.runCommand(t,a)}async test(s){let{packageDirectoryPath:e,namedAddresses:r,extraArguments:o,showStdout:a}=s,t=["aptos","move","test","--package-dir",e],n=this.parseNamedAddresses(r);return t.push(...this.prepareNamedAddresses(n)),o&&t.push(...o),this.runCommand(t,a)}async publish(s){let{packageDirectoryPath:e,namedAddresses:r,profile:o,extraArguments:a,showStdout:t}=s,n=["aptos","move","publish","--package-dir",e,`--profile=${_nullishCoalesce(o, () => ("default"))}`],i=this.parseNamedAddresses(r);return n.push(...this.prepareNamedAddresses(i)),a&&n.push(...a),this.runCommand(n,t)}async createObjectAndPublishPackage(s){let{packageDirectoryPath:e,addressName:r,namedAddresses:o,profile:a,extraArguments:t,showStdout:n}=s,i=["aptos","move","create-object-and-publish-package","--package-dir",e,"--address-name",r,`--profile=${_nullishCoalesce(a, () => ("default"))}`],d=this.parseNamedAddresses(o);i.push(...this.prepareNamedAddresses(d)),t&&i.push(...t);let{output:c}=await this.runCommand(i,n);return{objectAddress:this.extractAddressFromOutput(c)}}async upgradeObjectPackage(s){let{packageDirectoryPath:e,objectAddress:r,namedAddresses:o,profile:a,extraArguments:t,showStdout:n}=s,i=["aptos","move","upgrade-object-package","--package-dir",e,"--object-address",r,`--profile=${_nullishCoalesce(a, () => ("default"))}`],d=this.parseNamedAddresses(o);return i.push(...this.prepareNamedAddresses(d)),t&&i.push(...t),this.runCommand(i,n)}async buildPublishPayload(s){let{outputFile:e,packageDirectoryPath:r,namedAddresses:o,extraArguments:a,showStdout:t}=s,n=["aptos","move","build-publish-payload","--json-output-file",e,"--package-dir",r],i=this.parseNamedAddresses(o);return n.push(...this.prepareNamedAddresses(i)),a&&n.push(...a),this.runCommand(n,t)}async runScript(s){let{compiledScriptPath:e,profile:r,extraArguments:o,showStdout:a}=s,t=["aptos","move","run-script","--compiled-script-path",e,`--profile=${_nullishCoalesce(r, () => ("default"))}`];return o&&t.push(...o),this.runCommand(t,a)}async gasProfile(s){let{network:e,transactionId:r,extraArguments:o,showStdout:a}=s,t=["aptos","move","replay","--profile-gas","--network",e,"--txn-id",r];return o&&t.push(...o),this.runCommand(t,a)}async runCommand(s,e=!0){return new Promise((r,o)=>{let a=_os.platform.call(void 0, ),t,n="",i="";a==="win32"?t=_child_process.spawn.call(void 0, "npx",s,{shell:!0}):t=_child_process.spawn.call(void 0, "npx",s),t.stdout.on("data",d=>{i=d.toString(),n+=d.toString()}),e&&(t.stdout.pipe(process.stdout),t.stderr.pipe(process.stderr)),process.stdin.pipe(t.stdin),t.on("close",d=>{if(d===0)try{let c=JSON.parse(i);c.Error?o(new Error(`Error: ${c.Error}`)):c.Result&&r({result:c.Result,output:n})}catch (e3){r({output:n})}else o(new Error(`Child process exited with code ${d}`))})})}prepareNamedAddresses(s){let e=s.size,r=[];if(e===0)return r;r.push("--named-addresses");let o=[];return s.forEach((a,t)=>{let n=`${t}=${a.toString()}`;o.push(n)}),r.push(o.join(",")),r}parseNamedAddresses(s){let e=new Map;return Object.keys(s).forEach(r=>{let o=s[r];e.set(r,o)}),e}extractAddressFromOutput(s){let e=s.match("Code was successfully deployed to object address (0x[0-9a-fA-F]+)");if(e)return e[1];throw new Error("Failed to extract object address from output")}};exports.LocalNode = u; exports.Move = l;
//# sourceMappingURL=index.js.map