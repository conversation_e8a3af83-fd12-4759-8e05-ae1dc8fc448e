"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrustWalletException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
const maps_js_1 = require("../utils/maps.js");
const removeTrustWalletFromErrorString = (message) => message
    .replaceAll('TrustWallet', '')
    .replaceAll('Trust Wallet', '')
    .replaceAll('Trustwallet', '')
    .replaceAll('TrustWallet:', '')
    .replaceAll('Trust Wallet:', '');
class TrustWalletException extends base_js_1.ConcreteException {
    static errorClass = 'TrustWalletException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage((0, maps_js_1.mapMetamaskMessage)(removeTrustWalletFromErrorString(message)));
        this.setName(TrustWalletException.errorClass);
    }
}
exports.TrustWalletException = TrustWalletException;
