"use strict";
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreKeyConfig = exports.Module = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "cosmos.app.runtime.v1alpha1";
function createBaseModule() {
    return {
        appName: "",
        beginBlockers: [],
        endBlockers: [],
        initGenesis: [],
        exportGenesis: [],
        overrideStoreKeys: [],
        orderMigrations: [],
        precommiters: [],
        prepareCheckStaters: [],
    };
}
exports.Module = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b, e_3, _c, e_4, _d, e_5, _e, e_6, _f, e_7, _g, e_8, _h;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.appName !== "") {
            writer.uint32(10).string(message.appName);
        }
        try {
            for (var _j = __values(message.beginBlockers), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_a = _j.return)) _a.call(_j);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _l = __values(message.endBlockers), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_b = _l.return)) _b.call(_l);
            }
            finally { if (e_2) throw e_2.error; }
        }
        try {
            for (var _o = __values(message.initGenesis), _p = _o.next(); !_p.done; _p = _o.next()) {
                var v = _p.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_p && !_p.done && (_c = _o.return)) _c.call(_o);
            }
            finally { if (e_3) throw e_3.error; }
        }
        try {
            for (var _q = __values(message.exportGenesis), _r = _q.next(); !_r.done; _r = _q.next()) {
                var v = _r.value;
                writer.uint32(42).string(v);
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_r && !_r.done && (_d = _q.return)) _d.call(_q);
            }
            finally { if (e_4) throw e_4.error; }
        }
        try {
            for (var _s = __values(message.overrideStoreKeys), _t = _s.next(); !_t.done; _t = _s.next()) {
                var v = _t.value;
                exports.StoreKeyConfig.encode(v, writer.uint32(50).fork()).ldelim();
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_t && !_t.done && (_e = _s.return)) _e.call(_s);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _u = __values(message.orderMigrations), _v = _u.next(); !_v.done; _v = _u.next()) {
                var v = _v.value;
                writer.uint32(58).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_v && !_v.done && (_f = _u.return)) _f.call(_u);
            }
            finally { if (e_6) throw e_6.error; }
        }
        try {
            for (var _w = __values(message.precommiters), _x = _w.next(); !_x.done; _x = _w.next()) {
                var v = _x.value;
                writer.uint32(66).string(v);
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_x && !_x.done && (_g = _w.return)) _g.call(_w);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _y = __values(message.prepareCheckStaters), _z = _y.next(); !_z.done; _z = _y.next()) {
                var v = _z.value;
                writer.uint32(74).string(v);
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_z && !_z.done && (_h = _y.return)) _h.call(_y);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseModule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.appName = reader.string();
                    break;
                case 2:
                    message.beginBlockers.push(reader.string());
                    break;
                case 3:
                    message.endBlockers.push(reader.string());
                    break;
                case 4:
                    message.initGenesis.push(reader.string());
                    break;
                case 5:
                    message.exportGenesis.push(reader.string());
                    break;
                case 6:
                    message.overrideStoreKeys.push(exports.StoreKeyConfig.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.orderMigrations.push(reader.string());
                    break;
                case 8:
                    message.precommiters.push(reader.string());
                    break;
                case 9:
                    message.prepareCheckStaters.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            appName: isSet(object.appName) ? String(object.appName) : "",
            beginBlockers: Array.isArray(object === null || object === void 0 ? void 0 : object.beginBlockers) ? object.beginBlockers.map(function (e) { return String(e); }) : [],
            endBlockers: Array.isArray(object === null || object === void 0 ? void 0 : object.endBlockers) ? object.endBlockers.map(function (e) { return String(e); }) : [],
            initGenesis: Array.isArray(object === null || object === void 0 ? void 0 : object.initGenesis) ? object.initGenesis.map(function (e) { return String(e); }) : [],
            exportGenesis: Array.isArray(object === null || object === void 0 ? void 0 : object.exportGenesis) ? object.exportGenesis.map(function (e) { return String(e); }) : [],
            overrideStoreKeys: Array.isArray(object === null || object === void 0 ? void 0 : object.overrideStoreKeys)
                ? object.overrideStoreKeys.map(function (e) { return exports.StoreKeyConfig.fromJSON(e); })
                : [],
            orderMigrations: Array.isArray(object === null || object === void 0 ? void 0 : object.orderMigrations) ? object.orderMigrations.map(function (e) { return String(e); }) : [],
            precommiters: Array.isArray(object === null || object === void 0 ? void 0 : object.precommiters) ? object.precommiters.map(function (e) { return String(e); }) : [],
            prepareCheckStaters: Array.isArray(object === null || object === void 0 ? void 0 : object.prepareCheckStaters)
                ? object.prepareCheckStaters.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.appName !== undefined && (obj.appName = message.appName);
        if (message.beginBlockers) {
            obj.beginBlockers = message.beginBlockers.map(function (e) { return e; });
        }
        else {
            obj.beginBlockers = [];
        }
        if (message.endBlockers) {
            obj.endBlockers = message.endBlockers.map(function (e) { return e; });
        }
        else {
            obj.endBlockers = [];
        }
        if (message.initGenesis) {
            obj.initGenesis = message.initGenesis.map(function (e) { return e; });
        }
        else {
            obj.initGenesis = [];
        }
        if (message.exportGenesis) {
            obj.exportGenesis = message.exportGenesis.map(function (e) { return e; });
        }
        else {
            obj.exportGenesis = [];
        }
        if (message.overrideStoreKeys) {
            obj.overrideStoreKeys = message.overrideStoreKeys.map(function (e) { return e ? exports.StoreKeyConfig.toJSON(e) : undefined; });
        }
        else {
            obj.overrideStoreKeys = [];
        }
        if (message.orderMigrations) {
            obj.orderMigrations = message.orderMigrations.map(function (e) { return e; });
        }
        else {
            obj.orderMigrations = [];
        }
        if (message.precommiters) {
            obj.precommiters = message.precommiters.map(function (e) { return e; });
        }
        else {
            obj.precommiters = [];
        }
        if (message.prepareCheckStaters) {
            obj.prepareCheckStaters = message.prepareCheckStaters.map(function (e) { return e; });
        }
        else {
            obj.prepareCheckStaters = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.Module.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        var message = createBaseModule();
        message.appName = (_a = object.appName) !== null && _a !== void 0 ? _a : "";
        message.beginBlockers = ((_b = object.beginBlockers) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.endBlockers = ((_c = object.endBlockers) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.initGenesis = ((_d = object.initGenesis) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        message.exportGenesis = ((_e = object.exportGenesis) === null || _e === void 0 ? void 0 : _e.map(function (e) { return e; })) || [];
        message.overrideStoreKeys = ((_f = object.overrideStoreKeys) === null || _f === void 0 ? void 0 : _f.map(function (e) { return exports.StoreKeyConfig.fromPartial(e); })) || [];
        message.orderMigrations = ((_g = object.orderMigrations) === null || _g === void 0 ? void 0 : _g.map(function (e) { return e; })) || [];
        message.precommiters = ((_h = object.precommiters) === null || _h === void 0 ? void 0 : _h.map(function (e) { return e; })) || [];
        message.prepareCheckStaters = ((_j = object.prepareCheckStaters) === null || _j === void 0 ? void 0 : _j.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseStoreKeyConfig() {
    return { moduleName: "", kvStoreKey: "" };
}
exports.StoreKeyConfig = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.moduleName !== "") {
            writer.uint32(10).string(message.moduleName);
        }
        if (message.kvStoreKey !== "") {
            writer.uint32(18).string(message.kvStoreKey);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseStoreKeyConfig();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.moduleName = reader.string();
                    break;
                case 2:
                    message.kvStoreKey = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            moduleName: isSet(object.moduleName) ? String(object.moduleName) : "",
            kvStoreKey: isSet(object.kvStoreKey) ? String(object.kvStoreKey) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.moduleName !== undefined && (obj.moduleName = message.moduleName);
        message.kvStoreKey !== undefined && (obj.kvStoreKey = message.kvStoreKey);
        return obj;
    },
    create: function (base) {
        return exports.StoreKeyConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseStoreKeyConfig();
        message.moduleName = (_a = object.moduleName) !== null && _a !== void 0 ? _a : "";
        message.kvStoreKey = (_b = object.kvStoreKey) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
