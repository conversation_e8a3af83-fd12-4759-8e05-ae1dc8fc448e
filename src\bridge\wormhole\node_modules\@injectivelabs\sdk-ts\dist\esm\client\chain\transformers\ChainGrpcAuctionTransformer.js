import { ChainGrpcCommonTransformer } from './ChainGrpcCommonTransformer.js';
/**
 * @category Chain Grpc Transformer
 */
export class ChainGrpcAuctionTransformer {
    static grpcBidToBid(grpcBid) {
        return {
            bidder: grpcBid.bidder,
            amount: grpcBid.amount
                ? ChainGrpcCommonTransformer.grpcCoinToCoin(grpcBid.amount)
                : undefined,
        };
    }
    static grpcLastAuctionResultToLastAuctionResult(grpcLastAuctionResult) {
        return {
            winner: grpcLastAuctionResult.winner,
            amount: grpcLastAuctionResult.amount
                ? ChainGrpcCommonTransformer.grpcCoinToCoin(grpcLastAuctionResult.amount)
                : undefined,
            round: grpcLastAuctionResult.round,
        };
    }
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            auctionPeriod: parseInt(params?.auctionPeriod || '0', 10),
            minNextBidIncrementRate: params?.minNextBidIncrementRate || '0',
        };
    }
    static currentBasketResponseToCurrentBasket(response) {
        return {
            amountList: response.amount.map(ChainGrpcCommonTransformer.grpcCoinToCoin),
            auctionRound: parseInt(response.auctionRound, 10),
            auctionClosingTime: parseInt(response.auctionClosingTime, 10),
            highestBidder: response.highestBidAmount,
            highestBidAmount: response.highestBidAmount,
        };
    }
    static auctionModuleStateResponseToAuctionModuleState(response) {
        const state = response.state;
        const params = state.params;
        return {
            params: {
                auctionPeriod: parseInt(params.auctionPeriod, 10),
                minNextBidIncrementRate: params.minNextBidIncrementRate,
            },
            auctionRound: parseInt(state.auctionRound, 10),
            highestBid: state.highestBid
                ? ChainGrpcAuctionTransformer.grpcBidToBid(state.highestBid)
                : undefined,
            auctionEndingTimestamp: parseInt(state.auctionEndingTimestamp, 10),
            lastAuctionResult: state.lastAuctionResult
                ? ChainGrpcAuctionTransformer.grpcLastAuctionResultToLastAuctionResult(state.lastAuctionResult)
                : undefined,
        };
    }
    static LastAuctionResultResponseToLastAuctionResult(response) {
        if (!response.lastAuctionResult) {
            return;
        }
        return ChainGrpcAuctionTransformer.grpcLastAuctionResultToLastAuctionResult(response.lastAuctionResult);
    }
}
