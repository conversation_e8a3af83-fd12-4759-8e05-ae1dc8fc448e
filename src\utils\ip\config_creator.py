#!/usr/bin/env python3
"""
从订阅地址创建IP配置文件
"""

import yaml
import requests
import re
from pathlib import Path

def fetch_subscription():
    """获取订阅内容"""
    url = "https://cdn.0be.xyz/api/userhome/rssusernodeext?subtype=0&subkey=060583b8bf8b4e34add50c086d56cb87&inServerType=-1&shownodesubinfo=1&usenodetype=0"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    
    return response.text

def clean_proxy_name(name):
    """清理代理名称，移除Unicode字符"""
    # 移除Unicode编码的emoji
    name = re.sub(r'\\U[0-9A-Fa-f]{8}', '', name)
    # 移除多余的空格
    name = re.sub(r'\s+', ' ', name).strip()
    return name

def parse_subscription_content(content):
    """解析订阅内容"""
    try:
        # 直接解析YAML内容
        config = yaml.safe_load(content)
        
        if 'proxies' not in config:
            print("❌ 配置中未找到代理列表")
            return []
        
        proxies = []
        excluded_keywords = ['过期时间', '剩余流量', '官方地址', '备用地址', '建议使用']
        
        for proxy in config['proxies']:
            name = proxy.get('name', '')
            
            # 跳过信息节点
            if any(keyword in name for keyword in excluded_keywords):
                continue
            
            # 清理名称
            clean_name = clean_proxy_name(name)
            if not clean_name:
                continue
            
            # 创建标准化的代理配置
            clean_proxy = {
                'name': clean_name,
                'type': proxy.get('type', 'ss'),
                'server': proxy.get('server', ''),
                'port': proxy.get('port', 0),
                'cipher': proxy.get('cipher', 'aes-128-gcm'),
                'password': proxy.get('password', ''),
                'udp': proxy.get('udp', True)
            }
            
            # 验证必要字段
            if clean_proxy['server'] and clean_proxy['port'] and clean_proxy['password']:
                proxies.append(clean_proxy)
        
        print(f"✅ 成功解析 {len(proxies)} 个代理节点")
        return proxies
        
    except Exception as e:
        print(f"❌ 解析订阅内容失败: {e}")
        return []

def create_config_file(proxies):
    """创建配置文件"""
    config = {
        '# IP代理节点配置': None,
        '# 订阅地址': 'https://cdn.0be.xyz/api/userhome/rssusernodeext?subtype=0&subkey=060583b8bf8b4e34add50c086d56cb87&inServerType=-1&shownodesubinfo=1&usenodetype=0',
        
        'proxy_config': {
            'mixed_port': 7890,
            'allow_lan': False,
            'bind_address': '*',
            'mode': 'rule',
            'log_level': 'info',
            'ipv6': False,
            'external_controller': '127.0.0.1:9090'
        },
        
        'dns': {
            'enable': True,
            'ipv6': False,
            'default_nameserver': ['*********', '************'],
            'enhanced_mode': 'fake-ip',
            'fake_ip_range': '**********/16',
            'use_hosts': True,
            'nameserver': ['https://doh.pub/dns-query', 'https://dns.alidns.com/dns-query'],
            'fallback': ['https://doh.dns.sb/dns-query', 'https://dns.cloudflare.com/dns-query', 'https://dns.twnic.tw/dns-query', 'tls://*******:853'],
            'fallback_filter': {
                'geoip': True,
                'ipcidr': ['240.0.0.0/4', '0.0.0.0/32']
            }
        },
        
        'rotation_config': {
            'interval': 60,
            'enabled': True,
            'preferred_regions': ["香港", "台湾", "日本", "新加坡", "美国", "韩国"],
            'excluded_nodes': [],
            'test_timeout': 10,
            'max_retries': 3
        },
        
        'proxies': proxies
    }
    
    return config

def save_config_file(config, filename='config/ip.yaml'):
    """保存配置文件"""
    try:
        # 确保目录存在
        Path(filename).parent.mkdir(parents=True, exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入注释
            f.write("# IP代理节点配置\n")
            f.write("# 订阅地址: https://cdn.0be.xyz/api/userhome/rssusernodeext?subtype=0&subkey=060583b8bf8b4e34add50c086d56cb87&inServerType=-1&shownodesubinfo=1&usenodetype=0\n\n")
            
            # 写入配置
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
        
        print(f"✅ 配置文件已保存到: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 正在从订阅地址获取代理节点...")
    
    try:
        # 获取订阅内容
        content = fetch_subscription()
        print(f"✅ 成功获取订阅内容，长度: {len(content)} 字符")
        
        # 解析代理节点
        proxies = parse_subscription_content(content)
        
        if not proxies:
            print("❌ 未能解析到任何代理节点")
            return False
        
        # 统计地区分布
        regions = {}
        for proxy in proxies:
            name = proxy['name']
            for region in ['香港', '台湾', '日本', '新加坡', '美国', '韩国', '澳门', '加拿大', '巴西', '西班牙', '阿根廷', '印度尼西亚', '越南', '泰国', '马来西亚', '菲律宾', '柬埔寨', '俄罗斯', '尼泊尔', '孟加拉', '哈萨克斯坦', '巴基斯坦', '瑞士', '英国', '德国', '法国', '瑞典', '罗马尼亚', '捷克', '立陶宛', '奥地利', '波兰', '土耳其', '阿联酋', '阿塞拜疆', '澳大利亚', '新西兰']:
                if region in name:
                    regions[region] = regions.get(region, 0) + 1
                    break
        
        print("\n📊 节点分布:")
        for region, count in regions.items():
            print(f"   {region}: {count}个")
        
        # 创建配置
        config = create_config_file(proxies)
        
        # 保存配置文件
        if save_config_file(config):
            print(f"\n🎉 IP配置文件创建成功！")
            print(f"   总节点数: {len(proxies)}")
            print(f"   配置文件: config/ip.yaml")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

if __name__ == '__main__':
    main()
