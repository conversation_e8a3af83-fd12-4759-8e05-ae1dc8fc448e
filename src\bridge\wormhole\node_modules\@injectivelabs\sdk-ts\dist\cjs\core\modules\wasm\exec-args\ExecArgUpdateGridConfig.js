"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ExecArgBase_js_1 = require("../ExecArgBase.js");
/**
 * @category Contract Exec Arguments
 */
class ExecArgUpdateGridConfig extends ExecArgBase_js_1.ExecArgBase {
    static fromJSON(params) {
        return new ExecArgUpdateGridConfig(params);
    }
    toData() {
        const { params } = this;
        return {
            maximum_order_value_deviation: params.maximumOrderValueDeviation,
            maximum_rebalance_retries: params.maximumRebalanceRetries,
            slippage: params.slippage,
        };
    }
    toExecData() {
        return (0, ExecArgBase_js_1.dataToExecData)('update_config', this.toData());
    }
}
exports.default = ExecArgUpdateGridConfig;
