import { Deserializer } from '../bcs/deserializer.mjs';
import { HexInput } from '../types/types.mjs';
import { MultiKeyAccount } from './MultiKeyAccount.mjs';
import { A as Account, o as SingleKeyAccount, b as Ed25519Account } from '../Ed25519Account-D9XrCLfE.mjs';
import { KeylessAccount } from './KeylessAccount.mjs';
import { FederatedKeylessAccount } from './FederatedKeylessAccount.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../core/accountAddress.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../api/aptosConfig.mjs';
import '../utils/const.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/privateKey.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import './AbstractKeylessAccount.mjs';
import './EphemeralKeyPair.mjs';
import '../core/crypto/ephemeral.mjs';
import '../federatedKeyless-DAYXjY2Y.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';

/**
 * Utility functions for working with accounts.
 */
declare namespace AccountUtils {
    function toBytes(account: Account): Uint8Array;
    function toHexStringWithoutPrefix(account: Account): string;
    function toHexString(account: Account): string;
    function deserialize(deserializer: Deserializer): Account;
    function keylessAccountFromHex(hex: HexInput): KeylessAccount;
    function federatedKeylessAccountFromHex(hex: HexInput): FederatedKeylessAccount;
    function multiKeyAccountFromHex(hex: HexInput): MultiKeyAccount;
    function singleKeyAccountFromHex(hex: HexInput): SingleKeyAccount;
    function ed25519AccountFromHex(hex: HexInput): Ed25519Account;
    function fromHex(hex: HexInput): Account;
    function fromBytes(bytes: Uint8Array): Account;
}

export { AccountUtils };
