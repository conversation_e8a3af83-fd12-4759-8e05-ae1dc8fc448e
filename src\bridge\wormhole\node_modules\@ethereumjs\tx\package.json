{"name": "@ethereumjs/tx", "version": "4.2.0", "description": "A simple module for creating, manipulating and signing Ethereum transactions", "keywords": ["ethereum", "transactions"], "homepage": "https://github.com/ethereumjs/ethereumjs-monorepo/tree/master/packages/tx#readme", "bugs": {"url": "https://github.com/ethereumjs/ethereumjs-monorepo/issues?q=is%3Aissue+label%3A%22package%3A+tx%22"}, "repository": {"type": "git", "url": "https://github.com/ethereumjs/ethereumjs-monorepo.git"}, "license": "MPL-2.0", "author": "mjbecze <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/axic", "additions": 27562, "contributions": 22, "deletions": 42613, "hireable": true}], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "../../config/cli/ts-build.sh", "clean": "../../config/cli/clean-package.sh", "coverage": "../../config/cli/coverage.sh", "docs:build": "typedoc --options typedoc.js", "examples": "ts-node ../../scripts/examples-runner.ts -- tx", "lint": "../../config/cli/lint.sh", "lint:diff": "../../config/cli/lint-diff.sh", "lint:fix": "../../config/cli/lint-fix.sh", "prepublishOnly": "../../config/cli/prepublish.sh", "tape": "tape -r ts-node/register", "test": "npm run test:node && npm run test:browser && npm run test:txTests", "test:browser": "karma start karma.conf.js", "test:node": "tape -r ts-node/register -- 'test/**/*.spec.ts'", "test:txTests": "tape -r ts-node/register ./test/transactionRunner.ts", "tsc": "../../config/cli/ts-compile.sh"}, "dependencies": {"@ethereumjs/common": "^3.2.0", "@ethereumjs/rlp": "^4.0.1", "@ethereumjs/util": "^8.1.0", "ethereum-cryptography": "^2.0.0"}, "devDependencies": {"@types/minimist": "^1.2.0", "@types/node-dir": "^0.0.34", "minimist": "^1.2.0", "node-dir": "^0.1.16", "testdouble": "^3.17.2"}, "engines": {"node": ">=14"}}