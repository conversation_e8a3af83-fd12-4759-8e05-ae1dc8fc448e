{"version": 3, "file": "block.d.ts", "sourceRoot": "", "sources": ["../../src/block.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAA;AAEvC,OAAO,EAGL,aAAa,EAKb,UAAU,EAWX,MAAM,kBAAkB,CAAA;AAKzB,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAA;AAEzC,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAA;AACjE,OAAO,KAAK,EACV,UAAU,EACV,SAAS,EACT,YAAY,EACZ,gBAAgB,EAGhB,SAAS,EACT,YAAY,EAGb,MAAM,YAAY,CAAA;AACnB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAA;AAChD,OAAO,KAAK,EAIV,gBAAgB,EACjB,MAAM,gBAAgB,CAAA;AACvB,OAAO,KAAK,EACV,SAAS,EACT,cAAc,EAGd,sBAAsB,EAEvB,MAAM,kBAAkB,CAAA;AAEzB;;GAEG;AACH,qBAAa,KAAK;IAChB,SAAgB,MAAM,EAAE,WAAW,CAAA;IACnC,SAAgB,YAAY,EAAE,gBAAgB,EAAE,CAAK;IACrD,SAAgB,YAAY,EAAE,WAAW,EAAE,CAAK;IAChD,SAAgB,WAAW,CAAC,EAAE,UAAU,EAAE,CAAA;IAC1C,SAAgB,QAAQ,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,CAAA;IACrD,SAAgB,MAAM,EAAE,MAAM,CAAA;IAC9B,SAAS,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,UAAU,KAAK,UAAU,CAAA;IAEzD;;;;OAIG;IACH,SAAgB,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAA;IAEhE,SAAS,CAAC,KAAK,EAAE;QACf,UAAU,CAAC,EAAE,UAAU,CAAA;QACvB,mBAAmB,CAAC,EAAE,UAAU,CAAA;QAChC,YAAY,CAAC,EAAE,UAAU,CAAA;KAC1B,CAAK;IAEN;;;;OAIG;WACiB,sBAAsB,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,IAAI;IAQ9E;;;;OAIG;WACiB,uBAAuB,CAAC,GAAG,EAAE,gBAAgB,EAAE,EAAE,SAAS,CAAC,EAAE,IAAI;IAQrF;;;;;OAKG;WACiB,mBAAmB,CAAC,QAAQ,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,IAAI;IAgB9F;;;;;OAKG;WACW,aAAa,CAAC,SAAS,GAAE,SAAc,EAAE,IAAI,CAAC,EAAE,YAAY;IAyD1E;;;;;OAKG;WACW,sBAAsB,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,YAAY;IAUhF;;;;;OAKG;WACW,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,YAAY;IAuHrE;;;;;;OAMG;WACW,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,EAAE,YAAY;IAIlF;;;;;;OAMG;IACH,OAAc,mBAAmB,aACrB,MAAM,GAAG,cAAc,YACvB,MAAM,GAAG,MAAM,QACnB,YAAY,oBAiDnB;IAED;;;;;OAKG;WACiB,oBAAoB,CACtC,OAAO,EAAE,gBAAgB,EACzB,IAAI,CAAC,EAAE,YAAY,GAClB,OAAO,CAAC,KAAK,CAAC;IAsGjB;;;;;OAKG;WACiB,qBAAqB,CACvC,OAAO,EAAE,iBAAiB,EAC1B,IAAI,CAAC,EAAE,YAAY,GAClB,OAAO,CAAC,KAAK,CAAC;IAKjB;;;OAGG;gBAED,MAAM,CAAC,EAAE,WAAW,EACpB,YAAY,GAAE,gBAAgB,EAAO,EACrC,YAAY,GAAE,WAAW,EAAO,EAChC,WAAW,CAAC,EAAE,UAAU,EAAE,EAC1B,IAAI,GAAE,YAAiB,EACvB,QAAQ,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,EACrC,gBAAgB,CAAC,EAAE,sBAAsB,GAAG,IAAI;IA4ElD;;OAEG;IACH,GAAG,IAAI,UAAU;IAyBjB;;OAEG;IACH,IAAI,IAAI,UAAU;IAIlB;;OAEG;IACH,SAAS,IAAI,OAAO;IAIpB;;OAEG;IACH,SAAS,IAAI,UAAU;IAIvB;;OAEG;IACG,SAAS,IAAI,OAAO,CAAC,UAAU,CAAC;IAItC;;;;OAIG;IACG,uBAAuB,IAAI,OAAO,CAAC,OAAO,CAAC;IAc3C,mBAAmB,CAAC,aAAa,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;IAqBvF;;;OAGG;IACH,+BAA+B,IAAI,MAAM,EAAE;IA8C3C;;;OAGG;IACH,oBAAoB,IAAI,OAAO;IAM/B;;;;;;;;;OASG;IACG,YAAY,CAAC,UAAU,GAAE,OAAe,EAAE,SAAS,GAAE,OAAc,GAAG,OAAO,CAAC,IAAI,CAAC;IAmDzF;;;;;OAKG;IACH,wBAAwB,CAAC,YAAY,EAAE,WAAW;IA4ClD;;;OAGG;IACH,gBAAgB,IAAI,OAAO;IAS3B;;;OAGG;IACG,sBAAsB,IAAI,OAAO,CAAC,OAAO,CAAC;IAqBhD;;;;;;;;OAQG;IACH,cAAc;IAmBd;;;;OAIG;IACH,yBAAyB,CAAC,WAAW,EAAE,KAAK,GAAG,MAAM;IAIrD;;;;;OAKG;IACH,gBAAgB,CAAC,WAAW,EAAE,KAAK;IAInC;;OAEG;IACH,MAAM,IAAI,SAAS;IAenB;;;;;OAKG;IACH,kBAAkB,IAAI,gBAAgB;IA4DtC;;OAEG;IACI,QAAQ;IAmBf;;;;;OAKG;IACH,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM;CAGhC"}