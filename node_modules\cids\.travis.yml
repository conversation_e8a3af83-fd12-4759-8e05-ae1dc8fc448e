language: node_js
cache: npm
stages:
  - check
  - test
  - cov

node_js:
  - '10'

os:
  - linux
  - osx
  - windows

script: npx nyc -s npm run test:node -- --bail
after_success: npx nyc report --reporter=text-lcov > coverage.lcov && npx codecov

jobs:
  include:
    - stage: check
      script:
        - npx aegir commitlint --travis
        - npx aegir dep-check
        - npm run lint

    - stage: test
      name: chrome
      addons:
        chrome: stable
      script: npx aegir test -t browser -t webworker

    - stage: test
      name: firefox
      addons:
        firefox: latest
      script: npx aegir test -t browser -t webworker -- --browsers FirefoxHeadless

notifications:
  email: false
