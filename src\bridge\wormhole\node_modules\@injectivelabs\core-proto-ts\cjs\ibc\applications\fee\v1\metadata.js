"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Metadata = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "ibc.applications.fee.v1";
function createBaseMetadata() {
    return { feeVersion: "", appVersion: "" };
}
exports.Metadata = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feeVersion !== "") {
            writer.uint32(10).string(message.feeVersion);
        }
        if (message.appVersion !== "") {
            writer.uint32(18).string(message.appVersion);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMetadata();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feeVersion = reader.string();
                    break;
                case 2:
                    message.appVersion = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feeVersion: isSet(object.feeVersion) ? String(object.feeVersion) : "",
            appVersion: isSet(object.appVersion) ? String(object.appVersion) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feeVersion !== undefined && (obj.feeVersion = message.feeVersion);
        message.appVersion !== undefined && (obj.appVersion = message.appVersion);
        return obj;
    },
    create: function (base) {
        return exports.Metadata.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMetadata();
        message.feeVersion = (_a = object.feeVersion) !== null && _a !== void 0 ? _a : "";
        message.appVersion = (_b = object.appVersion) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
