import { InjectivePermissionsV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcPermissionsApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectivePermissionsV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/permissions.js").PermissionsModuleParams>;
    fetchNamespaceDenoms(): Promise<string[]>;
    fetchNamespaces(): Promise<void[]>;
    fetchNamespace(denom: string): Promise<import("../types/permissions.js").PermissionNamespace | undefined>;
    fetchActorsByRole({ denom, role }: {
        denom: string;
        role: string;
    }): Promise<{
        roles: string;
    }[]>;
    fetchRolesByActor({ actor, denom }: {
        actor: string;
        denom: string;
    }): Promise<{
        roles: string;
    }[]>;
    fetchRoleManager({ denom, manager, }: {
        denom: string;
        manager: string;
    }): Promise<import("../types/permissions.js").PermissionRoleManager | undefined>;
    fetchRoleManagers(): Promise<import("../types/permissions.js").PermissionRoleManager[]>;
    fetchPolicyStatuses(): Promise<import("../types/permissions.js").PermissionPolicyStatus[]>;
    fetchPolicyManagerCapabilities(denom: string): Promise<import("../types/permissions.js").PermissionPolicyManagerCapability[]>;
    fetchVoucher({ denom, address }: {
        denom: string;
        address: string;
    }): Promise<import("@injectivelabs/ts-types").Coin | undefined>;
    fetchVouchers(denom: string): Promise<import("../types/permissions.js").PermissionAddressVoucher[]>;
    fetchModuleState(): Promise<{
        params: import("../types/permissions.js").PermissionsModuleParams;
        namespaces: import("../types/permissions.js").PermissionNamespace[];
        vouchers: import("../types/permissions.js").PermissionAddressVoucher[];
    } | undefined>;
}
