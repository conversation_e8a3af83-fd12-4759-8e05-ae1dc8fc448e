{"name": "web3-core-promievent", "version": "1.10.4", "description": "This package extends the EventEmitter with the Promise class to allow chaining as well as multiple final states of a function.", "repository": "https://github.com/ethereum/web3.js/tree/1.x/packages/web3-core-promievent", "license": "LGPL-3.0", "engines": {"node": ">=8.0.0"}, "main": "lib/index.js", "scripts": {"compile": "tsc -b tsconfig.json"}, "dependencies": {"eventemitter3": "4.0.4"}, "gitHead": "56d35a4a9ec59c2735085dce1a5eebb0bb44fbce"}