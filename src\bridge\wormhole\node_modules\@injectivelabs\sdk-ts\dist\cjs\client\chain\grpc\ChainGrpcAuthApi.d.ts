import { CosmosAuthV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcAuthApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: CosmosAuthV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/auth.js").AuthModuleParams>;
    fetchAccount(address: string): Promise<import("../types/auth.js").Account>;
    fetchAccounts(pagination?: PaginationOption): Promise<{
        pagination: import("../../../types/pagination.js").Pagination;
        accounts: import("../types/auth.js").Account[];
    }>;
}
