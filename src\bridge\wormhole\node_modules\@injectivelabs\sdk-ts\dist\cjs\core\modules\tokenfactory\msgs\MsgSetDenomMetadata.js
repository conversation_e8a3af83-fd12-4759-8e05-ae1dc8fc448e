"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgSetDenomMetadata extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgSetDenomMetadata(params);
    }
    toProto() {
        const { params } = this;
        const metadata = core_proto_ts_1.CosmosBankV1Beta1Bank.Metadata.create();
        metadata.description = params.metadata.description;
        metadata.denomUnits = params.metadata.denomUnits.map((value) => {
            const denomUnit = core_proto_ts_1.CosmosBankV1Beta1Bank.DenomUnit.create();
            denomUnit.denom = value.denom;
            denomUnit.exponent = value.exponent;
            denomUnit.aliases = value.aliases;
            return denomUnit;
        });
        metadata.base = params.metadata.base;
        metadata.display = params.metadata.display;
        metadata.name = params.metadata.name;
        metadata.symbol = params.metadata.symbol;
        metadata.uri = params.metadata.uri;
        metadata.uriHash = params.metadata.uriHash;
        metadata.decimals = params.metadata.decimals;
        const message = core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata.create();
        if (params.adminBurnDisabled !== undefined) {
            const adminBurnDisabled = core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata_AdminBurnDisabled.create();
            adminBurnDisabled.shouldDisable = params.adminBurnDisabled;
            message.adminBurnDisabled = adminBurnDisabled;
        }
        message.sender = params.sender;
        message.metadata = metadata;
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgSetDenomMetadata',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'injective/tokenfactory/set-denom-metadata',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.tokenfactory.v1beta1.MsgSetDenomMetadata',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.tokenfactory.v1beta1.MsgSetDenomMetadata',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveTokenFactoryV1Beta1Tx.MsgSetDenomMetadata.encode(this.toProto()).finish();
    }
}
exports.default = MsgSetDenomMetadata;
