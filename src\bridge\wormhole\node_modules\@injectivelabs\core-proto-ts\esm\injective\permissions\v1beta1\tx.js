/* eslint-disable */
import { grpc } from "@injectivelabs/grpc-web";
import { BrowserHeaders } from "browser-headers";
import _m0 from "protobufjs/minimal.js";
import { Params } from "./params.js";
import { Namespace, PolicyManagerCapability, PolicyStatus, Role, RoleActors, RoleManager } from "./permissions.js";
export const protobufPackage = "injective.permissions.v1beta1";
function createBaseMsgUpdateParams() {
    return { authority: "", params: undefined };
}
export const MsgUpdateParams = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.authority !== "") {
            writer.uint32(10).string(message.authority);
        }
        if (message.params !== undefined) {
            Params.encode(message.params, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateParams();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.authority = reader.string();
                    break;
                case 2:
                    message.params = Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            authority: isSet(object.authority) ? String(object.authority) : "",
            params: isSet(object.params) ? Params.fromJSON(object.params) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.authority !== undefined && (obj.authority = message.authority);
        message.params !== undefined && (obj.params = message.params ? Params.toJSON(message.params) : undefined);
        return obj;
    },
    create(base) {
        return MsgUpdateParams.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgUpdateParams();
        message.authority = object.authority ?? "";
        message.params = (object.params !== undefined && object.params !== null)
            ? Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseMsgUpdateParamsResponse() {
    return {};
}
export const MsgUpdateParamsResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateParamsResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return MsgUpdateParamsResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseMsgUpdateParamsResponse();
        return message;
    },
};
function createBaseMsgCreateNamespace() {
    return { sender: "", namespace: undefined };
}
export const MsgCreateNamespace = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.namespace !== undefined) {
            Namespace.encode(message.namespace, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgCreateNamespace();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.namespace = Namespace.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            namespace: isSet(object.namespace) ? Namespace.fromJSON(object.namespace) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.namespace !== undefined &&
            (obj.namespace = message.namespace ? Namespace.toJSON(message.namespace) : undefined);
        return obj;
    },
    create(base) {
        return MsgCreateNamespace.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgCreateNamespace();
        message.sender = object.sender ?? "";
        message.namespace = (object.namespace !== undefined && object.namespace !== null)
            ? Namespace.fromPartial(object.namespace)
            : undefined;
        return message;
    },
};
function createBaseMsgCreateNamespaceResponse() {
    return {};
}
export const MsgCreateNamespaceResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgCreateNamespaceResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return MsgCreateNamespaceResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseMsgCreateNamespaceResponse();
        return message;
    },
};
function createBaseMsgUpdateNamespace() {
    return {
        sender: "",
        denom: "",
        contractHook: undefined,
        rolePermissions: [],
        roleManagers: [],
        policyStatuses: [],
        policyManagerCapabilities: [],
    };
}
export const MsgUpdateNamespace = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.contractHook !== undefined) {
            MsgUpdateNamespace_SetContractHook.encode(message.contractHook, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.rolePermissions) {
            Role.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (const v of message.roleManagers) {
            RoleManager.encode(v, writer.uint32(42).fork()).ldelim();
        }
        for (const v of message.policyStatuses) {
            PolicyStatus.encode(v, writer.uint32(50).fork()).ldelim();
        }
        for (const v of message.policyManagerCapabilities) {
            PolicyManagerCapability.encode(v, writer.uint32(58).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateNamespace();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.contractHook = MsgUpdateNamespace_SetContractHook.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.rolePermissions.push(Role.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleManagers.push(RoleManager.decode(reader, reader.uint32()));
                    break;
                case 6:
                    message.policyStatuses.push(PolicyStatus.decode(reader, reader.uint32()));
                    break;
                case 7:
                    message.policyManagerCapabilities.push(PolicyManagerCapability.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            contractHook: isSet(object.contractHook)
                ? MsgUpdateNamespace_SetContractHook.fromJSON(object.contractHook)
                : undefined,
            rolePermissions: Array.isArray(object?.rolePermissions)
                ? object.rolePermissions.map((e) => Role.fromJSON(e))
                : [],
            roleManagers: Array.isArray(object?.roleManagers)
                ? object.roleManagers.map((e) => RoleManager.fromJSON(e))
                : [],
            policyStatuses: Array.isArray(object?.policyStatuses)
                ? object.policyStatuses.map((e) => PolicyStatus.fromJSON(e))
                : [],
            policyManagerCapabilities: Array.isArray(object?.policyManagerCapabilities)
                ? object.policyManagerCapabilities.map((e) => PolicyManagerCapability.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        message.contractHook !== undefined && (obj.contractHook = message.contractHook
            ? MsgUpdateNamespace_SetContractHook.toJSON(message.contractHook)
            : undefined);
        if (message.rolePermissions) {
            obj.rolePermissions = message.rolePermissions.map((e) => e ? Role.toJSON(e) : undefined);
        }
        else {
            obj.rolePermissions = [];
        }
        if (message.roleManagers) {
            obj.roleManagers = message.roleManagers.map((e) => e ? RoleManager.toJSON(e) : undefined);
        }
        else {
            obj.roleManagers = [];
        }
        if (message.policyStatuses) {
            obj.policyStatuses = message.policyStatuses.map((e) => e ? PolicyStatus.toJSON(e) : undefined);
        }
        else {
            obj.policyStatuses = [];
        }
        if (message.policyManagerCapabilities) {
            obj.policyManagerCapabilities = message.policyManagerCapabilities.map((e) => e ? PolicyManagerCapability.toJSON(e) : undefined);
        }
        else {
            obj.policyManagerCapabilities = [];
        }
        return obj;
    },
    create(base) {
        return MsgUpdateNamespace.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgUpdateNamespace();
        message.sender = object.sender ?? "";
        message.denom = object.denom ?? "";
        message.contractHook = (object.contractHook !== undefined && object.contractHook !== null)
            ? MsgUpdateNamespace_SetContractHook.fromPartial(object.contractHook)
            : undefined;
        message.rolePermissions = object.rolePermissions?.map((e) => Role.fromPartial(e)) || [];
        message.roleManagers = object.roleManagers?.map((e) => RoleManager.fromPartial(e)) || [];
        message.policyStatuses = object.policyStatuses?.map((e) => PolicyStatus.fromPartial(e)) || [];
        message.policyManagerCapabilities =
            object.policyManagerCapabilities?.map((e) => PolicyManagerCapability.fromPartial(e)) || [];
        return message;
    },
};
function createBaseMsgUpdateNamespace_SetContractHook() {
    return { newValue: "" };
}
export const MsgUpdateNamespace_SetContractHook = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.newValue !== "") {
            writer.uint32(10).string(message.newValue);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateNamespace_SetContractHook();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.newValue = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { newValue: isSet(object.newValue) ? String(object.newValue) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.newValue !== undefined && (obj.newValue = message.newValue);
        return obj;
    },
    create(base) {
        return MsgUpdateNamespace_SetContractHook.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgUpdateNamespace_SetContractHook();
        message.newValue = object.newValue ?? "";
        return message;
    },
};
function createBaseMsgUpdateNamespaceResponse() {
    return {};
}
export const MsgUpdateNamespaceResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateNamespaceResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return MsgUpdateNamespaceResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseMsgUpdateNamespaceResponse();
        return message;
    },
};
function createBaseMsgUpdateActorRoles() {
    return { sender: "", denom: "", roleActorsToAdd: [], roleActorsToRevoke: [] };
}
export const MsgUpdateActorRoles = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        for (const v of message.roleActorsToAdd) {
            RoleActors.encode(v, writer.uint32(26).fork()).ldelim();
        }
        for (const v of message.roleActorsToRevoke) {
            RoleActors.encode(v, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateActorRoles();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.roleActorsToAdd.push(RoleActors.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.roleActorsToRevoke.push(RoleActors.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            roleActorsToAdd: Array.isArray(object?.roleActorsToAdd)
                ? object.roleActorsToAdd.map((e) => RoleActors.fromJSON(e))
                : [],
            roleActorsToRevoke: Array.isArray(object?.roleActorsToRevoke)
                ? object.roleActorsToRevoke.map((e) => RoleActors.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        if (message.roleActorsToAdd) {
            obj.roleActorsToAdd = message.roleActorsToAdd.map((e) => e ? RoleActors.toJSON(e) : undefined);
        }
        else {
            obj.roleActorsToAdd = [];
        }
        if (message.roleActorsToRevoke) {
            obj.roleActorsToRevoke = message.roleActorsToRevoke.map((e) => e ? RoleActors.toJSON(e) : undefined);
        }
        else {
            obj.roleActorsToRevoke = [];
        }
        return obj;
    },
    create(base) {
        return MsgUpdateActorRoles.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgUpdateActorRoles();
        message.sender = object.sender ?? "";
        message.denom = object.denom ?? "";
        message.roleActorsToAdd = object.roleActorsToAdd?.map((e) => RoleActors.fromPartial(e)) || [];
        message.roleActorsToRevoke = object.roleActorsToRevoke?.map((e) => RoleActors.fromPartial(e)) || [];
        return message;
    },
};
function createBaseMsgUpdateActorRolesResponse() {
    return {};
}
export const MsgUpdateActorRolesResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgUpdateActorRolesResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return MsgUpdateActorRolesResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseMsgUpdateActorRolesResponse();
        return message;
    },
};
function createBaseMsgClaimVoucher() {
    return { sender: "", denom: "" };
}
export const MsgClaimVoucher = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.sender !== "") {
            writer.uint32(10).string(message.sender);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgClaimVoucher();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.sender = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            sender: isSet(object.sender) ? String(object.sender) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.sender !== undefined && (obj.sender = message.sender);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create(base) {
        return MsgClaimVoucher.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMsgClaimVoucher();
        message.sender = object.sender ?? "";
        message.denom = object.denom ?? "";
        return message;
    },
};
function createBaseMsgClaimVoucherResponse() {
    return {};
}
export const MsgClaimVoucherResponse = {
    encode(_, writer = _m0.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMsgClaimVoucherResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return MsgClaimVoucherResponse.fromPartial(base ?? {});
    },
    fromPartial(_) {
        const message = createBaseMsgClaimVoucherResponse();
        return message;
    },
};
export class MsgClientImpl {
    rpc;
    constructor(rpc) {
        this.rpc = rpc;
        this.UpdateParams = this.UpdateParams.bind(this);
        this.CreateNamespace = this.CreateNamespace.bind(this);
        this.UpdateNamespace = this.UpdateNamespace.bind(this);
        this.UpdateActorRoles = this.UpdateActorRoles.bind(this);
        this.ClaimVoucher = this.ClaimVoucher.bind(this);
    }
    UpdateParams(request, metadata) {
        return this.rpc.unary(MsgUpdateParamsDesc, MsgUpdateParams.fromPartial(request), metadata);
    }
    CreateNamespace(request, metadata) {
        return this.rpc.unary(MsgCreateNamespaceDesc, MsgCreateNamespace.fromPartial(request), metadata);
    }
    UpdateNamespace(request, metadata) {
        return this.rpc.unary(MsgUpdateNamespaceDesc, MsgUpdateNamespace.fromPartial(request), metadata);
    }
    UpdateActorRoles(request, metadata) {
        return this.rpc.unary(MsgUpdateActorRolesDesc, MsgUpdateActorRoles.fromPartial(request), metadata);
    }
    ClaimVoucher(request, metadata) {
        return this.rpc.unary(MsgClaimVoucherDesc, MsgClaimVoucher.fromPartial(request), metadata);
    }
}
export const MsgDesc = { serviceName: "injective.permissions.v1beta1.Msg" };
export const MsgUpdateParamsDesc = {
    methodName: "UpdateParams",
    service: MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return MsgUpdateParams.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = MsgUpdateParamsResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const MsgCreateNamespaceDesc = {
    methodName: "CreateNamespace",
    service: MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return MsgCreateNamespace.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = MsgCreateNamespaceResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const MsgUpdateNamespaceDesc = {
    methodName: "UpdateNamespace",
    service: MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return MsgUpdateNamespace.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = MsgUpdateNamespaceResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const MsgUpdateActorRolesDesc = {
    methodName: "UpdateActorRoles",
    service: MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return MsgUpdateActorRoles.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = MsgUpdateActorRolesResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export const MsgClaimVoucherDesc = {
    methodName: "ClaimVoucher",
    service: MsgDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return MsgClaimVoucher.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = MsgClaimVoucherResponse.decode(data);
            return {
                ...value,
                toObject() {
                    return value;
                },
            };
        },
    },
};
export class GrpcWebImpl {
    host;
    options;
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        const request = { ..._request, ...methodDesc.requestType };
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new BrowserHeaders({ ...this.options?.metadata.headersMap, ...metadata?.headersMap })
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function isSet(value) {
    return value !== null && value !== undefined;
}
export class GrpcWebError extends tsProtoGlobalThis.Error {
    code;
    metadata;
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
