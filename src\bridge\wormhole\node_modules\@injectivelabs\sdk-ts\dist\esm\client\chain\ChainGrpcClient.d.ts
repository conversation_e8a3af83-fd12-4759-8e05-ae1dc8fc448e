import { ChainGrpcAuctionApi } from './grpc/ChainGrpcAuctionApi.js';
import { ChainGrpcBankApi } from './grpc/ChainGrpcBankApi.js';
import { ChainGrpcAuthApi } from './grpc/ChainGrpcAuthApi.js';
import { ChainGrpcDistributionApi } from './grpc/ChainGrpcDistributionApi.js';
import { ChainGrpcExchangeApi } from './grpc/ChainGrpcExchangeApi.js';
import { ChainGrpcGovApi } from './grpc/ChainGrpcGovApi.js';
import { ChainGrpcIbcApi } from './grpc/ChainGrpcIbcApi.js';
import { ChainGrpcInsuranceFundApi } from './grpc/ChainGrpcInsuranceFundApi.js';
import { ChainGrpcMintApi } from './grpc/ChainGrpcMintApi.js';
import { ChainGrpcOracleApi } from './grpc/ChainGrpcOracleApi.js';
import { ChainGrpcPeggyApi } from './grpc/ChainGrpcPeggyApi.js';
import { ChainGrpcPermissionsApi } from './grpc/ChainGrpcPermissionsApi.js';
import { ChainGrpcStakingApi } from './grpc/ChainGrpcStakingApi.js';
import { ChainGrpcTokenFactoryApi } from './grpc/ChainGrpcTokenFactoryApi.js';
import { ChainGrpcWasmApi } from './grpc/ChainGrpcWasmApi.js';
import { ChainGrpcWasmXApi } from './grpc/ChainGrpcWasmXApi.js';
/**
 * @category Chain Grpc API
 * @hidden
 */
export declare class ChainGrpcClient {
    auction: ChainGrpcAuctionApi;
    auth: ChainGrpcAuthApi;
    bank: ChainGrpcBankApi;
    distribution: ChainGrpcDistributionApi;
    exchange: ChainGrpcExchangeApi;
    gov: ChainGrpcGovApi;
    ibc: ChainGrpcIbcApi;
    insuranceFund: ChainGrpcInsuranceFundApi;
    mint: ChainGrpcMintApi;
    oracle: ChainGrpcOracleApi;
    peggy: ChainGrpcPeggyApi;
    permissions: ChainGrpcPermissionsApi;
    staking: ChainGrpcStakingApi;
    tokenfactory: ChainGrpcTokenFactoryApi;
    wasm: ChainGrpcWasmApi;
    wasmX: ChainGrpcWasmXApi;
    constructor(endpoint: string);
}
