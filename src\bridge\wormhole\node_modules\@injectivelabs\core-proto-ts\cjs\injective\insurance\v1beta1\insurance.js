"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedemptionSchedule = exports.InsuranceFund = exports.Params = exports.protobufPackage = void 0;
/* eslint-disable */
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var duration_1 = require("../../../google/protobuf/duration.js");
var timestamp_1 = require("../../../google/protobuf/timestamp.js");
var oracle_1 = require("../../oracle/v1beta1/oracle.js");
exports.protobufPackage = "injective.insurance.v1beta1";
function createBaseParams() {
    return { defaultRedemptionNoticePeriodDuration: undefined };
}
exports.Params = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.defaultRedemptionNoticePeriodDuration !== undefined) {
            duration_1.Duration.encode(message.defaultRedemptionNoticePeriodDuration, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseParams();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.defaultRedemptionNoticePeriodDuration = duration_1.Duration.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            defaultRedemptionNoticePeriodDuration: isSet(object.defaultRedemptionNoticePeriodDuration)
                ? duration_1.Duration.fromJSON(object.defaultRedemptionNoticePeriodDuration)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.defaultRedemptionNoticePeriodDuration !== undefined &&
            (obj.defaultRedemptionNoticePeriodDuration = message.defaultRedemptionNoticePeriodDuration
                ? duration_1.Duration.toJSON(message.defaultRedemptionNoticePeriodDuration)
                : undefined);
        return obj;
    },
    create: function (base) {
        return exports.Params.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseParams();
        message.defaultRedemptionNoticePeriodDuration =
            (object.defaultRedemptionNoticePeriodDuration !== undefined &&
                object.defaultRedemptionNoticePeriodDuration !== null)
                ? duration_1.Duration.fromPartial(object.defaultRedemptionNoticePeriodDuration)
                : undefined;
        return message;
    },
};
function createBaseInsuranceFund() {
    return {
        depositDenom: "",
        insurancePoolTokenDenom: "",
        redemptionNoticePeriodDuration: undefined,
        balance: "",
        totalShare: "",
        marketId: "",
        marketTicker: "",
        oracleBase: "",
        oracleQuote: "",
        oracleType: 0,
        expiry: "0",
    };
}
exports.InsuranceFund = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.depositDenom !== "") {
            writer.uint32(10).string(message.depositDenom);
        }
        if (message.insurancePoolTokenDenom !== "") {
            writer.uint32(18).string(message.insurancePoolTokenDenom);
        }
        if (message.redemptionNoticePeriodDuration !== undefined) {
            duration_1.Duration.encode(message.redemptionNoticePeriodDuration, writer.uint32(26).fork()).ldelim();
        }
        if (message.balance !== "") {
            writer.uint32(34).string(message.balance);
        }
        if (message.totalShare !== "") {
            writer.uint32(42).string(message.totalShare);
        }
        if (message.marketId !== "") {
            writer.uint32(50).string(message.marketId);
        }
        if (message.marketTicker !== "") {
            writer.uint32(58).string(message.marketTicker);
        }
        if (message.oracleBase !== "") {
            writer.uint32(66).string(message.oracleBase);
        }
        if (message.oracleQuote !== "") {
            writer.uint32(74).string(message.oracleQuote);
        }
        if (message.oracleType !== 0) {
            writer.uint32(80).int32(message.oracleType);
        }
        if (message.expiry !== "0") {
            writer.uint32(88).int64(message.expiry);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseInsuranceFund();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.depositDenom = reader.string();
                    break;
                case 2:
                    message.insurancePoolTokenDenom = reader.string();
                    break;
                case 3:
                    message.redemptionNoticePeriodDuration = duration_1.Duration.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.balance = reader.string();
                    break;
                case 5:
                    message.totalShare = reader.string();
                    break;
                case 6:
                    message.marketId = reader.string();
                    break;
                case 7:
                    message.marketTicker = reader.string();
                    break;
                case 8:
                    message.oracleBase = reader.string();
                    break;
                case 9:
                    message.oracleQuote = reader.string();
                    break;
                case 10:
                    message.oracleType = reader.int32();
                    break;
                case 11:
                    message.expiry = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            depositDenom: isSet(object.depositDenom) ? String(object.depositDenom) : "",
            insurancePoolTokenDenom: isSet(object.insurancePoolTokenDenom) ? String(object.insurancePoolTokenDenom) : "",
            redemptionNoticePeriodDuration: isSet(object.redemptionNoticePeriodDuration)
                ? duration_1.Duration.fromJSON(object.redemptionNoticePeriodDuration)
                : undefined,
            balance: isSet(object.balance) ? String(object.balance) : "",
            totalShare: isSet(object.totalShare) ? String(object.totalShare) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            marketTicker: isSet(object.marketTicker) ? String(object.marketTicker) : "",
            oracleBase: isSet(object.oracleBase) ? String(object.oracleBase) : "",
            oracleQuote: isSet(object.oracleQuote) ? String(object.oracleQuote) : "",
            oracleType: isSet(object.oracleType) ? (0, oracle_1.oracleTypeFromJSON)(object.oracleType) : 0,
            expiry: isSet(object.expiry) ? String(object.expiry) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.depositDenom !== undefined && (obj.depositDenom = message.depositDenom);
        message.insurancePoolTokenDenom !== undefined && (obj.insurancePoolTokenDenom = message.insurancePoolTokenDenom);
        message.redemptionNoticePeriodDuration !== undefined &&
            (obj.redemptionNoticePeriodDuration = message.redemptionNoticePeriodDuration
                ? duration_1.Duration.toJSON(message.redemptionNoticePeriodDuration)
                : undefined);
        message.balance !== undefined && (obj.balance = message.balance);
        message.totalShare !== undefined && (obj.totalShare = message.totalShare);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.marketTicker !== undefined && (obj.marketTicker = message.marketTicker);
        message.oracleBase !== undefined && (obj.oracleBase = message.oracleBase);
        message.oracleQuote !== undefined && (obj.oracleQuote = message.oracleQuote);
        message.oracleType !== undefined && (obj.oracleType = (0, oracle_1.oracleTypeToJSON)(message.oracleType));
        message.expiry !== undefined && (obj.expiry = message.expiry);
        return obj;
    },
    create: function (base) {
        return exports.InsuranceFund.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        var message = createBaseInsuranceFund();
        message.depositDenom = (_a = object.depositDenom) !== null && _a !== void 0 ? _a : "";
        message.insurancePoolTokenDenom = (_b = object.insurancePoolTokenDenom) !== null && _b !== void 0 ? _b : "";
        message.redemptionNoticePeriodDuration =
            (object.redemptionNoticePeriodDuration !== undefined && object.redemptionNoticePeriodDuration !== null)
                ? duration_1.Duration.fromPartial(object.redemptionNoticePeriodDuration)
                : undefined;
        message.balance = (_c = object.balance) !== null && _c !== void 0 ? _c : "";
        message.totalShare = (_d = object.totalShare) !== null && _d !== void 0 ? _d : "";
        message.marketId = (_e = object.marketId) !== null && _e !== void 0 ? _e : "";
        message.marketTicker = (_f = object.marketTicker) !== null && _f !== void 0 ? _f : "";
        message.oracleBase = (_g = object.oracleBase) !== null && _g !== void 0 ? _g : "";
        message.oracleQuote = (_h = object.oracleQuote) !== null && _h !== void 0 ? _h : "";
        message.oracleType = (_j = object.oracleType) !== null && _j !== void 0 ? _j : 0;
        message.expiry = (_k = object.expiry) !== null && _k !== void 0 ? _k : "0";
        return message;
    },
};
function createBaseRedemptionSchedule() {
    return { id: "0", marketId: "", redeemer: "", claimableRedemptionTime: undefined, redemptionAmount: undefined };
}
exports.RedemptionSchedule = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.id !== "0") {
            writer.uint32(8).uint64(message.id);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.redeemer !== "") {
            writer.uint32(26).string(message.redeemer);
        }
        if (message.claimableRedemptionTime !== undefined) {
            timestamp_1.Timestamp.encode(toTimestamp(message.claimableRedemptionTime), writer.uint32(34).fork()).ldelim();
        }
        if (message.redemptionAmount !== undefined) {
            coin_1.Coin.encode(message.redemptionAmount, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseRedemptionSchedule();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = longToString(reader.uint64());
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.redeemer = reader.string();
                    break;
                case 4:
                    message.claimableRedemptionTime = fromTimestamp(timestamp_1.Timestamp.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.redemptionAmount = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            id: isSet(object.id) ? String(object.id) : "0",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            redeemer: isSet(object.redeemer) ? String(object.redeemer) : "",
            claimableRedemptionTime: isSet(object.claimableRedemptionTime)
                ? fromJsonTimestamp(object.claimableRedemptionTime)
                : undefined,
            redemptionAmount: isSet(object.redemptionAmount) ? coin_1.Coin.fromJSON(object.redemptionAmount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.redeemer !== undefined && (obj.redeemer = message.redeemer);
        message.claimableRedemptionTime !== undefined &&
            (obj.claimableRedemptionTime = message.claimableRedemptionTime.toISOString());
        message.redemptionAmount !== undefined &&
            (obj.redemptionAmount = message.redemptionAmount ? coin_1.Coin.toJSON(message.redemptionAmount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.RedemptionSchedule.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseRedemptionSchedule();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "0";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.redeemer = (_c = object.redeemer) !== null && _c !== void 0 ? _c : "";
        message.claimableRedemptionTime = (_d = object.claimableRedemptionTime) !== null && _d !== void 0 ? _d : undefined;
        message.redemptionAmount = (object.redemptionAmount !== undefined && object.redemptionAmount !== null)
            ? coin_1.Coin.fromPartial(object.redemptionAmount)
            : undefined;
        return message;
    },
};
function toTimestamp(date) {
    var seconds = Math.trunc(date.getTime() / 1000).toString();
    var nanos = (date.getTime() % 1000) * 1000000;
    return { seconds: seconds, nanos: nanos };
}
function fromTimestamp(t) {
    var millis = Number(t.seconds) * 1000;
    millis += t.nanos / 1000000;
    return new Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
