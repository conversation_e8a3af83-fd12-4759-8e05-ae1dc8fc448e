"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.grpcPkg = exports.grpc = exports.getGrpcTransport = void 0;
const grpcPkg = __importStar(require("@injectivelabs/grpc-web"));
exports.grpcPkg = grpcPkg;
const grpc_web_node_http_transport_1 = require("@injectivelabs/grpc-web-node-http-transport");
const grpc_web_react_native_transport_1 = require("@injectivelabs/grpc-web-react-native-transport");
const helpers_js_1 = require("./helpers.js");
const grpc = grpcPkg.grpc ??
    grpcPkg.default
        .grpc ??
    grpcPkg;
exports.grpc = grpc;
const getGrpcTransport = () => {
    if ((0, helpers_js_1.isReactNative)()) {
        return (0, grpc_web_react_native_transport_1.ReactNativeTransport)({ withCredentials: true });
    }
    if ((0, helpers_js_1.isNode)()) {
        return (0, grpc_web_node_http_transport_1.NodeHttpTransport)();
    }
    return grpc.CrossBrowserHttpTransport({ withCredentials: false });
};
exports.getGrpcTransport = getGrpcTransport;
