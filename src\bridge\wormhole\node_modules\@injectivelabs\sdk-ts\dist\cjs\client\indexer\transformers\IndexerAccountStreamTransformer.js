"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexerAccountStreamTransformer = void 0;
const IndexerGrpcAccountTransformer_js_1 = require("./IndexerGrpcAccountTransformer.js");
const index_js_1 = require("../../../types/index.js");
/**
 * @category Indexer Stream Transformer
 */
class IndexerAccountStreamTransformer {
    static balanceStreamCallback = (response) => {
        const balance = response.balance;
        return {
            balance: balance
                ? IndexerGrpcAccountTransformer_js_1.IndexerGrpcAccountTransformer.grpcBalanceToBalance(balance)
                : undefined,
            operation: index_js_1.StreamOperation.Update,
            timestamp: response.timestamp,
        };
    };
}
exports.IndexerAccountStreamTransformer = IndexerAccountStreamTransformer;
