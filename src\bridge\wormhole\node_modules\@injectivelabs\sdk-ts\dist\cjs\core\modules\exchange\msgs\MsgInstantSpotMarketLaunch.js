"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const numbers_js_1 = require("../../../../utils/numbers.js");
const createMessage = (params) => {
    const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.create();
    message.sender = params.proposer;
    message.ticker = params.market.ticker;
    message.baseDenom = params.market.baseDenom;
    message.quoteDenom = params.market.quoteDenom;
    message.minPriceTickSize = params.market.minPriceTickSize;
    message.minQuantityTickSize = params.market.minQuantityTickSize;
    message.minNotional = params.market.minNotional;
    message.baseDecimals = Number(params.market.baseDecimals);
    message.quoteDecimals = Number(params.market.quoteDecimals);
    return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.fromPartial(message);
};
/**
 * @category Messages
 */
class MsgInstantSpotMarketLaunch extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgInstantSpotMarketLaunch(params);
    }
    toProto() {
        const { params: initialParams } = this;
        const params = {
            ...initialParams,
            market: {
                ...initialParams.market,
                minPriceTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minPriceTickSize).toFixed(),
                minQuantityTickSize: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minQuantityTickSize).toFixed(),
                minNotional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(initialParams.market.minNotional).toFixed(),
            },
        };
        return createMessage(params);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            ...proto,
        };
    }
    toAmino() {
        const { params } = this;
        const message = {
            ...(0, snakecase_keys_1.default)(createMessage(params)),
        };
        return {
            type: 'exchange/MsgInstantSpotMarketLaunch',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            ...value,
        };
    }
    toEip712() {
        const amino = this.toAmino();
        const { type, value } = amino;
        const messageAdjusted = {
            ...value,
            min_price_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_price_tick_size).toFixed(),
            min_quantity_tick_size: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_quantity_tick_size).toFixed(),
            min_notional: (0, numbers_js_1.amountToCosmosSdkDecAmount)(value.min_notional).toFixed(),
        };
        return {
            type,
            value: messageAdjusted,
        };
    }
    toEip712V2() {
        const { params } = this;
        const web3gw = this.toWeb3Gw();
        const messageAdjusted = {
            ...web3gw,
            min_price_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minPriceTickSize),
            min_quantity_tick_size: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minQuantityTickSize),
            min_notional: (0, numbers_js_1.numberToCosmosSdkDecString)(params.market.minNotional),
        };
        return messageAdjusted;
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgInstantSpotMarketLaunch',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch.encode(this.toProto()).finish();
    }
}
exports.default = MsgInstantSpotMarketLaunch;
