{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../src/coder/common.ts"], "names": [], "mappings": ";;;AACA,0CAAuC;AAEvC,SAAgB,WAAW,CAAC,GAAQ,EAAE,UAAsB;IAC1D,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE;QAC5B,KAAK,QAAQ,CAAC,CAAC;YACb,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM;iBAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;iBACjC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;SACzC;QAED,KAAK,MAAM,CAAC,CAAC;YACX,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC5D,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACnB,OAAO,CAAC,CAAC;iBACV;gBAED,OAAO,OAAO,CAAC,MAAM;qBAClB,GAAG,CAAC,CAAC,CAAqB,EAAE,EAAE;oBAC7B,uBAAuB;oBACvB,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE;wBAC3C,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;qBACzB;oBAED,qBAAqB;oBACrB,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC,CAAC;qBACD,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;SACtC;QAED,KAAK,OAAO,CAAC,CAAC;YACZ,OAAO,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC7C;KACF;AACH,CAAC;AAlCD,kCAkCC;AAED,gFAAgF;AAChF,qDAAqD;AACrD,SAAS,QAAQ,CAAC,GAAQ,EAAE,EAAW;;IACrC,QAAQ,EAAE,EAAE;QACV,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX,KAAK,IAAI;YACP,OAAO,CAAC,CAAC;QACX,KAAK,IAAI;YACP,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,CAAC,CAAC;QACX,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM;YACT,OAAO,EAAE,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX,KAAK,QAAQ;YACX,OAAO,CAAC,CAAC;QACX,KAAK,WAAW;YACd,OAAO,EAAE,CAAC;QACZ;YACE,IAAI,KAAK,IAAI,EAAE,EAAE;gBACf,OAAO,CAAC,CAAC;aACV;YACD,IAAI,QAAQ,IAAI,EAAE,EAAE;gBAClB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;aACrC;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;aACtC;YACD,IAAI,SAAS,IAAI,EAAE,EAAE;gBACnB,MAAM,QAAQ,GAAG,MAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,OAAO,CAAC,mCAAI,EAAE,CAAC;gBACvE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,MAAM,IAAI,mBAAQ,CAAC,mBAAmB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;iBAC7D;gBACD,IAAI,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE1B,OAAO,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;aAClC;YACD,IAAI,OAAO,IAAI,EAAE,EAAE;gBACjB,IAAI,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,OAAO,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,SAAS,CAAC;aAC3C;YACD,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;KACzD;AACH,CAAC"}