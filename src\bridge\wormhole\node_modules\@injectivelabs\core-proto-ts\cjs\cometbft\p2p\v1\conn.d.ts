import _m0 from "protobufjs/minimal.js";
import { PublicKey } from "../../crypto/v1/keys";
export declare const protobufPackage = "cometbft.p2p.v1";
/** PacketPing is a request to confirm that the connection is alive. */
export interface PacketPing {
}
/** PacketPong is a response to confirm that the connection is alive. */
export interface PacketPong {
}
/**
 * PacketMsg contains data for the specified channel ID. EOF means the message
 * is fully received.
 */
export interface PacketMsg {
    channelId: number;
    eof: boolean;
    data: Uint8Array;
}
/** Packet is an abstract p2p message. */
export interface Packet {
    packetPing?: PacketPing | undefined;
    packetPong?: PacketPong | undefined;
    packetMsg?: PacketMsg | undefined;
}
/**
 * AuthSigMessage is sent during the authentication and contains our/remote's
 * signature along with the public key.
 */
export interface AuthSigMessage {
    pubKey: PublicKey | undefined;
    sig: Uint8Array;
}
export declare const PacketPing: {
    encode(_: Packet<PERSON>ing, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PacketPing;
    fromJSON(_: any): PacketPing;
    toJSON(_: PacketPing): unknown;
    create(base?: DeepPartial<PacketPing>): PacketPing;
    fromPartial(_: DeepPartial<PacketPing>): PacketPing;
};
export declare const PacketPong: {
    encode(_: PacketPong, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PacketPong;
    fromJSON(_: any): PacketPong;
    toJSON(_: PacketPong): unknown;
    create(base?: DeepPartial<PacketPong>): PacketPong;
    fromPartial(_: DeepPartial<PacketPong>): PacketPong;
};
export declare const PacketMsg: {
    encode(message: PacketMsg, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PacketMsg;
    fromJSON(object: any): PacketMsg;
    toJSON(message: PacketMsg): unknown;
    create(base?: DeepPartial<PacketMsg>): PacketMsg;
    fromPartial(object: DeepPartial<PacketMsg>): PacketMsg;
};
export declare const Packet: {
    encode(message: Packet, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Packet;
    fromJSON(object: any): Packet;
    toJSON(message: Packet): unknown;
    create(base?: DeepPartial<Packet>): Packet;
    fromPartial(object: DeepPartial<Packet>): Packet;
};
export declare const AuthSigMessage: {
    encode(message: AuthSigMessage, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): AuthSigMessage;
    fromJSON(object: any): AuthSigMessage;
    toJSON(message: AuthSigMessage): unknown;
    create(base?: DeepPartial<AuthSigMessage>): AuthSigMessage;
    fromPartial(object: DeepPartial<AuthSigMessage>): AuthSigMessage;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
