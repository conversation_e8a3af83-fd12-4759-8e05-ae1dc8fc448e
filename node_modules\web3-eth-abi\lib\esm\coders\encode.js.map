{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../../../src/coders/encode.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAEvC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAEzC;;;;;;;;;GASG;AACH,SAAS,cAAc,CAAC,MAAiB;IACxC,MAAM,GAAG,GAAmB,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACtB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,MAAM,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,GAAG,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,OAAO;gBACb,UAAU,EAAE,cAAc;gBAC1B,IAAI,EAAE,EAAE;gBACR,iEAAiE;aACjD,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACP,iEAAiE;YACjE,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,KAAe,EAAE,IAAI,CAAC,EAAkB,CAAC,CAAC;QAClE,CAAC;IACF,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC;AACZ,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAA4B,EAAE,MAAiB;IAC/E,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM,MAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QACnC,MAAM,IAAI,QAAQ,CAAC,iDAAiD,EAAE;YACrE,QAAQ,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,MAAM;SACvB,CAAC,CAAC;IACJ,CAAC;IAED,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;IACnC,OAAO,KAAK,CAAC,qBAAqB,CACjC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,OAAO,CAC/E,CAAC;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,MAAM,UAAU,6BAA6B,CAAC,MAAiB;IAC9D,IAAI,CAAC;QACJ,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;QACzC,OAAO,KAAK,CAAC,qBAAqB,CACjC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC,OAAO,CAC/E,CAAC;IACH,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,qDAAqD;QACrD,MAAM,IAAI,QAAQ,CAAC,yCAAyC,EAAE;YAC7D,MAAM;SACN,CAAC,CAAC;IACJ,CAAC;AACF,CAAC"}