import { InsuranceFund, InsuranceModuleParams } from '../types/insurance.js';
import { GrpcInsuranceFund } from '../types/insurance.js';
import { InjectiveInsuranceV1Beta1Query } from '@injectivelabs/core-proto-ts';
/**
 * @category Chain Grpc Transformer
 */
export declare class ChainGrpcInsuranceFundTransformer {
    static moduleParamsResponseToModuleParams(response: InjectiveInsuranceV1Beta1Query.QueryInsuranceParamsResponse): InsuranceModuleParams;
    static grpcInsuranceFundToInsuranceFund(grpcFund: GrpcInsuranceFund): InsuranceFund;
    static insuranceFundResponseToInsuranceFund(response: InjectiveInsuranceV1Beta1Query.QueryInsuranceFundResponse): InsuranceFund;
    static insuranceFundsResponseToInsuranceFunds(response: InjectiveInsuranceV1Beta1Query.QueryInsuranceFundsResponse): InsuranceFund[];
    static redemptionsResponseToRedemptions(response: InjectiveInsuranceV1Beta1Query.QueryEstimatedRedemptionsResponse): {
        amount: string;
        denom: string;
    }[];
    static estimatedRedemptionsResponseToEstimatedRedemptions(response: InjectiveInsuranceV1Beta1Query.QueryPendingRedemptionsResponse): {
        amount: string;
        denom: string;
    }[];
}
