{"version": 3, "sources": ["../../src/internal/utils/utils.ts"], "sourcesContent": ["import { AccountAddress, AccountAddressInput } from \"../../core/accountAddress\";\nimport { MoveModuleBytecode, LedgerVersionArg, AccountData } from \"../../types/types\";\nimport { AptosConfig } from \"../../api/aptosConfig\";\nimport { getAptosFullNode } from \"../../client\";\nimport { memoizeAsync } from \"../../utils/memoize\";\n\n/**\n * Retrieves account information for a specified account address.\n *\n * @param args - The arguments for retrieving account information.\n * @param args.aptosConfig - The configuration object for Aptos.\n * @param args.accountAddress - The address of the account to retrieve information for.\n * @group Implementation\n */\nexport async function getInfo(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n}): Promise<AccountData> {\n  const { aptosConfig, accountAddress } = args;\n  const { data } = await getAptosFullNode<{}, AccountData>({\n    aptosConfig,\n    originMethod: \"getInfo\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}`,\n  });\n  return data;\n}\n\n/**\n * Queries for a move module given an account address and module name.\n * This function can help you retrieve the module's ABI and other relevant information.\n *\n * @param args - The arguments for retrieving the module.\n * @param args.aptosConfig - The configuration for the Aptos client.\n * @param args.accountAddress - The account address in hex-encoded 32 byte format.\n * @param args.moduleName - The name of the module to retrieve.\n * @param args.options - Optional parameters for the request.\n * @param args.options.ledgerVersion - Specifies the ledger version of transactions. By default, the latest version will be used.\n * @returns The move module.\n * @group Implementation\n */\nexport async function getModule(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  moduleName: string;\n  options?: LedgerVersionArg;\n}): Promise<MoveModuleBytecode> {\n  // We don't memoize the account module by ledger version, as it's not a common use case, this would be handled\n  // by the developer directly\n  if (args.options?.ledgerVersion !== undefined) {\n    return getModuleInner(args);\n  }\n\n  return memoizeAsync(\n    async () => getModuleInner(args),\n    `module-${args.accountAddress}-${args.moduleName}`,\n    1000 * 60 * 5, // 5 minutes\n  )();\n}\n\n/**\n * Retrieves the bytecode of a specified module from a given account address.\n *\n * @param args - The parameters for retrieving the module bytecode.\n * @param args.aptosConfig - The configuration for connecting to the Aptos network.\n * @param args.accountAddress - The address of the account from which to retrieve the module.\n * @param args.moduleName - The name of the module to retrieve.\n * @param args.options - Optional parameters for specifying the ledger version.\n * @param args.options.ledgerVersion - The specific ledger version to query.\n * @group Implementation\n */\nasync function getModuleInner(args: {\n  aptosConfig: AptosConfig;\n  accountAddress: AccountAddressInput;\n  moduleName: string;\n  options?: LedgerVersionArg;\n}): Promise<MoveModuleBytecode> {\n  const { aptosConfig, accountAddress, moduleName, options } = args;\n\n  const { data } = await getAptosFullNode<{}, MoveModuleBytecode>({\n    aptosConfig,\n    originMethod: \"getModule\",\n    path: `accounts/${AccountAddress.from(accountAddress).toString()}/module/${moduleName}`,\n    params: { ledger_version: options?.ledgerVersion },\n  });\n  return data;\n}\n"], "mappings": "2HAcA,eAAsBA,EAAQC,EAGL,CACvB,GAAM,CAAE,YAAAC,EAAa,eAAAC,CAAe,EAAIF,EAClC,CAAE,KAAAG,CAAK,EAAI,MAAMC,EAAkC,CACvD,YAAAH,EACA,aAAc,UACd,KAAM,YAAYI,EAAe,KAAKH,CAAc,EAAE,SAAS,CAAC,EAClE,CAAC,EACD,OAAOC,CACT,CAeA,eAAsBG,EAAUN,EAKA,CAG9B,OAAIA,EAAK,SAAS,gBAAkB,OAC3BO,EAAeP,CAAI,EAGrBQ,EACL,SAAYD,EAAeP,CAAI,EAC/B,UAAUA,EAAK,cAAc,IAAIA,EAAK,UAAU,GAChD,IAAO,GAAK,CACd,EAAE,CACJ,CAaA,eAAeO,EAAeP,EAKE,CAC9B,GAAM,CAAE,YAAAC,EAAa,eAAAC,EAAgB,WAAAO,EAAY,QAAAC,CAAQ,EAAIV,EAEvD,CAAE,KAAAG,CAAK,EAAI,MAAMC,EAAyC,CAC9D,YAAAH,EACA,aAAc,YACd,KAAM,YAAYI,EAAe,KAAKH,CAAc,EAAE,SAAS,CAAC,WAAWO,CAAU,GACrF,OAAQ,CAAE,eAAgBC,GAAS,aAAc,CACnD,CAAC,EACD,OAAOP,CACT", "names": ["getInfo", "args", "aptosConfig", "accountAddress", "data", "getAptosFullNode", "Account<PERSON><PERSON><PERSON>", "getModule", "getModuleInner", "memoizeAsync", "moduleName", "options"]}