import _m0 from "protobufjs/minimal.js";
import { Params, RegisteredContract } from "./wasmx";
export declare const protobufPackage = "injective.wasmx.v1";
export interface RegisteredContractWithAddress {
    address: string;
    registeredContract: RegisteredContract | undefined;
}
/** GenesisState defines the wasmx module's genesis state. */
export interface GenesisState {
    /** params defines all the parameters of related to wasmx. */
    params: Params | undefined;
    /**
     * registered_contracts is an array containing the genesis registered
     * contracts
     */
    registeredContracts: RegisteredContractWithAddress[];
}
export declare const RegisteredContractWithAddress: {
    encode(message: Registered<PERSON><PERSON><PERSON>t<PERSON><PERSON><PERSON>dd<PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): RegisteredContractWithAddress;
    fromJSON(object: any): RegisteredContractWithAddress;
    toJSON(message: RegisteredContractWithAddress): unknown;
    create(base?: DeepPartial<RegisteredContractWithAddress>): RegisteredContractWithAddress;
    fromPartial(object: DeepPartial<RegisteredContractWithAddress>): RegisteredContractWithAddress;
};
export declare const GenesisState: {
    encode(message: GenesisState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): GenesisState;
    fromJSON(object: any): GenesisState;
    toJSON(message: GenesisState): unknown;
    create(base?: DeepPartial<GenesisState>): GenesisState;
    fromPartial(object: DeepPartial<GenesisState>): GenesisState;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
