import { InjectiveAuctionV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcAuctionApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveAuctionV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchModuleParams(): Promise<import("../types/auction.js").AuctionModuleStateParams>;
    fetchCurrentBasket(): Promise<import("../types/auction.js").AuctionCurrentBasket>;
    fetchModuleState(): Promise<import("../types/auction.js").AuctionModuleState>;
    fetchLastAuctionResult(): Promise<import("../types/auction.js").AuctionLastAuctionResult | undefined>;
}
