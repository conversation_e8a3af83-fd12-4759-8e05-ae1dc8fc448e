import { ITransactionOption, IZkEvmClientConfig } from '../interfaces';
import { BaseToken, Web3SideChainClient } from '../utils';
import { TYPE_AMOUNT } from '../types';
/**
 * ZkEVMBridgeAdapter used ZkEVMBridge to implement additional custom features
 * like bridging custom ERC20
 */
export declare class ZkEVMBridgeAdapter extends BaseToken<IZkEvmClientConfig> {
    constructor(client_: Web3SideChainClient<IZkEvmClientConfig>, address: string, isParent: boolean);
    method(methodName: string, ...args: any[]): Promise<import("..").BaseContractMethod>;
    /**
     * uses the bridge function present in the adapter contract
     * @param recipient
     * @param amount
     * @param forceUpdateGlobalExitRoot
     * @param option
     *
     * @returns
     * @memberof ZkEvmCustomBridge
     */
    bridgeToken(recipient: string, amount: TYPE_AMOUNT, forceUpdateGlobalExitRoot?: boolean, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
}
