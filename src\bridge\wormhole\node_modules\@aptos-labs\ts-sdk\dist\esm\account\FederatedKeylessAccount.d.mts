import { HexInput } from '../types/types.mjs';
import { AccountAddress, AccountAddressInput } from '../core/accountAddress.mjs';
import { Deserializer } from '../bcs/deserializer.mjs';
import { Serializer } from '../bcs/serializer.mjs';
import { EphemeralKeyPair } from './EphemeralKeyPair.mjs';
import { AbstractKeylessAccount, ProofFetchCallback } from './AbstractKeylessAccount.mjs';
import { F as FederatedKeylessPublicKey, l as ZeroKnowledgeSig, n as Groth16VerificationKey } from '../federatedKeyless-DAYXjY2Y.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../core/common.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/hex.mjs';
import '../core/crypto/privateKey.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../api/aptosConfig.mjs';
import '../utils/const.mjs';
import '../core/crypto/ephemeral.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../Ed25519Account-D9XrCLfE.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';

/**
 * Account implementation for the FederatedKeyless authentication scheme.
 *
 * Used to represent a FederatedKeyless based account and sign transactions with it.
 *
 * Use `FederatedKeylessAccount.create()` to instantiate a KeylessAccount with a JSON Web Token (JWT), proof, EphemeralKeyPair and the
 * address the JSON Web Key Set (JWKS) are installed that will be used to verify the JWT.
 *
 * When the proof expires or the JWT becomes invalid, the KeylessAccount must be instantiated again with a new JWT,
 * EphemeralKeyPair, and corresponding proof.
 * @group Implementation
 * @category Account (On-Chain Model)
 */
declare class FederatedKeylessAccount extends AbstractKeylessAccount {
    /**
     * The FederatedKeylessPublicKey associated with the account
     * @group Implementation
     * @category Account (On-Chain Model)
     */
    readonly publicKey: FederatedKeylessPublicKey;
    readonly audless: boolean;
    /**
     * Use the static generator `FederatedKeylessAccount.create(...)` instead.
     * Creates a KeylessAccount instance using the provided parameters.
     * This function allows you to set up a KeylessAccount with specific attributes such as address, proof, and JWT.
     *
     * @param args - The parameters for creating a KeylessAccount.
     * @param args.address - Optional account address associated with the KeylessAccount.
     * @param args.proof - A Zero Knowledge Signature or a promise that resolves to one.
     * @param args.jwt - A JSON Web Token used for authentication.
     * @param args.ephemeralKeyPair - The ephemeral key pair used in the account creation.
     * @param args.jwkAddress - The address which stores the JSON Web Key Set (JWKS) used to verify the JWT.
     * @param args.uidKey - Optional key for user identification, defaults to "sub".
     * @param args.proofFetchCallback - Optional callback function for fetching proof.
     */
    constructor(args: {
        address?: AccountAddress;
        ephemeralKeyPair: EphemeralKeyPair;
        iss: string;
        uidKey: string;
        uidVal: string;
        aud: string;
        pepper: HexInput;
        jwkAddress: AccountAddress;
        proof: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;
        proofFetchCallback?: ProofFetchCallback;
        jwt: string;
        verificationKeyHash?: HexInput;
        audless?: boolean;
    });
    /**
     * Serializes the transaction data into a format suitable for transmission or storage.
     * This function ensures that both the transaction bytes and the proof are properly serialized.
     *
     * @param serializer - The serializer instance used to convert the transaction data into bytes.
     */
    serialize(serializer: Serializer): void;
    /**
     * Deserializes the provided deserializer to create a KeylessAccount instance.
     * This function extracts necessary components such as the JWT, UID key, pepper, ephemeral key pair, and proof from the deserializer.
     *
     * @param deserializer - The deserializer instance used to retrieve the serialized data.
     * @returns A KeylessAccount instance created from the deserialized data.
     */
    static deserialize(deserializer: Deserializer): FederatedKeylessAccount;
    /**
     * Deserialize bytes using this account's information.
     *
     * @param bytes The bytes being interpreted.
     * @returns
     */
    static fromBytes(bytes: HexInput): FederatedKeylessAccount;
    /**
     * Creates a KeylessAccount instance using the provided parameters.
     * This function allows you to set up a KeylessAccount with specific attributes such as address, proof, and JWT.
     * This is used instead of the KeylessAccount constructor.
     *
     * @param args - The parameters for creating a KeylessAccount.
     * @param args.address - Optional account address associated with the KeylessAccount.
     * @param args.proof - A Zero Knowledge Signature or a promise that resolves to one.
     * @param args.jwt - A JSON Web Token used for authentication.
     * @param args.ephemeralKeyPair - The ephemeral key pair used in the account creation.
     * @param args.jwkAddress - The address which stores the JSON Web Key Set (JWKS) used to verify the JWT.
     * @param args.uidKey - Optional key for user identification, defaults to "sub".
     * @param args.proofFetchCallback - Optional callback function for fetching proof.
     */
    static create(args: {
        address?: AccountAddress;
        proof: ZeroKnowledgeSig | Promise<ZeroKnowledgeSig>;
        jwt: string;
        ephemeralKeyPair: EphemeralKeyPair;
        pepper: HexInput;
        jwkAddress: AccountAddressInput;
        uidKey?: string;
        proofFetchCallback?: ProofFetchCallback;
        verificationKey?: Groth16VerificationKey;
    }): FederatedKeylessAccount;
}

export { FederatedKeylessAccount };
