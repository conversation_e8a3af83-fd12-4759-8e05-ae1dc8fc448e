import{a as p}from"./chunk-DPW6ELCQ.mjs";import{e as d,j as S}from"./chunk-C3Q23D22.mjs";import{a as u}from"./chunk-ROT6S6BM.mjs";import{a as f}from"./chunk-WSR5EBJM.mjs";import{a as h}from"./chunk-WCMW2L3P.mjs";import{a as g}from"./chunk-EBMEXURY.mjs";import{b as s}from"./chunk-STY74NUA.mjs";import{sha3_256 as H}from"@noble/hashes/sha3";import{secp256k1 as y}from"@noble/curves/secp256k1";import{HDKey as A}from"@scure/bip32";var i=class i extends f{constructor(e){super();let t=s.fromHexInput(e),{length:r}=t.toUint8Array();if(r===i.LENGTH)this.key=t;else if(r===i.COMPRESSED_LENGTH){let o=y.ProjectivePoint.fromHex(t.toUint8Array());this.key=s.fromHexInput(o.toRawBytes(!1))}else throw new Error(`PublicKey length should be ${i.LENGTH} or ${i.COMPRESSED_LENGTH}, received ${r}`)}verifySignature(e){let{message:t,signature:r}=e,o=p(t),c=s.fromHexInput(o).toUint8Array(),P=H(c),x=r.toUint8Array();return y.verify(x,P,this.key.toUint8Array(),{lowS:!0})}async verifySignatureAsync(e){return this.verifySignature(e)}toUint8Array(){return this.key.toUint8Array()}serialize(e){e.serializeBytes(this.key.toUint8Array())}deserialize(e){let t=e.deserializeBytes();return new l(t)}static deserialize(e){let t=e.deserializeBytes();return new i(t)}static isPublicKey(e){return e instanceof i}static isInstance(e){return"key"in e&&e.key?.data?.length===i.LENGTH}};i.LENGTH=65,i.COMPRESSED_LENGTH=33;var m=i,n=class n extends g{constructor(e,t){super();let r=u.parseHexInput(e,"secp256k1",t);if(r.toUint8Array().length!==n.LENGTH)throw new Error(`PrivateKey length should be ${n.LENGTH}`);this.key=r}static generate(){let e=y.utils.randomPrivateKey();return new n(e,!1)}static fromDerivationPath(e,t){if(!d(e))throw new Error(`Invalid derivation path ${e}`);return n.fromDerivationPathInner(e,S(t))}static fromDerivationPathInner(e,t){let{privateKey:r}=A.fromMasterSeed(t).derive(e);if(r===null)throw new Error("Invalid key");return new n(r,!1)}sign(e){let t=p(e),r=s.fromHexInput(t),o=H(r.toUint8Array()),c=y.sign(o,this.key.toUint8Array(),{lowS:!0});return new l(c.toCompactRawBytes())}publicKey(){let e=y.getPublicKey(this.key.toUint8Array(),!1);return new m(e)}toUint8Array(){return this.key.toUint8Array()}toString(){return this.toAIP80String()}toHexString(){return this.key.toString()}toAIP80String(){return u.formatPrivateKey(this.key.toString(),"secp256k1")}serialize(e){e.serializeBytes(this.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new n(t,!1)}static isPrivateKey(e){return e instanceof n}};n.LENGTH=32;var v=n,a=class a extends h{constructor(e){super();let t=s.fromHexInput(e);if(t.toUint8Array().length!==a.LENGTH)throw new Error(`Signature length should be ${a.LENGTH}, received ${t.toUint8Array().length}`);this.data=t}toUint8Array(){return this.data.toUint8Array()}serialize(e){e.serializeBytes(this.data.toUint8Array())}static deserialize(e){let t=e.deserializeBytes();return new a(t)}};a.LENGTH=64;var l=a;export{m as a,v as b,l as c};
//# sourceMappingURL=chunk-CO67Y6YE.mjs.map