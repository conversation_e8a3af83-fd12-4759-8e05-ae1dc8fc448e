"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.recoverTypedSignaturePubKey = void 0;
const ethereumjs_util_1 = require("ethereumjs-util");
const secp256k1 = __importStar(require("secp256k1"));
const eth_sig_util_1 = require("@metamask/eth-sig-util");
const recoverTypedSignaturePubKey = (data, signature) => {
    const compressedPubKeyPrefix = Buffer.from('04', 'hex');
    const message = eth_sig_util_1.TypedDataUtils.eip712Hash(data, eth_sig_util_1.SignTypedDataVersion.V4);
    const sigParams = (0, ethereumjs_util_1.fromRpcSig)(signature);
    const publicKey = (0, ethereumjs_util_1.ecrecover)(message, sigParams.v, sigParams.r, sigParams.s);
    const prefixedKey = Buffer.concat([compressedPubKeyPrefix, publicKey]);
    const compressedKey = Buffer.from(secp256k1.publicKeyConvert(prefixedKey));
    return `0x${compressedKey.toString('hex')}`;
};
exports.recoverTypedSignaturePubKey = recoverTypedSignaturePubKey;
