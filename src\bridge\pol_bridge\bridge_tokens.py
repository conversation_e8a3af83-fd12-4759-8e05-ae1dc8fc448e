#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import asyncio
import argparse
import yaml
import pathlib
from decimal import Decimal
from typing import Dict, Any, Optional, Tuple, Union, List
from web3 import Web3
from eth_account import Account
import time
import json
import requests
import logging
from web3.middleware import geth_poa_middleware
from eth_typing import Address
from eth_utils import to_checksum_address
import traceback
import websockets
import aiohttp
import platform
import urllib3
import warnings
import random

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 创建文件处理器
file_handler = logging.FileHandler('bridge_tokens.log')
file_handler.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# 添加处理器到日志记录器
logger.addHandler(console_handler)
logger.addHandler(file_handler)

from .token_list import PolygonBridgeTokens
from .monitor_token_tx import TokenTransactionMonitor

class PolygonEthereumBridge:
    """
    Polygon和以太坊之间的双向桥接工具
    使用官方的Polygon PoS桥实现代币的双向转移
    提供简单易用的接口，用于在两个网络之间桥接代币
    """
    
    # Polygon PoS桥合约地址
    # 主网地址
    POS_ROOT_CHAIN_MANAGER = "******************************************"  # RootChainManager on Ethereum
    ETHER_PREDICATE = "******************************************"         # EtherPredicate
    ERC20_PREDICATE = "******************************************"          # ERC20Predicate
    ERC721_PREDICATE = "******************************************"         # ERC721Predicate
    
    # 预测地址字典，用于不同类型的代币
    POS_TOKEN_PREDICATE = {
        "ERC20": "******************************************",   # ERC20代币预测地址
        "ERC721": "******************************************",  # ERC721代币预测地址
        "ETHER": "******************************************"    # ETH预测地址
    }
    
    ROOT_CHAIN_MANAGER = "******************************************"  # 根链管理器
    
    # 以太坊上的WETH合约
    WETH_ADDRESS = "******************************************"
    
    def __init__(
        self, 
        ethereum_rpc_url: str, 
        polygon_rpc_url: str,
        private_key: Optional[str] = None,
        private_key_env: Optional[str] = None,
        gas_price_gwei: float = 0.5,
        gas_limit: int = 500000,
        max_fee_per_gas: Optional[float] = None,
        max_priority_fee_per_gas: Optional[float] = None
    ):
        """
        初始化桥接工具
        """
        # 设置RPC URL
        self.ethereum_rpc_url = ethereum_rpc_url
        self.polygon_rpc_url = polygon_rpc_url
        
        # 从配置文件获取WebSocket URL
        config = load_config()
        self.ethereum_ws_url = config.get("rpc", {}).get("ethereum", {}).get("ws_url", ethereum_rpc_url)
        self.polygon_ws_url = config.get("rpc", {}).get("polygon", {}).get("ws_url", polygon_rpc_url)
        
        # 初始化Web3实例
        self.ethereum_web3 = Web3(Web3.HTTPProvider(ethereum_rpc_url))
        self.polygon_web3 = Web3(Web3.HTTPProvider(polygon_rpc_url))

        # 检查连接（提供更详细的错误信息）
        try:
            if not self.ethereum_web3.is_connected():
                raise ConnectionError(f"无法连接到以太坊节点: {ethereum_rpc_url}")
        except Exception as e:
            raise ConnectionError(f"以太坊节点连接测试失败: {ethereum_rpc_url}, 错误: {str(e)}")

        try:
            if not self.polygon_web3.is_connected():
                raise ConnectionError(f"无法连接到Polygon节点: {polygon_rpc_url}")
        except Exception as e:
            raise ConnectionError(f"Polygon节点连接测试失败: {polygon_rpc_url}, 错误: {str(e)}")
        
        # 添加PoS中间件
        from web3.middleware import geth_poa_middleware
        self.polygon_web3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # 获取私钥
        if private_key:
            self.private_key = private_key
        elif private_key_env:
            self.private_key = os.environ.get(private_key_env)
            if not self.private_key:
                raise ValueError(f"环境变量 {private_key_env} 未设置")
        else:
            raise ValueError("必须提供私钥或私钥环境变量名")
            
        # 确保私钥格式正确
        if not self.private_key.startswith('0x'):
            self.private_key = f"0x{self.private_key}"
            
        # 创建账户
        try:
            self.ethereum_account = Web3().eth.account.from_key(self.private_key)
            self.account = self.ethereum_account
            self.address = self.ethereum_account.address
        except Exception as e:
            raise ValueError(f"无法从私钥创建账户: {str(e)}")
        
        # 设置交易参数
        self.gas_price_gwei = gas_price_gwei
        self.gas_limit = gas_limit
        self.max_fee_per_gas = max_fee_per_gas
        self.max_priority_fee_per_gas = max_priority_fee_per_gas
        
        # 针对不同网络的gas配置
        self.eth_gas_config = {
            "gas_limit": 200000,              # 以太坊交易的标准gas限制
            "gas_limit_approve": 100000,      # 以太坊授权交易的gas限制
            "gas_price_gwei": 20,             # 以太坊的gas价格 (Gwei)
        }
        
        self.polygon_gas_config = {
            "gas_limit": 500000,              # Polygon交易的标准gas限制
            "gas_limit_approve": 200000,      # Polygon授权交易的gas限制
            "gas_price_gwei": 50,             # Polygon的gas价格 (Gwei)
        }
    
    def _parse_amount(self, amount: str, token_info: Dict[str, Any]) -> int:
        """将人类可读的金额转换为链上表示"""
        try:
            if not token_info:
                raise ValueError("token_info is None")
                
            decimals = token_info.get("decimals")
            if decimals is None:
                raise ValueError(f"无法获取代币精度，token_info: {token_info}")
                
            # 确保amount是字符串类型
            if isinstance(amount, (int, float)):
                amount = str(amount)
                
            try:
                amount_decimal = Decimal(amount)
                return int(amount_decimal * (10 ** decimals))
            except Exception as e:
                raise ValueError(f"金额转换失败: {amount} * (10 ** {decimals}), 错误: {str(e)}")
                
        except Exception as e:
            logging.error(f"_parse_amount 函数执行失败: {str(e)}")
            raise
    
    def get_token_balance(self, token_address: str, chain_id: int) -> int:
        """
        获取指定链上特定代币的余额
        
        Args:
            token_address: 代币合约地址
            chain_id: 链ID (1=以太坊, 137=Polygon)
            
        Returns:
            int: 代币余额（原始值，未按小数位转换）
        """
        # 确定使用哪个web3实例和配置
        if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID:
            web3 = self.ethereum_web3
            chain_name = "以太坊"
            rpc_url = web3.provider.endpoint_uri
        else:
            web3 = self.polygon_web3
            chain_name = "Polygon"
            rpc_url = web3.provider.endpoint_uri
            
        print(f"\n正在查询{chain_name}链上代币余额...")
        print(f"使用RPC节点: {rpc_url}")
        print(f"代币合约地址: {token_address}")
        print(f"钱包地址: {self.address}")
        
        # ERC20 ABI中的balanceOf方法
        abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            }
        ]
        
        # 创建合约实例
        token_contract = web3.eth.contract(address=web3.to_checksum_address(token_address), abi=abi)
        
        # 添加重试逻辑
        max_retries = 3
        retry_delay = 2  # 秒
        last_error = None
        
        for retry in range(max_retries):
            try:
                # 尝试获取余额
                balance = token_contract.functions.balanceOf(self.address).call()
                print(f"成功获取代币余额: {balance}")
                return balance
                
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    print(f"获取余额失败 (尝试 {retry + 1}/{max_retries}): {str(e)}")
                    print(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    print(f"获取余额失败，已达到最大重试次数: {str(e)}")
                    print("请检查:")
                    print(f"1. RPC节点状态: {rpc_url}")
                    print(f"2. 代币合约地址: {token_address}")
                    print(f"3. 钱包地址: {self.address}")
                    print(f"4. 网络连接状态: {web3.is_connected()}")
                    print(f"5. 当前链ID: {web3.eth.chain_id}")
                    
                    # 尝试获取代币信息以验证合约
                    try:
                        name = token_contract.functions.name().call()
                        symbol = token_contract.functions.symbol().call()
                        decimals = token_contract.functions.decimals().call()
                        print(f"\n代币信息验证:")
                        print(f"名称: {name}")
                        print(f"符号: {symbol}")
                        print(f"小数位: {decimals}")
                    except Exception as token_error:
                        print(f"获取代币信息失败: {str(token_error)}")
                    
                    raise ValueError(f"无法获取代币余额: {str(last_error)}")
        
        return 0
    
    def format_token_amount(self, amount: int, token_info: Dict[str, Any]) -> str:
        """
        将原始代币数量格式化为人类可读的形式
        
        Args:
            amount: 原始代币数量
            token_info: 代币信息
            
        Returns:
            str: 格式化后的代币数量
        """
        decimals = token_info.get("decimals", 18)
        formatted = amount / (10 ** decimals)
        return f"{formatted} {token_info['symbol']}"
    
    def get_token_info(self, token_symbol: Optional[str] = None, token_address: Optional[str] = None, chain: str = "polygon") -> Dict[str, Any]:
        try:
            if not token_symbol and not token_address:
                raise ValueError("必须提供代币符号或代币地址")
                
            chain_id = PolygonBridgeTokens.POLYGON_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.ETHEREUM_CHAIN_ID
            logging.info(f"查询参数: symbol={token_symbol}, address={token_address}, chain={chain}, chain_id={chain_id}")
            
            # 检查token_symbol是否实际上是一个地址（以0x开头）
            if token_symbol and token_symbol.startswith('0x') and len(token_symbol) >= 40:
                # 如果token_symbol像地址，将其视为地址处理
                token_address = token_symbol
                token_symbol = None
                print(f"检测到 --token-symbol 参数提供的是代币地址：{token_address}，自动转为使用地址查询")
            
            if token_symbol:
                token_info = PolygonBridgeTokens.get_token_by_symbol(token_symbol, chain_id)
            else:
                # 根据桥接方向选择在哪个链上查找代币
                if chain == "polygon":
                    # 如果是从以太坊到Polygon的桥接，先在以太坊链上查找
                    token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
                    if token_info:
                        return self._ensure_token_decimals(token_info, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
                    # 如果在以太坊链上找不到，再在Polygon链上查找
                    token_info = PolygonBridgeTokens.get_token_by_address(token_address, chain_id)
                else:
                    # 如果是从Polygon到以太坊的桥接，先在Polygon链上查找
                    token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
                    if token_info:
                        return self._ensure_token_decimals(token_info, PolygonBridgeTokens.POLYGON_CHAIN_ID)
                    # 如果在Polygon链上找不到，再在以太坊链上查找
                    token_info = PolygonBridgeTokens.get_token_by_address(token_address, chain_id)
                
            if not token_info:
                # 尝试在另一个链上查找
                other_chain_id = PolygonBridgeTokens.ETHEREUM_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.POLYGON_CHAIN_ID
                other_chain_name = "ethereum" if chain == "polygon" else "polygon"
                
                if token_symbol:
                    other_token_info = PolygonBridgeTokens.get_token_by_symbol(token_symbol, other_chain_id)
                else:
                    other_token_info = PolygonBridgeTokens.get_token_by_address(token_address, other_chain_id)
                    
                if other_token_info:
                    print(f"警告: 在{chain}链上找不到该代币，但在{other_chain_name}链上找到了。")
                    print(f"请使用 --chain {other_chain_name} 参数指定正确的链。")
                
                raise ValueError(f"找不到代币信息: {token_symbol or token_address}，请检查地址是否正确，或者使用 list 命令查看支持的代币")
                
            return self._ensure_token_decimals(token_info, chain_id)
            
        except Exception as e:
            logging.error(f"get_token_info 函数执行失败: {str(e)}")
            raise
    
    def _ensure_token_decimals(self, token_info: Dict[str, Any], chain_id: int) -> Dict[str, Any]:
        """
        确保token_info中有正确的decimals字段
        
        Args:
            token_info: 代币信息
            chain_id: 链ID
            
        Returns:
            Dict[str, Any]: 更新后的代币信息
        """
        if token_info.get("decimals") is not None:
            return token_info
            
        # 尝试从gate_tokens_with_decimals.json获取
        try:
            token_address = token_info["address"].lower()
            
            # 加载gate_tokens_with_decimals.json
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            json_path = os.path.join(current_dir, 'data', 'utils', 'token', 'gate_tokens_with_decimals.json')
            
            with open(json_path, 'r', encoding='utf-8') as f:
                gate_tokens = json.load(f)
                
            # 在gate_tokens中查找对应的代币
            for token in gate_tokens:
                if token.get("contract_address", "").lower() == token_address:
                    decimals = token.get("decimals")
                    if decimals is not None:
                        print(f"从gate_tokens_with_decimals.json获取到decimals: {decimals}")
                        token_info["decimals"] = decimals
                        return token_info
        except Exception as e:
            print(f"警告: 无法从gate_tokens_with_decimals.json获取decimals: {str(e)}")
        
        # 如果还是没有找到decimals，从链上获取
        try:
            web3 = self.get_web3_by_chain_id(chain_id)
            
            # 创建合约实例
            abi = [{"constant":True,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"type":"function"}]
            token_contract = web3.eth.contract(
                address=web3.to_checksum_address(token_info["address"]), 
                abi=abi
            )
            
            # 获取decimals
            decimals = token_contract.functions.decimals().call()
            print(f"从链上获取到decimals: {decimals}")
            token_info["decimals"] = decimals
            return token_info
        except Exception as e:
            print(f"警告: 无法从链上获取decimals: {str(e)}")
            print("使用默认值18")
            token_info["decimals"] = 18
            return token_info
    
    def get_balance(self, token_symbol: Optional[str] = None, token_address: Optional[str] = None, chain: str = "polygon") -> str:
        """
        获取代币余额
        
        Args:
            token_symbol: 代币符号（可选，与token_address二选一）
            token_address: 代币地址（可选，与token_symbol二选一）
            chain: 链名称（'polygon'或'ethereum'）
            
        Returns:
            str: 格式化后的代币余额
        """
        token_info = self.get_token_info(token_symbol, token_address, chain)
        chain_id = PolygonBridgeTokens.POLYGON_CHAIN_ID if chain == "polygon" else PolygonBridgeTokens.ETHEREUM_CHAIN_ID
        
        balance = self.get_token_balance(token_info["address"], chain_id)
        return self.format_token_amount(balance, token_info)
    
    async def _get_ethereum_gas_price(self) -> Optional[float]:
        """
        获取以太坊当前gas价格(单位: Gwei)
        """
        try:
            eth_w3 = self.get_web3_by_chain_id(1)
            gas_price = eth_w3.eth.gas_price
            gas_price_gwei = Web3.from_wei(gas_price, 'gwei')
            return float(gas_price_gwei)
        except Exception as e:
            logger.error(f"获取以太坊gas价格失败: {str(e)}")
            return None

    async def withdraw_from_polygon(self, token_address: str, amount: int) -> str:
        """
        从Polygon提取代币，执行burn操作
        """
        try:
            # 获取burn时的以太坊gas价格
            burn_time_gas = await self._get_ethereum_gas_price()
            if burn_time_gas is not None:
                logger.info(f"当前以太坊gas价格: {burn_time_gas} Gwei")
            
            # 获取代币信息
            token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
            if token_info is None:
                logging.error(f"无法获取代币信息，地址: {token_address}")
                raise ValueError(f"找不到代币信息: {token_address}")
            
            logging.info(f"代币信息: {token_info}")
            
            # 检查余额
            balance = self.get_token_balance(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
            if balance < amount:
                formatted_balance = self.format_token_amount(balance, token_info)
                formatted_amount = self.format_token_amount(amount, token_info)
                raise ValueError(f"余额不足，需要 {formatted_amount}，但只有 {formatted_balance}")
            
            logging.info(f"开始从Polygon提取 {self.format_token_amount(amount, token_info)} 到以太坊")
            
            # 子链上的ERC20合约ABI中的withdraw方法
            abi = [
                {
                    "inputs": [
                        {"internalType": "uint256", "name": "amount", "type": "uint256"}
                    ],
                    "name": "withdraw",
                    "outputs": [],
                    "stateMutability": "nonpayable",
                    "type": "function"
                }
            ]

            # 创建合约实例
            token_contract = self.polygon_web3.eth.contract(
                address=self.polygon_web3.to_checksum_address(token_address), 
                abi=abi
            )

            # 构建交易的基本参数
            tx_params = {
                'from': self.address,
                'nonce': self.polygon_web3.eth.get_transaction_count(self.address),
                'chainId': PolygonBridgeTokens.POLYGON_CHAIN_ID
            }

            # 估算gas限制
            try:
                estimated_gas = token_contract.functions.withdraw(amount).estimate_gas({
                    'from': self.address
                })
                # 添加20%的缓冲
                gas_limit = int(estimated_gas * 1.2)
                logging.info(f"估算gas限制: {estimated_gas}，使用{gas_limit}（含20%缓冲）")
            except Exception as e:
                # 如果估算失败，使用默认值
                gas_limit = 500000  # 默认值
                logging.warning(f"无法估算gas限制: {str(e)}，使用默认值: {gas_limit}")

            tx_params['gas'] = gas_limit

            # 获取当前区块和设置gas费用
            current_block = self.polygon_web3.eth.get_block('latest')
            base_fee = current_block['baseFeePerGas']
            priority_fee = self.polygon_web3.to_wei(250, 'gwei')  
            
            tx_params['maxFeePerGas'] = base_fee * 2 + priority_fee
            tx_params['maxPriorityFeePerGas'] = priority_fee
            
            logging.info(f"Gas费用信息:")
            logging.info(f"Base fee: {self.polygon_web3.from_wei(base_fee, 'gwei')} Gwei")
            logging.info(f"Priority fee: {self.polygon_web3.from_wei(priority_fee, 'gwei')} Gwei")
            logging.info(f"Max fee: {self.polygon_web3.from_wei(tx_params['maxFeePerGas'], 'gwei')} Gwei")

            # 构建交易
            tx = token_contract.functions.withdraw(amount).build_transaction(tx_params)

            # 签名并发送交易
            signed_tx = self.polygon_web3.eth.account.sign_transaction(tx, self.account.key)
            tx_hash = self.polygon_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            
            # 确保 tx_hash 是字符串类型
            tx_hash_str = tx_hash.hex() if hasattr(tx_hash, 'hex') else str(tx_hash)
            print(f"已发送提取交易，交易哈希: {tx_hash_str}")
            
            # 等待交易确认
            try:
                receipt = self.polygon_web3.eth.wait_for_transaction_receipt(tx_hash, timeout=60)
                if receipt.status != 1:
                    raise ValueError(f"提取交易失败，请检查交易详情: https://polygonscan.com/tx/{tx_hash_str}")
            except Exception as e:
                error_msg = str(e)
                if "Too many requests" in error_msg or "rate limit" in error_msg:
                    # 交易已发送，但因RPC限制无法确认状态
                    print(f"警告: 交易已发送，但由于RPC节点请求限制无法确认状态")
                    print(f"请手动查看交易状态: https://polygonscan.com/tx/{tx_hash_str}")
                    # 返回交易哈希，假设交易成功（可以后续验证）
                else:
                    raise
            
            print(f"代币已在Polygon上销毁，等待检查点后即可在以太坊上接收")
            print(f"注意: PoS桥一般需要3-6小时才能在以太坊上收到代币")
            
            # 记录burn时的gas价格到交易详情中
            if burn_time_gas is not None:
                tx_details = await self._get_burn_tx_details(tx_hash)
                tx_details['burn_time_gas'] = burn_time_gas
                
            return tx_hash_str
        except Exception as e:
            logger.error(f"从Polygon提取代币失败: {str(e)}")
            raise
    
    async def polygon_to_ethereum(
        self, 
        amount: str, 
        token_symbol: str = None, 
        token_address: str = None,
        wait_for_claim: bool = True,
        initial_wait_time: int = 1700,  # 28分钟默认等待时间
        max_check_time: int = 172800,   # 48小时最大检查时间
        auto_check_interval: int = 30  # 30秒检查间隔
    ):
        """
        从Polygon转移代币到以太坊
        
        Args:
            amount: 转移金额
            token_symbol: 代币符号
            token_address: 代币地址
            wait_for_claim: 是否等待并自动claim
            initial_wait_time: 初始等待时间(秒)
            max_check_time: 最大检查时间(秒)
            auto_check_interval: 自动检查间隔(秒)
            
        Returns:
            Dict: 包含交易结果的字典
        """
        try:
            # 获取代币信息
            token_info = self.get_token_info(token_symbol, token_address, chain="polygon")
            if not token_info:
                raise ValueError("无法获取代币信息")
            
            # 解析金额
            try:
                amount_wei = self._parse_amount(amount, token_info)
                logging.info(f"金额转换结果: {amount} -> {amount_wei}")
            except Exception as e:
                logging.error(f"金额转换失败: {str(e)}")
                raise
            
            # 从配置文件获取备用RPC节点
            try:
                config = load_config()
                # 首先获取主RPC节点
                main_rpc = config.get("rpc", {}).get("polygon", {}).get("rpc_url", "")
                # 获取备用RPC节点列表
                backup_rpc_nodes = config.get("rpc", {}).get("polygon", {}).get("backup_rpc_urls", [])
                # 确保主节点也在列表中
                if main_rpc and main_rpc not in backup_rpc_nodes:
                    backup_rpc_nodes.insert(0, main_rpc)
                
                if not backup_rpc_nodes:
                    raise ValueError("未配置任何可用的RPC节点，请在配置文件中添加RPC节点")
                
                print(f"已从配置文件加载 {len(backup_rpc_nodes)} 个Polygon RPC节点")
            except Exception as e:
                raise ValueError(f"无法从配置文件获取RPC节点: {str(e)}")
            
            # 执行burn操作（无重试）
            burn_tx_hash = await self.withdraw_from_polygon(token_info["address"], amount_wei)
            if not burn_tx_hash:
                raise ValueError("Burn操作失败")

            print(f"\nBurn交易已发送: {burn_tx_hash}")
            print("请等待交易确认...")
            
            # 等待burn交易确认
            try:
                tx_receipt = self.polygon_web3.eth.wait_for_transaction_receipt(burn_tx_hash)
                if tx_receipt["status"] != 1:
                    raise ValueError("Burn交易执行失败")
                    
                print(f"Burn交易已确认！")
                print(f"交易详情: https://polygonscan.com/tx/{burn_tx_hash}")
            except Exception as e:
                raise ValueError(f"等待交易确认时出错: {str(e)}")
            
            if not wait_for_claim:
                return {
                    "success": True,
                    "burn_tx_hash": burn_tx_hash,
                    "message": "Burn交易已确认，请稍后手动claim"
                }
            
            print(f"\n请等待 {initial_wait_time} 秒后开始尝试claim...")
            await asyncio.sleep(initial_wait_time)
            
            # 尝试claim，带重试机制
            start_time = time.time()
            while time.time() - start_time < max_check_time:
                try:
                    # 获取burn交易的详细信息
                    tx_details = await self._get_burn_tx_details(burn_tx_hash)
                    if not tx_details:
                        print(f"无法获取burn交易详情，{auto_check_interval}秒后重试...")
                        await asyncio.sleep(auto_check_interval)
                        continue
                    
                    # 尝试claim
                    max_retries = 1
                    for retry in range(max_retries):
                        try:
                            claim_tx = await self._execute_claim_on_ethereum(burn_tx_hash, tx_details)
                            if claim_tx:
                                print(f"\nClaim交易已发送: {claim_tx}")
                                print(f"交易详情: https://etherscan.io/tx/{claim_tx}")
                                return {
                                    "success": True,
                                    "burn_tx_hash": burn_tx_hash,
                                    "claim_tx_hash": claim_tx
                                }
                        except Exception as e:
                            error_msg = str(e)
                            if "Too many requests" in error_msg or "rate limit" in error_msg:
                                if retry < max_retries - 1:
                                    wait_time = 10 * (2 ** retry)
                                    print(f"遇到RPC请求限制，等待 {wait_time} 秒后重试...")
                                    
                                    # 切换到备用以太坊RPC节点
                                    config = load_config()
                                    backup_eth_nodes = config.get("rpc", {}).get("ethereum", {}).get("backup_rpc_urls", [])
                                    if retry < len(backup_eth_nodes):
                                        old_url = self.ethereum_web3.provider.endpoint_uri
                                        new_url = backup_eth_nodes[retry]
                                        print(f"切换RPC节点: {old_url} -> {new_url}")
                                        self.ethereum_web3 = Web3(Web3.HTTPProvider(new_url))
                                    
                                    await asyncio.sleep(wait_time)
                                    continue
                            elif "checkpoint" in str(e).lower():
                                print(f"交易尚未被包含在检查点中，{auto_check_interval}秒后重试...")
                                break  # 跳出重试循环，等待下一次检查
                            else:
                                print(f"Claim失败: {str(e)}")
                                raise
                    
                    await asyncio.sleep(auto_check_interval)
                    
                except Exception as e:
                    if "checkpoint" not in str(e).lower():
                        raise
                    await asyncio.sleep(auto_check_interval)
            
            print("\n超过最大等待时间，请稍后手动执行claim操作")
            print(f"使用命令: python -m src.bridge.pol_bridge.bridge_tokens claim --tx-hash {burn_tx_hash}")
            
            return {
                "success": True,
                "burn_tx_hash": burn_tx_hash,
                "message": "Burn交易已确认，请稍后手动claim"
            }
            
        except Exception as e:
            logging.error(f"从Polygon转移到以太坊时出错: {str(e)}")
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _get_burn_tx_details(self, burn_tx_hash: str) -> dict:
        """获取Polygon上burn交易的详细信息"""
        try:
            # 获取交易信息
            max_retries = 5
            base_delay = 2  # 秒
            tx = None
            receipt = None
            
            # 重试获取交易信息
            for retry in range(max_retries):
                try:
                    tx = self.polygon_web3.eth.get_transaction(burn_tx_hash)
                    break
                except Exception as e:
                    error_msg = str(e)
                    if "Too many requests" in error_msg or "rate limit" in error_msg:
                        wait_time = base_delay * (2 ** retry)
                        print(f"获取交易信息时遇到请求限制，等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        print(f"获取交易信息时出错: {error_msg}")
                        if retry == max_retries - 1:
                            raise  # 最后一次重试失败，抛出异常
            
            # 重试获取交易收据
            for retry in range(max_retries):
                try:
                    receipt = self.polygon_web3.eth.get_transaction_receipt(burn_tx_hash)
                    break
                except Exception as e:
                    error_msg = str(e)
                    if "Too many requests" in error_msg or "rate limit" in error_msg:
                        wait_time = base_delay * (2 ** retry)
                        print(f"获取交易收据时遇到请求限制，等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        print(f"获取交易收据时出错: {error_msg}")
                        if retry == max_retries - 1:
                            raise  # 最后一次重试失败，抛出异常
            
            # 检查是否成功获取交易信息和收据
            if tx is None or receipt is None:
                print(f"无法获取交易 {burn_tx_hash} 的信息或收据，可能是请求频率受限")
                return {}
            
            # 解析交易输入数据，找出代币地址和金额
            token_address = receipt['to']  # 通常是代币合约地址
            
            print(f"已找到交易 {burn_tx_hash} 的收据:")
            print(f"代币合约地址: {token_address}")
            
            # 获取代币信息 - 添加更多错误处理
            try:
                token_info = PolygonBridgeTokens.get_token_by_address(token_address, PolygonBridgeTokens.POLYGON_CHAIN_ID)
                if not token_info:
                    print(f"警告: 无法在Polygon上找到代币信息，尝试通过ERC20接口获取基本信息")
                    
                    # 通过ERC20接口获取基本信息
                    erc20_abi = [
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "name",
                            "outputs": [{"name": "", "type": "string"}],
                            "type": "function"
                        },
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "symbol",
                            "outputs": [{"name": "", "type": "string"}],
                            "type": "function"
                        },
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "decimals",
                            "outputs": [{"name": "", "type": "uint8"}],
                            "type": "function"
                        }
                    ]
                    
                    try:
                        token_contract = self.polygon_web3.eth.contract(
                            address=self.polygon_web3.to_checksum_address(token_address),
                            abi=erc20_abi
                        )
                        
                        # 重试获取代币信息
                        token_name = None
                        token_symbol = None
                        token_decimals = None
                        
                        for retry in range(max_retries):
                            try:
                                token_name = token_contract.functions.name().call()
                                break
                            except Exception as e:
                                if "Too many requests" in str(e) or "rate limit" in str(e):
                                    wait_time = base_delay * (2 ** retry)
                                    print(f"获取代币名称时遇到请求限制，等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                else:
                                    print(f"获取代币名称时出错: {str(e)}")
                                    if retry == max_retries - 1:
                                        token_name = "Unknown Token"
                        
                        for retry in range(max_retries):
                            try:
                                token_symbol = token_contract.functions.symbol().call()
                                break
                            except Exception as e:
                                if "Too many requests" in str(e) or "rate limit" in str(e):
                                    wait_time = base_delay * (2 ** retry)
                                    print(f"获取代币符号时遇到请求限制，等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                else:
                                    print(f"获取代币符号时出错: {str(e)}")
                                    if retry == max_retries - 1:
                                        token_symbol = "UNKNOWN"
                        
                        for retry in range(max_retries):
                            try:
                                token_decimals = token_contract.functions.decimals().call()
                                break
                            except Exception as e:
                                if "Too many requests" in str(e) or "rate limit" in str(e):
                                    wait_time = base_delay * (2 ** retry)
                                    print(f"获取代币精度时遇到请求限制，等待 {wait_time} 秒后重试...")
                                    await asyncio.sleep(wait_time)
                                else:
                                    print(f"获取代币精度时出错: {str(e)}")
                                    if retry == max_retries - 1:
                                        token_decimals = 18
                        
                        # 创建一个基本的代币信息对象
                        token_info = {
                            "address": token_address,
                            "name": token_name or "Unknown Token",
                            "symbol": token_symbol or "UNKNOWN",
                            "decimals": token_decimals or 18,
                            "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                        }
                        print(f"已通过ERC20接口获取代币信息: {token_symbol} ({token_name})")
                    except Exception as e:
                        print(f"无法通过ERC20接口读取代币信息: {str(e)}")
                        # 创建一个占位符代币信息
                        token_info = {
                            "address": token_address,
                            "name": "Unknown Token",
                            "symbol": "UNKNOWN",
                            "decimals": 18,  # 默认18位精度
                            "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                        }
            except Exception as e:
                print(f"获取Polygon上的代币信息时出错: {str(e)}")
                # 创建一个占位符代币信息
                token_info = {
                    "address": token_address,
                    "name": "Unknown Token",
                    "symbol": "UNKNOWN",
                    "decimals": 18,  # 默认18位精度
                    "chainId": PolygonBridgeTokens.POLYGON_CHAIN_ID
                }
            
            print(f"Polygon代币信息: {token_info.get('symbol')} ({token_info.get('name')})")
            
            # 获取对应的以太坊代币 - 添加错误处理
            try:
                eth_token_info = PolygonBridgeTokens.get_equivalent_token(
                    token_address, 
                    PolygonBridgeTokens.POLYGON_CHAIN_ID, 
                    PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                )
                
                if not eth_token_info:
                    print(f"警告: 在以太坊上找不到对应的代币映射，使用相同信息")
                    # 使用相同的信息，但更改chainId
                    eth_token_info = token_info.copy()
                    eth_token_info["chainId"] = PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                    # 移除可能包含"PoS"的名称
                    if "name" in eth_token_info and " (PoS)" in eth_token_info["name"]:
                        eth_token_info["name"] = eth_token_info["name"].replace(" (PoS)", "")
            except Exception as e:
                print(f"获取以太坊上对应代币时出错: {str(e)}")
                # 创建一个占位符代币信息
                eth_token_info = token_info.copy()
                eth_token_info["chainId"] = PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                if "name" in eth_token_info and " (PoS)" in eth_token_info["name"]:
                    eth_token_info["name"] = eth_token_info["name"].replace(" (PoS)", "")
                
            print(f"以太坊代币信息: {eth_token_info.get('symbol')} ({eth_token_info.get('name')})")
            
            return {
                "token_address": token_address,
                "polygon_token_info": token_info,
                "ethereum_token_info": eth_token_info,
                "receipt": receipt,
                "burn_time_gas": self.polygon_web3.from_wei(tx.get('gasPrice', 0), 'gwei')
            }
            
        except Exception as e:
            print(f"获取burn交易详情时出错: {str(e)}")
            return {}
    
    async def _execute_claim_on_ethereum(self, burn_tx_hash: str, tx_details: dict) -> Optional[str]:
        """
        在以太坊上执行claim操作
        """
        burn_time_gas = tx_details.get('burn_time_gas')
        start_time = time.time()
        
        while time.time() - start_time < 172800:  # 48小时最大检查时间
            try:
                # 获取proof证明
                try:
                    proof = await self._get_exit_proof(burn_tx_hash)
                    if not proof:
                        logger.warning("获取proof证明失败，30秒后重试...")
                        await asyncio.sleep(30)
                        continue
                except Exception as e:
                    logger.warning(f"获取proof证明失败: {str(e)}，30秒后重试...")
                    await asyncio.sleep(30)
                    continue

                # 获取当前以太坊gas价格
                current_gas = await self._get_ethereum_gas_price()
                if current_gas is None:
                    logger.warning("获取当前gas价格失败，30秒后重试...")
                    await asyncio.sleep(30)
                    continue
                
                logger.info(f"当前以太坊gas价格: {current_gas} Gwei")
                
                # 检查gas价格条件
                can_execute = False
                if current_gas <= 2.5:
                    can_execute = True
                    logger.info("当前gas价格低于2.5 Gwei，可以执行claim")
                elif burn_time_gas is not None:
                    max_allowed_gas = burn_time_gas * 2
                    if current_gas < max_allowed_gas:
                        can_execute = True
                        logger.info(f"当前gas价格 {current_gas} 低于burn时gas价格的2倍 {max_allowed_gas}，可以执行claim")
                    else:
                        logger.info(f"当前gas价格 {current_gas} 高于burn时gas价格的2倍 {max_allowed_gas}，等待gas降低")
                
                if not can_execute:
                    logger.info("gas价格条件不满足，30秒后重试...")
                    await asyncio.sleep(30)
                    continue
                
                # 从配置文件获取备用RPC节点
                try:
                    config = load_config()
                    # 首先获取主RPC节点
                    main_rpc = config.get("rpc", {}).get("ethereum", {}).get("rpc_url", "")
                    # 获取备用RPC节点列表
                    backup_rpc_nodes = config.get("rpc", {}).get("ethereum", {}).get("backup_rpc_urls", [])
                    # 确保主节点也在列表中
                    if main_rpc and main_rpc not in backup_rpc_nodes:
                        backup_rpc_nodes.insert(0, main_rpc)
                    
                    if not backup_rpc_nodes:
                        raise ValueError("未配置任何可用的RPC节点，请在配置文件中添加RPC节点")
                    
                    print(f"已从配置文件加载 {len(backup_rpc_nodes)} 个以太坊RPC节点")
                except Exception as e:
                    raise ValueError(f"无法从配置文件获取RPC节点: {str(e)}")

                # 打印钱包地址和余额信息进行调试
                eth_balance = self.ethereum_web3.eth.get_balance(self.address)
                eth_balance_in_ether = self.ethereum_web3.from_wei(eth_balance, 'ether')
                print(f"\n以太坊钱包信息:")
                print(f"地址: {self.address}")
                print(f"余额: {eth_balance_in_ether} ETH")
                
                if eth_balance < self.ethereum_web3.to_wei(0.01, 'ether'):
                    print(f"警告: 以太坊余额可能不足以支付gas费用，建议至少有0.01 ETH")
                
                # RootChainManager的ABI
                root_chain_manager_abi = [
                    {
                        "inputs": [
                            {"internalType": "bytes", "name": "inputData", "type": "bytes"}
                        ],
                        "name": "exit",
                        "outputs": [],
                        "stateMutability": "nonpayable",
                        "type": "function"
                    }
                ]
                
                # 创建RootChainManager合约实例
                root_chain_manager = self.ethereum_web3.eth.contract(
                    address=self.ethereum_web3.to_checksum_address(self.POS_ROOT_CHAIN_MANAGER),
                    abi=root_chain_manager_abi
                )
                
                # 获取交易收据
                receipt = tx_details.get("receipt")
                if not receipt or 'logs' not in receipt:
                    raise ValueError("无法获取交易日志")
                
                # 从Polygon网络获取proof证明，带重试机制
                max_retries = 3
                proof_data = None
                for retry in range(max_retries):
                    try:
                        proof_data = await self._get_exit_proof(burn_tx_hash)
                        if proof_data:
                            break
                    except Exception as e:
                        error_msg = str(e)
                        if "Too many requests" in error_msg or "rate limit" in error_msg:
                            if retry < max_retries - 1:
                                wait_time = 10 * (2 ** retry)
                                print(f"遇到RPC请求限制，等待 {wait_time} 秒后重试...")
                                
                                # 切换到备用RPC节点
                                if retry < len(backup_rpc_nodes):
                                    old_url = self.ethereum_web3.provider.endpoint_uri
                                    new_url = backup_rpc_nodes[retry]
                                    print(f"切换RPC节点: {old_url} -> {new_url}")
                                    self.ethereum_web3 = Web3(Web3.HTTPProvider(new_url))
                                
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                print("多次尝试后仍然遇到请求限制，30秒后重试...")
                                await asyncio.sleep(30)
                                break
                        else:
                            print(f"获取proof失败: {error_msg}，30秒后重试...")
                            await asyncio.sleep(30)
                            break

                if not proof_data:
                    print("无法获取exit proof证明，30秒后重试...")
                    await asyncio.sleep(30)
                    continue
                
                # 获取当前gas价格
                gas_price = self.ethereum_web3.eth.gas_price
                # 使用当前gas价格的1.1倍，确保交易能够被处理
                suggested_gas_price = int(gas_price * 1.1)
                print(f"当前以太坊gas价格: {self.ethereum_web3.from_wei(gas_price, 'gwei')} Gwei")
                print(f"建议gas价格: {self.ethereum_web3.from_wei(suggested_gas_price, 'gwei')} Gwei")
                
                # 估算gas限制
                try:
                    estimated_gas = root_chain_manager.functions.exit(proof_data).estimate_gas({'from': self.address})
                    # 添加20%的缓冲
                    gas_limit = int(estimated_gas * 1.2)
                    print(f"估算gas限制: {estimated_gas}，使用{gas_limit}（含20%缓冲）")
                except Exception as e:
                    print(f"无法估算gas限制: {str(e)}，使用默认值: {self.gas_limit * 2}")
                    gas_limit = self.gas_limit * 2
                
                # 获取当前nonce
                nonce = self.ethereum_web3.eth.get_transaction_count(self.address)
                print(f"当前nonce: {nonce}")
                
                # 构建交易
                tx_params = {
                    'from': self.address,
                    'gas': gas_limit,
                    'nonce': nonce,
                    'chainId': PolygonBridgeTokens.ETHEREUM_CHAIN_ID
                }
                
                # 根据网络条件设置gas价格
                current_block = self.ethereum_web3.eth.get_block('latest')
                if 'baseFeePerGas' in current_block:
                    # EIP-1559交易
                    print("使用EIP-1559交易类型")
                    base_fee = current_block['baseFeePerGas']
                    priority_fee = self.ethereum_web3.to_wei(0.1888, 'gwei')  # 固定priority fee为0.1888 gwei
                    
                    tx_params['maxFeePerGas'] = base_fee * 2 + priority_fee
                    tx_params['maxPriorityFeePerGas'] = priority_fee
                    
                    print(f"Base fee: {self.ethereum_web3.from_wei(base_fee, 'gwei')} Gwei")
                    print(f"Priority fee: {self.ethereum_web3.from_wei(priority_fee, 'gwei')} Gwei")
                    print(f"Max fee: {self.ethereum_web3.from_wei(tx_params['maxFeePerGas'], 'gwei')} Gwei")
                else:
                    # 传统交易
                    print("使用传统交易类型")
                    tx_params['gasPrice'] = suggested_gas_price
                    print(f"Gas价格: {self.ethereum_web3.from_wei(suggested_gas_price, 'gwei')} Gwei")
                
                # 使用proof_data构建exit交易
                tx = root_chain_manager.functions.exit(proof_data).build_transaction(tx_params)
                
                # 计算交易费用
                if 'maxFeePerGas' in tx:
                    max_tx_fee = tx['gas'] * tx['maxFeePerGas']
                else:
                    max_tx_fee = tx['gas'] * tx['gasPrice']
                    
                max_tx_fee_ether = self.ethereum_web3.from_wei(max_tx_fee, 'ether')
                print(f"最大可能交易费用: {max_tx_fee_ether} ETH")
                
                if eth_balance < max_tx_fee:
                    print(f"严重警告: 钱包余额 ({eth_balance_in_ether} ETH) 不足以支付最大可能的交易费用 ({max_tx_fee_ether} ETH)")
                    print("建议先向钱包充值ETH")
                
                # 签名并发送交易，带重试机制
                for retry in range(max_retries):
                    try:
                        # 签名交易
                        print("正在签名交易...")
                        signed_tx = self.ethereum_web3.eth.account.sign_transaction(tx, self.account.key)
                        
                        # 发送交易
                        print("正在发送交易...")
                        tx_hash = self.ethereum_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        
                        # 等待交易确认
                        print(f"交易已发送，等待确认...")
                        print(f"交易哈希: {tx_hash.hex()}")
                        print(f"交易详情: https://etherscan.io/tx/{tx_hash.hex()}")
                        
                        # 等待交易确认
                        tx_receipt = self.ethereum_web3.eth.wait_for_transaction_receipt(tx_hash)
                        if tx_receipt["status"] == 1:
                            print("Claim交易已确认！")
                            return tx_hash.hex()
                        else:
                            raise ValueError("Claim交易执行失败")
                            
                    except Exception as e:
                        error_msg = str(e)
                        if "Too many requests" in error_msg or "rate limit" in error_msg:
                            if retry < max_retries - 1:
                                wait_time = 10 * (2 ** retry)
                                print(f"遇到RPC请求限制，等待 {wait_time} 秒后重试...")
                                
                                # 切换到备用RPC节点
                                if retry < len(backup_rpc_nodes):
                                    old_url = self.ethereum_web3.provider.endpoint_uri
                                    new_url = backup_rpc_nodes[retry]
                                    print(f"切换RPC节点: {old_url} -> {new_url}")
                                    self.ethereum_web3 = Web3(Web3.HTTPProvider(new_url))
                                
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                print("多次尝试后仍然遇到请求限制，30秒后重试...")
                                await asyncio.sleep(30)
                                break
                        else:
                            print(f"发送交易失败: {error_msg}，30秒后重试...")
                            await asyncio.sleep(30)
                            break
                
            except Exception as e:
                error_msg = str(e)
                if "checkpoint" in error_msg.lower():
                    print(f"交易尚未被包含在检查点中，30秒后重试...")
                    await asyncio.sleep(30)
                    continue
                else:
                    print(f"执行claim操作时出错: {error_msg}，30秒后重试...")
                    await asyncio.sleep(30)
                    continue
        
        print("\n超过最大等待时间（48小时），请稍后手动执行claim操作")
        print(f"使用命令: python -m src.bridge.pol_bridge.bridge_tokens claim --tx-hash {burn_tx_hash}")
        return None

    def list_tokens(self) -> Dict[str, Dict[int, Any]]:
        """
        列出可桥接的代币
        
        Returns:
            Dict[str, Dict[int, Any]]: 可桥接的代币列表
        """
        print("使用官方Polygon PoS桥提供桥接服务")
        return PolygonBridgeTokens.get_common_tokens()

    async def approve_token(self, token_address: str, spender_address: str, amount: int, chain_id: int = None) -> Optional[str]:
        """
        授权智能合约使用代币
        
        Args:
            token_address: 代币合约地址
            spender_address: 要授权的智能合约地址
            amount: 授权金额
            chain_id: 链ID，如果不提供则使用当前链
            
        Returns:
            str: 授权交易的哈希，如果已经授权则返回None
        """
        if chain_id is None:
            chain_id = self.chain_id
            
        # 获取web3实例
        web3 = self.get_web3_by_chain_id(chain_id)
        if web3 is None:
            raise ValueError(f"未找到链ID {chain_id} 的web3实例")
            
        # 检查地址格式
        if not web3.is_address(token_address) or not web3.is_address(spender_address):
            raise ValueError(f"无效的地址: token={token_address}, spender={spender_address}")
            
        # 格式化地址
        formatted_token = web3.to_checksum_address(token_address)
        formatted_spender = web3.to_checksum_address(spender_address)
        
        # 创建合约实例
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}],
                "name": "allowance",
                "outputs": [{"name": "", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": False,
                "inputs": [{"name": "_spender", "type": "address"}, {"name": "_value", "type": "uint256"}],
                "name": "approve",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "symbol",
                "outputs": [{"name": "", "type": "string"}],
                "type": "function"
            }
        ]
        
        token_contract = web3.eth.contract(
            address=formatted_token,
            abi=erc20_abi
        )
        
        # 查询当前授权金额
        allowance = await self.get_token_allowance(token_address, spender_address, chain_id)
        
        if allowance >= amount:
            print(f"授权额度已足够: {allowance} >= {amount}")
            return None
            
        # 如果授权额度不足，则授权
        # 使用最大值授权，避免未来再次授权
        max_amount = 2**256 - 1
        
        # 查询代币符号
        try:
            token_symbol = token_contract.functions.symbol().call()
        except Exception:
            token_symbol = "未知代币"
        
        print(f"当前授权额度不足，准备增加对 {token_symbol} 的授权")
        
        # 判断是以太坊链还是Polygon链
        chain_name = "以太坊" if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else "Polygon"
        
        # 获取当前区块
        current_block = web3.eth.get_block('latest')
        base_fee = current_block['baseFeePerGas']
        
        # 根据链设置不同的priority fee
        if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID:
            priority_fee = web3.to_wei(0.1888, 'gwei')  # 以太坊链使用0.1888 gwei
        else:
            priority_fee = web3.to_wei(250, 'gwei')  
        
        # 准备交易参数
        tx_params = {
            'from': self.address,
            'nonce': web3.eth.get_transaction_count(self.address),
            'chainId': chain_id,
            'maxFeePerGas': base_fee * 2 + priority_fee,
            'maxPriorityFeePerGas': priority_fee
        }
        
        print(f"链: {chain_name}")
        print(f"Base fee: {web3.from_wei(base_fee, 'gwei')} Gwei")
        print(f"Priority fee: {web3.from_wei(priority_fee, 'gwei')} Gwei")
        print(f"Max fee: {web3.from_wei(tx_params['maxFeePerGas'], 'gwei')} Gwei")
        
        # 构建approve函数调用
        build_tx = token_contract.functions.approve(
            formatted_spender,
            max_amount
        ).build_transaction(tx_params)
        
        # 估算gas
        try:
            estimated_gas = web3.eth.estimate_gas(build_tx)
            print(f"估算授权交易需要的gas: {estimated_gas}")
            tx_params['gas'] = int(estimated_gas * 1.2)  # 增加20%的buffer
            print(f"设置gas限制为: {tx_params['gas']} (增加20%的buffer)")
        except Exception as e:
            print(f"估算gas失败，使用默认值: {str(e)}")
            tx_params['gas'] = 100000  # 授权操作的默认gas限制
            print(f"使用默认gas限制: {tx_params['gas']}")
        
        try:
            # 重新构建交易
            tx = token_contract.functions.approve(
                formatted_spender,
                max_amount
            ).build_transaction(tx_params)
            
            # 签名并发送交易
            signed_tx = web3.eth.account.sign_transaction(tx, self.account.key)
            tx_hash = web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            
            print(f"已发送授权交易，交易哈希: {tx_hash.hex()}")
            
            # 等待交易确认
            receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
            if receipt.status != 1:
                explorer_url = "https://etherscan.io/tx/" if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else "https://polygonscan.com/tx/"
                raise ValueError(f"授权交易失败，请检查交易详情: {explorer_url}{tx_hash.hex()}")
            
            print(f"授权成功，已授权 {spender_address} 使用最大数量的 {token_symbol}")
            
            return tx_hash.hex()
        except Exception as e:
            error_msg = str(e)
            if "insufficient funds" in error_msg:
                native_token = "ETH" if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID else "MATIC"
                native_balance = web3.eth.get_balance(self.address)
                native_balance_ether = web3.from_wei(native_balance, 'ether')
                gas_price = tx_params.get('gasPrice', web3.eth.gas_price)
                estimated_cost = web3.from_wei(tx_params.get('gas', 100000) * gas_price, 'ether')
                
                print(f"错误: {native_token}余额不足以支付授权交易费用")
                print(f"当前余额: {native_balance_ether:.8f} {native_token}")
                print(f"预估需要: {estimated_cost:.8f} {native_token}")
                print("请充值足够的" + native_token + "后重试")
            raise ValueError(f"授权交易失败: {error_msg}")
    
    async def _get_exit_proof(self, burn_tx_hash: str) -> Optional[bytes]:
        """获取exit proof证明"""
        try:
            print("正在从Polygon API获取exit proof...")
            
            # 从配置文件获取代理设置
            config = load_config()
            proxy_config = config.get("proxy", {})
            proxy_url = proxy_config.get("proxy_list", [""])[0]  # 获取第一个代理
            if not proxy_url:
                print("代理功能未启用")
            
            # 构建API请求URL
            api_url = f"https://proof-generator.polygon.technology/api/v1/matic/exit-payload/{burn_tx_hash}?eventSignature=0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
            
            max_retries = 3
            base_delay = 5  # 基础延迟时间（秒）
            
            # 创建支持SOCKS5的TCP连接器
            connector = aiohttp.TCPConnector(ssl=False)
            if proxy_url:
                print(f"使用代理: {proxy_url}")
                connector = aiohttp.TCPConnector(ssl=False, force_close=True)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                for attempt in range(max_retries):
                    try:
                        print(f"尝试API端点 {'(使用代理)' if proxy_url else ''}: {api_url} (尝试 {attempt + 1}/{max_retries})")
                        
                        # 设置超时时间
                        timeout = aiohttp.ClientTimeout(total=30)
                        
                        # 发送请求
                        async with session.get(
                            api_url,
                            timeout=timeout,
                            proxy=proxy_url if proxy_url else None,
                            ssl=False  # 禁用SSL验证以避免某些代理问题
                        ) as response:
                            if response.status == 200:
                                data = await response.json()
                                if "result" in data:
                                    # 将十六进制字符串转换为bytes
                                    proof = bytes.fromhex(data["result"].replace("0x", ""))
                                    print("成功获取proof证明")
                                    return proof
                                else:
                                    print(f"API返回数据格式错误: {data}")
                            elif response.status == 400:
                                error_data = await response.json()
                                error_message = error_data.get("message", "未知错误")
                                if "checkpoint" in error_message.lower():
                                    raise ValueError(f"交易尚未被包含在checkpoint中: {error_message}")
                                else:
                                    print(f"API返回400错误: {error_message}")
                            else:
                                print(f"API返回非200状态码: {response.status}")
                                if attempt < max_retries - 1:
                                    print(f"将在下次尝试时切换到下一个代理")
                    
                    except asyncio.TimeoutError:
                        print(f"请求超时，等待{base_delay * (attempt + 1)}秒后重试")
                        print("将在下次尝试时切换到下一个代理")
                    except Exception as e:
                        print(f"获取proof失败，等待{base_delay * (attempt + 1)}秒后重试: {str(e)}")
                        if "Too many requests" in str(e) or "rate limit" in str(e).lower():
                            print("遇到请求限制，将在下次尝试时切换到下一个代理")
                    
                    if attempt < max_retries - 1:
                        await asyncio.sleep(base_delay * (attempt + 1))
            
            raise ValueError("无法获取exit proof，请稍后重试")
            
        except Exception as e:
            logging.error(f"获取exit proof时出错: {str(e)}")
            raise ValueError(f"无法获取exit proof: {str(e)}")

    def _get_mapping_from_json(self, token_address: str, source_chain_id: int) -> Optional[str]:
        """
        从tokens.json文件中查询代币映射地址
        
        Args:
            token_address: 源代币地址
            source_chain_id: 源链ID (1: ethereum, 137: polygon)
            
        Returns:
            Optional[str]: 目标链上的映射地址，如果未找到则返回None
        """
        try:
            # 获取tokens.json文件路径
            json_path = os.path.join(os.path.dirname(__file__), "data", "tokens.json")
            if not os.path.exists(json_path):
                print(f"警告: 找不到tokens.json文件: {json_path}")
                return None
                
            # 读取映射数据
            with open(json_path, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
                
            # 转换地址为小写
            token_address = token_address.lower()
            source_chain_id = str(source_chain_id)
            target_chain_id = "137" if source_chain_id == "1" else "1"
            
            # 遍历所有代币映射
            for symbol, chain_data in mappings.items():
                if source_chain_id in chain_data:
                    # 检查源链地址是否匹配
                    if chain_data[source_chain_id].get("address", "").lower() == token_address:
                        # 如果找到匹配的源地址，返回目标链地址
                        if target_chain_id in chain_data:
                            target_address = chain_data[target_chain_id].get("address")
                            if target_address:
                                return target_address.lower()
            
            return None
            
        except Exception as e:
            print(f"从tokens.json获取映射地址时出错: {str(e)}")
            return None
    
    def get_root_token(self, token_address: str) -> Optional[str]:
        """获取Polygon代币对应的以太坊代币地址"""
        try:
            # 首先从tokens.json查询
            mapped_address = self._get_mapping_from_json(token_address, 137)  # 从Polygon查询以太坊地址
            if mapped_address:
                return mapped_address
                
            # 如果在json中未找到，则使用合约查询
            web3_polygon = self.get_web3_by_chain_id(137)
            contract = web3_polygon.eth.contract(
                address=web3_polygon.to_checksum_address(self.ROOT_CHAIN_MANAGER),
                abi=self.ROOT_CHAIN_MANAGER_ABI
            )
            
            root_token = contract.functions.childToRootToken(
                web3_polygon.to_checksum_address(token_address)
            ).call()
            
            return root_token if root_token != "******************************************" else None
            
        except Exception as e:
            print(f"获取根代币地址时出错: {str(e)}")
            return None
    
    def get_polygon_mapped_address(self, ethereum_token_address: str) -> Optional[str]:
        """获取以太坊代币在Polygon上的映射地址"""
        try:
            # 首先从tokens.json查询
            mapped_address = self._get_mapping_from_json(ethereum_token_address, 1)  # 从以太坊查询Polygon地址
            if mapped_address:
                return mapped_address
                
            # 如果在json中未找到，则使用合约查询
            web3_eth = self.get_web3_by_chain_id(1)
            contract = web3_eth.eth.contract(
                address=web3_eth.to_checksum_address(self.ROOT_CHAIN_MANAGER),
                abi=self.ROOT_CHAIN_MANAGER_ABI
            )
            
            child_token = contract.functions.rootToChildToken(
                web3_eth.to_checksum_address(ethereum_token_address)
            ).call()
            
            return child_token if child_token != "******************************************" else None
            
        except Exception as e:
            print(f"获取Polygon映射地址时出错: {str(e)}")
            return None
    
    def check_token_mapping(self, token_address: str, chain_id: int) -> Optional[Dict[str, Any]]:
        """
        检查代币的映射关系
        
        Args:
            token_address: 代币地址
            chain_id: 链ID (1: ethereum, 137: polygon)
            
        Returns:
            Optional[Dict[str, Any]]: 包含映射信息的字典
        """
        try:
            # 首先从tokens.json查询
            mapped_address = self._get_mapping_from_json(token_address, chain_id)
            if mapped_address:
                return {
                    "source_address": token_address,
                    "target_address": mapped_address,
                    "source_chain_id": chain_id,
                    "target_chain_id": 137 if chain_id == 1 else 1,
                    "method": "json"
                }
            
            # 如果在json中未找到，则使用合约查询
            if chain_id == 1:
                # 以太坊 -> Polygon
                mapped_address = self.get_polygon_mapped_address(token_address)
                target_chain_id = 137
            else:
                # Polygon -> 以太坊
                mapped_address = self.get_root_token(token_address)
                target_chain_id = 1
                
            if mapped_address:
                return {
                    "source_address": token_address,
                    "target_address": mapped_address,
                    "source_chain_id": chain_id,
                    "target_chain_id": target_chain_id,
                    "method": "contract"
                }
                
            return None
            
        except Exception as e:
            print(f"检查代币映射时出错: {str(e)}")
            return None
    
    def encode_deposit_data(self, amount: int, token_type: str = "ERC20") -> bytes:
        """
        编码存款数据
        
        Args:
            amount: 存款金额
            token_type: 代币类型 ('ERC20', 'ERC721', 'ETHER')
            
        Returns:
            bytes: 编码后的存款数据
        """
        if token_type == "ERC20":
            # 使用新的编码方式替代旧的encode_single
            return Web3.to_bytes(hexstr=Web3.to_hex(Web3.to_int(amount).to_bytes(32, byteorder='big')))
        elif token_type == "ERC721":
            # 对于ERC721代币，使用新的编码方式
            return Web3.to_bytes(hexstr=Web3.to_hex(Web3.to_int(amount).to_bytes(32, byteorder='big')))
        elif token_type == "ETHER":
            # 对于ETH，也使用新的编码方式
            return Web3.to_bytes(hexstr=Web3.to_hex(Web3.to_int(amount).to_bytes(32, byteorder='big')))
        else:
            raise ValueError(f"不支持的代币类型: {token_type}")

    def _safe_call_token_function(self, contract, function_name, default_value):
        """
        安全调用代币合约函数，处理可能的错误
        """
        try:
            return getattr(contract.functions, function_name)().call()
        except Exception as e:
            print(f"调用 {function_name} 失败: {str(e)}")
            return default_value

    async def direct_bridge_to_polygon(self, token_address: str, amount: Union[int, float, Decimal], receiver: Optional[str] = None) -> str:
        """
        直接将代币从以太坊桥接到Polygon，不检查代币列表
        
        Args:
            token_address: 以太坊上的代币合约地址
            amount: 桥接金额（可以是小数，会自动转换为代币最小单位）
            receiver: 接收地址（默认为发送地址）
            
        Returns:
            str: 桥接交易的哈希
        """
        print(f"使用直接桥接模式转移代币 {token_address}")
        
        # 检查地址格式
        if not Web3.is_address(token_address):
            raise ValueError(f"无效的代币地址: {token_address}")
        
        # 标准化地址格式
        formatted_token = self.ethereum_web3.to_checksum_address(token_address)
        
        # 获取代币信息（直接从链上查询）
        try:
            # 使用ERC20 ABI查询代币信息
            erc20_abi = [
                {
                    "constant": True,
                    "inputs": [],
                    "name": "name",
                    "outputs": [{"name": "", "type": "string"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "symbol",
                    "outputs": [{"name": "", "type": "string"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }
            ]
            
            token_contract = self.ethereum_web3.eth.contract(
                address=formatted_token,
                abi=erc20_abi
            )
            
            # 使用安全调用函数获取代币信息
            token_name = self._safe_call_token_function(token_contract, 'name', 'Unknown Token')
            token_symbol = self._safe_call_token_function(token_contract, 'symbol', 'UNKNOWN')
            token_decimals = self._safe_call_token_function(token_contract, 'decimals', 18)
            token_balance = token_contract.functions.balanceOf(self.address).call()  # 修复这里的调用
            
            print(f"已获取代币信息:")
            print(f"名称: {token_name}")
            print(f"符号: {token_symbol}")
            print(f"精度: {token_decimals}")
            human_balance = token_balance / (10 ** token_decimals)
            print(f"当前余额: {human_balance} {token_symbol}")
            
            # 检查余额是否足够
            decimal_amount = Decimal(str(amount))
            token_amount = int(decimal_amount * (10 ** token_decimals))
            
            if token_balance < token_amount:
                # 计算差异百分比
                balance_decimal = Decimal(str(token_balance)) / (10 ** token_decimals)
                difference_ratio = abs(decimal_amount - balance_decimal) / decimal_amount
                
                # 如果差异小于0.01%（非常接近），则使用实际余额
                if difference_ratio < Decimal('0.0001'):
                    print(f"请求金额 {decimal_amount} {token_symbol} 非常接近当前余额 {balance_decimal} {token_symbol}，将使用实际余额")
                    decimal_amount = balance_decimal
                    token_amount = token_balance
                else:
                    raise ValueError(f"代币余额不足: 需要 {decimal_amount} {token_symbol}，但只有 {human_balance} {token_symbol}")
            
            # 确定代币类型
            token_type = "ERC20"  # 默认为ERC20
            
            predicate_address = self.ethereum_web3.to_checksum_address(self.POS_TOKEN_PREDICATE[token_type])
            
            # 检查并获取授权
            await self.approve_token(
                token_address=formatted_token,
                spender_address=predicate_address,
                amount=token_amount,
                chain_id=PolygonBridgeTokens.ETHEREUM_CHAIN_ID
            )
            
            # 获取ETH余额
            eth_balance = self.ethereum_web3.eth.get_balance(self.address)
            eth_balance_ether = self.ethereum_web3.from_wei(eth_balance, 'ether')
            print(f"当前ETH余额: {eth_balance_ether:.6f} ETH")
            
            # 降低最低ETH余额要求至0.001 ETH
            min_eth_required = 0.001  # 最低要求只需0.001 ETH
            if eth_balance_ether < min_eth_required:
                print(f"⚠️ 警告: ETH余额可能不足! 最低需要: {min_eth_required} ETH, 当前余额: {eth_balance_ether:.6f} ETH")
                raise ValueError(f"ETH余额不足! 需要至少 {min_eth_required} ETH, 当前余额: {eth_balance_ether:.6f} ETH")
            
            # 直接定义RootChainManager合约的ABI，不从文件读取
            abi = [
                {
                    "inputs": [
                        {"internalType": "address", "name": "user", "type": "address"},
                        {"internalType": "address", "name": "rootToken", "type": "address"},
                        {"internalType": "bytes", "name": "depositData", "type": "bytes"}
                    ],
                    "name": "depositFor",
                    "outputs": [],
                    "stateMutability": "nonpayable",
                    "type": "function"
                }
            ]
            
            # 创建合约实例
            contract = self.ethereum_web3.eth.contract(
                address=self.ethereum_web3.to_checksum_address(self.ROOT_CHAIN_MANAGER),
                abi=abi
            )
            
            # 准备交易参数
            tx_params = {
                'from': self.address,
                'nonce': self.ethereum_web3.eth.get_transaction_count(self.address),
                'chainId': PolygonBridgeTokens.ETHEREUM_CHAIN_ID
            }
            
            # 设置EIP-1559 gas参数
            current_block = self.ethereum_web3.eth.get_block('latest')
            base_fee = current_block['baseFeePerGas']
            priority_fee = self.ethereum_web3.to_wei(0.1888, 'gwei')  # 固定priority fee为0.1888 gwei
            
            # 设置最大费用为基础费用的2倍加上优先费用
            max_fee_per_gas = base_fee * 2 + priority_fee
            
            tx_params['maxFeePerGas'] = max_fee_per_gas
            tx_params['maxPriorityFeePerGas'] = priority_fee
            
            print(f"Gas费用设置:")
            print(f"  Base fee: {self.ethereum_web3.from_wei(base_fee, 'gwei')} Gwei")
            print(f"  Priority fee: {self.ethereum_web3.from_wei(priority_fee, 'gwei')} Gwei")
            print(f"  Max fee: {self.ethereum_web3.from_wei(max_fee_per_gas, 'gwei')} Gwei")
            
            # 接收地址
            to_address = receiver if receiver else self.address
            
            # 构建交易数据
            print(f"准备桥接 {decimal_amount} {token_symbol} 从以太坊到Polygon...")
            
            # 使用encode_deposit_data方法进行编码，替代旧的编码方式
            encoded_deposit_data = self.encode_deposit_data(token_amount, token_type)
            
            # 测试构建交易但不发送
            build_tx = contract.functions.depositFor(
                to_address,
                formatted_token,
                encoded_deposit_data
            ).build_transaction(tx_params)
            
            # 估算gas限制
            try:
                estimated_gas = self.ethereum_web3.eth.estimate_gas(build_tx)
                # 添加20%的缓冲
                gas_limit = int(estimated_gas * 1.2)
                print(f"估算gas限制: {estimated_gas}，使用{gas_limit}（含20%缓冲）")
                tx_params['gas'] = gas_limit
            except Exception as e:
                print(f"无法估算gas限制: {str(e)}，使用默认值: {self.gas_limit * 2}")
                tx_params['gas'] = self.gas_limit * 2
            
            # 估算交易成本
            estimated_eth_cost = self.ethereum_web3.from_wei(tx_params['gas'] * max_fee_per_gas, 'ether')
            print(f"预估交易成本: {estimated_eth_cost:.8f} ETH")
            
            # 检查余额是否足够
            if estimated_eth_cost > eth_balance_ether:
                print(f"警告: 预估gas费用({estimated_eth_cost:.8f} ETH)高于当前余额({eth_balance_ether:.8f} ETH)")
                print("交易可能会失败，尝试降低gas限制...")
                
                # 降低gas限制
                tx_params['gas'] = int(tx_params['gas'] * 0.9)  # 降低10%
                estimated_eth_cost = self.ethereum_web3.from_wei(tx_params['gas'] * max_fee_per_gas, 'ether')
                print(f"降低gas限制后预估成本: {estimated_eth_cost:.8f} ETH")
                
                if estimated_eth_cost > eth_balance_ether:
                    raise ValueError(f"ETH余额不足，即使降低gas限制后仍然无法支付交易。需要: {estimated_eth_cost:.8f} ETH, 当前余额: {eth_balance_ether:.8f} ETH")
            
            try:
                # 重新构建交易
                tx = contract.functions.depositFor(
                    to_address,
                    formatted_token,
                    encoded_deposit_data
                ).build_transaction(tx_params)
                
                # 签名交易
                signed_tx = self.ethereum_web3.eth.account.sign_transaction(tx, self.account.key)
                
                # 发送交易
                tx_hash = self.ethereum_web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                print(f"已发送桥接交易，交易哈希: {tx_hash.hex()}")
                print(f"查看交易: https://etherscan.io/tx/{tx_hash.hex()}")
                print(f"桥接通常需要7-8分钟完成，请稍后在Polygon网络上检查您的代币")
                
                return tx_hash.hex()
            except Exception as e:
                error_msg = str(e)
                if "insufficient funds" in error_msg:
                    print(f"错误: ETH余额不足以支付交易费用")
                    print(f"当前余额: {eth_balance_ether:.8f} ETH")
                    print(f"预估需要: {estimated_eth_cost:.8f} ETH")
                    print("此错误通常出现在以下情况:")
                    print("1. 预估gas不准确")
                    print("2. 交易执行时网络状况发生变化")
                    print("请尝试以下解决方案:")
                    print("1. 向钱包充值更多ETH")
                    print("2. 使用--gas-price参数设置较低的gas价格 (例如: --gas-price 0.5)")
                    print("3. 等待以太坊网络不那么拥堵时再试")
                    raise ValueError(f"发送交易失败: {error_msg}")
                elif "nonce too low" in error_msg:
                    print("\n检测到nonce too low错误，开始智能处理...")
                    
                    # 记录原始桥接操作的相关数据
                    original_bridge_amount = token_amount  # 原始要桥接的代币数量
                    original_balance = token_balance       # 原始桥接前的余额
                    
                    # 获取最新余额
                    try:
                        current_balance = token_contract.functions.balanceOf(self.address).call()
                        
                        # 打印详细的余额信息
                        print("\n=== 余额变化检查 ===")
                        print(f"原始桥接金额: {original_bridge_amount / (10 ** token_decimals)} {token_symbol}")
                        print(f"桥接前余额: {original_balance / (10 ** token_decimals)} {token_symbol}")
                        print(f"当前余额: {current_balance / (10 ** token_decimals)} {token_symbol}")
                        
                        # 计算余额差异
                        balance_difference = original_balance - current_balance
                        print(f"余额减少量: {balance_difference / (10 ** token_decimals)} {token_symbol}")
                        
                        # 检查余额减少是否接近原始桥接金额（允许1%的误差）
                        expected_min_difference = original_bridge_amount * 0.99  # 允许1%的误差
                        expected_max_difference = original_bridge_amount * 1.01
                        
                        if balance_difference >= expected_min_difference and balance_difference <= expected_max_difference:
                            print("\n 检测到余额减少量与原始桥接金额匹配！")
                            print(f"预期减少: {original_bridge_amount / (10 ** token_decimals)} {token_symbol}")
                            print(f"实际减少: {balance_difference / (10 ** token_decimals)} {token_symbol}")
                            print("说明之前的桥接交易可能已经成功执行")
                            
                            # 尝试获取最近的交易
                            try:
                                recent_nonce = self.ethereum_web3.eth.get_transaction_count(self.address, 'latest')
                                pending_nonce = self.ethereum_web3.eth.get_transaction_count(self.address, 'pending')
                                
                                if pending_nonce > recent_nonce:
                                    print(f"\n检测到{pending_nonce - recent_nonce}个挂起的交易")
                                
                                # 查找最近的成功交易
                                found_tx_hash = None
                                print("\n开始查找相关的桥接交易...")
                                for nonce in range(recent_nonce - 5, recent_nonce + 1):
                                    try:
                                        tx = self.ethereum_web3.eth.get_transaction_by_nonce(self.address, nonce)
                                        if tx and tx['to'] and tx['to'].lower() == self.ROOT_CHAIN_MANAGER.lower():
                                            found_tx_hash = tx['hash'].hex()
                                            print(f"找到可能的桥接交易，哈希: {found_tx_hash}")
                                            break
                                    except Exception:
                                        continue
                                
                                if found_tx_hash:
                                    print("\n 成功找到桥接交易！")
                                    print(f"交易哈希: {found_tx_hash}")
                                    print("继续监控Polygon链上的到账情况...")
                                    return found_tx_hash
                                else:
                                    print("\n 未找到具体交易，但余额确实已经减少。建议:")
                                    print("1. 等待几分钟后在Polygon网络上检查代币是否到账")
                                    print("2. 检查钱包交易历史，寻找相关的桥接交易")
                                    print("3. 如果长时间未到账，可以联系Polygon桥接支持")
                                    return "BALANCE_REDUCED"
                                    
                            except Exception as e:
                                print(f"\n查询交易历史时出错: {str(e)}")
                                print("但余额已确认减少，建议等待代币到账")
                                return "BALANCE_REDUCED"
                        else:
                            print("\n 余额减少量与原始桥接金额不匹配")
                            print("继续尝试使用新的nonce重试交易...")
                    except Exception as e:
                        print(f"\n检查余额时出错: {str(e)}")
                        print("继续尝试使用新的nonce重试交易...")
                    
                    # 如果余额未减少或检查失败，尝试使用新的nonce重试
                    print("\n准备使用新的nonce重试交易...")
                    
                    # 获取最新的nonce
                    new_nonce = self.ethereum_web3.eth.get_transaction_count(self.address, 'latest')
                    pending_nonce = self.ethereum_web3.eth.get_transaction_count(self.address, 'pending')
                    
                    print(f"当前nonce: {tx_params['nonce']}")
                    print(f"链上最新nonce: {new_nonce}")
                    print(f"待处理nonce: {pending_nonce}")
                    
                    # 使用更高的nonce重试
                    retry_nonce = max(new_nonce, pending_nonce)
                    if retry_nonce > tx_params['nonce']:
                        print(f"\n使用新的nonce {retry_nonce} 重试交易...")
                        tx_params['nonce'] = retry_nonce
                        
                        # 重新构建并发送交易
                        retry_tx = contract.functions.depositFor(
                            to_address,
                            formatted_token,
                            encoded_deposit_data
                        ).build_transaction(tx_params)
                        
                        signed_retry_tx = self.ethereum_web3.eth.account.sign_transaction(retry_tx, self.account.key)
                        retry_tx_hash = self.ethereum_web3.eth.send_raw_transaction(signed_retry_tx.rawTransaction)
                        
                        print(f" 重试交易已发送")
                        print(f"新交易哈希: {retry_tx_hash.hex()}")
                        print("继续监控Polygon链上的到账情况...")
                        return retry_tx_hash.hex()
                    else:
                        print("当前有挂起的交易，请等待它们确认后再试")
                        print("建议:")
                        print("1. 查看钱包交易历史，确认是否有未确认的交易")
                        print("2. 等待几分钟后重试")
                        print("3. 如果长时间未确认，可以考虑使用加速交易功能")
                        raise ValueError("有挂起的交易需要确认")
                
                raise ValueError(f"发送交易失败: {error_msg}")
            
        except Exception as e:
            print(f"执行直接桥接时出错: {str(e)}")
            raise

    def get_web3_by_chain_id(self, chain_id: int) -> Web3:
        """
        根据链ID获取对应的Web3实例
        
        Args:
            chain_id: 链ID
            
        Returns:
            Web3: 对应链的Web3实例
        """
        if chain_id == PolygonBridgeTokens.ETHEREUM_CHAIN_ID:
            return self.ethereum_web3
        elif chain_id == PolygonBridgeTokens.POLYGON_CHAIN_ID:
            return self.polygon_web3
        else:
            raise ValueError(f"不支持的链ID: {chain_id}")
            
    async def get_token_allowance(self, token_address: str, spender_address: str, chain_id: int) -> int:
        """
        获取代币的授权额度
        
        Args:
            token_address: 代币地址
            spender_address: 被授权地址
            chain_id: 链ID
            
        Returns:
            int: 授权额度
        """
        # 获取对应链的Web3实例
        web3 = self.get_web3_by_chain_id(chain_id)
        
        # 格式化地址
        formatted_token = web3.to_checksum_address(token_address)
        formatted_spender = web3.to_checksum_address(spender_address)
        
        # ERC20代币ABI
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}],
                "name": "allowance",
                "outputs": [{"name": "", "type": "uint256"}],
                "type": "function"
            }
        ]
        
        token_contract = web3.eth.contract(
            address=formatted_token,
            abi=erc20_abi
        )
        
        # 查询授权额度
        allowance = token_contract.functions.allowance(self.address, formatted_spender).call()
        return allowance

    async def monitor_polygon_deposit(self, ethereum_token_address: str, receiver_address: str, 
                                check_interval: int = 10, timeout_minutes: int = 80,
                                expected_amount: Optional[Union[int, float, Decimal]] = None,
                                initial_wait_time: int = 900) -> Dict[str, Any]:
        """
        使用WebSocket监控Polygon上的代币到账情况
        
        Args:
            ethereum_token_address: 以太坊上的代币地址
            receiver_address: 接收地址
            check_interval: 检查间隔（秒）
            timeout_minutes: 超时时间（分钟）
            expected_amount: 期望到账金额（可选）
            initial_wait_time: 开始监控前的等待时间（秒），默认15分钟
            
        Returns:
            Dict[str, Any]: 监控结果
        """
        try:
            # 首先等待initial_wait_time
            logging.info(f"等待 {initial_wait_time} 秒后开始监控...")
            await asyncio.sleep(initial_wait_time)
            
            # 确保接收地址格式正确
            if not Web3.is_address(receiver_address):
                logging.error(f"无效的接收地址: {receiver_address}")
                return {"success": False, "message": "无效的接收地址"}
                
            # 标准化地址格式
            receiver_address = Web3.to_checksum_address(receiver_address)
            
            # 获取Polygon上的映射地址
            polygon_token = self.get_polygon_mapped_address(ethereum_token_address)
            if not polygon_token:
                logging.error(f"找不到代币在Polygon上的映射地址，以太坊代币地址: {ethereum_token_address}")
                return {"success": False, "message": "找不到代币在Polygon上的映射地址"}
            
            # 获取代币信息（包括精度）
            token_info = self.get_token_info(token_address=polygon_token, chain="polygon")
            if not token_info:
                logging.error(f"无法获取代币信息: {polygon_token}")
                return {"success": False, "message": "无法获取代币信息"}
            
            token_decimals = token_info.get("decimals", 18)  # 默认使用18位精度
            logging.info(f"代币精度: {token_decimals}")
                
            logging.info(f"开始监控Polygon网络上的代币到账情况...")
            logging.info(f"接收地址: {receiver_address}")
            logging.info(f"以太坊代币地址: {ethereum_token_address}")
            logging.info(f"Polygon代币地址: {polygon_token}")
            if expected_amount is not None:
                logging.info(f"期望金额: {expected_amount}")
            
            # 设置超时时间
            end_time = time.time() + timeout_minutes * 60
            
            # 重试参数
            base_retry_delay = 5  # 初始重试延迟5秒
            max_retry_delay = 120  # 最大重试延迟2分钟
            current_retry_delay = base_retry_delay
            max_retries = 30  # 最大重试次数
            retry_count = 0
            
            # 记录已收到但金额不匹配的交易
            mismatched_txs = []
            
            while time.time() < end_time:
                try:
                    # 创建WebSocket连接并监控
                    async with websockets.connect(self.polygon_ws_url) as ws:
                        # 订阅Transfer事件
                        transfer_topic = self.polygon_web3.keccak(text="Transfer(address,address,uint256)").hex()
                        
                        # 创建事件过滤器
                        event_filter = {
                            "address": Web3.to_checksum_address(polygon_token),
                            "topics": [
                                transfer_topic,
                                None,  # from address (any)
                                "0x" + "0" * 24 + receiver_address[2:].lower()  # to address (padded)
                            ]
                        }
                        
                        # 发送订阅请求
                        await ws.send(json.dumps({
                            "id": 1,
                            "method": "eth_subscribe",
                            "params": ["logs", event_filter]
                        }))
                        
                        # 接收订阅确认
                        response = await ws.recv()
                        subscription = json.loads(response)
                        if "error" in subscription:
                            raise ValueError(f"WebSocket订阅失败: {subscription['error']}")
                            
                        logging.info("WebSocket连接已建立，正在监控转账事件...")
                        
                        # 重置重试参数
                        current_retry_delay = base_retry_delay
                        retry_count = 0
                        
                        # 监控事件
                        while time.time() < end_time:
                            try:
                                # 设置接收超时
                                message = await asyncio.wait_for(ws.recv(), timeout=30)
                                event = json.loads(message)
                                
                                if "params" in event and "result" in event["params"]:
                                    log = event["params"]["result"]
                                    
                                    # 解析Transfer事件数据
                                    data = log["data"]
                                    amount = int(data, 16)  # 转换十六进制数据为整数
                                    tx_hash = log["transactionHash"]
                                    
                                    # 如果指定了期望金额，验证金额是否匹配
                                    if expected_amount is not None:
                                        # 使用正确的代币精度
                                        expected_int = int(Decimal(str(expected_amount)) * Decimal(f"1e{token_decimals}"))
                                        # 允许0.1%的误差
                                        if abs(amount - expected_int) <= expected_int * 0.001:
                                            logging.info(f"检测到代币到账，金额匹配: {amount / 10**token_decimals}")
                                            return {
                                                "success": True,
                                                "amount": str(amount),
                                                "amount_decimal": str(amount / 10**token_decimals),
                                                "tx_hash": tx_hash,
                                                "mismatched_txs": mismatched_txs
                                            }
                                        else:
                                            # 记录不匹配的交易
                                            mismatch_info = {
                                                "tx_hash": tx_hash,
                                                "received_amount": str(amount / 10**token_decimals),
                                                "expected_amount": str(expected_amount),
                                                "difference_percentage": abs(amount - expected_int) / expected_int * 100
                                            }
                                            mismatched_txs.append(mismatch_info)
                                            
                                            logging.warning(
                                                f"检测到代币到账但金额不匹配:\n"
                                                f"  交易哈希: {tx_hash}\n"
                                                f"  收到金额: {amount / 10**token_decimals}\n"
                                                f"  期望金额: {expected_amount}\n"
                                                f"  差异比例: {mismatch_info['difference_percentage']:.2f}%"
                                            )
                                            # 继续监控，等待匹配的转账
                                    else:
                                        # 如果没有指定期望金额，直接返回成功
                                        amount_decimal = amount / 10**token_decimals
                                        logging.info(f"检测到代币到账: {amount_decimal}")
                                        return {
                                            "success": True,
                                            "amount": str(amount),
                                            "amount_decimal": str(amount_decimal),
                                            "tx_hash": tx_hash
                                        }
                                        
                            except asyncio.TimeoutError:
                                # WebSocket接收超时，继续等待
                                continue
                            except Exception as e:
                                logging.error(f"处理事件时出错: {str(e)}")
                                raise  # 抛出异常以触发重连
                                
                except Exception as e:
                    retry_count += 1
                    if retry_count > max_retries:
                        logging.error(f"达到最大重试次数 ({max_retries})，停止监控")
                        return {
                            "success": False,
                            "message": f"连接失败，已达到最大重试次数: {str(e)}",
                            "mismatched_txs": mismatched_txs
                        }
                    
                    remaining_time = end_time - time.time()
                    if remaining_time <= 0:
                        break
                        
                    logging.warning(f"WebSocket连接断开 (重试 {retry_count}/{max_retries}): {str(e)}")
                    logging.info(f"等待 {current_retry_delay} 秒后重试...")
                    
                    await asyncio.sleep(current_retry_delay)
                    # 指数型重试，但不超过最大延迟
                    current_retry_delay = min(current_retry_delay * 2, max_retry_delay)
            
            # 超时处理
            logging.warning(f"监控超时，已等待 {timeout_minutes} 分钟")
            return {
                "success": False,
                "message": f"监控超时，未检测到匹配的代币到账",
                "mismatched_txs": mismatched_txs
            }
            
        except Exception as e:
            logging.error(f"监控过程中出错: {str(e)}")
            return {
                "success": False,
                "message": f"监控出错: {str(e)}",
                "mismatched_txs": mismatched_txs if 'mismatched_txs' in locals() else []
            }




def load_config() -> Dict[str, Any]:
    """
    从配置文件加载配置
    
    Returns:
        Dict[str, Any]: 配置信息
    """
    # 确定项目根目录，并加载配置文件
    # 首先尝试查找项目根目录（包含config文件夹的目录）
    current_dir = pathlib.Path(__file__).resolve().parent
    
    # 向上查找三级，直到找到config目录
    for _ in range(5):
        config_path = current_dir / "config" / "config.yaml"
        if config_path.exists():
            break
        current_dir = current_dir.parent
    
    # 如果仍然找不到，尝试从工作目录找
    if not config_path.exists():
        project_root = pathlib.Path.cwd()
        while project_root.name and not (project_root / "config" / "config.yaml").exists():
            project_root = project_root.parent
        
        config_path = project_root / "config" / "config.yaml"
        
    # 如果仍然找不到，则使用绝对路径
    if not config_path.exists():
        config_path = pathlib.Path("C:/Users/<USER>/CascadeProjects/cex_dex_arb_dev/config/config.yaml")
    
    # 如果仍然找不到，抛出异常
    if not config_path.exists():
        raise FileNotFoundError(f"找不到配置文件: {config_path}")
    
    # 加载YAML配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config

def test_rpc_connection(rpc_url: str, is_polygon: bool = False) -> bool:
    """
    测试 RPC 连接是否正常

    Args:
        rpc_url: RPC URL
        is_polygon: 是否为 Polygon 网络

    Returns:
        bool: 连接是否正常
    """
    try:
        web3 = Web3(Web3.HTTPProvider(rpc_url))

        # 如果是 Polygon 网络，添加 PoA 中间件
        if is_polygon:
            from web3.middleware import geth_poa_middleware
            web3.middleware_onion.inject(geth_poa_middleware, layer=0)

        # 测试连接并检查响应格式
        if web3.is_connected():
            # 额外检查：尝试获取区块号以确保响应格式正确
            block_number = web3.eth.block_number
            if block_number > 0:
                return True
        return False
    except Exception as e:
        print(f"RPC连接测试失败 {rpc_url}: {str(e)}")
        return False

def get_working_rpc(rpc_config: dict, network_name: str) -> str:
    """
    获取可用的 RPC URL

    Args:
        rpc_config: RPC 配置
        network_name: 网络名称

    Returns:
        str: 可用的 RPC URL
    """
    is_polygon = network_name.lower() == "polygon"

    # 首先尝试主要的 RPC URL
    main_rpc = rpc_config.get("rpc_url", "")
    if main_rpc:
        print(f"测试 {network_name} 主要 RPC: {main_rpc}")
        if test_rpc_connection(main_rpc, is_polygon):
            print(f"✅ {network_name} 主要 RPC 连接成功")
            return main_rpc
        else:
            print(f"❌ {network_name} 主要 RPC 连接失败")

    # 如果主要 RPC 失败，尝试备用 RPC
    backup_rpcs = rpc_config.get("backup_rpc_urls", [])
    if backup_rpcs:
        print(f"尝试 {network_name} 备用 RPC 节点...")
        for backup_rpc in backup_rpcs:
            print(f"测试 {network_name} 备用 RPC: {backup_rpc}")
            if test_rpc_connection(backup_rpc, is_polygon):
                print(f"✅ {network_name} 备用 RPC 连接成功: {backup_rpc}")
                return backup_rpc
            else:
                print(f"❌ {network_name} 备用 RPC 连接失败: {backup_rpc}")

    raise ConnectionError(f"所有 {network_name} RPC 节点都无法连接")

def get_network_config() -> Tuple[str, str, str]:
    """
    从配置文件中获取网络配置，支持 RPC 故障转移

    Returns:
        Tuple[str, str, str]: 以太坊RPC、Polygon RPC、私钥
    """
    config = load_config()

    # 获取以太坊RPC（支持故障转移）
    eth_rpc_config = config.get("rpc", {}).get("ethereum", {})
    if not eth_rpc_config:
        raise ValueError("配置文件中未找到以太坊RPC配置")

    eth_rpc = get_working_rpc(eth_rpc_config, "以太坊")

    # 获取Polygon RPC（支持故障转移）
    polygon_rpc_config = config.get("rpc", {}).get("polygon", {})
    if not polygon_rpc_config:
        raise ValueError("配置文件中未找到Polygon RPC配置")

    polygon_rpc = get_working_rpc(polygon_rpc_config, "Polygon")

    # 获取私钥
    private_key = config.get("wallet", {}).get("private_key", "")
    if not private_key:
        raise ValueError("配置文件中未找到私钥")

    print("\n网络配置信息:")
    print(f"以太坊 RPC: {eth_rpc}")
    print(f"Polygon RPC: {polygon_rpc}")
    print(f"钱包地址: {Web3().eth.account.from_key(private_key).address}")

    return eth_rpc, polygon_rpc, private_key

async def main_async():
    """
    异步主函数
    """
    parser = argparse.ArgumentParser(description="Polygon-Ethereum Bridge Tool")
    parser.add_argument("action", choices=[
        "to-polygon", "to-ethereum", "claim", "balance", "list-tokens"
    ], help="要执行的操作")
    
    parser.add_argument("--token-address", help="代币地址")
    parser.add_argument("--token-symbol", help="代币符号")
    parser.add_argument("--amount", type=float, help="转账金额")
    parser.add_argument("--chain", choices=["ethereum", "polygon"], default="polygon", help="选择链（用于balance查询）")
    parser.add_argument("--no-monitor", action="store_true", help="禁用自动监控")
    parser.add_argument("--wait-time", type=int, default=900, help="等待时间(秒)，默认900秒(15分钟)")
    parser.add_argument("--tx-hash", help="交易哈希（用于claim操作）")
    parser.add_argument("--gas-price", type=float, help="Gas价格(Gwei)")
    parser.add_argument("--gas-limit", type=int, help="Gas限制")
    parser.add_argument("--max-fee-per-gas", type=float, help="最大总gas费用(Gwei)")
    parser.add_argument("--max-priority-fee-per-gas", type=float, help="最大优先费用(Gwei)")
    
    args = parser.parse_args()
    
    ethereum_rpc, polygon_rpc, private_key = get_network_config()
    
    bridge = PolygonEthereumBridge(
        ethereum_rpc_url=ethereum_rpc,
        polygon_rpc_url=polygon_rpc,
        private_key=private_key,
        gas_price_gwei=args.gas_price,
        gas_limit=args.gas_limit,
        max_fee_per_gas=args.max_fee_per_gas,
        max_priority_fee_per_gas=args.max_priority_fee_per_gas
    )
    
    try:
        if args.action == "to-polygon":
            # 检查代币映射关系
            token_info = bridge.check_token_mapping(args.token_address, PolygonBridgeTokens.ETHEREUM_CHAIN_ID)
            if not token_info:
                # 尝试从tokens.json获取映射信息
                polygon_addr = bridge.get_polygon_mapped_address(args.token_address)
                if polygon_addr:
                    print(f"找到代币在Polygon上的映射地址: {polygon_addr}")
                else:
                    print("错误: 找不到代币在Polygon上的映射地址")
                    print("请确保代币已在Polygon PoS桥上注册")
                    sys.exit(1)
            
            # 执行桥接
            print(f"开始桥接代币到Polygon...")
            bridge_tx_hash = await bridge.direct_bridge_to_polygon(
                token_address=args.token_address,
                amount=args.amount
            )
            print(f"桥接交易已提交，交易哈希: {bridge_tx_hash}")
            
            if not args.no_monitor:
                print(f"等待 {args.wait_time} 秒后开始监控...")
                await asyncio.sleep(args.wait_time)
                
                # 监控到账
                monitor_result = await bridge.monitor_polygon_deposit(
                    ethereum_token_address=args.token_address,
                    receiver_address=bridge.address,  # 使用当前地址作为接收地址
                    expected_amount=args.amount,
                    initial_wait_time=0,  # 因为我们已经等待了wait_time
                    timeout_minutes=80  # 设置40分钟超时，防止永久等待
                )
                print(f"监控结果: {monitor_result}")
            else:
                print("已禁用自动监控，请手动检查代币到账情况")
                print(f"交易哈希: {bridge_tx_hash}")

        elif args.action == "to-ethereum":
            if not args.token_address or not args.amount:
                print("错误: 从Polygon到以太坊需要提供--token-address和--amount参数")
                sys.exit(1)
                
            result = await bridge.polygon_to_ethereum(
                amount=str(args.amount),
                token_address=args.token_address,
                wait_for_claim=not args.no_monitor
            )
            print(f"桥接结果: {result}")
            
            if isinstance(result, dict) and result.get("burn_transaction_hash"):
                print(f"\n交易详情:")
                print(f"Burn交易哈希: {result['burn_transaction_hash']}")
                print(f"查看交易: https://polygonscan.com/tx/{result['burn_transaction_hash']}")
                
                if result.get("claim_tx_hash"):
                    print(f"Claim交易哈希: {result['claim_tx_hash']}")
                    print(f"查看交易: https://etherscan.io/tx/{result['claim_tx_hash']}")

        elif args.action == "claim":
            if not args.tx_hash:
                parser.error("claim 命令需要 --tx-hash 参数")
                
            # 先获取交易详情
            tx_details = await bridge._get_burn_tx_details(args.tx_hash)
            if not tx_details:
                print("无法获取burn交易详情，可能是因为交易还未被包含在检查点中")
                return
                
            # 尝试claim
            claim_result = await bridge._execute_claim_on_ethereum(args.tx_hash, tx_details)
            if claim_result:
                print(f"Claim 交易已发送: {claim_result}")
            else:
                print("Claim 失败，可能是因为交易还未被包含在检查点中，请稍后重试")
    
        elif args.action == "balance":
            balance = bridge.get_balance(
                token_symbol=args.token_symbol,
                token_address=args.token_address,
                chain=args.chain
            )
            print(f"余额: {balance}")
            
        elif args.action == "list-tokens":
            tokens = bridge.list_tokens()
            print(json.dumps(tokens, indent=2))
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    import platform
    if platform.system() == "Windows":
        # Windows系统使用 SelectorEventLoop
        import asyncio
        import sys
        if sys.version_info[0] == 3 and sys.version_info[1] >= 8:
            # Python 3.8+ 需要显式设置策略
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main_async())