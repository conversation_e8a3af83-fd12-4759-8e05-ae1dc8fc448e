{"version": 3, "sources": ["../../src/bcs/serializable/movePrimitives.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\nimport {\n  MAX_U128_BIG_INT,\n  MAX_U16_NUMBER,\n  MAX_U32_NUMBER,\n  MAX_U64_BIG_INT,\n  MAX_U8_NUMBER,\n  MAX_U256_BIG_INT,\n} from \"../consts\";\nimport { Deserializer } from \"../deserializer\";\nimport { Serializable, Serializer, ensureBoolean, validateNumberInRange } from \"../serializer\";\nimport { TransactionArgument } from \"../../transactions/instances/transactionArgument\";\nimport { AnyNumber, Uint16, Uint32, Uint8, ScriptTransactionArgumentVariants } from \"../../types\";\n\n/**\n * Represents a boolean value that can be serialized and deserialized.\n * This class extends the Serializable class and provides methods to serialize\n * the boolean value for different contexts, such as entry functions and script functions.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class Bool extends Serializable implements TransactionArgument {\n  public readonly value: boolean;\n\n  /**\n   * Constructs a new instance with a specified value.\n   * This ensures that the value is validated to be within the acceptable range.\n   *\n   * @param value - The number to be validated and assigned, which must be between 0 and MAX_U256_BIG_INT.\n   * @group Implementation\n   * @category BCS\n   */\n  constructor(value: boolean) {\n    super();\n\n    /**\n     * Ensures that the provided value is of type boolean.\n     * This function throws an error if the value is not a boolean, helping to enforce type safety in your code.\n     *\n     * @param value - The value to be checked for boolean type.\n     * @throws {Error} Throws an error if the value is not a boolean.\n     * @group Implementation\n     * @category BCS\n     */\n    ensureBoolean(value);\n    this.value = value;\n  }\n\n  /**\n   * Serializes the value using the provided serializer.\n   * This function is essential for converting the value into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer instance used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serialize(serializer: Serializer): void {\n    serializer.serializeBool(this.value);\n  }\n\n  /**\n   * Serializes the current instance for use in an entry function by converting it to a byte sequence.\n   * This allows the instance to be properly formatted for serialization in transactions.\n   *\n   * @param serializer - The serializer instance used to serialize the byte sequence.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  /**\n   * Serializes the current instance for use in a script function.\n   * This allows for the conversion of the instance into a format suitable for transmission or storage.\n   *\n   * @param serializer - The serializer used to perform the serialization.\n   * @group Implementation\n   * @category BCS\n   */\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.Bool);\n    serializer.serialize(this);\n  }\n\n  /**\n   * Deserializes a U256 value from the provided deserializer.\n   *\n   * @param deserializer - The deserializer instance used to read the U256 data.\n   * @group Implementation\n   * @category BCS\n   */\n  // eslint-disable-next-line class-methods-use-this\n  deserialize(deserializer: Deserializer) {\n    return new U256(deserializer.deserializeU256());\n  }\n\n  static deserialize(deserializer: Deserializer): Bool {\n    return new Bool(deserializer.deserializeBool());\n  }\n}\n\n/**\n * Represents an unsigned 8-bit integer (U8) value.\n * This class extends the Serializable class and provides methods for serialization and deserialization of U8 values.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U8 extends Serializable implements TransactionArgument {\n  public readonly value: Uint8;\n\n  constructor(value: Uint8) {\n    super();\n    validateNumberInRange(value, 0, MAX_U8_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU8(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U8);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U8 {\n    return new U8(deserializer.deserializeU8());\n  }\n}\n\n/**\n * Represents a 16-bit unsigned integer (U16) value.\n * This class extends the Serializable class and provides methods for serialization\n * and deserialization of the U16 value.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U16 extends Serializable implements TransactionArgument {\n  public readonly value: Uint16;\n\n  constructor(value: Uint16) {\n    super();\n    validateNumberInRange(value, 0, MAX_U16_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU16(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U16);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U16 {\n    return new U16(deserializer.deserializeU16());\n  }\n}\n\n/**\n * Represents a 32-bit unsigned integer (U32) that can be serialized and deserialized.\n * This class ensures that the value is within the valid range for a U32.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U32 extends Serializable implements TransactionArgument {\n  public readonly value: Uint32;\n\n  constructor(value: Uint32) {\n    super();\n    validateNumberInRange(value, 0, MAX_U32_NUMBER);\n    this.value = value;\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU32(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U32);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U32 {\n    return new U32(deserializer.deserializeU32());\n  }\n}\n\n/**\n * Represents a 64-bit unsigned integer (U64) and provides methods for serialization.\n *\n * This class ensures that the value is within the valid range for a U64 and provides\n * functionality to serialize the value for various use cases, including entry functions\n * and script functions.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U64 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U64_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU64(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U64);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U64 {\n    return new U64(deserializer.deserializeU64());\n  }\n}\n\n/**\n * Represents a 128-bit unsigned integer value.\n * This class provides methods for serialization and deserialization\n * of U128 values, ensuring that the values are within the valid range.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U128 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U128_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU128(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U128);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U128 {\n    return new U128(deserializer.deserializeU128());\n  }\n}\n\n/**\n * Represents a 256-bit unsigned integer (U256) that extends the Serializable class.\n * This class provides methods for serialization and deserialization of U256 values,\n * ensuring that the values are within the valid range.\n *\n * @extends Serializable\n * @group Implementation\n * @category BCS\n */\nexport class U256 extends Serializable implements TransactionArgument {\n  public readonly value: bigint;\n\n  constructor(value: AnyNumber) {\n    super();\n    validateNumberInRange(value, BigInt(0), MAX_U256_BIG_INT);\n    this.value = BigInt(value);\n  }\n\n  serialize(serializer: Serializer): void {\n    serializer.serializeU256(this.value);\n  }\n\n  serializeForEntryFunction(serializer: Serializer): void {\n    const bcsBytes = this.bcsToBytes();\n    serializer.serializeBytes(bcsBytes);\n  }\n\n  serializeForScriptFunction(serializer: Serializer): void {\n    serializer.serializeU32AsUleb128(ScriptTransactionArgumentVariants.U256);\n    serializer.serialize(this);\n  }\n\n  static deserialize(deserializer: Deserializer): U256 {\n    return new U256(deserializer.deserializeU256());\n  }\n}\n"], "mappings": "mIAyBO,IAAMA,EAAN,MAAMC,UAAaC,CAA4C,CAWpE,YAAYC,EAAgB,CAC1B,MAAM,EAWNC,EAAcD,CAAK,EACnB,KAAK,MAAQA,CACf,CAUA,UAAUE,EAA8B,CACtCA,EAAW,cAAc,KAAK,KAAK,CACrC,CAUA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAUA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA4D,EACvEA,EAAW,UAAU,IAAI,CAC3B,CAUA,YAAYE,EAA4B,CACtC,OAAO,IAAIC,EAAKD,EAAa,gBAAgB,CAAC,CAChD,CAEA,OAAO,YAAYA,EAAkC,CACnD,OAAO,IAAIN,EAAKM,EAAa,gBAAgB,CAAC,CAChD,CACF,EAUaE,EAAN,MAAMC,UAAWR,CAA4C,CAGlE,YAAYC,EAAc,CACxB,MAAM,EACNQ,EAAsBR,EAAO,EAAGS,CAAa,EAC7C,KAAK,MAAQT,CACf,CAEA,UAAUE,EAA8B,CACtCA,EAAW,YAAY,KAAK,KAAK,CACnC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA0D,EACrEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAgC,CACjD,OAAO,IAAIG,EAAGH,EAAa,cAAc,CAAC,CAC5C,CACF,EAWaM,EAAN,MAAMC,UAAYZ,CAA4C,CAGnE,YAAYC,EAAe,CACzB,MAAM,EACNQ,EAAsBR,EAAO,EAAGY,CAAc,EAC9C,KAAK,MAAQZ,CACf,CAEA,UAAUE,EAA8B,CACtCA,EAAW,aAAa,KAAK,KAAK,CACpC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA2D,EACtEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAiC,CAClD,OAAO,IAAIO,EAAIP,EAAa,eAAe,CAAC,CAC9C,CACF,EAUaS,EAAN,MAAMC,UAAYf,CAA4C,CAGnE,YAAYC,EAAe,CACzB,MAAM,EACNQ,EAAsBR,EAAO,EAAGe,CAAc,EAC9C,KAAK,MAAQf,CACf,CAEA,UAAUE,EAA8B,CACtCA,EAAW,aAAa,KAAK,KAAK,CACpC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA2D,EACtEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAiC,CAClD,OAAO,IAAIU,EAAIV,EAAa,eAAe,CAAC,CAC9C,CACF,EAaaY,EAAN,MAAMC,UAAYlB,CAA4C,CAGnE,YAAYC,EAAkB,CAC5B,MAAM,EACNQ,EAAsBR,EAAO,OAAO,CAAC,EAAGkB,CAAe,EACvD,KAAK,MAAQ,OAAOlB,CAAK,CAC3B,CAEA,UAAUE,EAA8B,CACtCA,EAAW,aAAa,KAAK,KAAK,CACpC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA2D,EACtEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAiC,CAClD,OAAO,IAAIa,EAAIb,EAAa,eAAe,CAAC,CAC9C,CACF,EAWae,EAAN,MAAMC,UAAarB,CAA4C,CAGpE,YAAYC,EAAkB,CAC5B,MAAM,EACNQ,EAAsBR,EAAO,OAAO,CAAC,EAAGqB,CAAgB,EACxD,KAAK,MAAQ,OAAOrB,CAAK,CAC3B,CAEA,UAAUE,EAA8B,CACtCA,EAAW,cAAc,KAAK,KAAK,CACrC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA4D,EACvEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAkC,CACnD,OAAO,IAAIgB,EAAKhB,EAAa,gBAAgB,CAAC,CAChD,CACF,EAWaC,EAAN,MAAMiB,UAAavB,CAA4C,CAGpE,YAAYC,EAAkB,CAC5B,MAAM,EACNQ,EAAsBR,EAAO,OAAO,CAAC,EAAGuB,CAAgB,EACxD,KAAK,MAAQ,OAAOvB,CAAK,CAC3B,CAEA,UAAUE,EAA8B,CACtCA,EAAW,cAAc,KAAK,KAAK,CACrC,CAEA,0BAA0BA,EAA8B,CACtD,IAAMC,EAAW,KAAK,WAAW,EACjCD,EAAW,eAAeC,CAAQ,CACpC,CAEA,2BAA2BD,EAA8B,CACvDA,EAAW,uBAA4D,EACvEA,EAAW,UAAU,IAAI,CAC3B,CAEA,OAAO,YAAYE,EAAkC,CACnD,OAAO,IAAIkB,EAAKlB,EAAa,gBAAgB,CAAC,CAChD,CACF", "names": ["Bool", "_Bool", "Serializable", "value", "ensureBoolean", "serializer", "bcsBytes", "deserializer", "U256", "U8", "_U8", "validateNumberInRange", "MAX_U8_NUMBER", "U16", "_U16", "MAX_U16_NUMBER", "U32", "_U32", "MAX_U32_NUMBER", "U64", "_U64", "MAX_U64_BIG_INT", "U128", "_U128", "MAX_U128_BIG_INT", "_U256", "MAX_U256_BIG_INT"]}