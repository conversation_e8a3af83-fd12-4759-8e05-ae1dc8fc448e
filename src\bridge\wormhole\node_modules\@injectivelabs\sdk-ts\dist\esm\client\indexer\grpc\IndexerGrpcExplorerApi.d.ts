import { InjectiveExplorerRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
/**
 * @category Indexer Grpc API
 */
export declare class IndexerGrpcExplorerApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: InjectiveExplorerRpc.InjectiveExplorerRPCClientImpl;
    constructor(endpoint: string);
    fetchTxByHash(hash: string): Promise<import("../types/explorer.js").Transaction>;
    fetchAccountTx({ address, limit, type, before, after, startTime, endTime, }: {
        address: string;
        limit?: number;
        type?: string;
        before?: number;
        after?: number;
        startTime?: number;
        endTime?: number;
    }): Promise<{
        txs: import("../types/explorer.js").Transaction[];
        pagination: import("../../../index.js").ExchangePagination;
    }>;
    fetchValidator(validatorAddress: string): Promise<import("../types/explorer.js").ExplorerValidator>;
    fetchValidatorUptime(validatorAddress: string): Promise<import("../types/explorer.js").ValidatorUptime[]>;
    fetchPeggyDepositTxs({ sender, receiver, limit, skip, }: {
        receiver?: string;
        sender?: string;
        limit?: number;
        skip?: number;
    }): Promise<import("../types/explorer.js").PeggyDepositTx[]>;
    fetchPeggyWithdrawalTxs({ sender, receiver, limit, skip, }: {
        sender?: string;
        receiver?: string;
        limit?: number;
        skip?: number;
    }): Promise<import("../types/explorer.js").PeggyWithdrawalTx[]>;
    fetchBlocks({ before, after, limit, from, to, }: {
        before?: number;
        after?: number;
        limit?: number;
        from?: number;
        to?: number;
    }): Promise<InjectiveExplorerRpc.GetBlocksResponse>;
    fetchBlock(id: string): Promise<InjectiveExplorerRpc.GetBlockResponse>;
    fetchTxs({ before, after, limit, skip, type, chainModule, startTime, endTime, }: {
        before?: number;
        after?: number;
        limit?: number;
        skip?: number;
        type?: string;
        startTime?: number;
        endTime?: number;
        chainModule?: string;
    }): Promise<InjectiveExplorerRpc.GetTxsResponse>;
    fetchIBCTransferTxs({ sender, receiver, srcChannel, srcPort, destChannel, destPort, limit, skip, }: {
        sender?: string;
        receiver?: string;
        srcChannel?: string;
        srcPort?: string;
        destChannel?: string;
        destPort?: string;
        limit?: number;
        skip?: number;
    }): Promise<import("../types/explorer.js").IBCTransferTx[]>;
    fetchExplorerStats(): Promise<import("../types/explorer.js").ExplorerStats>;
    fetchTxsV2({ type, token, status, perPage, endTime, startTime, blockNumber, }: {
        type?: string;
        token?: string;
        status?: string;
        perPage?: number;
        endTime?: number;
        startTime?: number;
        blockNumber?: number;
    }): Promise<{
        data: import("../types/explorer.js").ExplorerTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    }>;
    fetchAccountTxsV2({ type, token, address, endTime, perPage, startTime, }: {
        type?: string;
        token?: string;
        address: string;
        endTime?: number;
        perPage?: number;
        startTime?: number;
    }): Promise<{
        data: import("../types/explorer.js").ExplorerTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    }>;
    fetchBlocksV2({ token, perPage, }: {
        token?: string;
        perPage?: number;
    }): Promise<{
        paging: InjectiveExplorerRpc.Cursor | undefined;
        data: import("../types/explorer.js").Block[];
    }>;
    fetchContractTxsV2({ to, from, token, height, status, perPage, contractAddress, }: {
        to?: number;
        from?: number;
        token?: string;
        height?: string;
        status?: string;
        perPage?: number;
        contractAddress: string;
    }): Promise<{
        data: import("../types/explorer.js").ContractTransaction[];
        paging: InjectiveExplorerRpc.Cursor | undefined;
    }>;
}
