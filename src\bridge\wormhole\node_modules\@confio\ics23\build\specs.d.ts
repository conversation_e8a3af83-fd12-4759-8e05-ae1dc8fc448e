import { ics23 } from "./generated/codecimpl";
export declare function ensureLeaf(leaf: ics23.ILeafOp, spec: ics23.ILeafOp): void;
export declare function ensureInner(inner: ics23.IInnerOp, prefix: Uint8Array | null | undefined, spec: ics23.IInnerSpec): void;
export declare function ensureBytesEqual(a: Uint8Array, b: Uint8Array): void;
export declare function bytesEqual(a: Uint8Array, b: Uint8Array): boolean;
export declare function ensureBytesBefore(first: Uint8Array, last: Uint8Array): void;
export declare function bytesBefore(first: Uint8Array, last: Uint8Array): boolean;
