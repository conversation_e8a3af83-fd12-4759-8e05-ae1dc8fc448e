---
name: Publish to NPM

on:
  workflow_dispatch:
  release:
    types: [created]
  push:
    tags:
      - 'v\\d.*'

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
    - name: Check out repository
      uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871  # v4.2.1

    - name: Set up Node.js
      uses: actions/setup-node@0a44ba7841725637a19e28fa30b79a866c81b0a6  # v4.0.4
      with:
        node-version: 20

    - name: Install dependencies and build
      run: npm ci

    - name: Run lint
      run: npm run lint

    - name: Run tests
      run: npm test

  publish-npm:
    needs: build-and-test
    runs-on: ubuntu-latest
    environment: publish
    steps:
      - uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871  # v4.2.1
      - uses: actions/setup-node@0a44ba7841725637a19e28fa30b79a866c81b0a6  # v4.0.4
        with:
          node-version: 20
          registry-url: https://registry.npmjs.org/
      - run: npm ci
      - run: npm publish --verbose
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_CCIP_TOOLS_TS}}
