/*!
 * @license :@maticnetwork/maticjs-web3 - V1.0.5 - 07/08/2024
 * https://github.com/maticnetwork/maticjs-web3
 * Copyright (c) 2024 @polygon; Licensed MIT
 */
module.exports=function(t){var i={};function r(n){if(i[n])return i[n].exports;var e=i[n]={i:n,l:!1,exports:{}};return t[n].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=t,r.c=i,r.d=function(t,i,n){r.o(t,i)||Object.defineProperty(t,i,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,i){if(1&i&&(t=r(t)),8&i)return t;if(4&i&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&i&&"string"!=typeof t)for(var e in t)r.d(n,e,function(i){return t[i]}.bind(null,e));return n},r.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(i,"a",i),i},r.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},r.p="",r(r.s=5)}([function(t,i,r){(function(t){!function(t,i){"use strict";function n(t,i){if(!t)throw new Error(i||"Assertion failed")}function e(t,i){t.super_=i;var r=function(){};r.prototype=i.prototype,t.prototype=new r,t.prototype.constructor=t}function o(t,i,r){if(o.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==i&&"be"!==i||(r=i,i=10),this._init(t||0,i||10,r||"be"))}var h;"object"==typeof t?t.exports=o:i.BN=o,o.BN=o,o.wordSize=26;try{h="undefined"!=typeof window&&void 0!==window.Buffer?window.Buffer:r(4).Buffer}catch(t){}function s(t,i){var r=t.charCodeAt(i);return r>=65&&r<=70?r-55:r>=97&&r<=102?r-87:r-48&15}function u(t,i,r){var n=s(t,r);return r-1>=i&&(n|=s(t,r-1)<<4),n}function a(t,i,r,n){for(var e=0,o=Math.min(t.length,r),h=i;h<o;h++){var s=t.charCodeAt(h)-48;e*=n,e+=s>=49?s-49+10:s>=17?s-17+10:s}return e}o.isBN=function(t){return t instanceof o||null!==t&&"object"==typeof t&&t.constructor.wordSize===o.wordSize&&Array.isArray(t.words)},o.max=function(t,i){return t.cmp(i)>0?t:i},o.min=function(t,i){return t.cmp(i)<0?t:i},o.prototype._init=function(t,i,r){if("number"==typeof t)return this._initNumber(t,i,r);if("object"==typeof t)return this._initArray(t,i,r);"hex"===i&&(i=16),n(i===(0|i)&&i>=2&&i<=36);var e=0;"-"===(t=t.toString().replace(/\s+/g,""))[0]&&(e++,this.negative=1),e<t.length&&(16===i?this._parseHex(t,e,r):(this._parseBase(t,i,e),"le"===r&&this._initArray(this.toArray(),i,r)))},o.prototype._initNumber=function(t,i,r){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(n(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===r&&this._initArray(this.toArray(),i,r)},o.prototype._initArray=function(t,i,r){if(n("number"==typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var e=0;e<this.length;e++)this.words[e]=0;var o,h,s=0;if("be"===r)for(e=t.length-1,o=0;e>=0;e-=3)h=t[e]|t[e-1]<<8|t[e-2]<<16,this.words[o]|=h<<s&67108863,this.words[o+1]=h>>>26-s&67108863,(s+=24)>=26&&(s-=26,o++);else if("le"===r)for(e=0,o=0;e<t.length;e+=3)h=t[e]|t[e+1]<<8|t[e+2]<<16,this.words[o]|=h<<s&67108863,this.words[o+1]=h>>>26-s&67108863,(s+=24)>=26&&(s-=26,o++);return this.strip()},o.prototype._parseHex=function(t,i,r){this.length=Math.ceil((t.length-i)/6),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var e,o=0,h=0;if("be"===r)for(n=t.length-1;n>=i;n-=2)e=u(t,i,n)<<o,this.words[h]|=67108863&e,o>=18?(o-=18,h+=1,this.words[h]|=e>>>26):o+=8;else for(n=(t.length-i)%2==0?i+1:i;n<t.length;n+=2)e=u(t,i,n)<<o,this.words[h]|=67108863&e,o>=18?(o-=18,h+=1,this.words[h]|=e>>>26):o+=8;this.strip()},o.prototype._parseBase=function(t,i,r){this.words=[0],this.length=1;for(var n=0,e=1;e<=67108863;e*=i)n++;n--,e=e/i|0;for(var o=t.length-r,h=o%n,s=Math.min(o,o-h)+r,u=0,l=r;l<s;l+=n)u=a(t,l,l+n,i),this.imuln(e),this.words[0]+u<67108864?this.words[0]+=u:this._iaddn(u);if(0!==h){var f=1;for(u=a(t,l,t.length,i),l=0;l<h;l++)f*=i;this.imuln(f),this.words[0]+u<67108864?this.words[0]+=u:this._iaddn(u)}this.strip()},o.prototype.copy=function(t){t.words=new Array(this.length);for(var i=0;i<this.length;i++)t.words[i]=this.words[i];t.length=this.length,t.negative=this.negative,t.red=this.red},o.prototype.clone=function(){var t=new o(null);return this.copy(t),t},o.prototype._expand=function(t){for(;this.length<t;)this.words[this.length++]=0;return this},o.prototype.strip=function(){for(;this.length>1&&0===this.words[this.length-1];)this.length--;return this._normSign()},o.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},o.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var l=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],f=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],m=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function p(t,i,r){r.negative=i.negative^t.negative;var n=t.length+i.length|0;r.length=n,n=n-1|0;var e=0|t.words[0],o=0|i.words[0],h=e*o,s=67108863&h,u=h/67108864|0;r.words[0]=s;for(var a=1;a<n;a++){for(var l=u>>>26,f=67108863&u,m=Math.min(a,i.length-1),p=Math.max(0,a-t.length+1);p<=m;p++){var c=a-p|0;l+=(h=(e=0|t.words[c])*(o=0|i.words[p])+f)/67108864|0,f=67108863&h}r.words[a]=0|f,u=0|l}return 0!==u?r.words[a]=0|u:r.length--,r.strip()}o.prototype.toString=function(t,i){var r;if(i=0|i||1,16===(t=t||10)||"hex"===t){r="";for(var e=0,o=0,h=0;h<this.length;h++){var s=this.words[h],u=(16777215&(s<<e|o)).toString(16);r=0!==(o=s>>>24-e&16777215)||h!==this.length-1?l[6-u.length]+u+r:u+r,(e+=2)>=26&&(e-=26,h--)}for(0!==o&&(r=o.toString(16)+r);r.length%i!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}if(t===(0|t)&&t>=2&&t<=36){var a=f[t],p=m[t];r="";var c=this.clone();for(c.negative=0;!c.isZero();){var d=c.modn(p).toString(t);r=(c=c.idivn(p)).isZero()?d+r:l[a-d.length]+d+r}for(this.isZero()&&(r="0"+r);r.length%i!=0;)r="0"+r;return 0!==this.negative&&(r="-"+r),r}n(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},o.prototype.toJSON=function(){return this.toString(16)},o.prototype.toBuffer=function(t,i){return n(void 0!==h),this.toArrayLike(h,t,i)},o.prototype.toArray=function(t,i){return this.toArrayLike(Array,t,i)},o.prototype.toArrayLike=function(t,i,r){var e=this.byteLength(),o=r||Math.max(1,e);n(e<=o,"byte array longer than desired length"),n(o>0,"Requested array length <= 0"),this.strip();var h,s,u="le"===i,a=new t(o),l=this.clone();if(u){for(s=0;!l.isZero();s++)h=l.andln(255),l.iushrn(8),a[s]=h;for(;s<o;s++)a[s]=0}else{for(s=0;s<o-e;s++)a[s]=0;for(s=0;!l.isZero();s++)h=l.andln(255),l.iushrn(8),a[o-s-1]=h}return a},Math.clz32?o.prototype._countBits=function(t){return 32-Math.clz32(t)}:o.prototype._countBits=function(t){var i=t,r=0;return i>=4096&&(r+=13,i>>>=13),i>=64&&(r+=7,i>>>=7),i>=8&&(r+=4,i>>>=4),i>=2&&(r+=2,i>>>=2),r+i},o.prototype._zeroBits=function(t){if(0===t)return 26;var i=t,r=0;return 0==(8191&i)&&(r+=13,i>>>=13),0==(127&i)&&(r+=7,i>>>=7),0==(15&i)&&(r+=4,i>>>=4),0==(3&i)&&(r+=2,i>>>=2),0==(1&i)&&r++,r},o.prototype.bitLength=function(){var t=this.words[this.length-1],i=this._countBits(t);return 26*(this.length-1)+i},o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,i=0;i<this.length;i++){var r=this._zeroBits(this.words[i]);if(t+=r,26!==r)break}return t},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},o.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return 0!==this.negative},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(t){for(;this.length<t.length;)this.words[this.length++]=0;for(var i=0;i<t.length;i++)this.words[i]=this.words[i]|t.words[i];return this.strip()},o.prototype.ior=function(t){return n(0==(this.negative|t.negative)),this.iuor(t)},o.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},o.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},o.prototype.iuand=function(t){var i;i=this.length>t.length?t:this;for(var r=0;r<i.length;r++)this.words[r]=this.words[r]&t.words[r];return this.length=i.length,this.strip()},o.prototype.iand=function(t){return n(0==(this.negative|t.negative)),this.iuand(t)},o.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},o.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},o.prototype.iuxor=function(t){var i,r;this.length>t.length?(i=this,r=t):(i=t,r=this);for(var n=0;n<r.length;n++)this.words[n]=i.words[n]^r.words[n];if(this!==i)for(;n<i.length;n++)this.words[n]=i.words[n];return this.length=i.length,this.strip()},o.prototype.ixor=function(t){return n(0==(this.negative|t.negative)),this.iuxor(t)},o.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},o.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},o.prototype.inotn=function(t){n("number"==typeof t&&t>=0);var i=0|Math.ceil(t/26),r=t%26;this._expand(i),r>0&&i--;for(var e=0;e<i;e++)this.words[e]=67108863&~this.words[e];return r>0&&(this.words[e]=~this.words[e]&67108863>>26-r),this.strip()},o.prototype.notn=function(t){return this.clone().inotn(t)},o.prototype.setn=function(t,i){n("number"==typeof t&&t>=0);var r=t/26|0,e=t%26;return this._expand(r+1),this.words[r]=i?this.words[r]|1<<e:this.words[r]&~(1<<e),this.strip()},o.prototype.iadd=function(t){var i,r,n;if(0!==this.negative&&0===t.negative)return this.negative=0,i=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,i=this.isub(t),t.negative=1,i._normSign();this.length>t.length?(r=this,n=t):(r=t,n=this);for(var e=0,o=0;o<n.length;o++)i=(0|r.words[o])+(0|n.words[o])+e,this.words[o]=67108863&i,e=i>>>26;for(;0!==e&&o<r.length;o++)i=(0|r.words[o])+e,this.words[o]=67108863&i,e=i>>>26;if(this.length=r.length,0!==e)this.words[this.length]=e,this.length++;else if(r!==this)for(;o<r.length;o++)this.words[o]=r.words[o];return this},o.prototype.add=function(t){var i;return 0!==t.negative&&0===this.negative?(t.negative=0,i=this.sub(t),t.negative^=1,i):0===t.negative&&0!==this.negative?(this.negative=0,i=t.sub(this),this.negative=1,i):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},o.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var i=this.iadd(t);return t.negative=1,i._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var r,n,e=this.cmp(t);if(0===e)return this.negative=0,this.length=1,this.words[0]=0,this;e>0?(r=this,n=t):(r=t,n=this);for(var o=0,h=0;h<n.length;h++)o=(i=(0|r.words[h])-(0|n.words[h])+o)>>26,this.words[h]=67108863&i;for(;0!==o&&h<r.length;h++)o=(i=(0|r.words[h])+o)>>26,this.words[h]=67108863&i;if(0===o&&h<r.length&&r!==this)for(;h<r.length;h++)this.words[h]=r.words[h];return this.length=Math.max(this.length,h),r!==this&&(this.negative=1),this.strip()},o.prototype.sub=function(t){return this.clone().isub(t)};var c=function(t,i,r){var n,e,o,h=t.words,s=i.words,u=r.words,a=0,l=0|h[0],f=8191&l,m=l>>>13,p=0|h[1],c=8191&p,d=p>>>13,g=0|h[2],v=8191&g,M=g>>>13,w=0|h[3],y=8191&w,b=w>>>13,_=0|h[4],x=8191&_,k=_>>>13,S=0|h[5],A=8191&S,T=S>>>13,B=0|h[6],P=8191&B,N=B>>>13,R=0|h[7],O=8191&R,q=R>>>13,j=0|h[8],Z=8191&j,C=j>>>13,E=0|h[9],I=8191&E,L=E>>>13,H=0|s[0],G=8191&H,z=H>>>13,F=0|s[1],W=8191&F,D=F>>>13,U=0|s[2],K=8191&U,J=U>>>13,Q=0|s[3],V=8191&Q,X=Q>>>13,Y=0|s[4],$=8191&Y,tt=Y>>>13,it=0|s[5],rt=8191&it,nt=it>>>13,et=0|s[6],ot=8191&et,ht=et>>>13,st=0|s[7],ut=8191&st,at=st>>>13,lt=0|s[8],ft=8191&lt,mt=lt>>>13,pt=0|s[9],ct=8191&pt,dt=pt>>>13;r.negative=t.negative^i.negative,r.length=19;var gt=(a+(n=Math.imul(f,G))|0)+((8191&(e=(e=Math.imul(f,z))+Math.imul(m,G)|0))<<13)|0;a=((o=Math.imul(m,z))+(e>>>13)|0)+(gt>>>26)|0,gt&=67108863,n=Math.imul(c,G),e=(e=Math.imul(c,z))+Math.imul(d,G)|0,o=Math.imul(d,z);var vt=(a+(n=n+Math.imul(f,W)|0)|0)+((8191&(e=(e=e+Math.imul(f,D)|0)+Math.imul(m,W)|0))<<13)|0;a=((o=o+Math.imul(m,D)|0)+(e>>>13)|0)+(vt>>>26)|0,vt&=67108863,n=Math.imul(v,G),e=(e=Math.imul(v,z))+Math.imul(M,G)|0,o=Math.imul(M,z),n=n+Math.imul(c,W)|0,e=(e=e+Math.imul(c,D)|0)+Math.imul(d,W)|0,o=o+Math.imul(d,D)|0;var Mt=(a+(n=n+Math.imul(f,K)|0)|0)+((8191&(e=(e=e+Math.imul(f,J)|0)+Math.imul(m,K)|0))<<13)|0;a=((o=o+Math.imul(m,J)|0)+(e>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,n=Math.imul(y,G),e=(e=Math.imul(y,z))+Math.imul(b,G)|0,o=Math.imul(b,z),n=n+Math.imul(v,W)|0,e=(e=e+Math.imul(v,D)|0)+Math.imul(M,W)|0,o=o+Math.imul(M,D)|0,n=n+Math.imul(c,K)|0,e=(e=e+Math.imul(c,J)|0)+Math.imul(d,K)|0,o=o+Math.imul(d,J)|0;var wt=(a+(n=n+Math.imul(f,V)|0)|0)+((8191&(e=(e=e+Math.imul(f,X)|0)+Math.imul(m,V)|0))<<13)|0;a=((o=o+Math.imul(m,X)|0)+(e>>>13)|0)+(wt>>>26)|0,wt&=67108863,n=Math.imul(x,G),e=(e=Math.imul(x,z))+Math.imul(k,G)|0,o=Math.imul(k,z),n=n+Math.imul(y,W)|0,e=(e=e+Math.imul(y,D)|0)+Math.imul(b,W)|0,o=o+Math.imul(b,D)|0,n=n+Math.imul(v,K)|0,e=(e=e+Math.imul(v,J)|0)+Math.imul(M,K)|0,o=o+Math.imul(M,J)|0,n=n+Math.imul(c,V)|0,e=(e=e+Math.imul(c,X)|0)+Math.imul(d,V)|0,o=o+Math.imul(d,X)|0;var yt=(a+(n=n+Math.imul(f,$)|0)|0)+((8191&(e=(e=e+Math.imul(f,tt)|0)+Math.imul(m,$)|0))<<13)|0;a=((o=o+Math.imul(m,tt)|0)+(e>>>13)|0)+(yt>>>26)|0,yt&=67108863,n=Math.imul(A,G),e=(e=Math.imul(A,z))+Math.imul(T,G)|0,o=Math.imul(T,z),n=n+Math.imul(x,W)|0,e=(e=e+Math.imul(x,D)|0)+Math.imul(k,W)|0,o=o+Math.imul(k,D)|0,n=n+Math.imul(y,K)|0,e=(e=e+Math.imul(y,J)|0)+Math.imul(b,K)|0,o=o+Math.imul(b,J)|0,n=n+Math.imul(v,V)|0,e=(e=e+Math.imul(v,X)|0)+Math.imul(M,V)|0,o=o+Math.imul(M,X)|0,n=n+Math.imul(c,$)|0,e=(e=e+Math.imul(c,tt)|0)+Math.imul(d,$)|0,o=o+Math.imul(d,tt)|0;var bt=(a+(n=n+Math.imul(f,rt)|0)|0)+((8191&(e=(e=e+Math.imul(f,nt)|0)+Math.imul(m,rt)|0))<<13)|0;a=((o=o+Math.imul(m,nt)|0)+(e>>>13)|0)+(bt>>>26)|0,bt&=67108863,n=Math.imul(P,G),e=(e=Math.imul(P,z))+Math.imul(N,G)|0,o=Math.imul(N,z),n=n+Math.imul(A,W)|0,e=(e=e+Math.imul(A,D)|0)+Math.imul(T,W)|0,o=o+Math.imul(T,D)|0,n=n+Math.imul(x,K)|0,e=(e=e+Math.imul(x,J)|0)+Math.imul(k,K)|0,o=o+Math.imul(k,J)|0,n=n+Math.imul(y,V)|0,e=(e=e+Math.imul(y,X)|0)+Math.imul(b,V)|0,o=o+Math.imul(b,X)|0,n=n+Math.imul(v,$)|0,e=(e=e+Math.imul(v,tt)|0)+Math.imul(M,$)|0,o=o+Math.imul(M,tt)|0,n=n+Math.imul(c,rt)|0,e=(e=e+Math.imul(c,nt)|0)+Math.imul(d,rt)|0,o=o+Math.imul(d,nt)|0;var _t=(a+(n=n+Math.imul(f,ot)|0)|0)+((8191&(e=(e=e+Math.imul(f,ht)|0)+Math.imul(m,ot)|0))<<13)|0;a=((o=o+Math.imul(m,ht)|0)+(e>>>13)|0)+(_t>>>26)|0,_t&=67108863,n=Math.imul(O,G),e=(e=Math.imul(O,z))+Math.imul(q,G)|0,o=Math.imul(q,z),n=n+Math.imul(P,W)|0,e=(e=e+Math.imul(P,D)|0)+Math.imul(N,W)|0,o=o+Math.imul(N,D)|0,n=n+Math.imul(A,K)|0,e=(e=e+Math.imul(A,J)|0)+Math.imul(T,K)|0,o=o+Math.imul(T,J)|0,n=n+Math.imul(x,V)|0,e=(e=e+Math.imul(x,X)|0)+Math.imul(k,V)|0,o=o+Math.imul(k,X)|0,n=n+Math.imul(y,$)|0,e=(e=e+Math.imul(y,tt)|0)+Math.imul(b,$)|0,o=o+Math.imul(b,tt)|0,n=n+Math.imul(v,rt)|0,e=(e=e+Math.imul(v,nt)|0)+Math.imul(M,rt)|0,o=o+Math.imul(M,nt)|0,n=n+Math.imul(c,ot)|0,e=(e=e+Math.imul(c,ht)|0)+Math.imul(d,ot)|0,o=o+Math.imul(d,ht)|0;var xt=(a+(n=n+Math.imul(f,ut)|0)|0)+((8191&(e=(e=e+Math.imul(f,at)|0)+Math.imul(m,ut)|0))<<13)|0;a=((o=o+Math.imul(m,at)|0)+(e>>>13)|0)+(xt>>>26)|0,xt&=67108863,n=Math.imul(Z,G),e=(e=Math.imul(Z,z))+Math.imul(C,G)|0,o=Math.imul(C,z),n=n+Math.imul(O,W)|0,e=(e=e+Math.imul(O,D)|0)+Math.imul(q,W)|0,o=o+Math.imul(q,D)|0,n=n+Math.imul(P,K)|0,e=(e=e+Math.imul(P,J)|0)+Math.imul(N,K)|0,o=o+Math.imul(N,J)|0,n=n+Math.imul(A,V)|0,e=(e=e+Math.imul(A,X)|0)+Math.imul(T,V)|0,o=o+Math.imul(T,X)|0,n=n+Math.imul(x,$)|0,e=(e=e+Math.imul(x,tt)|0)+Math.imul(k,$)|0,o=o+Math.imul(k,tt)|0,n=n+Math.imul(y,rt)|0,e=(e=e+Math.imul(y,nt)|0)+Math.imul(b,rt)|0,o=o+Math.imul(b,nt)|0,n=n+Math.imul(v,ot)|0,e=(e=e+Math.imul(v,ht)|0)+Math.imul(M,ot)|0,o=o+Math.imul(M,ht)|0,n=n+Math.imul(c,ut)|0,e=(e=e+Math.imul(c,at)|0)+Math.imul(d,ut)|0,o=o+Math.imul(d,at)|0;var kt=(a+(n=n+Math.imul(f,ft)|0)|0)+((8191&(e=(e=e+Math.imul(f,mt)|0)+Math.imul(m,ft)|0))<<13)|0;a=((o=o+Math.imul(m,mt)|0)+(e>>>13)|0)+(kt>>>26)|0,kt&=67108863,n=Math.imul(I,G),e=(e=Math.imul(I,z))+Math.imul(L,G)|0,o=Math.imul(L,z),n=n+Math.imul(Z,W)|0,e=(e=e+Math.imul(Z,D)|0)+Math.imul(C,W)|0,o=o+Math.imul(C,D)|0,n=n+Math.imul(O,K)|0,e=(e=e+Math.imul(O,J)|0)+Math.imul(q,K)|0,o=o+Math.imul(q,J)|0,n=n+Math.imul(P,V)|0,e=(e=e+Math.imul(P,X)|0)+Math.imul(N,V)|0,o=o+Math.imul(N,X)|0,n=n+Math.imul(A,$)|0,e=(e=e+Math.imul(A,tt)|0)+Math.imul(T,$)|0,o=o+Math.imul(T,tt)|0,n=n+Math.imul(x,rt)|0,e=(e=e+Math.imul(x,nt)|0)+Math.imul(k,rt)|0,o=o+Math.imul(k,nt)|0,n=n+Math.imul(y,ot)|0,e=(e=e+Math.imul(y,ht)|0)+Math.imul(b,ot)|0,o=o+Math.imul(b,ht)|0,n=n+Math.imul(v,ut)|0,e=(e=e+Math.imul(v,at)|0)+Math.imul(M,ut)|0,o=o+Math.imul(M,at)|0,n=n+Math.imul(c,ft)|0,e=(e=e+Math.imul(c,mt)|0)+Math.imul(d,ft)|0,o=o+Math.imul(d,mt)|0;var St=(a+(n=n+Math.imul(f,ct)|0)|0)+((8191&(e=(e=e+Math.imul(f,dt)|0)+Math.imul(m,ct)|0))<<13)|0;a=((o=o+Math.imul(m,dt)|0)+(e>>>13)|0)+(St>>>26)|0,St&=67108863,n=Math.imul(I,W),e=(e=Math.imul(I,D))+Math.imul(L,W)|0,o=Math.imul(L,D),n=n+Math.imul(Z,K)|0,e=(e=e+Math.imul(Z,J)|0)+Math.imul(C,K)|0,o=o+Math.imul(C,J)|0,n=n+Math.imul(O,V)|0,e=(e=e+Math.imul(O,X)|0)+Math.imul(q,V)|0,o=o+Math.imul(q,X)|0,n=n+Math.imul(P,$)|0,e=(e=e+Math.imul(P,tt)|0)+Math.imul(N,$)|0,o=o+Math.imul(N,tt)|0,n=n+Math.imul(A,rt)|0,e=(e=e+Math.imul(A,nt)|0)+Math.imul(T,rt)|0,o=o+Math.imul(T,nt)|0,n=n+Math.imul(x,ot)|0,e=(e=e+Math.imul(x,ht)|0)+Math.imul(k,ot)|0,o=o+Math.imul(k,ht)|0,n=n+Math.imul(y,ut)|0,e=(e=e+Math.imul(y,at)|0)+Math.imul(b,ut)|0,o=o+Math.imul(b,at)|0,n=n+Math.imul(v,ft)|0,e=(e=e+Math.imul(v,mt)|0)+Math.imul(M,ft)|0,o=o+Math.imul(M,mt)|0;var At=(a+(n=n+Math.imul(c,ct)|0)|0)+((8191&(e=(e=e+Math.imul(c,dt)|0)+Math.imul(d,ct)|0))<<13)|0;a=((o=o+Math.imul(d,dt)|0)+(e>>>13)|0)+(At>>>26)|0,At&=67108863,n=Math.imul(I,K),e=(e=Math.imul(I,J))+Math.imul(L,K)|0,o=Math.imul(L,J),n=n+Math.imul(Z,V)|0,e=(e=e+Math.imul(Z,X)|0)+Math.imul(C,V)|0,o=o+Math.imul(C,X)|0,n=n+Math.imul(O,$)|0,e=(e=e+Math.imul(O,tt)|0)+Math.imul(q,$)|0,o=o+Math.imul(q,tt)|0,n=n+Math.imul(P,rt)|0,e=(e=e+Math.imul(P,nt)|0)+Math.imul(N,rt)|0,o=o+Math.imul(N,nt)|0,n=n+Math.imul(A,ot)|0,e=(e=e+Math.imul(A,ht)|0)+Math.imul(T,ot)|0,o=o+Math.imul(T,ht)|0,n=n+Math.imul(x,ut)|0,e=(e=e+Math.imul(x,at)|0)+Math.imul(k,ut)|0,o=o+Math.imul(k,at)|0,n=n+Math.imul(y,ft)|0,e=(e=e+Math.imul(y,mt)|0)+Math.imul(b,ft)|0,o=o+Math.imul(b,mt)|0;var Tt=(a+(n=n+Math.imul(v,ct)|0)|0)+((8191&(e=(e=e+Math.imul(v,dt)|0)+Math.imul(M,ct)|0))<<13)|0;a=((o=o+Math.imul(M,dt)|0)+(e>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,n=Math.imul(I,V),e=(e=Math.imul(I,X))+Math.imul(L,V)|0,o=Math.imul(L,X),n=n+Math.imul(Z,$)|0,e=(e=e+Math.imul(Z,tt)|0)+Math.imul(C,$)|0,o=o+Math.imul(C,tt)|0,n=n+Math.imul(O,rt)|0,e=(e=e+Math.imul(O,nt)|0)+Math.imul(q,rt)|0,o=o+Math.imul(q,nt)|0,n=n+Math.imul(P,ot)|0,e=(e=e+Math.imul(P,ht)|0)+Math.imul(N,ot)|0,o=o+Math.imul(N,ht)|0,n=n+Math.imul(A,ut)|0,e=(e=e+Math.imul(A,at)|0)+Math.imul(T,ut)|0,o=o+Math.imul(T,at)|0,n=n+Math.imul(x,ft)|0,e=(e=e+Math.imul(x,mt)|0)+Math.imul(k,ft)|0,o=o+Math.imul(k,mt)|0;var Bt=(a+(n=n+Math.imul(y,ct)|0)|0)+((8191&(e=(e=e+Math.imul(y,dt)|0)+Math.imul(b,ct)|0))<<13)|0;a=((o=o+Math.imul(b,dt)|0)+(e>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,n=Math.imul(I,$),e=(e=Math.imul(I,tt))+Math.imul(L,$)|0,o=Math.imul(L,tt),n=n+Math.imul(Z,rt)|0,e=(e=e+Math.imul(Z,nt)|0)+Math.imul(C,rt)|0,o=o+Math.imul(C,nt)|0,n=n+Math.imul(O,ot)|0,e=(e=e+Math.imul(O,ht)|0)+Math.imul(q,ot)|0,o=o+Math.imul(q,ht)|0,n=n+Math.imul(P,ut)|0,e=(e=e+Math.imul(P,at)|0)+Math.imul(N,ut)|0,o=o+Math.imul(N,at)|0,n=n+Math.imul(A,ft)|0,e=(e=e+Math.imul(A,mt)|0)+Math.imul(T,ft)|0,o=o+Math.imul(T,mt)|0;var Pt=(a+(n=n+Math.imul(x,ct)|0)|0)+((8191&(e=(e=e+Math.imul(x,dt)|0)+Math.imul(k,ct)|0))<<13)|0;a=((o=o+Math.imul(k,dt)|0)+(e>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,n=Math.imul(I,rt),e=(e=Math.imul(I,nt))+Math.imul(L,rt)|0,o=Math.imul(L,nt),n=n+Math.imul(Z,ot)|0,e=(e=e+Math.imul(Z,ht)|0)+Math.imul(C,ot)|0,o=o+Math.imul(C,ht)|0,n=n+Math.imul(O,ut)|0,e=(e=e+Math.imul(O,at)|0)+Math.imul(q,ut)|0,o=o+Math.imul(q,at)|0,n=n+Math.imul(P,ft)|0,e=(e=e+Math.imul(P,mt)|0)+Math.imul(N,ft)|0,o=o+Math.imul(N,mt)|0;var Nt=(a+(n=n+Math.imul(A,ct)|0)|0)+((8191&(e=(e=e+Math.imul(A,dt)|0)+Math.imul(T,ct)|0))<<13)|0;a=((o=o+Math.imul(T,dt)|0)+(e>>>13)|0)+(Nt>>>26)|0,Nt&=67108863,n=Math.imul(I,ot),e=(e=Math.imul(I,ht))+Math.imul(L,ot)|0,o=Math.imul(L,ht),n=n+Math.imul(Z,ut)|0,e=(e=e+Math.imul(Z,at)|0)+Math.imul(C,ut)|0,o=o+Math.imul(C,at)|0,n=n+Math.imul(O,ft)|0,e=(e=e+Math.imul(O,mt)|0)+Math.imul(q,ft)|0,o=o+Math.imul(q,mt)|0;var Rt=(a+(n=n+Math.imul(P,ct)|0)|0)+((8191&(e=(e=e+Math.imul(P,dt)|0)+Math.imul(N,ct)|0))<<13)|0;a=((o=o+Math.imul(N,dt)|0)+(e>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,n=Math.imul(I,ut),e=(e=Math.imul(I,at))+Math.imul(L,ut)|0,o=Math.imul(L,at),n=n+Math.imul(Z,ft)|0,e=(e=e+Math.imul(Z,mt)|0)+Math.imul(C,ft)|0,o=o+Math.imul(C,mt)|0;var Ot=(a+(n=n+Math.imul(O,ct)|0)|0)+((8191&(e=(e=e+Math.imul(O,dt)|0)+Math.imul(q,ct)|0))<<13)|0;a=((o=o+Math.imul(q,dt)|0)+(e>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,n=Math.imul(I,ft),e=(e=Math.imul(I,mt))+Math.imul(L,ft)|0,o=Math.imul(L,mt);var qt=(a+(n=n+Math.imul(Z,ct)|0)|0)+((8191&(e=(e=e+Math.imul(Z,dt)|0)+Math.imul(C,ct)|0))<<13)|0;a=((o=o+Math.imul(C,dt)|0)+(e>>>13)|0)+(qt>>>26)|0,qt&=67108863;var jt=(a+(n=Math.imul(I,ct))|0)+((8191&(e=(e=Math.imul(I,dt))+Math.imul(L,ct)|0))<<13)|0;return a=((o=Math.imul(L,dt))+(e>>>13)|0)+(jt>>>26)|0,jt&=67108863,u[0]=gt,u[1]=vt,u[2]=Mt,u[3]=wt,u[4]=yt,u[5]=bt,u[6]=_t,u[7]=xt,u[8]=kt,u[9]=St,u[10]=At,u[11]=Tt,u[12]=Bt,u[13]=Pt,u[14]=Nt,u[15]=Rt,u[16]=Ot,u[17]=qt,u[18]=jt,0!==a&&(u[19]=a,r.length++),r};function d(t,i,r){return(new g).mulp(t,i,r)}function g(t,i){this.x=t,this.y=i}Math.imul||(c=p),o.prototype.mulTo=function(t,i){var r=this.length+t.length;return 10===this.length&&10===t.length?c(this,t,i):r<63?p(this,t,i):r<1024?function(t,i,r){r.negative=i.negative^t.negative,r.length=t.length+i.length;for(var n=0,e=0,o=0;o<r.length-1;o++){var h=e;e=0;for(var s=67108863&n,u=Math.min(o,i.length-1),a=Math.max(0,o-t.length+1);a<=u;a++){var l=o-a,f=(0|t.words[l])*(0|i.words[a]),m=67108863&f;s=67108863&(m=m+s|0),e+=(h=(h=h+(f/67108864|0)|0)+(m>>>26)|0)>>>26,h&=67108863}r.words[o]=s,n=h,h=e}return 0!==n?r.words[o]=n:r.length--,r.strip()}(this,t,i):d(this,t,i)},g.prototype.makeRBT=function(t){for(var i=new Array(t),r=o.prototype._countBits(t)-1,n=0;n<t;n++)i[n]=this.revBin(n,r,t);return i},g.prototype.revBin=function(t,i,r){if(0===t||t===r-1)return t;for(var n=0,e=0;e<i;e++)n|=(1&t)<<i-e-1,t>>=1;return n},g.prototype.permute=function(t,i,r,n,e,o){for(var h=0;h<o;h++)n[h]=i[t[h]],e[h]=r[t[h]]},g.prototype.transform=function(t,i,r,n,e,o){this.permute(o,t,i,r,n,e);for(var h=1;h<e;h<<=1)for(var s=h<<1,u=Math.cos(2*Math.PI/s),a=Math.sin(2*Math.PI/s),l=0;l<e;l+=s)for(var f=u,m=a,p=0;p<h;p++){var c=r[l+p],d=n[l+p],g=r[l+p+h],v=n[l+p+h],M=f*g-m*v;v=f*v+m*g,g=M,r[l+p]=c+g,n[l+p]=d+v,r[l+p+h]=c-g,n[l+p+h]=d-v,p!==s&&(M=u*f-a*m,m=u*m+a*f,f=M)}},g.prototype.guessLen13b=function(t,i){var r=1|Math.max(i,t),n=1&r,e=0;for(r=r/2|0;r;r>>>=1)e++;return 1<<e+1+n},g.prototype.conjugate=function(t,i,r){if(!(r<=1))for(var n=0;n<r/2;n++){var e=t[n];t[n]=t[r-n-1],t[r-n-1]=e,e=i[n],i[n]=-i[r-n-1],i[r-n-1]=-e}},g.prototype.normalize13b=function(t,i){for(var r=0,n=0;n<i/2;n++){var e=8192*Math.round(t[2*n+1]/i)+Math.round(t[2*n]/i)+r;t[n]=67108863&e,r=e<67108864?0:e/67108864|0}return t},g.prototype.convert13b=function(t,i,r,e){for(var o=0,h=0;h<i;h++)o+=0|t[h],r[2*h]=8191&o,o>>>=13,r[2*h+1]=8191&o,o>>>=13;for(h=2*i;h<e;++h)r[h]=0;n(0===o),n(0==(-8192&o))},g.prototype.stub=function(t){for(var i=new Array(t),r=0;r<t;r++)i[r]=0;return i},g.prototype.mulp=function(t,i,r){var n=2*this.guessLen13b(t.length,i.length),e=this.makeRBT(n),o=this.stub(n),h=new Array(n),s=new Array(n),u=new Array(n),a=new Array(n),l=new Array(n),f=new Array(n),m=r.words;m.length=n,this.convert13b(t.words,t.length,h,n),this.convert13b(i.words,i.length,a,n),this.transform(h,o,s,u,n,e),this.transform(a,o,l,f,n,e);for(var p=0;p<n;p++){var c=s[p]*l[p]-u[p]*f[p];u[p]=s[p]*f[p]+u[p]*l[p],s[p]=c}return this.conjugate(s,u,n),this.transform(s,u,m,o,n,e),this.conjugate(m,o,n),this.normalize13b(m,n),r.negative=t.negative^i.negative,r.length=t.length+i.length,r.strip()},o.prototype.mul=function(t){var i=new o(null);return i.words=new Array(this.length+t.length),this.mulTo(t,i)},o.prototype.mulf=function(t){var i=new o(null);return i.words=new Array(this.length+t.length),d(this,t,i)},o.prototype.imul=function(t){return this.clone().mulTo(t,this)},o.prototype.imuln=function(t){n("number"==typeof t),n(t<67108864);for(var i=0,r=0;r<this.length;r++){var e=(0|this.words[r])*t,o=(67108863&e)+(67108863&i);i>>=26,i+=e/67108864|0,i+=o>>>26,this.words[r]=67108863&o}return 0!==i&&(this.words[r]=i,this.length++),this},o.prototype.muln=function(t){return this.clone().imuln(t)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(t){var i=function(t){for(var i=new Array(t.bitLength()),r=0;r<i.length;r++){var n=r/26|0,e=r%26;i[r]=(t.words[n]&1<<e)>>>e}return i}(t);if(0===i.length)return new o(1);for(var r=this,n=0;n<i.length&&0===i[n];n++,r=r.sqr());if(++n<i.length)for(var e=r.sqr();n<i.length;n++,e=e.sqr())0!==i[n]&&(r=r.mul(e));return r},o.prototype.iushln=function(t){n("number"==typeof t&&t>=0);var i,r=t%26,e=(t-r)/26,o=67108863>>>26-r<<26-r;if(0!==r){var h=0;for(i=0;i<this.length;i++){var s=this.words[i]&o,u=(0|this.words[i])-s<<r;this.words[i]=u|h,h=s>>>26-r}h&&(this.words[i]=h,this.length++)}if(0!==e){for(i=this.length-1;i>=0;i--)this.words[i+e]=this.words[i];for(i=0;i<e;i++)this.words[i]=0;this.length+=e}return this.strip()},o.prototype.ishln=function(t){return n(0===this.negative),this.iushln(t)},o.prototype.iushrn=function(t,i,r){var e;n("number"==typeof t&&t>=0),e=i?(i-i%26)/26:0;var o=t%26,h=Math.min((t-o)/26,this.length),s=67108863^67108863>>>o<<o,u=r;if(e-=h,e=Math.max(0,e),u){for(var a=0;a<h;a++)u.words[a]=this.words[a];u.length=h}if(0===h);else if(this.length>h)for(this.length-=h,a=0;a<this.length;a++)this.words[a]=this.words[a+h];else this.words[0]=0,this.length=1;var l=0;for(a=this.length-1;a>=0&&(0!==l||a>=e);a--){var f=0|this.words[a];this.words[a]=l<<26-o|f>>>o,l=f&s}return u&&0!==l&&(u.words[u.length++]=l),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},o.prototype.ishrn=function(t,i,r){return n(0===this.negative),this.iushrn(t,i,r)},o.prototype.shln=function(t){return this.clone().ishln(t)},o.prototype.ushln=function(t){return this.clone().iushln(t)},o.prototype.shrn=function(t){return this.clone().ishrn(t)},o.prototype.ushrn=function(t){return this.clone().iushrn(t)},o.prototype.testn=function(t){n("number"==typeof t&&t>=0);var i=t%26,r=(t-i)/26,e=1<<i;return!(this.length<=r)&&!!(this.words[r]&e)},o.prototype.imaskn=function(t){n("number"==typeof t&&t>=0);var i=t%26,r=(t-i)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=r)return this;if(0!==i&&r++,this.length=Math.min(r,this.length),0!==i){var e=67108863^67108863>>>i<<i;this.words[this.length-1]&=e}return this.strip()},o.prototype.maskn=function(t){return this.clone().imaskn(t)},o.prototype.iaddn=function(t){return n("number"==typeof t),n(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},o.prototype._iaddn=function(t){this.words[0]+=t;for(var i=0;i<this.length&&this.words[i]>=67108864;i++)this.words[i]-=67108864,i===this.length-1?this.words[i+1]=1:this.words[i+1]++;return this.length=Math.max(this.length,i+1),this},o.prototype.isubn=function(t){if(n("number"==typeof t),n(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var i=0;i<this.length&&this.words[i]<0;i++)this.words[i]+=67108864,this.words[i+1]-=1;return this.strip()},o.prototype.addn=function(t){return this.clone().iaddn(t)},o.prototype.subn=function(t){return this.clone().isubn(t)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(t,i,r){var e,o,h=t.length+r;this._expand(h);var s=0;for(e=0;e<t.length;e++){o=(0|this.words[e+r])+s;var u=(0|t.words[e])*i;s=((o-=67108863&u)>>26)-(u/67108864|0),this.words[e+r]=67108863&o}for(;e<this.length-r;e++)s=(o=(0|this.words[e+r])+s)>>26,this.words[e+r]=67108863&o;if(0===s)return this.strip();for(n(-1===s),s=0,e=0;e<this.length;e++)s=(o=-(0|this.words[e])+s)>>26,this.words[e]=67108863&o;return this.negative=1,this.strip()},o.prototype._wordDiv=function(t,i){var r=(this.length,t.length),n=this.clone(),e=t,h=0|e.words[e.length-1];0!==(r=26-this._countBits(h))&&(e=e.ushln(r),n.iushln(r),h=0|e.words[e.length-1]);var s,u=n.length-e.length;if("mod"!==i){(s=new o(null)).length=u+1,s.words=new Array(s.length);for(var a=0;a<s.length;a++)s.words[a]=0}var l=n.clone()._ishlnsubmul(e,1,u);0===l.negative&&(n=l,s&&(s.words[u]=1));for(var f=u-1;f>=0;f--){var m=67108864*(0|n.words[e.length+f])+(0|n.words[e.length+f-1]);for(m=Math.min(m/h|0,67108863),n._ishlnsubmul(e,m,f);0!==n.negative;)m--,n.negative=0,n._ishlnsubmul(e,1,f),n.isZero()||(n.negative^=1);s&&(s.words[f]=m)}return s&&s.strip(),n.strip(),"div"!==i&&0!==r&&n.iushrn(r),{div:s||null,mod:n}},o.prototype.divmod=function(t,i,r){return n(!t.isZero()),this.isZero()?{div:new o(0),mod:new o(0)}:0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,i),"mod"!==i&&(e=s.div.neg()),"div"!==i&&(h=s.mod.neg(),r&&0!==h.negative&&h.iadd(t)),{div:e,mod:h}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),i),"mod"!==i&&(e=s.div.neg()),{div:e,mod:s.mod}):0!=(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),i),"div"!==i&&(h=s.mod.neg(),r&&0!==h.negative&&h.isub(t)),{div:s.div,mod:h}):t.length>this.length||this.cmp(t)<0?{div:new o(0),mod:this}:1===t.length?"div"===i?{div:this.divn(t.words[0]),mod:null}:"mod"===i?{div:null,mod:new o(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new o(this.modn(t.words[0]))}:this._wordDiv(t,i);var e,h,s},o.prototype.div=function(t){return this.divmod(t,"div",!1).div},o.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},o.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},o.prototype.divRound=function(t){var i=this.divmod(t);if(i.mod.isZero())return i.div;var r=0!==i.div.negative?i.mod.isub(t):i.mod,n=t.ushrn(1),e=t.andln(1),o=r.cmp(n);return o<0||1===e&&0===o?i.div:0!==i.div.negative?i.div.isubn(1):i.div.iaddn(1)},o.prototype.modn=function(t){n(t<=67108863);for(var i=(1<<26)%t,r=0,e=this.length-1;e>=0;e--)r=(i*r+(0|this.words[e]))%t;return r},o.prototype.idivn=function(t){n(t<=67108863);for(var i=0,r=this.length-1;r>=0;r--){var e=(0|this.words[r])+67108864*i;this.words[r]=e/t|0,i=e%t}return this.strip()},o.prototype.divn=function(t){return this.clone().idivn(t)},o.prototype.egcd=function(t){n(0===t.negative),n(!t.isZero());var i=this,r=t.clone();i=0!==i.negative?i.umod(t):i.clone();for(var e=new o(1),h=new o(0),s=new o(0),u=new o(1),a=0;i.isEven()&&r.isEven();)i.iushrn(1),r.iushrn(1),++a;for(var l=r.clone(),f=i.clone();!i.isZero();){for(var m=0,p=1;0==(i.words[0]&p)&&m<26;++m,p<<=1);if(m>0)for(i.iushrn(m);m-- >0;)(e.isOdd()||h.isOdd())&&(e.iadd(l),h.isub(f)),e.iushrn(1),h.iushrn(1);for(var c=0,d=1;0==(r.words[0]&d)&&c<26;++c,d<<=1);if(c>0)for(r.iushrn(c);c-- >0;)(s.isOdd()||u.isOdd())&&(s.iadd(l),u.isub(f)),s.iushrn(1),u.iushrn(1);i.cmp(r)>=0?(i.isub(r),e.isub(s),h.isub(u)):(r.isub(i),s.isub(e),u.isub(h))}return{a:s,b:u,gcd:r.iushln(a)}},o.prototype._invmp=function(t){n(0===t.negative),n(!t.isZero());var i=this,r=t.clone();i=0!==i.negative?i.umod(t):i.clone();for(var e,h=new o(1),s=new o(0),u=r.clone();i.cmpn(1)>0&&r.cmpn(1)>0;){for(var a=0,l=1;0==(i.words[0]&l)&&a<26;++a,l<<=1);if(a>0)for(i.iushrn(a);a-- >0;)h.isOdd()&&h.iadd(u),h.iushrn(1);for(var f=0,m=1;0==(r.words[0]&m)&&f<26;++f,m<<=1);if(f>0)for(r.iushrn(f);f-- >0;)s.isOdd()&&s.iadd(u),s.iushrn(1);i.cmp(r)>=0?(i.isub(r),h.isub(s)):(r.isub(i),s.isub(h))}return(e=0===i.cmpn(1)?h:s).cmpn(0)<0&&e.iadd(t),e},o.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var i=this.clone(),r=t.clone();i.negative=0,r.negative=0;for(var n=0;i.isEven()&&r.isEven();n++)i.iushrn(1),r.iushrn(1);for(;;){for(;i.isEven();)i.iushrn(1);for(;r.isEven();)r.iushrn(1);var e=i.cmp(r);if(e<0){var o=i;i=r,r=o}else if(0===e||0===r.cmpn(1))break;i.isub(r)}return r.iushln(n)},o.prototype.invm=function(t){return this.egcd(t).a.umod(t)},o.prototype.isEven=function(){return 0==(1&this.words[0])},o.prototype.isOdd=function(){return 1==(1&this.words[0])},o.prototype.andln=function(t){return this.words[0]&t},o.prototype.bincn=function(t){n("number"==typeof t);var i=t%26,r=(t-i)/26,e=1<<i;if(this.length<=r)return this._expand(r+1),this.words[r]|=e,this;for(var o=e,h=r;0!==o&&h<this.length;h++){var s=0|this.words[h];o=(s+=o)>>>26,s&=67108863,this.words[h]=s}return 0!==o&&(this.words[h]=o,this.length++),this},o.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},o.prototype.cmpn=function(t){var i,r=t<0;if(0!==this.negative&&!r)return-1;if(0===this.negative&&r)return 1;if(this.strip(),this.length>1)i=1;else{r&&(t=-t),n(t<=67108863,"Number is too big");var e=0|this.words[0];i=e===t?0:e<t?-1:1}return 0!==this.negative?0|-i:i},o.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var i=this.ucmp(t);return 0!==this.negative?0|-i:i},o.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var i=0,r=this.length-1;r>=0;r--){var n=0|this.words[r],e=0|t.words[r];if(n!==e){n<e?i=-1:n>e&&(i=1);break}}return i},o.prototype.gtn=function(t){return 1===this.cmpn(t)},o.prototype.gt=function(t){return 1===this.cmp(t)},o.prototype.gten=function(t){return this.cmpn(t)>=0},o.prototype.gte=function(t){return this.cmp(t)>=0},o.prototype.ltn=function(t){return-1===this.cmpn(t)},o.prototype.lt=function(t){return-1===this.cmp(t)},o.prototype.lten=function(t){return this.cmpn(t)<=0},o.prototype.lte=function(t){return this.cmp(t)<=0},o.prototype.eqn=function(t){return 0===this.cmpn(t)},o.prototype.eq=function(t){return 0===this.cmp(t)},o.red=function(t){return new x(t)},o.prototype.toRed=function(t){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},o.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(t){return this.red=t,this},o.prototype.forceRed=function(t){return n(!this.red,"Already a number in reduction context"),this._forceRed(t)},o.prototype.redAdd=function(t){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},o.prototype.redIAdd=function(t){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},o.prototype.redSub=function(t){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},o.prototype.redISub=function(t){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},o.prototype.redShl=function(t){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},o.prototype.redMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},o.prototype.redIMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},o.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(t){return n(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var v={k256:null,p224:null,p192:null,p25519:null};function M(t,i){this.name=t,this.p=new o(i,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function w(){M.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function y(){M.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function b(){M.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function _(){M.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function x(t){if("string"==typeof t){var i=o._prime(t);this.m=i.p,this.prime=i}else n(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function k(t){x.call(this,t),this.shift=this.m.bitLength(),this.shift%26!=0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}M.prototype._tmp=function(){var t=new o(null);return t.words=new Array(Math.ceil(this.n/13)),t},M.prototype.ireduce=function(t){var i,r=t;do{this.split(r,this.tmp),i=(r=(r=this.imulK(r)).iadd(this.tmp)).bitLength()}while(i>this.n);var n=i<this.n?-1:r.ucmp(this.p);return 0===n?(r.words[0]=0,r.length=1):n>0?r.isub(this.p):void 0!==r.strip?r.strip():r._strip(),r},M.prototype.split=function(t,i){t.iushrn(this.n,0,i)},M.prototype.imulK=function(t){return t.imul(this.k)},e(w,M),w.prototype.split=function(t,i){for(var r=Math.min(t.length,9),n=0;n<r;n++)i.words[n]=t.words[n];if(i.length=r,t.length<=9)return t.words[0]=0,void(t.length=1);var e=t.words[9];for(i.words[i.length++]=4194303&e,n=10;n<t.length;n++){var o=0|t.words[n];t.words[n-10]=(4194303&o)<<4|e>>>22,e=o}e>>>=22,t.words[n-10]=e,0===e&&t.length>10?t.length-=10:t.length-=9},w.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var i=0,r=0;r<t.length;r++){var n=0|t.words[r];i+=977*n,t.words[r]=67108863&i,i=64*n+(i/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},e(y,M),e(b,M),e(_,M),_.prototype.imulK=function(t){for(var i=0,r=0;r<t.length;r++){var n=19*(0|t.words[r])+i,e=67108863&n;n>>>=26,t.words[r]=e,i=n}return 0!==i&&(t.words[t.length++]=i),t},o._prime=function(t){if(v[t])return v[t];var i;if("k256"===t)i=new w;else if("p224"===t)i=new y;else if("p192"===t)i=new b;else{if("p25519"!==t)throw new Error("Unknown prime "+t);i=new _}return v[t]=i,i},x.prototype._verify1=function(t){n(0===t.negative,"red works only with positives"),n(t.red,"red works only with red numbers")},x.prototype._verify2=function(t,i){n(0==(t.negative|i.negative),"red works only with positives"),n(t.red&&t.red===i.red,"red works only with red numbers")},x.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},x.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},x.prototype.add=function(t,i){this._verify2(t,i);var r=t.add(i);return r.cmp(this.m)>=0&&r.isub(this.m),r._forceRed(this)},x.prototype.iadd=function(t,i){this._verify2(t,i);var r=t.iadd(i);return r.cmp(this.m)>=0&&r.isub(this.m),r},x.prototype.sub=function(t,i){this._verify2(t,i);var r=t.sub(i);return r.cmpn(0)<0&&r.iadd(this.m),r._forceRed(this)},x.prototype.isub=function(t,i){this._verify2(t,i);var r=t.isub(i);return r.cmpn(0)<0&&r.iadd(this.m),r},x.prototype.shl=function(t,i){return this._verify1(t),this.imod(t.ushln(i))},x.prototype.imul=function(t,i){return this._verify2(t,i),this.imod(t.imul(i))},x.prototype.mul=function(t,i){return this._verify2(t,i),this.imod(t.mul(i))},x.prototype.isqr=function(t){return this.imul(t,t.clone())},x.prototype.sqr=function(t){return this.mul(t,t)},x.prototype.sqrt=function(t){if(t.isZero())return t.clone();var i=this.m.andln(3);if(n(i%2==1),3===i){var r=this.m.add(new o(1)).iushrn(2);return this.pow(t,r)}for(var e=this.m.subn(1),h=0;!e.isZero()&&0===e.andln(1);)h++,e.iushrn(1);n(!e.isZero());var s=new o(1).toRed(this),u=s.redNeg(),a=this.m.subn(1).iushrn(1),l=this.m.bitLength();for(l=new o(2*l*l).toRed(this);0!==this.pow(l,a).cmp(u);)l.redIAdd(u);for(var f=this.pow(l,e),m=this.pow(t,e.addn(1).iushrn(1)),p=this.pow(t,e),c=h;0!==p.cmp(s);){for(var d=p,g=0;0!==d.cmp(s);g++)d=d.redSqr();n(g<c);var v=this.pow(f,new o(1).iushln(c-g-1));m=m.redMul(v),f=v.redSqr(),p=p.redMul(f),c=g}return m},x.prototype.invm=function(t){var i=t._invmp(this.m);return 0!==i.negative?(i.negative=0,this.imod(i).redNeg()):this.imod(i)},x.prototype.pow=function(t,i){if(i.isZero())return new o(1).toRed(this);if(0===i.cmpn(1))return t.clone();var r=new Array(16);r[0]=new o(1).toRed(this),r[1]=t;for(var n=2;n<r.length;n++)r[n]=this.mul(r[n-1],t);var e=r[0],h=0,s=0,u=i.bitLength()%26;for(0===u&&(u=26),n=i.length-1;n>=0;n--){for(var a=i.words[n],l=u-1;l>=0;l--){var f=a>>l&1;e!==r[0]&&(e=this.sqr(e)),0!==f||0!==h?(h<<=1,h|=f,(4===++s||0===n&&0===l)&&(e=this.mul(e,r[h]),s=0,h=0)):s=0}u=26}return e},x.prototype.convertTo=function(t){var i=t.umod(this.m);return i===t?i.clone():i},x.prototype.convertFrom=function(t){var i=t.clone();return i.red=null,i},o.mont=function(t){return new k(t)},e(k,x),k.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},k.prototype.convertFrom=function(t){var i=this.imod(t.mul(this.rinv));return i.red=null,i},k.prototype.imul=function(t,i){if(t.isZero()||i.isZero())return t.words[0]=0,t.length=1,t;var r=t.imul(i),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),e=r.isub(n).iushrn(this.shift),o=e;return e.cmp(this.m)>=0?o=e.isub(this.m):e.cmpn(0)<0&&(o=e.iadd(this.m)),o._forceRed(this)},k.prototype.mul=function(t,i){if(t.isZero()||i.isZero())return new o(0)._forceRed(this);var r=t.mul(i),n=r.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),e=r.isub(n).iushrn(this.shift),h=e;return e.cmp(this.m)>=0?h=e.isub(this.m):e.cmpn(0)<0&&(h=e.iadd(this.m)),h._forceRed(this)},k.prototype.invm=function(t){return this.imod(t._invmp(this.m).mul(this.r2))._forceRed(this)}}(t,this)}).call(this,r(3)(t))},function(t,i){t.exports=require("web3")},function(t,i){t.exports=require("@maticnetwork/maticjs")},function(t,i){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,i){t.exports=require("buffer")},function(t,i,r){"use strict";r.r(i),r.d(i,"Web3ClientPlugin",(function(){return x})),r.d(i,"MaticBigNumber",(function(){return l})),r.d(i,"maticTxRequestConfigToWeb3",(function(){return f})),r.d(i,"web3ReceiptToMaticReceipt",(function(){return m})),r.d(i,"web3TxToMaticTx",(function(){return p}));var n,e=r(1),o=r.n(e),h=r(2),s=r(0),u=r.n(s),a=(n=function(t,i){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function r(){this.constructor=t}n(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}),l=function(t){function i(i){var r=t.call(this)||this;return r.bn_=new u.a(i),r}return a(i,t),i.isBN=function(t){return t instanceof i||u.a.isBN(t)},i.prototype.toString=function(t){return this.bn_.toString(t)},i.prototype.toNumber=function(){return this.bn_.toNumber()},i.prototype.toBuffer=function(t){return this.bn_.toBuffer()},i.prototype.add=function(t){return new i(this.bn_.add(new u.a(t.toString())))},i.prototype.sub=function(t){return new i(this.bn_.sub(new u.a(t.toString())))},i.prototype.mul=function(t){return new i(this.bn_.mul(new u.a(t.toString())))},i.prototype.div=function(t){return new i(this.bn_.div(new u.a(t.toString())))},i.prototype.lte=function(t){return this.bn_.lte(new u.a(t.toString()))},i.prototype.lt=function(t){return this.bn_.lt(new u.a(t.toString()))},i.prototype.gte=function(t){return this.bn_.gte(new u.a(t.toString()))},i.prototype.gt=function(t){return this.bn_.gt(new u.a(t.toString()))},i.prototype.eq=function(t){return this.bn_.eq(new u.a(t.toString()))},i}(h.BaseBigNumber),f=function(t){void 0===t&&(t={});var i=o.a.utils.toHex;return{chainId:i(t.chainId),data:t.data,from:t.from,gas:t.gasLimit,gasPrice:t.gasPrice,nonce:t.nonce,to:t.to,value:t.value,maxFeePerGas:t.maxFeePerGas,maxPriorityFeePerGas:t.maxPriorityFeePerGas,type:i(t.type),hardfork:t.hardfork}},m=function(t){return{blockHash:t.blockHash,blockNumber:t.blockNumber,contractAddress:t.contractAddress,cumulativeGasUsed:t.cumulativeGasUsed,from:t.from,gasUsed:t.gasUsed,status:t.status,to:t.to,transactionHash:t.transactionHash,transactionIndex:t.transactionIndex,events:t.events,logs:t.logs,logsBloom:t.logsBloom,root:t.root,type:t.type}},p=function(t){var i=t;return i.transactionHash=t.hash,i},c=function(t){var i=this;this.promise=t;var r=new Promise((function(t,r){i.onTransactionReceipt=t,i.onTransactionReceiptError=r}));this.getReceipt=function(){return r.then((function(t){return m(t)}))};var n=new Promise((function(t,r){i.onTransactionHash=t,i.onTransactionError=r}));this.getTransactionHash=function(){return n},t.once("transactionHash",this.onTransactionHash).once("receipt",this.onTransactionReceipt).on("error",this.onTransactionError).on("error",this.onTransactionReceiptError)},d=function(){var t=function(i,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])})(i,r)};return function(i,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=i}t(i,r),i.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),g=function(t){function i(i,r,n){var e=t.call(this,r)||this;return e.address=i,e.method=n,e}return d(i,t),i.prototype.toHex=function(t){return null!=t?o.a.utils.toHex(t):t},i.prototype.read=function(t,i){return this.logger.log("sending tx with config",t,i),this.method.call(f(t),i)},i.prototype.write=function(t){return new c(this.method.send(f(t)))},i.prototype.estimateGas=function(t){return this.method.estimateGas(f(t))},i.prototype.encodeABI=function(){return this.method.encodeABI()},i}(h.BaseContractMethod),v=function(){var t=function(i,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])})(i,r)};return function(i,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=i}t(i,r),i.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),M=function(t){function i(i,r,n){var e=t.call(this,i,n)||this;return e.contract=r,e}return v(i,t),i.prototype.method=function(t){for(var i,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return this.logger.log("methodName",t,"args method",arguments),new g(this.address,this.logger,(i=this.contract.methods)[t].apply(i,r))},i}(h.BaseContract),w=function(){var t=function(i,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])})(i,r)};return function(i,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=i}t(i,r),i.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),y=function(t,i,r,n){return new(r||(r=Promise))((function(e,o){function h(t){try{u(n.next(t))}catch(t){o(t)}}function s(t){try{u(n.throw(t))}catch(t){o(t)}}function u(t){var i;t.done?e(t.value):(i=t.value,i instanceof r?i:new r((function(t){t(i)}))).then(h,s)}u((n=n.apply(t,i||[])).next())}))},b=function(t,i){var r,n,e,o,h={label:0,sent:function(){if(1&e[0])throw e[1];return e[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(r)throw new TypeError("Generator is already executing.");for(;h;)try{if(r=1,n&&(e=2&o[0]?n.return:o[0]?n.throw||((e=n.return)&&e.call(n),0):n.next)&&!(e=e.call(n,o[1])).done)return e;switch(n=0,e&&(o=[2&o[0],e.value]),o[0]){case 0:case 1:e=o;break;case 4:return h.label++,{value:o[1],done:!1};case 5:h.label++,n=o[1],o=[0];continue;case 7:o=h.ops.pop(),h.trys.pop();continue;default:if(!(e=h.trys,(e=e.length>0&&e[e.length-1])||6!==o[0]&&2!==o[0])){h=0;continue}if(3===o[0]&&(!e||o[1]>e[0]&&o[1]<e[3])){h.label=o[1];break}if(6===o[0]&&h.label<e[1]){h.label=e[1],e=o;break}if(e&&h.label<e[2]){h.label=e[2],h.ops.push(o);break}e[2]&&h.ops.pop(),h.trys.pop();continue}o=i.call(t,h)}catch(t){o=[6,t],n=0}finally{r=e=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},_=function(t){function i(i,r){var n=t.call(this,r)||this;return n.name="WEB3",n.web3_=new o.a(i),n}return w(i,t),i.prototype.read=function(t){return this.web3_.eth.call(f(t))},i.prototype.write=function(t){return new c(this.web3_.eth.sendTransaction(f(t)))},i.prototype.getContract=function(t,i){var r=new this.web3_.eth.Contract(i,t);return new M(t,r,this.logger)},i.prototype.getGasPrice=function(){return this.web3_.eth.getGasPrice()},i.prototype.estimateGas=function(t){return this.web3_.eth.estimateGas(f(t))},i.prototype.getTransactionCount=function(t,i){return this.web3_.eth.getTransactionCount(t,i)},i.prototype.getAccounts=function(){return this.web3_.eth.getAccounts()},i.prototype.getChainId=function(){return y(this,void 0,void 0,(function(){var t;return b(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,this.web3_.eth.net.getId()];case 1:if(t=i.sent())return[2,t];throw new Error("net_version is not enabled");case 2:return i.sent(),[2,this.web3_.eth.getChainId()];case 3:return[2]}}))}))},i.prototype.ensureTransactionNotNull_=function(t){if(!t)throw{type:"invalid_transaction",message:"Could not retrieve transaction. Either it is invalid or might be in archive node."}},i.prototype.getTransaction=function(t){var i=this;return this.web3_.eth.getTransaction(t).then((function(t){return i.ensureTransactionNotNull_(t),p(t)}))},i.prototype.getTransactionReceipt=function(t){var i=this;return this.web3_.eth.getTransactionReceipt(t).then((function(t){return i.ensureTransactionNotNull_(t),m(t)}))},i.prototype.getBlock=function(t){return this.web3_.eth.getBlock(t)},i.prototype.getBalance=function(t){return this.web3_.eth.getBalance(t)},i.prototype.getBlockWithTransaction=function(t){return this.web3_.eth.getBlock(t,!0).then((function(t){var i=t;return i.transactions=t.transactions.map((function(t){return p(t)})),i}))},i.prototype.sendRPCRequest=function(t){var i=this;return new Promise((function(r,n){i.web3_.currentProvider.send(t,(function(t,i){if(t)return n(t);r(i)}))}))},i.prototype.signTypedData=function(t,i){return this.sendRPCRequest({jsonrpc:"2.0",method:"eth_signTypedData_v4",params:[t,i],id:(new Date).getTime()}).then((function(t){return String(t.result)}))},i.prototype.encodeParameters=function(t,i){return this.web3_.eth.abi.encodeParameters(i,t)},i.prototype.decodeParameters=function(t,i){return this.web3_.eth.abi.decodeParameters(i,t)},i.prototype.etheriumSha3=function(){for(var t,i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];return(t=o.a.utils).soliditySha3.apply(t,i)},i.prototype.hexToNumber=function(t){return o.a.utils.hexToNumber(t)},i.prototype.hexToNumberString=function(t){return o.a.utils.hexToNumberString(t)},i}(h.BaseWeb3Client),x=function(){function t(){}return t.prototype.setup=function(t){t.utils.Web3Client=_,t.utils.BN=l,t.utils.isBN=function(t){return o.a.utils.isBN(t)}},t}();i.default=x}]);
//# sourceMappingURL=matic-web3.node.min.js.map