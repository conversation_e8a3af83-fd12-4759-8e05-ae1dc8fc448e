import { MsgBase } from '../../MsgBase.js';
import { CosmosBaseV1Beta1Coin, CosmosStakingV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgDelegate {
    interface Params {
        amount: {
            denom: string;
            amount: string;
        };
        validatorAddress: string;
        injectiveAddress: string;
    }
    type Proto = CosmosStakingV1Beta1Tx.MsgDelegate;
}
/**
 * @category Messages
 */
export default class MsgDelegate extends MsgBase<MsgDelegate.Params, MsgDelegate.Proto> {
    static fromJSON(params: MsgDelegate.Params): MsgDelegate;
    toProto(): CosmosStakingV1Beta1Tx.MsgDelegate;
    toData(): {
        delegatorAddress: string;
        validatorAddress: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            delegator_address: string;
            validator_address: string;
            amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        };
    };
    toWeb3Gw(): {
        delegator_address: string;
        validator_address: string;
        amount: CosmosBaseV1Beta1Coin.Coin | undefined;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: CosmosStakingV1Beta1Tx.MsgDelegate;
    };
    toBinary(): Uint8Array;
}
