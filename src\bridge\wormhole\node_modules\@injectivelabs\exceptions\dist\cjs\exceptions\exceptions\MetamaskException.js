"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetamaskException = void 0;
const base_js_1 = require("../base.js");
const index_js_1 = require("../types/index.js");
const maps_js_1 = require("../utils/maps.js");
const removeMetamaskFromErrorString = (message) => message
    .replaceAll('Metamask', '')
    .replaceAll('MetaMask', '')
    .replaceAll('Metamask:', '');
class MetamaskException extends base_js_1.ConcreteException {
    static errorClass = 'MetamaskException';
    constructor(error, context) {
        super(error, context);
        this.type = index_js_1.ErrorType.WalletError;
    }
    parse() {
        const { message } = this;
        this.setMessage((0, maps_js_1.mapMetamaskMessage)(removeMetamaskFromErrorString(message)));
        this.setName(MetamaskException.errorClass);
    }
}
exports.MetamaskException = MetamaskException;
