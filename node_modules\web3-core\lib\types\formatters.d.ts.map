{"version": 3, "file": "formatters.d.ts", "sourceRoot": "", "sources": ["../../src/formatters.ts"], "names": [], "mappings": "AAmBA,OAAO,EAEN,MAAM,EACN,OAAO,EACP,KAAK,EACL,UAAU,EACV,WAAW,EACX,SAAS,EACT,UAAU,EACV,OAAO,EACP,SAAS,EACT,UAAU,EACV,KAAK,EACL,YAAY,EACZ,aAAa,EACb,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,YAAY,CAAC;AAkBpB;;;GAGG;AACH,eAAO,MAAM,yBAAyB,SAAU,KAAK,CAAC,MAAM,CAAC,aAAsC,CAAC;AAEpG;;;GAGG;AACH,eAAO,MAAM,oBAAoB,UAAW,KAAK,KAAG,KAIlD,CAAC;AAEH;;;GAGG;AACH,eAAO,MAAM,yBAAyB,WAAY,OAAO,oBAAqB,CAAC;AAE/E;;;GAGG;AACH,eAAO,MAAM,yBAAyB,gBAAiB,OAAO,GAAG,SAAS,uBAkBzE,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,gCAAgC,gBAC/B,OAAO,GAAG,SAAS,gBAClB,OAAO,uBAOrB,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,qBAAqB,YAAa,MAAM,KAAG,MAAM,GAAG,KAchE,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,uBAAuB,YAAa,gBAAgB,KAAG,OAAO,CAAC,iBAAiB,CA2C5F,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,kBAAkB,YAAa,gBAAgB,mBAAmB,MAAM,+BAUpF,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,yBAAyB,YAAa,gBAAgB,mBAAmB,MAAM,+BAe3F,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,kBAAkB,SAAU,MAAM,WAAiD,CAAC;AAEjG;;;;GAIG;AACH,eAAO,MAAM,0BAA0B,OAAQ,gBAAgB,KAAG,iBA4CjE,CAAC;AAEF;;;GAGG;AAGH,eAAO,MAAM,mBAAmB,UAAW,KAAK,KAAG,KAAK,GAAG,IAQ1D,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,iBAAiB,WAAY,MAAM,KA8BjC,MACd,CAAC;AAEF;;;;GAIG;AACH,eAAO,MAAM,kBAAkB,QAAS,OAAO,CAAC,SAAS,CAAC,KAAG,UAqC5D,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,iCAAiC,YAAa,YAAY,KAAG,aAkCzE,CAAC;AAEF;;;;GAIG;AACH,eAAO,MAAM,oBAAoB,UAAW,UAAU,KAAG,WAkCxD,CAAC;AAEF;;;GAGG;AACH,eAAO,MAAM,kBAAkB,SAAU,UAAU,KAAG,SA0BrD,CAAC;AAEF;;;;GAIG;AACH,eAAO,MAAM,mBAAmB,SAAU,SAAS,KAAG,UAkCrD,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,sBAAsB,WAAY,SAAS,KAAG,UAgB1D,CAAC"}