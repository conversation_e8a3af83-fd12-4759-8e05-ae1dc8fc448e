"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
/**
 * @category Messages
 */
class MsgAuthorizeStakeGrants extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgAuthorizeStakeGrants(params);
    }
    toProto() {
        const { params } = this;
        const message = core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants.create();
        message.sender = params.injectiveAddress;
        message.grants = [
            {
                grantee: params.grantee,
                amount: params.amount,
            },
        ];
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.exchange.v1beta1.MsgAuthorizeStakeGrants',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'exchange/MsgAuthorizeStakeGrants',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.exchange.v1beta1.MsgAuthorizeStakeGrants',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.exchange.v1beta1.MsgAuthorizeStakeGrants',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveExchangeV1Beta1Tx.MsgAuthorizeStakeGrants.encode(this.toProto()).finish();
    }
}
exports.default = MsgAuthorizeStakeGrants;
