import { BaseToken, Web3SideChainClient } from "../utils";
import { IPOSClientConfig, ITransactionOption } from "../interfaces";
export declare class RootChainManager extends BaseToken<IPOSClientConfig> {
    constructor(client_: Web3SideChainClient<IPOSClientConfig>, address: string);
    method(methodName: string, ...args: any[]): Promise<import("..").BaseContractMethod>;
    deposit(userAddress: string, tokenAddress: string, depositData: string, option?: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    exit(exitPayload: string, option: ITransactionOption): Promise<import("../interfaces").ITransactionWriteResult>;
    isExitProcessed(exitHash: string): Promise<boolean>;
}
