{"name": "servify", "version": "0.1.12", "description": "", "engines": {"node": ">=6"}, "main": "servify-node.js", "browser": "servify-browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "browserify servify.js > dist/servify.bundle.js; ccjs dist/servify.bundle.js > dist/servify.min.js"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"body-parser": "^1.16.0", "express": "^4.14.0", "request": "^2.79.0", "xhr": "^2.3.3", "cors": "^2.8.1"}, "repository": {"type": "git", "url": "https://github.com/maiavictor/servify"}}