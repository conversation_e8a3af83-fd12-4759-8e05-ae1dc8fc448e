{"version": 3, "file": "account.d.ts", "sourceRoot": "", "sources": ["../../../../src/program/namespace/account.ts"], "names": [], "mappings": ";AACA,OAAO,YAAY,MAAM,eAAe,CAAC;AACzC,OAAO,EACL,MAAM,EACN,SAAS,EAET,sBAAsB,EACtB,UAAU,EACV,wBAAwB,EACxB,WAAW,EACX,qBAAqB,EACrB,OAAO,EACR,MAAM,iBAAiB,CAAC;AACzB,OAAO,QAAyB,MAAM,mBAAmB,CAAC;AAC1D,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAClD,OAAO,EAAE,KAAK,EAAc,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAgB,OAAO,EAAoB,MAAM,cAAc,CAAC;AACvE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAIzD,MAAM,CAAC,OAAO,OAAO,cAAc;WACnB,KAAK,CAAC,GAAG,SAAS,GAAG,EACjC,GAAG,EAAE,GAAG,EACR,KAAK,EAAE,KAAK,EACZ,SAAS,EAAE,SAAS,EACpB,QAAQ,CAAC,EAAE,QAAQ,GAClB,gBAAgB,CAAC,GAAG,CAAC;CAgBzB;AAED,KAAK,kBAAkB,CAAC,GAAG,SAAS,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,SAAS,GACxE,aAAa,GACb,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAEzC;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,MAAM,gBAAgB,CAAC,GAAG,SAAS,GAAG,GAAG,GAAG,IAAI;KACnD,CAAC,IAAI,MAAM,cAAc,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;CACxD,CAAC;AAEF,qBAAa,aAAa,CACxB,GAAG,SAAS,GAAG,GAAG,GAAG,EACrB,CAAC,SAAS,MAAM,WAAW,CAAC,GAAG,CAAC,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,EACzD,CAAC,SAAS,kBAAkB,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,EAC3D,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB;;OAEG;IACH,IAAI,IAAI,IAAI,MAAM,CAEjB;IACD,OAAO,CAAC,KAAK,CAAS;IAEtB;;OAEG;IACH,IAAI,SAAS,IAAI,SAAS,CAEzB;IACD,OAAO,CAAC,UAAU,CAAY;IAE9B;;OAEG;IACH,IAAI,QAAQ,IAAI,QAAQ,CAEvB;IACD,OAAO,CAAC,SAAS,CAAW;IAE5B;;OAEG;IACH,IAAI,KAAK,IAAI,KAAK,CAEjB;IACD,OAAO,CAAC,MAAM,CAAQ;IAEtB;;OAEG;IACH,IAAI,UAAU,IAAI,CAAC,CAElB;IACD,OAAO,CAAC,WAAW,CAAI;gBAGrB,GAAG,EAAE,GAAG,EACR,UAAU,EAAE,CAAC,EACb,SAAS,EAAE,SAAS,EACpB,QAAQ,CAAC,EAAE,QAAQ,EACnB,KAAK,CAAC,EAAE,KAAK;IASf;;;;OAIG;IACG,aAAa,CACjB,OAAO,EAAE,OAAO,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAKpB;;;;OAIG;IACG,uBAAuB,CAC3B,OAAO,EAAE,OAAO,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC;QAAE,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;QAAC,OAAO,EAAE,OAAO,CAAA;KAAE,CAAC;IAehD;;;;OAIG;IACG,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC;IAUlE;;;;OAIG;IACG,eAAe,CACnB,OAAO,EAAE,OAAO,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC;QAAE,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;QAAC,OAAO,EAAE,OAAO,CAAA;KAAE,CAAC;IAWhD;;;;;OAKG;IACG,aAAa,CACjB,SAAS,EAAE,OAAO,EAAE,EACpB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAKxB;;;;;OAKG;IACG,uBAAuB,CAC3B,SAAS,EAAE,OAAO,EAAE,EACpB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,CAAC;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,OAAO,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IAoBpD;;;;;;;;;;;;;OAaG;IACG,GAAG,CACP,OAAO,CAAC,EAAE,MAAM,GAAG,wBAAwB,EAAE,GAC5C,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IAkC/B;;;OAGG;IACH,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE,UAAU,GAAG,YAAY;IA4BlE;;OAEG;IACG,WAAW,CAAC,OAAO,EAAE,OAAO;IAgBlC;;OAEG;IACG,iBAAiB,CACrB,MAAM,EAAE,MAAM,EACd,YAAY,CAAC,EAAE,MAAM,GACpB,OAAO,CAAC,sBAAsB,CAAC;IAqBlC;;;;;OAKG;IACG,UAAU,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAKhE;;;;;OAKG;IACG,iBAAiB,CACrB,GAAG,IAAI,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,GACjC,OAAO,CAAC,SAAS,CAAC;IAIf,cAAc,CAClB,OAAO,EAAE,OAAO,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IAOhC,wBAAwB,CAC5B,OAAO,EAAE,OAAO,EAChB,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;CAM9D;AAED;;;;GAIG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI;IACpC,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,CAAC,CAAC;CACZ,CAAC"}