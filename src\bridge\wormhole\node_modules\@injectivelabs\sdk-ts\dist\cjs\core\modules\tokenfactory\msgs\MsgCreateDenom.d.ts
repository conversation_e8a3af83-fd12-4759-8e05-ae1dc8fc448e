import { MsgBase } from '../../MsgBase.js';
import { InjectiveTokenFactoryV1Beta1Tx } from '@injectivelabs/core-proto-ts';
export declare namespace MsgCreateDenom {
    interface Params {
        sender: string;
        subdenom: string;
        decimals?: number;
        name?: string;
        symbol?: string;
        allowAdminBurn?: boolean;
    }
    type Proto = InjectiveTokenFactoryV1Beta1Tx.MsgCreateDenom;
}
/**
 * @category Messages
 */
export default class MsgCreateDenom extends MsgBase<MsgCreateDenom.Params, MsgCreateDenom.Proto> {
    static fromJSON(params: MsgCreateDenom.Params): MsgCreateDenom;
    toProto(): InjectiveTokenFactoryV1Beta1Tx.MsgCreateDenom;
    toData(): {
        sender: string;
        subdenom: string;
        name: string;
        symbol: string;
        decimals: number;
        allowAdminBurn: boolean;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            subdenom: string;
            name: string;
            symbol: string;
            decimals: number;
            allow_admin_burn: boolean;
        };
    };
    toWeb3Gw(): {
        sender: string;
        subdenom: string;
        name: string;
        symbol: string;
        decimals: number;
        allow_admin_burn: boolean;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveTokenFactoryV1Beta1Tx.MsgCreateDenom;
    };
    toBinary(): Uint8Array;
}
