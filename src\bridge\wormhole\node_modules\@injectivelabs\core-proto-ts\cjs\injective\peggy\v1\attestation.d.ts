import _m0 from "protobufjs/minimal.js";
import { Any } from "../../../google/protobuf/any";
export declare const protobufPackage = "injective.peggy.v1";
/**
 * ClaimType is the cosmos type of an event from the counterpart chain that can
 * be handled
 */
export declare enum ClaimType {
    CLAIM_TYPE_UNKNOWN = 0,
    CLAIM_TYPE_DEPOSIT = 1,
    CLAIM_TYPE_WITHDRAW = 2,
    CLAIM_TYPE_ERC20_DEPLOYED = 3,
    CLAIM_TYPE_VALSET_UPDATED = 4,
    UNRECOGNIZED = -1
}
export declare function claimTypeFromJSON(object: any): ClaimType;
export declare function claimTypeToJSON(object: ClaimType): string;
/**
 * Attestation is an aggregate of `claims` that eventually becomes `observed` by
 * all orchestrators
 * EVENT_NONCE:
 * EventNonce a nonce provided by the peggy contract that is unique per event
 * fired These event nonces must be relayed in order. This is a correctness
 * issue, if relaying out of order transaction replay attacks become possible
 * OBSERVED:
 * Observed indicates that >67% of validators have attested to the event,
 * and that the event should be executed by the peggy state machine
 *
 * The actual content of the claims is passed in with the transaction making the
 * claim and then passed through the call stack alongside the attestation while
 * it is processed the key in which the attestation is stored is keyed on the
 * exact details of the claim but there is no reason to store those exact
 * details becuause the next message sender will kindly provide you with them.
 */
export interface Attestation {
    observed: boolean;
    votes: string[];
    height: string;
    claim: Any | undefined;
}
/**
 * ERC20Token unique identifier for an Ethereum ERC20 token.
 * CONTRACT:
 * The contract address on ETH of the token, this could be a Cosmos
 * originated token, if so it will be the ERC20 address of the representation
 * (note: developers should look up the token symbol using the address on ETH to
 * display for UI)
 */
export interface ERC20Token {
    contract: string;
    amount: string;
}
export declare const Attestation: {
    encode(message: Attestation, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): Attestation;
    fromJSON(object: any): Attestation;
    toJSON(message: Attestation): unknown;
    create(base?: DeepPartial<Attestation>): Attestation;
    fromPartial(object: DeepPartial<Attestation>): Attestation;
};
export declare const ERC20Token: {
    encode(message: ERC20Token, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ERC20Token;
    fromJSON(object: any): ERC20Token;
    toJSON(message: ERC20Token): unknown;
    create(base?: DeepPartial<ERC20Token>): ERC20Token;
    fromPartial(object: DeepPartial<ERC20Token>): ERC20Token;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
