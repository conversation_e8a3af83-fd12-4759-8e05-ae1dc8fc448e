{"globals": {"define": false}, "esversion": 6, "bitwise": false, "curly": false, "eqeqeq": true, "forin": true, "freeze": true, "funcscope": false, "iterator": false, "latedef": false, "maxcomplexity": false, "maxdepth": false, "maxerr": 50, "maxparams": false, "maxstatements": false, "noarg": true, "nocomma": false, "nonbsp": true, "nonew": false, "notypeof": false, "shadow": false, "singleGroups": false, "strict": false, "undef": true, "unused": true, "varstmt": false, "asi": false, "boss": true, "debug": false, "elision": false, "eqnull": false, "evil": false, "expr": false, "lastsemic": false, "loopfunc": true, "moz": false, "noyield": false, "plusplus": false, "proto": false, "scripturl": false, "supernew": false, "validthis": false, "withstmt": false, "browser": true, "browserify": true, "couch": false, "devel": true, "dojo": false, "jasmine": false, "jquery": false, "mocha": true, "module": false, "mootools": false, "node": true, "nonstandard": false, "phantom": false, "prototypejs": false, "qunit": false, "rhino": false, "shelljs": false, "typed": false, "worker": false, "wsh": false, "yui": false}