{"name": "got", "version": "12.1.0", "description": "Human-friendly and powerful HTTP request library for Node.js", "license": "MIT", "repository": "sindresorhus/got", "funding": "https://github.com/sindresorhus/got?sponsor=1", "type": "module", "exports": "./dist/source/index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava", "release": "np", "build": "del-cli dist && tsc", "prepare": "npm run build"}, "files": ["dist/source"], "keywords": ["http", "https", "http2", "get", "got", "url", "uri", "request", "simple", "curl", "wget", "fetch", "net", "network", "gzip", "brotli", "requests", "human-friendly", "axios", "superagent", "node-fetch", "ky"], "dependencies": {"@sindresorhus/is": "^4.6.0", "@szmarczak/http-timer": "^5.0.1", "@types/cacheable-request": "^6.0.2", "@types/responselike": "^1.0.0", "cacheable-lookup": "^6.0.4", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "form-data-encoder": "1.7.1", "get-stream": "^6.0.1", "http2-wrapper": "^2.1.10", "lowercase-keys": "^3.0.0", "p-cancelable": "^3.0.0", "responselike": "^2.0.0"}, "devDependencies": {"@hapi/bourne": "^2.0.0", "@sindresorhus/tsconfig": "^2.0.0", "@sinonjs/fake-timers": "^9.1.1", "@types/benchmark": "^2.1.1", "@types/express": "^4.17.13", "@types/node": "^17.0.21", "@types/pem": "^1.9.6", "@types/pify": "^5.0.1", "@types/readable-stream": "^2.3.13", "@types/request": "^2.48.8", "@types/sinon": "^10.0.11", "@types/sinonjs__fake-timers": "^8.1.1", "@types/tough-cookie": "^4.0.1", "ava": "^3.15.0", "axios": "^0.26.1", "benchmark": "^2.1.4", "bluebird": "^3.7.2", "body-parser": "^1.19.2", "create-cert": "^1.0.6", "create-test-server": "^3.0.1", "del-cli": "^4.0.1", "delay": "^5.0.0", "express": "^4.17.3", "form-data": "^4.0.0", "formdata-node": "^4.3.2", "nock": "^13.2.4", "node-fetch": "^3.2.3", "np": "^7.6.0", "nyc": "^15.1.0", "p-event": "^5.0.1", "pem": "^1.14.6", "pify": "^5.0.0", "readable-stream": "^3.6.0", "request": "^2.88.2", "sinon": "^13.0.1", "slow-stream": "0.0.4", "tempy": "^2.0.0", "then-busboy": "^5.1.1", "to-readable-stream": "^3.0.0", "tough-cookie": "^4.0.0", "ts-node": "^10.7.0", "typescript": "4.6.2", "xo": "^0.48.0"}, "types": "dist/source", "sideEffects": false, "ava": {"files": ["test/*"], "timeout": "1m", "nonSemVerExperiments": {"nextGenConfig": true, "configurableModuleFormat": true}, "extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "nyc": {"reporter": ["text", "html", "lcov"], "extension": [".ts"], "exclude": ["**/test/**"]}, "xo": {"ignores": ["documentation/examples/*"], "rules": {"@typescript-eslint/no-empty-function": "off", "node/no-deprecated-api": "off", "node/prefer-global/url": "off", "node/prefer-global/url-search-params": "off", "@typescript-eslint/no-implicit-any-catch": "off", "unicorn/prefer-node-protocol": "off", "ava/assertion-arguments": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/await-thenable": "off", "no-lone-blocks": "off", "unicorn/no-await-expression-member": "off"}}, "runkitExampleFilename": "./documentation/examples/runkit-example.js"}