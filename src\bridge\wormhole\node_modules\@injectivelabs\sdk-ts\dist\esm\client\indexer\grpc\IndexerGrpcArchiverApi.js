import { UnspecifiedErrorCode, grpcErrorCodeToErrorCode, GrpcUnaryRequestException, } from '@injectivelabs/exceptions';
import { InjectiveArchiverRpc } from '@injectivelabs/indexer-proto-ts';
import BaseGrpcConsumer from '../../base/BaseIndexerGrpcConsumer.js';
import { IndexerModule } from '../types/index.js';
import { IndexerGrpcArchiverTransformer } from '../transformers/index.js';
/**
 * @category Indexer Grpc API
 */
export class IndexerGrpcArchiverApi extends BaseGrpcConsumer {
    module = IndexerModule.Archiver;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new InjectiveArchiverRpc.InjectiveArchiverRPCClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchHistoricalBalance({ account, resolution, }) {
        const request = InjectiveArchiverRpc.BalanceRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Balance(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcHistoricalBalanceResponseToHistoricalBalances(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Historical Balance',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Historical Balance',
                contextModule: this.module,
            });
        }
    }
    async fetchHistoricalRpnl({ account, resolution, }) {
        const request = InjectiveArchiverRpc.RpnlRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Rpnl(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcHistoricalRPNLResponseToHistoricalRPNL(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Historical Rpnl',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Historical Rpnl',
                contextModule: this.module,
            });
        }
    }
    async fetchHistoricalVolumes({ account, resolution, }) {
        const request = InjectiveArchiverRpc.VolumesRequest.create();
        request.account = account;
        request.resolution = resolution;
        try {
            const response = await this.retry(() => this.client.Volumes(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcHistoricalVolumesResponseToHistoricalVolumes(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Historical Volumes',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Historical Volumes',
                contextModule: this.module,
            });
        }
    }
    async fetchPnlLeaderboard({ startDate, endDate, limit, account, }) {
        const request = InjectiveArchiverRpc.PnlLeaderboardRequest.create();
        request.startDate = startDate;
        request.endDate = endDate;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.PnlLeaderboard(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcPnlLeaderboardResponseToPnlLeaderboard(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Pnl Leaderboard',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Pnl Leaderboard',
                contextModule: this.module,
            });
        }
    }
    async fetchVolLeaderboard({ startDate, endDate, limit, account, }) {
        const request = InjectiveArchiverRpc.VolLeaderboardRequest.create();
        request.startDate = startDate;
        request.endDate = endDate;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.VolLeaderboard(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcVolLeaderboardResponseToVolLeaderboard(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Vol Leaderboard',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Vol Leaderboard',
                contextModule: this.module,
            });
        }
    }
    async fetchPnlLeaderboardFixedResolution({ resolution, limit, account, }) {
        const request = InjectiveArchiverRpc.PnlLeaderboardFixedResolutionRequest.create();
        request.resolution = resolution;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.PnlLeaderboardFixedResolution(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcPnlLeaderboardFixedResolutionResponseToPnlLeaderboard(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Pnl Leaderboard Fixed Resolution',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Pnl Leaderboard Fixed Resolution',
                contextModule: this.module,
            });
        }
    }
    async fetchVolLeaderboardFixedResolution({ resolution, limit, account, }) {
        const request = InjectiveArchiverRpc.VolLeaderboardFixedResolutionRequest.create();
        request.resolution = resolution;
        if (limit) {
            request.limit = limit;
        }
        if (account) {
            request.account = account;
        }
        try {
            const response = await this.retry(() => this.client.VolLeaderboardFixedResolution(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcVolLeaderboardFixedResolutionResponseToVolLeaderboard(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'Vol Leaderboard Fixed Resolution',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'Vol Leaderboard Fixed Resolution',
                contextModule: this.module,
            });
        }
    }
    async fetchDenomHolders({ denom, token, limit, }) {
        const request = InjectiveArchiverRpc.DenomHoldersRequest.create();
        request.denom = denom;
        if (token) {
            request.token = token;
        }
        if (limit) {
            request.limit = limit;
        }
        try {
            const response = await this.retry(() => this.client.DenomHolders(request, this.metadata));
            return IndexerGrpcArchiverTransformer.grpcDenomHoldersResponseToDenomHolders(response);
        }
        catch (e) {
            if (e instanceof InjectiveArchiverRpc.GrpcWebError) {
                throw new GrpcUnaryRequestException(new Error(e.toString()), {
                    code: grpcErrorCodeToErrorCode(e.code),
                    context: 'DenomHolders',
                    contextModule: this.module,
                });
            }
            throw new GrpcUnaryRequestException(e, {
                code: UnspecifiedErrorCode,
                context: 'DenomHolders',
                contextModule: this.module,
            });
        }
    }
}
