"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcDistributionTransformer = void 0;
const index_js_1 = require("../../../utils/index.js");
/**
 * @category Chain Grpc Transformer
 */
class ChainGrpcDistributionTransformer {
    static moduleParamsResponseToModuleParams(response) {
        const params = response.params;
        return {
            communityTax: (0, index_js_1.cosmosSdkDecToBigNumber)(params.communityTax).toFixed(),
            baseProposerReward: params.baseProposerReward,
            bonusProposerReward: params.bonusProposerReward,
            withdrawAddrEnabled: params.withdrawAddrEnabled,
        };
    }
    static delegationRewardResponseToReward(response) {
        const grpcRewards = response.rewards;
        return grpcRewards.map((grpcReward) => {
            return {
                amount: (0, index_js_1.cosmosSdkDecToBigNumber)(grpcReward.amount).toFixed(),
                denom: grpcReward.denom,
            };
        });
    }
    static totalDelegationRewardResponseToTotalReward(response) {
        const grpcRewards = response.rewards;
        return grpcRewards.map((grpcReward) => {
            const rewards = grpcReward.reward.map((reward) => ({
                amount: (0, index_js_1.cosmosSdkDecToBigNumber)(reward.amount).toFixed(),
                denom: reward.denom,
            }));
            return {
                rewards,
                validatorAddress: grpcReward.validatorAddress,
            };
        });
    }
}
exports.ChainGrpcDistributionTransformer = ChainGrpcDistributionTransformer;
