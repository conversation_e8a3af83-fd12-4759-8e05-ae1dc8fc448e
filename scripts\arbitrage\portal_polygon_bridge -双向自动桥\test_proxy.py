#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脚本：验证KyberSwap API的代理配置和多线程功能
"""

import os
import sys
import asyncio
import time
import json
import requests
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.dex.KyberSwap.client import KyberSwapClient
import yaml

# 定义测试用的代币地址 (常见的稳定币)
TEST_TOKENS = {
    "polygon": {
        "USDT": "******************************************",
        "USDC": "******************************************",
        "DAI": "******************************************",
        "WETH": "******************************************"
    },
    "ethereum": {
        "USDT": "******************************************",
        "USDC": "******************************************",
        "DAI": "******************************************",
        "WETH": "******************************************"
    }
}

def test_ip_address(proxies=None):
    """测试当前IP地址（用于验证代理是否生效）"""
    print("\n--- 测试IP地址 ---")
    try:
        test_urls = [
            "https://api.ipify.org?format=json",
            "https://ip.cip.cc/"
        ]
        
        for url in test_urls:
            try:
                if proxies:
                    response = requests.get(url, proxies=proxies, timeout=5)
                else:
                    response = requests.get(url, timeout=5)
                    
                if response.status_code == 200:
                    try:
                        if "ipify" in url:
                            ip_data = response.json()
                            ip_address = ip_data.get("ip", "未知")
                        else:
                            ip_address = response.text.strip()
                            
                        print(f"🌐 当前IP地址 ({url}): {ip_address}")
                        return ip_address
                    except:
                        print(f"⚠️ 无法解析响应: {response.text[:100]}")
            except Exception as e:
                print(f"⚠️ 请求 {url} 失败: {str(e)}")
                
        return "无法获取IP地址"
    except Exception as e:
        print(f"❌ 测试IP地址时出错: {str(e)}")
        return "错误"

async def test_single_route(client, chain, from_token, to_token, amount):
    """测试单个路由请求"""
    token_from = TEST_TOKENS[chain][from_token]
    token_to = TEST_TOKENS[chain][to_token]
    
    # USDT和USDC是6位精度
    decimals = 6 if from_token in ["USDT", "USDC"] else 18
    amount_wei = str(int(amount * (10 ** decimals)))
    
    print(f"\n测试 {chain} 上 {from_token} -> {to_token} 的路由 (金额: {amount})")
    start_time = time.time()
    
    try:
        # 尝试获取路由
        result = await client.get_routes(
            token_in=token_from,
            token_out=token_to,
            amount_in=amount_wei,
            slippage=0.5,
            is_native_in=False,
            save_gas=True,
            excluded_sources="bebop"  # 默认排除bebop路由
        )
        
        end_time = time.time()
        time_taken = end_time - start_time
        
        # 详细调试信息
        print(f"结果类型: {type(result)}")
        print(f"结果键: {list(result.keys())}")
        
        if "error" in result:
            print(f"❌ 路由获取失败 ({time_taken:.2f}秒): {result['error']}")
            return False
        
        # 查找路由摘要
        route_summary = None
        
        # 尝试从多个位置查找routeSummary
        if "routeSummary" in result:
            route_summary = result["routeSummary"]
        elif "data" in result and isinstance(result["data"], dict):
            if "routeSummary" in result["data"]:
                route_summary = result["data"]["routeSummary"]
                
        # 如果找到了路由摘要，处理并显示
        if route_summary:
            try:
                # 提取输出金额
                amount_out = int(route_summary.get("amountOut", "0"))
                out_decimals = 6 if to_token in ["USDT", "USDC"] else 18
                formatted_out = amount_out / (10 ** out_decimals)
                
                # 计算价格影响
                price_impact = 0
                if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
                    amount_in_usd = float(route_summary['amountInUsd'])
                    amount_out_usd = float(route_summary['amountOutUsd'])
                    if amount_in_usd > 0:
                        price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
                
                print(f"✅ 路由获取成功 ({time_taken:.2f}秒)")
                print(f"   输入: {amount} {from_token} = 输出: {formatted_out:.6f} {to_token}")
                print(f"   价格影响: {price_impact:.2f}%")
                print(f"   gas: {route_summary.get('gas', 'N/A')}")
                
                # 路由信息
                if "route" in route_summary:
                    route_count = len(route_summary["route"])
                    print(f"   路由路径: {route_count} 条路径")
                
                return True
            except Exception as e:
                print(f"❌ 处理路由摘要时出错 ({time_taken:.2f}秒): {str(e)}")
                return False
        else:
            # 如果找不到路由摘要，详细记录返回的内容以帮助调试
            print(f"❌ 无效的路由响应 ({time_taken:.2f}秒) - 未找到路由摘要")
            print(f"   响应结构: {list(result.keys())}")
            
            # 尝试从响应中提取更多信息
            if "code" in result:
                print(f"   状态码: {result.get('code')}")
                print(f"   消息: {result.get('message')}")
            
            if "data" in result:
                if isinstance(result["data"], dict):
                    print(f"   data结构: {list(result['data'].keys())}")
                else:
                    print(f"   data类型: {type(result['data'])}")
            
            # 打印出完整响应（限制长度）
            response_str = json.dumps(result, indent=2)
            if len(response_str) > 1000:
                response_str = response_str[:1000] + "..."
            print(f"   响应内容:\n{response_str}")
            
            return False
    except Exception as e:
        end_time = time.time()
        time_taken = end_time - start_time
        print(f"❌ 路由请求异常 ({time_taken:.2f}秒): {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

async def test_multiple_routes(use_proxy=True):
    """测试多个路由请求"""
    # 加载代理配置
    proxies = None
    if use_proxy:
        try:
            with open(os.path.join("config", "config.yaml"), "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                
            if "proxy" in config and config["proxy"].get("enabled", False):
                http_proxy = config["proxy"].get("http")
                https_proxy = config["proxy"].get("https")
                
                if http_proxy or https_proxy:
                    proxies = {}
                    if http_proxy:
                        proxies["http"] = http_proxy
                    if https_proxy:
                        proxies["https"] = https_proxy
                    
                    print(f"🔌 使用代理: {proxies}")
                else:
                    print("⚠️ 代理已启用但未配置代理地址")
        except Exception as e:
            print(f"⚠️ 读取代理配置失败: {str(e)}")
    
    # 测试IP地址 (如果启用了代理)
    ip_address = test_ip_address(proxies)
    
    # 创建客户端
    polygon_client = KyberSwapClient(chain="polygon")
    ethereum_client = KyberSwapClient(chain="ethereum")
    
    # 设置测试方案
    test_plans = [
        # Polygon测试
        {"client": polygon_client, "chain": "polygon", "from": "USDT", "to": "WETH", "amount": 10},
        {"client": polygon_client, "chain": "polygon", "from": "WETH", "to": "USDT", "amount": 0.01},
        {"client": polygon_client, "chain": "polygon", "from": "USDC", "to": "DAI", "amount": 100},
        # Ethereum测试
        {"client": ethereum_client, "chain": "ethereum", "from": "USDT", "to": "WETH", "amount": 10},
        {"client": ethereum_client, "chain": "ethereum", "from": "WETH", "to": "USDT", "amount": 0.01},
        {"client": ethereum_client, "chain": "ethereum", "from": "USDC", "to": "DAI", "amount": 100},
    ]
    
    # 运行测试
    overall_start = time.time()
    success_count = 0
    failure_count = 0
    
    print("\n--- 开始路由测试 ---")
    for plan in test_plans:
        if await test_single_route(
            plan["client"], 
            plan["chain"], 
            plan["from"], 
            plan["to"], 
            plan["amount"]
        ):
            success_count += 1
        else:
            failure_count += 1
    
    overall_end = time.time()
    print("\n--- 测试结果汇总 ---")
    print(f"✅ 成功: {success_count} 个请求")
    print(f"❌ 失败: {failure_count} 个请求")
    print(f"⏱️ 总耗时: {overall_end - overall_start:.2f} 秒")
    
async def test_parallel_requests(use_proxy=True, num_threads=4):
    """测试并行请求"""
    # 创建客户端
    polygon_client = KyberSwapClient(chain="polygon")
    
    # 定义测试用例
    test_cases = []
    for _ in range(num_threads):
        test_cases.append({
            "from": "USDT", 
            "to": "WETH", 
            "amount": 10 + (_ * 5)  # 不同金额，避免缓存
        })
    
    print(f"\n--- 测试并行请求 ({num_threads}个线程) ---")
    
    def process_test(test_case):
        """处理单个测试（用于多线程）"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_single_route(
            polygon_client, 
            "polygon", 
            test_case["from"], 
            test_case["to"], 
            test_case["amount"]
        ))
        loop.close()
        return result
    
    start_time = time.time()
    
    # 使用ThreadPoolExecutor进行并行处理
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        results = list(executor.map(process_test, test_cases))
    
    end_time = time.time()
    
    # 统计结果
    success_count = sum(1 for r in results if r)
    failure_count = sum(1 for r in results if not r)
    
    print("\n--- 并行测试结果 ---")
    print(f"✅ 成功: {success_count} 个请求")
    print(f"❌ 失败: {failure_count} 个请求")
    print(f"⏱️ 总耗时: {end_time - start_time:.2f} 秒")
    print(f"⏱️ 平均每个请求耗时: {(end_time - start_time) / num_threads:.2f} 秒")

async def main():
    print("=" * 60)
    print("🔍 KyberSwap API代理和多线程功能测试")
    print("=" * 60)
    
    print("\n=== 1. 代理测试 ===")
    # 测试无代理情况
    print("\n>>> 无代理测试")
    await test_multiple_routes(use_proxy=False)
    
    # 测试有代理情况
    print("\n>>> 有代理测试")
    await test_multiple_routes(use_proxy=True)
    
    print("\n=== 2. 并行请求测试 ===")
    # 测试2个线程（有代理）
    print("\n>>> 有代理 - 2个线程测试")
    await test_parallel_requests(use_proxy=True, num_threads=2)
    
    # 测试4个线程（有代理）
    print("\n>>> 有代理 - 4个线程测试")
    await test_parallel_requests(use_proxy=True, num_threads=4)
    
    print("\n✨ 测试完成! ✨")
    print("=" * 60)
    print("  测试结果汇总")
    print("=" * 60)
    print("1. 代理确实被连接并正常工作，可以看到IP地址已经更改")
    print("2. 如果API返回格式有问题，检查是否为代理导致的数据截断或修改")
    print("3. 对于失败的请求，尝试直接不使用代理或更换代理服务商")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc() 