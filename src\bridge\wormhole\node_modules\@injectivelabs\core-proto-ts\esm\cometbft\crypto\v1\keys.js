/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cometbft.crypto.v1";
function createBasePublicKey() {
    return { ed25519: undefined, secp256k1: undefined, bls12381: undefined, secp256k1eth: undefined };
}
export const PublicKey = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.ed25519 !== undefined) {
            writer.uint32(10).bytes(message.ed25519);
        }
        if (message.secp256k1 !== undefined) {
            writer.uint32(18).bytes(message.secp256k1);
        }
        if (message.bls12381 !== undefined) {
            writer.uint32(26).bytes(message.bls12381);
        }
        if (message.secp256k1eth !== undefined) {
            writer.uint32(34).bytes(message.secp256k1eth);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePublicKey();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.ed25519 = reader.bytes();
                    break;
                case 2:
                    message.secp256k1 = reader.bytes();
                    break;
                case 3:
                    message.bls12381 = reader.bytes();
                    break;
                case 4:
                    message.secp256k1eth = reader.bytes();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            ed25519: isSet(object.ed25519) ? bytesFromBase64(object.ed25519) : undefined,
            secp256k1: isSet(object.secp256k1) ? bytesFromBase64(object.secp256k1) : undefined,
            bls12381: isSet(object.bls12381) ? bytesFromBase64(object.bls12381) : undefined,
            secp256k1eth: isSet(object.secp256k1eth) ? bytesFromBase64(object.secp256k1eth) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.ed25519 !== undefined &&
            (obj.ed25519 = message.ed25519 !== undefined ? base64FromBytes(message.ed25519) : undefined);
        message.secp256k1 !== undefined &&
            (obj.secp256k1 = message.secp256k1 !== undefined ? base64FromBytes(message.secp256k1) : undefined);
        message.bls12381 !== undefined &&
            (obj.bls12381 = message.bls12381 !== undefined ? base64FromBytes(message.bls12381) : undefined);
        message.secp256k1eth !== undefined &&
            (obj.secp256k1eth = message.secp256k1eth !== undefined ? base64FromBytes(message.secp256k1eth) : undefined);
        return obj;
    },
    create(base) {
        return PublicKey.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePublicKey();
        message.ed25519 = object.ed25519 ?? undefined;
        message.secp256k1 = object.secp256k1 ?? undefined;
        message.bls12381 = object.bls12381 ?? undefined;
        message.secp256k1eth = object.secp256k1eth ?? undefined;
        return message;
    },
};
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function bytesFromBase64(b64) {
    if (tsProtoGlobalThis.Buffer) {
        return Uint8Array.from(tsProtoGlobalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = tsProtoGlobalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (tsProtoGlobalThis.Buffer) {
        return tsProtoGlobalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(String.fromCharCode(byte));
        });
        return tsProtoGlobalThis.btoa(bin.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
