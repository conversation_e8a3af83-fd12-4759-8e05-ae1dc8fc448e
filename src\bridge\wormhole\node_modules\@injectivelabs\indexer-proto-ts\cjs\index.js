"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.InjectiveReferralRpc = exports.InjectiveCampaignRpc = exports.InjectiveTradingRpc = exports.InjectiveSpotExchangeRpc = exports.InjectivePortfolioRpc = exports.InjectiveOracleRpc = exports.InjectiveMetaRpc = exports.InjectiveInsuranceRpc = exports.InjectiveExplorerRpc = exports.InjectiveExchangeRpc = exports.InjectiveDerivativeExchangeRpc = exports.InjectiveAuctionRpc = exports.InjectiveArchiverRpc = exports.InjectiveAccountRpc = exports.EventProviderApi = void 0;
exports.EventProviderApi = __importStar(require("./event_provider_api.js"));
exports.InjectiveAccountRpc = __importStar(require("./injective_accounts_rpc.js"));
exports.InjectiveArchiverRpc = __importStar(require("./injective_archiver_rpc.js"));
exports.InjectiveAuctionRpc = __importStar(require("./injective_auction_rpc.js"));
exports.InjectiveDerivativeExchangeRpc = __importStar(require("./injective_derivative_exchange_rpc.js"));
exports.InjectiveExchangeRpc = __importStar(require("./injective_exchange_rpc.js"));
exports.InjectiveExplorerRpc = __importStar(require("./injective_explorer_rpc.js"));
exports.InjectiveInsuranceRpc = __importStar(require("./injective_insurance_rpc.js"));
exports.InjectiveMetaRpc = __importStar(require("./injective_meta_rpc.js"));
exports.InjectiveOracleRpc = __importStar(require("./injective_oracle_rpc.js"));
exports.InjectivePortfolioRpc = __importStar(require("./injective_portfolio_rpc.js"));
exports.InjectiveSpotExchangeRpc = __importStar(require("./injective_spot_exchange_rpc.js"));
exports.InjectiveTradingRpc = __importStar(require("./injective_trading_rpc.js"));
exports.InjectiveCampaignRpc = __importStar(require("./injective_campaign_rpc.js"));
exports.InjectiveReferralRpc = __importStar(require("./injective_referral_rpc.js"));
