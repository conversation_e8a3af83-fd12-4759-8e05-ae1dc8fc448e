import { getGrpcTransport } from '../../utils/grpc.js';
import { GrpcWebImpl } from './GrpcWebImpl.js';
/**
 * @hidden
 */
export default class BaseGrpcWebConsumer extends GrpcWebImpl {
    module = '';
    constructor(endpoint) {
        super(endpoint, {
            transport: getGrpcTransport(),
            setCookieMetadata: true
        });
    }
    static getGrpcWebImpl = (endpoint) => new BaseGrpcWebConsumer(endpoint);
}
