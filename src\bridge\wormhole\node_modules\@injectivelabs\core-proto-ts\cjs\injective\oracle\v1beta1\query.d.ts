import { grpc } from "@injectivelabs/grpc-web";
import _m0 from "protobufjs/minimal.js";
import { GenesisState } from "./genesis";
import { BandPriceState, CoinbasePriceState, MetadataStatistics, OracleInfo, OracleType, Params, PriceFeedState, PriceRecord, PriceRecords, PriceState, ProviderInfo, ProviderState, PythPriceState, StorkPriceState } from "./oracle";
export declare const protobufPackage = "injective.oracle.v1beta1";
export interface QueryPythPriceRequest {
    priceId: string;
}
export interface QueryPythPriceResponse {
    priceState: PythPriceState | undefined;
}
/**
 * QueryOracleParamsRequest is the request type for the Query/OracleParams RPC
 * method.
 */
export interface QueryParamsRequest {
}
/**
 * QueryOracleParamsResponse is the response type for the Query/OracleParams RPC
 * method.
 */
export interface QueryParamsResponse {
    params: Params | undefined;
}
/**
 * QueryBandRelayersRequest is the request type for the Query/BandRelayers RPC
 * method.
 */
export interface QueryBandRelayersRequest {
}
/**
 * QueryBandRelayersResponse is the response type for the Query/BandRelayers RPC
 * method.
 */
export interface QueryBandRelayersResponse {
    relayers: string[];
}
/**
 * QueryBandPriceStatesRequest is the request type for the Query/BandPriceStates
 * RPC method.
 */
export interface QueryBandPriceStatesRequest {
}
/**
 * QueryBandPriceStatesResponse is the response type for the
 * Query/BandPriceStates RPC method.
 */
export interface QueryBandPriceStatesResponse {
    priceStates: BandPriceState[];
}
/**
 * QueryBandIBCPriceStatesRequest is the request type for the
 * Query/BandIBCPriceStates RPC method.
 */
export interface QueryBandIBCPriceStatesRequest {
}
/**
 * QueryBandIBCPriceStatesResponse is the response type for the
 * Query/BandIBCPriceStates RPC method.
 */
export interface QueryBandIBCPriceStatesResponse {
    priceStates: BandPriceState[];
}
/**
 * QueryPriceFeedPriceStatesRequest is the request type for the
 * Query/PriceFeedPriceStates RPC method.
 */
export interface QueryPriceFeedPriceStatesRequest {
}
/**
 * QueryPriceFeedPriceStatesResponse is the response type for the
 * Query/PriceFeedPriceStates RPC method.
 */
export interface QueryPriceFeedPriceStatesResponse {
    priceStates: PriceFeedState[];
}
/**
 * QueryCoinbasePriceStatesRequest is the request type for the
 * Query/CoinbasePriceStates RPC method.
 */
export interface QueryCoinbasePriceStatesRequest {
}
/**
 * QueryCoinbasePriceStatesResponse is the response type for the
 * Query/CoinbasePriceStates RPC method.
 */
export interface QueryCoinbasePriceStatesResponse {
    priceStates: CoinbasePriceState[];
}
/**
 * QueryPythPriceStatesRequest is the request type for the
 * Query/CoinbasePriceStates RPC method.
 */
export interface QueryPythPriceStatesRequest {
}
/**
 * QueryPythPriceStatesResponse is the response type for the
 * Query/CoinbasePriceStates RPC method.
 */
export interface QueryPythPriceStatesResponse {
    priceStates: PythPriceState[];
}
/**
 * QueryStorkPriceStatesRequest is the request type for the
 * Query/StorkPriceStates RPC method.
 */
export interface QueryStorkPriceStatesRequest {
}
/**
 * QueryStorkPriceStatesResponse is the response type for the
 * Query/StorkPriceStates RPC method.
 */
export interface QueryStorkPriceStatesResponse {
    priceStates: StorkPriceState[];
}
/**
 * QueryStorkPublishersRequest is the request type for the
 * Query/StorkPublishers RPC method.
 */
export interface QueryStorkPublishersRequest {
}
/**
 * QueryStorkPublishersResponse is the response type for the
 * Query/StorkPublishers RPC method.
 */
export interface QueryStorkPublishersResponse {
    publishers: string[];
}
/**
 * QueryProviderPriceStateRequest is the request type for the
 * Query/ProviderPriceState RPC method.
 */
export interface QueryProviderPriceStateRequest {
    provider: string;
    symbol: string;
}
/**
 * QueryProviderPriceStatesResponse is the response type for the
 * Query/ProviderPriceStates RPC method.
 */
export interface QueryProviderPriceStateResponse {
    priceState: PriceState | undefined;
}
/**
 * QueryModuleStateRequest is the request type for the Query/OracleModuleState
 * RPC method.
 */
export interface QueryModuleStateRequest {
}
/**
 * QueryModuleStateResponse is the response type for the Query/OracleModuleState
 * RPC method.
 */
export interface QueryModuleStateResponse {
    state: GenesisState | undefined;
}
export interface QueryHistoricalPriceRecordsRequest {
    oracle: OracleType;
    symbolId: string;
}
export interface QueryHistoricalPriceRecordsResponse {
    priceRecords: PriceRecords[];
}
export interface OracleHistoryOptions {
    /**
     * MaxAge restricts the oracle price records oldest age in seconds from the
     * current block time to consider. A value of 0 means use all the records
     * present on the chain.
     */
    maxAge: string;
    /**
     * If IncludeRawHistory is true, the raw underlying data used for the
     * computation is included in the response
     */
    includeRawHistory: boolean;
    /**
     * If IncludeMetadata is true, metadata on the computation is included in the
     * response
     */
    includeMetadata: boolean;
}
/**
 * QueryOracleVolatilityRequest is the request type for Query/OracleVolatility
 * RPC method.
 */
export interface QueryOracleVolatilityRequest {
    baseInfo: OracleInfo | undefined;
    quoteInfo: OracleInfo | undefined;
    oracleHistoryOptions: OracleHistoryOptions | undefined;
}
/**
 * QueryOracleVolatilityResponse is the response type for Query/OracleVolatility
 * RPC method.
 */
export interface QueryOracleVolatilityResponse {
    volatility: string;
    historyMetadata: MetadataStatistics | undefined;
    rawHistory: PriceRecord[];
}
export interface QueryOracleProvidersInfoRequest {
}
export interface QueryOracleProvidersInfoResponse {
    providers: ProviderInfo[];
}
export interface QueryOracleProviderPricesRequest {
    provider: string;
}
export interface QueryOracleProviderPricesResponse {
    providerState: ProviderState[];
}
/**
 * ScalingOptions defines optional configuration to avoid precision loss. The
 * oracle result will be returned as base_price * 10^base_decimals / quote_price
 * * 10^quote_decimals
 */
export interface ScalingOptions {
    baseDecimals: number;
    quoteDecimals: number;
}
/**
 * QueryOraclePriceRequest is the request type for the Query/OraclePrice RPC
 * method.
 */
export interface QueryOraclePriceRequest {
    oracleType: OracleType;
    base: string;
    quote: string;
    scalingOptions: ScalingOptions | undefined;
}
export interface PricePairState {
    pairPrice: string;
    basePrice: string;
    quotePrice: string;
    baseCumulativePrice: string;
    quoteCumulativePrice: string;
    baseTimestamp: string;
    quoteTimestamp: string;
}
/**
 * QueryOraclePriceResponse is the response type for the Query/OraclePrice RPC
 * method.
 */
export interface QueryOraclePriceResponse {
    pricePairState: PricePairState | undefined;
}
export declare const QueryPythPriceRequest: {
    encode(message: QueryPythPriceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPythPriceRequest;
    fromJSON(object: any): QueryPythPriceRequest;
    toJSON(message: QueryPythPriceRequest): unknown;
    create(base?: DeepPartial<QueryPythPriceRequest>): QueryPythPriceRequest;
    fromPartial(object: DeepPartial<QueryPythPriceRequest>): QueryPythPriceRequest;
};
export declare const QueryPythPriceResponse: {
    encode(message: QueryPythPriceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPythPriceResponse;
    fromJSON(object: any): QueryPythPriceResponse;
    toJSON(message: QueryPythPriceResponse): unknown;
    create(base?: DeepPartial<QueryPythPriceResponse>): QueryPythPriceResponse;
    fromPartial(object: DeepPartial<QueryPythPriceResponse>): QueryPythPriceResponse;
};
export declare const QueryParamsRequest: {
    encode(_: QueryParamsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsRequest;
    fromJSON(_: any): QueryParamsRequest;
    toJSON(_: QueryParamsRequest): unknown;
    create(base?: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
    fromPartial(_: DeepPartial<QueryParamsRequest>): QueryParamsRequest;
};
export declare const QueryParamsResponse: {
    encode(message: QueryParamsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryParamsResponse;
    fromJSON(object: any): QueryParamsResponse;
    toJSON(message: QueryParamsResponse): unknown;
    create(base?: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
    fromPartial(object: DeepPartial<QueryParamsResponse>): QueryParamsResponse;
};
export declare const QueryBandRelayersRequest: {
    encode(_: QueryBandRelayersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandRelayersRequest;
    fromJSON(_: any): QueryBandRelayersRequest;
    toJSON(_: QueryBandRelayersRequest): unknown;
    create(base?: DeepPartial<QueryBandRelayersRequest>): QueryBandRelayersRequest;
    fromPartial(_: DeepPartial<QueryBandRelayersRequest>): QueryBandRelayersRequest;
};
export declare const QueryBandRelayersResponse: {
    encode(message: QueryBandRelayersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandRelayersResponse;
    fromJSON(object: any): QueryBandRelayersResponse;
    toJSON(message: QueryBandRelayersResponse): unknown;
    create(base?: DeepPartial<QueryBandRelayersResponse>): QueryBandRelayersResponse;
    fromPartial(object: DeepPartial<QueryBandRelayersResponse>): QueryBandRelayersResponse;
};
export declare const QueryBandPriceStatesRequest: {
    encode(_: QueryBandPriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandPriceStatesRequest;
    fromJSON(_: any): QueryBandPriceStatesRequest;
    toJSON(_: QueryBandPriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryBandPriceStatesRequest>): QueryBandPriceStatesRequest;
    fromPartial(_: DeepPartial<QueryBandPriceStatesRequest>): QueryBandPriceStatesRequest;
};
export declare const QueryBandPriceStatesResponse: {
    encode(message: QueryBandPriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandPriceStatesResponse;
    fromJSON(object: any): QueryBandPriceStatesResponse;
    toJSON(message: QueryBandPriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryBandPriceStatesResponse>): QueryBandPriceStatesResponse;
    fromPartial(object: DeepPartial<QueryBandPriceStatesResponse>): QueryBandPriceStatesResponse;
};
export declare const QueryBandIBCPriceStatesRequest: {
    encode(_: QueryBandIBCPriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandIBCPriceStatesRequest;
    fromJSON(_: any): QueryBandIBCPriceStatesRequest;
    toJSON(_: QueryBandIBCPriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryBandIBCPriceStatesRequest>): QueryBandIBCPriceStatesRequest;
    fromPartial(_: DeepPartial<QueryBandIBCPriceStatesRequest>): QueryBandIBCPriceStatesRequest;
};
export declare const QueryBandIBCPriceStatesResponse: {
    encode(message: QueryBandIBCPriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryBandIBCPriceStatesResponse;
    fromJSON(object: any): QueryBandIBCPriceStatesResponse;
    toJSON(message: QueryBandIBCPriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryBandIBCPriceStatesResponse>): QueryBandIBCPriceStatesResponse;
    fromPartial(object: DeepPartial<QueryBandIBCPriceStatesResponse>): QueryBandIBCPriceStatesResponse;
};
export declare const QueryPriceFeedPriceStatesRequest: {
    encode(_: QueryPriceFeedPriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPriceFeedPriceStatesRequest;
    fromJSON(_: any): QueryPriceFeedPriceStatesRequest;
    toJSON(_: QueryPriceFeedPriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryPriceFeedPriceStatesRequest>): QueryPriceFeedPriceStatesRequest;
    fromPartial(_: DeepPartial<QueryPriceFeedPriceStatesRequest>): QueryPriceFeedPriceStatesRequest;
};
export declare const QueryPriceFeedPriceStatesResponse: {
    encode(message: QueryPriceFeedPriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPriceFeedPriceStatesResponse;
    fromJSON(object: any): QueryPriceFeedPriceStatesResponse;
    toJSON(message: QueryPriceFeedPriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryPriceFeedPriceStatesResponse>): QueryPriceFeedPriceStatesResponse;
    fromPartial(object: DeepPartial<QueryPriceFeedPriceStatesResponse>): QueryPriceFeedPriceStatesResponse;
};
export declare const QueryCoinbasePriceStatesRequest: {
    encode(_: QueryCoinbasePriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCoinbasePriceStatesRequest;
    fromJSON(_: any): QueryCoinbasePriceStatesRequest;
    toJSON(_: QueryCoinbasePriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryCoinbasePriceStatesRequest>): QueryCoinbasePriceStatesRequest;
    fromPartial(_: DeepPartial<QueryCoinbasePriceStatesRequest>): QueryCoinbasePriceStatesRequest;
};
export declare const QueryCoinbasePriceStatesResponse: {
    encode(message: QueryCoinbasePriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryCoinbasePriceStatesResponse;
    fromJSON(object: any): QueryCoinbasePriceStatesResponse;
    toJSON(message: QueryCoinbasePriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryCoinbasePriceStatesResponse>): QueryCoinbasePriceStatesResponse;
    fromPartial(object: DeepPartial<QueryCoinbasePriceStatesResponse>): QueryCoinbasePriceStatesResponse;
};
export declare const QueryPythPriceStatesRequest: {
    encode(_: QueryPythPriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPythPriceStatesRequest;
    fromJSON(_: any): QueryPythPriceStatesRequest;
    toJSON(_: QueryPythPriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryPythPriceStatesRequest>): QueryPythPriceStatesRequest;
    fromPartial(_: DeepPartial<QueryPythPriceStatesRequest>): QueryPythPriceStatesRequest;
};
export declare const QueryPythPriceStatesResponse: {
    encode(message: QueryPythPriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryPythPriceStatesResponse;
    fromJSON(object: any): QueryPythPriceStatesResponse;
    toJSON(message: QueryPythPriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryPythPriceStatesResponse>): QueryPythPriceStatesResponse;
    fromPartial(object: DeepPartial<QueryPythPriceStatesResponse>): QueryPythPriceStatesResponse;
};
export declare const QueryStorkPriceStatesRequest: {
    encode(_: QueryStorkPriceStatesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryStorkPriceStatesRequest;
    fromJSON(_: any): QueryStorkPriceStatesRequest;
    toJSON(_: QueryStorkPriceStatesRequest): unknown;
    create(base?: DeepPartial<QueryStorkPriceStatesRequest>): QueryStorkPriceStatesRequest;
    fromPartial(_: DeepPartial<QueryStorkPriceStatesRequest>): QueryStorkPriceStatesRequest;
};
export declare const QueryStorkPriceStatesResponse: {
    encode(message: QueryStorkPriceStatesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryStorkPriceStatesResponse;
    fromJSON(object: any): QueryStorkPriceStatesResponse;
    toJSON(message: QueryStorkPriceStatesResponse): unknown;
    create(base?: DeepPartial<QueryStorkPriceStatesResponse>): QueryStorkPriceStatesResponse;
    fromPartial(object: DeepPartial<QueryStorkPriceStatesResponse>): QueryStorkPriceStatesResponse;
};
export declare const QueryStorkPublishersRequest: {
    encode(_: QueryStorkPublishersRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryStorkPublishersRequest;
    fromJSON(_: any): QueryStorkPublishersRequest;
    toJSON(_: QueryStorkPublishersRequest): unknown;
    create(base?: DeepPartial<QueryStorkPublishersRequest>): QueryStorkPublishersRequest;
    fromPartial(_: DeepPartial<QueryStorkPublishersRequest>): QueryStorkPublishersRequest;
};
export declare const QueryStorkPublishersResponse: {
    encode(message: QueryStorkPublishersResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryStorkPublishersResponse;
    fromJSON(object: any): QueryStorkPublishersResponse;
    toJSON(message: QueryStorkPublishersResponse): unknown;
    create(base?: DeepPartial<QueryStorkPublishersResponse>): QueryStorkPublishersResponse;
    fromPartial(object: DeepPartial<QueryStorkPublishersResponse>): QueryStorkPublishersResponse;
};
export declare const QueryProviderPriceStateRequest: {
    encode(message: QueryProviderPriceStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryProviderPriceStateRequest;
    fromJSON(object: any): QueryProviderPriceStateRequest;
    toJSON(message: QueryProviderPriceStateRequest): unknown;
    create(base?: DeepPartial<QueryProviderPriceStateRequest>): QueryProviderPriceStateRequest;
    fromPartial(object: DeepPartial<QueryProviderPriceStateRequest>): QueryProviderPriceStateRequest;
};
export declare const QueryProviderPriceStateResponse: {
    encode(message: QueryProviderPriceStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryProviderPriceStateResponse;
    fromJSON(object: any): QueryProviderPriceStateResponse;
    toJSON(message: QueryProviderPriceStateResponse): unknown;
    create(base?: DeepPartial<QueryProviderPriceStateResponse>): QueryProviderPriceStateResponse;
    fromPartial(object: DeepPartial<QueryProviderPriceStateResponse>): QueryProviderPriceStateResponse;
};
export declare const QueryModuleStateRequest: {
    encode(_: QueryModuleStateRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateRequest;
    fromJSON(_: any): QueryModuleStateRequest;
    toJSON(_: QueryModuleStateRequest): unknown;
    create(base?: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
    fromPartial(_: DeepPartial<QueryModuleStateRequest>): QueryModuleStateRequest;
};
export declare const QueryModuleStateResponse: {
    encode(message: QueryModuleStateResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryModuleStateResponse;
    fromJSON(object: any): QueryModuleStateResponse;
    toJSON(message: QueryModuleStateResponse): unknown;
    create(base?: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
    fromPartial(object: DeepPartial<QueryModuleStateResponse>): QueryModuleStateResponse;
};
export declare const QueryHistoricalPriceRecordsRequest: {
    encode(message: QueryHistoricalPriceRecordsRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryHistoricalPriceRecordsRequest;
    fromJSON(object: any): QueryHistoricalPriceRecordsRequest;
    toJSON(message: QueryHistoricalPriceRecordsRequest): unknown;
    create(base?: DeepPartial<QueryHistoricalPriceRecordsRequest>): QueryHistoricalPriceRecordsRequest;
    fromPartial(object: DeepPartial<QueryHistoricalPriceRecordsRequest>): QueryHistoricalPriceRecordsRequest;
};
export declare const QueryHistoricalPriceRecordsResponse: {
    encode(message: QueryHistoricalPriceRecordsResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryHistoricalPriceRecordsResponse;
    fromJSON(object: any): QueryHistoricalPriceRecordsResponse;
    toJSON(message: QueryHistoricalPriceRecordsResponse): unknown;
    create(base?: DeepPartial<QueryHistoricalPriceRecordsResponse>): QueryHistoricalPriceRecordsResponse;
    fromPartial(object: DeepPartial<QueryHistoricalPriceRecordsResponse>): QueryHistoricalPriceRecordsResponse;
};
export declare const OracleHistoryOptions: {
    encode(message: OracleHistoryOptions, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): OracleHistoryOptions;
    fromJSON(object: any): OracleHistoryOptions;
    toJSON(message: OracleHistoryOptions): unknown;
    create(base?: DeepPartial<OracleHistoryOptions>): OracleHistoryOptions;
    fromPartial(object: DeepPartial<OracleHistoryOptions>): OracleHistoryOptions;
};
export declare const QueryOracleVolatilityRequest: {
    encode(message: QueryOracleVolatilityRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleVolatilityRequest;
    fromJSON(object: any): QueryOracleVolatilityRequest;
    toJSON(message: QueryOracleVolatilityRequest): unknown;
    create(base?: DeepPartial<QueryOracleVolatilityRequest>): QueryOracleVolatilityRequest;
    fromPartial(object: DeepPartial<QueryOracleVolatilityRequest>): QueryOracleVolatilityRequest;
};
export declare const QueryOracleVolatilityResponse: {
    encode(message: QueryOracleVolatilityResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleVolatilityResponse;
    fromJSON(object: any): QueryOracleVolatilityResponse;
    toJSON(message: QueryOracleVolatilityResponse): unknown;
    create(base?: DeepPartial<QueryOracleVolatilityResponse>): QueryOracleVolatilityResponse;
    fromPartial(object: DeepPartial<QueryOracleVolatilityResponse>): QueryOracleVolatilityResponse;
};
export declare const QueryOracleProvidersInfoRequest: {
    encode(_: QueryOracleProvidersInfoRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleProvidersInfoRequest;
    fromJSON(_: any): QueryOracleProvidersInfoRequest;
    toJSON(_: QueryOracleProvidersInfoRequest): unknown;
    create(base?: DeepPartial<QueryOracleProvidersInfoRequest>): QueryOracleProvidersInfoRequest;
    fromPartial(_: DeepPartial<QueryOracleProvidersInfoRequest>): QueryOracleProvidersInfoRequest;
};
export declare const QueryOracleProvidersInfoResponse: {
    encode(message: QueryOracleProvidersInfoResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleProvidersInfoResponse;
    fromJSON(object: any): QueryOracleProvidersInfoResponse;
    toJSON(message: QueryOracleProvidersInfoResponse): unknown;
    create(base?: DeepPartial<QueryOracleProvidersInfoResponse>): QueryOracleProvidersInfoResponse;
    fromPartial(object: DeepPartial<QueryOracleProvidersInfoResponse>): QueryOracleProvidersInfoResponse;
};
export declare const QueryOracleProviderPricesRequest: {
    encode(message: QueryOracleProviderPricesRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleProviderPricesRequest;
    fromJSON(object: any): QueryOracleProviderPricesRequest;
    toJSON(message: QueryOracleProviderPricesRequest): unknown;
    create(base?: DeepPartial<QueryOracleProviderPricesRequest>): QueryOracleProviderPricesRequest;
    fromPartial(object: DeepPartial<QueryOracleProviderPricesRequest>): QueryOracleProviderPricesRequest;
};
export declare const QueryOracleProviderPricesResponse: {
    encode(message: QueryOracleProviderPricesResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOracleProviderPricesResponse;
    fromJSON(object: any): QueryOracleProviderPricesResponse;
    toJSON(message: QueryOracleProviderPricesResponse): unknown;
    create(base?: DeepPartial<QueryOracleProviderPricesResponse>): QueryOracleProviderPricesResponse;
    fromPartial(object: DeepPartial<QueryOracleProviderPricesResponse>): QueryOracleProviderPricesResponse;
};
export declare const ScalingOptions: {
    encode(message: ScalingOptions, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): ScalingOptions;
    fromJSON(object: any): ScalingOptions;
    toJSON(message: ScalingOptions): unknown;
    create(base?: DeepPartial<ScalingOptions>): ScalingOptions;
    fromPartial(object: DeepPartial<ScalingOptions>): ScalingOptions;
};
export declare const QueryOraclePriceRequest: {
    encode(message: QueryOraclePriceRequest, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOraclePriceRequest;
    fromJSON(object: any): QueryOraclePriceRequest;
    toJSON(message: QueryOraclePriceRequest): unknown;
    create(base?: DeepPartial<QueryOraclePriceRequest>): QueryOraclePriceRequest;
    fromPartial(object: DeepPartial<QueryOraclePriceRequest>): QueryOraclePriceRequest;
};
export declare const PricePairState: {
    encode(message: PricePairState, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): PricePairState;
    fromJSON(object: any): PricePairState;
    toJSON(message: PricePairState): unknown;
    create(base?: DeepPartial<PricePairState>): PricePairState;
    fromPartial(object: DeepPartial<PricePairState>): PricePairState;
};
export declare const QueryOraclePriceResponse: {
    encode(message: QueryOraclePriceResponse, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): QueryOraclePriceResponse;
    fromJSON(object: any): QueryOraclePriceResponse;
    toJSON(message: QueryOraclePriceResponse): unknown;
    create(base?: DeepPartial<QueryOraclePriceResponse>): QueryOraclePriceResponse;
    fromPartial(object: DeepPartial<QueryOraclePriceResponse>): QueryOraclePriceResponse;
};
/** Query defines the gRPC querier service. */
export interface Query {
    /** Retrieves oracle params */
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    /** Retrieves the band relayers */
    BandRelayers(request: DeepPartial<QueryBandRelayersRequest>, metadata?: grpc.Metadata): Promise<QueryBandRelayersResponse>;
    /** Retrieves the state for all band price feeds */
    BandPriceStates(request: DeepPartial<QueryBandPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryBandPriceStatesResponse>;
    /** Retrieves the state for all band ibc price feeds */
    BandIBCPriceStates(request: DeepPartial<QueryBandIBCPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryBandIBCPriceStatesResponse>;
    /** Retrieves the state for all price feeds */
    PriceFeedPriceStates(request: DeepPartial<QueryPriceFeedPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryPriceFeedPriceStatesResponse>;
    /** Retrieves the state for all coinbase price feeds */
    CoinbasePriceStates(request: DeepPartial<QueryCoinbasePriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryCoinbasePriceStatesResponse>;
    /** Retrieves the state for all pyth price feeds */
    PythPriceStates(request: DeepPartial<QueryPythPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryPythPriceStatesResponse>;
    /** Retrieves the state for all stork price feeds */
    StorkPriceStates(request: DeepPartial<QueryStorkPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryStorkPriceStatesResponse>;
    /** Retrieves all stork publishers */
    StorkPublishers(request: DeepPartial<QueryStorkPublishersRequest>, metadata?: grpc.Metadata): Promise<QueryStorkPublishersResponse>;
    /** Retrieves the state for all provider price feeds */
    ProviderPriceState(request: DeepPartial<QueryProviderPriceStateRequest>, metadata?: grpc.Metadata): Promise<QueryProviderPriceStateResponse>;
    /** Retrieves the entire oracle module's state */
    OracleModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
    /** Retrieves historical price records for a given OracleType and Symbol */
    HistoricalPriceRecords(request: DeepPartial<QueryHistoricalPriceRecordsRequest>, metadata?: grpc.Metadata): Promise<QueryHistoricalPriceRecordsResponse>;
    /** Retrieves mixed volatility value for the specified pair of base/quote */
    OracleVolatility(request: DeepPartial<QueryOracleVolatilityRequest>, metadata?: grpc.Metadata): Promise<QueryOracleVolatilityResponse>;
    OracleProvidersInfo(request: DeepPartial<QueryOracleProvidersInfoRequest>, metadata?: grpc.Metadata): Promise<QueryOracleProvidersInfoResponse>;
    OracleProviderPrices(request: DeepPartial<QueryOracleProviderPricesRequest>, metadata?: grpc.Metadata): Promise<QueryOracleProviderPricesResponse>;
    OraclePrice(request: DeepPartial<QueryOraclePriceRequest>, metadata?: grpc.Metadata): Promise<QueryOraclePriceResponse>;
    PythPrice(request: DeepPartial<QueryPythPriceRequest>, metadata?: grpc.Metadata): Promise<QueryPythPriceResponse>;
}
export declare class QueryClientImpl implements Query {
    private readonly rpc;
    constructor(rpc: Rpc);
    Params(request: DeepPartial<QueryParamsRequest>, metadata?: grpc.Metadata): Promise<QueryParamsResponse>;
    BandRelayers(request: DeepPartial<QueryBandRelayersRequest>, metadata?: grpc.Metadata): Promise<QueryBandRelayersResponse>;
    BandPriceStates(request: DeepPartial<QueryBandPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryBandPriceStatesResponse>;
    BandIBCPriceStates(request: DeepPartial<QueryBandIBCPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryBandIBCPriceStatesResponse>;
    PriceFeedPriceStates(request: DeepPartial<QueryPriceFeedPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryPriceFeedPriceStatesResponse>;
    CoinbasePriceStates(request: DeepPartial<QueryCoinbasePriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryCoinbasePriceStatesResponse>;
    PythPriceStates(request: DeepPartial<QueryPythPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryPythPriceStatesResponse>;
    StorkPriceStates(request: DeepPartial<QueryStorkPriceStatesRequest>, metadata?: grpc.Metadata): Promise<QueryStorkPriceStatesResponse>;
    StorkPublishers(request: DeepPartial<QueryStorkPublishersRequest>, metadata?: grpc.Metadata): Promise<QueryStorkPublishersResponse>;
    ProviderPriceState(request: DeepPartial<QueryProviderPriceStateRequest>, metadata?: grpc.Metadata): Promise<QueryProviderPriceStateResponse>;
    OracleModuleState(request: DeepPartial<QueryModuleStateRequest>, metadata?: grpc.Metadata): Promise<QueryModuleStateResponse>;
    HistoricalPriceRecords(request: DeepPartial<QueryHistoricalPriceRecordsRequest>, metadata?: grpc.Metadata): Promise<QueryHistoricalPriceRecordsResponse>;
    OracleVolatility(request: DeepPartial<QueryOracleVolatilityRequest>, metadata?: grpc.Metadata): Promise<QueryOracleVolatilityResponse>;
    OracleProvidersInfo(request: DeepPartial<QueryOracleProvidersInfoRequest>, metadata?: grpc.Metadata): Promise<QueryOracleProvidersInfoResponse>;
    OracleProviderPrices(request: DeepPartial<QueryOracleProviderPricesRequest>, metadata?: grpc.Metadata): Promise<QueryOracleProviderPricesResponse>;
    OraclePrice(request: DeepPartial<QueryOraclePriceRequest>, metadata?: grpc.Metadata): Promise<QueryOraclePriceResponse>;
    PythPrice(request: DeepPartial<QueryPythPriceRequest>, metadata?: grpc.Metadata): Promise<QueryPythPriceResponse>;
}
export declare const QueryDesc: {
    serviceName: string;
};
export declare const QueryParamsDesc: UnaryMethodDefinitionish;
export declare const QueryBandRelayersDesc: UnaryMethodDefinitionish;
export declare const QueryBandPriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryBandIBCPriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryPriceFeedPriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryCoinbasePriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryPythPriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryStorkPriceStatesDesc: UnaryMethodDefinitionish;
export declare const QueryStorkPublishersDesc: UnaryMethodDefinitionish;
export declare const QueryProviderPriceStateDesc: UnaryMethodDefinitionish;
export declare const QueryOracleModuleStateDesc: UnaryMethodDefinitionish;
export declare const QueryHistoricalPriceRecordsDesc: UnaryMethodDefinitionish;
export declare const QueryOracleVolatilityDesc: UnaryMethodDefinitionish;
export declare const QueryOracleProvidersInfoDesc: UnaryMethodDefinitionish;
export declare const QueryOracleProviderPricesDesc: UnaryMethodDefinitionish;
export declare const QueryOraclePriceDesc: UnaryMethodDefinitionish;
export declare const QueryPythPriceDesc: UnaryMethodDefinitionish;
interface UnaryMethodDefinitionishR extends grpc.UnaryMethodDefinition<any, any> {
    requestStream: any;
    responseStream: any;
}
type UnaryMethodDefinitionish = UnaryMethodDefinitionishR;
interface Rpc {
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
export declare class GrpcWebImpl {
    private host;
    private options;
    constructor(host: string, options: {
        transport?: grpc.TransportFactory;
        debug?: boolean;
        metadata?: grpc.Metadata;
        upStreamRetryCodes?: number[];
    });
    unary<T extends UnaryMethodDefinitionish>(methodDesc: T, _request: any, metadata: grpc.Metadata | undefined): Promise<any>;
}
declare var tsProtoGlobalThis: any;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export declare class GrpcWebError extends tsProtoGlobalThis.Error {
    code: grpc.Code;
    metadata: grpc.Metadata;
    constructor(message: string, code: grpc.Code, metadata: grpc.Metadata);
}
export {};
