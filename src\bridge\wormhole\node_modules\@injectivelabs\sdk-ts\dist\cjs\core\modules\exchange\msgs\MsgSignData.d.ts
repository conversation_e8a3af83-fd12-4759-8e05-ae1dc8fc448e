import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgSignData {
    interface Params {
        sender: string;
        data: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgSignData;
}
/**
 * @category Messages
 */
export default class MsgSignData extends MsgBase<MsgSignData.Params, MsgSignData.Proto> {
    static fromJSON(params: MsgSignData.Params): MsgSignData;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgSignData;
    toData(): {
        Signer: Uint8Array;
        Data: Uint8Array;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            signer: Uint8Array<ArrayBufferLike>;
            data: Uint8Array<ArrayBufferLike>;
        };
    };
    toWeb3Gw(): {
        signer: Uint8Array<ArrayBufferLike>;
        data: Uint8Array<ArrayBufferLike>;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgSignData;
    };
    toBinary(): Uint8Array;
}
