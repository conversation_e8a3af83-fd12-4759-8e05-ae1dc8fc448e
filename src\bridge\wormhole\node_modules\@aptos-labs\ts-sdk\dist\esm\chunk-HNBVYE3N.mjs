import{b as c}from"./chunk-RGKRCZ36.mjs";import{sha3_256 as n}from"@noble/hashes/sha3";var d=(e,r)=>{let t=e.bcsToBytes(),s=typeof r=="string"?Buffer.from(r,"utf8"):r,o=new Uint8Array([...t,...s,254]);return new c(n(o))},f=(e,r)=>{let t=e.bcsToBytes(),s=typeof r=="string"?Buffer.from(r,"utf8"):r,o=new Uint8Array([...t,...s,255]);return new c(n(o))},a=(e,r,t)=>{let s=`${r}::${t}`;return d(e,s)};export{d as a,f as b,a as c};
//# sourceMappingURL=chunk-HNBVYE3N.mjs.map