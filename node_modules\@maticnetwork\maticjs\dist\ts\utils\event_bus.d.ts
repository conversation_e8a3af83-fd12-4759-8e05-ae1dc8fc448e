export interface IEventBusPromise<T> extends Promise<T> {
    on(event: string, cb: Function): any;
    emit(event: string, ...args: any[]): any;
    destroy(): any;
}
export declare const eventBusPromise: <T>(executor: (resolve: (value?: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void) => IEventBusPromise<T>;
export declare class EventBus {
    constructor(ctx?: any);
    private _ctx;
    private _events;
    on(event: string, cb: Function): this;
    off(event: string, cb: Function): void;
    emit(event: string, ...args: any[]): Promise<any[]>;
    destroy(): void;
}
