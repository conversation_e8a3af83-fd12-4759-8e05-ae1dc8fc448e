import { PublicKey } from './PublicKey.js';
import { Address } from './Address.js';
import { CosmosTxV1Beta1Tx } from '@injectivelabs/core-proto-ts';
/**
 * Class for wrapping SigningKey that is used for signature creation and public key derivation.
 *
 * @category Crypto Utility Classes
 */
export declare class PrivateKey {
    private wallet;
    private constructor();
    /**
     * Generate new private key with random mnemonic phrase
     * @returns { privateKey: PrivateKey, mnemonic: string }
     */
    static generate(): {
        privateKey: PrivateKey;
        mnemonic: string;
    };
    /**
     * Create a PrivateKey instance from a given mnemonic phrase and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} words the mnemonic phrase
     * @param {string|undefined} path the HD path that follows the BIP32 standard (optional)
     * @returns {PrivateKey} Initialized PrivateKey object
     */
    static fromMnemonic(words: string, path?: string): PrivateKey;
    /**
     * Create a PrivateKey instance from a given private key and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} privateKey  the private key
     * @returns {PrivateKey} Initialized PrivateKey object
     *
     * @deprecated - use fromHex instead
     */
    static fromPrivateKey(privateKey: string): PrivateKey;
    /**
     * Create a PrivateKey instance from a given private key and a HD derivation path.
     * If path is not given, default to Band's HD prefix 494 and all other indexes being zeroes.
     * @param {string} privateKey  the private key
     * @returns {PrivateKey} Initialized PrivateKey object
     */
    static fromHex(privateKey: string | Uint8Array): PrivateKey;
    /**
     * Return the private key in hex
     * @returns {string}
     **/
    toPrivateKeyHex(): string;
    /**
     * Return the PublicKey associated with this private key.
     * @returns {PublicKey} a Public key that can be used to verify the signatures made with this PrivateKey
     **/
    toPublicKey(): PublicKey;
    /**
     * Return the hex address associated with this private key.
     * @returns {string}
     */
    toHex(): string;
    /**
     * Return the Address associated with this private key.
     * @returns {Address}
     **/
    toAddress(): Address;
    /**
     * Return the Bech32 address associated with this private key.
     * @returns {string}
     **/
    toBech32(): string;
    /**
     * Sign the given message using the wallet's _signingKey function.
     * @param {string} messageBytes: the message that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    sign(messageBytes: Buffer): Uint8Array;
    /**
     * Sign the given message using the edcsa sign_deterministic function.
     * @param {Buffer} messageBytes: the message that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signEcda(messageBytes: Buffer): Uint8Array;
    /**
     * Sign the given message using the wallet's _signingKey function.
     * @param {string} messageHashedBytes: the message that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashed(messageHashedBytes: Buffer): Uint8Array;
    /**
     * Sign the given message using the edcsa sign_deterministic function.
     * @param {Buffer} messageHashedBytes: the message that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashedEcda(messageHashedBytes: Buffer): Uint8Array;
    /**
     * Sign the given typed data using the edcsa sign_deterministic function.
     * @param {Buffer} eip712Data: the typed data that will be hashed and signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signTypedData(eip712Data: any): Uint8Array;
    /**
     * Sign the given typed data using the edcsa sign_deterministic function.
     * @param {Buffer} eip712Data: the typed data that will be signed, a Buffer made of bytes
     * @returns {Uint8Array} a signature of this private key over the given message
     */
    signHashedTypedData(eip712Data: Buffer): Uint8Array;
    /**
     * Verify signature using EIP712 typed data
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} eip712: the EIP712 typed data to verify against
     * @param {string} publicKey: the public key to verify against in hex
     * */
    static verifySignature({ signature, eip712, publicKey, }: {
        signature: string;
        eip712: any;
        publicKey: string;
    }): boolean;
    /**
     * Verify signature using EIP712 typed data
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} eip712: the EIP712 typed data to verify against
     * @param {string} publicKey: the public key to verify against in hex
     * */
    verifyThisPkSignature({ signature, eip712, }: {
        signature: string;
        eip712: any;
    }): boolean;
    /**
     * Verify cosmos signature EIP712 typed
     * data from the TxRaw and verify the signature
     * that's included in the TxRaw
     *
     * (params are passed as an object)
     *
     * @param {CosmosTxV1Beta1Tx.TxRaw} txRaw: the signature to verify in hex
     * @param {object} signer: the public key and the account number to verify against
     **/
    static verifyCosmosSignature({ txRaw, signer, }: {
        txRaw: CosmosTxV1Beta1Tx.TxRaw;
        signer: {
            accountNumber: number | string;
            publicKey: string;
        };
    }): boolean;
    /**
     * Verify signature using ADR-36 sign doc
     * and the publicKey
     *
     * (params are passed as an object)
     *
     * @param {string} signature: the signature to verify in hex
     * @param {any} signDoc: the signDoc to verify against
     * @param {string} publicKey:the public key to verify against in hex
     * */
    static verifyArbitrarySignature({ signature, signDoc, publicKey, }: {
        signature: string;
        signDoc: Buffer;
        publicKey: string;
    }): boolean;
}
