"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __values = (this && this.__values) || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function () {
            if (o && i >= o.length) o = void 0;
            return { value: o && o[i++], done: !o };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryDerivativeMidPriceAndTOBResponse = exports.QueryDerivativeMidPriceAndTOBRequest = exports.QuerySpotMidPriceAndTOBResponse = exports.QuerySpotMidPriceAndTOBRequest = exports.QueryAccountAddressSpotOrdersResponse = exports.QueryTraderSpotOrdersResponse = exports.TrimmedSpotLimitOrder = exports.QueryAccountAddressSpotOrdersRequest = exports.QueryTraderSpotOrdersRequest = exports.QuerySpotOrdersByHashesResponse = exports.QuerySpotOrdersByHashesRequest = exports.QueryFullSpotMarketResponse = exports.QueryFullSpotMarketRequest = exports.QueryFullSpotMarketsResponse = exports.QueryFullSpotMarketsRequest = exports.FullSpotMarket = exports.QuerySpotOrderbookResponse = exports.QuerySpotOrderbookRequest = exports.QuerySpotMarketResponse = exports.QuerySpotMarketRequest = exports.QuerySpotMarketsResponse = exports.QuerySpotMarketsRequest = exports.QuerySubaccountDepositResponse = exports.QuerySubaccountDepositRequest = exports.QueryAggregateMarketVolumesResponse = exports.QueryAggregateMarketVolumesRequest = exports.QueryDenomDecimalsResponse = exports.QueryDenomDecimalsRequest = exports.QueryDenomDecimalResponse = exports.QueryDenomDecimalRequest = exports.QueryAggregateMarketVolumeResponse = exports.QueryAggregateMarketVolumeRequest = exports.QueryAggregateVolumesResponse = exports.QueryAggregateVolumesRequest = exports.QueryAggregateVolumeResponse = exports.QueryAggregateVolumeRequest = exports.QueryExchangeBalancesResponse = exports.QueryExchangeBalancesRequest = exports.QuerySubaccountDepositsResponse_DepositsEntry = exports.QuerySubaccountDepositsResponse = exports.QuerySubaccountDepositsRequest = exports.QueryExchangeParamsResponse = exports.QueryExchangeParamsRequest = exports.SubaccountOrderbookMetadataWithMarket = exports.QuerySubaccountOrdersResponse = exports.QuerySubaccountOrdersRequest = exports.Subaccount = exports.CancellationStrategy = exports.OrderSide = exports.protobufPackage = void 0;
exports.QueryFeeDiscountAccountInfoRequest = exports.QueryOptedOutOfRewardsAccountsResponse = exports.QueryOptedOutOfRewardsAccountsRequest = exports.QueryIsOptedOutOfRewardsResponse = exports.QueryIsOptedOutOfRewardsRequest = exports.QueryTradeRewardCampaignResponse = exports.QueryTradeRewardCampaignRequest = exports.QueryTradeRewardPointsResponse = exports.QueryTradeRewardPointsRequest = exports.QueryPositionsResponse = exports.QueryPositionsRequest = exports.QueryModuleStateResponse = exports.QueryModuleStateRequest = exports.QuerySubaccountTradeNonceResponse = exports.QuerySubaccountOrderMetadataResponse = exports.QueryPerpetualMarketFundingResponse = exports.QueryPerpetualMarketFundingRequest = exports.QueryExpiryFuturesMarketInfoResponse = exports.QueryExpiryFuturesMarketInfoRequest = exports.QueryPerpetualMarketInfoResponse = exports.QueryPerpetualMarketInfoRequest = exports.QuerySubaccountEffectivePositionInMarketResponse = exports.EffectivePosition = exports.QuerySubaccountPositionInMarketResponse = exports.QuerySubaccountPositionsResponse = exports.QuerySubaccountOrderMetadataRequest = exports.QuerySubaccountEffectivePositionInMarketRequest = exports.QuerySubaccountPositionInMarketRequest = exports.QuerySubaccountPositionsRequest = exports.QuerySubaccountTradeNonceRequest = exports.QueryDerivativeMarketAddressResponse = exports.QueryDerivativeMarketAddressRequest = exports.QueryDerivativeMarketResponse = exports.QueryDerivativeMarketRequest = exports.QueryDerivativeMarketsResponse = exports.FullDerivativeMarket = exports.PerpetualMarketState = exports.PriceLevel = exports.QueryDerivativeMarketsRequest = exports.QueryDerivativeOrdersByHashesResponse = exports.QueryDerivativeOrdersByHashesRequest = exports.QueryAccountAddressDerivativeOrdersResponse = exports.QueryTraderDerivativeOrdersResponse = exports.TrimmedDerivativeLimitOrder = exports.QueryAccountAddressDerivativeOrdersRequest = exports.QueryTraderDerivativeOrdersRequest = exports.QueryTraderDerivativeOrdersToCancelUpToAmountRequest = exports.QueryTraderSpotOrdersToCancelUpToAmountRequest = exports.QueryDerivativeOrderbookResponse = exports.QueryDerivativeOrderbookRequest = void 0;
exports.QueryDesc = exports.QueryClientImpl = exports.QueryDenomMinNotionalsResponse = exports.QueryDenomMinNotionalsRequest = exports.QueryDenomMinNotionalResponse = exports.QueryDenomMinNotionalRequest = exports.MarketBalance = exports.QueryMarketBalancesResponse = exports.QueryMarketBalancesRequest = exports.QueryMarketBalanceResponse = exports.QueryMarketBalanceRequest = exports.QueryGrantAuthorizationsResponse = exports.QueryGrantAuthorizationsRequest = exports.QueryGrantAuthorizationResponse = exports.QueryGrantAuthorizationRequest = exports.QueryActiveStakeGrantResponse = exports.QueryActiveStakeGrantRequest = exports.QueryMarketAtomicExecutionFeeMultiplierResponse = exports.QueryMarketAtomicExecutionFeeMultiplierRequest = exports.TrimmedLimitOrder = exports.QueryFullDerivativeOrderbookResponse = exports.QueryFullDerivativeOrderbookRequest = exports.QueryFullSpotOrderbookResponse = exports.QueryFullSpotOrderbookRequest = exports.QueryTraderDerivativeConditionalOrdersResponse = exports.TrimmedDerivativeConditionalOrder = exports.QueryTraderDerivativeConditionalOrdersRequest = exports.QueryBinaryMarketsResponse = exports.QueryBinaryMarketsRequest = exports.QueryMarketVolatilityResponse = exports.QueryMarketVolatilityRequest = exports.TradeHistoryOptions = exports.QueryHistoricalTradeRecordsResponse = exports.QueryHistoricalTradeRecordsRequest = exports.QueryMarketIDFromVaultResponse = exports.QueryMarketIDFromVaultRequest = exports.MitoVaultInfosResponse = exports.MitoVaultInfosRequest = exports.QueryFeeDiscountTierStatisticsResponse = exports.TierStatistic = exports.QueryFeeDiscountTierStatisticsRequest = exports.QueryBalanceWithBalanceHoldsResponse = exports.BalanceWithMarginHold = exports.QueryBalanceWithBalanceHoldsRequest = exports.QueryBalanceMismatchesResponse = exports.BalanceMismatch = exports.QueryBalanceMismatchesRequest = exports.QueryFeeDiscountScheduleResponse = exports.QueryFeeDiscountScheduleRequest = exports.QueryFeeDiscountAccountInfoResponse = void 0;
exports.QueryFeeDiscountTierStatisticsDesc = exports.QueryBalanceWithBalanceHoldsDesc = exports.QueryBalanceMismatchesDesc = exports.QueryFeeDiscountScheduleDesc = exports.QueryFeeDiscountAccountInfoDesc = exports.QueryTradeRewardCampaignDesc = exports.QueryPendingTradeRewardPointsDesc = exports.QueryTradeRewardPointsDesc = exports.QuerySubaccountOrderMetadataDesc = exports.QueryPerpetualMarketFundingDesc = exports.QueryExpiryFuturesMarketInfoDesc = exports.QueryPerpetualMarketInfoDesc = exports.QuerySubaccountEffectivePositionInMarketDesc = exports.QuerySubaccountPositionInMarketDesc = exports.QuerySubaccountPositionsDesc = exports.QueryPositionsDesc = exports.QueryExchangeModuleStateDesc = exports.QuerySubaccountTradeNonceDesc = exports.QueryDerivativeMarketAddressDesc = exports.QueryDerivativeMarketDesc = exports.QueryDerivativeMarketsDesc = exports.QueryTraderDerivativeTransientOrdersDesc = exports.QueryDerivativeOrdersByHashesDesc = exports.QueryAccountAddressDerivativeOrdersDesc = exports.QueryTraderDerivativeOrdersDesc = exports.QueryDerivativeOrderbookDesc = exports.QueryDerivativeMidPriceAndTOBDesc = exports.QuerySpotMidPriceAndTOBDesc = exports.QueryTraderSpotTransientOrdersDesc = exports.QuerySubaccountOrdersDesc = exports.QuerySpotOrdersByHashesDesc = exports.QueryAccountAddressSpotOrdersDesc = exports.QueryTraderSpotOrdersDesc = exports.QuerySpotOrderbookDesc = exports.QueryFullSpotMarketDesc = exports.QueryFullSpotMarketsDesc = exports.QuerySpotMarketDesc = exports.QuerySpotMarketsDesc = exports.QueryDenomDecimalsDesc = exports.QueryDenomDecimalDesc = exports.QueryAggregateMarketVolumesDesc = exports.QueryAggregateMarketVolumeDesc = exports.QueryAggregateVolumesDesc = exports.QueryAggregateVolumeDesc = exports.QueryExchangeBalancesDesc = exports.QuerySubaccountDepositDesc = exports.QuerySubaccountDepositsDesc = exports.QueryQueryExchangeParamsDesc = exports.QueryL3SpotOrderBookDesc = exports.QueryL3DerivativeOrderBookDesc = void 0;
exports.GrpcWebError = exports.GrpcWebImpl = exports.QueryDenomMinNotionalsDesc = exports.QueryDenomMinNotionalDesc = exports.QueryMarketBalancesDesc = exports.QueryMarketBalanceDesc = exports.QueryGrantAuthorizationsDesc = exports.QueryGrantAuthorizationDesc = exports.QueryActiveStakeGrantDesc = exports.QueryMarketAtomicExecutionFeeMultiplierDesc = exports.QueryTraderDerivativeConditionalOrdersDesc = exports.QueryBinaryOptionsMarketsDesc = exports.QueryMarketVolatilityDesc = exports.QueryOptedOutOfRewardsAccountsDesc = exports.QueryIsOptedOutOfRewardsDesc = exports.QueryHistoricalTradeRecordsDesc = exports.QueryQueryMarketIDFromVaultDesc = exports.QueryMitoVaultInfosDesc = void 0;
exports.orderSideFromJSON = orderSideFromJSON;
exports.orderSideToJSON = orderSideToJSON;
exports.cancellationStrategyFromJSON = cancellationStrategyFromJSON;
exports.cancellationStrategyToJSON = cancellationStrategyToJSON;
/* eslint-disable */
var grpc_web_1 = require("@injectivelabs/grpc-web");
var browser_headers_1 = require("browser-headers");
var long_1 = __importDefault(require("long"));
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var oracle_1 = require("../../oracle/v1beta1/oracle.js");
var exchange_1 = require("./exchange.js");
var genesis_1 = require("./genesis.js");
exports.protobufPackage = "injective.exchange.v1beta1";
var OrderSide;
(function (OrderSide) {
    /** Side_Unspecified - will return both */
    OrderSide[OrderSide["Side_Unspecified"] = 0] = "Side_Unspecified";
    OrderSide[OrderSide["Buy"] = 1] = "Buy";
    OrderSide[OrderSide["Sell"] = 2] = "Sell";
    OrderSide[OrderSide["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(OrderSide || (exports.OrderSide = OrderSide = {}));
function orderSideFromJSON(object) {
    switch (object) {
        case 0:
        case "Side_Unspecified":
            return OrderSide.Side_Unspecified;
        case 1:
        case "Buy":
            return OrderSide.Buy;
        case 2:
        case "Sell":
            return OrderSide.Sell;
        case -1:
        case "UNRECOGNIZED":
        default:
            return OrderSide.UNRECOGNIZED;
    }
}
function orderSideToJSON(object) {
    switch (object) {
        case OrderSide.Side_Unspecified:
            return "Side_Unspecified";
        case OrderSide.Buy:
            return "Buy";
        case OrderSide.Sell:
            return "Sell";
        case OrderSide.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
/** CancellationStrategy is the list of cancellation strategies. */
var CancellationStrategy;
(function (CancellationStrategy) {
    /** UnspecifiedOrder - just cancelling in random order in most efficient way */
    CancellationStrategy[CancellationStrategy["UnspecifiedOrder"] = 0] = "UnspecifiedOrder";
    /** FromWorstToBest - e.g. for buy orders from lowest to highest price */
    CancellationStrategy[CancellationStrategy["FromWorstToBest"] = 1] = "FromWorstToBest";
    /** FromBestToWorst - e.g. for buy orders from higest to lowest price */
    CancellationStrategy[CancellationStrategy["FromBestToWorst"] = 2] = "FromBestToWorst";
    CancellationStrategy[CancellationStrategy["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(CancellationStrategy || (exports.CancellationStrategy = CancellationStrategy = {}));
function cancellationStrategyFromJSON(object) {
    switch (object) {
        case 0:
        case "UnspecifiedOrder":
            return CancellationStrategy.UnspecifiedOrder;
        case 1:
        case "FromWorstToBest":
            return CancellationStrategy.FromWorstToBest;
        case 2:
        case "FromBestToWorst":
            return CancellationStrategy.FromBestToWorst;
        case -1:
        case "UNRECOGNIZED":
        default:
            return CancellationStrategy.UNRECOGNIZED;
    }
}
function cancellationStrategyToJSON(object) {
    switch (object) {
        case CancellationStrategy.UnspecifiedOrder:
            return "UnspecifiedOrder";
        case CancellationStrategy.FromWorstToBest:
            return "FromWorstToBest";
        case CancellationStrategy.FromBestToWorst:
            return "FromBestToWorst";
        case CancellationStrategy.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
function createBaseSubaccount() {
    return { trader: "", subaccountNonce: 0 };
}
exports.Subaccount = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.trader !== "") {
            writer.uint32(10).string(message.trader);
        }
        if (message.subaccountNonce !== 0) {
            writer.uint32(16).uint32(message.subaccountNonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccount();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.trader = reader.string();
                    break;
                case 2:
                    message.subaccountNonce = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            trader: isSet(object.trader) ? String(object.trader) : "",
            subaccountNonce: isSet(object.subaccountNonce) ? Number(object.subaccountNonce) : 0,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.trader !== undefined && (obj.trader = message.trader);
        message.subaccountNonce !== undefined && (obj.subaccountNonce = Math.round(message.subaccountNonce));
        return obj;
    },
    create: function (base) {
        return exports.Subaccount.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseSubaccount();
        message.trader = (_a = object.trader) !== null && _a !== void 0 ? _a : "";
        message.subaccountNonce = (_b = object.subaccountNonce) !== null && _b !== void 0 ? _b : 0;
        return message;
    },
};
function createBaseQuerySubaccountOrdersRequest() {
    return { subaccountId: "", marketId: "" };
}
exports.QuerySubaccountOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySubaccountOrdersRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQuerySubaccountOrdersResponse() {
    return { buyOrders: [], sellOrders: [] };
}
exports.QuerySubaccountOrdersResponse = {
    encode: function (message, writer) {
        var e_1, _a, e_2, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.buyOrders), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.SubaccountOrderData.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_1) throw e_1.error; }
        }
        try {
            for (var _e = __values(message.sellOrders), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.SubaccountOrderData.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.buyOrders.push(exchange_1.SubaccountOrderData.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.sellOrders.push(exchange_1.SubaccountOrderData.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            buyOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.buyOrders)
                ? object.buyOrders.map(function (e) { return exchange_1.SubaccountOrderData.fromJSON(e); })
                : [],
            sellOrders: Array.isArray(object === null || object === void 0 ? void 0 : object.sellOrders)
                ? object.sellOrders.map(function (e) { return exchange_1.SubaccountOrderData.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.buyOrders) {
            obj.buyOrders = message.buyOrders.map(function (e) { return e ? exchange_1.SubaccountOrderData.toJSON(e) : undefined; });
        }
        else {
            obj.buyOrders = [];
        }
        if (message.sellOrders) {
            obj.sellOrders = message.sellOrders.map(function (e) { return e ? exchange_1.SubaccountOrderData.toJSON(e) : undefined; });
        }
        else {
            obj.sellOrders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySubaccountOrdersResponse();
        message.buyOrders = ((_a = object.buyOrders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.SubaccountOrderData.fromPartial(e); })) || [];
        message.sellOrders = ((_b = object.sellOrders) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.SubaccountOrderData.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseSubaccountOrderbookMetadataWithMarket() {
    return { metadata: undefined, marketId: "", isBuy: false };
}
exports.SubaccountOrderbookMetadataWithMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.metadata !== undefined) {
            exchange_1.SubaccountOrderbookMetadata.encode(message.metadata, writer.uint32(10).fork()).ldelim();
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.isBuy === true) {
            writer.uint32(24).bool(message.isBuy);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseSubaccountOrderbookMetadataWithMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.metadata = exchange_1.SubaccountOrderbookMetadata.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.isBuy = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            metadata: isSet(object.metadata) ? exchange_1.SubaccountOrderbookMetadata.fromJSON(object.metadata) : undefined,
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.metadata !== undefined &&
            (obj.metadata = message.metadata ? exchange_1.SubaccountOrderbookMetadata.toJSON(message.metadata) : undefined);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        return obj;
    },
    create: function (base) {
        return exports.SubaccountOrderbookMetadataWithMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseSubaccountOrderbookMetadataWithMarket();
        message.metadata = (object.metadata !== undefined && object.metadata !== null)
            ? exchange_1.SubaccountOrderbookMetadata.fromPartial(object.metadata)
            : undefined;
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.isBuy = (_b = object.isBuy) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseQueryExchangeParamsRequest() {
    return {};
}
exports.QueryExchangeParamsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExchangeParamsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryExchangeParamsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryExchangeParamsRequest();
        return message;
    },
};
function createBaseQueryExchangeParamsResponse() {
    return { params: undefined };
}
exports.QueryExchangeParamsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.params !== undefined) {
            exchange_1.Params.encode(message.params, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExchangeParamsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.params = exchange_1.Params.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { params: isSet(object.params) ? exchange_1.Params.fromJSON(object.params) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.params !== undefined && (obj.params = message.params ? exchange_1.Params.toJSON(message.params) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryExchangeParamsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryExchangeParamsResponse();
        message.params = (object.params !== undefined && object.params !== null)
            ? exchange_1.Params.fromPartial(object.params)
            : undefined;
        return message;
    },
};
function createBaseQuerySubaccountDepositsRequest() {
    return { subaccountId: "", subaccount: undefined };
}
exports.QuerySubaccountDepositsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.subaccount !== undefined) {
            exports.Subaccount.encode(message.subaccount, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountDepositsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.subaccount = exports.Subaccount.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            subaccount: isSet(object.subaccount) ? exports.Subaccount.fromJSON(object.subaccount) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.subaccount !== undefined &&
            (obj.subaccount = message.subaccount ? exports.Subaccount.toJSON(message.subaccount) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountDepositsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountDepositsRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.subaccount = (object.subaccount !== undefined && object.subaccount !== null)
            ? exports.Subaccount.fromPartial(object.subaccount)
            : undefined;
        return message;
    },
};
function createBaseQuerySubaccountDepositsResponse() {
    return { deposits: {} };
}
exports.QuerySubaccountDepositsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        Object.entries(message.deposits).forEach(function (_a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            exports.QuerySubaccountDepositsResponse_DepositsEntry.encode({ key: key, value: value }, writer.uint32(10).fork())
                .ldelim();
        });
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountDepositsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    var entry1 = exports.QuerySubaccountDepositsResponse_DepositsEntry.decode(reader, reader.uint32());
                    if (entry1.value !== undefined) {
                        message.deposits[entry1.key] = entry1.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            deposits: isObject(object.deposits)
                ? Object.entries(object.deposits).reduce(function (acc, _a) {
                    var _b = __read(_a, 2), key = _b[0], value = _b[1];
                    acc[key] = exchange_1.Deposit.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON: function (message) {
        var obj = {};
        obj.deposits = {};
        if (message.deposits) {
            Object.entries(message.deposits).forEach(function (_a) {
                var _b = __read(_a, 2), k = _b[0], v = _b[1];
                obj.deposits[k] = exchange_1.Deposit.toJSON(v);
            });
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountDepositsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountDepositsResponse();
        message.deposits = Object.entries((_a = object.deposits) !== null && _a !== void 0 ? _a : {}).reduce(function (acc, _a) {
            var _b = __read(_a, 2), key = _b[0], value = _b[1];
            if (value !== undefined) {
                acc[key] = exchange_1.Deposit.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseQuerySubaccountDepositsResponse_DepositsEntry() {
    return { key: "", value: undefined };
}
exports.QuerySubaccountDepositsResponse_DepositsEntry = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            exchange_1.Deposit.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountDepositsResponse_DepositsEntry();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = exchange_1.Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? exchange_1.Deposit.fromJSON(object.value) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? exchange_1.Deposit.toJSON(message.value) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountDepositsResponse_DepositsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountDepositsResponse_DepositsEntry();
        message.key = (_a = object.key) !== null && _a !== void 0 ? _a : "";
        message.value = (object.value !== undefined && object.value !== null)
            ? exchange_1.Deposit.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseQueryExchangeBalancesRequest() {
    return {};
}
exports.QueryExchangeBalancesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExchangeBalancesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryExchangeBalancesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryExchangeBalancesRequest();
        return message;
    },
};
function createBaseQueryExchangeBalancesResponse() {
    return { balances: [] };
}
exports.QueryExchangeBalancesResponse = {
    encode: function (message, writer) {
        var e_3, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.balances), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                genesis_1.Balance.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExchangeBalancesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balances.push(genesis_1.Balance.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { balances: Array.isArray(object === null || object === void 0 ? void 0 : object.balances) ? object.balances.map(function (e) { return genesis_1.Balance.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.balances) {
            obj.balances = message.balances.map(function (e) { return e ? genesis_1.Balance.toJSON(e) : undefined; });
        }
        else {
            obj.balances = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryExchangeBalancesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryExchangeBalancesResponse();
        message.balances = ((_a = object.balances) === null || _a === void 0 ? void 0 : _a.map(function (e) { return genesis_1.Balance.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAggregateVolumeRequest() {
    return { account: "" };
}
exports.QueryAggregateVolumeRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateVolumeRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { account: isSet(object.account) ? String(object.account) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateVolumeRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAggregateVolumeRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryAggregateVolumeResponse() {
    return { aggregateVolumes: [] };
}
exports.QueryAggregateVolumeResponse = {
    encode: function (message, writer) {
        var e_4, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.aggregateVolumes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.MarketVolume.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_4) throw e_4.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateVolumeResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.aggregateVolumes.push(exchange_1.MarketVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            aggregateVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.aggregateVolumes)
                ? object.aggregateVolumes.map(function (e) { return exchange_1.MarketVolume.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.aggregateVolumes) {
            obj.aggregateVolumes = message.aggregateVolumes.map(function (e) { return e ? exchange_1.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.aggregateVolumes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateVolumeResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAggregateVolumeResponse();
        message.aggregateVolumes = ((_a = object.aggregateVolumes) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.MarketVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAggregateVolumesRequest() {
    return { accounts: [], marketIds: [] };
}
exports.QueryAggregateVolumesRequest = {
    encode: function (message, writer) {
        var e_5, _a, e_6, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.accounts), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_5) throw e_5.error; }
        }
        try {
            for (var _e = __values(message.marketIds), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_6_1) { e_6 = { error: e_6_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_6) throw e_6.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateVolumesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accounts.push(reader.string());
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accounts: Array.isArray(object === null || object === void 0 ? void 0 : object.accounts) ? object.accounts.map(function (e) { return String(e); }) : [],
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accounts) {
            obj.accounts = message.accounts.map(function (e) { return e; });
        }
        else {
            obj.accounts = [];
        }
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateVolumesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryAggregateVolumesRequest();
        message.accounts = ((_a = object.accounts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryAggregateVolumesResponse() {
    return { aggregateAccountVolumes: [], aggregateMarketVolumes: [] };
}
exports.QueryAggregateVolumesResponse = {
    encode: function (message, writer) {
        var e_7, _a, e_8, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.aggregateAccountVolumes), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.AggregateAccountVolumeRecord.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_7_1) { e_7 = { error: e_7_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_7) throw e_7.error; }
        }
        try {
            for (var _e = __values(message.aggregateMarketVolumes), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.MarketVolume.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_8_1) { e_8 = { error: e_8_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_8) throw e_8.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateVolumesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.aggregateAccountVolumes.push(exchange_1.AggregateAccountVolumeRecord.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.aggregateMarketVolumes.push(exchange_1.MarketVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            aggregateAccountVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.aggregateAccountVolumes)
                ? object.aggregateAccountVolumes.map(function (e) { return exchange_1.AggregateAccountVolumeRecord.fromJSON(e); })
                : [],
            aggregateMarketVolumes: Array.isArray(object === null || object === void 0 ? void 0 : object.aggregateMarketVolumes)
                ? object.aggregateMarketVolumes.map(function (e) { return exchange_1.MarketVolume.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.aggregateAccountVolumes) {
            obj.aggregateAccountVolumes = message.aggregateAccountVolumes.map(function (e) {
                return e ? exchange_1.AggregateAccountVolumeRecord.toJSON(e) : undefined;
            });
        }
        else {
            obj.aggregateAccountVolumes = [];
        }
        if (message.aggregateMarketVolumes) {
            obj.aggregateMarketVolumes = message.aggregateMarketVolumes.map(function (e) { return e ? exchange_1.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.aggregateMarketVolumes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateVolumesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryAggregateVolumesResponse();
        message.aggregateAccountVolumes =
            ((_a = object.aggregateAccountVolumes) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.AggregateAccountVolumeRecord.fromPartial(e); })) || [];
        message.aggregateMarketVolumes = ((_b = object.aggregateMarketVolumes) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.MarketVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAggregateMarketVolumeRequest() {
    return { marketId: "" };
}
exports.QueryAggregateMarketVolumeRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateMarketVolumeRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateMarketVolumeRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAggregateMarketVolumeRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryAggregateMarketVolumeResponse() {
    return { volume: undefined };
}
exports.QueryAggregateMarketVolumeResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.volume !== undefined) {
            exchange_1.VolumeRecord.encode(message.volume, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateMarketVolumeResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.volume = exchange_1.VolumeRecord.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { volume: isSet(object.volume) ? exchange_1.VolumeRecord.fromJSON(object.volume) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.volume !== undefined && (obj.volume = message.volume ? exchange_1.VolumeRecord.toJSON(message.volume) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateMarketVolumeResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryAggregateMarketVolumeResponse();
        message.volume = (object.volume !== undefined && object.volume !== null)
            ? exchange_1.VolumeRecord.fromPartial(object.volume)
            : undefined;
        return message;
    },
};
function createBaseQueryDenomDecimalRequest() {
    return { denom: "" };
}
exports.QueryDenomDecimalRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomDecimalRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomDecimalRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomDecimalRequest();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDenomDecimalResponse() {
    return { decimal: "0" };
}
exports.QueryDenomDecimalResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.decimal !== "0") {
            writer.uint32(8).uint64(message.decimal);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomDecimalResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.decimal = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { decimal: isSet(object.decimal) ? String(object.decimal) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.decimal !== undefined && (obj.decimal = message.decimal);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomDecimalResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomDecimalResponse();
        message.decimal = (_a = object.decimal) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseQueryDenomDecimalsRequest() {
    return { denoms: [] };
}
exports.QueryDenomDecimalsRequest = {
    encode: function (message, writer) {
        var e_9, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.denoms), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_9_1) { e_9 = { error: e_9_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_9) throw e_9.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomDecimalsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denoms.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { denoms: Array.isArray(object === null || object === void 0 ? void 0 : object.denoms) ? object.denoms.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.denoms) {
            obj.denoms = message.denoms.map(function (e) { return e; });
        }
        else {
            obj.denoms = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomDecimalsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomDecimalsRequest();
        message.denoms = ((_a = object.denoms) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryDenomDecimalsResponse() {
    return { denomDecimals: [] };
}
exports.QueryDenomDecimalsResponse = {
    encode: function (message, writer) {
        var e_10, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.denomDecimals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DenomDecimals.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_10_1) { e_10 = { error: e_10_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_10) throw e_10.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomDecimalsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denomDecimals.push(exchange_1.DenomDecimals.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denomDecimals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomDecimals)
                ? object.denomDecimals.map(function (e) { return exchange_1.DenomDecimals.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.denomDecimals) {
            obj.denomDecimals = message.denomDecimals.map(function (e) { return e ? exchange_1.DenomDecimals.toJSON(e) : undefined; });
        }
        else {
            obj.denomDecimals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomDecimalsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomDecimalsResponse();
        message.denomDecimals = ((_a = object.denomDecimals) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.DenomDecimals.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAggregateMarketVolumesRequest() {
    return { marketIds: [] };
}
exports.QueryAggregateMarketVolumesRequest = {
    encode: function (message, writer) {
        var e_11, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_11_1) { e_11 = { error: e_11_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_11) throw e_11.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateMarketVolumesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateMarketVolumesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAggregateMarketVolumesRequest();
        message.marketIds = ((_a = object.marketIds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryAggregateMarketVolumesResponse() {
    return { volumes: [] };
}
exports.QueryAggregateMarketVolumesResponse = {
    encode: function (message, writer) {
        var e_12, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.volumes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.MarketVolume.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_12_1) { e_12 = { error: e_12_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_12) throw e_12.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAggregateMarketVolumesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.volumes.push(exchange_1.MarketVolume.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { volumes: Array.isArray(object === null || object === void 0 ? void 0 : object.volumes) ? object.volumes.map(function (e) { return exchange_1.MarketVolume.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.volumes) {
            obj.volumes = message.volumes.map(function (e) { return e ? exchange_1.MarketVolume.toJSON(e) : undefined; });
        }
        else {
            obj.volumes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAggregateMarketVolumesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAggregateMarketVolumesResponse();
        message.volumes = ((_a = object.volumes) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.MarketVolume.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQuerySubaccountDepositRequest() {
    return { subaccountId: "", denom: "" };
}
exports.QuerySubaccountDepositRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountDepositRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountDepositRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySubaccountDepositRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQuerySubaccountDepositResponse() {
    return { deposits: undefined };
}
exports.QuerySubaccountDepositResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.deposits !== undefined) {
            exchange_1.Deposit.encode(message.deposits, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountDepositResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.deposits = exchange_1.Deposit.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { deposits: isSet(object.deposits) ? exchange_1.Deposit.fromJSON(object.deposits) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.deposits !== undefined && (obj.deposits = message.deposits ? exchange_1.Deposit.toJSON(message.deposits) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountDepositResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQuerySubaccountDepositResponse();
        message.deposits = (object.deposits !== undefined && object.deposits !== null)
            ? exchange_1.Deposit.fromPartial(object.deposits)
            : undefined;
        return message;
    },
};
function createBaseQuerySpotMarketsRequest() {
    return { status: "", marketIds: [] };
}
exports.QuerySpotMarketsRequest = {
    encode: function (message, writer) {
        var e_13, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== "") {
            writer.uint32(10).string(message.status);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_13_1) { e_13 = { error: e_13_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_13) throw e_13.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMarketsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            status: isSet(object.status) ? String(object.status) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = message.status);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMarketsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySpotMarketsRequest();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQuerySpotMarketsResponse() {
    return { markets: [] };
}
exports.QuerySpotMarketsResponse = {
    encode: function (message, writer) {
        var e_14, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.markets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.SpotMarket.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_14_1) { e_14 = { error: e_14_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_14) throw e_14.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMarketsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.markets.push(exchange_1.SpotMarket.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { markets: Array.isArray(object === null || object === void 0 ? void 0 : object.markets) ? object.markets.map(function (e) { return exchange_1.SpotMarket.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.markets) {
            obj.markets = message.markets.map(function (e) { return e ? exchange_1.SpotMarket.toJSON(e) : undefined; });
        }
        else {
            obj.markets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMarketsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySpotMarketsResponse();
        message.markets = ((_a = object.markets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.SpotMarket.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQuerySpotMarketRequest() {
    return { marketId: "" };
}
exports.QuerySpotMarketRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMarketRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMarketRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySpotMarketRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQuerySpotMarketResponse() {
    return { market: undefined };
}
exports.QuerySpotMarketResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.SpotMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.SpotMarket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { market: isSet(object.market) ? exchange_1.SpotMarket.fromJSON(object.market) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.SpotMarket.toJSON(message.market) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQuerySpotMarketResponse();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.SpotMarket.fromPartial(object.market)
            : undefined;
        return message;
    },
};
function createBaseQuerySpotOrderbookRequest() {
    return { marketId: "", limit: "0", orderSide: 0, limitCumulativeNotional: "", limitCumulativeQuantity: "" };
}
exports.QuerySpotOrderbookRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.limit !== "0") {
            writer.uint32(16).uint64(message.limit);
        }
        if (message.orderSide !== 0) {
            writer.uint32(24).int32(message.orderSide);
        }
        if (message.limitCumulativeNotional !== "") {
            writer.uint32(34).string(message.limitCumulativeNotional);
        }
        if (message.limitCumulativeQuantity !== "") {
            writer.uint32(42).string(message.limitCumulativeQuantity);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotOrderbookRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.limit = longToString(reader.uint64());
                    break;
                case 3:
                    message.orderSide = reader.int32();
                    break;
                case 4:
                    message.limitCumulativeNotional = reader.string();
                    break;
                case 5:
                    message.limitCumulativeQuantity = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            limit: isSet(object.limit) ? String(object.limit) : "0",
            orderSide: isSet(object.orderSide) ? orderSideFromJSON(object.orderSide) : 0,
            limitCumulativeNotional: isSet(object.limitCumulativeNotional) ? String(object.limitCumulativeNotional) : "",
            limitCumulativeQuantity: isSet(object.limitCumulativeQuantity) ? String(object.limitCumulativeQuantity) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.limit !== undefined && (obj.limit = message.limit);
        message.orderSide !== undefined && (obj.orderSide = orderSideToJSON(message.orderSide));
        message.limitCumulativeNotional !== undefined && (obj.limitCumulativeNotional = message.limitCumulativeNotional);
        message.limitCumulativeQuantity !== undefined && (obj.limitCumulativeQuantity = message.limitCumulativeQuantity);
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotOrderbookRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseQuerySpotOrderbookRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.limit = (_b = object.limit) !== null && _b !== void 0 ? _b : "0";
        message.orderSide = (_c = object.orderSide) !== null && _c !== void 0 ? _c : 0;
        message.limitCumulativeNotional = (_d = object.limitCumulativeNotional) !== null && _d !== void 0 ? _d : "";
        message.limitCumulativeQuantity = (_e = object.limitCumulativeQuantity) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseQuerySpotOrderbookResponse() {
    return { buysPriceLevel: [], sellsPriceLevel: [] };
}
exports.QuerySpotOrderbookResponse = {
    encode: function (message, writer) {
        var e_15, _a, e_16, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.buysPriceLevel), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.Level.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_15_1) { e_15 = { error: e_15_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_15) throw e_15.error; }
        }
        try {
            for (var _e = __values(message.sellsPriceLevel), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.Level.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_16_1) { e_16 = { error: e_16_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_16) throw e_16.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotOrderbookResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.buysPriceLevel.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.sellsPriceLevel.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            buysPriceLevel: Array.isArray(object === null || object === void 0 ? void 0 : object.buysPriceLevel)
                ? object.buysPriceLevel.map(function (e) { return exchange_1.Level.fromJSON(e); })
                : [],
            sellsPriceLevel: Array.isArray(object === null || object === void 0 ? void 0 : object.sellsPriceLevel)
                ? object.sellsPriceLevel.map(function (e) { return exchange_1.Level.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.buysPriceLevel) {
            obj.buysPriceLevel = message.buysPriceLevel.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.buysPriceLevel = [];
        }
        if (message.sellsPriceLevel) {
            obj.sellsPriceLevel = message.sellsPriceLevel.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.sellsPriceLevel = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotOrderbookResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySpotOrderbookResponse();
        message.buysPriceLevel = ((_a = object.buysPriceLevel) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        message.sellsPriceLevel = ((_b = object.sellsPriceLevel) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseFullSpotMarket() {
    return { market: undefined, midPriceAndTob: undefined };
}
exports.FullSpotMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.SpotMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        if (message.midPriceAndTob !== undefined) {
            exchange_1.MidPriceAndTOB.encode(message.midPriceAndTob, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFullSpotMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.SpotMarket.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.midPriceAndTob = exchange_1.MidPriceAndTOB.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            market: isSet(object.market) ? exchange_1.SpotMarket.fromJSON(object.market) : undefined,
            midPriceAndTob: isSet(object.midPriceAndTob) ? exchange_1.MidPriceAndTOB.fromJSON(object.midPriceAndTob) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.SpotMarket.toJSON(message.market) : undefined);
        message.midPriceAndTob !== undefined &&
            (obj.midPriceAndTob = message.midPriceAndTob ? exchange_1.MidPriceAndTOB.toJSON(message.midPriceAndTob) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FullSpotMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseFullSpotMarket();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.SpotMarket.fromPartial(object.market)
            : undefined;
        message.midPriceAndTob = (object.midPriceAndTob !== undefined && object.midPriceAndTob !== null)
            ? exchange_1.MidPriceAndTOB.fromPartial(object.midPriceAndTob)
            : undefined;
        return message;
    },
};
function createBaseQueryFullSpotMarketsRequest() {
    return { status: "", marketIds: [], withMidPriceAndTob: false };
}
exports.QueryFullSpotMarketsRequest = {
    encode: function (message, writer) {
        var e_17, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== "") {
            writer.uint32(10).string(message.status);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_17_1) { e_17 = { error: e_17_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_17) throw e_17.error; }
        }
        if (message.withMidPriceAndTob === true) {
            writer.uint32(24).bool(message.withMidPriceAndTob);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotMarketsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                case 3:
                    message.withMidPriceAndTob = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            status: isSet(object.status) ? String(object.status) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
            withMidPriceAndTob: isSet(object.withMidPriceAndTob) ? Boolean(object.withMidPriceAndTob) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = message.status);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        message.withMidPriceAndTob !== undefined && (obj.withMidPriceAndTob = message.withMidPriceAndTob);
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotMarketsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryFullSpotMarketsRequest();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.withMidPriceAndTob = (_c = object.withMidPriceAndTob) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBaseQueryFullSpotMarketsResponse() {
    return { markets: [] };
}
exports.QueryFullSpotMarketsResponse = {
    encode: function (message, writer) {
        var e_18, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.markets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.FullSpotMarket.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_18_1) { e_18 = { error: e_18_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_18) throw e_18.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotMarketsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.markets.push(exports.FullSpotMarket.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            markets: Array.isArray(object === null || object === void 0 ? void 0 : object.markets) ? object.markets.map(function (e) { return exports.FullSpotMarket.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.markets) {
            obj.markets = message.markets.map(function (e) { return e ? exports.FullSpotMarket.toJSON(e) : undefined; });
        }
        else {
            obj.markets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotMarketsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFullSpotMarketsResponse();
        message.markets = ((_a = object.markets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.FullSpotMarket.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryFullSpotMarketRequest() {
    return { marketId: "", withMidPriceAndTob: false };
}
exports.QueryFullSpotMarketRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.withMidPriceAndTob === true) {
            writer.uint32(16).bool(message.withMidPriceAndTob);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotMarketRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.withMidPriceAndTob = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            withMidPriceAndTob: isSet(object.withMidPriceAndTob) ? Boolean(object.withMidPriceAndTob) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.withMidPriceAndTob !== undefined && (obj.withMidPriceAndTob = message.withMidPriceAndTob);
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotMarketRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryFullSpotMarketRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.withMidPriceAndTob = (_b = object.withMidPriceAndTob) !== null && _b !== void 0 ? _b : false;
        return message;
    },
};
function createBaseQueryFullSpotMarketResponse() {
    return { market: undefined };
}
exports.QueryFullSpotMarketResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exports.FullSpotMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exports.FullSpotMarket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { market: isSet(object.market) ? exports.FullSpotMarket.fromJSON(object.market) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exports.FullSpotMarket.toJSON(message.market) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryFullSpotMarketResponse();
        message.market = (object.market !== undefined && object.market !== null)
            ? exports.FullSpotMarket.fromPartial(object.market)
            : undefined;
        return message;
    },
};
function createBaseQuerySpotOrdersByHashesRequest() {
    return { marketId: "", subaccountId: "", orderHashes: [] };
}
exports.QuerySpotOrdersByHashesRequest = {
    encode: function (message, writer) {
        var e_19, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.orderHashes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_19_1) { e_19 = { error: e_19_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_19) throw e_19.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotOrdersByHashesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.orderHashes.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.orderHashes) ? object.orderHashes.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.orderHashes) {
            obj.orderHashes = message.orderHashes.map(function (e) { return e; });
        }
        else {
            obj.orderHashes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotOrdersByHashesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQuerySpotOrdersByHashesRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.orderHashes = ((_c = object.orderHashes) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQuerySpotOrdersByHashesResponse() {
    return { orders: [] };
}
exports.QuerySpotOrdersByHashesResponse = {
    encode: function (message, writer) {
        var e_20, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedSpotLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_20_1) { e_20 = { error: e_20_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_20) throw e_20.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotOrdersByHashesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedSpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exports.TrimmedSpotLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedSpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotOrdersByHashesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySpotOrdersByHashesResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedSpotLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryTraderSpotOrdersRequest() {
    return { marketId: "", subaccountId: "" };
}
exports.QueryTraderSpotOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderSpotOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderSpotOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryTraderSpotOrdersRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryAccountAddressSpotOrdersRequest() {
    return { marketId: "", accountAddress: "" };
}
exports.QueryAccountAddressSpotOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(18).string(message.accountAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAccountAddressSpotOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryAccountAddressSpotOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryAccountAddressSpotOrdersRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.accountAddress = (_b = object.accountAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTrimmedSpotLimitOrder() {
    return { price: "", quantity: "", fillable: "", isBuy: false, orderHash: "", cid: "" };
}
exports.TrimmedSpotLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.fillable !== "") {
            writer.uint32(26).string(message.fillable);
        }
        if (message.isBuy === true) {
            writer.uint32(32).bool(message.isBuy);
        }
        if (message.orderHash !== "") {
            writer.uint32(42).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(50).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTrimmedSpotLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.fillable = reader.string();
                    break;
                case 4:
                    message.isBuy = reader.bool();
                    break;
                case 5:
                    message.orderHash = reader.string();
                    break;
                case 6:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            fillable: isSet(object.fillable) ? String(object.fillable) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.fillable !== undefined && (obj.fillable = message.fillable);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.TrimmedSpotLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseTrimmedSpotLimitOrder();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.fillable = (_c = object.fillable) !== null && _c !== void 0 ? _c : "";
        message.isBuy = (_d = object.isBuy) !== null && _d !== void 0 ? _d : false;
        message.orderHash = (_e = object.orderHash) !== null && _e !== void 0 ? _e : "";
        message.cid = (_f = object.cid) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseQueryTraderSpotOrdersResponse() {
    return { orders: [] };
}
exports.QueryTraderSpotOrdersResponse = {
    encode: function (message, writer) {
        var e_21, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedSpotLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_21_1) { e_21 = { error: e_21_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_21) throw e_21.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderSpotOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedSpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exports.TrimmedSpotLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedSpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderSpotOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryTraderSpotOrdersResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedSpotLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAccountAddressSpotOrdersResponse() {
    return { orders: [] };
}
exports.QueryAccountAddressSpotOrdersResponse = {
    encode: function (message, writer) {
        var e_22, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedSpotLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_22_1) { e_22 = { error: e_22_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_22) throw e_22.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAccountAddressSpotOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedSpotLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders) ? object.orders.map(function (e) { return exports.TrimmedSpotLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedSpotLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAccountAddressSpotOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAccountAddressSpotOrdersResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedSpotLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQuerySpotMidPriceAndTOBRequest() {
    return { marketId: "" };
}
exports.QuerySpotMidPriceAndTOBRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMidPriceAndTOBRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMidPriceAndTOBRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySpotMidPriceAndTOBRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQuerySpotMidPriceAndTOBResponse() {
    return { midPrice: "", bestBuyPrice: "", bestSellPrice: "" };
}
exports.QuerySpotMidPriceAndTOBResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.midPrice !== "") {
            writer.uint32(10).string(message.midPrice);
        }
        if (message.bestBuyPrice !== "") {
            writer.uint32(18).string(message.bestBuyPrice);
        }
        if (message.bestSellPrice !== "") {
            writer.uint32(26).string(message.bestSellPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySpotMidPriceAndTOBResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.midPrice = reader.string();
                    break;
                case 2:
                    message.bestBuyPrice = reader.string();
                    break;
                case 3:
                    message.bestSellPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            midPrice: isSet(object.midPrice) ? String(object.midPrice) : "",
            bestBuyPrice: isSet(object.bestBuyPrice) ? String(object.bestBuyPrice) : "",
            bestSellPrice: isSet(object.bestSellPrice) ? String(object.bestSellPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.midPrice !== undefined && (obj.midPrice = message.midPrice);
        message.bestBuyPrice !== undefined && (obj.bestBuyPrice = message.bestBuyPrice);
        message.bestSellPrice !== undefined && (obj.bestSellPrice = message.bestSellPrice);
        return obj;
    },
    create: function (base) {
        return exports.QuerySpotMidPriceAndTOBResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQuerySpotMidPriceAndTOBResponse();
        message.midPrice = (_a = object.midPrice) !== null && _a !== void 0 ? _a : "";
        message.bestBuyPrice = (_b = object.bestBuyPrice) !== null && _b !== void 0 ? _b : "";
        message.bestSellPrice = (_c = object.bestSellPrice) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseQueryDerivativeMidPriceAndTOBRequest() {
    return { marketId: "" };
}
exports.QueryDerivativeMidPriceAndTOBRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMidPriceAndTOBRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMidPriceAndTOBRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDerivativeMidPriceAndTOBRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDerivativeMidPriceAndTOBResponse() {
    return { midPrice: "", bestBuyPrice: "", bestSellPrice: "" };
}
exports.QueryDerivativeMidPriceAndTOBResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.midPrice !== "") {
            writer.uint32(10).string(message.midPrice);
        }
        if (message.bestBuyPrice !== "") {
            writer.uint32(18).string(message.bestBuyPrice);
        }
        if (message.bestSellPrice !== "") {
            writer.uint32(26).string(message.bestSellPrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMidPriceAndTOBResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.midPrice = reader.string();
                    break;
                case 2:
                    message.bestBuyPrice = reader.string();
                    break;
                case 3:
                    message.bestSellPrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            midPrice: isSet(object.midPrice) ? String(object.midPrice) : "",
            bestBuyPrice: isSet(object.bestBuyPrice) ? String(object.bestBuyPrice) : "",
            bestSellPrice: isSet(object.bestSellPrice) ? String(object.bestSellPrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.midPrice !== undefined && (obj.midPrice = message.midPrice);
        message.bestBuyPrice !== undefined && (obj.bestBuyPrice = message.bestBuyPrice);
        message.bestSellPrice !== undefined && (obj.bestSellPrice = message.bestSellPrice);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMidPriceAndTOBResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryDerivativeMidPriceAndTOBResponse();
        message.midPrice = (_a = object.midPrice) !== null && _a !== void 0 ? _a : "";
        message.bestBuyPrice = (_b = object.bestBuyPrice) !== null && _b !== void 0 ? _b : "";
        message.bestSellPrice = (_c = object.bestSellPrice) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseQueryDerivativeOrderbookRequest() {
    return { marketId: "", limit: "0", limitCumulativeNotional: "" };
}
exports.QueryDerivativeOrderbookRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.limit !== "0") {
            writer.uint32(16).uint64(message.limit);
        }
        if (message.limitCumulativeNotional !== "") {
            writer.uint32(26).string(message.limitCumulativeNotional);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeOrderbookRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.limit = longToString(reader.uint64());
                    break;
                case 3:
                    message.limitCumulativeNotional = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            limit: isSet(object.limit) ? String(object.limit) : "0",
            limitCumulativeNotional: isSet(object.limitCumulativeNotional) ? String(object.limitCumulativeNotional) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.limit !== undefined && (obj.limit = message.limit);
        message.limitCumulativeNotional !== undefined && (obj.limitCumulativeNotional = message.limitCumulativeNotional);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeOrderbookRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryDerivativeOrderbookRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.limit = (_b = object.limit) !== null && _b !== void 0 ? _b : "0";
        message.limitCumulativeNotional = (_c = object.limitCumulativeNotional) !== null && _c !== void 0 ? _c : "";
        return message;
    },
};
function createBaseQueryDerivativeOrderbookResponse() {
    return { buysPriceLevel: [], sellsPriceLevel: [] };
}
exports.QueryDerivativeOrderbookResponse = {
    encode: function (message, writer) {
        var e_23, _a, e_24, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.buysPriceLevel), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exchange_1.Level.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_23_1) { e_23 = { error: e_23_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_23) throw e_23.error; }
        }
        try {
            for (var _e = __values(message.sellsPriceLevel), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exchange_1.Level.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_24_1) { e_24 = { error: e_24_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_24) throw e_24.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeOrderbookResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.buysPriceLevel.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.sellsPriceLevel.push(exchange_1.Level.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            buysPriceLevel: Array.isArray(object === null || object === void 0 ? void 0 : object.buysPriceLevel)
                ? object.buysPriceLevel.map(function (e) { return exchange_1.Level.fromJSON(e); })
                : [],
            sellsPriceLevel: Array.isArray(object === null || object === void 0 ? void 0 : object.sellsPriceLevel)
                ? object.sellsPriceLevel.map(function (e) { return exchange_1.Level.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.buysPriceLevel) {
            obj.buysPriceLevel = message.buysPriceLevel.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.buysPriceLevel = [];
        }
        if (message.sellsPriceLevel) {
            obj.sellsPriceLevel = message.sellsPriceLevel.map(function (e) { return e ? exchange_1.Level.toJSON(e) : undefined; });
        }
        else {
            obj.sellsPriceLevel = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeOrderbookResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDerivativeOrderbookResponse();
        message.buysPriceLevel = ((_a = object.buysPriceLevel) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        message.sellsPriceLevel = ((_b = object.sellsPriceLevel) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.Level.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryTraderSpotOrdersToCancelUpToAmountRequest() {
    return { marketId: "", subaccountId: "", baseAmount: "", quoteAmount: "", strategy: 0, referencePrice: "" };
}
exports.QueryTraderSpotOrdersToCancelUpToAmountRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.baseAmount !== "") {
            writer.uint32(26).string(message.baseAmount);
        }
        if (message.quoteAmount !== "") {
            writer.uint32(34).string(message.quoteAmount);
        }
        if (message.strategy !== 0) {
            writer.uint32(40).int32(message.strategy);
        }
        if (message.referencePrice !== "") {
            writer.uint32(50).string(message.referencePrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderSpotOrdersToCancelUpToAmountRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.baseAmount = reader.string();
                    break;
                case 4:
                    message.quoteAmount = reader.string();
                    break;
                case 5:
                    message.strategy = reader.int32();
                    break;
                case 6:
                    message.referencePrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            baseAmount: isSet(object.baseAmount) ? String(object.baseAmount) : "",
            quoteAmount: isSet(object.quoteAmount) ? String(object.quoteAmount) : "",
            strategy: isSet(object.strategy) ? cancellationStrategyFromJSON(object.strategy) : 0,
            referencePrice: isSet(object.referencePrice) ? String(object.referencePrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.baseAmount !== undefined && (obj.baseAmount = message.baseAmount);
        message.quoteAmount !== undefined && (obj.quoteAmount = message.quoteAmount);
        message.strategy !== undefined && (obj.strategy = cancellationStrategyToJSON(message.strategy));
        message.referencePrice !== undefined && (obj.referencePrice = message.referencePrice);
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderSpotOrdersToCancelUpToAmountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f;
        var message = createBaseQueryTraderSpotOrdersToCancelUpToAmountRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.baseAmount = (_c = object.baseAmount) !== null && _c !== void 0 ? _c : "";
        message.quoteAmount = (_d = object.quoteAmount) !== null && _d !== void 0 ? _d : "";
        message.strategy = (_e = object.strategy) !== null && _e !== void 0 ? _e : 0;
        message.referencePrice = (_f = object.referencePrice) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBaseQueryTraderDerivativeOrdersToCancelUpToAmountRequest() {
    return { marketId: "", subaccountId: "", quoteAmount: "", strategy: 0, referencePrice: "" };
}
exports.QueryTraderDerivativeOrdersToCancelUpToAmountRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        if (message.quoteAmount !== "") {
            writer.uint32(26).string(message.quoteAmount);
        }
        if (message.strategy !== 0) {
            writer.uint32(32).int32(message.strategy);
        }
        if (message.referencePrice !== "") {
            writer.uint32(42).string(message.referencePrice);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderDerivativeOrdersToCancelUpToAmountRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.quoteAmount = reader.string();
                    break;
                case 4:
                    message.strategy = reader.int32();
                    break;
                case 5:
                    message.referencePrice = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            quoteAmount: isSet(object.quoteAmount) ? String(object.quoteAmount) : "",
            strategy: isSet(object.strategy) ? cancellationStrategyFromJSON(object.strategy) : 0,
            referencePrice: isSet(object.referencePrice) ? String(object.referencePrice) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.quoteAmount !== undefined && (obj.quoteAmount = message.quoteAmount);
        message.strategy !== undefined && (obj.strategy = cancellationStrategyToJSON(message.strategy));
        message.referencePrice !== undefined && (obj.referencePrice = message.referencePrice);
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderDerivativeOrdersToCancelUpToAmountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseQueryTraderDerivativeOrdersToCancelUpToAmountRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.quoteAmount = (_c = object.quoteAmount) !== null && _c !== void 0 ? _c : "";
        message.strategy = (_d = object.strategy) !== null && _d !== void 0 ? _d : 0;
        message.referencePrice = (_e = object.referencePrice) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseQueryTraderDerivativeOrdersRequest() {
    return { marketId: "", subaccountId: "" };
}
exports.QueryTraderDerivativeOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderDerivativeOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderDerivativeOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryTraderDerivativeOrdersRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryAccountAddressDerivativeOrdersRequest() {
    return { marketId: "", accountAddress: "" };
}
exports.QueryAccountAddressDerivativeOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.accountAddress !== "") {
            writer.uint32(18).string(message.accountAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAccountAddressDerivativeOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryAccountAddressDerivativeOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryAccountAddressDerivativeOrdersRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.accountAddress = (_b = object.accountAddress) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTrimmedDerivativeLimitOrder() {
    return { price: "", quantity: "", margin: "", fillable: "", isBuy: false, orderHash: "", cid: "" };
}
exports.TrimmedDerivativeLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.margin !== "") {
            writer.uint32(26).string(message.margin);
        }
        if (message.fillable !== "") {
            writer.uint32(34).string(message.fillable);
        }
        if (message.isBuy === true) {
            writer.uint32(40).bool(message.isBuy);
        }
        if (message.orderHash !== "") {
            writer.uint32(50).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(58).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTrimmedDerivativeLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.margin = reader.string();
                    break;
                case 4:
                    message.fillable = reader.string();
                    break;
                case 5:
                    message.isBuy = reader.bool();
                    break;
                case 6:
                    message.orderHash = reader.string();
                    break;
                case 7:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            fillable: isSet(object.fillable) ? String(object.fillable) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.margin !== undefined && (obj.margin = message.margin);
        message.fillable !== undefined && (obj.fillable = message.fillable);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.TrimmedDerivativeLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseTrimmedDerivativeLimitOrder();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.margin = (_c = object.margin) !== null && _c !== void 0 ? _c : "";
        message.fillable = (_d = object.fillable) !== null && _d !== void 0 ? _d : "";
        message.isBuy = (_e = object.isBuy) !== null && _e !== void 0 ? _e : false;
        message.orderHash = (_f = object.orderHash) !== null && _f !== void 0 ? _f : "";
        message.cid = (_g = object.cid) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseQueryTraderDerivativeOrdersResponse() {
    return { orders: [] };
}
exports.QueryTraderDerivativeOrdersResponse = {
    encode: function (message, writer) {
        var e_25, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedDerivativeLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_25_1) { e_25 = { error: e_25_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_25) throw e_25.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderDerivativeOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedDerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders)
                ? object.orders.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedDerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderDerivativeOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryTraderDerivativeOrdersResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryAccountAddressDerivativeOrdersResponse() {
    return { orders: [] };
}
exports.QueryAccountAddressDerivativeOrdersResponse = {
    encode: function (message, writer) {
        var e_26, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedDerivativeLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_26_1) { e_26 = { error: e_26_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_26) throw e_26.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryAccountAddressDerivativeOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedDerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders)
                ? object.orders.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedDerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryAccountAddressDerivativeOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryAccountAddressDerivativeOrdersResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryDerivativeOrdersByHashesRequest() {
    return { marketId: "", subaccountId: "", orderHashes: [] };
}
exports.QueryDerivativeOrdersByHashesRequest = {
    encode: function (message, writer) {
        var e_27, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        try {
            for (var _b = __values(message.orderHashes), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_27_1) { e_27 = { error: e_27_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_27) throw e_27.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeOrdersByHashesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                case 3:
                    message.orderHashes.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            orderHashes: Array.isArray(object === null || object === void 0 ? void 0 : object.orderHashes) ? object.orderHashes.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        if (message.orderHashes) {
            obj.orderHashes = message.orderHashes.map(function (e) { return e; });
        }
        else {
            obj.orderHashes = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeOrdersByHashesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryDerivativeOrdersByHashesRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        message.orderHashes = ((_c = object.orderHashes) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryDerivativeOrdersByHashesResponse() {
    return { orders: [] };
}
exports.QueryDerivativeOrdersByHashesResponse = {
    encode: function (message, writer) {
        var e_28, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedDerivativeLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_28_1) { e_28 = { error: e_28_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_28) throw e_28.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeOrdersByHashesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedDerivativeLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders)
                ? object.orders.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedDerivativeLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeOrdersByHashesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDerivativeOrdersByHashesResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedDerivativeLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryDerivativeMarketsRequest() {
    return { status: "", marketIds: [], withMidPriceAndTob: false };
}
exports.QueryDerivativeMarketsRequest = {
    encode: function (message, writer) {
        var e_29, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== "") {
            writer.uint32(10).string(message.status);
        }
        try {
            for (var _b = __values(message.marketIds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_29_1) { e_29 = { error: e_29_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_29) throw e_29.error; }
        }
        if (message.withMidPriceAndTob === true) {
            writer.uint32(24).bool(message.withMidPriceAndTob);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.string();
                    break;
                case 2:
                    message.marketIds.push(reader.string());
                    break;
                case 3:
                    message.withMidPriceAndTob = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            status: isSet(object.status) ? String(object.status) : "",
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map(function (e) { return String(e); }) : [],
            withMidPriceAndTob: isSet(object.withMidPriceAndTob) ? Boolean(object.withMidPriceAndTob) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = message.status);
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map(function (e) { return e; });
        }
        else {
            obj.marketIds = [];
        }
        message.withMidPriceAndTob !== undefined && (obj.withMidPriceAndTob = message.withMidPriceAndTob);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c;
        var message = createBaseQueryDerivativeMarketsRequest();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : "";
        message.marketIds = ((_b = object.marketIds) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.withMidPriceAndTob = (_c = object.withMidPriceAndTob) !== null && _c !== void 0 ? _c : false;
        return message;
    },
};
function createBasePriceLevel() {
    return { price: "", quantity: "" };
}
exports.PriceLevel = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePriceLevel();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        return obj;
    },
    create: function (base) {
        return exports.PriceLevel.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBasePriceLevel();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBasePerpetualMarketState() {
    return { marketInfo: undefined, fundingInfo: undefined };
}
exports.PerpetualMarketState = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketInfo !== undefined) {
            exchange_1.PerpetualMarketInfo.encode(message.marketInfo, writer.uint32(10).fork()).ldelim();
        }
        if (message.fundingInfo !== undefined) {
            exchange_1.PerpetualMarketFunding.encode(message.fundingInfo, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBasePerpetualMarketState();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketInfo = exchange_1.PerpetualMarketInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.fundingInfo = exchange_1.PerpetualMarketFunding.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketInfo: isSet(object.marketInfo) ? exchange_1.PerpetualMarketInfo.fromJSON(object.marketInfo) : undefined,
            fundingInfo: isSet(object.fundingInfo) ? exchange_1.PerpetualMarketFunding.fromJSON(object.fundingInfo) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketInfo !== undefined &&
            (obj.marketInfo = message.marketInfo ? exchange_1.PerpetualMarketInfo.toJSON(message.marketInfo) : undefined);
        message.fundingInfo !== undefined &&
            (obj.fundingInfo = message.fundingInfo ? exchange_1.PerpetualMarketFunding.toJSON(message.fundingInfo) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.PerpetualMarketState.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBasePerpetualMarketState();
        message.marketInfo = (object.marketInfo !== undefined && object.marketInfo !== null)
            ? exchange_1.PerpetualMarketInfo.fromPartial(object.marketInfo)
            : undefined;
        message.fundingInfo = (object.fundingInfo !== undefined && object.fundingInfo !== null)
            ? exchange_1.PerpetualMarketFunding.fromPartial(object.fundingInfo)
            : undefined;
        return message;
    },
};
function createBaseFullDerivativeMarket() {
    return {
        market: undefined,
        perpetualInfo: undefined,
        futuresInfo: undefined,
        markPrice: "",
        midPriceAndTob: undefined,
    };
}
exports.FullDerivativeMarket = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exchange_1.DerivativeMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        if (message.perpetualInfo !== undefined) {
            exports.PerpetualMarketState.encode(message.perpetualInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.futuresInfo !== undefined) {
            exchange_1.ExpiryFuturesMarketInfo.encode(message.futuresInfo, writer.uint32(26).fork()).ldelim();
        }
        if (message.markPrice !== "") {
            writer.uint32(34).string(message.markPrice);
        }
        if (message.midPriceAndTob !== undefined) {
            exchange_1.MidPriceAndTOB.encode(message.midPriceAndTob, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseFullDerivativeMarket();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exchange_1.DerivativeMarket.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.perpetualInfo = exports.PerpetualMarketState.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.futuresInfo = exchange_1.ExpiryFuturesMarketInfo.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.markPrice = reader.string();
                    break;
                case 5:
                    message.midPriceAndTob = exchange_1.MidPriceAndTOB.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            market: isSet(object.market) ? exchange_1.DerivativeMarket.fromJSON(object.market) : undefined,
            perpetualInfo: isSet(object.perpetualInfo) ? exports.PerpetualMarketState.fromJSON(object.perpetualInfo) : undefined,
            futuresInfo: isSet(object.futuresInfo) ? exchange_1.ExpiryFuturesMarketInfo.fromJSON(object.futuresInfo) : undefined,
            markPrice: isSet(object.markPrice) ? String(object.markPrice) : "",
            midPriceAndTob: isSet(object.midPriceAndTob) ? exchange_1.MidPriceAndTOB.fromJSON(object.midPriceAndTob) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined && (obj.market = message.market ? exchange_1.DerivativeMarket.toJSON(message.market) : undefined);
        message.perpetualInfo !== undefined &&
            (obj.perpetualInfo = message.perpetualInfo ? exports.PerpetualMarketState.toJSON(message.perpetualInfo) : undefined);
        message.futuresInfo !== undefined &&
            (obj.futuresInfo = message.futuresInfo ? exchange_1.ExpiryFuturesMarketInfo.toJSON(message.futuresInfo) : undefined);
        message.markPrice !== undefined && (obj.markPrice = message.markPrice);
        message.midPriceAndTob !== undefined &&
            (obj.midPriceAndTob = message.midPriceAndTob ? exchange_1.MidPriceAndTOB.toJSON(message.midPriceAndTob) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.FullDerivativeMarket.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseFullDerivativeMarket();
        message.market = (object.market !== undefined && object.market !== null)
            ? exchange_1.DerivativeMarket.fromPartial(object.market)
            : undefined;
        message.perpetualInfo = (object.perpetualInfo !== undefined && object.perpetualInfo !== null)
            ? exports.PerpetualMarketState.fromPartial(object.perpetualInfo)
            : undefined;
        message.futuresInfo = (object.futuresInfo !== undefined && object.futuresInfo !== null)
            ? exchange_1.ExpiryFuturesMarketInfo.fromPartial(object.futuresInfo)
            : undefined;
        message.markPrice = (_a = object.markPrice) !== null && _a !== void 0 ? _a : "";
        message.midPriceAndTob = (object.midPriceAndTob !== undefined && object.midPriceAndTob !== null)
            ? exchange_1.MidPriceAndTOB.fromPartial(object.midPriceAndTob)
            : undefined;
        return message;
    },
};
function createBaseQueryDerivativeMarketsResponse() {
    return { markets: [] };
}
exports.QueryDerivativeMarketsResponse = {
    encode: function (message, writer) {
        var e_30, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.markets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.FullDerivativeMarket.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_30_1) { e_30 = { error: e_30_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_30) throw e_30.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.markets.push(exports.FullDerivativeMarket.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            markets: Array.isArray(object === null || object === void 0 ? void 0 : object.markets) ? object.markets.map(function (e) { return exports.FullDerivativeMarket.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.markets) {
            obj.markets = message.markets.map(function (e) { return e ? exports.FullDerivativeMarket.toJSON(e) : undefined; });
        }
        else {
            obj.markets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDerivativeMarketsResponse();
        message.markets = ((_a = object.markets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.FullDerivativeMarket.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryDerivativeMarketRequest() {
    return { marketId: "" };
}
exports.QueryDerivativeMarketRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDerivativeMarketRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDerivativeMarketResponse() {
    return { market: undefined };
}
exports.QueryDerivativeMarketResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.market !== undefined) {
            exports.FullDerivativeMarket.encode(message.market, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.market = exports.FullDerivativeMarket.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { market: isSet(object.market) ? exports.FullDerivativeMarket.fromJSON(object.market) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.market !== undefined &&
            (obj.market = message.market ? exports.FullDerivativeMarket.toJSON(message.market) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryDerivativeMarketResponse();
        message.market = (object.market !== undefined && object.market !== null)
            ? exports.FullDerivativeMarket.fromPartial(object.market)
            : undefined;
        return message;
    },
};
function createBaseQueryDerivativeMarketAddressRequest() {
    return { marketId: "" };
}
exports.QueryDerivativeMarketAddressRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketAddressRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketAddressRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDerivativeMarketAddressRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDerivativeMarketAddressResponse() {
    return { address: "", subaccountId: "" };
}
exports.QueryDerivativeMarketAddressResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.address !== "") {
            writer.uint32(10).string(message.address);
        }
        if (message.subaccountId !== "") {
            writer.uint32(18).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDerivativeMarketAddressResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.address = reader.string();
                    break;
                case 2:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            address: isSet(object.address) ? String(object.address) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.address !== undefined && (obj.address = message.address);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QueryDerivativeMarketAddressResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryDerivativeMarketAddressResponse();
        message.address = (_a = object.address) !== null && _a !== void 0 ? _a : "";
        message.subaccountId = (_b = object.subaccountId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQuerySubaccountTradeNonceRequest() {
    return { subaccountId: "" };
}
exports.QuerySubaccountTradeNonceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountTradeNonceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountTradeNonceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountTradeNonceRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQuerySubaccountPositionsRequest() {
    return { subaccountId: "" };
}
exports.QuerySubaccountPositionsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountPositionsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountPositionsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountPositionsRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQuerySubaccountPositionInMarketRequest() {
    return { subaccountId: "", marketId: "" };
}
exports.QuerySubaccountPositionInMarketRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountPositionInMarketRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountPositionInMarketRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySubaccountPositionInMarketRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQuerySubaccountEffectivePositionInMarketRequest() {
    return { subaccountId: "", marketId: "" };
}
exports.QuerySubaccountEffectivePositionInMarketRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountEffectivePositionInMarketRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountEffectivePositionInMarketRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQuerySubaccountEffectivePositionInMarketRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQuerySubaccountOrderMetadataRequest() {
    return { subaccountId: "" };
}
exports.QuerySubaccountOrderMetadataRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountOrderMetadataRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountOrderMetadataRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountOrderMetadataRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQuerySubaccountPositionsResponse() {
    return { state: [] };
}
exports.QuerySubaccountPositionsResponse = {
    encode: function (message, writer) {
        var e_31, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.state), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                genesis_1.DerivativePosition.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_31_1) { e_31 = { error: e_31_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_31) throw e_31.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountPositionsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state.push(genesis_1.DerivativePosition.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: Array.isArray(object === null || object === void 0 ? void 0 : object.state) ? object.state.map(function (e) { return genesis_1.DerivativePosition.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.state) {
            obj.state = message.state.map(function (e) { return e ? genesis_1.DerivativePosition.toJSON(e) : undefined; });
        }
        else {
            obj.state = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountPositionsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountPositionsResponse();
        message.state = ((_a = object.state) === null || _a === void 0 ? void 0 : _a.map(function (e) { return genesis_1.DerivativePosition.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQuerySubaccountPositionInMarketResponse() {
    return { state: undefined };
}
exports.QuerySubaccountPositionInMarketResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            exchange_1.Position.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountPositionInMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = exchange_1.Position.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? exchange_1.Position.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? exchange_1.Position.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountPositionInMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQuerySubaccountPositionInMarketResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? exchange_1.Position.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseEffectivePosition() {
    return { isLong: false, quantity: "", entryPrice: "", effectiveMargin: "" };
}
exports.EffectivePosition = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.isLong === true) {
            writer.uint32(8).bool(message.isLong);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.entryPrice !== "") {
            writer.uint32(26).string(message.entryPrice);
        }
        if (message.effectiveMargin !== "") {
            writer.uint32(34).string(message.effectiveMargin);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEffectivePosition();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.isLong = reader.bool();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.entryPrice = reader.string();
                    break;
                case 4:
                    message.effectiveMargin = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            isLong: isSet(object.isLong) ? Boolean(object.isLong) : false,
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            entryPrice: isSet(object.entryPrice) ? String(object.entryPrice) : "",
            effectiveMargin: isSet(object.effectiveMargin) ? String(object.effectiveMargin) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.isLong !== undefined && (obj.isLong = message.isLong);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.entryPrice !== undefined && (obj.entryPrice = message.entryPrice);
        message.effectiveMargin !== undefined && (obj.effectiveMargin = message.effectiveMargin);
        return obj;
    },
    create: function (base) {
        return exports.EffectivePosition.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseEffectivePosition();
        message.isLong = (_a = object.isLong) !== null && _a !== void 0 ? _a : false;
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.entryPrice = (_c = object.entryPrice) !== null && _c !== void 0 ? _c : "";
        message.effectiveMargin = (_d = object.effectiveMargin) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseQuerySubaccountEffectivePositionInMarketResponse() {
    return { state: undefined };
}
exports.QuerySubaccountEffectivePositionInMarketResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            exports.EffectivePosition.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountEffectivePositionInMarketResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = exports.EffectivePosition.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? exports.EffectivePosition.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? exports.EffectivePosition.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountEffectivePositionInMarketResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQuerySubaccountEffectivePositionInMarketResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? exports.EffectivePosition.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseQueryPerpetualMarketInfoRequest() {
    return { marketId: "" };
}
exports.QueryPerpetualMarketInfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPerpetualMarketInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryPerpetualMarketInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPerpetualMarketInfoRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryPerpetualMarketInfoResponse() {
    return { info: undefined };
}
exports.QueryPerpetualMarketInfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.info !== undefined) {
            exchange_1.PerpetualMarketInfo.encode(message.info, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPerpetualMarketInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.info = exchange_1.PerpetualMarketInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { info: isSet(object.info) ? exchange_1.PerpetualMarketInfo.fromJSON(object.info) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.info !== undefined && (obj.info = message.info ? exchange_1.PerpetualMarketInfo.toJSON(message.info) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryPerpetualMarketInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryPerpetualMarketInfoResponse();
        message.info = (object.info !== undefined && object.info !== null)
            ? exchange_1.PerpetualMarketInfo.fromPartial(object.info)
            : undefined;
        return message;
    },
};
function createBaseQueryExpiryFuturesMarketInfoRequest() {
    return { marketId: "" };
}
exports.QueryExpiryFuturesMarketInfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExpiryFuturesMarketInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryExpiryFuturesMarketInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryExpiryFuturesMarketInfoRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryExpiryFuturesMarketInfoResponse() {
    return { info: undefined };
}
exports.QueryExpiryFuturesMarketInfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.info !== undefined) {
            exchange_1.ExpiryFuturesMarketInfo.encode(message.info, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryExpiryFuturesMarketInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.info = exchange_1.ExpiryFuturesMarketInfo.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { info: isSet(object.info) ? exchange_1.ExpiryFuturesMarketInfo.fromJSON(object.info) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.info !== undefined && (obj.info = message.info ? exchange_1.ExpiryFuturesMarketInfo.toJSON(message.info) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryExpiryFuturesMarketInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryExpiryFuturesMarketInfoResponse();
        message.info = (object.info !== undefined && object.info !== null)
            ? exchange_1.ExpiryFuturesMarketInfo.fromPartial(object.info)
            : undefined;
        return message;
    },
};
function createBaseQueryPerpetualMarketFundingRequest() {
    return { marketId: "" };
}
exports.QueryPerpetualMarketFundingRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPerpetualMarketFundingRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryPerpetualMarketFundingRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPerpetualMarketFundingRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryPerpetualMarketFundingResponse() {
    return { state: undefined };
}
exports.QueryPerpetualMarketFundingResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            exchange_1.PerpetualMarketFunding.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPerpetualMarketFundingResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = exchange_1.PerpetualMarketFunding.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? exchange_1.PerpetualMarketFunding.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined &&
            (obj.state = message.state ? exchange_1.PerpetualMarketFunding.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryPerpetualMarketFundingResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryPerpetualMarketFundingResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? exchange_1.PerpetualMarketFunding.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseQuerySubaccountOrderMetadataResponse() {
    return { metadata: [] };
}
exports.QuerySubaccountOrderMetadataResponse = {
    encode: function (message, writer) {
        var e_32, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.metadata), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.SubaccountOrderbookMetadataWithMarket.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_32_1) { e_32 = { error: e_32_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_32) throw e_32.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountOrderMetadataResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.metadata.push(exports.SubaccountOrderbookMetadataWithMarket.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            metadata: Array.isArray(object === null || object === void 0 ? void 0 : object.metadata)
                ? object.metadata.map(function (e) { return exports.SubaccountOrderbookMetadataWithMarket.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.metadata) {
            obj.metadata = message.metadata.map(function (e) { return e ? exports.SubaccountOrderbookMetadataWithMarket.toJSON(e) : undefined; });
        }
        else {
            obj.metadata = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountOrderMetadataResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountOrderMetadataResponse();
        message.metadata = ((_a = object.metadata) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.SubaccountOrderbookMetadataWithMarket.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQuerySubaccountTradeNonceResponse() {
    return { nonce: 0 };
}
exports.QuerySubaccountTradeNonceResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.nonce !== 0) {
            writer.uint32(8).uint32(message.nonce);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQuerySubaccountTradeNonceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.nonce = reader.uint32();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { nonce: isSet(object.nonce) ? Number(object.nonce) : 0 };
    },
    toJSON: function (message) {
        var obj = {};
        message.nonce !== undefined && (obj.nonce = Math.round(message.nonce));
        return obj;
    },
    create: function (base) {
        return exports.QuerySubaccountTradeNonceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQuerySubaccountTradeNonceResponse();
        message.nonce = (_a = object.nonce) !== null && _a !== void 0 ? _a : 0;
        return message;
    },
};
function createBaseQueryModuleStateRequest() {
    return {};
}
exports.QueryModuleStateRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryModuleStateRequest();
        return message;
    },
};
function createBaseQueryModuleStateResponse() {
    return { state: undefined };
}
exports.QueryModuleStateResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.state !== undefined) {
            genesis_1.GenesisState.encode(message.state, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryModuleStateResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state = genesis_1.GenesisState.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: isSet(object.state) ? genesis_1.GenesisState.fromJSON(object.state) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.state !== undefined && (obj.state = message.state ? genesis_1.GenesisState.toJSON(message.state) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryModuleStateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryModuleStateResponse();
        message.state = (object.state !== undefined && object.state !== null)
            ? genesis_1.GenesisState.fromPartial(object.state)
            : undefined;
        return message;
    },
};
function createBaseQueryPositionsRequest() {
    return {};
}
exports.QueryPositionsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPositionsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryPositionsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryPositionsRequest();
        return message;
    },
};
function createBaseQueryPositionsResponse() {
    return { state: [] };
}
exports.QueryPositionsResponse = {
    encode: function (message, writer) {
        var e_33, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.state), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                genesis_1.DerivativePosition.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_33_1) { e_33 = { error: e_33_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_33) throw e_33.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryPositionsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.state.push(genesis_1.DerivativePosition.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { state: Array.isArray(object === null || object === void 0 ? void 0 : object.state) ? object.state.map(function (e) { return genesis_1.DerivativePosition.fromJSON(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.state) {
            obj.state = message.state.map(function (e) { return e ? genesis_1.DerivativePosition.toJSON(e) : undefined; });
        }
        else {
            obj.state = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryPositionsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryPositionsResponse();
        message.state = ((_a = object.state) === null || _a === void 0 ? void 0 : _a.map(function (e) { return genesis_1.DerivativePosition.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryTradeRewardPointsRequest() {
    return { accounts: [], pendingPoolTimestamp: "0" };
}
exports.QueryTradeRewardPointsRequest = {
    encode: function (message, writer) {
        var e_34, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.accounts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_34_1) { e_34 = { error: e_34_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_34) throw e_34.error; }
        }
        if (message.pendingPoolTimestamp !== "0") {
            writer.uint32(16).int64(message.pendingPoolTimestamp);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTradeRewardPointsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accounts.push(reader.string());
                    break;
                case 2:
                    message.pendingPoolTimestamp = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accounts: Array.isArray(object === null || object === void 0 ? void 0 : object.accounts) ? object.accounts.map(function (e) { return String(e); }) : [],
            pendingPoolTimestamp: isSet(object.pendingPoolTimestamp) ? String(object.pendingPoolTimestamp) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accounts) {
            obj.accounts = message.accounts.map(function (e) { return e; });
        }
        else {
            obj.accounts = [];
        }
        message.pendingPoolTimestamp !== undefined && (obj.pendingPoolTimestamp = message.pendingPoolTimestamp);
        return obj;
    },
    create: function (base) {
        return exports.QueryTradeRewardPointsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryTradeRewardPointsRequest();
        message.accounts = ((_a = object.accounts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.pendingPoolTimestamp = (_b = object.pendingPoolTimestamp) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseQueryTradeRewardPointsResponse() {
    return { accountTradeRewardPoints: [] };
}
exports.QueryTradeRewardPointsResponse = {
    encode: function (message, writer) {
        var e_35, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.accountTradeRewardPoints), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_35_1) { e_35 = { error: e_35_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_35) throw e_35.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTradeRewardPointsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountTradeRewardPoints.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            accountTradeRewardPoints: Array.isArray(object === null || object === void 0 ? void 0 : object.accountTradeRewardPoints)
                ? object.accountTradeRewardPoints.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accountTradeRewardPoints) {
            obj.accountTradeRewardPoints = message.accountTradeRewardPoints.map(function (e) { return e; });
        }
        else {
            obj.accountTradeRewardPoints = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryTradeRewardPointsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryTradeRewardPointsResponse();
        message.accountTradeRewardPoints = ((_a = object.accountTradeRewardPoints) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryTradeRewardCampaignRequest() {
    return {};
}
exports.QueryTradeRewardCampaignRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTradeRewardCampaignRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryTradeRewardCampaignRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryTradeRewardCampaignRequest();
        return message;
    },
};
function createBaseQueryTradeRewardCampaignResponse() {
    return {
        tradingRewardCampaignInfo: undefined,
        tradingRewardPoolCampaignSchedule: [],
        totalTradeRewardPoints: "",
        pendingTradingRewardPoolCampaignSchedule: [],
        pendingTotalTradeRewardPoints: [],
    };
}
exports.QueryTradeRewardCampaignResponse = {
    encode: function (message, writer) {
        var e_36, _a, e_37, _b, e_38, _c;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tradingRewardCampaignInfo !== undefined) {
            exchange_1.TradingRewardCampaignInfo.encode(message.tradingRewardCampaignInfo, writer.uint32(10).fork()).ldelim();
        }
        try {
            for (var _d = __values(message.tradingRewardPoolCampaignSchedule), _e = _d.next(); !_e.done; _e = _d.next()) {
                var v = _e.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_36_1) { e_36 = { error: e_36_1 }; }
        finally {
            try {
                if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
            }
            finally { if (e_36) throw e_36.error; }
        }
        if (message.totalTradeRewardPoints !== "") {
            writer.uint32(26).string(message.totalTradeRewardPoints);
        }
        try {
            for (var _f = __values(message.pendingTradingRewardPoolCampaignSchedule), _g = _f.next(); !_g.done; _g = _f.next()) {
                var v = _g.value;
                exchange_1.CampaignRewardPool.encode(v, writer.uint32(34).fork()).ldelim();
            }
        }
        catch (e_37_1) { e_37 = { error: e_37_1 }; }
        finally {
            try {
                if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
            }
            finally { if (e_37) throw e_37.error; }
        }
        try {
            for (var _h = __values(message.pendingTotalTradeRewardPoints), _j = _h.next(); !_j.done; _j = _h.next()) {
                var v = _j.value;
                writer.uint32(42).string(v);
            }
        }
        catch (e_38_1) { e_38 = { error: e_38_1 }; }
        finally {
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            }
            finally { if (e_38) throw e_38.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTradeRewardCampaignResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tradingRewardCampaignInfo = exchange_1.TradingRewardCampaignInfo.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.tradingRewardPoolCampaignSchedule.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.totalTradeRewardPoints = reader.string();
                    break;
                case 4:
                    message.pendingTradingRewardPoolCampaignSchedule.push(exchange_1.CampaignRewardPool.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.pendingTotalTradeRewardPoints.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tradingRewardCampaignInfo: isSet(object.tradingRewardCampaignInfo)
                ? exchange_1.TradingRewardCampaignInfo.fromJSON(object.tradingRewardCampaignInfo)
                : undefined,
            tradingRewardPoolCampaignSchedule: Array.isArray(object === null || object === void 0 ? void 0 : object.tradingRewardPoolCampaignSchedule)
                ? object.tradingRewardPoolCampaignSchedule.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
            totalTradeRewardPoints: isSet(object.totalTradeRewardPoints) ? String(object.totalTradeRewardPoints) : "",
            pendingTradingRewardPoolCampaignSchedule: Array.isArray(object === null || object === void 0 ? void 0 : object.pendingTradingRewardPoolCampaignSchedule)
                ? object.pendingTradingRewardPoolCampaignSchedule.map(function (e) { return exchange_1.CampaignRewardPool.fromJSON(e); })
                : [],
            pendingTotalTradeRewardPoints: Array.isArray(object === null || object === void 0 ? void 0 : object.pendingTotalTradeRewardPoints)
                ? object.pendingTotalTradeRewardPoints.map(function (e) { return String(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tradingRewardCampaignInfo !== undefined &&
            (obj.tradingRewardCampaignInfo = message.tradingRewardCampaignInfo
                ? exchange_1.TradingRewardCampaignInfo.toJSON(message.tradingRewardCampaignInfo)
                : undefined);
        if (message.tradingRewardPoolCampaignSchedule) {
            obj.tradingRewardPoolCampaignSchedule = message.tradingRewardPoolCampaignSchedule.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.tradingRewardPoolCampaignSchedule = [];
        }
        message.totalTradeRewardPoints !== undefined && (obj.totalTradeRewardPoints = message.totalTradeRewardPoints);
        if (message.pendingTradingRewardPoolCampaignSchedule) {
            obj.pendingTradingRewardPoolCampaignSchedule = message.pendingTradingRewardPoolCampaignSchedule.map(function (e) {
                return e ? exchange_1.CampaignRewardPool.toJSON(e) : undefined;
            });
        }
        else {
            obj.pendingTradingRewardPoolCampaignSchedule = [];
        }
        if (message.pendingTotalTradeRewardPoints) {
            obj.pendingTotalTradeRewardPoints = message.pendingTotalTradeRewardPoints.map(function (e) { return e; });
        }
        else {
            obj.pendingTotalTradeRewardPoints = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryTradeRewardCampaignResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseQueryTradeRewardCampaignResponse();
        message.tradingRewardCampaignInfo =
            (object.tradingRewardCampaignInfo !== undefined && object.tradingRewardCampaignInfo !== null)
                ? exchange_1.TradingRewardCampaignInfo.fromPartial(object.tradingRewardCampaignInfo)
                : undefined;
        message.tradingRewardPoolCampaignSchedule =
            ((_a = object.tradingRewardPoolCampaignSchedule) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        message.totalTradeRewardPoints = (_b = object.totalTradeRewardPoints) !== null && _b !== void 0 ? _b : "";
        message.pendingTradingRewardPoolCampaignSchedule =
            ((_c = object.pendingTradingRewardPoolCampaignSchedule) === null || _c === void 0 ? void 0 : _c.map(function (e) { return exchange_1.CampaignRewardPool.fromPartial(e); })) || [];
        message.pendingTotalTradeRewardPoints = ((_d = object.pendingTotalTradeRewardPoints) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryIsOptedOutOfRewardsRequest() {
    return { account: "" };
}
exports.QueryIsOptedOutOfRewardsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryIsOptedOutOfRewardsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { account: isSet(object.account) ? String(object.account) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create: function (base) {
        return exports.QueryIsOptedOutOfRewardsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryIsOptedOutOfRewardsRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryIsOptedOutOfRewardsResponse() {
    return { isOptedOut: false };
}
exports.QueryIsOptedOutOfRewardsResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.isOptedOut === true) {
            writer.uint32(8).bool(message.isOptedOut);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryIsOptedOutOfRewardsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.isOptedOut = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { isOptedOut: isSet(object.isOptedOut) ? Boolean(object.isOptedOut) : false };
    },
    toJSON: function (message) {
        var obj = {};
        message.isOptedOut !== undefined && (obj.isOptedOut = message.isOptedOut);
        return obj;
    },
    create: function (base) {
        return exports.QueryIsOptedOutOfRewardsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryIsOptedOutOfRewardsResponse();
        message.isOptedOut = (_a = object.isOptedOut) !== null && _a !== void 0 ? _a : false;
        return message;
    },
};
function createBaseQueryOptedOutOfRewardsAccountsRequest() {
    return {};
}
exports.QueryOptedOutOfRewardsAccountsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOptedOutOfRewardsAccountsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryOptedOutOfRewardsAccountsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryOptedOutOfRewardsAccountsRequest();
        return message;
    },
};
function createBaseQueryOptedOutOfRewardsAccountsResponse() {
    return { accounts: [] };
}
exports.QueryOptedOutOfRewardsAccountsResponse = {
    encode: function (message, writer) {
        var e_39, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.accounts), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_39_1) { e_39 = { error: e_39_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_39) throw e_39.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryOptedOutOfRewardsAccountsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accounts.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { accounts: Array.isArray(object === null || object === void 0 ? void 0 : object.accounts) ? object.accounts.map(function (e) { return String(e); }) : [] };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.accounts) {
            obj.accounts = message.accounts.map(function (e) { return e; });
        }
        else {
            obj.accounts = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryOptedOutOfRewardsAccountsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryOptedOutOfRewardsAccountsResponse();
        message.accounts = ((_a = object.accounts) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryFeeDiscountAccountInfoRequest() {
    return { account: "" };
}
exports.QueryFeeDiscountAccountInfoRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountAccountInfoRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { account: isSet(object.account) ? String(object.account) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.account !== undefined && (obj.account = message.account);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountAccountInfoRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFeeDiscountAccountInfoRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryFeeDiscountAccountInfoResponse() {
    return { tierLevel: "0", accountInfo: undefined, accountTtl: undefined };
}
exports.QueryFeeDiscountAccountInfoResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tierLevel !== "0") {
            writer.uint32(8).uint64(message.tierLevel);
        }
        if (message.accountInfo !== undefined) {
            exchange_1.FeeDiscountTierInfo.encode(message.accountInfo, writer.uint32(18).fork()).ldelim();
        }
        if (message.accountTtl !== undefined) {
            exchange_1.FeeDiscountTierTTL.encode(message.accountTtl, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountAccountInfoResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tierLevel = longToString(reader.uint64());
                    break;
                case 2:
                    message.accountInfo = exchange_1.FeeDiscountTierInfo.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.accountTtl = exchange_1.FeeDiscountTierTTL.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tierLevel: isSet(object.tierLevel) ? String(object.tierLevel) : "0",
            accountInfo: isSet(object.accountInfo) ? exchange_1.FeeDiscountTierInfo.fromJSON(object.accountInfo) : undefined,
            accountTtl: isSet(object.accountTtl) ? exchange_1.FeeDiscountTierTTL.fromJSON(object.accountTtl) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tierLevel !== undefined && (obj.tierLevel = message.tierLevel);
        message.accountInfo !== undefined &&
            (obj.accountInfo = message.accountInfo ? exchange_1.FeeDiscountTierInfo.toJSON(message.accountInfo) : undefined);
        message.accountTtl !== undefined &&
            (obj.accountTtl = message.accountTtl ? exchange_1.FeeDiscountTierTTL.toJSON(message.accountTtl) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountAccountInfoResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFeeDiscountAccountInfoResponse();
        message.tierLevel = (_a = object.tierLevel) !== null && _a !== void 0 ? _a : "0";
        message.accountInfo = (object.accountInfo !== undefined && object.accountInfo !== null)
            ? exchange_1.FeeDiscountTierInfo.fromPartial(object.accountInfo)
            : undefined;
        message.accountTtl = (object.accountTtl !== undefined && object.accountTtl !== null)
            ? exchange_1.FeeDiscountTierTTL.fromPartial(object.accountTtl)
            : undefined;
        return message;
    },
};
function createBaseQueryFeeDiscountScheduleRequest() {
    return {};
}
exports.QueryFeeDiscountScheduleRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountScheduleRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountScheduleRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryFeeDiscountScheduleRequest();
        return message;
    },
};
function createBaseQueryFeeDiscountScheduleResponse() {
    return { feeDiscountSchedule: undefined };
}
exports.QueryFeeDiscountScheduleResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.feeDiscountSchedule !== undefined) {
            exchange_1.FeeDiscountSchedule.encode(message.feeDiscountSchedule, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountScheduleResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.feeDiscountSchedule = exchange_1.FeeDiscountSchedule.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            feeDiscountSchedule: isSet(object.feeDiscountSchedule)
                ? exchange_1.FeeDiscountSchedule.fromJSON(object.feeDiscountSchedule)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.feeDiscountSchedule !== undefined && (obj.feeDiscountSchedule = message.feeDiscountSchedule
            ? exchange_1.FeeDiscountSchedule.toJSON(message.feeDiscountSchedule)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountScheduleResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryFeeDiscountScheduleResponse();
        message.feeDiscountSchedule = (object.feeDiscountSchedule !== undefined && object.feeDiscountSchedule !== null)
            ? exchange_1.FeeDiscountSchedule.fromPartial(object.feeDiscountSchedule)
            : undefined;
        return message;
    },
};
function createBaseQueryBalanceMismatchesRequest() {
    return { dustFactor: "0" };
}
exports.QueryBalanceMismatchesRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.dustFactor !== "0") {
            writer.uint32(8).int64(message.dustFactor);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBalanceMismatchesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.dustFactor = longToString(reader.int64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { dustFactor: isSet(object.dustFactor) ? String(object.dustFactor) : "0" };
    },
    toJSON: function (message) {
        var obj = {};
        message.dustFactor !== undefined && (obj.dustFactor = message.dustFactor);
        return obj;
    },
    create: function (base) {
        return exports.QueryBalanceMismatchesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBalanceMismatchesRequest();
        message.dustFactor = (_a = object.dustFactor) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBaseBalanceMismatch() {
    return { subaccountId: "", denom: "", available: "", total: "", balanceHold: "", expectedTotal: "", difference: "" };
}
exports.BalanceMismatch = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.available !== "") {
            writer.uint32(26).string(message.available);
        }
        if (message.total !== "") {
            writer.uint32(34).string(message.total);
        }
        if (message.balanceHold !== "") {
            writer.uint32(42).string(message.balanceHold);
        }
        if (message.expectedTotal !== "") {
            writer.uint32(50).string(message.expectedTotal);
        }
        if (message.difference !== "") {
            writer.uint32(58).string(message.difference);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBalanceMismatch();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.available = reader.string();
                    break;
                case 4:
                    message.total = reader.string();
                    break;
                case 5:
                    message.balanceHold = reader.string();
                    break;
                case 6:
                    message.expectedTotal = reader.string();
                    break;
                case 7:
                    message.difference = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            available: isSet(object.available) ? String(object.available) : "",
            total: isSet(object.total) ? String(object.total) : "",
            balanceHold: isSet(object.balanceHold) ? String(object.balanceHold) : "",
            expectedTotal: isSet(object.expectedTotal) ? String(object.expectedTotal) : "",
            difference: isSet(object.difference) ? String(object.difference) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.denom !== undefined && (obj.denom = message.denom);
        message.available !== undefined && (obj.available = message.available);
        message.total !== undefined && (obj.total = message.total);
        message.balanceHold !== undefined && (obj.balanceHold = message.balanceHold);
        message.expectedTotal !== undefined && (obj.expectedTotal = message.expectedTotal);
        message.difference !== undefined && (obj.difference = message.difference);
        return obj;
    },
    create: function (base) {
        return exports.BalanceMismatch.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g;
        var message = createBaseBalanceMismatch();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.available = (_c = object.available) !== null && _c !== void 0 ? _c : "";
        message.total = (_d = object.total) !== null && _d !== void 0 ? _d : "";
        message.balanceHold = (_e = object.balanceHold) !== null && _e !== void 0 ? _e : "";
        message.expectedTotal = (_f = object.expectedTotal) !== null && _f !== void 0 ? _f : "";
        message.difference = (_g = object.difference) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBaseQueryBalanceMismatchesResponse() {
    return { balanceMismatches: [] };
}
exports.QueryBalanceMismatchesResponse = {
    encode: function (message, writer) {
        var e_40, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.balanceMismatches), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.BalanceMismatch.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_40_1) { e_40 = { error: e_40_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_40) throw e_40.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBalanceMismatchesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balanceMismatches.push(exports.BalanceMismatch.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            balanceMismatches: Array.isArray(object === null || object === void 0 ? void 0 : object.balanceMismatches)
                ? object.balanceMismatches.map(function (e) { return exports.BalanceMismatch.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.balanceMismatches) {
            obj.balanceMismatches = message.balanceMismatches.map(function (e) { return e ? exports.BalanceMismatch.toJSON(e) : undefined; });
        }
        else {
            obj.balanceMismatches = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBalanceMismatchesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBalanceMismatchesResponse();
        message.balanceMismatches = ((_a = object.balanceMismatches) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.BalanceMismatch.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryBalanceWithBalanceHoldsRequest() {
    return {};
}
exports.QueryBalanceWithBalanceHoldsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBalanceWithBalanceHoldsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryBalanceWithBalanceHoldsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryBalanceWithBalanceHoldsRequest();
        return message;
    },
};
function createBaseBalanceWithMarginHold() {
    return { subaccountId: "", denom: "", available: "", total: "", balanceHold: "" };
}
exports.BalanceWithMarginHold = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.denom !== "") {
            writer.uint32(18).string(message.denom);
        }
        if (message.available !== "") {
            writer.uint32(26).string(message.available);
        }
        if (message.total !== "") {
            writer.uint32(34).string(message.total);
        }
        if (message.balanceHold !== "") {
            writer.uint32(42).string(message.balanceHold);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseBalanceWithMarginHold();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.denom = reader.string();
                    break;
                case 3:
                    message.available = reader.string();
                    break;
                case 4:
                    message.total = reader.string();
                    break;
                case 5:
                    message.balanceHold = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            denom: isSet(object.denom) ? String(object.denom) : "",
            available: isSet(object.available) ? String(object.available) : "",
            total: isSet(object.total) ? String(object.total) : "",
            balanceHold: isSet(object.balanceHold) ? String(object.balanceHold) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.denom !== undefined && (obj.denom = message.denom);
        message.available !== undefined && (obj.available = message.available);
        message.total !== undefined && (obj.total = message.total);
        message.balanceHold !== undefined && (obj.balanceHold = message.balanceHold);
        return obj;
    },
    create: function (base) {
        return exports.BalanceWithMarginHold.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e;
        var message = createBaseBalanceWithMarginHold();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.denom = (_b = object.denom) !== null && _b !== void 0 ? _b : "";
        message.available = (_c = object.available) !== null && _c !== void 0 ? _c : "";
        message.total = (_d = object.total) !== null && _d !== void 0 ? _d : "";
        message.balanceHold = (_e = object.balanceHold) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBaseQueryBalanceWithBalanceHoldsResponse() {
    return { balanceWithBalanceHolds: [] };
}
exports.QueryBalanceWithBalanceHoldsResponse = {
    encode: function (message, writer) {
        var e_41, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.balanceWithBalanceHolds), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.BalanceWithMarginHold.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_41_1) { e_41 = { error: e_41_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_41) throw e_41.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBalanceWithBalanceHoldsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balanceWithBalanceHolds.push(exports.BalanceWithMarginHold.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            balanceWithBalanceHolds: Array.isArray(object === null || object === void 0 ? void 0 : object.balanceWithBalanceHolds)
                ? object.balanceWithBalanceHolds.map(function (e) { return exports.BalanceWithMarginHold.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.balanceWithBalanceHolds) {
            obj.balanceWithBalanceHolds = message.balanceWithBalanceHolds.map(function (e) {
                return e ? exports.BalanceWithMarginHold.toJSON(e) : undefined;
            });
        }
        else {
            obj.balanceWithBalanceHolds = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBalanceWithBalanceHoldsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBalanceWithBalanceHoldsResponse();
        message.balanceWithBalanceHolds =
            ((_a = object.balanceWithBalanceHolds) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.BalanceWithMarginHold.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryFeeDiscountTierStatisticsRequest() {
    return {};
}
exports.QueryFeeDiscountTierStatisticsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountTierStatisticsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountTierStatisticsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryFeeDiscountTierStatisticsRequest();
        return message;
    },
};
function createBaseTierStatistic() {
    return { tier: "0", count: "0" };
}
exports.TierStatistic = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tier !== "0") {
            writer.uint32(8).uint64(message.tier);
        }
        if (message.count !== "0") {
            writer.uint32(16).uint64(message.count);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTierStatistic();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tier = longToString(reader.uint64());
                    break;
                case 2:
                    message.count = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tier: isSet(object.tier) ? String(object.tier) : "0",
            count: isSet(object.count) ? String(object.count) : "0",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tier !== undefined && (obj.tier = message.tier);
        message.count !== undefined && (obj.count = message.count);
        return obj;
    },
    create: function (base) {
        return exports.TierStatistic.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseTierStatistic();
        message.tier = (_a = object.tier) !== null && _a !== void 0 ? _a : "0";
        message.count = (_b = object.count) !== null && _b !== void 0 ? _b : "0";
        return message;
    },
};
function createBaseQueryFeeDiscountTierStatisticsResponse() {
    return { statistics: [] };
}
exports.QueryFeeDiscountTierStatisticsResponse = {
    encode: function (message, writer) {
        var e_42, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.statistics), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TierStatistic.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_42_1) { e_42 = { error: e_42_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_42) throw e_42.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFeeDiscountTierStatisticsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.statistics.push(exports.TierStatistic.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            statistics: Array.isArray(object === null || object === void 0 ? void 0 : object.statistics) ? object.statistics.map(function (e) { return exports.TierStatistic.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.statistics) {
            obj.statistics = message.statistics.map(function (e) { return e ? exports.TierStatistic.toJSON(e) : undefined; });
        }
        else {
            obj.statistics = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryFeeDiscountTierStatisticsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFeeDiscountTierStatisticsResponse();
        message.statistics = ((_a = object.statistics) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TierStatistic.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMitoVaultInfosRequest() {
    return {};
}
exports.MitoVaultInfosRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMitoVaultInfosRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.MitoVaultInfosRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseMitoVaultInfosRequest();
        return message;
    },
};
function createBaseMitoVaultInfosResponse() {
    return { masterAddresses: [], derivativeAddresses: [], spotAddresses: [], cw20Addresses: [] };
}
exports.MitoVaultInfosResponse = {
    encode: function (message, writer) {
        var e_43, _a, e_44, _b, e_45, _c, e_46, _d;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _e = __values(message.masterAddresses), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                writer.uint32(10).string(v);
            }
        }
        catch (e_43_1) { e_43 = { error: e_43_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_a = _e.return)) _a.call(_e);
            }
            finally { if (e_43) throw e_43.error; }
        }
        try {
            for (var _g = __values(message.derivativeAddresses), _h = _g.next(); !_h.done; _h = _g.next()) {
                var v = _h.value;
                writer.uint32(18).string(v);
            }
        }
        catch (e_44_1) { e_44 = { error: e_44_1 }; }
        finally {
            try {
                if (_h && !_h.done && (_b = _g.return)) _b.call(_g);
            }
            finally { if (e_44) throw e_44.error; }
        }
        try {
            for (var _j = __values(message.spotAddresses), _k = _j.next(); !_k.done; _k = _j.next()) {
                var v = _k.value;
                writer.uint32(26).string(v);
            }
        }
        catch (e_45_1) { e_45 = { error: e_45_1 }; }
        finally {
            try {
                if (_k && !_k.done && (_c = _j.return)) _c.call(_j);
            }
            finally { if (e_45) throw e_45.error; }
        }
        try {
            for (var _l = __values(message.cw20Addresses), _m = _l.next(); !_m.done; _m = _l.next()) {
                var v = _m.value;
                writer.uint32(34).string(v);
            }
        }
        catch (e_46_1) { e_46 = { error: e_46_1 }; }
        finally {
            try {
                if (_m && !_m.done && (_d = _l.return)) _d.call(_l);
            }
            finally { if (e_46) throw e_46.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMitoVaultInfosResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.masterAddresses.push(reader.string());
                    break;
                case 2:
                    message.derivativeAddresses.push(reader.string());
                    break;
                case 3:
                    message.spotAddresses.push(reader.string());
                    break;
                case 4:
                    message.cw20Addresses.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            masterAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.masterAddresses) ? object.masterAddresses.map(function (e) { return String(e); }) : [],
            derivativeAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.derivativeAddresses)
                ? object.derivativeAddresses.map(function (e) { return String(e); })
                : [],
            spotAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.spotAddresses) ? object.spotAddresses.map(function (e) { return String(e); }) : [],
            cw20Addresses: Array.isArray(object === null || object === void 0 ? void 0 : object.cw20Addresses) ? object.cw20Addresses.map(function (e) { return String(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.masterAddresses) {
            obj.masterAddresses = message.masterAddresses.map(function (e) { return e; });
        }
        else {
            obj.masterAddresses = [];
        }
        if (message.derivativeAddresses) {
            obj.derivativeAddresses = message.derivativeAddresses.map(function (e) { return e; });
        }
        else {
            obj.derivativeAddresses = [];
        }
        if (message.spotAddresses) {
            obj.spotAddresses = message.spotAddresses.map(function (e) { return e; });
        }
        else {
            obj.spotAddresses = [];
        }
        if (message.cw20Addresses) {
            obj.cw20Addresses = message.cw20Addresses.map(function (e) { return e; });
        }
        else {
            obj.cw20Addresses = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.MitoVaultInfosResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseMitoVaultInfosResponse();
        message.masterAddresses = ((_a = object.masterAddresses) === null || _a === void 0 ? void 0 : _a.map(function (e) { return e; })) || [];
        message.derivativeAddresses = ((_b = object.derivativeAddresses) === null || _b === void 0 ? void 0 : _b.map(function (e) { return e; })) || [];
        message.spotAddresses = ((_c = object.spotAddresses) === null || _c === void 0 ? void 0 : _c.map(function (e) { return e; })) || [];
        message.cw20Addresses = ((_d = object.cw20Addresses) === null || _d === void 0 ? void 0 : _d.map(function (e) { return e; })) || [];
        return message;
    },
};
function createBaseQueryMarketIDFromVaultRequest() {
    return { vaultAddress: "" };
}
exports.QueryMarketIDFromVaultRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.vaultAddress !== "") {
            writer.uint32(10).string(message.vaultAddress);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketIDFromVaultRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.vaultAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { vaultAddress: isSet(object.vaultAddress) ? String(object.vaultAddress) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.vaultAddress !== undefined && (obj.vaultAddress = message.vaultAddress);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketIDFromVaultRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketIDFromVaultRequest();
        message.vaultAddress = (_a = object.vaultAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryMarketIDFromVaultResponse() {
    return { marketId: "" };
}
exports.QueryMarketIDFromVaultResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketIDFromVaultResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketIDFromVaultResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketIDFromVaultResponse();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryHistoricalTradeRecordsRequest() {
    return { marketId: "" };
}
exports.QueryHistoricalTradeRecordsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryHistoricalTradeRecordsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryHistoricalTradeRecordsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryHistoricalTradeRecordsRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryHistoricalTradeRecordsResponse() {
    return { tradeRecords: [] };
}
exports.QueryHistoricalTradeRecordsResponse = {
    encode: function (message, writer) {
        var e_47, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.tradeRecords), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.TradeRecords.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_47_1) { e_47 = { error: e_47_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_47) throw e_47.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryHistoricalTradeRecordsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tradeRecords.push(exchange_1.TradeRecords.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tradeRecords: Array.isArray(object === null || object === void 0 ? void 0 : object.tradeRecords)
                ? object.tradeRecords.map(function (e) { return exchange_1.TradeRecords.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.tradeRecords) {
            obj.tradeRecords = message.tradeRecords.map(function (e) { return e ? exchange_1.TradeRecords.toJSON(e) : undefined; });
        }
        else {
            obj.tradeRecords = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryHistoricalTradeRecordsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryHistoricalTradeRecordsResponse();
        message.tradeRecords = ((_a = object.tradeRecords) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.TradeRecords.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseTradeHistoryOptions() {
    return { tradeGroupingSec: "0", maxAge: "0", includeRawHistory: false, includeMetadata: false };
}
exports.TradeHistoryOptions = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.tradeGroupingSec !== "0") {
            writer.uint32(8).uint64(message.tradeGroupingSec);
        }
        if (message.maxAge !== "0") {
            writer.uint32(16).uint64(message.maxAge);
        }
        if (message.includeRawHistory === true) {
            writer.uint32(32).bool(message.includeRawHistory);
        }
        if (message.includeMetadata === true) {
            writer.uint32(40).bool(message.includeMetadata);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTradeHistoryOptions();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tradeGroupingSec = longToString(reader.uint64());
                    break;
                case 2:
                    message.maxAge = longToString(reader.uint64());
                    break;
                case 4:
                    message.includeRawHistory = reader.bool();
                    break;
                case 5:
                    message.includeMetadata = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            tradeGroupingSec: isSet(object.tradeGroupingSec) ? String(object.tradeGroupingSec) : "0",
            maxAge: isSet(object.maxAge) ? String(object.maxAge) : "0",
            includeRawHistory: isSet(object.includeRawHistory) ? Boolean(object.includeRawHistory) : false,
            includeMetadata: isSet(object.includeMetadata) ? Boolean(object.includeMetadata) : false,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.tradeGroupingSec !== undefined && (obj.tradeGroupingSec = message.tradeGroupingSec);
        message.maxAge !== undefined && (obj.maxAge = message.maxAge);
        message.includeRawHistory !== undefined && (obj.includeRawHistory = message.includeRawHistory);
        message.includeMetadata !== undefined && (obj.includeMetadata = message.includeMetadata);
        return obj;
    },
    create: function (base) {
        return exports.TradeHistoryOptions.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTradeHistoryOptions();
        message.tradeGroupingSec = (_a = object.tradeGroupingSec) !== null && _a !== void 0 ? _a : "0";
        message.maxAge = (_b = object.maxAge) !== null && _b !== void 0 ? _b : "0";
        message.includeRawHistory = (_c = object.includeRawHistory) !== null && _c !== void 0 ? _c : false;
        message.includeMetadata = (_d = object.includeMetadata) !== null && _d !== void 0 ? _d : false;
        return message;
    },
};
function createBaseQueryMarketVolatilityRequest() {
    return { marketId: "", tradeHistoryOptions: undefined };
}
exports.QueryMarketVolatilityRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.tradeHistoryOptions !== undefined) {
            exports.TradeHistoryOptions.encode(message.tradeHistoryOptions, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketVolatilityRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.tradeHistoryOptions = exports.TradeHistoryOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            tradeHistoryOptions: isSet(object.tradeHistoryOptions)
                ? exports.TradeHistoryOptions.fromJSON(object.tradeHistoryOptions)
                : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.tradeHistoryOptions !== undefined && (obj.tradeHistoryOptions = message.tradeHistoryOptions
            ? exports.TradeHistoryOptions.toJSON(message.tradeHistoryOptions)
            : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketVolatilityRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketVolatilityRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.tradeHistoryOptions = (object.tradeHistoryOptions !== undefined && object.tradeHistoryOptions !== null)
            ? exports.TradeHistoryOptions.fromPartial(object.tradeHistoryOptions)
            : undefined;
        return message;
    },
};
function createBaseQueryMarketVolatilityResponse() {
    return { volatility: "", historyMetadata: undefined, rawHistory: [] };
}
exports.QueryMarketVolatilityResponse = {
    encode: function (message, writer) {
        var e_48, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.volatility !== "") {
            writer.uint32(10).string(message.volatility);
        }
        if (message.historyMetadata !== undefined) {
            oracle_1.MetadataStatistics.encode(message.historyMetadata, writer.uint32(18).fork()).ldelim();
        }
        try {
            for (var _b = __values(message.rawHistory), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.TradeRecord.encode(v, writer.uint32(26).fork()).ldelim();
            }
        }
        catch (e_48_1) { e_48 = { error: e_48_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_48) throw e_48.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketVolatilityResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.volatility = reader.string();
                    break;
                case 2:
                    message.historyMetadata = oracle_1.MetadataStatistics.decode(reader, reader.uint32());
                    break;
                case 3:
                    message.rawHistory.push(exchange_1.TradeRecord.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            volatility: isSet(object.volatility) ? String(object.volatility) : "",
            historyMetadata: isSet(object.historyMetadata) ? oracle_1.MetadataStatistics.fromJSON(object.historyMetadata) : undefined,
            rawHistory: Array.isArray(object === null || object === void 0 ? void 0 : object.rawHistory) ? object.rawHistory.map(function (e) { return exchange_1.TradeRecord.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.volatility !== undefined && (obj.volatility = message.volatility);
        message.historyMetadata !== undefined &&
            (obj.historyMetadata = message.historyMetadata ? oracle_1.MetadataStatistics.toJSON(message.historyMetadata) : undefined);
        if (message.rawHistory) {
            obj.rawHistory = message.rawHistory.map(function (e) { return e ? exchange_1.TradeRecord.toJSON(e) : undefined; });
        }
        else {
            obj.rawHistory = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketVolatilityResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryMarketVolatilityResponse();
        message.volatility = (_a = object.volatility) !== null && _a !== void 0 ? _a : "";
        message.historyMetadata = (object.historyMetadata !== undefined && object.historyMetadata !== null)
            ? oracle_1.MetadataStatistics.fromPartial(object.historyMetadata)
            : undefined;
        message.rawHistory = ((_b = object.rawHistory) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.TradeRecord.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryBinaryMarketsRequest() {
    return { status: "" };
}
exports.QueryBinaryMarketsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.status !== "") {
            writer.uint32(10).string(message.status);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBinaryMarketsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.status = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { status: isSet(object.status) ? String(object.status) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.status !== undefined && (obj.status = message.status);
        return obj;
    },
    create: function (base) {
        return exports.QueryBinaryMarketsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBinaryMarketsRequest();
        message.status = (_a = object.status) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryBinaryMarketsResponse() {
    return { markets: [] };
}
exports.QueryBinaryMarketsResponse = {
    encode: function (message, writer) {
        var e_49, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.markets), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.BinaryOptionsMarket.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_49_1) { e_49 = { error: e_49_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_49) throw e_49.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryBinaryMarketsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.markets.push(exchange_1.BinaryOptionsMarket.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            markets: Array.isArray(object === null || object === void 0 ? void 0 : object.markets) ? object.markets.map(function (e) { return exchange_1.BinaryOptionsMarket.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.markets) {
            obj.markets = message.markets.map(function (e) { return e ? exchange_1.BinaryOptionsMarket.toJSON(e) : undefined; });
        }
        else {
            obj.markets = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryBinaryMarketsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryBinaryMarketsResponse();
        message.markets = ((_a = object.markets) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.BinaryOptionsMarket.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryTraderDerivativeConditionalOrdersRequest() {
    return { subaccountId: "", marketId: "" };
}
exports.QueryTraderDerivativeConditionalOrdersRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.subaccountId !== "") {
            writer.uint32(10).string(message.subaccountId);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderDerivativeConditionalOrdersRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.subaccountId = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderDerivativeConditionalOrdersRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryTraderDerivativeConditionalOrdersRequest();
        message.subaccountId = (_a = object.subaccountId) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseTrimmedDerivativeConditionalOrder() {
    return {
        price: "",
        quantity: "",
        margin: "",
        triggerPrice: "",
        isBuy: false,
        isLimit: false,
        orderHash: "",
        cid: "",
    };
}
exports.TrimmedDerivativeConditionalOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.margin !== "") {
            writer.uint32(26).string(message.margin);
        }
        if (message.triggerPrice !== "") {
            writer.uint32(34).string(message.triggerPrice);
        }
        if (message.isBuy === true) {
            writer.uint32(40).bool(message.isBuy);
        }
        if (message.isLimit === true) {
            writer.uint32(48).bool(message.isLimit);
        }
        if (message.orderHash !== "") {
            writer.uint32(58).string(message.orderHash);
        }
        if (message.cid !== "") {
            writer.uint32(66).string(message.cid);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTrimmedDerivativeConditionalOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.margin = reader.string();
                    break;
                case 4:
                    message.triggerPrice = reader.string();
                    break;
                case 5:
                    message.isBuy = reader.bool();
                    break;
                case 6:
                    message.isLimit = reader.bool();
                    break;
                case 7:
                    message.orderHash = reader.string();
                    break;
                case 8:
                    message.cid = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            margin: isSet(object.margin) ? String(object.margin) : "",
            triggerPrice: isSet(object.triggerPrice) ? String(object.triggerPrice) : "",
            isBuy: isSet(object.isBuy) ? Boolean(object.isBuy) : false,
            isLimit: isSet(object.isLimit) ? Boolean(object.isLimit) : false,
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            cid: isSet(object.cid) ? String(object.cid) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.margin !== undefined && (obj.margin = message.margin);
        message.triggerPrice !== undefined && (obj.triggerPrice = message.triggerPrice);
        message.isBuy !== undefined && (obj.isBuy = message.isBuy);
        message.isLimit !== undefined && (obj.isLimit = message.isLimit);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.cid !== undefined && (obj.cid = message.cid);
        return obj;
    },
    create: function (base) {
        return exports.TrimmedDerivativeConditionalOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        var message = createBaseTrimmedDerivativeConditionalOrder();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.margin = (_c = object.margin) !== null && _c !== void 0 ? _c : "";
        message.triggerPrice = (_d = object.triggerPrice) !== null && _d !== void 0 ? _d : "";
        message.isBuy = (_e = object.isBuy) !== null && _e !== void 0 ? _e : false;
        message.isLimit = (_f = object.isLimit) !== null && _f !== void 0 ? _f : false;
        message.orderHash = (_g = object.orderHash) !== null && _g !== void 0 ? _g : "";
        message.cid = (_h = object.cid) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBaseQueryTraderDerivativeConditionalOrdersResponse() {
    return { orders: [] };
}
exports.QueryTraderDerivativeConditionalOrdersResponse = {
    encode: function (message, writer) {
        var e_50, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.orders), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.TrimmedDerivativeConditionalOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_50_1) { e_50 = { error: e_50_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_50) throw e_50.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryTraderDerivativeConditionalOrdersResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.orders.push(exports.TrimmedDerivativeConditionalOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            orders: Array.isArray(object === null || object === void 0 ? void 0 : object.orders)
                ? object.orders.map(function (e) { return exports.TrimmedDerivativeConditionalOrder.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.orders) {
            obj.orders = message.orders.map(function (e) { return e ? exports.TrimmedDerivativeConditionalOrder.toJSON(e) : undefined; });
        }
        else {
            obj.orders = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryTraderDerivativeConditionalOrdersResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryTraderDerivativeConditionalOrdersResponse();
        message.orders = ((_a = object.orders) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedDerivativeConditionalOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryFullSpotOrderbookRequest() {
    return { marketId: "" };
}
exports.QueryFullSpotOrderbookRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotOrderbookRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotOrderbookRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFullSpotOrderbookRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryFullSpotOrderbookResponse() {
    return { Bids: [], Asks: [] };
}
exports.QueryFullSpotOrderbookResponse = {
    encode: function (message, writer) {
        var e_51, _a, e_52, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.Bids), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.TrimmedLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_51_1) { e_51 = { error: e_51_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_51) throw e_51.error; }
        }
        try {
            for (var _e = __values(message.Asks), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.TrimmedLimitOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_52_1) { e_52 = { error: e_52_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_52) throw e_52.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullSpotOrderbookResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.Bids.push(exports.TrimmedLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.Asks.push(exports.TrimmedLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            Bids: Array.isArray(object === null || object === void 0 ? void 0 : object.Bids) ? object.Bids.map(function (e) { return exports.TrimmedLimitOrder.fromJSON(e); }) : [],
            Asks: Array.isArray(object === null || object === void 0 ? void 0 : object.Asks) ? object.Asks.map(function (e) { return exports.TrimmedLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.Bids) {
            obj.Bids = message.Bids.map(function (e) { return e ? exports.TrimmedLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.Bids = [];
        }
        if (message.Asks) {
            obj.Asks = message.Asks.map(function (e) { return e ? exports.TrimmedLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.Asks = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryFullSpotOrderbookResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryFullSpotOrderbookResponse();
        message.Bids = ((_a = object.Bids) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedLimitOrder.fromPartial(e); })) || [];
        message.Asks = ((_b = object.Asks) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.TrimmedLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryFullDerivativeOrderbookRequest() {
    return { marketId: "" };
}
exports.QueryFullDerivativeOrderbookRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullDerivativeOrderbookRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryFullDerivativeOrderbookRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryFullDerivativeOrderbookRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryFullDerivativeOrderbookResponse() {
    return { Bids: [], Asks: [] };
}
exports.QueryFullDerivativeOrderbookResponse = {
    encode: function (message, writer) {
        var e_53, _a, e_54, _b;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _c = __values(message.Bids), _d = _c.next(); !_d.done; _d = _c.next()) {
                var v = _d.value;
                exports.TrimmedLimitOrder.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_53_1) { e_53 = { error: e_53_1 }; }
        finally {
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            }
            finally { if (e_53) throw e_53.error; }
        }
        try {
            for (var _e = __values(message.Asks), _f = _e.next(); !_f.done; _f = _e.next()) {
                var v = _f.value;
                exports.TrimmedLimitOrder.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_54_1) { e_54 = { error: e_54_1 }; }
        finally {
            try {
                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
            }
            finally { if (e_54) throw e_54.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryFullDerivativeOrderbookResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.Bids.push(exports.TrimmedLimitOrder.decode(reader, reader.uint32()));
                    break;
                case 2:
                    message.Asks.push(exports.TrimmedLimitOrder.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            Bids: Array.isArray(object === null || object === void 0 ? void 0 : object.Bids) ? object.Bids.map(function (e) { return exports.TrimmedLimitOrder.fromJSON(e); }) : [],
            Asks: Array.isArray(object === null || object === void 0 ? void 0 : object.Asks) ? object.Asks.map(function (e) { return exports.TrimmedLimitOrder.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.Bids) {
            obj.Bids = message.Bids.map(function (e) { return e ? exports.TrimmedLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.Bids = [];
        }
        if (message.Asks) {
            obj.Asks = message.Asks.map(function (e) { return e ? exports.TrimmedLimitOrder.toJSON(e) : undefined; });
        }
        else {
            obj.Asks = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryFullDerivativeOrderbookResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryFullDerivativeOrderbookResponse();
        message.Bids = ((_a = object.Bids) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.TrimmedLimitOrder.fromPartial(e); })) || [];
        message.Asks = ((_b = object.Asks) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exports.TrimmedLimitOrder.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseTrimmedLimitOrder() {
    return { price: "", quantity: "", orderHash: "", subaccountId: "" };
}
exports.TrimmedLimitOrder = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.price !== "") {
            writer.uint32(10).string(message.price);
        }
        if (message.quantity !== "") {
            writer.uint32(18).string(message.quantity);
        }
        if (message.orderHash !== "") {
            writer.uint32(26).string(message.orderHash);
        }
        if (message.subaccountId !== "") {
            writer.uint32(34).string(message.subaccountId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseTrimmedLimitOrder();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.price = reader.string();
                    break;
                case 2:
                    message.quantity = reader.string();
                    break;
                case 3:
                    message.orderHash = reader.string();
                    break;
                case 4:
                    message.subaccountId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            price: isSet(object.price) ? String(object.price) : "",
            quantity: isSet(object.quantity) ? String(object.quantity) : "",
            orderHash: isSet(object.orderHash) ? String(object.orderHash) : "",
            subaccountId: isSet(object.subaccountId) ? String(object.subaccountId) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.price !== undefined && (obj.price = message.price);
        message.quantity !== undefined && (obj.quantity = message.quantity);
        message.orderHash !== undefined && (obj.orderHash = message.orderHash);
        message.subaccountId !== undefined && (obj.subaccountId = message.subaccountId);
        return obj;
    },
    create: function (base) {
        return exports.TrimmedLimitOrder.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b, _c, _d;
        var message = createBaseTrimmedLimitOrder();
        message.price = (_a = object.price) !== null && _a !== void 0 ? _a : "";
        message.quantity = (_b = object.quantity) !== null && _b !== void 0 ? _b : "";
        message.orderHash = (_c = object.orderHash) !== null && _c !== void 0 ? _c : "";
        message.subaccountId = (_d = object.subaccountId) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseQueryMarketAtomicExecutionFeeMultiplierRequest() {
    return { marketId: "" };
}
exports.QueryMarketAtomicExecutionFeeMultiplierRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketAtomicExecutionFeeMultiplierRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketAtomicExecutionFeeMultiplierRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketAtomicExecutionFeeMultiplierRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryMarketAtomicExecutionFeeMultiplierResponse() {
    return { multiplier: "" };
}
exports.QueryMarketAtomicExecutionFeeMultiplierResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.multiplier !== "") {
            writer.uint32(10).string(message.multiplier);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketAtomicExecutionFeeMultiplierResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.multiplier = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { multiplier: isSet(object.multiplier) ? String(object.multiplier) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketAtomicExecutionFeeMultiplierResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketAtomicExecutionFeeMultiplierResponse();
        message.multiplier = (_a = object.multiplier) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryActiveStakeGrantRequest() {
    return { grantee: "" };
}
exports.QueryActiveStakeGrantRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grantee !== "") {
            writer.uint32(10).string(message.grantee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryActiveStakeGrantRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grantee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { grantee: isSet(object.grantee) ? String(object.grantee) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.grantee !== undefined && (obj.grantee = message.grantee);
        return obj;
    },
    create: function (base) {
        return exports.QueryActiveStakeGrantRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryActiveStakeGrantRequest();
        message.grantee = (_a = object.grantee) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryActiveStakeGrantResponse() {
    return { grant: undefined, effectiveGrant: undefined };
}
exports.QueryActiveStakeGrantResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.grant !== undefined) {
            exchange_1.ActiveGrant.encode(message.grant, writer.uint32(10).fork()).ldelim();
        }
        if (message.effectiveGrant !== undefined) {
            exchange_1.EffectiveGrant.encode(message.effectiveGrant, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryActiveStakeGrantResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.grant = exchange_1.ActiveGrant.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.effectiveGrant = exchange_1.EffectiveGrant.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            grant: isSet(object.grant) ? exchange_1.ActiveGrant.fromJSON(object.grant) : undefined,
            effectiveGrant: isSet(object.effectiveGrant) ? exchange_1.EffectiveGrant.fromJSON(object.effectiveGrant) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.grant !== undefined && (obj.grant = message.grant ? exchange_1.ActiveGrant.toJSON(message.grant) : undefined);
        message.effectiveGrant !== undefined &&
            (obj.effectiveGrant = message.effectiveGrant ? exchange_1.EffectiveGrant.toJSON(message.effectiveGrant) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.QueryActiveStakeGrantResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseQueryActiveStakeGrantResponse();
        message.grant = (object.grant !== undefined && object.grant !== null)
            ? exchange_1.ActiveGrant.fromPartial(object.grant)
            : undefined;
        message.effectiveGrant = (object.effectiveGrant !== undefined && object.effectiveGrant !== null)
            ? exchange_1.EffectiveGrant.fromPartial(object.effectiveGrant)
            : undefined;
        return message;
    },
};
function createBaseQueryGrantAuthorizationRequest() {
    return { granter: "", grantee: "" };
}
exports.QueryGrantAuthorizationRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        if (message.grantee !== "") {
            writer.uint32(18).string(message.grantee);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryGrantAuthorizationRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                case 2:
                    message.grantee = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            granter: isSet(object.granter) ? String(object.granter) : "",
            grantee: isSet(object.grantee) ? String(object.grantee) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        message.grantee !== undefined && (obj.grantee = message.grantee);
        return obj;
    },
    create: function (base) {
        return exports.QueryGrantAuthorizationRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryGrantAuthorizationRequest();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        message.grantee = (_b = object.grantee) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryGrantAuthorizationResponse() {
    return { amount: "" };
}
exports.QueryGrantAuthorizationResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.amount !== "") {
            writer.uint32(10).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryGrantAuthorizationResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { amount: isSet(object.amount) ? String(object.amount) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.QueryGrantAuthorizationResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryGrantAuthorizationResponse();
        message.amount = (_a = object.amount) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryGrantAuthorizationsRequest() {
    return { granter: "" };
}
exports.QueryGrantAuthorizationsRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.granter !== "") {
            writer.uint32(10).string(message.granter);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryGrantAuthorizationsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.granter = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { granter: isSet(object.granter) ? String(object.granter) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.granter !== undefined && (obj.granter = message.granter);
        return obj;
    },
    create: function (base) {
        return exports.QueryGrantAuthorizationsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryGrantAuthorizationsRequest();
        message.granter = (_a = object.granter) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryGrantAuthorizationsResponse() {
    return { totalGrantAmount: "", grants: [] };
}
exports.QueryGrantAuthorizationsResponse = {
    encode: function (message, writer) {
        var e_55, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.totalGrantAmount !== "") {
            writer.uint32(10).string(message.totalGrantAmount);
        }
        try {
            for (var _b = __values(message.grants), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.GrantAuthorization.encode(v, writer.uint32(18).fork()).ldelim();
            }
        }
        catch (e_55_1) { e_55 = { error: e_55_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_55) throw e_55.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryGrantAuthorizationsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.totalGrantAmount = reader.string();
                    break;
                case 2:
                    message.grants.push(exchange_1.GrantAuthorization.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            totalGrantAmount: isSet(object.totalGrantAmount) ? String(object.totalGrantAmount) : "",
            grants: Array.isArray(object === null || object === void 0 ? void 0 : object.grants) ? object.grants.map(function (e) { return exchange_1.GrantAuthorization.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.totalGrantAmount !== undefined && (obj.totalGrantAmount = message.totalGrantAmount);
        if (message.grants) {
            obj.grants = message.grants.map(function (e) { return e ? exchange_1.GrantAuthorization.toJSON(e) : undefined; });
        }
        else {
            obj.grants = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryGrantAuthorizationsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseQueryGrantAuthorizationsResponse();
        message.totalGrantAmount = (_a = object.totalGrantAmount) !== null && _a !== void 0 ? _a : "";
        message.grants = ((_b = object.grants) === null || _b === void 0 ? void 0 : _b.map(function (e) { return exchange_1.GrantAuthorization.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseQueryMarketBalanceRequest() {
    return { marketId: "" };
}
exports.QueryMarketBalanceRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketBalanceRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { marketId: isSet(object.marketId) ? String(object.marketId) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketBalanceRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketBalanceRequest();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryMarketBalanceResponse() {
    return { balance: "" };
}
exports.QueryMarketBalanceResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.balance !== "") {
            writer.uint32(10).string(message.balance);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketBalanceResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balance = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { balance: isSet(object.balance) ? String(object.balance) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.balance !== undefined && (obj.balance = message.balance);
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketBalanceResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketBalanceResponse();
        message.balance = (_a = object.balance) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryMarketBalancesRequest() {
    return {};
}
exports.QueryMarketBalancesRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketBalancesRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketBalancesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryMarketBalancesRequest();
        return message;
    },
};
function createBaseQueryMarketBalancesResponse() {
    return { balances: [] };
}
exports.QueryMarketBalancesResponse = {
    encode: function (message, writer) {
        var e_56, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.balances), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exports.MarketBalance.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_56_1) { e_56 = { error: e_56_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_56) throw e_56.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryMarketBalancesResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.balances.push(exports.MarketBalance.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            balances: Array.isArray(object === null || object === void 0 ? void 0 : object.balances) ? object.balances.map(function (e) { return exports.MarketBalance.fromJSON(e); }) : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.balances) {
            obj.balances = message.balances.map(function (e) { return e ? exports.MarketBalance.toJSON(e) : undefined; });
        }
        else {
            obj.balances = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryMarketBalancesResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryMarketBalancesResponse();
        message.balances = ((_a = object.balances) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exports.MarketBalance.fromPartial(e); })) || [];
        return message;
    },
};
function createBaseMarketBalance() {
    return { marketId: "", balance: "" };
}
exports.MarketBalance = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.balance !== "") {
            writer.uint32(18).string(message.balance);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseMarketBalance();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.balance = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            balance: isSet(object.balance) ? String(object.balance) : "",
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.balance !== undefined && (obj.balance = message.balance);
        return obj;
    },
    create: function (base) {
        return exports.MarketBalance.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseMarketBalance();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.balance = (_b = object.balance) !== null && _b !== void 0 ? _b : "";
        return message;
    },
};
function createBaseQueryDenomMinNotionalRequest() {
    return { denom: "" };
}
exports.QueryDenomMinNotionalRequest = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.denom !== "") {
            writer.uint32(10).string(message.denom);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomMinNotionalRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denom = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { denom: isSet(object.denom) ? String(object.denom) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.denom !== undefined && (obj.denom = message.denom);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomMinNotionalRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomMinNotionalRequest();
        message.denom = (_a = object.denom) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDenomMinNotionalResponse() {
    return { amount: "" };
}
exports.QueryDenomMinNotionalResponse = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.amount !== "") {
            writer.uint32(10).string(message.amount);
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomMinNotionalResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.amount = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { amount: isSet(object.amount) ? String(object.amount) : "" };
    },
    toJSON: function (message) {
        var obj = {};
        message.amount !== undefined && (obj.amount = message.amount);
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomMinNotionalResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomMinNotionalResponse();
        message.amount = (_a = object.amount) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBaseQueryDenomMinNotionalsRequest() {
    return {};
}
exports.QueryDenomMinNotionalsRequest = {
    encode: function (_, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomMinNotionalsRequest();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (_) {
        return {};
    },
    toJSON: function (_) {
        var obj = {};
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomMinNotionalsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (_) {
        var message = createBaseQueryDenomMinNotionalsRequest();
        return message;
    },
};
function createBaseQueryDenomMinNotionalsResponse() {
    return { denomMinNotionals: [] };
}
exports.QueryDenomMinNotionalsResponse = {
    encode: function (message, writer) {
        var e_57, _a;
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        try {
            for (var _b = __values(message.denomMinNotionals), _c = _b.next(); !_c.done; _c = _b.next()) {
                var v = _c.value;
                exchange_1.DenomMinNotional.encode(v, writer.uint32(10).fork()).ldelim();
            }
        }
        catch (e_57_1) { e_57 = { error: e_57_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_57) throw e_57.error; }
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseQueryDenomMinNotionalsResponse();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.denomMinNotionals.push(exchange_1.DenomMinNotional.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            denomMinNotionals: Array.isArray(object === null || object === void 0 ? void 0 : object.denomMinNotionals)
                ? object.denomMinNotionals.map(function (e) { return exchange_1.DenomMinNotional.fromJSON(e); })
                : [],
        };
    },
    toJSON: function (message) {
        var obj = {};
        if (message.denomMinNotionals) {
            obj.denomMinNotionals = message.denomMinNotionals.map(function (e) { return e ? exchange_1.DenomMinNotional.toJSON(e) : undefined; });
        }
        else {
            obj.denomMinNotionals = [];
        }
        return obj;
    },
    create: function (base) {
        return exports.QueryDenomMinNotionalsResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a;
        var message = createBaseQueryDenomMinNotionalsResponse();
        message.denomMinNotionals = ((_a = object.denomMinNotionals) === null || _a === void 0 ? void 0 : _a.map(function (e) { return exchange_1.DenomMinNotional.fromPartial(e); })) || [];
        return message;
    },
};
var QueryClientImpl = /** @class */ (function () {
    function QueryClientImpl(rpc) {
        this.rpc = rpc;
        this.L3DerivativeOrderBook = this.L3DerivativeOrderBook.bind(this);
        this.L3SpotOrderBook = this.L3SpotOrderBook.bind(this);
        this.QueryExchangeParams = this.QueryExchangeParams.bind(this);
        this.SubaccountDeposits = this.SubaccountDeposits.bind(this);
        this.SubaccountDeposit = this.SubaccountDeposit.bind(this);
        this.ExchangeBalances = this.ExchangeBalances.bind(this);
        this.AggregateVolume = this.AggregateVolume.bind(this);
        this.AggregateVolumes = this.AggregateVolumes.bind(this);
        this.AggregateMarketVolume = this.AggregateMarketVolume.bind(this);
        this.AggregateMarketVolumes = this.AggregateMarketVolumes.bind(this);
        this.DenomDecimal = this.DenomDecimal.bind(this);
        this.DenomDecimals = this.DenomDecimals.bind(this);
        this.SpotMarkets = this.SpotMarkets.bind(this);
        this.SpotMarket = this.SpotMarket.bind(this);
        this.FullSpotMarkets = this.FullSpotMarkets.bind(this);
        this.FullSpotMarket = this.FullSpotMarket.bind(this);
        this.SpotOrderbook = this.SpotOrderbook.bind(this);
        this.TraderSpotOrders = this.TraderSpotOrders.bind(this);
        this.AccountAddressSpotOrders = this.AccountAddressSpotOrders.bind(this);
        this.SpotOrdersByHashes = this.SpotOrdersByHashes.bind(this);
        this.SubaccountOrders = this.SubaccountOrders.bind(this);
        this.TraderSpotTransientOrders = this.TraderSpotTransientOrders.bind(this);
        this.SpotMidPriceAndTOB = this.SpotMidPriceAndTOB.bind(this);
        this.DerivativeMidPriceAndTOB = this.DerivativeMidPriceAndTOB.bind(this);
        this.DerivativeOrderbook = this.DerivativeOrderbook.bind(this);
        this.TraderDerivativeOrders = this.TraderDerivativeOrders.bind(this);
        this.AccountAddressDerivativeOrders = this.AccountAddressDerivativeOrders.bind(this);
        this.DerivativeOrdersByHashes = this.DerivativeOrdersByHashes.bind(this);
        this.TraderDerivativeTransientOrders = this.TraderDerivativeTransientOrders.bind(this);
        this.DerivativeMarkets = this.DerivativeMarkets.bind(this);
        this.DerivativeMarket = this.DerivativeMarket.bind(this);
        this.DerivativeMarketAddress = this.DerivativeMarketAddress.bind(this);
        this.SubaccountTradeNonce = this.SubaccountTradeNonce.bind(this);
        this.ExchangeModuleState = this.ExchangeModuleState.bind(this);
        this.Positions = this.Positions.bind(this);
        this.SubaccountPositions = this.SubaccountPositions.bind(this);
        this.SubaccountPositionInMarket = this.SubaccountPositionInMarket.bind(this);
        this.SubaccountEffectivePositionInMarket = this.SubaccountEffectivePositionInMarket.bind(this);
        this.PerpetualMarketInfo = this.PerpetualMarketInfo.bind(this);
        this.ExpiryFuturesMarketInfo = this.ExpiryFuturesMarketInfo.bind(this);
        this.PerpetualMarketFunding = this.PerpetualMarketFunding.bind(this);
        this.SubaccountOrderMetadata = this.SubaccountOrderMetadata.bind(this);
        this.TradeRewardPoints = this.TradeRewardPoints.bind(this);
        this.PendingTradeRewardPoints = this.PendingTradeRewardPoints.bind(this);
        this.TradeRewardCampaign = this.TradeRewardCampaign.bind(this);
        this.FeeDiscountAccountInfo = this.FeeDiscountAccountInfo.bind(this);
        this.FeeDiscountSchedule = this.FeeDiscountSchedule.bind(this);
        this.BalanceMismatches = this.BalanceMismatches.bind(this);
        this.BalanceWithBalanceHolds = this.BalanceWithBalanceHolds.bind(this);
        this.FeeDiscountTierStatistics = this.FeeDiscountTierStatistics.bind(this);
        this.MitoVaultInfos = this.MitoVaultInfos.bind(this);
        this.QueryMarketIDFromVault = this.QueryMarketIDFromVault.bind(this);
        this.HistoricalTradeRecords = this.HistoricalTradeRecords.bind(this);
        this.IsOptedOutOfRewards = this.IsOptedOutOfRewards.bind(this);
        this.OptedOutOfRewardsAccounts = this.OptedOutOfRewardsAccounts.bind(this);
        this.MarketVolatility = this.MarketVolatility.bind(this);
        this.BinaryOptionsMarkets = this.BinaryOptionsMarkets.bind(this);
        this.TraderDerivativeConditionalOrders = this.TraderDerivativeConditionalOrders.bind(this);
        this.MarketAtomicExecutionFeeMultiplier = this.MarketAtomicExecutionFeeMultiplier.bind(this);
        this.ActiveStakeGrant = this.ActiveStakeGrant.bind(this);
        this.GrantAuthorization = this.GrantAuthorization.bind(this);
        this.GrantAuthorizations = this.GrantAuthorizations.bind(this);
        this.MarketBalance = this.MarketBalance.bind(this);
        this.MarketBalances = this.MarketBalances.bind(this);
        this.DenomMinNotional = this.DenomMinNotional.bind(this);
        this.DenomMinNotionals = this.DenomMinNotionals.bind(this);
    }
    QueryClientImpl.prototype.L3DerivativeOrderBook = function (request, metadata) {
        return this.rpc.unary(exports.QueryL3DerivativeOrderBookDesc, exports.QueryFullDerivativeOrderbookRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.L3SpotOrderBook = function (request, metadata) {
        return this.rpc.unary(exports.QueryL3SpotOrderBookDesc, exports.QueryFullSpotOrderbookRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.QueryExchangeParams = function (request, metadata) {
        return this.rpc.unary(exports.QueryQueryExchangeParamsDesc, exports.QueryExchangeParamsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountDeposits = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountDepositsDesc, exports.QuerySubaccountDepositsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountDeposit = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountDepositDesc, exports.QuerySubaccountDepositRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ExchangeBalances = function (request, metadata) {
        return this.rpc.unary(exports.QueryExchangeBalancesDesc, exports.QueryExchangeBalancesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AggregateVolume = function (request, metadata) {
        return this.rpc.unary(exports.QueryAggregateVolumeDesc, exports.QueryAggregateVolumeRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AggregateVolumes = function (request, metadata) {
        return this.rpc.unary(exports.QueryAggregateVolumesDesc, exports.QueryAggregateVolumesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AggregateMarketVolume = function (request, metadata) {
        return this.rpc.unary(exports.QueryAggregateMarketVolumeDesc, exports.QueryAggregateMarketVolumeRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AggregateMarketVolumes = function (request, metadata) {
        return this.rpc.unary(exports.QueryAggregateMarketVolumesDesc, exports.QueryAggregateMarketVolumesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DenomDecimal = function (request, metadata) {
        return this.rpc.unary(exports.QueryDenomDecimalDesc, exports.QueryDenomDecimalRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DenomDecimals = function (request, metadata) {
        return this.rpc.unary(exports.QueryDenomDecimalsDesc, exports.QueryDenomDecimalsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SpotMarkets = function (request, metadata) {
        return this.rpc.unary(exports.QuerySpotMarketsDesc, exports.QuerySpotMarketsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SpotMarket = function (request, metadata) {
        return this.rpc.unary(exports.QuerySpotMarketDesc, exports.QuerySpotMarketRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FullSpotMarkets = function (request, metadata) {
        return this.rpc.unary(exports.QueryFullSpotMarketsDesc, exports.QueryFullSpotMarketsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FullSpotMarket = function (request, metadata) {
        return this.rpc.unary(exports.QueryFullSpotMarketDesc, exports.QueryFullSpotMarketRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SpotOrderbook = function (request, metadata) {
        return this.rpc.unary(exports.QuerySpotOrderbookDesc, exports.QuerySpotOrderbookRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TraderSpotOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryTraderSpotOrdersDesc, exports.QueryTraderSpotOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AccountAddressSpotOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryAccountAddressSpotOrdersDesc, exports.QueryAccountAddressSpotOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SpotOrdersByHashes = function (request, metadata) {
        return this.rpc.unary(exports.QuerySpotOrdersByHashesDesc, exports.QuerySpotOrdersByHashesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountOrders = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountOrdersDesc, exports.QuerySubaccountOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TraderSpotTransientOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryTraderSpotTransientOrdersDesc, exports.QueryTraderSpotOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SpotMidPriceAndTOB = function (request, metadata) {
        return this.rpc.unary(exports.QuerySpotMidPriceAndTOBDesc, exports.QuerySpotMidPriceAndTOBRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeMidPriceAndTOB = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeMidPriceAndTOBDesc, exports.QueryDerivativeMidPriceAndTOBRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeOrderbook = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeOrderbookDesc, exports.QueryDerivativeOrderbookRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TraderDerivativeOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryTraderDerivativeOrdersDesc, exports.QueryTraderDerivativeOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.AccountAddressDerivativeOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryAccountAddressDerivativeOrdersDesc, exports.QueryAccountAddressDerivativeOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeOrdersByHashes = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeOrdersByHashesDesc, exports.QueryDerivativeOrdersByHashesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TraderDerivativeTransientOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryTraderDerivativeTransientOrdersDesc, exports.QueryTraderDerivativeOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeMarkets = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeMarketsDesc, exports.QueryDerivativeMarketsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeMarket = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeMarketDesc, exports.QueryDerivativeMarketRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DerivativeMarketAddress = function (request, metadata) {
        return this.rpc.unary(exports.QueryDerivativeMarketAddressDesc, exports.QueryDerivativeMarketAddressRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountTradeNonce = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountTradeNonceDesc, exports.QuerySubaccountTradeNonceRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ExchangeModuleState = function (request, metadata) {
        return this.rpc.unary(exports.QueryExchangeModuleStateDesc, exports.QueryModuleStateRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.Positions = function (request, metadata) {
        return this.rpc.unary(exports.QueryPositionsDesc, exports.QueryPositionsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountPositions = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountPositionsDesc, exports.QuerySubaccountPositionsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountPositionInMarket = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountPositionInMarketDesc, exports.QuerySubaccountPositionInMarketRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountEffectivePositionInMarket = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountEffectivePositionInMarketDesc, exports.QuerySubaccountEffectivePositionInMarketRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PerpetualMarketInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryPerpetualMarketInfoDesc, exports.QueryPerpetualMarketInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ExpiryFuturesMarketInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryExpiryFuturesMarketInfoDesc, exports.QueryExpiryFuturesMarketInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PerpetualMarketFunding = function (request, metadata) {
        return this.rpc.unary(exports.QueryPerpetualMarketFundingDesc, exports.QueryPerpetualMarketFundingRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.SubaccountOrderMetadata = function (request, metadata) {
        return this.rpc.unary(exports.QuerySubaccountOrderMetadataDesc, exports.QuerySubaccountOrderMetadataRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TradeRewardPoints = function (request, metadata) {
        return this.rpc.unary(exports.QueryTradeRewardPointsDesc, exports.QueryTradeRewardPointsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.PendingTradeRewardPoints = function (request, metadata) {
        return this.rpc.unary(exports.QueryPendingTradeRewardPointsDesc, exports.QueryTradeRewardPointsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TradeRewardCampaign = function (request, metadata) {
        return this.rpc.unary(exports.QueryTradeRewardCampaignDesc, exports.QueryTradeRewardCampaignRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FeeDiscountAccountInfo = function (request, metadata) {
        return this.rpc.unary(exports.QueryFeeDiscountAccountInfoDesc, exports.QueryFeeDiscountAccountInfoRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FeeDiscountSchedule = function (request, metadata) {
        return this.rpc.unary(exports.QueryFeeDiscountScheduleDesc, exports.QueryFeeDiscountScheduleRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BalanceMismatches = function (request, metadata) {
        return this.rpc.unary(exports.QueryBalanceMismatchesDesc, exports.QueryBalanceMismatchesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BalanceWithBalanceHolds = function (request, metadata) {
        return this.rpc.unary(exports.QueryBalanceWithBalanceHoldsDesc, exports.QueryBalanceWithBalanceHoldsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.FeeDiscountTierStatistics = function (request, metadata) {
        return this.rpc.unary(exports.QueryFeeDiscountTierStatisticsDesc, exports.QueryFeeDiscountTierStatisticsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MitoVaultInfos = function (request, metadata) {
        return this.rpc.unary(exports.QueryMitoVaultInfosDesc, exports.MitoVaultInfosRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.QueryMarketIDFromVault = function (request, metadata) {
        return this.rpc.unary(exports.QueryQueryMarketIDFromVaultDesc, exports.QueryMarketIDFromVaultRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.HistoricalTradeRecords = function (request, metadata) {
        return this.rpc.unary(exports.QueryHistoricalTradeRecordsDesc, exports.QueryHistoricalTradeRecordsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.IsOptedOutOfRewards = function (request, metadata) {
        return this.rpc.unary(exports.QueryIsOptedOutOfRewardsDesc, exports.QueryIsOptedOutOfRewardsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.OptedOutOfRewardsAccounts = function (request, metadata) {
        return this.rpc.unary(exports.QueryOptedOutOfRewardsAccountsDesc, exports.QueryOptedOutOfRewardsAccountsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MarketVolatility = function (request, metadata) {
        return this.rpc.unary(exports.QueryMarketVolatilityDesc, exports.QueryMarketVolatilityRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.BinaryOptionsMarkets = function (request, metadata) {
        return this.rpc.unary(exports.QueryBinaryOptionsMarketsDesc, exports.QueryBinaryMarketsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.TraderDerivativeConditionalOrders = function (request, metadata) {
        return this.rpc.unary(exports.QueryTraderDerivativeConditionalOrdersDesc, exports.QueryTraderDerivativeConditionalOrdersRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MarketAtomicExecutionFeeMultiplier = function (request, metadata) {
        return this.rpc.unary(exports.QueryMarketAtomicExecutionFeeMultiplierDesc, exports.QueryMarketAtomicExecutionFeeMultiplierRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.ActiveStakeGrant = function (request, metadata) {
        return this.rpc.unary(exports.QueryActiveStakeGrantDesc, exports.QueryActiveStakeGrantRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GrantAuthorization = function (request, metadata) {
        return this.rpc.unary(exports.QueryGrantAuthorizationDesc, exports.QueryGrantAuthorizationRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.GrantAuthorizations = function (request, metadata) {
        return this.rpc.unary(exports.QueryGrantAuthorizationsDesc, exports.QueryGrantAuthorizationsRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MarketBalance = function (request, metadata) {
        return this.rpc.unary(exports.QueryMarketBalanceDesc, exports.QueryMarketBalanceRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.MarketBalances = function (request, metadata) {
        return this.rpc.unary(exports.QueryMarketBalancesDesc, exports.QueryMarketBalancesRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DenomMinNotional = function (request, metadata) {
        return this.rpc.unary(exports.QueryDenomMinNotionalDesc, exports.QueryDenomMinNotionalRequest.fromPartial(request), metadata);
    };
    QueryClientImpl.prototype.DenomMinNotionals = function (request, metadata) {
        return this.rpc.unary(exports.QueryDenomMinNotionalsDesc, exports.QueryDenomMinNotionalsRequest.fromPartial(request), metadata);
    };
    return QueryClientImpl;
}());
exports.QueryClientImpl = QueryClientImpl;
exports.QueryDesc = { serviceName: "injective.exchange.v1beta1.Query" };
exports.QueryL3DerivativeOrderBookDesc = {
    methodName: "L3DerivativeOrderBook",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFullDerivativeOrderbookRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFullDerivativeOrderbookResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryL3SpotOrderBookDesc = {
    methodName: "L3SpotOrderBook",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFullSpotOrderbookRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFullSpotOrderbookResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryQueryExchangeParamsDesc = {
    methodName: "QueryExchangeParams",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryExchangeParamsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryExchangeParamsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountDepositsDesc = {
    methodName: "SubaccountDeposits",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountDepositsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountDepositsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountDepositDesc = {
    methodName: "SubaccountDeposit",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountDepositRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountDepositResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryExchangeBalancesDesc = {
    methodName: "ExchangeBalances",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryExchangeBalancesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryExchangeBalancesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAggregateVolumeDesc = {
    methodName: "AggregateVolume",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAggregateVolumeRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAggregateVolumeResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAggregateVolumesDesc = {
    methodName: "AggregateVolumes",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAggregateVolumesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAggregateVolumesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAggregateMarketVolumeDesc = {
    methodName: "AggregateMarketVolume",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAggregateMarketVolumeRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAggregateMarketVolumeResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAggregateMarketVolumesDesc = {
    methodName: "AggregateMarketVolumes",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAggregateMarketVolumesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAggregateMarketVolumesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDenomDecimalDesc = {
    methodName: "DenomDecimal",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDenomDecimalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDenomDecimalResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDenomDecimalsDesc = {
    methodName: "DenomDecimals",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDenomDecimalsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDenomDecimalsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySpotMarketsDesc = {
    methodName: "SpotMarkets",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySpotMarketsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySpotMarketsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySpotMarketDesc = {
    methodName: "SpotMarket",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySpotMarketRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySpotMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFullSpotMarketsDesc = {
    methodName: "FullSpotMarkets",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFullSpotMarketsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFullSpotMarketsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFullSpotMarketDesc = {
    methodName: "FullSpotMarket",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFullSpotMarketRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFullSpotMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySpotOrderbookDesc = {
    methodName: "SpotOrderbook",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySpotOrderbookRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySpotOrderbookResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTraderSpotOrdersDesc = {
    methodName: "TraderSpotOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTraderSpotOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTraderSpotOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAccountAddressSpotOrdersDesc = {
    methodName: "AccountAddressSpotOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAccountAddressSpotOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAccountAddressSpotOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySpotOrdersByHashesDesc = {
    methodName: "SpotOrdersByHashes",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySpotOrdersByHashesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySpotOrdersByHashesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountOrdersDesc = {
    methodName: "SubaccountOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTraderSpotTransientOrdersDesc = {
    methodName: "TraderSpotTransientOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTraderSpotOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTraderSpotOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySpotMidPriceAndTOBDesc = {
    methodName: "SpotMidPriceAndTOB",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySpotMidPriceAndTOBRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySpotMidPriceAndTOBResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeMidPriceAndTOBDesc = {
    methodName: "DerivativeMidPriceAndTOB",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeMidPriceAndTOBRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeMidPriceAndTOBResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeOrderbookDesc = {
    methodName: "DerivativeOrderbook",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeOrderbookRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeOrderbookResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTraderDerivativeOrdersDesc = {
    methodName: "TraderDerivativeOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTraderDerivativeOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTraderDerivativeOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryAccountAddressDerivativeOrdersDesc = {
    methodName: "AccountAddressDerivativeOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryAccountAddressDerivativeOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryAccountAddressDerivativeOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeOrdersByHashesDesc = {
    methodName: "DerivativeOrdersByHashes",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeOrdersByHashesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeOrdersByHashesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTraderDerivativeTransientOrdersDesc = {
    methodName: "TraderDerivativeTransientOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTraderDerivativeOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTraderDerivativeOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeMarketsDesc = {
    methodName: "DerivativeMarkets",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeMarketsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeMarketsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeMarketDesc = {
    methodName: "DerivativeMarket",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeMarketRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDerivativeMarketAddressDesc = {
    methodName: "DerivativeMarketAddress",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDerivativeMarketAddressRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDerivativeMarketAddressResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountTradeNonceDesc = {
    methodName: "SubaccountTradeNonce",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountTradeNonceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountTradeNonceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryExchangeModuleStateDesc = {
    methodName: "ExchangeModuleState",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryModuleStateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryModuleStateResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPositionsDesc = {
    methodName: "Positions",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPositionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPositionsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountPositionsDesc = {
    methodName: "SubaccountPositions",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountPositionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountPositionsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountPositionInMarketDesc = {
    methodName: "SubaccountPositionInMarket",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountPositionInMarketRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountPositionInMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountEffectivePositionInMarketDesc = {
    methodName: "SubaccountEffectivePositionInMarket",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountEffectivePositionInMarketRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountEffectivePositionInMarketResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPerpetualMarketInfoDesc = {
    methodName: "PerpetualMarketInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPerpetualMarketInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPerpetualMarketInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryExpiryFuturesMarketInfoDesc = {
    methodName: "ExpiryFuturesMarketInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryExpiryFuturesMarketInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryExpiryFuturesMarketInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPerpetualMarketFundingDesc = {
    methodName: "PerpetualMarketFunding",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryPerpetualMarketFundingRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryPerpetualMarketFundingResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QuerySubaccountOrderMetadataDesc = {
    methodName: "SubaccountOrderMetadata",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QuerySubaccountOrderMetadataRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QuerySubaccountOrderMetadataResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTradeRewardPointsDesc = {
    methodName: "TradeRewardPoints",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTradeRewardPointsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTradeRewardPointsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryPendingTradeRewardPointsDesc = {
    methodName: "PendingTradeRewardPoints",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTradeRewardPointsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTradeRewardPointsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTradeRewardCampaignDesc = {
    methodName: "TradeRewardCampaign",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTradeRewardCampaignRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTradeRewardCampaignResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFeeDiscountAccountInfoDesc = {
    methodName: "FeeDiscountAccountInfo",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFeeDiscountAccountInfoRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFeeDiscountAccountInfoResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFeeDiscountScheduleDesc = {
    methodName: "FeeDiscountSchedule",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFeeDiscountScheduleRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFeeDiscountScheduleResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBalanceMismatchesDesc = {
    methodName: "BalanceMismatches",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBalanceMismatchesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBalanceMismatchesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBalanceWithBalanceHoldsDesc = {
    methodName: "BalanceWithBalanceHolds",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBalanceWithBalanceHoldsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBalanceWithBalanceHoldsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryFeeDiscountTierStatisticsDesc = {
    methodName: "FeeDiscountTierStatistics",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryFeeDiscountTierStatisticsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryFeeDiscountTierStatisticsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMitoVaultInfosDesc = {
    methodName: "MitoVaultInfos",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.MitoVaultInfosRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.MitoVaultInfosResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryQueryMarketIDFromVaultDesc = {
    methodName: "QueryMarketIDFromVault",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryMarketIDFromVaultRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryMarketIDFromVaultResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryHistoricalTradeRecordsDesc = {
    methodName: "HistoricalTradeRecords",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryHistoricalTradeRecordsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryHistoricalTradeRecordsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryIsOptedOutOfRewardsDesc = {
    methodName: "IsOptedOutOfRewards",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryIsOptedOutOfRewardsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryIsOptedOutOfRewardsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryOptedOutOfRewardsAccountsDesc = {
    methodName: "OptedOutOfRewardsAccounts",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryOptedOutOfRewardsAccountsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryOptedOutOfRewardsAccountsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMarketVolatilityDesc = {
    methodName: "MarketVolatility",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryMarketVolatilityRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryMarketVolatilityResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryBinaryOptionsMarketsDesc = {
    methodName: "BinaryOptionsMarkets",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryBinaryMarketsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryBinaryMarketsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryTraderDerivativeConditionalOrdersDesc = {
    methodName: "TraderDerivativeConditionalOrders",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryTraderDerivativeConditionalOrdersRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryTraderDerivativeConditionalOrdersResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMarketAtomicExecutionFeeMultiplierDesc = {
    methodName: "MarketAtomicExecutionFeeMultiplier",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryMarketAtomicExecutionFeeMultiplierRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryMarketAtomicExecutionFeeMultiplierResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryActiveStakeGrantDesc = {
    methodName: "ActiveStakeGrant",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryActiveStakeGrantRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryActiveStakeGrantResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGrantAuthorizationDesc = {
    methodName: "GrantAuthorization",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryGrantAuthorizationRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryGrantAuthorizationResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryGrantAuthorizationsDesc = {
    methodName: "GrantAuthorizations",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryGrantAuthorizationsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryGrantAuthorizationsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMarketBalanceDesc = {
    methodName: "MarketBalance",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryMarketBalanceRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryMarketBalanceResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryMarketBalancesDesc = {
    methodName: "MarketBalances",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryMarketBalancesRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryMarketBalancesResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDenomMinNotionalDesc = {
    methodName: "DenomMinNotional",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDenomMinNotionalRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDenomMinNotionalResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
exports.QueryDenomMinNotionalsDesc = {
    methodName: "DenomMinNotionals",
    service: exports.QueryDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary: function () {
            return exports.QueryDenomMinNotionalsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary: function (data) {
            var value = exports.QueryDenomMinNotionalsResponse.decode(data);
            return __assign(__assign({}, value), { toObject: function () {
                    return value;
                } });
        },
    },
};
var GrpcWebImpl = /** @class */ (function () {
    function GrpcWebImpl(host, options) {
        this.host = host;
        this.options = options;
    }
    GrpcWebImpl.prototype.unary = function (methodDesc, _request, metadata) {
        var _this = this;
        var _a;
        var request = __assign(__assign({}, _request), methodDesc.requestType);
        var maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(__assign(__assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise(function (resolve, reject) {
            grpc_web_1.grpc.unary(methodDesc, {
                request: request,
                host: _this.host,
                metadata: maybeCombinedMetadata,
                transport: _this.options.transport,
                debug: _this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        var err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    };
    return GrpcWebImpl;
}());
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (function () {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
var GrpcWebError = /** @class */ (function (_super) {
    __extends(GrpcWebError, _super);
    function GrpcWebError(message, code, metadata) {
        var _this = _super.call(this, message) || this;
        _this.code = code;
        _this.metadata = metadata;
        return _this;
    }
    return GrpcWebError;
}(tsProtoGlobalThis.Error));
exports.GrpcWebError = GrpcWebError;
