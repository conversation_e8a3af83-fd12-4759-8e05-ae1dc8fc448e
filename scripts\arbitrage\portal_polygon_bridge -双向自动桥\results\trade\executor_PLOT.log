2025-05-19 07:43:28,760 - INFO - ================================================================================
2025-05-19 07:43:28,760 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 07:43:28
2025-05-19 07:43:28,760 - INFO - 链: polygon, 投入金额: 255.66 USDT
2025-05-19 07:43:28,760 - INFO - 代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,761 - INFO - 收到PLOT买入请求 - 链:polygon, 投入:255.66USDT
2025-05-19 07:43:28,761 - INFO - PLOT: 将在polygon链上执行买入，代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,761 - INFO - PLOT: 准备使用KyberSwap在polygon上执行255.66USDT买入PLOT交易
2025-05-19 07:43:28,761 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 07:43:28,771 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 07:43:28,771 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 07:43:28,771 - INFO -   chain: polygon
2025-05-19 07:43:28,771 - INFO -   token_in: USDT
2025-05-19 07:43:28,771 - INFO -   token_out: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-19 07:43:28,772 - INFO -   amount: 255.66
2025-05-19 07:43:28,772 - INFO -   slippage: 0.5%
2025-05-19 07:43:28,772 - INFO -   real: True
2025-05-19 07:43:29,774 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 07:43:29,774 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 255.66 USDT，可用: 158.314544 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount': 255.66, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount_in_wei': '255660000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 07:43:29,774 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 255.66 USDT，可用: 158.314544 USDT
2025-05-19 07:43:29,775 - INFO - 读取到 122 条现有交易记录
2025-05-19 07:43:29,775 - INFO - 添加新交易记录: PLOT (PLOT_255.66_2025-05-19 07:43:28)
2025-05-19 07:43:29,777 - INFO - 成功保存 123 条交易记录
2025-05-19 07:43:29,777 - INFO - PLOT: 买入交易处理完成，耗时: 1.02秒
2025-05-19 07:43:29,777 - INFO - ================================================================================
2025-05-19 10:12:00,790 - INFO - ================================================================================
2025-05-19 10:12:00,790 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 10:12:00
2025-05-19 10:12:00,790 - INFO - 链: ethereum, 投入金额: 211.32 USDT
2025-05-19 10:12:00,790 - INFO - 代币地址: ******************************************
2025-05-19 10:12:00,790 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:211.32USDT
2025-05-19 10:12:00,790 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:12:00,790 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行211.32USDT买入PLOT交易
2025-05-19 10:12:00,790 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:12:00,791 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:12:00,827 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 10:12:00,827 - INFO -   chain: ethereum
2025-05-19 10:12:00,827 - INFO -   token_in: USDT
2025-05-19 10:12:00,827 - INFO -   token_out: ******************************************
2025-05-19 10:12:00,827 - INFO -   amount: 211.32
2025-05-19 10:12:00,827 - INFO -   slippage: 0.5%
2025-05-19 10:12:00,827 - INFO -   real: True
2025-05-19 10:12:02,265 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:12:02,265 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 211.32, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '211320000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:12:02,265 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT
2025-05-19 10:12:02,266 - INFO - 读取到 128 条现有交易记录
2025-05-19 10:12:02,267 - INFO - 添加新交易记录: PLOT (PLOT_211.32_2025-05-19 10:12:00)
2025-05-19 10:12:02,269 - INFO - 成功保存 129 条交易记录
2025-05-19 10:12:02,269 - INFO - PLOT: 买入交易处理完成，耗时: 1.48秒
2025-05-19 10:12:02,269 - INFO - ================================================================================
2025-05-19 10:43:21,082 - INFO - ================================================================================
2025-05-19 10:43:21,082 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 10:43:21
2025-05-19 10:43:21,082 - INFO - 链: ethereum, 投入金额: 211.32 USDT
2025-05-19 10:43:21,082 - INFO - 代币地址: ******************************************
2025-05-19 10:43:21,082 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:211.32USDT
2025-05-19 10:43:21,082 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 10:43:21,082 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行211.32USDT买入PLOT交易
2025-05-19 10:43:21,082 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 10:43:21,082 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 10:43:21,082 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 10:43:21,082 - INFO -   chain: ethereum
2025-05-19 10:43:21,082 - INFO -   token_in: USDT
2025-05-19 10:43:21,082 - INFO -   token_out: ******************************************
2025-05-19 10:43:21,082 - INFO -   amount: 211.32
2025-05-19 10:43:21,082 - INFO -   slippage: 0.5%
2025-05-19 10:43:21,082 - INFO -   real: True
2025-05-19 10:43:23,513 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 10:43:23,513 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 211.32, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '211320000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 10:43:23,513 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 211.32 USDT，可用: 67.81312 USDT
2025-05-19 10:43:23,514 - INFO - 读取到 132 条现有交易记录
2025-05-19 10:43:23,514 - INFO - 添加新交易记录: PLOT (PLOT_211.32_2025-05-19 10:43:21)
2025-05-19 10:43:23,516 - INFO - 成功保存 133 条交易记录
2025-05-19 10:43:23,516 - INFO - PLOT: 买入交易处理完成，耗时: 2.43秒
2025-05-19 10:43:23,516 - INFO - ================================================================================
2025-05-19 11:14:05,651 - INFO - ================================================================================
2025-05-19 11:14:05,651 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 11:14:05
2025-05-19 11:14:05,652 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-19 11:14:05,652 - INFO - 代币地址: ******************************************
2025-05-19 11:14:05,652 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-19 11:14:05,652 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 11:14:05,652 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行300.0USDT买入PLOT交易
2025-05-19 11:14:05,652 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 11:14:05,652 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 11:14:05,652 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 11:14:05,652 - INFO -   chain: ethereum
2025-05-19 11:14:05,652 - INFO -   token_in: USDT
2025-05-19 11:14:05,652 - INFO -   token_out: ******************************************
2025-05-19 11:14:05,653 - INFO -   amount: 300.0
2025-05-19 11:14:05,653 - INFO -   slippage: 0.5%
2025-05-19 11:14:05,653 - INFO -   real: True
2025-05-19 11:14:08,029 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 11:14:08,030 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 11:14:08,030 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT
2025-05-19 11:14:08,031 - INFO - 读取到 137 条现有交易记录
2025-05-19 11:14:08,032 - INFO - 添加新交易记录: PLOT (PLOT_300.0_2025-05-19 11:14:05)
2025-05-19 11:14:08,034 - INFO - 成功保存 138 条交易记录
2025-05-19 11:14:08,034 - INFO - PLOT: 买入交易处理完成，耗时: 2.38秒
2025-05-19 11:14:08,034 - INFO - ================================================================================
2025-05-19 12:50:52,170 - INFO - ================================================================================
2025-05-19 12:50:52,170 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 12:50:52
2025-05-19 12:50:52,170 - INFO - 链: ethereum, 投入金额: 144.81 USDT
2025-05-19 12:50:52,171 - INFO - 代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:144.81USDT
2025-05-19 12:50:52,171 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:50:52,171 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行144.81USDT买入PLOT交易
2025-05-19 12:50:52,171 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:50:52,171 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:50:52,171 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 12:50:52,171 - INFO -   chain: ethereum
2025-05-19 12:50:52,171 - INFO -   token_in: USDT
2025-05-19 12:50:52,171 - INFO -   token_out: ******************************************
2025-05-19 12:50:52,171 - INFO -   amount: 144.81
2025-05-19 12:50:52,171 - INFO -   slippage: 0.5%
2025-05-19 12:50:52,171 - INFO -   real: True
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:50:54,684 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 144.81, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '144810000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:50:54,684 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 144.81 USDT，可用: 67.81312 USDT
2025-05-19 12:50:54,685 - INFO - 读取到 142 条现有交易记录
2025-05-19 12:50:54,685 - INFO - 添加新交易记录: PLOT (PLOT_144.81_2025-05-19 12:50:52)
2025-05-19 12:50:54,688 - INFO - 成功保存 143 条交易记录
2025-05-19 12:50:54,688 - INFO - PLOT: 买入交易处理完成，耗时: 2.52秒
2025-05-19 12:50:54,688 - INFO - ================================================================================
2025-05-19 13:23:30,587 - INFO - ================================================================================
2025-05-19 13:23:30,588 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 13:23:30
2025-05-19 13:23:30,588 - INFO - 链: ethereum, 投入金额: 189.15 USDT
2025-05-19 13:23:30,588 - INFO - 代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:189.15USDT
2025-05-19 13:23:30,588 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 13:23:30,588 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行189.15USDT买入PLOT交易
2025-05-19 13:23:30,588 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 13:23:30,588 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 13:23:30,588 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 13:23:30,588 - INFO -   chain: ethereum
2025-05-19 13:23:30,588 - INFO -   token_in: USDT
2025-05-19 13:23:30,588 - INFO -   token_out: ******************************************
2025-05-19 13:23:30,588 - INFO -   amount: 189.15
2025-05-19 13:23:30,589 - INFO -   slippage: 0.5%
2025-05-19 13:23:30,589 - INFO -   real: True
2025-05-19 13:23:33,140 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 13:23:33,144 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 189.15, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '189150000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 13:23:33,145 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 189.15 USDT，可用: 67.81312 USDT
2025-05-19 13:23:33,146 - INFO - 读取到 144 条现有交易记录
2025-05-19 13:23:33,146 - INFO - 添加新交易记录: PLOT (PLOT_189.15_2025-05-19 13:23:30)
2025-05-19 13:23:33,151 - INFO - 成功保存 145 条交易记录
2025-05-19 13:23:33,151 - INFO - PLOT: 买入交易处理完成，耗时: 2.56秒
2025-05-19 13:23:33,151 - INFO - ================================================================================
2025-05-19 14:37:46,801 - INFO - ================================================================================
2025-05-19 14:37:46,802 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-19 14:37:46
2025-05-19 14:37:46,802 - INFO - 链: ethereum, 投入金额: 233.49 USDT
2025-05-19 14:37:46,802 - INFO - 代币地址: ******************************************
2025-05-19 14:37:46,802 - INFO - 收到PLOT买入请求 - 链:ethereum, 投入:233.49USDT
2025-05-19 14:37:46,802 - INFO - PLOT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 14:37:46,802 - INFO - PLOT: 准备使用KyberSwap在ethereum上执行233.49USDT买入PLOT交易
2025-05-19 14:37:46,802 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 14:37:46,802 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 14:37:46,802 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-19 14:37:46,802 - INFO -   chain: ethereum
2025-05-19 14:37:46,802 - INFO -   token_in: USDT
2025-05-19 14:37:46,802 - INFO -   token_out: ******************************************
2025-05-19 14:37:46,802 - INFO -   amount: 233.49
2025-05-19 14:37:46,802 - INFO -   slippage: 0.5%
2025-05-19 14:37:46,803 - INFO -   real: True
2025-05-19 14:37:58,890 - INFO - DODO: swap_tokens返回值类型: <class 'dict'>
2025-05-19 14:37:58,891 - INFO - DODO: swap_tokens返回值内容: {'success': True, 'status': '成功', 'message': '交换完成，接收了 2082.314490954566 ****************************************** 到地址 ******************************************', 'error': None, 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 105.5, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '105500000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '2082314490954565679763', 'amount_out': 2082.314490954566, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '105500000', 'amountInUsd': '105.6569703345483', 'tokenOut': '******************************************', 'amountOut': '2082314490954565679763', 'amountOutUsd': '106.30974187143198', 'gas': '270000', 'gasPrice': '634499686', 'gasUsd': '0.40702206038646804', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '******************************************', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '105500000', 'amountOut': '44485320852446528', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 100, 'priceLimit': '1461446703485210103287273052203988822378723970341'}, 'extra': {'nSqrtRx96': '3858165122369143791099021', 'ri': '930ca437-3d21-4dc2-a676-191580b77c5f'}}, {'pool': '0x68fa181c720c07b7ff7412220e2431ce90a65a14', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '44485320852446528', 'amountOut': '2082314490954565679763', 'exchange': 'uniswap', 'poolType': 'uniswap-v2', 'poolExtra': {'fee': 3, 'feePrecision': 1000, 'blockNumber': 22515257}, 'extra': None}]], 'routeID': '930ca437-3d21-4dc2-a676-191580b77c5f', 'checksum': '6654648624114982781', 'timestamp': 1747636630}, 'tx_hash': '0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61', 'receipt': AttributeDict({'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'blockNumber': 22515273, 'contractAddress': None, 'cumulativeGasUsed': 18887547, 'effectiveGasPrice': 906545792, 'from': '******************************************', 'gasUsed': 300053, 'logs': [AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000649cd60'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 585, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000009e273ae6763e41'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 586, 'removed': False}), AttributeDict({'address': '******************************************', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000649cd60'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 587, 'removed': False}), AttributeDict({'address': '0xc7bBeC68d12a0d1830360F8Ec58fA599bA1b0e9b', 'topics': [HexBytes('0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0xffffffffffffffffffffffffffffffffffffffffffffffffff61d8c51989c1bf000000000000000000000000000000000000000000000000000000000649cd600000000000000000000000000000000000000000000330b6055570cb905d3c41000000000000000000000000000000000000000000000000023678e8200d8fc2fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcf829'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 588, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b000000000000000000000000000000000000000000000000009e273ae6763e41000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 589, 'removed': False}), AttributeDict({'address': '0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000009e273ae6763e41'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 590, 'removed': False}), AttributeDict({'address': '0x43Dfc4159D86F3A37A5A4B3D4580b888ad7d4DDd', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 591, 'removed': False}), AttributeDict({'address': '0x68Fa181c720C07B7FF7412220E2431ce90A65A14', 'topics': [HexBytes('0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1')], 'data': HexBytes('0x00000000000000000000000000000000000000000000e7ce8da64750210ed07000000000000000000000000000000000000000000000000144a43ae63edd7454'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 592, 'removed': False}), AttributeDict({'address': '0x68Fa181c720C07B7FF7412220E2431ce90A65A14', 'topics': [HexBytes('0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6')], 'data': HexBytes('0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009e273ae6763e41000000000000000000000000000000000000000000000070cdb4373336b3fe520000000000000000000000000000000000000000000000000000000000000000'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 593, 'removed': False}), AttributeDict({'address': '0x6E4141d33021b52C91c28608403db4A0FFB50Ec6', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x00000000000000000000000068fa181c720c07b7ff7412220e2431ce90a65a14000000000000000000000000000000000000000000000070cdb4373336b3fe5200000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 594, 'removed': False}), AttributeDict({'address': '0x43Dfc4159D86F3A37A5A4B3D4580b888ad7d4DDd', 'topics': [HexBytes('0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'), HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6'), HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434')], 'data': HexBytes('0x000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 595, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xd6d4f5681c246c9f42c203e287975af1601f8df8035a9251f79aab5c8f09e2f8')], 'data': HexBytes('0x000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000dac17f958d2ee523a2206206994597c13d831ec700000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd000000000000000000000000f9f2934331c051a37bb6e9c1397205c0e458f434000000000000000000000000000000000000000000000000000000000649cd60000000000000000000000000000000000000000000000070cdb4373336b3fe52'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 596, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0xddac40937f35385a34f721af292e5a83fc5b840f722bff57c2fc71adba708c48')], 'data': HexBytes('0x0000000000000000000000006e4141d33021b52c91c28608403db4a0ffb50ec6000000000000000000000000000000000000000000000070cdb4373336b3fe5200000000000000000000000043dfc4159d86f3a37a5a4b3d4580b888ad7d4ddd'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 597, 'removed': False}), AttributeDict({'address': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'topics': [HexBytes('0x095e66fa4dd6a6f7b43fb8444a7bd0edb870508c7abf639bc216efb0bcff9779')], 'data': HexBytes('0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000002967b22536f75726365223a226b79626572737761702d6170692d636c69656e74222c22416d6f756e74496e555344223a223130352e36353639373033333435343833222c22416d6f756e744f7574555344223a223130362e3330393734313837313433313938222c22526566657272616c223a22222c22466c616773223a302c22416d6f756e744f7574223a2232303832333134343930393534353635363739373633222c2254696d657374616d70223a313734373633363633372c22526f7574654944223a2234393530643538662d306361662d343263392d386265362d6265613166613935633835363a30396666666562352d383764642d343930362d616135382d303931313435333861343465222c22496e74656772697479496e666f223a7b224b65794944223a2231222c225369676e6174757265223a2263394a54336b38714b6a65304d7a383864387557336b31416275516e51693451306e676a50366e72576e7a6352564a6e63786c4757524f436b49424a7778384747444a48756b557554337a57654b3675575a576b4e6e577867437531724b733430774d4c5355485a7a36724134586b6a694e6c533034753457372b54716571736d57774e696a4c7358434b512f2f5a72787953464b305747566637345a477975693930384e39504855684c4f53625530644536683374494466336a394e6545794b6e5548544e54534a623039434d44504d39584e4d59513346655a446f4532435448465967374c486a536d58586846684c4a616f57476d51364c713565596374627a446832704667442b67315a4d6c53547934575a4f6d74432f4f4a363753432b776a7459656a624a504a4f787974386a586e366b3339392f63716746537a4b707a58716c4479306c3534487a72576a3056655533413d3d227d7d00000000000000000000'), 'blockNumber': 22515273, 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'blockHash': HexBytes('0x59f52bd4f1fdc8b5c1088e554e1505049b4ed161c08222bc9dddb27ce7a925a5'), 'logIndex': 598, 'removed': False})], 'logsBloom': HexBytes('0x00202000010000000000000080000000000000000000000000000000200000000000001010000000000000000000010006000000080220000080000000000000000000000000000800000028000000200000003000000000000000000020000000000000002000000000000000000000000000000000004000000010000a00020000000000000000060000000000000000000000400008080000004000100000000000000000000000000080000800000000400000000000004000000000000000000002000008000000000000201c00000000000401001000400000000000020000200000002000040000000000000000008000010000000000000000000000'), 'status': 1, 'to': '0x6131B5fae19EA4f9D964eAc0408E4408b66337b5', 'transactionHash': HexBytes('0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61'), 'transactionIndex': 149, 'type': 0})}
2025-05-19 14:37:58,892 - INFO - DODO: 交易消息: 交换完成，接收了 2082.314490954566 ****************************************** 到地址 ******************************************
2025-05-19 14:37:58,894 - INFO - DODO: 从消息中提取到代币数量: 2082.314490954566
2025-05-19 14:37:58,894 - INFO - DODO: 交易成功 - 哈希: 0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61
2025-05-19 14:37:58,894 - INFO - DODO: 最终确认的购买数量: 2082.314490954566
2025-05-19 14:37:58,895 - INFO - 读取到 146 条现有交易记录
2025-05-19 14:37:58,896 - INFO - 添加新交易记录: DODO (DODO_105.5_2025-05-19 14:37:03)
2025-05-19 14:37:58,898 - INFO - 成功保存 147 条交易记录
2025-05-19 14:37:58,898 - INFO - DODO: 交易成功，从区块链获取实际买入数量...
2025-05-19 14:37:58,904 - INFO - DODO: 使用tx_token_change_tracker获取交易 0xf136a5734a0a55fecc4b19b7808fd020bc8e7652bef0f88476744f5ebcd07a61 的代币数量...
2025-05-19 14:37:59,849 - INFO - DODO: 从区块链获取到实际买入数量: 2080.8578692221536 DODO
2025-05-19 14:38:01,653 - INFO - DODO: 开始从以太坊桥接到Polygon...
2025-05-19 14:38:01,657 - ERROR - DODO: 桥接失败: Bridge未正确初始化，请检查private_key是否正确传入
2025-05-19 14:38:01,658 - INFO - DODO: 等待桥接完成...
2025-05-19 14:38:01,658 - WARNING - DODO: 未获取到Polygon到账交易哈希
2025-05-19 14:38:01,658 - INFO - DODO: 桥接操作完成
2025-05-19 14:38:01,658 - INFO - DODO: 桥接操作完成，结果: {'success': False, 'message': 'Bridge未正确初始化，请检查private_key是否正确传入', 'bridge_tx': None}
2025-05-19 14:38:01,659 - INFO - DODO: 买入交易处理完成，耗时: 58.63秒
2025-05-19 14:38:01,659 - INFO - ================================================================================
2025-05-19 14:38:05,207 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-19 14:38:05,207 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 233.49，可用: 174.738189', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 233.49, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '233490000', 'is_native_in': False, 'is_native_out': False, 'raw_amount_out': '51767426376219237554871', 'amount_out': 51767.426376219235, 'route_summary': {'tokenIn': '******************************************', 'amountIn': '233490000', 'amountInUsd': '233.73506045907484', 'tokenOut': '******************************************', 'amountOut': '51767426376219237554871', 'amountOutUsd': '234.46089726205048', 'gas': '295000', 'gasPrice': '682397557', 'gasUsd': '0.4776922749352929', 'l1FeeUsd': '0', 'extraFee': {'feeAmount': '', 'chargeFeeBy': '', 'isInBps': False, 'feeReceiver': ''}, 'route': [[{'pool': '******************************************', 'tokenIn': '******************************************', 'tokenOut': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'swapAmount': '233490000', 'amountOut': '98585800316979239', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 100, 'priceLimit': '1461446703485210103287273052203988822378723970341'}, 'extra': {'nSqrtRx96': '3855613856567439559819889', 'ri': '740ef857-ff77-466c-b094-bf8fb6d6c2df'}}, {'pool': '0xe13cf2d5d235aa25b489f55e4a940f8715d8d5ae', 'tokenIn': '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2', 'tokenOut': '******************************************', 'swapAmount': '98585800316979239', 'amountOut': '51767426376219237554871', 'exchange': 'uniswapv3', 'poolType': 'uniswapv3', 'poolExtra': {'swapFee': 3000, 'priceLimit': '4065270616124370550479328305'}, 'extra': {'nSqrtRx96': '109277932960907728894098298'}}]], 'routeID': '740ef857-ff77-466c-b094-bf8fb6d6c2df', 'checksum': '6674791427317512220', 'timestamp': 1747636673}}
2025-05-19 14:38:05,207 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 233.49，可用: 174.738189
2025-05-19 14:38:05,208 - INFO - 读取到 147 条现有交易记录
2025-05-19 14:38:05,209 - INFO - 添加新交易记录: PLOT (PLOT_233.49_2025-05-19 14:37:46)
2025-05-19 14:38:05,211 - INFO - 成功保存 148 条交易记录
2025-05-19 14:38:05,211 - INFO - PLOT: 买入交易处理完成，耗时: 18.41秒
2025-05-19 14:38:05,211 - INFO - ================================================================================
2025-05-20 12:55:25,982 - INFO - ================================================================================
2025-05-20 12:55:25,982 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-20 12:55:25
2025-05-20 12:55:25,982 - INFO - 链: polygon, 投入金额: 255.66 USDT
2025-05-20 12:55:25,982 - INFO - 代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-20 12:55:25,982 - INFO - 收到PLOT买入请求 - 链:polygon, 投入:255.66USDT
2025-05-20 12:55:25,982 - INFO - PLOT: 将在polygon链上执行买入，代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-20 12:55:25,982 - INFO - PLOT: 准备使用KyberSwap在polygon上执行255.66USDT买入PLOT交易
2025-05-20 12:55:25,983 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-20 12:55:25,992 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-20 12:55:25,992 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-20 12:55:25,992 - INFO -   chain: polygon
2025-05-20 12:55:25,992 - INFO -   token_in: USDT
2025-05-20 12:55:25,992 - INFO -   token_out: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-20 12:55:25,992 - INFO -   amount: 255.66
2025-05-20 12:55:25,993 - INFO -   slippage: 0.5%
2025-05-20 12:55:25,993 - INFO -   real: True
2025-05-20 12:55:26,967 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-20 12:55:26,974 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 255.66 USDT，可用: 83.942084 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount': 255.66, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount_in_wei': '255660000', 'is_native_in': False, 'is_native_out': False}
2025-05-20 12:55:26,976 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 255.66 USDT，可用: 83.942084 USDT
2025-05-20 12:55:26,980 - INFO - 读取到 156 条现有交易记录
2025-05-20 12:55:26,980 - INFO - 添加新交易记录: PLOT (PLOT_255.66_2025-05-20 12:55:25)
2025-05-20 12:55:26,985 - INFO - 成功保存 157 条交易记录
2025-05-20 12:55:26,985 - INFO - PLOT: 买入交易处理完成，耗时: 1.00秒
2025-05-20 12:55:26,985 - INFO - ================================================================================
2025-05-21 13:32:29,220 - INFO - ================================================================================
2025-05-21 13:32:29,220 - INFO - 开始执行 PLOT 买入交易 - 时间: 2025-05-21 13:32:29
2025-05-21 13:32:29,220 - INFO - 链: polygon, 投入金额: 300.0 USDT
2025-05-21 13:32:29,220 - INFO - 代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-21 13:32:29,220 - INFO - 收到PLOT买入请求 - 链:polygon, 投入:300.0USDT
2025-05-21 13:32:29,220 - INFO - PLOT: 将在polygon链上执行买入，代币地址: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-21 13:32:29,220 - INFO - PLOT: 准备使用KyberSwap在polygon上执行300.0USDT买入PLOT交易
2025-05-21 13:32:29,220 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-21 13:32:29,229 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-21 13:32:29,229 - INFO - PLOT: 准备调用swap_tokens函数，参数：
2025-05-21 13:32:29,229 - INFO -   chain: polygon
2025-05-21 13:32:29,229 - INFO -   token_in: USDT
2025-05-21 13:32:29,230 - INFO -   token_out: 0xe82808eaa78339b06a691fd92e1be79671cad8d3
2025-05-21 13:32:29,230 - INFO -   amount: 300.0
2025-05-21 13:32:29,230 - INFO -   slippage: 0.5%
2025-05-21 13:32:29,230 - INFO -   real: True
2025-05-21 13:32:30,224 - INFO - PLOT: swap_tokens返回值类型: <class 'dict'>
2025-05-21 13:32:30,224 - INFO - PLOT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 193.405006 USDT', 'chain': 'polygon', 'token_in': 'USDT', 'token_out': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount': 300.0, 'token_in_address': '0xc2132D05D31c914a87C6611C10748AEb04B58e8F', 'token_out_address': '0xe82808eaa78339b06a691fd92e1be79671cad8d3', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-21 13:32:30,224 - ERROR - PLOT: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 193.405006 USDT
2025-05-21 13:32:30,225 - INFO - 读取到 161 条现有交易记录
2025-05-21 13:32:30,225 - INFO - 添加新交易记录: PLOT (PLOT_300.0_2025-05-21 13:32:29)
2025-05-21 13:32:30,228 - INFO - 成功保存 162 条交易记录
2025-05-21 13:32:30,229 - INFO - PLOT: 买入交易处理完成，耗时: 1.01秒
2025-05-21 13:32:30,229 - INFO - ================================================================================
