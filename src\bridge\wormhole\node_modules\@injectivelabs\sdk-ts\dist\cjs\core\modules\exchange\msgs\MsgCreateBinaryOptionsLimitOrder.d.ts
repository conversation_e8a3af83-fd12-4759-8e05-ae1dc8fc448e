import { MsgBase } from '../../MsgBase.js';
import { SnakeCaseKeys } from 'snakecase-keys';
import { InjectiveExchangeV1Beta1Tx, InjectiveExchangeV1Beta1Exchange } from '@injectivelabs/core-proto-ts';
export declare namespace MsgCreateBinaryOptionsLimitOrder {
    interface Params {
        marketId: string;
        subaccountId: string;
        injectiveAddress: string;
        orderType: InjectiveExchangeV1Beta1Exchange.OrderType;
        triggerPrice?: string;
        feeRecipient: string;
        price: string;
        margin: string;
        quantity: string;
        cid?: string;
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder;
}
/**
 * @category Messages
 */
export default class MsgCreateBinaryOptionsLimitOrder extends MsgBase<MsgCreateBinaryOptionsLimitOrder.Params, MsgCreateBinaryOptionsLimitOrder.Proto> {
    static fromJSON(params: MsgCreateBinaryOptionsLimitOrder.Params): MsgCreateBinaryOptionsLimitOrder;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder;
    toData(): {
        sender: string;
        order: InjectiveExchangeV1Beta1Exchange.DerivativeOrder | undefined;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: SnakeCaseKeys<InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder>;
    };
    toWeb3Gw(): {
        sender: string;
        order: {
            market_id: string;
            order_info: {
                subaccount_id: string;
                fee_recipient: string;
                price: string;
                quantity: string;
                cid: string;
            } | undefined;
            order_type: InjectiveExchangeV1Beta1Exchange.OrderType;
            margin: string;
            trigger_price: string;
        } | undefined;
        '@type': string;
    };
    toEip712V2(): {
        order: any;
        sender: string;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            order: {
                order_info: {
                    price: string;
                    quantity: string;
                    subaccount_id?: string | undefined;
                    fee_recipient?: string | undefined;
                    cid?: string | undefined;
                };
                margin: string;
                trigger_price: string;
                market_id?: string | undefined;
                order_type?: InjectiveExchangeV1Beta1Exchange.OrderType | undefined;
            };
            sender: string;
        };
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgCreateBinaryOptionsLimitOrder;
    };
    toBinary(): Uint8Array;
}
