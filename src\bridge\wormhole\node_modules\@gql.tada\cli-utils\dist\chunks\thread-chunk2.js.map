{"version": 3, "file": "thread-chunk2.js", "sources": ["../../src/commands/turbo/thread.ts"], "sourcesContent": ["import ts from 'typescript';\nimport type { GraphQLSPConfig } from '@gql.tada/internal';\n\nimport { getSchemaNamesFromConfig } from '@gql.tada/internal';\nimport { findAllCallExpressions } from '@0no-co/graphqlsp/api';\n\nimport { programFactory } from '../../ts';\nimport { expose } from '../../threads';\n\nimport type { TurboSignal, TurboWarning, TurboDocument } from './types';\n\nexport interface TurboParams {\n  rootPath: string;\n  configPath: string;\n  pluginConfig: GraphQLSPConfig;\n}\n\nasync function* _runTurbo(params: TurboParams): AsyncIterableIterator<TurboSignal> {\n  const schemaNames = getSchemaNamesFromConfig(params.pluginConfig);\n  const factory = programFactory(params);\n\n  // NOTE: We add our override declaration here before loading all files\n  // This sets `__cacheDisabled` on the turbo cache, which disables the cache temporarily\n  // If we don't disable the cache then we couldn't regenerate it from inferred types\n  factory.addSourceFile({\n    fileId: '__gql-tada-override__.d.ts',\n    sourceText: DECLARATION_OVERRIDE,\n    scriptKind: ts.ScriptKind.TS,\n  });\n\n  const externalFiles = factory.createExternalFiles();\n  if (externalFiles.length) {\n    yield { kind: 'EXTERNAL_WARNING' };\n    await factory.addVirtualFiles(externalFiles);\n  }\n\n  const container = factory.build();\n  const pluginInfo = container.buildPluginInfo(params.pluginConfig);\n  const sourceFiles = container.getSourceFiles();\n\n  yield {\n    kind: 'FILE_COUNT',\n    fileCount: sourceFiles.length,\n  };\n\n  const checker = container.program.getTypeChecker();\n  for (const sourceFile of sourceFiles) {\n    let filePath = sourceFile.fileName;\n    const documents: TurboDocument[] = [];\n    const warnings: TurboWarning[] = [];\n\n    const calls = findAllCallExpressions(sourceFile, pluginInfo, false).nodes;\n    for (const call of calls) {\n      const callExpression = call.node.parent;\n      if (!ts.isCallExpression(callExpression)) {\n        continue;\n      }\n\n      const position = container.getSourcePosition(sourceFile, callExpression.getStart());\n      filePath = position.fileName;\n      if (!schemaNames.has(call.schema)) {\n        warnings.push({\n          message: call.schema\n            ? `The '${call.schema}' schema is not in the configuration but was referenced by document.`\n            : schemaNames.size > 1\n              ? 'The document is not for a known schema. Have you re-generated the output file?'\n              : 'Multiple schemas are configured, but the document is not for a specific schema.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      const returnType = checker.getTypeAtLocation(callExpression);\n      const argumentType = checker.getTypeAtLocation(call.node);\n      // NOTE: `returnType.symbol` is incorrectly typed and is in fact\n      // optional and not always present\n      if (!returnType.symbol || returnType.symbol.getEscapedName() !== 'TadaDocumentNode') {\n        warnings.push({\n          message:\n            `The discovered document is not of type \"TadaDocumentNode\".\\n` +\n            'If this is unexpected, please file an issue describing your case.',\n          file: position.fileName,\n          line: position.line,\n          col: position.col,\n        });\n        continue;\n      }\n\n      const argumentKey: string =\n        'value' in argumentType &&\n        typeof argumentType.value === 'string' &&\n        (argumentType.flags & ts.TypeFlags.StringLiteral) === 0\n          ? JSON.stringify(argumentType.value)\n          : checker.typeToString(argumentType, callExpression, BUILDER_FLAGS);\n      const documentType = checker.typeToString(returnType, callExpression, BUILDER_FLAGS);\n\n      documents.push({\n        schemaName: call.schema,\n        argumentKey,\n        documentType,\n      });\n    }\n\n    yield {\n      kind: 'FILE_TURBO',\n      filePath,\n      documents,\n      warnings,\n    };\n  }\n}\n\nexport const runTurbo = expose(_runTurbo);\n\nconst BUILDER_FLAGS: ts.TypeFormatFlags =\n  ts.TypeFormatFlags.NoTruncation |\n  ts.TypeFormatFlags.NoTypeReduction |\n  ts.TypeFormatFlags.InTypeAlias |\n  ts.TypeFormatFlags.UseFullyQualifiedType |\n  ts.TypeFormatFlags.GenerateNamesForShadowedTypeParams |\n  ts.TypeFormatFlags.UseAliasDefinedOutsideCurrentScope |\n  ts.TypeFormatFlags.AllowUniqueESSymbolType |\n  ts.TypeFormatFlags.WriteTypeArgumentsOfSignature;\n\nconst DECLARATION_OVERRIDE = `\nimport * as _gqlTada from 'gql.tada';\ndeclare module 'gql.tada' {\n  interface setupCache {\n    readonly __cacheDisabled: true;\n  }\n}\n`.trim();\n"], "names": ["runTurbo", "expose", "async", "_runTurbo", "params", "schemaNames", "getSchemaNamesFromConfig", "pluginConfig", "factory", "programFactory", "addSourceFile", "fileId", "sourceText", "DECLARATION_OVERRIDE", "script<PERSON>ind", "ts", "ScriptKind", "TS", "externalFiles", "createExternalFiles", "length", "kind", "addVirtualFiles", "container", "build", "pluginInfo", "buildPluginInfo", "sourceFiles", "getSourceFiles", "fileCount", "checker", "program", "getType<PERSON><PERSON>cker", "sourceFile", "filePath", "fileName", "documents", "warnings", "calls", "findAllCallExpressions", "nodes", "call", "callExpression", "node", "parent", "isCallExpression", "position", "getSourcePosition", "getStart", "has", "schema", "push", "message", "size", "file", "line", "col", "returnType", "getTypeAtLocation", "argumentType", "symbol", "getEscapedName", "<PERSON><PERSON><PERSON>", "value", "flags", "TypeFlags", "StringLiteral", "JSON", "stringify", "typeToString", "BUILDER_FLAGS", "documentType", "schemaName", "TypeFormatFlags", "NoTruncation", "NoTypeReduction", "InTypeAlias", "UseFullyQualifiedType", "GenerateNamesForShadowedTypeParams", "UseAliasDefinedOutsideCurrentScope", "AllowUniqueESSymbolType", "WriteTypeArgumentsOfSignature", "trim"], "mappings": ";;;;;;;;;;IAkHaA,IAAWC,EAAMA,QAjG9BC,gBAAgBC,UAAUC;EACxB,IAAMC,IAAcC,EAAAA,yBAAyBF,EAAOG;EACpD,IAAMC,IAAUC,iBAAeL;EAK/BI,EAAQE,cAAc;IACpBC,QAAQ;IACRC,YAAYC;IACZC,YAAYC,EAAGC,WAAWC;;EAG5B,IAAMC,IAAgBV,EAAQW;EAC9B,IAAID,EAAcE,QAAQ;UAClB;MAAEC,MAAM;;UACRb,EAAQc,gBAAgBJ;AAChC;EAEA,IAAMK,IAAYf,EAAQgB;EAC1B,IAAMC,IAAaF,EAAUG,gBAAgBtB,EAAOG;EACpD,IAAMoB,IAAcJ,EAAUK;QAExB;IACJP,MAAM;IACNQ,WAAWF,EAAYP;;EAGzB,IAAMU,IAAUP,EAAUQ,QAAQC;EAClC,KAAK,IAAMC,KAAcN,GAAa;IACpC,IAAIO,IAAWD,EAAWE;IAC1B,IAAMC,IAA6B;IACnC,IAAMC,IAA2B;IAEjC,IAAMC,IAAQC,EAAAA,uBAAuBN,GAAYR,IAAY,GAAOe;IACpE,KAAK,IAAMC,KAAQH,GAAO;MACxB,IAAMI,IAAiBD,EAAKE,KAAKC;MACjC,KAAK7B,EAAG8B,iBAAiBH;QACvB;;MAGF,IAAMI,IAAWvB,EAAUwB,kBAAkBd,GAAYS,EAAeM;MACxEd,IAAWY,EAASX;MACpB,KAAK9B,EAAY4C,IAAIR,EAAKS,SAAS;QACjCb,EAASc,KAAK;UACZC,SAASX,EAAKS,SACV,QAAQT,EAAKS,+EACb7C,EAAYgD,OAAO,IACjB,mFACA;UACNC,MAAMR,EAASX;UACfoB,MAAMT,EAASS;UACfC,KAAKV,EAASU;;QAEhB;AACF;MAEA,IAAMC,IAAa3B,EAAQ4B,kBAAkBhB;MAC7C,IAAMiB,IAAe7B,EAAQ4B,kBAAkBjB,EAAKE;MAGpD,KAAKc,EAAWG,UAAiD,uBAAvCH,EAAWG,OAAOC,kBAAyC;QACnFxB,EAASc,KAAK;UACZC,SACE;UAEFE,MAAMR,EAASX;UACfoB,MAAMT,EAASS;UACfC,KAAKV,EAASU;;QAEhB;AACF;MAEA,IAAMM,IACJ,WAAWH,KACmB,mBAAvBA,EAAaI,WACnBJ,EAAaK,QAAQjD,EAAGkD,UAAUC,iBAC/BC,KAAKC,UAAUT,EAAaI,SAC5BjC,EAAQuC,aAAaV,GAAcjB,GAAgB4B;MACzD,IAAMC,IAAezC,EAAQuC,aAAaZ,GAAYf,GAAgB4B;MAEtElC,EAAUe,KAAK;QACbqB,YAAY/B,EAAKS;QACjBY;QACAS;;AAEJ;UAEM;MACJlD,MAAM;MACNa;MACAE;MACAC;;AAEJ;AACF;;AAIA,IAAMiC,IACJvD,EAAG0D,gBAAgBC,eACnB3D,EAAG0D,gBAAgBE,kBACnB5D,EAAG0D,gBAAgBG,cACnB7D,EAAG0D,gBAAgBI,wBACnB9D,EAAG0D,gBAAgBK,qCACnB/D,EAAG0D,gBAAgBM,qCACnBhE,EAAG0D,gBAAgBO,0BACnBjE,EAAG0D,gBAAgBQ;;AAErB,IAAMpE,IAAuB,gJAO3BqE;;"}