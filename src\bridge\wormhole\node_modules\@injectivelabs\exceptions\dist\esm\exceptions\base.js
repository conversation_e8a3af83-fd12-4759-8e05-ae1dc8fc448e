/* eslint-disable class-methods-use-this */
import { ErrorType, UnspecifiedErrorCode, } from './types/index.js';
/**
 * we have to define it here as
 * well as in @injectivelabs/utils as that package is
 * importing the exceptions package
 * */
const toPascalCase = (str) => {
    return `${str}`
        .toLowerCase()
        .replace(new RegExp(/[-_]+/, 'g'), ' ')
        .replace(new RegExp(/[^\w\s]/, 'g'), '')
        .replace(new RegExp(/\s+(.)(\w*)/, 'g'), (_$1, $2, $3) => `${$2.toUpperCase() + $3}`)
        .replace(new RegExp(/\w/), (s) => s.toUpperCase());
};
export class ConcreteException extends Error {
    /**
     * The type of the Error
     */
    type = ErrorType.Unspecified;
    /**
     * Error specific code (HttpStatus, GrpcStatus, etc)
     */
    code = UnspecifiedErrorCode;
    /**
     * The name of the error (the name of the instance of the Exception)
     */
    name;
    /**
     * The name of the error (the name of the instance of the Exception)
     * Needed for reporting reasons, ex: bugsnag
     */
    errorClass;
    /**
     * Providing more context
     * (ex: endpoint on http request)
     */
    context;
    /**
     * Providing more context as to where the exception was thrown
     * (ex: on-chain module, etc)
     */
    contextModule;
    /**
     * Providing more context as to why the exception was thrown
     * (ex: on-chain error code, etc)
     */
    contextCode;
    /**
     * Parsed message of the exception
     */
    message = '';
    /**
     * The original stack of the error
     */
    stack = '';
    /**
     * The original message of the error
     */
    originalMessage = '';
    constructor(error, context) {
        super(error.message);
        this.parseError(error);
        this.parseContext(context);
        this.parse();
    }
    parse() {
        //
    }
    parseError(error) {
        this.setStack(error.stack || '');
        this.setMessage(error.message);
        this.originalMessage = error.message;
    }
    parseContext(errorContext) {
        const { contextModule, type, code, context } = errorContext || {
            contextModule: 'Unknown',
            context: 'Unknown',
            code: UnspecifiedErrorCode,
            type: ErrorType.Unspecified,
        };
        this.context = context;
        this.contextModule = contextModule;
        this.type = type || ErrorType.Unspecified;
        this.code = code || UnspecifiedErrorCode;
    }
    setType(type) {
        this.type = type;
    }
    setCode(code) {
        this.code = code;
    }
    setContext(context) {
        this.context = context;
    }
    setOriginalMessage(message) {
        this.originalMessage = message;
    }
    setStack(stack) {
        try {
            this.stack = stack;
        }
        catch (e) {
            // throw nothing here
        }
    }
    setName(name) {
        this.name = name;
        this.errorClass = name;
        super.name = name;
    }
    setMessage(message) {
        this.message = message;
        super.message = message;
    }
    setContextModule(contextModule) {
        this.contextModule = contextModule;
    }
    setContextCode(code) {
        this.contextCode = code;
    }
    toOriginalError() {
        const error = new Error(this.originalMessage);
        error.stack = this.stack;
        error.name = this.name || '';
        return error;
    }
    toError() {
        const error = new Error(this.message);
        error.stack = this.stack;
        error.name = this.name || '';
        return error;
    }
    toCompactError() {
        const name = this.name || toPascalCase(this.type);
        const error = new Error(`${this.message} | ${JSON.stringify({
            type: this.type,
            code: this.code,
            errorClass: name,
            message: this.message,
            context: this.context,
            contextCode: this.contextCode,
            contextModule: this.contextModule,
            originalMessage: this.originalMessage,
            stack: (this.stack || '').split('\n').map((line) => line.trim()),
        })}`);
        error.stack = this.stack;
        error.name = this.name || toPascalCase(this.type);
        return error;
    }
    toJson() {
        return JSON.stringify({ error: this.message, stack: this.stack });
    }
    toObject() {
        const name = this.name || toPascalCase(this.type);
        return {
            code: this.code,
            type: this.type,
            errorClass: name,
            message: this.message,
            context: this.context,
            contextCode: this.contextCode,
            contextModule: this.contextModule,
            originalMessage: this.originalMessage,
            stack: (this.stack || '').split('\n').map((line) => line.trim()),
        };
    }
    toString() {
        return this.message;
    }
}
