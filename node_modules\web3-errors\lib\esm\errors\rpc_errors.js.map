{"version": 3, "file": "rpc_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/rpc_errors.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AAKF,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EACN,sBAAsB,EACtB,qBAAqB,EACrB,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EACtB,uBAAuB,EACvB,sBAAsB,EACtB,wBAAwB,EACxB,qBAAqB,EACrB,4BAA4B,EAC5B,4BAA4B,EAC5B,0BAA0B,GAC1B,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,MAAM,yBAAyB,CAAC;AAE3F,MAAM,OAAO,QAAS,SAAQ,aAAa;IAK1C,YAAmB,QAAkC,EAAE,OAAgB;QACtE,KAAK,CACJ,OAAO,aAAP,OAAO,cAAP,OAAO,GACN,8BAA8B,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CACjF,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAChC,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC;IACpC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IAC5F,CAAC;CACD;AAED,MAAM,OAAO,uBAAwB,SAAQ,aAAa;IAIzD,YAAmB,IAAY,EAAE,IAAc;;QAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,sCAAsC;YACtC,KAAK,EAAE,CAAC;QACT,CAAC;aAAM,IAAI,MAAA,gBAAgB,CAAC,IAAI,CAAC,0CAAE,OAAO,EAAE,CAAC;YAC5C,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACP,gHAAgH;YAChH,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CACzD,UAAU,CAAC,EAAE,CACZ,OAAO,UAAU,KAAK,QAAQ;gBAC9B,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC9C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAC/C,CAAC;YACF,KAAK,CACJ,MAAA,MAAA,gBAAgB,CAAC,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,EAAE,CAAC,0CAAE,OAAO,mCAC/C,8BAA8B,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,mCAAI,IAAI,CAAC,CAC3E,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,CAAC;CACD;AAED,MAAM,OAAO,UAAW,SAAQ,QAAQ;IAEvC,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF1D,SAAI,GAAG,oBAAoB,CAAC;IAGnC,CAAC;CACD;AAED,MAAM,OAAO,mBAAoB,SAAQ,QAAQ;IAEhD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF7D,SAAI,GAAG,uBAAuB,CAAC;IAGtC,CAAC;CACD;AAED,MAAM,OAAO,mBAAoB,SAAQ,QAAQ;IAEhD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,sBAAsB,CAAC;IAGrC,CAAC;CACD;AAED,MAAM,OAAO,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,sBAAsB,CAAC;IAGrC,CAAC;CACD;AAED,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAE1C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,sBAAsB,CAAC;IAGrC,CAAC;CACD;AAED,MAAM,OAAO,iBAAkB,SAAQ,QAAQ;IAE9C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF3D,SAAI,GAAG,qBAAqB,CAAC;IAGpC,CAAC;CACD;AAED,MAAM,OAAO,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFhE,SAAI,GAAG,0BAA0B,CAAC;IAGzC,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFlE,SAAI,GAAG,4BAA4B,CAAC;IAG3C,CAAC;CACD;AAED,MAAM,OAAO,sBAAuB,SAAQ,QAAQ;IAEnD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF9D,SAAI,GAAG,wBAAwB,CAAC;IAGvC,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF3D,SAAI,GAAG,qBAAqB,CAAC;IAGpC,CAAC;CACD;AAED,MAAM,OAAO,wBAAyB,SAAQ,QAAQ;IAErD,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,OAAO,CAAC,CAAC;QAFlE,SAAI,GAAG,4BAA4B,CAAC;IAG3C,CAAC;CACD;AAED,MAAM,OAAO,kBAAmB,SAAQ,QAAQ;IAE/C,YAAmB,QAAkC;QACpD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAC,CAAC;QAF5D,SAAI,GAAG,sBAAsB,CAAC;IAGrC,CAAC;CACD;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAsC,CAAC;AAC1E,YAAY,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AAC9D,YAAY,CAAC,GAAG,CAAC,uBAAuB,EAAE;IACzC,KAAK,EAAE,mBAAmB;CAC1B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE;IACxC,KAAK,EAAE,mBAAmB;CAC1B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;AACxE,YAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC,CAAC;AACnE,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACtE,YAAY,CAAC,GAAG,CAAC,0BAA0B,EAAE;IAC5C,KAAK,EAAE,kBAAkB;CACzB,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,4BAA4B,EAAE;IAC9C,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,4BAA4B,EAAE;IAC9C,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,wBAAwB,EAAE;IAC1C,KAAK,EAAE,sBAAsB;CAC7B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,qBAAqB,EAAE;IACvC,KAAK,EAAE,wBAAwB;CAC/B,CAAC,CAAC;AACH,YAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC"}