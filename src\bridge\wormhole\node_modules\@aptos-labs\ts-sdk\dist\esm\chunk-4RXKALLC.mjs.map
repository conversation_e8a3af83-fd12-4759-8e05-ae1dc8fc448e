{"version": 3, "sources": ["../../src/utils/apiEndpoints.ts"], "sourcesContent": ["// Copyright © Aptos Foundation\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToIndexerAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/v1/graphql\",\n  testnet: \"https://api.testnet.aptoslabs.com/v1/graphql\",\n  devnet: \"https://api.devnet.aptoslabs.com/v1/graphql\",\n  local: \"http://127.0.0.1:8090/v1/graphql\",\n};\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToNodeAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/v1\",\n  testnet: \"https://api.testnet.aptoslabs.com/v1\",\n  devnet: \"https://api.devnet.aptoslabs.com/v1\",\n  local: \"http://127.0.0.1:8080/v1\",\n};\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToFaucetAPI: Record<string, string> = {\n  devnet: \"https://faucet.devnet.aptoslabs.com\",\n  local: \"http://127.0.0.1:8081\",\n};\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToPepperAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/keyless/pepper/v0\",\n  testnet: \"https://api.testnet.aptoslabs.com/keyless/pepper/v0\",\n  devnet: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\",\n  // Use the devnet service for local environment\n  local: \"https://api.devnet.aptoslabs.com/keyless/pepper/v0\",\n};\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToProverAPI: Record<string, string> = {\n  mainnet: \"https://api.mainnet.aptoslabs.com/keyless/prover/v0\",\n  testnet: \"https://api.testnet.aptoslabs.com/keyless/prover/v0\",\n  devnet: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\",\n  // Use the devnet service for local environment\n  local: \"https://api.devnet.aptoslabs.com/keyless/prover/v0\",\n};\n\n/**\n * Different network environments for connecting to services, ranging from production to development setups.\n * @group Implementation\n * @category Network\n */\nexport enum Network {\n  MAINNET = \"mainnet\",\n  TESTNET = \"testnet\",\n  DEVNET = \"devnet\",\n  LOCAL = \"local\",\n  CUSTOM = \"custom\",\n}\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToChainId: Record<string, number> = {\n  mainnet: 1,\n  testnet: 2,\n  local: 4,\n};\n\n/**\n * @group Implementation\n * @category Network\n */\nexport const NetworkToNetworkName: Record<string, Network> = {\n  mainnet: Network.MAINNET,\n  testnet: Network.TESTNET,\n  devnet: Network.DEVNET,\n  local: Network.LOCAL,\n  custom: Network.CUSTOM,\n};\n"], "mappings": "AAOO,IAAMA,EAA8C,CACzD,QAAS,+CACT,QAAS,+CACT,OAAQ,8CACR,MAAO,kCACT,EAMaC,EAA2C,CACtD,QAAS,uCACT,QAAS,uCACT,OAAQ,sCACR,MAAO,0BACT,EAMaC,EAA6C,CACxD,OAAQ,sCACR,MAAO,uBACT,EAMaC,EAA6C,CACxD,QAAS,sDACT,QAAS,sDACT,OAAQ,qDAER,MAAO,oDACT,EAMaC,EAA6C,CACxD,QAAS,sDACT,QAAS,sDACT,OAAQ,qDAER,MAAO,oDACT,EAOYC,OACVA,EAAA,QAAU,UACVA,EAAA,QAAU,UACVA,EAAA,OAAS,SACTA,EAAA,MAAQ,QACRA,EAAA,OAAS,SALCA,OAAA,IAYCC,EAA2C,CACtD,QAAS,EACT,QAAS,EACT,MAAO,CACT,EAMaC,EAAgD,CAC3D,QAAS,UACT,QAAS,UACT,OAAQ,SACR,MAAO,QACP,OAAQ,QACV", "names": ["NetworkToIndexerAPI", "NetworkToNodeAPI", "NetworkToFaucetAPI", "NetworkToPepperAPI", "NetworkToProverAPI", "Network", "NetworkToChainId", "NetworkToNetworkName"]}