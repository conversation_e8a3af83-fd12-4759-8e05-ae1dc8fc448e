{"version": 3, "sources": ["../../src/cli/move.ts"], "sourcesContent": ["import { spawn } from \"child_process\";\nimport { platform } from \"os\";\n\nimport { AccountAddress } from \"../core\";\nimport { Network } from \"../utils\";\n\n/**\n * Class representing a Move package management utility for the Aptos blockchain.\n * This class provides methods to initialize directories, compile packages, run tests, publish modules, create objects, upgrade\n * packages, build transaction payloads, and run scripts.\n * @group Implementation\n * @category CLI\n */\nexport class Move {\n  /**\n   * Initialize the current directory for Aptos by configuring the necessary settings.\n   * Configuration will be pushed into .aptos/config.yaml.\n   *\n   * @param args - The arguments for initialization.\n   * @param args.network - Optional Network type argument to use for default settings; defaults to local.\n   * @param args.profile - Optional Profile to use from the config file; defaults to 'default'. This will override associated\n   * settings such as the REST URL, the Faucet URL, and the private key arguments.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async init(args: {\n    network?: Network;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { network, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"init\", `--network=${network ?? \"local\"}`, `--profile=${profile ?? \"default\"}`];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Compile a Move package located at the specified directory path.\n   * This function helps in preparing the Move package for deployment or further processing.\n   *\n   * @param args - The arguments for compiling the package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async compile(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"compile\", \"--package-dir\", packageDirectoryPath];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Run Move unit tests for a specified package.\n   *\n   * @param args - The arguments for running the tests.\n   * @param args.packageDirectoryPath - The path to a Move package (the folder containing a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns The stdout output from running the tests.\n   * @group Implementation\n   * @category CLI\n   */\n  async test(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"test\", \"--package-dir\", packageDirectoryPath];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Publishes the modules to the publisher account on the Aptos blockchain.\n   *\n   * @param args - The arguments for publishing the modules.\n   * @param args.packageDirectoryPath - The path to a move package (the folder with a Move.toml file).\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async publish(args: {\n    packageDirectoryPath: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"publish\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Create a new object and publish the Move package to it on the Aptos blockchain.\n   *\n   * @param args - The arguments for creating the object and publishing the package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.addressName - Address name for the Move package.\n   * @param args.namedAddresses - Named addresses for the Move binary.\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns The object address.\n   *\n   * A complete example in CLI:\n   * aptos move create-object-and-publish-package \\\n   * --package-dir path_to_directory_that_has_move.toml \\\n   * --address-name launchpad_addr \\\n   * --named-addresses \"launchpad_addr=0x123,initial_creator_addr=0x456\" \\\n   * --profile my_profile \\\n   * --assume-yes\n   * @group Implementation\n   * @category CLI\n   */\n  async createObjectAndPublishPackage(args: {\n    packageDirectoryPath: string;\n    addressName: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ objectAddress: string }> {\n    const { packageDirectoryPath, addressName, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"create-object-and-publish-package\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      \"--address-name\",\n      addressName,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    const { output } = await this.runCommand(cliArgs, showStdout);\n    return { objectAddress: this.extractAddressFromOutput(output) };\n  }\n\n  /**\n   * Upgrade a Move package previously published to an object on the Aptos blockchain.\n   * The caller must be the object owner to execute this function.\n   *\n   * @param args - The arguments for upgrading the object package.\n   * @param args.packageDirectoryPath - Path to a Move package (the folder with a Move.toml file).\n   * @param args.objectAddress - Address of the object that the Move package published to. Ex. 0x1000\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async upgradeObjectPackage(args: {\n    packageDirectoryPath: string;\n    objectAddress: string;\n    namedAddresses: Record<string, AccountAddress>;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { packageDirectoryPath, objectAddress, namedAddresses, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"upgrade-object-package\",\n      \"--package-dir\",\n      packageDirectoryPath,\n      \"--object-address\",\n      objectAddress,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Build a publication transaction payload and store it in a JSON output file.\n   *\n   * @param args - The arguments for building the publishing payload.\n   * @param args.packageDirectoryPath - Path to a move package (the folder with a Move.toml file).\n   * @param args.outputFile - Output file to write the publication transaction to.\n   * @param args.namedAddresses - Named addresses for the move binary. Ex. { alice: 0x1234, bob: 0x5678 }\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]   *\n   * @returns stdout\n   * @group Implementation\n   * @category CLI\n   */\n  async buildPublishPayload(args: {\n    packageDirectoryPath: string;\n    outputFile: string;\n    namedAddresses: Record<string, AccountAddress>;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { outputFile, packageDirectoryPath, namedAddresses, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"build-publish-payload\",\n      \"--json-output-file\",\n      outputFile,\n      \"--package-dir\",\n      packageDirectoryPath,\n    ];\n\n    const addressesMap = this.parseNamedAddresses(namedAddresses);\n\n    cliArgs.push(...this.prepareNamedAddresses(addressesMap));\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Runs a Move script using the provided compiled script path and optional parameters. Ensure that the script is compiled\n   * before executing this function.\n   *\n   * @param args - The arguments for running the script.\n   * @param args.compiledScriptPath - Path to a compiled Move script bytecode file.\n   * Ex. \"build/my_package/bytecode_scripts/my_move_script.mv\"\n   * @param args.profile - Optional profile to use from the config file.\n   * @param args.extraArguments - Optional extra arguments to include in the form of an array of strings.\n   * Ex. [\"--assume-yes\",\"--gas-unit-price=10\"]\n   *\n   * @returns The standard output from running the script.\n   * @group Implementation\n   * @category CLI\n   */\n  async runScript(args: {\n    compiledScriptPath: string;\n    profile?: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string }> {\n    const { compiledScriptPath, profile, extraArguments, showStdout } = args;\n    const cliArgs = [\n      \"aptos\",\n      \"move\",\n      \"run-script\",\n      \"--compiled-script-path\",\n      compiledScriptPath,\n      `--profile=${profile ?? \"default\"}`,\n    ];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  async gasProfile(args: {\n    network: string;\n    transactionId: string;\n    extraArguments?: Array<string>;\n    showStdout?: boolean;\n  }): Promise<{ output: string; result?: any }> {\n    const { network, transactionId, extraArguments, showStdout } = args;\n    const cliArgs = [\"aptos\", \"move\", \"replay\", \"--profile-gas\", \"--network\", network, \"--txn-id\", transactionId];\n\n    if (extraArguments) {\n      cliArgs.push(...extraArguments);\n    }\n\n    return this.runCommand(cliArgs, showStdout);\n  }\n\n  /**\n   * Run a command with the specified arguments and return the output.\n   *\n   * @param args - An array of strings representing the command-line arguments to be passed to the command.\n   * @param showStdout - Show the standard output generated by the command.\n   * @returns The standard output generated by the command.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private async runCommand(args: Array<string>, showStdout: boolean = true): Promise<{ result?: any; output: string }> {\n    return new Promise((resolve, reject) => {\n      const currentPlatform = platform();\n      let childProcess;\n      let stdout = \"\";\n      // CLI final stdout is the Result/Error JSON string output\n      // so we need to keep track of the last stdout\n      let lastStdout = \"\";\n\n      // Check if current OS is windows\n      if (currentPlatform === \"win32\") {\n        childProcess = spawn(\"npx\", args, { shell: true });\n      } else {\n        childProcess = spawn(\"npx\", args);\n      }\n\n      childProcess.stdout.on(\"data\", (data) => {\n        lastStdout = data.toString();\n        stdout += data.toString();\n      });\n\n      if (showStdout) {\n        childProcess.stdout.pipe(process.stdout);\n        childProcess.stderr.pipe(process.stderr);\n      }\n      process.stdin.pipe(childProcess.stdin);\n\n      childProcess.on(\"close\", (code) => {\n        if (code === 0) {\n          try {\n            // parse the last stdout as it might be the result\n            const parsed = JSON.parse(lastStdout);\n            if (parsed.Error) {\n              reject(new Error(`Error: ${parsed.Error}`)); // Reject if the \"Error\" key exists\n            } else if (parsed.Result) {\n              resolve({ result: parsed.Result, output: stdout }); // Resolve if the \"Result\" key exists\n            }\n          } catch (error: any) {\n            // resolve the stdout as it might be just a stdout\n            resolve({ output: stdout });\n          }\n        } else {\n          reject(new Error(`Child process exited with code ${code}`)); // Reject with an error if the child process exits with an error code\n        }\n      });\n    });\n  }\n\n  /**\n   * Convert named addresses from a Map into an array separated by a comma.\n   *\n   * @param namedAddresses - A Map where the key is a string representing the name and the value is an AccountAddress.\n   * Ex. {'alice' => '0x123', 'bob' => '0x456'}\n   * @returns An array of named addresses formatted as strings separated by a comma. Ex. \"alice=0x123,bob=0x456\"\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private prepareNamedAddresses(namedAddresses: Map<string, AccountAddress>): Array<string> {\n    const totalNames = namedAddresses.size;\n    const newArgs: Array<string> = [];\n\n    if (totalNames === 0) {\n      return newArgs;\n    }\n\n    newArgs.push(\"--named-addresses\");\n\n    const names: Array<string> = [];\n    namedAddresses.forEach((value, key) => {\n      const toAppend = `${key}=${value.toString()}`;\n      names.push(toAppend);\n    });\n    newArgs.push(names.join(\",\"));\n    return newArgs;\n  }\n\n  /**\n   * Parse named addresses from a Record type into a Map.\n   *\n   * This function transforms a collection of named addresses into a more accessible format by mapping each name to its\n   * corresponding address.\n   *\n   * @param namedAddresses - A record containing named addresses where the key is the name and the value is the AccountAddress.\n   * @returns A Map where each key is a name and each value is the corresponding address.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private parseNamedAddresses(namedAddresses: Record<string, AccountAddress>): Map<string, AccountAddress> {\n    const addressesMap = new Map();\n\n    Object.keys(namedAddresses).forEach((key) => {\n      const address = namedAddresses[key];\n      addressesMap.set(key, address);\n    });\n\n    return addressesMap;\n  }\n\n  /**\n   * Extracts the object address from the provided output string.\n   *\n   * @param output - The output string containing the object address.\n   * @returns The extracted object address.\n   * @throws Error if the object address cannot be extracted from the output.\n   * @group Implementation\n   * @category CLI\n   */\n  // eslint-disable-next-line class-methods-use-this\n  private extractAddressFromOutput(output: string): string {\n    const match = output.match(\"Code was successfully deployed to object address (0x[0-9a-fA-F]+)\");\n    if (match) {\n      return match[1];\n    }\n    throw new Error(\"Failed to extract object address from output\");\n  }\n}\n"], "mappings": "AAAA,OAAS,SAAAA,MAAa,gBACtB,OAAS,YAAAC,MAAgB,KAYlB,IAAMC,EAAN,KAAW,CAehB,MAAM,KAAKC,EAKqB,CAC9B,GAAM,CAAE,QAAAC,EAAS,QAAAC,EAAS,eAAAC,EAAgB,WAAAC,CAAW,EAAIJ,EACnDK,EAAU,CAAC,QAAS,OAAQ,aAAaJ,GAAW,OAAO,GAAI,aAAaC,GAAW,SAAS,EAAE,EAExG,OAAIC,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAeA,MAAM,QAAQJ,EAKkB,CAC9B,GAAM,CAAE,qBAAAM,EAAsB,eAAAC,EAAgB,eAAAJ,EAAgB,WAAAC,CAAW,EAAIJ,EACvEK,EAAU,CAAC,QAAS,OAAQ,UAAW,gBAAiBC,CAAoB,EAE5EE,EAAe,KAAK,oBAAoBD,CAAc,EAE5D,OAAAF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAcA,MAAM,KAAKJ,EAKqB,CAC9B,GAAM,CAAE,qBAAAM,EAAsB,eAAAC,EAAgB,eAAAJ,EAAgB,WAAAC,CAAW,EAAIJ,EACvEK,EAAU,CAAC,QAAS,OAAQ,OAAQ,gBAAiBC,CAAoB,EAEzEE,EAAe,KAAK,oBAAoBD,CAAc,EAE5D,OAAAF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAeA,MAAM,QAAQJ,EAMkB,CAC9B,GAAM,CAAE,qBAAAM,EAAsB,eAAAC,EAAgB,QAAAL,EAAS,eAAAC,EAAgB,WAAAC,CAAW,EAAIJ,EAChFK,EAAU,CACd,QACA,OACA,UACA,gBACAC,EACA,aAAaJ,GAAW,SAAS,EACnC,EAEMM,EAAe,KAAK,oBAAoBD,CAAc,EAE5D,OAAAF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAwBA,MAAM,8BAA8BJ,EAOG,CACrC,GAAM,CAAE,qBAAAM,EAAsB,YAAAG,EAAa,eAAAF,EAAgB,QAAAL,EAAS,eAAAC,EAAgB,WAAAC,CAAW,EAAIJ,EAC7FK,EAAU,CACd,QACA,OACA,oCACA,gBACAC,EACA,iBACAG,EACA,aAAaP,GAAW,SAAS,EACnC,EAEMM,EAAe,KAAK,oBAAoBD,CAAc,EAE5DF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGhC,GAAM,CAAE,OAAAO,CAAO,EAAI,MAAM,KAAK,WAAWL,EAASD,CAAU,EAC5D,MAAO,CAAE,cAAe,KAAK,yBAAyBM,CAAM,CAAE,CAChE,CAiBA,MAAM,qBAAqBV,EAOK,CAC9B,GAAM,CAAE,qBAAAM,EAAsB,cAAAK,EAAe,eAAAJ,EAAgB,QAAAL,EAAS,eAAAC,EAAgB,WAAAC,CAAW,EAAIJ,EAC/FK,EAAU,CACd,QACA,OACA,yBACA,gBACAC,EACA,mBACAK,EACA,aAAaT,GAAW,SAAS,EACnC,EAEMM,EAAe,KAAK,oBAAoBD,CAAc,EAE5D,OAAAF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAeA,MAAM,oBAAoBJ,EAMM,CAC9B,GAAM,CAAE,WAAAY,EAAY,qBAAAN,EAAsB,eAAAC,EAAgB,eAAAJ,EAAgB,WAAAC,CAAW,EAAIJ,EACnFK,EAAU,CACd,QACA,OACA,wBACA,qBACAO,EACA,gBACAN,CACF,EAEME,EAAe,KAAK,oBAAoBD,CAAc,EAE5D,OAAAF,EAAQ,KAAK,GAAG,KAAK,sBAAsBG,CAAY,CAAC,EAEpDL,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAiBA,MAAM,UAAUJ,EAKgB,CAC9B,GAAM,CAAE,mBAAAa,EAAoB,QAAAX,EAAS,eAAAC,EAAgB,WAAAC,CAAW,EAAIJ,EAC9DK,EAAU,CACd,QACA,OACA,aACA,yBACAQ,EACA,aAAaX,GAAW,SAAS,EACnC,EAEA,OAAIC,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAEA,MAAM,WAAWJ,EAK6B,CAC5C,GAAM,CAAE,QAAAC,EAAS,cAAAa,EAAe,eAAAX,EAAgB,WAAAC,CAAW,EAAIJ,EACzDK,EAAU,CAAC,QAAS,OAAQ,SAAU,gBAAiB,YAAaJ,EAAS,WAAYa,CAAa,EAE5G,OAAIX,GACFE,EAAQ,KAAK,GAAGF,CAAc,EAGzB,KAAK,WAAWE,EAASD,CAAU,CAC5C,CAYA,MAAc,WAAWJ,EAAqBI,EAAsB,GAAiD,CACnH,OAAO,IAAI,QAAQ,CAACW,EAASC,IAAW,CACtC,IAAMC,EAAkBnB,EAAS,EAC7BoB,EACAC,EAAS,GAGTC,EAAa,GAGbH,IAAoB,QACtBC,EAAerB,EAAM,MAAOG,EAAM,CAAE,MAAO,EAAK,CAAC,EAEjDkB,EAAerB,EAAM,MAAOG,CAAI,EAGlCkB,EAAa,OAAO,GAAG,OAASG,GAAS,CACvCD,EAAaC,EAAK,SAAS,EAC3BF,GAAUE,EAAK,SAAS,CAC1B,CAAC,EAEGjB,IACFc,EAAa,OAAO,KAAK,QAAQ,MAAM,EACvCA,EAAa,OAAO,KAAK,QAAQ,MAAM,GAEzC,QAAQ,MAAM,KAAKA,EAAa,KAAK,EAErCA,EAAa,GAAG,QAAUI,GAAS,CACjC,GAAIA,IAAS,EACX,GAAI,CAEF,IAAMC,EAAS,KAAK,MAAMH,CAAU,EAChCG,EAAO,MACTP,EAAO,IAAI,MAAM,UAAUO,EAAO,KAAK,EAAE,CAAC,EACjCA,EAAO,QAChBR,EAAQ,CAAE,OAAQQ,EAAO,OAAQ,OAAQJ,CAAO,CAAC,CAErD,MAAqB,CAEnBJ,EAAQ,CAAE,OAAQI,CAAO,CAAC,CAC5B,MAEAH,EAAO,IAAI,MAAM,kCAAkCM,CAAI,EAAE,CAAC,CAE9D,CAAC,CACH,CAAC,CACH,CAYQ,sBAAsBf,EAA4D,CACxF,IAAMiB,EAAajB,EAAe,KAC5BkB,EAAyB,CAAC,EAEhC,GAAID,IAAe,EACjB,OAAOC,EAGTA,EAAQ,KAAK,mBAAmB,EAEhC,IAAMC,EAAuB,CAAC,EAC9B,OAAAnB,EAAe,QAAQ,CAACoB,EAAOC,IAAQ,CACrC,IAAMC,EAAW,GAAGD,CAAG,IAAID,EAAM,SAAS,CAAC,GAC3CD,EAAM,KAAKG,CAAQ,CACrB,CAAC,EACDJ,EAAQ,KAAKC,EAAM,KAAK,GAAG,CAAC,EACrBD,CACT,CAcQ,oBAAoBlB,EAA6E,CACvG,IAAMC,EAAe,IAAI,IAEzB,cAAO,KAAKD,CAAc,EAAE,QAASqB,GAAQ,CAC3C,IAAME,EAAUvB,EAAeqB,CAAG,EAClCpB,EAAa,IAAIoB,EAAKE,CAAO,CAC/B,CAAC,EAEMtB,CACT,CAYQ,yBAAyBE,EAAwB,CACvD,IAAMqB,EAAQrB,EAAO,MAAM,mEAAmE,EAC9F,GAAIqB,EACF,OAAOA,EAAM,CAAC,EAEhB,MAAM,IAAI,MAAM,8CAA8C,CAChE,CACF", "names": ["spawn", "platform", "Move", "args", "network", "profile", "extraArguments", "showStdout", "cli<PERSON><PERSON>s", "packageDirectoryPath", "named<PERSON><PERSON><PERSON>", "addressesMap", "addressName", "output", "objectAddress", "outputFile", "compiledScriptPath", "transactionId", "resolve", "reject", "currentPlatform", "childProcess", "stdout", "lastStdout", "data", "code", "parsed", "totalNames", "newArgs", "names", "value", "key", "toAppend", "address", "match"]}