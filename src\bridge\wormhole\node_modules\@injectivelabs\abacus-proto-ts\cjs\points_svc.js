"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointsSvcPointsStatsWeeklyForAccountDesc = exports.PointsSvcPointsStatsDailyForAccountDesc = exports.PointsSvcPointsLatestForAccountDesc = exports.PointsSvcDesc = exports.PointsSvcClientImpl = exports.PointsReprocessTradesAfterDateResponse = exports.PointsReprocessTradesAfterDateRequest = exports.PointsDeleteAdminCorrectionResponse = exports.PointsDeleteAdminCorrectionRequest = exports.PointsCreateAdminCorrectionResponse = exports.PointsCreateAdminCorrectionRequest = exports.AdminPointsCorrection = exports.AdminPointsCorrectionCollection = exports.PointsListAdminCorrectionsRequest = exports.PointsDeleteMultiplierConfigResponse = exports.PointsDeleteMultiplierConfigRequest = exports.PointsCreateMultiplierConfigResponse = exports.PointsCreateMultiplierConfigRequest = exports.PointsMultiplierConfig = exports.PointsMultiplierConfigCollection = exports.PointsListMultiplierConfigsRequest = exports.PointsDeleteBanConfigResponse = exports.PointsDeleteBanConfigRequest = exports.PointsCreateBanConfigRequest = exports.BanConfig = exports.BanConfigCollection = exports.PointsListBanConfigsRequest = exports.PointsSetEmissionConfigResponse = exports.PointsSetEmissionConfigRequest = exports.ModelVolumeTierConfig = exports.PointsGetEmissionConfigResponse = exports.PointsGetEmissionConfigRequest = exports.PointsSetLeagueConfigResponse = exports.PointsSetLeagueConfigRequest = exports.PointsGetLeagueConfigResponse = exports.PointsGetLeagueConfigRequest = exports.ModelPointsMultiplier = exports.ModelEffectiveVolumeTier = exports.PointsSimulateAllocationResponse = exports.PointsSimulateAllocationRequest = exports.LeaderboardPointsRow = exports.LeaderboardPointsRowCollection = exports.PointsLeaderboardRequest = exports.PointsStatsWeeklyForAccountRequest = exports.HistoricalPointsStatsRow = exports.HistoricalPointsStatsRowCollection = exports.PointsStatsDailyForAccountRequest = exports.PointsLatestForAccountResponse = exports.PointsLatestForAccountRequest = exports.protobufPackage = void 0;
exports.GrpcWebError = exports.GrpcWebImpl = exports.PointsSvcPointsReprocessTradesAfterDateDesc = exports.PointsSvcPointsDeleteAdminCorrectionDesc = exports.PointsSvcPointsCreateAdminCorrectionDesc = exports.PointsSvcPointsListAdminCorrectionsDesc = exports.PointsSvcPointsDeleteMultiplierConfigDesc = exports.PointsSvcPointsCreateMultiplierConfigDesc = exports.PointsSvcPointsListMultiplierConfigsDesc = exports.PointsSvcPointsDeleteBanConfigDesc = exports.PointsSvcPointsCreateBanConfigDesc = exports.PointsSvcPointsListBanConfigsDesc = exports.PointsSvcPointsSetEmissionConfigDesc = exports.PointsSvcPointsGetEmissionConfigDesc = exports.PointsSvcPointsSetLeagueConfigDesc = exports.PointsSvcPointsGetLeagueConfigDesc = exports.PointsSvcPointsSimulateAllocationDesc = exports.PointsSvcPointsLeaderboardDesc = void 0;
/* eslint-disable */
const grpc_web_1 = require("@injectivelabs/grpc-web");
const browser_headers_1 = require("browser-headers");
const long_1 = __importDefault(require("long"));
const minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
exports.protobufPackage = "points_svc";
function createBasePointsLatestForAccountRequest() {
    return { accountAddress: "" };
}
exports.PointsLatestForAccountRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLatestForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create(base) {
        return exports.PointsLatestForAccountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsLatestForAccountRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBasePointsLatestForAccountResponse() {
    return { rank: "0", totalPoints: "0", totalPointsPrecise: 0, league: "", updatedAt: "" };
}
exports.PointsLatestForAccountResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.rank !== "0") {
            writer.uint32(8).uint64(message.rank);
        }
        if (message.totalPoints !== "0") {
            writer.uint32(16).uint64(message.totalPoints);
        }
        if (message.totalPointsPrecise !== 0) {
            writer.uint32(25).double(message.totalPointsPrecise);
        }
        if (message.league !== "") {
            writer.uint32(34).string(message.league);
        }
        if (message.updatedAt !== "") {
            writer.uint32(42).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLatestForAccountResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rank = longToString(reader.uint64());
                    break;
                case 2:
                    message.totalPoints = longToString(reader.uint64());
                    break;
                case 3:
                    message.totalPointsPrecise = reader.double();
                    break;
                case 4:
                    message.league = reader.string();
                    break;
                case 5:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rank: isSet(object.rank) ? String(object.rank) : "0",
            totalPoints: isSet(object.totalPoints) ? String(object.totalPoints) : "0",
            totalPointsPrecise: isSet(object.totalPointsPrecise) ? Number(object.totalPointsPrecise) : 0,
            league: isSet(object.league) ? String(object.league) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.rank !== undefined && (obj.rank = message.rank);
        message.totalPoints !== undefined && (obj.totalPoints = message.totalPoints);
        message.totalPointsPrecise !== undefined && (obj.totalPointsPrecise = message.totalPointsPrecise);
        message.league !== undefined && (obj.league = message.league);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsLatestForAccountResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBasePointsLatestForAccountResponse();
        message.rank = (_a = object.rank) !== null && _a !== void 0 ? _a : "0";
        message.totalPoints = (_b = object.totalPoints) !== null && _b !== void 0 ? _b : "0";
        message.totalPointsPrecise = (_c = object.totalPointsPrecise) !== null && _c !== void 0 ? _c : 0;
        message.league = (_d = object.league) !== null && _d !== void 0 ? _d : "";
        message.updatedAt = (_e = object.updatedAt) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBasePointsStatsDailyForAccountRequest() {
    return { accountAddress: "", daysLimit: undefined };
}
exports.PointsStatsDailyForAccountRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.daysLimit !== undefined) {
            writer.uint32(16).uint64(message.daysLimit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsStatsDailyForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.daysLimit = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            daysLimit: isSet(object.daysLimit) ? String(object.daysLimit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.daysLimit !== undefined && (obj.daysLimit = message.daysLimit);
        return obj;
    },
    create(base) {
        return exports.PointsStatsDailyForAccountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBasePointsStatsDailyForAccountRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.daysLimit = (_b = object.daysLimit) !== null && _b !== void 0 ? _b : undefined;
        return message;
    },
};
function createBaseHistoricalPointsStatsRowCollection() {
    return { rows: [] };
}
exports.HistoricalPointsStatsRowCollection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.rows) {
            exports.HistoricalPointsStatsRow.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalPointsStatsRowCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rows.push(exports.HistoricalPointsStatsRow.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rows: Array.isArray(object === null || object === void 0 ? void 0 : object.rows) ? object.rows.map((e) => exports.HistoricalPointsStatsRow.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.rows) {
            obj.rows = message.rows.map((e) => e ? exports.HistoricalPointsStatsRow.toJSON(e) : undefined);
        }
        else {
            obj.rows = [];
        }
        return obj;
    },
    create(base) {
        return exports.HistoricalPointsStatsRowCollection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseHistoricalPointsStatsRowCollection();
        message.rows = ((_a = object.rows) === null || _a === void 0 ? void 0 : _a.map((e) => exports.HistoricalPointsStatsRow.fromPartial(e))) || [];
        return message;
    },
};
function createBaseHistoricalPointsStatsRow() {
    return { week: "", day: undefined, points: "0", pointsPrecise: 0, volume: 0 };
}
exports.HistoricalPointsStatsRow = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.week !== "") {
            writer.uint32(10).string(message.week);
        }
        if (message.day !== undefined) {
            writer.uint32(18).string(message.day);
        }
        if (message.points !== "0") {
            writer.uint32(24).uint64(message.points);
        }
        if (message.pointsPrecise !== 0) {
            writer.uint32(33).double(message.pointsPrecise);
        }
        if (message.volume !== 0) {
            writer.uint32(41).double(message.volume);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseHistoricalPointsStatsRow();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.week = reader.string();
                    break;
                case 2:
                    message.day = reader.string();
                    break;
                case 3:
                    message.points = longToString(reader.uint64());
                    break;
                case 4:
                    message.pointsPrecise = reader.double();
                    break;
                case 5:
                    message.volume = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            week: isSet(object.week) ? String(object.week) : "",
            day: isSet(object.day) ? String(object.day) : undefined,
            points: isSet(object.points) ? String(object.points) : "0",
            pointsPrecise: isSet(object.pointsPrecise) ? Number(object.pointsPrecise) : 0,
            volume: isSet(object.volume) ? Number(object.volume) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.week !== undefined && (obj.week = message.week);
        message.day !== undefined && (obj.day = message.day);
        message.points !== undefined && (obj.points = message.points);
        message.pointsPrecise !== undefined && (obj.pointsPrecise = message.pointsPrecise);
        message.volume !== undefined && (obj.volume = message.volume);
        return obj;
    },
    create(base) {
        return exports.HistoricalPointsStatsRow.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseHistoricalPointsStatsRow();
        message.week = (_a = object.week) !== null && _a !== void 0 ? _a : "";
        message.day = (_b = object.day) !== null && _b !== void 0 ? _b : undefined;
        message.points = (_c = object.points) !== null && _c !== void 0 ? _c : "0";
        message.pointsPrecise = (_d = object.pointsPrecise) !== null && _d !== void 0 ? _d : 0;
        message.volume = (_e = object.volume) !== null && _e !== void 0 ? _e : 0;
        return message;
    },
};
function createBasePointsStatsWeeklyForAccountRequest() {
    return { accountAddress: "", weeksLimit: undefined };
}
exports.PointsStatsWeeklyForAccountRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.weeksLimit !== undefined) {
            writer.uint32(16).uint64(message.weeksLimit);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsStatsWeeklyForAccountRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.weeksLimit = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            weeksLimit: isSet(object.weeksLimit) ? String(object.weeksLimit) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.weeksLimit !== undefined && (obj.weeksLimit = message.weeksLimit);
        return obj;
    },
    create(base) {
        return exports.PointsStatsWeeklyForAccountRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBasePointsStatsWeeklyForAccountRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.weeksLimit = (_b = object.weeksLimit) !== null && _b !== void 0 ? _b : undefined;
        return message;
    },
};
function createBasePointsLeaderboardRequest() {
    return {};
}
exports.PointsLeaderboardRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsLeaderboardRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsLeaderboardRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsLeaderboardRequest();
        return message;
    },
};
function createBaseLeaderboardPointsRowCollection() {
    return { rows: [] };
}
exports.LeaderboardPointsRowCollection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.rows) {
            exports.LeaderboardPointsRow.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLeaderboardPointsRowCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rows.push(exports.LeaderboardPointsRow.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { rows: Array.isArray(object === null || object === void 0 ? void 0 : object.rows) ? object.rows.map((e) => exports.LeaderboardPointsRow.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.rows) {
            obj.rows = message.rows.map((e) => e ? exports.LeaderboardPointsRow.toJSON(e) : undefined);
        }
        else {
            obj.rows = [];
        }
        return obj;
    },
    create(base) {
        return exports.LeaderboardPointsRowCollection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseLeaderboardPointsRowCollection();
        message.rows = ((_a = object.rows) === null || _a === void 0 ? void 0 : _a.map((e) => exports.LeaderboardPointsRow.fromPartial(e))) || [];
        return message;
    },
};
function createBaseLeaderboardPointsRow() {
    return { rank: "0", totalRank: "0", accountAddress: "", points: 0, league: "" };
}
exports.LeaderboardPointsRow = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.rank !== "0") {
            writer.uint32(8).uint64(message.rank);
        }
        if (message.totalRank !== "0") {
            writer.uint32(16).uint64(message.totalRank);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.points !== 0) {
            writer.uint32(33).double(message.points);
        }
        if (message.league !== "") {
            writer.uint32(50).string(message.league);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLeaderboardPointsRow();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rank = longToString(reader.uint64());
                    break;
                case 2:
                    message.totalRank = longToString(reader.uint64());
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.points = reader.double();
                    break;
                case 6:
                    message.league = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rank: isSet(object.rank) ? String(object.rank) : "0",
            totalRank: isSet(object.totalRank) ? String(object.totalRank) : "0",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            points: isSet(object.points) ? Number(object.points) : 0,
            league: isSet(object.league) ? String(object.league) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.rank !== undefined && (obj.rank = message.rank);
        message.totalRank !== undefined && (obj.totalRank = message.totalRank);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.points !== undefined && (obj.points = message.points);
        message.league !== undefined && (obj.league = message.league);
        return obj;
    },
    create(base) {
        return exports.LeaderboardPointsRow.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBaseLeaderboardPointsRow();
        message.rank = (_a = object.rank) !== null && _a !== void 0 ? _a : "0";
        message.totalRank = (_b = object.totalRank) !== null && _b !== void 0 ? _b : "0";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.points = (_d = object.points) !== null && _d !== void 0 ? _d : 0;
        message.league = (_e = object.league) !== null && _e !== void 0 ? _e : "";
        return message;
    },
};
function createBasePointsSimulateAllocationRequest() {
    return {
        account: "",
        marketId: "",
        tradeDirection: "",
        executionSide: "",
        usdValue: 0,
        flags: [],
        marketType: "",
        timestamp: "0",
        priorDailyVolume: undefined,
    };
}
exports.PointsSimulateAllocationRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.account !== "") {
            writer.uint32(10).string(message.account);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.tradeDirection !== "") {
            writer.uint32(26).string(message.tradeDirection);
        }
        if (message.executionSide !== "") {
            writer.uint32(34).string(message.executionSide);
        }
        if (message.usdValue !== 0) {
            writer.uint32(41).double(message.usdValue);
        }
        for (const v of message.flags) {
            writer.uint32(50).string(v);
        }
        if (message.marketType !== "") {
            writer.uint32(58).string(message.marketType);
        }
        if (message.timestamp !== "0") {
            writer.uint32(64).uint64(message.timestamp);
        }
        if (message.priorDailyVolume !== undefined) {
            writer.uint32(73).double(message.priorDailyVolume);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSimulateAllocationRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.account = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.tradeDirection = reader.string();
                    break;
                case 4:
                    message.executionSide = reader.string();
                    break;
                case 5:
                    message.usdValue = reader.double();
                    break;
                case 6:
                    message.flags.push(reader.string());
                    break;
                case 7:
                    message.marketType = reader.string();
                    break;
                case 8:
                    message.timestamp = longToString(reader.uint64());
                    break;
                case 9:
                    message.priorDailyVolume = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            account: isSet(object.account) ? String(object.account) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            tradeDirection: isSet(object.tradeDirection) ? String(object.tradeDirection) : "",
            executionSide: isSet(object.executionSide) ? String(object.executionSide) : "",
            usdValue: isSet(object.usdValue) ? Number(object.usdValue) : 0,
            flags: Array.isArray(object === null || object === void 0 ? void 0 : object.flags) ? object.flags.map((e) => String(e)) : [],
            marketType: isSet(object.marketType) ? String(object.marketType) : "",
            timestamp: isSet(object.timestamp) ? String(object.timestamp) : "0",
            priorDailyVolume: isSet(object.priorDailyVolume) ? Number(object.priorDailyVolume) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.account !== undefined && (obj.account = message.account);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.tradeDirection !== undefined && (obj.tradeDirection = message.tradeDirection);
        message.executionSide !== undefined && (obj.executionSide = message.executionSide);
        message.usdValue !== undefined && (obj.usdValue = message.usdValue);
        if (message.flags) {
            obj.flags = message.flags.map((e) => e);
        }
        else {
            obj.flags = [];
        }
        message.marketType !== undefined && (obj.marketType = message.marketType);
        message.timestamp !== undefined && (obj.timestamp = message.timestamp);
        message.priorDailyVolume !== undefined && (obj.priorDailyVolume = message.priorDailyVolume);
        return obj;
    },
    create(base) {
        return exports.PointsSimulateAllocationRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBasePointsSimulateAllocationRequest();
        message.account = (_a = object.account) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.tradeDirection = (_c = object.tradeDirection) !== null && _c !== void 0 ? _c : "";
        message.executionSide = (_d = object.executionSide) !== null && _d !== void 0 ? _d : "";
        message.usdValue = (_e = object.usdValue) !== null && _e !== void 0 ? _e : 0;
        message.flags = ((_f = object.flags) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.marketType = (_g = object.marketType) !== null && _g !== void 0 ? _g : "";
        message.timestamp = (_h = object.timestamp) !== null && _h !== void 0 ? _h : "0";
        message.priorDailyVolume = (_j = object.priorDailyVolume) !== null && _j !== void 0 ? _j : undefined;
        return message;
    },
};
function createBasePointsSimulateAllocationResponse() {
    return {
        pointsEmitted: 0,
        pointsBreakdown: "",
        priorDailyVolume: 0,
        effectiveVolumeTiers: [],
        effectiveMultipliers: [],
    };
}
exports.PointsSimulateAllocationResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.pointsEmitted !== 0) {
            writer.uint32(9).double(message.pointsEmitted);
        }
        if (message.pointsBreakdown !== "") {
            writer.uint32(18).string(message.pointsBreakdown);
        }
        if (message.priorDailyVolume !== 0) {
            writer.uint32(25).double(message.priorDailyVolume);
        }
        for (const v of message.effectiveVolumeTiers) {
            exports.ModelEffectiveVolumeTier.encode(v, writer.uint32(34).fork()).ldelim();
        }
        for (const v of message.effectiveMultipliers) {
            exports.ModelPointsMultiplier.encode(v, writer.uint32(42).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSimulateAllocationResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.pointsEmitted = reader.double();
                    break;
                case 2:
                    message.pointsBreakdown = reader.string();
                    break;
                case 3:
                    message.priorDailyVolume = reader.double();
                    break;
                case 4:
                    message.effectiveVolumeTiers.push(exports.ModelEffectiveVolumeTier.decode(reader, reader.uint32()));
                    break;
                case 5:
                    message.effectiveMultipliers.push(exports.ModelPointsMultiplier.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            pointsEmitted: isSet(object.pointsEmitted) ? Number(object.pointsEmitted) : 0,
            pointsBreakdown: isSet(object.pointsBreakdown) ? String(object.pointsBreakdown) : "",
            priorDailyVolume: isSet(object.priorDailyVolume) ? Number(object.priorDailyVolume) : 0,
            effectiveVolumeTiers: Array.isArray(object === null || object === void 0 ? void 0 : object.effectiveVolumeTiers)
                ? object.effectiveVolumeTiers.map((e) => exports.ModelEffectiveVolumeTier.fromJSON(e))
                : [],
            effectiveMultipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.effectiveMultipliers)
                ? object.effectiveMultipliers.map((e) => exports.ModelPointsMultiplier.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.pointsEmitted !== undefined && (obj.pointsEmitted = message.pointsEmitted);
        message.pointsBreakdown !== undefined && (obj.pointsBreakdown = message.pointsBreakdown);
        message.priorDailyVolume !== undefined && (obj.priorDailyVolume = message.priorDailyVolume);
        if (message.effectiveVolumeTiers) {
            obj.effectiveVolumeTiers = message.effectiveVolumeTiers.map((e) => e ? exports.ModelEffectiveVolumeTier.toJSON(e) : undefined);
        }
        else {
            obj.effectiveVolumeTiers = [];
        }
        if (message.effectiveMultipliers) {
            obj.effectiveMultipliers = message.effectiveMultipliers.map((e) => e ? exports.ModelPointsMultiplier.toJSON(e) : undefined);
        }
        else {
            obj.effectiveMultipliers = [];
        }
        return obj;
    },
    create(base) {
        return exports.PointsSimulateAllocationResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBasePointsSimulateAllocationResponse();
        message.pointsEmitted = (_a = object.pointsEmitted) !== null && _a !== void 0 ? _a : 0;
        message.pointsBreakdown = (_b = object.pointsBreakdown) !== null && _b !== void 0 ? _b : "";
        message.priorDailyVolume = (_c = object.priorDailyVolume) !== null && _c !== void 0 ? _c : 0;
        message.effectiveVolumeTiers = ((_d = object.effectiveVolumeTiers) === null || _d === void 0 ? void 0 : _d.map((e) => exports.ModelEffectiveVolumeTier.fromPartial(e))) ||
            [];
        message.effectiveMultipliers = ((_e = object.effectiveMultipliers) === null || _e === void 0 ? void 0 : _e.map((e) => exports.ModelPointsMultiplier.fromPartial(e))) || [];
        return message;
    },
};
function createBaseModelEffectiveVolumeTier() {
    return { tierNumber: 0, upperThreshold: 0, takerRate: 0, makerRate: 0, effectiveVolume: 0, effectiveRate: 0 };
}
exports.ModelEffectiveVolumeTier = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.tierNumber !== 0) {
            writer.uint32(8).uint32(message.tierNumber);
        }
        if (message.upperThreshold !== 0) {
            writer.uint32(17).double(message.upperThreshold);
        }
        if (message.takerRate !== 0) {
            writer.uint32(25).double(message.takerRate);
        }
        if (message.makerRate !== 0) {
            writer.uint32(33).double(message.makerRate);
        }
        if (message.effectiveVolume !== 0) {
            writer.uint32(41).double(message.effectiveVolume);
        }
        if (message.effectiveRate !== 0) {
            writer.uint32(49).double(message.effectiveRate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelEffectiveVolumeTier();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tierNumber = reader.uint32();
                    break;
                case 2:
                    message.upperThreshold = reader.double();
                    break;
                case 3:
                    message.takerRate = reader.double();
                    break;
                case 4:
                    message.makerRate = reader.double();
                    break;
                case 5:
                    message.effectiveVolume = reader.double();
                    break;
                case 6:
                    message.effectiveRate = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            tierNumber: isSet(object.tierNumber) ? Number(object.tierNumber) : 0,
            upperThreshold: isSet(object.upperThreshold) ? Number(object.upperThreshold) : 0,
            takerRate: isSet(object.takerRate) ? Number(object.takerRate) : 0,
            makerRate: isSet(object.makerRate) ? Number(object.makerRate) : 0,
            effectiveVolume: isSet(object.effectiveVolume) ? Number(object.effectiveVolume) : 0,
            effectiveRate: isSet(object.effectiveRate) ? Number(object.effectiveRate) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.tierNumber !== undefined && (obj.tierNumber = Math.round(message.tierNumber));
        message.upperThreshold !== undefined && (obj.upperThreshold = message.upperThreshold);
        message.takerRate !== undefined && (obj.takerRate = message.takerRate);
        message.makerRate !== undefined && (obj.makerRate = message.makerRate);
        message.effectiveVolume !== undefined && (obj.effectiveVolume = message.effectiveVolume);
        message.effectiveRate !== undefined && (obj.effectiveRate = message.effectiveRate);
        return obj;
    },
    create(base) {
        return exports.ModelEffectiveVolumeTier.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseModelEffectiveVolumeTier();
        message.tierNumber = (_a = object.tierNumber) !== null && _a !== void 0 ? _a : 0;
        message.upperThreshold = (_b = object.upperThreshold) !== null && _b !== void 0 ? _b : 0;
        message.takerRate = (_c = object.takerRate) !== null && _c !== void 0 ? _c : 0;
        message.makerRate = (_d = object.makerRate) !== null && _d !== void 0 ? _d : 0;
        message.effectiveVolume = (_e = object.effectiveVolume) !== null && _e !== void 0 ? _e : 0;
        message.effectiveRate = (_f = object.effectiveRate) !== null && _f !== void 0 ? _f : 0;
        return message;
    },
};
function createBaseModelPointsMultiplier() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
    };
}
exports.ModelPointsMultiplier = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(58).string(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(66).string(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelPointsMultiplier();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = reader.string();
                    break;
                case 8:
                    message.effectiveDateEnd = reader.string();
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object === null || object === void 0 ? void 0 : object.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object === null || object === void 0 ? void 0 : object.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        return obj;
    },
    create(base) {
        return exports.ModelPointsMultiplier.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBaseModelPointsMultiplier();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.multiplier = (_c = object.multiplier) !== null && _c !== void 0 ? _c : 0;
        message.affectedUsers = ((_d = object.affectedUsers) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.affectedMarkets = ((_e = object.affectedMarkets) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        message.allMarketsExcept = ((_f = object.allMarketsExcept) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.effectiveDateStart = (_g = object.effectiveDateStart) !== null && _g !== void 0 ? _g : undefined;
        message.effectiveDateEnd = (_h = object.effectiveDateEnd) !== null && _h !== void 0 ? _h : undefined;
        message.effectiveFlags = ((_j = object.effectiveFlags) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        return message;
    },
};
function createBasePointsGetLeagueConfigRequest() {
    return {};
}
exports.PointsGetLeagueConfigRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetLeagueConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsGetLeagueConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsGetLeagueConfigRequest();
        return message;
    },
};
function createBasePointsGetLeagueConfigResponse() {
    return {
        whiteThreshold: 0,
        orangeThreshold: 0,
        blueThreshold: 0,
        purpleThreshold: 0,
        blackThreshold: 0,
        updatedBy: "",
        updatedAt: "",
    };
}
exports.PointsGetLeagueConfigResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        if (message.updatedBy !== "") {
            writer.uint32(50).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(58).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetLeagueConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                case 6:
                    message.updatedBy = reader.string();
                    break;
                case 7:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsGetLeagueConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBasePointsGetLeagueConfigResponse();
        message.whiteThreshold = (_a = object.whiteThreshold) !== null && _a !== void 0 ? _a : 0;
        message.orangeThreshold = (_b = object.orangeThreshold) !== null && _b !== void 0 ? _b : 0;
        message.blueThreshold = (_c = object.blueThreshold) !== null && _c !== void 0 ? _c : 0;
        message.purpleThreshold = (_d = object.purpleThreshold) !== null && _d !== void 0 ? _d : 0;
        message.blackThreshold = (_e = object.blackThreshold) !== null && _e !== void 0 ? _e : 0;
        message.updatedBy = (_f = object.updatedBy) !== null && _f !== void 0 ? _f : "";
        message.updatedAt = (_g = object.updatedAt) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBasePointsSetLeagueConfigRequest() {
    return { whiteThreshold: 0, orangeThreshold: 0, blueThreshold: 0, purpleThreshold: 0, blackThreshold: 0 };
}
exports.PointsSetLeagueConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetLeagueConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        return obj;
    },
    create(base) {
        return exports.PointsSetLeagueConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBasePointsSetLeagueConfigRequest();
        message.whiteThreshold = (_a = object.whiteThreshold) !== null && _a !== void 0 ? _a : 0;
        message.orangeThreshold = (_b = object.orangeThreshold) !== null && _b !== void 0 ? _b : 0;
        message.blueThreshold = (_c = object.blueThreshold) !== null && _c !== void 0 ? _c : 0;
        message.purpleThreshold = (_d = object.purpleThreshold) !== null && _d !== void 0 ? _d : 0;
        message.blackThreshold = (_e = object.blackThreshold) !== null && _e !== void 0 ? _e : 0;
        return message;
    },
};
function createBasePointsSetLeagueConfigResponse() {
    return {
        whiteThreshold: 0,
        orangeThreshold: 0,
        blueThreshold: 0,
        purpleThreshold: 0,
        blackThreshold: 0,
        updatedBy: "",
        updatedAt: "",
    };
}
exports.PointsSetLeagueConfigResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.whiteThreshold !== 0) {
            writer.uint32(9).double(message.whiteThreshold);
        }
        if (message.orangeThreshold !== 0) {
            writer.uint32(17).double(message.orangeThreshold);
        }
        if (message.blueThreshold !== 0) {
            writer.uint32(25).double(message.blueThreshold);
        }
        if (message.purpleThreshold !== 0) {
            writer.uint32(33).double(message.purpleThreshold);
        }
        if (message.blackThreshold !== 0) {
            writer.uint32(41).double(message.blackThreshold);
        }
        if (message.updatedBy !== "") {
            writer.uint32(50).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(58).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetLeagueConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.whiteThreshold = reader.double();
                    break;
                case 2:
                    message.orangeThreshold = reader.double();
                    break;
                case 3:
                    message.blueThreshold = reader.double();
                    break;
                case 4:
                    message.purpleThreshold = reader.double();
                    break;
                case 5:
                    message.blackThreshold = reader.double();
                    break;
                case 6:
                    message.updatedBy = reader.string();
                    break;
                case 7:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            whiteThreshold: isSet(object.whiteThreshold) ? Number(object.whiteThreshold) : 0,
            orangeThreshold: isSet(object.orangeThreshold) ? Number(object.orangeThreshold) : 0,
            blueThreshold: isSet(object.blueThreshold) ? Number(object.blueThreshold) : 0,
            purpleThreshold: isSet(object.purpleThreshold) ? Number(object.purpleThreshold) : 0,
            blackThreshold: isSet(object.blackThreshold) ? Number(object.blackThreshold) : 0,
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.whiteThreshold !== undefined && (obj.whiteThreshold = message.whiteThreshold);
        message.orangeThreshold !== undefined && (obj.orangeThreshold = message.orangeThreshold);
        message.blueThreshold !== undefined && (obj.blueThreshold = message.blueThreshold);
        message.purpleThreshold !== undefined && (obj.purpleThreshold = message.purpleThreshold);
        message.blackThreshold !== undefined && (obj.blackThreshold = message.blackThreshold);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsSetLeagueConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g;
        const message = createBasePointsSetLeagueConfigResponse();
        message.whiteThreshold = (_a = object.whiteThreshold) !== null && _a !== void 0 ? _a : 0;
        message.orangeThreshold = (_b = object.orangeThreshold) !== null && _b !== void 0 ? _b : 0;
        message.blueThreshold = (_c = object.blueThreshold) !== null && _c !== void 0 ? _c : 0;
        message.purpleThreshold = (_d = object.purpleThreshold) !== null && _d !== void 0 ? _d : 0;
        message.blackThreshold = (_e = object.blackThreshold) !== null && _e !== void 0 ? _e : 0;
        message.updatedBy = (_f = object.updatedBy) !== null && _f !== void 0 ? _f : "";
        message.updatedAt = (_g = object.updatedAt) !== null && _g !== void 0 ? _g : "";
        return message;
    },
};
function createBasePointsGetEmissionConfigRequest() {
    return {};
}
exports.PointsGetEmissionConfigRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetEmissionConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsGetEmissionConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsGetEmissionConfigRequest();
        return message;
    },
};
function createBasePointsGetEmissionConfigResponse() {
    return { unitVolume: 0, volumeTiers: [], updatedBy: "", updatedAt: "" };
}
exports.PointsGetEmissionConfigResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            exports.ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.updatedBy !== "") {
            writer.uint32(26).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(34).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsGetEmissionConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(exports.ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.updatedBy = reader.string();
                    break;
                case 4:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object === null || object === void 0 ? void 0 : object.volumeTiers)
                ? object.volumeTiers.map((e) => exports.ModelVolumeTierConfig.fromJSON(e))
                : [],
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? exports.ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsGetEmissionConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePointsGetEmissionConfigResponse();
        message.unitVolume = (_a = object.unitVolume) !== null && _a !== void 0 ? _a : 0;
        message.volumeTiers = ((_b = object.volumeTiers) === null || _b === void 0 ? void 0 : _b.map((e) => exports.ModelVolumeTierConfig.fromPartial(e))) || [];
        message.updatedBy = (_c = object.updatedBy) !== null && _c !== void 0 ? _c : "";
        message.updatedAt = (_d = object.updatedAt) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBaseModelVolumeTierConfig() {
    return { upperThreshold: 0, takerRate: 0, makerRate: 0 };
}
exports.ModelVolumeTierConfig = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.upperThreshold !== 0) {
            writer.uint32(9).double(message.upperThreshold);
        }
        if (message.takerRate !== 0) {
            writer.uint32(17).double(message.takerRate);
        }
        if (message.makerRate !== 0) {
            writer.uint32(25).double(message.makerRate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModelVolumeTierConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.upperThreshold = reader.double();
                    break;
                case 2:
                    message.takerRate = reader.double();
                    break;
                case 3:
                    message.makerRate = reader.double();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            upperThreshold: isSet(object.upperThreshold) ? Number(object.upperThreshold) : 0,
            takerRate: isSet(object.takerRate) ? Number(object.takerRate) : 0,
            makerRate: isSet(object.makerRate) ? Number(object.makerRate) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        message.upperThreshold !== undefined && (obj.upperThreshold = message.upperThreshold);
        message.takerRate !== undefined && (obj.takerRate = message.takerRate);
        message.makerRate !== undefined && (obj.makerRate = message.makerRate);
        return obj;
    },
    create(base) {
        return exports.ModelVolumeTierConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c;
        const message = createBaseModelVolumeTierConfig();
        message.upperThreshold = (_a = object.upperThreshold) !== null && _a !== void 0 ? _a : 0;
        message.takerRate = (_b = object.takerRate) !== null && _b !== void 0 ? _b : 0;
        message.makerRate = (_c = object.makerRate) !== null && _c !== void 0 ? _c : 0;
        return message;
    },
};
function createBasePointsSetEmissionConfigRequest() {
    return { unitVolume: 0, volumeTiers: [] };
}
exports.PointsSetEmissionConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            exports.ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetEmissionConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(exports.ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object === null || object === void 0 ? void 0 : object.volumeTiers)
                ? object.volumeTiers.map((e) => exports.ModelVolumeTierConfig.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? exports.ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        return obj;
    },
    create(base) {
        return exports.PointsSetEmissionConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBasePointsSetEmissionConfigRequest();
        message.unitVolume = (_a = object.unitVolume) !== null && _a !== void 0 ? _a : 0;
        message.volumeTiers = ((_b = object.volumeTiers) === null || _b === void 0 ? void 0 : _b.map((e) => exports.ModelVolumeTierConfig.fromPartial(e))) || [];
        return message;
    },
};
function createBasePointsSetEmissionConfigResponse() {
    return { unitVolume: 0, volumeTiers: [], updatedBy: "", updatedAt: "" };
}
exports.PointsSetEmissionConfigResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.unitVolume !== 0) {
            writer.uint32(9).double(message.unitVolume);
        }
        for (const v of message.volumeTiers) {
            exports.ModelVolumeTierConfig.encode(v, writer.uint32(18).fork()).ldelim();
        }
        if (message.updatedBy !== "") {
            writer.uint32(26).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(34).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsSetEmissionConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.unitVolume = reader.double();
                    break;
                case 2:
                    message.volumeTiers.push(exports.ModelVolumeTierConfig.decode(reader, reader.uint32()));
                    break;
                case 3:
                    message.updatedBy = reader.string();
                    break;
                case 4:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            unitVolume: isSet(object.unitVolume) ? Number(object.unitVolume) : 0,
            volumeTiers: Array.isArray(object === null || object === void 0 ? void 0 : object.volumeTiers)
                ? object.volumeTiers.map((e) => exports.ModelVolumeTierConfig.fromJSON(e))
                : [],
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.unitVolume !== undefined && (obj.unitVolume = message.unitVolume);
        if (message.volumeTiers) {
            obj.volumeTiers = message.volumeTiers.map((e) => e ? exports.ModelVolumeTierConfig.toJSON(e) : undefined);
        }
        else {
            obj.volumeTiers = [];
        }
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsSetEmissionConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePointsSetEmissionConfigResponse();
        message.unitVolume = (_a = object.unitVolume) !== null && _a !== void 0 ? _a : 0;
        message.volumeTiers = ((_b = object.volumeTiers) === null || _b === void 0 ? void 0 : _b.map((e) => exports.ModelVolumeTierConfig.fromPartial(e))) || [];
        message.updatedBy = (_c = object.updatedBy) !== null && _c !== void 0 ? _c : "";
        message.updatedAt = (_d = object.updatedAt) !== null && _d !== void 0 ? _d : "";
        return message;
    },
};
function createBasePointsListBanConfigsRequest() {
    return {};
}
exports.PointsListBanConfigsRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListBanConfigsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsListBanConfigsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsListBanConfigsRequest();
        return message;
    },
};
function createBaseBanConfigCollection() {
    return { bans: [] };
}
exports.BanConfigCollection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.bans) {
            exports.BanConfig.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBanConfigCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.bans.push(exports.BanConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { bans: Array.isArray(object === null || object === void 0 ? void 0 : object.bans) ? object.bans.map((e) => exports.BanConfig.fromJSON(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.bans) {
            obj.bans = message.bans.map((e) => e ? exports.BanConfig.toJSON(e) : undefined);
        }
        else {
            obj.bans = [];
        }
        return obj;
    },
    create(base) {
        return exports.BanConfigCollection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseBanConfigCollection();
        message.bans = ((_a = object.bans) === null || _a === void 0 ? void 0 : _a.map((e) => exports.BanConfig.fromPartial(e))) || [];
        return message;
    },
};
function createBaseBanConfig() {
    return { id: "", label: "", accountAddress: "", ethAddress: "", createdBy: "", createdAt: "" };
}
exports.BanConfig = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.ethAddress !== "") {
            writer.uint32(34).string(message.ethAddress);
        }
        if (message.createdBy !== "") {
            writer.uint32(42).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(50).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseBanConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.ethAddress = reader.string();
                    break;
                case 5:
                    message.createdBy = reader.string();
                    break;
                case 6:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            ethAddress: isSet(object.ethAddress) ? String(object.ethAddress) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.ethAddress !== undefined && (obj.ethAddress = message.ethAddress);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return exports.BanConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f;
        const message = createBaseBanConfig();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.ethAddress = (_d = object.ethAddress) !== null && _d !== void 0 ? _d : "";
        message.createdBy = (_e = object.createdBy) !== null && _e !== void 0 ? _e : "";
        message.createdAt = (_f = object.createdAt) !== null && _f !== void 0 ? _f : "";
        return message;
    },
};
function createBasePointsCreateBanConfigRequest() {
    return { accountAddresses: [], ethAddresses: [], label: "", effectiveFrom: "0" };
}
exports.PointsCreateBanConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.accountAddresses) {
            writer.uint32(10).string(v);
        }
        for (const v of message.ethAddresses) {
            writer.uint32(18).string(v);
        }
        if (message.label !== "") {
            writer.uint32(26).string(message.label);
        }
        if (message.effectiveFrom !== "0") {
            writer.uint32(32).uint64(message.effectiveFrom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateBanConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddresses.push(reader.string());
                    break;
                case 2:
                    message.ethAddresses.push(reader.string());
                    break;
                case 3:
                    message.label = reader.string();
                    break;
                case 4:
                    message.effectiveFrom = longToString(reader.uint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.accountAddresses)
                ? object.accountAddresses.map((e) => String(e))
                : [],
            ethAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.ethAddresses) ? object.ethAddresses.map((e) => String(e)) : [],
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.accountAddresses) {
            obj.accountAddresses = message.accountAddresses.map((e) => e);
        }
        else {
            obj.accountAddresses = [];
        }
        if (message.ethAddresses) {
            obj.ethAddresses = message.ethAddresses.map((e) => e);
        }
        else {
            obj.ethAddresses = [];
        }
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        return obj;
    },
    create(base) {
        return exports.PointsCreateBanConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d;
        const message = createBasePointsCreateBanConfigRequest();
        message.accountAddresses = ((_a = object.accountAddresses) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.ethAddresses = ((_b = object.ethAddresses) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        message.label = (_c = object.label) !== null && _c !== void 0 ? _c : "";
        message.effectiveFrom = (_d = object.effectiveFrom) !== null && _d !== void 0 ? _d : "0";
        return message;
    },
};
function createBasePointsDeleteBanConfigRequest() {
    return { id: "" };
}
exports.PointsDeleteBanConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteBanConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return exports.PointsDeleteBanConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsDeleteBanConfigRequest();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBasePointsDeleteBanConfigResponse() {
    return {};
}
exports.PointsDeleteBanConfigResponse = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteBanConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsDeleteBanConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteBanConfigResponse();
        return message;
    },
};
function createBasePointsListMultiplierConfigsRequest() {
    return {};
}
exports.PointsListMultiplierConfigsRequest = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListMultiplierConfigsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsListMultiplierConfigsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsListMultiplierConfigsRequest();
        return message;
    },
};
function createBasePointsMultiplierConfigCollection() {
    return { multipliers: [] };
}
exports.PointsMultiplierConfigCollection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.multipliers) {
            exports.PointsMultiplierConfig.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsMultiplierConfigCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.multipliers.push(exports.PointsMultiplierConfig.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            multipliers: Array.isArray(object === null || object === void 0 ? void 0 : object.multipliers)
                ? object.multipliers.map((e) => exports.PointsMultiplierConfig.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.multipliers) {
            obj.multipliers = message.multipliers.map((e) => e ? exports.PointsMultiplierConfig.toJSON(e) : undefined);
        }
        else {
            obj.multipliers = [];
        }
        return obj;
    },
    create(base) {
        return exports.PointsMultiplierConfigCollection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsMultiplierConfigCollection();
        message.multipliers = ((_a = object.multipliers) === null || _a === void 0 ? void 0 : _a.map((e) => exports.PointsMultiplierConfig.fromPartial(e))) || [];
        return message;
    },
};
function createBasePointsMultiplierConfig() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
        enforceRecalculate: false,
        createdBy: "",
        createdAt: "",
        updatedBy: "",
        updatedAt: "",
    };
}
exports.PointsMultiplierConfig = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(56).uint64(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(64).uint64(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(80).bool(message.enforceRecalculate);
        }
        if (message.createdBy !== "") {
            writer.uint32(90).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(98).string(message.createdAt);
        }
        if (message.updatedBy !== "") {
            writer.uint32(106).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(114).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsMultiplierConfig();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = longToString(reader.uint64());
                    break;
                case 8:
                    message.effectiveDateEnd = longToString(reader.uint64());
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                case 10:
                    message.enforceRecalculate = reader.bool();
                    break;
                case 11:
                    message.createdBy = reader.string();
                    break;
                case 12:
                    message.createdAt = reader.string();
                    break;
                case 13:
                    message.updatedBy = reader.string();
                    break;
                case 14:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object === null || object === void 0 ? void 0 : object.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object === null || object === void 0 ? void 0 : object.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsMultiplierConfig.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        const message = createBasePointsMultiplierConfig();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.multiplier = (_c = object.multiplier) !== null && _c !== void 0 ? _c : 0;
        message.affectedUsers = ((_d = object.affectedUsers) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.affectedMarkets = ((_e = object.affectedMarkets) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        message.allMarketsExcept = ((_f = object.allMarketsExcept) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.effectiveDateStart = (_g = object.effectiveDateStart) !== null && _g !== void 0 ? _g : undefined;
        message.effectiveDateEnd = (_h = object.effectiveDateEnd) !== null && _h !== void 0 ? _h : undefined;
        message.effectiveFlags = ((_j = object.effectiveFlags) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        message.enforceRecalculate = (_k = object.enforceRecalculate) !== null && _k !== void 0 ? _k : false;
        message.createdBy = (_l = object.createdBy) !== null && _l !== void 0 ? _l : "";
        message.createdAt = (_m = object.createdAt) !== null && _m !== void 0 ? _m : "";
        message.updatedBy = (_o = object.updatedBy) !== null && _o !== void 0 ? _o : "";
        message.updatedAt = (_p = object.updatedAt) !== null && _p !== void 0 ? _p : "";
        return message;
    },
};
function createBasePointsCreateMultiplierConfigRequest() {
    return {
        marketIds: [],
        allMarketsExcept: [],
        accountAddresses: [],
        flags: [],
        multiplier: 0,
        label: "",
        effectiveFrom: undefined,
        effectiveUntil: undefined,
        enforceRecalculate: false,
    };
}
exports.PointsCreateMultiplierConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.marketIds) {
            writer.uint32(10).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(18).string(v);
        }
        for (const v of message.accountAddresses) {
            writer.uint32(26).string(v);
        }
        for (const v of message.flags) {
            writer.uint32(34).string(v);
        }
        if (message.multiplier !== 0) {
            writer.uint32(41).double(message.multiplier);
        }
        if (message.label !== "") {
            writer.uint32(50).string(message.label);
        }
        if (message.effectiveFrom !== undefined) {
            writer.uint32(56).sint64(message.effectiveFrom);
        }
        if (message.effectiveUntil !== undefined) {
            writer.uint32(64).sint64(message.effectiveUntil);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(72).bool(message.enforceRecalculate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateMultiplierConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketIds.push(reader.string());
                    break;
                case 2:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 3:
                    message.accountAddresses.push(reader.string());
                    break;
                case 4:
                    message.flags.push(reader.string());
                    break;
                case 5:
                    message.multiplier = reader.double();
                    break;
                case 6:
                    message.label = reader.string();
                    break;
                case 7:
                    message.effectiveFrom = longToString(reader.sint64());
                    break;
                case 8:
                    message.effectiveUntil = longToString(reader.sint64());
                    break;
                case 9:
                    message.enforceRecalculate = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            marketIds: Array.isArray(object === null || object === void 0 ? void 0 : object.marketIds) ? object.marketIds.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object === null || object === void 0 ? void 0 : object.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            accountAddresses: Array.isArray(object === null || object === void 0 ? void 0 : object.accountAddresses)
                ? object.accountAddresses.map((e) => String(e))
                : [],
            flags: Array.isArray(object === null || object === void 0 ? void 0 : object.flags) ? object.flags.map((e) => String(e)) : [],
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : undefined,
            effectiveUntil: isSet(object.effectiveUntil) ? String(object.effectiveUntil) : undefined,
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.marketIds) {
            obj.marketIds = message.marketIds.map((e) => e);
        }
        else {
            obj.marketIds = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        if (message.accountAddresses) {
            obj.accountAddresses = message.accountAddresses.map((e) => e);
        }
        else {
            obj.accountAddresses = [];
        }
        if (message.flags) {
            obj.flags = message.flags.map((e) => e);
        }
        else {
            obj.flags = [];
        }
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.effectiveUntil !== undefined && (obj.effectiveUntil = message.effectiveUntil);
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        return obj;
    },
    create(base) {
        return exports.PointsCreateMultiplierConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const message = createBasePointsCreateMultiplierConfigRequest();
        message.marketIds = ((_a = object.marketIds) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
        message.allMarketsExcept = ((_b = object.allMarketsExcept) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
        message.accountAddresses = ((_c = object.accountAddresses) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
        message.flags = ((_d = object.flags) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.multiplier = (_e = object.multiplier) !== null && _e !== void 0 ? _e : 0;
        message.label = (_f = object.label) !== null && _f !== void 0 ? _f : "";
        message.effectiveFrom = (_g = object.effectiveFrom) !== null && _g !== void 0 ? _g : undefined;
        message.effectiveUntil = (_h = object.effectiveUntil) !== null && _h !== void 0 ? _h : undefined;
        message.enforceRecalculate = (_j = object.enforceRecalculate) !== null && _j !== void 0 ? _j : false;
        return message;
    },
};
function createBasePointsCreateMultiplierConfigResponse() {
    return {
        id: "",
        label: "",
        multiplier: 0,
        affectedUsers: [],
        affectedMarkets: [],
        allMarketsExcept: [],
        effectiveDateStart: undefined,
        effectiveDateEnd: undefined,
        effectiveFlags: [],
        enforceRecalculate: false,
        createdBy: "",
        createdAt: "",
        updatedBy: "",
        updatedAt: "",
    };
}
exports.PointsCreateMultiplierConfigResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.multiplier !== 0) {
            writer.uint32(25).double(message.multiplier);
        }
        for (const v of message.affectedUsers) {
            writer.uint32(34).string(v);
        }
        for (const v of message.affectedMarkets) {
            writer.uint32(42).string(v);
        }
        for (const v of message.allMarketsExcept) {
            writer.uint32(50).string(v);
        }
        if (message.effectiveDateStart !== undefined) {
            writer.uint32(56).uint64(message.effectiveDateStart);
        }
        if (message.effectiveDateEnd !== undefined) {
            writer.uint32(64).uint64(message.effectiveDateEnd);
        }
        for (const v of message.effectiveFlags) {
            writer.uint32(74).string(v);
        }
        if (message.enforceRecalculate === true) {
            writer.uint32(80).bool(message.enforceRecalculate);
        }
        if (message.createdBy !== "") {
            writer.uint32(90).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(98).string(message.createdAt);
        }
        if (message.updatedBy !== "") {
            writer.uint32(106).string(message.updatedBy);
        }
        if (message.updatedAt !== "") {
            writer.uint32(114).string(message.updatedAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateMultiplierConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.multiplier = reader.double();
                    break;
                case 4:
                    message.affectedUsers.push(reader.string());
                    break;
                case 5:
                    message.affectedMarkets.push(reader.string());
                    break;
                case 6:
                    message.allMarketsExcept.push(reader.string());
                    break;
                case 7:
                    message.effectiveDateStart = longToString(reader.uint64());
                    break;
                case 8:
                    message.effectiveDateEnd = longToString(reader.uint64());
                    break;
                case 9:
                    message.effectiveFlags.push(reader.string());
                    break;
                case 10:
                    message.enforceRecalculate = reader.bool();
                    break;
                case 11:
                    message.createdBy = reader.string();
                    break;
                case 12:
                    message.createdAt = reader.string();
                    break;
                case 13:
                    message.updatedBy = reader.string();
                    break;
                case 14:
                    message.updatedAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            multiplier: isSet(object.multiplier) ? Number(object.multiplier) : 0,
            affectedUsers: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedUsers) ? object.affectedUsers.map((e) => String(e)) : [],
            affectedMarkets: Array.isArray(object === null || object === void 0 ? void 0 : object.affectedMarkets) ? object.affectedMarkets.map((e) => String(e)) : [],
            allMarketsExcept: Array.isArray(object === null || object === void 0 ? void 0 : object.allMarketsExcept)
                ? object.allMarketsExcept.map((e) => String(e))
                : [],
            effectiveDateStart: isSet(object.effectiveDateStart) ? String(object.effectiveDateStart) : undefined,
            effectiveDateEnd: isSet(object.effectiveDateEnd) ? String(object.effectiveDateEnd) : undefined,
            effectiveFlags: Array.isArray(object === null || object === void 0 ? void 0 : object.effectiveFlags) ? object.effectiveFlags.map((e) => String(e)) : [],
            enforceRecalculate: isSet(object.enforceRecalculate) ? Boolean(object.enforceRecalculate) : false,
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
            updatedBy: isSet(object.updatedBy) ? String(object.updatedBy) : "",
            updatedAt: isSet(object.updatedAt) ? String(object.updatedAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.multiplier !== undefined && (obj.multiplier = message.multiplier);
        if (message.affectedUsers) {
            obj.affectedUsers = message.affectedUsers.map((e) => e);
        }
        else {
            obj.affectedUsers = [];
        }
        if (message.affectedMarkets) {
            obj.affectedMarkets = message.affectedMarkets.map((e) => e);
        }
        else {
            obj.affectedMarkets = [];
        }
        if (message.allMarketsExcept) {
            obj.allMarketsExcept = message.allMarketsExcept.map((e) => e);
        }
        else {
            obj.allMarketsExcept = [];
        }
        message.effectiveDateStart !== undefined && (obj.effectiveDateStart = message.effectiveDateStart);
        message.effectiveDateEnd !== undefined && (obj.effectiveDateEnd = message.effectiveDateEnd);
        if (message.effectiveFlags) {
            obj.effectiveFlags = message.effectiveFlags.map((e) => e);
        }
        else {
            obj.effectiveFlags = [];
        }
        message.enforceRecalculate !== undefined && (obj.enforceRecalculate = message.enforceRecalculate);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        message.updatedBy !== undefined && (obj.updatedBy = message.updatedBy);
        message.updatedAt !== undefined && (obj.updatedAt = message.updatedAt);
        return obj;
    },
    create(base) {
        return exports.PointsCreateMultiplierConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        const message = createBasePointsCreateMultiplierConfigResponse();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.multiplier = (_c = object.multiplier) !== null && _c !== void 0 ? _c : 0;
        message.affectedUsers = ((_d = object.affectedUsers) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
        message.affectedMarkets = ((_e = object.affectedMarkets) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
        message.allMarketsExcept = ((_f = object.allMarketsExcept) === null || _f === void 0 ? void 0 : _f.map((e) => e)) || [];
        message.effectiveDateStart = (_g = object.effectiveDateStart) !== null && _g !== void 0 ? _g : undefined;
        message.effectiveDateEnd = (_h = object.effectiveDateEnd) !== null && _h !== void 0 ? _h : undefined;
        message.effectiveFlags = ((_j = object.effectiveFlags) === null || _j === void 0 ? void 0 : _j.map((e) => e)) || [];
        message.enforceRecalculate = (_k = object.enforceRecalculate) !== null && _k !== void 0 ? _k : false;
        message.createdBy = (_l = object.createdBy) !== null && _l !== void 0 ? _l : "";
        message.createdAt = (_m = object.createdAt) !== null && _m !== void 0 ? _m : "";
        message.updatedBy = (_o = object.updatedBy) !== null && _o !== void 0 ? _o : "";
        message.updatedAt = (_p = object.updatedAt) !== null && _p !== void 0 ? _p : "";
        return message;
    },
};
function createBasePointsDeleteMultiplierConfigRequest() {
    return { id: "" };
}
exports.PointsDeleteMultiplierConfigRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteMultiplierConfigRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return exports.PointsDeleteMultiplierConfigRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsDeleteMultiplierConfigRequest();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBasePointsDeleteMultiplierConfigResponse() {
    return {};
}
exports.PointsDeleteMultiplierConfigResponse = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteMultiplierConfigResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsDeleteMultiplierConfigResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteMultiplierConfigResponse();
        return message;
    },
};
function createBasePointsListAdminCorrectionsRequest() {
    return { correctionType: undefined, accountAddress: undefined };
}
exports.PointsListAdminCorrectionsRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.correctionType !== undefined) {
            writer.uint32(10).string(message.correctionType);
        }
        if (message.accountAddress !== undefined) {
            writer.uint32(18).string(message.accountAddress);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsListAdminCorrectionsRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.correctionType = reader.string();
                    break;
                case 2:
                    message.accountAddress = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : undefined,
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        return obj;
    },
    create(base) {
        return exports.PointsListAdminCorrectionsRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b;
        const message = createBasePointsListAdminCorrectionsRequest();
        message.correctionType = (_a = object.correctionType) !== null && _a !== void 0 ? _a : undefined;
        message.accountAddress = (_b = object.accountAddress) !== null && _b !== void 0 ? _b : undefined;
        return message;
    },
};
function createBaseAdminPointsCorrectionCollection() {
    return { corrections: [] };
}
exports.AdminPointsCorrectionCollection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        for (const v of message.corrections) {
            exports.AdminPointsCorrection.encode(v, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAdminPointsCorrectionCollection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.corrections.push(exports.AdminPointsCorrection.decode(reader, reader.uint32()));
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            corrections: Array.isArray(object === null || object === void 0 ? void 0 : object.corrections)
                ? object.corrections.map((e) => exports.AdminPointsCorrection.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.corrections) {
            obj.corrections = message.corrections.map((e) => e ? exports.AdminPointsCorrection.toJSON(e) : undefined);
        }
        else {
            obj.corrections = [];
        }
        return obj;
    },
    create(base) {
        return exports.AdminPointsCorrectionCollection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBaseAdminPointsCorrectionCollection();
        message.corrections = ((_a = object.corrections) === null || _a === void 0 ? void 0 : _a.map((e) => exports.AdminPointsCorrection.fromPartial(e))) || [];
        return message;
    },
};
function createBaseAdminPointsCorrection() {
    return {
        id: "",
        label: "",
        accountAddress: "",
        correctionType: "",
        value: 0,
        effectiveFrom: "",
        createdBy: "",
        createdAt: "",
    };
}
exports.AdminPointsCorrection = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(34).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(41).double(message.value);
        }
        if (message.effectiveFrom !== "") {
            writer.uint32(50).string(message.effectiveFrom);
        }
        if (message.createdBy !== "") {
            writer.uint32(58).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(66).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseAdminPointsCorrection();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.correctionType = reader.string();
                    break;
                case 5:
                    message.value = reader.double();
                    break;
                case 6:
                    message.effectiveFrom = reader.string();
                    break;
                case 7:
                    message.createdBy = reader.string();
                    break;
                case 8:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return exports.AdminPointsCorrection.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBaseAdminPointsCorrection();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.correctionType = (_d = object.correctionType) !== null && _d !== void 0 ? _d : "";
        message.value = (_e = object.value) !== null && _e !== void 0 ? _e : 0;
        message.effectiveFrom = (_f = object.effectiveFrom) !== null && _f !== void 0 ? _f : "";
        message.createdBy = (_g = object.createdBy) !== null && _g !== void 0 ? _g : "";
        message.createdAt = (_h = object.createdAt) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBasePointsCreateAdminCorrectionRequest() {
    return { accountAddress: "", correctionType: "", value: 0, label: "", effectiveFrom: "0" };
}
exports.PointsCreateAdminCorrectionRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.accountAddress !== "") {
            writer.uint32(10).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(18).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(25).double(message.value);
        }
        if (message.label !== "") {
            writer.uint32(34).string(message.label);
        }
        if (message.effectiveFrom !== "0") {
            writer.uint32(40).sint64(message.effectiveFrom);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateAdminCorrectionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.accountAddress = reader.string();
                    break;
                case 2:
                    message.correctionType = reader.string();
                    break;
                case 3:
                    message.value = reader.double();
                    break;
                case 4:
                    message.label = reader.string();
                    break;
                case 5:
                    message.effectiveFrom = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            label: isSet(object.label) ? String(object.label) : "",
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.label !== undefined && (obj.label = message.label);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        return obj;
    },
    create(base) {
        return exports.PointsCreateAdminCorrectionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e;
        const message = createBasePointsCreateAdminCorrectionRequest();
        message.accountAddress = (_a = object.accountAddress) !== null && _a !== void 0 ? _a : "";
        message.correctionType = (_b = object.correctionType) !== null && _b !== void 0 ? _b : "";
        message.value = (_c = object.value) !== null && _c !== void 0 ? _c : 0;
        message.label = (_d = object.label) !== null && _d !== void 0 ? _d : "";
        message.effectiveFrom = (_e = object.effectiveFrom) !== null && _e !== void 0 ? _e : "0";
        return message;
    },
};
function createBasePointsCreateAdminCorrectionResponse() {
    return {
        id: "",
        label: "",
        accountAddress: "",
        correctionType: "",
        value: 0,
        effectiveFrom: "",
        createdBy: "",
        createdAt: "",
    };
}
exports.PointsCreateAdminCorrectionResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.accountAddress !== "") {
            writer.uint32(26).string(message.accountAddress);
        }
        if (message.correctionType !== "") {
            writer.uint32(34).string(message.correctionType);
        }
        if (message.value !== 0) {
            writer.uint32(41).double(message.value);
        }
        if (message.effectiveFrom !== "") {
            writer.uint32(50).string(message.effectiveFrom);
        }
        if (message.createdBy !== "") {
            writer.uint32(58).string(message.createdBy);
        }
        if (message.createdAt !== "") {
            writer.uint32(66).string(message.createdAt);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsCreateAdminCorrectionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                case 2:
                    message.label = reader.string();
                    break;
                case 3:
                    message.accountAddress = reader.string();
                    break;
                case 4:
                    message.correctionType = reader.string();
                    break;
                case 5:
                    message.value = reader.double();
                    break;
                case 6:
                    message.effectiveFrom = reader.string();
                    break;
                case 7:
                    message.createdBy = reader.string();
                    break;
                case 8:
                    message.createdAt = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? String(object.id) : "",
            label: isSet(object.label) ? String(object.label) : "",
            accountAddress: isSet(object.accountAddress) ? String(object.accountAddress) : "",
            correctionType: isSet(object.correctionType) ? String(object.correctionType) : "",
            value: isSet(object.value) ? Number(object.value) : 0,
            effectiveFrom: isSet(object.effectiveFrom) ? String(object.effectiveFrom) : "",
            createdBy: isSet(object.createdBy) ? String(object.createdBy) : "",
            createdAt: isSet(object.createdAt) ? String(object.createdAt) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        message.label !== undefined && (obj.label = message.label);
        message.accountAddress !== undefined && (obj.accountAddress = message.accountAddress);
        message.correctionType !== undefined && (obj.correctionType = message.correctionType);
        message.value !== undefined && (obj.value = message.value);
        message.effectiveFrom !== undefined && (obj.effectiveFrom = message.effectiveFrom);
        message.createdBy !== undefined && (obj.createdBy = message.createdBy);
        message.createdAt !== undefined && (obj.createdAt = message.createdAt);
        return obj;
    },
    create(base) {
        return exports.PointsCreateAdminCorrectionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const message = createBasePointsCreateAdminCorrectionResponse();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        message.label = (_b = object.label) !== null && _b !== void 0 ? _b : "";
        message.accountAddress = (_c = object.accountAddress) !== null && _c !== void 0 ? _c : "";
        message.correctionType = (_d = object.correctionType) !== null && _d !== void 0 ? _d : "";
        message.value = (_e = object.value) !== null && _e !== void 0 ? _e : 0;
        message.effectiveFrom = (_f = object.effectiveFrom) !== null && _f !== void 0 ? _f : "";
        message.createdBy = (_g = object.createdBy) !== null && _g !== void 0 ? _g : "";
        message.createdAt = (_h = object.createdAt) !== null && _h !== void 0 ? _h : "";
        return message;
    },
};
function createBasePointsDeleteAdminCorrectionRequest() {
    return { id: "" };
}
exports.PointsDeleteAdminCorrectionRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.id !== "") {
            writer.uint32(10).string(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteAdminCorrectionRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.id = reader.string();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { id: isSet(object.id) ? String(object.id) : "" };
    },
    toJSON(message) {
        const obj = {};
        message.id !== undefined && (obj.id = message.id);
        return obj;
    },
    create(base) {
        return exports.PointsDeleteAdminCorrectionRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsDeleteAdminCorrectionRequest();
        message.id = (_a = object.id) !== null && _a !== void 0 ? _a : "";
        return message;
    },
};
function createBasePointsDeleteAdminCorrectionResponse() {
    return {};
}
exports.PointsDeleteAdminCorrectionResponse = {
    encode(_, writer = minimal_js_1.default.Writer.create()) {
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsDeleteAdminCorrectionResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(_) {
        return {};
    },
    toJSON(_) {
        const obj = {};
        return obj;
    },
    create(base) {
        return exports.PointsDeleteAdminCorrectionResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(_) {
        const message = createBasePointsDeleteAdminCorrectionResponse();
        return message;
    },
};
function createBasePointsReprocessTradesAfterDateRequest() {
    return { afterDate: "0" };
}
exports.PointsReprocessTradesAfterDateRequest = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.afterDate !== "0") {
            writer.uint32(8).sint64(message.afterDate);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsReprocessTradesAfterDateRequest();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.afterDate = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { afterDate: isSet(object.afterDate) ? String(object.afterDate) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.afterDate !== undefined && (obj.afterDate = message.afterDate);
        return obj;
    },
    create(base) {
        return exports.PointsReprocessTradesAfterDateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsReprocessTradesAfterDateRequest();
        message.afterDate = (_a = object.afterDate) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
function createBasePointsReprocessTradesAfterDateResponse() {
    return { affectedCount: "0" };
}
exports.PointsReprocessTradesAfterDateResponse = {
    encode(message, writer = minimal_js_1.default.Writer.create()) {
        if (message.affectedCount !== "0") {
            writer.uint32(8).sint64(message.affectedCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePointsReprocessTradesAfterDateResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.affectedCount = longToString(reader.sint64());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return { affectedCount: isSet(object.affectedCount) ? String(object.affectedCount) : "0" };
    },
    toJSON(message) {
        const obj = {};
        message.affectedCount !== undefined && (obj.affectedCount = message.affectedCount);
        return obj;
    },
    create(base) {
        return exports.PointsReprocessTradesAfterDateResponse.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial(object) {
        var _a;
        const message = createBasePointsReprocessTradesAfterDateResponse();
        message.affectedCount = (_a = object.affectedCount) !== null && _a !== void 0 ? _a : "0";
        return message;
    },
};
class PointsSvcClientImpl {
    constructor(rpc) {
        this.rpc = rpc;
        this.PointsLatestForAccount = this.PointsLatestForAccount.bind(this);
        this.PointsStatsDailyForAccount = this.PointsStatsDailyForAccount.bind(this);
        this.PointsStatsWeeklyForAccount = this.PointsStatsWeeklyForAccount.bind(this);
        this.PointsLeaderboard = this.PointsLeaderboard.bind(this);
        this.PointsSimulateAllocation = this.PointsSimulateAllocation.bind(this);
        this.PointsGetLeagueConfig = this.PointsGetLeagueConfig.bind(this);
        this.PointsSetLeagueConfig = this.PointsSetLeagueConfig.bind(this);
        this.PointsGetEmissionConfig = this.PointsGetEmissionConfig.bind(this);
        this.PointsSetEmissionConfig = this.PointsSetEmissionConfig.bind(this);
        this.PointsListBanConfigs = this.PointsListBanConfigs.bind(this);
        this.PointsCreateBanConfig = this.PointsCreateBanConfig.bind(this);
        this.PointsDeleteBanConfig = this.PointsDeleteBanConfig.bind(this);
        this.PointsListMultiplierConfigs = this.PointsListMultiplierConfigs.bind(this);
        this.PointsCreateMultiplierConfig = this.PointsCreateMultiplierConfig.bind(this);
        this.PointsDeleteMultiplierConfig = this.PointsDeleteMultiplierConfig.bind(this);
        this.PointsListAdminCorrections = this.PointsListAdminCorrections.bind(this);
        this.PointsCreateAdminCorrection = this.PointsCreateAdminCorrection.bind(this);
        this.PointsDeleteAdminCorrection = this.PointsDeleteAdminCorrection.bind(this);
        this.PointsReprocessTradesAfterDate = this.PointsReprocessTradesAfterDate.bind(this);
    }
    PointsLatestForAccount(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsLatestForAccountDesc, exports.PointsLatestForAccountRequest.fromPartial(request), metadata);
    }
    PointsStatsDailyForAccount(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsStatsDailyForAccountDesc, exports.PointsStatsDailyForAccountRequest.fromPartial(request), metadata);
    }
    PointsStatsWeeklyForAccount(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsStatsWeeklyForAccountDesc, exports.PointsStatsWeeklyForAccountRequest.fromPartial(request), metadata);
    }
    PointsLeaderboard(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsLeaderboardDesc, exports.PointsLeaderboardRequest.fromPartial(request), metadata);
    }
    PointsSimulateAllocation(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsSimulateAllocationDesc, exports.PointsSimulateAllocationRequest.fromPartial(request), metadata);
    }
    PointsGetLeagueConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsGetLeagueConfigDesc, exports.PointsGetLeagueConfigRequest.fromPartial(request), metadata);
    }
    PointsSetLeagueConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsSetLeagueConfigDesc, exports.PointsSetLeagueConfigRequest.fromPartial(request), metadata);
    }
    PointsGetEmissionConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsGetEmissionConfigDesc, exports.PointsGetEmissionConfigRequest.fromPartial(request), metadata);
    }
    PointsSetEmissionConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsSetEmissionConfigDesc, exports.PointsSetEmissionConfigRequest.fromPartial(request), metadata);
    }
    PointsListBanConfigs(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsListBanConfigsDesc, exports.PointsListBanConfigsRequest.fromPartial(request), metadata);
    }
    PointsCreateBanConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsCreateBanConfigDesc, exports.PointsCreateBanConfigRequest.fromPartial(request), metadata);
    }
    PointsDeleteBanConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsDeleteBanConfigDesc, exports.PointsDeleteBanConfigRequest.fromPartial(request), metadata);
    }
    PointsListMultiplierConfigs(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsListMultiplierConfigsDesc, exports.PointsListMultiplierConfigsRequest.fromPartial(request), metadata);
    }
    PointsCreateMultiplierConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsCreateMultiplierConfigDesc, exports.PointsCreateMultiplierConfigRequest.fromPartial(request), metadata);
    }
    PointsDeleteMultiplierConfig(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsDeleteMultiplierConfigDesc, exports.PointsDeleteMultiplierConfigRequest.fromPartial(request), metadata);
    }
    PointsListAdminCorrections(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsListAdminCorrectionsDesc, exports.PointsListAdminCorrectionsRequest.fromPartial(request), metadata);
    }
    PointsCreateAdminCorrection(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsCreateAdminCorrectionDesc, exports.PointsCreateAdminCorrectionRequest.fromPartial(request), metadata);
    }
    PointsDeleteAdminCorrection(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsDeleteAdminCorrectionDesc, exports.PointsDeleteAdminCorrectionRequest.fromPartial(request), metadata);
    }
    PointsReprocessTradesAfterDate(request, metadata) {
        return this.rpc.unary(exports.PointsSvcPointsReprocessTradesAfterDateDesc, exports.PointsReprocessTradesAfterDateRequest.fromPartial(request), metadata);
    }
}
exports.PointsSvcClientImpl = PointsSvcClientImpl;
exports.PointsSvcDesc = { serviceName: "points_svc.PointsSvc" };
exports.PointsSvcPointsLatestForAccountDesc = {
    methodName: "PointsLatestForAccount",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsLatestForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsLatestForAccountResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsStatsDailyForAccountDesc = {
    methodName: "PointsStatsDailyForAccount",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsStatsDailyForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.HistoricalPointsStatsRowCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsStatsWeeklyForAccountDesc = {
    methodName: "PointsStatsWeeklyForAccount",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsStatsWeeklyForAccountRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.HistoricalPointsStatsRowCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsLeaderboardDesc = {
    methodName: "PointsLeaderboard",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsLeaderboardRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.LeaderboardPointsRowCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsSimulateAllocationDesc = {
    methodName: "PointsSimulateAllocation",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsSimulateAllocationRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsSimulateAllocationResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsGetLeagueConfigDesc = {
    methodName: "PointsGetLeagueConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsGetLeagueConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsGetLeagueConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsSetLeagueConfigDesc = {
    methodName: "PointsSetLeagueConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsSetLeagueConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsSetLeagueConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsGetEmissionConfigDesc = {
    methodName: "PointsGetEmissionConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsGetEmissionConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsGetEmissionConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsSetEmissionConfigDesc = {
    methodName: "PointsSetEmissionConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsSetEmissionConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsSetEmissionConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsListBanConfigsDesc = {
    methodName: "PointsListBanConfigs",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsListBanConfigsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.BanConfigCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsCreateBanConfigDesc = {
    methodName: "PointsCreateBanConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsCreateBanConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.BanConfigCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsDeleteBanConfigDesc = {
    methodName: "PointsDeleteBanConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsDeleteBanConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsDeleteBanConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsListMultiplierConfigsDesc = {
    methodName: "PointsListMultiplierConfigs",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsListMultiplierConfigsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsMultiplierConfigCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsCreateMultiplierConfigDesc = {
    methodName: "PointsCreateMultiplierConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsCreateMultiplierConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsCreateMultiplierConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsDeleteMultiplierConfigDesc = {
    methodName: "PointsDeleteMultiplierConfig",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsDeleteMultiplierConfigRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsDeleteMultiplierConfigResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsListAdminCorrectionsDesc = {
    methodName: "PointsListAdminCorrections",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsListAdminCorrectionsRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.AdminPointsCorrectionCollection.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsCreateAdminCorrectionDesc = {
    methodName: "PointsCreateAdminCorrection",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsCreateAdminCorrectionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsCreateAdminCorrectionResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsDeleteAdminCorrectionDesc = {
    methodName: "PointsDeleteAdminCorrection",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsDeleteAdminCorrectionRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsDeleteAdminCorrectionResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
exports.PointsSvcPointsReprocessTradesAfterDateDesc = {
    methodName: "PointsReprocessTradesAfterDate",
    service: exports.PointsSvcDesc,
    requestStream: false,
    responseStream: false,
    requestType: {
        serializeBinary() {
            return exports.PointsReprocessTradesAfterDateRequest.encode(this).finish();
        },
    },
    responseType: {
        deserializeBinary(data) {
            const value = exports.PointsReprocessTradesAfterDateResponse.decode(data);
            return Object.assign(Object.assign({}, value), { toObject() {
                    return value;
                } });
        },
    },
};
class GrpcWebImpl {
    constructor(host, options) {
        this.host = host;
        this.options = options;
    }
    unary(methodDesc, _request, metadata) {
        var _a;
        const request = Object.assign(Object.assign({}, _request), methodDesc.requestType);
        const maybeCombinedMetadata = metadata && this.options.metadata
            ? new browser_headers_1.BrowserHeaders(Object.assign(Object.assign({}, (_a = this.options) === null || _a === void 0 ? void 0 : _a.metadata.headersMap), metadata === null || metadata === void 0 ? void 0 : metadata.headersMap))
            : metadata || this.options.metadata;
        return new Promise((resolve, reject) => {
            grpc_web_1.grpc.unary(methodDesc, {
                request,
                host: this.host,
                metadata: maybeCombinedMetadata,
                transport: this.options.transport,
                debug: this.options.debug,
                onEnd: function (response) {
                    if (response.status === grpc_web_1.grpc.Code.OK) {
                        resolve(response.message.toObject());
                    }
                    else {
                        const err = new GrpcWebError(response.statusMessage, response.status, response.trailers);
                        reject(err);
                    }
                },
            });
        });
    }
}
exports.GrpcWebImpl = GrpcWebImpl;
var tsProtoGlobalThis = (() => {
    if (typeof globalThis !== "undefined") {
        return globalThis;
    }
    if (typeof self !== "undefined") {
        return self;
    }
    if (typeof window !== "undefined") {
        return window;
    }
    if (typeof global !== "undefined") {
        return global;
    }
    throw "Unable to locate global object";
})();
function longToString(long) {
    return long.toString();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
    minimal_js_1.default.util.Long = long_1.default;
    minimal_js_1.default.configure();
}
function isSet(value) {
    return value !== null && value !== undefined;
}
class GrpcWebError extends tsProtoGlobalThis.Error {
    constructor(message, code, metadata) {
        super(message);
        this.code = code;
        this.metadata = metadata;
    }
}
exports.GrpcWebError = GrpcWebError;
