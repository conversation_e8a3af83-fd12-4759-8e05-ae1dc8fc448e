/* eslint-disable */
import _m0 from "protobufjs/minimal.js";
export const protobufPackage = "cosmos.autocli.v1";
function createBaseModuleOptions() {
    return { tx: undefined, query: undefined };
}
export const ModuleOptions = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.tx !== undefined) {
            ServiceCommandDescriptor.encode(message.tx, writer.uint32(10).fork()).ldelim();
        }
        if (message.query !== undefined) {
            ServiceCommandDescriptor.encode(message.query, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseModuleOptions();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.tx = ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.query = ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            tx: isSet(object.tx) ? ServiceCommandDescriptor.fromJSON(object.tx) : undefined,
            query: isSet(object.query) ? ServiceCommandDescriptor.fromJSON(object.query) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.tx !== undefined && (obj.tx = message.tx ? ServiceCommandDescriptor.toJSON(message.tx) : undefined);
        message.query !== undefined &&
            (obj.query = message.query ? ServiceCommandDescriptor.toJSON(message.query) : undefined);
        return obj;
    },
    create(base) {
        return ModuleOptions.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseModuleOptions();
        message.tx = (object.tx !== undefined && object.tx !== null)
            ? ServiceCommandDescriptor.fromPartial(object.tx)
            : undefined;
        message.query = (object.query !== undefined && object.query !== null)
            ? ServiceCommandDescriptor.fromPartial(object.query)
            : undefined;
        return message;
    },
};
function createBaseServiceCommandDescriptor() {
    return { service: "", rpcCommandOptions: [], subCommands: {} };
}
export const ServiceCommandDescriptor = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.service !== "") {
            writer.uint32(10).string(message.service);
        }
        for (const v of message.rpcCommandOptions) {
            RpcCommandOptions.encode(v, writer.uint32(18).fork()).ldelim();
        }
        Object.entries(message.subCommands).forEach(([key, value]) => {
            ServiceCommandDescriptor_SubCommandsEntry.encode({ key: key, value }, writer.uint32(26).fork()).ldelim();
        });
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseServiceCommandDescriptor();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.service = reader.string();
                    break;
                case 2:
                    message.rpcCommandOptions.push(RpcCommandOptions.decode(reader, reader.uint32()));
                    break;
                case 3:
                    const entry3 = ServiceCommandDescriptor_SubCommandsEntry.decode(reader, reader.uint32());
                    if (entry3.value !== undefined) {
                        message.subCommands[entry3.key] = entry3.value;
                    }
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            service: isSet(object.service) ? String(object.service) : "",
            rpcCommandOptions: Array.isArray(object?.rpcCommandOptions)
                ? object.rpcCommandOptions.map((e) => RpcCommandOptions.fromJSON(e))
                : [],
            subCommands: isObject(object.subCommands)
                ? Object.entries(object.subCommands).reduce((acc, [key, value]) => {
                    acc[key] = ServiceCommandDescriptor.fromJSON(value);
                    return acc;
                }, {})
                : {},
        };
    },
    toJSON(message) {
        const obj = {};
        message.service !== undefined && (obj.service = message.service);
        if (message.rpcCommandOptions) {
            obj.rpcCommandOptions = message.rpcCommandOptions.map((e) => e ? RpcCommandOptions.toJSON(e) : undefined);
        }
        else {
            obj.rpcCommandOptions = [];
        }
        obj.subCommands = {};
        if (message.subCommands) {
            Object.entries(message.subCommands).forEach(([k, v]) => {
                obj.subCommands[k] = ServiceCommandDescriptor.toJSON(v);
            });
        }
        return obj;
    },
    create(base) {
        return ServiceCommandDescriptor.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseServiceCommandDescriptor();
        message.service = object.service ?? "";
        message.rpcCommandOptions = object.rpcCommandOptions?.map((e) => RpcCommandOptions.fromPartial(e)) || [];
        message.subCommands = Object.entries(object.subCommands ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = ServiceCommandDescriptor.fromPartial(value);
            }
            return acc;
        }, {});
        return message;
    },
};
function createBaseServiceCommandDescriptor_SubCommandsEntry() {
    return { key: "", value: undefined };
}
export const ServiceCommandDescriptor_SubCommandsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            ServiceCommandDescriptor.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseServiceCommandDescriptor_SubCommandsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = ServiceCommandDescriptor.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? ServiceCommandDescriptor.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined &&
            (obj.value = message.value ? ServiceCommandDescriptor.toJSON(message.value) : undefined);
        return obj;
    },
    create(base) {
        return ServiceCommandDescriptor_SubCommandsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseServiceCommandDescriptor_SubCommandsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? ServiceCommandDescriptor.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseRpcCommandOptions() {
    return {
        rpcMethod: "",
        use: "",
        long: "",
        short: "",
        example: "",
        alias: [],
        suggestFor: [],
        deprecated: "",
        version: "",
        flagOptions: {},
        positionalArgs: [],
        skip: false,
    };
}
export const RpcCommandOptions = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.rpcMethod !== "") {
            writer.uint32(10).string(message.rpcMethod);
        }
        if (message.use !== "") {
            writer.uint32(18).string(message.use);
        }
        if (message.long !== "") {
            writer.uint32(26).string(message.long);
        }
        if (message.short !== "") {
            writer.uint32(34).string(message.short);
        }
        if (message.example !== "") {
            writer.uint32(42).string(message.example);
        }
        for (const v of message.alias) {
            writer.uint32(50).string(v);
        }
        for (const v of message.suggestFor) {
            writer.uint32(58).string(v);
        }
        if (message.deprecated !== "") {
            writer.uint32(66).string(message.deprecated);
        }
        if (message.version !== "") {
            writer.uint32(74).string(message.version);
        }
        Object.entries(message.flagOptions).forEach(([key, value]) => {
            RpcCommandOptions_FlagOptionsEntry.encode({ key: key, value }, writer.uint32(82).fork()).ldelim();
        });
        for (const v of message.positionalArgs) {
            PositionalArgDescriptor.encode(v, writer.uint32(90).fork()).ldelim();
        }
        if (message.skip === true) {
            writer.uint32(96).bool(message.skip);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpcCommandOptions();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.rpcMethod = reader.string();
                    break;
                case 2:
                    message.use = reader.string();
                    break;
                case 3:
                    message.long = reader.string();
                    break;
                case 4:
                    message.short = reader.string();
                    break;
                case 5:
                    message.example = reader.string();
                    break;
                case 6:
                    message.alias.push(reader.string());
                    break;
                case 7:
                    message.suggestFor.push(reader.string());
                    break;
                case 8:
                    message.deprecated = reader.string();
                    break;
                case 9:
                    message.version = reader.string();
                    break;
                case 10:
                    const entry10 = RpcCommandOptions_FlagOptionsEntry.decode(reader, reader.uint32());
                    if (entry10.value !== undefined) {
                        message.flagOptions[entry10.key] = entry10.value;
                    }
                    break;
                case 11:
                    message.positionalArgs.push(PositionalArgDescriptor.decode(reader, reader.uint32()));
                    break;
                case 12:
                    message.skip = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            rpcMethod: isSet(object.rpcMethod) ? String(object.rpcMethod) : "",
            use: isSet(object.use) ? String(object.use) : "",
            long: isSet(object.long) ? String(object.long) : "",
            short: isSet(object.short) ? String(object.short) : "",
            example: isSet(object.example) ? String(object.example) : "",
            alias: Array.isArray(object?.alias) ? object.alias.map((e) => String(e)) : [],
            suggestFor: Array.isArray(object?.suggestFor) ? object.suggestFor.map((e) => String(e)) : [],
            deprecated: isSet(object.deprecated) ? String(object.deprecated) : "",
            version: isSet(object.version) ? String(object.version) : "",
            flagOptions: isObject(object.flagOptions)
                ? Object.entries(object.flagOptions).reduce((acc, [key, value]) => {
                    acc[key] = FlagOptions.fromJSON(value);
                    return acc;
                }, {})
                : {},
            positionalArgs: Array.isArray(object?.positionalArgs)
                ? object.positionalArgs.map((e) => PositionalArgDescriptor.fromJSON(e))
                : [],
            skip: isSet(object.skip) ? Boolean(object.skip) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.rpcMethod !== undefined && (obj.rpcMethod = message.rpcMethod);
        message.use !== undefined && (obj.use = message.use);
        message.long !== undefined && (obj.long = message.long);
        message.short !== undefined && (obj.short = message.short);
        message.example !== undefined && (obj.example = message.example);
        if (message.alias) {
            obj.alias = message.alias.map((e) => e);
        }
        else {
            obj.alias = [];
        }
        if (message.suggestFor) {
            obj.suggestFor = message.suggestFor.map((e) => e);
        }
        else {
            obj.suggestFor = [];
        }
        message.deprecated !== undefined && (obj.deprecated = message.deprecated);
        message.version !== undefined && (obj.version = message.version);
        obj.flagOptions = {};
        if (message.flagOptions) {
            Object.entries(message.flagOptions).forEach(([k, v]) => {
                obj.flagOptions[k] = FlagOptions.toJSON(v);
            });
        }
        if (message.positionalArgs) {
            obj.positionalArgs = message.positionalArgs.map((e) => e ? PositionalArgDescriptor.toJSON(e) : undefined);
        }
        else {
            obj.positionalArgs = [];
        }
        message.skip !== undefined && (obj.skip = message.skip);
        return obj;
    },
    create(base) {
        return RpcCommandOptions.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRpcCommandOptions();
        message.rpcMethod = object.rpcMethod ?? "";
        message.use = object.use ?? "";
        message.long = object.long ?? "";
        message.short = object.short ?? "";
        message.example = object.example ?? "";
        message.alias = object.alias?.map((e) => e) || [];
        message.suggestFor = object.suggestFor?.map((e) => e) || [];
        message.deprecated = object.deprecated ?? "";
        message.version = object.version ?? "";
        message.flagOptions = Object.entries(object.flagOptions ?? {}).reduce((acc, [key, value]) => {
            if (value !== undefined) {
                acc[key] = FlagOptions.fromPartial(value);
            }
            return acc;
        }, {});
        message.positionalArgs = object.positionalArgs?.map((e) => PositionalArgDescriptor.fromPartial(e)) || [];
        message.skip = object.skip ?? false;
        return message;
    },
};
function createBaseRpcCommandOptions_FlagOptionsEntry() {
    return { key: "", value: undefined };
}
export const RpcCommandOptions_FlagOptionsEntry = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.key !== "") {
            writer.uint32(10).string(message.key);
        }
        if (message.value !== undefined) {
            FlagOptions.encode(message.value, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRpcCommandOptions_FlagOptionsEntry();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.key = reader.string();
                    break;
                case 2:
                    message.value = FlagOptions.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            key: isSet(object.key) ? String(object.key) : "",
            value: isSet(object.value) ? FlagOptions.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        message.key !== undefined && (obj.key = message.key);
        message.value !== undefined && (obj.value = message.value ? FlagOptions.toJSON(message.value) : undefined);
        return obj;
    },
    create(base) {
        return RpcCommandOptions_FlagOptionsEntry.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRpcCommandOptions_FlagOptionsEntry();
        message.key = object.key ?? "";
        message.value = (object.value !== undefined && object.value !== null)
            ? FlagOptions.fromPartial(object.value)
            : undefined;
        return message;
    },
};
function createBaseFlagOptions() {
    return {
        name: "",
        shorthand: "",
        usage: "",
        defaultValue: "",
        deprecated: "",
        shorthandDeprecated: "",
        hidden: false,
    };
}
export const FlagOptions = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.shorthand !== "") {
            writer.uint32(18).string(message.shorthand);
        }
        if (message.usage !== "") {
            writer.uint32(26).string(message.usage);
        }
        if (message.defaultValue !== "") {
            writer.uint32(34).string(message.defaultValue);
        }
        if (message.deprecated !== "") {
            writer.uint32(50).string(message.deprecated);
        }
        if (message.shorthandDeprecated !== "") {
            writer.uint32(58).string(message.shorthandDeprecated);
        }
        if (message.hidden === true) {
            writer.uint32(64).bool(message.hidden);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFlagOptions();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.name = reader.string();
                    break;
                case 2:
                    message.shorthand = reader.string();
                    break;
                case 3:
                    message.usage = reader.string();
                    break;
                case 4:
                    message.defaultValue = reader.string();
                    break;
                case 6:
                    message.deprecated = reader.string();
                    break;
                case 7:
                    message.shorthandDeprecated = reader.string();
                    break;
                case 8:
                    message.hidden = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            name: isSet(object.name) ? String(object.name) : "",
            shorthand: isSet(object.shorthand) ? String(object.shorthand) : "",
            usage: isSet(object.usage) ? String(object.usage) : "",
            defaultValue: isSet(object.defaultValue) ? String(object.defaultValue) : "",
            deprecated: isSet(object.deprecated) ? String(object.deprecated) : "",
            shorthandDeprecated: isSet(object.shorthandDeprecated) ? String(object.shorthandDeprecated) : "",
            hidden: isSet(object.hidden) ? Boolean(object.hidden) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.name !== undefined && (obj.name = message.name);
        message.shorthand !== undefined && (obj.shorthand = message.shorthand);
        message.usage !== undefined && (obj.usage = message.usage);
        message.defaultValue !== undefined && (obj.defaultValue = message.defaultValue);
        message.deprecated !== undefined && (obj.deprecated = message.deprecated);
        message.shorthandDeprecated !== undefined && (obj.shorthandDeprecated = message.shorthandDeprecated);
        message.hidden !== undefined && (obj.hidden = message.hidden);
        return obj;
    },
    create(base) {
        return FlagOptions.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseFlagOptions();
        message.name = object.name ?? "";
        message.shorthand = object.shorthand ?? "";
        message.usage = object.usage ?? "";
        message.defaultValue = object.defaultValue ?? "";
        message.deprecated = object.deprecated ?? "";
        message.shorthandDeprecated = object.shorthandDeprecated ?? "";
        message.hidden = object.hidden ?? false;
        return message;
    },
};
function createBasePositionalArgDescriptor() {
    return { protoField: "", varargs: false };
}
export const PositionalArgDescriptor = {
    encode(message, writer = _m0.Writer.create()) {
        if (message.protoField !== "") {
            writer.uint32(10).string(message.protoField);
        }
        if (message.varargs === true) {
            writer.uint32(16).bool(message.varargs);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof _m0.Reader ? input : new _m0.Reader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePositionalArgDescriptor();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.protoField = reader.string();
                    break;
                case 2:
                    message.varargs = reader.bool();
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON(object) {
        return {
            protoField: isSet(object.protoField) ? String(object.protoField) : "",
            varargs: isSet(object.varargs) ? Boolean(object.varargs) : false,
        };
    },
    toJSON(message) {
        const obj = {};
        message.protoField !== undefined && (obj.protoField = message.protoField);
        message.varargs !== undefined && (obj.varargs = message.varargs);
        return obj;
    },
    create(base) {
        return PositionalArgDescriptor.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePositionalArgDescriptor();
        message.protoField = object.protoField ?? "";
        message.varargs = object.varargs ?? false;
        return message;
    },
};
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
