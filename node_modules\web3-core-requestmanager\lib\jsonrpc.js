/*
    This file is part of web3.js.

    web3.js is free software: you can redistribute it and/or modify
    it under the terms of the GNU Lesser General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    web3.js is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERC<PERSON><PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public License
    along with web3.js.  If not, see <http://www.gnu.org/licenses/>.
*/
/** @file jsonrpc.js
 * @authors:
 *   <PERSON> <<EMAIL>>
 *   <PERSON><PERSON> <<EMAIL>>
 *   <PERSON> <<EMAIL>>
 * @date 2015
 */
"use strict";
// Initialize Jsonrpc as a simple object with utility functions.
var Jsonrpc = {
    // This is the starting counter for the Jsonrpc.id.
    // Pick a random number between 0 and the maximum safe integer
    messageId: Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)
};
/**
 * Should be called to valid json create payload object
 *
 * @method toPayload
 * @param {Function} method of jsonrpc call, required
 * @param {Array} params, an array of method params, optional
 * @returns {Object} valid jsonrpc payload object
 */
Jsonrpc.toPayload = function (method, params) {
    if (!method) {
        throw new Error('JSONRPC method should be specified for params: "' + JSON.stringify(params) + '"!');
    }
    if (Jsonrpc.messageId === Number.MAX_SAFE_INTEGER) {
        // if the maximum safe integer has been reached, restart from a random number
        Jsonrpc.messageId = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER);
    }
    else {
        // advance message ID
        Jsonrpc.messageId++;
    }
    return {
        jsonrpc: '2.0',
        id: Jsonrpc.messageId,
        method: method,
        params: params || []
    };
};
/**
 * Should be called to check if jsonrpc response is valid
 *
 * @method isValidResponse
 * @param {Object}
 * @returns {Boolean} true if response is valid, otherwise false
 */
Jsonrpc.isValidResponse = function (response) {
    return Array.isArray(response) ? response.every(validateSingleMessage) : validateSingleMessage(response);
    function validateSingleMessage(message) {
        return !!message &&
            !message.error &&
            message.jsonrpc === '2.0' &&
            (typeof message.id === 'number' || typeof message.id === 'string') &&
            message.result !== undefined; // only undefined is not valid json object
    }
};
/**
 * Should be called to create batch payload object
 *
 * @method toBatchPayload
 * @param {Array} messages, an array of objects with method (required) and params (optional) fields
 * @returns {Array} batch payload
 */
Jsonrpc.toBatchPayload = function (messages) {
    return messages.map(function (message) {
        return Jsonrpc.toPayload(message.method, message.params);
    });
};
module.exports = Jsonrpc;
