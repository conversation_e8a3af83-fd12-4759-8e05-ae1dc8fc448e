2025-05-19 09:07:19,373 - INFO - ================================================================================
2025-05-19 09:07:19,373 - INFO - 开始执行 RAI 买入交易 - 时间: 2025-05-19 09:07:19
2025-05-19 09:07:19,374 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-19 09:07:19,374 - INFO - 代币地址: ******************************************
2025-05-19 09:07:19,374 - INFO - 收到RAI买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-19 09:07:19,374 - INFO - RAI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 09:07:19,374 - INFO - RAI: 准备使用KyberSwap在ethereum上执行300.0USDT买入RAI交易
2025-05-19 09:07:19,374 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 09:07:19,374 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 09:07:19,374 - INFO - RAI: 准备调用swap_tokens函数，参数：
2025-05-19 09:07:19,374 - INFO -   chain: ethereum
2025-05-19 09:07:19,374 - INFO -   token_in: USDT
2025-05-19 09:07:19,374 - INFO -   token_out: ******************************************
2025-05-19 09:07:19,374 - INFO -   amount: 300.0
2025-05-19 09:07:19,374 - INFO -   slippage: 0.5%
2025-05-19 09:07:19,374 - INFO -   real: True
2025-05-19 09:07:21,794 - INFO - RAI: swap_tokens返回值类型: <class 'dict'>
2025-05-19 09:07:21,794 - INFO - RAI: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 09:07:21,794 - ERROR - RAI: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT
2025-05-19 09:07:21,795 - INFO - 读取到 126 条现有交易记录
2025-05-19 09:07:21,795 - INFO - 添加新交易记录: RAI (RAI_300.0_2025-05-19 09:07:19)
2025-05-19 09:07:21,797 - INFO - 成功保存 127 条交易记录
2025-05-19 09:07:21,797 - INFO - RAI: 买入交易处理完成，耗时: 2.42秒
2025-05-19 09:07:21,797 - INFO - ================================================================================
2025-05-19 12:41:55,693 - INFO - ================================================================================
2025-05-19 12:41:55,693 - INFO - 开始执行 RAI 买入交易 - 时间: 2025-05-19 12:41:55
2025-05-19 12:41:55,693 - INFO - 链: ethereum, 投入金额: 300.0 USDT
2025-05-19 12:41:55,693 - INFO - 代币地址: ******************************************
2025-05-19 12:41:55,693 - INFO - 收到RAI买入请求 - 链:ethereum, 投入:300.0USDT
2025-05-19 12:41:55,693 - INFO - RAI: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-19 12:41:55,694 - INFO - RAI: 准备使用KyberSwap在ethereum上执行300.0USDT买入RAI交易
2025-05-19 12:41:55,694 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-19 12:41:55,694 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-19 12:41:55,694 - INFO - RAI: 准备调用swap_tokens函数，参数：
2025-05-19 12:41:55,694 - INFO -   chain: ethereum
2025-05-19 12:41:55,694 - INFO -   token_in: USDT
2025-05-19 12:41:55,694 - INFO -   token_out: ******************************************
2025-05-19 12:41:55,694 - INFO -   amount: 300.0
2025-05-19 12:41:55,694 - INFO -   slippage: 0.5%
2025-05-19 12:41:55,694 - INFO -   real: True
2025-05-19 12:41:58,285 - INFO - RAI: swap_tokens返回值类型: <class 'dict'>
2025-05-19 12:41:58,285 - INFO - RAI: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 300.0, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '300000000', 'is_native_in': False, 'is_native_out': False}
2025-05-19 12:41:58,285 - ERROR - RAI: 交易失败 - 代币余额不足。请求: 300.0 USDT，可用: 67.81312 USDT
2025-05-19 12:41:58,286 - INFO - 读取到 141 条现有交易记录
2025-05-19 12:41:58,286 - INFO - 添加新交易记录: RAI (RAI_300.0_2025-05-19 12:41:55)
2025-05-19 12:41:58,288 - INFO - 成功保存 142 条交易记录
2025-05-19 12:41:58,289 - INFO - RAI: 买入交易处理完成，耗时: 2.60秒
2025-05-19 12:41:58,289 - INFO - ================================================================================
