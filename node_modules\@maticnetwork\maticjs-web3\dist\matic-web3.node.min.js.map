{"version": 3, "sources": ["webpack://matic-web3/webpack/bootstrap", "webpack://matic-web3/./node_modules/bn.js/lib/bn.js", "webpack://matic-web3/external \"web3\"", "webpack://matic-web3/external \"@maticnetwork/maticjs\"", "webpack://matic-web3/(webpack)/buildin/module.js", "webpack://matic-web3/external \"buffer\"", "webpack://matic-web3/./src/utils/matic_big_number.ts", "webpack://matic-web3/./src/utils/matic_tx_config_to_web3.ts", "webpack://matic-web3/./src/utils/web3_receipt_to_matic_receipt.ts", "webpack://matic-web3/./src/utils/web3_tx_to_matic_tx.ts", "webpack://matic-web3/./src/helpers/transaction_write_result.ts", "webpack://matic-web3/./src/web3/eth_method.ts", "webpack://matic-web3/./src/web3/eth_contract.ts", "webpack://matic-web3/./src/web3/web3_client.ts", "webpack://matic-web3/./src/index.ts"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "assert", "val", "msg", "Error", "inherits", "ctor", "superCtor", "super_", "TempCtor", "constructor", "BN", "number", "base", "endian", "isBN", "this", "negative", "words", "length", "red", "_init", "<PERSON><PERSON><PERSON>", "wordSize", "window", "e", "parseHex4Bits", "string", "index", "charCodeAt", "parseHexByte", "lowerBound", "parseBase", "str", "start", "end", "mul", "len", "Math", "min", "num", "Array", "isArray", "max", "left", "right", "cmp", "_initNumber", "_initArray", "toString", "replace", "_parseHex", "_parseBase", "toArray", "ceil", "j", "w", "off", "strip", "limbLen", "limbPow", "total", "mod", "word", "imuln", "_iaddn", "pow", "copy", "dest", "clone", "_expand", "size", "_normSign", "inspect", "zeros", "groupSizes", "groupBases", "smallMulTo", "self", "out", "a", "b", "lo", "carry", "k", "ncarry", "rword", "maxJ", "padding", "groupSize", "groupBase", "isZero", "modn", "idivn", "toNumber", "ret", "toJSON", "<PERSON><PERSON><PERSON><PERSON>", "toArrayLike", "ArrayType", "byteLength", "reqL<PERSON>th", "littleEndian", "res", "q", "andln", "i<PERSON>rn", "clz32", "_countBits", "_zeroBits", "bitLength", "hi", "zeroBits", "toTwos", "width", "abs", "inotn", "iaddn", "fromTwos", "testn", "notn", "ineg", "isNeg", "neg", "iuor", "ior", "or", "uor", "iuand", "iand", "and", "uand", "iuxor", "ixor", "xor", "uxor", "bytesNeeded", "bitsLeft", "setn", "bit", "wbit", "iadd", "isub", "add", "sub", "comb10MulTo", "mid", "a0", "al0", "ah0", "a1", "al1", "ah1", "a2", "al2", "ah2", "a3", "al3", "ah3", "a4", "al4", "ah4", "a5", "al5", "ah5", "a6", "al6", "ah6", "a7", "al7", "ah7", "a8", "al8", "ah8", "a9", "al9", "ah9", "b0", "bl0", "bh0", "b1", "bl1", "bh1", "b2", "bl2", "bh2", "b3", "bl3", "bh3", "b4", "bl4", "bh4", "b5", "bl5", "bh5", "b6", "bl6", "bh6", "b7", "bl7", "bh7", "b8", "bl8", "bh8", "b9", "bl9", "bh9", "w0", "imul", "w1", "w2", "w3", "w4", "w5", "w6", "w7", "w8", "w9", "w10", "w11", "w12", "w13", "w14", "w15", "w16", "w17", "w18", "jumboMulTo", "FFTM", "mulp", "x", "y", "mulTo", "hnc<PERSON><PERSON>", "bigMulTo", "makeRBT", "N", "revBin", "rb", "permute", "rbt", "rws", "iws", "rtws", "itws", "transform", "rtwdf", "cos", "PI", "itwdf", "sin", "rtwdf_", "itwdf_", "re", "ie", "ro", "io", "rx", "guessLen13b", "odd", "conjugate", "normalize13b", "ws", "round", "convert13b", "stub", "ph", "_", "rwst", "iwst", "nrws", "nrwst", "niwst", "rmws", "mulf", "muln", "sqr", "isqr", "toBitArray", "iushln", "bits", "carryMask", "new<PERSON>arry", "ishln", "hint", "extended", "h", "mask", "masked<PERSON><PERSON><PERSON>", "ishrn", "shln", "ushln", "shrn", "ushrn", "imaskn", "maskn", "isubn", "addn", "subn", "iabs", "_ishlnsubmul", "shift", "_wordDiv", "bhi", "diff", "qj", "div", "divmod", "positive", "divn", "umod", "divRound", "dm", "half", "r2", "acc", "egcd", "A", "B", "C", "D", "g", "isEven", "yp", "xp", "im", "isOdd", "jm", "gcd", "_invmp", "x1", "x2", "delta", "cmpn", "invm", "bincn", "ucmp", "gtn", "gt", "gten", "gte", "ltn", "lt", "lten", "lte", "eqn", "eq", "Red", "toRed", "ctx", "convertTo", "_forceRed", "fromRed", "convertFrom", "forceRed", "redAdd", "redIAdd", "redSub", "redISub", "redShl", "shl", "redMul", "_verify2", "redIMul", "redSqr", "_verify1", "redISqr", "redSqrt", "sqrt", "redInvm", "redNeg", "redPow", "primes", "k256", "p224", "p192", "p25519", "MPrime", "tmp", "_tmp", "K256", "P224", "P192", "P25519", "prime", "_prime", "Mont", "imod", "rinv", "minv", "ireduce", "rlen", "split", "imulK", "undefined", "_strip", "input", "output", "outLen", "prev", "next", "mod3", "one", "nOne", "lpow", "z", "inv", "wnd", "current", "currentLen", "mont", "u", "require", "webpackPolyfill", "deprecate", "paths", "children", "bn_", "MaticBigNumber", "maticTxRequestConfigToWeb3", "config", "toHex", "utils", "chainId", "data", "from", "gas", "gasLimit", "gasPrice", "nonce", "to", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "type", "hardfork", "web3ReceiptToMaticReceipt", "receipt", "blockHash", "blockNumber", "contractAddress", "cumulativeGasUsed", "gasUsed", "status", "transactionHash", "transactionIndex", "events", "logs", "logsBloom", "root", "web3TxToMaticTx", "tx", "maticTx", "hash", "promise", "receiptPromise", "Promise", "rej", "onTransactionReceipt", "onTransactionReceiptError", "getReceipt", "then", "txHashPromise", "onTransactionHash", "onTransactionError", "getTransactionHash", "once", "on", "address", "logger", "method", "read", "defaultBlock", "log", "write", "send", "estimateGas", "encodeABI", "contract", "methodName", "arguments", "methods", "args", "provider", "web3_", "eth", "sendTransaction", "getContract", "abi", "cont", "Contract", "getGasPrice", "getTransactionCount", "getAccounts", "get<PERSON>hainId", "net", "getId", "ensureTransactionNotNull_", "message", "getTransaction", "getTransactionReceipt", "getBlock", "blockHashOrBlockNumber", "getBalance", "getBlockWithTransaction", "result", "blockData", "transactions", "map", "sendRPCRequest", "request", "currentProvider", "error", "signTypedData", "signer", "typedData", "jsonrpc", "params", "id", "Date", "getTime", "payload", "String", "encodeParameters", "types", "decodeParameters", "hexString", "etheriumSha3", "soliditySha3", "hexToNumber", "hexToNumberString", "setup", "matic", "Web3Client"], "mappings": ";;;;;2BACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QA0Df,OArDAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,mBClFrD,2BACE,aAGA,SAASC,EAAQC,EAAKC,GACpB,IAAKD,EAAK,MAAM,IAAIE,MAAMD,GAAO,oBAKnC,SAASE,EAAUC,EAAMC,GACvBD,EAAKE,OAASD,EACd,IAAIE,EAAW,aACfA,EAASZ,UAAYU,EAAUV,UAC/BS,EAAKT,UAAY,IAAIY,EACrBH,EAAKT,UAAUa,YAAcJ,EAK/B,SAASK,EAAIC,EAAQC,EAAMC,GACzB,GAAIH,EAAGI,KAAKH,GACV,OAAOA,EAGTI,KAAKC,SAAW,EAChBD,KAAKE,MAAQ,KACbF,KAAKG,OAAS,EAGdH,KAAKI,IAAM,KAEI,OAAXR,IACW,OAATC,GAA0B,OAATA,IACnBC,EAASD,EACTA,EAAO,IAGTG,KAAKK,MAAMT,GAAU,EAAGC,GAAQ,GAAIC,GAAU,OAYlD,IAAIQ,EATkB,iBAAXtD,EACTA,EAAOD,QAAU4C,EAEjB5C,EAAQ4C,GAAKA,EAGfA,EAAGA,GAAKA,EACRA,EAAGY,SAAW,GAGd,IAEID,EADoB,oBAAXE,aAAmD,IAAlBA,OAAOF,OACxCE,OAAOF,OAEP,EAAQ,GAAUA,OAE7B,MAAOG,IAgIT,SAASC,EAAeC,EAAQC,GAC9B,IAAItD,EAAIqD,EAAOE,WAAWD,GAE1B,OAAItD,GAAK,IAAMA,GAAK,GACXA,EAAI,GAEFA,GAAK,IAAMA,GAAK,IAClBA,EAAI,GAGHA,EAAI,GAAM,GAItB,SAASwD,EAAcH,EAAQI,EAAYH,GACzC,IAAI7C,EAAI2C,EAAcC,EAAQC,GAI9B,OAHIA,EAAQ,GAAKG,IACfhD,GAAK2C,EAAcC,EAAQC,EAAQ,IAAM,GAEpC7C,EA8CT,SAASiD,EAAWC,EAAKC,EAAOC,EAAKC,GAGnC,IAFA,IAAIrD,EAAI,EACJsD,EAAMC,KAAKC,IAAIN,EAAId,OAAQgB,GACtBlE,EAAIiE,EAAOjE,EAAIoE,EAAKpE,IAAK,CAChC,IAAIK,EAAI2D,EAAIJ,WAAW5D,GAAK,GAE5Bc,GAAKqD,EAIHrD,GADET,GAAK,GACFA,EAAI,GAAK,GAGLA,GAAK,GACTA,EAAI,GAAK,GAITA,EAGT,OAAOS,EAnNT4B,EAAGI,KAAO,SAAeyB,GACvB,OAAIA,aAAe7B,GAIJ,OAAR6B,GAA+B,iBAARA,GAC5BA,EAAI9B,YAAYa,WAAaZ,EAAGY,UAAYkB,MAAMC,QAAQF,EAAItB,QAGlEP,EAAGgC,IAAM,SAAcC,EAAMC,GAC3B,OAAID,EAAKE,IAAID,GAAS,EAAUD,EACzBC,GAGTlC,EAAG4B,IAAM,SAAcK,EAAMC,GAC3B,OAAID,EAAKE,IAAID,GAAS,EAAUD,EACzBC,GAGTlC,EAAGd,UAAUwB,MAAQ,SAAeT,EAAQC,EAAMC,GAChD,GAAsB,iBAAXF,EACT,OAAOI,KAAK+B,YAAYnC,EAAQC,EAAMC,GAGxC,GAAsB,iBAAXF,EACT,OAAOI,KAAKgC,WAAWpC,EAAQC,EAAMC,GAG1B,QAATD,IACFA,EAAO,IAETZ,EAAOY,KAAiB,EAAPA,IAAaA,GAAQ,GAAKA,GAAQ,IAGnD,IAAIqB,EAAQ,EACM,OAFlBtB,EAASA,EAAOqC,WAAWC,QAAQ,OAAQ,KAEhC,KACThB,IACAlB,KAAKC,SAAW,GAGdiB,EAAQtB,EAAOO,SACJ,KAATN,EACFG,KAAKmC,UAAUvC,EAAQsB,EAAOpB,IAE9BE,KAAKoC,WAAWxC,EAAQC,EAAMqB,GACf,OAAXpB,GACFE,KAAKgC,WAAWhC,KAAKqC,UAAWxC,EAAMC,MAM9CH,EAAGd,UAAUkD,YAAc,SAAsBnC,EAAQC,EAAMC,GACzDF,EAAS,IACXI,KAAKC,SAAW,EAChBL,GAAUA,GAERA,EAAS,UACXI,KAAKE,MAAQ,CAAW,SAATN,GACfI,KAAKG,OAAS,GACLP,EAAS,kBAClBI,KAAKE,MAAQ,CACF,SAATN,EACCA,EAAS,SAAa,UAEzBI,KAAKG,OAAS,IAEdlB,EAAOW,EAAS,kBAChBI,KAAKE,MAAQ,CACF,SAATN,EACCA,EAAS,SAAa,SACvB,GAEFI,KAAKG,OAAS,GAGD,OAAXL,GAGJE,KAAKgC,WAAWhC,KAAKqC,UAAWxC,EAAMC,IAGxCH,EAAGd,UAAUmD,WAAa,SAAqBpC,EAAQC,EAAMC,GAG3D,GADAb,EAAgC,iBAAlBW,EAAOO,QACjBP,EAAOO,QAAU,EAGnB,OAFAH,KAAKE,MAAQ,CAAE,GACfF,KAAKG,OAAS,EACPH,KAGTA,KAAKG,OAASmB,KAAKgB,KAAK1C,EAAOO,OAAS,GACxCH,KAAKE,MAAQ,IAAIuB,MAAMzB,KAAKG,QAC5B,IAAK,IAAIlD,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAC/B+C,KAAKE,MAAMjD,GAAK,EAGlB,IAAIsF,EAAGC,EACHC,EAAM,EACV,GAAe,OAAX3C,EACF,IAAK7C,EAAI2C,EAAOO,OAAS,EAAGoC,EAAI,EAAGtF,GAAK,EAAGA,GAAK,EAC9CuF,EAAI5C,EAAO3C,GAAM2C,EAAO3C,EAAI,IAAM,EAAM2C,EAAO3C,EAAI,IAAM,GACzD+C,KAAKE,MAAMqC,IAAOC,GAAKC,EAAO,SAC9BzC,KAAKE,MAAMqC,EAAI,GAAMC,IAAO,GAAKC,EAAQ,UACzCA,GAAO,KACI,KACTA,GAAO,GACPF,UAGC,GAAe,OAAXzC,EACT,IAAK7C,EAAI,EAAGsF,EAAI,EAAGtF,EAAI2C,EAAOO,OAAQlD,GAAK,EACzCuF,EAAI5C,EAAO3C,GAAM2C,EAAO3C,EAAI,IAAM,EAAM2C,EAAO3C,EAAI,IAAM,GACzD+C,KAAKE,MAAMqC,IAAOC,GAAKC,EAAO,SAC9BzC,KAAKE,MAAMqC,EAAI,GAAMC,IAAO,GAAKC,EAAQ,UACzCA,GAAO,KACI,KACTA,GAAO,GACPF,KAIN,OAAOvC,KAAK0C,SAyBd/C,EAAGd,UAAUsD,UAAY,SAAoBvC,EAAQsB,EAAOpB,GAE1DE,KAAKG,OAASmB,KAAKgB,MAAM1C,EAAOO,OAASe,GAAS,GAClDlB,KAAKE,MAAQ,IAAIuB,MAAMzB,KAAKG,QAC5B,IAAK,IAAIlD,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAC/B+C,KAAKE,MAAMjD,GAAK,EAIlB,IAGIuF,EAHAC,EAAM,EACNF,EAAI,EAGR,GAAe,OAAXzC,EACF,IAAK7C,EAAI2C,EAAOO,OAAS,EAAGlD,GAAKiE,EAAOjE,GAAK,EAC3CuF,EAAI1B,EAAalB,EAAQsB,EAAOjE,IAAMwF,EACtCzC,KAAKE,MAAMqC,IAAU,SAAJC,EACbC,GAAO,IACTA,GAAO,GACPF,GAAK,EACLvC,KAAKE,MAAMqC,IAAMC,IAAM,IAEvBC,GAAO,OAKX,IAAKxF,GADa2C,EAAOO,OAASe,GACX,GAAM,EAAIA,EAAQ,EAAIA,EAAOjE,EAAI2C,EAAOO,OAAQlD,GAAK,EAC1EuF,EAAI1B,EAAalB,EAAQsB,EAAOjE,IAAMwF,EACtCzC,KAAKE,MAAMqC,IAAU,SAAJC,EACbC,GAAO,IACTA,GAAO,GACPF,GAAK,EACLvC,KAAKE,MAAMqC,IAAMC,IAAM,IAEvBC,GAAO,EAKbzC,KAAK0C,SA2BP/C,EAAGd,UAAUuD,WAAa,SAAqBxC,EAAQC,EAAMqB,GAE3DlB,KAAKE,MAAQ,CAAE,GACfF,KAAKG,OAAS,EAGd,IAAK,IAAIwC,EAAU,EAAGC,EAAU,EAAGA,GAAW,SAAWA,GAAW/C,EAClE8C,IAEFA,IACAC,EAAWA,EAAU/C,EAAQ,EAO7B,IALA,IAAIgD,EAAQjD,EAAOO,OAASe,EACxB4B,EAAMD,EAAQF,EACdxB,EAAMG,KAAKC,IAAIsB,EAAOA,EAAQC,GAAO5B,EAErC6B,EAAO,EACF9F,EAAIiE,EAAOjE,EAAIkE,EAAKlE,GAAK0F,EAChCI,EAAO/B,EAAUpB,EAAQ3C,EAAGA,EAAI0F,EAAS9C,GAEzCG,KAAKgD,MAAMJ,GACP5C,KAAKE,MAAM,GAAK6C,EAAO,SACzB/C,KAAKE,MAAM,IAAM6C,EAEjB/C,KAAKiD,OAAOF,GAIhB,GAAY,IAARD,EAAW,CACb,IAAII,EAAM,EAGV,IAFAH,EAAO/B,EAAUpB,EAAQ3C,EAAG2C,EAAOO,OAAQN,GAEtC5C,EAAI,EAAGA,EAAI6F,EAAK7F,IACnBiG,GAAOrD,EAGTG,KAAKgD,MAAME,GACPlD,KAAKE,MAAM,GAAK6C,EAAO,SACzB/C,KAAKE,MAAM,IAAM6C,EAEjB/C,KAAKiD,OAAOF,GAIhB/C,KAAK0C,SAGP/C,EAAGd,UAAUsE,KAAO,SAAeC,GACjCA,EAAKlD,MAAQ,IAAIuB,MAAMzB,KAAKG,QAC5B,IAAK,IAAIlD,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAC/BmG,EAAKlD,MAAMjD,GAAK+C,KAAKE,MAAMjD,GAE7BmG,EAAKjD,OAASH,KAAKG,OACnBiD,EAAKnD,SAAWD,KAAKC,SACrBmD,EAAKhD,IAAMJ,KAAKI,KAGlBT,EAAGd,UAAUwE,MAAQ,WACnB,IAAItF,EAAI,IAAI4B,EAAG,MAEf,OADAK,KAAKmD,KAAKpF,GACHA,GAGT4B,EAAGd,UAAUyE,QAAU,SAAkBC,GACvC,KAAOvD,KAAKG,OAASoD,GACnBvD,KAAKE,MAAMF,KAAKG,UAAY,EAE9B,OAAOH,MAITL,EAAGd,UAAU6D,MAAQ,WACnB,KAAO1C,KAAKG,OAAS,GAAqC,IAAhCH,KAAKE,MAAMF,KAAKG,OAAS,IACjDH,KAAKG,SAEP,OAAOH,KAAKwD,aAGd7D,EAAGd,UAAU2E,UAAY,WAKvB,OAHoB,IAAhBxD,KAAKG,QAAkC,IAAlBH,KAAKE,MAAM,KAClCF,KAAKC,SAAW,GAEXD,MAGTL,EAAGd,UAAU4E,QAAU,WACrB,OAAQzD,KAAKI,IAAM,UAAY,SAAWJ,KAAKiC,SAAS,IAAM,KAiChE,IAAIyB,EAAQ,CACV,GACA,IACA,KACA,MACA,OACA,QACA,SACA,UACA,WACA,YACA,aACA,cACA,eACA,gBACA,iBACA,kBACA,mBACA,oBACA,qBACA,sBACA,uBACA,wBACA,yBACA,0BACA,2BACA,6BAGEC,EAAa,CACf,EAAG,EACH,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EACvB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAClB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAClB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAClB,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGhBC,EAAa,CACf,EAAG,EACH,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAC5D,SAAU,IAAU,SAAU,SAAU,SAAU,QAAS,SAC3D,SAAU,SAAU,SAAU,SAAU,KAAU,QAAS,QAC3D,QAAS,QAAS,QAAS,SAAU,SAAU,SAAU,SACzD,MAAU,SAAU,SAAU,SAAU,SAAU,SAAU,UAsjB9D,SAASC,EAAYC,EAAMtC,EAAKuC,GAC9BA,EAAI9D,SAAWuB,EAAIvB,SAAW6D,EAAK7D,SACnC,IAAIoB,EAAOyC,EAAK3D,OAASqB,EAAIrB,OAAU,EACvC4D,EAAI5D,OAASkB,EACbA,EAAOA,EAAM,EAAK,EAGlB,IAAI2C,EAAoB,EAAhBF,EAAK5D,MAAM,GACf+D,EAAmB,EAAfzC,EAAItB,MAAM,GACdnC,EAAIiG,EAAIC,EAERC,EAAS,SAAJnG,EACLoG,EAASpG,EAAI,SAAa,EAC9BgG,EAAI7D,MAAM,GAAKgE,EAEf,IAAK,IAAIE,EAAI,EAAGA,EAAI/C,EAAK+C,IAAK,CAM5B,IAHA,IAAIC,EAASF,IAAU,GACnBG,EAAgB,SAARH,EACRI,EAAOjD,KAAKC,IAAI6C,EAAG5C,EAAIrB,OAAS,GAC3BoC,EAAIjB,KAAKK,IAAI,EAAGyC,EAAIN,EAAK3D,OAAS,GAAIoC,GAAKgC,EAAMhC,IAAK,CAC7D,IAAItF,EAAKmH,EAAI7B,EAAK,EAIlB8B,IADAtG,GAFAiG,EAAoB,EAAhBF,EAAK5D,MAAMjD,KACfgH,EAAmB,EAAfzC,EAAItB,MAAMqC,IACF+B,GACG,SAAa,EAC5BA,EAAY,SAAJvG,EAEVgG,EAAI7D,MAAMkE,GAAa,EAARE,EACfH,EAAiB,EAATE,EAQV,OANc,IAAVF,EACFJ,EAAI7D,MAAMkE,GAAa,EAARD,EAEfJ,EAAI5D,SAGC4D,EAAIrB,QAzlBb/C,EAAGd,UAAUoD,SAAW,SAAmBpC,EAAM2E,GAI/C,IAAIT,EACJ,GAHAS,EAAoB,EAAVA,GAAe,EAGZ,MAJb3E,EAAOA,GAAQ,KAIa,QAATA,EAAgB,CACjCkE,EAAM,GAGN,IAFA,IAAItB,EAAM,EACN0B,EAAQ,EACHlH,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAAK,CACpC,IAAIuF,EAAIxC,KAAKE,MAAMjD,GACf8F,GAA+B,UAArBP,GAAKC,EAAO0B,IAAmBlC,SAAS,IAGpD8B,EADY,KADdI,EAAS3B,IAAO,GAAKC,EAAQ,WACVxF,IAAM+C,KAAKG,OAAS,EAC/BuD,EAAM,EAAIX,EAAK5C,QAAU4C,EAAOgB,EAEhChB,EAAOgB,GAEftB,GAAO,IACI,KACTA,GAAO,GACPxF,KAMJ,IAHc,IAAVkH,IACFJ,EAAMI,EAAMlC,SAAS,IAAM8B,GAEtBA,EAAI5D,OAASqE,GAAY,GAC9BT,EAAM,IAAMA,EAKd,OAHsB,IAAlB/D,KAAKC,WACP8D,EAAM,IAAMA,GAEPA,EAGT,GAAIlE,KAAiB,EAAPA,IAAaA,GAAQ,GAAKA,GAAQ,GAAI,CAElD,IAAI4E,EAAYd,EAAW9D,GAEvB6E,EAAYd,EAAW/D,GAC3BkE,EAAM,GACN,IAAIzG,EAAI0C,KAAKqD,QAEb,IADA/F,EAAE2C,SAAW,GACL3C,EAAEqH,UAAU,CAClB,IAAI5G,EAAIT,EAAEsH,KAAKF,GAAWzC,SAASpC,GAMjCkE,GALFzG,EAAIA,EAAEuH,MAAMH,IAELC,SAGC5G,EAAIgG,EAFJL,EAAMe,EAAY1G,EAAEoC,QAAUpC,EAAIgG,EAQ5C,IAHI/D,KAAK2E,WACPZ,EAAM,IAAMA,GAEPA,EAAI5D,OAASqE,GAAY,GAC9BT,EAAM,IAAMA,EAKd,OAHsB,IAAlB/D,KAAKC,WACP8D,EAAM,IAAMA,GAEPA,EAGT9E,GAAO,EAAO,oCAGhBU,EAAGd,UAAUiG,SAAW,WACtB,IAAIC,EAAM/E,KAAKE,MAAM,GASrB,OARoB,IAAhBF,KAAKG,OACP4E,GAAuB,SAAhB/E,KAAKE,MAAM,GACO,IAAhBF,KAAKG,QAAkC,IAAlBH,KAAKE,MAAM,GAEzC6E,GAAO,iBAAoC,SAAhB/E,KAAKE,MAAM,GAC7BF,KAAKG,OAAS,GACvBlB,GAAO,EAAO,8CAEU,IAAlBe,KAAKC,UAAmB8E,EAAMA,GAGxCpF,EAAGd,UAAUmG,OAAS,WACpB,OAAOhF,KAAKiC,SAAS,KAGvBtC,EAAGd,UAAUoG,SAAW,SAAmBnF,EAAQK,GAEjD,OADAlB,OAAyB,IAAXqB,GACPN,KAAKkF,YAAY5E,EAAQR,EAAQK,IAG1CR,EAAGd,UAAUwD,QAAU,SAAkBvC,EAAQK,GAC/C,OAAOH,KAAKkF,YAAYzD,MAAO3B,EAAQK,IAGzCR,EAAGd,UAAUqG,YAAc,SAAsBC,EAAWrF,EAAQK,GAClE,IAAIiF,EAAapF,KAAKoF,aAClBC,EAAYlF,GAAUmB,KAAKK,IAAI,EAAGyD,GACtCnG,EAAOmG,GAAcC,EAAW,yCAChCpG,EAAOoG,EAAY,EAAG,+BAEtBrF,KAAK0C,QACL,IAGIuB,EAAGhH,EAHHqI,EAA0B,OAAXxF,EACfyF,EAAM,IAAIJ,EAAUE,GAGpBG,EAAIxF,KAAKqD,QACb,GAAKiC,EAYE,CACL,IAAKrI,EAAI,GAAIuI,EAAEb,SAAU1H,IACvBgH,EAAIuB,EAAEC,MAAM,KACZD,EAAEE,OAAO,GAETH,EAAItI,GAAKgH,EAGX,KAAOhH,EAAIoI,EAAWpI,IACpBsI,EAAItI,GAAK,MArBM,CAEjB,IAAKA,EAAI,EAAGA,EAAIoI,EAAYD,EAAYnI,IACtCsI,EAAItI,GAAK,EAGX,IAAKA,EAAI,GAAIuI,EAAEb,SAAU1H,IACvBgH,EAAIuB,EAAEC,MAAM,KACZD,EAAEE,OAAO,GAETH,EAAIF,EAAYpI,EAAI,GAAKgH,EAe7B,OAAOsB,GAGLjE,KAAKqE,MACPhG,EAAGd,UAAU+G,WAAa,SAAqBpD,GAC7C,OAAO,GAAKlB,KAAKqE,MAAMnD,IAGzB7C,EAAGd,UAAU+G,WAAa,SAAqBpD,GAC7C,IAAIrE,EAAIqE,EACJzE,EAAI,EAiBR,OAhBII,GAAK,OACPJ,GAAK,GACLI,KAAO,IAELA,GAAK,KACPJ,GAAK,EACLI,KAAO,GAELA,GAAK,IACPJ,GAAK,EACLI,KAAO,GAELA,GAAK,IACPJ,GAAK,EACLI,KAAO,GAEFJ,EAAII,GAIfwB,EAAGd,UAAUgH,UAAY,SAAoBrD,GAE3C,GAAU,IAANA,EAAS,OAAO,GAEpB,IAAIrE,EAAIqE,EACJzE,EAAI,EAoBR,OAnBqB,IAAZ,KAAJI,KACHJ,GAAK,GACLI,KAAO,IAEU,IAAV,IAAJA,KACHJ,GAAK,EACLI,KAAO,GAES,IAAT,GAAJA,KACHJ,GAAK,EACLI,KAAO,GAES,IAAT,EAAJA,KACHJ,GAAK,EACLI,KAAO,GAES,IAAT,EAAJA,IACHJ,IAEKA,GAIT4B,EAAGd,UAAUiH,UAAY,WACvB,IAAItD,EAAIxC,KAAKE,MAAMF,KAAKG,OAAS,GAC7B4F,EAAK/F,KAAK4F,WAAWpD,GACzB,OAA2B,IAAnBxC,KAAKG,OAAS,GAAU4F,GAiBlCpG,EAAGd,UAAUmH,SAAW,WACtB,GAAIhG,KAAK2E,SAAU,OAAO,EAG1B,IADA,IAAI5G,EAAI,EACCd,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAAK,CACpC,IAAIgH,EAAIjE,KAAK6F,UAAU7F,KAAKE,MAAMjD,IAElC,GADAc,GAAKkG,EACK,KAANA,EAAU,MAEhB,OAAOlG,GAGT4B,EAAGd,UAAUuG,WAAa,WACxB,OAAO9D,KAAKgB,KAAKtC,KAAK8F,YAAc,IAGtCnG,EAAGd,UAAUoH,OAAS,SAAiBC,GACrC,OAAsB,IAAlBlG,KAAKC,SACAD,KAAKmG,MAAMC,MAAMF,GAAOG,MAAM,GAEhCrG,KAAKqD,SAGd1D,EAAGd,UAAUyH,SAAW,SAAmBJ,GACzC,OAAIlG,KAAKuG,MAAML,EAAQ,GACdlG,KAAKwG,KAAKN,GAAOG,MAAM,GAAGI,OAE5BzG,KAAKqD,SAGd1D,EAAGd,UAAU6H,MAAQ,WACnB,OAAyB,IAAlB1G,KAAKC,UAIdN,EAAGd,UAAU8H,IAAM,WACjB,OAAO3G,KAAKqD,QAAQoD,QAGtB9G,EAAGd,UAAU4H,KAAO,WAKlB,OAJKzG,KAAK2E,WACR3E,KAAKC,UAAY,GAGZD,MAITL,EAAGd,UAAU+H,KAAO,SAAepF,GACjC,KAAOxB,KAAKG,OAASqB,EAAIrB,QACvBH,KAAKE,MAAMF,KAAKG,UAAY,EAG9B,IAAK,IAAIlD,EAAI,EAAGA,EAAIuE,EAAIrB,OAAQlD,IAC9B+C,KAAKE,MAAMjD,GAAK+C,KAAKE,MAAMjD,GAAKuE,EAAItB,MAAMjD,GAG5C,OAAO+C,KAAK0C,SAGd/C,EAAGd,UAAUgI,IAAM,SAAcrF,GAE/B,OADAvC,EAA0C,IAAlCe,KAAKC,SAAWuB,EAAIvB,WACrBD,KAAK4G,KAAKpF,IAInB7B,EAAGd,UAAUiI,GAAK,SAAatF,GAC7B,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQwD,IAAIrF,GAC/CA,EAAI6B,QAAQwD,IAAI7G,OAGzBL,EAAGd,UAAUkI,IAAM,SAAcvF,GAC/B,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQuD,KAAKpF,GAChDA,EAAI6B,QAAQuD,KAAK5G,OAI1BL,EAAGd,UAAUmI,MAAQ,SAAgBxF,GAEnC,IAAIyC,EAEFA,EADEjE,KAAKG,OAASqB,EAAIrB,OAChBqB,EAEAxB,KAGN,IAAK,IAAI/C,EAAI,EAAGA,EAAIgH,EAAE9D,OAAQlD,IAC5B+C,KAAKE,MAAMjD,GAAK+C,KAAKE,MAAMjD,GAAKuE,EAAItB,MAAMjD,GAK5C,OAFA+C,KAAKG,OAAS8D,EAAE9D,OAETH,KAAK0C,SAGd/C,EAAGd,UAAUoI,KAAO,SAAezF,GAEjC,OADAvC,EAA0C,IAAlCe,KAAKC,SAAWuB,EAAIvB,WACrBD,KAAKgH,MAAMxF,IAIpB7B,EAAGd,UAAUqI,IAAM,SAAc1F,GAC/B,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQ4D,KAAKzF,GAChDA,EAAI6B,QAAQ4D,KAAKjH,OAG1BL,EAAGd,UAAUsI,KAAO,SAAe3F,GACjC,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQ2D,MAAMxF,GACjDA,EAAI6B,QAAQ2D,MAAMhH,OAI3BL,EAAGd,UAAUuI,MAAQ,SAAgB5F,GAEnC,IAAIwC,EACAC,EACAjE,KAAKG,OAASqB,EAAIrB,QACpB6D,EAAIhE,KACJiE,EAAIzC,IAEJwC,EAAIxC,EACJyC,EAAIjE,MAGN,IAAK,IAAI/C,EAAI,EAAGA,EAAIgH,EAAE9D,OAAQlD,IAC5B+C,KAAKE,MAAMjD,GAAK+G,EAAE9D,MAAMjD,GAAKgH,EAAE/D,MAAMjD,GAGvC,GAAI+C,OAASgE,EACX,KAAO/G,EAAI+G,EAAE7D,OAAQlD,IACnB+C,KAAKE,MAAMjD,GAAK+G,EAAE9D,MAAMjD,GAM5B,OAFA+C,KAAKG,OAAS6D,EAAE7D,OAETH,KAAK0C,SAGd/C,EAAGd,UAAUwI,KAAO,SAAe7F,GAEjC,OADAvC,EAA0C,IAAlCe,KAAKC,SAAWuB,EAAIvB,WACrBD,KAAKoH,MAAM5F,IAIpB7B,EAAGd,UAAUyI,IAAM,SAAc9F,GAC/B,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQgE,KAAK7F,GAChDA,EAAI6B,QAAQgE,KAAKrH,OAG1BL,EAAGd,UAAU0I,KAAO,SAAe/F,GACjC,OAAIxB,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQ+D,MAAM5F,GACjDA,EAAI6B,QAAQ+D,MAAMpH,OAI3BL,EAAGd,UAAUuH,MAAQ,SAAgBF,GACnCjH,EAAwB,iBAAViH,GAAsBA,GAAS,GAE7C,IAAIsB,EAAsC,EAAxBlG,KAAKgB,KAAK4D,EAAQ,IAChCuB,EAAWvB,EAAQ,GAGvBlG,KAAKsD,QAAQkE,GAETC,EAAW,GACbD,IAIF,IAAK,IAAIvK,EAAI,EAAGA,EAAIuK,EAAavK,IAC/B+C,KAAKE,MAAMjD,GAAsB,UAAhB+C,KAAKE,MAAMjD,GAS9B,OALIwK,EAAW,IACbzH,KAAKE,MAAMjD,IAAM+C,KAAKE,MAAMjD,GAAM,UAAc,GAAKwK,GAIhDzH,KAAK0C,SAGd/C,EAAGd,UAAU2H,KAAO,SAAeN,GACjC,OAAOlG,KAAKqD,QAAQ+C,MAAMF,IAI5BvG,EAAGd,UAAU6I,KAAO,SAAeC,EAAKzI,GACtCD,EAAsB,iBAAR0I,GAAoBA,GAAO,GAEzC,IAAIlF,EAAOkF,EAAM,GAAM,EACnBC,EAAOD,EAAM,GAUjB,OARA3H,KAAKsD,QAAQb,EAAM,GAGjBzC,KAAKE,MAAMuC,GADTvD,EACgBc,KAAKE,MAAMuC,GAAQ,GAAKmF,EAExB5H,KAAKE,MAAMuC,KAAS,GAAKmF,GAGtC5H,KAAK0C,SAId/C,EAAGd,UAAUgJ,KAAO,SAAerG,GACjC,IAAIzD,EAkBAiG,EAAGC,EAfP,GAAsB,IAAlBjE,KAAKC,UAAmC,IAAjBuB,EAAIvB,SAI7B,OAHAD,KAAKC,SAAW,EAChBlC,EAAIiC,KAAK8H,KAAKtG,GACdxB,KAAKC,UAAY,EACVD,KAAKwD,YAGP,GAAsB,IAAlBxD,KAAKC,UAAmC,IAAjBuB,EAAIvB,SAIpC,OAHAuB,EAAIvB,SAAW,EACflC,EAAIiC,KAAK8H,KAAKtG,GACdA,EAAIvB,SAAW,EACRlC,EAAEyF,YAKPxD,KAAKG,OAASqB,EAAIrB,QACpB6D,EAAIhE,KACJiE,EAAIzC,IAEJwC,EAAIxC,EACJyC,EAAIjE,MAIN,IADA,IAAImE,EAAQ,EACHlH,EAAI,EAAGA,EAAIgH,EAAE9D,OAAQlD,IAC5Bc,GAAkB,EAAbiG,EAAE9D,MAAMjD,KAAwB,EAAbgH,EAAE/D,MAAMjD,IAAUkH,EAC1CnE,KAAKE,MAAMjD,GAAS,SAAJc,EAChBoG,EAAQpG,IAAM,GAEhB,KAAiB,IAAVoG,GAAelH,EAAI+G,EAAE7D,OAAQlD,IAClCc,GAAkB,EAAbiG,EAAE9D,MAAMjD,IAAUkH,EACvBnE,KAAKE,MAAMjD,GAAS,SAAJc,EAChBoG,EAAQpG,IAAM,GAIhB,GADAiC,KAAKG,OAAS6D,EAAE7D,OACF,IAAVgE,EACFnE,KAAKE,MAAMF,KAAKG,QAAUgE,EAC1BnE,KAAKG,cAEA,GAAI6D,IAAMhE,KACf,KAAO/C,EAAI+G,EAAE7D,OAAQlD,IACnB+C,KAAKE,MAAMjD,GAAK+G,EAAE9D,MAAMjD,GAI5B,OAAO+C,MAITL,EAAGd,UAAUkJ,IAAM,SAAcvG,GAC/B,IAAI+D,EACJ,OAAqB,IAAjB/D,EAAIvB,UAAoC,IAAlBD,KAAKC,UAC7BuB,EAAIvB,SAAW,EACfsF,EAAMvF,KAAKgI,IAAIxG,GACfA,EAAIvB,UAAY,EACTsF,GACmB,IAAjB/D,EAAIvB,UAAoC,IAAlBD,KAAKC,UACpCD,KAAKC,SAAW,EAChBsF,EAAM/D,EAAIwG,IAAIhI,MACdA,KAAKC,SAAW,EACTsF,GAGLvF,KAAKG,OAASqB,EAAIrB,OAAeH,KAAKqD,QAAQwE,KAAKrG,GAEhDA,EAAI6B,QAAQwE,KAAK7H,OAI1BL,EAAGd,UAAUiJ,KAAO,SAAetG,GAEjC,GAAqB,IAAjBA,EAAIvB,SAAgB,CACtBuB,EAAIvB,SAAW,EACf,IAAIlC,EAAIiC,KAAK6H,KAAKrG,GAElB,OADAA,EAAIvB,SAAW,EACRlC,EAAEyF,YAGJ,GAAsB,IAAlBxD,KAAKC,SAId,OAHAD,KAAKC,SAAW,EAChBD,KAAK6H,KAAKrG,GACVxB,KAAKC,SAAW,EACTD,KAAKwD,YAId,IAWIQ,EAAGC,EAXHnC,EAAM9B,KAAK8B,IAAIN,GAGnB,GAAY,IAARM,EAIF,OAHA9B,KAAKC,SAAW,EAChBD,KAAKG,OAAS,EACdH,KAAKE,MAAM,GAAK,EACTF,KAKL8B,EAAM,GACRkC,EAAIhE,KACJiE,EAAIzC,IAEJwC,EAAIxC,EACJyC,EAAIjE,MAIN,IADA,IAAImE,EAAQ,EACHlH,EAAI,EAAGA,EAAIgH,EAAE9D,OAAQlD,IAE5BkH,GADApG,GAAkB,EAAbiG,EAAE9D,MAAMjD,KAAwB,EAAbgH,EAAE/D,MAAMjD,IAAUkH,IAC7B,GACbnE,KAAKE,MAAMjD,GAAS,SAAJc,EAElB,KAAiB,IAAVoG,GAAelH,EAAI+G,EAAE7D,OAAQlD,IAElCkH,GADApG,GAAkB,EAAbiG,EAAE9D,MAAMjD,IAAUkH,IACV,GACbnE,KAAKE,MAAMjD,GAAS,SAAJc,EAIlB,GAAc,IAAVoG,GAAelH,EAAI+G,EAAE7D,QAAU6D,IAAMhE,KACvC,KAAO/C,EAAI+G,EAAE7D,OAAQlD,IACnB+C,KAAKE,MAAMjD,GAAK+G,EAAE9D,MAAMjD,GAU5B,OANA+C,KAAKG,OAASmB,KAAKK,IAAI3B,KAAKG,OAAQlD,GAEhC+G,IAAMhE,OACRA,KAAKC,SAAW,GAGXD,KAAK0C,SAId/C,EAAGd,UAAUmJ,IAAM,SAAcxG,GAC/B,OAAOxB,KAAKqD,QAAQyE,KAAKtG,IA+C3B,IAAIyG,EAAc,SAAsBnE,EAAMtC,EAAKuC,GACjD,IAIIG,EACAgE,EACAnC,EANA/B,EAAIF,EAAK5D,MACT+D,EAAIzC,EAAItB,MACRxC,EAAIqG,EAAI7D,MACR5C,EAAI,EAIJ6K,EAAY,EAAPnE,EAAE,GACPoE,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPtE,EAAE,GACPuE,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPzE,EAAE,GACP0E,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAP5E,EAAE,GACP6E,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAP/E,EAAE,GACPgF,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPlF,EAAE,GACPmF,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPrF,EAAE,GACPsF,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPxF,EAAE,GACPyF,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAP3F,EAAE,GACP4F,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAP9F,EAAE,GACP+F,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPhG,EAAE,GACPiG,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPnG,EAAE,GACPoG,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPtG,EAAE,GACPuG,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAPzG,EAAE,GACP0G,EAAW,KAALD,EACNE,EAAMF,IAAO,GACbG,EAAY,EAAP5G,EAAE,GACP6G,EAAW,KAALD,EACNE,GAAMF,IAAO,GACbG,GAAY,EAAP/G,EAAE,GACPgH,GAAW,KAALD,GACNE,GAAMF,KAAO,GACbG,GAAY,EAAPlH,EAAE,GACPmH,GAAW,KAALD,GACNE,GAAMF,KAAO,GACbG,GAAY,EAAPrH,EAAE,GACPsH,GAAW,KAALD,GACNE,GAAMF,KAAO,GACbG,GAAY,EAAPxH,EAAE,GACPyH,GAAW,KAALD,GACNE,GAAMF,KAAO,GACbG,GAAY,EAAP3H,EAAE,GACP4H,GAAW,KAALD,GACNE,GAAMF,KAAO,GAEjB7H,EAAI9D,SAAW6D,EAAK7D,SAAWuB,EAAIvB,SACnC8D,EAAI5D,OAAS,GAMb,IAAI4L,IAAQzO,GAJZ4G,EAAK5C,KAAK0K,KAAK5D,EAAK8B,IAIE,KAAa,MAFnChC,GADAA,EAAM5G,KAAK0K,KAAK5D,EAAK+B,IACR7I,KAAK0K,KAAK3D,EAAK6B,GAAQ,KAEU,IAAO,EACrD5M,IAFAyI,EAAKzE,KAAK0K,KAAK3D,EAAK8B,KAEPjC,IAAQ,IAAO,IAAM6D,KAAO,IAAO,EAChDA,IAAM,SAEN7H,EAAK5C,KAAK0K,KAAKzD,EAAK2B,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKzD,EAAK4B,IACR7I,KAAK0K,KAAKxD,EAAK0B,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKxD,EAAK2B,GAKpB,IAAI8B,IAAQ3O,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKiC,GAAQ,GAIZ,KAAa,MAFnCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKkC,GAAQ,GACvBhJ,KAAK0K,KAAK3D,EAAKgC,GAAQ,KAEU,IAAO,EACrD/M,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKiC,GAAQ,IAErBpC,IAAQ,IAAO,IAAM+D,KAAO,IAAO,EAChDA,IAAM,SAEN/H,EAAK5C,KAAK0K,KAAKtD,EAAKwB,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKtD,EAAKyB,IACR7I,KAAK0K,KAAKrD,EAAKuB,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKrD,EAAKwB,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAK8B,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAK+B,GAAQ,GACvBhJ,KAAK0K,KAAKxD,EAAK6B,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAK8B,GAAQ,EAKlC,IAAI4B,IAAQ5O,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKoC,GAAQ,GAIZ,KAAa,MAFnCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKqC,GAAQ,GACvBnJ,KAAK0K,KAAK3D,EAAKmC,GAAQ,KAEU,IAAO,EACrDlN,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKoC,GAAQ,IAErBvC,IAAQ,IAAO,IAAMgE,KAAO,IAAO,EAChDA,IAAM,SAENhI,EAAK5C,KAAK0K,KAAKnD,EAAKqB,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKnD,EAAKsB,IACR7I,KAAK0K,KAAKlD,EAAKoB,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKlD,EAAKqB,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAK2B,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAK4B,GAAQ,GACvBhJ,KAAK0K,KAAKrD,EAAK0B,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAK2B,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKiC,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKkC,GAAQ,GACvBnJ,KAAK0K,KAAKxD,EAAKgC,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKiC,GAAQ,EAKlC,IAAI0B,IAAQ7O,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKuC,GAAQ,GAIZ,KAAa,MAFnCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKwC,GAAQ,GACvBtJ,KAAK0K,KAAK3D,EAAKsC,GAAQ,KAEU,IAAO,EACrDrN,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKuC,GAAQ,IAErB1C,IAAQ,IAAO,IAAMiE,KAAO,IAAO,EAChDA,IAAM,SAENjI,EAAK5C,KAAK0K,KAAKhD,EAAKkB,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKhD,EAAKmB,IACR7I,KAAK0K,KAAK/C,EAAKiB,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAK/C,EAAKkB,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAKwB,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAKyB,GAAQ,GACvBhJ,KAAK0K,KAAKlD,EAAKuB,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAKwB,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAK8B,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAK+B,GAAQ,GACvBnJ,KAAK0K,KAAKrD,EAAK6B,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAK8B,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKoC,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKqC,GAAQ,GACvBtJ,KAAK0K,KAAKxD,EAAKmC,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKoC,GAAQ,EAKlC,IAAIwB,IAAQ9O,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAK0C,GAAQ,GAIZ,KAAa,MAFnC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAK2C,IAAQ,GACvBzJ,KAAK0K,KAAK3D,EAAKyC,GAAQ,KAEU,IAAO,EACrDxN,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAK0C,IAAQ,IAErB7C,IAAQ,IAAO,IAAMkE,KAAO,IAAO,EAChDA,IAAM,SAENlI,EAAK5C,KAAK0K,KAAK7C,EAAKe,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAK7C,EAAKgB,IACR7I,KAAK0K,KAAK5C,EAAKc,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAK5C,EAAKe,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAKqB,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAKsB,GAAQ,GACvBhJ,KAAK0K,KAAK/C,EAAKoB,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAKqB,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAK2B,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAK4B,GAAQ,GACvBnJ,KAAK0K,KAAKlD,EAAK0B,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAK2B,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAKiC,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAKkC,GAAQ,GACvBtJ,KAAK0K,KAAKrD,EAAKgC,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAKiC,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKuC,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKwC,IAAQ,GACvBzJ,KAAK0K,KAAKxD,EAAKsC,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKuC,IAAQ,EAKlC,IAAIsB,IAAQ/O,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAK6C,IAAQ,GAIZ,KAAa,MAFnC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAK8C,IAAQ,GACvB5J,KAAK0K,KAAK3D,EAAK4C,IAAQ,KAEU,IAAO,EACrD3N,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAK6C,IAAQ,IAErBhD,IAAQ,IAAO,IAAMmE,KAAO,IAAO,EAChDA,IAAM,SAENnI,EAAK5C,KAAK0K,KAAK1C,EAAKY,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAK1C,EAAKa,IACR7I,KAAK0K,KAAKzC,EAAKW,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKzC,EAAKY,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKkB,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKmB,GAAQ,GACvBhJ,KAAK0K,KAAK5C,EAAKiB,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKkB,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAKwB,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAKyB,GAAQ,GACvBnJ,KAAK0K,KAAK/C,EAAKuB,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAKwB,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAK8B,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAK+B,GAAQ,GACvBtJ,KAAK0K,KAAKlD,EAAK6B,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAK8B,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAKoC,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAKqC,IAAQ,GACvBzJ,KAAK0K,KAAKrD,EAAKmC,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAKoC,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAK0C,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAK2C,IAAQ,GACvB5J,KAAK0K,KAAKxD,EAAKyC,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAK0C,IAAQ,EAKlC,IAAIoB,IAAQhP,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKgD,IAAQ,GAIZ,KAAa,MAFnClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKiD,IAAQ,GACvB/J,KAAK0K,KAAK3D,EAAK+C,IAAQ,KAEU,IAAO,EACrD9N,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKgD,IAAQ,IAErBnD,IAAQ,IAAO,IAAMoE,KAAO,IAAO,EAChDA,IAAM,SAENpI,EAAK5C,KAAK0K,KAAKvC,EAAKS,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKvC,EAAKU,IACR7I,KAAK0K,KAAKtC,EAAKQ,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKtC,EAAKS,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKe,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKgB,GAAQ,GACvBhJ,KAAK0K,KAAKzC,EAAKc,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKe,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKqB,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKsB,GAAQ,GACvBnJ,KAAK0K,KAAK5C,EAAKoB,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKqB,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAK2B,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAK4B,GAAQ,GACvBtJ,KAAK0K,KAAK/C,EAAK0B,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAK2B,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAKiC,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAKkC,IAAQ,GACvBzJ,KAAK0K,KAAKlD,EAAKgC,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAKiC,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAKuC,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAKwC,IAAQ,GACvB5J,KAAK0K,KAAKrD,EAAKsC,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAKuC,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAK6C,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAK8C,IAAQ,GACvB/J,KAAK0K,KAAKxD,EAAK4C,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAK6C,IAAQ,EAKlC,IAAIkB,IAAQjP,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKmD,IAAQ,GAIZ,KAAa,MAFnCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKoD,IAAQ,GACvBlK,KAAK0K,KAAK3D,EAAKkD,IAAQ,KAEU,IAAO,EACrDjO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKmD,IAAQ,IAErBtD,IAAQ,IAAO,IAAMqE,KAAO,IAAO,EAChDA,IAAM,SAENrI,EAAK5C,KAAK0K,KAAKpC,EAAKM,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKpC,EAAKO,IACR7I,KAAK0K,KAAKnC,EAAKK,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKnC,EAAKM,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKY,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKa,GAAQ,GACvBhJ,KAAK0K,KAAKtC,EAAKW,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKY,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKkB,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKmB,GAAQ,GACvBnJ,KAAK0K,KAAKzC,EAAKiB,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKkB,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKwB,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKyB,GAAQ,GACvBtJ,KAAK0K,KAAK5C,EAAKuB,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKwB,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAK8B,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAK+B,IAAQ,GACvBzJ,KAAK0K,KAAK/C,EAAK6B,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAK8B,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAKoC,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAKqC,IAAQ,GACvB5J,KAAK0K,KAAKlD,EAAKmC,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAKoC,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAK0C,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAK2C,IAAQ,GACvB/J,KAAK0K,KAAKrD,EAAKyC,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAK0C,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKgD,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKiD,IAAQ,GACvBlK,KAAK0K,KAAKxD,EAAK+C,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKgD,IAAQ,EAKlC,IAAIgB,IAAQlP,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKsD,IAAQ,GAIZ,KAAa,MAFnCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAKuD,IAAQ,GACvBrK,KAAK0K,KAAK3D,EAAKqD,IAAQ,KAEU,IAAO,EACrDpO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKsD,IAAQ,IAErBzD,IAAQ,IAAO,IAAMsE,KAAO,IAAO,EAChDA,IAAM,SAENtI,EAAK5C,KAAK0K,KAAKjC,EAAKG,GAEpBhC,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKI,IACR7I,KAAK0K,KAAKhC,EAAKE,GAAQ,EACpCnE,EAAKzE,KAAK0K,KAAKhC,EAAKG,GACpBjG,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKS,GAAQ,EAElCnC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKU,GAAQ,GACvBhJ,KAAK0K,KAAKnC,EAAKQ,GAAQ,EACpCtE,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKS,GAAQ,EAClCpG,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKe,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKgB,GAAQ,GACvBnJ,KAAK0K,KAAKtC,EAAKc,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKe,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKqB,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKsB,GAAQ,GACvBtJ,KAAK0K,KAAKzC,EAAKoB,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKqB,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAK2B,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAK4B,IAAQ,GACvBzJ,KAAK0K,KAAK5C,EAAK0B,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAK2B,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAKiC,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAKkC,IAAQ,GACvB5J,KAAK0K,KAAK/C,EAAKgC,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAKiC,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAKuC,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAKwC,IAAQ,GACvB/J,KAAK0K,KAAKlD,EAAKsC,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAKuC,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAK6C,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAK8C,IAAQ,GACvBlK,KAAK0K,KAAKrD,EAAK4C,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAK6C,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKmD,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKoD,IAAQ,GACvBrK,KAAK0K,KAAKxD,EAAKkD,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKmD,IAAQ,EAKlC,IAAIc,IAAQnP,GAJZ4G,EAAMA,EAAK5C,KAAK0K,KAAK5D,EAAKyD,IAAQ,GAIZ,KAAa,MAFnC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK5D,EAAK0D,IAAQ,GACvBxK,KAAK0K,KAAK3D,EAAKwD,IAAQ,KAEU,IAAO,EACrDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK3D,EAAKyD,IAAQ,IAErB5D,IAAQ,IAAO,IAAMuE,KAAO,IAAO,EAChDA,IAAM,SAENvI,EAAK5C,KAAK0K,KAAKjC,EAAKM,GAEpBnC,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKO,IACRhJ,KAAK0K,KAAKhC,EAAKK,GAAQ,EACpCtE,EAAKzE,KAAK0K,KAAKhC,EAAKM,GACpBpG,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKY,GAAQ,EAElCtC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKa,GAAQ,GACvBnJ,KAAK0K,KAAKnC,EAAKW,GAAQ,EACpCzE,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKY,GAAQ,EAClCvG,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKkB,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKmB,GAAQ,GACvBtJ,KAAK0K,KAAKtC,EAAKiB,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKkB,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKwB,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKyB,IAAQ,GACvBzJ,KAAK0K,KAAKzC,EAAKuB,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKwB,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAK8B,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAK+B,IAAQ,GACvB5J,KAAK0K,KAAK5C,EAAK6B,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAK8B,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAKoC,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAKqC,IAAQ,GACvB/J,KAAK0K,KAAK/C,EAAKmC,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAKoC,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAK0C,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAK2C,IAAQ,GACvBlK,KAAK0K,KAAKlD,EAAKyC,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAK0C,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAKgD,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAKiD,IAAQ,GACvBrK,KAAK0K,KAAKrD,EAAK+C,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAKgD,IAAQ,EAKlC,IAAIe,IAASpP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKzD,EAAKsD,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKzD,EAAKuD,IAAQ,GACvBxK,KAAK0K,KAAKxD,EAAKqD,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKxD,EAAKsD,IAAQ,IAErB5D,IAAQ,IAAO,IAAMwE,KAAQ,IAAO,EACjDA,IAAO,SAEPxI,EAAK5C,KAAK0K,KAAKjC,EAAKS,GAEpBtC,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKU,IACRnJ,KAAK0K,KAAKhC,EAAKQ,GAAQ,EACpCzE,EAAKzE,KAAK0K,KAAKhC,EAAKS,GACpBvG,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKe,GAAQ,EAElCzC,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKgB,GAAQ,GACvBtJ,KAAK0K,KAAKnC,EAAKc,GAAQ,EACpC5E,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKe,GAAQ,EAClC1G,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKqB,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKsB,IAAQ,GACvBzJ,KAAK0K,KAAKtC,EAAKoB,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKqB,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAK2B,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAK4B,IAAQ,GACvB5J,KAAK0K,KAAKzC,EAAK0B,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAK2B,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKiC,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKkC,IAAQ,GACvB/J,KAAK0K,KAAK5C,EAAKgC,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKiC,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAKuC,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAKwC,IAAQ,GACvBlK,KAAK0K,KAAK/C,EAAKsC,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAKuC,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAK6C,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAK8C,IAAQ,GACvBrK,KAAK0K,KAAKlD,EAAK4C,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAK6C,IAAQ,EAKlC,IAAIgB,IAASrP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKtD,EAAKmD,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKtD,EAAKoD,IAAQ,GACvBxK,KAAK0K,KAAKrD,EAAKkD,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKrD,EAAKmD,IAAQ,IAErB5D,IAAQ,IAAO,IAAMyE,KAAQ,IAAO,EACjDA,IAAO,SAEPzI,EAAK5C,KAAK0K,KAAKjC,EAAKY,GAEpBzC,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKa,IACRtJ,KAAK0K,KAAKhC,EAAKW,GAAQ,EACpC5E,EAAKzE,KAAK0K,KAAKhC,EAAKY,GACpB1G,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKkB,GAAQ,EAElC5C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKmB,IAAQ,GACvBzJ,KAAK0K,KAAKnC,EAAKiB,GAAQ,EACpC/E,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKkB,IAAQ,EAClC7G,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKwB,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKyB,IAAQ,GACvB5J,KAAK0K,KAAKtC,EAAKuB,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKwB,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAK8B,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAK+B,IAAQ,GACvB/J,KAAK0K,KAAKzC,EAAK6B,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAK8B,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKoC,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKqC,IAAQ,GACvBlK,KAAK0K,KAAK5C,EAAKmC,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKoC,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAK0C,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAK2C,IAAQ,GACvBrK,KAAK0K,KAAK/C,EAAKyC,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAK0C,IAAQ,EAKlC,IAAIiB,IAAStP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKnD,EAAKgD,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKnD,EAAKiD,IAAQ,GACvBxK,KAAK0K,KAAKlD,EAAK+C,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKlD,EAAKgD,IAAQ,IAErB5D,IAAQ,IAAO,IAAM0E,KAAQ,IAAO,EACjDA,IAAO,SAEP1I,EAAK5C,KAAK0K,KAAKjC,EAAKe,GAEpB5C,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKgB,KACRzJ,KAAK0K,KAAKhC,EAAKc,GAAQ,EACpC/E,EAAKzE,KAAK0K,KAAKhC,EAAKe,IACpB7G,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKqB,IAAQ,EAElC/C,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKsB,IAAQ,GACvB5J,KAAK0K,KAAKnC,EAAKoB,IAAQ,EACpClF,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKqB,IAAQ,EAClChH,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAK2B,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAK4B,IAAQ,GACvB/J,KAAK0K,KAAKtC,EAAK0B,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAK2B,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKiC,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKkC,IAAQ,GACvBlK,KAAK0K,KAAKzC,EAAKgC,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKiC,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAKuC,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAKwC,IAAQ,GACvBrK,KAAK0K,KAAK5C,EAAKsC,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAKuC,IAAQ,EAKlC,IAAIkB,IAASvP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKhD,EAAK6C,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKhD,EAAK8C,IAAQ,GACvBxK,KAAK0K,KAAK/C,EAAK4C,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK/C,EAAK6C,IAAQ,IAErB5D,IAAQ,IAAO,IAAM2E,KAAQ,IAAO,EACjDA,IAAO,SAEP3I,EAAK5C,KAAK0K,KAAKjC,EAAKkB,IAEpB/C,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKmB,KACR5J,KAAK0K,KAAKhC,EAAKiB,IAAQ,EACpClF,EAAKzE,KAAK0K,KAAKhC,EAAKkB,IACpBhH,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKwB,IAAQ,EAElClD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKyB,IAAQ,GACvB/J,KAAK0K,KAAKnC,EAAKuB,IAAQ,EACpCrF,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKwB,IAAQ,EAClCnH,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAK8B,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAK+B,IAAQ,GACvBlK,KAAK0K,KAAKtC,EAAK6B,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAK8B,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKoC,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKqC,IAAQ,GACvBrK,KAAK0K,KAAKzC,EAAKmC,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKoC,IAAQ,EAKlC,IAAImB,IAASxP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAK7C,EAAK0C,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK7C,EAAK2C,IAAQ,GACvBxK,KAAK0K,KAAK5C,EAAKyC,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAK5C,EAAK0C,IAAQ,IAErB5D,IAAQ,IAAO,IAAM4E,KAAQ,IAAO,EACjDA,IAAO,SAEP5I,EAAK5C,KAAK0K,KAAKjC,EAAKqB,IAEpBlD,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKsB,KACR/J,KAAK0K,KAAKhC,EAAKoB,IAAQ,EACpCrF,EAAKzE,KAAK0K,KAAKhC,EAAKqB,IACpBnH,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAK2B,IAAQ,EAElCrD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAK4B,IAAQ,GACvBlK,KAAK0K,KAAKnC,EAAK0B,IAAQ,EACpCxF,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAK2B,IAAQ,EAClCtH,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKiC,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKkC,IAAQ,GACvBrK,KAAK0K,KAAKtC,EAAKgC,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKiC,IAAQ,EAKlC,IAAIoB,IAASzP,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAK1C,EAAKuC,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAK1C,EAAKwC,IAAQ,GACvBxK,KAAK0K,KAAKzC,EAAKsC,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKzC,EAAKuC,IAAQ,IAErB5D,IAAQ,IAAO,IAAM6E,KAAQ,IAAO,EACjDA,IAAO,SAEP7I,EAAK5C,KAAK0K,KAAKjC,EAAKwB,IAEpBrD,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAKyB,KACRlK,KAAK0K,KAAKhC,EAAKuB,IAAQ,EACpCxF,EAAKzE,KAAK0K,KAAKhC,EAAKwB,IACpBtH,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAK8B,IAAQ,EAElCxD,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAK+B,IAAQ,GACvBrK,KAAK0K,KAAKnC,EAAK6B,IAAQ,EACpC3F,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAK8B,IAAQ,EAKlC,IAAIqB,IAAS1P,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKvC,EAAKoC,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKvC,EAAKqC,IAAQ,GACvBxK,KAAK0K,KAAKtC,EAAKmC,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKtC,EAAKoC,IAAQ,IAErB5D,IAAQ,IAAO,IAAM8E,KAAQ,IAAO,EACjDA,IAAO,SAEP9I,EAAK5C,KAAK0K,KAAKjC,EAAK2B,IAEpBxD,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAK4B,KACRrK,KAAK0K,KAAKhC,EAAK0B,IAAQ,EACpC3F,EAAKzE,KAAK0K,KAAKhC,EAAK2B,IAKpB,IAAIsB,IAAS3P,GAJb4G,EAAMA,EAAK5C,KAAK0K,KAAKpC,EAAKiC,IAAQ,GAIX,KAAa,MAFpC3D,GADAA,EAAOA,EAAM5G,KAAK0K,KAAKpC,EAAKkC,IAAQ,GACvBxK,KAAK0K,KAAKnC,EAAKgC,IAAQ,KAEW,IAAO,EACtDvO,IAFAyI,EAAMA,EAAKzE,KAAK0K,KAAKnC,EAAKiC,IAAQ,IAErB5D,IAAQ,IAAO,IAAM+E,KAAQ,IAAO,EACjDA,IAAO,SAMP,IAAIC,IAAS5P,GAJb4G,EAAK5C,KAAK0K,KAAKjC,EAAK8B,KAIG,KAAa,MAFpC3D,GADAA,EAAM5G,KAAK0K,KAAKjC,EAAK+B,KACRxK,KAAK0K,KAAKhC,EAAK6B,IAAQ,KAEW,IAAO,EA0BtD,OAzBAvO,IAFAyI,EAAKzE,KAAK0K,KAAKhC,EAAK8B,MAEP5D,IAAQ,IAAO,IAAMgF,KAAQ,IAAO,EACjDA,IAAO,SACPxP,EAAE,GAAKqO,GACPrO,EAAE,GAAKuO,GACPvO,EAAE,GAAKwO,GACPxO,EAAE,GAAKyO,GACPzO,EAAE,GAAK0O,GACP1O,EAAE,GAAK2O,GACP3O,EAAE,GAAK4O,GACP5O,EAAE,GAAK6O,GACP7O,EAAE,GAAK8O,GACP9O,EAAE,GAAK+O,GACP/O,EAAE,IAAMgP,GACRhP,EAAE,IAAMiP,GACRjP,EAAE,IAAMkP,GACRlP,EAAE,IAAMmP,GACRnP,EAAE,IAAMoP,GACRpP,EAAE,IAAMqP,GACRrP,EAAE,IAAMsP,GACRtP,EAAE,IAAMuP,GACRvP,EAAE,IAAMwP,GACE,IAAN5P,IACFI,EAAE,IAAMJ,EACRyG,EAAI5D,UAEC4D,GAiDT,SAASoJ,EAAYrJ,EAAMtC,EAAKuC,GAE9B,OADW,IAAIqJ,GACHC,KAAKvJ,EAAMtC,EAAKuC,GAsB9B,SAASqJ,EAAME,EAAGC,GAChBvN,KAAKsN,EAAIA,EACTtN,KAAKuN,EAAIA,EAvENjM,KAAK0K,OACR/D,EAAcpE,GAiDhBlE,EAAGd,UAAU2O,MAAQ,SAAgBhM,EAAKuC,GACxC,IACI1C,EAAMrB,KAAKG,OAASqB,EAAIrB,OAW5B,OAVoB,KAAhBH,KAAKG,QAAgC,KAAfqB,EAAIrB,OACtB8H,EAAYjI,KAAMwB,EAAKuC,GACpB1C,EAAM,GACTwC,EAAW7D,KAAMwB,EAAKuC,GACnB1C,EAAM,KArDnB,SAAmByC,EAAMtC,EAAKuC,GAC5BA,EAAI9D,SAAWuB,EAAIvB,SAAW6D,EAAK7D,SACnC8D,EAAI5D,OAAS2D,EAAK3D,OAASqB,EAAIrB,OAI/B,IAFA,IAAIgE,EAAQ,EACRsJ,EAAU,EACLrJ,EAAI,EAAGA,EAAIL,EAAI5D,OAAS,EAAGiE,IAAK,CAGvC,IAAIC,EAASoJ,EACbA,EAAU,EAGV,IAFA,IAAInJ,EAAgB,SAARH,EACRI,EAAOjD,KAAKC,IAAI6C,EAAG5C,EAAIrB,OAAS,GAC3BoC,EAAIjB,KAAKK,IAAI,EAAGyC,EAAIN,EAAK3D,OAAS,GAAIoC,GAAKgC,EAAMhC,IAAK,CAC7D,IAAItF,EAAImH,EAAI7B,EAGRxE,GAFoB,EAAhB+F,EAAK5D,MAAMjD,KACI,EAAfuE,EAAItB,MAAMqC,IAGd2B,EAAS,SAAJnG,EAGTuG,EAAa,UADbJ,EAAMA,EAAKI,EAAS,GAIpBmJ,IAFApJ,GAHAA,EAAUA,GAAWtG,EAAI,SAAa,GAAM,IAGxBmG,IAAO,IAAO,KAEZ,GACtBG,GAAU,SAEZN,EAAI7D,MAAMkE,GAAKE,EACfH,EAAQE,EACRA,EAASoJ,EAQX,OANc,IAAVtJ,EACFJ,EAAI7D,MAAMkE,GAAKD,EAEfJ,EAAI5D,SAGC4D,EAAIrB,QAgBHgL,CAAS1N,KAAMwB,EAAKuC,GAEpBoJ,EAAWnN,KAAMwB,EAAKuC,IAchCqJ,EAAKvO,UAAU8O,QAAU,SAAkBC,GAGzC,IAFA,IAAIzP,EAAI,IAAIsD,MAAMmM,GACd1Q,EAAIyC,EAAGd,UAAU+G,WAAWgI,GAAK,EAC5B3Q,EAAI,EAAGA,EAAI2Q,EAAG3Q,IACrBkB,EAAElB,GAAK+C,KAAK6N,OAAO5Q,EAAGC,EAAG0Q,GAG3B,OAAOzP,GAITiP,EAAKvO,UAAUgP,OAAS,SAAiBP,EAAGpQ,EAAG0Q,GAC7C,GAAU,IAANN,GAAWA,IAAMM,EAAI,EAAG,OAAON,EAGnC,IADA,IAAIQ,EAAK,EACA7Q,EAAI,EAAGA,EAAIC,EAAGD,IACrB6Q,IAAW,EAAJR,IAAWpQ,EAAID,EAAI,EAC1BqQ,IAAM,EAGR,OAAOQ,GAKTV,EAAKvO,UAAUkP,QAAU,SAAkBC,EAAKC,EAAKC,EAAKC,EAAMC,EAAMR,GACpE,IAAK,IAAI3Q,EAAI,EAAGA,EAAI2Q,EAAG3Q,IACrBkR,EAAKlR,GAAKgR,EAAID,EAAI/Q,IAClBmR,EAAKnR,GAAKiR,EAAIF,EAAI/Q,KAItBmQ,EAAKvO,UAAUwP,UAAY,SAAoBJ,EAAKC,EAAKC,EAAMC,EAAMR,EAAGI,GACtEhO,KAAK+N,QAAQC,EAAKC,EAAKC,EAAKC,EAAMC,EAAMR,GAExC,IAAK,IAAI5O,EAAI,EAAGA,EAAI4O,EAAG5O,IAAM,EAM3B,IALA,IAAI9B,EAAI8B,GAAK,EAETsP,EAAQhN,KAAKiN,IAAI,EAAIjN,KAAKkN,GAAKtR,GAC/BuR,EAAQnN,KAAKoN,IAAI,EAAIpN,KAAKkN,GAAKtR,GAE1B6B,EAAI,EAAGA,EAAI6O,EAAG7O,GAAK7B,EAI1B,IAHA,IAAIyR,EAASL,EACTM,EAASH,EAEJlM,EAAI,EAAGA,EAAIvD,EAAGuD,IAAK,CAC1B,IAAIsM,EAAKV,EAAKpP,EAAIwD,GACduM,EAAKV,EAAKrP,EAAIwD,GAEdwM,EAAKZ,EAAKpP,EAAIwD,EAAIvD,GAClBgQ,EAAKZ,EAAKrP,EAAIwD,EAAIvD,GAElBiQ,EAAKN,EAASI,EAAKH,EAASI,EAEhCA,EAAKL,EAASK,EAAKJ,EAASG,EAC5BA,EAAKE,EAELd,EAAKpP,EAAIwD,GAAKsM,EAAKE,EACnBX,EAAKrP,EAAIwD,GAAKuM,EAAKE,EAEnBb,EAAKpP,EAAIwD,EAAIvD,GAAK6P,EAAKE,EACvBX,EAAKrP,EAAIwD,EAAIvD,GAAK8P,EAAKE,EAGnBzM,IAAMrF,IACR+R,EAAKX,EAAQK,EAASF,EAAQG,EAE9BA,EAASN,EAAQM,EAASH,EAAQE,EAClCA,EAASM,KAOnB7B,EAAKvO,UAAUqQ,YAAc,SAAsBxQ,EAAGrB,GACpD,IAAIuQ,EAAqB,EAAjBtM,KAAKK,IAAItE,EAAGqB,GAChByQ,EAAU,EAAJvB,EACN3Q,EAAI,EACR,IAAK2Q,EAAIA,EAAI,EAAI,EAAGA,EAAGA,KAAU,EAC/B3Q,IAGF,OAAO,GAAKA,EAAI,EAAIkS,GAGtB/B,EAAKvO,UAAUuQ,UAAY,SAAoBnB,EAAKC,EAAKN,GACvD,KAAIA,GAAK,GAET,IAAK,IAAI3Q,EAAI,EAAGA,EAAI2Q,EAAI,EAAG3Q,IAAK,CAC9B,IAAIkB,EAAI8P,EAAIhR,GAEZgR,EAAIhR,GAAKgR,EAAIL,EAAI3Q,EAAI,GACrBgR,EAAIL,EAAI3Q,EAAI,GAAKkB,EAEjBA,EAAI+P,EAAIjR,GAERiR,EAAIjR,IAAMiR,EAAIN,EAAI3Q,EAAI,GACtBiR,EAAIN,EAAI3Q,EAAI,IAAMkB,IAItBiP,EAAKvO,UAAUwQ,aAAe,SAAuBC,EAAI1B,GAEvD,IADA,IAAIzJ,EAAQ,EACHlH,EAAI,EAAGA,EAAI2Q,EAAI,EAAG3Q,IAAK,CAC9B,IAAIuF,EAAoC,KAAhClB,KAAKiO,MAAMD,EAAG,EAAIrS,EAAI,GAAK2Q,GACjCtM,KAAKiO,MAAMD,EAAG,EAAIrS,GAAK2Q,GACvBzJ,EAEFmL,EAAGrS,GAAS,SAAJuF,EAGN2B,EADE3B,EAAI,SACE,EAEAA,EAAI,SAAY,EAI5B,OAAO8M,GAGTlC,EAAKvO,UAAU2Q,WAAa,SAAqBF,EAAIjO,EAAK4M,EAAKL,GAE7D,IADA,IAAIzJ,EAAQ,EACHlH,EAAI,EAAGA,EAAIoE,EAAKpE,IACvBkH,GAAyB,EAARmL,EAAGrS,GAEpBgR,EAAI,EAAIhR,GAAa,KAARkH,EAAgBA,KAAkB,GAC/C8J,EAAI,EAAIhR,EAAI,GAAa,KAARkH,EAAgBA,KAAkB,GAIrD,IAAKlH,EAAI,EAAIoE,EAAKpE,EAAI2Q,IAAK3Q,EACzBgR,EAAIhR,GAAK,EAGXgC,EAAiB,IAAVkF,GACPlF,EAA6B,KAAb,KAARkF,KAGViJ,EAAKvO,UAAU4Q,KAAO,SAAe7B,GAEnC,IADA,IAAI8B,EAAK,IAAIjO,MAAMmM,GACV3Q,EAAI,EAAGA,EAAI2Q,EAAG3Q,IACrByS,EAAGzS,GAAK,EAGV,OAAOyS,GAGTtC,EAAKvO,UAAUwO,KAAO,SAAeC,EAAGC,EAAGxJ,GACzC,IAAI6J,EAAI,EAAI5N,KAAKkP,YAAY5B,EAAEnN,OAAQoN,EAAEpN,QAErC6N,EAAMhO,KAAK2N,QAAQC,GAEnB+B,EAAI3P,KAAKyP,KAAK7B,GAEdK,EAAM,IAAIxM,MAAMmM,GAChBgC,EAAO,IAAInO,MAAMmM,GACjBiC,EAAO,IAAIpO,MAAMmM,GAEjBkC,EAAO,IAAIrO,MAAMmM,GACjBmC,EAAQ,IAAItO,MAAMmM,GAClBoC,EAAQ,IAAIvO,MAAMmM,GAElBqC,EAAOlM,EAAI7D,MACf+P,EAAK9P,OAASyN,EAEd5N,KAAKwP,WAAWlC,EAAEpN,MAAOoN,EAAEnN,OAAQ8N,EAAKL,GACxC5N,KAAKwP,WAAWjC,EAAErN,MAAOqN,EAAEpN,OAAQ2P,EAAMlC,GAEzC5N,KAAKqO,UAAUJ,EAAK0B,EAAGC,EAAMC,EAAMjC,EAAGI,GACtChO,KAAKqO,UAAUyB,EAAMH,EAAGI,EAAOC,EAAOpC,EAAGI,GAEzC,IAAK,IAAI/Q,EAAI,EAAGA,EAAI2Q,EAAG3Q,IAAK,CAC1B,IAAIgS,EAAKW,EAAK3S,GAAK8S,EAAM9S,GAAK4S,EAAK5S,GAAK+S,EAAM/S,GAC9C4S,EAAK5S,GAAK2S,EAAK3S,GAAK+S,EAAM/S,GAAK4S,EAAK5S,GAAK8S,EAAM9S,GAC/C2S,EAAK3S,GAAKgS,EAUZ,OAPAjP,KAAKoP,UAAUQ,EAAMC,EAAMjC,GAC3B5N,KAAKqO,UAAUuB,EAAMC,EAAMI,EAAMN,EAAG/B,EAAGI,GACvChO,KAAKoP,UAAUa,EAAMN,EAAG/B,GACxB5N,KAAKqP,aAAaY,EAAMrC,GAExB7J,EAAI9D,SAAWqN,EAAErN,SAAWsN,EAAEtN,SAC9B8D,EAAI5D,OAASmN,EAAEnN,OAASoN,EAAEpN,OACnB4D,EAAIrB,SAIb/C,EAAGd,UAAUuC,IAAM,SAAcI,GAC/B,IAAIuC,EAAM,IAAIpE,EAAG,MAEjB,OADAoE,EAAI7D,MAAQ,IAAIuB,MAAMzB,KAAKG,OAASqB,EAAIrB,QACjCH,KAAKwN,MAAMhM,EAAKuC,IAIzBpE,EAAGd,UAAUqR,KAAO,SAAe1O,GACjC,IAAIuC,EAAM,IAAIpE,EAAG,MAEjB,OADAoE,EAAI7D,MAAQ,IAAIuB,MAAMzB,KAAKG,OAASqB,EAAIrB,QACjCgN,EAAWnN,KAAMwB,EAAKuC,IAI/BpE,EAAGd,UAAUmN,KAAO,SAAexK,GACjC,OAAOxB,KAAKqD,QAAQmK,MAAMhM,EAAKxB,OAGjCL,EAAGd,UAAUmE,MAAQ,SAAgBxB,GACnCvC,EAAsB,iBAARuC,GACdvC,EAAOuC,EAAM,UAIb,IADA,IAAI2C,EAAQ,EACHlH,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAAK,CACpC,IAAIuF,GAAqB,EAAhBxC,KAAKE,MAAMjD,IAAUuE,EAC1B0C,GAAU,SAAJ1B,IAA0B,SAAR2B,GAC5BA,IAAU,GACVA,GAAU3B,EAAI,SAAa,EAE3B2B,GAASD,IAAO,GAChBlE,KAAKE,MAAMjD,GAAU,SAALiH,EAQlB,OALc,IAAVC,IACFnE,KAAKE,MAAMjD,GAAKkH,EAChBnE,KAAKG,UAGAH,MAGTL,EAAGd,UAAUsR,KAAO,SAAe3O,GACjC,OAAOxB,KAAKqD,QAAQL,MAAMxB,IAI5B7B,EAAGd,UAAUuR,IAAM,WACjB,OAAOpQ,KAAKoB,IAAIpB,OAIlBL,EAAGd,UAAUwR,KAAO,WAClB,OAAOrQ,KAAKgM,KAAKhM,KAAKqD,UAIxB1D,EAAGd,UAAUqE,IAAM,SAAc1B,GAC/B,IAAIgB,EAxxCN,SAAqBhB,GAGnB,IAFA,IAAIgB,EAAI,IAAIf,MAAMD,EAAIsE,aAEb6B,EAAM,EAAGA,EAAMnF,EAAErC,OAAQwH,IAAO,CACvC,IAAIlF,EAAOkF,EAAM,GAAM,EACnBC,EAAOD,EAAM,GAEjBnF,EAAEmF,IAAQnG,EAAItB,MAAMuC,GAAQ,GAAKmF,KAAWA,EAG9C,OAAOpF,EA8wCC8N,CAAW9O,GACnB,GAAiB,IAAbgB,EAAErC,OAAc,OAAO,IAAIR,EAAG,GAIlC,IADA,IAAI4F,EAAMvF,KACD/C,EAAI,EAAGA,EAAIuF,EAAErC,QACP,IAATqC,EAAEvF,GADsBA,IAAKsI,EAAMA,EAAI6K,OAI7C,KAAMnT,EAAIuF,EAAErC,OACV,IAAK,IAAIqF,EAAID,EAAI6K,MAAOnT,EAAIuF,EAAErC,OAAQlD,IAAKuI,EAAIA,EAAE4K,MAClC,IAAT5N,EAAEvF,KAENsI,EAAMA,EAAInE,IAAIoE,IAIlB,OAAOD,GAIT5F,EAAGd,UAAU0R,OAAS,SAAiBC,GACrCvR,EAAuB,iBAATuR,GAAqBA,GAAQ,GAC3C,IAGIvT,EAHAc,EAAIyS,EAAO,GACXxR,GAAKwR,EAAOzS,GAAK,GACjB0S,EAAa,WAAe,GAAK1S,GAAQ,GAAKA,EAGlD,GAAU,IAANA,EAAS,CACX,IAAIoG,EAAQ,EAEZ,IAAKlH,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAAK,CAChC,IAAIyT,EAAW1Q,KAAKE,MAAMjD,GAAKwT,EAC3BnT,GAAsB,EAAhB0C,KAAKE,MAAMjD,IAAUyT,GAAa3S,EAC5CiC,KAAKE,MAAMjD,GAAKK,EAAI6G,EACpBA,EAAQuM,IAAc,GAAK3S,EAGzBoG,IACFnE,KAAKE,MAAMjD,GAAKkH,EAChBnE,KAAKG,UAIT,GAAU,IAANnB,EAAS,CACX,IAAK/B,EAAI+C,KAAKG,OAAS,EAAGlD,GAAK,EAAGA,IAChC+C,KAAKE,MAAMjD,EAAI+B,GAAKgB,KAAKE,MAAMjD,GAGjC,IAAKA,EAAI,EAAGA,EAAI+B,EAAG/B,IACjB+C,KAAKE,MAAMjD,GAAK,EAGlB+C,KAAKG,QAAUnB,EAGjB,OAAOgB,KAAK0C,SAGd/C,EAAGd,UAAU8R,MAAQ,SAAgBH,GAGnC,OADAvR,EAAyB,IAAlBe,KAAKC,UACLD,KAAKuQ,OAAOC,IAMrB7Q,EAAGd,UAAU6G,OAAS,SAAiB8K,EAAMI,EAAMC,GAEjD,IAAIC,EADJ7R,EAAuB,iBAATuR,GAAqBA,GAAQ,GAGzCM,EADEF,GACGA,EAAQA,EAAO,IAAO,GAEvB,EAGN,IAAI7S,EAAIyS,EAAO,GACXxR,EAAIsC,KAAKC,KAAKiP,EAAOzS,GAAK,GAAIiC,KAAKG,QACnC4Q,EAAO,SAAc,WAAchT,GAAMA,EACzCiT,EAAcH,EAMlB,GAJAC,GAAK9R,EACL8R,EAAIxP,KAAKK,IAAI,EAAGmP,GAGZE,EAAa,CACf,IAAK,IAAI/T,EAAI,EAAGA,EAAI+B,EAAG/B,IACrB+T,EAAY9Q,MAAMjD,GAAK+C,KAAKE,MAAMjD,GAEpC+T,EAAY7Q,OAASnB,EAGvB,GAAU,IAANA,QAEG,GAAIgB,KAAKG,OAASnB,EAEvB,IADAgB,KAAKG,QAAUnB,EACV/B,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAC3B+C,KAAKE,MAAMjD,GAAK+C,KAAKE,MAAMjD,EAAI+B,QAGjCgB,KAAKE,MAAM,GAAK,EAChBF,KAAKG,OAAS,EAGhB,IAAIgE,EAAQ,EACZ,IAAKlH,EAAI+C,KAAKG,OAAS,EAAGlD,GAAK,IAAgB,IAAVkH,GAAelH,GAAK6T,GAAI7T,IAAK,CAChE,IAAI8F,EAAuB,EAAhB/C,KAAKE,MAAMjD,GACtB+C,KAAKE,MAAMjD,GAAMkH,GAAU,GAAKpG,EAAOgF,IAAShF,EAChDoG,EAAQpB,EAAOgO,EAajB,OATIC,GAAyB,IAAV7M,IACjB6M,EAAY9Q,MAAM8Q,EAAY7Q,UAAYgE,GAGxB,IAAhBnE,KAAKG,SACPH,KAAKE,MAAM,GAAK,EAChBF,KAAKG,OAAS,GAGTH,KAAK0C,SAGd/C,EAAGd,UAAUoS,MAAQ,SAAgBT,EAAMI,EAAMC,GAG/C,OADA5R,EAAyB,IAAlBe,KAAKC,UACLD,KAAK0F,OAAO8K,EAAMI,EAAMC,IAIjClR,EAAGd,UAAUqS,KAAO,SAAeV,GACjC,OAAOxQ,KAAKqD,QAAQsN,MAAMH,IAG5B7Q,EAAGd,UAAUsS,MAAQ,SAAgBX,GACnC,OAAOxQ,KAAKqD,QAAQkN,OAAOC,IAI7B7Q,EAAGd,UAAUuS,KAAO,SAAeZ,GACjC,OAAOxQ,KAAKqD,QAAQ4N,MAAMT,IAG5B7Q,EAAGd,UAAUwS,MAAQ,SAAgBb,GACnC,OAAOxQ,KAAKqD,QAAQqC,OAAO8K,IAI7B7Q,EAAGd,UAAU0H,MAAQ,SAAgBoB,GACnC1I,EAAsB,iBAAR0I,GAAoBA,GAAO,GACzC,IAAI5J,EAAI4J,EAAM,GACV3I,GAAK2I,EAAM5J,GAAK,GAChByH,EAAI,GAAKzH,EAGb,QAAIiC,KAAKG,QAAUnB,OAGXgB,KAAKE,MAAMlB,GAELwG,IAIhB7F,EAAGd,UAAUyS,OAAS,SAAiBd,GACrCvR,EAAuB,iBAATuR,GAAqBA,GAAQ,GAC3C,IAAIzS,EAAIyS,EAAO,GACXxR,GAAKwR,EAAOzS,GAAK,GAIrB,GAFAkB,EAAyB,IAAlBe,KAAKC,SAAgB,2CAExBD,KAAKG,QAAUnB,EACjB,OAAOgB,KAQT,GALU,IAANjC,GACFiB,IAEFgB,KAAKG,OAASmB,KAAKC,IAAIvC,EAAGgB,KAAKG,QAErB,IAANpC,EAAS,CACX,IAAIgT,EAAO,SAAc,WAAchT,GAAMA,EAC7CiC,KAAKE,MAAMF,KAAKG,OAAS,IAAM4Q,EAGjC,OAAO/Q,KAAK0C,SAId/C,EAAGd,UAAU0S,MAAQ,SAAgBf,GACnC,OAAOxQ,KAAKqD,QAAQiO,OAAOd,IAI7B7Q,EAAGd,UAAUwH,MAAQ,SAAgB7E,GAGnC,OAFAvC,EAAsB,iBAARuC,GACdvC,EAAOuC,EAAM,UACTA,EAAM,EAAUxB,KAAKwR,OAAOhQ,GAGV,IAAlBxB,KAAKC,SACa,IAAhBD,KAAKG,SAAiC,EAAhBH,KAAKE,MAAM,IAAUsB,GAC7CxB,KAAKE,MAAM,GAAKsB,GAAuB,EAAhBxB,KAAKE,MAAM,IAClCF,KAAKC,SAAW,EACTD,OAGTA,KAAKC,SAAW,EAChBD,KAAKwR,MAAMhQ,GACXxB,KAAKC,SAAW,EACTD,MAIFA,KAAKiD,OAAOzB,IAGrB7B,EAAGd,UAAUoE,OAAS,SAAiBzB,GACrCxB,KAAKE,MAAM,IAAMsB,EAGjB,IAAK,IAAIvE,EAAI,EAAGA,EAAI+C,KAAKG,QAAUH,KAAKE,MAAMjD,IAAM,SAAWA,IAC7D+C,KAAKE,MAAMjD,IAAM,SACbA,IAAM+C,KAAKG,OAAS,EACtBH,KAAKE,MAAMjD,EAAI,GAAK,EAEpB+C,KAAKE,MAAMjD,EAAI,KAKnB,OAFA+C,KAAKG,OAASmB,KAAKK,IAAI3B,KAAKG,OAAQlD,EAAI,GAEjC+C,MAITL,EAAGd,UAAU2S,MAAQ,SAAgBhQ,GAGnC,GAFAvC,EAAsB,iBAARuC,GACdvC,EAAOuC,EAAM,UACTA,EAAM,EAAG,OAAOxB,KAAKqG,OAAO7E,GAEhC,GAAsB,IAAlBxB,KAAKC,SAIP,OAHAD,KAAKC,SAAW,EAChBD,KAAKqG,MAAM7E,GACXxB,KAAKC,SAAW,EACTD,KAKT,GAFAA,KAAKE,MAAM,IAAMsB,EAEG,IAAhBxB,KAAKG,QAAgBH,KAAKE,MAAM,GAAK,EACvCF,KAAKE,MAAM,IAAMF,KAAKE,MAAM,GAC5BF,KAAKC,SAAW,OAGhB,IAAK,IAAIhD,EAAI,EAAGA,EAAI+C,KAAKG,QAAUH,KAAKE,MAAMjD,GAAK,EAAGA,IACpD+C,KAAKE,MAAMjD,IAAM,SACjB+C,KAAKE,MAAMjD,EAAI,IAAM,EAIzB,OAAO+C,KAAK0C,SAGd/C,EAAGd,UAAU4S,KAAO,SAAejQ,GACjC,OAAOxB,KAAKqD,QAAQgD,MAAM7E,IAG5B7B,EAAGd,UAAU6S,KAAO,SAAelQ,GACjC,OAAOxB,KAAKqD,QAAQmO,MAAMhQ,IAG5B7B,EAAGd,UAAU8S,KAAO,WAGlB,OAFA3R,KAAKC,SAAW,EAETD,MAGTL,EAAGd,UAAUsH,IAAM,WACjB,OAAOnG,KAAKqD,QAAQsO,QAGtBhS,EAAGd,UAAU+S,aAAe,SAAuBpQ,EAAKJ,EAAKyQ,GAC3D,IACI5U,EAIAuF,EALAnB,EAAMG,EAAIrB,OAAS0R,EAGvB7R,KAAKsD,QAAQjC,GAGb,IAAI8C,EAAQ,EACZ,IAAKlH,EAAI,EAAGA,EAAIuE,EAAIrB,OAAQlD,IAAK,CAC/BuF,GAA6B,EAAxBxC,KAAKE,MAAMjD,EAAI4U,IAAc1N,EAClC,IAAItC,GAAwB,EAAfL,EAAItB,MAAMjD,IAAUmE,EAEjC+C,IADA3B,GAAa,SAARX,IACS,KAAQA,EAAQ,SAAa,GAC3C7B,KAAKE,MAAMjD,EAAI4U,GAAa,SAAJrP,EAE1B,KAAOvF,EAAI+C,KAAKG,OAAS0R,EAAO5U,IAE9BkH,GADA3B,GAA6B,EAAxBxC,KAAKE,MAAMjD,EAAI4U,IAAc1N,IACrB,GACbnE,KAAKE,MAAMjD,EAAI4U,GAAa,SAAJrP,EAG1B,GAAc,IAAV2B,EAAa,OAAOnE,KAAK0C,QAK7B,IAFAzD,GAAkB,IAAXkF,GACPA,EAAQ,EACHlH,EAAI,EAAGA,EAAI+C,KAAKG,OAAQlD,IAE3BkH,GADA3B,IAAsB,EAAhBxC,KAAKE,MAAMjD,IAAUkH,IACd,GACbnE,KAAKE,MAAMjD,GAAS,SAAJuF,EAIlB,OAFAxC,KAAKC,SAAW,EAETD,KAAK0C,SAGd/C,EAAGd,UAAUiT,SAAW,SAAmBtQ,EAAKpD,GAC9C,IAAIyT,GAAQ7R,KAAKG,OAASqB,EAAIrB,QAE1B6D,EAAIhE,KAAKqD,QACTY,EAAIzC,EAGJuQ,EAA8B,EAAxB9N,EAAE/D,MAAM+D,EAAE9D,OAAS,GAGf,KADd0R,EAAQ,GADM7R,KAAK4F,WAAWmM,MAG5B9N,EAAIA,EAAEkN,MAAMU,GACZ7N,EAAEuM,OAAOsB,GACTE,EAA8B,EAAxB9N,EAAE/D,MAAM+D,EAAE9D,OAAS,IAI3B,IACIqF,EADAnI,EAAI2G,EAAE7D,OAAS8D,EAAE9D,OAGrB,GAAa,QAAT/B,EAAgB,EAClBoH,EAAI,IAAI7F,EAAG,OACTQ,OAAS9C,EAAI,EACfmI,EAAEtF,MAAQ,IAAIuB,MAAM+D,EAAErF,QACtB,IAAK,IAAIlD,EAAI,EAAGA,EAAIuI,EAAErF,OAAQlD,IAC5BuI,EAAEtF,MAAMjD,GAAK,EAIjB,IAAI+U,EAAOhO,EAAEX,QAAQuO,aAAa3N,EAAG,EAAG5G,GAClB,IAAlB2U,EAAK/R,WACP+D,EAAIgO,EACAxM,IACFA,EAAEtF,MAAM7C,GAAK,IAIjB,IAAK,IAAIkF,EAAIlF,EAAI,EAAGkF,GAAK,EAAGA,IAAK,CAC/B,IAAI0P,EAAmC,UAAL,EAAxBjO,EAAE9D,MAAM+D,EAAE9D,OAASoC,KACE,EAA5ByB,EAAE9D,MAAM+D,EAAE9D,OAASoC,EAAI,IAO1B,IAHA0P,EAAK3Q,KAAKC,IAAK0Q,EAAKF,EAAO,EAAG,UAE9B/N,EAAE4N,aAAa3N,EAAGgO,EAAI1P,GACA,IAAfyB,EAAE/D,UACPgS,IACAjO,EAAE/D,SAAW,EACb+D,EAAE4N,aAAa3N,EAAG,EAAG1B,GAChByB,EAAEW,WACLX,EAAE/D,UAAY,GAGduF,IACFA,EAAEtF,MAAMqC,GAAK0P,GAajB,OAVIzM,GACFA,EAAE9C,QAEJsB,EAAEtB,QAGW,QAATtE,GAA4B,IAAVyT,GACpB7N,EAAE0B,OAAOmM,GAGJ,CACLK,IAAK1M,GAAK,KACV1C,IAAKkB,IAQTrE,EAAGd,UAAUsT,OAAS,SAAiB3Q,EAAKpD,EAAMgU,GAGhD,OAFAnT,GAAQuC,EAAImD,UAER3E,KAAK2E,SACA,CACLuN,IAAK,IAAIvS,EAAG,GACZmD,IAAK,IAAInD,EAAG,IAKM,IAAlBK,KAAKC,UAAmC,IAAjBuB,EAAIvB,UAC7BsF,EAAMvF,KAAK2G,MAAMwL,OAAO3Q,EAAKpD,GAEhB,QAATA,IACF8T,EAAM3M,EAAI2M,IAAIvL,OAGH,QAATvI,IACF0E,EAAMyC,EAAIzC,IAAI6D,MACVyL,GAA6B,IAAjBtP,EAAI7C,UAClB6C,EAAI+E,KAAKrG,IAIN,CACL0Q,IAAKA,EACLpP,IAAKA,IAIa,IAAlB9C,KAAKC,UAAmC,IAAjBuB,EAAIvB,UAC7BsF,EAAMvF,KAAKmS,OAAO3Q,EAAImF,MAAOvI,GAEhB,QAATA,IACF8T,EAAM3M,EAAI2M,IAAIvL,OAGT,CACLuL,IAAKA,EACLpP,IAAKyC,EAAIzC,MAI0B,IAAlC9C,KAAKC,SAAWuB,EAAIvB,WACvBsF,EAAMvF,KAAK2G,MAAMwL,OAAO3Q,EAAImF,MAAOvI,GAEtB,QAATA,IACF0E,EAAMyC,EAAIzC,IAAI6D,MACVyL,GAA6B,IAAjBtP,EAAI7C,UAClB6C,EAAIgF,KAAKtG,IAIN,CACL0Q,IAAK3M,EAAI2M,IACTpP,IAAKA,IAOLtB,EAAIrB,OAASH,KAAKG,QAAUH,KAAK8B,IAAIN,GAAO,EACvC,CACL0Q,IAAK,IAAIvS,EAAG,GACZmD,IAAK9C,MAKU,IAAfwB,EAAIrB,OACO,QAAT/B,EACK,CACL8T,IAAKlS,KAAKqS,KAAK7Q,EAAItB,MAAM,IACzB4C,IAAK,MAII,QAAT1E,EACK,CACL8T,IAAK,KACLpP,IAAK,IAAInD,EAAGK,KAAK4E,KAAKpD,EAAItB,MAAM,MAI7B,CACLgS,IAAKlS,KAAKqS,KAAK7Q,EAAItB,MAAM,IACzB4C,IAAK,IAAInD,EAAGK,KAAK4E,KAAKpD,EAAItB,MAAM,MAI7BF,KAAK8R,SAAStQ,EAAKpD,GAlF1B,IAAI8T,EAAKpP,EAAKyC,GAsFhB5F,EAAGd,UAAUqT,IAAM,SAAc1Q,GAC/B,OAAOxB,KAAKmS,OAAO3Q,EAAK,OAAO,GAAO0Q,KAIxCvS,EAAGd,UAAUiE,IAAM,SAActB,GAC/B,OAAOxB,KAAKmS,OAAO3Q,EAAK,OAAO,GAAOsB,KAGxCnD,EAAGd,UAAUyT,KAAO,SAAe9Q,GACjC,OAAOxB,KAAKmS,OAAO3Q,EAAK,OAAO,GAAMsB,KAIvCnD,EAAGd,UAAU0T,SAAW,SAAmB/Q,GACzC,IAAIgR,EAAKxS,KAAKmS,OAAO3Q,GAGrB,GAAIgR,EAAG1P,IAAI6B,SAAU,OAAO6N,EAAGN,IAE/B,IAAIpP,EAA0B,IAApB0P,EAAGN,IAAIjS,SAAiBuS,EAAG1P,IAAIgF,KAAKtG,GAAOgR,EAAG1P,IAEpD2P,EAAOjR,EAAI6P,MAAM,GACjBqB,EAAKlR,EAAIiE,MAAM,GACf3D,EAAMgB,EAAIhB,IAAI2Q,GAGlB,OAAI3Q,EAAM,GAAY,IAAP4Q,GAAoB,IAAR5Q,EAAkB0Q,EAAGN,IAGrB,IAApBM,EAAGN,IAAIjS,SAAiBuS,EAAGN,IAAIV,MAAM,GAAKgB,EAAGN,IAAI7L,MAAM,IAGhE1G,EAAGd,UAAU+F,KAAO,SAAepD,GACjCvC,EAAOuC,GAAO,UAId,IAHA,IAAIzC,GAAK,GAAK,IAAMyC,EAEhBmR,EAAM,EACD1V,EAAI+C,KAAKG,OAAS,EAAGlD,GAAK,EAAGA,IACpC0V,GAAO5T,EAAI4T,GAAuB,EAAhB3S,KAAKE,MAAMjD,KAAWuE,EAG1C,OAAOmR,GAIThT,EAAGd,UAAUgG,MAAQ,SAAgBrD,GACnCvC,EAAOuC,GAAO,UAGd,IADA,IAAI2C,EAAQ,EACHlH,EAAI+C,KAAKG,OAAS,EAAGlD,GAAK,EAAGA,IAAK,CACzC,IAAIuF,GAAqB,EAAhBxC,KAAKE,MAAMjD,IAAkB,SAARkH,EAC9BnE,KAAKE,MAAMjD,GAAMuF,EAAIhB,EAAO,EAC5B2C,EAAQ3B,EAAIhB,EAGd,OAAOxB,KAAK0C,SAGd/C,EAAGd,UAAUwT,KAAO,SAAe7Q,GACjC,OAAOxB,KAAKqD,QAAQwB,MAAMrD,IAG5B7B,EAAGd,UAAU+T,KAAO,SAAe7T,GACjCE,EAAsB,IAAfF,EAAEkB,UACThB,GAAQF,EAAE4F,UAEV,IAAI2I,EAAItN,KACJuN,EAAIxO,EAAEsE,QAGRiK,EADiB,IAAfA,EAAErN,SACAqN,EAAEgF,KAAKvT,GAEPuO,EAAEjK,QAaR,IATA,IAAIwP,EAAI,IAAIlT,EAAG,GACXmT,EAAI,IAAInT,EAAG,GAGXoT,EAAI,IAAIpT,EAAG,GACXqT,EAAI,IAAIrT,EAAG,GAEXsT,EAAI,EAED3F,EAAE4F,UAAY3F,EAAE2F,UACrB5F,EAAE5H,OAAO,GACT6H,EAAE7H,OAAO,KACPuN,EAMJ,IAHA,IAAIE,EAAK5F,EAAElK,QACP+P,EAAK9F,EAAEjK,SAEHiK,EAAE3I,UAAU,CAClB,IAAK,IAAI1H,EAAI,EAAGoW,EAAK,EAAyB,IAArB/F,EAAEpN,MAAM,GAAKmT,IAAapW,EAAI,KAAMA,EAAGoW,IAAO,GACvE,GAAIpW,EAAI,EAEN,IADAqQ,EAAE5H,OAAOzI,GACFA,KAAM,IACP4V,EAAES,SAAWR,EAAEQ,WACjBT,EAAEhL,KAAKsL,GACPL,EAAEhL,KAAKsL,IAGTP,EAAEnN,OAAO,GACToN,EAAEpN,OAAO,GAIb,IAAK,IAAInD,EAAI,EAAGgR,EAAK,EAAyB,IAArBhG,EAAErN,MAAM,GAAKqT,IAAahR,EAAI,KAAMA,EAAGgR,IAAO,GACvE,GAAIhR,EAAI,EAEN,IADAgL,EAAE7H,OAAOnD,GACFA,KAAM,IACPwQ,EAAEO,SAAWN,EAAEM,WACjBP,EAAElL,KAAKsL,GACPH,EAAElL,KAAKsL,IAGTL,EAAErN,OAAO,GACTsN,EAAEtN,OAAO,GAIT4H,EAAExL,IAAIyL,IAAM,GACdD,EAAExF,KAAKyF,GACPsF,EAAE/K,KAAKiL,GACPD,EAAEhL,KAAKkL,KAEPzF,EAAEzF,KAAKwF,GACPyF,EAAEjL,KAAK+K,GACPG,EAAElL,KAAKgL,IAIX,MAAO,CACL9O,EAAG+O,EACH9O,EAAG+O,EACHQ,IAAKjG,EAAEgD,OAAO0C,KAOlBtT,EAAGd,UAAU4U,OAAS,SAAiB1U,GACrCE,EAAsB,IAAfF,EAAEkB,UACThB,GAAQF,EAAE4F,UAEV,IAAIX,EAAIhE,KACJiE,EAAIlF,EAAEsE,QAGRW,EADiB,IAAfA,EAAE/D,SACA+D,EAAEsO,KAAKvT,GAEPiF,EAAEX,QAQR,IALA,IAuCIkC,EAvCAmO,EAAK,IAAI/T,EAAG,GACZgU,EAAK,IAAIhU,EAAG,GAEZiU,EAAQ3P,EAAEZ,QAEPW,EAAE6P,KAAK,GAAK,GAAK5P,EAAE4P,KAAK,GAAK,GAAG,CACrC,IAAK,IAAI5W,EAAI,EAAGoW,EAAK,EAAyB,IAArBrP,EAAE9D,MAAM,GAAKmT,IAAapW,EAAI,KAAMA,EAAGoW,IAAO,GACvE,GAAIpW,EAAI,EAEN,IADA+G,EAAE0B,OAAOzI,GACFA,KAAM,GACPyW,EAAGJ,SACLI,EAAG7L,KAAK+L,GAGVF,EAAGhO,OAAO,GAId,IAAK,IAAInD,EAAI,EAAGgR,EAAK,EAAyB,IAArBtP,EAAE/D,MAAM,GAAKqT,IAAahR,EAAI,KAAMA,EAAGgR,IAAO,GACvE,GAAIhR,EAAI,EAEN,IADA0B,EAAEyB,OAAOnD,GACFA,KAAM,GACPoR,EAAGL,SACLK,EAAG9L,KAAK+L,GAGVD,EAAGjO,OAAO,GAIV1B,EAAElC,IAAImC,IAAM,GACdD,EAAE8D,KAAK7D,GACPyP,EAAG5L,KAAK6L,KAER1P,EAAE6D,KAAK9D,GACP2P,EAAG7L,KAAK4L,IAeZ,OATEnO,EADgB,IAAdvB,EAAE6P,KAAK,GACHH,EAEAC,GAGAE,KAAK,GAAK,GAChBtO,EAAIsC,KAAK9I,GAGJwG,GAGT5F,EAAGd,UAAU2U,IAAM,SAAchS,GAC/B,GAAIxB,KAAK2E,SAAU,OAAOnD,EAAI2E,MAC9B,GAAI3E,EAAImD,SAAU,OAAO3E,KAAKmG,MAE9B,IAAInC,EAAIhE,KAAKqD,QACTY,EAAIzC,EAAI6B,QACZW,EAAE/D,SAAW,EACbgE,EAAEhE,SAAW,EAGb,IAAK,IAAI4R,EAAQ,EAAG7N,EAAEkP,UAAYjP,EAAEiP,SAAUrB,IAC5C7N,EAAE0B,OAAO,GACTzB,EAAEyB,OAAO,GAGX,OAAG,CACD,KAAO1B,EAAEkP,UACPlP,EAAE0B,OAAO,GAEX,KAAOzB,EAAEiP,UACPjP,EAAEyB,OAAO,GAGX,IAAI3H,EAAIiG,EAAElC,IAAImC,GACd,GAAIlG,EAAI,EAAG,CAET,IAAII,EAAI6F,EACRA,EAAIC,EACJA,EAAI9F,OACC,GAAU,IAANJ,GAAyB,IAAdkG,EAAE4P,KAAK,GAC3B,MAGF7P,EAAE8D,KAAK7D,GAGT,OAAOA,EAAEsM,OAAOsB,IAIlBlS,EAAGd,UAAUiV,KAAO,SAAetS,GACjC,OAAOxB,KAAK4S,KAAKpR,GAAKwC,EAAEsO,KAAK9Q,IAG/B7B,EAAGd,UAAUqU,OAAS,WACpB,OAA+B,IAAP,EAAhBlT,KAAKE,MAAM,KAGrBP,EAAGd,UAAUyU,MAAQ,WACnB,OAA+B,IAAP,EAAhBtT,KAAKE,MAAM,KAIrBP,EAAGd,UAAU4G,MAAQ,SAAgBjE,GACnC,OAAOxB,KAAKE,MAAM,GAAKsB,GAIzB7B,EAAGd,UAAUkV,MAAQ,SAAgBpM,GACnC1I,EAAsB,iBAAR0I,GACd,IAAI5J,EAAI4J,EAAM,GACV3I,GAAK2I,EAAM5J,GAAK,GAChByH,EAAI,GAAKzH,EAGb,GAAIiC,KAAKG,QAAUnB,EAGjB,OAFAgB,KAAKsD,QAAQtE,EAAI,GACjBgB,KAAKE,MAAMlB,IAAMwG,EACVxF,KAKT,IADA,IAAImE,EAAQqB,EACHvI,EAAI+B,EAAa,IAAVmF,GAAelH,EAAI+C,KAAKG,OAAQlD,IAAK,CACnD,IAAIuF,EAAoB,EAAhBxC,KAAKE,MAAMjD,GAEnBkH,GADA3B,GAAK2B,KACS,GACd3B,GAAK,SACLxC,KAAKE,MAAMjD,GAAKuF,EAMlB,OAJc,IAAV2B,IACFnE,KAAKE,MAAMjD,GAAKkH,EAChBnE,KAAKG,UAEAH,MAGTL,EAAGd,UAAU8F,OAAS,WACpB,OAAuB,IAAhB3E,KAAKG,QAAkC,IAAlBH,KAAKE,MAAM,IAGzCP,EAAGd,UAAUgV,KAAO,SAAerS,GACjC,IAOI+D,EAPAtF,EAAWuB,EAAM,EAErB,GAAsB,IAAlBxB,KAAKC,WAAmBA,EAAU,OAAQ,EAC9C,GAAsB,IAAlBD,KAAKC,UAAkBA,EAAU,OAAO,EAK5C,GAHAD,KAAK0C,QAGD1C,KAAKG,OAAS,EAChBoF,EAAM,MACD,CACDtF,IACFuB,GAAOA,GAGTvC,EAAOuC,GAAO,SAAW,qBAEzB,IAAIgB,EAAoB,EAAhBxC,KAAKE,MAAM,GACnBqF,EAAM/C,IAAMhB,EAAM,EAAIgB,EAAIhB,GAAO,EAAI,EAEvC,OAAsB,IAAlBxB,KAAKC,SAA8B,GAANsF,EAC1BA,GAOT5F,EAAGd,UAAUiD,IAAM,SAAcN,GAC/B,GAAsB,IAAlBxB,KAAKC,UAAmC,IAAjBuB,EAAIvB,SAAgB,OAAQ,EACvD,GAAsB,IAAlBD,KAAKC,UAAmC,IAAjBuB,EAAIvB,SAAgB,OAAO,EAEtD,IAAIsF,EAAMvF,KAAKgU,KAAKxS,GACpB,OAAsB,IAAlBxB,KAAKC,SAA8B,GAANsF,EAC1BA,GAIT5F,EAAGd,UAAUmV,KAAO,SAAexS,GAEjC,GAAIxB,KAAKG,OAASqB,EAAIrB,OAAQ,OAAO,EACrC,GAAIH,KAAKG,OAASqB,EAAIrB,OAAQ,OAAQ,EAGtC,IADA,IAAIoF,EAAM,EACDtI,EAAI+C,KAAKG,OAAS,EAAGlD,GAAK,EAAGA,IAAK,CACzC,IAAI+G,EAAoB,EAAhBhE,KAAKE,MAAMjD,GACfgH,EAAmB,EAAfzC,EAAItB,MAAMjD,GAElB,GAAI+G,IAAMC,EAAV,CACID,EAAIC,EACNsB,GAAO,EACEvB,EAAIC,IACbsB,EAAM,GAER,OAEF,OAAOA,GAGT5F,EAAGd,UAAUoV,IAAM,SAAczS,GAC/B,OAA0B,IAAnBxB,KAAK6T,KAAKrS,IAGnB7B,EAAGd,UAAUqV,GAAK,SAAa1S,GAC7B,OAAyB,IAAlBxB,KAAK8B,IAAIN,IAGlB7B,EAAGd,UAAUsV,KAAO,SAAe3S,GACjC,OAAOxB,KAAK6T,KAAKrS,IAAQ,GAG3B7B,EAAGd,UAAUuV,IAAM,SAAc5S,GAC/B,OAAOxB,KAAK8B,IAAIN,IAAQ,GAG1B7B,EAAGd,UAAUwV,IAAM,SAAc7S,GAC/B,OAA2B,IAApBxB,KAAK6T,KAAKrS,IAGnB7B,EAAGd,UAAUyV,GAAK,SAAa9S,GAC7B,OAA0B,IAAnBxB,KAAK8B,IAAIN,IAGlB7B,EAAGd,UAAU0V,KAAO,SAAe/S,GACjC,OAAOxB,KAAK6T,KAAKrS,IAAQ,GAG3B7B,EAAGd,UAAU2V,IAAM,SAAchT,GAC/B,OAAOxB,KAAK8B,IAAIN,IAAQ,GAG1B7B,EAAGd,UAAU4V,IAAM,SAAcjT,GAC/B,OAA0B,IAAnBxB,KAAK6T,KAAKrS,IAGnB7B,EAAGd,UAAU6V,GAAK,SAAalT,GAC7B,OAAyB,IAAlBxB,KAAK8B,IAAIN,IAOlB7B,EAAGS,IAAM,SAAcoB,GACrB,OAAO,IAAImT,EAAInT,IAGjB7B,EAAGd,UAAU+V,MAAQ,SAAgBC,GAGnC,OAFA5V,GAAQe,KAAKI,IAAK,yCAClBnB,EAAyB,IAAlBe,KAAKC,SAAgB,iCACrB4U,EAAIC,UAAU9U,MAAM+U,UAAUF,IAGvClV,EAAGd,UAAUmW,QAAU,WAErB,OADA/V,EAAOe,KAAKI,IAAK,wDACVJ,KAAKI,IAAI6U,YAAYjV,OAG9BL,EAAGd,UAAUkW,UAAY,SAAoBF,GAE3C,OADA7U,KAAKI,IAAMyU,EACJ7U,MAGTL,EAAGd,UAAUqW,SAAW,SAAmBL,GAEzC,OADA5V,GAAQe,KAAKI,IAAK,yCACXJ,KAAK+U,UAAUF,IAGxBlV,EAAGd,UAAUsW,OAAS,SAAiB3T,GAErC,OADAvC,EAAOe,KAAKI,IAAK,sCACVJ,KAAKI,IAAI2H,IAAI/H,KAAMwB,IAG5B7B,EAAGd,UAAUuW,QAAU,SAAkB5T,GAEvC,OADAvC,EAAOe,KAAKI,IAAK,uCACVJ,KAAKI,IAAIyH,KAAK7H,KAAMwB,IAG7B7B,EAAGd,UAAUwW,OAAS,SAAiB7T,GAErC,OADAvC,EAAOe,KAAKI,IAAK,sCACVJ,KAAKI,IAAI4H,IAAIhI,KAAMwB,IAG5B7B,EAAGd,UAAUyW,QAAU,SAAkB9T,GAEvC,OADAvC,EAAOe,KAAKI,IAAK,uCACVJ,KAAKI,IAAI0H,KAAK9H,KAAMwB,IAG7B7B,EAAGd,UAAU0W,OAAS,SAAiB/T,GAErC,OADAvC,EAAOe,KAAKI,IAAK,sCACVJ,KAAKI,IAAIoV,IAAIxV,KAAMwB,IAG5B7B,EAAGd,UAAU4W,OAAS,SAAiBjU,GAGrC,OAFAvC,EAAOe,KAAKI,IAAK,sCACjBJ,KAAKI,IAAIsV,SAAS1V,KAAMwB,GACjBxB,KAAKI,IAAIgB,IAAIpB,KAAMwB,IAG5B7B,EAAGd,UAAU8W,QAAU,SAAkBnU,GAGvC,OAFAvC,EAAOe,KAAKI,IAAK,sCACjBJ,KAAKI,IAAIsV,SAAS1V,KAAMwB,GACjBxB,KAAKI,IAAI4L,KAAKhM,KAAMwB,IAG7B7B,EAAGd,UAAU+W,OAAS,WAGpB,OAFA3W,EAAOe,KAAKI,IAAK,sCACjBJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAIgQ,IAAIpQ,OAGtBL,EAAGd,UAAUiX,QAAU,WAGrB,OAFA7W,EAAOe,KAAKI,IAAK,uCACjBJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAIiQ,KAAKrQ,OAIvBL,EAAGd,UAAUkX,QAAU,WAGrB,OAFA9W,EAAOe,KAAKI,IAAK,uCACjBJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAI4V,KAAKhW,OAGvBL,EAAGd,UAAUoX,QAAU,WAGrB,OAFAhX,EAAOe,KAAKI,IAAK,uCACjBJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAI0T,KAAK9T,OAIvBL,EAAGd,UAAUqX,OAAS,WAGpB,OAFAjX,EAAOe,KAAKI,IAAK,sCACjBJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAIuG,IAAI3G,OAGtBL,EAAGd,UAAUsX,OAAS,SAAiB3U,GAGrC,OAFAvC,EAAOe,KAAKI,MAAQoB,EAAIpB,IAAK,qBAC7BJ,KAAKI,IAAIyV,SAAS7V,MACXA,KAAKI,IAAI8C,IAAIlD,KAAMwB,IAI5B,IAAI4U,EAAS,CACXC,KAAM,KACNC,KAAM,KACNC,KAAM,KACNC,OAAQ,MAIV,SAASC,EAAQjZ,EAAMuB,GAErBiB,KAAKxC,KAAOA,EACZwC,KAAKjB,EAAI,IAAIY,EAAGZ,EAAG,IACnBiB,KAAKtB,EAAIsB,KAAKjB,EAAE+G,YAChB9F,KAAKoE,EAAI,IAAIzE,EAAG,GAAG4Q,OAAOvQ,KAAKtB,GAAGoJ,KAAK9H,KAAKjB,GAE5CiB,KAAK0W,IAAM1W,KAAK2W,OAiDlB,SAASC,IACPH,EAAOrZ,KACL4C,KACA,OACA,2EA+DJ,SAAS6W,IACPJ,EAAOrZ,KACL4C,KACA,OACA,kEAIJ,SAAS8W,IACPL,EAAOrZ,KACL4C,KACA,OACA,yDAIJ,SAAS+W,IAEPN,EAAOrZ,KACL4C,KACA,QACA,uEA8CJ,SAAS2U,EAAKtX,GACZ,GAAiB,iBAANA,EAAgB,CACzB,IAAI2Z,EAAQrX,EAAGsX,OAAO5Z,GACtB2C,KAAK3C,EAAI2Z,EAAMjY,EACfiB,KAAKgX,MAAQA,OAEb/X,EAAO5B,EAAE4W,IAAI,GAAI,kCACjBjU,KAAK3C,EAAIA,EACT2C,KAAKgX,MAAQ,KAkOjB,SAASE,EAAM7Z,GACbsX,EAAIvX,KAAK4C,KAAM3C,GAEf2C,KAAK6R,MAAQ7R,KAAK3C,EAAEyI,YAChB9F,KAAK6R,MAAQ,IAAO,IACtB7R,KAAK6R,OAAS,GAAM7R,KAAK6R,MAAQ,IAGnC7R,KAAKjC,EAAI,IAAI4B,EAAG,GAAG4Q,OAAOvQ,KAAK6R,OAC/B7R,KAAK0S,GAAK1S,KAAKmX,KAAKnX,KAAKjC,EAAEqS,OAC3BpQ,KAAKoX,KAAOpX,KAAKjC,EAAE0V,OAAOzT,KAAK3C,GAE/B2C,KAAKqX,KAAOrX,KAAKoX,KAAKhW,IAAIpB,KAAKjC,GAAGyT,MAAM,GAAGU,IAAIlS,KAAK3C,GACpD2C,KAAKqX,KAAOrX,KAAKqX,KAAK/E,KAAKtS,KAAKjC,GAChCiC,KAAKqX,KAAOrX,KAAKjC,EAAEiK,IAAIhI,KAAKqX,MA5a9BZ,EAAO5X,UAAU8X,KAAO,WACtB,IAAID,EAAM,IAAI/W,EAAG,MAEjB,OADA+W,EAAIxW,MAAQ,IAAIuB,MAAMH,KAAKgB,KAAKtC,KAAKtB,EAAI,KAClCgY,GAGTD,EAAO5X,UAAUyY,QAAU,SAAkB9V,GAG3C,IACI+V,EADAxZ,EAAIyD,EAGR,GACExB,KAAKwX,MAAMzZ,EAAGiC,KAAK0W,KAGnBa,GADAxZ,GADAA,EAAIiC,KAAKyX,MAAM1Z,IACT8J,KAAK7H,KAAK0W,MACP5Q,kBACFyR,EAAOvX,KAAKtB,GAErB,IAAIoD,EAAMyV,EAAOvX,KAAKtB,GAAK,EAAIX,EAAEiW,KAAKhU,KAAKjB,GAgB3C,OAfY,IAAR+C,GACF/D,EAAEmC,MAAM,GAAK,EACbnC,EAAEoC,OAAS,GACF2B,EAAM,EACf/D,EAAE+J,KAAK9H,KAAKjB,QAEI2Y,IAAZ3Z,EAAE2E,MAEJ3E,EAAE2E,QAGF3E,EAAE4Z,SAIC5Z,GAGT0Y,EAAO5X,UAAU2Y,MAAQ,SAAgBI,EAAO7T,GAC9C6T,EAAMlS,OAAO1F,KAAKtB,EAAG,EAAGqF,IAG1B0S,EAAO5X,UAAU4Y,MAAQ,SAAgBjW,GACvC,OAAOA,EAAIwK,KAAKhM,KAAKoE,IASvB/E,EAASuX,EAAMH,GAEfG,EAAK/X,UAAU2Y,MAAQ,SAAgBI,EAAOC,GAK5C,IAHA,IAEIC,EAASxW,KAAKC,IAAIqW,EAAMzX,OAAQ,GAC3BlD,EAAI,EAAGA,EAAI6a,EAAQ7a,IAC1B4a,EAAO3X,MAAMjD,GAAK2a,EAAM1X,MAAMjD,GAIhC,GAFA4a,EAAO1X,OAAS2X,EAEZF,EAAMzX,QAAU,EAGlB,OAFAyX,EAAM1X,MAAM,GAAK,OACjB0X,EAAMzX,OAAS,GAKjB,IAAI4X,EAAOH,EAAM1X,MAAM,GAGvB,IAFA2X,EAAO3X,MAAM2X,EAAO1X,UAhBT,QAgBqB4X,EAE3B9a,EAAI,GAAIA,EAAI2a,EAAMzX,OAAQlD,IAAK,CAClC,IAAI+a,EAAwB,EAAjBJ,EAAM1X,MAAMjD,GACvB2a,EAAM1X,MAAMjD,EAAI,KApBP,QAoBe+a,IAAgB,EAAMD,IAAS,GACvDA,EAAOC,EAETD,KAAU,GACVH,EAAM1X,MAAMjD,EAAI,IAAM8a,EACT,IAATA,GAAcH,EAAMzX,OAAS,GAC/ByX,EAAMzX,QAAU,GAEhByX,EAAMzX,QAAU,GAIpByW,EAAK/X,UAAU4Y,MAAQ,SAAgBjW,GAErCA,EAAItB,MAAMsB,EAAIrB,QAAU,EACxBqB,EAAItB,MAAMsB,EAAIrB,OAAS,GAAK,EAC5BqB,EAAIrB,QAAU,EAId,IADA,IAAI+D,EAAK,EACAjH,EAAI,EAAGA,EAAIuE,EAAIrB,OAAQlD,IAAK,CACnC,IAAIuF,EAAmB,EAAfhB,EAAItB,MAAMjD,GAClBiH,GAAU,IAAJ1B,EACNhB,EAAItB,MAAMjD,GAAU,SAALiH,EACfA,EAAS,GAAJ1B,GAAa0B,EAAK,SAAa,GAUtC,OANkC,IAA9B1C,EAAItB,MAAMsB,EAAIrB,OAAS,KACzBqB,EAAIrB,SAC8B,IAA9BqB,EAAItB,MAAMsB,EAAIrB,OAAS,IACzBqB,EAAIrB,UAGDqB,GASTnC,EAASwX,EAAMJ,GAQfpX,EAASyX,EAAML,GASfpX,EAAS0X,EAAQN,GAEjBM,EAAOlY,UAAU4Y,MAAQ,SAAgBjW,GAGvC,IADA,IAAI2C,EAAQ,EACHlH,EAAI,EAAGA,EAAIuE,EAAIrB,OAAQlD,IAAK,CACnC,IAAI8I,EAA0B,IAAL,EAAfvE,EAAItB,MAAMjD,IAAiBkH,EACjCD,EAAU,SAAL6B,EACTA,KAAQ,GAERvE,EAAItB,MAAMjD,GAAKiH,EACfC,EAAQ4B,EAKV,OAHc,IAAV5B,IACF3C,EAAItB,MAAMsB,EAAIrB,UAAYgE,GAErB3C,GAIT7B,EAAGsX,OAAS,SAAgBzZ,GAE1B,GAAI4Y,EAAO5Y,GAAO,OAAO4Y,EAAO5Y,GAEhC,IAAIwZ,EACJ,GAAa,SAATxZ,EACFwZ,EAAQ,IAAIJ,OACP,GAAa,SAATpZ,EACTwZ,EAAQ,IAAIH,OACP,GAAa,SAATrZ,EACTwZ,EAAQ,IAAIF,MACP,IAAa,WAATtZ,EAGT,MAAM,IAAI4B,MAAM,iBAAmB5B,GAFnCwZ,EAAQ,IAAID,EAMd,OAFAX,EAAO5Y,GAAQwZ,EAERA,GAkBTrC,EAAI9V,UAAUgX,SAAW,SAAmB7R,GAC1C/E,EAAsB,IAAf+E,EAAE/D,SAAgB,iCACzBhB,EAAO+E,EAAE5D,IAAK,oCAGhBuU,EAAI9V,UAAU6W,SAAW,SAAmB1R,EAAGC,GAC7ChF,EAAqC,IAA7B+E,EAAE/D,SAAWgE,EAAEhE,UAAiB,iCACxChB,EAAO+E,EAAE5D,KAAO4D,EAAE5D,MAAQ6D,EAAE7D,IAC1B,oCAGJuU,EAAI9V,UAAUsY,KAAO,SAAenT,GAClC,OAAIhE,KAAKgX,MAAchX,KAAKgX,MAAMM,QAAQtT,GAAG+Q,UAAU/U,MAChDgE,EAAEsO,KAAKtS,KAAK3C,GAAG0X,UAAU/U,OAGlC2U,EAAI9V,UAAU8H,IAAM,SAAc3C,GAChC,OAAIA,EAAEW,SACGX,EAAEX,QAGJrD,KAAK3C,EAAE2K,IAAIhE,GAAG+Q,UAAU/U,OAGjC2U,EAAI9V,UAAUkJ,IAAM,SAAc/D,EAAGC,GACnCjE,KAAK0V,SAAS1R,EAAGC,GAEjB,IAAIsB,EAAMvB,EAAE+D,IAAI9D,GAIhB,OAHIsB,EAAIzD,IAAI9B,KAAK3C,IAAM,GACrBkI,EAAIuC,KAAK9H,KAAK3C,GAETkI,EAAIwP,UAAU/U,OAGvB2U,EAAI9V,UAAUgJ,KAAO,SAAe7D,EAAGC,GACrCjE,KAAK0V,SAAS1R,EAAGC,GAEjB,IAAIsB,EAAMvB,EAAE6D,KAAK5D,GAIjB,OAHIsB,EAAIzD,IAAI9B,KAAK3C,IAAM,GACrBkI,EAAIuC,KAAK9H,KAAK3C,GAETkI,GAGToP,EAAI9V,UAAUmJ,IAAM,SAAchE,EAAGC,GACnCjE,KAAK0V,SAAS1R,EAAGC,GAEjB,IAAIsB,EAAMvB,EAAEgE,IAAI/D,GAIhB,OAHIsB,EAAIsO,KAAK,GAAK,GAChBtO,EAAIsC,KAAK7H,KAAK3C,GAETkI,EAAIwP,UAAU/U,OAGvB2U,EAAI9V,UAAUiJ,KAAO,SAAe9D,EAAGC,GACrCjE,KAAK0V,SAAS1R,EAAGC,GAEjB,IAAIsB,EAAMvB,EAAE8D,KAAK7D,GAIjB,OAHIsB,EAAIsO,KAAK,GAAK,GAChBtO,EAAIsC,KAAK7H,KAAK3C,GAETkI,GAGToP,EAAI9V,UAAU2W,IAAM,SAAcxR,EAAGxC,GAEnC,OADAxB,KAAK6V,SAAS7R,GACPhE,KAAKmX,KAAKnT,EAAEmN,MAAM3P,KAG3BmT,EAAI9V,UAAUmN,KAAO,SAAehI,EAAGC,GAErC,OADAjE,KAAK0V,SAAS1R,EAAGC,GACVjE,KAAKmX,KAAKnT,EAAEgI,KAAK/H,KAG1B0Q,EAAI9V,UAAUuC,IAAM,SAAc4C,EAAGC,GAEnC,OADAjE,KAAK0V,SAAS1R,EAAGC,GACVjE,KAAKmX,KAAKnT,EAAE5C,IAAI6C,KAGzB0Q,EAAI9V,UAAUwR,KAAO,SAAerM,GAClC,OAAOhE,KAAKgM,KAAKhI,EAAGA,EAAEX,UAGxBsR,EAAI9V,UAAUuR,IAAM,SAAcpM,GAChC,OAAOhE,KAAKoB,IAAI4C,EAAGA,IAGrB2Q,EAAI9V,UAAUmX,KAAO,SAAehS,GAClC,GAAIA,EAAEW,SAAU,OAAOX,EAAEX,QAEzB,IAAI4U,EAAOjY,KAAK3C,EAAEoI,MAAM,GAIxB,GAHAxG,EAAOgZ,EAAO,GAAM,GAGP,IAATA,EAAY,CACd,IAAI/U,EAAMlD,KAAK3C,EAAE0K,IAAI,IAAIpI,EAAG,IAAI+F,OAAO,GACvC,OAAO1F,KAAKkD,IAAIc,EAAGd,GAQrB,IAFA,IAAIsC,EAAIxF,KAAK3C,EAAEqU,KAAK,GAChB1S,EAAI,GACAwG,EAAEb,UAA2B,IAAfa,EAAEC,MAAM,IAC5BzG,IACAwG,EAAEE,OAAO,GAEXzG,GAAQuG,EAAEb,UAEV,IAAIuT,EAAM,IAAIvY,EAAG,GAAGiV,MAAM5U,MACtBmY,EAAOD,EAAIhC,SAIXkC,EAAOpY,KAAK3C,EAAEqU,KAAK,GAAGhM,OAAO,GAC7B2S,EAAIrY,KAAK3C,EAAEyI,YAGf,IAFAuS,EAAI,IAAI1Y,EAAG,EAAI0Y,EAAIA,GAAGzD,MAAM5U,MAEW,IAAhCA,KAAKkD,IAAImV,EAAGD,GAAMtW,IAAIqW,IAC3BE,EAAEjD,QAAQ+C,GAOZ,IAJA,IAAI7a,EAAI0C,KAAKkD,IAAImV,EAAG7S,GAChBzH,EAAIiC,KAAKkD,IAAIc,EAAGwB,EAAEiM,KAAK,GAAG/L,OAAO,IACjCvH,EAAI6B,KAAKkD,IAAIc,EAAGwB,GAChBnI,EAAI2B,EACc,IAAfb,EAAE2D,IAAIoW,IAAY,CAEvB,IADA,IAAIxB,EAAMvY,EACDlB,EAAI,EAAoB,IAAjByZ,EAAI5U,IAAIoW,GAAYjb,IAClCyZ,EAAMA,EAAId,SAEZ3W,EAAOhC,EAAII,GACX,IAAI4G,EAAIjE,KAAKkD,IAAI5F,EAAG,IAAIqC,EAAG,GAAG4Q,OAAOlT,EAAIJ,EAAI,IAE7Cc,EAAIA,EAAE0X,OAAOxR,GACb3G,EAAI2G,EAAE2R,SACNzX,EAAIA,EAAEsX,OAAOnY,GACbD,EAAIJ,EAGN,OAAOc,GAGT4W,EAAI9V,UAAUiV,KAAO,SAAe9P,GAClC,IAAIsU,EAAMtU,EAAEyP,OAAOzT,KAAK3C,GACxB,OAAqB,IAAjBib,EAAIrY,UACNqY,EAAIrY,SAAW,EACRD,KAAKmX,KAAKmB,GAAKpC,UAEflW,KAAKmX,KAAKmB,IAIrB3D,EAAI9V,UAAUqE,IAAM,SAAcc,EAAGxC,GACnC,GAAIA,EAAImD,SAAU,OAAO,IAAIhF,EAAG,GAAGiV,MAAM5U,MACzC,GAAoB,IAAhBwB,EAAIqS,KAAK,GAAU,OAAO7P,EAAEX,QAEhC,IACIkV,EAAM,IAAI9W,MAAM,IACpB8W,EAAI,GAAK,IAAI5Y,EAAG,GAAGiV,MAAM5U,MACzBuY,EAAI,GAAKvU,EACT,IAAK,IAAI/G,EAAI,EAAGA,EAAIsb,EAAIpY,OAAQlD,IAC9Bsb,EAAItb,GAAK+C,KAAKoB,IAAImX,EAAItb,EAAI,GAAI+G,GAGhC,IAAIuB,EAAMgT,EAAI,GACVC,EAAU,EACVC,EAAa,EACbvX,EAAQM,EAAIsE,YAAc,GAK9B,IAJc,IAAV5E,IACFA,EAAQ,IAGLjE,EAAIuE,EAAIrB,OAAS,EAAGlD,GAAK,EAAGA,IAAK,CAEpC,IADA,IAAI8F,EAAOvB,EAAItB,MAAMjD,GACZsF,EAAIrB,EAAQ,EAAGqB,GAAK,EAAGA,IAAK,CACnC,IAAIoF,EAAO5E,GAAQR,EAAK,EACpBgD,IAAQgT,EAAI,KACdhT,EAAMvF,KAAKoQ,IAAI7K,IAGL,IAARoC,GAAyB,IAAZ6Q,GAKjBA,IAAY,EACZA,GAAW7Q,GA9BE,MA+Bb8Q,GACwC,IAANxb,GAAiB,IAANsF,KAE7CgD,EAAMvF,KAAKoB,IAAImE,EAAKgT,EAAIC,IACxBC,EAAa,EACbD,EAAU,IAXRC,EAAa,EAajBvX,EAAQ,GAGV,OAAOqE,GAGToP,EAAI9V,UAAUiW,UAAY,SAAoBtT,GAC5C,IAAIzD,EAAIyD,EAAI8Q,KAAKtS,KAAK3C,GAEtB,OAAOU,IAAMyD,EAAMzD,EAAEsF,QAAUtF,GAGjC4W,EAAI9V,UAAUoW,YAAc,SAAsBzT,GAChD,IAAI+D,EAAM/D,EAAI6B,QAEd,OADAkC,EAAInF,IAAM,KACHmF,GAOT5F,EAAG+Y,KAAO,SAAelX,GACvB,OAAO,IAAI0V,EAAK1V,IAmBlBnC,EAAS6X,EAAMvC,GAEfuC,EAAKrY,UAAUiW,UAAY,SAAoBtT,GAC7C,OAAOxB,KAAKmX,KAAK3V,EAAI2P,MAAMnR,KAAK6R,SAGlCqF,EAAKrY,UAAUoW,YAAc,SAAsBzT,GACjD,IAAIzD,EAAIiC,KAAKmX,KAAK3V,EAAIJ,IAAIpB,KAAKoX,OAE/B,OADArZ,EAAEqC,IAAM,KACDrC,GAGTmZ,EAAKrY,UAAUmN,KAAO,SAAehI,EAAGC,GACtC,GAAID,EAAEW,UAAYV,EAAEU,SAGlB,OAFAX,EAAE9D,MAAM,GAAK,EACb8D,EAAE7D,OAAS,EACJ6D,EAGT,IAAI7F,EAAI6F,EAAEgI,KAAK/H,GACX3G,EAAIa,EAAEoT,MAAMvR,KAAK6R,OAAOzQ,IAAIpB,KAAKqX,MAAM/F,OAAOtR,KAAK6R,OAAOzQ,IAAIpB,KAAK3C,GACnEsb,EAAIxa,EAAE2J,KAAKxK,GAAGoI,OAAO1F,KAAK6R,OAC1BtM,EAAMoT,EAQV,OANIA,EAAE7W,IAAI9B,KAAK3C,IAAM,EACnBkI,EAAMoT,EAAE7Q,KAAK9H,KAAK3C,GACTsb,EAAE9E,KAAK,GAAK,IACrBtO,EAAMoT,EAAE9Q,KAAK7H,KAAK3C,IAGbkI,EAAIwP,UAAU/U,OAGvBkX,EAAKrY,UAAUuC,IAAM,SAAc4C,EAAGC,GACpC,GAAID,EAAEW,UAAYV,EAAEU,SAAU,OAAO,IAAIhF,EAAG,GAAGoV,UAAU/U,MAEzD,IAAI7B,EAAI6F,EAAE5C,IAAI6C,GACV3G,EAAIa,EAAEoT,MAAMvR,KAAK6R,OAAOzQ,IAAIpB,KAAKqX,MAAM/F,OAAOtR,KAAK6R,OAAOzQ,IAAIpB,KAAK3C,GACnEsb,EAAIxa,EAAE2J,KAAKxK,GAAGoI,OAAO1F,KAAK6R,OAC1BtM,EAAMoT,EAOV,OANIA,EAAE7W,IAAI9B,KAAK3C,IAAM,EACnBkI,EAAMoT,EAAE7Q,KAAK9H,KAAK3C,GACTsb,EAAE9E,KAAK,GAAK,IACrBtO,EAAMoT,EAAE9Q,KAAK7H,KAAK3C,IAGbkI,EAAIwP,UAAU/U,OAGvBkX,EAAKrY,UAAUiV,KAAO,SAAe9P,GAGnC,OADUhE,KAAKmX,KAAKnT,EAAEyP,OAAOzT,KAAK3C,GAAG+D,IAAIpB,KAAK0S,KACnCqC,UAAU/U,OAn3GzB,CAq3GoChD,EAAQgD,Q,kCCr3G5ChD,EAAOD,QAAU6b,QAAQ,S,cCAzB5b,EAAOD,QAAU6b,QAAQ,0B,cCAzB5b,EAAOD,QAAU,SAASC,GAoBzB,OAnBKA,EAAO6b,kBACX7b,EAAO8b,UAAY,aACnB9b,EAAO+b,MAAQ,GAEV/b,EAAOgc,WAAUhc,EAAOgc,SAAW,IACxCrb,OAAOC,eAAeZ,EAAQ,SAAU,CACvCa,YAAY,EACZC,IAAK,WACJ,OAAOd,EAAOE,KAGhBS,OAAOC,eAAeZ,EAAQ,KAAM,CACnCa,YAAY,EACZC,IAAK,WACJ,OAAOd,EAAOC,KAGhBD,EAAO6b,gBAAkB,GAEnB7b,I,cCpBRA,EAAOD,QAAU6b,QAAQ,W,wxBCIzB,cAGI,WAAY1a,GAAZ,MACI,cAAO,K,OACP,EAAK+a,IAAM,IAAI,IAAG/a,G,EAqF1B,OA1FoC,OAUzB,EAAA6B,KAAP,SAAY7B,GACR,OAAIA,aAAiBgb,GAGd,IAAGnZ,KAAK7B,IAGnB,YAAA+D,SAAA,SAASpC,GACL,OAAOG,KAAKiZ,IAAIhX,SAASpC,IAG7B,YAAAiF,SAAA,WACI,OAAO9E,KAAKiZ,IAAInU,YAGpB,YAAAG,SAAA,SAASpF,GACL,OAAOG,KAAKiZ,IAAIhU,YAOpB,YAAA8C,IAAA,SAAI7J,GAIA,OAAO,IAAIgb,EAHAlZ,KAAKiZ,IAAIlR,IAChB,IAAI,IAAG7J,EAAM+D,eAKrB,YAAA+F,IAAA,SAAI9J,GAIA,OAAO,IAAIgb,EAHAlZ,KAAKiZ,IAAIjR,IAChB,IAAI,IAAG9J,EAAM+D,eAKrB,YAAAb,IAAA,SAAIlD,GAIA,OAAO,IAAIgb,EAHAlZ,KAAKiZ,IAAI7X,IAChB,IAAI,IAAGlD,EAAM+D,eAKrB,YAAAiQ,IAAA,SAAIhU,GAIA,OAAO,IAAIgb,EAHAlZ,KAAKiZ,IAAI/G,IAChB,IAAI,IAAGhU,EAAM+D,eAKrB,YAAAuS,IAAA,SAAItW,GACA,OAAO8B,KAAKiZ,IAAIzE,IACZ,IAAI,IAAGtW,EAAM+D,cAIrB,YAAAqS,GAAA,SAAGpW,GACC,OAAO8B,KAAKiZ,IAAI3E,GACZ,IAAI,IAAGpW,EAAM+D,cAIrB,YAAAmS,IAAA,SAAIlW,GACA,OAAO8B,KAAKiZ,IAAI7E,IACZ,IAAI,IAAGlW,EAAM+D,cAIrB,YAAAiS,GAAA,SAAGhW,GACC,OAAO8B,KAAKiZ,IAAI/E,GACZ,IAAI,IAAGhW,EAAM+D,cAIrB,YAAAyS,GAAA,SAAGxW,GACC,OAAO8B,KAAKiZ,IAAIvE,GACZ,IAAI,IAAGxW,EAAM+D,cAGzB,EA1FA,CAAoC,iBCAvBkX,EAA6B,SAACC,QAAA,IAAAA,MAAA,IACvC,IAAMC,EAAQ,IAAKC,MAAMD,MACzB,MAAO,CACHE,QAASF,EAAMD,EAAOG,SACtBC,KAAMJ,EAAOI,KACbC,KAAML,EAAOK,KACbC,IAAKN,EAAOO,SACZC,SAAUR,EAAOQ,SACjBC,MAAOT,EAAOS,MACdC,GAAIV,EAAOU,GACX5b,MAAOkb,EAAOlb,MACd6b,aAAcX,EAAOW,aACrBC,qBAAsBZ,EAAOY,qBAC7BC,KAAMZ,EAAMD,EAAOa,MACnBC,SAAUd,EAAOc,WCfZC,EAA4B,SAACC,GACtC,MAAO,CACHC,UAAWD,EAAQC,UACnBC,YAAaF,EAAQE,YACrBC,gBAAiBH,EAAQG,gBACzBC,kBAAmBJ,EAAQI,kBAC3Bf,KAAMW,EAAQX,KACdgB,QAASL,EAAQK,QACjBC,OAAQN,EAAQM,OAChBZ,GAAIM,EAAQN,GACZa,gBAAiBP,EAAQO,gBACzBC,iBAAkBR,EAAQQ,iBAC1BC,OAAQT,EAAQS,OAChBC,KAAMV,EAAQU,KACdC,UAAWX,EAAQW,UACnBC,KAAOZ,EAAgBY,KACvBf,KAAOG,EAAgBH,OChBlBgB,EAAkB,SAACC,GAC5B,IAAMC,EAA4BD,EAElC,OADAC,EAAQR,gBAAkBO,EAAGE,KACtBD,GCFX,EAWI,SAAoBE,GAApB,WAAoB,KAAAA,UAChB,IAAMC,EAAiB,IAAIC,SAAa,SAAChW,EAAKiW,GAC1C,EAAKC,qBAAuBlW,EAC5B,EAAKmW,0BAA4BF,KAErCxb,KAAK2b,WAAa,WACd,OAAOL,EAAeM,MAAK,SAAAxB,GACvB,OAAOD,EAA0BC,OAIzC,IAAMyB,EAAgB,IAAIN,SAAgB,SAAChW,EAAKiW,GAC5C,EAAKM,kBAAoBvW,EACzB,EAAKwW,mBAAqBP,KAG9Bxb,KAAKgc,mBAAqB,WACtB,OAAOH,GAGXR,EAAQY,KAAK,kBAAmBjc,KAAK8b,mBACjCG,KAAK,UAAWjc,KAAKyb,sBACrBS,GAAG,QAASlc,KAAK+b,oBACjBG,GAAG,QAASlc,KAAK0b,4B,ydChC7B,cAEI,WAAmBS,EAASC,EAAwBC,GAApD,MACI,YAAMD,IAAO,K,OADE,EAAAD,UAAiC,EAAAE,S,EAkCxD,OApC+B,OAM3B,YAAAhD,MAAA,SAAMnb,GACF,OAAgB,MAATA,EAAgB,IAAKob,MAAMD,MAAMnb,GAASA,GAGrD,YAAAoe,KAAA,SAAQpB,EAA+BqB,GAEnC,OADAvc,KAAKoc,OAAOI,IAAI,yBAA0BtB,EAAIqB,GACtCvc,KAAKqc,OAAOjf,KAChB+b,EAA2B+B,GAAYqB,IAI/C,YAAAE,MAAA,SAAMvB,GAEF,OAAO,IAAI,EACPlb,KAAKqc,OAAOK,KACRvD,EAA2B+B,MAKvC,YAAAyB,YAAA,SAAYzB,GACR,OAAOlb,KAAKqc,OAAOM,YACfxD,EAA2B+B,KAInC,YAAA0B,UAAA,WACI,OAAO5c,KAAKqc,OAAOO,aAG3B,EApCA,CAA+B,sB,ydCF/B,cAGI,WAAYT,EAAiBU,EAAoBT,GAAjD,MACI,YAAMD,EAASC,IAAO,K,OACtB,EAAKS,SAAWA,E,EASxB,OAdkC,OAQ9B,YAAAR,OAAA,SAAOS,G,UAAoB,oDAEvB,OADA9c,KAAKoc,OAAOI,IAAI,aAAcM,EAAY,cAAeC,WAClD,IAAI,EACP/c,KAAKmc,QAASnc,KAAKoc,QAAQ,EAAApc,KAAK6c,SAASG,SAAQF,GAAW,QAAIG,KAG5E,EAdA,CAAkC,gB,syDCIlC,cAII,WAAYC,EAAed,GAA3B,MACI,YAAMA,IAAO,K,OAHjB,EAAA5e,KAAO,OAIH,EAAK2f,MAAQ,IAAI,IAAKD,G,EAqI9B,OA3IgC,OAU5B,YAAAZ,KAAA,SAAKlD,GACD,OAAOpZ,KAAKmd,MAAMC,IAAIhgB,KAClB+b,EAA2BC,KAInC,YAAAqD,MAAA,SAAMrD,GACF,OAAO,IAAI,EACPpZ,KAAKmd,MAAMC,IAAIC,gBACXlE,EAA2BC,MAKvC,YAAAkE,YAAA,SAAYnB,EAAiBoB,GACzB,IAAMC,EAAO,IAAIxd,KAAKmd,MAAMC,IAAIK,SAASF,EAAKpB,GAC9C,OAAO,IAAI,EAAaA,EAASqB,EAAaxd,KAAKoc,SAGvD,YAAAsB,YAAA,WACI,OAAO1d,KAAKmd,MAAMC,IAAIM,eAG1B,YAAAf,YAAA,SAAYvD,GACR,OAAOpZ,KAAKmd,MAAMC,IAAIT,YAClBxD,EAA2BC,KAInC,YAAAuE,oBAAA,SAAoBxB,EAAiB7B,GACjC,OAAOta,KAAKmd,MAAMC,IAAIO,oBAAoBxB,EAAS7B,IAGvD,YAAAsD,YAAA,WACI,OAAO5d,KAAKmd,MAAMC,IAAIQ,eAGpB,YAAAC,WAAN,W,gGAEwB,O,sBAAA,GAAM7d,KAAKmd,MAAMC,IAAIU,IAAIC,S,OACzC,GADMxE,EAAU,SAEZ,MAAO,CAAP,EAAOA,GAEX,MAAM,IAAIna,MAAM,8B,OAEhB,O,SAAO,CAAP,EAAOY,KAAKmd,MAAMC,IAAIS,c,yBAItB,YAAAG,0BAAR,SAAkCxE,GAC9B,IAAKA,EACD,KAAM,CACFS,KAAM,sBACNgE,QAAS,sFAKrB,YAAAC,eAAA,SAAevD,GAAf,WACI,OAAO3a,KAAKmd,MAAMC,IAAIc,eAAevD,GAAiBiB,MAAK,SAAApC,GAEvD,OADA,EAAKwE,0BAA0BxE,GACxByB,EAAgBzB,OAI/B,YAAA2E,sBAAA,SAAsBxD,GAAtB,WACI,OAAO3a,KAAKmd,MAAMC,IAAIe,sBAAsBxD,GAAiBiB,MAAK,SAAApC,GAE9D,OADA,EAAKwE,0BAA0BxE,GACxBW,EAA0BX,OAIzC,YAAA4E,SAAA,SAASC,GACL,OAAQre,KAAKmd,MAAMC,IAAIgB,SAASC,IAGpC,YAAAC,WAAA,SAAWnC,GACP,OAAOnc,KAAKmd,MAAMC,IAAIkB,WAAWnC,IAGrC,YAAAoC,wBAAA,SAAwBF,GACpB,OAAOre,KAAKmd,MAAMC,IAAIgB,SAASC,GAAwB,GAAMzC,MAAK,SAAA4C,GAC9D,IAAMC,EAAmCD,EAIzC,OAHAC,EAAUC,aAAeF,EAAOE,aAAaC,KAAI,SAAAzD,GAC7C,OAAOD,EAAgBC,MAEpBuD,MAIf,YAAAG,eAAA,SAAeC,GAAf,WACI,OAAO,IAAItD,SAA0B,SAAChW,EAAKiW,GACtC,EAAK2B,MAAM2B,gBAAqCpC,KAAKmC,GAAS,SAACE,EAAOP,GACnE,GAAIO,EAAO,OAAOvD,EAAIuD,GACtBxZ,EAAIiZ,UAKhB,YAAAQ,cAAA,SAAcC,EAAQC,GAClB,OAAOlf,KAAK4e,eAAe,CACvBO,QAAS,MACT9C,OAAQ,uBACR+C,OAAQ,CAACH,EAAQC,GACjBG,IAAI,IAAIC,MAAOC,YAChB3D,MAAK,SAAA4D,GACJ,OAAOC,OAAOD,EAAQhB,YAI9B,YAAAkB,iBAAA,SAAiBN,EAAeO,GAC5B,OAAO3f,KAAKmd,MAAMC,IAAIG,IAAImC,iBAAiBC,EAAOP,IAGtD,YAAAQ,iBAAA,SAAiBC,EAAWF,GACxB,OAAO3f,KAAKmd,MAAMC,IAAIG,IAAIqC,iBAAiBD,EAAOE,IAGtD,YAAAC,aAAA,W,UAAa,kDACT,OAAO,MAAKxG,OAAMyG,aAAY,QAAI7hB,IAGtC,YAAA8hB,YAAA,SAAY9hB,GACR,OAAO,IAAKob,MAAM0G,YAAY9hB,IAGlC,YAAA+hB,kBAAA,SAAkB/hB,GACd,OAAO,IAAKob,MAAM2G,kBAAkB/hB,IAE5C,EA3IA,CAAgC,kBCHhC,2BAQA,OAPI,YAAAgiB,MAAA,SAAMC,GACFA,EAAM7G,MAAM8G,WAAa,EACzBD,EAAM7G,MAAM3Z,GAAK,EACjBwgB,EAAM7G,MAAMvZ,KAAO,SAAC7B,GAChB,OAAO,IAAKob,MAAMvZ,KAAK7B,KAGnC,EARA,GAae", "file": "matic-web3.node.min.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 5);\n", "(function (module, exports) {\n  'use strict';\n\n  // Utils\n  function assert (val, msg) {\n    if (!val) throw new Error(msg || 'Assertion failed');\n  }\n\n  // Could use `inherits` module, but don't want to move from single file\n  // architecture yet.\n  function inherits (ctor, superCtor) {\n    ctor.super_ = superCtor;\n    var TempCtor = function () {};\n    TempCtor.prototype = superCtor.prototype;\n    ctor.prototype = new TempCtor();\n    ctor.prototype.constructor = ctor;\n  }\n\n  // BN\n\n  function BN (number, base, endian) {\n    if (BN.isBN(number)) {\n      return number;\n    }\n\n    this.negative = 0;\n    this.words = null;\n    this.length = 0;\n\n    // Reduction context\n    this.red = null;\n\n    if (number !== null) {\n      if (base === 'le' || base === 'be') {\n        endian = base;\n        base = 10;\n      }\n\n      this._init(number || 0, base || 10, endian || 'be');\n    }\n  }\n  if (typeof module === 'object') {\n    module.exports = BN;\n  } else {\n    exports.BN = BN;\n  }\n\n  BN.BN = BN;\n  BN.wordSize = 26;\n\n  var Buffer;\n  try {\n    if (typeof window !== 'undefined' && typeof window.Buffer !== 'undefined') {\n      Buffer = window.Buffer;\n    } else {\n      Buffer = require('buffer').Buffer;\n    }\n  } catch (e) {\n  }\n\n  BN.isBN = function isBN (num) {\n    if (num instanceof BN) {\n      return true;\n    }\n\n    return num !== null && typeof num === 'object' &&\n      num.constructor.wordSize === BN.wordSize && Array.isArray(num.words);\n  };\n\n  BN.max = function max (left, right) {\n    if (left.cmp(right) > 0) return left;\n    return right;\n  };\n\n  BN.min = function min (left, right) {\n    if (left.cmp(right) < 0) return left;\n    return right;\n  };\n\n  BN.prototype._init = function init (number, base, endian) {\n    if (typeof number === 'number') {\n      return this._initNumber(number, base, endian);\n    }\n\n    if (typeof number === 'object') {\n      return this._initArray(number, base, endian);\n    }\n\n    if (base === 'hex') {\n      base = 16;\n    }\n    assert(base === (base | 0) && base >= 2 && base <= 36);\n\n    number = number.toString().replace(/\\s+/g, '');\n    var start = 0;\n    if (number[0] === '-') {\n      start++;\n      this.negative = 1;\n    }\n\n    if (start < number.length) {\n      if (base === 16) {\n        this._parseHex(number, start, endian);\n      } else {\n        this._parseBase(number, base, start);\n        if (endian === 'le') {\n          this._initArray(this.toArray(), base, endian);\n        }\n      }\n    }\n  };\n\n  BN.prototype._initNumber = function _initNumber (number, base, endian) {\n    if (number < 0) {\n      this.negative = 1;\n      number = -number;\n    }\n    if (number < 0x4000000) {\n      this.words = [ number & 0x3ffffff ];\n      this.length = 1;\n    } else if (number < 0x10000000000000) {\n      this.words = [\n        number & 0x3ffffff,\n        (number / 0x4000000) & 0x3ffffff\n      ];\n      this.length = 2;\n    } else {\n      assert(number < 0x20000000000000); // 2 ^ 53 (unsafe)\n      this.words = [\n        number & 0x3ffffff,\n        (number / 0x4000000) & 0x3ffffff,\n        1\n      ];\n      this.length = 3;\n    }\n\n    if (endian !== 'le') return;\n\n    // Reverse the bytes\n    this._initArray(this.toArray(), base, endian);\n  };\n\n  BN.prototype._initArray = function _initArray (number, base, endian) {\n    // Perhaps a Uint8Array\n    assert(typeof number.length === 'number');\n    if (number.length <= 0) {\n      this.words = [ 0 ];\n      this.length = 1;\n      return this;\n    }\n\n    this.length = Math.ceil(number.length / 3);\n    this.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      this.words[i] = 0;\n    }\n\n    var j, w;\n    var off = 0;\n    if (endian === 'be') {\n      for (i = number.length - 1, j = 0; i >= 0; i -= 3) {\n        w = number[i] | (number[i - 1] << 8) | (number[i - 2] << 16);\n        this.words[j] |= (w << off) & 0x3ffffff;\n        this.words[j + 1] = (w >>> (26 - off)) & 0x3ffffff;\n        off += 24;\n        if (off >= 26) {\n          off -= 26;\n          j++;\n        }\n      }\n    } else if (endian === 'le') {\n      for (i = 0, j = 0; i < number.length; i += 3) {\n        w = number[i] | (number[i + 1] << 8) | (number[i + 2] << 16);\n        this.words[j] |= (w << off) & 0x3ffffff;\n        this.words[j + 1] = (w >>> (26 - off)) & 0x3ffffff;\n        off += 24;\n        if (off >= 26) {\n          off -= 26;\n          j++;\n        }\n      }\n    }\n    return this.strip();\n  };\n\n  function parseHex4Bits (string, index) {\n    var c = string.charCodeAt(index);\n    // 'A' - 'F'\n    if (c >= 65 && c <= 70) {\n      return c - 55;\n    // 'a' - 'f'\n    } else if (c >= 97 && c <= 102) {\n      return c - 87;\n    // '0' - '9'\n    } else {\n      return (c - 48) & 0xf;\n    }\n  }\n\n  function parseHexByte (string, lowerBound, index) {\n    var r = parseHex4Bits(string, index);\n    if (index - 1 >= lowerBound) {\n      r |= parseHex4Bits(string, index - 1) << 4;\n    }\n    return r;\n  }\n\n  BN.prototype._parseHex = function _parseHex (number, start, endian) {\n    // Create possibly bigger array to ensure that it fits the number\n    this.length = Math.ceil((number.length - start) / 6);\n    this.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      this.words[i] = 0;\n    }\n\n    // 24-bits chunks\n    var off = 0;\n    var j = 0;\n\n    var w;\n    if (endian === 'be') {\n      for (i = number.length - 1; i >= start; i -= 2) {\n        w = parseHexByte(number, start, i) << off;\n        this.words[j] |= w & 0x3ffffff;\n        if (off >= 18) {\n          off -= 18;\n          j += 1;\n          this.words[j] |= w >>> 26;\n        } else {\n          off += 8;\n        }\n      }\n    } else {\n      var parseLength = number.length - start;\n      for (i = parseLength % 2 === 0 ? start + 1 : start; i < number.length; i += 2) {\n        w = parseHexByte(number, start, i) << off;\n        this.words[j] |= w & 0x3ffffff;\n        if (off >= 18) {\n          off -= 18;\n          j += 1;\n          this.words[j] |= w >>> 26;\n        } else {\n          off += 8;\n        }\n      }\n    }\n\n    this.strip();\n  };\n\n  function parseBase (str, start, end, mul) {\n    var r = 0;\n    var len = Math.min(str.length, end);\n    for (var i = start; i < len; i++) {\n      var c = str.charCodeAt(i) - 48;\n\n      r *= mul;\n\n      // 'a'\n      if (c >= 49) {\n        r += c - 49 + 0xa;\n\n      // 'A'\n      } else if (c >= 17) {\n        r += c - 17 + 0xa;\n\n      // '0' - '9'\n      } else {\n        r += c;\n      }\n    }\n    return r;\n  }\n\n  BN.prototype._parseBase = function _parseBase (number, base, start) {\n    // Initialize as zero\n    this.words = [ 0 ];\n    this.length = 1;\n\n    // Find length of limb in base\n    for (var limbLen = 0, limbPow = 1; limbPow <= 0x3ffffff; limbPow *= base) {\n      limbLen++;\n    }\n    limbLen--;\n    limbPow = (limbPow / base) | 0;\n\n    var total = number.length - start;\n    var mod = total % limbLen;\n    var end = Math.min(total, total - mod) + start;\n\n    var word = 0;\n    for (var i = start; i < end; i += limbLen) {\n      word = parseBase(number, i, i + limbLen, base);\n\n      this.imuln(limbPow);\n      if (this.words[0] + word < 0x4000000) {\n        this.words[0] += word;\n      } else {\n        this._iaddn(word);\n      }\n    }\n\n    if (mod !== 0) {\n      var pow = 1;\n      word = parseBase(number, i, number.length, base);\n\n      for (i = 0; i < mod; i++) {\n        pow *= base;\n      }\n\n      this.imuln(pow);\n      if (this.words[0] + word < 0x4000000) {\n        this.words[0] += word;\n      } else {\n        this._iaddn(word);\n      }\n    }\n\n    this.strip();\n  };\n\n  BN.prototype.copy = function copy (dest) {\n    dest.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      dest.words[i] = this.words[i];\n    }\n    dest.length = this.length;\n    dest.negative = this.negative;\n    dest.red = this.red;\n  };\n\n  BN.prototype.clone = function clone () {\n    var r = new BN(null);\n    this.copy(r);\n    return r;\n  };\n\n  BN.prototype._expand = function _expand (size) {\n    while (this.length < size) {\n      this.words[this.length++] = 0;\n    }\n    return this;\n  };\n\n  // Remove leading `0` from `this`\n  BN.prototype.strip = function strip () {\n    while (this.length > 1 && this.words[this.length - 1] === 0) {\n      this.length--;\n    }\n    return this._normSign();\n  };\n\n  BN.prototype._normSign = function _normSign () {\n    // -0 = 0\n    if (this.length === 1 && this.words[0] === 0) {\n      this.negative = 0;\n    }\n    return this;\n  };\n\n  BN.prototype.inspect = function inspect () {\n    return (this.red ? '<BN-R: ' : '<BN: ') + this.toString(16) + '>';\n  };\n\n  /*\n\n  var zeros = [];\n  var groupSizes = [];\n  var groupBases = [];\n\n  var s = '';\n  var i = -1;\n  while (++i < BN.wordSize) {\n    zeros[i] = s;\n    s += '0';\n  }\n  groupSizes[0] = 0;\n  groupSizes[1] = 0;\n  groupBases[0] = 0;\n  groupBases[1] = 0;\n  var base = 2 - 1;\n  while (++base < 36 + 1) {\n    var groupSize = 0;\n    var groupBase = 1;\n    while (groupBase < (1 << BN.wordSize) / base) {\n      groupBase *= base;\n      groupSize += 1;\n    }\n    groupSizes[base] = groupSize;\n    groupBases[base] = groupBase;\n  }\n\n  */\n\n  var zeros = [\n    '',\n    '0',\n    '00',\n    '000',\n    '0000',\n    '00000',\n    '000000',\n    '0000000',\n    '00000000',\n    '000000000',\n    '0000000000',\n    '00000000000',\n    '000000000000',\n    '0000000000000',\n    '00000000000000',\n    '000000000000000',\n    '0000000000000000',\n    '00000000000000000',\n    '000000000000000000',\n    '0000000000000000000',\n    '00000000000000000000',\n    '000000000000000000000',\n    '0000000000000000000000',\n    '00000000000000000000000',\n    '000000000000000000000000',\n    '0000000000000000000000000'\n  ];\n\n  var groupSizes = [\n    0, 0,\n    25, 16, 12, 11, 10, 9, 8,\n    8, 7, 7, 7, 7, 6, 6,\n    6, 6, 6, 6, 6, 5, 5,\n    5, 5, 5, 5, 5, 5, 5,\n    5, 5, 5, 5, 5, 5, 5\n  ];\n\n  var groupBases = [\n    0, 0,\n    33554432, 43046721, 16777216, 48828125, 60466176, 40353607, 16777216,\n    43046721, 10000000, 19487171, 35831808, 62748517, 7529536, 11390625,\n    16777216, 24137569, 34012224, 47045881, 64000000, 4084101, 5153632,\n    6436343, 7962624, 9765625, 11881376, 14348907, 17210368, 20511149,\n    24300000, 28629151, 33554432, 39135393, 45435424, 52521875, 60466176\n  ];\n\n  BN.prototype.toString = function toString (base, padding) {\n    base = base || 10;\n    padding = padding | 0 || 1;\n\n    var out;\n    if (base === 16 || base === 'hex') {\n      out = '';\n      var off = 0;\n      var carry = 0;\n      for (var i = 0; i < this.length; i++) {\n        var w = this.words[i];\n        var word = (((w << off) | carry) & 0xffffff).toString(16);\n        carry = (w >>> (24 - off)) & 0xffffff;\n        if (carry !== 0 || i !== this.length - 1) {\n          out = zeros[6 - word.length] + word + out;\n        } else {\n          out = word + out;\n        }\n        off += 2;\n        if (off >= 26) {\n          off -= 26;\n          i--;\n        }\n      }\n      if (carry !== 0) {\n        out = carry.toString(16) + out;\n      }\n      while (out.length % padding !== 0) {\n        out = '0' + out;\n      }\n      if (this.negative !== 0) {\n        out = '-' + out;\n      }\n      return out;\n    }\n\n    if (base === (base | 0) && base >= 2 && base <= 36) {\n      // var groupSize = Math.floor(BN.wordSize * Math.LN2 / Math.log(base));\n      var groupSize = groupSizes[base];\n      // var groupBase = Math.pow(base, groupSize);\n      var groupBase = groupBases[base];\n      out = '';\n      var c = this.clone();\n      c.negative = 0;\n      while (!c.isZero()) {\n        var r = c.modn(groupBase).toString(base);\n        c = c.idivn(groupBase);\n\n        if (!c.isZero()) {\n          out = zeros[groupSize - r.length] + r + out;\n        } else {\n          out = r + out;\n        }\n      }\n      if (this.isZero()) {\n        out = '0' + out;\n      }\n      while (out.length % padding !== 0) {\n        out = '0' + out;\n      }\n      if (this.negative !== 0) {\n        out = '-' + out;\n      }\n      return out;\n    }\n\n    assert(false, 'Base should be between 2 and 36');\n  };\n\n  BN.prototype.toNumber = function toNumber () {\n    var ret = this.words[0];\n    if (this.length === 2) {\n      ret += this.words[1] * 0x4000000;\n    } else if (this.length === 3 && this.words[2] === 0x01) {\n      // NOTE: at this stage it is known that the top bit is set\n      ret += 0x10000000000000 + (this.words[1] * 0x4000000);\n    } else if (this.length > 2) {\n      assert(false, 'Number can only safely store up to 53 bits');\n    }\n    return (this.negative !== 0) ? -ret : ret;\n  };\n\n  BN.prototype.toJSON = function toJSON () {\n    return this.toString(16);\n  };\n\n  BN.prototype.toBuffer = function toBuffer (endian, length) {\n    assert(typeof Buffer !== 'undefined');\n    return this.toArrayLike(Buffer, endian, length);\n  };\n\n  BN.prototype.toArray = function toArray (endian, length) {\n    return this.toArrayLike(Array, endian, length);\n  };\n\n  BN.prototype.toArrayLike = function toArrayLike (ArrayType, endian, length) {\n    var byteLength = this.byteLength();\n    var reqLength = length || Math.max(1, byteLength);\n    assert(byteLength <= reqLength, 'byte array longer than desired length');\n    assert(reqLength > 0, 'Requested array length <= 0');\n\n    this.strip();\n    var littleEndian = endian === 'le';\n    var res = new ArrayType(reqLength);\n\n    var b, i;\n    var q = this.clone();\n    if (!littleEndian) {\n      // Assume big-endian\n      for (i = 0; i < reqLength - byteLength; i++) {\n        res[i] = 0;\n      }\n\n      for (i = 0; !q.isZero(); i++) {\n        b = q.andln(0xff);\n        q.iushrn(8);\n\n        res[reqLength - i - 1] = b;\n      }\n    } else {\n      for (i = 0; !q.isZero(); i++) {\n        b = q.andln(0xff);\n        q.iushrn(8);\n\n        res[i] = b;\n      }\n\n      for (; i < reqLength; i++) {\n        res[i] = 0;\n      }\n    }\n\n    return res;\n  };\n\n  if (Math.clz32) {\n    BN.prototype._countBits = function _countBits (w) {\n      return 32 - Math.clz32(w);\n    };\n  } else {\n    BN.prototype._countBits = function _countBits (w) {\n      var t = w;\n      var r = 0;\n      if (t >= 0x1000) {\n        r += 13;\n        t >>>= 13;\n      }\n      if (t >= 0x40) {\n        r += 7;\n        t >>>= 7;\n      }\n      if (t >= 0x8) {\n        r += 4;\n        t >>>= 4;\n      }\n      if (t >= 0x02) {\n        r += 2;\n        t >>>= 2;\n      }\n      return r + t;\n    };\n  }\n\n  BN.prototype._zeroBits = function _zeroBits (w) {\n    // Short-cut\n    if (w === 0) return 26;\n\n    var t = w;\n    var r = 0;\n    if ((t & 0x1fff) === 0) {\n      r += 13;\n      t >>>= 13;\n    }\n    if ((t & 0x7f) === 0) {\n      r += 7;\n      t >>>= 7;\n    }\n    if ((t & 0xf) === 0) {\n      r += 4;\n      t >>>= 4;\n    }\n    if ((t & 0x3) === 0) {\n      r += 2;\n      t >>>= 2;\n    }\n    if ((t & 0x1) === 0) {\n      r++;\n    }\n    return r;\n  };\n\n  // Return number of used bits in a BN\n  BN.prototype.bitLength = function bitLength () {\n    var w = this.words[this.length - 1];\n    var hi = this._countBits(w);\n    return (this.length - 1) * 26 + hi;\n  };\n\n  function toBitArray (num) {\n    var w = new Array(num.bitLength());\n\n    for (var bit = 0; bit < w.length; bit++) {\n      var off = (bit / 26) | 0;\n      var wbit = bit % 26;\n\n      w[bit] = (num.words[off] & (1 << wbit)) >>> wbit;\n    }\n\n    return w;\n  }\n\n  // Number of trailing zero bits\n  BN.prototype.zeroBits = function zeroBits () {\n    if (this.isZero()) return 0;\n\n    var r = 0;\n    for (var i = 0; i < this.length; i++) {\n      var b = this._zeroBits(this.words[i]);\n      r += b;\n      if (b !== 26) break;\n    }\n    return r;\n  };\n\n  BN.prototype.byteLength = function byteLength () {\n    return Math.ceil(this.bitLength() / 8);\n  };\n\n  BN.prototype.toTwos = function toTwos (width) {\n    if (this.negative !== 0) {\n      return this.abs().inotn(width).iaddn(1);\n    }\n    return this.clone();\n  };\n\n  BN.prototype.fromTwos = function fromTwos (width) {\n    if (this.testn(width - 1)) {\n      return this.notn(width).iaddn(1).ineg();\n    }\n    return this.clone();\n  };\n\n  BN.prototype.isNeg = function isNeg () {\n    return this.negative !== 0;\n  };\n\n  // Return negative clone of `this`\n  BN.prototype.neg = function neg () {\n    return this.clone().ineg();\n  };\n\n  BN.prototype.ineg = function ineg () {\n    if (!this.isZero()) {\n      this.negative ^= 1;\n    }\n\n    return this;\n  };\n\n  // Or `num` with `this` in-place\n  BN.prototype.iuor = function iuor (num) {\n    while (this.length < num.length) {\n      this.words[this.length++] = 0;\n    }\n\n    for (var i = 0; i < num.length; i++) {\n      this.words[i] = this.words[i] | num.words[i];\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ior = function ior (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuor(num);\n  };\n\n  // Or `num` with `this`\n  BN.prototype.or = function or (num) {\n    if (this.length > num.length) return this.clone().ior(num);\n    return num.clone().ior(this);\n  };\n\n  BN.prototype.uor = function uor (num) {\n    if (this.length > num.length) return this.clone().iuor(num);\n    return num.clone().iuor(this);\n  };\n\n  // And `num` with `this` in-place\n  BN.prototype.iuand = function iuand (num) {\n    // b = min-length(num, this)\n    var b;\n    if (this.length > num.length) {\n      b = num;\n    } else {\n      b = this;\n    }\n\n    for (var i = 0; i < b.length; i++) {\n      this.words[i] = this.words[i] & num.words[i];\n    }\n\n    this.length = b.length;\n\n    return this.strip();\n  };\n\n  BN.prototype.iand = function iand (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuand(num);\n  };\n\n  // And `num` with `this`\n  BN.prototype.and = function and (num) {\n    if (this.length > num.length) return this.clone().iand(num);\n    return num.clone().iand(this);\n  };\n\n  BN.prototype.uand = function uand (num) {\n    if (this.length > num.length) return this.clone().iuand(num);\n    return num.clone().iuand(this);\n  };\n\n  // Xor `num` with `this` in-place\n  BN.prototype.iuxor = function iuxor (num) {\n    // a.length > b.length\n    var a;\n    var b;\n    if (this.length > num.length) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    for (var i = 0; i < b.length; i++) {\n      this.words[i] = a.words[i] ^ b.words[i];\n    }\n\n    if (this !== a) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    this.length = a.length;\n\n    return this.strip();\n  };\n\n  BN.prototype.ixor = function ixor (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuxor(num);\n  };\n\n  // Xor `num` with `this`\n  BN.prototype.xor = function xor (num) {\n    if (this.length > num.length) return this.clone().ixor(num);\n    return num.clone().ixor(this);\n  };\n\n  BN.prototype.uxor = function uxor (num) {\n    if (this.length > num.length) return this.clone().iuxor(num);\n    return num.clone().iuxor(this);\n  };\n\n  // Not ``this`` with ``width`` bitwidth\n  BN.prototype.inotn = function inotn (width) {\n    assert(typeof width === 'number' && width >= 0);\n\n    var bytesNeeded = Math.ceil(width / 26) | 0;\n    var bitsLeft = width % 26;\n\n    // Extend the buffer with leading zeroes\n    this._expand(bytesNeeded);\n\n    if (bitsLeft > 0) {\n      bytesNeeded--;\n    }\n\n    // Handle complete words\n    for (var i = 0; i < bytesNeeded; i++) {\n      this.words[i] = ~this.words[i] & 0x3ffffff;\n    }\n\n    // Handle the residue\n    if (bitsLeft > 0) {\n      this.words[i] = ~this.words[i] & (0x3ffffff >> (26 - bitsLeft));\n    }\n\n    // And remove leading zeroes\n    return this.strip();\n  };\n\n  BN.prototype.notn = function notn (width) {\n    return this.clone().inotn(width);\n  };\n\n  // Set `bit` of `this`\n  BN.prototype.setn = function setn (bit, val) {\n    assert(typeof bit === 'number' && bit >= 0);\n\n    var off = (bit / 26) | 0;\n    var wbit = bit % 26;\n\n    this._expand(off + 1);\n\n    if (val) {\n      this.words[off] = this.words[off] | (1 << wbit);\n    } else {\n      this.words[off] = this.words[off] & ~(1 << wbit);\n    }\n\n    return this.strip();\n  };\n\n  // Add `num` to `this` in-place\n  BN.prototype.iadd = function iadd (num) {\n    var r;\n\n    // negative + positive\n    if (this.negative !== 0 && num.negative === 0) {\n      this.negative = 0;\n      r = this.isub(num);\n      this.negative ^= 1;\n      return this._normSign();\n\n    // positive + negative\n    } else if (this.negative === 0 && num.negative !== 0) {\n      num.negative = 0;\n      r = this.isub(num);\n      num.negative = 1;\n      return r._normSign();\n    }\n\n    // a.length > b.length\n    var a, b;\n    if (this.length > num.length) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    var carry = 0;\n    for (var i = 0; i < b.length; i++) {\n      r = (a.words[i] | 0) + (b.words[i] | 0) + carry;\n      this.words[i] = r & 0x3ffffff;\n      carry = r >>> 26;\n    }\n    for (; carry !== 0 && i < a.length; i++) {\n      r = (a.words[i] | 0) + carry;\n      this.words[i] = r & 0x3ffffff;\n      carry = r >>> 26;\n    }\n\n    this.length = a.length;\n    if (carry !== 0) {\n      this.words[this.length] = carry;\n      this.length++;\n    // Copy the rest of the words\n    } else if (a !== this) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    return this;\n  };\n\n  // Add `num` to `this`\n  BN.prototype.add = function add (num) {\n    var res;\n    if (num.negative !== 0 && this.negative === 0) {\n      num.negative = 0;\n      res = this.sub(num);\n      num.negative ^= 1;\n      return res;\n    } else if (num.negative === 0 && this.negative !== 0) {\n      this.negative = 0;\n      res = num.sub(this);\n      this.negative = 1;\n      return res;\n    }\n\n    if (this.length > num.length) return this.clone().iadd(num);\n\n    return num.clone().iadd(this);\n  };\n\n  // Subtract `num` from `this` in-place\n  BN.prototype.isub = function isub (num) {\n    // this - (-num) = this + num\n    if (num.negative !== 0) {\n      num.negative = 0;\n      var r = this.iadd(num);\n      num.negative = 1;\n      return r._normSign();\n\n    // -this - num = -(this + num)\n    } else if (this.negative !== 0) {\n      this.negative = 0;\n      this.iadd(num);\n      this.negative = 1;\n      return this._normSign();\n    }\n\n    // At this point both numbers are positive\n    var cmp = this.cmp(num);\n\n    // Optimization - zeroify\n    if (cmp === 0) {\n      this.negative = 0;\n      this.length = 1;\n      this.words[0] = 0;\n      return this;\n    }\n\n    // a > b\n    var a, b;\n    if (cmp > 0) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    var carry = 0;\n    for (var i = 0; i < b.length; i++) {\n      r = (a.words[i] | 0) - (b.words[i] | 0) + carry;\n      carry = r >> 26;\n      this.words[i] = r & 0x3ffffff;\n    }\n    for (; carry !== 0 && i < a.length; i++) {\n      r = (a.words[i] | 0) + carry;\n      carry = r >> 26;\n      this.words[i] = r & 0x3ffffff;\n    }\n\n    // Copy rest of the words\n    if (carry === 0 && i < a.length && a !== this) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    this.length = Math.max(this.length, i);\n\n    if (a !== this) {\n      this.negative = 1;\n    }\n\n    return this.strip();\n  };\n\n  // Subtract `num` from `this`\n  BN.prototype.sub = function sub (num) {\n    return this.clone().isub(num);\n  };\n\n  function smallMulTo (self, num, out) {\n    out.negative = num.negative ^ self.negative;\n    var len = (self.length + num.length) | 0;\n    out.length = len;\n    len = (len - 1) | 0;\n\n    // Peel one iteration (compiler can't do it, because of code complexity)\n    var a = self.words[0] | 0;\n    var b = num.words[0] | 0;\n    var r = a * b;\n\n    var lo = r & 0x3ffffff;\n    var carry = (r / 0x4000000) | 0;\n    out.words[0] = lo;\n\n    for (var k = 1; k < len; k++) {\n      // Sum all words with the same `i + j = k` and accumulate `ncarry`,\n      // note that ncarry could be >= 0x3ffffff\n      var ncarry = carry >>> 26;\n      var rword = carry & 0x3ffffff;\n      var maxJ = Math.min(k, num.length - 1);\n      for (var j = Math.max(0, k - self.length + 1); j <= maxJ; j++) {\n        var i = (k - j) | 0;\n        a = self.words[i] | 0;\n        b = num.words[j] | 0;\n        r = a * b + rword;\n        ncarry += (r / 0x4000000) | 0;\n        rword = r & 0x3ffffff;\n      }\n      out.words[k] = rword | 0;\n      carry = ncarry | 0;\n    }\n    if (carry !== 0) {\n      out.words[k] = carry | 0;\n    } else {\n      out.length--;\n    }\n\n    return out.strip();\n  }\n\n  // TODO(indutny): it may be reasonable to omit it for users who don't need\n  // to work with 256-bit numbers, otherwise it gives 20% improvement for 256-bit\n  // multiplication (like elliptic secp256k1).\n  var comb10MulTo = function comb10MulTo (self, num, out) {\n    var a = self.words;\n    var b = num.words;\n    var o = out.words;\n    var c = 0;\n    var lo;\n    var mid;\n    var hi;\n    var a0 = a[0] | 0;\n    var al0 = a0 & 0x1fff;\n    var ah0 = a0 >>> 13;\n    var a1 = a[1] | 0;\n    var al1 = a1 & 0x1fff;\n    var ah1 = a1 >>> 13;\n    var a2 = a[2] | 0;\n    var al2 = a2 & 0x1fff;\n    var ah2 = a2 >>> 13;\n    var a3 = a[3] | 0;\n    var al3 = a3 & 0x1fff;\n    var ah3 = a3 >>> 13;\n    var a4 = a[4] | 0;\n    var al4 = a4 & 0x1fff;\n    var ah4 = a4 >>> 13;\n    var a5 = a[5] | 0;\n    var al5 = a5 & 0x1fff;\n    var ah5 = a5 >>> 13;\n    var a6 = a[6] | 0;\n    var al6 = a6 & 0x1fff;\n    var ah6 = a6 >>> 13;\n    var a7 = a[7] | 0;\n    var al7 = a7 & 0x1fff;\n    var ah7 = a7 >>> 13;\n    var a8 = a[8] | 0;\n    var al8 = a8 & 0x1fff;\n    var ah8 = a8 >>> 13;\n    var a9 = a[9] | 0;\n    var al9 = a9 & 0x1fff;\n    var ah9 = a9 >>> 13;\n    var b0 = b[0] | 0;\n    var bl0 = b0 & 0x1fff;\n    var bh0 = b0 >>> 13;\n    var b1 = b[1] | 0;\n    var bl1 = b1 & 0x1fff;\n    var bh1 = b1 >>> 13;\n    var b2 = b[2] | 0;\n    var bl2 = b2 & 0x1fff;\n    var bh2 = b2 >>> 13;\n    var b3 = b[3] | 0;\n    var bl3 = b3 & 0x1fff;\n    var bh3 = b3 >>> 13;\n    var b4 = b[4] | 0;\n    var bl4 = b4 & 0x1fff;\n    var bh4 = b4 >>> 13;\n    var b5 = b[5] | 0;\n    var bl5 = b5 & 0x1fff;\n    var bh5 = b5 >>> 13;\n    var b6 = b[6] | 0;\n    var bl6 = b6 & 0x1fff;\n    var bh6 = b6 >>> 13;\n    var b7 = b[7] | 0;\n    var bl7 = b7 & 0x1fff;\n    var bh7 = b7 >>> 13;\n    var b8 = b[8] | 0;\n    var bl8 = b8 & 0x1fff;\n    var bh8 = b8 >>> 13;\n    var b9 = b[9] | 0;\n    var bl9 = b9 & 0x1fff;\n    var bh9 = b9 >>> 13;\n\n    out.negative = self.negative ^ num.negative;\n    out.length = 19;\n    /* k = 0 */\n    lo = Math.imul(al0, bl0);\n    mid = Math.imul(al0, bh0);\n    mid = (mid + Math.imul(ah0, bl0)) | 0;\n    hi = Math.imul(ah0, bh0);\n    var w0 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w0 >>> 26)) | 0;\n    w0 &= 0x3ffffff;\n    /* k = 1 */\n    lo = Math.imul(al1, bl0);\n    mid = Math.imul(al1, bh0);\n    mid = (mid + Math.imul(ah1, bl0)) | 0;\n    hi = Math.imul(ah1, bh0);\n    lo = (lo + Math.imul(al0, bl1)) | 0;\n    mid = (mid + Math.imul(al0, bh1)) | 0;\n    mid = (mid + Math.imul(ah0, bl1)) | 0;\n    hi = (hi + Math.imul(ah0, bh1)) | 0;\n    var w1 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w1 >>> 26)) | 0;\n    w1 &= 0x3ffffff;\n    /* k = 2 */\n    lo = Math.imul(al2, bl0);\n    mid = Math.imul(al2, bh0);\n    mid = (mid + Math.imul(ah2, bl0)) | 0;\n    hi = Math.imul(ah2, bh0);\n    lo = (lo + Math.imul(al1, bl1)) | 0;\n    mid = (mid + Math.imul(al1, bh1)) | 0;\n    mid = (mid + Math.imul(ah1, bl1)) | 0;\n    hi = (hi + Math.imul(ah1, bh1)) | 0;\n    lo = (lo + Math.imul(al0, bl2)) | 0;\n    mid = (mid + Math.imul(al0, bh2)) | 0;\n    mid = (mid + Math.imul(ah0, bl2)) | 0;\n    hi = (hi + Math.imul(ah0, bh2)) | 0;\n    var w2 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w2 >>> 26)) | 0;\n    w2 &= 0x3ffffff;\n    /* k = 3 */\n    lo = Math.imul(al3, bl0);\n    mid = Math.imul(al3, bh0);\n    mid = (mid + Math.imul(ah3, bl0)) | 0;\n    hi = Math.imul(ah3, bh0);\n    lo = (lo + Math.imul(al2, bl1)) | 0;\n    mid = (mid + Math.imul(al2, bh1)) | 0;\n    mid = (mid + Math.imul(ah2, bl1)) | 0;\n    hi = (hi + Math.imul(ah2, bh1)) | 0;\n    lo = (lo + Math.imul(al1, bl2)) | 0;\n    mid = (mid + Math.imul(al1, bh2)) | 0;\n    mid = (mid + Math.imul(ah1, bl2)) | 0;\n    hi = (hi + Math.imul(ah1, bh2)) | 0;\n    lo = (lo + Math.imul(al0, bl3)) | 0;\n    mid = (mid + Math.imul(al0, bh3)) | 0;\n    mid = (mid + Math.imul(ah0, bl3)) | 0;\n    hi = (hi + Math.imul(ah0, bh3)) | 0;\n    var w3 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w3 >>> 26)) | 0;\n    w3 &= 0x3ffffff;\n    /* k = 4 */\n    lo = Math.imul(al4, bl0);\n    mid = Math.imul(al4, bh0);\n    mid = (mid + Math.imul(ah4, bl0)) | 0;\n    hi = Math.imul(ah4, bh0);\n    lo = (lo + Math.imul(al3, bl1)) | 0;\n    mid = (mid + Math.imul(al3, bh1)) | 0;\n    mid = (mid + Math.imul(ah3, bl1)) | 0;\n    hi = (hi + Math.imul(ah3, bh1)) | 0;\n    lo = (lo + Math.imul(al2, bl2)) | 0;\n    mid = (mid + Math.imul(al2, bh2)) | 0;\n    mid = (mid + Math.imul(ah2, bl2)) | 0;\n    hi = (hi + Math.imul(ah2, bh2)) | 0;\n    lo = (lo + Math.imul(al1, bl3)) | 0;\n    mid = (mid + Math.imul(al1, bh3)) | 0;\n    mid = (mid + Math.imul(ah1, bl3)) | 0;\n    hi = (hi + Math.imul(ah1, bh3)) | 0;\n    lo = (lo + Math.imul(al0, bl4)) | 0;\n    mid = (mid + Math.imul(al0, bh4)) | 0;\n    mid = (mid + Math.imul(ah0, bl4)) | 0;\n    hi = (hi + Math.imul(ah0, bh4)) | 0;\n    var w4 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w4 >>> 26)) | 0;\n    w4 &= 0x3ffffff;\n    /* k = 5 */\n    lo = Math.imul(al5, bl0);\n    mid = Math.imul(al5, bh0);\n    mid = (mid + Math.imul(ah5, bl0)) | 0;\n    hi = Math.imul(ah5, bh0);\n    lo = (lo + Math.imul(al4, bl1)) | 0;\n    mid = (mid + Math.imul(al4, bh1)) | 0;\n    mid = (mid + Math.imul(ah4, bl1)) | 0;\n    hi = (hi + Math.imul(ah4, bh1)) | 0;\n    lo = (lo + Math.imul(al3, bl2)) | 0;\n    mid = (mid + Math.imul(al3, bh2)) | 0;\n    mid = (mid + Math.imul(ah3, bl2)) | 0;\n    hi = (hi + Math.imul(ah3, bh2)) | 0;\n    lo = (lo + Math.imul(al2, bl3)) | 0;\n    mid = (mid + Math.imul(al2, bh3)) | 0;\n    mid = (mid + Math.imul(ah2, bl3)) | 0;\n    hi = (hi + Math.imul(ah2, bh3)) | 0;\n    lo = (lo + Math.imul(al1, bl4)) | 0;\n    mid = (mid + Math.imul(al1, bh4)) | 0;\n    mid = (mid + Math.imul(ah1, bl4)) | 0;\n    hi = (hi + Math.imul(ah1, bh4)) | 0;\n    lo = (lo + Math.imul(al0, bl5)) | 0;\n    mid = (mid + Math.imul(al0, bh5)) | 0;\n    mid = (mid + Math.imul(ah0, bl5)) | 0;\n    hi = (hi + Math.imul(ah0, bh5)) | 0;\n    var w5 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w5 >>> 26)) | 0;\n    w5 &= 0x3ffffff;\n    /* k = 6 */\n    lo = Math.imul(al6, bl0);\n    mid = Math.imul(al6, bh0);\n    mid = (mid + Math.imul(ah6, bl0)) | 0;\n    hi = Math.imul(ah6, bh0);\n    lo = (lo + Math.imul(al5, bl1)) | 0;\n    mid = (mid + Math.imul(al5, bh1)) | 0;\n    mid = (mid + Math.imul(ah5, bl1)) | 0;\n    hi = (hi + Math.imul(ah5, bh1)) | 0;\n    lo = (lo + Math.imul(al4, bl2)) | 0;\n    mid = (mid + Math.imul(al4, bh2)) | 0;\n    mid = (mid + Math.imul(ah4, bl2)) | 0;\n    hi = (hi + Math.imul(ah4, bh2)) | 0;\n    lo = (lo + Math.imul(al3, bl3)) | 0;\n    mid = (mid + Math.imul(al3, bh3)) | 0;\n    mid = (mid + Math.imul(ah3, bl3)) | 0;\n    hi = (hi + Math.imul(ah3, bh3)) | 0;\n    lo = (lo + Math.imul(al2, bl4)) | 0;\n    mid = (mid + Math.imul(al2, bh4)) | 0;\n    mid = (mid + Math.imul(ah2, bl4)) | 0;\n    hi = (hi + Math.imul(ah2, bh4)) | 0;\n    lo = (lo + Math.imul(al1, bl5)) | 0;\n    mid = (mid + Math.imul(al1, bh5)) | 0;\n    mid = (mid + Math.imul(ah1, bl5)) | 0;\n    hi = (hi + Math.imul(ah1, bh5)) | 0;\n    lo = (lo + Math.imul(al0, bl6)) | 0;\n    mid = (mid + Math.imul(al0, bh6)) | 0;\n    mid = (mid + Math.imul(ah0, bl6)) | 0;\n    hi = (hi + Math.imul(ah0, bh6)) | 0;\n    var w6 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w6 >>> 26)) | 0;\n    w6 &= 0x3ffffff;\n    /* k = 7 */\n    lo = Math.imul(al7, bl0);\n    mid = Math.imul(al7, bh0);\n    mid = (mid + Math.imul(ah7, bl0)) | 0;\n    hi = Math.imul(ah7, bh0);\n    lo = (lo + Math.imul(al6, bl1)) | 0;\n    mid = (mid + Math.imul(al6, bh1)) | 0;\n    mid = (mid + Math.imul(ah6, bl1)) | 0;\n    hi = (hi + Math.imul(ah6, bh1)) | 0;\n    lo = (lo + Math.imul(al5, bl2)) | 0;\n    mid = (mid + Math.imul(al5, bh2)) | 0;\n    mid = (mid + Math.imul(ah5, bl2)) | 0;\n    hi = (hi + Math.imul(ah5, bh2)) | 0;\n    lo = (lo + Math.imul(al4, bl3)) | 0;\n    mid = (mid + Math.imul(al4, bh3)) | 0;\n    mid = (mid + Math.imul(ah4, bl3)) | 0;\n    hi = (hi + Math.imul(ah4, bh3)) | 0;\n    lo = (lo + Math.imul(al3, bl4)) | 0;\n    mid = (mid + Math.imul(al3, bh4)) | 0;\n    mid = (mid + Math.imul(ah3, bl4)) | 0;\n    hi = (hi + Math.imul(ah3, bh4)) | 0;\n    lo = (lo + Math.imul(al2, bl5)) | 0;\n    mid = (mid + Math.imul(al2, bh5)) | 0;\n    mid = (mid + Math.imul(ah2, bl5)) | 0;\n    hi = (hi + Math.imul(ah2, bh5)) | 0;\n    lo = (lo + Math.imul(al1, bl6)) | 0;\n    mid = (mid + Math.imul(al1, bh6)) | 0;\n    mid = (mid + Math.imul(ah1, bl6)) | 0;\n    hi = (hi + Math.imul(ah1, bh6)) | 0;\n    lo = (lo + Math.imul(al0, bl7)) | 0;\n    mid = (mid + Math.imul(al0, bh7)) | 0;\n    mid = (mid + Math.imul(ah0, bl7)) | 0;\n    hi = (hi + Math.imul(ah0, bh7)) | 0;\n    var w7 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w7 >>> 26)) | 0;\n    w7 &= 0x3ffffff;\n    /* k = 8 */\n    lo = Math.imul(al8, bl0);\n    mid = Math.imul(al8, bh0);\n    mid = (mid + Math.imul(ah8, bl0)) | 0;\n    hi = Math.imul(ah8, bh0);\n    lo = (lo + Math.imul(al7, bl1)) | 0;\n    mid = (mid + Math.imul(al7, bh1)) | 0;\n    mid = (mid + Math.imul(ah7, bl1)) | 0;\n    hi = (hi + Math.imul(ah7, bh1)) | 0;\n    lo = (lo + Math.imul(al6, bl2)) | 0;\n    mid = (mid + Math.imul(al6, bh2)) | 0;\n    mid = (mid + Math.imul(ah6, bl2)) | 0;\n    hi = (hi + Math.imul(ah6, bh2)) | 0;\n    lo = (lo + Math.imul(al5, bl3)) | 0;\n    mid = (mid + Math.imul(al5, bh3)) | 0;\n    mid = (mid + Math.imul(ah5, bl3)) | 0;\n    hi = (hi + Math.imul(ah5, bh3)) | 0;\n    lo = (lo + Math.imul(al4, bl4)) | 0;\n    mid = (mid + Math.imul(al4, bh4)) | 0;\n    mid = (mid + Math.imul(ah4, bl4)) | 0;\n    hi = (hi + Math.imul(ah4, bh4)) | 0;\n    lo = (lo + Math.imul(al3, bl5)) | 0;\n    mid = (mid + Math.imul(al3, bh5)) | 0;\n    mid = (mid + Math.imul(ah3, bl5)) | 0;\n    hi = (hi + Math.imul(ah3, bh5)) | 0;\n    lo = (lo + Math.imul(al2, bl6)) | 0;\n    mid = (mid + Math.imul(al2, bh6)) | 0;\n    mid = (mid + Math.imul(ah2, bl6)) | 0;\n    hi = (hi + Math.imul(ah2, bh6)) | 0;\n    lo = (lo + Math.imul(al1, bl7)) | 0;\n    mid = (mid + Math.imul(al1, bh7)) | 0;\n    mid = (mid + Math.imul(ah1, bl7)) | 0;\n    hi = (hi + Math.imul(ah1, bh7)) | 0;\n    lo = (lo + Math.imul(al0, bl8)) | 0;\n    mid = (mid + Math.imul(al0, bh8)) | 0;\n    mid = (mid + Math.imul(ah0, bl8)) | 0;\n    hi = (hi + Math.imul(ah0, bh8)) | 0;\n    var w8 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w8 >>> 26)) | 0;\n    w8 &= 0x3ffffff;\n    /* k = 9 */\n    lo = Math.imul(al9, bl0);\n    mid = Math.imul(al9, bh0);\n    mid = (mid + Math.imul(ah9, bl0)) | 0;\n    hi = Math.imul(ah9, bh0);\n    lo = (lo + Math.imul(al8, bl1)) | 0;\n    mid = (mid + Math.imul(al8, bh1)) | 0;\n    mid = (mid + Math.imul(ah8, bl1)) | 0;\n    hi = (hi + Math.imul(ah8, bh1)) | 0;\n    lo = (lo + Math.imul(al7, bl2)) | 0;\n    mid = (mid + Math.imul(al7, bh2)) | 0;\n    mid = (mid + Math.imul(ah7, bl2)) | 0;\n    hi = (hi + Math.imul(ah7, bh2)) | 0;\n    lo = (lo + Math.imul(al6, bl3)) | 0;\n    mid = (mid + Math.imul(al6, bh3)) | 0;\n    mid = (mid + Math.imul(ah6, bl3)) | 0;\n    hi = (hi + Math.imul(ah6, bh3)) | 0;\n    lo = (lo + Math.imul(al5, bl4)) | 0;\n    mid = (mid + Math.imul(al5, bh4)) | 0;\n    mid = (mid + Math.imul(ah5, bl4)) | 0;\n    hi = (hi + Math.imul(ah5, bh4)) | 0;\n    lo = (lo + Math.imul(al4, bl5)) | 0;\n    mid = (mid + Math.imul(al4, bh5)) | 0;\n    mid = (mid + Math.imul(ah4, bl5)) | 0;\n    hi = (hi + Math.imul(ah4, bh5)) | 0;\n    lo = (lo + Math.imul(al3, bl6)) | 0;\n    mid = (mid + Math.imul(al3, bh6)) | 0;\n    mid = (mid + Math.imul(ah3, bl6)) | 0;\n    hi = (hi + Math.imul(ah3, bh6)) | 0;\n    lo = (lo + Math.imul(al2, bl7)) | 0;\n    mid = (mid + Math.imul(al2, bh7)) | 0;\n    mid = (mid + Math.imul(ah2, bl7)) | 0;\n    hi = (hi + Math.imul(ah2, bh7)) | 0;\n    lo = (lo + Math.imul(al1, bl8)) | 0;\n    mid = (mid + Math.imul(al1, bh8)) | 0;\n    mid = (mid + Math.imul(ah1, bl8)) | 0;\n    hi = (hi + Math.imul(ah1, bh8)) | 0;\n    lo = (lo + Math.imul(al0, bl9)) | 0;\n    mid = (mid + Math.imul(al0, bh9)) | 0;\n    mid = (mid + Math.imul(ah0, bl9)) | 0;\n    hi = (hi + Math.imul(ah0, bh9)) | 0;\n    var w9 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w9 >>> 26)) | 0;\n    w9 &= 0x3ffffff;\n    /* k = 10 */\n    lo = Math.imul(al9, bl1);\n    mid = Math.imul(al9, bh1);\n    mid = (mid + Math.imul(ah9, bl1)) | 0;\n    hi = Math.imul(ah9, bh1);\n    lo = (lo + Math.imul(al8, bl2)) | 0;\n    mid = (mid + Math.imul(al8, bh2)) | 0;\n    mid = (mid + Math.imul(ah8, bl2)) | 0;\n    hi = (hi + Math.imul(ah8, bh2)) | 0;\n    lo = (lo + Math.imul(al7, bl3)) | 0;\n    mid = (mid + Math.imul(al7, bh3)) | 0;\n    mid = (mid + Math.imul(ah7, bl3)) | 0;\n    hi = (hi + Math.imul(ah7, bh3)) | 0;\n    lo = (lo + Math.imul(al6, bl4)) | 0;\n    mid = (mid + Math.imul(al6, bh4)) | 0;\n    mid = (mid + Math.imul(ah6, bl4)) | 0;\n    hi = (hi + Math.imul(ah6, bh4)) | 0;\n    lo = (lo + Math.imul(al5, bl5)) | 0;\n    mid = (mid + Math.imul(al5, bh5)) | 0;\n    mid = (mid + Math.imul(ah5, bl5)) | 0;\n    hi = (hi + Math.imul(ah5, bh5)) | 0;\n    lo = (lo + Math.imul(al4, bl6)) | 0;\n    mid = (mid + Math.imul(al4, bh6)) | 0;\n    mid = (mid + Math.imul(ah4, bl6)) | 0;\n    hi = (hi + Math.imul(ah4, bh6)) | 0;\n    lo = (lo + Math.imul(al3, bl7)) | 0;\n    mid = (mid + Math.imul(al3, bh7)) | 0;\n    mid = (mid + Math.imul(ah3, bl7)) | 0;\n    hi = (hi + Math.imul(ah3, bh7)) | 0;\n    lo = (lo + Math.imul(al2, bl8)) | 0;\n    mid = (mid + Math.imul(al2, bh8)) | 0;\n    mid = (mid + Math.imul(ah2, bl8)) | 0;\n    hi = (hi + Math.imul(ah2, bh8)) | 0;\n    lo = (lo + Math.imul(al1, bl9)) | 0;\n    mid = (mid + Math.imul(al1, bh9)) | 0;\n    mid = (mid + Math.imul(ah1, bl9)) | 0;\n    hi = (hi + Math.imul(ah1, bh9)) | 0;\n    var w10 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w10 >>> 26)) | 0;\n    w10 &= 0x3ffffff;\n    /* k = 11 */\n    lo = Math.imul(al9, bl2);\n    mid = Math.imul(al9, bh2);\n    mid = (mid + Math.imul(ah9, bl2)) | 0;\n    hi = Math.imul(ah9, bh2);\n    lo = (lo + Math.imul(al8, bl3)) | 0;\n    mid = (mid + Math.imul(al8, bh3)) | 0;\n    mid = (mid + Math.imul(ah8, bl3)) | 0;\n    hi = (hi + Math.imul(ah8, bh3)) | 0;\n    lo = (lo + Math.imul(al7, bl4)) | 0;\n    mid = (mid + Math.imul(al7, bh4)) | 0;\n    mid = (mid + Math.imul(ah7, bl4)) | 0;\n    hi = (hi + Math.imul(ah7, bh4)) | 0;\n    lo = (lo + Math.imul(al6, bl5)) | 0;\n    mid = (mid + Math.imul(al6, bh5)) | 0;\n    mid = (mid + Math.imul(ah6, bl5)) | 0;\n    hi = (hi + Math.imul(ah6, bh5)) | 0;\n    lo = (lo + Math.imul(al5, bl6)) | 0;\n    mid = (mid + Math.imul(al5, bh6)) | 0;\n    mid = (mid + Math.imul(ah5, bl6)) | 0;\n    hi = (hi + Math.imul(ah5, bh6)) | 0;\n    lo = (lo + Math.imul(al4, bl7)) | 0;\n    mid = (mid + Math.imul(al4, bh7)) | 0;\n    mid = (mid + Math.imul(ah4, bl7)) | 0;\n    hi = (hi + Math.imul(ah4, bh7)) | 0;\n    lo = (lo + Math.imul(al3, bl8)) | 0;\n    mid = (mid + Math.imul(al3, bh8)) | 0;\n    mid = (mid + Math.imul(ah3, bl8)) | 0;\n    hi = (hi + Math.imul(ah3, bh8)) | 0;\n    lo = (lo + Math.imul(al2, bl9)) | 0;\n    mid = (mid + Math.imul(al2, bh9)) | 0;\n    mid = (mid + Math.imul(ah2, bl9)) | 0;\n    hi = (hi + Math.imul(ah2, bh9)) | 0;\n    var w11 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w11 >>> 26)) | 0;\n    w11 &= 0x3ffffff;\n    /* k = 12 */\n    lo = Math.imul(al9, bl3);\n    mid = Math.imul(al9, bh3);\n    mid = (mid + Math.imul(ah9, bl3)) | 0;\n    hi = Math.imul(ah9, bh3);\n    lo = (lo + Math.imul(al8, bl4)) | 0;\n    mid = (mid + Math.imul(al8, bh4)) | 0;\n    mid = (mid + Math.imul(ah8, bl4)) | 0;\n    hi = (hi + Math.imul(ah8, bh4)) | 0;\n    lo = (lo + Math.imul(al7, bl5)) | 0;\n    mid = (mid + Math.imul(al7, bh5)) | 0;\n    mid = (mid + Math.imul(ah7, bl5)) | 0;\n    hi = (hi + Math.imul(ah7, bh5)) | 0;\n    lo = (lo + Math.imul(al6, bl6)) | 0;\n    mid = (mid + Math.imul(al6, bh6)) | 0;\n    mid = (mid + Math.imul(ah6, bl6)) | 0;\n    hi = (hi + Math.imul(ah6, bh6)) | 0;\n    lo = (lo + Math.imul(al5, bl7)) | 0;\n    mid = (mid + Math.imul(al5, bh7)) | 0;\n    mid = (mid + Math.imul(ah5, bl7)) | 0;\n    hi = (hi + Math.imul(ah5, bh7)) | 0;\n    lo = (lo + Math.imul(al4, bl8)) | 0;\n    mid = (mid + Math.imul(al4, bh8)) | 0;\n    mid = (mid + Math.imul(ah4, bl8)) | 0;\n    hi = (hi + Math.imul(ah4, bh8)) | 0;\n    lo = (lo + Math.imul(al3, bl9)) | 0;\n    mid = (mid + Math.imul(al3, bh9)) | 0;\n    mid = (mid + Math.imul(ah3, bl9)) | 0;\n    hi = (hi + Math.imul(ah3, bh9)) | 0;\n    var w12 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w12 >>> 26)) | 0;\n    w12 &= 0x3ffffff;\n    /* k = 13 */\n    lo = Math.imul(al9, bl4);\n    mid = Math.imul(al9, bh4);\n    mid = (mid + Math.imul(ah9, bl4)) | 0;\n    hi = Math.imul(ah9, bh4);\n    lo = (lo + Math.imul(al8, bl5)) | 0;\n    mid = (mid + Math.imul(al8, bh5)) | 0;\n    mid = (mid + Math.imul(ah8, bl5)) | 0;\n    hi = (hi + Math.imul(ah8, bh5)) | 0;\n    lo = (lo + Math.imul(al7, bl6)) | 0;\n    mid = (mid + Math.imul(al7, bh6)) | 0;\n    mid = (mid + Math.imul(ah7, bl6)) | 0;\n    hi = (hi + Math.imul(ah7, bh6)) | 0;\n    lo = (lo + Math.imul(al6, bl7)) | 0;\n    mid = (mid + Math.imul(al6, bh7)) | 0;\n    mid = (mid + Math.imul(ah6, bl7)) | 0;\n    hi = (hi + Math.imul(ah6, bh7)) | 0;\n    lo = (lo + Math.imul(al5, bl8)) | 0;\n    mid = (mid + Math.imul(al5, bh8)) | 0;\n    mid = (mid + Math.imul(ah5, bl8)) | 0;\n    hi = (hi + Math.imul(ah5, bh8)) | 0;\n    lo = (lo + Math.imul(al4, bl9)) | 0;\n    mid = (mid + Math.imul(al4, bh9)) | 0;\n    mid = (mid + Math.imul(ah4, bl9)) | 0;\n    hi = (hi + Math.imul(ah4, bh9)) | 0;\n    var w13 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w13 >>> 26)) | 0;\n    w13 &= 0x3ffffff;\n    /* k = 14 */\n    lo = Math.imul(al9, bl5);\n    mid = Math.imul(al9, bh5);\n    mid = (mid + Math.imul(ah9, bl5)) | 0;\n    hi = Math.imul(ah9, bh5);\n    lo = (lo + Math.imul(al8, bl6)) | 0;\n    mid = (mid + Math.imul(al8, bh6)) | 0;\n    mid = (mid + Math.imul(ah8, bl6)) | 0;\n    hi = (hi + Math.imul(ah8, bh6)) | 0;\n    lo = (lo + Math.imul(al7, bl7)) | 0;\n    mid = (mid + Math.imul(al7, bh7)) | 0;\n    mid = (mid + Math.imul(ah7, bl7)) | 0;\n    hi = (hi + Math.imul(ah7, bh7)) | 0;\n    lo = (lo + Math.imul(al6, bl8)) | 0;\n    mid = (mid + Math.imul(al6, bh8)) | 0;\n    mid = (mid + Math.imul(ah6, bl8)) | 0;\n    hi = (hi + Math.imul(ah6, bh8)) | 0;\n    lo = (lo + Math.imul(al5, bl9)) | 0;\n    mid = (mid + Math.imul(al5, bh9)) | 0;\n    mid = (mid + Math.imul(ah5, bl9)) | 0;\n    hi = (hi + Math.imul(ah5, bh9)) | 0;\n    var w14 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w14 >>> 26)) | 0;\n    w14 &= 0x3ffffff;\n    /* k = 15 */\n    lo = Math.imul(al9, bl6);\n    mid = Math.imul(al9, bh6);\n    mid = (mid + Math.imul(ah9, bl6)) | 0;\n    hi = Math.imul(ah9, bh6);\n    lo = (lo + Math.imul(al8, bl7)) | 0;\n    mid = (mid + Math.imul(al8, bh7)) | 0;\n    mid = (mid + Math.imul(ah8, bl7)) | 0;\n    hi = (hi + Math.imul(ah8, bh7)) | 0;\n    lo = (lo + Math.imul(al7, bl8)) | 0;\n    mid = (mid + Math.imul(al7, bh8)) | 0;\n    mid = (mid + Math.imul(ah7, bl8)) | 0;\n    hi = (hi + Math.imul(ah7, bh8)) | 0;\n    lo = (lo + Math.imul(al6, bl9)) | 0;\n    mid = (mid + Math.imul(al6, bh9)) | 0;\n    mid = (mid + Math.imul(ah6, bl9)) | 0;\n    hi = (hi + Math.imul(ah6, bh9)) | 0;\n    var w15 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w15 >>> 26)) | 0;\n    w15 &= 0x3ffffff;\n    /* k = 16 */\n    lo = Math.imul(al9, bl7);\n    mid = Math.imul(al9, bh7);\n    mid = (mid + Math.imul(ah9, bl7)) | 0;\n    hi = Math.imul(ah9, bh7);\n    lo = (lo + Math.imul(al8, bl8)) | 0;\n    mid = (mid + Math.imul(al8, bh8)) | 0;\n    mid = (mid + Math.imul(ah8, bl8)) | 0;\n    hi = (hi + Math.imul(ah8, bh8)) | 0;\n    lo = (lo + Math.imul(al7, bl9)) | 0;\n    mid = (mid + Math.imul(al7, bh9)) | 0;\n    mid = (mid + Math.imul(ah7, bl9)) | 0;\n    hi = (hi + Math.imul(ah7, bh9)) | 0;\n    var w16 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w16 >>> 26)) | 0;\n    w16 &= 0x3ffffff;\n    /* k = 17 */\n    lo = Math.imul(al9, bl8);\n    mid = Math.imul(al9, bh8);\n    mid = (mid + Math.imul(ah9, bl8)) | 0;\n    hi = Math.imul(ah9, bh8);\n    lo = (lo + Math.imul(al8, bl9)) | 0;\n    mid = (mid + Math.imul(al8, bh9)) | 0;\n    mid = (mid + Math.imul(ah8, bl9)) | 0;\n    hi = (hi + Math.imul(ah8, bh9)) | 0;\n    var w17 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w17 >>> 26)) | 0;\n    w17 &= 0x3ffffff;\n    /* k = 18 */\n    lo = Math.imul(al9, bl9);\n    mid = Math.imul(al9, bh9);\n    mid = (mid + Math.imul(ah9, bl9)) | 0;\n    hi = Math.imul(ah9, bh9);\n    var w18 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w18 >>> 26)) | 0;\n    w18 &= 0x3ffffff;\n    o[0] = w0;\n    o[1] = w1;\n    o[2] = w2;\n    o[3] = w3;\n    o[4] = w4;\n    o[5] = w5;\n    o[6] = w6;\n    o[7] = w7;\n    o[8] = w8;\n    o[9] = w9;\n    o[10] = w10;\n    o[11] = w11;\n    o[12] = w12;\n    o[13] = w13;\n    o[14] = w14;\n    o[15] = w15;\n    o[16] = w16;\n    o[17] = w17;\n    o[18] = w18;\n    if (c !== 0) {\n      o[19] = c;\n      out.length++;\n    }\n    return out;\n  };\n\n  // Polyfill comb\n  if (!Math.imul) {\n    comb10MulTo = smallMulTo;\n  }\n\n  function bigMulTo (self, num, out) {\n    out.negative = num.negative ^ self.negative;\n    out.length = self.length + num.length;\n\n    var carry = 0;\n    var hncarry = 0;\n    for (var k = 0; k < out.length - 1; k++) {\n      // Sum all words with the same `i + j = k` and accumulate `ncarry`,\n      // note that ncarry could be >= 0x3ffffff\n      var ncarry = hncarry;\n      hncarry = 0;\n      var rword = carry & 0x3ffffff;\n      var maxJ = Math.min(k, num.length - 1);\n      for (var j = Math.max(0, k - self.length + 1); j <= maxJ; j++) {\n        var i = k - j;\n        var a = self.words[i] | 0;\n        var b = num.words[j] | 0;\n        var r = a * b;\n\n        var lo = r & 0x3ffffff;\n        ncarry = (ncarry + ((r / 0x4000000) | 0)) | 0;\n        lo = (lo + rword) | 0;\n        rword = lo & 0x3ffffff;\n        ncarry = (ncarry + (lo >>> 26)) | 0;\n\n        hncarry += ncarry >>> 26;\n        ncarry &= 0x3ffffff;\n      }\n      out.words[k] = rword;\n      carry = ncarry;\n      ncarry = hncarry;\n    }\n    if (carry !== 0) {\n      out.words[k] = carry;\n    } else {\n      out.length--;\n    }\n\n    return out.strip();\n  }\n\n  function jumboMulTo (self, num, out) {\n    var fftm = new FFTM();\n    return fftm.mulp(self, num, out);\n  }\n\n  BN.prototype.mulTo = function mulTo (num, out) {\n    var res;\n    var len = this.length + num.length;\n    if (this.length === 10 && num.length === 10) {\n      res = comb10MulTo(this, num, out);\n    } else if (len < 63) {\n      res = smallMulTo(this, num, out);\n    } else if (len < 1024) {\n      res = bigMulTo(this, num, out);\n    } else {\n      res = jumboMulTo(this, num, out);\n    }\n\n    return res;\n  };\n\n  // Cooley-Tukey algorithm for FFT\n  // slightly revisited to rely on looping instead of recursion\n\n  function FFTM (x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  FFTM.prototype.makeRBT = function makeRBT (N) {\n    var t = new Array(N);\n    var l = BN.prototype._countBits(N) - 1;\n    for (var i = 0; i < N; i++) {\n      t[i] = this.revBin(i, l, N);\n    }\n\n    return t;\n  };\n\n  // Returns binary-reversed representation of `x`\n  FFTM.prototype.revBin = function revBin (x, l, N) {\n    if (x === 0 || x === N - 1) return x;\n\n    var rb = 0;\n    for (var i = 0; i < l; i++) {\n      rb |= (x & 1) << (l - i - 1);\n      x >>= 1;\n    }\n\n    return rb;\n  };\n\n  // Performs \"tweedling\" phase, therefore 'emulating'\n  // behaviour of the recursive algorithm\n  FFTM.prototype.permute = function permute (rbt, rws, iws, rtws, itws, N) {\n    for (var i = 0; i < N; i++) {\n      rtws[i] = rws[rbt[i]];\n      itws[i] = iws[rbt[i]];\n    }\n  };\n\n  FFTM.prototype.transform = function transform (rws, iws, rtws, itws, N, rbt) {\n    this.permute(rbt, rws, iws, rtws, itws, N);\n\n    for (var s = 1; s < N; s <<= 1) {\n      var l = s << 1;\n\n      var rtwdf = Math.cos(2 * Math.PI / l);\n      var itwdf = Math.sin(2 * Math.PI / l);\n\n      for (var p = 0; p < N; p += l) {\n        var rtwdf_ = rtwdf;\n        var itwdf_ = itwdf;\n\n        for (var j = 0; j < s; j++) {\n          var re = rtws[p + j];\n          var ie = itws[p + j];\n\n          var ro = rtws[p + j + s];\n          var io = itws[p + j + s];\n\n          var rx = rtwdf_ * ro - itwdf_ * io;\n\n          io = rtwdf_ * io + itwdf_ * ro;\n          ro = rx;\n\n          rtws[p + j] = re + ro;\n          itws[p + j] = ie + io;\n\n          rtws[p + j + s] = re - ro;\n          itws[p + j + s] = ie - io;\n\n          /* jshint maxdepth : false */\n          if (j !== l) {\n            rx = rtwdf * rtwdf_ - itwdf * itwdf_;\n\n            itwdf_ = rtwdf * itwdf_ + itwdf * rtwdf_;\n            rtwdf_ = rx;\n          }\n        }\n      }\n    }\n  };\n\n  FFTM.prototype.guessLen13b = function guessLen13b (n, m) {\n    var N = Math.max(m, n) | 1;\n    var odd = N & 1;\n    var i = 0;\n    for (N = N / 2 | 0; N; N = N >>> 1) {\n      i++;\n    }\n\n    return 1 << i + 1 + odd;\n  };\n\n  FFTM.prototype.conjugate = function conjugate (rws, iws, N) {\n    if (N <= 1) return;\n\n    for (var i = 0; i < N / 2; i++) {\n      var t = rws[i];\n\n      rws[i] = rws[N - i - 1];\n      rws[N - i - 1] = t;\n\n      t = iws[i];\n\n      iws[i] = -iws[N - i - 1];\n      iws[N - i - 1] = -t;\n    }\n  };\n\n  FFTM.prototype.normalize13b = function normalize13b (ws, N) {\n    var carry = 0;\n    for (var i = 0; i < N / 2; i++) {\n      var w = Math.round(ws[2 * i + 1] / N) * 0x2000 +\n        Math.round(ws[2 * i] / N) +\n        carry;\n\n      ws[i] = w & 0x3ffffff;\n\n      if (w < 0x4000000) {\n        carry = 0;\n      } else {\n        carry = w / 0x4000000 | 0;\n      }\n    }\n\n    return ws;\n  };\n\n  FFTM.prototype.convert13b = function convert13b (ws, len, rws, N) {\n    var carry = 0;\n    for (var i = 0; i < len; i++) {\n      carry = carry + (ws[i] | 0);\n\n      rws[2 * i] = carry & 0x1fff; carry = carry >>> 13;\n      rws[2 * i + 1] = carry & 0x1fff; carry = carry >>> 13;\n    }\n\n    // Pad with zeroes\n    for (i = 2 * len; i < N; ++i) {\n      rws[i] = 0;\n    }\n\n    assert(carry === 0);\n    assert((carry & ~0x1fff) === 0);\n  };\n\n  FFTM.prototype.stub = function stub (N) {\n    var ph = new Array(N);\n    for (var i = 0; i < N; i++) {\n      ph[i] = 0;\n    }\n\n    return ph;\n  };\n\n  FFTM.prototype.mulp = function mulp (x, y, out) {\n    var N = 2 * this.guessLen13b(x.length, y.length);\n\n    var rbt = this.makeRBT(N);\n\n    var _ = this.stub(N);\n\n    var rws = new Array(N);\n    var rwst = new Array(N);\n    var iwst = new Array(N);\n\n    var nrws = new Array(N);\n    var nrwst = new Array(N);\n    var niwst = new Array(N);\n\n    var rmws = out.words;\n    rmws.length = N;\n\n    this.convert13b(x.words, x.length, rws, N);\n    this.convert13b(y.words, y.length, nrws, N);\n\n    this.transform(rws, _, rwst, iwst, N, rbt);\n    this.transform(nrws, _, nrwst, niwst, N, rbt);\n\n    for (var i = 0; i < N; i++) {\n      var rx = rwst[i] * nrwst[i] - iwst[i] * niwst[i];\n      iwst[i] = rwst[i] * niwst[i] + iwst[i] * nrwst[i];\n      rwst[i] = rx;\n    }\n\n    this.conjugate(rwst, iwst, N);\n    this.transform(rwst, iwst, rmws, _, N, rbt);\n    this.conjugate(rmws, _, N);\n    this.normalize13b(rmws, N);\n\n    out.negative = x.negative ^ y.negative;\n    out.length = x.length + y.length;\n    return out.strip();\n  };\n\n  // Multiply `this` by `num`\n  BN.prototype.mul = function mul (num) {\n    var out = new BN(null);\n    out.words = new Array(this.length + num.length);\n    return this.mulTo(num, out);\n  };\n\n  // Multiply employing FFT\n  BN.prototype.mulf = function mulf (num) {\n    var out = new BN(null);\n    out.words = new Array(this.length + num.length);\n    return jumboMulTo(this, num, out);\n  };\n\n  // In-place Multiplication\n  BN.prototype.imul = function imul (num) {\n    return this.clone().mulTo(num, this);\n  };\n\n  BN.prototype.imuln = function imuln (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n\n    // Carry\n    var carry = 0;\n    for (var i = 0; i < this.length; i++) {\n      var w = (this.words[i] | 0) * num;\n      var lo = (w & 0x3ffffff) + (carry & 0x3ffffff);\n      carry >>= 26;\n      carry += (w / 0x4000000) | 0;\n      // NOTE: lo is 27bit maximum\n      carry += lo >>> 26;\n      this.words[i] = lo & 0x3ffffff;\n    }\n\n    if (carry !== 0) {\n      this.words[i] = carry;\n      this.length++;\n    }\n\n    return this;\n  };\n\n  BN.prototype.muln = function muln (num) {\n    return this.clone().imuln(num);\n  };\n\n  // `this` * `this`\n  BN.prototype.sqr = function sqr () {\n    return this.mul(this);\n  };\n\n  // `this` * `this` in-place\n  BN.prototype.isqr = function isqr () {\n    return this.imul(this.clone());\n  };\n\n  // Math.pow(`this`, `num`)\n  BN.prototype.pow = function pow (num) {\n    var w = toBitArray(num);\n    if (w.length === 0) return new BN(1);\n\n    // Skip leading zeroes\n    var res = this;\n    for (var i = 0; i < w.length; i++, res = res.sqr()) {\n      if (w[i] !== 0) break;\n    }\n\n    if (++i < w.length) {\n      for (var q = res.sqr(); i < w.length; i++, q = q.sqr()) {\n        if (w[i] === 0) continue;\n\n        res = res.mul(q);\n      }\n    }\n\n    return res;\n  };\n\n  // Shift-left in-place\n  BN.prototype.iushln = function iushln (bits) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var r = bits % 26;\n    var s = (bits - r) / 26;\n    var carryMask = (0x3ffffff >>> (26 - r)) << (26 - r);\n    var i;\n\n    if (r !== 0) {\n      var carry = 0;\n\n      for (i = 0; i < this.length; i++) {\n        var newCarry = this.words[i] & carryMask;\n        var c = ((this.words[i] | 0) - newCarry) << r;\n        this.words[i] = c | carry;\n        carry = newCarry >>> (26 - r);\n      }\n\n      if (carry) {\n        this.words[i] = carry;\n        this.length++;\n      }\n    }\n\n    if (s !== 0) {\n      for (i = this.length - 1; i >= 0; i--) {\n        this.words[i + s] = this.words[i];\n      }\n\n      for (i = 0; i < s; i++) {\n        this.words[i] = 0;\n      }\n\n      this.length += s;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ishln = function ishln (bits) {\n    // TODO(indutny): implement me\n    assert(this.negative === 0);\n    return this.iushln(bits);\n  };\n\n  // Shift-right in-place\n  // NOTE: `hint` is a lowest bit before trailing zeroes\n  // NOTE: if `extended` is present - it will be filled with destroyed bits\n  BN.prototype.iushrn = function iushrn (bits, hint, extended) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var h;\n    if (hint) {\n      h = (hint - (hint % 26)) / 26;\n    } else {\n      h = 0;\n    }\n\n    var r = bits % 26;\n    var s = Math.min((bits - r) / 26, this.length);\n    var mask = 0x3ffffff ^ ((0x3ffffff >>> r) << r);\n    var maskedWords = extended;\n\n    h -= s;\n    h = Math.max(0, h);\n\n    // Extended mode, copy masked part\n    if (maskedWords) {\n      for (var i = 0; i < s; i++) {\n        maskedWords.words[i] = this.words[i];\n      }\n      maskedWords.length = s;\n    }\n\n    if (s === 0) {\n      // No-op, we should not move anything at all\n    } else if (this.length > s) {\n      this.length -= s;\n      for (i = 0; i < this.length; i++) {\n        this.words[i] = this.words[i + s];\n      }\n    } else {\n      this.words[0] = 0;\n      this.length = 1;\n    }\n\n    var carry = 0;\n    for (i = this.length - 1; i >= 0 && (carry !== 0 || i >= h); i--) {\n      var word = this.words[i] | 0;\n      this.words[i] = (carry << (26 - r)) | (word >>> r);\n      carry = word & mask;\n    }\n\n    // Push carried bits as a mask\n    if (maskedWords && carry !== 0) {\n      maskedWords.words[maskedWords.length++] = carry;\n    }\n\n    if (this.length === 0) {\n      this.words[0] = 0;\n      this.length = 1;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ishrn = function ishrn (bits, hint, extended) {\n    // TODO(indutny): implement me\n    assert(this.negative === 0);\n    return this.iushrn(bits, hint, extended);\n  };\n\n  // Shift-left\n  BN.prototype.shln = function shln (bits) {\n    return this.clone().ishln(bits);\n  };\n\n  BN.prototype.ushln = function ushln (bits) {\n    return this.clone().iushln(bits);\n  };\n\n  // Shift-right\n  BN.prototype.shrn = function shrn (bits) {\n    return this.clone().ishrn(bits);\n  };\n\n  BN.prototype.ushrn = function ushrn (bits) {\n    return this.clone().iushrn(bits);\n  };\n\n  // Test if n bit is set\n  BN.prototype.testn = function testn (bit) {\n    assert(typeof bit === 'number' && bit >= 0);\n    var r = bit % 26;\n    var s = (bit - r) / 26;\n    var q = 1 << r;\n\n    // Fast case: bit is much higher than all existing words\n    if (this.length <= s) return false;\n\n    // Check bit and return\n    var w = this.words[s];\n\n    return !!(w & q);\n  };\n\n  // Return only lowers bits of number (in-place)\n  BN.prototype.imaskn = function imaskn (bits) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var r = bits % 26;\n    var s = (bits - r) / 26;\n\n    assert(this.negative === 0, 'imaskn works only with positive numbers');\n\n    if (this.length <= s) {\n      return this;\n    }\n\n    if (r !== 0) {\n      s++;\n    }\n    this.length = Math.min(s, this.length);\n\n    if (r !== 0) {\n      var mask = 0x3ffffff ^ ((0x3ffffff >>> r) << r);\n      this.words[this.length - 1] &= mask;\n    }\n\n    return this.strip();\n  };\n\n  // Return only lowers bits of number\n  BN.prototype.maskn = function maskn (bits) {\n    return this.clone().imaskn(bits);\n  };\n\n  // Add plain number `num` to `this`\n  BN.prototype.iaddn = function iaddn (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n    if (num < 0) return this.isubn(-num);\n\n    // Possible sign change\n    if (this.negative !== 0) {\n      if (this.length === 1 && (this.words[0] | 0) < num) {\n        this.words[0] = num - (this.words[0] | 0);\n        this.negative = 0;\n        return this;\n      }\n\n      this.negative = 0;\n      this.isubn(num);\n      this.negative = 1;\n      return this;\n    }\n\n    // Add without checks\n    return this._iaddn(num);\n  };\n\n  BN.prototype._iaddn = function _iaddn (num) {\n    this.words[0] += num;\n\n    // Carry\n    for (var i = 0; i < this.length && this.words[i] >= 0x4000000; i++) {\n      this.words[i] -= 0x4000000;\n      if (i === this.length - 1) {\n        this.words[i + 1] = 1;\n      } else {\n        this.words[i + 1]++;\n      }\n    }\n    this.length = Math.max(this.length, i + 1);\n\n    return this;\n  };\n\n  // Subtract plain number `num` from `this`\n  BN.prototype.isubn = function isubn (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n    if (num < 0) return this.iaddn(-num);\n\n    if (this.negative !== 0) {\n      this.negative = 0;\n      this.iaddn(num);\n      this.negative = 1;\n      return this;\n    }\n\n    this.words[0] -= num;\n\n    if (this.length === 1 && this.words[0] < 0) {\n      this.words[0] = -this.words[0];\n      this.negative = 1;\n    } else {\n      // Carry\n      for (var i = 0; i < this.length && this.words[i] < 0; i++) {\n        this.words[i] += 0x4000000;\n        this.words[i + 1] -= 1;\n      }\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.addn = function addn (num) {\n    return this.clone().iaddn(num);\n  };\n\n  BN.prototype.subn = function subn (num) {\n    return this.clone().isubn(num);\n  };\n\n  BN.prototype.iabs = function iabs () {\n    this.negative = 0;\n\n    return this;\n  };\n\n  BN.prototype.abs = function abs () {\n    return this.clone().iabs();\n  };\n\n  BN.prototype._ishlnsubmul = function _ishlnsubmul (num, mul, shift) {\n    var len = num.length + shift;\n    var i;\n\n    this._expand(len);\n\n    var w;\n    var carry = 0;\n    for (i = 0; i < num.length; i++) {\n      w = (this.words[i + shift] | 0) + carry;\n      var right = (num.words[i] | 0) * mul;\n      w -= right & 0x3ffffff;\n      carry = (w >> 26) - ((right / 0x4000000) | 0);\n      this.words[i + shift] = w & 0x3ffffff;\n    }\n    for (; i < this.length - shift; i++) {\n      w = (this.words[i + shift] | 0) + carry;\n      carry = w >> 26;\n      this.words[i + shift] = w & 0x3ffffff;\n    }\n\n    if (carry === 0) return this.strip();\n\n    // Subtraction overflow\n    assert(carry === -1);\n    carry = 0;\n    for (i = 0; i < this.length; i++) {\n      w = -(this.words[i] | 0) + carry;\n      carry = w >> 26;\n      this.words[i] = w & 0x3ffffff;\n    }\n    this.negative = 1;\n\n    return this.strip();\n  };\n\n  BN.prototype._wordDiv = function _wordDiv (num, mode) {\n    var shift = this.length - num.length;\n\n    var a = this.clone();\n    var b = num;\n\n    // Normalize\n    var bhi = b.words[b.length - 1] | 0;\n    var bhiBits = this._countBits(bhi);\n    shift = 26 - bhiBits;\n    if (shift !== 0) {\n      b = b.ushln(shift);\n      a.iushln(shift);\n      bhi = b.words[b.length - 1] | 0;\n    }\n\n    // Initialize quotient\n    var m = a.length - b.length;\n    var q;\n\n    if (mode !== 'mod') {\n      q = new BN(null);\n      q.length = m + 1;\n      q.words = new Array(q.length);\n      for (var i = 0; i < q.length; i++) {\n        q.words[i] = 0;\n      }\n    }\n\n    var diff = a.clone()._ishlnsubmul(b, 1, m);\n    if (diff.negative === 0) {\n      a = diff;\n      if (q) {\n        q.words[m] = 1;\n      }\n    }\n\n    for (var j = m - 1; j >= 0; j--) {\n      var qj = (a.words[b.length + j] | 0) * 0x4000000 +\n        (a.words[b.length + j - 1] | 0);\n\n      // NOTE: (qj / bhi) is (0x3ffffff * 0x4000000 + 0x3ffffff) / 0x2000000 max\n      // (0x7ffffff)\n      qj = Math.min((qj / bhi) | 0, 0x3ffffff);\n\n      a._ishlnsubmul(b, qj, j);\n      while (a.negative !== 0) {\n        qj--;\n        a.negative = 0;\n        a._ishlnsubmul(b, 1, j);\n        if (!a.isZero()) {\n          a.negative ^= 1;\n        }\n      }\n      if (q) {\n        q.words[j] = qj;\n      }\n    }\n    if (q) {\n      q.strip();\n    }\n    a.strip();\n\n    // Denormalize\n    if (mode !== 'div' && shift !== 0) {\n      a.iushrn(shift);\n    }\n\n    return {\n      div: q || null,\n      mod: a\n    };\n  };\n\n  // NOTE: 1) `mode` can be set to `mod` to request mod only,\n  //       to `div` to request div only, or be absent to\n  //       request both div & mod\n  //       2) `positive` is true if unsigned mod is requested\n  BN.prototype.divmod = function divmod (num, mode, positive) {\n    assert(!num.isZero());\n\n    if (this.isZero()) {\n      return {\n        div: new BN(0),\n        mod: new BN(0)\n      };\n    }\n\n    var div, mod, res;\n    if (this.negative !== 0 && num.negative === 0) {\n      res = this.neg().divmod(num, mode);\n\n      if (mode !== 'mod') {\n        div = res.div.neg();\n      }\n\n      if (mode !== 'div') {\n        mod = res.mod.neg();\n        if (positive && mod.negative !== 0) {\n          mod.iadd(num);\n        }\n      }\n\n      return {\n        div: div,\n        mod: mod\n      };\n    }\n\n    if (this.negative === 0 && num.negative !== 0) {\n      res = this.divmod(num.neg(), mode);\n\n      if (mode !== 'mod') {\n        div = res.div.neg();\n      }\n\n      return {\n        div: div,\n        mod: res.mod\n      };\n    }\n\n    if ((this.negative & num.negative) !== 0) {\n      res = this.neg().divmod(num.neg(), mode);\n\n      if (mode !== 'div') {\n        mod = res.mod.neg();\n        if (positive && mod.negative !== 0) {\n          mod.isub(num);\n        }\n      }\n\n      return {\n        div: res.div,\n        mod: mod\n      };\n    }\n\n    // Both numbers are positive at this point\n\n    // Strip both numbers to approximate shift value\n    if (num.length > this.length || this.cmp(num) < 0) {\n      return {\n        div: new BN(0),\n        mod: this\n      };\n    }\n\n    // Very short reduction\n    if (num.length === 1) {\n      if (mode === 'div') {\n        return {\n          div: this.divn(num.words[0]),\n          mod: null\n        };\n      }\n\n      if (mode === 'mod') {\n        return {\n          div: null,\n          mod: new BN(this.modn(num.words[0]))\n        };\n      }\n\n      return {\n        div: this.divn(num.words[0]),\n        mod: new BN(this.modn(num.words[0]))\n      };\n    }\n\n    return this._wordDiv(num, mode);\n  };\n\n  // Find `this` / `num`\n  BN.prototype.div = function div (num) {\n    return this.divmod(num, 'div', false).div;\n  };\n\n  // Find `this` % `num`\n  BN.prototype.mod = function mod (num) {\n    return this.divmod(num, 'mod', false).mod;\n  };\n\n  BN.prototype.umod = function umod (num) {\n    return this.divmod(num, 'mod', true).mod;\n  };\n\n  // Find Round(`this` / `num`)\n  BN.prototype.divRound = function divRound (num) {\n    var dm = this.divmod(num);\n\n    // Fast case - exact division\n    if (dm.mod.isZero()) return dm.div;\n\n    var mod = dm.div.negative !== 0 ? dm.mod.isub(num) : dm.mod;\n\n    var half = num.ushrn(1);\n    var r2 = num.andln(1);\n    var cmp = mod.cmp(half);\n\n    // Round down\n    if (cmp < 0 || r2 === 1 && cmp === 0) return dm.div;\n\n    // Round up\n    return dm.div.negative !== 0 ? dm.div.isubn(1) : dm.div.iaddn(1);\n  };\n\n  BN.prototype.modn = function modn (num) {\n    assert(num <= 0x3ffffff);\n    var p = (1 << 26) % num;\n\n    var acc = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      acc = (p * acc + (this.words[i] | 0)) % num;\n    }\n\n    return acc;\n  };\n\n  // In-place division by number\n  BN.prototype.idivn = function idivn (num) {\n    assert(num <= 0x3ffffff);\n\n    var carry = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      var w = (this.words[i] | 0) + carry * 0x4000000;\n      this.words[i] = (w / num) | 0;\n      carry = w % num;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.divn = function divn (num) {\n    return this.clone().idivn(num);\n  };\n\n  BN.prototype.egcd = function egcd (p) {\n    assert(p.negative === 0);\n    assert(!p.isZero());\n\n    var x = this;\n    var y = p.clone();\n\n    if (x.negative !== 0) {\n      x = x.umod(p);\n    } else {\n      x = x.clone();\n    }\n\n    // A * x + B * y = x\n    var A = new BN(1);\n    var B = new BN(0);\n\n    // C * x + D * y = y\n    var C = new BN(0);\n    var D = new BN(1);\n\n    var g = 0;\n\n    while (x.isEven() && y.isEven()) {\n      x.iushrn(1);\n      y.iushrn(1);\n      ++g;\n    }\n\n    var yp = y.clone();\n    var xp = x.clone();\n\n    while (!x.isZero()) {\n      for (var i = 0, im = 1; (x.words[0] & im) === 0 && i < 26; ++i, im <<= 1);\n      if (i > 0) {\n        x.iushrn(i);\n        while (i-- > 0) {\n          if (A.isOdd() || B.isOdd()) {\n            A.iadd(yp);\n            B.isub(xp);\n          }\n\n          A.iushrn(1);\n          B.iushrn(1);\n        }\n      }\n\n      for (var j = 0, jm = 1; (y.words[0] & jm) === 0 && j < 26; ++j, jm <<= 1);\n      if (j > 0) {\n        y.iushrn(j);\n        while (j-- > 0) {\n          if (C.isOdd() || D.isOdd()) {\n            C.iadd(yp);\n            D.isub(xp);\n          }\n\n          C.iushrn(1);\n          D.iushrn(1);\n        }\n      }\n\n      if (x.cmp(y) >= 0) {\n        x.isub(y);\n        A.isub(C);\n        B.isub(D);\n      } else {\n        y.isub(x);\n        C.isub(A);\n        D.isub(B);\n      }\n    }\n\n    return {\n      a: C,\n      b: D,\n      gcd: y.iushln(g)\n    };\n  };\n\n  // This is reduced incarnation of the binary EEA\n  // above, designated to invert members of the\n  // _prime_ fields F(p) at a maximal speed\n  BN.prototype._invmp = function _invmp (p) {\n    assert(p.negative === 0);\n    assert(!p.isZero());\n\n    var a = this;\n    var b = p.clone();\n\n    if (a.negative !== 0) {\n      a = a.umod(p);\n    } else {\n      a = a.clone();\n    }\n\n    var x1 = new BN(1);\n    var x2 = new BN(0);\n\n    var delta = b.clone();\n\n    while (a.cmpn(1) > 0 && b.cmpn(1) > 0) {\n      for (var i = 0, im = 1; (a.words[0] & im) === 0 && i < 26; ++i, im <<= 1);\n      if (i > 0) {\n        a.iushrn(i);\n        while (i-- > 0) {\n          if (x1.isOdd()) {\n            x1.iadd(delta);\n          }\n\n          x1.iushrn(1);\n        }\n      }\n\n      for (var j = 0, jm = 1; (b.words[0] & jm) === 0 && j < 26; ++j, jm <<= 1);\n      if (j > 0) {\n        b.iushrn(j);\n        while (j-- > 0) {\n          if (x2.isOdd()) {\n            x2.iadd(delta);\n          }\n\n          x2.iushrn(1);\n        }\n      }\n\n      if (a.cmp(b) >= 0) {\n        a.isub(b);\n        x1.isub(x2);\n      } else {\n        b.isub(a);\n        x2.isub(x1);\n      }\n    }\n\n    var res;\n    if (a.cmpn(1) === 0) {\n      res = x1;\n    } else {\n      res = x2;\n    }\n\n    if (res.cmpn(0) < 0) {\n      res.iadd(p);\n    }\n\n    return res;\n  };\n\n  BN.prototype.gcd = function gcd (num) {\n    if (this.isZero()) return num.abs();\n    if (num.isZero()) return this.abs();\n\n    var a = this.clone();\n    var b = num.clone();\n    a.negative = 0;\n    b.negative = 0;\n\n    // Remove common factor of two\n    for (var shift = 0; a.isEven() && b.isEven(); shift++) {\n      a.iushrn(1);\n      b.iushrn(1);\n    }\n\n    do {\n      while (a.isEven()) {\n        a.iushrn(1);\n      }\n      while (b.isEven()) {\n        b.iushrn(1);\n      }\n\n      var r = a.cmp(b);\n      if (r < 0) {\n        // Swap `a` and `b` to make `a` always bigger than `b`\n        var t = a;\n        a = b;\n        b = t;\n      } else if (r === 0 || b.cmpn(1) === 0) {\n        break;\n      }\n\n      a.isub(b);\n    } while (true);\n\n    return b.iushln(shift);\n  };\n\n  // Invert number in the field F(num)\n  BN.prototype.invm = function invm (num) {\n    return this.egcd(num).a.umod(num);\n  };\n\n  BN.prototype.isEven = function isEven () {\n    return (this.words[0] & 1) === 0;\n  };\n\n  BN.prototype.isOdd = function isOdd () {\n    return (this.words[0] & 1) === 1;\n  };\n\n  // And first word and num\n  BN.prototype.andln = function andln (num) {\n    return this.words[0] & num;\n  };\n\n  // Increment at the bit position in-line\n  BN.prototype.bincn = function bincn (bit) {\n    assert(typeof bit === 'number');\n    var r = bit % 26;\n    var s = (bit - r) / 26;\n    var q = 1 << r;\n\n    // Fast case: bit is much higher than all existing words\n    if (this.length <= s) {\n      this._expand(s + 1);\n      this.words[s] |= q;\n      return this;\n    }\n\n    // Add bit and propagate, if needed\n    var carry = q;\n    for (var i = s; carry !== 0 && i < this.length; i++) {\n      var w = this.words[i] | 0;\n      w += carry;\n      carry = w >>> 26;\n      w &= 0x3ffffff;\n      this.words[i] = w;\n    }\n    if (carry !== 0) {\n      this.words[i] = carry;\n      this.length++;\n    }\n    return this;\n  };\n\n  BN.prototype.isZero = function isZero () {\n    return this.length === 1 && this.words[0] === 0;\n  };\n\n  BN.prototype.cmpn = function cmpn (num) {\n    var negative = num < 0;\n\n    if (this.negative !== 0 && !negative) return -1;\n    if (this.negative === 0 && negative) return 1;\n\n    this.strip();\n\n    var res;\n    if (this.length > 1) {\n      res = 1;\n    } else {\n      if (negative) {\n        num = -num;\n      }\n\n      assert(num <= 0x3ffffff, 'Number is too big');\n\n      var w = this.words[0] | 0;\n      res = w === num ? 0 : w < num ? -1 : 1;\n    }\n    if (this.negative !== 0) return -res | 0;\n    return res;\n  };\n\n  // Compare two numbers and return:\n  // 1 - if `this` > `num`\n  // 0 - if `this` == `num`\n  // -1 - if `this` < `num`\n  BN.prototype.cmp = function cmp (num) {\n    if (this.negative !== 0 && num.negative === 0) return -1;\n    if (this.negative === 0 && num.negative !== 0) return 1;\n\n    var res = this.ucmp(num);\n    if (this.negative !== 0) return -res | 0;\n    return res;\n  };\n\n  // Unsigned comparison\n  BN.prototype.ucmp = function ucmp (num) {\n    // At this point both numbers have the same sign\n    if (this.length > num.length) return 1;\n    if (this.length < num.length) return -1;\n\n    var res = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      var a = this.words[i] | 0;\n      var b = num.words[i] | 0;\n\n      if (a === b) continue;\n      if (a < b) {\n        res = -1;\n      } else if (a > b) {\n        res = 1;\n      }\n      break;\n    }\n    return res;\n  };\n\n  BN.prototype.gtn = function gtn (num) {\n    return this.cmpn(num) === 1;\n  };\n\n  BN.prototype.gt = function gt (num) {\n    return this.cmp(num) === 1;\n  };\n\n  BN.prototype.gten = function gten (num) {\n    return this.cmpn(num) >= 0;\n  };\n\n  BN.prototype.gte = function gte (num) {\n    return this.cmp(num) >= 0;\n  };\n\n  BN.prototype.ltn = function ltn (num) {\n    return this.cmpn(num) === -1;\n  };\n\n  BN.prototype.lt = function lt (num) {\n    return this.cmp(num) === -1;\n  };\n\n  BN.prototype.lten = function lten (num) {\n    return this.cmpn(num) <= 0;\n  };\n\n  BN.prototype.lte = function lte (num) {\n    return this.cmp(num) <= 0;\n  };\n\n  BN.prototype.eqn = function eqn (num) {\n    return this.cmpn(num) === 0;\n  };\n\n  BN.prototype.eq = function eq (num) {\n    return this.cmp(num) === 0;\n  };\n\n  //\n  // A reduce context, could be using montgomery or something better, depending\n  // on the `m` itself.\n  //\n  BN.red = function red (num) {\n    return new Red(num);\n  };\n\n  BN.prototype.toRed = function toRed (ctx) {\n    assert(!this.red, 'Already a number in reduction context');\n    assert(this.negative === 0, 'red works only with positives');\n    return ctx.convertTo(this)._forceRed(ctx);\n  };\n\n  BN.prototype.fromRed = function fromRed () {\n    assert(this.red, 'fromRed works only with numbers in reduction context');\n    return this.red.convertFrom(this);\n  };\n\n  BN.prototype._forceRed = function _forceRed (ctx) {\n    this.red = ctx;\n    return this;\n  };\n\n  BN.prototype.forceRed = function forceRed (ctx) {\n    assert(!this.red, 'Already a number in reduction context');\n    return this._forceRed(ctx);\n  };\n\n  BN.prototype.redAdd = function redAdd (num) {\n    assert(this.red, 'redAdd works only with red numbers');\n    return this.red.add(this, num);\n  };\n\n  BN.prototype.redIAdd = function redIAdd (num) {\n    assert(this.red, 'redIAdd works only with red numbers');\n    return this.red.iadd(this, num);\n  };\n\n  BN.prototype.redSub = function redSub (num) {\n    assert(this.red, 'redSub works only with red numbers');\n    return this.red.sub(this, num);\n  };\n\n  BN.prototype.redISub = function redISub (num) {\n    assert(this.red, 'redISub works only with red numbers');\n    return this.red.isub(this, num);\n  };\n\n  BN.prototype.redShl = function redShl (num) {\n    assert(this.red, 'redShl works only with red numbers');\n    return this.red.shl(this, num);\n  };\n\n  BN.prototype.redMul = function redMul (num) {\n    assert(this.red, 'redMul works only with red numbers');\n    this.red._verify2(this, num);\n    return this.red.mul(this, num);\n  };\n\n  BN.prototype.redIMul = function redIMul (num) {\n    assert(this.red, 'redMul works only with red numbers');\n    this.red._verify2(this, num);\n    return this.red.imul(this, num);\n  };\n\n  BN.prototype.redSqr = function redSqr () {\n    assert(this.red, 'redSqr works only with red numbers');\n    this.red._verify1(this);\n    return this.red.sqr(this);\n  };\n\n  BN.prototype.redISqr = function redISqr () {\n    assert(this.red, 'redISqr works only with red numbers');\n    this.red._verify1(this);\n    return this.red.isqr(this);\n  };\n\n  // Square root over p\n  BN.prototype.redSqrt = function redSqrt () {\n    assert(this.red, 'redSqrt works only with red numbers');\n    this.red._verify1(this);\n    return this.red.sqrt(this);\n  };\n\n  BN.prototype.redInvm = function redInvm () {\n    assert(this.red, 'redInvm works only with red numbers');\n    this.red._verify1(this);\n    return this.red.invm(this);\n  };\n\n  // Return negative clone of `this` % `red modulo`\n  BN.prototype.redNeg = function redNeg () {\n    assert(this.red, 'redNeg works only with red numbers');\n    this.red._verify1(this);\n    return this.red.neg(this);\n  };\n\n  BN.prototype.redPow = function redPow (num) {\n    assert(this.red && !num.red, 'redPow(normalNum)');\n    this.red._verify1(this);\n    return this.red.pow(this, num);\n  };\n\n  // Prime numbers with efficient reduction\n  var primes = {\n    k256: null,\n    p224: null,\n    p192: null,\n    p25519: null\n  };\n\n  // Pseudo-Mersenne prime\n  function MPrime (name, p) {\n    // P = 2 ^ N - K\n    this.name = name;\n    this.p = new BN(p, 16);\n    this.n = this.p.bitLength();\n    this.k = new BN(1).iushln(this.n).isub(this.p);\n\n    this.tmp = this._tmp();\n  }\n\n  MPrime.prototype._tmp = function _tmp () {\n    var tmp = new BN(null);\n    tmp.words = new Array(Math.ceil(this.n / 13));\n    return tmp;\n  };\n\n  MPrime.prototype.ireduce = function ireduce (num) {\n    // Assumes that `num` is less than `P^2`\n    // num = HI * (2 ^ N - K) + HI * K + LO = HI * K + LO (mod P)\n    var r = num;\n    var rlen;\n\n    do {\n      this.split(r, this.tmp);\n      r = this.imulK(r);\n      r = r.iadd(this.tmp);\n      rlen = r.bitLength();\n    } while (rlen > this.n);\n\n    var cmp = rlen < this.n ? -1 : r.ucmp(this.p);\n    if (cmp === 0) {\n      r.words[0] = 0;\n      r.length = 1;\n    } else if (cmp > 0) {\n      r.isub(this.p);\n    } else {\n      if (r.strip !== undefined) {\n        // r is BN v4 instance\n        r.strip();\n      } else {\n        // r is BN v5 instance\n        r._strip();\n      }\n    }\n\n    return r;\n  };\n\n  MPrime.prototype.split = function split (input, out) {\n    input.iushrn(this.n, 0, out);\n  };\n\n  MPrime.prototype.imulK = function imulK (num) {\n    return num.imul(this.k);\n  };\n\n  function K256 () {\n    MPrime.call(\n      this,\n      'k256',\n      'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f');\n  }\n  inherits(K256, MPrime);\n\n  K256.prototype.split = function split (input, output) {\n    // 256 = 9 * 26 + 22\n    var mask = 0x3fffff;\n\n    var outLen = Math.min(input.length, 9);\n    for (var i = 0; i < outLen; i++) {\n      output.words[i] = input.words[i];\n    }\n    output.length = outLen;\n\n    if (input.length <= 9) {\n      input.words[0] = 0;\n      input.length = 1;\n      return;\n    }\n\n    // Shift by 9 limbs\n    var prev = input.words[9];\n    output.words[output.length++] = prev & mask;\n\n    for (i = 10; i < input.length; i++) {\n      var next = input.words[i] | 0;\n      input.words[i - 10] = ((next & mask) << 4) | (prev >>> 22);\n      prev = next;\n    }\n    prev >>>= 22;\n    input.words[i - 10] = prev;\n    if (prev === 0 && input.length > 10) {\n      input.length -= 10;\n    } else {\n      input.length -= 9;\n    }\n  };\n\n  K256.prototype.imulK = function imulK (num) {\n    // K = 0x1000003d1 = [ 0x40, 0x3d1 ]\n    num.words[num.length] = 0;\n    num.words[num.length + 1] = 0;\n    num.length += 2;\n\n    // bounded at: 0x40 * 0x3ffffff + 0x3d0 = 0x100000390\n    var lo = 0;\n    for (var i = 0; i < num.length; i++) {\n      var w = num.words[i] | 0;\n      lo += w * 0x3d1;\n      num.words[i] = lo & 0x3ffffff;\n      lo = w * 0x40 + ((lo / 0x4000000) | 0);\n    }\n\n    // Fast length reduction\n    if (num.words[num.length - 1] === 0) {\n      num.length--;\n      if (num.words[num.length - 1] === 0) {\n        num.length--;\n      }\n    }\n    return num;\n  };\n\n  function P224 () {\n    MPrime.call(\n      this,\n      'p224',\n      'ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001');\n  }\n  inherits(P224, MPrime);\n\n  function P192 () {\n    MPrime.call(\n      this,\n      'p192',\n      'ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff');\n  }\n  inherits(P192, MPrime);\n\n  function P25519 () {\n    // 2 ^ 255 - 19\n    MPrime.call(\n      this,\n      '25519',\n      '7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed');\n  }\n  inherits(P25519, MPrime);\n\n  P25519.prototype.imulK = function imulK (num) {\n    // K = 0x13\n    var carry = 0;\n    for (var i = 0; i < num.length; i++) {\n      var hi = (num.words[i] | 0) * 0x13 + carry;\n      var lo = hi & 0x3ffffff;\n      hi >>>= 26;\n\n      num.words[i] = lo;\n      carry = hi;\n    }\n    if (carry !== 0) {\n      num.words[num.length++] = carry;\n    }\n    return num;\n  };\n\n  // Exported mostly for testing purposes, use plain name instead\n  BN._prime = function prime (name) {\n    // Cached version of prime\n    if (primes[name]) return primes[name];\n\n    var prime;\n    if (name === 'k256') {\n      prime = new K256();\n    } else if (name === 'p224') {\n      prime = new P224();\n    } else if (name === 'p192') {\n      prime = new P192();\n    } else if (name === 'p25519') {\n      prime = new P25519();\n    } else {\n      throw new Error('Unknown prime ' + name);\n    }\n    primes[name] = prime;\n\n    return prime;\n  };\n\n  //\n  // Base reduction engine\n  //\n  function Red (m) {\n    if (typeof m === 'string') {\n      var prime = BN._prime(m);\n      this.m = prime.p;\n      this.prime = prime;\n    } else {\n      assert(m.gtn(1), 'modulus must be greater than 1');\n      this.m = m;\n      this.prime = null;\n    }\n  }\n\n  Red.prototype._verify1 = function _verify1 (a) {\n    assert(a.negative === 0, 'red works only with positives');\n    assert(a.red, 'red works only with red numbers');\n  };\n\n  Red.prototype._verify2 = function _verify2 (a, b) {\n    assert((a.negative | b.negative) === 0, 'red works only with positives');\n    assert(a.red && a.red === b.red,\n      'red works only with red numbers');\n  };\n\n  Red.prototype.imod = function imod (a) {\n    if (this.prime) return this.prime.ireduce(a)._forceRed(this);\n    return a.umod(this.m)._forceRed(this);\n  };\n\n  Red.prototype.neg = function neg (a) {\n    if (a.isZero()) {\n      return a.clone();\n    }\n\n    return this.m.sub(a)._forceRed(this);\n  };\n\n  Red.prototype.add = function add (a, b) {\n    this._verify2(a, b);\n\n    var res = a.add(b);\n    if (res.cmp(this.m) >= 0) {\n      res.isub(this.m);\n    }\n    return res._forceRed(this);\n  };\n\n  Red.prototype.iadd = function iadd (a, b) {\n    this._verify2(a, b);\n\n    var res = a.iadd(b);\n    if (res.cmp(this.m) >= 0) {\n      res.isub(this.m);\n    }\n    return res;\n  };\n\n  Red.prototype.sub = function sub (a, b) {\n    this._verify2(a, b);\n\n    var res = a.sub(b);\n    if (res.cmpn(0) < 0) {\n      res.iadd(this.m);\n    }\n    return res._forceRed(this);\n  };\n\n  Red.prototype.isub = function isub (a, b) {\n    this._verify2(a, b);\n\n    var res = a.isub(b);\n    if (res.cmpn(0) < 0) {\n      res.iadd(this.m);\n    }\n    return res;\n  };\n\n  Red.prototype.shl = function shl (a, num) {\n    this._verify1(a);\n    return this.imod(a.ushln(num));\n  };\n\n  Red.prototype.imul = function imul (a, b) {\n    this._verify2(a, b);\n    return this.imod(a.imul(b));\n  };\n\n  Red.prototype.mul = function mul (a, b) {\n    this._verify2(a, b);\n    return this.imod(a.mul(b));\n  };\n\n  Red.prototype.isqr = function isqr (a) {\n    return this.imul(a, a.clone());\n  };\n\n  Red.prototype.sqr = function sqr (a) {\n    return this.mul(a, a);\n  };\n\n  Red.prototype.sqrt = function sqrt (a) {\n    if (a.isZero()) return a.clone();\n\n    var mod3 = this.m.andln(3);\n    assert(mod3 % 2 === 1);\n\n    // Fast case\n    if (mod3 === 3) {\n      var pow = this.m.add(new BN(1)).iushrn(2);\n      return this.pow(a, pow);\n    }\n\n    // Tonelli-Shanks algorithm (Totally unoptimized and slow)\n    //\n    // Find Q and S, that Q * 2 ^ S = (P - 1)\n    var q = this.m.subn(1);\n    var s = 0;\n    while (!q.isZero() && q.andln(1) === 0) {\n      s++;\n      q.iushrn(1);\n    }\n    assert(!q.isZero());\n\n    var one = new BN(1).toRed(this);\n    var nOne = one.redNeg();\n\n    // Find quadratic non-residue\n    // NOTE: Max is such because of generalized Riemann hypothesis.\n    var lpow = this.m.subn(1).iushrn(1);\n    var z = this.m.bitLength();\n    z = new BN(2 * z * z).toRed(this);\n\n    while (this.pow(z, lpow).cmp(nOne) !== 0) {\n      z.redIAdd(nOne);\n    }\n\n    var c = this.pow(z, q);\n    var r = this.pow(a, q.addn(1).iushrn(1));\n    var t = this.pow(a, q);\n    var m = s;\n    while (t.cmp(one) !== 0) {\n      var tmp = t;\n      for (var i = 0; tmp.cmp(one) !== 0; i++) {\n        tmp = tmp.redSqr();\n      }\n      assert(i < m);\n      var b = this.pow(c, new BN(1).iushln(m - i - 1));\n\n      r = r.redMul(b);\n      c = b.redSqr();\n      t = t.redMul(c);\n      m = i;\n    }\n\n    return r;\n  };\n\n  Red.prototype.invm = function invm (a) {\n    var inv = a._invmp(this.m);\n    if (inv.negative !== 0) {\n      inv.negative = 0;\n      return this.imod(inv).redNeg();\n    } else {\n      return this.imod(inv);\n    }\n  };\n\n  Red.prototype.pow = function pow (a, num) {\n    if (num.isZero()) return new BN(1).toRed(this);\n    if (num.cmpn(1) === 0) return a.clone();\n\n    var windowSize = 4;\n    var wnd = new Array(1 << windowSize);\n    wnd[0] = new BN(1).toRed(this);\n    wnd[1] = a;\n    for (var i = 2; i < wnd.length; i++) {\n      wnd[i] = this.mul(wnd[i - 1], a);\n    }\n\n    var res = wnd[0];\n    var current = 0;\n    var currentLen = 0;\n    var start = num.bitLength() % 26;\n    if (start === 0) {\n      start = 26;\n    }\n\n    for (i = num.length - 1; i >= 0; i--) {\n      var word = num.words[i];\n      for (var j = start - 1; j >= 0; j--) {\n        var bit = (word >> j) & 1;\n        if (res !== wnd[0]) {\n          res = this.sqr(res);\n        }\n\n        if (bit === 0 && current === 0) {\n          currentLen = 0;\n          continue;\n        }\n\n        current <<= 1;\n        current |= bit;\n        currentLen++;\n        if (currentLen !== windowSize && (i !== 0 || j !== 0)) continue;\n\n        res = this.mul(res, wnd[current]);\n        currentLen = 0;\n        current = 0;\n      }\n      start = 26;\n    }\n\n    return res;\n  };\n\n  Red.prototype.convertTo = function convertTo (num) {\n    var r = num.umod(this.m);\n\n    return r === num ? r.clone() : r;\n  };\n\n  Red.prototype.convertFrom = function convertFrom (num) {\n    var res = num.clone();\n    res.red = null;\n    return res;\n  };\n\n  //\n  // Montgomery method engine\n  //\n\n  BN.mont = function mont (num) {\n    return new Mont(num);\n  };\n\n  function Mont (m) {\n    Red.call(this, m);\n\n    this.shift = this.m.bitLength();\n    if (this.shift % 26 !== 0) {\n      this.shift += 26 - (this.shift % 26);\n    }\n\n    this.r = new BN(1).iushln(this.shift);\n    this.r2 = this.imod(this.r.sqr());\n    this.rinv = this.r._invmp(this.m);\n\n    this.minv = this.rinv.mul(this.r).isubn(1).div(this.m);\n    this.minv = this.minv.umod(this.r);\n    this.minv = this.r.sub(this.minv);\n  }\n  inherits(Mont, Red);\n\n  Mont.prototype.convertTo = function convertTo (num) {\n    return this.imod(num.ushln(this.shift));\n  };\n\n  Mont.prototype.convertFrom = function convertFrom (num) {\n    var r = this.imod(num.mul(this.rinv));\n    r.red = null;\n    return r;\n  };\n\n  Mont.prototype.imul = function imul (a, b) {\n    if (a.isZero() || b.isZero()) {\n      a.words[0] = 0;\n      a.length = 1;\n      return a;\n    }\n\n    var t = a.imul(b);\n    var c = t.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m);\n    var u = t.isub(c).iushrn(this.shift);\n    var res = u;\n\n    if (u.cmp(this.m) >= 0) {\n      res = u.isub(this.m);\n    } else if (u.cmpn(0) < 0) {\n      res = u.iadd(this.m);\n    }\n\n    return res._forceRed(this);\n  };\n\n  Mont.prototype.mul = function mul (a, b) {\n    if (a.isZero() || b.isZero()) return new BN(0)._forceRed(this);\n\n    var t = a.mul(b);\n    var c = t.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m);\n    var u = t.isub(c).iushrn(this.shift);\n    var res = u;\n    if (u.cmp(this.m) >= 0) {\n      res = u.isub(this.m);\n    } else if (u.cmpn(0) < 0) {\n      res = u.iadd(this.m);\n    }\n\n    return res._forceRed(this);\n  };\n\n  Mont.prototype.invm = function invm (a) {\n    // (AR)^-1 * R^2 = (A^-1 * R^-1) * R^2 = A^-1 * R\n    var res = this.imod(a._invmp(this.m).mul(this.r2));\n    return res._forceRed(this);\n  };\n})(typeof module === 'undefined' || module, this);\n", "module.exports = require(\"web3\");", "module.exports = require(\"@maticnetwork/maticjs\");", "module.exports = function(module) {\n\tif (!module.webpackPolyfill) {\n\t\tmodule.deprecate = function() {};\n\t\tmodule.paths = [];\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n", "module.exports = require(\"buffer\");", "import { BaseBigNumber } from \"@maticnetwork/maticjs\";\nimport BN from \"bn.js\";\nimport Web3 from \"web3\";\n\nexport class MaticBigNumber extends BaseBigNumber {\n    private bn_: BN;\n\n    constructor(value) {\n        super();\n        this.bn_ = new BN(value);\n\n        // this.bn_.toString()\n    }\n\n    static isBN(value) {\n        if (value instanceof MaticBigNumber) {\n            return true;\n        }\n        return BN.isBN(value);\n    }\n\n    toString(base?) {\n        return this.bn_.toString(base);\n    }\n\n    toNumber() {\n        return this.bn_.toNumber();\n    }\n\n    toBuffer(base?) {\n        return this.bn_.toBuffer();\n    }\n\n    // static from(value) {\n    //     return new MaticBigNumber(value);\n    // }\n\n    add(value: BaseBigNumber) {\n        const bn = this.bn_.add(\n            new BN(value.toString())\n        );\n        return new MaticBigNumber(bn);\n    }\n\n    sub(value: BaseBigNumber) {\n        const bn = this.bn_.sub(\n            new BN(value.toString())\n        );\n        return new MaticBigNumber(bn);\n    }\n\n    mul(value: BaseBigNumber) {\n        const bn = this.bn_.mul(\n            new BN(value.toString())\n        );\n        return new MaticBigNumber(bn);\n    }\n\n    div(value: BaseBigNumber) {\n        const bn = this.bn_.div(\n            new BN(value.toString())\n        );\n        return new MaticBigNumber(bn);\n    }\n\n    lte(value: BaseBigNumber) {\n        return this.bn_.lte(\n            new BN(value.toString())\n        );\n    }\n\n    lt(value: BaseBigNumber) {\n        return this.bn_.lt(\n            new BN(value.toString())\n        );\n    }\n\n    gte(value: BaseBigNumber) {\n        return this.bn_.gte(\n            new BN(value.toString())\n        );\n    }\n\n    gt(value: BaseBigNumber) {\n        return this.bn_.gt(\n            new BN(value.toString())\n        );\n    }\n\n    eq(value: BaseBigNumber) {\n        return this.bn_.eq(\n            new BN(value.toString())\n        );\n    }\n}", "import { ITransactionRequestConfig } from \"@maticnetwork/maticjs\";\nimport Web3 from \"web3\";\nimport { TransactionConfig } from \"web3-core\";\n\nexport const maticTxRequestConfigToWeb3 = (config: ITransactionRequestConfig = {}) => {\n    const toHex = Web3.utils.toHex;\n    return {\n        chainId: toHex(config.chainId) as any,\n        data: config.data,\n        from: config.from,\n        gas: config.gasLimit,\n        gasPrice: config.gasPrice,\n        nonce: config.nonce,\n        to: config.to,\n        value: config.value,\n        maxFeePerGas: config.maxFeePerGas,\n        maxPriorityFeePerGas: config.maxPriorityFeePerGas,\n        type: toHex(config.type),\n        hardfork: config.hardfork\n    } as TransactionConfig;\n};", "import { ITransactionReceipt } from \"@maticnetwork/maticjs\";\n\n\nexport const web3ReceiptToMaticReceipt = (receipt: any) => {\n    return {\n        blockHash: receipt.blockHash,\n        blockNumber: receipt.blockNumber,\n        contractAddress: receipt.contractAddress,\n        cumulativeGasUsed: receipt.cumulativeGasUsed,\n        from: receipt.from,\n        gasUsed: receipt.gasUsed,\n        status: receipt.status,\n        to: receipt.to,\n        transactionHash: receipt.transactionHash,\n        transactionIndex: receipt.transactionIndex,\n        events: receipt.events,\n        logs: receipt.logs,\n        logsBloom: receipt.logsBloom,\n        root: (receipt as any).root,\n        type: (receipt as any).type\n    } as ITransactionReceipt;\n\n};", "import { ITransactionData } from \"@maticnetwork/maticjs\";\nimport { Transaction } from \"web3/eth/types\";\n\nexport const web3TxToMaticTx = (tx: Transaction) => {\n    const maticTx: ITransactionData = tx as any;\n    maticTx.transactionHash = tx.hash;\n    return maticTx;\n};", "import { ITransactionWriteResult } from \"@maticnetwork/maticjs\";\nimport { web3ReceiptToMaticReceipt } from \"../utils\";\nimport { doNothing } from \"./do_nothing\";\n\nexport class TransactionWriteResult implements ITransactionWriteResult {\n\n    onTransactionHash: Function;\n    onTransactionError: Function;\n    onTransactionReceiptError: Function;\n\n    onTransactionReceipt: Function;\n\n    getReceipt;\n    getTransactionHash;\n\n    constructor(private promise: any) {\n        const receiptPromise = new Promise<any>((res, rej) => {\n            this.onTransactionReceipt = res;\n            this.onTransactionReceiptError = rej;\n        });\n        this.getReceipt = () => {\n            return receiptPromise.then(receipt => {\n                return web3ReceiptToMaticReceipt(receipt);\n            });\n        };\n\n        const txHashPromise = new Promise<string>((res, rej) => {\n            this.onTransactionHash = res;\n            this.onTransactionError = rej;\n        });\n\n        this.getTransactionHash = () => {\n            return txHashPromise;\n        };\n\n        promise.once(\"transactionHash\", this.onTransactionHash).\n            once(\"receipt\", this.onTransactionReceipt as any).\n            on(\"error\", this.onTransactionError).\n            on(\"error\", this.onTransactionReceiptError);\n    }\n\n\n}", "import { BaseContract<PERSON>ethod, Logger, ITransactionRequestConfig, Converter } from \"@maticnetwork/maticjs\";\nimport Web3 from \"web3\";\nimport { TransactionObject, Tx } from \"web3/eth/types\";\nimport { doNothing, TransactionWriteResult } from \"../helpers\";\nimport { maticTxRequestConfigToWeb3 } from \"../utils\";\n\nexport class EthMethod extends BaseContractMethod {\n\n    constructor(public address, logger: Logger, private method: TransactionObject<any>) {\n        super(logger);\n    }\n\n    toHex(value) {\n        return value != null ? Web3.utils.toHex(value) : value;\n    }\n\n    read<T>(tx: ITransactionRequestConfig, defaultBlock?: number | string): Promise<T> {\n        this.logger.log(\"sending tx with config\", tx, defaultBlock);\n        return (this.method.call as any)(\n            maticTxRequestConfigToWeb3(tx) as any, defaultBlock\n        );\n    }\n\n    write(tx: ITransactionRequestConfig) {\n\n        return new TransactionWriteResult(\n            this.method.send(\n                maticTxRequestConfigToWeb3(tx) as any\n            )\n        );\n    }\n\n    estimateGas(tx: ITransactionRequestConfig): Promise<number> {\n        return this.method.estimateGas(\n            maticTxRequestConfigToWeb3(tx) as any\n        );\n    }\n\n    encodeABI() {\n        return this.method.encodeABI();\n    }\n\n}\n", "import { BaseContract } from \"@maticnetwork/maticjs\";\nimport Contract from \"web3/eth/contract\";\nimport { EthMethod } from \"./eth_method\";\n\nexport class Web3Contract extends BaseContract {\n    contract: Contract;\n\n    constructor(address: string, contract: Contract, logger) {\n        super(address, logger);\n        this.contract = contract;\n    }\n\n    method(methodName: string, ...args) {\n        this.logger.log(\"methodName\", methodName, \"args method\", arguments);\n        return new EthMethod(\n            this.address, this.logger, this.contract.methods[methodName](...args)\n        );\n    }\n}", "import { Web3Contract } from \"./eth_contract\";\nimport Web3 from \"web3\";\nimport { Transaction } from \"web3/eth/types\";\nimport { AbstractProvider } from \"web3-core\";\nimport { doNothing, TransactionWriteResult } from \"../helpers\";\nimport { BaseWeb3Client, IBlockWithTransaction, IJsonRpcRequestPayload, IJsonRpcResponse, ITransactionRequestConfig, ITransactionData, ITransactionReceipt, Logger, ERROR_TYPE, IError } from \"@maticnetwork/maticjs\";\nimport { maticTxRequestConfigToWeb3, web3ReceiptToMaticReceipt, web3TxToMaticTx } from \"../utils\";\n\nexport class Web3Client extends BaseWeb3Client {\n    private web3_: Web3;\n    name = 'WEB3';\n\n    constructor(provider: any, logger: Logger) {\n        super(logger);\n        this.web3_ = new Web3(provider);\n    }\n\n\n    read(config: ITransactionRequestConfig) {\n        return this.web3_.eth.call(\n            maticTxRequestConfigToWeb3(config)\n        );\n    }\n\n    write(config: ITransactionRequestConfig) {\n        return new TransactionWriteResult(\n            this.web3_.eth.sendTransaction(\n                maticTxRequestConfigToWeb3(config)\n            )\n        );\n    }\n\n    getContract(address: string, abi: any) {\n        const cont = new this.web3_.eth.Contract(abi, address);\n        return new Web3Contract(address, cont as any, this.logger);\n    }\n\n    getGasPrice() {\n        return this.web3_.eth.getGasPrice();\n    }\n\n    estimateGas(config: ITransactionRequestConfig) {\n        return this.web3_.eth.estimateGas(\n            maticTxRequestConfigToWeb3(config)\n        );\n    }\n\n    getTransactionCount(address: string, blockNumber: any) {\n        return this.web3_.eth.getTransactionCount(address, blockNumber);\n    }\n\n    getAccounts() {\n        return this.web3_.eth.getAccounts();\n    }\n\n    async getChainId() {\n        try {\n            const chainId = await this.web3_.eth.net.getId();\n            if (chainId) {\n                return chainId;\n            }\n            throw new Error('net_version is not enabled');\n        } catch (e) {\n            return this.web3_.eth.getChainId();\n        }\n    }\n\n    private ensureTransactionNotNull_(data) {\n        if (!data) {\n            throw {\n                type: 'invalid_transaction' as any,\n                message: 'Could not retrieve transaction. Either it is invalid or might be in archive node.'\n            } as IError;\n        }\n    }\n\n    getTransaction(transactionHash: string) {\n        return this.web3_.eth.getTransaction(transactionHash).then(data => {\n            this.ensureTransactionNotNull_(data);\n            return web3TxToMaticTx(data);\n        });\n    }\n\n    getTransactionReceipt(transactionHash: string): Promise<ITransactionReceipt> {\n        return this.web3_.eth.getTransactionReceipt(transactionHash).then(data => {\n            this.ensureTransactionNotNull_(data);\n            return web3ReceiptToMaticReceipt(data);\n        });\n    }\n\n    getBlock(blockHashOrBlockNumber) {\n        return (this.web3_.eth.getBlock(blockHashOrBlockNumber) as any);\n    }\n\n    getBalance(address) {\n        return this.web3_.eth.getBalance(address);\n    }\n\n    getBlockWithTransaction(blockHashOrBlockNumber) {\n        return this.web3_.eth.getBlock(blockHashOrBlockNumber, true).then(result => {\n            const blockData: IBlockWithTransaction = result as any;\n            blockData.transactions = result.transactions.map(tx => {\n                return web3TxToMaticTx(tx);\n            });\n            return blockData;\n        });\n    }\n\n    sendRPCRequest(request: IJsonRpcRequestPayload) {\n        return new Promise<IJsonRpcResponse>((res, rej) => {\n            (this.web3_.currentProvider as AbstractProvider).send(request, (error, result) => {\n                if (error) return rej(error);\n                res(result as any);\n            });\n        });\n    }\n\n    signTypedData(signer, typedData) {\n        return this.sendRPCRequest({\n            jsonrpc: '2.0',\n            method: 'eth_signTypedData_v4',\n            params: [signer, typedData],\n            id: new Date().getTime()\n        }).then(payload => {\n            return String(payload.result);\n        });\n    }\n\n    encodeParameters(params: any[], types: any[]) {\n        return this.web3_.eth.abi.encodeParameters(types, params);\n    }\n\n    decodeParameters(hexString, types: any[]) {\n        return this.web3_.eth.abi.decodeParameters(types, hexString) as any;\n    }\n\n    etheriumSha3(...value) {\n        return Web3.utils.soliditySha3(...value);\n    }\n\n    hexToNumber(value) {\n        return Web3.utils.hexToNumber(value);\n    }\n\n    hexToNumberString(value) {\n        return Web3.utils.hexToNumberString(value);\n    }\n}\n", "import { IPlugin } from \"@maticnetwork/maticjs\";\nimport Web3 from \"web3\";\nimport { MaticBigNumber } from \"./utils\";\nimport { Web3Client } from \"./web3\";\n\nexport class Web3ClientPlugin implements IPlugin {\n    setup(matic) {\n        matic.utils.Web3Client = Web3Client;\n        matic.utils.BN = MaticBigNumber;\n        matic.utils.isBN = (value) => {\n            return Web3.utils.isBN(value);\n        };\n    }\n}\n\nexport * from \"./utils\";\n\n/* tslint:disable-next-line */\nexport default Web3ClientPlugin;"], "sourceRoot": ""}