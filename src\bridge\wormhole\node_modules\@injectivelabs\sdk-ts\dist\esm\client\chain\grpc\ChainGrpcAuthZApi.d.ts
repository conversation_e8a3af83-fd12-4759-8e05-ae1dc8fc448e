import { CosmosAuthzV1Beta1Query } from '@injectivelabs/core-proto-ts';
import BaseGrpcConsumer from '../../base/BaseGrpcConsumer.js';
import { PaginationOption } from '../../../types/pagination.js';
/**
 * @category Chain Grpc API
 */
export declare class ChainGrpcAuthZApi extends BaseGrpcConsumer {
    protected module: string;
    protected client: CosmosAuthzV1Beta1Query.QueryClientImpl;
    constructor(endpoint: string);
    fetchGrants({ pagination, granter, grantee, msgTypeUrl, }: {
        pagination?: PaginationOption;
        granter: string;
        grantee: string;
        msgTypeUrl?: string;
    }): Promise<{
        pagination: import("../../../types/pagination.js").Pagination;
        grants: import("../types/authZ.js").GrantWithDecodedAuthorization[];
    }>;
    fetchGranterGrants(granter: string, pagination?: PaginationOption): Promise<{
        pagination: import("../../../types/pagination.js").Pagination;
        grants: import("../types/authZ.js").GrantAuthorizationWithDecodedAuthorization[];
    }>;
    fetchGranteeGrants(grantee: string, pagination?: PaginationOption): Promise<{
        pagination: import("../../../types/pagination.js").Pagination;
        grants: import("../types/authZ.js").GrantAuthorizationWithDecodedAuthorization[];
    }>;
}
