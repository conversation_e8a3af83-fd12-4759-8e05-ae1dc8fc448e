#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
价格监控脚本 - 异步获取CEX和DEX价格
"""

import os
import sys
import time
import asyncio
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.cex.gate.client import GateClient
from src.dex.astar.arthswap.client import ArthSwapClient
from src.utils.proxy_manager import ProxyManager
from src.utils.logger import logger

# 套利阈值设置
CEX_TO_DEX_THRESHOLD = 0.3  # CEX买入DEX卖出的价差阈值
DEX_TO_CEX_THRESHOLD = 0.6  # DEX买入CEX卖出的价差阈值

async def get_cex_price(symbol="ASTR/USDT"):
    """
    获取CEX价格（不使用代理）
    
    Args:
        symbol: 交易对，默认为ASTR/USDT
        
    Returns:
        float: 当前价格，失败返回None
    """
    try:
        client = GateClient()
        order_book = client.get_order_book(symbol, limit=1)
        
        if order_book and 'bids' in order_book and order_book['bids']:
            # 获取最高买价
            bid_price = float(order_book['bids'][0][0])
            logger.info(f"CEX {symbol} 当前买价: {bid_price}")
            return bid_price
        else:
            logger.error(f"无法获取CEX {symbol} 价格数据")
            return None
    except Exception as e:
        logger.error(f"获取CEX价格时出错: {e}")
        return None

async def get_dex_price(token="WASTR", base_token="USDT", proxy_manager=None):
    """
    获取DEX价格（使用代理）
    
    Args:
        token: 代币符号，默认为WASTR
        base_token: 基准代币，默认为USDT
        proxy_manager: 代理管理器实例
        
    Returns:
        float: 当前价格，失败返回None
    """
    try:
        # 创建ArthSwap客户端
        client = ArthSwapClient()
        
        # 获取代币价格
        price = client.get_token_price(token, base_token)
        
        if price is not None:
            logger.info(f"DEX {token}/{base_token} 当前价格: {price}")
            return price
        else:
            logger.error(f"无法获取DEX {token}/{base_token} 价格数据")
            return None
    except Exception as e:
        logger.error(f"获取DEX价格时出错: {e}")
        # 如果代理失效，标记当前代理失败并尝试切换
        if proxy_manager:
            proxy_manager.mark_current_proxy_failed()
            logger.info("代理失效，尝试切换新代理")
        return None

async def monitor_prices(interval=30):
    """
    监控CEX和DEX价格
    
    Args:
        interval: 监控间隔（秒）
    """
    # 初始化代理管理器
    proxy_manager = ProxyManager()
    
    while True:
        try:
            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"开始新一轮价格监控 - {current_time}")
            
            # 异步获取CEX和DEX价格
            cex_price_task = asyncio.create_task(get_cex_price())
            dex_price_task = asyncio.create_task(get_dex_price(proxy_manager=proxy_manager))
            
            # 等待两个任务完成
            cex_price, dex_price = await asyncio.gather(cex_price_task, dex_price_task)
            
            # 如果两个价格都获取成功，计算价差
            if cex_price is not None and dex_price is not None:
                # 计算CEX买入DEX卖出的价差百分比
                cex_to_dex_diff = ((dex_price - cex_price) / cex_price) * 100
                
                # 计算DEX买入CEX卖出的价差百分比
                dex_to_cex_diff = ((cex_price - dex_price) / dex_price) * 100
                
                logger.info(f"CEX价格: {cex_price}, DEX价格: {dex_price}")
                logger.info(f"CEX买入DEX卖出价差: {cex_to_dex_diff:.2f}%")
                logger.info(f"DEX买入CEX卖出价差: {dex_to_cex_diff:.2f}%")
                
                # 检查是否有套利机会
                if cex_to_dex_diff >= CEX_TO_DEX_THRESHOLD:
                    logger.info(f"发现套利机会: CEX买入DEX卖出 (价差: {cex_to_dex_diff:.2f}%)")
                    print(f"\n[套利机会] CEX买入DEX卖出 - 价差: {cex_to_dex_diff:.2f}%")
                    print(f"CEX价格: {cex_price}, DEX价格: {dex_price}")
                
                if dex_to_cex_diff >= DEX_TO_CEX_THRESHOLD:
                    logger.info(f"发现套利机会: DEX买入CEX卖出 (价差: {dex_to_cex_diff:.2f}%)")
                    print(f"\n[套利机会] DEX买入CEX卖出 - 价差: {dex_to_cex_diff:.2f}%")
                    print(f"CEX价格: {cex_price}, DEX价格: {dex_price}")
            
            # 等待下一次监控
            logger.info(f"等待 {interval} 秒后进行下一次监控...")
            await asyncio.sleep(interval)
            
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
            await asyncio.sleep(interval)

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="监控CEX和DEX价格差异")
    parser.add_argument("-i", "--interval", type=int, default=30, help="监控间隔（秒），默认为30秒")
    parser.add_argument("-t", "--threshold", type=float, help="自定义CEX到DEX的价差阈值（百分比）")
    parser.add_argument("-d", "--dex-threshold", type=float, help="自定义DEX到CEX的价差阈值（百分比）")
    
    args = parser.parse_args()
    
    # 如果提供了自定义阈值，则更新全局变量
    if args.threshold is not None:
        global CEX_TO_DEX_THRESHOLD
        CEX_TO_DEX_THRESHOLD = args.threshold
        logger.info(f"已设置CEX到DEX的价差阈值为: {CEX_TO_DEX_THRESHOLD}%")
    
    if args.dex_threshold is not None:
        global DEX_TO_CEX_THRESHOLD
        DEX_TO_CEX_THRESHOLD = args.dex_threshold
        logger.info(f"已设置DEX到CEX的价差阈值为: {DEX_TO_CEX_THRESHOLD}%")
    
    logger.info(f"开始监控CEX和DEX价格差异，间隔: {args.interval}秒")
    logger.info(f"CEX买入DEX卖出价差阈值: {CEX_TO_DEX_THRESHOLD}%")
    logger.info(f"DEX买入CEX卖出价差阈值: {DEX_TO_CEX_THRESHOLD}%")
    
    try:
        await monitor_prices(args.interval)
    except KeyboardInterrupt:
        logger.info("用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")

if __name__ == "__main__":
    # 在Windows上使用SelectorEventLoop
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main()) 