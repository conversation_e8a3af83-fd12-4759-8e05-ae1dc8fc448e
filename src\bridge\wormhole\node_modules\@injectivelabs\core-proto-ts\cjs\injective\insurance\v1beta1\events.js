"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventInsuranceWithdraw = exports.EventUnderwrite = exports.EventWithdrawRedemption = exports.EventRequestRedemption = exports.EventInsuranceFundUpdate = exports.protobufPackage = void 0;
/* eslint-disable */
var minimal_js_1 = __importDefault(require("protobufjs/minimal.js"));
var coin_1 = require("../../../cosmos/base/v1beta1/coin.js");
var insurance_1 = require("./insurance.js");
exports.protobufPackage = "injective.insurance.v1beta1";
function createBaseEventInsuranceFundUpdate() {
    return { fund: undefined };
}
exports.EventInsuranceFundUpdate = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.fund !== undefined) {
            insurance_1.InsuranceFund.encode(message.fund, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventInsuranceFundUpdate();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.fund = insurance_1.InsuranceFund.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { fund: isSet(object.fund) ? insurance_1.InsuranceFund.fromJSON(object.fund) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.fund !== undefined && (obj.fund = message.fund ? insurance_1.InsuranceFund.toJSON(message.fund) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventInsuranceFundUpdate.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventInsuranceFundUpdate();
        message.fund = (object.fund !== undefined && object.fund !== null)
            ? insurance_1.InsuranceFund.fromPartial(object.fund)
            : undefined;
        return message;
    },
};
function createBaseEventRequestRedemption() {
    return { schedule: undefined };
}
exports.EventRequestRedemption = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.schedule !== undefined) {
            insurance_1.RedemptionSchedule.encode(message.schedule, writer.uint32(10).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventRequestRedemption();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.schedule = insurance_1.RedemptionSchedule.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return { schedule: isSet(object.schedule) ? insurance_1.RedemptionSchedule.fromJSON(object.schedule) : undefined };
    },
    toJSON: function (message) {
        var obj = {};
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? insurance_1.RedemptionSchedule.toJSON(message.schedule) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventRequestRedemption.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventRequestRedemption();
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? insurance_1.RedemptionSchedule.fromPartial(object.schedule)
            : undefined;
        return message;
    },
};
function createBaseEventWithdrawRedemption() {
    return { schedule: undefined, redeemCoin: undefined };
}
exports.EventWithdrawRedemption = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.schedule !== undefined) {
            insurance_1.RedemptionSchedule.encode(message.schedule, writer.uint32(10).fork()).ldelim();
        }
        if (message.redeemCoin !== undefined) {
            coin_1.Coin.encode(message.redeemCoin, writer.uint32(18).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventWithdrawRedemption();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.schedule = insurance_1.RedemptionSchedule.decode(reader, reader.uint32());
                    break;
                case 2:
                    message.redeemCoin = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            schedule: isSet(object.schedule) ? insurance_1.RedemptionSchedule.fromJSON(object.schedule) : undefined,
            redeemCoin: isSet(object.redeemCoin) ? coin_1.Coin.fromJSON(object.redeemCoin) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.schedule !== undefined &&
            (obj.schedule = message.schedule ? insurance_1.RedemptionSchedule.toJSON(message.schedule) : undefined);
        message.redeemCoin !== undefined &&
            (obj.redeemCoin = message.redeemCoin ? coin_1.Coin.toJSON(message.redeemCoin) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventWithdrawRedemption.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var message = createBaseEventWithdrawRedemption();
        message.schedule = (object.schedule !== undefined && object.schedule !== null)
            ? insurance_1.RedemptionSchedule.fromPartial(object.schedule)
            : undefined;
        message.redeemCoin = (object.redeemCoin !== undefined && object.redeemCoin !== null)
            ? coin_1.Coin.fromPartial(object.redeemCoin)
            : undefined;
        return message;
    },
};
function createBaseEventUnderwrite() {
    return { underwriter: "", marketId: "", deposit: undefined, shares: undefined };
}
exports.EventUnderwrite = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.underwriter !== "") {
            writer.uint32(10).string(message.underwriter);
        }
        if (message.marketId !== "") {
            writer.uint32(18).string(message.marketId);
        }
        if (message.deposit !== undefined) {
            coin_1.Coin.encode(message.deposit, writer.uint32(26).fork()).ldelim();
        }
        if (message.shares !== undefined) {
            coin_1.Coin.encode(message.shares, writer.uint32(34).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventUnderwrite();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.underwriter = reader.string();
                    break;
                case 2:
                    message.marketId = reader.string();
                    break;
                case 3:
                    message.deposit = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                case 4:
                    message.shares = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            underwriter: isSet(object.underwriter) ? String(object.underwriter) : "",
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            deposit: isSet(object.deposit) ? coin_1.Coin.fromJSON(object.deposit) : undefined,
            shares: isSet(object.shares) ? coin_1.Coin.fromJSON(object.shares) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.underwriter !== undefined && (obj.underwriter = message.underwriter);
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.deposit !== undefined && (obj.deposit = message.deposit ? coin_1.Coin.toJSON(message.deposit) : undefined);
        message.shares !== undefined && (obj.shares = message.shares ? coin_1.Coin.toJSON(message.shares) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventUnderwrite.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventUnderwrite();
        message.underwriter = (_a = object.underwriter) !== null && _a !== void 0 ? _a : "";
        message.marketId = (_b = object.marketId) !== null && _b !== void 0 ? _b : "";
        message.deposit = (object.deposit !== undefined && object.deposit !== null)
            ? coin_1.Coin.fromPartial(object.deposit)
            : undefined;
        message.shares = (object.shares !== undefined && object.shares !== null)
            ? coin_1.Coin.fromPartial(object.shares)
            : undefined;
        return message;
    },
};
function createBaseEventInsuranceWithdraw() {
    return { marketId: "", marketTicker: "", withdrawal: undefined };
}
exports.EventInsuranceWithdraw = {
    encode: function (message, writer) {
        if (writer === void 0) { writer = minimal_js_1.default.Writer.create(); }
        if (message.marketId !== "") {
            writer.uint32(10).string(message.marketId);
        }
        if (message.marketTicker !== "") {
            writer.uint32(18).string(message.marketTicker);
        }
        if (message.withdrawal !== undefined) {
            coin_1.Coin.encode(message.withdrawal, writer.uint32(26).fork()).ldelim();
        }
        return writer;
    },
    decode: function (input, length) {
        var reader = input instanceof minimal_js_1.default.Reader ? input : new minimal_js_1.default.Reader(input);
        var end = length === undefined ? reader.len : reader.pos + length;
        var message = createBaseEventInsuranceWithdraw();
        while (reader.pos < end) {
            var tag = reader.uint32();
            switch (tag >>> 3) {
                case 1:
                    message.marketId = reader.string();
                    break;
                case 2:
                    message.marketTicker = reader.string();
                    break;
                case 3:
                    message.withdrawal = coin_1.Coin.decode(reader, reader.uint32());
                    break;
                default:
                    reader.skipType(tag & 7);
                    break;
            }
        }
        return message;
    },
    fromJSON: function (object) {
        return {
            marketId: isSet(object.marketId) ? String(object.marketId) : "",
            marketTicker: isSet(object.marketTicker) ? String(object.marketTicker) : "",
            withdrawal: isSet(object.withdrawal) ? coin_1.Coin.fromJSON(object.withdrawal) : undefined,
        };
    },
    toJSON: function (message) {
        var obj = {};
        message.marketId !== undefined && (obj.marketId = message.marketId);
        message.marketTicker !== undefined && (obj.marketTicker = message.marketTicker);
        message.withdrawal !== undefined &&
            (obj.withdrawal = message.withdrawal ? coin_1.Coin.toJSON(message.withdrawal) : undefined);
        return obj;
    },
    create: function (base) {
        return exports.EventInsuranceWithdraw.fromPartial(base !== null && base !== void 0 ? base : {});
    },
    fromPartial: function (object) {
        var _a, _b;
        var message = createBaseEventInsuranceWithdraw();
        message.marketId = (_a = object.marketId) !== null && _a !== void 0 ? _a : "";
        message.marketTicker = (_b = object.marketTicker) !== null && _b !== void 0 ? _b : "";
        message.withdrawal = (object.withdrawal !== undefined && object.withdrawal !== null)
            ? coin_1.Coin.fromPartial(object.withdrawal)
            : undefined;
        return message;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
