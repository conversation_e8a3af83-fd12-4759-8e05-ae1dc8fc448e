import { InjectiveExchangeV1Beta1Tx } from '@injectivelabs/core-proto-ts';
import { MsgBase } from '../../MsgBase.js';
export declare namespace MsgInstantSpotMarketLaunch {
    interface Params {
        proposer: string;
        market: {
            sender: string;
            ticker: string;
            baseDenom: string;
            quoteDenom: string;
            minNotional: string;
            baseDecimals: number;
            quoteDecimals: number;
            minPriceTickSize: string;
            minQuantityTickSize: string;
        };
    }
    type Proto = InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch;
}
/**
 * @category Messages
 */
export default class MsgInstantSpotMarketLaunch extends MsgBase<MsgInstantSpotMarketLaunch.Params, MsgInstantSpotMarketLaunch.Proto> {
    static fromJSON(params: MsgInstantSpotMarketLaunch.Params): MsgInstantSpotMarketLaunch;
    toProto(): InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch;
    toData(): {
        sender: string;
        ticker: string;
        baseDenom: string;
        quoteDenom: string;
        minPriceTickSize: string;
        minQuantityTickSize: string;
        minNotional: string;
        baseDecimals: number;
        quoteDecimals: number;
        '@type': string;
    };
    toAmino(): {
        type: string;
        value: {
            sender: string;
            ticker: string;
            base_denom: string;
            quote_denom: string;
            min_price_tick_size: string;
            min_quantity_tick_size: string;
            min_notional: string;
            base_decimals: number;
            quote_decimals: number;
        };
    };
    toWeb3Gw(): {
        sender: string;
        ticker: string;
        base_denom: string;
        quote_denom: string;
        min_price_tick_size: string;
        min_quantity_tick_size: string;
        min_notional: string;
        base_decimals: number;
        quote_decimals: number;
        '@type': string;
    };
    toEip712(): {
        type: string;
        value: {
            min_price_tick_size: string;
            min_quantity_tick_size: string;
            min_notional: string;
            sender: string;
            ticker: string;
            base_denom: string;
            quote_denom: string;
            base_decimals: number;
            quote_decimals: number;
        };
    };
    toEip712V2(): {
        min_price_tick_size: string;
        min_quantity_tick_size: string;
        min_notional: string;
        sender: string;
        ticker: string;
        base_denom: string;
        quote_denom: string;
        base_decimals: number;
        quote_decimals: number;
        '@type': string;
    };
    toDirectSign(): {
        type: string;
        message: InjectiveExchangeV1Beta1Tx.MsgInstantSpotMarketLaunch;
    };
    toBinary(): Uint8Array;
}
