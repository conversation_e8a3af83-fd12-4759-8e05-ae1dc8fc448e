"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcTendermintApi = void 0;
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../types/index.js");
const exceptions_1 = require("@injectivelabs/exceptions");
/**
 * @category Chain Grpc API
 */
class ChainGrpcTendermintApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_1.ChainModule.Tendermint;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.CosmosBaseTendermintV1Beta1Query.ServiceClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchLatestBlock() {
        const request = core_proto_ts_1.CosmosBaseTendermintV1Beta1Query.GetLatestBlockRequest.create();
        try {
            const response = await this.retry(() => this.client.GetLatestBlock(request, this.metadata));
            return response.sdkBlock || response.block;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosBaseTendermintV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'TendermintApi.fetchLatestBlock',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'TendermintApi.fetchLatestBlock',
                contextModule: this.module,
            });
        }
    }
    async fetchBlock(height) {
        const request = core_proto_ts_1.CosmosBaseTendermintV1Beta1Query.GetBlockByHeightRequest.create();
        request.height = height.toString();
        try {
            const response = await this.retry(() => this.client.GetBlockByHeight(request, this.metadata));
            return response.sdkBlock || response.block;
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.CosmosBaseTendermintV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'TendermintApi.fetchBlock',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'TendermintApi.fetchBlock',
                contextModule: this.module,
            });
        }
    }
}
exports.ChainGrpcTendermintApi = ChainGrpcTendermintApi;
