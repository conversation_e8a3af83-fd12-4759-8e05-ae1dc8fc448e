{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../src/util/view.ts"], "names": [], "mappings": ";;;AAAA,iCAAuC;AACvC,uDAA8F;AAE9F,+CAAsE;AACtE,wCAAiC;AAOjC,MAAM,KAAK,GAAG,IAAA,aAAM,EAAC,UAAU,CAAC,CAAA;AAChC,MAAM,UAAU,GAAG;IACjB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;IACnB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC;IAC3B,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC;CACrB,CAAA;AAEV,MAAM,SAAS,GAAG,CAAC,KAAgB,EAAQ,EAAE;IAC3C,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;AAC/C,CAAC,CAAA;AACD,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAU,CAAA;AAEjE,MAAM,MAAM,GAAG,CAAC,IAAW,EAAE,CAAY,EAAE,EAAE;IAC3C,CAAC,GAAG,CAAC,IAAI,KAAK,CAAA;IACd,MAAM,aAAa,GAAG;QACpB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;QAC1B,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC;QAC7B,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;QACxB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;QACxB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;QACxB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;QACzB,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;KAC1B,CAAA;IACD,OAAO,aAAa,CAAC,IAAI,CAAC,CAAA;AAC5B,CAAC,CAAA;AACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;AAEnC,SAAS,WAAW,CAAC,IAAc;IACjC,OAAO,IAAI,YAAY,qBAAU;QAC/B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,IAAI,YAAY,wBAAa;YAC/B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,YAAY,mBAAQ;gBAC1B,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,IAAI,CAAA;AACV,CAAC;AAED,SAAS,OAAO,CAAC,IAAU,EAAE,IAAc,EAAE,UAAoB;IAC/D,SAAS,CAAC,CAAC,CAAC,CAAA;IACZ,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAA;IAC9B,IAAI,IAAA,mBAAW,EAAE,IAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE;QAClE,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CACvB,OAAO,IAAA,kBAAU,EAAE,IAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAC3D,CAAC,EACD,EAAE,CACH,2FAA2F,CAC7F,CAAA;KACF;SAAM;QACL,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,IAAA,kBAAU,EAAE,IAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,CAAA;KAC/F;IACD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,UAAU,GAAG,CAAC,CAAA;IAChD,IAAI,UAAU,IAAI,IAAI,EAAE;QACtB,MAAM,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA;QACrC,MAAM,CAAC,cAAc,CAAC,GAAG,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;KACxD;SAAM,IAAI,WAAW,IAAI,IAAI,EAAE;QAC9B,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aACjD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;aAC1D,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;aAC5B,OAAO,EAAE,EAAE;YACZ,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,sBAAsB,CAAC,CAAA;YACpE,KAAK,GAAG,KAAK,CAAA;YACb,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;YACzB,MAAM,CAAC,cAAc,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;SAC9C;KACF;IACD,SAAS,CAAC,CAAC,CAAC,CAAA;AACd,CAAC;AAEM,MAAM,IAAI,GAAG,KAAK,EAAE,QAAgB,EAAE,MAAa,EAAE,IAAY,EAAE,EAAE;IAC1E,MAAM,IAAI,GAAG,IAAI,cAAI,EAAE,CAAA;IACvB,MAAM,MAAM,GAAG,IAAI,CAAA;IACnB,MAAM,QAAQ,GAAmC,IAAI,GAAG,EAAE,CAAA;IAC1D,MAAM,WAAW,GAAyC,IAAI,GAAG,EAAE,CAAA;IACnE,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,UAAU,GAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAA;QAC/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;gBACjE,KAAK,CAAC,CAAC,CAAC,GAAG,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;aAChC;iBAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACvC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;aACjC;SACF;QACD,IAAI;YACF,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;SACnC;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;SACvC;QACD,IAAI,CAAC,UAAU,EAAE,CAAA;QACjB,MAAM,IAAI,CAAC,MAAM,EAAE,CAAA;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACvB,QAAQ,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAC5C,WAAW,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACjD,IAAI,KAAK,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,QAAQ,CAAC,OAAO,EAAE,EAAE;YAClD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC,CAAA;YACjD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,MAAM,SAAS,IAAI,IAAA,mBAAW,EAAC,SAAS,EAAE,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAA;SAC1F;KACF;IACD,MAAM,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,CAAA;IACvC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;IACrD,SAAS,CAAC,CAAC,CAAC,CAAA;IACZ,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAA;IACnC,IAAI,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,MAAM,EAAE;QAC/C,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;KAChC;AACH,CAAC,CAAA;AApCY,QAAA,IAAI,QAoChB"}