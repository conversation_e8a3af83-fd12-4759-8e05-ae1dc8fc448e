import _m0 from "protobufjs/minimal.js";
export declare const protobufPackage = "cosmos.bank.v1beta1";
/** EventSetBalance is an event that tracks the latest bank balance. */
export interface EventSetBalances {
    balanceUpdates: BalanceUpdate[];
}
/** BalanceUpdate contains a given address's latest balance */
export interface BalanceUpdate {
    addr: Uint8Array;
    denom: Uint8Array;
    /** the latest amount */
    amt: string;
}
export declare const EventSetBalances: {
    encode(message: EventSetBalances, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): EventSetBalances;
    fromJSON(object: any): EventSetBalances;
    toJSON(message: EventSetBalances): unknown;
    create(base?: DeepPartial<EventSetBalances>): EventSetBalances;
    fromPartial(object: DeepPartial<EventSetBalances>): EventSetBalances;
};
export declare const BalanceUpdate: {
    encode(message: BalanceUp<PERSON>, writer?: _m0.Writer): _m0.Writer;
    decode(input: _m0.Reader | Uint8Array, length?: number): BalanceUpdate;
    fromJSON(object: any): BalanceUpdate;
    toJSON(message: BalanceUpdate): unknown;
    create(base?: DeepPartial<BalanceUpdate>): BalanceUpdate;
    fromPartial(object: DeepPartial<BalanceUpdate>): BalanceUpdate;
};
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends Array<infer U> ? Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
export {};
