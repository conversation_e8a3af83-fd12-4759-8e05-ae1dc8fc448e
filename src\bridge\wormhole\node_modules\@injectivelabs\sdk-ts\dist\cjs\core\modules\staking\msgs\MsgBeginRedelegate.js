"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgBeginRedelegate extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgBeginRedelegate(params);
    }
    toProto() {
        const { params } = this;
        const coinAmount = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        coinAmount.denom = params.amount.denom;
        coinAmount.amount = params.amount.amount;
        const message = core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgBeginRedelegate.create();
        message.delegatorAddress = params.injectiveAddress;
        message.validatorSrcAddress = params.srcValidatorAddress;
        message.validatorDstAddress = params.dstValidatorAddress;
        message.amount = coinAmount;
        return core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgBeginRedelegate.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/cosmos.staking.v1beta1.MsgBeginRedelegate',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'cosmos-sdk/MsgBeginRedelegate',
            value: message,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/cosmos.staking.v1beta1.MsgBeginRedelegate',
            ...value,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/cosmos.staking.v1beta1.MsgBeginRedelegate',
            message: proto,
        };
    }
    toBinary() {
        return core_proto_ts_1.CosmosStakingV1Beta1Tx.MsgBeginRedelegate.encode(this.toProto()).finish();
    }
}
exports.default = MsgBeginRedelegate;
