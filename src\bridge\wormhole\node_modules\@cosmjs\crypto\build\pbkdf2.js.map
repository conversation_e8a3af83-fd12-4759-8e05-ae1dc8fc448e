{"version": 3, "file": "pbkdf2.js", "sourceRoot": "", "sources": ["../src/pbkdf2.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yCAAuC;AACvC,iDAAuE;AACvE,iDAA6D;AAE7D;;;;;;GAMG;AACI,KAAK,UAAU,aAAa;IACjC,IAAI;QACF,MAAM,UAAU,GAAG,wDAAa,QAAQ,GAAC,CAAC;QAC1C,8DAA8D;QAC9D,oEAAoE;QACpE,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE;YACzE,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,UAAU,CAAC;KACnB;IAAC,MAAM;QACN,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAZD,sCAYC;AAEM,KAAK,UAAU,SAAS;IAC7B,iEAAiE;IACjE,gEAAgE;IAChE,oEAAoE;IACpE,qBAAqB;IACrB,mEAAmE;IACnE,mEAAmE;IAEnE,2BAA2B;IAC3B,IAAI,MAAM,GAAqB,UAAkB,EAAE,MAAM,EAAE,MAAM,CAAC;IAClE,cAAc;IACd,IAAI,CAAC,MAAM;QAAE,MAAM,GAAI,UAAkB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;IAErE,OAAO,MAAM,CAAC;AAChB,CAAC;AAdD,8BAcC;AAEM,KAAK,UAAU,kBAAkB;AACtC,6EAA6E;AAC7E,MAAW,EACX,MAAkB,EAClB,IAAgB,EAChB,UAAkB,EAClB,MAAc;IAEd,IAAA,cAAM,EAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;IAC3C,IAAA,cAAM,EAAC,OAAO,MAAM,KAAK,QAAQ,EAAE,uCAAuC,CAAC,CAAC;IAC5E,IAAA,cAAM,EAAC,OAAO,MAAM,CAAC,SAAS,KAAK,UAAU,EAAE,oCAAoC,CAAC,CAAC;IACrF,IAAA,cAAM,EAAC,OAAO,MAAM,CAAC,UAAU,KAAK,UAAU,EAAE,qCAAqC,CAAC,CAAC;IAEvF,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAe,EAAE,EAAE,CACzG,MAAM;SACH,UAAU,CACT;QACE,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,IAAI;QACV,UAAU,EAAE,UAAU;QACtB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B,EACD,GAAG,EACH,MAAM,GAAG,CAAC,CACX;SACA,IAAI,CAAC,CAAC,MAAmB,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CACzD,CAAC;AACJ,CAAC;AA3BD,gDA2BC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB;AAC1C,6EAA6E;AAC7E,UAAe,EACf,MAAkB,EAClB,IAAgB,EAChB,UAAkB,EAClB,MAAc;IAEd,IAAA,cAAM,EAAC,UAAU,EAAE,8BAA8B,CAAC,CAAC;IACnD,IAAA,cAAM,EAAC,OAAO,UAAU,KAAK,QAAQ,EAAE,2CAA2C,CAAC,CAAC;IACpF,IAAA,cAAM,EAAC,OAAO,UAAU,CAAC,MAAM,KAAK,UAAU,EAAE,qCAAqC,CAAC,CAAC;IAEvF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAU,EAAE,MAAW,EAAE,EAAE;YACxF,IAAI,KAAK,EAAE;gBACT,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;iBAAM;gBACL,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AArBD,wDAqBC;AAEM,KAAK,UAAU,iBAAiB,CACrC,MAAkB,EAClB,IAAgB,EAChB,UAAkB,EAClB,MAAc;IAEd,OAAO,IAAA,oBAAgB,EAAC,eAAW,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AACvF,CAAC;AAPD,8CAOC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY,CAChC,MAAkB,EAClB,IAAgB,EAChB,UAAkB,EAClB,MAAc;IAEd,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;IACjC,IAAI,MAAM,EAAE;QACV,OAAO,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;KACrE;SAAM;QACL,MAAM,UAAU,GAAG,MAAM,aAAa,EAAE,CAAC;QACzC,IAAI,UAAU,EAAE;YACd,OAAO,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;SAC7E;aAAM;YACL,OAAO,iBAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;SAC5D;KACF;AACH,CAAC;AAjBD,oCAiBC"}