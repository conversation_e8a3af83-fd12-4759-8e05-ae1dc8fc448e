#!/usr/bin/env python3
"""
IP轮换器测试脚本
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.utils.ip.ip_manager import IPManager

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    try:
        ip_manager = IPManager()
        print(f"✓ 配置加载成功")
        print(f"✓ 代理节点数量: {len(ip_manager.proxy_list)}")
        
        if ip_manager.proxy_list:
            print(f"✓ 第一个节点: {ip_manager.proxy_list[0]['name']}")
        
        return True
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False

def test_proxy_connection():
    """测试代理连接"""
    print("\n=== 测试代理连接 ===")
    try:
        ip_manager = IPManager()
        
        # 测试前3个代理
        test_count = min(3, len(ip_manager.proxy_list))
        success_count = 0
        
        for i in range(test_count):
            proxy = ip_manager.proxy_list[i]
            print(f"测试代理 {i+1}: {proxy['name']}")
            
            if ip_manager._test_proxy_connection(proxy):
                print(f"  ✓ 连接成功")
                success_count += 1
            else:
                print(f"  ✗ 连接失败")
        
        print(f"\n连接测试结果: {success_count}/{test_count} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"✗ 代理连接测试失败: {e}")
        return False

def test_ip_detection():
    """测试IP检测"""
    print("\n=== 测试IP检测 ===")
    try:
        ip_manager = IPManager()
        current_ip = ip_manager.get_current_ip()
        
        if current_ip:
            print(f"✓ 当前IP: {current_ip}")
            return True
        else:
            print("✗ 无法获取当前IP")
            return False
            
    except Exception as e:
        print(f"✗ IP检测失败: {e}")
        return False

def test_proxy_switch():
    """测试代理切换"""
    print("\n=== 测试代理切换 ===")
    try:
        ip_manager = IPManager()
        
        # 获取初始IP
        initial_ip = ip_manager.get_current_ip()
        print(f"初始IP: {initial_ip}")
        
        # 尝试切换代理
        if ip_manager.switch_proxy():
            print(f"✓ 代理切换成功")
            print(f"✓ 当前代理: {ip_manager.current_proxy['name']}")
            
            # 等待一下再检测IP
            time.sleep(3)
            new_ip = ip_manager.get_current_ip()
            print(f"新IP: {new_ip}")
            
            if new_ip and new_ip != initial_ip:
                print("✓ IP地址已改变")
                return True
            else:
                print("⚠ IP地址未改变（可能是代理未生效）")
                return True  # 切换成功但IP未变也算部分成功
        else:
            print("✗ 代理切换失败")
            return False
            
    except Exception as e:
        print(f"✗ 代理切换测试失败: {e}")
        return False

def test_status():
    """测试状态获取"""
    print("\n=== 测试状态获取 ===")
    try:
        ip_manager = IPManager()
        status = ip_manager.get_status()
        
        print("状态信息:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        print("✓ 状态获取成功")
        return True
        
    except Exception as e:
        print(f"✗ 状态获取失败: {e}")
        return False

def main():
    """主测试函数"""
    print("IP轮换器功能测试")
    print("=" * 50)
    
    setup_logging()
    
    tests = [
        ("配置加载", test_config_loading),
        ("IP检测", test_ip_detection),
        ("代理连接", test_proxy_connection),
        ("状态获取", test_status),
        ("代理切换", test_proxy_switch),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！IP轮换器功能正常")
        return 0
    elif passed > 0:
        print("⚠ 部分测试通过，请检查失败的功能")
        return 1
    else:
        print("❌ 所有测试失败，请检查配置和网络连接")
        return 2

if __name__ == '__main__':
    sys.exit(main())
