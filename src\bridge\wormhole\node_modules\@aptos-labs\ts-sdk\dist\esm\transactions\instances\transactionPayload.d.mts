import { Deserializer } from '../../bcs/deserializer.mjs';
import { Serializable, Serializer } from '../../bcs/serializer.mjs';
import { AccountAddress } from '../../core/accountAddress.mjs';
import { MoveModuleId } from '../../types/types.mjs';
import { TransactionArgument, ScriptFunctionArgument, EntryFunctionArgument } from './transactionArgument.mjs';
import { Identifier } from './identifier.mjs';
import { ModuleId } from './moduleId.mjs';
import { TypeTag } from '../typeTag/index.mjs';
import '../../types/indexer.mjs';
import '../../types/generated/operations.mjs';
import '../../types/generated/types.mjs';
import '../../utils/apiEndpoints.mjs';
import '../../core/hex.mjs';
import '../../core/common.mjs';

/**
 * Deserialize a Script Transaction Argument.
 * This function retrieves and deserializes various types of script transaction arguments based on the provided deserializer.
 *
 * @param deserializer - The deserializer used to read the script transaction argument.
 * @returns The deserialized script transaction argument.
 * @throws Error if the variant index is unknown.
 * @group Implementation
 * @category Transactions
 */
declare function deserializeFromScriptArgument(deserializer: Deserializer): TransactionArgument;
/**
 * Represents a supported Transaction Payload that can be serialized and deserialized.
 *
 * This class serves as a base for different types of transaction payloads, allowing for
 * their serialization into a format suitable for transmission and deserialization back
 * into their original form.
 * @group Implementation
 * @category Transactions
 */
declare abstract class TransactionPayload extends Serializable {
    /**
     * Serialize a Transaction Payload
     * @group Implementation
     * @category Transactions
     */
    abstract serialize(serializer: Serializer): void;
    /**
     * Deserialize a Transaction Payload
     * @group Implementation
     * @category Transactions
     */
    /**
     * Deserializes a multisig transaction payload from the provided deserializer.
     * This function enables the reconstruction of a MultiSigTransactionPayload object from its serialized form.
     *
     * @param deserializer - The deserializer instance used to read the serialized data.
     * @group Implementation
     * @category Transactions
     */
    static deserialize(deserializer: Deserializer): TransactionPayload;
}
/**
 * Represents a transaction payload script that can be serialized and deserialized.
 *
 * This class encapsulates a script that defines the logic for a transaction payload.
 *
 * @extends TransactionPayload
 * @group Implementation
 * @category Transactions
 */
declare class TransactionPayloadScript extends TransactionPayload {
    readonly script: Script;
    /**
     * Initializes a multi-sig account transaction with the provided payload.
     *
     * @param script - The payload of the multi-sig transaction. This can only be an EntryFunction for now, but Script might be
     * supported in the future.
     * @group Implementation
     * @category Transactions
     */
    constructor(script: Script);
    /**
     * Serializes the transaction payload, enabling future support for multiple types of inner transaction payloads.
     *
     * @param serializer - The serializer instance used to serialize the transaction data.
     * @group Implementation
     * @category Transactions
     */
    serialize(serializer: Serializer): void;
    /**
     * Loads a MultiSig transaction payload from the provided deserializer.
     * This function helps in reconstructing a MultiSig transaction payload from its serialized form.
     *
     * @param deserializer - The deserializer used to read the serialized data.
     * @group Implementation
     * @category Transactions
     */
    static load(deserializer: Deserializer): TransactionPayloadScript;
}
/**
 * Represents a transaction payload entry function that can be serialized and deserialized.
 *
 * @extends TransactionPayload
 * @group Implementation
 * @category Transactions
 */
declare class TransactionPayloadEntryFunction extends TransactionPayload {
    readonly entryFunction: EntryFunction;
    constructor(entryFunction: EntryFunction);
    serialize(serializer: Serializer): void;
    static load(deserializer: Deserializer): TransactionPayloadEntryFunction;
}
/**
 * Represents a multi-signature transaction payload that can be serialized and deserialized.
 * @group Implementation
 * @category Transactions
 */
declare class TransactionPayloadMultiSig extends TransactionPayload {
    readonly multiSig: MultiSig;
    constructor(multiSig: MultiSig);
    serialize(serializer: Serializer): void;
    static load(deserializer: Deserializer): TransactionPayloadMultiSig;
}
/**
 * Represents an entry function that can be serialized and deserialized.
 * This class encapsulates the details required to invoke a function within a module,
 * including the module name, function name, type arguments, and function arguments.
 *
 * @param module_name - Fully qualified module name in the format "account_address::module_name" (e.g., "0x1::coin").
 * @param function_name - The name of the function (e.g., "transfer").
 * @param type_args - Type arguments required by the Move function.
 * @param args - Arguments to the Move function.
 * @group Implementation
 * @category Transactions
 */
declare class EntryFunction {
    readonly module_name: ModuleId;
    readonly function_name: Identifier;
    readonly type_args: Array<TypeTag>;
    readonly args: Array<EntryFunctionArgument>;
    /**
     * Contains the payload to run a function within a module.
     * @param module_name Fully qualified module name in format "account_address::module_name" e.g. "0x1::coin"
     * @param function_name The function name. e.g "transfer"
     * @param type_args Type arguments that move function requires.
     *
     * @example
     * A coin transfer function has one type argument "CoinType".
     * ```
     * public entry fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     * @param args arguments to the move function.
     *
     * @example
     * A coin transfer function has three arguments "from", "to" and "amount".
     * ```
     * public entry fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     * @group Implementation
     * @category Transactions
     */
    constructor(module_name: ModuleId, function_name: Identifier, type_args: Array<TypeTag>, args: Array<EntryFunctionArgument>);
    /**
     * Build an EntryFunction payload from raw primitive values.
     *
     * @param module_id - Fully qualified module name in the format "AccountAddress::module_id", e.g., "0x1::coin".
     * @param function_name - The name of the function to be called.
     * @param type_args - Type arguments that the Move function requires.
     * @param args - Arguments to the Move function.
     *
     * @example
     * A coin transfer function has one type argument "CoinType".
     * ```
     * public(script) fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     *
     * A coin transfer function has three arguments "from", "to", and "amount".
     * ```
     * public(script) fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     *
     * @returns EntryFunction
     * @group Implementation
     * @category Transactions
     */
    static build(module_id: MoveModuleId, function_name: string, type_args: Array<TypeTag>, args: Array<EntryFunctionArgument>): EntryFunction;
    serialize(serializer: Serializer): void;
    /**
     * Deserializes an entry function payload with the arguments represented as EntryFunctionBytes instances.
     * @see EntryFunctionBytes
     *
     * NOTE: When you deserialize an EntryFunction payload with this method, the entry function
     * arguments are populated into the deserialized instance as type-agnostic, raw fixed bytes
     * in the form of the EntryFunctionBytes class.
     *
     * In order to correctly deserialize these arguments as their actual type representations, you
     * must know the types of the arguments beforehand and deserialize them yourself individually.
     *
     * One way you could achieve this is by using the ABIs for an entry function and deserializing each
     * argument as its given, corresponding type.
     *
     * @param deserializer
     * @returns A deserialized EntryFunction payload for a transaction.
     *
     * @group Implementation
     * @category Transactions
     */
    static deserialize(deserializer: Deserializer): EntryFunction;
}
/**
 * Represents a Script that can be serialized and deserialized.
 * Scripts contain the Move bytecode payload that can be submitted to the Aptos chain for execution.
 * @group Implementation
 * @category Transactions
 */
declare class Script {
    /**
     * The move module bytecode
     * @group Implementation
     * @category Transactions
     */
    readonly bytecode: Uint8Array;
    /**
     * The type arguments that the bytecode function requires.
     * @group Implementation
     * @category Transactions
     */
    readonly type_args: Array<TypeTag>;
    /**
     * The arguments that the bytecode function requires.
     * @group Implementation
     * @category Transactions
     */
    readonly args: Array<ScriptFunctionArgument>;
    /**
     * Scripts contain the Move bytecodes payload that can be submitted to Aptos chain for execution.
     *
     * @param bytecode The move module bytecode
     * @param type_args The type arguments that the bytecode function requires.
     *
     * @example
     * A coin transfer function has one type argument "CoinType".
     * ```
     * public(script) fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     * @param args The arguments that the bytecode function requires.
     *
     * @example
     * A coin transfer function has three arguments "from", "to" and "amount".
     * ```
     * public(script) fun transfer<CoinType>(from: &signer, to: address, amount: u64)
     * ```
     * @group Implementation
     * @category Transactions
     */
    constructor(bytecode: Uint8Array, type_args: Array<TypeTag>, args: Array<ScriptFunctionArgument>);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): Script;
}
/**
 * Represents a MultiSig account that can be serialized and deserialized.
 *
 * This class encapsulates the functionality to manage multi-signature transactions, including the address of the
 * multi-sig account and the associated transaction payload.
 * @group Implementation
 * @category Transactions
 */
declare class MultiSig {
    readonly multisig_address: AccountAddress;
    readonly transaction_payload?: MultiSigTransactionPayload;
    /**
     * Contains the payload to run a multi-sig account transaction.
     *
     * @param multisig_address The multi-sig account address the transaction will be executed as.
     *
     * @param transaction_payload The payload of the multi-sig transaction. This is optional when executing a multi-sig
     *  transaction whose payload is already stored on chain.
     * @group Implementation
     * @category Transactions
     */
    constructor(multisig_address: AccountAddress, transaction_payload?: MultiSigTransactionPayload);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): MultiSig;
}
/**
 * Represents a multi-signature transaction payload that can be serialized and deserialized.
 * This class is designed to encapsulate the transaction payload for multi-sig account transactions
 * as defined in the `multisig_account.move` module. Future enhancements may allow support for script
 * payloads as the `multisig_account.move` module evolves.
 * @group Implementation
 * @category Transactions
 */
declare class MultiSigTransactionPayload extends Serializable {
    readonly transaction_payload: EntryFunction;
    /**
     * Contains the payload to run a multi-sig account transaction.
     *
     * @param transaction_payload The payload of the multi-sig transaction.
     * This can only be EntryFunction for now but,
     * Script might be supported in the future.
     * @group Implementation
     * @category Transactions
     */
    constructor(transaction_payload: EntryFunction);
    serialize(serializer: Serializer): void;
    static deserialize(deserializer: Deserializer): MultiSigTransactionPayload;
}

export { EntryFunction, MultiSig, MultiSigTransactionPayload, Script, TransactionPayload, TransactionPayloadEntryFunction, TransactionPayloadMultiSig, TransactionPayloadScript, deserializeFromScriptArgument };
