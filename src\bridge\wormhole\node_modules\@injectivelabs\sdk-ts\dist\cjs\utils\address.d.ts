/**
 * Get injective address from Ethereum hex address
 *
 * @param ethAddress string
 * @returns string
 */
export declare const getInjectiveAddress: (ethAddress: string) => string;
/**
 * Get ethereum address from injective bech32 address
 *
 * @param injectiveAddress string
 * @returns string
 */
export declare const getEthereumAddress: (injectiveAddress: string) => string;
/**
 * Get ethereum address from injective bech32 address
 *
 * @param injectiveAddress string
 * @returns string
 */
export declare const getInjectiveAddressFromSubaccountId: (subaccountId: string) => string;
/**
 * Get default subaccount id from injective bech32 address
 *
 * @param injectiveAddress string
 * @returns string
 */
export declare const getDefaultSubaccountId: (injectiveAddress: string) => string;
/**
 * Get subaccount id from injective bech32 address and an index (defaults to 0)
 * @param injectiveAddress string
 * @param nonce number
 * @returns string
 */
export declare const getSubaccountId: (injectiveAddress: string, nonce?: number) => string;
/** @deprecated - use getEthereumAddress */
export declare const getAddressFromInjectiveAddress: (address: string) => string;
export declare const getChecksumAddress: (ethAddress: string) => string;
export declare const isCw20ContractAddress: (address: string) => boolean;
export declare const addHexPrefix: (hex: string) => string;
export declare const removeHexPrefix: (hex: string) => string;
