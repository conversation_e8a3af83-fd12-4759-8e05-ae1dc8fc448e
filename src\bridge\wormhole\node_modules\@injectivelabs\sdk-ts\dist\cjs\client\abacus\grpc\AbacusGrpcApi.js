"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbacusGrpcApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const abacus_proto_ts_1 = require("@injectivelabs/abacus-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("./transformers/index.js");
class AbacusGrpcApi extends BaseGrpcConsumer_js_1.default {
    module = exceptions_1.IndexerErrorModule.Abacus;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new abacus_proto_ts_1.InjectiveAbacusRpc.PointsSvcClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchAccountLatestPoints(address) {
        const request = abacus_proto_ts_1.InjectiveAbacusRpc.PointsLatestForAccountRequest.create();
        request.accountAddress = address;
        try {
            const response = await this.retry(() => this.client.PointsLatestForAccount(request));
            return index_js_1.AbacusGrpcTransformer.grpcPointsLatestToPointsLatest(response);
        }
        catch (e) {
            if (e instanceof abacus_proto_ts_1.InjectiveAbacusRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PointsStatsLatestForAccount',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PointsStatsLatestForAccount',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountDailyPoints(address, daysLimit) {
        const request = abacus_proto_ts_1.InjectiveAbacusRpc.PointsStatsDailyForAccountRequest.create();
        request.accountAddress = address;
        if (daysLimit) {
            request.daysLimit = daysLimit.toString();
        }
        try {
            const response = await this.retry(() => this.client.PointsStatsDailyForAccount(request));
            return index_js_1.AbacusGrpcTransformer.grpcPointsStatsDailyToPointsStatsDaily(response);
        }
        catch (e) {
            if (e instanceof abacus_proto_ts_1.InjectiveAbacusRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PointsStatsDailyForAccount',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PointsStatsDailyForAccount',
                contextModule: this.module,
            });
        }
    }
    async fetchAccountWeeklyPoints(address, weeksLimit) {
        const request = abacus_proto_ts_1.InjectiveAbacusRpc.PointsStatsWeeklyForAccountRequest.create();
        request.accountAddress = address;
        if (weeksLimit) {
            request.weeksLimit = weeksLimit.toString();
        }
        try {
            const response = await this.retry(() => this.client.PointsStatsWeeklyForAccount(request));
            return index_js_1.AbacusGrpcTransformer.grpcPointsStatsWeeklyToPointsStatsWeekly(response);
        }
        catch (e) {
            if (e instanceof abacus_proto_ts_1.InjectiveAbacusRpc.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PointsStatsWeeklyForAccount',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PointsStatsWeeklyForAccount',
                contextModule: this.module,
            });
        }
    }
}
exports.AbacusGrpcApi = AbacusGrpcApi;
