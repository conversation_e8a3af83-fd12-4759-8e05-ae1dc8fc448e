# RPC 故障转移机制实现总结

## 问题描述

在使用桥接脚本时，即使配置文件中设置了多个备用 RPC 节点，但当主要 RPC（如 Infura Polygon）连接失败时，系统没有自动切换到备用 RPC，而是直接返回错误。

## 根本原因

### 1. Infura Polygon RPC 问题
- **错误表现**：`KeyError: 'jsonrpc'`
- **原因**：Infura 的 Polygon RPC 返回的响应格式不符合 JSON-RPC 2.0 规范
- **影响**：导致 Web3 连接测试失败

### 2. 缺少故障转移机制
- **原有逻辑**：`get_network_config()` 只获取主要 RPC URL
- **问题**：没有实现 RPC 节点的健康检查和自动切换

## 解决方案

### 1. 实现 RPC 连接测试函数

```python
def test_rpc_connection(rpc_url: str, is_polygon: bool = False) -> bool:
    """
    测试 RPC 连接是否正常
    
    Args:
        rpc_url: RPC URL
        is_polygon: 是否为 Polygon 网络
        
    Returns:
        bool: 连接是否正常
    """
    try:
        web3 = Web3(Web3.HTTPProvider(rpc_url))
        
        # 如果是 Polygon 网络，添加 PoA 中间件
        if is_polygon:
            from web3.middleware import geth_poa_middleware
            web3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # 测试连接并检查响应格式
        if web3.is_connected():
            # 额外检查：尝试获取区块号以确保响应格式正确
            block_number = web3.eth.block_number
            if block_number > 0:
                return True
        return False
    except Exception as e:
        print(f"RPC连接测试失败 {rpc_url}: {str(e)}")
        return False
```

### 2. 实现 RPC 故障转移逻辑

```python
def get_working_rpc(rpc_config: dict, network_name: str) -> str:
    """
    获取可用的 RPC URL
    
    Args:
        rpc_config: RPC 配置
        network_name: 网络名称
        
    Returns:
        str: 可用的 RPC URL
    """
    is_polygon = network_name.lower() == "polygon"
    
    # 首先尝试主要的 RPC URL
    main_rpc = rpc_config.get("rpc_url", "")
    if main_rpc:
        print(f"测试 {network_name} 主要 RPC: {main_rpc}")
        if test_rpc_connection(main_rpc, is_polygon):
            print(f"✅ {network_name} 主要 RPC 连接成功")
            return main_rpc
        else:
            print(f"❌ {network_name} 主要 RPC 连接失败")
    
    # 如果主要 RPC 失败，尝试备用 RPC
    backup_rpcs = rpc_config.get("backup_rpc_urls", [])
    if backup_rpcs:
        print(f"尝试 {network_name} 备用 RPC 节点...")
        for backup_rpc in backup_rpcs:
            print(f"测试 {network_name} 备用 RPC: {backup_rpc}")
            if test_rpc_connection(backup_rpc, is_polygon):
                print(f"✅ {network_name} 备用 RPC 连接成功: {backup_rpc}")
                return backup_rpc
            else:
                print(f"❌ {network_name} 备用 RPC 连接失败: {backup_rpc}")
    
    raise ConnectionError(f"所有 {network_name} RPC 节点都无法连接")
```

### 3. 更新网络配置获取函数

```python
def get_network_config() -> Tuple[str, str, str]:
    """
    从配置文件中获取网络配置，支持 RPC 故障转移
    
    Returns:
        Tuple[str, str, str]: 以太坊RPC、Polygon RPC、私钥
    """
    config = load_config()
    
    # 获取以太坊RPC（支持故障转移）
    eth_rpc_config = config.get("rpc", {}).get("ethereum", {})
    if not eth_rpc_config:
        raise ValueError("配置文件中未找到以太坊RPC配置")
    
    eth_rpc = get_working_rpc(eth_rpc_config, "以太坊")
    
    # 获取Polygon RPC（支持故障转移）
    polygon_rpc_config = config.get("rpc", {}).get("polygon", {})
    if not polygon_rpc_config:
        raise ValueError("配置文件中未找到Polygon RPC配置")
    
    polygon_rpc = get_working_rpc(polygon_rpc_config, "Polygon")
    
    # 获取私钥
    private_key = config.get("wallet", {}).get("private_key", "")
    if not private_key:
        raise ValueError("配置文件中未找到私钥")
    
    print("\n网络配置信息:")
    print(f"以太坊 RPC: {eth_rpc}")
    print(f"Polygon RPC: {polygon_rpc}")
    print(f"钱包地址: {Web3().eth.account.from_key(private_key).address}")
    
    return eth_rpc, polygon_rpc, private_key
```

### 4. 改进错误处理

```python
# 在 PolygonEthereumBridge.__init__ 中
try:
    if not self.ethereum_web3.is_connected():
        raise ConnectionError(f"无法连接到以太坊节点: {ethereum_rpc_url}")
except Exception as e:
    raise ConnectionError(f"以太坊节点连接测试失败: {ethereum_rpc_url}, 错误: {str(e)}")

try:
    if not self.polygon_web3.is_connected():
        raise ConnectionError(f"无法连接到Polygon节点: {polygon_rpc_url}")
except Exception as e:
    raise ConnectionError(f"Polygon节点连接测试失败: {polygon_rpc_url}, 错误: {str(e)}")
```

## 实现效果

### 修复前
```
网络配置信息:
以太坊 RPC: https://mainnet.infura.io/v3/...
Polygon RPC: https://polygon-mainnet.infura.io/v3/...  # 直接使用，连接失败
钱包地址: ******************************************
Traceback (most recent call last):
  ...
KeyError: 'jsonrpc'
```

### 修复后
```
测试 以太坊 主要 RPC: https://mainnet.infura.io/v3/...
✅ 以太坊 主要 RPC 连接成功
测试 Polygon 主要 RPC: https://polygon-mainnet.infura.io/v3/...
RPC连接测试失败 https://polygon-mainnet.infura.io/v3/...: 'jsonrpc'
❌ Polygon 主要 RPC 连接失败
尝试 Polygon 备用 RPC 节点...
测试 Polygon 备用 RPC: https://1rpc.io/matic
✅ Polygon 备用 RPC 连接成功: https://1rpc.io/matic

网络配置信息:
以太坊 RPC: https://mainnet.infura.io/v3/...
Polygon RPC: https://1rpc.io/matic  # 自动切换到可用节点
钱包地址: ******************************************
```

## 配置优化

已将配置文件中的 RPC 节点优先级调整为：

```yaml
polygon:
  backup_rpc_urls:
    - https://1rpc.io/matic                    # 稳定，优先使用
    - https://polygon-rpc.com                  # 官方推荐
    - https://polygon-mainnet.public.blastapi.io
    - https://polygon-rpc.publicnode.com
    - https://rpc-mainnet.matic.network
    - https://polygon-mainnet.infura.io/v3/... # 作为最后备用
  rpc_url: https://1rpc.io/matic
```

## 关键特性

1. **自动故障转移**：主要 RPC 失败时自动尝试备用节点
2. **详细日志**：显示每个 RPC 节点的测试结果
3. **智能检测**：不仅测试连接，还验证响应格式
4. **Polygon 优化**：为 Polygon 网络添加必要的 PoA 中间件
5. **错误透明**：显示具体的连接失败原因

## 适用场景

- **Infura 服务问题**：自动切换到其他稳定节点
- **网络不稳定**：重试不同的 RPC 提供商
- **地区限制**：使用不同地区的 RPC 节点
- **负载均衡**：分散请求到多个节点

通过这个 RPC 故障转移机制，大大提高了桥接操作的成功率和系统的稳定性，确保即使某个 RPC 提供商出现问题，系统仍能正常工作。
