import { Account } from './account.mjs';
import { AptosConfig } from './aptosConfig.mjs';
import { Coin } from './coin.mjs';
import { DigitalAsset } from './digitalAsset.mjs';
import { Event } from './event.mjs';
import { Faucet } from './faucet.mjs';
import { FungibleAsset } from './fungibleAsset.mjs';
import { General } from './general.mjs';
import { ANS } from './ans.mjs';
import { Staking } from './staking.mjs';
import { Transaction } from './transaction.mjs';
import { Table } from './table.mjs';
import { Keyless } from './keyless.mjs';
import { AptosObject } from './object.mjs';
import '../types/indexer.mjs';
import '../types/generated/operations.mjs';
import '../types/generated/types.mjs';
import '../types/types.mjs';
import '../utils/apiEndpoints.mjs';
import '../core/accountAddress.mjs';
import '../bcs/serializer.mjs';
import '../core/hex.mjs';
import '../core/common.mjs';
import '../bcs/deserializer.mjs';
import '../transactions/instances/transactionArgument.mjs';
import '../core/crypto/privateKey.mjs';
import '../publicKey-CJOcUwJK.mjs';
import '../core/crypto/signature.mjs';
import '../utils/const.mjs';
import '../Ed25519Account-D9XrCLfE.mjs';
import '../transactions/authenticator/account.mjs';
import '../core/crypto/ed25519.mjs';
import '../core/crypto/multiEd25519.mjs';
import '../core/crypto/multiKey.mjs';
import '../core/crypto/singleKey.mjs';
import '../core/crypto/secp256k1.mjs';
import '../transactions/types.mjs';
import '../bcs/serializable/moveStructs.mjs';
import '../bcs/serializable/movePrimitives.mjs';
import '../bcs/serializable/fixedBytes.mjs';
import '../transactions/instances/rawTransaction.mjs';
import '../transactions/instances/chainId.mjs';
import '../transactions/instances/transactionPayload.mjs';
import '../transactions/instances/identifier.mjs';
import '../transactions/instances/moduleId.mjs';
import '../transactions/typeTag/index.mjs';
import '../transactions/instances/simpleTransaction.mjs';
import '../transactions/instances/multiAgentTransaction.mjs';
import './account/abstraction.mjs';
import '../internal/digitalAsset.mjs';
import '../internal/ans.mjs';
import '../internal/transactionSubmission.mjs';
import './transactionSubmission/build.mjs';
import './transactionSubmission/simulate.mjs';
import './transactionSubmission/submit.mjs';
import './transactionSubmission/management.mjs';
import 'eventemitter3';
import '../transactions/management/transactionWorker.mjs';
import '../transactions/management/accountSequenceNumber.mjs';
import '../transactions/management/asyncQueue.mjs';
import '../federatedKeyless-DAYXjY2Y.mjs';
import '../core/crypto/ephemeral.mjs';
import '../core/crypto/proof.mjs';
import '../types/keyless.mjs';
import '@noble/curves/abstract/weierstrass';
import '@noble/curves/abstract/tower';
import '../account/EphemeralKeyPair.mjs';
import '../account/KeylessAccount.mjs';
import '../account/AbstractKeylessAccount.mjs';
import '../account/FederatedKeylessAccount.mjs';

/**
 * The main entry point for interacting with the Aptos APIs,
 * providing access to various functionalities organized into
 * distinct namespaces.
 *
 * To utilize the SDK, instantiate a new Aptos object to gain
 * access to the complete range of SDK features.
 *
 * @example
 * ```typescript
 * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
 *
 * async function runExample() {
 *     // Create a configuration for connecting to the Aptos testnet
 *     const config = new AptosConfig({ network: Network.TESTNET });
 *
 *     // Initialize the Aptos client with the configuration
 *     const aptos = new Aptos(config);
 *
 *     console.log("Aptos client initialized:", aptos);
 * }
 * runExample().catch(console.error);
 * ```
 * @group Client
 */
declare class Aptos {
    readonly config: AptosConfig;
    readonly account: Account;
    readonly ans: ANS;
    readonly coin: Coin;
    readonly digitalAsset: DigitalAsset;
    readonly event: Event;
    readonly faucet: Faucet;
    readonly fungibleAsset: FungibleAsset;
    readonly general: General;
    readonly staking: Staking;
    readonly transaction: Transaction;
    readonly table: Table;
    readonly keyless: Keyless;
    readonly object: AptosObject;
    /**
     * Initializes a new instance of the Aptos client with the provided configuration settings.
     * This allows you to interact with various Aptos functionalities such as accounts, transactions, and events.
     *
     * @param settings - Configuration settings for the Aptos client.
     *
     * @example
     * ```typescript
     * import { Aptos, AptosConfig, Network } from "@aptos-labs/ts-sdk";
     *
     * async function runExample() {
     *     // Create a new Aptos client with default settings
     *     const config = new AptosConfig({ network: Network.TESTNET }); // Specify your own settings if needed
     *     const aptos = new Aptos(config);
     *
     *     console.log("Aptos client initialized:", aptos);
     * }
     * runExample().catch(console.error);
     * ```
     * @group Client
     */
    constructor(settings?: AptosConfig);
}
interface Aptos extends Account, ANS, Coin, DigitalAsset, Event, Faucet, FungibleAsset, General, Keyless, Staking, Table, AptosObject, Omit<Transaction, "build" | "simulate" | "submit" | "batch"> {
}

export { Aptos };
