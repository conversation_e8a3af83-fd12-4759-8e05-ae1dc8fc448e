#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time
import os
import sys
import argparse
import logging
import yaml
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("extract_pools.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 获取脚本所在目录
SCRIPT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
# 项目根目录
PROJECT_ROOT = SCRIPT_DIR.parent.parent
# 输出文件路径 (修改为新的路径和文件名)
OUTPUT_DIR = PROJECT_ROOT / "data" / "utils" / "token"
OUTPUT_FILE = OUTPUT_DIR / "all_pool_addresses_geckoterminal.txt"
# 配置文件路径
CONFIG_FILE = PROJECT_ROOT / "config" / "config.yaml"
# 请求URL基础部分
BASE_URL = "https://app.geckoterminal.com/api/p1/{network}/pools?&liquidity%5Bgte%5D=100"
# 最大重试次数
MAX_RETRIES = 5
# 用户代理
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36"

# 支持的网络列表
SUPPORTED_NETWORKS = {
    "eth": "以太坊",
    "arbitrum": "Arbitrum",
    "optimism": "Optimism",
    "zksync": "zkSync Era",
    "polygon_pos": "Polygon PoS",
    "base": "Base",
    "sonic": "sonic",
    "bsc": "币安智能链",
    "linea": "linea",
    "unichain": "unichain",
    "mantle": "mantle",
    "berachain": "berachain",
    "ronin": "ronin",
    "avax": "Avalanche",
    "ftm": "Fantom",
    "mode": "mode",
    "manta": "Manta",   
}

# 主要网络列表
MAIN_NETWORKS = ["eth", "polygon_pos", "bsc", "arbitrum", "optimism", "base", "avax"]

def load_config():
    """从配置文件加载配置"""
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"读取配置文件时出错: {e}")
        return {}

def get_proxy_from_config(config):
    """从配置中获取代理设置"""
    if not config or 'proxy' not in config or not config['proxy'].get('enabled', False):
        return None
    
    proxies = {}
    if 'http' in config['proxy']:
        proxies['http'] = config['proxy']['http']
    if 'https' in config['proxy']:
        proxies['https'] = config['proxy']['https']
    
    return proxies if proxies else None

def ensure_output_directory():
    """确保输出目录存在"""
    if not OUTPUT_DIR.exists():
        try:
            OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
            logger.info(f"已创建输出目录: {OUTPUT_DIR}")
        except Exception as e:
            logger.error(f"创建输出目录时出错: {e}")
            return False
    return True

def get_network_section(network):
    """获取网络在文件中的标记"""
    return f"{SUPPORTED_NETWORKS.get(network, network)}网络："

def check_network_section_exists(network):
    """检查输出文件中是否已存在特定网络的部分"""
    if not OUTPUT_FILE.exists():
        return False
    
    network_section = get_network_section(network)
    try:
        with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
            return network_section in content
    except Exception as e:
        logger.error(f"检查网络部分时出错: {e}")
        return False

def write_network_section(network):
    """向文件中写入网络标记"""
    network_section = get_network_section(network)
    
    # 检查文件是否存在
    if not OUTPUT_FILE.exists():
        try:
            with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
                f.write(f"{network_section}\n")
            logger.info(f"已创建输出文件并添加{network_section}")
            return True
        except Exception as e:
            logger.error(f"创建文件并写入网络标记时出错: {e}")
            return False
    
    # 检查是否已存在该网络标记
    if check_network_section_exists(network):
        logger.info(f"文件中已存在{network_section}，将继续添加地址")
        return True
    
    # 添加新的网络标记
    try:
        with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
            f.write(f"\n{network_section}\n")
        logger.info(f"已向文件添加{network_section}")
        return True
    except Exception as e:
        logger.error(f"添加网络标记时出错: {e}")
        return False

def read_existing_addresses(network):
    """读取已存在的地址列表，避免重复添加"""
    if not OUTPUT_FILE.exists():
        return set()
    
    try:
        with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        network_section = get_network_section(network)
        existing_addresses = set()
        in_target_section = False
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.endswith("网络："):
                in_target_section = (line == network_section)
                continue
                
            if in_target_section and line.startswith("0x"):
                existing_addresses.add(line)
        
        logger.info(f"从文件中读取到 {len(existing_addresses)} 个已存在的 {network} 地址")
        return existing_addresses
    except Exception as e:
        logger.error(f"读取已存在地址时出错: {e}")
        return set()

def append_address(address, network, existing_addresses):
    """追加地址到文件"""
    if address in existing_addresses:
        return False  # 地址已存在
        
    try:
        with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{address}\n")
        
        # 验证地址是否确实被写入
        try:
            with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
                content = f.read()
                if address in content:
                    return True
                else:
                    logger.warning(f"地址 {address} 可能未成功写入文件")
                    return False
        except Exception as e:
            logger.error(f"验证地址写入时出错: {e}")
            return False
    except Exception as e:
        logger.error(f"保存地址时出错: {e}")
        return False

def fetch_pool_addresses(network, page_number, proxies=None):
    """
    获取指定网络和页码的池子地址
    
    Args:
        network: 网络名称
        page_number: 页码
        proxies: 代理设置
        
    Returns:
        addresses: 获取到的地址列表
        has_next: 是否有下一页
    """
    addresses = []
    url = BASE_URL.format(network=network) + f"?&page={page_number}"
    
    logger.info(f"正在请求 {network} 网络的第 {page_number} 页...")
    
    headers = {
        "User-Agent": USER_AGENT,
        "Accept": "application/json",
        "Referer": "https://app.geckoterminal.com/"
    }
    
    for retry in range(MAX_RETRIES):
        try:
            response = requests.get(url, headers=headers, proxies=proxies, timeout=30)
            response.raise_for_status()  # 如果状态码不是200，引发异常
            
            data = response.json()
            
            # 检查是否有数据
            if "data" not in data or not data["data"]:
                logger.info(f"没有更多数据，到达 {network} 网络的最后一页: {page_number}")
                return addresses, False
                
            # 提取api_address
            for item in data["data"]:
                if "attributes" in item and "api_address" in item["attributes"]:
                    addresses.append(item["attributes"]["api_address"])
            
            # 检查是否有下一页 - 更可靠的方法
            has_next = False
            if "links" in data and "next" in data["links"]:
                next_link = data["links"]["next"]
                if next_link and isinstance(next_link, str) and "page=" in next_link:
                    has_next = True
            
            return addresses, has_next
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求 {network} 网络的第 {page_number} 页时出错: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法获取 {network} 网络的第 {page_number} 页")
                return addresses, False
        except json.JSONDecodeError as e:
            logger.error(f"解析 {network} 网络的第 {page_number} 页响应JSON时出错: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法解析 {network} 网络的第 {page_number} 页响应")
                return addresses, False
        except Exception as e:
            logger.error(f"处理 {network} 网络的第 {page_number} 页时发生未知错误: {e}")
            if retry < MAX_RETRIES - 1:
                logger.info(f"将进行第 {retry + 2} 次重试...")
            else:
                logger.error(f"已达到最大重试次数 ({MAX_RETRIES})，无法处理 {network} 网络的第 {page_number} 页")
                return addresses, False
    
    return addresses, False

def extract_pool_addresses(network, proxies=None):
    """提取指定网络的池子地址"""
    logger.info(f"开始提取 {network} 网络的池子地址")
    
    # 确保输出目录存在
    if not ensure_output_directory():
        logger.error("无法创建输出目录，脚本退出")
        return 0
        
    # 写入网络标记
    if not write_network_section(network):
        logger.error(f"无法写入 {network} 网络标记，跳过该网络")
        return 0
    
    # 读取已存在的地址
    existing_addresses = read_existing_addresses(network)
    
    page_number = 1
    total_addresses = 0
    total_new_addresses = 0
    
    while True:
        addresses, has_next = fetch_pool_addresses(network, page_number, proxies)
        
        if addresses:
            logger.info(f"从 {network} 网络的第 {page_number} 页获取了 {len(addresses)} 个地址")
            
            # 保存获取到的地址
            new_addresses = 0
            for address in addresses:
                if append_address(address, network, existing_addresses):
                    existing_addresses.add(address)  # 更新已存在地址集合
                    total_addresses += 1
                    new_addresses += 1
                    
            total_new_addresses += new_addresses
            logger.info(f"已保存 {new_addresses} 个新地址（跳过 {len(addresses) - new_addresses} 个重复地址）")
        else:
            logger.info(f"{network} 网络的第 {page_number} 页没有获取到地址")
        
        if not has_next or not addresses:
            logger.info(f"已到达 {network} 网络的最后一页或无更多数据")
            break
            
        # 使用代理时不需要等待
        page_number += 1
    
    logger.info(f"{network} 网络总共添加了 {total_new_addresses} 个新地址，总计 {total_addresses} 个地址")
    return total_new_addresses

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="从GeckoTerminal提取不同网络的池子地址")
    
    # 添加网络参数
    for network_code, network_name in SUPPORTED_NETWORKS.items():
        parser.add_argument(
            f"--{network_code}", 
            action="store_true", 
            help=f"提取{network_name}网络的池子地址"
        )
    
    # 添加自定义网络参数
    parser.add_argument(
        "--network", 
        type=str, 
        help="提取自定义网络的池子地址，使用GeckoTerminal API中的网络标识符"
    )
    
    # 添加所有网络参数
    parser.add_argument(
        "--all", "-a",
        action="store_true",
        help="提取所有支持的网络的池子地址"
    )
    
    # 添加主要网络参数
    parser.add_argument(
        "--main", "-m",
        action="store_true",
        help=f"提取主要网络的池子地址 ({', '.join(MAIN_NETWORKS)})"
    )
    
    # 添加不使用代理参数
    parser.add_argument(
        "--no-proxy",
        action="store_true",
        help="不使用配置文件中的代理设置"
    )
    
    # 添加重试次数参数
    parser.add_argument(
        "--retries", 
        type=int, 
        default=MAX_RETRIES, 
        help=f"最大重试次数，默认 {MAX_RETRIES}次"
    )
    
    # 解析参数
    args = parser.parse_args()
    
    # 检查是否指定了任何网络
    networks_specified = any(getattr(args, network, False) for network in SUPPORTED_NETWORKS) or args.network or args.all or args.main
    
    # 输出帮助信息，如果没有指定任何网络
    if not networks_specified:
        parser.print_help()
        logger.error("错误: 必须指定至少一个网络")
        sys.exit(1)
    
    return args

def main():
    """主函数"""
    start_time = datetime.now()
    logger.info(f"脚本开始执行时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"输出文件将保存到: {OUTPUT_FILE}")
    
    # 检查文件和目录权限
    if not ensure_output_directory():
        logger.error("无法创建或访问输出目录，脚本退出")
        return
    
    # 解析命令行参数
    args = parse_args()
    
    # 设置全局参数
    global MAX_RETRIES
    MAX_RETRIES = args.retries
    
    # 加载配置和代理设置
    config = load_config()
    proxies = None if args.no_proxy else get_proxy_from_config(config)
    
    if proxies:
        logger.info(f"使用代理: {proxies}")
    else:
        logger.warning("不使用代理")
    
    networks_to_process = []
    
    # 如果指定了--all参数，处理所有支持的网络
    if args.all:
        networks_to_process = list(SUPPORTED_NETWORKS.keys())
        logger.info(f"将处理所有 {len(networks_to_process)} 个支持的网络")
    # 如果指定了--main参数，处理主要网络
    elif args.main:
        networks_to_process = MAIN_NETWORKS.copy()
        logger.info(f"将处理 {len(networks_to_process)} 个主要网络: {', '.join(networks_to_process)}")
    else:
        # 检查预定义网络
        for network in SUPPORTED_NETWORKS:
            if getattr(args, network, False):
                networks_to_process.append(network)
        
        # 检查自定义网络
        if args.network:
            networks_to_process.append(args.network)
    
    # 提取每个网络的池子地址
    total_results = {}
    
    try:
        for network in networks_to_process:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始处理 {network} 网络")
            logger.info(f"{'='*50}")
            
            start_time_network = datetime.now()
            addresses_count = extract_pool_addresses(network, proxies)
            end_time_network = datetime.now()
            duration = (end_time_network - start_time_network).total_seconds()
            
            total_results[network] = {
                "count": addresses_count,
                "duration": duration
            }
            
            logger.info(f"\n完成 {network} 网络的池子地址提取，共获取 {addresses_count} 个地址")
            logger.info(f"用时: {duration:.2f} 秒\n")
    
    except KeyboardInterrupt:
        logger.warning("\n用户中断，停止获取")
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
    
    # 输出总结
    logger.info("\n" + "="*50)
    logger.info("任务完成！总结:")
    logger.info("="*50)
    
    for network, result in total_results.items():
        logger.info(f"{network} 网络: 提取了 {result['count']} 个地址，用时 {result['duration']:.2f} 秒")
    
    logger.info(f"\n所有地址已保存到: {OUTPUT_FILE}")
    end_time = datetime.now()
    total_duration = (end_time - start_time).total_seconds()
    logger.info(f"脚本结束执行时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"总用时: {total_duration:.2f} 秒")

if __name__ == "__main__":
    main() 