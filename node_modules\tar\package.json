{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "name": "tar", "description": "tar for node", "version": "4.4.19", "publishConfig": {"tag": "v4-legacy"}, "repository": {"type": "git", "url": "https://github.com/npm/node-tar.git"}, "scripts": {"test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "test": "node test/fixtures/test.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "genparse": "node scripts/generate-parse-fixtures.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done"}, "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.4", "events-to-array": "^1.1.2", "mutate-fs": "^2.1.1", "require-inject": "^1.4.4", "rimraf": "^2.7.1", "tap": "^14.11.0", "tar-fs": "^1.16.3", "tar-stream": "^1.6.2"}, "license": "ISC", "engines": {"node": ">=4.5"}, "files": ["index.js", "lib/"], "tap": {"coverage-map": "map.js", "check-coverage": true}}