"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChainGrpcPermissionsApi = void 0;
const exceptions_1 = require("@injectivelabs/exceptions");
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
const BaseGrpcConsumer_js_1 = __importDefault(require("../../base/BaseGrpcConsumer.js"));
const index_js_1 = require("../transformers/index.js");
const index_js_2 = require("../types/index.js");
/**
 * @category Chain Grpc API
 */
class ChainGrpcPermissionsApi extends BaseGrpcConsumer_js_1.default {
    module = index_js_2.ChainModule.Permissions;
    client;
    constructor(endpoint) {
        super(endpoint);
        this.client = new core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryClientImpl(this.getGrpcWebImpl(endpoint));
    }
    async fetchModuleParams() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryParamsRequest.create();
        try {
            const response = await this.retry(() => this.client.Params(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.moduleParamsResponseToModuleParams(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Params',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Params',
                contextModule: this.module
            });
        }
    }
    async fetchNamespaceDenoms() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryNamespaceDenomsRequest.create();
        try {
            const response = await this.retry(() => this.client.NamespaceDenoms(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.nameSpaceDenomsResponseToNameSpaceDenoms(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'NamespaceByDenoms',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'NamespaceDenoms',
                contextModule: this.module
            });
        }
    }
    async fetchNamespaces() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryNamespacesRequest.create();
        try {
            const response = await this.retry(() => this.client.Namespaces(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.namespacesResponseToNamespaces(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Namespaces',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Namespaces',
                contextModule: this.module
            });
        }
    }
    async fetchNamespace(denom) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryNamespaceRequest.create();
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.Namespace(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.namespaceResponseToNamespaces(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Namespace',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Namespace',
                contextModule: this.module
            });
        }
    }
    async fetchActorsByRole({ denom, role }) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryActorsByRoleRequest.create();
        request.denom = denom;
        request.role = role;
        try {
            const response = await this.retry(() => this.client.ActorsByRole(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.actorsByRoleResponseToActorsByRole(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'ActorsByRole',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'ActorsByRole',
                contextModule: this.module
            });
        }
    }
    async fetchRolesByActor({ actor, denom }) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryRolesByActorRequest.create();
        request.actor = actor;
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.RolesByActor(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.rolesByActorResponseToRolesByActor(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'RolesByActor',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'RolesByActor',
                contextModule: this.module
            });
        }
    }
    async fetchRoleManager({ denom, manager, }) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryRoleManagerRequest.create();
        request.denom = denom;
        request.manager = manager;
        try {
            const response = await this.retry(() => this.client.RoleManager(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.roleManagerResponseToRoleManager(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'RoleManager',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'RoleManager',
                contextModule: this.module
            });
        }
    }
    async fetchRoleManagers() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryRoleManagersRequest.create();
        try {
            const response = await this.retry(() => this.client.RoleManagers(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.roleManagersResponseToRoleManagers(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'RoleManagers',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'RoleManagers',
                contextModule: this.module
            });
        }
    }
    async fetchPolicyStatuses() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryPolicyStatusesRequest.create();
        try {
            const response = await this.retry(() => this.client.PolicyStatuses(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.policyStatusesResponseToPolicyStatuses(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PolicyStatuses',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PolicyStatuses',
            });
        }
    }
    async fetchPolicyManagerCapabilities(denom) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryPolicyManagerCapabilitiesRequest.create();
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.PolicyManagerCapabilities(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.policyManagerCapabilitiesResponseToPolicyManagerCapabilities(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'PolicyManagerCapabilities',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'PolicyManagerCapabilities',
                contextModule: this.module
            });
        }
    }
    async fetchVoucher({ denom, address }) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryVoucherRequest.create();
        request.denom = denom;
        request.address = address;
        try {
            const response = await this.retry(() => this.client.Voucher(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.voucherResponseToVoucher(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'Voucher',
                    contextModule: this.module
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'Voucher',
            });
        }
    }
    async fetchVouchers(denom) {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryVouchersRequest.create();
        request.denom = denom;
        try {
            const response = await this.retry(() => this.client.Vouchers(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.vouchersResponseToVouchers(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'VouchersForAddress',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'VouchersForAddress',
                contextModule: this.module,
            });
        }
    }
    async fetchModuleState() {
        const request = core_proto_ts_1.InjectivePermissionsV1Beta1Query.QueryModuleStateRequest.create();
        try {
            const response = await this.retry(() => this.client.PermissionsModuleState(request, this.metadata));
            return index_js_1.ChainGrpcPermissionsTransformer.moduleStateResponseToModuleState(response);
        }
        catch (e) {
            if (e instanceof core_proto_ts_1.InjectivePermissionsV1Beta1Query.GrpcWebError) {
                throw new exceptions_1.GrpcUnaryRequestException(new Error(e.toString()), {
                    code: (0, exceptions_1.grpcErrorCodeToErrorCode)(e.code),
                    context: 'ModuleState',
                    contextModule: this.module,
                });
            }
            throw new exceptions_1.GrpcUnaryRequestException(e, {
                code: exceptions_1.UnspecifiedErrorCode,
                context: 'ModuleState',
            });
        }
    }
}
exports.ChainGrpcPermissionsApi = ChainGrpcPermissionsApi;
