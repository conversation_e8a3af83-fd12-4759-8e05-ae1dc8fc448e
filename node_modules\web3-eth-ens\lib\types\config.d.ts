/**
 * An object holding the interface Ids of the ENS resolver contracts. Please see [how to write a resolver](https://docs.ens.domains/contract-developer-guide/writing-a-resolver).
 */
export declare const interfaceIds: {
    [T: string]: string;
};
/**
 * An object holding the functions that are supported by the ENS resolver contracts/interfaces.
 */
export declare const methodsInInterface: {
    [T: string]: string;
};
/**
 * An object holding the addressed of the ENS registries on the different networks (mainnet, goerli).
 */
export declare const registryAddresses: {
    [T: string]: string;
};
export declare const networkIds: {
    [T: string]: string;
};
//# sourceMappingURL=config.d.ts.map