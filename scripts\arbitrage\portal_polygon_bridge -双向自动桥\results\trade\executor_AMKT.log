2025-05-22 08:28:44,304 - INFO - ================================================================================
2025-05-22 08:28:44,304 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 08:28:44
2025-05-22 08:28:44,304 - INFO - 链: ethereum, 投入金额: 182.92 USDT
2025-05-22 08:28:44,304 - INFO - 代币地址: ******************************************
2025-05-22 08:28:44,304 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:182.92USDT
2025-05-22 08:28:44,304 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 08:28:44,304 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行182.92USDT买入AMKT交易
2025-05-22 08:28:44,304 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 08:28:44,304 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 08:28:44,305 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 08:28:44,305 - INFO -   chain: ethereum
2025-05-22 08:28:44,305 - INFO -   token_in: USDT
2025-05-22 08:28:44,305 - INFO -   token_out: ******************************************
2025-05-22 08:28:44,305 - INFO -   amount: 182.92
2025-05-22 08:28:44,305 - INFO -   slippage: 0.5%
2025-05-22 08:28:44,305 - INFO -   real: True
2025-05-22 08:28:46,937 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 08:28:46,938 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 182.92 USDT，可用: 166.71782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 182.92, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '182920000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 08:28:46,938 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 182.92 USDT，可用: 166.71782 USDT
2025-05-22 08:28:46,938 - INFO - 读取到 162 条现有交易记录
2025-05-22 08:28:46,939 - INFO - 添加新交易记录: AMKT (AMKT_182.92_2025-05-22 08:28:44)
2025-05-22 08:28:46,941 - INFO - 成功保存 163 条交易记录
2025-05-22 08:28:46,941 - INFO - AMKT: 买入交易处理完成，耗时: 2.64秒
2025-05-22 08:28:46,941 - INFO - ================================================================================
2025-05-22 09:27:55,412 - INFO - ================================================================================
2025-05-22 09:27:55,412 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 09:27:55
2025-05-22 09:27:55,412 - INFO - 链: ethereum, 投入金额: 173.04 USDT
2025-05-22 09:27:55,412 - INFO - 代币地址: ******************************************
2025-05-22 09:27:55,412 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:173.04USDT
2025-05-22 09:27:55,412 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 09:27:55,412 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行173.04USDT买入AMKT交易
2025-05-22 09:27:55,412 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 09:27:55,421 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 09:27:55,421 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 09:27:55,421 - INFO -   chain: ethereum
2025-05-22 09:27:55,421 - INFO -   token_in: USDT
2025-05-22 09:27:55,421 - INFO -   token_out: ******************************************
2025-05-22 09:27:55,421 - INFO -   amount: 173.04
2025-05-22 09:27:55,421 - INFO -   slippage: 0.5%
2025-05-22 09:27:55,421 - INFO -   real: True
2025-05-22 09:27:58,410 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 09:27:58,410 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 173.04 USDT，可用: 166.71782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 173.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '173040000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 09:27:58,410 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 173.04 USDT，可用: 166.71782 USDT
2025-05-22 09:27:58,411 - INFO - 读取到 163 条现有交易记录
2025-05-22 09:27:58,411 - INFO - 添加新交易记录: AMKT (AMKT_173.04_2025-05-22 09:27:55)
2025-05-22 09:27:58,413 - INFO - 成功保存 164 条交易记录
2025-05-22 09:27:58,413 - INFO - AMKT: 买入交易处理完成，耗时: 3.00秒
2025-05-22 09:27:58,413 - INFO - ================================================================================
2025-05-22 10:04:07,693 - INFO - ================================================================================
2025-05-22 10:04:07,693 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 10:04:07
2025-05-22 10:04:07,693 - INFO - 链: ethereum, 投入金额: 173.04 USDT
2025-05-22 10:04:07,693 - INFO - 代币地址: ******************************************
2025-05-22 10:04:07,693 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:173.04USDT
2025-05-22 10:04:07,693 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 10:04:07,694 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行173.04USDT买入AMKT交易
2025-05-22 10:04:07,694 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 10:04:07,694 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 10:04:07,694 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 10:04:07,694 - INFO -   chain: ethereum
2025-05-22 10:04:07,694 - INFO -   token_in: USDT
2025-05-22 10:04:07,694 - INFO -   token_out: ******************************************
2025-05-22 10:04:07,694 - INFO -   amount: 173.04
2025-05-22 10:04:07,694 - INFO -   slippage: 0.5%
2025-05-22 10:04:07,694 - INFO -   real: True
2025-05-22 10:04:09,589 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 10:04:09,589 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 173.04 USDT，可用: 166.71782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 173.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '173040000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 10:04:09,589 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 173.04 USDT，可用: 166.71782 USDT
2025-05-22 10:04:09,590 - INFO - 读取到 165 条现有交易记录
2025-05-22 10:04:09,590 - INFO - 添加新交易记录: AMKT (AMKT_173.04_2025-05-22 10:04:07)
2025-05-22 10:04:09,593 - INFO - 成功保存 166 条交易记录
2025-05-22 10:04:09,593 - INFO - AMKT: 买入交易处理完成，耗时: 1.90秒
2025-05-22 10:04:09,593 - INFO - ================================================================================
2025-05-22 12:42:07,810 - INFO - ================================================================================
2025-05-22 12:42:07,810 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 12:42:07
2025-05-22 12:42:07,810 - INFO - 链: ethereum, 投入金额: 173.04 USDT
2025-05-22 12:42:07,810 - INFO - 代币地址: ******************************************
2025-05-22 12:42:07,810 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:173.04USDT
2025-05-22 12:42:07,810 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 12:42:07,810 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行173.04USDT买入AMKT交易
2025-05-22 12:42:07,810 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 12:42:07,810 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 12:42:07,810 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 12:42:07,810 - INFO -   chain: ethereum
2025-05-22 12:42:07,810 - INFO -   token_in: USDT
2025-05-22 12:42:07,810 - INFO -   token_out: ******************************************
2025-05-22 12:42:07,811 - INFO -   amount: 173.04
2025-05-22 12:42:07,811 - INFO -   slippage: 0.5%
2025-05-22 12:42:07,811 - INFO -   real: True
2025-05-22 12:42:09,881 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 12:42:09,889 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 173.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '173040000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 12:42:09,890 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT
2025-05-22 12:42:09,891 - INFO - 读取到 167 条现有交易记录
2025-05-22 12:42:09,892 - INFO - 添加新交易记录: AMKT (AMKT_173.04_2025-05-22 12:42:07)
2025-05-22 12:42:09,896 - INFO - 成功保存 168 条交易记录
2025-05-22 12:42:09,896 - INFO - AMKT: 买入交易处理完成，耗时: 2.09秒
2025-05-22 12:42:09,896 - INFO - ================================================================================
2025-05-22 13:15:45,256 - INFO - ================================================================================
2025-05-22 13:15:45,256 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 13:15:45
2025-05-22 13:15:45,256 - INFO - 链: ethereum, 投入金额: 173.04 USDT
2025-05-22 13:15:45,256 - INFO - 代币地址: ******************************************
2025-05-22 13:15:45,256 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:173.04USDT
2025-05-22 13:15:45,256 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 13:15:45,256 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行173.04USDT买入AMKT交易
2025-05-22 13:15:45,256 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 13:15:45,256 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 13:15:45,256 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 13:15:45,256 - INFO -   chain: ethereum
2025-05-22 13:15:45,256 - INFO -   token_in: USDT
2025-05-22 13:15:45,256 - INFO -   token_out: ******************************************
2025-05-22 13:15:45,256 - INFO -   amount: 173.04
2025-05-22 13:15:45,256 - INFO -   slippage: 0.5%
2025-05-22 13:15:45,256 - INFO -   real: True
2025-05-22 13:15:47,216 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 13:15:47,216 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 173.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '173040000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 13:15:47,216 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT
2025-05-22 13:15:47,216 - INFO - 读取到 169 条现有交易记录
2025-05-22 13:15:47,216 - INFO - 添加新交易记录: AMKT (AMKT_173.04_2025-05-22 13:15:45)
2025-05-22 13:15:47,216 - INFO - 成功保存 170 条交易记录
2025-05-22 13:15:47,216 - INFO - AMKT: 买入交易处理完成，耗时: 1.96秒
2025-05-22 13:15:47,216 - INFO - ================================================================================
2025-05-22 13:48:45,636 - INFO - ================================================================================
2025-05-22 13:48:45,636 - INFO - 开始执行 AMKT 买入交易 - 时间: 2025-05-22 13:48:45
2025-05-22 13:48:45,636 - INFO - 链: ethereum, 投入金额: 173.04 USDT
2025-05-22 13:48:45,636 - INFO - 代币地址: ******************************************
2025-05-22 13:48:45,636 - INFO - 收到AMKT买入请求 - 链:ethereum, 投入:173.04USDT
2025-05-22 13:48:45,637 - INFO - AMKT: 将在ethereum链上执行买入，代币地址: ******************************************
2025-05-22 13:48:45,637 - INFO - AMKT: 准备使用KyberSwap在ethereum上执行173.04USDT买入AMKT交易
2025-05-22 13:48:45,637 - INFO - KyberSwap模块路径: C:\Users\<USER>\CascadeProjects\cex_dex_arb_dev\src\dex\KyberSwap\swap.py
2025-05-22 13:48:45,637 - INFO - swap_tokens 函数参数: ['chain', 'token_in', 'token_out', 'amount', 'slippage', 'save_gas', 'real', 'timeout', 'gas_multiplier', 'excluded_sources', 'recipient', 'skip_confirmation']
2025-05-22 13:48:45,637 - INFO - AMKT: 准备调用swap_tokens函数，参数：
2025-05-22 13:48:45,637 - INFO -   chain: ethereum
2025-05-22 13:48:45,637 - INFO -   token_in: USDT
2025-05-22 13:48:45,637 - INFO -   token_out: ******************************************
2025-05-22 13:48:45,637 - INFO -   amount: 173.04
2025-05-22 13:48:45,637 - INFO -   slippage: 0.5%
2025-05-22 13:48:45,637 - INFO -   real: True
2025-05-22 13:48:47,597 - INFO - AMKT: swap_tokens返回值类型: <class 'dict'>
2025-05-22 13:48:47,597 - INFO - AMKT: swap_tokens返回值内容: {'success': False, 'status': '失败', 'message': '', 'error': '代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT', 'chain': 'ethereum', 'token_in': 'USDT', 'token_out': '******************************************', 'amount': 173.04, 'token_in_address': '******************************************', 'token_out_address': '******************************************', 'amount_in_wei': '173040000', 'is_native_in': False, 'is_native_out': False}
2025-05-22 13:48:47,598 - ERROR - AMKT: 交易失败 - 代币余额不足。请求: 173.04 USDT，可用: 0.21782 USDT
2025-05-22 13:48:47,598 - INFO - 读取到 173 条现有交易记录
2025-05-22 13:48:47,598 - INFO - 添加新交易记录: AMKT (AMKT_173.04_2025-05-22 13:48:45)
2025-05-22 13:48:47,601 - INFO - 成功保存 174 条交易记录
2025-05-22 13:48:47,601 - INFO - AMKT: 买入交易处理完成，耗时: 1.96秒
2025-05-22 13:48:47,601 - INFO - ================================================================================
