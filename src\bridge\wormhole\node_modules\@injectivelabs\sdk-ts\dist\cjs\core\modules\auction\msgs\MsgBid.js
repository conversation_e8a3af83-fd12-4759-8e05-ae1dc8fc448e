"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const MsgBase_js_1 = require("../../MsgBase.js");
const snakecase_keys_1 = __importDefault(require("snakecase-keys"));
const core_proto_ts_1 = require("@injectivelabs/core-proto-ts");
/**
 * @category Messages
 */
class MsgBid extends MsgBase_js_1.MsgBase {
    static fromJSON(params) {
        return new MsgBid(params);
    }
    toProto() {
        const { params } = this;
        const amountCoin = core_proto_ts_1.CosmosBaseV1Beta1Coin.Coin.create();
        amountCoin.denom = params.amount.denom;
        amountCoin.amount = params.amount.amount;
        const message = core_proto_ts_1.InjectiveAuctionV1Beta1Tx.MsgBid.create();
        message.sender = params.injectiveAddress;
        message.bidAmount = amountCoin;
        message.round = params.round.toString();
        return core_proto_ts_1.InjectiveAuctionV1Beta1Tx.MsgBid.fromPartial(message);
    }
    toData() {
        const proto = this.toProto();
        return {
            '@type': '/injective.auction.v1beta1.MsgBid',
            ...proto,
        };
    }
    toAmino() {
        const proto = this.toProto();
        const message = {
            ...(0, snakecase_keys_1.default)(proto),
        };
        return {
            type: 'auction/MsgBid',
            value: message,
        };
    }
    toDirectSign() {
        const proto = this.toProto();
        return {
            type: '/injective.auction.v1beta1.MsgBid',
            message: proto,
        };
    }
    toWeb3Gw() {
        const amino = this.toAmino();
        const { value } = amino;
        return {
            '@type': '/injective.auction.v1beta1.MsgBid',
            ...value,
        };
    }
    toBinary() {
        return core_proto_ts_1.InjectiveAuctionV1Beta1Tx.MsgBid.encode(this.toProto()).finish();
    }
}
exports.default = MsgBid;
