{"version": 3, "sources": ["/home/<USER>/src/pl/js-cid/webpack/universalModuleDefinition", "/home/<USER>/src/pl/js-cid/webpack/bootstrap", "/home/<USER>/src/pl/js-cid/node_modules/buffer/index.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multihashes/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/base-x/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/util.js", "/home/<USER>/src/pl/js-cid/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/webpack/buildin/global.js", "/home/<USER>/src/pl/js-cid/node_modules/base64-js/index.js", "/home/<USER>/src/pl/js-cid/node_modules/ieee754/index.js", "/home/<USER>/src/pl/js-cid/node_modules/isarray/index.js", "/home/<USER>/src/pl/js-cid/node_modules/bs58/index.js", "/home/<USER>/src/pl/js-cid/node_modules/safe-buffer/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multihashes/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/encode.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/decode.js", "/home/<USER>/src/pl/js-cid/node_modules/varint/length.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base16.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base32.js", "/home/<USER>/src/pl/js-cid/node_modules/multibase/src/base64.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/index.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/int-table.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/varint-table.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/constants.js", "/home/<USER>/src/pl/js-cid/node_modules/multicodec/src/print.js", "/home/<USER>/src/pl/js-cid/src/cid-util.js", "/home/<USER>/src/pl/js-cid/node_modules/class-is/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "base64", "ieee754", "isArray", "kMaxLength", "<PERSON><PERSON><PERSON>", "TYPED_ARRAY_SUPPORT", "createBuffer", "that", "length", "RangeError", "Uint8Array", "__proto__", "arg", "encodingOrOffset", "this", "Error", "allocUnsafe", "from", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "byteOffset", "byteLength", "undefined", "fromArrayLike", "fromArrayBuffer", "string", "encoding", "isEncoding", "actual", "write", "slice", "fromString", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "checked", "copy", "buffer", "val", "type", "data", "fromObject", "assertSize", "size", "toString", "<PERSON><PERSON><PERSON><PERSON>", "loweredCase", "utf8ToBytes", "base64ToBytes", "toLowerCase", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "b", "bidirectionalIndexOf", "dir", "isNaN", "arrayIndexOf", "indexOf", "lastIndexOf", "arr", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "String", "read", "buf", "readUInt16BE", "foundIndex", "found", "j", "hexWrite", "offset", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "str", "byteArray", "push", "charCodeAt", "asciiToBytes", "latin1Write", "base64Write", "ucs2Write", "units", "hi", "lo", "utf16leToBytes", "fromByteArray", "Math", "min", "res", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "firstByte", "codePoint", "bytesPerSequence", "codePoints", "fromCharCode", "apply", "decodeCodePointsArray", "<PERSON><PERSON><PERSON><PERSON>", "alloc", "INSPECT_MAX_BYTES", "global", "foo", "subarray", "e", "typedArraySupport", "poolSize", "_augment", "species", "configurable", "fill", "allocUnsafeSlow", "_isBuffer", "compare", "a", "x", "y", "concat", "list", "pos", "swap16", "swap32", "swap64", "arguments", "equals", "inspect", "max", "match", "join", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "includes", "isFinite", "toJSON", "Array", "_arr", "ret", "out", "toHex", "bytes", "checkOffset", "ext", "checkInt", "objectWriteUInt16", "littleEndian", "objectWriteUInt32", "checkIEEE754", "writeFloat", "noAssert", "writeDouble", "newBuf", "sliceLen", "readUIntLE", "mul", "readUIntBE", "readUInt8", "readUInt16LE", "readUInt32LE", "readUInt32BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "writeUIntLE", "writeUIntBE", "writeUInt8", "floor", "writeUInt16LE", "writeUInt16BE", "writeUInt32LE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "writeFloatLE", "writeFloatBE", "writeDoubleLE", "writeDoubleBE", "targetStart", "set", "code", "INVALID_BASE64_RE", "Infinity", "leadSurrogate", "toByteArray", "trim", "replace", "stringtrim", "base64clean", "src", "dst", "encode", "decode", "<PERSON><PERSON><PERSON><PERSON>", "bs58", "cs", "names", "codes", "defaultLengths", "varint", "validate", "multihash", "toHexString", "hash", "fromHexString", "toB58String", "fromB58String", "encoded", "isValidCode", "digest", "hashfn", "coerceCode", "isAppCode", "prefix", "ALPHABET", "ALPHABET_MAP", "BASE", "LEADER", "char<PERSON>t", "z", "decodeUnsafe", "carry", "k", "reverse", "source", "digits", "q", "bufferToNumber", "numberToBuffer", "num", "hexString", "varintBufferEncode", "input", "varintBufferDecode", "varintEncode", "require", "mh", "multibase", "multicodec", "codecs", "CIDUtil", "CID", "constructor", "version", "codec", "multibaseName", "_CID", "isCID", "cid", "baseName", "isEncoded", "getCodec", "rmPrefix", "validateCID", "v", "_buffer", "getCodeVarint", "toV0", "toV1", "toBaseEncodedString", "base", "for", "other", "errorMsg", "checkCIDComponents", "withIs", "className", "symbolName", "g", "Function", "b64", "lens", "getLens", "validLen", "placeHoldersLen", "tmp", "Arr", "_byteLength", "curByte", "revLookup", "uint8", "extraBytes", "parts", "len2", "encodeChunk", "lookup", "output", "isLE", "mLen", "nBytes", "eLen", "eMax", "eBias", "nBits", "NaN", "rt", "abs", "log", "LN2", "basex", "copyProps", "SafeBuffer", "freeze", "0", "oldOffset", "INT", "shift", "counter", "N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8", "N9", "constants", "nameOrCode", "getBase", "bufOrString", "substring", "err", "keys", "errNotSupported", "codeBuf", "validEncode", "isImplemented", "Base", "baseX", "base16", "base32", "reduce", "prev", "tupple", "implementation", "alphabet", "engine", "string<PERSON><PERSON><PERSON><PERSON><PERSON>", "char", "view", "padding", "bits", "RegExp", "index", "url", "pad", "intTable", "codecNameToCodeVarint", "util", "addPrefix", "multicodecStrOrCode", "prefixedData", "codecName", "getName", "getNumber", "getCode", "getVarint", "assign", "print", "baseTable", "nameTable", "Map", "encodingName", "varintTable", "table", "entries", "toUpperCase", "tableByCode", "message", "Class", "symbol", "ClassIsWrapper", "args", "super", "proto", "withoutNew", "_this"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAc,KAAID,IAElBD,EAAW,KAAIC,IARjB,CASGK,QAAQ,WACX,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,gCClFrD,YAUA,IAAIC,EAAS,EAAQ,GAEjBC,EAAU,EAAQ,IAElBC,EAAU,EAAQ,IAsDtB,SAASC,IACP,OAAOC,EAAOC,oBAAsB,WAAa,WAGnD,SAASC,EAAaC,EAAMC,GAC1B,GAAIL,IAAeK,EACjB,MAAM,IAAIC,WAAW,8BAgBvB,OAbIL,EAAOC,qBAETE,EAAO,IAAIG,WAAWF,IACjBG,UAAYP,EAAOR,WAGX,OAATW,IACFA,EAAO,IAAIH,EAAOI,IAGpBD,EAAKC,OAASA,GAGTD,EAaT,SAASH,EAAOQ,EAAKC,EAAkBL,GACrC,KAAKJ,EAAOC,qBAAyBS,gBAAgBV,GACnD,OAAO,IAAIA,EAAOQ,EAAKC,EAAkBL,GAI3C,GAAmB,iBAARI,EAAkB,CAC3B,GAAgC,iBAArBC,EACT,MAAM,IAAIE,MAAM,qEAGlB,OAAOC,EAAYF,KAAMF,GAG3B,OAAOK,EAAKH,KAAMF,EAAKC,EAAkBL,GAW3C,SAASS,EAAKV,EAAMtB,EAAO4B,EAAkBL,GAC3C,GAAqB,iBAAVvB,EACT,MAAM,IAAIiC,UAAU,yCAGtB,MAA2B,oBAAhBC,aAA+BlC,aAAiBkC,YAsI7D,SAAyBZ,EAAMa,EAAOC,EAAYb,GAGhD,GAFAY,EAAME,WAEFD,EAAa,GAAKD,EAAME,WAAaD,EACvC,MAAM,IAAIZ,WAAW,6BAGvB,GAAIW,EAAME,WAAaD,GAAcb,GAAU,GAC7C,MAAM,IAAIC,WAAW,6BAIrBW,OADiBG,IAAfF,QAAuCE,IAAXf,EACtB,IAAIE,WAAWU,QACHG,IAAXf,EACD,IAAIE,WAAWU,EAAOC,GAEtB,IAAIX,WAAWU,EAAOC,EAAYb,GAGxCJ,EAAOC,qBAETE,EAAOa,GACFT,UAAYP,EAAOR,UAGxBW,EAAOiB,EAAcjB,EAAMa,GAG7B,OAAOb,EAjKEkB,CAAgBlB,EAAMtB,EAAO4B,EAAkBL,GAGnC,iBAAVvB,EAgGb,SAAoBsB,EAAMmB,EAAQC,GACR,iBAAbA,GAAsC,KAAbA,IAClCA,EAAW,QAGb,IAAKvB,EAAOwB,WAAWD,GACrB,MAAM,IAAIT,UAAU,8CAGtB,IAAIV,EAAwC,EAA/Bc,EAAWI,EAAQC,GAE5BE,GADJtB,EAAOD,EAAaC,EAAMC,IACRsB,MAAMJ,EAAQC,GAE5BE,IAAWrB,IAIbD,EAAOA,EAAKwB,MAAM,EAAGF,IAGvB,OAAOtB,EAnHEyB,CAAWzB,EAAMtB,EAAO4B,GAgKnC,SAAoBN,EAAM0B,GACxB,GAAI7B,EAAO8B,SAASD,GAAM,CACxB,IAAIE,EAA4B,EAAtBC,EAAQH,EAAIzB,QAGtB,OAAoB,KAFpBD,EAAOD,EAAaC,EAAM4B,IAEjB3B,OACAD,GAGT0B,EAAII,KAAK9B,EAAM,EAAG,EAAG4B,GACd5B,GAGT,GAAI0B,EAAK,CACP,GAA2B,oBAAhBd,aAA+Bc,EAAIK,kBAAkBnB,aAAe,WAAYc,EACzF,MAA0B,iBAAfA,EAAIzB,SAigDN+B,EAjgDmCN,EAAIzB,SAkgDrC+B,EAjgDFjC,EAAaC,EAAM,GAGrBiB,EAAcjB,EAAM0B,GAG7B,GAAiB,WAAbA,EAAIO,MAAqBtC,EAAQ+B,EAAIQ,MACvC,OAAOjB,EAAcjB,EAAM0B,EAAIQ,MAy/CrC,IAAeF,EAr/Cb,MAAM,IAAIrB,UAAU,sFAxLbwB,CAAWnC,EAAMtB,GA6B1B,SAAS0D,EAAWC,GAClB,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,oCACf,GAAI0B,EAAO,EAChB,MAAM,IAAInC,WAAW,wCA8BzB,SAASO,EAAYT,EAAMqC,GAIzB,GAHAD,EAAWC,GACXrC,EAAOD,EAAaC,EAAMqC,EAAO,EAAI,EAAoB,EAAhBR,EAAQQ,KAE5CxC,EAAOC,oBACV,IAAK,IAAIrC,EAAI,EAAGA,EAAI4E,IAAQ5E,EAC1BuC,EAAKvC,GAAK,EAId,OAAOuC,EA0CT,SAASiB,EAAcjB,EAAMa,GAC3B,IAAIZ,EAASY,EAAMZ,OAAS,EAAI,EAA4B,EAAxB4B,EAAQhB,EAAMZ,QAClDD,EAAOD,EAAaC,EAAMC,GAE1B,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,EAAQxC,GAAK,EAC/BuC,EAAKvC,GAAgB,IAAXoD,EAAMpD,GAGlB,OAAOuC,EAgET,SAAS6B,EAAQ5B,GAGf,GAAIA,GAAUL,IACZ,MAAM,IAAIM,WAAW,0DAAiEN,IAAa0C,SAAS,IAAM,UAGpH,OAAgB,EAATrC,EA8FT,SAASc,EAAWI,EAAQC,GAC1B,GAAIvB,EAAO8B,SAASR,GAClB,OAAOA,EAAOlB,OAGhB,GAA2B,oBAAhBW,aAA6D,mBAAvBA,YAAY2B,SAA0B3B,YAAY2B,OAAOpB,IAAWA,aAAkBP,aACrI,OAAOO,EAAOJ,WAGM,iBAAXI,IACTA,EAAS,GAAKA,GAGhB,IAAIS,EAAMT,EAAOlB,OACjB,GAAY,IAAR2B,EAAW,OAAO,EAItB,IAFA,IAAIY,GAAc,IAGhB,OAAQpB,GACN,IAAK,QACL,IAAK,SACL,IAAK,SACH,OAAOQ,EAET,IAAK,OACL,IAAK,QACL,UAAKZ,EACH,OAAOyB,EAAYtB,GAAQlB,OAE7B,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAa,EAAN2B,EAET,IAAK,MACH,OAAOA,IAAQ,EAEjB,IAAK,SACH,OAAOc,EAAcvB,GAAQlB,OAE/B,QACE,GAAIuC,EAAa,OAAOC,EAAYtB,GAAQlB,OAE5CmB,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,GAOtB,SAASI,EAAaxB,EAAUyB,EAAOC,GACrC,IAAIN,GAAc,EAalB,SANcxB,IAAV6B,GAAuBA,EAAQ,KACjCA,EAAQ,GAKNA,EAAQtC,KAAKN,OACf,MAAO,GAOT,SAJYe,IAAR8B,GAAqBA,EAAMvC,KAAKN,UAClC6C,EAAMvC,KAAKN,QAGT6C,GAAO,EACT,MAAO,GAOT,IAHAA,KAAS,KACTD,KAAW,GAGT,MAAO,GAKT,IAFKzB,IAAUA,EAAW,UAGxB,OAAQA,GACN,IAAK,MACH,OAAO2B,EAASxC,KAAMsC,EAAOC,GAE/B,IAAK,OACL,IAAK,QACH,OAAOE,EAAUzC,KAAMsC,EAAOC,GAEhC,IAAK,QACH,OAAOG,EAAW1C,KAAMsC,EAAOC,GAEjC,IAAK,SACL,IAAK,SACH,OAAOI,EAAY3C,KAAMsC,EAAOC,GAElC,IAAK,SACH,OAAOK,EAAY5C,KAAMsC,EAAOC,GAElC,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOM,EAAa7C,KAAMsC,EAAOC,GAEnC,QACE,GAAIN,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAYA,EAAW,IAAIuB,cAC3BH,GAAc,GAStB,SAASa,EAAKC,EAAGpE,EAAGrB,GAClB,IAAIJ,EAAI6F,EAAEpE,GACVoE,EAAEpE,GAAKoE,EAAEzF,GACTyF,EAAEzF,GAAKJ,EAgJT,SAAS8F,EAAqBxB,EAAQC,EAAKlB,EAAYM,EAAUoC,GAE/D,GAAsB,IAAlBzB,EAAO9B,OAAc,OAAQ,EAqBjC,GAnB0B,iBAAfa,GACTM,EAAWN,EACXA,EAAa,GACJA,EAAa,WACtBA,EAAa,WACJA,GAAc,aACvBA,GAAc,YAGhBA,GAAcA,EAEV2C,MAAM3C,KAERA,EAAa0C,EAAM,EAAIzB,EAAO9B,OAAS,GAIrCa,EAAa,IAAGA,EAAaiB,EAAO9B,OAASa,GAE7CA,GAAciB,EAAO9B,OAAQ,CAC/B,GAAIuD,EAAK,OAAQ,EAAO1C,EAAaiB,EAAO9B,OAAS,OAChD,GAAIa,EAAa,EAAG,CACzB,IAAI0C,EAAyB,OAAQ,EAA5B1C,EAAa,EASxB,GALmB,iBAARkB,IACTA,EAAMnC,EAAOa,KAAKsB,EAAKZ,IAIrBvB,EAAO8B,SAASK,GAElB,OAAmB,IAAfA,EAAI/B,QACE,EAGHyD,EAAa3B,EAAQC,EAAKlB,EAAYM,EAAUoC,GAClD,GAAmB,iBAARxB,EAGhB,OAFAA,GAAY,IAERnC,EAAOC,qBAA+D,mBAAjCK,WAAWd,UAAUsE,QACxDH,EACKrD,WAAWd,UAAUsE,QAAQ/F,KAAKmE,EAAQC,EAAKlB,GAE/CX,WAAWd,UAAUuE,YAAYhG,KAAKmE,EAAQC,EAAKlB,GAIvD4C,EAAa3B,EAAQ,CAACC,GAAMlB,EAAYM,EAAUoC,GAG3D,MAAM,IAAI7C,UAAU,wCAGtB,SAAS+C,EAAaG,EAAK7B,EAAKlB,EAAYM,EAAUoC,GACpD,IA2BI/F,EA3BAqG,EAAY,EACZC,EAAYF,EAAI5D,OAChB+D,EAAYhC,EAAI/B,OAEpB,QAAiBe,IAAbI,IAGe,UAFjBA,EAAW6C,OAAO7C,GAAUuB,gBAEY,UAAbvB,GAAqC,YAAbA,GAAuC,aAAbA,GAAyB,CACpG,GAAIyC,EAAI5D,OAAS,GAAK+B,EAAI/B,OAAS,EACjC,OAAQ,EAGV6D,EAAY,EACZC,GAAa,EACbC,GAAa,EACblD,GAAc,EAIlB,SAASoD,EAAKC,EAAK1G,GACjB,OAAkB,IAAdqG,EACKK,EAAI1G,GAEJ0G,EAAIC,aAAa3G,EAAIqG,GAMhC,GAAIN,EAAK,CACP,IAAIa,GAAc,EAElB,IAAK5G,EAAIqD,EAAYrD,EAAIsG,EAAWtG,IAClC,GAAIyG,EAAKL,EAAKpG,KAAOyG,EAAKlC,GAAqB,IAAhBqC,EAAoB,EAAI5G,EAAI4G,IAEzD,IADoB,IAAhBA,IAAmBA,EAAa5G,GAChCA,EAAI4G,EAAa,IAAML,EAAW,OAAOK,EAAaP,OAEtC,IAAhBO,IAAmB5G,GAAKA,EAAI4G,GAChCA,GAAc,OAMlB,IAFIvD,EAAakD,EAAYD,IAAWjD,EAAaiD,EAAYC,GAE5DvG,EAAIqD,EAAYrD,GAAK,EAAGA,IAAK,CAGhC,IAFA,IAAI6G,GAAQ,EAEHC,EAAI,EAAGA,EAAIP,EAAWO,IAC7B,GAAIL,EAAKL,EAAKpG,EAAI8G,KAAOL,EAAKlC,EAAKuC,GAAI,CACrCD,GAAQ,EACR,MAIJ,GAAIA,EAAO,OAAO7G,EAItB,OAAQ,EAeV,SAAS+G,EAASL,EAAKhD,EAAQsD,EAAQxE,GACrCwE,EAASC,OAAOD,IAAW,EAC3B,IAAIE,EAAYR,EAAIlE,OAASwE,EAExBxE,GAGHA,EAASyE,OAAOzE,IAEH0E,IACX1E,EAAS0E,GALX1E,EAAS0E,EAUX,IAAIC,EAASzD,EAAOlB,OACpB,GAAI2E,EAAS,GAAM,EAAG,MAAM,IAAIjE,UAAU,sBAEtCV,EAAS2E,EAAS,IACpB3E,EAAS2E,EAAS,GAGpB,IAAK,IAAInH,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAC/B,IAAIoH,EAASC,SAAS3D,EAAO4D,OAAW,EAAJtH,EAAO,GAAI,IAC/C,GAAIgG,MAAMoB,GAAS,OAAOpH,EAC1B0G,EAAIM,EAAShH,GAAKoH,EAGpB,OAAOpH,EAGT,SAASuH,EAAUb,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EAAWxC,EAAYtB,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GAG3E,SAASiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GACvC,OAAOgF,EA26BT,SAAsBE,GAGpB,IAFA,IAAIC,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,SAAUxC,EAEhC2H,EAAUC,KAAyB,IAApBF,EAAIG,WAAW7H,IAGhC,OAAO2H,EAn7BWG,CAAapE,GAASgD,EAAKM,EAAQxE,GAGvD,SAASuF,EAAYrB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GAGzC,SAASwF,EAAYtB,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOgF,EAAWvC,EAAcvB,GAASgD,EAAKM,EAAQxE,GAGxD,SAASyF,EAAUvB,EAAKhD,EAAQsD,EAAQxE,GACtC,OAAOgF,EA06BT,SAAwBE,EAAKQ,GAI3B,IAHA,IAAI7H,EAAG8H,EAAIC,EACPT,EAAY,GAEP3H,EAAI,EAAGA,EAAI0H,EAAIlF,WACjB0F,GAAS,GAAK,KADalI,EAEhCK,EAAIqH,EAAIG,WAAW7H,GACnBmI,EAAK9H,GAAK,EACV+H,EAAK/H,EAAI,IACTsH,EAAUC,KAAKQ,GACfT,EAAUC,KAAKO,GAGjB,OAAOR,EAv7BWU,CAAe3E,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GA+E9E,SAASkD,EAAYgB,EAAKtB,EAAOC,GAC/B,OAAc,IAAVD,GAAeC,IAAQqB,EAAIlE,OACtBR,EAAOsG,cAAc5B,GAErB1E,EAAOsG,cAAc5B,EAAI3C,MAAMqB,EAAOC,IAIjD,SAASE,EAAUmB,EAAKtB,EAAOC,GAC7BA,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAI3B,IAHA,IAAIoD,EAAM,GACNzI,EAAIoF,EAEDpF,EAAIqF,GAAK,CACd,IAKMqD,EAAYC,EAAWC,EAAYC,EALrCC,EAAYpC,EAAI1G,GAChB+I,EAAY,KACZC,EAAmBF,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAIA,EAAY,IAAO,EAAI,EAE5F,GAAI9I,EAAIgJ,GAAoB3D,EAG1B,OAAQ2D,GACN,KAAK,EACCF,EAAY,MACdC,EAAYD,GAGd,MAEF,KAAK,EAGyB,MAAV,KAFlBJ,EAAahC,EAAI1G,EAAI,OAGnB6I,GAA6B,GAAZC,IAAqB,EAAmB,GAAbJ,GAExB,MAClBK,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GAEQ,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,KACnCE,GAA6B,GAAZC,IAAoB,IAAoB,GAAbJ,IAAsB,EAAkB,GAAZC,GAEpD,OAAUE,EAAgB,OAAUA,EAAgB,SACtEE,EAAYF,GAIhB,MAEF,KAAK,EACHH,EAAahC,EAAI1G,EAAI,GACrB2I,EAAYjC,EAAI1G,EAAI,GACpB4I,EAAalC,EAAI1G,EAAI,GAEO,MAAV,IAAb0I,IAAsD,MAAV,IAAZC,IAAsD,MAAV,IAAbC,KAClEC,GAA6B,GAAZC,IAAoB,IAAqB,GAAbJ,IAAsB,IAAmB,GAAZC,IAAqB,EAAmB,GAAbC,GAEjF,OAAUC,EAAgB,UAC5CE,EAAYF,GAOJ,OAAdE,GAGFA,EAAY,MACZC,EAAmB,GACVD,EAAY,QAErBA,GAAa,MACbN,EAAIb,KAAKmB,IAAc,GAAK,KAAQ,OACpCA,EAAY,MAAqB,KAAZA,GAGvBN,EAAIb,KAAKmB,GACT/I,GAAKgJ,EAGP,OAQF,SAA+BC,GAC7B,IAAI9E,EAAM8E,EAAWzG,OAErB,GAAI2B,GALqB,KAMvB,OAAOqC,OAAO0C,aAAaC,MAAM3C,OAAQyC,GAI3C,IAAIR,EAAM,GACNzI,EAAI,EAER,KAAOA,EAAImE,GACTsE,GAAOjC,OAAO0C,aAAaC,MAAM3C,OAAQyC,EAAWlF,MAAM/D,EAAGA,GAdtC,OAiBzB,OAAOyI,EAvBAW,CAAsBX,GA1gC/BjJ,EAAQ4C,OAASA,EACjB5C,EAAQ6J,WAiUR,SAAoB7G,IACbA,GAAUA,IAEbA,EAAS,GAGX,OAAOJ,EAAOkH,OAAO9G,IAtUvBhD,EAAQ+J,kBAAoB,GA0B5BnH,EAAOC,yBAAqDkB,IAA/BiG,EAAOnH,oBAAoCmH,EAAOnH,oBAO/E,WACE,IACE,IAAI+D,EAAM,IAAI1D,WAAW,GAOzB,OANA0D,EAAIzD,UAAY,CACdA,UAAWD,WAAWd,UACtB6H,IAAK,WACH,OAAO,KAGU,KAAdrD,EAAIqD,OACa,mBAAjBrD,EAAIsD,UACuB,IAAlCtD,EAAIsD,SAAS,EAAG,GAAGpG,WACnB,MAAOqG,GACP,OAAO,GApB0FC,GAKrGpK,EAAQ2C,WAAaA,IAuErBC,EAAOyH,SAAW,KAGlBzH,EAAO0H,SAAW,SAAU1D,GAE1B,OADAA,EAAIzD,UAAYP,EAAOR,UAChBwE,GA4BThE,EAAOa,KAAO,SAAUhC,EAAO4B,EAAkBL,GAC/C,OAAOS,EAAK,KAAMhC,EAAO4B,EAAkBL,IAGzCJ,EAAOC,sBACTD,EAAOR,UAAUe,UAAYD,WAAWd,UACxCQ,EAAOO,UAAYD,WAEG,oBAAX3B,QAA0BA,OAAOgJ,SAAW3H,EAAOrB,OAAOgJ,WAAa3H,GAEhF1B,OAAOC,eAAeyB,EAAQrB,OAAOgJ,QAAS,CAC5C9I,MAAO,KACP+I,cAAc,KAmCpB5H,EAAOkH,MAAQ,SAAU1E,EAAMqF,EAAMtG,GACnC,OAvBF,SAAepB,EAAMqC,EAAMqF,EAAMtG,GAG/B,OAFAgB,EAAWC,GAEPA,GAAQ,EACHtC,EAAaC,EAAMqC,QAGfrB,IAAT0G,EAIyB,iBAAbtG,EAAwBrB,EAAaC,EAAMqC,GAAMqF,KAAKA,EAAMtG,GAAYrB,EAAaC,EAAMqC,GAAMqF,KAAKA,GAG/G3H,EAAaC,EAAMqC,GASnB0E,CAAM,KAAM1E,EAAMqF,EAAMtG,IAoBjCvB,EAAOY,YAAc,SAAU4B,GAC7B,OAAO5B,EAAY,KAAM4B,IAO3BxC,EAAO8H,gBAAkB,SAAUtF,GACjC,OAAO5B,EAAY,KAAM4B,IAqH3BxC,EAAO8B,SAAW,SAAkB2B,GAClC,QAAe,MAALA,IAAaA,EAAEsE,YAG3B/H,EAAOgI,QAAU,SAAiBC,EAAGxE,GACnC,IAAKzD,EAAO8B,SAASmG,KAAOjI,EAAO8B,SAAS2B,GAC1C,MAAM,IAAI3C,UAAU,6BAGtB,GAAImH,IAAMxE,EAAG,OAAO,EAIpB,IAHA,IAAIyE,EAAID,EAAE7H,OACN+H,EAAI1E,EAAErD,OAEDxC,EAAI,EAAGmE,EAAMoE,KAAKC,IAAI8B,EAAGC,GAAIvK,EAAImE,IAAOnE,EAC/C,GAAIqK,EAAErK,KAAO6F,EAAE7F,GAAI,CACjBsK,EAAID,EAAErK,GACNuK,EAAI1E,EAAE7F,GACN,MAIJ,OAAIsK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAGTlI,EAAOwB,WAAa,SAAoBD,GACtC,OAAQ6C,OAAO7C,GAAUuB,eACvB,IAAK,MACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,SACL,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAO,EAET,QACE,OAAO,IAIb9C,EAAOoI,OAAS,SAAgBC,EAAMjI,GACpC,IAAKN,EAAQuI,GACX,MAAM,IAAIvH,UAAU,+CAGtB,GAAoB,IAAhBuH,EAAKjI,OACP,OAAOJ,EAAOkH,MAAM,GAGtB,IAAItJ,EAEJ,QAAeuD,IAAXf,EAGF,IAFAA,EAAS,EAEJxC,EAAI,EAAGA,EAAIyK,EAAKjI,SAAUxC,EAC7BwC,GAAUiI,EAAKzK,GAAGwC,OAItB,IAAI8B,EAASlC,EAAOY,YAAYR,GAC5BkI,EAAM,EAEV,IAAK1K,EAAI,EAAGA,EAAIyK,EAAKjI,SAAUxC,EAAG,CAChC,IAAI0G,EAAM+D,EAAKzK,GAEf,IAAKoC,EAAO8B,SAASwC,GACnB,MAAM,IAAIxD,UAAU,+CAGtBwD,EAAIrC,KAAKC,EAAQoG,GACjBA,GAAOhE,EAAIlE,OAGb,OAAO8B,GAsDTlC,EAAOkB,WAAaA,EAyEpBlB,EAAOR,UAAUuI,WAAY,EAQ7B/H,EAAOR,UAAU+I,OAAS,WACxB,IAAIxG,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAGpB,OAAO8C,MAGTV,EAAOR,UAAUgJ,OAAS,WACxB,IAAIzG,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUiJ,OAAS,WACxB,IAAI1G,EAAMrB,KAAKN,OAEf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAGvB,IAAK,IAAIzC,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EAC5B4F,EAAK9C,KAAM9C,EAAGA,EAAI,GAClB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GACtB4F,EAAK9C,KAAM9C,EAAI,EAAGA,EAAI,GAGxB,OAAO8C,MAGTV,EAAOR,UAAUiD,SAAW,WAC1B,IAAIrC,EAAuB,EAAdM,KAAKN,OAClB,OAAe,IAAXA,EAAqB,GACA,IAArBsI,UAAUtI,OAAqB+C,EAAUzC,KAAM,EAAGN,GAC/C2C,EAAagE,MAAMrG,KAAMgI,YAGlC1I,EAAOR,UAAUmJ,OAAS,SAAgBlF,GACxC,IAAKzD,EAAO8B,SAAS2B,GAAI,MAAM,IAAI3C,UAAU,6BAC7C,OAAIJ,OAAS+C,GACsB,IAA5BzD,EAAOgI,QAAQtH,KAAM+C,IAG9BzD,EAAOR,UAAUoJ,QAAU,WACzB,IAAItD,EAAM,GACNuD,EAAMzL,EAAQ+J,kBAOlB,OALIzG,KAAKN,OAAS,IAChBkF,EAAM5E,KAAK+B,SAAS,MAAO,EAAGoG,GAAKC,MAAM,SAASC,KAAK,KACnDrI,KAAKN,OAASyI,IAAKvD,GAAO,UAGzB,WAAaA,EAAM,KAG5BtF,EAAOR,UAAUwI,QAAU,SAAiBgB,EAAQhG,EAAOC,EAAKgG,EAAWC,GACzE,IAAKlJ,EAAO8B,SAASkH,GACnB,MAAM,IAAIlI,UAAU,6BAmBtB,QAhBcK,IAAV6B,IACFA,EAAQ,QAGE7B,IAAR8B,IACFA,EAAM+F,EAASA,EAAO5I,OAAS,QAGfe,IAAd8H,IACFA,EAAY,QAGE9H,IAAZ+H,IACFA,EAAUxI,KAAKN,QAGb4C,EAAQ,GAAKC,EAAM+F,EAAO5I,QAAU6I,EAAY,GAAKC,EAAUxI,KAAKN,OACtE,MAAM,IAAIC,WAAW,sBAGvB,GAAI4I,GAAaC,GAAWlG,GAASC,EACnC,OAAO,EAGT,GAAIgG,GAAaC,EACf,OAAQ,EAGV,GAAIlG,GAASC,EACX,OAAO,EAOT,GAAIvC,OAASsI,EAAQ,OAAO,EAO5B,IANA,IAAId,GAFJgB,KAAa,IADbD,KAAe,GAIXd,GALJlF,KAAS,IADTD,KAAW,GAOPjB,EAAMoE,KAAKC,IAAI8B,EAAGC,GAClBgB,EAAWzI,KAAKiB,MAAMsH,EAAWC,GACjCE,EAAaJ,EAAOrH,MAAMqB,EAAOC,GAE5BrF,EAAI,EAAGA,EAAImE,IAAOnE,EACzB,GAAIuL,EAASvL,KAAOwL,EAAWxL,GAAI,CACjCsK,EAAIiB,EAASvL,GACbuK,EAAIiB,EAAWxL,GACf,MAIJ,OAAIsK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAqITlI,EAAOR,UAAU6J,SAAW,SAAkBlH,EAAKlB,EAAYM,GAC7D,OAAoD,IAA7Cb,KAAKoD,QAAQ3B,EAAKlB,EAAYM,IAGvCvB,EAAOR,UAAUsE,QAAU,SAAiB3B,EAAKlB,EAAYM,GAC3D,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAG/DvB,EAAOR,UAAUuE,YAAc,SAAqB5B,EAAKlB,EAAYM,GACnE,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAsD/DvB,EAAOR,UAAUkC,MAAQ,SAAeJ,EAAQsD,EAAQxE,EAAQmB,GAE9D,QAAeJ,IAAXyD,EACFrD,EAAW,OACXnB,EAASM,KAAKN,OACdwE,EAAS,OACJ,QAAezD,IAAXf,GAA0C,iBAAXwE,EACxCrD,EAAWqD,EACXxE,EAASM,KAAKN,OACdwE,EAAS,MACJ,KAAI0E,SAAS1E,GAYlB,MAAM,IAAIjE,MAAM,2EAXhBiE,GAAkB,EAEd0E,SAASlJ,IACXA,GAAkB,OACDe,IAAbI,IAAwBA,EAAW,UAEvCA,EAAWnB,EACXA,OAASe,GAOb,IAAI2D,EAAYpE,KAAKN,OAASwE,EAG9B,SAFezD,IAAXf,GAAwBA,EAAS0E,KAAW1E,EAAS0E,GAErDxD,EAAOlB,OAAS,IAAMA,EAAS,GAAKwE,EAAS,IAAMA,EAASlE,KAAKN,OACnE,MAAM,IAAIC,WAAW,0CAGlBkB,IAAUA,EAAW,QAG1B,IAFA,IAAIoB,GAAc,IAGhB,OAAQpB,GACN,IAAK,MACH,OAAOoD,EAASjE,KAAMY,EAAQsD,EAAQxE,GAExC,IAAK,OACL,IAAK,QACH,OAAO+E,EAAUzE,KAAMY,EAAQsD,EAAQxE,GAEzC,IAAK,QACH,OAAOiF,EAAW3E,KAAMY,EAAQsD,EAAQxE,GAE1C,IAAK,SACL,IAAK,SACH,OAAOuF,EAAYjF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,SAEH,OAAOwF,EAAYlF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOyF,EAAUnF,KAAMY,EAAQsD,EAAQxE,GAEzC,QACE,GAAIuC,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,IAKtB3C,EAAOR,UAAU+J,OAAS,WACxB,MAAO,CACLnH,KAAM,SACNC,KAAMmH,MAAMhK,UAAUmC,MAAM5D,KAAK2C,KAAK+I,MAAQ/I,KAAM,KAsHxD,SAAS0C,EAAWkB,EAAKtB,EAAOC,GAC9B,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B8L,GAAOtF,OAAO0C,aAAsB,IAATxC,EAAI1G,IAGjC,OAAO8L,EAGT,SAASrG,EAAYiB,EAAKtB,EAAOC,GAC/B,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAIrF,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B8L,GAAOtF,OAAO0C,aAAaxC,EAAI1G,IAGjC,OAAO8L,EAGT,SAASxG,EAASoB,EAAKtB,EAAOC,GAC5B,IAAIlB,EAAMuC,EAAIlE,SACT4C,GAASA,EAAQ,KAAGA,EAAQ,KAC5BC,GAAOA,EAAM,GAAKA,EAAMlB,KAAKkB,EAAMlB,GAGxC,IAFA,IAAI4H,EAAM,GAED/L,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EAC7B+L,GAAOC,EAAMtF,EAAI1G,IAGnB,OAAO+L,EAGT,SAASpG,EAAae,EAAKtB,EAAOC,GAIhC,IAHA,IAAI4G,EAAQvF,EAAI3C,MAAMqB,EAAOC,GACzBoD,EAAM,GAEDzI,EAAI,EAAGA,EAAIiM,EAAMzJ,OAAQxC,GAAK,EACrCyI,GAAOjC,OAAO0C,aAAa+C,EAAMjM,GAAoB,IAAfiM,EAAMjM,EAAI,IAGlD,OAAOyI,EA4CT,SAASyD,EAAYlF,EAAQmF,EAAK3J,GAChC,GAAIwE,EAAS,GAAM,GAAKA,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBACzD,GAAIuE,EAASmF,EAAM3J,EAAQ,MAAM,IAAIC,WAAW,yCA+IlD,SAAS2J,EAAS1F,EAAKzF,EAAO+F,EAAQmF,EAAKlB,EAAKzC,GAC9C,IAAKpG,EAAO8B,SAASwC,GAAM,MAAM,IAAIxD,UAAU,+CAC/C,GAAIjC,EAAQgK,GAAOhK,EAAQuH,EAAK,MAAM,IAAI/F,WAAW,qCACrD,GAAIuE,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBAsDtD,SAAS4J,EAAkB3F,EAAKzF,EAAO+F,EAAQsF,GACzCrL,EAAQ,IAAGA,EAAQ,MAASA,EAAQ,GAExC,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,IAAMiB,EAAQ,KAAQ,GAAKqL,EAAetM,EAAI,EAAIA,MAAqC,GAA5BsM,EAAetM,EAAI,EAAIA,GAkCnG,SAASuM,EAAkB7F,EAAKzF,EAAO+F,EAAQsF,GACzCrL,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAE5C,IAAK,IAAIjB,EAAI,EAAG8G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIhH,EAAI8G,IAAK9G,EAC7D0G,EAAIM,EAAShH,GAAKiB,IAAuC,GAA5BqL,EAAetM,EAAI,EAAIA,GAAS,IAmKjE,SAASwM,EAAa9F,EAAKzF,EAAO+F,EAAQmF,EAAKlB,EAAKzC,GAClD,GAAIxB,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBACpD,GAAIuE,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBAGvC,SAASgK,EAAW/F,EAAKzF,EAAO+F,EAAQsF,EAAcI,GAMpD,OALKA,GACHF,EAAa9F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EAWlB,SAAS2F,EAAYjG,EAAKzF,EAAO+F,EAAQsF,EAAcI,GAMrD,OALKA,GACHF,EAAa9F,EAAKzF,EAAO+F,EAAQ,GAGnC/E,EAAQ6B,MAAM4C,EAAKzF,EAAO+F,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EA5dlB5E,EAAOR,UAAUmC,MAAQ,SAAeqB,EAAOC,GAC7C,IAmBIuH,EAnBAzI,EAAMrB,KAAKN,OAqBf,IApBA4C,IAAUA,GAGE,GACVA,GAASjB,GACG,IAAGiB,EAAQ,GACdA,EAAQjB,IACjBiB,EAAQjB,IANVkB,OAAc9B,IAAR8B,EAAoBlB,IAAQkB,GASxB,GACRA,GAAOlB,GACG,IAAGkB,EAAM,GACVA,EAAMlB,IACfkB,EAAMlB,GAGJkB,EAAMD,IAAOC,EAAMD,GAGnBhD,EAAOC,qBACTuK,EAAS9J,KAAK4G,SAAStE,EAAOC,IACvB1C,UAAYP,EAAOR,cACrB,CACL,IAAIiL,EAAWxH,EAAMD,EACrBwH,EAAS,IAAIxK,EAAOyK,OAAUtJ,GAE9B,IAAK,IAAIvD,EAAI,EAAGA,EAAI6M,IAAY7M,EAC9B4M,EAAO5M,GAAK8C,KAAK9C,EAAIoF,GAIzB,OAAOwH,GAYTxK,EAAOR,UAAUkL,WAAa,SAAoB9F,EAAQ1D,EAAYoJ,GACpE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACN/M,EAAI,IAECA,EAAIsD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAAShH,GAAK+M,EAG5B,OAAOxI,GAGTnC,EAAOR,UAAUoL,WAAa,SAAoBhG,EAAQ1D,EAAYoJ,GACpE1F,GAAkB,EAClB1D,GAA0B,EAErBoJ,GACHR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAMvC,IAHA,IAAI+B,EAAMzB,KAAKkE,IAAW1D,GACtByJ,EAAM,EAEHzJ,EAAa,IAAMyJ,GAAO,MAC/BxI,GAAOzB,KAAKkE,IAAW1D,GAAcyJ,EAGvC,OAAOxI,GAGTnC,EAAOR,UAAUqL,UAAY,SAAmBjG,EAAQ0F,GAEtD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAGd5E,EAAOR,UAAUsL,aAAe,SAAsBlG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,GAG5C5E,EAAOR,UAAU+E,aAAe,SAAsBK,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,EAAIlE,KAAKkE,EAAS,IAG3C5E,EAAOR,UAAUuL,aAAe,SAAsBnG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,SACnCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,IAAyB,SAAnBlE,KAAKkE,EAAS,IAGzF5E,EAAOR,UAAUwL,aAAe,SAAsBpG,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACrB,SAAfM,KAAKkE,IAAuBlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,KAGpG5E,EAAOR,UAAUyL,UAAY,SAAmBrG,EAAQ1D,EAAYoJ,GAClE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACN/M,EAAI,IAECA,EAAIsD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAAShH,GAAK+M,EAK5B,OADIxI,IADJwI,GAAO,OACSxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAChCiB,GAGTnC,EAAOR,UAAU2L,UAAY,SAAmBvG,EAAQ1D,EAAYoJ,GAClE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAJA,IAAIxC,EAAIsD,EACJyJ,EAAM,EACNxI,EAAMzB,KAAKkE,IAAWhH,GAEnBA,EAAI,IAAM+M,GAAO,MACtBxI,GAAOzB,KAAKkE,IAAWhH,GAAK+M,EAK9B,OADIxI,IADJwI,GAAO,OACSxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAChCiB,GAGTnC,EAAOR,UAAU4L,SAAW,SAAkBxG,EAAQ0F,GAEpD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACtB,IAAfM,KAAKkE,IACyB,GAA5B,IAAOlE,KAAKkE,GAAU,GADKlE,KAAKkE,IAI1C5E,EAAOR,UAAU6L,YAAc,SAAqBzG,EAAQ0F,GACrDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAU8L,YAAc,SAAqB1G,EAAQ0F,GACrDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,EAAS,GAAKlE,KAAKkE,IAAW,EAC7C,OAAa,MAANzC,EAAqB,WAANA,EAAmBA,GAG3CnC,EAAOR,UAAU+L,YAAc,SAAqB3G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAUlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,IAG7F5E,EAAOR,UAAUgM,YAAc,SAAqB5G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAAW,GAAKlE,KAAKkE,EAAS,IAAM,GAAKlE,KAAKkE,EAAS,IAAM,EAAIlE,KAAKkE,EAAS,IAG7F5E,EAAOR,UAAUiM,YAAc,SAAqB7G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUkM,YAAc,SAAqB9G,EAAQ0F,GAE1D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAG/C5E,EAAOR,UAAUmM,aAAe,SAAsB/G,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOR,UAAUoM,aAAe,SAAsBhH,EAAQ0F,GAE5D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAS/C5E,EAAOR,UAAUqM,YAAc,SAAqBhN,EAAO+F,EAAQ1D,EAAYoJ,IAC7EzL,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBoJ,IAEHN,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAIyJ,EAAM,EACN/M,EAAI,EAGR,IAFA8C,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAeyJ,GAAO,MACjCjK,KAAKkE,EAAShH,GAAKiB,EAAQ8L,EAAM,IAGnC,OAAO/F,EAAS1D,GAGlBlB,EAAOR,UAAUsM,YAAc,SAAqBjN,EAAO+F,EAAQ1D,EAAYoJ,IAC7EzL,GAASA,EACT+F,GAAkB,EAClB1D,GAA0B,EAErBoJ,IAEHN,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAItD,EAAIsD,EAAa,EACjByJ,EAAM,EAGV,IAFAjK,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAM+M,GAAO,MACzBjK,KAAKkE,EAAShH,GAAKiB,EAAQ8L,EAAM,IAGnC,OAAO/F,EAAS1D,GAGlBlB,EAAOR,UAAUuM,WAAa,SAAoBlN,EAAO+F,EAAQ0F,GAM/D,OALAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,IAAM,GACjD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK6F,MAAMnN,IACpD6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAWlB5E,EAAOR,UAAUyM,cAAgB,SAAuBpN,EAAO+F,EAAQ0F,GAYrE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU0M,cAAgB,SAAuBrN,EAAO+F,EAAQ0F,GAYrE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,MAAQ,GAEpD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAWlB5E,EAAOR,UAAU2M,cAAgB,SAAuBtN,EAAO+F,EAAQ0F,GAcrE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,GAAkB,IAAR/F,GAEfsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU4M,cAAgB,SAAuBvN,EAAO+F,EAAQ0F,GAcrE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,WAAY,GAExD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAU6M,WAAa,SAAoBxN,EAAO+F,EAAQ1D,EAAYoJ,GAI3E,GAHAzL,GAASA,EACT+F,GAAkB,GAEb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GACzC8I,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI1O,EAAI,EACJ+M,EAAM,EACN4B,EAAM,EAGV,IAFA7L,KAAKkE,GAAkB,IAAR/F,IAENjB,EAAIsD,IAAeyJ,GAAO,MAC7B9L,EAAQ,GAAa,IAAR0N,GAAsC,IAAzB7L,KAAKkE,EAAShH,EAAI,KAC9C2O,EAAM,GAGR7L,KAAKkE,EAAShH,IAAMiB,EAAQ8L,GAAO,GAAK4B,EAAM,IAGhD,OAAO3H,EAAS1D,GAGlBlB,EAAOR,UAAUgN,WAAa,SAAoB3N,EAAO+F,EAAQ1D,EAAYoJ,GAI3E,GAHAzL,GAASA,EACT+F,GAAkB,GAEb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GACzC8I,EAAStJ,KAAM7B,EAAO+F,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI1O,EAAIsD,EAAa,EACjByJ,EAAM,EACN4B,EAAM,EAGV,IAFA7L,KAAKkE,EAAShH,GAAa,IAARiB,IAEVjB,GAAK,IAAM+M,GAAO,MACrB9L,EAAQ,GAAa,IAAR0N,GAAsC,IAAzB7L,KAAKkE,EAAShH,EAAI,KAC9C2O,EAAM,GAGR7L,KAAKkE,EAAShH,IAAMiB,EAAQ8L,GAAO,GAAK4B,EAAM,IAGhD,OAAO3H,EAAS1D,GAGlBlB,EAAOR,UAAUiN,UAAY,SAAmB5N,EAAO+F,EAAQ0F,GAO7D,OANAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,KAAO,KAClD5E,EAAOC,sBAAqBpB,EAAQsH,KAAK6F,MAAMnN,IAChDA,EAAQ,IAAGA,EAAQ,IAAOA,EAAQ,GACtC6B,KAAKkE,GAAkB,IAAR/F,EACR+F,EAAS,GAGlB5E,EAAOR,UAAUkN,aAAe,SAAsB7N,EAAO+F,EAAQ0F,GAYnE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,GAE7BoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUmN,aAAe,SAAsB9N,EAAO+F,EAAQ0F,GAYnE,OAXAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,OAAS,OAErD5E,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,EACzB6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBoL,EAAkBvJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUoN,aAAe,SAAsB/N,EAAO+F,EAAQ0F,GAcnE,OAbAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YAEzD5E,EAAOC,qBACTS,KAAKkE,GAAkB,IAAR/F,EACf6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,IAE7BsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAGlB5E,EAAOR,UAAUqN,aAAe,SAAsBhO,EAAO+F,EAAQ0F,GAenE,OAdAzL,GAASA,EACT+F,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM7B,EAAO+F,EAAQ,EAAG,YAAa,YACzD/F,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAExCmB,EAAOC,qBACTS,KAAKkE,GAAU/F,IAAU,GACzB6B,KAAKkE,EAAS,GAAK/F,IAAU,GAC7B6B,KAAKkE,EAAS,GAAK/F,IAAU,EAC7B6B,KAAKkE,EAAS,GAAa,IAAR/F,GAEnBsL,EAAkBzJ,KAAM7B,EAAO+F,GAAQ,GAGlCA,EAAS,GAiBlB5E,EAAOR,UAAUsN,aAAe,SAAsBjO,EAAO+F,EAAQ0F,GACnE,OAAOD,EAAW3J,KAAM7B,EAAO+F,GAAQ,EAAM0F,IAG/CtK,EAAOR,UAAUuN,aAAe,SAAsBlO,EAAO+F,EAAQ0F,GACnE,OAAOD,EAAW3J,KAAM7B,EAAO+F,GAAQ,EAAO0F,IAYhDtK,EAAOR,UAAUwN,cAAgB,SAAuBnO,EAAO+F,EAAQ0F,GACrE,OAAOC,EAAY7J,KAAM7B,EAAO+F,GAAQ,EAAM0F,IAGhDtK,EAAOR,UAAUyN,cAAgB,SAAuBpO,EAAO+F,EAAQ0F,GACrE,OAAOC,EAAY7J,KAAM7B,EAAO+F,GAAQ,EAAO0F,IAIjDtK,EAAOR,UAAUyC,KAAO,SAAc+G,EAAQkE,EAAalK,EAAOC,GAOhE,GANKD,IAAOA,EAAQ,GACfC,GAAe,IAARA,IAAWA,EAAMvC,KAAKN,QAC9B8M,GAAelE,EAAO5I,SAAQ8M,EAAclE,EAAO5I,QAClD8M,IAAaA,EAAc,GAC5BjK,EAAM,GAAKA,EAAMD,IAAOC,EAAMD,GAE9BC,IAAQD,EAAO,OAAO,EAC1B,GAAsB,IAAlBgG,EAAO5I,QAAgC,IAAhBM,KAAKN,OAAc,OAAO,EAErD,GAAI8M,EAAc,EAChB,MAAM,IAAI7M,WAAW,6BAGvB,GAAI2C,EAAQ,GAAKA,GAAStC,KAAKN,OAAQ,MAAM,IAAIC,WAAW,6BAC5D,GAAI4C,EAAM,EAAG,MAAM,IAAI5C,WAAW,2BAE9B4C,EAAMvC,KAAKN,SAAQ6C,EAAMvC,KAAKN,QAE9B4I,EAAO5I,OAAS8M,EAAcjK,EAAMD,IACtCC,EAAM+F,EAAO5I,OAAS8M,EAAclK,GAGtC,IACIpF,EADAmE,EAAMkB,EAAMD,EAGhB,GAAItC,OAASsI,GAAUhG,EAAQkK,GAAeA,EAAcjK,EAE1D,IAAKrF,EAAImE,EAAM,EAAGnE,GAAK,IAAKA,EAC1BoL,EAAOpL,EAAIsP,GAAexM,KAAK9C,EAAIoF,QAEhC,GAAIjB,EAAM,MAAS/B,EAAOC,oBAE/B,IAAKrC,EAAI,EAAGA,EAAImE,IAAOnE,EACrBoL,EAAOpL,EAAIsP,GAAexM,KAAK9C,EAAIoF,QAGrC1C,WAAWd,UAAU2N,IAAIpP,KAAKiL,EAAQtI,KAAK4G,SAAStE,EAAOA,EAAQjB,GAAMmL,GAG3E,OAAOnL,GAOT/B,EAAOR,UAAUqI,KAAO,SAAc1F,EAAKa,EAAOC,EAAK1B,GAErD,GAAmB,iBAARY,EAAkB,CAU3B,GATqB,iBAAVa,GACTzB,EAAWyB,EACXA,EAAQ,EACRC,EAAMvC,KAAKN,QACa,iBAAR6C,IAChB1B,EAAW0B,EACXA,EAAMvC,KAAKN,QAGM,IAAf+B,EAAI/B,OAAc,CACpB,IAAIgN,EAAOjL,EAAIsD,WAAW,GAEtB2H,EAAO,MACTjL,EAAMiL,GAIV,QAAiBjM,IAAbI,GAA8C,iBAAbA,EACnC,MAAM,IAAIT,UAAU,6BAGtB,GAAwB,iBAAbS,IAA0BvB,EAAOwB,WAAWD,GACrD,MAAM,IAAIT,UAAU,qBAAuBS,OAErB,iBAARY,IAChBA,GAAY,KAId,GAAIa,EAAQ,GAAKtC,KAAKN,OAAS4C,GAAStC,KAAKN,OAAS6C,EACpD,MAAM,IAAI5C,WAAW,sBAGvB,GAAI4C,GAAOD,EACT,OAAOtC,KAMT,IAAI9C,EAEJ,GALAoF,KAAkB,EAClBC,OAAc9B,IAAR8B,EAAoBvC,KAAKN,OAAS6C,IAAQ,EAC3Cd,IAAKA,EAAM,GAGG,iBAARA,EACT,IAAKvE,EAAIoF,EAAOpF,EAAIqF,IAAOrF,EACzB8C,KAAK9C,GAAKuE,MAEP,CACL,IAAI0H,EAAQ7J,EAAO8B,SAASK,GAAOA,EAAMS,EAAY,IAAI5C,EAAOmC,EAAKZ,GAAUkB,YAC3EV,EAAM8H,EAAMzJ,OAEhB,IAAKxC,EAAI,EAAGA,EAAIqF,EAAMD,IAASpF,EAC7B8C,KAAK9C,EAAIoF,GAAS6G,EAAMjM,EAAImE,GAIhC,OAAOrB,MAKT,IAAI2M,EAAoB,qBAoBxB,SAASzD,EAAMvK,GACb,OAAIA,EAAI,GAAW,IAAMA,EAAEoD,SAAS,IAC7BpD,EAAEoD,SAAS,IAGpB,SAASG,EAAYtB,EAAQwE,GAE3B,IAAIa,EADJb,EAAQA,GAASwH,IAMjB,IAJA,IAAIlN,EAASkB,EAAOlB,OAChBmN,EAAgB,KAChB1D,EAAQ,GAEHjM,EAAI,EAAGA,EAAIwC,IAAUxC,EAAG,CAG/B,IAFA+I,EAAYrF,EAAOmE,WAAW7H,IAEd,OAAU+I,EAAY,MAAQ,CAE5C,IAAK4G,EAAe,CAElB,GAAI5G,EAAY,MAAQ,EAEjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SACK,GAAI5H,EAAI,IAAMwC,EAAQ,EAEtB0F,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SAIF+H,EAAgB5G,EAChB,SAIF,GAAIA,EAAY,MAAQ,EACjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C+H,EAAgB5G,EAChB,SAIFA,EAAkE,OAArD4G,EAAgB,OAAU,GAAK5G,EAAY,YAC/C4G,IAEJzH,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAKhD,GAFA+H,EAAgB,KAEZ5G,EAAY,IAAM,CACpB,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,QACN,GAAIA,EAAY,KAAO,CAC5B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,EAAM,IAAkB,GAAZA,EAAmB,UAClD,GAAIA,EAAY,MAAS,CAC9B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,GAAM,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,SAClF,MAAIA,EAAY,SAIrB,MAAM,IAAIhG,MAAM,sBAHhB,IAAKmF,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,GAAa,GAAO,IAAMA,GAAa,GAAM,GAAO,IAAMA,GAAa,EAAM,GAAO,IAAkB,GAAZA,EAAmB,MAM5H,OAAOkD,EA8BT,SAAShH,EAAcyC,GACrB,OAAO1F,EAAO4N,YApHhB,SAAqBlI,GAInB,IAFAA,EAWF,SAAoBA,GAClB,OAAIA,EAAImI,KAAanI,EAAImI,OAClBnI,EAAIoI,QAAQ,aAAc,IAb3BC,CAAWrI,GAAKoI,QAAQL,EAAmB,KAEzCjN,OAAS,EAAG,MAAO,GAE3B,KAAOkF,EAAIlF,OAAS,GAAM,GACxBkF,GAAY,IAGd,OAAOA,EA0GmBsI,CAAYtI,IAGxC,SAASF,EAAWyI,EAAKC,EAAKlJ,EAAQxE,GACpC,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,KACdxC,EAAIgH,GAAUkJ,EAAI1N,QAAUxC,GAAKiQ,EAAIzN,UADbxC,EAE5BkQ,EAAIlQ,EAAIgH,GAAUiJ,EAAIjQ,GAGxB,OAAOA,K,gvQCrzDTP,EAAOD,QAAU,CACf2Q,OAAQ,EAAQ,IAChBC,OAAQ,EAAQ,IAChBC,eAAgB,EAAQ,M,8BCL1B,YAOA,MAAMC,EAAO,EAAQ,IAEfC,EAAK,EAAQ,IAEnB/Q,EAAQgR,MAAQD,EAAGC,MACnBhR,EAAQiR,MAAQF,EAAGE,MACnBjR,EAAQkR,eAAiBH,EAAGG,eAE5B,MAAMC,EAAS,EAAQ,GA4MvB,SAASC,EAASC,GAChBrR,EAAQ4Q,OAAOS,GApMjBrR,EAAQsR,YAAc,SAAqBC,GACzC,IAAK3O,EAAO8B,SAAS6M,GACnB,MAAM,IAAIhO,MAAM,2BAGlB,OAAOgO,EAAKlM,SAAS,QAUvBrF,EAAQwR,cAAgB,SAAuBD,GAC7C,OAAO3O,EAAOa,KAAK8N,EAAM,QAU3BvR,EAAQyR,YAAc,SAAqBF,GACzC,IAAK3O,EAAO8B,SAAS6M,GACnB,MAAM,IAAIhO,MAAM,2BAGlB,OAAOuN,EAAKH,OAAOY,IAUrBvR,EAAQ0R,cAAgB,SAAuBH,GAC7C,IAAII,EAAUJ,EAMd,OAJI3O,EAAO8B,SAAS6M,KAClBI,EAAUJ,EAAKlM,YAGVzC,EAAOa,KAAKqN,EAAKF,OAAOe,KAUjC3R,EAAQ4Q,OAAS,SAAgB1J,GAC/B,IAAKtE,EAAO8B,SAASwC,GACnB,MAAM,IAAI3D,MAAM,8BAGlB,GAAI2D,EAAIlE,OAAS,EACf,MAAM,IAAIO,MAAM,2CAGlB,MAAMyM,EAAOmB,EAAOP,OAAO1J,GAE3B,IAAKlH,EAAQ4R,YAAY5B,GACvB,MAAM,IAAIzM,MAAM,sCAAsCyH,OAAOgF,EAAK3K,SAAS,MAG7E6B,EAAMA,EAAI3C,MAAM4M,EAAOP,OAAOnE,OAC9B,MAAM9H,EAAMwM,EAAOP,OAAO1J,GAE1B,GAAIvC,EAAM,EACR,MAAM,IAAIpB,MAAM,+BAA+ByH,OAAOrG,EAAIU,SAAS,MAKrE,IAFA6B,EAAMA,EAAI3C,MAAM4M,EAAOP,OAAOnE,QAEtBzJ,SAAW2B,EACjB,MAAM,IAAIpB,MAAM,oCAAoCyH,OAAO9D,EAAI7B,SAAS,SAG1E,MAAO,CACL2K,KAAMA,EACNjP,KAAMgQ,EAAGE,MAAMjB,GACfhN,OAAQ2B,EACRkN,OAAQ3K,IAeZlH,EAAQ2Q,OAAS,SAAgBkB,EAAQ7B,EAAMhN,GAC7C,IAAK6O,QAAmB9N,IAATiM,EACb,MAAM,IAAIzM,MAAM,6DAIlB,MAAMuO,EAAS9R,EAAQ+R,WAAW/B,GAElC,IAAKpN,EAAO8B,SAASmN,GACnB,MAAM,IAAItO,MAAM,6BAOlB,GAJc,MAAVP,IACFA,EAAS6O,EAAO7O,QAGdA,GAAU6O,EAAO7O,SAAWA,EAC9B,MAAM,IAAIO,MAAM,sDAGlB,OAAOX,EAAOoI,OAAO,CAACpI,EAAOa,KAAK0N,EAAOR,OAAOmB,IAAUlP,EAAOa,KAAK0N,EAAOR,OAAO3N,IAAU6O,KAUhG7R,EAAQ+R,WAAa,SAAoBhR,GACvC,IAAIiP,EAAOjP,EAEX,GAAoB,iBAATA,EAAmB,CAC5B,QAAuBgD,IAAnBgN,EAAGC,MAAMjQ,GACX,MAAM,IAAIwC,MAAM,qCAAqCyH,OAAOjK,IAG9DiP,EAAOe,EAAGC,MAAMjQ,GAGlB,GAAoB,iBAATiP,EACT,MAAM,IAAIzM,MAAM,+CAA+CyH,OAAOgF,IAGxE,QAAuBjM,IAAnBgN,EAAGE,MAAMjB,KAAwBhQ,EAAQgS,UAAUhC,GACrD,MAAM,IAAIzM,MAAM,+BAA+ByH,OAAOgF,IAGxD,OAAOA,GAUThQ,EAAQgS,UAAY,SAAiBhC,GACnC,OAAOA,EAAO,GAAKA,EAAO,IAU5BhQ,EAAQ4R,YAAc,SAAmB5B,GACvC,QAAIhQ,EAAQgS,UAAUhC,MAIlBe,EAAGE,MAAMjB,IAmBfhQ,EAAQoR,SAAWA,EASnBpR,EAAQiS,OAAS,SAAgBZ,GAE/B,OADAD,EAASC,GACFA,EAAU9M,MAAM,EAAG,M,qDCjO5B,IAAI3B,EAAS,EAAQ,IAAeA,OAEpC3C,EAAOD,QAAU,SAAckS,GAK7B,IAJA,IAAIC,EAAe,GACfC,EAAOF,EAASlP,OAChBqP,EAASH,EAASI,OAAO,GAEpBC,EAAI,EAAGA,EAAIL,EAASlP,OAAQuP,IAAK,CACxC,IAAIzH,EAAIoH,EAASI,OAAOC,GACxB,QAAwBxO,IAApBoO,EAAarH,GAAkB,MAAM,IAAIpH,UAAUoH,EAAI,iBAC3DqH,EAAarH,GAAKyH,EA8BpB,SAASC,EAAatO,GACpB,GAAsB,iBAAXA,EAAqB,MAAM,IAAIR,UAAU,mBACpD,GAAsB,IAAlBQ,EAAOlB,OAAc,OAAOJ,EAAOY,YAAY,GAGnD,IAFA,IAAIiJ,EAAQ,CAAC,GAEJjM,EAAI,EAAGA,EAAI0D,EAAOlB,OAAQxC,IAAK,CACtC,IAAIiB,EAAQ0Q,EAAajO,EAAO1D,IAChC,QAAcuD,IAAVtC,EAAqB,OAEzB,IAAK,IAAI6F,EAAI,EAAGmL,EAAQhR,EAAO6F,EAAImF,EAAMzJ,SAAUsE,EACjDmL,GAAShG,EAAMnF,GAAK8K,EACpB3F,EAAMnF,GAAa,IAARmL,EACXA,IAAU,EAGZ,KAAOA,EAAQ,GACbhG,EAAMrE,KAAa,IAARqK,GACXA,IAAU,EAKd,IAAK,IAAIC,EAAI,EAAGxO,EAAOwO,KAAOL,GAAUK,EAAIxO,EAAOlB,OAAS,IAAK0P,EAC/DjG,EAAMrE,KAAK,GAGb,OAAOxF,EAAOa,KAAKgJ,EAAMkG,WAS3B,MAAO,CACLhC,OA/DF,SAAgBiC,GACd,GAAsB,IAAlBA,EAAO5P,OAAc,MAAO,GAGhC,IAFA,IAAI6P,EAAS,CAAC,GAELrS,EAAI,EAAGA,EAAIoS,EAAO5P,SAAUxC,EAAG,CACtC,IAAK,IAAI8G,EAAI,EAAGmL,EAAQG,EAAOpS,GAAI8G,EAAIuL,EAAO7P,SAAUsE,EACtDmL,GAASI,EAAOvL,IAAM,EACtBuL,EAAOvL,GAAKmL,EAAQL,EACpBK,EAAQA,EAAQL,EAAO,EAGzB,KAAOK,EAAQ,GACbI,EAAOzK,KAAKqK,EAAQL,GACpBK,EAAQA,EAAQL,EAAO,EAM3B,IAFA,IAAIlO,EAAS,GAEJwO,EAAI,EAAiB,IAAdE,EAAOF,IAAYA,EAAIE,EAAO5P,OAAS,IAAK0P,EAAGxO,GAAUmO,EAGzE,IAAK,IAAIS,EAAID,EAAO7P,OAAS,EAAG8P,GAAK,IAAKA,EAAG5O,GAAUgO,EAASW,EAAOC,IAEvE,OAAO5O,GAwCPsO,aAAcA,EACd5B,OATF,SAAgB1M,GACd,IAAIY,EAAS0N,EAAatO,GAC1B,GAAIY,EAAQ,OAAOA,EACnB,MAAM,IAAIvB,MAAM,WAAa6O,EAAO,kB,8BCjFxC,YAEA,MAAMjB,EAAS,EAAQ,GAUvB,SAAS4B,EAAe7L,GACtB,OAAOW,SAASX,EAAI7B,SAAS,OAAQ,IAGvC,SAAS2N,EAAeC,GACtB,IAAIC,EAAYD,EAAI5N,SAAS,IAM7B,OAJI6N,EAAUlQ,OAAS,GAAM,IAC3BkQ,EAAY,IAAMA,GAGbtQ,EAAOa,KAAKyP,EAAW,OAnBhCjT,EAAOD,QAAU,CACfgT,iBACAD,iBACAI,mBAmBF,SAA4BC,GAC1B,OAAOxQ,EAAOa,KAAK0N,EAAOR,OAAOoC,EAAeK,MAnBhDC,mBAsBF,SAA4BD,GAC1B,OAAOJ,EAAe7B,EAAOP,OAAOwC,KAtBpCE,aAyBF,SAAsBL,GACpB,OAAOrQ,EAAOa,KAAK0N,EAAOR,OAAOsC,Q,qFCjCnC,MAAM,OAAErQ,GAAW2Q,EAAQ,GACrBC,EAAKD,EAAQ,GACbE,EAAYF,EAAQ,IACpBG,EAAaH,EAAQ,IACrBI,EAASJ,EAAQ,GACjBK,EAAUL,EAAQ,IAwBxB,MAAMM,EA+BJC,YAAaC,EAASC,EAAO3C,EAAW4C,GACtC,GAAIC,EAAKC,MAAMJ,GAAU,CAEvB,MAAMK,EAAML,EAMZ,OALAzQ,KAAKyQ,QAAUK,EAAIL,QACnBzQ,KAAK0Q,MAAQI,EAAIJ,MACjB1Q,KAAK+N,UAAYzO,EAAOa,KAAK2Q,EAAI/C,gBAEjC/N,KAAK2Q,cAAgBG,EAAIH,gBAAkC,IAAhBG,EAAIL,QAAgB,YAAc,WAI/E,GAAuB,iBAAZA,EAAsB,CAE/B,MAAMM,EAAWZ,EAAUa,UAAUP,GACrC,GAAIM,EAAU,CAEZ,MAAMD,EAAMX,EAAU7C,OAAOmD,GAC7BzQ,KAAKyQ,QAAUlM,SAASuM,EAAI7P,MAAM,EAAG,GAAGc,SAAS,OAAQ,IACzD/B,KAAK0Q,MAAQN,EAAWa,SAASH,EAAI7P,MAAM,IAC3CjB,KAAK+N,UAAYqC,EAAWc,SAASJ,EAAI7P,MAAM,IAC/CjB,KAAK2Q,cAAgBI,OAGrB/Q,KAAKyQ,QAAU,EACfzQ,KAAK0Q,MAAQ,SACb1Q,KAAK+N,UAAYmC,EAAG9B,cAAcqC,GAClCzQ,KAAK2Q,cAAgB,YAIvB,OAFAJ,EAAIY,YAAYnR,WAChBpC,OAAOC,eAAemC,KAAM,SAAU,CAAE7B,MAAOsS,IAIjD,GAAInR,EAAO8B,SAASqP,GAApB,CACE,MAAMzK,EAAYyK,EAAQxP,MAAM,EAAG,GAC7BmQ,EAAI7M,SAASyB,EAAUjE,SAAS,OAAQ,IAC9C,GAAU,IAANqP,EAAS,CAEX,MAAMN,EAAML,EACZzQ,KAAKyQ,QAAUW,EACfpR,KAAK0Q,MAAQN,EAAWa,SAASH,EAAI7P,MAAM,IAC3CjB,KAAK+N,UAAYqC,EAAWc,SAASJ,EAAI7P,MAAM,IAC/CjB,KAAK2Q,cAAgB,cAGrB3Q,KAAKyQ,QAAU,EACfzQ,KAAK0Q,MAAQ,SACb1Q,KAAK+N,UAAY0C,EACjBzQ,KAAK2Q,cAAgB,YAEvBJ,EAAIY,YAAYnR,WASlBA,KAAKyQ,QAAUA,EAKfzQ,KAAK0Q,MAAQA,EAKb1Q,KAAK+N,UAAYA,EAKjB/N,KAAK2Q,cAAgBA,IAA8B,IAAZF,EAAgB,YAAc,UAErEF,EAAIY,YAAYnR,MAWlB,aACE,IAAIwB,EAASxB,KAAKqR,QAElB,IAAK7P,EAAQ,CACX,GAAqB,IAAjBxB,KAAKyQ,QACPjP,EAASxB,KAAK+N,cACT,IAAqB,IAAjB/N,KAAKyQ,QAOd,MAAM,IAAIxQ,MAAM,uBANhBuB,EAASlC,EAAOoI,OAAO,CACrBpI,EAAOa,KAAK,KAAM,OAClBiQ,EAAWkB,cAActR,KAAK0Q,OAC9B1Q,KAAK+N,YAOTnQ,OAAOC,eAAemC,KAAM,UAAW,CAAE7B,MAAOqD,IAGlD,OAAOA,EAST,aACE,OAAOlC,EAAOoI,OAAO,CACnBpI,EAAOa,KAAP,WAAgBH,KAAKyQ,SAAW,OAChCL,EAAWkB,cAActR,KAAK0Q,OAC9BR,EAAGvB,OAAO3O,KAAK+N,aASnBwD,OACE,GAAmB,WAAfvR,KAAK0Q,MACP,MAAM,IAAIzQ,MAAM,4CAGlB,MAAM,KAAExC,EAAF,OAAQiC,GAAWwQ,EAAG5C,OAAOtN,KAAK+N,WAExC,GAAa,aAATtQ,EACF,MAAM,IAAIwC,MAAM,sDAGlB,GAAe,KAAXP,EACF,MAAM,IAAIO,MAAM,qDAGlB,OAAO,IAAI2Q,EAAK,EAAG5Q,KAAK0Q,MAAO1Q,KAAK+N,WAQtCyD,OACE,OAAO,IAAIZ,EAAK,EAAG5Q,KAAK0Q,MAAO1Q,KAAK+N,WAStC0D,oBAAqBC,EAAO1R,KAAK2Q,eAC/B,GAAI3Q,KAAKY,QAAU8Q,IAAS1R,KAAK2Q,cAC/B,OAAO3Q,KAAKY,OAEd,IAAIgE,EAAM,KACV,GAAqB,IAAjB5E,KAAKyQ,QAAe,CACtB,GAAa,cAATiB,EACF,MAAM,IAAIzR,MAAM,kIAElB2E,EAAMsL,EAAG/B,YAAYnO,KAAK+N,eACrB,IAAqB,IAAjB/N,KAAKyQ,QAGd,MAAM,IAAIxQ,MAAM,uBAFhB2E,EAAMuL,EAAU9C,OAAOqE,EAAM1R,KAAKwB,QAAQO,WAQ5C,OAJI2P,IAAS1R,KAAK2Q,eAEhB/S,OAAOC,eAAemC,KAAM,SAAU,CAAE7B,MAAOyG,IAE1CA,EAQT,CAAC3G,OAAO0T,IAAI,iCACV,MAAO,OAAS3R,KAAK+B,WAAa,IAGpCA,SAAU2P,GACR,OAAO1R,KAAKyR,oBAAoBC,GAQlC7I,SACE,MAAO,CACL6H,MAAO1Q,KAAK0Q,MACZD,QAASzQ,KAAKyQ,QACdxC,KAAMjO,KAAK+N,WAUf9F,OAAQ2J,GACN,OAAO5R,KAAK0Q,QAAUkB,EAAMlB,OAC1B1Q,KAAKyQ,UAAYmB,EAAMnB,SACvBzQ,KAAK+N,UAAU9F,OAAO2J,EAAM7D,WAUhC,mBAAoB6D,GAClB,MAAMC,EAAWvB,EAAQwB,mBAAmBF,GAC5C,GAAIC,EACF,MAAM,IAAI5R,MAAM4R,IAKtB,MAAMjB,EApSSX,EAAQ,GAoSV8B,CAAOxB,EAAK,CACvByB,UAAW,MACXC,WAAY,qBAGdrB,EAAKP,OAASA,EAEd1T,EAAOD,QAAUkU,G,6BCjTjB,IAAIsB,EAEJA,EAAI,WACF,OAAOlS,KADL,GAIJ,IAEEkS,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACT,MAAOtL,GAEe,iBAAX/J,SAAqBoV,EAAIpV,QAMtCH,EAAOD,QAAUwV,G,6BCjBjBxV,EAAQ8D,WAkCR,SAAoB4R,GAClB,IAAIC,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAC3B,OAAsC,GAA9BE,EAAWC,GAAuB,EAAIA,GArChD9V,EAAQoQ,YA4CR,SAAqBsF,GACnB,IAAIK,EAQAvV,EAPAmV,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GACvB/O,EAAM,IAAIoP,EAThB,SAAqBN,EAAKG,EAAUC,GAClC,OAAsC,GAA9BD,EAAWC,GAAuB,EAAIA,EAQ5BG,CAAYP,EAAKG,EAAUC,IACzCI,EAAU,EAEVvR,EAAMmR,EAAkB,EAAID,EAAW,EAAIA,EAG/C,IAAKrV,EAAI,EAAGA,EAAImE,EAAKnE,GAAK,EACxBuV,EAAMI,EAAUT,EAAIrN,WAAW7H,KAAO,GAAK2V,EAAUT,EAAIrN,WAAW7H,EAAI,KAAO,GAAK2V,EAAUT,EAAIrN,WAAW7H,EAAI,KAAO,EAAI2V,EAAUT,EAAIrN,WAAW7H,EAAI,IACzJoG,EAAIsP,KAAaH,GAAO,GAAK,IAC7BnP,EAAIsP,KAAaH,GAAO,EAAI,IAC5BnP,EAAIsP,KAAmB,IAANH,EAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAIrN,WAAW7H,KAAO,EAAI2V,EAAUT,EAAIrN,WAAW7H,EAAI,KAAO,EAC9EoG,EAAIsP,KAAmB,IAANH,GAGK,IAApBD,IACFC,EAAMI,EAAUT,EAAIrN,WAAW7H,KAAO,GAAK2V,EAAUT,EAAIrN,WAAW7H,EAAI,KAAO,EAAI2V,EAAUT,EAAIrN,WAAW7H,EAAI,KAAO,EACvHoG,EAAIsP,KAAaH,GAAO,EAAI,IAC5BnP,EAAIsP,KAAmB,IAANH,GAGnB,OAAOnP,GAxET5G,EAAQ8I,cA2FR,SAAuBsN,GASrB,IARA,IAAIL,EACApR,EAAMyR,EAAMpT,OACZqT,EAAa1R,EAAM,EAEnB2R,EAAQ,GAIH9V,EAAI,EAAG+V,EAAO5R,EAAM0R,EAAY7V,EAAI+V,EAAM/V,GAH9B,MAInB8V,EAAMlO,KAAKoO,EAAYJ,EAAO5V,EAAGA,EAJd,MAImC+V,EAAOA,EAAO/V,EAJjD,QAQF,IAAf6V,GACFN,EAAMK,EAAMzR,EAAM,GAClB2R,EAAMlO,KAAKqO,EAAOV,GAAO,GAAKU,EAAOV,GAAO,EAAI,IAAQ,OAChC,IAAfM,IACTN,GAAOK,EAAMzR,EAAM,IAAM,GAAKyR,EAAMzR,EAAM,GAC1C2R,EAAMlO,KAAKqO,EAAOV,GAAO,IAAMU,EAAOV,GAAO,EAAI,IAAQU,EAAOV,GAAO,EAAI,IAAQ,MAGrF,OAAOO,EAAM3K,KAAK,KA3GpB,IALA,IAAI8K,EAAS,GACTN,EAAY,GACZH,EAA4B,oBAAf9S,WAA6BA,WAAakJ,MACvD4D,EAAO,mEAEFxP,EAAI,EAAGmE,EAAMqL,EAAKhN,OAAQxC,EAAImE,IAAOnE,EAC5CiW,EAAOjW,GAAKwP,EAAKxP,GACjB2V,EAAUnG,EAAK3H,WAAW7H,IAAMA,EAQlC,SAASoV,EAAQF,GACf,IAAI/Q,EAAM+Q,EAAI1S,OAEd,GAAI2B,EAAM,EAAI,EACZ,MAAM,IAAIpB,MAAM,kDAKlB,IAAIsS,EAAWH,EAAIhP,QAAQ,KAG3B,OAFkB,IAAdmP,IAAiBA,EAAWlR,GAEzB,CAACkR,EADcA,IAAalR,EAAM,EAAI,EAAIkR,EAAW,GAoD9D,SAASW,EAAYJ,EAAOxQ,EAAOC,GAIjC,IAHA,IAAIkQ,EALmB9C,EAMnByD,EAAS,GAEJlW,EAAIoF,EAAOpF,EAAIqF,EAAKrF,GAAK,EAChCuV,GAAOK,EAAM5V,IAAM,GAAK,WAAa4V,EAAM5V,EAAI,IAAM,EAAI,QAA0B,IAAf4V,EAAM5V,EAAI,IAC9EkW,EAAOtO,KATFqO,GADgBxD,EAUO8C,IATT,GAAK,IAAQU,EAAOxD,GAAO,GAAK,IAAQwD,EAAOxD,GAAO,EAAI,IAAQwD,EAAa,GAANxD,IAY9F,OAAOyD,EAAO/K,KAAK,IA3ErBwK,EAAU,IAAI9N,WAAW,IAAM,GAC/B8N,EAAU,IAAI9N,WAAW,IAAM,I,6BChB/BrI,EAAQiH,KAAO,SAAUnC,EAAQ0C,EAAQmP,EAAMC,EAAMC,GACnD,IAAI1M,EAAGvJ,EACHkW,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBE,GAAS,EACTzW,EAAImW,EAAOE,EAAS,EAAI,EACxB/V,EAAI6V,GAAQ,EAAI,EAChBpU,EAAIuC,EAAO0C,EAAShH,GAMxB,IALAA,GAAKM,EACLqJ,EAAI5H,GAAK,IAAM0U,GAAS,EACxB1U,KAAO0U,EACPA,GAASH,EAEFG,EAAQ,EAAG9M,EAAQ,IAAJA,EAAUrF,EAAO0C,EAAShH,GAAIA,GAAKM,EAAGmW,GAAS,GAMrE,IAJArW,EAAIuJ,GAAK,IAAM8M,GAAS,EACxB9M,KAAO8M,EACPA,GAASL,EAEFK,EAAQ,EAAGrW,EAAQ,IAAJA,EAAUkE,EAAO0C,EAAShH,GAAIA,GAAKM,EAAGmW,GAAS,GAErE,GAAU,IAAN9M,EACFA,EAAI,EAAI6M,MACH,IAAI7M,IAAM4M,EACf,OAAOnW,EAAIsW,IAAqBhH,KAAd3N,GAAK,EAAI,GAE3B3B,GAAQmI,KAAK+E,IAAI,EAAG8I,GACpBzM,GAAQ6M,EAGV,OAAQzU,GAAK,EAAI,GAAK3B,EAAImI,KAAK+E,IAAI,EAAG3D,EAAIyM,IAG5C5W,EAAQsE,MAAQ,SAAUQ,EAAQrD,EAAO+F,EAAQmP,EAAMC,EAAMC,GAC3D,IAAI1M,EAAGvJ,EAAGC,EACNiW,EAAgB,EAATD,EAAaD,EAAO,EAC3BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBI,EAAc,KAATP,EAAc7N,KAAK+E,IAAI,GAAI,IAAM/E,KAAK+E,IAAI,GAAI,IAAM,EACzDtN,EAAImW,EAAO,EAAIE,EAAS,EACxB/V,EAAI6V,EAAO,GAAK,EAChBpU,EAAId,EAAQ,GAAe,IAAVA,GAAe,EAAIA,EAAQ,EAAI,EAAI,EAqCxD,IApCAA,EAAQsH,KAAKqO,IAAI3V,GAEb+E,MAAM/E,IAAUA,IAAUyO,KAC5BtP,EAAI4F,MAAM/E,GAAS,EAAI,EACvB0I,EAAI4M,IAEJ5M,EAAIpB,KAAK6F,MAAM7F,KAAKsO,IAAI5V,GAASsH,KAAKuO,KAElC7V,GAASZ,EAAIkI,KAAK+E,IAAI,GAAI3D,IAAM,IAClCA,IACAtJ,GAAK,IAILY,GADE0I,EAAI6M,GAAS,EACNG,EAAKtW,EAELsW,EAAKpO,KAAK+E,IAAI,EAAG,EAAIkJ,IAGpBnW,GAAK,IACfsJ,IACAtJ,GAAK,GAGHsJ,EAAI6M,GAASD,GACfnW,EAAI,EACJuJ,EAAI4M,GACK5M,EAAI6M,GAAS,GACtBpW,GAAKa,EAAQZ,EAAI,GAAKkI,KAAK+E,IAAI,EAAG8I,GAClCzM,GAAQ6M,IAERpW,EAAIa,EAAQsH,KAAK+E,IAAI,EAAGkJ,EAAQ,GAAKjO,KAAK+E,IAAI,EAAG8I,GACjDzM,EAAI,IAIDyM,GAAQ,EAAG9R,EAAO0C,EAAShH,GAAS,IAAJI,EAAUJ,GAAKM,EAAGF,GAAK,IAAKgW,GAAQ,GAK3E,IAHAzM,EAAIA,GAAKyM,EAAOhW,EAChBkW,GAAQF,EAEDE,EAAO,EAAGhS,EAAO0C,EAAShH,GAAS,IAAJ2J,EAAU3J,GAAKM,EAAGqJ,GAAK,IAAK2M,GAAQ,GAE1EhS,EAAO0C,EAAShH,EAAIM,IAAU,IAAJyB,I,6BCtF5B,IAAI8C,EAAW,GAAGA,SAElBpF,EAAOD,QAAUoM,MAAM1J,SAAW,SAAUkE,GAC1C,MAA6B,kBAAtBvB,EAAS1E,KAAKiG,K,6BCHvB,IAAI2Q,EAAQ,EAAQ,GAGpBtX,EAAOD,QAAUuX,EADF,+D,6BCDf,IAAIzS,EAAS,EAAQ,GAEjBlC,EAASkC,EAAOlC,OAEpB,SAAS4U,EAAU/G,EAAKC,GACtB,IAAK,IAAI3O,KAAO0O,EACdC,EAAI3O,GAAO0O,EAAI1O,GAYnB,SAAS0V,EAAWrU,EAAKC,EAAkBL,GACzC,OAAOJ,EAAOQ,EAAKC,EAAkBL,GATnCJ,EAAOa,MAAQb,EAAOkH,OAASlH,EAAOY,aAAeZ,EAAO8H,gBAC9DzK,EAAOD,QAAU8E,GAGjB0S,EAAU1S,EAAQ9E,GAClBA,EAAQ4C,OAAS6U,GAOnBA,EAAWrV,UAAYlB,OAAOY,OAAOc,EAAOR,WAE5CoV,EAAU5U,EAAQ6U,GAElBA,EAAWhU,KAAO,SAAUL,EAAKC,EAAkBL,GACjD,GAAmB,iBAARI,EACT,MAAM,IAAIM,UAAU,iCAGtB,OAAOd,EAAOQ,EAAKC,EAAkBL,IAGvCyU,EAAW3N,MAAQ,SAAU1E,EAAMqF,EAAMtG,GACvC,GAAoB,iBAATiB,EACT,MAAM,IAAI1B,UAAU,6BAGtB,IAAIwD,EAAMtE,EAAOwC,GAYjB,YAVarB,IAAT0G,EACsB,iBAAbtG,EACT+C,EAAIuD,KAAKA,EAAMtG,GAEf+C,EAAIuD,KAAKA,GAGXvD,EAAIuD,KAAK,GAGJvD,GAGTuQ,EAAWjU,YAAc,SAAU4B,GACjC,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,6BAGtB,OAAOd,EAAOwC,IAGhBqS,EAAW/M,gBAAkB,SAAUtF,GACrC,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,6BAGtB,OAAOoB,EAAO+E,WAAWzE,K,6BCjE3BpF,EAAQgR,MAAQ9P,OAAOwW,OAAO,CAC5B,SAAY,EACZ,KAAQ,GACR,WAAY,GACZ,WAAY,GACZ,eAAgB,GAChB,WAAY,GACZ,WAAY,GACZ,WAAY,GACZ,WAAY,GACZ,YAAa,GACb,YAAa,GACb,aAAc,GACd,aAAc,GACd,aAAc,GACd,aAAc,GACd,cAAe,GACf,aAAc,GACd,YAAa,MACb,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,YAAa,MACb,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,aAAc,MACd,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,cAAe,MACf,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,eAAgB,MAChB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,KACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,gBAAiB,MACjB,iBAAkB,MAClB,iBAAkB,MAClB,iBAAkB,MAClB,iBAAkB,QAEpB1X,EAAQiR,MAAQ/P,OAAOwW,OAAO,CAC5BC,EAAK,WAEL,GAAM,OACN,GAAM,WACN,GAAM,WACN,GAAM,eACN,GAAM,WACN,GAAM,WACN,GAAM,WACN,GAAM,WACN,GAAM,YACN,GAAM,YACN,GAAM,aACN,GAAM,aACN,GAAM,aACN,GAAM,aACN,GAAM,cACN,GAAM,aAEN,MAAQ,YACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,YACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cAER,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,aACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,cACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,eACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,KAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,iBACR,MAAQ,iBACR,MAAQ,iBACR,MAAQ,mBAEV3X,EAAQkR,eAAiBhQ,OAAOwW,OAAO,CACrC,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,GAAM,GACN,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,EACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,KAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,GACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,IACR,MAAQ,O,6BC3/BVzX,EAAOD,QAMP,SAAS2Q,EAAOsC,EAAK1G,EAAK/E,GACxB+E,EAAMA,GAAO,GAEb,IAAIqL,EADJpQ,EAASA,GAAU,EAGnB,KAAOyL,GAAO4E,GACZtL,EAAI/E,KAAkB,IAANyL,EAXV,IAYNA,GAAO,IAGT,MAbW,IAaJA,GACL1G,EAAI/E,KAAkB,IAANyL,EAhBV,IAiBNA,KAAS,EAKX,OAFA1G,EAAI/E,GAAgB,EAANyL,EACdtC,EAAOlE,MAAQjF,EAASoQ,EAAY,EAC7BrL,GAtBT,IAGIsL,EAAM9O,KAAK+E,IAAI,EAAG,K,6BCJtB7N,EAAOD,QAIP,SAASiH,EAAKC,EAAKM,GACjB,IAIInB,EAJA4C,EAAM,EAEN6O,EAAQ,EACRC,EAFAvQ,EAASA,GAAU,EAInB/G,EAAIyG,EAAIlE,OAEZ,EAAG,CACD,GAAI+U,GAAWtX,EAEb,MADAwG,EAAKwF,MAAQ,EACP,IAAIxJ,WAAW,2BAGvBoD,EAAIa,EAAI6Q,KACR9O,GAAO6O,EAAQ,IAjBR,IAiBczR,IAAayR,GAjB3B,IAiBoCzR,GAAY0C,KAAK+E,IAAI,EAAGgK,GACnEA,GAAS,QACFzR,GApBD,KAuBR,OADAY,EAAKwF,MAAQsL,EAAUvQ,EAChByB,I,6BCxBT,IAAI+O,EAAKjP,KAAK+E,IAAI,EAAG,GACjBmK,EAAKlP,KAAK+E,IAAI,EAAG,IACjBoK,EAAKnP,KAAK+E,IAAI,EAAG,IACjBqK,EAAKpP,KAAK+E,IAAI,EAAG,IACjBsK,EAAKrP,KAAK+E,IAAI,EAAG,IACjBuK,EAAKtP,KAAK+E,IAAI,EAAG,IACjBwK,EAAKvP,KAAK+E,IAAI,EAAG,IACjByK,EAAKxP,KAAK+E,IAAI,EAAG,IACjB0K,EAAKzP,KAAK+E,IAAI,EAAG,IAErB7N,EAAOD,QAAU,SAAUyB,GACzB,OAAOA,EAAQuW,EAAK,EAAIvW,EAAQwW,EAAK,EAAIxW,EAAQyW,EAAK,EAAIzW,EAAQ0W,EAAK,EAAI1W,EAAQ2W,EAAK,EAAI3W,EAAQ4W,EAAK,EAAI5W,EAAQ6W,EAAK,EAAI7W,EAAQ8W,EAAK,EAAI9W,EAAQ+W,EAAK,EAAI,K,8BCblK,YAMA,MAAMC,EAAY,EAAQ,KAE1BzY,EAAUC,EAAOD,QAAUyT,GACnB9C,OAoCR,SAAgB+H,EAAYxR,GAC1B,MAAM8N,EAAO2D,EAAQD,GAErB,OAAOjF,EADMuB,EAAKjU,KACK6B,EAAOa,KAAKuR,EAAKrE,OAAOzJ,MAtCjDlH,EAAQ4Q,OAmDR,SAAgBgI,GACVhW,EAAO8B,SAASkU,KAClBA,EAAcA,EAAYvT,YAG5B,MAAM2K,EAAO4I,EAAYC,UAAU,EAAG,GAGX,iBAF3BD,EAAcA,EAAYC,UAAU,EAAGD,EAAY5V,WAGjD4V,EAAchW,EAAOa,KAAKmV,IAG5B,MAAM5D,EAAO2D,EAAQ3I,GACrB,OAAOpN,EAAOa,KAAKuR,EAAKpE,OAAOgI,EAAYvT,cA/D7CrF,EAAQsU,UA0ER,SAAmBsE,GACbhW,EAAO8B,SAASkU,KAClBA,EAAcA,EAAYvT,YAI5B,GAAoD,oBAAhDnE,OAAOkB,UAAUiD,SAAS1E,KAAKiY,GACjC,OAAO,EAGT,MAAM5I,EAAO4I,EAAYC,UAAU,EAAG,GAEtC,IAEE,OADaF,EAAQ3I,GACTjP,KACZ,MAAO+X,GACP,OAAO,IAzFX9Y,EAAQgR,MAAQ9P,OAAOwW,OAAOxW,OAAO6X,KAAKN,EAAUzH,QACpDhR,EAAQiR,MAAQ/P,OAAOwW,OAAOxW,OAAO6X,KAAKN,EAAUxH,QACpD,MAAM+H,EAAkB,IAAIzV,MAAM,wBAUlC,SAASkQ,EAAUiF,EAAYxR,GAC7B,IAAKA,EACH,MAAM,IAAI3D,MAAM,8BAGlB,MAAMyR,EAAO2D,EAAQD,GACfO,EAAUrW,EAAOa,KAAKuR,EAAKhF,MAGjC,OA+EF,SAAqBjP,EAAMmG,GACZyR,EAAQ5X,GAChB6P,OAAO1J,EAAI7B,YAlFhB6T,CADalE,EAAKjU,KACAmG,GACXtE,EAAOoI,OAAO,CAACiO,EAAS/R,IAoFjC,SAASyR,EAAQD,GACf,IAAI1D,EAEJ,GAAIyD,EAAUzH,MAAM0H,GAClB1D,EAAOyD,EAAUzH,MAAM0H,OAClB,KAAID,EAAUxH,MAAMyH,GAGzB,MAAMM,EAFNhE,EAAOyD,EAAUxH,MAAMyH,GAKzB,IAAK1D,EAAKmE,gBACR,MAAM,IAAI5V,MAAM,QAAUmV,EAAa,2BAGzC,OAAO1D,K,qDClIT,MAAMoE,EAAO,EAAQ,IAEfC,EAAQ,EAAQ,GAEhBC,EAAS,EAAQ,IAEjBC,EAAS,EAAQ,IAEjB/W,EAAS,EAAQ,IAGjBiW,EAAY,CAAC,CAAC,QAAS,IAAK,GAAI,KAAM,CAAC,QAAS,IAAKY,EAAO,MAAO,CAAC,QAAS,IAAKA,EAAO,YAAa,CAAC,SAAU,IAAKA,EAAO,cAAe,CAAC,SAAU,IAAKC,EAAQ,oBAAqB,CAAC,SAAU,IAAKC,EAAQ,oCAAqC,CAAC,YAAa,IAAKA,EAAQ,qCAAsC,CAAC,YAAa,IAAKA,EAAQ,oCAAqC,CAAC,eAAgB,IAAKA,EAAQ,qCAAsC,CAAC,UAAW,IAAKA,EAAQ,oCAAqC,CAAC,eAAgB,IAAKF,EAAO,8DAA+D,CAAC,YAAa,IAAKA,EAAO,8DAA+D,CAAC,SAAU,IAAK7W,EAAQ,oEAAqE,CAAC,YAAa,IAAKA,EAAQ,qEAAsE,CAAC,YAAa,IAAKA,EAAQ,oEAAqE,CAAC,eAAgB,IAAKA,EAAQ,sEAC1+BwO,EAAQyH,EAAUe,OAAO,CAACC,EAAMC,KACpCD,EAAKC,EAAO,IAAM,IAAIN,EAAKM,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAC5DD,GACN,IACGxI,EAAQwH,EAAUe,OAAO,CAACC,EAAMC,KACpCD,EAAKC,EAAO,IAAM1I,EAAM0I,EAAO,IACxBD,GACN,IACHxZ,EAAOD,QAAU,CACfgR,MAAOA,EACPC,MAAOA,I,6BCGThR,EAAOD,QAzBP,MACE,YAAYe,EAAMiP,EAAM2J,EAAgBC,GACtCtW,KAAKvC,KAAOA,EACZuC,KAAK0M,KAAOA,EACZ1M,KAAKsW,SAAWA,EAEZD,GAAkBC,IACpBtW,KAAKuW,OAASF,EAAeC,IAIjC,OAAOE,GACL,OAAOxW,KAAKuW,OAAOlJ,OAAOmJ,GAG5B,OAAOA,GACL,OAAOxW,KAAKuW,OAAOjJ,OAAOkJ,GAG5B,gBACE,OAAOxW,KAAKuW,U,8BCtBhB,YAEA5Z,EAAOD,QAAU,SAAgB4Z,GAC/B,MAAO,CACLjJ,OAAOyC,GACgB,iBAAVA,EACFxQ,EAAOa,KAAK2P,GAAO/N,SAAS,OAG9B+N,EAAM/N,SAAS,OAGxB,OAAO+N,GACL,IAAK,IAAI2G,KAAQ3G,EACf,GAAIwG,EAASlT,QAAQqT,GAAQ,EAC3B,MAAM,IAAIxW,MAAM,4BAIpB,OAAOX,EAAOa,KAAK2P,EAAO,Y,sDCnBhC,YAuBA,SAASzC,EAAO7L,EAAQ8U,GACtB,IAAI5W,EAAS8B,EAAOhB,WAChBkW,EAAO,IAAI9W,WAAW4B,GACtBmV,EAAUL,EAASlT,QAAQ,OAASkT,EAAS5W,OAAS,EAEtDiX,IACFL,EAAWA,EAASf,UAAU,EAAGe,EAAS5W,OAAS,IAGrD,IAAIkX,EAAO,EACPzY,EAAQ,EACRiV,EAAS,GAEb,IAAK,IAAIlW,EAAI,EAAGA,EAAIwC,EAAQxC,IAI1B,IAHAiB,EAAQA,GAAS,EAAIuY,EAAKxZ,GAC1B0Z,GAAQ,EAEDA,GAAQ,GACbxD,GAAUkD,EAASnY,IAAUyY,EAAO,EAAI,IACxCA,GAAQ,EAQZ,GAJIA,EAAO,IACTxD,GAAUkD,EAASnY,GAAS,EAAIyY,EAAO,KAGrCD,EACF,KAAOvD,EAAO1T,OAAS,GAAM,GAC3B0T,GAAU,IAId,OAAOA,EAGTzW,EAAOD,QAAU,SAAgB4Z,GAC/B,MAAO,CACLjJ,OAAOyC,GAEIzC,EADY,iBAAVyC,EACKxQ,EAAOa,KAAK2P,GAGdA,EAHsBwG,GAMtC,OAAOxG,GACL,IAAK,IAAI2G,KAAQ3G,EACf,GAAIwG,EAASlT,QAAQqT,GAAQ,EAC3B,MAAM,IAAIxW,MAAM,4BAIpB,OA1EN,SAAgB6P,EAAOwG,GAErB,IAAI5W,GADJoQ,EAAQA,EAAM9C,QAAQ,IAAI6J,OAAO,IAAK,KAAM,KACzBnX,OACfkX,EAAO,EACPzY,EAAQ,EACR2Y,EAAQ,EACR1D,EAAS,IAAIxT,WAAoB,EAATF,EAAa,EAAI,GAE7C,IAAK,IAAIxC,EAAI,EAAGA,EAAIwC,EAAQxC,IAC1BiB,EAAQA,GAAS,EAAImY,EAASlT,QAAQ0M,EAAM5S,IAC5C0Z,GAAQ,EAEJA,GAAQ,IACVxD,EAAO0D,KAAW3Y,IAAUyY,EAAO,EAAI,IACvCA,GAAQ,GAIZ,OAAOxD,EAAO5R,OAwDH8L,CAAOwC,EAAOwG,Q,sDC5E3B,YAEA3Z,EAAOD,QAAU,SAAgB4Z,GAM/B,MAAMK,EAAUL,EAASlT,QAAQ,MAAQ,EACnC2T,EAAMT,EAASlT,QAAQ,MAAQ,GAAKkT,EAASlT,QAAQ,MAAQ,EACnE,MAAO,CACL,OAAO0M,GACL,IAAIsD,EAAS,GAGXA,EADmB,iBAAVtD,EACAxQ,EAAOa,KAAK2P,GAAO/N,SAAS,UAE5B+N,EAAM/N,SAAS,UAGtBgV,IACF3D,EAASA,EAAOpG,QAAQ,MAAO,KAAKA,QAAQ,MAAO,MAGrD,MAAMgK,EAAM5D,EAAOhQ,QAAQ,KAM3B,OAJI4T,EAAM,IAAML,IACdvD,EAASA,EAAOmC,UAAU,EAAGyB,IAGxB5D,GAGT,OAAOtD,GACL,IAAK,IAAI2G,KAAQ3G,EACf,GAAIwG,EAASlT,QAAQqT,GAAQ,EAC3B,MAAM,IAAIxW,MAAM,4BAIpB,OAAOX,EAAOa,KAAK2P,EAAO,e,sDCxChC,YAaA,MAAMjC,EAAS,EAAQ,GAEjBoJ,EAAW,EAAQ,IAEnBC,EAAwB,EAAQ,IAEhCC,EAAO,EAAQ,IAErBza,EAAUC,EAAOD,SAST0a,UAAY,CAACC,EAAqB1V,KACxC,IAAIgN,EAEJ,GAAIrP,EAAO8B,SAASiW,GAClB1I,EAASwI,EAAKtH,mBAAmBwH,OAC5B,CACL,IAAIH,EAAsBG,GAGxB,MAAM,IAAIpX,MAAM,6BAFhB0O,EAASuI,EAAsBG,GAMnC,OAAO/X,EAAOoI,OAAO,CAACiH,EAAQhN,KAUhCjF,EAAQwU,SAAWvP,IACjBkM,EAAOP,OAAO3L,GACPA,EAAKV,MAAM4M,EAAOP,OAAOnE,QASlCzM,EAAQuU,SAAWqG,IACjB,MAAM5K,EAAOmB,EAAOP,OAAOgK,GACrBC,EAAYN,EAASlZ,IAAI2O,GAE/B,QAAkBjM,IAAd8W,EACF,MAAM,IAAItX,MAAM,QAAQyH,OAAOgF,EAAM,eAGvC,OAAO6K,GAST7a,EAAQ8a,QAAU9G,GACTuG,EAASlZ,IAAI2S,GAStBhU,EAAQ+a,UAAYha,IAClB,MAAMiP,EAAOwK,EAAsBzZ,GAEnC,QAAagD,IAATiM,EACF,MAAM,IAAIzM,MAAM,UAAYxC,EAAO,eAGrC,OAAO0Z,EAAKpH,mBAAmBrD,GAAM,IASvChQ,EAAQgb,QAAUJ,GACTzJ,EAAOP,OAAOgK,GASvB5a,EAAQ4U,cAAgBiG,IACtB,MAAM7K,EAAOwK,EAAsBK,GAEnC,QAAa9W,IAATiM,EACF,MAAM,IAAIzM,MAAM,UAAYsX,EAAY,eAG1C,OAAO7K,GASThQ,EAAQib,UAAYjL,GACXmB,EAAOR,OAAOX,GAIvB,MAAMyI,EAAY,EAAQ,IAE1BvX,OAAOga,OAAOlb,EAASyY,GAEvBzY,EAAQmb,MAAQ,EAAQ,M,qDC5IxB,MAAMC,EAAY,EAAQ,GAGpBC,EAAY,IAAIC,IAEtB,IAAK,MAAMC,KAAgBH,EAAW,CACpC,MAAMpL,EAAOoL,EAAUG,GACvBF,EAAUtL,IAAIC,EAAMuL,GAGtBtb,EAAOD,QAAUkB,OAAOwW,OAAO2D,I,6BCV/B,MAAMD,EAAY,EAAQ,GAEpB9H,EAAe,EAAQ,GAAUA,aAGjCkI,EAAc,GAEpB,IAAK,MAAMD,KAAgBH,EAAW,CACpC,MAAMpL,EAAOoL,EAAUG,GACvBC,EAAYD,GAAgBjI,EAAatD,GAG3C/P,EAAOD,QAAUkB,OAAOwW,OAAO8D,I,6BCZ/B,MAAMC,EAAQ,EAAQ,GAGhBhD,EAAY,GAElB,IAAK,MAAO1X,EAAMiP,KAAS9O,OAAOwa,QAAQD,GACxChD,EAAU1X,EAAK4a,cAAcrL,QAAQ,KAAM,MAAQN,EAGrD/P,EAAOD,QAAUkB,OAAOwW,OAAOe,I,6BCT/B,MAAMgD,EAAQ,EAAQ,GAGhBG,EAAc,GAEpB,IAAK,MAAO7a,EAAMiP,KAAS9O,OAAOwa,QAAQD,QACd1X,IAAtB6X,EAAY5L,KAAqB4L,EAAY5L,GAAQjP,GAG3Dd,EAAOD,QAAUkB,OAAOwW,OAAOkE,I,6BCT/B,MAAMpI,EAAKD,EAAQ,IACb,OAAE3Q,GAAW2Q,EAAQ,GAC3B,IAAIK,EAAU,CASZwB,mBAAoB,SAAUF,GAC5B,GAAa,MAATA,EACF,MAAO,iCAGT,GAAwB,IAAlBA,EAAMnB,SAAmC,IAAlBmB,EAAMnB,QACjC,MAAO,oDAGT,GAA2B,iBAAhBmB,EAAMlB,MACf,MAAO,uBAGT,GAAsB,IAAlBkB,EAAMnB,QAAe,CACvB,GAAoB,WAAhBmB,EAAMlB,MACR,MAAO,mCAET,GAA4B,cAAxBkB,EAAMjB,cACR,MAAO,8CAIX,IAAKrR,EAAO8B,SAASwQ,EAAM7D,WACzB,MAAO,6BAGT,IACEmC,EAAGpC,SAAS8D,EAAM7D,WAClB,MAAOyH,GACP,IAAI3D,EAAW2D,EAAI+C,QAInB,OAHK1G,IACHA,EAAW,+BAENA,KAKblV,EAAOD,QAAU4T,G,6BCwBjB3T,EAAOD,QAzEP,SAAgB8b,GAAO,UACrBxG,EAAS,WACTC,IAEA,MAAMwG,EAASxa,OAAO0T,IAAIM,GACpByG,EAAiB,CAOrB,CAAC1G,GAAY,cAAcwG,EACzB,eAAeG,GACbC,SAASD,GACT/a,OAAOC,eAAemC,KAAMyY,EAAQ,CAClCta,OAAO,IAIXJ,IAAKE,OAAOC,eACV,OAAO8T,KAIXA,GAIF,OAFA0G,EAAe,KAAKhR,OAAOsK,IAAc7Q,MAAUA,IAAOA,EAAIsX,IAEvDC,GA6CT/b,EAAOD,QAAQmc,MA1Cf,SAAqBL,GAAO,UAC1BxG,EAAS,WACTC,EAAU,WACV6G,IAEA,MAAML,EAASxa,OAAO0T,IAAIM,GAGpByG,EAAiB,CACrB,CAAC1G,GAAY,YAAa2G,GACxB,GAAIG,KAAgB9Y,gBAAgB0Y,GAClC,OAAO,IAAIA,KAAkBC,GAG/B,MAAMI,EAAQP,EAAMnb,KAAK2C,QAAS2Y,IAAS3Y,KAQ3C,OANI+Y,IAAUA,EAAMN,IAClB7a,OAAOC,eAAekb,EAAON,EAAQ,CACnCta,OAAO,IAIJ4a,IAET/G,GAcF,OAXA0G,EAAe5Z,UAAYlB,OAAOY,OAAOga,EAAM1Z,WAC/C4Z,EAAe5Z,UAAU0R,YAAckI,EACvC9a,OAAOC,eAAe6a,EAAe5Z,UAAWb,OAAOC,YAAa,CAClEH,IAAG,IACMiU,IAKX0G,EAAe,KAAKhR,OAAOsK,IAAc7Q,MAAUA,IAAOA,EAAIsX,IAEvDC", "file": "index.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Cids\"] = factory();\n\telse\n\t\troot[\"Cids\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 6);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n\n/* eslint-disable no-proto */\n'use strict';\n\nvar base64 = require('base64-js');\n\nvar ieee754 = require('ieee754');\n\nvar isArray = require('isarray');\n\nexports.Buffer = Buffer;\nexports.SlowBuffer = SlowBuffer;\nexports.INSPECT_MAX_BYTES = 50;\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\n\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined ? global.TYPED_ARRAY_SUPPORT : typedArraySupport();\n/*\n * Export kMaxLength after typed array support is determined.\n */\n\nexports.kMaxLength = kMaxLength();\n\nfunction typedArraySupport() {\n  try {\n    var arr = new Uint8Array(1);\n    arr.__proto__ = {\n      __proto__: Uint8Array.prototype,\n      foo: function foo() {\n        return 42;\n      }\n    };\n    return arr.foo() === 42 && // typed array instances can be augmented\n    typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n    arr.subarray(1, 1).byteLength === 0; // ie10 has broken `subarray`\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction kMaxLength() {\n  return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;\n}\n\nfunction createBuffer(that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length');\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n\n    that.length = length;\n  }\n\n  return that;\n}\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\n\nfunction Buffer(arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length);\n  } // Common case.\n\n\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error('If encoding is specified then the first argument must be a string');\n    }\n\n    return allocUnsafe(this, arg);\n  }\n\n  return from(this, arg, encodingOrOffset, length);\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n// TODO: Legacy, not needed anymore. Remove in next major version.\n\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr;\n};\n\nfunction from(that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number');\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length);\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset);\n  }\n\n  return fromObject(that, value);\n}\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\n\n\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length);\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n\n  if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    });\n  }\n}\n\nfunction assertSize(size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number');\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative');\n  }\n}\n\nfunction alloc(that, size, fill, encoding) {\n  assertSize(size);\n\n  if (size <= 0) {\n    return createBuffer(that, size);\n  }\n\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);\n  }\n\n  return createBuffer(that, size);\n}\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\n\n\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding);\n};\n\nfunction allocUnsafe(that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n\n  return that;\n}\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\n\n\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size);\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\n\n\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size);\n};\n\nfunction fromString(that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding');\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that;\n}\n\nfunction fromArrayLike(that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n\n  return that;\n}\n\nfunction fromArrayBuffer(that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds');\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds');\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n\n  return that;\n}\n\nfunction fromObject(that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that;\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that;\n  }\n\n  if (obj) {\n    if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0);\n      }\n\n      return fromArrayLike(that, obj);\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data);\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');\n}\n\nfunction checked(length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');\n  }\n\n  return length | 0;\n}\n\nfunction SlowBuffer(length) {\n  if (+length != length) {\n    // eslint-disable-line eqeqeq\n    length = 0;\n  }\n\n  return Buffer.alloc(+length);\n}\n\nBuffer.isBuffer = function isBuffer(b) {\n  return !!(b != null && b._isBuffer);\n};\n\nBuffer.compare = function compare(a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers');\n  }\n\n  if (a === b) return 0;\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n};\n\nBuffer.isEncoding = function isEncoding(encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true;\n\n    default:\n      return false;\n  }\n};\n\nBuffer.concat = function concat(list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers');\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0);\n  }\n\n  var i;\n\n  if (length === undefined) {\n    length = 0;\n\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers');\n    }\n\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n\n  return buffer;\n};\n\nfunction byteLength(string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length;\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength;\n  }\n\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0; // Use a for loop to avoid recursion\n\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len;\n\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length;\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2;\n\n      case 'hex':\n        return len >>> 1;\n\n      case 'base64':\n        return base64ToBytes(string).length;\n\n      default:\n        if (loweredCase) return utf8ToBytes(string).length; // assume utf8\n\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\nBuffer.byteLength = byteLength;\n\nfunction slowToString(encoding, start, end) {\n  var loweredCase = false; // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n\n  if (start === undefined || start < 0) {\n    start = 0;\n  } // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n\n\n  if (start > this.length) {\n    return '';\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return '';\n  } // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n\n\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return '';\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end);\n\n      case 'ascii':\n        return asciiSlice(this, start, end);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end);\n\n      case 'base64':\n        return base64Slice(this, start, end);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n} // The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\n\n\nBuffer.prototype._isBuffer = true;\n\nfunction swap(b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16() {\n  var len = this.length;\n\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits');\n  }\n\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap32 = function swap32() {\n  var len = this.length;\n\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits');\n  }\n\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n\n  return this;\n};\n\nBuffer.prototype.swap64 = function swap64() {\n  var len = this.length;\n\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits');\n  }\n\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n\n  return this;\n};\n\nBuffer.prototype.toString = function toString() {\n  var length = this.length | 0;\n  if (length === 0) return '';\n  if (arguments.length === 0) return utf8Slice(this, 0, length);\n  return slowToString.apply(this, arguments);\n};\n\nBuffer.prototype.equals = function equals(b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer');\n  if (this === b) return true;\n  return Buffer.compare(this, b) === 0;\n};\n\nBuffer.prototype.inspect = function inspect() {\n  var str = '';\n  var max = exports.INSPECT_MAX_BYTES;\n\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n\n  return '<Buffer ' + str + '>';\n};\n\nBuffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer');\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index');\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0;\n  }\n\n  if (thisStart >= thisEnd) {\n    return -1;\n  }\n\n  if (start >= end) {\n    return 1;\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n  if (this === target) return 0;\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break;\n    }\n  }\n\n  if (x < y) return -1;\n  if (y < x) return 1;\n  return 0;\n}; // Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\n\n\nfunction bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1; // Normalize byteOffset\n\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n\n  byteOffset = +byteOffset; // Coerce to Number.\n\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : buffer.length - 1;\n  } // Normalize byteOffset: negative offsets start from the end of the buffer\n\n\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1;else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;else return -1;\n  } // Normalize val\n\n\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  } // Finally, search either indexOf (if dir is true) or lastIndexOf\n\n\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1;\n    }\n\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir);\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n\n    if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);\n      }\n    }\n\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);\n  }\n\n  throw new TypeError('val must be string, number or Buffer');\n}\n\nfunction arrayIndexOf(arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n\n    if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1;\n      }\n\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read(buf, i) {\n    if (indexSize === 1) {\n      return buf[i];\n    } else {\n      return buf.readUInt16BE(i * indexSize);\n    }\n  }\n\n  var i;\n\n  if (dir) {\n    var foundIndex = -1;\n\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break;\n        }\n      }\n\n      if (found) return i;\n    }\n  }\n\n  return -1;\n}\n\nBuffer.prototype.includes = function includes(val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1;\n};\n\nBuffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true);\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false);\n};\n\nfunction hexWrite(buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n\n    if (length > remaining) {\n      length = remaining;\n    }\n  } // must be an even number of digits\n\n\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i;\n    buf[offset + i] = parsed;\n  }\n\n  return i;\n}\n\nfunction utf8Write(buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nfunction asciiWrite(buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length);\n}\n\nfunction latin1Write(buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length);\n}\n\nfunction base64Write(buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length);\n}\n\nfunction ucs2Write(buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);\n}\n\nBuffer.prototype.write = function write(string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0; // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0; // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    } // legacy write(string, encoding, offset, length) - remove in v0.13\n\n  } else {\n    throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds');\n  }\n\n  if (!encoding) encoding = 'utf8';\n  var loweredCase = false;\n\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length);\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length);\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length);\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length);\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length);\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length);\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON() {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  };\n};\n\nfunction base64Slice(buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf);\n  } else {\n    return base64.fromByteArray(buf.slice(start, end));\n  }\n}\n\nfunction utf8Slice(buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n  var i = start;\n\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n\n          break;\n\n        case 2:\n          secondByte = buf[i + 1];\n\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;\n\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;\n\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n          break;\n\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;\n\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res);\n} // Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\n\n\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray(codePoints) {\n  var len = codePoints.length;\n\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints); // avoid extra slice()\n  } // Decode in chunks to avoid \"call stack size exceeded\".\n\n\n  var res = '';\n  var i = 0;\n\n  while (i < len) {\n    res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));\n  }\n\n  return res;\n}\n\nfunction asciiSlice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n\n  return ret;\n}\n\nfunction latin1Slice(buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n\n  return ret;\n}\n\nfunction hexSlice(buf, start, end) {\n  var len = buf.length;\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n  var out = '';\n\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n\n  return out;\n}\n\nfunction utf16leSlice(buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n\n  return res;\n}\n\nBuffer.prototype.slice = function slice(start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n  var newBuf;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf;\n};\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\n\n\nfunction checkOffset(offset, ext, length) {\n  if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val;\n};\n\nBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset];\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | this[offset + 1] << 8;\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] << 8 | this[offset + 1];\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);\n};\n\nBuffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n\n  mul *= 0x80;\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n  return val;\n};\n\nBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return this[offset];\n  return (0xff - this[offset] + 1) * -1;\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | this[offset + 1] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | this[offset] << 8;\n  return val & 0x8000 ? val | 0xFFFF0000 : val;\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, true, 23, 4);\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return ieee754.read(this, offset, false, 23, 4);\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, true, 52, 8);\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return ieee754.read(this, offset, false, 52, 8);\n};\n\nfunction checkInt(buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance');\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds');\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = value / mul & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nfunction objectWriteUInt16(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nfunction objectWriteUInt32(buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = value >>> 24;\n    this[offset + 2] = value >>> 16;\n    this[offset + 1] = value >>> 8;\n    this[offset] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n\n    this[offset + i] = (value / mul >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength;\n};\n\nBuffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = value & 0xff;\n  return offset + 1;\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 8;\n    this[offset + 1] = value & 0xff;\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n\n  return offset + 2;\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value & 0xff;\n    this[offset + 1] = value >>> 8;\n    this[offset + 2] = value >>> 16;\n    this[offset + 3] = value >>> 24;\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n\n  return offset + 4;\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = value >>> 24;\n    this[offset + 1] = value >>> 16;\n    this[offset + 2] = value >>> 8;\n    this[offset + 3] = value & 0xff;\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n\n  return offset + 4;\n};\n\nfunction checkIEEE754(buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range');\n  if (offset < 0) throw new RangeError('Index out of range');\n}\n\nfunction writeFloat(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4;\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert);\n};\n\nfunction writeDouble(buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308);\n  }\n\n  ieee754.write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8;\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert);\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert);\n}; // copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\n\n\nBuffer.prototype.copy = function copy(target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start; // Copy 0 bytes; we're done\n\n  if (end === start) return 0;\n  if (target.length === 0 || this.length === 0) return 0; // Fatal error conditions\n\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds');\n  }\n\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');\n  if (end < 0) throw new RangeError('sourceEnd out of bounds'); // Are we oob?\n\n  if (end > this.length) end = this.length;\n\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);\n  }\n\n  return len;\n}; // Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\n\n\nBuffer.prototype.fill = function fill(val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n\n      if (code < 256) {\n        val = code;\n      }\n    }\n\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string');\n    }\n\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding);\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  } // Invalid ranges are not set to a default, so can range check early.\n\n\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index');\n  }\n\n  if (end <= start) {\n    return this;\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n  if (!val) val = 0;\n  var i;\n\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this;\n}; // HELPER FUNCTIONS\n// ================\n\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean(str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, ''); // Node converts strings with length < 2 to ''\n\n  if (str.length < 2) return ''; // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n\n  return str;\n}\n\nfunction stringtrim(str) {\n  if (str.trim) return str.trim();\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n\nfunction toHex(n) {\n  if (n < 16) return '0' + n.toString(16);\n  return n.toString(16);\n}\n\nfunction utf8ToBytes(string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i); // is surrogate component\n\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue;\n        } // valid lead\n\n\n        leadSurrogate = codePoint;\n        continue;\n      } // 2 leads in a row\n\n\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue;\n      } // valid surrogate pair\n\n\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null; // encode utf8\n\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break;\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break;\n      bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break;\n      bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break;\n      bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);\n    } else {\n      throw new Error('Invalid code point');\n    }\n  }\n\n  return bytes;\n}\n\nfunction asciiToBytes(str) {\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n\n  return byteArray;\n}\n\nfunction utf16leToBytes(str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break;\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray;\n}\n\nfunction base64ToBytes(str) {\n  return base64.toByteArray(base64clean(str));\n}\n\nfunction blitBuffer(src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if (i + offset >= dst.length || i >= src.length) break;\n    dst[i + offset] = src[i];\n  }\n\n  return i;\n}\n\nfunction isnan(val) {\n  return val !== val; // eslint-disable-line no-self-compare\n}", "\"use strict\";\n\nmodule.exports = {\n  encode: require('./encode.js'),\n  decode: require('./decode.js'),\n  encodingLength: require('./length.js')\n};", "/**\n * Multihash implementation in JavaScript.\n *\n * @module multihash\n */\n'use strict';\n\nconst bs58 = require('bs58');\n\nconst cs = require('./constants');\n\nexports.names = cs.names;\nexports.codes = cs.codes;\nexports.defaultLengths = cs.defaultLengths;\n\nconst varint = require('varint');\n/**\n * Convert the given multihash to a hex encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\n\n\nexports.toHexString = function toHexString(hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer');\n  }\n\n  return hash.toString('hex');\n};\n/**\n * Convert the given hex encoded string to a multihash.\n *\n * @param {string} hash\n * @returns {Buffer}\n */\n\n\nexports.fromHexString = function fromHexString(hash) {\n  return Buffer.from(hash, 'hex');\n};\n/**\n * Convert the given multihash to a base58 encoded string.\n *\n * @param {Buffer} hash\n * @returns {string}\n */\n\n\nexports.toB58String = function toB58String(hash) {\n  if (!Buffer.isBuffer(hash)) {\n    throw new Error('must be passed a buffer');\n  }\n\n  return bs58.encode(hash);\n};\n/**\n * Convert the given base58 encoded string to a multihash.\n *\n * @param {string|Buffer} hash\n * @returns {Buffer}\n */\n\n\nexports.fromB58String = function fromB58String(hash) {\n  let encoded = hash;\n\n  if (Buffer.isBuffer(hash)) {\n    encoded = hash.toString();\n  }\n\n  return Buffer.from(bs58.decode(encoded));\n};\n/**\n * Decode a hash from the given multihash.\n *\n * @param {Buffer} buf\n * @returns {{code: number, name: string, length: number, digest: Buffer}} result\n */\n\n\nexports.decode = function decode(buf) {\n  if (!Buffer.isBuffer(buf)) {\n    throw new Error('multihash must be a Buffer');\n  }\n\n  if (buf.length < 3) {\n    throw new Error('multihash too short. must be > 3 bytes.');\n  }\n\n  const code = varint.decode(buf);\n\n  if (!exports.isValidCode(code)) {\n    throw new Error(\"multihash unknown function code: 0x\".concat(code.toString(16)));\n  }\n\n  buf = buf.slice(varint.decode.bytes);\n  const len = varint.decode(buf);\n\n  if (len < 1) {\n    throw new Error(\"multihash invalid length: 0x\".concat(len.toString(16)));\n  }\n\n  buf = buf.slice(varint.decode.bytes);\n\n  if (buf.length !== len) {\n    throw new Error(\"multihash length inconsistent: 0x\".concat(buf.toString('hex')));\n  }\n\n  return {\n    code: code,\n    name: cs.codes[code],\n    length: len,\n    digest: buf\n  };\n};\n/**\n *  Encode a hash digest along with the specified function code.\n *\n * > **Note:** the length is derived from the length of the digest itself.\n *\n * @param {Buffer} digest\n * @param {string|number} code\n * @param {number} [length]\n * @returns {Buffer}\n */\n\n\nexports.encode = function encode(digest, code, length) {\n  if (!digest || code === undefined) {\n    throw new Error('multihash encode requires at least two args: digest, code');\n  } // ensure it's a hashfunction code.\n\n\n  const hashfn = exports.coerceCode(code);\n\n  if (!Buffer.isBuffer(digest)) {\n    throw new Error('digest should be a Buffer');\n  }\n\n  if (length == null) {\n    length = digest.length;\n  }\n\n  if (length && digest.length !== length) {\n    throw new Error('digest length should be equal to specified length.');\n  }\n\n  return Buffer.concat([Buffer.from(varint.encode(hashfn)), Buffer.from(varint.encode(length)), digest]);\n};\n/**\n * Converts a hash function name into the matching code.\n * If passed a number it will return the number if it's a valid code.\n * @param {string|number} name\n * @returns {number}\n */\n\n\nexports.coerceCode = function coerceCode(name) {\n  let code = name;\n\n  if (typeof name === 'string') {\n    if (cs.names[name] === undefined) {\n      throw new Error(\"Unrecognized hash function named: \".concat(name));\n    }\n\n    code = cs.names[name];\n  }\n\n  if (typeof code !== 'number') {\n    throw new Error(\"Hash function code should be a number. Got: \".concat(code));\n  }\n\n  if (cs.codes[code] === undefined && !exports.isAppCode(code)) {\n    throw new Error(\"Unrecognized function code: \".concat(code));\n  }\n\n  return code;\n};\n/**\n * Checks wether a code is part of the app range\n *\n * @param {number} code\n * @returns {boolean}\n */\n\n\nexports.isAppCode = function appCode(code) {\n  return code > 0 && code < 0x10;\n};\n/**\n * Checks whether a multihash code is valid.\n *\n * @param {number} code\n * @returns {boolean}\n */\n\n\nexports.isValidCode = function validCode(code) {\n  if (exports.isAppCode(code)) {\n    return true;\n  }\n\n  if (cs.codes[code]) {\n    return true;\n  }\n\n  return false;\n};\n/**\n * Check if the given buffer is a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\n\n\nfunction validate(multihash) {\n  exports.decode(multihash); // throws if bad.\n}\n\nexports.validate = validate;\n/**\n * Returns a prefix from a valid multihash. Throws an error if it is not valid.\n *\n * @param {Buffer} multihash\n * @returns {undefined}\n * @throws {Error}\n */\n\nexports.prefix = function prefix(multihash) {\n  validate(multihash);\n  return multihash.slice(0, 2);\n};", "\"use strict\";\n\n// base-x encoding\n// Forked from https://github.com/cryptocoinjs/bs58\n// Originally written by <PERSON> for BitcoinJ\n// Copyright (c) 2011 Google Inc\n// Ported to JavaScript by <PERSON>\n// Merged Buffer refactorings from base58-native by <PERSON>\n// Copyright (c) 2013 BitPay Inc\nvar Buffer = require('safe-buffer').Buffer;\n\nmodule.exports = function base(ALPHABET) {\n  var ALPHABET_MAP = {};\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0); // pre-compute lookup table\n\n  for (var z = 0; z < ALPHABET.length; z++) {\n    var x = ALPHABET.charAt(z);\n    if (ALPHABET_MAP[x] !== undefined) throw new TypeError(x + ' is ambiguous');\n    ALPHABET_MAP[x] = z;\n  }\n\n  function encode(source) {\n    if (source.length === 0) return '';\n    var digits = [0];\n\n    for (var i = 0; i < source.length; ++i) {\n      for (var j = 0, carry = source[i]; j < digits.length; ++j) {\n        carry += digits[j] << 8;\n        digits[j] = carry % BASE;\n        carry = carry / BASE | 0;\n      }\n\n      while (carry > 0) {\n        digits.push(carry % BASE);\n        carry = carry / BASE | 0;\n      }\n    }\n\n    var string = ''; // deal with leading zeros\n\n    for (var k = 0; source[k] === 0 && k < source.length - 1; ++k) string += LEADER; // convert digits to a string\n\n\n    for (var q = digits.length - 1; q >= 0; --q) string += ALPHABET[digits[q]];\n\n    return string;\n  }\n\n  function decodeUnsafe(string) {\n    if (typeof string !== 'string') throw new TypeError('Expected String');\n    if (string.length === 0) return Buffer.allocUnsafe(0);\n    var bytes = [0];\n\n    for (var i = 0; i < string.length; i++) {\n      var value = ALPHABET_MAP[string[i]];\n      if (value === undefined) return;\n\n      for (var j = 0, carry = value; j < bytes.length; ++j) {\n        carry += bytes[j] * BASE;\n        bytes[j] = carry & 0xff;\n        carry >>= 8;\n      }\n\n      while (carry > 0) {\n        bytes.push(carry & 0xff);\n        carry >>= 8;\n      }\n    } // deal with leading zeros\n\n\n    for (var k = 0; string[k] === LEADER && k < string.length - 1; ++k) {\n      bytes.push(0);\n    }\n\n    return Buffer.from(bytes.reverse());\n  }\n\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) return buffer;\n    throw new Error('Non-base' + BASE + ' character');\n  }\n\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n};", "'use strict';\n\nconst varint = require('varint');\n\nmodule.exports = {\n  numberTo<PERSON>uffer,\n  bufferToNumber,\n  varintBufferEncode,\n  varintBufferDecode,\n  varintEncode\n};\n\nfunction bufferToNumber(buf) {\n  return parseInt(buf.toString('hex'), 16);\n}\n\nfunction numberToBuffer(num) {\n  let hexString = num.toString(16);\n\n  if (hexString.length % 2 === 1) {\n    hexString = '0' + hexString;\n  }\n\n  return Buffer.from(hexString, 'hex');\n}\n\nfunction varintBufferEncode(input) {\n  return Buffer.from(varint.encode(bufferToNumber(input)));\n}\n\nfunction varintBufferDecode(input) {\n  return numberToBuffer(varint.decode(input));\n}\n\nfunction varintEncode(num) {\n  return Buffer.from(varint.encode(num));\n}", "'use strict'\n\nconst { Buffer } = require('buffer')\nconst mh = require('multihashes')\nconst multibase = require('multibase')\nconst multicodec = require('multicodec')\nconst codecs = require('multicodec/src/base-table.json')\nconst CIDUtil = require('./cid-util')\nconst withIs = require('class-is')\n\n/**\n * @typedef {Object} SerializedCID\n * @param {string} codec\n * @param {number} version\n * @param {Buffer} multihash\n */\n\n/**\n * Test if the given input is a CID.\n * @function isCID\n * @memberof CID\n * @static\n * @param {any} other\n * @returns {bool}\n */\n\n/**\n * Class representing a CID `<mbase><version><mcodec><mhash>`\n * , as defined in [ipld/cid](https://github.com/multiformats/cid).\n * @class CID\n */\nclass CID {\n  /**\n   * Create a new CID.\n   *\n   * The algorithm for argument input is roughly:\n   * ```\n   * if (cid)\n   *   -> create a copy\n   * else if (str)\n   *   if (1st char is on multibase table) -> CID String\n   *   else -> bs58 encoded multihash\n   * else if (Buffer)\n   *   if (1st byte is 0 or 1) -> CID\n   *   else -> multihash\n   * else if (Number)\n   *   -> construct CID by parts\n   * ```\n   *\n   * @param {string|Buffer|CID} version\n   * @param {string} [codec]\n   * @param {Buffer} [multihash]\n   * @param {string} [multibaseName]\n   *\n   * @example\n   * new CID(<version>, <codec>, <multihash>, <multibaseName>)\n   * new CID(<cidStr>)\n   * new CID(<cid.buffer>)\n   * new CID(<multihash>)\n   * new CID(<bs58 encoded multihash>)\n   * new CID(<cid>)\n   */\n  constructor (version, codec, multihash, multibaseName) {\n    if (_CID.isCID(version)) {\n      // version is an exising CID instance\n      const cid = version\n      this.version = cid.version\n      this.codec = cid.codec\n      this.multihash = Buffer.from(cid.multihash)\n      // Default guard for when a CID < 0.7 is passed with no multibaseName\n      this.multibaseName = cid.multibaseName || (cid.version === 0 ? 'base58btc' : 'base32')\n      return\n    }\n\n    if (typeof version === 'string') {\n      // e.g. 'base32' or false\n      const baseName = multibase.isEncoded(version)\n      if (baseName) {\n        // version is a CID String encoded with multibase, so v1\n        const cid = multibase.decode(version)\n        this.version = parseInt(cid.slice(0, 1).toString('hex'), 16)\n        this.codec = multicodec.getCodec(cid.slice(1))\n        this.multihash = multicodec.rmPrefix(cid.slice(1))\n        this.multibaseName = baseName\n      } else {\n        // version is a base58btc string multihash, so v0\n        this.version = 0\n        this.codec = 'dag-pb'\n        this.multihash = mh.fromB58String(version)\n        this.multibaseName = 'base58btc'\n      }\n      CID.validateCID(this)\n      Object.defineProperty(this, 'string', { value: version })\n      return\n    }\n\n    if (Buffer.isBuffer(version)) {\n      const firstByte = version.slice(0, 1)\n      const v = parseInt(firstByte.toString('hex'), 16)\n      if (v === 1) {\n        // version is a CID buffer\n        const cid = version\n        this.version = v\n        this.codec = multicodec.getCodec(cid.slice(1))\n        this.multihash = multicodec.rmPrefix(cid.slice(1))\n        this.multibaseName = 'base32'\n      } else {\n        // version is a raw multihash buffer, so v0\n        this.version = 0\n        this.codec = 'dag-pb'\n        this.multihash = version\n        this.multibaseName = 'base58btc'\n      }\n      CID.validateCID(this)\n      return\n    }\n\n    // otherwise, assemble the CID from the parameters\n\n    /**\n     * @type {number}\n     */\n    this.version = version\n\n    /**\n     * @type {string}\n     */\n    this.codec = codec\n\n    /**\n     * @type {Buffer}\n     */\n    this.multihash = multihash\n\n    /**\n     * @type {string}\n     */\n    this.multibaseName = multibaseName || (version === 0 ? 'base58btc' : 'base32')\n\n    CID.validateCID(this)\n  }\n\n  /**\n   * The CID as a `Buffer`\n   *\n   * @return {Buffer}\n   * @readonly\n   *\n   * @memberOf CID\n   */\n  get buffer () {\n    let buffer = this._buffer\n\n    if (!buffer) {\n      if (this.version === 0) {\n        buffer = this.multihash\n      } else if (this.version === 1) {\n        buffer = Buffer.concat([\n          Buffer.from('01', 'hex'),\n          multicodec.getCodeVarint(this.codec),\n          this.multihash\n        ])\n      } else {\n        throw new Error('unsupported version')\n      }\n\n      // Cache this buffer so it doesn't have to be recreated\n      Object.defineProperty(this, '_buffer', { value: buffer })\n    }\n\n    return buffer\n  }\n\n  /**\n   * Get the prefix of the CID.\n   *\n   * @returns {Buffer}\n   * @readonly\n   */\n  get prefix () {\n    return Buffer.concat([\n      Buffer.from(`0${this.version}`, 'hex'),\n      multicodec.getCodeVarint(this.codec),\n      mh.prefix(this.multihash)\n    ])\n  }\n\n  /**\n   * Convert to a CID of version `0`.\n   *\n   * @returns {CID}\n   */\n  toV0 () {\n    if (this.codec !== 'dag-pb') {\n      throw new Error('Cannot convert a non dag-pb CID to CIDv0')\n    }\n\n    const { name, length } = mh.decode(this.multihash)\n\n    if (name !== 'sha2-256') {\n      throw new Error('Cannot convert non sha2-256 multihash CID to CIDv0')\n    }\n\n    if (length !== 32) {\n      throw new Error('Cannot convert non 32 byte multihash CID to CIDv0')\n    }\n\n    return new _CID(0, this.codec, this.multihash)\n  }\n\n  /**\n   * Convert to a CID of version `1`.\n   *\n   * @returns {CID}\n   */\n  toV1 () {\n    return new _CID(1, this.codec, this.multihash)\n  }\n\n  /**\n   * Encode the CID into a string.\n   *\n   * @param {string} [base=this.multibaseName] - Base encoding to use.\n   * @returns {string}\n   */\n  toBaseEncodedString (base = this.multibaseName) {\n    if (this.string && base === this.multibaseName) {\n      return this.string\n    }\n    let str = null\n    if (this.version === 0) {\n      if (base !== 'base58btc') {\n        throw new Error('not supported with CIDv0, to support different bases, please migrate the instance do CIDv1, you can do that through cid.toV1()')\n      }\n      str = mh.toB58String(this.multihash)\n    } else if (this.version === 1) {\n      str = multibase.encode(base, this.buffer).toString()\n    } else {\n      throw new Error('unsupported version')\n    }\n    if (base === this.multibaseName) {\n      // cache the string value\n      Object.defineProperty(this, 'string', { value: str })\n    }\n    return str\n  }\n\n  /**\n   * CID(QmdfTbBqBPQ7VNxZEYEj14VmRuZBkqFbiwReogJgS1zR1n)\n   *\n   * @returns {String}\n   */\n  [Symbol.for('nodejs.util.inspect.custom')] () {\n    return 'CID(' + this.toString() + ')'\n  }\n\n  toString (base) {\n    return this.toBaseEncodedString(base)\n  }\n\n  /**\n   * Serialize to a plain object.\n   *\n   * @returns {SerializedCID}\n   */\n  toJSON () {\n    return {\n      codec: this.codec,\n      version: this.version,\n      hash: this.multihash\n    }\n  }\n\n  /**\n   * Compare equality with another CID.\n   *\n   * @param {CID} other\n   * @returns {bool}\n   */\n  equals (other) {\n    return this.codec === other.codec &&\n      this.version === other.version &&\n      this.multihash.equals(other.multihash)\n  }\n\n  /**\n   * Test if the given input is a valid CID object.\n   * Throws if it is not.\n   *\n   * @param {any} other\n   * @returns {void}\n   */\n  static validateCID (other) {\n    const errorMsg = CIDUtil.checkCIDComponents(other)\n    if (errorMsg) {\n      throw new Error(errorMsg)\n    }\n  }\n}\n\nconst _CID = withIs(CID, {\n  className: 'CID',\n  symbolName: '@ipld/js-cid/CID'\n})\n\n_CID.codecs = codecs\n\nmodule.exports = _CID\n", "\"use strict\";\n\nvar g; // This works in non-strict mode\n\ng = function () {\n  return this;\n}();\n\ntry {\n  // This works if eval is allowed (see CSP)\n  g = g || new Function(\"return this\")();\n} catch (e) {\n  // This works if the window reference is available\n  if (typeof window === \"object\") g = window;\n} // g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\n\nmodule.exports = g;", "'use strict';\n\nexports.byteLength = byteLength;\nexports.toByteArray = toByteArray;\nexports.fromByteArray = fromByteArray;\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n} // Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\n\n\nrevLookup['-'.charCodeAt(0)] = 62;\nrevLookup['_'.charCodeAt(0)] = 63;\n\nfunction getLens(b64) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4');\n  } // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n\n\n  var validLen = b64.indexOf('=');\n  if (validLen === -1) validLen = len;\n  var placeHoldersLen = validLen === len ? 0 : 4 - validLen % 4;\n  return [validLen, placeHoldersLen];\n} // base64 is 4/3 + up to two characters of the original data\n\n\nfunction byteLength(b64) {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(b64, validLen, placeHoldersLen) {\n  return (validLen + placeHoldersLen) * 3 / 4 - placeHoldersLen;\n}\n\nfunction toByteArray(b64) {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n  var curByte = 0; // if there are placeholders, only get up to the last complete 4 chars\n\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n  var i;\n\n  for (i = 0; i < len; i += 4) {\n    tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = tmp >> 16 & 0xFF;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;\n    arr[curByte++] = tmp >> 8 & 0xFF;\n    arr[curByte++] = tmp & 0xFF;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];\n}\n\nfunction encodeChunk(uint8, start, end) {\n  var tmp;\n  var output = [];\n\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16 & 0xFF0000) + (uint8[i + 1] << 8 & 0xFF00) + (uint8[i + 2] & 0xFF);\n    output.push(tripletToBase64(tmp));\n  }\n\n  return output.join('');\n}\n\nfunction fromByteArray(uint8) {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n  // go through the array every three bytes, we'll deal with trailing stuff later\n\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));\n  } // pad the end with zeros, but make sure to not forget the extra bytes\n\n\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[tmp << 4 & 0x3F] + '==');\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(lookup[tmp >> 10] + lookup[tmp >> 4 & 0x3F] + lookup[tmp << 2 & 0x3F] + '=');\n  }\n\n  return parts.join('');\n}", "\"use strict\";\n\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = e << mLen | m;\n  eLen += mLen;\n\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n};", "\"use strict\";\n\nvar toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};", "\"use strict\";\n\nvar basex = require('base-x');\n\nvar ALPHABET = '**********************************************************';\nmodule.exports = basex(ALPHABET);", "\"use strict\";\n\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\n\nvar Buffer = buffer.Buffer; // alternative to using Object.keys for old browsers\n\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\n\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\n\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype); // Copy static methods from Buffer\n\ncopyProps(Buffer, SafeBuffer);\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n\n  return Buffer(arg, encodingOrOffset, length);\n};\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  var buf = Buffer(size);\n\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n\n  return buf;\n};\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return Buffer(size);\n};\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n\n  return buffer.SlowBuffer(size);\n};", "/* eslint quote-props: off */\n\n/* eslint key-spacing: off */\n'use strict';\n\nexports.names = Object.freeze({\n  'identity': 0x0,\n  'sha1': 0x11,\n  'sha2-256': 0x12,\n  'sha2-512': 0x13,\n  'dbl-sha2-256': 0x56,\n  'sha3-224': 0x17,\n  'sha3-256': 0x16,\n  'sha3-384': 0x15,\n  'sha3-512': 0x14,\n  'shake-128': 0x18,\n  'shake-256': 0x19,\n  'keccak-224': 0x1A,\n  'keccak-256': 0x1B,\n  'keccak-384': 0x1C,\n  'keccak-512': 0x1D,\n  'murmur3-128': 0x22,\n  'murmur3-32': 0x23,\n  'blake2b-8': 0xb201,\n  'blake2b-16': 0xb202,\n  'blake2b-24': 0xb203,\n  'blake2b-32': 0xb204,\n  'blake2b-40': 0xb205,\n  'blake2b-48': 0xb206,\n  'blake2b-56': 0xb207,\n  'blake2b-64': 0xb208,\n  'blake2b-72': 0xb209,\n  'blake2b-80': 0xb20a,\n  'blake2b-88': 0xb20b,\n  'blake2b-96': 0xb20c,\n  'blake2b-104': 0xb20d,\n  'blake2b-112': 0xb20e,\n  'blake2b-120': 0xb20f,\n  'blake2b-128': 0xb210,\n  'blake2b-136': 0xb211,\n  'blake2b-144': 0xb212,\n  'blake2b-152': 0xb213,\n  'blake2b-160': 0xb214,\n  'blake2b-168': 0xb215,\n  'blake2b-176': 0xb216,\n  'blake2b-184': 0xb217,\n  'blake2b-192': 0xb218,\n  'blake2b-200': 0xb219,\n  'blake2b-208': 0xb21a,\n  'blake2b-216': 0xb21b,\n  'blake2b-224': 0xb21c,\n  'blake2b-232': 0xb21d,\n  'blake2b-240': 0xb21e,\n  'blake2b-248': 0xb21f,\n  'blake2b-256': 0xb220,\n  'blake2b-264': 0xb221,\n  'blake2b-272': 0xb222,\n  'blake2b-280': 0xb223,\n  'blake2b-288': 0xb224,\n  'blake2b-296': 0xb225,\n  'blake2b-304': 0xb226,\n  'blake2b-312': 0xb227,\n  'blake2b-320': 0xb228,\n  'blake2b-328': 0xb229,\n  'blake2b-336': 0xb22a,\n  'blake2b-344': 0xb22b,\n  'blake2b-352': 0xb22c,\n  'blake2b-360': 0xb22d,\n  'blake2b-368': 0xb22e,\n  'blake2b-376': 0xb22f,\n  'blake2b-384': 0xb230,\n  'blake2b-392': 0xb231,\n  'blake2b-400': 0xb232,\n  'blake2b-408': 0xb233,\n  'blake2b-416': 0xb234,\n  'blake2b-424': 0xb235,\n  'blake2b-432': 0xb236,\n  'blake2b-440': 0xb237,\n  'blake2b-448': 0xb238,\n  'blake2b-456': 0xb239,\n  'blake2b-464': 0xb23a,\n  'blake2b-472': 0xb23b,\n  'blake2b-480': 0xb23c,\n  'blake2b-488': 0xb23d,\n  'blake2b-496': 0xb23e,\n  'blake2b-504': 0xb23f,\n  'blake2b-512': 0xb240,\n  'blake2s-8': 0xb241,\n  'blake2s-16': 0xb242,\n  'blake2s-24': 0xb243,\n  'blake2s-32': 0xb244,\n  'blake2s-40': 0xb245,\n  'blake2s-48': 0xb246,\n  'blake2s-56': 0xb247,\n  'blake2s-64': 0xb248,\n  'blake2s-72': 0xb249,\n  'blake2s-80': 0xb24a,\n  'blake2s-88': 0xb24b,\n  'blake2s-96': 0xb24c,\n  'blake2s-104': 0xb24d,\n  'blake2s-112': 0xb24e,\n  'blake2s-120': 0xb24f,\n  'blake2s-128': 0xb250,\n  'blake2s-136': 0xb251,\n  'blake2s-144': 0xb252,\n  'blake2s-152': 0xb253,\n  'blake2s-160': 0xb254,\n  'blake2s-168': 0xb255,\n  'blake2s-176': 0xb256,\n  'blake2s-184': 0xb257,\n  'blake2s-192': 0xb258,\n  'blake2s-200': 0xb259,\n  'blake2s-208': 0xb25a,\n  'blake2s-216': 0xb25b,\n  'blake2s-224': 0xb25c,\n  'blake2s-232': 0xb25d,\n  'blake2s-240': 0xb25e,\n  'blake2s-248': 0xb25f,\n  'blake2s-256': 0xb260,\n  'Skein256-8': 0xb301,\n  'Skein256-16': 0xb302,\n  'Skein256-24': 0xb303,\n  'Skein256-32': 0xb304,\n  'Skein256-40': 0xb305,\n  'Skein256-48': 0xb306,\n  'Skein256-56': 0xb307,\n  'Skein256-64': 0xb308,\n  'Skein256-72': 0xb309,\n  'Skein256-80': 0xb30a,\n  'Skein256-88': 0xb30b,\n  'Skein256-96': 0xb30c,\n  'Skein256-104': 0xb30d,\n  'Skein256-112': 0xb30e,\n  'Skein256-120': 0xb30f,\n  'Skein256-128': 0xb310,\n  'Skein256-136': 0xb311,\n  'Skein256-144': 0xb312,\n  'Skein256-152': 0xb313,\n  'Skein256-160': 0xb314,\n  'Skein256-168': 0xb315,\n  'Skein256-176': 0xb316,\n  'Skein256-184': 0xb317,\n  'Skein256-192': 0xb318,\n  'Skein256-200': 0xb319,\n  'Skein256-208': 0xb31a,\n  'Skein256-216': 0xb31b,\n  'Skein256-224': 0xb31c,\n  'Skein256-232': 0xb31d,\n  'Skein256-240': 0xb31e,\n  'Skein256-248': 0xb31f,\n  'Skein256-256': 0xb320,\n  'Skein512-8': 0xb321,\n  'Skein512-16': 0xb322,\n  'Skein512-24': 0xb323,\n  'Skein512-32': 0xb324,\n  'Skein512-40': 0xb325,\n  'Skein512-48': 0xb326,\n  'Skein512-56': 0xb327,\n  'Skein512-64': 0xb328,\n  'Skein512-72': 0xb329,\n  'Skein512-80': 0xb32a,\n  'Skein512-88': 0xb32b,\n  'Skein512-96': 0xb32c,\n  'Skein512-104': 0xb32d,\n  'Skein512-112': 0xb32e,\n  'Skein512-120': 0xb32f,\n  'Skein512-128': 0xb330,\n  'Skein512-136': 0xb331,\n  'Skein512-144': 0xb332,\n  'Skein512-152': 0xb333,\n  'Skein512-160': 0xb334,\n  'Skein512-168': 0xb335,\n  'Skein512-176': 0xb336,\n  'Skein512-184': 0xb337,\n  'Skein512-192': 0xb338,\n  'Skein512-200': 0xb339,\n  'Skein512-208': 0xb33a,\n  'Skein512-216': 0xb33b,\n  'Skein512-224': 0xb33c,\n  'Skein512-232': 0xb33d,\n  'Skein512-240': 0xb33e,\n  'Skein512-248': 0xb33f,\n  'Skein512-256': 0xb340,\n  'Skein512-264': 0xb341,\n  'Skein512-272': 0xb342,\n  'Skein512-280': 0xb343,\n  'Skein512-288': 0xb344,\n  'Skein512-296': 0xb345,\n  'Skein512-304': 0xb346,\n  'Skein512-312': 0xb347,\n  'Skein512-320': 0xb348,\n  'Skein512-328': 0xb349,\n  'Skein512-336': 0xb34a,\n  'Skein512-344': 0xb34b,\n  'Skein512-352': 0xb34c,\n  'Skein512-360': 0xb34d,\n  'Skein512-368': 0xb34e,\n  'Skein512-376': 0xb34f,\n  'Skein512-384': 0xb350,\n  'Skein512-392': 0xb351,\n  'Skein512-400': 0xb352,\n  'Skein512-408': 0xb353,\n  'Skein512-416': 0xb354,\n  'Skein512-424': 0xb355,\n  'Skein512-432': 0xb356,\n  'Skein512-440': 0xb357,\n  'Skein512-448': 0xb358,\n  'Skein512-456': 0xb359,\n  'Skein512-464': 0xb35a,\n  'Skein512-472': 0xb35b,\n  'Skein512-480': 0xb35c,\n  'Skein512-488': 0xb35d,\n  'Skein512-496': 0xb35e,\n  'Skein512-504': 0xb35f,\n  'Skein512-512': 0xb360,\n  'Skein1024-8': 0xb361,\n  'Skein1024-16': 0xb362,\n  'Skein1024-24': 0xb363,\n  'Skein1024-32': 0xb364,\n  'Skein1024-40': 0xb365,\n  'Skein1024-48': 0xb366,\n  'Skein1024-56': 0xb367,\n  'Skein1024-64': 0xb368,\n  'Skein1024-72': 0xb369,\n  'Skein1024-80': 0xb36a,\n  'Skein1024-88': 0xb36b,\n  'Skein1024-96': 0xb36c,\n  'Skein1024-104': 0xb36d,\n  'Skein1024-112': 0xb36e,\n  'Skein1024-120': 0xb36f,\n  'Skein1024-128': 0xb370,\n  'Skein1024-136': 0xb371,\n  'Skein1024-144': 0xb372,\n  'Skein1024-152': 0xb373,\n  'Skein1024-160': 0xb374,\n  'Skein1024-168': 0xb375,\n  'Skein1024-176': 0xb376,\n  'Skein1024-184': 0xb377,\n  'Skein1024-192': 0xb378,\n  'Skein1024-200': 0xb379,\n  'Skein1024-208': 0xb37a,\n  'Skein1024-216': 0xb37b,\n  'Skein1024-224': 0xb37c,\n  'Skein1024-232': 0xb37d,\n  'Skein1024-240': 0xb37e,\n  'Skein1024-248': 0xb37f,\n  'Skein1024-256': 0xb380,\n  'Skein1024-264': 0xb381,\n  'Skein1024-272': 0xb382,\n  'Skein1024-280': 0xb383,\n  'Skein1024-288': 0xb384,\n  'Skein1024-296': 0xb385,\n  'Skein1024-304': 0xb386,\n  'Skein1024-312': 0xb387,\n  'Skein1024-320': 0xb388,\n  'Skein1024-328': 0xb389,\n  'Skein1024-336': 0xb38a,\n  'Skein1024-344': 0xb38b,\n  'Skein1024-352': 0xb38c,\n  'Skein1024-360': 0xb38d,\n  'Skein1024-368': 0xb38e,\n  'Skein1024-376': 0xb38f,\n  'Skein1024-384': 0xb390,\n  'Skein1024-392': 0xb391,\n  'Skein1024-400': 0xb392,\n  'Skein1024-408': 0xb393,\n  'Skein1024-416': 0xb394,\n  'Skein1024-424': 0xb395,\n  'Skein1024-432': 0xb396,\n  'Skein1024-440': 0xb397,\n  'Skein1024-448': 0xb398,\n  'Skein1024-456': 0xb399,\n  'Skein1024-464': 0xb39a,\n  'Skein1024-472': 0xb39b,\n  'Skein1024-480': 0xb39c,\n  'Skein1024-488': 0xb39d,\n  'Skein1024-496': 0xb39e,\n  'Skein1024-504': 0xb39f,\n  'Skein1024-512': 0xb3a0,\n  'Skein1024-520': 0xb3a1,\n  'Skein1024-528': 0xb3a2,\n  'Skein1024-536': 0xb3a3,\n  'Skein1024-544': 0xb3a4,\n  'Skein1024-552': 0xb3a5,\n  'Skein1024-560': 0xb3a6,\n  'Skein1024-568': 0xb3a7,\n  'Skein1024-576': 0xb3a8,\n  'Skein1024-584': 0xb3a9,\n  'Skein1024-592': 0xb3aa,\n  'Skein1024-600': 0xb3ab,\n  'Skein1024-608': 0xb3ac,\n  'Skein1024-616': 0xb3ad,\n  'Skein1024-624': 0xb3ae,\n  'Skein1024-632': 0xb3af,\n  'Skein1024-640': 0xb3b0,\n  'Skein1024-648': 0xb3b1,\n  'Skein1024-656': 0xb3b2,\n  'Skein1024-664': 0xb3b3,\n  'Skein1024-672': 0xb3b4,\n  'Skein1024-680': 0xb3b5,\n  'Skein1024-688': 0xb3b6,\n  'Skein1024-696': 0xb3b7,\n  'Skein1024-704': 0xb3b8,\n  'Skein1024-712': 0xb3b9,\n  'Skein1024-720': 0xb3ba,\n  'Skein1024-728': 0xb3bb,\n  'Skein1024-736': 0xb3bc,\n  'Skein1024-744': 0xb3bd,\n  'Skein1024-752': 0xb3be,\n  'Skein1024-760': 0xb3bf,\n  'Skein1024-768': 0xb3c0,\n  'Skein1024-776': 0xb3c1,\n  'Skein1024-784': 0xb3c2,\n  'Skein1024-792': 0xb3c3,\n  'Skein1024-800': 0xb3c4,\n  'Skein1024-808': 0xb3c5,\n  'Skein1024-816': 0xb3c6,\n  'Skein1024-824': 0xb3c7,\n  'Skein1024-832': 0xb3c8,\n  'Skein1024-840': 0xb3c9,\n  'Skein1024-848': 0xb3ca,\n  'Skein1024-856': 0xb3cb,\n  'Skein1024-864': 0xb3cc,\n  'Skein1024-872': 0xb3cd,\n  'Skein1024-880': 0xb3ce,\n  'Skein1024-888': 0xb3cf,\n  'Skein1024-896': 0xb3d0,\n  'Skein1024-904': 0xb3d1,\n  'Skein1024-912': 0xb3d2,\n  'Skein1024-920': 0xb3d3,\n  'Skein1024-928': 0xb3d4,\n  'Skein1024-936': 0xb3d5,\n  'Skein1024-944': 0xb3d6,\n  'Skein1024-952': 0xb3d7,\n  'Skein1024-960': 0xb3d8,\n  'Skein1024-968': 0xb3d9,\n  'Skein1024-976': 0xb3da,\n  'Skein1024-984': 0xb3db,\n  'Skein1024-992': 0xb3dc,\n  'Skein1024-1000': 0xb3dd,\n  'Skein1024-1008': 0xb3de,\n  'Skein1024-1016': 0xb3df,\n  'Skein1024-1024': 0xb3e0\n});\nexports.codes = Object.freeze({\n  0x0: 'identity',\n  // sha family\n  0x11: 'sha1',\n  0x12: 'sha2-256',\n  0x13: 'sha2-512',\n  0x56: 'dbl-sha2-256',\n  0x17: 'sha3-224',\n  0x16: 'sha3-256',\n  0x15: 'sha3-384',\n  0x14: 'sha3-512',\n  0x18: 'shake-128',\n  0x19: 'shake-256',\n  0x1A: 'keccak-224',\n  0x1B: 'keccak-256',\n  0x1C: 'keccak-384',\n  0x1D: 'keccak-512',\n  0x22: 'murmur3-128',\n  0x23: 'murmur3-32',\n  // blake2\n  0xb201: 'blake2b-8',\n  0xb202: 'blake2b-16',\n  0xb203: 'blake2b-24',\n  0xb204: 'blake2b-32',\n  0xb205: 'blake2b-40',\n  0xb206: 'blake2b-48',\n  0xb207: 'blake2b-56',\n  0xb208: 'blake2b-64',\n  0xb209: 'blake2b-72',\n  0xb20a: 'blake2b-80',\n  0xb20b: 'blake2b-88',\n  0xb20c: 'blake2b-96',\n  0xb20d: 'blake2b-104',\n  0xb20e: 'blake2b-112',\n  0xb20f: 'blake2b-120',\n  0xb210: 'blake2b-128',\n  0xb211: 'blake2b-136',\n  0xb212: 'blake2b-144',\n  0xb213: 'blake2b-152',\n  0xb214: 'blake2b-160',\n  0xb215: 'blake2b-168',\n  0xb216: 'blake2b-176',\n  0xb217: 'blake2b-184',\n  0xb218: 'blake2b-192',\n  0xb219: 'blake2b-200',\n  0xb21a: 'blake2b-208',\n  0xb21b: 'blake2b-216',\n  0xb21c: 'blake2b-224',\n  0xb21d: 'blake2b-232',\n  0xb21e: 'blake2b-240',\n  0xb21f: 'blake2b-248',\n  0xb220: 'blake2b-256',\n  0xb221: 'blake2b-264',\n  0xb222: 'blake2b-272',\n  0xb223: 'blake2b-280',\n  0xb224: 'blake2b-288',\n  0xb225: 'blake2b-296',\n  0xb226: 'blake2b-304',\n  0xb227: 'blake2b-312',\n  0xb228: 'blake2b-320',\n  0xb229: 'blake2b-328',\n  0xb22a: 'blake2b-336',\n  0xb22b: 'blake2b-344',\n  0xb22c: 'blake2b-352',\n  0xb22d: 'blake2b-360',\n  0xb22e: 'blake2b-368',\n  0xb22f: 'blake2b-376',\n  0xb230: 'blake2b-384',\n  0xb231: 'blake2b-392',\n  0xb232: 'blake2b-400',\n  0xb233: 'blake2b-408',\n  0xb234: 'blake2b-416',\n  0xb235: 'blake2b-424',\n  0xb236: 'blake2b-432',\n  0xb237: 'blake2b-440',\n  0xb238: 'blake2b-448',\n  0xb239: 'blake2b-456',\n  0xb23a: 'blake2b-464',\n  0xb23b: 'blake2b-472',\n  0xb23c: 'blake2b-480',\n  0xb23d: 'blake2b-488',\n  0xb23e: 'blake2b-496',\n  0xb23f: 'blake2b-504',\n  0xb240: 'blake2b-512',\n  0xb241: 'blake2s-8',\n  0xb242: 'blake2s-16',\n  0xb243: 'blake2s-24',\n  0xb244: 'blake2s-32',\n  0xb245: 'blake2s-40',\n  0xb246: 'blake2s-48',\n  0xb247: 'blake2s-56',\n  0xb248: 'blake2s-64',\n  0xb249: 'blake2s-72',\n  0xb24a: 'blake2s-80',\n  0xb24b: 'blake2s-88',\n  0xb24c: 'blake2s-96',\n  0xb24d: 'blake2s-104',\n  0xb24e: 'blake2s-112',\n  0xb24f: 'blake2s-120',\n  0xb250: 'blake2s-128',\n  0xb251: 'blake2s-136',\n  0xb252: 'blake2s-144',\n  0xb253: 'blake2s-152',\n  0xb254: 'blake2s-160',\n  0xb255: 'blake2s-168',\n  0xb256: 'blake2s-176',\n  0xb257: 'blake2s-184',\n  0xb258: 'blake2s-192',\n  0xb259: 'blake2s-200',\n  0xb25a: 'blake2s-208',\n  0xb25b: 'blake2s-216',\n  0xb25c: 'blake2s-224',\n  0xb25d: 'blake2s-232',\n  0xb25e: 'blake2s-240',\n  0xb25f: 'blake2s-248',\n  0xb260: 'blake2s-256',\n  // skein\n  0xb301: 'Skein256-8',\n  0xb302: 'Skein256-16',\n  0xb303: 'Skein256-24',\n  0xb304: 'Skein256-32',\n  0xb305: 'Skein256-40',\n  0xb306: 'Skein256-48',\n  0xb307: 'Skein256-56',\n  0xb308: 'Skein256-64',\n  0xb309: 'Skein256-72',\n  0xb30a: 'Skein256-80',\n  0xb30b: 'Skein256-88',\n  0xb30c: 'Skein256-96',\n  0xb30d: 'Skein256-104',\n  0xb30e: 'Skein256-112',\n  0xb30f: 'Skein256-120',\n  0xb310: 'Skein256-128',\n  0xb311: 'Skein256-136',\n  0xb312: 'Skein256-144',\n  0xb313: 'Skein256-152',\n  0xb314: 'Skein256-160',\n  0xb315: 'Skein256-168',\n  0xb316: 'Skein256-176',\n  0xb317: 'Skein256-184',\n  0xb318: 'Skein256-192',\n  0xb319: 'Skein256-200',\n  0xb31a: 'Skein256-208',\n  0xb31b: 'Skein256-216',\n  0xb31c: 'Skein256-224',\n  0xb31d: 'Skein256-232',\n  0xb31e: 'Skein256-240',\n  0xb31f: 'Skein256-248',\n  0xb320: 'Skein256-256',\n  0xb321: 'Skein512-8',\n  0xb322: 'Skein512-16',\n  0xb323: 'Skein512-24',\n  0xb324: 'Skein512-32',\n  0xb325: 'Skein512-40',\n  0xb326: 'Skein512-48',\n  0xb327: 'Skein512-56',\n  0xb328: 'Skein512-64',\n  0xb329: 'Skein512-72',\n  0xb32a: 'Skein512-80',\n  0xb32b: 'Skein512-88',\n  0xb32c: 'Skein512-96',\n  0xb32d: 'Skein512-104',\n  0xb32e: 'Skein512-112',\n  0xb32f: 'Skein512-120',\n  0xb330: 'Skein512-128',\n  0xb331: 'Skein512-136',\n  0xb332: 'Skein512-144',\n  0xb333: 'Skein512-152',\n  0xb334: 'Skein512-160',\n  0xb335: 'Skein512-168',\n  0xb336: 'Skein512-176',\n  0xb337: 'Skein512-184',\n  0xb338: 'Skein512-192',\n  0xb339: 'Skein512-200',\n  0xb33a: 'Skein512-208',\n  0xb33b: 'Skein512-216',\n  0xb33c: 'Skein512-224',\n  0xb33d: 'Skein512-232',\n  0xb33e: 'Skein512-240',\n  0xb33f: 'Skein512-248',\n  0xb340: 'Skein512-256',\n  0xb341: 'Skein512-264',\n  0xb342: 'Skein512-272',\n  0xb343: 'Skein512-280',\n  0xb344: 'Skein512-288',\n  0xb345: 'Skein512-296',\n  0xb346: 'Skein512-304',\n  0xb347: 'Skein512-312',\n  0xb348: 'Skein512-320',\n  0xb349: 'Skein512-328',\n  0xb34a: 'Skein512-336',\n  0xb34b: 'Skein512-344',\n  0xb34c: 'Skein512-352',\n  0xb34d: 'Skein512-360',\n  0xb34e: 'Skein512-368',\n  0xb34f: 'Skein512-376',\n  0xb350: 'Skein512-384',\n  0xb351: 'Skein512-392',\n  0xb352: 'Skein512-400',\n  0xb353: 'Skein512-408',\n  0xb354: 'Skein512-416',\n  0xb355: 'Skein512-424',\n  0xb356: 'Skein512-432',\n  0xb357: 'Skein512-440',\n  0xb358: 'Skein512-448',\n  0xb359: 'Skein512-456',\n  0xb35a: 'Skein512-464',\n  0xb35b: 'Skein512-472',\n  0xb35c: 'Skein512-480',\n  0xb35d: 'Skein512-488',\n  0xb35e: 'Skein512-496',\n  0xb35f: 'Skein512-504',\n  0xb360: 'Skein512-512',\n  0xb361: 'Skein1024-8',\n  0xb362: 'Skein1024-16',\n  0xb363: 'Skein1024-24',\n  0xb364: 'Skein1024-32',\n  0xb365: 'Skein1024-40',\n  0xb366: 'Skein1024-48',\n  0xb367: 'Skein1024-56',\n  0xb368: 'Skein1024-64',\n  0xb369: 'Skein1024-72',\n  0xb36a: 'Skein1024-80',\n  0xb36b: 'Skein1024-88',\n  0xb36c: 'Skein1024-96',\n  0xb36d: 'Skein1024-104',\n  0xb36e: 'Skein1024-112',\n  0xb36f: 'Skein1024-120',\n  0xb370: 'Skein1024-128',\n  0xb371: 'Skein1024-136',\n  0xb372: 'Skein1024-144',\n  0xb373: 'Skein1024-152',\n  0xb374: 'Skein1024-160',\n  0xb375: 'Skein1024-168',\n  0xb376: 'Skein1024-176',\n  0xb377: 'Skein1024-184',\n  0xb378: 'Skein1024-192',\n  0xb379: 'Skein1024-200',\n  0xb37a: 'Skein1024-208',\n  0xb37b: 'Skein1024-216',\n  0xb37c: 'Skein1024-224',\n  0xb37d: 'Skein1024-232',\n  0xb37e: 'Skein1024-240',\n  0xb37f: 'Skein1024-248',\n  0xb380: 'Skein1024-256',\n  0xb381: 'Skein1024-264',\n  0xb382: 'Skein1024-272',\n  0xb383: 'Skein1024-280',\n  0xb384: 'Skein1024-288',\n  0xb385: 'Skein1024-296',\n  0xb386: 'Skein1024-304',\n  0xb387: 'Skein1024-312',\n  0xb388: 'Skein1024-320',\n  0xb389: 'Skein1024-328',\n  0xb38a: 'Skein1024-336',\n  0xb38b: 'Skein1024-344',\n  0xb38c: 'Skein1024-352',\n  0xb38d: 'Skein1024-360',\n  0xb38e: 'Skein1024-368',\n  0xb38f: 'Skein1024-376',\n  0xb390: 'Skein1024-384',\n  0xb391: 'Skein1024-392',\n  0xb392: 'Skein1024-400',\n  0xb393: 'Skein1024-408',\n  0xb394: 'Skein1024-416',\n  0xb395: 'Skein1024-424',\n  0xb396: 'Skein1024-432',\n  0xb397: 'Skein1024-440',\n  0xb398: 'Skein1024-448',\n  0xb399: 'Skein1024-456',\n  0xb39a: 'Skein1024-464',\n  0xb39b: 'Skein1024-472',\n  0xb39c: 'Skein1024-480',\n  0xb39d: 'Skein1024-488',\n  0xb39e: 'Skein1024-496',\n  0xb39f: 'Skein1024-504',\n  0xb3a0: 'Skein1024-512',\n  0xb3a1: 'Skein1024-520',\n  0xb3a2: 'Skein1024-528',\n  0xb3a3: 'Skein1024-536',\n  0xb3a4: 'Skein1024-544',\n  0xb3a5: 'Skein1024-552',\n  0xb3a6: 'Skein1024-560',\n  0xb3a7: 'Skein1024-568',\n  0xb3a8: 'Skein1024-576',\n  0xb3a9: 'Skein1024-584',\n  0xb3aa: 'Skein1024-592',\n  0xb3ab: 'Skein1024-600',\n  0xb3ac: 'Skein1024-608',\n  0xb3ad: 'Skein1024-616',\n  0xb3ae: 'Skein1024-624',\n  0xb3af: 'Skein1024-632',\n  0xb3b0: 'Skein1024-640',\n  0xb3b1: 'Skein1024-648',\n  0xb3b2: 'Skein1024-656',\n  0xb3b3: 'Skein1024-664',\n  0xb3b4: 'Skein1024-672',\n  0xb3b5: 'Skein1024-680',\n  0xb3b6: 'Skein1024-688',\n  0xb3b7: 'Skein1024-696',\n  0xb3b8: 'Skein1024-704',\n  0xb3b9: 'Skein1024-712',\n  0xb3ba: 'Skein1024-720',\n  0xb3bb: 'Skein1024-728',\n  0xb3bc: 'Skein1024-736',\n  0xb3bd: 'Skein1024-744',\n  0xb3be: 'Skein1024-752',\n  0xb3bf: 'Skein1024-760',\n  0xb3c0: 'Skein1024-768',\n  0xb3c1: 'Skein1024-776',\n  0xb3c2: 'Skein1024-784',\n  0xb3c3: 'Skein1024-792',\n  0xb3c4: 'Skein1024-800',\n  0xb3c5: 'Skein1024-808',\n  0xb3c6: 'Skein1024-816',\n  0xb3c7: 'Skein1024-824',\n  0xb3c8: 'Skein1024-832',\n  0xb3c9: 'Skein1024-840',\n  0xb3ca: 'Skein1024-848',\n  0xb3cb: 'Skein1024-856',\n  0xb3cc: 'Skein1024-864',\n  0xb3cd: 'Skein1024-872',\n  0xb3ce: 'Skein1024-880',\n  0xb3cf: 'Skein1024-888',\n  0xb3d0: 'Skein1024-896',\n  0xb3d1: 'Skein1024-904',\n  0xb3d2: 'Skein1024-912',\n  0xb3d3: 'Skein1024-920',\n  0xb3d4: 'Skein1024-928',\n  0xb3d5: 'Skein1024-936',\n  0xb3d6: 'Skein1024-944',\n  0xb3d7: 'Skein1024-952',\n  0xb3d8: 'Skein1024-960',\n  0xb3d9: 'Skein1024-968',\n  0xb3da: 'Skein1024-976',\n  0xb3db: 'Skein1024-984',\n  0xb3dc: 'Skein1024-992',\n  0xb3dd: 'Skein1024-1000',\n  0xb3de: 'Skein1024-1008',\n  0xb3df: 'Skein1024-1016',\n  0xb3e0: 'Skein1024-1024'\n});\nexports.defaultLengths = Object.freeze({\n  0x11: 20,\n  0x12: 32,\n  0x13: 64,\n  0x56: 32,\n  0x17: 28,\n  0x16: 32,\n  0x15: 48,\n  0x14: 64,\n  0x18: 32,\n  0x19: 64,\n  0x1A: 28,\n  0x1B: 32,\n  0x1C: 48,\n  0x1D: 64,\n  0x22: 32,\n  0xb201: 0x01,\n  0xb202: 0x02,\n  0xb203: 0x03,\n  0xb204: 0x04,\n  0xb205: 0x05,\n  0xb206: 0x06,\n  0xb207: 0x07,\n  0xb208: 0x08,\n  0xb209: 0x09,\n  0xb20a: 0x0a,\n  0xb20b: 0x0b,\n  0xb20c: 0x0c,\n  0xb20d: 0x0d,\n  0xb20e: 0x0e,\n  0xb20f: 0x0f,\n  0xb210: 0x10,\n  0xb211: 0x11,\n  0xb212: 0x12,\n  0xb213: 0x13,\n  0xb214: 0x14,\n  0xb215: 0x15,\n  0xb216: 0x16,\n  0xb217: 0x17,\n  0xb218: 0x18,\n  0xb219: 0x19,\n  0xb21a: 0x1a,\n  0xb21b: 0x1b,\n  0xb21c: 0x1c,\n  0xb21d: 0x1d,\n  0xb21e: 0x1e,\n  0xb21f: 0x1f,\n  0xb220: 0x20,\n  0xb221: 0x21,\n  0xb222: 0x22,\n  0xb223: 0x23,\n  0xb224: 0x24,\n  0xb225: 0x25,\n  0xb226: 0x26,\n  0xb227: 0x27,\n  0xb228: 0x28,\n  0xb229: 0x29,\n  0xb22a: 0x2a,\n  0xb22b: 0x2b,\n  0xb22c: 0x2c,\n  0xb22d: 0x2d,\n  0xb22e: 0x2e,\n  0xb22f: 0x2f,\n  0xb230: 0x30,\n  0xb231: 0x31,\n  0xb232: 0x32,\n  0xb233: 0x33,\n  0xb234: 0x34,\n  0xb235: 0x35,\n  0xb236: 0x36,\n  0xb237: 0x37,\n  0xb238: 0x38,\n  0xb239: 0x39,\n  0xb23a: 0x3a,\n  0xb23b: 0x3b,\n  0xb23c: 0x3c,\n  0xb23d: 0x3d,\n  0xb23e: 0x3e,\n  0xb23f: 0x3f,\n  0xb240: 0x40,\n  0xb241: 0x01,\n  0xb242: 0x02,\n  0xb243: 0x03,\n  0xb244: 0x04,\n  0xb245: 0x05,\n  0xb246: 0x06,\n  0xb247: 0x07,\n  0xb248: 0x08,\n  0xb249: 0x09,\n  0xb24a: 0x0a,\n  0xb24b: 0x0b,\n  0xb24c: 0x0c,\n  0xb24d: 0x0d,\n  0xb24e: 0x0e,\n  0xb24f: 0x0f,\n  0xb250: 0x10,\n  0xb251: 0x11,\n  0xb252: 0x12,\n  0xb253: 0x13,\n  0xb254: 0x14,\n  0xb255: 0x15,\n  0xb256: 0x16,\n  0xb257: 0x17,\n  0xb258: 0x18,\n  0xb259: 0x19,\n  0xb25a: 0x1a,\n  0xb25b: 0x1b,\n  0xb25c: 0x1c,\n  0xb25d: 0x1d,\n  0xb25e: 0x1e,\n  0xb25f: 0x1f,\n  0xb260: 0x20,\n  0xb301: 0x01,\n  0xb302: 0x02,\n  0xb303: 0x03,\n  0xb304: 0x04,\n  0xb305: 0x05,\n  0xb306: 0x06,\n  0xb307: 0x07,\n  0xb308: 0x08,\n  0xb309: 0x09,\n  0xb30a: 0x0a,\n  0xb30b: 0x0b,\n  0xb30c: 0x0c,\n  0xb30d: 0x0d,\n  0xb30e: 0x0e,\n  0xb30f: 0x0f,\n  0xb310: 0x10,\n  0xb311: 0x11,\n  0xb312: 0x12,\n  0xb313: 0x13,\n  0xb314: 0x14,\n  0xb315: 0x15,\n  0xb316: 0x16,\n  0xb317: 0x17,\n  0xb318: 0x18,\n  0xb319: 0x19,\n  0xb31a: 0x1a,\n  0xb31b: 0x1b,\n  0xb31c: 0x1c,\n  0xb31d: 0x1d,\n  0xb31e: 0x1e,\n  0xb31f: 0x1f,\n  0xb320: 0x20,\n  0xb321: 0x01,\n  0xb322: 0x02,\n  0xb323: 0x03,\n  0xb324: 0x04,\n  0xb325: 0x05,\n  0xb326: 0x06,\n  0xb327: 0x07,\n  0xb328: 0x08,\n  0xb329: 0x09,\n  0xb32a: 0x0a,\n  0xb32b: 0x0b,\n  0xb32c: 0x0c,\n  0xb32d: 0x0d,\n  0xb32e: 0x0e,\n  0xb32f: 0x0f,\n  0xb330: 0x10,\n  0xb331: 0x11,\n  0xb332: 0x12,\n  0xb333: 0x13,\n  0xb334: 0x14,\n  0xb335: 0x15,\n  0xb336: 0x16,\n  0xb337: 0x17,\n  0xb338: 0x18,\n  0xb339: 0x19,\n  0xb33a: 0x1a,\n  0xb33b: 0x1b,\n  0xb33c: 0x1c,\n  0xb33d: 0x1d,\n  0xb33e: 0x1e,\n  0xb33f: 0x1f,\n  0xb340: 0x20,\n  0xb341: 0x21,\n  0xb342: 0x22,\n  0xb343: 0x23,\n  0xb344: 0x24,\n  0xb345: 0x25,\n  0xb346: 0x26,\n  0xb347: 0x27,\n  0xb348: 0x28,\n  0xb349: 0x29,\n  0xb34a: 0x2a,\n  0xb34b: 0x2b,\n  0xb34c: 0x2c,\n  0xb34d: 0x2d,\n  0xb34e: 0x2e,\n  0xb34f: 0x2f,\n  0xb350: 0x30,\n  0xb351: 0x31,\n  0xb352: 0x32,\n  0xb353: 0x33,\n  0xb354: 0x34,\n  0xb355: 0x35,\n  0xb356: 0x36,\n  0xb357: 0x37,\n  0xb358: 0x38,\n  0xb359: 0x39,\n  0xb35a: 0x3a,\n  0xb35b: 0x3b,\n  0xb35c: 0x3c,\n  0xb35d: 0x3d,\n  0xb35e: 0x3e,\n  0xb35f: 0x3f,\n  0xb360: 0x40,\n  0xb361: 0x01,\n  0xb362: 0x02,\n  0xb363: 0x03,\n  0xb364: 0x04,\n  0xb365: 0x05,\n  0xb366: 0x06,\n  0xb367: 0x07,\n  0xb368: 0x08,\n  0xb369: 0x09,\n  0xb36a: 0x0a,\n  0xb36b: 0x0b,\n  0xb36c: 0x0c,\n  0xb36d: 0x0d,\n  0xb36e: 0x0e,\n  0xb36f: 0x0f,\n  0xb370: 0x10,\n  0xb371: 0x11,\n  0xb372: 0x12,\n  0xb373: 0x13,\n  0xb374: 0x14,\n  0xb375: 0x15,\n  0xb376: 0x16,\n  0xb377: 0x17,\n  0xb378: 0x18,\n  0xb379: 0x19,\n  0xb37a: 0x1a,\n  0xb37b: 0x1b,\n  0xb37c: 0x1c,\n  0xb37d: 0x1d,\n  0xb37e: 0x1e,\n  0xb37f: 0x1f,\n  0xb380: 0x20,\n  0xb381: 0x21,\n  0xb382: 0x22,\n  0xb383: 0x23,\n  0xb384: 0x24,\n  0xb385: 0x25,\n  0xb386: 0x26,\n  0xb387: 0x27,\n  0xb388: 0x28,\n  0xb389: 0x29,\n  0xb38a: 0x2a,\n  0xb38b: 0x2b,\n  0xb38c: 0x2c,\n  0xb38d: 0x2d,\n  0xb38e: 0x2e,\n  0xb38f: 0x2f,\n  0xb390: 0x30,\n  0xb391: 0x31,\n  0xb392: 0x32,\n  0xb393: 0x33,\n  0xb394: 0x34,\n  0xb395: 0x35,\n  0xb396: 0x36,\n  0xb397: 0x37,\n  0xb398: 0x38,\n  0xb399: 0x39,\n  0xb39a: 0x3a,\n  0xb39b: 0x3b,\n  0xb39c: 0x3c,\n  0xb39d: 0x3d,\n  0xb39e: 0x3e,\n  0xb39f: 0x3f,\n  0xb3a0: 0x40,\n  0xb3a1: 0x41,\n  0xb3a2: 0x42,\n  0xb3a3: 0x43,\n  0xb3a4: 0x44,\n  0xb3a5: 0x45,\n  0xb3a6: 0x46,\n  0xb3a7: 0x47,\n  0xb3a8: 0x48,\n  0xb3a9: 0x49,\n  0xb3aa: 0x4a,\n  0xb3ab: 0x4b,\n  0xb3ac: 0x4c,\n  0xb3ad: 0x4d,\n  0xb3ae: 0x4e,\n  0xb3af: 0x4f,\n  0xb3b0: 0x50,\n  0xb3b1: 0x51,\n  0xb3b2: 0x52,\n  0xb3b3: 0x53,\n  0xb3b4: 0x54,\n  0xb3b5: 0x55,\n  0xb3b6: 0x56,\n  0xb3b7: 0x57,\n  0xb3b8: 0x58,\n  0xb3b9: 0x59,\n  0xb3ba: 0x5a,\n  0xb3bb: 0x5b,\n  0xb3bc: 0x5c,\n  0xb3bd: 0x5d,\n  0xb3be: 0x5e,\n  0xb3bf: 0x5f,\n  0xb3c0: 0x60,\n  0xb3c1: 0x61,\n  0xb3c2: 0x62,\n  0xb3c3: 0x63,\n  0xb3c4: 0x64,\n  0xb3c5: 0x65,\n  0xb3c6: 0x66,\n  0xb3c7: 0x67,\n  0xb3c8: 0x68,\n  0xb3c9: 0x69,\n  0xb3ca: 0x6a,\n  0xb3cb: 0x6b,\n  0xb3cc: 0x6c,\n  0xb3cd: 0x6d,\n  0xb3ce: 0x6e,\n  0xb3cf: 0x6f,\n  0xb3d0: 0x70,\n  0xb3d1: 0x71,\n  0xb3d2: 0x72,\n  0xb3d3: 0x73,\n  0xb3d4: 0x74,\n  0xb3d5: 0x75,\n  0xb3d6: 0x76,\n  0xb3d7: 0x77,\n  0xb3d8: 0x78,\n  0xb3d9: 0x79,\n  0xb3da: 0x7a,\n  0xb3db: 0x7b,\n  0xb3dc: 0x7c,\n  0xb3dd: 0x7d,\n  0xb3de: 0x7e,\n  0xb3df: 0x7f,\n  0xb3e0: 0x80\n});", "\"use strict\";\n\nmodule.exports = encode;\nvar MSB = 0x80,\n    REST = 0x7F,\n    MSBALL = ~REST,\n    INT = Math.pow(2, 31);\n\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n\n  while (num >= INT) {\n    out[offset++] = num & 0xFF | MSB;\n    num /= 128;\n  }\n\n  while (num & MSBALL) {\n    out[offset++] = num & 0xFF | MSB;\n    num >>>= 7;\n  }\n\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}", "\"use strict\";\n\nmodule.exports = read;\nvar MSB = 0x80,\n    REST = 0x7F;\n\nfunction read(buf, offset) {\n  var res = 0,\n      offset = offset || 0,\n      shift = 0,\n      counter = offset,\n      b,\n      l = buf.length;\n\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST) << shift : (b & REST) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB);\n\n  read.bytes = counter - offset;\n  return res;\n}", "\"use strict\";\n\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\n\nmodule.exports = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};", "/**\n * Implementation of the [multibase](https://github.com/multiformats/multibase) specification.\n * @module Multibase\n */\n'use strict';\n\nconst constants = require('./constants');\n\nexports = module.exports = multibase;\nexports.encode = encode;\nexports.decode = decode;\nexports.isEncoded = isEncoded;\nexports.names = Object.freeze(Object.keys(constants.names));\nexports.codes = Object.freeze(Object.keys(constants.codes));\nconst errNotSupported = new Error('Unsupported encoding');\n/**\n * Create a new buffer with the multibase varint+code.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be prefixed with multibase.\n * @memberof Multibase\n * @returns {Buffer}\n */\n\nfunction multibase(nameOrCode, buf) {\n  if (!buf) {\n    throw new Error('requires an encoded buffer');\n  }\n\n  const base = getBase(nameOrCode);\n  const codeBuf = Buffer.from(base.code);\n  const name = base.name;\n  validEncode(name, buf);\n  return Buffer.concat([codeBuf, buf]);\n}\n/**\n * Encode data with the specified base and add the multibase prefix.\n *\n * @param {string|number} nameOrCode - The multibase name or code number.\n * @param {Buffer} buf - The data to be encoded.\n * @returns {Buffer}\n * @memberof Multibase\n */\n\n\nfunction encode(nameOrCode, buf) {\n  const base = getBase(nameOrCode);\n  const name = base.name;\n  return multibase(name, Buffer.from(base.encode(buf)));\n}\n/**\n * Takes a buffer or string encoded with multibase header, decodes it and\n * returns the decoded buffer\n *\n * @param {Buffer|string} bufOrString\n * @returns {Buffer}\n * @memberof Multibase\n *\n */\n\n\nfunction decode(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  }\n\n  const code = bufOrString.substring(0, 1);\n  bufOrString = bufOrString.substring(1, bufOrString.length);\n\n  if (typeof bufOrString === 'string') {\n    bufOrString = Buffer.from(bufOrString);\n  }\n\n  const base = getBase(code);\n  return Buffer.from(base.decode(bufOrString.toString()));\n}\n/**\n * Is the given data multibase encoded?\n *\n * @param {Buffer|string} bufOrString\n * @returns {boolean}\n * @memberof Multibase\n */\n\n\nfunction isEncoded(bufOrString) {\n  if (Buffer.isBuffer(bufOrString)) {\n    bufOrString = bufOrString.toString();\n  } // Ensure bufOrString is a string\n\n\n  if (Object.prototype.toString.call(bufOrString) !== '[object String]') {\n    return false;\n  }\n\n  const code = bufOrString.substring(0, 1);\n\n  try {\n    const base = getBase(code);\n    return base.name;\n  } catch (err) {\n    return false;\n  }\n}\n/**\n * @param {string} name\n * @param {Buffer} buf\n * @private\n * @returns {undefined}\n */\n\n\nfunction validEncode(name, buf) {\n  const base = getBase(name);\n  base.decode(buf.toString());\n}\n\nfunction getBase(nameOrCode) {\n  let base;\n\n  if (constants.names[nameOrCode]) {\n    base = constants.names[nameOrCode];\n  } else if (constants.codes[nameOrCode]) {\n    base = constants.codes[nameOrCode];\n  } else {\n    throw errNotSupported;\n  }\n\n  if (!base.isImplemented()) {\n    throw new Error('Base ' + nameOrCode + ' is not implemented yet');\n  }\n\n  return base;\n}", "'use strict';\n\nconst Base = require('./base.js');\n\nconst baseX = require('base-x');\n\nconst base16 = require('./base16');\n\nconst base32 = require('./base32');\n\nconst base64 = require('./base64'); // name, code, implementation, alphabet\n\n\nconst constants = [['base1', '1', '', '1'], ['base2', '0', baseX, '01'], ['base8', '7', baseX, '01234567'], ['base10', '9', baseX, '0123456789'], ['base16', 'f', base16, '0123456789abcdef'], ['base32', 'b', base32, 'abcdefghijklmnopqrstuvwxyz234567'], ['base32pad', 'c', base32, 'abcdefghijklmnopqrstuvwxyz234567='], ['base32hex', 'v', base32, '0123456789abcdefghijklmnopqrstuv'], ['base32hexpad', 't', base32, '0123456789abcdefghijklmnopqrstuv='], ['base32z', 'h', base32, 'ybndrfg8ejkmcpqxot1uwisza345h769'], ['base58flickr', 'Z', baseX, '**********************************************************'], ['base58btc', 'z', baseX, '**********************************************************'], ['base64', 'm', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'], ['base64pad', 'M', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='], ['base64url', 'u', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'], ['base64urlpad', 'U', base64, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=']];\nconst names = constants.reduce((prev, tupple) => {\n  prev[tupple[0]] = new Base(tupple[0], tupple[1], tupple[2], tupple[3]);\n  return prev;\n}, {});\nconst codes = constants.reduce((prev, tupple) => {\n  prev[tupple[1]] = names[tupple[0]];\n  return prev;\n}, {});\nmodule.exports = {\n  names: names,\n  codes: codes\n};", "'use strict';\n\nclass Base {\n  constructor(name, code, implementation, alphabet) {\n    this.name = name;\n    this.code = code;\n    this.alphabet = alphabet;\n\n    if (implementation && alphabet) {\n      this.engine = implementation(alphabet);\n    }\n  }\n\n  encode(stringOrBuffer) {\n    return this.engine.encode(stringOrBuffer);\n  }\n\n  decode(stringOrBuffer) {\n    return this.engine.decode(stringOrBuffer);\n  }\n\n  isImplemented() {\n    return this.engine;\n  }\n\n}\n\nmodule.exports = Base;", "'use strict';\n\nmodule.exports = function base16(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return Buffer.from(input).toString('hex');\n      }\n\n      return input.toString('hex');\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base16 character');\n        }\n      }\n\n      return Buffer.from(input, 'hex');\n    }\n\n  };\n};", "'use strict';\n\nfunction decode(input, alphabet) {\n  input = input.replace(new RegExp('=', 'g'), '');\n  let length = input.length;\n  let bits = 0;\n  let value = 0;\n  let index = 0;\n  let output = new Uint8Array(length * 5 / 8 | 0);\n\n  for (let i = 0; i < length; i++) {\n    value = value << 5 | alphabet.indexOf(input[i]);\n    bits += 5;\n\n    if (bits >= 8) {\n      output[index++] = value >>> bits - 8 & 255;\n      bits -= 8;\n    }\n  }\n\n  return output.buffer;\n}\n\nfunction encode(buffer, alphabet) {\n  let length = buffer.byteLength;\n  let view = new Uint8Array(buffer);\n  let padding = alphabet.indexOf('=') === alphabet.length - 1;\n\n  if (padding) {\n    alphabet = alphabet.substring(0, alphabet.length - 2);\n  }\n\n  let bits = 0;\n  let value = 0;\n  let output = '';\n\n  for (let i = 0; i < length; i++) {\n    value = value << 8 | view[i];\n    bits += 8;\n\n    while (bits >= 5) {\n      output += alphabet[value >>> bits - 5 & 31];\n      bits -= 5;\n    }\n  }\n\n  if (bits > 0) {\n    output += alphabet[value << 5 - bits & 31];\n  }\n\n  if (padding) {\n    while (output.length % 8 !== 0) {\n      output += '=';\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = function base32(alphabet) {\n  return {\n    encode(input) {\n      if (typeof input === 'string') {\n        return encode(Buffer.from(input), alphabet);\n      }\n\n      return encode(input, alphabet);\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base32 character');\n        }\n      }\n\n      return decode(input, alphabet);\n    }\n\n  };\n};", "'use strict';\n\nmodule.exports = function base64(alphabet) {\n  // The alphabet is only used to know:\n  //   1. If padding is enabled (must contain '=')\n  //   2. If the output must be url-safe (must contain '-' and '_')\n  //   3. If the input of the output function is valid\n  // The alphabets from RFC 4648 are always used.\n  const padding = alphabet.indexOf('=') > -1;\n  const url = alphabet.indexOf('-') > -1 && alphabet.indexOf('_') > -1;\n  return {\n    encode(input) {\n      let output = '';\n\n      if (typeof input === 'string') {\n        output = Buffer.from(input).toString('base64');\n      } else {\n        output = input.toString('base64');\n      }\n\n      if (url) {\n        output = output.replace(/\\+/g, '-').replace(/\\//g, '_');\n      }\n\n      const pad = output.indexOf('=');\n\n      if (pad > 0 && !padding) {\n        output = output.substring(0, pad);\n      }\n\n      return output;\n    },\n\n    decode(input) {\n      for (let char of input) {\n        if (alphabet.indexOf(char) < 0) {\n          throw new Error('invalid base64 character');\n        }\n      }\n\n      return Buffer.from(input, 'base64');\n    }\n\n  };\n};", "/**\n * Implementation of the multicodec specification.\n *\n * @module multicodec\n * @example\n * const multicodec = require('multicodec')\n *\n * const prefixedProtobuf = multicodec.addPrefix('protobuf', protobufBuffer)\n * // prefixedProtobuf 0x50...\n *\n */\n'use strict';\n\nconst varint = require('varint');\n\nconst intTable = require('./int-table');\n\nconst codecNameToCodeVarint = require('./varint-table');\n\nconst util = require('./util');\n\nexports = module.exports;\n/**\n * Prefix a buffer with a multicodec-packed.\n *\n * @param {string|number} multicodecStrOrCode\n * @param {Buffer} data\n * @returns {Buffer}\n */\n\nexports.addPrefix = (multicodecStrOrCode, data) => {\n  let prefix;\n\n  if (Buffer.isBuffer(multicodecStrOrCode)) {\n    prefix = util.varintBufferEncode(multicodecStrOrCode);\n  } else {\n    if (codecNameToCodeVarint[multicodecStrOrCode]) {\n      prefix = codecNameToCodeVarint[multicodecStrOrCode];\n    } else {\n      throw new Error('multicodec not recognized');\n    }\n  }\n\n  return Buffer.concat([prefix, data]);\n};\n/**\n * Decapsulate the multicodec-packed prefix from the data.\n *\n * @param {Buffer} data\n * @returns {Buffer}\n */\n\n\nexports.rmPrefix = data => {\n  varint.decode(data);\n  return data.slice(varint.decode.bytes);\n};\n/**\n * Get the codec of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {string}\n */\n\n\nexports.getCodec = prefixedData => {\n  const code = varint.decode(prefixedData);\n  const codecName = intTable.get(code);\n\n  if (codecName === undefined) {\n    throw new Error(\"Code \".concat(code, \" not found\"));\n  }\n\n  return codecName;\n};\n/**\n * Get the name of the codec.\n * @param {number} codec\n * @returns {string}\n */\n\n\nexports.getName = codec => {\n  return intTable.get(codec);\n};\n/**\n * Get the code of the codec\n * @param {string} name\n * @returns {number}\n */\n\n\nexports.getNumber = name => {\n  const code = codecNameToCodeVarint[name];\n\n  if (code === undefined) {\n    throw new Error('Codec `' + name + '` not found');\n  }\n\n  return util.varintBufferDecode(code)[0];\n};\n/**\n * Get the code of the prefixed data.\n * @param {Buffer} prefixedData\n * @returns {number}\n */\n\n\nexports.getCode = prefixedData => {\n  return varint.decode(prefixedData);\n};\n/**\n * Get the code as varint of a codec name.\n * @param {string} codecName\n * @returns {Buffer}\n */\n\n\nexports.getCodeVarint = codecName => {\n  const code = codecNameToCodeVarint[codecName];\n\n  if (code === undefined) {\n    throw new Error('Codec `' + codecName + '` not found');\n  }\n\n  return code;\n};\n/**\n * Get the varint of a code.\n * @param {Number} code\n * @returns {Array.<number>}\n */\n\n\nexports.getVarint = code => {\n  return varint.encode(code);\n}; // Make the constants top-level constants\n\n\nconst constants = require('./constants');\n\nObject.assign(exports, constants); // Human friendly names for printing, e.g. in error messages\n\nexports.print = require('./print');", "'use strict';\n\nconst baseTable = require('./base-table.json'); // map for hexString -> codecName\n\n\nconst nameTable = new Map();\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName];\n  nameTable.set(code, encodingName);\n}\n\nmodule.exports = Object.freeze(nameTable);", "'use strict';\n\nconst baseTable = require('./base-table.json');\n\nconst varintEncode = require('./util').varintEncode; // map for codecName -> codeVarintBuffer\n\n\nconst varintTable = {};\n\nfor (const encodingName in baseTable) {\n  const code = baseTable[encodingName];\n  varintTable[encodingName] = varintEncode(code);\n}\n\nmodule.exports = Object.freeze(varintTable);", "'use strict';\n\nconst table = require('./base-table.json'); // map for codecConstant -> code\n\n\nconst constants = {};\n\nfor (const [name, code] of Object.entries(table)) {\n  constants[name.toUpperCase().replace(/-/g, '_')] = code;\n}\n\nmodule.exports = Object.freeze(constants);", "'use strict';\n\nconst table = require('./base-table.json'); // map for code -> print friendly name\n\n\nconst tableByCode = {};\n\nfor (const [name, code] of Object.entries(table)) {\n  if (tableByCode[code] === undefined) tableByCode[code] = name;\n}\n\nmodule.exports = Object.freeze(tableByCode);", "'use strict'\n\nconst mh = require('multihashes')\nconst { <PERSON><PERSON><PERSON> } = require('buffer')\nvar CIDUtil = {\n  /**\n   * Test if the given input is a valid CID object.\n   * Returns an error message if it is not.\n   * Returns undefined if it is a valid CID.\n   *\n   * @param {any} other\n   * @returns {string}\n   */\n  checkCIDComponents: function (other) {\n    if (other == null) {\n      return 'null values are not valid CIDs'\n    }\n\n    if (!(other.version === 0 || other.version === 1)) {\n      return 'Invalid version, must be a number equal to 1 or 0'\n    }\n\n    if (typeof other.codec !== 'string') {\n      return 'codec must be string'\n    }\n\n    if (other.version === 0) {\n      if (other.codec !== 'dag-pb') {\n        return \"codec must be 'dag-pb' for CIDv0\"\n      }\n      if (other.multibaseName !== 'base58btc') {\n        return \"multibaseName must be 'base58btc' for CIDv0\"\n      }\n    }\n\n    if (!Buffer.isBuffer(other.multihash)) {\n      return 'multihash must be a Buffer'\n    }\n\n    try {\n      mh.validate(other.multihash)\n    } catch (err) {\n      let errorMsg = err.message\n      if (!errorMsg) { // Just in case mh.validate() throws an error with empty error message\n        errorMsg = 'Multihash validation failed'\n      }\n      return errorMsg\n    }\n  }\n}\n\nmodule.exports = CIDUtil\n", "'use strict';\n\nfunction withIs(Class, {\n  className,\n  symbolName\n}) {\n  const symbol = Symbol.for(symbolName);\n  const ClassIsWrapper = {\n    // The code below assigns the class wrapper to an object to trick\n    // JavaScript engines to show the name of the extended class when\n    // logging an instances.\n    // We are assigning an anonymous class (class wrapper) to the object\n    // with key `className` to keep the correct name.\n    // If this is not supported it falls back to logging `ClassIsWrapper`.\n    [className]: class extends Class {\n      constructor(...args) {\n        super(...args);\n        Object.defineProperty(this, symbol, {\n          value: true\n        });\n      }\n\n      get [Symbol.toStringTag]() {\n        return className;\n      }\n\n    }\n  }[className];\n\n  ClassIsWrapper[\"is\".concat(className)] = obj => !!(obj && obj[symbol]);\n\n  return ClassIsWrapper;\n}\n\nfunction withIsProto(Class, {\n  className,\n  symbolName,\n  withoutNew\n}) {\n  const symbol = Symbol.for(symbolName);\n  /* eslint-disable object-shorthand */\n\n  const ClassIsWrapper = {\n    [className]: function (...args) {\n      if (withoutNew && !(this instanceof ClassIsWrapper)) {\n        return new ClassIsWrapper(...args);\n      }\n\n      const _this = Class.call(this, ...args) || this;\n\n      if (_this && !_this[symbol]) {\n        Object.defineProperty(_this, symbol, {\n          value: true\n        });\n      }\n\n      return _this;\n    }\n  }[className];\n  /* eslint-enable object-shorthand */\n\n  ClassIsWrapper.prototype = Object.create(Class.prototype);\n  ClassIsWrapper.prototype.constructor = ClassIsWrapper;\n  Object.defineProperty(ClassIsWrapper.prototype, Symbol.toStringTag, {\n    get() {\n      return className;\n    }\n\n  });\n\n  ClassIsWrapper[\"is\".concat(className)] = obj => !!(obj && obj[symbol]);\n\n  return ClassIsWrapper;\n}\n\nmodule.exports = withIs;\nmodule.exports.proto = withIsProto;"], "sourceRoot": ""}