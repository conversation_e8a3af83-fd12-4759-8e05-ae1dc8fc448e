{"version": 3, "file": "transaction_errors.js", "sourceRoot": "", "sources": ["../../../src/errors/transaction_errors.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;AAWF,sDA2C2B;AAC3B,8DAAyE;AAEzE,MAAa,gBAAmD,SAAQ,kCAAa;IAGpF,YAAmB,OAAe,EAAS,OAAqB;QAC/D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD2B,YAAO,GAAP,OAAO,CAAc;QAFzD,SAAI,GAAG,uBAAM,CAAC;IAIrB,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IACrD,CAAC;CACD;AAVD,4CAUC;AAED,MAAa,sBAAuB,SAAQ,kCAAa;IAGxD,YAA0B,MAAc,EAAS,SAAiB;QACjE,KAAK,CAAC,+DAA+D,MAAM,EAAE,CAAC,CAAC;QADtD,WAAM,GAAN,MAAM,CAAQ;QAAS,cAAS,GAAT,SAAS,CAAQ;QAF3D,SAAI,GAAG,0CAAyB,CAAC;IAIxC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,IAAG;IAC9E,CAAC;CACD;AAVD,wDAUC;AAED,MAAa,iCAEX,SAAQ,kCAAa;IAGtB,YACQ,MAAc,EACd,SAAkB,EAClB,OAAqB,EACrB,IAAa;QAEpB,KAAK,CACJ,2CACC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,kCAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAC3E,EAAE,CACF,CAAC;QATK,WAAM,GAAN,MAAM,CAAQ;QACd,cAAS,GAAT,SAAS,CAAS;QAClB,YAAO,GAAP,OAAO,CAAc;QACrB,SAAI,GAAJ,IAAI,CAAS;QANd,SAAI,GAAG,0CAAyB,CAAC;IAaxC,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AA3BD,8EA2BC;AAED;;;;GAIG;AACH,MAAa,gCAEX,SAAQ,iCAA8C;IAGvD,YACQ,MAAc,EACd,eAAuB,EACvB,2BAAmC,EACnC,oBAA6C,EAC7C,SAAkB,EAClB,OAAqB,EACrB,IAAa;QAEpB,KAAK,CAAC,MAAM,CAAC,CAAC;QARP,WAAM,GAAN,MAAM,CAAQ;QACd,oBAAe,GAAf,eAAe,CAAQ;QACvB,gCAA2B,GAA3B,2BAA2B,CAAQ;QACnC,yBAAoB,GAApB,oBAAoB,CAAyB;QAC7C,cAAS,GAAT,SAAS,CAAS;QAClB,YAAO,GAAP,OAAO,CAAc;QACrB,SAAI,GAAJ,IAAI,CAAS;QATd,SAAI,GAAG,uDAAsC,CAAC;IAYrD,CAAC;IAEM,MAAM;QACZ,uCACI,KAAK,CAAC,MAAM,EAAE,KACjB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,eAAe,EAAE,IAAI,CAAC,eAAe,EACrC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,EAC7D,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,EAC/C,SAAS,EAAE,IAAI,CAAC,SAAS,EACzB,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,IAAI,EAAE,IAAI,CAAC,IAAI,IACd;IACH,CAAC;CACD;AA7BD,4EA6BC;AAED,MAAa,2BAA4B,SAAQ,gBAAgB;IAChE,YAAmB,OAA2B;QAC7C,KAAK,CAAC,4DAA4D,EAAE,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,IAAI,GAAG,2CAA0B,CAAC;IACxC,CAAC;IAEM,MAAM;QACZ,uCAAY,KAAK,CAAC,MAAM,EAAE,KAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAG;IACrD,CAAC;CACD;AATD,kEASC;AAED,MAAa,0BAA2B,SAAQ,gBAAgB;IAC/D,YAAmB,OAA2B;QAC7C,KAAK,CAAC,oEAAoE,EAAE,OAAO,CAAC,CAAC;QACrF,IAAI,CAAC,IAAI,GAAG,2CAA0B,CAAC;IACxC,CAAC;CACD;AALD,gEAKC;AAED,MAAa,qCAEX,SAAQ,gBAA6B;IACtC,YAAmB,OAAqB;QACvC,KAAK,CACJ,2CACC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,kCAAa,CAAC,eAAe,CAAC,OAAO,CAAC,EAC3E,EAAE,EACF,OAAO,CACP,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,6CAA4B,CAAC;IAC1C,CAAC;CACD;AAZD,sFAYC;AAED,MAAa,wBAAyB,SAAQ,gBAAgB;IAC7D,YAAmB,OAA2B;QAC7C,KAAK,CACJ,0DAA0D,IAAI,CAAC,SAAS,CACvE,OAAO,EACP,SAAS,EACT,CAAC,CACD,EAAE,EACH,OAAO,CACP,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,kCAAiB,CAAC;IAC/B,CAAC;CACD;AAZD,4DAYC;AAED,MAAa,4BAA6B,SAAQ,gBAAgB;IACjE;QACC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,GAAG,qCAAoB,CAAC;IAClC,CAAC;CACD;AALD,oEAKC;AACD,MAAa,mBAAoB,SAAQ,gBAAgB;IACxD;QACC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,iCAAgB,CAAC;IAC9B,CAAC;CACD;AALD,kDAKC;AAED,MAAa,4BAA6B,SAAQ,sCAAiB;IAGlE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAC;QAHlD,SAAI,GAAG,sCAAqB,CAAC;IAIpC,CAAC;CACD;AAND,oEAMC;AACD,MAAa,8BAA+B,SAAQ,sCAAiB;IAGpE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,2CAA2C,CAAC,CAAC;QAHpD,SAAI,GAAG,wCAAuB,CAAC;IAItC,CAAC;CACD;AAND,wEAMC;AACD,MAAa,sBAAuB,SAAQ,sCAAiB;IAG5D,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;QAHnC,SAAI,GAAG,oCAAmB,CAAC;IAIlC,CAAC;CACD;AAND,wDAMC;AAED,MAAa,uBAAwB,SAAQ,sCAAiB;IAG7D;QACC,KAAK,CACJ,yBAAyB,EACzB,6DAA6D,CAC7D,CAAC;QANI,SAAI,GAAG,4CAA2B,CAAC;IAO1C,CAAC;CACD;AATD,0DASC;AAED,MAAa,yBAA0B,SAAQ,sCAAiB;IAG/D;QACC,KAAK,CACJ,2BAA2B,EAC3B,+FAA+F,CAC/F,CAAC;QANI,SAAI,GAAG,+CAA8B,CAAC;IAO7C,CAAC;CACD;AATD,8DASC;AAED,MAAa,oBAAqB,SAAQ,sCAAiB;IAG1D,YAAmB,KAAqD;QACvE,KAAK,CACJ,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QACrB,kIAAkI;QAClI,mEAAmE,CACnE,CAAC;QAPI,SAAI,GAAG,yCAAwB,CAAC;IAQvC,CAAC;CACD;AAVD,oDAUC;AAED,MAAa,kBAAmB,SAAQ,sCAAiB;IAGxD,YAAmB,KAA+C;QACjE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,oDAAoD,CAAC,CAAC;QAH7E,SAAI,GAAG,sCAAqB,CAAC;IAIpC,CAAC;CACD;AAND,gDAMC;AAED,MAAa,qBAAsB,SAAQ,sCAAiB;IAG3D,YAAmB,KAAuD;QACzE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,yDAAyD,CAAC,CAAC;QAHlF,SAAI,GAAG,yCAAwB,CAAC;IAIvC,CAAC;CACD;AAND,sDAMC;AAED,MAAa,6BAA8B,SAAQ,sCAAiB;IAGnE;QACC,KAAK,CACJ,+BAA+B,EAC/B,2FAA2F,CAC3F,CAAC;QANI,SAAI,GAAG,0CAAyB,CAAC;IAOxC,CAAC;CACD;AATD,sEASC;AAED,MAAa,2BAA4B,SAAQ,sCAAiB;IAGjE,YAAmB,KAAkE;;QACpF,KAAK,CACJ,6BAA6B,EAC7B,sFACC,MAAA,KAAK,CAAC,KAAK,mCAAI,WAChB,iBAAiB,MAAA,KAAK,CAAC,QAAQ,mCAAI,WAAW,EAAE,CAChD,CAAC;QARI,SAAI,GAAG,0CAAyB,CAAC;IASxC,CAAC;CACD;AAXD,kEAWC;AAED,MAAa,oBAAqB,SAAQ,kCAAa;IAGtD;QACC,KAAK,CACJ,2KAA2K,CAC3K,CAAC;QALI,SAAI,GAAG,+CAA8B,CAAC;IAM7C,CAAC;CACD;AARD,oDAQC;AAED,MAAa,eAAgB,SAAQ,sCAAiB;IAGrD,YAAmB,KAKlB;;QACA,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAC/B,MAAA,KAAK,CAAC,QAAQ,mCAAI,WACnB,2BAA2B,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACnE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,kBAAkB,CAClB,CAAC;QAfI,SAAI,GAAG,mCAAkB,CAAC;QAgBhC,IAAI,CAAC,KAAK,GAAG,IAAI,oBAAoB,EAAE,CAAC;IACzC,CAAC;CACD;AAnBD,0CAmBC;AAED,MAAa,gCAAiC,SAAQ,kCAAa;IAGlE;QACC,KAAK,CACJ,qLAAqL,CACrL,CAAC;QALI,SAAI,GAAG,gDAA+B,CAAC;IAM9C,CAAC;CACD;AARD,4EAQC;AAED,MAAa,2BAA4B,SAAQ,sCAAiB;IAGjE,YAAmB,KAKlB;;QACA,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAC/B,MAAA,KAAK,CAAC,QAAQ,mCAAI,WACnB,2BAA2B,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACnE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,wEAAwE,CACxE,CAAC;QAfI,SAAI,GAAG,oCAAmB,CAAC;QAgBjC,IAAI,CAAC,KAAK,GAAG,IAAI,gCAAgC,EAAE,CAAC;IACrD,CAAC;CACD;AAnBD,kEAmBC;AAED,MAAa,oBAAqB,SAAQ,sCAAiB;IAG1D,YAAmB,KAAkE;;QACpF,KAAK,CACJ,QAAQ,MAAA,KAAK,CAAC,GAAG,mCAAI,WAAW,eAAe,MAAA,KAAK,CAAC,QAAQ,mCAAI,WAAW,EAAE,EAC9E,iCAAiC,CACjC,CAAC;QANI,SAAI,GAAG,0CAAyB,CAAC;IAOxC,CAAC;CACD;AATD,oDASC;AAED,MAAa,yCAA0C,SAAQ,sCAAiB;IAG/E,YAAmB,KAGlB;;QACA,KAAK,CACJ,yBAAyB,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACjE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,sDAAsD,CACtD,CAAC;QAXI,SAAI,GAAG,8CAA6B,CAAC;IAY5C,CAAC;CACD;AAdD,8FAcC;AAED,MAAa,oBAAqB,SAAQ,sCAAiB;IAG1D,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,8CAA8C,CAAC,CAAC;QAHvD,SAAI,GAAG,oDAAmC,CAAC;IAIlD,CAAC;CACD;AAND,oDAMC;AAED,MAAa,yBAA0B,SAAQ,sCAAiB;IAG/D,YAAmB,KAGlB;;QACA,KAAK,CACJ,yBAAyB,MAAA,KAAK,CAAC,oBAAoB,mCAAI,WAAW,mBACjE,MAAA,KAAK,CAAC,YAAY,mCAAI,WACvB,EAAE,EACF,0EAA0E,CAC1E,CAAC;QAXI,SAAI,GAAG,iDAAgC,CAAC;IAY/C,CAAC;CACD;AAdD,8DAcC;AAED,MAAa,6BAA8B,SAAQ,sCAAiB;IAGnE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,4BAA4B,CAAC,CAAC;QAHrC,SAAI,GAAG,sCAAqB,CAAC;IAIpC,CAAC;CACD;AAND,sEAMC;AAED,MAAa,0BAA2B,SAAQ,sCAAiB;IAGhE,YAAmB,KAAmE;;QACrF,KAAK,CACJ,UAAU,MAAA,KAAK,CAAC,KAAK,mCAAI,WAAW,cAAc,MAAA,KAAK,CAAC,OAAO,mCAAI,WAAW,EAAE,EAChF,kCAAkC,CAClC,CAAC;QANI,SAAI,GAAG,iDAAgC,CAAC;IAO/C,CAAC;CACD;AATD,gEASC;AAED,MAAa,0BAA2B,SAAQ,sCAAiB;IAGhE;QACC,KAAK,CAAC,4BAA4B,EAAE,qDAAqD,CAAC,CAAC;QAHrF,SAAI,GAAG,gDAA+B,CAAC;IAI9C,CAAC;CACD;AAND,gEAMC;AAED,MAAa,wBAAyB,SAAQ,sCAAiB;IAG9D;QACC,KAAK,CAAC,0BAA0B,EAAE,kCAAkC,CAAC,CAAC;QAHhE,SAAI,GAAG,4CAA2B,CAAC;IAI1C,CAAC;CACD;AAND,4DAMC;AAED,MAAa,+BAAgC,SAAQ,sCAAiB;IAGrE,YAAmB,KAAc;QAChC,KAAK,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;QAHvC,SAAI,GAAG,wCAAuB,CAAC;IAItC,CAAC;CACD;AAND,0EAMC;AAED,MAAa,4BAA6B,SAAQ,sCAAiB;IAGlE,YAAmB,KAAoE;;QACtF,KAAK,CACJ,SAAS,MAAA,KAAK,CAAC,IAAI,mCAAI,WAAW,YAAY,MAAA,KAAK,CAAC,KAAK,mCAAI,WAAW,EAAE,EAC1E,iIAAiI,CACjI,CAAC;QANI,SAAI,GAAG,sCAAqB,CAAC;IAOpC,CAAC;CACD;AATD,oEASC;AAED,MAAa,2BAA4B,SAAQ,kCAAa;IAG7D,YAAmB,KAA2D;QAC7E,KAAK,CACJ,sDACC,KAAK,CAAC,eACP,0LACC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,eAC5D,EAAE,CACF,CAAC;QATI,SAAI,GAAG,oCAAmB,CAAC;IAUlC,CAAC;CACD;AAZD,kEAYC;AAED,SAAS,sBAAsB,CAAC,eAAuB;IACtD,OAAO,gMACN,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,eAChD,EAAE,CAAC;AACJ,CAAC;AAED,MAAa,8BAA+B,SAAQ,kCAAa;IAGhE,YAAmB,KAA0D;QAC5E,KAAK,CACJ,oCACC,KAAK,CAAC,eACP,aAAa,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAC5D,CAAC;QAPI,SAAI,GAAG,uCAAsB,CAAC;IAQrC,CAAC;CACD;AAVD,wEAUC;AAED,MAAa,4BAA6B,SAAQ,kCAAa;IAG9D,YAAmB,KAIlB;QACA,KAAK,CACJ,0BAA0B,KAAK,CAAC,kBAAkB,6BACjD,KAAK,CAAC,cACP,YAAY,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAC3D,CAAC;QAXI,SAAI,GAAG,qCAAoB,CAAC;IAYnC,CAAC;CACD;AAdD,oEAcC;AAED,MAAa,yCAA0C,SAAQ,sCAAiB;IAG/E,YAAmB,KAIlB;;QACA,KAAK,CACJ,YAAY,IAAI,CAAC,SAAS,CACzB,KAAK,CAAC,OAAO,CACb,gBAAgB,MAAA,KAAK,CAAC,SAAS,0CAAE,QAAQ,EAAE,sBAAsB,MAAA,KAAK,CAAC,eAAe,0CAAE,QAAQ,EAAE,EAAE,EACrG,mCAAmC,CACnC,CAAC;QAZI,SAAI,GAAG,yDAAwC,CAAC;IAavD,CAAC;CACD;AAfD,8FAeC;AAED,MAAa,yCAA0C,SAAQ,sCAAiB;IAG/E,YAAmB,KAAsC;QACxD,KAAK,CAAC,YAAY,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,8BAA8B,CAAC,CAAC;QAH7E,SAAI,GAAG,oDAAmC,CAAC;IAIlD,CAAC;CACD;AAND,8FAMC;AAED,MAAa,uBAAwB,SAAQ,kCAAa;IAEzD,YAAmB,YAAoB;QACtC,KAAK,CAAC,uBAAuB,YAAY,GAAG,CAAC,CAAC;QAFxC,SAAI,GAAG,+BAAc,CAAC;IAG7B,CAAC;CACD;AALD,0DAKC;AAED,MAAa,4BAA6B,SAAQ,sCAAiB;IAGlE;QACC,KAAK,CACJ,8BAA8B,EAC9B,wEAAwE,CACxE,CAAC;QANI,SAAI,GAAG,kDAAiC,CAAC;IAOhD,CAAC;CACD;AATD,oEASC;AACD,MAAa,wCAAyC,SAAQ,kCAAa;IAG1E,YACC,eAA4C,EAC5C,MAA6B;QAE7B,MAAM,oBAAoB,GAAa,EAAE,CAAC;QAC1C,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3E,KAAK,CACJ,iEAAiE,MAAM,KAAK,oBAAoB,CAAC,IAAI,CACpG,IAAI,CACJ,EAAE,CACH,CAAC;QAZI,SAAI,GAAG,mDAAkC,CAAC;IAajD,CAAC;CACD;AAfD,4FAeC"}