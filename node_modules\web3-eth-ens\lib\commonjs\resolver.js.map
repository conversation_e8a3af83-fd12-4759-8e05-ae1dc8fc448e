{"version": 3, "file": "resolver.js", "sourceRoot": "", "sources": ["../../src/resolver.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;EAeE;;;;;;;;;;;;AAEF,6CAAyD;AAEzD,2CAA6C;AAC7C,mDAA6C;AAG7C,2CAA+D;AAE/D,yCAAsC;AAGtC,2BAA2B;AAC3B,oFAAoF;AAEpF,MAAa,QAAQ;IAGpB,YAAmB,QAAkB;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAEa,0BAA0B,CAAC,OAAe;;YACvD,kLAAkL;YAClL,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;KAAA;IAED,0CAA0C;IAC1C,kDAAkD;IACrC,qBAAqB,CACjC,gBAAoD,EACpD,UAAkB;;;YAElB,IAAI,IAAA,sBAAS,EAAC,wBAAY,CAAC,UAAU,CAAC,CAAC;gBACtC,MAAM,IAAI,wCAA0B,CACnC,MAAA,gBAAgB,CAAC,OAAO,CAAC,OAAO,mCAAI,EAAE,EACtC,UAAU,CACV,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,OAAO;iBAC9C,iBAAiB,CAAC,wBAAY,CAAC,UAAU,CAAC,CAAC;iBAC3C,IAAI,EAAE,CAAC;YAET,IAAI,CAAC,SAAS;gBACb,MAAM,IAAI,wCAA0B,CACnC,MAAA,gBAAgB,CAAC,OAAO,CAAC,OAAO,mCAAI,EAAE,EACtC,UAAU,CACV,CAAC;;KACH;IAEY,iBAAiB,CAAC,OAAe,EAAE,WAAmB;;;YAClE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,IAAI,gBAAgB,GAAG,WAAW,CAAC;YAEnC,IAAI,CAAC,IAAA,4BAAW,EAAC,gBAAgB,CAAC,EAAE;gBACnC,gBAAgB,GAAG,MAAA,IAAA,iBAAI,EAAC,WAAW,CAAC,mCAAI,EAAE,CAAC;gBAE3C,IAAI,WAAW,KAAK,EAAE;oBAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAEhE,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;aACjD;YAED,OAAO,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,CAAC;;KAC3E;IAED,kEAAkE;IACrD,UAAU,CAAC,OAAe,EAAE,WAAmB,EAAE;;YAC7D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,IAAI,CAAC,CAAC;YAE5E,OAAO,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1E,CAAC;KAAA;IAEY,SAAS,CAAC,OAAe;;YACrC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,MAAM,CAAC,CAAC;YAE9E,OAAO,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClE,CAAC;KAAA;IAEY,cAAc,CAAC,OAAe;;YAC1C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAExE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,WAAW,CAAC,CAAC;YAEnF,OAAO,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvE,CAAC;KAAA;IAEY,UAAU,CACtB,OAAe,EACf,OAAgB,EAChB,QAA4B;;YAE5B,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,OAAO,CAAC,CAAC;YAE/E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,OAAO,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,EAAE,OAAO,CAAC;iBACnC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClB,CAAC;KAAA;IAEY,OAAO,CACnB,OAAe,EACf,GAAW;;YAEX,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,IAAI,CAAC,CAAC;YAE5E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,IAAI,CAAC,IAAA,mBAAQ,EAAC,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAA;QACtC,CAAC;KAAA;IAEY,OAAO,CACnB,OAAe,EACf,qBAAqB,GAAG,IAAI;;YAE5B,MAAM,WAAW,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC;YAEzE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;YAE5E,IAAG,qBAAqB;gBACvB,MAAM,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,8BAAkB,CAAC,IAAI,CAAC,CAAC;YAE7E,OAAO,gBAAgB,CAAC,OAAO;iBAC7B,IAAI,CAAC,IAAA,mBAAQ,EAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;QACrC,CAAC;KAAA;CACD;AAlHD,4BAkHC"}