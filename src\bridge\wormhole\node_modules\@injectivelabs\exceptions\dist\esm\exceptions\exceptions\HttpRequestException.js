import { ConcreteException } from '../base.js';
import { ErrorType, HttpRequestMethod } from '../types/index.js';
export class HttpRequestException extends ConcreteException {
    static errorClass = 'HttpRequestException';
    method = HttpRequestMethod.Get;
    constructor(error, context) {
        super(error, context);
        this.type = ErrorType.HttpRequest;
        this.method = context
            ? context.method || HttpRequestMethod.Get
            : HttpRequestMethod.Get;
        this.context = context?.context || 'Unknown';
    }
    parse() {
        this.setName(HttpRequestException.errorClass);
    }
}
