"""
Windows代理管理器
专门用于Windows系统的代理设置和管理
"""

import yaml
import time
import random
import logging
import subprocess
import threading
import tempfile
import json
from pathlib import Path
from typing import Dict, Any, Optional

class WindowsProxyManager:
    """Windows代理管理器"""
    
    def __init__(self, config_path: str = "config/ip.yaml"):
        """
        初始化Windows代理管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.current_proxy = None
        self.proxy_list = []
        self.rotation_thread = None
        self.is_running = False
        self.clash_process = None
        self.clash_config_file = None
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化代理列表
        self._initialize_proxy_list()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _initialize_proxy_list(self):
        """初始化代理列表"""
        if 'proxies' not in self.config:
            self.logger.error("配置文件中未找到代理列表")
            return
            
        self.proxy_list = []
        preferred_regions = self.config.get('rotation_config', {}).get('preferred_regions', [])
        excluded_nodes = self.config.get('rotation_config', {}).get('excluded_nodes', [])
        
        # 按地区优先级排序
        for region in preferred_regions:
            for proxy in self.config['proxies']:
                if region in proxy['name'] and proxy['name'] not in excluded_nodes:
                    self.proxy_list.append(proxy)
        
        # 添加其他未分类的代理
        for proxy in self.config['proxies']:
            if proxy not in self.proxy_list and proxy['name'] not in excluded_nodes:
                self.proxy_list.append(proxy)
                
        self.logger.info(f"初始化代理列表完成，共 {len(self.proxy_list)} 个节点")
    
    def _test_proxy_port(self, proxy: Dict[str, Any]) -> bool:
        """
        测试代理端口连通性
        
        Args:
            proxy: 代理配置
            
        Returns:
            bool: 端口是否可连接
        """
        try:
            import socket
            
            test_timeout = 3  # 3秒超时
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(test_timeout)
            
            result = sock.connect_ex((proxy['server'], proxy['port']))
            sock.close()
            
            if result == 0:
                self.logger.info(f"代理 {proxy['name']} 端口连通")
                return True
            else:
                self.logger.warning(f"代理 {proxy['name']} 端口不通")
                return False
                
        except Exception as e:
            self.logger.warning(f"代理 {proxy['name']} 端口测试失败: {e}")
            return False
    
    def _generate_clash_config(self, proxy: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成Clash配置文件
        
        Args:
            proxy: 代理配置
            
        Returns:
            dict: Clash配置
        """
        clash_config = {
            'mixed-port': 7890,
            'allow-lan': False,
            'bind-address': '*',
            'mode': 'global',
            'log-level': 'info',
            'ipv6': False,
            'external-controller': '127.0.0.1:9090',
            'proxies': [proxy],
            'proxy-groups': [
                {
                    'name': 'PROXY',
                    'type': 'select',
                    'proxies': [proxy['name']]
                }
            ],
            'rules': [
                'MATCH,PROXY'
            ]
        }
        
        return clash_config
    
    def _set_windows_proxy(self, enable: bool = True, server: str = None, port: int = None) -> bool:
        """
        设置Windows系统代理
        
        Args:
            enable: 是否启用代理
            server: 代理服务器
            port: 代理端口
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if enable and server and port:
                # 启用代理
                cmd_enable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f'
                cmd_server = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d "127.0.0.1:7890" /f'
                
                subprocess.run(cmd_enable, shell=True, check=True, capture_output=True)
                subprocess.run(cmd_server, shell=True, check=True, capture_output=True)
                
                self.logger.info(f"Windows系统代理已启用: 127.0.0.1:7890")
            else:
                # 禁用代理
                cmd_disable = f'reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f'
                subprocess.run(cmd_disable, shell=True, check=True, capture_output=True)
                
                self.logger.info("Windows系统代理已禁用")
            
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"设置Windows代理失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"设置Windows代理异常: {e}")
            return False
    
    def switch_proxy(self, proxy: Optional[Dict[str, Any]] = None) -> bool:
        """
        切换代理
        
        Args:
            proxy: 指定的代理配置，如果为None则随机选择
            
        Returns:
            bool: 切换是否成功
        """
        if not proxy:
            if not self.proxy_list:
                self.logger.error("代理列表为空")
                return False
            proxy = random.choice(self.proxy_list)
        
        self.logger.info(f"正在切换到代理: {proxy['name']}")
        
        # 测试端口连通性
        if not self._test_proxy_port(proxy):
            self.logger.warning(f"代理 {proxy['name']} 端口不通，但仍然记录为当前代理")
        
        # 记录当前代理信息
        self.current_proxy = proxy
        self.logger.info(f"当前代理: {proxy['name']}")
        self.logger.info(f"服务器: {proxy['server']}:{proxy['port']}")
        self.logger.info(f"类型: {proxy['type']}")
        
        return True
    
    def start_rotation(self):
        """启动自动轮换"""
        if not self.config.get('rotation_config', {}).get('enabled', True):
            self.logger.info("IP轮换功能已禁用")
            return
            
        if self.is_running:
            self.logger.warning("IP轮换已在运行中")
            return
            
        self.is_running = True
        self.rotation_thread = threading.Thread(target=self._rotation_loop, daemon=True)
        self.rotation_thread.start()
        self.logger.info("IP自动轮换已启动")
    
    def stop_rotation(self):
        """停止自动轮换"""
        self.is_running = False
        if self.rotation_thread:
            self.rotation_thread.join(timeout=5)
        self.logger.info("IP自动轮换已停止")
    
    def _rotation_loop(self):
        """轮换循环"""
        interval = self.config.get('rotation_config', {}).get('interval', 60)
        
        # 首次切换
        self.switch_proxy()
        
        while self.is_running:
            try:
                time.sleep(interval)
                if self.is_running:
                    self.switch_proxy()
            except Exception as e:
                self.logger.error(f"轮换循环异常: {e}")
                time.sleep(10)  # 异常时等待10秒
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取当前状态
        
        Returns:
            dict: 状态信息
        """
        return {
            'is_running': self.is_running,
            'current_proxy': self.current_proxy['name'] if self.current_proxy else None,
            'current_server': f"{self.current_proxy['server']}:{self.current_proxy['port']}" if self.current_proxy else None,
            'total_proxies': len(self.proxy_list),
            'rotation_interval': self.config.get('rotation_config', {}).get('interval', 60)
        }
    
    def list_proxies(self) -> list:
        """
        获取代理列表
        
        Returns:
            list: 代理列表
        """
        return self.proxy_list
    
    def get_proxy_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据名称查找代理
        
        Args:
            name: 代理名称（部分匹配）
            
        Returns:
            dict: 代理配置
        """
        for proxy in self.proxy_list:
            if name.lower() in proxy['name'].lower():
                return proxy
        return None
