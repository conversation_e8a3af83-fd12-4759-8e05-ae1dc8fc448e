import { Common } from '@ethereumjs/common';
import { Address } from '@ethereumjs/util';
import type { BlockHeaderBytes, BlockOptions, HeaderData, JsonHeader } from './types.js';
interface HeaderCache {
    hash: Uint8Array | undefined;
}
/**
 * An object that represents the block header.
 */
export declare class BlockHeader {
    readonly parentHash: Uint8Array;
    readonly uncleHash: Uint8Array;
    readonly coinbase: Address;
    readonly stateRoot: Uint8Array;
    readonly transactionsTrie: Uint8Array;
    readonly receiptTrie: Uint8Array;
    readonly logsBloom: Uint8Array;
    readonly difficulty: bigint;
    readonly number: bigint;
    readonly gasLimit: bigint;
    readonly gasUsed: bigint;
    readonly timestamp: bigint;
    readonly extraData: Uint8Array;
    readonly mixHash: Uint8Array;
    readonly nonce: Uint8Array;
    readonly baseFeePerGas?: bigint;
    readonly withdrawalsRoot?: Uint8Array;
    readonly blobGasUsed?: bigint;
    readonly excessBlobGas?: bigint;
    readonly parentBeaconBlockRoot?: Uint8Array;
    readonly requestsRoot?: Uint8Array;
    readonly common: Common;
    protected keccakFunction: (msg: Uint8Array) => Uint8Array;
    protected cache: HeaderCache;
    /**
     * EIP-4399: After merge to PoS, `mixHash` supplanted as `prevRandao`
     */
    get prevRandao(): Uint8Array;
    /**
     * Static constructor to create a block header from a header data dictionary
     *
     * @param headerData
     * @param opts
     */
    static fromHeaderData(headerData?: HeaderData, opts?: BlockOptions): BlockHeader;
    /**
     * Static constructor to create a block header from a RLP-serialized header
     *
     * @param serializedHeaderData
     * @param opts
     */
    static fromRLPSerializedHeader(serializedHeaderData: Uint8Array, opts?: BlockOptions): BlockHeader;
    /**
     * Static constructor to create a block header from an array of Bytes values
     *
     * @param values
     * @param opts
     */
    static fromValuesArray(values: BlockHeaderBytes, opts?: BlockOptions): BlockHeader;
    /**
     * This constructor takes the values, validates them, assigns them and freezes the object.
     *
     * @deprecated Use the public static factory methods to assist in creating a Header object from
     * varying data types. For a default empty header, use {@link BlockHeader.fromHeaderData}.
     *
     */
    constructor(headerData: HeaderData, opts?: BlockOptions);
    /**
     * Validates correct buffer lengths, throws if invalid.
     */
    protected _genericFormatValidation(): void;
    /**
     * Checks static parameters related to consensus algorithm
     * @throws if any check fails
     */
    protected _consensusFormatValidation(): void;
    /**
     * Validates if the block gasLimit remains in the boundaries set by the protocol.
     * Throws if out of bounds.
     *
     * @param parentBlockHeader - the header from the parent `Block` of this header
     */
    validateGasLimit(parentBlockHeader: BlockHeader): void;
    /**
     * Calculates the base fee for a potential next block
     */
    calcNextBaseFee(): bigint;
    /**
     * Returns the price per unit of blob gas for a blob transaction in the current/pending block
     * @returns the price in gwei per unit of blob gas spent
     */
    getBlobGasPrice(): bigint;
    /**
     * Returns the blob gas price depending upon the `excessBlobGas` value
     * @param excessBlobGas
     */
    private _getBlobGasPrice;
    /**
     * Returns the total fee for blob gas spent for including blobs in block.
     *
     * @param numBlobs number of blobs in the transaction/block
     * @returns the total blob gas fee for numBlobs blobs
     */
    calcDataFee(numBlobs: number): bigint;
    /**
     * Calculates the excess blob gas for next (hopefully) post EIP 4844 block.
     */
    calcNextExcessBlobGas(): bigint;
    /**
     * Calculate the blob gas price of the block built on top of this one
     * @returns The blob gas price
     */
    calcNextBlobGasPrice(): bigint;
    /**
     * Returns a Uint8Array Array of the raw Bytes in this header, in order.
     */
    raw(): BlockHeaderBytes;
    /**
     * Returns the hash of the block header.
     */
    hash(): Uint8Array;
    /**
     * Checks if the block header is a genesis header.
     */
    isGenesis(): boolean;
    protected _requireClique(name: string): void;
    /**
     * Returns the canonical difficulty for this block.
     *
     * @param parentBlockHeader - the header from the parent `Block` of this header
     */
    ethashCanonicalDifficulty(parentBlockHeader: BlockHeader): bigint;
    /**
     * PoA clique signature hash without the seal.
     */
    cliqueSigHash(): Uint8Array;
    /**
     * Checks if the block header is an epoch transition
     * header (only clique PoA, throws otherwise)
     */
    cliqueIsEpochTransition(): boolean;
    /**
     * Returns extra vanity data
     * (only clique PoA, throws otherwise)
     */
    cliqueExtraVanity(): Uint8Array;
    /**
     * Returns extra seal data
     * (only clique PoA, throws otherwise)
     */
    cliqueExtraSeal(): Uint8Array;
    /**
     * Seal block with the provided signer.
     * Returns the final extraData field to be assigned to `this.extraData`.
     * @hidden
     */
    private cliqueSealBlock;
    /**
     * Returns a list of signers
     * (only clique PoA, throws otherwise)
     *
     * This function throws if not called on an epoch
     * transition block and should therefore be used
     * in conjunction with {@link BlockHeader.cliqueIsEpochTransition}
     */
    cliqueEpochTransitionSigners(): Address[];
    /**
     * Verifies the signature of the block (last 65 bytes of extraData field)
     * (only clique PoA, throws otherwise)
     *
     *  Method throws if signature is invalid
     */
    cliqueVerifySignature(signerList: Address[]): boolean;
    /**
     * Returns the signer address
     */
    cliqueSigner(): Address;
    /**
     * Returns the rlp encoding of the block header.
     */
    serialize(): Uint8Array;
    /**
     * Returns the block header in JSON format.
     */
    toJSON(): JsonHeader;
    /**
     * Validates extra data is DAO_ExtraData for DAO_ForceExtraDataRange blocks after DAO
     * activation block (see: https://blog.slock.it/hard-fork-specification-24b889e70703)
     */
    protected _validateDAOExtraData(): void;
    /**
     * Return a compact error string representation of the object
     */
    errorStr(): string;
    /**
     * Helper function to create an annotated error message
     *
     * @param msg Base error message
     * @hidden
     */
    protected _errorMsg(msg: string): string;
}
export {};
//# sourceMappingURL=header.d.ts.map